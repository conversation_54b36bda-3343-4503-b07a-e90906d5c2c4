import abc
import json
import logging
from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, cast

import numpy as np

from deeplearning.utils.dataset import get_recency_split_value
from lib.common.time.time import maka_control_timestamp_ms

LOG = logging.getLogger(__name__)

RECENCY_CANDIDATES = {
    "4h": 4 * 60 * 60 * 1000,  # 4 hours
    "1d": 24 * 60 * 60 * 1000,  # 1 day
    "3d": 3 * 24 * 60 * 60 * 1000,  # 3 days
}


def generate_is_new_default() -> Callable[[Dict[str, Any]], bool]:
    def is_new(datapoint: Dict[str, Any]) -> bool:
        return bool(datapoint["is_new"])

    return is_new


def generate_is_new_recency(recency_split: int) -> Callable[[Dict[str, Any]], bool]:
    def is_new(datapoint: Dict[str, Any]) -> bool:
        return bool(datapoint["captured_at"] > recency_split)

    return is_new


def generate_is_new_robot_specific(robot_ids: List[str]) -> Callable[[Dict[str, Any]], bool]:
    def is_new(datapoint: Dict[str, Any]) -> bool:
        return bool(datapoint["robot_id"] in robot_ids)

    return is_new


def generate_is_in_geohashes(geohashes: List[str]) -> Callable[[Dict[str, Any]], bool]:
    def is_in_geohashes(datapoint: Dict[str, Any]) -> bool:
        if datapoint["geohash"] is None:
            return False

        for hash in geohashes:
            if datapoint["geohash"].startswith(hash):
                return True

        return False

    return is_in_geohashes


def find_recency_split(
    filepath: str,
    min_recent_images: int = 100,
    recency_candidate_ms: int = RECENCY_CANDIDATES["3d"],
    dataset_version: int = 1,
) -> Tuple[Optional[int], Optional[int]]:
    # Read in the dataset json object

    if dataset_version == 1:
        with open(filepath) as f:
            data = json.load(f)

        num_images = len(data["images"])

        dataset_created_time = data["info"]["created"]

        reverse_sorted_timestamps = [
            item["captured_at"]
            for item in sorted(data["images"], key=lambda x: cast(int, x["captured_at"]), reverse=True)
        ]

    elif dataset_version == 2:
        with open(filepath) as f:
            data = [json.loads(line) for line in f]

        metadata_path = "/".join(filepath.split("/")[:-1]) + "/metadata.json"
        with open(metadata_path) as f:
            metadata = json.load(f)

        num_images = len(data)

        dataset_created_time = metadata["statistics"]["creation_time"]

        reverse_sorted_timestamps = [
            item["captured_at"] for item in sorted(data, key=lambda x: cast(int, x["captured_at"]), reverse=True)
        ]

    recency_split: Optional[int] = None
    recency_split_age: Optional[int] = None

    if len(reverse_sorted_timestamps) < min_recent_images:
        LOG.warning("Falling back to is_new, as there aren't enough images for recency split.")
    else:
        recency_split, recent_count = get_recency_split_value(
            reverse_sorted_timestamps, min_recent_images, dataset_created_time - recency_candidate_ms
        )

        if recent_count >= min_recent_images and num_images - recent_count >= min_recent_images:
            recency_split = recency_split
            recency_split_age = dataset_created_time - recency_split

    return recency_split, recency_split_age


class BucketKey(abc.ABC):
    def __init__(self) -> None:
        pass

    @abc.abstractmethod
    def __eq__(self, other: Any) -> bool:
        pass

    @abc.abstractmethod
    def __hash__(self) -> int:
        pass


class GeohashMonthCategoryBucket(BucketKey):
    def __init__(self, geohash: str, month: str, category: str):
        self.geohash = geohash
        self.month = month
        self.category = category
        super().__init__()

    def __eq__(self, other: Any) -> bool:

        if isinstance(other, GeohashMonthCategoryBucket):
            return self.geohash == other.geohash and self.month == other.month and self.category == other.category
        else:
            return False

    def __hash__(self) -> int:
        return f"{self.geohash}-{self.month}-{self.category}".__hash__()


class GeohashMonthBucket(BucketKey):
    def __init__(self, geohash: str, month: str):
        self.geohash = geohash
        self.month = month
        super().__init__()

    def __eq__(self, other: Any) -> bool:

        if isinstance(other, GeohashMonthBucket):
            return self.geohash == other.geohash and self.month == other.month
        else:
            return False

    def __hash__(self) -> int:
        return f"{self.geohash}-{self.month}".__hash__()


class EmbeddingBucket(BucketKey):
    def __init__(self, embedding_bucket: Tuple[int]) -> None:
        self.embedding_bucket = embedding_bucket

    def __eq__(self, other: Any) -> bool:
        if isinstance(other, EmbeddingBucket):
            return self.embedding_bucket == other.embedding_bucket
        else:
            return False

    def __hash__(self) -> int:
        return f"{self.embedding_bucket}".__hash__()


class SamplingBuckets:
    def __init__(self) -> None:
        self._sampling_bucket: Dict[BucketKey, Any] = defaultdict(set)
        self._rng = np.random.default_rng(seed=maka_control_timestamp_ms())

    def add(self, key: BucketKey, value: Any) -> None:
        self._sampling_bucket[key].add(value)

    def sample(self, key: BucketKey) -> Any:
        assert len(self._sampling_bucket[key]) > 0, f"No items in sampling bucket of {key}"
        return self._rng.choice(self._sampling_bucket[key])


def get_embedding_index(
    x: float,
    y: float,
    x_buckets: Optional[List[int]] = None,
    y_buckets: Optional[List[int]] = None,
    min_inclusive_value: float = 0.0,
    max_inclusive_value: float = 1 - 1e-6,
) -> Tuple[int, ...]:
    x_buckets = x_buckets if x_buckets is not None else [10]
    y_buckets = y_buckets if y_buckets is not None else [10]
    x_total, y_total = 1, 1
    for bucket_x, bucket_y in zip(x_buckets, y_buckets):
        x_total *= bucket_x
        y_total *= bucket_y

    x_value = min(max(x, min_inclusive_value), max_inclusive_value)
    y_value = min(max(y, min_inclusive_value), max_inclusive_value)
    x_ind = int(x_value * x_total)
    y_ind = int(y_value * y_total)

    total_x_bucket, total_y_bucket = x_ind, y_ind
    split_embedding_buckets = []
    x_bounds = (0, x_total)
    y_bounds = (0, y_total)

    for x_bucket, y_bucket in zip(x_buckets, y_buckets):
        x_range = x_bounds[1] - x_bounds[0]
        y_range = y_bounds[1] - y_bounds[0]

        adjusted_x_bucket = int((total_x_bucket - x_bounds[0]) / x_range * x_bucket)
        adjusted_y_bucket = int((total_y_bucket - y_bounds[0]) / y_range * y_bucket)

        split_embedding_buckets.append(adjusted_y_bucket * x_bucket + adjusted_x_bucket)

        # Adjusting bounds

        x_bounds = (
            x_bounds[0] + x_range // x_bucket * adjusted_x_bucket,
            x_bounds[0] + x_range // x_bucket * (adjusted_x_bucket + 1),
        )
        y_bounds = (
            y_bounds[0] + y_range // y_bucket * adjusted_y_bucket,
            y_bounds[0] + y_range // y_bucket * (adjusted_y_bucket + 1),
        )

    return tuple(split_embedding_buckets)


class EmbeddingSampler:
    def __init__(self, goal_percentage_emphasized: Optional[float], seed: int = 10) -> None:
        self._goal_percentage_emphasized = goal_percentage_emphasized
        self._embedding_bucket_dict: Dict[Any, Any] = {}
        self._metadata_index_embedding_indices_to_categories: Dict[int, Dict[Tuple[int, ...], Any]] = {}
        self._rng = np.random.default_rng(seed)

    def add(
        self,
        embedding_bucket_index: Tuple[int, ...],
        row_index: int,
        category: str,
        is_emphasized: Optional[bool],
        use_category_as_key: bool,
    ) -> None:
        buckets_dict = self._embedding_bucket_dict

        category_upper = category.upper()

        embedding_bucket_items = [(index, "embedding") for index in embedding_bucket_index]

        # Add sampling items into self._embedding_bucket_dict.
        for key, type in [
            (is_emphasized, "emphasized"),
            (category_upper if use_category_as_key else None, "category"),
            *embedding_bucket_items[:-1],
        ]:
            buckets_dict = buckets_dict.setdefault(key, {"type": type, "value": {}})["value"]

        embedding_bucket_items_set = buckets_dict.setdefault(
            embedding_bucket_items[-1][0], {"type": embedding_bucket_items[-1][1], "value": set()}
        )["value"]
        embedding_bucket_items_set.add(row_index)

        # Add sampling items into self._metadata_index_embedding_indices_to_categories.
        row_index_dict = self._metadata_index_embedding_indices_to_categories.setdefault(row_index, {})
        embedding_bucket_index_set = row_index_dict.setdefault(embedding_bucket_index, set())
        embedding_bucket_index_set.add(category_upper)

    def convert_items_container(self,) -> None:
        # Convert the last set of self._embedding_bucket_dict to list.
        stack = [self._embedding_bucket_dict]
        while stack:
            current_dict = stack.pop()
            for node in current_dict.values():
                if isinstance(node, dict):
                    value = node.get("value", None)

                    if isinstance(value, dict):
                        stack.append(value)
                    elif isinstance(value, set):
                        node["value"] = list(value)

        # Convert the last set of self._metadata_index_embedding_indices_to_categories to list.
        for row_index_dict in self._metadata_index_embedding_indices_to_categories.values():
            row_index_dict.update({key: list(val) for key, val in row_index_dict.items()})

    def sample(self) -> Tuple[int, Tuple[int, ...], str]:
        buckets_dict = self._embedding_bucket_dict
        chosen_embedding = []
        chosen_category = None
        if self._goal_percentage_emphasized is not None:
            emphasized = self._goal_percentage_emphasized < self._rng.uniform()
        else:
            emphasized = self._rng.choice(list(buckets_dict.keys()))
        buckets_dict = buckets_dict[emphasized]["value"]
        while type(buckets_dict) != list:
            key: Any = self._rng.choice(list(buckets_dict.keys()))
            buckets_dict = buckets_dict[key]
            if buckets_dict["type"] == "embedding":
                chosen_embedding.append(int(key))
            elif buckets_dict["type"] == "category":
                chosen_category = key
            buckets_dict = buckets_dict["value"]
        inner_list = cast(List[int], buckets_dict)
        embedding_ind = tuple(chosen_embedding)
        index = self._rng.choice(inner_list)

        if chosen_category is None:
            chosen_category = self._rng.choice(
                self._metadata_index_embedding_indices_to_categories[index][embedding_ind]
            )

        assert (
            chosen_category is not None and embedding_ind is not None
        ), f"{chosen_category} and {chosen_embedding}, {self._metadata_index_embedding_indices_to_categories[index][embedding_ind]}"

        return index, embedding_ind, chosen_category


def extract_image_ids_from_dataset(json_filepath: str) -> Set[str]:
    if json_filepath.endswith(".json"):
        with open(json_filepath) as f:
            dataset = json.load(f)

        return set([item["id"] for item in dataset["images"]])

    elif json_filepath.endswith(".jsonl"):
        image_ids = set()
        with open(json_filepath) as f:
            for line in f:
                item = json.loads(line)
                image_ids.add(item["image_id"])
        return image_ids

    return set()

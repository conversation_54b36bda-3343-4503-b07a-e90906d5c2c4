import logging
import math
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Set, Tuple, cast

import cv2
import numpy
import scipy
import torch
from scipy.spatial import cKDTree

from cv.deepweed import deepweed_python
from cv.deepweed.deepweed_python import DeepweedDetection
from deeplearning.deepweed.datasets_types import Dataset<PERSON>abel
from deeplearning.deepweed.metadata import Point, detections_to_points
from deeplearning.utils.tensor import RenderMarkerKind, render_marker
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder_decorator

LOG = logging.getLogger(__name__)

if TYPE_CHECKING:
    from generated.cv.runtime.proto.cv_runtime_pb2 import HitClassValue

BEAM_RADIUS_IN = (5.0 / 2.0) / 25.4


def compute_downsampled_size(input_shape: Tuple[int, int], downsample: int) -> <PERSON><PERSON>[int, int]:
    return (math.ceil(input_shape[0] / downsample), math.ceil(input_shape[1] / downsample))


def get_zero_tensors(
    input: DatasetLabel, num_hits: int, point_categories_size: int, downsampled_size: Tuple[int, int],
) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
    hits_tensor = torch.zeros((input.mask.shape[0], num_hits) + downsampled_size, device=input.mask.device)
    categories_tensor = torch.zeros(
        (input.mask.shape[0], point_categories_size) + downsampled_size, device=input.mask.device
    )
    offsets_tensor = torch.zeros((input.mask.shape[0], num_hits) + downsampled_size + (2,), device=input.mask.device)
    sizes_tensor = torch.zeros((input.mask.shape[0], num_hits) + downsampled_size, device=input.mask.device)
    confidence_tensor = torch.ones((input.mask.shape[0], 1) + downsampled_size, device=input.mask.device)
    crop_protection_tensor = torch.ones(
        (input.mask.shape[0], 1) + downsampled_size, device=input.mask.device, dtype=torch.float
    )

    return hits_tensor, categories_tensor, offsets_tensor, sizes_tensor, confidence_tensor, crop_protection_tensor


def make_centroid_tensors(  # noqa: C901
    input: DatasetLabel,
    point_categories: List[str],
    supported_hit_classes: List["HitClassValue"],
    downsample: int = 1,
    skip_too_large_centroids: bool = True,
    skip_too_small_centroids: bool = True,
    confidence_padding: int = 0,
    crop_protection_padding: int = 0,
    baby_crop_size: float = 0.0,
    crop_protection_multiplier: float = 1,
    baby_crop_protection_multiplier: float = 1,
) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
    category_to_index = {k: v for v, k in enumerate(point_categories)}
    num_hits = len(supported_hit_classes)
    downsampled_size = compute_downsampled_size((input.mask.shape[2], input.mask.shape[3]), downsample)
    (
        hits_tensor,
        categories_tensor,
        offsets_tensor,
        sizes_tensor,
        confidence_tensor,
        crop_protection_tensor,
    ) = get_zero_tensors(input, num_hits, len(point_categories), downsampled_size)

    # adjust downsample rates based on actual shapes
    adjusted_downsample_x = input.mask.shape[3] / downsampled_size[1]
    adjusted_downsample_y = input.mask.shape[2] / downsampled_size[0]

    for n in range(len(input.points)):
        # Bilinear interpolation for the centroid
        for point in input.points[n]:
            (cx, cy, r, c, hit_clz, confidence) = (
                point.x,
                point.y,
                point.r,
                category_to_index.get(point.clz, 0),
                point.hit_clz,
                point.confidence,
            )
            # Shift centroids by 0.5
            cx += 0.5
            cy += 0.5

            hit_clz = point.hit_clz

            # Downsample centroids
            cx /= adjusted_downsample_x
            cy /= adjusted_downsample_y

            # Subtract 0.5 from downsampled centroids
            cx -= 0.5
            cy -= 0.5

            # Compute floor and floating remainder
            cy_, cx_ = math.floor(cy), math.floor(cx)

            # Adjust radius
            r /= (adjusted_downsample_x + adjusted_downsample_y) / 2

            # Size based hit multiplier
            if r > 0.75 and skip_too_large_centroids:
                hit_multiplier = max(0, 4 - r * 4)
            elif r < 0.25 and skip_too_small_centroids:
                hit_multiplier = r * 4
            else:
                hit_multiplier = 1

            if r > baby_crop_size:
                cpm = crop_protection_multiplier
            else:
                cpm = baby_crop_protection_multiplier

            hit_y_xs = []
            for offset in [[0, 0], [1, 0], [0, 1], [1, 1], [-1, -1], [-1, 0], [0, -1], [1, -1], [-1, 1]]:
                if (
                    cy_ + offset[0] >= 0
                    and cx_ + offset[1] >= 0
                    and cy_ + offset[0] < downsampled_size[0]
                    and cx_ + offset[1] < downsampled_size[1]
                ):
                    cy0, cx0 = cy - (cy_ + offset[0]), cx - (cx_ + offset[1])
                    if cy0 <= 1 and cx0 <= 1:
                        if hit_multiplier > hits_tensor[n, hit_clz, cy_ + offset[0], cx_ + offset[1]]:
                            hit_y_xs.append((cy_ + offset[0], cx_ + offset[1]))
                            hits_tensor[n, HitClass.PLANT, cy_ + offset[0], cx_ + offset[1]] = hit_multiplier
                            hits_tensor[n, hit_clz, cy_ + offset[0], cx_ + offset[1]] = hit_multiplier
                            offsets_tensor[n, hit_clz, cy_ + offset[0], cx_ + offset[1], 0] = cy0
                            offsets_tensor[n, hit_clz, cy_ + offset[0], cx_ + offset[1], 1] = cx0
                            offsets_tensor[n, HitClass.PLANT, cy_ + offset[0], cx_ + offset[1], 0] = cy0
                            offsets_tensor[n, HitClass.PLANT, cy_ + offset[0], cx_ + offset[1], 1] = cx0
                            categories_tensor[n, c, cy_ + offset[0], cx_ + offset[1]] = hit_multiplier
                            sizes_tensor[n, hit_clz, cy_ + offset[0], cx_ + offset[1]] = r
                            sizes_tensor[n, HitClass.PLANT, cy_ + offset[0], cx_ + offset[1]] = r
                            if confidence == 0:
                                y_min = max(cy_ + offset[0] - confidence_padding, 0)
                                y_max = min(cy_ + offset[0] + (confidence_padding + 1), downsampled_size[0])
                                x_min = max(cx_ + offset[1] - confidence_padding, 0)
                                x_max = min(cx_ + offset[1] + (confidence_padding + 1), downsampled_size[1])

                                confidence_tensor[n, 0, y_min:y_max, x_min:x_max] = 0

                            if confidence == 3:
                                crop_mask_array = cv2.circle(
                                    numpy.ones(confidence_tensor.shape), (cx_, cy_), int(r), (0,), -1
                                )
                                crop_mask_tensor = torch.tensor(crop_mask_array, device=confidence_tensor.device)
                                confidence_tensor = confidence_tensor * crop_mask_tensor

                            if hit_clz == HitClass.CROP:
                                y_min = max(cy_ + offset[0] - crop_protection_padding, 0)
                                y_max = min(cy_ + offset[0] + (crop_protection_padding + 1), downsampled_size[0])
                                x_min = max(cx_ + offset[1] - crop_protection_padding, 0)
                                x_max = min(cx_ + offset[1] + (crop_protection_padding + 1), downsampled_size[1])

                                crop_protection_tensor[n, 0, y_min:y_max, x_min:x_max] = cpm
            point.set_hit_y_x(hit_y_xs)

    return hits_tensor, categories_tensor, offsets_tensor, sizes_tensor, confidence_tensor, crop_protection_tensor


def render_hits(hits: torch.Tensor, size: Tuple[int, int]) -> torch.Tensor:
    assert len(hits.shape) == 4, f"Input has wrong shape: {hits.shape}"
    return cast(torch.Tensor, torch.nn.functional.interpolate(hits.to(torch.float32), size=size))


def compute_points(
    hits: List[torch.Tensor],
    categories: List[torch.Tensor],
    offsets: List[torch.Tensor],
    sizes: List[torch.Tensor],
    embeddings: List[torch.Tensor],
    size: Tuple[int, int],
    beam_radius_px: float,
    weed_point_classes: List[str],
    enable_weed: bool = True,
    enable_crop: bool = True,
    thresholds: Optional[Dict["HitClassValue", float]] = None,
) -> Tuple[List[List[Point]], List[List[Point]]]:
    thresholds = thresholds if thresholds is not None else {}
    detections_hat = deepweed_python.compute_deepweed_detections_v2(
        hits,
        categories,
        offsets,
        sizes,
        embeddings,
        size,
        weed_point_classes,
        thresholds.get(HitClass.WEED, 0.5),
        thresholds.get(HitClass.CROP, 0.5),
        thresholds.get(HitClass.PLANT, 0.5),
        enable_weed,
        enable_crop,
        True,
        False,
        False,
    )
    detections_hat_nms: List[List[DeepweedDetection]] = []
    for n in range(len(detections_hat)):
        detections_hat_nms.append(deepweed_python.non_maximum_suppression(detections_hat[n], beam_radius_px))
    result: List[List[Point]] = []
    for n in range(len(detections_hat)):
        detections = detections_to_points(detections_hat[n], 0.5, weed_point_classes)
        result.append(detections)
    result_nms: List[List[Point]] = []
    for n in range(len(detections_hat)):
        detections_nms = detections_to_points(detections_hat_nms[n], 0.5, weed_point_classes)
        result_nms.append(detections_nms)

    return result, result_nms


def non_maximum_suppression(points: List[Point], beam_radius_px: float, overlap_threshold: float = 0.0) -> List[Point]:
    if len(points) == 0:
        return []

    point_query_f = lambda p: (p.x, p.y)
    points = list(set(points))
    points_to_keep: Set[Point] = set()
    points_to_discard: Set[Point] = set()

    points_kd = cKDTree([point_query_f(p) for p in points])
    for p in sorted(points, key=lambda p: -p.score):
        if p in points_to_discard:
            continue

        # keep points we're not discarding
        points_to_keep.add(p)

        # search for points to discard
        _, p2_indices = points_kd.query(point_query_f(p), k=100, distance_upper_bound=beam_radius_px * 2)
        for p2_idx in p2_indices:
            if p2_idx == points_kd.n:
                break

            p2 = points[p2_idx]
            if p2 in points_to_keep:
                # ignore points we've decided to keep
                continue

            intersection = circles_intersect_area(p.x, p.y, beam_radius_px, p2.x, p2.y, beam_radius_px)
            if intersection / (2 * circle_area(beam_radius_px) - intersection + 1e-5) > overlap_threshold:
                # discard points with sufficient intersection
                points_to_discard.add(p2)

    assert len(points) == len(points_to_keep) + len(
        points_to_discard
    ), f"{len(points)} != {len(points_to_keep)} + {len(points_to_discard)}: {[p for p in points_to_keep if p in points_to_discard]}"
    return list(points_to_keep)


def render_point_circles(
    point_hits: List[torch.Tensor],
    point_categories: List[torch.Tensor],
    point_offsets: List[torch.Tensor],
    point_sizes: List[torch.Tensor],
    size: Tuple[int, int],
    beam_radius_px: float,
    weed_point_classes: List[str],
    thresholds: Optional[Dict["HitClassValue", float]] = None,
    nms: bool = True,
) -> torch.Tensor:
    thresholds = thresholds if thresholds is not None else {}
    mask = torch.zeros(point_hits[0].shape[0:2] + size, device=point_hits[0].device, dtype=torch.bool)

    points, points_nms = compute_points(
        point_hits,
        point_categories,
        point_offsets,
        point_sizes,
        [torch.zeros_like(h) for h in point_hits],
        size=size,
        weed_point_classes=weed_point_classes,
        beam_radius_px=beam_radius_px,
        thresholds=thresholds,
    )
    if nms:
        points = points_nms

    for n in range(len(points)):
        for p in points[n]:
            render_marker(mask[n, p.hit_clz], int(p.x), int(p.y), int(p.r), RenderMarkerKind.CIRCLE)
    return mask


def circle_area(r: float) -> float:
    return math.pi * r ** 2


def circles_intersect_area(cx1: float, cy1: float, r1: float, cx2: float, cy2: float, r2: float) -> float:
    # https://diego.assencio.com/?index=8d6ca3d82151bad815f78addf9b5c1c6
    # require r1 >= r2
    if r2 > r1:
        cx1, cx2 = cx2, cx1
        cy1, cy2 = cy2, cy1
        r1, r2 = r2, r1

    d = math.hypot(cx1 - cx2, cy1 - cy2)
    if d <= r1 - r2:
        return circle_area(r2)
    if r1 + r2 <= d:
        return 0.0

    d1 = (r1 ** 2 - r2 ** 2 + d ** 2) / (2 * d)
    d2 = d - d1

    return (
        r1 ** 2 * math.acos(d1 / r1)
        - d1 * math.sqrt(r1 ** 2 - d1 ** 2)
        + r2 ** 2 * math.acos(d2 / r2)
        - d2 * math.sqrt(r2 ** 2 - d2 ** 2)
    )


def is_low_confidence(
    cx: float, cy: float, discard_border_px: float, width: int, height: int, confidence: int, use_confidence: bool,
) -> bool:
    # If label not matched but it's in border forgiveness area, ignore it.
    # Also, if label is not matched but it's low confidence, ignore it as well.
    # This allows model to correctly predict points in the border area without
    # getting penalized, but get penalized for predicting non-existent point.
    # Model won't get penalized for not predicting point in the border area.
    return (
        cx < discard_border_px
        or cx >= width - discard_border_px
        or cy < discard_border_px
        or cy >= height - discard_border_px
    ) or (confidence == 0 and use_confidence)


def point_overlap_check(
    cx: float, cy: float, cx_h: float, cy_h: float, beam_radius_px: float, overlap_threshold: float
) -> bool:
    intersect_area = circles_intersect_area(cx, cy, beam_radius_px, cx_h, cy_h, beam_radius_px)
    return intersect_area / (2 * circle_area(beam_radius_px) - intersect_area + 1e-5) > overlap_threshold


@duration_perf_recorder_decorator(PerfCategory.TRAINING)
def point_f1_components(  # noqa
    input_points: List[List[Point]],
    target: List[List[Point]],
    shape: List[int],
    device: torch.device,
    beam_radius_px: float,
    discard_border_px: float,
    batch_enabled_weed_point_classes: torch.Tensor,
    batch_enabled_hits: torch.Tensor,
    point_categories: List[str],
    supported_hit_classes: List["HitClassValue"],
    overlap_threshold: float = 0.5,
    use_confidence: bool = False,
    categorized_correctly: bool = False,
) -> Tuple[
    torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor
]:
    category_to_index = {k: v for v, k in enumerate(point_categories)}
    num_hits = len(supported_hit_classes)
    overlap_counts = torch.zeros(shape[0], num_hits)
    overlap_ignore_class_counts = torch.zeros(shape[0], len(point_categories))
    input_counts = torch.zeros(shape[0], num_hits)
    target_counts = torch.zeros(shape[0], num_hits)
    input_counts_category = torch.zeros(shape[0], len(point_categories))
    target_counts_category = torch.zeros(shape[0], len(point_categories))
    class_confusion_matrix = torch.zeros(shape[0], len(point_categories), len(point_categories))
    hit_confusion_matrix = torch.zeros(shape[0], num_hits, num_hits)

    for n in range(len(input_points)):
        centroids_hat = [(p.x, p.y, p.r, category_to_index.get(p.clz, -1), p.hit_clz) for p in input_points[n]]
        centroids = [(p.x, p.y, p.r, category_to_index.get(p.clz, -1), p.hit_clz, p.confidence) for p in target[n]]

        for (cx, cy, r, clz, hit_clz, conf) in centroids:
            # Only add high confidence and non-border region points for now
            if not is_low_confidence(cx, cy, discard_border_px, shape[3], shape[2], conf, use_confidence):
                target_counts[n, hit_clz] += 1
                target_counts[n, HitClass.PLANT] += 1
                if hit_clz == HitClass.WEED and batch_enabled_weed_point_classes[n, clz]:
                    target_counts_category[n, clz] += 1

        for (cx, cy, r, clz, hit_clz) in centroids_hat:
            input_counts[n, hit_clz] += 1
            if hit_clz == HitClass.WEED and batch_enabled_weed_point_classes[n, clz]:
                input_counts_category[n, clz] += 1

        if len(centroids) == 0 or len(centroids_hat) == 0:
            continue

        centroids_hat_kd = cKDTree([(cx_h, cy_h) for (cx_h, cy_h, r_h, clz_h, hit_clz_h) in centroids_hat])
        matched_indices = set()
        for (cx, cy, r, clz, hit_clz, conf) in centroids:
            _, c_h_indices = centroids_hat_kd.query((cx, cy), k=100, distance_upper_bound=beam_radius_px * 2)
            c_h_indices_sorted = sorted(
                [x for x in c_h_indices if x < centroids_hat_kd.n],
                key=lambda x: 0 if centroids_hat[x][4] == hit_clz else 1,
            )

            plant_matched = False
            c_h_indices_sorted_plants = [x for x in c_h_indices_sorted if centroids_hat[x][4] == HitClass.PLANT]
            for c_h_idx in c_h_indices_sorted_plants:
                if c_h_idx in matched_indices:
                    continue

                (cx_h, cy_h, r_h, clz_h, hit_clz_h) = centroids_hat[c_h_idx]

                if not point_overlap_check(cx, cy, cx_h, cy_h, beam_radius_px, overlap_threshold):
                    continue

                matched_indices.add(c_h_idx)
                overlap_counts[n, HitClass.PLANT] += 1
                plant_matched = True
                break

            matched = False
            c_h_indices_sorted_not_plants = [x for x in c_h_indices_sorted if centroids_hat[x][4] != HitClass.PLANT]
            for c_h_idx in c_h_indices_sorted_not_plants:
                if c_h_idx in matched_indices:
                    continue

                (cx_h, cy_h, r_h, clz_h, hit_clz_h) = centroids_hat[c_h_idx]

                if categorized_correctly and hit_clz == HitClass.WEED and clz != clz_h:
                    continue

                if not point_overlap_check(cx, cy, cx_h, cy_h, beam_radius_px, overlap_threshold):
                    continue

                hit_confusion_matrix[n, hit_clz, hit_clz_h] += 1
                # Only care about class for weeds whose class is enabled for weed classification
                if hit_clz == HitClass.WEED and clz != -1 and clz_h != -1 and batch_enabled_weed_point_classes[n, clz]:
                    class_confusion_matrix[n, clz, clz_h] += 1
                    overlap_ignore_class_counts[n, clz] += 1

                matched_indices.add(c_h_idx)
                matched = True

                if hit_clz_h == hit_clz:
                    overlap_counts[n, hit_clz] += 1

                break

            if is_low_confidence(cx, cy, discard_border_px, shape[3], shape[2], conf, use_confidence):
                if matched:
                    target_counts[n, hit_clz] += 1
                    if hit_clz == HitClass.WEED and clz != -1 and batch_enabled_weed_point_classes[n, clz]:
                        target_counts_category[n, clz] += 1
                if plant_matched:
                    target_counts[n, HitClass.PLANT] += 1

    for n in range(len(input_points)):
        for hit_clz in supported_hit_classes:
            if not batch_enabled_hits[n, hit_clz]:
                overlap_counts[n, hit_clz] = 0
                input_counts[n, hit_clz] = 0
                target_counts[n, hit_clz] = 0

    return (
        overlap_counts.to(device),
        overlap_ignore_class_counts.to(device),
        input_counts.to(device),
        target_counts.to(device),
        class_confusion_matrix.to(device),
        hit_confusion_matrix.to(device),
        input_counts_category.to(device),
        target_counts_category.to(device),
    )


# Match labeled weeds first with predicted weeds, then match labeled crops with predicted weeds to determine
# total number of crops shot
@duration_perf_recorder_decorator(PerfCategory.TRAINING)
def crops_shot(
    input_points: List[List[Point]],
    target: List[List[Point]],
    shape: List[int],
    device: torch.device,
    beam_radius_px: float,
    point_categories: List[str],
    discard_border_px: float,
    overlap_threshold: float = 0.5,
) -> Tuple[torch.Tensor, torch.Tensor]:
    category_to_index = {k: v for v, k in enumerate(point_categories)}
    crops_shot = torch.zeros(shape[0])
    crops_available = torch.zeros(shape[0])

    for n in range(len(input_points)):
        centroids_hat = [(p.x, p.y, p.r, category_to_index.get(p.clz, -1), p.hit_clz) for p in input_points[n]]
        centroids = [(p.x, p.y, p.r, category_to_index.get(p.clz, -1), p.hit_clz, p.confidence) for p in target[n]]
        # Match labeled weeds first, so we know when matching labeled crops to weeds there was no labeled weed
        # for that predicted weed.
        centroids = sorted(centroids, key=lambda x: 0 if x[4] == HitClass.WEED else 1)

        if len(centroids) == 0 or len(centroids_hat) == 0:
            for cx, cy, r, clz, hit_clz, conf in centroids:
                if hit_clz == HitClass.CROP and not (
                    (
                        cx < discard_border_px
                        or cx >= shape[3] - discard_border_px
                        or cy < discard_border_px
                        or cy >= shape[2] - discard_border_px
                    )
                ):
                    crops_available[n] += 1
            continue

        centroids_hat_kd = cKDTree([(cx_h, cy_h) for (cx_h, cy_h, r_h, clz_h, hit_clz_h) in centroids_hat])
        matched_indices = set()
        for cx, cy, r, clz, hit_clz, conf in centroids:
            shot = False
            _, c_h_indices = centroids_hat_kd.query((cx, cy), k=100, distance_upper_bound=beam_radius_px * 2)
            for c_h_idx in c_h_indices:
                if c_h_idx == centroids_hat_kd.n:
                    break
                if c_h_idx in matched_indices:
                    continue

                (cx_h, cy_h, r_h, clz_h, hit_clz_h) = centroids_hat[c_h_idx]
                # Only care about predicted weeds for this metric
                if hit_clz_h != HitClass.WEED:
                    continue

                intersect_area = circles_intersect_area(cx, cy, beam_radius_px, cx_h, cy_h, beam_radius_px)
                if intersect_area / (2 * circle_area(beam_radius_px) - intersect_area + 1e-5) > overlap_threshold:
                    if hit_clz == HitClass.CROP:
                        crops_shot[n] += 1
                        shot = True
                    matched_indices.add(c_h_idx)
                    break

            # Don't count crops that were not shot and were within the border region or low confidence as available crops to be shot
            if not shot and (
                (
                    cx < discard_border_px
                    or cx >= shape[3] - discard_border_px
                    or cy < discard_border_px
                    or cy >= shape[2] - discard_border_px
                )
            ):
                pass
            elif hit_clz == HitClass.CROP:
                crops_available[n] += 1

    return crops_shot.to(device), crops_available.to(device)


def f1_components(
    input: torch.Tensor, target: torch.Tensor, thresholds: List[float],
) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    threshold = torch.tensor(thresholds).to(input.device).unsqueeze(-1).unsqueeze(-1)
    overlap_sum = ((input > threshold) & (target > threshold)).sum((-2, -1))
    input_sum = (input > threshold).sum((-2, -1))
    target_sum = (target > threshold).sum((-2, -1))
    return overlap_sum, input_sum, target_sum


def get_merged_points(
    points_hat: List[Point], points_truth: List[Point], dist_threshold: int = 15, score_threshold: float = 0.5
) -> List[Dict[str, Any]]:
    if len(points_hat) <= 1 or len(points_truth) <= 1:
        return []

    points_hat = [p for p in points_hat if p.score > score_threshold]

    A = numpy.array([(p.x, p.y) for p in points_hat])
    B = numpy.array([(p.x, p.y) for p in points_truth])

    distances = scipy.spatial.distance.cdist(A, B)  # Returns array of shape (len(points_hat), len(points_truth))

    merged_list = []
    for hat_index, point_hat in enumerate(points_hat):
        point_info = {
            "x": point_hat.x,
            "y": point_hat.y,
            "r": point_hat.r,
            "score": point_hat.score,
            "confidence": point_hat.confidence,
            "predicted_clz": point_hat.clz,
            "predicted_hit_clz": point_hat.hit_clz,
        }

        min_idx = int(numpy.argmin(distances[hat_index]).item())

        if distances[hat_index, min_idx] < dist_threshold:
            point_info["true_clz"] = points_truth[min_idx].clz
            point_info["true_hit_clz"] = points_truth[min_idx].hit_clz
            point_info["relocation_info"] = points_truth[min_idx].relocation_info

        merged_list.append(point_info)

    return merged_list


def get_embedding_at_hit_point(embeddings: torch.Tensor, batch_idx: int, *, y: int, x: int) -> torch.Tensor:
    return embeddings[batch_idx, :, y, x]

import functools
import io
import itertools
import json
import logging
import os
import pickle
import random
import shutil
import socket
import tempfile
import time
import uuid
from collections import defaultdict
from multiprocessing import Process
from threading import Thread
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional, Sequence, Tuple, Union, cast

import cv2
import matplotlib.patches as patches
import matplotlib.pyplot as plt
import numpy as np
import numpy.typing as npt
import pandas
import sqlalchemy
import torch
import torch.nn.functional as F
import torchvision.transforms.functional as TF
import umap
import wandb
from autograd_lib import autograd_lib
from lightning.pytorch.loggers import WandbLogger
from PIL import Image
from sqlalchemy.sql.expression import func
from tabulate import tabulate
from torch.utils.data import DataLoader, Sampler

import deeplearning.server.trt_runtime as rt
import deeplearning.utils.cv2_segfault_fix  # noqa
import simplejson
from _thread import interrupt_main
from deeplearning import dl_metrics
from deeplearning.comparison.data_utils import (
    ComparisonEmbeddingObject,
    embeddings_to_dataframes,
    save_image_level_embedding_files_to_torch,
)
from deeplearning.constants import CARBON_DATA_DIR, DeepweedTrainingSubtype
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.constants import REMOTE_VESELKA_DATASET_SERVER_PORT
from deeplearning.deepweed.datapoint_timestamps import DatapointTimestamps
from deeplearning.deepweed.datasets_types import DatasetLabel, DeepweedDatapoint
from deeplearning.deepweed.deployment import (
    comparison_deployment_check,
    compensation_check,
    get_crops_and_weeds_shot,
    old_and_new_data_delta,
)
from deeplearning.deepweed.losses import point_loss, segmentation_loss
from deeplearning.deepweed.losses.embeddings_loss import embeddings_loss, info_nce_loss, triplet_loss
from deeplearning.deepweed.losses.loss_tracker import LossTracker
from deeplearning.deepweed.metadata import ImageMetadata
from deeplearning.deepweed.metrics import OVERLAP_THRESHOLDS, AverageMetrics, DlMetricsRunner, Metrics
from deeplearning.deepweed.metrics.aggregate_metrics import AggregateMetrics
from deeplearning.deepweed.metrics.utils import compute_oec, compute_point_oec
from deeplearning.deepweed.model import Deepweed, DeepweedOutput, DeepweedOutputFactory
from deeplearning.deepweed.point_utils import (
    BEAM_RADIUS_IN,
    Point,
    RenderMarkerKind,
    compute_points,
    render_hits,
    render_marker,
)
from deeplearning.deepweed.remote_veselka_dataset import (
    RemoteVeselkaDatasetClient,
    RemoteVeselkaDatasets,
    RemoteVeselkaDatasetServer,
    initialize_datasets,
)
from deeplearning.deepweed.sampling_visualization import save_sampling_file
from deeplearning.deepweed.trt_convert import TrtConvert
from deeplearning.deepweed.version import get_version
from deeplearning.deepweed.veselka_utils import get_reverse_sorted_evaluation_ids_by_model_id
from deeplearning.dl_metrics.dl_metrics.points_db import EmbeddingType
from deeplearning.embeddings.io import (
    EmbeddingDatapoint,
    EmbeddingDatapointMetadata,
    EmbeddingDatapointPrediction,
    EmbeddingDataset,
    EmbeddingDatasetMetadata,
    EmbeddingLookupTable,
    EmbeddingLookupTables,
)
from deeplearning.model_io import ModelMetadata, load_tensorrt_model
from deeplearning.model_io.pytorch import load_pytorch_model, peek_pytorch_metadata, save_pytorch_model
from deeplearning.parametric_umap.model import ParametricUMAP
from deeplearning.parametric_umap.trainer import PUMAPTrainer
from deeplearning.utils.dataset import DatasetType
from deeplearning.utils.debug_signal import DebugSignal
from deeplearning.utils.download_utils import download_records, download_resume_model
from deeplearning.utils.fire_utils import safe_split
from deeplearning.utils.images import IMAGENET_MEANS, IMAGENET_STDS
from deeplearning.utils.resize_utils import tile_crop_origins
from deeplearning.utils.resnet import download_weights_to_cache, get_resnet50_weights_url
from deeplearning.utils.tensor import gather_objects, make_texture_tensor
from deeplearning.utils.trainer import (
    S3_BUCKET,
    Environment,
    Trainer,
    TrainingModule,
    add_timing_metrics,
    compute_md5sum,
    conv_2d_backward,
    get_examples_with_highest_last_n_epochs,
    get_tensorrt_file_name,
    worker_init_fn,
)
from deeplearning.utils.use_cases import ModelUseCase
from deeplearning.utils.wandb import get_wandb_metadata_json_str
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass
from lib.common.collections.list import flatten
from lib.common.perf.perf_tracker import (
    PerfCategory,
    duration_perf_recorder,
    duration_perf_recorder_decorator,
    set_verbosity,
)
from lib.common.s3_cache_proxy.client import S3CacheProxyClient
from lib.common.time.time import maka_control_timestamp_ms

LOG = logging.getLogger(__name__)
set_verbosity(False)

if TYPE_CHECKING:
    from generated.cv.runtime.proto.cv_runtime_pb2 import HitClassValue

MIN_OVERLAPPING_NEW = 8


MAX_DISTANCE_MM = 5.0
MIN_PLANT_SCORE = 0.05

DATASET_TYPES = Union[RemoteVeselkaDatasetClient]


def get_model_embedding_lookup_path(exp_dir: str, prefix: str, embedding_type: EmbeddingType) -> str:
    return os.path.join(exp_dir, f"{prefix}embedding_lookup_{embedding_type.name.lower()}/embeddings.h5")


class DeepweedTrainingModule(TrainingModule):
    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def __init__(
        self,
        datasets: RemoteVeselkaDatasets,
        use_cases: List[str],
        config: DeepweedConfig,
        overprediction_warmup: int = 0,
        train_log_image_p: float = 0.05,
        val_log_image_p: float = 0.05,
        tile_height: Optional[int] = None,
        tile_width: Optional[int] = None,
        eval_four_flips: bool = False,
        log_gradient_norms: bool = False,
        start_logging_gradient_norms_at_epoch: int = 0,
        use_confidence: bool = False,
        confidence_padding: int = 0,
        no_crop_background_multiplier: float = 2,
        additional_wandb_config: Dict[str, Any] = {},
        pretrained_model: Optional[str] = None,
        pretrained_segmentation_model: Optional[str] = None,
        model: Optional[Deepweed] = None,
        int8_calibration: Optional[str] = None,
        frozen_backbone_point_layers: Optional[List[str]] = None,
        positive_sample_percentage: float = 10 / 11,
        positive_sample_gamma: float = 1.0,
        positive_sample_milestones: Optional[List[int]] = None,
        ci_run: bool = False,
        convert_to_sql_metrics: bool = True,
        segm_dice_loss_weight: float = 1.0,
        segm_bce_loss_weight: float = 5.0,
        new_data_weight: Optional[float] = None,
        save_image_point_embeddings: bool = False,
        save_hdf5_embeddings: bool = False,
        profile_memory: bool = False,
        dataset_id: Optional[str] = None,
    ) -> None:
        super().__init__()

        self._memory_profile_dir: Optional[str] = None
        if profile_memory:
            self._memory_profile_dir = f"{CARBON_DATA_DIR}/deeplearning/models/{config.model_id}/cuda_memory_profiling"
            os.makedirs(self._memory_profile_dir, exist_ok=True)
            torch.cuda.memory._record_memory_history(max_entries=100000)

        self.config = config
        self.segm_dice_loss_weight = segm_dice_loss_weight
        self.segm_bce_loss_weight = segm_bce_loss_weight
        self.new_data_weight = new_data_weight

        self.datasets = datasets
        self.discard_points_border_px = self.datasets.discard_point_border_px
        self.trt_model: Optional[torch.nn.Module] = None
        self._ci_run = ci_run
        self._dataset_id = dataset_id

        self._capture_embeddings = (
            config.comparison_model_id is not None and config.train_embeddings
        ) or config.save_embeddings_to_points_db

        self._positive_sample_percentage = positive_sample_percentage
        self._positive_sample_gamma = positive_sample_gamma
        self._positive_sample_milestones = positive_sample_milestones

        self.contains_trt_pumap_head: Optional[bool] = False
        self.trt_pumap_shifter_scaler: Optional[Dict[str, float]] = None

        if pretrained_segmentation_model:
            self.pretrained_model_segmentation_classes = peek_pytorch_metadata(
                pretrained_segmentation_model
            ).segm_classes
            if self.pretrained_model_segmentation_classes is None:
                num_segm_classes = 0
            else:
                num_segm_classes = len(self.pretrained_model_segmentation_classes)
        else:
            num_segm_classes = len(self.datasets.get_training().segm_classes)
            self.pretrained_model_segmentation_classes = None

        # We want to freeze segmentation from training if it's not present in training dataset
        freeze_segm = False
        if len(self.datasets.get_training().segm_classes) == 0:
            freeze_segm = True

        url = get_resnet50_weights_url()
        if torch.distributed.get_rank() == 0:
            try:
                download_weights_to_cache(url)
            except:
                LOG.warning("Failed to predownload model weights")

        torch.distributed.barrier()

        self._all_crop_ids = list(
            sorted(
                set(
                    self.datasets.get_training().crop_ids
                    + self.datasets.get_validation().crop_ids
                    + self.datasets.get_test().crop_ids
                )
            )
        )

        keep_crop_id_idxes = None
        if pretrained_model:
            parent_metadata = peek_pytorch_metadata(pretrained_model)
            if torch.distributed.get_rank() == 0:
                LOG.info(f"Number of crops in pretrained model: {len(parent_metadata.crop_ids or [])}")

            if self.config.enable_crop_embeddings:
                assert (
                    parent_metadata.crop_embeddings and parent_metadata.crop_ids
                ), "Pretrained model does not have crop embeddings"
                parent_crop_ids = sorted((parent_metadata.crop_ids))

                keep_crop_id_idxes = [parent_crop_ids.index(crop) for crop in self._all_crop_ids]

        if model is None:
            self.model = Deepweed(
                num_weed_point_classes=len(self.datasets.get_training().weed_classes),
                num_segm_classes=num_segm_classes,
                num_crop_ids=len(self._all_crop_ids),
                enable_crop_embeddings=self.config.enable_crop_embeddings,
                discard_points_border_px=self.discard_points_border_px,
                pretrained_model=pretrained_model,
                pretrained_segmentation_model=pretrained_segmentation_model,
                frozen_backbone_point_layers=frozen_backbone_point_layers,
                disable_crop=len(self.datasets.get_training().crop_classes) == 0,
                freeze_segmentation=freeze_segm,
                config=config,
                use_pumap_head=False,
                crop_id_idxes=keep_crop_id_idxes,
            )

            if self.config.enable_crop_embeddings and torch.distributed.get_rank() == 0:
                LOG.info(f"Number of crop embeddings in model: {self.model.crop_emb_a.num_embeddings}")
        else:
            LOG.info("Using pre-defined model.")
            self.model = model

        self._use_cases = [ModelUseCase[c] for c in use_cases]
        self._crop_id_map = {c.lower(): idx for idx, c in enumerate(self._all_crop_ids)}
        self.overprediction_warmup = overprediction_warmup
        self.train_log_image_p = train_log_image_p
        self.val_log_image_p = val_log_image_p
        self.tile_height = tile_height or 1200
        self.tile_width = tile_width or 1200
        self.eval_four_flips = eval_four_flips
        self.debug_signal = DebugSignal()
        self.num_training_batches = int(
            int(len(self.datasets.get_training()) / self.config.training_batch_size)
            / torch.distributed.get_world_size()
        )
        self.log_gradient_norms = log_gradient_norms
        self.start_logging_gradient_norms_at_epoch = start_logging_gradient_norms_at_epoch
        self.activations: Dict[torch.nn.Module, torch.Tensor] = {}
        if self.log_gradient_norms:
            autograd_lib.register(self.model)
            assert torch.distributed.get_world_size() == 1
        self.gradient_norm_logger: Dict[str, List[Tuple[int, float]]] = {}
        self.use_confidence = use_confidence
        self.confidence_padding = confidence_padding
        self.no_crop_background_multiplier = no_crop_background_multiplier
        self.is_trt_fp16 = False
        self.int8_calibration = int8_calibration
        self._exp_dir: Optional[str] = None
        self._validation_sql_dir: Optional[str] = None
        self._experiment_url: Optional[str] = None
        self._additional_wandb_config = additional_wandb_config
        self._convert_to_sql_metrics = convert_to_sql_metrics
        self._sql_metric_id: Optional[int] = None  # Used to keep track of initial sql metric id
        self._validation_outputs: List[Dict[str, List[Metrics]]] = []
        self._test_outputs: List[Dict[str, List[Metrics]]] = []
        self._training_outputs: List[Metrics] = []
        self._pre_sigmoid_point_hit_threshold = -np.log(1 / self.config.point_hit_threshold - 1)

        if torch.distributed.get_rank() == 0:
            LOG.info(
                "Datasets: train={train} examples, val={val} examples, test={test} examples".format(
                    train=len(self.datasets.get_training()),
                    val=len(self.datasets.get_validation()),
                    test=len(self.datasets.get_test()),
                )
            )

        if self.config.comparison_model_id is not None and self.config.train_embeddings:
            if torch.distributed.get_rank() == 0:
                for d_set in [self.datasets.get_training(), self.datasets.get_validation(), self.datasets.get_test()]:
                    if not d_set.comparison_embeddings_loaded():
                        LOG.info(f"Loading comparison embeddings in {d_set.mode}")
                        d_set.load_comparison_embeddings()
            torch.distributed.barrier()

        if self.config.embedding_balancing_model is not None:
            if torch.distributed.get_rank() == 0:
                for d_set in [self.datasets.get_training(), self.datasets.get_calibration()]:
                    if not d_set.sampling_embeddings_loaded():
                        LOG.info(f"Loading sampling embeddings in {d_set.mode}")
                        d_set.load_sampling_embeddings()
            torch.distributed.barrier()

        if not self.use_confidence:
            assert not self.datasets.get_training().keep_low_confidence
            assert not self.datasets.get_validation().keep_low_confidence
            assert not self.datasets.get_test().keep_low_confidence

        # Set weights to enabled classes
        self.train_segm_class_weights = torch.tensor([1.0 for _ in self.datasets.get_training().segm_classes])
        self.test_segm_class_weights = torch.tensor([1.0 for _ in self.datasets.get_test().segm_classes])
        self.weed_point_class_weights = torch.tensor([1.0 for _ in self.datasets.get_training().weed_classes])
        self.point_hit_weights = torch.tensor(
            [
                1.0 if len(self.datasets.get_training().weed_classes) > 0 else 0.0,
                1.0 if len(self.datasets.get_training().crop_classes) > 0 else 0.0,
                1.0,
            ]
        )
        self.weed_point_category_to_index = {k: v for v, k in enumerate(self.datasets.get_training().weed_classes)}

        self._save_image_point_embeddings = save_image_point_embeddings
        self._save_hdf5_embeddings = save_hdf5_embeddings

        self._sampled_image_filepaths: Dict[str, int] = defaultdict(int)
        self._sampled_image_filepaths_embeddings: Dict[str, List[int]] = defaultdict(list)
        self._sampled_selected_categories: Dict[str, int] = defaultdict(int)
        self._sampled_selected_embeddings: Dict[Any, int] = defaultdict(int)

        self.train_loss_tracker: Optional[LossTracker] = None
        self.validation_loss_tracker: Optional[LossTracker] = None
        self.test_loss_tracker: Optional[LossTracker] = None
        if self.config.save_recording_loss:
            dataset_id = self._dataset_id if self._dataset_id is not None else ""
            self.train_loss_tracker = LossTracker(dataset_id=dataset_id, phase="train",)
            self.validation_loss_tracker = LossTracker(dataset_id=dataset_id, phase="validation",)
            self.test_loss_tracker = LossTracker(dataset_id=dataset_id, phase="test",)

        self._sandbox_dir = tempfile.mkdtemp(
            prefix=f"deepweed_sandbox_{self.config.model_id}_{torch.distributed.get_rank()}"
        )
        self._embeddings_lookup_tables: Dict[EmbeddingType, EmbeddingLookupTable] = {}

    def __del__(self) -> None:
        """Clean up temporary sandbox directory on object destruction."""
        if hasattr(self, "_sandbox_dir") and self._sandbox_dir and os.path.exists(self._sandbox_dir):
            try:
                shutil.rmtree(self._sandbox_dir)
            except (OSError, PermissionError) as e:
                # Log the error but don't raise it, as destructors shouldn't raise exceptions
                LOG.warning(f"Failed to remove sandbox directory {self._sandbox_dir}: {e}")

    def get_tmp_embedding_lookup_table_path(self, embedding_type: EmbeddingType) -> str:
        return f"{self._sandbox_dir}/{embedding_type.name.lower()}/{torch.distributed.get_rank()}.h5"

    def get_embedding_lookup_table_path(self, embedding_type: EmbeddingType) -> str:
        return f"{CARBON_DATA_DIR}/deeplearning/embeddings_lookup_table/{self.config.model_id}/{embedding_type.name.lower()}/{torch.distributed.get_rank()}.h5"

    def create_embedding_lookup_table(
        self, embedding_lookup_table_path: str, embedding_type: EmbeddingType = EmbeddingType.FULL
    ) -> None:
        if os.path.exists(embedding_lookup_table_path):
            os.remove(embedding_lookup_table_path)
        os.makedirs(os.path.dirname(embedding_lookup_table_path), exist_ok=True)
        self._embeddings_lookup_tables[embedding_type] = EmbeddingLookupTable(
            embedding_lookup_table_path, embedding_type.name.lower()
        )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def record_memory_snapshot(self, step: Optional[str] = None, epoch: Optional[str] = None) -> None:
        # Seems like this is keeping memory from blowing up?
        if self._memory_profile_dir is not None:
            filename = f"rank_{torch.distributed.get_rank()}.pickle"
            if step is not None:
                filename = f"step_{step}_{filename}"
            if epoch is not None:
                filename = f"epoch_{epoch}_{filename}"
            torch.cuda.memory._dump_snapshot(os.path.join(self._memory_profile_dir, filename))

    @property
    def supported_classes(self) -> List[str]:
        return self.datasets.get_training().enabled_classes

    @duration_perf_recorder_decorator(f"{PerfCategory.TRAINING}")
    def forward(self, x: torch.Tensor, crop_ids: List[str]) -> torch.Tensor:
        if self.config.enable_crop_embeddings:
            for cid in crop_ids:
                assert cid in self._crop_id_map, f"Crop {cid} is not in crop_id_map: {self._crop_id_map}"
            crop_id_idx = torch.tensor([self._crop_id_map[cid] for cid in crop_ids], dtype=torch.int, device=x.device)
        else:
            crop_id_idx = None

        # Only Use TRT Model if it exists
        if self.trt_model is not None:
            if self.config.convert_fp16:
                # convert input to correct dtype
                x = x.half()
            if self.config.enable_crop_embeddings:
                y_hat = self.trt_model(x, crop_id_idx)
            else:
                y_hat = self.trt_model(x)
            # convert output to correct dtype
            y_hat_unpacked = DeepweedOutputFactory.unpack(y_hat).float()
            y_hat_unpacked = self.model.fix_zero_tensor_shape(y_hat_unpacked, x.shape)
            y_hat = y_hat_unpacked.pack()
        else:
            y_hat = self.model(
                x,
                crop_id_idx,
                gradient_checkpoint=self.config.gradient_checkpoint and torch.is_grad_enabled(),
                preserve_zero_tensor_shape=True,
                sigmoid=False,
            )

        return cast(torch.Tensor, y_hat)

    @duration_perf_recorder_decorator(f"{PerfCategory.TRAINING}")
    def compute_metrics(
        self,
        out: DeepweedOutput,
        out_hat: DeepweedOutput,
        out_label: DatasetLabel,
        batch_enabled_weed_point_classes: torch.Tensor,
        batch_enabled_segm_classes: torch.Tensor,
        batch_enabled_hits: torch.Tensor,
        out_point_confidence: List[torch.Tensor],
        out_point_crop_protection: List[torch.Tensor],
        filepaths: Optional[List[str]],
        loss_multipliers: torch.Tensor,
        segm_class_weights: torch.Tensor,
        image_metadata: List[ImageMetadata],
        compute_dataframes: Optional[bool] = False,
        model_elapsed_time: Optional[float] = None,
        starting_pos: Optional[List[Tuple[float, float]]] = None,
        embeddings: Optional[torch.Tensor] = None,
        capture_embeddings: bool = False,
        reduced_scaled_embeddings: Optional[torch.Tensor] = None,
        save_embeddings_to_db: bool = False,
        evaluation_height: int = 0,
        evaluation_width: int = 0,
        transpose_points: List[bool] = [],
    ) -> Tuple[Metrics, Dict[str, Any]]:
        batch_size = out.batch_size

        with duration_perf_recorder(PerfCategory.TRAINING, "segmentation losses"):
            if segm_class_weights.sum() > 1e-9:
                segm_dice_loss, segm_dice_loss_per_sample = segmentation_loss.segm_dice_loss(
                    out.mask,
                    out_hat.mask,
                    segm_class_weights,
                    batch_enabled_segm_classes.unsqueeze(-1).unsqueeze(-1),
                    out_hat.pre_sigmoid,
                    self.current_epoch,
                    self.overprediction_warmup,
                )
                segm_bce_loss, segm_bce_loss_per_sample = segmentation_loss.segm_bce_loss(
                    out.mask,
                    out_hat.mask,
                    segm_class_weights,
                    batch_enabled_segm_classes.unsqueeze(-1).unsqueeze(-1),
                    out_hat.pre_sigmoid,
                )
            else:
                segm_dice_loss = torch.tensor(0.0, device=out.mask.device)
                segm_dice_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
                segm_bce_loss = torch.tensor(0.0, device=out.mask.device)
                segm_bce_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)

        point_weed_hit_loss = torch.tensor(0.0, device=out.mask.device)
        point_weed_hit_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
        point_crop_hit_loss = torch.tensor(0.0, device=out.mask.device)
        point_crop_hit_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
        point_plant_hit_loss = torch.tensor(0.0, device=out.mask.device)
        point_plant_hit_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
        point_category_loss = torch.tensor(0.0, device=out.mask.device)
        point_category_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
        point_offset_loss = torch.tensor(0.0, device=out.mask.device)
        point_offset_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
        point_size_loss = torch.tensor(0.0, device=out.mask.device)
        point_size_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)
        embedding_loss = torch.tensor(0.0, device=out.mask.device)
        embedding_loss_per_sample = torch.zeros((batch_size,), device=out.mask.device)

        positive_distances = None
        negative_distances = None
        positive_comparison = None
        negative_comparison = None

        with duration_perf_recorder(PerfCategory.TRAINING, "Point weed/crop hit losses"):
            if self.weed_point_class_weights.sum() > 1e-9:
                crop_punishment_weights = [
                    torch.stack(
                        [
                            torch.where(  # All spots where there is a point hit but on doesn't exist in the label
                                torch.logical_and(
                                    y_point_hits_i[HitClass.WEED : HitClass.WEED + 1, :, :]
                                    <= self.config.point_hit_threshold,
                                    y_point_hits_hat_i[HitClass.WEED : HitClass.WEED + 1, :, :]
                                    > (
                                        self._pre_sigmoid_point_hit_threshold
                                        if out_hat.pre_sigmoid
                                        else self.config.point_hit_threshold
                                    ),
                                ),
                                y_point_crop_protection_i,  # Pick from the multipliers, which take into account whether there is a crop/baby crop at that location
                                torch.ones_like(y_point_crop_protection_i),  # ones
                            )
                            # Do this computation separately for every example
                            for (y_point_hits_i, y_point_hits_hat_i, y_point_crop_protection_i) in zip(
                                y_point_hits, y_point_hits_hat, y_point_crop_protection
                            )
                        ]
                    )
                    for (y_point_hits, y_point_hits_hat, y_point_crop_protection) in zip(
                        out.point_hits, out_hat.point_hits, out_point_crop_protection
                    )
                ]

                point_weed_hit_losses = [
                    point_loss.point_hit_loss(*t)
                    for t in zip(
                        out.point_hits,
                        out_hat.point_hits,
                        out_point_confidence,
                        crop_punishment_weights,
                        self.config.point_downsample,
                        loss_multipliers,
                        [batch_enabled_hits.unsqueeze(-1).unsqueeze(-1)] * len(self.config.point_downsample),
                        [HitClass.WEED] * len(self.config.point_downsample),
                        [out_hat.pre_sigmoid] * len(self.config.point_downsample),
                        [self.point_hit_weights] * len(self.config.point_downsample),
                        [self.config.point_weight_constant] * len(self.config.point_downsample),
                        [self.datasets.get_training().crop_classes] * len(self.config.point_downsample),
                        [self.no_crop_background_multiplier] * len(self.config.point_downsample),
                        [self.config.focal_loss] * len(self.config.point_downsample),
                    )
                ]
                point_weed_hit_loss = cast(torch.Tensor, sum(item[0] for item in point_weed_hit_losses))
                point_weed_hit_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_weed_hit_losses))

                point_crop_hit_losses = [
                    point_loss.point_hit_loss(*t)
                    for t in zip(
                        out.point_hits,
                        out_hat.point_hits,
                        out_point_confidence,
                        crop_punishment_weights,
                        self.config.point_downsample,
                        loss_multipliers,
                        [batch_enabled_hits.unsqueeze(-1).unsqueeze(-1)] * len(self.config.point_downsample),
                        [HitClass.CROP] * len(self.config.point_downsample),
                        [out_hat.pre_sigmoid] * len(self.config.point_downsample),
                        [self.point_hit_weights] * len(self.config.point_downsample),
                        [self.config.point_weight_constant] * len(self.config.point_downsample),
                        [self.datasets.get_training().crop_classes] * len(self.config.point_downsample),
                        [self.no_crop_background_multiplier] * len(self.config.point_downsample),
                        [self.config.focal_loss] * len(self.config.point_downsample),
                    )
                ]
                point_crop_hit_loss = cast(torch.Tensor, sum(item[0] for item in point_crop_hit_losses))
                point_crop_hit_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_crop_hit_losses))

                point_plant_hit_losses = [
                    point_loss.point_hit_loss(*t)
                    for t in zip(
                        out.point_hits,
                        out_hat.point_hits,
                        out_point_confidence,
                        crop_punishment_weights,
                        self.config.point_downsample,
                        loss_multipliers,
                        [batch_enabled_hits.unsqueeze(-1).unsqueeze(-1)] * len(self.config.point_downsample),
                        [HitClass.PLANT] * len(self.config.point_downsample),
                        [out_hat.pre_sigmoid] * len(self.config.point_downsample),
                        [self.point_hit_weights] * len(self.config.point_downsample),
                        [self.config.point_weight_constant] * len(self.config.point_downsample),
                        [self.datasets.get_training().crop_classes] * len(self.config.point_downsample),
                        [self.no_crop_background_multiplier] * len(self.config.point_downsample),
                        [self.config.focal_loss] * len(self.config.point_downsample),
                    )
                ]
                point_plant_hit_loss = cast(torch.Tensor, sum(item[0] for item in point_plant_hit_losses))
                point_plant_hit_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_plant_hit_losses))

                point_category_losses = [
                    point_loss.point_category_loss(*t)
                    for t in zip(
                        out.point_hits,
                        out.point_categories,
                        out_hat.point_categories,
                        out_point_confidence,
                        self.config.point_downsample,
                        loss_multipliers,
                        batch_enabled_weed_point_classes.unsqueeze(-1).unsqueeze(-1),
                        [out_hat.pre_sigmoid] * len(self.config.point_downsample),
                        [self.config.point_weight_constant] * len(self.config.point_downsample),
                        [self.config.point_hit_threshold] * len(self.config.point_downsample),
                        [self.weed_point_class_weights] * len(self.config.point_downsample),
                    )
                ]
                point_category_loss = cast(torch.Tensor, sum(item[0] for item in point_category_losses))
                point_category_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_category_losses))

                point_offset_losses = [
                    point_loss.point_offset_loss(*t)
                    for t in zip(
                        out.point_hits,
                        out.point_offsets,
                        out_hat.point_offsets,
                        out_point_confidence,
                        self.config.point_downsample,
                        loss_multipliers,
                        [self.config.point_weight_constant] * len(self.config.point_downsample),
                        [self.config.point_hit_threshold] * len(self.config.point_downsample),
                    )
                ]
                point_offset_loss = cast(torch.Tensor, sum(item[0] for item in point_offset_losses))
                point_offset_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_offset_losses))

                point_size_losses = [
                    point_loss.point_size_loss(*t)
                    for t in zip(
                        out.point_hits,
                        out.point_sizes,
                        out_hat.point_sizes,
                        out_point_confidence,
                        self.config.point_downsample,
                        loss_multipliers,
                        [self.config.point_weight_constant] * len(self.config.point_downsample),
                        [self.config.point_hit_threshold] * len(self.config.point_downsample),
                    )
                ]
                point_size_loss = cast(torch.Tensor, sum(item[0] for item in point_size_losses))
                point_size_loss_per_sample = cast(torch.Tensor, sum(item[1] for item in point_size_losses))

                if embeddings is not None:
                    if self.config.embedding_loss_function == "pairwise":
                        (
                            embedding_loss,
                            embedding_loss_per_sample,
                            positive_distances,
                            negative_distances,
                            positive_comparison,
                            negative_comparison,
                        ) = embeddings_loss(
                            out_label.points,
                            embeddings,
                            base_embedding_difference_on_hit_class=self.config.base_embedding_label_on_hit,
                            max_items_to_compare=self.config.max_comparison_pairs_for_embedding_training,
                            min_for_positive=self.config.embedding_positive_threshold,
                            max_for_negative=self.config.embedding_negative_threshold,
                            margin=self.config.embedding_margin,
                            compare_whole_batch=self.config.apply_embedding_loss_across_batch,
                            use_comparison_similarity_loss=self.config.use_comparison_similarity_loss,
                            use_points_with_overlap=self.config.use_points_with_overlap,
                            without_overlap_and_without_overlap_percentage=self.config.without_overlap_and_without_overlap_percentage,
                            with_overlap_and_with_overlap_percentage=self.config.with_overlap_and_with_overlap_percentage,
                            remove_size_differentiated_pairs=self.config.embedding_loss_size_differentiation_enabled,
                            size_differentiated_threshold=self.config.embedding_loss_size_differentiation_threshold,
                        )
                    elif self.config.embedding_loss_function == "info_nce":
                        embedding_loss, embedding_loss_per_sample, _ = info_nce_loss(
                            out_label.points,
                            embeddings,
                            base_embedding_difference_on_hit_class=self.config.base_embedding_label_on_hit,
                            min_for_positive=self.config.embedding_positive_threshold,
                            max_for_negative=self.config.embedding_negative_threshold,
                            max_items_to_compare=self.config.max_comparison_pairs_for_embedding_training,
                        )
                    elif self.config.embedding_loss_function == "triplet":
                        (
                            embedding_loss,
                            embedding_loss_per_sample,
                            positive_distances,
                            negative_distances,
                        ) = triplet_loss(
                            out_label.points,
                            embeddings,
                            max_items_to_compare=self.config.max_comparison_pairs_for_embedding_training,
                            min_for_positive=self.config.embedding_positive_threshold,
                            max_for_negative=self.config.embedding_negative_threshold,
                            margin=self.config.embedding_margin,
                            random_neighbor=self.config.triplet_loss_random_neighbor,
                            base_embedding_difference_on_hit_class=self.config.base_embedding_label_on_hit,
                        )

                    # Remove embedding labels from out_label.points because they aren't used passed here and take up too much memory
                    for point_list in out_label.points:
                        for po in point_list:
                            del po.comparison_embedding

        out_hat = out_hat.sigmoid()

        # fence label & predictions based on enabled classes for this batch
        with torch.no_grad():
            out = out.fence(batch_enabled_segm_classes)
        out_hat = out_hat.fence(batch_enabled_segm_classes)

        model_is_driptape = bool(segm_class_weights.sum() > 1e-9 and self.weed_point_class_weights.sum() < 1e-9)

        overall_loss_per_sample = (
            self.segm_dice_loss_weight * segm_dice_loss_per_sample
            + self.segm_bce_loss_weight * segm_bce_loss_per_sample
            + self.config.point_weed_hit_loss_weight * point_weed_hit_loss_per_sample
            + self.config.point_crop_hit_loss_weight * point_crop_hit_loss_per_sample
            + self.config.point_plant_hit_loss_weight * point_plant_hit_loss_per_sample
            + self.config.point_category_loss_weight * point_category_loss_per_sample
            + self.config.point_offset_loss_weight * point_offset_loss_per_sample
            + self.config.point_size_loss_weight * point_size_loss_per_sample
            + self.config.embedding_loss_weight * embedding_loss_per_sample
        )

        # Record losses in the dictionary
        with duration_perf_recorder(PerfCategory.TRAINING, "Recording losses / moving to CPU"):
            recording_loss_dict = {
                "overall_loss_per_sample": overall_loss_per_sample.detach().cpu(),
                "segm_dice_loss_per_sample": segm_dice_loss_per_sample.detach().cpu(),
                "weighted_segm_dice_loss_per_sample": self.segm_dice_loss_weight
                * segm_dice_loss_per_sample.detach().cpu(),
                "segm_bce_loss_per_sample": segm_bce_loss_per_sample.detach().cpu(),
                "weighted_segm_bce_loss_per_sample": self.segm_bce_loss_weight
                * segm_bce_loss_per_sample.detach().cpu(),
                "point_weed_hit_loss_per_sample": point_weed_hit_loss_per_sample.detach().cpu(),
                "weighted_point_weed_hit_loss_per_sample": self.config.point_weed_hit_loss_weight
                * point_weed_hit_loss_per_sample.detach().cpu(),
                "point_crop_hit_loss_per_sample": point_crop_hit_loss_per_sample.detach().cpu(),
                "weighted_point_crop_hit_loss_per_sample": self.config.point_crop_hit_loss_weight
                * point_crop_hit_loss_per_sample.detach().cpu(),
                "point_plant_hit_loss_per_sample": point_plant_hit_loss_per_sample.detach().cpu(),
                "weighted_point_plant_hit_loss_per_sample": self.config.point_plant_hit_loss_weight
                * point_plant_hit_loss_per_sample.detach().cpu(),
                "point_category_loss_per_sample": point_category_loss_per_sample.detach().cpu(),
                "weighted_point_category_loss_per_sample": self.config.point_category_loss_weight
                * point_category_loss_per_sample.detach().cpu(),
                "point_offset_loss_per_sample": point_offset_loss_per_sample.detach().cpu(),
                "weighted_point_offset_loss_per_sample": self.config.point_offset_loss_weight
                * point_offset_loss_per_sample.detach().cpu(),
                "point_size_loss_per_sample": point_size_loss_per_sample.detach().cpu(),
                "weighted_point_size_loss_per_sample": self.config.point_size_loss_weight
                * point_size_loss_per_sample.detach().cpu(),
                "embedding_loss_per_sample": embedding_loss_per_sample.detach().cpu(),
                "weighted_embedding_loss_per_sample": self.config.embedding_loss_weight
                * embedding_loss_per_sample.detach().cpu(),
            }

        return (
            Metrics(
                out,
                out_hat,
                out_label,
                segm_dice_loss,
                segm_bce_loss,
                segm_class_weights > 0,
                point_weed_hit_loss,
                point_crop_hit_loss,
                point_plant_hit_loss,
                point_category_loss,
                point_offset_loss,
                point_size_loss,
                embedding_loss,
                self.weed_point_class_weights > 0,
                batch_enabled_hits,
                batch_enabled_weed_point_classes,
                self.datasets.train_ppi,
                self.discard_points_border_px,
                self.datasets.get_training().weed_classes,
                self.datasets.get_training().segm_classes,
                image_metadata,
                self.config,
                use_confidence=self.use_confidence,
                learning_rate=self.optimizer.param_groups[0]["lr"] if hasattr(self, "optimizer") else 0,
                compute_dataframes=compute_dataframes,
                filepaths=filepaths,
                model_elapsed_time=model_elapsed_time,
                starting_pos=starting_pos,
                segm_dice_loss_weight=self.segm_dice_loss_weight,
                segm_bce_loss_weight=self.segm_bce_loss_weight,
                point_weed_hit_loss_weight=self.config.point_weed_hit_loss_weight,
                point_crop_hit_loss_weight=self.config.point_crop_hit_loss_weight,
                point_plant_hit_loss_weight=self.config.point_plant_hit_loss_weight,
                point_category_loss_weight=self.config.point_category_loss_weight,
                point_offset_loss_weight=self.config.point_offset_loss_weight,
                point_size_loss_weight=self.config.point_size_loss_weight,
                embedding_loss_weight=self.config.embedding_loss_weight,
                embeddings=embeddings if capture_embeddings else None,
                reduced_scaled_embeddings=reduced_scaled_embeddings,
                exp_dir=self._exp_dir,
                total_positive_example_distance=positive_distances.sum().unsqueeze(0)
                if positive_distances is not None
                else None,
                total_negative_example_distance=negative_distances.sum().unsqueeze(0)
                if negative_distances is not None
                else None,
                total_positive_example_comparison=positive_comparison.sum().unsqueeze(0)
                if positive_comparison is not None
                else None,
                total_negative_example_comparison=negative_comparison.sum().unsqueeze(0)
                if negative_comparison is not None
                else None,
                total_positive_examples=torch.tensor(torch.numel(positive_distances)).unsqueeze(0)
                if positive_distances is not None
                else None,
                total_negative_examples=torch.tensor(torch.numel(negative_distances)).unsqueeze(0)
                if negative_distances is not None
                else None,
                save_embeddings_to_db=save_embeddings_to_db,
                for_driptape_model=model_is_driptape,
                evaluation_height=evaluation_height,
                evaluation_width=evaluation_width,
                transpose_points=transpose_points,
                embeddings_lookup_tables=self._embeddings_lookup_tables,
                save_embeddings_as_fp16=self.config.save_embeddings_as_fp16,
            ),
            recording_loss_dict,
        )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def on_train_start(self) -> None:
        if self.logger is not None:
            # Log model graph
            self.logger.watch(self.model, log=None)

            # Log config values
            self.logger.experiment.config.update(
                {
                    "image_file_counts": {
                        "train": {
                            "total": self.datasets.get_training().distinct_examples,
                            "new": self.datasets.get_training().distinct_new_examples,
                            "old": self.datasets.get_training().distinct_old_examples,
                        },
                        "validation": {
                            "total": self.datasets.get_validation().distinct_examples,
                            "new": self.datasets.get_validation().distinct_new_examples,
                            "old": self.datasets.get_validation().distinct_old_examples,
                        },
                        "test": {
                            "total": self.datasets.get_test().distinct_examples,
                            "new": self.datasets.get_test().distinct_new_examples,
                            "old": self.datasets.get_test().distinct_old_examples,
                        },
                    },
                    "crops": self.datasets.get_training().crop_classes,
                    "weeds": self.datasets.get_training().weed_classes,
                    "hostname": os.getenv("NODE_NAME", socket.gethostname()),
                    "initial_lr": self.config.lr,
                    "loss_weights": {
                        "point_weed_hit_loss_weight": self.config.point_weed_hit_loss_weight,
                        "point_crop_hit_loss_weight": self.config.point_crop_hit_loss_weight,
                        "point_category_loss_weight": self.config.point_category_loss_weight,
                        "point_offset_loss_weight": self.config.point_offset_loss_weight,
                        "point_size_loss_weight": self.config.point_size_loss_weight,
                        "embedding_loss_weight": self.config.embedding_loss_weight,
                    },
                },
                allow_val_change=True,
            )

            self.logger.experiment.config.update(self._additional_wandb_config, allow_val_change=True)
            self.logger.experiment.config.update(self.config.to_dict(), allow_val_change=True)

    @duration_perf_recorder_decorator(f"{PerfCategory.TRAINING}")
    def get_metrics(
        self,
        x: torch.Tensor,
        y: DatasetLabel,
        batch_enabled_weed_point_classes: torch.Tensor,
        batch_enabled_segm_classes: torch.Tensor,
        batch_enabled_hits: torch.Tensor,
        loss_multipliers: torch.Tensor,
        segm_class_weights: torch.Tensor,
        image_metadata: List[ImageMetadata],
        filepaths: Optional[List[str]] = None,
        compute_dataframes: Optional[bool] = False,
        starting_pos: Optional[List[Tuple[float, float]]] = None,
        capture_embeddings: bool = False,
        save_embeddings_to_db: bool = False,
        evaluation_height: int = 0,
        evaluation_width: int = 0,
        transpose_points: List[bool] = [],
    ) -> Tuple[Metrics, Dict[str, Any], DeepweedOutput, DeepweedOutput, List[torch.Tensor], List[torch.Tensor]]:
        model_start_time_ms = maka_control_timestamp_ms()
        embeddings = None
        crop_ids = []
        for m in image_metadata:
            assert m.crop_id is not None, f"Image {m.filepath} has no crop_id set"
            crop_ids.append(m.crop_id)
        model_output = self.forward(x, crop_ids)
        if capture_embeddings:
            unpacked = DeepweedOutputFactory.unpack(model_output)
            embeddings = unpacked.embedding_output[0]
        model_end_time_ms = maka_control_timestamp_ms()
        model_elapsed_time_ms = model_end_time_ms - model_start_time_ms

        out_hat = DeepweedOutputFactory.unpack(model_output)
        weed_point_enabled_classes = (self.weed_point_class_weights > 0).unsqueeze(0).repeat(x.shape[0], 1).cuda()
        out, out_point_confidence, out_point_crop_protection = DeepweedOutputFactory.from_label(
            y,
            self.datasets.get_training().weed_classes,
            discard_points_border_px=self.discard_points_border_px,
            use_confidence=self.use_confidence,
            confidence_padding=self.confidence_padding,
            use_crop_protection=self.config.use_crop_protection,
            crop_protection_padding=self.config.crop_protection_padding,
            baby_crop_size=self.config.baby_crop_size,
            crop_protection_multiplier=self.config.crop_protection_multiplier,
            baby_crop_protection_multiplier=self.config.baby_crop_protection_multiplier,
            point_downsample=self.config.point_downsample,
        )
        y = y.fence(weed_point_enabled_classes, batch_enabled_hits, self.weed_point_category_to_index)

        use_pumap_head = self.model.use_pumap_head if self.trt_model is None else self.contains_trt_pumap_head

        metrics, recording_loss_dict = self.compute_metrics(
            out,
            out_hat,
            y,
            batch_enabled_weed_point_classes,
            batch_enabled_segm_classes,
            batch_enabled_hits,
            out_point_confidence,
            out_point_crop_protection,
            filepaths,
            loss_multipliers,
            segm_class_weights,
            image_metadata,
            compute_dataframes=compute_dataframes,
            model_elapsed_time=model_elapsed_time_ms,
            starting_pos=starting_pos,
            embeddings=embeddings,
            capture_embeddings=capture_embeddings,
            reduced_scaled_embeddings=out_hat.pumap_output[0] if use_pumap_head else None,
            save_embeddings_to_db=save_embeddings_to_db,
            evaluation_height=evaluation_height,
            evaluation_width=evaluation_width,
            transpose_points=transpose_points,
        )

        out_hat = out_hat.sigmoid()

        return metrics, recording_loss_dict, out, out_hat, out_point_confidence, out_point_crop_protection

    @property
    def should_log_grad_norms(self) -> bool:
        return self.log_gradient_norms and (self.current_epoch >= self.start_logging_gradient_norms_at_epoch)

    @duration_perf_recorder_decorator(f"{PerfCategory.TRAINING}")
    def backward(self, loss: torch.Tensor) -> None:
        if self.should_log_grad_norms:
            self.norms = torch.zeros(len(self.filepaths), device=loss.device)

            def per_example_norms(layer: torch.nn.Module, _: torch.Tensor, B: torch.Tensor) -> None:
                A = self.activations[layer]
                if layer.__class__.__name__ == "Conv2d":
                    weights = conv_2d_backward(
                        A,
                        B,
                        A.shape[1],
                        B.shape[1],
                        cast(Union[int, Tuple[int, ...]], layer.kernel_size),
                        cast(Union[int, Tuple[int, ...]], layer.stride),
                        cast(Union[int, Tuple[int, ...]], layer.dilation),
                        cast(Union[int, Tuple[int, ...]], layer.padding),
                        cast(int, layer.groups),
                    ).to(device=loss.device)
                    norm = torch.sqrt((weights * weights).sum(dim=[1, 2, 3, 4]))

                    self.norms += norm

            with autograd_lib.backward_module_hook(per_example_norms):
                loss.backward()
        else:
            loss.backward()

    @duration_perf_recorder_decorator(f"{PerfCategory.TRAINING}")
    def training_step(self, batch: List[DeepweedDatapoint], batch_nb: int) -> Dict[str, Any]:
        if self.debug_signal.is_set():
            import pdb

            pdb.set_trace()
            self.debug_signal.clear()

        if batch_nb == 0:
            torch.cuda.memory._record_memory_history(max_entries=100000)
            self.model.eval_frozen_layers()

        preprocessed_batch, transforms_batch_list = self.gpu_transforms(batch, dataset_type=DatasetType.TRAIN)
        collated_batch = self.collate_fn(preprocessed_batch)
        (
            x,
            y,
            batch_enabled_weed_point_classes,
            batch_enabled_segm_classes,
            batch_enabled_hits,
            filepaths,
            starting_pos,
            loss_multipliers,
            _,
            image_metadata,
            enabled_embedding_buckets,
            enabled_categories,
        ) = collated_batch
        for file in filepaths:
            self._sampled_image_filepaths[file] += 1
        for meta in image_metadata:
            self._sampled_image_filepaths_embeddings[meta.filepath] = meta.deepweed_embedding_buckets
        for enabled_category in enabled_categories:
            self._sampled_selected_categories[enabled_category] += 1
        for enabled_embedding_bucket in enabled_embedding_buckets:
            self._sampled_selected_embeddings[str(enabled_embedding_bucket)] += 1
        self.filepaths: List[str] = filepaths
        if self.should_log_grad_norms:

            def save_activations(layer: torch.nn.Module, A: torch.Tensor, _: torch.Tensor) -> None:
                self.activations[layer] = A

            with autograd_lib.forward_module_hook(save_activations):
                (
                    metrics,
                    recording_loss_dict,
                    out,
                    out_hat,
                    out_point_confidence,
                    out_crop_protection,
                ) = self.get_metrics(
                    x,
                    y,
                    batch_enabled_weed_point_classes,
                    batch_enabled_segm_classes,
                    batch_enabled_hits,
                    loss_multipliers,
                    self.train_segm_class_weights,
                    image_metadata,
                    filepaths=filepaths,
                    starting_pos=starting_pos,
                    capture_embeddings=self._capture_embeddings,
                )
        else:
            metrics, recording_loss_dict, out, out_hat, out_point_confidence, out_crop_protection = self.get_metrics(
                x,
                y,
                batch_enabled_weed_point_classes,
                batch_enabled_segm_classes,
                batch_enabled_hits,
                loss_multipliers,
                self.train_segm_class_weights,
                image_metadata,
                filepaths=filepaths,
                starting_pos=starting_pos,
                capture_embeddings=self._capture_embeddings,
            )

        if torch.isnan(metrics.loss):
            metrics_dict = metrics.detach().cpu().to_dict()
            losses = {k: v for k, v in metrics_dict.items() if "loss" in k}
            raise RuntimeError(f"NaN loss reached. Crashing to allow resume from latest checkpoint. Losses: {losses}")

        if self.logger is not None:
            for n in range(x.shape[0]):
                if random.random() < self.train_log_image_p:
                    self.log_image(
                        x,
                        out,
                        out_hat,
                        y,
                        batch_enabled_segm_classes,
                        batch_enabled_hits,
                        out_point_confidence,
                        out_crop_protection,
                        "train_",
                        n,
                        segm_class_weights=self.train_segm_class_weights,
                        is_training=True,
                        filename=os.path.basename(filepaths[n]),
                    )

        detached_metrics = metrics.detach().cpu()
        metrics_dict = detached_metrics.to_dict(lr=self.optimizer.param_groups[0]["lr"], epoch=self.current_epoch)

        # PyTorch-Lightning doesn't like "loss" in progress bar since it's already there
        progress_bar_keys = [
            "segm_dice_loss",
            "segm_bce_loss",
            "point_weed_hit_loss",
            "point_crop_hit_loss",
            "point_plant_hit_loss",
            "point_offset_loss",
            "point_size_loss",
            "point_category_loss",
            "embedding_loss",
            "model_elapsed_time",
            "lr",
        ]
        metrics_for_progress_bar = {key: metrics_dict[key] for key in progress_bar_keys}
        metrics_for_progress_bar.update(
            {"positive_sample_percentage": self.datasets.get_training().positive_sample_percentage}
        )

        self.log_dict(metrics_for_progress_bar, logger=False, prog_bar=True, on_epoch=False, on_step=True)

        result = {"loss": metrics.loss}
        self._training_outputs.append(detached_metrics.to_numpy())

        # Add the recording loss to the loss tracker per exmaple.
        length = len(transforms_batch_list)
        if self.train_loss_tracker is not None:
            self.train_loss_tracker.add_examples(
                image_filepath_list=[item.filepath for item in batch],
                metrics=recording_loss_dict,
                epoch_list=[self.current_epoch] * length,
                step_list=[batch_nb] * length,
                transforms=transforms_batch_list,
            )

        return result

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def on_train_epoch_end(self) -> None:
        self.record_memory_snapshot()
        all_metrics = gather_objects(self._training_outputs)
        self._training_outputs.clear()

        all_sampled_filepaths = gather_objects([self._sampled_image_filepaths])
        all_sampled_filepath_embeddings = gather_objects([self._sampled_image_filepaths_embeddings])
        all_sampled_selected_categories = gather_objects([self._sampled_selected_categories])
        all_sampled_selected_embeddings = gather_objects([self._sampled_selected_embeddings])
        if self.train_loss_tracker is not None:
            all_recording_data = gather_objects([self.train_loss_tracker.data])
            self.train_loss_tracker.clear_recording_data()

        if torch.distributed.get_rank() != 0:
            return

        assert self._exp_dir is not None

        combined_sampled_filepaths: Dict[str, int] = defaultdict(int)
        combined_filepath_embeddings: Dict[str, List[int]] = defaultdict(list)
        combined_selected_categories: Dict[str, int] = defaultdict(int)
        combined_selected_embeddings: Dict[Any, int] = defaultdict(int)

        for sampled_filepaths in all_sampled_filepaths:
            for sampled_filepath, sampled_count in sampled_filepaths.items():
                combined_sampled_filepaths[sampled_filepath] += sampled_count
        for sampled_file_embeddings in all_sampled_filepath_embeddings:
            for sampled_file, sampled_filepaths_embeddings in sampled_file_embeddings.items():
                combined_filepath_embeddings[sampled_file] = sampled_filepaths_embeddings
        for sampled_categories in all_sampled_selected_categories:
            for sampled_category, count in sampled_categories.items():
                combined_selected_categories[sampled_category] += count
        for sampled_embeddings in all_sampled_selected_embeddings:
            for sampled_embedding, count in sampled_embeddings.items():
                combined_selected_embeddings[sampled_embedding] += count

        save_sampling_file(
            self._exp_dir,
            combined_sampled_filepaths,
            os.path.basename(os.path.dirname(self.datasets.get_training().filepath)),
            combined_filepath_embeddings,
            combined_selected_categories,
            combined_selected_embeddings,
            {"weed": self.datasets.get_training().weed_classes, "crop": self.datasets.get_training().crop_classes},
        )
        self.log_embedding_bucket_histogram(combined_filepath_embeddings, "train_")
        del all_sampled_filepaths
        del combined_sampled_filepaths
        del combined_filepath_embeddings
        del combined_selected_categories
        del combined_selected_embeddings

        avg_metrics = AverageMetrics(
            [m.to_tensor() for m in all_metrics],
            segm_enabled_classes=self.train_segm_class_weights > 0,
            point_enabled_classes=self.weed_point_class_weights > 0,
            point_enabled_hits=self.point_hit_weights > 0,
            segm_classes=self.datasets.get_training().segm_classes,
            config=self.config,
        )

        metrics_dict = {k: v.item() for k, v in avg_metrics.to_dict(prefix="train_").items()}
        metrics_dict = add_timing_metrics(metrics_dict)

        # Hacky way of adjusting the positive sampling milestones
        if self._positive_sample_milestones is not None and self.current_epoch in self._positive_sample_milestones:
            self.datasets.get_training().set_positive_percentage(
                self.datasets.get_training().positive_sample_percentage * self._positive_sample_gamma
            )

        self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)
        self.record_memory_snapshot()

        if self.train_loss_tracker is not None:
            self.train_loss_tracker.make_ordered_data(all_recording_data=all_recording_data)
            self.train_loss_tracker.save_recording_loss_file(file_dir=os.path.join(self._exp_dir, "train_record"))

        torch.cuda.memory._record_memory_history(enabled=False)

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def log_embedding_bucket_histogram(
        self, all_sampled_filepath_embeddings: Dict[str, List[int]], prefix: str
    ) -> None:
        if self.logger is None:
            return

        filtered_embedding_buckets = [  # type: ignore
            bucket for bucket in flatten(list(all_sampled_filepath_embeddings.values())) if bucket is not None  # type: ignore
        ]
        self.logger.experiment.log({f"{prefix}sampled_embeddings": wandb.Histogram(filtered_embedding_buckets)})

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def on_after_backward(self) -> None:
        # Log gradient histograms to W&B
        if self.logger is not None and isinstance(self.logger, WandbLogger) and self.trainer.global_step % 100 == 0:
            for name, p in self.model.named_parameters():
                if p.grad is None:
                    continue
                hist_counts = torch.histc(p.grad, min=-1.0, max=1.0, bins=40).cpu().numpy()
                hist_bins = np.linspace(-1.0, 1.0, 41)
                np_histogram = (hist_counts, hist_bins)
                self.logger.experiment.log(
                    {f"{name}.grad": wandb.Histogram(np_histogram=np_histogram)}, step=self.logger.experiment.step
                )

        if self.should_log_grad_norms:
            for index in range(len(self.filepaths)):
                filepath = self.filepaths[index]
                if filepath not in self.gradient_norm_logger:
                    self.gradient_norm_logger[filepath] = []
                self.gradient_norm_logger[filepath].append((self.current_epoch, self.norms[index].item()))

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    @torch.no_grad()
    def log_image(
        self,
        x: torch.Tensor,
        out: DeepweedOutput,
        out_hat: DeepweedOutput,
        label: DatasetLabel,
        batch_enabled_segm_classes: torch.Tensor,
        batch_enabled_hits: torch.Tensor,
        out_point_confidence: List[torch.Tensor],
        out_crop_protection: List[torch.Tensor],
        prefix: str,
        n: int,
        is_training: bool,
        filename: str,
        segm_class_weights: torch.Tensor,
        segm_threshold: float = 0.5,
        point_thresholds: Dict["HitClassValue", float] = {},
    ) -> None:
        assert self.logger is not None
        assert isinstance(self.logger, WandbLogger)

        img = x[n]
        if img.shape[0] == 4:
            # RGBD to RGB
            img = img[:3, :, :]
        img = self.datasets.denormalize_image(img)

        if self.weed_point_class_weights.sum() > 0:
            self.log_image_points(
                img,
                out,
                out_hat,
                label,
                batch_enabled_hits,
                out_point_confidence,
                out_crop_protection,
                prefix,
                n,
                filename,
                point_thresholds,
            )

        for index, clz in enumerate(self.datasets.get_training().segm_classes):
            if not batch_enabled_segm_classes[n, index].item():
                # category not labeled for a given image
                continue

            mask_red = None
            mask_green = None
            if segm_class_weights[index] > 0:
                mask_red = self.render_segm_mask(out.mask[n, index], segm_threshold, (img.shape[-2], img.shape[-1]))
                mask_green = self.render_segm_mask(
                    out_hat.mask[n, index], segm_threshold, (img.shape[-2], img.shape[-1])
                )
            else:
                continue

            if (mask_red is None or not mask_red.sum() > 0) and (mask_green is None or not mask_green.sum() > 0):
                # not interesting image
                continue

            masked_img = img.clone()
            if mask_red is not None:
                masked_img[0, :, :] = torch.max(masked_img[0, :, :], mask_red.float())
            if mask_green is not None:
                masked_img[1, :, :] = torch.max(masked_img[1, :, :], mask_green.float())

            filename_overlay = np.zeros((masked_img.shape[1:3]), dtype=np.uint8)
            cv2.putText(filename_overlay, filename, (10, 30), cv2.FONT_HERSHEY_COMPLEX_SMALL, 1, (1,), 2)
            masked_img[1, :, :] = torch.max(masked_img[1, :, :], torch.tensor(filename_overlay).cuda().float())

            self.logger.experiment.log(
                {f"{prefix}mask_{clz}": [wandb.Image(masked_img)]}, step=self.logger.experiment.step,
            )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def log_image_points(
        self,
        img: torch.Tensor,
        out: DeepweedOutput,
        out_hat: DeepweedOutput,
        label: DatasetLabel,
        batch_enabled_hits: torch.Tensor,
        out_point_confidence: List[torch.Tensor],
        out_crop_protection: List[torch.Tensor],
        prefix: str,
        n: int,
        filename: str,
        thresholds: Dict["HitClassValue", float] = {},
    ) -> None:
        assert self.datasets.train_ppi is not None
        beam_radius_px = BEAM_RADIUS_IN * self.datasets.train_ppi
        with duration_perf_recorder(PerfCategory.TRAINING, "Compute points"):
            points_hat_tuple = compute_points(
                [h[n : n + 1] for h in out_hat.point_hits],
                [c[n : n + 1] for c in out_hat.point_categories],
                [o[n : n + 1] for o in out_hat.point_offsets],
                [s[n : n + 1] for s in out_hat.point_sizes],
                [e[n : n + 1] for e in out_hat.pumap_output],
                (label.mask.shape[2], label.mask.shape[3]),
                enable_crop=self.point_hit_weights[HitClass.CROP].item() > 0,
                enable_weed=self.point_hit_weights[HitClass.WEED].item() > 0,
                weed_point_classes=self.datasets.get_training().weed_classes,
                beam_radius_px=beam_radius_px,
                thresholds=thresholds,
            )
        points_hat, points_hat_nms = points_hat_tuple[0][0], points_hat_tuple[1][0]
        for clz in Deepweed.SUPPORTED_HIT_CLASSES:
            if not batch_enabled_hits[n, clz]:
                continue

            assert self.datasets.train_ppi is not None
            beam_radius_px = BEAM_RADIUS_IN * self.datasets.train_ppi

            mask_red_hits = self.render_point_hit_mask(img, [h[n, clz] for h in out.point_hits])
            mask_red_points = self.render_point_mask(img, label.points[n], clz,)
            mask_red = torch.max(mask_red_hits, mask_red_points)
            mask_light_magenta = None
            mask_magenta = None

            if self.use_confidence:
                mask_yellow = self.render_point_supplementary_information_mask(
                    img, [c[n, 0] for c in out_point_confidence]
                )

            if self.config.use_crop_protection:
                mask_light_magenta = self.render_point_supplementary_information_mask(
                    img,
                    [c[n, 0].float() for c in out_crop_protection],
                    comparison_value=self.config.crop_protection_multiplier,
                )
                mask_magenta = self.render_point_supplementary_information_mask(
                    img,
                    [c[n, 0].float() for c in out_crop_protection],
                    comparison_value=self.config.baby_crop_protection_multiplier,
                )

            mask_blue_hits = self.render_point_hit_mask(img, [h[n, clz] for h in out_hat.point_hits])
            mask_blue_points = self.render_point_mask(img, points_hat, clz,)
            mask_blue = torch.max(mask_blue_hits, mask_blue_points)

            mask_green = self.render_point_mask(img, points_hat_nms, clz,)

            if (
                (mask_red is None or not mask_red.sum() > 0)
                and (mask_green is None or not mask_green.sum() > 0)
                and (mask_blue is None or not mask_blue.sum() > 0)
                and (mask_yellow is None or not mask_yellow.sum() > 0)
                and (mask_light_magenta is None or not mask_light_magenta.sum() > 0)
                and (mask_magenta is None or not mask_magenta.sum() > 0)
            ):
                # not interesting image
                continue

            with duration_perf_recorder(PerfCategory.TRAINING, "Coloring and overlay"):
                masked_img = img.clone()
                if mask_yellow is not None:
                    masked_img[1, :, :] = torch.max(masked_img[1, :, :], mask_yellow.float())
                    masked_img[0, :, :] = torch.max(masked_img[0, :, :], mask_yellow.float())
                if mask_light_magenta is not None:
                    masked_img[2, :, :] = torch.max(masked_img[2, :, :], 0.8 * mask_light_magenta.float())
                    masked_img[0, :, :] = torch.max(masked_img[0, :, :], 0.8 * mask_light_magenta.float())
                if mask_magenta is not None:
                    masked_img[2, :, :] = torch.max(masked_img[2, :, :], mask_magenta.float())
                    masked_img[0, :, :] = torch.max(masked_img[0, :, :], mask_magenta.float())
                if mask_red is not None:
                    masked_img[0, :, :] = torch.max(masked_img[0, :, :], mask_red.float())
                if mask_green is not None:
                    masked_img[1, :, :] = torch.max(masked_img[1, :, :], mask_green.float())
                if mask_blue is not None:
                    masked_img[2, :, :] = torch.max(masked_img[2, :, :], mask_blue.float())

                filename_overlay = np.zeros((masked_img.shape[1:3]), dtype=np.uint8)
                cv2.putText(filename_overlay, filename, (10, 30), cv2.FONT_HERSHEY_COMPLEX_SMALL, 1, (1,), 2)
                masked_img[1, :, :] = torch.max(masked_img[1, :, :], torch.tensor(filename_overlay).cuda().float())

            assert self.logger is not None
            assert isinstance(self.logger, WandbLogger)
            self.logger.experiment.log(
                {f"{prefix}mask_{HitClass.Name(clz)}": [wandb.Image(masked_img)]}, step=self.logger.experiment.step,
            )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def alternate_log_image(
        self,
        img: torch.Tensor,
        out: DeepweedOutput,
        out_hat: DeepweedOutput,
        label: DatasetLabel,
        batch_enabled_hits: torch.Tensor,
        out_point_confidence: List[torch.Tensor],
        out_crop_protection: List[torch.Tensor],
        prefix: str,
        n: int,
        filename: str,
        thresholds: Dict["HitClassValue", float] = {},
    ) -> None:
        assert self.datasets.train_ppi is not None
        assert self.logger is not None
        assert isinstance(self.logger, WandbLogger)
        beam_radius_px = BEAM_RADIUS_IN * self.datasets.train_ppi
        points_hat = compute_points(
            [h[n : n + 1] for h in out_hat.point_hits],
            [c[n : n + 1] for c in out_hat.point_categories],
            [o[n : n + 1] for o in out_hat.point_offsets],
            [s[n : n + 1] for s in out_hat.point_sizes],
            [e[n : n + 1] for e in out_hat.pumap_output],
            (label.mask.shape[2], label.mask.shape[3]),
            enable_crop=self.point_hit_weights[HitClass.CROP].item() > 0,
            enable_weed=self.point_hit_weights[HitClass.WEED].item() > 0,
            weed_point_classes=self.datasets.get_training().weed_classes,
            beam_radius_px=beam_radius_px,
            thresholds=thresholds,
        )[1][0]
        points_truth = compute_points(
            [h[n : n + 1] for h in out.point_hits],
            [c[n : n + 1] for c in out.point_categories],
            [o[n : n + 1] for o in out.point_offsets],
            [s[n : n + 1] for s in out.point_sizes],
            [e[n : n + 1] for e in out.pumap_output],
            (label.mask.shape[2], label.mask.shape[3]),
            enable_crop=self.point_hit_weights[HitClass.CROP].item() > 0,
            enable_weed=self.point_hit_weights[HitClass.WEED].item() > 0,
            weed_point_classes=self.datasets.get_training().weed_classes,
            beam_radius_px=beam_radius_px,
            thresholds=thresholds,
        )[1][0]

        for clz in Deepweed.SUPPORTED_HIT_CLASSES:
            if not batch_enabled_hits[n, clz]:
                continue

            annotated_image = img.clone().cpu().numpy().transpose(1, 2, 0)
            annotated_image = annotated_image * 255
            annotated_image = annotated_image.astype(np.uint8)
            annotated_image = cv2.cvtColor(annotated_image, cv2.COLOR_RGB2BGR)
            for point in points_hat:
                if point.hit_clz != clz:
                    continue
                annotated_image = cv2.circle(
                    np.array(annotated_image), (int(point.x), int(point.y)), int(point.r), (0, 0, 255), 2
                )
                temp = np.zeros_like(annotated_image)
                temp = cv2.circle(temp, (int(point.x), int(point.y)), 10, (0, 0, 255), -1)
                annotated_image = cv2.addWeighted(annotated_image, 1, temp, 0.5, 0)

            for point in points_truth:
                if point.hit_clz != clz:
                    continue
                annotated_image = cv2.circle(
                    annotated_image, (int(point.x), int(point.y)), int(point.r), (255, 0, 0), 2
                )
                temp = np.zeros_like(annotated_image)
                temp = cv2.circle(temp, (int(point.x), int(point.y)), 10, (255, 0, 0), -1)
                annotated_image = cv2.addWeighted(annotated_image, 1, temp, 0.5, 0)

            annotated_image = cv2.cvtColor(annotated_image, cv2.COLOR_BGR2RGB)

            width, height = annotated_image.shape[1], annotated_image.shape[0]
            plt.rcParams["figure.figsize"] = [width / 200, height / 200]
            plt.rcParams["figure.dpi"] = 200
            fig, ax = plt.subplots()
            ax.imshow(annotated_image)
            ax.set_title(f"{filename} {HitClass.Name(clz)}", fontsize=12)
            ax.axis("off")
            legend_elements = [
                patches.Patch(facecolor="red", edgecolor="black", label="Predicted"),
                patches.Patch(facecolor="blue", edgecolor="black", label="Truth"),
            ]
            ax.legend(handles=legend_elements, bbox_to_anchor=(0, 0), loc="lower left", ncol=2)

            self.logger.experiment.log(
                {f"{prefix}mask_{HitClass.Name(clz)}_alternate": [wandb.Image(fig)]}, step=self.logger.experiment.step,
            )
            plt.close(fig)

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def render_segm_mask(self, mask: torch.Tensor, segm_threshold: float, input_shape: Tuple[int, int]) -> torch.Tensor:
        return cast(
            torch.Tensor,
            F.interpolate(mask.unsqueeze(0).unsqueeze(0), input_shape, mode="bilinear", align_corners=False)
            .squeeze(0)
            .squeeze(0)
            > segm_threshold,
        )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def render_point_hit_mask(self, img: torch.Tensor, hits: List[torch.Tensor]) -> torch.Tensor:
        mask = torch.zeros_like(img[0])
        for idx, h in enumerate(hits):
            h_rendered = render_hits(h.unsqueeze(0).unsqueeze(0), size=(img.shape[1], img.shape[2]))
            texture = make_texture_tensor(cast(Tuple[int, int], mask.shape), idx).to(mask.device)
            mask = torch.max(mask, ((h_rendered[0, 0] > self.config.point_hit_threshold) & texture).float() * 0.5)
        return mask

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def render_point_supplementary_information_mask(
        self, img: torch.Tensor, mask_tensors: List[torch.Tensor], comparison_value: float = 0
    ) -> torch.Tensor:
        mask = torch.zeros_like(img[0])
        for idx, c in enumerate(mask_tensors):
            c_rendered = render_hits(c.unsqueeze(0).unsqueeze(0), size=(img.shape[1], img.shape[2]))
            texture = make_texture_tensor(cast(Tuple[int, int], mask.shape), idx).to(mask.device)
            mask = torch.max(mask, ((abs(c_rendered[0, 0] - comparison_value) <= 1e-3) & texture).float() * 0.5)
        return mask

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def render_point_mask(self, img: torch.Tensor, points: List[Point], class_id: "HitClassValue",) -> torch.Tensor:
        mask = torch.zeros_like(img[0]).bool()
        for p in points:
            if p.hit_clz != class_id and class_id != HitClass.PLANT:
                continue
            render_marker(mask, int(p.x), int(p.y), int(p.r), RenderMarkerKind.DIAMOND)
        return mask.float()

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def tile_crop_origins(self, height: int, width: int) -> List[Tuple[int, int]]:

        size = (height, width)
        crop_size = (self.config.evaluation_image_height, self.config.evaluation_image_width)
        tile = (self.tile_height, self.tile_width)

        origins: List[Tuple[int, int]] = tile_crop_origins(size=size, crop_size=crop_size, tile=tile)

        return origins

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    @torch.no_grad()
    def gpu_transforms(
        self, batch: List[DeepweedDatapoint], dataset_type: DatasetType
    ) -> Tuple[List[DeepweedDatapoint], List[List[Dict[str, Any]]]]:
        dataset: DATASET_TYPES

        if dataset_type == DatasetType.TRAIN:
            dataset = self.datasets.get_training()
        elif dataset_type == DatasetType.VALIDATION:
            dataset = self.datasets.get_validation()
        elif dataset_type == DatasetType.TEST:
            dataset = self.datasets.get_test()

        transforms_batch_list = []

        for datapoint in batch:
            with duration_perf_recorder(PerfCategory.TRAINING, "moving images to CUDA"):
                image = datapoint.image.cuda()
                datapoint.enabled_hits

            assert datapoint.target_list is not None, "Datapoint is missing target list."

            with duration_perf_recorder(PerfCategory.TRAINING, "Building target list"):
                target_list = [x if x is None else x.cuda() for x in datapoint.target_list]

            with torch.autocast(device_type="cuda", enabled=False):
                image, target, transforms = dataset.preprocess_datapoint(
                    image,
                    target_list,
                    datapoint.points,
                    datapoint.image_meta,
                    set(
                        [x for i, x in enumerate(dataset.weed_classes) if datapoint.enabled_weed_point_classes[i] == 1]
                    ),
                    set([x for i, x in enumerate(Deepweed.SUPPORTED_HIT_CLASSES) if datapoint.enabled_hits[i] == 1]),
                    datapoint.enabled_segm_classes,
                    datapoint.enabled_embedding_bucket,
                    previous_transforms_list=None,
                )
            datapoint.image = image
            datapoint.target = target
            transforms_batch_list.append(transforms)

        return batch, transforms_batch_list

    # flake8: noqa: C901
    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    @torch.no_grad()
    def evaluate_crops(
        self,
        batch: Any,
        dataset_type: DatasetType,
        segm_class_weights: torch.Tensor,
        log_p: float = 1.0,
        split_new_old: bool = False,
        commit_metrics_to_db: bool = False,
        save_embeddings_to_db: bool = False,
    ) -> Tuple[Dict[str, Any], Dict[str, Any], List[List[Dict[str, Any]]], List[str]]:
        if dataset_type == DatasetType.VALIDATION:
            prefix = "val_"
        elif dataset_type == DatasetType.TEST:
            prefix = "test_"

        if self.trt_model is None:
            batch_size = self.config.training_batch_size
        else:
            batch_size = self.config.evaluation_batch_size

        preprocessed_batch, transforms_batch_list = self.gpu_transforms(batch, dataset_type=dataset_type)
        image_filepath_list = [item.filepath for item in batch]
        collated_batch = self.collate_fn(preprocessed_batch)

        (
            x,
            y,
            batch_enabled_weed_point_classes,
            batch_enabled_segm_classes,
            batch_enabled_hits,
            filepaths,
            _,
            loss_multipliers,
            is_new_datapoints,
            image_metadata,
            _,
            _,
        ) = collated_batch

        metrics_list: List[Metrics] = []
        recording_loss_dict_total: Dict[str, torch.Tensor] = {}
        transforms_batch_list_total: List[List[Dict[str, Any]]] = []
        image_filepath_list_total: List[str] = []
        crop_origins = self.tile_crop_origins(height=x.shape[2], width=x.shape[3])
        flip_dims = [[], [2], [3], [2, 3]] if self.eval_four_flips else [[]]
        log_indices = torch.randint(len(crop_origins) * len(flip_dims), (len(x),))
        origins = list(cast(Sequence[Tuple[Tuple[int, int], List[int]]], itertools.product(crop_origins, flip_dims)))

        new_metrics: List[Metrics] = []
        old_metrics: List[Metrics] = []
        for index in range(0, len(origins), batch_size):
            x_batch_list = []
            y_batch_list = []

            batch_size = min(batch_size, len(origins) - index)
            starting_pos = []

            h = self.config.evaluation_image_height
            w = self.config.evaluation_image_width

            transpose_points = transforms_batch_list[0][0].get("is_transpose_align_longer_side", False)
            for batch_index in range(min(batch_size, len(origins) - index)):
                (origin_y, origin_x), flip = origins[index + batch_index]
                x_ = x[..., origin_y : origin_y + h, origin_x : origin_x + w]
                y_ = y[..., origin_y : origin_y + h, origin_x : origin_x + w]

                if flip:
                    x_ = x_.flip(dims=flip)
                    y_ = y_.flip(dims=flip)

                x_batch_list.append(x_)
                y_batch_list.append(y_)
                starting_pos.append((float(origin_x), float(origin_y)))
                transforms_batch_list_total.extend(transforms_batch_list)
                image_filepath_list_total.extend(image_filepath_list)

            batch_enabled_weed_point_classes_ = batch_enabled_weed_point_classes.repeat(batch_size, 1)
            batch_enabled_segm_classes_ = batch_enabled_segm_classes.repeat(batch_size, 1)
            batch_enabled_hits_ = batch_enabled_hits.repeat(batch_size, 1)

            x_ = torch.cat(x_batch_list)
            y_ = DatasetLabel.stack(y_batch_list)
            i_m_ = image_metadata * len(x_batch_list)

            (metrics, recording_loss_dict, out, out_hat, out_point_confidence, out_crop_protection,) = self.get_metrics(
                x_,
                y_,
                batch_enabled_weed_point_classes_,
                batch_enabled_segm_classes_,
                batch_enabled_hits_,
                loss_multipliers,
                segm_class_weights,
                i_m_,
                filepaths=filepaths * batch_size,
                compute_dataframes=commit_metrics_to_db,
                starting_pos=starting_pos,
                capture_embeddings=(
                    self._capture_embeddings
                    and (
                        (self.trt_model is None)
                        or (self.config.save_full_embeddings_from_trt_model and self.trt_model is not None)
                    )
                ),
                save_embeddings_to_db=save_embeddings_to_db,
                evaluation_height=h,
                evaluation_width=w,
                transpose_points=[transpose_points] * batch_size,
            )

            if split_new_old:
                if is_new_datapoints[0]:
                    new_metrics.append(metrics.detach().cpu().remove_dataframe().to_numpy())
                else:
                    old_metrics.append(metrics.detach().cpu().remove_dataframe().to_numpy())

            if self.logger is not None and isinstance(self.logger, WandbLogger):
                for n in range(x_.shape[0]):
                    if log_indices[0] == index + n and random.random() < log_p:
                        self.log_image(
                            x_,
                            out,
                            out_hat,
                            y_,
                            batch_enabled_segm_classes_,
                            batch_enabled_hits_,
                            out_point_confidence,
                            out_crop_protection,
                            prefix,
                            n,
                            segm_class_weights=segm_class_weights,
                            is_training=False,
                            filename=os.path.basename(filepaths[0]),
                            point_thresholds={
                                HitClass.WEED: self.config.point_hit_threshold,
                                HitClass.CROP: self.config.point_hit_threshold,
                                HitClass.PLANT: self.config.point_hit_threshold,
                            },
                        )

            metrics_list.append(metrics.detach().cpu().to_numpy())
            recording_loss_dict_total = (
                recording_loss_dict
                if not recording_loss_dict_total
                else {
                    key: torch.cat((recording_loss_dict_total[key], recording_loss_dict[key]))
                    for key in recording_loss_dict.keys()
                }
            )

        if commit_metrics_to_db:
            assert self._exp_dir is not None
            df = None
            for met in metrics_list:
                if df is None:
                    df = met.get_dataframe()
                else:
                    df = df.append(met.get_dataframe())
            agg_metrics = AggregateMetrics([met.to_tensor() for met in metrics_list]).to_dict(
                filepaths[0]
            )  # Validation and test both have batch size 1, so everything is from the same filepath
            if dataset_type == DatasetType.VALIDATION:
                assert self._validation_sql_dir is not None
                with duration_perf_recorder(f"{PerfCategory.TRAINING}", "val_step_sql_conversion"):
                    self.convert_to_sql_metrics_incrementally(
                        os.path.join(self._sandbox_dir, f"validation_{self.current_epoch}"),
                        df,
                        image_metrics=agg_metrics,
                        tile_height=self.config.evaluation_image_height,
                        tile_width=self.config.evaluation_image_width,
                    )
            elif dataset_type == DatasetType.TEST:
                with duration_perf_recorder(f"{PerfCategory.TRAINING}", "test_step_sql_conversion"):
                    self.convert_to_sql_metrics_incrementally(
                        os.path.join(self._sandbox_dir, f"{self.get_test_prefix()}dataframes"),
                        df,
                        image_metrics=agg_metrics,
                        tile_height=self.config.evaluation_image_height,
                        tile_width=self.config.evaluation_image_width,
                    )

            # Remove embeddings from dataframes to save memory
            for met in metrics_list + new_metrics + old_metrics:
                met.remove_embeddings_from_dataframe()

        obj: Dict[str, Any] = {
            "new_metrics": new_metrics,
            "old_metrics": old_metrics,
        }

        return obj, recording_loss_dict_total, transforms_batch_list_total, image_filepath_list_total

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def validation_step(self, batch: List[DeepweedDatapoint], batch_nb: int) -> None:
        if batch_nb % 5 == 0:
            self.record_memory_snapshot()
        with torch.autocast(device_type="cuda", enabled=False):
            output, recording_loss_dict, transforms_batch_list, image_filepath_list = self.evaluate_crops(
                batch,
                DatasetType.VALIDATION,
                self.train_segm_class_weights,
                self.val_log_image_p,
                split_new_old=True,
                commit_metrics_to_db=self._convert_to_sql_metrics,
                save_embeddings_to_db=False,
            )

        self._validation_outputs.append(output)

        # Add the recording loss to the loss tracker per exmaple.
        length = len(transforms_batch_list)
        if self.validation_loss_tracker is not None:
            self.validation_loss_tracker.add_examples(
                image_filepath_list=image_filepath_list,
                metrics=recording_loss_dict,
                epoch_list=[self.current_epoch] * length,
                step_list=[batch_nb] * length,
                transforms=transforms_batch_list,
            )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def on_validation_epoch_end(self) -> None:
        if self._convert_to_sql_metrics:
            # Move points dbs to shared directory to make accessible to rank 0
            assert self._validation_sql_dir is not None
            shutil.move(
                os.path.join(self._sandbox_dir, f"validation_{self.current_epoch}"),
                os.path.join(self._validation_sql_dir, f"{self.current_epoch}_{torch.distributed.get_rank()}"),
            )
            torch.distributed.barrier()

        self.record_memory_snapshot()
        all_metrics = gather_objects(self._validation_outputs)
        if self.validation_loss_tracker is not None:
            all_recording_data = gather_objects([self.validation_loss_tracker.data])
            self.validation_loss_tracker.clear_recording_data()
        self._validation_outputs.clear()

        if torch.distributed.get_rank() == 0:
            metrics_dict = {}

            new_metrics: List[Metrics] = flatten([met["new_metrics"] for met in all_metrics])
            old_metrics: List[Metrics] = flatten([met["old_metrics"] for met in all_metrics])
            average_metrics = AverageMetrics(
                [m.to_tensor() for m in new_metrics + old_metrics],
                segm_enabled_classes=self.train_segm_class_weights > 0,
                point_enabled_classes=self.weed_point_class_weights > 0,
                point_enabled_hits=self.point_hit_weights > 0,
                segm_classes=self.datasets.get_training().segm_classes,
                config=self.config,
            )
            metrics_dict.update({k: v.item() for k, v in average_metrics.to_dict(prefix="val_").items()})

            assert self._validation_sql_dir is not None
            if self._convert_to_sql_metrics:
                with tempfile.TemporaryDirectory() as tmpdir:
                    target_tmp_file = os.path.join(tmpdir, f"points_{dl_metrics.__version__}.db")

                    for rank in range(torch.distributed.get_world_size()):
                        source_dir = os.path.join(self._validation_sql_dir, f"{self.current_epoch}_{rank}")
                        source_file = os.path.join(source_dir, f"points_{dl_metrics.__version__}.db")
                        source_tmp_file = os.path.join(tmpdir, f"rank{rank}_points_{dl_metrics.__version__}.db")
                        shutil.move(source_file, source_tmp_file)
                        shutil.rmtree(source_dir)

                        dl_metrics.simple_copy_db(source_tmp_file, target_tmp_file)
                        os.remove(source_tmp_file)

                    dl_metrics_dict = self.log_dl_metrics(
                        tmpdir,
                        prefix="val_",
                        new_datapoints=self.datasets.get_validation().new_datapoints,
                        old_datapoints=self.datasets.get_validation().old_datapoints,
                    )
                    metrics_dict.update(dl_metrics_dict)

            if self.new_data_weight is not None and len(new_metrics) > 0 and len(old_metrics) > 0:
                avg_new_metrics = AverageMetrics(
                    [m.to_tensor() for m in new_metrics],
                    segm_enabled_classes=self.train_segm_class_weights > 0,
                    point_enabled_classes=self.weed_point_class_weights > 0,
                    point_enabled_hits=self.point_hit_weights > 0,
                    segm_classes=self.datasets.get_training().segm_classes,
                    config=self.config,
                )
                metrics_dict.update({k: v.item() for k, v in avg_new_metrics.to_dict(prefix="val_new_split_").items()})
                new_point_oec = compute_point_oec(
                    metrics_dict["dl_metrics_val_new_split_weeds_targeted"],
                    metrics_dict["dl_metrics_val_new_split_weeds_targeted"],
                )
                new_oec = compute_oec(new_point_oec, metrics_dict["val_new_split_segmentation_oec"])
                metrics_dict["val_new_split_point_oec"] = new_point_oec
                metrics_dict["val_new_split_oec"] = new_oec

                avg_old_metrics = AverageMetrics(
                    [m.to_tensor() for m in old_metrics],
                    segm_enabled_classes=self.train_segm_class_weights > 0,
                    point_enabled_classes=self.weed_point_class_weights > 0,
                    point_enabled_hits=self.point_hit_weights > 0,
                    segm_classes=self.datasets.get_training().segm_classes,
                    config=self.config,
                )
                metrics_dict.update({k: v.item() for k, v in avg_old_metrics.to_dict(prefix="val_old_split_").items()})
                old_point_oec = compute_point_oec(
                    metrics_dict["dl_metrics_val_old_split_weeds_targeted"],
                    metrics_dict["dl_metrics_val_old_split_weeds_targeted"],
                )
                old_oec = compute_oec(old_point_oec, metrics_dict["val_old_split_segmentation_oec"])
                metrics_dict["val_old_split_point_oec"] = old_point_oec
                metrics_dict["val_old_split_oec"] = old_oec

                metrics_dict["val_oec"] = self.new_data_weight * new_oec + (1.0 - self.new_data_weight) * old_oec
            else:
                # Compute validation OEC
                metrics_dict["val_point_oec"] = compute_point_oec(
                    metrics_dict["dl_metrics_val_weeds_targeted"], metrics_dict["dl_metrics_val_crops_targeted"]
                )
                metrics_dict["val_oec"] = compute_oec(
                    metrics_dict["val_point_oec"], metrics_dict["val_segmentation_oec"]
                )

            metrics_dict = add_timing_metrics(metrics_dict)

            self._sql_metric_id = 0

            self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)

            val_oec_list = [metrics_dict["val_oec"]]
            torch.distributed.broadcast_object_list(val_oec_list)
            if self.validation_loss_tracker is not None:
                self.validation_loss_tracker.make_ordered_data(all_recording_data=all_recording_data)
                assert self._exp_dir is not None
                self.validation_loss_tracker.save_recording_loss_file(
                    file_dir=os.path.join(self._exp_dir, "validation_record")
                )
        else:
            val_oec_list = [0.0]
            torch.distributed.broadcast_object_list(val_oec_list)
            self.log("val_oec", val_oec_list[0])
        self.record_memory_snapshot()

    def set_exp_dir(self, exp_dir: str) -> None:
        self._exp_dir = exp_dir
        self._validation_sql_dir = os.path.join(self._exp_dir, "validation")
        os.makedirs(self._validation_sql_dir, exist_ok=True)

    def set_experiment_url(self, experiment_url: str) -> None:
        self._experiment_url = experiment_url

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def on_train_end(self) -> None:

        if self.should_log_grad_norms and torch.distributed.get_rank() == 0:
            per_epoch_filepath = "gradient_norms.pickle"
            average_filepath = f"gradient_norms_last_{self.current_epoch - self.start_logging_gradient_norms_at_epoch + 1}_average.pickle"
            if self._exp_dir is not None:
                per_epoch_filepath = os.path.join(self._exp_dir, per_epoch_filepath)
                average_filepath = os.path.join(self._exp_dir, average_filepath)
            with open(per_epoch_filepath, "wb") as handle:
                pickle.dump(self.gradient_norm_logger, handle, protocol=pickle.HIGHEST_PROTOCOL)
            average_norms = get_examples_with_highest_last_n_epochs(
                self.start_logging_gradient_norms_at_epoch, self.gradient_norm_logger
            )
            average_keys_to_scores = {key_score[0]: key_score[1] for key_score in average_norms}
            with open(average_filepath, "wb") as handle:
                pickle.dump(average_keys_to_scores, handle, protocol=pickle.HIGHEST_PROTOCOL)

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def load_trt(self, trt_file_path: str) -> None:
        self.trt_model, metadata = load_tensorrt_model(trt_file_path)
        self.is_trt_fp16 = metadata.input_dtype == torch.float16
        self.contains_trt_pumap_head = metadata.contains_pumap_head
        self.trt_pumap_shifter_scaler = metadata.scaler_shifter_parameters
        cast(rt.TRTModule, self.trt_model).set_cache_context(True)

        LOG.info(f"Rank {torch.distributed.get_rank()} loaded TensorRT model.")

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    @torch.no_grad()
    def optimize_for_testing(self, best_checkpoint: Optional[str] = None) -> bool:
        if self.config.make_trt_model and self.trt_model is None:
            torch.cuda.empty_cache()
            if torch.distributed.get_rank() == 0:
                assert self._exp_dir is not None
                trt_converter = TrtConvert(
                    self.datasets.get_calibration(),
                    self.datasets.get_validation(),
                    self.datasets.get_test(),
                    self.export_metadata(),
                    self.export_model(),
                )
                if best_checkpoint is not None:
                    trt_converter.load_model(best_checkpoint, self.config)
                trt_converter.convert(
                    max_batch_size=self.config.evaluation_batch_size,
                    save_to=self.trt_file_path,
                    int8=self.config.convert_int8,
                    fp16=self.config.convert_fp16,
                    calibration_batch_size=16,
                    calibration_cache_input_file=self.int8_calibration,
                    calibration_cache_output_file=os.path.join(self._exp_dir, "trt_int8.calib"),
                    calibration_only=False,
                    error_metrics=True,
                )

            # wait for model to be created
            torch.distributed.barrier()

            # replace model with optimized model
            self.load_trt(self.trt_file_path)
            # wait for model to be loaded
            torch.distributed.barrier()
            return True
        return False

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def test_step(self, batch: List[DeepweedDatapoint], batch_nb: int) -> None:
        if batch_nb == 0:
            # recreate table every time we start test so different test runs (optimized vs unoptimized) don't interfere
            self.create_embedding_lookup_table(
                self.get_tmp_embedding_lookup_table_path(EmbeddingType.REDUCED_SCALED),
                embedding_type=EmbeddingType.REDUCED_SCALED,
            )
            self.create_embedding_lookup_table(
                self.get_tmp_embedding_lookup_table_path(EmbeddingType.FULL), embedding_type=EmbeddingType.FULL
            )
            self.model.set_predict_segmentation(len(self.datasets.get_test().segm_classes) > 0)
        with torch.autocast(device_type="cuda", enabled=False):
            # TODO(asergeev): support for logging test images
            metrics, recording_loss_dict, transforms_batch_list, image_filepath_list = self.evaluate_crops(
                batch,
                DatasetType.TEST,
                self.test_segm_class_weights,
                log_p=0,
                split_new_old=True,
                commit_metrics_to_db=self._convert_to_sql_metrics,
                save_embeddings_to_db=True,
            )

        self._test_outputs.append(metrics)

        # Add the recording loss to the loss tracker per exmaple.
        length = len(transforms_batch_list)
        if self.test_loss_tracker is not None:
            self.test_loss_tracker.add_examples(
                image_filepath_list=image_filepath_list,
                metrics=recording_loss_dict,
                epoch_list=[self.current_epoch] * length,
                step_list=[batch_nb] * length,
                transforms=transforms_batch_list,
            )

    def get_test_prefix(self) -> str:
        prefix = "test_"
        if self.trt_model is None:
            prefix = prefix + "unoptimized_"

        return prefix

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def get_image_from_relocation_information(self, image_s3_path: str) -> torch.Tensor:
        split_s3_path = image_s3_path.split("/", maxsplit=3)
        assert len(split_s3_path) == 4  # Ex: ['s3:', '', 'maka-pono', 'media/test.png']

        s3_cache_proxy_client = S3CacheProxyClient(
            s3_cache_proxy_host=os.getenv("S3_CACHE_PROXY_SERVICE_HOST"), timeout=30
        )

        image = Image.open(io.BytesIO(s3_cache_proxy_client.get(split_s3_path[2], split_s3_path[3]))).convert("RGB")
        return cast(torch.Tensor, TF.to_tensor(image))

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def pad_and_initial_crop(self, image: npt.NDArray[Any], x: float, y: float, radius: float,) -> npt.NDArray[Any]:
        padded_image_og = np.pad(
            image, ((int(radius), int(radius)), (int(radius), int(radius)), (0, 0),), mode="constant",
        )
        padded_x = int(x + radius)
        padded_y = int(y + radius)
        padded_image: npt.NDArray[Any] = padded_image_og[
            int(padded_y - radius) : int(padded_y + radius), int(padded_x - radius) : int(padded_x + radius), :,
        ]

        return padded_image

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def log_embeddings(
        self,
        dataframe_dir: str,
        embedding_lookup_table: EmbeddingLookupTables,
        prefix: str,
        embedding_type: EmbeddingType = EmbeddingType.FULL,
    ) -> None:

        embeddings = []
        cropped_images: List[npt.NDArray[Any]] = []

        points_db_path = os.path.join(dataframe_dir, f"points_{dl_metrics.__version__}.db")
        with dl_metrics.get_session(dl_metrics.get_db(points_db_path)) as sess:
            query_items = [
                dl_metrics.Point.category_class_name.label("category_class_name"),
                dl_metrics.Image.filepath.label("filepath"),
                dl_metrics.Point.x.label("point_x"),
                dl_metrics.Point.y.label("point_y"),
                dl_metrics.Point.uuid.label("point_uuid"),
                dl_metrics.Point.corrected_transpose.label("point_corrected_transpose"),
            ]

            points_query = sess.query(*query_items).join(
                dl_metrics.Image, dl_metrics.Point.image_id == dl_metrics.Image.id
            )

            points_from_db = (
                points_query.filter(dl_metrics.Point.confidence > 0)
                .filter(dl_metrics.Point.type == dl_metrics.PointType.LABEL)
                .filter(dl_metrics.Point.hit_class != dl_metrics.HitClass.PLANT)
                .order_by(func.random())
                .limit(1000)
                .all()
            )

        for item in points_from_db:
            x = item.point_x
            y = item.point_y
            if not item.point_corrected_transpose and "slayer" in item.filepath:
                y = item.point_x
                x = item.point_y

            embedding = embedding_lookup_table.get(item.point_uuid)
            assert embedding is not None, f"point uuid is missing in lookup table {item.point_uuid}"

            embeddings.append(
                ComparisonEmbeddingObject(
                    image_id="fake_image_id",
                    x=x,
                    y=y,
                    radius=0,
                    category=item.category_class_name,
                    epoch="test",
                    embedding=embedding.tolist(),
                    geohash=None,
                    filepath=item.filepath,
                )
            )

        embeddings_df, _, random_indices, number_components = embeddings_to_dataframes(
            embeddings,
            num_samples=50 if self.config.fast_run else 1000,
            pca_components=50 if embedding_type == EmbeddingType.FULL else None,
        )

        for ind in random_indices:
            item = embeddings[ind]
            if item is None:
                cropped_images.append(np.zeros((400, 400, 3)))
                continue
            try:
                image = self.get_image_from_relocation_information(item.filepath)
                cropped_im = self.pad_and_initial_crop(image.moveaxis(0, -1).cpu().numpy(), item.x, item.y, 200)
                cropped_im *= 255
                cropped_im = cropped_im.astype(np.uint8)
                cropped_images.append(cropped_im)
            except Exception as e:
                LOG.warning(f"Exception getting image: {e}")
                cropped_images.append(np.zeros((400, 400, 3)))

        if len(cropped_images) > 0:
            embeddings_df[number_components + 1] = [wandb.Image(cropped_image) for cropped_image in cropped_images]

        self.logger.experiment.log(
            {f"{prefix}.{embedding_type.name.lower()}_embeddings": wandb.Table(dataframe=embeddings_df)}
        )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def save_embedding_files(
        self,
        dataframe_dir: str,
        embedding_lookup_tables: EmbeddingLookupTables,
        embedding_type: EmbeddingType = EmbeddingType.REDUCED_SCALED,
        image_level_pytorch: bool = False,
        hdf5: bool = False,
    ) -> None:
        assert self._exp_dir is not None
        embedding_type = (
            EmbeddingType.FULL if self.config.save_full_embeddings_from_trt_model else EmbeddingType.REDUCED_SCALED
        )
        save_dir = os.path.join(self._exp_dir, "image_point_embeddings", embedding_type.name.lower())
        os.makedirs(save_dir, exist_ok=True)
        hdf5_save_path = os.path.join(self._exp_dir, f"{embedding_type.name.lower()}_embeddings.hdf5")
        points_db_path = os.path.join(dataframe_dir, f"points_{dl_metrics.__version__}.db")

        dataset_url2id = self.datasets.get_test().image_url2id

        if image_level_pytorch:
            self.save_image_level_embeddings(points_db_path, save_dir, dataset_url2id, embedding_lookup_tables)
        if hdf5:
            self.save_hdf5_embeddings(
                points_db_path, hdf5_save_path, dataset_url2id, embedding_lookup_tables,
            )

    def save_image_level_embeddings(
        self,
        points_db_filepath: str,
        save_dir: str,
        dataset_url2id: Dict[str, str],
        model_embedding_lookup_tables: EmbeddingLookupTables,
    ) -> None:
        labeled_points_from_db = dl_metrics.queries.get_labeled_points(points_db_filepath)

        filepath_to_points: Dict[str, List[Tuple[Dict[str, Any], List[Any]]]] = defaultdict(list)

        for item in labeled_points_from_db:
            embedding = model_embedding_lookup_tables.get(item.uuid)
            assert embedding is not None
            if embedding is not None:
                filepath_to_points[dataset_url2id[item.filepath]].append(
                    (
                        {
                            "image_url": item.filepath,
                            "image_id": dataset_url2id[item.filepath],
                            "x": item.point_x,
                            "y": item.point_y,
                            "geohash": item.geohash,
                        },
                        embedding.tolist(),
                    )
                )

        save_image_level_embedding_files_to_torch(filepath_to_points, dir=save_dir)

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def save_hdf5_embeddings(
        self,
        points_db_path: str,
        save_path: str,
        dataset_url2id: Dict[str, str],
        model_embedding_lookup_tables: EmbeddingLookupTables,
    ) -> None:
        start = maka_control_timestamp_ms()
        (
            labeled_points_from_db,
            matched_plant_predictions,
        ) = dl_metrics.queries.get_labels_and_matched_plant_predictions(
            points_db_path, distance_threshold_mm=MAX_DISTANCE_MM, min_score=MIN_PLANT_SCORE,
        )

        if len(labeled_points_from_db) == 0:
            LOG.warning("No compatible embeddings found for saving hdf5")
            return

        # Because hdf5 appends to files, we delete the file if it currently exists. This is to prevent
        # duplicate data from being added to the file if we run this twice with the same exp_dir
        if os.path.exists(save_path):
            os.remove(save_path)
        assert self.config.model_id is not None
        assert self._dataset_id is not None
        emb = model_embedding_lookup_tables.get(labeled_points_from_db[0].uuid)
        assert emb is not None
        embeddings_dataset_metadata = EmbeddingDatasetMetadata(
            model_id=self.config.model_id,
            embedding_size=len(emb),
            dataset_id=self._dataset_id,
            embedding_precision=str(emb.numpy().dtype),
            version_id=str(uuid.uuid4()),
        )
        embeddings_dataset = EmbeddingDataset(dataset_filepath=save_path, metadata=embeddings_dataset_metadata)
        step_size = 1024
        for i in range(0, len(labeled_points_from_db), step_size):
            LOG.info(f"Saving to h5: {i}->{i+step_size} of {len(labeled_points_from_db)}")
            embedding_datapoints = []
            for point in labeled_points_from_db[i : i + step_size]:
                assert point.uuid is not None
                embedding = model_embedding_lookup_tables.get(point.uuid)

                if embedding is None:
                    continue
                predictions = []
                predictions_embeddings = []
                if point.id in matched_plant_predictions:
                    for prediction in matched_plant_predictions[point.id]:
                        assert prediction["uuid"] is not None
                        prediction_embedding = model_embedding_lookup_tables.get(prediction["uuid"])
                        assert prediction_embedding is not None
                        if prediction_embedding is None:
                            continue
                        predictions.append(
                            EmbeddingDatapointPrediction(
                                plant_score=prediction["plant_score"],
                                weed_score=prediction["weed_score"],
                                crop_score=prediction["crop_score"],
                                category_scores=prediction["category_scores"],
                                distance_mm=prediction["distance_mm"],
                            )
                        )
                        predictions_embeddings.append(prediction_embedding)

                embedding_datapoint_metadata = EmbeddingDatapointMetadata(
                    image_id=dataset_url2id[point.filepath],
                    image_url=point.filepath,
                    captured_at=point.timestamp_ms,
                    point_category_id=point.category_class_name.lower(),
                    geohash=point.geohash,
                    image_crop_id=point.crop_id,
                    x=point.point_x,
                    y=point.point_y,
                    radius=point.point_r_mm,
                    point_id=point.uuid,
                )
                embedding_datapoint = EmbeddingDatapoint(
                    embedding=embedding,
                    metadata=embedding_datapoint_metadata,
                    predictions_metadata=predictions,
                    predictions_embeddings=predictions_embeddings,
                )
                embedding_datapoints.append(embedding_datapoint)
            embeddings_dataset.append(embedding_datapoints)
        end = maka_control_timestamp_ms()
        LOG.info(f"Time to save h5 {(end - start) / 1000} seconds")

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def on_test_epoch_end(self) -> None:
        # Move points dbs to shared directory to make accessible to rank 0
        assert self._exp_dir is not None
        shutil.move(
            os.path.join(self._sandbox_dir, f"{self.get_test_prefix()}dataframes"),
            os.path.join(self._exp_dir, f"{self.get_test_prefix()}dataframes_{torch.distributed.get_rank()}"),
        )
        torch.distributed.barrier()

        # Move embedding lookup tables to shared directory to make accessible to rank 0
        os.makedirs(os.path.dirname(self.get_embedding_lookup_table_path(EmbeddingType.REDUCED_SCALED)), exist_ok=True)
        shutil.move(
            self.get_tmp_embedding_lookup_table_path(EmbeddingType.REDUCED_SCALED),
            self.get_embedding_lookup_table_path(EmbeddingType.REDUCED_SCALED),
        )
        os.makedirs(os.path.dirname(self.get_embedding_lookup_table_path(EmbeddingType.FULL)), exist_ok=True)
        shutil.move(
            self.get_tmp_embedding_lookup_table_path(EmbeddingType.FULL),
            self.get_embedding_lookup_table_path(EmbeddingType.FULL),
        )
        torch.distributed.barrier()

        self.record_memory_snapshot()
        LOG.info(f"Rank {torch.distributed.get_rank()} at test gather")
        all_metrics = gather_objects(self._test_outputs)
        if self.test_loss_tracker is not None:
            all_recording_data = gather_objects([self.test_loss_tracker.data])
            self.test_loss_tracker.clear_recording_data()
        self._test_outputs.clear()

        all_full_embedding_lookup_table_paths = gather_objects(
            [self.get_embedding_lookup_table_path(EmbeddingType.FULL)]
        )
        all_reduced_scaled_embedding_lookup_table_paths = gather_objects(
            [self.get_embedding_lookup_table_path(EmbeddingType.REDUCED_SCALED)]
        )

        if torch.distributed.get_rank() != 0:
            return

        assert self._exp_dir is not None
        new_metrics: List[Metrics] = flatten([met["new_metrics"] for met in all_metrics])
        old_metrics: List[Metrics] = flatten([met["old_metrics"] for met in all_metrics])
        del all_metrics

        avg_metrics = AverageMetrics(
            [m.to_tensor() for m in old_metrics + new_metrics],
            segm_enabled_classes=self.test_segm_class_weights > 0,
            point_enabled_classes=self.weed_point_class_weights > 0,
            point_enabled_hits=self.point_hit_weights > 0,
            segm_classes=self.datasets.get_training().segm_classes,
            config=self.config,
        )

        prefix = self.get_test_prefix()
        metrics_dict = {k: v.item() for k, v in avg_metrics.to_dict(prefix=prefix).items()}
        metrics_dict = add_timing_metrics(metrics_dict)
        if self.new_data_weight is not None and len(new_metrics) > 0 and len(old_metrics) > 0:
            avg_new_metrics = AverageMetrics(
                [m.to_tensor() for m in new_metrics],
                segm_enabled_classes=self.test_segm_class_weights > 0,
                point_enabled_classes=self.weed_point_class_weights > 0,
                point_enabled_hits=self.point_hit_weights > 0,
                segm_classes=self.datasets.get_training().segm_classes,
                config=self.config,
            )

            metrics_dict.update({k: v.item() for k, v in avg_new_metrics.to_dict(prefix=f"{prefix}new_split_").items()})
            del new_metrics
            del avg_new_metrics

            avg_old_metrics = AverageMetrics(
                [m.to_tensor() for m in old_metrics],
                segm_enabled_classes=self.test_segm_class_weights > 0,
                point_enabled_classes=self.weed_point_class_weights > 0,
                point_enabled_hits=self.point_hit_weights > 0,
                segm_classes=self.datasets.get_training().segm_classes,
                config=self.config,
            )
            metrics_dict.update({k: v.item() for k, v in avg_old_metrics.to_dict(prefix=f"{prefix}old_split_").items()})
            del old_metrics
            del avg_old_metrics

        avg_metrics.save_dataframes_to_file(
            self.current_epoch, os.path.join(self._exp_dir, f"{prefix}dataframes"), test=True
        )
        del avg_metrics

        with tempfile.TemporaryDirectory() as tmpdir:
            target_dir = os.path.join(self._exp_dir, f"{prefix}dataframes")
            target_file = os.path.join(target_dir, f"points_{dl_metrics.__version__}.db")
            target_tmp_file = os.path.join(tmpdir, f"points_{dl_metrics.__version__}.db")

            for rank in range(torch.distributed.get_world_size()):
                source_dir = os.path.join(self._exp_dir, f"{self.get_test_prefix()}dataframes_{rank}")
                source_file = os.path.join(source_dir, f"points_{dl_metrics.__version__}.db")
                source_tmp_file = os.path.join(tmpdir, f"rank{rank}_points_{dl_metrics.__version__}.db")
                shutil.move(source_file, source_tmp_file)
                shutil.rmtree(source_dir)

                dl_metrics.simple_copy_db(source_tmp_file, target_tmp_file)
                os.remove(source_tmp_file)

            dl_metrics_dict = self.log_dl_metrics(
                tmpdir, prefix, self.datasets.get_test().new_datapoints, self.datasets.get_test().old_datapoints,
            )
            metrics_dict.update(dl_metrics_dict)
            if self.logger is not None and isinstance(self.logger, WandbLogger) and self.trt_model is not None:
                plots = DlMetricsRunner(tmpdir).get_score_distribution()
                self.logger.experiment.log({title: wandb.Image(figure) for title, figure in plots.items()})

            if self.logger is not None and isinstance(self.logger, WandbLogger):
                self.logger.experiment.summary.update(metrics_dict)

            # Save for posterity
            os.makedirs(target_dir, exist_ok=True)
            shutil.move(target_tmp_file, target_file)

        # Compute test OEC
        metrics_dict[f"{prefix}point_oec"] = compute_point_oec(
            metrics_dict[f"dl_metrics_{prefix}weeds_targeted"], metrics_dict[f"dl_metrics_{prefix}crops_targeted"]
        )
        metrics_dict[f"{prefix}oec"] = compute_oec(
            metrics_dict[f"{prefix}point_oec"], segmentation_oec=metrics_dict[f"{prefix}segmentation_oec"]
        )

        if self.logger is not None:
            self.log_dict(metrics_dict, logger=True, prog_bar=False, on_epoch=True, on_step=False)

        embedding_lookup_tables = {}
        for emb_type, rank_filepaths in [
            (EmbeddingType.FULL, all_full_embedding_lookup_table_paths),
            (EmbeddingType.REDUCED_SCALED, all_reduced_scaled_embedding_lookup_table_paths),
        ]:
            model_embedding_lookup_table_path = get_model_embedding_lookup_path(
                self._exp_dir, prefix, embedding_type=emb_type
            )
            os.makedirs(os.path.dirname(model_embedding_lookup_table_path), exist_ok=True)

            embedding_lookup_tables[emb_type] = EmbeddingLookupTables(rank_filepaths)
            embedding_lookup_tables[emb_type].save(model_embedding_lookup_table_path)

        try:
            if self.logger is not None:
                if "unoptimized" in prefix:
                    self.log_embeddings(
                        os.path.join(self._exp_dir, f"{prefix}dataframes"),
                        embedding_lookup_tables[EmbeddingType.FULL],
                        prefix,
                        embedding_type=EmbeddingType.FULL,
                    )
                else:
                    self.log_embeddings(
                        os.path.join(self._exp_dir, f"{prefix}dataframes"),
                        embedding_lookup_tables[EmbeddingType.REDUCED_SCALED],
                        prefix,
                        embedding_type=EmbeddingType.REDUCED_SCALED,
                    )
                    if self.config.save_full_embeddings_from_trt_model:
                        self.log_embeddings(
                            os.path.join(self._exp_dir, f"{prefix}dataframes"),
                            embedding_lookup_tables[EmbeddingType.FULL],
                            prefix,
                            embedding_type=EmbeddingType.FULL,
                        )

        except Exception as e:
            LOG.warning(f"Failed to log embeddings: {e}")

        if self._save_image_point_embeddings or self._save_hdf5_embeddings:
            emb_type = (
                EmbeddingType.FULL if self.config.save_full_embeddings_from_trt_model else EmbeddingType.REDUCED_SCALED
            )
            self.save_embedding_files(
                os.path.join(self._exp_dir, f"{prefix}dataframes"),
                embedding_lookup_tables=embedding_lookup_tables[emb_type],
                image_level_pytorch=self._save_image_point_embeddings,
                embedding_type=EmbeddingType.FULL
                if self.config.save_full_embeddings_from_trt_model
                else EmbeddingType.REDUCED_SCALED,
                hdf5=self._save_hdf5_embeddings,
            )

        if self.logger is not None and isinstance(self.logger, WandbLogger):
            metrics_dict.update(self.logger.experiment.config.as_dict())
        with open(os.path.join(self._exp_dir, f"{prefix}results.json"), "w") as f:
            simplejson.dump(metrics_dict, f, ignore_nan=True)

        del metrics_dict
        self.record_memory_snapshot()

        if self.test_loss_tracker is not None:
            self.test_loss_tracker.make_ordered_data(all_recording_data=all_recording_data)
            self.test_loss_tracker.save_recording_loss_file(file_dir=os.path.join(self._exp_dir, "test_record"))

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def convert_to_sql_metrics_incrementally(
        self,
        dataframe_dir: str,
        dataframe: Optional[pandas.DataFrame] = None,
        image_metrics: Dict[str, List[Dict[str, Any]]] = {},
        tile_height: Optional[int] = None,
        tile_width: Optional[int] = None,
    ) -> None:
        if not os.path.exists(dataframe_dir):
            os.makedirs(dataframe_dir)
        points_db_path = os.path.join(dataframe_dir, f"points_{dl_metrics.__version__}.db")
        for _ in range(10):
            try:
                p_db = dl_metrics.get_db(points_db_path)
                break
            except sqlalchemy.exc.OperationalError:
                time.sleep(1)
                continue
        with dl_metrics.get_session(p_db) as points_db_session:
            self._sql_metric_id = dl_metrics.convert_df(
                dataframe,
                points_db_session,
                rank=torch.distributed.get_rank(),
                initial_id=self._sql_metric_id,
                verbose=False,
                image_metrics=image_metrics,
                tile_height=tile_height,
                tile_width=tile_width,
                corrected_transpose=True,
            )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def log_dl_metrics(
        self, dataframe_dir: str, prefix: str, new_datapoints: List[str], old_datapoints: List[str]
    ) -> Dict[str, float]:
        start = maka_control_timestamp_ms()
        dl_mets_dict = {}
        threshold = self.config.point_hit_threshold

        dl_metrics_runner = DlMetricsRunner(dataframe_dir)
        got_metrics = maka_control_timestamp_ms()

        dl_mets_dict.update(dl_metrics_runner(threshold, threshold, {}, {}, prefix))

        if len(new_datapoints) > 0 and len(old_datapoints) > 0:
            dl_mets_dict.update(
                dl_metrics_runner(
                    threshold, threshold, {"filepaths_to_include": new_datapoints}, {}, f"{prefix}new_split_"
                )
            )
            dl_mets_dict.update(
                dl_metrics_runner(
                    threshold, threshold, {"filepaths_to_include": old_datapoints}, {}, f"{prefix}old_split_"
                )
            )

        dl_mets_dict.update(dl_metrics_runner(threshold, threshold, {}, {"geohash_prefix": 4}, f"{prefix}geo4_"))
        dl_mets_dict.update(
            dl_metrics_runner(
                threshold, threshold, {}, {"embedding_bucket": (10, 10)}, f"{prefix}embedding_10_10_", add_min_max=True
            )
        )
        dl_mets_dict.update(dl_metrics_runner(threshold, threshold, {}, {"date": True}, f"{prefix}date_"))
        dl_mets_dict.update(
            dl_metrics_runner(threshold, threshold, {}, {"category_class_name": True}, f"{prefix}category_class_name_")
        )

        size_bounds = {
            "small": (0.0, 8.0),
            "medium": (8.0, 23.0),
            "large": (23.0, 50.0),
            "xlarge": (50.0, float("inf")),
        }
        for size, bounds in size_bounds.items():
            distance_threshold_mm = None
            if size == "xlarge":
                distance_threshold_mm = 30.0
            size_averaged = dl_metrics_runner(
                threshold,
                threshold,
                {"size": bounds},
                {},
                f"{prefix}{size}_",
                distance_threshold_mm=distance_threshold_mm,
            )
            dl_mets_dict.update(size_averaged)
        end = maka_control_timestamp_ms()
        LOG.info(f"DL Metrics Timing: Logging metrics={end - got_metrics} ms, total={end - start} ms")
        return dl_mets_dict

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def configure_optimizers(
        self,
    ) -> Union[
        torch.optim.Optimizer, Tuple[List[torch.optim.Optimizer], List[torch.optim.lr_scheduler._LRScheduler]],
    ]:
        self.optimizer = torch.optim.SGD(
            self.parameters(),
            lr=self.config.lr * torch.distributed.get_world_size(),
            momentum=self.config.momentum,
            weight_decay=self.config.weight_decay,
            nesterov=self.config.nesterov,
        )

        if len(self.config.lr_milestones) > 0:
            scheduler = torch.optim.lr_scheduler.MultiStepLR(
                self.optimizer, milestones=self.config.lr_milestones, gamma=self.config.lr_gamma
            )
            return [self.optimizer], [cast(torch.optim.lr_scheduler._LRScheduler, scheduler)]

        return self.optimizer

    @staticmethod
    def collate_fn(data: List[DeepweedDatapoint],) -> Any:
        if type(data[0]) is torch.Tensor:
            return data

        if data[0].target is None:
            return data

        targets = []
        for datapoint in data:
            assert datapoint.target is not None, "Datapoint missing targets"
            targets.append(datapoint.target)

        batch = (
            torch.stack([datapoint.image for datapoint in data]).cuda(),
            DatasetLabel.stack(targets),
            torch.stack([datapoint.enabled_weed_point_classes for datapoint in data]).cuda(),
            torch.stack([datapoint.enabled_segm_classes for datapoint in data]).cuda(),
            torch.stack([datapoint.enabled_hits for datapoint in data]).cuda(),
            [datapoint.filepath for datapoint in data],
            [(datapoint.start_x, datapoint.start_y) for datapoint in data],
            torch.stack([torch.ones(1) * datapoint.loss_multiplier for datapoint in data]).cuda(),
            [datapoint.new_datapoint for datapoint in data],
            [datapoint.image_meta for datapoint in data],
            [datapoint.enabled_embedding_bucket for datapoint in data],
            [datapoint.enabled_category for datapoint in data],
        )

        return batch

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def get_worker_init_fn(self) -> Callable[[int], None]:
        num_workers = self.config.num_workers
        return functools.partial(worker_init_fn, num_workers)

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def train_dataloader(self) -> "DataLoader[DeepweedDatapoint]":
        dataset = self.datasets.get_training()

        sampler: Sampler[DeepweedDatapoint] = torch.utils.data.distributed.DistributedSampler(
            dataset, num_replicas=torch.distributed.get_world_size(), rank=torch.distributed.get_rank(), shuffle=True
        )

        return DataLoader(
            dataset,
            sampler=sampler,
            batch_size=self.config.training_batch_size,
            collate_fn=self.collate_fn,
            pin_memory=False,
            drop_last=True,
            num_workers=self.config.num_workers,
            worker_init_fn=self.get_worker_init_fn(),
            persistent_workers=True,
        )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def val_dataloader(self) -> "DataLoader[DeepweedDatapoint]":
        # OPTIONAL
        # can also return a list of val dataloaders
        dataset = self.datasets.get_validation()
        sampler: Sampler[DeepweedDatapoint] = torch.utils.data.distributed.DistributedSampler(
            dataset, num_replicas=torch.distributed.get_world_size(), rank=torch.distributed.get_rank(), drop_last=True
        )
        return DataLoader(
            dataset,
            sampler=sampler,
            collate_fn=self.collate_fn,
            pin_memory=False,
            num_workers=self.config.num_workers,
            worker_init_fn=self.get_worker_init_fn(),
            persistent_workers=True,
        )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def test_dataloader(self) -> "DataLoader[DeepweedDatapoint]":
        # OPTIONAL
        # can also return a list of test dataloaders
        dataset = self.datasets.get_test()
        sampler: Sampler[DeepweedDatapoint] = torch.utils.data.distributed.DistributedSampler(
            dataset, num_replicas=torch.distributed.get_world_size(), rank=torch.distributed.get_rank(), drop_last=True
        )
        return DataLoader(
            dataset,
            sampler=sampler,
            collate_fn=self.collate_fn,
            pin_memory=False,
            num_workers=self.config.num_workers,
            worker_init_fn=self.get_worker_init_fn(),
            persistent_workers=True,
        )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def export_model(self) -> torch.nn.Module:
        return self.model

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def export_torch_script(self) -> torch.jit.ScriptModule:
        model_bn = cast(Deepweed, self.export_model())

        return cast(torch.jit.ScriptModule, torch.jit.script(model_bn))

    @property
    def use_cases(self) -> List[ModelUseCase]:
        return self._use_cases

    @property
    def trt_file_name(self) -> str:
        return get_tensorrt_file_name(self.config.convert_int8)

    @property
    def trt_file_path(self) -> str:
        assert self._exp_dir is not None
        return os.path.join(self._exp_dir, self.trt_file_name)

    def export_metadata(self) -> ModelMetadata:
        number_of_input_layers = 3

        return ModelMetadata(
            input_dtype=torch.float32,
            input_size=(self.config.evaluation_image_height, self.config.evaluation_image_width),
            means=IMAGENET_MEANS[:number_of_input_layers],
            stds=IMAGENET_STDS[:number_of_input_layers],
            experiment_url=self._experiment_url,
            weed_point_classes=self.datasets.get_training().weed_classes,
            crop_point_classes=self.datasets.get_training().crop_classes,
            segm_classes=self.datasets.get_test().segm_classes,
            crop_ids=self._all_crop_ids,
            crop_embeddings=self.config.enable_crop_embeddings,
            use_cases=self.use_cases,
            supports_depth=False,
            ppi=self.datasets.train_ppi,
            tile=(self.tile_height, self.tile_width),
            supports_half=True,
            not_interleaved=True,
            discard_points_border_px=self.discard_points_border_px,
            plant_enabled=HitClass.PLANT in Deepweed.SUPPORTED_HIT_CLASSES,
            trained_embeddings=self.config.train_embeddings,
            contains_pumap_head=self.contains_trt_pumap_head
            if self.trt_model is not None
            else self.model.use_pumap_head,
            backbone_architecture=self.config.backbone_architecture,
            scaler_shifter_parameters=self.trt_pumap_shifter_scaler
            if self.trt_model is not None
            else self.model._pumap_head.scaler_shifter_parameters,
        )


class DeepweedTrainer(Trainer):
    def __init__(self) -> None:
        super().__init__()
        self._datasets: Optional[RemoteVeselkaDatasets] = None
        self._training_parameters: Dict[str, Any] = {}
        self._start_time = int(time.time())
        self._remote_dataset_process: Optional[Process] = None

        torch.backends.cudnn.enabled = True
        torch.backends.cudnn.allow_tf32 = True
        torch.backends.cudnn.deterministic = False
        torch.set_float32_matmul_precision("medium")

    def __exit__(self, exc_type: Any, exc_value: Any, traceback: Any) -> None:
        self.shutdown()

    def __enter__(self) -> "DeepweedTrainer":
        return self

    def shutdown(self) -> None:
        if self._remote_dataset_process:
            self._remote_dataset_process.terminate()
            self._remote_dataset_process = None

    @property
    def datasets(self) -> RemoteVeselkaDatasets:
        assert self._datasets is not None
        return self._datasets

    def trt_convert(self, evaluation_dir: str) -> None:
        exp_dir = self._find_exp_dir(evaluation_dir)
        self.module.set_exp_dir(exp_dir)
        self.module.optimize_for_testing()

    def veselka_dataset(
        self,
        config: DeepweedConfig,
        train_filepath: str,
        validation_filepath: str,
        test_filepath: str,
        num_samples: int,
        camera: Optional[str] = None,
        train_ppi: Optional[int] = None,
        dilate_mask: int = 0,
        segm_classes: Optional[Tuple[str, ...]] = None,
        test_segm_classes: Optional[Tuple[str, ...]] = None,
        weed_classes: Optional[Tuple[str, ...]] = None,
        crop_classes: Optional[Tuple[str, ...]] = None,
        keep_low_confidence: bool = True,
        seed: int = 1,
        goal_percentage_new: Optional[float] = None,
        new_loss_multiplier: float = 1.0,
        positive_sample_percentage: float = 10 / 11,
        recency_split_age: Optional[int] = None,
        recency_split: Optional[int] = None,
        is_new_func: Optional[Callable[[Dict[str, Any]], bool]] = None,
        duplicate_new_data: Optional[float] = None,
        geohash_max_precision: int = 0,
        geohash_min_precision: int = 0,
        use_date_groups: bool = False,
        calibration_dataset_size: int = 1024,
        embedding_balancing_evaluation_path: Optional[str] = None,
        port: int = REMOTE_VESELKA_DATASET_SERVER_PORT,
    ) -> "Trainer":
        if torch.distributed.get_rank() == 0:

            self._remote_dataset_process = Process(
                target=self.init_dataset_server,
                kwargs={
                    "config": config,
                    "train_filepath": train_filepath,
                    "validation_filepath": validation_filepath,
                    "test_filepath": test_filepath,
                    "num_samples": num_samples,
                    "camera": camera,
                    "train_ppi": train_ppi,
                    "dilate_mask": dilate_mask,
                    "segm_classes": segm_classes,
                    "test_segm_classes": test_segm_classes,
                    "weed_classes": weed_classes,
                    "crop_classes": crop_classes,
                    "keep_low_confidence": keep_low_confidence,
                    "seed": seed,
                    "goal_percentage_new": goal_percentage_new,
                    "new_loss_multiplier": new_loss_multiplier,
                    "positive_sample_percentage": positive_sample_percentage,
                    "recency_split_age": recency_split_age,
                    "recency_split": recency_split,
                    "is_new_func": is_new_func,
                    "duplicate_new_data": duplicate_new_data,
                    "geohash_max_precision": geohash_max_precision,
                    "geohash_min_precision": geohash_min_precision,
                    "use_date_groups": use_date_groups,
                    "calibration_dataset_size": calibration_dataset_size,
                    "embedding_balancing_evaluation_path": embedding_balancing_evaluation_path,
                    "port": port,
                },
            )
            self._remote_dataset_process.start()

            self._remote_dataset_process_thread = Thread(target=self.monitor_process)
            self._remote_dataset_process_thread.start()

        torch.distributed.barrier()

        self._datasets = RemoteVeselkaDatasets(config, port=port)

        return self

    def monitor_process(self) -> None:
        if not self._remote_dataset_process:
            return
        remote_dataset_process = self._remote_dataset_process
        remote_dataset_process.join()
        if remote_dataset_process.exitcode is not None and abs(remote_dataset_process.exitcode) != 15:
            interrupt_main()

    def init_dataset_server(
        self,
        config: DeepweedConfig,
        train_filepath: str,
        validation_filepath: str,
        test_filepath: str,
        num_samples: int,
        camera: Optional[str] = None,
        train_ppi: Optional[int] = None,
        dilate_mask: int = 0,
        segm_classes: Optional[Tuple[str, ...]] = None,
        test_segm_classes: Optional[Tuple[str, ...]] = None,
        weed_classes: Optional[Tuple[str, ...]] = None,
        crop_classes: Optional[Tuple[str, ...]] = None,
        keep_low_confidence: bool = True,
        seed: int = 1,
        goal_percentage_new: Optional[float] = None,
        new_loss_multiplier: float = 1.0,
        positive_sample_percentage: float = 10 / 11,
        recency_split_age: Optional[int] = None,
        recency_split: Optional[int] = None,
        is_new_func: Optional[Callable[[Dict[str, Any]], bool]] = None,
        duplicate_new_data: Optional[float] = None,
        geohash_max_precision: int = 0,
        geohash_min_precision: int = 0,
        use_date_groups: bool = False,
        calibration_dataset_size: int = 1024,
        embedding_balancing_evaluation_path: Optional[str] = None,
        port: int = REMOTE_VESELKA_DATASET_SERVER_PORT,
    ) -> None:
        train_dataset, validation_dataset, test_dataset, calibration_dataset = initialize_datasets(
            config_dict=config.to_json(),
            train_filepath=train_filepath,
            validation_filepath=validation_filepath,
            test_filepath=test_filepath,
            num_samples=num_samples,
            camera=camera,
            train_ppi=train_ppi,
            dilate_mask=dilate_mask,
            segm_classes=segm_classes,
            test_segm_classes=test_segm_classes,
            weed_classes=weed_classes,
            crop_classes=crop_classes,
            keep_low_confidence=keep_low_confidence,
            seed=seed,
            goal_percentage_new=goal_percentage_new,
            new_loss_multiplier=new_loss_multiplier,
            positive_sample_percentage=positive_sample_percentage,
            recency_split_age=recency_split_age,
            recency_split=recency_split,
            is_new_func=is_new_func,
            duplicate_new_data=duplicate_new_data,
            geohash_max_precision=geohash_max_precision,
            geohash_min_precision=geohash_min_precision,
            use_date_groups=use_date_groups,
            calibration_dataset_size=calibration_dataset_size,
            embedding_balancing_evaluation_path=embedding_balancing_evaluation_path,
        )

        remote_dataset = RemoteVeselkaDatasetServer(
            {
                DatasetType.TRAIN: train_dataset,
                DatasetType.VALIDATION: validation_dataset,
                DatasetType.TEST: test_dataset,
                DatasetType.CALIBRATION: calibration_dataset,
            }
        )
        remote_dataset.run(port=port)

    def get_followup_logging_fn(
        self,
        config: DeepweedConfig,
        parent_model_id: Optional[str] = None,
        sub_type: Optional[str] = None,
        last_nth_timestamps: Optional[Dict[int, DatapointTimestamps]] = None,
    ) -> Callable[[Any, str], Any]:
        def followup_logging_fn(logger: Any, exp_dir: str) -> None:
            now = maka_control_timestamp_ms()

            if last_nth_timestamps is not None:
                assert last_nth_timestamps is not None
                diff: Dict[str, Dict[str, float]] = {
                    "image_to_model_post_ms": {},
                    "image_to_model_post_h": {},
                    "label_to_model_post_ms": {},
                    "label_to_model_post_h": {},
                }
                for key, value in last_nth_timestamps.items():
                    if value.image_captured_timestamp_ms is not None:
                        assert value.image_captured_timestamp_ms is not None
                        diff["image_to_model_post_ms"][f"last_{key}th_image"] = now - value.image_captured_timestamp_ms
                        diff["image_to_model_post_h"][f"last_{key}th_image"] = (
                            (now - value.image_captured_timestamp_ms) / 1000 / 60 / 60
                        )

                    if value.label_updated_timestamp_ms is not None:
                        assert value.label_updated_timestamp_ms is not None
                        diff["label_to_model_post_ms"][f"last_{key}th_image"] = now - value.label_updated_timestamp_ms
                        diff["label_to_model_post_h"][f"last_{key}th_image"] = (
                            (now - value.label_updated_timestamp_ms) / 1000 / 60 / 60
                        )
                logger.experiment.log(diff)

            if (
                sub_type
                in [DeepweedTrainingSubtype.FINE_TUNE.name.lower(), DeepweedTrainingSubtype.GEO_FINE_TUNE.name.lower()]
                and not config.fast_run
                and parent_model_id is not None
            ):
                self._compute_maintained(logger, parent_model_id, exp_dir)

            recency_split_age = None
            if self._datasets is not None and self._datasets.get_training().recency_split_age is not None:
                recency_split_age = self._datasets.get_training().recency_split_age
                assert recency_split_age is not None
                recency_split_age = recency_split_age // (1000 * 60 * 60)

                if self._datasets is not None:
                    logger.experiment.config.update(
                        {
                            "recency_split_age_h": recency_split_age,
                            "recency_split": self._datasets.get_training().recency_split,
                        }
                    )

        return followup_logging_fn

    def initialize_module(
        self,
        config: DeepweedConfig,
        overprediction_warmup: int = 0,
        train_log_image_p: float = 0.05,
        val_log_image_p: float = 0.05,
        pretrained_model: Optional[str] = None,
        pretrained_segmentation_model: Optional[str] = None,
        tile_height: Optional[int] = None,
        tile_width: Optional[int] = None,
        eval_four_flips: bool = False,
        ci_run: bool = False,
        log_gradient_norms: bool = False,
        use_confidence: bool = True,
        confidence_padding: int = 0,
        int8_calibration: Optional[str] = None,
        additional_wandb_config: Dict[str, Any] = {},
        model: Optional[Deepweed] = None,
        frozen_backbone_point_layers: Optional[List[str]] = None,
        positive_sample_milestones: Optional[List[int]] = None,
        positive_sample_gamma: float = 1.0,
        start_logging_gradient_norms_at_epoch: int = 0,
        convert_to_sql_metrics: bool = True,
        segm_dice_loss_weight: float = 1.0,
        segm_bce_loss_weight: float = 5.0,
        new_data_weight: Optional[float] = None,
        save_image_point_embeddings: bool = False,
        save_hdf5_embeddings: bool = False,
        dataset_id: Optional[str] = None,
    ) -> None:
        assert self._datasets is not None
        # Create trainer
        self.module = DeepweedTrainingModule(
            self._datasets,
            use_cases=safe_split(("PREDICT",)),
            config=config,
            overprediction_warmup=overprediction_warmup,
            train_log_image_p=train_log_image_p,
            val_log_image_p=val_log_image_p,
            tile_height=tile_height,
            tile_width=tile_width,
            eval_four_flips=eval_four_flips,
            log_gradient_norms=log_gradient_norms,
            start_logging_gradient_norms_at_epoch=start_logging_gradient_norms_at_epoch,
            use_confidence=use_confidence,
            confidence_padding=confidence_padding,
            additional_wandb_config=additional_wandb_config,
            pretrained_model=pretrained_model,
            pretrained_segmentation_model=pretrained_segmentation_model,
            model=model,
            int8_calibration=int8_calibration,
            frozen_backbone_point_layers=frozen_backbone_point_layers,
            positive_sample_milestones=positive_sample_milestones,
            positive_sample_gamma=positive_sample_gamma,
            ci_run=ci_run,
            convert_to_sql_metrics=convert_to_sql_metrics,
            segm_dice_loss_weight=segm_dice_loss_weight,
            segm_bce_loss_weight=segm_bce_loss_weight,
            new_data_weight=new_data_weight,
            save_image_point_embeddings=save_image_point_embeddings,
            save_hdf5_embeddings=save_hdf5_embeddings,
            dataset_id=dataset_id,
        )

    def infer(
        self,
        config: DeepweedConfig,
        ci_run: bool,
        evaluation_dir: str,
        trt_path: Optional[str] = None,
        tile_height: int = 1200,
        tile_width: int = 1200,
        convert_to_sql_metrics: bool = False,
        save_image_point_embeddings: bool = False,
        save_hdf5_embeddings: bool = False,
        dataset_id: Optional[str] = None,
    ) -> None:
        assert self._datasets is not None
        self.initialize_module(
            config=config,
            tile_height=tile_height,
            tile_width=tile_width,
            ci_run=ci_run,
            convert_to_sql_metrics=convert_to_sql_metrics,
            save_image_point_embeddings=save_image_point_embeddings,
            save_hdf5_embeddings=save_hdf5_embeddings,
            dataset_id=dataset_id,
        )

        super()._infer(
            self.module, trt_path=trt_path, evaluation_dir=evaluation_dir, fast_run=config.fast_run,
        )

    def train(  # noqa
        self,
        config: DeepweedConfig,
        overprediction_warmup: int = 0,
        train_log_image_p: float = 0.05,
        val_log_image_p: float = 0.05,
        resume_from: Optional[str] = None,
        pretrained_model: Optional[str] = None,
        pretrained_segmentation_model: Optional[str] = None,
        # If not enough validation data is available, use tiling window smaller than training window
        # and evaluate on four flips
        tile_height: int = 1200,
        tile_width: int = 1200,
        eval_four_flips: bool = False,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        log_experiment: bool = True,
        ci_run: bool = False,
        checkpoint_dir: Optional[str] = None,
        log_gradient_norms: bool = False,
        start_logging_gradient_norms_at: float = 0.0,
        checkpoint_start_pct: int = 50,
        use_confidence: bool = True,
        confidence_padding: int = 0,
        int8_calibration: Optional[str] = None,
        customer: Optional[str] = None,
        robot_name: Optional[str] = None,
        environment: Environment = Environment.DEVELOPMENT,
        deploy: bool = False,
        additional_wandb_config: Dict[str, Any] = {},
        model: Optional[Deepweed] = None,
        frozen_backbone_point_layers: Optional[List[str]] = None,
        positive_sample_milestones: Optional[List[int]] = None,
        positive_sample_gamma: float = 1.0,
        dataset_id: Optional[str] = None,
        sub_type: Optional[str] = None,
        last_nth_timestamps: Optional[Dict[int, DatapointTimestamps]] = None,
        pipeline_id: Optional[str] = None,
        crop_ids: Optional[List[str]] = None,
        parent_model_id: Optional[str] = None,
        segm_dice_loss_weight: float = 1.0,
        segm_bce_loss_weight: float = 5.0,
        new_data_weight: Optional[float] = None,
        check_val_every_n_epoch: int = 1,
        geohash_prefix: Optional[str] = None,
        family_id: Optional[str] = None,
    ) -> None:
        assert (
            self._datasets is not None
        ), "Dataset is None. Please initialize dataset (.veselka_dataset(...)) before training"

        if family_id is None:
            family_id = str(uuid.uuid4())

        training_parameters = self.update_training_parameters(locals())
        additional_wandb_config.update({"parameters": training_parameters})

        start_logging_gradient_norms_at_epoch = int(start_logging_gradient_norms_at * config.num_epochs)

        self.sub_type = sub_type

        tags = self.form_tags(tags, environment)

        download_resume_model(resume_from=resume_from)

        # Create trainer
        self.initialize_module(
            config=config,
            overprediction_warmup=overprediction_warmup,
            train_log_image_p=train_log_image_p,
            val_log_image_p=val_log_image_p,
            tile_height=tile_height,
            tile_width=tile_width,
            eval_four_flips=eval_four_flips,
            log_gradient_norms=log_gradient_norms,
            start_logging_gradient_norms_at_epoch=start_logging_gradient_norms_at_epoch,
            use_confidence=use_confidence,
            confidence_padding=confidence_padding,
            additional_wandb_config=additional_wandb_config,
            pretrained_model=pretrained_model,
            pretrained_segmentation_model=pretrained_segmentation_model,
            model=model,
            int8_calibration=int8_calibration,
            frozen_backbone_point_layers=frozen_backbone_point_layers,
            positive_sample_milestones=positive_sample_milestones,
            positive_sample_gamma=positive_sample_gamma,
            ci_run=ci_run,
            segm_dice_loss_weight=segm_dice_loss_weight,
            segm_bce_loss_weight=segm_bce_loss_weight,
            new_data_weight=new_data_weight,
            dataset_id=dataset_id,
        )

        crop = self.select_crop()

        self.experiment_directory = super()._train(
            self.module,
            ci_run=config.ci_run,
            fast_run=config.fast_run,
            epochs=config.num_epochs,
            checkpoint_start_pct=checkpoint_start_pct,
            resume_from=resume_from,
            description=description,
            tags=tuple(tags),
            log_experiment=log_experiment,
            checkpoint_dir=checkpoint_dir,
            version=get_version(),
            customer=customer,
            robot_name=robot_name,
            environment=environment,
            deploy=deploy,
            crop=crop,
            sub_type=self.sub_type,
            pipeline_id=pipeline_id,
            crop_ids=crop_ids,
            parent_model_id=parent_model_id,
            dataset_id=dataset_id,
            check_val_every_n_epoch=check_val_every_n_epoch,
            followup_logging_fn=self.get_followup_logging_fn(
                sub_type=sub_type,
                config=config,
                parent_model_id=parent_model_id,
                last_nth_timestamps=last_nth_timestamps,
            ),
            precision=config.precision,
            sync_batchnorm=True,
            comparison_model_id=config.comparison_model_id,
        )

        if torch.distributed.get_rank() == 0 and int8_calibration is not None and os.path.exists(int8_calibration):
            assert self.experiment_directory is not None
            shutil.copy(int8_calibration, os.path.join(self.experiment_directory, os.path.basename(int8_calibration)))

        if config.make_trt_model and torch.distributed.get_rank() == 0 and not self._ci_run and log_experiment:
            assert self.experiment_directory is not None
            model_id = self.model_id
            wandb_json = get_wandb_metadata_json_str(self.experiment_directory)

            with open(os.path.join(self.experiment_directory, "test_results.json"), "r") as f:
                test_results = json.load(f)
            with open(os.path.join(self.experiment_directory, "test_unoptimized_results.json"), "r") as f:
                test_results.update(json.load(f))
            self._veselka_post_model(
                model_id=model_id,
                url=f"s3://{S3_BUCKET}/models/{model_id}/{self.module.trt_file_name}",
                crop=crop,
                checksum=compute_md5sum(self.module.trt_file_path),
                trained_at=self._start_time,
                metadata_json=self.module.export_metadata().dump(),
                test_results_json=json.dumps(test_results),
                wandb_json=wandb_json,
                is_good_to_deploy=True,
                exp_dir=self.experiment_directory,
                weed_point_threshold=config.point_hit_threshold,
                crop_point_threshold=config.point_hit_threshold,
                is_pretraining=sub_type == DeepweedTrainingSubtype.PRETRAIN.name.lower(),
                dataset_id=dataset_id,
                sub_type=self.sub_type,
                crop_ids=crop_ids,
                parent_model_id=parent_model_id,
                recency_split=self._datasets.get_training().recency_split,
                geohash_prefix=geohash_prefix,
                family_id=family_id,
                dl_config=config.to_dict(),
            )

    @property
    def model_id(self) -> str:
        assert self.experiment_directory is not None
        model_id = self.experiment_directory.rsplit("/", maxsplit=1)[-1]
        return model_id

    def _compute_maintained(self, logger: Any, parent_model_id: str, exp_dir: str) -> None:
        assert self._datasets is not None
        new_datapoints = self._datasets.get_test().new_datapoints
        old_datapoints = self._datasets.get_test().old_datapoints

        parent_directory = f"{os.getenv('MAKA_DATA_DIR')}/deeplearning/models/{parent_model_id}"
        eval_ids = get_reverse_sorted_evaluation_ids_by_model_id(parent_model_id)
        LOG.info(f"Number of evaluations: {len(eval_ids)}")
        if len(eval_ids) > 0:
            eval_id = eval_ids[0]
            download_records(eval_id, "evaluations", include_points_db=True)
            parent_directory = f"{os.getenv('MAKA_DATA_DIR')}/deeplearning/evaluations/{eval_id}"

        parent_points_db_file = os.path.join(parent_directory, "test_dataframes/points_v2.db",)
        if not os.path.exists(parent_points_db_file):
            LOG.warning("Parent file does not exist. Maintenance cannot be computed")
            return

        with dl_metrics.get_session(
            dl_metrics.get_db(os.path.join(exp_dir, "test_dataframes/points_v2.db"))
        ) as experiment_session, dl_metrics.get_session(dl_metrics.get_db(parent_points_db_file)) as parent_session:
            # At this point, new_overlapping_images will be common images that are recent
            new_overlapping_images = dl_metrics.get_overlapping_filepaths(parent_session, new_datapoints)
            old_overlapping_images = dl_metrics.get_overlapping_filepaths(parent_session, old_datapoints)
            weeds_shot_current_model_new_common_data: Optional[float] = None
            weeds_shot_previous_model_new_common_data: Optional[float] = None

            if len(new_overlapping_images) > MIN_OVERLAPPING_NEW:
                (_, weeds_shot_current_model_new_common_data,) = get_crops_and_weeds_shot(
                    experiment_session,
                    data_to_include=new_overlapping_images,  # current model's performance on common recent images
                )
                (_, weeds_shot_previous_model_new_common_data,) = get_crops_and_weeds_shot(
                    parent_session,
                    data_to_include=new_overlapping_images,  # Parent model's performance on common recent images
                )

            (
                crops_shot_previous_model_old_common_data,
                weeds_shot_previous_model_old_common_data,
            ) = get_crops_and_weeds_shot(
                parent_session,
                data_to_exclude=old_overlapping_images,  # Parent model's performance on all non-recent data but really I should be checking
            )

            (_, weeds_shot_current_model_old_common_data,) = get_crops_and_weeds_shot(
                experiment_session,
                data_to_exclude=old_overlapping_images,  # Parent model's performance on all non-recent data but really I should be checking
            )

            if (
                crops_shot_previous_model_old_common_data is not None
                and weeds_shot_previous_model_old_common_data is not None
            ):
                maintained_performance = comparison_deployment_check(
                    weeds_shot_previous_model_old_common_data, weeds_shot_current_model_old_common_data,
                )

                maintenance_dict: Dict[str, Dict[str, Any]] = {
                    "maintenance": {
                        "number_new_overlapping_images": len(new_overlapping_images),
                        "number_old_overlapping_images": len(old_overlapping_images),
                    },
                }

                compensated_performance = True

                if (
                    weeds_shot_previous_model_new_common_data is not None
                    and weeds_shot_current_model_new_common_data is not None
                ):
                    (weeds_shot_old_data_delta, weeds_shot_new_data_delta,) = old_and_new_data_delta(
                        weeds_shot_parent_model_old_data=weeds_shot_previous_model_old_common_data,
                        weeds_shot_parent_model_new_data=weeds_shot_previous_model_new_common_data,
                        weeds_shot_current_model_old_data=weeds_shot_current_model_old_common_data,
                        weeds_shot_current_model_new_data=weeds_shot_current_model_new_common_data,
                    )

                    compensated_performance = compensation_check(weeds_shot_new_data_delta, weeds_shot_old_data_delta)

                    maintenance_dict["maintenance"]["weeds_shot_old_data_delta"] = weeds_shot_old_data_delta
                    maintenance_dict["maintenance"]["weeds_shot_new_data_delta"] = weeds_shot_new_data_delta

                if logger is not None:
                    logger.experiment.log(maintenance_dict)

                maintenance_dict["maintenance"]["maintained_performance"] = maintained_performance
                maintenance_dict["maintenance"]["compensated_weeds_shot"] = compensated_performance

                if logger is not None and maintained_performance is not None:
                    logger.experiment.config.update(maintenance_dict)

            else:
                LOG.info(
                    f"Could not get metrics for parent model {parent_model_id}, so could not check if model maintained performance."
                )

    def update_training_parameters(self, locals: Dict[str, Any]) -> Dict[str, Any]:
        self._training_parameters.update({"train": {k: v for k, v in locals.items() if k != "additional_wandb_config"}})
        return self._training_parameters

    def form_tags(
        self, tags: Optional[List[str]] = None, environment: Environment = Environment.DEVELOPMENT
    ) -> List[str]:
        if not tags:
            tags = []
        tags.append(environment.name.lower())

        if torch.distributed.get_rank() == 0:
            LOG.info(f"tags={tags}")
        return tags

    def select_crop(self) -> Optional[str]:
        assert self._datasets is not None
        crop: Optional[str] = None
        if len(self._datasets.get_training().crop_classes) == 1:
            crop = self._datasets.get_training().crop_classes[0]

        return crop

    def train_and_unoptimized_test(  # noqa
        self,
        config: DeepweedConfig,
        datasets: RemoteVeselkaDatasets,
        overprediction_warmup: int = 0,
        train_log_image_p: float = 0.05,
        val_log_image_p: float = 0.05,
        resume_from: Optional[str] = None,
        pretrained_model: Optional[str] = None,
        pretrained_segmentation_model: Optional[str] = None,
        # If not enough validation data is available, use tiling window smaller than training window
        # and evaluate on four flips
        tile_height: int = 1200,
        tile_width: int = 1200,
        eval_four_flips: bool = False,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        log_experiment: bool = True,
        ci_run: bool = False,
        checkpoint_dir: Optional[str] = None,
        log_gradient_norms: bool = False,
        start_logging_gradient_norms_at: float = 0.0,
        checkpoint_start_pct: int = 50,
        use_confidence: bool = True,
        confidence_padding: int = 0,
        int8_calibration: Optional[str] = None,
        customer: Optional[str] = None,
        robot_name: Optional[str] = None,
        environment: Environment = Environment.DEVELOPMENT,
        deploy: bool = False,
        additional_wandb_config: Dict[str, Any] = {},
        model: Optional[Deepweed] = None,
        frozen_backbone_point_layers: Optional[List[str]] = None,
        positive_sample_milestones: Optional[List[int]] = None,
        positive_sample_gamma: float = 1.0,
        dataset_id: Optional[str] = None,
        sub_type: Optional[str] = None,
        last_nth_timestamps: Optional[Dict[int, DatapointTimestamps]] = None,
        pipeline_id: Optional[str] = None,
        crop_ids: Optional[List[str]] = None,
        parent_model_id: Optional[str] = None,
        segm_dice_loss_weight: float = 1.0,
        segm_bce_loss_weight: float = 5.0,
        new_data_weight: Optional[float] = None,
        geohash_prefix: Optional[str] = None,
        family_id: Optional[str] = None,
        logger: Optional[WandbLogger] = None,
    ) -> None:
        self._datasets = datasets
        self._ci_run = ci_run

        training_parameters = self.update_training_parameters(locals())
        additional_wandb_config.update({"parameters": training_parameters})

        start_logging_gradient_norms_at_epoch = int(start_logging_gradient_norms_at * config.num_epochs)

        self.sub_type = sub_type

        tags = self.form_tags(tags, environment)

        download_resume_model(resume_from=resume_from)

        # Create trainer
        self.initialize_module(
            config=config,
            overprediction_warmup=overprediction_warmup,
            train_log_image_p=train_log_image_p,
            val_log_image_p=val_log_image_p,
            tile_height=tile_height,
            tile_width=tile_width,
            eval_four_flips=eval_four_flips,
            log_gradient_norms=log_gradient_norms,
            start_logging_gradient_norms_at_epoch=start_logging_gradient_norms_at_epoch,
            use_confidence=use_confidence,
            confidence_padding=confidence_padding,
            additional_wandb_config=additional_wandb_config,
            pretrained_model=pretrained_model,
            pretrained_segmentation_model=pretrained_segmentation_model,
            model=model,
            int8_calibration=int8_calibration,
            frozen_backbone_point_layers=frozen_backbone_point_layers,
            positive_sample_milestones=positive_sample_milestones,
            positive_sample_gamma=positive_sample_gamma,
            ci_run=ci_run,
            segm_dice_loss_weight=segm_dice_loss_weight,
            segm_bce_loss_weight=segm_bce_loss_weight,
            new_data_weight=new_data_weight,
            dataset_id=dataset_id,
        )

        crop = self.select_crop()

        self.experiment_directory = super()._train_and_unoptimized_test(
            self.module,
            ci_run=config.ci_run,
            fast_run=config.fast_run,
            epochs=config.num_epochs,
            checkpoint_start_pct=checkpoint_start_pct,
            resume_from=resume_from,
            description=description,
            tags=tuple(tags),
            log_experiment=log_experiment,
            checkpoint_dir=checkpoint_dir,
            version=get_version(),
            customer=customer,
            robot_name=robot_name,
            environment=environment,
            deploy=deploy,
            crop=crop,
            sub_type=self.sub_type,
            pipeline_id=pipeline_id,
            crop_ids=crop_ids,
            parent_model_id=parent_model_id,
            dataset_id=dataset_id,
            check_val_every_n_epoch=config.check_val_every_n_epoch,
            precision=config.precision,
            sync_batchnorm=True,
            comparison_model_id=config.comparison_model_id,
            logger=logger,
            geohash_prefix=geohash_prefix,
        )

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def load_pumap_head(self, config: DeepweedConfig, dw_model_checkpoint: str, pumap_model_checkpoint: str,) -> None:
        dw_model_metadata = peek_pytorch_metadata(dw_model_checkpoint)

        num_segm_classes = 0
        num_weed_point_classes = 0
        num_crop_point_classes = 0
        num_crop_ids = 0

        if dw_model_metadata.weed_point_classes is not None:
            num_weed_point_classes = len(dw_model_metadata.weed_point_classes)
        if dw_model_metadata.crop_point_classes is not None:
            num_crop_point_classes = len(dw_model_metadata.crop_point_classes)
        if dw_model_metadata.segm_classes is not None:
            num_segm_classes = len(dw_model_metadata.segm_classes)
        if dw_model_metadata.crop_ids is not None:
            num_crop_ids = len(dw_model_metadata.crop_ids)

        template_model = Deepweed(
            num_weed_point_classes=num_weed_point_classes,
            num_segm_classes=num_segm_classes,
            num_crop_ids=num_crop_ids,
            enable_crop_embeddings=dw_model_metadata.crop_embeddings or False,
            discard_points_border_px=dw_model_metadata.discard_points_border_px,
            disable_crop=num_crop_point_classes == 0,
            config=config,
            use_pumap_head=False,
        )

        dw_model, dw_metadata = load_pytorch_model(template_model, dw_model_checkpoint)
        pumap_model, pumap_metadata = load_pytorch_model(ParametricUMAP(), pumap_model_checkpoint)
        pumap_model.set_scaler_shifter(pumap_metadata.scaler_shifter_parameters)
        dw_model.set_pumap_head(cast(ParametricUMAP, pumap_model))

        dw_metadata = dw_metadata.with_contains_pumap_head(True)
        dw_metadata = dw_metadata.with_scaler_shifter_parameters(dw_model._pumap_head.scaler_shifter_parameters)

        if torch.distributed.get_rank() == 0:
            save_pytorch_model(dw_model, dw_metadata, dw_model_checkpoint)

    @duration_perf_recorder_decorator(PerfCategory.TRAINING)
    def optimize_and_test(
        self,
        config: DeepweedConfig,
        datasets: RemoteVeselkaDatasets,
        overprediction_warmup: int = 0,
        train_log_image_p: float = 0.05,
        val_log_image_p: float = 0.05,
        pretrained_model: Optional[str] = None,
        pretrained_segmentation_model: Optional[str] = None,
        # If not enough validation data is available, use tiling window smaller than training window
        # and evaluate on four flips
        tile_height: int = 1200,
        tile_width: int = 1200,
        eval_four_flips: bool = False,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        log_experiment: bool = True,
        ci_run: bool = False,
        checkpoint_dir: Optional[str] = None,
        log_gradient_norms: bool = False,
        start_logging_gradient_norms_at: float = 0.0,
        checkpoint_start_pct: int = 50,
        use_confidence: bool = True,
        confidence_padding: int = 0,
        int8_calibration: Optional[str] = None,
        environment: Environment = Environment.DEVELOPMENT,
        additional_wandb_config: Dict[str, Any] = {},
        model: Optional[Deepweed] = None,
        frozen_backbone_point_layers: Optional[List[str]] = None,
        positive_sample_milestones: Optional[List[int]] = None,
        positive_sample_gamma: float = 1.0,
        dataset_id: Optional[str] = None,
        sub_type: Optional[str] = None,
        last_nth_timestamps: Optional[Dict[int, DatapointTimestamps]] = None,
        crop_ids: Optional[List[str]] = None,
        parent_model_id: Optional[str] = None,
        segm_dice_loss_weight: float = 1.0,
        segm_bce_loss_weight: float = 5.0,
        new_data_weight: Optional[float] = None,
        check_val_every_n_epoch: int = 1,
        geohash_prefix: Optional[str] = None,
        family_id: Optional[str] = None,
        logger: Optional[WandbLogger] = None,
    ) -> None:
        self._datasets = datasets

        assert (
            self._datasets is not None
        ), "Dataset is None. Please initialize dataset (.veselka_dataset(...)) before training"

        if family_id is None:
            family_id = str(uuid.uuid4())

        training_parameters = self.update_training_parameters(locals())
        additional_wandb_config.update({"parameters": training_parameters})

        start_logging_gradient_norms_at_epoch = int(start_logging_gradient_norms_at * config.num_epochs)

        self.sub_type = sub_type

        tags = self.form_tags(tags, environment)

        # Create trainer
        self.initialize_module(
            config=config,
            overprediction_warmup=overprediction_warmup,
            train_log_image_p=train_log_image_p,
            val_log_image_p=val_log_image_p,
            tile_height=tile_height,
            tile_width=tile_width,
            eval_four_flips=eval_four_flips,
            log_gradient_norms=log_gradient_norms,
            start_logging_gradient_norms_at_epoch=start_logging_gradient_norms_at_epoch,
            use_confidence=use_confidence,
            confidence_padding=confidence_padding,
            additional_wandb_config=additional_wandb_config,
            pretrained_model=pretrained_model,
            pretrained_segmentation_model=pretrained_segmentation_model,
            model=model,
            int8_calibration=int8_calibration,
            frozen_backbone_point_layers=frozen_backbone_point_layers,
            positive_sample_milestones=positive_sample_milestones,
            positive_sample_gamma=positive_sample_gamma,
            ci_run=ci_run,
            segm_dice_loss_weight=segm_dice_loss_weight,
            segm_bce_loss_weight=segm_bce_loss_weight,
            new_data_weight=new_data_weight,
            dataset_id=dataset_id,
        )

        crop = self.select_crop()

        self.experiment_directory = super()._optimize_and_test(
            self.module,
            fast_run=config.fast_run,
            epochs=config.num_epochs,
            checkpoint_start_pct=checkpoint_start_pct,
            description=description,
            tags=tuple(tags),
            log_experiment=log_experiment,
            checkpoint_dir=checkpoint_dir,
            check_val_every_n_epoch=check_val_every_n_epoch,
            followup_logging_fn=self.get_followup_logging_fn(
                sub_type=sub_type,
                config=config,
                parent_model_id=parent_model_id,
                last_nth_timestamps=last_nth_timestamps,
            ),
            precision=config.precision,
            sync_batchnorm=True,
            ci_run=ci_run,
            logger=logger,
        )

        if torch.distributed.get_rank() == 0 and int8_calibration is not None and os.path.exists(int8_calibration):
            assert self.experiment_directory is not None
            shutil.copy(int8_calibration, os.path.join(self.experiment_directory, os.path.basename(int8_calibration)))

        if config.make_trt_model and torch.distributed.get_rank() == 0 and not self._ci_run and log_experiment:
            assert self.experiment_directory is not None
            model_id = self.model_id

            wandb_json = get_wandb_metadata_json_str(self.experiment_directory)

            with open(os.path.join(self.experiment_directory, "test_results.json"), "r") as f:
                test_results = json.load(f)
            with open(os.path.join(self.experiment_directory, "test_unoptimized_results.json"), "r") as f:
                test_results.update(json.load(f))
            self._veselka_post_model(
                model_id=model_id,
                url=f"s3://{S3_BUCKET}/models/{model_id}/{self.module.trt_file_name}",
                crop=crop,
                checksum=compute_md5sum(self.module.trt_file_path),
                trained_at=self._start_time,
                metadata_json=self.module.export_metadata().dump(),
                test_results_json=json.dumps(test_results),
                wandb_json=wandb_json,
                is_good_to_deploy=True,
                exp_dir=self.experiment_directory,
                weed_point_threshold=config.point_hit_threshold,
                crop_point_threshold=config.point_hit_threshold,
                is_pretraining=sub_type == DeepweedTrainingSubtype.PRETRAIN.name.lower(),
                dataset_id=dataset_id,
                sub_type=self.sub_type,
                crop_ids=crop_ids,
                parent_model_id=parent_model_id,
                recency_split=self._datasets.get_training().recency_split,
                geohash_prefix=geohash_prefix,
                family_id=family_id,
                dl_config=config.to_dict(),
            )

            recency_split_age = None
            if self._datasets.get_training().recency_split_age is not None:
                recency_split_age = self._datasets.get_training().recency_split_age
                assert recency_split_age is not None
                recency_split_age = recency_split_age // (1000 * 60 * 60)

            if logger is not None:
                logger.experiment.config.update(
                    {
                        "recency_split_age_h": recency_split_age,
                        "recency_split": self._datasets.get_training().recency_split,
                    }
                )


if __name__ == "__main__":
    DeepweedTrainer.main()

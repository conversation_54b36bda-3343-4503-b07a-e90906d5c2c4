import json
import logging
import os
from collections import defaultdict
from typing import Dict, List

from deeplearning.deepweed.remote_veselka_dataset import RemoteVeselkaDataset
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder_decorator

LOG = logging.getLogger(__name__)


@duration_perf_recorder_decorator(PerfCategory.TRAINING)
def save_sampling_file(
    dir: str,
    image_filepaths: Dict[str, int],
    dataset_id: str,
    file_embeddings: Dict[str, List[int]],
    selected_categories: Dict[str, int],
    selected_embeddings: Dict[str, int],
    class_data: Dict[str, List[str]],
) -> None:
    os.makedirs(dir, exist_ok=True)
    with open(os.path.join(dir, "sampled_filepaths.json"), "w") as f:
        points_images = {
            "filepaths": image_filepaths,
            "dataset_id": dataset_id,
            "file_embeddings": file_embeddings,
            "selected_categories": selected_categories,
            "selected_embeddings": selected_embeddings,
            "class_data": class_data,
        }
        json.dump(points_images, f)


def simulate_sampling(dataset: RemoteVeselkaDataset, number_samples: int, epochs: int, dir: str) -> None:
    image_filepaths: Dict[str, int] = defaultdict(int)
    file_embeddings: Dict[str, List[int]] = defaultdict(list)
    selected_categories: Dict[str, int] = defaultdict(int)
    selected_embeddings: Dict[str, int] = defaultdict(int)
    classdata: Dict[str, List[str]] = {}

    classdata["weed"] = dataset.weed_classes
    classdata["crop"] = dataset.crop_classes

    for epoch in range(epochs):
        LOG.info(f"Epoch {epoch}")
        for i in range(number_samples):
            if i % 500 == 0:
                LOG.info(f"    iteration: {i}")
            sample_metadata = dataset[i]
            image_filepaths[sample_metadata["uri"]] += 1
            file_embeddings[sample_metadata["uri"]] = sample_metadata["embeddings"]
            selected_categories[sample_metadata["selected_category"]] += 1
            selected_embeddings[sample_metadata["selected_embedding"]] += 1

    save_sampling_file(
        dir,
        image_filepaths,
        os.path.basename(os.path.dirname(dataset.filepath)),
        file_embeddings,
        selected_categories,
        selected_embeddings,
        classdata,
    )

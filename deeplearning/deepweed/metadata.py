import datetime
import fnmatch
import json
import logging
import os
import re
import uuid as uuid_lib
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Set, Tuple, cast

import torch

from cv.deepweed.deepweed_python import DeepweedDetection
from deeplearning.utils.uuid import get_globally_unique_id
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass

if TYPE_CHECKING:
    from generated.cv.runtime.proto.cv_runtime_pb2 import HitClassValue

META_JSON = "meta.json"
DEFAULT_POINT_CONFIDENCE = 2

LOG = logging.getLogger(__name__)


class Point:
    def __init__(
        self,
        x: float,
        y: float,
        r: float,
        score: float,
        clz: str,
        confidence: int,
        hit_clz: "HitClassValue",
        clz_categories: Optional[List[Tuple[str, float]]] = None,
        comparison_embedding: Optional[torch.Tensor] = None,
        relocation_info: Optional[Dict[str, Any]] = None,
        hit_y_x: Optional[List[Tuple[int, int]]] = None,
        deepweed_embedding_bucket: Optional[int] = None,
        uuid: Optional[str] = None,
        plant_score: Optional[float] = None,
        weed_score: Optional[float] = None,
        crop_score: Optional[float] = None,
        weed_clz_categories: Optional[List[Tuple[str, float]]] = None,
    ):
        hit_y_x = hit_y_x if hit_y_x is not None else []
        self.x = x
        self.y = y
        self.r = r
        self.score = score
        self.clz = clz
        self.confidence = confidence
        self.hit_clz = hit_clz
        self.clz_categories = clz_categories
        self.comparison_embedding = comparison_embedding
        self.relocation_info = relocation_info
        self.hit_y_x: List[Tuple[int, int]] = hit_y_x
        self.deepweed_embedding_bucket = deepweed_embedding_bucket
        self.uuid = uuid if uuid is not None else str(uuid_lib.uuid4())
        self.weed_clz_categories = weed_clz_categories
        self.plant_score = plant_score
        self.weed_score = weed_score
        self.crop_score = crop_score

    def set_hit_y_x(self, hit_y_xs: List[Tuple[int, int]]) -> None:
        self.hit_y_x = hit_y_xs

    def __repr__(self) -> str:
        return f"Point({self.x}, {self.y}, {self.r}, {self.score}, {HitClass.Name(self.hit_clz)}, {self.clz}, {self.confidence}, {self.clz_categories}, comparison_embedding={self.comparison_embedding is not None}, relocation_info={self.relocation_info}), hit_y_x={len(self.hit_y_x)}, deepweed_embedding_bucket={self.deepweed_embedding_bucket}"

    @staticmethod
    def from_json(point_dict: Dict[str, Any]) -> "Point":
        return Point(
            point_dict["x"],
            point_dict["y"],
            point_dict["radius"],
            1.0,
            point_dict["class"].upper() if "class" in point_dict else point_dict["point_category_id"].upper(),
            point_dict.get("confidence", DEFAULT_POINT_CONFIDENCE),
            HitClass.Value(point_dict["hit_class"].upper()) if "hit_class" in point_dict else HitClass.WEED,
            comparison_embedding=point_dict.get("comparison_embedding"),
            relocation_info=point_dict.get("relocation_info"),
            deepweed_embedding_bucket=point_dict.get("deepweed_embedding_bucket"),
            uuid=get_globally_unique_id(point_dict),
        )


def detections_to_points(
    detections: List[DeepweedDetection], category_threshold: float, weed_point_classes: List[str]
) -> List[Point]:
    points = []
    for detection in detections:
        category = "UNKNOWN"
        detection_class_pairs: List[Tuple[str, float]] = detection.get_detection_classes(weed_point_classes)
        weed_clz_categories: List[Tuple[str, float]] = detection.get_detection_classes(weed_point_classes)
        best_score = -1.0
        best_class = ""
        for d in detection_class_pairs:
            if d[1] > best_score:
                best_class = d[0]
                best_score = d[1]
        if best_score > category_threshold:
            category = best_class

        point = Point(
            x=detection.x,
            y=detection.y,
            r=detection.size,
            score=detection.score,
            clz=category,
            confidence=DEFAULT_POINT_CONFIDENCE,
            hit_clz=detection.get_hit_class(),
            clz_categories=detection_class_pairs,
            hit_y_x=[(detection.hit_y, detection.hit_x)],
            plant_score=detection.plant_score,
            weed_score=detection.weed_score,
            crop_score=detection.crop_score,
            weed_clz_categories=weed_clz_categories,
        )
        points.append(point)

    return points


class Polygon:
    def __init__(self, polygon_dict: Dict[str, Any]):
        self.label = polygon_dict["label"]
        self.confidence = polygon_dict["confidence"]
        self.coordinates = polygon_dict["coordinates"]

    def __repr__(self) -> str:
        return f"label={self.label}, confidence={self.confidence}, coordinates={self.coordinates}"


class LabelMetadata:
    def __init__(
        self,
        filepath: Optional[str],
        clazz: str,
        certified: bool,
        points: List[Point],
        polygons: Optional[List[Polygon]] = None,
        last_updated_timestamp_ms: Optional[int] = None,
        points_certified_categories: Optional[List[str]] = None,
    ):
        self._filepath = filepath
        self._clazz = clazz
        self._certified = certified
        self._points = points
        self._polygons = polygons
        self._last_updated_timestamp_ms = last_updated_timestamp_ms
        self._points_certified_categories = points_certified_categories

    @property
    def polygons(self) -> Optional[List[Polygon]]:
        return self._polygons

    @property
    def filepath(self) -> Optional[str]:
        return self._filepath

    @property
    def clazz(self) -> str:
        return self._clazz

    @property
    def certified(self) -> bool:
        return self._certified

    @property
    def points(self) -> List[Point]:
        # make a copy of the list here to ensure unmodifiable behavior
        return list(self._points)

    @property
    def points_certified_categories(self) -> Optional[List[str]]:
        return self._points_certified_categories

    @property
    def last_updated_timestamp_ms(self) -> Optional[int]:
        return self._last_updated_timestamp_ms

    @staticmethod
    def from_mask_json(filepath: str, clazz: str) -> "LabelMetadata":
        assert os.path.exists(filepath), f"Mask .json file does not exist: {filepath}"

        dirpath, filename = os.path.split(filepath)
        mask_filename = os.path.splitext(filename)[0] + ".png"
        mask_filepath: Optional[str] = os.path.join(dirpath, mask_filename)
        if not os.path.exists(cast(str, mask_filepath)):
            # Mask does not exist - reset to None
            mask_filepath = None

        with open(filepath, "r") as f:
            meta_json = json.load(f)

        assert meta_json["version"] == 1, f"Unexpected version: {meta_json['version']}"
        points_json = meta_json.get("points", [])
        points_list = [Point.from_json(point) for point in points_json]
        points = points_list
        last_updated_timestamp_ms = meta_json.get("last_updated", None)
        return LabelMetadata(
            filepath=mask_filepath,
            clazz=clazz,
            certified=meta_json["certified"],
            points=points,
            last_updated_timestamp_ms=last_updated_timestamp_ms,
            points_certified_categories=[x.upper() for x in meta_json["points_certified_categories"]]
            if "points_certified_categories" in meta_json
            else None,
        )


class ImageMetadata:
    def __init__(
        self,
        filepath: str,
        ppi: Optional[float] = None,
        labels: Optional[Dict[str, LabelMetadata]] = None,
        points_label: Optional[LabelMetadata] = None,
        npz_rgb_key: Optional[str] = None,
        npz_depth_key: Optional[str] = None,
        new_datapoint: bool = False,
        timestamp: Optional[int] = None,
        city: Optional[str] = None,
        crop: Optional[str] = None,
        crop_id: Optional[str] = None,
        robot_id: Optional[str] = None,
        row_id: Optional[str] = None,
        cam_id: Optional[str] = None,
        session_name: Optional[str] = None,
        geohash: Optional[str] = None,
        deepweed_embeddings: Optional[List[int]] = None,
    ):
        labels = labels if labels is not None else {}
        self._filepath = filepath
        self._ppi = ppi
        self._labels = labels
        self._npz_rgb_key = npz_rgb_key
        self._npz_depth_key = npz_depth_key
        self._points_label = points_label
        self._points_classes: Set[str] = set()
        self._new_datapoint = new_datapoint
        self._crop = crop
        self._crop_id = crop_id
        self._robot_id = robot_id
        self._row_id = row_id
        self._cam_id = cam_id
        self._session_name = session_name
        self._geohash = geohash
        self._deepweed_embeddings = deepweed_embeddings
        if self._points_label is not None:
            for point in self._points_label.points:
                self._points_classes.add(point.clz)

        if city is None:
            with open(os.path.splitext(filepath)[0] + ".points.json") as f:
                points_data = json.load(f)
            self.city = points_data["city"]
        else:
            self.city = city

        if timestamp is None:
            with open(os.path.splitext(filepath)[0] + ".metadata.json") as f:
                metadata = json.load(f)
            self._timestamp = metadata["timestamp_ms"]
        else:
            self._timestamp = timestamp

        self.date = datetime.datetime.fromtimestamp(self._timestamp / 1000.0).date()

    @property
    def deepweed_embedding_buckets(self) -> Optional[List[int]]:
        """
        A list of embedding buckets represented in the image. This is largely useable for sampling verification
        """
        return self._deepweed_embeddings

    @property
    def crop(self) -> Optional[str]:
        return self._crop

    @property
    def robot_id(self) -> Optional[str]:
        return self._robot_id

    @property
    def row_id(self) -> Optional[str]:
        return self._row_id

    @property
    def cam_id(self) -> Optional[str]:
        return self._cam_id

    @property
    def timestamp_ms(self) -> Optional[int]:
        return cast(Optional[int], self._timestamp)

    @property
    def filepath(self) -> str:
        return self._filepath

    @property
    def ppi(self) -> Optional[float]:
        return self._ppi

    @property
    def labels(self) -> Dict[str, LabelMetadata]:
        return self._labels

    @property
    def points_label(self) -> Optional[LabelMetadata]:
        return self._points_label

    @property
    def points_classes(self) -> Set[str]:
        return self._points_classes

    @property
    def new_datapoint(self) -> bool:
        return self._new_datapoint

    @property
    def session_name(self) -> Optional[str]:
        return self._session_name

    @property
    def geohash(self) -> Optional[str]:
        return self._geohash

    @property
    def crop_id(self) -> Optional[str]:
        return self._crop_id

    def has_certified_segm_label(self, clz: str) -> bool:
        return clz in self.labels and self.labels[clz].certified

    def has_positive_segm_label(self, clz: str) -> bool:
        return self.has_certified_segm_label(clz) and self.labels[clz].filepath is not None

    def has_positive_points_label(self, clz: str) -> bool:
        return self.has_certified_points_label(clz) and clz in self._points_classes

    def has_certified_points_label(self, clz: str) -> bool:
        return (
            self.points_label is not None
            and self.points_label.certified
            and (
                self.points_label.points_certified_categories is None
                or clz in self.points_label.points_certified_categories
            )
        )

    @property
    def npz_rgb_key(self) -> Optional[str]:
        return self._npz_rgb_key

    @property
    def npz_depth_key(self) -> Optional[str]:
        return self._npz_depth_key

    @staticmethod
    def from_file(
        filepath: str,
        ppi: Optional[float],
        segm_classes: List[str],
        new_datapoint: bool = False,
        session_name: str = "",
    ) -> "ImageMetadata":
        labels: Dict[str, LabelMetadata] = {}
        dirpath, filename = os.path.split(filepath)
        for clazz in segm_classes:
            mask_json_filename = os.path.splitext(filename)[0] + f".mask_{clazz.lower()}.json"
            mask_json_filepath = os.path.join(dirpath, mask_json_filename)
            if os.path.exists(mask_json_filepath):
                labels[clazz] = LabelMetadata.from_mask_json(mask_json_filepath, clazz)
        points_filepath = os.path.join(dirpath, os.path.splitext(filename)[0] + ".points.json")
        points_metadata: Optional[LabelMetadata] = None
        if os.path.exists(points_filepath):
            points_metadata = LabelMetadata.from_mask_json(points_filepath, "crown")
        return ImageMetadata(
            filepath=filepath,
            ppi=ppi,
            labels=labels,
            points_label=points_metadata,
            new_datapoint=new_datapoint,
            session_name=session_name,
        )


class _PatternMetadata:
    def __init__(
        self,
        pattern: str,
        ppi: Optional[float] = None,
        npz_rgb_key: Optional[str] = None,
        npz_depth_key: Optional[str] = None,
    ):
        self._pattern = pattern
        self._ppi = ppi
        self._npz_rgb_key = npz_rgb_key
        self._npz_depth_key = npz_depth_key

    @property
    def pattern(self) -> str:
        return self._pattern

    @property
    def ppi(self) -> Optional[float]:
        return self._ppi

    @property
    def npz_rgb_key(self) -> Optional[str]:
        return self._npz_rgb_key

    @property
    def npz_depth_key(self) -> Optional[str]:
        return self._npz_depth_key


class DirectoryMetadata:
    def __init__(self, dirpath: str, patterns: List[_PatternMetadata], segm_classes: List[str]):
        self._dirpath = dirpath
        self._patterns = patterns
        self._segm_classes = segm_classes

    def list_files(self, camera: Optional[str] = None) -> List[ImageMetadata]:
        mask_regex = r"^.*\.mask_(.*?)\.png$"
        input_regex = r"^.*(?:{camera}).*\.(?:png|jpg|npz)$".format(camera=re.escape(camera) if camera else "")
        files = [f for f in os.listdir(self._dirpath) if re.match(input_regex, f) and not re.match(mask_regex, f)]
        result = []
        for f in files:
            image_meta = self.get_image_metadata(f)
            if image_meta is not None:
                result.append(image_meta)
        return result

    def get_image_metadata(self, filename: str) -> Optional[ImageMetadata]:
        # Assumes that we want the first match, and that multiple patterns won't apply
        for pat in self._patterns:
            if fnmatch.fnmatch(filename, pat.pattern):
                ppi = pat.ppi

                return ImageMetadata.from_file(
                    filepath=os.path.join(self._dirpath, filename), ppi=ppi, segm_classes=self._segm_classes,
                )

        return None

    @staticmethod
    def load(dirpath: str, segm_classes: List[str]) -> "DirectoryMetadata":
        path = os.path.join(dirpath, META_JSON)
        patterns = []
        if os.path.isfile(path):
            try:
                with open(path, "r") as f:
                    meta_json = json.load(f)
                for _json in meta_json:
                    pattern = _PatternMetadata(
                        pattern=_json["pattern"],
                        ppi=_json.get("ppi"),
                        npz_rgb_key=_json.get("npz_rgb_key"),
                        npz_depth_key=_json.get("npz_depth_key"),
                    )
                    patterns.append(pattern)
            except Exception as e:
                print(f"Could not deserialize {path}: {e}")
        return DirectoryMetadata(dirpath=dirpath, patterns=patterns, segm_classes=segm_classes)

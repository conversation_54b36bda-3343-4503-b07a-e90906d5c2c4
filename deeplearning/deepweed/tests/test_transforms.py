from deeplearning.deepweed.transforms import select_cropping_point_x_y_ranges


def test_select_cropping_point_x_y_ranges() -> None:
    for ind, test in enumerate(
        [
            {
                "x": 10,
                "y": 10,
                "image_height": 400,
                "image_width": 300,
                "crop_height": 200,
                "crop_width": 150,
                "discard_border_px": 5,
                "expected_min_x": 0,
                "expected_max_x": 4,
                "expected_min_y": 0,
                "expected_max_y": 4,
            },
            {
                "x": 10,
                "y": 10,
                "image_height": 400,
                "image_width": 300,
                "crop_height": 200,
                "crop_width": 150,
                "discard_border_px": 0,
                "expected_min_x": 0,
                "expected_max_x": 9,
                "expected_min_y": 0,
                "expected_max_y": 9,
            },
            {
                "x": 200,
                "y": 200,
                "image_height": 400,
                "image_width": 400,
                "crop_height": 200,
                "crop_width": 200,
                "discard_border_px": 10,
                "expected_min_x": 11,
                "expected_max_x": 189,
                "expected_min_y": 11,
                "expected_max_y": 189,
            },
            {
                "x": 300,
                "y": 300,
                "image_height": 400,
                "image_width": 400,
                "crop_height": 200,
                "crop_width": 200,
                "discard_border_px": 0,
                "expected_min_x": 101,
                "expected_max_x": 200,
                "expected_min_y": 101,
                "expected_max_y": 200,
            },
            {
                "x": 300,
                "y": 200,
                "image_height": 400,
                "image_width": 400,
                "crop_height": 200,
                "crop_width": 200,
                "discard_border_px": 10,
                "expected_min_x": 111,
                "expected_max_x": 200,
                "expected_min_y": 11,
                "expected_max_y": 189,
            },
            {
                "x": 200,
                "y": 300,
                "image_height": 400,
                "image_width": 400,
                "crop_height": 200,
                "crop_width": 200,
                "discard_border_px": 0,
                "expected_min_x": 1,
                "expected_max_x": 199,
                "expected_min_y": 101,
                "expected_max_y": 200,
            },
        ]
    ):
        min_x, max_x, min_y, max_y = select_cropping_point_x_y_ranges(
            x=test["x"],
            y=test["y"],
            image_width=test["image_width"],
            image_height=test["image_height"],
            crop_width=test["crop_width"],
            crop_height=test["crop_height"],
            discard_border_px=test["discard_border_px"],
        )

        assert min_x == test["expected_min_x"], f"Failure: test {ind}"
        assert max_x == test["expected_max_x"], f"Failure: test {ind}"
        assert min_y == test["expected_min_y"], f"Failure: test {ind}"
        assert max_y == test["expected_max_y"], f"Failure: test {ind}"

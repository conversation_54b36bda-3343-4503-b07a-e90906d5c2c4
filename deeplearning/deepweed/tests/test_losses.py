import logging
import math
import random
from typing import Any, List

import torch

from deeplearning.deepweed.losses.embeddings_loss import (
    embeddings_loss,
    find_positive_and_negative_examples,
    get_average_embedding,
    get_eligible_batch_points,
    get_random_embedding,
    info_nce_loss,
    triplet_loss,
)
from deeplearning.deepweed.metadata import Point
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass

LOG = logging.getLogger(__name__)

EPS = 1e-6


def test_get_eligible_batch_points() -> None:
    initial_comparison_embedding = torch.randn([4096])
    point_a = Point(
        x=0.0,
        y=10,
        r=10,
        score=0.1,
        comparison_embedding=initial_comparison_embedding,
        hit_y_x=[(1, 0), (2, 0)],
        clz="purslane",
        confidence=10,
        hit_clz=HitClass.WEED,
    )
    point_b = Point(
        x=1.0,
        y=11,
        r=11,
        score=0.8,
        comparison_embedding=initial_comparison_embedding,
        hit_y_x=[(1, 0), (2, 0)],
        clz="carrot",
        confidence=10,
        hit_clz=HitClass.CROP,
    )
    point_c = Point(
        x=0.1, y=12, r=12, score=0.6, hit_y_x=[(1, 0), (2, 0)], clz="broadleaf", confidence=10, hit_clz=HitClass.WEED,
    )

    all_points = [[point_a], [point_b, point_c]]

    batch_points = get_eligible_batch_points(all_points)

    assert len(batch_points) == 2
    assert batch_points[0] == [{"id": 0, "batch_ind": 0, "point": point_a}]
    assert batch_points[1] == [{"id": 1, "batch_ind": 1, "point": point_b}]


def test_info_nce_loss_only_positive() -> None:

    # Test When There are Only Positive Samples
    embeddings = torch.randn(3, 1024, 106, 113)
    initial_comparison_embedding = torch.randn([4096])

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=initial_comparison_embedding,
                hit_y_x=[(random.randint(0, 105), random.randint(0, 112)) for _ in range(10)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(100)
        ]
        for y in range(3)
    ]
    loss, loss_per_sample, sets = info_nce_loss(
        points, embeddings, base_embedding_difference_on_hit_class=True, num_neg_samples=10
    )
    assert not sets


def test_info_nce_loss_only_negative() -> None:

    # Only Negative Samples
    embeddings = torch.randn(3, 1024, 106, 113)
    zero_embedding = torch.tensor([0, 1.0], dtype=torch.float)
    one_embedding = torch.tensor([1, -1.0], dtype=torch.float)

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=zero_embedding if x == 0 else one_embedding,
                hit_y_x=[(random.randint(0, 105), random.randint(0, 112)) for _ in range(10)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(2)
        ]
    ]
    loss, loss_per_sample, sets = info_nce_loss(
        points, embeddings, base_embedding_difference_on_hit_class=True, num_neg_samples=10
    )
    assert not sets

    # Not enough negative samples to maximize
    embeddings = torch.randn(3, 1024, 106, 113)
    zero_embedding = torch.tensor([0, 1.0], dtype=torch.float)
    one_embedding = torch.tensor([1, -1.0], dtype=torch.float)

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=zero_embedding if x <= 1 else one_embedding,
                hit_y_x=[(random.randint(0, 105), random.randint(0, 112)) for _ in range(10)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(10)
        ]
    ]
    loss, loss_per_sample, sets = info_nce_loss(
        points, embeddings, base_embedding_difference_on_hit_class=True, num_neg_samples=10
    )
    assert sets
    assert len(sets) > 0


def test_info_nce_loss_calculation() -> None:

    # Testing Calculating Loss
    embeddings = torch.zeros(1, 1024, 106, 113)
    embeddings[0, :, 0, 0] = torch.ones_like(embeddings[0, :, 0, 0])
    zero_embedding = torch.tensor([0, 1.0], dtype=torch.float)
    one_embedding = torch.tensor([1, -1.0], dtype=torch.float)

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=zero_embedding if x <= 12 else one_embedding,
                hit_y_x=[(0, 0)] if x <= 12 else [(1, 1)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(50)
        ]
    ]
    loss, loss_per_sample, sets = info_nce_loss(
        points, embeddings, base_embedding_difference_on_hit_class=True, num_neg_samples=10, max_items_to_compare=1
    )
    assert sets
    assert loss < 1e-2
    assert len(sets[0]) == 11


def test_info_nce_embedding_selection_options() -> None:
    # Testing Calculating Loss
    embeddings = torch.zeros(1, 1024, 106, 113)
    embeddings[0, :, 0, 0] = torch.ones_like(embeddings[0, :, 0, 0])
    zero_embedding = torch.tensor([0, 1.0], dtype=torch.float)
    one_embedding = torch.tensor([1, -1.0], dtype=torch.float)

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=zero_embedding if x <= 12 else one_embedding,
                hit_y_x=[(0, 0)] if x <= 12 else [(1, 1)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(50)
        ]
    ]
    loss, loss_per_sample, sets = info_nce_loss(
        points,
        embeddings,
        base_embedding_difference_on_hit_class=True,
        num_neg_samples=10,
        max_items_to_compare=1,
        average_embeddings_across_hits=True,
    )
    assert sets
    assert loss < 1e-2
    assert len(sets[0]) == 11

    loss, loss_per_sample, sets = info_nce_loss(
        points,
        embeddings,
        base_embedding_difference_on_hit_class=True,
        num_neg_samples=10,
        max_items_to_compare=1,
        average_embeddings_across_hits=False,
    )
    assert sets
    assert loss < 1e-2
    assert len(sets[0]) == 11


def test_find_positive_and_negative_examples() -> None:
    test_mat = torch.Tensor([[0, 1], [0, 2], [1, 1], [1, 0], [0.5, 0],])

    positive, negative = find_positive_and_negative_examples(test_mat, 0.8, 0.2)

    assert set(positive.keys()) == set([0, 1, 3, 4])

    assert sorted(positive[0]) == [1]
    assert sorted(positive[1]) == [0]
    assert sorted(positive[3]) == [4]
    assert sorted(positive[4]) == [3]

    assert set(negative.keys()) == set([0, 1, 3, 4])
    assert sorted(negative[0]) == [3, 4]
    assert sorted(negative[1]) == [3, 4]
    assert sorted(negative[3]) == [0, 1]
    assert sorted(negative[4]) == [0, 1]


def test_find_positive_negative_examples_one_positive_one_negative() -> None:
    test_mat = torch.Tensor([[1.0, 0.0], [2.0, 0.0], [0.0, 3.0]])

    positive, negative = find_positive_and_negative_examples(test_mat, 0.8, 0.2)

    assert len(positive.keys()) == 2
    assert len(negative.keys()) == 3
    assert sorted(positive[0]) == [1]
    assert sorted(positive[1]) == [0]
    assert sorted(negative[0]) == [2]
    assert sorted(negative[1]) == [2]
    assert sorted(negative[2]) == [0, 1]


def test_find_positive_and_negative_examples_with_hit() -> None:
    test_mat = torch.Tensor([[0, 1], [0, 2], [1, 1], [1, 0], [0.5, 0],])
    hit_match_list = [HitClass.WEED, HitClass.CROP, HitClass.CROP, HitClass.WEED, HitClass.WEED]

    positive, negative = find_positive_and_negative_examples(test_mat, 0.8, 0.2, hit_list=hit_match_list)

    assert set(positive.keys()) == set([3, 4])

    assert sorted(positive[3]) == [4]
    assert sorted(positive[4]) == [3]

    assert set(negative.keys()) == set([0, 1, 2, 3, 4])
    assert sorted(negative[0]) == [1, 2, 3, 4]
    assert sorted(negative[1]) == [0, 3, 4]
    assert sorted(negative[2]) == [0, 3, 4]
    assert sorted(negative[3]) == [0, 1, 2]
    assert sorted(negative[4]) == [0, 1, 2]


def test_triplet_loss_all_embeddings_are_same() -> None:
    embeddings = torch.zeros(3, 2, 15, 15)

    points_per_batch = 10
    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor([0.0, 1.0]) if x % 2 == 0 else torch.tensor([1.0, 0.0]),
                hit_y_x=[(x, x)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(3)
    ]
    loss, loss_per_sample, _, _ = triplet_loss(points, embeddings)
    assert abs(loss - 128) < EPS


def test_triplet_loss_all_embeddings_are_same_embedding_selection_options() -> None:
    embeddings = torch.zeros(3, 2, 15, 15)

    points_per_batch = 10
    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor([0.0, 1.0]) if x % 2 == 0 else torch.tensor([1.0, 0.0]),
                hit_y_x=[(x, x)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(3)
    ]
    loss, loss_per_sample, _, _ = triplet_loss(points, embeddings, average_embeddings_across_hits=True)
    assert abs(loss - 128) < EPS

    loss, loss_per_sample, _, _ = triplet_loss(points, embeddings, average_embeddings_across_hits=False)
    assert abs(loss - 128) < EPS


def test_triplet_loss_no_positive_match() -> None:
    batch_size = 1
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    points_per_batch = 4
    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor([0.0 + x, 1.0]),
                hit_y_x=[(x, x)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]
    loss, loss_per_sample, positive, negative = triplet_loss(points, embeddings, min_for_positive=1.0)
    assert abs(loss - 0) < EPS
    assert positive is None
    assert negative is None


def test_triplet_loss_no_negative_match() -> None:
    batch_size = 1
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    points_per_batch = 4
    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor([0.0 + x, 1.0]),
                hit_y_x=[(x, x)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]
    loss, loss_per_sample, positive, negative = triplet_loss(points, embeddings, max_for_negative=0)
    assert abs(loss - 0) < EPS
    assert positive is None
    assert negative is None


def test_triplet_loss_one_positive_one_negative() -> None:
    batch_size = 1
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    # first two items are close, last is further
    embeddings[0, 0, 0, 0] = 1.0
    embeddings[0, 1, 0, 0] = 0.0

    embeddings[0, 0, 1, 1] = 1.0
    embeddings[0, 1, 1, 1] = 0.1

    embeddings[0, 0, 2, 2] = 3.0
    embeddings[0, 1, 2, 2] = 0.0

    points_per_batch = 3

    # First two items will be positive match, third item will be negative match
    comparison_embeddings = [[1.0, 0.0], [2.0, 0.0], [0.0, 3.0]]
    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor(comparison_embeddings[x]),
                hit_y_x=[(x, x)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]
    loss, loss_per_sample, positive, negative = triplet_loss(points, embeddings, margin=1)
    assert abs(loss - 0) < EPS
    assert positive is not None
    assert negative is not None
    assert abs(positive.mean() - 0.1) < EPS
    assert abs(negative.mean() - ((math.sqrt(0 ** 2 + 2 ** 2) + math.sqrt(0.1 ** 2 + 2 ** 2)) / 2)) < EPS, negative


def test_triplet_loss_works_with_options() -> None:
    batch_size = 1
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    # first two items are close, last is further
    embeddings[0, 0, 0, 0] = 1.0
    embeddings[0, 1, 0, 0] = 0.0

    embeddings[0, 0, 1, 1] = 1.0
    embeddings[0, 1, 1, 1] = 0.1

    embeddings[0, 0, 2, 2] = 3.0
    embeddings[0, 1, 2, 2] = 0.0

    points_per_batch = 3

    # First two items will be positive match, third item will be negative match
    comparison_embeddings = [[1.0, 0.0], [2.0, 0.0], [0.0, 3.0]]
    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor(comparison_embeddings[x]),
                hit_y_x=[(x, x)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]
    loss, loss_per_sample, positive, negative = triplet_loss(points, embeddings, margin=1, random_neighbor=True)
    assert abs(loss - 0) < EPS
    assert positive is not None
    assert negative is not None
    assert abs(positive.mean() - 0.1) < EPS
    assert abs(negative.mean() - ((math.sqrt(0 ** 2 + 2 ** 2) + math.sqrt(0.1 ** 2 + 2 ** 2)) / 2)) < EPS, negative

    loss, loss_per_sample, positive, negative = triplet_loss(points, embeddings, margin=1, random_neighbor=False)
    assert abs(loss - 0) < EPS
    assert positive is not None
    assert negative is not None
    assert abs(positive.mean() - 0.1) < EPS
    assert abs(negative.mean() - ((math.sqrt(0 ** 2 + 2 ** 2) + math.sqrt(0.1 ** 2 + 2 ** 2)) / 2)) < EPS, negative


def test_embeddings_loss_two_similar_comparison_similar_embedding() -> None:
    batch_size = 2
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    embeddings[0, 0, 0, 0] = 1.0
    embeddings[0, 1, 0, 0] = 0.0

    embeddings[1, 0, 0, 0] = 1.0
    embeddings[1, 1, 0, 0] = 0.0

    points_per_batch = 1

    comparison_embeddings = [[1.0, 0.0], [2.0, 0.0]]

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor(comparison_embeddings[x]),
                hit_y_x=[(0, 0)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]

    (
        loss_per_batch,
        _,
        positive_distances,
        negative_distances,
        positive_comparison,
        negative_comparison,
    ) = embeddings_loss(points, embeddings)

    assert negative_distances is None
    assert positive_distances is not None
    assert len(positive_distances) == 1
    assert negative_comparison is None
    assert positive_comparison is not None
    assert len(positive_comparison) == 1
    assert abs(loss_per_batch) < EPS


def test_embeddings_loss_two_similar_comparison_different_embedding() -> None:
    batch_size = 2
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    embeddings[0, 0, 0, 0] = 1.0
    embeddings[0, 1, 0, 0] = 0.0

    embeddings[1, 0, 0, 0] = 1.0
    embeddings[1, 1, 0, 0] = 1.0

    points_per_batch = 1

    comparison_embeddings = [[1.0, 0.0], [2.0, 0.0]]  # , [0.0, 3.0]]

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor(comparison_embeddings[x]),
                hit_y_x=[(0, 0)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]

    (
        loss_per_batch,
        _,
        positive_distances,
        negative_distances,
        positive_comparison,
        negative_comparison,
    ) = embeddings_loss(points, embeddings)
    assert negative_distances is None
    assert positive_distances is not None
    assert len(positive_distances) == 1
    assert negative_comparison is None
    assert positive_comparison is not None
    assert len(positive_comparison) == 1
    assert abs(loss_per_batch - 1) < 1e-5


def test_embeddings_loss_two_different_comparison_similar_embedding() -> None:
    batch_size = 2
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    embeddings[0, 0, 0, 0] = 1.0
    embeddings[0, 1, 0, 0] = 0.0

    embeddings[1, 0, 0, 0] = 1.0
    embeddings[1, 1, 0, 0] = 1.0

    points_per_batch = 1

    comparison_embeddings = [[1.0, 0.0], [0.0, 3.0]]

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor(comparison_embeddings[y]),
                hit_y_x=[(0, 0)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]

    (
        loss_per_batch,
        _,
        positive_distances,
        negative_distances,
        positive_comparison,
        negative_comparison,
    ) = embeddings_loss(points, embeddings, margin=10)
    assert negative_distances is not None
    assert len(negative_distances) == 1
    assert positive_distances is None
    assert negative_comparison is not None
    assert len(negative_comparison) == 1
    assert positive_comparison is None
    assert abs(loss_per_batch - 81) < 1e-3


def test_embeddings_loss_two_different_comparison_different_embedding() -> None:
    batch_size = 2
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    embeddings[0, 0, 0, 0] = 1.0
    embeddings[0, 1, 0, 0] = 0.0

    embeddings[1, 0, 0, 0] = 3.0
    embeddings[1, 1, 0, 0] = 0.0

    points_per_batch = 1

    comparison_embeddings = [[1.0, 0.0], [0.0, 3.0]]

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor(comparison_embeddings[y]),
                hit_y_x=[(0, 0)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]

    (
        loss_per_batch,
        _,
        positive_distances,
        negative_distances,
        positive_comparison,
        negative_comparison,
    ) = embeddings_loss(points, embeddings, margin=10)
    assert negative_distances is not None
    assert len(negative_distances) == 1
    assert positive_distances is None
    assert negative_comparison is not None
    assert len(negative_comparison) == 1
    assert positive_comparison is None
    assert abs(loss_per_batch - 64) < 1e-3


def test_embeddings_loss_no_eligible_points() -> None:
    batch_size = 2
    points_per_batch = 1

    # No points are eligible because there are no comparison points
    points = [
        [
            Point(x=0.0, y=10, r=10, score=10, hit_y_x=[(0, 0)], clz="Crop", confidence=10, hit_clz=HitClass.WEED,)
            for _ in range(points_per_batch)
        ]
        for _ in range(batch_size)
    ]

    (
        loss_per_batch,
        loss_per_sample,
        positive_distances,
        negative_distances,
        positive_comparison,
        negative_comparison,
    ) = embeddings_loss(points, torch.zeros((batch_size, 2, 4, 4)))
    assert negative_distances is None
    assert positive_distances is None
    assert negative_comparison is None
    assert positive_comparison is None
    assert abs(loss_per_batch) < EPS
    assert torch.sum(torch.abs(loss_per_sample)) < EPS


def test_embeddings_loss_two_different_comparison_different_embedding_average_embedding_options() -> None:
    batch_size = 2
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    embeddings[0, 0, 0, 0] = 1.0
    embeddings[0, 1, 0, 0] = 0.0

    embeddings[1, 0, 0, 0] = 3.0
    embeddings[1, 1, 0, 0] = 0.0

    points_per_batch = 1

    comparison_embeddings = [[1.0, 0.0], [0.0, 3.0]]

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor(comparison_embeddings[y]),
                hit_y_x=[(0, 0)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]

    (
        loss_per_batch,
        _,
        positive_distances,
        negative_distances,
        positive_comparison,
        negative_comparison,
    ) = embeddings_loss(points, embeddings, margin=10, average_embeddings_across_hits=True)
    assert negative_distances is not None
    assert len(negative_distances) == 1
    assert positive_distances is None
    assert negative_comparison is not None
    assert len(negative_comparison) == 1
    assert positive_comparison is None
    assert abs(loss_per_batch - 64) < 1e-3

    (
        loss_per_batch,
        _,
        positive_distances,
        negative_distances,
        positive_comparison,
        negative_comparison,
    ) = embeddings_loss(points, embeddings, margin=10, average_embeddings_across_hits=False)
    assert negative_distances is not None
    assert len(negative_distances) == 1
    assert positive_distances is None
    assert negative_comparison is not None
    assert len(negative_comparison) == 1
    assert positive_comparison is None
    assert abs(loss_per_batch - 64) < 1e-3


def test_get_random_embedding() -> None:
    embeddings = torch.ones((3, 2, 4, 4))
    for i in range(0, 4):
        embeddings[:, :, i, :] = i
        embeddings[:, :, :, i] = i
    embeddings[1] = 2 * embeddings[1]
    embeddings[2] = 3 * embeddings[2]

    embeddings = embeddings.to(torch.uint8)

    expected_1 = torch.tensor([1, 1])
    random_1 = get_random_embedding(embeddings=embeddings, batch_ind=0, hits_y_x=[(1, 0)])
    assert torch.equal(random_1, expected_1)

    random.seed(0)
    random_2 = get_random_embedding(embeddings=embeddings, batch_ind=2, hits_y_x=[(1, 1), (2, 1), (3, 1)])
    assert torch.equal(random_2, torch.tensor([6, 6]))


def test_get_average_embedding() -> None:
    embeddings = torch.ones((3, 2, 4, 4))
    for i in range(0, 4):
        embeddings[:, :, i, :] = i
        embeddings[:, :, :, i] = i
    embeddings[1] = 2 * embeddings[1]
    embeddings[2] = 4 * embeddings[2]

    expected_1 = torch.tensor([1, 1])
    random_1 = get_average_embedding(embeddings=embeddings, batch_ind=0, hits_y_x=[(1, 0)])
    assert torch.equal(random_1, expected_1)

    random_2 = get_average_embedding(embeddings=embeddings, batch_ind=2, hits_y_x=[(1, 1), (2, 1), (3, 1)])
    assert torch.equal(random_2, torch.tensor([8, 8]))


def test_embeddings_loss_two_similar_comparison_similar_embedding_comparison() -> None:
    batch_size = 1
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    embeddings[0, 0, 0, 0] = 1.0
    embeddings[0, 1, 0, 0] = 0.0

    embeddings[0, 0, 0, 1] = 20.0
    embeddings[0, 1, 0, 1] = 0.0

    points_per_batch = 2

    comparison_embeddings = [[1.0, 0.0], [2.0, 0.0]]

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor(comparison_embeddings[x]),
                hit_y_x=[[(0, 0), (0, 1)][x]],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]

    (
        loss_per_batch,
        _,
        positive_distances,
        negative_distances,
        positive_comparison,
        negative_comparison,
    ) = embeddings_loss(points, embeddings, use_comparison_similarity_loss=True)

    assert negative_distances is None
    assert positive_distances is not None
    assert len(positive_distances) == 1
    assert negative_comparison is None
    assert positive_comparison is not None
    assert len(positive_comparison) == 1
    assert abs(loss_per_batch) < EPS


def test_embeddings_loss_two_different_comparison_different_embedding_comparison() -> None:
    batch_size = 2
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    embeddings[0, 0, 0, 0] = 1.0
    embeddings[0, 1, 0, 0] = 0.0

    embeddings[1, 0, 0, 0] = 20.0
    embeddings[1, 1, 0, 0] = 0.0

    points_per_batch = 1

    comparison_embeddings = [[1.0, 0.0], [0.0, 3.0]]

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor(comparison_embeddings[y]),
                hit_y_x=[(0, 0)],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]

    (
        loss_per_batch,
        _,
        positive_distances,
        negative_distances,
        positive_comparison,
        negative_comparison,
    ) = embeddings_loss(points, embeddings, margin=10, use_comparison_similarity_loss=True)
    assert negative_distances is not None
    assert len(negative_distances) == 1
    assert positive_distances is None
    assert negative_comparison is not None
    assert len(negative_comparison) == 1
    assert positive_comparison is None
    assert abs(loss_per_batch - 1) < 1e-3


def test_embeddings_loss_two_similar_comparison_similar_embedding_comparison_3() -> None:
    # Two batch items
    # Two pairs of points per batch
    #
    batch_size = 1
    embeddings = torch.zeros(batch_size, 2, 4, 4)

    embeddings[0, 0, 0, 0] = 1.0
    embeddings[0, 1, 0, 0] = 0.0

    embeddings[0, 0, 1, 0] = 20.0
    embeddings[0, 1, 1, 0] = 0.0

    embeddings[0, 0, 0, 1] = 1.0
    embeddings[0, 1, 0, 1] = 1.0

    points_per_batch = 3

    comparison_embeddings = [[1.0, 0.0], [2.0, 0.0], [0.0, 30.0]]

    points = [
        [
            Point(
                x=0.0,
                y=10,
                r=10,
                score=10,
                comparison_embedding=torch.tensor(comparison_embeddings[x]),
                hit_y_x=[[(0, 0), (1, 0), (0, 1)][x]],
                clz="Crop",
                confidence=10,
                hit_clz=HitClass.WEED,
            )
            for x in range(points_per_batch)
        ]
        for y in range(batch_size)
    ]

    (
        loss_per_batch,
        _,
        positive_distances,
        negative_distances,
        positive_comparison,
        negative_comparison,
    ) = embeddings_loss(points, embeddings, use_comparison_similarity_loss=True)

    assert negative_distances is not None
    assert len(negative_distances) == 2
    assert positive_distances is not None
    assert len(positive_distances) == 1
    assert negative_comparison is not None
    assert len(negative_comparison) == 2
    assert positive_comparison is not None
    assert len(positive_comparison) == 1
    assert abs(loss_per_batch - 1 / 3) < EPS


def test_embeddings_loss_with_overlap_check_multiple_batch() -> None:
    batch_size_list = [1, 2]
    compute_whole_batch_list = [False]

    for batch_size in batch_size_list:
        for compute_whole_batch in compute_whole_batch_list:
            embeddings = torch.zeros(batch_size, 2, 4, 4)  # [batch_size, embedding_dim, hit_y, hit_x]
            embedding_values: List[List[Any]] = []
            for b in range(batch_size):
                embedding_values.extend(
                    [
                        [(b, 0, 0, 0), 1.0],
                        [(b, 1, 0, 0), 0.0],
                        [(b, 0, 0, 1), 2.0],
                        [(b, 1, 0, 1), 0.0],
                        [(b, 0, 1, 0), 0.0],
                        [(b, 1, 1, 0), 3.0],
                        [(b, 0, 1, 1), 1.0],
                        [(b, 1, 1, 1), 0.0],
                        [(b, 0, 2, 0), 30.0],
                        [(b, 1, 2, 0), 0.0],
                        [(b, 0, 2, 1), 0.0],
                        [(b, 1, 2, 1), 4.0],
                    ]
                )
            for (b, c, h, w), value in embedding_values:
                embeddings[b, c, h, w] = value

            comparison_embeddings = [[1.0, 0.0], [2.0, 0.0], [0.0, 30.0], [1.0, 0.0], [2.0, 0.0], [0.0, 30.0]]

            points = [
                [
                    Point(
                        x=coords[0],
                        y=coords[1],
                        r=10,
                        score=10,
                        comparison_embedding=torch.tensor(comparison_embeddings[i]),
                        hit_y_x=[[(0, 0), (0, 1), (1, 0), (1, 1), (2, 0), (2, 1)][i]],
                        clz="Crop",
                        confidence=10,
                        hit_clz=HitClass.WEED,
                    )
                    for i, coords in enumerate([(0, 0), (100, 100), (400, 400), (500, 500), (800, 800), (1100, 1100)])
                ]
                for _ in range(batch_size)
            ]  # two_points_overlap_with_each_other_pairs: 2, with_overlap: 4, without_overlap: 2.

            # len(negative_distances) + len(positive_distances) should be equal to 15
            # two_points_overlap_with_each_other_pairs == 2
            # with_overlap_and_with_overlap, not two points overlap with each other == 4
            # with_overlap_and_without_overlap == 8
            # without_overlap_and_without_overlap == 1

            (
                loss_per_batch,
                _,
                positive_distances,
                negative_distances,
                positive_comparison,
                negative_comparison,
            ) = embeddings_loss(
                points,
                embeddings,
                max_items_to_compare=32768,  # Increasing this number ensures that we capture all possible combinations for batch_points_for_comparison.
                compare_whole_batch=compute_whole_batch,
                use_comparison_similarity_loss=True,
                use_points_with_overlap=True,
            )

            assert negative_distances is not None
            assert len(negative_distances) == 8 * batch_size
            assert positive_distances is not None
            assert len(positive_distances) == 7 * batch_size
            assert negative_comparison is not None
            assert len(negative_comparison) == 8 * batch_size
            assert positive_comparison is not None
            assert len(positive_comparison) == 7 * batch_size
            assert abs(loss_per_batch) < 1e-03


def test_embeddings_loss_with_overlap_check_compute_whole_batch() -> None:
    batch_size_list = [2]
    compute_whole_batch_list = [True]

    for batch_size in batch_size_list:
        for compute_whole_batch in compute_whole_batch_list:
            embeddings = torch.zeros(batch_size, 2, 4, 4)  # [batch_size, embedding_dim, hit_y, hit_x]
            embedding_values: List[List[Any]] = []
            for b in range(batch_size):
                embedding_values.extend(
                    [
                        [(b, 0, 0, 0), 1.0],
                        [(b, 1, 0, 0), 0.0],
                        [(b, 0, 0, 1), 2.0],
                        [(b, 1, 0, 1), 0.0],
                        [(b, 0, 1, 0), 0.0],
                        [(b, 1, 1, 0), 3.0],
                        [(b, 0, 1, 1), 1.0],
                        [(b, 1, 1, 1), 0.0],
                        [(b, 0, 2, 0), 30.0],
                        [(b, 1, 2, 0), 0.0],
                        [(b, 0, 2, 1), 0.0],
                        [(b, 1, 2, 1), 4.0],
                    ]
                )
            for (b, c, h, w), value in embedding_values:
                embeddings[b, c, h, w] = value

            comparison_embeddings = [[1.0, 0.0], [2.0, 0.0], [0.0, 30.0], [1.0, 0.0], [2.0, 0.0], [0.0, 30.0]]

            points = [
                [
                    Point(
                        x=coords[0],
                        y=coords[1],
                        r=10,
                        score=10,
                        comparison_embedding=torch.tensor(comparison_embeddings[i]),
                        hit_y_x=[[(0, 0), (0, 1), (1, 0), (1, 1), (2, 0), (2, 1)][i]],
                        clz="Crop",
                        confidence=10,
                        hit_clz=HitClass.WEED,
                    )
                    for i, coords in enumerate([(0, 0), (100, 100), (400, 400), (500, 500), (800, 800), (1100, 1100)])
                ]
                for _ in range(batch_size)
            ]  # two_points_overlap_with_each_other_pairs: 2, with_overlap: 4, without_overlap: 2.

            # len(negative_distances) + len(positive_distances) should be equal to 66
            # two_points_overlap_with_each_other_pairs == 4
            # with_overlap_and_with_overlap, not two points overlap with each other == 24
            # with_overlap_and_without_overlap == 32
            # without_overlap_and_without_overlap == 6

            (
                loss_per_batch,
                _,
                positive_distances,
                negative_distances,
                positive_comparison,
                negative_comparison,
            ) = embeddings_loss(
                points,
                embeddings,
                max_items_to_compare=32768,  # Increasing this number ensures that we capture all possible combinations for batch_points_for_comparison.
                compare_whole_batch=compute_whole_batch,
                use_comparison_similarity_loss=True,
                use_points_with_overlap=True,
            )

            assert negative_distances is not None
            assert len(negative_distances) == 32
            assert positive_distances is not None
            assert len(positive_distances) == 34
            assert negative_comparison is not None
            assert len(negative_comparison) == 32
            assert positive_comparison is not None
            assert len(positive_comparison) == 34
            assert abs(loss_per_batch) < 1e-03

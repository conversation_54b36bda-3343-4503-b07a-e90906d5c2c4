from collections import Counter
from typing import Any, Dict, List, Tuple

from deeplearning.deepweed.dataset_utils import EmbeddingSampler, get_embedding_index


def test_get_embedding_index() -> None:
    tests: List[Dict[str, Any]] = [
        {"x": 0.51, "y": 0.76, "x_buckets": [10], "y_buckets": [10], "expected": (75,)},
        {"x": 0, "y": 0, "x_buckets": [10], "y_buckets": [10], "expected": (0,)},
        {"x": 0.51, "y": 0.76, "x_buckets": [5], "y_buckets": [10], "expected": (37,)},
        {"x": 0.51, "y": 0.76, "x_buckets": [20], "y_buckets": [20], "expected": (310,)},
        {"x": 0.61, "y": 1.00, "x_buckets": [10], "y_buckets": [10], "expected": (96,)},
        {"x": 1.00, "y": 1.00, "x_buckets": [10], "y_buckets": [10], "expected": (99,)},
        {"x": 0.5433, "y": 0.748, "x_buckets": [10], "y_buckets": [10], "expected": (75,)},
        {"x": 0.5433, "y": 0.748, "x_buckets": [10, 10], "y_buckets": [10, 10], "expected": (75, 44)},
        {"x": 0.5433, "y": 0.748, "x_buckets": [10, 5], "y_buckets": [10, 5], "expected": (75, 12)},
        {"x": 0.425, "y": 0.214, "x_buckets": [5, 10], "y_buckets": [10, 5], "expected": (12, 1)},
        {
            "x": 0.5228899756197,
            "y": 0.0154288662672,
            "x_buckets": [10, 10, 5],
            "y_buckets": [10, 10, 5],
            "expected": (5, 12, 11),
        },
    ]
    for test_obj in tests:
        assert (
            get_embedding_index(
                x=test_obj["x"], y=test_obj["y"], x_buckets=test_obj["x_buckets"], y_buckets=test_obj["y_buckets"]
            )
            == test_obj["expected"]
        )


def test_embedding_bucket_dict_structure() -> None:
    embedding_sampling_dict = EmbeddingSampler(goal_percentage_emphasized=None)
    test_basic: List[Dict[str, Any]] = [
        {"embedding_bucket": (1, 10), "row_index": 1, "category": "b", "is_emphasized": True},
        {"embedding_bucket": (1, 10), "row_index": 2, "category": "a", "is_emphasized": True},
        {"embedding_bucket": (1, 10), "row_index": 3, "category": "b", "is_emphasized": False},
        {"embedding_bucket": (1, 10), "row_index": 4, "category": "a", "is_emphasized": False},
        {"embedding_bucket": (2, 11), "row_index": 5, "category": "b", "is_emphasized": True},
        {"embedding_bucket": (2, 11), "row_index": 6, "category": "a", "is_emphasized": True},
        {"embedding_bucket": (1, 10), "row_index": 7, "category": "b", "is_emphasized": False},
        {"embedding_bucket": (1, 10), "row_index": 8, "category": "a", "is_emphasized": False},
        {"embedding_bucket": (1, 11), "row_index": 9, "category": "b", "is_emphasized": True},
        {"embedding_bucket": (1, 11), "row_index": 10, "category": "a", "is_emphasized": True},
        {"embedding_bucket": (1, 11), "row_index": 10, "category": "a", "is_emphasized": True},
    ]
    for test in test_basic:
        embedding_sampling_dict.add(
            embedding_bucket_index=test["embedding_bucket"],
            row_index=test["row_index"],
            category=test["category"],
            is_emphasized=test["is_emphasized"],
            use_category_as_key=True,
        )
    embedding_sampling_dict.convert_items_container()

    expected_dict = {
        True: {
            "type": "emphasized",
            "value": {
                "B": {
                    "type": "category",
                    "value": {
                        1: {
                            "type": "embedding",
                            "value": {10: {"type": "embedding", "value": [1]}, 11: {"type": "embedding", "value": [9]}},
                        },
                        2: {"type": "embedding", "value": {11: {"type": "embedding", "value": [5]}}},
                    },
                },
                "A": {
                    "type": "category",
                    "value": {
                        1: {
                            "type": "embedding",
                            "value": {
                                10: {"type": "embedding", "value": [2]},
                                11: {"type": "embedding", "value": [10]},
                            },
                        },
                        2: {"type": "embedding", "value": {11: {"type": "embedding", "value": [6]}}},
                    },
                },
            },
        },
        False: {
            "type": "emphasized",
            "value": {
                "B": {
                    "type": "category",
                    "value": {1: {"type": "embedding", "value": {10: {"type": "embedding", "value": [3, 7]}}}},
                },
                "A": {
                    "type": "category",
                    "value": {1: {"type": "embedding", "value": {10: {"type": "embedding", "value": [4, 8]}}}},
                },
            },
        },
    }

    def compare_nested_dicts(d1: Dict[Any, Any], d2: Dict[Any, Any]) -> bool:
        if isinstance(d1, dict) and isinstance(d2, dict):
            if d1.keys() != d2.keys():
                return False
            for key in d1:
                if not compare_nested_dicts(d1[key], d2[key]):
                    return False
            return True
        elif isinstance(d1, list) and isinstance(d2, list):
            return set(d1) == set(d2)
        else:
            return d1 == d2

    # The last items (value) is not necessary in the same order.
    assert compare_nested_dicts(d1=embedding_sampling_dict._embedding_bucket_dict, d2=expected_dict)

    expected_index_embedding_categories_dict = {
        1: {(1, 10): ["B"]},
        2: {(1, 10): ["A"]},
        3: {(1, 10): ["B"]},
        4: {(1, 10): ["A"]},
        5: {(2, 11): ["B"]},
        6: {(2, 11): ["A"]},
        7: {(1, 10): ["B"]},
        8: {(1, 10): ["A"]},
        9: {(1, 11): ["B"]},
        10: {(1, 11): ["A"]},
    }

    assert (
        embedding_sampling_dict._metadata_index_embedding_indices_to_categories
        == expected_index_embedding_categories_dict
    )


def test_embedding_bucket_dict_hierarchical_embedding_sampling() -> None:
    embedding_sampling_dict = EmbeddingSampler(goal_percentage_emphasized=None, seed=10)
    test_basic_sampling: List[Dict[str, Any]] = [
        {"embedding_bucket": (1, 10), "row_index": 1, "category": "a", "is_emphasized": None},
        {"embedding_bucket": (1, 10), "row_index": 2, "category": "a", "is_emphasized": None},
        {"embedding_bucket": (1, 10), "row_index": 3, "category": "a", "is_emphasized": None},
        {"embedding_bucket": (1, 10), "row_index": 4, "category": "a", "is_emphasized": None},
        {"embedding_bucket": (2, 11), "row_index": 5, "category": "a", "is_emphasized": None},
        {"embedding_bucket": (2, 11), "row_index": 6, "category": "a", "is_emphasized": None},
        {"embedding_bucket": (1, 10), "row_index": 7, "category": "b", "is_emphasized": None},
        {"embedding_bucket": (1, 10), "row_index": 8, "category": "b", "is_emphasized": None},
        {"embedding_bucket": (1, 11), "row_index": 9, "category": "b", "is_emphasized": None},
        {"embedding_bucket": (1, 11), "row_index": 10, "category": "b", "is_emphasized": None},
        {"embedding_bucket": (1, 11), "row_index": 10, "category": "b", "is_emphasized": None},
        {"embedding_bucket": (1, 11), "row_index": 10, "category": "a", "is_emphasized": None},
    ]
    for test in test_basic_sampling:
        embedding_sampling_dict.add(
            embedding_bucket_index=test["embedding_bucket"],
            row_index=test["row_index"],
            category=test["category"],
            is_emphasized=test["is_emphasized"],
            use_category_as_key=False,
        )
    embedding_sampling_dict.convert_items_container()

    indices: Dict[int, int] = Counter()
    chosen_buckets: Dict[Tuple[int, ...], int] = Counter()

    total_count = int(2e4)

    for _ in range(total_count):
        index, chosen_bucket, category = embedding_sampling_dict.sample()
        if index <= 6:
            assert category == "A"
        elif index <= 9:
            assert category == "B"
        else:
            assert category in ["A", "B"]
        indices[index] += 1
        chosen_buckets[chosen_bucket] += 1

    assert 0.24 < chosen_buckets[(1, 10)] / total_count < 0.26
    assert 0.24 < chosen_buckets[(1, 11)] / total_count < 0.26
    assert 0.49 < chosen_buckets[(2, 11)] / total_count < 0.51


def test_embedding_bucket_dict_single_level_embedding_sampling() -> None:
    embedding_sampling_dict = EmbeddingSampler(goal_percentage_emphasized=None, seed=10)
    test_basic_sampling: List[Dict[str, Any]] = [
        {"embedding_bucket": (1,), "row_index": 1, "category": "a", "is_emphasized": None},
        {"embedding_bucket": (1,), "row_index": 2, "category": "a", "is_emphasized": None},
        {"embedding_bucket": (1,), "row_index": 3, "category": "a", "is_emphasized": None},
        {"embedding_bucket": (1,), "row_index": 4, "category": "a", "is_emphasized": None},
        {"embedding_bucket": (2,), "row_index": 5, "category": "a", "is_emphasized": None},
        {"embedding_bucket": (2,), "row_index": 6, "category": "b", "is_emphasized": None},
        {"embedding_bucket": (1,), "row_index": 7, "category": "b", "is_emphasized": None},
        {"embedding_bucket": (1,), "row_index": 8, "category": "b", "is_emphasized": None},
        {"embedding_bucket": (1,), "row_index": 9, "category": "b", "is_emphasized": None},
        {"embedding_bucket": (1,), "row_index": 10, "category": "b", "is_emphasized": None},
    ]
    for test in test_basic_sampling:
        embedding_sampling_dict.add(
            embedding_bucket_index=test["embedding_bucket"],
            row_index=test["row_index"],
            category=test["category"],
            is_emphasized=test["is_emphasized"],
            use_category_as_key=False,
        )
    embedding_sampling_dict.convert_items_container()

    indices: Dict[int, int] = Counter()
    chosen_buckets: Dict[Tuple[int, ...], int] = Counter()

    total_count = int(1e4)

    for _ in range(total_count):
        index, chosen_bucket, category = embedding_sampling_dict.sample()
        indices[index] += 1
        chosen_buckets[chosen_bucket] += 1

    assert 0.49 < chosen_buckets[(1,)] / total_count < 0.51
    assert 0.49 < chosen_buckets[(2,)] / total_count < 0.51

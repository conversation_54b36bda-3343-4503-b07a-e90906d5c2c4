from typing import Any, Dict, List

from deeplearning.deepweed.deployment import compensation_check


def test_compensation_check() -> None:
    tests: List[Dict[str, Any]] = [
        {"new_delta": 1, "old_delta": 1, "expected": True},
        {"new_delta": 1, "old_delta": -1, "expected": True},
        {"new_delta": 0.5, "old_delta": -1, "expected": False},
        {"new_delta": -1, "old_delta": 1, "expected": False},
        {"new_delta": -1, "old_delta": -1, "expected": False},
    ]

    for test in tests:
        actual = compensation_check(test["new_delta"], test["old_delta"])
        assert (
            actual == test["expected"]
        ), f"new_delta: {test['new_delta']}, old_delta: {test['old_delta']}, expected value: {test['expected']}"

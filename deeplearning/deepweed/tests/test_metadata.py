import uuid

import pytest


@pytest.mark.dl_unit_test
def test_check_point_from_json_picks_right_uuid() -> None:
    from deeplearning.deepweed.metadata import Point

    uuid_1 = str(uuid.uuid4())
    uuid_2 = "1234"

    image_id = str(uuid.uuid4())

    item_dict_1 = {"x": 10, "y": 12, "radius": 12.3, "class": "CLASS_A", "id": uuid_1, "image_id": image_id}

    item_dict_2 = {"x": 10, "y": 12, "radius": 12.3, "class": "CLASS_A", "id": uuid_2, "image_id": image_id}

    point_a = Point.from_json(item_dict_1)
    point_b = Point.from_json(item_dict_2)

    assert point_a.uuid == uuid_1
    assert point_b.uuid == f"{image_id}-{uuid_2}"

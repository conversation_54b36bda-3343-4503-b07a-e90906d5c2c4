import deeplearning.deepweed.transforms as T
from deeplearning.utils.images import IMAGENET_MEANS, IMAGENET_STDS


def training_transforms(
    height: int, width: int, dilate_mask_iterations: int, discard_points_border_px: int, random_rotation: bool = True
) -> T.Compose:

    transforms_list = [
        T.TransposeAlignLongerSide(want_longer_width=False),
        T.<PERSON>hoice(
            [
                T.ColorJitter(brightness=(0.5, 1.5), saturation=(0.75, 5.0), hue=(0, 0), gamma=(0.75, 1.33)),
                T<PERSON>(stddev=0.03),
                T.RandomVerticalFlip(p=1.0),
                T.RandomHorizontalFlip(p=1.0),
                T.<PERSON>([T.RandomVerticalFlip(p=1.0), T.RandomHorizontalFlip(p=1.0)]),
                T<PERSON><PERSON>(kernel_size=3, stddev=(0.1, 1.0)),
            ]
        ),
    ]

    if random_rotation:
        transforms_list.append(
            T.RandomRotation(min_scale=0.75, max_scale=1.5, discard_points_border_px=discard_points_border_px)
        )

    transforms_list += [
        T.RandomCrop(size=(height, width), discard_points_border_px=discard_points_border_px),
        T.Normalize(mean=IMAGENET_MEANS, std=IMAGENET_STDS),
        T.DilateMask(iterations=dilate_mask_iterations),
    ]

    transforms = T.Compose(transforms_list)

    return transforms


def evaluation_transforms(dilate_mask: int) -> T.Compose:

    transforms = [
        T.TransposeAlignLongerSide(want_longer_width=False),
        T.Normalize(mean=IMAGENET_MEANS, std=IMAGENET_STDS),
        T.DilateMask(iterations=dilate_mask),
    ]

    transforms = T.Compose(transforms)

    return transforms


def calibration_transforms(height: int, width: int, dilate_mask: int, discard_points_border_px: int) -> T.Compose:

    transforms = [
        T.TransposeAlignLongerSide(want_longer_width=False),
        T.ColorJitter(brightness=(0.75, 1.33), saturation=(1.0, 2.0), hue=(0, 0), gamma=(0.75, 1.33)),
        T.GaussianNoise(stddev=0.03),
        T.RandomCrop(size=(height, width), discard_points_border_px=discard_points_border_px),
        T.Normalize(mean=IMAGENET_MEANS, std=IMAGENET_STDS),
        T.DilateMask(iterations=dilate_mask),
    ]

    transforms = T.Compose(transforms)

    return transforms

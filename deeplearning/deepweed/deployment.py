import argparse
import datetime
from typing import List, <PERSON>tional, <PERSON><PERSON>

from sqlalchemy.orm import Session

from deeplearning import dl_metrics
from deeplearning.deepweed.points_db_utils import get_points_db_filenames
from deeplearning.dl_metrics import get_session, nan_divide
from deeplearning.scripts.utils.training_info import TrainingInfo


def get_crops_and_weeds_shot(
    session: Session,
    recommended_wpt: Optional[float] = None,
    recommended_cpt: Optional[float] = None,
    data_to_exclude: Optional[List[str]] = None,
    data_to_include: Optional[List[str]] = None,
) -> Tuple[float, float]:
    data_to_exclude = data_to_exclude if data_to_exclude is not None else []
    data_to_include = data_to_include if data_to_include is not None else []
    wpt = recommended_wpt if recommended_wpt is not None else 0.5
    cpt = recommended_cpt if recommended_cpt is not None else 0.5

    min_date = datetime.date(year=2000, month=1, day=1)
    tomorrow_date = datetime.datetime.now() + datetime.timedelta(1)

    crops_targeted_function = dl_metrics.CropsTargeted(wpt=wpt, cpt=cpt,).filter(
        filepaths_to_exclude=set(data_to_exclude),
        filepaths_to_include=set(data_to_include),
        date_range=(min_date, tomorrow_date),
    )
    weeds_targeted_function = dl_metrics.WeedsTargeted(wpt=wpt, cpt=cpt,).filter(
        filepaths_to_exclude=set(data_to_exclude),
        filepaths_to_include=set(data_to_include),
        date_range=(min_date, tomorrow_date),
    )

    crops_targeted = crops_targeted_function(session)
    weeds_targeted = weeds_targeted_function(session)
    crops_targeted_percent = nan_divide(crops_targeted["numerator"].sum(), crops_targeted["denominator"].sum())
    weeds_targeted_percent = nan_divide(weeds_targeted["numerator"].sum(), weeds_targeted["denominator"].sum())

    return (crops_targeted_percent, weeds_targeted_percent)


def compensation_check(weeds_shot_new_data_delta: float, weeds_shot_old_data_delta: float) -> bool:
    return weeds_shot_new_data_delta > 0 and weeds_shot_new_data_delta + weeds_shot_old_data_delta >= 0


def comparison_deployment_check(
    weeds_shot_parent_model_old_data: float,
    weeds_shot_current_model_old_data: float,
    target_weeds_shot_buffer: float = 2.0,  # 2% weeds shot dip allowed
) -> bool:
    return bool(weeds_shot_current_model_old_data >= (weeds_shot_parent_model_old_data - target_weeds_shot_buffer))


def old_and_new_data_delta(
    weeds_shot_parent_model_old_data: float,
    weeds_shot_current_model_old_data: float,
    weeds_shot_parent_model_new_data: float,
    weeds_shot_current_model_new_data: float,
) -> Tuple[float, float]:
    old_data_delta = weeds_shot_current_model_old_data - weeds_shot_parent_model_old_data
    new_data_delta = weeds_shot_current_model_new_data - weeds_shot_parent_model_new_data

    return old_data_delta, new_data_delta


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", type=str)
    parser.add_argument("--comparison-model-id", type=str, default=None)

    args = parser.parse_args()

    training_info = TrainingInfo(args.model_id)

    db = dl_metrics.get_db_from_directory(f"/data/deeplearning/models/{args.model_id}/test_dataframes")
    session = get_session(db)

    crops_shot, weeds_shot = get_crops_and_weeds_shot(
        session,
        recommended_wpt=training_info.recommended_wpt,
        recommended_cpt=training_info.recommended_cpt,
        data_to_exclude=[],
    )
    print(f"Model: {args.model_id}, crops_shot={crops_shot}, weeds_shot={weeds_shot}")

    if args.comparison_model_id is not None:
        training_info_comparison = TrainingInfo(args.comparison_model_id)

        first_model_files = get_points_db_filenames(
            f"/data/deeplearning/models/{args.model_id}/test_dataframes/points_v2.db"
        )
        comparison_model_files = get_points_db_filenames(
            f"/data/deeplearning/models/{args.comparison_model_id}/test_dataframes/points_v2.db"
        )

        new_files = []
        old_files = []
        for file in comparison_model_files:
            if file not in first_model_files:
                new_files.append(file)
            else:
                old_files.append(file)

        db = dl_metrics.get_db_from_directory(f"/data/deeplearning/models/{args.comparison_model_id}/test_dataframes")
        session = get_session(db)

        crops_shot, weeds_shot = get_crops_and_weeds_shot(
            session,
            recommended_wpt=training_info_comparison.recommended_wpt,
            recommended_cpt=training_info_comparison.recommended_cpt,
            data_to_exclude=[],
        )
        print(
            f"    Comparing to all data on {args.comparison_model_id} ({len(new_files) + len(old_files)} total images): crops_shot={crops_shot}, weeds_shot={weeds_shot}"
        )
        crops_shot, weeds_shot = get_crops_and_weeds_shot(
            session,
            recommended_wpt=training_info_comparison.recommended_wpt,
            recommended_cpt=training_info_comparison.recommended_cpt,
            data_to_exclude=new_files,
        )
        print(
            f"    Comparing to similar data on {args.comparison_model_id} ({len(old_files)} old images): crops_shot={crops_shot}, weeds_shot={weeds_shot}"
        )
        crops_shot, weeds_shot = get_crops_and_weeds_shot(
            session,
            recommended_wpt=training_info_comparison.recommended_wpt,
            recommended_cpt=training_info_comparison.recommended_cpt,
            data_to_exclude=old_files,
        )
        print(
            f"    Comparing to new data on {args.comparison_model_id} ({len(new_files)} new images): crops_shot={crops_shot}, weeds_shot={weeds_shot}"
        )

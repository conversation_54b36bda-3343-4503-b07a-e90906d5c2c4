from typing import Any, Dict, List, Optional, Tuple, cast

import torch

from deeplearning.deepweed.metadata import ImageMetadata, Point
from deeplearning.utils.dataset import DatasetType
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass


class DatasetFileStructure:
    def __init__(self, current_dir: str):
        self.current_dir = current_dir
        self.filenames: List[str] = []
        self.subdirs: Dict[str, DatasetFileStructure] = {}


class DatasetLabel:
    def __init__(self, mask: torch.Tensor, points: List[List[Point]]) -> None:
        self.mask = mask
        self.points = points

    @staticmethod
    def stack(labels: List["DatasetLabel"], dim: int = 0) -> "DatasetLabel":
        points_list: List[List[Point]] = []
        mask_list: List[torch.Tensor] = []
        dimensions = labels[0].mask.dim()
        if dimensions == 2:
            points_list.append([])
        for label in labels:
            mask_list.append(label.mask)
            if dimensions == 2:
                points_list[0].extend(label.points[0])
            if dimensions >= 3:
                points_list.extend(label.points)

        if dimensions < 4:
            stacked_mask = torch.stack(mask_list, dim)
        else:
            stacked_mask = torch.cat(mask_list, dim)
        cat_labels = DatasetLabel(stacked_mask, points_list)

        return cat_labels

    def flip(self, dims: List[int]) -> "DatasetLabel":
        flipped = DatasetLabel(self.mask, [[] for _ in self.points])
        flipped.mask = flipped.mask.flip(dims=dims)
        flipped_mask_width = flipped.mask.shape[-1]
        flipped_mask_height = flipped.mask.shape[-2]
        for dim in dims:
            for b in range(len(flipped.points)):
                if dim == 2:
                    for i in range(len(self.points[b])):
                        point = self.points[b][i]
                        flipped.points[b].append(
                            Point(
                                point.x,
                                flipped_mask_height - 1 - point.y,
                                point.r,
                                point.score,
                                point.clz,
                                point.confidence,
                                point.hit_clz,
                                comparison_embedding=point.comparison_embedding,
                                relocation_info=point.relocation_info,
                                deepweed_embedding_bucket=point.deepweed_embedding_bucket,
                                uuid=point.uuid,
                            )
                        )
                elif dim == 3:
                    for i in range(len(self.points[b])):
                        point = self.points[b][i]
                        flipped.points[b].append(
                            Point(
                                flipped_mask_width - 1 - point.x,
                                point.y,
                                point.r,
                                point.score,
                                point.clz,
                                point.confidence,
                                point.hit_clz,
                                comparison_embedding=point.comparison_embedding,
                                relocation_info=point.relocation_info,
                                deepweed_embedding_bucket=point.deepweed_embedding_bucket,
                                uuid=point.uuid,
                            )
                        )
        return flipped

    def __getitem__(self, input: Any) -> "DatasetLabel":
        subset = DatasetLabel(self.mask, [[] for _ in self.points])
        if isinstance(input[-1], slice) and isinstance(input[-2], slice):
            input = cast(Tuple[Any, slice, slice], input)
            subset.mask = self.mask[..., input[-2], input[-1]]
            for b in range(len(self.points)):
                subset.points[b] = [
                    Point(
                        point.x - input[-1].start,
                        point.y - input[-2].start,
                        point.r,
                        point.score,
                        point.clz,
                        point.confidence,
                        point.hit_clz,
                        comparison_embedding=point.comparison_embedding,
                        relocation_info=point.relocation_info,
                        deepweed_embedding_bucket=point.deepweed_embedding_bucket,
                        uuid=point.uuid,
                    )
                    for point in self.points[b]
                    if point.x >= input[-1].start
                    and point.x < input[-1].stop
                    and point.y >= input[-2].start
                    and point.y < input[-2].stop
                ]
        else:
            raise NotImplementedError()
        return subset

    def cuda(self, gpu_id: int) -> "DatasetLabel":
        self.mask = self.mask.cuda(gpu_id)
        return self

    def pin_memory(self) -> "DatasetLabel":
        self.mask = self.mask.pin_memory()
        return self

    def fence(
        self,
        batch_enabled_weed_classes: torch.Tensor,
        batch_enabled_hits: torch.Tensor,
        category_to_index: Dict[str, int],
    ) -> "DatasetLabel":
        fenced = DatasetLabel(self.mask, [[] for _ in self.points])
        for b in range(len(self.points)):
            fenced.points[b] = [
                Point(
                    point.x,
                    point.y,
                    point.r,
                    point.score,
                    point.clz,
                    point.confidence,
                    point.hit_clz,
                    comparison_embedding=point.comparison_embedding,
                    relocation_info=point.relocation_info,
                    hit_y_x=point.hit_y_x,
                    deepweed_embedding_bucket=point.deepweed_embedding_bucket,
                    uuid=point.uuid,
                )
                for point in self.points[b]
                if batch_enabled_hits[b, point.hit_clz].item()
                and (
                    point.hit_clz == HitClass.CROP
                    or (
                        (
                            point.clz in category_to_index
                            and batch_enabled_weed_classes[b, category_to_index[point.clz]].item()
                        )
                        or point.clz not in category_to_index
                    )
                )
            ]
        return fenced


class DeepweedDatapoint:
    def __init__(
        self,
        image: torch.Tensor,
        target: Optional[DatasetLabel],
        target_list: Optional[List[Optional[torch.Tensor]]],
        points: List[Point],
        enabled_weed_point_classes: torch.Tensor,
        enabled_segm_classes: torch.Tensor,
        enabled_hits: torch.Tensor,
        filepath: str,
        image_meta: ImageMetadata,
        start_x: float = 0,
        start_y: float = 0,
        loss_multiplier: float = 1.0,
        new_datapoint: bool = False,
        dataset_type: DatasetType = DatasetType.TRAIN,
        enabled_embedding_bucket: Optional[int] = None,
        enabled_category: Optional[str] = None,
    ):

        self.image = image
        self.target = target
        self.target_list = target_list
        self.points = points
        self.enabled_weed_point_classes = enabled_weed_point_classes
        self.enabled_segm_classes = enabled_segm_classes
        self.enabled_hits = enabled_hits
        self.filepath = filepath
        self.image_meta = image_meta
        self.start_x = start_x
        self.start_y = start_y
        self.loss_multiplier = loss_multiplier
        self.new_datapoint = new_datapoint
        self.dataset_type = dataset_type
        self.enabled_embedding_bucket = enabled_embedding_bucket
        self.enabled_category = enabled_category

import random
from typing import Dict, Optional, cast

import cv2
import torch


def dice_loss(
    input: torch.Tensor,
    target: torch.Tensor,
    class_weights: torch.Tensor,
    smooth: float = 1.0,
    penalize_overprediction_p: float = 1.0,
    overprediction_forgiveness: float = 1.0,
) -> torch.Tensor:
    assert input.shape == target.shape, f"input={input.shape} != target={target.shape}"

    # TODO(asergeev): adaptive per-class fencing, based on frequency
    if not (random.random() < penalize_overprediction_p):
        # Do not penalize overprediction
        input = input * target

    # move weights to appropriate device
    class_weights = class_weights.to(input.device)

    # permute(0, 2, 3, 1) is required so that channel becomes the last dimension
    # and we can multiply it by class_weights which will get broadcasted properly

    iflat = input.permute(0, 2, 3, 1)
    tflat = (target > 0.99).float().permute(0, 2, 3, 1)
    intersection = (class_weights * iflat * tflat).sum()

    # Deduct 50% of hit pixels on the dilation boundary. This penalizes mis-predicting
    # random pixels higher than mis-predicting pixels on the dilation boundary
    # TODO(asergeev): full multi-step dilation with gradually increasing penalty for mis-prediction
    tflat_dilation_boundary = ((target <= 0.99) & (target > 0.01)).float().permute(0, 2, 3, 1)
    dilation_forgiveness = 0.5 * (class_weights * iflat * tflat_dilation_boundary).sum()
    union = (
        (class_weights * iflat).sum()
        - dilation_forgiveness
        + overprediction_forgiveness * (class_weights * tflat).sum()
    )

    loss = 1 - (((1.0 + overprediction_forgiveness) * intersection + smooth) / (union + smooth))

    return cast(torch.Tensor, loss)


def dots_count(
    input: torch.Tensor, enabled_classes: Optional[torch.Tensor] = None, threshold: float = 0.5, min_px: int = 25
) -> torch.Tensor:
    assert (
        enabled_classes is None or enabled_classes.dtype == torch.bool
    ), f"enabled_classes must be bool: {enabled_classes.dtype}"

    count = torch.tensor(0.0)

    if enabled_classes is None:
        enabled_classes = torch.tensor([True] * input.shape[1])

    for n in range(input.shape[0]):
        for c in range(input.shape[1]):
            if not enabled_classes[c]:
                continue

            predicted = ((input[n, c] > threshold).byte() * 255).cpu().numpy()
            component_sizes = cv2.connectedComponentsWithStats(predicted)[2][:, -1]
            count += sum(component_sizes < min_px)

    num_active_channels = enabled_classes.sum()
    return count / (input.shape[0] * num_active_channels)


def iou_and_pr_components(input: torch.Tensor, target: torch.Tensor, threshold: float = 0.5) -> Dict[str, torch.Tensor]:
    # Turn into truth values
    input = input > threshold

    # Remove dilation boundary from the prediction and label
    label_dilation_boundary = (target > 0.01) & (target <= 0.99)
    input = input & (~label_dilation_boundary)
    target = target > 0.99

    # Sum over batch
    intersection = (input & target).float().sum((-2, -1))  # Will be zero if Truth=0 or Prediction=0
    union = (input | target).float().sum((-2, -1))  # Will be zero if both are 0
    input_sum = input.float().sum((-2, -1))
    target_sum = target.float().sum((-2, -1))

    true_positives = (input & target).float().sum((-2, -1))
    false_positives = (input & (~target)).float().sum((-2, -1))
    true_negatives = ((~input) & (~target)).float().sum((-2, -1))
    false_negatives = ((~input) & target).float().sum((-2, -1))

    return {
        "intersection": intersection,
        "union": union,
        "input_sum": input_sum,
        "target_sum": target_sum,
        "true_positives": true_positives,
        "false_positives": false_positives,
        "true_negatives": true_negatives,
        "false_negatives": false_negatives,
    }

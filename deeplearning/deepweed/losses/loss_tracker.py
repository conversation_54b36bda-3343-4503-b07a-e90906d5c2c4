import json
import os
from collections import defaultdict
from typing import Any, Dict, List

import h5py
import numpy as np

SAVED_FILENAME = "loss.h5"


class LossTracker:
    def __init__(self, dataset_id: str, phase: str) -> None:
        self.attrs: Dict[str, str] = {"dataset_id": dataset_id, "phase": phase}
        self.data: Dict[str, Any] = {
            "image_uri": [],
            "losses": defaultdict(list),
            "epoch": [],
            "step": [],
            "transforms": [],
        }

    def make_ordered_data(self, all_recording_data: List[Dict[str, Any]]) -> None:
        for recording_data in all_recording_data:
            self.add_examples(
                image_filepath_list=recording_data["image_uri"],
                metrics=recording_data["losses"],
                epoch_list=recording_data["epoch"],
                step_list=recording_data["step"],
                transforms=recording_data["transforms"],
            )

    def add_examples(
        self,
        image_filepath_list: List[str],
        metrics: Dict[str, Any],
        epoch_list: List[int],
        step_list: List[int],
        transforms: List[List[Dict[str, Any]]],
    ) -> None:
        """
        Add loss information for a single example during training/validation/testing.
        """
        self.data["image_uri"].extend(image_filepath_list)
        for key, value in metrics.items():
            self.data["losses"][key].extend(value)
        self.data["epoch"].extend(epoch_list)
        self.data["step"].extend(step_list)
        self.data["transforms"].extend(transforms)

    def update_recording_data(self, data_key: str, group: h5py.Group, data: Any, data_type: Any,) -> None:
        if data_key not in group:
            dataset = group.create_dataset(data_key, shape=(0,), maxshape=(None,), dtype=data_type)

        dataset = group[data_key]
        current_size = dataset.shape[0]
        dataset.resize(current_size + len(data), axis=0)  # Resize the dataset to accommodate new data.
        dataset[current_size:] = data  # Append new data to the dataset.

    def clear_recording_data(self,) -> None:
        for data_key in self.data:
            self.data[data_key].clear()

    def save_recording_loss_file(self, file_dir: str) -> None:
        """
        Write all accumulated data to a file at the end of the epoch without replacing existing data.
        """
        os.makedirs(file_dir, exist_ok=True)

        with h5py.File(os.path.join(file_dir, SAVED_FILENAME), "a", libver="latest") as f:
            f.attrs.update({k: v for k, v in self.attrs.items() if k not in f.attrs})
            if "losses" not in f:
                f.create_group("losses")

            for data_key, data_value in self.data.items():
                if data_key == "losses":
                    for loss_key, loss_value in data_value.items():
                        self.update_recording_data(
                            data_key=loss_key,
                            group=f["losses"],
                            data=np.array(loss_value, dtype="float32"),
                            data_type="float32",
                        )

                elif data_key == "transforms":
                    self.update_recording_data(
                        data_key=data_key,
                        group=f,
                        data=np.array(
                            [json.dumps(data) for data in data_value], dtype=h5py.string_dtype(encoding="utf-8")
                        ),
                        data_type=h5py.string_dtype(encoding="utf-8"),
                    )

                elif data_key == "image_uri":
                    self.update_recording_data(
                        data_key=data_key,
                        group=f,
                        data=np.array(data_value, dtype=h5py.string_dtype(encoding="utf-8")),
                        data_type=h5py.string_dtype(encoding="utf-8"),
                    )

                else:  # for data_key in ["epoch", "step"]
                    self.update_recording_data(
                        data_key=data_key, group=f, data=np.array(data_value, dtype="uint32"), data_type="uint32",
                    )

        self.clear_recording_data()

from typing import Any, Optional, <PERSON><PERSON>

import torch

from deeplearning.utils.segm_utils import dice_loss


def segm_dice_loss(
    y: torch.Tensor,
    y_hat: torch.Tensor,
    segm_class_weights: torch.Tensor,
    batch_enabled_segm_classes: torch.Tensor,
    pre_sigmoid: bool,
    current_epoch: int,
    overprediction_warmup: Optional[int] = None,
) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
    loss, loss_per_sample = dice_loss(
        torch.sigmoid(y_hat) if pre_sigmoid else y_hat,
        y,
        class_weights=segm_class_weights * batch_enabled_segm_classes.to(segm_class_weights.device),
        # Small-occupancy plants such as onions tends to collapse to always predict that
        # they don't exist. This helps to train model to predict their existence first.
        penalize_overprediction_p=(current_epoch / overprediction_warmup if overprediction_warmup else 1.0),
        overprediction_forgiveness=40.0,
    )

    return loss, loss_per_sample


def segm_bce_loss(
    y: torch.Tensor,
    y_hat: torch.Tensor,
    segm_class_weights: torch.Tensor,
    batch_enabled_segm_classes: torch.Tensor,
    pre_sigmoid: bool,
) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:

    loss_function: Any
    if pre_sigmoid:
        loss_function = torch.nn.functional.binary_cross_entropy_with_logits
    else:
        loss_function = torch.nn.functional.binary_cross_entropy

    batch_size = y.shape[0]
    reshaped_segm_class_weights = segm_class_weights.repeat(batch_size).reshape(batch_size, -1, 1, 1).to(y.device)
    red_confidence_weights = ((y < 0.01) | (y > 0.99)).float()
    weight = reshaped_segm_class_weights * red_confidence_weights * batch_enabled_segm_classes

    loss: torch.Tensor = loss_function(y_hat, y, weight=weight, reduction="none")
    # Calculate the loss based on each batch.
    loss_per_batch = loss.nanmean()
    # Calculate the loss based on each sample.
    loss_per_sample = loss.detach().nanmean(dim=[1, 2, 3])

    return loss_per_batch, loss_per_sample

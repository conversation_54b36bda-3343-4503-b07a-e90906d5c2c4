from typing import TYPE_CHECKING, Any, List, Optional, Tuple

import torch
import torch.nn.functional as F

from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass

if TYPE_CHECKING:
    from generated.cv.runtime.proto.cv_runtime_pb2 import HitClassValue


def sigmoid_focal_loss(
    inputs: torch.Tensor,
    targets: torch.Tensor,
    alpha: float = 0.25,
    gamma: float = 2,
    reduction: str = "none",
    weight: Optional[torch.Tensor] = None,
) -> torch.Tensor:

    p = inputs
    ce_loss = F.binary_cross_entropy(inputs, targets, weight=weight, reduction="none")
    p_t = p * targets + (1 - p) * (1 - targets)
    loss: torch.Tensor = ce_loss * ((1 - p_t) ** gamma)

    if alpha >= 0:
        alpha_t = alpha * targets + (1 - alpha) * (1 - targets)
        loss = alpha_t * loss

    # Check reduction option and return loss accordingly
    if reduction == "none":
        pass
    elif reduction == "mean":
        loss = loss.mean()
    elif reduction == "sum":
        loss = loss.sum()
    else:
        raise ValueError(
            f"Invalid Value for arg 'reduction': '{reduction} \n Supported reduction modes: 'none', 'mean', 'sum'"
        )
    return loss


def sigmoid_focal_loss_with_logits(
    inputs: torch.Tensor,
    targets: torch.Tensor,
    alpha: float = 0.25,
    gamma: float = 2,
    reduction: str = "none",
    weight: Optional[torch.Tensor] = None,
) -> torch.Tensor:

    p = torch.sigmoid(inputs)
    ce_loss = F.binary_cross_entropy_with_logits(inputs, targets, weight=weight, reduction="none")
    p_t = p * targets + (1 - p) * (1 - targets)
    loss: torch.Tensor = ce_loss * ((1 - p_t) ** gamma)

    if alpha >= 0:
        alpha_t = alpha * targets + (1 - alpha) * (1 - targets)
        loss = alpha_t * loss

    # Check reduction option and return loss accordingly
    if reduction == "none":
        pass
    elif reduction == "mean":
        loss = loss.mean()
    elif reduction == "sum":
        loss = loss.sum()
    else:
        raise ValueError(
            f"Invalid Value for arg 'reduction': '{reduction} \n Supported reduction modes: 'none', 'mean', 'sum'"
        )
    return loss


def point_category_loss(
    y_point_hits: torch.Tensor,
    y_point_categories: torch.Tensor,
    y_point_categories_hat: torch.Tensor,
    y_point_confidence: torch.Tensor,
    downsample: int,
    loss_multipliers: torch.Tensor,
    bep_4d: torch.Tensor,
    pre_sigmoid: bool,
    point_weight_constant: float,
    point_hit_threshold: float,
    weed_point_class_weights: torch.Tensor,
) -> Tuple[torch.Tensor, torch.Tensor]:

    loss_function: Any
    if pre_sigmoid:
        loss_function = torch.nn.functional.binary_cross_entropy_with_logits
    else:
        loss_function = torch.nn.functional.binary_cross_entropy

    batch_size = y_point_categories.shape[0]
    reshaped_weed_point_class_weights = (
        weed_point_class_weights.repeat(batch_size).reshape(batch_size, -1, 1, 1).to(y_point_categories.device)
    )

    # Select category hits that actually contain a labeled weed
    selected_hits = (y_point_hits[:, HitClass.WEED].unsqueeze(1) > point_hit_threshold).float()

    # Compute the adjusted importance weight for each of the category hits
    weights = selected_hits * reshaped_weed_point_class_weights * y_point_confidence * loss_multipliers * bep_4d

    # Scale the weights based on the downsample ratio and point weight constant
    weights = weights * point_weight_constant / downsample ** 2

    # Compute the loss
    loss: torch.Tensor = loss_function(y_point_categories_hat, y_point_categories, weight=weights, reduction="none")
    # Calculate the loss based on each batch.
    loss_per_batch = loss.mean()
    # Calculate the loss based on each sample.
    loss_per_sample = loss.detach().mean(dim=[1, 2, 3])

    return loss_per_batch, loss_per_sample


def point_offset_loss(
    y_point_hits: torch.Tensor,
    y_point_offsets: torch.Tensor,
    y_point_offsets_hat: torch.Tensor,
    y_point_confidence: torch.Tensor,
    downsample: int,
    loss_multipliers: torch.Tensor,
    point_weight_constant: float,
    point_hit_threshold: float,
) -> Tuple[torch.Tensor, torch.Tensor]:
    adjusted_point_weight_constant = point_weight_constant / downsample ** 2
    selected_hits = (y_point_hits > point_hit_threshold).float()
    weights = selected_hits * adjusted_point_weight_constant * y_point_confidence * loss_multipliers
    weights = weights.sqrt().unsqueeze(-1)

    loss: torch.Tensor = torch.nn.functional.huber_loss(
        y_point_offsets_hat * weights, y_point_offsets * weights, reduction="none"
    )
    # Calculate the loss based on each batch.
    loss_per_batch = loss.mean()
    # Calculate the loss based on each sample.
    loss_per_sample = loss.detach().mean(dim=[1, 2, 3, 4])

    return loss_per_batch, loss_per_sample


def point_size_loss(
    y_point_hits: torch.Tensor,
    y_point_sizes: torch.Tensor,
    y_point_sizes_hat: torch.Tensor,
    y_point_confidence: torch.Tensor,
    downsample: int,
    loss_multipliers: torch.Tensor,
    point_weight_constant: float,
    point_hit_threshold: float,
) -> Tuple[torch.Tensor, torch.Tensor]:
    adjusted_point_weight_constant = point_weight_constant / downsample ** 2
    weights = (
        (y_point_hits > point_hit_threshold).float()
        * adjusted_point_weight_constant
        * y_point_confidence
        * loss_multipliers
    ).sqrt()

    loss: torch.Tensor = torch.nn.functional.huber_loss(
        y_point_sizes_hat * weights, y_point_sizes * weights, reduction="none"
    )
    # Calculate the loss based on each batch.
    loss_per_batch = loss.mean()
    # Calculate the loss based on each sample.
    loss_per_sample = loss.detach().mean(dim=[1, 2, 3])

    return loss_per_batch, loss_per_sample


def point_hit_loss(
    y_point_hits: torch.Tensor,
    y_point_hits_hat: torch.Tensor,
    y_point_confidence: torch.Tensor,
    crop_punishment_weight: torch.Tensor,
    downsample: int,
    loss_multipliers: torch.Tensor,
    beh_4d: torch.Tensor,
    hit_class: "HitClassValue",
    pre_sigmoid: bool,
    point_hit_weights: torch.Tensor,
    point_weight_constant: float,
    crop_classes: List[str],
    no_crop_background_multiplier: float,
    focal_loss: bool,
) -> Tuple[torch.Tensor, torch.Tensor]:

    loss_function: Any
    if focal_loss:
        if pre_sigmoid:
            loss_function = sigmoid_focal_loss_with_logits
        else:
            loss_function = sigmoid_focal_loss
    else:
        if pre_sigmoid:
            loss_function = torch.nn.functional.binary_cross_entropy_with_logits
        else:
            loss_function = torch.nn.functional.binary_cross_entropy

    # This adds a weight to the hit values in the first channel
    punishment_weight = torch.cat(
        [crop_punishment_weight, torch.ones_like(crop_punishment_weight), torch.ones_like(crop_punishment_weight)], 1,
    )
    batch_size = y_point_hits.shape[0]
    reshaped_point_hit_weights = (
        point_hit_weights.repeat(batch_size).reshape(batch_size, -1, 1, 1).to(y_point_hits.device)
    )

    # POINT_WEIGHT_CONSTANT = 4e5
    # downsample defaults to 16 so 4e5 / 16 ** 2 = 1562.5
    adjusted_point_weight_constant = point_weight_constant / downsample ** 2

    # Default runs do have a crop, this is not used by default
    if len(crop_classes) == 0:
        background_weight = no_crop_background_multiplier * adjusted_point_weight_constant
    else:
        background_weight = adjusted_point_weight_constant

    weights = (
        (y_point_hits * adjusted_point_weight_constant + (1 - y_point_hits) * background_weight)
        * reshaped_point_hit_weights
        * y_point_confidence
        * punishment_weight
        * loss_multipliers
        * beh_4d
    )

    loss: torch.Tensor = loss_function(
        y_point_hits_hat[:, hit_class : hit_class + 1],
        y_point_hits[:, hit_class : hit_class + 1],
        weight=weights[:, hit_class : hit_class + 1],
        reduction="none",
    )
    # Calculate the loss based on each batch.
    loss_per_batch = loss.nanmean()
    # Calculate the loss based on each sample.
    loss_per_sample = loss.detach().nanmean(dim=[1, 2, 3])

    # Set nan loss to 0. Helps deal with https://github.com/pytorch/pytorch/issues/49844
    loss_per_batch[loss_per_batch.isnan()] = 0
    loss_per_sample[loss_per_sample.isnan()] = 0

    return loss_per_batch, loss_per_sample

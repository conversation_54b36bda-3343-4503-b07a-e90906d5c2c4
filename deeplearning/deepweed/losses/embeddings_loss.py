import random
from collections import defaultdict
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple, cast

import numpy as np
import torch

from deeplearning.comparison.data_utils import cosine_similarity_normed_inputs
from deeplearning.comparison.loss import contrastive_loss
from deeplearning.deepweed.constants import EMBEDDING_LOSS_SIZE_DIFFERENTIATION_THRESHOLD_DEFAULT
from deeplearning.deepweed.point_utils import Point, get_embedding_at_hit_point
from deeplearning.scripts.datasets.get_uuids import does_square_a_overlap_center_b, does_square_a_overlap_circle_b

if TYPE_CHECKING:
    from generated.cv.runtime.proto.cv_runtime_pb2 import HitClassValue


def get_eligible_batch_points(all_points: List[List[Point]]) -> Dict[int, List[Dict[str, Any]]]:
    # size of the all_points is the batch size.
    batch_points: Dict[int, List[Dict[str, Any]]] = defaultdict(list)
    id = 0
    for batch_ind, points in enumerate(all_points):
        for p in points:
            if p.comparison_embedding is not None and len(p.hit_y_x) > 0:
                batch_points[batch_ind].append({"id": id, "batch_ind": batch_ind, "point": p})
            id += 1

    return batch_points


def get_average_embedding(embeddings: torch.Tensor, batch_ind: int, hits_y_x: List[Tuple[int, int]]) -> torch.Tensor:
    embeddings_a = [
        get_embedding_at_hit_point(embeddings=embeddings, batch_idx=batch_ind, y=hit_y, x=hit_x)
        for hit_y, hit_x in hits_y_x
    ]
    return torch.mean(torch.stack(embeddings_a), dim=0)


def get_random_embedding(embeddings: torch.Tensor, batch_ind: int, hits_y_x: List[Tuple[int, int]]) -> torch.Tensor:
    hit_y, hit_x = hits_y_x[random.randint(0, len(hits_y_x) - 1)]
    return get_embedding_at_hit_point(embeddings=embeddings, batch_idx=batch_ind, y=hit_y, x=hit_x,)


def create_batch_overlapped_points_dict(
    batch_index_to_points: Dict[int, List[Dict[str, Any]]], check_by_center: bool = True
) -> Dict[int, Tuple[List[Tuple[Dict[str, Any], Dict[str, Any]]], List[Dict[str, Any]], List[Dict[str, Any]]]]:
    batch_overlapped_points_dict = {}

    for batch_idx, points in batch_index_to_points.items():
        num_points = len(points)
        overlapped_matrix = np.zeros(
            (num_points, num_points), dtype=bool
        )  # overlapped_matrix contains False (non-overlapped or invalid), True (overlapped) only.

        for idx_a, point_a in enumerate(points):
            point_a_info = {
                "x": point_a["point"].x,
                "y": point_a["point"].y,
                "radius": point_a["point"].r,
                "uuid": point_a["point"].uuid,
            }

            for idx_b, point_b in enumerate(points):
                if idx_a == idx_b:
                    continue

                point_b_info = {
                    "x": point_b["point"].x,
                    "y": point_b["point"].y,
                    "radius": point_b["point"].r,
                    "uuid": point_b["point"].uuid,
                }
                if check_by_center:
                    flag = does_square_a_overlap_center_b(
                        point_a=point_a_info, point_b=point_b_info
                    )  # flag == True denotes overlapped, while flag == False denotes non-overlapped.
                else:
                    flag = does_square_a_overlap_circle_b(
                        point_a=point_a_info, point_b=point_b_info
                    )  # flag == True denotes overlapped, while flag == False denotes non-overlapped.
                overlapped_matrix[idx_a, idx_b] = flag

        points_with_overlap_mask = np.any(overlapped_matrix, axis=1)
        points_with_overlap_index = np.where(points_with_overlap_mask)[0]
        points_without_overlap_index = np.where(~points_with_overlap_mask)[0]
        two_points_overlap_with_each_other_pairs = get_pairs_for_overlapping_with_each_other(
            overlapped_matrix=overlapped_matrix, points=points
        )
        points_with_overlap = [points[idx] for idx in points_with_overlap_index]
        points_without_overlap = [points[idx] for idx in points_without_overlap_index]
        batch_overlapped_points_dict[batch_idx] = (
            two_points_overlap_with_each_other_pairs,
            points_with_overlap,
            points_without_overlap,
        )

    return batch_overlapped_points_dict


def get_pairs_for_overlapping_with_each_other(
    overlapped_matrix: Any, points: List[Dict[str, Any]],
) -> List[Tuple[Dict[str, Any], Dict[str, Any]]]:
    idx_pairs = np.argwhere(overlapped_matrix)
    return list(map(lambda idx: (points[idx[0]], points[idx[1]]), idx_pairs))


def create_positive_negative_samples(
    batch_points_for_comparison: Dict[Tuple[int, int], Tuple[Dict[str, Any], Dict[str, Any]]],
    embeddings: torch.Tensor,
    average_embeddings_across_hits: bool,
    min_for_positive: float,
    max_for_negative: float,
    base_embedding_difference_on_hit_class: bool,
    positive_point_labels: List[float],
    negative_point_labels: List[float],
    positive_dist_list: List[torch.Tensor],
    negative_dist_list: List[torch.Tensor],
    positive_comparison_list: List[torch.Tensor],
    negative_comparison_list: List[torch.Tensor],
    batch_idx_list: List[Tuple[int, int]],
    remove_size_differentiated_pairs: bool = False,
    size_differentiated_threshold: float = EMBEDDING_LOSS_SIZE_DIFFERENTIATION_THRESHOLD_DEFAULT,
) -> None:
    for points_ in batch_points_for_comparison.values():
        point_a = points_[0]
        point_b = points_[1]

        point_a_point = cast(Point, point_a["point"])
        point_b_point = cast(Point, point_b["point"])

        assert point_a_point.comparison_embedding is not None
        assert point_b_point.comparison_embedding is not None
        cosine_sim = cosine_similarity_normed_inputs(
            torch.nn.functional.normalize(
                point_a_point.comparison_embedding.clone().detach().to(device=embeddings.device).unsqueeze(0)
            ),
            torch.nn.functional.normalize(
                point_b_point.comparison_embedding.clone().detach().to(device=embeddings.device).unsqueeze(0)
            ),
        )

        softplused_cosine_sim = torch.nn.functional.softplus(cosine_sim, beta=20, threshold=2)

        if average_embeddings_across_hits:
            embedding_a = get_average_embedding(embeddings, point_a["batch_ind"], point_a_point.hit_y_x)
            embedding_b = get_average_embedding(embeddings, point_b["batch_ind"], point_b_point.hit_y_x)

        else:
            embedding_a = get_random_embedding(embeddings, point_a["batch_ind"], point_a_point.hit_y_x)
            embedding_b = get_random_embedding(embeddings, point_b["batch_ind"], point_b_point.hit_y_x)

        embedding_cosine_sim = cosine_similarity_normed_inputs(
            torch.nn.functional.normalize(embedding_a.unsqueeze(0)),
            torch.nn.functional.normalize(embedding_b.unsqueeze(0)),
        )
        comparison_similarity_dist = (
            torch.nn.functional.softplus(embedding_cosine_sim, beta=20, threshold=2).squeeze().unsqueeze(0)
        )
        euclidean_dist = torch.nn.functional.pairwise_distance(embedding_a.unsqueeze(0), embedding_b.unsqueeze(0))

        similar = bool(softplused_cosine_sim > min_for_positive)
        different = bool(softplused_cosine_sim < max_for_negative)

        if base_embedding_difference_on_hit_class:
            similar = similar and point_a_point.hit_clz == point_b_point.hit_clz
            different = different or point_a_point.hit_clz != point_b_point.hit_clz

        if remove_size_differentiated_pairs:

            size_a = point_a_point.r
            size_b = point_b_point.r

            if size_a > size_b * size_differentiated_threshold or size_a < size_b / size_differentiated_threshold:
                similar = False

        if similar:
            positive_point_labels.append(1.0)
            positive_dist_list.append(euclidean_dist)
            positive_comparison_list.append(comparison_similarity_dist)
            batch_idx_list.append((point_a["batch_ind"], point_b["batch_ind"]))

        elif different:
            negative_point_labels.append(0.0)
            negative_dist_list.append(euclidean_dist)
            negative_comparison_list.append(comparison_similarity_dist)
            batch_idx_list.append((point_a["batch_ind"], point_b["batch_ind"]))


def sample_pair(
    batch_points_for_comparison: Dict[Tuple[int, int], Tuple[Dict[str, Any], Dict[str, Any]]],
    batch_points1: List[Dict[str, Any]],
    batch_points2: List[Dict[str, Any]],
    items_to_get: int,
) -> None:
    if not batch_points1 or not batch_points2:
        return

    random_points_1 = random.choices(batch_points1, k=items_to_get)
    random_points_2 = random.choices(batch_points2, k=items_to_get)
    for point1, point2 in zip(random_points_1, random_points_2):
        if point1["id"] == point2["id"]:
            continue
        ids = tuple(sorted((point1["id"], point2["id"])))
        batch_points_for_comparison[ids] = (point1, point2)


def embeddings_loss(  # noqa: C901
    points: List[List[Point]],
    embeddings: torch.Tensor,
    base_embedding_difference_on_hit_class: bool = False,
    max_items_to_compare: int = 128,
    min_for_positive: float = 0.8,
    max_for_negative: float = 0.2,
    margin: float = 128,
    compare_whole_batch: bool = True,
    average_embeddings_across_hits: bool = False,
    use_comparison_similarity_loss: bool = False,
    use_points_with_overlap: bool = False,
    without_overlap_and_without_overlap_percentage: float = 0.5,
    with_overlap_and_with_overlap_percentage: float = 0.5,
    two_points_overlap_with_each_percentage: float = 0.5,
    remove_size_differentiated_pairs: bool = False,
    size_differentiated_threshold: float = EMBEDDING_LOSS_SIZE_DIFFERENTIATION_THRESHOLD_DEFAULT,
) -> Tuple[
    torch.Tensor,
    torch.Tensor,
    Optional[torch.Tensor],
    Optional[torch.Tensor],
    Optional[torch.Tensor],
    Optional[torch.Tensor],
]:
    batch_size = embeddings.shape[0]
    batch_index_to_points = get_eligible_batch_points(points)

    if sum([len(val) for val in batch_index_to_points.values()]) == 0:
        return (
            torch.tensor(0.0, device=embeddings.device),
            torch.zeros((batch_size,), device=embeddings.device),
            None,
            None,
            None,
            None,
        )

    positive_point_labels: List[float] = []
    negative_point_labels: List[float] = []
    positive_dist_list: List[torch.Tensor] = []
    negative_dist_list: List[torch.Tensor] = []
    positive_comparison_list: List[torch.Tensor] = []
    negative_comparison_list: List[torch.Tensor] = []
    batch_idx_list: List[Tuple[int, int]] = []

    # Find the overlap information within each batch.
    if use_points_with_overlap:
        batch_overlapped_points_dict = create_batch_overlapped_points_dict(
            batch_index_to_points=batch_index_to_points, check_by_center=True
        )  # If check_by_center == True, we check whether the compared point's centers is within the main point's square. Otherwise, we determine if the compared point's radii overlap with the main point's square.

    # Compute the embedding loss based on all the batches or not.
    if compare_whole_batch:
        if use_points_with_overlap:
            tmp_all_points: Dict[int, Any] = {0: ([], [], [])}
            for (
                _,
                (points_overlap_with_each_pairs, points_with_overlap, points_without_overlap),
            ) in batch_overlapped_points_dict.items():
                tmp_all_points[0][0].extend(points_overlap_with_each_pairs)
                tmp_all_points[0][1].extend(points_with_overlap)
                tmp_all_points[0][2].extend(points_without_overlap)
            batch_overlapped_points_dict = tmp_all_points
        else:
            tmp_all_points = {0: []}
            for _, batch_points in batch_index_to_points.items():
                tmp_all_points[0].extend(batch_points)
            batch_index_to_points = tmp_all_points

    # Calculate the items_to_get number.
    if use_points_with_overlap:
        items_to_get = max_items_to_compare // len(
            batch_overlapped_points_dict
        )  # Evenly get the pairs from each batch.
        without_overlap_and_without_overlap_to_get = int(
            items_to_get * without_overlap_and_without_overlap_percentage
        )  # Without overlap point and without overlap point.
        with_overlap_to_get = items_to_get - without_overlap_and_without_overlap_to_get
        with_overlap_and_with_overlap_to_get = int(with_overlap_to_get * with_overlap_and_with_overlap_percentage)
        with_overlap_and_with_overlap_with_two_points_overlap_with_each_to_get = int(
            with_overlap_and_with_overlap_to_get * two_points_overlap_with_each_percentage
        )  # With overlap point and with overlap point, and two points overlapping with each other.
        with_overlap_and_with_overlap_without_two_points_overlap_with_each_to_get = (
            with_overlap_and_with_overlap_to_get
            - with_overlap_and_with_overlap_with_two_points_overlap_with_each_to_get
        )  # With overlap point and with overlap point, and two points not overlapping with each other.
        with_overlap_and_without_overlap_to_get = (
            with_overlap_to_get - with_overlap_and_with_overlap_to_get
        )  # With overlap point and without overlap point.
    else:
        items_to_get = max_items_to_compare // len(batch_index_to_points)

    # Create positive and negative samples from the batch dict.
    if use_points_with_overlap:
        for (
            idx,
            (points_overlap_with_each_pairs, points_with_overlap, points_without_overlap),
        ) in batch_overlapped_points_dict.items():
            batch_points_for_comparison: Dict[Tuple[int, int], Tuple[Dict[str, Any], Dict[str, Any]]] = {}
            points_overlap_with_each_pairs, points_with_overlap, points_without_overlap = batch_overlapped_points_dict[
                idx
            ]

            # Stage 1: Sample the pair: (with overlap, with overlap, two points overlap with each others).
            random_pairs = random.sample(
                points_overlap_with_each_pairs,
                k=min(
                    len(points_overlap_with_each_pairs),
                    with_overlap_and_with_overlap_with_two_points_overlap_with_each_to_get,
                ),
            )
            for point1, point2 in random_pairs:
                ids = tuple(sorted((point1["id"], point2["id"])))
                batch_points_for_comparison[ids] = (point1, point2)

            # Stage 2: Sample the pair: (with overlap, with overlap, not guarantee two points overlap with each others).
            sample_pair(
                batch_points_for_comparison=batch_points_for_comparison,
                batch_points1=points_with_overlap,
                batch_points2=points_with_overlap,
                items_to_get=with_overlap_and_with_overlap_without_two_points_overlap_with_each_to_get,
            )

            # Stage 3: Sample the pair: (with overlap, without overlap).
            sample_pair(
                batch_points_for_comparison=batch_points_for_comparison,
                batch_points1=points_with_overlap,
                batch_points2=points_without_overlap,
                items_to_get=with_overlap_and_without_overlap_to_get,
            )

            # Stage 4: Sample the pair: (without overlap, without overlap).
            sample_pair(
                batch_points_for_comparison=batch_points_for_comparison,
                batch_points1=points_without_overlap,
                batch_points2=points_without_overlap,
                items_to_get=without_overlap_and_without_overlap_to_get,
            )

            create_positive_negative_samples(
                batch_points_for_comparison=batch_points_for_comparison,
                embeddings=embeddings,
                average_embeddings_across_hits=average_embeddings_across_hits,
                min_for_positive=min_for_positive,
                max_for_negative=max_for_negative,
                base_embedding_difference_on_hit_class=base_embedding_difference_on_hit_class,
                positive_point_labels=positive_point_labels,
                negative_point_labels=negative_point_labels,
                positive_dist_list=positive_dist_list,
                negative_dist_list=negative_dist_list,
                positive_comparison_list=positive_comparison_list,
                negative_comparison_list=negative_comparison_list,
                batch_idx_list=batch_idx_list,
                remove_size_differentiated_pairs=remove_size_differentiated_pairs,
                size_differentiated_threshold=size_differentiated_threshold,
            )

    else:
        for idx, batch_points in batch_index_to_points.items():
            if len(batch_points) < 2:
                continue

            batch_points_for_comparison = {}

            sample_pair(
                batch_points_for_comparison=batch_points_for_comparison,
                batch_points1=batch_points,
                batch_points2=batch_points,
                items_to_get=items_to_get,
            )

            if len(batch_points_for_comparison) == 0:
                continue

            create_positive_negative_samples(
                batch_points_for_comparison=batch_points_for_comparison,
                embeddings=embeddings,
                average_embeddings_across_hits=average_embeddings_across_hits,
                min_for_positive=min_for_positive,
                max_for_negative=max_for_negative,
                base_embedding_difference_on_hit_class=base_embedding_difference_on_hit_class,
                positive_point_labels=positive_point_labels,
                negative_point_labels=negative_point_labels,
                positive_dist_list=positive_dist_list,
                negative_dist_list=negative_dist_list,
                positive_comparison_list=positive_comparison_list,
                negative_comparison_list=negative_comparison_list,
                batch_idx_list=batch_idx_list,
                remove_size_differentiated_pairs=remove_size_differentiated_pairs,
                size_differentiated_threshold=size_differentiated_threshold,
            )

    point_labels = positive_point_labels + negative_point_labels
    dist_list = positive_dist_list + negative_dist_list
    comparison_list = positive_comparison_list + negative_comparison_list
    if len(dist_list) == 0:
        return (
            torch.tensor(0.0, device=embeddings.device),
            torch.zeros((batch_size,), device=embeddings.device),
            None,
            None,
            None,
            None,
        )

    if use_comparison_similarity_loss:
        torch_embeddings = torch.cat(comparison_list)
        loss = torch.nn.functional.mse_loss(
            torch_embeddings.unsqueeze(0),
            torch.tensor(point_labels, device=embeddings.device).unsqueeze(0),
            reduction="none",
        ).squeeze(0)
    else:
        torch_embeddings = torch.cat(dist_list)
        loss = contrastive_loss(
            torch_embeddings, torch.tensor(point_labels, device=embeddings.device), margin=margin, aggregation_fn=None,
        )
    # Calculate the loss based on each batch.
    loss_per_batch = loss.mean()
    # Calculate the loss based on each sample.
    loss_per_sample = torch.zeros((batch_size,), device=embeddings.device)
    for idx in range(len(loss)):
        batch_idx = batch_idx_list[idx]
        loss_per_sample[batch_idx[0]] += loss[idx].detach() / 2
        loss_per_sample[batch_idx[1]] += loss[idx].detach() / 2
    loss_per_sample /= len(loss)

    return (
        loss_per_batch,
        loss_per_sample,
        torch.cat(positive_dist_list).detach() if positive_dist_list else None,
        torch.cat(negative_dist_list).detach() if negative_dist_list else None,
        torch.cat(positive_comparison_list).detach() if positive_comparison_list else None,
        torch.cat(negative_comparison_list).detach() if negative_comparison_list else None,
    )


def info_nce_loss(
    points_list: List[List[Point]],
    embeddings: torch.Tensor,
    base_embedding_difference_on_hit_class: bool,
    num_neg_samples: int = 10,
    max_items_to_compare: int = 10,
    min_for_positive: float = 0.8,
    max_for_negative: float = 0.2,
    average_embeddings_across_hits: bool = False,
) -> Tuple[torch.Tensor, torch.Tensor, Optional[List[torch.Tensor]]]:
    batch_size = embeddings.shape[0]

    loss_per_sample = torch.zeros((batch_size,), device=embeddings.device)
    batch_index_to_points = get_eligible_batch_points(points_list)
    batch_points = []
    for val in batch_index_to_points.values():
        batch_points.extend(val)

    if len(batch_points) < 2:
        return torch.tensor(0.0, device=embeddings.device), torch.zeros((batch_size,), device=embeddings.device), None

    losses: List[torch.Tensor] = []
    sets: List[torch.Tensor] = []
    # Try to fill the nce_sets list

    batch_comparison_embeddings = torch.stack(
        [p["point"].comparison_embedding.to(device=embeddings.device) for p in batch_points]
    )
    hit_list = None
    if base_embedding_difference_on_hit_class:
        hit_list = [p["point"].hit_clz for p in batch_points]

    positive_matches, negative_matches = find_positive_and_negative_examples(
        batch_comparison_embeddings,
        min_for_positive=min_for_positive,
        max_for_negative=max_for_negative,
        hit_list=hit_list,
    )

    positive_keys = set(positive_matches.keys())
    negative_keys = set(negative_matches.keys())

    overlap = list(positive_keys.intersection(negative_keys))

    if len(overlap) < 1:
        return (
            torch.tensor(0.0, device=embeddings.device),
            torch.zeros((batch_size,), device=embeddings.device),
            None,
        )

    while len(losses) < min(max_items_to_compare, len(batch_points)):
        index = random.choice(overlap)

        anchor_point = batch_points[index]
        positive_match_for_loss = random.choice(positive_matches[index])
        negative_matches_for_loss = random.sample(
            negative_matches[index], min(num_neg_samples, len(negative_matches[index]))
        )

        if average_embeddings_across_hits:
            anchor_embedding = get_average_embedding(
                embeddings, anchor_point["batch_ind"], anchor_point["point"].hit_y_x
            )
        else:
            anchor_embedding = get_average_embedding(
                embeddings, anchor_point["batch_ind"], anchor_point["point"].hit_y_x
            )

        match_indices = [positive_match_for_loss] + negative_matches_for_loss
        dists = []
        for match_ind in match_indices:
            match_point = batch_points[match_ind]

            if average_embeddings_across_hits:
                match_embedding = get_average_embedding(
                    embeddings, match_point["batch_ind"], match_point["point"].hit_y_x
                )
            else:
                match_embedding = get_average_embedding(
                    embeddings, match_point["batch_ind"], match_point["point"].hit_y_x
                )
            dists.append(
                -torch.nn.functional.pairwise_distance(anchor_embedding.unsqueeze(0), match_embedding.unsqueeze(0))
            )

        dists_raw = torch.stack(dists).squeeze().detach()

        loss = torch.nn.CrossEntropyLoss()
        label = torch.zeros_like(dists_raw).squeeze()
        label[0] = 1

        current_loss = loss(dists_raw, label)
        losses.append(current_loss.reshape(1))
        sets.append(dists_raw)

        # Disribute the loss to the corresponding sample.
        loss_per_sample[anchor_point["batch_ind"]] += current_loss.detach()

    if len(losses) == 0:
        return torch.tensor(0.0, device=embeddings.device), torch.zeros((batch_size,), device=embeddings.device), None
    stacked_losses: torch.Tensor = torch.stack(losses, dim=0)
    # Calculate the loss based on each batch.
    loss_per_batch = stacked_losses.mean()
    # Calculate the loss based on each sample.
    loss_per_sample /= len(stacked_losses)

    return loss_per_batch, loss_per_sample, sets


def triplet_loss(
    points_list: List[List[Point]],
    embeddings: torch.Tensor,
    max_items_to_compare: int = 128,
    min_for_positive: float = 0.8,
    max_for_negative: float = 0.2,
    margin: float = 128,
    random_neighbor: bool = False,
    base_embedding_difference_on_hit_class: bool = False,
    average_embeddings_across_hits: bool = False,
) -> Tuple[torch.Tensor, torch.Tensor, Optional[torch.Tensor], Optional[torch.Tensor]]:
    batch_size = embeddings.shape[0]
    batch_index_to_points = get_eligible_batch_points(points_list)
    batch_points = []
    for val in batch_index_to_points.values():
        batch_points.extend(val)
    batch_index_list: List[int] = []
    if len(batch_points) < 3:
        return (
            torch.tensor(0.0, device=embeddings.device),
            torch.zeros((batch_size,), device=embeddings.device),
            None,
            None,
        )
    anchors: List[torch.Tensor] = []
    positives: List[torch.Tensor] = []
    negatives: List[torch.Tensor] = []
    batch_comparison_embeddings = torch.stack(
        [p["point"].comparison_embedding.to(device=embeddings.device) for p in batch_points]
    )
    hit_list = None
    if base_embedding_difference_on_hit_class:
        hit_list = [p["point"].hit_clz for p in batch_points]

    positive_matches, negative_matches = find_positive_and_negative_examples(
        batch_comparison_embeddings,
        min_for_positive=min_for_positive,
        max_for_negative=max_for_negative,
        hit_list=hit_list,
    )

    positive_keys = set(positive_matches.keys())
    negative_keys = set(negative_matches.keys())

    overlap = list(positive_keys.intersection(negative_keys))

    if len(overlap) < 1:
        return (
            torch.tensor(0.0, device=embeddings.device),
            torch.zeros((batch_size,), device=embeddings.device),
            None,
            None,
        )

    for index in overlap:
        anchor_point = batch_points[index]
        if random_neighbor:
            positive_matches_for_loss = [random.choice(positive_matches[index])]
            negative_matches_for_loss = [random.choice(negative_matches[index])]
        else:
            positive_matches_for_loss = positive_matches[index]
            negative_matches_for_loss = negative_matches[index]
        for positive_match in positive_matches_for_loss:
            for negative_match in negative_matches_for_loss:
                positive_point = batch_points[positive_match]
                negative_point = batch_points[negative_match]

                for (item_list, point) in [
                    (anchors, anchor_point),
                    (positives, positive_point),
                    (negatives, negative_point),
                ]:
                    if average_embeddings_across_hits:
                        embedding = get_average_embedding(embeddings, point["batch_ind"], point["point"].hit_y_x)
                    else:
                        embedding = get_average_embedding(embeddings, point["batch_ind"], point["point"].hit_y_x)

                    item_list.append(embedding)

                batch_index_list.append(batch_points[index]["batch_ind"])
                if len(anchors) > max_items_to_compare:
                    break

    anchor_tensor = torch.stack(anchors)
    positive_tensor = torch.stack(positives)
    negative_tensor = torch.stack(negatives)

    triplet_loss = torch.nn.functional.triplet_margin_loss(
        anchor=anchor_tensor, positive=positive_tensor, negative=negative_tensor, margin=margin, p=2, reduction="none"
    )
    # Calculate the loss based on each batch.
    loss_per_batch = triplet_loss.mean()
    # Calculate the loss based on each sample.
    loss_per_sample = torch.zeros((batch_size,), device=embeddings.device)
    loss_per_sample.scatter_add_(0, torch.tensor(batch_index_list, device=embeddings.device), triplet_loss.detach())
    loss_per_sample /= len(triplet_loss)

    positive_dists = torch.nn.functional.pairwise_distance(positive_tensor.detach(), anchor_tensor.detach())
    negative_dists = torch.nn.functional.pairwise_distance(negative_tensor.detach(), anchor_tensor.detach())

    return loss_per_batch, loss_per_sample, positive_dists, negative_dists


def find_positive_and_negative_examples(
    embedding_mat: torch.Tensor,
    min_for_positive: float,
    max_for_negative: float,
    hit_list: Optional[List["HitClassValue"]] = None,
) -> Tuple[Dict[int, List[int]], Dict[int, List[int]]]:
    assert len(embedding_mat.shape) == 2
    normalized_embedding_mat = torch.nn.functional.normalize(embedding_mat)
    cosine_similarity_matrix = cosine_similarity_normed_inputs(normalized_embedding_mat, normalized_embedding_mat)
    softplused = torch.nn.functional.softplus(cosine_similarity_matrix, beta=20, threshold=2)

    if hit_list is not None:
        hit_match_formatted_list = torch.tensor(hit_list).to(device=softplused.device).unsqueeze(-1).float()
        hit_match_tensor = 1 - torch.cdist(hit_match_formatted_list, hit_match_formatted_list)
        softplused = softplused * hit_match_tensor

    indices_for_positive = softplused >= min_for_positive
    indices_for_negative = softplused <= max_for_negative

    similarities = torch.zeros_like(softplused)

    similarities[indices_for_positive] = 1
    similarities[indices_for_negative] = -1
    similarities.fill_diagonal_(0)

    positive = torch.nonzero(similarities == 1, as_tuple=False)
    negative = torch.nonzero(similarities == -1, as_tuple=False)

    positive_matches = defaultdict(set)
    negative_matches = defaultdict(set)

    for col, row in positive:
        positive_matches[col.item()].add(row.item())

    for col, row in negative:
        negative_matches[col.item()].add(row.item())

    positive_matches_dict: Dict[int, List[int]] = {}
    negative_matches_dict: Dict[int, List[int]] = {}

    for key, value in positive_matches.items():
        positive_matches_dict[key] = list(value)

    for key, value in negative_matches.items():
        negative_matches_dict[key] = list(value)

    return positive_matches_dict, negative_matches_dict

# This file extends PyTorch native transforms with support for t(input, target).
# This is important for semantic segmentation task.
import inspect
import logging
import math
import random
from collections import OrderedDict
from copy import deepcopy
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional, Set, Tuple

import cv2
import numpy as np
import torch
import torch.nn.functional as F
import torchvision.transforms.v2 as T
from torchvision.transforms.functional import gaussian_blur

from deeplearning.deepweed.metadata import ImageMetadata, Point
from deeplearning.utils.images import _random_sample_around_one, random_gaussian_noise
from deeplearning.utils.resize_utils import interpolate

if TYPE_CHECKING:
    from generated.cv.runtime.proto.cv_runtime_pb2 import HitClassValue
LOG = logging.getLogger(__name__)

Transform = Callable[
    [
        torch.Tensor,
        List[Optional[torch.Tensor]],
        List[Point],
        ImageMetadata,
        Set[str],
        Set["HitClassValue"],
        torch.Tensor,
        Optional[int],
        Optional[Dict[str, Any]],
    ],
    Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]],
]  # input, target, points, transforms_parameters_dict


def get_previous_transforms(previous_transforms: List[Dict[str, Any]]) -> Tuple[T.Compose, Dict[str, Any]]:
    previous_transforms_list: List[Any] = []
    previous_transforms_dict = OrderedDict()
    transforms_classes = {
        name: obj for name, obj in globals().items() if inspect.isclass(obj) and obj.__module__ == __name__
    }

    for transform in previous_transforms:
        transforms_class: Any = transforms_classes[transform["name"]]
        previous_transforms_list.append(transforms_class.from_dict(transfroms_dict=transform))
        previous_transforms_dict[transform["name"]] = transform

    return Compose(previous_transforms_list), previous_transforms_dict


class RandomChoice(T.RandomChoice):
    def __init__(self, augmentations: List[Transform]) -> None:
        self._augmentations = augmentations

    def __call__(
        self, *args: Any, **kwargs: Any
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        augmentation = random.choice(self._augmentations)
        return augmentation(*args, **kwargs)


class Compose(T.Compose):
    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], List[Dict[str, Any]]]:
        transforms_dict_list = []

        for t in self.transforms:
            input, target, points, transforms_dict = t(
                input,
                target,
                points,
                image_meta,
                enabled_weed_classes,
                enabled_hit_classes,
                enabled_segm_classes,
                enabled_embedding_bucket,
                previous_transforms,
            )
            if isinstance(
                transforms_dict, list
            ):  # If there is a Compose instance, extend the list of transforms within it.
                transforms_dict_list.extend(transforms_dict)
            else:
                transforms_dict_list.append(transforms_dict)

        return input, target, points, transforms_dict_list


class TransposeAlignLongerSide:
    def __init__(self, want_longer_width: bool):
        self.want_longer_width = want_longer_width

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:

        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "TransposeAlignLongerSide"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "TransposeAlignLongerSide", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        transforms_dict["is_transpose_align_longer_side"] = transforms_dict.get(
            "is_transpose_align_longer_side",
            (transforms_dict["want_longer_width"] and input.shape[1] > input.shape[2])
            or (not transforms_dict["want_longer_width"] and input.shape[2] > input.shape[1]),
        )  # Use the previous condition if exists else re-determine.
        if transforms_dict["is_transpose_align_longer_side"]:
            # Want longer width, but height is currently longer
            assert len(input.shape) == 3, f"Expected 3-dim input shape: {input.shape}"
            assert input.shape[0] in [3, 4], f"Expected CHW/(C+D)HW order: {input.shape}"
            input = input.permute(0, 2, 1)
            for c in range(len(target)):
                target_c = target[c]
                if target_c is not None:
                    assert len(target_c.shape) == 2, f"Expected 2-dim target shape: {target_c.shape}"
                    target[c] = target_c.permute(1, 0)
            for i in range(len(points)):
                point = points[i]
                points[i] = Point(
                    point.y,
                    point.x,
                    point.r,
                    point.score,
                    point.clz,
                    point.confidence,
                    point.hit_clz,
                    comparison_embedding=point.comparison_embedding,
                    relocation_info=point.relocation_info,
                    deepweed_embedding_bucket=point.deepweed_embedding_bucket,
                    uuid=point.uuid,
                )

        return input, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return TransposeAlignLongerSide(want_longer_width=transfroms_dict["want_longer_width"])


class ColorJitter(T.ColorJitter):
    def __init__(
        self,
        brightness: Tuple[float, float],
        saturation: Tuple[float, float],
        hue: Tuple[float, float],
        gamma: Tuple[float, float],
    ):
        self.brightness = brightness
        self.saturation = saturation
        self.hue = hue
        self.gamma = gamma
        super().__init__(brightness=self.brightness, hue=self.hue, saturation=self.saturation)

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = {
            key: self.__dict__[key]
            for key in ["brightness", "saturation", "hue", "gamma", "contrast"]
            if key in self.__dict__
        }  # Extract the keys that we need only.
        transforms_dict["factor"] = _random_sample_around_one(*transforms_dict["gamma"])
        transforms_dict["name"] = "ColorJitter"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "ColorJitter", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        depth = None
        if input.shape[0] == 4:
            depth = input[3:4, :, :]
            input = input[:3, :, :]
        ret_input = super().__call__(input)
        ret_input = ret_input ** transforms_dict["factor"]
        if depth is not None:
            ret_input = torch.cat((ret_input, depth), 0)

        return ret_input, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return ColorJitter(
            brightness=transfroms_dict["brightness"],
            saturation=transfroms_dict["saturation"],
            hue=transfroms_dict["hue"],
            gamma=transfroms_dict["gamma"],
        )


class RandomBlur:
    def __init__(self, min_blur_radius: int, max_blur_radius: int) -> None:
        self.min_blur_radius = min_blur_radius
        self.max_blur_radius = max_blur_radius

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "RandomBlur"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "RandomBlur", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        transforms_dict["blur_radius"] = transforms_dict.get(
            "blur_radius", random.randint(transforms_dict["min_blur_radius"], transforms_dict["max_blur_radius"])
        )  # Use the previous condition if exists else re-determine.
        if transforms_dict["blur_radius"] > 0:
            img = input.numpy().transpose(1, 2, 0)
            img = cv2.blur(img, (transforms_dict["blur_radius"] * 2 + 1, transforms_dict["blur_radius"] * 2 + 1))
            input = torch.tensor(img.transpose(2, 0, 1))

        return input, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return RandomBlur(
            min_blur_radius=transfroms_dict["min_blur_radius"], max_blur_radius=transfroms_dict["max_blur_radius"]
        )


class GaussianNoise:
    def __init__(self, stddev: float) -> None:
        self.stddev = stddev

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "GaussianNoise"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "GaussianNoise", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        output, transforms_dict = random_gaussian_noise(input, transforms_dict["stddev"], transforms_dict)

        return output, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return GaussianNoise(stddev=transfroms_dict["stddev"])


class GaussianBlur(T.GaussianBlur):
    def __init__(self, kernel_size: float, stddev: Tuple[float, float]) -> None:
        self.kernel_size = kernel_size
        self.stddev = stddev
        super().__init__(self.kernel_size, self.stddev)

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = {
            key: self.__dict__[key] for key in ["stddev", "kernel_size", "sigma"] if key in self.__dict__
        }  # Extract the keys that we need only.
        transforms_dict["name"] = "GaussianBlur"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "GaussianBlur", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        transforms_dict["chosen_sigma"] = transforms_dict.get(
            "chosen_sigma", self.get_params(transforms_dict["sigma"][0], transforms_dict["sigma"][1])
        )  # Use the previous condition if exists else re-determine.
        output = gaussian_blur(
            input, transforms_dict["kernel_size"], [transforms_dict["chosen_sigma"], transforms_dict["chosen_sigma"]]
        )

        return output, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return GaussianBlur(stddev=transfroms_dict["stddev"], kernel_size=transfroms_dict["kernel_size"])


class RandomSimBlue:
    def __init__(self, p: float) -> None:
        self.p = p

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "RandomSimBlue"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "RandomSimBlue", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        transforms_dict["is_random_sim_blue"] = transforms_dict.get(
            "is_random_sim_blue", random.random() < transforms_dict["p"]
        )  # Use the previous condition if exists else re-determine.
        assert input.shape[0] in [3, 4], f"Expected CHW/(C+D)HW order: {input.shape}"
        if transforms_dict["is_random_sim_blue"]:
            input[2] = 0.75 * input[1] + 0.25 * input[0]

        return input, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return RandomSimBlue(p=transfroms_dict["p"])


class RandomRotation:
    def __init__(self, min_scale: float, max_scale: float, discard_points_border_px: int, border_mode: int = 0) -> None:
        self.min_scale = min_scale
        self.max_scale = max_scale
        self.discard_points_border_px = discard_points_border_px
        self.border_mode = border_mode

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "RandomRotation"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "RandomRotation", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        np_points_enabled = [
            (
                (len(enabled_weed_classes) > 0 and x.clz in enabled_weed_classes)
                or (len(enabled_weed_classes) == 0 and x.hit_clz in enabled_hit_classes)
            )
            and (enabled_embedding_bucket is None or x.deepweed_embedding_bucket == enabled_embedding_bucket)
            for x in points
        ]

        np_points = np.array([[p.x, p.y] for p in points])
        np_points = np.expand_dims(np_points, 1)
        success = False
        for _ in range(50):
            cx = input.shape[2] // 2
            cy = input.shape[1] // 2
            transforms_dict["angle"] = transforms_dict.get(
                "angle", random.uniform(-360, 360)
            )  # Use the previous rotate_angle if exists else re-determine.
            transforms_dict["size_multiplier"] = transforms_dict.get(
                "size_multiplier", random.uniform(transforms_dict["min_scale"], transforms_dict["max_scale"])
            )  # Use the previous scale_size if exists else re-determine.
            size = (
                int(round(input.shape[1] * transforms_dict["size_multiplier"])),
                int(round(input.shape[2] * transforms_dict["size_multiplier"])),
            )
            M = cv2.getRotationMatrix2D((cx, cy), transforms_dict["angle"], 1.0)
            # Pad image by half its size, then rotate and scale. Select inner input size from the result.
            warped_target = [
                interpolate(
                    T.functional.rotate(
                        t.unsqueeze(0).unsqueeze(0),
                        transforms_dict["angle"],
                        interpolation=T.InterpolationMode.BILINEAR,
                        fill=self.border_mode,
                    ),
                    (size[0], size[1]),
                )
                .squeeze(0)
                .squeeze(0)
                if t is not None
                else None
                for t in target
            ]

            if len(points) == 0:
                warped_points_np = np_points
            else:
                warped_points_np = np.squeeze(cv2.transform(np_points, M), 1)

            warped_points_filtered = np.array([x for i, x in enumerate(warped_points_np) if np_points_enabled[i]])
            if any([t.sum() > 0 for i, t in enumerate(warped_target) if t is not None and enabled_segm_classes[i]]) or (
                len(warped_points_filtered) > 0
                and np.any(
                    np.all(
                        np.logical_and(
                            warped_points_filtered
                            >= [
                                transforms_dict["discard_points_border_px"],
                                transforms_dict["discard_points_border_px"],
                            ],
                            warped_points_filtered
                            < [
                                float(input.shape[2]) - transforms_dict["discard_points_border_px"],
                                float(input.shape[1]) - transforms_dict["discard_points_border_px"],
                            ],
                        ),
                        1,
                    )
                )
            ):
                success = True
                break
        if not success:
            LOG.debug(f"Rank {torch.distributed.get_rank()} Did not succeed getting requested point via RandomRotation")
        warped_input = interpolate(
            T.functional.rotate(
                input,
                transforms_dict["angle"],
                interpolation=T.InterpolationMode.BILINEAR,
                fill=transforms_dict["border_mode"],
            ).unsqueeze(0),
            (size[0], size[1]),
        ).squeeze(0)

        new_points = []
        for i in range(len(warped_points_np)):
            if (
                warped_points_np[i][0] < 0
                or warped_points_np[i][0] >= input.shape[2]
                or warped_points_np[i][1] < 0
                or warped_points_np[i][1] >= input.shape[1]
            ):
                continue
            point = points[i]
            point.x = warped_points_np[i][0] * transforms_dict["size_multiplier"]
            point.y = warped_points_np[i][1] * transforms_dict["size_multiplier"]
            point.r *= transforms_dict["size_multiplier"]
            new_points.append(point)

        return warped_input, warped_target, new_points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return RandomRotation(
            min_scale=transfroms_dict["min_scale"],
            max_scale=transfroms_dict["max_scale"],
            discard_points_border_px=transfroms_dict["discard_points_border_px"],
            border_mode=transfroms_dict["border_mode"],
        )


def select_cropping_point_x_y_ranges(
    x: float, y: float, image_width: int, image_height: int, crop_width: int, crop_height: int, discard_border_px: int
) -> Tuple[int, int, int, int]:
    min_x = max(0, min(x - crop_width + discard_border_px + 1, image_width - crop_width))
    max_x = min(max(0, x - discard_border_px - 1), (image_width - crop_width))
    min_y = max(0, min(y - crop_height + discard_border_px + 1, image_height - crop_height))
    max_y = min(max(0, y - discard_border_px - 1), (image_height - crop_height))
    return (int(min_x), int(max_x), int(min_y), int(max_y))


class RandomCrop:
    def __init__(self, size: Tuple[int, int], discard_points_border_px: int) -> None:
        self.size = size
        self.discard_points_border_px = discard_points_border_px

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "RandomCrop"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "RandomCrop", transforms_dict
            )  # Use the previous rotate_angle if exists else re-determine.

        assert (
            input.shape[2] >= transforms_dict["size"][1]
        ), f"RandomCrop width ({transforms_dict['size'][1]}) is larger than image width ({input.shape[2]})"
        assert (
            input.shape[1] >= transforms_dict["size"][0]
        ), f"RandomCrop height ({transforms_dict['size'][0]}) is larger than image height ({input.shape[1]})"

        success = False

        eligible_points = [
            point
            for point in points
            if (len(enabled_weed_classes) > 0 and point.clz in enabled_weed_classes)
            or (len(enabled_weed_classes) == 0 and point.hit_clz in enabled_hit_classes)
        ]
        if enabled_embedding_bucket is not None:
            eligible_points = [
                point for point in eligible_points if point.deepweed_embedding_bucket == enabled_embedding_bucket
            ]

        eligible_points = [
            p
            for p in eligible_points
            if p.x > transforms_dict["discard_points_border_px"]
            and p.x < input.shape[2] - transforms_dict["discard_points_border_px"]
            and p.y > transforms_dict["discard_points_border_px"]
            and p.y < input.shape[1] - transforms_dict["discard_points_border_px"]
        ]

        if len(eligible_points) > 0:
            random_point = random.choice(eligible_points)
            random_point_x = random_point.x
            random_point_y = random_point.y
            min_x, max_x, min_y, max_y = select_cropping_point_x_y_ranges(
                x=random_point_x,
                y=random_point_y,
                image_width=input.shape[2],
                image_height=input.shape[1],
                crop_width=transforms_dict["size"][1],
                crop_height=transforms_dict["size"][0],
                discard_border_px=transforms_dict["discard_points_border_px"],
            )
        else:
            min_x = 0
            max_x = input.shape[2] - transforms_dict["size"][1]
            min_y = 0
            max_y = input.shape[1] - transforms_dict["size"][0]

        transforms_dict["ox"] = transforms_dict.get(
            "ox", int(random.uniform(min_x, max_x))
        )  # Use the previous condition if exists else re-determine.
        transforms_dict["oy"] = transforms_dict.get(
            "oy", int(random.uniform(min_y, max_y))
        )  # Use the previous condition if exists else re-determine.
        cropped_target = [
            t[
                transforms_dict["oy"] : transforms_dict["oy"] + transforms_dict["size"][0],
                transforms_dict["ox"] : transforms_dict["ox"] + transforms_dict["size"][1],
            ]
            if t is not None
            else None
            for t in target
        ]
        new_points = [
            Point(
                point.x - transforms_dict["ox"],
                point.y - transforms_dict["oy"],
                point.r,
                point.score,
                point.clz,
                point.confidence,
                point.hit_clz,
                comparison_embedding=point.comparison_embedding,
                relocation_info=point.relocation_info,
                deepweed_embedding_bucket=point.deepweed_embedding_bucket,
                uuid=point.uuid,
            )
            for point in points
            if point.x - transforms_dict["discard_points_border_px"] >= transforms_dict["ox"]
            and point.x + transforms_dict["discard_points_border_px"]
            < transforms_dict["ox"] + transforms_dict["size"][1]
            and point.y - transforms_dict["discard_points_border_px"] >= transforms_dict["oy"]
            and point.y + transforms_dict["discard_points_border_px"]
            < transforms_dict["oy"] + transforms_dict["size"][0]
        ]

        if (
            any([t.sum() > 0 for i, t in enumerate(cropped_target) if t is not None and enabled_segm_classes[i]])
            or len(
                [
                    x
                    for x in new_points
                    if (
                        (len(enabled_weed_classes) > 0 and x.clz in enabled_weed_classes)
                        or (len(enabled_weed_classes) == 0 and x.hit_clz in enabled_hit_classes)
                    )
                    and (enabled_embedding_bucket is None or x.deepweed_embedding_bucket == enabled_embedding_bucket)
                ]
            )
            > 0
        ):
            success = True

        if not success and len(eligible_points) > 0:
            LOG.warning(f"Rank {torch.distributed.get_rank()} Did not succeed getting requested point in RandomCrop")

        cropped_input = input[
            :,
            transforms_dict["oy"] : transforms_dict["oy"] + transforms_dict["size"][0],
            transforms_dict["ox"] : transforms_dict["ox"] + transforms_dict["size"][1],
        ]
        return cropped_input, cropped_target, new_points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return RandomCrop(
            size=transfroms_dict["size"], discard_points_border_px=transfroms_dict["discard_points_border_px"]
        )


class ResizePixelsPerInch:
    def __init__(self, ppi: float) -> None:
        self.ppi = ppi

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "ResizePixelsPerInch"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "ResizePixelsPerInch", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        assert image_meta.ppi is not None, f"Image {image_meta.filepath} does not have PPI metadata"
        size_multiplier = transforms_dict["ppi"] / image_meta.ppi
        size = (int(round(input.shape[1] * size_multiplier)), int(round(input.shape[2] * size_multiplier)))
        input = interpolate(input.unsqueeze(0), size).squeeze(0)
        for c in range(len(target)):
            target_c = target[c]
            if target_c is not None:
                target[c] = interpolate(target_c.unsqueeze(0).unsqueeze(0), size).squeeze(0).squeeze(0)
        for i in range(len(points)):
            point = points[i]
            points[i] = Point(
                point.x * size_multiplier,
                point.y * size_multiplier,
                point.r * size_multiplier,
                point.score,
                point.clz,
                point.confidence,
                point.hit_clz,
                comparison_embedding=point.comparison_embedding,
                relocation_info=point.relocation_info,
                deepweed_embedding_bucket=point.deepweed_embedding_bucket,
                uuid=point.uuid,
            )

        return input, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return ResizePixelsPerInch(ppi=transfroms_dict["ppi"])


class PadIfNeeded:
    def __init__(self, size: Tuple[int, int]) -> None:
        self.size = size

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "PadIfNeeded"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "PadIfNeeded", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        pad_width = max(transforms_dict["size"][1] - input.shape[2], 0)
        pad_height = max(transforms_dict["size"][0] - input.shape[1], 0)
        pad_sizes = [
            math.floor(pad_width / 2),
            math.ceil(pad_width / 2),
            math.floor(pad_height / 2),
            math.ceil(pad_height / 2),
        ]
        input = F.pad(input, pad_sizes)

        for c in range(len(target)):
            target_c = target[c]
            if target_c is not None:
                target[c] = F.pad(target_c, pad_sizes)
        for i in range(len(points)):
            point = points[i]
            points[i] = Point(
                point.x + pad_sizes[0],
                point.y + pad_sizes[2],
                point.r,
                point.score,
                point.clz,
                point.confidence,
                point.hit_clz,
                comparison_embedding=point.comparison_embedding,
                relocation_info=point.relocation_info,
                deepweed_embedding_bucket=point.deepweed_embedding_bucket,
                uuid=point.uuid,
            )
        return input, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return PadIfNeeded(size=transfroms_dict["size"])


class Resize:
    def __init__(self, size: Tuple[int, int]) -> None:
        self.size = size

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "Resize"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "Resize", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        initial_size = input.shape[-2], input.shape[-1]
        input = interpolate(input.unsqueeze(0), transforms_dict["size"]).squeeze(0)
        for c in range(len(target)):
            target_c = target[c]
            if target_c is not None:
                target[c] = (
                    interpolate(target_c.unsqueeze(0).unsqueeze(0), transforms_dict["size"]).squeeze(0).squeeze(0)
                )
        size_multiplier_height = transforms_dict["size"][0] / initial_size[0]
        size_multiplier_width = transforms_dict["size"][1] / initial_size[1]
        size_multiplier_r = (size_multiplier_width + size_multiplier_height) / 2
        for i in range(len(points)):
            point = points[i]
            points[i] = Point(
                point.x * size_multiplier_width,
                point.y * size_multiplier_height,
                point.r * size_multiplier_r,
                point.score,
                point.clz,
                point.confidence,
                point.hit_clz,
                comparison_embedding=point.comparison_embedding,
                relocation_info=point.relocation_info,
                deepweed_embedding_bucket=point.deepweed_embedding_bucket,
                uuid=point.uuid,
            )
        return input, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return Resize(size=transfroms_dict["size"])


class RandomVerticalFlip:
    def __init__(self, p: float) -> None:
        self.p = p

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "RandomVerticalFlip"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "RandomVerticalFlip", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        transforms_dict["is_random_vertical_flip"] = transforms_dict.get(
            "is_random_vertical_flip", random.random() < transforms_dict["p"]
        )  # Use the previous condition if exists else re-determine.
        if transforms_dict["is_random_vertical_flip"]:
            input = torch.flip(input, dims=[-2])
            for c in range(len(target)):
                target_c = target[c]
                if target_c is not None:
                    target[c] = torch.flip(target_c, dims=[0])
            target_mask_height = input.shape[-2]
            for i in range(len(points)):
                point = points[i]
                points[i] = Point(
                    point.x,
                    target_mask_height - 1 - point.y,
                    point.r,
                    point.score,
                    point.clz,
                    point.confidence,
                    point.hit_clz,
                    comparison_embedding=point.comparison_embedding,
                    relocation_info=point.relocation_info,
                    deepweed_embedding_bucket=point.deepweed_embedding_bucket,
                    uuid=point.uuid,
                )

        return input, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return RandomVerticalFlip(p=transfroms_dict["p"])


class RandomHorizontalFlip:
    def __init__(self, p: float) -> None:
        self.p = p

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "RandomHorizontalFlip"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "RandomHorizontalFlip", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        transforms_dict["is_random_horizontal_flip"] = transforms_dict.get(
            "is_random_horizontal_flip", random.random() < transforms_dict["p"]
        )  # Use the previous condition if exists else re-determine.
        if transforms_dict["is_random_horizontal_flip"]:
            input = torch.flip(input, dims=[-1])
            for c in range(len(target)):
                target_c = target[c]
                if target_c is not None:
                    target[c] = torch.flip(target_c, dims=[1])
            target_mask_width = input.shape[-1]
            for i in range(len(points)):
                point = points[i]
                points[i] = Point(
                    target_mask_width - 1 - point.x,
                    point.y,
                    point.r,
                    point.score,
                    point.clz,
                    point.confidence,
                    point.hit_clz,
                    comparison_embedding=point.comparison_embedding,
                    relocation_info=point.relocation_info,
                    deepweed_embedding_bucket=point.deepweed_embedding_bucket,
                    uuid=point.uuid,
                )

        return input, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return RandomHorizontalFlip(p=transfroms_dict["p"])


class Normalize(T.Normalize):
    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = {
            key: self.__dict__[key] for key in ["mean", "std", "inplace"] if key in self.__dict__
        }  # Extract the keys that we need only.
        transforms_dict["name"] = "Normalize"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "Normalize", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        assert input.shape[0] in [3, 4], f"Expected CHW/(C+D)HW order: {input.shape}"
        if input.shape[0] == 3:
            depth = torch.zeros((1, input.shape[1], input.shape[2]), device=input.device)
            input = torch.cat((input, depth), 0)
            remove_depth = True
        else:
            remove_depth = False

        normalized_image = super().__call__(input)

        if remove_depth:
            normalized_image = normalized_image[:3, :, :]

        return normalized_image, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return Normalize(mean=transfroms_dict["mean"], std=transfroms_dict["std"])


class DilateMask:
    EPS = 1e-3

    def __init__(self, iterations: int) -> None:
        self.iterations = iterations

    @staticmethod
    def _conv2d(mask: torch.Tensor, size: int) -> Tuple[torch.Tensor, int]:
        w = torch.ones(1, 1, size * 2 + 1, size * 2 + 1).cuda()
        mask = mask.float()  # turn to float for convolving
        mask = mask.unsqueeze(1)  # introduce fake channel
        mask = F.conv2d(mask, w, padding=size)
        mask = mask.squeeze(1)  # swallow fake channel
        return mask, w.numel()

    def __call__(
        self,
        input: torch.Tensor,
        target: List[Optional[torch.Tensor]],
        points: List[Point],
        image_meta: ImageMetadata,
        enabled_weed_classes: Set[str],
        enabled_hit_classes: Set["HitClassValue"],
        enabled_segm_classes: torch.Tensor,
        enabled_embedding_bucket: Optional[int] = None,
        previous_transforms: Optional[Dict[str, Any]] = None,
    ) -> Tuple[torch.Tensor, List[Optional[torch.Tensor]], List[Point], Dict[str, Any]]:
        transforms_dict = deepcopy(self.__dict__)
        transforms_dict["name"] = "DilateMask"
        if previous_transforms:
            transforms_dict = previous_transforms.get(
                "DilateMask", transforms_dict
            )  # Use the previous transform if exists else self.__dict__.

        for c in range(len(target)):
            target_c = target[c]
            if target_c is not None:
                dilated_mask = target_c.unsqueeze(0)
                for _ in range(transforms_dict["iterations"]):
                    dilated_mask, _ = self._conv2d(dilated_mask, size=1)
                    dilated_mask = dilated_mask > DilateMask.EPS
                target[c] = 0.5 * target_c + 0.5 * dilated_mask.squeeze(0)
        return input, target, points, transforms_dict

    @staticmethod
    def from_dict(transfroms_dict: Dict[str, Any]) -> T.Compose:
        return DilateMask(iterations=transfroms_dict["iterations"])

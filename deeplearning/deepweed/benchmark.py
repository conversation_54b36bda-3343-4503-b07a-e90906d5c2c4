from typing import Any, Dict, Optional

import fire
import torch

from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.model import Deepweed
from deeplearning.utils.model_benchmark import ModelBenchmark


class DeepweedBenchmark(ModelBenchmark):
    _TRT_MODEL_FILE = "deepweed_resnet50.trt"

    def __init__(
        self,
        fp16: bool = False,
        int8: bool = False,
        batch_size: int = 1,
        height: int = 1696,
        width: int = 1800,
        discard_points_border_px: float = 100,
        num_weed_point_classes: int = 5,
        num_segm_classes: int = 1,
        num_crop_ids: int = 100,
        enable_crop_embeddings: bool = False,
        config_dict: Optional[Dict[str, Any]] = None,
        fixed_crop_idx: bool = False,
    ) -> None:
        config_dict = config_dict if config_dict is not None else {}
        model: Any = Deepweed(
            num_weed_point_classes=num_weed_point_classes,
            num_segm_classes=num_segm_classes,
            num_crop_ids=num_crop_ids,
            enable_crop_embeddings=enable_crop_embeddings,
            discard_points_border_px=discard_points_border_px,
            config=DeepweedConfig.from_dict(config_dict),
            fixed_crop_idx=0 if fixed_crop_idx else None,
        )

        test_tensor = torch.rand(batch_size, 3, height, width).cuda()
        if enable_crop_embeddings and not fixed_crop_idx:
            test_crop_id_idx = (torch.rand(batch_size) * num_crop_ids).int().cuda()
            test_input = [test_tensor, test_crop_id_idx]
        else:
            test_input = [test_tensor]

        super(DeepweedBenchmark, self).__init__(
            fp16,
            int8,
            batch_size,
            test_input,
            model,
            DeepweedBenchmark._TRT_MODEL_FILE,
            use_implicit_batch_dimension_trt=False,
        )


if __name__ == "__main__":
    fire.Fire(DeepweedBenchmark)

import functools
import logging
from typing import Any, Callable, Dict, Optional, Tuple

from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.remote_veselka_dataset.remote_dataset import RemoteVeselkaDataset
from deeplearning.dl_metrics.dl_metrics.points_db import EmbeddingType
from deeplearning.utils.dataset import DatasetType

LOG = logging.getLogger(__name__)


def initialize_datasets(
    config_dict: Dict[str, Any],
    train_filepath: str,
    validation_filepath: str,
    test_filepath: str,
    num_samples: Optional[int] = None,
    camera: Optional[str] = None,
    train_ppi: Optional[int] = None,
    dilate_mask: int = 0,
    segm_classes: Optional[Tuple[str, ...]] = None,
    test_segm_classes: Optional[Tuple[str, ...]] = None,
    weed_classes: Optional[Tuple[str, ...]] = None,
    crop_classes: Optional[Tuple[str, ...]] = None,
    keep_low_confidence: bool = True,
    seed: int = 1,
    goal_percentage_new: Optional[float] = None,
    new_loss_multiplier: float = 1.0,
    positive_sample_percentage: float = 10 / 11,
    recency_split_age: Optional[int] = None,
    recency_split: Optional[int] = None,
    is_new_func: Optional[Callable[[Dict[str, Any]], bool]] = None,
    duplicate_new_data: Optional[float] = None,
    geohash_max_precision: int = 0,
    geohash_min_precision: int = 0,
    use_date_groups: bool = False,
    calibration_dataset_size: int = 1024,
    embedding_balancing_evaluation_path: Optional[str] = None,
    embedding_type: EmbeddingType = EmbeddingType.REDUCED_SCALED,
    embedding_balancing_on_clusters: bool = False,
) -> Tuple[RemoteVeselkaDataset, RemoteVeselkaDataset, RemoteVeselkaDataset, RemoteVeselkaDataset]:
    config = DeepweedConfig.from_json(config_dict)
    veselka_dataset_initializer = functools.partial(
        RemoteVeselkaDataset,
        config,
        segm_classes=segm_classes,
        weed_classes=weed_classes,
        crop_classes=crop_classes,
        camera=camera,
        train_ppi=train_ppi,
        dilate_mask=dilate_mask,
        keep_low_confidence=keep_low_confidence,
        seed=seed,
        geohash_max_precision=geohash_max_precision,
        geohash_min_precision=geohash_min_precision,
        use_date_groups=use_date_groups,
    )

    train_dataset = veselka_dataset_initializer(
        train_filepath,
        mode=DatasetType.TRAIN,
        num_samples=num_samples,
        goal_percentage_new=goal_percentage_new,
        duplicate_new_data=duplicate_new_data,
        new_loss_multiplier=new_loss_multiplier,
        positive_sample_percentage=positive_sample_percentage,
        recency_split=recency_split,
        recency_split_age=recency_split_age,
        is_new_func=is_new_func,
        embedding_balancing_evaluation_path=embedding_balancing_evaluation_path,
        embedding_type=embedding_type,
        embedding_balancing_on_clusters=embedding_balancing_on_clusters,
    )
    validation_dataset = veselka_dataset_initializer(
        validation_filepath,
        mode=DatasetType.VALIDATION,
        new_loss_multiplier=new_loss_multiplier,
        recency_split=recency_split,
        recency_split_age=recency_split_age,
        is_new_func=is_new_func,
    )
    test_dataset = veselka_dataset_initializer(
        test_filepath,
        mode=DatasetType.TEST,
        new_loss_multiplier=new_loss_multiplier,
        segm_classes=test_segm_classes,
        recency_split=recency_split,
        recency_split_age=recency_split_age,
        is_new_func=is_new_func,
    )
    calibration_dataset = veselka_dataset_initializer(
        test_filepath,
        mode=DatasetType.CALIBRATION,
        num_samples=calibration_dataset_size,
        new_loss_multiplier=new_loss_multiplier,
        embedding_balancing_evaluation_path=embedding_balancing_evaluation_path,
        embedding_type=embedding_type,
        embedding_balancing_on_clusters=embedding_balancing_on_clusters,
    )

    return train_dataset, validation_dataset, test_dataset, calibration_dataset

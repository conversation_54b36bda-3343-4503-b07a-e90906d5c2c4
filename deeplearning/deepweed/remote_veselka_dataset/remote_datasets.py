import logging
from typing import List

import torch

from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.datapoint_timestamps import DatapointTimestamps
from deeplearning.deepweed.remote_veselka_dataset.client import RemoteVeselkaDatasetClient
from deeplearning.utils.dataset import DatasetType
from deeplearning.utils.images import denormalize_image

LOG = logging.getLogger(__name__)


class RemoteVeselkaDatasets:
    def __init__(self, config: DeepweedConfig, host: str = "localhost", port: int = 8000) -> None:

        self.train_dataset = RemoteVeselkaDatasetClient(
            DatasetType.TRAIN, config, host=host, port=port, model_id=config.model_id
        )
        self.validation_dataset = RemoteVeselkaDatasetClient(
            DatasetType.VALIDATION, config, host=host, port=port, model_id=config.model_id
        )
        self.test_dataset = RemoteVeselkaDatasetClient(
            DatasetType.TEST, config, host=host, port=port, model_id=config.model_id
        )
        self.calibration_dataset = RemoteVeselkaDatasetClient(
            DatasetType.CALIBRATION, config, host=host, port=port, model_id=config.model_id
        )

        self.train_ppi = self.train_dataset.get_train_ppi()
        self.discard_point_border_px = self.train_dataset.get_discard_points_border_px()

    def get_training(self) -> RemoteVeselkaDatasetClient:
        return self.train_dataset

    def get_validation(self) -> RemoteVeselkaDatasetClient:
        return self.validation_dataset

    def get_test(self) -> RemoteVeselkaDatasetClient:
        return self.test_dataset

    def get_calibration(self) -> RemoteVeselkaDatasetClient:
        return self.calibration_dataset

    def denormalize_image(self, img: torch.Tensor) -> torch.Tensor:
        return denormalize_image(img)

    @property
    def new_data_captured_ats(self) -> List[DatapointTimestamps]:
        captured_ats = self.train_dataset.new_data_captured_ats.copy()
        captured_ats.extend(self.validation_dataset.new_data_captured_ats)
        captured_ats.extend(self.test_dataset.new_data_captured_ats)

        return captured_ats

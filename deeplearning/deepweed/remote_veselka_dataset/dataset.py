import logging
from multiprocessing import Process
from threading import Thread
from typing import Any, Callable, Dict, Optional, Tuple

from _thread import interrupt_main
from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.constants import REMOTE_VESELKA_DATASET_SERVER_PORT
from deeplearning.deepweed.remote_veselka_dataset.remote_datasets import RemoteVeselkaDatasets
from deeplearning.deepweed.remote_veselka_dataset.server import RemoteVeselkaDatasetServer
from deeplearning.deepweed.remote_veselka_dataset.utils import initialize_datasets
from deeplearning.dl_metrics.dl_metrics.points_db import EmbeddingType
from deeplearning.utils.dataset import DatasetType

LOG = logging.getLogger(__name__)


class VeselkaDataset:
    def __init__(
        self,
        config: DeepweedConfig,
        train_filepath: str,
        validation_filepath: str,
        test_filepath: str,
        num_samples: int,
        camera: Optional[str] = None,
        train_ppi: Optional[int] = None,
        dilate_mask: int = 0,
        segm_classes: Optional[Tuple[str, ...]] = None,
        test_segm_classes: Optional[Tuple[str, ...]] = None,
        weed_classes: Optional[Tuple[str, ...]] = None,
        crop_classes: Optional[Tuple[str, ...]] = None,
        keep_low_confidence: bool = True,
        seed: int = 1,
        goal_percentage_new: Optional[float] = None,
        new_loss_multiplier: float = 1.0,
        positive_sample_percentage: float = 10 / 11,
        recency_split_age: Optional[int] = None,
        recency_split: Optional[int] = None,
        is_new_func: Optional[Callable[[Dict[str, Any]], bool]] = None,
        duplicate_new_data: Optional[float] = None,
        geohash_max_precision: int = 0,
        geohash_min_precision: int = 0,
        use_date_groups: bool = False,
        calibration_dataset_size: int = 1024,
        embedding_balancing_evaluation_path: Optional[str] = None,
        host: str = "localhost",
        port: int = REMOTE_VESELKA_DATASET_SERVER_PORT,
        embedding_type: EmbeddingType = EmbeddingType.REDUCED_SCALED,
        embedding_balancing_on_clusters: bool = False,
    ):
        self._remote_dataset_process: Optional[Process] = Process(
            target=self.init_dataset_server,
            kwargs={
                "config": config,
                "train_filepath": train_filepath,
                "validation_filepath": validation_filepath,
                "test_filepath": test_filepath,
                "num_samples": num_samples,
                "camera": camera,
                "train_ppi": train_ppi,
                "dilate_mask": dilate_mask,
                "segm_classes": segm_classes,
                "test_segm_classes": test_segm_classes,
                "weed_classes": weed_classes,
                "crop_classes": crop_classes,
                "keep_low_confidence": keep_low_confidence,
                "seed": seed,
                "goal_percentage_new": goal_percentage_new,
                "new_loss_multiplier": new_loss_multiplier,
                "positive_sample_percentage": positive_sample_percentage,
                "recency_split_age": recency_split_age,
                "recency_split": recency_split,
                "is_new_func": is_new_func,
                "duplicate_new_data": duplicate_new_data,
                "geohash_max_precision": geohash_max_precision,
                "geohash_min_precision": geohash_min_precision,
                "use_date_groups": use_date_groups,
                "calibration_dataset_size": calibration_dataset_size,
                "embedding_balancing_evaluation_path": embedding_balancing_evaluation_path,
                "port": port,
                "embedding_type": embedding_type,
                "embedding_balancing_on_clusters": embedding_balancing_on_clusters,
            },
        )
        self._remote_dataset_process.start()

        self._remote_dataset_process_thread = Thread(target=self.monitor_process)
        self._remote_dataset_process_thread.start()

        self._datasets = RemoteVeselkaDatasets(config, host=host, port=port)

    def init_dataset_server(
        self,
        config: DeepweedConfig,
        train_filepath: str,
        validation_filepath: str,
        test_filepath: str,
        num_samples: int,
        camera: Optional[str] = None,
        train_ppi: Optional[int] = None,
        dilate_mask: int = 0,
        segm_classes: Optional[Tuple[str, ...]] = None,
        test_segm_classes: Optional[Tuple[str, ...]] = None,
        weed_classes: Optional[Tuple[str, ...]] = None,
        crop_classes: Optional[Tuple[str, ...]] = None,
        keep_low_confidence: bool = True,
        seed: int = 1,
        goal_percentage_new: Optional[float] = None,
        new_loss_multiplier: float = 1.0,
        positive_sample_percentage: float = 10 / 11,
        recency_split_age: Optional[int] = None,
        recency_split: Optional[int] = None,
        is_new_func: Optional[Callable[[Dict[str, Any]], bool]] = None,
        duplicate_new_data: Optional[float] = None,
        geohash_max_precision: int = 0,
        geohash_min_precision: int = 0,
        use_date_groups: bool = False,
        calibration_dataset_size: int = 1024,
        embedding_balancing_evaluation_path: Optional[str] = None,
        port: int = REMOTE_VESELKA_DATASET_SERVER_PORT,
        embedding_type: EmbeddingType = EmbeddingType.REDUCED_SCALED,
        embedding_balancing_on_clusters: bool = False,
    ) -> None:
        train_dataset, validation_dataset, test_dataset, calibration_dataset = initialize_datasets(
            config_dict=config.to_json(),
            train_filepath=train_filepath,
            validation_filepath=validation_filepath,
            test_filepath=test_filepath,
            num_samples=num_samples,
            camera=camera,
            train_ppi=train_ppi,
            dilate_mask=dilate_mask,
            segm_classes=segm_classes,
            test_segm_classes=test_segm_classes,
            weed_classes=weed_classes,
            crop_classes=crop_classes,
            keep_low_confidence=keep_low_confidence,
            seed=seed,
            goal_percentage_new=goal_percentage_new,
            new_loss_multiplier=new_loss_multiplier,
            positive_sample_percentage=positive_sample_percentage,
            recency_split_age=recency_split_age,
            recency_split=recency_split,
            is_new_func=is_new_func,
            duplicate_new_data=duplicate_new_data,
            geohash_max_precision=geohash_max_precision,
            geohash_min_precision=geohash_min_precision,
            use_date_groups=use_date_groups,
            calibration_dataset_size=calibration_dataset_size,
            embedding_balancing_evaluation_path=embedding_balancing_evaluation_path,
            embedding_type=embedding_type,
            embedding_balancing_on_clusters=embedding_balancing_on_clusters,
        )

        remote_dataset = RemoteVeselkaDatasetServer(
            {
                DatasetType.TRAIN: train_dataset,
                DatasetType.VALIDATION: validation_dataset,
                DatasetType.TEST: test_dataset,
                DatasetType.CALIBRATION: calibration_dataset,
            }
        )
        remote_dataset.run(port=port)

    def monitor_process(self) -> None:
        if not self._remote_dataset_process:
            return
        remote_dataset_process = self._remote_dataset_process
        remote_dataset_process.join()
        if remote_dataset_process.exitcode is not None and abs(remote_dataset_process.exitcode) != 15:
            interrupt_main()

    @property
    def datasets(self) -> RemoteVeselkaDatasets:
        return self._datasets

    def shutdown(self) -> None:
        if self._remote_dataset_process:
            self._remote_dataset_process.terminate()
            self._remote_dataset_process = None

from typing import Any, Dict, List

from lib.common.veselka.client import VeselkaClient


def get_reverse_sorted_evaluation_ids_by_model_id(model_id: str) -> List[str]:
    evaluations = get_reverse_sorted_evaluations_by_model_id(model_id)
    return [evaluation["id"] for evaluation in evaluations]


def get_reverse_sorted_evaluations_by_model_id(model_id: str) -> List[Dict[str, Any]]:
    client = VeselkaClient()
    evaluations: List[Dict[str, Any]] = client.get_evaluations_by_model_id(model_id)
    evaluations.sort(reverse=True, key=lambda x: x["created"])
    return evaluations

syntax = "proto3";

package carbon.frontend.category;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";
import "category/proto/category.proto";

message GetNextCategoryDataRequest { carbon.frontend.util.Timestamp ts = 1; }

message GetNextCategoryDataResponse {
  carbon.frontend.util.Timestamp ts = 1;
  repeated carbon.category.Category categories = 2;
}


service CategoryService {
  rpc GetNextCategoryData(GetNextCategoryDataRequest)
      returns (GetNextCategoryDataResponse);
}

syntax = "proto3";

package carbon.frontend.debug;
option go_package = "proto/frontend";

import "frontend/proto/alarm.proto";
import "frontend/proto/util.proto";

message Message {
  int64 id = 1;
  carbon.frontend.util.Timestamp ts = 2;
  string message = 3;
  bool from_support = 4;
  bool read = 5;
  string author_user_id = 6;
  int64 author_robot_id = 7;
  string recipient_user_id = 8;
  int64 recipient_customer_id = 9;
  int64 recipient_robot_id = 10;
}

message MessageRequest {
  string message = 1;
}

message MessagesRequest {
  carbon.frontend.util.Timestamp ts = 1;
}

message MessagesResponse {
  carbon.frontend.util.Timestamp ts = 1;
  repeated Message messages = 2;
}
message ReadRequest { int64 id = 1; }

service MessagesService {
  rpc ReadMessage(ReadRequest) returns (carbon.frontend.util.Empty);
  rpc SendMessage(MessageRequest) returns (carbon.frontend.util.Empty);
  rpc GetNextMessages(carbon.frontend.util.Timestamp)
      returns (MessagesResponse);
}
syntax = "proto3";

package carbon.frontend.model;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";

message Model {
    string id = 1;
    string crop = 2;
    carbon.frontend.util.Timestamp ts = 3;
    bool custom = 4;
    bool pinned = 5;
    bool active = 6;
    bool synced = 7;
    repeated bool synced_to_rows = 8;
    bool downloading = 9;
    string type = 10;
    carbon.frontend.util.Timestamp last_used_timestamp = 11;
    float downloading_progress = 12;
    uint64 estimated_downloading_remaining_time_ms = 13;
    carbon.frontend.util.Timestamp downloaded_timestamp = 14;
    bool recommended = 15;
    repeated string viable_crop_ids = 16;
    bool maintained = 17;
    string nickname = 18;
}

message SelectCropRequest {
    string crop_id = 1;
}

message ListCropParameters {
    string lang = 1;
}

message EnabledCrop {
    string id = 1;
    int64 created = 2;
    string carbon_name = 3 [ deprecated = true ]; // Deprecated
    string common_name = 4;
    string description = 5;
    string notes = 6;
    string pinned_model_id = 7;
    string recommended_model = 8;
}

message EnabledCropList {
    repeated EnabledCrop enabledCrops = 1;
}

message GetNextSelectedCropIDResponse {
    carbon.frontend.util.Timestamp ts = 1;
    string crop_id = 2;
}

message PinModelRequest {
    string id = 1;
    string crop = 2 [ deprecated = true ]; // Deprecated, use crop_id
    bool allow_pinned_crop_override = 3;
    string crop_id = 4;
    bool p2p = 5;
}

message UnpinModelRequest {
    string crop = 1 [ deprecated = true ]; // Deprecated, use crop_id
    string crop_id = 4;
    bool p2p = 5;
}

message GetNextModelStateRequest {
    string crop = 1 [ deprecated = true ]; // Deprecated, use crop_id
    carbon.frontend.util.Timestamp ts = 2;
    string crop_id = 3;
}

message GetNextModelStateResponse {
    repeated Model models = 1;
    carbon.frontend.util.Timestamp ts = 2;
    string current_p2p_model_id = 3;
    string current_deepweed_model_id = 4;
}

message DownloadModelRequest {
    string model_id = 1;
}

message ModelHistoryRequest {
    int64 start_timestamp = 1;
    int64 count = 2;
    bool reverse = 3;
    ModelEvent match_filter = 4;
    carbon.frontend.util.Timestamp ts = 5;
    ModelEventTypeMatcher event_type_matcher = 6;
}

message ModelEvent {
    string type = 1;
    string model_id = 2;
    string model_type = 3;
    string crop_id = 4;
    string job_name = 5;
    int64 time = 6;
    string model_nickname = 7;
    string model_parameters = 8;
}

message ModelEventTypeMatcher {
    bool robot_start = 1;
    bool pinned = 2;
    bool unpinned = 3;
    bool recommended = 4;
    bool activated = 5;
    bool nickname_change = 6;
    bool nickname_delete = 7;
    bool default_parameter_change = 8;
    bool parameter_change = 9;
}

message ModelHistoryResponse {
    repeated ModelEvent events = 1;
    carbon.frontend.util.Timestamp ts = 5;
}

message GetModelNicknamesRequest {
    repeated string model_ids = 1;
    carbon.frontend.util.Timestamp ts = 5;
}

message GetModelNicknamesResponse {
    map<string, string> model_nicknames = 1;
    carbon.frontend.util.Timestamp ts = 5;
}

message SetModelNicknameRequest {
    string model_id = 1;
    string model_nickname = 2;
}

message CropModelPair {
    string crop_id = 1;
    string model_id = 2;
}

message RefreshDefaultModelParametersRequest {
    repeated CropModelPair cropModelPairs = 1;
}

message SyncCropIDsRequest {
    bool force_cache_refresh = 1;
}

message GetNextEnabledCropsRequest {
    carbon.frontend.util.Timestamp ts = 1;
    string lang = 2;
}

message GetNextEnabledCropsResponse {
    repeated EnabledCrop enabledCrops = 1;
    carbon.frontend.util.Timestamp ts = 2;
}

message GetNextCaptureCropsRequest {
    carbon.frontend.util.Timestamp ts = 1;
    string lang = 2;
}

message GetNextCaptureCropsResponse {
    repeated EnabledCrop enabledCrops = 1;
    carbon.frontend.util.Timestamp ts = 2;
}

service ModelService {
    rpc PinModel(PinModelRequest) returns (carbon.frontend.util.Empty);
    rpc UnpinModel(UnpinModelRequest) returns (carbon.frontend.util.Empty);
    rpc GetNextModelState(GetNextModelStateRequest) returns (GetNextModelStateResponse);
    rpc GetNextAllModelState(GetNextModelStateRequest) returns (GetNextModelStateResponse);
    rpc UpdateModel(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc ListEnabledCrops(ListCropParameters) returns (EnabledCropList);
    rpc GetNextEnabledCrops(GetNextEnabledCropsRequest) returns (GetNextEnabledCropsResponse);
    rpc ListCaptureCrops(ListCropParameters) returns (EnabledCropList);
    rpc GetNextCaptureCrops(GetNextCaptureCropsRequest) returns (GetNextCaptureCropsResponse);
    rpc GetNextSelectedCropID(carbon.frontend.util.Timestamp) returns (GetNextSelectedCropIDResponse);
    rpc SelectCrop(SelectCropRequest) returns (carbon.frontend.util.Empty);
    rpc DownloadModel(DownloadModelRequest) returns (carbon.frontend.util.Empty);
    rpc GetNextModelHistory(ModelHistoryRequest) returns (ModelHistoryResponse);
    rpc GetModelHistory(ModelHistoryRequest) returns (ModelHistoryResponse);
    rpc GetModelNicknames(GetModelNicknamesRequest) returns (GetModelNicknamesResponse);
    rpc GetNextModelNicknames(GetModelNicknamesRequest) returns (GetModelNicknamesResponse);
    rpc SetModelNickname(SetModelNicknameRequest) returns (carbon.frontend.util.Empty);
    rpc RefreshDefaultModelParameters(RefreshDefaultModelParametersRequest) returns (carbon.frontend.util.Empty);
    rpc SyncCropIDs(SyncCropIDsRequest) returns (carbon.frontend.util.Empty);
    rpc TriggerDownload(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
}

syntax = "proto3";

package carbon.frontend.actuation_tasks;
option go_package = "proto/frontend";

import "core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto";
import "frontend/proto/util.proto";

message GlobalAimbotActuationTaskRequest {
    uint32 row_id = 1; // 0 means all of them
    aimbot.ActuationTaskRequest task = 2;
}

message GlobalActuationTaskState {
    carbon.frontend.util.Timestamp ts = 1;
    bool running = 2;
    uint32 elapsed_time_ms = 3;
    uint32 expected_time_ms = 4;
}

service ActuationTasksService {
    rpc GetNextGlobalActuationTaskState(carbon.frontend.util.Timestamp) returns (GlobalActuationTaskState);
    rpc StartGlobalAimbotActuationTask(GlobalAimbotActuationTaskRequest) returns (carbon.frontend.util.Empty);
    rpc CancelGlobalAimbotActuationTask(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
}
syntax = "proto3";

package carbon.frontend.crosshair;
option go_package = "proto/frontend";

import "frontend/proto/camera.proto";
import "frontend/proto/util.proto";

message CrosshairPosition {
    float x = 1;
    float y = 2;
}

message CrosshairPositionState {
    carbon.frontend.util.Timestamp ts = 1;
    CrosshairPosition pos = 2;
    bool calibrating = 3;
    bool calibration_failed = 4;
}

message CrosshairPositionRequest {
    string cam_id = 1;
    carbon.frontend.util.Timestamp ts = 2;
}

message SetCrosshairPositionRequest {
    string cam_id = 1;
    CrosshairPosition pos = 2;
}

message MoveScannerRequest {
    string cam_id = 1;
    float x = 2;
    float y = 3;
}
message AutoCrossHairCalStateRequest {
    carbon.frontend.util.Timestamp ts = 1;
}
message AutoCrossHairCalStateResponse {
    carbon.frontend.util.Timestamp ts = 1;
    bool in_progress = 2;
    float progress = 3;
    repeated string failed = 4;
}

service CrosshairService {
    rpc StartAutoCalibrateCrosshair(carbon.frontend.camera.CameraRequest) returns (carbon.frontend.util.Empty);
    rpc StartAutoCalibrateAllCrosshairs(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc StopAutoCalibrate(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc GetNextCrosshairState(CrosshairPositionRequest) returns (CrosshairPositionState);
    rpc SetCrosshairPosition(SetCrosshairPositionRequest) returns (carbon.frontend.util.Empty);
    rpc MoveScanner(MoveScannerRequest) returns (carbon.frontend.util.Empty);
    rpc GetNextAutoCrossHairCalState(AutoCrossHairCalStateRequest) returns (AutoCrossHairCalStateResponse);
}
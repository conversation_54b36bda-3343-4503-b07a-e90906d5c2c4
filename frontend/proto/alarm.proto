syntax = "proto3";

package carbon.frontend.alarm;
option go_package = "proto/frontend";

import "frontend/proto/translation.proto";
import "frontend/proto/util.proto";

enum AlarmLevel {
    AL_UNKNOWN = 0;
    AL_CRITICAL = 1;
	AL_HIGH = 2;
    AL_MEDIUM = 3;
    AL_LOW = 4;
	AL_HIDDEN = 5;
}

enum AlarmImpact {
	AI_UNKNOWN = 0;
	AI_CRITICAL = 1;
	AI_OFFLINE = 2;
	AI_DEGRADED = 3;
	AI_NONE = 4;
}

message AlarmRow {
    int64 timestamp_ms = 1;
    string alarm_code = 2;
    string subsystem = 3;
    string description = 4;
    AlarmLevel level = 5;
    string identifier = 6;
    bool acknowledged = 7;
	AlarmImpact impact = 8;
    int64 stop_timestamp_ms = 9;
    bool autofix_available = 10;
    bool autofix_attempted = 11;
    uint32 autofix_duration_sec = 12;
    string description_key = 13;
    repeated translation.TranslationParameter translation_parameters = 14;
}

message AlarmTable {
    carbon.frontend.util.Timestamp ts = 1;
    repeated AlarmRow alarms = 2;
}

message AlarmCount {
    carbon.frontend.util.Timestamp ts = 1;
    uint32 count = 2;
}

message AcknowledgeRequest {
    string identifier = 1;
}

message GetNextAlarmLogRequest {
    int32 from_idx = 1;
    int32 to_idx = 2;
    carbon.frontend.util.Timestamp ts = 3;
    bool visible_only = 4;
}

message GetNextAlarmLogResponse {
    repeated AlarmRow alarms = 1;
    carbon.frontend.util.Timestamp ts = 2;
}

message GetNextAlarmLogCountRequest {
    carbon.frontend.util.Timestamp ts = 1;
    bool visible_only = 2;
}

message GetNextAlarmLogCountResponse {
    int32 num_alarms = 1;
    carbon.frontend.util.Timestamp ts = 2;
}

message AttemptAutofixAlarmRequest {
    string identifier = 1;
}

message GetNextAutofixAlarmStatusRequest {
    carbon.frontend.util.Timestamp ts = 1;
}

message GetNextAutofixAlarmStatusResponse {
    carbon.frontend.util.Timestamp ts = 1;
    bool completed = 2;
    string error_message = 3;
}

service AlarmService {
    rpc GetNextAlarmList(carbon.frontend.util.Timestamp) returns (AlarmTable);
    rpc GetNextAlarmCount(carbon.frontend.util.Timestamp) returns (AlarmCount);
    rpc GetNextNewAlarmList(carbon.frontend.util.Timestamp) returns (AlarmTable);
    rpc AcknowledgeAlarm(AcknowledgeRequest) returns (carbon.frontend.util.Empty);
    rpc ResetAlarms(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc GetNextAlarmLog(GetNextAlarmLogRequest) returns (GetNextAlarmLogResponse);
    rpc GetNextAlarmLogCount(GetNextAlarmLogCountRequest) returns (GetNextAlarmLogCountResponse);
    rpc AttemptAutofixAlarm(AttemptAutofixAlarmRequest) returns (carbon.frontend.util.Empty);
    rpc GetNextAutofixAlarmStatus(GetNextAutofixAlarmStatusRequest) returns (GetNextAutofixAlarmStatusResponse);
}

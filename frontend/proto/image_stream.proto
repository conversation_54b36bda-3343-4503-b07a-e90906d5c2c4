syntax = "proto3";

package carbon.frontend.image_stream;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";
import "weed_tracking/proto/weed_tracking.proto";

message Annotations {
    weed_tracking.Detections detections = 1;
    weed_tracking.Bands bands = 2;
    int32 crosshair_x = 3;
    int32 crosshair_y = 4;
}

message Image {
    carbon.frontend.util.Timestamp ts = 1;
    uint32 width = 2;
    uint32 height = 3;
    double focus = 4;
    bytes data = 5;
    Annotations annotations = 6;
}

message CameraImageRequest {
    string cam_id = 1;
    carbon.frontend.util.Timestamp ts = 2;
    bool annotated = 3;
    bool include_annotations_metadata = 4;
    bool dont_downsample = 5;
    bool encode_as_png = 6;
    bool encode_as_raw = 7;
}

message GetPredictImageByTimestampRequest {
    string cam_id = 1;
    carbon.frontend.util.Timestamp ts = 2;
    int32 crop_around_x = 3;
    int32 crop_around_y = 4;
}

message GetPredictImageByTimestampResponse {
    bytes data = 1;
    int32 center_x = 2;
    int32 center_y = 3;
}

message PossiblePerspective {
    carbon.frontend.util.Timestamp ts = 1;
    int32 crop_around_x = 2;
    int32 crop_around_y = 3;
}

message GetMultiPredictPerspectivesRequest {
    string cam_id = 1;
    repeated PossiblePerspective perspectives = 2;
    int32 requested_perspectives = 3;
}

message CentroidPerspective {
    carbon.frontend.util.Timestamp ts = 1;
    int32 center_x = 2;
    int32 center_y = 3;
    bytes data = 4;
}

message GetMultiPredictPerspectivesResponse {
    repeated CentroidPerspective perspectives = 1; 
}

service ImageStreamService {
    rpc GetNextCameraImage(CameraImageRequest) returns (Image);
    rpc GetPredictImageByTimestamp(GetPredictImageByTimestampRequest) returns (GetPredictImageByTimestampResponse);
    rpc GetMultiPredictPerspectives(GetMultiPredictPerspectivesRequest) returns (GetMultiPredictPerspectivesResponse);
}
syntax = "proto3";

package carbon.frontend.features;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";

enum Generation {
    Undefined = 0;
    Slayer = 1;
    Reaper = 2;
}

message RowConfiguration {
    int32 num_predicts = 1;
    int32 num_targets = 2;
}

message RobotConfiguration {
    int32 num_rows = 1;
    map<int32, RowConfiguration> row_configuration = 2;
    Generation generation = 3;
}

service FeatureService {
    rpc GetNextFeatureFlags(carbon.frontend.util.Timestamp) returns (carbon.frontend.util.FeatureFlags);
    rpc GetRobotConfiguration(carbon.frontend.util.Empty) returns (RobotConfiguration);
}
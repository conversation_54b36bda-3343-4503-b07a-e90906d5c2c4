syntax = "proto3";

package carbon.frontend.startup_task;
option go_package = "proto/frontend";

import "proto/startup_task/startup_task.proto";
import "frontend/proto/util.proto";

message GetNextTasksResponse {
  carbon.frontend.util.Timestamp ts = 1;
  repeated carbon.startup_task.Task tasks = 2;
}

message MarkTaskCompleteRequest {
  string task_id = 1;
}
message MarkTaskCompleteResponse {
}

service StartupTaskService {
    rpc GetNextTasks(carbon.frontend.util.Timestamp) returns (GetNextTasksResponse);
    rpc MarkTaskComplete(MarkTaskCompleteRequest) returns (MarkTaskCompleteResponse);
}
syntax = "proto3";

package carbon.frontend.target_velocity_estimator;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";
import "proto/target_velocity_estimator/target_velocity_estimator.proto";
message GetNextAvailableTVEProfilesResponse {
  carbon.frontend.util.Timestamp ts = 1;
  repeated carbon.aimbot.target_velocity_estimator.ProfileDetails profiles = 2;
}
message GetNextActiveTVEProfileResponse {
  carbon.frontend.util.Timestamp ts = 1;
  carbon.aimbot.target_velocity_estimator.TVEProfile profile = 2;
}
message LoadTVEProfileRequest {
  string id = 1;
}
message LoadTVEProfileResponse {
  carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
}
message SaveTVEProfileRequest {
  carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
  bool set_active = 2;
}
message SaveTVEProfileResponse {
  string id = 1;
}
message SetActiveTVEProfileRequest {
  string id = 1;
}
message SetActiveTVEProfileResponse {
}
message DeleteTVEProfileRequest {
  string id = 1;
  string new_active_id = 2; // Only looked at if id is currently active, and this must be a valid id in such case else can be empty
}
message DeleteTVEProfileResponse {
}

service TargetVelocityEstimatorService {
  rpc GetNextAvailableProfiles(carbon.frontend.util.Timestamp) returns (GetNextAvailableTVEProfilesResponse);
  rpc GetNextActiveProfile(carbon.frontend.util.Timestamp) returns (GetNextActiveTVEProfileResponse);
  rpc LoadProfile(LoadTVEProfileRequest) returns (LoadTVEProfileResponse);
  rpc SaveProfile(SaveTVEProfileRequest) returns (SaveTVEProfileResponse);
  rpc SetActive(SetActiveTVEProfileRequest) returns (SetActiveTVEProfileResponse);
  rpc DeleteProfile(DeleteTVEProfileRequest) returns (DeleteTVEProfileResponse);
}
syntax = "proto3";

package carbon.frontend.thinning;
option go_package = "proto/frontend";

import "proto/thinning/thinning.proto";
import "frontend/proto/util.proto";

enum ThinningConfVer {
  THIN_CONF_V1 = 0;
  THIN_CONF_V2 = 1;
}

message GetNextConfigurationsResponse {
  carbon.frontend.util.Timestamp ts = 1;
  repeated carbon.thinning.ConfigDefinition  definitions = 2;
  string active_id = 3;
}
message GetNextActiveConfResponse {
  carbon.frontend.util.Timestamp ts = 1;
  string  name = 2 [ deprecated = true ];
  string  id = 3;
}
message DefineConfigurationRequest {
  carbon.thinning.ConfigDefinition  definition = 1;
  bool set_active = 2;
  ThinningConfVer ver = 3;
}
message DefineConfigurationResponse {
  string id = 1;  // If new config then this will be the newly generated id for that config
}
message SetActiveConfigRequest {
  string  name = 1 [ deprecated = true ];
  string  id = 2;
  ThinningConfVer ver = 3;
}
message SetActiveConfigResponse {
}
message DeleteConfigRequest {
  string  name = 1 [ deprecated = true ];
  string  id = 2;
  ThinningConfVer ver = 3;
  string new_active_id = 4; // If id was active must set this to a valid
}
message DeleteConfigResponse {
}

service ThinningService {
    rpc GetNextConfigurations(carbon.frontend.util.Timestamp) returns (GetNextConfigurationsResponse);
    rpc GetNextActiveConf(carbon.frontend.util.Timestamp) returns (GetNextActiveConfResponse);
    rpc DefineConfiguration(DefineConfigurationRequest) returns (DefineConfigurationResponse);
    rpc SetActiveConfig(SetActiveConfigRequest) returns (SetActiveConfigResponse);
    rpc DeleteConfig(DeleteConfigRequest) returns (DeleteConfigResponse);
}
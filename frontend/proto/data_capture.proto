syntax = "proto3";

package carbon.frontend.data_capture;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";

message DataCaptureRate {
    double rate = 1; // feet per image
}

enum UploadMethod {
    WIRELESS = 0;
    USB = 1;
}

enum ProcedureStep {
    NEW = 0;
    CAPTURING = 1;
    CAPTURE_PAUSED = 2;
    CAPTURE_COMPLETE = 3;
    UPLOADING_WIRELESS = 4;
    UPLOADING_WIRELESS_PAUSED = 5;
    UPLOADING_USB = 6;
    UPLOADING_USB_PAUSED = 7;
    UPLOADING_COMPLETE = 8;
}

message DataCaptureState {
    carbon.frontend.util.Timestamp ts = 1;
    uint32 images_taken = 2;
    uint32 target_images_taken = 3;
    uint64 estimated_capture_remaining_time_ms = 4;
    uint32 images_uploaded = 5;
    uint32 target_images_uploaded = 6;
    uint64 estimated_upload_remaining_time_ms = 7;
    DataCaptureRate rate = 8;
    bool wireless_upload_available = 9;
    bool usb_storage_connected = 10;
    string capture_status = 11;
    string upload_status = 12;
    string session_name = 13;
    ProcedureStep step = 14;
    string crop = 15;
    string error_message = 16;
    string crop_id = 17;
}

message DataCaptureSession {
    string name = 1;
}

message StartDataCaptureRequest {
    string name = 1;
    double rate = 2; // feet per image
    string crop = 3;
    string crop_id = 4;
    bool snap_capture = 5;
}

message SnapImagesRequest {
    string crop = 1;
    string crop_id = 2;
    optional string cam_id = 3;
    optional int64 timestamp_ms = 4;
    string session_name = 5;
}

message Session {
    string name = 1;
    uint32 images_remaining = 2;
    bool is_uploading = 3;
    bool has_completed = 4;
    bool is_capturing = 5;
}

message AvailableSessionResponse {
    repeated Session sessions = 1;
}

message SessionName {
    string name = 1;
}

message RegularCaptureStatus {
    uint32 uploaded = 1;
    uint32 budget = 2;
    int64 last_upload_timestamp = 3;
}

service DataCaptureService {
    rpc StartDataCapture(StartDataCaptureRequest) returns (carbon.frontend.util.Empty);
    rpc PauseDataCapture(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc StopDataCapture(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc ResumeDataCapture(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc CompleteDataCapture(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc StartDataCaptureWirelessUpload(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc StartDataCaptureUSBUpload(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc StopDataCaptureUpload(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc PauseDataCaptureUpload(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc ResumeDataCaptureUpload(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc StartBackgroundDataCaptureWirelessUpload(SessionName) returns (carbon.frontend.util.Empty);
    rpc StartBackgroundDataCaptureUSBUpload(SessionName) returns (carbon.frontend.util.Empty);
    rpc StopBackgroundDataCaptureUpload(SessionName) returns (carbon.frontend.util.Empty);
    rpc PauseBackgroundDataCaptureUpload(SessionName) returns (carbon.frontend.util.Empty);
    rpc ResumeBackgroundDataCaptureUpload(SessionName) returns (carbon.frontend.util.Empty);
    rpc GetNextDataCaptureState(carbon.frontend.util.Timestamp) returns (DataCaptureState);
    rpc SnapImages(SnapImagesRequest) returns (carbon.frontend.util.Empty);
    rpc GetSessions(carbon.frontend.util.Empty) returns (AvailableSessionResponse);
    rpc GetRegularCaptureStatus(carbon.frontend.util.Empty) returns (RegularCaptureStatus);
}
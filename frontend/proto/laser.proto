syntax = "proto3";

package carbon.frontend.laser;
option go_package = "proto/frontend";

import "frontend/proto/camera.proto";
import "frontend/proto/util.proto";

message LaserDescriptor {
    uint32 row_number = 1;
    uint32 laser_id = 2;
    string camera_id = 3;
    string serial = 4;
}

message LaserState {
    LaserDescriptor laser_descriptor = 1;
    bool firing = 2;
    bool enabled = 3;
    bool error = 4;
    int64 total_fire_count = 5;
    int64 total_fire_time_ms = 6;
    float delta_temp = 7;
    float current = 9;
    uint32 target_trajectory_id = 10;
    uint64 lifetime_sec = 11;
    float power_level = 12;
    int64 installed_at = 13;
}

message LaserStateList {
    carbon.frontend.util.Timestamp ts = 1;
    repeated LaserState lasers = 2;
}

message RowRequest {
    uint32 row_number = 1;
}

message SetLaserPowerRequest {
    LaserDescriptor laser_descriptor = 1;
    float power_level = 2;
}

message FixLaserMetricsRequest {
    LaserDescriptor laser_descriptor = 1;
    int64 total_fire_count = 2;
    int64 total_fire_time_ms = 3;
    uint64 lifetime_sec = 4;
}

service LaserService {
    rpc FireLaser(stream carbon.frontend.camera.CameraRequest) returns (carbon.frontend.util.Empty);
    rpc GetNextLaserState(carbon.frontend.util.Timestamp) returns (LaserStateList);
    rpc ToggleLaserEnabled(LaserDescriptor) returns (carbon.frontend.util.Empty);
    rpc EnableRow(RowRequest) returns (carbon.frontend.util.Empty);
    rpc DisableRow(RowRequest) returns (carbon.frontend.util.Empty);
    rpc ResetLaserMetrics(LaserDescriptor) returns (carbon.frontend.util.Empty);
    rpc FixLaserMetrics(FixLaserMetricsRequest) returns (carbon.frontend.util.Empty);
    rpc SetLaserPower(SetLaserPowerRequest) returns (carbon.frontend.util.Empty);
}
syntax = "proto3";

package carbon.frontend.camera;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";

enum CameraType {
    ANY = 0;
    PREDICT = 1;
    TARGET = 2;
    KILLCAM = 3;
}

message CameraRequest {
    string cam_id = 1;
}

message Camera {
    uint32 row_number = 1;
    string camera_id = 2;
    CameraType type = 3;
    bool auto_focusable = 4;
    string stream_host = 5;
    uint32 stream_port = 6;
    uint32 width = 7;
    uint32 height = 8;
    bool transpose = 9;
    bool connected = 10;
    RTCInfo rtc_info = 11;
}

message RTCInfo {
    string host_id = 1;
    string stream_id = 2;
}

message CameraList {
    repeated Camera cameras = 1;
    carbon.frontend.util.Timestamp ts = 2;
}

message CameraListRequest {
    CameraType type = 1;
    bool include_disconnected = 2;
}

message NextCameraListRequest {
    CameraType type = 1;
    carbon.frontend.util.Timestamp ts = 2;
    bool include_disconnected = 3;
}

service CameraService {
    rpc GetCameraList(CameraListRequest) returns (CameraList);
    rpc GetNextCameraList(NextCameraListRequest) returns (CameraList);
}

syntax = "proto3";

package carbon.frontend.color_calibration;
option go_package = "proto/frontend";

import "frontend/proto/camera.proto";
import "frontend/proto/util.proto";

message ColorCalibrationValues {
    float red = 1;
    float green = 2;
    float blue = 3;
    string cam_id = 4;
}

service CalibrationService {
    rpc StartColorCalibration(carbon.frontend.camera.CameraRequest) returns (ColorCalibrationValues);
    rpc SaveColorCalibration(ColorCalibrationValues) returns (frontend.util.Empty);
}
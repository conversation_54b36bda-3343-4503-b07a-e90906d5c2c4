syntax = "proto3";

package carbon.frontend.features;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";

message Location {
  carbon.frontend.util.Timestamp ts = 1;
  float latitude = 2;
  float longitude = 3;
  float altitude = 4;
  bool is_weeding = 5;
}

message LocationHistory {
  carbon.frontend.util.Timestamp ts = 1;
  repeated Location history = 2;
}

service ReportingService {
  rpc GetNextLocationHistory(carbon.frontend.util.Timestamp) returns (LocationHistory);
}
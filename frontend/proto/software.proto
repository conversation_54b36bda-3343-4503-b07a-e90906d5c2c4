syntax = "proto3";

package carbon.frontend.software;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";

message SoftwareVersion {
    string tag = 1;
    bool available = 2;
    bool ready = 3; // TODO Implement this
}

message HostSoftwareVersionState {
    string host_name = 1;
    uint32 host_id = 2;
    bool active = 3;
    SoftwareVersion current = 4;
    SoftwareVersion target = 5;
    SoftwareVersion previous = 6;
    bool updating = 7;
}

message SoftwareVersionState {
    carbon.frontend.util.Timestamp ts = 1;
    SoftwareVersion current = 2;
    SoftwareVersion target = 3;
    SoftwareVersion previous = 4;
    bool updating = 5;
    bool show_software_update_to_user = 6;
    repeated HostSoftwareVersionState host_states = 7;
    bool version_mismatch = 8;
}

message SoftwareVersionStateRequest {
    carbon.frontend.util.Timestamp ts = 1;
    bool get_host_states = 2;
}

message UpdateHostRequest {
    uint32 host_id = 1;
}

service SoftwareService {
    rpc GetNextSoftwareVersionState(SoftwareVersionStateRequest) returns (SoftwareVersionState);
    rpc UpdateHost(UpdateHostRequest) returns (carbon.frontend.util.Empty);
    rpc Update(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc Revert(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc FixVersionMismatch(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
}
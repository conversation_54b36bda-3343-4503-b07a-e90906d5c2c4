syntax = "proto3";

package carbon.frontend.chip;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";

message ChipData {
  string id = 1;
  string url = 2;
  string geohash = 3;
  string checksum = 4;
  uint32 content_length = 5;
  carbon.frontend.util.Timestamp downloaded_ts = 6;
  carbon.frontend.util.Timestamp last_used_ts = 7;
}

message GetChipMetadataResponse {
  repeated ChipData chips = 1;
}

message ChipIdsResponse {
  repeated string chip_ids = 1;
}

service ChipService {
  rpc GetChipMetadata(carbon.frontend.util.Empty)
      returns (GetChipMetadataResponse);
  rpc GetDownloadedChipIds(carbon.frontend.util.Empty)
      returns (ChipIdsResponse);
  rpc GetSyncedChipIds(carbon.frontend.util.Empty)
      returns (ChipIdsResponse);
}

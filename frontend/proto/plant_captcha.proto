syntax = "proto3";

package carbon.frontend.plant_captcha;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";
import "weed_tracking/proto/weed_tracking.proto";
import "proto/almanac/almanac.proto";

message PlantCaptcha {
    string name = 1;
    string model_id = 2;
    string crop_id = 3;
    string crop_name = 4;
    int64 start_time_ms = 5;
    repeated int32 rows_used = 6;
}

message StartPlantCaptchaRequest {
    PlantCaptcha plant_captcha = 1;
}

message StartPlantCaptchaResponse {
}

message GetNextPlantCaptchaStatusRequest {
    carbon.frontend.util.Timestamp ts = 1;
}

message GetNextPlantCaptchaStatusResponse {
    carbon.frontend.util.Timestamp ts = 1;
    weed_tracking.PlantCaptchaStatus status = 2;
    int32 total_images = 3;
    int32 images_taken = 4;
    int32 metadata_taken = 5;
}

message GetNextPlantCaptchasListRequest {
    carbon.frontend.util.Timestamp ts = 1;
}

message PlantCaptchaListItem {
    PlantCaptcha plant_captcha = 1;
    int32 images_taken = 2;
    int32 images_processed = 3;
}

message GetNextPlantCaptchasListResponse {
    carbon.frontend.util.Timestamp ts = 1;
    repeated PlantCaptchaListItem plant_captchas = 2;
}

message DeletePlantCaptchaRequest {
    string name = 1;
}

message GetPlantCaptchaRequest {
    string name = 1;
}

message PlantCaptchaItem {
    string image_url = 1;
    weed_tracking.PlantCaptchaItemMetadata metadata = 2;
    repeated string additional_image_urls = 3;
    repeated weed_tracking.PlantCaptchaItemMetadata additional_metadatas = 4;
}

message GetPlantCaptchaResponse {
    PlantCaptcha plant_captcha = 1;
    repeated PlantCaptchaItem items = 2;
}

message StartPlantCaptchaUploadRequest {
    string name = 1;
}

enum PlantCaptchaUploadState {
    NONE = 0;
    IN_PROGRESS = 1;
    DONE = 2;
}

message GetNextPlantCaptchaUploadStateRequest {
    carbon.frontend.util.Timestamp ts = 1;
    string name = 2;
}

message GetNextPlantCaptchaUploadStateResponse {
    carbon.frontend.util.Timestamp ts = 1;
    PlantCaptchaUploadState upload_state = 2;
    int32 percent = 3;
}

message PlantCaptchaItemResult {
    string id = 1;
    weed_tracking.PlantCaptchaUserPrediction user_prediction = 2;
}

message SubmitPlantCaptchaResultsRequest {
    string name = 1;
    repeated PlantCaptchaItemResult results = 2;
}

message GetPlantCaptchaItemResultsRequest {
    string name = 1;
    repeated string id = 2;
}

message GetPlantCaptchaItemResultsResponse {
    repeated PlantCaptchaItemResult results = 1;
}

message CalculatePlantCaptchaRequest {
    string name = 1;
}

enum PlantLabelAlgorithmFailureReason {
    NO_FAILURE = 0;
    METRICS_NOT_MET = 1;
    NOT_ENOUGH_ITEMS = 2;
}

message CalculatePlantCaptchaResponse {
    carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
    bool succeeded = 2;
    PlantLabelAlgorithmFailureReason failure_reason = 3;
}

message PlantCaptchaResult {
    weed_tracking.PlantCaptchaUserPrediction label = 1;
    weed_tracking.PlantCaptchaItemMetadata metadata = 2;
}

message PlantCaptchaResults {
    carbon.aimbot.almanac.ModelinatorConfig current_parameters = 1;
    repeated PlantCaptchaResult captcha_results = 2;
    string algorithm = 3;
    float goal_crops_targeted = 4;
    float goal_weeds_targeted = 5;
    float goal_unknown_targeted = 6;
    carbon.aimbot.almanac.AlmanacConfig almanac = 7;
    float max_recommended_mindoo = 8;
    int32 min_items_for_recommendation = 9;
    bool use_weed_categories_for_weed_labels = 10;
    float min_recommended_mindoo = 11;
    float min_recommended_weed_threshold = 12;
    float max_recommended_weed_threshold = 13;
    float min_recommended_crop_threshold = 14;
    float max_recommended_crop_threshold = 15;
    float min_doo_for_recommendation = 16;
    bool use_other_as_tiebreaker = 17;
    bool limit_by_crops_missed = 18;
    int32 number_of_crop_configurations = 19;
    string tiebreaker = 20;
    bool pad_crop_configurations = 21;
    string mindoo_tiebreaker = 22;
    bool use_beneficials_as_crops = 23;
    bool use_volunteers_as_weeds = 24;
    string tiebreaker_strategy_threshold_weed = 25;
    string tiebreaker_strategy_threshold_crop = 26;
    string tiebreaker_strategy_mindoo_weed = 27;
    string tiebreaker_strategy_mindoo_crop = 28;
}

message VeselkaPlantCaptchaResponse {
    carbon.aimbot.almanac.ModelinatorConfig new_model_parameters = 1;
    bool succeeded = 2;
}

message GetOriginalModelinatorConfigRequest {
    string name = 1;
}

message GetOriginalModelinatorConfigResponse {
    carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
}

message GetCaptchaRowStatusResponse {
    map<int32, weed_tracking.PlantCaptchaStatusResponse> row_status = 1;
}

message CancelPlantCaptchaOnRowRequest {
    int32 row_id  = 1;
}

service PlantCaptchaService {
    rpc StartPlantCaptcha(StartPlantCaptchaRequest) returns (StartPlantCaptchaResponse);
    rpc GetNextPlantCaptchaStatus(GetNextPlantCaptchaStatusRequest) returns (GetNextPlantCaptchaStatusResponse);
    rpc GetNextPlantCaptchasList(GetNextPlantCaptchasListRequest) returns (GetNextPlantCaptchasListResponse);
    rpc DeletePlantCaptcha(DeletePlantCaptchaRequest) returns (carbon.frontend.util.Empty);
    rpc GetPlantCaptcha(GetPlantCaptchaRequest) returns (GetPlantCaptchaResponse);
    rpc CancelPlantCaptcha(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc StartPlantCaptchaUpload(StartPlantCaptchaUploadRequest) returns (carbon.frontend.util.Empty);
    rpc GetNextPlantCaptchaUploadState(GetNextPlantCaptchaUploadStateRequest) returns (GetNextPlantCaptchaUploadStateResponse);
    rpc SubmitPlantCaptchaResults(SubmitPlantCaptchaResultsRequest) returns (carbon.frontend.util.Empty);
    rpc GetPlantCaptchaItemResults(GetPlantCaptchaItemResultsRequest) returns (GetPlantCaptchaItemResultsResponse);
    rpc CalculatePlantCaptcha(CalculatePlantCaptchaRequest) returns (CalculatePlantCaptchaResponse);
    rpc GetOriginalModelinatorConfig(GetOriginalModelinatorConfigRequest) returns (GetOriginalModelinatorConfigResponse);
    rpc GetCaptchaRowStatus(carbon.frontend.util.Empty) returns (GetCaptchaRowStatusResponse);
    rpc CancelPlantCaptchaOnRow(CancelPlantCaptchaOnRowRequest) returns (carbon.frontend.util.Empty);
}
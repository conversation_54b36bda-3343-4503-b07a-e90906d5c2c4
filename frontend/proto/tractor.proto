syntax = "proto3";

package carbon.frontend.tractor;
option go_package = "proto/frontend";
import "frontend/proto/util.proto";

message TractorIfState {
  bool expected = 1;
  bool connected = 2;
}

message TractorSafetyState {
  bool is_safe = 1;
  bool enforced = 2;
}

message GetNextTractorIfStateResponse {
  carbon.frontend.util.Timestamp ts = 1;
  TractorIfState state = 2;
}
message GetNextTractorSafetyStateResponse {
  carbon.frontend.util.Timestamp ts = 1;
  TractorSafetyState state = 2;
}

message SetEnforcementPolicyRequest {
  bool enforced = 1;
}
message SetEnforcementPolicyResponse {
}


service TractorService {
    rpc GetNextTractorIfState(carbon.frontend.util.Timestamp) returns (GetNextTractorIfStateResponse);
    rpc GetNextTractorSafetyState(carbon.frontend.util.Timestamp) returns (GetNextTractorSafetyStateResponse);
    rpc SetEnforcementPolicy(SetEnforcementPolicyRequest) returns (SetEnforcementPolicyResponse);
}
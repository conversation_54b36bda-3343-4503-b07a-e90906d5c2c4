syntax = "proto3";

package carbon.frontend.translation;
option go_package = "proto/frontend";

message IntegerValue {
    int64 value = 1;
}

message DoubleValue {
    double value = 1;
}

message StringValue {
    string value = 1;
}

message TemperatureValue {
    oneof value {
        double celcius = 1;
        double fahrenheit = 2;
    }
}

message PercentValue {
    uint32 percent = 1;
}

message VoltageValue {
    double volts = 1;
}

message FrequencyValue {
    double hertz = 1;
}

message AreaValue {
    oneof value {
        double acres = 1;
        double hectares = 2;
        double square_feet = 3;
        double square_meters = 4;
    }
}

message DurationValue {
    oneof value {
        uint64 milliseconds = 1;
        uint64 seconds = 2;
        uint64 minutes = 3;
        uint64 hours = 4;
    }
}

message DistanceValue {
    oneof value {
        double millimeters = 1;
        double meters = 2;
        double kilometers = 3;
        double inches = 4;
        double feet = 5;
        double miles = 6;
        double centimeters = 7;
    }
}

message SpeedValue {
    oneof value {
        double kilometers_per_hour = 1;
        double miles_per_hour = 2;
    }
}

message TranslationParameter {
    string name = 1;
    oneof value {
        IntegerValue int_value = 2;
        DoubleValue double_value = 3;
        StringValue string_value = 4;
        TemperatureValue temperature_value = 5;
        PercentValue percent_value = 6;
        VoltageValue voltage_value = 7;
        FrequencyValue frequency_value = 8;
        AreaValue area_value = 9;
        DurationValue duration_value = 10;
        DistanceValue distance_value = 11;
        SpeedValue speed_value = 12;
    }
}
syntax = "proto3";

package carbon.frontend.dashboard;
option go_package = "proto/frontend";

import "frontend/proto/translation.proto";
import "frontend/proto/util.proto";

message ExtraStatus {
    string title = 1;           // top text
    string icon_name = 2;       // center icon 
    string icon_color = 3;      // center icon color
    string status_text = 4;     // middle text if no icon
    string status_color = 5;    // middle text color if no icon
    string group_id = 6;        // group to cycle through in a single slot
    string section_id = 7;      // section to display this status in
    double progress = 8;        // show background progress if set
    uint32 width = 9;           // column width, 1 by default 
    string bottom_text = 10;    // bottom text if no icon
}

message WeedTargeting {
    bool enabled = 1;
}
message ThinningTargeting {
    bool enabled = 1;
    uint64 algorithm = 2 [ deprecated = true ];
}
message TargetingState {
    WeedTargeting weed_state = 1;
    ThinningTargeting thinning_state = 2;
    repeated bool enabled = 3 [ deprecated = true ];
    map<int32, bool> enabled_rows = 4;
}

enum SafetyOverrideState {
  SafetyOverrideNone = 0;
  SafetyOverrideVelocityStop = 1;
}

enum ImplementState {
    RAISED = 0;
    LOWERED = 1;
}

message ExtraConclusion {
    string title = 1;
    bool flip_thresholds = 2;
    uint32 good_threshold_percent = 3;
    uint32 medium_threshold_percent = 4;
    translation.PercentValue percent = 5;
}

message RowStateMessage {
    bool enabled = 1;
    bool target_state_mismatch = 2;
    bool ready = 3;
    SafetyOverrideState safety_override_state = 4;
}

message DashboardStateMessage {
    carbon.frontend.util.Timestamp ts = 1;
    bool lasers_enabled = 2;  
    repeated bool row_enabled = 3 [ deprecated = true ]; // Use row_states.enabled
    repeated ExtraStatus extras = 4;
    CropModel selected_model = 5;
    TargetingState targeting_state = 6;
    repeated bool target_state_mismatch = 7 [ deprecated = true ]; // use row_states.target_state_mismatch
    repeated bool row_ready = 8 [ deprecated = true ]; // use row_states.ready
    repeated bool row_exists = 9 [ deprecated = true ]; // row exists if key exists in row_states
    double row_width_in = 10;
    repeated SafetyOverrideState safety_override_state = 11 [ deprecated = true ]; //use row_states.safety_override_state
    ImplementState implement_state = 13;
    bool efficiency_enabled = 14;
    translation.PercentValue efficiency_percent = 15;
    bool error_rate_enabled = 16;
    translation.PercentValue error_rate = 17;
    repeated ExtraConclusion extra_conclusions = 18;
    translation.AreaValue area_weeded_today = 19;
    translation.AreaValue area_weeded_total = 20;
    translation.IntegerValue weeds_killed_today = 21;
    translation.IntegerValue weeds_killed_total = 22;
    translation.DurationValue time_weeded_today = 23;
    translation.DurationValue time_weeded_total = 24;
    bool weeding_enabled = 25;
    bool debug_mode = 26;
    translation.IntegerValue crops_killed_today = 27;
    translation.IntegerValue crops_killed_total = 28;
    bool cruise_enabled = 29;
    map<int32, RowStateMessage> row_states = 30;
    bool cruise_allow_enable = 31;
    string cruise_disallowed_reason = 32;

    reserved 12;
}

message CropModel {
    string crop = 1 [ deprecated = true ]; // Deprecated, use crop_id
    bool has_model = 2;
    bool preferred = 3;
    string crop_id = 4;
}

message CropModelOptions {
    repeated CropModel models = 1;
}

message RowId {
    uint32 row_number = 1;
}

message WeedingVelocity {
    carbon.frontend.util.Timestamp ts = 1;
    double current_velocity_mph = 2;
    double target_velocity_mph = 3; // Will be deprecated once we move to ve profiles
    double tolerance_mph = 4; // Will be deprecated once we move to ve profiles
    double primary_target_velocity_top_mph = 5; //will be used once we move to ve profiles
    double primary_target_velocity_bottom_mph = 6; //will be used once we move to ve profiles
    double secondary_target_velocity_top_mph = 7; //will be used once we move to ve profiles
    double secondary_target_velocity_bottom_mph = 8; //will be used once we move to ve profiles
    double cruise_control_velocity_mph = 9; //will be used once we move to ve profiles
}

message RowSpacing { double width = 1; }

message CruiseEnable{
    bool enabled = 1;
}

service DashboardService {
    rpc ToggleRow(RowId) returns (carbon.frontend.util.Empty);
    rpc ToggleLasers(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc GetNextDashboardState(carbon.frontend.util.Timestamp) returns (DashboardStateMessage);
    rpc GetCropModelOptions(carbon.frontend.util.Empty) returns (CropModelOptions) { option deprecated = true; };
    rpc SetCropModel(CropModel) returns (carbon.frontend.util.Empty) { option deprecated = true; };
    rpc GetNextWeedingVelocity(carbon.frontend.util.Timestamp) returns (WeedingVelocity);
    rpc SetTargetingState(TargetingState) returns (carbon.frontend.util.Empty);
    rpc SetRowSpacing(RowSpacing) returns (carbon.frontend.util.Empty);
    rpc SetCruiseEnabled(CruiseEnable) returns (carbon.frontend.util.Empty);
}
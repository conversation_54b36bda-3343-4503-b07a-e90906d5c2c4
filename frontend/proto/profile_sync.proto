syntax = "proto3";

package carbon.frontend.profile_sync;
option go_package = "proto/frontend";

enum ProfileType {
    ALMANAC = 0;
    DISCRIMINATOR = 1;
    MODELINATOR = 3;
    BANDING = 4;
    THINNING = 5;
    TARGET_VELOCITY_ESTIMATOR = 6;
    CATEGORY_COLLECTION = 7;
    CATEGORY = 8;
}

message ProfileSyncData {
    ProfileType profile_type = 1;
    int64 last_updated_ts_ms = 2;
    bool deleted = 3;
    bool protected = 4;
}

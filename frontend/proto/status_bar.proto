syntax = "proto3";

package carbon.frontend.status_bar;
option go_package = "proto/frontend";

import "frontend/proto/translation.proto";
import "frontend/proto/util.proto";

enum Status {
  STATUS_ERROR = 0;
  STATUS_ESTOPPED = 1;
  STATUS_PRE_ARMED = 2;
  STATUS_POWERED_DOWN = 3;
  STATUS_POWERING_UP = 4;
  STATUS_UPDATE_INSTALLING = 5;
  STATUS_MODEL_LOADING = 6;
  STATUS_MODEL_INSTALLING = 7;
  STATUS_WEEDING = 8;
  STATUS_STANDBY = 9;
  STATUS_UNKNOWN = 10;
  STATUS_DISCONNECTED = 11;
  STATUS_LIFTED = 12;
  STATUS_LOADING = 13;
  STATUS_ALARM_AUTOFIX_IN_PROGRESS = 14;
  STATUS_FAILED_TO_POWER_UP = 15;
  STATUS_SERVER_CABINET_COOLDOWN = 16;
  STATUS_CHILLER_COOLDOWN = 17;
  STATUS_TRACTOR_NOT_SAFE = 18;
}

enum StatusLevel {
  INVALID = 0;
  READY = 1;
  LOADING = 2;
  ESTOPPED = 3 [ deprecated = true ];
}

message GlobalStatus {
  string hint = 1;
  string icon_name = 2;
  string icon_color = 3;
}

message ServiceStatus {
  string name = 1;
  StatusLevel status_level = 2;
}

message ServerStatus {
  StatusLevel status_level = 1;
  repeated ServiceStatus service_status = 2;
}

message TranslatedStatusMessageDetails {
  oneof details {
    string details_string_key = 1;
    translation.DurationValue timer = 2;
  }
}

message TranslatedStatusMessage {
  string prefix = 1;
  TranslatedStatusMessageDetails details = 2;
}

message StatusBarMessage {
  carbon.frontend.util.Timestamp ts = 1;
  //  bool estopped = 2; Deprecated
  bool lasers_enabled = 3;
  bool weeding_enabled = 4;
  Status status_level = 5;
  string status_message = 6;
  string serial = 7;
  map<int32, ServerStatus> row_status = 8;
  ServerStatus command_status = 9;
  repeated GlobalStatus global_statuses = 10;
  TranslatedStatusMessage translated_status_message = 11;
}

message ReportIssueRequest {
  string description = 1;
  string phone_number = 2;
}

message SupportPhoneMessage { string support_phone = 1; }

service StatusBarService {
  rpc GetNextStatus(carbon.frontend.util.Timestamp) returns (StatusBarMessage);
  rpc ReportIssue(ReportIssueRequest) returns (carbon.frontend.util.Empty);
  rpc GetSupportPhone(carbon.frontend.util.Empty) returns (SupportPhoneMessage);
}
syntax = "proto3";

package carbon.frontend.power;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";

enum Device {
  SENSOR_LIFTED = 0;
  SENSOR_SERVER_TEMPERATURE = 1;
  SENSOR_SERVER_HUMIDITY = 2;
  SENSOR_WATER = 3;
  SENSOR_12V_BATTERY = 4;
  SENSOR_POWER_QUALITY = 5;
  SENSOR_TRACTOR = 6;
  SENSOR_AC_FREQUENCY = 7;
  SENSOR_AB_VOLTAGE = 8;
  SENSOR_BC_VOLTAGE = 9;
  SENSOR_AC_VOLTAGE = 10;
  SENSOR_A_CURRENT = 11;
  SENSOR_B_CURRENT = 12;
  SENSOR_C_CURRENT = 13;
  RELAY_SUICIDE = 14;
  RELAY_REBOOT = 15;
  RELAY_MAIN = 16;
  RELAY_ROW_1 = 17;
  RELAY_ROW_2 = 18;
  RELAY_ROW_3 = 19;
  RELAY_LIGHTS_1 = 20;
  RELAY_LIGHTS_2 = 21;
  R<PERSON><PERSON><PERSON>_LIGHTS_3 = 22;
  RELAY_SCANNER_1 = 23;
  RELAY_SCANNER_2 = 24;
  RELAY_SCANNER_3 = 25;
  RELAY_AC = 26;
  RELAY_CHILLER = 27;
  RELAY_STROBE = 28;
  RELAY_ENCODER_FRONT_LEFT = 29;
  RELAY_ENCODER_FRONT_RIGHT = 30;
  RELAY_ENCODER_BACK_LEFT = 31;
  RELAY_ENCODER_BACK_RIGHT = 32;
  SENSOR_ENCODER_FRONT_LEFT = 33;
  SENSOR_ENCODER_FRONT_RIGHT = 34;
  SENSOR_ENCODER_BACK_LEFT = 35;
  SENSOR_ENCODER_BACK_RIGHT = 36;
  RELAY_GPS = 37;
  SENSOR_LATITUDE = 38;
  SENSOR_LONGITUDE = 39;
  SENSOR_KEY = 40;
  SENSOR_INTERLOCK = 41;
  RELAY_ENCODER_BOARD = 42;
}

enum DeviceValueColor {
  COLOR_GRAY = 0;
  COLOR_GREEN = 1;
  COLOR_ORANGE = 2;
  COLOR_RED = 3;
}

message DeviceStatus {
  Device device = 1;
  string label = 2;

  message RelayStatus { bool disabled = 3; }

  message SensorStatus {
    string status = 4;
    DeviceValueColor color = 5;
  }

  oneof type {
    RelayStatus relay_type = 6;
    SensorStatus sensor_type = 7;
  }
}

message PowerStatusRequest {
  carbon.frontend.util.Timestamp ts = 2;
}

message PowerStatusResponse {
  repeated DeviceStatus devices = 1;
  carbon.frontend.util.Timestamp ts = 2;
}

message ValueWithRange {
  double value = 1;
  bool is_ok = 2;
};

message EnvironmentalSensorData {
  ValueWithRange temperature_c = 1;
  ValueWithRange humidity_rh = 2;
  ValueWithRange pressure_hpa = 3;
}

message CoolantSensorData {
  ValueWithRange temperature_c = 1;
  ValueWithRange pressure_kpa = 2;
}

enum NetworkLinkSpeed {
  UNKNOWN = 0;
  SPEED_10M_HALF = 1;
  SPEED_10M_FULL = 2;
  SPEED_100M_HALF = 3;
  SPEED_100M_FULL = 4;
  SPEED_1G_FULL = 5;
  SPEED_2G5_FULL = 6;
  SPEED_5G_FULL = 7;
  SPEED_10G_FULL = 8;
};

message NetworkPortState {
  bool link_up = 1;

  NetworkLinkSpeed actual_link_speed = 2;
  NetworkLinkSpeed expected_link_speed = 3;
};

message ReaperPcSensorData {
  ValueWithRange temperature_cpu_core_c = 1;
  ValueWithRange temperature_system_c = 2;
  optional ValueWithRange temperature_gpu_1_c = 3;
  optional ValueWithRange temperature_gpu_2_c = 4;

  ValueWithRange psu_12v = 5;
  ValueWithRange psu_5v = 6;
  ValueWithRange psu_3v3 = 7;

  // CPU load average
  ValueWithRange load = 8;
  // system uptime, in seconds
  uint32 uptime = 9;

  // Memory (RAM) utilization
  ValueWithRange ram_usage_percent = 10;
  // Disk space utilization
  ValueWithRange disk_usage_percent = 11;

  // Link state for scanner PCBs
  NetworkPortState scanner_a_link = 12;
  NetworkPortState scanner_b_link = 13;
  // Link state for target cam
  NetworkPortState target_cam_a_link = 14;
  NetworkPortState target_cam_b_link = 15;
  // Link state for predict cam
  NetworkPortState predict_cam_link = 16;
  // IPMI port link state
  NetworkPortState ipmi_link = 17;
  // Global network uplink port link state (ext port)
  NetworkPortState ext_link = 18;
}

// Info about the BWT laser connected to Reaper scanner
message ReaperScannerLaserStatus {
  // Laser model number
  string model = 1;
  // Serial number reported by laser
  string sn = 2;
  // Rated laser power (W)
  uint32 rated_power = 3;

  // Internal temperature
  ValueWithRange temperature_c = 4;
  // Laser humidity
  ValueWithRange humidity = 5;
  // Current through laser diodes
  ValueWithRange laser_current_ma = 6;

  // Laser faults (if any)
  repeated string faults = 7;
}

// Information for each of the motors/controllers
message ReaperScannerMotorData {
  // Serial number of motor controller
  string controller_sn = 1;

  // Output/driver stage temperature
  ValueWithRange temperature_output_c = 2;

  // Motor supply voltage
  ValueWithRange motor_supply_v = 3;
  // Instantaneous motor current
  ValueWithRange motor_current_a = 4;

  // Current encoder position, ticks
  int64 encoder_position = 5;
};

// Sensor data readings for the Reaper scanners
message ReaperScannerSensorData {
  // Scanner assembly serial number
  string scanner_sn = 1;

  // Scanner current consumption
  ValueWithRange current_a = 2;
  // Whether fuse to scanner is tripped
  bool fuse_tripped = 3;

  // Temperature of collimator (°C)
  ValueWithRange temperature_collimator_c = 4;
  // laser fiber connection temperature (°C)
  ValueWithRange temperature_fiber_c = 5;
  // Approximate laser power reading from photodiode
  ValueWithRange laser_power_w = 6;

  // Whether laser is connected
  bool laser_connected = 7;
  // Additional laser status information
  optional ReaperScannerLaserStatus laser_status = 8;

  // If target cam is connected
  bool target_connected = 9;
  // Target camera's serial number
  optional string target_sn = 10;
  // Temperature of target camera
  optional ValueWithRange temperature_target_c = 11;

  // Individual motor controller info
  optional ReaperScannerMotorData motor_pan = 12;
  optional ReaperScannerMotorData motor_tilt = 13;

  // whether switchable power to the scanner itself is enabled
  bool scanner_power_enabled = 14;
  // whether power to the target cam (controlled via scanner) is enabled
  bool target_cam_power_enabled = 15;
}

enum AcPowerStatus {
  POWER_UNKNOWN = 0;
  POWER_GOOD = 1;
  POWER_BAD = 2;
  POWER_VERY_BAD = 3;
};

message ReaperGpsData {
  bool has_fix = 1;

  float latitude = 2;
  float longitude = 3;
};

message ReaperWheelEncoderData {
  int64 front_left = 1;
  int64 front_right = 2;
};

// All readings from stuff in the center enclosure
// Similar to GetSupervisoryStatusResponse
message ReaperCenterEnclosureData {
  bool water_protect_status = 1;
  bool main_contactor_status_fb = 2;
  AcPowerStatus power_status = 3;

  bool lifted_status = 4;
  bool temp_humidity_status = 5 [deprecated = true];
  bool tractor_power = 6;

  ValueWithRange ac_frequency = 7;
  ValueWithRange ac_voltage_a_b = 8;
  ValueWithRange ac_voltage_b_c = 9;
  ValueWithRange ac_voltage_a_c = 10;
  ValueWithRange ac_voltage_a = 11;
  ValueWithRange ac_voltage_b = 12;
  ValueWithRange ac_voltage_c = 13;
  int64 phase_power_w_3 = 14;
  int64 phase_power_va_3 = 15;
  ValueWithRange power_factor = 16;

  ValueWithRange server_cabinet_temp = 17;
  ValueWithRange server_cabinet_humidity = 18;
  ValueWithRange battery_voltage_12v = 19;

  bool wheel_encoder_disabled = 20;
  // per module, this should be removed
  bool strobe_disabled = 21 [ deprecated = true ];
  bool gps_disabled = 22;
  bool main_contactor_disabled = 23;
  bool air_conditioner_disabled = 24;
  bool chiller_disabled = 25;

  repeated string chiller_alarms = 26;
  // actual chiller circulating fluid temp
  ValueWithRange chiller_temp_c = 27;
  // litres per minute
  ValueWithRange chiller_flow_l_min = 28;
  // circulating fluid pressure at chiller
  ValueWithRange chiller_pressure_psi = 29;
  // microsiemens per cm
  ValueWithRange chiller_conductivity_us_cm = 30;
  // circulating fluid target temperature
  ValueWithRange chiller_set_temp_c = 31;
  ValueWithRange chiller_heat_transfer_kbtu = 32;
  ValueWithRange chiller_fluid_delta_temp_c = 33;

  ReaperGpsData gps = 34;
  ReaperWheelEncoderData wheel_encoder = 35;
}

enum ModuleStatus {
  OK = 0;
  Error = 1;
}

// Sensor readings from a single module
message ReaperModuleSensorData {
  // module ID from which this data was read
  int32 module_id = 1;
  // serial number of the ID from which this data was read
  string module_sn = 2;

  // Sensor on MCB (general enclosure area)
  EnvironmentalSensorData enviro_enclosure = 3;
  // Remote sensor in PC enclosure area
  EnvironmentalSensorData enviro_pc = 4;
  // Coolant inlet sensor
  CoolantSensorData coolant_inlet = 5;
  // Coolant outlet sensor
  CoolantSensorData coolant_outlet = 6;

  // Strobe board temperature
  ValueWithRange strobe_temperature_c = 7;
  // Strobe capacitor voltage
  ValueWithRange strobe_cap_voltage = 8;
  // Current (averaged) through the LEDs
  ValueWithRange strobe_current = 9;

  // Detailed information from sensors on the module PC
  optional ReaperPcSensorData pc = 10;
  // Info about each of the scanners and target cams
  optional ReaperScannerSensorData scanner_a = 11;
  optional ReaperScannerSensorData scanner_b = 12;

  bool pc_power_enabled = 13;
  bool lasers_power_enabled = 14;
  bool predict_cam_power_enabled = 15;

  // whether the relay for BTL power supply is on
  bool strobe_power_enabled = 16;
  // whether software is requesting strobe light triggering
  bool strobe_enabled = 17;

  ModuleStatus status = 18;
}

message CenterEnclosureStatusRequest {}

message ModuleHardwareStatusRequest {
  // ID of a single module for which hardware status will be returned
  int32 module_id = 1;
}

message GetNextReaperHardwareStatusRequest {
  carbon.frontend.util.Timestamp ts = 1;

  oneof request {
    CenterEnclosureStatusRequest center_enclosure_status = 2;
    ModuleHardwareStatusRequest module_status = 3;
  }
}

message GetNextReaperHardwareStatusResponse {
  carbon.frontend.util.Timestamp ts = 1;
    
  oneof response {
    ReaperCenterEnclosureData center_enclosure_status = 2;
    ReaperModuleSensorData module_status = 3;
  }
}

message GetNextReaperAllHardwareStatusRequest {
  carbon.frontend.util.Timestamp ts = 1;
}

message GetNextReaperAllHardwareStatusResponse {
  carbon.frontend.util.Timestamp ts = 1;
  // center enclosure stuff
  ReaperCenterEnclosureData center_enclosure_status = 2;
  // sensors from all modules
  repeated ReaperModuleSensorData module_status = 3;
}

message RelayRequest {
  Device device = 1;
}

message RelayResponse {
  bool success = 1;
}

// Set switched scanner power
message SetReaperScannerPowerRequest {
  uint32 module_id = 1;

  optional bool scanner_a_power = 2;
  optional bool scanner_b_power = 3;
}
message SetReaperScannerPowerResponse { bool success = 1; }

// Set switched target cam power (via scanner)
message SetReaperTargetPowerRequest {
  uint32 module_id = 1;

  optional bool target_a_power = 2;
  optional bool target_b_power = 3;
}
message SetReaperTargetPowerResponse { bool success = 1; }

// Set the switchde power to a module's predict cam
message SetReaperPredictCamPowerRequest {
  uint32 module_id = 1;
  bool enabled = 2;
}
message SetReaperPredictCamPowerResponse { bool success = 1; }

// Set whether strobe firing is enabled
message SetReaperStrobeEnableRequest {
  uint32 module_id = 1;
  bool enabled = 2;
}
message SetReaperStrobeEnableResponse { bool success = 1; }

// Control strobes for _all_ modules on the machine
message SetReaperAllStrobesEnableRequest { bool enabled = 1; }
message SetReaperAllStrobesEnableResponse { bool success = 1; }

// Set module PC 240V input power
message SetReaperModulePcPowerRequest {
  uint32 module_id = 1;
  bool enabled = 2;
}
message SetReaperModulePcPowerResponse { bool success = 1; }

// Set module lasers 240V input power; all lasers are controlled together
message SetReaperModuleLaserPowerRequest {
  uint32 module_id = 1;
  bool enabled = 2;
}
message SetReaperModuleLaserPowerResponse { bool success = 1; }

service PowerService {
  rpc GetNextPowerStatus(PowerStatusRequest) returns (PowerStatusResponse);
  rpc TurnOffDevice(RelayRequest) returns (RelayResponse);
  rpc TurnOnDevice(RelayRequest) returns (RelayResponse);

  rpc GetNextReaperAllHardwareStatus(GetNextReaperAllHardwareStatusRequest)
      returns (GetNextReaperAllHardwareStatusResponse);
  rpc GetNextReaperHardwareStatus(GetNextReaperHardwareStatusRequest)
      returns (GetNextReaperHardwareStatusResponse);
  rpc SetReaperScannerPower(SetReaperScannerPowerRequest)
      returns (SetReaperScannerPowerResponse) {}
  rpc SetReaperTargetPower(SetReaperTargetPowerRequest)
      returns (SetReaperTargetPowerResponse) {}
  rpc SetReaperPredictCamPower(SetReaperPredictCamPowerRequest)
      returns (SetReaperPredictCamPowerResponse) {}
  rpc SetReaperStrobeEnable(SetReaperStrobeEnableRequest)
      returns (SetReaperStrobeEnableResponse) {}
  rpc SetReaperAllStrobesEnable(SetReaperAllStrobesEnableRequest)
      returns (SetReaperAllStrobesEnableResponse) {}
  rpc SetReaperModulePcPower(SetReaperModulePcPowerRequest)
      returns (SetReaperModulePcPowerResponse) {}
  rpc SetReaperModuleLaserPower(SetReaperModuleLaserPowerRequest)
      returns (SetReaperModuleLaserPowerResponse) {}
}

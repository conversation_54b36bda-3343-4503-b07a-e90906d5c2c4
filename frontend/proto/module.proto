syntax = "proto3";

package carbon.frontend.module;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";

/* Module Identity */

message ModuleIdentity {
    uint32 id = 1;
    string serial = 2;
}

message GetNextModulesListRequest {
    carbon.frontend.util.Timestamp ts = 1;
} 

message GetNextModulesListResponse {
    carbon.frontend.util.Timestamp ts = 1;
    repeated ModuleIdentity assigned_modules = 2;
    repeated ModuleIdentity unassigned_modules = 3;
    repeated ModuleIdentity unset_serial_modules = 4;
}

message GetNextActiveModulesRequest {
    carbon.frontend.util.Timestamp ts = 1;
}

message GetNextActiveModulesResponse {
    carbon.frontend.util.Timestamp ts = 1;
    repeated ModuleIdentity active_modules = 2;
}

message IdentifyModuleRequest {
    ModuleIdentity module_identity = 1;
}

message AssignModuleRequest {
    ModuleIdentity module_identity = 1;
}

message ClearModuleAssignmentRequest {
    ModuleIdentity module_identity = 1;
}

message SetModuleSerialRequest {
    ModuleIdentity module_identity = 1;
    string new_serial = 2;
}

/* Robot Definition */

message ModuleDefinition {
    uint32 module_id = 1;
    float module_spacing_mm = 2; // spacing between this module and the one to its
                              // left. 0 for the first module in a row
    bool disabled = 3;        // always false for presets
}

message RowDefinition {
    uint32 row_id = 1;
    repeated ModuleDefinition modules = 2;
    float row_spacing_mm = 3; // spacing between this row and the one to its left. 0
                           // for the first row
}

message BarDefinition {
    uint32 bar_length_mm = 1;
    bool folding = 2;
}

message RobotDefinition {
    repeated RowDefinition rows = 1;
    BarDefinition bar_definition = 3;
}

message Preset {
    string uuid = 1;
    string display_name = 2;
    RobotDefinition definition = 3;
}

message GetPresetsListRequest { string language = 1; }

message GetPresetsListResponse { repeated Preset presets = 1; }

message GetCurrentRobotDefinitionResponse {
    optional RobotDefinition current_definition = 1;
}

message SetCurrentRobotDefinitionRequest {
    RobotDefinition current_definition = 1;
}

service ModuleAssignmentService {
    /* Module Identity */
    rpc GetNextModulesList(GetNextModulesListRequest) returns (GetNextModulesListResponse);
    rpc GetNextActiveModules(GetNextActiveModulesRequest) returns (GetNextActiveModulesResponse);
    rpc IdentifyModule(IdentifyModuleRequest) returns (carbon.frontend.util.Empty);
    rpc AssignModule(AssignModuleRequest) returns (carbon.frontend.util.Empty);
    rpc ClearModuleAssignment(ClearModuleAssignmentRequest) returns (carbon.frontend.util.Empty);
    rpc SetModuleSerial(SetModuleSerialRequest) returns (carbon.frontend.util.Empty);

    /* Robot Definition */
    rpc GetPresetsList(GetPresetsListRequest) returns (GetPresetsListResponse);
    rpc GetCurrentRobotDefinition(carbon.frontend.util.Empty) returns (GetCurrentRobotDefinitionResponse);
    rpc SetCurrentRobotDefinition(SetCurrentRobotDefinitionRequest) returns (carbon.frontend.util.Empty);
}
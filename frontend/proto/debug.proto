syntax = "proto3";

package carbon.frontend.debug;
option go_package = "proto/frontend";
option optimize_for = LITE_RUNTIME;

import "frontend/proto/alarm.proto";
import "frontend/proto/util.proto";
import "proto/logging/logging.proto";
import "weed_tracking/proto/weed_tracking.proto";

message RobotMessage {
    string serial = 1;
    repeated carbon.frontend.alarm.AlarmRow alarms = 2;
}

message SetLogLevelRequest {
    carbon.logging.LogLevel level = 1;
    string component = 2;
    int32 row_num = 3;
}

service DebugService {
    rpc GetRobot(carbon.frontend.util.Empty) returns (RobotMessage);
    rpc SetLogLevel(SetLogLevelRequest) returns (carbon.frontend.util.Empty);
    rpc StartSavingCropLineDetectionReplay(weed_tracking.StartSavingCropLineDetectionReplayRequest) returns (weed_tracking.Empty);
    rpc StartRecordingAimbotInputs(weed_tracking.RecordAimbotInputRequest) returns (weed_tracking.Empty);
    rpc AddMockSpatialMetricsBlock(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc DeleteProfileSyncData(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
}
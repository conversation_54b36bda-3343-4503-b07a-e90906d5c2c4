syntax = "proto3";

package carbon.frontend.focus;
option go_package = "proto/frontend";

import "frontend/proto/camera.proto";
import "frontend/proto/util.proto";

message TargetFocusState {
    uint32 liquid_lens_value = 1;
    double focus_progress_pct = 2;
    uint32 max_lens_value = 3;
    uint32 min_lens_value = 4;
    bool focus_in_progress = 5;
}

message PredictFocusState {

}

message FocusState {
    carbon.frontend.util.Timestamp ts = 1;
    oneof type_state {
        TargetFocusState target = 2;
        PredictFocusState predict = 3;
    }
    double global_focus_progress_pct = 4;
    bool grid_view_enabled = 5;
    bool focus_in_progress = 6;
}

message LensSetRequest {
    string cam_id = 1;
    uint32 lens_value = 2;
}

message FocusStateRequest {
    string cam_id = 1;
    carbon.frontend.util.Timestamp ts = 2;
}

service FocusService {
    rpc TogglePredictGridView(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc GetNextFocusState(FocusStateRequest) returns (FocusState);
    rpc StartAutoFocusSpecific(carbon.frontend.camera.CameraRequest) returns (carbon.frontend.util.Empty);
    rpc StartAutoFocusAll(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc StopAutoFocus(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc SetLensValue(LensSetRequest) returns (carbon.frontend.util.Empty);
}
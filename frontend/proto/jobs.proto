syntax = "proto3";

package carbon.frontend.jobs;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";
import "frontend/proto/weeding_diagnostics.proto";
import "metrics/proto/metrics_aggregator_service.proto";
import "frontend/proto/profile_sync.proto";

message JobDescription {
    string jobId = 1;
    string name = 2;
    int64 timestampMs = 3;
}

message ActiveProfile {
    carbon.frontend.profile_sync.ProfileType profile_type = 1;
    string id = 2;
    string name = 3;
}

message Job {
    JobDescription jobDescription = 1;
    string bandingProfile = 2;
    string thinningProfile = 3;
    int64 stopTimeMs = 4;
    int64 lastUpdateTimeMs = 5;
    float expectedAcreage = 6;
    bool completed = 7;
    string almanac = 8;
    string discriminator = 9;
    string crop_id = 10;
    string bandingProfileUUID = 11;
    string thinningProfileUUID = 12;
    string almanacProfileUUID = 13;
    string discriminatorProfileUUID = 14;
    map <int32, ActiveProfile> active_profiles = 15; // Hack since you cant use enum as key type we place enum in object as well
    int64 lastUsedTimeMs = 16;
}

message CreateJobRequest {
    string name = 1;
    bool active = 2;
    float expectedAcreage = 3;
}

message CreateJobResponse {
    string jobId = 1;
}

message UpdateJobRequest {
    JobDescription jobDescription = 1;
    float expectedAcreage = 2;
}

message GetNextJobsRequest {
    carbon.frontend.util.Timestamp timestamp = 1;
}

message JobWithMetrics {
    Job job = 1;
    metrics_aggregator.Metrics metrics = 2;
}
message GetNextJobsResponse {
    repeated JobWithMetrics jobs = 1;
    string activeJobId = 2;
    carbon.frontend.util.Timestamp timestamp = 3;
}

message GetNextActiveJobIdRequest {
    carbon.frontend.util.Timestamp timestamp = 1;
}

message GetNextActiveJobIdResponse {
    string activeJobId = 1;
    carbon.frontend.util.Timestamp timestamp = 2;
}

message GetJobRequest {
    string jobId = 1;
}

message GetJobResponse {
    Job job = 1;
}

message StartJobRequest {
    string jobId = 1;
}

message GetConfigDumpRequest {
    string jobId = 1;
}

message GetConfigDumpResponse {
    carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 1;
}

message GetActiveJobMetricsResponse {
    metrics_aggregator.Metrics jobMetrics = 1;
}

message DeleteJobRequest {
    string jobId = 1;
}

message MarkJobCompletedRequest {
    string jobId = 1;
}

message MarkJobIncompleteRequest {
    string jobId = 1;
}

message GetNextJobRequest {
    carbon.frontend.util.Timestamp ts = 1;
    string jobId = 2;
}

message GetNextJobResponse {
    carbon.frontend.util.Timestamp ts = 1;
    JobWithMetrics job = 2;
}

service JobsService {
    rpc GetNextJobs(GetNextJobsRequest) returns (GetNextJobsResponse);
    rpc CreateJob(CreateJobRequest) returns (CreateJobResponse);
    rpc UpdateJob(UpdateJobRequest) returns (carbon.frontend.util.Empty);
    rpc StartJob(StartJobRequest) returns (carbon.frontend.util.Empty);
    rpc StopActiveJob(carbon.frontend.util.Empty) returns (carbon.frontend.util.Empty);
    rpc GetNextActiveJobId(GetNextActiveJobIdRequest) returns (GetNextActiveJobIdResponse);
    rpc GetJob(GetJobRequest) returns (GetJobResponse);
    rpc GetConfigDump(GetConfigDumpRequest) returns (GetConfigDumpResponse);
    rpc GetActiveJobMetrics(carbon.frontend.util.Empty) returns (GetActiveJobMetricsResponse);
    rpc DeleteJob(DeleteJobRequest) returns (carbon.frontend.util.Empty);
    rpc MarkJobCompleted(MarkJobCompletedRequest) returns (carbon.frontend.util.Empty);
    rpc MarkJobIncomplete(MarkJobIncompleteRequest) returns (carbon.frontend.util.Empty);
    rpc GetNextJob(GetNextJobRequest) returns (GetNextJobResponse);
}
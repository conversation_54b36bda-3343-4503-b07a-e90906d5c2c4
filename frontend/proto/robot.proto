syntax = "proto3";

package carbon.frontend.robot;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";

message BoardState {
    string board_rev = 1;
    bool connected = 3;
    bool booted = 4;
    bool error = 5;
    uint64 timestamp = 6;
    string firmware_version = 7;
}

message GPSCoord {
    double latitude = 2;
    double longitude = 3;
    double altitude_mm = 4;
    double ecef_x = 5;
    double ecef_y = 6;
    double ecef_z = 7;
}

message GPSBoardState {
    BoardState board = 1;
    bool have_fix = 2;
    bool have_approx_fix = 3;
    int32 num_sats = 4;
    float hdop = 5;
    GPSCoord position = 6;
}

message WheelState {
    int32 last_tick_position = 1;
    float vel_mph = 2;
    bool filtered = 3;
    bool enabled = 4;
    float diameter_in = 5;
}

message WheelEncoderBoardState {
    BoardState board = 1;
    repeated WheelState wheels = 2;
}

message StrobeBoardState {
    BoardState board = 1;
    int32 exposure_us = 2;
    int32 period_us = 3;
    int32 targets_per_predict_ratio = 4;
}

message ISOBusState {
    // TODO Fill when this exists
}

message ChillerState {
    // TODO Fill when this exists
}

message AirConditionerState {
    // TODO Fill when this exists
}

message USBDriveState {
    bool connected = 1;
    float utilization_pct = 2;
}

message SupervisoryPLCState {
    float ac_frequency = 1;
    float ac_voltage_a = 2;
    float ac_voltage_a_b = 3;
    float ac_voltage_a_c = 4;
    float ac_voltage_b = 5;
    float ac_voltage_b_c = 6;
    float ac_voltage_c = 7;
    float phase_power_va_3 = 8;
    float phase_power_w_3 = 9;
    float power_factor = 10;
    bool power_bad = 11;
    bool power_good = 12;
    bool power_very_bad = 13;
    float battery_voltage_12v = 14;

    bool air_conditioner_disabled = 15;
    bool chiller_disabled = 16;
    bool gps_disabled = 17;
    bool strobe_disabled = 18;
    bool wheel_encoder_disabled = 19;
    repeated bool btl_disabled = 20;
    repeated bool server_disabled = 21;
    repeated bool scanners_disabled = 22;
    bool main_contactor_disabled = 23;
    bool main_contactor_status_fb = 24;

    float server_cabinet_humidity = 25;
    float server_cabinet_temp = 26;
    bool humidity_bypassed = 27;
    bool temp_bypassed = 28;
    bool humidity_status = 29;
    bool temp_status = 30;
    bool temp_humidity_status = 31;

    bool lifted_status = 32;
    bool tractor_power = 33;
    bool water_protect_status = 34;
}

message SafetyPLCState {
    bool estopped = 1;
    bool in_cab_estopped = 2;
    bool interlock = 3;
    bool laser_key = 4;
    bool left_estopped = 5;
    bool right_estopped = 6;
    bool lifted = 7;
    bool water_protect = 8;
    bool lift_sensor_bypassed = 9;
}

message NicState {
    bool ptp_online = 1;
    int64 ptp_offset = 2;
    bool link_online = 3;
    bool link_correct = 4;
    int32 link_speed = 5;
}

message StorageDriveState {
    uint64 capacity = 1;
    uint64 used = 2;
}

message GPUState {
    bool online = 1;
    uint32 index = 2;
    string model = 3;
    float temperature_C = 4;
    float power_used = 5;
    float power_capacity = 6;
    float memory_used = 7;
    float memory_capacity = 8;
    float fan = 9;
    float gpu_util = 10;
}

message HostState {
    bool online = 1;
    bool ptp_checker_online = 2;
    bool ethernet_checker_online = 3;
    bool storage_checker_online = 4;
    bool gpu_checker_online = 5;
    map<string, NicState> nics = 6;
    StorageDriveState main_partition = 7;
    StorageDriveState data_partition = 8;
    repeated GPUState gpus = 9;
}

message SoftwareUpdateVersionState {
    string tag = 1;
    string system = 2;
    bool available = 3;
    bool ready = 4;
    map<string, bool> images_required = 5; // True if done downloading
}

message OperatingSystemState {
    string partition = 1;
    string version = 2;
}

message SystemVersionState {
    OperatingSystemState current = 1;
    OperatingSystemState other = 2;
}

message SoftwareVersionsState {
    SoftwareUpdateVersionState current = 1;
    SoftwareUpdateVersionState previous = 2;
    SoftwareUpdateVersionState target = 3;
    SystemVersionState system = 4;
}

enum SoftwareProcessStage {
    SPS_UNKNOWN = 0;
    SPS_ERROR = 1;
    SPS_BOOTING = 2;
    SPS_READY = 3;
} 

message SoftwareProcessState {
    string name = 1;
    SoftwareProcessStage stage = 2;
}

message SoftwareState {
    map<string, SoftwareProcessState> processes = 1;
    SoftwareVersionsState versions = 2;
    bool restarting = 3;
    int64 last_restart_time_ms = 4;
    bool updating = 5;
    int64 last_update_time_ms = 6;
}

message DeepweedModelState {
    repeated string viable_crop_ids = 1;
}

message P2PModelState {
    // TODO Fill once there is anything here
}

message ModelState {
    string model_id = 1;
    string type = 2;
    oneof specific {
        DeepweedModelState deepweed = 3;
        P2PModelState p2p = 4;
    }
    bool ready = 5;
    bool loaded = 6;
}

message RowModelState {
    repeated ModelState models = 1;
}

message ModelManagerState {
    repeated ModelState local_models = 1;
    repeated RowModelState row_models = 2;
    string active_crop_id = 3;
    string active_deepweed_model = 4;
    string active_p2p_model = 5;
}

message ComputerState {
    SoftwareState software = 1;
    HostState host = 2;
}

message PowerTimeState {
    int64 commander_on_time_ms = 1;
    int64 power_on_time_ms = 2;
    int64 air_conditioner_enabled_time_ms = 3;
    int64 chiller_enabled_time_ms = 4;
    int64 gps_enabled_time_ms = 5;
    int64 strobe_enabled_time_ms = 6;
    int64 wheel_encoder_enabled_time_ms = 7;
    repeated int64 btl_enabled_time_ms = 8;
    repeated int64 server_enabled_time_ms = 9;
    repeated int64 scanners_enabled_time_ms = 10;
    int64 main_contactor_enabled_time_ms = 11;
}

enum BootStage {
    BS_UPDATE_INSTALLING = 0;
    BS_POWERED_DOWN = 1;
    BS_POWERING_UP = 2;
    BS_SOFTWARE_LOADING = 3;
    BS_BOOT_FAILURE = 4;
    BS_MODEL_LOADING = 5;
    BS_CRITICAL_FAILURE = 6;
    BS_LIFTED = 7;
    BS_ESTOPPED = 8;
    BS_WEEDING = 9;
    BS_STANDBY = 10;
}

message VelocityState {
    float current_velocity_mph = 1;
    float target_velocity_mph = 2;
    repeated float row_target_velocity_mph = 3;
}

message WeedingState {
    repeated bool weeding = 1;
    BootStage stage = 2;
    VelocityState velocity = 3;
}

message GlobalState {
    ComputerState computer = 1;
    GPSBoardState gps = 2;
    WheelEncoderBoardState wheel = 3;
    StrobeBoardState strobe = 4;
    SupervisoryPLCState supervisory = 5;
    SafetyPLCState safety = 6;
    ChillerState chiller = 8;
    AirConditionerState air_conditioner = 9;
    ISOBusState isobus = 10;
    PowerTimeState power_time = 11;
    ModelManagerState models = 12;
    WeedingState weeding = 13;
    USBDriveState usb = 14;

    reserved 7;
}

message CameraState {
    string vendor = 1;
    string model = 2;
    string ip = 3;
    string serial = 4;
    string id = 5;
    bool connected = 6;
    float exposure_us = 7;
    int32 gpu_id = 8;
}

message MaxonPidState {
    uint32 current_p = 1;
    uint32 current_i = 2;
    uint32 position_p = 3;
    uint32 position_i = 4;
    uint32 position_d = 5;
    uint32 position_ffv = 6;
    uint32 position_ffa = 7;
}

message ServoState {
    string type = 1;
    int32 min = 2;
    int32 max = 3;
    int32 last_read_position = 4;
    int32 last_requested_position = 5;
    uint32 last_requested_velocity = 6;
    uint32 last_requested_follow_velocity = 7;
    oneof pids {
        MaxonPidState maxon = 8;
    }
}

message ScannerMetricState {
    float n_entities_targeted = 1;
    float avg_accuracy = 2; // At 2 P2P
    float avg_initial_accuracy = 3; // At first P2P
    float avg_overhead_time = 4;
    float error_rate = 5;
    float not_found_rate = 6;
    float out_of_range_rate = 7;
    float target_changed_rate = 8;
}

message ScannerLaserState {
    string serial = 1;
    int64 n_shots = 2;
    int64 fire_time_ms = 3;
    int64 controlled_time_ms = 4;
    bool lpsu_power = 5;
    float lpsu_current = 6;
    float therm_sensor = 7;
    float therm_ref = 8;
    float delta_temp = 9;
    bool power_bypassed = 10;
    bool armed = 11;
    bool watchdog = 12;
    bool firing_requested = 13;
    bool firing = 14;
    float intensity = 15;
    float wattage = 16;
    float nominal_wattage = 17;
}

message ScannerCrosshairState {
    int64 last_update_timestamp_ms = 1;
    float x = 2; // As percentages
    float y = 3;
}

message ScannerLensState {
    float last_requested_value = 1;
    float actual_value = 2;
}

message ScannerState {
    string name = 1;
    BoardState board = 2;
    bool enabled = 3;
    ServoState pan = 4;
    ServoState tilt = 5;
    ScannerCrosshairState crosshair = 6;
    ScannerLensState lens = 7;
    ScannerLaserState laser = 8;
    ScannerMetricState metrics = 9;
}

message CVNodeState {
    string name = 1;
    float mean_fps = 2;
    float mean_output_latency = 3;
    float p99_output_latency = 4;
    float mean_real_latency = 5;
    float p99_real_latency = 6;
    string state = 7;
    float pull_ms = 8;
    float processing_ms = 9;
    float pushing_ms = 10;
}

message CVState {
    repeated CVNodeState nodes = 1;
}

message TrackerState {
    string name = 1;
    uint32 n_trajectories = 2;
    uint32 capacity = 3;
}

message IngestClientState {
    string name = 1;
    bool over_capacity = 2;
    bool over_constraints = 3;
}

message AimbotState {
    repeated TrackerState trackers = 1;
    repeated IngestClientState ingests = 2;
}

message RowState {
    ComputerState computer = 1;
    repeated ScannerState scanners = 2;
    repeated CameraState predicts = 3;
    repeated CameraState targets = 4;
    repeated CameraState extras = 5;
    CVState cv = 6;
    AimbotState aimbot = 7;
}

message RobotState {
    GlobalState global = 1;
    repeated RowState rows = 2;
}

message TimestampedRobotState { 
    carbon.frontend.util.Timestamp ts = 1;
    RobotState state = 2;
}

message RobotStateRequest {
    carbon.frontend.util.Timestamp ts = 1;
}

service RobotDiagnosticService {
    rpc GetNextRobotState(RobotStateRequest) returns (TimestampedRobotState);
}

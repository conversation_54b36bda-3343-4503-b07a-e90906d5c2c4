syntax = "proto3";

package carbon.frontend.almanac;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";
import "proto/almanac/almanac.proto";

message GetConfigDataResponse {
    option deprecated = true;
    uint32 num_size_categories = 1;
    map<string, string> crop_category_names = 2;
    map<string, string> weed_category_names = 3;
} // Does not change often and can be queried once on page load only

message LoadAlmanacConfigRequest {
    string id = 1; 
}
message LoadAlmanacConfigResponse {
    carbon.aimbot.almanac.AlmanacConfig config = 1;
}

message SaveAlmanacConfigRequest {
    carbon.aimbot.almanac.AlmanacConfig config = 1;
    bool set_active = 2;
}
message SaveAlmanacConfigResponse {
    string id = 1; // if save created a new config this will be the new uuid
}

message SetActiveAlmanacConfigRequest { 
    string id = 1; 
}

message DeleteAlmanacConfigRequest { 
    string id = 1; 
    string new_active_id = 2; // required if id is currently active
}

message GetNextAlmanacConfigResponse { 
    carbon.frontend.util.Timestamp ts = 1;
    string active = 2; 
    map<string, string> available = 3; //ID -> Name
}

message LoadDiscriminatorConfigRequest {
    string id = 1; 
}
message LoadDiscriminatorConfigResponse {
    carbon.aimbot.almanac.DiscriminatorConfig config = 1;
}

message SaveDiscriminatorConfigRequest {
    carbon.aimbot.almanac.DiscriminatorConfig config = 1;
    bool associate_with_active_crop = 2;
}
message SaveDiscriminatorConfigResponse {
    string id = 1; // if save created a new config this will be the new uuid
}

message SetActiveDiscriminatorConfigRequest { 
    string id = 1; 
    optional string crop_id = 2; // If set associate with this crop id instead of active crop id
}

message DeleteDiscriminatorConfigRequest { 
    string id = 1; 
}

message GetNextDiscriminatorConfigResponse { 
    carbon.frontend.util.Timestamp ts = 1;
    string active = 2; 
    map<string, string> available = 3; //ID -> Name
}

message GetNextModelinatorConfigResponse {
    carbon.frontend.util.Timestamp ts = 1;
    carbon.aimbot.almanac.ModelinatorConfig config = 2;
}
message SaveModelinatorConfigRequest {
    carbon.aimbot.almanac.ModelinatorConfig config = 1;
}
message FetchModelinatorConfigRequest {
    string model_id = 1;
    string crop_id = 2;
}
message FetchModelinatorConfigResponse {
    carbon.aimbot.almanac.ModelinatorConfig config = 1;
}
message ResetModelinatorConfigRequest {
}

message GetNextConfigDataRequest {
    carbon.frontend.util.Timestamp ts = 1;
    string lang = 2;
}

message GetNextConfigDataResponse {
    carbon.frontend.util.Timestamp ts = 1;
    uint32 num_size_categories = 2;
    map<string, string> crop_category_names = 3;
    map<string, string> weed_category_names = 4;
}

service AlmanacConfigService {
    rpc GetConfigData(carbon.frontend.util.Empty) returns (GetConfigDataResponse) { option deprecated = true; };
    rpc GetNextConfigData(GetNextConfigDataRequest) returns (GetNextConfigDataResponse);

    rpc LoadAlmanacConfig(LoadAlmanacConfigRequest) returns (LoadAlmanacConfigResponse);
    rpc SaveAlmanacConfig(SaveAlmanacConfigRequest)returns (SaveAlmanacConfigResponse);
    rpc SetActiveAlmanacConfig(SetActiveAlmanacConfigRequest) returns (carbon.frontend.util.Empty);
    rpc DeleteAlmanacConfig(DeleteAlmanacConfigRequest) returns (carbon.frontend.util.Empty);
    rpc GetNextAlmanacConfig(carbon.frontend.util.Timestamp) returns (GetNextAlmanacConfigResponse);

    rpc LoadDiscriminatorConfig(LoadDiscriminatorConfigRequest) returns (LoadDiscriminatorConfigResponse);
    rpc SaveDiscriminatorConfig(SaveDiscriminatorConfigRequest)returns (SaveDiscriminatorConfigResponse);
    rpc SetActiveDiscriminatorConfig(SetActiveDiscriminatorConfigRequest) returns (carbon.frontend.util.Empty);
    rpc DeleteDiscriminatorConfig(DeleteDiscriminatorConfigRequest) returns (carbon.frontend.util.Empty);
    rpc GetNextDiscriminatorConfig(carbon.frontend.util.Timestamp) returns (GetNextDiscriminatorConfigResponse);

    rpc GetNextModelinatorConfig(carbon.frontend.util.Timestamp) returns (GetNextModelinatorConfigResponse);
    rpc SaveModelinatorConfig(SaveModelinatorConfigRequest) returns (carbon.frontend.util.Empty);
    rpc FetchModelinatorConfig(FetchModelinatorConfigRequest) returns (FetchModelinatorConfigResponse);
    rpc ResetModelinatorConfig(ResetModelinatorConfigRequest) returns (carbon.frontend.util.Empty);
}

syntax = "proto3";

package carbon.frontend.category_collection;
option go_package = "proto/frontend";

import "frontend/proto/util.proto";
import "category/proto/category.proto";

message GetNextCategoryCollectionsDataRequest { carbon.frontend.util.Timestamp ts = 1; }

message GetNextCategoryCollectionsDataResponse {
  carbon.frontend.util.Timestamp ts = 1;
  repeated carbon.category.CategoryCollection category_collections = 3;
}

message GetNextActiveCategoryCollectionIdRequest {
  carbon.frontend.util.Timestamp ts = 1;
}

message GetNextActiveCategoryCollectionIdResponse {
  string uuid = 1;
  carbon.frontend.util.Timestamp ts = 2;
  bool reload_required = 3;
  int64 last_updated_timestamp_ms = 4;
}

message SetActiveCategoryCollectionIdRequest {
  string uuid = 1;
  carbon.frontend.util.Timestamp ts = 2;
}

service CategoryCollectionService {
  rpc GetNextCategoryCollectionsData(GetNextCategoryCollectionsDataRequest)
      returns (GetNextCategoryCollectionsDataResponse);
  rpc GetNextActiveCategoryCollectionId(
      GetNextActiveCategoryCollectionIdRequest)
      returns (GetNextActiveCategoryCollectionIdResponse);
  rpc SetActiveCategoryCollectionId(SetActiveCategoryCollectionIdRequest)
      returns (carbon.frontend.util.Empty);
  rpc ReloadCategoryCollection(carbon.frontend.util.Empty)
      returns (carbon.frontend.util.Empty);
}

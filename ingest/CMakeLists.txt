add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)

add_library(ingest SHARED ${SOURCES})
target_compile_definitions(ingest PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(ingest PUBLIC m stdc++fs pthread rt exceptions fmt config_tree_lib config_client_lib trajectory)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(ingest_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(ingest_python  PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
target_link_libraries(ingest_python PUBLIC ingest)
set_target_properties(ingest_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)
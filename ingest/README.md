# Ingest

Ingest is used to distribute trajectories between weed_tracking and other aimbot sub-services.

### ingest
A system that accumulates requests to add/remove trajectories from producers and broadcasts them to a set of clients.

Trajectories are broadcasted once for adds and once for removes by calling `trajectory_request` for each client.

### ingest_producer
Virtual base class for defining trajectory adding and removing from the ingest system.

### ingest_client
Clients receive add and remove requests for trajectories.

#### IngestClient
Ingests all crop and weed trajectories.

Must implement `trajectory_request`

#### FilteredIngestClient
Configurable to ingest adds and/or deletes.

Must implement `_trajectory_req` or override `trajectory_request`


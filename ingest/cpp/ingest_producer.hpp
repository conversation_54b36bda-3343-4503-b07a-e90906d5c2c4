#pragma once
#include <functional>
#include <memory>

#include <trajectory/cpp/trajectory.hpp>

namespace carbon::ingest {
using AddDelCallback = std::function<void(std::shared_ptr<trajectory::Trajectory>)>;
class IngestProducer {
public:
  virtual void set_add_trajectory_callback(AddDelCallback cb) = 0;
  virtual void set_del_trajectory_callback(AddDelCallback cb) = 0;
};
} // namespace carbon::ingest
#pragma once

#include <condition_variable>
#include <memory>
#include <stdint.h>
#include <thread>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include <ingest/cpp/ingest_client.hpp>
#include <ingest/cpp/ingest_producer.hpp>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/utils/thread_safe_queue.hpp>
#include <trajectory/cpp/trajectory.hpp>

namespace carbon {
namespace ingest {
class Ingest {
public:
  Ingest(const std::vector<std::shared_ptr<IngestProducer>> &producers,
         const std::vector<std::shared_ptr<IngestClient<>>> &clients);
  using CLIENT_ID = uint32_t;
  CLIENT_ID add_client(std::shared_ptr<IngestClient<>> client);
  void remove_client(CLIENT_ID id);
  ~Ingest();

private:
  // objects
  std::atomic<CLIENT_ID> next_id_;
  std::unordered_map<uint32_t, std::shared_ptr<trajectory::Trajectory>> trajectories_;
  common::ThreadSafeQueue<Request<>> requests_;
  std::vector<common::ThreadSafeQueue<std::shared_ptr<trajectory::Trajectory>>> slow_inputs_;
  mutable std::mutex mut_;

  const std::vector<std::shared_ptr<IngestClient<>>> clients_;
  std::unordered_map<CLIENT_ID, std::shared_ptr<IngestClient<>>> dynamic_clients_;
  struct ChangeRequest {
    CLIENT_ID id;
    bool add;
    std::shared_ptr<IngestClient<>> client;
    ChangeRequest(CLIENT_ID _id, bool _add, std::shared_ptr<IngestClient<>> _client)
        : id(_id), add(_add), client(_client) {}
  };
  common::ThreadSafeQueue<ChangeRequest> intake_;

  // threads last object for initialization
  std::thread thread_;
  std::vector<std::thread> slow_threads_;

  // functions
  void add_trajectory(std::shared_ptr<trajectory::Trajectory> traj);
  void remove_trajectory(std::shared_ptr<trajectory::Trajectory> traj);
  void processor();
  void slow_loop(size_t index);
};
} // namespace ingest
} // namespace carbon
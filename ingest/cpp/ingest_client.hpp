#pragma once
#include <trajectory/cpp/trajectory.hpp>

#include <memory>
#include <unordered_map>

namespace carbon {
namespace ingest {
enum RequestType {
  INGEST_CLIENT_ADD = 0,
  INGEST_CLIENT_REMOVE = 1,
};

template <typename TrajectoryType = trajectory::Trajectory>
struct Request {
  std::shared_ptr<TrajectoryType> traj;
  RequestType type;
  Request(std::shared_ptr<TrajectoryType> trajectory, RequestType t) : traj(trajectory), type(t) {}
  Request(const Request &rhs) : traj(rhs.traj), type(rhs.type) {}
  Request() : traj(nullptr), type(INGEST_CLIENT_ADD) {}
};

template <typename TrajectoryType = trajectory::Trajectory>
class IngestClient {
public:
  virtual void trajectory_request(const Request<TrajectoryType> &req) = 0;
  virtual void
  backfill(const std::unordered_map<typename TrajectoryType::ID, std::shared_ptr<TrajectoryType>> &trajectories) {
    (void)trajectories;
  }; // can be used by dynamic clients to catchup on insertion
};

template <typename TrajectoryType = trajectory::Trajectory>
class FilteredIngestClient : public IngestClient<TrajectoryType> {
public:
  FilteredIngestClient(bool consume_add, bool consume_remove, bool consume_plant_captcha = false)
      : consume_add_(consume_add), consume_remove_(consume_remove), consume_plant_captcha_(consume_plant_captcha) {}

  virtual void trajectory_request(const Request<TrajectoryType> &req) override {
    if ((consume_add_ && req.type == INGEST_CLIENT_ADD) || (consume_remove_ && req.type == INGEST_CLIENT_REMOVE)) {
      if (req.traj->is_plant_captcha_only() && !consume_plant_captcha_) {
        return;
      }
      _trajectory_req(req);
    }
  }

protected:
  virtual void _trajectory_req(const Request<TrajectoryType> &req) = 0;

private:
  const bool consume_add_;
  const bool consume_remove_;
  const bool consume_plant_captcha_;
};
} // namespace ingest
} // namespace carbon
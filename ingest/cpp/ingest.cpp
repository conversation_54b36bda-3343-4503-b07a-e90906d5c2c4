#include "ingest/cpp/ingest.hpp"

#include <spdlog/spdlog.h>
namespace carbon {
namespace ingest {
constexpr size_t num_deleters = 4; // 1 per producer should be enough
constexpr size_t alarm_queue_size = 5000;
Ingest::Ingest(const std::vector<std::shared_ptr<IngestProducer>> &producers,
               const std::vector<std::shared_ptr<IngestClient<>>> &clients)
    : next_id_(0), clients_(clients), thread_(&Ingest::processor, this) {
  slow_inputs_.resize(num_deleters);
  for (size_t i = 0; i < num_deleters; ++i) {
    slow_threads_.emplace_back([=]() { slow_loop(i); });
  }
  for (auto &producer : producers) {
    producer->set_add_trajectory_callback(std::bind(&Ingest::add_trajectory, this, std::placeholders::_1));
    producer->set_del_trajectory_callback(std::bind(&Ingest::remove_trajectory, this, std::placeholders::_1));
  }
}
Ingest::~Ingest() {
  thread_.join();
  for (auto &t : slow_threads_) {
    t.join();
  }
}
void Ingest::add_trajectory(std::shared_ptr<trajectory::Trajectory> traj) {
  size_t queued = requests_.emplace_add(traj, INGEST_CLIENT_ADD);
  if (queued > alarm_queue_size) {
    spdlog::warn("Ingest queue appears to be growing too large");
  }
}
void Ingest::remove_trajectory(std::shared_ptr<trajectory::Trajectory> traj) {
  size_t queued = requests_.emplace_add(traj, INGEST_CLIENT_REMOVE);
  if (queued > alarm_queue_size) {
    spdlog::warn("Ingest queue appears to be growing too large");
  }
}

Ingest::CLIENT_ID Ingest::add_client(std::shared_ptr<IngestClient<>> client) {
  CLIENT_ID id = ++next_id_;
  intake_.emplace_add_no_notify(id, true, client);
  return id;
}
void Ingest::remove_client(CLIENT_ID id) { intake_.emplace_add_no_notify(id, false, nullptr); }

void Ingest::processor() {
  size_t index = 0;
  auto bse(lib::common::bot::BotStopHandler::get().create_scoped_event("ingest_processor",
                                                                       [this]() { requests_.terminate(); }));
  while (!bse.is_stopped()) {
    auto requests = requests_.wait_pop_all(0);
    if (requests.size() == 0) {
      continue;
    }
    if (!intake_.empty()) {
      while (true) {
        auto cr = intake_.pop();
        if (!cr) {
          break;
        }
        if (cr.value().add && cr.value().client) {
          dynamic_clients_.emplace(cr.value().id, cr.value().client);
          cr.value().client->backfill(trajectories_);
        } else if (!cr.value().add) {
          dynamic_clients_.erase(cr.value().id);
        }
      }
    }
    for (const auto &req : requests) {
      if (req.type == INGEST_CLIENT_ADD) {
        trajectories_.emplace(req.traj->id(), req.traj);
      } else if (req.type == INGEST_CLIENT_REMOVE) {
        trajectories_.erase(req.traj->id());
      }

      for (auto &client : clients_) {
        client->trajectory_request(req);
      }
      for (auto &pair : dynamic_clients_) {
        pair.second->trajectory_request(req);
      }

      if (req.type == INGEST_CLIENT_REMOVE) {
        slow_inputs_[(++index % num_deleters)].add(req.traj);
      }
    }
  }
}
void Ingest::slow_loop(size_t index) {
  auto bse(lib::common::bot::BotStopHandler::get().create_scoped_event(
      fmt::format("ingest_slow_loop_{}", index), [this, index]() { slow_inputs_[index].terminate(); }));
  std::unordered_set<std::shared_ptr<trajectory::Trajectory>> slow_trajectories;
  while (!bse.is_stopped()) {
    for (auto traj : slow_inputs_[index].wait_pop_all(1000)) {
      if (traj.use_count() == 1) {
        continue;
      } else {
        slow_trajectories.insert(traj);
      }
    }
    for (auto it = slow_trajectories.begin(); it != slow_trajectories.end();) {
      if ((*it).use_count() == 1) {
        it = slow_trajectories.erase(it);
      } else {
        ++it;
      }
    }
  }
}

} // namespace ingest
} // namespace carbon
#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include <ingest/cpp/ingest.hpp>
#include <ingest/cpp/ingest_client.hpp>
#include <ingest/cpp/ingest_producer.hpp>

namespace py = pybind11;

namespace carbon::ingest {

PYBIND11_MODULE(ingest_python, m) {
  py::class_<IngestProducer, std::shared_ptr<IngestProducer>>(m, "IngestProducer");
  py::class_<IngestClient<>, std::shared_ptr<IngestClient<>>>(m, "IngestClient");

  py::class_<Ingest, std::shared_ptr<Ingest>>(m, "Ingest")
      .def(py::init<const std::vector<std::shared_ptr<IngestProducer>> &,
                    const std::vector<std::shared_ptr<IngestClient<>>> &>(),
           py::arg("producers"), py::arg("clients"), py::call_guard<py::gil_scoped_release>())
      .def("add_client", &Ingest::add_client, py::arg("client"), py::call_guard<py::gil_scoped_release>())
      .def("remove_client", &Ingest::remove_client, py::arg("id"), py::call_guard<py::gil_scoped_release>());
}

} // namespace carbon::ingest
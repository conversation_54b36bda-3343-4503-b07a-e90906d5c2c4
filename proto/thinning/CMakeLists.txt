# This File Compiles all the proto files in this directory into a single lib
file(GLOB PROTO_FILES *.proto)

foreach(PROTO_FILE ${PROTO_FILES})
CompileProto(${PROTO_FILE} GENERATED_PATH GOPKG proto/thinning LANGS go go-grpc grpc_python python mypy grpc cpp)
endforeach()

string(REGEX REPLACE "${CMAKE_CURRENT_SOURCE_DIR}" "${GENERATED_PATH}" PROTO_GEN "${PROTO_FILES}")
string(REGEX REPLACE "[.]proto" ".pb.cc" PROTO_SOURCES "${PROTO_GEN}")
string(REGEX REPLACE "[.]proto" ".grpc.pb.cc" GRPC_SOURCES "${PROTO_GEN}")

add_library(thinning_proto SHARED ${PROTO_SOURCES} ${GRPC_SOURCES})
target_compile_options(thinning_proto PRIVATE "-w")
target_link_libraries(thinning_proto grpc++ protobuf)
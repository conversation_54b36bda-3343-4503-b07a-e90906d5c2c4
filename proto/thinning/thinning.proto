syntax = "proto3";

package carbon.thinning;
option go_package = "proto/thinning";

message Box {
    double width = 1;
    double height = 2;
}
message DoubleBox {
    option deprecated = true;
    Box box_1 =1;
    Box box_2 =2;
    double ideal_y_dist = 3;
}
message SizedNotSoGreedyCfg {
  Box min_keepout = 1;
  double max_y_search_radius = 2;
  double ideal_y_dist = 3;
  double size_weight = 4;
  double dist_weight = 5;
}
message Bounds {
  oneof bounds {
    Box box = 1;
    DoubleBox double_box = 2 [deprecated = true ];
    SizedNotSoGreedyCfg sized_cfg = 3;
  }
}
message SizeFilter {
  option deprecated = true;
  bool enabled = 1;
  uint64 samples_size = 2;
  double acceptable_variance = 3;
}
message ConfigDefinition {
    string name = 1;
    bool active = 2 [ deprecated = true ];
    Bounds row_1 = 3;
    Bounds row_2 = 4;
    Bounds row_3 = 5;
    SizeFilter size_filter = 6 [ deprecated = true ];
    string id = 7; //Auto generated uuid if new
    map<int32, Bounds> rows = 8;

}
syntax = "proto3";

package carbon.model_receiver;
option go_package = "proto/model_receiver";

message DownloadModelArtifactRequest {
  string model_id = 1;
  string artifact_name = 2;
  bytes artifact_contents = 4;
}

message DownloadModelMetadataRequest {
  string model_id = 1;
  bytes metadata_contents = 2;
}

message Empty {}

message Model {
  string id = 1;
  repeated string artifact_ids = 4;
  string model_sha = 2;
  string metadata_sha = 3;
}

message DownloadedModelResponse { repeated Model models = 1; }

message CleanupModelsRequest {
  repeated string model_id = 1;
}

// Chip related messages
message DownloadChipRequest {
  string chip_id = 1;
  bytes contents = 2;
}

message DownloadChipMetadataRequest {
  string chip_id = 1;
  bytes metadata_contents = 2;
}

message Chip {
  string id = 1;
}

message GetDownloadedChipsResponse { repeated Chip chips = 1; }

message RemoveChipsRequest {
  repeated string chip_id = 1;
}

service ModelReceiver {
  rpc DownloadModelArtifact(DownloadModelArtifactRequest) returns (Empty);
  rpc DownloadModelMetadata(DownloadModelMetadataRequest) returns (Empty);
  rpc GetDownloadedModels(Empty) returns (DownloadedModelResponse);
  rpc CleanupModels(CleanupModelsRequest) returns (Empty);

  // Category related functions
  rpc DownloadChip(DownloadChipRequest) returns (Empty);
  rpc DownloadChipMetadata(DownloadChipMetadataRequest) returns (Empty);
  rpc GetDownloadedChips(Empty) returns (GetDownloadedChipsResponse);
  rpc RemoveChips(RemoveChipsRequest) returns (Empty);
}

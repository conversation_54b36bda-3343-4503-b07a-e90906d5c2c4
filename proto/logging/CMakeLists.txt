# This File Compiles all the proto files in this directory into a single lib
file(GLOB PROTO_FILES *.proto)

foreach(PROTO_FILE ${PROTO_FILES})
CompileProto(${PROTO_FILE} GENERATED_PATH GOPKG proto/logging LANGS python mypy grpc_python go go-grpc cpp grpc)
endforeach()

string(REGEX REPLACE "${CMAKE_CURRENT_SOURCE_DIR}" "${GENERATED_PATH}" PROTO_GEN "${PROTO_FILES}")
string(REGEX REPLACE "[.]proto" ".pb.cc" PROTO_SOURCES "${PROTO_GEN}")
string(REGEX REPLACE "[.]proto" ".grpc.pb.cc" GRPC_SOURCES "${PROTO_GEN}")

add_library(logging_proto ${PROTO_SOURCES} ${GRPC_SOURCES})
add_dependencies(logging_proto generate_proto_logging_logging)
target_compile_options(logging_proto PRIVATE "-w")
target_link_libraries(logging_proto grpc++ protobuf)
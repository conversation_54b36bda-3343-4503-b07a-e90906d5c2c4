syntax = "proto3";

package carbon.rtc;
option go_package = "proto/rtc";

import "google/protobuf/empty.proto";

// TODO:(smt) this should mirror nanopb messages for hh, we need to link back
// with comments or docs.
enum HHStateStatus {
  HH_UNKNOWN = 0;
  HH_DISABLED = 1;
  HH_OPERATIONAL = 2;
  HH_STOPPED = 3;
  HH_SAFE = 4;
  HH_ESTOP = 5;
}

// RobotRequiredState denotes requirements from the cloud that should be
// enforced on the robot.
message RobotRequiredState { bool stopped_state = 1; }

// RobotStatusInfo is the robot returning info that is relevant for the
// requirements. Ex: robot in DISABLED state would qualify as success for
// requesting 'stopped state'
message RobotStatusInfo { HHStateStatus state = 1; }

message SetRobotRequiredStateRequest {
  int64 timestamp_ms = 1;
  string robot_serial = 2;
  RobotRequiredState required_state = 3;
}

message GetRobotRequiredStateRequest {
  int64 timestamp_ms = 1;
  string robot_serial = 2;
}

message GetRobotRequiredStateResponse {
  int64 timestamp_ms = 1;
  RobotRequiredState required_state = 2;
}

// RobotState service is for informing the robot about cloud side state
// requirements.
service RobotState {
  rpc SetRobotRequiredState(SetRobotRequiredStateRequest)
      returns (google.protobuf.Empty);
  rpc GetNextRequiredState(GetRobotRequiredStateRequest)
      returns (GetRobotRequiredStateResponse);

  rpc RobotRequirementStream(stream RobotStatusInfo)
      returns (stream RobotRequiredState);
}
syntax = "proto3";

package carbon.rtc;
option go_package = "proto/rtc";

message AuthStatus {
  bool read = 1;
  bool write = 2;
}

message RtcMessage{
  uint64 id = 1;
  bytes msg = 2;
  AuthStatus auth = 3;
}

message SignalingMsg {
  bytes msg = 1;
}

message StreamListRequest {
}

message StreamListResponse {
  map<string, string> streams = 1; // map of human readable name to globally unique stream id
}

service Broadcaster {
  rpc MessageBus(stream RtcMessage) returns (stream RtcMessage); // Note requires metadata field data_channel to be set
  rpc LocalSignalServer(stream SignalingMsg) returns (stream SignalingMsg);
  rpc GetStreamList(StreamListRequest) returns (StreamListResponse);
}
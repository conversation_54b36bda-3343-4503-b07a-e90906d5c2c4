syntax = "proto3";

package carbon.rtc;
option go_package = "proto/rtc";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

import "proto/geo/geo.proto";

message RobotData {
  uint64 task_id = 1; // 0 is not a valid task_id and can signify no task.
  // Indicates whether the robot was making progress on its task, using a
  // task-specific definition of "active" that is determined by the robot. For
  // example, for a laserweeding task, the state might be determined by whether
  // the hitch is lowered.
  bool active = 2;

  // Objective ID is for tracking purposes and ease of querying contiguous
  // location data. 0 is not a valid objective_id and signifies no objective.
  uint64 objective_id = 3;
}

message LocationHistoryRecord {
  geo.Point point = 1;
  // Facing direction of the robot, if available from hardware.
  optional double heading_degrees = 4;
  google.protobuf.Timestamp timestamp = 2;
  RobotData data = 3;
}

message LocationHistoryRecordList {
  repeated LocationHistoryRecord records = 1;
}

message LogLocationHistoryRequest { LocationHistoryRecordList history = 1; }

message ListRobotsRequest {
  int32 page_size = 1;
  string page_token = 2;
  // If not empty, only list robots with the given serials.
  repeated string robot_serials = 3;
}

message ListRobotsResponse {
  string next_page_token = 1;
  repeated RobotSummary robots = 2;
}

message RobotSummary {
  string serial = 1;
  LocationHistoryRecord last_seen = 2;
}

message ListLocationHistoryRequest {
  int32 page_size = 1;
  string page_token = 2;
  string robot_serial = 3;
  google.protobuf.Timestamp start = 4;
  google.protobuf.Timestamp end = 5;
  bool desc = 6;
  bool include_closest = 7;
  uint64 task_id = 8;
  uint64 objective_id = 9;
}

message ListLocationHistoryResponse {
  string next_page_token = 1;
  LocationHistoryRecordList history = 2;
}

service LocationHistory {
  rpc LogLocationHistory(LogLocationHistoryRequest)
      returns (google.protobuf.Empty);
  rpc ListRobots(ListRobotsRequest) returns (ListRobotsResponse);
  rpc ListLocationHistory(ListLocationHistoryRequest)
      returns (ListLocationHistoryResponse);
  rpc StreamLocation(stream LocationHistoryRecord)
      returns (google.protobuf.Empty);
}

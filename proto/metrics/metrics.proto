syntax = "proto3";

package carbon.metrics;
option go_package = "proto/metrics";

message LaserPosition {
  uint32 row = 1;
  uint32 slot = 2;
}

message LaserIdentifier {
  LaserPosition position = 1;
  string serial = 2;
}

message LaserLifeTime {
  LaserIdentifier id = 1;
  uint64 lifetime_sec = 2;
}
message LaserEventTime {
  LaserIdentifier id = 1;
  int64 timestamp_sec = 2;
}

message LaserLifeTimes {
  repeated LaserLifeTime lifetimes = 1;
}
message LaserChangeTimes {
  repeated LaserEventTime installs = 1;
  repeated LaserEventTime removals = 2;
}

message CountsByConclusionType {
  repeated uint32 disarmed_weed = 1;
  repeated uint32 armed_weed = 2;
  repeated uint32 disarmed_crop = 3;
  repeated uint32 armed_crop = 4;
}
message TargetSizeData {
  double cumulative_size = 1;
  uint64 count = 2;
}
message RequiredLaserTimeData {
  uint64 cumulative_time = 1;
  uint64 count = 2;
}
message SpatialPosition {
  double latitude = 1;
  double longitude = 2;
  double height_mm = 3;
  uint64 timestamp_ms = 4;
  double ecef_x = 5;
  double ecef_y = 6;
  double ecef_z = 7;
}
message WeedCounterChunk {
  CountsByConclusionType  conclusion_counts = 1;
  TargetSizeData weed_size_data = 2;
  TargetSizeData crop_size_data = 3;
  map<string, uint32> counts_by_category = 4;
  RequiredLaserTimeData targeted_laser_time_data = 5;
  RequiredLaserTimeData untargeted_laser_time_data  = 6;
  uint64 valid_crop_count = 7;
}
message WheelEncoderSpatialData {
  float start_pos_m = 1;
  float end_pos_m = 2;
}
message BandingSpatialData {
  float percent_banded = 1;
}
message ImplementWidthSpatialData {
  float width_mm = 1;
}
message VelocitySpatialMetric {
  map<string, float> avg_target_vel = 1;
}
message JobMetric {
  string job_id = 1;
}
message HWMetric {
  bool lifted = 1;
  bool estopped = 2;
  bool laser_key = 3;
  bool interlock = 4;
  bool water_protect = 5;
  bool debug_mode = 6;
}
message SpatialMetricBlock {
  SpatialPosition start = 1;
  SpatialPosition end = 2;
  WeedCounterChunk weed_count = 3;
  WheelEncoderSpatialData we_data = 4;
  BandingSpatialData banding_data = 5;
  ImplementWidthSpatialData implement_width_data = 6;
  VelocitySpatialMetric vel_data = 7;
  SpatialPosition start_left = 8;
  SpatialPosition start_right = 9;
  SpatialPosition end_left = 10;
  SpatialPosition end_right = 11;
  JobMetric job_metric = 12;
  bool suspicious = 13; // May not be a valid block
  HWMetric hw_metric = 14;
}
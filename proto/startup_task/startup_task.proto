syntax = "proto3";

package carbon.startup_task;
option go_package = "proto/startup_task";

enum TaskState {
  QUEUED = 0;
  IN_PROGRESS = 1;
  COMPLETE = 2;
  ERROR = 3;
  UNKNOWN = 4;
}

message CrosshairCalibrationTask {
    uint32 row_number = 1;
    uint32 laser_id = 2;
}

message Task {
    string id = 1;
    string label = 2;
    string description = 3;
    TaskState state = 4;
    oneof task_details {
        CrosshairCalibrationTask crosshair_cal_task = 5;
    }
}
syntax = "proto3";

package carbon.aimbot.almanac;
option go_package = "proto/almanac";

message Formula {
    float multiplier = 1; // A
    float offset = 2; // b
    float exponent =3; // e
    uint32 fine_tune_multiplier = 4; // discrete val 1-10
    uint32 max_time = 5; // Time to cap all shooting at
    float fine_tune_multiplier_val = 6; // how big each step is for moving ftm: F= 1.0 + (5-ftm)*ftm_val
} // F * (A*(x^(e)) + b)

message Trust {
    bool ignorable = 1; // In weeding Can be skipped if not enough time (will not be used in VE, but can be targeted still)
    bool avoid = 2; // Do not allow shooting this
}

message ModelTrust {
    float min_doo = 1; // Minimum detections over opportuninty
    float weeding_threshold = 2; // for weed: acts as WPT, for crop: used as crop protect threshold
    float thinning_threshold = 3; // for crop: used as keeper theshold, for weed: used as reverse crop protect threshold 
    float banding_threshold = 4; // for crop: threshold for use in banding algo
}

enum CategoryClassification {
    CATEGORY_WEED = 0;
    CATEGORY_CROP = 1;
}

message TypeCategory {
    string category = 1; // This is a uuid which maps to a crop id or weed id
    CategoryClassification classification = 2;
}

message AlmanacTypeCategory {
    TypeCategory type = 1;
    repeated float sizes = 2; //Each size goes from [sizes[i-1], sizes[i]) for i=0 we use [0, sizes[i]) additionally we add [sizes[N], inf) to the end so for 3 categories only need 2 values
    repeated Formula formulas = 3; // len(formulas) == len(sizes) +1
}

message DiscriminatorTypeCategory {
    TypeCategory type = 1;
    repeated Trust trusts = 2;
}

message ModelinatorTypeCategory {
    TypeCategory type = 1;
    repeated ModelTrust trusts = 2;
}

message AlmanacConfig {
    string id = 1; // auto generated uuid
    string name = 2;
    bool protected = 3;
    uint64 updated_ts = 4;
    repeated AlmanacTypeCategory categories = 5; //Must contain an instance with category="DEFAULT"
}

message DiscriminatorConfig {
    string id = 1; // auto generated uuid
    string name = 2;
    bool protected = 3;
    uint64 updated_ts = 4;
    repeated DiscriminatorTypeCategory categories = 5;
}

message ModelinatorConfig {
    string model_id = 1; // must match an existing model id
    string crop_id = 2; // must match an existing crop id
    bool modified = 3; // Set if has been modifed by user from model suggestion
    repeated ModelinatorTypeCategory categories = 4;
}
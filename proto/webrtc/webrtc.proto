syntax = "proto3";

package carbon.webrtc;
option go_package = "proto/webrtc";
option optimize_for = LITE_RUNTIME;

message CreateConnectionRequest {
    string id = 1;
}

message CreateConnectionResponse {
    string sdp_json = 1;
}

message SetRemoteSdpRequest {
    string id = 1;
    string sdp_json = 2;
}
message SetRemoteSdpResponse {
}

service WebrtcService {
    rpc CreateConnection(CreateConnectionRequest) returns (CreateConnectionResponse);
    rpc SetRemoteSdp(SetRemoteSdpRequest) returns (SetRemoteSdpResponse);
}
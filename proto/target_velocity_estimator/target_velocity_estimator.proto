syntax = "proto3";

package carbon.aimbot.target_velocity_estimator;
option go_package = "proto/target_velocity_estimator";

message TVERowProfile {
    float primary_kill_rate = 1;
    float secondary_kill_rate = 2;
}
message TVEProfile {
    string id = 1;
    string name = 2;
    int64 update_ts = 3;
    bool protected = 4;
    float cruise_offset_percent = 5;
    float primary_range = 6;
    float secondary_range = 7;
    float increase_smoothing = 8;
    float decrease_smoothing = 9;
    TVERowProfile row_1 = 10;
    TVERowProfile row_2 = 11;
    TVERowProfile row_3 = 12;
    map<uint32, TVERowProfile> rows = 13;
    float min_vel_mph = 14;
    float max_vel_mph = 15;
}
message ProfileDetails {
  string id = 1;
  string name = 2;
}
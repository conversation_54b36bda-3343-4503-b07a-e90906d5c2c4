# This File Compiles all the proto files in this directory into a single lib
file(GLOB PROTO_FILES *.proto)

foreach(PROTO_FILE ${PROTO_FILES})
CompileProto(${PROTO_FILE} GENERATED_PATH GOPKG proto/module/types LANGS go python mypy cpp)
endforeach()

string(REGEX REPLACE "${CMAKE_CURRENT_SOURCE_DIR}" "${GENERATED_PATH}" PROTO_GEN "${PROTO_FILES}")
string(REGEX REPLACE "[.]proto" ".pb.cc" PROTO_SOURCES "${PROTO_GEN}")

add_library(module_type_proto SHARED ${PROTO_SOURCES})
target_compile_options(module_type_proto PRIVATE "-w")
target_link_libraries(module_type_proto grpc++ protobuf)

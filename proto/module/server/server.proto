syntax = "proto3";

package carbon.module.server;
option go_package = "proto/module/server";

import "proto/module/types/types.proto";
import "hardware_manager/proto/hardware_manager_service.proto";

message GetModuleIdentityResponse {
    types.ModuleIdentity identity = 1;
}

message SetModuleSerialNumberRequest {
    string serial_number = 1;
    bool force = 2;
}

message SetModuleIdentityRequest {
    types.ModuleIdentity identity = 1;
}

service ModuleServerService {
    rpc GetModuleIdentity(types.Empty) returns (GetModuleIdentityResponse);
    rpc SetModuleSerialNumber(SetModuleSerialNumberRequest) returns (types.Empty);
    rpc SetModuleIdentity(SetModuleIdentityRequest) returns (types.Empty);

    rpc GetModuleSensors(types.Empty) returns (hardware_manager.ReaperPcSensorData);
}

syntax = "proto3";

package carbon.software_manager;
option go_package = "proto/software_manager";

message SoftwareVersionMetadata {
    string tag = 1;
    repeated string containers = 2;
    string system = 3;
    bool ready = 4;
    bool available = 5;
}

message SoftwareVersionMetadataRequest {
    string tag = 1;
}

message VersionSummaryRequest {

}

message VersionSummaryReply {
    SoftwareVersionMetadata current = 1;
    SoftwareVersionMetadata target = 2;
    SoftwareVersionMetadata previous = 3;
    bool updating = 4;
}

message GetIdentityRequest {

}

message ComputerRole {
    string role = 1;
    string row = 2;
    repeated string extra_roles = 3;
}

message IdentityInfo {
    string name = 1;
    string generation = 2;
    ComputerRole role = 3;
    string auth_client_id = 4;
    string auth_client_secret = 5;
    string auth_domain = 6;
    string carbon_robot_username = 7;
    string carbon_robot_password = 8;
    string environment = 9;
}

message ClearPackagesCacheRequest {

}

message ClearPackagesCacheResponse {

}

message PrepareUpdateRequest {
    string tag = 1;
    string req_id = 2;
}

message PrepareUpdateResponse {

}

message AbortUpdateRequest {
    string tag = 1;
    string req_id = 2;
}

message AbortUpdateResponse {

}

message TriggerUpdateRequest {
    string tag = 1;
    string req_id = 2;
}

message TriggerUpdateReply {

}

message SystemVersionStateReply {
    bool confirmed = 1;
}


message empty {}


service SoftwareManagerService {
    rpc GetSoftwareVersionMetadata(SoftwareVersionMetadataRequest) returns (SoftwareVersionMetadata);
    rpc GetVersionsSummary(VersionSummaryRequest) returns (VersionSummaryReply);
    rpc GetIdentity(GetIdentityRequest) returns (IdentityInfo);
    rpc ClearPackagesCache(ClearPackagesCacheRequest) returns (ClearPackagesCacheResponse);
    rpc PrepareUpdate(PrepareUpdateRequest) returns (PrepareUpdateResponse);
    rpc AbortUpdate(AbortUpdateRequest) returns (AbortUpdateResponse);
    rpc TriggerUpdate(TriggerUpdateRequest) returns (TriggerUpdateReply);
    rpc Reboot(empty) returns (empty);
    rpc PushRobotDefinitionUpdate(empty) returns (empty);
    rpc RestartDependentServices(empty) returns (empty);
    rpc Ping(empty) returns (empty);
}
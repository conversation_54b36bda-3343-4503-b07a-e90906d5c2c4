syntax = "proto3";

package carbon.version_metadata;
option go_package = "proto/version_metadata";

message VersionMetadata {
  repeated string containers = 1;
  string systemVersion = 2;
}

message GetVersionMetadataRequest {
  string version = 1;
  string gen = 2;
}

message UploadVersionMetadataRequest {
  string version = 1;
  string gen = 2;
  VersionMetadata versionMetadata = 3;
}

message Empty {}

service VersionMetadataService {
  rpc GetVersionMetadata(GetVersionMetadataRequest) returns (VersionMetadata);
  rpc UploadVersionMetadata(UploadVersionMetadataRequest) returns (Empty);
}

#include "generated/recorder/proto/recorder.pb.h"
#include <iostream>
#include <recorder/lib/proto_file_stream.hpp>

using namespace carbon::recorder;

int main() {

  ::recorder::RotaryTicksRecord record;
  record.set_record_timestamp_ms(12345678930);
  auto snapshot = record.mutable_snapshot();
  snapshot->set_timestamp_us(12345678903);
  snapshot->set_bl(1000000000);
  snapshot->set_br(1234567890);
  snapshot->set_fl(1234567890);
  snapshot->set_fr(1234567890);

  std::cout << "Hello World: " << record.ByteSizeLong() << std::endl;
  {
    ProtobufStreamFileWriter writer("RotaryTicksRecord", "test.carbon");
    writer.append(record);
    record.set_record_timestamp_ms(42);
    writer.append(record);
  }
  {
    ::recorder::RotaryTicksRecord record2;
    ProtobufStreamFileReader reader("RotaryTicksRecord", "test.carbon");
    reader.next(&record2);
    std::cout << "I Read: " << record2.record_timestamp_ms() << std::endl;
    reader.next(&record2);
    std::cout << "I Read: " << record2.record_timestamp_ms() << std::endl;
  }

  return 0;
}

#include "generated/recorder/proto/recorder.pb.h"
#include <chrono>
#include <iostream>
#include <lib/common/cpp/time.h>
#include <recorder/lib/proto_file_stream.hpp>
#include <spdlog/spdlog.h>
#include <stdio.h>
#include <thread>

using namespace carbon::recorder;

int main(int argc, char **argv) {
  if (argc < 2) {
    const std::string bin_path(argv[0]);
    std::cout << "Usage:" << std::endl;
    std::cout << "  " << bin_path << " <filepath>" << std::endl;
    return 0;
  }

  auto file = fopen64(argv[1], "rb");

  uint64_t derp = 0;

  fpos64_t prev;

  fgetpos64(file, &prev);

  auto read_size = fread(&derp, 8, 1, file);

  if (read_size != 1) {
    std::cout << "Read Size Non-Matching: " << read_size << std::endl;
  }

  std::cout << std::hex << derp << std::endl;

  fsetpos64(file, &prev);

  read_size = fread(&derp, 8, 1, file);

  if (read_size != 1) {
    std::cout << "Read Size Non-Matching: " << read_size << std::endl;
  }

  std::cout << std::hex << derp << std::endl;

  fclose(file);

  // The first parameter is the filepath to read
  std::string filepath(argv[1]);

  ::recorder::RotaryTicksRecord record;
  record.set_record_timestamp_ms(12345678930);

  ProtobufStreamFileReader reader("RotaryTicksRecord", filepath);

  uint64_t record_start_time_ms = 0;
  uint64_t comp_start_time_ms = 0;

  while (reader.is_valid() && !reader.is_done()) {
    bool result = reader.next(&record);
    if (result) {
      if (record_start_time_ms == 0) {
        record_start_time_ms = record.record_timestamp_ms();
        comp_start_time_ms = maka_control_timestamp_ms();
      }
      auto record_delta_ms = record.record_timestamp_ms() - record_start_time_ms;
      auto comp_delta_ms = maka_control_timestamp_ms() - comp_start_time_ms;

      if (comp_delta_ms < record_delta_ms) {
        auto sleep_time_ms = record_delta_ms - comp_delta_ms;
        if (sleep_time_ms > 45) {
          spdlog::warn("Latency Spike!");
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_time_ms));
      }

      auto snap = record.snapshot();
      spdlog::info("Rec TS: {}, TS: {}, FL: {}, FR: {}, BL: {}, BR: {}", record.record_timestamp_ms(),
                   snap.timestamp_us(), snap.fl(), snap.fr(), snap.bl(), snap.br());
    }
  }

  while (reader.is_valid() && !reader.is_at_start()) {
    bool result = reader.previous(&record);
    if (result) {
      auto snap = record.snapshot();
      spdlog::info("Prev Rec TS: {}, TS: {}, FL: {}, FR: {}, BL: {}, BR: {}", record.record_timestamp_ms(),
                   snap.timestamp_us(), snap.fl(), snap.fr(), snap.bl(), snap.br());
    }
  }

  return 0;
}

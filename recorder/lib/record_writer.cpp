#include "recorder/lib/record_writer.hpp"

namespace carbon::recorder {

RecordWriter::RecordWriter(const std::string &record_type, const std::string &filename)
    : record_type_(record_type), filename_(filename), task_(&RecordWriter::run, this) {}

void RecordWriter::run() {
  auto writer = ProtobufStreamFileWriter(record_type_, filename_);
  while (!terminated_) {
    auto msg = queue_.wait_pop(0);
    if (!msg) {
      continue;
      ;
    } else {
      writer.append(*msg.value().get());
    }
  }
}

void RecordWriter::append(std::shared_ptr<::google::protobuf::MessageLite> message) { queue_.add(message); }

void RecordWriter::terminate() {
  terminated_ = true;
  queue_.terminate();
}

void RecordWriter::join() {
  if (task_.joinable()) {
    task_.join();
  }
}

} // namespace carbon::recorder
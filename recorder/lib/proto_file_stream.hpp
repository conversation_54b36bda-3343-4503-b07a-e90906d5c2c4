#pragma once

#include <fstream>
#include <optional>
#include <stack>
#include <stdio.h>
#include <string>
#include <tuple>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/io/zero_copy_stream_impl.h>
#include <google/protobuf/message_lite.h>

#define CARBON_PROTO_FILE_STREAM_MAGIC 0x4E4F42524143

namespace carbon {
namespace recorder {

class ProtobufStreamFileWriter {
public:
  ProtobufStreamFileWriter(std::string type_name, std::string filepath);
  ~ProtobufStreamFileWriter();
  bool append(::google::protobuf::MessageLite &message);
  bool append(const std::string &msg);
  bool is_valid();

private:
  struct PathMaker {
    std::string filepath;
    PathMaker(const std::string &_filepath);
  };
  bool valid_;
  std::string type_name_;
  PathMaker filepath_;
  std::ofstream file_stream_;
  ::google::protobuf::io::OstreamOutputStream *raw_output_stream_;
  ::google::protobuf::io::CodedOutputStream *coded_output_stream_;
};

class ProtobufStreamFileReader {
public:
  ProtobufStreamFileReader(std::string type_name, std::string filepath);
  ~ProtobufStreamFileReader();
  bool next(::google::protobuf::MessageLite *message);
  bool previous(::google::protobuf::MessageLite *message);
  bool current(::google::protobuf::MessageLite *message);
  std::tuple<bool, std::string> next();
  std::tuple<bool, std::string> previous();
  std::tuple<bool, std::string> current();
  bool is_valid();
  bool is_done();
  bool is_at_start();

private:
  void resize_buffer_if_needed(uint32_t size);
  bool _previous();
  bool _current();
  std::tuple<bool, uint32_t> _next();
  bool valid_;
  bool done_;
  std::string type_name_;
  std::string filepath_;
  FILE *stream_;
  std::stack<fpos64_t> previous_pointers_;
  uint32_t buffer_size_;
  uint8_t *buffer_;
};

} // namespace recorder
} // namespace carbon
#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include <recorder/lib/proto_file_stream.hpp>

namespace py = pybind11;

namespace carbon::recorder {

PYBIND11_MODULE(recorder_python, m) {
  py::class_<ProtobufStreamFileReader, std::shared_ptr<ProtobufStreamFileReader>>(m, "ProtobufStreamFileReader")
      .def(py::init<std::string, std::string>(), py::arg("type_name"), py::arg("filepath"),
           py::call_guard<py::gil_scoped_release>())
      .def("next",
           [](ProtobufStreamFileReader &self) {
             auto [valid, data] = self.next();
             return std::make_tuple<bool, py::bytes>(std::forward<bool>(valid), py::bytes(data));
           })
      .def("previous",
           [](ProtobufStreamFileReader &self) {
             auto [valid, data] = self.previous();
             return std::make_tuple<bool, py::bytes>(std::forward<bool>(valid), py::bytes(data));
           })
      .def("current",
           [](ProtobufStreamFileReader &self) {
             auto [valid, data] = self.current();
             return std::make_tuple<bool, py::bytes>(std::forward<bool>(valid), py::bytes(data));
           })
      .def("is_valid", &ProtobufStreamFileReader::is_valid, py::call_guard<py::gil_scoped_release>())
      .def("is_done", &ProtobufStreamFileReader::is_done, py::call_guard<py::gil_scoped_release>())
      .def("is_at_start", &ProtobufStreamFileReader::is_at_start, py::call_guard<py::gil_scoped_release>());
  py::class_<ProtobufStreamFileWriter, std::shared_ptr<ProtobufStreamFileWriter>>(m, "ProtobufStreamFileWriter")
      .def(py::init<std::string, std::string>(), py::arg("type_name"), py::arg("filepath"),
           py::call_guard<py::gil_scoped_release>())
      .def("append", py::overload_cast<const std::string &>(&ProtobufStreamFileWriter::append),
           py::call_guard<py::gil_scoped_release>())
      .def("is_valid", &ProtobufStreamFileWriter::is_valid, py::call_guard<py::gil_scoped_release>());
}

} // namespace carbon::recorder
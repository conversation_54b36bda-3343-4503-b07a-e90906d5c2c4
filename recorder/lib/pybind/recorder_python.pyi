from typing import AnyStr, <PERSON>ple

class ProtobufStreamFileReader:
    def __init__(self, type_name: str, filepath: str) -> None: ...
    def next(self) -> <PERSON><PERSON>[bool, bytes]: ...
    def previous(self) -> <PERSON><PERSON>[bool, bytes]: ...
    def current(self) -> <PERSON><PERSON>[bool, bytes]: ...
    def is_valid(self) -> bool: ...
    def is_done(self) -> bool: ...
    def is_at_start(self) -> bool: ...

class ProtobufStreamFileWriter:
    def __init__(self, type_name: str, filepath: str) -> None: ...
    def append(self, msg: AnyStr) -> bool: ...
    def is_valid(self) -> bool: ...

#pragma once
#include "lib/common/cpp/utils/thread_safe_queue.hpp"
#include <google/protobuf/message_lite.h>
#include <recorder/lib/proto_file_stream.hpp>
#include <spdlog/spdlog.h>

namespace carbon::recorder {

class RecordWriter {
public:
  RecordWriter(const std::string &record_type, const std::string &filename);
  void append(std::shared_ptr<::google::protobuf::MessageLite> message);
  void terminate();
  void join();

private:
  std::string record_type_;
  std::string filename_;
  ::carbon::common::ThreadSafeQueue<std::shared_ptr<::google::protobuf::MessageLite>> queue_;
  std::thread task_;
  std::atomic<bool> terminated_ = false;

  void run();
};

} // namespace carbon::recorder
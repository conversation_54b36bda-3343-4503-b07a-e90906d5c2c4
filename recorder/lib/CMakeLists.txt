file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp)

add_library(recorder_lib SHARED ${SOURCES})
target_compile_definitions(recorder_lib PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(recorder_lib PUBLIC exceptions m spdlog fmt pthread grpc++ absl_synchronization)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(recorder_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(recorder_python PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
target_link_libraries(recorder_python PUBLIC recorder_lib protobuf)
set_target_properties(recorder_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)
#include <filesystem>
#include <recorder/lib/proto_file_stream.hpp>
#include <spdlog/spdlog.h>

namespace carbon {
namespace recorder {

ProtobufStreamFileWriter::PathMaker::PathMaker(const std::string &_filepath) : filepath(_filepath) {
  std::filesystem::path fpath(filepath);
  if (fpath.has_filename()) {
    std::filesystem::create_directories(fpath.parent_path());
  }
}
ProtobufStreamFileWriter::ProtobufStreamFileWriter(std::string type_name, std::string filepath)
    : valid_(true), type_name_(type_name), filepath_(filepath), file_stream_(filepath),
      raw_output_stream_(new ::google::protobuf::io::OstreamOutputStream(&file_stream_)),
      coded_output_stream_(new ::google::protobuf::io::CodedOutputStream(raw_output_stream_)) {
  coded_output_stream_->WriteLittleEndian64(CARBON_PROTO_FILE_STREAM_MAGIC);
  valid_ = !coded_output_stream_->HadError();

  if (!valid_) {
    return;
  }

  coded_output_stream_->WriteLittleEndian32((uint32_t)type_name.length());
  valid_ = !coded_output_stream_->HadError();

  if (!valid_) {
    return;
  }

  coded_output_stream_->WriteString(type_name);
  valid_ = !coded_output_stream_->HadError();
}

ProtobufStreamFileWriter::~ProtobufStreamFileWriter() {
  delete coded_output_stream_;
  delete raw_output_stream_;
  file_stream_.close();
}

bool ProtobufStreamFileWriter::append(::google::protobuf::MessageLite &message) {
  if (!valid_) {
    return false;
  }
  coded_output_stream_->WriteLittleEndian32((uint32_t)message.ByteSizeLong());

  if (coded_output_stream_->HadError()) {
    valid_ = false;
    return false;
  }

  valid_ = message.SerializeToCodedStream(coded_output_stream_);
  return valid_;
}
bool ProtobufStreamFileWriter::append(const std::string &msg) {
  if (!valid_) {
    return false;
  }
  coded_output_stream_->WriteLittleEndian32((uint32_t)msg.size());

  if (coded_output_stream_->HadError()) {
    valid_ = false;
    return valid_;
  }
  coded_output_stream_->WriteString(msg);
  if (coded_output_stream_->HadError()) {
    valid_ = false;
  }
  return valid_;
}

bool ProtobufStreamFileWriter::is_valid() { return valid_; }

void ProtobufStreamFileReader::resize_buffer_if_needed(uint32_t size) {
  if (size <= buffer_size_) {
    return;
  }
  delete[] buffer_;
  buffer_size_ = size;
  buffer_ = new uint8_t[buffer_size_];
}

bool read_little_endian_32(FILE *stream, uint32_t *out) {
  uint8_t buf[4];
  bool ret = fread(&buf, 4, 1, stream) == 1;
  *out = (buf[0] << 0) | (buf[1] << 8) | (buf[2] << 16) | (buf[3] << 24);
  return ret;
}

bool read_little_endian_64(FILE *stream, uint64_t *out) {
  uint8_t buf[8];
  bool ret = fread(&buf, 8, 1, stream) == 1;
  *out = ((uint64_t)buf[0] << 0) | ((uint64_t)buf[1] << 8) | ((uint64_t)buf[2] << 16) | ((uint64_t)buf[3] << 24) |
         ((uint64_t)buf[4] << 32) | ((uint64_t)buf[5] << 40) | ((uint64_t)buf[6] << 48) | ((uint64_t)buf[7] << 56);
  return ret;
}

ProtobufStreamFileReader::ProtobufStreamFileReader(std::string type_name, std::string filepath)
    : valid_(true), done_(false), filepath_(filepath), stream_(fopen64(filepath.c_str(), "rb")), buffer_size_(64),
      buffer_(new uint8_t[buffer_size_]) {
  uint64_t magic_number;

  valid_ = read_little_endian_64(stream_, &magic_number);

  if (!valid_) {
    spdlog::error("Proto Stream File Reader Failed to read Magic number");
    return;
  }

  valid_ = magic_number == CARBON_PROTO_FILE_STREAM_MAGIC;
  if (!valid_) {
    spdlog::error("Proto Stream File Reader Magic Number is incorrect, found: ", magic_number,
                  " expected: ", CARBON_PROTO_FILE_STREAM_MAGIC);
    return;
  }

  uint32_t size;

  valid_ = read_little_endian_32(stream_, &size);

  if (!valid_) {
    spdlog::error("Proto Stream File Reader Failed to read header size");
    return;
  }

  resize_buffer_if_needed(size + 1);

  valid_ = fread(buffer_, 1, size, stream_) == size;
  buffer_[size] = '\0';
  type_name_ = std::string((char *)buffer_);

  valid_ = valid_ && type_name_ == type_name;

  if (!valid_) {
    spdlog::error("Proto Stream File Reader Failed to read header or header does not match: og size: {:x}", size);
  }
}

ProtobufStreamFileReader::~ProtobufStreamFileReader() {
  delete[] buffer_;
  fclose(stream_);
}

bool ProtobufStreamFileReader::_previous() {
  if (!valid_) {
    spdlog::error("Proto Stream File Reader Not Valid, Can't read previous");
    return false;
  }

  if (is_at_start()) {
    return false;
  }

  if (previous_pointers_.size() >= 2) {
    previous_pointers_.pop();
  }

  auto pos = previous_pointers_.top();

  valid_ = fsetpos64(stream_, &pos) == 0;

  if (!valid_) {
    spdlog::info("Proto Stream File Reader Failed to go to previous position.");
    return false;
  }

  previous_pointers_.pop();
  return true;
}
bool ProtobufStreamFileReader::previous(::google::protobuf::MessageLite *message) {
  if (!_previous()) {
    return false;
  }
  return next(message);
}
std::tuple<bool, std::string> ProtobufStreamFileReader::previous() {
  if (!_previous()) {
    return std::make_tuple<bool, std::string>(false, "");
  }
  return next();
}

bool ProtobufStreamFileReader::_current() {
  if (!valid_) {
    spdlog::error("Proto Stream File Reader Not Valid, Can't read current");
    return false;
  }

  if (previous_pointers_.size() == 0) {
    return true;
  }

  auto pos = previous_pointers_.top();

  valid_ = fsetpos64(stream_, &pos) == 0;

  if (!valid_) {
    spdlog::info("Proto Stream File Reader Failed to go to current position.");
    return false;
  }

  previous_pointers_.pop();
  return true;
}
bool ProtobufStreamFileReader::current(::google::protobuf::MessageLite *message) {
  if (!_current()) {
    return false;
  }
  return next(message);
}
std::tuple<bool, std::string> ProtobufStreamFileReader::current() {
  if (!_current()) {
    return std::make_tuple<bool, std::string>(false, "");
  }
  return next();
}

std::tuple<bool, uint32_t> ProtobufStreamFileReader::_next() {
  if (!valid_) {
    spdlog::error("Proto Stream File Reader Not Valid, Can't read next");
    return std::make_tuple<bool, uint32_t>(false, 0);
  }

  if (is_done()) {
    spdlog::error("Proto Stream File Reader at end, can't read next");
    return std::make_tuple<bool, uint32_t>(false, 0);
  }

  fpos64_t pos;
  valid_ = fgetpos64(stream_, &pos) == 0;

  if (!valid_) {
    spdlog::info("Proto Stream File Reader Failed to read current file position.");
    return std::make_tuple<bool, uint32_t>(false, 0);
  }

  previous_pointers_.push(pos);

  uint32_t size;
  valid_ = read_little_endian_32(stream_, &size);

  if (!valid_) {
    if (feof(stream_) != 0) {
      valid_ = true;
      previous_pointers_.pop();
      return std::make_tuple<bool, uint32_t>(false, 0);
    } else {
      spdlog::error("Proto Stream File Reader Failed to read next size");
      return std::make_tuple<bool, uint32_t>(false, 0);
    }
  }

  resize_buffer_if_needed(size);

  valid_ = fread(buffer_, 1, size, stream_) == size;

  if (!valid_) {
    spdlog::error("Proto Stream File Reader Failed to read message from file");
    return std::make_tuple<bool, uint32_t>(false, 0);
  }
  return std::make_tuple<bool, uint32_t>(true, std::forward<uint32_t>(size));
}
bool ProtobufStreamFileReader::next(::google::protobuf::MessageLite *message) {
  auto [valid, size] = _next();
  if (!valid) {
    return false;
  }
  valid_ = message->ParseFromArray(buffer_, size);

  if (!valid_) {
    spdlog::error("Proto Stream File Reader Failed to parse message");
    return false;
  }

  return valid_;
}
std::tuple<bool, std::string> ProtobufStreamFileReader::next() {
  auto [valid, size] = _next();
  (void)size;
  if (!valid) {
    return std::make_tuple<bool, std::string>(false, "");
  }
  return std::make_tuple<bool, std::string>(true, std::string((char *)buffer_, static_cast<size_t>(size)));
}

bool ProtobufStreamFileReader::is_valid() { return valid_; }

bool ProtobufStreamFileReader::is_done() { return feof(stream_) != 0; }

bool ProtobufStreamFileReader::is_at_start() { return previous_pointers_.size() <= 1; }

} // namespace recorder
} // namespace carbon
CompileProto(geo_service.proto GENERATED_PATH GOPKG proto/geo_service LANGS python mypy grpc_python go go-grpc cpp grpc)

add_library(geo_service_proto SHARED ${GENERATED_PATH}/geo_service.grpc.pb.cc ${GENERATED_PATH}/geo_service.pb.cc)
add_dependencies(geo_service_proto generate_geo_service_proto_geo_service)
target_compile_options(geo_service_proto PRIVATE "-w")
target_link_libraries(geo_service_proto PUBLIC grpc++ protobuf)

import asyncio

import grpc
from pyproj import Geod

import generated.geo_service.proto.geo_service_pb2 as gspb
import generated.geo_service.proto.geo_service_pb2_grpc as gs_grpc
import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)
DEFAULT_PORT = 64656


class GrpcServicer(gs_grpc.GeoServiceServicer):
    def __init__(self) -> None:
        self._geod = Geod(ellps="WGS84")

    async def GeodFwd(self, req: gspb.GeodFwdRequest, context: grpc.ServicerContext) -> gspb.GeodFwdResponse:
        long, lat, _ = self._geod.fwd(req.start.lng, req.start.lat, req.azimuth_deg, req.dist_meters)
        context.set_code(grpc.StatusCode.OK)
        return gspb.GeodFwdResponse(pos=gspb.GeoLL(lat=lat, lng=long))


class GrpcServer:
    def __init__(self, port: int = DEFAULT_PORT):
        self._port = port
        self._server = grpc.aio.server()
        self._servicer = GrpcServicer()
        gs_grpc.add_GeoServiceServicer_to_server(self._servicer, self._server)
        self._server.add_insecure_port(f"[::]:{self._port}")

    async def run(self, stop_event: asyncio.Event) -> None:
        await self._server.start()
        LOG.info(f"Started Geo service at 0.0.0.0:{self._port}")
        await stop_event.wait()
        await self._server.stop()
        await self._server.wait_for_termination()

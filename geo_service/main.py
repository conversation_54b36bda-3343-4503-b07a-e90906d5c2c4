import asyncio
import signal
from typing import Any

from geo_service.grpc import GrpcServer
from lib.common.logging import get_logger, init_log

LOG = get_logger(__name__)


async def main() -> None:
    stop_event = asyncio.Event()

    def _exit(signum: int, frame: Any) -> None:
        stop_event.set()

    signal.signal(signal.SIGINT, _exit)
    signal.signal(signal.SIGTERM, _exit)

    server = GrpcServer()
    LOG.info("server created")
    await server.run(stop_event)


if __name__ == "__main__":
    init_log(level="INFO")
    asyncio.run(main())

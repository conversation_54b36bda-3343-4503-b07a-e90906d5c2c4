syntax = "proto3";

package carbon.portal.model_history;
option go_package = "proto/portal";

import "portal/proto/util.proto";

enum ModelEventType {
    UNKNOWN = 0;
    ROBOT_START = 1;
    PINNED = 2;
    UNPINNED = 3;
    RECOMMENDED = 4;
    ACTIVATED = 5;
    NICKNAME_CHANGE = 6;
    NICKNAME_DELETE = 7;
    DEFAULT_PARAMETER_CHANGE = 8;
    PARAMETER_CHANGE = 9;
}

message ModelEvent {
    ModelEventType type = 1;
    string model_id = 2;
    string model_nickname = 3;
    string model_parameters = 4;
    string model_type = 5;
    string crop_id = 6;
    string job_id = 7;
    int64 timestamp_ms = 8;
}

message UploadModelEventsRequest {
    string robot = 1;
    repeated ModelEvent events = 2;
}

service ModelHistorySyncService {
    rpc UploadModelEvents(UploadModelEventsRequest) returns (carbon.portal.util.Empty);
}

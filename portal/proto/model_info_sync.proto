syntax = "proto3";

package carbon.portal.model_info;
option go_package = "proto/portal";

import "portal/proto/util.proto";

message ModelInfo {
    string model_id = 1;
    repeated string crop_ids = 2;
    string nickname = 3;
}

message UploadModelInfosRequest {
    string robot = 1;
    repeated ModelInfo model_infos = 2;
}

message RenameModelCommand {
    string model_id = 1;
    string new_nickname = 2;
}

message GetRenameModelCommandsRequest {
    string robot = 1;
}

message GetRenameModelCommandsResponse {
    repeated RenameModelCommand commands = 1; 
}

message PurgeRenameModelCommandsRequest {
    string robot = 1;
    repeated RenameModelCommand commands = 2;
}

service ModelInfoSyncService {
    rpc UploadModelInfos(UploadModelInfosRequest) returns (carbon.portal.util.Empty);
    rpc GetRenameModelCommands(GetRenameModelCommandsRequest) returns (GetRenameModelCommandsResponse);
    rpc PurgeRenameModelCommands(PurgeRenameModelCommandsRequest) returns (carbon.portal.util.Empty);
}
syntax = "proto3";

package carbon.portal.profile_sync;
option go_package = "proto/portal";

import "portal/proto/util.proto";
import "frontend/proto/profile_sync.proto";
import "proto/almanac/almanac.proto";
import "proto/thinning/thinning.proto";
import "frontend/proto/banding.proto";
import "proto/target_velocity_estimator/target_velocity_estimator.proto";

message GetProfilesDataRequest {
    string robot_name = 1;
}

message GetProfilesDataResponse {
    map<string, frontend.profile_sync.ProfileSyncData> profiles = 1;
}

message UploadProfileRequest {
    int64 last_update_time_ms = 1;
    string robot_name = 2;
    oneof profile {
        carbon.aimbot.almanac.AlmanacConfig almanac = 3;
        carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
        carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
        carbon.frontend.banding.BandingDef banding = 6;
        carbon.thinning.ConfigDefinition thinning = 7;
        carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 8;
    }
}

message GetProfileRequest {
    string uuid = 1;
}

message GetProfileResponse {
    oneof profile {
        carbon.aimbot.almanac.AlmanacConfig almanac = 1;
        carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
        carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
        carbon.frontend.banding.BandingDef banding = 4;
        carbon.thinning.ConfigDefinition thinning = 5;
        carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 6;
    }
}

message DeleteProfileRequest {
    string uuid = 1;
}

message PurgeProfileRequest {
    string uuid = 1;
}

message SetActiveProfileCommand {
    carbon.frontend.profile_sync.ProfileType profile_type = 1;
    string uuid = 2;
}

message GetSetActiveProfileCommandsRequest {
    string robot = 1;
}

message GetSetActiveProfileCommandsResponse {
    repeated SetActiveProfileCommand commands = 1;
}

message PurgeSetActiveProfileCommandsRequest {
    string robot = 1;
    repeated SetActiveProfileCommand commands = 2;
}

service PortalProfileSyncService {
    rpc GetProfilesData(GetProfilesDataRequest) returns (GetProfilesDataResponse);
    rpc UploadProfile(UploadProfileRequest) returns (carbon.portal.util.Empty);
    rpc GetProfile(GetProfileRequest) returns (GetProfileResponse);
    rpc DeleteProfile(DeleteProfileRequest) returns (carbon.portal.util.Empty);
    rpc PurgeProfile(PurgeProfileRequest) returns (carbon.portal.util.Empty);
    rpc GetSetActiveProfileCommands(GetSetActiveProfileCommandsRequest) returns (GetSetActiveProfileCommandsResponse);
    rpc PurgeSetActiveProfileCommands(PurgeSetActiveProfileCommandsRequest) returns (carbon.portal.util.Empty);
}

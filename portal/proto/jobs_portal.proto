syntax = "proto3";

package carbon.portal.jobs;
option go_package = "proto/portal";

import "portal/proto/util.proto";
import "frontend/proto/jobs.proto";
import "frontend/proto/weeding_diagnostics.proto";
import "metrics/proto/metrics_aggregator_service.proto";

message UploadJobRequest {
    carbon.frontend.jobs.Job job = 1;
    string robot = 2;
}

message UploadJobConfigDumpRequest {
    string jobId = 1;
    carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 2;
}

message UploadJobMetricsRequest {
    string jobId = 1;
    metrics_aggregator.Metrics jobMetrics = 2;
}

service PortalJobsService {
    rpc UploadJob(UploadJobRequest) returns (carbon.portal.util.Empty);
    rpc UploadJobConfigDump(UploadJobConfigDumpRequest) returns (carbon.portal.util.Empty);
    rpc UploadJobMetrics(UploadJobMetricsRequest) returns (carbon.portal.util.Empty);
}
syntax = "proto3";

package carbon.portal.farm;
option go_package = "proto/portal";

import "proto/geo/geo.proto";
import "google/protobuf/timestamp.proto";

message Farm {
  carbon.geo.Id id = 1;
  VersionInfo version = 2;

  string name = 3;
  int64 customer_id = 4;

  repeated PointDefinition point_defs = 5;
  repeated Zone zones = 6;
}

message VersionInfo {
  // Sequence number for this version. Monotonically increasing.
  int64 ordinal = 1;

  // Update time for this version.
  google.protobuf.Timestamp update_time = 2;

  // Whether this entity is intended to be deleted in the latest version. The
  // entity is kept around as a tombstone so that we can tell that future syncs
  // aren't intending to recreate it.
  bool deleted = 3;

  // Input-only field: whether this entity is intended to be changed in a given
  // patch request. If this is false, then no edits to this entity's fields
  // will be incorporated into the merge, though edits to its descendants may
  // still be incorporated.
  bool changed = 4;
}

message PointDefinition {
  carbon.geo.Point point = 1;
  VersionInfo version = 2;
}

message Zone {
  carbon.geo.Id id = 1;
  VersionInfo version = 2;

  // Human-readable name for this zone.
  string name = 3;

  // The physical areas that this zone includes.
  repeated Area areas = 4;

  ZoneContents contents = 5;
}

// This is a full message because `protoc-gen-go` generates bad code for
// `oneof`s: https://github.com/golang/protobuf/issues/1326
message ZoneContents {
  oneof data {
    FarmBoundaryData farm_boundary = 9;
    FieldData field = 5;
    HeadlandData headland = 6;
    PrivateRoadData private_road = 7;
    ObstacleData obstacle = 8;
  }
}

// An area is a contiguous region of space. It comprises all points within
// `buffer_meters` of the `geometry`. For example, a `line_string` in the shape
// of a circle describes an annulus, whereas a `polygon` in the same shape
// describes a disk.
message Area {
  double buffer_meters = 1;
  oneof geometry {
    carbon.geo.Point point = 2;
    carbon.geo.LineString line_string = 3;
    carbon.geo.Polygon polygon = 4;
  }
}

// A farm boundary denotes the maximal extent of the farm. Under no
// circumstance should autonomous vehicles exist this boundary.
//
// A farm should have at most one zone of this type.
message FarmBoundaryData {}

message FieldData {
  // Heading along which crops are planted. Used for determining a default
  // direction of travel when plotting a GPS path through this field.
  PlantingHeading planting_heading = 1;
  // For circular fields that use center-pivot irrigation, this message
  // describes the geometry of the center pivot. See:
  // https://en.wikipedia.org/wiki/Center-pivot_irrigation
  CenterPivot center_pivot = 2;
}

message HeadlandData {}

message PrivateRoadData {}

message ObstacleData {}

// A planting heading can either be specified as an A-B line of recorded
// points, or just a fixed numeric heading.
message PlantingHeading {
  oneof heading {
    // Degrees clockwise from north in the middle of the field.
    double azimuth_degrees = 1;
    // A vector between two points along the centerline of a single row.
    carbon.geo.AbLine ab_line = 2;
  }
}

message CenterPivot {
  // The center point of a center pivot.
  carbon.geo.Point center = 1;
  // The width of the arm of the center pivot.
  double width_meters = 2;
  // Radius of the center pivot, from the center point to the farthest point on
  // the physical arm.
  double length_meters = 3;
}

message CreateFarmRequest { Farm farm = 1; }

message UpdateFarmRequest {
  // Patch updates to the farm. Descendant `VersionInfo`s must retain the
  // `ordinal` of the last copy that this client received from the server, for
  // merging purposes. To delete an entity, flip `deleted` from `false` to
  // `true`. To update an entity, set `changed` to `true` and edit the fields
  // on the entity.
  Farm farm = 1;
}

message ListFarmsRequest {
  // Cursor from `ListFarmsResponse.next_page_token` on a previous response.
  // If blank, starts from the beginning.
  string page_token = 1;
}

message ListFarmsResponse {
  repeated Farm farms = 1;
  // Cursor for `ListFarmsRequest.page_token`. If blank, there are no further
  // pages.
  string next_page_token = 2;
}

message GetFarmRequest {
  // ID of the farm to get.
  carbon.geo.Id id = 1;
  // If specified, this is a conditional request; see `FarmsService.GetFarm`.
  google.protobuf.Timestamp if_modified_since = 2;
}

message GetFarmResponse { Farm farm = 1; }

service FarmsService {
  // Gets a farm by ID. If `if_modified_since` is present and none of the
  // farm's descendant entities has been modified at a strictly later time than
  // that, then the RPC will succeed with code OK and no `farm` on the
  // response.
  rpc GetFarm(GetFarmRequest) returns (GetFarmResponse);
}
syntax = "proto3";

package carbon.portal.spatial_metrics;
option go_package = "proto/portal";

import "proto/metrics/metrics.proto";
import "portal/proto/util.proto";

message SyncSpatialMetricBlocksRequest {
    repeated carbon.metrics.SpatialMetricBlock blocks = 1;
    string robot = 2;
}

service SpatialMetricsSyncService {
    rpc SyncSpatialMetricBlocks(SyncSpatialMetricBlocksRequest) returns (carbon.portal.util.Empty);
}
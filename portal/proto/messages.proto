syntax = "proto3";

package carbon.portal.messages;
option go_package = "proto/portal";

import "portal/proto/db.proto";

message Message {
  carbon.portal.db.DB db = 1;
  string message = 2;
  string author_user_id = 3;
  int64 author_robot_id = 4;
  string recipient_user_id = 5;
  int64 recipient_customer_id = 6;
  int64 recipient_robot_id = 7;
}

message MessagesResponse {
  int64 page = 2;
  int64 limit = 3;
  repeated Message messages = 4;
}
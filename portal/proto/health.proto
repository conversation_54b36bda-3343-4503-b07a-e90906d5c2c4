syntax = "proto3";

package carbon.portal.health;
option go_package = "proto/portal";

import "google/protobuf/struct.proto";
import "portal/proto/util.proto";
import "frontend/proto/alarm.proto";
import "frontend/proto/laser.proto";
import "frontend/proto/status_bar.proto";
import "proto/metrics/metrics.proto";

// ########### START DEPRECATED ALARMS
enum AlarmLevel {
  ALARM_UNKNOWN = 0 [ deprecated = true ];
  ALARM_CRITICAL = 1 [ deprecated = true ];
  ALARM_HIGH = 2 [ deprecated = true ];
  ALARM_MEDIUM = 3 [ deprecated = true ];
  ALARM_LOW = 4 [ deprecated = true ];
  ALARM_HIDDEN = 5 [ deprecated = true ];
}

enum AlarmImpact {
  IMPACT_UNDEFINED = 0 [ deprecated = true ];
  IMPACT_CRITICAL = 1 [ deprecated = true ];
  IMPACT_OFFLINE = 2 [ deprecated = true ];
  IMPACT_DEGRADED = 3 [ deprecated = true ];
  IMPACT_NONE = 4 [ deprecated = true ];
}

message AlarmRow {
  option deprecated = true;
  int64 timestamp_ms = 1;
  string alarm_code = 2;
  string description = 3;
  AlarmLevel level = 4;
  string identifier = 5;
  bool acknowledged = 6;
  AlarmImpact impact = 7;
}
// ########### END DEPRECATED ALARMS

message Location {
  double x = 1;
  double y = 2;
  double z = 3;

}

message FieldConfig {
  bool banding_enabled = 1;
  bool banding_dynamic = 2;
  string active_band_config = 3;
  string active_thinning_config_id = 4;
  string active_job_id = 5;
  string active_almanac_id = 6;
  string active_discriminator_id = 7;
  bool is_weeding = 8;
  bool is_thinning = 9;
  string active_band_config_name = 10;
  string active_thinning_config_name = 11;
  string active_job_name = 12;
  string active_almanac_name = 13;
  string active_discriminator_name = 14;
  string active_modelinator_id = 15;
  string active_velocity_estimator_id = 16;
  string active_velocity_estimator_name = 17;
  string active_category_collection_id = 18;
}

message Versions {
  string current = 1;
  string latest = 2;
}

message WeedingPerformance {
  double area_weeded_total = 1;
  double area_weeded_today = 2;
  int64 time_weeded_today = 3;
}

message Performance {
  WeedingPerformance weeding = 1;
}

message DailyMetrics {
  map<string, string> metrics = 1;
}
message Metrics {
  map<string, DailyMetrics> daily_metrics = 1;
}

message HealthLog {
  repeated AlarmRow alarms = 1 [ deprecated = true ];
  Location location = 2;
  string model = 3;
  repeated string models = 4;
  Performance performance = 5;
  int64 reported_at = 6;
  string robot_serial = 7;
  repeated .google.protobuf.Struct systems = 8;
  carbon.frontend.status_bar.Status status = 9;
  int64 status_changed_at = 10;
  string crop = 11 [ deprecated = true ]; // DEPRECATED: use crop_id
  string p2p = 12;
  string software_version = 13;
  string target_version = 14;
  bool target_version_ready = 15;
  string status_message = 16;
  map<string, uint64> metric_totals = 17;
  repeated carbon.frontend.alarm.AlarmRow alarm_list = 18;
  FieldConfig field_config = 19;
  Metrics metrics = 20;
  string crop_id = 21;
  uint32 robot_runtime_240v = 22;
  carbon.frontend.laser.LaserStateList laser_state = 23;
  carbon.metrics.LaserChangeTimes laser_change_times = 24;
  map<string, string> host_serials = 25;
  map<string, bool> feature_flags = 26;
  carbon.frontend.status_bar.TranslatedStatusMessage translated_status_message = 27;
}

message IssueReport {
  string description = 1;
  string phone_number = 2;
  string robot_serial = 3;
  int64 reported_at = 4;
  string crop = 5 [ deprecated = true ]; // Deprecated, use crop_id
  string model_id = 6;
  string software_version = 7;
  string crop_id = 8;
}

service HealthService {
  rpc LogHealth(HealthLog) returns (carbon.portal.util.Empty);
  rpc ReportIssue(IssueReport) returns (carbon.portal.util.Empty);
}

syntax = "proto3";

package carbon.portal.reaper;
option go_package = "proto/portal";

import "frontend/proto/module.proto";

message ReaperConfiguration {
  repeated frontend.module.ModuleIdentity assigned_modules = 1;
  frontend.module.RobotDefinition current_robot_definition = 2;
}

message UploadReaperConfigurationRequest {
  ReaperConfiguration configuration = 1;
}

message UploadReaperConfigurationResponse {}

service ReaperConfigurationService {
  rpc UploadReaperConfiguration(UploadReaperConfigurationRequest)
      returns (UploadReaperConfigurationResponse);
}

# WebSockets

**WebSocket** is a computer communications protocol, providing full-duplex communication channels over a single TCP connection.

WebSocket is distinct from HTTP. Both protocols are located at layer 7 in the OSI model and depend on TCP at layer 4. Although they are different, RFC 6455 states that WebSocket "is designed to work over HTTP ports 80 and 443 as well as to support HTTP proxies and intermediaries," thus making it compatible with the HTTP protocol. To achieve compatibility, the WebSocket handshake uses the HTTP Upgrade header to change from the HTTP protocol to the WebSocket protocol.

## Further Reading
* https://en.wikipedia.org/wiki/WebSocket
* https://blogs.windows.com/windowsdeveloper/2016/03/14/when-to-use-a-http-call-instead-of-a-websocket-or-http-2-0/

## Advantages
* Full duplex
* Server push
* Single TCP connection
* Low Latency (vs HTTP)
* Stateful
* No intermediate caching

## Disadvantages
* Additional WebSocket handshake response required to upgrade and maintain connection
* No intermediary/edge caching
* Additional control loop per connection
* Stateful (yes, both advantage and disadvantage)
* Single TCP connection (con if speed is more important than guaranteed message delivery)

## When should you use a websocket?
WebSockets provide significant advantages for the following use cases:
* Asynchronous client/server communication
* Sending data from client to server as fast as possible
* Pushing data from the server to the client
* Applications requiring fast reaction time
* Ongoing updates
* High-Frequency messaging with small payloads
* No structure imposed on the data, akin to OS sockets

## When should you not use a websocket?
It is better to use HTTP for the following use cases:
* Retrieve resource without ongoing updates
* Highly cacheable resource
  * Static content such as web page layouts
  * Retrieving/publishing large resources such as archive files
* Video streaming
* Peer-to-peer communication

## You might be using HTTP wrong if
* Your design relies on a client polling the service often, without the user taking action.
* Your design requires frequent service calls to send small messages.
* The client needs to quickly react to a change to a resource, and it cannot predict when the change will occur.

## You might be using Websockets wrong if
* The connection is used only for a very small number of events, or a very small amount of time, and the client does not need to quickly react to the events.
* Your feature requires multiple WebSockets to be open to the same service at once.
* Your feature opens a WebSocket, sends messages, then closes it—then repeats the process later.
* You’re re-implementing a request/response pattern within the messaging layer.

## Performance Comparisons
The most noteworthy performance comparison I've found on the internet is below:
* https://blog.feathersjs.com/http-vs-websockets-a-performance-comparison-da2533f13a77

## Example 1 (status poll):
Status polling benefits from
* Ongoing updates
* Pushing data from server to client
* Client cannot predict when data will change

Using HTTP polling, on a Macbook Air, the HTTP server is overwhelmed at around 15+ requests/sec, so we are forced to throttle on the clientside.

Using WebSockets, we can push status updates from the server at > 50 request/sec.

## Example 2 (log poll):
Log polling benefits from:
* Ongoing updates
* Pushing data from server to client
* Client cannot predict when data will change

This example is the same as status polling. We also get the advantage of only pushing messages from the server when there are new logs, as opposed to eagerly HTTP polling for logs repeatedly even if they aren't there.

## Example 3 (Manual Driving)
Manual Driving benefits from:
* Sending data from client to server as fast as possible
* Realtime applications requiring fast reaction time
* Ongoing updates
* High-Frequency messaging with small payloads

HTTP polling is limited to around 15 requests/sec, but with WebSockets we are able to send gamepad requests to the server at up to 60 requests/sec on a Macbook Air

# Maka Design

## Server
The server can specify a producer callback to push messages to the client, and/or a map from message type to consumer callback.

Here are a few examples:

### Status
```
> WebSocket(name="status", produce=lamdba _: bot.status_callback())
```

### Drive
```
> WebSocket(name="drive", consumer={"drive": driver.handle_drive_request_json})
```

## Client (JavaScript)

On the client, just open the connection. You can pass a parameters dictionary in the `hello` message. For instance, `freq_hz` will control the frequency at which the robot websocket server pushes messages (e.g. status, logs).
### Status
```
new MakaWebSocket({
    name: "status",
    hello: {"freq_hz": "50"}
})
```
### Logs
```
new MakaWebSocket({
    name: "logs",
    hello: {"freq_hz": "50"}
})
```
### Drive
```
driveWebSocket = new MakaWebSocket({
    name: "drive"
})
driveWebSocket.send({
    forward: 1,
    steer: 0,
    source: "example",
    timestamp_ms: new Date.now()
})
```

# Shoot N' Scoot

The goal of this document is to layout where we are today, and the next steps towards implementing our first Robot-level routine: Shoot N' Scoot.

It is not meant to be overly specific, as these ideas will evolve during implementation.

This document will not be kept up to date long-term. It is a historical snapshot of where we thought we were going.

## Overview
Shoot N' Scoot is a `Robot`-level routine that alternates between a "shoot" operation performed by the `Exterminator` and a "scoot" operation performed by the `Driver`.

It is the first such routine that combines driving and shooting.

## How Drive / Exterminator Components Work Today
### Driver

Today, the `Driver` runs as an `async` control loop.

```
async def _drive_loop(self):
    while True:
        check if cancelled
   
        // drive
        do drive loop based on current drive mode
   
        // throttle
        await asyncio.sleep(...)
```

#### Driver Mode
The drive loop will check which `mode` the `Driver` is in:
* `NONE` - do nothing
* `FOLLOW` - target in place
* `ATTACK` - drive towards target

### Exterminator

Today, the `Exterminator` works via synchronous invocations.

```
def exterminate(self, shoot=SHOOT_PARTITION):
    // do extermination
    ...
    return
```

#### Exterminator Strategy
There are a number of different exterminator strategies available:
* SHOOT_PARTITION
* SHOOT_BLIND_PARTITION
* SHOOT_MANUAL_PARTITION
* SHOOT_MANUAL_PANO_PARTITION
* SHOOT_EXPERIMENT

It is expected that more strategies will continue to be added, and existing strategies will become more parameterized

## Shoot N' Scoot Design

### Robot Control Loop (or job)
* Introduce top-level control loop at Robot level
  * Will this merge cleanly with existing operational idioms?
    * Need exterminator <--> UI locking. Who is in charge?
    * Actuator locking / checkout design
  * Anything robot level must be responsive and yield to scheduler frequently
* Maybe start with a job that gets scheduled, and build it out into a long-lived control loop.
  * We don't want much compute on this loop. It should be a coordination layer that dispatches and coordinates work by `Driver` vs `Exterminator`.

### Robot Operational Modes
Introduce a state machine for different operation modes:
* Weeding
* Calibration
* Data Capture
* Inter-field travel
* Refueling

#### Weeding Mode
In weeding mode, we weed!

Different Strategies:
* Shoot N' Scoot
  * different shoot strategies
  * different scoot parameterized behavior (time based, Maka box length)
* Crawl (really slow)
  * Thorough, Slow
  * As fast as we can while still getting every weed
* Aggressive
  * Fast, Aggressive
  * as fast as we can drive and shoot accurately, even if we miss some weeds

Smarter strategies will build on or alongside these.

### How Exterminator signals "shoot" completion
The simple way is that the `exterminate()` function returns.

#### New Driver Mode: SCOOT
* `SCOOT` - Scoot forward. Signal that we have scooted.
  * Idea 1: It stays in this mode the whole time. Waiting for signal to scoot again, then repeating earlier logic.
  * Idea 2: When the scoot is complete, the `Driver` puts itself back into `NONE` mode. To make it scoot again, set its mode to `SCOOT`. Essentially entering `SCOOT` mode is equivalent to doing a single scoot.

### How Driver signals "scoot" completion
* `Event` object(s)?
  * start event
  * completed event
* Message Bus
  * What does our general message template look like? (Can be very similar to WebSocket message structure)
    * id
    * timestamp
    * sender
    * msg_type
    * data
  * How do we subscribe/filter messages?
  * How is the Message Bus initialized? Is it a function module? Something inited by the bootloader? Does it live in the namespace at `/bus` (or some derivative thereof)?
  * Does this fit in with our `DataLogger` or is it an entirely separate stream structure?

Similarly, how does the `Driver` know when to scoot again? Does it stay in `SCOOT` mode and wait on a signal, or switch into `NONE` mode and then the `Robot`-level logic switches it back into `SCOOT`.

### How Driver knows when "scoot" complete
v1) Clock-based: enter ATTACK mode for a parameterized length of time.

v2) Inertial-based: Hard code a real-world length in vehicle frame of reference. Listen to velocity / position to go exactly target distance.
  * v2.1) Raw Duro / IMU values (integrated/derived, as needed)
  * v2.2) Statistical Filtering (Kalman Filter, Particle Filter, etc.)

v3) Intertial/Camera Sensor Fusion: Integrate optical flow to improve accuracy. New calibration steps for px/py (predict space) -> vx/vy/vz (vehicle space)?


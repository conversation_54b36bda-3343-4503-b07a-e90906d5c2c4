# 1 Overview
This document is the first attempt at organizing the ideas that will be critical to self-driving.

# 2 Immediate Goals
The immediate goals are to demonstrate the following field capabilities, in order:
1. **Follow row without trampling onions**
   * Row Following
1. **Shoot N Scoot a single row**
   * Distance Traveled Estimation
   * Driver/Exterminator coordination
1. **Turn around at end of row**
   * Turn Around Procedure
1. **Autonomously drive consecutive furrows with turn around**
   * Commander transitions
   * De-couple from shooting due to time constraints

# 3 Raw Sensor Data
The raw sensor information for self-driving will come from:
* INS (pose) (i.e. Duro Inertial)
* Drive Camera (front-facing camera) (i.e. Axis)
* Predict Camera (downward-facing camera) (i.e. <PERSON><PERSON>)

## 3.1 Duro Inertial
The Duro Inertial has the following accuracies in SBAS positioning mode (no differential RTK) as we have it now:
* 75 cm position
* 60 mm/s velocity
* 15 degree heading (empirical)
   * Need to better understand impact of motion on heading accuracy
      * differential GPS over time will improve estimates
   * Data sheet claims 0.8 degree heading at 1+ m/s in RTK positioning mode

The Duro Inertial performs better under motion. This allows it to take advantage of differential GPS. Faster motion is actually better. You can confirm this empirically looking at our data logs.

Compared with other commercially available INS at a similar price point, these values are very good. A small improvement in these numbers results in doubling or more in price.

## 3.2 Axis Camera
The AXIS P3915-R Mk II Network Camera has the following key features:
* IP67
* 1080p
* 25 fps
* pan / tilt / zoom
* PoE

## 3.3 Basler Cameras
We are testing multiple different predict cameras of different resolution and frame rates. The primary guide here is small weed detection.

Minimally, these offer 1080p at up to 30fps, which is expected to be sufficient for our initial optical flow implementation.

# 4 Pose Estimator
## 4.1 Robot Pose API
The robot **pose** comprises its location and orientation relative to a global coordinate frame.

A rigid, motionless robot is described by six state variables. 3 positional variables in their Cartesian coordinates, and three variables for angular orientation (pitch, roll, yaw).

Motion is therefore described by six corresponding velocity variables, one for each pose variable. We also know our 3-axis acceleration.

For rigid mobile robots confined to planar environments, the pose is usually given by three variables, its two location coordinates in the plane, and its heading direction. This simplification is something we will take advantage of in our driver goal algorithms, although we may still use 3D ECEF coordinates.

### 4.1.1 Quaternions > Euler Angles
Spatial rotations in three dimensions can be parametrized using both euler angles and unit quaternions. 

Quaternions should be used for all calculations, since euler angles suffer from *gimbal lock*. That said, our robot will be confined to a mostly planar environment and gimbal lock should be all but impossible if the robot is to survive.

For humans, including annotated data, familiar values for pitch, roll, and yaw are preferred. Quaternions can be displayed alongside as appropriate.

## 4.2 Pose Estimation
We will run **Pose Estimation** as a single dedicated module. We will (have already stubbed) introduce a `PoseEstimator` `Node` which is available under the `/pose` namespace. This Pose Estimator will manage the execution loops for statistical filtering of our pose estimation.

Initially, this will be primarily supported by the raw sensor stream of the Duro. We do not expect to benefit from filtering without fusion, as the Duro Inertial already does its own filtering.

### 4.2.1 Pose API
It will initially be expressed in the same style as our existing sensors. You can get a reference to the `/pose` at boot.

This `/pose` node will offer APIs for:
* `heading()`
* `position()`
* `velocity()`
* `quaternion()` or `euler_angles()`

These APIs provide the latest pose estimations with highest certainty.

## 4.3 Distance Traveled Estimation
For Shoot N Scoot, we must move a precise distance, then stop. We will build the driver controls to be conservative so that we prefer scooting too little as opposed to too far. This heuristic preference will be parameterized explicitly.

### 4.3.1 Differential Pose
One way of computing distance traveled (in vehicle space) is to consider differential poses. The positional difference between the estimated pose at two points in time is a first-order estimate of the distance traveled between those points in time. 

Simple subtraction between sample poses should suffice initially. And we can look to filter, smooth, or PID the estimation as appropriate. 

### 4.3.2 Optical Flow
The other primary methodology we should immediately consider is running **optical flow** on predict camera frames. This provides an estimate of distance traveled in pixel space.

### 4.3.3 Sensor Fusion
The above techniques and more can be combined using linear weighting or more complex ensembles.

## 4.4 Pose Accuracy Roadmap
### 4.4.1 Computer Vision
Going forward, we will integrate computer vision information to improve our pose estimation. We can use optical flow to better improve estimates around distance traveled, assuming a translation function is available for vehicle to predict space. We can use furrow detection to better judge our heading, especially relative to a target.

### 4.4.2 Inertial Data
We can also look at getting multiple cheaper GNSS devices and doing differential GNSS for better heading. As we build out our algorithms and consider more sensor combinations, we can likely find a solutions with more favorable cost to performance ratios. It is unlikely our current configuration is ideal.

### 4.4.3 Wheel Encoders
At 1km/h, a 34.2in wheel revolves just 0.1018 times per second. A high resolution wheel encoder will offer much greater accuracy than the 60 mm/s precision of the Duro Inertial. We are able to use a high resolution encoder because we are moving slowly.

# 5 Furrow Detection
The primary goal of the drive camera is to help safety and driving. Today, it is mounted, angled, and calibrated to detect furrows as best as possible.

## 5.1 Furrow Features
The primary features of a furrow are that it is deeper than surrounding bedtop and it is evenly spaced in rows.

## 5.2 Neural Network
We will leverage our existing Deep Learning infrastructure. That means we will start with a CNN based on DeepLabV3 to detect furrows. For Owyhee field, the neural network should detect furrows as lines with the same vanishing point. The vanishing point may be off the top edge of the camera.

For this initial problem, we are okay with overfitting to the Owyhee field.

### 5.2.1 v1: Unity Simulator
To start, we can get known furrows from the Unity Simulator.
* Get furrows from simulator
* Find central furrow
* Use cv2 fit line

### 5.2.2 v2: Neural Network
* Get predictions from DL
  * Can run predictions on Unity camera stream
* magic magic magic
* Central line in same format as cv2

Magic magic magic pending exploration and understanding. We are still looking at different labeling techniques, camera angles, loss functions, etc.

Focused discussion at [#648](https://github.com/carbonrobotics/robot/issues/648)

## 5.3 Errors
A couple of different pose errors can be determined from the detected furrows in the drive camera.

### 5.3.1 Angle Of Error From Furrow
One way to detect the error is to make the line between the horizon point made from all furrows converging and the estimated pixel location for the bottom of the front right drive wheel. The angle of this line from vertical is the error in vehicle heading. If that line is pointed exactly vertical through the vanishing point, then the vehicle is driving directly down the furrow.

### 5.3.2 Offset From Furrow Center
The offset from the furrow center is defined as the offset from the drive wheel pixel and the nearest furrow pixel. Effectively the x difference.

## 5.4 Depth Sensing
The existing mono camera solution is fundamentally limited in depth detection. Since depth is a key feature of furrows, we are limited in what we can achieve with a single mono camera.

To address this, we will explore stereo cameras or LIDAR to get raw depth information. We can feed this information into the neural network or apply classical computer vision directly on the new outputs.

# 6 Probabilistic Robotics
The core idea in probabilistic robotics is the idea of estimating state from sensor data.

This is a central design principle that we will emulate. Everything is an estimation.

We will employ probabilistic state estimation algorithms to compute belief distributions over possible world states.
 * Kalman Filters
 * Particle Filters
 * Masks with Confidence Intervals
 * etc.

Our control algorithms will be built to consume a probabilistic view of the world and make decisions based on that range of beliefs.

## 6.1 Probabilistic Representation
Wherever possible, sensor and control data should be expressed as a belief distribution over a range of possible states.

In many cases, there will be an underlying Gaussian assumption, which can be represented as a pair of values: mean and standard deviation.

This will naturally align with the output of Kalman Filters, Deep Learning models, etc.

## 6.2 Anomaly Detection
This concept extends to anomaly detection and the difficult problem of detecting a faulty sensor.

For instance, if the deep learning server gives us garbage, which it will due from time to time, we need a statistical model to evaluate outputs against. Having multiple different confidence intervals from different sources will be critical input data to developing fault-tolerant sensor and control algorithms.

# 7 Visual Cortex
We will run computer vision as a single dedicated module managing the image stream and computer vision operations (primarily GPU-based).

We will introduce a `VisualCortex` `Node` which is available under the `/visual_cortex` namespace. This Visual Cortex will manage the execution context for computer vision operations.

We will start by building the Visual Cortex to interact only with the Driver. Over time, all synchronous `huli` calls / `cv2.aruco` library calls will be ported to run under the Visual Cortex in a common style.

## 7.1 Vision Output Stream
The Visual Cortex will stream outputs asynchronously, in a common style that matches the Pose Estimator. Small outputs can be streamed over the message bus while larger outputs like PNG masks can be communicated via shared memory primitives.

Outputs will be consumed by clients such as the Driver / Exterminator as `(timestamp, camera_id, vision_output)` where `vision_output` here is a generic, placeholder concept for vision outputs like fiducials, sticks, furrows, weeds, etc.

TODO: what term to call this?

## 7.2 Analogy with Pose Estimator
The Retina / Visual Cortex modules will form a similar relationship to the Frame / Pose modules.

The Visual Cortex is analgous to the Pose Estimator. It consumes raw sensor data and outputs refined data.

Interacting with the data streams from either the Visual Cortex or Pose Estimator should feel similar to clients.

## 7.2.1 Interaction with Retina

In the same way that the Frame provides raw sensor data in a common reference frame (rotated, etc.), the Retina also provides standardized raw camera data (rotated, color corrected).

Then, in the same way that the Pose Estimator runs statistical filters on the Frame's raw inertial stream, the Visual Cortex runs computer vision on the Retina's raw image stream.

Actuators such as the Driver and Exterminator will primarily depend on the Pose / Visual Cortex directly. They should not need raw data from the Frame or Retina, though that option is not withheld. It will take time to unwind the Exterminator's dependency on raw camera images, particularly during calibration. 

## 7.3 Computer Vision Operations and Outputs
The Visual Cortex will manage all computer vision operations that exist today, including:
* Fiducial Detection
* Class Expression Mask Detection
  * cable
  * grass
  * onions
  * sticks
  * weeds

New operations will be explored for driving:
* New Mask Classes
   * Furrow
* Optical Flow
* Line / Contour Outputs
   * Furrow Lines
   * Furrow Vanishing Point

TODO: Define subscribe/publish API in detail

### 7.3.1 Client Subscription Example
Consider the Exterminator. Different shooting plans require different vision information.

If a shooting plan requires the weed mask (minus onions, cables) and weed centroid, then it would subscribe to both of those outputs.

If a shooting plan instead only targets a single point, then the Exterminator could unsubscribe from the mask output stream and rely only on the weed centroid output stream.

All of the computer vision operations are kept in the Visual Cortex, and consumers only subscribe to the output streams they require.

## 7.4 Vision Loops
We will leverage the Node execution loop architecture to build out the processor module for vision.

We will build vision loops similarly to the pose loop(s). They will consume raw images, execute computer vision operations, and produce a stream of vision outputs for Driver, Exterminator, etc.

### 7.4.1 Initial Drive Vision Processor
We will initially build out the Visual Cortex as a single loop for just the single drive camera.

A simple start is to model a simple prediction type state machine of NONE, FIDUCIALS, STICKS, FURROW. This relies on a reasonable initial simplification that only a single type of prediction is active at once.

We will use this as an example to define and build the generic vision API for clients which bridges all CV outputs including fiducials, masks, lines, and optical flow.

### 7.4.2 Future Vision Processors
We will learn from this experience, and then expand to consolidate vision operations for predict/target camera under the Visual Cortex as well.

As we refactor Exterminator into an asynchronous module that shoots while moving, it will be desirable to de-couple our vision stream in the same way we are de-coupling our pose stream.

# 8 Drive Plan
A drive plan is expressed in terms of goals.

## 8.1 How it works today
Today, those goals are fairly abstract:
* Align with a fiducial
* Target a fiducial
* Align with two colored sticks
* Turn 90 degrees left/right
* Drive along a heading vector

Most of these goals are expressed as a computation on sensor data which determines the estimated error angle relative to the vehicle's current heading.

In fact, these goals broadly fit into a few categories:
* Target a particular heading while maintaining current position
  * e.g. Fiducial align
  * e.g. Turn left/right
* Target a particular heading while driving forward indefinitely
  * e.g. Targeting a fiducial
  * e.g. Attacking sticks
  * e.g. Targeting current heading
* A procedure maneuver
  * e.g. Sticks Align Procedure
  
The first two can generally be described as Pose Goals. The Robot aims to achieve a certain pose (while obeying a bunch of constraints along the way).

The last one is more similar to a planned procedure maneuver where you pre-plan a set of drive instructions and follow it steadfast until a change of plans. It is possible to re-express this as a sequence of goal poses, but it's not clear if that's really useful. Maybe as a translation layer, but certainly the concept of a "procedure maneuver" should exist without being fixed to a global reference frame.

## 8.2 Pose Goal
A **Pose Goal** is a goal in which the Driver aims to achieve a specific pose. Generally, this will be some reduction of the full pose variable. For instance, the robot may target a `(latitude, longitude, heading)` triplet or using ECEF, `(x, y, z, heading)`.

More complex pose goals could aim to achieve a certain velocity or angular turn rate.

### 8.2.1 Coordinate Systems
It will be useful to be able to target goal poses expressed in multiple (but not too many) coordinate systems.

For instance, the robot should be able to target a particular lat/lon or a particular ECEF point.

Humans will generally enter data as lat/lons, and the robot will translate as needed. Those two systems should be enough for now.

### 8.2.2 Translations
We should build a translation layer for latitude/longitude data to be expressed in ECEF. This should allow the robot to mostly think in terms of ECEF, and only deal with lat/lon when interacting with user or developer-facing data, where lat/lon convenience triumphs over linearity.

## 8.3 Multiple Plans in Parallel
We will enhance the Driver / Drive Plan to be able to run different goal strategies in parallel, and then combine them before doing actuation.

## 8.4 Dry-Run Plans
We will also build dry-run capabilities for easier comparative analysis between techniques.

## 8.5 Pose/Vision Data Streams
We will generalize the hard-coded tick vs front-right image concepts in the plan handlers to a generic stream of data we can use per control cycle.

### 8.5.1 How it works today
Today there are separate function handles for separate inertial / vision events:
* `handle_tick()`
* `handle_image_front_right()`

In each case, we generally process the last received event. Since there is no Visual Cortex yet, there is unwanted callback patterns managed by the DrivePlan to run the deep learning operations.

As we expand cameras and handle more data streams, we must generalize and de-couple this architecture.

### 8.5.2 A single stream of subscribed data
Modeling the Pose and Visual Cortex provides two like-modeled publishers for consumption.

Rather than always doing an "inertial tick" and then always consuming the "front right image" on every control cycle, we will drain our subscriptions using the message bus infrastructure.

These messages tell the Driver all of the new pose data and vision data available since the last control cycle. This could be a lot of data if the sensor streams are noisy or the control cycle is slow. It could be no new data if we are missing sensors or running blind. We must be robust to both cases.

All data will be keyed off millisecond timestamp as an `int`.

# 9 Driver Control Loop
The driver control loop will be simplified to consolidate around an active drive plan. This is how certain drive modes work today, but not all. We will port fiducial targeting to the drive plan architecture as it is useful in the office.

Once per control cycle, we will consume messages. These messages will primarily be operations about a plan: create new plan, cancel current plan, modify existing plan. There may be other types of high level messages, such as to change underlying settings or switch PID parameters. We don't need to force all Driver messages through the plan concept.

Roughly,
```
while True:
  // check if cancelled

  // handle manual drive request (priority message)

  // drain messages (create/modify/cancel plan, update (hyper)parameters, etc.)

  // if not manually driven:
  //   if active plan:
  //     plan.tick()
  //   else:
  //     new plan

  // sleep

```

TODO: Block diagram

## 9.1 Short-Term Planning
The general idea is to have short-term plan actively managed by the Driver. The plan is meant to be "short", and the driver should only continue executing the plan if it continues to receive positive control messages (heartbeat or otherwise).

For now, we are mainly focused on "short-term" planning. In simple words, that means the driver will manage a plan to go straight, or turn, but not both. The longest plan will be "drive to the end of the furrow". When to turn around or when to drive to the next furrow will be managed by Commander.

# 10 Row Following
There are a few building blocks we can use for initial Row Following.

## 10.1 Heading
We have existing functionality to turn in-place to a desired heading or track a target heading under motion.

An in-place turn can be expressed as a pose goal `(heading, velocity=0)`. This is supported by our existing driver PIDs by calculating the relative error angle and evaluating the driver target PID.

We can extend this to support tracking a heading under motion by making the `velocity` component of the pose goal nonzero and evaluating the velocity PID.

## 10.2 GPS
A GPS goal can be represented as a pose goal `(latitude, longitude)`, or in ECEF, `(x, y, z)`. We don't need to worry about `heading` to start as we can assume we're facing the right direction for following a straight line.

A simple idea:
* Once per control cycle, determine your error angle relative to your position `(latitude, longitude)` or `(x,y,z)`.
* If using `ECEF`, map the 3D error angle to a 2D error angle by making a linear assumption (details?)
* If you are not at your target, evaluate the velocity PID.
* If you are very close to your target, evaluate the target PID to do the final heading alignment

## 10.3 Furrow Detection
Detecting and computing an error angle to a furrow gives us an error angle we can use to evaluate our PIDs. We know the position of the camera on the vehicle, so we know the camera's pose and velocity.

Using purely furrow detection, the tracking procedure becomes a recursive problem of continually minimizing your error angle relative to the furrow. Evaluating the velocity PID while doing this, will drive down the furrow going forward.

This information is computed in image space with no absolute correlation to vehicle position/velocity. For instance, we don't know the ECEF position of each drive pixel.

One method to start correlating pixels with inertial data is to link the information through heading. If we know our goal heading (e.g. manually specified to match furrow angle), then computing the error angle through furrow detection also yields a prediction about the vehicles heading, independent of our inertial sensors.

This will be a fundamental challenge that comes up repeatedly: how do we correlate vision information and inertial information?

### 10.3.1 Combining CV with Inertial

For instance, the robot could target a goal pose (e.g. heading or GPS), while in parallel detecting furrows and running vision alignment.

Instead of trying to correlate different sensor systems, we ensemble the multiple control outputs into a single control output.

A simple linear weighting between techniques will provide a foundation to build upon.

# 11 End of Row Turn Around
## 11.1 Procedure Turn
We can adapt the sticks align technique to furrow wheel alignment:
```
while not aligned with furrow:
  Check origin heading
  Turn orthogonal left/right
  Drive for set time
  Turn back to origin heading
  Check if aligned with furrow
```

This relies on detecting the furrows with the neural network, determining which furrow is next, angle of error computation, and zero point turn capabilities.

## 11.2 GPS Point by Point Turn Around
Another technique is to rely primarily on GPS and heading to guide the turn, and then possibly some help from computer vision to align at the end.

### 11.2.1 Smoothing
One algorithm that produces nice smoothness and is fairly easy to implement is quintic fitting.

Many ideas (and code) are available here: https://github.com/AtsushiSakai/PythonRobotics

These ideas are probably a bit more complicated than we are ready to take on today. We need to work on fitting a target trajectory before we tackle smoothing that target trajectory optimally.

# 12 Calibration
The calibration process will be generalized to extend across our major top-level nodes:
* Driver
* Exterminator
* Retina
* Pose
* Frame

## 12.1 Retina Calibration
A distinct calibration flow for the Retina will be introduced.

It will calibrate:
* Drive Camera Undistortion (new)
* Predict Camera Undistortion
    * to be moved from Exterminator Calibration
* Target Camera Undistortion
    * to be moved from Scanner Calibration
* Target Camera Color Correction
    * to be moved from manually modifying scanner calibration file

Any future innovations around raw image correction will be modeled under the Retina as well.

## 12.2 Drive Calibration
Building out this functionality will necessitate a well-modeled calibration state machine and user calibration wizard, similar to Exterminator/Retina calibration.

### 12.2.1 Calibration Requirements
* Magnetometer Sampling
* Location of wheel in undistorted drive camera space (out of view)
* Algorithm-layer PIDs (Target/Velocity)
* Hardware-layer PIDs (Lando board helac set point)

Future:
* Relationship between optical flow and inertial sensors
* Filter hyperparameters
* A lot more PIDs

### 12.2.2 State Machine
The main question is whether to use a linear state machine, like the Scanner Calibration Wizard, or a more graph-like model.

While the linear model is useful and lends itself to a nice user experience, the drive calibration sequence is totally new and unknown.

For now, we should model them as independent tuning steps that happen in any order. We can use an enum, but it won't be ordered.

### 12.2.3 Drive Calibration Sampling and Processing
We can use a similar calibration data flow as the Scanner Calibration Wizard.

The primary idea is to de-couple the independent operations and model them as a multi-producer, multi-consumer dependency chain. Raw data samplers pipe into data cleaners which pipe into correlation processors.

For instance, spawn inertial and vision data samplers, drive around to gather sample data, park, and compute correlations. There could be dozens of interdependent operations which fan-out and fan-in over a time period (e.g. 15 min).

Simple ideas to improve upon the Scanner calibration flow:
* Checkpointing long operations
* Progress bars
* Pause/Restart capabilities
* Anomaly detection and kickout of bad training data in small volume
* Direct integration of calibration report

#### ******** MakaTask Enhancements
We should introduce the notion of pause/restart to a MakaTask, as well as checkpointing and emitting progress.

These patterns will be re-used for other worker tasks on the robot, including backporting the concepts to Scanner calibration.

### 12.2.4 Drive Calibration Technician Experience
Drive calibration should be simple for the user.
1. Start
1. Click button to begin drive calibration sampling
   1. Drive around manually. Do figure eights.
   1. Click button to end drive calibration sampling.
   1. Wait for robot to compute necessary correlations.
1. Manually adjust PIDs (can we heavily rely on auto-tuning?)
1. Done

## 12.3 Pose Calibration
Pose calibration will primarily involve the tuning of hyperparameters for pose estimation algorithms.

Some examples include:
* Sensor noise profiles
* Filter hyperparameters

This is distinct from the Driver because other systems like the Exterminator or the Safety system will benefit.

### 12.3.1 Pose Calibration Wizard
We're not sure yet how we'll differentiate the user wizard for pose calibration, as it will likely benefit from the drive calibration procedure.

This suggests the need for a Robot-level Calibration Wizard that can manage the calibration data feeds better than a Node-specific calibration wizard.

## 12.4 Exterminator Calibration Changes
It is likely that some of the data collected during drive calibration will be useful to calibrate shooting-while-moving functions.

### 12.4.1 Shooting While Moving Calibration
Correlations between optical flow and the inertial sensors will be useful, so we can better track Exterminator targets under vehicle motion.

We should think further on how these data pipelines and state machines will connect.

For the Exterminator, these could represent additional linear calibration steps at the end, or a second subsequence that pairs with the existing linear sequence.

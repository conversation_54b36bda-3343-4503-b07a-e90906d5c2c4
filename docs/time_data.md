# Time Data
All time data at rest should be stored in UTC and aggregated/indexable by hour.

This provides robustness against hourly timezones and daylight savings time changes.

# Precision
Timestamps should almost always be stored at the max precision available.

Control logic is standardized on milliseconds since epoch as an int16.

# Format
Timestamps in text format should almost always be in ISO 8601 format. The suffix may be trimmed to reduce precision.

A specific exception is carved for filenames, in which case ":" is replaced with "-" for wider filesystem support.

# User Display
Timezone should be clearly marked in user-displayed data.

It is up to the application to determine which timezone to display. Field apps will want local timezone of the current geography. Some developer apps are better served in UTC. Wherever complexity can be managed, the option should be presented to the user.


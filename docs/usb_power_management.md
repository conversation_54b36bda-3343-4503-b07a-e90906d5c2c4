# USB power management

This is a living document to write down power management techniques for our USB cameras.

## xhci_hcd

`xhci_hcd` driver supports device re-binding which power cycles USB device attached.

### Usage

Find Basler USB cameras:
```
grep <PERSON>sler /sys/bus/pci/drivers/xhci_hcd/*/usb*/*/manufacturer | cut -d "/" -f 7
```

Will show:
```
0000:45:00.0
0000:46:00.0
0000:47:00.0
```

Power down camera:
```
echo 0000:46:00.0 | sudo tee /sys/bus/pci/drivers/xhci_hcd/unbind
```

Power up camera:
```
echo 0000:46:00.0 | sudo tee /sys/bus/pci/drivers/xhci_hcd/bind
```

## uhubctl

[uhubctl](https://github.com/mvp/uhubctl) is an open source tool that allows to power on-off specific USB ports.

### Installation

```
sudo apt-get install libusb-1.0-0-dev
git clone https://github.com/mvp/uhubctl
cd uhubctl
make install
```

### Usage

Find ports:
```
sudo uhubctl
```

Will show:
```
Current status for hub 14 [1d6b:0003 Linux 5.3.0-51-generic xhci-hcd xHCI Host Controller 0000:47:00.0, USB 3.00, 4 ports]
  Port 1: 02a0 power 5gbps Rx.Detect
  Port 2: 02a0 power 5gbps Rx.Detect
  Port 3: 02a0 power 5gbps Rx.Detect
  Port 4: 0203 power 5gbps U0 enable connect [2676:ba02 Basler acA4112-20uc 40044409]
Current status for hub 13 [1d6b:0002 Linux 5.3.0-51-generic xhci-hcd xHCI Host Controller 0000:47:00.0]
  Port 1: 0100 power
  Port 2: 0100 power
  Port 3: 0100 power
  Port 4: 0100 power
Current status for hub 12 [1d6b:0003 Linux 5.3.0-51-generic xhci-hcd xHCI Host Controller 0000:46:00.0, USB 3.00, 4 ports]
  Port 1: 02a0 power 5gbps Rx.Detect
  Port 2: 02a0 power 5gbps Rx.Detect
  Port 3: 02a0 power 5gbps Rx.Detect
  Port 4: 0203 power 5gbps U0 enable connect [2676:ba03 Basler daA1920-30uc 23167029]
Current status for hub 11 [1d6b:0002 Linux 5.3.0-51-generic xhci-hcd xHCI Host Controller 0000:46:00.0]
  Port 1: 0100 power
  Port 2: 0100 power
  Port 3: 0100 power
  Port 4: 0100 power
  ...
```

Write down hub #s and port #s of Basler cameras. With Basler USB card, port number seems to always be 4.

Check that you got parameters right like this:
```
sudo uhubctl -e -p 4 -l 12
```

Will show:
```
Current status for hub 12 [1d6b:0003 Linux 5.3.0-51-generic xhci-hcd xHCI Host Controller 0000:46:00.0, USB 3.00, 4 ports]
  Port 4: 0203 power 5gbps U0 enable connect [2676:ba03 Basler daA1920-30uc 23167029]
```

**Turn off USB port:**
```
sudo uhubctl -e -p 4 -l 12 -a off
```

Will show:
```
Current status for hub 12 [1d6b:0003 Linux 5.3.0-51-generic xhci-hcd xHCI Host Controller 0000:46:00.0, USB 3.00, 4 ports]
  Port 4: 0203 power 5gbps U0 enable connect [2676:ba03 Basler daA1920-30uc 23167029]
Sent power off request
New status for hub 12 [1d6b:0003 Linux 5.3.0-51-generic xhci-hcd xHCI Host Controller 0000:46:00.0, USB 3.00, 4 ports]
  Port 4: 0080 off
```

**Turn on USB port:**
```
sudo uhubctl -e -p 4 -l 12 -a on
```

Will show:
```
Current status for hub 12 [1d6b:0003 Linux 5.3.0-51-generic xhci-hcd xHCI Host Controller 0000:46:00.0, USB 3.00, 4 ports]
  Port 4: 0080 off
Sent power on request
New status for hub 12 [1d6b:0003 Linux 5.3.0-51-generic xhci-hcd xHCI Host Controller 0000:46:00.0, USB 3.00, 4 ports]
  Port 4: 0280 power 5gbps SS.Disabled
```

**Note:** After power-cycling USB using this tool, `sudo uhubctl` doesn't show camera ID in its output anymore and opening camera takes longer time (but still works), so something gets a bit broken.

#pragma once

#include <c10/cuda/CUDAStream.h>
#include <glm/glm.hpp>
#include <torch/torch.h>

#include "config/tree/cpp/config_tree.hpp"
#include "cv/deepweed/output.h"
#include "cv/embeddings/classifier.h"
#include "cv/runtime/proto/cv_runtime.pb.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/model/cpp/atomic_model.h"

namespace F = torch::nn::functional;

namespace cv::deepweed {

using namespace lib::common;

struct EmbeddingPrediction {
  torch::Tensor embedding;
  torch::Tensor weed_score;
  torch::Tensor crop_score;

  size_t size(size_t dimension);
};

class DeepweedModel {
public:
  DeepweedModel(model::AtomicModel model);

  std::vector<std::string> get_supported_segmentation_categories();
  std::vector<std::string> get_supported_point_categories();

  const ModelMetadataProto &get_internal_metadata();
  const nlohmann::json &get_external_metadata();

  float get_discard_points_border_px();
  int get_max_batch_size();
  std::pair<int, int> get_input_shape();

  DeepweedOutput infer(torch::Tensor image, int64_t image_timestamp_ms, float image_ppi,
                       std::vector<runtime::proto::SegmentationDetectionCategory> segmentation_categories,
                       float weed_point_threshold, float crop_point_threshold, float plant_point_threshold,
                       bool return_embeddings, bool only_plant_points = false);
  DeepweedOutput infer(camera::CameraImage image,
                       std::vector<runtime::proto::SegmentationDetectionCategory> segmentation_categories,
                       float weed_point_threshold, float crop_point_threshold, float plant_point_threshold,
                       bool return_embeddings, bool only_plant_points = false);

  // Returns a tensor with embeddings at the point specified for each downsample level
  std::vector<EmbeddingPrediction> get_embedding(torch::Tensor image_tensor, float x, float y);

  void set_classifier(std::shared_ptr<embeddings::Classifier> embeddings_classifier);
  bool set_crop_id(const std::string &crop_id);

private:
  torch::Tensor normalize(torch::Tensor image);
  static glm::vec2 adjust_size_ppi(glm::vec2 coord, float ppi, float new_ppi) { return coord / ppi * new_ppi; }

  model::AtomicModel model_;
  torch::Tensor means_;
  torch::Tensor stds_;
  glm::ivec2 input_size_;
  int batch_size_;
  glm::ivec2 tile_size_;

  std::map<std::string, int> weed_point_category_indices_;
  std::map<int, std::string> weed_point_index_categories_;
  std::map<std::string, int> segmentation_category_indices_;
  std::map<int, std::string> segmentation_index_categories_;
  int gpu_id_;
  std::vector<runtime::proto::SegmentationDetectionCategory> segmentation_categories_;

  std::shared_ptr<embeddings::Classifier> embeddings_classifier_;
  std::optional<std::string> crop_id_;
  torch::Tensor crop_index_tensor_;
};

} // namespace cv::deepweed

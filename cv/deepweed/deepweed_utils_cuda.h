#pragma once

#include <glm/glm.hpp>
#include <torch/torch.h>

namespace cv::deepweed {

void mark_point_mask_intersections(torch::Tensor to_be_removed, torch::Tensor batch_ind, torch::Tensor y_ind,
                                   torch::Tensor x_ind, torch::Tensor p_sizes, torch::Tensor protection_mask,
                                   torch::Tensor protection_radius, glm::ivec2 size);
} // namespace cv::deepweed

#pragma once

#include <future>
#include <spdlog/spdlog.h>
#include <vector>

#include "cv/deepweed/deepweed_utils_cuda.h"
#include "cv/deepweed/non_maximum_suppression.h"
#include "cv/deepweed/output.h"
#include "cv/embeddings/classifier.h"
#include "cv/runtime/proto/cv_runtime.pb.h"
#include "lib/common/cpp/tensor_util.h"

namespace cv::deepweed {

class DetectionIdGenerator {
public:
  DetectionIdGenerator() : id_(0) {}
  uint32_t get() {
    auto id = id_;
    id_ += 1;
    return id;
  }

private:
  uint32_t id_;
};

inline std::vector<std::vector<DeepweedDetection>> compute_deepweed_detections_v2(
    const DeepweedRawOutput &output, glm::ivec2 size, float weed_threshold, float crop_threshold, float plant_threshold,
    bool weed_enabled, bool crop_enabled, bool plant_enabled, std::vector<std::string> weed_point_categories,
    std::vector<runtime::proto::SegmentationDetectionCategory> segmentation_categories,
    std::map<std::string, int> segmentation_category_indices, std::map<int, std::string>, torch::Tensor protection_mask,
    float ppi, bool trained_embeddings, bool contains_pumap_head, std::shared_ptr<embeddings::Classifier> classifier) {
  if (output.point_hits.size() == 0) {
    return {};
  }

  // List of list of weeds per batch instance
  std::vector<std::vector<DeepweedDetection>> detections_per_instance((size_t)output.point_hits[0].size(0));
  for (size_t i = 0; i < (size_t)output.point_hits[0].size(0); i++) {
    detections_per_instance[i].reserve(1000);
  }

  torch::Tensor segmentation_radius;
  torch::Tensor segmentation_threshold;

  if (segmentation_categories.size() > 0) {
    segmentation_radius = torch::zeros({protection_mask.size(1)});
    segmentation_threshold = torch::ones({protection_mask.size(1), 1, 1});

    for (size_t i = 0; i < segmentation_categories.size(); i++) {
      int c = segmentation_category_indices.at(segmentation_categories[i].category());
      segmentation_radius[c] = segmentation_categories[i].safety_radius_in() * ppi;
      segmentation_threshold[c][0][0] = segmentation_categories[i].threshold();
    }

    segmentation_radius = segmentation_radius.to(protection_mask.device());
    segmentation_threshold = segmentation_threshold.to(protection_mask.device());

    protection_mask = protection_mask > segmentation_threshold;
  }

  // Loop over downsampling levels
  DetectionIdGenerator detection_id_generator;
  for (size_t ds = 0; ds < output.point_hits.size(); ds++) {
    if (output.point_hits[ds].dim() < 4) {
      continue;
    }
    auto ds_categories = output.point_categories[ds];

    torch::Tensor ds_reduced_scaled_embedding;
    if (!output.point_reduced_scaled_embeddings.empty() && trained_embeddings && contains_pumap_head) {
      ds_reduced_scaled_embedding = output.point_reduced_scaled_embeddings[ds];
    }

    std::vector<std::tuple<float, bool, runtime::proto::HitClass>> hit_classes = {
        {weed_threshold, weed_enabled, runtime::proto::HitClass::WEED},
        {crop_threshold, crop_enabled, runtime::proto::HitClass::CROP},
        {plant_threshold, plant_enabled, runtime::proto::HitClass::PLANT}};
    for (const auto &[threshold, enabled, hit_class] : hit_classes) {
      if (!enabled) {
        continue;
      }
      int c = static_cast<int>(hit_class);
      if (output.point_hits[ds].size(1) <= c) {
        continue;
      }

      auto weed_hits = output.point_hits[ds]
                           .slice(1, static_cast<int>(runtime::proto::HitClass::WEED),
                                  static_cast<int>(runtime::proto::HitClass::WEED) + 1)
                           .squeeze(1);
      auto crop_hits = output.point_hits[ds]
                           .slice(1, static_cast<int>(runtime::proto::HitClass::CROP),
                                  static_cast<int>(runtime::proto::HitClass::CROP) + 1)
                           .squeeze(1);
      auto plant_hits = output.point_hits[ds]
                            .slice(1, static_cast<int>(runtime::proto::HitClass::PLANT),
                                   static_cast<int>(runtime::proto::HitClass::PLANT) + 1)
                            .squeeze(1);

      auto class_hits = output.point_hits[ds].slice(1, c, c + 1).squeeze(1);
      auto class_offsets = output.point_offsets[ds].slice(1, c, c + 1).squeeze(1);
      auto class_sizes = output.point_sizes[ds].slice(1, c, c + 1).squeeze(1);

      const glm::vec2 eff_downsample =
          glm::vec2(size) / glm::vec2(output.point_hits[ds].size(-1), output.point_hits[ds].size(-2));
      const float mean_eff_downsample = (eff_downsample.x + eff_downsample.y) / 2.0f;

      std::vector<torch::Tensor> coords = torch::where(class_hits > threshold);

      auto coords_list = lib::common::tensor_vector_to_list(coords);
      torch::Tensor poffsets = class_offsets.index(coords_list);
      torch::Tensor ys =
          ((coords[1] + poffsets.index({"...", 0}) + 0.5f) * eff_downsample.y - 0.5f).to(torch::kFloat32);
      torch::Tensor xs =
          ((coords[2] + poffsets.index({"...", 1}) + 0.5f) * eff_downsample.x - 0.5f).to(torch::kFloat32);
      torch::Tensor hit_ys = coords[1].to(torch::kInt32);
      torch::Tensor hit_xs = coords[2].to(torch::kInt32);
      torch::Tensor psizes = (class_sizes.index(coords_list) * mean_eff_downsample).to(torch::kFloat32);
      torch::Tensor pscores = class_hits.index(coords_list).cpu().to(torch::kFloat32);
      torch::Tensor weed_scores = weed_hits.index(coords_list).cpu().to(torch::kFloat32);
      torch::Tensor crop_scores = crop_hits.index(coords_list).cpu().to(torch::kFloat32);
      torch::Tensor plant_scores;
      if (plant_hits.numel() > 0) {
        plant_scores = plant_hits.index(coords_list).cpu().to(torch::kFloat32);
      }
      torch::Tensor pcategories = ds_categories.permute({0, 2, 3, 1}).index(coords_list).cpu().to(torch::kFloat32);
      torch::Tensor p_reduced_scaled_embedding;
      if (ds_reduced_scaled_embedding.numel() > 0 && trained_embeddings && contains_pumap_head) {
        p_reduced_scaled_embedding = ds_reduced_scaled_embedding.permute({0, 2, 3, 1}).index(coords_list).cpu();
      }

      torch::Tensor embedding_category_distances;
      if (!output.point_embeddings.empty() && trained_embeddings && classifier) {
        torch::Tensor embeddings = output.point_embeddings[ds].permute({0, 2, 3, 1}).index(coords_list);
        embedding_category_distances =
            classifier->classify(embeddings, weed_scores.unsqueeze(-1), crop_scores.unsqueeze(-1)).cpu();
      }

      torch::Tensor batch_ind = coords[0].to(torch::kInt32);
      torch::Tensor mask_intersections;

      torch::Tensor ys_cpu = ys.cpu();
      torch::Tensor xs_cpu = xs.cpu();
      torch::Tensor hit_ys_cpu = hit_ys.cpu();
      torch::Tensor hit_xs_cpu = hit_xs.cpu();
      torch::Tensor psizes_cpu = psizes.cpu();
      torch::Tensor batch_ind_cpu = batch_ind.cpu();

      if (segmentation_categories.size() > 0) {
        mask_intersections = torch::zeros({xs.size(0), protection_mask.size(1)},
                                          torch::TensorOptions().dtype(torch::kBool).device(class_hits.device()));
        mark_point_mask_intersections(mask_intersections, batch_ind, ys, xs, psizes, protection_mask,
                                      segmentation_radius, size);
        mask_intersections = mask_intersections.cpu();
      }

      ys = ys_cpu;
      xs = xs_cpu;
      hit_ys = hit_ys_cpu;
      hit_xs = hit_xs_cpu;
      psizes = psizes_cpu;
      batch_ind = batch_ind_cpu;
      auto xs_ptr = xs.data_ptr<float>();
      auto ys_ptr = ys.data_ptr<float>();
      auto hit_ys_ptr = hit_ys.data_ptr<int32_t>();
      auto hit_xs_ptr = hit_xs.data_ptr<int32_t>();
      auto psizes_ptr = psizes.data_ptr<float>();
      auto pscores_ptr = pscores.data_ptr<float>();
      auto weed_scores_ptr = weed_scores.data_ptr<float>();
      auto crop_scores_ptr = crop_scores.data_ptr<float>();
      float *plant_scores_ptr = nullptr;
      if (plant_hits.numel() > 0) {
        plant_scores_ptr = plant_scores.data_ptr<float>();
      }

      for (int64_t i = 0; i < coords[0].size(0); i++) {
        float plant_score = 0.0f;
        if (plant_scores_ptr != nullptr) {
          plant_score = plant_scores_ptr[i];
        }
        DeepweedDetection detection(xs_ptr[i], ys_ptr[i], psizes_ptr[i], pscores_ptr[i], weed_scores_ptr[i],
                                    crop_scores_ptr[i], plant_score, hit_class, hit_ys_ptr[i], hit_xs_ptr[i], (int)ds,
                                    detection_id_generator.get());
        detection.weed_detection_class_scores = std::vector<float>(
            pcategories[i].data_ptr<float>(), pcategories[i].data_ptr<float>() + weed_point_categories.size());

        if (embedding_category_distances.numel() > 0) {
          detection.embedding_category_distances = std::vector<float>(
              embedding_category_distances[i].data_ptr<float>(),
              embedding_category_distances[i].data_ptr<float>() + embedding_category_distances.size(1));
        }

        if (segmentation_categories.size() > 0) {
          for (int64_t category = 0; category < mask_intersections.size(1); category++) {
            if (!mask_intersections[i].data_ptr<bool>()[category]) {
              continue;
            }
            detection.mask_intersections.push_back((int)category);
          }
        }

        if (trained_embeddings && contains_pumap_head) {
          if (p_reduced_scaled_embedding.numel() == 0) {
            throw maka_error(
                fmt::format("Deepweed model produced a 0 dimension embedding but was marked with trained_embeddings"));
          }
          for (int em = 0; em < static_cast<int>(p_reduced_scaled_embedding.size(1)); ++em) {
            detection.reduced_scaled_embedding.push_back(p_reduced_scaled_embedding[i].data_ptr<float>()[em]);
          }
        }

        detections_per_instance[(size_t)batch_ind.data_ptr<int>()[i]].push_back(detection);
      }
    }
  }

  return detections_per_instance;
}

inline std::string get_detection_clz(std::vector<std::pair<std::string, float>> detection_classes) {
  auto max_it = std::max_element(detection_classes.begin(), detection_classes.end(),
                                 [](const auto &first, const auto &second) { return first.second < second.second; });
  std::string category = "WEED";
  if (max_it != detection_classes.end() && max_it->second > 0.5f) {
    category = max_it->first;
  }
  return category;
}

} // namespace cv::deepweed

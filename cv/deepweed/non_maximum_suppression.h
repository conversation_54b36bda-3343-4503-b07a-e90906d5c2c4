#pragma once

#include <cmath>
#include <nanoflann.hpp>
#include <torch/torch.h>

#include "cv/deepweed/output.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/math.h"

namespace cv::deepweed {

inline float circle_area(float r) { return std::pow(lib::common::kPi * r, 2.f); }

inline float circles_intersect_area(float cx1, float cy1, float r1, float cx2, float cy2, float r2) {
  if (r2 > r1) {
    std::swap(cx1, cx2);
    std::swap(cy1, cy2);
    std::swap(r1, r2);
  }

  float distance = std::hypot(cx1 - cx2, cy1 - cy2);
  if (distance <= r1 - r2) {
    return circle_area(r2);
  } else if (r1 + r2 <= distance) {
    return 0.f;
  }

  float d1 = (std::pow(r1, 2.f) - std::pow(r2, 2.f) + std::pow(distance, 2.f)) / (2.f * distance);
  float d2 = distance - d1;

  return std::pow(r1, 2.f) * std::acos(d1 / r1) - d1 * std::sqrt(std::pow(r1, 2.f) - std::pow(d1, 2.f)) +
         std::pow(r2, 2.f) * std::acos(d2 / r2) - d2 * std::sqrt(std::pow(r2, 2.f) - std::pow(d2, 2.f));
}

// Even though the distance metric is L2 here. The actual distance returned is the squared L2 norm.
template <typename NumT = float, int DIM = 2, class Distance = nanoflann::metric_L2>
class KDTreeDeepweedAdaptor {
public:
  typedef size_t IndexType;
  typedef KDTreeDeepweedAdaptor<NumT, DIM, Distance> self_t;
  typedef typename Distance::template traits<NumT, self_t>::distance_t metric_t;
  typedef nanoflann::KDTreeSingleIndexAdaptor<metric_t, self_t, DIM, IndexType> index_t;

  KDTreeDeepweedAdaptor(const std::vector<DeepweedDetection> &detections, const size_t leaf_max_size = 10)
      : detections_(detections),
        index_(new index_t(2, *this, nanoflann::KDTreeSingleIndexAdaptorParams(leaf_max_size))) {
    index_->buildIndex();
  }

  inline void query(NumT query_point_x, NumT query_point_y, float radius,
                    std::vector<std::pair<IndexType, float>> &out_points) const {
    std::vector<NumT> query_point = {query_point_x, query_point_y};
    index_->radiusSearch(query_point.data(), radius, out_points, nanoflann::SearchParams());
  }

  inline size_t kdtree_get_point_count() const { return detections_.size(); }

  inline NumT kdtree_get_pt(const size_t idx, const size_t dim) const {
    if (dim == 0) {
      return detections_[idx].x;
    } else if (dim == 1) {
      return detections_[idx].y;
    } else {
      throw maka_error("unsupported");
    }
  }

  template <class BBOX>
  bool kdtree_get_bbox(BBOX &) const {
    return false;
  }

private:
  const std::vector<DeepweedDetection> &detections_;

  std::unique_ptr<index_t> index_;
};

inline std::vector<DeepweedDetection> non_maximum_suppression(const std::vector<DeepweedDetection> &detections,
                                                              float beam_radius_px) {
  const auto num_points = detections.size();
  std::vector<int> to_be_removed(detections.size());

  KDTreeDeepweedAdaptor points_kd(detections);

  std::vector<size_t> sorted_indices(num_points);
  std::iota(sorted_indices.begin(), sorted_indices.end(), 0);
  std::sort(sorted_indices.begin(), sorted_indices.end(),
            [&](size_t first, size_t second) { return detections[first].score > detections[second].score; });

  for (size_t p1 = 0; p1 < num_points; p1++) {
    const size_t p1_index = sorted_indices[p1];
    if (to_be_removed[p1_index] == 1) {
      continue;
    }

    // Not removing
    to_be_removed[p1_index] = -1;

    std::vector<std::pair<size_t, float>> query_points;
    // Distance metric is squared L2 norm
    points_kd.query(detections[p1_index].x, detections[p1_index].y, std::pow(2 * beam_radius_px, 2.f), query_points);

    for (size_t p2 = 0; p2 < query_points.size(); p2++) {
      const size_t p2_index = query_points[p2].first;
      if (to_be_removed[p2_index] == -1) {
        continue;
      }

      if (std::hypot(std::abs(detections[p1_index].x - detections[p2_index].x),
                     std::abs(detections[p1_index].y - detections[p2_index].y)) <= 45.0f &&
          detections[p1_index].hit_class == detections[p2_index].hit_class) {
        to_be_removed[p2_index] = 1;
      }
    }
  }

  std::vector<DeepweedDetection> output;
  for (size_t i = 0; i < num_points; i++) {
    if (to_be_removed[i] == 1) {
      continue;
    }
    output.push_back(detections[i]);
  }
  return output;
}

} // namespace cv::deepweed

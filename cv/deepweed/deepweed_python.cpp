#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <torch/extension.h>
#include <torch/torch.h>

#include "cv/deepweed/deepweed_model.h"
#include "cv/deepweed/deepweed_utils.h"
#include "cv/deepweed/non_maximum_suppression.h"

namespace py = pybind11;

namespace cv {
namespace deepweed {

py::array_t<uint8_t> get_numpy_array_from_deepweed_mask(deepweed::DeepweedOutput &buf, py::handle base) {
  std::vector<uint32_t> vec_shape;
  uint8_t *address;
  {
    py::gil_scoped_release release;
    vec_shape = std::vector<uint32_t>({buf.mask_channels, buf.mask_height, buf.mask_width});
    address = buf.mask.data();
  }
  return py::array_t<uint8_t>(vec_shape, address, base);
}

std::vector<std::vector<deepweed::DeepweedDetection>> compute_deepweed_detections_v2_python(
    const std::vector<torch::Tensor> &hits, const std::vector<torch::Tensor> &categories,
    const std::vector<torch::Tensor> &offsets, const std::vector<torch::Tensor> &sizes,
    const std::vector<torch::Tensor> &reduced_scaled_embeddings, std::tuple<int, int> size,
    std::vector<std::string> weed_point_categories, float weed_point_threshold, float crop_point_threshold,
    float plant_point_threshold, bool weed_enabled, bool crop_enabled, bool plant_enabled, bool trained_embeddings,
    bool contains_pumap_head) {
  std::vector<cv::runtime::proto::SegmentationDetectionCategory> segmentation_categories;
  DeepweedRawOutput output;
  output.point_hits = hits;
  output.point_categories = categories;
  output.point_offsets = offsets;
  output.point_sizes = sizes;
  output.point_reduced_scaled_embeddings = reduced_scaled_embeddings;
  return deepweed::compute_deepweed_detections_v2(
      output, glm::ivec2(std::get<1>(size), std::get<0>(size)), weed_point_threshold, crop_point_threshold,
      plant_point_threshold, weed_enabled, crop_enabled, plant_enabled, weed_point_categories, segmentation_categories,
      {}, {}, torch::Tensor(), 0, trained_embeddings, contains_pumap_head, nullptr);
}

deepweed::DeepweedOutput deepweed_infer(deepweed::DeepweedModel &model, torch::Tensor image, int64_t image_timestamp_ms,
                                        float image_ppi, float segmentation_threshold, float segmentation_radius_in,
                                        float weed_point_threshold, float crop_point_threshold,
                                        float plant_point_threshold, bool return_embeddings) {
  std::vector<cv::runtime::proto::SegmentationDetectionCategory> segmentation_categories;
  for (const auto &category : model.get_internal_metadata().segm_classes()) {
    cv::runtime::proto::SegmentationDetectionCategory segmentation_category;
    segmentation_category.set_category(category);
    segmentation_category.set_threshold(segmentation_threshold);
    segmentation_category.set_safety_radius_in(segmentation_radius_in);
    segmentation_categories.push_back(segmentation_category);
  }
  return model.infer(image, image_timestamp_ms, image_ppi, segmentation_categories, weed_point_threshold,
                     crop_point_threshold, plant_point_threshold, return_embeddings);
}

int get_hit_class(cv::deepweed::DeepweedDetection &detection) { return (int)detection.hit_class; }

void set_hit_class(deepweed::DeepweedDetection &detection, int hit_class) {
  detection.hit_class = (cv::runtime::proto::HitClass)hit_class;
}

void set_mask_intersections(deepweed::DeepweedDetection &detection, std::vector<int> mask_intersections_vector) {
  detection.mask_intersections = mask_intersections_vector;
}

void set_weed_detection_class_scores(deepweed::DeepweedDetection &detection,
                                     std::vector<float> weed_detection_class_scores_vector) {
  detection.weed_detection_class_scores = weed_detection_class_scores_vector;
}

PYBIND11_MODULE(deepweed_python, m) {
  py::module::import("lib.common.model.cpp.model_python");

  py::class_<deepweed::DeepweedDetection>(m, "DeepweedDetection")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def_readwrite("x", &deepweed::DeepweedDetection::x)
      .def_readwrite("y", &deepweed::DeepweedDetection::y)
      .def_readwrite("size", &deepweed::DeepweedDetection::size)
      .def_readwrite("score", &deepweed::DeepweedDetection::score)
      .def_readwrite("plant_score", &deepweed::DeepweedDetection::plant_score)
      .def_readwrite("weed_score", &deepweed::DeepweedDetection::weed_score)
      .def_readwrite("crop_score", &deepweed::DeepweedDetection::crop_score)
      .def_readwrite("embedding", &deepweed::DeepweedDetection::embedding)
      .def_readwrite("reduced_scaled_embedding", &deepweed::DeepweedDetection::reduced_scaled_embedding)
      .def_readwrite("hit_x", &deepweed::DeepweedDetection::hit_x)
      .def_readwrite("hit_y", &deepweed::DeepweedDetection::hit_y)
      .def("set_weed_detection_class_scores", &set_weed_detection_class_scores,
           py::call_guard<py::gil_scoped_release>())
      .def("set_mask_intersections", &set_mask_intersections, py::call_guard<py::gil_scoped_release>())
      .def("get_mask_intersections", &deepweed::DeepweedDetection::get_mask_intersections,
           py::arg("mask_channel_classes"), py::call_guard<py::gil_scoped_release>())
      .def("get_detection_classes", &deepweed::DeepweedDetection::get_detection_classes,
           py::call_guard<py::gil_scoped_release>())
      .def("fill_detection_classes", &deepweed::DeepweedDetection::fill_detection_classes,
           py::call_guard<py::gil_scoped_release>())
      .def("fill_weed_detection_classes", &deepweed::DeepweedDetection::fill_weed_detection_classes,
           py::call_guard<py::gil_scoped_release>())
      .def("get_hit_class", &get_hit_class, py::call_guard<py::gil_scoped_release>())
      .def("set_hit_class", &set_hit_class, py::call_guard<py::gil_scoped_release>());

  py::class_<deepweed::DeepweedOutput>(m, "DeepweedOutput")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def_readwrite("detections", &deepweed::DeepweedOutput::detections)
      .def_readwrite("timestamp_ms", &deepweed::DeepweedOutput::timestamp_ms)
      .def_readwrite("weed_detection_classes", &deepweed::DeepweedOutput::weed_detection_classes)
      .def_readwrite("mask_channel_classes", &deepweed::DeepweedOutput::mask_channel_classes)
      .def("get_mask_numpy_array", &get_numpy_array_from_deepweed_mask, py::arg("base"));

  m.def("non_maximum_suppression", &cv::deepweed::non_maximum_suppression, py::call_guard<py::gil_scoped_release>(),
        py::arg("points"), py::arg("beam_radius_px"));

  m.def("compute_deepweed_detections_v2", &compute_deepweed_detections_v2_python,
        py::call_guard<py::gil_scoped_release>(), py::arg("hits"), py::arg("categories"), py::arg("offsets"),
        py::arg("sizes"), py::arg("embeddings"), py::arg("size"), py::arg("weed_point_categories"),
        py::arg("weed_point_threshold"), py::arg("crop_point_threshold"), py::arg("plant_point_threshold"),
        py::arg("weed_enabled"), py::arg("crop_enabled"), py::arg("plant_enabled"), py::arg("trained_embeddings"),
        py::arg("contains_pumap_head"));

  py::class_<deepweed::DeepweedModel>(m, "DeepweedModel")
      .def(py::init<lib::common::model::AtomicModel>(), py::call_guard<py::gil_scoped_release>())
      .def("get_discard_points_border_px", &deepweed::DeepweedModel::get_discard_points_border_px,
           py::call_guard<py::gil_scoped_release>())
      .def("get_input_shape", &deepweed::DeepweedModel::get_input_shape, py::call_guard<py::gil_scoped_release>())
      .def("infer", &deepweed_infer, py::arg("image_tensor"), py::arg("image_timestamp_ms"), py::arg("image_ppi"),
           py::arg("segmentation_threshold"), py::arg("segmentation_radius_in"), py::arg("weed_point_threshold"),
           py::arg("crop_point_threshold"), py::arg("plant_point_threshold"), py::arg("return_embeddings"),
           py::call_guard<py::gil_scoped_release>())
      .def("set_crop_id", &deepweed::DeepweedModel::set_crop_id, py::arg("crop_id"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_embedding", &deepweed::DeepweedModel::get_embedding, py::arg("image_tensor"), py::arg("x"),
           py::arg("y"), py::call_guard<py::gil_scoped_release>());

  py::class_<deepweed::EmbeddingPrediction>(m, "EmbeddingPrediction")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def_readwrite("embedding", &deepweed::EmbeddingPrediction::embedding)
      .def_readwrite("weed_score", &deepweed::EmbeddingPrediction::weed_score)
      .def_readwrite("crop_score", &deepweed::EmbeddingPrediction::crop_score);
}

} // namespace deepweed
} // namespace cv

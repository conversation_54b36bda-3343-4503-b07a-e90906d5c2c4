from typing import Any, List, <PERSON>ple

import numpy.typing as npt
import torch

from generated.cv.runtime.proto.cv_runtime_pb2 import HitClassValue
from lib.common.model.cpp.model_python import AtomicModel

class DeepweedDetection:
    x: float
    y: float
    size: float
    score: float
    weed_score: float
    crop_score: float
    plant_score: float
    embedding: torch.Tensor
    reduced_scaled_embedding: List[float]
    hit_y: int
    hit_x: int
    def set_mask_intersections(self, mask_intersection_vector: List[int]) -> None: ...
    def set_weed_detection_class_scores(self, detection_class_scores: List[float]) -> None: ...
    def get_mask_intersections(self, mask_channel_classes: List[str]) -> List[str]: ...
    def get_detection_classes(self, weed_detection_classes: List[str]) -> List[Tuple[str, float]]: ...
    def fill_detection_classes(
        self, detection_class_pairs: List[Tuple[str, float]], weed_detection_classes: List[str]
    ) -> None: ...
    def get_hit_class(self) -> HitClassValue: ...
    def set_hit_class(self, hit_class: int) -> None: ...

class DeepweedOutput:
    detections: List[DeepweedDetection]
    weed_detection_classes: List[str]
    mask_channel_classes: List[str]
    timestamp_ms: int
    def get_mask_numpy_array(self, base: DeepweedOutput) -> npt.NDArray[Any]: ...

class EmbeddingPrediction:
    def __init__(self) -> None: ...
    embedding: torch.Tensor
    weed_score: torch.Tensor
    crop_score: torch.Tensor

class DeepweedModel:
    def __init__(self, model: AtomicModel) -> None: ...
    def infer(
        self,
        image_tensor: torch.Tensor,
        image_timestamp_ms: int,
        image_ppi: float,
        segmentation_threshold: float,
        segmentation_radius_in: float,
        weed_point_threshold: float,
        crop_point_threshold: float,
        plant_point_threshold: float,
        return_embeddings: bool,
    ) -> DeepweedOutput: ...
    def set_crop_id(self, crop_id: str) -> bool: ...
    def get_embedding(self, image_tensor: torch.Tensor, x: float, y: float) -> List[EmbeddingPrediction]: ...
    def get_discard_points_border_px(self) -> float: ...
    def get_input_shape(self) -> Tuple[int, int]: ...

def non_maximum_suppression(detections: List[DeepweedDetection], beam_radius_px: float) -> List[DeepweedDetection]: ...
def compute_deepweed_detections_v2(
    hits: List[torch.Tensor],
    categories: List[torch.Tensor],
    offsets: List[torch.Tensor],
    sizes: List[torch.Tensor],
    embeddings: List[torch.Tensor],
    size: Tuple[int, int],
    weed_point_categories: List[str],
    weed_point_threshold: float,
    crop_point_threshold: float,
    plant_point_threshold: float,
    weed_enabled: bool,
    crop_enabled: bool,
    plant_enabled: bool,
    trained_embeddings: bool,
    contains_pumap_head: bool,
) -> List[List[DeepweedDetection]]: ...

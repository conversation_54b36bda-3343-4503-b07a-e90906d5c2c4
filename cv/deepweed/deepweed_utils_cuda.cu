#include "deepweed_utils_cuda.h"

#include <c10/cuda/CUDAStream.h>
#include <glm/glm.hpp>

#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/exceptions.h"

namespace cv::deepweed {

__global__ void mark_point_mask_intersections_kernel(
    torch::PackedTensorAccessor32<bool, 2, torch::RestrictPtrTraits> to_be_removed,
    torch::PackedTensorAccessor32<int, 1, torch::RestrictPtrTraits> batch_ind,
    torch::PackedTensorAccessor32<float, 1, torch::RestrictPtrTraits> y_ind,
    torch::PackedTensorAccessor32<float, 1, torch::RestrictPtrTraits> x_ind,
    torch::PackedTensorAccessor32<float, 1, torch::RestrictPtrTraits> p_sizes,
    torch::PackedTensorAccessor32<bool, 4, torch::RestrictPtrTraits> protection_mask,
    torch::PackedTensorAccessor32<float, 1, torch::RestrictPtrTraits> protection_radius, glm::ivec2 input_size) {
  const int x = blockIdx.x * blockDim.x + threadIdx.x;
  const int y = blockIdx.y * blockDim.y + threadIdx.y;
  const int z = blockIdx.z * blockDim.z + threadIdx.z;

  if (x < 0 || x >= protection_mask.size(3) || y < 0 || y >= protection_mask.size(2) || z < 0 ||
      z >= protection_mask.size(0)) {
    return;
  }

  const int x_point = lroundf(input_size.x * ((float)x / protection_mask.size(3)));
  const int y_point = lroundf(input_size.y * ((float)y / protection_mask.size(2)));

  // Loop over mask categories
  for (int c = 0; c < protection_mask.size(1); c++) {
    if (!protection_mask[z][c][y][x]) {
      continue;
    }

    const float radius = protection_radius[c];

    // Loop over point detections
    for (int i = 0; i < batch_ind.size(0); i++) {
      if (batch_ind[i] != z) {
        continue;
      }
      const float px = x_ind[i];
      const float py = y_ind[i];
      const int x_dist = (x_point - px);
      const int x_2 = x_dist * x_dist;
      const int y_dist = (y_point - py);
      const int y_2 = y_dist * y_dist;
      const float distance = sqrtf(x_2 + y_2);

      if (distance > radius) {
        continue;
      }

      to_be_removed[i][c] = true;
    }
  }
}

void mark_point_mask_intersections(torch::Tensor to_be_removed, torch::Tensor batch_ind, torch::Tensor y_ind,
                                   torch::Tensor x_ind, torch::Tensor p_sizes, torch::Tensor protection_mask,
                                   torch::Tensor protection_radius, glm::ivec2 input_size) {
  if (batch_ind.size(0) != to_be_removed.size(0) || //
      batch_ind.size(0) != y_ind.size(0) ||         //
      batch_ind.size(0) != x_ind.size(0) ||         //
      batch_ind.size(0) != p_sizes.size(0)) {
    throw maka_error("Indices tensors sizes must match");
  }

  if (batch_ind.size(0) == 0) {
    return;
  }

  const dim3 threads_per_block(8, 8);
  const dim3 blocks_per_grid((protection_mask.size(3) + threads_per_block.x - 1) / threads_per_block.x,
                             (protection_mask.size(2) + threads_per_block.y - 1) / threads_per_block.y,
                             (protection_mask.size(0) + threads_per_block.z - 1) / threads_per_block.z);

  with_device device_guard(protection_mask.device().index());
  auto torch_stream = c10::cuda::getCurrentCUDAStream();
  mark_point_mask_intersections_kernel<<<blocks_per_grid, threads_per_block, 0, torch_stream.stream()>>>(
      to_be_removed.packed_accessor32<bool, 2, torch::RestrictPtrTraits>(),
      batch_ind.packed_accessor32<int, 1, torch::RestrictPtrTraits>(),
      y_ind.packed_accessor32<float, 1, torch::RestrictPtrTraits>(),
      x_ind.packed_accessor32<float, 1, torch::RestrictPtrTraits>(),
      p_sizes.packed_accessor32<float, 1, torch::RestrictPtrTraits>(),
      protection_mask.packed_accessor32<bool, 4, torch::RestrictPtrTraits>(),
      protection_radius.packed_accessor32<float, 1, torch::RestrictPtrTraits>(), input_size);
}

} // namespace cv::deepweed

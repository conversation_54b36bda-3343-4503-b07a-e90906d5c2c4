#include "deepweed_model.h"

#include <c10/cuda/CUDAGuard.h>
#include <fmt/format.h>
#include <google/protobuf/util/json_util.h>
#include <spdlog/spdlog.h>

#include "cv/deepweed/deepweed_utils.h"
#include "cv/runtime/proto/cv_runtime.pb.h"
#include "deeplearning/model_io/cpp/model_utils.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/resize_util.h"

namespace F = torch::nn::functional;

namespace cv::deepweed {

using namespace lib::common;

const float kBeamRadiusIn = (5.0f / 2.0f) / 25.4f;

size_t EmbeddingPrediction::size(size_t dimension) { return embedding.size(dimension); }

DeepweedModel::DeepweedModel(model::AtomicModel model) : model_(model), gpu_id_(model_.get_gpu_id()) {
  if (!model_) {
    throw maka_error("DeepweedModel cannot be constructed with an empty model.");
  }

  if (!model_.get_internal_metadata().means().empty()) {
    std::vector<float> means(model_.get_internal_metadata().means().begin(),
                             model_.get_internal_metadata().means().end());
    means_ = torch::tensor(means).to({torch::kCUDA, gpu_id_});
  }

  if (!model_.get_internal_metadata().stds().empty()) {
    std::vector<float> stds(model_.get_internal_metadata().stds().begin(), model_.get_internal_metadata().stds().end());
    stds_ = torch::tensor(stds).to({torch::kCUDA, gpu_id_});
  }

  weed_point_index_categories_.clear();
  weed_point_category_indices_.clear();
  segmentation_index_categories_.clear();
  segmentation_category_indices_.clear();
  for (int i = 0; i < model_.get_internal_metadata().weed_point_classes_size(); i++) {
    const auto class_name = model_.get_internal_metadata().weed_point_classes(i);
    weed_point_index_categories_[i] = class_name;
    weed_point_category_indices_[class_name] = i;
  }
  for (int i = 0; i < model_.get_internal_metadata().segm_classes_size(); i++) {
    const auto class_name = model_.get_internal_metadata().segm_classes(i);
    segmentation_index_categories_[i] = class_name;
    segmentation_category_indices_[class_name] = i;
  }

  input_size_ = glm::ivec2(model_.get_internal_metadata().input_size().width(),
                           model_.get_internal_metadata().input_size().height());
  tile_size_ =
      glm::ivec2(model_.get_internal_metadata().tile().width(), model_.get_internal_metadata().tile().height());
  if (tile_size_.x == 0 || tile_size_.y == 0) {
    throw maka_error("Tile width or height is 0. Deepweed model malformed.");
  }
  batch_size_ = model_.get_internal_metadata().max_batch_size();
}

std::vector<std::string> DeepweedModel::get_supported_segmentation_categories() {
  std::vector<std::string> output;
  for (auto &pair : segmentation_category_indices_) {
    output.push_back(pair.first);
  }
  return output;
}

std::vector<std::string> DeepweedModel::get_supported_point_categories() {
  std::vector<std::string> output;
  for (auto &pair : weed_point_category_indices_) {
    output.push_back(pair.first);
  }
  return output;
}

const ModelMetadataProto &DeepweedModel::get_internal_metadata() { return model_.get_internal_metadata(); }
const nlohmann::json &DeepweedModel::get_external_metadata() { return model_.get_external_metadata(); }

float DeepweedModel::get_discard_points_border_px() {
  return model_.get_internal_metadata().discard_points_border_px();
}

int DeepweedModel::get_max_batch_size() { return batch_size_; }

void DeepweedModel::set_classifier(std::shared_ptr<embeddings::Classifier> embeddings_classifier) {
  embeddings_classifier_ = embeddings_classifier;
}

torch::Tensor DeepweedModel::normalize(torch::Tensor image) {
  torch::Tensor stds;
  torch::Tensor means;
  image = image.permute({0, 2, 3, 1});
  stds = stds_.slice(0, 0, 3);
  means = means_.slice(0, 0, 3);
  image /= 255.0f;
  // normalize to 0..1
  if (!model_.get_internal_metadata().means().empty()) {
    image -= means;
  }
  if (!model_.get_internal_metadata().stds().empty()) {
    image /= stds;
  }
  image = image.permute({0, 3, 1, 2});
  return image;
}

static std::set<int> tile_crop_coord(int size, int crop_size, int tile) {
  std::set<int> result;

  int origin = 0;
  while (origin + crop_size <= size) {
    if (origin > size / 2) {
      break;
    }
    result.insert(origin);
    origin += tile;
  }

  origin = size - crop_size;
  while (origin >= 0) {
    if (origin + crop_size < size / 2) {
      break;
    }
    result.insert(origin);
    origin -= tile;
  }

  return result;
}

bool DeepweedModel::set_crop_id(const std::string &crop_id) {
  // Don't do anything if crop embeddings are not enabled
  if (!model_.get_internal_metadata().crop_embeddings()) {
    return true;
  }
  const auto &crop_ids = model_.get_internal_metadata().crop_ids();
  auto crop_ids_iterator = std::find(crop_ids.begin(), crop_ids.end(), crop_id);

  if (crop_ids_iterator == crop_ids.end()) {
    return false;
  }

  auto crop_index = std::distance(crop_ids.begin(), crop_ids_iterator);
  crop_index_tensor_ =
      torch::tensor({crop_index}, torch::kInt32).expand({batch_size_}).contiguous().to({torch::kCUDA, gpu_id_});

  crop_id_ = crop_id;
  return true;
}

static std::vector<glm::ivec2> tile_crop_origins(glm::ivec2 size, glm::ivec2 crop_size, glm::ivec2 tile) {
  const std::set<int> crop_coords_x = tile_crop_coord(size.x, crop_size.x, tile.x);
  const std::set<int> crop_coords_y = tile_crop_coord(size.y, crop_size.y, tile.y);
  std::vector<glm::ivec2> crop_origins;
  for (int y : crop_coords_y) {
    for (int x : crop_coords_x) {
      crop_origins.push_back({x, y});
    }
  }
  return crop_origins;
}

static torch::Tensor tile_crop(torch::Tensor tensor, const std::vector<glm::ivec2> &crop_origins,
                               glm::ivec2 crop_size) {
  std::vector<torch::Tensor> crop_tensors;
  for (const auto coord : crop_origins) {
    crop_tensors.push_back(tensor.slice(-1, coord.x, coord.x + crop_size.x).slice(-2, coord.y, coord.y + crop_size.y));
  }

  return torch::cat(crop_tensors, 0);
}

static torch::Tensor stitch_tiled_crops(torch::Tensor tiled, std::vector<glm::ivec2> crop_origins,
                                        glm::ivec2 original_size, int downsample_factor) {
  const std::vector<int64_t> merged_shape = {tiled.size(1), original_size.y / downsample_factor,
                                             original_size.x / downsample_factor};
  glm::ivec2 tiled_dimensions(tiled.size(-1), tiled.size(-2));
  torch::Tensor result =
      torch::zeros(merged_shape, torch::TensorOptions().dtype(torch::kFloat32).device(tiled.device()));
  for (size_t i = 0; i < (size_t)tiled.size(0); i++) {
    glm::ivec2 scaled_crop_origin = crop_origins[i] / glm::ivec2(downsample_factor);
    glm::ivec2 output_pos = scaled_crop_origin + tiled_dimensions;
    glm::ivec2 clamped_output_pos = glm::clamp(output_pos, glm::ivec2(0), glm::ivec2(result.size(-1), result.size(-2)));
    glm::ivec2 clamped_size = clamped_output_pos - scaled_crop_origin;
    result.slice(1, scaled_crop_origin.y, output_pos.y).slice(2, scaled_crop_origin.x, output_pos.x) =
        torch::max(result.slice(1, scaled_crop_origin.y, output_pos.y).slice(2, scaled_crop_origin.x, output_pos.x),
                   tiled[i].slice(-1, 0, clamped_size.x).slice(-2, 0, clamped_size.y));
  }
  return result * 255;
}

DeepweedOutput DeepweedModel::infer(camera::CameraImage image,
                                    std::vector<runtime::proto::SegmentationDetectionCategory> segmentation_categories,
                                    float weed_point_threshold, float crop_point_threshold, float plant_point_threshold,
                                    bool return_embeddings, bool only_plant_points) {
  return infer(image.image, image.timestamp_ms, *image.ppi, segmentation_categories, weed_point_threshold,
               crop_point_threshold, plant_point_threshold, return_embeddings, only_plant_points);
}

DeepweedOutput DeepweedModel::infer(torch::Tensor image_tensor, int64_t image_timestamp_ms, float image_ppi,
                                    std::vector<runtime::proto::SegmentationDetectionCategory> segmentation_categories,
                                    float weed_point_threshold, float crop_point_threshold, float plant_point_threshold,
                                    bool return_embeddings, bool only_plant_points) {
  nvtxRangePushA("Deepweed_pre_processing");

  bool weed_enabled = model_.get_internal_metadata().weed_point_classes_size() > 0 && !only_plant_points;
  bool crop_enabled = model_.get_internal_metadata().crop_point_classes_size() > 0 && !only_plant_points;
  bool plant_enabled = model_.get_internal_metadata().plant_enabled() || only_plant_points;
  // these two values are not currently independent but maybe in the future
  bool trained_embeddings = model_.get_internal_metadata().trained_embeddings();
  bool contains_pumap_head = model_.get_internal_metadata().contains_pumap_head();

  std::vector<std::string> weed_point_categories =
      std::vector<std::string>(model_.get_internal_metadata().weed_point_classes().begin(),
                               model_.get_internal_metadata().weed_point_classes().end());

  image_tensor = image_tensor.to({torch::kCUDA, gpu_id_}).to(torch::kF32);
  while (image_tensor.ndimension() < 4) {
    image_tensor = image_tensor.unsqueeze(0);
  }
  image_tensor = normalize(image_tensor);

  glm::ivec2 original_size(image_tensor.size(-1), image_tensor.size(-2));

  glm::ivec2 adjusted_size = adjust_size_ppi(original_size, image_ppi, model_.get_internal_metadata().ppi());
  image_tensor = interpolate(image_tensor, adjusted_size);

  bool transposed = false;
  if (tile_crop_origins({image_tensor.size(-1), image_tensor.size(-2)}, input_size_, tile_size_).size() >
      tile_crop_origins({image_tensor.size(-2), image_tensor.size(-1)}, input_size_, tile_size_).size()) {
    image_tensor = image_tensor.transpose(-1, -2);
    transposed = true;
  }

  auto torch_type = deeplearning::model_io::dtype_to_torch_type(model_.get_internal_metadata().input_dtype());
  image_tensor = image_tensor.to(torch_type);

  const int pad_width = std::max(input_size_.x - (int)image_tensor.size(-1), 0);
  const int pad_height = std::max(input_size_.y - (int)image_tensor.size(-2), 0);
  glm::ivec2 before_pad_size(image_tensor.size(-1), image_tensor.size(-2));
  std::vector<int64_t> pad_dimensions = {
      (int64_t)std::floor((float)pad_width / 2.0f), (int64_t)std::ceil((float)pad_width / 2.0f),
      (int64_t)std::floor((float)pad_height / 2.0f), (int64_t)std::ceil((float)pad_height / 2.0f)};
  image_tensor = F::pad(image_tensor, F::PadFuncOptions(pad_dimensions));
  glm::ivec2 after_pad_size(image_tensor.size(-1), image_tensor.size(-2));
  std::vector<glm::ivec2> crop_origins =
      tile_crop_origins({image_tensor.size(-1), image_tensor.size(-2)}, input_size_, tile_size_);
  torch::Tensor cropped_gpu_image_batch = tile_crop(image_tensor, crop_origins, input_size_);
  nvtxRangePop();
  std::vector<DeepweedRawOutput> results;
  {
    NVTXRange range("Deepweed_model");
    for (int i = 0; i < cropped_gpu_image_batch.size(0); i += batch_size_) {
      auto batch = cropped_gpu_image_batch.slice(0, i, i + batch_size_);
      auto initial_batch_size = batch.size(0);
      // Expand to batch_size_ in case there are fewer items than batch_size_
      if (batch.size(0) < batch_size_) {
        int64_t additional_batch_items = batch_size_ - batch.size(0);
        batch = torch::cat({batch, batch.slice(0, 0, 1).expand({additional_batch_items, -1, -1, -1})}, 0).contiguous();
      }

      std::vector<torch::Tensor> output;
      if (crop_id_ && model_.get_internal_metadata().crop_embeddings()) {
        if (!crop_index_tensor_.defined()) {
          throw std::runtime_error("crop_index_tensor_ is not set");
        }

        output = model_({batch, crop_index_tensor_});
      } else if (model_.get_internal_metadata().crop_embeddings()) {
        throw std::runtime_error("Running Crop Embedding Deepweed model without specifying a crop ID is not possible");
      } else {
        output = model_({batch});
      }

      for (size_t output_i = 0; output_i < output.size(); output_i++) {
        output[output_i] = output[output_i].slice(0, 0, initial_batch_size);
      }
      results.push_back(DeepweedRawOutput(output));
    }
  }

  NVTXRange range("Deepweed_post_processing");
  std::vector<torch::Tensor> masks_list;
  std::vector<DeepweedDetection> deepweed_detections;
  for (size_t i = 0; i < results.size(); i++) {
    std::vector<std::vector<DeepweedDetection>> cropped_deepweed_detections = compute_deepweed_detections_v2(
        results[i], input_size_, weed_point_threshold, crop_point_threshold, plant_point_threshold, weed_enabled,
        crop_enabled, plant_enabled, weed_point_categories, segmentation_categories, segmentation_category_indices_,
        segmentation_index_categories_, results[i].mask, model_.get_internal_metadata().ppi(), trained_embeddings,
        contains_pumap_head, embeddings_classifier_);

    for (size_t j = 0; j < cropped_deepweed_detections.size(); j++) {
      for (auto &detection : cropped_deepweed_detections[j]) {
        const int tile_index = (int)i * batch_size_ + (int)j;
        detection.x += (float)crop_origins[tile_index].x;
        detection.y += (float)crop_origins[tile_index].y;
        detection.tile_index = tile_index;
        deepweed_detections.push_back(detection);
      }
    }
    masks_list.push_back(results[i].mask);
  }

  const float beam_radius_px = kBeamRadiusIn * model_.get_internal_metadata().ppi();
  deepweed_detections = non_maximum_suppression(deepweed_detections, beam_radius_px);

  const float model_ppi_to_predict_ppi = image_ppi / model_.get_internal_metadata().ppi();
  for (auto &weed_detection : deepweed_detections) {
    weed_detection.x = (weed_detection.x - (float)pad_dimensions[0]) * model_ppi_to_predict_ppi;
    weed_detection.y = (weed_detection.y - (float)pad_dimensions[2]) * model_ppi_to_predict_ppi;
    weed_detection.size = weed_detection.size * model_ppi_to_predict_ppi;
    const int batch_index = weed_detection.tile_index % batch_size_;
    const int batch_number = weed_detection.tile_index / batch_size_;
    if (return_embeddings && trained_embeddings && !results[batch_number].point_embeddings.empty() &&
        results[batch_number].point_embeddings[weed_detection.hit_ds].numel() != 0) {
      weed_detection.embedding = results[batch_number]
                                     .point_embeddings[weed_detection.hit_ds]
                                     .permute({0, 2, 3, 1})
                                     .index({batch_index, weed_detection.hit_y, weed_detection.hit_x})
                                     .cpu();
    }
    if (transposed) {
      std::swap(weed_detection.x, weed_detection.y);
    }
  }

  DeepweedOutput deepweed_output;
  if (segmentation_categories.size() > 0 && masks_list.size() > 0) {
    const int downsample_factor = (int)((input_size_.x + (masks_list[0].size(-1) - 1)) / masks_list[0].size(-1));
    torch::Tensor masks = torch::cat(masks_list, 0);
    torch::Tensor mask = stitch_tiled_crops(masks, crop_origins, after_pad_size, downsample_factor);
    mask = mask.slice(1, pad_dimensions[2], pad_dimensions[2] + before_pad_size.y)
               .slice(2, pad_dimensions[0], pad_dimensions[0] + before_pad_size.x);
    if (transposed) {
      mask = mask.transpose(-1, -2);
    }
    torch::Tensor mask_cpu = mask.to(torch::kUInt8).cpu().contiguous();
    std::memcpy(deepweed_output.mask.data(), mask_cpu.data_ptr(), mask_cpu.numel());
    deepweed_output.mask_width = (uint32_t)mask.size(-1);
    deepweed_output.mask_height = (uint32_t)mask.size(-2);
    deepweed_output.mask_channels = (uint32_t)mask.size(0);
    for (size_t i = 0; i < segmentation_categories.size(); i++) {
      deepweed_output.mask_channel_classes.push_back(segmentation_categories[i].category());
    }
  } else {
    deepweed_output.mask_width = 0;
    deepweed_output.mask_height = 0;
    deepweed_output.mask_channels = 0;
  }

  deepweed_output.timestamp_ms = image_timestamp_ms;
  deepweed_output.predict_in_distance_buffer = false;
  deepweed_output.detections = deepweed_detections;
  deepweed_output.weed_detection_classes = weed_point_categories;
  if (embeddings_classifier_) {
    deepweed_output.embedding_categories = embeddings_classifier_->get_categories();
  }

  return deepweed_output;
}

std::pair<int, int> DeepweedModel::get_input_shape() { return std::make_pair((int)input_size_.x, (int)input_size_.y); }

std::vector<EmbeddingPrediction> DeepweedModel::get_embedding(torch::Tensor image_tensor, float x, float y) {
  image_tensor = image_tensor.to({torch::kCUDA, gpu_id_}).to(torch::kF32);
  bool added_batch_dim = false;
  if (image_tensor.ndimension() == 3) {
    image_tensor = image_tensor.unsqueeze(0);
    added_batch_dim = true;
  }
  image_tensor = normalize(image_tensor);

  const int pad_width = std::max(input_size_.x - (int)image_tensor.size(-1), 0);
  const int pad_height = std::max(input_size_.y - (int)image_tensor.size(-2), 0);
  glm::ivec2 before_pad_size(image_tensor.size(-1), image_tensor.size(-2));
  std::vector<int64_t> pad_dimensions = {
      (int64_t)std::floor((float)pad_width / 2.0f), (int64_t)std::ceil((float)pad_width / 2.0f),
      (int64_t)std::floor((float)pad_height / 2.0f), (int64_t)std::ceil((float)pad_height / 2.0f)};
  image_tensor = F::pad(image_tensor, F::PadFuncOptions(pad_dimensions));

  auto torch_type = deeplearning::model_io::dtype_to_torch_type(model_.get_internal_metadata().input_dtype());
  image_tensor = image_tensor.to(torch_type);

  if (image_tensor.size(-1) > input_size_.x || image_tensor.size(-2) > input_size_.y) {
    throw maka_error("Input image size is larger than model input size. Input image size: " +
                     std::to_string(image_tensor.size(-1)) + "x" + std::to_string(image_tensor.size(-2)) +
                     ". Model input size: " + std::to_string(input_size_.x) + "x" + std::to_string(input_size_.y));
  }

  // Expand to batch_size_ in case there are fewer items than batch_size_
  auto initial_batch_size = image_tensor.size(0);
  if (image_tensor.size(0) < batch_size_) {
    int64_t additional_batch_items = batch_size_ - image_tensor.size(0);
    image_tensor =
        torch::cat({image_tensor, image_tensor.slice(0, 0, 1).expand({additional_batch_items, -1, -1, -1})}, 0)
            .contiguous();
  }
  std::vector<torch::Tensor> output;
  if (crop_id_ && model_.get_internal_metadata().crop_embeddings()) {
    if (!crop_index_tensor_.defined()) {
      throw std::runtime_error("crop_index_tensor_ is not set");
    }

    output = model_({image_tensor, crop_index_tensor_});
  } else if (model_.get_internal_metadata().crop_embeddings()) {
    throw std::runtime_error("Running Crop Embedding Deepweed model without specifying a crop ID is not possible");
  } else {
    output = model_({image_tensor});
  }
  for (size_t output_i = 0; output_i < output.size(); output_i++) {
    output[output_i] = output[output_i].slice(0, 0, initial_batch_size);
  }
  auto result = DeepweedRawOutput(output);

  if (result.point_embeddings.empty() || result.point_embeddings[0].numel() == 0) {
    return {};
  }

  std::vector<EmbeddingPrediction> embedding_predictions;
  for (size_t ds = 0; ds < result.point_embeddings.size(); ds++) {
    const glm::vec2 eff_downsample =
        glm::vec2(input_size_) / glm::vec2(result.point_embeddings[ds].size(-1), result.point_embeddings[ds].size(-2));
    const float mean_eff_downsample = (eff_downsample.x + eff_downsample.y) / 2.0f;

    int hit_x = (int)std::round((x + (float)pad_dimensions[0]) / mean_eff_downsample);
    int hit_y = (int)std::round((y + (float)pad_dimensions[2]) / mean_eff_downsample);

    if (hit_x < 0 || hit_x >= result.point_embeddings[ds].size(-1) || hit_y < 0 ||
        hit_y >= result.point_embeddings[ds].size(-2)) {
      EmbeddingPrediction prediction;
      prediction.embedding = torch::empty(0);
      prediction.weed_score = torch::empty(0);
      prediction.crop_score = torch::empty(0);
      embedding_predictions.push_back(prediction);
      continue;
    }

    auto embedding =
        result.point_embeddings[ds].index({torch::indexing::Slice(), torch::indexing::Slice(), hit_y, hit_x});
    if (added_batch_dim) {
      embedding = embedding.squeeze(0);
    }
    EmbeddingPrediction prediction;
    prediction.embedding = embedding;
    prediction.weed_score =
        result.point_hits[ds].index({torch::indexing::Slice(),
                                     torch::indexing::Slice(static_cast<int>(runtime::proto::HitClass::WEED),
                                                            static_cast<int>(runtime::proto::HitClass::WEED) + 1),
                                     hit_y, hit_x});
    prediction.crop_score =
        result.point_hits[ds].index({torch::indexing::Slice(),
                                     torch::indexing::Slice(static_cast<int>(runtime::proto::HitClass::CROP),
                                                            static_cast<int>(runtime::proto::HitClass::CROP) + 1),
                                     hit_y, hit_x});

    embedding_predictions.push_back(prediction);
  }

  return embedding_predictions;
}

} // namespace cv::deepweed

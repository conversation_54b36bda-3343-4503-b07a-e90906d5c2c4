#include "cv/deepweed/non_maximum_suppression.h"
#include "gtest/gtest.h"

TEST(NMS, BasicUsage) {
  std::vector<cv::deepweed::DeepweedDetection> detections;

  auto xs = std::vector<float>{5.f, 6.5f, 14.f, 20.f};
  auto ys = std::vector<float>{6.5f, 6.5f, 20.f, 20.f};
  auto sizes = std::vector<float>{2, 2, 12, 12};
  auto beam_radius_px = 5.f;
  auto scores = std::vector<float>{0.1f, 0.5f, 0.5f, 0.1f};

  for (size_t i = 0; i < xs.size(); i++) {
    cv::deepweed::DeepweedDetection detection;
    detection.x = xs[i];
    detection.y = ys[i];
    detection.size = sizes[i];
    detection.score = scores[i];
    detections.push_back(detection);
  }

  auto nms_detections = cv::deepweed::non_maximum_suppression(detections, beam_radius_px);

  ASSERT_EQ(nms_detections.size(), 2);
  ASSERT_TRUE(nms_detections[0].x == xs[1] && nms_detections[0].y == ys[1] && //
              nms_detections[0].size == sizes[1] && nms_detections[0].score == scores[1]);
  ASSERT_TRUE(nms_detections[1].x == xs[2] && nms_detections[1].y == ys[2] && //
              nms_detections[1].size == sizes[2] && nms_detections[1].score == scores[2]);
}

set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR})

add_executable(nms_test nms_test.cpp)
get_target_property(CV_RUNTIME_INCLUDES cv_runtime_lib INCLUDE_DIRECTORIES)
target_link_directories(nms_test PUBLIC /usr/local/lib /usr/local/cuda/lib64/ /opt/hpcx/ompi/lib/ /usr/lib/aarch64-linux-gnu)
target_link_libraries(nms_test gtest_main exceptions ${TORCH_LIBRARIES})
target_include_directories(nms_test SYSTEM PUBLIC ${CV_RUNTIME_INCLUDES})

gtest_discover_tests(nms_test DISCOVERY_TIMEOUT 10)
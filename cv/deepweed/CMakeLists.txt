add_library(deepweed SHARED deepweed_model.cpp deepweed_utils_cuda.cu)
target_link_libraries(deepweed PUBLIC trt_runtime exceptions cv_runtime_proto lib_common_model)
target_include_directories(deepweed SYSTEM PUBLIC ${nanoflann_DIR})

pybind11_add_module(deepweed_python SHARED deepweed_python.cpp)
target_link_libraries(deepweed_python PUBLIC deepweed torch_python)
target_compile_options(deepweed_python PRIVATE -fvisibility=default)
set_target_properties(deepweed_python PROPERTIES OUTPUT_NAME deepweed_python.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

add_subdirectory(tests)

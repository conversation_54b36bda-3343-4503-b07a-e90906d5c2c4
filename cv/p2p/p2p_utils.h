#include "cv/utils/resize.h"
#include "lib/common/cpp/resize_util.h"

namespace cv {
namespace p2p {

inline std::tuple<torch::Tensor, glm::vec2, glm::vec2> crop_and_resize_ppi(torch::Tensor input, float input_ppi,
                                                                           glm::ivec2 crop_center, glm::ivec2 crop_size,
                                                                           float target_ppi) {
  if (input.ndimension() == 3) {
    input = input.unsqueeze(0);
  }

  glm::ivec2 initial_crop_size = cv::utils::adjust_size_ppi(crop_size, target_ppi, input_ppi) + 10.0f;
  glm::vec2 initial_crop_coord_offset = glm::vec2(crop_center) - glm::vec2(initial_crop_size) / 2.0f;
  std::tie(input, std::ignore) = cv::utils::crop_or_pad(input, crop_center, initial_crop_size);

  glm::ivec2 input_at_model_ppi_size =
      cv::utils::adjust_size_ppi({input.size(-1), input.size(-2)}, input_ppi, target_ppi);
  input = lib::common::interpolate(input, input_at_model_ppi_size);

  glm::ivec2 final_crop_center = cv::utils::adjust_size_ppi(glm::vec2(initial_crop_size) / 2.0f, input_ppi, target_ppi);
  glm::vec2 final_crop_coord_offset = glm::vec2(final_crop_center) - glm::vec2(crop_size) / 2.0f;
  std::tie(input, std::ignore) = cv::utils::crop_or_pad(input, final_crop_center, crop_size);
  input = input.contiguous();
  return {input, initial_crop_coord_offset, final_crop_coord_offset};
}

} // namespace p2p

} // namespace cv
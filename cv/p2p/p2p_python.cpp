#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "cv/p2p/output.h"

namespace py = pybind11;

namespace cv {
namespace p2p {

PYBIND11_MODULE(p2p_python, m) {
  py::class_<P2POutput>(m, "P2POutput")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def_readwrite("matched", &P2POutput::matched)
      .def_readwrite("target_coord_x", &P2POutput::target_coord_x)
      .def_readwrite("target_coord_y", &P2POutput::target_coord_y)
      .def_readwrite("target_timestamp_ms", &P2POutput::target_timestamp_ms)
      .def_readwrite("predict_timestamp_ms", &P2POutput::predict_timestamp_ms)
      .def_readwrite("safe", &P2POutput::safe)
      .def_readwrite("predict_coord_x", &P2POutput::predict_coord_x)
      .def_readwrite("predict_coord_y", &P2POutput::predict_coord_y)
      .def_readwrite("predict_cam", &P2POutput::predict_cam);
}

} // namespace p2p
} // namespace cv

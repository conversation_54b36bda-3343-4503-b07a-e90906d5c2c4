#pragma once

#include <cstdint>
#include <glm/glm.hpp>
#include <string>

namespace cv {
namespace p2p {

struct alignas(int64_t) P2POutput {
  bool matched;
  float target_coord_x;
  float target_coord_y;
  int64_t target_timestamp_ms;
  int64_t predict_timestamp_ms;
  bool safe;
  float predict_coord_x = -1.0f;
  float predict_coord_y = -1.0f;
  std::string predict_cam = "";
};

struct alignas(int16_t) P2POutputForBurstRecord {
  std::string predict_buffer_name;
  glm::ivec2 predict_coord;
  glm::ivec2 predict_crop_size;
  float predict_ppi;
};

} // namespace p2p
} // namespace cv

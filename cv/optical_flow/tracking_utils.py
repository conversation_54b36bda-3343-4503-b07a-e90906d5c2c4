from collections import deque
from typing import Deque, <PERSON><PERSON>

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)


class TimestampedFlow:
    def __init__(self, *, x_delta: float, y_delta: float, timestamp_ms: int):
        self._timestamp_ms = timestamp_ms
        self._x_delta = x_delta
        self._y_delta = y_delta

    @property
    def x_delta(self) -> float:
        return self._x_delta

    @property
    def y_delta(self) -> float:
        return self._y_delta

    @property
    def timestamp_ms(self) -> int:
        return self._timestamp_ms


class FlowAccumulator:
    def __init__(self) -> None:
        self._accumulator: Deque[TimestampedFlow] = deque()

    def update(self, *, x_delta: float, y_delta: float, timestamp_ms: int) -> None:
        self._accumulator.append(TimestampedFlow(x_delta=x_delta, y_delta=y_delta, timestamp_ms=timestamp_ms))

    def get_flow(self, *, from_timestamp_ms: int, to_timestamp_ms: int) -> Tuple[float, float]:
        if from_timestamp_ms == to_timestamp_ms:
            return (0, 0)

        assert from_timestamp_ms < to_timestamp_ms, f"from: {from_timestamp_ms}, to: {to_timestamp_ms}"
        accumulator = self._accumulator
        assert (
            from_timestamp_ms >= accumulator[0].timestamp_ms and from_timestamp_ms < accumulator[-1].timestamp_ms
        ), f"from_timestamp_ms: {from_timestamp_ms} should be between {accumulator[0].timestamp_ms} and {accumulator[-1].timestamp_ms}"
        assert (
            to_timestamp_ms > accumulator[0].timestamp_ms and to_timestamp_ms <= accumulator[-1].timestamp_ms
        ), f"to_timestamp_ms: {to_timestamp_ms} should be between {accumulator[0].timestamp_ms} and {accumulator[-1].timestamp_ms}"
        assert len(accumulator) >= 2
        x_delta = 0.0
        y_delta = 0.0
        last_timestamp_ms = 0
        i = 0

        while i < len(accumulator) and accumulator[i].timestamp_ms <= from_timestamp_ms:
            last_timestamp_ms = accumulator[i].timestamp_ms
            i += 1

        if i < len(accumulator):
            portion = accumulator[i].timestamp_ms - from_timestamp_ms
            whole = accumulator[i].timestamp_ms - last_timestamp_ms

            proportion = portion / whole

            x_delta += proportion * accumulator[i].x_delta
            y_delta += proportion * accumulator[i].y_delta
            last_timestamp_ms = accumulator[i].timestamp_ms
            i += 1

        while i < len(accumulator) and accumulator[i].timestamp_ms <= to_timestamp_ms:
            last_timestamp_ms = accumulator[i].timestamp_ms
            x_delta += accumulator[i].x_delta
            y_delta += accumulator[i].y_delta
            i += 1

        if i < len(accumulator):
            portion = to_timestamp_ms - last_timestamp_ms
            whole = accumulator[i].timestamp_ms - last_timestamp_ms

            proportion = portion / whole

            x_delta += proportion * accumulator[i].x_delta
            y_delta += proportion * accumulator[i].y_delta

        return (x_delta, y_delta)

    def prune_up_to_timestamp_ms(self, timestamp_ms: int) -> None:
        while self._accumulator[0].timestamp_ms < timestamp_ms:
            self._accumulator.popleft()

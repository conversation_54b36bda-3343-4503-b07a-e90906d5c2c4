from enum import Enum
from typing import Any, cast

import cv2
import numpy.typing as npt

# Not exposed in python API
# https://docs.opencv.org/master/dc/d9d/classcv_1_1cuda_1_1NvidiaOpticalFlow__1__0.html


class OpticalFlowPerformanceLevel(Enum):
    NV_OF_PERF_LEVEL_SLOW = 5
    NV_OF_PERF_LEVEL_MEDIUM = 10
    NV_OF_PERF_LEVEL_FAST = 20


DEFAULT_OF_GPU_ID = 0


class NVIDIAOpticalFlowEngine:
    """
    A managed execution engine for an image stream. Reserves allocations on the GPU
    matching the size of the first images given.

    With NVIDIA Optical Flow SDK 1.0, you should use one engine per GPU, per camera stream.

    Note: This has only been tested with a single camera stream
    """

    def __init__(
        self,
        first_image: npt.NDArray[Any],
        performance_preset: OpticalFlowPerformanceLevel = OpticalFlowPerformanceLevel.NV_OF_PERF_LEVEL_SLOW,
        enable_temporal_hints: bool = True,
        enable_external_hints: bool = False,
        enable_cost_buffer: bool = False,
        gpu_id: int = DEFAULT_OF_GPU_ID,
    ):
        assert (
            cv2.__version__ >= "4.3.0"
        ), f"Please rebuild CV container with latest cv2 changes for optical flow support: {cv2.__version__}"

        assert (
            first_image.shape[0] >= 160 and first_image.shape[1] >= 160
        ), f"Optical flow requires height and width greater than or equal to 160 pixels, got height = {first_image.shape[0]} and width = {first_image.shape[1]}"

        # Helpful docs: https://devblogs.nvidia.com/an-introduction-to-the-nvidia-optical-flow-sdk/
        # The Optical Flow API consists of three major functions: Init, Execute and Destroy

        # 1. INIT
        #
        # wrapper around factory function for underlying Ptr< NvidiaOpticalFlow_1_0 >
        self._nvidia_optical_flow_impl = cv2.cuda_NvidiaOpticalFlow_1_0.create(  # type: ignore
            width=first_image.shape[1],
            height=first_image.shape[0],
            perfPreset=performance_preset.value,
            enableTemporalHints=enable_temporal_hints,
            enableExternalHints=enable_external_hints,
            enableCostBuffer=enable_cost_buffer,
            gpuId=gpu_id,
        )

        self._last_image_grayscale: npt.NDArray[Any] = cv2.cvtColor(first_image, cv2.COLOR_BGR2GRAY)

    def calc(self, *, next: npt.NDArray[Any], output_width: int, output_height: int) -> npt.NDArray[Any]:
        # TODO: we can cache this calculation since we run on a continuous image stream,
        # this ends up doing this twice per image
        next_grayscale: npt.NDArray[Any] = cv2.cvtColor(next, cv2.COLOR_BGR2GRAY)

        # Future: we can also connect a input/output CUDA stream and it will synchronize
        # the CUDA processing tasks with OF HW engine.
        # If not set, the execute function will use default stream which is NULL stream

        # 2. EXECUTE
        #
        # granular flow will have four 2D vectors per pixel, essential quarter-pixel accuracy
        raw_granular_flow = self._nvidia_optical_flow_impl.calc(self._last_image_grayscale, next_grayscale, None)
        # we don't need that level of detail, so do an upsample on the GPU
        flow_up_sampled = self._nvidia_optical_flow_impl.upSampler(
            raw_granular_flow[0], output_width, output_height, self._nvidia_optical_flow_impl.getGridSize(), None,
        )

        self._last_image_grayscale = next_grayscale

        return cast(npt.NDArray[Any], flow_up_sampled)

    def __del__(self) -> None:
        self._nvidia_optical_flow_impl.collectGarbage()

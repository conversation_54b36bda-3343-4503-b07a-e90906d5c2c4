add_library(comparison SHARED comparison_model.cpp)
target_link_libraries(comparison PUBLIC trt_runtime exceptions lib_common_model)

pybind11_add_module(comparison_python SHARED comparison_python.cpp)
target_link_libraries(comparison_python PUBLIC comparison torch_python)
target_compile_options(comparison_python PRIVATE -fvisibility=default)
set_target_properties(comparison_python PROPERTIES OUTPUT_NAME comparison_python.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

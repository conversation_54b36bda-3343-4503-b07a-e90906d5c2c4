from typing import Tuple


class TrackedItemCentroid:
    def __init__(
        self, pcam_id: str, x: float, y: float, size: float, timestamp_ms: int, has_perspective: bool = False,
    ):
        assert timestamp_ms != 0
        self._pcam_id = pcam_id
        self._x = x
        self._y = y
        self._size = size
        self._has_perspective = has_perspective
        self._timestamp_ms = timestamp_ms

    @property
    def pcam_id(self) -> str:
        return self._pcam_id

    @property
    def coord(self) -> Tuple[float, float]:
        return (self._x, self._y)

    @property
    def x(self) -> float:
        return self._x

    @property
    def y(self) -> float:
        return self._y

    def shift_coord(self, x_shift: float, y_shift: float) -> None:
        self._x += x_shift
        self._y += y_shift

    @property
    def size(self) -> float:
        return self._size

    @property
    def has_perspective(self) -> bool:
        return self._has_perspective

    def set_has_perspective(self, has_perspective: bool) -> None:
        self._has_perspective = has_perspective

    @property
    def timestamp_ms(self) -> int:
        return self._timestamp_ms

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, TrackedItemCentroid):
            raise NotImplementedError
        return (
            self.x == other.x
            and self.y == other.y
            and self.size == other.size
            and self.has_perspective == other.has_perspective
            and self.timestamp_ms == other.timestamp_ms
        )

    def __hash__(self) -> int:
        return hash((self.x, self.y, self.size, self.has_perspective, self.timestamp_ms))

    def __str__(self) -> str:
        return f"({self.x}, {self.y}) [size={self.size:.2f}, timestamp_ms={self.timestamp_ms}{', +has_perspective' if self.has_perspective else ''}]"

    def __repr__(self) -> str:
        return str(self)

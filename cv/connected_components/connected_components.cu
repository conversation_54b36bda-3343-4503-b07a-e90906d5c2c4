#include <cstdint>

extern "C" {
__device__ int _find(int labels[], int index) {
  int label = labels[index];

  while (label - 1 != index) {
    index = label - 1;
    label = labels[index];
  }

  return index;
}

__device__ void _union(int labels[], int a, int b) {
  bool done = (a == b);
  int _a = a;
  int _b = b;

  while (!done) {
    _a = _find(labels, _a);
    _b = _find(labels, _b);

    done = (_a == _b);

    if (!done) {
      if (_a < _b) {
        int old = atomicMin(&labels[_b], _a + 1);
        done = (old == _b + 1);
        _b = old - 1;
      } else if (_b < _a) {
        int old = atomicMin(&labels[_a], _b + 1);
        done = (old == _a + 1);
        _a = old - 1;
      }
    }
  }
}

__global__ void initialize(uint8_t image[], int labels[], int max_ind) {
  int t_ind = threadIdx.x;
  int block_ind = blockIdx.x;
  int block_width = blockDim.x;
  int pos = t_ind + block_ind * block_width;

  if (pos >= max_ind) {
    return;
  }

  if (image[pos] > 0) {
    labels[pos] = pos + 1;
  } else {
    labels[pos] = 0;
  }
}

__global__ void merge(uint8_t image[], int labels[], int row_width, int max_ind) {
  int t_ind = threadIdx.x;
  int block_ind = blockIdx.x;
  int block_width = blockDim.x;
  int pos = t_ind + block_ind * block_width;

  if (pos >= max_ind) {
    return;
  }

  if (image[pos] > 0) {
    int top_neighbor_index = pos - row_width;
    int left_neighbor_index = pos - 1;

    if (top_neighbor_index >= 0 && top_neighbor_index < max_ind) {
      if (image[top_neighbor_index] > 0) {
        _union(labels, pos, top_neighbor_index);
      }
    }

    if (left_neighbor_index >= 0 && left_neighbor_index < max_ind) {
      if (image[left_neighbor_index] > 0) {
        _union(labels, pos, left_neighbor_index);
      }
    }
  }
}

__global__ void analysis(uint8_t image[], int labels[], float x[], float y[], float size[], int row_width,
                         int max_ind) {
  int t_ind = threadIdx.x;
  int block_ind = blockIdx.x;
  int block_width = blockDim.x;
  int pos = t_ind + block_ind * block_width;

  if (pos >= max_ind) {
    return;
  }

  if (image[pos] > 0) {
    labels[pos] = _find(labels, pos) + 1;
    int label = labels[pos];
    atomicAdd(&x[label], pos % row_width);
    atomicAdd(&y[label], pos / row_width);
    atomicAdd(&size[label], 1);
  }
}

__global__ void get_centroids_cuda(float x[], float y[], float size[], int max_ind) {
  int t_ind = threadIdx.x;
  int block_ind = blockIdx.x;
  int block_width = blockDim.x;
  int pos = t_ind + block_ind * block_width;

  if (pos >= max_ind) {
    return;
  }

  if (size[pos] > 0) {
    x[pos] = x[pos] / size[pos];
    y[pos] = y[pos] / size[pos];
    size[pos] = sqrt(size[pos] / 3.141593);
  }
}
}
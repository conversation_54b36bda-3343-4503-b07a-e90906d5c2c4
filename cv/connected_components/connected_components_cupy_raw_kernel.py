import math
import sys
from typing import List, <PERSON><PERSON>

import numpy as np

import lib.common.logging
from cv.utils.cupy import CUPY_IMPORTED, xp

LOG = lib.common.logging.get_logger(__name__)

# Prevent this from being interpreted if cupy is not imported. <PERSON><PERSON><PERSON> throws an error because RawModule is not defined
if CUPY_IMPORTED:

    path = "cv/connected_components/connected_components.ptx"

    kernels = ["initialize", "merge", "analysis", "get_centroids_cuda"]
    mod = xp.RawModule(path=path)
    mod.compile(log_stream=sys.stdout)

    centroids: List[Tuple[float, float, float]] = []
    initialize = mod.get_function(kernels[0])
    merge = mod.get_function(kernels[1])
    analysis = mod.get_function(kernels[2])
    get_centroids_cuda = mod.get_function(kernels[3])

    def connected_components_cupy(image: xp.ndarray) -> List[Tuple[float, float, float]]:
        flattened_image = xp.ravel(image).astype(np.int8)
        flattened_labels = xp.zeros(flattened_image.shape, dtype=np.int32)
        x = xp.zeros(flattened_image.shape, dtype=np.float32)
        y = xp.zeros(flattened_image.shape, dtype=np.float32)
        size = xp.zeros(flattened_image.shape, dtype=np.float32)

        threads_per_block = 256
        blocks_per_grid = math.ceil(flattened_labels.shape[0] / threads_per_block)

        initialize(
            (blocks_per_grid,), (threads_per_block,), (flattened_image, flattened_labels, flattened_image.shape[0])
        )
        merge(
            (blocks_per_grid,),
            (threads_per_block,),
            (flattened_image, flattened_labels, image.shape[1], flattened_image.shape[0]),
        )
        analysis(
            (blocks_per_grid,),
            (threads_per_block,),
            (flattened_image, flattened_labels, x, y, size, image.shape[1], flattened_image.shape[0]),
        )
        xp.cuda.Stream.null.synchronize()
        get_centroids_cuda((blocks_per_grid,), (threads_per_block,), (x, y, size, flattened_image.shape[0]))
        xp.cuda.Stream.null.synchronize()
        nonzero = xp.nonzero(size)
        centroids = list(zip(x[nonzero].tolist(), y[nonzero].tolist(), size[nonzero].tolist()))

        return centroids

#include "connected_components_interface.h"

#include "connected_components.cu"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/tensor_util.h"

#include <c10/cuda/CUDAStream.h>

namespace cv {
namespace connected_components {
torch::Tensor get_centroids(torch::Tensor image) {
  image = image.contiguous();
  torch::Tensor labels =
      torch::zeros(image.sizes(), torch::TensorOptions().device(image.device()).dtype(torch::kInt32));
  torch::Tensor xs = torch::zeros({image.numel()}, torch::TensorOptions().device(image.device()).dtype(torch::kF32));
  torch::Tensor ys = torch::zeros({image.numel()}, torch::TensorOptions().device(image.device()).dtype(torch::kF32));
  torch::Tensor sizes = torch::zeros({image.numel()}, torch::TensorOptions().device(image.device()).dtype(torch::kF32));

  const int threads_per_block = 256;
  const int blocks_per_grid = (labels.numel() + threads_per_block - 1) / threads_per_block;

  with_device device_guard(image.device().index());
  auto torch_stream = c10::cuda::getCurrentCUDAStream();
  initialize<<<blocks_per_grid, threads_per_block, 0, torch_stream>>>((uint8_t *)image.data_ptr(),
                                                                      (int *)labels.data_ptr(), image.numel());

  merge<<<blocks_per_grid, threads_per_block, 0, torch_stream>>>((uint8_t *)image.data_ptr(), (int *)labels.data_ptr(),
                                                                 image.size(1), image.numel());

  analysis<<<blocks_per_grid, threads_per_block, 0, torch_stream>>>(
      (uint8_t *)image.data_ptr(), (int *)labels.data_ptr(), (float *)xs.data_ptr(), (float *)ys.data_ptr(),
      (float *)sizes.data_ptr(), image.size(1), image.numel());

  get_centroids_cuda<<<blocks_per_grid, threads_per_block, 0, torch_stream>>>(
      (float *)xs.data_ptr(), (float *)ys.data_ptr(), (float *)sizes.data_ptr(), image.numel());

  auto coords = lib::common::tensor_vector_to_list(torch::where(sizes != 0.0f));
  xs = xs.index(coords).unsqueeze(1);
  ys = ys.index(coords).unsqueeze(1);
  sizes = sizes.index(coords).unsqueeze(1);

  return torch::cat({xs, ys, sizes}, 1);
}
} // namespace connected_components
} // namespace cv

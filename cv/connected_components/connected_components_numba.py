# connected components numba
# https://prittt.github.io/pub_files/2018ipas_gpuccl.pdf

import math
from typing import List, Tuple

import numpy as np
from numba import cuda

import lib.common.logging
from cv.utils.cupy import xp

LOG = lib.common.logging.get_logger(__name__)


@cuda.jit(device=True)  # type: ignore
def find(labels: cuda.cudadrv.devicearray.DeviceNDArray, index: int) -> int:
    label = labels[index]
    while label - 1 != index:
        index = label - 1
        label = labels[index]
    return index


@cuda.jit(device=True)  # type: ignore
def union(labels: cuda.cudadrv.devicearray.DeviceNDArray, a: int, b: int) -> None:
    done = False
    while not done:
        a = find(labels, a)
        b = find(labels, b)

        done = a == b

        if not done:
            if a < b:
                old = cuda.atomic.min(labels, b, a + 1)
                done = old == b + 1
                b = old - 1
            elif b < a:
                old = cuda.atomic.min(labels, a, b + 1)
                done = old == a + 1
                a = old - 1


def initialize_raw(
    image: cuda.cudadrv.devicearray.DeviceNDArray, labels: cuda.cudadrv.devicearray.DeviceNDArray
) -> None:
    thread_idx = cuda.threadIdx
    block_idx = cuda.blockIdx
    block_dim = cuda.blockDim

    t_ind = thread_idx.x
    block_ind = block_idx.x
    block_width = block_dim.x
    ind = t_ind + block_ind * block_width

    if ind >= image.shape[0]:
        ind = image.shape[0] - 1

    if image[ind] > 0:
        labels[ind] = ind + 1
    else:
        labels[ind] = 0


initialize = cuda.jit(debug=False, fastmath=True)(initialize_raw)


def merge_raw(
    image: cuda.cudadrv.devicearray.DeviceNDArray, labels: cuda.cudadrv.devicearray.DeviceNDArray, row_width: int
) -> None:
    thread_idx = cuda.threadIdx
    block_idx = cuda.blockIdx
    block_dim = cuda.blockDim

    t_ind = thread_idx.x
    block_ind = block_idx.x
    block_width = block_dim.x
    ind = t_ind + block_ind * block_width

    if ind >= image.shape[0]:
        ind = image.shape[0] - 1

    if image[ind] > 0:
        top_neighbor_index = ind - row_width
        left_neighbor_index = ind - 1

        if top_neighbor_index >= 0 and top_neighbor_index < len(image):
            if image[top_neighbor_index] > 0:
                union(labels, ind, top_neighbor_index)
        if left_neighbor_index >= 0 and left_neighbor_index < len(image):
            if image[left_neighbor_index] > 0:
                union(labels, ind, left_neighbor_index)


merge = cuda.jit(debug=False, fastmath=True)(merge_raw)


def analysis_raw(
    image: cuda.cudadrv.devicearray.DeviceNDArray,
    labels: cuda.cudadrv.devicearray.DeviceNDArray,
    x: cuda.cudadrv.devicearray.DeviceNDArray,
    y: cuda.cudadrv.devicearray.DeviceNDArray,
    size: cuda.cudadrv.devicearray.DeviceNDArray,
    row_width: int,
) -> None:
    thread_idx = cuda.threadIdx
    block_idx = cuda.blockIdx
    block_dim = cuda.blockDim

    t_ind = thread_idx.x
    block_ind = block_idx.x
    block_width = block_dim.x
    ind = t_ind + block_ind * block_width

    if ind >= image.shape[0]:
        ind = image.shape[0] - 1

    if image[ind] > 0:
        labels[ind] = find(labels, ind) + 1
        label = labels[ind]
        cuda.atomic.add(x, label, ind % row_width)
        cuda.atomic.add(y, label, ind // row_width)
        cuda.atomic.add(size, label, 1)


analysis = cuda.jit(fastmath=True)(analysis_raw)


def get_centroids_cuda_raw(
    x: cuda.cudadrv.devicearray.DeviceNDArray,
    y: cuda.cudadrv.devicearray.DeviceNDArray,
    size: cuda.cudadrv.devicearray.DeviceNDArray,
    max_ind: int,
) -> None:
    thread_idx = cuda.threadIdx
    block_idx = cuda.blockIdx
    block_dim = cuda.blockDim

    t_ind = thread_idx.x
    block_ind = block_idx.x
    block_width = block_dim.x
    ind = t_ind + block_ind * block_width

    if ind >= max_ind:
        ind = max_ind - 1

    if size[ind] > 0:
        x[ind] /= size[ind]
        y[ind] /= size[ind]
        size[ind] = math.sqrt(size[ind] / math.pi)


get_centroids_cuda = cuda.jit(fastmath=True)(get_centroids_cuda_raw)


@cuda.jit  # type: ignore
def pixels_touched(labels: cuda.cudadrv.devicearray.DeviceNDArray) -> None:
    thread_idx = cuda.threadIdx
    block_idx = cuda.blockIdx
    block_dim = cuda.blockDim

    t_ind = thread_idx.x
    block_ind = block_idx.x
    block_width = block_dim.x
    pos = t_ind + block_ind * block_width

    labels[pos] = 1


def connected_components_numba(image: xp.ndarray) -> List[Tuple[float, float, float]]:
    flattened_image = xp.ravel(image)
    flattened_labels = xp.zeros(flattened_image.shape, dtype=np.int32)
    x = xp.zeros(flattened_image.shape, dtype=np.float32)
    y = xp.zeros(flattened_image.shape, dtype=np.float32)
    size = xp.zeros(flattened_image.shape, dtype=np.float32)

    threads_per_block = 1024
    blocks_per_grid = math.ceil(flattened_labels.shape[0] / threads_per_block)

    initialize[blocks_per_grid, threads_per_block](flattened_image, flattened_labels)
    merge[blocks_per_grid, threads_per_block](flattened_image, flattened_labels, image.shape[1])
    analysis[blocks_per_grid, threads_per_block](flattened_image, flattened_labels, x, y, size, image.shape[1])
    cuda.synchronize()
    get_centroids_cuda[blocks_per_grid, threads_per_block](x, y, size, flattened_image.shape[0])
    cuda.synchronize()
    nonzero = xp.nonzero(size)
    centroids = list(zip(x[nonzero].tolist(), y[nonzero].tolist(), size[nonzero].tolist()))

    return centroids


def get_centroids(labels: xp.ndarray, unique_labels: xp.ndarray) -> List[Tuple[float, float, float]]:
    centroids = []
    for label in unique_labels:
        if label == 0:
            continue
        matches = xp.nonzero(labels == label)
        size = len(matches[0])
        y = xp.mean(matches[0])
        x = xp.mean(matches[1])
        radius = math.sqrt(size / math.pi)
        centroids.append((x, y, radius))
    return centroids

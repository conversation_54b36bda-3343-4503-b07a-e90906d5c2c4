import os

from prometheus_client import start_http_server

from lib.common.role import is_simulator


def main() -> None:
    container = os.getenv("HOSTNAME", "")
    if is_simulator() and container == "cv2":
        start_http_server(62104)
    else:
        start_http_server(62103)
    extra_args = os.environ.get("MAKA_BOT_ARGS", "")
    os.execv(
        "./bin/cv_runtime", ["./bin/cv_runtime", "-r", "/models/models.yaml"] + extra_args.strip().split(" "),
    )


if __name__ == "__main__":
    main()

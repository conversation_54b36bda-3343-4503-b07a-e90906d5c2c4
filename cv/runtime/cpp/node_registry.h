#pragma once

#include "cv/runtime/cpp/node.h"
#include "cv/runtime/proto/cv_runtime.grpc.pb.h"
#include "lib/common/model/cpp/model_types.h"

#include <shared_mutex>
#include <typeindex>

namespace cv {
namespace runtime {
class NodeRegistry {

public:
  template <typename T, typename... Ts>
  inline T &create_model_node(std::string camera_id, lib::common::model::ModelUseCase use_case, Ts &&... node_args);
  template <typename T, typename... Ts>
  inline T &create_node(std::string camera_id, Ts &&... node_args);
  template <typename T, typename... Ts>
  inline T &create_node(Ts &&... args);

  template <typename T, typename... Ts>
  inline Connector<T> create_connector(Output<T> output, std::string cam_id, std::string connector_name,
                                       Ts &&... connector_args);

  template <typename T>
  inline bool has_typed_node(std::string camera_id) const;
  template <typename T>
  inline T &get_typed_node(std::string camera_id) const;
  template <typename T>
  inline const CVNode &get_node_by_type(std::string camera_id) const;

  inline bool has_model_node(std::string camera_id, lib::common::model::ModelUseCase use_case) const;
  template <typename T>
  inline T &get_model_node(std::string camera_id, lib::common::model::ModelUseCase use_case) const;

  inline bool has_connector(std::string camera_id, std::string connector_name) const;
  inline std::shared_ptr<ConnectorControl> get_connector(std::string camera_id, std::string connector_name) const;
  inline std::vector<std::string> get_connector_ids(std::string camera_id) const;

  inline bool has_camera(std::string camera_id) const;
  inline std::set<std::string> get_known_cameras() const;

  template <typename T>
  inline std::vector<std::pair<std::string, CVNode>> get_nodes_by_type() const;

  inline std::vector<CVNode> get_nodes() const;

private:
  std::set<std::string> known_cameras_;
  std::map<std::string, std::map<std::type_index, CVNode>> camera_to_nodes_;
  std::map<std::string, std::map<lib::common::model::ModelUseCase, CVNode>> camera_to_model_nodes_;
  std::map<std::string, std::map<std::string, std::shared_ptr<ConnectorControl>>> camera_to_connectors_;
  std::vector<CVNode> all_nodes_;
  mutable std::shared_mutex mutex_;
};

} // namespace runtime
} // namespace cv

#include "node_registry.inl"
#pragma once

#include <map>
#include <memory>
#include <optional>
#include <shared_mutex>
#include <unordered_map>

#include "config/tree/cpp/config_tree.hpp"
#include "cv/runtime/cpp/node_registry.h"
#include "frontend/proto/image_stream.grpc.pb.h"
#include "frontend/proto/image_stream.pb.h"
#include "lib/common/camera/cpp/camera_image.h"
#include "lib/common/cpp/fixed_buffer.h"
#include "weed_tracking/cpp/client/grpc_client.h"
#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_scoped_callback.hpp>
#include <cv/runtime/cpp/image_service_consts.hpp>
#include <weed_tracking_libs/tracker_roi/cpp/tracker_roi.hpp>

namespace cv {
namespace runtime {

class ImageStreamServiceImpl : public carbon::frontend::image_stream::ImageStreamService::Service {
public:
  ImageStreamServiceImpl(
      std::shared_ptr<
          std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, lib::common::camera::CameraImage>>>>
          stream_camera_buffers,
      std::shared_ptr<
          std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, lib::common::camera::CameraImage>>>>
          distance_camera_buffers,
      std::shared_ptr<NodeRegistry> node_registry, std::shared_ptr<carbon::config::ConfigTree> aimbot_config,
      std::shared_ptr<weed_tracking::WeedTrackingClient> weed_tracking_client);

  grpc::Status GetNextCameraImage(grpc::ServerContext *, const carbon::frontend::image_stream::CameraImageRequest *,
                                  carbon::frontend::image_stream::Image *) override;
  grpc::Status
  GetPredictImageByTimestamp(grpc::ServerContext *,
                             const carbon::frontend::image_stream::GetPredictImageByTimestampRequest *,
                             carbon::frontend::image_stream::GetPredictImageByTimestampResponse *) override;

  grpc::Status
  GetMultiPredictPerspectives(grpc::ServerContext *context,
                              const carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest *,
                              carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse *) override;

private:
  std::pair<std::string, int> global_camera_id_to_local(std::string global);

  std::shared_ptr<
      std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, lib::common::camera::CameraImage>>>>
      stream_camera_buffers_;
  std::shared_ptr<
      std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, lib::common::camera::CameraImage>>>>
      distance_camera_buffers_;
  std::shared_ptr<weed_tracking::WeedTrackingClient> weed_tracking_client_;
  std::shared_ptr<NodeRegistry> node_registry_;
  std::shared_ptr<carbon::config::ConfigTree> aimbot_config_;
  carbon::tracker_roi::TrackerRoiAccessor roi_accessor_;
};

} // namespace runtime
} // namespace cv

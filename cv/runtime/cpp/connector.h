#pragma once

#include <atomic>
#include <condition_variable>
#include <memory>
#include <optional>
#include <vector>

#include "lib/common/cpp/exceptions.h"

namespace cv {
namespace runtime {

class connector_closed_error : public maka_error {
public:
  explicit connector_closed_error(size_t stack_skip = 1) : maka_error("Connector closed", stack_skip + 1) {}
};

class ConnectorControl {
public:
  virtual ~ConnectorControl() = default;

  virtual void set_enabled(bool value) = 0;
  virtual bool get_enabled() const = 0;

  virtual void set_reduction_ratio(int value) = 0;
  virtual int get_reduction_ratio() const = 0;
};

template <typename T>
class Connector final : virtual public ConnectorControl {
public:
  Connector(int reduction_ratio = 1, bool overwrite = false) : state_(std::make_shared<_State<T>>()) {
    state_->reduction_ratio = reduction_ratio;
    state_->overwrite = overwrite;
    state_->reduction_idx = std::rand() % reduction_ratio;
  }

  void push(const T &value) {
    if (state_->enabled && ++state_->reduction_idx >= state_->reduction_ratio) {
      {
        std::unique_lock<std::mutex> lock(state_->mutex);
        while (!state_->overwrite && state_->value.has_value() && !state_->closed) {
          state_->receiver_done_condition.wait(lock);
        }
        if (state_->closed) {
          throw connector_closed_error();
        }
        state_->value = value;
      }
      state_->sender_done_condition.notify_all();
      state_->reduction_idx = 0;
    }
  }

  T pop() {
    T val;
    {
      std::unique_lock<std::mutex> lock(state_->mutex);
      while (!state_->value.has_value() && !state_->closed) {
        state_->sender_done_condition.wait(lock);
      }
      if (state_->closed) {
        throw connector_closed_error();
      }
      val = *state_->value;
      state_->value.reset();
    }
    state_->receiver_done_condition.notify_all();
    return val;
  }

  void close() {
    std::unique_lock<std::mutex> lock(state_->mutex);
    state_->closed = true;
    state_->sender_done_condition.notify_all();
    state_->receiver_done_condition.notify_all();
  }

  void set_enabled(bool value) override { state_->enabled = value; }
  bool get_enabled() const override { return state_->enabled; }

  void set_reduction_ratio(int value) override { state_->reduction_ratio = value; }
  int get_reduction_ratio() const override { return state_->reduction_ratio; }

private:
  template <typename TT>
  struct _State {
    // These are atomic to facilitate update from UI.
    std::atomic<int> reduction_ratio = 1;
    std::atomic<int> reduction_idx = 0;
    std::atomic<bool> enabled = true;
    std::condition_variable receiver_done_condition;
    std::condition_variable sender_done_condition;
    std::mutex mutex;
    std::optional<TT> value;
    bool closed = false;
    bool overwrite = false;
  };
  std::shared_ptr<_State<T>> state_;
};

template <typename T>
class Input {
public:
  Input(Connector<T> connector) : connector_(connector) {}

  T pop() { return connector_.pop(); }

  void close() { connector_.close(); }

private:
  Connector<T> connector_;
};

template <typename T>
class Output {
public:
  Output() : state_(std::make_shared<_State<T>>()) {}

  void operator+=(Connector<T> connector) {
    std::unique_lock<std::mutex> lck(state_->mutex);
    state_->connectors.push_back(connector);
  }

  void push(const T &value) {
    std::vector<Connector<T>> connectors;
    {
      std::unique_lock<std::mutex> lck(state_->mutex);
      connectors = state_->connectors;
    }
    for (auto &c : connectors) {
      c.push(value);
    }
  }

  void close() {
    std::vector<Connector<T>> connectors;
    {
      std::unique_lock<std::mutex> lck(state_->mutex);
      connectors = state_->connectors;
    }
    for (auto &c : connectors) {
      c.close();
    }
  }

  Connector<T> add_output(int reduction_ratio = 1, bool overwrite = false) {
    Connector<T> connector(reduction_ratio, overwrite);
    *this += connector;
    return connector;
  }

private:
  template <typename TT>
  struct _State {
    std::mutex mutex;
    std::vector<Connector<TT>> connectors;
  };
  std::shared_ptr<_State<T>> state_;
};

} // namespace runtime
} // namespace cv

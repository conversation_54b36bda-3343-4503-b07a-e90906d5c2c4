#pragma once

#include "lib/common/shmem/cpp/buffer/latest_buffer.hpp"
#include <cv/deepweed/output.h>
#include <cv/runtime/cpp/client/exceptions.hpp>
#include <generated/cv/runtime/proto/cv_runtime.grpc.pb.h>
#include <generated/cv/runtime/proto/cv_runtime.pb.h>
#include <grpcpp/grpcpp.h>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <memory>
#include <string>
#include <unordered_map>

namespace cv {
namespace runtime {
namespace client {

std::string get_secondary_address();

class CVRuntimeClient {
protected:
  std::string addr_;
  std::string addr2_;
  std::shared_ptr<grpc::Channel> channel_;
  std::shared_ptr<cv::runtime::proto::CVRuntimeService::Stub> stub_;
  std::shared_ptr<grpc::Channel> channel2_;
  std::shared_ptr<cv::runtime::proto::CVRuntimeService::Stub> stub2_;

  std::unordered_map<std::string, bool> cam_is_on_primary_cache_;
  bool has_secondary_;

public:
  CVRuntimeClient(const std::string &addr = "localhost:15053", const std::string &addr2 = get_secondary_address());
  ~CVRuntimeClient();

  void ping();
  void reset();
  void get_next_deepweed_prediction(std::string pcam_id, uint32_t timeout_ms, int64_t last_timestamp_ms,
                                    deepweed::DeepweedOutput *dw_buffer);
  std::string get_deepweed_buffer_name(std::string pcam_id);

  std::tuple<int64_t, int64_t> get_camera_dimensions(const std::string &cam_id);
  std::shared_ptr<cv::runtime::proto::DeepweedOutput> get_deepweed_output_by_ts(std::string pcam_id, int64_t ts);
  void set_image_score(std::string cam_id, int64_t timestamp_ms, double score,
                       deepweed::DeepweedOutput deepweed_output);
  bool wait_for_cv(lib::common::bot::BotStopEvent *bot_stop_event = nullptr);

private:
  grpc::Status exec_grpc(bool primary, std::function<grpc::Status()> func);
  grpc::Status exec_grpc_with_reset(bool primary, std::function<grpc::Status()> func);
  std::shared_ptr<cv::runtime::proto::CVRuntimeService::Stub> get_grpc_stub(bool primary);
  void reset_stub(bool primary);
  bool is_cam_on_primary(std::string cam_id);
};

} // namespace client
} // namespace runtime
} // namespace cv
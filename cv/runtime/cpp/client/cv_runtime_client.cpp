#include "lib/common/geometric/cpp/geometric_height_estimator.hpp"
#include <atomic>
#include <config/client/cpp/config_subscriber.hpp>
#include <cv/runtime/cpp/client/cv_runtime_client.hpp>
#include <cv/runtime/cpp/deepweed_output_protobuf.h>
#include <fmt/format.h>
#include <functional>
#include <lib/common/cpp/utils/role.hpp>
#include <spdlog/spdlog.h>

namespace cv {
namespace runtime {
namespace client {

constexpr std::chrono::milliseconds kDefaultDeadlineMs(500);
constexpr std::chrono::milliseconds kExtendedDeadlineMs(1000);

std::string get_secondary_address() {
  auto role = carbon::common::get_role();

  if (role == carbon::common::Role::ROLE_SIMULATOR_MINICOMPUTERS) {
    return "localhost:15054";
  } else if (role == carbon::common::Role::ROLE_ROW_PRIMARY) {
    return "**********:15053";
  }
  return "";
}

CVRuntimeClient::CVRuntimeClient(const std::string &addr, const std::string &addr2)
    : addr_(addr), addr2_(addr2), channel_(nullptr), has_secondary_(carbon::common::has_secondary()) {
  auto config_subscriber = carbon::config::get_global_config_subscriber();
  auto camerasNode1 = config_subscriber->get_config_node("cv", "cameras");
  for (auto &node : camerasNode1->get_children_nodes()) {
    cam_is_on_primary_cache_[node->get_name()] = node->get_node("is_on_primary")->get_value<bool>();
  }
}

CVRuntimeClient::~CVRuntimeClient() {}

std::shared_ptr<cv::runtime::proto::CVRuntimeService::Stub> CVRuntimeClient::get_grpc_stub(bool primary) {
  if (primary) {
    if (this->channel_ == nullptr) {
      this->channel_ = grpc::CreateChannel(this->addr_, grpc::InsecureChannelCredentials());
    }
    if (this->stub_ == nullptr) {
      this->stub_ = std::make_shared<cv::runtime::proto::CVRuntimeService::Stub>(this->channel_);
    }
    return this->stub_;
  } else {
    if (this->channel2_ == nullptr) {
      this->channel2_ = grpc::CreateChannel(this->addr2_, grpc::InsecureChannelCredentials());
    }
    if (this->stub2_ == nullptr) {
      this->stub2_ = std::make_shared<cv::runtime::proto::CVRuntimeService::Stub>(this->channel2_);
    }
    return this->stub2_;
  }
}

void CVRuntimeClient::reset() {
  this->reset_stub(true);
  if (has_secondary_) {
    this->reset_stub(false);
  }
}

void CVRuntimeClient::reset_stub(bool primary) {
  if (primary) {
    this->stub_ = nullptr;
    this->channel_ = nullptr;
  } else {
    this->stub2_ = nullptr;
    this->channel2_ = nullptr;
  }
}

grpc::Status CVRuntimeClient::exec_grpc(bool primary, std::function<grpc::Status()> func) {
  try {
    return func();
  } catch (const std::exception &ex) {
    this->reset_stub(primary);
    throw cv_runtime_client_error(ex.what());
  }
}

grpc::Status CVRuntimeClient::exec_grpc_with_reset(bool primary, std::function<grpc::Status()> func) {
  try {
    grpc::Status status = func();
    if (status.error_code() == grpc::StatusCode::UNAVAILABLE) {
      this->reset_stub(primary);
    }
    return status;
  } catch (const std::exception &ex) {
    this->reset_stub(primary);
    throw cv_runtime_client_error(ex.what());
  }
}

void CVRuntimeClient::ping() {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + kDefaultDeadlineMs);
  cv::runtime::proto::GetModelPathsRequest req;
  cv::runtime::proto::GetModelPathsResponse resp;
  std::shared_ptr<cv::runtime::proto::CVRuntimeService::Stub> stub = this->get_grpc_stub(true);
  grpc::Status status = this->exec_grpc(
      true, std::bind(&cv::runtime::proto::CVRuntimeService::Stub::GetModelPaths, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw cv_runtime_client_error(fmt::format("Invalid Ping Status Code: {}", status.error_code()));
  }

  if (has_secondary_) {
    grpc::ClientContext context2;
    cv::runtime::proto::GetModelPathsRequest req2;
    cv::runtime::proto::GetModelPathsResponse resp2;
    std::shared_ptr<cv::runtime::proto::CVRuntimeService::Stub> stub2 = this->get_grpc_stub(false);
    grpc::Status status2 = this->exec_grpc(
        false, std::bind(&cv::runtime::proto::CVRuntimeService::Stub::GetModelPaths, stub2, &context2, req2, &resp2));
    if (status2.error_code() != grpc::StatusCode::OK) {
      throw cv_runtime_client_error(fmt::format("Invalid Ping Status Code From Secondary: {}", status2.error_code()));
    }
  }
}

void CVRuntimeClient::get_next_deepweed_prediction(std::string pcam_id, uint32_t timeout_ms, int64_t last_timestamp_ms,
                                                   deepweed::DeepweedOutput *dw_buffer) {
  grpc::ClientContext context;
  // this call is expected to block for up to timeout_ms, so we add kDefaultDeadlineMs to the deadline to account for
  // network latency
  context.set_deadline(std::chrono::system_clock::now() + std::chrono::milliseconds(timeout_ms) + kDefaultDeadlineMs);
  cv::runtime::proto::GetNextDeepweedOutputRequest req;
  cv::runtime::proto::DeepweedOutput resp;
  bool primary = is_cam_on_primary(pcam_id);
  std::shared_ptr<cv::runtime::proto::CVRuntimeService::Stub> stub = this->get_grpc_stub(primary);

  req.set_cam_id(pcam_id);
  req.set_timeout_ms(timeout_ms);
  req.set_timestamp_ms(last_timestamp_ms);

  grpc::Status status =
      this->exec_grpc_with_reset(primary, std::bind(&cv::runtime::proto::CVRuntimeService::Stub::GetNextDeepweedOutput,
                                                    stub, &context, req, &resp));

  if (status.error_code() == grpc::StatusCode::ABORTED) {
    throw cv_runtime_client_aborted_error(
        fmt::format("Get Next Deepwed Output for {} was aborted due to timeout: {}", pcam_id, status.error_message()));
  } else if (status.error_code() != grpc::StatusCode::OK) {
    throw cv_runtime_client_error(
        fmt::format("Failed to Get Next Deepwed Output for {}: {}", pcam_id, status.error_message()));
  }

  cv::runtime::DeepweedOutputToProtobuf::from_protobuf(&resp, dw_buffer);
}

std::tuple<int64_t, int64_t> CVRuntimeClient::get_camera_dimensions(const std::string &cam_id) {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + kExtendedDeadlineMs);
  cv::runtime::proto::GetCameraDimensionsRequest req;
  req.set_cam_id(cam_id);
  cv::runtime::proto::GetCameraDimensionsResponse resp;
  bool primary = is_cam_on_primary(cam_id);
  std::shared_ptr<cv::runtime::proto::CVRuntimeService::Stub> stub = this->get_grpc_stub(primary);
  grpc::Status status = this->exec_grpc(
      primary, std::bind(&cv::runtime::proto::CVRuntimeService::Stub::GetCameraDimensions, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw cv_runtime_client_error(fmt::format("Failed to get camera dimensions for {}", cam_id));
  }

  std::tuple<int64_t, int64_t> height_width(resp.height(), resp.width());

  if (resp.transpose()) {
    height_width = std::tuple<int64_t, int64_t>(resp.width(), resp.height());
  }

  return height_width;
}

std::shared_ptr<cv::runtime::proto::DeepweedOutput> CVRuntimeClient::get_deepweed_output_by_ts(std::string pcam_id,
                                                                                               int64_t ts) {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + kExtendedDeadlineMs);
  cv::runtime::proto::GetDeepweedOutputByTimestampRequest req;
  req.set_cam_id(pcam_id);
  req.set_timestamp_ms(ts);
  auto resp = std::make_shared<cv::runtime::proto::DeepweedOutput>();
  bool primary = is_cam_on_primary(pcam_id);
  std::shared_ptr<cv::runtime::proto::CVRuntimeService::Stub> stub = this->get_grpc_stub(primary);
  grpc::Status status =
      this->exec_grpc(primary, std::bind(&cv::runtime::proto::CVRuntimeService::Stub::GetDeepweedOutputByTimestamp,
                                         stub, &context, req, resp.get()));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw cv_runtime_client_error(fmt::format("Failed to get deepweed output for cam {} ts {}", pcam_id, ts));
  }

  return resp;
}

void CVRuntimeClient::set_image_score(std::string cam_id, int64_t timestamp_ms, double score,
                                      deepweed::DeepweedOutput deepweed_output) {
  grpc::ClientContext context;
  context.set_deadline(std::chrono::system_clock::now() + kExtendedDeadlineMs);
  cv::runtime::proto::SetImageScoreRequest req;
  req.set_cam_id(cam_id);
  req.set_timestamp_ms(timestamp_ms);
  req.set_score(score);

  for (size_t i = 0; i < deepweed_output.detections.size(); i++) {
    deepweed::DeepweedDetection det = deepweed_output.detections[i];
    cv::runtime::proto::DeepweedDetection *deepweed_detection = req.mutable_deepweed_output()->add_detections();

    deepweed_detection->set_x(det.x);
    deepweed_detection->set_y(det.y);
    deepweed_detection->set_size(det.size);
    deepweed_detection->set_score(det.score);
    deepweed_detection->set_hit_class(det.hit_class);
    deepweed_detection->set_weed_score(det.weed_score);
    deepweed_detection->set_crop_score(det.crop_score);

    for (size_t j = 0; j < det.weed_detection_class_scores.size(); j++) {
      deepweed_detection->add_weed_detection_class_scores(det.weed_detection_class_scores[j]);
    }

    for (size_t j = 0; j < det.mask_intersections.size(); j++) {
      deepweed_detection->add_mask_intersections(det.mask_intersections[j]);
    }

    for (float f : det.reduced_scaled_embedding) {
      deepweed_detection->add_embedding(f);
    }
  }

  for (size_t i = 0; i < deepweed_output.weed_detection_classes.size(); i++) {
    req.mutable_deepweed_output()->add_weed_detection_classes(deepweed_output.weed_detection_classes[i]);
  }

  cv::runtime::proto::SetImageScoreResponse resp;
  bool primary = is_cam_on_primary(cam_id);
  std::shared_ptr<cv::runtime::proto::CVRuntimeService::Stub> stub = this->get_grpc_stub(primary);
  grpc::Status status = this->exec_grpc(
      primary, std::bind(&cv::runtime::proto::CVRuntimeService::Stub::SetImageScore, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw cv_runtime_client_error(
        fmt::format("Failed to set image score {} for camera {} at timestamp_ms {}", score, cam_id, timestamp_ms));
  }
}
static void error_log() {
  static std::atomic<uint32_t> count(0);
  uint32_t local_count = ++count;
  if (local_count % 120 == 0) {
    spdlog::info("Waiting for CV runtime to come online...");
  }
}
bool CVRuntimeClient::wait_for_cv(lib::common::bot::BotStopEvent *bot_stop_event) {
  while (bot_stop_event == nullptr || !bot_stop_event->is_stopped()) {
    try {
      ping();
      return true;
    } catch (cv_runtime_client_error &ex) {
      reset();
      error_log();
    }
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }
  // Exit due to bot_stop, not cv up
  return false;
}

bool CVRuntimeClient::is_cam_on_primary(std::string cam_id) {
  if (cam_is_on_primary_cache_.count(cam_id) == 0) {
    throw cv_runtime_client_error(fmt::format("Camera {} is not found", cam_id));
  }

  return cam_is_on_primary_cache_[cam_id];
}

} // namespace client
} // namespace runtime
} // namespace cv

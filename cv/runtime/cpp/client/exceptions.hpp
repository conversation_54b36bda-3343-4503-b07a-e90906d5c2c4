#pragma once

#include "lib/common/cpp/exceptions.h"

namespace cv {
namespace runtime {
namespace client {

class cv_runtime_client_error : public maka_error {
public:
  explicit cv_runtime_client_error(const std::string &what, size_t stack_skip = 1) : maka_error(what, stack_skip + 1) {}
};

class cv_runtime_client_aborted_error : public cv_runtime_client_error {
public:
  explicit cv_runtime_client_aborted_error(const std::string &what, size_t stack_skip = 1)
      : cv_runtime_client_error(what, stack_skip + 1) {}
};

} // namespace client
} // namespace runtime
} // namespace cv
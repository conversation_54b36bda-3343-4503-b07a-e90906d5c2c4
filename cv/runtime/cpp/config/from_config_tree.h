#pragma once

#include "lib/common/camera/cpp/camera_info.h"
#include "lib/common/camera/cpp/camera_settings.h"
#include <config/tree/cpp/config_tree.hpp>

namespace cv {
namespace runtime {

inline lib::common::camera::CameraSettings
get_camera_settings(std::shared_ptr<carbon::config::ConfigTree> config_tree) {
  lib::common::camera::CameraSettings settings;
  settings.mirror = config_tree->get_node("mirror")->get_value<bool>();
  settings.flip = config_tree->get_node("flip")->get_value<bool>();
  settings.strobing = config_tree->get_node("strobing")->get_value<bool>();
  settings.ptp = config_tree->get_node("ptp")->get_value<bool>();
  if (config_tree->get_node("roi/height")->get_value<float>() > 0) {
    settings.roi_height = config_tree->get_node("roi/height")->get_value<float>();
  }
  if (config_tree->get_node("roi/width")->get_value<float>() > 0) {
    settings.roi_width = config_tree->get_node("roi/width")->get_value<float>();
  }
  if (config_tree->get_node("roi/offset_x")->get_value<float>() > 0) {
    settings.roi_offset_x = config_tree->get_node("roi/offset_x")->get_value<float>();
  }
  if (config_tree->get_node("roi/offset_y")->get_value<float>() > 0) {
    settings.roi_offset_y = config_tree->get_node("roi/offset_y")->get_value<float>();
  }
  if (config_tree->get_node("exposure_us")->get_value<float>() > 0) {
    settings.exposure_us = config_tree->get_node("exposure_us")->get_value<float>();
  }
  if (!config_tree->get_node("light_source_preset")->get_value<std::string>().empty()) {
    std::string light_source_str = config_tree->get_node("light_source_preset")->get_value<std::string>();
    lib::common::camera::LightSourcePreset light_source_preset;
    if (light_source_str == "Off") {
      light_source_preset = lib::common::camera::LightSourcePreset::kOff;
    } else if (light_source_str == "Daylight5000K") {
      light_source_preset = lib::common::camera::LightSourcePreset::kDaylight5000K;
    } else if (light_source_str == "Daylight6500K") {
      light_source_preset = lib::common::camera::LightSourcePreset::kDaylight6500K;
    } else if (light_source_str == "Tungsten2800K") {
      light_source_preset = lib::common::camera::LightSourcePreset::kTungsten2800K;
    } else {
      throw maka_error(fmt::format("Unexpected value for LightSourcePreset: {}", light_source_str));
    }
    settings.light_source_preset = light_source_preset;
  }
  if (config_tree->get_node("gamma")->get_value<float>() != 0) {
    settings.gamma = config_tree->get_node("gamma")->get_value<float>();
  }
  if (config_tree->get_node("gain_db")->get_value<float>() != 0) {
    settings.gain_db = config_tree->get_node("gain_db")->get_value<float>();
  }
  if (config_tree->get_node("white_balance/red")->get_value<float>() != 0) {
    settings.wb_ratio_red = config_tree->get_node("white_balance/red")->get_value<float>();
  }
  if (config_tree->get_node("white_balance/green")->get_value<float>() != 0) {
    settings.wb_ratio_green = config_tree->get_node("white_balance/green")->get_value<float>();
  }
  if (config_tree->get_node("white_balance/blue")->get_value<float>() != 0) {
    settings.wb_ratio_blue = config_tree->get_node("white_balance/blue")->get_value<float>();
  }
  if (!config_tree->get_node("line_filter_selector")->get_value<std::string>().empty()) {
    settings.line_filter_selector = config_tree->get_node("line_filter_selector")->get_value<std::string>();
  }
  if (config_tree->get_node("line_filter_width")->get_value<float>() != 0) {
    settings.line_filter_width = config_tree->get_node("line_filter_width")->get_value<float>();
  }
  settings.sim_fps = config_tree->get_node("sim_fps")->get_value<float>();
  settings.stream_packet_resend = config_tree->get_node("stream_packet_resend")->get_value<bool>();
  return settings;
}

inline lib::common::camera::CameraInfo get_camera_info(std::shared_ptr<carbon::config::ConfigTree> config_tree) {
  lib::common::camera::CameraInfo info;
  if (!config_tree->get_node("ip_address")->get_value<std::string>().empty()) {
    info.ip_address = config_tree->get_node("ip_address")->get_value<std::string>();
  }
  if (!config_tree->get_node("serial_number")->get_value<std::string>().empty()) {
    info.serial_number = config_tree->get_node("serial_number")->get_value<std::string>();
  }
  if (!config_tree->get_node("model")->get_value<std::string>().empty()) {
    info.model = config_tree->get_node("model")->get_value<std::string>();
  }
  if (!config_tree->get_node("vendor")->get_value<std::string>().empty()) {
    auto vendor_string = config_tree->get_node("vendor")->get_value<std::string>();
    lib::common::camera::CameraVendor vendor = lib::common::camera::CameraVendor::kUnknown;
    if (vendor_string == "lucid") {
      vendor = lib::common::camera::CameraVendor::kThinkLucid;
    } else if (vendor_string == "basler") {
      vendor = lib::common::camera::CameraVendor::kBasler;
    } else if (vendor_string == "simulated") {
      vendor = lib::common::camera::CameraVendor::kSimulated;
    } else if (vendor_string == "mock") {
      vendor = lib::common::camera::CameraVendor::kMock;
    } else if (vendor_string == "kaya") {
      vendor = lib::common::camera::CameraVendor::kKaya;
    } else if (vendor_string == "zed") {
      vendor = lib::common::camera::CameraVendor::kZed;
    }

    info.vendor = vendor;
  }
  if (config_tree->get_node("ppi")->get_value<float>() != 0) {
    info.ppi = config_tree->get_node("ppi")->get_value<float>();
  }
  return info;
}
} // namespace runtime
} // namespace cv

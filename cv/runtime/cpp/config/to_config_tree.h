#pragma once

#include "config/tree/cpp/config_tree.hpp"
#include "lib/common/cpp/time.h"

namespace cv {
namespace runtime {
void cv_yaml_to_config(YAML::Node node, std::shared_ptr<carbon::config::ConfigTree> cameras_config) {
  auto config_def = cameras_config->get_def()->get_child("item");
  for (auto kv : node["cameras"]) {
    std::string name = kv.first.as<std::string>();
    if (cameras_config->get_node(name) == nullptr) {
      cameras_config->add_child(std::make_shared<carbon::config::ConfigTree>(name, config_def));
    }
    cameras_config->get_node(fmt::format("{}/enabled", name))->set_value(true, maka_control_timestamp_ms(), false);
    if (kv.second["serial_number"]) {
      cameras_config->get_node(fmt::format("{}/serial_number", name))
          ->set_value(kv.second["serial_number"].as<std::string>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["ip_address"]) {
      cameras_config->get_node(fmt::format("{}/ip_address", name))
          ->set_value(kv.second["ip_address"].as<std::string>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["vendor"]) {
      cameras_config->get_node(fmt::format("{}/vendor", name))
          ->set_value(kv.second["vendor"].as<std::string>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["model"]) {
      cameras_config->get_node(fmt::format("{}/model", name))
          ->set_value(kv.second["model"].as<std::string>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["ppi"]) {
      cameras_config->get_node(fmt::format("{}/ppi", name))
          ->set_value(kv.second["ppi"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["strobing"]) {
      cameras_config->get_node(fmt::format("{}/strobing", name))
          ->set_value(kv.second["settings"]["strobing"].as<bool>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["ptp"]) {
      cameras_config->get_node(fmt::format("{}/ptp", name))
          ->set_value(kv.second["settings"]["ptp"].as<bool>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["wb_ratio_red"]) {
      cameras_config->get_node(fmt::format("{}/white_balance/red", name))
          ->set_value(kv.second["settings"]["wb_ratio_red"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["wb_ratio_green"]) {
      cameras_config->get_node(fmt::format("{}/white_balance/green", name))
          ->set_value(kv.second["settings"]["wb_ratio_green"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["wb_ratio_blue"]) {
      cameras_config->get_node(fmt::format("{}/white_balance/blue", name))
          ->set_value(kv.second["settings"]["wb_ratio_blue"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["auto_brightness"]) {
      cameras_config->get_node(fmt::format("{}/auto_brightness/min_exposure_us", name))
          ->set_value(kv.second["auto_brightness"]["min_exposure_us"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/auto_brightness/max_exposure_us", name))
          ->set_value(kv.second["auto_brightness"]["max_exposure_us"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/auto_brightness/exposure_delta_us", name))
          ->set_value(kv.second["auto_brightness"]["exposure_delta_us"].as<double>(), maka_control_timestamp_ms(),
                      false);
      cameras_config->get_node(fmt::format("{}/auto_brightness_enabled", name))
          ->set_value(true, maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["gain_db"]) {
      cameras_config->get_node(fmt::format("{}/gain_db", name))
          ->set_value(kv.second["settings"]["gain_db"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["flip"]) {
      cameras_config->get_node(fmt::format("{}/flip", name))
          ->set_value(kv.second["settings"]["flip"].as<bool>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["transforms"]["transpose"]) {
      cameras_config->get_node(fmt::format("{}/transpose", name))
          ->set_value(kv.second["transforms"]["transpose"].as<bool>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["ptp"]) {
      cameras_config->get_node(fmt::format("{}/ptp", name))
          ->set_value(kv.second["settings"]["ptp"].as<bool>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["strobing"]) {
      cameras_config->get_node(fmt::format("{}/strobing", name))
          ->set_value(kv.second["settings"]["strobing"].as<bool>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["mirror"]) {
      cameras_config->get_node(fmt::format("{}/mirror", name))
          ->set_value(kv.second["settings"]["mirror"].as<bool>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["roi_height"]) {
      cameras_config->get_node(fmt::format("{}/roi/height", name))
          ->set_value(kv.second["settings"]["roi_height"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["roi_width"]) {
      cameras_config->get_node(fmt::format("{}/roi/width", name))
          ->set_value(kv.second["settings"]["roi_width"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["roi_offset_x"]) {
      cameras_config->get_node(fmt::format("{}/roi/offset_x", name))
          ->set_value(kv.second["settings"]["roi_offset_x"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["roi_offset_y"]) {
      cameras_config->get_node(fmt::format("{}/roi/offset_y", name))
          ->set_value(kv.second["settings"]["roi_offset_y"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["gamma"]) {
      cameras_config->get_node(fmt::format("{}/gamma", name))
          ->set_value(kv.second["settings"]["gamma"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["exposure_us"]) {
      cameras_config->get_node(fmt::format("{}/exposure_us", name))
          ->set_value(kv.second["settings"]["exposure_us"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["light_source_preset"]) {
      cameras_config->get_node(fmt::format("{}/light_source_preset", name))
          ->set_value(kv.second["settings"]["light_source_preset"].as<std::string>(), maka_control_timestamp_ms(),
                      false);
    }
    if (kv.second["settings"]["camera_matrix"]) {
      cameras_config->get_node(fmt::format("{}/camera_matrix/fx", name))
          ->set_value(kv.second["camera_matrix"]["fx"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/camera_matrix/fy", name))
          ->set_value(kv.second["camera_matrix"]["fy"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/camera_matrix/cx", name))
          ->set_value(kv.second["camera_matrix"]["cx"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/camera_matrix/cy", name))
          ->set_value(kv.second["camera_matrix"]["cy"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["distortion_coefficients"]) {
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/k1", name))
          ->set_value(kv.second["distortion_coefficients"]["k1"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/k2", name))
          ->set_value(kv.second["distortion_coefficients"]["k2"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/p1", name))
          ->set_value(kv.second["distortion_coefficients"]["p1"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/p2", name))
          ->set_value(kv.second["distortion_coefficients"]["p2"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/k3", name))
          ->set_value(kv.second["distortion_coefficients"]["k3"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/k4", name))
          ->set_value(kv.second["distortion_coefficients"]["k4"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/k5", name))
          ->set_value(kv.second["distortion_coefficients"]["k5"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/k6", name))
          ->set_value(kv.second["distortion_coefficients"]["k6"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/s1", name))
          ->set_value(kv.second["distortion_coefficients"]["s1"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/s2", name))
          ->set_value(kv.second["distortion_coefficients"]["s2"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/s3", name))
          ->set_value(kv.second["distortion_coefficients"]["s3"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/s4", name))
          ->set_value(kv.second["distortion_coefficients"]["s4"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/tau1", name))
          ->set_value(kv.second["distortion_coefficients"]["tau1"].as<double>(), maka_control_timestamp_ms(), false);
      cameras_config->get_node(fmt::format("{}/distortion_coefficients/tau2", name))
          ->set_value(kv.second["distortion_coefficients"]["tau2"].as<double>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["deepweed_infer"]) {
      cameras_config->get_node(fmt::format("{}/deepweed_enabled", name))
          ->set_value(true, maka_control_timestamp_ms(), false);
    }
    if (kv.second["p2p_infer"]) {
      cameras_config->get_node(fmt::format("{}/p2p_enabled", name))
          ->set_value(true, maka_control_timestamp_ms(), false);
    }
    if (kv.second["furrows_infer"]) {
      cameras_config->get_node(fmt::format("{}/furrows_enabled", name))
          ->set_value(true, maka_control_timestamp_ms(), false);
    }
    if (kv.second["buffer"]) {
      cameras_config->get_node(fmt::format("{}/buffer_enabled", name))
          ->set_value(true, maka_control_timestamp_ms(), false);
      if (kv.second["buffer"]["max_size"]) {
        cameras_config->get_node(fmt::format("{}/buffer_max_size", name))
            ->set_value(kv.second["buffer"]["max_size"].as<int64_t>(), maka_control_timestamp_ms(), false);
      }
    }
    if (kv.second["focus_metric"]) {
      cameras_config->get_node(fmt::format("{}/focus_metric_enabled", name))
          ->set_value(true, maka_control_timestamp_ms(), false);
    }
    if (kv.second["random_image_scoring"]) {
      cameras_config->get_node(fmt::format("{}/random_image_scoring_enabled", name))
          ->set_value(true, maka_control_timestamp_ms(), false);
    }
    if (kv.second["publisher"] && kv.second["publisher"]["reduction_ratio"]) {
      cameras_config->get_node(fmt::format("{}/publisher_reduction_ratio", name))
          ->set_value(kv.second["publisher"]["reduction_ratio"].as<int64_t>(), maka_control_timestamp_ms(), false);
    }
    if (kv.second["settings"]["sim_fps"]) {
      cameras_config->get_node(fmt::format("{}/sim_fps", name))
          ->set_value(kv.second["settings"]["sim_fps"].as<double>(), maka_control_timestamp_ms(), false);
    }
  }
}
} // namespace runtime
} // namespace cv

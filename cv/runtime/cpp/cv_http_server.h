#pragma once

#include "lib/common/camera/cpp/camera_image.h"
#include "lib/common/cpp/fixed_buffer.h"
#include <httplib.h>

namespace cv::runtime {

using CameraBufferMap = std::shared_ptr<
    std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, lib::common::camera::CameraImage>>>>;

class CVHttpServer {
public:
  CVHttpServer(CameraBufferMap distance_camera_buffers);
  ~CVHttpServer();

private:
  CameraBufferMap distance_camera_buffers_;

  std::thread thread_;
  std::unique_ptr<httplib::Server> server_;

  void run();
};

} // namespace cv::runtime
#include "cv/runtime/cpp/image_score_queue.h"
#include "generated/cv/runtime/proto/cv_runtime.pb.h"
#include "gtest/gtest.h"

TEST(ScoreQueue, BasicUsage) {
  cv::runtime::ScoreQueue score_queue(20);

  lib::common::camera::CameraImage cam_image_1;
  cam_image_1.camera_id = "1";
  cam_image_1.timestamp_ms = 11111;

  lib::common::camera::CameraImage cam_image_2;
  cam_image_2.camera_id = "1";
  cam_image_2.timestamp_ms = 22222;

  lib::common::camera::CameraImage cam_image_3;
  cam_image_3.camera_id = "1";
  cam_image_3.timestamp_ms = 33333;

  lib::common::camera::CameraImage cam_image_4;
  cam_image_4.camera_id = "1";
  cam_image_4.timestamp_ms = 44444;

  lib::common::camera::CameraImage cam_image_5;
  cam_image_5.camera_id = "1";
  cam_image_5.timestamp_ms = 55555;

  ASSERT_EQ(score_queue.size(), 0);

  score_queue.push({0.5, cam_image_1});

  auto timestamps_ms_in_heap = score_queue.timestamps();
  std::vector<int64_t> timestamps_in_expected_order{11111};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.9, cam_image_2});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {11111, 22222};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.7, cam_image_3});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {11111, 22222, 33333};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.1, cam_image_4});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {44444, 22222, 33333, 11111};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.79, cam_image_5});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {44444, 22222, 33333, 11111, 55555};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  ASSERT_EQ(score_queue.size(), 5);
  ASSERT_EQ(score_queue.peek_max().cam_image.timestamp_ms, 22222);

  auto max = score_queue.pop_max();

  ASSERT_EQ(score_queue.size(), 4);
  ASSERT_EQ(score_queue.peek_max().cam_image.timestamp_ms, 55555);
  ASSERT_EQ(max.cam_image.timestamp_ms, 22222);

  auto min = score_queue.pop_min();

  ASSERT_EQ(score_queue.size(), 3);
  ASSERT_EQ(score_queue.peek_min().cam_image.timestamp_ms, 11111);
  ASSERT_EQ(score_queue.peek_max().cam_image.timestamp_ms, 55555);
  ASSERT_EQ(min.cam_image.timestamp_ms, 44444);

  min = score_queue.pop_min();

  ASSERT_EQ(score_queue.size(), 2);
  ASSERT_EQ(score_queue.peek_max().cam_image.timestamp_ms, 55555);
  ASSERT_EQ(min.cam_image.timestamp_ms, 11111);

  max = score_queue.pop_max();
  ASSERT_EQ(score_queue.size(), 1);
  ASSERT_EQ(score_queue.peek_max().cam_image.timestamp_ms, 33333);
  ASSERT_EQ(max.cam_image.timestamp_ms, 55555);
}

TEST(ScoreQueue, MaxSize) {
  cv::runtime::ScoreQueue score_queue(5);

  lib::common::camera::CameraImage cam_image_1;
  cam_image_1.camera_id = "1";
  cam_image_1.timestamp_ms = 11111;

  lib::common::camera::CameraImage cam_image_2;
  cam_image_2.camera_id = "1";
  cam_image_2.timestamp_ms = 22222;

  lib::common::camera::CameraImage cam_image_3;
  cam_image_3.camera_id = "1";
  cam_image_3.timestamp_ms = 33333;

  lib::common::camera::CameraImage cam_image_4;
  cam_image_4.camera_id = "1";
  cam_image_4.timestamp_ms = 44444;

  lib::common::camera::CameraImage cam_image_5;
  cam_image_5.camera_id = "1";
  cam_image_5.timestamp_ms = 55555;

  lib::common::camera::CameraImage cam_image_6;
  cam_image_6.camera_id = "1";
  cam_image_6.timestamp_ms = 66666;

  lib::common::camera::CameraImage cam_image_7;
  cam_image_7.camera_id = "1";
  cam_image_7.timestamp_ms = 77777;

  ASSERT_EQ(score_queue.size(), 0);

  score_queue.push({0.5, cam_image_1});

  auto timestamps_ms_in_heap = score_queue.timestamps();
  std::vector<int64_t> timestamps_in_expected_order{11111};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.9, cam_image_2});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {11111, 22222};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.7, cam_image_3});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {11111, 22222, 33333};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.1, cam_image_4});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {44444, 22222, 33333, 11111};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.79, cam_image_5});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {44444, 22222, 33333, 11111, 55555};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  ASSERT_EQ(score_queue.size(), 5);
  ASSERT_EQ(score_queue.peek_min().cam_image.timestamp_ms, 44444);

  // Push something below min score
  score_queue.push({0.001, cam_image_6});
  ASSERT_EQ(score_queue.size(), 5);

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {44444, 22222, 33333, 11111, 55555};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  // Push something above min score
  score_queue.push({0.2, cam_image_7});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {77777, 22222, 33333, 11111, 55555};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  ASSERT_EQ(score_queue.size(), 5);
}

TEST(ScoreQueue, ReplaceScore) {
  cv::runtime::ScoreQueue score_queue(5);

  lib::common::camera::CameraImage cam_image_1;
  cam_image_1.camera_id = "1";
  cam_image_1.timestamp_ms = 11111;

  lib::common::camera::CameraImage cam_image_2;
  cam_image_2.camera_id = "1";
  cam_image_2.timestamp_ms = 22222;

  lib::common::camera::CameraImage cam_image_3;
  cam_image_3.camera_id = "1";
  cam_image_3.timestamp_ms = 33333;

  lib::common::camera::CameraImage cam_image_4;
  cam_image_4.camera_id = "1";
  cam_image_4.timestamp_ms = 44444;

  lib::common::camera::CameraImage cam_image_5;
  cam_image_5.camera_id = "1";
  cam_image_5.timestamp_ms = 55555;

  ASSERT_EQ(score_queue.size(), 0);

  score_queue.push({0.5, cam_image_1});

  auto timestamps_ms_in_heap = score_queue.timestamps();
  std::vector<int64_t> timestamps_in_expected_order{11111};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.9, cam_image_2});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {11111, 22222};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.7, cam_image_3});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {11111, 22222, 33333};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.1, cam_image_4});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {44444, 22222, 33333, 11111};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.79, cam_image_5});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {44444, 22222, 33333, 11111, 55555};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  score_queue.push({0.8, cam_image_1});

  timestamps_ms_in_heap = score_queue.timestamps();
  timestamps_in_expected_order = {44444, 22222, 33333, 55555, 11111};

  for (size_t i = 0; i < timestamps_in_expected_order.size(); i++) {
    ASSERT_EQ(timestamps_ms_in_heap[i], timestamps_in_expected_order[i]);
  }

  ASSERT_EQ(score_queue.size(), 5);
}

TEST(ScoreQueue, Push) {
  cv::runtime::ScoreQueue score_queue(20);

  std::vector<std::tuple<double, std::string, int64_t>> items{
      std::make_tuple(0.6800000071525574, "predict2", 7543068),
      std::make_tuple(0.6700000166893005, "predict1", 7543267),
      std::make_tuple(0.33000001311302185, "predict2", 7543333),
      std::make_tuple(0.4000000059604645, "predict3", 7543340),
      std::make_tuple(0.07000000029802322, "predict4", 7543407),
      std::make_tuple(0.3199999928474426, "predict1", 7543532),
      std::make_tuple(0.05999999865889549, "predict3", 7543606),
      std::make_tuple(0.7200000286102295, "predict4", 7543672),
      std::make_tuple(0.9800000190734863, "predict1", 7543798),
      std::make_tuple(0.3799999952316284, "predict4", 7543938),
      std::make_tuple(0.75, "predict3", 7543075),
      std::make_tuple(0.6399999856948853, "predict2", 7543864),
      std::make_tuple(0.7099999785423279, "predict3", 7543871),
      std::make_tuple(0.6299999952316284, "predict1", 7544063),
      std::make_tuple(0.28999999165534973, "predict2", 7544129),
      std::make_tuple(0.3700000047683716, "predict3", 7544137),
      std::make_tuple(0.9900000095367432, "predict4", 7544999),
      std::make_tuple(0.9900000095367432, "predict2", 7550499),
      std::make_tuple(0.45, "predict2", 7550634),
      std::make_tuple(0.9900000095367432, "predict2", 9825199),
  };

  for (auto [score, cam_id, timestamp] : items) {
    lib::common::camera::CameraImage cam_image;
    cam_image.camera_id = cam_id;
    cam_image.timestamp_ms = timestamp;
    score_queue.push({score, cam_image});
  }

  ASSERT_EQ(score_queue.size(), 20);
  ASSERT_EQ(score_queue.peek_min().cam_image.timestamp_ms, 7543606);

  lib::common::camera::CameraImage cam_image;
  cam_image.camera_id = "predict2";
  cam_image.timestamp_ms = 9841399;
  score_queue.push({0.9900000095367432, cam_image});

  ASSERT_EQ(score_queue.size(), 20);
  ASSERT_EQ(score_queue.peek_min().cam_image.timestamp_ms, 7543407);
}

TEST(ScoreQueue, DeepweedDetections) {
  cv::runtime::ScoreQueue score_queue(5);

  lib::common::camera::CameraImage cam_image_1;
  cam_image_1.camera_id = "1";
  cam_image_1.timestamp_ms = 11111;

  cv::runtime::proto::DeepweedOutput deepweed_output;
  cv::runtime::proto::DeepweedDetection *detection = deepweed_output.add_detections();
  detection->set_x(0);
  detection->set_y(0);
  detection->set_size(0);
  detection->set_score(0);
  detection->set_hit_class(cv::runtime::proto::HitClass::CROP);
  detection->set_weed_score(0);
  detection->set_crop_score(0);
  detection->add_weed_detection_class_scores(1);

  deepweed_output.add_weed_detection_classes("CARROT");

  ASSERT_EQ(score_queue.size(), 0);
  score_queue.push({0.5, cam_image_1, deepweed_output});
  ASSERT_EQ(score_queue.size(), 1);
}
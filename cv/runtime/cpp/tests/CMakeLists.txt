set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR})

add_executable(image_score_queue_test image_score_queue_test.cpp)
target_compile_definitions(image_score_queue_test PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_link_directories(image_score_queue_test PUBLIC /usr/local/lib /usr/local/cuda/lib64/ /opt/hpcx/ompi/lib/)
target_link_libraries(image_score_queue_test gtest_main exceptions spdlog fmt simulator_proto cv_runtime_proto weed_tracking_client ${TORCH_LIBRARIES})
target_include_directories(image_score_queue_test SYSTEM PUBLIC ${CV_RUNTIME_INCLUDES})

gtest_discover_tests(image_score_queue_test DISCOVERY_TIMEOUT 10)
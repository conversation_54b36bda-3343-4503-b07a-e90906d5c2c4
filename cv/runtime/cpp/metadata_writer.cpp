#include "metadata_writer.h"

#include "lib/common/cpp/exceptions.h"
#include "targeting/cpp/targeting_mode_watcher.hpp"
#include <spdlog/async.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>

#ifdef PYBIND
namespace py = pybind11;
#endif

namespace cv {
namespace runtime {

MetadataWriter::MetadataWriter() {}

void MetadataWriter::write_metadata(std::string image_path, GeoLLAData lla, GeoECEFData ecef, int64_t timestamp_ms,
                                    std::string cam_id, std::string capture_method, float ppi) {

  auto filepath = fmt::format("{}.metadata.json", image_path);
  auto crop = carbon::config::get_global_config_subscriber()->get_config_node("commander", "current_crop");
  auto crop_id = carbon::config::get_global_config_subscriber()->get_config_node("commander", "current_crop_id");

  boost::property_tree::ptree pt;

  char *row_id_ptr = std::getenv("MAKA_ROW");
  std::string row_id = "1";
  if (row_id_ptr != NULL) {
    row_id = row_id_ptr;
  }

  char *robot_id_ptr = std::getenv("MAKA_ROBOT_NAME");
  std::string robot_id = "unset";
  if (robot_id_ptr != NULL) {
    robot_id = robot_id_ptr;
  }

  pt.put("geo.lla.lat", lla.get_lat());
  pt.put("geo.lla.lng", lla.get_lng());
  pt.put("geo.lla.alt", lla.get_alt());
  pt.put("geo.lla.timestamp_ms", lla.get_timestamp_ms());

  pt.put("geo.ecef.x", ecef.get_x());
  pt.put("geo.ecef.y", ecef.get_y());
  pt.put("geo.ecef.z", ecef.get_z());
  pt.put("geo.ecef.timestamp_ms", ecef.get_timestamp_ms());

  pt.put("timestamp_ms", timestamp_ms);
  pt.put("cam_id", cam_id);
  pt.put("robot_id", robot_id);
  pt.put("row_id", row_id);
  pt.put("ppi", ppi);
  pt.put("capture_method", capture_method);
  pt.put("weeding_enabled", carbon::targeting::TargetingModeWatcher::get().weeding_enabled());
  pt.put("thinning_enabled", carbon::targeting::TargetingModeWatcher::get().thinning_enabled());

  if (!crop && !crop_id) {
    throw maka_error(fmt::format("No Crop or CropID is specified by config service."));
  }

  pt.put("crop", crop->get_value<std::string>());
  pt.put("crop_id", crop_id->get_value<std::string>());

  std::stringstream ss;
  boost::property_tree::json_parser::write_json(ss, pt);

  std::ofstream metadata_file;
  metadata_file.open(filepath, std::ofstream::out);
  metadata_file << ss.str();
  metadata_file.close();
}

} // namespace runtime
} // namespace cv

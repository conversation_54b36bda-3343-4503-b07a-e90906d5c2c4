#pragma once
#include "cv/runtime/proto/cv_runtime.grpc.pb.h"
#include "lib/common/camera/cpp/camera_image.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/geo_data.h"
#include "lib/common/cpp/min_max_heap.h"
#include <map>
#include <math.h>
#include <mutex>
#include <optional>
#include <spdlog/spdlog.h>
#include <string>
#include <vector>

namespace cv {
namespace runtime {

using namespace lib::common;

struct ScoreQueueObject {
  double score;
  lib::common::camera::CameraImage cam_image;
  std::optional<proto::DeepweedOutput> deepweed_detections;

  ScoreQueueObject(double _score, lib::common::camera::CameraImage _cam_image,
                   std::optional<proto::DeepweedOutput> _deepweed_detections)
      : score(_score), cam_image(_cam_image), deepweed_detections(_deepweed_detections) {}

  ScoreQueueObject(double _score, lib::common::camera::CameraImage _cam_image)
      : ScoreQueueObject(_score, _cam_image, std::nullopt) {}

  bool operator==(const ScoreQueueObject &rhs) {
    return cam_image.camera_id == rhs.cam_image.camera_id && cam_image.timestamp_ms == rhs.cam_image.timestamp_ms;
  }
  bool operator<(const ScoreQueueObject &rhs) { return score < rhs.score; }
  bool operator>(const ScoreQueueObject &rhs) { return score > rhs.score; }
  bool operator<=(const ScoreQueueObject &rhs) { return !(score > rhs.score); }
  bool operator>=(const ScoreQueueObject &rhs) { return !(score < rhs.score); }

  std::string key() { return cam_image.camera_id + std::to_string(cam_image.timestamp_ms); }
};

struct P2PScoreQueueObject {
  double score;
  lib::common::camera::CameraImage target;
  lib::common::camera::CameraImage perspective;
  torch::Tensor annotated_target;

  bool operator==(const P2PScoreQueueObject &rhs) {
    return target.camera_id == rhs.target.camera_id && target.timestamp_ms == rhs.target.timestamp_ms &&
           perspective.camera_id == rhs.perspective.camera_id &&
           perspective.timestamp_ms == rhs.perspective.timestamp_ms;
  }
  bool operator<(const P2PScoreQueueObject &rhs) { return score < rhs.score; }
  bool operator>(const P2PScoreQueueObject &rhs) { return score > rhs.score; }
  bool operator<=(const P2PScoreQueueObject &rhs) { return !(score > rhs.score); }
  bool operator>=(const P2PScoreQueueObject &rhs) { return !(score < rhs.score); }

  std::string key() {
    return target.camera_id + std::to_string(target.timestamp_ms) + perspective.camera_id +
           std::to_string(perspective.timestamp_ms);
  }
};

struct ChipScoreMetadata {
  float x;
  float y;
  float radius;
  std::string model_id;
  std::map<std::string, float> scores;
  std::map<std::string, float> embedding_distances;
  std::string band_status = "unknown";
};

struct ChipScoreQueueObject {
  double score;
  lib::common::camera::CameraImage cam_image;

  ChipScoreMetadata metadata;
  bool operator==(const ChipScoreQueueObject &rhs) {
    return cam_image.camera_id == rhs.cam_image.camera_id && cam_image.timestamp_ms == rhs.cam_image.timestamp_ms &&
           metadata.x == rhs.metadata.x && metadata.y == rhs.metadata.y;
  }
  bool operator<(const ChipScoreQueueObject &rhs) { return score < rhs.score; }
  bool operator>(const ChipScoreQueueObject &rhs) { return score > rhs.score; }
  bool operator<=(const ChipScoreQueueObject &rhs) { return !(score > rhs.score); }
  bool operator>=(const ChipScoreQueueObject &rhs) { return !(score < rhs.score); }

  std::string key() {
    return cam_image.camera_id + std::to_string(cam_image.timestamp_ms) + std::to_string(metadata.x) +
           std::to_string(metadata.y);
  }
};

template <class T = ScoreQueueObject>
class ScoreQueue {
public:
  ScoreQueue(size_t max_size) : heap_(MinMaxHeap<T>()), max_size_(max_size) {}

  void push(T score_queue_object) {
    std::lock_guard lock(mutex_);

    // If item already exists, pop it from heap, assign the max score, push back onto queue
    if (items_set_.count(score_queue_object.key()) > 0) {
      auto popped_item = heap_.pop_item(score_queue_object);

      if (popped_item.has_value()) {
        score_queue_object.score = std::max(score_queue_object.score, popped_item.value().score);
        heap_.push(score_queue_object);
      }
    } else {
      heap_.push(score_queue_object);
      items_set_.insert(score_queue_object.key());

      if (heap_.size() > max_size_) {
        auto min_item = heap_.pop_min();
        items_set_.erase(min_item.key());
      }
    }
  }

  void flush() {
    std::lock_guard lock(mutex_);
    heap_.clear();
    items_set_.clear();
  }

  T pop_min() {
    std::lock_guard lock(mutex_);
    auto min_item = heap_.pop_min();
    items_set_.erase(min_item.key());
    return min_item;
  }

  T pop_max() {
    std::lock_guard lock(mutex_);
    auto max_item = heap_.pop_max();
    items_set_.erase(max_item.key());
    return max_item;
  }

  std::vector<proto::ScoreObject> enumerate_heap() {
    std::vector<T> score_queue_objects;
    {
      std::lock_guard lock(mutex_);
      score_queue_objects = heap_.enumerate_heap();
    }

    std::vector<proto::ScoreObject> score_objects;
    for (auto heap_item : score_queue_objects) {
      proto::ScoreObject score_object;
      score_object.set_cam_id(heap_item.cam_image.camera_id);
      score_object.set_timestamp_ms(heap_item.cam_image.timestamp_ms);
      score_object.set_score((float)heap_item.score);
      score_objects.push_back(score_object);
    }
    return score_objects;
  }

  std::vector<int64_t> timestamps() {
    std::vector<T> score_queue_objects;
    {
      std::lock_guard lock(mutex_);
      score_queue_objects = heap_.enumerate_heap();
    }

    std::vector<int64_t> timestamps_ms;
    for (auto heap_item : score_queue_objects) {
      timestamps_ms.push_back(heap_item.cam_image.timestamp_ms);
    }
    return timestamps_ms;
  }

  T peek_max() {
    std::lock_guard lock(mutex_);
    return heap_.peek_max();
  }

  T peek_min() {
    std::lock_guard lock(mutex_);
    return heap_.peek_min();
  }

  void remove_old_points(int64_t timestamp_ms, int64_t max_age) {
    std::lock_guard lock(mutex_);
    while (heap_.size() != 0 && (timestamp_ms - heap_.peek_max().cam_image.timestamp_ms) > max_age) {
      auto max_item = heap_.pop_max();
      items_set_.erase(max_item.key());
    }
  }

  size_t size() {
    std::lock_guard lock(mutex_);
    return heap_.size();
  };

  MinMaxHeap<T> heap_;
  std::mutex mutex_;
  size_t max_size_;
  std::set<std::string> items_set_;
};

} // namespace runtime
} // namespace cv
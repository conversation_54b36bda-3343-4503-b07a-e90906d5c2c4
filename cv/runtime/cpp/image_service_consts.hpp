#pragma once

/*
 * This file contains constants for the image service.
 * It is intended to be lightweight and not include any heavy dependencies as
 * it is included in many places outside of cv.
 */

#include <string>
#include <unordered_set>

namespace cv {
namespace runtime {

const float kBeamRadiusIn = (5.0f / 2.0f) / 25.4f;
const int kGetCameraImageTimeoutMS = 5000;
const int kCropRadius = 300;

} // namespace runtime
} // namespace cv
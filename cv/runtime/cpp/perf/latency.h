#pragma once

#include <algorithm>
#include <deque>
#include <memory>
#include <mutex>
#include <numeric>

#include "lib/common/cpp/time.h"

namespace cv {
namespace runtime {

// This has to be 100 since we're using max value to determine 99th percentile.
constexpr size_t kLatencyDequeSize = 100;

class Latency {
public:
  // We use high measuring period to avoid bothering system with excessive syscalls.
  // With 100ms period, it takes 10 seconds to get trustworthy latency measurement.
  Latency(int64_t measure_period_ms = 100)
      : measure_period_ms_(measure_period_ms), state_(std::make_shared<_State>()) {}

  void tick(int64_t timestamp_ms) {
    if (state_->initialized) {
      if (abs(timestamp_ms - state_->last_timestamp_ms) >= measure_period_ms_) {
        auto latency_ms = maka_control_timestamp_ms() - timestamp_ms;
        std::lock_guard<std::mutex> lock(state_->state_mu_);
        while (state_->q_timestamp_ms.size() >= kLatencyDequeSize) {
          state_->q_timestamp_ms.pop_front();
        }
        state_->q_timestamp_ms.push_back(latency_ms);
        state_->last_timestamp_ms = timestamp_ms;
      }
    } else {
      auto latency_ms = maka_control_timestamp_ms() - timestamp_ms;
      std::lock_guard<std::mutex> lock(state_->state_mu_);
      state_->q_timestamp_ms.push_back(latency_ms);
      state_->last_timestamp_ms = timestamp_ms;
      state_->initialized = true;
    }
  }

  float get_mean_latency_ms() const {
    std::lock_guard<std::mutex> lock(state_->state_mu_);
    if (state_->q_timestamp_ms.empty()) {
      return 0.0;
    }
    return float(std::accumulate(state_->q_timestamp_ms.begin(), state_->q_timestamp_ms.end(), 0)) /
           (float)state_->q_timestamp_ms.size();
  }

  float get_99pct_latency_ms() const {
    std::lock_guard<std::mutex> lock(state_->state_mu_);
    if (state_->q_timestamp_ms.empty()) {
      return 0.0;
    }
    return float(*std::max_element(state_->q_timestamp_ms.begin(), state_->q_timestamp_ms.end()));
  }

private:
  struct _State {
    std::mutex state_mu_;
    int64_t last_timestamp_ms = 0;
    std::deque<int64_t> q_timestamp_ms;
    bool initialized = false;
  };
  int64_t measure_period_ms_;
  std::shared_ptr<_State> state_;
};

} // namespace runtime
} // namespace cv

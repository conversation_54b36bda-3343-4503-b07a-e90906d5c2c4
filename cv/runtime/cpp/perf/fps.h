#pragma once

#include <algorithm>
#include <deque>
#include <memory>
#include <mutex>
#include <numeric>

namespace cv {
namespace runtime {

// This has to be 100 since we're using max value to determine 99th percentile.
constexpr size_t kFpsDequeSize = 100;

class FPS {
public:
  FPS() : state_(std::make_shared<_State>()) {}

  void tick(int64_t timestamp_ms) {
    if (state_->last_timestamp_ms != 0) {
      auto latency_ms = timestamp_ms - state_->last_timestamp_ms;
      std::lock_guard<std::mutex> lock(state_->state_mu_);
      while (state_->q_timestamp_ms.size() >= kFpsDequeSize) {
        state_->q_timestamp_ms.pop_front();
      }
      state_->q_timestamp_ms.push_back(latency_ms);
    }
    state_->last_timestamp_ms = timestamp_ms;
  }

  float get_mean_fps() const {
    std::lock_guard<std::mutex> lock(state_->state_mu_);
    if (state_->q_timestamp_ms.empty()) {
      return 0.0;
    }
    return 1000.0f * (float)state_->q_timestamp_ms.size() /
           (float)std::accumulate(state_->q_timestamp_ms.begin(), state_->q_timestamp_ms.end(), 0);
  }

  float get_99pct_fps() const {
    std::lock_guard<std::mutex> lock(state_->state_mu_);
    if (state_->q_timestamp_ms.empty()) {
      return 0.0f;
    }
    return 1000.0f / (float)*std::max_element(state_->q_timestamp_ms.begin(), state_->q_timestamp_ms.end());
  }

private:
  struct _State {
    std::mutex state_mu_;
    int64_t last_timestamp_ms = 0;
    std::deque<int64_t> q_timestamp_ms;
  };
  std::shared_ptr<_State> state_;
};

} // namespace runtime
} // namespace cv

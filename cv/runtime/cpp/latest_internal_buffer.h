#pragma once

#include <condition_variable>
#include <mutex>
#include <optional>

namespace cv {
namespace runtime {

template <class T>
class LatestInternalBuffer {
private:
  std::shared_ptr<T> data_ = std::make_shared<T>();
  int64_t timestamp_ms_ = 0;
  std::mutex mutex_;
  std::condition_variable cv_;

public:
  void update(T data, int64_t ts) {
    std::unique_lock<std::mutex> lock(mutex_);
    *data_ = data;
    timestamp_ms_ = ts;

    cv_.notify_all();
  }

  std::optional<T> get_next(int64_t timestamp_ms, int64_t timeout_ms) {
    std::unique_lock<std::mutex> lock(mutex_);
    if (timestamp_ms < timestamp_ms_) {
      return *data_;
    }

    cv_.wait_for(lock, std::chrono::milliseconds(timeout_ms), [&] { return timestamp_ms < timestamp_ms_; });
    if (timestamp_ms < timestamp_ms_) {
      return *data_;
    }

    return {};
  }
};

} // namespace runtime
} // namespace cv
#pragma once

#include <optional>
#include <string>
#include <vector>

namespace cv {
namespace runtime {

struct Options {
  std::string config_file;
  std::optional<std::string> model_registry_file;
  bool dummy_publisher;
  bool skip_models;
  bool verbose;
  bool force_load_yaml;
  std::vector<std::string> sim_target_image;
  std::vector<std::string> sim_predict_image;
  std::vector<std::string> sim_drive_image;
};

Options parse_options(int argc, char **argv);

} // namespace runtime
} // namespace cv

#pragma once

#include "lib/drivers/basler/cpp/camera_factory.h"
#include "lib/drivers/mock_cam/cpp/camera_factory.h"
#include "lib/drivers/sim_cam/cpp/camera_factory.h"
#include "lib/drivers/thinklucid/cpp/camera_factory.h"
#include "lib/drivers/zedcam/client/cpp/camera_factory.h"

namespace cv {
namespace runtime {

using namespace lib::drivers::basler;
using namespace lib::drivers::thinklucid;
using namespace lib::drivers::zedcam;
using namespace lib::drivers::sim_cam;
using namespace lib::drivers::mock_cam;

class CameraRegistry {
public:
  std::vector<CameraInfo> list_devices();
  Camera create_device(const CameraInfo &info, const CameraSettings &settings,
                       std::shared_ptr<carbon::config::ConfigTree> camera_config);

private:
  // https://stackoverflow.com/a/9618553
  std::vector<std::unique_ptr<CameraFactory>> factories_ = []() {
    std::vector<std::unique_ptr<CameraFactory>> v;
    v.emplace_back(std::make_unique<BaslerCameraFactory>());
    v.emplace_back(std::make_unique<ThinkLucidCameraFactory>());
    v.emplace_back(std::make_unique<ZedCameraFactory>());
    v.emplace_back(std::make_unique<SimulatedCameraFactory>());
    v.emplace_back(std::make_unique<MockCameraFactory>());
    return v;
  }();
};

} // namespace runtime
} // namespace cv
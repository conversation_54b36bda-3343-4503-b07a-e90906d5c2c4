#include <prometheus/gauge.h>
#include <prometheus/registry.h>

#include "config/client/cpp/config_subscriber.hpp"
#include "cv/runtime/cpp/node_registry.h"

namespace cv {
namespace runtime {
class MetricsLogger {
public:
  MetricsLogger(std::shared_ptr<NodeRegistry> node_registry, std::shared_ptr<prometheus::Registry> registry);

  void update();

private:
  std::shared_ptr<NodeRegistry> node_registry_;
  std::shared_ptr<prometheus::Registry> registry_;
  std::map<std::string, std::reference_wrapper<prometheus::Gauge>> camera_id_to_fps_gauge_, camera_id_to_pct_fps_gauge_,
      camera_id_to_latency_gauge_, camera_id_to_pct_latency_gauge_, camera_id_to_deepweed_fps_gauge_,
      camera_id_to_deepweed_pct_fps_gauge_, camera_id_to_deepweed_latency_gauge_,
      camera_id_to_deepweed_pct_latency_gauge_, camera_id_to_p2p_fps_gauge_, camera_id_to_p2p_pct_fps_gauge_,
      camera_id_to_p2p_latency_gauge_, camera_id_to_p2p_pct_latency_gauge_, camera_id_to_exposure_time_gauge_,
      camera_id_to_deepweed_model_gauge_, camera_id_to_deepweed_wpt_gauge_, camera_id_to_deepweed_cpt_gauge_,
      camera_id_to_focus_metric_gauge_, camera_id_to_frequency_focus_metric_gauge_, camera_id_to_temperature_gauge_,
      camera_id_to_gain_db_gauge_;
  std::shared_ptr<std::reference_wrapper<prometheus::Family<prometheus::Gauge>>> deepweed_model_family_;
  std::shared_ptr<std::reference_wrapper<prometheus::Gauge>> deepweed_wpt_gauge_;
  std::shared_ptr<std::reference_wrapper<prometheus::Gauge>> deepweed_cpt_gauge_;
  std::shared_ptr<carbon::config::ConfigTree> deepweed_config_;
};
} // namespace runtime

} // namespace cv

#include "cv_http_client.h"
#include <fmt/format.h>
#include <fstream>
#include <iostream>
#include <spdlog/spdlog.h>

namespace cv::runtime {

using namespace std;
using namespace lib::common;

CVHttpClient::CVHttpClient() {
  char *role_ptr = std::getenv("MAKA_ROLE");
  std::string role = "bud";
  if (role_ptr != NULL) {
    role = role_ptr;
  }

  char *row_ptr = std::getenv("MAKA_ROW");
  std::string row = "1";
  if (row_ptr != NULL) {
    row = row_ptr;
  }

  char *container_ptr = std::getenv("HOSTNAME");
  std::string container = "cv";
  if (container_ptr != NULL) {
    container = container_ptr;
  }

  std::string addr = "";
  if (role == "row-primary") {
    addr = "**********:15055";
  } else if (role == "row-secondary") {
    addr = "**********:15055";
  } else if (role == "simulator_minicomputers") {
    if (container == "cv") {
      addr = "127.0.0.1:15056";
    } else if (container == "cv2") {
      addr = "127.0.0.1:15055";
    }
  }

  if (addr != "") {
    addr_ = addr;
    client_ = std::make_unique<httplib::Client>(addr);
    client_->set_keep_alive(true);
  }
}

CVHttpClient::CVHttpClient(const CVHttpClient &other) {
  addr_ = other.addr_;
  if (addr_ != "") {
    client_ = std::make_unique<httplib::Client>(addr_);
    client_->set_keep_alive(true);
  }
}

std::optional<camera::CameraImage> CVHttpClient::get(glm::vec2 predict_coords, int64_t predict_timestamp,
                                                     std::string predict_cam_id, glm::ivec2 size, float target_ppi) {
  if (client_ == nullptr) {
    return {};
  }

  std::string url = fmt::format(
      "/perspective?camera={}&ts={}&coords_x={}&coords_y={}&size_width={}&size_height={}&target_ppi={}", predict_cam_id,
      predict_timestamp, predict_coords[0], predict_coords[1], size[0], size[1], target_ppi);
  auto res = client_->Get(url);
  if (!res) {
    spdlog::warn("CVHttpClient: error {} in response to /perspective call for cam {}, ts {}",
                 httplib::to_string(res.error()), predict_cam_id, predict_timestamp);
    return {};
  } else if (res->status != 200) {
    spdlog::warn("CVHttpClient: status {} in response to /perspective call for cam {}, ts {}", res->status,
                 predict_cam_id, predict_timestamp);
    return {};
  }

  stringstream ss;
  ss << res->body;
  camera::CameraImage image;
  torch::load(image.image, ss);
  image.camera_id = predict_cam_id;
  image.timestamp_ms = predict_timestamp;
  auto header_it = res->headers.find("X-Carbon-PPI");
  if (header_it != res->headers.end()) {
    image.ppi = std::stof(header_it->second);
  }

  return image;
}

} // namespace cv::runtime

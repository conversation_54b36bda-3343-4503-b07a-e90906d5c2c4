#pragma once

#include <c10/cuda/CUDAGuard.h>
#include <c10/cuda/CUDAStream.h>
#include <fmt/format.h>

#include "cv/runtime/cpp/node.h"
#include "cv/runtime/cpp/nodes/shmem_sink_def.h"
#include "cv/runtime/cpp/shmem_image.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/shmem/cpp/buffer/image_buffer.hpp"
#include "lib/common/shmem/cpp/buffer/latest_buffer.hpp"
#include "lib/common/shmem/cpp/buffer/ref_buffer.hpp"

extern template class lib::common::shmem::ImageBuffer<uint8_t>;
extern template class lib::common::shmem::RefBuffer<lib::common::shmem::ImageBuffer<uint8_t>>;

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common::camera;
using namespace lib::common::shmem;

class RefImageShmemSink : public CVNodeImpl {
public:
  RefImageShmemSink(Camera camera, std::string buffer_name, bool stage_on_cpu, Input<CameraImage> input)
      : CVNodeImpl(fmt::format("/shmem_sink/{}", camera.get_info().camera_id)), input_(input),
        ref_buffer_(buffer_name, true), stage_on_cpu_(stage_on_cpu) {}

protected:
  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    CameraImage cam_image = input_.pop();
    set_state(CVNodeState::kPushing);
    ref_buffer_.create_buffer_and_export_ref([&](std::string name) { return create_image_buffer(name, cam_image); });
    return cam_image.timestamp_ms;
  }

  void close_connectors() override { input_.close(); }

private:
  std::unique_ptr<ImageBuffer<uint8_t>> create_image_buffer(std::string name, CameraImage cam_image) {
    auto height = cam_image.get_height();
    auto width = cam_image.get_width();
    auto image = cam_image.image;
    auto channels = 3;
    // If image contains depth, prepend it.
    if (cam_image.depth.defined()) {
      image = torch::cat({cam_image.depth.unsqueeze(0), image}, 0);
      channels = 4;
    }
    // Convert [D]RGB -> BGR[D], CHW -> HWC.
    image = image.flip(0).permute({1, 2, 0});
    if (stage_on_cpu_) {
      // This may help pre-Ampere GPUs which have limited memcpy engines. Copy to shared memory is slower compared to
      // plain CPU copy.
      with_device device_guard(image.device().index());
      image = image.to(torch::TensorOptions().pinned_memory(true).dtype(torch::kUInt8));
    }
    std::unique_ptr<ImageBuffer<uint8_t>> buf = std::make_unique<ImageBuffer<uint8_t>>(
        name, std::tuple<uint32_t, uint32_t, uint32_t>(height, width, channels), cam_image.timestamp_ms);
    uint8_t *address = buf->get_image_data_address();
    torch::Tensor shmem_image = torch::from_blob(address, {height, width, channels}, torch::kUInt8);
    shmem_image.copy_(image);
    return buf;
  }

  Input<CameraImage> input_;
  RefBuffer<ImageBuffer<uint8_t>> ref_buffer_;
  bool stage_on_cpu_;
};

class LatestImageShmemSink : public CVNodeImpl {
public:
  LatestImageShmemSink(Camera camera, std::string buffer_name, bool stage_on_cpu, Input<CameraImage> input)
      : CVNodeImpl(fmt::format("/latest_shmem_sink/{}", camera.get_info().camera_id)), input_(input),
        buffer_name_(buffer_name),
        shmem_buffer_(new LatestBuffer<ShmemImageOutput>(buffer_name, true, &copy_shmem_image_output)),
        stage_on_cpu_(stage_on_cpu) {}

  ~LatestImageShmemSink() { delete shmem_buffer_; }

protected:
  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    CameraImage cam_image = input_.pop();
    set_state(CVNodeState::kProcessing);

    auto height = cam_image.get_height();
    auto width = cam_image.get_width();
    auto image = cam_image.image;
    auto channels = 3;

    if (!cuda_stream_) {
      cuda_stream_.reset(
          new c10::cuda::CUDAStream(c10::cuda::getStreamFromPool(true, cam_image.image.device().index())));
    }

    c10::cuda::CUDAStreamGuard stream_guard(*cuda_stream_);

    // If image contains depth, prepend it.
    if (cam_image.depth.defined()) {
      image = torch::cat({cam_image.depth.unsqueeze(0), image}, 0);
      channels = 4;
    }

    // Convert [D]RGB -> BGR[D], CHW -> HWC.
    image = image.flip(0).permute({1, 2, 0});
    if (stage_on_cpu_) {
      // This may help pre-Ampere GPUs which have limited memcpy engines. Copy to shared memory is slower compared to
      // plain CPU copy.
      with_device device_guard(image.device().index());
      image = image.to(torch::TensorOptions().pinned_memory(true).dtype(torch::kUInt8));
    }

    try {
      set_state(CVNodeState::kPushing);
      shmem_buffer_->custom_update(
          [&](void *address, uint32_t size) {
            return update_buffer((uint32_t)width, (uint32_t)height, (uint32_t)channels, cam_image.timestamp_ms,
                                 cam_image.focus_metric.value_or(-1), image, address, size);
          },
          cam_image.timestamp_ms);
    } catch (...) {
      spdlog::error("Image Shmem Sink Exception");
      delete shmem_buffer_;
      shmem_buffer_ = new LatestBuffer<ShmemImageOutput>(buffer_name_, true, &copy_shmem_image_output);
    }

    cuda_stream_->synchronize();

    return cam_image.timestamp_ms;
  }

  void close_connectors() override { input_.close(); }

private:
  void update_buffer(uint32_t width, uint32_t height, uint32_t channels, int64_t timestamp_ms, float focus_metric,
                     torch::Tensor &image, void *address, uint32_t size) {
    (void)size;
    ShmemImageOutput *output = static_cast<ShmemImageOutput *>(address);

    output->width = width;
    output->height = height;
    output->channels = channels;
    output->timestamp_ms = timestamp_ms;
    output->focus_metric = focus_metric;

    torch::Tensor shmem_image = torch::from_blob(output->data.data(), {height, width, channels}, torch::kUInt8);
    shmem_image.copy_(image);
  }

  Input<CameraImage> input_;
  std::string buffer_name_;
  LatestBuffer<ShmemImageOutput> *shmem_buffer_;
  bool stage_on_cpu_;
  std::unique_ptr<c10::cuda::CUDAStream> cuda_stream_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv

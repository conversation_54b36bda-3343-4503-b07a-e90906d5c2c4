#pragma once

#include <c10/cuda/CUDAGuard.h>
#include <fmt/format.h>
#include <opencv2/core.hpp>
#include <opencv2/core/cuda.hpp>
#include <opencv2/core/cuda_stream_accessor.hpp>
#include <opencv2/cudaimgproc.hpp>
#include <opencv2/imgproc.hpp>

#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/cuda_util.h"

namespace cv {
namespace runtime {
namespace nodes {

using namespace torch::indexing;
using namespace lib::common::camera;

class Transformer : public CVNodeImpl {
public:
  Transformer(Camera camera, Input<CameraImage> input, bool transpose)
      : CVNodeImpl(fmt::format("/transformer/{}", camera.get_info().camera_id)), input_(input), transpose_(transpose) {}
  Output<CameraImage> &get_output() { return output_; }

protected:
  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    CameraImage cam_image = input_.pop();
    set_state(CVNodeState::kProcessing);

    c10::cuda::OptionalCUDAStreamGuard stream_guard;
    if (cam_image.get_device().type() == torch::kCUDA) {
      if (!cuda_stream_) {
        cuda_stream_.reset(
            new c10::cuda::CUDAStream(c10::cuda::getStreamFromPool(true, cam_image.get_device().index())));
      }
      stream_guard.reset_stream(*cuda_stream_);
    }
    if (transpose_) {
      cam_image = cam_image.transpose();
    }

    if (cam_image.pixel_format != PixelFormat::kRGB8) {
      auto height = cam_image.get_height();
      auto width = cam_image.get_width();
      cam_image = cam_image.contiguous();

      torch::Tensor converted_cam_image;
      // OpenCV has R and B mixed up in their Bayer specification compared to vendors.
      // https://docs.opencv.org/3.4/de/d25/imgproc_color_conversions.html
      if (cam_image.get_device().type() == torch::kCUDA) {
        int gpu_id = cam_image.get_device().index();
        with_device guard(gpu_id);
        cv::cuda::Stream cv_stream = cv::cuda::StreamAccessor::wrapStream(cuda_stream_->stream());
        cv::cuda::GpuMat image(height, width, CV_8UC1, cam_image.image.data_ptr());
        auto opts = torch::TensorOptions().dtype(torch::kUInt8).device(torch::kCUDA, gpu_id);
        converted_cam_image = torch::empty({height, width, 3}, opts);
        cv::cuda::GpuMat converted_image(height, width, CV_8UC3, converted_cam_image.data_ptr());

        switch (cam_image.pixel_format) {
        case PixelFormat::kBayerBG8:
          cv::cuda::cvtColor(image, converted_image, COLOR_BayerRG2RGB, 0, cv_stream);
          break;
        case PixelFormat::kBayerGB8:
          cv::cuda::cvtColor(image, converted_image, COLOR_BayerGR2RGB, 0, cv_stream);
          break;
        case PixelFormat::kBayerGR8:
          cv::cuda::cvtColor(image, converted_image, COLOR_BayerGB2RGB, 0, cv_stream);
          break;
        case PixelFormat::kBayerRG8:
          cv::cuda::cvtColor(image, converted_image, COLOR_BayerBG2RGB, 0, cv_stream);
          break;
        case PixelFormat::kMono8:
          cv::cuda::cvtColor(image, converted_image, COLOR_GRAY2RGB, 0, cv_stream);
          break;
        default:
          break;
        }
      } else {
        cv::Mat image(height, width, CV_8UC1, cam_image.image.data_ptr());
        converted_cam_image = torch::empty({height, width, 3}, torch::kUInt8);
        cv::Mat converted_image(height, width, CV_8UC3, converted_cam_image.data_ptr());

        switch (cam_image.pixel_format) {
        case PixelFormat::kBayerBG8:
          cv::cvtColor(image, converted_image, COLOR_BayerRG2RGB);
          break;
        case PixelFormat::kBayerGB8:
          cv::cvtColor(image, converted_image, COLOR_BayerGR2RGB);
          break;
        case PixelFormat::kBayerGR8:
          cv::cvtColor(image, converted_image, COLOR_BayerGB2RGB);
          break;
        case PixelFormat::kBayerRG8:
          cv::cvtColor(image, converted_image, COLOR_BayerBG2RGB);
          break;
        case PixelFormat::kMono8:
          cv::cvtColor(image, converted_image, COLOR_GRAY2RGB);
          break;
        default:
          break;
        }
      }

      cam_image.image = converted_cam_image.permute({2, 0, 1});
    }

    if (cuda_stream_) {
      cuda_stream_->synchronize();
    }

    set_state(CVNodeState::kPushing);
    output_.push(cam_image);
    return cam_image.timestamp_ms;
  }

  void close_connectors() override {
    input_.close();
    output_.close();
  }

private:
  Input<CameraImage> input_;
  Output<CameraImage> output_;
  bool transpose_;
  std::unique_ptr<c10::cuda::CUDAStream> cuda_stream_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv

#include "focus_metric.h"

#include <fmt/format.h>
#include <fmt/ostream.h>
#include <opencv2/core.hpp>
#include <opencv2/core/cuda.hpp>
#include <opencv2/core/cuda_stream_accessor.hpp>
#include <opencv2/cudaarithm.hpp>
#include <opencv2/imgproc.hpp>
#include <spdlog/spdlog.h>

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common::camera;

FocusMetric::FocusMetric(Camera cam, Input<CameraImage> input)
    : CVNodeImpl(fmt::format("/focus_metric/{}", cam.get_info().camera_id)), input_(input) {}

Output<CameraImage> &FocusMetric::get_output() { return output_; }

std::optional<float> FocusMetric::get_last_focus_metric() const { return last_focus_metric_.load(); }

int64_t FocusMetric::tick() {
  set_state(CVNodeState::kPulling);
  CameraImage cam_image = input_.pop();
  set_state(CVNodeState::kProcessing);

  if (!focus_metric_) {
    focus_metric_.reset(new lib::common::image::FocusMetric());
  }
  if (!cuda_stream_) {
    cuda_stream_.reset(new c10::cuda::CUDAStream(c10::cuda::getStreamFromPool(true, cam_image.get_device().index())));
  }
  c10::cuda::CUDAStreamGuard stream_guard(*cuda_stream_);

  cam_image.focus_metric = focus_metric_->compute(cam_image.image);
  last_focus_metric_ = cam_image.focus_metric;
  set_state(CVNodeState::kPushing);
  output_.push(cam_image);
  return cam_image.timestamp_ms;
}

void FocusMetric::close_connectors() { input_.close(); }

} // namespace nodes
} // namespace runtime
} // namespace cv

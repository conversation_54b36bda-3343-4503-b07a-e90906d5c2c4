#pragma once

#include <array>
#include <prometheus/family.h>
#include <prometheus/histogram.h>

namespace cv {
namespace runtime {
namespace nodes {

class DeepweedMetrics {
public:
  DeepweedMetrics(float score_bucket_increment, float size_bucket_increment, float embedding_bucket_increment,
                  float embedding_category_distance_bucket_increment, std::shared_ptr<prometheus::Registry> registry)
      : score_bucket_increment_(score_bucket_increment), size_bucket_increment_(size_bucket_increment),
        embedding_bucket_increment_(embedding_bucket_increment),
        embedding_category_distance_bucket_increment_(embedding_category_distance_bucket_increment) {

    for (float bucket = 0.0f; bucket < 1.0f; bucket += score_bucket_increment_) {
      score_buckets_.push_back(bucket);
    }
    for (float bucket = 0.0f; bucket < 50.0f; bucket += size_bucket_increment_) {
      size_buckets_.push_back(bucket);
    }
    // for (float bucket = 0.0f; bucket < 1.0f; bucket += embedding_bucket_increment_) {
    //   embedding_buckets_.push_back(bucket);
    // }
    for (float bucket = -1.0f; bucket < 1.0f; bucket += embedding_category_distance_bucket_increment_) {
      embedding_category_distance_buckets_.push_back(bucket);
    }

    auto &scores_family =
        prometheus::BuildCounter()
            .Name("deepweed_scores")
            .Help("A histogram capturing the distribution of weed and crop scores coming out of Deepweed.")
            .Register(*registry);

    auto &sizes_family =
        prometheus::BuildCounter()
            .Name("deepweed_sizes")
            .Help("A histogram capturing the distribution of weed and crop scores coming out of Deepweed.")
            .Register(*registry);

    // auto &embeddings_family =
    //     prometheus::BuildCounter()
    //         .Name("deepweed_embeddings")
    //         .Help("A histogram capturing the distribution of weed and crop 2D embeddings coming out of Deepweed.")
    //         .Register(*registry);

    embedding_category_distances_family_ = std::make_unique<
        std::reference_wrapper<prometheus::Family<prometheus::Counter>>>(
        prometheus::BuildCounter()
            .Name("deepweed_embedding_category_distances")
            .Help(
                "A histogram capturing the distribution of category distances based on the active category collection.")
            .Register(*registry));

    for (auto lower_bound : score_buckets_) {
      metrics_.emplace(fmt::format("crop_scores_{}", lower_bound),
                       scores_family.Add({{"detection_type", "crop"}, {"lower_bound", std::to_string(lower_bound)}}));
      metrics_.emplace(fmt::format("weed_scores_{}", lower_bound),
                       scores_family.Add({{"detection_type", "weed"}, {"lower_bound", std::to_string(lower_bound)}}));
      metrics_.emplace(fmt::format("plant_scores_{}", lower_bound),
                       scores_family.Add({{"detection_type", "plant"}, {"lower_bound", std::to_string(lower_bound)}}));
    }

    for (auto lower_bound : size_buckets_) {
      metrics_.emplace(fmt::format("crop_sizes_{}", lower_bound),
                       sizes_family.Add({{"detection_type", "crop"}, {"lower_bound", std::to_string(lower_bound)}}));
      metrics_.emplace(fmt::format("weed_sizes_{}", lower_bound),
                       sizes_family.Add({{"detection_type", "weed"}, {"lower_bound", std::to_string(lower_bound)}}));
      metrics_.emplace(fmt::format("plant_sizes_{}", lower_bound),
                       sizes_family.Add({{"detection_type", "plant"}, {"lower_bound", std::to_string(lower_bound)}}));
    }

    // for (auto lower_bound_x : embedding_buckets_) {
    //   for (auto lower_bound_y : embedding_buckets_) {
    //     std::string lower_bound_label = fmt::format("{:.2f}_{:.2f}", lower_bound_x, lower_bound_y);
    //     metrics_.emplace(fmt::format("crop_embeddings_{}", lower_bound_label),
    //                      embeddings_family.Add({{"detection_type", "crop"}, {"lower_bound", lower_bound_label}}));
    //     metrics_.emplace(fmt::format("weed_embeddings_{}", lower_bound_label),
    //                      embeddings_family.Add({{"detection_type", "weed"}, {"lower_bound", lower_bound_label}}));
    //     metrics_.emplace(fmt::format("plant_embeddings_{}", lower_bound_label),
    //                      embeddings_family.Add({{"detection_type", "plant"}, {"lower_bound", lower_bound_label}}));
    //   }
    // }
  }
  void log(const deepweed::DeepweedOutput &output) {
    for (const auto &detection : output.detections) {
      if (detection.score > 0) {
        std::string hit_class = "";
        if (detection.hit_class == proto::HitClass::WEED) {
          hit_class = "weed";
        } else if (detection.hit_class == proto::HitClass::CROP) {
          hit_class = "crop";
        } else if (detection.hit_class == proto::HitClass::PLANT) {
          hit_class = "plant";
        } else {
          continue;
        }
        increment_scores(detection.plant_score, detection.weed_score, detection.crop_score);
        increment_sizes(hit_class, detection.size);
        // increment_embeddings(hit_class, detection.reduced_scaled_embedding);
        increment_embedding_category_distances(detection.embedding_category_distances, output.embedding_categories);
      }
    }
  }

  void increment_embedding_category_distances(const std::vector<float> &distances,
                                              const std::vector<std::string> &embedding_categories) {
    for (size_t i = 0; i < distances.size(); i++) {
      for (auto lower_bound : embedding_category_distance_buckets_) {
        if (distances[i] < lower_bound + embedding_category_distance_bucket_increment_) {
          auto key = fmt::format("{}_distances_{}", embedding_categories[i], lower_bound);
          if (metrics_.count(key) == 0) {
            metrics_.emplace(
                key, embedding_category_distances_family_->get().Add(
                         {{"detection_type", embedding_categories[i]}, {"lower_bound", std::to_string(lower_bound)}}));
          }
          metrics_.at(key).get().Increment();
          break;
        }
      }
    }
  }

  void increment_scores(const float plant_score, const float weed_score, const float crop_score) {

    bool plant_bucket_found = false;
    bool weed_bucket_found = false;
    bool crop_bucket_found = false;

    for (auto lower_bound : score_buckets_) {
      if (plant_score <= lower_bound + score_bucket_increment_ && !plant_bucket_found) {
        metrics_.at(fmt::format("plant_scores_{}", lower_bound)).get().Increment();
        plant_bucket_found = true;
      }

      if (weed_score <= lower_bound + score_bucket_increment_ && !weed_bucket_found) {
        metrics_.at(fmt::format("weed_scores_{}", lower_bound)).get().Increment();
        weed_bucket_found = true;
      }

      if (crop_score <= lower_bound + score_bucket_increment_ && !crop_bucket_found) {
        metrics_.at(fmt::format("crop_scores_{}", lower_bound)).get().Increment();
        crop_bucket_found = true;
      }

      if (plant_bucket_found && weed_bucket_found && crop_bucket_found) {
        break;
      }
    }
  }

  void increment_sizes(std::string hit_class, const float size) {
    for (auto lower_bound : size_buckets_) {
      if (size < lower_bound + size_bucket_increment_) {
        metrics_.at(fmt::format("{}_sizes_{}", hit_class, lower_bound)).get().Increment();
        break;
      }
    }
  }

  void increment_embeddings(std::string hit_class, const std::vector<float> &embedding) {
    if (embedding.empty()) {
      return;
    }
    float lower_bound_x = -1;
    float lower_bound_y = -1;
    for (auto b : embedding_buckets_) {
      if (embedding[0] < b + embedding_bucket_increment_) {
        lower_bound_x = b;
        break;
      }
    }
    if (embedding[0] > embedding_buckets_.back() + embedding_bucket_increment_) {
      lower_bound_x = embedding_buckets_.back();
    }
    for (auto b : embedding_buckets_) {
      if (embedding[1] < b + embedding_bucket_increment_) {
        lower_bound_y = b;
        break;
      }
    }
    if (embedding[1] > embedding_buckets_.back() + embedding_bucket_increment_) {
      lower_bound_y = embedding_buckets_.back();
    }
    if (lower_bound_x != -1 && lower_bound_y != -1) {
      metrics_.at(fmt::format("{}_embeddings_{:.2f}_{:.2f}", hit_class, lower_bound_x, lower_bound_y))
          .get()
          .Increment();
    }
  }

private:
  std::map<std::string, std::reference_wrapper<prometheus::Counter>> metrics_;
  std::unique_ptr<std::reference_wrapper<prometheus::Family<prometheus::Counter>>> embedding_category_distances_family_;
  std::vector<float> score_buckets_;
  std::vector<float> size_buckets_;
  std::vector<float> embedding_buckets_;
  std::vector<float> embedding_category_distance_buckets_;
  float score_bucket_increment_;
  float size_bucket_increment_;
  float embedding_bucket_increment_;
  float embedding_category_distance_bucket_increment_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv

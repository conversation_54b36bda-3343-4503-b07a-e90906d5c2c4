#pragma once

#include "cv/runtime/cpp/metadata_writer.h"
#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/camera/cpp/camera_info.h"
#include "lib/common/image/cpp/async_image_io.h"

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

class BurstRecord : public CVNodeImpl {
public:
  BurstRecord(camera::Camera camera, Input<camera::CameraImage> input, std::shared_ptr<std::mutex> burst_record_mutex)
      : CVNodeImpl(fmt::format("/burst_record/{}", camera.get_info().camera_id)), input_(input), recording_(false),
        image_io_(kNumIOWorkerThreads),
        image_path_prefix_("/data/media/burst_record_{}/" + camera.get_info().camera_id),
        burst_record_mutex_(burst_record_mutex), cam_info_(camera.get_info()), machine_id_("unset"), row_id_("unset"),
        metadata_writer_(MetadataWriter()) {
    char *machine_id_pointer = std::getenv("MAKA_ROBOT_NAME");
    if (machine_id_pointer != NULL) {
      machine_id_ = machine_id_pointer;
    }
    char *row_id_pointer = std::getenv("MAKA_ROW");
    if (row_id_pointer != NULL) {
      row_id_ = row_id_pointer;
    }

    capture_method_ = "burst-target";

    if (camera.get_info().camera_id.find("predict") == 0) {
      capture_method_ = "burst-predict";
    }
  }

  ~BurstRecord() {
    std::unique_lock<std::mutex> lck(recording_mutex_);
    if (recording_) {
      burst_record_mutex_->unlock();
    }
  }

  void close_connectors() override { input_.close(); }

  template <typename T, typename D>
  bool start_recording_images(std::chrono::duration<T, D> duration_to_record, const std::string &path,
                              int downsample_factor) {
    std::unique_lock<std::mutex> lck(recording_mutex_);
    if (recording_) {
      return false;
    }
    if (!burst_record_mutex_->try_lock()) {
      return false;
    }
    end_record_time_ = std::chrono::system_clock::now() + duration_to_record;
    image_path_ = std::filesystem::path(path);
    recording_ = true;
    downsample_factor_ = downsample_factor;
    return true;
  }

  void stop_recording_images(std::optional<int64_t> last_frame_timestamp_ms) {
    std::unique_lock<std::mutex> lck(recording_mutex_);
    if (!recording_) {
      return;
    }
    end_record_time_ = std::chrono::system_clock::now();
    last_frame_timestamp_ms_ = last_frame_timestamp_ms;
  }

  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    camera::CameraImage image = input_.pop();
    set_state(CVNodeState::kProcessing);
    std::chrono::time_point<std::chrono::system_clock> end_record_time;
    {
      std::unique_lock<std::mutex> lck(recording_mutex_);
      if (!recording_) {
        return image.timestamp_ms;
      }
      end_record_time = end_record_time_;
    }

    auto now = std::chrono::system_clock::now();

    if (now >= end_record_time && (!last_frame_timestamp_ms_ || image.timestamp_ms >= last_frame_timestamp_ms_)) {

      for (size_t i = 0; i < image_cache_.size(); i++) {
        const std::string timestamp = iso8601_timestamp(
            std::chrono::time_point<std::chrono::system_clock>(std::chrono::milliseconds(image_cache_[i].timestamp_ms)),
            true, true);
        torch::Tensor cached_image = image_cache_[i].image;
        std::string image_output_filepath = fmt::format("{}/{}_{}_row{}_{}_{}", image_path_.string(), capture_method_,
                                                        machine_id_, row_id_, cam_info_.camera_id, timestamp);
        image_io_.save(fmt::format("{}.png", image_output_filepath), [=]() { return cached_image.permute({1, 2, 0}); });
      }

      for (size_t i = 0; i < gps_cache_.size(); i++) {
        const std::string timestamp = iso8601_timestamp(
            std::chrono::time_point<std::chrono::system_clock>(std::chrono::milliseconds(image_cache_[i].timestamp_ms)),
            true, true);

        torch::Tensor cached_image = image_cache_[i].image;

        auto [lla, ecef] = gps_cache_[i];
        std::string image_output_filepath = fmt::format("{}/{}_{}_row{}_{}_{}", image_path_.string(), capture_method_,
                                                        machine_id_, row_id_, cam_info_.camera_id, timestamp);
        metadata_writer_.write_metadata(image_output_filepath, lla, ecef, image_cache_[i].timestamp_ms,
                                        cam_info_.camera_id, capture_method_, cam_info_.ppi);
      }

      image_cache_.clear();
      gps_cache_.clear();

      std::unique_lock<std::mutex> lck(recording_mutex_);
      recording_ = false;
      last_frame_timestamp_ms_ = {};
      downsample_factor_ = 1;
      burst_record_mutex_->unlock();
      return image.timestamp_ms;
    }

    image.image = lib::common::interpolate(image.image.unsqueeze(0).to(torch::kFloat32),
                                           image.get_dimensions() / std::max(downsample_factor_, 1))
                      .squeeze(0)
                      .to(torch::kUInt8);

    image = image.cpu();
    image_cache_.push_back(image);

    gps_cache_.push_back(std::make_tuple(image.geo_lla_data, image.geo_ecef_data));

    return image.timestamp_ms;
  }

private:
  static const int kNumIOWorkerThreads = 5;

  Input<camera::CameraImage> input_;
  std::mutex recording_mutex_;
  std::chrono::time_point<std::chrono::system_clock> end_record_time_;
  std::optional<int64_t> last_frame_timestamp_ms_;
  bool recording_;
  std::vector<camera::CameraImage> image_cache_;
  std::vector<std::tuple<GeoLLAData, GeoECEFData>> gps_cache_;
  std::filesystem::path image_path_;
  image::AsyncImageIO image_io_;
  std::string image_path_prefix_;
  std::shared_ptr<std::mutex> burst_record_mutex_;
  CameraInfo cam_info_;
  std::string machine_id_;
  std::string row_id_;
  MetadataWriter metadata_writer_;
  std::string capture_method_;
  int downsample_factor_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv

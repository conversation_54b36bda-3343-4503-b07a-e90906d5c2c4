#pragma once

#include "hardware_manager/cpp/grpc_client.h"
#include "lib/common/cpp/geo_data.h"
#include "lib/common/cpp/time.h"
#include <chrono>

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

class GPSRetriever : public CVNodeImpl {
public:
  GPSRetriever(std::shared_ptr<GeoData> geo_data,
               std::shared_ptr<hardware_manager::HardwareManagerClient> hardware_manager_client)
      : CVNodeImpl("GPSRetriever"), geo_data_(geo_data), hardware_manager_client_(hardware_manager_client),
        connected_(true) {
    char *role_ptr = std::getenv("MAKA_ROLE");
    role_ = "unset";
    if (role_ptr != NULL) {
      role_ = role_ptr;
    }
  }

  int64_t tick() override {
    std::this_thread::sleep_for(std::chrono::seconds(1));
    if (role_ == "veselka_cv") {
      return 0;
    }

    set_state(CVNodeState::kProcessing);

    try {
      auto geo = hardware_manager_client_->get_gps_data();
      auto geo_lla = geo.lla();
      auto geo_ecef = geo.ecef();

      if (!connected_) {
        spdlog::info("GPS Retriever connected to hardware manager");
        connected_ = true;
      }

      geo_data_->update(GeoLLAData(geo_lla.lat(), geo_lla.lng(), geo_lla.alt(), geo_lla.timestamp_ms()),
                        GeoECEFData(geo_ecef.x(), geo_ecef.y(), geo_ecef.z(), geo_ecef.timestamp_ms()));
    } catch (std::exception &e) {
      if (connected_) {
        spdlog::warn("GPS Retriever disconnected from hardware manager: {}", e.what());
      }
      connected_ = false;
      return 0;
    }
    return maka_control_timestamp_ms();
  }

private:
  std::shared_ptr<GeoData> geo_data_;
  std::shared_ptr<hardware_manager::HardwareManagerClient> hardware_manager_client_;
  std::string role_;
  bool connected_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
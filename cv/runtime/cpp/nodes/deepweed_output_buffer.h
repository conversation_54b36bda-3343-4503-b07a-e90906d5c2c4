#include "cv/runtime/cpp/deepweed_output_protobuf.h"
#include "cv/runtime/cpp/node.h"
#include "cv/runtime/proto/cv_runtime.grpc.pb.h"
#include "lib/common/cpp/fixed_buffer.h"

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

class DeepweedOutputBuffer : public CVNodeImpl {
public:
  DeepweedOutputBuffer(camera::Camera camera, Input<deepweed::DeepweedOutput> input, int max_items)
      : CVNodeImpl(fmt::format("/deepweed_output_buffer/{}", camera.get_info().camera_id)), input_(input),
        buffer_((size_t)max_items) {}

  bool get_output_by_ts(int64_t ts, proto::DeepweedOutput *response) {
    if (!buffer_.contains(ts)) {
      return false;
    }

    auto output = buffer_.get(ts);

    DeepweedOutputToProtobuf::to_protobuf(output.get(), response);

    return true;
  }

protected:
  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    auto deepweed_output = input_.pop();
    set_state(CVNodeState::kProcessing);

    if (!buffer_.contains(deepweed_output.timestamp_ms)) {
      buffer_.push(deepweed_output.timestamp_ms, std::make_shared<deepweed::DeepweedOutput>(deepweed_output));
    }
    return deepweed_output.timestamp_ms;
  }

private:
  Input<deepweed::DeepweedOutput> input_;
  FixedBuffer<int64_t, std::shared_ptr<deepweed::DeepweedOutput>> buffer_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
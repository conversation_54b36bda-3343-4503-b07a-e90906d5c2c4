#pragma once

#include "hardware_manager/cpp/grpc_client.h"
#include "lib/common/cpp/distance_status.h"
#include "lib/common/cpp/time.h"
#include "wheel_encoder/cpp/wheel_encoder.hpp"
#include <chrono>
#include <cmath>

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

class DistanceStatusRetriever : public CVNodeImpl {
public:
  DistanceStatusRetriever(std::shared_ptr<DistanceStatus> velocity_status,
                          std::shared_ptr<hardware_manager::HardwareManagerClient> hardware_manager_client)
      : CVNodeImpl("DistanceStatusRetriever"), distance_status_(velocity_status),
        hardware_manager_client_(hardware_manager_client), last_distance_timestamp_ms_(0), connected_(true) {
    char *role_ptr = std::getenv("MAKA_ROLE");
    role_ = "unset";
    if (role_ptr != NULL) {
      role_ = role_ptr;
    }
  }

  int64_t tick() override {
    if (role_ == "veselka_cv" || role_ == "unset") {
      return 0;
    }
    set_state(CVNodeState::kProcessing);

    try {
      // grab total travelled distance from the board
      auto time_and_dist_m = carbon::wheel_encoder::WheelEncoder::get()->get_next_pos(last_distance_timestamp_ms_);
      if (!connected_) {
        spdlog::info("Distance Status Retriever connected to wheel encoder board");
        connected_ = true;
      }

      last_distance_timestamp_ms_ = std::get<0>(time_and_dist_m);
      auto distance_travelled_mm = std::get<1>(time_and_dist_m) * 1000.0; // m to mm
      distance_status_->update(distance_travelled_mm);
    } catch (const std::exception &e) {
      if (connected_) {
        spdlog::warn("Distance Status Retriever disconnected from wheel encoder board: {}", e.what());
      }
      connected_ = false;
      return 0;
    }
    return maka_control_timestamp_ms();
  }

private:
  std::shared_ptr<DistanceStatus> distance_status_;
  std::shared_ptr<hardware_manager::HardwareManagerClient> hardware_manager_client_;
  std::string role_;
  int64_t last_distance_timestamp_ms_;
  bool connected_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv

#pragma once

#include "config/tree/cpp/config_tree.hpp"
#include "cv/p2p/output.h"
#include "cv/runtime/cpp/node.h"
#include "cv/runtime/cpp/nodes/buffer.h"
#include "cv/runtime/cpp/prediction_writer.h"
#include "cv/runtime/proto/cv_runtime.pb.h"
#include "lib/common/camera/cpp/camera.h"
#include <filesystem>
#include <google/protobuf/util/json_util.h>

namespace cv {
namespace runtime {
namespace nodes {
namespace p2p {

using namespace lib::common;

class P2PBurstRecord : public CVNodeImpl {
public:
  P2PBurstRecord(
      camera::Camera camera, Input<cv::p2p::P2POutput> input,
      Input<cv::p2p::P2POutputForBurstRecord> p2p_output_for_burst_record,
      std::shared_ptr<std::map<std::string, std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>>>> camera_buffers,
      std::shared_ptr<carbon::config::ConfigTree> save_predict_images_config_node)
      : CVNodeImpl(fmt::format("/p2p_burst_record/{}", camera.get_info().camera_id)), input_(input),
        p2p_output_for_burst_record_(p2p_output_for_burst_record), recording_(false),
        camera_id_(camera.get_info().camera_id), camera_buffers_(camera_buffers), machine_id_("unset"),
        row_id_("unset"), save_predict_images_config_node_(save_predict_images_config_node),
        metadata_writer_(MetadataWriter()) {
    char *machine_id_pointer = std::getenv("MAKA_ROBOT_NAME");
    if (machine_id_pointer != NULL) {
      machine_id_ = machine_id_pointer;
    }
    char *row_id_pointer = std::getenv("MAKA_ROW");
    if (row_id_pointer != NULL) {
      row_id_ = row_id_pointer;
    }
  }

  template <typename T, typename D>
  bool start_recording(std::chrono::duration<T, D> duration_to_record, const std::string &path,
                       bool dont_capture_predict_image) {
    std::unique_lock<std::mutex> lck(recording_mutex_);
    if (recording_) {
      return false;
    }
    end_record_time_ = std::chrono::system_clock::now() + duration_to_record;
    p2p_output_path_ = std::filesystem::path(path);
    dont_capture_predict_image_ = dont_capture_predict_image;
    recording_ = true;
    save_predict_ = true;
    return true;
  }

  void stop_recording(std::optional<int64_t> last_frame_timestamp_ms) {
    std::unique_lock<std::mutex> lck(recording_mutex_);
    if (!recording_) {
      return;
    }
    end_record_time_ = std::chrono::system_clock::now();
    last_frame_timestamp_ms_ = last_frame_timestamp_ms;
  }

  void close_connectors() override { input_.close(); }

  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    cv::p2p::P2POutput p2p_output = input_.pop();
    cv::p2p::P2POutputForBurstRecord p2p_output_for_burst_record = p2p_output_for_burst_record_.pop();
    set_state(CVNodeState::kProcessing);
    std::chrono::time_point<std::chrono::system_clock> end_record_time;
    bool dont_capture_predict_image;
    {
      std::unique_lock<std::mutex> lck(recording_mutex_);
      if (!recording_) {
        return p2p_output.target_timestamp_ms;
      }
      end_record_time = end_record_time_;
      dont_capture_predict_image = dont_capture_predict_image_;
    }

    auto now = std::chrono::system_clock::now();
    if (now >= end_record_time &&
        (!last_frame_timestamp_ms_ || p2p_output.target_timestamp_ms >= last_frame_timestamp_ms_)) {
      if (predict_image_ && save_predict_images_config_node_->get_value<bool>() && !dont_capture_predict_image) {

        auto lla = predict_image_->geo_lla_data;
        auto ecef = predict_image_->geo_ecef_data;
        float ppi = 0.0;
        if ((predict_image_->ppi).has_value()) {
          ppi = (predict_image_->ppi).value();
        }
        auto iso_timestamp = iso8601_timestamp(std::chrono::time_point<std::chrono::system_clock>(
                                                   std::chrono::milliseconds(p2p_output.predict_timestamp_ms)),
                                               true, true);

        // Set save path for predict image, metadata, and deepweed predictions
        std::string predict_save_path = fmt::format("{}/burst-predict_{}_row{}_{}_{}", p2p_output_path_.string(),
                                                    machine_id_, row_id_, predict_image_->camera_id, iso_timestamp);

        // Save predict image
        if (predict_image_.has_value()) {
          auto local_predict = predict_image_;
          image_io_.save(fmt::format("{}.png", predict_save_path), [=]() {
            return local_predict.value().image.cpu().permute({1, 2, 0});
          });
        }

        // Save predict image metadata
        metadata_writer_.write_metadata(predict_save_path, lla, ecef, p2p_output.predict_timestamp_ms,
                                        predict_image_->camera_id, "burst-predict", ppi);

        // Save deepweed predictions for predict image
        cv::runtime::PredictionWriter::write_deepweed_prediction(predict_save_path, predict_coord_);
      }
      for (size_t i = 0; i < p2p_output_cache_.size(); i++) {
        const std::string timestamp =
            iso8601_timestamp(std::chrono::time_point<std::chrono::system_clock>(
                                  std::chrono::milliseconds(p2p_output_cache_[i].target_timestamp_ms)),
                              true, true);
        std::string p2p_metadata_output_filepath =
            fmt::format("{}/burst-target_{}_row{}_{}_{}.p2p", p2p_output_path_.string(), machine_id_, row_id_,
                        camera_id_, timestamp);
        std::ofstream out_file(p2p_metadata_output_filepath);
        proto::P2POutputProto p2p_output_proto;
        p2p_output_proto.set_matched(p2p_output_cache_[i].matched);
        p2p_output_proto.set_target_coord_x(p2p_output_cache_[i].target_coord_x);
        p2p_output_proto.set_target_coord_y(p2p_output_cache_[i].target_coord_y);
        p2p_output_proto.set_target_timestamp_ms(p2p_output_cache_[i].target_timestamp_ms);
        p2p_output_proto.set_predict_timestamp_ms(p2p_output_cache_[i].predict_timestamp_ms);

        std::string p2p_output_json;
        google::protobuf::util::JsonPrintOptions print_options;
        print_options.always_print_primitive_fields = true;
        google::protobuf::util::MessageToJsonString(p2p_output_proto, &p2p_output_json, print_options);
        out_file << p2p_output_json;
      }
      p2p_output_cache_.clear();

      std::unique_lock<std::mutex> lck(recording_mutex_);
      recording_ = false;
      predict_image_ = {};
      last_frame_timestamp_ms_ = {};
      return p2p_output.target_timestamp_ms;
    }

    p2p_output_cache_.push_back(p2p_output);
    if (save_predict_ && save_predict_images_config_node_->get_value<bool>()) {
      if (camera_buffers_->count(p2p_output_for_burst_record.predict_buffer_name) != 0) {
        auto predict_buffer = camera_buffers_->at(p2p_output_for_burst_record.predict_buffer_name);
        auto predict_image_opt = predict_buffer->get_opt(p2p_output.predict_timestamp_ms);
        if (predict_image_opt.has_value()) {
          predict_image_ = *predict_image_opt;
          predict_coord_ = p2p_output_for_burst_record.predict_coord;
        }
      }
      save_predict_ = false;
    }
    return p2p_output.target_timestamp_ms;
  }

private:
  Input<cv::p2p::P2POutput> input_;
  Input<cv::p2p::P2POutputForBurstRecord> p2p_output_for_burst_record_;
  std::mutex recording_mutex_;
  std::chrono::time_point<std::chrono::system_clock> end_record_time_;
  std::optional<int64_t> last_frame_timestamp_ms_;
  bool dont_capture_predict_image_;
  bool recording_;
  bool save_predict_;
  std::vector<cv::p2p::P2POutput> p2p_output_cache_;
  std::filesystem::path p2p_output_path_;
  std::string camera_id_;
  std::shared_ptr<std::map<std::string, std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>>>> camera_buffers_;
  image::AsyncImageIO image_io_;
  std::string machine_id_;
  std::string row_id_;
  std::optional<camera::CameraImage> predict_image_;
  std::shared_ptr<carbon::config::ConfigTree> save_predict_images_config_node_;
  glm::ivec2 predict_coord_;
  MetadataWriter metadata_writer_;

  const int kCropRadius = 400;
};

} // namespace p2p
} // namespace nodes
} // namespace runtime
} // namespace cv

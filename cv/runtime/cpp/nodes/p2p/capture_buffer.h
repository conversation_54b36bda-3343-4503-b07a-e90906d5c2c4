#pragma once

#include <atomic>
#include <chrono>
#include <condition_variable>
#include <filesystem>
#include <mutex>
#include <thread>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "cv/p2p/output.h"
#include "cv/runtime/cpp/cv_http_client.h"
#include "cv/runtime/cpp/image_score_queue.h"
#include "cv/runtime/cpp/metadata_writer.h"
#include "cv/runtime/cpp/node.h"
#include "cv/runtime/cpp/nodes/buffer.h"
#include "cv/runtime/cpp/prediction_writer.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/image/cpp/async_image_io.h"

namespace cv {
namespace runtime {
namespace nodes {
namespace p2p {

class P2PCaptureBuffer : public CVNodeImpl {
public:
  static std::filesystem::path capture_path();
  using FixedCamBuffer = std::shared_ptr<FixedBuffer<int64_t, lib::common::camera::CameraImage>>;
  using FixedCamBufferMap = std::shared_ptr<std::map<std::string, FixedCamBuffer>>;
  P2PCaptureBuffer(lib::common::camera::Camera camera, Input<cv::p2p::P2POutput> input,
                   Input<cv::p2p::P2POutputForBurstRecord> p2p_output_for_burst_record,
                   FixedCamBufferMap dist_camera_buffers, FixedCamBuffer target_img_buffer, size_t capacity,
                   uint32_t max_capture_age,
                   const std::unordered_map<int, std::shared_ptr<ScoreQueue<P2PScoreQueueObject>>> &p2p_queues,
                   std::unordered_map<std::string, cv::runtime::CVHttpClient> &http_clients);
  ~P2PCaptureBuffer();

  void capture_p2p(const std::string &name, int64_t p2p_ts, bool write_to_disk, int reason);
  void burst_capture(const std::string &path, int64_t p2p_ts_start, int64_t p2p_ts_end, bool save_predict,
                     std::optional<std::string> predict_path_opt = std::nullopt, bool save_predict_metadata = false,
                     std::optional<proto::P2PBufferingBurstPredictMetadata> predict_metadata = std::nullopt);
  void close_connectors() override { input_.close(); }
  int64_t tick() override;
  static std::string build_name(const std::string &cam_id) { return fmt::format("/p2p_capture_buffer/{}", cam_id); }

private:
  static const int kNumIOWorkerThreads = 5;
  struct P2PCaptureData {
    cv::p2p::P2POutput p2p_output;
    cv::p2p::P2POutputForBurstRecord record_output;
    P2PCaptureData(const cv::p2p::P2POutput &_p2p_output, const cv::p2p::P2POutputForBurstRecord &_record_output)
        : p2p_output(_p2p_output), record_output(_record_output) {}
    P2PCaptureData(const P2PCaptureData &rhs) : p2p_output(rhs.p2p_output), record_output(rhs.record_output) {}
  };
  Input<cv::p2p::P2POutput> input_;
  Input<cv::p2p::P2POutputForBurstRecord> p2p_output_for_burst_record_;
  lib::common::camera::CameraInfo cam_info_;
  FixedCamBufferMap dist_camera_buffers_;
  FixedCamBuffer target_img_buffer_;
  FixedBuffer<int64_t, P2PCaptureData> p2p_cache_;
  lib::common::image::AsyncImageIO image_io_;
  std::string machine_id_;
  std::string row_id_;
  MetadataWriter metadata_writer_;
  std::chrono::hours max_capture_age_;
  std::mutex mut_;
  std::condition_variable cv_;
  std::atomic<bool> shutdown_;

  std::thread thread_;
  const std::unordered_map<int, std::shared_ptr<ScoreQueue<P2PScoreQueueObject>>> &p2p_queues_;
  std::unordered_map<std::string, cv::runtime::CVHttpClient> cv_http_clients_; // predict_id -> http_client

  void p2p_burst_capture(const std::string &path, const std::vector<P2PCaptureData> &p2p_data, int64_t min_ts,
                         int64_t max_ts, bool save_predict, std::optional<std::string> predict_path = std::nullopt,
                         bool save_predict_metadata = false,
                         std::optional<proto::P2PBufferingBurstPredictMetadata> predict_metadata = std::nullopt);

  std::tuple<torch::Tensor, glm::vec2, std::optional<lib::common::camera::CameraImage>>
  fetch_predict_image(const cv::p2p::P2POutputForBurstRecord &output_data, int64_t timestamp_ms, const glm::vec2 &dims,
                      float target_ppi);
  void capture_p2p_data(const std::filesystem::path &session_prefix, const P2PCaptureData &p2p_data, bool write_to_disk,
                        int reason);
  void cleanup_loop();
};

} // namespace p2p
} // namespace nodes
} // namespace runtime
} // namespace cv

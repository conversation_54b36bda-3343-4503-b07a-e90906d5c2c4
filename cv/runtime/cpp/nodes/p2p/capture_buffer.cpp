#include "capture_buffer.h"
#include "infer.h"

#include "cv/p2p/p2p_utils.h"
#include "cv/runtime/proto/cv_runtime.pb.h"
#include "frontend/proto/weeding_diagnostics.pb.h"

#include <google/protobuf/util/json_util.h>
#include <spdlog/spdlog.h>

namespace cv::runtime::nodes::p2p {
std::filesystem::path P2PCaptureBuffer::capture_path() {
  std::string data_dir("/data");
  char *data_dir_ptr = std::getenv("MAKA_DATA_DIR");
  if (data_dir_ptr != NULL) {
    data_dir = data_dir_ptr;
  }
  return std::filesystem::path(data_dir) / "media/requested_p2p_captures";
}
P2PCaptureBuffer::P2PCaptureBuffer(
    lib::common::camera::Camera camera, Input<cv::p2p::P2POutput> input,
    Input<cv::p2p::P2POutputForBurstRecord> p2p_output_for_burst_record, FixedCamBufferMap dist_camera_buffers,
    FixedCamBuffer target_img_buffer, size_t capacity, uint32_t max_capture_age,
    const std::unordered_map<int, std::shared_ptr<ScoreQueue<P2PScoreQueueObject>>> &p2p_queues,
    std::unordered_map<std::string, cv::runtime::CVHttpClient> &http_clients)
    : CVNodeImpl(P2PCaptureBuffer::build_name(camera.get_info().camera_id)), input_(input),
      p2p_output_for_burst_record_(p2p_output_for_burst_record), cam_info_(camera.get_info()),
      dist_camera_buffers_(dist_camera_buffers), target_img_buffer_(target_img_buffer), p2p_cache_(capacity),
      image_io_(kNumIOWorkerThreads), machine_id_("unset"), row_id_("unset"), metadata_writer_(MetadataWriter()),
      max_capture_age_(max_capture_age), shutdown_(false), thread_(&P2PCaptureBuffer::cleanup_loop, this),
      p2p_queues_(p2p_queues), cv_http_clients_(http_clients) {
  char *machine_id_pointer = std::getenv("MAKA_ROBOT_NAME");
  if (machine_id_pointer != NULL) {
    machine_id_ = machine_id_pointer;
  }
  char *row_id_pointer = std::getenv("MAKA_ROW");
  if (row_id_pointer != NULL) {
    row_id_ = row_id_pointer;
  }
}

P2PCaptureBuffer::~P2PCaptureBuffer() {
  shutdown_ = true;
  cv_.notify_all();
  thread_.join();
}

void P2PCaptureBuffer::capture_p2p(const std::string &name, int64_t p2p_ts, bool write_to_disk, int reason) {
  if (p2p_cache_.contains(p2p_ts)) {
    auto p2p_data = p2p_cache_.get(p2p_ts);
    capture_p2p_data(capture_path() / name, p2p_data, write_to_disk, reason);
  }
}
void P2PCaptureBuffer::burst_capture(const std::string &path, int64_t p2p_ts_start, int64_t p2p_ts_end,
                                     bool save_predict, std::optional<std::string> predict_path_opt,
                                     bool save_predict_metadata,
                                     std::optional<proto::P2PBufferingBurstPredictMetadata> predict_metadata) {
  std::vector<P2PCaptureData> p2p_data;
  p2p_cache_.apply([&](const int64_t &ts, const P2PCaptureData &data) {
    if (ts >= p2p_ts_start && ts <= p2p_ts_end) {
      p2p_data.emplace_back(data);
    }
  });

  p2p_burst_capture(path, p2p_data, p2p_ts_start, p2p_ts_end, save_predict, predict_path_opt, save_predict_metadata,
                    predict_metadata);
}

int64_t P2PCaptureBuffer::tick() {
  set_state(CVNodeState::kPulling);
  cv::p2p::P2POutput p2p_output = input_.pop();
  cv::p2p::P2POutputForBurstRecord p2p_output_for_burst_record = p2p_output_for_burst_record_.pop();
  set_state(CVNodeState::kProcessing);
  p2p_cache_.push_ignore_dup(p2p_output.target_timestamp_ms, P2PCaptureData(p2p_output, p2p_output_for_burst_record));
  return p2p_output.target_timestamp_ms;
}

void P2PCaptureBuffer::p2p_burst_capture(const std::string &path, const std::vector<P2PCaptureData> &p2p_data,
                                         int64_t min_ts, int64_t max_ts, bool save_predict,
                                         std::optional<std::string> predict_path, bool save_predict_metadata,
                                         std::optional<proto::P2PBufferingBurstPredictMetadata> predict_metadata) {
  if (p2p_data.empty()) {
    return;
  }
  for (const auto &data : p2p_data) {
    if (data.p2p_output.target_timestamp_ms > max_ts) {
      max_ts = data.p2p_output.target_timestamp_ms;
    }
    if (data.p2p_output.target_timestamp_ms < min_ts) {
      min_ts = data.p2p_output.target_timestamp_ms;
    }
  }
  std::vector<lib::common::camera::CameraImage> images;
  target_img_buffer_->apply([&](const int64_t &ts, const lib::common::camera::CameraImage &img) {
    if (ts >= min_ts && ts <= max_ts) {
      images.emplace_back(img);
    }
  });
  float ppi = 0.0f;
  for (auto &img : images) {
    if (img.ppi && ppi == 0.0f) {
      ppi = img.ppi.value();
    }
    img = img.cpu();
    const std::string timestamp = iso8601_timestamp(
        std::chrono::time_point<std::chrono::system_clock>(std::chrono::milliseconds(img.timestamp_ms)), true, true);
    torch::Tensor cached_image = img.image;

    std::string image_output_filepath =
        fmt::format("{}/burst-target_{}_row{}_{}_{}", path, machine_id_, row_id_, cam_info_.camera_id, timestamp);
    image_io_.save(fmt::format("{}.png", image_output_filepath), [=]() { return cached_image.permute({1, 2, 0}); });
    metadata_writer_.write_metadata(image_output_filepath, img.geo_lla_data, img.geo_ecef_data, img.timestamp_ms,
                                    cam_info_.camera_id, "burst-target", cam_info_.ppi);
  }
  if (save_predict && ppi > 0.0f && !images.empty()) {
    std::string predict_image_path = path;
    if (predict_path.has_value())
      predict_image_path = predict_path.value();

    glm::vec2 dims(images.front().get_dimensions());
    std::unordered_map<int64_t, cv::p2p::P2POutputForBurstRecord> predict_img_details;
    for (const auto &data : p2p_data) {
      predict_img_details.emplace(data.p2p_output.predict_timestamp_ms,
                                  data.record_output); // get unique predict images
    }
    for (auto &kv : predict_img_details) {
      auto [predict_img, new_predict_coords, _] = fetch_predict_image(kv.second, kv.first, dims, ppi);
      if (!predict_img.defined()) {
        return;
      }
      const std::string timestamp = iso8601_timestamp(
          std::chrono::time_point<std::chrono::system_clock>(std::chrono::milliseconds(kv.first)), true, true);
      std::string image_output_filepath = fmt::format("{}/burst-predict_{}_row{}_{}_{}", predict_image_path,
                                                      machine_id_, row_id_, kv.second.predict_buffer_name, timestamp);
      image_io_.save(fmt::format("{}.png", image_output_filepath), [=]() {
        return predict_img.squeeze(0).permute({1, 2, 0}).to(torch::kUInt8);
      });

      if (save_predict_metadata) {
        carbon::frontend::weeding_diagnostics::TrajectoryPredictImageMetadata metadata;
        metadata.set_center_x_px((int32_t)new_predict_coords.x);
        metadata.set_center_y_px((int32_t)new_predict_coords.y);
        if (predict_metadata.has_value()) {
          metadata.set_radius_px((int32_t)predict_metadata.value().plant_size_px());
        } else {
          metadata.set_radius_px(0);
        }
        metadata.set_pcam_id(kv.second.predict_buffer_name);
        metadata.mutable_ts()->set_timestamp_ms(kv.first);
        auto image_meta_file_name = fmt::format("{}.meta.json", image_output_filepath);
        std::filesystem::create_directories(std::filesystem::path(image_meta_file_name).parent_path());
        std::ofstream img_meta_file(image_meta_file_name);
        std::string meta_json;
        google::protobuf::util::JsonPrintOptions print_options;
        print_options.always_print_primitive_fields = true;
        auto status = google::protobuf::util::MessageToJsonString(metadata, &meta_json, print_options);
        img_meta_file << meta_json;
        img_meta_file.close();
      }
    }
  }

  google::protobuf::util::JsonPrintOptions print_options;
  print_options.always_print_primitive_fields = true;
  for (auto &data : p2p_data) {
    const std::string timestamp = iso8601_timestamp(std::chrono::time_point<std::chrono::system_clock>(
                                                        std::chrono::milliseconds(data.p2p_output.target_timestamp_ms)),
                                                    true, true);
    std::string p2p_metadata_output_filepath =
        fmt::format("{}/burst-target_{}_row{}_{}_{}.p2p", path, machine_id_, row_id_, cam_info_.camera_id, timestamp);
    std::ofstream out_file(p2p_metadata_output_filepath);
    proto::P2POutputProto p2p_output_proto;
    p2p_output_proto.set_matched(data.p2p_output.matched);
    p2p_output_proto.set_target_coord_x(data.p2p_output.target_coord_x);
    p2p_output_proto.set_target_coord_y(data.p2p_output.target_coord_y);
    p2p_output_proto.set_target_timestamp_ms(data.p2p_output.target_timestamp_ms);
    p2p_output_proto.set_predict_timestamp_ms(data.p2p_output.predict_timestamp_ms);

    std::string p2p_output_json;
    google::protobuf::util::MessageToJsonString(p2p_output_proto, &p2p_output_json, print_options);
    out_file << p2p_output_json;
  }
}

std::tuple<torch::Tensor, glm::vec2, std::optional<lib::common::camera::CameraImage>>
P2PCaptureBuffer::fetch_predict_image(const cv::p2p::P2POutputForBurstRecord &output_data, int64_t timestamp_ms,
                                      const glm::vec2 &dims, float target_ppi) {
  FixedBuffer<int64_t, lib::common::camera::CameraImage> *predict_cam_buffer = nullptr;

  bool img_found = false;
  if (dist_camera_buffers_->count(output_data.predict_buffer_name) != 0) {
    predict_cam_buffer = dist_camera_buffers_->at(output_data.predict_buffer_name).get();
    if (predict_cam_buffer->contains(timestamp_ms)) {
      img_found = true;
    }
  }

  if (!img_found) {
    glm::vec2 new_coord;
    return std::make_tuple(torch::Tensor(), new_coord, std::nullopt);
  }
  auto predict_image = predict_cam_buffer->get(timestamp_ms);
  // Pad and crop predict image to target image size in inches centered around predict coordinates
  glm::ivec2 predict_half_dimensions = glm::round(dims / target_ppi * predict_image.ppi.value() / 2.0f);
  auto [predict_crop, new_coords] = cv::utils::crop_or_pad(predict_image.image.unsqueeze(0).to(torch::kFloat32),
                                                           output_data.predict_coord, predict_half_dimensions * 2);
  return std::make_tuple(predict_crop.cpu(), new_coords, predict_image);
}

void P2PCaptureBuffer::capture_p2p_data(const std::filesystem::path &session_prefix, const P2PCaptureData &p2p_data,
                                        bool write_to_disk, int reason) {
  const std::string timestamp = iso8601_timestamp(true);

  std::string prefix = session_prefix / fmt::format("{}_{}", cam_info_.camera_id, timestamp);

  if (!target_img_buffer_->contains(p2p_data.p2p_output.target_timestamp_ms)) {
    return;
  }
  if (write_to_disk) {
    std::filesystem::create_directories(session_prefix);
  }
  try {
    auto target_image = target_img_buffer_->get(p2p_data.p2p_output.target_timestamp_ms);
    float ppi_val = 0.0f;
    if (target_image.ppi.has_value()) {
      ppi_val = target_image.ppi.value();
    } else {
      return;
    }
    auto [predict_crop, _, predict_image] =
        fetch_predict_image(p2p_data.record_output, p2p_data.p2p_output.predict_timestamp_ms,
                            glm::vec2(target_image.get_dimensions()), ppi_val);
    if (!predict_crop.defined() || !predict_image.has_value()) {
      return;
    }

    auto predict_cam_image = predict_image.value().cpu();
    predict_cam_image.image = predict_crop;
    target_image = target_image.cpu(); // move to cpu now that predict is in cpu

    torch::Tensor target_image_t = target_image.image.clone().detach();
    const int num_cascades = 2;
    for (int i = 0; i < num_cascades; i++) {
      float half_percentage = (0.2f * std::pow(3.0f, (float)i)) / 2.0f;
      torch::Tensor predict_at_target_ppi_inner =
          predict_crop
              .slice(-2, (int64_t)((float)predict_crop.size(-2) * (0.5f - half_percentage)),
                     (int64_t)((float)predict_crop.size(-2) * (0.5f + half_percentage)))
              .slice(-1, (int64_t)((float)predict_crop.size(-1) * (0.5f - half_percentage)),
                     (int64_t)((float)predict_crop.size(-1) * (0.5f + half_percentage)));

      // Create opencv mat from predict image and draw cross at center of the image
      predict_at_target_ppi_inner =
          predict_at_target_ppi_inner.squeeze(0).permute({1, 2, 0}).to(torch::kUInt8).contiguous();
      const int data_type = CV_8UC((int)predict_at_target_ppi_inner.size(2));
      cv::Mat predict_mat((int)predict_at_target_ppi_inner.size(0), (int)predict_at_target_ppi_inner.size(1), data_type,
                          predict_at_target_ppi_inner.data_ptr());

      cv::drawMarker(predict_mat,
                     {(int)predict_at_target_ppi_inner.size(1) / 2, (int)predict_at_target_ppi_inner.size(0) / 2},
                     {255, 0, 0}, cv::MARKER_TILTED_CROSS, 15, 2);
      predict_at_target_ppi_inner = predict_at_target_ppi_inner.permute({2, 0, 1}).unsqueeze(0).to(torch::kFloat32);

      // Resize predict image to 20% of target image
      predict_at_target_ppi_inner =
          interpolate(predict_at_target_ppi_inner, glm::vec2(target_image.get_dimensions()) * 0.2f);

      if (p2p_data.p2p_output.matched) {
        target_image_t = target_image_t.permute({1, 2, 0}).to(torch::kUInt8).contiguous();
        cv::Mat target_mat((int)target_image_t.size(0), (int)target_image_t.size(1), data_type,
                           target_image_t.data_ptr());

        cv::drawMarker(target_mat, {(int)p2p_data.p2p_output.target_coord_x, (int)p2p_data.p2p_output.target_coord_y},
                       {255, 0, 0}, cv::MARKER_TILTED_CROSS, 15, 2);

        target_image_t = target_image_t.permute({2, 0, 1}).to(torch::kFloat32);
      }
      // Copy predict image into top left 20% of target image to create image used for annotating
      target_image_t
          .slice(-1, predict_at_target_ppi_inner.size(-1) * (num_cascades - 1 - i),
                 predict_at_target_ppi_inner.size(-1) * (num_cascades - 1 - i + 1))
          .slice(-2, 0, predict_at_target_ppi_inner.size(-2))
          .copy_(predict_at_target_ppi_inner.squeeze(0));
    }

    auto predict_tensor = predict_crop.squeeze(0).permute({1, 2, 0}).to(torch::kUInt8);
    auto target_tensor = target_image.image.permute({1, 2, 0});
    auto target_annotated_tensor = target_image_t.permute({1, 2, 0}).to(torch::kUInt8);
    if (write_to_disk) {
      metadata_writer_.write_metadata(prefix, target_image.geo_lla_data, target_image.geo_ecef_data,
                                      target_image.timestamp_ms, cam_info_.camera_id, "p2p", ppi_val);
      image_io_.save(prefix + ".perspective.png", [=]() { return predict_tensor; });
      image_io_.save(prefix + ".image.png", [=]() { return target_tensor; });
      image_io_.save(prefix + ".image_annotated.png", [=]() { return target_annotated_tensor; });
    } else {
      P2PScoreQueueObject score_queue_object;
      score_queue_object.perspective = predict_cam_image;
      score_queue_object.perspective.image = predict_tensor;

      score_queue_object.target = target_image;
      score_queue_object.target.image = target_tensor;

      score_queue_object.annotated_target = target_annotated_tensor;

      score_queue_object.score = (double)target_image.timestamp_ms;
      auto it = p2p_queues_.find(reason);
      if (it != p2p_queues_.end()) {
        it->second->push(score_queue_object);
      } else {
        spdlog::warn("Invalid p2p queue reason {}", reason);
      }
    }
  } catch (const maka_error &err) {
    // most likely failed to fetch image
  }
}
void P2PCaptureBuffer::cleanup_loop() {
  auto base_dir = capture_path();
  std::filesystem::create_directories(base_dir);
  while (!shutdown_) {
    std::vector<std::filesystem::path> to_rm;
    std::chrono::hours sleep_time(max_capture_age_);
    auto now = std::filesystem::file_time_type::clock::now();
    for (const auto &dir_entry : std::filesystem::directory_iterator{base_dir}) {
      auto path = dir_entry.path();
      if (!std::filesystem::is_directory(path)) {
        continue;
      }
      try {
        auto age = std::chrono::duration_cast<std::chrono::hours>(now - std::filesystem::last_write_time(path));
        if (age > max_capture_age_) {
          to_rm.emplace_back(path);
        } else if (age < sleep_time) {
          sleep_time = age;
        }
      } catch (std::filesystem::filesystem_error const &ex) {
        continue;
      }
    }
    for (const auto &path : to_rm) {
      std::filesystem::remove_all(path);
    }
    {
      std::unique_lock<std::mutex> lock(mut_);
      cv_.wait_for(lock, sleep_time);
    }
  }
}
} // namespace cv::runtime::nodes::p2p

#pragma once

#include <fmt/format.h>

#include "cv/p2p/output.h"
#include "cv/runtime/cpp/latest_internal_buffer.h"
#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/shmem/cpp/buffer/latest_buffer.hpp"

namespace cv {
namespace runtime {
namespace nodes {
namespace p2p {

using namespace lib::common::camera;
using namespace lib::common::shmem;

class P2POutputSink : public CVNodeImpl {
public:
  P2POutputSink(Camera camera, std::shared_ptr<LatestInternalBuffer<cv::p2p::P2POutput>> buffer,
                Input<cv::p2p::P2POutput> input)
      : CVNodeImpl(fmt::format("/p2p_output_sink/{}", camera.get_info().camera_id)), input_(input), buffer_(buffer) {}

protected:
  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    cv::p2p::P2POutput p2p_output = input_.pop();
    set_state(CVNodeState::kPushing);
    buffer_->update(p2p_output, p2p_output.target_timestamp_ms);
    return p2p_output.target_timestamp_ms;
  }

  void close_connectors() override { input_.close(); }

private:
  Input<cv::p2p::P2POutput> input_;
  std::shared_ptr<LatestInternalBuffer<cv::p2p::P2POutput>> buffer_;
};

} // namespace p2p
} // namespace nodes
} // namespace runtime
} // namespace cv

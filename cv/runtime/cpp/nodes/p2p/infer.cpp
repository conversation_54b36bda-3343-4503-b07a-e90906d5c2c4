#include "infer.h"

#include <c10/cuda/CUDAGuard.h>
#include <fmt/format.h>
#include <opencv2/imgproc.hpp>
#include <torch/script.h>

#include "cv/p2p/p2p_utils.h"
#include "deeplearning/model_io/cpp/model_utils.h"
#include "deeplearning/model_io/cpp/tensorrt.h"
#include "generated/deeplearning/model_io/proto/trt_model.pb.h"
#include "lib/common/cpp/resize_util.h"
#include "lib/common/cpp/time.h"
#include "lib/common/image/cpp/focus_metric.h"

namespace F = torch::nn::functional;

namespace cv {
namespace runtime {
namespace nodes {
namespace p2p {

using namespace lib::common;

class P2PResult {
public:
  P2PResult(std::vector<torch::Tensor> output) : hit_(output[2]), offset_(output[4]), match_(output[5]) {}

  std::optional<glm::ivec2> coord(int n, float hit_threshold = 1e-5f, float score_threshold = 0.5f) {
    torch::Tensor coords = (hit_[n][0] > hit_threshold).nonzero();
    if (coords.size(0) == 0) {
      return {};
    }

    glm::ivec2 point(coords[0][1].item().to<int>(), coords[0][0].item().to<int>());

    torch::Tensor offset_max_nz = (offset_[n][0] >= offset_[n][0].max()).nonzero();
    glm::ivec2 point_offset(offset_max_nz[0][1].item().to<int>() - (int)std::ceil((float)offset_.size(3) / 2.0f),
                            offset_max_nz[0][0].item().to<int>() - (int)std::ceil((float)offset_.size(2) / 2.0f));
    point += point_offset;

    if (score(n) < score_threshold) {
      return {};
    }
    return point;
  }

  float score(int n) { return match_[n].item().to<float>(); }

private:
  torch::Tensor hit_;
  torch::Tensor offset_;
  torch::Tensor match_;
};

P2PInfer::P2PInfer(camera::Camera camera, Input<camera::CameraImage> input_target,
                   CameraBufferMap distance_camera_buffers,
                   std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry, int gpu_id,
                   bool enable_incremental_p2p, bool is_simulator,
                   std::unordered_map<std::string, CVHttpClient> &http_clients)
    : CVNodeImpl(fmt::format("/p2p/{}", camera.get_info().camera_id)), input_target_(input_target),
      distance_camera_buffers_(distance_camera_buffers), model_registry_(model_registry), gpu_id_(gpu_id),
      enable_incremental_p2p_(enable_incremental_p2p),
      cuda_stream_(c10::cuda::getStreamFromPool(true, c10::DeviceIndex(gpu_id))), is_simulator_(is_simulator),
      cv_http_clients_(http_clients) {
  reload_model();
  if (model_) {
    camera::CameraImage image;
    image.image =
        torch::zeros({3, 4000, 3096}, torch::TensorOptions().device(torch::kCUDA, gpu_id).dtype(torch::kUInt8));
    image.ppi = 200;
    image.timestamp_ms = maka_control_timestamp_ms();
    set_context_with_image({image.get_width() / 2, image.get_height() / 2}, image);
  }
}

void P2PInfer::clear_model() {
  std::unique_lock<std::shared_mutex> lock(tick_mutex_);
  model_ = model::AtomicModel();
}

bool P2PInfer::ready() {
  bool ready = true;
  {
    std::shared_lock<std::shared_mutex> lock(tick_mutex_);
    ready &= model_;
  }
  return ready;
}

void P2PInfer::reload_model() {
  std::unique_lock<std::shared_mutex> lock(tick_mutex_);
  // Free old model before loading new model
  model_ = model::AtomicModel();
  if (!model_registry_->contains(model::ModelUseCase::kP2P)) {
    return;
  }

  spdlog::info("Loading P2P model {} to GPU {}", model_registry_->get_path_for_use_case(model::ModelUseCase::kP2P),
               gpu_id_);
  try {
    model_ = model_registry_->get(model::ModelUseCase::kP2P, gpu_id_);
  } catch (const std::exception &e) {
    spdlog::warn("Unable to load P2P model: {}", e.what());
    return;
  }

  if (!model_.get_internal_metadata().means().empty()) {
    means_ = torch::tensor(std::vector<float>{model_.get_internal_metadata().means()[0],
                                              model_.get_internal_metadata().means()[1],
                                              model_.get_internal_metadata().means()[2]})
                 .to({torch::kCUDA, gpu_id_});
  }

  if (!model_.get_internal_metadata().stds().empty()) {
    stds_ = torch::tensor(std::vector<float>{model_.get_internal_metadata().stds()[0],
                                             model_.get_internal_metadata().stds()[1],
                                             model_.get_internal_metadata().stds()[2]})
                .to({torch::kCUDA, gpu_id_});
  }
  predict_crop_size_ = glm::ivec2(model_.get_internal_metadata().aux_input_sizes()[0].width(),
                                  model_.get_internal_metadata().aux_input_sizes()[0].height());
  target_crop_size_ = glm::ivec2(model_.get_internal_metadata().input_size().width(),
                                 model_.get_internal_metadata().input_size().height());

  incremental_p2p_ = false;
  context_updated_ = true;
  safety_zone_up_ = 0.f;
  safety_zone_down_ = 0.f;
  safety_zone_left_ = 0.f;
  safety_zone_right_ = 0.f;

  spdlog::info("Loaded P2P model to GPU {}", gpu_id_);
}

bool P2PInfer::set_context(glm::vec2 predict_coords, int64_t predict_timestamp, std::string predict_cam_id) {
  NVTXRange range("P2P_set_context");
  if (!model_) {
    return false;
  }
  if (is_simulator_ && last_predict_image_) {
    std::unique_lock<std::mutex> lck(last_predict_mutex_);
    last_predict_image_->timestamp_ms = predict_timestamp;
    last_predict_coords_ = predict_coords;
    last_predict_cam_id_ = predict_cam_id;
    return true;
  }
  if (distance_camera_buffers_->find(predict_cam_id) == distance_camera_buffers_->end()) {
    auto remote_img_opt = cv_http_clients_[predict_cam_id].get(
        predict_coords, predict_timestamp, predict_cam_id, predict_crop_size_, model_.get_internal_metadata().ppi());
    if (remote_img_opt) {
      set_context_with_remote_image(remote_img_opt.value());
      last_predict_cam_id_ = predict_cam_id;
      return true;
    } else {
      spdlog::warn("P2PInfer::set_context: Failed to retrieve remote context.", predict_cam_id);
      return false;
    }
  }

  auto distance_buffer = distance_camera_buffers_->at(predict_cam_id);
  if (distance_buffer->contains(predict_timestamp)) {
    set_context_with_image(predict_coords, distance_buffer->get(predict_timestamp));
    last_predict_cam_id_ = predict_cam_id;
    return true;
  }

  return false;
}

void P2PInfer::set_context_with_remote_image(camera::CameraImage image) {
  torch::Tensor predict_image_t =
      image.image.to(deeplearning::model_io::dtype_to_torch_type(model_.get_internal_metadata().input_dtype()));
  if (predict_image_t.ndimension() == 3) {
    predict_image_t = predict_image_t.unsqueeze(0);
  }
  c10::cuda::CUDAStreamGuard stream_guard(cuda_stream_);
  {
    NVTXRange range("P2P_memcpy_then_norm");
    predict_image_t = normalize(predict_image_t.to({torch::kCUDA, gpu_id_}));
  }
  std::unique_lock<std::mutex> lck(last_predict_mutex_);
  last_predict_image_t_ = predict_image_t;
  last_predict_image_ = image;

  last_predict_coords_ = {};
  context_updated_ = true;
  cuda_stream_.synchronize();
}

void P2PInfer::set_context_with_image(glm::vec2 predict_coords, camera::CameraImage image) {
  torch::Tensor predict_image_t =
      image.image.to(deeplearning::model_io::dtype_to_torch_type(model_.get_internal_metadata().input_dtype()));
  if (predict_image_t.ndimension() == 3) {
    predict_image_t = predict_image_t.unsqueeze(0);
  }
  {
    NVTXRange range("P2P_crop_and_resize");
    // Resize and crop perspectives
    std::tie(predict_image_t, std::ignore, std::ignore) =
        crop_and_resize_ppi(predict_image_t, *image.ppi, predict_coords, predict_crop_size_);
  }

  c10::cuda::CUDAStreamGuard stream_guard(cuda_stream_);
  {
    NVTXRange range("P2P_memcpy_then_norm");
    predict_image_t = normalize(predict_image_t.to({torch::kCUDA, gpu_id_}));
  }
  std::unique_lock<std::mutex> lck(last_predict_mutex_);
  last_predict_image_t_ = predict_image_t;
  last_predict_image_ = image;

  last_predict_coords_ = predict_coords;
  context_updated_ = true;
  cuda_stream_.synchronize();
}

void P2PInfer::set_safety_zone(float safety_zone_up, float safety_zone_down, float safety_zone_left,
                               float safety_zone_right) {
  std::unique_lock<std::mutex> lck(last_predict_mutex_);
  safety_zone_up_ = safety_zone_up;
  safety_zone_down_ = safety_zone_down;
  safety_zone_left_ = safety_zone_left;
  safety_zone_right_ = safety_zone_right;
}

std::tuple<torch::Tensor, glm::vec2, glm::vec2>
P2PInfer::crop_and_resize_ppi(torch::Tensor input, float input_ppi, glm::ivec2 crop_center, glm::ivec2 crop_size) {
  return cv::p2p::crop_and_resize_ppi(input, input_ppi, crop_center, crop_size, model_.get_internal_metadata().ppi());
}

torch::Tensor P2PInfer::normalize(torch::Tensor image) {
  image = image.permute({0, 2, 3, 1});
  image /= 255.0f;
  // normalize to 0..1
  if (!model_.get_internal_metadata().means().empty()) {
    image -= means_;
  }
  if (!model_.get_internal_metadata().stds().empty()) {
    image /= stds_;
  }
  image = image.permute({0, 3, 1, 2});
  return image;
}

int64_t P2PInfer::tick() {
  set_state(CVNodeState::kPulling);
  camera::CameraImage target_image = input_target_.pop();
  set_state(CVNodeState::kProcessing);

  std::shared_lock<std::shared_mutex> lock(tick_mutex_);
  c10::cuda::CUDAStreamGuard stream_guard(cuda_stream_);

  if (!last_predict_image_ || !last_predict_image_t_ || !model_) {
    return 0;
  }

  camera::CameraImage predict_image;
  torch::Tensor predict_image_t;
  std::optional<glm::ivec2> predict_coord;
  {
    std::unique_lock<std::mutex> lck(last_predict_mutex_);
    if (context_updated_) {
      incremental_p2p_ = false;
      context_updated_ = false;
    }
  }
  std::optional<glm::vec2> last_coord;
  std::optional<std::string> last_predict_cam;
  if (incremental_p2p_) {
    predict_image = last_target_image_;
    predict_coord = last_target_coords_;
  } else {
    std::unique_lock<std::mutex> lck(last_predict_mutex_);
    last_predict_cam = last_predict_cam_id_;

    predict_image = *last_predict_image_;
    predict_image_t = *last_predict_image_t_;
    predict_coord = last_predict_coords_;
  }

  if (is_simulator_) {
    cv::p2p::P2POutput p2p_output;
    p2p_output = {.matched = true,
                  .target_coord_x = (float)target_image.get_width() / 2,
                  .target_coord_y = (float)target_image.get_height() / 2,
                  .target_timestamp_ms = target_image.timestamp_ms,
                  .predict_timestamp_ms = predict_image.timestamp_ms,
                  .safe = true};
    cv::p2p::P2POutputForBurstRecord p2p_output_for_burst_record;
    p2p_output_for_burst_record.predict_buffer_name = predict_image.camera_id;
    p2p_output_for_burst_record.predict_coord = predict_coord.value_or(glm::ivec2(0));
    p2p_output_for_burst_record.predict_crop_size = predict_crop_size_;
    // [TODO] Setting the default to 200 because ppi is optional. We should make it non-optional
    p2p_output_for_burst_record.predict_ppi = predict_image.ppi.value_or(200);

    set_state(CVNodeState::kPushing);
    output_.push(p2p_output);
    output_for_burst_record_.push(p2p_output_for_burst_record);
    return target_image.timestamp_ms;
  }

  // Assure both inputs are on the correct gpu for this node
  predict_image_t = predict_image_t.to({torch::kCUDA, gpu_id_});
  target_image = target_image.cuda(gpu_id_);

  torch::Tensor target_image_t =
      target_image.image.to(deeplearning::model_io::dtype_to_torch_type(model_.get_internal_metadata().input_dtype()));

  if (target_image_t.ndimension() == 3) {
    target_image_t = target_image_t.unsqueeze(0);
  }

  // Resize and crop targets
  glm::vec2 target_initial_crop_coord_offset, target_final_crop_coord_offset;
  std::tie(target_image_t, target_initial_crop_coord_offset, target_final_crop_coord_offset) =
      crop_and_resize_ppi(target_image_t, *target_image.ppi, target_image.get_dimensions() / 2, target_crop_size_);

  target_image_t = normalize(target_image_t);

  P2PResult p2p_result(model_({predict_image_t, target_image_t}));

  cv::p2p::P2POutput p2p_output;
  if (std::optional<glm::ivec2> max_score_coord_opt = p2p_result.coord(0)) {
    glm::vec2 max_score_coord_adj = *max_score_coord_opt;
    max_score_coord_adj += target_final_crop_coord_offset;
    max_score_coord_adj =
        cv::utils::adjust_size_ppi(max_score_coord_adj, model_.get_internal_metadata().ppi(), *target_image.ppi);
    max_score_coord_adj += target_initial_crop_coord_offset;
    p2p_output = {.matched = true,
                  .target_coord_x = max_score_coord_adj.x,
                  .target_coord_y = max_score_coord_adj.y,
                  .target_timestamp_ms = target_image.timestamp_ms,
                  .predict_timestamp_ms = predict_image.timestamp_ms,
                  .safe = true};

    last_target_image_ = target_image;
    last_target_coords_ =
        glm::clamp(max_score_coord_adj, glm::vec2(0.0f), static_cast<glm::vec2>(target_image.get_dimensions()));

    // Now that we have target coordinates, we can do incremental P2P until we get a new predict context.
    incremental_p2p_ = enable_incremental_p2p_;

    p2p_output.safe = true;
  } else {
    p2p_output.matched = false;
    p2p_output.safe = true;
    p2p_output.target_coord_x = 0.0f;
    p2p_output.target_coord_y = 0.0f;
    p2p_output.target_timestamp_ms = target_image.timestamp_ms;
    p2p_output.predict_timestamp_ms = predict_image.timestamp_ms;
  }

  if (last_coord.has_value()) {
    p2p_output.predict_coord_x = last_coord.value().x;
    p2p_output.predict_coord_y = last_coord.value().y;
  }

  if (last_predict_cam.has_value()) {
    p2p_output.predict_cam = last_predict_cam.value();
  }

  cv::p2p::P2POutputForBurstRecord p2p_output_for_burst_record;
  p2p_output_for_burst_record.predict_buffer_name = predict_image.camera_id;
  p2p_output_for_burst_record.predict_coord = predict_coord.value_or(glm::ivec2(0));
  p2p_output_for_burst_record.predict_crop_size = predict_crop_size_;
  p2p_output_for_burst_record.predict_ppi = predict_image.ppi.value_or(200);

  cuda_stream_.synchronize();
  set_state(CVNodeState::kPushing);
  output_.push(p2p_output);
  output_for_burst_record_.push(p2p_output_for_burst_record);

  return target_image.timestamp_ms;
}

void P2PInfer::close_connectors() {
  input_target_.close();
  output_.close();
}

} // namespace p2p
} // namespace nodes
} // namespace runtime
} // namespace cv

#pragma once

#include "hardware_manager/cpp/grpc_client.h"
#include "lib/common/cpp/implement_status.h"
#include "lib/common/cpp/time.h"
#include "lib/common/cpp/utils/generation.hpp"
#include <chrono>

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

class ImplementStatusRetriever : public CVNodeImpl {
public:
  ImplementStatusRetriever(std::shared_ptr<ImplementStatus> implement_status,
                           std::shared_ptr<hardware_manager::HardwareManagerClient> hardware_manager_client)
      : CVNodeImpl("ImplementStatusRetriever"), implement_status_(implement_status),
        hardware_manager_client_(hardware_manager_client), connected_(true) {
    char *role_ptr = std::getenv("MAKA_ROLE");
    role_ = "unset";
    if (role_ptr != NULL) {
      role_ = role_ptr;
    }
  }

  int64_t tick() override {
    std::this_thread::sleep_for(std::chrono::seconds(1));
    if (role_ == "veselka_cv" || role_ == carbon::common::GEN_BUD || role_ == "unset") {
      return 0;
    }
    set_state(CVNodeState::kProcessing);

    try {
      auto status = hardware_manager_client_->get_implement_status();
      auto lifted = status.lifted();
      auto estopped = status.estopped();

      if (!connected_) {
        spdlog::info("Implement Status Retriever connected to hardware manager");
        connected_ = true;
      }

      implement_status_->update(lifted, estopped);
    } catch (const std::exception &e) {
      if (connected_) {
        spdlog::warn("Implement Status Retriever disconnected from hardware manager: {}", e.what());
      }
      connected_ = false;
      return 0;
    }
    return maka_control_timestamp_ms();
  }

private:
  std::shared_ptr<ImplementStatus> implement_status_;
  std::shared_ptr<hardware_manager::HardwareManagerClient> hardware_manager_client_;
  std::string role_;
  bool connected_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
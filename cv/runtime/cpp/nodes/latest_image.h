#pragma once

#include "cv/runtime/cpp/node.h"
#include <chrono>
#include <condition_variable>
#include <fmt/format.h>
#include <mutex>
#include <optional>

namespace cv {
namespace runtime {
namespace nodes {

class LatestImage : public CVNodeImpl {
public:
  LatestImage(Camera camera, Input<CameraImage> input)
      : CVNodeImpl(fmt::format("/latest_image/{}", camera.get_info().camera_id)), input_(input) {}

  std::optional<CameraImage> get() {
    std::unique_lock lck(mutex_);
    return cached_image_;
  }
  std::optional<CameraImage> get_next(int64_t timestamp_ms, int64_t timeout_ms) {
    std::unique_lock lock(mutex_);
    if (cached_image_ && timestamp_ms < cached_image_->timestamp_ms) {
      return cached_image_;
    }

    cv_.wait_for(lock, std::chrono::milliseconds(timeout_ms),
                 [&] { return cached_image_ && timestamp_ms < cached_image_->timestamp_ms; });
    if (timestamp_ms < cached_image_->timestamp_ms) {
      return cached_image_;
    }

    return {};
  }

protected:
  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    CameraImage cam_image = input_.pop();
    set_state(CVNodeState::kProcessing);

    try {
      std::unique_lock lck(mutex_);
      cached_image_ = cam_image;
    } catch (...) {
      spdlog::error("Latest Image Exception");
    }

    return cam_image.timestamp_ms;
  }

  void close_connectors() override { input_.close(); }

private:
  Input<CameraImage> input_;
  std::mutex mutex_;
  std::optional<CameraImage> cached_image_;
  std::condition_variable cv_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
#pragma once

#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common::camera;

const std::string kNoopNodeName = "noop";

template <typename T>
class NoopNode : public CVNodeImpl {
public:
  NoopNode(Input<T> input) : CVNodeImpl(kNoopNodeName), input_(input) {}
  Output<T> &get_output() { return output_; }

protected:
  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    T val = input_.pop();
    set_state(CVNodeState::kPushing);
    output_.push(val);
    return 0;
  }

  void close_connectors() override {
    input_.close();
    output_.close();
  }

private:
  Input<T> input_;
  Output<T> output_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
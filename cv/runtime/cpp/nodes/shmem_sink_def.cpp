#include "cv/runtime/cpp/nodes/shmem_sink_def.h"

namespace cv {
namespace runtime {
namespace nodes {

void copy_shmem_image_output(void *dst, void *src, uint32_t size) {
  (void)size;
  ShmemImageOutput *src_ptr = static_cast<ShmemImageOutput *>(src);
  ShmemImageOutput *dst_ptr = static_cast<ShmemImageOutput *>(dst);
  dst_ptr->width = src_ptr->width;
  dst_ptr->height = src_ptr->height;
  dst_ptr->channels = src_ptr->channels;
  dst_ptr->timestamp_ms = src_ptr->timestamp_ms;
  dst_ptr->focus_metric = src_ptr->focus_metric;

  std::memcpy(dst_ptr->data.data(), src_ptr->data.data(),
              dst_ptr->width * dst_ptr->height * dst_ptr->channels * sizeof(uint8_t));
}

} // namespace nodes
} // namespace runtime
} // namespace cv
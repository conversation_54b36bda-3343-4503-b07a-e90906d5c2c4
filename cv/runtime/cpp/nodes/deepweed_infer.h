#pragma once

#include <c10/cuda/CUDAStream.h>
#include <glm/glm.hpp>
#include <prometheus/histogram.h>
#include <torch/torch.h>

#include "config/tree/cpp/config_tree.hpp"
#include "cv/deepweed/deepweed_model.h"
#include "cv/deepweed/output.h"
#include "cv/runtime/cpp/node.h"
#include "cv/runtime/cpp/nodes/deepweed_metrics.h"
#include "cv/runtime/proto/cv_runtime.pb.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/fixed_buffer.h"
#include "lib/common/model/cpp/model_registry.h"

namespace F = torch::nn::functional;

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

class DeepweedInfer : public CVNodeImpl {
public:
  DeepweedInfer(camera::Camera camera, Input<camera::CameraImage> input_predict,
                std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry, int gpu_id,
                std::shared_ptr<carbon::config::ConfigTree> deepweed_config,
                std::shared_ptr<carbon::config::ConfigTree> simulator_config,
                std::shared_ptr<prometheus::Registry> registry,
                std::shared_ptr<std::map<std::string, std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>>>>
                    distance_camera_buffers);
  Output<deepweed::DeepweedOutput> &get_output() { return output_; }
  void reload_model();
  void clear_model();
  bool ready();
  void set_classifier(std::shared_ptr<embeddings::Classifier> embeddings_classifier);
  void clear_classifier();

  std::vector<std::string> get_supported_segmentation_categories();
  std::vector<std::string> get_supported_point_categories();

  float get_weed_point_threshold();
  float get_crop_point_threshold();
  float get_plant_point_threshold();
  std::optional<std::string> get_model_id();
  int get_gpu_id() { return gpu_id_; }

private:
  void close_connectors();
  int64_t tick();

  std::vector<proto::SegmentationDetectionCategory> get_segmentation_categories();

  torch::Tensor normalize(torch::Tensor image);
  static glm::vec2 adjust_size_ppi(glm::vec2 coord, float ppi, float new_ppi) { return coord / ppi * new_ppi; }

  int64_t process_simulator_data(camera::CameraImage &predict_image, bool only_plant_points);

  std::mutex current_mask_expression_mutex_;
  std::shared_mutex infer_mutex_;

  std::unique_ptr<deepweed::DeepweedModel> model_;
  Input<camera::CameraImage> input_;
  std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry_;
  int gpu_id_;
  Output<deepweed::DeepweedOutput> output_;
  c10::cuda::CUDAStream cuda_stream_;
  std::shared_ptr<carbon::config::ConfigTree> deepweed_config_;
  std::shared_ptr<carbon::config::ConfigTree> simulator_config_;
  std::shared_ptr<carbon::config::ConfigTree> current_crop_id_config_;
  std::vector<proto::SegmentationDetectionCategory> segmentation_categories_;
  DeepweedMetrics deepweed_metrics_;
  std::shared_ptr<std::map<std::string, std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>>>>
      distance_camera_buffers_;
  std::atomic<int64_t> simulator_wait_time_;
  std::shared_ptr<embeddings::Classifier> embeddings_classifier_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv

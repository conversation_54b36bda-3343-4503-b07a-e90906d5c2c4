#pragma once

#include <atomic>
#include <chrono>
#include <deque>
#include <functional>
#include <optional>
#include <thread>

#include <c10/cuda/CUDAGuard.h>
#include <fmt/format.h>
#include <fmt/ostream.h>
#include <opencv2/core.hpp>
#include <opencv2/imgcodecs.hpp>
#include <opencv2/imgproc.hpp>
#include <prometheus/counter.h>
#include <prometheus/registry.h>
#include <spdlog/spdlog.h>
#include <torch/torch.h>

#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/bedtop_profile.h"
#include "lib/common/cpp/geo_data.h"
#include "lib/drivers/thinklucid/cpp/camera_factory.h"

#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_atomic_accessor.hpp>

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common::camera;
using namespace lib::common;

const int kRetryWaitMillis = 1000;
const int kRediscoverCamerasTimeoutSeconds = 30;

enum ERROR_TYPE {
  NONE,
  GRAB,
  CONNECTION,
};

class CameraGrabber : public CVNodeImpl {
public:
  CameraGrabber(Camera cam, std::shared_ptr<GeoData> geo_data_container, std::shared_ptr<BedtopProfile> bedtop_profile,
                prometheus::Counter &dropped_frame_counter, std::function<void(const std::string &)> cam_resetter)
      : CVNodeImpl(fmt::format("/grabber/{}", cam.get_info().camera_id)), cam_(cam), initial_prepare_(true),
        disconnected_(true), last_grab_timestamp_ms_(maka_control_timestamp_ms()),
        received_image_after_disconnect_(false), geo_data_container_(geo_data_container),
        bedtop_profile_(bedtop_profile), dropped_frame_counter_(dropped_frame_counter), last_error_type_(CONNECTION),
        grab_timeout_count_(0), prev_connection_failure_(false), cam_resetter_(cam_resetter) {}

  Camera get_camera() { return cam_; }

  Output<CameraImage> &get_output() { return output_; }

  bool get_received_image_after_disconnect() { return received_image_after_disconnect_; }

  ERROR_TYPE get_last_error_type() { return last_error_type_; }

  int64_t get_last_grab_timestamp_ms() { return last_grab_timestamp_ms_; }

  void set_images(const std::vector<std::string> &image_paths) {
    std::unique_lock<std::mutex> lk(mutex_);
    set_images_.clear();
    for (const auto &path : image_paths) {
      set_images_.push_back(load_image(path, 0));
    }
    set_images_index_ = 0;
  }

  void queue_images(const std::vector<std::string> &image_paths, const std::vector<int64_t> &timestamps_ms) {
    std::unique_lock<std::mutex> lk(mutex_);
    for (size_t i = 0; i < image_paths.size(); i++) {
      image_queue_.push_back(image_paths[i]);
      timestamps_queue_.push_back(timestamps_ms[i]);
    }
  }

  void load_and_queue_images(const std::vector<std::string> &image_paths, const std::vector<int64_t> &timestamps_ms) {
    std::unique_lock<std::mutex> lk(mutex_);
    for (size_t i = 0; i < image_paths.size(); i++) {
      auto loaded_image = load_image(image_paths[i], timestamps_ms[i]);
      cam_image_queue_.push_back(loaded_image);
      timestamps_queue_.push_back(timestamps_ms[i]);
    }
  }

  void set_image(std::string image_path) {
    std::unique_lock<std::mutex> lk(mutex_);
    set_image_ = load_image(image_path, 0);
  }

  void unset_image() {
    std::unique_lock<std::mutex> lk(mutex_);
    set_image_ = std::nullopt;
  }

protected:
  void prepare() override {
    std::shared_ptr<CameraImpl> impl;
    try {
      if (cam_.get_info().vendor == CameraVendor::kThinkLucid &&
          std::chrono::system_clock::time_point(std::chrono::milliseconds(last_grab_timestamp_ms_)) +
                  std::chrono::seconds(kRediscoverCamerasTimeoutSeconds) <
              std::chrono::system_clock::now()) {
        lib::drivers::thinklucid::ThinkLucidCameraFactory::get_instance()->update_camera_info();
      }
      cam_.reset();
      impl = cam_.get_impl(std::chrono::milliseconds(0));
      if (!impl) {
        throw restart_node_error();
      }
      impl->start_grabbing();
    } catch (camera_error &ex) {
      if (!disconnected_ || initial_prepare_) {
        spdlog::warn("Camera disconnected {}: {}", cam_.get_info(), ex.what());
      }
      initial_prepare_ = false;
      disconnected_ = true;
      set_last_error_type(CONNECTION);
      std::this_thread::sleep_for(std::chrono::milliseconds(kRetryWaitMillis));
      throw restart_node_error();
    }

    set_last_error_type(NONE);
    spdlog::info("Camera connected {}", impl->get_info());
    initial_prepare_ = false;
    disconnected_ = false;
  }

  int64_t tick() override {
    set_state(CVNodeState::kProcessing);
    c10::cuda::OptionalCUDAStreamGuard stream_guard;
    if (cam_.get_settings().gpu_id) {
      if (!cuda_stream_) {
        cuda_stream_.reset(new c10::cuda::CUDAStream(
            c10::cuda::getStreamFromPool(true, c10::DeviceIndex(*cam_.get_settings().gpu_id))));
      }
      stream_guard.reset_stream(*cuda_stream_);
    }

    try {
      CameraImage cam_image;
      auto [lla_data, ecef_data] = geo_data_container_->retrieve();
      auto [bedtop_profile, bbh_offset_mm] = bedtop_profile_->retrieve(cam_.get_info().camera_id);
      {
        std::unique_lock<std::mutex> lk(mutex_);

        if (image_queue_.size() > 0) {
          auto t_ms = timestamps_queue_.front();
          cam_image = load_image(image_queue_.front(), t_ms);
          image_queue_.pop_front();
          timestamps_queue_.pop_front();
          std::this_thread::sleep_until(std::chrono::time_point<std::chrono::system_clock>(
              std::chrono::milliseconds(t_ms))); // Get this timestamp
        } else if (cam_image_queue_.size() > 0) {
          auto t_ms = timestamps_queue_.front();
          cam_image = cam_image_queue_.front();
          cam_image_queue_.pop_front();
          timestamps_queue_.pop_front();
          std::this_thread::sleep_until(std::chrono::time_point<std::chrono::system_clock>(
              std::chrono::milliseconds(t_ms))); // Get this timestamp
        } else if (set_image_.has_value()) {
          cam_image = set_image_.value();
          cam_image.timestamp_ms = maka_control_timestamp_ms();
          std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        } else if (set_images_.size() > 0) {
          cam_image = set_images_[set_images_index_];
          cam_image.timestamp_ms = maka_control_timestamp_ms();
          set_images_index_++;
          if (set_images_index_ >= (int)set_images_.size()) {
            set_images_index_ = 0;
          }
          std::this_thread::sleep_for(std::chrono::milliseconds((int)(1000 / cam_.get_settings().sim_fps.value_or(1))));
        } else {
          cam_image = cam_.grab();
        }
        received_image_after_disconnect_ = true;
      }
      cam_image.geo_lla_data = lla_data;
      cam_image.geo_ecef_data = ecef_data;
      cam_image.bedtop_profile = bedtop_profile;
      cam_image.bbh_offset_mm = bbh_offset_mm;
      cam_image.exposure_us = cam_.get_settings().exposure_us.value_or(0);
      cam_image.gain_db = cam_.get_settings().gain_db.value_or(0);

      if (cuda_stream_) {
        cuda_stream_->synchronize();
      }
      last_grab_timestamp_ms_ = maka_control_timestamp_ms();

      set_state(CVNodeState::kPushing);
      grab_timeout_count_ = 0;
      output_.push(cam_image);
      set_last_error_type(NONE);
      return cam_image.timestamp_ms;
    } catch (grab_incomplete_error &ex) {
      dropped_frame_counter_.Increment();
      spdlog::warn("{}", ex.what());
    } catch (grab_timeout_error &ex) {
      spdlog::warn("Grab timed out: {}", ex.what());
      grab_timeout_count_++;
      if (grab_timeout_count_ >= 3) {
        received_image_after_disconnect_ = false;
      }
      set_last_error_type(GRAB);
      std::this_thread::sleep_for(std::chrono::milliseconds(kRetryWaitMillis));
      throw restart_node_error();
    } catch (camera_error &ex) {
      spdlog::error("{}", ex.what());
      received_image_after_disconnect_ = false;
      set_last_error_type(CONNECTION);
      std::this_thread::sleep_for(std::chrono::milliseconds(kRetryWaitMillis));
      throw restart_node_error();
    }
    return 0;
  }

  void close_connectors() override { output_.close(); }

  void finalize() override {
    auto impl = cam_.get_impl(std::chrono::milliseconds(0));
    if (!impl) {
      return;
    }
    impl->stop_grabbing();
  }

private:
  CameraImage load_image(std::string path, int64_t timestamp_ms) {
    cv::Mat cv_image = cv::imread(path, cv::IMREAD_UNCHANGED);
    if (cv_image.empty()) {
      throw maka_error(fmt::format("Unable to read image file {}", path));
    }

    torch::Tensor tensor_image =
        torch::zeros({cv_image.rows, cv_image.cols, cv_image.channels()}, torch::TensorOptions().dtype(torch::kUInt8));

    cv::Mat cv_image_tensor((int)tensor_image.size(0), (int)tensor_image.size(1), CV_8UC(cv_image.channels()),
                            tensor_image.data_ptr());

    std::optional<torch::Tensor> static_depth_image = std::nullopt;

    if (cv_image.channels() > 3) {
      cv::cvtColor(cv_image, cv_image_tensor, cv::COLOR_BGRA2RGBA);
      static_depth_image = tensor_image.index({"...", 3});
    } else {
      cv::cvtColor(cv_image, cv_image_tensor, cv::COLOR_BGR2RGB);
    }

    torch::Tensor static_image = tensor_image.index({"...", torch::indexing::Slice(0, 3)}).permute({2, 0, 1});

    auto width = (int)static_image.size(2);
    auto height = (int)static_image.size(1);
    auto pixel_format = PixelFormat::kRGB8;

    with_device device_guard(*cam_.get_settings().gpu_id);
    torch::Tensor pinned_temp_image =
        torch::empty({3, height, width}, torch::TensorOptions().pinned_memory(true).dtype(torch::kUInt8));

    CameraImage cam_image;
    cam_image.camera_id = cam_.get_info().camera_id;

    cam_image.timestamp_ms = timestamp_ms;
    cam_image.pixel_format = pixel_format;
    cam_image.ppi = cam_.get_info().ppi;
    if (cam_.get_settings().gpu_id.has_value()) {
      auto opts = torch::TensorOptions().device(torch::kCUDA, cam_.get_settings().gpu_id.value());
      pinned_temp_image.copy_(static_image);
      cam_image.image = pinned_temp_image.to(opts, true);
      if (static_depth_image.has_value()) {
        cam_image.depth = static_depth_image->to(opts, true);
      }
    } else {
      cam_image.image = static_image.clone();
      if (static_depth_image.has_value()) {
        cam_image.depth = static_depth_image.value().clone();
      }
    }
    return cam_image;
  }

  void set_last_error_type(ERROR_TYPE error) {
    static carbon::config::ConfigAtomicAccessor<uint32_t> connection_timer(
        carbon::config::get_global_config_subscriber()->get_config_node("cv", "auto_fix_connection_timer"));
    last_error_type_ = error;
    if (error != CONNECTION) {
      prev_connection_failure_ = false;
      return;
    }
    if (!prev_connection_failure_) {
      prev_connection_failure_ = true;
      failure_start_time_ = std::chrono::steady_clock::now();
    }
    auto timer = connection_timer.get_value();
    if (timer > 0) {
      auto now = std::chrono::steady_clock::now();
      if ((now - failure_start_time_) > std::chrono::seconds(timer)) {
        spdlog::info("Camera {} has been unreachable for over {} seconds, attempting powercycle.",
                     cam_.get_info().camera_id, timer);
        cam_resetter_(cam_.get_info().camera_id);
        // Prevent powercycle looping, set last failure to now + timer so we have 2x timer for powercycle
        failure_start_time_ = now + std::chrono::seconds(timer);
      }
    }
  }

  Camera cam_;
  Output<CameraImage> output_;
  std::unique_ptr<c10::cuda::CUDAStream> cuda_stream_;
  std::deque<std::string> image_queue_;
  std::deque<int64_t> timestamps_queue_;
  std::deque<CameraImage> cam_image_queue_;
  std::mutex mutex_;
  std::vector<CameraImage> set_images_;
  int set_images_index_;
  std::optional<CameraImage> set_image_;
  bool initial_prepare_;
  bool disconnected_;
  std::atomic<int64_t> last_grab_timestamp_ms_;
  std::atomic<bool> received_image_after_disconnect_;
  std::shared_ptr<GeoData> geo_data_container_;
  std::shared_ptr<BedtopProfile> bedtop_profile_;
  prometheus::Counter &dropped_frame_counter_;
  std::atomic<ERROR_TYPE> last_error_type_;
  uint32_t grab_timeout_count_;
  bool prev_connection_failure_;
  std::chrono::steady_clock::time_point failure_start_time_;
  std::function<void(const std::string &)> cam_resetter_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv

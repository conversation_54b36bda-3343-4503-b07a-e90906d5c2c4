#pragma once

#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/time.h"

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common::camera;

class DummySource : public CVNodeImpl {
public:
  DummySource() : CVNodeImpl("DummySource") {}
  Output<CameraImage> &get_output() { return output_; }

protected:
  int64_t tick() override {
    CameraImage cam_image;
    cam_image.timestamp_ms = maka_control_timestamp_ms();
    set_state(CVNodeState::kPushing);
    output_.push(cam_image);
    return cam_image.timestamp_ms;
  }

  void close_connectors() override { output_.close(); }

private:
  Output<CameraImage> output_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
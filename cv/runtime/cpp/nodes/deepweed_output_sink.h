#pragma once

#include <fmt/format.h>

#include "cv/deepweed/output.h"
#include "cv/runtime/cpp/latest_internal_buffer.h"
#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include <spdlog/spdlog.h>

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common::camera;

class DeepweedOutputSink : public CVNodeImpl {
public:
  DeepweedOutputSink(Camera camera, Input<deepweed::DeepweedOutput> input,
                     std::shared_ptr<LatestInternalBuffer<deepweed::DeepweedOutput>> buffer)
      : CVNodeImpl(fmt::format("/deepweed_output_sink/{}", camera.get_info().camera_id)), input_(input),
        buffer_(buffer) {}

protected:
  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    deepweed::DeepweedOutput deepweed_output = input_.pop();
    set_state(CVNodeState::kProcessing);
    buffer_->update(deepweed_output, deepweed_output.timestamp_ms);
    return deepweed_output.timestamp_ms;
  }

  void close_connectors() override { input_.close(); }

private:
  Input<deepweed::DeepweedOutput> input_;
  std::shared_ptr<LatestInternalBuffer<deepweed::DeepweedOutput>> buffer_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv

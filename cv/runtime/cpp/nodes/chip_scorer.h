#pragma once

#include "cv/deepweed/output.h"
#include "cv/runtime/cpp/image_score_queue.h"
#include "cv/runtime/cpp/node.h"
#include "cv/runtime/cpp/nodes/constants.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/camera/cpp/camera_image.h"
#include "lib/common/cpp/aimbot_state.h"
#include "lib/common/cpp/distance_status.h"
#include "lib/common/cpp/fixed_buffer.h"
#include "lib/common/cpp/implement_status.h"
#include "lib/common/cpp/interest_scores.hpp"
#include "lib/common/model/cpp/model_registry.h"
#include "weed_tracking/cpp/client/grpc_client.h"
#include "weed_tracking/proto/weed_tracking.grpc.pb.h"
#include <config/tree/cpp/config_scoped_callback.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <fmt/format.h>
#include <spdlog/spdlog.h>
#include <vector>

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

enum ScoreType { Ambiguity, Weak, Disagreement, Random, COUNT };

constexpr std::array<ScoreType, static_cast<size_t>(ScoreType::COUNT)> score_types = {
    ScoreType::Ambiguity,
    ScoreType::Weak,
    ScoreType::Disagreement,
    ScoreType::Random,
};

struct ChipConstructor {
  ChipConstructor(ChipScoreMetadata prediction_metadata_, float ambiguity_score_, float weak_score_,
                  float disagreement_score_, float random_score_, std::string ambiguous_closest_category_,
                  std::string ambiguous_second_closest_category_, std::string weak_closest_category_,
                  std::string disagreement_closest_category_, float dw_detection_x_, float dw_detection_y_,
                  float required_length_, int64_t timestamp_ms_, bool embeddings_available_)
      : prediction_metadata(prediction_metadata_), ambiguity_score(ambiguity_score_), weak_score(weak_score_),
        disagreement_score(disagreement_score_), random_score(random_score_),
        ambiguous_closest_category(ambiguous_closest_category_),
        ambiguous_second_closest_category(ambiguous_second_closest_category_),
        weak_closest_category(weak_closest_category_), disagreement_closest_category(disagreement_closest_category_),
        dw_prediction_x(dw_detection_x_), dw_prediction_y(dw_detection_y_), required_length(required_length_),
        timestamp_ms(timestamp_ms_), embeddings_available(embeddings_available_) {}

  float get_score_by_type(ScoreType score_type);

  ChipScoreMetadata prediction_metadata;
  float ambiguity_score;
  float weak_score;
  float disagreement_score;
  float random_score;
  std::string ambiguous_closest_category;
  std::string ambiguous_second_closest_category;
  std::string weak_closest_category;
  std::string disagreement_closest_category;
  float dw_prediction_x;
  float dw_prediction_y;
  float required_length;
  int64_t timestamp_ms;
  bool embeddings_available;
};

class ChipInterestScorer : public CVNodeImpl {
public:
  ChipInterestScorer(
      camera::Camera camera, Input<deepweed::DeepweedOutput> input,
      std::shared_ptr<std::map<std::string, std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>>>>
          distance_camera_buffers,
      std::shared_ptr<std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>> score_queues,
      std::shared_ptr<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>> model_registry,
      std::shared_ptr<ImplementStatus> implement_status, std::shared_ptr<GeoData> geo_data,
      std::shared_ptr<DistanceStatus> velocity_status, std::shared_ptr<AimbotState> aimbot_state,
      std::shared_ptr<weed_tracking::WeedTrackingClient> weed_tracking_client,
      std::shared_ptr<carbon::config::ConfigTree> dum_config)
      : CVNodeImpl(fmt::format("/chip_interest_scorer/{}", camera.get_info().camera_id)), camera_(camera),
        input_(input), distance_camera_buffers_(distance_camera_buffers), score_queues_(score_queues),
        model_registry_(model_registry), implement_status_(implement_status), geo_data_(geo_data),
        velocity_status_(velocity_status), aimbot_state_(aimbot_state), weed_tracking_client_(weed_tracking_client),
        last_distance_(0), scoped_tree_(dum_config), cfg_(scoped_tree_.tree()) {}

  struct Config {
    std::atomic<bool> capture_enabled;
    std::atomic<int> max_age_seconds;
    Config(std::shared_ptr<carbon::config::ConfigTree> tree);
    void update(std::shared_ptr<carbon::config::ConfigTree> tree);
  };

protected:
  std::tuple<bool, double> capture_guard();

  void prune_chip_construction_buffer();

  void merge_metadata_and_move_to_score_queue();

  int64_t tick() override;

  void close_connectors() override { input_.close(); }

private:
  camera::Camera camera_;
  Input<deepweed::DeepweedOutput> input_;
  std::shared_ptr<std::map<std::string, std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>>>>
      distance_camera_buffers_;
  std::shared_ptr<std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>> score_queues_;
  std::shared_ptr<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>> model_registry_;
  std::shared_ptr<ImplementStatus> implement_status_;
  std::shared_ptr<GeoData> geo_data_;
  std::shared_ptr<DistanceStatus> velocity_status_;
  std::shared_ptr<AimbotState> aimbot_state_;
  std::shared_ptr<weed_tracking::WeedTrackingClient> weed_tracking_client_;
  double last_distance_;
  std::unordered_map<std::string,
                     std::unordered_map<int64_t, std::unordered_map<uint32_t, std::shared_ptr<ChipConstructor>>>>
      chip_construction_buffer_;

  carbon::config::AtomicFlagConfigScopedCallback scoped_tree_;
  Config cfg_;
  int64_t milliseconds_since_epoch_;

  bool maybe_push_ambiguity_score(float score, std::string closest_category, std::string second_closest_category,
                                  camera::CameraImage camera_image, ChipScoreMetadata prediction_metadata,
                                  float required_length, float x, float y);

  bool maybe_push_weak_score(float score, std::string closest_category, camera::CameraImage camera_image,
                             ChipScoreMetadata prediction_metadata, float required_length, float x, float y);

  bool maybe_push_disagreement_score(float score, std::string closest_category, camera::CameraImage camera_image,
                                     ChipScoreMetadata prediction_metadata, float required_length, float x, float y);

  bool maybe_push_random_score(float score, camera::CameraImage camera_image, ChipScoreMetadata prediction_metadata,
                               float required_length, float x, float y);

  bool maybe_push_score(float score, std::string score_queue_key, camera::CameraImage camera_image,
                        ChipScoreMetadata prediction_metadata, float required_length, float x, float y);
};

} // namespace nodes
} // namespace runtime
} // namespace cv

#pragma once

#include "cv/runtime/cpp/image_score_queue.h"
#include "cv/runtime/cpp/node.h"
#include "cv/runtime/cpp/nodes/constants.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/aimbot_state.h"
#include "lib/common/cpp/distance_status.h"
#include "lib/common/cpp/fixed_buffer.h"
#include "lib/common/cpp/geo_data.h"
#include "lib/common/cpp/implement_status.h"
#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <cstdlib>
#include <random>
#include <spdlog/spdlog.h>
#include <vector>

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

class ImageInterestScorer : public CVNodeImpl {
public:
  ImageInterestScorer(
      camera::Camera camera, Input<deepweed::DeepweedOutput> input,
      std::map<std::string, std::shared_ptr<ScoreQueue<>>> score_queues,
      std::shared_ptr<std::map<std::string, std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>>>> camera_buffers,
      std::shared_ptr<ImplementStatus> implement_status, std::shared_ptr<GeoData> geo_data,
      std::shared_ptr<DistanceStatus> velocity_status, std::shared_ptr<AimbotState> aimbot_state,
      std::shared_ptr<carbon::config::ConfigTree> deepweed_config)
      : CVNodeImpl(fmt::format("/image_interest_scorer/{}", camera.get_info().camera_id)), input_(input),
        score_queues_(score_queues), camera_(camera), camera_buffers_(camera_buffers),
        implement_status_(implement_status), geo_data_(geo_data), velocity_status_(velocity_status), last_distance_(0),
        aimbot_state_(aimbot_state),
        ambiguous_min_score_(carbon::config::get_global_config_subscriber()->get_config_node("data_upload_manager",
                                                                                             "ambiguous_min_score")),
        ambiguous_max_score_(carbon::config::get_global_config_subscriber()->get_config_node("data_upload_manager",
                                                                                             "ambiguous_max_score")),
        deepweed_config_(deepweed_config) {}

  std::tuple<bool, bool, double> regular_emergency_guard() {
    auto [lifted, estopped] = implement_status_->retrieve();
    (void)estopped;
    auto next_distance = velocity_status_->retrieve();
    if (last_distance_ == 0) {
      last_distance_ = next_distance;
      return std::tuple<bool, bool, double>(false, false, next_distance);
    }

    auto stationary = std::abs(next_distance - last_distance_) < 0.25 * kMmInFeet;
    auto [geo_lla, geo_ecef] = geo_data_->retrieve();
    auto [aimbot_running, lasers_armed] = aimbot_state_->retrieve();

    auto good_for_regular = !(lifted || stationary || geo_lla.get_timestamp_ms() == 0 ||
                              geo_ecef.get_timestamp_ms() == 0 || !aimbot_running || !lasers_armed);
    auto good_for_emergency =
        !(lifted || stationary || geo_lla.get_timestamp_ms() == 0 || geo_ecef.get_timestamp_ms() == 0);

    if (lifted) {
      last_distance_ = next_distance;
    }

    return std::tuple<bool, bool, double>(good_for_regular, good_for_emergency, next_distance);
  }

  void push_or_update(std::string queue_name, int64_t timestamp_ms, double score,
                      const proto::DeepweedOutput &deepweed_output) {
    auto [good_for_regular, good_for_emergency, next_distance] = regular_emergency_guard();
    auto starts_with_emergency = queue_name.rfind("emergency", 0) == 0;

    // If score is a regular score and it's not good_for_regular OR score is emergency and not good_for_emergency, skip
    // image
    if ((!starts_with_emergency && !good_for_regular) || (starts_with_emergency && !good_for_emergency)) {
      return;
    }

    last_distance_ = next_distance;

    try {
      auto camera_buffer = camera_buffers_->at(camera_.get_info().camera_id);
      auto camera_image_opt = camera_buffer->get_opt(timestamp_ms);
      if (camera_image_opt) {
        score_queues_.at(queue_name)->push({score, *camera_image_opt, deepweed_output});
      }
    } catch (const maka_error &e) {
      spdlog::error("Tried to push to {}. Unable to push image: {}", queue_name, e.what());
    }
  }

protected:
  int64_t tick() override {
    std::vector<double> weed_detection_scores;
    std::vector<double> crop_detection_scores;

    // Grab deepweed output from input connector
    set_state(CVNodeState::kPulling);
    auto deepweed_output = input_.pop();
    set_state(CVNodeState::kProcessing);
    if (camera_buffers_->count(camera_.get_info().camera_id) == 0) {
      // can happen during startup while running simulation, deepweed_infer node was too fast and
      // we got here before camera buffer was added to the map in main.cpp.
      return deepweed_output.timestamp_ms;
    }
    auto camera_buffer = camera_buffers_->at(camera_.get_info().camera_id);

    auto [good_for_regular, good_for_emergency, next_distance] = regular_emergency_guard();

    if (good_for_regular || good_for_emergency) {
      last_distance_ = next_distance;
    } else {
      return deepweed_output.timestamp_ms;
    }

    try {
      // First check if image exists in buffer and then if it does exist, get it and compute the
      // various interest score metrics.
      auto camera_image_opt = camera_buffer->get_opt(deepweed_output.timestamp_ms);
      if (camera_image_opt.has_value()) {
        auto camera_image = *camera_image_opt;

        // Populate a matrix of detection confidences
        for (uint32_t index = 0; index < deepweed_output.detections.size(); index++) {
          auto detection = deepweed_output.detections[index];
          if (detection.hit_class == proto::HitClass::WEED) {
            weed_detection_scores.push_back(detection.score);
          } else if (detection.hit_class == proto::HitClass::CROP) {
            crop_detection_scores.push_back(detection.score);
          }
        }

        // Recency score
        auto recency_score = (double)camera_image.timestamp_ms;
        // Margin max score generation
        // NOTE: We are not currently computing scores based on multiple classes.
        auto margin_max_weed_score = compute_margin_max(weed_detection_scores);
        auto margin_max_crop_score = compute_margin_max(crop_detection_scores);

        auto driptape_score = compute_driptape_count(deepweed_output);

        auto ambiguous_min_score = (double)ambiguous_min_score_->get_value<float>();
        auto ambiguous_max_score = (double)ambiguous_max_score_->get_value<float>();
        auto ambiguous_weed_count_score =
            compute_ambiguous_count(weed_detection_scores, ambiguous_min_score, ambiguous_max_score);
        auto ambiguous_crop_count_score =
            compute_ambiguous_count(crop_detection_scores, ambiguous_min_score, ambiguous_max_score);
        auto unknown_plant_count = compute_unknown_plant_count(deepweed_output);

        cv::runtime::proto::DeepweedOutput p_deepweed_output;
        DeepweedOutputToProtobuf::to_protobuf(&deepweed_output, &p_deepweed_output);

        if (good_for_regular) {
          score_queues_.at("recency")->push({recency_score, camera_image, p_deepweed_output});
          score_queues_.at("weed_margin_max")->push({margin_max_weed_score, camera_image, p_deepweed_output});
          score_queues_.at("crop_margin_max")->push({margin_max_crop_score, camera_image, p_deepweed_output});
          score_queues_.at("ambiguous_weed_count")->push({ambiguous_weed_count_score, camera_image, p_deepweed_output});
          score_queues_.at("ambiguous_crop_count")->push({ambiguous_crop_count_score, camera_image, p_deepweed_output});
          score_queues_.at("driptape")->push({driptape_score, camera_image, p_deepweed_output});
          score_queues_.at("unknown_plant")->push({unknown_plant_count, camera_image, p_deepweed_output});
        }

        if (good_for_emergency) {
          score_queues_.at("emergency_recency")->push({recency_score, camera_image, p_deepweed_output});
          score_queues_.at("emergency_weed_margin_max")->push({margin_max_weed_score, camera_image, p_deepweed_output});
          score_queues_.at("emergency_crop_margin_max")->push({margin_max_crop_score, camera_image, p_deepweed_output});
          score_queues_.at("emergency_ambiguous_weed_count")
              ->push({ambiguous_weed_count_score, camera_image, p_deepweed_output});
          score_queues_.at("emergency_ambiguous_crop_count")
              ->push({ambiguous_crop_count_score, camera_image, p_deepweed_output});
          score_queues_.at("emergency_driptape")->push({driptape_score, camera_image, p_deepweed_output});
          score_queues_.at("emergency_unknown_plant")->push({unknown_plant_count, camera_image, p_deepweed_output});
        }

        // NOTE: Weed tracking score generation is not included here as it requires information from aimbot to properly
        // compute a score.
      }
    } catch (const maka_error &e) {
      spdlog::error("Failed to compute interest scores.");
    }

    return deepweed_output.timestamp_ms;
  }

  void close_connectors() override { input_.close(); }

  double compute_margin_max(std::vector<double> detection_scores) {
    // If there are no detections for an image we apply a score of zero for the margin-max metric.
    double score = 0.0;
    if (detection_scores.size() > 0) {
      auto margin_scores = compute_margin_scores_(detection_scores);
      score = *std::max_element(margin_scores.begin(), margin_scores.end());
    }
    return score;
  }

  double compute_ambiguous_count(std::vector<double> detection_scores, double ambiguous_count_min,
                                 double ambiguous_count_max) {
    int count = 0;
    for (double detection_score : detection_scores) {
      if (detection_score > ambiguous_count_min && detection_score < ambiguous_count_max) {
        count += 1;
      }
    }

    return (double)count;
  }

  double compute_unknown_plant_count(deepweed::DeepweedOutput output) {
    auto wpt = deepweed_config_->get_node("weed_point_threshold")->get_value<double>();
    auto cpt = deepweed_config_->get_node("crop_point_threshold")->get_value<double>();
    int count = 0;
    for (uint32_t index = 0; index < output.detections.size(); index++) {
      auto detection = output.detections[index];
      if (detection.hit_class == proto::HitClass::PLANT && detection.weed_score <= wpt && detection.crop_score <= cpt) {
        count += 1;
      }
    }

    return (double)count;
  }

  double compute_driptape_count(deepweed::DeepweedOutput output) {
    int count = 0;
    for (uint32_t i = 0; i < output.mask_width * output.mask_height * output.mask_channels; i++) {
      if (output.mask[i] > 128) {
        count += 1;
      }
    }

    return (double)count;
  }

  // TODO: (ZACH) Implement these:
  // float compute_margin_average_(std::vector<std::vector<float>> model_outputs) { return 1.0; }
  // float compute_margin_sum_(std::vector<std::vector<float>> model_outputs) { return 1.0; }

  std::vector<double> compute_margin_scores_(std::vector<double> detection_scores) {
    // Computes the margin score or 1vs2 score utilized in this paper: https://arxiv.org/pdf/1809.09875.pdf
    // NOTE: This implementation has been modified to only work on a single score instead of a distribution of scores.
    //       It is assumed that the scores are the model confidence of the object being a weed. This allows us to also
    //       compute the confidence that its not a weed (1 - detection_score) and then apply the margin score formula
    //       to this two valued probability distribution.
    std::vector<double> margin_scores;
    for (double detection_score : detection_scores) {
      double margin_score = std::pow(1 - std::abs(2 * detection_score - 1), 2.0);
      margin_scores.push_back(margin_score);
    }
    return margin_scores;
  }

private:
  Input<deepweed::DeepweedOutput> input_;
  std::map<std::string, std::shared_ptr<ScoreQueue<>>> score_queues_;
  camera::Camera camera_;
  std::shared_ptr<std::map<std::string, std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>>>> camera_buffers_;
  std::uniform_real_distribution<float> uniform_distribution_;
  std::mt19937 random_generator_;
  std::shared_ptr<ImplementStatus> implement_status_;
  std::shared_ptr<GeoData> geo_data_;
  std::shared_ptr<DistanceStatus> velocity_status_;
  double last_distance_;
  std::shared_ptr<AimbotState> aimbot_state_;
  std::shared_ptr<carbon::config::ConfigTree> ambiguous_min_score_;
  std::shared_ptr<carbon::config::ConfigTree> ambiguous_max_score_;
  std::shared_ptr<carbon::config::ConfigTree> deepweed_config_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv

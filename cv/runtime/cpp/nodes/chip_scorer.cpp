#include "cv/runtime/cpp/nodes/chip_scorer.h"

#include "cv/runtime/cpp/image_score_queue.h"
#include "cv/runtime/cpp/nodes/constants.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/camera/cpp/camera_image.h"
#include "lib/common/cpp/interest_scores.hpp"
#include "weed_tracking/proto/weed_tracking.grpc.pb.h"
#include <config/client/cpp/config_subscriber.hpp>
#include <fmt/format.h>
#include <spdlog/spdlog.h>
#include <vector>

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

float ChipConstructor::get_score_by_type(ScoreType score_type) {
  switch (score_type) {
  case ScoreType::Ambiguity:
    return ambiguity_score;
  case ScoreType::Weak:
    return weak_score;
  case ScoreType::Disagreement:
    return disagreement_score;
  default:
    return random_score;
  }
}

std::tuple<bool, double> ChipInterestScorer::capture_guard() {
  auto [lifted, estopped] = implement_status_->retrieve();
  (void)estopped;
  auto next_distance = velocity_status_->retrieve();
  if (last_distance_ == 0) {
    last_distance_ = next_distance;
    return std::tuple<bool, double>(false, next_distance);
  }

  auto stationary = std::abs(next_distance - last_distance_) < 0.25 * kMmInFeet;
  auto [geo_lla, geo_ecef] = geo_data_->retrieve();
  auto good_to_capture = !(lifted || stationary || geo_lla.get_timestamp_ms() == 0 || geo_ecef.get_timestamp_ms() == 0);
  good_to_capture = good_to_capture && cfg_.capture_enabled;

  if (lifted) {
    last_distance_ = next_distance;
  }

  return std::tuple<bool, double>(good_to_capture, next_distance);
}

void ChipInterestScorer::prune_chip_construction_buffer() {
  // For each camera, find the oldest timestamp in buffer. Delete all timestamps that are older than that timestamp
  for (auto cam_it = distance_camera_buffers_->begin(); cam_it != distance_camera_buffers_->end(); cam_it++) {
    auto oldest_timestamp_ms = std::numeric_limits<int64_t>::max();
    auto keys = cam_it->second->keys();
    for (auto time_it = keys.begin(); time_it != keys.end(); time_it++) {
      if (*time_it < oldest_timestamp_ms) {
        oldest_timestamp_ms = *time_it;
      }
    }

    if (chip_construction_buffer_.count(cam_it->first) == 0)
      continue;

    for (auto time_it = chip_construction_buffer_.at(cam_it->first).begin();
         time_it != chip_construction_buffer_.at(cam_it->first).end();) {
      if (time_it->first < oldest_timestamp_ms) {
        chip_construction_buffer_.at(cam_it->first).erase(time_it++);
      } else {
        ++time_it;
      }
    }
  }
}

void ChipInterestScorer::merge_metadata_and_move_to_score_queue() {
  if (chip_construction_buffer_.empty())
    return;

  prune_chip_construction_buffer();

  weed_tracking::GetTrajectoryMetadataResponse metadata_response;
  try {
    metadata_response = weed_tracking_client_->get_trajectory_metadata();
  } catch (maka_error &e) {
    spdlog::warn("Failed to retrieve trajectory metadata: {}", e.what());
    return;
  }

  auto trajectory_metadata = metadata_response.metadata();
  for (auto i = 0; i < trajectory_metadata.size(); i++) {
    auto cam_id = trajectory_metadata[i].cam_id();
    auto band_status = trajectory_metadata[i].band_status();

    std::vector<std::pair<int64_t, uint32_t>> detection_timestamps_and_ids;

    std::shared_ptr<ChipConstructor> best_chip;
    bool success = false;
    float best_score = 0;

    bool score_type_selected = false;
    auto score_type = ScoreType::Random;

    for (auto j = 0; j < trajectory_metadata[i].tracked_item_metadata().size(); j++) {
      auto detection_id = trajectory_metadata[i].tracked_item_metadata()[j].detection_id();
      auto timestamp_ms = trajectory_metadata[i].tracked_item_metadata()[j].timestamp_ms();

      if (distance_camera_buffers_->count(cam_id) == 0) {
        continue;
      } else if (!distance_camera_buffers_->at(cam_id)->contains(timestamp_ms)) {
        continue;
      }

      try {
        if (!score_type_selected) {
          if (chip_construction_buffer_.at(cam_id).at(timestamp_ms).at(detection_id)->embeddings_available) {
            auto random_pos = (size_t)(std::rand() % score_types.size());
            score_type = score_types[random_pos];
          }
          score_type_selected = true;
        }
        if (chip_construction_buffer_.at(cam_id).at(timestamp_ms).at(detection_id)->get_score_by_type(score_type) >
            best_score) {
          best_chip = chip_construction_buffer_.at(cam_id).at(timestamp_ms).at(detection_id);
          success = true;
          detection_timestamps_and_ids.push_back(std::make_pair(timestamp_ms, detection_id));
        }
      } catch (const std::out_of_range &e) {
        (void)e;
        continue;
      }
    }

    if (!success)
      continue;

    // Remove all chips from this trajectory so that we won't upload again
    for (auto detection_timestamp_and_id : detection_timestamps_and_ids) {
      try {
        chip_construction_buffer_.at(cam_id)
            .at(detection_timestamp_and_id.first)
            .erase(detection_timestamp_and_id.second);
      } catch (const std::out_of_range &e) {
        (void)e;
        continue;
      }
    }

    auto prediction_metadata = best_chip->prediction_metadata;
    prediction_metadata.band_status = band_status;
    auto ambiguity_score = best_chip->ambiguity_score;
    auto weak_score = best_chip->weak_score;
    auto disagreement_score = best_chip->disagreement_score;
    auto random_score = best_chip->random_score;
    auto ambiguous_closest_category = best_chip->ambiguous_closest_category;
    auto ambiguous_second_closest_category = best_chip->ambiguous_second_closest_category;
    auto weak_closest_category = best_chip->weak_closest_category;
    auto disagreement_closest_category = best_chip->disagreement_closest_category;
    auto dw_prediction_x = best_chip->dw_prediction_x;
    auto dw_prediction_y = best_chip->dw_prediction_y;
    auto required_length = best_chip->required_length;

    camera::CameraImage camera_image;
    try {
      auto cam_image_opt = distance_camera_buffers_->at(cam_id)->get_opt(best_chip->timestamp_ms);
      if (!cam_image_opt.has_value()) {
        continue;
      }
      camera_image = cam_image_opt.value();
    } catch (const std::out_of_range &e) {
      spdlog::warn("Cam image is out of range: {}", e.what());
      continue;
    }

    int64_t success_timestamp = 0;
    if (best_chip->embeddings_available) {
      if (score_type == Ambiguity) {
        success_timestamp = maybe_push_ambiguity_score(
            ambiguity_score, ambiguous_closest_category, ambiguous_second_closest_category, camera_image,
            prediction_metadata, required_length, dw_prediction_x, dw_prediction_y);
      } else if (score_type == Weak) {
        success_timestamp = maybe_push_weak_score(weak_score, weak_closest_category, camera_image, prediction_metadata,
                                                  required_length, dw_prediction_x, dw_prediction_y);
      } else if (score_type == Disagreement) {
        success_timestamp =
            maybe_push_disagreement_score(disagreement_score, disagreement_closest_category, camera_image,
                                          prediction_metadata, required_length, dw_prediction_x, dw_prediction_y);
      }
    }

    if (score_type == Random || success_timestamp == 0)
      maybe_push_random_score(random_score, camera_image, prediction_metadata, required_length, dw_prediction_x,
                              dw_prediction_y);
  }
}

ChipInterestScorer::Config::Config(std::shared_ptr<carbon::config::ConfigTree> tree) { update(tree); }
void ChipInterestScorer::Config::update(std::shared_ptr<carbon::config::ConfigTree> tree) {
  this->capture_enabled = tree->get_node("chip_capture/enabled")->get_value<bool>();
  this->max_age_seconds = tree->get_node("chip_capture/max_age_seconds")->get_value<int>();
}

int64_t ChipInterestScorer::tick() {
  set_state(CVNodeState::kPulling);
  auto deepweed_output = input_.pop();
  set_state(CVNodeState::kProcessing);

  if (scoped_tree_.reload_required()) {
    cfg_.update(scoped_tree_.tree());
  }

  auto [good_to_capture, next_distance] = capture_guard();

  if (!cfg_.capture_enabled) {
    for (auto it = score_queues_->begin(); it != score_queues_->end(); it++) {
      if (it->second->size() > 0) {
        it->second->flush();
      }
    }
  }

  if (!good_to_capture) {
    return deepweed_output.timestamp_ms;
  }

  last_distance_ = next_distance;

  auto embedding_classes = deepweed_output.embedding_categories;
  auto embeddings_available = embedding_classes.size() > 0;

  if (distance_camera_buffers_->count(camera_.get_info().camera_id) == 0) {
    return deepweed_output.timestamp_ms;
  }
  auto camera_buffer = distance_camera_buffers_->at(camera_.get_info().camera_id);

  auto model_id = model_registry_->get_external_metadata_by_use_case(lib::common::model::ModelUseCase::kPredict)["id"];

  auto time_since_epoch = std::chrono::system_clock::now().time_since_epoch();
  milliseconds_since_epoch_ = std::chrono::duration_cast<std::chrono::milliseconds>(time_since_epoch).count();

  auto camera_image_opt = camera_buffer->get_opt(deepweed_output.timestamp_ms);
  if (camera_image_opt.has_value()) {
    auto camera_image = *camera_image_opt;
    for (auto &dw_prediction : deepweed_output.detections) {
      auto radius = dw_prediction.size;

      auto required_length = (float)std::max(kMinChipLength, (int)(2 * 1.1 * radius));

      float ambiguity_score = 0.0f;
      float weak_score = 0.0f;
      float disagreement_score = 0.0f;
      std::string ambiguous_closest_category = "";
      std::string ambiguous_second_closest_category = "";
      std::string weak_closest_category = "";
      std::string disagreement_closest_category = "";
      if (embeddings_available) {
        std::tie(ambiguity_score, ambiguous_closest_category, ambiguous_second_closest_category) =
            ambiguity_interest_score(dw_prediction.embedding_category_distances, embedding_classes);
        std::tie(weak_score, weak_closest_category) =
            weak_interest_score(dw_prediction.embedding_category_distances, embedding_classes);
        std::tie(disagreement_score, disagreement_closest_category) =
            disagreement_interest_score(dw_prediction.embedding_category_distances, dw_prediction.weed_score,
                                        dw_prediction.crop_score, embedding_classes);
      }

      float random_score = random_interest_score();

      ChipScoreMetadata prediction_metadata;
      prediction_metadata.x = required_length / 2;
      prediction_metadata.y = required_length / 2;
      prediction_metadata.radius = radius;
      prediction_metadata.model_id = model_id;
      std::map<std::string, float> scores{
          {"weed", dw_prediction.weed_score}, {"crop", dw_prediction.crop_score}, {"plant", dw_prediction.plant_score}};
      prediction_metadata.scores = scores;
      for (size_t i = 0; i < embedding_classes.size(); i++) {
        prediction_metadata.embedding_distances[embedding_classes[i]] = dw_prediction.embedding_category_distances[i];
      }

      auto chip_construction_information = std::make_shared<ChipConstructor>(
          prediction_metadata, ambiguity_score, weak_score, disagreement_score, random_score,
          ambiguous_closest_category, ambiguous_second_closest_category, weak_closest_category,
          disagreement_closest_category, dw_prediction.x, dw_prediction.y, required_length,
          deepweed_output.timestamp_ms, embeddings_available);

      chip_construction_buffer_[camera_.get_info().camera_id][deepweed_output.timestamp_ms]
                               [dw_prediction.detection_id] = chip_construction_information;
    }
  }
  merge_metadata_and_move_to_score_queue();

  set_state(CVNodeState::kPushing);

  return deepweed_output.timestamp_ms;
}

bool ChipInterestScorer::maybe_push_ambiguity_score(float score, std::string closest_category,
                                                    std::string second_closest_category,
                                                    camera::CameraImage camera_image,
                                                    ChipScoreMetadata prediction_metadata, float required_length,
                                                    float x, float y) {
  std::vector<std::string> names{closest_category, second_closest_category};
  std::sort(names.begin(), names.end());
  auto score_queue_key = fmt::format("ambiguity_{}_{}", names[0], names[1]);
  return maybe_push_score(score, score_queue_key, camera_image, prediction_metadata, required_length, x, y);
}

bool ChipInterestScorer::maybe_push_weak_score(float score, std::string closest_category,
                                               camera::CameraImage camera_image, ChipScoreMetadata prediction_metadata,
                                               float required_length, float x, float y) {
  auto score_queue_key = fmt::format("weak_{}", closest_category);

  return maybe_push_score(score, score_queue_key, camera_image, prediction_metadata, required_length, x, y);
}

bool ChipInterestScorer::maybe_push_disagreement_score(float score, std::string closest_category,
                                                       camera::CameraImage camera_image,
                                                       ChipScoreMetadata prediction_metadata, float required_length,
                                                       float x, float y) {
  auto score_queue_key = fmt::format("disagreement_{}", closest_category);

  return maybe_push_score(score, score_queue_key, camera_image, prediction_metadata, required_length, x, y);
}

bool ChipInterestScorer::maybe_push_random_score(float score, camera::CameraImage camera_image,
                                                 ChipScoreMetadata prediction_metadata, float required_length, float x,
                                                 float y) {
  auto score_queue_key = "random_chip";

  return maybe_push_score(score, score_queue_key, camera_image, prediction_metadata, required_length, x, y);
}

bool ChipInterestScorer::maybe_push_score(float score, std::string score_queue_key, camera::CameraImage camera_image,
                                          ChipScoreMetadata prediction_metadata, float required_length, float x,
                                          float y) {
  try {
    if (score_queues_->count(score_queue_key) == 0) {
      score_queues_->emplace(score_queue_key, std::make_shared<ScoreQueue<ChipScoreQueueObject>>(1));
    }

    auto score_queue = score_queues_->at(score_queue_key);

    score_queue->remove_old_points(milliseconds_since_epoch_, cfg_.max_age_seconds * 1000);

    bool crop_and_push = false;

    if (score_queue->size() >= 1) {
      if (score > score_queue->peek_max().score) {
        crop_and_push = true;
      }
    } else {
      crop_and_push = true;
    }

    if (crop_and_push) {
      auto push_camera_image = camera_image.pad_and_crop((int)x, (int)y, (int)required_length, (int)required_length);

      score_queues_->at(score_queue_key)->push({score, push_camera_image, prediction_metadata});
      return true;
    }
  } catch (const std::out_of_range &e) {
    (void)e;
  }
  return false;
}

} // namespace nodes
} // namespace runtime
} // namespace cv
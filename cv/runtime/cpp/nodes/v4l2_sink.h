#pragma once

#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/cuda_util.h"
#include "v4l2_utils/cpp/v4l2_writer.hpp"

#include <config/tree/cpp/config_atomic_accessor.hpp>
#include <config/tree/cpp/config_tree.hpp>

#include <algorithm>
#include <array>
#include <assert.h>
#include <atomic>
#include <c10/cuda/CUDAGuard.h>
#include <c10/cuda/CUDAStream.h>
#include <cctype>
#include <cmath>
#include <cstdio>
#include <fcntl.h>
#include <filesystem>
#include <fmt/format.h>
#include <iostream>
#include <locale>
#include <memory>
#include <stdexcept>
#include <stdio.h>
#include <stdlib.h>
#include <string>

#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common::camera;

class V4L2ImageSink : public CVNodeImpl {
public:
  V4L2ImageSink(Camera camera, std::string buffer_name, int channels, bool stage_on_cpu, bool transpose,
                Input<CameraImage> input, std::shared_ptr<carbon::config::ConfigTree> enabled_cfg, float scale = 1.0f)
      : CVNodeImpl(fmt::format("/V4L2_sink/{}", camera.get_info().camera_id)), input_(input),
        scale_(std::fabs(scale - 1.0f) < 0.01f ? 1.0f : scale), buffer_name_(buffer_name), channels_(channels),
        stage_on_cpu_(stage_on_cpu), v4l2_writer_(buffer_name), transpose_(transpose), buffer_initialized_(false),
        video_buffer_(nullptr), enabled_(enabled_cfg), identifier_(0) {
    if (!v4l2_writer_.valid()) {
      throw std::runtime_error("Failed to create v4l2 device");
    }
    if (channels_ > 0 && channels_ <= 4) {
      cv_write_supported_ = true;
      cv_type_ = CV_MAKETYPE(CV_8U, channels_);
    } else {
      cv_write_supported_ = false;
    }
  }

  ~V4L2ImageSink() {
    if (video_buffer_ != nullptr) {
      delete[] video_buffer_;
    }
  }
  void set_identifier(uint32_t id) { identifier_ = id; }

protected:
  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    CameraImage cam_image = input_.pop();
    if (enabled_.get_value()) {
      set_state(CVNodeState::kProcessing);

      if (!cuda_stream_) {
        cuda_stream_.reset(
            new c10::cuda::CUDAStream(c10::cuda::getStreamFromPool(true, cam_image.image.device().index())));
      }

      c10::cuda::CUDAStreamGuard stream_guard(*cuda_stream_);
      if (scale_ != 1.0f) {
        cam_image = cam_image.rescale(scale_);
      }

      auto height = cam_image.get_height();
      auto width = cam_image.get_width();
      auto image = cam_image.image;

      init_buffers_if_needed(width, height);

      // If image contains depth, prepend it.
      if (cam_image.depth.defined() && channels_ == 4) {
        image = torch::cat({cam_image.depth.unsqueeze(0), image}, 0);
      }

      // Convert CHW -> HWC.
      image = image.permute({1, 2, 0});

      if (stage_on_cpu_) {
        // This may help pre-Ampere GPUs which have limited memcpy engines. Copy to shared memory is slower compared to
        // plain CPU copy.
        with_device device_guard(image.device().index());
        image = image.to(torch::TensorOptions().pinned_memory(true).dtype(torch::kUInt8));
      }

      cuda_stream_->synchronize();
      try {
        set_state(CVNodeState::kPushing);

        // We can't write directly to v4l2 from gpu memory
        // There may be ways to optimize this through mmap in the future, though people are reporting questionable
        // performance with mmap online
        torch_video_buffer_.copy_(image);
        add_id(width, height);
        auto ret_code = v4l2_writer_.write(video_buffer_, width * height * channels_);
        if (ret_code < 0) {
          spdlog::error("V4L2 Write Error: {} Written: {} Expected: {} WxH: {}x{}", buffer_name_, std::strerror(errno),
                        width * height * channels_, width, height);
        }

      } catch (const std::exception &e) {
        spdlog::error("V4L2 Sink Exception: {}", e.what());
      }

    } else {
      set_state(CVNodeState::kProcessing);
    }
    return cam_image.timestamp_ms;
  }

  void close_connectors() override { input_.close(); }

private:
  void init_buffers_if_needed(int width, int height) {
    if (buffer_initialized_) {
      return;
    }

    video_buffer_ = new __u8[width * height * channels_];
    torch_video_buffer_ = torch::from_blob(video_buffer_, {height, width, channels_}, torch::kUInt8);
    if (!v4l2_writer_.set_format(width, height)) {
      throw std::runtime_error("Failed to set format v4l2 device");
    }

    buffer_initialized_ = true;
  }

  void add_id(int width, int height) {
    if (!cv_write_supported_ || identifier_ == 0) {
      return;
    }
    auto id_str = fmt::format("{}", identifier_);

    const auto font = cv::FONT_HERSHEY_COMPLEX_SMALL;
    const double font_scale = 1;
    const int thickness = 1;
    const uint32_t padding = 5;

    int baseline = 0;
    auto text_size = cv::getTextSize(id_str, font, font_scale, thickness, &baseline);

    cv::Mat m(height, width, cv_type_, video_buffer_);
    cv::putText(m, id_str, cv::Point(width - text_size.width - padding, text_size.height + padding), font, font_scale,
                CV_RGB(0, 0, 0), thickness + 2);
    cv::putText(m, id_str, cv::Point(width - text_size.width - padding, text_size.height + padding), font, font_scale,
                CV_RGB(255, 219, 0), thickness);
  }

  Input<CameraImage> input_;
  const float scale_;
  std::string buffer_name_;
  int channels_;
  bool stage_on_cpu_;
  std::unique_ptr<c10::cuda::CUDAStream> cuda_stream_;
  carbon::v4l2::V4l2Writer v4l2_writer_;
  bool transpose_;
  bool buffer_initialized_;
  __u8 *video_buffer_;
  torch::Tensor torch_video_buffer_;
  carbon::config::ConfigAtomicAccessor<bool> enabled_;
  std::atomic<uint32_t> identifier_;
  bool cv_write_supported_;
  int cv_type_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv

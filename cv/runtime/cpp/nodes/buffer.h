#pragma once

#include <c10/cuda/CUDAGuard.h>

#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/fixed_buffer.h"

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

class Buffer : public CVNodeImpl {
public:
  Buffer(camera::Camera camera, Input<camera::CameraImage> input, int max_items, std::optional<int> gpu_id,
         bool simulator)
      : CVNodeImpl(fmt::format("/buffer/{}", camera.get_info().camera_id)), input_(input), gpu_id_(gpu_id),
        buffer_(new FixedBuffer<int64_t, camera::CameraImage>((size_t)max_items)), simulator_(simulator) {}

  std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>> get_buffer() const { return buffer_; }

protected:
  int64_t tick() override {
    c10::cuda::OptionalCUDAStreamGuard stream_guard;
    set_state(CVNodeState::kPulling);
    camera::CameraImage cam_image = input_.pop();
    set_state(CVNodeState::kProcessing);
    if (!buffer_->contains(cam_image.timestamp_ms)) {
      if (gpu_id_) {
        if (!cuda_stream_) {
          cuda_stream_.reset(
              new c10::cuda::CUDAStream(c10::cuda::getStreamFromPool(true, cam_image.image.device().index())));
        }
        stream_guard.reset_stream(*cuda_stream_);
        cam_image = cam_image.cuda(*gpu_id_);
        cuda_stream_->synchronize();
      }
      buffer_->push(cam_image.timestamp_ms, cam_image);
    }
    return cam_image.timestamp_ms;
  }

  void close_connectors() override { input_.close(); }

private:
  Input<camera::CameraImage> input_;

  std::optional<int> gpu_id_;
  std::shared_ptr<FixedBuffer<int64_t, camera::CameraImage>> buffer_;
  std::unique_ptr<c10::cuda::CUDAStream> cuda_stream_;
  bool simulator_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv

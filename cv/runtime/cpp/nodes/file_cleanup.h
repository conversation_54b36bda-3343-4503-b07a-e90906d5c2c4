#pragma once

#include <ctime>
#include <iomanip>
#include <iostream>
#include <sstream>

#include "lib/common/cpp/time.h"
#include <chrono>
#include <filesystem>

#include "cv/p2p/output.h"

namespace cv {
namespace runtime {
namespace nodes {

class FileSystemCleaner : public CVNodeImpl {
public:
  FileSystemCleaner(std::string kLightweightBurstDirectory, std::string kFullBurstDirectory)
      : CVNodeImpl("FileSystemCleaner"), kLightweightBurstDirectory_(kLightweightBurstDirectory),
        kFullBurstDirectory_(kFullBurstDirectory) {}

  int64_t tick() override {
    std::this_thread::sleep_for(std::chrono::seconds(30));
    // TODO This needs to be fixed, currently cleans everything regardless of age
    // clean_directory(kLightweightBurstDirectory_);
    // clean_directory(kFullBurstDirectory_);
    return maka_control_timestamp_ms();
  }

  void clean_directory(std::string directory) {
    // This function loops over the contents of a directory, looks for a trailing iso timestamp and deletes anything
    // older than 24 hours from the the time of execution.

    if (std::filesystem::exists(directory)) {
      std::time_t threshold =
          std::chrono::system_clock::to_time_t(std::chrono::system_clock::now() - std::chrono::hours(6));
      for (const auto &subdir : std::filesystem::directory_iterator(directory)) {
        std::string subdir_path = subdir.path();
        std::string subdir_name = subdir_path.substr(subdir_path.find_last_of("/\\") + 1);
        std::string subdir_basename = remove_extension(subdir_name);
        if (subdir_basename.size() <= 18) {
          // TODO Zach not all names are guaranteed to have 18 characters...
          continue;
        }
        std::string datetime_string = subdir_basename.substr(18);
        std::wstring datetime_wstring(datetime_string.begin(), datetime_string.end());
        std::time_t datetime_t = get_epoch_time(datetime_wstring);
        if (datetime_t < threshold) {
          std::error_code error;
          std::filesystem::remove_all(subdir.path(), error);
        }
      }
    }
  }

  std::string remove_extension(std::string filename) {
    size_t lastdot = filename.find_last_of(".");
    while (true) {
      if (lastdot == std::string::npos) {
        return filename;
      }
      filename = filename.substr(0, lastdot);
      lastdot = filename.find_last_of(".");
    }
  }

  std::time_t get_epoch_time(const std::wstring &dateTime) {
    static const std::wstring dateTimeFormat{L"%Y-%m-%dT%H-%M-%SZ"};
    std::wistringstream ss{dateTime};
    std::tm dt;
    ss >> std::get_time(&dt, dateTimeFormat.c_str());
    return std::mktime(&dt);
  }

private:
  std::string kLightweightBurstDirectory_;
  std::string kFullBurstDirectory_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
#pragma once

#include <fmt/format.h>

#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/shmem/cpp/buffer/latest_buffer.hpp"

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common::camera;
using namespace lib::common::shmem;

class DeepweedThresholdFilterBase : public CVNodeImpl {
public:
  DeepweedThresholdFilterBase(Camera camera, Input<deepweed::DeepweedOutput> input,
                              std::shared_ptr<carbon::config::ConfigTree> deepweed_config, std::string node_name)
      : CVNodeImpl(fmt::format("/" + node_name + "/{}", camera.get_info().camera_id)),
        deepweed_config_(deepweed_config), input_(input), node_name_{node_name} {}

  DeepweedThresholdFilterBase(Camera camera, Input<deepweed::DeepweedOutput> input,
                              std::shared_ptr<carbon::config::ConfigTree> deepweed_config, std::string node_name,
                              std::shared_ptr<std::reference_wrapper<prometheus::Gauge>> num_detections_gauge)
      : CVNodeImpl(fmt::format("/" + node_name + "/{}", camera.get_info().camera_id)),
        deepweed_config_(deepweed_config), input_(input), node_name_{node_name},
        num_detections_gauge_(num_detections_gauge) {}

  Output<deepweed::DeepweedOutput> &get_output() { return output_; }

protected:
  std::shared_ptr<carbon::config::ConfigTree> deepweed_config_;
  virtual float get_weed_threshold() = 0;
  virtual float get_crop_threshold() = 0;
  virtual float get_plant_threshold() = 0;
  virtual bool get_only_plant_points() = 0;

  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    deepweed::DeepweedOutput deepweed_output = input_.pop();
    set_state(CVNodeState::kProcessing);
    std::vector<deepweed::DeepweedDetection> detection_vector;

    if (num_detections_gauge_) {
      num_detections_gauge_->get().Set((double)deepweed_output.detections.size());
    }

    try {
      auto weed_threshold = get_weed_threshold();
      auto crop_threshold = get_crop_threshold();
      auto plant_threshold = get_plant_threshold();
      auto only_plant_points = get_only_plant_points();
      for (size_t i = 0; i < deepweed_output.detections.size(); i++) {
        auto det = deepweed_output.detections[i];
        if (det.hit_class == proto::HitClass::WEED && !only_plant_points) {
          if (det.score > weed_threshold) {
            detection_vector.push_back(det);
          }
        } else if (det.hit_class == proto::HitClass::CROP && !only_plant_points) {
          if (det.score > crop_threshold) {
            detection_vector.push_back(det);
          }
        } else if (det.hit_class == proto::HitClass::PLANT && only_plant_points) {
          if (det.score > plant_threshold) {
            detection_vector.push_back(det);
          }
        }
      }

      deepweed_output.detections = detection_vector;

      set_state(CVNodeState::kPushing);
      output_.push(deepweed_output);
    } catch (std::exception &e) {
      spdlog::error("{} Exception: {}", node_name_, e.what());
    }
    return deepweed_output.timestamp_ms;
  }

  void close_connectors() override {
    input_.close();
    output_.close();
  }

private:
  Input<deepweed::DeepweedOutput> input_;
  Output<deepweed::DeepweedOutput> output_;
  std::string node_name_;
  std::shared_ptr<std::reference_wrapper<prometheus::Gauge>> num_detections_gauge_;
};

class DeepweedThresholdFilter : public DeepweedThresholdFilterBase {
public:
  DeepweedThresholdFilter(Camera camera, Input<deepweed::DeepweedOutput> input,
                          std::shared_ptr<carbon::config::ConfigTree> deepweed_config,
                          std::shared_ptr<std::reference_wrapper<prometheus::Gauge>> num_detections_gauge)
      : DeepweedThresholdFilterBase(camera, input, deepweed_config, "deepweed_threshold_filter", num_detections_gauge) {
  }

protected:
  float get_weed_threshold() override {
    return (float)deepweed_config_->get_node("weed_point_threshold")->get_value<double>();
  }

  float get_crop_threshold() override {
    return (float)deepweed_config_->get_node("crop_point_threshold")->get_value<double>();
  }

  float get_plant_threshold() override {
    return (float)deepweed_config_->get_node("plant_point_threshold")->get_value<double>();
  }

  bool get_only_plant_points() override { return true; }
};

class DeepweedThresholdFilterForWeedingDiagnostics : public DeepweedThresholdFilterBase {
public:
  DeepweedThresholdFilterForWeedingDiagnostics(Camera camera, Input<deepweed::DeepweedOutput> input,
                                               std::shared_ptr<carbon::config::ConfigTree> deepweed_config)
      : DeepweedThresholdFilterBase(camera, input, deepweed_config, "deepweed_diagnostics_filter") {}

protected:
  float get_weed_threshold() override {
    return (float)deepweed_config_->get_node("weed_point_threshold_diagnostics")->get_value<double>();
  }

  float get_crop_threshold() override {
    return (float)deepweed_config_->get_node("crop_point_threshold_diagnostics")->get_value<double>();
  }

  float get_plant_threshold() override {
    return (float)deepweed_config_->get_node("plant_point_threshold")->get_value<double>();
  }

  bool get_only_plant_points() override { return true; }
};

} // namespace nodes
} // namespace runtime
} // namespace cv

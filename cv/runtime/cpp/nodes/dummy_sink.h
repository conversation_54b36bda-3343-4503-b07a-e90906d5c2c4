#pragma once

#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common::camera;

class DummySink : public CVNodeImpl {
public:
  DummySink(Camera camera, Input<CameraImage> input)
      : CVNodeImpl(fmt::format("/dummy_sink/{}", camera.get_info().camera_id)), input_(input) {}

protected:
  int64_t tick() override {
    set_state(CVNodeState::kPulling);
    CameraImage cam_image = input_.pop();
    return cam_image.timestamp_ms;
  }

  void close_connectors() override { input_.close(); }

private:
  Input<CameraImage> input_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
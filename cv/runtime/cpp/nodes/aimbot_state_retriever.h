#pragma once

#include "core/controls/exterminator/controllers/aimbot/cpp/grpc_client.h"
#include "lib/common/cpp/aimbot_state.h"
#include "lib/common/cpp/bedtop_profile.h"
#include "lib/common/cpp/time.h"
#include <chrono>

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common;

class AimbotStateRetriever : public CVNodeImpl {
public:
  AimbotStateRetriever(std::shared_ptr<AimbotState> aimbot_state, std::shared_ptr<BedtopProfile> bedtop_profile,
                       std::shared_ptr<aimbot::AimbotClient> aimbot_client)
      : CVNodeImpl("AimbotStateRetriever"), aimbot_state_(aimbot_state), bedtop_profile_(bedtop_profile),
        aimbot_client_(aimbot_client), connected_(true) {
    char *role_ptr = std::getenv("MAKA_ROLE");
    role_ = "unset";
    if (role_ptr != NULL) {
      role_ = role_ptr;
    }
  }

  int64_t tick() override {
    std::this_thread::sleep_for(std::chrono::seconds(1));
    if (role_ == "veselka_cv") {
      return 0;
    }

    set_state(CVNodeState::kProcessing);
    try {
      auto aimbot_state = aimbot_client_->get_aimbot_state();
      auto running = aimbot_state.running();
      auto armed = aimbot_state.armed();

      if (!connected_) {
        spdlog::info("Aimbot State Retriever connected to aimbot");
        connected_ = true;
      }

      aimbot_state_->update(running, armed);

      auto [height_profile, bbh_offset_mm] = aimbot_client_->get_bedtop_height_profile();

      bedtop_profile_->update(height_profile, bbh_offset_mm);
    } catch (std::exception &e) {
      if (connected_) {
        spdlog::warn("Aimbot State Retriever disconnected from aimbot: {}", e.what());
      }
      connected_ = false;
      return 0;
    }
    return maka_control_timestamp_ms();
  }

private:
  std::shared_ptr<AimbotState> aimbot_state_;
  std::shared_ptr<BedtopProfile> bedtop_profile_;
  std::shared_ptr<aimbot::AimbotClient> aimbot_client_;
  std::string role_;
  bool connected_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
#pragma once

#include <atomic>
#include <condition_variable>
#include <fmt/format.h>
#include <fmt/ostream.h>
#include <future>
#include <map>
#include <memory>
#include <shared_mutex>
#include <spdlog/spdlog.h>
#include <thread>
#include <vector>

#include "connector.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/utils.h"
#include "perf/fps.h"
#include "perf/latency.h"

namespace cv {
namespace runtime {

enum class CVNodeState { kUnset = 0, kPulling = 1, kProcessing = 2, kPushing = 3 };

inline std::string cv_node_state_to_string(CVNodeState state) {
  switch (state) {
  case CVNodeState::kProcessing:
    return "kProcessing";
  case CVNodeState::kPulling:
    return "kPulling";
  case CVNodeState::kPushing:
    return "kPushing";
  default:
    return "";
  }
}

class restart_node_error : public maka_error {
public:
  explicit restart_node_error(size_t stack_skip = 1) : maka_error("Node restart", false, stack_skip + 1) {}
};

class CVNodeImpl {
public:
  CVNodeImpl(const std::string &name)
      : name_(name), state_(CVNodeState::kUnset), last_state_transition_(std::chrono::system_clock::now()) {}
  virtual ~CVNodeImpl() = default;
  DELETE_COPY_AND_MOVE(CVNodeImpl)

  const std::string &get_name() { return name_; }
  CVNodeState get_state() {
    std::shared_lock<std::shared_mutex> lck(mutex_);
    return state_;
  }

  std::map<CVNodeState, std::chrono::milliseconds> get_state_timings() {
    std::shared_lock<std::shared_mutex> lck(mutex_);
    return state_timings_;
  }

protected:
  virtual void prepare(){};
  virtual int64_t tick() = 0;
  virtual void close_connectors(){};
  virtual void finalize(){};

  void set_state(CVNodeState state) {
    std::unique_lock<std::shared_mutex> lck(mutex_);
    if (state == state_)
      return;
    auto current_time = std::chrono::system_clock::now();
    state_timings_[state_] =
        std::chrono::duration_cast<std::chrono::milliseconds>(current_time - last_state_transition_);
    last_state_transition_ = current_time;
    state_ = state;
  }

private:
  std::string name_;
  CVNodeState state_;

  std::shared_mutex mutex_;
  std::chrono::system_clock::time_point last_state_transition_;
  std::map<CVNodeState, std::chrono::milliseconds> state_timings_;

  friend class CVNode;
};

class CVNode {
public:
  template <typename T, typename = std::enable_if<std::is_base_of<CVNodeImpl, T>::value>>
  CVNode(std::unique_ptr<T> &&impl) : state_(std::make_shared<_State>()) {
    impl_ = std::shared_ptr<CVNodeImpl>(impl.release(), [state = state_](CVNodeImpl *impl_deleter) {
      state->terminated = true;
      impl_deleter->close_connectors();
      if (state->thread_future.valid()) {
        state->thread_future.get();
      }
      delete impl_deleter;
    });
    state_->thread_future =
        std::async(std::launch::async, [state = state_, impl = impl_.get()]() { run_forever(state, impl); });
  }

  void check() const {
    if (state_->thread_future.wait_for(std::chrono::milliseconds(0)) == std::future_status::ready) {
      state_->thread_future.get();
    }
  }

  template <typename T, typename = std::enable_if<std::is_base_of<CVNodeImpl, T>::value>>
  T &as() const {
    if (T *val = dynamic_cast<T *>(impl_.get())) {
      return *val;
    } else {
      throw maka_error("Tried to extract wrong type out of CVNode.");
    }
  }

  const std::string &get_name() const { return impl_->get_name(); }
  float get_mean_fps() const { return state_->fps.get_mean_fps(); }
  float get_99pct_fps() const { return state_->fps.get_99pct_fps(); }
  float get_mean_latency_ms() const { return state_->latency.get_mean_latency_ms(); }
  float get_99pct_latency_ms() const { return state_->latency.get_99pct_latency_ms(); }
  CVNodeState get_state() const { return impl_->get_state(); }
  std::map<CVNodeState, std::chrono::milliseconds> get_state_timings() const { return impl_->get_state_timings(); }

private:
  struct _State {
    std::future<void> thread_future;
    std::atomic<bool> terminated = false;
    FPS fps;
    Latency latency;
  };

  static void run_forever(std::shared_ptr<_State> state, CVNodeImpl *impl) {
    while (!state->terminated) {
      try {
        impl->prepare();
        while (!state->terminated) {
          int64_t timestamp_ms;
          {
            NVTXRange range(impl->get_name());
            timestamp_ms = impl->tick();
            impl->set_state(CVNodeState::kUnset);
          }
          if (timestamp_ms != 0) {
            state->fps.tick(timestamp_ms);
            state->latency.tick(timestamp_ms);
          }
        }
      } catch (restart_node_error &ex) {
        // Node should be restarted.
        try {
          impl->finalize();
        } catch (...) {
          // ignore
        }
      } catch (connector_closed_error &ex) {
        // Connector closed, we are terminating.
        break;
// Disable rethrowing in debug to allow debugger to resume at original throw location
#ifndef DEBUG
      } catch (std::exception &ex) {
        spdlog::error("Node {} failed: {}", impl->get_name(), ex.what());
        std::throw_with_nested(maka_error(fmt::format("Node {} failed", impl->get_name()), false));
#endif
      }
    }
    try {
      impl->finalize();
    } catch (...) {
      // ignore
    }
  }

  std::shared_ptr<_State> state_;
  std::shared_ptr<CVNodeImpl> impl_;
};

} // namespace runtime
} // namespace cv

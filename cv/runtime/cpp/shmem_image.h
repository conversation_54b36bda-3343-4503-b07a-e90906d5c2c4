#pragma once

#include <array>
#include <cstdint>

namespace cv {
namespace runtime {

template <typename T, int ImageMaxWidth, int ImageMaxHeight, int ImageMaxChannels>
struct alignas(int64_t) ShmemImage {
  uint32_t width, height, channels;
  std::array<T, ImageMaxWidth * ImageMaxHeight * ImageMaxChannels> data;
  int64_t timestamp_ms;
  float focus_metric;
};

} // namespace runtime
} // namespace cv

#include "cv_http_server.h"
#include "cv/p2p/p2p_utils.h"
#include <fstream>
#include <glm/glm.hpp>
#include <iostream>
#include <spdlog/spdlog.h>
#include <sstream>

namespace cv::runtime {

using namespace std;
using namespace lib::common;

CVHttpServer::CVHttpServer(CameraBufferMap distance_camera_buffers)
    : distance_camera_buffers_(distance_camera_buffers), thread_(&CVHttpServer::run, this) {}

CVHttpServer::~CVHttpServer() {
  if (thread_.joinable()) {
    if (server_) {
      server_->stop();
    }
    thread_.join();
    server_.reset();
  }
}

void CVHttpServer::run() {
  server_ = std::make_unique<httplib::Server>();
  server_->Get("/perspective", [&](const httplib::Request &req, httplib::Response &res) {
    try {
      if (req.params.find("camera") == req.params.end() || req.params.find("coords_x") == req.params.end() ||
          req.params.find("coords_y") == req.params.end() || req.params.find("ts") == req.params.end() ||
          req.params.find("size_width") == req.params.end() || req.params.find("size_height") == req.params.end()) {
        res.status = 400;
        res.set_content("Bad Request: requires camera, coords_x, coords_y, ts, size_with and size_height query params.",
                        "text/plain");
        return;
      }

      auto predict_cam_id = req.params.find("camera")->second;
      auto coords_x = atoi(req.params.find("coords_x")->second.c_str());
      auto coords_y = atoi(req.params.find("coords_y")->second.c_str());
      auto predict_timestamp = std::stol(req.params.find("ts")->second);
      auto size_w = atoi(req.params.find("size_width")->second.c_str());
      auto size_h = atoi(req.params.find("size_height")->second.c_str());
      auto target_ppi = std::stof(req.params.find("target_ppi")->second);
      glm::ivec2 coord(coords_x, coords_y);
      glm::ivec2 size(size_w, size_h);

      if (distance_camera_buffers_->find(predict_cam_id) == distance_camera_buffers_->end()) {
        spdlog::warn("CVHttpServer.perspective: tried to set context for camera {} that is unknown.", predict_cam_id);
        res.status = 404;
        return;
      }

      std::unique_ptr<camera::CameraImage> image;
      auto buffer = distance_camera_buffers_->at(predict_cam_id);
      if (buffer->contains(predict_timestamp)) {
        image = std::make_unique<camera::CameraImage>(buffer->get(predict_timestamp));
      }

      if (image == nullptr) {
        spdlog::warn("CVHttpServer.perspective: could not find image for cam {} with timestamp {} in buffers",
                     predict_cam_id, predict_timestamp);
        res.status = 404;
        return;
      }

      auto tensor = image->cpu().contiguous().image;
      std::tie(tensor, std::ignore, std::ignore) =
          p2p::crop_and_resize_ppi(tensor, *image->ppi, coord, size, target_ppi);
      ostringstream ss;
      torch::save(tensor, ss);
      res.set_content(ss.str(), "multipart/form-data");
      if (image->ppi) {
        res.set_header("X-Carbon-PPI", std::to_string(target_ppi));
      }
    } catch (const std::exception &e) {
      spdlog::warn("CVHttpServer.perspective unknown exception {}", e.what());
    }
  });

  char *container_ptr = getenv("HOSTNAME");
  std::string container = "";
  if (container_ptr != NULL) {
    container = container_ptr;
  }
  int port = 15055;
  if (container == "cv2") {
    port = 15056;
  }
  server_->listen("0.0.0.0", port);
}

} // namespace cv::runtime

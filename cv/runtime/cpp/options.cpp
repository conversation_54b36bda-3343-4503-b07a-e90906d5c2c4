#include <cxxopts.hpp>
#include <fmt/core.h>

#include "options.h"

namespace cv {
namespace runtime {

Options parse_options(int argc, char **argv) {
  cxxopts::Options options("cv_runtime", "Computer Vision Runtime");
  // clang-format off
    options.add_options()
      ("c,config", "Path to configuration file", cxxopts::value<std::string>())
      ("r,model-registry", "Path to model registry file", cxxopts::value<std::string>())
      ("sim-target-image", "Path to image for simming target", cxxopts::value<std::vector<std::string>>())
      ("sim-predict-image", "Path to image for simming predict", cxxopts::value<std::vector<std::string>>())
      ("sim-drive-image", "Path to image for simming drive", cxxopts::value<std::vector<std::string>>())
      ("dummy-publisher", "Use dummy publisher", cxxopts::value<bool>())
      ("force-load-yaml", "Load cv.yaml even if config is populated. (Only temporary, does not effect global config."
       "Use tools/config/load_cv.py to persist changes)", cxxopts::value<bool>())
      ("skip-models", "Skip loading and running models", cxxopts::value<bool>())
      ("verbose", "Show more output", cxxopts::value<bool>())
      ("h,help", "Print usage")
    ;
  // clang-format on
  auto args = options.parse(argc, argv);

  if (args.count("help") != 0) {
    fmt::print("{}", options.help());
    exit(0);
  }

  Options opts;
  if (args.count("model-registry") != 0) {
    opts.model_registry_file = args["model-registry"].as<std::string>();
  }
  opts.dummy_publisher = args["dummy-publisher"].as<bool>();
  opts.skip_models = args["skip-models"].as<bool>();
  opts.verbose = args["verbose"].as<bool>();
  opts.force_load_yaml = args["force-load-yaml"].as<bool>();

  if (args.count("config") != 0) {
    opts.config_file = args["config"].as<std::string>();
  }

  if (args.count("sim-target-image") != 0) {
    opts.sim_target_image = args["sim-target-image"].as<std::vector<std::string>>();
  }

  if (args.count("sim-predict-image") != 0) {
    opts.sim_predict_image = args["sim-predict-image"].as<std::vector<std::string>>();
  }

  if (args.count("sim-drive-image") != 0) {
    opts.sim_drive_image = args["sim-drive-image"].as<std::vector<std::string>>();
  }
  return opts;
}

} // namespace runtime
} // namespace cv
#include "metrics_logger.h"

#include "cv/runtime/cpp/nodes/camera_grabber.h"
#include "cv/runtime/cpp/nodes/deepweed_infer.h"
#include "cv/runtime/cpp/nodes/focus_metric.h"
#include "cv/runtime/cpp/nodes/frequency_focus_metric.h"
#include "cv/runtime/cpp/nodes/p2p/infer.h"
#include "lib/common/camera/cpp/exceptions.h"

namespace cv {
namespace runtime {
MetricsLogger::MetricsLogger(std::shared_ptr<NodeRegistry> node_registry,
                             std::shared_ptr<prometheus::Registry> registry)
    : node_registry_(node_registry), registry_(registry),
      deepweed_config_(carbon::config::get_global_config_subscriber()->get_config_node("common", "deepweed")) {

  auto &grabber_99_pct_fps = prometheus::BuildGauge()
                                 .Name("grabber_99_pct_fps")
                                 .Help("99th percentile of frames per second of the camera grabber")
                                 .Register(*registry_);
  auto &grabber_fps =
      prometheus::BuildGauge().Name("grabber_fps").Help("Frames per second of the camera grabber").Register(*registry_);
  auto &grabber_latency =
      prometheus::BuildGauge().Name("grabber_latency").Help("Latency (ms) of the camera grabber").Register(*registry_);
  auto &grabber_99_pct_latency = prometheus::BuildGauge()
                                     .Name("grabber_99_pct_latency")
                                     .Help("99th percentile of latency (ms) of the camera grabber")
                                     .Register(*registry_);

  auto &deepweed_99_pct_fps = prometheus::BuildGauge()
                                  .Name("deepweed_99_pct_fps")
                                  .Help("99th percentile of frames per second of deepweed")
                                  .Register(*registry_);
  auto &deepweed_fps =
      prometheus::BuildGauge().Name("deepweed_fps").Help("Frames per second of deepweed").Register(*registry_);
  auto &deepweed_latency =
      prometheus::BuildGauge().Name("deepweed_latency").Help("Latency (ms) of deepweed").Register(*registry_);
  auto &deepweed_99_pct_latency = prometheus::BuildGauge()
                                      .Name("deepweed_99_pct_latency")
                                      .Help("99th percentile of latency (ms) of deepweed")
                                      .Register(*registry_);

  auto &p2p_99_pct_fps = prometheus::BuildGauge()
                             .Name("p2p_99_pct_fps")
                             .Help("99th percentile of frames per second of p2p")
                             .Register(*registry_);
  auto &p2p_fps = prometheus::BuildGauge().Name("p2p_fps").Help("Frames per second of p2p").Register(*registry_);
  auto &p2p_latency = prometheus::BuildGauge().Name("p2p_latency").Help("Latency (ms) of p2p").Register(*registry_);
  auto &p2p_99_pct_latency = prometheus::BuildGauge()
                                 .Name("p2p_99_pct_latency")
                                 .Help("99th percentile of latency (ms) of p2p")
                                 .Register(*registry_);

  auto &focus_metric =
      prometheus::BuildGauge().Name("focus_metric").Help("Focus metric of last camera image").Register(*registry_);
  auto &frequency_focus_metric = prometheus::BuildGauge()
                                     .Name("frequency_focus_metric")
                                     .Help("Frequency-based focus metric of last camera image")
                                     .Register(*registry_);

  deepweed_model_family_ = std::make_shared<std::reference_wrapper<prometheus::Family<prometheus::Gauge>>>(
      prometheus::BuildGauge().Name("deepweed_model_id").Help("Deepweed model id").Register(*registry_));
  auto &deepweed_wpt =
      prometheus::BuildGauge().Name("deepweed_wpt").Help("Deepweed weed point threshold").Register(*registry_);
  auto &deepweed_cpt =
      prometheus::BuildGauge().Name("deepweed_cpt").Help("Deepweed crop point threshold").Register(*registry_);

  deepweed_wpt_gauge_ = std::make_shared<std::reference_wrapper<prometheus::Gauge>>(deepweed_wpt.Add({}));
  deepweed_cpt_gauge_ = std::make_shared<std::reference_wrapper<prometheus::Gauge>>(deepweed_cpt.Add({}));

  auto &exposure_time = prometheus::BuildGauge().Name("exposure_time").Help("Exposure time (us)").Register(*registry_);
  auto &gain_db = prometheus::BuildGauge().Name("gain_db").Help("Gain (db)").Register(*registry_);
  auto &camera_temperature =
      prometheus::BuildGauge().Name("camera_temperature").Help("Camera temperature (celsius)").Register(*registry_);

  for (auto id : node_registry_->get_known_cameras()) {
    if (node_registry_->has_typed_node<cv::runtime::nodes::CameraGrabber>(id)) {
      camera_id_to_fps_gauge_.emplace(id, grabber_fps.Add({{"camera_id", id}}));
      camera_id_to_pct_fps_gauge_.emplace(id, grabber_99_pct_fps.Add({{"camera_id", id}}));
      camera_id_to_latency_gauge_.emplace(id, grabber_latency.Add({{"camera_id", id}}));
      camera_id_to_pct_latency_gauge_.emplace(id, grabber_99_pct_latency.Add({{"camera_id", id}}));
      camera_id_to_exposure_time_gauge_.emplace(id, exposure_time.Add({{"camera_id", id}}));
      camera_id_to_gain_db_gauge_.emplace(id, gain_db.Add({{"camera_id", id}}));
      camera_id_to_temperature_gauge_.emplace(id, camera_temperature.Add({{"camera_id", id}}));
    }

    if (node_registry_->has_typed_node<cv::runtime::nodes::DeepweedInfer>(id)) {
      camera_id_to_deepweed_fps_gauge_.emplace(id, deepweed_fps.Add({{"camera_id", id}}));
      camera_id_to_deepweed_pct_fps_gauge_.emplace(id, deepweed_99_pct_fps.Add({{"camera_id", id}}));
      camera_id_to_deepweed_latency_gauge_.emplace(id, deepweed_latency.Add({{"camera_id", id}}));
      camera_id_to_deepweed_pct_latency_gauge_.emplace(id, deepweed_99_pct_latency.Add({{"camera_id", id}}));
    }

    if (node_registry_->has_typed_node<cv::runtime::nodes::p2p::P2PInfer>(id)) {
      camera_id_to_p2p_fps_gauge_.emplace(id, p2p_fps.Add({{"camera_id", id}}));
      camera_id_to_p2p_pct_fps_gauge_.emplace(id, p2p_99_pct_fps.Add({{"camera_id", id}}));
      camera_id_to_p2p_latency_gauge_.emplace(id, p2p_latency.Add({{"camera_id", id}}));
      camera_id_to_p2p_pct_latency_gauge_.emplace(id, p2p_99_pct_latency.Add({{"camera_id", id}}));
    }

    if (node_registry_->has_typed_node<cv::runtime::nodes::FocusMetric>(id)) {
      camera_id_to_focus_metric_gauge_.emplace(id, focus_metric.Add({{"camera_id", id}}));
    }

    if (node_registry_->has_typed_node<cv::runtime::nodes::FrequencyFocusMetric>(id)) {
      camera_id_to_frequency_focus_metric_gauge_.emplace(id, frequency_focus_metric.Add({{"camera_id", id}}));
    }
  }
}

void MetricsLogger::update() {
  deepweed_wpt_gauge_->get().Set(deepweed_config_->get_node("weed_point_threshold")->get_value<double>());
  deepweed_cpt_gauge_->get().Set(deepweed_config_->get_node("crop_point_threshold")->get_value<double>());

  for (auto id : node_registry_->get_known_cameras()) {
    if (node_registry_->has_typed_node<cv::runtime::nodes::CameraGrabber>(id)) {
      camera_id_to_fps_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::CameraGrabber>(id).get_mean_fps());
      camera_id_to_pct_fps_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::CameraGrabber>(id).get_99pct_fps());

      camera_id_to_latency_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::CameraGrabber>(id).get_mean_latency_ms());
      camera_id_to_pct_latency_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::CameraGrabber>(id).get_99pct_latency_ms());

      auto camera_impl = node_registry_->get_node_by_type<cv::runtime::nodes::CameraGrabber>(id)
                             .as<cv::runtime::nodes::CameraGrabber>()
                             .get_camera()
                             .get_impl(std::chrono::milliseconds(0));

      if (camera_impl) {
        camera_id_to_exposure_time_gauge_.at(id).get().Set(camera_impl->get_settings().exposure_us.value_or(0.0));
        camera_id_to_gain_db_gauge_.at(id).get().Set(camera_impl->get_settings().gain_db.value_or(0.0));
        try {
          camera_id_to_temperature_gauge_.at(id).get().Set(camera_impl->get_temperature());
        } catch (lib::common::camera::camera_error &error) {
          spdlog::info("Failed to log camera {} temperature: {}", id, error.what());
        }
      }
    }

    if (node_registry_->has_typed_node<cv::runtime::nodes::DeepweedInfer>(id)) {
      camera_id_to_deepweed_fps_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::DeepweedInfer>(id).get_mean_fps());
      camera_id_to_deepweed_pct_fps_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::DeepweedInfer>(id).get_99pct_fps());

      camera_id_to_deepweed_latency_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::DeepweedInfer>(id).get_mean_latency_ms());
      camera_id_to_deepweed_pct_latency_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::DeepweedInfer>(id).get_99pct_latency_ms());

      auto model_id = node_registry_->get_node_by_type<cv::runtime::nodes::DeepweedInfer>(id)
                          .as<cv::runtime::nodes::DeepweedInfer>()
                          .get_model_id();
      if (model_id) {
        std::map<std::string, std::string> model_gauge_labels = {{"camera_id", id}, {"model_id", *model_id}};
        if (!deepweed_model_family_->get().Has(model_gauge_labels)) {
          if (camera_id_to_deepweed_model_gauge_.count(id) > 0) {
            deepweed_model_family_->get().Remove(&camera_id_to_deepweed_model_gauge_.at(id).get());
            camera_id_to_deepweed_model_gauge_.erase(id);
          }
          auto &model_gauge = deepweed_model_family_->get().Add(model_gauge_labels);
          model_gauge.SetToCurrentTime();
          camera_id_to_deepweed_model_gauge_.emplace(id, model_gauge);
        }
      }
    }

    if (node_registry_->has_typed_node<cv::runtime::nodes::p2p::P2PInfer>(id)) {
      camera_id_to_p2p_fps_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::p2p::P2PInfer>(id).get_mean_fps());
      camera_id_to_p2p_pct_fps_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::p2p::P2PInfer>(id).get_99pct_fps());

      camera_id_to_p2p_latency_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::p2p::P2PInfer>(id).get_mean_latency_ms());
      camera_id_to_p2p_pct_latency_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::p2p::P2PInfer>(id).get_99pct_latency_ms());
    }

    if (node_registry_->has_typed_node<cv::runtime::nodes::FocusMetric>(id)) {
      camera_id_to_focus_metric_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::FocusMetric>(id)
              .as<cv::runtime::nodes::FocusMetric>()
              .get_last_focus_metric()
              .value_or(0.0f));
    }

    if (node_registry_->has_typed_node<cv::runtime::nodes::FrequencyFocusMetric>(id)) {
      camera_id_to_frequency_focus_metric_gauge_.at(id).get().Set(
          node_registry_->get_node_by_type<cv::runtime::nodes::FrequencyFocusMetric>(id)
              .as<cv::runtime::nodes::FrequencyFocusMetric>()
              .get_last_frequency_focus_metric()
              .value_or(0.0f));
    }
  }
}
} // namespace runtime

} // namespace cv

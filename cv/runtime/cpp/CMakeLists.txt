file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp nodes/*.cpp deepweed/*.cpp nodes/p2p/*.cpp comparison/*.cpp
grpc_services/impl/*.cpp grpc_services/impl/*.hpp grpc_services/sync/*.cpp grpc_services/sync/*.hpp grpc_services/async/*.cpp grpc_services/async/*.hpp)
list(REMOVE_ITEM SOURCES ${CMAKE_CURRENT_SOURCE_DIR}/main.cpp)
list(FILTER SOURCES EXCLUDE REGEX ".*_python\\.cpp$")

CompileProto(../proto/cv_runtime.proto GENERATED_PATH GOPKG proto/cv LANGS grpc_python python mypy grpc cpp go go-grpc)

add_library(cv_runtime_proto SHARED ${GENERATED_PATH}/cv_runtime.grpc.pb.cc ${GENERATED_PATH}/cv_runtime.pb.cc)
target_compile_options(cv_runtime_proto PRIVATE "-w")
target_link_libraries(cv_runtime_proto grpc++ protobuf camera_proto prometheus-cpp-core prometheus-cpp-pull cv_proto utils weed_tracking_proto)

add_library(cv_runtime_lib SHARED ${SOURCES})
target_compile_definitions(cv_runtime_lib PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_include_directories(cv_runtime_lib SYSTEM PUBLIC /usr/local/include/opencv4 ${nanoflann_DIR})
target_link_directories(cv_runtime_lib PUBLIC /usr/local/lib /usr/local/cuda/lib64/)
target_link_libraries(cv_runtime_lib PUBLIC exceptions m yaml-cpp spdlog fmt basler_camera thinklucid_camera zed_camera 
    simulated_camera mock_camera pthread torch opencv_core opencv_calib3d opencv_imgproc
    opencv_cudaimgproc opencv_imgcodecs shmem cudart model_io connected_components cv_runtime_proto config_client_lib
    config_tree_lib sentry_reporter frontend_proto focus_metric hardware_manager_client weed_tracking_client
    aimbot_client logging_service opencv_allocator targeting utils httplib v4l2_utils lib_common_model deepweed furrows
    comparison module_server_client redis_client cv_embeddings interest_scores wheel_encoder nanopb tracker_roi)

add_executable(cv_runtime main.cpp)
target_link_libraries(cv_runtime PUBLIC cv_runtime_lib)

file(GLOB CLIENT_SOURCES CONFIGURE_DEPENDS client/*.cpp)

add_library(cv_runtime_client SHARED ${CLIENT_SOURCES})
target_include_directories(cv_runtime_client SYSTEM PUBLIC /usr/local/include/opencv4 ${nanoflann_DIR})
target_compile_definitions(cv_runtime_client PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(cv_runtime_client PUBLIC m stdc++fs boost_chrono pthread rt boost_date_time boost_thread shmem fmt torch bot_stop utils)
target_link_libraries(cv_runtime_client PRIVATE cv_runtime_proto)

add_subdirectory(image_service_client)
add_subdirectory(tests)

#include "image_service.h"

#include "lib/common/cpp/cuda_util.h"
#include "lib/common/image/cpp/focus_metric.h"
#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_scoped_callback.hpp>
#include <cv/runtime/cpp/image_service_consts.hpp>
#include <fmt/format.h>
#include <opencv2/core.hpp>
#include <opencv2/imgcodecs.hpp>
#include <opencv2/imgproc.hpp>
#include <spdlog/spdlog.h>
#include <weed_tracking_libs/tracker_roi/cpp/tracker_roi.hpp>

namespace cv {
namespace runtime {

ImageStreamServiceImpl::ImageStreamServiceImpl(
    std::shared_ptr<
        std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, lib::common::camera::CameraImage>>>>
        stream_camera_buffers,
    std::shared_ptr<
        std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, lib::common::camera::CameraImage>>>>
        distance_camera_buffers,
    std::shared_ptr<NodeRegistry> node_registry, std::shared_ptr<carbon::config::ConfigTree> aimbot_config,
    std::shared_ptr<weed_tracking::WeedTrackingClient> weed_tracking_client)
    : stream_camera_buffers_(stream_camera_buffers), distance_camera_buffers_(distance_camera_buffers),
      weed_tracking_client_(weed_tracking_client), node_registry_(node_registry), aimbot_config_(aimbot_config),
      roi_accessor_() {}

grpc::Status
ImageStreamServiceImpl::GetNextCameraImage(grpc::ServerContext *,
                                           const carbon::frontend::image_stream::CameraImageRequest *request,
                                           carbon::frontend::image_stream::Image *image) {

  // TODO: You, yes you. You need to add context cancellation handling here.
  // Don't forget the client side as well.

  try {
    auto start = std::chrono::system_clock::now();
    const auto global_cam_id = request->cam_id();
    std::string cam_id;
    int index;
    std::tie(cam_id, index) = global_camera_id_to_local(global_cam_id);
    if (stream_camera_buffers_->count(cam_id) == 0) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND, "Camera not found: " + cam_id);
    }

    auto buffer = stream_camera_buffers_->at(cam_id);
    lib::common::camera::CameraImage camera_image;
    if (buffer->size() > 0) {
      camera_image = buffer->get_latest();
    } else {
      camera_image = buffer->get_next(std::chrono::milliseconds(kGetCameraImageTimeoutMS));
    }
    while (camera_image.timestamp_ms <= request->ts().timestamp_ms() &&
           std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - start).count() <
               kGetCameraImageTimeoutMS) {
      camera_image = buffer->get_next(std::chrono::milliseconds(kGetCameraImageTimeoutMS));
    }
    if (std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - start).count() >=
        kGetCameraImageTimeoutMS) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND, "Timeout retrieving frame for: " + cam_id);
    }

    if (!camera_image.focus_metric) {
      lib::common::image::FocusMetric focus_metric;
      camera_image.focus_metric = focus_metric.compute(camera_image.image);
    }

    int downsample_factor = 1;
    if (!request->dont_downsample()) {
      // TODO: Add a max height/width to adjust size
      const int max_dimension = 1400;
      float x_factor = (float)camera_image.get_width() / (float)max_dimension;
      float y_factor = (float)camera_image.get_height() / (float)max_dimension;
      downsample_factor = std::max((int)std::ceil(std::max(x_factor, y_factor)), 1);
    }

    auto image_tensor = camera_image.image
                            .index({"...", torch::indexing::Slice(0, torch::indexing::None, downsample_factor),
                                    torch::indexing::Slice(0, torch::indexing::None, downsample_factor)})
                            .permute({1, 2, 0})
                            .cpu()
                            .contiguous();
    const int data_type = CV_8UC((int)image_tensor.size(2));
    cv::Mat image_opencv((int)image_tensor.size(0), (int)image_tensor.size(1), data_type, image_tensor.data_ptr());
    cv::cvtColor(image_opencv, image_opencv, cv::COLOR_RGB2BGR);

    if (request->annotated() || request->include_annotations_metadata()) {
      const cv::Scalar red_color = {0, 60, 255};
      const cv::Scalar black_color = {0, 0, 0};
      // Predict annotations
      if (node_registry_->has_model_node(cam_id, lib::common::model::ModelUseCase::kPredict)) {
        try {
          auto resp =
              weed_tracking_client_->get_detections(cam_id, camera_image.timestamp_ms, (int)camera_image.get_width());
          for (const auto &detection : resp.detections().detections()) {
            if (detection.x() < 0 || detection.x() >= (float)camera_image.get_width() || detection.y() < 0 ||
                detection.y() >= (float)camera_image.get_height()) {
              continue;
            }
            if (request->annotated()) {
              cv::Point circle_pos = {(int)(detection.x() / (float)downsample_factor),
                                      (int)(detection.y() / (float)downsample_factor)};
              auto circle_size = (int)(kBeamRadiusIn * camera_image.ppi.value() / (float)downsample_factor);
              cv::circle(image_opencv, circle_pos, circle_size, black_color, 2);
            }
            if (request->include_annotations_metadata()) {
              auto det = image->mutable_annotations()->mutable_detections()->add_detections();
              det->CopyFrom(detection);
              det->set_x(detection.x() / (float)downsample_factor);
              det->set_y(detection.y() / (float)downsample_factor);
            }
          }
          if (resp.bands().banding_enabled() && resp.bands().row_has_bands_defined()) {
            if (request->annotated()) {
              // draw red rectangles over OUT_OF_BAND areas
              ::weed_tracking::Band bStart;
              bStart.set_end_x_px(0);
              ::weed_tracking::Band bEnd;
              bEnd.set_start_x_px((int)camera_image.get_width());
              double alpha = 0.2;
              for (int idx = -1; idx < resp.bands().band().size(); idx++) {
                try {
                  auto &band1 = idx >= 0 ? resp.bands().band().at(idx) : bStart;
                  auto &band2 = (idx + 1) < resp.bands().band().size() ? resp.bands().band().at(idx + 1) : bEnd;
                  int x = (int)band1.end_x_px() / downsample_factor;
                  int w = ((int)band2.start_x_px() - (int)band1.end_x_px()) / downsample_factor;
                  if (w == 0) {
                    continue;
                  }
                  int h = camera_image.get_height() / downsample_factor;
                  cv::Mat rect = image_opencv(cv::Rect(x, 0, w, h));
                  cv::addWeighted(red_color, alpha, rect, 1.0 - alpha, 0.0, rect);
                } catch (std::exception &e) {
                  spdlog::error("Error while drawing banding annotations: {}", e.what());
                }
              }
            }
            if (request->include_annotations_metadata()) {
              for (int idx = 0; idx < resp.bands().band().size(); idx++) {
                auto band = image->mutable_annotations()->mutable_bands()->add_band();
                auto &b = resp.bands().band()[idx];
                band->set_start_x_px(b.start_x_px() / (float)downsample_factor);
                band->set_end_x_px(b.end_x_px() / (float)downsample_factor);
              }
              image->mutable_annotations()->mutable_bands()->set_banding_enabled(true);
              image->mutable_annotations()->mutable_bands()->set_row_has_bands_defined(true);
            }
          }
        } catch (const std::exception &ex) {
          spdlog::error("Error while getting detections from aimbot, skipping annotations: {}", ex.what());
        }

        if (auto roi_setting = roi_accessor_.get_roi_setting(cam_id); roi_setting) {
          int start_x = roi_setting->get_start_x() / downsample_factor;
          int start_y = roi_setting->get_start_y() / downsample_factor;
          int end_x = roi_setting->get_end_x() / downsample_factor;
          int end_y = roi_setting->get_end_y() / downsample_factor;

          int width = image_opencv.size[1];
          int height = image_opencv.size[0];

          cv::Mat roi_overlay = image_opencv.clone();

          const cv::Scalar bounds_color = {192, 192, 192};

          if (start_x != 0) { // left box
            cv::rectangle(roi_overlay, cv::Point(0, 0), cv::Point(start_x, height), bounds_color, cv::FILLED);
          }

          if (end_x < width) { // right box
            cv::rectangle(roi_overlay, cv::Point(end_x, 0), cv::Point(width, height), bounds_color, cv::FILLED);
          }

          if (start_y != 0) { // top box
            cv::rectangle(roi_overlay, cv::Point(0, 0), cv::Point(width, start_y), bounds_color, cv::FILLED);
          }

          if (end_y < height) { // bottom box
            cv::rectangle(roi_overlay, cv::Point(0, end_y), cv::Point(width, height), bounds_color, cv::FILLED);
          }

          // set opacity, lower alpha -> darker overlay
          double alpha = 0.3;
          double beta = (1.0 - alpha);
          cv::addWeighted(image_opencv, alpha, roi_overlay, beta, 0.0, image_opencv);
        }
      }
      if (node_registry_->has_model_node(cam_id, lib::common::model::ModelUseCase::kP2P)) {
        auto target_x_node = aimbot_config_->get_node(std::string("scanners/scanner") + std::to_string(index) +
                                                      std::string("/target/x"));
        auto target_y_node = aimbot_config_->get_node(std::string("scanners/scanner") + std::to_string(index) +
                                                      std::string("/target/y"));
        if (target_x_node == nullptr || target_y_node == nullptr) {
          return grpc::Status(grpc::StatusCode::NOT_FOUND, "Scanner not found for: " + cam_id);
        }
        const auto crosshair_pos_x = target_x_node->get_value<int>();
        const auto crosshair_pos_y = target_y_node->get_value<int>();
        if (request->annotated()) {
          const int crosshair_radius = (int)std::ceil(60 / downsample_factor);
          const int inner_thickness = (int)std::ceil(2 / downsample_factor);
          const int inner_radius = (int)std::ceil(4 / downsample_factor);

          cv::Point crosshair_bottom = {crosshair_pos_x / downsample_factor,
                                        (crosshair_pos_y - crosshair_radius) / downsample_factor};
          cv::Point crosshair_bottom_inner = {crosshair_pos_x / downsample_factor,
                                              (crosshair_pos_y - inner_radius) / downsample_factor};
          cv::Point crosshair_top = {crosshair_pos_x / downsample_factor,
                                     (crosshair_pos_y + crosshair_radius) / downsample_factor};
          cv::Point crosshair_top_inner = {crosshair_pos_x / downsample_factor,
                                           (crosshair_pos_y + inner_radius) / downsample_factor};
          cv::Point crosshair_left = {(crosshair_pos_x - crosshair_radius) / downsample_factor,
                                      crosshair_pos_y / downsample_factor};
          cv::Point crosshair_left_inner = {(crosshair_pos_x - inner_radius) / downsample_factor,
                                            crosshair_pos_y / downsample_factor};
          cv::Point crosshair_right = {(crosshair_pos_x + crosshair_radius) / downsample_factor,
                                       crosshair_pos_y / downsample_factor};
          cv::Point crosshair_right_inner = {(crosshair_pos_x + inner_radius) / downsample_factor,
                                             crosshair_pos_y / downsample_factor};

          cv::line(image_opencv, crosshair_bottom, crosshair_bottom_inner, red_color, inner_thickness);
          cv::line(image_opencv, crosshair_top_inner, crosshair_top, red_color, inner_thickness);
          cv::line(image_opencv, crosshair_left, crosshair_left_inner, red_color, inner_thickness);
          cv::line(image_opencv, crosshair_right_inner, crosshair_right, red_color, inner_thickness);
        }
        if (request->include_annotations_metadata()) {
          image->mutable_annotations()->set_crosshair_x(crosshair_pos_x);
          image->mutable_annotations()->set_crosshair_y(crosshair_pos_y);
        }
      }
    }
    std::vector<uint8_t> image_bytes;
    if (request->encode_as_png()) {
      cv::imencode(".png", image_opencv, image_bytes);
    } else if (request->encode_as_raw()) {
      image_bytes =
          std::vector<uint8_t>(image_opencv.ptr(), image_opencv.ptr() + image_opencv.total() * image_opencv.channels());
    } else {
      cv::imencode(".jpg", image_opencv, image_bytes, {int(cv::IMWRITE_JPEG_QUALITY), 95});
    }
    image->set_width(camera_image.get_width() / downsample_factor);
    image->set_height(camera_image.get_height() / downsample_factor);
    image->set_focus(*camera_image.focus_metric);
    image->set_data(image_bytes.data(), image_bytes.size());
    auto ts = new carbon::frontend::util::Timestamp();
    ts->set_timestamp_ms(camera_image.timestamp_ms);
    image->set_allocated_ts(ts);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

std::pair<std::string, int> ImageStreamServiceImpl::global_camera_id_to_local(std::string global) {
  const std::string parser = "_";

  const std::string target_name = "target";
  const std::string predict_name = "predict";
  std::string local_camera_id;
  int index = 0;

  auto parser_pos = global.find(parser);

  if (parser_pos != std::string::npos) {
    global = global.substr(parser_pos + 1);
  }

  if (global.find(target_name) != std::string::npos) {
    index = std::atoi(global.substr(target_name.size()).c_str());
    local_camera_id = target_name + std::to_string(index);
  } else if (global.find(predict_name) != std::string::npos) {
    index = std::atoi(global.substr(predict_name.size()).c_str());
    local_camera_id = predict_name + std::to_string(index);
  } else {
    local_camera_id = global;
  }
  return {local_camera_id, index};
}

grpc::Status ImageStreamServiceImpl::GetPredictImageByTimestamp(
    grpc::ServerContext *, const carbon::frontend::image_stream::GetPredictImageByTimestampRequest *request,
    carbon::frontend::image_stream::GetPredictImageByTimestampResponse *response) {

  // You know what to do here, right? (Hint: context cancellation handling)

  try {
    const auto global_cam_id = request->cam_id();
    std::string cam_id;
    int index;
    std::tie(cam_id, index) = global_camera_id_to_local(global_cam_id);
    if (distance_camera_buffers_->count(cam_id) == 0) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND, "Camera not found: " + cam_id);
    }

    auto buffer = distance_camera_buffers_->at(cam_id);
    auto ts = request->ts().timestamp_ms();
    if (!buffer->contains(ts)) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("No image for cam {} timestamp {}", cam_id, ts));
    }

    auto camera_image = buffer->get(ts);
    auto img = camera_image.image.permute({1, 2, 0});

    auto crop_around_x = request->crop_around_x();
    auto crop_around_y = request->crop_around_y();
    int start_x = std::max(0, crop_around_x - kCropRadius);
    int end_x = std::min((int)img.size(1), crop_around_x + kCropRadius);
    if (start_x == 0) {
      end_x = 2 * kCropRadius;
    } else if (end_x == (int)img.size(1)) {
      start_x = end_x - 2 * kCropRadius;
    }
    int start_y = std::max(0, crop_around_y - kCropRadius);
    int end_y = std::min((int)img.size(0), crop_around_y + kCropRadius);
    if (start_y == 0) {
      end_y = 2 * kCropRadius;
    } else if (end_y == (int)img.size(0)) {
      start_y = end_y - 2 * kCropRadius;
    }

    img = img.slice(0, start_y, end_y).slice(1, start_x, end_x).cpu().contiguous();

    const int data_type = CV_8UC((int)img.size(2));
    cv::Mat image_opencv((int)img.size(0), (int)img.size(1), data_type, img.data_ptr());

    std::vector<uint8_t> image_bytes;
    cv::cvtColor(image_opencv, image_opencv, cv::COLOR_BGR2RGB);
    cv::imencode(".png", image_opencv, image_bytes);
    response->set_data(image_bytes.data(), image_bytes.size());
    response->set_center_x(crop_around_x - start_x);
    response->set_center_y(crop_around_y - start_y);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }

  return grpc::Status::OK;
}

grpc::Status ImageStreamServiceImpl::GetMultiPredictPerspectives(
    grpc::ServerContext *context, const carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest *request,
    carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse *response) {

  if (context->IsCancelled()) {
    return grpc::Status(grpc::StatusCode::CANCELLED, "Deadline exceeded or Client cancelled, abandoning.");
  }

  try {

    // given a list of possible perspectives (from aimbot),
    // 1) find the perspectives that exist in the distance buffer
    // 2) grab n equally spaced perspectives from the list of found perspectives
    // 3) crop (if needed) and return the images

    const size_t requested_perspectives = static_cast<size_t>(request->requested_perspectives());
    if (requested_perspectives == 0) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "requested_perspectives cannot be 0");
    }

    const auto global_cam_id = request->cam_id();
    std::string cam_id;
    int index;
    std::tie(cam_id, index) = global_camera_id_to_local(global_cam_id);
    if (distance_camera_buffers_->count(cam_id) == 0) {
      return grpc::Status(grpc::StatusCode::NOT_FOUND, "Camera not found: " + cam_id);
    }

    auto buffer = distance_camera_buffers_->at(cam_id);

    // map for quick access of the possible perspectives
    auto const possible_perspectives = request->perspectives();
    std::unordered_map<int64_t, std::pair<int32_t, int32_t>> pp_map;
    for (auto &possible_perspective : possible_perspectives) {
      pp_map[possible_perspective.ts().timestamp_ms()] =
          std::make_pair(possible_perspective.crop_around_x(), possible_perspective.crop_around_y());
    }

    // find the perspectives that exist in the distance buffer
    std::unordered_map<int64_t, lib::common::camera::CameraImage> images;
    buffer->apply([&](const int64_t &ts, const lib::common::camera::CameraImage &camera_image) {
      if (pp_map.count(ts) > 0) {
        images[ts] = camera_image;
      }
    });

    if (images.empty()) {
      return grpc::Status(grpc::StatusCode::INTERNAL, "Buffer does not contain any of the requested perspectives");
    }

    // sort the perspectives by timestamp
    std::vector<int64_t> ordered_perspectives;
    for (auto &[ts, _] : images) {
      ordered_perspectives.push_back(ts);
    }
    std::sort(ordered_perspectives.begin(), ordered_perspectives.end());

    size_t len = ordered_perspectives.size();
    size_t spacing;
    if (len <= requested_perspectives) {
      spacing = 1;
    } else {
      spacing = static_cast<size_t>(
          std::ceil(static_cast<double>(len) / static_cast<double>(requested_perspectives - 1)) - 1);
    }
    std::vector<std::pair<int64_t, lib::common::camera::CameraImage>> picked;
    for (size_t i = 0; i < len && picked.size() < requested_perspectives; i += spacing) {
      picked.push_back(std::make_pair(ordered_perspectives[i], images[ordered_perspectives[i]]));
    }

    for (auto &[ts, camera_image] : picked) {
      auto img = camera_image.image.permute({1, 2, 0});

      auto crop_around_x = pp_map[ts].first;
      auto crop_around_y = pp_map[ts].second;
      int start_x = std::max(0, crop_around_x - kCropRadius);
      int end_x = std::min((int)img.size(1), crop_around_x + kCropRadius);
      if (start_x == 0) {
        end_x = 2 * kCropRadius;
      } else if (end_x == (int)img.size(1)) {
        start_x = end_x - 2 * kCropRadius;
      }
      int start_y = std::max(0, crop_around_y - kCropRadius);
      int end_y = std::min((int)img.size(0), crop_around_y + kCropRadius);
      if (start_y == 0) {
        end_y = 2 * kCropRadius;
      } else if (end_y == (int)img.size(0)) {
        start_y = end_y - 2 * kCropRadius;
      }

      img = img.slice(0, start_y, end_y).slice(1, start_x, end_x).cpu().contiguous();

      const int data_type = CV_8UC((int)img.size(2));
      cv::Mat image_opencv((int)img.size(0), (int)img.size(1), data_type, img.data_ptr());

      std::vector<uint8_t> image_bytes;
      cv::cvtColor(image_opencv, image_opencv, cv::COLOR_BGR2RGB);
      cv::imencode(".png", image_opencv, image_bytes);

      auto per = response->add_perspectives();

      per->mutable_ts()->set_timestamp_ms(ts);
      per->set_data(image_bytes.data(), image_bytes.size());
      per->set_center_x(crop_around_x - start_x);
      per->set_center_y(crop_around_y - start_y);
    }

  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }

  return grpc::Status::OK;
}
} // namespace runtime
} // namespace cv

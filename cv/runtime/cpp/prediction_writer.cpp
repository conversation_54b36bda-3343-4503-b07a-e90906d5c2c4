#include "prediction_writer.h"
#include "lib/common/cpp/exceptions.h"
#include <fmt/format.h>
#include <glm/gtx/string_cast.hpp>
#include <spdlog/spdlog.h>

namespace cv {
namespace runtime {

PredictionWriter::PredictionWriter() {}

void PredictionWriter::write_deepweed_prediction(std::string filepath, glm::ivec2 prediction) {
  auto write_filepath = fmt::format("{}.predictions.json", filepath);

  boost::property_tree::ptree property_tree;
  property_tree.put("x", prediction.x);
  property_tree.put("y", prediction.y);

  std::stringstream strings_stream;
  boost::property_tree::json_parser::write_json(strings_stream, property_tree);

  std::ofstream output_file;
  output_file.open(write_filepath, std::ofstream::out);
  output_file << strings_stream.str();
  output_file.close();
}

void PredictionWriter::write_p2p_predictions(std::string filepath, cv::p2p::P2POutput predictions) {
  auto write_filepath = fmt::format("{}.p2p.json", filepath);

  spdlog::info("PredictionWriter::write_p2p_predictions has been executed.");
  spdlog::info(fmt::format("PredictionWriter::write_p2p_predictions {}", predictions.target_timestamp_ms));
  throw maka_error("Unimplemented.");
}

} // namespace runtime
} // namespace cv
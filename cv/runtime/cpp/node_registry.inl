#include "node_registry.h"

#include <exception>

namespace cv {
namespace runtime {

template <typename T, typename... Ts>
inline T &NodeRegistry::create_model_node(std::string camera_id, lib::common::model::ModelUseCase use_case,
                                          Ts &&... node_args) {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  known_cameras_.insert(camera_id);
  auto node = CVNode(std::make_unique<T>(std::forward<Ts>(node_args)...));
  if (camera_to_model_nodes_.count(camera_id) == 0) {
    camera_to_model_nodes_.emplace(camera_id, std::map<lib::common::model::ModelUseCase, CVNode>());
  }
  camera_to_model_nodes_.at(camera_id).emplace(use_case, node);
  if (camera_to_nodes_.count(camera_id) == 0) {
    camera_to_nodes_.emplace(camera_id, std::map<std::type_index, CVNode>());
  }
  camera_to_nodes_.at(camera_id).emplace(typeid(T), node);
  all_nodes_.push_back(node);
  return node.as<T>();
}

template <typename T, typename... Ts>
T &NodeRegistry::create_node(std::string camera_id, Ts &&... node_args) {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  known_cameras_.insert(camera_id);
  auto node = CVNode(std::make_unique<T>(std::forward<Ts>(node_args)...));
  if (camera_to_nodes_.count(camera_id) == 0) {
    camera_to_nodes_.emplace(camera_id, std::map<std::type_index, CVNode>());
  }
  camera_to_nodes_.at(camera_id).emplace(typeid(T), node);
  all_nodes_.push_back(node);
  return node.as<T>();
}

template <typename T, typename... Ts>
T &NodeRegistry::create_node(Ts &&... node_args) {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  auto node = CVNode(std::make_unique<T>(std::forward<Ts>(node_args)...));
  all_nodes_.push_back(node);
  return node.as<T>();
}

std::set<std::string> NodeRegistry::get_known_cameras() const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  return known_cameras_;
}

template <typename T, typename... Ts>
inline Connector<T> NodeRegistry::create_connector(Output<T> output, std::string camera_id, std::string connector_name,
                                                   Ts &&... connector_args) {
  std::unique_lock<std::shared_mutex> lock(mutex_);
  known_cameras_.insert(camera_id);
  auto connector = output.add_output(connector_args...);
  if (camera_to_connectors_.count(camera_id) == 0) {
    camera_to_connectors_.emplace(camera_id, std::map<std::string, std::shared_ptr<ConnectorControl>>());
  }
  camera_to_connectors_.at(camera_id).emplace(connector_name, std::make_shared<Connector<T>>(connector));
  return connector;
}

template <typename T>
bool NodeRegistry::has_typed_node(std::string camera_id) const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  const std::type_index type_index = typeid(T);
  return camera_to_nodes_.count(camera_id) != 0 && camera_to_nodes_.at(camera_id).count(type_index) != 0;
}

template <typename T>
T &NodeRegistry::get_typed_node(std::string camera_id) const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  const std::type_index type_index = typeid(T);
  if (!has_typed_node<T>(camera_id)) {
    throw maka_error(
        fmt::format("Requested camera id {} does not have a node registered for requested type", camera_id));
  }
  return camera_to_nodes_.at(camera_id).at(type_index).as<T>();
}

template <typename T>
const CVNode &NodeRegistry::get_node_by_type(std::string camera_id) const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  const std::type_index type_index = typeid(T);
  if (!has_typed_node<T>(camera_id)) {
    throw maka_error(
        fmt::format("Requested camera id {} does not have a node registered for requested type", camera_id));
  }
  return camera_to_nodes_.at(camera_id).at(type_index);
}

bool NodeRegistry::has_model_node(std::string camera_id, lib::common::model::ModelUseCase use_case) const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  return camera_to_model_nodes_.count(camera_id) != 0 && camera_to_model_nodes_.at(camera_id).count(use_case) != 0;
}

template <typename T>
T &NodeRegistry::get_model_node(std::string camera_id, lib::common::model::ModelUseCase use_case) const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  if (!has_model_node(camera_id, use_case)) {
    throw maka_error(
        fmt::format("Requested camera id {} does not have a node registered for requested type", camera_id));
  }
  return camera_to_model_nodes_.at(camera_id).at(use_case).as<T>();
}

inline bool NodeRegistry::has_camera(std::string camera_id) const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  return known_cameras_.count(camera_id) > 0;
}

inline bool NodeRegistry::has_connector(std::string camera_id, std::string connector_name) const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  return camera_to_connectors_.count(camera_id) > 0 && camera_to_connectors_.at(camera_id).count(connector_name) > 0;
}

inline std::shared_ptr<ConnectorControl> NodeRegistry::get_connector(std::string camera_id,
                                                                     std::string connector_name) const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  if (!has_connector(camera_id, connector_name)) {
    throw maka_error(fmt::format("Requested camera id {} does not have a connector registered with name {}", camera_id,
                                 connector_name));
  }
  return camera_to_connectors_.at(camera_id).at(connector_name);
}

inline std::vector<std::string> NodeRegistry::get_connector_ids(std::string camera_id) const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  std::map<std::string, std::shared_ptr<ConnectorControl>> cam_connectors = camera_to_connectors_.at(camera_id);
  std::vector<std::string> connector_ids;
  for (std::map<std::string, std::shared_ptr<ConnectorControl>>::iterator it = cam_connectors.begin();
       it != cam_connectors.end(); ++it) {
    connector_ids.push_back(it->first);
  }

  return connector_ids;
}

template <typename T>
std::vector<std::pair<std::string, CVNode>> NodeRegistry::get_nodes_by_type() const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  std::vector<std::pair<std::string, CVNode>> output;
  for (const auto &[name, type_to_node] : camera_to_nodes_) {
    for (const auto &[type_id, node] : type_to_node) {
      if (type_id == typeid(T)) {
        output.emplace_back(name, node);
      }
    }
  }
  return output;
}

std::vector<CVNode> NodeRegistry::get_nodes() const {
  std::shared_lock<std::shared_mutex> lock(mutex_);
  return all_nodes_;
}

} // namespace runtime
} // namespace cv
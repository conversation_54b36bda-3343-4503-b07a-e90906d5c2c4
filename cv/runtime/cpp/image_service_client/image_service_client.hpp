#pragma once

#include "frontend/proto/image_stream.grpc.pb.h"
#include "frontend/proto/image_stream.pb.h"
#include <grpcpp/grpcpp.h>

namespace cv::runtime::image_service_client {

class ImageServiceClient {
private:
  std::string addr_;
  std::shared_ptr<grpc::Channel> channel_;
  std::shared_ptr<carbon::frontend::image_stream::ImageStreamService::Stub> stub_;

  std::shared_ptr<carbon::frontend::image_stream::ImageStreamService::Stub> get_grpc_stub();
  grpc::Status exec_grpc(std::function<grpc::Status()> func);
  void reset_stub();

public:
  ImageServiceClient(const std::string &address);
  std::shared_ptr<carbon::frontend::image_stream::Image>
  get_next_camera_image(std::string cam_id, int64_t timestamp_ms, bool annotated, bool include_annotations_metadata,
                        bool dont_downsample, bool encode_as_png);
  std::shared_ptr<carbon::frontend::image_stream::GetPredictImageByTimestampResponse>
  get_predict_image_by_timestamp(std::string cam_id, int64_t timestamp_ms, int crop_around_x, int crop_around_y);
  std::vector<std::shared_ptr<carbon::frontend::image_stream::CentroidPerspective>>
  get_multi_predict_perspectives(std::string cam_id, int requested_perspectives,
                                 std::vector<std::tuple<int64_t, int, int>> possible_perspectives);
};
} // namespace cv::runtime::image_service_client

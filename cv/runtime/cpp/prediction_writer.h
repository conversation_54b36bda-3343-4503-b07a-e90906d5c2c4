#pragma once
#include "cv/p2p/output.h"
#include <boost/optional.hpp>
#include <boost/property_tree/json_parser.hpp>
#include <boost/property_tree/ptree.hpp>
#include <string>

namespace cv {
namespace runtime {

class PredictionWriter {
public:
  PredictionWriter();
  static void write_deepweed_prediction(std::string filepath, glm::ivec2 prediction);
  static void write_p2p_predictions(std::string filepath, cv::p2p::P2POutput predictions);
};

} // namespace runtime
} // namespace cv

#pragma once
#include "lib/common/cpp/geo_data.h"
#include "lib/common/cpp/time.h"
#include <boost/optional.hpp>
#include <boost/property_tree/json_parser.hpp>
#include <boost/property_tree/ptree.hpp>
#include <fmt/format.h>
#include <fstream>
#include <iostream>
#include <optional>
#include <tuple>

#include <config/client/cpp/config_subscriber.hpp>

namespace cv {
namespace runtime {

using namespace lib::common;

class MetadataWriter {
public:
  MetadataWriter();
  void write_metadata(std::string image_path, GeoLLAData lla, GeoECEFData ecef, int64_t timestamp_ms,
                      std::string cam_id, std::string capture_method, float ppi = 0);

private:
};

} // namespace runtime
} // namespace cv
#pragma once

#include <cmath>
#include <string>

namespace cv::runtime {

enum class CameraType { kTarget = 0, kPredict = 1, kDrive = 2 };

class GPUAssigner {
public:
  GPUAssigner(const std::string &role, int num_target_cameras, int num_predict_cameras, int num_drive_cameras,
              int num_gpus)
      : num_target_cameras_(num_target_cameras), num_predict_cameras_(num_predict_cameras),
        num_drive_cameras_(num_drive_cameras), role_(role) {
    if (num_gpus >= 2) {
      if (role == "row-primary" || role == "row-secondary") {
        target_num_gpus_ = 1;
        predict_num_gpus_ = 1;
        if (num_gpus >= 5) {
          target_num_gpus_ = 3;
          predict_num_gpus_ = 2;
        } else if (num_gpus >= 4) {
          target_num_gpus_ = 2;
          predict_num_gpus_ = 2;
        } else if (num_gpus >= 3) {
          target_num_gpus_ = 1;
          predict_num_gpus_ = 2;
        } else if (num_gpus >= 2) {
          target_num_gpus_ = 1;
          predict_num_gpus_ = 1;
        }
      } else {
        target_num_gpus_ = 1;
        predict_num_gpus_ = 1;
        if (num_gpus >= 7) {
          target_num_gpus_ = 5;
          predict_num_gpus_ = 2;
        } else if (num_gpus >= 6) {
          target_num_gpus_ = 4;
          predict_num_gpus_ = 2;
        } else if (num_gpus >= 5) {
          target_num_gpus_ = 3;
          predict_num_gpus_ = 2;
        } else if (num_gpus >= 4) {
          target_num_gpus_ = 2;
          predict_num_gpus_ = 2;
        } else if (num_gpus >= 3) {
          target_num_gpus_ = 2;
          predict_num_gpus_ = 1;
        } else if (num_gpus >= 2) {
          target_num_gpus_ = 1;
          predict_num_gpus_ = 1;
        }
      }
    }
  }
  virtual ~GPUAssigner() {}

  int get_gpu_id_by_index(CameraType camera_type, int index) {
    int gpu_id;
    if (role_ == "row-primary" || role_ == "row-secondary") {
      if (camera_type == CameraType::kTarget) {
        float gpus_per_camera = (float)target_num_gpus_ / (float)num_target_cameras_;
        gpu_id = (int)std::floor(gpus_per_camera * (float)(index));
        // Shift target camera assignment so gpu 0 only gets
        // one target camera when all gpus are available
        gpu_id = (gpu_id + 1) % target_num_gpus_;
      } else if (camera_type == CameraType::kPredict) {
        float gpus_per_camera = (float)predict_num_gpus_ / (float)num_predict_cameras_;
        gpu_id = target_num_gpus_ + (int)std::floor(gpus_per_camera * (float)index);
      } else { // camera_type == CameraType::kDrive
        float gpus_per_camera = (float)predict_num_gpus_ / (float)num_drive_cameras_;
        gpu_id = target_num_gpus_ + (int)std::floor(gpus_per_camera * (float)index);
      }
    } else {
      if (camera_type == CameraType::kTarget) {
        float gpus_per_camera = (float)target_num_gpus_ / (float)num_target_cameras_;
        gpu_id = (int)std::floor(gpus_per_camera * (float)index);
      } else if (camera_type == CameraType::kPredict) {
        float gpus_per_camera = (float)predict_num_gpus_ / (float)num_predict_cameras_;
        gpu_id = target_num_gpus_ + (int)std::floor(gpus_per_camera * (float)index);
      } else { // camera_type == CameraType::kDrive
        float gpus_per_camera = (float)predict_num_gpus_ / (float)num_drive_cameras_;
        gpu_id = target_num_gpus_ + (int)std::floor(gpus_per_camera * (float)index);
      }
    }
    return gpu_id;
  }

  int get_next_gpu_id(CameraType camera_type) {
    int *index;
    if (camera_type == CameraType::kTarget) {
      index = &target_camera_index_;
    } else if (camera_type == CameraType::kPredict) {
      index = &predict_camera_index_;
    } else { // camera_type == CameraType::kDrive
      index = &drive_camera_index_;
    }
    int gpu_id = get_gpu_id_by_index(camera_type, *index);
    (*index)++;
    return gpu_id;
  }

private:
  int target_num_gpus_;
  int predict_num_gpus_;
  int drive_num_gpus_;
  int num_target_cameras_;
  int num_predict_cameras_;
  int num_drive_cameras_;
  int target_camera_index_ = 0;
  int predict_camera_index_ = 0;
  int drive_camera_index_ = 0;
  std::string role_;
};

} // namespace cv::runtime

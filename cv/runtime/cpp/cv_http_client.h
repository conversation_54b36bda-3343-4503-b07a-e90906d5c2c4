#pragma once

#include "lib/common/camera/cpp/camera_image.h"
#include <glm/glm.hpp>
#include <httplib.h>
#include <optional>

namespace cv::runtime {

using namespace lib::common;

class CVHttpClient {
public:
  CVHttpClient();
  CVHttpClient(std::string addr) : addr_(addr), client_(std::make_unique<httplib::Client>(addr)) {
    client_->set_keep_alive(true);
  }
  CVHttpClient(const CVHttpClient &);
  std::optional<camera::CameraImage> get(glm::vec2 predict_coords, int64_t predict_timestamp,
                                         std::string predict_cam_id, glm::ivec2 size, float target_ppi);

private:
  std::string addr_;
  std::unique_ptr<httplib::Client> client_;
};

} // namespace cv::runtime
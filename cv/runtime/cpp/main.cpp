#include <chrono>
#include <csignal>
#include <filesystem>
#include <thread>
#include <unordered_map>

#include <fmt/format.h>
#include <fmt/ostream.h>
#include <prometheus/exposer.h>
#include <prometheus/gauge.h>
#include <prometheus/registry.h>
#include <spdlog/async.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <yaml-cpp/yaml.h>

#include "camera_registry.h"
#include "config/from_config_tree.h"
#include "config/to_config_tree.h"
#include "core/controls/exterminator/controllers/aimbot/cpp/grpc_client.h"
#include "cv/embeddings/distance_functions.h"
#include "cv/embeddings/manager.h"
#include "cv/embeddings/nearest_neighbor_classifier.h"
#include "cv/embeddings/random_forest_classifier.h"
#include "cv/runtime/cpp/cv_http_client.h"
#include "cv/runtime/cpp/gpu_assigner.h"
#include "cv/runtime/cpp/latest_internal_buffer.h"
#include "cv/runtime/proto/cv_runtime.pb.h"
#include "cv_http_server.h"
#include "grpc_services/async/async_grpc_service.hpp"
#include "grpc_services/sync/grpc_service.hpp"
#include "hardware_manager/cpp/grpc_client.h"
#include "image_score_queue.h"
#include "lib/common/cpp/aimbot_state.h"
#include "lib/common/cpp/category_collection_state.h"
#include "lib/common/cpp/distance_status.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/geo_data.h"
#include "lib/common/cpp/implement_status.h"
#include "lib/common/cpp/logging_service.hpp"
#include "lib/common/cpp/opencv_allocator.h"
#include "lib/common/cpp/time.h"
#include "lib/common/cpp/utils/environment.hpp"
#include "lib/common/cpp/utils/generation.hpp"
#include "lib/common/model/cpp/model_registry.h"
#include "lib/common/sentry_reporter/cpp/sentry_reporter.h"
#include "lib/drivers/sim_cam/cpp/camera.h"
#include "metrics_logger.h"
#include "node.h"
#include "node_registry.h"
#include "nodes/aimbot_state_retriever.h"
#include "nodes/brightness_adjuster.h"
#include "nodes/buffer.h"
#include "nodes/burst_record.h"
#include "nodes/camera_grabber.h"
#include "nodes/chip_scorer.h"
#include "nodes/deepweed_infer.h"
#include "nodes/deepweed_output_buffer.h"
#include "nodes/deepweed_output_sink.h"
#include "nodes/deepweed_threshold_filter.h"
#include "nodes/distance_buffer.h"
#include "nodes/distance_status_retriever.h"
#include "nodes/dummy_sink.h"
#include "nodes/dummy_source.h"
#include "nodes/file_cleanup.h"
#include "nodes/focus_metric.h"
#include "nodes/frequency_focus_metric.h"
#include "nodes/gps_retriever.h"
#include "nodes/image_interest_scorer.h"
#include "nodes/implement_status_retriever.h"
#include "nodes/latest_image.h"
#include "nodes/noop_node.h"
#include "nodes/p2p/burst_record.h"
#include "nodes/p2p/capture_buffer.h"
#include "nodes/p2p/infer.h"
#include "nodes/p2p/output_sink.h"
#include "nodes/p2p/p2p_v4l2_stream.hpp"
#include "nodes/shmem_sink.h"
#include "nodes/target_img_buffer.h"
#include "nodes/transformer.h"
#include "nodes/v4l2_sink.h"
#include "options.h"
#include <config/client/cpp/config_subscriber.hpp>
#include <lib/common/redis/redis_client.hpp>
#include <lib/drivers/nanopb/nofx_board/cpp/wheel_encoder.hpp>
#include <module_server/cpp/client.hpp>
#include <weed_tracking/cpp/client/grpc_client.h>
#include <wheel_encoder/cpp/wheel_encoder.hpp>

#include <generated/proto/cv/cv.pb.h>

using namespace prometheus;
using namespace cv::runtime;
using namespace lib::common;

constexpr double kInchesToMM = 25.4;
constexpr std::chrono::seconds kCameraConnectionTimeoutSeconds = std::chrono::seconds(60);

CameraType get_camera_type(std::shared_ptr<carbon::config::ConfigTree> config_node) {
  const auto &camera_name = config_node->get_name();

  if (camera_name.find("predict") != std::string::npos) {
    return CameraType::kPredict;
  } else if (camera_name.find("target") != std::string::npos) {
    return CameraType::kTarget;
  } else {
    return CameraType::kDrive;
  }
}

bool is_secondary(std::string role, std::string container) { return (role == "row-secondary" || container == "cv2"); }

constexpr int kGRPCListenPort = 15053;
constexpr int kGRPCListenPort2 = 15054;
constexpr int kMetricsListenPort = 62103;
constexpr int kMetricsListenPort2 = 62104;
std::atomic<bool> kShutdown = false;
const std::filesystem::path kModelCachePath = std::filesystem::path("/data/model_manager/model_cache/");
const int kAutoBrightnessReductionRatio = 8;

void signal_handler(int) { kShutdown = true; }

constexpr char kLightweightBurstDirectory[] = "/data/media/burst_recordings/lightweight_burst_recordings/";
constexpr char kFullBurstDirectory[] = "/data/media/burst_recordings/full_burst_recordings/";
const std::string kEmbeddingsCacheDirectory = "/data/embeddings/embeddings_cache/";
const std::string kChipsCacheDirectory = "/data/embeddings/chip_cache/";

class CVRuntime {
public:
  CVRuntime()
      : camera_to_buffer_(new std::map<std::string, std::map<proto::BufferUseCase, std::string>>()),
        camera_id_to_camera_(new std::map<std::string, Camera>()),
        camera_id_to_transpose_(new std::map<std::string, bool>()),
        sim_cameras_(new std::map<std::string, std::pair<std::string, Camera>>()), node_registry_(new NodeRegistry()),
        stream_camera_buffers_(
            new std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, camera::CameraImage>>>()),
        distance_camera_buffers_(
            new std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, camera::CameraImage>>>()),
        burst_record_mutex_(new std::mutex()), config_subscriber_(carbon::config::get_global_config_subscriber()),
        registry_(new Registry()), aimbot_connected_(false),
        deepweed_buffers_(
            new std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<cv::deepweed::DeepweedOutput>>>()),
        p2p_buffers_(new std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<cv::p2p::P2POutput>>>()),
        chip_score_queues_(new std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>()),
        error_state_(new ErrorState()) {

    GeoLLAData default_geo_lla(0.0, 0.0, 0.0, 0);
    GeoECEFData default_geo_ecef(0.0, 0.0, 0.0, 0);
    geo_data_ = std::make_shared<GeoData>(default_geo_lla, default_geo_ecef);
    aimbot_state_ = std::make_shared<AimbotState>(false, false);
    bedtop_profile_ = std::make_shared<BedtopProfile>();
    implement_status_ = std::make_shared<ImplementStatus>(false, false);
    distance_status_ = std::make_shared<DistanceStatus>(false);

    score_queues_["weed_tracking"] = std::make_shared<ScoreQueue<>>(10);
    score_queues_["recency"] = std::make_shared<ScoreQueue<>>(1);
    score_queues_["weed_margin_max"] = std::make_shared<ScoreQueue<>>(10);
    score_queues_["crop_margin_max"] = std::make_shared<ScoreQueue<>>(10);
    score_queues_["ambiguous_weed_count"] = std::make_shared<ScoreQueue<>>(3);
    score_queues_["ambiguous_crop_count"] = std::make_shared<ScoreQueue<>>(3);
    score_queues_["driptape"] = std::make_shared<ScoreQueue<>>(10);
    score_queues_["unknown_plant"] = std::make_shared<ScoreQueue<>>(10);

    score_queues_["emergency_weed_tracking"] = std::make_shared<ScoreQueue<>>(3);
    score_queues_["emergency_recency"] = std::make_shared<ScoreQueue<>>(3);
    score_queues_["emergency_weed_margin_max"] = std::make_shared<ScoreQueue<>>(3);
    score_queues_["emergency_crop_margin_max"] = std::make_shared<ScoreQueue<>>(3);
    score_queues_["emergency_ambiguous_weed_count"] = std::make_shared<ScoreQueue<>>(3);
    score_queues_["emergency_ambiguous_crop_count"] = std::make_shared<ScoreQueue<>>(3);
    score_queues_["emergency_driptape"] = std::make_shared<ScoreQueue<>>(10);
    score_queues_["emergency_unknown_plant"] = std::make_shared<ScoreQueue<>>(10);

    for (int i = 0; i < carbon::aimbot::cv::P2PCaptureReason_ARRAYSIZE; ++i) {
      p2p_queues_.emplace(i, std::make_shared<ScoreQueue<P2PScoreQueueObject>>(1));
    }

    std::string command_addr = carbon::config::make_robot_local_addr(61006);
    hardware_manager_client_ = std::make_shared<hardware_manager::HardwareManagerClient>(command_addr);
    aimbot_client_ = std::make_shared<aimbot::AimbotClient>();
    redis_client_ = std::make_shared<lib::common::RedisClient>();
    module_server_client_ = std::make_shared<carbon::module_server::client::ModuleServerClient>();
    weed_tracking_client_ = std::make_shared<weed_tracking::WeedTrackingClient>();
    category_collection_state_.reset(new CategoryCollectionState(redis_client_));

    bool use_treadkill = config_subscriber_->get_config_node("common", "use_treadkill")->get_value<bool>();
    wheel_encoder_board_ = std::make_shared<carbon::nanopb::nofx_board::WheelEncoderNOFX>(use_treadkill, 20000, 30);
    carbon::wheel_encoder::WheelEncoder::set(wheel_encoder_board_);

    cv::cuda::GpuMat::setDefaultAllocator(&OpenCVAllocator::get());
  }
  void run(const Options &opts, std::string role, std::string container) {
    bool is_secondary_cache = is_secondary(role, container);
    if (opts.model_registry_file && std::filesystem::exists(*opts.model_registry_file)) {
      model_registry_ = std::make_shared<model::ModelRegistry<model::ModelUseCase>>(*opts.model_registry_file);
    } else {
      model_registry_ = std::make_shared<model::ModelRegistry<model::ModelUseCase>>();
    }

    bool privileged = carbon::common::getenv("CARBON_PRIVILEGED") == "1";

    auto metrics_port = kMetricsListenPort;
    if (role == "simulator_minicomputers" && container == "cv2") {
      metrics_port = kMetricsListenPort2;
    }
    exposer_ = std::make_shared<Exposer>(fmt::format("0.0.0.0:{}", metrics_port));
    // Create metrics server and registry
    exposer_->RegisterCollectable(registry_);

    auto deepweed_config = config_subscriber_->get_config_node("common", "deepweed");
    auto aimbot_config = config_subscriber_->get_config_node("aimbot", "");
    auto cameras_config = config_subscriber_->get_config_node("cv", "cameras");
    auto common_config = config_subscriber_->get_config_node("common", "");
    auto dum_config = config_subscriber_->get_config_node("data_upload_manager", "");
    auto embedding_classifier_config = config_subscriber_->get_config_node("cv", "embedding_classifier");
    std::shared_ptr<carbon::config::ConfigTree> simulator_config;
    if (role.rfind("simulator", 0) == 0) {
      simulator_config = config_subscriber_->get_config_node("simulator", "");
    }
    if (!model_registry_->contains(model::ModelUseCase::kPredict)) {
      auto callback = [&]() {
        start_model_load_from_config<nodes::DeepweedInfer>(
            model::ModelUseCase::kPredict, config_subscriber_->get_config_node("common", "deepweed/model_id"),
            embedding_classifier_config);
      };
      callback();
      deepweed_config->get_node("model_id")->register_callback(callback);
    }
    if (!model_registry_->contains(model::ModelUseCase::kP2P)) {
      auto callback = [&]() {
        start_model_load_from_config<nodes::p2p::P2PInfer>(
            model::ModelUseCase::kP2P, config_subscriber_->get_config_node("common", "p2p/model_id"),
            embedding_classifier_config);
      };
      callback();
      config_subscriber_->get_config_node("common", "p2p/model_id")->register_callback(callback);
    }

    if (std::filesystem::exists(std::filesystem::path(opts.config_file)) &&
        (cameras_config->get_children_nodes().size() == 0 || opts.force_load_yaml)) {
      YAML::Node config = YAML::LoadFile(opts.config_file);
      cv_yaml_to_config(config, cameras_config);
    }

    std::unordered_map<std::string, CVHttpClient> http_clients; // predict_id -> http_client

    std::vector<std::string> module_cameras;
    if (carbon::common::is_reaper()) {
      auto module_id = get_module_id();
      auto row_def = get_row_definition(module_id);
      auto leader_hostname = get_leader_hostname(row_def, module_id);
      aimbot_client_ = std::make_shared<aimbot::AimbotClient>(leader_hostname + ":6942");
      weed_tracking_client_ = std::make_shared<weed_tracking::WeedTrackingClient>(leader_hostname + ":65432");
      module_cameras = get_cameras_for_module(row_def, module_id);
      fill_http_clients_for_modules(row_def, module_id, http_clients);
    }

    int num_target_cameras = 0;
    int num_predict_cameras = 0;
    int num_drive_cameras = 0;
    for (auto config_node : cameras_config->get_children_nodes()) {
      auto name = config_node->get_name();
      if (carbon::common::is_reaper() &&
          std::find(module_cameras.begin(), module_cameras.end(), name) == module_cameras.end()) {
        continue;
      }
      bool is_on_primary = config_node->get_node("is_on_primary")->get_value<bool>();
      if (carbon::common::is_slayer() && is_on_primary == is_secondary_cache) {
        http_clients.emplace(std::pair<std::string, CVHttpClient>(name, {}));
        continue;
      }
      const auto camera_type = get_camera_type(config_node);
      if (camera_type == CameraType::kPredict) {
        num_predict_cameras++;
      } else if (camera_type == CameraType::kTarget) {
        num_target_cameras++;
      } else if (camera_type == CameraType::kDrive) {
        num_drive_cameras++;
      }
    }

    const int num_gpus = (int)torch::cuda::device_count();

    gpu_assigner_.reset(new GPUAssigner(role, num_target_cameras, num_predict_cameras, num_drive_cameras, num_gpus));
    embeddings_manager_.reset(new cv::embeddings::Manager(
        std::make_shared<cv::embeddings::Cache>(kEmbeddingsCacheDirectory), model_registry_,
        gpu_assigner_->get_gpu_id_by_index(CameraType::kPredict, 0), kChipsCacheDirectory, registry_));

    nlohmann::json gpu_assignment_override;
    if (config_subscriber_->get_config_node("common", "cv_runtime/gpu_assignment_override")
            ->has_node(std::to_string(num_gpus))) {
      try {
        gpu_assignment_override =
            nlohmann::json::parse(config_subscriber_->get_config_node("common", "cv_runtime/gpu_assignment_override")
                                      ->get_node(std::to_string(num_gpus))
                                      ->get_value<std::string>());
      } catch (nlohmann::json::exception &exception) {
        spdlog::warn("Failed to load gpu assignment override");
      }
    }

    auto start_reload_category_collection_lambda = [&]() {
      start_reload_category_collection(embedding_classifier_config);
    };

    // Start grpc server
    std::unique_ptr<CVRuntimeServiceImplCommon> service;
    if (common_config->get_node("cv_runtime/async_grpc_server")->get_value<bool>()) {
      service = std::make_unique<CVRuntimeServiceImplAsync>(
          node_registry_, camera_to_buffer_, camera_id_to_camera_, camera_id_to_transpose_, sim_cameras_,
          model_registry_, geo_data_, implement_status_, score_queues_, deepweed_config, kLightweightBurstDirectory,
          kFullBurstDirectory, p2p_queues_, deepweed_buffers_, p2p_buffers_, role, distance_camera_buffers_,
          chip_score_queues_, start_reload_category_collection_lambda, embeddings_manager_, registry_, error_state_);

    } else {
      service = std::make_unique<CVRuntimeServiceImplSync>(
          node_registry_, camera_to_buffer_, camera_id_to_camera_, camera_id_to_transpose_, sim_cameras_,
          model_registry_, geo_data_, implement_status_, score_queues_, deepweed_config, kLightweightBurstDirectory,
          kFullBurstDirectory, p2p_queues_, deepweed_buffers_, p2p_buffers_, role, distance_camera_buffers_,
          chip_score_queues_, start_reload_category_collection_lambda, embeddings_manager_, error_state_);
    }

    ImageStreamServiceImpl stream_service(stream_camera_buffers_, distance_camera_buffers_, node_registry_,
                                          aimbot_config, weed_tracking_client_);
    lib::common::logging::LoggingServiceImpl logging_service =
        lib::common::logging::LoggingServiceImpl("cv_runtime.log");
    auto port = kGRPCListenPort;
    if (role == "simulator_minicomputers" && container == "cv2") {
      port = kGRPCListenPort2;
    }
    CVRuntimeGRPCServer server(service.get(), &stream_service, &logging_service, fmt::format("0.0.0.0:{}", port));

    node_registry_->create_node<nodes::FileSystemCleaner>(kLightweightBurstDirectory, kFullBurstDirectory);
    node_registry_->create_node<nodes::GPSRetriever>(geo_data_, hardware_manager_client_);
    node_registry_->create_node<nodes::ImplementStatusRetriever>(implement_status_, hardware_manager_client_);
    node_registry_->create_node<nodes::DistanceStatusRetriever>(distance_status_, hardware_manager_client_);
    node_registry_->create_node<nodes::AimbotStateRetriever>(aimbot_state_, bedtop_profile_, aimbot_client_);

    auto &dropped_frames_counter = prometheus::BuildCounter()
                                       .Name("dropped_frames_total")
                                       .Help("A counter tracking dropped camera frames.")
                                       .Register(*registry_);

    auto &num_deepweed_detections = prometheus::BuildGauge()
                                        .Name("num_deepweed_detections")
                                        .Help("A gauge tracking the number of deepweed detections.")
                                        .Register(*registry_);

    auto devices = camera_registry_.list_devices();
    add_sim_devices(devices, cameras_config);
    spdlog::info(fmt::format("Connected devices: {}", fmt::join(devices, ", ")));
    std::vector<std::shared_ptr<carbon::config::ConfigTree>> cameras_config_nodes =
        cameras_config->get_children_nodes();
    std::sort(cameras_config_nodes.begin(), cameras_config_nodes.end(), [](const auto &first, const auto &second) {
      return std::make_tuple<size_t, std::string>(first->get_name().size(), first->get_name()) <
             std::make_tuple<size_t, std::string>(second->get_name().size(), second->get_name());
    });

    // need to await connection before creating cameras, as aimbot controls target camera power
    // will block until aimbot is connected, always returns true
    if (role == "bud" || role.rfind("row", 0) == 0 || role.rfind("module", 0) == 0 || role == "veselka_cv" ||
        role.rfind("simulator", 0) == 0) {
      aimbot_connected_ = aimbot_client_->await_connection();
    }

    for (auto config_node : cameras_config_nodes) {
      if (kShutdown) {
        spdlog::info("Terminating...");
        return;
      }

      // If this camera is not enabled then skip the entire loop.
      if (!config_node->get_node("enabled")->get_value<bool>()) {
        continue;
      }
      auto name = config_node->get_name();
      if (carbon::common::is_reaper() &&
          std::find(module_cameras.begin(), module_cameras.end(), name) == module_cameras.end()) {
        continue;
      }
      bool is_on_primary = config_node->get_node("is_on_primary")->get_value<bool>();
      if (carbon::common::is_slayer() && is_on_primary == is_secondary_cache) {
        continue;
      }

      auto camera_type = get_camera_type(config_node);
      auto info_pattern = get_camera_info(config_node);
      info_pattern.camera_id = name;
      if (camera_type == CameraType::kTarget && info_pattern.vendor != CameraVendor::kSimulated &&
          !carbon::common::is_reaper()) {
        info_pattern.serial_refresher = [&](const std::string &cam_id) { return this->get_serial_number(cam_id); };
        info_pattern.serial_number = info_pattern.serial_refresher(name); // Need to initialize value
      }

      auto settings = get_camera_settings(config_node);
      bool matched = false;
      lib::common::camera::CameraInfo device = info_pattern;
      for (auto &device_info : devices) {
        if (device_info.matches(info_pattern)) {
          device = device_info.combine(info_pattern);
          // Keep info pattern ip address (if it exists)
          device.ip_address = info_pattern.ip_address;
          matched = true;
        }
      }

      if (!matched && device.vendor == CameraVendor::kUnknown) {
        throw maka_error(fmt::format("Camera {} with {} is not online. Set vendor in config to support booting with "
                                     "offline cameras. Available: {}",
                                     name, device, fmt::join(devices, ", ")));
      }

      int gpu_id;
      if (num_gpus >= 2) {
        gpu_id = gpu_assigner_->get_next_gpu_id(camera_type);
        if (gpu_assignment_override.contains(name)) {
          gpu_id = gpu_assignment_override[name].get<int>();
        }
      } else {
        // If we only have one either testing, or everything is messed up.
        gpu_id = 0;
      }
      if (gpu_id >= num_gpus) {
        spdlog::warn("{} assigned gpu {} but it does not exist. This camera will be unused", name, gpu_id);
        continue;
      }

      settings.gpu_id = gpu_id;

      spdlog::info("Assigned {}: gpu {}", name, gpu_id);

      Camera cam = camera_registry_.create_device(device, settings, config_node);
      camera_id_to_camera_->emplace(name, cam);
      camera_id_to_transpose_->emplace(name, config_node->get_node("transpose")->get_value<bool>());
      if (device.model == "sim-predict" && sim_cameras_->count("sim-predict") == 0) {
        std::pair<std::string, Camera> name_and_cam(name, cam);
        sim_cameras_->emplace("sim-predict", name_and_cam);
      } else if (device.model == "sim-drive" && sim_cameras_->count("sim-drive") == 0) {
        std::pair<std::string, Camera> name_and_cam(name, cam);
        sim_cameras_->emplace("sim-drive", name_and_cam);
      }

      auto &grabber = node_registry_->create_node<nodes::CameraGrabber>(
          name, cam, geo_data_, bedtop_profile_, dropped_frames_counter.Add({{"camera_id", cam.get_info().camera_id}}),
          std::bind(&CVRuntime::reset_local_camera, this, std::placeholders::_1));
      auto camera_output = grabber.get_output();
      camera_to_buffer_->emplace(name, std::map<proto::BufferUseCase, std::string>());

      if (device.model == "sim-target") {
        grabber.set_images(opts.sim_target_image);
      } else if (device.model == "sim-predict") {
        grabber.set_images(opts.sim_predict_image);
      } else if (device.model == "sim-drive") {
        grabber.set_images(opts.sim_drive_image);
      }

      auto &transformer = node_registry_->create_node<nodes::Transformer>(
          name, cam, camera_output.add_output(), config_node->get_node("transpose")->get_value<bool>());
      camera_output = transformer.get_output();

      auto grabber_to_brightness_adj = node_registry_->create_connector(
          camera_output, name, "cam_image_to_brightness_adj", kAutoBrightnessReductionRatio, true);
      node_registry_->create_node<nodes::BrightnessAdjuster>(name, cam, grabber_to_brightness_adj, implement_status_);

      node_registry_->create_node<nodes::BurstRecord>(name, cam, camera_output.add_output(), burst_record_mutex_);

      // P2P model ------------------------------------------------------------------------------------------------
      if (config_node->get_node("p2p_enabled")->get_value<bool>() && !opts.skip_models) {
        auto &p2p_infer = node_registry_->create_model_node<nodes::p2p::P2PInfer>(
            name, model::ModelUseCase::kP2P, cam, camera_output.add_output(), distance_camera_buffers_, model_registry_,
            gpu_id, false, role.rfind("simulator", 0) == 0, http_clients);

        auto save_predicts_config_node = config_node->get_node("burst_record_predict_save_enabled");
        node_registry_->create_node<nodes::p2p::P2PBurstRecord>(name, cam, p2p_infer.get_output().add_output(),
                                                                p2p_infer.get_output_for_burst_record().add_output(),
                                                                distance_camera_buffers_, save_predicts_config_node);
        auto p2p_buffer_name = "cv_p2p_" + name;
        auto p2p_buffer = std::make_shared<LatestInternalBuffer<cv::p2p::P2POutput>>();
        p2p_buffers_->emplace(p2p_buffer_name, p2p_buffer);

        node_registry_->create_node<nodes::p2p::P2POutputSink>(name, cam, p2p_buffer,
                                                               p2p_infer.get_output().add_output());
        camera_to_buffer_->at(name).emplace(proto::BufferUseCase::P2P, p2p_buffer_name);

        auto &buffer = node_registry_->create_node<nodes::TargetImgBuffer>(
            name, cam, camera_output.add_output(), config_node->get_node("buffer_max_size")->get_value<int>(), gpu_id);

        node_registry_->create_node<nodes::p2p::P2PCaptureBuffer>(
            name, cam, p2p_infer.get_output().add_output(), p2p_infer.get_output_for_burst_record().add_output(),
            distance_camera_buffers_, buffer.get_buffer(),
            config_node->get_node("p2p_capture_buffer_size")->get_value<size_t>(),
            config_node->get_node("p2p_capture_max_age")->get_value<uint32_t>(), p2p_queues_, http_clients);

        if (privileged) {
          node_registry_->create_node<nodes::p2p::P2PV4l2Stream>(
              name, cam, p2p_infer.get_output().add_output(), p2p_infer.get_output_for_burst_record().add_output(),
              distance_camera_buffers_, buffer.get_buffer(), config_node->get_node("v4l2/p2p_stream_enabled"));
        }
      }

      // Create the predict camera buffer for the current camera. Note that this emplaces the created buffer in the
      // previously created map named camera_buffers_.
      std::optional<Output<CameraImage>> distance_buffer_output;
      if (config_node->get_node("buffer_enabled")->get_value<bool>()) {
        auto output_to_distance_buffer =
            node_registry_->create_connector(camera_output, name, "cam_image_to_distance_buffer");
        auto &distance_buffer = node_registry_->create_node<nodes::DistanceBuffer>(
            name, cam, output_to_distance_buffer, config_node->get_node("distance_buffer_max_size")->get_value<int>(),
            gpu_id, config_node->get_node("distance_buffer_interval_inches")->get_value<float>() * kInchesToMM,
            distance_status_, role.rfind("simulator", 0) == 0);
        distance_camera_buffers_->emplace(name, distance_buffer.get_buffer());
        distance_buffer_output = distance_buffer.get_output();
      }

      // Deepweed model -------------------------------------------------------------------------------------------
      if (config_node->get_node("deepweed_enabled")->get_value<bool>() && !opts.skip_models) {
        auto camera_output_for_deepweed = camera_output;
        if (config_node->get_node("only_use_distance_based")->get_value<bool>() && distance_buffer_output) {
          camera_output_for_deepweed = *distance_buffer_output;
        }
        auto &deepweed_infer = node_registry_->create_model_node<nodes::DeepweedInfer>(
            name, model::ModelUseCase::kPredict, cam, camera_output_for_deepweed.add_output(), model_registry_, gpu_id,
            deepweed_config, simulator_config, registry_, distance_camera_buffers_);

        auto deepweed_buffer_name = "cv_deepweed_" + name;

        if (config_node->get_node("buffer_enabled")->get_value<bool>()) {
          // Creates interest score connector and node for each predict camera
          node_registry_->create_node<nodes::ImageInterestScorer>(
              name, cam, deepweed_infer.get_output().add_output(), score_queues_, distance_camera_buffers_,
              implement_status_, geo_data_, distance_status_, aimbot_state_, deepweed_config);

          node_registry_->create_node<nodes::ChipInterestScorer>(
              name, cam,
              deepweed_infer.get_output().add_output(
                  config_node->get_node("chip_interest_scorer/reduction_ratio")->get_value<int>(),
                  config_node->get_node("chip_interest_scorer/overwrite_connector")->get_value<bool>()),
              distance_camera_buffers_, chip_score_queues_, model_registry_, implement_status_, geo_data_,
              distance_status_, aimbot_state_, weed_tracking_client_, dum_config);
        }

        auto &deepweed_threshold_buffer = node_registry_->create_node<nodes::DeepweedThresholdFilter>(
            name, cam, deepweed_infer.get_output().add_output(), deepweed_config,
            std::make_shared<std::reference_wrapper<prometheus::Gauge>>(
                num_deepweed_detections.Add({{"camera_id", cam.get_info().camera_id}})));

        auto &deepweed_threshold_buffer_diagnostics =
            node_registry_->create_node<nodes::DeepweedThresholdFilterForWeedingDiagnostics>(
                name, cam, deepweed_infer.get_output().add_output(), deepweed_config);

        node_registry_->create_node<nodes::DeepweedOutputBuffer>(
            name, cam, deepweed_threshold_buffer_diagnostics.get_output().add_output(),
            config_node->get_node("buffer_max_size")->get_value<int>());

        auto buffer = std::make_shared<LatestInternalBuffer<cv::deepweed::DeepweedOutput>>();
        deepweed_buffers_->emplace(deepweed_buffer_name, buffer);
        node_registry_->create_node<nodes::DeepweedOutputSink>(
            name, cam, deepweed_threshold_buffer.get_output().add_output(), buffer);
        camera_to_buffer_->at(name).emplace(proto::BufferUseCase::Predict, deepweed_buffer_name);
      }

      if (privileged) {
        // TODO at some point figure out the right number of channels per camera
        node_registry_->create_node<nodes::V4L2ImageSink>(
            name, cam, name, 3, false, config_node->get_node("transpose")->get_value<bool>(),
            camera_output.add_output(config_node->get_node("v4l2/reduction_ratio")->get_value<int>()),
            config_node->get_node("v4l2/enabled"), config_node->get_node("v4l2/rescale_ratio")->get_value<float>());
      }

      auto connector =
          node_registry_->create_connector(camera_output, name, "buffer_publisher_reduction",
                                           config_node->get_node("publisher_reduction_ratio")->get_value<int>());
      auto &noop_node = node_registry_->create_node<nodes::NoopNode<CameraImage>>(connector);
      camera_output = noop_node.get_output();

      auto &stream_buffer = node_registry_->create_node<nodes::Buffer>(cam, camera_output.add_output(), 1, gpu_id,
                                                                       role.rfind("simulator", 0) == 0);
      stream_camera_buffers_->emplace(name, stream_buffer.get_buffer());

      if (config_node->get_node("focus_metric_enabled")->get_value<bool>()) {
        auto &focus_metric_node =
            node_registry_->create_node<nodes::FocusMetric>(name, cam, camera_output.add_output());
        camera_output = focus_metric_node.get_output();
      }

      if (config_node->get_node("frequency_focus_metric_enabled")->get_value<bool>()) {
        auto frequency_connector = node_registry_->create_connector(
            camera_output, name, "frequency_focus_metric_reduction",
            config_node->get_node("frequency_focus_metric_reduction_ratio")->get_value<int>());
        auto &frequency_focus_metric_node =
            node_registry_->create_node<nodes::FrequencyFocusMetric>(name, cam, frequency_connector);
        camera_output = frequency_focus_metric_node.get_output();
      }

      if (opts.dummy_publisher) {
        node_registry_->create_node<nodes::DummySink>(name, cam, camera_output.add_output());
      } else {
        auto image_buffer_name = "cv_" + name;
        node_registry_->create_node<nodes::LatestImageShmemSink>(name, cam, image_buffer_name, false,
                                                                 camera_output.add_output());
      }

      node_registry_->create_node<nodes::LatestImage>(name, cam, camera_output.add_output());

      for (auto &node : node_registry_->get_nodes()) {
        node.check();
      }
    }
    start_reload_category_collection(embedding_classifier_config);
    MetricsLogger metrics_logger(node_registry_, registry_);
    CVHttpServer http_server(distance_camera_buffers_);

    spdlog::info("Waiting for cameras to connect...");
    if (!block_until_cameras_connected(kCameraConnectionTimeoutSeconds)) {
      spdlog::warn("Timed out waiting for cameras to connect");
    }

    spdlog::info("Running until terminated...");
    service->set_booted(true);
    while (!kShutdown) {
      std::this_thread::sleep_for(std::chrono::seconds(1));
      for (auto &node : node_registry_->get_nodes()) {
        node.check();
      }
      metrics_logger.update();
      service->Check();
      service->ReportMetrics();
      if (opts.verbose) {
        spdlog::info(
            "---------------------------------------------------------------------------------------------------"
            "---------------");
        for (auto &node : node_registry_->get_nodes()) {
          if (node.get_name() == "noop") {
            continue;
          }
          // The way real world->output latency is the sum of image->output latency and half of the period between
          // images. We use half of the period because that's the expected value of the time it takes to capture a new
          // input. We use full period for 99%-tile calculation.
          spdlog::info("{} running at {:.1f} FPS, image->output latency: {:.1f} ms ({:.1f} ms 99%-tile), real "
                       "world->output latency: {:.1f} ms ({:.1f} ms 99%-tile)..",
                       node.get_name(), node.get_mean_fps(), node.get_mean_latency_ms(), node.get_99pct_latency_ms(),
                       500.0 / (node.get_mean_fps() + 1e-5) + node.get_mean_latency_ms(),
                       1000.0 / (node.get_99pct_fps() + 1e-5) + node.get_99pct_latency_ms());
        }
      }
    }
  }

private:
  template <typename ModelNodeType>
  void start_model_load_from_config(model::ModelUseCase model_use_case,
                                    std::shared_ptr<carbon::config::ConfigTree> model_config,
                                    std::shared_ptr<carbon::config::ConfigTree> embedding_cfg) {
    model_reload_done_[model_use_case] = true;
    model_reload_done_condition_[model_use_case].notify_all();
    if (model_reload_threads_.count(model_use_case) > 0 && model_reload_threads_.at(model_use_case).joinable()) {
      model_reload_threads_.at(model_use_case).join();
    }

    model_reload_done_[model_use_case] = false;
    model_reload_threads_[model_use_case] =
        std::thread([=]() { update_model_from_config<ModelNodeType>(model_use_case, model_config, embedding_cfg); });
  }

  template <typename ModelNodeType>
  void update_model_from_config(model::ModelUseCase model_use_case,
                                std::shared_ptr<carbon::config::ConfigTree> model_config,
                                std::shared_ptr<carbon::config::ConfigTree> embedding_cfg) {
    for (const std::string &cam_id : node_registry_->get_known_cameras()) {
      if (node_registry_->has_model_node(cam_id, model_use_case)) {
        node_registry_->get_model_node<ModelNodeType>(cam_id, model_use_case).clear_model();
      }
    }

    while (!model_reload_done_[model_use_case]) {
      auto model_id = model_config->get_value<std::string>();
      std::filesystem::path model_path;
      if (model_use_case == model::ModelUseCase::kDriving) {
        model_path = model_id;
      } else {
        if (std::filesystem::exists(kModelCachePath / model_id)) {
          model_path = kModelCachePath / model_id;
        } else {
          model_path = kModelCachePath / (model_id + ".trt");
        }
      }
      if (model_id.empty() || !std::filesystem::exists(model_path)) {
        spdlog::warn("Unable to load model: {}. Waiting {}", model_path.string(), kModelLoadRetryDelay);
        std::unique_lock<std::mutex> lck(model_reload_done_mutex_[model_use_case]);
        model_reload_done_condition_[model_use_case].wait_for(
            lck, kModelLoadRetryDelay, [&]() { return model_reload_done_[model_use_case].load(); });
        continue;
      }

      std::string cached_path;
      if (model_registry_->contains(model_use_case)) {
        cached_path = model_registry_->get_path_for_use_case(model_use_case);
      }

      model_registry_->set_model_for_use_case(model_use_case, model_path.string());
      model_registry_->clear_path_from_cache(cached_path);
      try {
        // Try to fail loading early
        model_registry_->get_internal_metadata_by_use_case(model_use_case);

        // Trigger reload of embeddings so they are based on the new model
        bool model_reloaded = false;
        if (model_use_case == model::ModelUseCase::kPredict) {
          category_collection_reload_done_ = true;
          category_collection_reload_done_condition_.notify_all();
          if (category_collection_reload_thread_.joinable()) {
            category_collection_reload_thread_.join();
          }
          category_collection_reload_done_ = false;
          model_reloaded = reload_category_collection(embedding_cfg);
        }

        if (!model_reloaded) {
          for (const std::string &cam_id : node_registry_->get_known_cameras()) {
            if (node_registry_->has_model_node(cam_id, model_use_case)) {
              node_registry_->get_model_node<ModelNodeType>(cam_id, model_use_case).reload_model();
            }
          }
        }
      } catch (const maka_error &err) {
        for (const std::string &cam_id : node_registry_->get_known_cameras()) {
          if (node_registry_->has_model_node(cam_id, model_use_case)) {
            node_registry_->get_model_node<ModelNodeType>(cam_id, model_use_case).clear_model();
          }
        }
        model_registry_->clear_model_for_use_case(model_use_case);
        spdlog::warn("Unable to load model: {}. Waiting {}", model_path.string(), kModelLoadRetryDelay);
        std::this_thread::sleep_for(kModelLoadRetryDelay);
        continue;
      }

      break;
    }
  }

  void start_reload_category_collection(std::shared_ptr<carbon::config::ConfigTree> embedding_cfg) {
    category_collection_reload_done_ = true;
    category_collection_reload_done_condition_.notify_all();
    if (category_collection_reload_thread_.joinable()) {
      category_collection_reload_thread_.join();
    }

    category_collection_reload_done_ = false;
    category_collection_reload_thread_ = std::thread([=]() { reload_category_collection(embedding_cfg); });
  }

  // Returns true if we reloaded the model. Allows us to skip reloading the model twice when switching models.
  bool reload_category_collection(std::shared_ptr<carbon::config::ConfigTree> embedding_cfg) {
    lib::common::CategoryCollection category_collection;

    while (!category_collection_reload_done_) {
      auto category_collection_opt = category_collection_state_->get();
      if (!category_collection_opt || category_collection_opt->category_collection.category_ids().size() == 0) {
        // Invalid category collection stored in redis. We'll stop trying to load it since we'll be notified when it
        // changes
        spdlog::warn("Unable to load category collection from redis. Won't retry.");
        for (const std::string &cam_id : node_registry_->get_known_cameras()) {
          if (node_registry_->has_model_node(cam_id, model::ModelUseCase::kPredict)) {
            node_registry_->get_model_node<nodes::DeepweedInfer>(cam_id, model::ModelUseCase::kPredict)
                .clear_classifier();
          }
        }
        error_state_->plant_profile_error = false;
        return false;
      }
      if (embeddings_manager_->is_valid_category_collection(category_collection_opt->category_collection,
                                                            category_collection_opt->category_definitions)) {
        category_collection = *category_collection_opt;
        break;
      }

      error_state_->plant_profile_error = true;
      spdlog::warn("Unable to load category collection: {}. Waiting {}",
                   category_collection_opt->category_collection.name(), kModelLoadRetryDelay);
      std::unique_lock<std::mutex> lck(category_collection_reload_done_mutex_);
      category_collection_reload_done_condition_.wait_for(lck, kModelLoadRetryDelay,
                                                          [&]() { return category_collection_reload_done_.load(); });
    }
    if (category_collection_reload_done_) {
      return false;
    }
    error_state_->plant_profile_error = false;

    auto embedding_algorithm = embedding_cfg->get_node("algorithm")->get_value<std::string>();
    spdlog::info("Reloading category collection: {} with algorithm type: {}",
                 category_collection.category_collection.id(), embedding_algorithm);

    for (const std::string &cam_id : node_registry_->get_known_cameras()) {
      if (node_registry_->has_model_node(cam_id, model::ModelUseCase::kPredict)) {
        node_registry_->get_model_node<nodes::DeepweedInfer>(cam_id, model::ModelUseCase::kPredict).clear_model();
      }
    }

    const std::string crop_id = carbon::config::get_global_config_subscriber()
                                    ->get_config_node("commander", "current_crop_id")
                                    ->get_value<std::string>();
    try {
      embeddings_manager_->set_active_category_collection(category_collection.category_collection,
                                                          category_collection.category_definitions,
                                                          category_collection.last_updated_timestamp_ms, crop_id);
    } catch (const cv::embeddings::embeddings_unsupported_exception &err) {
      spdlog::warn("Model does not support embeddings. Setting active category collection failed.");
      error_state_->model_unsupported_embeddings = true;
      for (const std::string &cam_id : node_registry_->get_known_cameras()) {
        if (node_registry_->has_model_node(cam_id, model::ModelUseCase::kPredict)) {
          node_registry_->get_model_node<nodes::DeepweedInfer>(cam_id, model::ModelUseCase::kPredict).reload_model();
        }
      }
      return true;
    }
    error_state_->model_unsupported_embeddings = false;

    for (const std::string &cam_id : node_registry_->get_known_cameras()) {
      if (node_registry_->has_model_node(cam_id, model::ModelUseCase::kPredict)) {
        auto &model_node = node_registry_->get_model_node<nodes::DeepweedInfer>(cam_id, model::ModelUseCase::kPredict);
        auto categories = embeddings_manager_->get_category_collection(model_node.get_gpu_id());
        std::shared_ptr<cv::embeddings::Classifier> classifier;
        if (embedding_algorithm == "random_forest") {
          classifier = std::make_shared<cv::embeddings::RandomForestClassifier>(
              cv::embeddings::RandomForestClassifier(cv::embeddings::cosine_distance));
        } else {
          classifier = std::make_shared<cv::embeddings::NearestNeighborClassifier>(
              cv::embeddings::NearestNeighborClassifier(cv::embeddings::cosine_distance));
        }
        classifier->set_category_collection(categories, embeddings_manager_->get_categories());
        model_node.set_classifier(classifier);
        model_node.reload_model();
      }
    }
    spdlog::info("Reloaded category collection: {}", category_collection.category_collection.id());
    embeddings_manager_->update_category_collection_metrics();
    return true;
  }

  void add_sim_devices(std::vector<CameraInfo> &camera_info,
                       std::shared_ptr<carbon::config::ConfigTree> cameras_config) {
    for (auto config_node : cameras_config->get_children_nodes()) {
      auto info_pattern = get_camera_info(config_node);
      if (info_pattern.model == "sim-target" || info_pattern.model == "sim-predict" ||
          info_pattern.model == "sim-drive") {
        info_pattern.vendor = CameraVendor::kSimulated;
        camera_info.push_back(info_pattern);
      }
    }
  }

  std::string get_serial_number(const std::string &name) {
    for (size_t i = 0; i < 3; ++i) {
      if (!aimbot_connected_) {
        aimbot_client_->await_connection();
      }
      aimbot_connected_ = true;
      try {
        return aimbot_client_->get_target_camera_serial_number(name);
      } catch (const maka_error &e) {
        aimbot_connected_ = false;
      }
    }
    throw maka_error(fmt::format("Failed to get serial number for cammera {}", name));
  }

  uint32_t get_module_id() {
    while (!kShutdown) {
      try {
        auto [id, _] = module_server_client_->GetModuleIdentity();
        if (id == 0) {
          throw maka_error("Module id is unset");
        }
        return id;
      } catch (const std::exception &e) {
        spdlog::error("Failed to get module identity: {}, retrying", e.what());
        std::this_thread::sleep_for(std::chrono::seconds(30));
      }
    }
    return 0;
  }

  // TODO, unify with robot_definition.cpp
  nlohmann::json get_row_definition(int module_id) {
    redis_client_->wait_until_ready();
    while (!kShutdown) {
      try {
        auto encoded_robot_def = redis_client_->hget("robot_definition/", "current");
        if (!encoded_robot_def) {
          throw maka_error("Failed to get robot definition from redis");
        }
        std::string robot_def_str = encoded_robot_def.value();
        spdlog::info("Got robot definition: {}", robot_def_str);
        auto robot_def = nlohmann::json::parse(robot_def_str);
        for (auto &row : robot_def["rows"]) {
          for (auto &module : row["modules"]) {
            if (module["module_id"].get<int>() == module_id) {
              spdlog::info("row: {}", row.dump());
              return row;
            }
          }
        }
        throw maka_error(fmt::format("Failed to find module {} in robot definition", module_id));
      } catch (const std::exception &e) {
        spdlog::error("Failed to get robot definition: {}, retrying", e.what());
        std::this_thread::sleep_for(std::chrono::seconds(30));
      }
    }
    throw maka_error("Failed to get robot definition from redis");
  }

  std::string get_leader_hostname(nlohmann::json &row_def, int module_id) {
    for (auto &module : row_def["modules"]) {
      if (module["aimbot_host"].get<bool>()) {
        std::string aimbot_addr = "127.0.0.1";
        int id = module["module_id"].get<int>();
        if (id != module_id) {
          aimbot_addr = fmt::format("10.10.20.{}", id);
        }
        spdlog::info("aimbot address: {}", aimbot_addr);
        return aimbot_addr;
      }
    }
    throw maka_error("Failed to find aimbot address in robot definition");
  }

  std::vector<std::string> get_cameras_for_module(nlohmann::json &row_def, int module_id) {
    std::vector<std::string> cameras;
    int idx = 0;
    for (auto &module : row_def["modules"]) {
      ++idx;
      if (module["module_id"].get<int>() == module_id) {
        cameras.push_back(fmt::format("predict{}", idx));
        cameras.push_back(fmt::format("target{}", idx * 2 - 1));
        cameras.push_back(fmt::format("target{}", idx * 2));
        spdlog::info("cameras: {}", fmt::join(cameras, ", "));
        return cameras;
      }
    }
    throw maka_error(fmt::format("Failed to find module {} in robot definition", module_id));
  }

  void fill_http_clients_for_modules(nlohmann::json &row_def, int module_id,
                                     std::unordered_map<std::string, CVHttpClient> &http_clients) {
    int idx = 0;
    for (auto &module : row_def["modules"]) {
      ++idx;
      int id = module["module_id"].get<int>();
      bool disabled = module["disabled"].get<bool>();
      if (id == module_id) { // don't add self
        continue;
      } else if (disabled) {
        spdlog::info("Skipping http client for disabled module {}", id);
        continue;
      }
      std::string hostname = fmt::format("10.10.20.{}", id);
      http_clients.emplace(
          std::pair<std::string, CVHttpClient>(fmt::format("predict{}", idx), fmt::format("{}:{}", hostname, 15055)));
      spdlog::info("http_clients: {} -> {}", fmt::format("predict{}", idx), fmt::format("{}:{}", hostname, 15055));
    }
  }

  bool block_until_cameras_connected(std::chrono::seconds timeout) {
    auto start = std::chrono::steady_clock::now();
    while (!kShutdown) {
      bool all_connected = true;
      for (const auto &[id, cam] : *camera_id_to_camera_) {
        auto &camera_grabber = node_registry_->get_typed_node<nodes::CameraGrabber>(id);
        auto last_error_type = camera_grabber.get_last_error_type();
        if (last_error_type == nodes::ERROR_TYPE::CONNECTION) {
          spdlog::info("Camera {} is not connected, retrying...", id);
          all_connected = false;
          break;
        }
      }
      if (all_connected) {
        spdlog::info("All cameras are connected");
        return true;
      }
      if (timeout != std::chrono::seconds::zero() && std::chrono::steady_clock::now() - start > timeout) {
        spdlog::warn("Timed out waiting for cameras to connect");
        return false;
      }
      std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    return false;
  }

  void reset_local_camera(const std::string &cam_name) {
    auto module_id = get_module_id();
    spdlog::info("Attempting to power cycle camera {}", cam_name);
    hardware_manager_client_->power_cycle_cam(module_id, cam_name, std::chrono::milliseconds(100));
  }

  const std::chrono::seconds kModelLoadRetryDelay = std::chrono::seconds(30);

  CameraRegistry camera_registry_;
  std::shared_ptr<std::map<std::string, std::map<proto::BufferUseCase, std::string>>> camera_to_buffer_;
  std::shared_ptr<std::map<std::string, Camera>> camera_id_to_camera_;
  std::shared_ptr<std::map<std::string, bool>> camera_id_to_transpose_;
  std::shared_ptr<std::map<std::string, std::pair<std::string, Camera>>> sim_cameras_;
  std::shared_ptr<NodeRegistry> node_registry_;
  std::shared_ptr<model::ModelRegistry<model::ModelUseCase>> model_registry_;
  std::shared_ptr<std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, camera::CameraImage>>>>
      stream_camera_buffers_;
  std::shared_ptr<std::map<std::string, std::shared_ptr<lib::common::FixedBuffer<int64_t, camera::CameraImage>>>>
      distance_camera_buffers_;
  std::shared_ptr<std::mutex> burst_record_mutex_;
  std::map<std::string, std::shared_ptr<ScoreQueue<>>> score_queues_;
  std::unordered_map<int, std::shared_ptr<ScoreQueue<P2PScoreQueueObject>>> p2p_queues_;
  std::shared_ptr<GeoData> geo_data_;
  std::shared_ptr<AimbotState> aimbot_state_;
  std::shared_ptr<BedtopProfile> bedtop_profile_;
  std::shared_ptr<ImplementStatus> implement_status_;
  std::shared_ptr<DistanceStatus> distance_status_;
  std::shared_ptr<carbon::config::ConfigSubscriber> config_subscriber_;
  std::shared_ptr<Exposer> exposer_;
  std::shared_ptr<Registry> registry_;
  std::shared_ptr<hardware_manager::HardwareManagerClient> hardware_manager_client_;
  std::shared_ptr<aimbot::AimbotClient> aimbot_client_;
  std::map<model::ModelUseCase, std::thread> model_reload_threads_;
  std::map<model::ModelUseCase, std::atomic<bool>> model_reload_done_;
  std::map<model::ModelUseCase, std::condition_variable> model_reload_done_condition_;
  std::map<model::ModelUseCase, std::mutex> model_reload_done_mutex_;
  std::thread category_collection_reload_thread_;
  std::atomic<bool> category_collection_reload_done_;
  std::condition_variable category_collection_reload_done_condition_;
  std::mutex category_collection_reload_done_mutex_;
  bool aimbot_connected_;
  std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<cv::deepweed::DeepweedOutput>>>>
      deepweed_buffers_;
  std::shared_ptr<std::unordered_map<std::string, std::shared_ptr<LatestInternalBuffer<cv::p2p::P2POutput>>>>
      p2p_buffers_;
  std::shared_ptr<std::map<std::string, std::shared_ptr<ScoreQueue<ChipScoreQueueObject>>>> chip_score_queues_;
  std::shared_ptr<lib::common::RedisClient> redis_client_;
  std::shared_ptr<carbon::module_server::client::ModuleServerClient> module_server_client_;
  std::shared_ptr<weed_tracking::WeedTrackingClient> weed_tracking_client_;
  std::shared_ptr<cv::embeddings::Manager> embeddings_manager_;
  std::shared_ptr<GPUAssigner> gpu_assigner_;
  std::unique_ptr<lib::common::CategoryCollectionState> category_collection_state_;
  std::shared_ptr<carbon::nanopb::nofx_board::WheelEncoderNOFX> wheel_encoder_board_;
  std::shared_ptr<ErrorState> error_state_;
};

int main(int argc, char **argv) {
  std::signal(SIGINT, signal_handler);
  std::signal(SIGTERM, signal_handler);

  std::string role = carbon::common::getenv("MAKA_ROLE");
  std::string container = carbon::common::getenv("HOSTNAME");

  std::shared_ptr<carbon::config::ConfigSubscriber> config_subscriber = carbon::config::get_global_config_subscriber();
  config_subscriber->add_config_tree("common", "common", "services/common.yaml");
  config_subscriber->add_config_tree("cv", carbon::config::get_computer_config_prefix() + "/cv", "services/cv.yaml");
  config_subscriber->add_config_tree("aimbot", carbon::config::get_computer_config_prefix() + "/aimbot",
                                     "services/aimbot.yaml");
  config_subscriber->add_config_tree("commander", carbon::config::get_command_computer_config_prefix() + "/commander",
                                     "services/commander.yaml");
  config_subscriber->add_config_tree("data_upload_manager",
                                     carbon::config::get_command_computer_config_prefix() + "/data_upload_manager",
                                     "services/data_upload_manager.yaml");
  if (role.rfind("simulator", 0) == 0) {
    config_subscriber->add_config_tree("simulator", "simulator", "services/simulator.yaml");
  }

  if (role == "bud" || role.rfind("row", 0) == 0 || role.rfind("module", 0) == 0 || role == "veselka_cv" ||
      role.rfind("simulator", 0) == 0) {
    config_subscriber->start();
    spdlog::info("Opening Connection to Config Service...");
    config_subscriber->wait_until_ready();
  } else {
    spdlog::info("Skipping Config Service connection when run under role: {}", role);
  }

  if (config_subscriber->get_config_node("common", "environment")->get_value<std::string>() == "production") {
    SentryReporter::initialize();
  }

  CVRuntime cv_runtime;

  try {
    Options opts = parse_options(argc, argv);
    cv_runtime.run(opts, role, container);
    return 0;
  } catch (std::exception &ex) {
    spdlog::error(exception_to_string(ex));
    spdlog::shutdown();
    SentryReporter::report_exception(ex);
    SentryReporter::shutdown();

    // Intentionally skip cleaning up nodes to avoid cv runtime hanging and not restarting on error
    std::abort();
  }
}

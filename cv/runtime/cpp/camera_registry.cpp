#include <fmt/format.h>
#include <fmt/ostream.h>

#include "camera_registry.h"
#include "lib/common/cpp/exceptions.h"

namespace cv {
namespace runtime {

std::vector<CameraInfo> CameraRegistry::list_devices() {
  std::vector<CameraInfo> result;
  for (auto &f : factories_) {
    auto f_cameras = f->list_devices();
    result.insert(result.end(), f_cameras.begin(), f_cameras.end());
  }
  return result;
}

Camera CameraRegistry::create_device(const CameraInfo &info, const CameraSettings &settings,
                                     std::shared_ptr<carbon::config::ConfigTree> camera_config) {
  for (auto &f : factories_) {
    if (info.vendor == f->get_vendor()) {
      return f->create_device(info, settings, camera_config);
    }
  }
  throw maka_error(fmt::format("No factory for vendor {}", info.vendor));
}

} // namespace runtime
} // namespace cv
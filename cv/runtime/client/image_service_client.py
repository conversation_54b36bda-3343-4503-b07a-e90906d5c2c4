from typing import Optional, cast

import grpc

from cv.runtime.client.utils import CVCameraMap, get_secondary_hostname, get_secondary_port
from generated.frontend.proto.image_stream_pb2 import CameraImageRequest, Image
from generated.frontend.proto.image_stream_pb2_grpc import ImageStreamServiceStub
from generated.frontend.proto.util_pb2 import Timestamp
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class ImageServiceClient:
    def __init__(self, hostname: str = "localhost", port: int = 15053, hostname2: str = "", port2: int = 0) -> None:
        self._addr = f"{hostname}:{port}"
        self._channel = None
        self._stub: Optional[ImageStreamServiceStub] = None
        if hostname2 == "":
            hostname2 = get_secondary_hostname(hostname)
        if port2 == 0:
            port2 = get_secondary_port()
        self._addr2 = f"{hostname2}:{port2}"
        self._channel2 = None
        self._stub2: Optional[ImageStreamServiceStub] = None
        self._cam_map: CVCameraMap = CVCameraMap()

    def has_secondary(self) -> bool:
        return self._cam_map.has_secondary()

    def _get_primary_grpc_stub(self) -> ImageStreamServiceStub:
        if self._stub is None:
            if self._channel is None:
                self._channel = grpc.aio.insecure_channel(self._addr)
            self._stub = ImageStreamServiceStub(self._channel)
        return self._stub

    def _get_secondary_grpc_stub(self) -> ImageStreamServiceStub:
        if self._stub2 is None:
            if self._channel2 is None:
                self._channel2 = grpc.aio.insecure_channel(self._addr2)
            self._stub2 = ImageStreamServiceStub(self._channel2)
        return self._stub2

    def _get_grpc_stub(self, cam_id: Optional[str] = None) -> ImageStreamServiceStub:
        if cam_id is None:
            return self._get_primary_grpc_stub()

        return self._get_primary_grpc_stub() if self._cam_map.is_on_primary(cam_id) else self._get_secondary_grpc_stub()

    def reset(self) -> None:
        self._stub = None
        self._channel = None
        if self.has_secondary():
            self._stub2 = None
            self._channel2 = None

    async def get_next_camera_image(
        self,
        cam_id: str,
        timestamp_ms: int,
        annotated: bool,
        include_annotations_metadata: bool,
        dont_downsample: bool,
        encode_as_png: bool,
        encode_as_raw: bool,
    ) -> Image:
        stub = self._get_grpc_stub(cam_id)
        req = CameraImageRequest(
            cam_id=cam_id,
            ts=Timestamp(timestamp_ms=timestamp_ms),
            annotated=annotated,
            include_annotations_metadata=include_annotations_metadata,
            dont_downsample=dont_downsample,
            encode_as_png=encode_as_png,
            encode_as_raw=encode_as_raw,
        )
        return cast(Image, await stub.GetNextCameraImage(req))

import asyncio
from typing import cast

import grpc

import generated.cv.runtime.proto.cv_runtime_pb2 as pb
from generated.cv.runtime.proto.cv_runtime_pb2_grpc import CVRuntimeServiceStub
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger

LOG = get_logger(__name__)

DEFAULT_CV_HOST = "localhost"
DEFAULT_CV_PORT = 15053


class BaseCVRuntimeClient:
    def __init__(self, hostname: str = DEFAULT_CV_HOST, port: int = DEFAULT_CV_PORT):
        self._hostname = hostname
        self._port = port
        self._reset()

    def _reset(self) -> None:
        self._channel = grpc.aio.insecure_channel(f"{self._hostname}:{self._port}")
        self._stub = CVRuntimeServiceStub(self._channel)

    async def get_booted(self, timeout: int = 60) -> bool:
        try:
            response = cast(pb.GetBootedResponse, await self._stub.GetBooted(pb.GetBootedRequest(), timeout=timeout))
            return response.booted
        except grpc.RpcError:
            LOG.warning("Failed To Connect to CV Runtime.")
        return False

    async def wait_for_cv(self) -> bool:
        task = asyncio.create_task(self.get_booted(60))
        while not bot_stop_handler.stopped:
            if task.done():
                if await task:
                    return True
                task = asyncio.create_task(self.get_booted(60))
            await asyncio.sleep(1)
        return False

import torch

try:
    import cupy as xp

    CUPY_IMPORTED = True
except ImportError:
    import numpy as xp

    CUPY_IMPORTED = False


def set_cupy_memory_allocator() -> None:
    if CUPY_IMPORTED:
        xp.cuda.set_allocator(_torch_alloc)


if CUPY_IMPORTED:

    def _torch_alloc(size: int) -> xp.cuda.MemoryPointer:
        device = xp.cuda.Device().id
        tensor = torch.empty(size, dtype=torch.uint8, device=device)
        data_ptr = tensor.data_ptr()
        assert data_ptr, "torch data pointer is a null pointer in cupy allocation"
        return xp.cuda.MemoryPointer(xp.cuda.UnownedMemory(data_ptr, size, tensor), 0)

#pragma once

namespace cv::utils {

inline glm::vec2 adjust_size_ppi(glm::vec2 coord, float ppi, float new_ppi) { return coord / ppi * new_ppi; }

inline std::tuple<torch::Tensor, glm::vec2> crop_or_pad(torch::Tensor image, glm::ivec2 coord, glm::ivec2 size) {
  // extra padding with half crop on all edges
  image = F::pad(image, F::PadFuncOptions({
                            // padding for width
                            (int64_t)std::floor(size.x / 2),
                            (int64_t)std::ceil(size.x / 2),
                            // padding for height
                            (int64_t)std::floor(size.y / 2),
                            (int64_t)std::ceil(size.y / 2),
                        }));
  image = image.slice(-1, coord.x, coord.x + size.x).slice(-2, coord.y, coord.y + size.y);

  glm::vec2 new_coord;
  new_coord.x = (float)std::floor(size.x / 2);
  new_coord.y = (float)std::floor(size.y / 2);

  return std::make_tuple(image, new_coord);
}
} // namespace cv::utils

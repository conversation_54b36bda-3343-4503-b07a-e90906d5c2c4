#include "to_gpu_node.h"

namespace cv::deck {
ToGPUNode::ToGPUNode(std::string camera_id, cv::runtime::Input<lib::common::camera::CameraImage> input, int gpu_id)
    : CVNodeImpl(fmt::format("/to_gpu_node/{}", camera_id)), input_(input), gpu_id_(gpu_id) {}

void ToGPUNode::close_connectors() { input_.close(); }

int64_t ToGPUNode::tick() {
  auto image = input_.pop();

  image = image.cuda(gpu_id_);

  output_.push(image);
  return image.timestamp_ms;
}

cv::runtime::Output<lib::common::camera::CameraImage> &ToGPUNode::get_output() { return output_; }

} // namespace cv::deck

#include "deck_cv.h"

#include <fmt/format.h>

#include "config/client/cpp/config_subscriber.hpp"
#include "cv/deck/furrows_node.h"
#include "cv/deck/furrows_writer_node.h"
#include "cv/deck/grpc_service.h"
#include "cv/deck/hdr_brightness_adjuster.h"
#include "cv/deck/to_gpu_node.h"
#include "lib/common/bot/cpp/stop_handler/stop_handler.hpp"
#include "lib/common/cpp/opencv_allocator.h"
#include "lib/common/cpp/time.h"
#include "metrics/cpp/prometheus/exposer_owner.hpp"

#include <prometheus/registry.h>

namespace cv::deck {

DeckCV::DeckCV(std::shared_ptr<carbon::atomic_data_share::AtomicDataWrapper<float>> speed_mph)
    : latest_furrow_detections_(
          std::make_shared<std::unordered_map<
              std::string, std::shared_ptr<cv::runtime::LatestInternalBuffer<proto::FurrowDetections>>>>()),
      config_callback_id_(-1), speed_mph_(speed_mph) {
  config_subscriber_ = carbon::config::get_global_config_subscriber();
  config_tree_ = config_subscriber_->get_config_node("deck", "");
  model_registry_.reset(new lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>());
  cv::cuda::GpuMat::setDefaultAllocator(&lib::common::OpenCVAllocator::get());
}

DeckCV::~DeckCV() {}

void DeckCV::start() {
  auto pr = std::make_shared<prometheus::Registry>();
  carbon::metrics::ExposerOwner::register_collectable(pr);
  for (auto camera_config : camera_cfgs_) {
    auto camera_id = camera_config->get_name();
    auto connector = cv::runtime::Output<lib::common::camera::CameraImage>();
    image_connectors_.emplace(camera_id, connector);
    if (camera_config->get_node("cv/furrows_enabled")->get_value<bool>()) {
      auto latest_furrow_detection = std::make_shared<cv::runtime::LatestInternalBuffer<proto::FurrowDetections>>();
      auto furrows_node = cv::runtime::CVNode(std::make_unique<FurrowsNode>(
          camera_id,
          connector.add_output(camera_config->get_node("cv/furrows_reduction_ratio")->get_value<int>(), true),
          latest_furrow_detection, 0, model_registry_, pr));
      latest_furrow_detections_->emplace(camera_id, latest_furrow_detection);
      furrows_nodes_.emplace(camera_id, furrows_node);
      nodes_.push_back(furrows_node);

      auto furrows_writer_node = cv::runtime::CVNode(
          std::make_unique<FurrowsWriterNode>(camera_id, camera_config, config_tree_,
                                              furrows_node.as<FurrowsNode>().get_output().add_output(), speed_mph_));
      nodes_.push_back(furrows_writer_node);
    }
    nodes_.push_back(cv::runtime::CVNode(std::make_unique<HDRBrightnessAdjuster>(
        camera_id, camera_config,
        connector.add_output(camera_config->get_node("cv/brightness_adjuster_reduction_ratio")->get_value<int>(),
                             true))));
  }

  deck_cv_service_ = std::make_unique<DeckCVServiceImpl>(latest_furrow_detections_, nodes_);
  grpc_server_ = start_grpc_server(deck_cv_service_.get(), fmt::format("0.0.0.0:{}", kGRPCListenPort));

  auto callback = [&]() {
    if (model_reload_future_.valid()) {
      auto result = model_reload_future_.wait_for(std::chrono::seconds(0));
      if (result == std::future_status::timeout) {
        return;
      } else {
        model_reload_future_.get();
      }
    }
    model_reload_future_ = std::async(std::launch::async, [&]() { update_model_from_config(); });
  };
  callback();
  config_callback_id_ = config_tree_->get_node("furrows_model_path")->register_callback(callback);
}
void DeckCV::stop() {
  if (config_callback_id_ != -1) {
    config_tree_->get_node("furrows_model_path")->unregister_callback(config_callback_id_);
    config_callback_id_ = -1;
  }
  for (auto &connector : image_connectors_) {
    connector.second.close();
  }
  image_connectors_.clear();
  latest_furrow_detections_->clear();
  furrows_nodes_.clear();
  grpc_server_->Shutdown();
  deck_cv_service_.reset();
  grpc_server_.reset();
}
void DeckCV::push_image(lib::common::camera::CameraImage image) {
  if (image_connectors_.count(image.camera_id) == 0) {
    return;
  }
  image.image = image.image.permute({2, 0, 1});
  image_connectors_.at(image.camera_id).push(image);
}

void DeckCV::update_model_from_config() {
  auto furrows_model_path = config_tree_->get_node("furrows_model_path")->get_value<std::string>();
  if (furrows_model_path.empty() || !std::filesystem::exists(furrows_model_path)) {
    spdlog::warn("Unable to load model: {}.", furrows_model_path);
    return;
  }

  std::string cached_path;
  if (model_registry_->contains(lib::common::model::ModelUseCase::kDriving)) {
    cached_path = model_registry_->get_path_for_use_case(lib::common::model::ModelUseCase::kDriving);
  }

  if (cached_path == furrows_model_path) {
    return;
  }

  for (auto &furrows_node : furrows_nodes_) {
    furrows_node.second.as<FurrowsNode>().clear_model();
  }

  model_registry_->set_model_for_use_case(lib::common::model::ModelUseCase::kDriving, furrows_model_path);
  model_registry_->clear_path_from_cache(cached_path);

  try {
    model_registry_->get_internal_metadata_by_use_case(lib::common::model::ModelUseCase::kDriving);

    for (auto &furrows_node : furrows_nodes_) {
      furrows_node.second.as<FurrowsNode>().reload_model();
    }
  } catch (const maka_error &err) {
    for (auto &furrows_node : furrows_nodes_) {
      furrows_node.second.as<FurrowsNode>().clear_model();
    }
    model_registry_->clear_model_for_use_case(lib::common::model::ModelUseCase::kDriving);
    spdlog::warn("Unable to load Furrows model: {}", err.what());
  }
}

} // namespace cv::deck

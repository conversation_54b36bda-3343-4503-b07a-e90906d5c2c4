#include "grpc_service.h"

#include <fmt/format.h>

namespace cv::deck {

DeckCVServiceImpl::DeckCVServiceImpl(
    std::shared_ptr<
        std::unordered_map<std::string, std::shared_ptr<cv::runtime::LatestInternalBuffer<proto::FurrowDetections>>>>
        latest_furrow_detections,
    std::vector<runtime::CVNode> nodes)
    : latest_furrow_detections_(latest_furrow_detections), nodes_(nodes) {}

grpc::Status DeckCVServiceImpl::GetNextFurrowDetections(grpc::ServerContext *,
                                                        const proto::GetNextFurrowDetectionsRequest *request,
                                                        proto::FurrowDetections *furrow_detections) {
  if (latest_furrow_detections_->count(request->camera_id()) == 0) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND, fmt::format("Camera ID {} not found", request->camera_id()));
  }

  auto latest_detection =
      latest_furrow_detections_->at(request->camera_id())->get_next(request->timestamp_ms(), request->timeout_ms());
  if (!latest_detection.has_value()) {
    return grpc::Status(grpc::StatusCode::NOT_FOUND,
                        fmt::format("Timed out waiting for next furrows output {}", request->camera_id()));
  }
  *furrow_detections = latest_detection.value();
  return grpc::Status::OK;
}

grpc::Status DeckCVServiceImpl::GetNodeTiming(grpc::ServerContext *, const proto::Empty *,
                                              proto::NodeTimingResponse *response) {
  try {
    for (auto &node : nodes_) {
      if (node.get_name() == "noop") {
        continue;
      }
      proto::NodeTiming *node_timing = response->add_node_timing();
      node_timing->set_name(node.get_name());
      node_timing->set_fps_mean(node.get_mean_fps());
      node_timing->set_fps_99pct(node.get_99pct_fps());
      node_timing->set_latency_ms_mean(node.get_mean_latency_ms());
      node_timing->set_latency_ms_99pct(node.get_99pct_latency_ms());
      node_timing->set_state(cv_node_state_to_string(node.get_state()));
      auto state_timings = node.get_state_timings();
      for (auto [state, time_ms] : state_timings) {
        (*node_timing->mutable_state_timings())[cv_node_state_to_string(state)] = (float)time_ms.count();
      }
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }

  return grpc::Status::OK;
}

std::unique_ptr<grpc::Server> start_grpc_server(DeckCVServiceImpl *service_impl, const std::string &server_address) {
  grpc::ServerBuilder builder;
  builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
  builder.RegisterService(service_impl);
  return builder.BuildAndStart();
}

} // namespace cv::deck

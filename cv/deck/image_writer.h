#include <torch/torch.h>

#include "lib/common/camera/cpp/camera_image.h"

#include <filesystem>
#include <nlohmann/json.hpp>

namespace cv::deck {
class ImageWriter {
public:
  ImageWriter(std::filesystem::path capture_path);
  void write(lib::common::camera::CameraImage image, std::string artifact_type, std::string artifact_subtype,
             nlohmann::json extra_metadata);

private:
  std::string robot_name_;
  std::string version_tag_;
  std::filesystem::path capture_path_;
};
} // namespace cv::deck

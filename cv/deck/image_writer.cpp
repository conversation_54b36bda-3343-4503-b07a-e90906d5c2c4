#include "image_writer.h"

#include "lib/common/cpp/md5.h"
#include "lib/common/cpp/opencv_tensor_util.h"
#include "lib/common/cpp/time.h"

#include <fstream>
#include <opencv2/opencv.hpp>

namespace cv::deck {
ImageWriter::ImageWriter(std::filesystem::path capture_path) : capture_path_(capture_path) {
  char *robot_name_ptr = std::getenv("MAKA_ROBOT_NAME");
  if (robot_name_ptr != NULL) {
    robot_name_ = robot_name_ptr;
  }

  char *version_tag_ptr = std::getenv("CARBON_VERSION_TAG");
  if (version_tag_ptr != NULL) {
    version_tag_ = version_tag_ptr;
  }
}

void ImageWriter::write(lib::common::camera::CameraImage image, std::string artifact_type, std::string artifact_subtype,
                        nlohmann::json extra_metadata) {
  auto filepath_no_ext =
      capture_path_ / date_timestamp() /
      fmt::format("{}_{}_{}_{}", artifact_type, robot_name_, image.camera_id, iso8601_timestamp(true, true));
  std::filesystem::create_directories(filepath_no_ext.parent_path());
  auto image_opencv = lib::common::wrap_to_opencv(image.image.permute({1, 2, 0}).cpu().contiguous());
  cv::cvtColor(image_opencv, image_opencv, cv::COLOR_RGB2BGR);
  cv::imwrite(filepath_no_ext.string() + ".png", image_opencv);

  nlohmann::json json = {
      {"geo",
       {{"lla",
         {{"lat", image.geo_lla_data.get_lat()},
          {"lng", image.geo_lla_data.get_lng()},
          {"alt", image.geo_lla_data.get_alt()},
          {"timestamp_ms", image.geo_lla_data.get_timestamp_ms()}}}}},
      {"artifact_type", artifact_type},
      {"artifact_subtype", artifact_subtype},
      {"robot_id", robot_name_},
      {"software_version", version_tag_},
      {"cam_id", image.camera_id},
      {"timestamp_ms", image.timestamp_ms},
      {"height", image.get_height()},
      {"width", image.get_width()},
      {"files", nlohmann::json::array({{{"name", filepath_no_ext.filename().string() + ".png"},
                                        {"md5", lib::common::md5sum(filepath_no_ext.string() + ".png")}}})}};
  json.merge_patch(extra_metadata);

  std::ofstream of(filepath_no_ext.string() + ".metadata.json");
  of << json << std::endl;
}
} // namespace cv::deck

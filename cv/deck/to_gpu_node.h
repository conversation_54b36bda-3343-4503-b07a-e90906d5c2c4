#pragma once

#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera_image.h"

#include <torch/torch.h>

namespace cv::deck {
class ToGPUNode : public cv::runtime::CVNodeImpl {
public:
  ToGPUNode(std::string camera_id, cv::runtime::Input<lib::common::camera::CameraImage> input, int gpu_id);
  int64_t tick() override;
  virtual void close_connectors() override;
  cv::runtime::Output<lib::common::camera::CameraImage> &get_output();

private:
  cv::runtime::Input<lib::common::camera::CameraImage> input_;
  cv::runtime::Output<lib::common::camera::CameraImage> output_;
  int gpu_id_;
};
} // namespace cv::deck

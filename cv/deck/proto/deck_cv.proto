syntax = "proto3";

package cv.deck.proto;

message FurrowDetection {
  float start_x = 1;
  float start_y = 2;
  float end_x = 3;
  float end_y = 4;
  string category = 5;
}
  
message FurrowDetections {
  repeated FurrowDetection furrows = 1;
  int64 timestamp_ms = 2;
}

message GetNextFurrowDetectionsRequest {
  string camera_id = 1;
  int64 timestamp_ms = 2;
  int64 timeout_ms = 3;
}

message NodeTiming {
  string name = 1;

  float fps_mean = 2;
  float fps_99pct = 3;

  float latency_ms_mean = 4;
  float latency_ms_99pct = 5;

  string state = 6;
  map<string, float> state_timings = 7;
}

message NodeTimingResponse {
  repeated NodeTiming node_timing = 1;
}

message Empty {}

service DeckCVService {
    rpc GetNextFurrowDetections(GetNextFurrowDetectionsRequest) returns (FurrowDetections) {}
    rpc GetNodeTiming(Empty) returns (NodeTimingResponse) {}
}
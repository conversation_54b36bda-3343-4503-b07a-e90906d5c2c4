#pragma once

#include "config/client/cpp/config_subscriber.hpp"
#include "config/tree/cpp/config_tree.hpp"
#include "cv/runtime/cpp/node.h"
#include "deck/cpp/grpc_client.h"
#include "lib/common/camera/cpp/camera_image.h"

#include <torch/torch.h>

namespace cv::deck {
class HDRBrightnessAdjuster : public cv::runtime::CVNodeImpl {
public:
  HDRBrightnessAdjuster(std::string camera_id, std::shared_ptr<carbon::config::ConfigTree> tree,
                        cv::runtime::Input<lib::common::camera::CameraImage> input);
  int64_t tick() override;
  virtual void close_connectors() override;

private:
  std::shared_ptr<carbon::config::ConfigTree> tree_;
  cv::runtime::Input<lib::common::camera::CameraImage> input_;
  std::string camera_id_;
  std::shared_ptr<carbon::config::ConfigSubscriber> config_subscriber_;
  carbon::deck::DeckClient deck_client_;
  std::chrono::system_clock::time_point last_sync_;
  float current_hdr_brightness_;
};
} // namespace cv::deck

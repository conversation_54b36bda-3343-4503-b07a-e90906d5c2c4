#include "furrows_writer_node.h"

namespace cv::deck {

const std::string FurrowsWriterNode::IMG_CAPTURE_PATH = "/data/uploader/uploads";

FurrowsWriterNode::FurrowsWriterNode(
    std::string camera_id, std::shared_ptr<carbon::config::ConfigTree> camera_tree,
    std::shared_ptr<carbon::config::ConfigTree> deck_tree,
    cv::runtime::Input<std::pair<lib::common::camera::CameraImage, proto::FurrowDetections>> input,
    std::shared_ptr<carbon::atomic_data_share::AtomicDataWrapper<float>> speed_mph)
    : CVNodeImpl(fmt::format("/furrows_writer_node/{}", camera_id)), input_(input), image_writer_(IMG_CAPTURE_PATH),
      persistence_rate_(camera_tree->get_node("cv/persistence_rate"), 0.0f),
      min_capture_speed_(deck_tree->get_node("min_capture_speed"), 0.0f), speed_mph_(speed_mph) {}

void FurrowsWriterNode::close_connectors() { input_.close(); }

int64_t FurrowsWriterNode::tick() {
  auto [camera_image, detections] = input_.pop();
  auto random_float = static_cast<float>(std::rand()) / static_cast<float>(RAND_MAX);
  auto persistence_rate = persistence_rate_.get_value();
  auto min_capture_speed = min_capture_speed_.get_value();
  float speed_mph = speed_mph_->get();
  if (random_float > persistence_rate || speed_mph < min_capture_speed) {
    return camera_image.timestamp_ms;
  }

  std::string sub_type = "rgbd";
  if (camera_image.depth.numel() == 0) {
    sub_type = "rgb";
  } else if (camera_image.image.numel() == 0) {
    sub_type = "depth";
  }

  nlohmann::json detections_json = nlohmann::json::array();
  for (auto &detection : detections.furrows()) {
    detections_json.push_back(nlohmann::json::object({{"start_x", detection.start_x()},
                                                      {"start_y", detection.start_y()},
                                                      {"end_x", detection.end_x()},
                                                      {"end_y", detection.end_y()},
                                                      {"category", detection.category()}}));
  }

  image_writer_.write(camera_image, "furrows", sub_type, nlohmann::json({{"furrow_detections", detections_json}}));

  return camera_image.timestamp_ms;
}

} // namespace cv::deck

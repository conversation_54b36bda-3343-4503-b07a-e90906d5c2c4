#pragma once

#include "config/client/cpp/config_subscriber.hpp"
#include "config/tree/cpp/config_atomic_accessor.hpp"
#include "cv/deck/image_writer.h"
#include "cv/deck/proto/deck_cv.pb.h"
#include "cv/runtime/cpp/latest_internal_buffer.h"
#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera_image.h"
#include "lib/common/model/cpp/model_registry.h"
#include <atomic_data_share/cpp/atomic_data_share.hpp>

namespace cv::deck {
class FurrowsWriterNode : public cv::runtime::CVNodeImpl {
public:
  FurrowsWriterNode(std::string camera_id, std::shared_ptr<carbon::config::ConfigTree> camera_tree,
                    std::shared_ptr<carbon::config::ConfigTree> deck_tree,
                    cv::runtime::Input<std::pair<lib::common::camera::CameraImage, proto::FurrowDetections>> input,
                    std::shared_ptr<carbon::atomic_data_share::AtomicDataWrapper<float>> speed_mph);
  int64_t tick() override;
  virtual void close_connectors() override;

private:
  static const std::string IMG_CAPTURE_PATH;
  cv::runtime::Input<std::pair<lib::common::camera::CameraImage, proto::FurrowDetections>> input_;
  ImageWriter image_writer_;
  std::string camera_id_;
  carbon::config::ConfigAtomicAccessor<float> persistence_rate_;
  carbon::config::ConfigAtomicAccessor<float> min_capture_speed_;
  std::shared_ptr<carbon::atomic_data_share::AtomicDataWrapper<float>> speed_mph_;
};
} // namespace cv::deck

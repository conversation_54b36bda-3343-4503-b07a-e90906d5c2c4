#include "hdr_brightness_adjuster.h"
#include <cmath>

namespace cv::deck {
HDRBrightnessAdjuster::HDRBrightnessAdjuster(std::string camera_id, std::shared_ptr<carbon::config::ConfigTree> tree,
                                             cv::runtime::Input<lib::common::camera::CameraImage> input)
    : CVNodeImpl(fmt::format("/hdr_brightness_adjuster/{}", camera_id)), tree_(tree), input_(input),
      camera_id_(camera_id), config_subscriber_(carbon::config::get_global_config_subscriber()),
      last_sync_(std::chrono::system_clock::now()), current_hdr_brightness_(-1.0f) {}

void HDRBrightnessAdjuster::close_connectors() { input_.close(); }

int64_t HDRBrightnessAdjuster::tick() {
  auto camera_image = input_.pop();

  if (!tree_->get_node("hdr/auto_brightness/enabled")->get_value<bool>()) {
    return camera_image.timestamp_ms;
  }

  float min_hdr_brightness = tree_->get_node("hdr/auto_brightness/min_hdr_brightness")->get_value<float>();
  float max_hdr_brightness = tree_->get_node("hdr/auto_brightness/max_hdr_brightness")->get_value<float>();
  float hdr_brightness_delta = tree_->get_node("hdr/auto_brightness/hdr_brightness_delta")->get_value<float>();
  float max_brightness = tree_->get_node("hdr/auto_brightness/max_brightness")->get_value<float>();
  float min_brightness = tree_->get_node("hdr/auto_brightness/min_brightness")->get_value<float>();
  float quantile = tree_->get_node("hdr/auto_brightness/quantile")->get_value<float>();

  auto image_tensor_downsampled_100x =
      camera_image.image.index({"...", torch::indexing::Slice(0, torch::indexing::None, 10),
                                torch::indexing::Slice(0, torch::indexing::None, 10)});
  float brightness =
      std::get<0>(image_tensor_downsampled_100x.max(0)).to(torch::kFloat32).quantile(quantile).item<float>() / 255.0f;

  if (brightness > max_brightness || brightness < min_brightness) {
    if (current_hdr_brightness_ < 0.0f) {
      current_hdr_brightness_ = (float)deck_client_.get_double_config(camera_id_, "ExposureTime");
    }
    const float pos_or_neg_hdr_brightness_delta =
        brightness > max_brightness ? -hdr_brightness_delta : hdr_brightness_delta;
    float new_hdr_brightness = current_hdr_brightness_ + pos_or_neg_hdr_brightness_delta;
    new_hdr_brightness = std::min(std::max(new_hdr_brightness, min_hdr_brightness), max_hdr_brightness);

    if (std::abs(new_hdr_brightness - current_hdr_brightness_) > 0.5f) {
      // Values are pretty course (5.0-10.0), so a diff of less than that is not worth adjusting
      deck_client_.set_configs(camera_id_, {{"ExposureTime", new_hdr_brightness}});
      current_hdr_brightness_ = new_hdr_brightness;
    }
  }

  return camera_image.timestamp_ms;
}

} // namespace cv::deck

#pragma once

#include <c10/cuda/CUDAStream.h>

#include "cv/deck/proto/deck_cv.pb.h"
#include "cv/furrows/furrows_model.h"
#include "cv/runtime/cpp/latest_internal_buffer.h"
#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera_image.h"
#include "lib/common/model/cpp/model_registry.h"
#include "metrics/cpp/prometheus/time_gauge_guard.hpp"

namespace prometheus {
class Registry;
}
namespace cv::deck {
class FurrowsNode : public cv::runtime::CVNodeImpl {
public:
  FurrowsNode(std::string camera_id, cv::runtime::Input<lib::common::camera::CameraImage> input,
              std::shared_ptr<cv::runtime::LatestInternalBuffer<proto::FurrowDetections>> latest_buffer, int gpu_id,
              std::shared_ptr<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>> model_registry,
              std::shared_ptr<prometheus::Registry> pr);
  cv::runtime::Output<std::pair<lib::common::camera::CameraImage, proto::FurrowDetections>> get_output();

  int64_t tick() override;
  virtual void close_connectors() override;
  void clear_model();
  void reload_model();

private:
  cv::runtime::Input<lib::common::camera::CameraImage> input_;
  cv::runtime::Output<std::pair<lib::common::camera::CameraImage, proto::FurrowDetections>> output_;
  std::shared_ptr<cv::runtime::LatestInternalBuffer<proto::FurrowDetections>> latest_buffer_;
  std::unique_ptr<furrows::FurrowsModel> model_;
  std::shared_mutex infer_mutex_;
  int gpu_id_;
  std::shared_ptr<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>> model_registry_;
  c10::cuda::CUDAStream cuda_stream_;
  carbon::metrics::TimeGauge tg_;
};
} // namespace cv::deck

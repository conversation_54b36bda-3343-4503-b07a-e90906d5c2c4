#pragma once

#include "generated/cv/deck/proto/deck_cv.grpc.pb.h"
#include "generated/cv/deck/proto/deck_cv.pb.h"

#include <chrono>
#include <grpcpp/grpcpp.h>
#include <memory>
#include <mutex>
#include <string>

namespace cv::deck {

class DeckCvClient {
protected:
  std::string addr_;
  std::shared_ptr<grpc::Channel> channel_;
  std::shared_ptr<proto::DeckCVService::Stub> stub_;
  std::mutex channel_setup_mutex_;

public:
  static const uint32_t DEFAULT_PORT = 61013;
  DeckCvClient(const std::string &host = "127.0.0.1", uint32_t port = 61013);

  void reset();
  std::string get_addr() const;
  std::unique_ptr<proto::FurrowDetections> get_next_furrow_detection(int64_t timestamp_ms, const std::string &camera_id,
                                                                     std::chrono::milliseconds timeout);

private:
  grpc::Status exec_grpc(std::function<grpc::Status()> func);
  void setup_grpc(bool reconnect_if_down = true);
  std::shared_ptr<proto::DeckCVService::Stub> get_grpc_stub(bool reconnect_if_down = true);
  void reset_stub();
};

} // namespace cv::deck

#include "cv/deck/client/cpp/grpc_client.h"

#include <functional>
#include <set>

#include <fmt/format.h>
#include <spdlog/spdlog.h>

namespace cv::deck {

DeckCvClient::DeckCvClient(const std::string &host, uint32_t port)
    : addr_(fmt::format("{}:{}", host, port)), channel_(nullptr) {}

void DeckCvClient::setup_grpc(bool reconnect_if_down) {
  // Not Thread Safe
  if (this->channel_ != nullptr && this->channel_->GetState(true) != GRPC_CHANNEL_READY) {
    this->reset_stub();
  }
  if (this->channel_ == nullptr) {
    if (!reconnect_if_down) {
      return;
    }
    this->channel_ = grpc::CreateChannel(this->addr_, grpc::InsecureChannelCredentials());
  }
  if (this->stub_ == nullptr) {
    this->stub_ = std::make_shared<proto::DeckCVService::Stub>(this->channel_);
  }
}

std::shared_ptr<proto::DeckCVService::Stub> DeckCvClient::get_grpc_stub(bool reconnect_if_down) {
  std::lock_guard<std::mutex> lock(this->channel_setup_mutex_);
  this->setup_grpc(reconnect_if_down);
  return this->stub_;
}

void DeckCvClient::reset() {
  std::lock_guard<std::mutex> lock(this->channel_setup_mutex_);
  this->reset_stub();
}

void DeckCvClient::reset_stub() {
  // Not Thread Safe
  this->stub_ = nullptr;
  this->channel_ = nullptr;
}

grpc::Status DeckCvClient::exec_grpc(std::function<grpc::Status()> func) {
  try {
    return func();
  } catch (const std::exception &ex) { // TODO Better Exception Handling?
    this->reset_stub();
    throw;
  }
}

std::string DeckCvClient::get_addr() const { return addr_; }

std::unique_ptr<proto::FurrowDetections> DeckCvClient::get_next_furrow_detection(int64_t timestamp_ms,
                                                                                 const std::string &camera_id,
                                                                                 std::chrono::milliseconds timeout) {
  grpc::ClientContext context;
  proto::GetNextFurrowDetectionsRequest req;
  req.set_timestamp_ms(timestamp_ms);
  req.set_camera_id(camera_id);
  req.set_timeout_ms(timeout.count());
  std::unique_ptr<proto::FurrowDetections> resp = std::make_unique<proto::FurrowDetections>();
  auto stub = this->get_grpc_stub();
  grpc::Status status =
      exec_grpc(std::bind(&proto::DeckCVService::Stub::GetNextFurrowDetections, stub, &context, req, resp.get()));
  if (status.error_code() != grpc::StatusCode::OK) {
    return nullptr;
  }
  return resp;
}
} // namespace cv::deck
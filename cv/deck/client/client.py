import asyncio
from argparse import Argument<PERSON>arser
from typing import Optional

import grpc
from google.protobuf.json_format import MessageT<PERSON><PERSON>son

from generated.cv.deck.proto.deck_cv_pb2 import (
    Empty,
    FurrowDetections,
    GetNextFurrowDetectionsRequest,
    NodeTimingResponse,
)
from generated.cv.deck.proto.deck_cv_pb2_grpc import DeckCVServiceStub
from lib.common.tasks.manager import get_event_loop_by_name

DEFAULT_PORT = 61013


class DeckCVClient:
    def __init__(self, hostname: str = "localhost", port: int = DEFAULT_PORT) -> None:
        self._addr = f"{hostname}:{port}"
        self._channel = None
        self._stub: Optional[DeckCVServiceStub] = None
        self._event_loop = get_event_loop_by_name()

    def _get_stub(self) -> DeckCVServiceStub:
        if self._stub is None:
            if self._channel is None:
                self._channel = grpc.aio.insecure_channel(self._addr)
            self._stub = DeckCVServiceStub(self._channel)
        return self._stub

    def reset(self) -> None:
        self._stub = None
        self._channel = None

    async def get_next_furrow_detection(
        self, timestamp_ms: int, camera_id: str, timeout_ms: int = 0
    ) -> FurrowDetections:
        req = GetNextFurrowDetectionsRequest(timestamp_ms=timestamp_ms, camera_id=camera_id, timeout_ms=timeout_ms)
        resp: FurrowDetections = await self._get_stub().GetNextFurrowDetections(req)
        return resp

    async def get_node_timing(self) -> NodeTimingResponse:
        resp: NodeTimingResponse = await self._get_stub().GetNodeTiming(Empty())
        return resp


def interactive(hostname: str, port: int) -> None:
    from lib.common.asyncio.repl import start

    client = DeckCVClient(hostname, port)
    imports = {
        "client": client,
    }
    start(imports)


async def tester(hostname: str, port: int) -> None:
    deck_cv_client = DeckCVClient()
    furrows = await deck_cv_client.get_next_furrow_detection(0, "zed")
    print(MessageToJson(furrows))

    node_timing = await deck_cv_client.get_node_timing()
    print(MessageToJson(node_timing))


def main() -> None:
    parser = ArgumentParser("DeckCV GRPC client")
    parser.add_argument("--hostname", type=str, default="localhost", help="DeckCV grpc service host")
    parser.add_argument("-p", "--port", type=int, default=DEFAULT_PORT, help="DeckCV grpc service port")
    parser.add_argument("-t", "--tester", action="store_true", default=False, help="run pre-canned test code")
    args = parser.parse_args()
    if args.tester:
        asyncio.run_coroutine_threadsafe(tester(args.hostname, args.port), get_event_loop_by_name()).result()
    else:
        interactive(args.hostname, args.port)


if __name__ == "__main__":
    main()

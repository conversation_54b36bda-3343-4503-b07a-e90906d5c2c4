#pragma once

#include <grpcpp/grpcpp.h>
#include <memory>

#include "cv/deck/proto/deck_cv.grpc.pb.h"
#include "cv/runtime/cpp/latest_internal_buffer.h"
#include "cv/runtime/cpp/node.h"

namespace cv::deck {
class DeckCVServiceImpl final : public cv::deck::proto::DeckCVService::Service {
public:
  DeckCVServiceImpl(
      std::shared_ptr<
          std::unordered_map<std::string, std::shared_ptr<cv::runtime::LatestInternalBuffer<proto::FurrowDetections>>>>
          latest_furrow_detections,
      std::vector<runtime::CVNode> nodes);
  grpc::Status GetNextFurrowDetections(grpc::ServerContext *, const proto::GetNextFurrowDetectionsRequest *request,
                                       proto::FurrowDetections *);
  grpc::Status GetNodeTiming(grpc::ServerContext *, const proto::Empty *, proto::NodeTimingResponse *response);

private:
  std::shared_ptr<
      std::unordered_map<std::string, std::shared_ptr<cv::runtime::LatestInternalBuffer<proto::FurrowDetections>>>>
      latest_furrow_detections_;
  std::vector<runtime::CVNode> nodes_;
};

std::unique_ptr<grpc::Server> start_grpc_server(DeckCVServiceImpl *service_impl, const std::string &server_address);
} // namespace cv::deck

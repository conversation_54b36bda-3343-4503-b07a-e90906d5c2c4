#pragma once

#include <future>
#include <grpcpp/grpcpp.h>
#include <torch/torch.h>

#include "config/client/cpp/config_subscriber.hpp"
#include "config/tree/cpp/config_tree.hpp"
#include "cv/deck/grpc_service.h"
#include "cv/furrows/furrows_model.h"
#include "cv/runtime/cpp/connector.h"
#include "cv/runtime/cpp/latest_internal_buffer.h"
#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera_image.h"
#include "lib/common/model/cpp/model_registry.h"
#include <atomic_data_share/cpp/atomic_data_share.hpp>

namespace cv::deck {

class DeckCV {
public:
  DeckCV(std::shared_ptr<carbon::atomic_data_share::AtomicDataWrapper<float>> speed_mph);
  ~DeckCV();

  inline void add_camera(std::shared_ptr<carbon::config::ConfigTree> tree) { camera_cfgs_.emplace_back(tree); }
  void start();
  void stop();

  void push_image(lib::common::camera::CameraImage image);

private:
  void update_model_from_config();

  std::unordered_map<std::string, runtime::Output<lib::common::camera::CameraImage>> image_connectors_;
  std::shared_ptr<
      std::unordered_map<std::string, std::shared_ptr<runtime::LatestInternalBuffer<proto::FurrowDetections>>>>
      latest_furrow_detections_;
  std::vector<std::shared_ptr<carbon::config::ConfigTree>> camera_cfgs_;
  std::unordered_map<std::string, runtime::CVNode> furrows_nodes_;
  std::vector<runtime::CVNode> nodes_;
  std::unique_ptr<grpc::Server> grpc_server_;
  std::unique_ptr<DeckCVServiceImpl> deck_cv_service_;
  std::shared_ptr<carbon::config::ConfigSubscriber> config_subscriber_;
  std::shared_ptr<carbon::config::ConfigTree> config_tree_;
  std::shared_ptr<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>> model_registry_;
  std::future<void> model_reload_future_;
  int config_callback_id_;
  std::shared_ptr<carbon::atomic_data_share::AtomicDataWrapper<float>> speed_mph_;

  static const int kGRPCListenPort = 61013;
};

} // namespace cv::deck
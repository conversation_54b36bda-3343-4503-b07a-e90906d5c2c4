#include "cv/deck/deck_cv.h"

#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <torch/extension.h>

namespace py = pybind11;

namespace cv::deck {

PYBIND11_MODULE(deck_cv_python, m) {
  py::module::import("lib.common.camera.cpp.camera_image_python");

  py::class_<DeckCV, std::shared_ptr<DeckCV>>(m, "DeckCV")
      .def(py::init<std::shared_ptr<carbon::atomic_data_share::AtomicDataWrapper<float>>>(), py::arg("speed_mph"),
           py::call_guard<py::gil_scoped_release>())
      .def("start", &DeckCV::start, py::call_guard<py::gil_scoped_release>())
      .def("stop", &DeckCV::start, py::call_guard<py::gil_scoped_release>());
}

} // namespace cv::deck
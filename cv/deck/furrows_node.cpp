#include "furrows_node.h"

#include <c10/cuda/CUDAGuard.h>
#include <prometheus/gauge.h>
#include <prometheus/registry.h>

prometheus::Family<prometheus::Gauge> &get_family(std::shared_ptr<prometheus::Registry> r) {
  static prometheus::Family<prometheus::Gauge> &family =
      prometheus::BuildGauge()
          .Name("furrow_node_timer")
          .Help("The time it takes to run 1 tick of the furrow prediction node")
          .Register(*r);
  return family;
}
namespace cv::deck {
FurrowsNode::FurrowsNode(
    std::string camera_id, cv::runtime::Input<lib::common::camera::CameraImage> input,
    std::shared_ptr<cv::runtime::LatestInternalBuffer<proto::FurrowDetections>> latest_buffer, int gpu_id,
    std::shared_ptr<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>> model_registry,
    std::shared_ptr<prometheus::Registry> pr)
    : CVNodeImpl(fmt::format("/furrows_node/{}", camera_id)), input_(input), latest_buffer_(latest_buffer),
      gpu_id_(gpu_id), model_registry_(model_registry),
      cuda_stream_(c10::cuda::getStreamFromPool(true, c10::DeviceIndex(gpu_id))),
      tg_(get_family(pr).Add({{"camera", camera_id}}), 100) {}

cv::runtime::Output<std::pair<lib::common::camera::CameraImage, proto::FurrowDetections>> FurrowsNode::get_output() {
  return output_;
}

void FurrowsNode::close_connectors() { input_.close(); }

int64_t FurrowsNode::tick() {
  auto tgg = tg_.time();
  auto camera_image = input_.pop();
  auto image = camera_image.image;

  proto::FurrowDetections detection;
  detection.set_timestamp_ms(camera_image.timestamp_ms);

  std::shared_lock<std::shared_mutex> lock(infer_mutex_);
  if (!model_) {
    output_.push(std::make_pair(camera_image, detection));
    tgg.cancel();
    return camera_image.timestamp_ms;
  }

  c10::cuda::CUDAStreamGuard stream_guard(cuda_stream_);

  auto metadata = model_->get_internal_metadata();
  if (!metadata.supports_depth()) {
    image = image.slice(0, 0, 3); // Remove depth if it exists
  }

  auto furrows_output = model_->infer(image);

  for (const auto &furrow : furrows_output.furrows) {
    auto furrow_detection = detection.add_furrows();
    furrow_detection->set_start_x((float)furrow.start_x / (float)furrows_output.image_width);
    furrow_detection->set_start_y((float)furrow.start_y / (float)furrows_output.image_height);
    furrow_detection->set_end_x((float)furrow.end_x / (float)furrows_output.image_width);
    furrow_detection->set_end_y((float)furrow.end_y / (float)furrows_output.image_height);
    furrow_detection->set_category(furrow.category);
  }
  latest_buffer_->update(detection, detection.timestamp_ms());

  output_.push(std::make_pair(camera_image, detection));
  return camera_image.timestamp_ms;
}

void FurrowsNode::clear_model() {
  std::unique_lock<std::shared_mutex> lock(infer_mutex_);
  model_.reset();
}

void FurrowsNode::reload_model() {
  std::unique_lock<std::shared_mutex> lock(infer_mutex_);
  // Free old model before loading new model
  model_.reset();
  if (!model_registry_->contains(lib::common::model::ModelUseCase::kDriving)) {
    return;
  }

  spdlog::info("Loading Furrows model {} to GPU {}",
               model_registry_->get_path_for_use_case(lib::common::model::ModelUseCase::kDriving), gpu_id_);
  try {
    auto trt_model = model_registry_->get(lib::common::model::ModelUseCase::kDriving, gpu_id_);
    model_.reset(new furrows::FurrowsModel(trt_model));

    spdlog::info("Loaded Furrows model {}", trt_model.get_model_path());
  } catch (const std::exception &e) {
    spdlog::warn("Unable to load Furrows model: {}", e.what());
    return;
  }
}
} // namespace cv::deck

CompileProto(proto/deck_cv.proto GENERATED_PATH GOPKG proto/deck_cv LANGS grpc_python python mypy grpc cpp)

add_library(deck_cv_proto SHARED ${GENERATED_PATH}/deck_cv.grpc.pb.cc ${GENERATED_PATH}/deck_cv.pb.cc)
target_compile_options(deck_cv_proto PRIVATE "-w")
target_link_libraries(deck_cv_proto grpc++ protobuf)
add_dependencies(deck_cv_proto generate_cv_deck_proto_deck_cv)

add_library(deck_cv deck_cv.cpp grpc_service.cpp furrows_node.cpp to_gpu_node.cpp hdr_brightness_adjuster.cpp furrows_writer_node.cpp image_writer.cpp)
target_link_libraries(deck_cv PUBLIC deck_cv_proto exceptions lib_common_model yaml-cpp config_client_lib model_io furrows deck_client simulator_proto opencv_core opencv_imgproc opencv_imgcodecs metrics_prometheus opencv_allocator)
add_dependencies(deck_cv camera_image_python geo_data_python)

pybind11_add_module(deck_cv_python SHARED deck_cv_python.cpp)
target_link_libraries(deck_cv_python PUBLIC deck_cv torch_python)
target_compile_options(deck_cv_python PRIVATE -fvisibility=default)
set_target_properties(deck_cv_python PROPERTIES OUTPUT_NAME deck_cv_python.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

add_subdirectory(client/cpp)

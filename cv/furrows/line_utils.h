#pragma once

#include <glm/glm.hpp>
#include <optional>
#include <torch/torch.h>

#include "lib/common/cpp/math.h"

namespace cv::furrows {
namespace F = torch::nn::functional;

struct HoughTransformOptions {
  const float kDefaultThreshold = 0.5f;
  const float kDefaultRhoStep = 1.0f;
  const float kDefaultThetaStep = lib::common::kPi / 360.0f;
  const int kDefaultKernelSize = 3;
  const int kDefaultDensity = 1;
  const float kDefaultEps = 3.0f;
  const int kDefaultMinSamples = 5;
  const float kDefaultThetaFlipThreshold = 1e-3f;
  const int kDefaultRenderLinePixels = 4000;

  float hough_threshold = kDefaultThreshold;
  std::optional<float> centroid_threshold;
  float theta_flip_threshold = kDefaultThetaFlipThreshold;
  float rho_step = kDefaultRhoStep;
  float theta_step = kDefaultThetaStep;
  int kernel_size = kDefaultKernelSize;
  int density = kDefaultDensity;
  int render_line_pixels = kDefaultRenderLinePixels;
  float eps = kDefaultEps;
  int min_samples = kDefaultMinSamples;
  int num_furrows = 1;
  std::optional<glm::ivec2> point;
  std::optional<glm::ivec2> origin;
  std::optional<float> elongated_component_threshold;
};

std::vector<glm::vec2> generate_rho_theta(torch::Tensor angles_2theta, torch::Tensor mask,
                                          HoughTransformOptions options);

torch::Tensor to_hough_tensor(torch::Tensor mask, torch::Tensor angles, float threshold, float rho_step,
                              float theta_step, int density, glm::ivec2 origin);

torch::Tensor compute_theta_tensor(torch::Tensor angles_2theta, float flip_threshold);

std::vector<glm::vec2> find_weighted_centroids_of_connected_components(
    torch::Tensor input, float threshold, std::optional<float> elongated_component_threshold, int number_of_furrows);

torch::Tensor blur(torch::Tensor image, int kernel_size);

std::vector<glm::vec2> get_weighted_centroids(torch::Tensor input, torch::Tensor labels, int min_label, int max_label,
                                              const std::set<int> &labels_to_avoid,
                                              std::optional<float> elongated_component_threshold,
                                              int number_of_furrows);

bool component_is_elongated(torch::Tensor coords, std::optional<float> threshold);

std::vector<glm::vec2> hough_to_rho_theta(const std::vector<glm::vec2> &weighted_centroids, int hough_space_height,
                                          float rho_step, float theta_step, int8_t device);

glm::vec2 get_nearest_line(const std::vector<glm::vec2> &rho_thetas, glm::vec2 point);

float get_point_to_line_distance(glm::vec2 point, glm::vec2 rho_theta);

} // namespace cv::furrows

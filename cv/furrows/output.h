#pragma once

#include <cstdint>
#include <glm/glm.hpp>
#include <torch/torch.h>

namespace cv::furrows {

struct FurrowDetection {
  int32_t start_x, start_y;
  int32_t end_x, end_y;
  std::string category;
};

constexpr int kFurrowMaxDetections = 10;
struct FurrowsOutput {
  int32_t image_width, image_height;
  std::vector<FurrowDetection> furrows;
};

struct FurrowsRawOutput {
public:
  FurrowsRawOutput(std::vector<torch::Tensor> output) {
    version = output[0][0].item().to<int>();
    mask = output[1];
    direction = output[2];
  }

  c10::DeviceIndex device() { return mask.device().index(); }

  int batch_size() {
    // We ensure that dummy mask has correct batch size
    return (int)mask.size(0);
  }

  int version;
  torch::Tensor mask;
  torch::Tensor direction;
};
} // namespace cv::furrows

add_library(furrows SHARED furrows_model.cpp line_utils_cuda.cu line_utils.cpp)
target_link_libraries(furrows PUBLIC lib_common_model opencv_imgproc exceptions torch)

pybind11_add_module(furrows_python SHARED furrows_python.cpp)
target_link_libraries(furrows_python PUBLIC furrows torch_python)
target_compile_options(furrows_python PRIVATE -fvisibility=default)
set_target_properties(furrows_python PROPERTIES OUTPUT_NAME furrows_python.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

#pragma once

#include <torch/torch.h>

#include "cv/furrows/output.h"
#include "lib/common/model/cpp/atomic_model.h"

namespace cv::furrows {

class FurrowsModel {
public:
  FurrowsModel(lib::common::model::AtomicModel model);

  FurrowsOutput infer(torch::Tensor image);

  const ModelMetadataProto &get_internal_metadata();
  const nlohmann::json &get_external_metadata();

private:
  torch::Tensor normalize(torch::Tensor image);
  FurrowsOutput raw_output_to_furrows(FurrowsRawOutput);

  lib::common::model::AtomicModel model_;
  torch::Tensor means_;
  torch::Tensor stds_;
  glm::ivec2 input_size_;
};

} // namespace cv::furrows

#include "line_utils.h"

#include <cmath>

#include "cv/furrows/line_utils_cuda.h"
#include "lib/common/cpp/opencv_tensor_util.h"

namespace cv::furrows {

torch::Tensor compute_theta_tensor(torch::Tensor angles_2theta, float flip_threshold) {
  angles_2theta = F::normalize(angles_2theta, F::NormalizeFuncOptions().dim(-1));

  bool unsqueeze_first = false;
  if (angles_2theta.ndimension() == 3) {
    angles_2theta = angles_2theta.unsqueeze(0).unsqueeze(0);
    unsqueeze_first = true;
  }

  torch::Tensor angles_theta =
      torch::zeros(angles_2theta.sizes(), torch::TensorOptions().device(angles_2theta.device()));

  torch::Tensor zero = torch::zeros({1}, torch::TensorOptions().device(angles_2theta.device()));
  torch::Tensor one = torch::ones({1}, torch::TensorOptions().device(angles_2theta.device()));

  for (int example_id = 0; example_id < angles_2theta.size(0); example_id++) {
    for (int class_id = 0; class_id < angles_2theta.size(1); class_id++) {
      torch::Tensor cos_2theta =
          angles_2theta.index({example_id, class_id, torch::indexing::Slice(), torch::indexing::Slice(), 0});
      torch::Tensor sin_2theta =
          angles_2theta.index({example_id, class_id, torch::indexing::Slice(), torch::indexing::Slice(), 1});

      // floating point arithmetic causes 1 - 1. below to nan. Because cos_2theta is -1..1, 1 + cos_2theta
      // must be at least 0, so we'll max with 0 here to prevent nan
      torch::Tensor cos_theta = torch::pow(torch::max(one + cos_2theta, zero) / 2, 0.5f);
      torch::Tensor sin_theta = torch::pow(torch::max(one - cos_2theta, zero) / 2, 0.5f);

      // There are two possible solutions for the half-angle formula. The following checks
      // whether the correct solution was selected by doubling the resulting angle and
      // comparing it to 2theta, plus/minus a margin of error. 1e-3 is selected as the margin
      // of error because we observed it works well with tensors of both float and half types.
      torch::Tensor cos_flip_range = (sin_2theta - 2 * sin_theta * cos_theta).abs() > flip_threshold;

      // Subtract by 2 * cos_theta * cos_flip_range in order to negate only elements that were above flip_threshold.
      cos_theta = cos_theta - 2 * cos_theta * cos_flip_range;
      angles_theta.index(
          {example_id, class_id, torch::indexing::Slice(), torch::indexing::Slice(), torch::indexing::Slice()}) =
          torch::stack({cos_theta, sin_theta}, -1).to(angles_2theta.device());
    }
  }

  if (unsqueeze_first) {
    angles_theta = angles_theta.squeeze(0).squeeze(0);
  }

  return angles_theta;
}

torch::Tensor to_hough_tensor(torch::Tensor mask, torch::Tensor angles, float threshold, float rho_step,
                              float theta_step, int density, glm::ivec2 origin) {
  torch::Tensor result =
      torch::zeros({static_cast<int>(2 * std::ceil(glm::length(glm::vec2(mask.size(1), mask.size(0))) / rho_step)),
                    static_cast<int>(std::ceil(lib::common::kPi / theta_step))},
                   torch::TensorOptions().device(mask.device()));

  torch::Tensor mask_nonzero = (mask > threshold).nonzero();
  angles = angles.to(mask.device());

  result = hough_transform(result, mask_nonzero, mask, angles, rho_step, theta_step, density, origin);

  return result;
}

bool component_is_elongated(torch::Tensor coords, std::optional<float> threshold) {
  if (!threshold) {
    return false;
  }

  if (coords.size(0) <= 1) {
    return true;
  }

  cv::PCA pca(lib::common::wrap_to_opencv(coords.unsqueeze(2).contiguous()), cv::Mat(), PCA::DATA_AS_ROW);
  torch::Tensor eigen_values = lib::common::to_tensor(pca.eigenvalues);
  const float total_variance = eigen_values.sum(0).item().to<float>();
  const float explained_variance_ratio = eigen_values.max().item().to<float>() / total_variance;
  return explained_variance_ratio >= *threshold;
}

std::vector<glm::vec2> get_weighted_centroids(torch::Tensor input, torch::Tensor labels, int min_label, int max_label,
                                              const std::set<int> &labels_to_avoid,
                                              std::optional<float> elongated_component_threshold,
                                              int number_of_furrows) {
  std::vector<std::pair<float, glm::vec2>> result;
  for (int label = min_label; label < max_label + 1; label++) {
    if (labels_to_avoid.count(label) != 0) {
      continue;
    }

    torch::Tensor coords = (labels == label).nonzero().to(torch::kF32);
    if (coords.size(0) == 0) {
      continue;
    }

    if (component_is_elongated(coords, elongated_component_threshold)) {
      continue;
    }

    coords = coords.to(torch::kInt64);

    torch::Tensor weights = input.index({coords.index({"...", 0}), coords.index({"...", 1})}).unsqueeze(1).cpu();
    torch::Tensor weighted_centroids = (weights * coords).sum(0) / weights.sum(0);

    result.push_back({weights.sum().item().to<float>(),
                      glm::vec2(weighted_centroids[1].item().to<float>(), weighted_centroids[0].item().to<float>())});
  }

  // Sort centroids so the highest weight comes first.
  std::sort(result.begin(), result.end(), [](std::pair<float, glm::vec2> first, std::pair<float, glm::vec2> second) {
    return first.first > second.first;
  });

  number_of_furrows = std::min(number_of_furrows, static_cast<int>(result.size()));

  std::vector<glm::vec2> output_centroids((size_t)number_of_furrows);
  std::transform(result.begin(), result.begin() + number_of_furrows, output_centroids.begin(),
                 [](std::pair<float, glm::vec2> item) { return item.second; });
  return output_centroids;
}

std::vector<glm::vec2> find_weighted_centroids_of_connected_components(
    torch::Tensor input, float threshold, std::optional<float> elongated_component_threshold, int number_of_furrows) {
  torch::Tensor mask = ((input > threshold).to(torch::kU8) * 255).unsqueeze(2).cpu();
  cv::Mat mask_cv = lib::common::wrap_to_opencv(mask.contiguous());

  cv::Mat labels;
  cv::Mat stats;
  cv::Mat centroids;
  const int num_labels = cv::connectedComponentsWithStats(mask_cv, labels, stats, centroids);

  const auto stats_area = stats.col(cv::ConnectedComponentsTypes::CC_STAT_AREA);
  const auto max_element = std::max_element(stats.begin<float>(), stats.end<float>());
  const int max_element_index = (int)(max_element - stats.begin<float>());

  return get_weighted_centroids(input, lib::common::to_tensor(labels).squeeze(2), 0, num_labels - 1,
                                {max_element_index}, elongated_component_threshold, number_of_furrows);
}

std::vector<glm::vec2> hough_to_rho_theta(const std::vector<glm::vec2> &weighted_centroids, int hough_space_height,
                                          float rho_step, float theta_step, int8_t device) {
  std::vector<float> centroids_x;
  std::vector<float> centroids_y;
  for (const auto &centroid : weighted_centroids) {
    centroids_x.push_back(centroid.x);
    centroids_y.push_back(centroid.y);
  }

  const float mid_row = std::floor((float)hough_space_height / 2.0f);

  torch::Tensor theta = torch::tensor(centroids_x, torch::TensorOptions().device_index(device)) * theta_step;
  torch::Tensor rho = torch::tensor(centroids_y, torch::TensorOptions().device_index(device)) * rho_step - mid_row;

  theta = rotate_theta_pi_half(theta.contiguous());
  rho = rotate_rho_pi_half(rho.contiguous(), theta.contiguous());

  theta = theta.cpu();
  rho = rho.cpu();

  std::vector<glm::vec2> rho_theta;
  for (int i = 0; i < theta.size(0); i++) {
    rho_theta.push_back(glm::vec2(rho[i].item().to<float>(), theta[i].item().to<float>()));
  }
  return rho_theta;
}

torch::Tensor render_hough_lines(const std::vector<glm::vec2> &rho_thetas, glm::ivec2 image_shape, int thickness,
                                 glm::ivec2 origin, int line_length) {
  torch::Tensor mask = torch::zeros({image_shape.y, image_shape.x, 1}, torch::TensorOptions());
  cv::Mat mask_cv = lib::common::wrap_to_opencv(mask);

  for (const auto &rho_theta : rho_thetas) {
    const float rho = rho_theta.x;
    const float theta = rho_theta.y;
    const float cos_theta = std::cos(theta);
    const float sin_theta = std::sin(theta);

    const float x0 = cos_theta * rho + (float)origin.x;
    const float y0 = sin_theta * rho + (float)origin.y;
    const int x1 = (int)std::round(x0 + ((float)line_length / 2.0f) * -sin_theta);
    const int y1 = (int)std::round(y0 + ((float)line_length / 2.0f) * cos_theta);
    const int x2 = (int)std::round(x0 - ((float)line_length / 2.0f) * -sin_theta);
    const int y2 = (int)std::round(y0 - ((float)line_length / 2.0f) * cos_theta);

    cv::line(mask_cv, {x1, y1}, {x2, y2}, 1, thickness);
  }

  return mask.squeeze(2);
}

torch::Tensor blur(torch::Tensor image, int kernel_size) {
  // Uniform blurring kernel
  torch::Tensor kernel = torch::ones({kernel_size, kernel_size}, torch::TensorOptions().device(image.device()));
  kernel /= kernel_size * kernel_size;

  F::conv2d(image.unsqueeze(0).unsqueeze(0), kernel.repeat({1, 1, 1, 1}));
  return image.squeeze(0).squeeze(0);
}

float get_point_to_line_distance(glm::vec2 point, glm::vec2 rho_theta) {
  float rho_prime = glm::dot(point, glm::vec2(std::cos(rho_theta.y), std::sin(rho_theta.y)));
  return std::abs(rho_theta.x - rho_prime);
}

glm::vec2 get_nearest_line(const std::vector<glm::vec2> &rho_thetas, glm::vec2 point) {
  float min_dist = std::numeric_limits<float>::max();
  glm::vec2 min_rho_theta;
  for (const auto &rho_theta : rho_thetas) {
    const float dist = get_point_to_line_distance(point, rho_theta);
    if (dist < min_dist) {
      min_dist = dist;
      min_rho_theta = rho_theta;
    }
  }
  return min_rho_theta;
}

std::vector<glm::vec2> generate_rho_theta(torch::Tensor angles_2theta, torch::Tensor mask,
                                          HoughTransformOptions options) {
  torch::Tensor angles_theta = compute_theta_tensor(angles_2theta, options.theta_flip_threshold);
  torch::Tensor hough_tensor = to_hough_tensor(mask.to(angles_2theta.device()), angles_theta, options.hough_threshold,
                                               options.rho_step, options.theta_step, options.density, *options.origin);
  std::vector<glm::vec2> centroids;
  hough_tensor /= hough_tensor.max();
  hough_tensor = blur(hough_tensor, options.kernel_size);

  if (!options.centroid_threshold) {
    options.centroid_threshold = 0.0f;
  }
  centroids = find_weighted_centroids_of_connected_components(
      hough_tensor, *options.centroid_threshold, options.elongated_component_threshold, options.num_furrows);
  std::vector<glm::vec2> rho_theta = hough_to_rho_theta(centroids, (int)hough_tensor.size(0), options.rho_step,
                                                        options.theta_step, angles_theta.device().index());

  if (options.point && rho_theta.size() > 0) {
    rho_theta = {get_nearest_line(rho_theta, *options.point)};
  }

  return rho_theta;
}

torch::Tensor generate_hough_lines(torch::Tensor angles_2theta, torch::Tensor mask, HoughTransformOptions options) {
  if (!options.origin) {
    options.origin = glm::ivec2(mask.size(2), mask.size(1)) / 2;
  }

  if (options.point) {
    options.point = *options.point - *options.origin;
  }

  torch::Tensor result = torch::zeros(mask.sizes(), torch::TensorOptions());
  for (int i = 0; i < angles_2theta.size(0); i++) {
    std::vector<glm::vec2> rho_theta = generate_rho_theta(angles_2theta[i], mask[i], options);

    result[i] = render_hough_lines(rho_theta, glm::ivec2(mask.size(2), mask.size(1)), 7, *options.origin,
                                   options.render_line_pixels);
  }

  return result;
}
} // namespace cv::furrows

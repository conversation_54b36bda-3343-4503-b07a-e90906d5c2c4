from typing import List, Optional, Set, Tuple

import torch

def generate_rho_theta(
    angles_2theta: torch.Tensor, mask: torch.Tensor, origin: Tuple[float, float], threshold: float,
) -> List[Tuple[float, float]]: ...
def to_hough_tensor(
    mask: torch.Tensor,
    angles: torch.Tensor,
    threshold: float,
    rho_step: float,
    theta_step: float,
    density: int,
    origin: Tuple[float, float],
) -> torch.Tensor: ...
def compute_theta_tensor(angle_2theta: torch.Tensor, flip_threshold: float) -> torch.Tensor: ...
def find_weighted_centroids_of_connected_components(
    input: torch.Tensor, threshold: float, elongated_component_threshold: Optional[float], number_of_furrows: int
) -> List[Tuple[float, float]]: ...
def get_weighted_centroids(
    input: torch.Tensor,
    labels: torch.Tensor,
    min_label: int,
    max_label: int,
    labels_to_avoid: Set[int],
    elongated_component_threshold: Optional[float],
    number_of_furrows: int,
) -> List[Tuple[float, float]]: ...
def component_is_elongated(coords: torch.Tensor, threshold: Optional[float]) -> bool: ...
def hough_to_rho_theta(
    weighted_centroids: List[Tuple[float, float]],
    hough_space_height: int,
    rho_step: float,
    theta_step: float,
    device: torch.Device,
) -> List[Tuple[float, float]]: ...
def get_point_to_line_distance(point: Tuple[float, float], rho_theta: Tuple[float, float]) -> float: ...
def blur(image: torch.Tensor, kernel_size: int) -> torch.Tensor: ...

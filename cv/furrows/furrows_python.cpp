#include "cv/furrows/line_utils.h"

#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <torch/extension.h>
#include <torch/torch.h>

#include "lib/common/pybind11/glm.h"

namespace py = pybind11;

namespace cv::furrows {

std::vector<glm::vec2> generate_rho_theta_python(torch::Tensor angles_2theta, torch::Tensor mask, glm::ivec2 origin,
                                                 float threshold) {
  auto options = HoughTransformOptions();
  options.origin = origin;
  options.hough_threshold = threshold;
  return generate_rho_theta(angles_2theta, mask, options);
}

PYBIND11_MODULE(furrows_python, m) {
  m.def("generate_rho_theta", &generate_rho_theta_python, py::call_guard<py::gil_scoped_release>(),
        py::arg("angles_2theta"), py::arg("mask"), py::arg("origin"), py::arg("threshold"));
  m.def("to_hough_tensor", &to_hough_tensor, py::call_guard<py::gil_scoped_release>(), py::arg("mask"),
        py::arg("angles"), py::arg("threshold"), py::arg("rho_step"), py::arg("theta_step"), py::arg("density"),
        py::arg("origin"));
  m.def("compute_theta_tensor", &compute_theta_tensor, py::call_guard<py::gil_scoped_release>(),
        py::arg("angles_2theta"), py::arg("flip_threshold"));
  m.def("find_weighted_centroids_of_connected_components", &find_weighted_centroids_of_connected_components,
        py::call_guard<py::gil_scoped_release>(), py::arg("input"), py::arg("threshold"),
        py::arg("elongated_component_threshold"), py::arg("number_of_furrows"));
  m.def("get_weighted_centroids", &get_weighted_centroids, py::call_guard<py::gil_scoped_release>(), py::arg("input"),
        py::arg("labels"), py::arg("min_label"), py::arg("max_label"), py::arg("labels_to_avoid"),
        py::arg("elongated_component_threshold"), py::arg("number_of_furrows"));
  m.def("component_is_elongated", &component_is_elongated, py::call_guard<py::gil_scoped_release>(), py::arg("coords"),
        py::arg("threshold"));
  m.def("hough_to_rho_theta", &hough_to_rho_theta, py::call_guard<py::gil_scoped_release>(),
        py::arg("weighted_centroids"), py::arg("hough_space_height"), py::arg("rho_step"), py::arg("theta_step"),
        py::arg("device"));
  m.def("get_point_to_line_distance", &get_point_to_line_distance, py::call_guard<py::gil_scoped_release>(),
        py::arg("point"), py::arg("rho_theta"));
  m.def("blur", &blur, py::call_guard<py::gil_scoped_release>(), py::arg("image"), py::arg("kernel_size"));
}
} // namespace cv::furrows
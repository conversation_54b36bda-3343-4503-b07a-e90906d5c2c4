#include "furrows_model.h"

#include <opencv2/imgproc.hpp>

#include "cv/furrows/line_utils.h"
#include "lib/common/cpp/opencv_tensor_util.h"
#include "lib/common/cpp/resize_util.h"

namespace cv::furrows {
FurrowsModel::FurrowsModel(lib::common::model::AtomicModel model) : model_(model) {
  if (!model_.get_internal_metadata().means().empty()) {
    std::vector<float> means(model_.get_internal_metadata().means().begin(),
                             model_.get_internal_metadata().means().end());
    means_ = torch::tensor(means).to({torch::kCUDA, model_.get_gpu_id()});
  }

  if (!model_.get_internal_metadata().stds().empty()) {
    std::vector<float> stds(model_.get_internal_metadata().stds().begin(), model_.get_internal_metadata().stds().end());
    stds_ = torch::tensor(stds).to({torch::kCUDA, model_.get_gpu_id()});
  }

  input_size_ = glm::ivec2(model_.get_internal_metadata().input_size().width(),
                           model_.get_internal_metadata().input_size().height());
}

torch::Tensor FurrowsModel::normalize(torch::Tensor image) {
  image = image.permute({0, 2, 3, 1});
  image /= 255.0f;
  // normalize to 0..1
  if (!model_.get_internal_metadata().means().empty()) {
    image -= means_;
  }
  if (!model_.get_internal_metadata().stds().empty()) {
    image /= stds_;
  }
  image = image.permute({0, 3, 1, 2});
  return image;
}

FurrowsOutput FurrowsModel::infer(torch::Tensor image) {
  image = image.to({torch::kCUDA, model_.get_gpu_id()}).to(torch::kF32);
  while (image.ndimension() < 4) {
    image = image.unsqueeze(0);
  }

  auto half_input_size = input_size_ / 2;
  glm::ivec2 center = glm::ivec2(image.size(-1), image.size(-2)) / 2;
  glm::ivec2 start = center - half_input_size;

  auto cropped_image = image.index({"...", torch::indexing::Slice(start.y, start.y + input_size_.y),
                                    torch::indexing::Slice(start.x, start.x + input_size_.x)});
  cropped_image = normalize(cropped_image);
  auto raw_output = FurrowsRawOutput(model_({cropped_image.contiguous()}));

  FurrowsOutput furrows_output = raw_output_to_furrows(raw_output);
  for (auto &furrow_detection : furrows_output.furrows) {
    float crop_image_ratio_width = (float)cropped_image.size(-1) / (float)furrows_output.image_width;
    float crop_image_ratio_height = (float)cropped_image.size(-2) / (float)furrows_output.image_height;
    furrow_detection.start_x = (int)((float)furrow_detection.start_x * crop_image_ratio_width + (float)start.x);
    furrow_detection.end_x = (int)((float)furrow_detection.end_x * crop_image_ratio_width + (float)start.x);

    furrow_detection.start_y = (int)((float)furrow_detection.start_y * crop_image_ratio_height + (float)start.y);
    furrow_detection.end_y = (int)((float)furrow_detection.end_y * crop_image_ratio_height + (float)start.y);
  }
  furrows_output.image_width = (int)image.size(-1);
  furrows_output.image_height = (int)image.size(-2);
  return furrows_output;
}

const ModelMetadataProto &FurrowsModel::get_internal_metadata() { return model_.get_internal_metadata(); }
const nlohmann::json &FurrowsModel::get_external_metadata() { return model_.get_external_metadata(); }

FurrowsOutput FurrowsModel::raw_output_to_furrows(FurrowsRawOutput raw_output) {
  FurrowsOutput output;
  output.image_width = (int)raw_output.mask.size(-1);
  output.image_height = (int)raw_output.mask.size(-2);
  for (int clz_i = 0; clz_i < model_.get_internal_metadata().segm_classes_size(); clz_i++) {
    std::string clz = model_.get_internal_metadata().segm_classes(clz_i);
    torch::Tensor direction = raw_output.direction.index({torch::indexing::Slice(), clz_i}).to(torch::kF32);
    torch::Tensor mask = (raw_output.mask.index({torch::indexing::Slice(), clz_i}) > 0.5f).to(torch::kF32);

    HoughTransformOptions options;
    options.origin = glm::ivec2(mask.size(2), mask.size(1)) / 2;
    options.point = glm::ivec2(mask.size(2), mask.size(1)) / 2;

    for (int batch_idx = 0; batch_idx < direction.size(0); batch_idx++) {
      auto rho_thetas = generate_rho_theta(direction[batch_idx], mask[batch_idx], options);

      for (const auto &rho_theta : rho_thetas) {
        const float rho = rho_theta.x;
        const float theta = rho_theta.y;
        const float cos_theta = std::cos(theta);
        const float sin_theta = std::sin(theta);

        const float x0 = cos_theta * rho + (float)mask.size(2) / 2.0f;
        const float y0 = sin_theta * rho + (float)mask.size(1) / 2.0f;
        auto line_length = std::max(mask.size(1), mask.size(2)) * 2;
        const int x1 =
            std::max(0, std::min((int)mask.size(-1), (int)std::round(x0 + ((float)line_length / 2.0f) * -sin_theta)));
        const int y1 =
            std::max(0, std::min((int)mask.size(-2), (int)std::round(y0 + ((float)line_length / 2.0f) * cos_theta)));
        const int x2 =
            std::max(0, std::min((int)mask.size(-1), (int)std::round(x0 - ((float)line_length / 2.0f) * -sin_theta)));
        const int y2 =
            std::max(0, std::min((int)mask.size(-2), (int)std::round(y0 - ((float)line_length / 2.0f) * cos_theta)));

        output.furrows.push_back(
            FurrowDetection{.start_x = x1, .start_y = y1, .end_x = x2, .end_y = y2, .category = clz});
      }
    }
  }
  return output;
}
} // namespace cv::furrows

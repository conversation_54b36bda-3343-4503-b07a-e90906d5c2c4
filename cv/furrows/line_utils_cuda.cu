#include <c10/cuda/CUDAStream.h>
#include <glm/glm.hpp>
#include <math_constants.h>
#include <thrust/iterator/counting_iterator.h>
#include <thrust/transform.h>

#include "line_utils_cuda.h"

namespace cv::furrows {
__device__ float rotate_rho_pi_half_device(float rho_original, float theta_original) {
  // Rotate rho so that theta origin is y axis rather than x axis
  // Whereas in original hough transform Q1, 4 are negative rho, in modified hough transform Q1, 2 are negative rho,
  // So we flip rho for Q2, 4 (where original theta is 0..pi/2)
  float rho_rotated = rho_original;

  if (theta_original < CUDART_PI / 2.0f) {
    rho_rotated = -rho_original;
  }

  return rho_rotated;
}

__device__ float rotate_theta_pi_half_device(float theta_original) {
  // Rotate theta by pi/2 so that theta origin is y axis rather than x axis
  return fmod((theta_original + CUDART_PI / 2.0f), CUDART_PI);
}

__global__ void
hough_transform_kernel(torch::PackedTensorAccessor32<float, 2, torch::RestrictPtrTraits> out,
                       torch::PackedTensorAccessor32<long int, 2, torch::RestrictPtrTraits> nonzero_thresholded_mask,
                       torch::PackedTensorAccessor32<float, 2, torch::RestrictPtrTraits> mask,
                       torch::PackedTensorAccessor32<float, 3, torch::RestrictPtrTraits> angles, const float rho_step,
                       const float theta_step, const int density, const glm::ivec2 origin, const int mid_row) {
  const int thread_idx = threadIdx.x;
  const int block_idx = blockIdx.x;
  const int block_width = blockDim.x;
  const int grid_width = gridDim.x;

  const int pos = thread_idx + block_idx * block_width;
  const int section_width =
      (nonzero_thresholded_mask.size(0) + (grid_width * block_width) - 1) / (grid_width * block_width);

  const int ind_start = pos * section_width;
  const int ind_end = (pos + 1) * section_width;

  for (int idx = ind_start; idx < ind_end; idx++) {
    if (idx % density != 0 || idx >= nonzero_thresholded_mask.size(0)) {
      continue;
    }

    const int row = nonzero_thresholded_mask[idx][0];
    const int col = nonzero_thresholded_mask[idx][1];

    const torch::TensorAccessor<float, 1, torch::RestrictPtrTraits, int32_t> cos_sin_theta = angles[row][col];
    const float confidence = mask[row][col];
    const float cos_theta = cos_sin_theta[0];
    const float sin_theta = cos_sin_theta[1];
    float rho = (col - origin.x) * cos_theta + (row - origin.y) * sin_theta;
    float theta = acos(cos_theta);

    rho = rotate_rho_pi_half_device(rho, theta);
    theta = rotate_theta_pi_half_device(theta);

    // rho is negative if the line passes right of the origin. In this case, theta will remain less than pi
    // To represent this in hough transform space, all rows above mid_row represent negative rho
    // whereas all rows below mid_row represent positive rho (mid_row == rho = 0)
    const int rho_ind = static_cast<int>((rho / rho_step) - 1e-6) + mid_row;
    const int theta_ind = static_cast<int>((theta / theta_step) - 1e-6);
    out[rho_ind][theta_ind] += confidence;
  }
}

torch::Tensor hough_transform(torch::Tensor result, torch::Tensor nonzero_thresholded_mask, torch::Tensor mask,
                              torch::Tensor angles, float rho_step, float theta_step, int density, glm::ivec2 origin) {
  if (nonzero_thresholded_mask.numel() == 0) {
    return result;
  }
  const int threads_per_block = 32;
  const int blocks_per_grid = (nonzero_thresholded_mask.size(0) + threads_per_block - 1) / threads_per_block;

  with_device device_guard(result.device().index());
  auto torch_stream = c10::cuda::getCurrentCUDAStream();
  hough_transform_kernel<<<blocks_per_grid, threads_per_block, 0, torch_stream.stream()>>>(
      result.packed_accessor32<float, 2, torch::RestrictPtrTraits>(),
      nonzero_thresholded_mask.packed_accessor32<long int, 2, torch::RestrictPtrTraits>(),
      mask.packed_accessor32<float, 2, torch::RestrictPtrTraits>(),
      angles.packed_accessor32<float, 3, torch::RestrictPtrTraits>(), rho_step, theta_step, density, origin,
      std::floor(result.size(0) / 2.0f));

  return result;
}

torch::Tensor rotate_theta_pi_half(torch::Tensor theta) {
  if (theta.numel() == 0) {
    return theta;
  }

  with_device device_guard(theta.device().index());
  auto torch_stream = c10::cuda::getCurrentCUDAStream();
  float *theta_ptr = (float *)theta.data_ptr();
  thrust::for_each(thrust::cuda::par.on(torch_stream.stream()), thrust::counting_iterator<int>(0),
                   thrust::counting_iterator<int>(theta.numel()), [theta_ptr] __device__(int index) {
                     theta_ptr[index] = rotate_theta_pi_half_device(theta_ptr[index]);
                   });
  return theta;
}

torch::Tensor rotate_rho_pi_half(torch::Tensor rho, torch::Tensor theta) {
  if (rho.numel() == 0) {
    return rho;
  }

  with_device device_guard(theta.device().index());
  auto torch_stream = c10::cuda::getCurrentCUDAStream();
  float *rho_ptr = (float *)rho.data_ptr();
  float *theta_ptr = (float *)theta.data_ptr();
  thrust::for_each(thrust::cuda::par.on(torch_stream.stream()), thrust::counting_iterator<int>(0),
                   thrust::counting_iterator<int>(rho.numel()), [rho_ptr, theta_ptr] __device__(int index) {
                     rho_ptr[index] = rotate_rho_pi_half_device(rho_ptr[index], theta_ptr[index]);
                   });
  return rho;
}

} // namespace cv::furrows

#pragma once

#include <glm/glm.hpp>
#include <torch/torch.h>

#include "lib/common/cpp/cuda_util.h"

namespace cv::furrows {
torch::Tensor hough_transform(torch::Tensor result, torch::Tensor nonzero_thresholded_mask, torch::Tensor mask,
                              torch::Tensor angles, float rho_step, float theta_step, int density, glm::ivec2 origin);

torch::Tensor rotate_theta_pi_half(torch::Tensor theta);

torch::Tensor rotate_rho_pi_half(torch::Tensor rho, torch::Tensor theta);

} // namespace cv::furrows

#pragma once

#include <filesystem>

#include <exception>
#include <map>
#include <spdlog/spdlog.h>
#include <torch/torch.h>

namespace cv::embeddings {

const std::string kEmbeddingKey = "embedding";
const std::string kWeedScore = "weed_score";
const std::string kCropScore = "crop_score";

class Cache {
public:
  Cache(std::filesystem::path cache_path) : cache_path_(cache_path) {}
  virtual ~Cache() {}
  bool contains(const std::string &chip_id, const std::string &model_id) {
    return (std::filesystem::exists(get_path(chip_id, model_id, kEmbeddingKey)) &&
            std::filesystem::exists(get_path(chip_id, model_id, kWeedScore)) &&
            std::filesystem::exists(get_path(chip_id, model_id, kCropScore)));
  }
  std::optional<std::map<std::string, torch::Tensor>> get(const std::string &chip_id, const std::string &model_id) {
    std::map<std::string, torch::Tensor> tensor_map;
    torch::Tensor embedding;
    torch::Tensor weed_score;
    torch::Tensor crop_score;

    try {
      torch::load(embedding, get_path(chip_id, model_id, kEmbeddingKey));
      torch::load(weed_score, get_path(chip_id, model_id, kWeedScore));
      torch::load(crop_score, get_path(chip_id, model_id, kCropScore));
    } catch (std::exception &e) {
      // Failed to load one or more tensors so we should remove them to be recomputed
      std::filesystem::remove(get_path(chip_id, model_id, kEmbeddingKey));
      std::filesystem::remove(get_path(chip_id, model_id, kWeedScore));
      std::filesystem::remove(get_path(chip_id, model_id, kCropScore));
      return std::nullopt;
    }

    tensor_map[kEmbeddingKey] = embedding;
    tensor_map[kWeedScore] = weed_score;
    tensor_map[kCropScore] = crop_score;

    return tensor_map;
  }

  void put(const std::string &chip_id, const std::string &model_id, std::map<std::string, torch::Tensor> tensor_map) {
    auto path = get_path(chip_id, model_id, kEmbeddingKey);
    std::filesystem::create_directories(path.parent_path());

    for (auto it = tensor_map.begin(); it != tensor_map.end(); it++) {
      torch::save(it->second, get_path(chip_id, model_id, it->first));
    }
  }

private:
  std::filesystem::path get_path(const std::string &chip_id, const std::string &model_id, const std::string &suffix) {
    return (cache_path_ / model_id / chip_id / suffix).replace_extension("pt");
  }

  std::filesystem::path cache_path_;
};
} // namespace cv::embeddings

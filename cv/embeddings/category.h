#pragma once
#include <torch/torch.h>

namespace cv::embeddings {

class Category {
public:
  Category(torch::Tensor embeddings, torch::Tensor weed_scores, torch::Tensor crop_scores);

  Category to(torch::DeviceType device, int device_id);
  torch::Tensor embeddings() { return embeddings_; }
  torch::Tensor weed_scores() { return weed_scores_; }
  torch::Tensor crop_scores() { return crop_scores_; }

private:
  torch::Tensor embeddings_;
  torch::Tensor weed_scores_;
  torch::Tensor crop_scores_;
};

} // namespace cv::embeddings
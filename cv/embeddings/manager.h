#pragma once

#include "category/proto/category.pb.h"
#include "cv/embeddings/cache.h"
#include "cv/embeddings/category.h"
#include "lib/common/model/cpp/model_registry.h"
#include "lib/common/redis/redis_client.hpp"

#include <memory>
#include <prometheus/family.h>
#include <prometheus/histogram.h>
#include <torch/torch.h>

namespace cv::embeddings {

class embeddings_unsupported_exception : public std::runtime_error {
public:
  embeddings_unsupported_exception(const std::string &message) : std::runtime_error(message) {}
};

class Manager {
public:
  Manager(std::shared_ptr<Cache> cache,
          std::shared_ptr<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>> model_registry,
          int eval_gpu_id, std::filesystem::path chip_store_path, std::shared_ptr<prometheus::Registry> registry);
  virtual ~Manager();

  void set_active_category_collection(const carbon::category::CategoryCollection &category_collection_proto,
                                      const std::vector<carbon::category::Category> category_definitions_proto,
                                      int64_t last_updated_timestamp_ms, const std::string &crop_id);
  bool is_valid_category_collection(const carbon::category::CategoryCollection &category_collection_proto,
                                    const std::vector<carbon::category::Category> category_definitions_proto);
  std::string get_active_category_collection_id();
  std::vector<Category> get_category_collection(int gpu_id);
  std::vector<std::string> get_categories();
  int64_t get_last_updated_timestamp_ms();
  void update_category_collection_metrics();

private:
  std::shared_mutex mutex_;
  std::shared_ptr<Cache> cache_;
  std::shared_ptr<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>> model_registry_;
  std::unique_ptr<std::reference_wrapper<prometheus::Family<prometheus::Gauge>>> interclass_distance_family_;
  std::map<std::string, std::reference_wrapper<prometheus::Gauge>> interclass_distance_gauges_;
  int eval_gpu_id_;
  std::filesystem::path chip_store_path_;
  static const std::string kChipExtension;

  std::unordered_map<int, std::vector<Category>> category_collection_cache_;
  std::vector<Category> category_collection_;
  std::vector<std::string> categories_;
  int64_t last_updated_timestamp_ms_;
  std::string category_collection_id_;
  std::string model_id_;
};
} // namespace cv::embeddings

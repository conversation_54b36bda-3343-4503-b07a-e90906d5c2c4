#include "nearest_neighbor_classifier.h"
#include <shared_mutex>
#include <spdlog/spdlog.h>

namespace cv::embeddings {
NearestNeighborClassifier::NearestNeighborClassifier(
    std::function<torch::Tensor(torch::Tensor, torch::Tensor)> distance_function)
    : Classifier(distance_function) {}
NearestNeighborClassifier::~NearestNeighborClassifier() {}

void NearestNeighborClassifier::set_category_collection(std::vector<cv::embeddings::Category> category_embeddings,
                                                        std::vector<std::string> categories) {
  spdlog::info("Setting category collection for NearestNeighborClassifier");
  std::unique_lock lock(mut_);
  if (category_embeddings.empty()) {
    return;
  }
  categories_ = categories;
  int max_examples = 0;
  for (Category category_embedding : category_embeddings) {
    max_examples = std::max(max_examples, (int)category_embedding.embeddings().size(0));
  }
  max_examples_ = max_examples;
  category_embeddings_ =
      torch::full({(int64_t)category_embeddings.size(), max_examples, category_embeddings[0].embeddings().size(1)},
                  std::numeric_limits<float>::infinity(),
                  torch::TensorOptions().device(category_embeddings[0].embeddings().device()).dtype(torch::kFloat32));
  for (int i = 0; i < (int)category_embeddings.size(); i++) {
    for (int j = 0; j < category_embeddings[i].embeddings().size(0); j++) {
      category_embeddings_[i][j] = category_embeddings[i].embeddings()[j];
    }
  }
  category_embeddings_ = category_embeddings_.reshape(
      {category_embeddings_.size(0) * category_embeddings_.size(1), category_embeddings_.size(2)});
}

std::vector<std::string> NearestNeighborClassifier::get_categories() {
  std::shared_lock lock(mut_);
  return categories_;
}

torch::Tensor NearestNeighborClassifier::classify(torch::Tensor embeddings, torch::Tensor weed_scores,
                                                  torch::Tensor crop_scores) {
  (void)weed_scores;
  (void)crop_scores;
  std::shared_lock lock(mut_);
  if (category_embeddings_.size(0) == 0) {
    return torch::empty({embeddings.size(0), (int64_t)categories_.size()},
                        torch::TensorOptions().device(embeddings.device()).dtype(torch::kFloat32));
  }
  torch::Tensor distances = distance_function_(embeddings, category_embeddings_);
  distances = distances.reshape({distances.size(0), (int64_t)categories_.size(), max_examples_});
  distances = torch::where(torch::isfinite(distances), distances, std::numeric_limits<float>::infinity());
  torch::Tensor category_min_distances;
  torch::Tensor embedding_classifications;
  std::tie(category_min_distances, std::ignore) = torch::min(distances, 2);
  return category_min_distances;
}

} // namespace cv::embeddings

#pragma once

#include "cv/embeddings/category.h"
#include <shared_mutex>
#include <torch/torch.h>

namespace cv::embeddings {
class Classifier {
public:
  Classifier(std::function<torch::Tensor(torch::Tensor, torch::Tensor)> distance_function)
      : distance_function_(distance_function) {}
  Classifier(const Classifier &other) : mut_(), distance_function_(other.distance_function_){};
  virtual ~Classifier() {}

  /**
   * Update the category profile
   * @param category_embeddings C length vector of ExD tensor where C is the number of categories, E is the number of
   * examples, and D is the embedding dimension
   * @param categories List of category names
   */
  virtual void set_category_collection(std::vector<cv::embeddings::Category> category_embeddings,
                                       std::vector<std::string> categories) = 0;

  /**
   * Classifies the input embeddings
   * @param embeddings BxD tensor where B is the number of items, and D is the embedding dimension
   * @return BxC tensor where B is the number of examples, and C is the number of categories. Contains the distance to
   * each category
   */
  virtual torch::Tensor classify(torch::Tensor embeddings, torch::Tensor weed_scores, torch::Tensor crop_scores) = 0;
  virtual std::vector<std::string> get_categories() = 0;

protected:
  mutable std::shared_mutex mut_;
  std::function<torch::Tensor(torch::Tensor, torch::Tensor)> distance_function_;
};
} // namespace cv::embeddings

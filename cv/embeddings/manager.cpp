#include "cv/embeddings/manager.h"

#include "config/client/cpp/config_subscriber.hpp"
#include "cv/deepweed/deepweed_model.h"
#include "cv/embeddings/distance_functions.h"
#include "cv/utils/resize.h"
#include "lib/common/cpp/opencv_tensor_util.h"

#include <opencv2/opencv.hpp>
#include <spdlog/spdlog.h>

namespace cv::embeddings {
const std::string Manager::kChipExtension = "png";

Manager::Manager(std::shared_ptr<Cache> cache,
                 std::shared_ptr<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>> model_registry,
                 int eval_gpu_id, std::filesystem::path chip_store_path, std::shared_ptr<prometheus::Registry> registry)
    : cache_(cache), model_registry_(model_registry), eval_gpu_id_(eval_gpu_id), chip_store_path_(chip_store_path) {
  interclass_distance_family_ = std::make_unique<std::reference_wrapper<prometheus::Family<prometheus::Gauge>>>(
      prometheus::BuildGauge()
          .Name("embeddings_interclass_distance")
          .Help("Mean interclass distance per category")
          .Register(*registry));
}

Manager::~Manager() {}

std::vector<std::string> Manager::get_categories() {
  std::shared_lock<std::shared_mutex> lck(mutex_);
  return categories_;
}

std::vector<Category> Manager::get_category_collection(int gpu_id) {
  std::shared_lock<std::shared_mutex> lck(mutex_);
  if (category_collection_cache_.count(gpu_id) == 0) {
    std::vector<Category> category_collection;
    for (auto &category : category_collection_) {
      category_collection.push_back(category.to(torch::kCUDA, gpu_id));
    }
    category_collection_cache_[gpu_id] = category_collection;
  }

  return category_collection_cache_[gpu_id];
}

std::string Manager::get_active_category_collection_id() {
  std::shared_lock<std::shared_mutex> lck(mutex_);
  return category_collection_id_;
}

int64_t Manager::get_last_updated_timestamp_ms() {
  std::shared_lock<std::shared_mutex> lck(mutex_);
  return last_updated_timestamp_ms_;
}

void Manager::set_active_category_collection(const carbon::category::CategoryCollection &category_collection_proto,
                                             const std::vector<carbon::category::Category> category_definitions_proto,
                                             int64_t last_updated_timestamp_ms, const std::string &crop_id) {
  const auto model_use_case = lib::common::model::ModelUseCase::kPredict;
  if (category_collection_proto.category_ids().size() != (int)category_definitions_proto.size() ||
      !model_registry_->contains(model_use_case)) {
    return;
  }
  std::unique_lock<std::shared_mutex> lck(mutex_);

  auto new_model_id = model_registry_->get_external_metadata_by_use_case(model_use_case)["id"];
  auto previous_category_collection_id = category_collection_id_;
  auto new_category_collection_id = category_collection_proto.id();
  category_collection_cache_.clear();
  categories_.clear();

  deepweed::DeepweedModel deepweed_model(model_registry_->get(model_use_case, eval_gpu_id_));
  deepweed_model.set_crop_id(crop_id);
  std::vector<Category> category_collection;
  for (int i = 0; i < category_collection_proto.category_ids().size(); i++) {
    std::string category_name = category_definitions_proto[i].name();
    spdlog::info("Category {} contains {} chips", category_name, category_definitions_proto[i].chip_ids().size());
    std::transform(category_name.begin(), category_name.end(), category_name.begin(),
                   [](unsigned char c) { return std::toupper(c); });
    std::vector<torch::Tensor> embeddings;
    std::vector<torch::Tensor> weed_scores;
    std::vector<torch::Tensor> crop_scores;
    std::vector<std::string> chip_ids_cache_miss;
    for (const auto &chip_id : category_definitions_proto[i].chip_ids()) {
      if (!cache_->contains(chip_id, new_model_id)) {
        chip_ids_cache_miss.push_back(chip_id);
      } else {
        auto tensor_map_opt = cache_->get(chip_id, new_model_id);
        if (!tensor_map_opt) {
          chip_ids_cache_miss.push_back(chip_id);
        } else {
          auto tensor_map = *tensor_map_opt;
          embeddings.push_back(tensor_map[kEmbeddingKey].to({torch::kCUDA, eval_gpu_id_}));
          weed_scores.push_back(tensor_map[kWeedScore].to({torch::kCUDA, eval_gpu_id_}));
          crop_scores.push_back(tensor_map[kCropScore].to({torch::kCUDA, eval_gpu_id_}));
        }
      }
    }

    for (int chip_start_index = 0; chip_start_index < (int)chip_ids_cache_miss.size();
         chip_start_index += deepweed_model.get_max_batch_size()) {
      std::vector<torch::Tensor> chip_tensors;
      int max_width = 0;
      int max_height = 0;
      for (int chip_index = chip_start_index;
           chip_index <
           std::min(chip_start_index + deepweed_model.get_max_batch_size(), (int)chip_ids_cache_miss.size());
           chip_index++) {
        auto cv_mat =
            cv::imread((chip_store_path_ / chip_ids_cache_miss[chip_index]).replace_extension(kChipExtension).string(),
                       cv::IMREAD_COLOR);
        cv::cvtColor(cv_mat, cv_mat, cv::COLOR_BGR2RGB);
        auto tensor = lib::common::to_tensor(cv_mat).permute({2, 0, 1}).to({torch::kCUDA, eval_gpu_id_});
        max_width = std::max(max_width, (int)tensor.size(-1));
        max_height = std::max(max_height, (int)tensor.size(-2));
        chip_tensors.push_back(tensor);
      }

      for (int chip_index = 0; chip_index < (int)chip_tensors.size(); chip_index++) {
        std::tie(chip_tensors[chip_index], std::ignore) =
            cv::utils::crop_or_pad(chip_tensors[chip_index],
                                   glm::ivec2(chip_tensors[chip_index].size(-1), chip_tensors[chip_index].size(-2)) / 2,
                                   glm::ivec2(max_width, max_height));
      }

      torch::Tensor chip_tensor = torch::stack(chip_tensors);

      std::vector<cv::deepweed::EmbeddingPrediction> embedding_per_ds = deepweed_model.get_embedding(
          chip_tensor, (float)(chip_tensor.size(-1) / 2), (float)(chip_tensor.size(-2) / 2));
      if (embedding_per_ds.size() == 0) {
        throw embeddings_unsupported_exception(
            "Model does not support embeddings. Setting active category collection failed.");
      }

      torch::Tensor embedding = embedding_per_ds[0].embedding.to({torch::kCUDA, eval_gpu_id_});
      torch::Tensor weed_score = embedding_per_ds[0].weed_score.to({torch::kCUDA, eval_gpu_id_});
      torch::Tensor crop_score = embedding_per_ds[0].crop_score.to({torch::kCUDA, eval_gpu_id_});
      for (int embedding_index = 0; embedding_index < embedding.size(0); embedding_index++) {
        std::map<std::string, torch::Tensor> tensor_map;
        tensor_map[kEmbeddingKey] = embedding[embedding_index].cpu().contiguous();
        tensor_map[kWeedScore] = weed_score[embedding_index].cpu().contiguous();
        tensor_map[kCropScore] = crop_score[embedding_index].cpu().contiguous();
        cache_->put(chip_ids_cache_miss[chip_start_index + embedding_index], new_model_id, tensor_map);
        embeddings.push_back(embedding[embedding_index]);
        weed_scores.push_back(weed_score[embedding_index]);
        crop_scores.push_back(crop_score[embedding_index]);
      }
    }
    if (embeddings.size() == 0) {
      continue;
    }

    category_collection.push_back(
        Category(torch::stack(embeddings), torch::stack(weed_scores), torch::stack(crop_scores)));
    categories_.push_back(category_name);
  }

  last_updated_timestamp_ms_ = last_updated_timestamp_ms;
  category_collection_id_ = new_category_collection_id;
  model_id_ = new_model_id;
  category_collection_ = category_collection;
  category_collection_cache_[eval_gpu_id_] = category_collection;
}

bool Manager::is_valid_category_collection(const carbon::category::CategoryCollection &category_collection_proto,
                                           const std::vector<carbon::category::Category> category_definitions_proto) {
  if (category_collection_proto.category_ids().size() != (int)category_definitions_proto.size()) {
    return false;
  }
  for (int i = 0; i < category_collection_proto.category_ids().size(); i++) {
    for (const auto &chip_id : category_definitions_proto[i].chip_ids()) {
      auto chip_path = (chip_store_path_ / chip_id).replace_extension(kChipExtension);
      if (!std::filesystem::exists(chip_path)) {
        spdlog::warn("{} chip missing", chip_path.string());
        return false;
      }
      auto cv_mat = cv::imread(chip_path.string());
      if (cv_mat.empty() || cv_mat.channels() != 3) {
        spdlog::warn("{} chip malformed", chip_path.string());
        return false;
      }
    }
  }
  return true;
}

void Manager::update_category_collection_metrics() {
  for (int i = 0; i < (int)category_collection_.size(); i++) {
    Category &category = category_collection_[i];
    for (int j = i; j < (int)category_collection_.size(); j++) {
      Category &other_category = category_collection_[j];
      float mean_interclass_distance =
          cosine_distance(category.embeddings(), other_category.embeddings()).mean().item<float>();
      std::string combined_category_name = categories_[i] + "_" + categories_[j];
      if (interclass_distance_gauges_.count(combined_category_name) == 0) {
        interclass_distance_gauges_.emplace(combined_category_name, interclass_distance_family_->get().Add(
                                                                        {{"first_detection_type", categories_[i]},
                                                                         {"second_detection_type", categories_[j]}}));
      }
      interclass_distance_gauges_.at(combined_category_name).get().Set(mean_interclass_distance);
    }
  }
}
} // namespace cv::embeddings

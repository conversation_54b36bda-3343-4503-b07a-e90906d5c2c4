#include "cv/embeddings/manager.h"
#include "gtest/gtest.h"

#include <prometheus/registry.h>

using namespace cv::embeddings;

TEST(EmbeddingManager, Basic) {
  const std::string crop_id = "d1d5db";
  const auto category_ids = {"1", "2"};
  const auto category_one_chip_ids = {
      "predict_slayer40_row1_predict2_2023-12-20T13-38-55-567000Z_crop_3251_62_213_213_#d1d5db",
      "predict_slayer45_row2_predict2_2024-12-02T01-27-17-669000Z_crop_2360_2544_87_87_#d1d5db"};
  const auto category_two_chip_ids = {
      "predict_slayer46_row1_predict4_2023-12-30T18-45-31-311000Z_crop_449_1486_571_571_#d1d5db"};
  const std::string test_chip_data_path = "/robot/cv/embeddings/tests/data/";

  std::filesystem::remove_all("/tmp/embedding_cache/");
  auto cache = std::make_shared<Cache>("/tmp/embedding_cache/");
  auto model_registry = std::make_shared<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>>();
  model_registry->set_model_for_use_case(lib::common::model::ModelUseCase::kPredict,
                                         "/robot/test/models/fut-20241216-814jypvwjo.trt");

  carbon::category::CategoryCollection category_collection;
  std::vector<carbon::category::Category> category_definition(2);
  category_collection.set_id("1234");
  *category_collection.mutable_category_ids() = {category_ids.begin(), category_ids.end()};
  category_definition[0].set_id("1");
  category_definition[0].set_name("cat1");
  *category_definition[0].mutable_chip_ids() = {category_one_chip_ids.begin(), category_one_chip_ids.end()};
  category_definition[1].set_id("2");
  category_definition[1].set_name("cat2");
  *category_definition[1].mutable_chip_ids() = {category_two_chip_ids.begin(), category_two_chip_ids.end()};
  const int64_t last_updated_timestamp_ms = 220;
  auto registry = std::make_shared<prometheus::Registry>();
  // Run once to populate cache and once using cache
  for (int i = 0; i < 2; i++) {
    Manager manager(cache, model_registry, 0, test_chip_data_path, registry);
    manager.set_active_category_collection(category_collection, category_definition, last_updated_timestamp_ms,
                                           crop_id);

    EXPECT_EQ(manager.get_active_category_collection_id(), category_collection.id());
    EXPECT_EQ(manager.get_last_updated_timestamp_ms(), last_updated_timestamp_ms);
    auto category_collection_embeddings = manager.get_category_collection(0);
    EXPECT_EQ(category_collection_embeddings.size(), category_definition.size());
    for (int category_index = 0; category_index < (int)category_collection_embeddings.size(); category_index++) {
      EXPECT_EQ(category_collection_embeddings[category_index].embeddings().dim(), 2);
      EXPECT_EQ(category_collection_embeddings[category_index].embeddings().size(0),
                category_definition[category_index].chip_ids().size());
      EXPECT_EQ(category_collection_embeddings[category_index].embeddings().size(1), 1024);
    }
  }
}

TEST(EmbeddingManager, CategoryWithNoChips) {
  const std::string crop_id = "d1d5db";
  const auto category_ids = {"1", "2"};
  const auto category_one_chip_ids = {
      "predict_slayer40_row1_predict2_2023-12-20T13-38-55-567000Z_crop_3251_62_213_213_#d1d5db",
      "predict_slayer45_row2_predict2_2024-12-02T01-27-17-669000Z_crop_2360_2544_87_87_#d1d5db"};
  const std::vector<std::string> category_two_chip_ids;
  const std::string test_chip_data_path = "/robot/cv/embeddings/tests/data/";

  std::filesystem::remove_all("/tmp/embedding_cache/");
  auto cache = std::make_shared<Cache>("/tmp/embedding_cache/");
  auto model_registry = std::make_shared<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>>();
  model_registry->set_model_for_use_case(lib::common::model::ModelUseCase::kPredict,
                                         "/robot/test/models/fut-20241216-814jypvwjo.trt");

  carbon::category::CategoryCollection category_collection;
  std::vector<carbon::category::Category> category_definition(2);
  category_collection.set_id("1234");
  *category_collection.mutable_category_ids() = {category_ids.begin(), category_ids.end()};
  category_definition[0].set_id("1");
  category_definition[0].set_name("cat1");
  *category_definition[0].mutable_chip_ids() = {category_one_chip_ids.begin(), category_one_chip_ids.end()};
  category_definition[1].set_id("2");
  category_definition[1].set_name("cat2");
  *category_definition[1].mutable_chip_ids() = {category_two_chip_ids.begin(), category_two_chip_ids.end()};
  const int64_t last_updated_timestamp_ms = 225;
  auto registry = std::make_shared<prometheus::Registry>();
  // Run once to populate cache and once using cache
  for (int i = 0; i < 2; i++) {
    Manager manager(cache, model_registry, 0, test_chip_data_path, registry);
    manager.set_active_category_collection(category_collection, category_definition, last_updated_timestamp_ms,
                                           crop_id);

    EXPECT_EQ(manager.get_active_category_collection_id(), category_collection.id());
    EXPECT_EQ(manager.get_last_updated_timestamp_ms(), last_updated_timestamp_ms);
    auto category_collection_embeddings = manager.get_category_collection(0);
    EXPECT_EQ(category_collection_embeddings.size(), 1);
    for (int category_index = 0; category_index < (int)category_collection_embeddings.size(); category_index++) {
      EXPECT_EQ(category_collection_embeddings[category_index].embeddings().dim(), 2);
      EXPECT_EQ(category_collection_embeddings[category_index].embeddings().size(0),
                category_definition[category_index].chip_ids().size());
      EXPECT_EQ(category_collection_embeddings[category_index].embeddings().size(1), 1024);
    }
  }
}

TEST(EmbeddingManager, AllCategoriesWithNoChips) {
  const auto category_ids = {"1", "2"};
  const std::vector<std::string> category_one_chip_ids;
  const std::vector<std::string> category_two_chip_ids;
  const std::string test_chip_data_path = "/robot/cv/embeddings/tests/data/";

  std::filesystem::remove_all("/tmp/embedding_cache/");
  auto cache = std::make_shared<Cache>("/tmp/embedding_cache/");
  auto model_registry = std::make_shared<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>>();
  model_registry->set_model_for_use_case(lib::common::model::ModelUseCase::kPredict,
                                         "/robot/test/models/fut-20241216-814jypvwjo.trt");

  carbon::category::CategoryCollection category_collection;
  std::vector<carbon::category::Category> category_definition(2);
  category_collection.set_id("1234");
  *category_collection.mutable_category_ids() = {category_ids.begin(), category_ids.end()};
  category_definition[0].set_id("1");
  category_definition[0].set_name("cat1");
  *category_definition[0].mutable_chip_ids() = {category_one_chip_ids.begin(), category_one_chip_ids.end()};
  category_definition[1].set_id("2");
  category_definition[1].set_name("cat2");
  *category_definition[1].mutable_chip_ids() = {category_two_chip_ids.begin(), category_two_chip_ids.end()};
  auto registry = std::make_shared<prometheus::Registry>();
  // Run once to populate cache and once using cache
  for (int i = 0; i < 2; i++) {
    Manager manager(cache, model_registry, 0, test_chip_data_path, registry);
    EXPECT_TRUE(manager.is_valid_category_collection(category_collection, category_definition));
  }
}

TEST(EmbeddingManager, AllCategoriesMissingChips) {
  const std::string crop_id = "d1d5db";
  const auto category_ids = {"1", "2"};
  const auto category_one_chip_ids = {
      "predict_slayer40_row1_predict2_2023-12-20T13-38-55-567000Z_crop_3251_62_213_213_#d1d5db",
      "predict_slayer45_row2_predict2_2024-12-02T01-27-17-669000Z_crop_2360_2544_87_87_#d1d5db"};
  const auto category_two_chip_ids = {
      "predict_slayer46_row1_predict4_2023-12-30T18-45-31-311000Z_crop_449_1486_571_571_#d1d5db"};
  const std::string test_chip_data_path_empty = "/robot/cv/embeddings/tests/data/blarg/";

  std::filesystem::remove_all("/tmp/embedding_cache/");
  auto cache = std::make_shared<Cache>("/tmp/embedding_cache/");
  auto model_registry = std::make_shared<lib::common::model::ModelRegistry<lib::common::model::ModelUseCase>>();
  model_registry->set_model_for_use_case(lib::common::model::ModelUseCase::kPredict,
                                         "/robot/test/models/fut-20241216-814jypvwjo.trt");

  carbon::category::CategoryCollection category_collection;
  std::vector<carbon::category::Category> category_definition(2);
  category_collection.set_id("1234");
  *category_collection.mutable_category_ids() = {category_ids.begin(), category_ids.end()};
  category_definition[0].set_id("1");
  category_definition[0].set_name("cat1");
  *category_definition[0].mutable_chip_ids() = {category_one_chip_ids.begin(), category_one_chip_ids.end()};
  category_definition[1].set_id("2");
  category_definition[1].set_name("cat2");
  *category_definition[1].mutable_chip_ids() = {category_two_chip_ids.begin(), category_two_chip_ids.end()};
  auto registry = std::make_shared<prometheus::Registry>();

  Manager manager(cache, model_registry, 0, test_chip_data_path_empty, registry);
  EXPECT_FALSE(manager.is_valid_category_collection(category_collection, category_definition));
}

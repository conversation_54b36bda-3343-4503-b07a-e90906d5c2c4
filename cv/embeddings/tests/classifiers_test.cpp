#include "cv/embeddings/category.h"
#include "cv/embeddings/distance_functions.h"
#include "cv/embeddings/nearest_neighbor_classifier.h"
#include "cv/embeddings/random_forest_classifier.h"
#include "gtest/gtest.h"

using namespace cv::embeddings;

TEST(EmbeddingClassifier, NearestNeighborEuclidean) {
  std::vector<cv::embeddings::Category> category_embeddings = {
      Category(torch::tensor({{1, 2}, {3, 4}, {5, 6}, {9, 10}}).to(torch::kFloat32),
               torch::tensor({{0.3}, {0.4}, {0.6}, {0.7}}).to(torch::kFloat32),
               torch::tensor({{0.35}, {0.45}, {0.65}, {0.75}}).to(torch::kFloat32)),
      Category(torch::tensor({{11, 12}, {13, 14}, {15, 16}}).to(torch::kFloat32),
               torch::tensor({{0.3}, {0.4}, {0.6}}).to(torch::kFloat32),
               torch::tensor({{0.35}, {0.45}, {0.65}}).to(torch::kFloat32)),
      Category(torch::tensor({{21, 22}, {23, 24}, {25, 26}, {27, 28}, {29, 30}}).to(torch::kFloat32),
               torch::tensor({{0.3}, {0.4}, {0.6}, {0.7}, {0.8}}).to(torch::kFloat32),
               torch::tensor({{0.35}, {0.45}, {0.65}, {0.75}, {0.85}}).to(torch::kFloat32))};

  NearestNeighborClassifier classifier(euclidean_distance);
  classifier.set_category_collection(category_embeddings, {"a", "b", "c"});

  torch::Tensor embeddings = torch::tensor({{1, 2}, {3, 4}, {15, 15}, {16, 16}, {25, 26}}).to(torch::kFloat32);
  torch::Tensor weed_scores = torch::tensor({{0.1}, {0.2}, {0.3}, {0.4}, {0.5}}).to(torch::kFloat32);
  torch::Tensor crop_scores = torch::tensor({{0.2}, {0.25}, {0.35}, {0.45}, {0.55}}).to(torch::kFloat32);
  std::vector<std::string> expected_categories = {"a", "a", "b", "b", "c"};
  torch::Tensor embedding_classes = classifier.classify(embeddings, weed_scores, crop_scores);
  auto categories = classifier.get_categories();
  EXPECT_EQ(embedding_classes.size(0), embeddings.size(0));
  for (int i = 0; i < embedding_classes.size(0); i++) {
    auto classification = torch::argmin(embedding_classes[i]).item<int>();
    EXPECT_LT(classification, categories.size());
    EXPECT_EQ(categories[classification], expected_categories[i]);
  }
}

TEST(EmbeddingClassifier, NearestNeighborCosine) {
  std::vector<Category> category_embeddings = {
      Category(torch::tensor({{1, 0}, {1, 0}, {1, 0}, {1, 0}}).to(torch::kFloat32),
               torch::tensor({{0.3}, {0.4}, {0.6}, {0.7}}).to(torch::kFloat32),
               torch::tensor({{0.35}, {0.45}, {0.65}, {0.75}}).to(torch::kFloat32)),
      Category(torch::tensor({{0, 1}, {0, 1}, {0, 1}}).to(torch::kFloat32),
               torch::tensor({{0.3}, {0.4}, {0.6}}).to(torch::kFloat32),
               torch::tensor({{0.35}, {0.45}, {0.65}}).to(torch::kFloat32)),
      Category(torch::tensor({{-1, 0}, {-1, 0}, {-1, 0}, {-1, 0}, {-1, 0}}).to(torch::kFloat32),
               torch::tensor({{0.3}, {0.4}, {0.6}, {0.7}, {0.8}}).to(torch::kFloat32),
               torch::tensor({{0.35}, {0.45}, {0.65}, {0.75}, {0.85}}).to(torch::kFloat32))};

  NearestNeighborClassifier classifier(cosine_distance);
  classifier.set_category_collection(category_embeddings, {"a", "b", "c"});

  torch::Tensor embeddings = torch::tensor({{1, 0}, {1, 0}, {0, 1}, {0, 1}, {-1, 0}}).to(torch::kFloat32);
  torch::Tensor weed_scores = torch::tensor({{0.1}, {0.2}, {0.3}, {0.4}, {0.5}}).to(torch::kFloat32);
  torch::Tensor crop_scores = torch::tensor({{0.2}, {0.25}, {0.35}, {0.45}, {0.55}}).to(torch::kFloat32);
  std::vector<std::string> expected_categories = {"a", "a", "b", "b", "c"};
  torch::Tensor embedding_classes = classifier.classify(embeddings, weed_scores, crop_scores);
  auto categories = classifier.get_categories();
  EXPECT_EQ(embedding_classes.size(0), embeddings.size(0));
  for (int i = 0; i < embedding_classes.size(0); i++) {
    auto classification = torch::argmin(embedding_classes[i]).item<int>();
    EXPECT_LT(classification, categories.size());
    EXPECT_EQ(categories[classification], expected_categories[i]);
  }
}

TEST(EmbeddingClassifier, EmptyCategory) {
  std::vector<cv::embeddings::Category> category_embeddings = {};
  torch::Tensor embeddings = torch::tensor({{1, 2}, {3, 4}, {15, 15}, {16, 16}, {25, 26}}).to(torch::kFloat32);
  torch::Tensor weed_scores = torch::tensor({{0.2}, {0.3}, {0.4}, {0.5}, {0.6}}).to(torch::kFloat32);
  torch::Tensor crop_scores = torch::tensor({{0.23}, {0.33}, {0.43}, {0.53}, {0.63}}).to(torch::kFloat32);

  {
    NearestNeighborClassifier classifier_nn(euclidean_distance);
    classifier_nn.set_category_collection(category_embeddings, {"a", "b", "c"});
    torch::Tensor embedding_classes_nn = classifier_nn.classify(embeddings, weed_scores, crop_scores);
    EXPECT_EQ(embedding_classes_nn.size(0), embeddings.size(0));
  }

  {
    RandomForestClassifier classifier_rf(euclidean_distance);
    classifier_rf.set_category_collection(category_embeddings, {"a", "b", "c"});
    torch::Tensor embedding_classes_rf = classifier_rf.classify(embeddings, weed_scores, crop_scores);
    EXPECT_EQ(embedding_classes_rf.size(0), embeddings.size(0));
  }
}

TEST(EmbeddingClassifier, RandomForest) {
  std::vector<cv::embeddings::Category> category_embeddings = {
      cv::embeddings::Category(torch::tensor({{1, 2}, {3, 4}, {5, 6}, {9, 10}}).to(torch::kFloat32),
                               torch::tensor({{0.2}, {0.3}, {0.4}, {0.5}}).to(torch::kFloat32),
                               torch::tensor({{0.25}, {0.35}, {0.45}, {0.55}}).to(torch::kFloat32)),
      cv::embeddings::Category(torch::tensor({{11, 12}, {13, 14}, {15, 16}}).to(torch::kFloat32),
                               torch::tensor({{1.2}, {1.3}, {1.4}}).to(torch::kFloat32),
                               torch::tensor({{1.25}, {1.35}, {1.45}}).to(torch::kFloat32)),
      cv::embeddings::Category(torch::tensor({{21, 22}, {23, 24}, {25, 26}, {27, 28}, {29, 30}}).to(torch::kFloat32),
                               torch::tensor({{2.2}, {2.3}, {2.4}, {2.5}, {2.6}}).to(torch::kFloat32),
                               torch::tensor({{2.25}, {2.35}, {2.45}, {2.55}, {2.65}}).to(torch::kFloat32))};

  RandomForestClassifier classifier(cosine_distance);
  classifier.set_category_collection(category_embeddings, {"a", "b", "c"});

  torch::Tensor embeddings = torch::tensor({{1, 2}, {3, 4}, {15, 15}, {16, 16}, {25, 26}}).to(torch::kFloat32);
  torch::Tensor weed_scores = torch::tensor({{0.1}, {1.2}, {2.3}, {0.4}, {2.5}}).to(torch::kFloat32);
  torch::Tensor crop_scores = torch::tensor({{0.2}, {1.25}, {2.35}, {0.45}, {2.55}}).to(torch::kFloat32);
  std::vector<std::string> expected_categories = {"a", "b", "c", "a", "c"};
  torch::Tensor embedding_classes = classifier.classify(embeddings, weed_scores, crop_scores);
  auto categories = classifier.get_categories();
  EXPECT_EQ(embedding_classes.size(0), embeddings.size(0));
  for (int i = 0; i < embedding_classes.size(0); i++) {
    auto classification = torch::argmin(embedding_classes[i]).item<int>();
    EXPECT_LT(classification, categories.size());
    EXPECT_EQ(categories[classification], expected_categories[i]);
  }
}

class test_RandomForestClassifier : public RandomForestClassifier {
public:
  test_RandomForestClassifier(std::function<torch::Tensor(torch::Tensor, torch::Tensor)> distance_function)
      : RandomForestClassifier(distance_function) {}

  torch::Tensor append_dw_information_fn(torch::Tensor category_embeddings, torch::Tensor weed_scores,
                                         torch::Tensor crop_scores, int repeat_dw_information = 100,
                                         bool append_binary = true) {
    return RandomForestClassifier::append_dw_information(category_embeddings, weed_scores, crop_scores,
                                                         repeat_dw_information, append_binary);
  }
  arma::mat tensor_to_arma_mat_fn(torch::Tensor tensor) { return RandomForestClassifier::tensor_to_arma_mat(tensor); }
  torch::Tensor arma_mat_to_tensor_fn(arma::mat &matrix) { return RandomForestClassifier::arma_mat_to_tensor(matrix); }
};

TEST(EmbeddingClassifier, RandomForestAppendDW) {
  std::vector<cv::embeddings::Category> category_embeddings = {
      cv::embeddings::Category(torch::tensor({{1, 2}, {3, 4}, {5, 6}, {9, 10}}).to(torch::kFloat32),
                               torch::tensor({{0.2}, {0.3}, {0.4}, {0.5}}).to(torch::kFloat32),
                               torch::tensor({{0.25}, {0.35}, {0.45}, {0.55}}).to(torch::kFloat32)),
      cv::embeddings::Category(torch::tensor({{11, 12}, {13, 14}, {15, 16}}).to(torch::kFloat32),
                               torch::tensor({{0.2}, {0.3}, {0.4}}).to(torch::kFloat32),
                               torch::tensor({{0.25}, {0.35}, {0.45}}).to(torch::kFloat32)),
      cv::embeddings::Category(torch::tensor({{21, 22}, {23, 24}, {25, 26}, {27, 28}, {29, 30}}).to(torch::kFloat32),
                               torch::tensor({{0.2}, {0.3}, {0.4}, {0.5}, {0.6}}).to(torch::kFloat32),
                               torch::tensor({{0.25}, {0.35}, {0.45}, {0.55}, {0.65}}).to(torch::kFloat32))};

  test_RandomForestClassifier classifier(cosine_distance);
  classifier.set_category_collection(category_embeddings, {"a", "b", "c"});

  torch::Tensor embeddings = torch::tensor({{1, 2}, {3, 4}, {15, 15}, {16, 16}, {25, 26}}).to(torch::kFloat32);
  torch::Tensor weed_score = torch::tensor({{0.4}, {0.4}, {0.5}, {0.6}, {0.7}}).to(torch::kFloat32);
  torch::Tensor crop_score = torch::tensor({{0.35}, {0.45}, {0.55}, {0.65}, {0.75}}).to(torch::kFloat32);
  std::vector<std::string> expected_categories = {"a", "a", "b", "b", "c"};

  auto appended_embeddings = classifier.append_dw_information_fn(embeddings, weed_score, crop_score, 2, false);

  torch::Tensor expected = torch::tensor({{1.0, 2.0, 0.4, 0.35, 0.4, 0.35},
                                          {3.0, 4.0, 0.4, 0.45, 0.4, 0.45},
                                          {15.0, 15.0, 0.5, 0.55, 0.5, 0.55},
                                          {16.0, 16.0, 0.6, 0.65, 0.6, 0.65},
                                          {25.0, 26.0, 0.7, 0.75, 0.7, 0.75}})
                               .to(torch::kFloat32);

  ASSERT_TRUE(torch::equal(appended_embeddings, expected));

  appended_embeddings = classifier.append_dw_information_fn(embeddings, weed_score, crop_score, 2, true);

  expected = torch::tensor({{1.0, 2.0, 0.4, 0.35, 0.0, 0.4, 0.35, 0.0},
                            {3.0, 4.0, 0.4, 0.45, 1.0, 0.4, 0.45, 1.0},
                            {15.0, 15.0, 0.5, 0.55, 1.0, 0.5, 0.55, 1.0},
                            {16.0, 16.0, 0.6, 0.65, 1.0, 0.6, 0.65, 1.0},
                            {25.0, 26.0, 0.7, 0.75, 1.0, 0.7, 0.75, 1.0}})
                 .to(torch::kFloat32);

  ASSERT_TRUE(torch::equal(appended_embeddings, expected));
}

TEST(EmbeddingClassifier, RandomForestArmadillo) {
  auto tensor = torch::tensor({{21, 22}, {23, 24}, {25, 26}, {27, 28}, {29, 30}}).to(torch::kFloat32);

  test_RandomForestClassifier classifier(cosine_distance);
  arma::mat arma_mat = classifier.tensor_to_arma_mat_fn(tensor);

  auto tensor_2 = classifier.arma_mat_to_tensor_fn(arma_mat);

  EXPECT_EQ(tensor.equal(tensor_2), true);
}

#include "random_forest_classifier.h"
#include <iostream>
#include <mlpack.hpp>
#include <spdlog/spdlog.h>
#include <torch/torch.h>

namespace cv::embeddings {

constexpr int kDWRepeatCount = 100;
constexpr bool kUseBinary = true;

RandomForestClassifier::RandomForestClassifier(
    std::function<torch::Tensor(torch::Tensor, torch::Tensor)> distance_function)
    : Classifier(distance_function) {}
RandomForestClassifier::~RandomForestClassifier() {}

void RandomForestClassifier::set_category_collection(std::vector<cv::embeddings::Category> category_embeddings,
                                                     std::vector<std::string> categories) {
  spdlog::info("Setting category collection for RandomForestClassifier");
  std::unique_lock lock(mut_);
  if (category_embeddings.size() == 0) {
    return;
  }
  categories_ = categories;

  // If weed and crop scores passed in, add
  std::vector<torch::Tensor> embeddings;
  for (auto &category : category_embeddings) {
    auto emb = append_dw_information(category.embeddings(), category.weed_scores(), category.crop_scores(),
                                     kDWRepeatCount, kUseBinary);
    embeddings.push_back(emb);
  }

  std::vector<size_t> category_label_vec;
  for (size_t i = 0; i < embeddings.size(); i++) {
    for (size_t j = 0; j < (size_t)embeddings[i].size(0); j++) {
      category_label_vec.push_back(i);
    }
  }

  all_embeddings_ = torch::vstack(embeddings);

  arma::Row<size_t> category_labels(category_label_vec);
  auto train_dataset = tensor_to_arma_mat(all_embeddings_);

  // 100 is number of trees and 1 is min samples per leaf, which match sklearn defaults
  random_forest_classifier_.Train(train_dataset, category_labels, categories.size(), 100, 1);
}

torch::Tensor RandomForestClassifier::append_dw_information(torch::Tensor category_embeddings,
                                                            torch::Tensor weed_scores, torch::Tensor crop_scores,
                                                            int repeat_dw_information, bool append_binary) {

  weed_scores = weed_scores.to(category_embeddings.device());
  crop_scores = crop_scores.to(category_embeddings.device());
  auto dw = torch::hstack({weed_scores, crop_scores});
  if (append_binary) {
    auto binary = crop_scores > weed_scores;
    dw = torch::hstack({dw, binary});
  }

  dw = torch::tile(dw, {1, repeat_dw_information});

  return torch::hstack({category_embeddings, dw});
}

std::vector<std::string> RandomForestClassifier::get_categories() {
  std::shared_lock lock(mut_);
  return categories_;
}

arma::mat RandomForestClassifier::tensor_to_arma_mat(torch::Tensor embeddings) {
  // Armadillo is column major, meaning that instances are saved as columns rather than rows, as we traditionally do in
  // torch Therefore, we need to transpose
  embeddings = embeddings.to(torch::kFloat64).to(torch::kCPU).contiguous().t();
  arma::mat train_dataset(embeddings.data_ptr<double>(), embeddings.size(0), embeddings.size(1), true, true);

  return train_dataset;
}

torch::Tensor RandomForestClassifier::arma_mat_to_tensor(arma::mat &matrix) {
  auto tensor = torch::from_blob(matrix.memptr(), {(int)matrix.n_cols, (int)matrix.n_rows}, torch::kFloat64);
  return tensor.contiguous();
}

torch::Tensor RandomForestClassifier::classify(torch::Tensor embeddings, torch::Tensor weed_scores,
                                               torch::Tensor crop_scores) {
  std::shared_lock lock(mut_);
  if (all_embeddings_.size(0) == 0) {
    return torch::empty({embeddings.size(0), (int64_t)categories_.size()},
                        torch::TensorOptions().device(embeddings.device()).dtype(torch::kFloat32));
  }
  auto device = embeddings.device();
  arma::mat probabilities;
  arma::Row<size_t> predictions;

  auto emb = append_dw_information(embeddings, weed_scores, crop_scores, kDWRepeatCount, kUseBinary);

  random_forest_classifier_.Classify(tensor_to_arma_mat(emb), predictions, probabilities);

  auto probabilities_tensor = arma_mat_to_tensor(probabilities);
  probabilities_tensor = probabilities_tensor.to(device);
  auto distances = torch::subtract(
      torch::ones({probabilities_tensor.size(0), probabilities_tensor.size(1)}).to(device).to(torch::kFloat32),
      probabilities_tensor);
  distances = distances.to(device).to(torch::kFloat32);
  return distances;
}

} // namespace cv::embeddings

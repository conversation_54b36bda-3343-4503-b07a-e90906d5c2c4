#include "cv/embeddings/category.h"

namespace cv::embeddings {

Category::Category(torch::Tensor embeddings, torch::Tensor weed_scores, torch::Tensor crop_scores)
    : embeddings_(embeddings), weed_scores_(weed_scores), crop_scores_(crop_scores) {}

Category Category::to(torch::DeviceType device, int device_id) {
  Category category = *this;
  category.embeddings_ = embeddings_.to({device, device_id});
  category.weed_scores_ = weed_scores_.to({device, device_id});
  category.crop_scores_ = crop_scores_.to({device, device_id});
  return category;
}

} // namespace cv::embeddings

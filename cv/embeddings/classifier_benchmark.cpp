#include "cv/embeddings/category.h"
#include "cv/embeddings/distance_functions.h"
#include "cv/embeddings/nearest_neighbor_classifier.h"
#include <optional>

int main(int, char const *[]) {

  const int categories = 5;
  const int examples = 100;
  const int n_features = 1024;
  const int n_points = 3000;

  auto embeddings = torch::rand({n_points, n_features}).to(torch::kFloat32).cuda();
  std::vector<cv::embeddings::Category> category_embeddings;
  for (int i = 0; i < categories; i++) {
    category_embeddings.push_back(
        cv::embeddings::Category(torch::zeros({examples, n_features}).to(torch::kFloat32).cuda(),
                                 torch::zeros({examples, 1}).to(torch::kFloat32).cuda(),
                                 torch::zeros({examples, 1}).to(torch::kFloat32).cuda()));
  }

  cv::embeddings::NearestNeighborClassifier classifier(cv::embeddings::cosine_distance);
  classifier.set_category_collection(category_embeddings, {"a", "b", "c", "d", "e"});

  std::vector<int64_t> latencies;
  torch::cuda::synchronize();
  for (int i = 0; i < 100; i++) {
    auto begin = std::chrono::system_clock::now();
    classifier.classify(embeddings, torch::ones({1}), torch::ones({1}));
    torch::cuda::synchronize();
    auto end = std::chrono::system_clock::now();
    latencies.push_back(std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());
  }

  float sum = 0;
  for (auto latency : latencies) {
    sum += (float)latency;
  }
  auto mean = sum / (float)latencies.size();

  std::cout << "Mean Latency (ms)" << mean << std::endl;
  return 0;
}

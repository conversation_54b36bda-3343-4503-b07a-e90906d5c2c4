#pragma once

#include <map>
#include <mlpack.hpp>
#include <torch/torch.h>

#include "cv/embeddings/classifier.h"

namespace cv::embeddings {
class RandomForestClassifier : public Classifier {
public:
  /**
   *  Classifier that uses a random forest to redetermine class
   */
  RandomForestClassifier(std::function<torch::Tensor(torch::Tensor, torch::Tensor)> distance_function);
  virtual ~RandomForestClassifier();

  void set_category_collection(std::vector<cv::embeddings::Category> category_embeddings,
                               std::vector<std::string> categories) override;

  /**
   * Classifies the input embeddings
   * @param embeddings BxD tensor where B is the number of items, and D is the embedding dimension
   * @return BxC tensor where B is the number of examples, and C is the number of categories. Contains the distance to
   * each category
   */
  torch::Tensor classify(torch::Tensor embeddings, torch::Tensor weed_scores, torch::Tensor crop_scores) override;
  std::vector<std::string> get_categories() override;

protected:
  arma::mat tensor_to_arma_mat(torch::Tensor tensor);
  torch::Tensor arma_mat_to_tensor(arma::mat &matrix);

  torch::Tensor append_dw_information(torch::Tensor category_embeddings, torch::Tensor weed_scores,
                                      torch::Tensor crop_scores, int repeat_dw_information = 100,
                                      bool append_binary = true);

private:
  torch::Tensor all_embeddings_;
  std::vector<std::string> categories_;

  mlpack::RandomForest<> random_forest_classifier_;
};
} // namespace cv::embeddings

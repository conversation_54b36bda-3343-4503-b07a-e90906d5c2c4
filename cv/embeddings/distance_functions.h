#include "lib/common/cpp/tensor_util.h"
#include <torch/torch.h>

namespace cv::embeddings {

torch::Tensor euclidean_distance(torch::Tensor embeddings, torch::Tensor category_embeddings) {
  return torch::cdist(embeddings, category_embeddings);
}

torch::Tensor cosine_distance(torch::Tensor embeddings, torch::Tensor category_embeddings) {
  auto embeddings_finite = torch::where(torch::isfinite(embeddings), embeddings, 0);
  auto embeddings_norm = embeddings / embeddings_finite.norm(2, 1, true);
  auto category_embeddings_finite = torch::where(torch::isfinite(category_embeddings), category_embeddings, 0);
  auto category_embeddings_norm = category_embeddings / category_embeddings_finite.norm(2, 1, true);
  auto distances = -torch::mm(embeddings_norm, category_embeddings_norm.t());
  return distances;
}

} // namespace cv::embeddings

#pragma once

#include <torch/torch.h>

#include "cv/embeddings/classifier.h"

namespace cv::embeddings {
class NearestNeighborClassifier : public Classifier {
public:
  /**
   *  Classifier that finds the closest embedding for each example and assigns the category of that embedding
   */
  NearestNeighborClassifier(std::function<torch::Tensor(torch::Tensor, torch::Tensor)> distance_function);
  virtual ~NearestNeighborClassifier();

  void set_category_collection(std::vector<cv::embeddings::Category> category_embeddings,
                               std::vector<std::string> categories) override;

  /**
   * Classifies the input embeddings
   * @param embeddings BxD tensor where B is the number of items, and D is the embedding dimension
   * @return BxC tensor where B is the number of examples, and C is the number of categories. Contains the distance to
   * each category
   */
  torch::Tensor classify(torch::Tensor embeddings, torch::Tensor weed_scores, torch::Tensor crop_score) override;
  std::vector<std::string> get_categories() override;

private:
  torch::Tensor category_embeddings_;
  std::vector<std::string> categories_;
  int max_examples_;
};
} // namespace cv::embeddings

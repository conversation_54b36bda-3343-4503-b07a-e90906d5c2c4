add_library(cv_embeddings SHARED nearest_neighbor_classifier.cpp random_forest_classifier.cpp manager.cpp category.cpp)
target_link_libraries(cv_embeddings PUBLIC exceptions torch category_proto opencv_imgcodecs deepweed lib_common_model armadillo config_client_lib)

add_executable(classifier_benchmark classifier_benchmark.cpp)
target_link_libraries(classifier_benchmark cv_embeddings)

add_subdirectory(tests)
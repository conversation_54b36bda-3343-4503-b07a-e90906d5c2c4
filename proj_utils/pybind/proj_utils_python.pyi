from typing import List, Optional, <PERSON>ple

def available_ellipses() -> List[str]: ...

class Geod:
    def __init__(self, ellipse: str) -> None: ...
    def inv(self, lon_start: float, lat_start: float, lon_end: float, lat_end: float) -> Tuple[float, float]: ...
    def direct(self, lon_start: float, lat_start: float, azimuth_deg: float, dist_meters: float) -> Point: ...

class Point:
    def __init__(self, _x: float, _y: float) -> None: ...
    lon: float
    lat: float
    x: float
    y: float

class RelativePosCoord:
    @staticmethod
    def azimuth_to_xy_theta(azimuth_rad: float) -> float: ...
    def __init__(self, start: Optional[Point], ellipse: Optional[str]) -> None: ...
    def set_start(self, start: Point) -> None: ...
    def start_is_valid(self) -> bool: ...
    def geo_to_rel(self, geo_pos: Point, azm_offset_deg: float = 0.0) -> Point: ...

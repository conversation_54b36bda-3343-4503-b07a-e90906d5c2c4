add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)

add_library(proj_utils SHARED ${SOURCES})
target_compile_definitions(proj_utils PRIVATE -DSPDLOG_FMT_EXTERNAL=1 -DUSE_UNSTABLE_GEOS_CPP_API=1)
target_link_libraries(proj_utils PRIVATE m stdc++fs pthread rt fmt proj)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(proj_utils_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(proj_utils_python  PRIVATE -DSPDLOG_FMT_EXTERNAL=1 -DUSE_UNSTABLE_GEOS_CPP_API=1 PYBIND)
target_link_libraries(proj_utils_python PUBLIC proj_utils)
set_target_properties(proj_utils_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)
target_compile_options(proj_utils_python PRIVATE -fvisibility=hidden)
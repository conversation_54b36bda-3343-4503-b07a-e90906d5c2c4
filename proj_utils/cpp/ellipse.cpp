#include "proj_utils/cpp/ellipse.hpp"

#include <cmath>
#include <stdexcept>

#include <fmt/format.h>
#include <proj.h>
#include <spdlog/spdlog.h>

namespace carbon::proj {
Ellipse::Ellipse(const PJ_ELLPS *pj_ellps) {
  std::string major(pj_ellps->major); // c str to string
  std::string ell(pj_ellps->ell);     // c str to string
  auto equal_pos = major.find('=');
  if (equal_pos == std::string::npos) {
    throw std::runtime_error("Invalid ellipse definition");
  }
  semi_major_axis_ = std::stod(major.substr(equal_pos + 1));
  equal_pos = ell.find('=');
  if (equal_pos == std::string::npos) {
    throw std::runtime_error("Invalid ellipse definition");
  }
  ell_key_ = ell.substr(0, equal_pos);
  ell_data_ = std::stod(ell.substr(equal_pos + 1));
}
EllipseData Ellipse::data() const {
  double semi_minor_axis = semi_major_axis_;
  double flattening = 0.0;
  double eccentricity_squared = 0.0;

  if (ell_key_ == "b") {
    semi_minor_axis = ell_data_;
    eccentricity_squared = 1.0 - std::pow(semi_minor_axis, 2) / std::pow(semi_major_axis_, 2);
    flattening = (semi_major_axis_ - semi_minor_axis) / semi_major_axis_;
  } else if (ell_key_ == "rf") {
    flattening = 1.0 / ell_data_;
    semi_minor_axis = semi_major_axis_ * (1.0 - flattening);
    eccentricity_squared = 1.0 - std::pow(semi_minor_axis, 2) / std::pow(semi_major_axis_, 2);
  } else if (ell_key_ == "f") {
    flattening = ell_data_;
    semi_minor_axis = semi_major_axis_ * (1.0 - flattening);
    eccentricity_squared = std::pow(1.0 - (semi_minor_axis / semi_major_axis_), 2);
  } else if (ell_key_ == "es") {
    eccentricity_squared = ell_data_;
    semi_minor_axis = std::sqrt(std::pow(semi_major_axis_, 2) - eccentricity_squared * std::pow(semi_major_axis_, 2));
    flattening = (semi_major_axis_ - semi_minor_axis) / semi_major_axis_;
  } else if (ell_key_ == "e") {
    eccentricity_squared = std::pow(ell_data_, 2);
    semi_minor_axis = std::sqrt(std::pow(semi_major_axis_, 2) - eccentricity_squared * std::pow(semi_major_axis_, 2));
    flattening = (semi_major_axis_ - semi_minor_axis) / semi_major_axis_;
  }
  return {semi_major_axis_, semi_minor_axis, flattening, eccentricity_squared};
}

std::unordered_map<std::string, Ellipse> build_list_ellipse() {
  std::unordered_map<std::string, Ellipse> tmp;
  const PJ_ELLPS *proj_ellps = proj_list_ellps();
  size_t i = 0;
  while (proj_ellps[i].id != nullptr) {
    tmp.emplace(proj_ellps[i].id, &proj_ellps[i]);
    ++i;
  }
  return tmp;
}
const std::unordered_map<std::string, Ellipse> &list_ellipse() {
  static std::unordered_map<std::string, Ellipse> ellipse_map(build_list_ellipse());
  return ellipse_map;
}
} // namespace carbon::proj
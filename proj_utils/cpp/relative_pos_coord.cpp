#include "proj_utils/cpp/relative_pos_coord.hpp"

#include <geodesic.h>

constexpr std::string_view default_ellipse("WGS84");
inline double deg2rad(const double &degrees) { return degrees * M_PI / 180.0; }
namespace carbon::proj {
RelativePosCoord::RelativePosCoord(std::optional<Point> start, std::optional<std::string> ellipse)
    : start_(start), geod_((ellipse ? *ellipse : std::string(default_ellipse))) {}
Point RelativePosCoord::geo_to_rel(const Point &geo_pos, const double &azm_offset_deg) {
  auto [azimuth_deg, dist_m] = geod_.inv((start_ ? *start_ : Point()), geo_pos);

  auto theta = azimuth_to_xy_theta(deg2rad(azimuth_deg - azm_offset_deg));
  return Point(std::cos(theta) * dist_m, std::sin(theta) * dist_m);
}
} // namespace carbon::proj

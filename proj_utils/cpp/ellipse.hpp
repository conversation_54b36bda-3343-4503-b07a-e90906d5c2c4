#pragma once

#include <string>
#include <unordered_map>

struct PJ_ELLPS;

namespace carbon::proj {
struct EllipseData {
  double semi_major_axis;
  double semi_minor_axis;
  double flattening;
  double eccentricity_squared;
};
class Ellipse {
public:
  Ellipse(const PJ_ELLPS *pj_ellps);
  EllipseData data() const;

private:
  double semi_major_axis_;
  double ell_data_;
  std::string ell_key_;
};
const std::unordered_map<std::string, Ellipse> &list_ellipse();
} // namespace carbon::proj
#include "proj_utils/cpp/geod.hpp"

#include <exception>

#include <proj_utils/cpp/ellipse.hpp>

#include <fmt/format.h>
#include <geodesic.h>
#include <spdlog/spdlog.h>
namespace carbon::proj {
Geod::Geod(const std::string &ellipse) : geod_(std::make_unique<geod_geodesic>()) {
  auto &avail = list_ellipse();
  auto it = avail.find(ellipse);
  if (it == avail.end()) {
    throw std::runtime_error(fmt::format("Unknown ellipse name {}", ellipse));
  }
  auto data = it->second.data();
  geod_init(geod_.get(), data.semi_major_axis, data.flattening);
}
std::tuple<double, double> Geod::inv(const double &lon_start, const double &lat_start, const double &lon_end,
                                     const double &lat_end) {
  double dist_start_end;
  double azm_start_end;
  geod_inverse(geod_.get(), lat_start, lon_start, lat_end, lon_end, &dist_start_end, &azm_start_end, nullptr);
  return std::make_tuple(azm_start_end, dist_start_end);
}
Point Geod::direct(const double &lon_start, const double &lat_start, const double &azimuth_deg,
                   const double &dist_meters) {
  double lat, lon;
  geod_direct(geod_.get(), lat_start, lon_start, azimuth_deg, dist_meters, &lat, &lon, nullptr);
  return Point(lon, lat);
}
} // namespace carbon::proj
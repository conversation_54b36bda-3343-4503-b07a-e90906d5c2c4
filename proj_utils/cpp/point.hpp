#pragma once

#include <cmath>

namespace carbon::proj {
struct Point {
public:
  Point(const double &_x, const double &_y) : x_(_x), y_(_y) {}
  Point() : Point(0.0, 0.0) {}
  Point(const Point &rhs) : x_(rhs.x_), y_(rhs.y_) {}
  Point &operator=(const Point &other) {
    x_ = other.x_;
    y_ = other.y_;
    return *this;
  }
  inline double lon() const { return x_; }
  inline double lat() const { return y_; }
  inline double x() const { return x_; }
  inline double y() const { return y_; }
  inline void set_x(const double &x) { x_ = x; }
  inline void set_y(const double &y) { y_ = y; }
  inline void set_lon(const double &lon) { x_ = lon; }
  inline void set_lat(const double &lat) { y_ = lat; }

  inline bool valid_geo() const { return std::abs(x_) > 0.5 && std::abs(y_) > 0.5; }

private:
  double x_;
  double y_;
};

} // namespace carbon::proj
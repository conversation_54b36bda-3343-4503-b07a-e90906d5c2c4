import argparse
import asyncio
import sys
from contextlib import AbstractAsyncContextManager, AsyncExitStack
from typing import Any, List

from config.client.cpp.config_client_python import ConfigTree, get_computer_config_prefix, get_global_config_subscriber
from jobber.constants import SERVICE_NAME
from jobber.location_streamer import LocationStreamer
from jobber.tractor_ctl_state import CONTROL_STATE_SYNC
from lib.cloud.auth import AuthConf, TokenStore
from lib.common.asyncio.task_master import TaskMaster
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.generation import is_rtc
from lib.common.logging import get_logger, init_log
from lib.common.redis_client import wait_for_redis
from lib.common.role import is_simulator
from lib.common.tasks.manager import get_event_loop_by_name
from lib.rtc.farm_manager import FarmSyncer
from lib.rtc.tractor_pose import init_pose

LOG = get_logger(SERVICE_NAME)


class Jobber(TaskMaster):
    def __init__(self, tree: ConfigTree, stop_event: asyncio.Event) -> None:
        super().__init__()
        self._tree = tree
        self._loop = asyncio.get_event_loop()
        self._stop_event = stop_event
        self._cm: List[AbstractAsyncContextManager[Any]] = []
        self._ts = TokenStore(AuthConf())

    async def init(self) -> None:
        await init_pose()
        self._cm.append(await LocationStreamer.build(self._ts))
        self._cm.append(
            await FarmSyncer.build(
                self._tree.get_node("active_farm_id"), CONTROL_STATE_SYNC.tractor_ctl_client, self._ts
            )
        )

    async def run(self) -> None:
        await self.add_task(await CONTROL_STATE_SYNC.start(self._stop_event))
        async with AsyncExitStack() as stack:
            for cm in self._cm:
                await stack.enter_async_context(cm)
            await self._stop_event.wait()
        await self.stop()


async def main() -> None:
    async def inner() -> None:
        parser = argparse.ArgumentParser(
            description="Service to talk to cloud for jobs, vstop, and time_series location data"
        )
        _ = parser.parse_args()
        if not is_rtc() and not is_simulator():
            LOG.error("Tractor ctl only runs on RTC generation devices, or simulators emulating rtc")
            return

        config_subscriber = get_global_config_subscriber()
        if is_rtc():
            config_subscriber.add_config_tree(
                SERVICE_NAME, f"{get_computer_config_prefix()}/{SERVICE_NAME}", f"services/{SERVICE_NAME}.yaml"
            )
            config_subscriber.add_config_tree("common", "common", "services/rtc_common.yaml")
        else:
            LOG.info("Non RTC hardware pretending to be RTC")
            config_subscriber.add_config_tree(SERVICE_NAME, f"rtc/{SERVICE_NAME}", f"services/{SERVICE_NAME}.yaml")
            config_subscriber.add_config_tree("common", "common", "services/common.yaml")
        config_subscriber.start()
        await asyncio.get_event_loop().run_in_executor(None, lambda: config_subscriber.wait_until_ready())
        service_tree = config_subscriber.get_config_node(SERVICE_NAME, "")

        stop_event = await bot_stop_handler.get_stop_event()
        controller = Jobber(service_tree, stop_event)
        await controller.init()
        LOG.info("serving ...")
        await controller.run()

    await wait_for_redis()
    try:
        await inner()
    except Exception as ex:
        bot_stop_handler.exit_with_exception(ex)


if __name__ == "__main__":
    init_log(level="INFO", logfile=f"{SERVICE_NAME}.log")
    asyncio.run_coroutine_threadsafe(main(), get_event_loop_by_name())
    bot_stop_handler.ready_for_termination_event.wait()
    sys.exit(bot_stop_handler.error_code)

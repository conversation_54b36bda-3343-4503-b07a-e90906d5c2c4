import asyncio
from contextlib import AbstractAsync<PERSON><PERSON>xt<PERSON>ana<PERSON>, AsyncExitStack, asynccontextmanager
from dataclasses import dataclass, field
from math import modf
from time import time
from types import TracebackType
from typing import TYPE_CHECKING, AsyncIterator, List, Optional, Type
from uuid import uuid4

import aioredis
import grpc
from google.protobuf import empty_pb2, timestamp_pb2

from config.client.cpp.config_client_python import get_global_config_subscriber
from generated.proto.geo import geo_pb2
from generated.proto.rtc import location_history_pb2, location_history_pb2_grpc
from hardware_manager.python.client import HardwareManagerClient
from hardware_manager.python.client import Position as HWPos
from jobber.constants import SERVICE_NAME
from jobber.objective_syncer import ObjectiveSyncer
from jobber.tractor_ctl_state import CONTROL_STATE_SYNC
from lib.cloud.auth import TokenStore
from lib.cloud.grpc import GrpcConf, get_channel
from lib.cloud.services import RTC_LOCATOR_GRPC_HOST, RTC_LOCATOR_GRPC_PORT
from lib.common.asyncio.cancellable_await import cancellable_await
from lib.common.config.accessor import ConfigAccessor
from lib.common.environment import robot_name
from lib.common.logging import get_logger
from lib.common.redis_client import RedisClient
from lib.common.units.angle import Angle
from lib.drivers.nanopb.benjamin_gps_board.benjamin_gps_board_connector import CarrierSolution, FixType
from lib.rtc.jobs.constants import ACTIVE_TASK_KEY
from lib.rtc.tractor_pose import TRACTOR_POSE
from lib.rtc.tractor_pose import Position as PosePos

LOG = get_logger("location_streamer")

if TYPE_CHECKING:
    ACM = AbstractAsyncContextManager["LocationStreamer"]
    TASK_TYPE = asyncio.Task[None]
else:
    ACM = AbstractAsyncContextManager
    TASK_TYPE = asyncio.Task


PENDING_RECORDS_KEY = "/location/pending"


@dataclass
class Record:
    data: Optional[location_history_pb2.LocationHistoryRecord]
    id: str = field(default_factory=lambda: str(uuid4()))


def _make_proto_ts() -> timestamp_pb2.Timestamp:
    now = modf(time())
    return timestamp_pb2.Timestamp(seconds=int(now[1]), nanos=int(now[0] * 1e9))


def _fixed_pos(pos: HWPos) -> PosePos:
    antenna_pos = PosePos(x=pos.longitude, y=pos.latitude)
    if pos.dual_gps_data is None or pos.dual_gps_data.heading is None:
        return antenna_pos
    return TRACTOR_POSE.get_fixed_point(antenna_pos, Angle.from_degrees(pos.dual_gps_data.heading.value))


class LocationStreamer(ACM):
    def __init__(self, redis: aioredis.Redis, ts: TokenStore, conf: Optional[GrpcConf] = None) -> None:
        super().__init__()
        self._redis = redis
        self._ts = ts
        if conf is None:
            self._conf = GrpcConf(RTC_LOCATOR_GRPC_HOST, RTC_LOCATOR_GRPC_PORT)
        else:
            self._conf = conf

        self._stop_event = asyncio.Event()
        self._tasks: List[TASK_TYPE] = []
        self._send_queue: asyncio.Queue[Record] = asyncio.Queue()
        self._confirm_queue: asyncio.Queue[str] = asyncio.Queue()
        service_tree = get_global_config_subscriber().get_config_node(SERVICE_NAME, "")
        self._hitch_down_pos = ConfigAccessor(float, service_tree.get_node("hitch_down_val"))
        self._max_imp_state_age = ConfigAccessor(float, service_tree.get_node("max_trusted_imp_state_age"))
        self._use_imp_state = ConfigAccessor(bool, service_tree.get_node("use_imp_state"))
        self._objective_syncer = ObjectiveSyncer(self._ts)
        self._ctx_stack = AsyncExitStack()

    @staticmethod
    async def build(ts: TokenStore, conf: Optional[GrpcConf] = None) -> "LocationStreamer":
        redis = await RedisClient.build(False)
        return LocationStreamer(redis, ts, conf)

    async def _make_data(self) -> location_history_pb2.RobotData:
        data = location_history_pb2.RobotData()
        if self._use_imp_state.value:
            if (
                CONTROL_STATE_SYNC.control_state.implement is not None
                and CONTROL_STATE_SYNC.control_state.implement.active is not None
            ):
                if (time() - CONTROL_STATE_SYNC.control_state.implement.last_update) < self._max_imp_state_age.value:
                    data.active = CONTROL_STATE_SYNC.control_state.implement.active
        elif CONTROL_STATE_SYNC.control_state.hitch_state is not None:
            data.active = CONTROL_STATE_SYNC.control_state.hitch_state.lift_percent <= self._hitch_down_pos.value
        data.objective_id = self._objective_syncer.active_objective
        active_task = await self._redis.get(ACTIVE_TASK_KEY)
        if active_task is not None:
            data.task_id = int(active_task)
        return data

    async def _record_generator(self) -> None:
        hc = HardwareManagerClient()
        while not self._stop_event.is_set():
            try:
                pos = await hc.next_raw_gps(0)
                fix_type = geo_pb2.FIX_TYPE_UNSPECIFIED
                if pos.fix_flags.carr_soln == CarrierSolution.FIXED_SOLUTION:
                    fix_type = geo_pb2.RTK_FIXED
                elif pos.fix_flags.carr_soln == CarrierSolution.FLOATING_SOLUTION:
                    fix_type = geo_pb2.RTK_FLOAT
                elif pos.fix_type == FixType.NO_FIX:
                    fix_type = geo_pb2.NO_FIX
                elif pos.fix_type == FixType.GNSS_DR:
                    fix_type = geo_pb2.GNSS
                elif pos.fix_type == FixType.DEAD_RECKONING_ONLY:
                    fix_type = geo_pb2.DEAD_RECKONING
                drive_pos = _fixed_pos(pos)
                # TODO add code here to fill rest of message out
                r = Record(
                    location_history_pb2.LocationHistoryRecord(
                        point=geo_pb2.Point(
                            lng=drive_pos.longitude,
                            lat=drive_pos.latitude,
                            alt=pos.height_mm / 1000,
                            capture_info=geo_pb2.CaptureInfo(fix_type=fix_type),
                        ),
                        timestamp=_make_proto_ts(),
                        heading_degrees=pos.dual_gps_data.heading.value
                        if pos.dual_gps_data is not None and pos.dual_gps_data.heading is not None
                        else None,
                        data=await self._make_data(),
                    )
                )
                assert r.data is not None
                await self._redis.hset(PENDING_RECORDS_KEY, r.id, r.data.SerializeToString())
                self._send_queue.put_nowait(r)
                await asyncio.sleep(1)
            except Exception as ex:
                LOG.error(f"Unknown exception: {ex}")

    @asynccontextmanager
    async def _location_stream(
        self,
    ) -> AsyncIterator[grpc.aio.StreamUnaryCall[location_history_pb2.LocationHistoryRecord, empty_pb2.Empty]]:
        token = await self._ts.fetch()
        async with get_channel(self._conf, token) as chan:
            stub = location_history_pb2_grpc.LocationHistoryStub(chan)
            stream: grpc.aio.StreamUnaryCall[
                location_history_pb2.LocationHistoryRecord, empty_pb2.Empty
            ] = stub.StreamLocation(metadata=(("robot", robot_name()),))
            try:
                yield stream
            finally:
                try:
                    await stream.done_writing()
                except Exception:
                    LOG.exception("failed to send done")

    async def _to_server(self) -> None:
        while not self._stop_event.is_set():
            try:
                async with self._location_stream() as stream:
                    while not self._stop_event.is_set():
                        record = await self._send_queue.get()
                        if record.data is None:
                            continue
                        assert record.data is not None
                        await stream.write(record.data)
                        self._confirm_queue.put_nowait(record.id)
            except Exception as ex:
                LOG.error(f"Failed to send location record to service: {ex}")

    async def _record_remover(self) -> None:
        while not self._stop_event.is_set():
            try:
                rec_id = await cancellable_await(self._confirm_queue.get(), self._stop_event)
                if rec_id is None:
                    continue
                await self._redis.hdel(PENDING_RECORDS_KEY, rec_id)
            except Exception as ex:
                LOG.error(f"Unknown exception: {ex}")

    async def _backfill(self) -> None:
        age = 30
        while not self._stop_event.is_set():
            try:
                await cancellable_await(asyncio.sleep(age), self._stop_event)
                pending = await self._redis.hgetall(PENDING_RECORDS_KEY)
                now = time()
                for id, encoded in pending.items():
                    msg = location_history_pb2.LocationHistoryRecord()
                    msg.ParseFromString(encoded)
                    if now - msg.timestamp.seconds > age:
                        await self._send_queue.put(Record(data=msg, id=id))
            except Exception as ex:
                LOG.error(f"Unknown exception: {ex}")

    async def __aenter__(self) -> "LocationStreamer":
        self._tasks.append(asyncio.get_event_loop().create_task(self._record_generator()))
        self._tasks.append(asyncio.get_event_loop().create_task(self._record_remover()))
        self._tasks.append(asyncio.get_event_loop().create_task(self._to_server()))
        self._tasks.append(asyncio.get_event_loop().create_task(self._backfill()))
        await self._ctx_stack.enter_async_context(self._objective_syncer)
        return self

    async def __aexit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        self._stop_event.set()
        await asyncio.gather(*self._tasks)
        await self._ctx_stack.aclose()

from abc import ABC, abstractmethod
from typing import Any, Dict, Union

from lib.common.serialization.json import JsonSerializable

ParameterValueType = Union[str, float]


class Parameter(JsonSerializable, ABC):
    def __init__(self, name: str, type: str, description: str = ""):
        super().__init__()
        self._name = name
        self._type = type
        self._description = description

    @property
    def name(self) -> str:
        return self._name

    @property
    def type(self) -> str:
        return self._type

    @property
    def description(self) -> str:
        return self._description

    @abstractmethod
    def set_value(self, v: ParameterValueType) -> None:
        pass

    @property
    @abstractmethod
    def value(self) -> ParameterValueType:
        pass

    @property
    def int_value(self) -> int:
        v = self.value
        assert isinstance(v, int), f"Not an int: {v}"
        return v

    @property
    def float_value(self) -> float:
        v = self.value
        assert isinstance(v, (int, float)), f"Not a float: {v}"
        return v

    @property
    def str_value(self) -> str:
        return str(self.value)


class NumericParameter(Parameter):
    @property
    @abstractmethod
    def min(self) -> float:
        pass

    @property
    @abstractmethod
    def max(self) -> float:
        pass

    @property
    @abstractmethod
    def step(self) -> float:
        pass

    def to_json(self) -> Dict[str, ParameterValueType]:
        return {
            "name": self.name,
            "description": self.description,
            "type": self.type,
            "value": self.value,
            "min": self.min,
            "max": self.max,
            "step": self.step,
        }


class IntParameter(NumericParameter):
    def __init__(self, name: str, value: int, min: int, max: int, step: int, description: str = ""):
        super().__init__(name=name, type="number", description=description)
        self._min = min
        self._max = max
        self._step = step

        self._value = self._validate(value)

    def _validate(self, v: ParameterValueType) -> int:
        if isinstance(v, float) and v == int(v):
            v = int(v)
        assert isinstance(v, int), f"Wrong value type: {type(v)} for value: {v}"
        assert self._min <= v <= self._max, f"Invalid value {v} outside range [{self._min}, {self._max}]"
        return v

    def set_value(self, v: ParameterValueType) -> None:
        self._value = self._validate(v)

    @property
    def min(self) -> int:
        return self._min

    @property
    def max(self) -> int:
        return self._max

    @property
    def step(self) -> int:
        return self._step

    @property
    def value(self) -> int:
        return self._value

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "IntParameter":
        assert data["type"] == "number"

        return IntParameter(name=data["name"], value=data["value"], min=data["min"], max=data["max"], step=data["step"])


class FloatParameter(NumericParameter):
    def __init__(self, name: str, value: float, min: float, max: float, step: float, description: str = ""):
        super().__init__(name=name, type="number", description=description)
        self._min = min
        self._max = max
        self._step = step

        self._value = self._validate(value)

    def _validate(self, v: ParameterValueType) -> float:
        assert isinstance(v, (int, float)), f"Wrong value type: {type(v)} for value: {v}. Expected: {float}"
        assert self._min <= v <= self._max, f"Invalid value {v} outside range [{self._min}, {self._max}]"
        return v

    def set_value(self, v: ParameterValueType) -> None:
        self._value = self._validate(v)

    @property
    def value(self) -> float:
        return self._value

    @property
    def min(self) -> float:
        return self._min

    @property
    def max(self) -> float:
        return self._max

    @property
    def step(self) -> float:
        return self._step

    def to_json(self) -> Dict[str, ParameterValueType]:
        return {
            "name": self.name,
            "description": self.description,
            "type": self.type,
            "value": self.value,
            "min": self.min,
            "max": self.max,
            "step": self.step,
        }

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "FloatParameter":
        assert data["type"] == "number"

        return FloatParameter(
            name=data["name"], value=data["value"], min=data["min"], max=data["max"], step=data["step"]
        )

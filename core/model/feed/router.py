from typing import Any, Dict, Optional, Union

from lib.common.protocol.channel.async_channel import AsyncSubscriberChannel
from lib.common.protocol.channel.base import SubscriberChannel, Topic
from lib.common.protocol.feed.base import Feed
from lib.common.serialization.json import JsonSerializable
from lib.common.web.http.router import Component<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RouterBuilder
from lib.common.web.http.router_utils import merge_add

JsonSerializableType = Optional[Union[str, float, int, Dict[str, Any]]]


def _try_serialize(v: Optional[Any]) -> JsonSerializableType:
    result: JsonSerializableType
    if v is None:
        result = v
    elif isinstance(v, (str, float, int)):
        result = v
    elif isinstance(v, JsonSerializable):
        result = v.to_json()
    elif len(str(v)) <= 100:
        result = str(v)
    else:
        result = "<unserializable>"
    return result


class FeedRouterBuilder(ComponentRouterBuilder):
    def __init__(self, feed: Feed):
        super().__init__(base_route="/feed")
        self._feed: Feed = feed

    def _read_channel(self, sub: Union[AsyncSubscriberChannel[object], SubscriberChannel[object]]) -> Dict[str, Any]:
        result: Dict[str, Any]
        if isinstance(sub, AsyncSubscriberChannel):
            result = {"channel": {"topic": sub.topic, "type": "async"}, "read": "<non-async-context>"}
        else:
            result = {"channel": {"topic": sub.topic, "type": "sync"}, "read": _try_serialize(sub.read())}
        return result

    def _handle_read_all(self, bot: Any) -> Dict[str, Dict[str, Any]]:
        result: Dict[str, Dict[str, Any]] = {}
        for t in self._feed.topics:
            result[t] = self._read_channel(self._feed.subscribe_either(topic=t, msg_type=object))
        return result

    def _handle_read(self, bot: Any, topic: str) -> Dict[str, Any]:
        return self._read_channel(self._feed.subscribe_either(topic=Topic(topic), msg_type=object))

    def add_onto(self, bot_routes: RouterBuilder) -> None:
        bot_routes.add("GET", self.suburl("/topics"))(merge_add(lambda **_: self._feed.topics))
        bot_routes.add("GET", self.suburl("/read"))(merge_add(self._handle_read_all))
        bot_routes.add("GET", self.suburl("/read(?P<topic>.+)"))(merge_add(self._handle_read))

# from enum import Enum
#
# from core.model.node.type import NodeType
# from lib.common.protocol.channel.base import Topic
#
# class BaseTopic(Topic, Enum):
#     COMMANDER = Topic(f"/{NodeType.COMMANDER.serialize()}")
#     DRIVER = Topic(f"/{NodeType.DRIVER.serialize()}")
#     EXTERMINATOR = Topic(f"/{NodeType.EXTERMINATOR.serialize()}")
#     FEED = Topic("/feed")
#     FRAME = Topic(f"/{NodeType.FRAME.serialize()}")
#     RETINA = Topic(f"/{NodeType.RETINA.serialize()}")
#     VISUAL_CORTEX = Topic(f"/{NodeType.VISUAL_CORTEX.serialize()}")
#
# # validate
# for n in NodeType.roots():
#     assert f"/{n.serialize()}" in [str(t) for t in BaseTopic], f"Missing BaseTopic: /{n.serialize()}"

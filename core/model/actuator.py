from abc import ABC
from typing import List

from core.model.node.base import NodeAccessPolicy
from core.model.node.classic import ClassicNode


class Actuator(ClassicNode, ABC):
    """
    An actuator is a device that is responsible for moving and controlling a
    mechanism or system. In simple terms, it is a "mover".


    From https://en.wikipedia.org/wiki/Actuator:
        An actuator requires a control signal and a source of energy. The
        control signal is relatively low energy and may be electric voltage or
        current, pneumatic or hydraulic pressure, or even human power. Its main
        energy source may be an electric current, hydraulic fluid pressure, or
        pneumatic pressure. When it receives a control signal, an actuator
        responds by converting the signal's energy into mechanical motion.

        An actuator is the mechanism by which a control system acts upon an
        environment. [...]

        [....]

        Types of actuator
          * Hydraulic
          * Pneumatic
          * Electric
          * Twisted and coiled polymer (TCP) or supercoiled polymer
          * Thermal or magnetic
          * Mechanical

        [....]

        In engineering, actuators are frequently used as mechanisms to
        introduce motion, or to clamp an object so as to prevent motion. In
        electronic engineering, actuators are a subdivision of transducers.
        They are devices which transform an input signal (mainly an electrical
        signal) into some form of motion.

        [....]

        Performance metrics for actuators include speed, acceleration, and
        force (alternatively, angular speed, angular acceleration, and torque),
        as well as energy efficiency and considerations such as mass, volume,
        operating conditions, and durability, among others.


    From Probabilistic Robotics (2006), page 4:
        Robot actuation involves motors that are, at least to some extent,
        unpredictable. Uncertainty arises from effects like control noise,
        wear-and-tear, and mechanical failure. Some actuators, such as
        heavy-duty industrial robot arms, are accurate and reliable. Others,
        like low-cost mobile robots, can be extremely flaky.
    """

    @property
    def access_policy(self) -> NodeAccessPolicy:
        return NodeAccessPolicy.PRIVATE

    # TODO: This is a stopgap until we have a more declarative mechanism to built/init the tree
    #  and tell the UI about our links
    def get_cameras_for_ui(self) -> List[str]:
        return []

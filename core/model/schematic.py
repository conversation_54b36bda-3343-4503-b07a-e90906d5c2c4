from typing import Any, List

from core.model.node.type import NodeType
from core.model.path import CameraInstance, DeviceExpression, DevicePath, Instance, RelativeInstance, ServoInstance

_ROBOT_DEVICE_PATH = DevicePath.robot()


_DRIVER_CHILDREN = [
    DeviceExpression(NodeType.DRIVE_SYSTEM),
    DeviceExpression(NodeType.LED),
]


_DRIVE_SYSTEM_CHILDREN_HYDRAULICS = [
    DeviceExpression(NodeType.ECU),
    DeviceExpression(NodeType.HYDRAULICS),
    DeviceExpression(NodeType.ROTARY_ENCODER, instance=RelativeInstance.BACK_LEFT),
    DeviceExpression(NodeType.ROTARY_ENCODER, instance=RelativeInstance.BACK_RIGHT),
    DeviceExpression(NodeType.ROTARY_ENCODER, instance=RelativeInstance.FRONT_LEFT),
    DeviceExpression(NodeType.ROTARY_ENCODER, instance=RelativeInstance.FRONT_RIGHT),
    DeviceExpression(NodeType.PARKING_BRAKE),
]

_HYDRAULICS_CHILDREN = [
    DeviceExpression(NodeType.DRIVE_MOTOR, instance=RelativeInstance.BACK_LEFT),
    DeviceExpression(NodeType.DRIVE_MOTOR, instance=RelativeInstance.BACK_RIGHT),
    DeviceExpression(NodeType.DRIVE_MOTOR, instance=RelativeInstance.FRONT_LEFT),
    DeviceExpression(NodeType.DRIVE_MOTOR, instance=RelativeInstance.FRONT_RIGHT),
    DeviceExpression(NodeType.ROTARY, instance=RelativeInstance.FRONT_LEFT),
    DeviceExpression(NodeType.ROTARY, instance=RelativeInstance.FRONT_RIGHT),
]


_DRIVE_SYSTEM_CHILDREN_MOTORS = [
    DeviceExpression(NodeType.DRIVE_MOTOR, instance=RelativeInstance.BACK_LEFT),
    DeviceExpression(NodeType.DRIVE_MOTOR, instance=RelativeInstance.BACK_RIGHT),
]


_FRAME_CHILDREN = [
    DeviceExpression(NodeType.GPS),
    DeviceExpression(NodeType.IMU),
    DeviceExpression(NodeType.INS),
    DeviceExpression(NodeType.FUEL_SENSOR),
]


_RETINA_CHILDREN = [
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.PREDICT + str(1))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.PREDICT + str(2))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.PREDICT + str(3))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.PREDICT + str(4))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.TARGET + str(1))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.TARGET + str(2))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.TARGET + str(3))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.TARGET + str(4))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.TARGET + str(5))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.TARGET + str(6))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.TARGET + str(7))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.TARGET + str(8))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.TARGET + str(9))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.TARGET + str(10))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.HUBBLE + str(1))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.BIRDSEYE + str(1))),
    DeviceExpression(NodeType.CAMERA, instance=RelativeInstance.BACK_LEFT),
    DeviceExpression(NodeType.CAMERA, instance=RelativeInstance.BACK_RIGHT),
    DeviceExpression(NodeType.CAMERA, instance=RelativeInstance.FRONT_LEFT),
    DeviceExpression(NodeType.CAMERA, instance=RelativeInstance.FRONT_RIGHT),
    DeviceExpression(NodeType.LENS, instance=Instance(CameraInstance.TARGET + str(1))),
    DeviceExpression(NodeType.LENS, instance=Instance(CameraInstance.TARGET + str(2))),
    DeviceExpression(NodeType.LENS, instance=Instance(CameraInstance.TARGET + str(3))),
    DeviceExpression(NodeType.LENS, instance=Instance(CameraInstance.TARGET + str(4))),
    DeviceExpression(NodeType.LENS, instance=Instance(CameraInstance.TARGET + str(5))),
    DeviceExpression(NodeType.LENS, instance=Instance(CameraInstance.TARGET + str(6))),
    DeviceExpression(NodeType.LENS, instance=Instance(CameraInstance.TARGET + str(7))),
    DeviceExpression(NodeType.LENS, instance=Instance(CameraInstance.TARGET + str(8))),
    DeviceExpression(NodeType.LENS, instance=Instance(CameraInstance.TARGET + str(9))),
    DeviceExpression(NodeType.LENS, instance=Instance(CameraInstance.TARGET + str(10))),
    DeviceExpression(NodeType.CAMERA, instance=Instance(CameraInstance.BIRDSEYE + str(1))),
    DeviceExpression(NodeType.LENS, instance=Instance(CameraInstance.HUBBLE + str(1))),
]


_GIMBAL_CHILDREN = [
    DeviceExpression(NodeType.SERVO, instance=ServoInstance.PAN),
    DeviceExpression(NodeType.SERVO, instance=ServoInstance.TILT),
]

_SCANNER_CHILDREN = [
    DeviceExpression(NodeType.GIMBAL),
    DeviceExpression(NodeType.LASER),
]

_ROW_MODULE_CHILDREN = [
    DeviceExpression(NodeType.SCANNER, instance=Instance(1)),
    DeviceExpression(NodeType.SCANNER, instance=Instance(2)),
    DeviceExpression(NodeType.SCANNER, instance=Instance(3)),
    DeviceExpression(NodeType.SCANNER, instance=Instance(4)),
    DeviceExpression(NodeType.SCANNER, instance=Instance(5)),
    DeviceExpression(NodeType.SCANNER, instance=Instance(6)),
]

_EXTERMINATOR_CHILDREN = [
    DeviceExpression(NodeType.ROW_MODULE, instance=Instance(1)),
    DeviceExpression(NodeType.ROW_MODULE, instance=Instance(2)),
]


class SubTreePaths:
    @staticmethod
    def expand(device_path: DevicePath, **kwargs: Any) -> List[DevicePath]:
        assert device_path is not None

        result: List[DevicePath] = []
        std_subtree: List[DeviceExpression] = SubTreePaths.get_children(device_path.type, **kwargs)
        for sub_expression in std_subtree:
            subpath: DevicePath = device_path.join(sub_expression)

            # Base case
            if subpath.type.is_leaf or subpath.type.is_dynamic_internal:
                result.append(subpath)

            if not subpath.type.is_leaf or subpath.type.is_dynamic_internal:
                # recursive case, need to get subtree
                child_tree: List[DevicePath] = SubTreePaths.expand(subpath, **kwargs)
                result.extend(child_tree)

        return result

    @staticmethod
    def get_children(node_type: NodeType, hydraulics: bool = False) -> List[DeviceExpression]:
        # TODO right now the default parameter for hydraulics controls which one gets booted
        # Need to connect this with boot.args, but it breaks previous assumptions that there is only
        # one way to unpack the /driver subtree. So need to give some thought to the right abstraction.
        # For now, I can just change this locally when I need.
        assert node_type is not None

        if node_type == NodeType.EXTERMINATOR:
            return _EXTERMINATOR_CHILDREN
        elif node_type == NodeType.ROW_MODULE:
            return _ROW_MODULE_CHILDREN
        elif node_type == NodeType.DRIVER:
            return _DRIVER_CHILDREN
        elif node_type == NodeType.DRIVE_SYSTEM:
            # inelegant
            if hydraulics:
                return _DRIVE_SYSTEM_CHILDREN_HYDRAULICS
            else:
                return _DRIVE_SYSTEM_CHILDREN_MOTORS
        elif node_type == NodeType.FRAME:
            return _FRAME_CHILDREN
        elif node_type == NodeType.GIMBAL:
            return _GIMBAL_CHILDREN
        elif node_type == NodeType.RETINA:
            return _RETINA_CHILDREN
        elif node_type == NodeType.SCANNER:
            return _SCANNER_CHILDREN
        elif node_type == NodeType.HYDRAULICS:
            return _HYDRAULICS_CHILDREN
        else:
            assert False, "Unexpected node_type: {}".format(node_type)


class Schematic:
    # inelegant
    DRIVER_HYDRAULIC = SubTreePaths.expand(DevicePath.parse(f"/{NodeType.DRIVER}"), hydraulics=True)
    DRIVER_MOTORS = SubTreePaths.expand(DevicePath.parse(f"/{NodeType.DRIVER}"), hydraulics=False)
    EXTERMINATOR = SubTreePaths.expand(DevicePath.parse(f"/{NodeType.EXTERMINATOR}"))
    FRAME = SubTreePaths.expand(DevicePath.parse(f"/{NodeType.FRAME}"))
    RETINA = SubTreePaths.expand(DevicePath.parse(f"/{NodeType.RETINA}"))
    WHITELIST = list(sorted(set(EXTERMINATOR + DRIVER_HYDRAULIC + DRIVER_MOTORS + FRAME + RETINA)))

from aenum import AutoNumberEnum


class NodeDepthClass(AutoNumberEnum):
    """
    Classification of the node's depth:

    root: /
    internal: anything that is not the root and not a leaf
    leaf: nodes with nothing under them in the namespace
    """

    # The base robot
    ROOT: "NodeDepthClass" = ()  # type: ignore

    # internal node which links to other node
    INTERNAL: "NodeDepthClass" = ()  # type: ignore

    # leaf node
    LEAF: "NodeDepthClass" = ()  # type: ignore

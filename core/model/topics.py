from enum import Enum

from lib.common.protocol.channel.base import Topic


# TODO - work in progress. Lots of inconsistencies at the moment:
# - some places we use raw strings
# - some places we use topic.sub("raw_string")
# - and some places we use this below Enum
#
# TODO we can dynamically generate enums from a file or list of strings or custom metaclass
# Helpful links found:
# - https://stackoverflow.com/a/13603392/2079993
# - https://stackoverflow.com/a/33690233/2079993
class Topics(str, Enum):
    GEOFENCE = "/driver/geofence"
    LOCATION_STATE = "/driver/location_state"
    VELOCITY_ERROR_MPH = "/driver/error_velocity_mph"
    HEADING = "/frame/heading"
    GEOPOSITION_LLA = "/frame/geoposition_lla"
    GEOPOSITION_ECEF = "/frame/geoposition_ecef"
    BODY_POSITION = "/frame/body"
    HEADING_DISTANCE = "/frame/heading_distance"
    ROTARY_ENCODERS_SNAPSHOT = "/frame/rotary_encoder_ticks"
    VELOCITY_MPH = "/frame/velocity_body_mph"
    ACCEL_MPH = "/frame/accel_body_mph"
    GPS_HISTORY_VECTOR = "/frame/gps_spaced_history"
    FUEL_LVL = "/frame/fuel_lvl"
    DRIVE_CAM_MECH_CFG_BACK_LEFT = "/frame/mechanical/drive_cam/back_left"
    DRIVE_CAM_MECH_CFG_BACK_RIGHT = "/frame/mechanical/drive_cam/back_right"
    DRIVE_CAM_MECH_CFG_FRONT_LEFT = "/frame/mechanical/drive_cam/front_left"
    DRIVE_CAM_MECH_CFG_FRONT_RIGHT = "/frame/mechanical/drive_cam/front_right"
    FURROWS_BACK_LEFT = "/visual_cortex/furrows_back_left"
    FURROWS_BACK_RIGHT = "/visual_cortex/furrows_back_right"
    FURROWS_FRONT_LEFT = "/visual_cortex/furrows_front_left"
    FURROWS_FRONT_RIGHT = "/visual_cortex/furrows_front_right"
    OPTICAL_FLOW = "/visual_cortex/optical_flow"

    @property
    def topic(self) -> Topic:
        return Topic(str(self))

    def __str__(self) -> str:
        return str(self.value)

from typing import Iterable, List, cast

from aenum import AutoNumberEnum

from core.model.depth_class import NodeDepthClass


class NodeType(AutoNumberEnum):
    """
    Enumeration of different types of nodes
    """

    # Leaf Nodes

    CAMERA: "NodeType" = cast("NodeType", ())
    DRIVE_MOTOR: "NodeType" = cast("NodeType", ())
    ECU = cast("NodeType", ())
    HYDRAULICS: "NodeType" = cast("NodeType", ())
    GPS: "NodeType" = cast("NodeType", ())
    IMU: "NodeType" = cast("NodeType", ())
    INS: "NodeType" = cast("NodeType", ())
    LASER: "NodeType" = cast("NodeType", ())
    LED: "NodeType" = cast("NodeType", ())
    LENS: "NodeType" = cast("NodeType", ())
    PARKING_BRAKE: "NodeType" = cast("NodeType", ())
    FUEL_SENSOR: "NodeType" = cast("NodeType", ())
    SERVO: "NodeType" = cast("NodeType", ())
    # rotary actuator
    ROTARY: "NodeType" = cast("NodeType", ())
    # rotary encoder
    ROTARY_ENCODER: "NodeType" = cast("NodeType", ())

    # Algorithms

    COMMANDER: "NodeType" = cast("NodeType", ())
    VISUAL_CORTEX: "NodeType" = cast("NodeType", ())

    # Actuators

    # drive system, CV, LEDs
    DRIVER: "NodeType" = cast("NodeType", ())
    # drive motors, steering
    DRIVE_SYSTEM: "NodeType" = cast("NodeType", ())
    # mechanical frame / reference frame
    FRAME: "NodeType" = cast("NodeType", ())
    # servo pair
    GIMBAL: "NodeType" = cast("NodeType", ())
    # cameras
    RETINA: "NodeType" = cast("NodeType", ())
    # gimbal, laser, target camera
    SCANNER: "NodeType" = cast("NodeType", ())
    # Scanners, Predict Cameras
    ROW_MODULE: "NodeType" = cast("NodeType", ())
    # Row Modules, Safety Guard
    EXTERMINATOR: "NodeType" = cast("NodeType", ())

    # Root Node

    # bot
    ROBOT: "NodeType" = cast("NodeType", ())

    def __str__(self) -> str:
        """
        Override default string formatting to just print the name. Not meant to be deserialized.
        """
        return str(self.name)

    @staticmethod
    def leaves() -> List["NodeType"]:
        return [
            NodeType.CAMERA,
            NodeType.DRIVE_MOTOR,
            NodeType.ECU,
            NodeType.GPS,
            NodeType.IMU,
            NodeType.INS,
            NodeType.LASER,
            NodeType.LED,
            NodeType.LENS,
            NodeType.SERVO,
            NodeType.ROTARY,
            NodeType.ROTARY_ENCODER,
            NodeType.PARKING_BRAKE,
            NodeType.FUEL_SENSOR,
        ]

    @staticmethod
    def roots() -> List["NodeType"]:
        """
        The list of "top-level" nodes.
        """
        return [
            NodeType.COMMANDER,
            NodeType.DRIVER,
            NodeType.EXTERMINATOR,
            NodeType.FRAME,
            NodeType.RETINA,
            NodeType.VISUAL_CORTEX,
        ]

    @staticmethod
    def dynamic_internal() -> List["NodeType"]:
        return [NodeType.HYDRAULICS, NodeType.GIMBAL]

    @staticmethod
    def internal_nodes() -> List["NodeType"]:
        return [t for t in cast(Iterable[NodeType], NodeType) if not t.is_leaf and t != NodeType.ROBOT]

    @staticmethod
    def nonleaves() -> List["NodeType"]:
        return [t for t in cast(Iterable[NodeType], NodeType) if not t.is_leaf]

    @property
    def is_leaf(self) -> bool:
        return self in NodeType.leaves()

    @property
    def is_dynamic_internal(self) -> bool:
        return self in NodeType.dynamic_internal()

    @property
    def depth_class(self) -> NodeDepthClass:
        if self == NodeType.ROBOT:
            return NodeDepthClass.ROOT
        elif self.is_leaf or self.is_dynamic_internal:
            return NodeDepthClass.LEAF
        else:
            return NodeDepthClass.INTERNAL

    def serialize(self) -> str:
        """
        Serialization meant for grepping and programmatic usage.
        """
        return str(self).lower()

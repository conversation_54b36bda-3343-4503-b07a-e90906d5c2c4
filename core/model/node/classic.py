import abc
import asyncio
import re
import threading
from typing import Any, Dict, List, Optional, Union, cast

import lib.common.logging
import lib.common.tasks
from core.fs.filesystem import FileSystem
from core.model.id import ReferenceId
from core.model.node.base import Node
from core.model.node.status import Status
from core.model.node.type import NodeType
from core.model.path import DeviceExpression, DevicePath, Instance
from core.web.status import status_reporter
from lib.common.annotate import Annotator
from lib.common.protocol.feed.base import Feed
from lib.common.tasks import MakaTask

LOG = lib.common.logging.get_logger(__name__)


class ClassicNode(Node, abc.ABC):
    """
    Base class for managed Bot nodes.

    A Bot node has:
      * id
      * start/stop/join API
      * started boolean state
      * status callback

    A Bot node may have:
      * control loop / corresponding MakaTask

    A Bot node should be:
      * singleton
      * started only once
    """

    def __init__(
        self,
        *,
        # WARNING: Please do not add further dependencies. There exists a migration plan for this monolithic code.
        device_path: DevicePath,
        filesystem: FileSystem,
        feed: Feed,
        ref_id: Optional[ReferenceId] = None,
        subtree: Optional[Dict[DevicePath, "ClassicNode"]] = None,
        subscriptions: Optional[Dict[DevicePath, "ClassicNode"]] = None,
    ):

        ######################################################################################################
        # Namespace
        ######################################################################################################
        assert device_path is not None, "No device path given. Ref id given: {}".format(ref_id)
        self._device_path: DevicePath = device_path

        self._id: ReferenceId = ref_id if ref_id is not None else self._init_id()  # must init device_path first

        ######################################################################################################
        # Resources
        ######################################################################################################
        self._feed: Feed = feed
        self._filesystem: FileSystem = filesystem

        ######################################################################################################
        # References and links to other nodes
        ######################################################################################################
        subtree = subtree if subtree is not None else {}
        assert self.device_path not in subtree, "Bad reference to own path: {} in subtree".format(self.device_path)
        for child_device_path, child in subtree.items():
            assert child is not None, "Bad subtree[{}]: {}".format(child_device_path, child)

        # Must copy or else it could get modified elsewhere, thereby breaking downstream recursion
        # public, encouraged to be used with bracket syntax. Good for lookup.
        # empty subtree => leaf
        self._subtree: Dict[DevicePath, ClassicNode] = subtree.copy() if subtree is not None else {}
        # private subset of subtree, specifically only children. Good for iteration.
        self._children: Dict[DevicePath, ClassicNode] = {}
        # public, encouraged to be used with bracket syntax. Good for lookup.
        self._pool: Dict[ReferenceId, ClassicNode] = {}
        # private subset of pool, for only leaves. Good for iteration.
        self._leaves: Dict[ReferenceId, ClassicNode] = {}
        self._parse_subtree()
        # managed subscriptions
        self._subscriptions: Dict[DevicePath, ClassicNode] = subscriptions.copy() if subscriptions is not None else {}

        self._cache_named_references()

        ######################################################################################################
        # Control state - locks, started/cancelled/etc.
        ######################################################################################################
        self._task: Optional[MakaTask] = None
        # For use by child class when starting
        self._control_lock = threading.Lock()
        # For use by child class when starting
        self._control_started = False
        self._control_cancelled = False
        self._control_joined = False

        ######################################################################################################
        # Annotations
        ######################################################################################################
        self._annotator: Optional[Annotator] = None

        ######################################################################################################
        # Subtasks to shut down on stop
        ######################################################################################################
        self._subtasks: List[MakaTask] = []
        self._paused: bool = False

        # Sanity Check in case we forgot to pass args and mypy didn't catch it
        assert self._feed is not None
        assert self._filesystem is not None

        self.feed.publish_callback(topic=self.topic.sub("/status"), callback=lambda: self.status())

    @property
    def feed(self) -> Feed:
        return self._feed

    @property
    def fs(self) -> FileSystem:
        return self._filesystem

    @property
    def id(self) -> ReferenceId:
        return self._id

    @property
    def device_path(self) -> DevicePath:
        return self._device_path

    @property
    def subtree(self) -> Dict[DevicePath, "ClassicNode"]:
        """
        clients encouraged to use bracket syntax to lookup references
        """
        return self._subtree

    def children(self) -> List[DevicePath]:
        """
        function so that clients can easily iterate over children device paths if they desire.
        """
        return sorted(iter(self._children))

    @property
    def pool(self) -> Dict[ReferenceId, "ClassicNode"]:
        """
        clients encouraged to use bracket syntax to lookup references
        """
        return self._pool

    def leaves(self) -> List[ReferenceId]:
        """
        function so that clients can easily iterate over leaf reference ids if they desire.
        """
        return sorted(iter(self._leaves))

    @property
    def subscriptions(self) -> Dict[DevicePath, "ClassicNode"]:
        """
        clients encouraged to use bracket syntax to lookup references
        """
        return self._subscriptions

    @property
    def leaf(self) -> bool:
        """
        Whether we are a leaf node
        """
        return len(self.subtree) == 0

    @property
    def task(self) -> Optional[MakaTask]:
        return self._task

    @property
    def annotator(self) -> Optional[Annotator]:
        return self._annotator

    @property
    def paused(self) -> bool:
        return self._paused

    def __str__(self) -> str:
        return str(self.device_path)

    def _init_id(self) -> ReferenceId:
        # default implementation just takes the leaf name or uses the robot name
        # if this is not unique (e.g. gimbal), it should be overridden by the subclass
        return ReferenceId(self.device_path.leaf()) if self.device_path != DevicePath.robot() else ReferenceId.robot()

    def _parse_subtree(self) -> None:
        has_grandchild: bool = False
        for device_path, reference in sorted(self.subtree.items()):
            assert (
                device_path.depth > self.device_path.depth
            ), "Bad subtree entry: {} must have greater depth than: {}".format(device_path, self.device_path)

            # always add the reference to the pool
            self.pool[reference.id] = reference

            # check if it is a child
            child_depth: int = self.device_path.depth + 1
            if device_path.depth == child_depth:
                self._children[device_path] = reference

            has_grandchild = has_grandchild or device_path.depth > child_depth

            # check if it is a leaf (==> its a Device)
            # TODO write better leaf functions
            if len(reference.subtree) == 0:
                self._leaves[reference.id] = reference

        assert len(self.subtree) >= len(self.children())
        if has_grandchild:
            assert len(self.subtree) > len(self.children())
        assert len(self.pool) >= len(self.leaves())

    def _cache_named_references(self) -> None:
        """
        Child should override this to cache references to devices as desired,
        e.g. gimbal caching _pan and _tilt or scanner caching _gimbal, _laser, and _tcam
        """
        pass

    def get_node(
        self, node_type: NodeType, instance: Optional[Instance] = None, required: bool = False
    ) -> Optional["ClassicNode"]:
        """
        Lookup the unique node in the tree, by type/instance.
        """
        pattern: str = f"{DeviceExpression(node_type, instance)}$"
        # cast valid because we are passing unique=True
        result: Optional[ClassicNode] = cast(
            Optional[ClassicNode], self.grep_subtree(pattern, unique=True, required=False)
        )
        if result is None:
            # cast valid because we are passing unique=True
            result = cast(Optional[ClassicNode], self.grep_subscriptions(pattern, unique=True, required=False))
        assert not required or result is not None, f"Could not find {node_type}"
        return result

    def get_nodes(
        self, node_type: NodeType, instance: Optional[Instance] = None, required: bool = False
    ) -> Optional[Dict[DevicePath, "ClassicNode"]]:
        pattern: str = str(DeviceExpression(node_type, instance))
        result: Optional[Dict[DevicePath, ClassicNode]]
        # cast valid because we are passing unique=False
        result = cast(Optional[Dict[DevicePath, ClassicNode]], self.grep_subtree(pattern, unique=False, required=False))
        if result is None:
            # cast valid because we are passing unique=False
            result = cast(
                Optional[Dict[DevicePath, ClassicNode]], self.grep_subscriptions(pattern, unique=False, required=False)
            )
        assert not required or result is not None, f"Could not find {node_type}"
        return result

    def grep_subscriptions(
        self, pattern: Union[str, NodeType, NodeType], unique: bool = False, required: bool = False
    ) -> Optional[Union["ClassicNode", Dict[DevicePath, "ClassicNode"]]]:
        """
        Refer grep_subtree, but for subscriptions.
        """
        return ClassicNode._grep(pattern, self.subscriptions, unique=unique, required=required)

    def grep_subtree(
        self, pattern: Union[str, NodeType, NodeType], unique: bool = False, required: bool = False
    ) -> Optional[Union["ClassicNode", Dict[DevicePath, "ClassicNode"]]]:
        """
        Greps the subtree of fully specified device paths based on the given pattern.
        If first is given, the first reference alphabetically is returned, or None if none found.

        If unique is specified, an error is thrown if multiple are found. It is ok if it is missing.
        """
        return ClassicNode._grep(pattern, self.subtree, unique=unique, required=required)

    @staticmethod
    def _grep(
        pattern: Union[str, NodeType, NodeType],
        paths: Dict[DevicePath, "ClassicNode"],
        unique: bool = False,
        required: bool = False,
    ) -> Optional[Union["ClassicNode", Dict[DevicePath, "ClassicNode"]]]:
        assert pattern is not None
        if isinstance(pattern, NodeType) or isinstance(pattern, NodeType):
            # another way to do this would be to support a type= parameter and make pattern= be optional?
            pattern = pattern.serialize()
        # O(n) try pattern on every path
        search: Dict[DevicePath, ClassicNode] = {k: v for k, v in paths.items() if re.search(pattern, str(k))}

        result: Optional[Union[ClassicNode, Dict[DevicePath, ClassicNode]]] = search
        if required:
            assert len(search) > 0, "Expected device path grep match for pattern: {}".format(pattern)
        if unique:
            if len(search) == 0:
                result = None
            elif len(search) == 1:
                result = search[next(iter(search))]
            else:
                assert not unique, 'Expected unique result for pattern "{}" but found: {}'.format(pattern, search)
                result = search[next(iter(sorted(search)))]

        return result

    def _control_loop(self) -> None:
        """
        Subclass should override this method with the infinite thread loop.
        """
        pass

    def _start_task(self) -> Optional[MakaTask]:
        """
        Start the object control loop in a task, if it is overridden, else do nothing.

        # TODO: is there a better way to express objects with control loops and those without?
        # We might want to model control loops more explicitly
        """
        # only spawn if overridden
        if self.__class__._control_loop != ClassicNode._control_loop:
            return lib.common.tasks.start(name=f"{self.device_path}/control_loop", target=self._control_loop)
        else:
            return None

    def setup_async(self, loop: asyncio.BaseEventLoop) -> None:
        """
        Sets up co-routines to be run on the async thread (specified as loop)
        """
        pass

    def shutdown_async(self) -> None:
        pass

    def start(self) -> None:
        """
        Gracefully start this device.
        """
        with self._control_lock:
            if not self._control_started:
                for child_path in self.children():
                    try:
                        self.subtree[child_path].start()
                    except Exception:
                        LOG.exception("{} Start Failed! Uh-Oh!!!".format(child_path))
                        LOG.error("{} Attempting safe shutdown after start failure".format(self.device_path))
                        self.stop()
                        raise

                self._task = self._start_task()
                if self._task is not None:
                    self.add_subtask(self._task)

                status_reporter().register_device_tree_cb(self.device_path, self.status_callback)

            self._control_started = True

    def serve_forever(self, block: bool, http_port: int) -> None:
        LOG.warning(
            f"{self.device_path} Unexpected call to serve_forever(). This Node implementation does not override serve_forever() which was built as a stop-gap for process-separated Duro"
        )

    def pause(self) -> None:
        self._paused = True
        for task in self._subtasks:
            task.pause()

    def resume(self) -> None:
        for task in self._subtasks:
            task.unpause()
        self._paused = False

    def add_subtask(self, task: MakaTask) -> MakaTask:
        self._subtasks.append(task)
        return task

    def stop(self) -> None:
        """
        Gracefully stop the subtree under this device.
        """
        with self._control_lock:
            if not self._control_cancelled:
                # shutdown self task
                LOG.debug("{} Stop!".format(self.id))

                self.shutdown_async()

                if self._task is not None:
                    lib.common.tasks.cancel(self._task.id)
                while len(self._subtasks) > 0:
                    task = self._subtasks.pop()
                    lib.common.tasks.cancel(task.id)

                for child_path in self.children():
                    LOG.debug("TRYING for {}".format(child_path))
                    try:
                        self.subtree[child_path].stop()
                    except Exception:
                        # swallow exception to ensure we signal all children to stop
                        LOG.exception("{} Stop Failed! Uh-Oh!!!".format(child_path))

                self._control_cancelled = True

    def wait(self, *, timeout_ms: Optional[int] = None) -> None:
        """
        Wait until the underlying task is complete. Must have called stop() first.
        """
        with self._control_lock:
            if not self._control_joined:
                assert self._control_cancelled, "{} Must call stop() before join()".format(self._id)
                if self._task is not None and not self._task.wait_until_complete(timeout_ms=timeout_ms):
                    LOG.critical("{} DID NOT CANCEL!!!".format(self._id))
                else:
                    self._control_joined = True

    def dump_callback(self) -> Dict[str, Any]:
        return {"status": self.status_callback()}

    def status_callback(self) -> Dict[str, Any]:
        return {}

    def status(self) -> Status:
        return Status(self.status_callback())

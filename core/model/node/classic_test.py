from typing import Generator, Optional

import pytest

import lib.common.tasks
from core.fs.filesystem import TemporaryFileSystem
from core.model.id import ReferenceId
from core.model.node.base import NodeAccessPolicy
from core.model.node.classic import ClassicNode
from core.model.path import DevicePath
from lib.common.protocol.feed.inproc import Inproc<PERSON><PERSON>
from lib.common.tasks import MakaTask
from lib.common.time.sleep import sleep_ms


# The only way to test a class designed for inheritance is to inherit from that class and test it
class FakeClassicNode(ClassicNode):  # don't use "Test" in name
    @property
    def access_policy(self) -> NodeAccessPolicy:
        return NodeAccessPolicy.PRIVATE

    def start(self) -> None:
        self._control_started = True

    def _control_loop(self) -> None:
        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None
        while True:
            task.tick()
            sleep_ms(10)


@pytest.fixture
def obj() -> Generator[FakeClassicNode, None, None]:
    yield FakeClassicNode(
        filesystem=TemporaryFileSystem(),
        feed=InprocFeed(),
        ref_id=ReferenceId("testId"),
        device_path=DevicePath.parse("/"),
    )


def test_start_stop_join_sequence(obj: FakeClassicNode) -> None:
    assert not obj._control_started
    assert not obj._control_cancelled
    assert not obj._control_joined

    obj.start()
    assert obj._control_started
    assert not obj._control_cancelled
    assert not obj._control_joined

    obj.stop()
    assert obj._control_started
    assert obj._control_cancelled
    assert not obj._control_joined

    obj.wait(timeout_ms=50)
    assert obj._control_started
    assert obj._control_cancelled
    assert obj._control_joined


def test_join_before_stop(obj: FakeClassicNode) -> None:
    assert not obj._control_started
    assert not obj._control_cancelled
    assert not obj._control_joined
    obj.start()
    assert obj._control_started
    assert not obj._control_cancelled
    assert not obj._control_joined

    with pytest.raises(AssertionError, match=r"Must call stop\(\) before join\(\)"):
        obj.wait(timeout_ms=0)

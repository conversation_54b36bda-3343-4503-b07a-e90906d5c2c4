from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict

from core.fs.filesystem import FileSystem
from core.model.id import ReferenceId
from core.model.path import DevicePath
from lib.common.protocol.channel.base import Topic
from lib.common.protocol.feed.base import Feed


class NodeAccessPolicy(Enum):
    """
    The scope of a Node informs how its object reference should be shared.

    This could expand out to capture more Access Control policies in the future.

    For now an enum is fine.
    """

    PRIVATE = 1
    PUBLIC = 2


class Node(ABC):
    """
    A Node manages concepts such as:
      * resource availability / interaction with FileSystem
      * scheduling of control routines including start/stop
      * namespacing
    """

    #########################################################
    # State
    #########################################################
    @abstractmethod
    def dump_callback(self) -> Dict[str, Any]:
        """
        Overridable method to provide callback to web UI about all state, static or dynamic.

        This should include a "status" field equaling the latest status callback.
        """
        pass

    @abstractmethod
    def status_callback(self) -> Dict[str, Any]:
        """
        Overridable method to provide callback to web UI about current status information.
        """
        pass

    #########################################################
    # Resources
    #########################################################
    @property
    @abstractmethod
    def fs(self) -> FileSystem:
        pass

    #########################################################
    # Namespace
    #########################################################
    @property
    @abstractmethod
    def access_policy(self) -> NodeAccessPolicy:
        pass

    @property
    @abstractmethod
    def id(self) -> ReferenceId:
        pass

    # TODO don't have two namespaces that are isomorphic?
    @property
    @abstractmethod
    def device_path(self) -> DevicePath:
        pass

    @property
    def topic(self) -> Topic:
        return self.device_path.topic

    @property
    @abstractmethod
    def feed(self) -> Feed:
        pass

    #########################################################
    # Procedural Control
    #########################################################
    @abstractmethod
    def start(self) -> None:
        pass

    @abstractmethod
    def stop(self) -> None:
        pass

    # TODO this should collapse into start()
    def serve_forever(self, block: bool, http_port: int) -> None:
        pass

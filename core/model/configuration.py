from typing import Dict, Generic, Optional, TypeVar

from core.model.parameter import FloatParameter, IntParameter, Parameter
from lib.common.config_file import ConfigDictType, ConfigFile
from lib.common.logging import get_logger

LOG = get_logger(__name__)

ConfigFileType = TypeVar("ConfigFileType", bound=ConfigFile)

ParametersType = TypeVar("ParametersType", bound=Dict[str, Parameter])


class ServiceConfigurationException(Exception):
    pass


class ServiceConfiguration(Generic[ConfigFileType, ParametersType]):
    def __init__(self, cfg_file: ConfigFileType, parameters: ParametersType):
        self._cfg_file = cfg_file
        self._parameters = parameters

        self._initial_configuration: ConfigDictType = self._cfg_file.load()

        if "parameters" in self._initial_configuration and self._initial_configuration["parameters"] is not None:
            for k, v in self._initial_configuration["parameters"].items():

                if k not in self._parameters:
                    raise ServiceConfigurationException(
                        f"Unknown parameter: {k}={v} found in {self._cfg_file.filepath}"
                    )

                parameter = self._parameters[k]
                before_value = parameter.value

                if isinstance(parameter, FloatParameter):
                    override_value = float(v)

                elif isinstance(parameter, IntParameter):
                    as_float = float(v)
                    override_value = int(as_float)
                    if as_float != override_value:
                        raise ServiceConfigurationException(f"Expected int but got: {v}")
                else:
                    raise ServiceConfigurationException(
                        f"Unimplemented parameter type: {type(parameter)} : {parameter}"
                    )

                # set
                if before_value != override_value:
                    LOG.info(f"[{self._cfg_file.filepath}] Override {k}: {before_value} -> {override_value}")
                parameter.set_value(override_value)

        self._saved_configuration = self._initial_configuration

        self._load_initial_configuration()

    def _load_initial_configuration(self) -> None:
        pass

    @property
    def parameters(self) -> ParametersType:
        return self._parameters

    def save_parameter(self, parameter: str) -> None:
        as_parameter: Optional[Parameter] = self.parameters.get(parameter)
        if as_parameter is None:
            raise ServiceConfigurationException(f"Invalid parameter: {parameter}")

        LOG.info(f"Saving parameter {parameter} = {as_parameter.value}")

        config_to_save = self._saved_configuration.copy()
        parameters_to_save = config_to_save.get("parameters")
        if parameters_to_save is None:
            parameters_to_save = {}
            config_to_save["parameters"] = parameters_to_save
        parameters_to_save[parameter] = as_parameter.value

        self._cfg_file.save(config_to_save)
        self._saved_configuration = config_to_save
        LOG.info(f"Saved parameter {parameter} = {as_parameter.value} to {self._cfg_file.filepath}")

from abc import ABC
from typing import Any, List, Optional, Union

import numpy as np

import lib.common.logging
from core.model.node.base import NodeAccessPolicy
from core.model.node.classic import ClassicNode
from lib.common.recipes import eval_2d_matrix_strs

# import some common math functions that could be used in frame rotation definitions.
from math import cos, pi, sin  # noqa  # isort:skip

LOG = lib.common.logging.get_logger(__name__)


RotationType = List[List[Union[int, float, str]]]


class Sensor(ClassicNode, ABC):
    """
    A sensor is a device whose purpose is to detect events or changes in its
    environment.


    From https://en.wikipedia.org/wiki/Sensor:
        A sensor's sensitivity indicates how much the sensor's output changes
        when the input quantity being measured changes. For instance, if the
        mercury in a thermometer moves 1 cm when the temperature changes by
        1 °C, the sensitivity is 1 cm/°C (it is basically the slope dy/dx
        assuming a linear characteristic). Some sensors can also affect what
        they measure; for instance, a room temperature thermometer inserted
        into a hot cup of liquid cools the liquid while the liquid heats the
        thermometer. Sensors are usually designed to have a small effect on
        what is measured; making the sensor smaller often improves this and
        may introduce other advantages.

        [....]

        A good sensor obeys the following rules:
          * it is sensitive to the measured property
          * it is insensitive to any other property likely to be encountered in
              its application, and
          * it does not influence the measured property.

        Most sensors have a linear transfer function. The sensitivity is then
        defined as the ratio between the output signal and measured property.
        For example, if a sensor measures temperature and has a voltage output,
        the sensitivity is a constant with the units [V/K]. The sensitivity is
        the slope of the transfer function.


    From Probabilistic Robotics (2006), page 3:
        Sensors are limited in what they can perceive. Limitations arise from
        several factors. The range and resolution of a sensor is subject to
        physical limitations. For example, cameras cannot see through walls,
        and the spatial resolution of a camera image is limited. Sensors are
        also subject to noise, which perturbs sensor measurements in
        unpredictable ways and hence limits the information that can be
        extracted. And finally, sensors can break. Detecting a faulty sensor
        can be extremely difficult.
    """

    # Most sensors we have are 3D, so we expect by default their sensor dimensions to be 3x1 column
    # vectors.
    N_DIMS = 3
    DEFAULT_SENSOR_DIMENSIONS = (N_DIMS, 1)

    def __init__(self, *argv: Any, device_to_body_rotation: Optional[RotationType] = None, **kwargs: Any):
        """
        Constructor.

        Parameters:
            argv (list): Positional arguments, passed onto the Node constructor.
            device_to_body_rotation (np.ndarray): An optional rotation matrix that defines how to
                                                  transform the sensor readings into readings that
                                                  are aligned with the body of the robot. Can be
                                                  None if the idea of rotation for the given sensor
                                                  doesn't make sense.
            kwargs (dict): Keyword arguments, passed onto the Node constructor.
        """
        super().__init__(*argv, **kwargs)

        # Represents a rotation matrix that can be applied to transform the sensor from its local
        # body frame into the more useful robot body frame. This concept doesn't always make sense
        # for sensors (e.g. temperature sensors don't need to be rotated) so this rotation is left
        # as an optional type and only applies if a `device_to_body_rotation` is given.
        # Each sensor device is then free to make use of this rotation matrix as it chooses/knows
        # best.
        # TODO: Consider factoring this out of the sensor class into something more specific for
        # dimensional data, however it's not clear at this time if this should only apply to 3DOF
        # sensors. Some specific considerations are the Duro which has accel/gyro in the same
        # inertial frame - it's unclear if we always want to use the same 3x3 rotation for both
        # accel and gyro or if it makes sense to have them be separate (in which case, is it a 6x6
        # rotation matrix or do we split the sensors up to be 3x3?). Also consider 2DOF sensors.
        self._rotation: Optional[np.ndarray] = None

        if device_to_body_rotation is not None:
            read_rotation_matrix = np.array(device_to_body_rotation)

            # Assume all sensors that have a rotation matrix defined are 3D sensors, in which case
            # they need a 3x3 rotation matrix.
            assert read_rotation_matrix.shape == (Sensor.N_DIMS, Sensor.N_DIMS)
            self._rotation = eval_2d_matrix_strs(device_to_body_rotation)
            assert self._rotation.shape == (Sensor.N_DIMS, Sensor.N_DIMS)
            LOG.debug(f"{self.device_path} Parsed rotation matrix as: {self._rotation.tolist()}")

        for path, obj in self.subtree.items():
            assert isinstance(obj, Sensor), "Unexpected type: {}".format(type(obj))

    @property
    def access_policy(self) -> NodeAccessPolicy:
        return NodeAccessPolicy.PUBLIC

    async def tick(self) -> None:
        """Optional tick based update for sensors that need to run asyncio to get updates"""
        pass

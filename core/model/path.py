from typing import List, Optional, Tuple, Union, cast

from core.model.node.type import NodeType
from lib.common.logging import get_logger
from lib.common.namespace.base import Path
from lib.common.protocol.channel.base import Topic

LOG = get_logger(__name__)


class Instance(str):
    """
    The relative instance (aka role or position) of a device or device tree,
    relative to its neighbor devices or device trees with the same parent device path and same device [tree] type

    Examples:
        /scanner          -> [0...N-1]
        /scanner:*/camera -> [target]
        /driver/led       -> [fiducial, moving]
        /scanner:*/led    -> [armed, firing]
    """

    @staticmethod
    def prefix() -> str:
        """
        prefix separator for complex serialization
        """
        return ":"


class CameraInstance:
    BIRDSEYE = Instance("birdseye")
    DRIVE = Instance("drive")
    PREDICT = Instance("predict")
    TARGET = Instance("target")
    HUBBLE = Instance("hubble")


class RelativeInstance:
    # 1D
    LEFT = Instance("left")
    RIGHT = Instance("right")

    # 2D
    # quadrants
    BACK_LEFT = Instance("back_left")
    BACK_RIGHT = Instance("back_right")
    FRONT_LEFT = Instance("front_left")
    FRONT_RIGHT = Instance("front_right")

    @staticmethod
    def quadrants() -> "List[Instance]":
        return [
            RelativeInstance.BACK_LEFT,
            RelativeInstance.FRONT_LEFT,
            RelativeInstance.FRONT_RIGHT,
            RelativeInstance.BACK_RIGHT,
        ]


class ServoInstance:
    PAN = Instance("pan")
    TILT = Instance("tilt")

    @staticmethod
    def both() -> Tuple["Instance", "Instance"]:
        return ServoInstance.PAN, ServoInstance.TILT


class DeviceExpression:
    """
    Wrapper for (DeviceType, DeviceInstance) with serialization support.
    """

    def __init__(self, type_: NodeType, instance: Optional[Instance] = None):
        # Required
        assert type_ is not None
        self._type: NodeType = type_

        # If none, then the null/canonical role is assumed (e.g. a given scanner's laser is unique)
        self._instance: Optional[Instance] = instance

    def __str__(self) -> str:
        return self.type.serialize() + (Instance.prefix() + self.instance.lower() if self.instance is not None else "")

    @property
    def instance(self) -> Optional[Instance]:
        return self._instance

    @property
    def type(self) -> NodeType:
        return self._type

    @staticmethod
    def parse(s: str) -> "DeviceExpression":
        tokens: List[str] = s.split(Instance.prefix())
        assert 1 <= len(tokens) <= 2, f"Bad expression: {s}"
        type_upper: str = tokens[0].upper()
        type_: NodeType
        if type_upper in NodeType.__members__:
            # mypy still doesn't understand AutoNumberEnum is indexable
            type_ = cast(NodeType, NodeType[type_upper])  # type: ignore
        elif type_upper in NodeType.__members__:
            # mypy still doesn't understand AutoNumberEnum is indexable
            type_ = cast(NodeType, NodeType[type_upper])  # type: ignore
        else:
            assert False, f"Unexpected type_: {type_upper} parsed from: {s}"
        instance: Optional[Instance] = Instance(tokens[1]) if len(tokens) > 1 else None
        return DeviceExpression(type_=type_, instance=instance)

    def __repr__(self) -> str:
        return str(self)


class DevicePath(Path):
    """
    A device path is a path in a device tree, represented as a possibly empty sequence of DeviceExpressions.

    For example,

    /
    /driver
    /driver/drive_system/drive_motor:left
    /driver/drive_system/drive_motor:right
    /driver/led:fiducial
    /exterminator/scanner:1/gimbal/servo:pan
    /exterminator/scanner:1/gimbal/servo:tilt
    /exterminator/scanner:1/laser
    /frame/imu
    /frame/ins
    /retina/camera:front_right
    /retina/camera:predict
    /retina/camera:target0
    /retina/camera:target1
    /retina/camera:hubble1
    """

    def __new__(cls, expressions: List[DeviceExpression]) -> "DevicePath":
        formed_str: str = DevicePath.separator() + DevicePath.separator().join([str(e) for e in expressions])
        instance: DevicePath = super().__new__(cls, formed_str)
        instance._expressions = expressions
        instance._depth = len(instance._expressions)
        instance._topic = Topic(instance)  # cached since likely to be accessed
        return instance

    @property
    def depth(self) -> int:
        return self._depth  # type: ignore

    @property
    def topic(self) -> Topic:
        return self._topic  # type: ignore

    @property
    def expressions(self) -> List[DeviceExpression]:
        return self._expressions  # type: ignore

    @property
    def type(self) -> NodeType:
        if self.depth == 0:
            return NodeType.ROBOT
        else:
            return self.leaf().type

    @property
    def instance_str(self) -> Optional[str]:
        return self.leaf().instance if self.leaf().instance is not None else None

    def is_ancestor(self, other: "DevicePath") -> bool:
        return self.depth < other.depth and other.startswith(self)

    def is_parent(self, other: "DevicePath") -> bool:
        return self.depth + 1 == other.depth and self.is_ancestor(other)

    def join(self, other: Union[str, DeviceExpression, NodeType]) -> "DevicePath":
        if isinstance(other, NodeType):
            other = other.serialize()
        return DevicePath.parse(f"{self if self != DevicePath.robot() else ''}{DevicePath.separator()}{other}")

    def leaf(self) -> DeviceExpression:
        assert self.depth > 0
        return self.expressions[-1]

    def trim_leaf(self) -> "DevicePath":
        assert self.depth > 0
        return DevicePath(self.expressions[:-1])

    @staticmethod
    def parse(s: str) -> "DevicePath":
        assert DevicePath.separator() == s[0], f"Bad path (first char) {s}"
        assert len(s) == 1 or DevicePath.separator() != s[len(s) - 1], f"Bad path (last char)): {s}"

        expressions: List[DeviceExpression] = []
        try:
            if len(s) > 1:  # else base case / for robot
                expression_strs: List[str] = s[1:].split(DevicePath.separator())
                for expression_str in expression_strs:
                    expression = DeviceExpression.parse(expression_str)
                    expressions.append(expression)
        except Exception:
            LOG.exception(f'Unable to parse device path from: "{s}"')
            raise
        return DevicePath(expressions)

    @staticmethod
    def robot() -> "DevicePath":
        """
        Singleton reference to robot device path
        """
        return DevicePath.parse(DevicePath.separator())

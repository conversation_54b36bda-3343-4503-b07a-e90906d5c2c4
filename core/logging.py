import argparse
import os
from typing import Any, Dict, Optional

from core.fs.defaults import LOG_DIR
from lib.common.logging import add_log_args, get_log_args, init_log

# Logging
LOG_FILEPATH = os.path.join(LOG_DIR, "robot.log")
DATA_LOG_FILEPATH = os.path.join(LOG_DIR, "data.log")
WEBSOCKET_LOG_FILEPATH = os.path.join(LOG_DIR, "websocket.log")
PERF_LOG_FILEPATH = os.path.join(LOG_DIR, "perf.log")


# add defaults for logfile, data log
def add_robot_log_args(
    parser: argparse.ArgumentParser,
    default_logfile: Optional[str] = LOG_FILEPATH,
    default_data_logfile: Optional[str] = DATA_LOG_FILEPATH,
    default_perf_logfile: Optional[str] = PERF_LOG_FILEPATH,
) -> None:
    return add_log_args(
        parser,
        default_logfile=default_logfile,
        default_data_logfile=default_data_logfile,
        default_perf_logfile=default_perf_logfile,
    )


def get_robot_log_args(args: argparse.Namespace) -> Dict[str, Any]:
    return get_log_args(args=args)


# add defaults for logfile, data log
def init_robot_logs(
    *,
    logfile: str = LOG_FILEPATH,
    level: str = "INFO",
    data_logfile: str = DATA_LOG_FILEPATH,
    websocket_logfile: Optional[str] = None,
    perf_logfile: str = PERF_LOG_FILEPATH,
    stdout: bool = True,
    stdout_timestamps: bool = True,
    colorize: bool = False,
) -> None:
    # make the directory where the logfile will reside as long as its somewhere in the log dir
    # otherwise --fs-force-tmp-dir is probably set and we will trust what we were given
    if LOG_DIR in logfile:
        os.makedirs(os.path.dirname(logfile), exist_ok=True)
    # init logging
    init_log(
        logfile=logfile,
        level=level,
        data_logfile=data_logfile,
        websocket_logfile=websocket_logfile,
        perf_logfile=perf_logfile,
        stdout=stdout,
        stdout_timestamps=stdout_timestamps,
        colorize=colorize,
    )

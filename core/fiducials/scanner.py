from argparse import Namespace
from typing import TYPE_CHECKING, Any, Optional, Tuple, Union

import cv2
import numpy as np
import numpy.typing as npt

import lib.common.logging
from core.fiducials.mode import FiducialMode
from lib.common.fiducials.aruco_markers import ArucoMarkers
from lib.common.fiducials.charuco_board import CharucoBoard
from lib.common.fiducials.charuco_diamonds import CharucoDiamonds
from lib.common.fiducials.fiducials import Fiducials, NoArucoMarkersFoundException

LOG = lib.common.logging.get_logger(__name__)

if TYPE_CHECKING:
    # TODO: scanner keeps a reference to the controller but it should be the other way around only
    from core.controls.exterminator.model.scanner import Scanner


class ScannerFiducialsController:
    """
    Contains functions and routines for determining scanner information based on the fiducials in view of the cameras.
    """

    def __init__(self, scanner: "Scanner", args: Optional[Namespace]):
        if args is None:
            return
        self.scanner = scanner
        board = CharucoBoard.create_from_args(args)
        self._fiducials = Fiducials(charuco_board=board)

    @property
    def fiducials(self) -> Fiducials:
        return self._fiducials

    def mode(self) -> int:
        """Check if we can detect ChArUco diamonds. If so, use ChArUco, else ArUco."""
        assert self.scanner.target_cam is not None
        LOG.info("Checking for ArUco vs ChArUco...")
        self.scanner.goto_servo_center()
        self.scanner.settle_delay()
        img = self.scanner.target_cam.capture_stack()
        markers = self._fiducials.find_markers(img)
        if len(markers) == 0:
            raise NoArucoMarkersFoundException("No ArUco markers found when checking for fiducial mode")
        diamonds = self._fiducials.find_diamonds(img, markers)
        if len(diamonds) > 0:
            LOG.info("ChArUco diamonds detected. Using ChArUco diamonds...")
            return FiducialMode.CHARUCO_DIAMONDS
        else:
            LOG.info("No ChArUco diamonds detected. Using ArUco markers...")
            return FiducialMode.ARUCO_MARKERS

    # Perspective transform APIs

    def locate_perspective_transform_images(
        self, loc: Tuple[float, float], src_image: npt.NDArray[Any], dst_image: npt.NDArray[Any]
    ) -> Optional[Tuple[float, float]]:
        """
        Locate the loc in the dst_image, based on a perspective translation.
        The perspective transform is discovered by determining the four corners
        of the marker closest to the given loc.
        """
        # Determine the marker. The four corners of the marker in the src versus dst image will define the
        # perspective transformation
        src_marker = self.get_nearest_basis_marker(src_image, loc)
        if src_marker is None:
            # in case of no visible markers
            LOG.warning("Unable to construct perspective transformation. No visible markers in src image")
            return None
        marker_id = src_marker.ids[0][0]
        dst_marker = self._fiducials.find_marker(dst_image, marker_id)
        if dst_marker is None:
            # in case of no visible markers
            LOG.warning("Unable to construct perspective transformation. Marker %d not found in dst image.", marker_id)
            return None

        return ScannerFiducialsController.compute_apply_perspective_transform(loc, src_marker, dst_marker)

    @staticmethod
    def apply_perspective_transform(
        src: Tuple[float, float], perspective_transform: npt.NDArray[Any]
    ) -> Tuple[float, float]:
        """
        Let transform P = [ P_00 | P_01 | P_02 ]
                          [ P_10 | P_11 | P_12 ]
                          [ P_20 | P_21 | P_22 ]
        Then,
             dst(x,y) = ( (P_00x + P_01y + P_02) / (P_20x + P_21y + P_22),
                          (P_10x + P_11y + P_12) / (P_20x + P_21y + P_22) )

        Refer https://docs.opencv.org/2.4/modules/imgproc/doc/geometric_transformations.html#warpperspective

        This is similar to the classic 3D perspective transform ("the camera transform")
        in computer graphics, but there is no z-axis. I had trouble finding literature on
        the 2D transform, beyond the API documentation.

        To build your intuition on the 3D perspective transform,
        recommend this video: https://youtu.be/mpTl003EXCY
        """

        x = src[0]
        y = src[1]
        P = perspective_transform
        divisor = P[2][0] * x + P[2][1] * y + P[2][2]
        dst_x = (P[0][0] * x + P[0][1] * y + P[0][2]) / divisor
        dst_y = (P[1][0] * x + P[1][1] * y + P[1][2]) / divisor
        return dst_x, dst_y

    @staticmethod
    def locate_perspective_transform_diamonds(
        loc: Tuple[float, float], src_diamonds: CharucoDiamonds, dst_diamonds: CharucoDiamonds, logging: bool = False
    ) -> Optional[Tuple[float, float]]:
        """
        Locate the loc in the dst_marker coordinate systems, based on a perspective translation.
        The perspective transform is discovered by determining the four corners of the marker
        closest to the given loc.
        """
        # Determine the marker. The four corners of the marker in the src versus dst image will define the
        # perspective transformation
        nearest_src_diamonds: Optional[CharucoDiamonds] = src_diamonds.get_nearest(loc)
        if nearest_src_diamonds is None:
            # in case of no visible markers
            if logging:
                LOG.warning("Unable to construct perspective transformation. No src diamonds")
            return None
        if len(nearest_src_diamonds) < 4:
            if logging:
                LOG.warning(
                    "Unable to construct perspective transformation. Insufficient src diamond corners: %d",
                    len(nearest_src_diamonds),
                )
            return None

        nearest_dst_diamonds: Optional[CharucoDiamonds] = dst_diamonds.get_all(list(nearest_src_diamonds.ids))
        if nearest_dst_diamonds is None:
            if logging:
                LOG.warning(
                    "Unable to construct perspective transformation. Diamond corners %s not found in dst" "diamonds.",
                    nearest_src_diamonds.ids.tolist(),
                )
            return None
        if len(nearest_dst_diamonds) < 4:
            if logging:
                LOG.warning(
                    "Unable to construct perspective transformation. Insufficient dst diamond nearest" "corners: %d",
                    len(nearest_dst_diamonds),
                )
            return None

        return ScannerFiducialsController.compute_apply_perspective_transform(
            loc, nearest_src_diamonds, nearest_dst_diamonds
        )

    @staticmethod
    def locate_perspective_transform_markers(
        loc: Tuple[float, float], src_markers: ArucoMarkers, dst_markers: ArucoMarkers, logging: bool = False
    ) -> Optional[Tuple[float, float]]:
        """
        Locate the loc in the dst_marker coordinate systems, based on a perspective translation.
        The perspective transform is discovered by determining the four corners of the marker
        closest to the given loc.

        :type src_markers: ArucoMarkers
        :type dst_markers: ArucoMarkers
        """
        # Determine the marker. The four corners of the marker in the src versus dst image will define the
        # perspective transformation
        src_marker = src_markers.get_nearest(loc)
        if src_marker is None:
            # in case of no visible markers
            if logging:
                LOG.warning("Unable to construct perspective transformation. No src markers")
            return None
        marker_id = src_marker.ids[0][0]
        dst_marker = dst_markers.get(marker_id)
        if dst_marker is None:
            if logging:
                LOG.warning(
                    "Unable to construct perspective transformation. Marker %d not found in dst markers.", marker_id
                )
            # in case of no visible markers
            return None

        return ScannerFiducialsController.compute_apply_perspective_transform(loc, src_marker, dst_marker)

    @staticmethod
    def compute_apply_perspective_transform(
        src_loc: Tuple[float, float],
        src_marker: Union[ArucoMarkers, CharucoDiamonds],
        dst_marker: Union[ArucoMarkers, CharucoDiamonds],
    ) -> Tuple[float, float]:
        """
        Apply a perspective transformation to the given src (x, y) based on the perspective
        transform defined by the four corners of the src marker and dst marker
        """
        src_marker_corners = src_marker.corners[0] if type(src_marker) == ArucoMarkers else np.array(src_marker.corners)
        dst_marker_corners = dst_marker.corners[0] if type(dst_marker) == ArucoMarkers else np.array(dst_marker.corners)
        perspective_transform_matrix = cv2.getPerspectiveTransform(src_marker_corners, dst_marker_corners)

        dst_loc = ScannerFiducialsController.apply_perspective_transform(src_loc, perspective_transform_matrix)

        return dst_loc

    def get_nearest_basis_marker(self, img: npt.NDArray[Any], loc: Tuple[float, float]) -> Optional[ArucoMarkers]:
        """Return the marker for the fiducial which has ZERO_CORNER nearest to loc."""
        markers = self._fiducials.find_markers(img, ignore_small=True)
        if markers is None:
            LOG.debug("Cannot find nearest marker as there are no markers found in image.")
            return None
        LOG.debug("Found %d markers. Getting nearest...", len(markers.ids))
        marker = markers.get_nearest(loc)
        return marker  # possibly None

from aenum import IntEnum, unique


@unique
class FiducialMode(IntEnum):
    """
    The type of fiducials to look for, either ArUco markers or ChArUco diamonds.
    Other modes are theoretically possible but these two are supported by cv2.
    """

    ARUCO_MARKERS: int = 1

    CHARUCO_DIAMONDS: int = 2

    def __str__(self) -> str:
        """
        Override default string formatting to just print the name
        """
        return str(self.name)

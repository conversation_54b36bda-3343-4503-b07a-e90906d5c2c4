import asyncio
from typing import Any, Awaitable, Callable, Coroutine, Optional

import numpy as np

import lib.common.logging
from config.client.cpp.config_client_python import get_computer_config_prefix, get_global_config_subscriber
from cv.runtime.client import CVRuntimeClient
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.devices.boards.pulczar.pulczar_board_device import PulczarBoardDevice
from lib.common.math import MovingAverage
from lib.common.time import maka_control_timestamp_ms

LOG = lib.common.logging.get_logger(__name__)


def target_id_to_cam_id(target_id: int) -> str:
    return f"target{target_id}"


async def get_focus_value(client: CVRuntimeClient, cam_id: str, iterations: int = 10) -> float:
    last_timestamp = maka_control_timestamp_ms()
    focus_metric_total: float = 0
    success_count = 0
    while success_count < iterations:
        resp = await client.get_next_focus_metric(cam_id, last_timestamp)
        if resp is not None:
            success_count += 1
            focus_metric_total += resp[0]
            last_timestamp = resp[1]
    return focus_metric_total / iterations


async def focus_range_scan(
    client: CVRuntimeClient,
    cam_id: str,
    pulczar_lens: PulczarBoardDevice,
    progress_callback: Optional[Callable[[int], Awaitable[None]]] = None,
) -> int:
    step_sizes = [20, 10, 5, 1]
    max_fa: float = 0
    desired_fv: int = 0
    focus_start, focus_end = pulczar_lens.min_lens_value, pulczar_lens.max_lens_value
    # target focus value
    total_focus_tests = len(step_sizes)
    focus_tests_completed = 0
    for step_size in step_sizes:
        for tfv in np.arange(focus_start, focus_end, step_size, dtype=float):
            await pulczar_lens.lens_set(int(tfv))
            fa = await get_focus_value(client, cam_id, iterations=1)

            if fa > max_fa:
                max_fa = fa
                desired_fv = tfv
        focus_tests_completed += 1
        if progress_callback:
            await progress_callback(int(focus_tests_completed / total_focus_tests * 100))
        # adjust focus range for next step size iteration0
        focus_start = max(focus_start, desired_fv - step_size)
        focus_end = min(focus_end, desired_fv + step_size)

    await pulczar_lens.lens_set(int(desired_fv))
    return desired_fv


async def auto_focus(
    pulczar_board: PulczarBoardDevice,
    client: CVRuntimeClient,
    target_id: int,
    pause_callback: Callable[[], Coroutine[Any, Any, bool]],
) -> None:
    cam_id = target_id_to_cam_id(target_id)
    with bot_stop_handler.scoped_bot_stop_blocker(f"auto_focus_{target_id}") as bot_stop:
        while not bot_stop.is_stopped():
            try:
                focus_range_start, focus_range_end = pulczar_board.min_lens_value, pulczar_board.max_lens_value
                focus_range = focus_range_end - focus_range_start

                enabled_node = get_global_config_subscriber().get_config_node("aimbot", "enable_auto_focus")

                if not enabled_node.get_bool_value() or not pulczar_board.is_enabled:
                    await asyncio.sleep(5)
                    continue

                current_fv = (
                    get_global_config_subscriber()
                    .get_config_node("aimbot", f"scanners/scanner{pulczar_board.scanner_id}/focus")
                    .get_int_value()
                )
                focus_range_start_limit = max(current_fv - focus_range * 0.2, focus_range_start)
                focus_range_end_limit = min(current_fv + focus_range * 0.2, focus_range_end)
                step_size = 2
                direction = 1
                fa_moving_avg = MovingAverage(initial_value=0, smooth_factor=0.5)

                last_fa = await get_focus_value(client, cam_id)
                if last_fa < 0:
                    LOG.warning(
                        f"Exiting auto focus for target{target_id} since"
                        + "focus metric computation is not enabled for this camera in cv runtime"
                    )
                    return

                while not bot_stop.is_stopped():
                    if not enabled_node.get_bool_value() or not pulczar_board.is_enabled or await pause_callback():
                        await asyncio.sleep(1)
                        last_fa = await get_focus_value(client, cam_id)
                        current_fv = (
                            get_global_config_subscriber()
                            .get_config_node("aimbot", f"scanners/scanner{pulczar_board.scanner_id}/focus")
                            .get_int_value()
                        )
                        focus_range_start_limit = max(current_fv - focus_range * 0.2, focus_range_start)
                        focus_range_end_limit = min(current_fv + focus_range * 0.2, focus_range_end)
                        continue
                    await pulczar_board.lens_set(int(current_fv))
                    fa = await get_focus_value(client, cam_id)

                    # Heuristic to determine when we are too out of focus to hill climb
                    if fa < 0.005:
                        current_fv = await focus_range_scan(client, cam_id, pulczar_board)
                        fa = await get_focus_value(client, cam_id)
                        last_fa = fa

                    fa_diff = (fa - last_fa) / step_size
                    fa_diff = fa_moving_avg.update(fa_diff)

                    if fa_diff < -0.001:
                        direction = -direction
                        fa_moving_avg.reset()
                    current_fv += direction * step_size

                    if current_fv < focus_range_start_limit:
                        direction = -direction
                        current_fv = int(focus_range_start_limit)
                    elif current_fv > focus_range_end_limit:
                        direction = -direction
                        current_fv = int(focus_range_end_limit)

                    last_fa = fa
            except Exception as e:
                LOG.error(e)
                continue
            break


async def single_auto_focus(
    pulczar_board: PulczarBoardDevice,
    client: CVRuntimeClient,
    target_id: int,
    progress_callback: Optional[Callable[[int], Awaitable[None]]] = None,
) -> None:
    best_value = await focus_range_scan(client, target_id_to_cam_id(target_id), pulczar_board, progress_callback)

    def add_to_config() -> None:
        if get_global_config_subscriber().get_config_node("aimbot", "scanners").get_node(f"scanner{target_id}") is None:
            get_global_config_subscriber().get_client().add_to_list(
                f"{get_computer_config_prefix()}/aimbot/scanners", f"scanner{target_id}"
            )
        get_global_config_subscriber().get_client().set_int_value(
            f"{get_computer_config_prefix()}/aimbot/scanners/scanner{target_id}/focus", int(best_value)
        )

    await asyncio.get_event_loop().run_in_executor(None, add_to_config)

import abc
from typing import Any, Dict, <PERSON>, Tuple, Union

from core.boot.context import Shared<PERSON>ontext
from core.cv.retina.config import RetinaConfigFile
from core.model.sensor import Sensor

LensConfigType = Dict[str, Union[float, str]]
LensConfigFileType = Dict[str, LensConfigType]


# TODO create "Lens" interface, de-coupled from Node. See INS structure as an example.
class LensNode(Sensor, abc.ABC):
    def __init__(self, context: SharedContext, **kwargs: Any):
        super().__init__(**kwargs)
        self._context = context

    @abc.abstractmethod
    def get_focus(self) -> float:
        pass

    @abc.abstractmethod
    def set_focus(self, focus: float) -> None:
        pass

    @abc.abstractmethod
    def get_id(self) -> str:
        pass

    @abc.abstractmethod
    def focus_range(self) -> Tuple[float, float]:
        pass

    @abc.abstractmethod
    def focus_step_sizes(self) -> List[float]:
        pass

    def _start_task(self) -> None:
        self.load()

        return None

    def to_dict(self) -> LensConfigType:
        lens_id: str = self.get_id()
        min_fv, max_fv = self.focus_range()

        return {"lens_id": lens_id, "focus": self.get_focus(), "max": max_fv, "min": min_fv}

    def _retina_config_file(self) -> RetinaConfigFile:
        return self._context.retina_cfg

    def save(self) -> None:
        pass

    def load(self) -> None:
        pass

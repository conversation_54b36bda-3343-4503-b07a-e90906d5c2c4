from typing import Dict

from core.fs.defaults import CONFIG_DIR
from lib.common.config_file import ConfigFile

RETINA = "retina"
RetinaConfigType = Dict[str, Dict[str, float]]


class RetinaConfigFile(ConfigFile):
    FILENAME = "retina.json"

    def __init__(self, config_dir: str = CONFIG_DIR, filename: str = FILENAME) -> None:
        super().__init__(config_dir=config_dir, filename=filename, root_element=RETINA)

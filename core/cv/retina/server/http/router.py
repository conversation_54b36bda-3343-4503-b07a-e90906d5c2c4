from typing import Any, Dict, Optional, Sequence, Tuple

from core.cv.retina.camera.node import Cam
from core.cv.retina.retina import Retina
from core.model.node.type import NodeType
from lib.common.web.http.error import HTTPError
from lib.common.web.http.router import Component<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RouterBuilder
from lib.common.web.http.router_utils import merge_add


class RetinaRouterBuilder(ComponentRouterBuilder):
    def __init__(self, retina: Retina):
        super().__init__(base_route=f"/{NodeType.RETINA.serialize()}")
        self._retina = retina

    # common helper functions
    def _get_camera_by_hardware_id(self, camera_id: str) -> Cam:
        cam: Optional[Cam] = self._retina.get_camera_by_hardware_id(camera_id)
        if cam is None:
            raise HTTPError(404, f"Camera: {camera_id} does not exist.")
        return cam

    def handle_get_camera_fps(self, bot: Any, camera_id: str) -> float:
        return self._get_camera_by_hardware_id(camera_id).preview_fps_limit

    def handle_update_camera_fps(self, bot: Any, camera_id: str, fps: float) -> None:
        self._get_camera_by_hardware_id(camera_id).handle_update_preview_limit(fps)

    def handle_get_cameras(self, bot: Any) -> Sequence[str]:
        return self._retina.camera_ids

    def handle_get_camera_resolution(self, bot: Any, camera_id: str) -> Dict[str, Any]:
        resolution: Optional[Tuple[int, int]] = self._get_camera_by_hardware_id(camera_id).resolution
        assert resolution is not None, f"Camera {camera_id} does not know its own resolution?"
        return {"resolution": list(resolution)}

    def add_onto(self, bot_routes: RouterBuilder) -> None:
        """
        Attach retina-specific routes
        """
        bot_routes.add("POST", self.suburl("/camera/(?P<camera_id>.*)/fps"), body_desc={"fps": float})(
            merge_add(self.handle_update_camera_fps)
        )
        bot_routes.add("GET", self.suburl("/camera/(?P<camera_id>.*)/fps"))(merge_add(self.handle_get_camera_fps))
        bot_routes.add("GET", self.suburl("/cameras"))(merge_add(self.handle_get_cameras))
        bot_routes.add("GET", self.suburl("/camera/(?P<camera_id>.*)/resolution"))(
            merge_add(self.handle_get_camera_resolution)
        )

from typing import Any, Dict, List, Optional, Tuple, cast

import numpy as np

import lib.common.logging
import lib.common.tasks
from core.config.hardware import HardwareConfigFile
from core.cv.retina.camera.node import Cam
from core.cv.retina.config import RETINA, RetinaConfigFile, RetinaConfigType
from core.cv.retina.lens import LensNode
from core.cv.retina.space.predict_space import PredictSpace
from core.model.id import ReferenceId
from core.model.node.type import NodeType
from core.model.path import CameraInstance, DeviceExpression, DevicePath, Instance, RelativeInstance
from core.model.sensor import Sensor
from lib.common.fiducials.fiducials import Fiducials
from lib.common.image import CamImage
from lib.common.protocol.channel.base import SubscriberChannel

LOG = lib.common.logging.get_logger(__name__)

CAMERA_LENS_UNDISTORTION = "camera_lens_undistortion"
UNDISTORTION_COEFFICIENTS = "undistortion_coefficients"
CAMERA_MATRIX = "camera_matrix"


class Retina(Sensor):
    """
    The Retina senses visual light or infared rays that travel into the robot
    cameras, and provides access to raw camera sensor data to the rest of the
    robot, including the Visual Cortex.

    This class is primarily responsible for:
      * Raw camera streams
        * RGB
        * IR
        * Stereoscopic
      * Basic image pre-processing
        * Mirror
        * Flip
        * Rotate
      * Load Balancing
        * USB Bandwith
        * GPU (as needed for basic image pre-processing)

    This class is NOT responsible for:
      * Algorithms which use images
      * Optical Flow
      * Optical Color Correction
      * Computer Vision
      * Deep Learning

    Those functions are meant to be clients of the Retina.

    Generally speaking, actuation will not depend on the Retina. It will depend on an intermediate
    computation such as weed mask detection or optical flow. We know we will have done well unwinding
    our dependencies when the raw cameras / retina are no longer referenced directly during actuation.

    Retina (https://en.wikipedia.org/wiki/Retina):
        The retina is the innermost, light-sensitive layer of tissue of the eye
        of most vertebrates and some molluscs. The optics of the eye create a
        focused two-dimensional image of the visual world on the retina, which
        translates that image into electrical neural impulses to the brain to
        create visual perception, the retina serving a function analogous to
        that of the film or image sensor in a camera.

        [....]

        Light striking the retina initiates a cascade of chemical and
        electrical events that ultimately trigger nerve impulses that are sent
        to various visual centres of the brain through the fibres of the optic
        nerve. Neural signals from the rods and cones undergo processing by
        other neurons, whose output takes the form of action potentials in
        retinal ganglion cells whose axons form the optic nerve. Several
        important features of visual perception can be traced to the retinal
        encoding and processing of light.
    """

    def __init__(self, *argv: Any, **kwargs: Any):
        super().__init__(*argv, **kwargs)

        self.undisort_cameras()

    def dump_callback(self) -> Dict[str, Any]:
        return {"cameras": {c.id: {"fps": c.dump_callback()} for c in self.cameras}, "status": self.status_callback()}

    def status_callback(self) -> Dict[str, Any]:
        return {}

    @property
    def predict_cam(self) -> Optional[Cam]:
        return self._pcam

    @property
    def predict_cams(self) -> Dict[DevicePath, Cam]:
        return self._pcams if self._pcams is not None else {}

    def get_predict_cam(self, index: int) -> Optional[Cam]:
        return (self._pcams or {}).get(DevicePath.parse(f"/retina/camera:predict{index}"))

    def get_predict_space(self, index: int) -> Optional[PredictSpace]:
        return self._predict_spaces.get(index)

    @property
    def back_left_cam_image_subscription(self) -> Optional[SubscriberChannel[CamImage]]:
        return self._dcam_back_left.subscription if self._dcam_back_left is not None else None

    @property
    def back_right_cam_image_subscription(self) -> Optional[SubscriberChannel[CamImage]]:
        return self._dcam_back_right.subscription if self._dcam_back_right is not None else None

    @property
    def front_left_cam_image_subscription(self) -> Optional[SubscriberChannel[CamImage]]:
        return self._dcam_front_left.subscription if self._dcam_front_left is not None else None

    @property
    def front_right_cam_image_subscription(self) -> Optional[SubscriberChannel[CamImage]]:
        return self._dcam_front_right.subscription if self._dcam_front_right is not None else None

    @property
    def cameras(self) -> List[Cam]:
        return [c for c in self.subtree.values() if isinstance(c, Cam)]

    @property
    def camera_ids(self) -> List[ReferenceId]:
        return [c.id for c in self.subtree.values() if isinstance(c, Cam)]

    @property
    def back_left_cam(self) -> Optional[Cam]:
        return self._dcam_back_left

    @property
    def back_right_cam(self) -> Optional[Cam]:
        return self._dcam_back_right

    @property
    def front_left_cam(self) -> Optional[Cam]:
        return self._dcam_front_left

    @property
    def front_right_cam(self) -> Optional[Cam]:
        return self._dcam_front_right

    def get_target_cam(self, index: int) -> Optional[Cam]:
        return (self._tcams or {}).get(DevicePath.parse(f"/retina/camera:target{index}"))

    def get_camera_by_hardware_id(self, hardware_id: str) -> Optional[Cam]:
        for camera_device_path in [c for c in self.children() if c.type == NodeType.CAMERA]:
            cam = cast(Cam, self.subtree[camera_device_path])
            if cam.id == hardware_id:
                return cam
        return None

    def get_camera(self, instance: Instance) -> Optional[Cam]:
        return cast(Optional[Cam], self.grep_subtree(f"/retina/camera:{instance}", unique=True))

    def get_lens(self, instance: Instance) -> Optional[LensNode]:
        return cast(Optional[LensNode], self.grep_subtree(f"/retina/lens:{instance}$", unique=True))

    def auto_focus(self, camera_id: str) -> Optional[float]:
        """
        auto_focus() performs the following steps:
            - camera/lens lookup (w/ camera_id)
            - iterate through lens.focus_range() for first step size A from focus_step_sizes()
                - set_focus
                - pull image
                - compute_focus
            - repeat iteration for next step size for [best_focus - A, best_focus + A]

        Returns the lens value with highest focus annotation.
        """
        cam = self.get_camera(Instance(camera_id))
        lens = self.get_lens(Instance(camera_id))
        if not cam or not lens:
            return None

        cam.set_focusing_mode(True)
        try:
            max_fa: float = 0
            desired_fv: float = 0
            focus_range_start, focus_range_end = lens.focus_range()
            # target focus value
            for step_size in lens.focus_step_sizes():
                for tfv in np.arange(focus_range_start, focus_range_end, step_size, dtype=float):
                    LOG.debug(f"auto-focusing {lens} for {cam} to {tfv}")
                    lens.set_focus(tfv)
                    # TODO(asergeev): remove this after we fix RollingOrderedBuffer.next()
                    cam.next()
                    image = cam.next()
                    # focus annotation
                    fa = image.compute_focus()

                    if fa > max_fa:
                        max_fa = fa
                        desired_fv = tfv

                # adjust focus range for next step size iteration0
                focus_range_start = max(focus_range_start, desired_fv - step_size)
                focus_range_end = min(focus_range_end, desired_fv + step_size)
        finally:
            cam.set_focusing_mode(False)

        lens.set_focus(desired_fv)
        LOG.debug(f"auto-focusing {lens} for {cam} set to {desired_fv} for {max_fa}")

        return desired_fv

    def _cache_named_references(self) -> None:
        # Drive Cameras
        self._dcam_back_left: Optional[Cam] = cast(
            Optional[Cam], self.grep_subtree(f"{NodeType.CAMERA}:{RelativeInstance.BACK_LEFT}".lower(), unique=True),
        )
        if self._dcam_back_left is None:
            LOG.warning("{} Initializing with no back left drive camera".format(self.device_path))

        self._dcam_back_right: Optional[Cam] = cast(
            Optional[Cam], self.grep_subtree(f"{NodeType.CAMERA}:{RelativeInstance.BACK_RIGHT}".lower(), unique=True),
        )
        if self._dcam_back_right is None:
            LOG.warning("{} Initializing with no back right drive camera".format(self.device_path))

        self._dcam_front_left: Optional[Cam] = cast(
            Optional[Cam], self.grep_subtree(f"{NodeType.CAMERA}:{RelativeInstance.FRONT_LEFT}".lower(), unique=True),
        )
        if self._dcam_front_left is None:
            LOG.warning("{} Initializing with no front left drive camera".format(self.device_path))

        self._dcam_front_right: Optional[Cam] = cast(
            Optional[Cam], self.grep_subtree(f"{NodeType.CAMERA}:{RelativeInstance.FRONT_RIGHT}".lower(), unique=True),
        )
        if self._dcam_front_right is None:
            LOG.warning("{} Initializing with no front right drive camera".format(self.device_path))

        # Predict Cameras
        self._predict_spaces: Dict[int, PredictSpace] = {}
        self._pcams: Optional[Dict[DevicePath, Cam]] = cast(
            Optional[Dict[DevicePath, Cam]],
            self.grep_subtree("{}:{}".format(NodeType.CAMERA, CameraInstance.PREDICT).lower()),
        )
        self._pcam: Optional[Cam] = None
        if self._pcams is None or len(self._pcams) == 0:
            LOG.warning("{} Initializing with no predict cams".format(self.device_path))
        else:
            cam1 = self.get_predict_cam(1)
            cam2 = self.get_predict_cam(2)
            cam3 = self.get_predict_cam(3)
            cam4 = self.get_predict_cam(4)
            # TODO Remove this once there are no longer assumptions on 1 predict cam
            self._pcam = list(self._pcams.values())[0]
            if len(self._pcams) == 1:  # Predict Space Hardcoding until we have better boot tree.
                self._predict_spaces[1] = PredictSpace([self._pcam])
            elif cam1 is not None and cam2 is not None:
                self._predict_spaces[1] = PredictSpace([cam1, cam2])
            if cam3 is not None and cam4 is not None:
                self._predict_spaces[2] = PredictSpace([cam3, cam4])

        # Target Cameras
        self._tcams: Optional[Dict[DevicePath, Cam]] = cast(
            Optional[Dict[DevicePath, Cam]],
            self.grep_subtree("{}:{}".format(NodeType.CAMERA, CameraInstance.TARGET).lower()),
        )
        if self._tcams is None:
            LOG.warning("{} Initializing with no target cams".format(self.device_path))

        assert self._dcam_front_right is not None or self._pcam is not None or self._tcams is not None, "No cameras?"

    def undisort_cameras(self) -> None:
        # TODO(asergeev): This should use RetinaConfigFile injected from boot loader, but
        #                 I'm not going to do that because I don't have a way to test this code.
        config_file = RetinaConfigFile(config_dir=self.fs.abs_config_dir)
        retina_config: RetinaConfigType = config_file.load() or {}

        for camera_expression, camera_config in retina_config.items():
            camera_device_path = DevicePath(cast(List[DeviceExpression], [RETINA, camera_expression]))
            cam = cast(Optional[Cam], self.subtree.get(camera_device_path))
            if cam is not None:
                self._undistort_cam(cam, camera_config)

    @staticmethod
    def get_undistortion_data(config: Dict[str, Any]) -> Tuple[Optional[List[float]], Optional[List[List[float]]]]:
        camera_lens_undistortion: Dict[str, Any] = config.get(CAMERA_LENS_UNDISTORTION, {})
        lens_undistortion_coefficients: Optional[List[float]] = camera_lens_undistortion.get(UNDISTORTION_COEFFICIENTS)
        camera_matrix: Optional[List[List[float]]] = camera_lens_undistortion.get(CAMERA_MATRIX)

        return lens_undistortion_coefficients, camera_matrix

    def _undistort_cam(self, camera: Cam, config: Dict[str, Any]) -> None:
        LOG.info(f"{self.id} Undistorting {camera.id} camera...")
        if camera.resolution is None:
            LOG.warning(f"{self.id} No resolution for {camera.id} camera in {HardwareConfigFile.FILENAME}")
            return

        lens_undistortion_coefficients, camera_matrix = Retina.get_undistortion_data(config)
        if lens_undistortion_coefficients is None or camera_matrix is None:
            LOG.warning(
                f"{self.id} No undistortion parameters in {RetinaConfigFile.FILENAME} " f"for {camera.id} camera"
            )
            return

        lens_undistortion_coefficients_np = np.array(lens_undistortion_coefficients)
        camera_matrix_np = np.array(camera_matrix)

        fiducials = Fiducials()
        img_shape = camera.resolution[1], camera.resolution[0]
        mapping_parameters = fiducials.find_lens_undistortion_mapping_parameters(
            img_shape, camera_matrix_np, lens_undistortion_coefficients_np,
        )
        camera.set_lens_undistortion_mapping_parameters((mapping_parameters[0], mapping_parameters[1]))

        LOG.info(f"{self.id} Undistorted {camera.id} camera")

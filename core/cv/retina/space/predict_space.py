import functools
import json
import os
import threading
from typing import Any, Dict, List, Optional, Tuple, cast

import cv2
import numpy as np

import lib.common.logging
import lib.common.tasks
from core.config.config import ConfigCategory, MakaConfigException, PhysicalRobotConfiguration, load_config
from core.controls.annotate import ROW_MODULE_LAYER
from core.cv.retina.camera.node import Cam
from core.model.id import ReferenceId
from core.model.path import DevicePath
from lib.common.annotate import Annotation
from lib.common.error import MakaException
from lib.common.fiducials.charuco_diamonds import CharucoDiamonds
from lib.common.image import CamImage

LOG = lib.common.logging.get_logger(__name__)


class PredictSpaceException(MakaException):
    pass


class PredictSpace:
    def __init__(self, cameras: List[Cam]):
        self._cameras = {x.id: x for x in cameras}
        self._indexed_cameras = cameras
        self._calibrated_deltas: Optional[List[Optional[Tuple[float, float]]]] = None
        try:
            self._robot_config: Optional[PhysicalRobotConfiguration] = None
            self._robot_config = load_config()
        except MakaConfigException:
            pass
        self._calibration_lock = threading.Lock()
        self._saving_lock = threading.Lock()
        self._load_calibration()

        # Add annotations to Predict Cams
        for pindex, predict_cam in enumerate(self._indexed_cameras):
            predict_cam.add_annotation(
                Annotation(
                    "Predict Space",
                    layer=ROW_MODULE_LAYER,
                    f=functools.partial(self.annotate_predict_space, pindex),
                    enabled=False,
                )
            )

    def list_cameras(self) -> List[Cam]:
        return self._indexed_cameras

    def get_cam_by_index(self, index: int) -> Optional[Cam]:
        if len(self._indexed_cameras) <= index:
            return None
        return self._indexed_cameras[index]

    def get_cam_by_id(self, cam_id: ReferenceId) -> Optional[Cam]:
        if cam_id not in self._cameras:
            return None
        return self._cameras[cam_id]

    def get_index_by_id(self, cam_id: ReferenceId) -> Optional[int]:
        for pindex, pcam in enumerate(self._indexed_cameras):
            if pcam.id == cam_id:
                return pindex
        return None

    def get_index_by_device_path(self, kpcam: DevicePath) -> Optional[int]:
        for pindex, pcam in enumerate(self._indexed_cameras):
            if pcam.device_path == kpcam:
                return pindex
        return None

    @staticmethod
    def _min_max_coord(coord: float, minimum: int, maximum: int) -> int:
        return int(min(max(coord, minimum), maximum))

    def annotate_predict_space(self, pindex: int, cam_image: CamImage) -> None:
        image = cam_image.image_bgr
        calibrated_deltas = self.get_calibrated_deltas()
        res = self._indexed_cameras[pindex].resolution

        if calibrated_deltas is None or res is None:
            return

        def _draw_overlap(img: np.ndarray, pre_orig: Tuple[float, float], resol: Tuple[float, float]) -> None:
            assert res is not None
            origin = (self._min_max_coord(pre_orig[0], 0, res[0]), self._min_max_coord(pre_orig[1], 0, res[1]))
            if resolution is not None:
                destination = (
                    self._min_max_coord(pre_orig[0] + resol[0], 0, res[0]),
                    self._min_max_coord(pre_orig[1] + resol[1], 0, res[1]),
                )
                sub_img = img[origin[1] : destination[1], origin[0] : destination[0]]
                white_rect = np.ones(sub_img.shape, dtype=np.uint8) * 255
                img[origin[1] : destination[1], origin[0] : destination[0]] = cv2.addWeighted(
                    sub_img, 0.8, white_rect, 0.2, 1.0
                )

        if pindex > 0 and calibrated_deltas[pindex - 1] is not None:
            resolution = self._indexed_cameras[pindex - 1].resolution
            pre_origin = calibrated_deltas[pindex - 1]
            assert pre_origin is not None
            pre_origin = (-pre_origin[0], -pre_origin[1])
            if resolution is not None:
                _draw_overlap(image, pre_origin, resolution)

        if pindex < len(calibrated_deltas) and calibrated_deltas[pindex] is not None:
            resolution = self._indexed_cameras[pindex].resolution
            pre_origin = calibrated_deltas[pindex]
            assert pre_origin is not None
            if resolution is not None:
                _draw_overlap(image, pre_origin, resolution)

    def get_calibrated_deltas(self) -> Optional[List[Optional[Tuple[float, float]]]]:
        with self._calibration_lock:
            return self._calibrated_deltas

    def _get_calibration_name(self) -> str:
        return "predict_space_calibration_" + "_".join([x.id for x in self._indexed_cameras]) + ".json"

    def _load_calibration(self) -> None:
        if self._robot_config is None:
            return
        try:
            calibration_file_path = self._robot_config.get(ConfigCategory.CALIBRATION, self._get_calibration_name())
            with open(calibration_file_path, "r") as f:
                calibrated_deltas = json.load(f)
            with self._calibration_lock:
                self._calibrated_deltas = cast(
                    List[Optional[Tuple[float, float]]],
                    [tuple(x) if x is not None else None for x in calibrated_deltas["calibration"]],
                )
            LOG.info(
                f"Loaded Calibrated deltas: {self._calibrated_deltas} for {'_'.join([x.id for x in self._indexed_cameras])}"
            )
        except MakaConfigException:
            LOG.warning(f"No Predict Space Calibration for : {'_'.join([x.id for x in self._indexed_cameras])}")

    def _save_calibration(self, calibrated_deltas: List[Optional[Tuple[float, float]]]) -> None:
        with self._saving_lock:
            if self._robot_config is not None:
                calibration_file_path = self._robot_config.get_or_make_path(
                    ConfigCategory.CALIBRATION, self._get_calibration_name()
                )
                calibration: Dict[str, Any] = {"calibration": calibrated_deltas}
                os.makedirs(os.path.dirname(calibration_file_path), exist_ok=True)
                with open(calibration_file_path, "w") as f:
                    json.dump(calibration, f)
                LOG.info(f"Calibration File Path: {calibration_file_path}")
        with self._calibration_lock:
            self._calibrated_deltas = calibrated_deltas

    def calibrate_with_markers(self, predict_space_diamonds: List[CharucoDiamonds]) -> None:
        assert len(predict_space_diamonds) == len(self._indexed_cameras) and len(self._indexed_cameras) > 0
        base_diamonds = predict_space_diamonds[0]

        calibrated_deltas: List[Optional[Tuple[float, float]]] = []

        for diamonds in predict_space_diamonds[1:]:
            base_overlapping_diamonds = base_diamonds.get_all(list(diamonds.ids))
            if base_overlapping_diamonds is None:
                LOG.warning("No Overlap in Predict Space")
                base_diamonds = diamonds
                calibrated_deltas.append(None)
                continue
            follow_overlapping_diamonds = diamonds.get_all(list(base_overlapping_diamonds.ids))
            if follow_overlapping_diamonds is None:
                LOG.warning("No Overlap in Predict Space")
                base_diamonds = diamonds
                calibrated_deltas.append(None)
                continue

            base_corners = cast(np.ndarray, base_overlapping_diamonds.corners)
            follow_corners = cast(np.ndarray, follow_overlapping_diamonds.corners)
            deltas: List[np.ndarray] = []
            for base_d, follow_d in zip(base_corners, follow_corners):
                delta = base_d - follow_d
                deltas.append(delta)

            if len(deltas) == 0:
                LOG.warning("No Overlap in Predict Space")
                base_diamonds = diamonds
                calibrated_deltas.append(None)
                continue
            final_delta = np.mean(np.array(deltas), axis=0)
            final_delta_tuple = (float(final_delta[0][0]), float(final_delta[0][1]))
            calibrated_deltas.append(final_delta_tuple)
            LOG.info(f"Delta Diamonds is {final_delta_tuple}")
            base_diamonds = diamonds
        self._save_calibration(calibrated_deltas)

import abc
import os
import shutil
import time
from functools import partial
from typing import Any, Callable, Dict, List, Optional, Tuple, TypeVar, cast

import cv2
import numpy as np
from sortedcontainers import SortedDict, SortedValuesView

import lib.common.annotate
import lib.common.logging
import lib.common.tasks
from core.controls.annotate import BASE_LAYER, IMAGE_OVERRIDE_LAYER
from core.cv.retina.camera.base import Camera
from core.fs.utils import camera_filename, tmp_filename
from core.model.sensor import Sensor
from cv.runtime.cpp.metadata_writer_python import GeoECEFData, GeoLLAData, MetadataWriter
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from lib.common.annotate import Annotation
from lib.common.concurrent import RollingOrderedBuffer
from lib.common.image import CamImage, ColorCorrectionProfile, LensUndistortionMappingParametersType
from lib.common.image import apply_transforms as apply_image_transforms
from lib.common.image import tiled_9crop
from lib.common.image.cam import BufferedCamImage, cam_image_depth_save
from lib.common.math import MovingAverage
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.protocol.channel.callback import CallbackSubscriberChannel
from lib.common.record.base import Recorder
from lib.common.record.frame_sequence import FrameSequenceRecorder
from lib.common.record.video import VideoRecorder
from lib.common.shmem.cpp.shmem_python import ImageBuffer
from lib.common.tasks import MakaTask
from lib.common.time import maka_control_timestamp_ms

LOG = lib.common.logging.get_logger(__name__)

ANNOTATION_DEPTH = "depth"
ANNOTATION_TIMESTAMP = "timestamp"
ANNOTATION_UNDISTORTED = "undistorted"
ANNOTATION_FPS = "fps"
ANNOTATION_FOCUS = "focus"
ANNOTATION_BRIGHTNESS = "brightness"

T = TypeVar("T")


def _imwrite(file: str, image: np.ndarray, quality: float = 95) -> None:
    if file.endswith(".jpg") or file.endswith(".jpeg"):
        ret = cv2.imwrite(file, image, [int(cv2.IMWRITE_JPEG_QUALITY), quality])
    else:
        ret = cv2.imwrite(file, image)
    assert ret


class Cam(Camera, Sensor, abc.ABC):
    def __init__(
        self,
        *argv: Any,
        preview_scale: float = 1,
        preview_fps_limit: float = 0.1,
        frame_capture_fps: float = 2,
        mirror: bool = False,
        flip: bool = False,
        transpose: bool = False,
        recover_blue: bool = False,
        crop: Optional[Dict[str, int]] = None,
        latency_ms: int = 200,
        ppi: Optional[float] = None,
        always_save_unannotated: bool = False,
        **kwargs: Any,
    ) -> None:
        super().__init__(*argv, **kwargs)
        self.short_name: str = self.id  # TODO refine this
        assert 0 < preview_scale <= 1
        self._preview_scale: float = preview_scale
        self._preview_fps_limit: float = preview_fps_limit  # CameraFeed Vue widget is tied to this
        self._mirror: bool = mirror
        self._flip: bool = flip
        self._transpose: bool = transpose
        self._recover_blue: bool = recover_blue
        self._crop = crop
        assert latency_ms is None or latency_ms >= 0
        self._latency_ms = latency_ms if latency_ms is not None else 0
        self._ppi = ppi

        self._always_save_unannotated = always_save_unannotated
        self._annotations_by_layer: SortedDict[int, Annotation] = SortedDict()
        self._annotations_by_name: SortedDict[str, Annotation] = SortedDict()
        if self.has_depth:
            self.add_annotation(
                Annotation(
                    name=ANNOTATION_DEPTH,
                    f=self._annotate_depth_if_supported,
                    layer=IMAGE_OVERRIDE_LAYER,
                    enabled=False,
                )
            )
        self.add_annotation(Annotation(name=ANNOTATION_TIMESTAMP, f=self._annotate_timestamp, layer=BASE_LAYER))
        self.add_annotation(
            Annotation(name=ANNOTATION_UNDISTORTED, f=self._annotate_undistorted_if_undistorted, layer=BASE_LAYER + 1)
        )
        self.add_annotation(Annotation(name=ANNOTATION_FPS, f=self._annotate_fps, layer=BASE_LAYER + 2))
        self.add_annotation(
            Annotation(name=ANNOTATION_FOCUS, f=self._annotate_focus, layer=BASE_LAYER + 3, enabled=False)
        )
        self.add_annotation(
            Annotation(name=ANNOTATION_BRIGHTNESS, f=self._annotate_brightness, layer=BASE_LAYER + 4, enabled=False)
        )

        self.video_recorder_unannotated = VideoRecorder(self.short_name + "_unannotated", fps=100)
        self.video_recorder_annotated = VideoRecorder(self.short_name + "_annotated", fps=self._preview_fps_limit)
        self.frame_sequence_recorder_unannotated = FrameSequenceRecorder(
            self.short_name + "_unannotated", frame_capture_fps
        )
        self.frame_sequence_recorder_annotated = FrameSequenceRecorder(
            self.short_name + "_annotated", frame_capture_fps
        )

        self._fps = MovingAverage(initial_value=0, smooth_factor=0.99)
        self._preview_fps = MovingAverage(initial_value=0, smooth_factor=0.9)
        self._last_preview_time_ms = maka_control_timestamp_ms()
        self._last_published_time_ms = 0

        # Use a single image buffer. A collections-based buffer is chosen for
        # future expandability and to maintain implementation symmetry with
        # other modules like the Visual Cortex.
        self._single_image_buffer: RollingOrderedBuffer[CamImage] = RollingOrderedBuffer(
            maxlen=1, ordering_func=lambda cam_image: cam_image.timestamp_ms
        )

        self._properties: Optional[Any] = None

        self._lens_undistortion_mapping_parameters: LensUndistortionMappingParametersType = None

        self._color_correction_profile: Optional[ColorCorrectionProfile] = None
        self._pixel_angle_rad: Optional[Tuple[float, float]] = None
        self._top_left_angle_rad: Optional[Tuple[float, float]] = None

        # If media directory, start background annotation task.
        if self.fs.abs_media_dir:
            task = lib.common.tasks.start(name=f"{self.device_path}/annotation_loop", target=self.annotation_loop)
            self.add_subtask(task)

        self._metadata_writer = MetadataWriter()

    def dump_callback(self) -> Dict[str, Any]:
        resolution: Tuple[int, int] = (0, 0)
        try:
            resolution = self.resolution or resolution
        except Exception:
            LOG.exception(f"{self.device_path} Gracefully returning 0x0 resolution after lookup failure")

        result: Dict[str, Any] = {
            "status": self.status_callback(),
            "has_depth": self.has_depth,
            "fps": self.fps,
            "preview_fps": self.preview_fps,
            "preview_fps_limit": self.preview_fps_limit,
            "resolution": resolution,
            "annotations": [a.json for a in self.annotations],
        }
        if self.ppi is not None:
            result["ppi"] = self.ppi
        return result

    @property
    def fps(self) -> float:
        return self._fps.value

    @property
    def preview_fps(self) -> float:
        return self._preview_fps.value

    @property
    def ppi(self) -> Optional[float]:
        return self._ppi

    @property
    def has_depth(self) -> bool:
        return False

    def set_focusing_mode(self, focusing_mode: bool) -> None:
        pass

    @property
    def focusing_mode(self) -> bool:
        return False

    def handle_update_preview_limit(self, fps: float) -> None:
        assert fps >= 0
        self._preview_fps = MovingAverage(initial_value=0, smooth_factor=0.9)
        self._preview_fps_limit = fps

    # Cam provides empty versions of these save functions, override if you are configurable
    def copy_settings_to(self, dir_: str) -> None:
        pass

    def get_camera_config_files(self) -> Optional[List[str]]:
        return None

    @property
    def active_settings_filepath(self) -> str:
        return ""

    def save_settings(
        self, save_directory: Optional[str] = None, save_name: Optional[str] = None, logging: bool = True
    ) -> str:
        pass

    def change_camera_config_file(self, filename: Optional[str] = None) -> None:
        pass

    @property
    def preview_scale(self) -> float:
        return self._preview_scale

    @property
    def preview_fps_limit(self) -> float:
        return self._preview_fps_limit

    @property
    def latency_ms(self) -> int:
        return self._latency_ms

    @property
    def pixel_count(self) -> int:
        res = self.resolution
        assert res is not None
        return res[0] * res[1]

    def last(self) -> Optional[CamImage]:
        """
        :return: the last frame captured, or None if no frame yet captured
        """
        return self._single_image_buffer.last()

    @property
    def subscription(self) -> SubscriberChannel[CamImage]:
        return CallbackSubscriberChannel(self.topic, callback=lambda: self.last())

    def _tick_fps(self, current_fps: float, last_elapsed_ms: int) -> float:
        latest_fps = 1000 / last_elapsed_ms if last_elapsed_ms > 0 else 0
        smoothing = 0.9  # larger = more smoothing
        return (current_fps * smoothing) + (latest_fps * (1.0 - smoothing))

    @property
    def annotations(self) -> SortedValuesView:
        return [v for v in self._annotations_by_layer.values()]

    def _annotate(self, cam_image: CamImage) -> CamImage:
        with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.id}", "Annotation"):
            for level, annotation in self._annotations_by_layer.items():
                # don't kill thread on exception, just log exception since we are doing annotations
                annotation.safe_apply_if_enabled(cam_image)

        return cam_image

    def add_annotation(self, annotation: Annotation) -> None:
        """
        Attach an annotation.

        :param annotation: the annotation function
        """
        assert (
            annotation.layer not in self._annotations_by_layer
        ), "{} Annotation {} already attached by layer as: {}".format(
            self.id, annotation, self._annotations_by_layer[annotation.layer]
        )
        assert (
            annotation.name not in self._annotations_by_name
        ), "{} Annotation {} already attached by name as: {}".format(
            self.id, annotation, self._annotations_by_name[annotation.name]
        )
        LOG.debug(f"{self.id} Attaching annotation: {annotation.name} on layer {annotation.layer}")

        self._annotations_by_name[annotation.name] = annotation
        self._annotations_by_layer[annotation.layer] = annotation

    def del_annotation(self, name: Optional[str] = None, layer: Optional[int] = None) -> None:
        """
        Delete an annotation from this camera
        """
        assert (name is not None) ^ (layer is not None), "Must pass name XOR layer"
        if name is not None:
            annotation = self._annotations_by_name[name]
            del self._annotations_by_layer[annotation.layer]
            del self._annotations_by_name[annotation.name]
        else:
            annotation = self._annotations_by_layer[layer]
            del self._annotations_by_layer[annotation.layer]
            del self._annotations_by_name[annotation.name]

    def enable_annotation(self, name: Optional[str] = None, layer: Optional[int] = None) -> None:
        """
        Enable an annotation. No action if already enabled.
        """
        assert (name is not None) ^ (layer is not None), "Must pass name XOR layer"
        annotation = self._annotations_by_layer[layer] if layer is not None else self._annotations_by_name[name]
        annotation.enable()

    def disable_annotation(self, name: Optional[str] = None, layer: Optional[int] = None) -> None:
        """
        Disable an annotation. No action if already disabled.
        """
        assert (name is not None) ^ (layer is not None), "Must pass name XOR layer"
        annotation = self._annotations_by_layer[layer] if layer is not None else self._annotations_by_name[name]
        annotation.disable()

    def toggle_annotation(self, name: Optional[str] = None, layer: Optional[int] = None) -> None:
        """
        Toggle an annotation.
        """
        assert (name is not None) ^ (layer is not None), "Must pass name XOR layer"
        annotation = self._annotations_by_layer[layer] if layer is not None else self._annotations_by_name[name]
        annotation.toggle()

    def _annotate_depth_if_supported(self, cam_image: CamImage) -> np.ndarray:
        image = cam_image.image_bgr
        if cam_image.depth is not None:
            image[:, :, :] = cv2.cvtColor(cam_image.depth, cv2.COLOR_GRAY2BGR)
        return image

    def _annotate_undistorted_if_undistorted(self, cam_image: CamImage) -> np.ndarray:
        image = cam_image.image_bgr
        if self._lens_undistortion_mapping_parameters is not None:
            lib.common.annotate.annotate_text_line(image, "Undistorted", y_index=1, color=lib.common.annotate.BGR_GREEN)
        return image

    def _annotate_focus(self, cam_image: CamImage) -> np.ndarray:
        image = cam_image.image_bgr
        focus = round(cam_image.compute_focus(), 4)

        text = f"Focus: {focus}"
        font_scale = 3
        thickness = 3
        padding = 5
        font = cv2.FONT_HERSHEY_COMPLEX_SMALL
        text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
        assert self.resolution is not None

        # make 9-crop
        cropped = tiled_9crop(image, 10, 10)
        image[:, :, :] = cv2.resize(cropped, (image.shape[1], image.shape[0]))
        cv2.putText(
            image,
            text,
            (self.resolution[0] - text_size[0] - padding, 95),
            font,
            font_scale,
            lib.common.annotate.BGR_RED,
            thickness,
        )

        return image

    def _annotate_brightness(self, cam_image: CamImage) -> np.ndarray:
        image = cam_image.image_bgr
        brightness = round(self._compute_brightness(image) * 100, 2)
        text = f"Bright: {brightness} %"
        font_scale = 3
        thickness = 3
        padding = 5
        font = cv2.FONT_HERSHEY_COMPLEX_SMALL
        text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
        assert self.resolution is not None
        cv2.putText(
            image,
            text,
            (self.resolution[0] - text_size[0] - padding, 175),
            font,
            font_scale,
            lib.common.annotate.BGR_RED,
            thickness,
        )
        return image

    def _annotate_timestamp(self, cam_image: CamImage) -> np.ndarray:
        # don't bother with lock because image could have changed by now anyway. Need to keep them paired better
        return lib.common.annotate.timestamp(cam_image.image_bgr, timestamp_ms=cam_image.timestamp_ms)

    def _annotate_fps(self, cam_image: CamImage) -> np.ndarray:
        image = cam_image.image_bgr
        font = cv2.FONT_HERSHEY_COMPLEX_SMALL
        fps_text = f"{self._fps:.2f}"
        fps_font_scale = 1
        thickness = 1
        fps_text_size = cv2.getTextSize(fps_text, font, fps_font_scale, thickness)[0]
        padding = 5

        y = fps_text_size[1] + padding

        assert self.resolution is not None
        cv2.putText(
            image,
            fps_text,
            (self.resolution[0] - fps_text_size[0] - padding, y),
            fontFace=font,
            fontScale=fps_font_scale,
            color=lib.common.annotate.BGR_PINK,
            thickness=thickness,
        )

        # annotate preview fps
        preview_fps_text = "{:.2f}".format(self._preview_fps)
        preview_fps_text_size = cv2.getTextSize(preview_fps_text, font, fps_font_scale, thickness)[0]
        y += preview_fps_text_size[1] + padding
        assert self.resolution is not None
        cv2.putText(
            image,
            preview_fps_text,
            (self.resolution[0] - preview_fps_text_size[0] - padding, y),
            fontFace=font,
            fontScale=fps_font_scale,
            color=lib.common.annotate.BGR_PINK,
            thickness=thickness,
        )

        return image

    def _publish(self, cam_image: CamImage) -> None:
        """
        Make the given camera image available
        """
        self._single_image_buffer.publish(output=cam_image)

    def _publish_cam_image(self, frame_timestamp_ms: int, cam_image: CamImage) -> None:
        self._publish(cam_image)
        # capture unannotated frame
        self.frame_sequence_recorder_unannotated.push_cam_image(cam_image)
        self.video_recorder_unannotated.push_cam_image(cam_image)

        elapsed_ms = frame_timestamp_ms - self._last_published_time_ms
        if elapsed_ms > 0:
            self._fps.update(1000 / elapsed_ms)
        self._last_published_time_ms = frame_timestamp_ms

    def _new_frame_from_buffer(
        self, frame: ImageBuffer, depth_buf: Optional[ImageBuffer] = None, frame_timestamp_ms: Optional[int] = None,
    ) -> None:
        """
        Loop function to capture new frames at high fps
        Does not support any image transform, they should have all been already applied
        """
        frame_timestamp_ms = frame_timestamp_ms if frame_timestamp_ms is not None else maka_control_timestamp_ms()

        # This is temporary until we move that to GPU
        buf = frame
        img = buf.get_numpy_array(buf)
        assert img.shape[-1] == 3, f"Wrong image shape: {img.shape}"
        if self._transpose:
            name = frame.export_path()
            with duration_perf_recorder(f"{PerfCategory.IMAGE}:{img.shape}", "Transpose2"):
                with duration_perf_recorder(f"{PerfCategory.IMAGE}:{img.shape}", "BufferConstructT"):
                    buf = ImageBuffer(f"{name}T", (img.shape[1], img.shape[0], img.shape[2]))
                cv2.transpose(img, dst=buf.get_numpy_array(buf))
            frame.release_path(name)

        cam_image = BufferedCamImage(
            device_path=self.device_path,
            camera_id=self.id,
            timestamp_ms=frame_timestamp_ms,
            ppi=self._ppi,
            buf=buf,
            depth_buf=depth_buf,
        )

        self._publish_cam_image(frame_timestamp_ms, cam_image)

    def _new_frame(
        self,
        frame: np.ndarray,
        depth_frame: Optional[np.ndarray] = None,
        frame_timestamp_ms: Optional[int] = None,
        focus_value: Optional[float] = None,
    ) -> None:
        """
        Loop function to capture new frames at high fps
        """
        frame_timestamp_ms = frame_timestamp_ms if frame_timestamp_ms is not None else maka_control_timestamp_ms()

        # If we are getting the same image back then short circuit (only known case is Unity)
        last_cam_image: Optional[CamImage] = self.last()
        if last_cam_image is not None and last_cam_image.timestamp_ms == frame_timestamp_ms:
            return

        with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.id}", "ApplyTransform"):
            frame = apply_image_transforms(
                frame,
                mirror=self._mirror,
                flip=self._flip,
                transpose=self._transpose,
                recover_blue=self._recover_blue,
                crop=self._crop,
                lens_undistortion_mapping_parameters=self._lens_undistortion_mapping_parameters,
                color_correction_profile=self._color_correction_profile,
                top_left_angle_rad=self._top_left_angle_rad,
                pixel_angle_rad=self._pixel_angle_rad,
            )
            if depth_frame is not None:
                depth_frame = apply_image_transforms(
                    depth_frame,
                    mirror=self._mirror,
                    flip=self._flip,
                    transpose=self._transpose,
                    crop=self._crop,
                    lens_undistortion_mapping_parameters=self._lens_undistortion_mapping_parameters,
                )
        cam_image = CamImage(
            device_path=self.device_path,
            camera_id=self.id,
            timestamp_ms=frame_timestamp_ms,
            ppi=self._ppi,
            image_bgr=frame,
            depth=depth_frame,
            focus_value=focus_value,
        )

        self._publish_cam_image(frame_timestamp_ms, cam_image)

    def annotation_loop(self) -> None:
        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None
        while True:
            task.tick()
            try:
                cam_image = self.next(timeout_ms=1000)
            except lib.common.time.time.TimeoutException:
                # loop until image is available
                continue

            frame_timestamp_ms = cam_image.timestamp_ms

            task.tick()

            preview_elapsed_ms = frame_timestamp_ms - self._last_preview_time_ms

            preview_elapsed_ms = 1 if preview_elapsed_ms < 1 else preview_elapsed_ms

            if self._always_save_unannotated:
                self.scale_save(file_suffix=".jpg", image=cam_image.image_bgr, is_annotated=False, quality=80)
            self._preview_fps.update(1000 / preview_elapsed_ms)
            self._last_preview_time_ms = frame_timestamp_ms

            # annotate frame
            annotated_cam_image = self._annotate(cam_image.copy())

            # record annotated frame
            self.video_recorder_annotated.push_cam_image(annotated_cam_image)
            self.frame_sequence_recorder_annotated.push_cam_image(annotated_cam_image)

            with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.id}", "ScaleSave"):
                self.scale_save(file_suffix=".jpg", image=annotated_cam_image.image_bgr, quality=80)

    # TODO: timeout_ms of 1000ms fails for --virtual seedy. Need to make client calibration code robust to cam timeouts
    def next(self, timeout_ms: int = 5000) -> CamImage:
        """
        Returns the next CamImage

        raises CamTimeoutException if there is a timeout in retrieving the next camera image
        """
        return self._single_image_buffer.next(timeout_ms=timeout_ms)

    def capture(self, annotated: bool = False) -> np.ndarray:
        cam_image: CamImage = self.next()

        if annotated:
            # TODO consolidate this annotation with the annotate in the frame loop
            cam_image = self._annotate(cam_image.copy())

        # TODO migrate all clients to CamImage funcs
        return cam_image.image_bgr

    def capture_stack(self, count: int = 5, blur: bool = False) -> np.ndarray:
        assert count > 0
        image = None
        for i in range(count):
            image2 = self.capture()
            if image is None:
                image = np.zeros(image2.shape, dtype=np.uint8)
            assert image.shape == image2.shape, "Mismatch shape! {} vs {}".format(image.shape, image2.shape)
            if blur:
                image2 = cv2.GaussianBlur(image2, (11, 11), 0)
            image = cv2.addWeighted(image2, 1 / count, image, 1, 0)
        assert image is not None
        return image

    def get_parameters(self, ids: List[str], exposure_time_limit: float = 150000) -> Dict[str, Tuple[float, float]]:
        if not self._properties:
            return {}
        hyperparamers = {}
        for node_id in ids:
            node = self._properties.get_node_by_id(node_id)
            if node_id == "ExposureTime":
                # Setting exposure time limit to help algorithm and avoid exception
                # "genicam.TimeoutException: Grab timed out"
                hyperparamers[node_id] = (node.Min, min(node.Max, exposure_time_limit))
            else:
                hyperparamers[node_id] = (node.Min, node.Max)

        return hyperparamers

    def capture_frame(
        self,
        subdir: str,
        annotated: bool = False,
        filename: str = ".png",
        geo_lla_msg: Optional[GeopositionLatLonAltMessage] = None,
        geo_ecef_msg: Optional[GeopositionEcefMessage] = None,
    ) -> None:
        cam_image: CamImage = self.next()

        if annotated:
            # TODO consolidate this annotation with the annotate in the frame loop
            cam_image = self._annotate(cam_image.copy())

        # save bgr image
        path = self.save(filename_suffix=filename, image=cam_image.image_bgr, subdir=subdir, is_annotated=annotated)

        # save depth image
        if self.has_depth:
            filename_npz = os.path.splitext(filename)[0] + ".npz"
            self.save_npz(filename_npz, cam_image, subdir=subdir)

        geo_lla = GeoLLAData(0.0, 0.0, 0.0, 0)
        geo_ecef = GeoECEFData(0.0, 0.0, 0.0, 0)

        if geo_lla_msg is not None:
            geo_lla = GeoLLAData(geo_lla_msg.lat, geo_lla_msg.lon, geo_lla_msg.alt, geo_lla_msg.timestamp_ms)

        if geo_ecef_msg is not None:
            geo_ecef = GeoECEFData(geo_ecef_msg.x, geo_ecef_msg.y, geo_ecef_msg.z, geo_ecef_msg.timestamp_ms)

        assert path is not None
        self._metadata_writer.write_metadata(
            image_path=os.path.splitext(path)[0],
            lla=geo_lla,
            ecef=geo_ecef,
            timestamp_ms=cam_image.timestamp_ms,
            cam_id=cam_image.camera_id,
            capture_method="frame-capture",
            ppi=cam_image.ppi if cam_image.ppi is not None else 0,
        )

    def set_geo_subscriver_channels(
        self,
        lla_channel: SubscriberChannel[GeopositionLatLonAltMessage],
        ecef_channel: SubscriberChannel[GeopositionEcefMessage],
    ) -> None:
        self.frame_sequence_recorder_annotated.set_geo_subscriver_channels(lla_channel, ecef_channel)
        self.frame_sequence_recorder_unannotated.set_geo_subscriver_channels(lla_channel, ecef_channel)

    def start_frame_capture(
        self,
        subdir: str,
        is_annotated: bool,
        fps: Optional[float] = Recorder.DEFAULT_RECORDER_FPS,
        cam_buffer_limit: Optional[int] = None,
    ) -> None:
        if is_annotated:
            fsr = self.frame_sequence_recorder_annotated
        else:
            fsr = self.frame_sequence_recorder_unannotated
            if fps is not None and fps > self._preview_fps_limit:
                LOG.info(f"Updating preview FPS to {fps} for frame capturing")
                self.handle_update_preview_limit(fps)

        fsr.set_resolution(self.resolution)  # not currently used
        fsr.start(subdir, fps, cam_buffer_limit)

    def stop_frame_capture(self, is_annotated: bool = False) -> None:
        if is_annotated:
            self.frame_sequence_recorder_annotated.stop()
        else:
            self.frame_sequence_recorder_unannotated.stop()

    def start_record(
        self,
        subdir: str,
        is_annotated: bool,
        fps: Optional[float] = Recorder.DEFAULT_RECORDER_FPS,
        cam_buffer_limit: Optional[int] = None,
    ) -> None:
        if is_annotated:
            self.video_recorder_annotated.start(subdir, fps, cam_buffer_limit)
            if fps is not None and fps > self._preview_fps_limit:
                LOG.info(f"Updating preview FPS to {fps} for video recording")
                self.handle_update_preview_limit(fps)
        else:
            self.video_recorder_unannotated.start(subdir, fps, cam_buffer_limit)

    def stop_record(self, is_annotated: bool = False) -> None:
        if is_annotated:
            self.video_recorder_annotated.stop()
        else:
            self.video_recorder_unannotated.stop()

    def scale_save(self, *, file_suffix: str, image: np.ndarray, is_annotated: bool = True, quality: int = 95) -> None:
        if self.preview_scale is not None and self.preview_scale != 1:
            image = cv2.resize(image, dsize=(0, 0), fx=self.preview_scale, fy=self.preview_scale)

        self.save(
            filename_suffix=file_suffix,
            image=image,
            is_annotated=is_annotated,
            saver=partial(_imwrite, quality=quality),
        )

    def save(
        self,
        *,
        filename_suffix: str,
        image: T,
        is_annotated: bool = False,
        subdir: Optional[str] = None,
        saver: Callable[[str, T], None] = _imwrite,  # type: ignore
    ) -> Optional[str]:
        save_dir = self.fs.abs_media_dir if subdir is not None else self.fs.abs_media_stream_dir
        if subdir is not None:
            save_dir = os.path.join(save_dir, subdir)
            os.makedirs(save_dir, exist_ok=True)
        filename = camera_filename(camera_id=self.short_name, filename=filename_suffix, annotated=is_annotated)
        tmpfilename = tmp_filename(filename)

        tmp_file = os.path.join(save_dir, tmpfilename)
        try:
            saver(tmp_file, image)
        except:  # noqa
            LOG.exception(
                "%s Unable to write image to %s. Try checking permissions on %s",
                self.short_name,
                tmp_file,
                self.fs.abs_media_dir,
            )
            return None
        os.chmod(tmp_file, 0o644)
        path = os.path.join(save_dir, filename)
        shutil.move(tmp_file, path)

        return path

    def save_npz(
        self, file_suffix: str, image: CamImage, subdir: Optional[str] = None, is_annotated: bool = True
    ) -> Optional[str]:

        return self.save(
            filename_suffix=file_suffix,
            image=image,
            subdir=subdir,
            is_annotated=is_annotated,
            saver=cam_image_depth_save,
        )

    def config_save(self) -> Dict[str, Any]:
        assert self._properties
        return {pname: self._properties[pname].value for pname in list(self._properties.keys())}

    def get_properties(self) -> Dict[str, Any]:
        if not self._properties:
            return {}
        return cast(Dict[str, Any], self._properties.get_all())

    def set_property(self, **kwargs: Any) -> None:
        assert self._properties
        self._properties.set(**kwargs)

    def stop(self) -> None:
        super().stop()
        self.stop_record(is_annotated=True)
        self.stop_record(is_annotated=False)
        self.stop_frame_capture(is_annotated=True)
        self.stop_frame_capture(is_annotated=False)

    @staticmethod
    def _compute_brightness(frame: np.ndarray) -> float:
        return cast(float, np.percentile(frame, 95)) / 255.0

    @staticmethod
    def _compute_fps(exposure_time_us: int) -> float:
        exposure_time_s = exposure_time_us * 1e-6
        # calculate fps
        fps = 1 / exposure_time_s
        # add extra time buffer for other operations
        fps /= 10
        return fps

    def auto_brightness_configuration(
        self, exposure_max: float = 1e6, brightness_min: float = 0.85, brightness_max: float = 0.92
    ) -> None:
        LOG.info(f"{self.id} Starting brightness configuration.")
        assert self._properties
        exposure = self._properties.get_node_by_id("ExposureTime").Value
        delta = 100.0
        coefficient = 2.0

        while delta < exposure_max and coefficient > 1:
            temp_exposure = exposure + delta
            if temp_exposure <= exposure_max:
                preferable_fps = self._compute_fps(temp_exposure)
                self.set_property(key="ExposureTime", value=temp_exposure)
                time.sleep(3.0 / preferable_fps)
                temp_brightness = self._compute_brightness(self.capture())
                if temp_brightness < brightness_max:
                    exposure = temp_exposure
                    if brightness_min < temp_brightness:
                        break
                else:
                    delta = delta / 2
                    coefficient *= 0.9
                LOG.info(
                    f"{self.id} Valid exposure: {exposure}. Temporary exposure: {temp_exposure}. "
                    f"Temporary brightness: {temp_brightness}."
                )
            else:
                delta = delta / 2
                coefficient *= 0.9
            delta *= coefficient

        brightness = self._compute_brightness(self.capture())
        LOG.info(
            f"{self.id} Finished brightness configuration. Exposure time: {exposure}. " f"Brightness: {brightness}"
        )

    @property
    def lens_undistortion_mapping_parameters(self) -> LensUndistortionMappingParametersType:
        return self._lens_undistortion_mapping_parameters

    def set_lens_undistortion_mapping_parameters(
        self, lens_undistortion_mapping_parameters: LensUndistortionMappingParametersType
    ) -> None:
        self._lens_undistortion_mapping_parameters = lens_undistortion_mapping_parameters

    def set_color_correction_profile(self, profile: ColorCorrectionProfile) -> None:
        self._color_correction_profile = profile

    @property
    def color_correction_profile(self) -> Optional[ColorCorrectionProfile]:
        return self._color_correction_profile

    def set_pixel_angle_rad(self, pixel_angle_rad: Tuple[float, float]) -> None:
        self._pixel_angle_rad = pixel_angle_rad

    @property
    def pixel_angle_rad(self) -> Optional[Tuple[float, float]]:
        return self._pixel_angle_rad

    def set_top_left_angle_rad(self, angle_rad: Tuple[float, float]) -> None:
        self._top_left_angle_rad = angle_rad

    @property
    def top_left_angle_rad(self) -> Optional[Tuple[float, float]]:
        return self._top_left_angle_rad

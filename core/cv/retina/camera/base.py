from abc import ABC, abstractmethod
from typing import Optional, <PERSON><PERSON>

from lib.common.image import CamImage


class Camera(ABC):
    """
    The base interface for a camera.
    """

    @property
    @abstractmethod
    def resolution(self) -> Optional[Tuple[int, int]]:
        pass

    @property
    @abstractmethod
    def has_depth(self) -> bool:
        pass

    # TODO: async capture/grab interface for getting a CamImage
    # (This is the main one used by Cam clients today so this one was pulled into this barebones interface)
    @abstractmethod
    def last(self) -> Optional[CamImage]:
        pass

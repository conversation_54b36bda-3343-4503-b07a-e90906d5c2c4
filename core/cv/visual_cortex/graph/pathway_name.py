from core.cv.visual_cortex.graph.pathway_output import PathwayOutput
from core.model.node.type import NodeType
from core.model.path import Instance


class PathwayName(str):
    @staticmethod
    def from_single_source(*, source_id: str, output: PathwayOutput) -> "PathwayName":
        # if someone uses a full topic as their source id, e.g. "/retina/camera:front_right"
        # then trim the portion that is implied (i.e. of course C<PERSON> is using a camera from the retina)
        neat_source_id: str = source_id.replace(
            f"/{NodeType.RETINA.serialize()}/{NodeType.CAMERA.serialize()}{Instance.prefix()}", ""
        )
        return PathwayName(f"{output.value}_{neat_source_id}")

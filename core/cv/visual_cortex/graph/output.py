from typing import Any, Dict, Generic, List, Optional, Sequence, Tuple, TypeVar, cast

import numpy as np

from core.cv.visual_cortex.model.ungenerated import FurrowSlope, TimestampedSlope
from cv.optical_flow.tracking_utils import FlowAccumulator
from cv.runtime.cpp import shmem_types_python
from lib.common.time import TimestampedObject
from trajectory.pybind.trajectory_python import Trajectory


class FurrowsContext:
    def __init__(self) -> None:
        self.wheel_x: float = 0
        self.wheel_y: float = 0


class ComputerVisionNodeOutput(TimestampedObject):
    """
    The base class for Computer Vision outputs
    """

    def __init__(self, *, timestamp_ms: int = 0):
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)


Tpath = TypeVar("Tpath", bound=ComputerVisionNodeOutput)


class ComputerVisionPathwayOutput(TimestampedObject, Generic[Tpath]):
    def __init__(self, output: Tpath):
        self._output = output

    @property
    def output(self) -> Tpath:
        return self._output

    @property
    def timestamp_ms(self) -> int:
        return self._output.timestamp_ms


T = TypeVar("T")


class NodeInput(Generic[T]):
    def __init__(self, computer_vision_output: Optional[T] = None):
        self._computer_vision_output = computer_vision_output

    @property
    def computer_vision_output(self) -> Optional[T]:
        return self._computer_vision_output


class LinesOutput(ComputerVisionNodeOutput):
    """
    A collection of lines
    """

    def __init__(self, *argv: Any, lines: Optional[List[List[Tuple[float, float]]]], **kwargs: Any):
        super().__init__(*argv, **kwargs)
        self._lines: Optional[List[List[Tuple[float, float]]]] = lines

    @property
    def lines(self) -> Optional[List[List[Tuple[float, float]]]]:
        return self._lines


class ContoursOutput(ComputerVisionNodeOutput):
    """
    A collection of contours
    """

    def __init__(
        self,
        *argv: Any,
        contours: Optional[List[np.ndarray]] = None,
        slopes: Optional[List[List[np.ndarray]]] = None,
        **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)
        # convert from
        #
        # [
        #  array(
        #        [[[ 1,  2]],
        #         [[ 3,  4]]],
        #        dtype=int32
        #        )
        # ]
        #
        # to
        #
        # [
        #  array(
        #        [[ 1,  2],
        #         [ 3,  4]]
        #        )
        # ]
        self._contours: List[np.ndarray] = (
            [np.rint(contour).astype(int).reshape((-1, 2)) for contour in contours] if contours is not None else []
        )
        self._slopes: List[List[np.ndarray]] = slopes if slopes is not None else []

    @property
    def contours(self) -> List[np.ndarray]:
        return self._contours

    @property
    def slopes(self) -> List[List[np.ndarray]]:
        return self._slopes


class SlopesOutput(ComputerVisionNodeOutput):
    def __init__(self, slopes: Sequence[TimestampedSlope], **kwargs: Any):
        super().__init__(**kwargs)
        # Sequence chosen so that Slope can be subclassed later
        # Sequence is covariant, List is invariant
        self._slopes: Sequence[TimestampedSlope] = slopes

    @property
    def slopes(self) -> Sequence[TimestampedSlope]:
        return self._slopes


class FurrowsOutput(SlopesOutput):
    def __init__(self, slopes: Sequence[FurrowSlope], **kwargs: Any):
        super().__init__(slopes=slopes, **kwargs)

    @property
    def closest_furrow(self) -> Optional[FurrowSlope]:
        return self.slopes[0] if len(self.slopes) > 0 else None

    @property
    def slopes(self) -> Sequence[FurrowSlope]:
        # we know from the __init__ syntax that this will be FurrowSlope
        #
        # This is written this way because there will be other lines besides Furrows that we want to follow
        return cast(Sequence[FurrowSlope], self._slopes)


class OpticalFlowOutput(ComputerVisionNodeOutput):
    def __init__(
        self,
        *argv: Any,
        flow: Optional[np.ndarray] = None,
        flow_accumulator: Optional[FlowAccumulator] = None,
        **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)
        self._flow = flow
        self._flow_accumulator = flow_accumulator
        self._mean_flow: Optional[np.ndarray] = None

    @property
    def flow(self) -> Optional[np.ndarray]:
        return self._flow

    @property
    def flow_accumulator(self) -> Optional[FlowAccumulator]:
        return self._flow_accumulator

    @property
    def mean_flow(self) -> np.ndarray:
        # This is a long computation so we cache it
        if self._mean_flow is None:
            flow = self.flow
            assert flow is not None
            self._mean_flow = np.mean(np.mean(flow, axis=1), axis=0)
        return self._mean_flow


class WeedTrackingOutput(ComputerVisionNodeOutput):
    def __init__(
        self,
        *argv: Any,
        trajectories: List[Trajectory],
        newly_tracked_ids: List[int] = [],
        deepweed_flow_offsets_xy: Dict[int, Tuple[float, float]] = {},
        **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)
        self._trajectories = trajectories
        self._newly_tracked_ids = newly_tracked_ids
        self._deepweed_flow_offsets_xy = deepweed_flow_offsets_xy

    @property
    def trajectories(self) -> List[Trajectory]:
        return self._trajectories

    @property
    def newly_tracked_ids(self) -> List[int]:
        return self._newly_tracked_ids

    @property
    def deepweed_flow_offsets_xy(self) -> Dict[int, Tuple[float, float]]:
        return self._deepweed_flow_offsets_xy


class DeepweedOutput(ComputerVisionNodeOutput):
    def __init__(self, *argv: Any, raw: Optional[shmem_types_python.DeepweedOutput], **kwargs: Any):
        super().__init__(*argv, **kwargs)
        self._raw = raw

    @property
    def raw(self) -> Optional[shmem_types_python.DeepweedOutput]:
        return self._raw

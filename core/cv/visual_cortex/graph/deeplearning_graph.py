from typing import Any, List, Optional, Union

import torch

import lib.common.logging
from config.client.cpp.config_client_python import get_global_config_subscriber
from core.cv.retina.camera.node import Cam
from core.cv.visual_cortex.graph.deeplearning.furrows import FurrowsDeepLearningNode
from core.cv.visual_cortex.graph.graph import ComputerVisionGraph
from core.cv.visual_cortex.graph.output import FurrowsContext, FurrowsOutput, NodeInput, WeedTrackingOutput
from core.cv.visual_cortex.graph.pathway import ComputerVisionPathway
from core.cv.visual_cortex.graph.pathway_name import PathwayName
from core.cv.visual_cortex.graph.pathway_output import PathwayOutput
from core.cv.visual_cortex.graph.utility_nodes.subscription import SubscriptionNode
from core.cv.visual_cortex.graph.weed_tracking.weed_tracking import WeedTrackingNode
from cv.runtime.client import CVRuntimeClient
from cv.utils.cupy import CUPY_IMPORTED
from lib.common.devices.boards.nofx.mock_nofx_board_device import MockNoFXBoardDevice
from lib.common.devices.boards.nofx.nofx_board_device import NoFXBoardDevice
from lib.common.geometric.cpp.geometric_python import get_crop_global_height_estimator, get_global_height_estimator
from lib.common.geometric.geometric_calibration import get_predict_cam_calibration
from lib.common.image import CamImage
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.protocol.channel.callback import CallbackSubscriberChannel
from lib.common.tasks.manager import get_event_loop_by_name

LOG = lib.common.logging.get_logger(__name__)

NUMBER_CUDA_DEVICES = 0

# CUPY_IMPORTED will be true if cupy is imported, which only happens if nvidia drivers are installed
# So this is a hacky way to get around the fact that torch.cuda.is_available() fails when nvidia drivers aren't installed
if CUPY_IMPORTED:
    NUMBER_CUDA_DEVICES = torch.cuda.device_count()


async def configure_height_estimator(predict_ids: List[str], number_lanes: int) -> None:
    width, _ = get_predict_cam_calibration().image_size
    get_global_height_estimator().configure(predict_ids, number_lanes, width)
    get_crop_global_height_estimator().configure(predict_ids, number_lanes, width)


class DeeplearningComputerVisionGraph(ComputerVisionGraph):
    def __init__(
        self,
        *,
        cv_runtime_client: CVRuntimeClient,
        nofx_board_device: Optional[Union[NoFXBoardDevice, MockNoFXBoardDevice]] = None,
        is_virtual: bool = False,
        boot_weed_tracking: bool = False,
        **kwargs: Any,
    ):
        self._cv_runtime_client = cv_runtime_client
        self._nofx_board_device = nofx_board_device
        self._boot_weed_tracking = boot_weed_tracking
        self._is_virtual = is_virtual
        super().__init__(**kwargs)

    def _register_furrows_pathway(self, cam: Cam, furrows_context: FurrowsContext) -> None:
        subscription_node: SubscriptionNode[NodeInput[None]] = SubscriptionNode(cam.subscription)

        furrows_deep_learning_node = FurrowsDeepLearningNode(
            cv_runtime_client=self._cv_runtime_client, camera=cam, furrows_context=furrows_context,
        )

        furrows_deep_learning_pathway = ComputerVisionPathway[FurrowsOutput](
            name=PathwayName.from_single_source(source_id=cam.subscription.topic, output=PathwayOutput.FURROWS),
            output=PathwayOutput.FURROWS,
            subscription_node=subscription_node,
            computer_vision_node=furrows_deep_learning_node,
        )

        self._register_pathways([furrows_deep_learning_pathway])

    def _create_pathways(
        self,
        enable_pathways: List[PathwayName],
        back_left_cam: Optional[Cam] = None,
        back_right_cam: Optional[Cam] = None,
        front_left_cam: Optional[Cam] = None,
        front_right_cam: Optional[Cam] = None,
        predict_cams: Optional[List[Cam]] = None,
    ) -> None:
        if front_left_cam is not None:
            self._register_furrows_pathway(front_left_cam, self._front_right_furrows_context)

        if front_right_cam is not None:
            self._register_furrows_pathway(front_right_cam, self._front_right_furrows_context)

        if back_left_cam is not None:
            self._register_furrows_pathway(back_left_cam, self._front_right_furrows_context)

        if back_right_cam is not None:
            self._register_furrows_pathway(back_right_cam, self._front_right_furrows_context)

        if self._boot_weed_tracking and predict_cams is not None and len(predict_cams) > 0:
            LOG.warning(
                "You are booting weed tracking in controls. This is good for debugging but causes performance issues with the rotary encoder board. Use with caution!"
            )
            pcam: Cam
            pcam_image_subscription: SubscriberChannel[CamImage]
            num_lanes = 5
            if get_global_config_subscriber().started():
                num_lanes = (
                    get_global_config_subscriber()
                    .get_config_node("aimbot", "height_estimation/num_lanes")
                    .get_uint_value()
                )
            predict_ids = [str(predict_cam.id) for predict_cam in predict_cams]
            get_event_loop_by_name().create_task(configure_height_estimator(predict_ids, num_lanes,))
            for pcam in predict_cams:
                pcam_image_subscription = CallbackSubscriberChannel(pcam.topic, callback=pcam.last)

                weed_tracking_subscription_node: SubscriptionNode[NodeInput[None]] = SubscriptionNode(
                    pcam_image_subscription
                )

                weed_tracking_node = WeedTrackingNode(
                    pcam=pcam, cv_runtime_client=self._cv_runtime_client, nofx_board_device=self._nofx_board_device,
                )

                weed_tracking_pathway = ComputerVisionPathway[WeedTrackingOutput](
                    name=PathwayName.from_single_source(
                        source_id=pcam_image_subscription.topic, output=PathwayOutput.WEED_TRACKING
                    ),
                    output=PathwayOutput.WEED_TRACKING,
                    subscription_node=weed_tracking_subscription_node,
                    computer_vision_node=weed_tracking_node,
                    maxlen_buffered_timestamp_stream=10,
                    poll_interval_ms=100,
                )

                self._register_pathways([weed_tracking_pathway])

        for p in enable_pathways:
            if p in self.pathways:
                self.enable_pathway(p)

import abc
from typing import Any, TypeVar

from core.cv.visual_cortex.graph.node import ComputerVisionNode
from core.cv.visual_cortex.graph.output import ComputerVisionNodeOutput, NodeInput

Tin = TypeVar("Tin", bound=NodeInput[Any])
Tout = TypeVar("Tout", bound=ComputerVisionNodeOutput)


class GrpcComputerVisionNode(ComputerVisionNode[Tin, Tout], abc.ABC):
    def __init__(self, *argv: Any, **kwargs: Any):
        super().__init__(*argv, **kwargs)

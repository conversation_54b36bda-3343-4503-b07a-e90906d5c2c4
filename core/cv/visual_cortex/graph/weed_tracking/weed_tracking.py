import asyncio
import math
import random
import re
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Optional, Tuple, Union, cast

import cv2
import numpy as np

import lib.common.logging
from config.client.cpp.config_client_python import get_computer_config_prefix, get_global_config_subscriber
from core.cv.retina.camera.node import Cam
from core.cv.visual_cortex.graph.node import ComputerVisionNode
from core.cv.visual_cortex.graph.output import NodeInput
from cv.runtime.client.client import CVRuntimeClient
from cv.runtime.cpp import shmem_types_python
from lib.common.annotate import BGR_GREEN, BGR_RED, BGR_YELLOW, annotate_text_line
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.db.carbon_db_lite import CarbonRobotTableBase
from lib.common.db.carbon_robot_db import CarbonRobotDBLite
from lib.common.db.records.first_weed_detection_samples import FirstWeedDetectionSamples
from lib.common.db.records.weed_tracking_error_by_distance_samples import WeedTrackingErrorByDistanceSamples
from lib.common.devices.boards.nofx.mock_nofx_board_device import MockNoFXBoardDevice
from lib.common.devices.boards.nofx.nofx_board_device import NoFXBoardDevice
from lib.common.geometric.cpp.geometric_python import GeometricCam
from lib.common.geometric.geometric_calibration import get_predict_cam_calibration
from lib.common.geometric.geometric_space import get_geometric_space
from lib.common.image import CamImage
from lib.common.perf.perf_tracker import PerfCategory, add_perf_data_point
from lib.common.time.time import TimestampedObject, maka_control_timestamp_ms
from trajectory.pybind.trajectory_python import TrackedItemCentroid, TrackedItemPosition, Trajectory
from weed_tracking.cpp.weed_tracking_python import AimbotWeedTracker, WeedTrackerOutput

LOG = lib.common.logging.get_logger(__name__)

EXECUTOR = ThreadPoolExecutor(4, thread_name_prefix="WeedTracking-")


class WeedTrackingOutputWrapper(TimestampedObject):
    def __init__(self, *, weed_tracking_output: WeedTrackerOutput):
        super().__init__(timestamp_ms=weed_tracking_output.get_timestamp_ms())
        self._weed_tracking_output = weed_tracking_output

    @property
    def trajectories(self) -> List[Trajectory]:
        return self._weed_tracking_output.get_trajectories()

    @property
    def newly_tracked_ids(self) -> List[int]:
        return self._weed_tracking_output.get_newly_tracked_ids()

    @property
    def deepweed_flow_offsets_xy(self) -> Dict[int, Tuple[float, float]]:
        return self._weed_tracking_output.get_deepweed_flow_offsets_xy()

    @property
    def number_missed_first_instances(self) -> Dict[int, int]:
        return self._weed_tracking_output.get_number_missed_first_instances()

    @property
    def vertical_offset_first_instances(self) -> Dict[int, float]:
        return self._weed_tracking_output.get_vertical_offset_first_instances()


class WeedTrackingNode(ComputerVisionNode[NodeInput[None], WeedTrackingOutputWrapper]):
    """
    A computer vision pathway for weed tracking
    """

    def __init__(
        self,
        pcam: Cam,
        cv_runtime_client: CVRuntimeClient,
        nofx_board_device: Optional[Union[NoFXBoardDevice, MockNoFXBoardDevice]] = None,
    ):
        super().__init__(
            f=self._track_weeds,
            annotation_options={
                "size": True,
                "class": True,
                "height": True,
                "score": True,
                "id": True,
                "last_deepweed_prediction": True,
            },
        )
        self._pcam = pcam

        width, height = get_predict_cam_calibration().image_size
        self._width = width
        self._height = height
        self._cv_runtime_client = cv_runtime_client
        self._config_subscriber = get_global_config_subscriber()
        self._nofx_board_device = nofx_board_device

        self._aimbot_weed_tracker: Optional[AimbotWeedTracker] = None

        self._latest_of_timestamp_ms = 0
        self._last_annotation_timestamp_ms: Optional[int] = None
        self._temp_dw_buffer = shmem_types_python.DeepweedOutput()

        self._db = CarbonRobotDBLite.get_instance()
        self._session_id = maka_control_timestamp_ms()
        if not self._config_subscriber.started():
            self._config_subscriber.add_config_tree(
                "aimbot", f"{get_computer_config_prefix()}/aimbot", "services/aimbot.yaml"
            )
            self._config_subscriber.add_config_tree("cv", f"{get_computer_config_prefix()}/cv", "services/cv.yaml")
            self._config_subscriber.add_config_tree("cv2", f"{get_computer_config_prefix()}/cv2", "services/cv.yaml")
            self._config_subscriber.add_config_tree("common", "common", "services/common.yaml")
            self._config_subscriber.start()
            self._config_subscriber.wait_until_ready()
        self._banding_tree = self._config_subscriber.get_config_node("aimbot", "banding")
        self._wt_tree = self._config_subscriber.get_config_node("aimbot", "weed_tracking")
        self._max_number_of_weeds = self._config_subscriber.get_config_node(
            "aimbot", "max_number_of_weeds"
        ).get_uint_value()

    def _make_weed_tracker(self) -> AimbotWeedTracker:
        numeric_id = int(re.sub("[^0-9]", "", self._pcam.id))

        return AimbotWeedTracker(
            self._pcam.id,
            numeric_id,
            self._width,
            self._height,
            log_ticks=True,
            geo_cam=get_geometric_space().get_device(GeometricCam, self._pcam.id),
            max_pos_mm_y=5000,
            score_images=True,
            max_number_of_weeds=self._max_number_of_weeds,
        )

    def _log_metrics(self, latest_weeds: WeedTrackingOutputWrapper) -> List[CarbonRobotTableBase]:
        for _ in range(len(latest_weeds.newly_tracked_ids)):
            add_perf_data_point(f"{PerfCategory.WEED_TRACKING}:{self._pcam.id}", "NumberDistinctWeeds", "Count", 1)

        dist_bucket_size = self._wt_tree.get_node("distance_bucket_size").get_float_value()
        err_bucket_size = self._wt_tree.get_node("error_bucket_size").get_float_value()
        for trajectory in latest_weeds.trajectories:
            id = trajectory.id()

            missed_instances = latest_weeds.number_missed_first_instances
            vertical_offsets = latest_weeds.vertical_offset_first_instances
            if id in missed_instances:
                if missed_instances[id] == -1:
                    add_perf_data_point(
                        f"{PerfCategory.WEED_TRACKING}:{self._pcam.id}",
                        "NumberMissedDeepweedOpportunitiesUndetermined",
                        "Count",
                        1,
                    )
                else:
                    add_perf_data_point(
                        f"{PerfCategory.WEED_TRACKING}:{self._pcam.id}",
                        f"NumberMissedDeepweedOpportunities{missed_instances[id]}",
                        "Count",
                        1,
                    )

            if id in vertical_offsets:
                dist_bucket = math.ceil(vertical_offsets[id] / dist_bucket_size)
                add_perf_data_point(
                    f"{PerfCategory.WEED_TRACKING}:{self._pcam.id}",
                    f"FirstPredictionVerticalOffset[{((dist_bucket-1) * dist_bucket_size + 1)}-{dist_bucket * dist_bucket_size}]px",
                    "Count",
                    1,
                )

        request_id = 0

        return []

    def _run_tracking_step(self, node: NodeInput[None]) -> Tuple[WeedTrackingOutputWrapper, List[CarbonRobotTableBase]]:
        if self._aimbot_weed_tracker is None:
            self._aimbot_weed_tracker = self._make_weed_tracker()

        latest_weeds = WeedTrackingOutputWrapper(weed_tracking_output=self._aimbot_weed_tracker.get_latest_weeds())
        to_be_committed = self._log_metrics(latest_weeds)

        return latest_weeds, to_be_committed

    async def _track_weeds(self, node: NodeInput[None]) -> WeedTrackingOutputWrapper:
        latest_weeds, to_be_committed = await asyncio.get_event_loop().run_in_executor(
            EXECUTOR, lambda: self._run_tracking_step(node)
        )
        await self._db.db_processor.add_rows(to_be_committed)
        return latest_weeds

    def _get_last_deepweed_position(self, last_deepweed_pos: TrackedItemCentroid) -> Tuple[int, int]:
        last_deepweed_pos_px = (
            cast(AimbotWeedTracker, self._aimbot_weed_tracker)
            .get_geo_cam()
            .get_distorted_px_from_abs_position(
                (last_deepweed_pos.get_x_mm(), last_deepweed_pos.get_y_mm(), last_deepweed_pos.get_z_mm())
            )
        )

        return (
            int(last_deepweed_pos_px[0]),
            int(last_deepweed_pos_px[1]),
        )

    def _annotate_size(
        self, cam_image: CamImage, radius_mm: float, text_pos: Tuple[int, int], color: Tuple[int, int, int]
    ) -> CamImage:
        cv2.putText(
            cam_image.image_bgr,
            f"{radius_mm:.2f}",
            text_pos,
            fontFace=cv2.FONT_HERSHEY_SIMPLEX,
            fontScale=2.5,
            color=color,
            thickness=8,
            bottomLeftOrigin=False,
        )

        return cam_image

    def _annotate_class(
        self,
        cam_image: CamImage,
        last_deepweed_pos: TrackedItemCentroid,
        class_text_pos: Tuple[int, int],
        intersection_text_pos: Tuple[int, int],
        color: Tuple[int, int, int],
        threshold: float = 0.5,
    ) -> CamImage:
        max_detection_class = max(last_deepweed_pos.get_classes(), key=lambda x: x[1])
        detection_class_str = max_detection_class[0] if max_detection_class[1] > threshold else "WEED"
        cv2.putText(
            cam_image.image_bgr,
            f"+ {detection_class_str}",
            class_text_pos,
            fontFace=cv2.FONT_HERSHEY_SIMPLEX,
            fontScale=1.0,
            color=color,
            thickness=2,
            bottomLeftOrigin=False,
        )

        intersection_category_str = ", ".join(last_deepweed_pos.get_intersection_classes())

        cv2.putText(
            cam_image.image_bgr,
            f"- {intersection_category_str}",
            intersection_text_pos,
            fontFace=cv2.FONT_HERSHEY_SIMPLEX,
            fontScale=1.0,
            color=color,
            thickness=2,
            bottomLeftOrigin=False,
        )

        return cam_image

    def _annotate_height(
        self,
        cam_image: CamImage,
        last_pos: TrackedItemPosition,
        height_text_pos: Tuple[int, int],
        color: Tuple[int, int, int],
    ) -> CamImage:
        cv2.putText(
            cam_image.image_bgr,
            f"{round(last_pos.get_height_mm())} mm",
            height_text_pos,
            fontFace=cv2.FONT_HERSHEY_SIMPLEX,
            fontScale=1.0,
            color=color,
            thickness=2,
            bottomLeftOrigin=False,
        )

        return cam_image

    def _annotate_score(
        self,
        cam_image: CamImage,
        last_deepweed_pos: TrackedItemCentroid,
        score_text_pos: Tuple[int, int],
        color: Tuple[int, int, int],
    ) -> CamImage:
        cv2.putText(
            cam_image.image_bgr,
            f"{last_deepweed_pos.get_score():.4f}",
            score_text_pos,
            fontFace=cv2.FONT_HERSHEY_SIMPLEX,
            fontScale=1.0,
            color=color,
            thickness=2,
            bottomLeftOrigin=False,
        )
        return cam_image

    def _annotate_id(
        self, cam_image: CamImage, id: int, id_text_pos: Tuple[int, int], color: Tuple[int, int, int],
    ) -> CamImage:
        cv2.putText(
            cam_image.image_bgr,
            f"ID: {id}",
            id_text_pos,
            fontFace=cv2.FONT_HERSHEY_SIMPLEX,
            fontScale=1.0,
            color=color,
            thickness=2,
            bottomLeftOrigin=False,
        )
        return cam_image

    def _annotate_last_dw_position(
        self,
        last_deepweed_pos: Optional[TrackedItemCentroid],
        cam_image: CamImage,
        last_position: Tuple[int, int],
        size: int,
    ) -> None:
        if last_deepweed_pos is not None:
            last_deepweed_position = self._get_last_deepweed_position(last_deepweed_pos)

            cv2.line(cam_image.image_bgr, last_position, last_deepweed_position, BGR_RED, 5)
            cv2.circle(cam_image.image_bgr, last_deepweed_position, size, (155, 255, 0), 5)

    def _annotate_bands(self, cam_image: CamImage) -> None:
        if self._banding_tree.get_node("enabled").get_bool_value():
            unit_multiplier = self._banding_tree.get_node("unit_multiplier").get_float_value()
            offset = self._banding_tree.get_node("offset").get_float_value()
            cam = cast(AimbotWeedTracker, self._aimbot_weed_tracker).get_geo_cam()
            max_x = cam_image.image_bgr.shape[1]
            max_y = cam_image.image_bgr.shape[0]
            (_, pos_y, pos_z) = cam.get_abs_position_from_px((0, 0))
            for band in self._banding_tree.get_node("bands").get_children_nodes():
                start_x_mm = band.get_node("start").get_float_value() * unit_multiplier + offset * unit_multiplier
                end_x_mm = band.get_node("end").get_float_value() * unit_multiplier + offset * unit_multiplier
                start_x_px = round(cam.get_distorted_px_from_abs_position((start_x_mm, pos_y, pos_z))[0])
                end_x_px = round(cam.get_distorted_px_from_abs_position((end_x_mm, pos_y, pos_z))[0])
                if (start_x_px < 0 and end_x_px <= 0) or start_x_px >= max_x and end_x_px >= max_x:
                    # This band is not in this img
                    continue
                if start_x_px < 0:
                    start_x_px = 0
                if end_x_px > max_x:
                    end_x_px = max_x
                sub_img = cam_image.image_bgr[0:max_y, start_x_px:end_x_px]
                rect = np.zeros(sub_img.shape, dtype=np.uint8)
                rect[:, :, 1] = 255  # Green
                weight = 0.75
                res = cv2.addWeighted(sub_img, weight, rect, 1 - weight, 0)
                cam_image.image_bgr[0:max_y, start_x_px:end_x_px] = res

    # flake8: noqa: C901
    def annotate(self, cam_image: CamImage, last_output: WeedTrackingOutputWrapper) -> CamImage:
        if self._aimbot_weed_tracker is None:
            return cam_image

        self._annotate_bands(cam_image)
        number_lanes = self._config_subscriber.get_config_node("aimbot", "height_estimation/num_lanes").get_uint_value()

        average_heights = self._aimbot_weed_tracker.get_averages_for_all_columns()

        for i in range(number_lanes):
            left = int(i * (self._width / number_lanes))
            right = int((i + 1) * (self._width / number_lanes))
            cv2.putText(
                cam_image.image_bgr,
                f"{round(average_heights[i])} mm",
                (int((right + left) / 2) - 100, 150),
                fontFace=cv2.FONT_HERSHEY_SIMPLEX,
                fontScale=2.5,
                color=BGR_GREEN,
                thickness=8,
                bottomLeftOrigin=False,
            )

            cv2.line(
                cam_image.image_bgr, (right, 0), (right, self._height), BGR_GREEN, 3,
            )

        to_timestamp = cam_image.timestamp_ms

        deepweed_flow_offsets_xy = last_output.deepweed_flow_offsets_xy
        for trajectory in last_output.trajectories:
            id = trajectory.id()

            last_item = self._aimbot_weed_tracker.get_last_item_from_trajectory(trajectory)
            last_pos = self._aimbot_weed_tracker.get_estimated_position_at_timestamp_from_trajectory(
                trajectory, cam_image.timestamp_ms
            )

            last_position = (
                int(last_pos.get_x()),
                int(last_pos.get_y()),
            )

            if (
                last_position[0] < 0
                or last_position[0] >= self._width
                or last_position[1] < 0
                or last_position[1] >= self._height
            ):
                continue

            try:
                if self._annotation_options["last_deepweed_prediction"]:
                    last_deepweed_pos = self._aimbot_weed_tracker.get_last_centroid_with_perspective_before_timestamp_from_trajectory(
                        trajectory, to_timestamp
                    )

                    self._annotate_last_dw_position(
                        last_deepweed_pos, cam_image, last_position, int(last_item.get_size())
                    )

                    if id in deepweed_flow_offsets_xy:
                        offset = deepweed_flow_offsets_xy[id]
                        end_position = (int(last_position[0] + offset[0]), int(last_position[1] + offset[1]))
                        cv2.line(cam_image.image_bgr, last_position, end_position, BGR_GREEN, 5)
                        cv2.circle(cam_image.image_bgr, end_position, int(last_item.get_size()), (255, 155, 0), 5)

                color_circle = BGR_YELLOW
                if not last_item.is_weed():
                    color_circle = BGR_GREEN
                if trajectory.intersected_with_nonshootable():
                    color_circle = BGR_RED
                cv2.circle(
                    cam_image.image_bgr, last_position, int(last_item.get_size()), color_circle, 5,
                )

                text_pos: Tuple[int, int] = (
                    int(last_position[0] + 20 + last_item.get_size()),
                    int(last_position[1] + 25),
                )

                color = BGR_YELLOW

                radius_mm = last_item.get_size() / cam_image.ppi * 25.4 if cam_image.ppi is not None else None

                if self._annotation_options["size"] and radius_mm is not None:
                    self._annotate_size(cam_image, radius_mm, text_pos, color)
                    text_pos = (text_pos[0], text_pos[1] + 45)

                if self._annotation_options["last_deepweed_prediction"] and self._annotation_options["class"]:
                    self._annotate_class(cam_image, last_deepweed_pos, text_pos, (text_pos[0], text_pos[1] + 25), color)
                    text_pos = (text_pos[0], text_pos[1] + 70)

                if self._annotation_options["height"]:
                    self._annotate_height(cam_image, last_pos, text_pos, color)
                    text_pos = (text_pos[0], text_pos[1] + 45)

                if self._annotation_options["last_deepweed_prediction"] and self._annotation_options["score"]:
                    self._annotate_score(cam_image, last_deepweed_pos, text_pos, color)
                    text_pos = (text_pos[0], text_pos[1] + 45)

                if self._annotation_options["id"]:
                    self._annotate_id(cam_image, id, text_pos, color)
            except Exception as e:
                LOG.warn(f"Couldn't get and display the last deepweed position before timestamp {to_timestamp}: {e}")

        time_diff_str = f"Annotation Lag: {to_timestamp - last_output.timestamp_ms} ms"
        annotate_text_line(cam_image.image_bgr, time_diff_str, y_index=11, color=(255, 0, 255))

        return cam_image

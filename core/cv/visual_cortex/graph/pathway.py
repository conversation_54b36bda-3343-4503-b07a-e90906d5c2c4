import asyncio
from typing import Any, Dict, Generic, List, Optional, TypeVar, cast

import lib.common.logging
import lib.common.tasks
from core.cv.visual_cortex.graph.node import ComputerVisionNode
from core.cv.visual_cortex.graph.output import ComputerVisionNodeOutput, ComputerVisionPathwayOutput, NodeInput
from core.cv.visual_cortex.graph.pathway_name import PathwayName
from core.cv.visual_cortex.graph.pathway_output import PathwayOutput
from core.cv.visual_cortex.graph.utility_nodes.subscription import SubscriptionNode
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.concurrent import RollingOrderedBuffer
from lib.common.image import CamImage
from lib.common.tasks import MakaTask
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time import maka_control_timestamp_ms
from lib.common.time.sleep import async_sleep_ms

LOG = lib.common.logging.get_logger(__name__)

# This is a first attempt at what will be an important module.
#
# In the future we would like to be able to:
# - Batch images going into pathways
# - Sequence pathway operations (e.g. DL --> post-process)
# - functional connection of sequential post processing operations

# wake up this often to check if we should be active
POLL_ACTIVITY_MS: int = 5000
MAX_DL_REQUEST_TIMEOUT_SECS: float = 10

Tnode = TypeVar("Tnode", bound=ComputerVisionNodeOutput)


class ComputerVisionPathway(Generic[Tnode]):
    """
    A computer vision pathway is an asynchronous context for consumer/producer
    operations of cameras -> computer vision outputs.
    """

    def __init__(
        self,
        name: PathwayName,
        output: PathwayOutput,
        subscription_node: SubscriptionNode[NodeInput[Any]],
        computer_vision_node: ComputerVisionNode[NodeInput[Any], Any],
        maxlen_buffered_timestamp_stream: int = 20,
        poll_interval_ms: int = 10,
    ):
        assert name.startswith(
            output.value
        ), f"Pathway name needs to be derived from its output, name: {name}, output: {output}"
        self._name: PathwayName = name
        self._output_buffer: RollingOrderedBuffer[ComputerVisionPathwayOutput[Tnode]] = RollingOrderedBuffer(
            maxlen=maxlen_buffered_timestamp_stream, ordering_func=lambda cvpo: cvpo.timestamp_ms
        )
        self._output = output
        self._subscription_node = subscription_node
        self._computer_vision_node = computer_vision_node

        self._active = False
        self._last_timestamp_ms: int = 0
        self._poll_interval_ms: int = poll_interval_ms

    @property
    def name(self) -> PathwayName:
        return self._name

    @property
    def output(self) -> PathwayOutput:
        return self._output

    @property
    def active(self) -> bool:
        return self._active

    @property
    def subscription_node(self) -> SubscriptionNode[Any]:
        return self._subscription_node

    def toggle_active(self) -> bool:
        self._active = not self._active
        if not self._active:
            self.clear_output()
        return self._active

    def deactivate(self) -> bool:
        self._active = False
        self.clear_output()
        return self._active

    @property
    def buffer_stream(self) -> RollingOrderedBuffer[ComputerVisionPathwayOutput[Tnode]]:
        return self._output_buffer

    async def _execute(self, input: NodeInput[Any]) -> ComputerVisionPathwayOutput[Tnode]:
        output = await self._computer_vision_node.compute(input)
        if output is None:
            output = NodeInput(None)
        return ComputerVisionPathwayOutput[Tnode](output=cast(Tnode, output))

    async def execute_pathway(self, input: NodeInput[Any]) -> None:
        try:
            output = await self._execute(input)
            self._output_buffer.publish(output)
            self._last_timestamp_ms = output.timestamp_ms
        except TimeoutError:
            LOG.exception(f"{self.name} timeout")

    async def traverse_pathway(self) -> None:
        input = self._subscription_node.last()
        assert input is not None, f"pathway name: {self._name}"
        await self.execute_pathway(input)

    async def async_loop(self) -> None:
        LOG.debug(f"{self._name} Starting...")

        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None
        stop_blocker_event = bot_stop_handler.create_stop_blocker()
        try:
            while not bot_stop_handler.stopped:

                # check if we are not suppose to be active
                # Note: This is a naive way of implementing toggling that keeps the thread alive indefinitely
                #   - This impl was chosen for simplicity and so we can leverage the shutdown logic
                #   - This impl does not scale well as number of possible pathways increases
                #   - We do not want a bunch of threads waking up and doing nothing all the time when we
                #        could just not have those threads at all.
                # Future: destroy/init the whole thread upon toggling activation
                if not self.active:
                    await async_sleep_ms(POLL_ACTIVITY_MS)
                    # update loop variable
                    self._last_timestamp_ms = maka_control_timestamp_ms()
                    continue

                try:
                    await async_sleep_ms(self._poll_interval_ms)
                    await self.traverse_pathway()
                except Exception as e:
                    LOG.exception(e)
                    self.deactivate()
        finally:
            stop_blocker_event.set()

    @property
    def annotation_options(self) -> Dict[str, bool]:
        return self._computer_vision_node.annotation_options

    def toggle_annotation_option(self, annotation: str) -> None:
        self._computer_vision_node.toggle_annotation_option(annotation)

    def annotate(self, cam_image: CamImage) -> CamImage:
        if not self.active:
            return cam_image
        last = self.buffer_stream.last()
        if last is None:
            return cam_image
        return self._computer_vision_node.annotate(cam_image, last.output)

    def run_forever(self) -> None:
        """
        Run this pathway forever, or until cancelled.
        """
        loop = get_event_loop_by_name()
        asyncio.run_coroutine_threadsafe(self.async_loop(), loop)

    def last(self) -> Optional[ComputerVisionPathwayOutput[Tnode]]:
        return self.buffer_stream.last()

    def all(self) -> List[ComputerVisionPathwayOutput[Tnode]]:
        outputs = [v for k, v in self.buffer_stream.buffer.items()]
        return outputs

    def clear_output(self) -> None:
        self.buffer_stream.reset()

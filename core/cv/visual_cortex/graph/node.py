import abc
from typing import Any, Callable, Coroutine, Dict, Generic, Optional, TypeVar, Union

import lib.common.logging
import lib.common.tasks
from core.cv.visual_cortex.graph.output import NodeInput
from lib.common.asyncio.utils import wrap_async
from lib.common.image import CamImage

LOG = lib.common.logging.get_logger(__name__)

POLL_ACTIVITY_MS: int = 50
MAX_DL_REQUEST_TIMEOUT_SECS: float = 10

Tin = TypeVar("Tin", bound=NodeInput[Any])
Tout = TypeVar("Tout")

CVCallableType = Callable[[Tin], Tout]
AsyncCVCallableType = Callable[[Tin], Coroutine[Any, Any, Tout]]


class ComputerVisionNode(Generic[Tin, Tout]):
    """
    A computer vision node that is executed as part of a ComputerVisionPathway
    """

    def __init__(
        self,
        f: Union[CVCallableType[Tin, Tout], AsyncCVCallableType[Tin, Tout]],
        annotation_options: Dict[str, bool] = {},
    ):
        self._f: AsyncCVCallableType[Tin, Tout] = wrap_async(f)
        self._last_timestamp_ms: int = 0
        self._annotation_options: Dict[str, bool] = annotation_options

    async def compute(self, input: Tin) -> Optional[Tout]:
        output: Optional[Tout] = None

        try:
            output = await self._f(input)
        except TimeoutError:
            output = None

        return output

    @abc.abstractmethod
    def annotate(self, cam_image: CamImage, last_output: Tout) -> CamImage:
        pass

    @property
    def annotation_options(self) -> Dict[str, bool]:
        return self._annotation_options

    def toggle_annotation_option(self, annotation: str) -> None:
        if annotation in self._annotation_options:
            self._annotation_options[annotation] = not self._annotation_options[annotation]

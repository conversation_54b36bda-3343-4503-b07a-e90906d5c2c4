from typing import Any, Generic, List, Optional, TypeVar, cast

from core.cv.visual_cortex.graph.output import NodeInput
from core.cv.visual_cortex.graph.pathway_name import PathwayName
from lib.common.image import CamImage
from lib.common.protocol.channel.base import SubscriberChannel

Tout = TypeVar("Tout", bound=NodeInput[Any])


class SubscriptionNode(Generic[Tout]):
    def __init__(self, subscriber_channel: SubscriberChannel[CamImage]):
        self._subscriber_channel = subscriber_channel

    @property
    def name(self) -> str:
        return "subscription"

    @property
    def subscriber_channel(self) -> SubscriberChannel[CamImage]:
        return self._subscriber_channel

    @property
    def incoming_pathways(self) -> List[PathwayName]:
        return []

    def last(self) -> Optional[Tout]:
        latest = self._subscriber_channel.read()
        if latest is None:
            return None

        return cast(Tout, NodeInput())

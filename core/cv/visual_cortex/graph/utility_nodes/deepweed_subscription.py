from typing import List, Optional

from core.cv.visual_cortex.graph.deeplearning.deepweed import DeepweedOutput
from core.cv.visual_cortex.graph.output import NodeInput
from core.cv.visual_cortex.graph.pathway import ComputerVisionPathway, ComputerVisionPathwayOutput, PathwayName
from core.cv.visual_cortex.graph.utility_nodes.subscription import SubscriptionNode
from lib.common.image import CamImage
from lib.common.protocol.channel.base import SubscriberChannel


class DeepweedNodeOutput:
    def __init__(
        self, deepweed_output: Optional[List[ComputerVisionPathwayOutput[DeepweedOutput]]] = None,
    ):
        self._deepweed_output = deepweed_output

    @property
    def deepweed_output(self) -> Optional[List[ComputerVisionPathwayOutput[DeepweedOutput]]]:
        return self._deepweed_output


class DeepweedSubscriptionNode(SubscriptionNode[NodeInput[DeepweedNodeOutput]]):
    def __init__(
        self, deepweed_input: ComputerVisionPathway[DeepweedOutput],
    ):
        self._deepweed = deepweed_input
        self._last_timestamp_deepweed: int = 0

        self._subscriber_channel = self.deepweed_pathway.subscription_node.subscriber_channel

        self._incoming_pathways = [self.deepweed_pathway.name]

    def _is_new_input(self, timestamp_1: int, timestamp_2: int) -> bool:
        return timestamp_1 > timestamp_2

    @property
    def deepweed_pathway(self) -> ComputerVisionPathway[DeepweedOutput]:
        return self._deepweed

    @property
    def subscriber_channel(self) -> "SubscriberChannel[CamImage]":
        return self._subscriber_channel

    @property
    def name(self) -> str:
        return "deepweed"

    @property
    def incoming_pathways(self) -> List[PathwayName]:
        return self._incoming_pathways

    def last(self) -> Optional[NodeInput[DeepweedNodeOutput]]:
        if not self._deepweed.active:
            raise Exception("Deepweed is inactive")

        deepweed = self._deepweed.last()
        self._deepweed.clear_output()
        # TODO This is hacky but temporary as we move weed tracking to CVRT
        ret_deepweed = None

        if deepweed is not None and self._is_new_input(deepweed.timestamp_ms, self._last_timestamp_deepweed):
            self._last_timestamp_deepweed = deepweed.timestamp_ms
            ret_deepweed = [deepweed]

        return NodeInput(computer_vision_output=DeepweedNodeOutput(deepweed_output=ret_deepweed),)

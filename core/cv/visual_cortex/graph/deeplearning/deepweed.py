from typing import Any, Optional, <PERSON><PERSON>

import lib.common.logging
from core.cv.visual_cortex.graph.deeplearning.deeplearning import Maka<PERSON>eepLabV3Node
from core.cv.visual_cortex.graph.output import DeepweedOutput, NodeInput
from cv.runtime.cpp import shmem_types_python
from lib.common.image import CamImage

LOG = lib.common.logging.get_logger(__name__)


class DeepweedPredictDeepLearningNode(
    MakaDeepLabV3Node[NodeInput[None], shmem_types_python.DeepweedOutput, DeepweedOutput]
):
    """
    A computer vision pathway for deepweed
    """

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)

    def _get_prediction(self, last_timestamp_ms: int) -> Tuple[shmem_types_python.DeepweedOutput, int]:
        prediction = self._cv_runtime_client.get_next_deepweed_prediction(
            self._camera_id, timeout_ms=5000, last_timestamp_ms=last_timestamp_ms
        )
        return prediction, prediction.timestamp_ms

    def _process_prediction(self, prediction: Optional[shmem_types_python.DeepweedOutput]) -> DeepweedOutput:
        if prediction is None:
            return DeepweedOutput(raw=None, timestamp_ms=0)
        return DeepweedOutput(raw=prediction, timestamp_ms=prediction.timestamp_ms)

    def annotate(self, cam_image: CamImage, last_output: DeepweedOutput) -> CamImage:
        return cam_image

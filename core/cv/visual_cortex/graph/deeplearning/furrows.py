from typing import Any, List, Optional, Tu<PERSON>

import cv2
import numpy as np

from core.cv.visual_cortex.graph.deeplearning.deeplearning import Maka<PERSON>eep<PERSON>abV3Node
from core.cv.visual_cortex.graph.output import FurrowsContext, FurrowsOutput, NodeInput
from core.cv.visual_cortex.model.ungenerated import FurrowSlope
from cv.runtime.cpp import shmem_types_python
from deeplearning.utils.use_cases import ModelUseCase
from generated.cv.runtime.proto.cv_runtime_pb2 import AllLinesExpression, DetectionClass, LineExpression, MaskExpression
from lib.common.annotate import BGR_CYAN
from lib.common.image import CamImage


def process_furrows(prediction: shmem_types_python.FurrowOutput) -> FurrowsOutput:
    slopes: List[FurrowSlope] = []
    for output in prediction.mask_outputs[: prediction.num_outputs]:
        image_center = (output.image_center_x, output.image_center_y)
        for furrow in output.furrows[: output.num_furrows]:
            slopes.append(
                FurrowSlope(
                    timestamp_ms=prediction.timestamp_ms,
                    image_center=image_center,
                    image_height=output.image_height,
                    image_width=output.image_width,
                    start=(furrow.start_x, furrow.start_y),
                    end=(furrow.end_x, furrow.end_y),
                )
            )
    return FurrowsOutput(timestamp_ms=prediction.timestamp_ms, slopes=slopes,)


def annotate_furrows(image_bgr: np.ndarray, last_output: FurrowsOutput) -> None:
    closest: Optional[FurrowSlope] = last_output.closest_furrow
    if closest is not None:
        cv2.line(image_bgr, closest.start, closest.end, color=BGR_CYAN, thickness=3)


class FurrowsDeepLearningNode(MakaDeepLabV3Node[NodeInput[None], shmem_types_python.FurrowOutput, FurrowsOutput]):
    """
    A computer vision pathway for finding furrows in fields.
    """

    def __init__(self, furrows_context: FurrowsContext, **kwargs: Any):
        self.furrows_context: FurrowsContext = furrows_context
        self.furrows_context.wheel_x = 0.5
        self.furrows_context.wheel_y = 3.0
        exprs = [
            MaskExpression(
                line=LineExpression(
                    category=DetectionClass.Name(DetectionClass.FURROW),
                    x=self.furrows_context.wheel_x,
                    y=self.furrows_context.wheel_y,
                )
            ),
            MaskExpression(all_lines=AllLinesExpression(category=DetectionClass.Name(DetectionClass.FURROW))),
        ]
        self._temp_buffer = shmem_types_python.FurrowOutput()
        super().__init__(use_case=ModelUseCase.DRIVING, exprs=exprs, **kwargs)

    def _get_prediction(self, last_timestamp_ms: int) -> Tuple[shmem_types_python.FurrowOutput, int]:
        self._cv_runtime_client.get_next_furrow_prediction(
            self._camera_id, buffer=self._temp_buffer, timeout_ms=5000, last_timestamp_ms=last_timestamp_ms
        )
        return self._temp_buffer, self._temp_buffer.timestamp_ms

    def _process_prediction(self, prediction: Optional[shmem_types_python.FurrowOutput]) -> FurrowsOutput:
        if prediction is None:
            return FurrowsOutput(timestamp_ms=0, slopes=[],)

        return process_furrows(prediction)

    def annotate(self, cam_image: CamImage, last_output: FurrowsOutput) -> CamImage:
        annotate_furrows(cam_image.image_bgr, last_output)
        return cam_image

import abc
import asyncio
import functools
from typing import Any, <PERSON>ric, List, Optional, Tuple, TypeVar

import numpy as np

import lib.common.logging
from core.cv.retina.camera.node import Cam
from core.cv.visual_cortex.graph.node import ComputerVisionNode
from core.cv.visual_cortex.graph.output import ComputerVisionNodeOutput, NodeInput
from cv.runtime.client import CVRuntimeClient
from deeplearning.utils.use_cases import ModelUseCase
from generated.cv.runtime.proto.cv_runtime_pb2 import BufferUseCase, MaskExpression
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.time import maka_control_timestamp_ms
from lib.common.time.sleep import async_sleep_ms

LOG = lib.common.logging.get_logger(__name__)

Tin = TypeVar("Tin", bound=NodeInput[Any])
Tintermediate = TypeVar("Tintermediate")
Tout = TypeVar("Tout", bound=ComputerVisionNodeOutput)


class MakaDeepLabV3Node(ComputerVisionNode[Tin, Tout], abc.ABC, <PERSON>ric[<PERSON>, <PERSON><PERSON>, Tout]):
    """
    A computer vision pathway specific to deeplabv3 model.
    """

    def __init__(
        self,
        *argv: Any,
        use_case: ModelUseCase,
        exprs: List[MaskExpression],
        cv_runtime_client: CVRuntimeClient,
        camera: Cam,
        cv_runtime_client_buffer_timeout_ms: int = 2000,
        cv_runtime_buffer_retrieve_interval_ms: int = 5,
        **kwargs: Any
    ):
        super().__init__(f=self._dlreq)
        self._use_case: ModelUseCase = use_case
        self._exprs: List[MaskExpression] = exprs
        self._cv_runtime_client = cv_runtime_client
        self._cv_runtime_buffer_timeout_ms = cv_runtime_client_buffer_timeout_ms
        self._cv_runtime_buffer_retrieve_interval_ms = cv_runtime_buffer_retrieve_interval_ms
        self._cv_runtime_last_timestamp_ms = 0
        self._set_exprs = False
        self._camera_id = camera.id
        self._camera_ppi = camera.ppi
        self._camera_device_path = camera.device_path
        self._dummy_bgr: Optional[np.ndarray] = None

    def _model_use_case_to_buffer_use_case(self, model_use_case: ModelUseCase) -> BufferUseCase:
        if model_use_case == ModelUseCase.DRIVING:
            return BufferUseCase.Drive
        elif model_use_case == ModelUseCase.PREDICT:
            return BufferUseCase.Predict

    @abc.abstractmethod
    def _get_prediction(self, last_timestamp_ms: int) -> Tuple[Tintermediate, int]:
        """Subclasses to override this to retrieve prediction"""
        pass

    @abc.abstractmethod
    def _process_prediction(self, prediction: Optional[Tintermediate]) -> Tout:
        """Subclasses to override this to process prediction"""
        pass

    async def _dlreq(self, input: Tin) -> Tout:
        """Run the deep learning request and retrieve from cv runtime"""
        predict_output = None
        try:
            timestamp_ms = None
            if not self._set_exprs:
                await self._cv_runtime_client.set_deeplab_mask_expressions_async(
                    self._camera_id, self._exprs, self._model_use_case_to_buffer_use_case(self._use_case)
                )
                self._cv_runtime_last_timestamp_ms = maka_control_timestamp_ms()
                self._set_exprs = True

            # Assure the masks we get are for the mask expressions we just set by comparing timestamps.
            # We will likely skip more results than necessary but we will only do this the first time.
            grab_mask_begin = maka_control_timestamp_ms()
            while not bot_stop_handler.stopped:
                if maka_control_timestamp_ms() - grab_mask_begin > self._cv_runtime_buffer_timeout_ms:
                    raise TimeoutError("Failed to retrieve new MakaDeepLabV3 result")

                try:
                    predict_output, timestamp_ms = await asyncio.get_event_loop().run_in_executor(
                        None, functools.partial(self._get_prediction, self._cv_runtime_last_timestamp_ms)
                    )
                except Exception as e:
                    LOG.exception(e)
                    await async_sleep_ms(1000)
                    continue

                if timestamp_ms > self._cv_runtime_last_timestamp_ms:
                    break

                await asyncio.sleep(self._cv_runtime_buffer_retrieve_interval_ms / 1000)
            if timestamp_ms is not None:
                self._cv_runtime_last_timestamp_ms = timestamp_ms
        except:  # noqa
            LOG.exception("Failed to retrieve new MakaDeepLabV3 result")
            predict_output = None
            self._set_exprs = False

        return self._process_prediction(predict_output)

from enum import Enum
from typing import cast


class PathwayOutput(Enum):
    DEEPWEED = "deepweed"
    FURROWS = "furrows"
    OPTICAL_FLOW = "optical_flow"
    SUBSAMPLED_OPTICAL_FLOW = "subsampled_optical_flow"
    WEED_TRACKING = "weed_tracking"

    def __lt__(self, other: object) -> bool:
        if not isinstance(other, PathwayOutput):
            raise NotImplementedError
        return cast(bool, self.value < other.value)

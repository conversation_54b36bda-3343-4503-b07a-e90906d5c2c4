import abc
import functools
from typing import Any, Dict, List, Optional

import torch
from sortedcontainers import SortedSet

import lib.common.logging
from core.cv.retina.camera.node import Cam
from core.cv.retina.retina import <PERSON>tina
from core.cv.visual_cortex.graph.output import ComputerVisionNodeOutput, FurrowsContext
from core.cv.visual_cortex.graph.pathway import ComputerVisionPathway
from core.cv.visual_cortex.graph.pathway_name import PathwayName
from core.cv.visual_cortex.graph.pathway_output import PathwayOutput
from core.model.id import ReferenceId
from core.model.path import DevicePath
from cv.utils.cupy import CUPY_IMPORTED
from lib.common.protocol.channel.base import Topic
from lib.common.protocol.feed.base import Feed

LOG = lib.common.logging.get_logger(__name__)

NUMBER_CUDA_DEVICES = 0

# CUPY_IMPORTED will be true if cupy is imported, which only happens if nvidia drivers are installed
# So this is a hacky way to get around the fact that torch.cuda.is_available() fails when nvidia drivers aren't installed
if CUPY_IMPORTED:
    NUMBER_CUDA_DEVICES = torch.cuda.device_count()


class ComputerVisionGraph(abc.ABC):
    def __init__(
        self,
        *,
        base_topic: Topic,
        feed: Feed,
        back_left_cam: Optional[Cam] = None,
        back_right_cam: Optional[Cam] = None,
        front_left_cam: Optional[Cam] = None,
        front_right_cam: Optional[Cam] = None,
        predict_cams: Optional[List[Cam]] = None,
        enable_pathways: Optional[List[PathwayName]] = None,
    ):
        self._pathways: Dict[PathwayName, ComputerVisionPathway[ComputerVisionNodeOutput]] = {}
        self._front_right_furrows_context = FurrowsContext()
        self._create_pathways(
            back_left_cam=back_left_cam,
            back_right_cam=back_right_cam,
            front_left_cam=front_left_cam,
            front_right_cam=front_right_cam,
            predict_cams=predict_cams,
            enable_pathways=enable_pathways if enable_pathways is not None else [],
        )

        # publish pathways to feed
        for p in self.pathways:
            callback = functools.partial(self.last_output, p)
            feed.publish_callback(topic=base_topic.sub(p), callback=callback)

    @abc.abstractmethod
    def _create_pathways(
        self,
        enable_pathways: List[PathwayName],
        back_left_cam: Optional[Cam] = None,
        back_right_cam: Optional[Cam] = None,
        front_left_cam: Optional[Cam] = None,
        front_right_cam: Optional[Cam] = None,
        predict_cams: Optional[List[Cam]] = None,
    ) -> None:
        pass

    def _register_pathways(self, pathways: List[ComputerVisionPathway[Any]], enable: bool = False) -> None:
        for pathway in pathways:
            assert pathway.name not in self.pathways, f"Attempted to register pathway twice: {pathway.name}"
            self.pathways[pathway.name] = pathway
            if enable:
                self.enable_pathway(pathway.name)

    def set_front_right_wheel_image_location(self, x: float, y: float) -> None:
        self._front_right_furrows_context.wheel_x = x
        self._front_right_furrows_context.wheel_y = y

    def _toggle_on_all_upstream(self, pathway_name: PathwayName) -> None:
        stack = [pathway_name]

        while len(stack):
            p_name = stack.pop()
            if p_name not in self.pathways:
                LOG.error(f"only only valid pathways are {self.pathways.keys()}")
            p = self.pathways[p_name]
            if not p.active:
                p.toggle_active()
                stack.extend(self.pathways[p_name].subscription_node.incoming_pathways)

    def _toggle_off_all_downstream(self, pathway_name: PathwayName) -> None:
        stack = [pathway_name]
        while len(stack):
            p_name = stack.pop()
            if self.pathways[p_name].active:
                self.pathways[p_name].toggle_active()
            for name, pathway in self.pathways.items():
                if name == p_name:
                    continue
                if pathway.active:
                    incoming_pathways = pathway.subscription_node.incoming_pathways
                    if p_name in incoming_pathways:
                        stack.append(name)

    def toggle_off_all_pathways(self) -> None:
        for _, pathway in self.pathways.items():
            pathway.deactivate()

    def enable_pathway(self, pathway_name: PathwayName) -> None:
        self._toggle_on_all_upstream(pathway_name)

    def disable_pathway(self, pathway_name: PathwayName) -> None:
        self._toggle_off_all_downstream(pathway_name)

    def enable_pathway_output(self, pathway_output: PathwayOutput) -> None:
        for pathway in [p for p in self.pathways.values() if p.output == pathway_output]:
            self._toggle_on_all_upstream(pathway.name)

    def disable_pathway_output(self, pathway_output: PathwayOutput) -> None:
        for pathway in [p for p in self.pathways.values() if p.output == pathway_output]:
            self._toggle_off_all_downstream(pathway.name)

    @property
    def annotation_options(self) -> Dict[str, Dict[str, bool]]:
        annotation_options: Dict[str, Dict[str, bool]] = {}
        for pathway in self.pathways.values():
            annotation_options[pathway.output.value] = pathway.annotation_options
        return annotation_options

    def toggle_annotation_option(self, pathway_output: str, annotation_option: str) -> None:
        for pathway in self.pathways.values():
            if pathway.output.value == pathway_output:
                self.pathways[pathway.name].toggle_annotation_option(annotation_option)

    def pathways_for_cam(
        self, cam_id: ReferenceId, retina: Retina
    ) -> Dict[str, ComputerVisionPathway[ComputerVisionNodeOutput]]:
        """
        :return: the pathways for a given cam
        """
        pathways_for_cam: Dict[str, ComputerVisionPathway[ComputerVisionNodeOutput]] = {
            k: p
            for k, p in self.pathways.items()
            if retina.subtree[DevicePath.parse(p.subscription_node.subscriber_channel.topic)].id == cam_id
        }

        return pathways_for_cam

    def last_output(self, pathway_name: PathwayName) -> Optional[ComputerVisionNodeOutput]:
        if pathway_name not in self.pathways:
            return None
        last = self.pathways[pathway_name].last()
        if last is None:
            return None
        return last.output

    def status_callback(self) -> Dict[str, Any]:
        status = {}

        status["pathways"] = {
            output.value: {
                p2.name: {"active": p2.active} for p2 in [p3 for p3 in self.pathways.values() if p3.output == output]
            }
            for output in SortedSet([p.output for p in self.pathways.values()])
        }

        return status

    def pathway_is_active(self, pathway_name: PathwayName) -> bool:
        return self.pathways[pathway_name].active

    @property
    def pathways(self) -> Dict[PathwayName, ComputerVisionPathway[ComputerVisionNodeOutput]]:
        return self._pathways

    def pathway_exists(self, pathway_name: PathwayName) -> bool:
        return pathway_name in self.pathways.keys()

from typing import Optional

from core.cv.visual_cortex.graph.output import FurrowsOutput
from core.cv.visual_cortex.model.ungenerated import FurrowSlope
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.protocol.channel.callback import CallbackSubscriberChannel


class Closest(CallbackSubscriberChannel[FurrowSlope]):
    """
    Select the closest furrow
    """

    def __init__(self, furrows_subscription: SubscriberChannel[FurrowsOutput]):
        super().__init__(topic=furrows_subscription.topic.sub("closest"), callback=self._closest_furrow_callback)
        self._furrows_subscription = furrows_subscription

    @staticmethod
    def filter_closest_furrow(furrows: Optional[FurrowsOutput]) -> Optional[FurrowSlope]:
        return furrows.closest_furrow if furrows is not None else None

    def _closest_furrow_callback(self) -> Optional[FurrowSlope]:
        return Closest.filter_closest_furrow(self._furrows_subscription.read())

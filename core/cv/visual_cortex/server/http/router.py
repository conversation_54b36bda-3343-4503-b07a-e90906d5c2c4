from typing import Any, Dict, Optional

import lib.common.logging
from config.client.cpp.config_client_python import Config<PERSON>lient, ConfigSubscriber, ConfigTree, make_robot_local_addr
from core.cv.visual_cortex.graph.pathway_name import PathwayName
from core.cv.visual_cortex.graph.pathway_output import PathwayOutput
from core.cv.visual_cortex.visual_cortex import Visual<PERSON><PERSON>
from core.model.node.type import NodeType
from cv.runtime.client import CVRuntimeClient
from lib.common.web.http.router import ComponentRouter<PERSON>uilder, RouterBuilder
from lib.common.web.http.router_utils import merge_add

LOG = lib.common.logging.get_logger(__name__)


class VisualCortexRouterBuilder(ComponentRouterBuilder):
    def __init__(self, visual_cortex: VisualCortex):
        super().__init__(base_route=f"/{NodeType.VISUAL_CORTEX.serialize()}")
        self._visual_cortex: VisualCortex = visual_cortex
        self._cv_runtime_client = CVRuntimeClient()
        self._config_client = ConfigClient(make_robot_local_addr(61001))
        self._deepweed_config: Optional[ConfigTree] = None
        self._config_subscriber: Optional[ConfigSubscriber] = None

    def enable_pathway_output(self, bot: Any, pathway_output: str) -> None:
        self._visual_cortex.enable_pathway_output(PathwayOutput(pathway_output.lower()))

    def disable_pathway_output(self, bot: Any, pathway_output: str) -> None:
        self._visual_cortex.disable_pathway_output(PathwayOutput(pathway_output.lower()))

    def enable_pathway(self, bot: Any, pathway: str) -> None:
        self._visual_cortex.enable_pathway(PathwayName(pathway))

    def disable_pathway(self, bot: Any, pathway: str) -> None:
        self._visual_cortex.disable_pathway(PathwayName(pathway))

    def toggle_all_off(self, bot: Any) -> None:
        self._visual_cortex.toggle_off_all_pathways()

    def is_pathway_active(self, bot: Any, pathway: str) -> bool:
        return self._visual_cortex.pathway_is_active(PathwayName(pathway))

    def get_annotation_options(self, bot: Any) -> Dict[str, Dict[str, bool]]:
        LOG.info("get_annotation_options being called")
        return self._visual_cortex.annotation_options

    def toggle_annotation_option(self, bot: Any, pathway_output: str, annotation_option: str) -> None:
        self._visual_cortex.toggle_annotation_option(pathway_output, annotation_option)

    def get_deepweed_detection_criteria(self, bot: Any) -> Dict[str, Any]:
        if self._deepweed_config is None:
            self._connect_to_config()
        assert self._deepweed_config is not None
        supported_categories = self.get_deepweed_supported_categories(bot)
        segmentation_categories = set(supported_categories["segmentationCategories"])
        segmentation_criteria = {}
        for x in self._deepweed_config.get_node("segmentationCategories").get_children_nodes():
            category = x.get_name()
            if category not in segmentation_categories:
                continue
            segmentation_criteria[category] = {
                "threshold": x.get_node("threshold").get_float_value(),
                "safety_radius_in": x.get_node("safety_radius_in").get_float_value(),
            }
        for category in segmentation_categories:
            if category in segmentation_criteria:
                continue
            segmentation_criteria[category] = {"threshold": 0.5, "safety_radius_in": 0.4}
        return {
            "segmentationCategories": segmentation_criteria,
            "pointCategories": {
                "WEED": {"threshold": self._deepweed_config.get_node("weed_point_threshold").get_float_value()},
                "CROP": {"threshold": self._deepweed_config.get_node("crop_point_threshold").get_float_value()},
            },
        }

    def get_deepweed_supported_categories(self, bot: Any) -> Dict[str, Any]:
        supported_categories = self._cv_runtime_client.get_deepweed_supported_categories()
        return {
            "segmentationCategories": [x for x in supported_categories.segmentation_categories],
            "pointCategories": [x for x in supported_categories.point_categories],
        }

    def set_deepweed_detection_criteria(
        self, bot: Any, pointCategories: Dict[str, Any], segmentationCategories: Dict[str, Any]
    ) -> None:
        if self._deepweed_config is None or self._config_subscriber is None:
            self._connect_to_config()
        for key, value in pointCategories.items():
            if key == "WEED":
                self._config_client.set_float_value("common/deepweed/weed_point_threshold", float(value["threshold"]))
            elif key == "CROP":
                self._config_client.set_float_value("common/deepweed/crop_point_threshold", float(value["threshold"]))
        assert self._deepweed_config is not None
        config_segmentation_categories = set(
            [x.get_name() for x in self._deepweed_config.get_node("segmentationCategories").get_children_nodes()]
        )
        for key, value in segmentationCategories.items():
            if key not in config_segmentation_categories:
                self._config_client.add_to_list("common/deepweed/segmentationCategories", key)
            self._config_client.set_float_value(
                f"common/deepweed/segmentationCategories/{key}/threshold", float(value["threshold"]),
            )
            self._config_client.set_float_value(
                f"common/deepweed/segmentationCategories/{key}/safety_radius_in", float(value["safety_radius_in"]),
            )

    def _connect_to_config(self) -> None:
        self._config_subscriber = ConfigSubscriber(make_robot_local_addr(61001))
        self._config_subscriber.add_config_tree("common", "common", "services/common.yaml")
        self._config_subscriber.start()
        self._deepweed_config = self._config_subscriber.get_config_node("common", "deepweed")

    def add_onto(self, bot_routes: RouterBuilder) -> None:
        """
        Attach visual-cortex-specific routes
        """
        bot_routes.add("POST", self.suburl("/pathway_output/enable"), body_desc={"pathway_output": str})(
            merge_add(self.enable_pathway_output)
        )

        bot_routes.add("POST", self.suburl("/pathway_output/disable"), body_desc={"pathway_output": str})(
            merge_add(self.disable_pathway_output)
        )

        bot_routes.add("POST", self.suburl("/pathway/enable"), body_desc={"pathway": str})(
            merge_add(self.enable_pathway)
        )

        bot_routes.add("POST", self.suburl("/pathway/disable"), body_desc={"pathway": str})(
            merge_add(self.disable_pathway)
        )

        bot_routes.add("POST", self.suburl("/pathways/deactivate"))(merge_add(self.toggle_all_off))

        bot_routes.add(
            "POST",
            self.suburl("/pathways/toggle_annotation_option"),
            body_desc={"pathway_output": str, "annotation_option": str},
        )(merge_add(self.toggle_annotation_option))

        bot_routes.add("GET", self.suburl("/pathways/get_annotation_options"))(merge_add(self.get_annotation_options))

        bot_routes.add("GET", self.suburl("/pathway/active/(?P<pathway>[a-z_0-9]+)"))(merge_add(self.is_pathway_active))

        bot_routes.add("GET", self.suburl("/deepweed/get_detection_criteria"))(
            merge_add(self.get_deepweed_detection_criteria)
        )

        bot_routes.add(
            "POST",
            self.suburl("/deepweed/set_detection_criteria"),
            body_desc={"pointCategories": dict, "segmentationCategories": dict},
        )(merge_add(self.set_deepweed_detection_criteria))

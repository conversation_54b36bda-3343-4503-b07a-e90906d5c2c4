import os
from typing import Any, Dict, List, Optional, Union, cast

import lib.common.logging
import lib.common.tasks
from config.client.cpp.config_client_python import get_global_config_subscriber
from core.cv.retina.camera.node import Cam
from core.cv.retina.retina import <PERSON><PERSON>
from core.cv.visual_cortex.annotate import Visual<PERSON>ortexAnnotator
from core.cv.visual_cortex.graph.output import ComputerVisionNodeOutput
from core.cv.visual_cortex.graph.pathway import ComputerVisionPathway, PathwayName, PathwayOutput
from core.model.id import ReferenceId
from core.model.node.type import NodeType
from core.model.path import DevicePath
from core.model.sensor import Sensor
from cv.runtime.client import CVRuntimeClient
from lib.common.devices.boards.nofx.mock_nofx_board_device import MockNoFXBoardDevice
from lib.common.devices.boards.nofx.nofx_board_device import NoFXBoardDevice
from lib.common.devices.registry import DeviceRegistry, DeviceRegistryException

LOG = lib.common.logging.get_logger(__name__)


class VisualCortex(Sensor):
    """
    The Visual Cortex node is the authority on Robot Computer Vision data. It
    consumes predictions from CV runtime and facilitates annotations.

    The visual cortex is responsible for:

    Neuroanatomy, Visual Cortex https://www.ncbi.nlm.nih.gov/books/NBK482504/
        The visual cortex is the primary cortical region of the brain that
        receives, integrates, and processes visual information relayed from the
        retinas.
    """

    def __init__(
        self,
        *argv: Any,
        nofx_device_id: str,
        device_registry: Optional[DeviceRegistry] = None,
        enable_pathways: Optional[List[PathwayName]] = None,
        unity: bool = False,
        is_virtual: bool = False,
        **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)

        # Dumb check to prevent issues for old integration tests
        if os.getenv("MAKA_BOT_MODE") != "legacy" and os.getenv("MAKA_GEN") is not None:
            subscriber = get_global_config_subscriber()
            if subscriber.started():
                boot_weed_tracking = (
                    get_global_config_subscriber()
                    .get_config_node("common", "use_controls_weed_tracking")
                    .get_bool_value()
                )
            else:
                LOG.warning("Config service is not running, defaulting to true")
                boot_weed_tracking = True
        else:
            LOG.warning(
                f"Failed to get value, using boot_weed_tracking = True: {os.getenv('MAKA_BOT_MODE')} {os.getenv('MAKA_GEN')}"
            )
            boot_weed_tracking = True

        self._unity = unity
        self._is_virtual = is_virtual

        self._cv_runtime_client = CVRuntimeClient()
        self._nofx_board_device: Optional[Union[NoFXBoardDevice, MockNoFXBoardDevice]] = None

        assert device_registry is not None
        if boot_weed_tracking:
            try:
                self._nofx_board_device = device_registry.get_device_from_sync(NoFXBoardDevice, nofx_device_id)
            except DeviceRegistryException:
                # This is to satisfy the calibration Unit Test which we can't get rid of yet
                pass

            if self._nofx_board_device is None:
                try:
                    self._nofx_board_device = device_registry.get_device_from_sync(MockNoFXBoardDevice, nofx_device_id)
                except DeviceRegistryException:
                    # This is to satisfy the calibration Unit Test which we can't get rid of yet
                    pass

        # Get graph
        if not unity:
            from core.cv.visual_cortex.graph.deeplearning_graph import DeeplearningComputerVisionGraph

            self._computer_vision_graph = DeeplearningComputerVisionGraph(
                base_topic=self.topic,
                feed=self.feed,
                back_left_cam=self._retina.back_left_cam,
                back_right_cam=self._retina.back_right_cam,
                front_left_cam=self._retina.front_left_cam,
                front_right_cam=self._retina.front_right_cam,
                predict_cams=[predict_cam for (_, predict_cam) in self._retina.predict_cams.items()],
                enable_pathways=enable_pathways,
                cv_runtime_client=self._cv_runtime_client,
                nofx_board_device=self._nofx_board_device,
                is_virtual=is_virtual,
                boot_weed_tracking=boot_weed_tracking,
            )
        else:
            from core.unity.cv.graph import UnityComputerVisionGraph

            self._computer_vision_graph = UnityComputerVisionGraph(
                base_topic=self.topic,
                feed=self.feed,
                back_left_cam=self._retina.back_left_cam,
                back_right_cam=self._retina.back_right_cam,
                front_left_cam=self._retina.front_left_cam,
                front_right_cam=self._retina.front_right_cam,
                predict_cams=[predict_cam for (_, predict_cam) in self._retina.predict_cams.items()],
                enable_pathways=enable_pathways,
            )

        # Attach annotations
        self._annotator: VisualCortexAnnotator = VisualCortexAnnotator(self)
        self._annotator.attach()

    @property
    def nofx_board_device(self) -> Optional[Union[NoFXBoardDevice, MockNoFXBoardDevice]]:
        return self._nofx_board_device

    def set_front_right_wheel_image_location(self, x: float, y: float) -> None:
        self._computer_vision_graph.set_front_right_wheel_image_location(x, y)

    def status_callback(self) -> Dict[str, Any]:
        return self._computer_vision_graph.status_callback()

    # please only use this method to facilitate annotations.
    # We don't really want to encourage reference access
    # We will move this to a better architecture, but we are still figuring it out
    def get_cam_by_path_for_annotations(self, path: DevicePath) -> Cam:
        # we know the cam will be there because we checked before setting up the pathway
        return cast(Cam, self._retina.subtree[path])

    def enable_pathway(self, pathway_name: PathwayName) -> None:
        return self._computer_vision_graph.enable_pathway(pathway_name)

    def disable_pathway(self, pathway_name: PathwayName) -> None:
        return self._computer_vision_graph.disable_pathway(pathway_name)

    def enable_pathway_output(self, pathway_output: PathwayOutput) -> None:
        return self._computer_vision_graph.enable_pathway_output(pathway_output)

    def disable_pathway_output(self, pathway_output: PathwayOutput) -> None:
        return self._computer_vision_graph.disable_pathway_output(pathway_output)

    def toggle_off_all_pathways(self) -> None:
        self._computer_vision_graph.toggle_off_all_pathways()

    @property
    def annotation_options(self) -> Dict[str, Dict[str, bool]]:
        return self._computer_vision_graph.annotation_options

    def toggle_annotation_option(self, pathway_output: str, annotation_option: str) -> None:
        self._computer_vision_graph.toggle_annotation_option(pathway_output, annotation_option)

    def pathway_is_active(self, pathway: PathwayName) -> bool:
        return self._computer_vision_graph.pathway_is_active(pathway)

    def pathways_for_cam(self, cam_id: ReferenceId) -> Dict[str, ComputerVisionPathway[ComputerVisionNodeOutput]]:
        """
        :return: the pathways for a given cam
        """
        return self._computer_vision_graph.pathways_for_cam(cam_id, self._retina)

    def _cache_named_references(self) -> None:
        self._retina: Retina = cast(Retina, self.get_node(NodeType.RETINA, required=True))

    def last_output(self, pathway_name: PathwayName) -> Optional[ComputerVisionNodeOutput]:
        return self._computer_vision_graph.last_output(pathway_name)

    def _control_loop(self) -> None:
        for pathway in self._computer_vision_graph.pathways.values():
            self.add_subtask(
                lib.common.tasks.start(name=f"{self.device_path}/{pathway.name}", target=pathway.run_forever)
            )

    @property
    def pathways(self) -> Dict[PathwayName, ComputerVisionPathway[ComputerVisionNodeOutput]]:
        return self._computer_vision_graph.pathways

    def pathway_exists(self, pathway_name: PathwayName) -> bool:
        return self._computer_vision_graph.pathway_exists(pathway_name)

    @property
    def is_virtual(self) -> bool:
        return self._is_virtual

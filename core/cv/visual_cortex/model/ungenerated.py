from abc import ABC, abstractmethod
from typing import <PERSON>ple, cast

import numpy as np

# Furrow type is explicitly an experiment to see if we can use np.int32 as a base type
# This will work better with generated code, or pretty much any other language besides python/JS
from lib.common.math import add_tuples, div_tuple
from lib.common.time import Timestamp

Point = Tuple[int, int]


class Slope(ABC):
    """
    A slope is just a line branded as a slope. Kind of implies it is to be used for angular operations.
    """

    @property
    @abstractmethod
    def image_center(self) -> Tuple[int, int]:
        pass

    @property
    @abstractmethod
    def start(self) -> Point:
        pass

    @property
    @abstractmethod
    def end(self) -> Point:
        pass

    @property
    def midpoint(self) -> Point:
        return div_tuple(add_tuples(self.start, self.end), 2)

    @property
    def angle_from_vertical_rad(self) -> float:
        # opposite = x diff
        # adjacent = y diff
        slope: float = (self.start[0] - self.end[0]) / (self.start[1] - self.end[1])
        # tan(theta) = opposite / adjacent
        return cast(float, np.arctan(slope))

    @property
    def angle_from_vertical_deg(self) -> float:
        return cast(float, np.rad2deg(self.angle_from_vertical_rad))


class TimestampedSlope(Slope, Timestamp, ABC):
    pass


class FurrowSlope(TimestampedSlope):
    """
    A Furrow, represented as a sloped line (pair of points).

    TODO generate this class via codegen
    """

    def __init__(
        self,
        *,
        timestamp_ms: int,
        image_center: Tuple[int, int],
        image_height: int,
        image_width: int,
        start: Point,
        end: Point,
    ):
        self._timestamp_ms: int = timestamp_ms
        self._image_center: Tuple[int, int] = image_center
        self._image_height = image_height
        self._image_width = image_width
        self._start: Point = start
        self._end: Point = end

    @property
    def timestamp_ms(self) -> int:
        return self._timestamp_ms

    @property
    def image_center(self) -> Tuple[int, int]:
        return self._image_center

    @property
    def image_height(self) -> int:
        return self._image_height

    @property
    def image_width(self) -> int:
        return self._image_width

    @property
    def start(self) -> Point:
        return self._start

    @property
    def end(self) -> Point:
        return self._end

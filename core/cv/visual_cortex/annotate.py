from typing import TYPE_CHECKING

from core.controls.annotate import VISUAL_CORTEX_LAYER
from core.cv.retina.camera.node import Cam
from core.model.path import DevicePath
from lib.common.annotate import Annotation, Annotator

if TYPE_CHECKING:
    from core.cv.visual_cortex.visual_cortex import VisualCortex


class VisualCortexAnnotator(Annotator):
    def __init__(self, visual_cortex: "VisualCortex"):
        self._visual_cortex: VisualCortex = visual_cortex

    def attach(self) -> None:
        layer = VISUAL_CORTEX_LAYER
        cam: Cam
        cam_path: DevicePath

        # attach pathway-specific annotations
        for i, pathway in enumerate(self._visual_cortex.pathways.values()):
            subscriber_channel = pathway.subscription_node.subscriber_channel
            # TODO: move annotator to separate component and publish annotation messages to it
            if hasattr(pathway, "annotate"):
                cam_path = DevicePath.parse(subscriber_channel.topic)
                cam = self._visual_cortex.get_cam_by_path_for_annotations(cam_path)
                cam.add_annotation(
                    Annotation(f"{self._visual_cortex.id}/{pathway.name}", f=pathway.annotate, layer=layer + i)
                )

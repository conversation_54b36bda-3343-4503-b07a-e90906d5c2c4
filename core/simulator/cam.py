import glob
import os
import time
from argparse import Namespace
from typing import Any, Callable, Dict, List, Optional, Tuple, cast

import cv2
import numpy as np
import numpy.typing as npt

import lib.common.annotate
import lib.common.logging
from core.cv.retina.camera.node import Cam
from core.simulator.utils import DRIVE_RESOLUTION, PREDICT_RESOLUTION, SERVO_TO_PREDICT_SCALE, TARGET_RESOLUTION
from lib.common.fiducials.charuco_board import generate_aruco_marker_image, generate_cropped_charuco_image
from lib.common.math import floordiv_tuple
from lib.common.tasks import MakaTask, get_current
from lib.common.time import maka_control_timestamp_ms
from lib.common.time.sleep import sleep_ms

LOG = lib.common.logging.get_logger(__name__)

CROSSHAIR = floordiv_tuple(TARGET_RESOLUTION, 2)


def servo_to_predict(servo_rots: Tu<PERSON>[float, float], round_: bool = True) -> <PERSON><PERSON>[float, float]:
    """
    This is the inverse of :func:`predict_to_servo`
    :param servo_rots: The servo rotational coordinates, in servo space
    :param round_: whether to round output
    :return: The simulated prediction coordinates, in prediction camera space
    """

    # TODO can we add typing here?
    assert len(servo_rots) == 2

    # Choice of 4 is arbitrary
    px, py = servo_rots[0] * SERVO_TO_PREDICT_SCALE, servo_rots[1] * SERVO_TO_PREDICT_SCALE
    if round_:
        px, py = round(px), round(py)
    return px, py


_LASER_REFLECTION_COLOR_BGR = lib.common.annotate.BGR_RED


# Pull sim image from a directory of images
_global_simulator_image_dir_index: int = 0


def sim_image_from_dir(dirname: str, exts: List[str], sort_files: Optional[bool] = False) -> Optional[npt.NDArray[Any]]:
    global _global_simulator_image_dir_index
    files = []
    dirname = os.path.expanduser(dirname)
    for ext in exts:
        files.extend(glob.glob(dirname + "/*." + ext))
    if sort_files:
        files = sorted(files)
    if len(files) == 0:
        return None
    if _global_simulator_image_dir_index >= len(files):
        _global_simulator_image_dir_index = 0
    image = cv2.imread(files[_global_simulator_image_dir_index])
    _global_simulator_image_dir_index += 1
    return cast(npt.NDArray[Any], image)


# Simulated predict image
_global_simulater_predict_image: Optional[npt.NDArray[Any]] = None


def cached_simulated_predict_image(args: Namespace) -> npt.NDArray[Any]:
    global _global_simulater_predict_image
    if args.sim_image_dir is not None:
        _global_simulater_predict_image = sim_image_from_dir(args.sim_image_dir, ["png", "jpg"], True)
    if _global_simulater_predict_image is None:
        if args.sim_image is not None:
            _global_simulater_predict_image = cv2.imread(os.path.expanduser(args.sim_image))
        else:
            _global_simulater_predict_image = generate_cropped_charuco_image(PREDICT_RESOLUTION)
    return _global_simulater_predict_image


# Simulated drive image
_global_simulator_drive_image: Optional[npt.NDArray[Any]] = None
_global_blurred_simulator_drive_image: Optional[npt.NDArray[Any]] = None
_global_grey_simulator_drive_image: Optional[npt.NDArray[Any]] = None

next_flash = time.time()
flash = False


def cached_simulated_drive_image(
    args: Namespace, periodic_flash_sec: Optional[int] = None, blur_flash: bool = False
) -> npt.NDArray[Any]:
    global _global_simulator_drive_image
    # toggle the image in / out of view every N seconds:
    if periodic_flash_sec is not None:
        global flash, next_flash
        now = maka_control_timestamp_ms()
        if now > next_flash:
            flash = not flash
            next_flash = now + periodic_flash_sec * 1000
        if flash and _global_simulator_drive_image is not None:
            global _global_blurred_simulator_drive_image
            if blur_flash:
                if _global_blurred_simulator_drive_image is None:
                    # blur the image and darken
                    _global_blurred_simulator_drive_image = cv2.GaussianBlur(
                        (_global_simulator_drive_image / 3).astype(np.uint8), (19, 19), 100
                    )
                return _global_blurred_simulator_drive_image
            else:
                # full grey
                global _global_grey_simulator_drive_image
                if _global_grey_simulator_drive_image is None:
                    _global_grey_simulator_drive_image = np.full(
                        shape=(DRIVE_RESOLUTION[1], DRIVE_RESOLUTION[0], 3), fill_value=128, dtype=np.uint8
                    )
                return _global_grey_simulator_drive_image

    # show image
    if args.sim_image_dir is not None:
        sim_img = sim_image_from_dir(args.sim_image_dir, ["npz"], True)
        assert sim_img is not None
        _global_simulator_drive_image = cv2.resize(
            sim_img, dsize=tuple(DRIVE_RESOLUTION), fx=0, fy=0, interpolation=cv2.INTER_LINEAR
        )
    if _global_simulator_drive_image is None:
        if args.sim_image is not None:
            if args.sim_image.endswith(".npz"):
                loaded = np.load(os.path.expanduser(args.sim_image))
                sim_img = loaded["view_image"][:, :, ::-1]
                assert sim_img is not None
                _global_simulator_drive_image = cv2.resize(
                    sim_img, dsize=tuple(DRIVE_RESOLUTION), fx=0, fy=0, interpolation=cv2.INTER_LINEAR
                )
                _global_simulator_drive_depth = cv2.resize(
                    loaded["view_depth"], dsize=tuple(DRIVE_RESOLUTION), fx=0, fy=0, interpolation=cv2.INTER_LINEAR
                )
                _global_simulator_drive_image = np.concatenate(
                    (_global_simulator_drive_image, np.expand_dims(_global_simulator_drive_depth, axis=-1)), axis=-1
                )
            else:
                sim_img = cv2.imread(os.path.expanduser(args.sim_image))
                _global_simulator_drive_image = cv2.resize(
                    sim_img, dsize=tuple(DRIVE_RESOLUTION), fx=0, fy=0, interpolation=cv2.INTER_LINEAR
                )
        else:
            # ATTACK!
            _global_simulator_drive_image = generate_aruco_marker_image(DRIVE_RESOLUTION)
    return cast(npt.NDArray[Any], _global_simulator_drive_image)


class Properties:
    def get_all(self) -> Dict[str, Any]:
        return {}

    def set(self, **kwargs: Any) -> None:
        pass


class SimCam(Cam):
    def __init__(
        self,
        *argv: Any,
        poll_ms: int,
        resolution: Tuple[int, int],
        grabframe_func: Callable[[], npt.NDArray[Any]],
        **kwargs: Any
    ):
        super().__init__(*argv, **kwargs)
        self._grabframe = grabframe_func
        self._properties: Properties = Properties()
        self._resolution = resolution
        self._poll_ms = poll_ms

    def _control_loop(self) -> None:
        """simulator loop function"""
        LOG.debug(
            "%s Starting camloop. resolution %dx%d, preview scale %.2f, saving to %s...",
            self.short_name,
            *self.resolution,
            self.preview_scale,
            self.fs.abs_media_dir
        )
        task: Optional[MakaTask] = get_current()
        assert task is not None
        while True:
            task.tick()
            gframe = self._grabframe()
            image = gframe[:, :, :3]
            depth = None
            if gframe.shape[2] == 4:
                depth = gframe[:, :, 3:4]
            self._new_frame(image, depth)
            sleep_ms(self._poll_ms)

    def get_properties(self) -> Dict[str, Any]:
        assert self._properties
        return self._properties.get_all()

    def set_property(self, **kwargs: Any) -> None:
        assert self._properties
        self._properties.set(**kwargs)

    @property
    def resolution(self) -> Tuple[int, int]:
        return self._resolution

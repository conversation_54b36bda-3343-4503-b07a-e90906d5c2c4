from typing import Any, Dict, List, Tuple

import lib.common.logging
import lib.common.math
from core.controls.driver.hydraulics.hydraulics import Hydraulics, UnreachableHydraulicRotaryAngleException
from core.model.path import RelativeInstance

LOG = lib.common.logging.get_logger(__name__)


# Mimic Rover
##############################
#   +45              -45     #
#        /--------\          #
#         |      |           #
#         |      |           #
#         |      |           #
#        \--------/          #
#   -45              +45     #
##############################
DEFAULT_ROTARY_FRAME_OFFSET_ANGLES_DEG: Dict[str, float] = {
    RelativeInstance.BACK_LEFT: -45,
    RelativeInstance.BACK_RIGHT: +45,
    RelativeInstance.FRONT_LEFT: +45,
    RelativeInstance.FRONT_RIGHT: -45,
}

ROTARY_LIMITS: Tuple[float, float] = (-85, 85)


def offset_limits(limits: Tuple[float, float], offset: float) -> Tuple[float, float]:
    return limits[0] + offset, limits[1] + offset


class SimHydraulics(Hydraulics):
    def __init__(self, *argv: Any, legs: List[str], **kwargs: Any):
        super().__init__(*argv, **kwargs)
        self._speed_mph: float = 0

        assert len(legs) > 0
        for l in legs:
            assert l in RelativeInstance.quadrants()
        self._legs: List[str] = legs

        self._rotary_offsets: Dict[str, float] = {k: DEFAULT_ROTARY_FRAME_OFFSET_ANGLES_DEG[k] for k in self._legs}
        self._rotary_frame_angles_deg: Dict[str, float] = {k: 0 for k in self._rotary_offsets.keys()}
        self._rotary_limits_frame_angle_deg: Dict[str, Tuple[float, float]] = {
            k: offset_limits(ROTARY_LIMITS, self._rotary_offsets[k]) for k in self._legs
        }

        # just in case
        assert len(self._legs) == len(self._rotary_offsets) == len(self._rotary_frame_angles_deg)

    @property
    def forward(self) -> float:
        return self._speed_mph

    @property
    def rotaries(self) -> List[str]:
        return self._legs

    @property
    def rotary_position_frame_angles_deg(self) -> Dict[str, float]:
        return self._rotary_frame_angles_deg

    @property
    def rotary_offset_frame_angles_deg(self) -> Dict[str, float]:
        return self._rotary_offsets

    @property
    def rotary_limits_frame_angles_deg(self) -> Dict[str, Tuple[float, float]]:
        return self._rotary_limits_frame_angle_deg

    def drive(self, speed_mph: float) -> None:
        self._speed_mph = speed_mph

    def set_rotary_position(self, rotary: str, frame_angle_deg: float, log: bool = False) -> None:
        LOG.debug(f"{self.device_path} {rotary} --> {frame_angle_deg:.2f}°")

        limits: Tuple[float, float] = self._rotary_limits_frame_angle_deg[rotary]
        if not limits[0] <= frame_angle_deg <= limits[1]:
            raise UnreachableHydraulicRotaryAngleException(
                f"Rotary {rotary} cannot achieve requested frame angle {frame_angle_deg:.2f} due to limits {limits}"
            )

        self._rotary_frame_angles_deg[rotary] = frame_angle_deg

    def notify_positive_control(self) -> None:
        pass

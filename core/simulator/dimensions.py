from typing import <PERSON>ple

from core.coords.dimensions import RealWorldDimensions, ServoDimensions
from core.simulator.utils import SERVO_RESOLUTION, SERVO_TO_PREDICT_SCALE
from lib.common.math import deg2rad

ONE_TICK_DPX: Tuple[float, float] = (
    SERVO_TO_PREDICT_SCALE,
    SERVO_TO_PREDICT_SCALE,
)  # one servo tick most this many pixels in predict space
ONE_TICK_MECHANICAL_THETA: float = deg2rad(360 / SERVO_RESOLUTION)  # angle of one servo tick in radians
ONE_TICK_OPTICAL_THETA: float = 2 * ONE_TICK_MECHANICAL_THETA  # *2 for optical angle


MM_PER_TPX: float = 0.5  # arbitrarily chosen so that computed height is close to a meter


class SimServoDimensions(ServoDimensions):
    def __init__(
        self,
        pan_theta: float = ONE_TICK_OPTICAL_THETA,
        pan_means: Tuple[float, float] = ONE_TICK_DPX,
        tilt_theta: float = ONE_TICK_OPTICAL_THETA,
        tilt_means: Tuple[float, float] = ONE_TICK_DPX,
    ):
        super().__init__(pan_theta, pan_means, tilt_theta, tilt_means)


class SimRealWorldDimensions(RealWorldDimensions):
    def __init__(self, servo_tdims: ServoDimensions, mm_per_tpx: float = MM_PER_TPX):
        super().__init__(servo_tdims=servo_tdims, mm_per_tpx=mm_per_tpx)

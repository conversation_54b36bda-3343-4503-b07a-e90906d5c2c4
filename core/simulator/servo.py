import threading
from typing import Any, Optional, Tuple

import lib.common.logging
from core.controls.exterminator.model.servo import Servo
from core.simulator.utils import SERVO_RESOLUTION, simulate_race_condition

LOG = lib.common.logging.get_logger(__name__)


class SimServo(Servo):
    def __init__(self, *argv: Any, min: int, max: int, **kwargs: Any):
        self._position = min
        self._enabled = True
        self._simulate_race_condition_lock = threading.Lock()
        super().__init__(*argv, resolution=SERVO_RESOLUTION, min=min, max=max, max_range_degrees=360, **kwargs)

    def _compute_velocity_from_delta(self, position: int, time_ms: int) -> int:
        return 1

    def _set_position(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        if not self._enabled:
            raise Exception("Servo not enabled")
        self._position = position
        if await_settle:
            self._wait_position_complete()

    def _wait_position_complete(self) -> None:
        if not self._enabled:
            raise Exception("Servo not enabled")

    @simulate_race_condition
    def get_position(self) -> Tuple[int, Optional[int]]:
        return 0, self._position

    @simulate_race_condition
    def _get_position(self) -> int:
        return self._position

    def _disable(self) -> None:
        self._enabled = False

    def _enable(self) -> None:
        self._enabled = True

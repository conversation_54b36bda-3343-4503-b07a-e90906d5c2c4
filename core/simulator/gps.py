from typing import Any

from core.controls.frame.sensor.geolocation.node import GPSNode
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from lib.common.time import maka_control_timestamp_ms


class SimGPSNode(GPSNode):
    def __init__(self, *argv: Any, **kwargs: Any):
        GPSNode.__init__(self, *argv, **kwargs)

    @property
    def geoposition_lat_lon_alt(self) -> GeopositionLatLonAltMessage:
        return GeopositionLatLonAltMessage(timestamp_ms=maka_control_timestamp_ms(), lat=0, lon=0, alt=0)

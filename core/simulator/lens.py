from typing import Any, List, Tuple

from core.cv.retina.lens import LensNode


class SimLensNode(LensNode):
    def __init__(self, *argv: Any, **kwargs: Any):
        super().__init__(*argv, **kwargs)

        self._focus: float = 0

    def get_id(self) -> str:
        return "sim-lens"

    def get_focus(self) -> float:
        return self._focus

    def set_focus(self, focus: float) -> None:
        self._focus = focus

    def focus_range(self) -> Tuple[float, float]:
        return (0, 10)

    def focus_step_sizes(self) -> List[float]:
        return [1, 0.2]

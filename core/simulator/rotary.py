from typing import Any

from core.controls.driver.rotary import RotaryNode


class SimRotaryNode(RotaryNode):
    def __init__(self, *argv: Any, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        self._angle_deg: float = 0

    @property
    def angle_deg(self) -> float:
        return self._angle_deg

    def set_angle_deg(self, frame_angle_deg: float) -> None:
        self._angle_deg = frame_angle_deg

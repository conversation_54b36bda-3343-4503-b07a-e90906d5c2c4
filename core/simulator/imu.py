import asyncio
import random
from typing import Any, <PERSON><PERSON>

import numpy as np
import numpy.typing as npt

from core.controls.frame.sensor.imu.node import IMUNode
from core.controls.frame.sensor.magnetometer.calibration.base import CalibratedMagnetometer
from core.model.sensor import Sensor
from generated.core.controls.frame.model.acceleration_message import AccelerationMessage
from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
from generated.core.controls.frame.model.heading_message import HeadingMessage
from generated.core.controls.frame.model.mag_message import MagMessage
from lib.common.logging import get_logger
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.tasks.scheduler import dispatch
from lib.common.time import maka_control_timestamp_ms

SAMPLE_DIMENSIONS = Sensor.DEFAULT_SENSOR_DIMENSIONS

LOG = get_logger(__name__)

_CONSTANT_IMU_DRIFT = 10


class SimIMUNode(CalibratedMagnetometer, IMUNode):
    def __init__(self, *argv: Any, **kwargs: Any):
        IMUNode.__init__(self, *argv, **kwargs)
        CalibratedMagnetometer.__init__(self, rotation=self._rotation)
        self._heading = 0
        self._poll_rate_hz: int = 10

    async def tick(self) -> None:
        pass

    def _control_loop(self) -> None:
        LOG.debug(f"{self.device_path} Starting Sim IMU Thread...")
        asyncio.run_coroutine_threadsafe(
            dispatch(name=str(self.device_path), callback=self.tick, poll_ms=int(round(1000 / self._poll_rate_hz))),
            get_event_loop_by_name(),
        ).result()

    @property
    def heading(self) -> HeadingMessage:
        ret = self._heading
        drift = _CONSTANT_IMU_DRIFT * random.choice([-1, 1])
        if self._heading + drift >= 360:
            drift -= 360
        elif self._heading + drift < 0:
            drift += 360
        self._heading += drift
        return HeadingMessage(timestamp_ms=maka_control_timestamp_ms(), heading=ret)

    @property
    def acceleration(self) -> AccelerationMessage:
        return AccelerationMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def angular_velocity(self) -> AngularVelocityMessage:
        return AngularVelocityMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def mag(self) -> MagMessage:
        return MagMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def accel_gyro(self) -> Tuple[int, npt.NDArray[Any], npt.NDArray[Any]]:
        return maka_control_timestamp_ms(), np.zeros(SAMPLE_DIMENSIONS), np.zeros(SAMPLE_DIMENSIONS)

from typing import Any

import lib.common.logging
from core.controls.exterminator.model.laser import Laser

LASER_RADIUS_TPX = 11

LOG = lib.common.logging.get_logger(__name__)


class SimLaser(Laser):
    def __init__(self, *argv: Any, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        self._on = False  # not used by anything

    def arm(self) -> None:
        super().arm()
        self._armed = True

    def disarm(self) -> None:
        super().disarm()
        self._armed = False

    def on(self) -> None:
        super().on()
        if self.armed:
            LOG.info("{} Firing laser".format(self.id))
            self._on = True
        else:
            LOG.warning("{} Must arm before firing!".format(self.id))

    def off(self) -> None:
        super().off()
        self._on = False

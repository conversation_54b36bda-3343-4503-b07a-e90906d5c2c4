import asyncio
from typing import Any, <PERSON><PERSON>

import numpy as np
import numpy.typing as npt

from core.controls.frame.sensor.ins.node import INSNode
from core.controls.frame.sensor.magnetometer.calibration.base import CalibratedMagnetometer
from core.model.sensor import Sensor
from generated.core.controls.frame.model.acceleration_message import AccelerationMessage
from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.heading_message import HeadingMessage
from generated.core.controls.frame.model.mag_message import MagMessage
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.logging import get_logger
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.tasks.scheduler import dispatch
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)

SAMPLE_DIMENSIONS = Sensor.DEFAULT_SENSOR_DIMENSIONS


class SimINSNode(CalibratedMagnetometer, INSNode):
    def __init__(self, *argv: Any, **kwargs: Any):
        INSNode.__init__(self, *argv, **kwargs)
        CalibratedMagnetometer.__init__(self, rotation=self._rotation)
        self._poll_rate_hz = 1

    async def tick(self) -> None:
        pass

    def _control_loop(self) -> None:
        LOG.debug(f"{self.device_path} Starting Sim INS Thread...")
        asyncio.run_coroutine_threadsafe(
            dispatch(name=str(self.device_path), callback=self.tick, poll_ms=int(round(1000 / self._poll_rate_hz))),
            get_event_loop_by_name(),
        ).result()

    @property
    def heading(self) -> HeadingMessage:
        return HeadingMessage(timestamp_ms=maka_control_timestamp_ms(), heading=0)

    @property
    def geoposition_ecef_m(self) -> GeopositionEcefMessage:
        return GeopositionEcefMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def acceleration(self) -> AccelerationMessage:
        return AccelerationMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def angular_velocity(self) -> AngularVelocityMessage:
        return AngularVelocityMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def accel_gyro(self) -> Tuple[int, npt.NDArray[Any], npt.NDArray[Any]]:
        # mimic duro for now until we have this modeled better
        return maka_control_timestamp_ms(), np.zeros(SAMPLE_DIMENSIONS), np.zeros(SAMPLE_DIMENSIONS)

    @property
    def mag(self) -> MagMessage:
        return MagMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def velocity(self) -> VelocityMessage:
        return VelocityMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

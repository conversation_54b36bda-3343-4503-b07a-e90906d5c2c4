from typing import Any, Dict

import lib.common.logging
from core.controls.driver.motor import DriveMotor

LOG = lib.common.logging.get_logger(__name__)

DEFAULT_MIN_SPEED: float = 0
DEFAULT_MAX_SPEED: float = 0.75


class SimDriveMotor(DriveMotor):
    def __init__(
        self, *argv: Any, min_speed: float = DEFAULT_MIN_SPEED, max_speed: float = DEFAULT_MAX_SPEED, **kwargs: Any
    ):
        super().__init__(*argv, min_speed=min_speed, max_speed=max_speed, **kwargs)
        self._speed_mph: float = 0
        self._steer: float = 0  # TODO delete me after sim hydraulics

    def status_callback(self) -> Dict[str, Any]:
        return {"value": str(self.speed_mph)}

    @property
    def speed_mph(self) -> float:
        return self._speed_mph

    @property
    def steer(self) -> float:
        return self._steer

    def set_speed_mph(self, speed_mph: float) -> None:
        self._speed_mph = speed_mph

    def stop(self) -> None:
        super().stop()
        self._speed_mph = 0

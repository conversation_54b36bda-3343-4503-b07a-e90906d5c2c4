# This file is for constants and util functions shared across the simulator
import time
from typing import Any, Callable, Tuple

import lib.common.logging
from lib.common.error import MakaException

LOG = lib.common.logging.get_logger(__name__)

PREDICT_RESOLUTION = (1920, 1200)
TARGET_RESOLUTION = (1920, 1080)
DRIVE_RESOLUTION = (1920, 1080)
IMAGE_LIMITS = [
    (0, 0),
    (PREDICT_RESOLUTION[0], 0),
    (PREDICT_RESOLUTION[0], PREDICT_RESOLUTION[1]),
    (0, PREDICT_RESOLUTION[1]),
]

TARGET_FRUSTRUM_RATIO = 1.125  # how much of the outside of the target screen is unviewable


def _simulated_target_area_to_target_frustrum(target_bounds: Tuple[int, int]) -> Tuple[int, int]:
    """
    Here, "frustrum" is the region of the target camera that is visible
    (regions on edge will be blocked by physical barriers and appear black)
    https://en.wikipedia.org/wiki/Frustum
    :param target_bounds: The total target camera size, in pixels
    :return: The target frustrum bounds
    """

    # TODO can we add typing here?
    assert len(target_bounds) == 2

    # TODO long-term better abstract the mapping as it won't be linear
    return int(target_bounds[0] // TARGET_FRUSTRUM_RATIO), int(target_bounds[1] // TARGET_FRUSTRUM_RATIO)


# meant for import in other classes
SIMULATOR_TARGET_SIZE = _simulated_target_area_to_target_frustrum(TARGET_RESOLUTION)


SERVO_RESOLUTION = 4096  # ticks per revolution

# TODO improve this so we can test other resolutions
SERVO_TO_PREDICT_SCALE = 1


def predict_to_servo(predict_coords: Tuple[float, float]) -> Tuple[float, float]:
    """
    This is the inverse of :func:`servo_to_predict`
    """

    # TODO can we add typing here?
    assert len(predict_coords) == 2

    # Choice of 4 is arbitrary
    return predict_coords[0] / SERVO_TO_PREDICT_SCALE, predict_coords[1] / SERVO_TO_PREDICT_SCALE


SERVO_BOUNDS = predict_to_servo(PREDICT_RESOLUTION)  # max_pan, max_tilt


class SimulatedRaceConditionException(MakaException):
    """Raise when a race condition is detected in simulator"""

    pass


_SIMULATE_RACE_CONDITION_PARAM = "simulate_race_condition"


F = Callable[..., Any]


def simulate_race_condition(func: F) -> F:
    """
    Function decorator to try to simulate a race condition over calling a nonconcurrent function.
    This is done by funneling access through a lock that is acquired with no timeout. Allows for
    passing parameter to skip this check, if desired.

    self._simulate_race_condition_lock must exist and be a lock.
    """

    def simulate_race_condition_decorator(self: Any, *argv: Any, **kwargs: Any) -> Any:
        # allow for skipping the check if client passes in simulate_race_condition=False.
        # This allows us to skip the check when we know it will trigger but we are ok.
        # Simulators often need to cheat references to each other to know environmental state,
        # so this is generally ok
        do_simulate_race_condition = True
        if _SIMULATE_RACE_CONDITION_PARAM in kwargs:
            do_simulate_race_condition = kwargs[_SIMULATE_RACE_CONDITION_PARAM]
            del kwargs[_SIMULATE_RACE_CONDITION_PARAM]

        # early exit if no check, standard function evaluation
        if not do_simulate_race_condition:
            return func(self, *argv, **kwargs)

        # simulate a race condition by funneling access through a single lock that is acquired with no timeout
        try:
            success = self._simulate_race_condition_lock.acquire(blocking=True, timeout=0)
            # try to force a scheduler decision. In theory, should slightly increase chance of thread switch
            # This is done to try to eagerly surface race conditions
            time.sleep(0)

            # we were unsuccessful. There must be another function using the instance lock
            if not success:
                msg = "Race Condition Detected! Unexpected Lock Contention!"
                LOG.critical(msg)
                raise SimulatedRaceConditionException(msg)

            return func(self, *argv, **kwargs)
        finally:
            # safely release the lock
            if self._simulate_race_condition_lock.locked():
                self._simulate_race_condition_lock.release()

    return simulate_race_condition_decorator

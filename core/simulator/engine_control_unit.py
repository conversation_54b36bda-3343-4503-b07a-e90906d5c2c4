from typing import Any

from core.controls.driver.engine_control_unit import EngineControlUnitNode
from lib.common.time import maka_control_timestamp_ms


class SimEngineControlUnit(EngineControlUnitNode):
    def __init__(self, *argv: Any, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        self._init_time_ms = maka_control_timestamp_ms()

    async def runtime_ms(self) -> int:
        return maka_control_timestamp_ms() - self._init_time_ms

    async def rpm(self) -> float:
        return 1

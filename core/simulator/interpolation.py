from typing import Optional, <PERSON>ple

from core.coords.interpolation import Interpolation, StratifiedInterpolation
from core.coords.strata import BivariateStrata
from core.simulator.utils import predict_to_servo
from lib.common.math import multiply_tuple


def delta_target_to_delta_servo(delta_tcoords: Tuple[float, float]) -> <PERSON><PERSON>[float, float]:
    """
    This is a simple simulation of the underlying spline.
    """
    # target space has a 1-1 mapping to predict space
    # however, there is a tricky sign flip
    # moving left in target space is actually moving the underlying picture to the right
    # it's easier to think of the click point moving to the crosshair, as opposed to vice versa

    # so all we need to do is reverse the sign of the delta in order to translate it to a delta in prediction space
    delta_pcoords = multiply_tuple(delta_tcoords, -1)

    # and then go from prediction space to servo space (delta vs absolute here doesn't matter as its linear)
    delta_servos = predict_to_servo(delta_pcoords)

    return delta_servos


class SimInterpolation(Interpolation):
    def __init__(self, name: str, num_samples: int):
        super().__init__(name, num_samples)
        self.min_samples: int = 0

    def interpolate(self, xy: Tuple[float, float]) -> Tuple[int, Optional[Tuple[float, float]]]:
        return 0, predict_to_servo(xy)


class SimStratifiedInterpolation(StratifiedInterpolation):
    def __init__(self, name: str, num_samples: int, strata: BivariateStrata):
        super().__init__(name, num_samples, strata)
        self.min_samples = 0

    def interpolate(self, _: Tuple[int, int], xy: Tuple[float, float]) -> Tuple[int, Optional[Tuple[float, float]]]:
        return 0, delta_target_to_delta_servo(xy)

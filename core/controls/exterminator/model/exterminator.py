import asyncio
import concurrent.futures
import datetime
import functools
import os
from argparse import Namespace
from enum import Enum
from typing import Any, Dict, List, Optional, cast

import grpc
import pytz

import lib.common.logging
from core.controls.exterminator.calibration.level import CalibrationLevel
from core.controls.exterminator.controllers.aimbot.process.client.aimbot_client import (
    AimbotClient,
    shared_aimbot_client,
)
from core.controls.exterminator.model.gimbal import Gimbal2D
from core.controls.exterminator.model.laser import Laser
from core.controls.exterminator.model.row_module import RowModule
from core.controls.exterminator.model.scanner import Scanner
from core.controls.exterminator.model.servo import Servo
from core.cv.retina.camera.node import Cam
from core.cv.retina.retina import Retina
from core.model.actuator import Actuator
from core.model.node.type import NodeType
from core.model.path import DevicePath
from core.web.websocket.path.exterminator import ExterminatorWebSocket
from core.web.websocket.requests.servo_request import ServoRequest
from lib.common.asyncio.event_loop import use_specific_loop
from lib.common.commander.client import CommanderClient
from lib.common.generation import is_slayer
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time import timestamp_filename

LOG = lib.common.logging.get_logger(__name__)


class CalibrationConfig:
    """namespace"""

    ROW_MODULES = "row_modules"


class ExterminatorState(str, Enum):
    IDLE = "IDLE"
    ERROR = "ERROR"
    EXTERMINATING = "EXTERMINATING"
    WEEDING = "WEEDING"


class Exterminator(Actuator):

    STRATEGIES: List[str] = [
        RowModule.SHOOT_P2P_STATIONARY,
        RowModule.SHOOT_EXPERIMENT,
        RowModule.SHOOT_P2P_STATIONARY_VISUAL_CORTEX,
        RowModule.DO_NOTHING,
    ]

    def __init__(
        self, *argv: Any, args: Optional[Namespace] = None, calib_cfg: Optional[Dict[str, Any]] = None, **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)
        self._args = args
        self._calib_cfg = calib_cfg

        self._state: ExterminatorState = ExterminatorState.IDLE
        self._tpx_per_ms: float = 4

        if calib_cfg is not None:
            self.config_load(calib_cfg)

        # Must be acccessed on async loop only
        self._async_loop = get_event_loop_by_name()
        with use_specific_loop(self._async_loop):
            self._async_makannihilate_task_lock = asyncio.Lock()
        self._aimbot_client = shared_aimbot_client
        self._aimbot_running: bool = False

        self._websocket: Optional[ExterminatorWebSocket] = None
        self._commander_addr = "10.10.3.1" if is_slayer() else "127.0.0.1"
        row: str = str(os.getenv("MAKA_ROW")) if os.getenv("MAKA_ROW") is not None else "1"
        self._row_number = int(row) if row.isdigit() else 1
        self._commander_client: CommanderClient = CommanderClient(self._commander_addr)

    @property
    def aimbot_client(self) -> AimbotClient:
        return self._aimbot_client

    # --- Boot --- #

    def _cache_named_references(self) -> None:
        self._row_modules: Dict[DevicePath, RowModule] = {}

        for child_device_path in self.children():
            LOG.info(f"Child Device Path: {child_device_path} Type: {child_device_path.leaf().type}")
            child_reference = self.subtree[child_device_path]
            assert (
                child_device_path.leaf().type == NodeType.ROW_MODULE
            ), f"This is a bug. Unexpected child device path: {child_device_path}"
            self._row_modules[child_device_path] = cast(RowModule, child_reference)

        # not a leaf node so easiest to just loop through row_modules and pick off the scanners
        self._scanners: Dict[DevicePath, Scanner] = {}
        for row_module in self._row_modules.values():
            for scanner in row_module.scanners:
                self._scanners[scanner.device_path] = scanner
        assert len(self._row_modules) <= len(self._scanners), "Cannot have more row_modules than scanners?"

        self._lasers: Dict[DevicePath, Laser] = cast(
            Dict[DevicePath, Laser], self.get_nodes(NodeType.LASER, required=True)
        )

        self._retina: Optional[Retina] = cast(Optional[Retina], self.get_node(NodeType.RETINA))

    def setup_async(self, loop: asyncio.BaseEventLoop) -> None:
        assert self._websocket is None
        self._websocket = ExterminatorWebSocket(exterminator=self)
        self._websocket.setup_async(loop)

    def shutdown_async(self) -> None:
        assert self._websocket is not None
        self._websocket.shutdown_async()

    def config_save(self, debug: bool = False) -> Dict[str, Any]:
        """
        Robot config_save callback.
        """
        config: Dict[str, Any] = {}
        config[CalibrationConfig.ROW_MODULES] = {}
        for row_module in self.row_modules:
            config[CalibrationConfig.ROW_MODULES][row_module.device_path] = row_module.config_save(debug=debug)
        return config

    def reset_level(self, level: CalibrationLevel) -> None:
        for row_module in self.row_modules:
            row_module.reset_level(level)

    def config_load(self, calib_cfg: Dict[str, Any]) -> None:
        if calib_cfg is not None and CalibrationConfig.ROW_MODULES in calib_cfg:
            for krow, row_cfg in calib_cfg[CalibrationConfig.ROW_MODULES].items():
                if krow in self._row_modules:
                    self._row_modules[krow].config_load(row_cfg)

    # --- Properties --- #

    @property
    def last_weed_count(self) -> int:
        return sum([r.last_weed_count for _, r in self._row_modules.items()])

    @property
    def armed(self) -> bool:
        return any([l.armed for l in self._lasers.values()])

    @property
    def state(self) -> ExterminatorState:
        return self._state

    @property
    def number_of_row_modules(self) -> int:
        return len(self._row_modules)

    @property
    def row_modules(self) -> List[RowModule]:
        # TODO Reconsider the APIs using this directly
        return list(self._row_modules.values())

    @property
    def calib_level(self) -> CalibrationLevel:
        return CalibrationLevel(
            min([row_module.calib_level for row_module in self._row_modules.values()])
            if self.number_of_row_modules > 0
            else CalibrationLevel.NONE
        )

    def arm(self) -> None:
        for laser in self._lasers.values():
            laser.arm()

    def disarm(self) -> None:
        for laser in self._lasers.values():
            laser.disarm()

    # --- Status --- #

    def dump_callback(self) -> Dict[str, Any]:
        return {
            "lasers": [dp for dp in self._lasers.keys()],
            "row_modules": [rm for rm in self._row_modules.keys()],
            "scanners": [dp for dp in self._scanners.keys()],
            "status": self.status_callback(),
        }

    def status_callback(self) -> Dict[str, Any]:
        armed = False
        row_enabled = False
        try:
            state = asyncio.run_coroutine_threadsafe(
                asyncio.wait_for(self._commander_client.GetNextDashboardState(0), timeout=1), self._async_loop
            ).result(timeout=1)
            armed = state.lasers_enabled
            row_enabled = state.row_enabled[self._row_number - 1]
        except asyncio.TimeoutError:
            pass
        except grpc.aio._call.AioRpcError:
            pass
        except concurrent.futures.TimeoutError:
            pass
        return {
            "armed": armed,
            "level": str(self.calib_level),
            "state": ExterminatorState.WEEDING if row_enabled else ExterminatorState.IDLE,
        }

    # --- Manual Control Calls --- #

    def handle_servo_move(self, servo_request: ServoRequest) -> ServoRequest:
        if servo_request.servo_path is not None:
            assert servo_request.servo_path in self.subtree
            servo: Servo = cast(Servo, self.subtree[servo_request.servo_path])
            assert servo_request.servo_value is not None
            servo.set_position_unsafe(servo_request.servo_value, await_settle=True)

        assert servo_request.gimbal_path in self.subtree
        gimbal: Gimbal2D = cast(Gimbal2D, self.subtree[servo_request.gimbal_path])
        servos_position = gimbal.get_position()
        servo_request.set_servos(servos_position)
        servo_request.set_recommended_servo_step(gimbal.resolution)
        return servo_request

    def predict(self) -> None:
        """Run CV prediction."""
        LOG.info("Starting Prediction")
        for row_module in self.row_modules:
            row_module.predict()
        LOG.info("Prediction Complete")

    def clear_masks(self) -> None:
        for row_module in self.row_modules:
            row_module.clear_masks()

    def makannihilate_from_sync(self, wait: bool = True) -> None:
        future = asyncio.run_coroutine_threadsafe(self.makannihilate(), self._async_loop)
        if wait:
            future.result()

    def cancel_makannihilate_from_sync(self) -> None:
        future = asyncio.run_coroutine_threadsafe(self.cancel_makannihilate(), self._async_loop)
        future.result()

    async def cancel_makannihilate(self) -> None:
        assert self._async_loop == asyncio.get_event_loop()
        async with self._async_makannihilate_task_lock:
            if self._state == ExterminatorState.WEEDING:
                if await self._aimbot_client.ping(42) == 42:
                    LOG.info("Stopping Makannihilate in Process Separated Aimbot")
                    await self._aimbot_client.stop_aimbot()
                    self._state = ExterminatorState.IDLE
                    return
            else:
                return

    def _drive_capture(self, drive_cam: Optional[Cam]) -> None:
        # TODO move this to commander
        if drive_cam is None:
            return

        session = f"carzalia-{pytz.timezone('America/Los_Angeles').fromutc(datetime.datetime.utcnow()).date()}/drive"
        full_path = os.path.join(self.fs.abs_data_dir, "labels", session)
        os.makedirs(full_path, exist_ok=True)
        drive_cam.next()
        drive_cam.capture_frame(subdir=full_path, annotated=False, filename=f"{timestamp_filename('')}.png")

    async def _drive_capture_task(self) -> None:
        return  # TODO make this configurable
        assert self._retina is not None
        while True:
            await asyncio.gather(
                *[
                    asyncio.get_event_loop().run_in_executor(
                        None, functools.partial(self._drive_capture, drive_cam=self._retina.front_right_cam)
                    ),
                    asyncio.get_event_loop().run_in_executor(
                        None, functools.partial(self._drive_capture, drive_cam=self._retina.back_right_cam)
                    ),
                ]
            )
            await asyncio.sleep(300)  # 5 minutes

    async def makannihilate(self) -> None:
        assert self._async_loop == asyncio.get_event_loop()
        async with self._async_makannihilate_task_lock:
            if self._state != ExterminatorState.WEEDING:
                if await self._aimbot_client.ping(42) == 42:
                    LOG.info("Starting Makannihilate in Process Separated Aimbot")
                    await self._aimbot_client.start_aimbot()
                    self._state = ExterminatorState.WEEDING
                    return
            else:
                return

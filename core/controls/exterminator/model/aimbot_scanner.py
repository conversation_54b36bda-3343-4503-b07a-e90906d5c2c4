import asyncio
import time
from os import getenv
from typing import Op<PERSON>, <PERSON><PERSON>, cast

import aioredis

from config.client.cpp.config_client_python import ConfigTree, get_computer_config_prefix, get_global_config_subscriber
from core.controls.exterminator.controllers.aimbot.metrics import SERVO_FAILURE_DETECTOR_SUCCESS_RATE
from core.controls.exterminator.nodes.pulczar_board.pulczar_board_gimbal import PulczarBoardGimbal
from core.controls.exterminator.nodes.pulczar_board.pulczar_board_laser import PulczarBoardLaser
from cv.runtime.client import CVRuntimeClient
from lib.common.db.carbon_robot_db import CarbonRobotDBLite
from lib.common.devices.boards.pulczar.pulczar_board_device import PulczarBoardDevice
from lib.common.devices.device import DeviceStatus
from lib.common.generation import is_slayer
from lib.common.geometric.cpp.geometric_python import GeometricScanner
from lib.common.geometric.geometric_calibration import get_target_cam_calibration
from lib.common.geometric.geometric_space import get_geometric_space
from lib.common.logging import get_logger
from lib.common.math import subtract_tuples
from lib.common.time.time import maka_control_timestamp_ms
from lib.common.types import GimbalPosition
from metrics.pybind.metrics_python import DailyTimezone
from scanner.pybind.scanner_python import ScannerWrapper
from trajectory.pybind.trajectory_python import TrackedItemCentroid

LOG = get_logger(__name__)


ERROR_SERVO_LIMITED: int = 10002  # requested position is limited by servo limits


class ServoFailureDetector:
    def __init__(self) -> None:
        subscriber = get_global_config_subscriber()
        self.enabled_node = subscriber.get_config_node(
            "common", "shooting/rotary_and_p2p/servo_failure_detection/enabled"
        )
        self.enabled = self.enabled_node.get_bool_value()
        self.enabled_node.register_callback(self._update_configs)
        self.smoothing_node = subscriber.get_config_node(
            "common", "shooting/rotary_and_p2p/servo_failure_detection/smoothing"
        )
        self.smoothing = self.smoothing_node.get_float_value()
        self.smoothing_node.register_callback(self._update_configs)
        self.min_ratio_node = subscriber.get_config_node(
            "common", "shooting/rotary_and_p2p/servo_failure_detection/minimum_good_ratio"
        )
        self.min_ratio = self.min_ratio_node.get_float_value()
        self.min_ratio_node.register_callback(self._update_configs)
        self.min_bad_delta_node = subscriber.get_config_node(
            "common", "shooting/rotary_and_p2p/servo_failure_detection/min_bad_delta"
        )
        self.min_bad_delta = self.min_bad_delta_node.get_uint_value()
        self.min_bad_delta_node.register_callback(self._update_configs)
        self.success_rate = 1.0

    def _update_configs(self) -> None:
        self.enabled = self.enabled_node.get_bool_value()
        self.smoothing = self.smoothing_node.get_float_value()
        self.min_ratio = self.min_ratio_node.get_float_value()
        self.min_bad_delta = self.min_bad_delta_node.get_uint_value()

    def _add_value(self, value: float) -> None:
        self.success_rate = (self.success_rate * self.smoothing) + (value * (1 - self.smoothing))

    def add_success(self) -> None:
        self._add_value(1.0)

    def add_failure(self) -> None:
        self._add_value(0.0)

    def add_ticks_delta(self, delta_ticks: int) -> None:
        if delta_ticks > self.min_bad_delta:
            self.add_failure()
        else:
            self.add_success()

    def is_failed(self) -> bool:
        if not self.enabled:
            return False
        return self.success_rate < self.min_ratio

    def reset(self) -> None:
        self.success_rate = 1.0


class AimbotScanner:
    """
    The scanner object encapsulates a laser, the servo motion that positions it, and
    the mapping between servo motion and camera coordinate systems.  There is one
    scanner per physical laser output.
    """

    NEXT_ID = 0

    def __init__(
        self,
        gimbal: PulczarBoardGimbal,
        laser: PulczarBoardLaser,
        numeric_id: int,
        redis_client: aioredis.Redis,
        dtz: DailyTimezone,
        cv_hostname: str,
    ):
        # TODO Make These private
        self.gimbal = gimbal
        self.laser = laser
        self._redis = redis_client
        self._dtz = dtz

        self.last_weed = {"id": -1, "failed": False}

        self._numeric_id: int = numeric_id
        self._target: Optional[Tuple[int, int]] = None
        self._load_cfg()
        self._geometric_scanner: GeometricScanner = get_geometric_space().get_device(
            GeometricScanner, f"scanner{self.numeric_id}"
        )
        self._scanner_wrapper = ScannerWrapper(
            self.numeric_id, self.geometric_scanner, self.gimbal.pan.limits, self.gimbal.tilt.limits, self._cfg
        )
        self.board.set_enabled_func(self._scanner_wrapper.enabled)
        if self._target is not None:
            self._scanner_wrapper.set_crosshair(self._target)

        self._cv_runtime_client = CVRuntimeClient(hostname=cv_hostname)

        self.tilt_center_ticks: int = 5125
        self.tilt_mirror_height: float = 33.5
        self._aimbot_running: bool = False
        self._last_centroid_ts = 0
        self._last_centroid_cam = ""
        self._last_centroid_p2p_coords = (0.0, 0.0)
        self._alt_last_centroid_ts = 0
        self._alt_last_centroid_cam = ""
        self._alt_last_centroid_p2p_coords = (0.0, 0.0)

        self._db = CarbonRobotDBLite.get_instance()
        self.gimbal.add_callback(self._set_data)
        self.laser.enable(self.enabled)
        self.servo_failure_detectors = (ServoFailureDetector(), ServoFailureDetector())
        self._update_metrics()

        self._set_data()

        LOG.debug(f"Initialized Scanner. Numeric ID: {self._numeric_id}")

    async def get_status(self) -> DeviceStatus:
        return await self.gimbal.get_status()

    def add_to_failure_detection(self, pos_delta: Tuple[int, int]) -> None:
        self.servo_failure_detectors[0].add_ticks_delta(pos_delta[0])
        self.servo_failure_detectors[1].add_ticks_delta(pos_delta[1])
        self._update_metrics()

    def _set_data(self) -> None:
        skews = self.gimbal.get_delta_target_skews()

        centers = (
            int(round((self.gimbal.max[0] + self.gimbal.min[0]) / 2)),
            int(round((self.gimbal.max[1] + self.gimbal.min[1]) / 2)),
        )
        self._geometric_scanner.set_servo_centers(centers)
        if abs(skews[0]) > 0.1 and abs(skews[1]) > 0.1:
            self._geometric_scanner.set_override_skews(skews[0], skews[1])
            LOG.info(f"Setting Override Skews for Scanner {self.id} at {skews}")
        self._scanner_wrapper.limit_reset_callback()

    def _load_cfg(self) -> None:
        self._cfg = self._find_cfg()
        self._create_default_node()
        assert self._cfg is not None, "Config node does not exists."
        target_cfg = self._cfg.get_node("target")
        assert target_cfg is not None, "Target config node does not exist."
        self._target = (target_cfg.get_node("x").get_int_value(), target_cfg.get_node("y").get_int_value())
        self._sync_target_cam_cfg()

    def _find_cfg(self) -> Optional[ConfigTree]:
        subscriber = get_global_config_subscriber()
        for scanner_cfg in subscriber.get_config_node("aimbot", "scanners").get_children_nodes():
            if scanner_cfg.get_name() == self.cfg_node_name:
                return scanner_cfg
        return None

    def _sync_target_cam_cfg(self) -> None:
        try:
            if not is_slayer():
                return
            eeprom_serial = self.board.get_serial_number_config_sync()
            if not eeprom_serial:
                LOG.warning(f"Camera serial number does not appear to be set in eeprom for scanner {self.numeric_id}")
                return
            node = get_global_config_subscriber().get_config_node(
                "cv", f"cameras/target{self.numeric_id}/serial_number"
            )
            if not node:
                LOG.warning(f"Could not find camera config node for scanner {self.numeric_id}")
                return
            if eeprom_serial != node.get_string_value():
                LOG.info(
                    f"serial number mismatch for scanner {self.numeric_id}. replacing cfg value '{node.get_string_value()}' with '{eeprom_serial}' from eeprom"
                )
                path = f"{get_computer_config_prefix()}{node.get_path()}"
                client = get_global_config_subscriber().get_client()
                client.set_string_value(path, eeprom_serial)
        except Exception as e:
            LOG.warning(f"Failed to sync target camera serial number for scanner {self.numeric_id}. Exception: {e}")

    def set_running(self, running: bool) -> None:
        self._aimbot_running = running
        if not running:
            self._scanner_wrapper.cancel()  # force stop any awaiting threads

    def _update_metrics(self) -> None:
        try:
            SERVO_FAILURE_DETECTOR_SUCCESS_RATE.labels(scanner_id=f"scanner{self.numeric_id}", servo="pan").set(
                self.servo_failure_detectors[0].success_rate
            )
            SERVO_FAILURE_DETECTOR_SUCCESS_RATE.labels(scanner_id=f"scanner{self.numeric_id}", servo="tilt").set(
                self.servo_failure_detectors[1].success_rate
            )
        except Exception as ex:
            LOG.error(ex)

    async def request_reset(self, metrics_only: bool = False) -> None:
        self.servo_failure_detectors[0].reset()
        self.servo_failure_detectors[1].reset()
        self._update_metrics()
        LOG.info(f"Resetting Scanner {self.numeric_id} with metric only {metrics_only}")
        if not metrics_only:
            await self.board.request_reset()

    @property
    def running(self) -> bool:
        return self._aimbot_running

    @property
    def numeric_id(self) -> int:
        return self._numeric_id

    @property
    def cfg_node_name(self) -> str:
        return f"scanner{self.numeric_id}"

    @property
    def cv_runtime_client(self) -> CVRuntimeClient:
        return self._cv_runtime_client

    @property
    def scanner_wrapper(self) -> ScannerWrapper:
        return self._scanner_wrapper

    # Position/Targeting APIs
    @property
    def target(self) -> Optional[Tuple[int, int]]:
        """
        Return the target position, in target space
        """
        return self._target

    @property
    def enabled(self) -> bool:
        return self._scanner_wrapper.enabled()

    def enable(self, on: bool) -> None:
        self._scanner_wrapper.enable(on)
        self.laser.enable(on)

    async def set_target(self, target: Tuple[float, float]) -> None:
        rounded_target = int(round(target[0])), int(round(target[1]))
        self._create_default_node()
        prefix = get_computer_config_prefix()
        client = get_global_config_subscriber().get_client()
        assert self._cfg is not None, "Config node does not exist."
        target_path = f"{prefix}{self._cfg.get_path()}/target"
        client.set_int_value(f"{target_path}/x", rounded_target[0])
        client.set_int_value(f"{target_path}/y", rounded_target[1])
        self._target = rounded_target
        self._scanner_wrapper.set_crosshair(self._target)
        base_key = f"{getenv('MAKA_ROW')}/{self.numeric_id}"
        day_str = await asyncio.get_event_loop().run_in_executor(None, lambda: self._dtz.get_day())
        if day_str != "":
            data = {f"{base_key}/day": day_str, f"{base_key}/epoch": str(maka_control_timestamp_ms())}
            await self._redis.hset("crosshair_cal", mapping=data)
        else:
            LOG.warning("Failed to get day from DailyTimezone, skipping redis update")

    def _create_default_node(self) -> None:
        if self._cfg is not None:
            return

        (roi_width, roi_height) = get_target_cam_calibration().roi_size
        target = (int(roi_width // 2), int(roi_height // 2))
        prefix = get_computer_config_prefix()
        client = get_global_config_subscriber().get_client()
        client.add_to_list(f"{prefix}/aimbot/scanners", self.cfg_node_name)
        self._cfg = self._find_cfg()
        assert self._cfg is not None, "Config node does not exist."
        target_path = f"{prefix}{self._cfg.get_path()}/target"
        client.set_int_value(f"{target_path}/x", target[0])
        client.set_int_value(f"{target_path}/y", target[1])

    def clear_target(self) -> None:
        self._target = None

    # Coordinate translation APIs

    def geometric_dtarget_to_servos_abs(
        self, delta_tcoord: Tuple[float, float], pos: Tuple[int, int]
    ) -> Tuple[float, float]:
        """Use the geometric scanner to calculate the required servo position"""
        scanner = self.geometric_scanner
        delta_px = tuple(map(round, delta_tcoord))
        servo_pos = scanner.get_servo_position_for_delta_px(cast(Tuple[int, int], delta_px), pos)
        return (float(servo_pos[0]), float(servo_pos[1]))

    def servo_limit(self, servos: Tuple[int, int]) -> Tuple[int, int]:
        """
        Apply servo limits to the given servos
        """
        assert self.gimbal is not None
        return self.gimbal.clamp(GimbalPosition(*servos))

    async def trace_boundaries(self, duration_s: int) -> None:
        try:
            line_duration_ms = int((duration_s * 1000.0) / 4.0)
            fast_settle_time_ms = 100
            servo_corners = [
                (self.gimbal.pan_limits[0], self.gimbal.tilt_limits[0]),
                (self.gimbal.pan_limits[1], self.gimbal.tilt_limits[0]),
                (self.gimbal.pan_limits[1], self.gimbal.tilt_limits[1]),
                (self.gimbal.pan_limits[0], self.gimbal.tilt_limits[1]),
            ]

            # Go To Starting Point
            await self.gimbal.async_set_position(GimbalPosition(*servo_corners[0]))
            await asyncio.sleep(fast_settle_time_ms / 1000)

            # Turn Laser On
            await self.laser.async_on()

            # Trace Lines
            await self.gimbal.async_set_position(
                GimbalPosition(*servo_corners[1]), time_ms=line_duration_ms, await_settle=False
            )
            await asyncio.sleep(line_duration_ms / 1000)
            await self.gimbal.async_set_position(
                GimbalPosition(*servo_corners[2]), time_ms=line_duration_ms, await_settle=False
            )
            await asyncio.sleep(line_duration_ms / 1000)
            await self.gimbal.async_set_position(
                GimbalPosition(*servo_corners[3]), time_ms=line_duration_ms, await_settle=False
            )
            await asyncio.sleep(line_duration_ms / 1000)
            await self.gimbal.async_set_position(
                GimbalPosition(*servo_corners[0]), time_ms=line_duration_ms, await_settle=False
            )
            await asyncio.sleep(line_duration_ms / 1000)

            # Turn Laser Off
            await self.laser.async_off()
        finally:
            await self.laser.async_off()

    # goto APIs

    def goto_servos(self, servos: Tuple[int, int], quiesce: bool = True, time_ms: int = 0) -> int:
        """
        Goto the given servos position. This is the primary goto function as the only way
        to actually go to a particular point in any of the scanner coordinate systems is
        to point the servos to a particular pan/tilt location.
        """
        assert self.gimbal
        assert servos is not None and len(servos) == 2, f"Expected 2-tuple for servos. Got: {servos}"

        self.gimbal.set_position(GimbalPosition(*servos), time_ms, await_settle=quiesce)

        return 0

    def goto_pan(self, pan: float, quiesce: bool = True) -> int:
        """
        Goto the given pan position. This is the primary pan goto function as the only way
        to actually go to a particular point in any of the scanner coordinate systems is
        to point the servos to a particular pan location.
        """
        assert self.gimbal
        self.gimbal.pan.set_position(int(pan), await_settle=quiesce)

        return 0

    def goto_tilt(self, tilt: float, quiesce: bool = True) -> int:
        """
        Goto the given tilt position. This is the primary tilt goto function as the only way
        to actually go to a particular point in any of the scanner coordinate systems is
        to point the servos to a particular tilt location.
        """
        assert self.gimbal
        self.gimbal.tilt.set_position(int(tilt), await_settle=quiesce)

        return 0

    def goto_servo_center(self) -> None:
        """Goto the middle point between servo min/max"""
        assert self.gimbal
        self.goto_servos(self.gimbal.center)

    def goto_pan_center(self) -> int:
        """Goto the middle point between pan min/max"""
        assert self.gimbal
        return self.goto_pan(self.gimbal.center[0])

    def goto_tilt_center(self) -> int:
        """Goto the middle point between tilt min/max"""
        assert self.gimbal
        return self.goto_tilt(self.gimbal.center[1])

    def goto_target_coords(
        self, tcoords: Tuple[float, float], abort_if_limited: bool = False, time_ms: int = 0, quiesce: bool = True
    ) -> int:
        """
        Goto the given target coordinates
        """
        assert self.gimbal is not None

        # The below statement may seem backwards, but it is because we are modeling a goto function.
        #
        # Imagine you click to the left of the target crosshair. If you imagine the old crosshair being fixed in the
        # image, clicking left means you want to move the old crosshair to the right.
        #
        # So if you think of the goto point as part of the fixed picture, you actually want it to go right when you
        # click to go left. With respect to the coordinate system, it is the goto point that is moving to the
        # crosshair, even though mechanically it is the crosshair moving to the goto point.
        #
        # e.g.:
        #   let x = crosshair
        #   let a,b = shapes that can be seen in the image (to see how the camera moves)
        #
        #   in before image:
        #     let g = goto click
        #   in after image:
        #     let y = old crosshair position
        #   ----------------------------      ----------------------------
        #   |         a           b    |      |              a          b|
        #   |            g    x        | ---> |                 x    y   |
        #   |                          |      |                          |
        #   ----------------------------      ----------------------------
        #
        # As you can see, we clicked left of the image (e.g. we requested our crosshair move by (-X, 0)),
        # but what actually happened is that the underlying image got shifted right by X
        #
        # For this reason, you subtract the requested coordinates from the current position in order to determine
        # the delta required.
        assert self.target is not None
        tdelta_required = subtract_tuples(self.target, tcoords)
        cur_servos = self.gimbal.get_position()

        # predict servo location corresponding to given coordinates
        new_servos = self.geometric_dtarget_to_servos_abs(tdelta_required, cur_servos)

        if abort_if_limited and new_servos != self.gimbal.clamp(
            GimbalPosition(cast(int, new_servos[0]), cast(int, new_servos[1]))
        ):
            return ERROR_SERVO_LIMITED

        # goto servo location
        ret = self.goto_servos((round(new_servos[0]), round(new_servos[1])), time_ms=time_ms, quiesce=quiesce)
        if ret != 0:
            LOG.error("Got error %d when going to servos %s", ret, new_servos)
            return ret

        return 0

    def _scale_coords(self, coords_scaled: Tuple[float, float], scale: Optional[float] = None) -> Tuple[int, int]:
        if scale is None:
            scale = 1
        x = round(coords_scaled[0] / scale)
        y = round(coords_scaled[1] / scale)

        return x, y

    def user_request_goto_target_coords(self, tcoords_scaled: Tuple[float, float], time_ms: int = 0) -> int:
        """
        Goto the given target coordinates requested by a human user
        """
        # translate coordinates from preview space (which may be scaled down a.k.a. fewer pixels)
        # to predict space (full camera coordinates - e.g. 1080p)
        tx, ty = self._scale_coords(tcoords_scaled)
        return self.goto_target_coords((tx, ty), time_ms=time_ms)

    def loop_pulse(self, duration_ms: float) -> None:
        while True:
            self.precise_pulse(duration_ms)
            time.sleep(duration_ms / 1000)

    def precise_pulse(self, duration_ms: float) -> None:
        assert self.laser is not None
        start_time = time.time()
        try:
            self.laser.on()
            time.sleep(max(0, duration_ms / 1000 - (time.time() - start_time)))
        finally:
            self.laser.off()
        LOG.info(
            "Scanner %s Precise Pulse Total Time: %s ms", self.numeric_id, round((time.time() - start_time) * 1000)
        )

    def compare_last_centroids(
        self, centroid: TrackedItemCentroid, opt_centroid: Optional[TrackedItemCentroid]
    ) -> bool:
        if (
            centroid.get_timestamp_ms() != self._last_centroid_ts
            or centroid.get_pcam_id() != self._last_centroid_cam
            or centroid.get_p2p_coord() != self._last_centroid_p2p_coords
        ):
            return False
        if opt_centroid is not None:
            if (
                opt_centroid.get_timestamp_ms() != self._alt_last_centroid_ts
                or opt_centroid.get_pcam_id() != self._alt_last_centroid_cam
                or opt_centroid.get_p2p_coord() != self._last_centroid_p2p_coords
            ):
                return False
        return True

    def set_last_centroids(self, centroid: TrackedItemCentroid, opt_centroid: Optional[TrackedItemCentroid]) -> None:
        self._last_centroid_cam = centroid.get_pcam_id()
        self._last_centroid_ts = centroid.get_timestamp_ms()
        self._last_centroid_p2p_coords = centroid.get_p2p_coord()
        if opt_centroid is not None:
            self._alt_last_centroid_cam = opt_centroid.get_pcam_id()
            self._alt_last_centroid_ts = opt_centroid.get_timestamp_ms()
            self._alt_last_centroid_p2p_coords = opt_centroid.get_p2p_coord()

    # Gimbal APIs
    def gimbal_position(self) -> GimbalPosition:
        return self.gimbal.get_position()

    def pan_position(self) -> int:
        return self.gimbal.pan.get_position()

    def tilt_position(self) -> int:
        return self.gimbal.tilt.get_position()

    @property
    def resolution(self) -> int:
        assert self.gimbal is not None
        return max(self.gimbal.pan.resolution, self.gimbal.tilt.resolution)

    @property
    def resolution_range(self) -> int:
        return max(self.gimbal.max[0] - self.gimbal.min[0], self.gimbal.max[1] - self.gimbal.min[1])

    @property
    def geometric_scanner(self) -> GeometricScanner:
        return self._geometric_scanner

    @property
    def id(self) -> str:
        return self._scanner_wrapper.identifier()

    @property
    def target_id(self) -> str:
        return f"target{self._numeric_id}"

    @property
    def db(self) -> CarbonRobotDBLite:
        return self._db

    def __str__(self) -> str:
        return self.id

    def __repr__(self) -> str:
        return self.__str__()

    @property
    def error(self) -> bool:
        return not self.gimbal.board.state_match

    @property
    def status(self) -> DeviceStatus:
        return self.gimbal.board.get_status_sync()

    @property
    def cfg(self) -> ConfigTree:
        assert self._cfg is not None
        return self._cfg

    @property
    def board(self) -> PulczarBoardDevice:
        return self.gimbal.board

    @property
    def power(self) -> float:
        return self._scanner_wrapper.power()

    @staticmethod
    def make_identifier(id: int) -> str:
        return ScannerWrapper.make_identifier(id)

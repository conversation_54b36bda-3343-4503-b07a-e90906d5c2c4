import functools
import threading
from typing import Any, Callable, Dict, List, Optional, Tuple, TypeVar, cast

import cv2
import numpy.typing as npt

import lib.common.annotate
import lib.common.logging
import lib.common.tasks
from core.controls.annotate import ROW_MODULE_LAYER
from core.controls.exterminator.calibration.level import CalibrationLevel
from core.controls.exterminator.model.scanner import <PERSON>anne<PERSON>
from core.cv.retina.camera.node import Cam
from core.cv.retina.retina import Retina
from core.cv.retina.space.predict_space import PredictSpace
from core.cv.visual_cortex.graph.output import WeedTrackingOutput
from core.cv.visual_cortex.graph.pathway_name import PathwayName
from core.cv.visual_cortex.graph.pathway_output import PathwayOutput
from core.cv.visual_cortex.visual_cortex import VisualCortex
from core.model.actuator import Actuator
from core.model.id import ReferenceId
from core.model.node.type import NodeType
from core.model.path import DevicePath
from cv.runtime.client import CVRuntimeClient
from cv.runtime.cpp import shmem_types_python
from cv.tracking.tracker import Tracked<PERSON>temC<PERSON>roid
from deeplearning.utils.use_cases import ModelUseCase
from lib.common.annotate import Annotation
from lib.common.image import CamImage
from lib.common.perf.perf_tracker import PerfCategory, add_perf_data_point
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.protocol.channel.callback import CallbackSubscriberChannel
from lib.common.tasks import MakaTask
from lib.common.time import maka_control_timestamp_ms
from lib.common.time.sleep import sleep_ms

LOG = lib.common.logging.get_logger(__name__)


T = TypeVar("T")


class CalibrationConfig:
    """namespace"""

    SCANNERS = "scanners"
    PREDICT_RESOLUTION = "predict_resolution"


class RowModule(Actuator):
    """
    The Exterminator object encapsulates a set of Scanners and Predict Cameras. It encompasses the system responsible for exterminating weeds.
    """

    SHOOT_EXPERIMENT = "experiment"
    SHOOT_P2P_STATIONARY = "p2p_stationary"
    SHOOT_P2P_STATIONARY_VISUAL_CORTEX = "p2p_stationary_visual_cortex"
    DO_NOTHING = "do_nothing"

    def __init__(self, *argv: Any, args: Any, calib_cfg: Dict[str, Any], **kwargs: Any):
        super().__init__(*argv, **kwargs)
        LOG.debug(f"{self.device_path} Initializing...")
        self._args = args
        self._numeric_id = int(self.id.split(":")[-1])
        self._debug_annotation_flag = False
        self._cam_predictions: Dict[DevicePath, List[TrackedItemCentroid]] = {}
        self._cam_predictions_lock: threading.Lock = threading.Lock()
        self._tpx_per_ms: float = 4
        self._target_shooting_planner_name = "point"
        self._extermination_strategy = ""
        self._predict_expression_name = ""
        self._last_weed_count: int = 0

        # Add annotations to Predict Cams
        for predict_cam in self._predict_space.list_cameras() if self._predict_space is not None else []:
            predict_cam.add_annotation(
                Annotation(
                    str(self.device_path),
                    layer=ROW_MODULE_LAYER + self._numeric_id,
                    f=functools.partial(self.annotate_predict, predict_cam.device_path),
                )
            )

        self._cv_runtime_client = CVRuntimeClient()

        self.config_load(calib_cfg)

        self._weed_tracking_subscriptions: Dict[ReferenceId, SubscriberChannel[WeedTrackingOutput]] = {}

        class WeedTrackingCaptureLambda:
            def __init__(self, name: PathwayName, func: Callable[[PathwayName], WeedTrackingOutput]):
                self.name = name
                self.func = func

            def __call__(self, *args: Any, **kwargs: Any) -> WeedTrackingOutput:
                return self.func(self.name)

        for predict_cam in self._predict_space.list_cameras() if self._predict_space is not None else []:
            if self._visual_cortex is not None:
                LOG.info(f"Visual Cortex established for predict cam {predict_cam.id}")
                visual_cortex = self._visual_cortex
                capture_func = WeedTrackingCaptureLambda(
                    PathwayName.from_single_source(source_id=predict_cam.id, output=PathwayOutput.WEED_TRACKING),
                    lambda x: cast(WeedTrackingOutput, visual_cortex.last_output(x)),
                )
                _weed_tracking_subscription = CallbackSubscriberChannel(
                    topic=self._visual_cortex.topic.sub(f"weed_tracking_{predict_cam.id}"), callback=capture_func
                )
                self._weed_tracking_subscriptions[predict_cam.id] = _weed_tracking_subscription

        LOG.debug(f"{self.device_path} Initialized")

    @property
    def last_weed_count(self) -> int:
        return self._last_weed_count

    def status_callback(self) -> Dict[str, Any]:
        return {"level": str(self.calib_level)}

    def _cache_named_references(self) -> None:
        self._scanners: Dict[DevicePath, Scanner] = {}
        self._predict_space: Optional[PredictSpace] = None

        self._visual_cortex: Optional[VisualCortex] = cast(
            Optional[VisualCortex], self.get_node(NodeType.VISUAL_CORTEX)
        )
        if self._visual_cortex is None:
            LOG.warning(f"{self.device_path} Initializing with no visual cortex!")

        for child_device_path in self.children():
            child_reference = self.subtree[child_device_path]
            assert (
                child_device_path.leaf().type == NodeType.SCANNER
            ), "This is a bug. Unexpected child device path: {}".format(child_device_path)
            self._scanners[child_device_path] = cast(Scanner, child_reference)

        self._retina: Optional[Retina] = cast(Optional[Retina], self.get_node(NodeType.RETINA))
        if self._retina is None:
            LOG.warning("{} Initializing with no retina!".format(self.device_path))
        else:
            instance = self.device_path.leaf().instance
            assert instance is not None
            index = int(instance)
            self._predict_space = self._retina.get_predict_space(index)
        LOG.info(f"Retina is: {self._retina}")

    def get_cameras_for_ui(self) -> List[str]:
        if self._predict_space is None:
            return []
        return [cam.id for cam in self._predict_space.list_cameras()]

    def get_predict_device_string(self, numeric_id: int) -> Optional[DevicePath]:
        # Predict id for now is just the nth index into the keys
        if self._retina is None:
            return None
        predict_paths = list(filter(lambda x: f"{numeric_id}" in str(x), self._retina.predict_cams.keys()))
        if len(predict_paths) == 1:
            return predict_paths[0]
        return None

    def get_predict_image(self, predict_id: int) -> Optional[npt.NDArray[Any]]:
        if self._retina is None:
            return None
        pcam = self._retina.get_predict_cam(predict_id)
        if pcam is None:
            return None
        image = pcam.capture()
        ret, val = cv2.imencode(".png", image)
        if ret:
            return cast(npt.NDArray[Any], val)
        return None

    def clear_masks(self) -> None:
        with self._cam_predictions_lock:
            self._cam_predictions = {}

    @property
    def scanners(self) -> List[Scanner]:
        # TODO Reconsider the APIs using this directly
        return list(self._scanners.values())

    # Calibration

    @property
    def calib_level(self) -> CalibrationLevel:
        return min([scanner.calib_level for scanner in self.scanners])

    def reset_level(self, level: CalibrationLevel) -> None:
        for scanner in self.scanners:
            CalibrationLevel.reset_level(scanner, level)

    def config_load(self, calib_cfg: Dict[str, Any]) -> None:
        if calib_cfg is not None and CalibrationConfig.SCANNERS in calib_cfg:
            for scanner, scanner_cfg in calib_cfg[CalibrationConfig.SCANNERS].items():
                if scanner in self._scanners:
                    self._scanners[scanner].config_load(scanner_cfg)

    def config_save(self, debug: bool = False) -> Dict[str, Any]:
        """
        Robot config_save callback.
        """
        config: Dict[str, Any] = {}
        config[CalibrationConfig.SCANNERS] = {}
        for scanner in self.scanners:
            config[CalibrationConfig.SCANNERS][scanner.device_path] = scanner.config_save(debug=debug)
        return config

    # Annotate
    def annotate_predict(self, kpcam: str, cam_image: CamImage) -> None:
        image = cam_image.image_bgr
        with self._cam_predictions_lock:
            for k in self._cam_predictions:
                if k != kpcam:
                    continue
                if k in self._cam_predictions:
                    predictions = self._cam_predictions[k]

                    for prediction in predictions:
                        cv2.circle(image, (int(prediction.x), int(prediction.y)), int(prediction.size), (0, 255, 0), 5)
                        cv2.circle(image, (int(prediction.x), int(prediction.y)), 5, (0, 255, 255), 5)

    # Prediction
    # TODO those predict methods should be split from Scanner and Exterminator, they don't really depend on self
    def _predict(
        self, cam: Cam, use_case: ModelUseCase, buffer_timeout_ms: int = 2000,
    ) -> Tuple[CamImage, Optional[shmem_types_python.DeepweedOutput]]:
        """
        Predict Masks in the given Cam
        :param cam:
        :param exprs:
        :return:
        """
        start_time = maka_control_timestamp_ms()
        deepweed_prediction = None

        perspective = cam.next()

        # Assure the masks we get are for the mask expressions we just set by comparing timestamps.
        grab_mask_begin = maka_control_timestamp_ms()
        while maka_control_timestamp_ms() - grab_mask_begin < buffer_timeout_ms:
            try:
                deepweed_prediction = self._cv_runtime_client.get_next_deepweed_prediction(
                    cam.id, buffer_timeout_ms, maka_control_timestamp_ms()
                )
            except Exception as e:
                LOG.error(e)
                continue

            break

        if deepweed_prediction is None:
            return perspective, None
        add_perf_data_point(PerfCategory.PREDICTION, "DeepWeed", "ms", maka_control_timestamp_ms() - start_time)
        return perspective, deepweed_prediction

    def predict(self) -> int:
        """Run CV prediction."""
        LOG.debug("{} Predicting...".format(self.device_path))
        cam_predictions: Dict[DevicePath, List[TrackedItemCentroid]] = {}
        ret = 0
        task_dict: Dict[DevicePath, MakaTask] = {}
        assert self._predict_space is not None
        for pcam in self._predict_space.list_cameras():
            task_dict[pcam.device_path] = lib.common.tasks.start(
                f"P{pcam.device_path}", functools.partial(self._predict, pcam, use_case=ModelUseCase.PREDICT)
            )
        for pcam in self._predict_space.list_cameras():
            perspective, predictions = task_dict[pcam.device_path].get_result()
            if predictions is None:
                LOG.error("Error Predicting")
                continue
            assert predictions is not None
            LOG.debug("Predictions for camera: %s", pcam.device_path)

            centroids = []
            for detection in predictions.detections:
                centroids.append(
                    TrackedItemCentroid(
                        pcam.id,
                        detection.x,
                        detection.y,
                        detection.size,
                        predictions.timestamp_ms,
                        has_perspective=True,
                    )
                )
            cam_predictions[pcam.device_path] = centroids
        with self._cam_predictions_lock:
            self._cam_predictions = cam_predictions
        return ret

    # Exterminate

    def _clear(self) -> None:
        pass

    def _do_nothing_fake_shooting_job(self, _: Dict[DevicePath, MakaTask], delay_ms: int = 0) -> None:
        assert delay_ms >= 0
        LOG.info(f"Do nothing for {delay_ms}ms!")
        if delay_ms > 0:
            sleep_ms(delay_ms)
        return

import abc
from typing import Any, Dict, Optional, Tuple

import lib.common.logging
from core.model.actuator import Actuator

LOG = lib.common.logging.get_logger(__name__)


MAX_RANGE_DEGREES = 22  # God says servos shall not move more than 22 degrees between min/max


# Servos are now RAII-ified. Implement all initialization logic in __init__ and provide
# the 3 abstract methods and this does the rest.
#
# Basically everything here should be considered slow.


class Servo(Actuator, abc.ABC):
    def __init__(
        self,
        *argv: Any,
        min: int,
        max: int,
        resolution: Optional[int] = None,
        max_range_degrees: int = MAX_RANGE_DEGREES,
        **kwargs: Any
    ):
        super().__init__(*argv, **kwargs)
        assert resolution is not None and resolution > 0
        self._resolution = resolution
        self._min, self._max = min, max
        self._is_tilt = False

        self._goal_position = 0
        # Check that limits are not too wide for safety (prevent breaking through mechanical stops in case of bug)
        servo_range = self._max - self._min
        max_ticks_delta = self._resolution * (max_range_degrees / 360)
        assert (
            servo_range <= max_ticks_delta
        ), "{} range = {} > {} is too large. This is likely a bug or user error.".format(
            self.id, servo_range, max_ticks_delta
        )

    def status_callback(self) -> Dict[str, Any]:
        return {"min": self._min, "max": self._max, "resolution": self.resolution, "goal_position": self._goal_position}

    @abc.abstractmethod
    def _get_position(self) -> int:
        """actual position direct from the servo in increments"""
        raise NotImplementedError("Not overridden")

    @abc.abstractmethod
    def _set_position(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        """sets goal position direct to servo in increments"""
        raise NotImplementedError("Not overridden")

    async def async_set_position(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        """sets goal position direct to servo in increments"""
        raise NotImplementedError("Not overridden")

    @abc.abstractmethod
    def _wait_position_complete(self) -> None:
        """Wait until the servo claims the goal has been reached within implementation-defined error margin."""
        raise NotImplementedError("Not overridden")

    @abc.abstractmethod
    def _compute_velocity_from_delta(self, position: int, time_ms: int) -> int:
        """Compute the velocity required to reach the position in the given time"""
        raise NotImplementedError("Not overridden")

    def _compute_velocity(self, position: int, time_ms: int) -> int:
        """Compute the velocity required to reach the position in the given time"""
        raise NotImplementedError("Not overridden")

    @abc.abstractmethod
    def _disable(self) -> None:
        raise NotImplementedError("Not overridden")

    @abc.abstractmethod
    def _enable(self) -> None:
        raise NotImplementedError("Not overridden")

    @property
    def goal_position(self) -> int:
        return self._goal_position

    @goal_position.setter
    def goal_position(self, position: int) -> None:
        self._goal_position = position

    @property
    def resolution(self) -> int:
        return self._resolution

    @property
    def min(self) -> int:
        return self._min

    @property
    def max(self) -> int:
        return self._max

    def limits(self) -> Tuple[int, int]:
        return (self._min, self._max)

    @property
    def center(self) -> int:
        return (self.min + self.max) // 2

    def within_limits(self, val: int) -> bool:
        return self.min <= val <= self.max

    def clamp(self, val: int) -> int:
        if val > self.max:
            return self.max
        if val < self.min:
            return self.min
        return val

    def get_position(self) -> int:
        return self._get_position()

    def set_position(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        clamped_position = self.clamp(position)
        self.set_position_unsafe(clamped_position, time_ms, await_settle)

    def set_position_unsafe(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        """
        Set position without checking limits - very unsafe, only for debugging
        """
        self._set_position(position, time_ms, await_settle)
        self._goal_position = position

    async def async_set_position_unsafe(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        """
        Set position without checking limits - very unsafe, only for debugging
        """
        await self.async_set_position(position, time_ms, await_settle)
        self._goal_position = position

    def compute_velocity_from_delta(self, position: int, time_ms: int) -> int:
        return self._compute_velocity_from_delta(position, time_ms)

    def compute_velocity(self, position: int, time_ms: int) -> int:
        return self._compute_velocity(position, time_ms)

    def set_is_tilt(self) -> None:
        self._is_tilt = True

    def wait_position_complete(self) -> None:
        self._wait_position_complete()

    def disable(self) -> None:
        self._disable()

    def enable(self) -> None:
        self._enable()

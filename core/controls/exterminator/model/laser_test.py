import time
from typing import Any, Generator, cast

import pytest

from core.controls.exterminator.model.laser import Laser
from core.controls.exterminator.model.laser_request import (
    BadLaserRequestError,
    LaserOffRequest,
    LaserOnRequest,
    LaserOnResponse,
)
from core.fs.filesystem import TemporaryFileSystem
from core.model.path import DevicePath
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.protocol.feed.inproc import InprocFeed


class FakeLaser(Laser):
    def __init__(self, *args: Any, **kwargs: Any):
        super().__init__(*args, feed=InprocFeed(), filesystem=TemporaryFileSystem(), **kwargs)
        self._on = False

    def arm(self) -> None:
        pass

    def firing(self) -> bool:
        return self._on

    def disarm(self) -> None:
        pass

    def off(self) -> None:
        super().off()
        self._on = False

    def on(self) -> None:
        super().on()
        self._on = True


@pytest.fixture
def obj() -> Generator[FakeLaser, None, None]:
    bot_stop_handler.exit_gracefully()  # make sure dog doesnt run as asyncio blocks shutdown
    yield FakeLaser(device_path=DevicePath.robot())


def test_off_to_off(obj: FakeLaser) -> None:
    req = LaserOffRequest()
    obj.handle_request(req)

    assert not obj._on
    assert obj._token.obj is None


def test_off_to_on(obj: FakeLaser) -> None:
    req = LaserOnRequest(source="pytest", duration=1000, token=None)
    res: LaserOnResponse = cast(LaserOnResponse, obj.handle_request(req))

    assert obj._on
    assert obj._token.obj == res.token


def test_off_to_on_to_off(obj: FakeLaser) -> None:
    req1 = LaserOnRequest(source="pytest", duration=1000, token=None)
    obj.handle_request(req1)

    req2 = LaserOffRequest()
    obj.handle_request(req2)

    assert not obj._on
    assert obj._token.obj is None


def test_consecutive_on_good_token(obj: FakeLaser) -> None:
    req1 = LaserOnRequest(source="pytest", duration=1000, token=None)
    res1: LaserOnResponse = cast(LaserOnResponse, obj.handle_request(req1))

    req2 = LaserOnRequest(source="pytest", duration=1000, token=res1.token)
    res2: LaserOnResponse = cast(LaserOnResponse, obj.handle_request(req2))

    assert obj._on
    assert obj._token.obj == res2.token

    time.sleep(1.05)

    assert not obj._on


def test_consecutive_on_bad_token(obj: FakeLaser) -> None:
    req1 = LaserOnRequest(source="pytest", duration=1000, token=None)
    obj.handle_request(req1)

    with pytest.raises(BadLaserRequestError):
        req2 = LaserOnRequest(source="pytest", duration=1000, token="wrong_token")
        obj.handle_request(req2)

    assert obj._on

    time.sleep(1.05)

    assert not obj._on


def test_timer_waits(obj: FakeLaser) -> None:
    req1 = LaserOnRequest(source="pytest", duration=1000, token=None)
    obj.handle_request(req1)

    time.sleep(0.95)

    assert obj._on


def test_timer_not_cumulative(obj: FakeLaser) -> None:
    req1 = LaserOnRequest(source="pytest", duration=1000, token=None)
    res1: LaserOnResponse = cast(LaserOnResponse, obj.handle_request(req1))

    req2 = LaserOnRequest(source="pytest", duration=1000, token=res1.token)
    obj.handle_request(req2)

    time.sleep(1.05)

    assert not obj._on

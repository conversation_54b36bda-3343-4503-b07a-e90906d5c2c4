from typing import Any, Dict, Optional, Union
from uuid import UUID

from lib.common.error import MakaException
from lib.common.serialization.json import JsonSerializable


class BadLaserRequestError(MakaException):
    def __init__(self, message: str):
        super().__init__()
        self._message: str = message

    @property
    def message(self) -> str:
        return self._message


class LaserRequest:
    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> Union["LaserOnRequest", "LaserOffRequest"]:
        if "on" not in data:
            raise BadLaserRequestError("Required property 'on' missing")

        on = data["on"]
        if on:
            return LaserOnRequest.from_json(data)

        return LaserOffRequest.from_json(data)


class LaserOffMessage:
    @staticmethod
    def to_json() -> Dict[str, Any]:
        return {
            "on": False,
        }


class LaserOffRequest(LaserOffMessage, JsonSerializable):
    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "LaserOffRequest":
        return LaserOffRequest()


class LaserOffResponse(LaserOffMessage, JsonSerializable):
    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "LaserOffResponse":
        return LaserOffResponse()


class LaserOnMessage:
    """
    Common data exchanged between client and server when coordinating user-initiated laser pulses.
    Should not be instantiated: use LaserRequest or LaserResponse subclasses
    """

    def __init__(
        self, duration: int = 1000, token: Optional[UUID] = None,
    ):
        assert 0 < duration <= 5000
        self._duration: int = duration

        self._token: Optional[UUID] = token

    @property
    def duration(self) -> int:
        """
        duration represents how long the laser will stay on in milliseconds. The maximum duration is
        5000ms, which strikes a reasonable balance between safety and usability.
        """
        return self._duration

    @property
    def token(self) -> Optional[UUID]:
        """
        token is a unique identifier that constraints laser usage.
        """
        return self._token


class LaserOnRequest(LaserOnMessage, JsonSerializable):
    def __init__(self, *, source: str, **kwargs: Any):
        super().__init__(**kwargs)
        self._source = source

    @property
    def source(self) -> str:
        """
        The source/origin of the laser request.
        """
        return self._source

    def to_json(self) -> Dict[str, Any]:
        return {
            "source": self._source,
        }

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "LaserOnRequest":
        token = data.get("token", None)
        if token is not None:
            token = UUID(token)
        return LaserOnRequest(duration=int(data["duration"]), token=token, source=str(data["source"]),)


class LaserOnResponse(LaserOnMessage, JsonSerializable):
    def to_json(self) -> Dict[str, Any]:
        return {
            "duration": self._duration,
            "token": str(self._token),
        }

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "LaserOnResponse":
        return LaserOnResponse(duration=int(data["duration"]), token=UUID(data.get("token", None)),)

import argparse
import os
import pickle
import time
from typing import Any, Dict, List, Optional, Tuple, Type, Union, cast

import cv2
import numpy as np
import numpy.typing as npt

import core.defaults as defs
import lib.common.annotate
import lib.common.concurrent
from config.client.cpp.config_client_python import ConfigTree, get_global_config_subscriber
from core.controls.annotate import SCANNER_LAYER
from core.controls.exterminator.calibration.errors import ERROR_CALIBRATE_BELOW_MIN_LEVEL
from core.controls.exterminator.calibration.level import CalibrationLevel
from core.controls.exterminator.model.gimbal import Gimbal2D
from core.controls.exterminator.model.laser import Laser
from core.controls.frame.frame import Frame
from core.coords.dimensions import RealWorldDimensions, ServoDimensions
from core.coords.hyperparameters import TrainHyperParameters
from core.coords.interpolation import (
    IndexedBivariateInterpolation,
    Interpolation,
    StratifiedBivariateSplinePairInterpolation,
)
from core.coords.samples import CalibrationSamples
from core.coords.strata import BivariateStrata
from core.cv.retina.camera.node import Cam
from core.cv.retina.retina import Retina
from core.cv.retina.space.predict_space import PredictSpace
from core.fiducials.scanner import ScannerFiducialsController
from core.model.actuator import Actuator
from core.model.id import ReferenceId
from core.model.node.type import NodeType
from core.model.path import Instance
from cv.runtime.client import CVRuntimeClient
from lib.common.annotate import Annotation, Annotator
from lib.common.config_file import ConfigFile
from lib.common.fiducials.aruco_markers import ArucoMarkers
from lib.common.fiducials.charuco_diamonds import CharucoDiamonds
from lib.common.fiducials.draw import find_draw_fiducials
from lib.common.geometric.cpp.geometric_python import GeometricCam, GeometricScanner
from lib.common.geometric.geometric_space import get_geometric_space
from lib.common.image import CamImage, ColorCorrectionProfile
from lib.common.logging import get_logger
from lib.common.math import add_tuples, multiply_tuple, multiply_tuples, subtract_tuples
from lib.common.types import GimbalPosition

LOG = get_logger(__name__)


ERROR_SERVO_LIMITED: int = 10002  # requested position is limited by servo limits


class CalibrationConfig:
    """namespace"""

    CROSSHAIR = "crosshair"
    PCAM_LENS_UNDISTORTION = "pcam_lens_undistortion"
    PREDICT_IMAGE_LIMITS = "predict_image_limits"
    SAMPLES = "samples"
    SERVO_DIMENSIONS = "servo_tdims"
    COLOR_CORRECTION = "color_correction"
    GOTO_PREDICT = "goto_predict"
    GOTO_TARGET = "goto_target"
    REAL_WORLD_DIMENSIONS = "real_world_dims"

    TARGET_RESOLUTION = "target_resolution"
    TRAIN_HYPERPARAMS = "train_hyperparams"
    STRATA = "strata"
    STRATA_MIN_SAMPLES = "strata_min_samples"

    @staticmethod
    def passthrough_or_pickle_load(obj: Union[object, bytes], expected_type: Type[Any]) -> Any:
        """
        Helper method to not pickle things that are already their expected type
        """
        if isinstance(obj, expected_type):
            return obj
        else:
            return pickle.loads(cast(bytes, obj))


class ColorCorrectionConfig:
    def __init__(self, profile: ColorCorrectionProfile, base_servo_coords: Tuple[int, int]):
        self.profile: ColorCorrectionProfile = profile
        self.base_servo_coords: Tuple[int, int] = base_servo_coords

    def to_json(self) -> Dict[str, Any]:
        return {"profile": self.profile.to_json(), "base_servo_coords": self.base_servo_coords}

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "ColorCorrectionConfig":
        return ColorCorrectionConfig(ColorCorrectionProfile.from_json(data["profile"]), data["base_servo_coords"])


KILL_TPX_PER_MS: float = 4000 / 1000


class Scanner(Actuator):
    """
    The scanner object encapsulates a laser, the servo motion that positions it, and
    the mapping between servo motion and camera coordinate systems.  There is one
    scanner per physical laser output.
    """

    NEXT_ID = 0

    def __init__(
        self,
        *argv: Any,
        args: Optional[argparse.Namespace] = None,
        calib_cfg: Optional[Dict[str, Any]] = None,
        gimbal_override: Optional[Gimbal2D] = None,
        laser_override: Optional[Laser] = None,
        **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)
        LOG.debug("Initializing {}...".format(self.device_path))
        # TODO Make These private
        self.gimbal: Gimbal2D
        self.laser: Laser

        if gimbal_override is not None:
            self.gimbal = gimbal_override
        if laser_override is not None:
            self.laser = laser_override

        self._args = args
        self._numeric_id: int = int(ReferenceId(self.device_path.leaf()).split(":")[-1])
        self.annotation_layer_id: int = int(self.id.split(":")[-1])
        self._safety_dilation = defs.DEFAULT_SAFETY_DILATION

        ######################################################################################################
        # Deeplearning properties
        ######################################################################################################
        self._cv_runtime_client = CVRuntimeClient()

        ######################################################################################################
        # Scanner CNN Argument Overrides
        ######################################################################################################
        if args is not None and args.scanner_safety_dilate is not None:
            for scanner_no, value in args.scanner_safety_dilate:
                if scanner_no == self._numeric_id:
                    self._safety_dilation = value
                    break

        ######################################################################################################
        # Attach calibration config properties
        ######################################################################################################
        self._target: Optional[Tuple[int, int]] = None
        self.calib_samples: Optional[CalibrationSamples] = None
        self.pcam_limits: Optional[List[Optional[List[Tuple[int, int]]]]] = None
        self.servo_tdims: Optional[ServoDimensions] = None
        self.real_world_dims: Optional[RealWorldDimensions] = None
        self.predict_to_servos: Optional[IndexedBivariateInterpolation] = None
        self.delta_target_to_delta_servos: Optional[StratifiedBivariateSplinePairInterpolation] = None
        self.color_correction_config: Optional[ColorCorrectionConfig] = None
        if calib_cfg is not None:
            self._deserialize_calibration_config(calib_cfg)

        self._load_cfg()

        ######################################################################################################
        # Initialize properties which depend on devices / calibration data
        ######################################################################################################
        self._fiducials_controller = ScannerFiducialsController(self, args=args)
        # computed based on other attributes
        self.calib_level = CalibrationLevel.NONE  # cached calibration level
        CalibrationLevel.set_level(self, logging=True)

        ######################################################################################################
        # Miscellaneous variables
        ######################################################################################################
        # 1. mask
        self.match_lasing: bool = False

        ######################################################################################################
        # Annotations
        ######################################################################################################
        self.calib_sample_strata = None
        if self.gimbal is not None and self._args is not None:
            self.calib_sample_strata = BivariateStrata.from_servo_limits(
                self.gimbal.min,
                self.gimbal.max,
                self._args.calibrate_num_strata_pan,
                self._args.calibrate_num_strata_tilt,
            )
            if self.calib_sample_strata.num_strata_x != defs.CALIBRATE_NUM_STRATA_PAN:
                LOG.info("Calibration override: num_strata_pan is %d", self.calib_sample_strata.num_strata_x)
            if self.calib_sample_strata.num_strata_y != defs.CALIBRATE_NUM_STRATA_TILT:
                LOG.info("Calibration override: num_strata_tilt is %d", self.calib_sample_strata.num_strata_y)
        self._calibration_predict_markers: Optional[ArucoMarkers] = None
        self._calibration_predict_diamonds: Optional[CharucoDiamonds] = None
        # Attach annotations
        self._annotator: ScannerAnnotator = ScannerAnnotator(scanner=self, args=args)
        self._annotator.attach()

        self._geo_scanner: Optional[GeometricScanner] = None
        ######################################################################################################
        # Done
        ######################################################################################################
        LOG.debug(f"Initialized {self.device_path}. Numeric ID: {self._numeric_id}")

    def _load_cfg(self) -> None:
        cfg = self._find_cfg()
        if cfg is None:
            return
        target_cfg = cfg.get_node("target")
        self._target = (target_cfg.get_node("x").get_int_value(), target_cfg.get_node("y").get_int_value())

    def _find_cfg(self) -> Optional[ConfigTree]:
        subscriber = get_global_config_subscriber()
        if not subscriber.started():
            return None
        for scanner_cfg in subscriber.get_config_node("aimbot", "scanners").get_children_nodes():
            num_id = self.id.split(":")[-1]
            if scanner_cfg.get_name() == f"scanner{num_id}":
                return scanner_cfg
        return None

    @classmethod
    def _next_id(cls) -> int:
        cls.NEXT_ID += 1
        return cls.NEXT_ID

    def _init_id(self) -> ReferenceId:
        # TODO we need proper configuration files for nodes
        return ReferenceId(f"{NodeType.SCANNER.serialize()}:{self._next_id()}")

    @property
    def annotator(self) -> "ScannerAnnotator":
        return self._annotator

    @property
    def fiducials_controller(self) -> ScannerFiducialsController:
        return self._fiducials_controller

    @property
    def numeric_id(self) -> int:
        return self._numeric_id

    @property
    def real_numeric_id(self) -> int:
        return int(self.id.split(":")[-1])

    def status_callback(self) -> Dict[str, Any]:
        return {"level": str(self.calib_level)}

    @property
    def cv_runtime_client(self) -> CVRuntimeClient:
        return self._cv_runtime_client

    @property
    def target_cam(self) -> Optional[Cam]:
        return self._target_cam

    def _cache_named_references(self) -> None:
        self._target_cam: Optional[Cam] = None
        for child_device_path in self.children():
            assert child_device_path.type in [
                NodeType.GIMBAL,
                NodeType.LASER,
            ], "Unexpected child device type: {}".format(child_device_path)
            child_reference = self.subtree[child_device_path]
            if child_device_path.type == NodeType.GIMBAL:
                self.gimbal = cast(Gimbal2D, child_reference)
            elif child_device_path.type == NodeType.LASER:
                self.laser = cast(Laser, child_reference)
            else:
                assert False, "This is a bug. Unexpected child device path: {}".format(child_device_path)

        # Frame
        self._frame: Optional[Frame] = cast(Optional[Frame], self.get_node(NodeType.FRAME))

        self._retina: Optional[Retina] = cast(Optional[Retina], self.get_node(NodeType.RETINA))

        self._predict_space: Optional[PredictSpace] = None
        if self._retina is not None:
            row_instance = self.device_path.trim_leaf().leaf().instance
            assert row_instance is not None
            row_index = int(row_instance)
            self._predict_space = self._retina.get_predict_space(row_index)

        index = int(self.id.split(Instance.prefix())[1])

        self._target_cam = self._retina.get_target_cam(index) if self._retina is not None else None
        if self.target_cam is None:
            LOG.warning("{} Initializing without target cam".format(self.device_path))

    def set_predict_space(self, predict_space: PredictSpace) -> None:
        self._predict_space = predict_space

    @property
    def predict_space(self) -> Optional[PredictSpace]:
        return self._predict_space

    def get_cameras_for_ui(self) -> List[str]:
        result = []
        if self._predict_space is not None:
            result.extend([x.id for x in self._predict_space.list_cameras()])
        if self.target_cam is not None:
            result.append(self.target_cam.id)
        return cast(List[str], result)

    # Config APIs

    def config_load(self, config: Dict[str, Any]) -> None:
        self._deserialize_calibration_config(config)
        CalibrationLevel.set_level(self, logging=True)
        self._load_cfg()

    def _deserialize_calibration_config(self, config: Dict[str, Any]) -> None:
        """
        Read in attributes from given scanner config.
        """
        assert config is not None
        LOG.debug("{} Deserializing calibration config...".format(self.device_path))

        ############################################
        # Crosshair
        ############################################
        if CalibrationConfig.CROSSHAIR in config:
            self.set_target(config[CalibrationConfig.CROSSHAIR])
            LOG.debug("{} Loaded target crosshair at {}".format(self.device_path, self.target))
        else:
            LOG.debug("{} Calibration config missing: {}".format(self.device_path, CalibrationConfig.CROSSHAIR))

        ############################################
        # Samples
        ############################################
        if CalibrationConfig.SAMPLES in config:
            self._deserialize_calibration_config_samples(config)
        else:
            LOG.debug("{} Calibration config missing: {}".format(self.device_path, CalibrationConfig.SAMPLES))

        ############################################
        # Image Limits
        ############################################
        if CalibrationConfig.PREDICT_IMAGE_LIMITS in config:
            self.pcam_limits = config[CalibrationConfig.PREDICT_IMAGE_LIMITS]
            LOG.debug("{} Loaded predict cam limits: {}".format(self.device_path, self.pcam_limits))
        else:
            LOG.debug(
                "{} Calibration config missing: {}".format(self.device_path, CalibrationConfig.PREDICT_IMAGE_LIMITS)
            )

        ############################################
        # Servo Dimensions
        ############################################
        if CalibrationConfig.SERVO_DIMENSIONS in config:
            self.servo_tdims = config[CalibrationConfig.SERVO_DIMENSIONS]
            assert self.servo_tdims
            LOG.debug(
                "{} Loaded pan one tick moves {:5.2f}x, {:5.2f}y = {:5.2f}xy in target space".format(
                    self.device_path, self.servo_tdims.pan.x, self.servo_tdims.pan.y, self.servo_tdims.pan.xy
                )
            )
            LOG.debug(
                "{} Loaded tilt one tick moves {:5.2f}x, {:5.2f}y = {:5.2f}xy in target space".format(
                    self.device_path, self.servo_tdims.tilt.x, self.servo_tdims.tilt.y, self.servo_tdims.tilt.xy
                )
            )
            LOG.debug(
                "{} Loaded estimated scanner z-height is {:.2f} target pixels".format(
                    self.device_path, self.servo_tdims.z
                )
            )
        else:
            LOG.debug("{} Calibration config missing: {}".format(self.device_path, CalibrationConfig.SERVO_DIMENSIONS))

        ############################################
        # Color correction
        ############################################
        if CalibrationConfig.COLOR_CORRECTION in config:
            if self.target_cam is None:
                LOG.warning(
                    "{} Skipping loading color correction profile because no target cam".format(self.device_path)
                )
            elif self.gimbal is None:
                LOG.warning("{} Skipping loading color correction profile because no gimbal".format(self.device_path))
            elif self.servo_tdims is None:
                LOG.warning(
                    "{} Skipping loading color correction profile because no servo dimensions".format(self.device_path)
                )
            else:
                self.color_correction_config = ColorCorrectionConfig.from_json(
                    config[CalibrationConfig.COLOR_CORRECTION]
                )
                # set color correction profile and optical target pixel angle
                self.target_cam.set_color_correction_profile(self.color_correction_config.profile)
                self.target_cam.set_pixel_angle_rad(
                    (
                        self._optical_angle_rad_per_servo_tick / self.servo_tdims.pan.xy,
                        self._optical_angle_rad_per_servo_tick / self.servo_tdims.tilt.xy,
                    )
                )
                LOG.debug(
                    "{} Loaded target cam color correction profile: {}, base servo coords: {}, pixel angle: {}".format(
                        self.device_path,
                        self.color_correction_config.profile,
                        self.color_correction_config.base_servo_coords,
                        self.target_cam.pixel_angle_rad,
                    )
                )
        else:
            LOG.debug("{} Calibration config missing: {}".format(self.device_path, CalibrationConfig.COLOR_CORRECTION))

        ############################################
        # P->S Interpolation
        ############################################
        if CalibrationConfig.GOTO_PREDICT in config:
            LOG.debug("{} Loading P->S interpolation...".format(self.device_path))
            self.predict_to_servos = CalibrationConfig.passthrough_or_pickle_load(
                config[CalibrationConfig.GOTO_PREDICT], Interpolation
            )
            LOG.debug("{} Loaded P->S interpolation.".format(self.device_path))
        else:
            LOG.debug("{} Calibration config missing: {}".format(self.device_path, CalibrationConfig.GOTO_PREDICT))

        ############################################
        # dT->dS Interpolation
        ############################################
        self._deserialize_calibration_config_goto_target(config)

        ############################################
        # Real World Dimensions
        ############################################
        if CalibrationConfig.REAL_WORLD_DIMENSIONS in config:
            self.real_world_dims = config[CalibrationConfig.REAL_WORLD_DIMENSIONS]
            if self.real_world_dims is not None:
                LOG.debug(
                    "{} Loaded estimated scanner z-height of {:.2f}mm = {:.2f}in".format(
                        self.device_path, self.real_world_dims.mm.z, self.real_world_dims.inches.z
                    )
                )
        else:
            LOG.debug(
                "{} Calibration config missing: {}".format(self.device_path, CalibrationConfig.REAL_WORLD_DIMENSIONS)
            )

        LOG.debug("{} Finished deserializing calibration config.".format(self.device_path))

    def _deserialize_calibration_config_samples(self, config: Dict[str, Any]) -> None:
        if os.path.exists(config[CalibrationConfig.SAMPLES]):
            samples_file = ConfigFile(*os.path.split(config[CalibrationConfig.SAMPLES]), backups=0)
            samples = samples_file.load()
            if CalibrationConfig.SAMPLES in samples:
                self.calib_samples = CalibrationConfig.passthrough_or_pickle_load(
                    samples[CalibrationConfig.SAMPLES], CalibrationSamples
                )
        if self.calib_samples is None:
            self.calib_samples = CalibrationSamples(pre_calibrated=True)
        assert self.calib_samples is not None
        LOG.debug("{} Loaded {} and {} samples".format(self.device_path, *self.calib_samples.lens()))

    def _deserialize_calibration_config_goto_target(self, config: Dict[str, Any]) -> None:
        """
        Deserialize GOTO TARGET
        """
        if CalibrationConfig.GOTO_TARGET in config:
            LOG.debug("{} Loading dT->dS interpolation...".format(self.device_path))
            self.delta_target_to_delta_servos = cast(
                StratifiedBivariateSplinePairInterpolation,
                CalibrationConfig.passthrough_or_pickle_load(config[CalibrationConfig.GOTO_TARGET], Interpolation),
            )

            assert self.delta_target_to_delta_servos
        else:
            LOG.debug("{} Calibration config missing: {}".format(self.device_path, CalibrationConfig.GOTO_TARGET))

    def config_save(self, debug: bool = False) -> Dict[str, Any]:
        """
        Robot config_save callback.
        """
        config: Dict[str, Any] = {
            CalibrationConfig.TARGET_RESOLUTION: self.target_cam.resolution if self.target_cam is not None else [0, 0]
        }
        if self.target is not None:
            config[CalibrationConfig.CROSSHAIR] = self.target

        if self.calib_samples is not None:
            relative_path = f"{str(self.device_path).replace(':', '_')}.json"
            os.makedirs(self.fs.abs_calibration_dir + os.path.dirname(relative_path), exist_ok=True)
            samples_file = ConfigFile(
                self.fs.abs_calibration_dir + os.path.dirname(relative_path),
                os.path.basename(relative_path),
                backups=0,
            )
            if not self.calib_samples.pre_calibrated:
                self.calib_samples.pre_calibrated = True
                samples = {CalibrationConfig.SAMPLES: pickle.dumps(self.calib_samples)}
                samples_file.save(samples)
            config[CalibrationConfig.SAMPLES] = samples_file.filepath

        if self.pcam_limits is not None:
            config[CalibrationConfig.PREDICT_IMAGE_LIMITS] = self.pcam_limits

        if self.servo_tdims is not None:
            config[CalibrationConfig.SERVO_DIMENSIONS] = self.servo_tdims

        if self.color_correction_config is not None:
            config[CalibrationConfig.COLOR_CORRECTION] = self.color_correction_config.to_json()

        if self.predict_to_servos is not None:
            config[CalibrationConfig.GOTO_PREDICT] = pickle.dumps(self.predict_to_servos)

        if self.delta_target_to_delta_servos is not None:
            config[CalibrationConfig.GOTO_TARGET] = pickle.dumps(self.delta_target_to_delta_servos)

        if self.real_world_dims is not None:
            config[CalibrationConfig.REAL_WORLD_DIMENSIONS] = self.real_world_dims

        if debug and self._args is not None:
            config[CalibrationConfig.TRAIN_HYPERPARAMS] = TrainHyperParameters.guess(self.resolution_range)
            assert self.gimbal is not None
            config[CalibrationConfig.STRATA] = BivariateStrata.from_servo_limits(
                self.gimbal.min,
                self.gimbal.max,
                self._args.target_interpolate_num_strata_pan,
                self._args.target_interpolate_num_strata_tilt,
            )
            config[CalibrationConfig.STRATA_MIN_SAMPLES] = self._args.target_interpolate_strata_min_samples

        return config

    # Position/Targeting APIs
    @property
    def target(self) -> Optional[Tuple[int, int]]:
        """
        Return the target position, in target space
        """
        return self._target

    def set_target(self, target: Tuple[float, float]) -> None:
        assert (
            target is not None
            and isinstance(target, tuple)
            and None not in target
            and len(target) == 2
            and min(target) >= 0
        ), "Bad target: {}".format(target)

        rounded_target = int(round(target[0])), int(round(target[1]))
        LOG.debug(f"{self.device_path} Setting target to {rounded_target} (previously: {self._target})")
        self._target = rounded_target

    def clear_target(self) -> None:
        self._target = None

    def settle_delay(self) -> None:
        # TODO include servo delay as well
        assert self.target_cam
        if self.target_cam.latency_ms > 0:
            time.sleep(self.target_cam.latency_ms / 1000)

    # Coordinate translation APIs

    def interpolate_predict_to_servos(self, pindex: int, pcoord: Tuple[float, float]) -> Optional[Tuple[float, float]]:
        """
        Look up a coordinate in prediction camera space and return the servo rotations at that location.
        """
        # TODO update client code to handle error codes from this function
        if not CalibrationLevel.min_level(self, CalibrationLevel.GOTO_PREDICT):
            return None
        assert self.predict_to_servos is not None
        if not self.predict_to_servos.is_index_calibrated(pindex):
            return None
        ret, servos = self.predict_to_servos.interpolate(pindex, pcoord)
        if ret != 0:
            LOG.error("Interpolation failed with error %d", ret)
            return None
        return servos

    def geometric_dtarget_to_servos_abs(
        self, delta_tcoord: Tuple[float, float], pos: Tuple[int, int]
    ) -> Tuple[float, float]:
        """Use the geometric scanner to calculate the required servo position"""
        scanner = self.geometric_scanner()
        delta_px = cast(Tuple[int, int], tuple(map(round, delta_tcoord)))
        servo_pos = scanner.get_servo_position_for_delta_px(delta_px, pos)
        return (float(servo_pos[0]), float(servo_pos[1]))

    def set_calibration_markers(
        self, predict_markers: Optional[ArucoMarkers] = None, predict_diamonds: Optional[CharucoDiamonds] = None
    ) -> None:
        self._calibration_predict_markers = predict_markers
        self._calibration_predict_diamonds = predict_diamonds

    def calibration_markers_complete(self) -> None:
        self._calibration_predict_markers = None
        self._calibration_predict_diamonds = None

    def servo_limit(self, servos: Tuple[int, int]) -> Tuple[int, int]:
        """
        Apply servo limits to the given servos
        """
        if not CalibrationLevel.min_level(self, CalibrationLevel.SERVO_LIMITS):
            return servos
        assert self.gimbal is not None
        return self.gimbal.clamp(GimbalPosition(*servos))

    @property
    def _optical_angle_rad_per_servo_tick(self) -> float:
        rads_per_servo_tick: float = 2 * np.pi / self.resolution
        return 2 * rads_per_servo_tick

    # goto APIs

    def _notify_target_cam_angle_change(self) -> None:
        if (
            self._target is None
            or self.target_cam is None
            or self.gimbal is None
            or self.color_correction_config is None
        ):
            return
        assert self.target_cam.pixel_angle_rad  # mypy

        # compute angle of gimbal goal w/r/t base servos
        angle_rad = multiply_tuple(
            subtract_tuples(self.gimbal.goal_position, self.color_correction_config.base_servo_coords),
            self._optical_angle_rad_per_servo_tick,
        )
        # convert crosshair (in tpx) to angle from top left corner (as if (0,0))
        target_offset = multiply_tuples(self._target, self.target_cam.pixel_angle_rad)
        # determine top left angle rad
        top_left_angle_rad = subtract_tuples(angle_rad, target_offset)
        self.target_cam.set_top_left_angle_rad(top_left_angle_rad)

    def goto_servos(self, servos: Tuple[int, int], quiesce: bool = True, time_ms: int = 0) -> int:
        """
        Goto the given servos position. This is the primary goto function as the only way
        to actually go to a particular point in any of the scanner coordinate systems is
        to point the servos to a particular pan/tilt location.
        """
        assert self.gimbal
        assert servos is not None and len(servos) == 2, f"Expected 2-tuple for servos. Got: {servos}"

        self.gimbal.set_position(GimbalPosition(*servos), time_ms, await_settle=quiesce)

        self._notify_target_cam_angle_change()

        return 0

    def goto_pan(self, pan: float, quiesce: bool = True) -> int:
        """
        Goto the given pan position. This is the primary pan goto function as the only way
        to actually go to a particular point in any of the scanner coordinate systems is
        to point the servos to a particular pan location.
        """
        assert self.gimbal
        self.gimbal.pan.set_position(int(pan), await_settle=quiesce)
        self._notify_target_cam_angle_change()

        return 0

    def goto_tilt(self, tilt: float, quiesce: bool = True) -> int:
        """
        Goto the given tilt position. This is the primary tilt goto function as the only way
        to actually go to a particular point in any of the scanner coordinate systems is
        to point the servos to a particular tilt location.
        """
        assert self.gimbal
        self.gimbal.tilt.set_position(int(tilt), await_settle=quiesce)
        self._notify_target_cam_angle_change()

        return 0

    def goto_servo_center(self) -> None:
        """Goto the middle point between servo min/max"""
        assert self.gimbal
        self.goto_servos(self.gimbal.center)

    def goto_pan_center(self) -> int:
        """Goto the middle point between pan min/max"""
        assert self.gimbal
        return self.goto_pan(self.gimbal.center[0])

    def goto_tilt_center(self) -> int:
        """Goto the middle point between tilt min/max"""
        assert self.gimbal
        return self.goto_tilt(self.gimbal.center[1])

    def goto_predict_coords(self, pindex: int, pcoords: Tuple[float, float]) -> int:
        """
        Goto the given predict coordinates by looking up the corresponding pan/tilt coordinates
        in the learned relationship between px/py and pan/tilt. This function requires we have
        calibrated functions (px, py) --> pan, and (px, py) --> tilt. This function comes
        from our splines.
        """
        if not CalibrationLevel.min_level(self, CalibrationLevel.GOTO_PREDICT):
            return ERROR_CALIBRATE_BELOW_MIN_LEVEL
        # predict servo location corresponding to given coordinates
        servos = self.interpolate_predict_to_servos(pindex, pcoords)
        assert servos
        # goto servo location
        return self.goto_servos((round(servos[0]), round(servos[1])))

    def goto_predict_geometric(self, pcam: Cam, pcoords: Tuple[float, float]) -> int:
        geometric_space = get_geometric_space()
        predict = geometric_space.get_device(GeometricCam, pcam.id)
        scanner = self.geometric_scanner()
        real_world_coords = predict.get_abs_position_from_px_from_height_estimate(pcoords, 874)
        servo_coords = scanner.get_servo_position_from_ground(real_world_coords)
        LOG.info(f"Real World: {pcam.id} -> {real_world_coords} from {pcoords} to {servo_coords}")
        return self.goto_servos((round(servo_coords[0]), round(servo_coords[1])))

    def user_request_goto_predict_coords(self, pcoords_index: int, pcoords_scaled: Tuple[float, float]) -> int:
        """
        Goto the given predict coordinates requested by a human user
        """
        # translate coordinates from preview space (which may be scaled down a.k.a. fewer pixels)
        # to predict space (full camera coordinates - e.g. 1080p)
        assert self._predict_space is not None
        pcam = self._predict_space.get_cam_by_index(pcoords_index)
        assert pcam is not None
        px, py = self._scale_coords(pcoords_scaled, pcam.preview_scale)
        self.goto_predict_geometric(pcam, (px, py))
        return 0

    def goto_target_coords(
        self, tcoords: Tuple[float, float], abort_if_limited: bool = False, time_ms: int = 0, quiesce: bool = True
    ) -> int:
        """
        Goto the given target coordinates
        """
        assert self.gimbal is not None

        # The below statement may seem backwards, but it is because we are modeling a goto function.
        #
        # Imagine you click to the left of the target crosshair. If you imagine the old crosshair being fixed in the
        # image, clicking left means you want to move the old crosshair to the right.
        #
        # So if you think of the goto point as part of the fixed picture, you actually want it to go right when you
        # click to go left. With respect to the coordinate system, it is the goto point that is moving to the
        # crosshair, even though mechanically it is the crosshair moving to the goto point.
        #
        # e.g.:
        #   let x = crosshair
        #   let a,b = shapes that can be seen in the image (to see how the camera moves)
        #
        #   in before image:
        #     let g = goto click
        #   in after image:
        #     let y = old crosshair position
        #   ----------------------------      ----------------------------
        #   |         a           b    |      |              a          b|
        #   |            g    x        | ---> |                 x    y   |
        #   |                          |      |                          |
        #   ----------------------------      ----------------------------
        #
        # As you can see, we clicked left of the image (e.g. we requested our crosshair move by (-X, 0)),
        # but what actually happened is that the underlying image got shifted right by X
        #
        # For this reason, you subtract the requested coordinates from the current position in order to determine
        # the delta required.
        try:
            self.gimbal.move((round(tcoords[0]), round(tcoords[1])))
            time.sleep(0.040)
            cur_servos = self.gimbal.get_position()
            self.gimbal.pan.goal_position = cur_servos.pan
            self.gimbal.tilt.goal_position = cur_servos.tilt
            return 0
        except NotImplementedError:
            LOG.error("Failed to Go To via Gimbal")
            pass

        assert self.target is not None
        tdelta_required = subtract_tuples(self.target, tcoords)
        cur_servos = self.gimbal.get_position()

        # predict servo location corresponding to given coordinates
        new_servos = self.geometric_dtarget_to_servos_abs(tdelta_required, cur_servos)

        if abort_if_limited and new_servos != self.gimbal.clamp(
            GimbalPosition(cast(int, new_servos[0]), cast(int, new_servos[1]))
        ):
            return ERROR_SERVO_LIMITED

        # goto servo location
        ret = self.goto_servos((round(new_servos[0]), round(new_servos[1])), time_ms=time_ms, quiesce=quiesce)
        if ret != 0:
            LOG.error("Got error %d when going to servos %s", ret, new_servos)
            return ret

        return 0

    def _scale_coords(self, coords_scaled: Tuple[float, float], scale: Optional[float] = None) -> Tuple[int, int]:
        if scale is None:
            assert self.target_cam is not None
            target_cam = self.target_cam
            scale = target_cam.preview_scale
        x = round(coords_scaled[0] / scale)
        y = round(coords_scaled[1] / scale)

        return x, y

    def user_request_goto_target_coords(self, tcoords_scaled: Tuple[float, float], time_ms: int = 0) -> int:
        """
        Goto the given target coordinates requested by a human user
        """
        # translate coordinates from preview space (which may be scaled down a.k.a. fewer pixels)
        # to predict space (full camera coordinates - e.g. 1080p)
        tx, ty = self._scale_coords(tcoords_scaled)
        return self.goto_target_coords((tx, ty), time_ms=time_ms)

    def loop_pulse(self, duration_ms: float) -> None:
        while True:
            self.precise_pulse(duration_ms * 0.8)
            time.sleep(duration_ms / 1000 * 0.2)

    def precise_pulse(self, duration_ms: float) -> None:
        assert self.laser is not None
        assert self.target_cam is not None
        start_time = time.time()
        try:
            self.laser.on()
            time.sleep(max(0, duration_ms / 1000 - (time.time() - start_time)))
        finally:
            self.laser.off()
        LOG.info(
            "Scanner %s Precise Pulse Total Time: %s ms", self.numeric_id, round((time.time() - start_time) * 1000)
        )

    # Gimbal APIs
    def gimbal_position(self) -> GimbalPosition:
        return self.gimbal.get_position()

    def pan_position(self) -> int:
        return self.gimbal.pan.get_position()

    def tilt_position(self) -> int:
        return self.gimbal.tilt.get_position()

    @property
    def resolution(self) -> int:
        assert self.gimbal is not None
        return max(self.gimbal.pan.resolution, self.gimbal.tilt.resolution)

    @property
    def resolution_range(self) -> int:
        return max(self.gimbal.max[0] - self.gimbal.min[0], self.gimbal.max[1] - self.gimbal.min[1])

    # Remote mask manipulation
    @property
    def target_image(self) -> Optional[npt.NDArray[Any]]:
        assert self.target_cam is not None
        image = self.target_cam.capture()
        ret, val = cv2.imencode(".png", image)
        if not ret:
            return None
        return cast(npt.NDArray[Any], val)

    def geometric_scanner(self) -> GeometricScanner:
        if self._geo_scanner is None:
            self._geo_scanner = get_geometric_space().get_device(GeometricScanner, f"scanner{self.real_numeric_id}")
            centers = (
                int(round((self.gimbal.max[0] + self.gimbal.min[0]) / 2)),
                int(round((self.gimbal.max[1] - self.gimbal.min[1]) / 2)),
            )
            self._geo_scanner.set_servo_centers(centers)
        return self._geo_scanner


CAMERA_TARGET_CROSSHAIR_RADIUS = 5


class ScannerAnnotator(Annotator):
    """
    Logic container for annotating images based on scanner state
    """

    def __init__(self, *, scanner: Scanner, args: Optional[argparse.Namespace]):
        self._scanner: Scanner = scanner
        self._args = args

        self._annotation_name_predict = "{}/predict".format(self._scanner.id)
        self._annotation_name_predict_fiducials = "{}/fiducials".format(self._annotation_name_predict)
        self._annotation_name_predict_boxes = "{}/boxes".format(self._annotation_name_predict)
        self._annotation_name_predict_pcam_limits = "{}/pcam_limits".format(self._annotation_name_predict)
        self._annotation_name_predict_samples = "{}/samples".format(self._annotation_name_predict)

        self._annotation_name_target = "{}/target".format(self._scanner.annotation_layer_id)
        self._annotation_name_target_fiducials = "{}/fiducials".format(self._annotation_name_target)
        self._annotation_name_aimbot = "{}/aimbot".format(self._annotation_name_target)
        self._annotation_name_target_mask = "{}/mask".format(self._annotation_name_target)
        self._annotation_name_target_mask_override = "{}/mask_override".format(self._annotation_name_target)
        self._annotation_name_target_servo_limits = "{}/servo_limits".format(self._annotation_name_target)
        self._annotation_name_target_crosshair = "{}/crosshair".format(self._annotation_name_target)
        self._annotation_name_target_active_plan = "{}/active_plan".format(self._annotation_name_target)
        self._annotation_name_target_grid = "{}/target_grid".format(self._annotation_name_target)

    @property
    def scanner(self) -> Scanner:
        return self._scanner

    def attach(self) -> None:
        """
        Attach annotation functions to the cameras for this scanner.

        TODO this is separated out so that later we can model this behavior better with objects.
        We also want to override this in the simulator, so that we can unwind the simulated cameras
        from needing references to the gimbal and laser during init. We can lazily attach that annotation here,
        under the context of a Scanner which puts the objects together.
        """
        # level schema supports 100 scanners with 100 different annotation functions
        layer = SCANNER_LAYER + 100 * self._scanner.annotation_layer_id
        if self._scanner.predict_space is not None and self._args is not None:
            for pcam in self._scanner.predict_space.list_cameras():
                pcam.add_annotation(
                    Annotation(
                        name=self._annotation_name_predict_fiducials,
                        layer=-layer,  # negative so this happens first (this can be modeled better by splitting fiducial compute from fiducial draw
                        f=self.annotate_predict_fiducials,
                        enabled=self._args.fiducials,
                    )
                )
                pcam.add_annotation(
                    Annotation(
                        name=self._annotation_name_predict_samples,
                        layer=layer + 3,
                        f=self.annotate_samples,
                        enabled=self._args.show_samples,
                    )
                )

        if self._scanner.target_cam is not None and self._args is not None:
            self._scanner.target_cam.add_annotation(
                Annotation(
                    name=self._annotation_name_target_fiducials,
                    f=self.annotate_target_fiducials,
                    layer=-layer,
                    enabled=self._args.fiducials,
                )
            )
            self._scanner.target_cam.add_annotation(
                Annotation(name=self._annotation_name_aimbot, f=self.annotate_aimbot, layer=layer + 1,)
            )
            self._scanner.target_cam.add_annotation(
                Annotation(
                    name=self._annotation_name_target_servo_limits,
                    f=self.annotate_target_servo_limits,
                    layer=layer + 4,
                )
            )
            self._scanner.target_cam.add_annotation(
                Annotation(name=self._annotation_name_target_crosshair, f=self.annotate_crosshair, layer=layer + 5)
            )
            self._scanner.target_cam.add_annotation(
                Annotation(name=self._annotation_name_target_grid, f=self.annotate_grid, layer=layer + 7, enabled=False)
            )

    def enable_fiducials_annotations(self) -> None:
        if self._scanner.target_cam is not None:
            self._scanner.target_cam.enable_annotation(self._annotation_name_target_fiducials)

        if self._scanner.predict_space is not None:
            for pcam in self._scanner.predict_space.list_cameras():
                pcam.enable_annotation(self._annotation_name_predict_fiducials)

    def enable_predict_sample_annotation(self) -> None:
        if self._scanner.predict_space is not None:
            for pcam in self._scanner.predict_space.list_cameras():
                pcam.enable_annotation(self._annotation_name_predict_samples)

    def enable_target_mask(self) -> None:
        if self._scanner.target_cam is not None:
            self._scanner.target_cam.enable_annotation(self._annotation_name_target_mask)

    def disable_target_mask(self) -> None:
        if self._scanner.target_cam is not None:
            self._scanner.target_cam.disable_annotation(self._annotation_name_target_mask)

    def annotate_grid(self, cam_image: CamImage) -> CamImage:
        image = cam_image.image_bgr

        def _grid(res: Tuple[int, int], grid_size: int, thickness: int) -> None:
            for i in range(1, grid_size):
                x = int((res[0] / grid_size) * i)
                y = int((res[1] / grid_size) * i)
                cv2.line(image, (0, y), (res[0] - 1, y), color=lib.common.annotate.BGR_RED, thickness=thickness)
                cv2.line(image, (x, 0), (x, res[1] - 1), color=lib.common.annotate.BGR_RED, thickness=thickness)

        if self.scanner.target_cam is None:
            raise RuntimeError("Cannot annotate grid. Missing target camera.")
        res = cast(Tuple[int, int], cast(Cam, self._scanner.target_cam).resolution)
        _grid(res, 2, 3)
        _grid(res, 40, 1)

        return cam_image

    def annotate_crosshair(self, cam_image: CamImage) -> CamImage:
        image = cam_image.image_bgr
        # apply crosshair
        if not CalibrationLevel.min_level(self._scanner, CalibrationLevel.TARGET_CROSSHAIR, logging=False):
            return cam_image
        tx_ty = self._scanner.target
        if tx_ty is not None:
            x, y = tx_ty
            # Draw an approximation of the Halo 3 Battle Rifle reticle:
            # https://www.halopedia.org/images/d/d4/Halo-2-20070411043848392.jpg
            # because its the best game ever
            #
            #          |
            #      /‾‾‾|‾‾‾\
            #     |         |
            #   -----     -----
            #     |         |
            #      \___|___/
            #          |
            #
            crosshair_bottom = (x, y - defs.CAMERA_TARGET_CROSSHAIR_RADIUS)
            crosshair_middle_bottom = (x, y - int(0.3 * defs.CAMERA_TARGET_CROSSHAIR_RADIUS))
            crosshair_top = (x, y + defs.CAMERA_TARGET_CROSSHAIR_RADIUS)
            crosshair_middle_top = (x, y + int(0.3 * defs.CAMERA_TARGET_CROSSHAIR_RADIUS))
            crosshair_left = (x - defs.CAMERA_TARGET_CROSSHAIR_RADIUS, y)
            crosshair_middle_left = (x - int(0.3 * defs.CAMERA_TARGET_CROSSHAIR_RADIUS), y)
            crosshair_right = (x + defs.CAMERA_TARGET_CROSSHAIR_RADIUS, y)
            crosshair_middle_right = (x + int(0.3 * defs.CAMERA_TARGET_CROSSHAIR_RADIUS), y)
            outer_thickness = 6
            inner_thickness = 2

            # draw a circle - looks nicest when scaled by .75
            lib.common.annotate.circle(
                image,
                (x, y),
                0.75 * defs.CAMERA_TARGET_CROSSHAIR_RADIUS,
                lib.common.annotate.BGR_PINK,
                thickness=outer_thickness,
            )
            lib.common.annotate.circle(
                image,
                (x, y),
                0.75 * defs.CAMERA_TARGET_CROSSHAIR_RADIUS,
                lib.common.annotate.BGR_YELLOW,
                thickness=inner_thickness,
            )
            # pink crosshair outer
            cv2.line(
                image,
                crosshair_bottom,
                crosshair_middle_bottom,
                lib.common.annotate.BGR_PINK,
                thickness=outer_thickness,
            )
            cv2.line(
                image, crosshair_middle_top, crosshair_top, lib.common.annotate.BGR_PINK, thickness=outer_thickness
            )
            cv2.line(
                image, crosshair_left, crosshair_middle_left, lib.common.annotate.BGR_PINK, thickness=outer_thickness
            )
            cv2.line(
                image, crosshair_middle_right, crosshair_right, lib.common.annotate.BGR_PINK, thickness=outer_thickness
            )
            # yellow crosshair inner
            cv2.line(
                image,
                crosshair_bottom,
                crosshair_middle_bottom,
                lib.common.annotate.BGR_YELLOW,
                thickness=inner_thickness,
            )
            cv2.line(
                image, crosshair_middle_top, crosshair_top, lib.common.annotate.BGR_YELLOW, thickness=inner_thickness
            )
            cv2.line(
                image, crosshair_left, crosshair_middle_left, lib.common.annotate.BGR_YELLOW, thickness=inner_thickness
            )
            cv2.line(
                image,
                crosshair_middle_right,
                crosshair_right,
                lib.common.annotate.BGR_YELLOW,
                thickness=inner_thickness,
            )
        return cam_image

    def annotate_target_fiducials(self, cam_image: CamImage) -> CamImage:
        image = cam_image.image_bgr
        # apply fiducials first otherwise the other annotations will get in the way of finding the fiducials
        find_draw_fiducials(self._scanner.fiducials_controller.fiducials, image, ignore_small=True)
        return cam_image

    def annotate_aimbot(self, cam_image: CamImage) -> CamImage:
        image = cam_image.image_bgr
        if self._scanner.match_lasing:
            cv2.rectangle(image, (0, 0), (image.shape[1], image.shape[0]), (0, 0, 255), 20)

        return cam_image

    def annotate_target_servo_limits(self, cam_image: CamImage) -> CamImage:
        image = cam_image.image_bgr
        if (
            self._scanner.target_cam is not None
            and self._scanner.target is not None
            and self._scanner.gimbal is not None
            and self._scanner.servo_tdims is not None
        ):
            pan, tilt = self._scanner.gimbal.goal_position
            pan_min, pan_max = self._scanner.gimbal.pan_limits
            tilt_min, tilt_max = self._scanner.gimbal.tilt_limits

            pan_min_delta = pan - pan_min
            pan_max_delta = pan - pan_max
            tilt_min_delta = tilt - tilt_min
            tilt_max_delta = tilt - tilt_max
            # this has a sign flip from what I expected: probably should come back and understand that later
            tx_ty_delta_to_pan_min = (
                pan_min_delta * self._scanner.servo_tdims.pan.x,
                pan_min_delta * self._scanner.servo_tdims.pan.y,
            )  # noqa
            tx_ty_delta_to_pan_max = (
                pan_max_delta * self._scanner.servo_tdims.pan.x,
                pan_max_delta * self._scanner.servo_tdims.pan.y,
            )  # noqa
            tx_ty_delta_to_tilt_min = (
                tilt_min_delta * self._scanner.servo_tdims.tilt.x,
                tilt_min_delta * self._scanner.servo_tdims.tilt.y,
            )  # noqa
            tx_ty_delta_to_tilt_max = (
                tilt_max_delta * self._scanner.servo_tdims.tilt.x,
                tilt_max_delta * self._scanner.servo_tdims.tilt.y,
            )  # noqa

            pmin_tmin_delta = add_tuples(tx_ty_delta_to_pan_min, tx_ty_delta_to_tilt_min)
            pmin_tmax_delta = add_tuples(tx_ty_delta_to_pan_min, tx_ty_delta_to_tilt_max)
            pmax_tmax_delta = add_tuples(tx_ty_delta_to_pan_max, tx_ty_delta_to_tilt_max)
            pmax_tmin_delta = add_tuples(tx_ty_delta_to_pan_max, tx_ty_delta_to_tilt_min)

            crosshair = self._scanner.target
            crosshair_at_pmin_tmin = add_tuples(crosshair, pmin_tmin_delta)
            crosshair_at_pmin_tmax = add_tuples(crosshair, pmin_tmax_delta)
            crosshair_at_pmax_tmax = add_tuples(crosshair, pmax_tmax_delta)
            crosshair_at_pmax_tmin = add_tuples(crosshair, pmax_tmin_delta)

            pts = np.array(
                [crosshair_at_pmin_tmin, crosshair_at_pmin_tmax, crosshair_at_pmax_tmax, crosshair_at_pmax_tmin]
            )
            pts = np.array(pts, np.int32).reshape((-1, 1, 2))

            cv2.polylines(image, [pts], True, lib.common.annotate.BGR_YELLOW, thickness=3)

        return cam_image

    def get_target_resolution(self) -> Tuple[int, int]:
        assert self._scanner.target_cam
        assert self._scanner.target_cam.resolution
        return (self._scanner.target_cam.resolution[0] or 0, self._scanner.target_cam.resolution[1] or 0)

    def annotate_predict_fiducials(self, cam_image: CamImage) -> CamImage:
        image = cam_image.image_bgr

        # TODO this function is a bit sloppy to work with and relies on
        # a bunch of tightly coupled yet optional parameters

        def last_target_frame_callback() -> Optional[npt.NDArray[Any]]:
            if self._scanner.target_cam is not None:
                last_cam_image = self._scanner.target_cam.last()
                if last_cam_image is not None:
                    return last_cam_image.image_bgr
            return None

        find_draw_fiducials(
            self._scanner.fiducials_controller.fiducials,
            image,
            target_callback=lambda: self._scanner.target,
            last_target_frame_callback=last_target_frame_callback,
            existing_markers=self._scanner._calibration_predict_markers,
            existing_diamonds=self._scanner._calibration_predict_diamonds,
            ignore_small=False,
        )

        return cam_image

    def annotate_samples(self, cam_image: CamImage) -> CamImage:
        image = cam_image.image_bgr
        # annotate samples
        if self._scanner.calib_samples is not None and self._scanner.predict_space is not None:
            for sample in self._scanner.calib_samples.predict_to_servos:  # empty if not set
                pcam = self._scanner.predict_space.get_cam_by_index(sample.predict_index)
                if pcam is None or cam_image.camera_id != pcam.id:
                    continue
                # default to red (will get overwritten if we can determine strata
                color = lib.common.annotate.BGR_RED
                assert self._scanner.gimbal

                # alternate yellow/cyan per stratum
                if self._scanner.calib_sample_strata is not None and self._scanner.gimbal.within_limits(
                    GimbalPosition(*sample.servos)
                ):
                    stratum_id = self._scanner.calib_sample_strata.compute_bounding_stratum(sample.servos)
                    if stratum_id is not None:
                        is_even = sum([int(val) for val in stratum_id.split(",")]) % 2 == 0
                        if is_even:
                            color = lib.common.annotate.BGR_YELLOW
                        else:
                            color = lib.common.annotate.BGR_CYAN
                lib.common.annotate.circle(image, sample.pcoords, 6, color, -1)

        return cam_image

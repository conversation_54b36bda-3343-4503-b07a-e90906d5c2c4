import abc
import asyncio
import uuid
from threading import Lock, Timer
from typing import Any, Dict, Optional, Union

from core.controls.exterminator.model.laser_request import (
    BadLaserRequestError,
    LaserOffRequest,
    LaserOffResponse,
    LaserOnRequest,
    LaserOnResponse,
)
from core.model.actuator import Actuator
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.concurrent import LockedObject
from lib.common.devices.device import <PERSON><PERSON><PERSON>tatus<PERSON><PERSON>
from lib.common.logging import get_logger
from lib.common.retry import retry
from lib.common.tasks.manager import get_event_loop_by_name

LOG = get_logger(__name__)

LASER_BEAM_DIAMETER_TPX = 15


class Laser(Actuator, abc.ABC):
    def __init__(self, *argv: Any, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        self._token: LockedObject[uuid.UUID] = LockedObject()
        self._token_expirer: Optional[Timer] = None
        self._user_lock: Lock = Lock()
        self._event_loop = get_event_loop_by_name()
        asyncio.run_coroutine_threadsafe(self.__start_dog(), self._event_loop).result()

        # For use by subclasses
        self._armed: bool = False

    async def __start_dog(self) -> None:
        self._safe_fire_queue: asyncio.Queue[bool] = asyncio.Queue()
        self._safe_fire_task = self._event_loop.create_task(self._external_dog_task())

    def status_callback(self) -> Dict[str, Any]:
        return {"enabled": self._armed}

    def beam_radius(self) -> float:
        # TODO Make this abstract and implement it in the drivers with args coming from hardware.json
        return LASER_BEAM_DIAMETER_TPX / 2

    @property
    def armed(self) -> bool:
        # read-only property. No setter.
        # Clients must call arm/disarm to flip state.
        return self._armed

    def firing(self) -> bool:
        return False

    async def enable_async(self, enabled: bool) -> None:
        pass

    def enable(self, enabled: bool) -> None:
        pass

    @abc.abstractmethod
    def arm(self) -> None:
        self._armed = True

    async def async_arm(self) -> None:
        raise NotImplementedError("Base Laser Not Async")

    async def is_armed(self) -> bool:
        raise NotImplementedError("Base Laser Not Async")

    @abc.abstractmethod
    def disarm(self) -> None:
        self._armed = False
        LOG.info("{} Disarmed laser".format(self.id))

    async def async_disarm(self) -> None:
        raise NotImplementedError("Base Laser Not Async")

    @abc.abstractmethod
    def on(self) -> None:
        # TODO clients should actually use the time limit. We should express a way at this base Laser class to ensure
        # this behavior is implemented and enforced by all subclasses

        # One idea is for this function to return a token string to the client which they then pass into off().
        # off() registers than an off() call was made against that token. If an off() call has already been made,
        # then do nothing (even if the laser is on)
        # In the original on() call, we spawn a thread that waits for the duration and then wakes up and calls off()
        # with that token.
        # This creates a fairly idempotent transaction-type model of triggering the laser. Clients can still turn it off
        # early, and the laser will shut itself off at the duration.
        # We will have to add locking.

        # In particular to repeat, I would like to do this at the base class level, with subclass just overriding
        # the single-threaded on/off
        pass

    async def async_on(self) -> None:
        await asyncio.get_event_loop().run_in_executor(None, lambda: self.on())

    @retry(tries=5)
    def try_off(self) -> None:
        # This is to better handle cypress communication errors
        # Where turning the laser off fails
        self.off()

    @abc.abstractmethod
    def off(self) -> None:
        pass

    def error(self) -> bool:
        return False

    def error_code(self) -> DeviceStatusCode:
        return DeviceStatusCode.OK

    def error_message(self) -> str:
        return ""

    async def async_off(self) -> bool:
        await asyncio.get_event_loop().run_in_executor(None, lambda: self.off())
        return True

    async def _ext_dog_func(self) -> None:
        current = False
        while not bot_stop_handler.stopped:
            try:
                if current:
                    requested = await asyncio.wait_for(self._safe_fire_queue.get(), timeout=0.5)
                else:
                    requested = await self._safe_fire_queue.get()
                if current != requested:
                    try:
                        if requested:
                            current = requested
                            await self.async_on()
                        else:
                            await self.async_off()
                            current = requested
                    except Exception as e:
                        LOG.warning(f"Safe fire state change failure: {e}")
            except asyncio.CancelledError:
                raise
            except asyncio.TimeoutError:
                if current:
                    LOG.info("Failed to get pet, stopping laser.")
                    try:
                        await self.async_off()
                        current = False
                    except Exception as e:
                        LOG.warning(f"Safe fire state change failure: {e}")
            except Exception as e:
                LOG.warning(f"Unknonw error in external dog task: {e}")

    async def _external_dog_task(self) -> None:
        try:
            await self._ext_dog_func()
        except asyncio.CancelledError:
            pass
        finally:
            await self.async_off()

    async def on_with_safety(self) -> None:
        self._safe_fire_queue.put_nowait(True)

    async def off_with_safety(self) -> None:
        if self._safe_fire_queue is not None:
            self._safe_fire_queue.put_nowait(False)

    def stop(self) -> None:
        """Common Laser stop operations"""
        super().stop()
        self.off()
        self.disarm()

    def handle_request(self, req: Union[LaserOffRequest, LaserOnRequest]) -> Union[LaserOffResponse, LaserOnResponse]:
        if isinstance(req, LaserOffRequest):
            # Laser off requests are always honored
            self._user_off()
            return LaserOffResponse()

        if isinstance(req, LaserOnRequest):
            return self._user_on(req)

        raise BadLaserRequestError("Unknown request type.")

    def _user_on(self, req: LaserOnRequest) -> LaserOnResponse:
        with self._user_lock:
            if req.token != self._token.obj:
                raise BadLaserRequestError("Tokens do not match")

            # Tokens match, generate new token and continue firing
            self._clear_token_expirer()
            self._token.set(uuid.uuid4())
            self._token_expirer = Timer(req.duration / 1000, self._user_off)
            self._token_expirer.start()
            self.on()
            return LaserOnResponse(duration=req.duration, token=self._token.obj)

    def _user_off(self) -> None:
        with self._user_lock:
            self.off()
            self._token.pop()
            self._clear_token_expirer()

    def _clear_token_expirer(self) -> None:
        if self._token_expirer:
            self._token_expirer.cancel()
            self._token_expirer = None

    @property
    def powered(self) -> bool:
        return False

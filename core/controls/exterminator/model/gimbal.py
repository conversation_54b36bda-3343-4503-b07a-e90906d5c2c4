import asyncio
from typing import Any, Callable, Dict, List, Tuple, cast

import numpy as np

import lib.common.logging
from core.controls.exterminator.model.servo import Servo
from core.model.actuator import Actuator
from core.model.id import ReferenceId
from core.model.node.type import NodeType
from core.model.path import ServoInstance
from lib.common.error import MakaException
from lib.common.types import GimbalPosition

LOG = lib.common.logging.get_logger(__name__)


class GimbalException(MakaException):
    pass


class GimbalOutOfBoundsException(GimbalException):
    def __init__(self, *args: object, error_in_pan: bool, error_in_tilt: bool):
        self.error_in_pan = error_in_pan
        self.error_in_tilt = error_in_tilt
        super().__init__(*args)


class Gimbal2D(Actuator):

    NEXT_ID: int = 0

    MODE_IMMEDIATE = 0
    MODE_REACHED = 1
    MODE_SETTLED = 2
    MODE_TRAJECTORY = 3

    def __init__(self, *argv: Any, tilt_mirror_height_in: float = 33.5, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        # prevent various error states until we have a use case for them
        assert self._tilt and self._pan
        if not isinstance(self._pan, type(self._tilt)) or not isinstance(self._tilt, type(self._pan)):
            LOG.warning(f"Unexpected mismatching servo types: {type(self._pan)} vs {type(self._tilt)}")
        if self._pan.resolution != self._tilt.resolution:
            LOG.warning(f"Unexpected mismatching servo resolutions: {self._pan.resolution} vs {self._tilt.resolution}")
        self.tilt_mirror_height_in = tilt_mirror_height_in
        self.set_data()

    def add_callback(self, _: Callable[[], None]) -> None:
        pass

    def move(self, pos: Tuple[int, int]) -> None:
        raise NotImplementedError("Not Implemented")

    def set_data(self) -> None:
        self._min = self._pan.min, self._tilt.min
        self._max = self._pan.max, self._tilt.max
        self._pan_limits = self._pan.min, self._pan.max
        self._tilt_limits = self._tilt.min, self._tilt.max
        self._center = self._pan.center, self._tilt.center
        # pan is left-->right
        # tilt is bottom-->top
        self._bottom_left = self._min
        self._top_left = self._pan.max, self._tilt.min
        self._top_right = self._max
        self._bottom_right = self._pan.min, self._tilt.max
        self._corners = [self._bottom_left, self._top_left, self._top_right, self._bottom_right]

        self._top_mid = self._pan.center, self._tilt.max
        self._right_mid = self._pan.max, self._tilt.center
        self._bottom_mid = self._pan.center, self._tilt.min
        self._left_mid = self._pan.min, self._tilt.center
        self._cardinals = [self._top_mid, self._right_mid, self._bottom_mid, self._left_mid]

    def status_callback(self) -> Dict[str, Any]:
        return {"min": self._min, "max": self._max}

    @classmethod
    def next_id(cls) -> int:
        cls.NEXT_ID += 1
        return cls.NEXT_ID

    @property
    def pan(self) -> Servo:
        return self._pan

    @property
    def tilt(self) -> Servo:
        return self._tilt

    @property
    def cardinals(self) -> List[Tuple[int, int]]:
        """top mid, right mid, bottom mid, left mid"""
        return self._cardinals

    @property
    def center(self) -> Tuple[int, int]:
        """servo center"""
        return self._center

    @property
    def min(self) -> Tuple[int, int]:
        """servo min"""
        return self._min

    @property
    def max(self) -> Tuple[int, int]:
        """servo max"""
        return self._max

    @property
    def pan_limits(self) -> Tuple[int, int]:
        """pan min/max"""
        return self._pan_limits

    @property
    def tilt_limits(self) -> Tuple[int, int]:
        """tilt min/max"""
        return self._tilt_limits

    @property
    def top_left(self) -> Tuple[int, int]:
        return self._top_left

    @property
    def top_mid(self) -> Tuple[int, int]:
        return self._top_mid

    @property
    def top_right(self) -> Tuple[int, int]:
        return self._top_right

    @property
    def right_mid(self) -> Tuple[int, int]:
        return self._right_mid

    @property
    def bottom_left(self) -> Tuple[int, int]:
        return self._bottom_left

    @property
    def bottom_mid(self) -> Tuple[int, int]:
        return self._bottom_mid

    @property
    def bottom_right(self) -> Tuple[int, int]:
        return self._bottom_right

    @property
    def left_mid(self) -> Tuple[int, int]:
        return self._left_mid

    @property
    def corners(self) -> List[Tuple[int, int]]:
        """4 corners, clockwise, from bottom left (pan left-->right, tilt bottom-->top)"""
        return self._corners

    @property
    def goal_position(self) -> Tuple[int, int]:
        return self._pan.goal_position, self._tilt.goal_position

    @property
    def resolution(self) -> int:
        return self._pan.resolution

    def _init_id(self) -> ReferenceId:
        return ReferenceId(f"{str(NodeType.GIMBAL).lower()}:{self.next_id()}")

    def _cache_named_references(self) -> None:
        for child_device_path, child_reference in self.subtree.items():
            if child_device_path.leaf().instance == ServoInstance.PAN:
                self._pan: Servo = cast(Servo, child_reference)
            elif child_device_path.leaf().instance == ServoInstance.TILT:
                self._tilt: Servo = cast(Servo, child_reference)
            else:
                assert False, "This is a bug. Unexpected child device path: {}, {}".format(
                    child_device_path, self.device_path
                )

    def points(
        self,
        center: bool = True,
        corners: bool = True,
        cardinals: bool = True,
        random: int = 0,
        quadrant_centroids: bool = False,
    ) -> List[Tuple[int, int]]:
        """
        Get a list of gimbal positions based on given parameters.

        Parameters:
            quadrant_centroids (bool): Whether to include centers of quarters
            corners (bool): Whether to include corners
            center (bool): Whether to include the center
            cardinals (bool): Whether to include the cardinal points
            random (int): Number of random points to include
            quadrant_centroids (bool): Whether to include the quadrant centers.

        Returns:
            list: A list of gimbal positions
        """
        reference_points = [self.center] if center else []
        if corners:
            reference_points += self.corners
        if cardinals:
            reference_points += self.cardinals
        for i in range(random):
            rand_pan = np.random.randint(*self.pan_limits)
            rand_tilt = np.random.randint(*self.tilt_limits)
            reference_points += [(rand_pan, rand_tilt)]

        if quadrant_centroids:
            pan_min, pan_max = self.pan_limits
            tilt_min, tilt_max = self.tilt_limits
            pan_step = (pan_max - pan_min) / 4
            tilt_step = (tilt_max - tilt_min) / 4
            tilts = [tilt_min + tilt_step, tilt_max - tilt_step, tilt_min + tilt_step, tilt_min + 2 * tilt_step]
            pans = [
                pan_min + pan_step,
                pan_min + pan_step,
                pan_max - pan_step,
                pan_max - pan_step,
                pan_min + 2 * pan_step,
            ]
            reference_points += cast(List[Tuple[int, int]], [*zip(pans, tilts)])

        return reference_points

    def within_limits(self, position: GimbalPosition) -> bool:
        return self._pan.within_limits(position.pan) and self._tilt.within_limits(position.tilt)

    def clamp(self, position: GimbalPosition) -> Tuple[int, int]:
        return self._pan.clamp(position.pan), self._tilt.clamp(position.tilt)

    # Position
    def set_position(self, position: GimbalPosition, time_ms: int = 0, await_settle: bool = False) -> None:
        self._pan.set_position(position.pan, time_ms)
        self._tilt.set_position(position.tilt, time_ms)
        if await_settle:
            self._pan.wait_position_complete()
            self._tilt.wait_position_complete()

    async def async_set_position(self, position: GimbalPosition, time_ms: int = 0, await_settle: bool = False) -> None:
        await asyncio.get_event_loop().run_in_executor(None, lambda: self.set_position(position, time_ms, await_settle))

    async def go_to_delta(self, delta_position: GimbalPosition, time_ms: int = 0, mode: int = 0) -> GimbalPosition:
        raise NotImplementedError("Go To Delta has no default implementation")

    async def go_to_delta_follow(
        self,
        delta_position: GimbalPosition,
        follow_velocity_vector: GimbalPosition,
        time_ms: int = 0,
        follow_time_ms: int = 200,
        interval_sleep_time_ms: int = 0,
        mode: int = 0,
    ) -> GimbalPosition:
        raise NotImplementedError("Go To Delta Follow has no default implementation")

    async def go_to_timestamp(
        self,
        timestamp_ms: int,
        mode: int,
        position: Tuple[int, int],
        velocity_mrpm: Tuple[int, int],
        follow_velocity: Tuple[int, int],
        follow_accel: Tuple[int, int],
        interval_sleep_time_ms: int,
        query_latest_pos_for_vel: bool,
    ) -> Tuple[Tuple[Tuple[int, int], Tuple[int, int]], Tuple[Tuple[int, int], Tuple[int, int]]]:
        raise NotImplementedError("Go To Timestamp has no default implementation")

    async def follow_timestamp(
        self, timestamp_ms: int, follow_velocity: Tuple[int, int], follow_accel: Tuple[int, int],
    ) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        raise NotImplementedError("Follow Timestamp has no default implementation")

    async def async_get_position(self) -> GimbalPosition:
        raise NotImplementedError("Async not Implemented by default")

    def get_position(self) -> GimbalPosition:
        return GimbalPosition(self._pan.get_position(), self._tilt.get_position())

    def get_goal_position(self) -> GimbalPosition:
        return GimbalPosition(self._pan.goal_position, self._tilt.goal_position)

    def get_delta_target_skews(self) -> Tuple[float, float]:
        return 0, 0

    # Enable / disable
    def enable(self) -> None:
        self._pan.enable()
        self._tilt.enable()

    def disable(self) -> None:
        self._pan.disable()
        self._tilt.disable()

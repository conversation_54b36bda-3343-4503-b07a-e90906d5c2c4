import asyncio
import os
from typing import Any, Dict

from config.client.cpp.config_client_python import ConfigClient
from core.controls.exterminator.controllers.aimbot.aimbot import get_aimbot_algorithms
from core.controls.exterminator.model.exterminator import Exterminator
from core.model.node.type import NodeType
from lib.common.commander.client import Commander<PERSON>lient
from lib.common.generation import GENERATION, is_slayer
from lib.common.logging import get_logger
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.web.http.router import ComponentRouterBuilder, RouterBuilder
from lib.common.web.http.router_utils import merge_add

LOG = get_logger(__name__)


class ExterminatorRouterBuilder(ComponentRouterBuilder):
    def __init__(self, exterminator: Exterminator):
        super().__init__(base_route=f"/{NodeType.EXTERMINATOR.serialize()}")
        self._exterminator: Exterminator = exterminator
        self._commander_addr = "10.10.3.1" if is_slayer() else "127.0.0.1"
        row: str = str(os.getenv("MAKA_ROW")) if os.getenv("MAKA_ROW") is not None else "1"
        self._row_number = int(row) if row.isdigit() else 1
        self._config_prefix = f"row{self._row_number}" if is_slayer() else GENERATION.BUD.to_str()
        self._commander_client: CommanderClient = CommanderClient(self._commander_addr)
        self._async_loop = get_event_loop_by_name()
        self._config_client = ConfigClient(f"{self._commander_addr}:61001")

    def _handle_arm(self, bot: Any) -> None:
        enabled: bool = asyncio.run_coroutine_threadsafe(
            self._commander_client.GetNextDashboardState(0), self._async_loop
        ).result().lasers_enabled
        if not enabled:
            asyncio.run_coroutine_threadsafe(self._commander_client.ToggleLasers(), self._async_loop).result()

    def _handle_disarm(self, bot: Any) -> None:
        enabled: bool = asyncio.run_coroutine_threadsafe(
            self._commander_client.GetNextDashboardState(0), self._async_loop
        ).result().lasers_enabled
        if enabled:
            asyncio.run_coroutine_threadsafe(self._commander_client.ToggleLasers(), self._async_loop).result()

    def _handle_makannihilate_start(self, bot: Any, algo: str) -> None:
        # defensive against UI bugs (why didn't body_desc validation catch this?)
        assert algo is not None and len(algo) > 0
        self._config_client.set_string_value(f"{self._config_prefix}/aimbot/algorithm", algo)
        enabled: bool = asyncio.run_coroutine_threadsafe(
            self._commander_client.GetNextDashboardState(0), self._async_loop
        ).result().row_enabled[self._row_number - 1]
        if not enabled:
            asyncio.run_coroutine_threadsafe(
                self._commander_client.ToggleRow(self._row_number), self._async_loop
            ).result()

    def _handle_makannihilate_stop(self, bot: Any) -> None:
        enabled: bool = asyncio.run_coroutine_threadsafe(
            self._commander_client.GetNextDashboardState(0), self._async_loop
        ).result().row_enabled[self._row_number - 1]
        if enabled:
            asyncio.run_coroutine_threadsafe(
                self._commander_client.ToggleRow(self._row_number), self._async_loop
            ).result()
        self._exterminator.cancel_makannihilate_from_sync()

    def _handle_makannihilate_algos(self, bot: Any) -> Dict[str, Any]:
        return {"algos": get_aimbot_algorithms()}

    def add_onto(self, bot_routes: RouterBuilder) -> None:
        bot_routes.add("POST", self.suburl("/arm"))(merge_add(self._handle_arm))
        bot_routes.add("POST", self.suburl("/disarm"))(merge_add(self._handle_disarm))

        bot_routes.add("GET", self.suburl("/makannihilate/algos"))(merge_add(self._handle_makannihilate_algos))
        bot_routes.add("POST", self.suburl("/makannihilate/start"), body_desc={"algo": str})(
            merge_add(self._handle_makannihilate_start)
        )
        bot_routes.add("POST", self.suburl("/makannihilate/stop"), body_desc={})(
            merge_add(self._handle_makannihilate_stop)
        )

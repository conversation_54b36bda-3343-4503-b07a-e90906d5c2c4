syntax = "proto3";

package aimbot;
option go_package = "proto/aimbot";

import "weed_tracking/proto/weed_tracking.proto";


message PingRequest {
  uint32 x = 1;
}

message PongReply {
  uint32  x = 1;
}

message Empty {

}
enum SafetyOverrideState {
  SafetyOverrideNone = 0;
  SafetyOverrideVelocityStop = 1;
}

// Aimbot

message TargetingState {
  bool weeding_enabled = 1;
  bool thinning_enabled = 2;
}
message AimbotState {
  string algorithm = 1;
  bool running = 2;
  bool armed = 3;
  TargetingState targeting_state = 4;
  bool ready = 5;
  SafetyOverrideState safety_override_state= 6;
  bool actuation_tasks_running = 7;
}

message TargetVelocityRequest {

}

message TargetVelocityReply {
  float velocity_min = 1;
  float velocity_max = 2;
}

// Actuation Task

message LaserTestActuationTask {
  uint32 scanner_id = 1; // 0 Means all of them
  uint32 duration_ms = 2;
}

message ImageDrawActuationTask {
  uint32 scanner_id = 1; // 0 Means all of them
  float speed_mmps = 2; // 50 is a good default
}

message RangeDrawActuationTask {
  uint32 scanner_id = 1; // 0 Means all of them
  float duration_s = 2; // 20 is a good default
}

message ActuationTaskRequest {
  oneof task {
    LaserTestActuationTask laser_test = 1;
    ImageDrawActuationTask image_draw = 2;
    RangeDrawActuationTask range_draw = 3;
  }
}

// Lens

message LensSetRequest {
  uint32 scanner_id = 1;
  uint32 value = 2;
}

message LensSetReply {

}

message LensGetRequest {
  uint32 scanner_id = 1;
}

message LensGetReply {
  uint32 value = 1;
  uint32 min_value = 2;
  uint32 max_value = 3;
  
  float manual_autofocus_percent = 4;
  bool manual_autofocusing = 5;
  uint32 scanner_id = 6;
}

message LensGetAllReply {
  repeated LensGetReply lens_status = 1;
}

message LensAutoFocusRequest {
  uint32 scanner_id = 1;
}

message LensAutoFocusReply {}

message StopLensAutoFocusRequest {
  uint32 scanner_id = 1;
}

// Laser

message LaserArmRequest {
  uint32 scanner_id = 1;
  bool armed = 2;
}

message LaserArmReply {

}

message LaserEnableRequest {
  uint32 scanner_id = 1;
  bool enabled = 2;
}
message LaserEnableReply {
  bool status = 1;
  string message = 2;
}

message LaserFireRequest {
  uint32 scanner_id = 1;
  bool fire = 2;
}
message LaserFireReply {
}

message LaserSetRequest {
  uint32 scanner_id = 1;
  bool on = 2;
}

message LaserSetReply {

}

message BurnIdividualImagesRequest {
  repeated uint32 scanner_id = 1;
  float speed_mmps = 2;
  float intensity = 3;
  string json_img = 4;
}

message LaserState {
	bool enabled = 1;
	bool firing = 2;
	bool error =3;
	string error_code = 4 [ deprecated = true ]; // Deprecated, use scanner state
	string error_message = 5 [ deprecated = true ]; // Deprecated, use scanner state
	bool power = 6;
  float delta_temp = 7;
  float current = 8;
  bool arced = 9;
  float power_level = 10;
}

// Servos

message ServoGoToRequest {
  uint32 scanner_id = 1;
  enum ServoType {
    PAN = 0;
    TILT = 1;
  }
  ServoType servo_type = 2;
  int32 position = 3;
  uint32 time_ms = 4;
  bool await_settle = 5;
}

message ServoGoToReply {

}

message ServoGetPosVelRequest {
  uint32 scanner_id = 1;
}

message ServoGetPosVelReply {
  int32 pan_position = 1;
  int32 tilt_position = 2;
  uint32 pan_velocity = 3;
  uint32 tilt_velocity = 4;
}

message ServoGetLimitsRequest {
  uint32 scanner_id = 1;
}

message ServoGetLimitsReply {
  int32 pan_min = 1;
  int32 pan_max = 2;
  int32 tilt_min = 3;
  int32 tilt_max = 4;
}

message TuningParam {
  string name = 1;
  oneof value {
    uint32 v_uint = 2;
    int32 v_int = 3;
    bool v_bool = 4;
    float v_float = 5;
    string v_string = 6;
  }
}

message TuningParamsUpdateRequest {
  repeated TuningParam params = 1;
}

message TuningParamsUpdateReply{

}

message TuningParamsGetRequest {
  string name = 1;
}

message TuningParamsGetReply {
  repeated TuningParam params = 1;
}

// Load Estimation

message GetLoadEstimateRequest {

}

message GetLoadEstimateReply {
  float current_load = 1;
  float target_load = 2;
}

// Management

message GetDiagnosticRequest {

}

message GetDiagnosticReply {
    string diagnostic = 1;
}

message ResetDevicesRequest {
  string device_id = 1;
}

message ResetDevicesReply {

}

message ResetScannerRequest {
  uint32 scanner_id = 1;
  bool metrics_only = 2;
}

message ResetScannerReply {

}

// Crosshair

message ScannerDescriptor {
  uint32 id = 1;
}

message ScannerTargetPosition {
  ScannerDescriptor scanner_descriptor = 1;
  uint32 x = 2;
  uint32 y = 3;
}
message CrosshairState {
  uint32 x = 1;
  uint32 y = 2;
  bool calibrating = 3;
  bool calibration_failed = 4;
}

message ScannerState {
  ScannerDescriptor scanner_descriptor = 1;
  LaserState laser_state = 2;
  CrosshairState crosshair_state = 3;
  bool scanner_error = 4;
	string error_code = 5;
	string error_message = 6;
  uint32 target_trajectory_id = 7;
  bool pan_failure = 8;
  bool tilt_failure = 9;
}

message AutoXHairCalibrationProgress {
  bool in_progress = 1;
  float progress = 2;
}
message ScannerStatusReply {
  repeated ScannerState states = 1;
  AutoXHairCalibrationProgress x_hair_progress = 2;
}

message BootedReply {
  bool booted = 1;
}

// Tracking

message TrackerState {
  uint32 id = 1;
  bool at_weed_limit = 2;
  bool rotary_timeout = 3;
  bool deepweed_error = 4;
}
message SchedulerState {
  bool over_capacity = 1;
}

message TrackingState {
  repeated TrackerState states = 1;
  SchedulerState scheduler_state = 2;
}

message BedtopHeightProfile {
  repeated double weed_height_columns = 1;
  repeated double crop_height_columns = 2;
  string pcam_id = 3;
}

message TrackerBedtopHeightProfile {
  repeated BedtopHeightProfile profiles = 1;
  float bbh_offset_mm = 2;
}

message GetDimensionsResponse {
  double min_x_mm = 1;
  double max_x_mm = 2;
  double min_y_mm = 3;
  double max_y_mm = 4;
  double center_x_mm = 5;
}

message GetTargetCamSNRequest {
  string camera_id = 1;
}
message GetTargetCamSNResponse {
  string serial_number = 1;
}

message ReloadThinningConfRequest {
}

message ReloadAlmanacConfRequest {
}
message ReloadDiscriminatorConfRequest {
}
message ReloadModelinatorConfRequest {
}
message ReloadTVEProfileRequest {
}
message FixLaserMetricsRequest {
  ScannerDescriptor scanner = 1;
  int64 total_fire_count = 2;
  int64 total_fire_time_ms = 3;
}

message TrackedItemsRequest {
  string cam_id = 1;
}

message TrackedItemHistory {
  uint64 timestamp_ms = 1;
  weed_tracking.Detection detection = 2;
}

message TrackedItem {
  int64 id = 1;
  repeated TrackedItemHistory history = 2;
}

message TrackedItemsResponse {
  repeated TrackedItem tracked_items = 1;
}

message ParticipationResponse {
  bool running_as_leader = 1;
}

service AimbotService {
  rpc Ping(PingRequest) returns (PongReply) {}
  rpc GetBooted(Empty) returns (BootedReply) {}

  // Aimbot
  rpc ArmLasers(Empty) returns (Empty) {}
  rpc DisarmLasers(Empty) returns (Empty) {}
  rpc GetAimbotState(Empty) returns (AimbotState) {}
  rpc SetTargetingState(TargetingState) returns (Empty) {}

  // Actuation Tasks
  rpc StartActuationTask(ActuationTaskRequest) returns (Empty) {}
  rpc CancelActuationTask(Empty) returns (Empty) {}

  // Lens
  rpc LensSet(LensSetRequest) returns (LensSetReply) {}
  rpc LensGet(LensGetRequest) returns (LensGetReply) {}
  rpc LensGetAll(Empty) returns (LensGetAllReply) {}
  rpc LensAutoFocus(LensAutoFocusRequest) returns (LensAutoFocusReply) {}
  rpc StopLensAutoFocus(StopLensAutoFocusRequest) returns (Empty) {}

  // Laser
  rpc LaserArm(LaserArmRequest) returns (LaserArmReply) {}
  rpc LaserSet(LaserSetRequest) returns (LaserSetReply) {}
  rpc LaserEnable(LaserEnableRequest) returns (LaserEnableReply) {}
  rpc LaserFire(LaserFireRequest) returns (LaserFireReply) {}
  rpc ResetLaserMetrics(ScannerDescriptor) returns (Empty) {}
  rpc FixLaserMetrics(FixLaserMetricsRequest) returns (Empty) {}
  rpc BurnIdividualImage(BurnIdividualImagesRequest) returns (Empty) {}

  // Servo
  rpc ServoGoTo(ServoGoToRequest) returns (ServoGoToReply) {}
  rpc ServoGetPosVel(ServoGetPosVelRequest) returns (ServoGetPosVelReply) {}
  rpc ServoGetLimits(ServoGetLimitsRequest) returns (ServoGetLimitsReply) {}

  // Tuning Params
  rpc TuningParamsUpdate(TuningParamsUpdateRequest) returns (TuningParamsUpdateReply) {}
  rpc TuningParamsGet(TuningParamsGetRequest) returns (TuningParamsGetReply) {}

  // Load Estimate
  rpc GetLoadEstimate(GetLoadEstimateRequest) returns (GetLoadEstimateReply) {}

  // Management
  rpc GetDiagnostic(GetDiagnosticRequest) returns (GetDiagnosticReply) {}
  rpc ResetDevices(ResetDevicesRequest) returns (ResetDevicesReply) {}
  rpc ResetScanner(ResetScannerRequest) returns (ResetScannerReply) {}

  // Crosshair
  rpc StartAutoCalibrateCrosshair(ScannerDescriptor) returns (Empty);
  rpc StartAutoCalibrateAllCrosshairs(Empty) returns (Empty);
  rpc StopAutoCalibrate(Empty) returns (Empty);
  rpc SetCrosshairPosition(ScannerTargetPosition) returns (Empty);
  rpc MoveScanner(ScannerTargetPosition) returns (Empty);
  rpc GetAutoCalibrationProgress(Empty) returns (AutoXHairCalibrationProgress);

  rpc GetScannerStatus(Empty) returns (ScannerStatusReply);
  rpc GetTargetVelocity(TargetVelocityRequest) returns (TargetVelocityReply);

  // Tracking
  rpc GetTrackingState(Empty) returns (TrackingState);
  rpc GetBedtopHeightProfile(Empty) returns (TrackerBedtopHeightProfile);

  rpc GetDimensions(Empty) returns (GetDimensionsResponse);

  // TargetCam
  rpc GetTargetCamSN(GetTargetCamSNRequest) returns (GetTargetCamSNResponse);

  rpc ReloadThinningConf(ReloadThinningConfRequest) returns (Empty);

  // Almanac
  rpc ReloadAlmanacConf(ReloadAlmanacConfRequest) returns (Empty);
  rpc ReloadDiscriminatorConf(ReloadDiscriminatorConfRequest) returns (Empty);
  rpc ReloadModelinatorConf(ReloadModelinatorConfRequest) returns (Empty);

  // Target Velocity Estimator
  rpc ReloadTVEProfile(ReloadTVEProfileRequest) returns (Empty);

  // Tracked Items
  rpc GetDistanceTrackedItems(TrackedItemsRequest) returns (TrackedItemsResponse);

  rpc GetParticipation(Empty) returns (ParticipationResponse);
}

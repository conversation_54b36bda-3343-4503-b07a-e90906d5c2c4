import asyncio
import datetime
import functools
import json
import os
import random
from types import TracebackType
from typing import Any, Dict, Optional, Tuple, Type

import grpc
import pytz

import lib.common.logging
from config.client.cpp.config_client_python import ConfigTree, get_global_config_subscriber
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from core.fs.defaults import MEDIA_DIR
from lib.common.error import MakaException
from lib.common.time import iso8601_timestamp, maka_control_timestamp_ms
from trajectory.pybind.trajectory_python import TrackedItemCentroid

LOG = lib.common.logging.get_logger(__name__)
GEO_LOCATION = os.environ.get("MAKA_GEO_LOCATION", "unset")
DATA_DIR = os.environ.get("MAKA_DATA_DIR", "/data")


class P2PContextNotFoundException(MakaException):
    pass


class P2PMatch:
    def __init__(self, coord: Optional[Tuple[float, float]], safe: bool, timestamp_ms: int):
        self._coord = coord
        self._timestamp_ms = timestamp_ms
        self._safe = safe

    @property
    def timestamp_ms(self) -> int:
        return self._timestamp_ms

    @property
    def found(self) -> bool:
        """Whether match has been found."""
        return self._coord is not None

    @property
    def coord(self) -> Optional[Tuple[float, float]]:
        """(x, y) coordinates of object-of-interest."""
        return self._coord

    @property
    def safe(self) -> bool:
        return self._safe


def get_next_p2p_result_after(
    scanner: AimbotScanner, timestamp_ms: int, predict_timestamp_ms: int, timeout_ms: int = 1000
) -> P2PMatch:
    target_id = scanner.target_id
    start_time = maka_control_timestamp_ms()
    while maka_control_timestamp_ms() - start_time < timeout_ms:
        p2p_result = scanner.cv_runtime_client.get_next_p2p_prediction(target_id, timeout_ms, timestamp_ms)
        if p2p_result.target_timestamp_ms >= timestamp_ms and p2p_result.predict_timestamp_ms >= predict_timestamp_ms:
            return P2PMatch(
                (p2p_result.target_coord_x, p2p_result.target_coord_y) if p2p_result.matched else None,
                p2p_result.safe,
                p2p_result.timestamp_ms,
            )
    raise TimeoutError("Failed to get p2p result")


async def get_next_p2p_result_after_async(
    scanner: AimbotScanner,
    timestamp_ms: int,
    predict_timestamp_ms: int,
    retrieve_interval_ms: int,
    optional_predict_timestamp_ms: Optional[int],
    timeout_ms: int = 150,
    centroid: Any = None,
    optional_centroid: Any = None,
) -> P2PMatch:
    target_id = scanner.target_id
    p2p_result = await asyncio.get_event_loop().run_in_executor(
        None,
        functools.partial(
            scanner.cv_runtime_client.get_next_p2p_prediction,
            target_id,
            timeout_ms=timeout_ms,
            last_timestamp_ms=timestamp_ms,
        ),
    )
    if p2p_result.target_timestamp_ms >= timestamp_ms and (
        p2p_result.predict_timestamp_ms == predict_timestamp_ms
        or (
            optional_predict_timestamp_ms is not None
            and p2p_result.predict_timestamp_ms == optional_predict_timestamp_ms
        )
    ):
        if p2p_result.predict_timestamp_ms == predict_timestamp_ms:
            if p2p_result.predict_coord_x < 0 and p2p_result.predict_coord_y < 0:
                pass
            elif (
                centroid.get_pcam_id() != p2p_result.predict_cam
                or abs(centroid.get_p2p_coord()[0] - p2p_result.predict_coord_x) > 1e-3
                or abs(centroid.get_p2p_coord()[1] - p2p_result.predict_coord_y) > 1e-3
            ):
                LOG.error(
                    f"SUSPICIOUS P2P {centroid.get_pcam_id()} {p2p_result.predict_cam}, {centroid.get_p2p_coord()[0]} {p2p_result.predict_coord_x}, {centroid.get_p2p_coord()[1]} {p2p_result.predict_coord_y}"
                )
        if (
            optional_predict_timestamp_ms is not None
            and p2p_result.predict_timestamp_ms == optional_predict_timestamp_ms
        ):
            if p2p_result.predict_coord_x < 0 and p2p_result.predict_coord_y < 0:
                pass
            elif (
                optional_centroid.get_pcam_id() != p2p_result.predict_cam
                or abs(optional_centroid.get_p2p_coord()[0] - p2p_result.predict_coord_x) > 1e-3
                or abs(optional_centroid.get_p2p_coord()[1] - p2p_result.predict_coord_y) > 1e-3
            ):
                LOG.error(
                    f"SUSPICIOUS P2P (OPTIONAL) {optional_centroid.get_pcam_id()} {p2p_result.predict_cam}, {optional_centroid.get_p2p_coord()[0]} {p2p_result.predict_coord_x}, {optional_centroid.get_p2p_coord()[1]} {p2p_result.predict_coord_y}"
                )

        return P2PMatch(
            (p2p_result.target_coord_x, p2p_result.target_coord_y) if p2p_result.matched else None,
            p2p_result.safe,
            p2p_result.target_timestamp_ms,
        )

    if p2p_result.target_timestamp_ms < timestamp_ms:
        raise TimeoutError(f"Target Timestamp too low: {p2p_result.target_timestamp_ms} {timestamp_ms}")

    if p2p_result.predict_timestamp_ms != predict_timestamp_ms and optional_predict_timestamp_ms is None:
        raise TimeoutError(f"Perspective Timestamp too low: {p2p_result.predict_timestamp_ms} {predict_timestamp_ms}")

    if optional_predict_timestamp_ms is not None and p2p_result.predict_timestamp_ms != optional_predict_timestamp_ms:
        raise TimeoutError(
            f"Optional Perspective Timestamp too low: {p2p_result.predict_timestamp_ms} {optional_predict_timestamp_ms}"
        )

    raise TimeoutError("Failed to get p2p result")


def get_p2p_capture_path() -> str:
    return os.path.join(
        DATA_DIR,
        "labels",
        f"{GEO_LOCATION}-{pytz.timezone('America/Los_Angeles').fromutc(datetime.datetime.utcnow()).date()}",
    )


def get_p2p_prediction_cv_runtime(
    scanner: AimbotScanner,
    target_after_timestamp_ms: int,
    centroid: TrackedItemCentroid,
    forward: bool,
    fallback_centroid: Optional[TrackedItemCentroid],
) -> P2PMatch:
    assert centroid.get_has_perspective()
    subscriber = get_global_config_subscriber()
    tsz_node = subscriber.get_config_node("aimbot", "target_safety_zone")
    target_id = scanner.target_id
    up_zone = (
        tsz_node.get_node("forward_px").get_uint_value()
        if forward
        else tsz_node.get_node("backward_px").get_uint_value()
    )
    down_zone = (
        tsz_node.get_node("forward_px").get_uint_value()
        if not forward
        else tsz_node.get_node("backward_px").get_uint_value()
    )
    if not scanner.compare_last_centroids(centroid, fallback_centroid):
        scanner.cv_runtime_client.set_p2p_context(
            target_id,
            centroid.get_pcam_id(),
            centroid.get_timestamp_ms(),
            centroid.get_p2p_coord(),
            safe_zone_up=up_zone,
            safe_zone_down=down_zone,
            safe_zone_left=tsz_node.get_node("side_px").get_uint_value(),
            safe_zone_right=tsz_node.get_node("side_px").get_uint_value(),
            secondary_predict_camera_id=fallback_centroid.get_pcam_id() if fallback_centroid is not None else None,
            secondary_predict_timestamp_ms=fallback_centroid.get_timestamp_ms()
            if fallback_centroid is not None
            else None,
            secondary_perspective_coord=fallback_centroid.get_p2p_coord() if fallback_centroid is not None else None,
        )
    scanner.set_last_centroids(centroid, fallback_centroid)
    return get_next_p2p_result_after(scanner, target_after_timestamp_ms, centroid.get_timestamp_ms())


async def get_p2p_prediction_cv_runtime_async(
    scanner: AimbotScanner,
    target_after_timestamp_ms: int,
    centroid: TrackedItemCentroid,
    retrieve_interval_ms: int,
    forward: bool,
    fallback_centroid: Optional[TrackedItemCentroid],
    set_context_only: bool = False,
    identifier: int = 0,
) -> P2PMatch:
    assert centroid.get_has_perspective()
    subscriber = get_global_config_subscriber()
    tsz_node = subscriber.get_config_node("aimbot", "target_safety_zone")
    target_id = scanner.target_id
    up_zone = (
        tsz_node.get_node("forward_px").get_uint_value()
        if forward
        else tsz_node.get_node("backward_px").get_uint_value()
    )
    down_zone = (
        tsz_node.get_node("forward_px").get_uint_value()
        if not forward
        else tsz_node.get_node("backward_px").get_uint_value()
    )

    if not scanner.compare_last_centroids(centroid, fallback_centroid):
        try:
            await scanner.cv_runtime_client.set_p2p_context_async(
                target_id,
                centroid.get_pcam_id(),
                centroid.get_timestamp_ms(),
                centroid.get_p2p_coord(),
                safe_zone_up=up_zone,
                safe_zone_down=down_zone,
                safe_zone_left=tsz_node.get_node("side_px").get_uint_value(),
                safe_zone_right=tsz_node.get_node("side_px").get_uint_value(),
                secondary_predict_camera_id=fallback_centroid.get_pcam_id() if fallback_centroid is not None else None,
                secondary_predict_timestamp_ms=fallback_centroid.get_timestamp_ms()
                if fallback_centroid is not None
                else None,
                secondary_perspective_coord=fallback_centroid.get_p2p_coord()
                if fallback_centroid is not None
                else None,
                identifier=identifier,
            )
        except grpc.aio.AioRpcError as exc:
            if exc.code() == grpc.StatusCode.NOT_FOUND:
                raise P2PContextNotFoundException(f"Could not set p2p context for scanner {scanner.numeric_id}")
            else:
                raise exc
        target_after_timestamp_ms = maka_control_timestamp_ms()
    scanner.set_last_centroids(centroid, fallback_centroid)

    if set_context_only:
        return P2PMatch((0, 0), False, 0)
    alt_ts = None
    if fallback_centroid is not None:
        alt_ts = fallback_centroid.get_timestamp_ms()
    return await get_next_p2p_result_after_async(
        scanner,
        target_after_timestamp_ms,
        centroid.get_timestamp_ms(),
        retrieve_interval_ms=retrieve_interval_ms,
        optional_predict_timestamp_ms=alt_ts,
        timeout_ms=150,
        centroid=centroid,
        optional_centroid=fallback_centroid,
    )


async def get_p2p_prediction_async(
    scanner: AimbotScanner,
    target_after_timestamp_ms: int,
    centroid: TrackedItemCentroid,
    retrieve_interval_ms: int,
    forward: bool,
    fallback_centroid: Optional[TrackedItemCentroid],
    set_context_only: bool = False,
    identifier: int = 0,
) -> P2PMatch:
    return await get_p2p_prediction_cv_runtime_async(
        scanner,
        target_after_timestamp_ms,
        centroid,
        retrieve_interval_ms,
        forward,
        fallback_centroid,
        set_context_only,
        identifier,
    )


class BurstRecorder:
    machine_info = os.getenv("MAKA_ROBOT_NAME")
    row_info = os.getenv("MAKA_ROW")

    def __init__(self, scanner: AimbotScanner, on_time_ms: int) -> None:
        assert scanner.target is not None
        self._scanner = scanner
        self._target_cam_id = f"target{scanner.numeric_id}"
        self._on_time_ms = on_time_ms
        self._record = False
        self._subscriber = get_global_config_subscriber()

    @property
    def record(self) -> bool:
        return self._record

    async def __aenter__(self) -> Dict[str, Any]:
        burst_capture_base_dir = f"{MEDIA_DIR}/burst_recordings/full_burst_recordings"
        datetime_string = iso8601_timestamp(replace_colon=True)
        assert self._scanner.target is not None
        self._burst_record_metadata = {
            "iso8601_timestamp": datetime_string,
            "target_crosshair": list(self._scanner.target),
        }

        self._burst_record_path = (
            f"{burst_capture_base_dir}/{self.machine_info}_row{self.row_info}_{self._target_cam_id}_{datetime_string}"
        )

        burst_capture_config_node: ConfigTree = self._subscriber.get_config_node("aimbot", "target_burst_capture")
        burst_capture_filter = [
            node.get_uint_value() for node in burst_capture_config_node.get_node("filter").get_children_nodes()
        ]

        record = burst_capture_config_node.get_node("rate").get_float_value() > random.random()
        record = record and (not bool(burst_capture_filter) or self._scanner.numeric_id in burst_capture_filter)

        self._record = record

        if record:
            try:
                LOG.info(f"Saving burst recording to {self._burst_record_path}")
                os.makedirs(self._burst_record_path, exist_ok=True)

                await self._scanner.cv_runtime_client.start_burst_record_async(
                    self._target_cam_id, self._on_time_ms, self._burst_record_path
                )
            except Exception as e:
                LOG.error(e)
        return self._burst_record_metadata

    async def __aexit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        if os.path.isdir(self._burst_record_path):
            with open(f"{self._burst_record_path}/meta.json", "w") as fp:
                json.dump(self._burst_record_metadata, fp)
        try:
            await self._scanner.cv_runtime_client.stop_burst_record_async(self._target_cam_id)
        except Exception as e:
            LOG.error(e)

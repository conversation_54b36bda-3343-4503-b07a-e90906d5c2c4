from typing import Any, Optional

import lib.common.logging
from core.controls.exterminator.model.laser import Laser
from lib.common.devices.boards.row_module.row_module_board_device import RowModuleBoardDevice
from lib.common.devices.registry import DeviceRegistry

LOG = lib.common.logging.get_logger(__name__)


class RowModuleBoardLaser(Laser):
    def __init__(self, *args: Any, device_id: str, device_registry: DeviceRegistry, **kwargs: Any):
        super().__init__(*args, **kwargs)
        self._device_registry = device_registry
        self._device_id = device_id
        self._board = device_registry.get_device_from_sync(RowModuleBoardDevice, device_id)

        # Finding Scanner ID
        instance = self.device_path.trim_leaf().leaf().instance
        assert instance is not None
        self._scanner_id = int(instance)

    def arm(self) -> None:
        super().arm()
        self._board.arm_from_sync(True)

    async def async_arm(self) -> None:
        await self._board.arm(True)

    def disarm(self) -> None:
        self._board.arm_from_sync(False)
        super().disarm()

    async def async_disarm(self) -> None:
        await self._board.arm(False)

    def on(self, duration: Optional[int] = None) -> None:
        self._board.laser_from_sync(self._scanner_id, True)

    async def async_on(self) -> None:
        await self._board.laser(self._scanner_id, True)

    def off(self) -> None:
        self._board.laser_from_sync(self._scanner_id, False)

    async def async_off(self) -> bool:
        await self._board.laser(self._scanner_id, False)
        return True

from typing import Any, cast

import lib.common.logging
from core.controls.exterminator.model.gimbal import Gimbal2<PERSON>, GimbalException
from lib.common.devices.boards.row_module.row_module_board_device import GoToModeType, RowModuleBoardDevice
from lib.common.devices.registry import DeviceRegistry
from lib.common.types import GimbalPosition, GimbalVelocity
from lib.drivers.nanopb.row_module_board.row_module_board_connector import RowModuleBoardException

LOG = lib.common.logging.get_logger(__name__)


class RowModuleBoardGimbal(Gimbal2D):
    def __init__(
        self, *args: Any, device_id: str, device_registry: DeviceRegistry, **kwargs: Any,
    ):
        super().__init__(*args, **kwargs)
        self._device_registry = device_registry
        self._device_id = device_id
        self._board = device_registry.get_device_from_sync(RowModuleBoardDevice, device_id)

        # Finding Scanner ID
        instance = self.device_path.trim_leaf().leaf().instance
        assert instance is not None
        self._scanner_id = int(instance)

    def set_position(self, position: GimbalPosition, time_ms: int = 0, await_settle: bool = False) -> None:
        position = GimbalPosition(self._pan.clamp(position.pan), self._tilt.clamp(position.tilt))
        self._board.go_to_from_sync(
            self._scanner_id,
            position,
            GimbalVelocity(
                self._pan.compute_velocity_from_delta(position.pan, time_ms),
                self._tilt.compute_velocity_from_delta(position.tilt, time_ms),
            ),
            await_settle,
        )
        self._pan.goal_position = position.pan
        self._tilt.goal_position = position.tilt

    async def async_set_position(self, position: GimbalPosition, time_ms: int = 0, await_settle: bool = False) -> None:
        position = GimbalPosition(self._pan.clamp(position.pan), self._tilt.clamp(position.tilt))
        await self._board.go_to(
            self._scanner_id,
            position,
            GimbalVelocity(
                self._pan.compute_velocity_from_delta(position.pan, time_ms),
                self._tilt.compute_velocity_from_delta(position.tilt, time_ms),
            ),
            await_settle,
        )
        self._pan.goal_position = position.pan
        self._tilt.goal_position = position.tilt

    async def go_to_delta(self, delta_position: GimbalPosition, time_ms: int = 0, mode: int = 0) -> GimbalPosition:
        result = await self._board.go_to_delta(
            self._scanner_id,
            delta_position,
            GimbalVelocity(
                self._pan.compute_velocity(delta_position.pan, time_ms),
                self._tilt.compute_velocity(delta_position.tilt, time_ms),
            ),
            cast(GoToModeType, mode),
        )
        self._pan.goal_position = result.pan
        self._tilt.goal_position = result.tilt
        return result

    async def go_to_delta_follow(
        self,
        delta_position: GimbalPosition,
        follow_velocity_vector: GimbalPosition,
        time_ms: int = 0,
        follow_time_ms: int = 200,
        interval_sleep_time_ms: int = 0,
        mode: int = 0,
    ) -> GimbalPosition:
        try:
            result = await self._board.go_to_delta_follow(
                scanner_id=self._scanner_id,
                delta_position=delta_position,
                velocity=GimbalVelocity(
                    self._pan.compute_velocity(delta_position.pan, time_ms),
                    self._tilt.compute_velocity(delta_position.tilt, time_ms),
                ),
                follow_velocity_vector=follow_velocity_vector,
                follow_velocity_mrpm=GimbalVelocity(
                    self._pan.compute_velocity(follow_velocity_vector.pan, follow_time_ms),
                    self._tilt.compute_velocity(follow_velocity_vector.tilt, follow_time_ms),
                ),
                interval_sleep_time_ms=interval_sleep_time_ms,
                mode=cast(GoToModeType, mode),
            )
            self._pan.goal_position = result.pan
            self._tilt.goal_position = result.tilt
            return result
        except RowModuleBoardException as ex:
            raise GimbalException(ex)

    def get_position(self) -> GimbalPosition:
        position, _ = self._board.get_pos_vel_from_sync(self._scanner_id)
        return position

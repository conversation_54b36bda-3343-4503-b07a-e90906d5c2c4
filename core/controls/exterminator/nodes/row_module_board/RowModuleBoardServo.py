import math
from typing import Any

import lib.common.logging
from core.controls.exterminator.model.servo import Servo
from core.model.path import ServoInstance
from lib.common.devices.boards.row_module.row_module_board_device import RowModuleBoardDevice
from lib.common.devices.registry import DeviceRegistry
from lib.common.time.sleep import sleep_ms

LOG = lib.common.logging.get_logger(__name__)


class RowModuleBoardServo(Servo):
    def __init__(
        self, *args: Any, device_id: str, device_registry: DeviceRegistry, **kwargs: Any,
    ):
        super().__init__(*args, **kwargs)
        self._device_registry = device_registry
        self._device_id = device_id
        self._board = device_registry.get_device_from_sync(RowModuleBoardDevice, device_id)
        self._incs_per_ms_per_mrpm = ((self._resolution / 60) / 1000) / 1000

        # Finding Scanner ID
        instance = self.device_path.trim_leaf().trim_leaf().leaf().instance
        assert instance is not None
        self._scanner_id = int(instance)

        limits = self._board.get_limits_from_sync(self._scanner_id)
        limit = limits[0] if self.device_path.leaf().instance == ServoInstance.PAN else limits[1]
        assert limit[0] <= self.min and limit[1] >= self.max

        self._goal_position = self._get_position()

    def _compute_velocity_from_delta(self, position: int, time_ms: int) -> int:
        return self._compute_velocity(self._goal_position - position, time_ms)

    def _compute_velocity(self, position: int, time_ms: int) -> int:
        return math.ceil(
            self._board.max_velocity_mrpm
            if time_ms == 0
            else int((abs(position) / time_ms) / self._incs_per_ms_per_mrpm)
        )

    def _get_position(self) -> int:
        position, _ = self._board.get_pos_vel_from_sync(self._scanner_id)
        if self.device_path.leaf().instance == ServoInstance.PAN:
            return position[0]
        else:
            return position[1]

    def _set_position(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        if self.device_path.leaf().instance == ServoInstance.PAN:
            self._board.go_to_pan_from_sync(
                self._scanner_id, position, self._compute_velocity_from_delta(position, time_ms), await_settle
            )
        else:
            self._board.go_to_tilt_from_sync(
                self._scanner_id, position, self._compute_velocity_from_delta(position, time_ms), await_settle
            )

    def _wait_position_complete(self) -> None:
        # This is legacy, await settle should be requested in the set position call
        sleep_ms(200)

    def _disable(self) -> None:
        pass

    def _enable(self) -> None:
        pass

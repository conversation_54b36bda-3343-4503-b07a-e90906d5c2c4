from typing import Any, <PERSON><PERSON>

import lib.common.logging
from core.controls.exterminator.controllers.aimbot.process.client.aimbot_client import shared_aimbot_client
from core.controls.exterminator.model.gimbal import Gimbal2D

LOG = lib.common.logging.get_logger(__name__)


class AimbotProcessGimbal(Gimbal2D):
    def __init__(
        self, *args: Any, scanner_id: int, tilt_mirror_height_in: float = 33.5, **kwargs: Any,
    ):
        super().__init__(*args, tilt_mirror_height_in=tilt_mirror_height_in, **kwargs)
        self._aimbot_client = shared_aimbot_client
        self._scanner_id = scanner_id
        self._aimbot_client.await_connection_from_sync()

    def move(self, pos: Tuple[int, int]) -> None:
        self._aimbot_client.move_scanner_from_sync(self._scanner_id, pos)

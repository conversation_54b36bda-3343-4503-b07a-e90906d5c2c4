from typing import Any, Optional

import lib.common.logging
from core.controls.exterminator.controllers.aimbot.process.client.aimbot_client import shared_aimbot_client
from core.controls.exterminator.model.laser import Laser

LOG = lib.common.logging.get_logger(__name__)


class AimbotProcessLaser(Laser):
    def __init__(self, *args: Any, scanner_id: int, **kwargs: Any):
        super().__init__(*args, **kwargs)
        self._aimbot_client = shared_aimbot_client
        self._scanner_id = scanner_id
        self._aimbot_client.await_connection_from_sync()

    def arm(self) -> None:
        super().arm()
        self._aimbot_client.laser_arm_from_sync(self._scanner_id, True)

    async def async_arm(self) -> None:
        await self._aimbot_client.laser_arm(self._scanner_id, True)

    def disarm(self) -> None:
        self._aimbot_client.laser_arm_from_sync(self._scanner_id, False)
        super().disarm()

    async def async_disarm(self) -> None:
        await self._aimbot_client.laser_arm(self._scanner_id, False)

    def on(self, duration: Optional[int] = None) -> None:
        self._aimbot_client.laser_set_from_sync(self._scanner_id, True)

    async def async_on(self) -> None:
        await self._aimbot_client.laser_set(self._scanner_id, True)

    def off(self) -> None:
        self._aimbot_client.laser_set_from_sync(self._scanner_id, False)

    async def async_off(self) -> bool:
        await self._aimbot_client.laser_set(self._scanner_id, False)
        return True

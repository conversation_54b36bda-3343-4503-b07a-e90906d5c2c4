import math
from typing import Any

import lib.common.logging
from core.controls.exterminator.controllers.aimbot.process.client.aimbot_client import shared_aimbot_client
from core.controls.exterminator.model.servo import Servo
from core.model.path import ServoInstance
from lib.common.time.sleep import sleep_ms

LOG = lib.common.logging.get_logger(__name__)


class AimbotProcessServoException(Exception):
    pass


class AimbotProcessServo(Servo):
    def __init__(
        self, *args: Any, scanner_id: int, **kwargs: Any,
    ):
        super().__init__(*args, min=0, max=10000, resolution=262144, **kwargs)
        self._aimbot_client = shared_aimbot_client
        self._scanner_id = scanner_id
        self._aimbot_client.await_connection_from_sync()

        limits = self._aimbot_client.servo_get_limits_from_sync(self._scanner_id)
        limit = limits[0] if self.device_path.leaf().instance == ServoInstance.PAN else limits[1]
        self._min = limit[0]
        self._max = limit[1]
        self._incs_per_ms_per_mrpm = ((self._resolution / 60) / 1000) / 1000

        self._goal_position = 0

    def _compute_velocity_from_delta(self, position: int, time_ms: int) -> int:
        return self._compute_velocity(self._goal_position - position, time_ms)

    def _compute_velocity(self, position: int, time_ms: int) -> int:
        return math.ceil(590000 if time_ms == 0 else int((abs(position) / time_ms) / self._incs_per_ms_per_mrpm))

    def _get_position(self) -> int:
        pan, tilt = self._aimbot_client.servo_get_pos_vel_from_sync(self._scanner_id)
        if self.device_path.leaf().instance == ServoInstance.PAN:
            return pan[0]
        else:
            return tilt[0]

    async def async_set_position(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        servo_type = "pan" if self.device_path.leaf().instance == ServoInstance.PAN else "tilt"
        await self._aimbot_client.servo_go_to(
            self._scanner_id, servo_type, position, self._compute_velocity_from_delta(position, time_ms), await_settle
        )

    def _set_position(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        servo_type = "pan" if self.device_path.leaf().instance == ServoInstance.PAN else "tilt"
        self._aimbot_client.servo_go_to_from_sync(
            self._scanner_id, servo_type, position, self._compute_velocity_from_delta(position, time_ms), await_settle
        )

    def _wait_position_complete(self) -> None:
        # This is legacy, await settle should be requested in the set position call
        sleep_ms(200)

    def _disable(self) -> None:
        pass

    def _enable(self) -> None:
        pass

import asyncio
import math
from collections import deque
from typing import Any, Deque, List, Optional, Tuple

import numpy as np
from prometheus_client import Counter, Gauge

import lib.common.logging
from config.client.cpp.config_client_python import ConfigTree, get_global_config_subscriber
from core.controls.exterminator.controllers.aimbot.metrics import SCANNER_TIME_METRICS
from core.controls.exterminator.model.laser import Laser
from lib.common.asyncio.event_loop import use_specific_loop
from lib.common.devices.boards.pulczar.pulczar_board_device import PulczarBoardDevice
from lib.common.devices.device import DeviceStatusCode
from lib.common.devices.registry import DeviceRegistry
from lib.common.generation import is_bud
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time import maka_control_timestamp_ms
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import PulczarBoardFireStatus

LOG = lib.common.logging.get_logger(__name__)

LASER_TIME_COUNTER = Counter(
    name="laser_on_time_sec", documentation="Count laser usage in seconds", labelnames=["laser_id"]
)
LASER_TRIGGER_COUNTER = Counter(name="laser_trigger", documentation="Count laser triggers", labelnames=["laser_id"])
LASER_DUTY_CYCLE = Gauge(name="laser_duty_cycle", documentation="How often laser is firing", labelnames=["laser_id"])
LASER_DELTA_TEMP = Gauge(
    name="laser_delta_temp", documentation="Temp delta between baseline and laser thermistor", labelnames=["laser_id"]
)
LASER_CURRENT = Gauge(
    name="laser_current", documentation="The current as reported from the laser power supply", labelnames=["laser_id"]
)

# Found via experimentation that this is a good balance for low false positives and quick response
MIN_DUTY_CYCLE = 0.25
MIN_DELTA_TEMP = 0.25
DUTY_CYCLE_TIME_MS = 10000

ARC_CURRENT = 0.42
ARC_COUNT = 1
ARC_PERIOD = 1.0  # 1 hour


class PulczarBoardLaser(Laser):
    def __init__(self, *args: Any, device_id: str, device_registry: DeviceRegistry, **kwargs: Any):
        super().__init__(*args, **kwargs)
        self._device_registry = device_registry
        self._device_id = device_id
        self._board = device_registry.get_device_from_sync(PulczarBoardDevice, device_id)
        self._laser_start_time = 0
        self._time_counter = LASER_TIME_COUNTER.labels(device_id)  # TODO get laser serial number
        self._trigger_counter = LASER_TRIGGER_COUNTER.labels(device_id)  # TODO get laser serial number
        self._duty_cycle_metric = LASER_DUTY_CYCLE.labels(device_id)
        self._temp_metric = LASER_DELTA_TEMP.labels(device_id)
        self._current_metric = LASER_CURRENT.labels(device_id)
        self._arc_events: List[int] = []
        self._arc_count = 0
        self._arced_deluxe = False
        self._arced = False
        self._enabled = True
        self._firing = False
        self._shoot_times: Deque[Tuple[int, int, bool]] = deque([], maxlen=25)
        self._laser_failed = False
        self._event_loop = get_event_loop_by_name()
        with use_specific_loop(self._event_loop):
            self._metrics_lock = asyncio.Lock()

        self._config: Optional[ConfigTree] = None
        self._arc_deluxe_config: Optional[ConfigTree] = None

        tmp = (
            get_global_config_subscriber()
            .get_config_node("aimbot", "scanners")
            .get_node(f"scanner{self._board.scanner_id}")
        )
        if tmp is not None:
            # laser driving config
            self._config = tmp.get_node("laser")

            # arc detector (on the scanner)
            self._arc_deluxe_config = tmp.get_node("arc_detector")
        else:
            LOG.info(f"No config found for aimbot/scanners/scanner{self._board.scanner_id}")

        # config for global (old aimbot-side) arc detector
        tmp = get_global_config_subscriber().get_config_node("common", "arc_detector")
        if tmp is not None:
            self._arc_config = tmp
        else:
            LOG.info("No config found for common/arc_detector")

        # log message if both arc detectors are on
        if (
            self._arc_deluxe_config is not None
            and self._arc_config is not None
            and self._arc_deluxe_config.get_node("enabled").get_bool_value()
            and self._arc_config.get_node("enabled").get_bool_value()
        ):
            LOG.warning(f"Both aimbot and scanner based laser arc detector enabled for scanner{self._board.scanner_id}")

    def arm(self) -> None:
        super().arm()
        self._board.arm_from_sync(self._enabled)

    async def async_arm(self) -> None:
        super().arm()
        await self._board.arm(self._enabled)

    def disarm(self) -> None:
        self._board.arm_from_sync(False)
        super().disarm()

    async def async_disarm(self) -> None:
        await self._board.arm(False)
        super().disarm()

    async def is_armed(self) -> bool:
        return self._board.armed

    def on(self, duration: Optional[int] = None) -> None:
        event_loop = self._board.event_loop
        future = asyncio.run_coroutine_threadsafe(self.async_on(), event_loop)
        return future.result()

    async def async_on(self) -> None:
        SCANNER_TIME_METRICS.start_fire(self._board.scanner_id)
        self._start_count()
        start_data = await self._board.laser_set(True)
        await self._board.clear_current()
        self._firing = start_data.firing

    def off(self) -> None:
        event_loop = self._board.event_loop
        future = asyncio.run_coroutine_threadsafe(self.async_off(), event_loop)
        future.result()
        return

    async def async_off(self) -> bool:
        SCANNER_TIME_METRICS.end_fire(self._board.scanner_id)
        end = await self._board.laser_set(False)
        await self._end_count(end)

        status = self._board.pulczar_status
        if status is not None:
            self._arced_deluxe = status.arc_detected

        return True

    def firing(self) -> bool:
        return self._board.firing

    def _start_count(self) -> None:
        self._laser_start_time = maka_control_timestamp_ms()

    async def _end_count(self, end_state: PulczarBoardFireStatus) -> None:
        now = maka_control_timestamp_ms()
        delta = now - self._laser_start_time
        if self._laser_start_time > 0:
            currents = await self._board.get_current()
            if currents:
                current = np.mean(currents).item()
            else:
                current = 0
            asyncio.create_task(
                self._calc_metrics(
                    self._armed and self._board.get_shootable_status(), self._firing, now, delta, end_state, current
                )
            )
        self._laser_start_time = 0
        self._firing = False

    async def _calc_metrics(
        self, shootable: bool, shot: bool, now: int, delta: int, end_state: PulczarBoardFireStatus, avg_current: float
    ) -> None:
        async with self._metrics_lock:
            if shootable:
                if delta > 0:
                    self._time_counter.inc(delta / 1000)  # MS to seconds
                self._trigger_counter.inc()
            if shot and not math.isnan(avg_current):
                # Do not set if we have no data
                self._current_metric.set(avg_current)
            self._calc_duty_cycle(shot, now, delta, end_state)
            self._calc_arc_status(avg_current)

    def _calc_arc_status(self, avg_current: float) -> None:
        current_threshold = ARC_CURRENT
        count_threshold = ARC_COUNT
        count_period = 60 * 60 * 1000 * ARC_PERIOD
        if self._arc_config is not None:
            current_threshold = self._arc_config.get_node("current_threshold").get_float_value()
            count_threshold = self._arc_config.get_node("count_threshold").get_uint_value()
            # hours -> ms
            count_period = 60 * 60 * 1000 * self._arc_config.get_node("count_period").get_float_value()

        # Check for arcing
        if avg_current >= current_threshold:
            self._arc_count += 1
            self._arc_events.append(maka_control_timestamp_ms())

        # Clear old arc events
        # Iterate through whole list in case time jumps
        for e in self._arc_events:
            if e < maka_control_timestamp_ms() - count_period:
                self._arc_events.remove(e)  # all values are unique

        # Determine if meets arcing condition
        if len(self._arc_events) >= count_threshold:
            self._arced = True

    def _calc_duty_cycle(self, shot: bool, now: int, delta: int, end_state: PulczarBoardFireStatus) -> None:
        min_duty_cycle = MIN_DUTY_CYCLE
        min_delta_temp = MIN_DELTA_TEMP
        trust_thermistors = True
        if self._config is not None:
            min_duty_cycle = self._config.get_node("min_duty_cycle").get_float_value()
            min_delta_temp = self._config.get_node("min_delta_temp").get_float_value()
            trust_thermistors = self._config.get_node("trust_thermistors").get_bool_value()

        did_fire = shot and end_state.lpsu_power and end_state.fireable
        self._shoot_times.append((now, delta, did_fire))
        tmp_data = list(self._shoot_times)
        if len(tmp_data) > 10:
            for i in range(len(tmp_data)):
                if tmp_data[-1][0] - tmp_data[i][0] < DUTY_CYCLE_TIME_MS:
                    break
            if i > 0:
                i -= 1
            delta_time = tmp_data[-1][0] - tmp_data[i][0]
            shoot_time = 0
            duty_time = 0
            for st in tmp_data[i:]:
                if st[2]:
                    shoot_time += st[1]
                duty_time += st[1]
            duty_cycle = duty_time / delta_time
            shoot_cycle = shoot_time / delta_time
            if shoot_cycle > min_duty_cycle:
                delta_temp = end_state.delta_temp
                self._temp_metric.set(delta_temp)
                state = delta_temp < min_delta_temp
                if state != self._laser_failed:
                    LOG.debug(
                        f"Laser {self._device_id}: : failed state changed to {state}, delta temp: {delta_temp}, shoot cycle: {shoot_cycle}, shoot time: {shoot_time}, total time: {delta_time}"
                    )
                if trust_thermistors:
                    self._laser_failed = state
                else:
                    self._laser_failed = False
            self._duty_cycle_metric.set(duty_cycle)

    def error(self) -> bool:
        trust = True
        if self._config is not None:
            trust = self._config.get_node("trust_thermistors").get_bool_value()
        return self._laser_failed if trust else False

    def error_code(self) -> DeviceStatusCode:
        return self._board.get_status_sync().code

    def error_message(self) -> str:
        return self._board.get_status_sync().msg

    async def enable_async(self, enabled: bool) -> None:
        if self._enabled is True and enabled is False:
            self._arc_events = []
            self._arced = False
            self._arced_deluxe = False

        self._enabled = enabled
        await self._board.arm(super().armed and self._enabled)

    def _enable_from_sync(self, enabled: bool) -> None:
        # call into enable_async
        event_loop = self._board.event_loop
        assert event_loop is not None
        future = asyncio.run_coroutine_threadsafe(self.enable_async(enabled), event_loop)
        return future.result()

    def enable(self, enabled: bool) -> None:
        self._enable_from_sync(enabled)
        self._board.arm_from_sync(super().armed and self._enabled)

    @property
    def powered(self) -> bool:
        trust = True
        if self._config is not None:
            trust = self._config.get_node("trust_power").get_bool_value()
        return (not trust) or self._board.powered

    def latest_current(self) -> float:
        status = self._board.pulczar_status
        if status is None:
            return 0.0
        return status.lpsu_current

    def latest_delta_temp(self) -> float:
        if is_bud():
            return 0.0
        status = self._board.pulczar_status
        if status is None:
            return 0.0
        tmp = status.power_value
        power_mv = int(tmp)
        baseline_mv = int(tmp * 10000) % 10000
        return PulczarBoardFireStatus.temp(power_mv) - PulczarBoardFireStatus.temp(baseline_mv)

    def is_arced(self) -> bool:
        """
        Determine whether laser arced; this is the logical OR of the new arc detector (running on
        the scanner) as well as the old one that is implemented in this class based on the LPSU
        current provided in the firing status message. If both types of arc detectors are enabled,
        preference is given to the new detector.
        """
        arc_flag = False

        if self._arc_deluxe_config is not None and self._arc_deluxe_config.get_node("enabled").get_bool_value():
            arc_flag = arc_flag or self._arced_deluxe

        if self._arc_config is not None and self._arc_config.get_node("enabled").get_bool_value():
            arc_flag = arc_flag or self._arced

        return arc_flag

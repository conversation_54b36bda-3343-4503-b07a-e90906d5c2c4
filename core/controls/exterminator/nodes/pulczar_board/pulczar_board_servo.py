import math
from typing import Any

import lib.common.logging
from core.controls.exterminator.model.servo import Servo
from core.model.path import ServoInstance
from lib.common.devices.boards.pulczar.pulczar_board_device import PulczarBoardDevice
from lib.common.devices.registry import DeviceRegistry
from lib.common.time.sleep import sleep_ms

LOG = lib.common.logging.get_logger(__name__)


class PulczarBoardServoException(Exception):
    pass


class PulczarBoardServo(Servo):
    def __init__(
        self, *args: Any, device_id: str, device_registry: DeviceRegistry, **kwargs: Any,
    ):
        super().__init__(*args, min=0, max=10000, resolution=262144, **kwargs)
        self._device_registry = device_registry
        self._device_id = device_id
        self._board = device_registry.get_device_from_sync(PulczarBoardDevice, device_id)
        self._set_info_from_board()
        self._incs_per_ms_per_mrpm = ((self._resolution / 60) / 1000) / 1000
        self._board.add_callback(self._set_info_from_board)

    def _set_info_from_board(self) -> None:
        try:
            limits = self._board.get_limits_from_sync()
            limit = limits[0] if self.device_path.leaf().instance == ServoInstance.PAN else limits[1]
            self._min = limit[0]
            self._max = limit[1]
            self._goal_position = self._get_position()
        except Exception as ex:
            LOG.error(f"Failed to set data for {self._device_id}: {ex}")
            self._min = 0
            self._max = 0
            self._goal_position = 0

    def _compute_velocity_from_delta(self, position: int, time_ms: int) -> int:
        return self._compute_velocity(self._goal_position - position, time_ms)

    def _compute_velocity(self, position: int, time_ms: int) -> int:
        default_velocity = self._board.get_best_velocity_for_move(position, is_tilt=self._is_tilt)
        return math.ceil(
            default_velocity if time_ms == 0 else int((abs(position) / time_ms) / self._incs_per_ms_per_mrpm)
        )

    def _get_position(self) -> int:
        position, _ = self._board.get_pos_vel_from_sync()
        if self.device_path.leaf().instance == ServoInstance.PAN:
            return position[0]
        else:
            return position[1]

    async def async_set_position(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        if self.device_path.leaf().instance == ServoInstance.PAN:
            await self._board.go_to_pan(position, self._compute_velocity_from_delta(position, time_ms), await_settle)
        else:
            await self._board.go_to_tilt(position, self._compute_velocity_from_delta(position, time_ms), await_settle)

    def _set_position(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        if self.device_path.leaf().instance == ServoInstance.PAN:
            self._board.go_to_pan_from_sync(
                position, self._compute_velocity_from_delta(position, time_ms), await_settle
            )
        else:
            self._board.go_to_tilt_from_sync(
                position, self._compute_velocity_from_delta(position, time_ms), await_settle
            )

    def _wait_position_complete(self) -> None:
        # This is legacy, await settle should be requested in the set position call
        sleep_ms(200)

    def _disable(self) -> None:
        pass

    def _enable(self) -> None:
        pass

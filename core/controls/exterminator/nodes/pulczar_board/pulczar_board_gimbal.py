from typing import Any, Callable, Optional, Tuple, cast

import lib.common.logging
from core.controls.exterminator.model.gimbal import Gimbal2D, GimbalException, GimbalOutOfBoundsException
from core.controls.exterminator.nodes.pulczar_board.pulczar_board_servo import PulczarBoardServo
from lib.common.devices.boards.nofx.nofx_board_device import NoFXBoardDevice
from lib.common.devices.boards.pulczar.pulczar_board_device import (
    GoToModeType,
    PulczarBoardDevice,
    PulczarBoardException,
)
from lib.common.devices.device import DeviceStatus
from lib.common.devices.registry import DeviceRegistry, DeviceRegistryException
from lib.common.types import GimbalPosition, GimbalVelocity
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import PositionAt, PulczarOutOfBoundsException

LOG = lib.common.logging.get_logger(__name__)


class PulczarBoardGimbal(Gimbal2D):
    def __init__(
        self,
        *args: Any,
        device_id: str,
        device_registry: DeviceRegistry,
        nofx_device_id: str = "nofx_board_1",
        tilt_mirror_height_in: float = 33.5,
        pan_override: Optional[PulczarBoardServo] = None,
        tilt_override: Optional[PulczarBoardServo] = None,
        **kwargs: Any,
    ):
        if pan_override is not None:
            self._pan = pan_override
        if tilt_override is not None:
            self._tilt = tilt_override
        self._tilt.set_is_tilt()
        super().__init__(*args, tilt_mirror_height_in=tilt_mirror_height_in, **kwargs)
        self._device_registry = device_registry
        self._device_id = device_id
        self._board = device_registry.get_device_from_sync(PulczarBoardDevice, device_id)
        self._nofx_device_id = nofx_device_id
        # TODO Find a better way to get the ref, putting here for sake of field urgency
        try:
            self._nofx_device: Optional[NoFXBoardDevice] = device_registry.get_device_from_sync(
                NoFXBoardDevice, nofx_device_id
            )
        except DeviceRegistryException:
            self._nofx_device = None
        self._board.add_callback(self.set_data)

    async def get_status(self) -> DeviceStatus:
        return await self._board.get_status()

    def add_callback(self, callback: Callable[[], None]) -> None:
        self._board.add_callback(callback)

    @property
    def hacky_nofx_device(self) -> Optional[NoFXBoardDevice]:
        return self._nofx_device

    def get_delta_target_skews(self) -> Tuple[float, float]:
        return self._board.get_delta_target_config_from_sync()

    def set_position(self, position: GimbalPosition, time_ms: int = 0, await_settle: bool = False) -> None:
        position = GimbalPosition(self._pan.clamp(position.pan), self._tilt.clamp(position.tilt))
        self._board.go_to_from_sync(
            position,
            GimbalVelocity(
                self._pan.compute_velocity_from_delta(position.pan, time_ms),
                self._tilt.compute_velocity_from_delta(position.tilt, time_ms),
            ),
            await_settle,
        )
        self._pan.goal_position = position.pan
        self._tilt.goal_position = position.tilt

    async def async_set_position(self, position: GimbalPosition, time_ms: int = 0, await_settle: bool = False) -> None:
        position = GimbalPosition(self._pan.clamp(position.pan), self._tilt.clamp(position.tilt))
        await self._board.go_to(
            position,
            GimbalVelocity(
                self._pan.compute_velocity_from_delta(position.pan, time_ms),
                self._tilt.compute_velocity_from_delta(position.tilt, time_ms),
            ),
            await_settle,
        )
        self._pan.goal_position = position.pan
        self._tilt.goal_position = position.tilt

    async def go_to_delta(self, delta_position: GimbalPosition, time_ms: int = 0, mode: int = 0) -> GimbalPosition:
        result = await self._board.go_to_delta(
            delta_position,
            GimbalVelocity(
                self._pan.compute_velocity(delta_position.pan, time_ms),
                self._tilt.compute_velocity(delta_position.tilt, time_ms),
            ),
            cast(GoToModeType, mode),
        )
        self._pan.goal_position = result.pan
        self._tilt.goal_position = result.tilt
        return result

    async def go_to_delta_follow(
        self,
        delta_position: GimbalPosition,
        follow_velocity_vector: GimbalPosition,
        time_ms: int = 0,
        follow_time_ms: int = 200,
        interval_sleep_time_ms: int = 0,
        mode: int = 0,
        fast_return: bool = False,
        timeout_ms: int = 1000,
    ) -> GimbalPosition:
        try:
            result = await self._board.go_to_delta_follow(
                delta_position=delta_position,
                velocity=GimbalVelocity(
                    self._pan.compute_velocity(delta_position.pan, time_ms),
                    self._tilt.compute_velocity(delta_position.tilt, time_ms),
                ),
                follow_velocity_vector=follow_velocity_vector,
                follow_velocity_mrpm=GimbalVelocity(
                    self._pan.compute_velocity(follow_velocity_vector.pan, follow_time_ms),
                    self._tilt.compute_velocity(follow_velocity_vector.tilt, follow_time_ms),
                ),
                interval_sleep_time_ms=interval_sleep_time_ms,
                mode=cast(GoToModeType, mode),
                fast_return=fast_return,
                timeout_ms=timeout_ms,
            )
            self._pan.goal_position = result.pan
            self._tilt.goal_position = result.tilt
            return result
        except PulczarOutOfBoundsException as ex:
            raise GimbalOutOfBoundsException(ex, error_in_pan=ex.error_in_pan, error_in_tilt=ex.error_in_tilt)
        except PulczarBoardException as ex:
            raise GimbalException(ex)

    async def compute_velocity(
        self, position: Tuple[int, int], time_ms: int = 0, query_latest: bool = False
    ) -> Tuple[int, int]:
        current_pos = (self._pan.goal_position, self._tilt.goal_position)
        if query_latest:
            current_pos = await self._board.get_pos()
        return (
            self._pan.compute_velocity(current_pos[0] - position[0], time_ms),
            self._tilt.compute_velocity(current_pos[1] - position[1], time_ms),
        )

    async def go_to_timestamp(
        self,
        timestamp_ms: int,
        mode: int,
        position: Tuple[int, int],
        velocity_mrpm: Optional[Tuple[int, int]],
        follow_velocity: Tuple[int, int],
        follow_accel: Tuple[int, int],
        interval_sleep_time_ms: int,
        query_latest_pos_for_vel: bool = False,
        timeout_ms: int = 10000,
    ) -> Tuple[Tuple[Tuple[int, int], Tuple[int, int]], Tuple[Tuple[int, int], Tuple[int, int]]]:
        if velocity_mrpm is None:
            velocity_mrpm = await self.compute_velocity(position, query_latest=query_latest_pos_for_vel)
        try:
            result = await self._board.go_to_timestamp(
                timestamp_ms,
                cast(GoToModeType, mode),
                position,
                velocity_mrpm,
                follow_velocity,
                follow_accel,
                interval_sleep_time_ms,
                timeout_ms=timeout_ms,
            )
            self._pan.goal_position = result[1][1][0]
            self._tilt.goal_position = result[1][1][1]
            return result
        except PulczarOutOfBoundsException as ex:
            raise GimbalOutOfBoundsException(ex, error_in_pan=ex.error_in_pan, error_in_tilt=ex.error_in_tilt)
        except PulczarBoardException as ex:
            raise GimbalException(ex)

    async def go_to_follow(
        self,
        timestamp_ms: int,
        position: Tuple[int, int],
        velocity_mrpm: Optional[Tuple[int, int]],
        follow_velocity: Tuple[int, int],
        follow_accel: Tuple[int, int],
        interval_sleep_time_ms: int,
    ) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        if velocity_mrpm is None:
            velocity_mrpm = (
                self._pan.compute_velocity(self._pan.goal_position - position[0], 0),
                self._tilt.compute_velocity(self._tilt.goal_position - position[1], 0),
            )
        try:
            result = await self._board.go_to_follow(
                timestamp_ms, position, velocity_mrpm, follow_velocity, follow_accel, interval_sleep_time_ms
            )
            return result
        except PulczarOutOfBoundsException as ex:
            raise GimbalOutOfBoundsException(ex, error_in_pan=ex.error_in_pan, error_in_tilt=ex.error_in_tilt)
        except PulczarBoardException as ex:
            raise GimbalException(ex)

    async def get_position_at_time(self, timestamp_us: int) -> Tuple[PositionAt, PositionAt]:
        return await self._board.get_pos_at_time(timestamp_us)

    async def follow_timestamp(
        self, timestamp_ms: int, follow_velocity: Tuple[int, int], follow_accel: Tuple[int, int],
    ) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        try:
            result = await self._board.follow_timestamp(timestamp_ms, follow_velocity, follow_accel)
            self._pan.goal_position = result[1][0]
            self._tilt.goal_position = result[1][1]
            return result
        except PulczarBoardException as ex:
            raise GimbalException(ex)

    async def async_get_position(self) -> GimbalPosition:
        position, _ = await self._board.get_pos_vel()
        return position

    async def async_set_goal_position(self, position: GimbalPosition) -> None:
        self._pan.goal_position = position.pan
        self._tilt.goal_position = position.tilt

    def get_position(self) -> GimbalPosition:
        position, _ = self._board.get_pos_vel_from_sync()
        return position

    @property
    def board(self) -> PulczarBoardDevice:
        return self._board

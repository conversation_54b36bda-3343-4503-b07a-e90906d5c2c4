# This package is for control interfaces to devices. The code in here should expose device-independent
# interfaces / abstract class to control various devices such as gimbals, servos, drive motors, lasers,
# IMU, GPS, INS, cameras, etc.
#
# For now, we have main devices interfaces directly under core/controls, and subpackages for routines
# (driving, shooting), inside further subpackages core.controls.drive and core.controls.shoot.

import math
from typing import Any, Dict, List, Optional

import numpy as np

from core.model.path import RelativeInstance
from lib.common.logging import get_logger
from lib.common.serialization.json import JsonSerializable
from lib.common.statistics import remove_outliers_index
from lib.common.time.time import TimestampedObject

LOG = get_logger(__name__)


class RotaryEncoderStats(JsonSerializable):
    def __init__(self, TPR: int, diameter_in: float):
        self.TPR = TPR
        self.diameter_in = diameter_in
        if diameter_in == 0:
            LOG.warning("rotary encoder wheel has 0 diameter")

    def to_json(self) -> Dict[str, Any]:
        return {"TPR": self.TPR, "diameter_in": self.diameter_in}

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "RotaryEncoderStats":
        return RotaryEncoderStats(TPR=data["TPR"], diameter_in=data["diameter_in"])


class RotaryEncodersSnapshot(JsonSerializable, TimestampedObject):
    def to_json(self) -> Dict[str, Any]:
        return {
            "timestamp_ms": self.timestamp_ms,
            "ticks_back_left": self.ticks[RelativeInstance.BACK_LEFT],
            "ticks_back_right": self.ticks[RelativeInstance.BACK_RIGHT],
            "ticks_front_left": self.ticks[RelativeInstance.FRONT_LEFT],
            "ticks_front_right": self.ticks[RelativeInstance.FRONT_RIGHT],
            "rotary_encoders_stats": {k: v.to_json() for k, v in self._encoder_stats.items()},
        }

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "RotaryEncodersSnapshot":
        return RotaryEncodersSnapshot(
            timestamp_ms=data["timestamp_ms"],
            ticks_back_left=data["ticks_back_left"],
            ticks_back_right=data["ticks_back_right"],
            ticks_front_left=data["ticks_front_left"],
            ticks_front_right=data["ticks_front_right"],
            rotary_encoders_stats={k: RotaryEncoderStats.from_json(v) for k, v in data["rotary_encoder_stats"]},
        )

    def __init__(
        self,
        timestamp_ms: int,
        ticks_back_left: Optional[int] = None,
        ticks_back_right: Optional[int] = None,
        ticks_front_left: Optional[int] = None,
        ticks_front_right: Optional[int] = None,
        rotary_encoders_stats: Optional[Dict[str, RotaryEncoderStats]] = None,
    ):
        super().__init__(timestamp_ms=timestamp_ms)
        self.ticks: Dict[str, int] = {
            RelativeInstance.BACK_LEFT: ticks_back_left if ticks_back_left is not None else 0,
            RelativeInstance.BACK_RIGHT: ticks_back_right if ticks_back_right is not None else 0,
            RelativeInstance.FRONT_LEFT: ticks_front_left if ticks_front_left is not None else 0,
            RelativeInstance.FRONT_RIGHT: ticks_front_right if ticks_front_right is not None else 0,
        }
        self._encoder_stats: Dict[
            str, RotaryEncoderStats
        ] = rotary_encoders_stats if rotary_encoders_stats is not None else {}

    def inches_since(self, other: "RotaryEncodersSnapshot") -> float:
        # Get tpr, diameters, and ticks difference in list form for stats
        wheel_tpr: List[int] = [stat.TPR for stat in self._encoder_stats.values()]
        wheel_diameters: List[float] = [stat.diameter_in for stat in self._encoder_stats.values()]
        wheel_ticks: List[int] = [self.ticks[s] - other.ticks[s] for s in self._encoder_stats.keys()]

        # filtering
        np_ticks = np.array(wheel_ticks)
        np_diameters = np.array(wheel_diameters)
        np_tpr = np.array(wheel_tpr)

        # find best resolution
        best = np_tpr == max(np_tpr)
        np_ticks = np.abs(np_ticks[best])
        np_diameters = np_diameters[best]
        np_tpr = np_tpr[best]

        # remove outliers
        if len(np_ticks) > 2:
            good = remove_outliers_index(np_ticks, 2)

            np_ticks = np_ticks[good]
            np_diameters = np_diameters[good]
            np_tpr = np_tpr[good]

        # convert to inches
        inches: List[float] = []
        for ticks, diameter, tpr in zip(np_ticks, np_diameters, np_tpr):
            inches.append((diameter * math.pi / tpr) * ticks)

        # get everyones data
        display_inches: List[float] = []
        for ticks, diameter, tpr in zip(wheel_ticks, wheel_diameters, wheel_tpr):
            display_inches.append((diameter * math.pi / tpr) * ticks)
        LOG.debug(
            f"inches_since report: {wheel_diameters} * pi / {wheel_tpr} * {wheel_ticks} -> {display_inches} w/ best {inches}"
        )

        # We have signed encoders now - update this to support querying the signed capabilities and have
        # callers take that into account.
        return float(np.mean(np.array(inches)))

#
# This Make<PERSON><PERSON> builds the Frame

SERVICE=frame
DIR=core/controls/$(SERVICE)

ROOT_DIR=$(shell realpath ../../../)
MODEL_MAKEFILE_TEMPLATE=$(ROOT_DIR)/core/model/Makefile.template
GENERATED_MODEL_DIR=$(ROOT_DIR)/generated/core/controls/$(SERVICE)/model
GENERATED_MODEL_MAKEFILE=$(GENERATED_MODEL_DIR)/Makefile
OLD_GENERATED_MAKEFILE_PATH=$(ROOT_DIR)/core/controls/$(SERVICE)/model/Makefile

all: model

model:
	mkdir -p $(GENERATED_MODEL_DIR)
	sed 's/___SERVICE___/$(SERVICE)/g' $(MODEL_MAKEFILE_TEMPLATE) > $(GENERATED_MODEL_MAKEFILE)
	make -C $(GENERATED_MODEL_DIR)

clean:
	if [ -f $(OLD_GENERATED_MAKEFILE_PATH) ]; then rm -f $(OLD_GENERATED_MAKEFILE_PATH); fi
	if [ -f $(GENERATED_MODEL_MAKEFILE) ]; then make -C $(GENERATED_MODEL_DIR) clean; fi

unit_tests:
	cd ../../.. && python -m pytest $(DIR) --durations=0

.PHONY: \
    all \
    clean \
    model \
    unit_tests

# TODO this file is named "frame_messages" to avoid protobuf name collision with driver/messages.proto at runtime
# Apparently, protobuf doesn't understand python modules and instead de-dupes by filename
#
# We can update the code generator modify the file name so this is avoided
acceleration_message:
  compose:
    - timestamp

  fields:
    x:
      type: float
      required: true
    y:
      type: float
      required: true
    z:
      type: float
      required: true


angular_velocity_message:
  compose:
    - timestamp

  fields:
    x:
      type: float
      required: true
    y:
      type: float
      required: true
    z:
      type: float
      required: true


geoposition_ecef_message:
  compose:
    - timestamp

  fields:
    x:
      type: float
      required: true
    y:
      type: float
      required: true
    z:
      type: float
      required: true


geoposition_lat_lon_alt_message:
  compose:
    - timestamp

  fields:
    lat:
      type: float
      required: true
    lon:
      type: float
      required: true
    alt:
      type: float
      required: true


heading_message:
  compose:
    - timestamp

  fields:
    heading:
      type: float
      required: true
      validations:
        - zero_to_360


mag_message:
  compose:
    - timestamp

  fields:
    x:
      type: float
      required: true
    y:
      type: float
      required: true
    z:
      type: float
      required: true


velocity_message:
  compose:
    - timestamp

  fields:
    x:
      type: float
      required: true
    y:
      type: float
      required: true
    z:
      type: float
      required: true

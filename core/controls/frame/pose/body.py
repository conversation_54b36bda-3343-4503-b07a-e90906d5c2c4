import math
from typing import Callable, List, Optional

import navpy
import numpy as np

from core.controls.frame.mechanical import MechanicalConfig
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from generated.core.controls.frame.model.heading_message import HeadingMessage
from lib.common.math import cart2pol, pol2heading
from lib.common.protocol.channel.base import SubscriberChannel, Topic
from lib.common.protocol.channel.cache import Cache


def in_to_km(inches: float) -> float:
    return inches * 0.0000254


def translate(ll: GeopositionLatLonAltMessage, d_in: float, bearing: float) -> GeopositionLatLonAltMessage:
    bearing = bearing % 360

    #
    # There are many places you can find this logic
    #

    R = 6378.1  # Radius of the Earth
    bearing_rad = np.deg2rad(bearing)
    d = in_to_km(d_in)  # Distance in km

    lat1 = np.deg2rad(ll.lat)
    lon1 = np.deg2rad(ll.lon)

    lat2 = math.asin(math.sin(lat1) * math.cos(d / R) + math.cos(lat1) * math.sin(d / R) * math.cos(bearing_rad))

    lon2 = lon1 + math.atan2(
        math.sin(bearing_rad) * math.sin(d / R) * math.cos(lat1), math.cos(d / R) - math.sin(lat1) * math.sin(lat2)
    )

    lat2 = np.rad2deg(lat2)
    lon2 = np.rad2deg(lon2)

    return GeopositionLatLonAltMessage(timestamp_ms=ll.timestamp_ms, lat=lat2, lon=lon2, alt=ll.alt)


class RobotBodyPoint:

    # TODO phi_deg
    def __init__(self, origin: GeopositionLatLonAltMessage, r: float, phi: float):
        self._origin = origin
        self._r: float = r
        self._phi: float = phi
        if max(abs(self._r), abs(self._phi)) > 0:
            self._ll = translate(self._origin, self._r, self._phi)
        else:
            self._ll = origin

    @property
    def r(self) -> float:
        return self._r

    @property
    def phi(self) -> float:
        return self._phi

    @property
    def lat_lon(self) -> GeopositionLatLonAltMessage:
        return self._ll

    @property
    def ecef(self) -> GeopositionEcefMessage:
        ecef = navpy.lla2ecef(self._ll.lat, self._ll.lon, self._ll.alt, "deg")
        return GeopositionEcefMessage(timestamp_ms=self._ll.timestamp_ms, x=ecef[0], y=ecef[1], z=ecef[2])

    def translate(self, r: float, phi: float) -> "RobotBodyPoint":
        return RobotBodyPoint(origin=self.lat_lon, r=r, phi=phi)


class RobotBody:
    def __init__(
        self,
        heading: HeadingMessage,
        lla: GeopositionLatLonAltMessage,
        mechanical_config: MechanicalConfig,
        force_field_distance_in: float,
    ):
        self._heading = heading
        heading_deg = heading.heading

        if lla.lat == 0 and lla.lon == 0:
            # lost gps
            self._gps_antenna = RobotBodyPoint(origin=lla, r=0, phi=0)
            self._wheel_centroid = RobotBodyPoint(origin=lla, r=0, phi=0)
        else:
            #
            # antenna
            #
            self._gps_antenna = RobotBodyPoint(origin=lla, r=0, phi=0)
            antenna_x, antenna_y = mechanical_config.gps_antenna_offset_in
            antenna_angle_deg_reversed = np.rad2deg(np.arctan2(-antenna_y, -antenna_x))
            wheel_angle_vector_length_in = np.sqrt(antenna_x ** 2 + antenna_y ** 2)

            self._wheel_centroid = self._gps_antenna.translate(
                r=wheel_angle_vector_length_in, phi=pol2heading(angle_deg=antenna_angle_deg_reversed) + heading_deg
            )

        #
        # Wheels
        #
        wheel_track_width_in = mechanical_config.track_width_in
        wheel_base_length_in = mechanical_config.wheelbase_length_in

        wheel_angle_deg = np.rad2deg(np.arctan2(wheel_base_length_in, wheel_track_width_in))
        wheel_angle_vector_length_in = np.sqrt((wheel_track_width_in / 2) ** 2 + (wheel_base_length_in / 2) ** 2)

        self._front_right_wheel_lla = self._wheel_centroid.translate(
            r=wheel_angle_vector_length_in, phi=pol2heading(angle_deg=wheel_angle_deg) + heading_deg
        )
        self._front_left_wheel_lla = self._wheel_centroid.translate(
            r=wheel_angle_vector_length_in, phi=pol2heading(angle_deg=-wheel_angle_deg) + heading_deg
        )
        self._back_right_wheel_lla = self._wheel_centroid.translate(
            r=wheel_angle_vector_length_in, phi=pol2heading(angle_deg=180 - wheel_angle_deg) + heading_deg
        )
        self._back_left_wheel_lla = self._wheel_centroid.translate(
            r=wheel_angle_vector_length_in, phi=pol2heading(angle_deg=180 + wheel_angle_deg) + heading_deg
        )

        #
        # vertices
        #
        frame_vertices_xy_in = mechanical_config.frame_vertices_in
        self._frame_vertices: List[RobotBodyPoint] = []
        for i, vertex_xy_in in enumerate(frame_vertices_xy_in):
            r, phi_rad = cart2pol(x=vertex_xy_in[0], y=vertex_xy_in[1])
            # need an extra quarter rotation clockwise to play nice with other math
            self._frame_vertices.append(
                self._wheel_centroid.translate(r=r, phi=pol2heading(angle_deg=np.rad2deg(phi_rad)) + heading_deg)
            )

        #
        # force field
        #
        self._force_field_points: List[RobotBodyPoint] = []

        for i, p in enumerate(self.frame_vertices):
            self._force_field_points.append(p.translate(r=force_field_distance_in, phi=p.phi))

    @property
    def heading(self) -> HeadingMessage:
        return self._heading

    @property
    def antenna_lla(self) -> GeopositionLatLonAltMessage:
        return self._gps_antenna.lat_lon

    @property
    def wheel_centroid_lla(self) -> GeopositionLatLonAltMessage:
        return self._wheel_centroid.lat_lon

    @property
    def wheel_centroid_ecef(self) -> GeopositionEcefMessage:
        return self._wheel_centroid.ecef

    @property
    def wheels(self) -> List[RobotBodyPoint]:
        return [
            self._front_right_wheel_lla,
            self._front_left_wheel_lla,
            self._back_right_wheel_lla,
            self._back_left_wheel_lla,
        ]

    @property
    def wheels_lat_lons(self) -> List[GeopositionLatLonAltMessage]:
        return [p.lat_lon for p in self.wheels]

    @property
    def frame_vertices(self) -> List[RobotBodyPoint]:
        return self._frame_vertices

    @property
    def frame_vertices_lat_lons(self) -> List[GeopositionLatLonAltMessage]:
        return [p.lat_lon for p in self.frame_vertices]

    @property
    def force_field(self) -> List[GeopositionLatLonAltMessage]:
        return [p.lat_lon for p in self._force_field_points]


class RobotBodyChannel(SubscriberChannel[RobotBody]):
    def __init__(
        self,
        topic: Topic,
        heading_subscription: SubscriberChannel[HeadingMessage],
        lla_subscription: SubscriberChannel[GeopositionLatLonAltMessage],
        mechanical_config: MechanicalConfig,
        force_field_distance_in: Callable[[], float],
    ):
        super().__init__(topic=topic)
        self._heading_subscription = Cache(
            heading_subscription, initial_value=HeadingMessage(timestamp_ms=0, heading=0)
        )
        self._lla_subscription = Cache(
            lla_subscription, initial_value=GeopositionLatLonAltMessage(timestamp_ms=0, lat=0, lon=0, alt=0)
        )
        self._mechanical_config = mechanical_config
        self._force_field_distance_in: Callable[[], float] = force_field_distance_in

    def read(self) -> Optional[RobotBody]:
        heading: HeadingMessage = self._heading_subscription.read()
        lla: GeopositionLatLonAltMessage = self._lla_subscription.read()
        # GPS outage
        if lla.lat == 0 and lla.lon == 0:
            return None
        else:
            return RobotBody(
                heading=heading,
                lla=lla,
                mechanical_config=self._mechanical_config,
                force_field_distance_in=self._force_field_distance_in(),
            )

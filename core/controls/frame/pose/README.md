# Pose
The Pose node is the authority on Robot Pose data.

Error bounds can be provided when they are known.

**<PERSON><PERSON> (Computer Vision) ( https://en.wikipedia.org/wiki/Pose_(computer_vision) ):**
> In computer vision and robotics, a typical task is to identify specific objects in an image and to determine each object's position and orientation relative to some coordinate system. This information can then be used, for example, to allow a robot to manipulate an object or to avoid moving into the object. The combination of position and orientation is referred to as the pose of an object [...].
>
>  The specific task of determining the pose of an object in an image (or stereo images, image sequence) is referred to as pose estimation.

**From Probabilistic Robotics (2006), page 20:**
> The robot pose, which comprises its location and orientation relative to a global coordinate frame. Rigid mobile robots possess six such state variables, three for their Cartesian coordinates, and three for their angular orientation (pitch, roll, and yaw). For rigid mobile robots confined to planar environments, the pose is usually given by three variables, its two location coordinates in the plane and its heading direction (yaw).
>
> In robot manipulation, the pose includes variables for the configuration of the robot's actuators. [...] The robot configuration is so often referred to as kinematic state.
>
> The robot velocity and velocities of its joints are commonly referred to as dynamic state. A rigid robot moving through space is characterized by up to six velocity variables, one for each pose variables.
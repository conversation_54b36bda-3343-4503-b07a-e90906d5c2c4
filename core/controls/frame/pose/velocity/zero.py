from core.controls.frame.pose.velocity.base import VelocityEstimator
from generated.core.controls.frame.model.acceleration_message import AccelerationMessage
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.logging import get_logger
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)


class AlwaysZeroVelocityEstimator(VelocityEstimator):
    """
    When nothing else is available, it's nice to have this plumbed.
    """

    def get_latest_accel(self) -> AccelerationMessage:
        return AccelerationMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    def get_latest_velocity(self) -> VelocityMessage:
        return VelocityMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    async def tick(self) -> None:
        return

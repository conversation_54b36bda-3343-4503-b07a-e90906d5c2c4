import asyncio

from core.controls.driver.drivebot.client.client import DrivebotClient
from core.controls.frame.pose.velocity.base import VelocityEstimator
from core.unity.unity import get_unity_connector
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.logging import get_logger
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)


class RotaryEncoderVelocityEstimator(VelocityEstimator):
    """
    A velocity estimator based on differential rotary encoder snapshots.
    """

    def __init__(self, unity: bool = False) -> None:
        super().__init__()

        # TODO expose parameters to UI
        self._latest_velocity_estimate = VelocityMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)
        if unity:
            unity_connector = get_unity_connector()
            self._drivebot = DrivebotClient(hostname=unity_connector.host, port=unity_connector.port)
        else:
            self._drivebot = DrivebotClient()
        self._drivebot_loop = get_event_loop_by_name()

    def get_latest_velocity(self) -> VelocityMessage:
        return self._latest_velocity_estimate

    async def tick(self) -> None:
        try:
            status = await asyncio.wrap_future(
                asyncio.run_coroutine_threadsafe(self._drivebot.get_status(), self._drivebot_loop)
            )
        except Exception as e:
            LOG.exception(e)
            return
        vel_msg = VelocityMessage(timestamp_ms=status.timestamp_ms, x=0, y=status.actual_velocity_mph, z=0)

        self._latest_velocity_estimate = vel_msg

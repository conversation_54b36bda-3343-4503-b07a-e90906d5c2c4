import math
from typing import Optional

import numpy as np

from core.controls.frame.model.topics import FrameTopic
from core.controls.frame.pose.heading.base import HeadingEstimator
from core.controls.frame.sensor.magnetometer.calibration.calibration import MagneticCalibration
from generated.core.controls.frame.model.heading_message import HeadingMessage
from generated.core.controls.frame.model.mag_message import MagMessage
from lib.common.logging import get_logger
from lib.common.math import MovingAverage
from lib.common.protocol.channel.base import SubscriberChannel, Topic
from lib.common.protocol.channel.callback import CallbackSubscriberChannel
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)


class MagHeadingEstimator(HeadingEstimator):
    def __init__(
        self,
        topic: Topic,
        mag_subscription: SubscriberChannel[MagMessage],
        mag_calibration: MagneticCalibration,
        heading_offset: int = 0,
    ):
        super().__init__(topic=topic)
        self._mag_subscription = mag_subscription
        self._mag_calibration = mag_calibration
        self._heading_offset = heading_offset

        self._last_timestamp_ms: Optional[int] = None

        # simple heading
        self._heading: Optional[float] = None

        # nominally attempt some basic smoothing
        self._smooth_mag_x: MovingAverage = MovingAverage(initial_value=0, smooth_factor=0.5)
        self._smooth_mag_y: MovingAverage = MovingAverage(initial_value=0, smooth_factor=0.5)
        self._smooth_heading: Optional[float] = None

    def get_heading_subscription(self) -> SubscriberChannel[HeadingMessage]:
        return CallbackSubscriberChannel(
            topic=self.topic.sub(FrameTopic.HEADING),
            callback=lambda: HeadingMessage(timestamp_ms=maka_control_timestamp_ms(), heading=self._smooth_heading)
            if self._heading is not None and self._smooth_heading is not None
            else None,
        )

    def _mag_x_y_to_heading(self, mag_x: float, mag_y: float) -> float:
        # trigonmetric heading w/r/t magnetic fields x/y
        heading_rad = math.atan2(mag_x, mag_y)
        # acount for offset and wraparound
        return (float(np.rad2deg(heading_rad)) + self._heading_offset) % 360

    async def tick(self) -> None:
        mag_message: Optional[MagMessage] = self._mag_subscription.read()
        if mag_message is not None:
            raw_mag = np.array([mag_message.x, mag_message.y, mag_message.z])
            self._mag_calibration.add_sample(raw_mag.reshape((3, 1)))

            normalized_mag = self._mag_calibration.apply(raw_mag)
            self._heading = self._mag_x_y_to_heading(normalized_mag[0], normalized_mag[1])

            self._smooth_mag_x.update(normalized_mag[0])
            self._smooth_mag_y.update(normalized_mag[1])
            self._smooth_heading = self._mag_x_y_to_heading(self._smooth_mag_x.value, self._smooth_mag_y.value)

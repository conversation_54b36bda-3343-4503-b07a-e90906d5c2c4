from typing import Callable

import numpy as np

from core.controls.frame.model.topics import FrameTopic
from core.controls.frame.pose.heading.base import HeadingEstimator
from core.unity.drivers.unity_ins import UnityINSNode
from generated.core.controls.frame.model.heading_message import HeadingMessage
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import Subscriber<PERSON>hannel, Topic
from lib.common.protocol.channel.callback import CallbackSubscriberChannel

LOG = get_logger(__name__)

# TODO patch Unity ECEF conversion so this is not required
UNITY_SKEW_DEG = 9.3811


class UnityHeadingEstimator(HeadingEstimator):
    def __init__(self, topic: Topic, ins: UnityINSNode, fuzz_stdev: Callable[[], float], bias: float = UNITY_SKEW_DEG):
        super().__init__(topic=topic)
        self._ins = ins
        self._bias: float = bias
        self._fuzz_stdev: Callable[[], float] = fuzz_stdev

    async def tick(self) -> None:
        pass

    def get_heading_subscription(self) -> SubscriberChannel[HeadingMessage]:
        return CallbackSubscriberChannel(topic=self.topic.sub(FrameTopic.HEADING), callback=self._heading)

    def _heading(self) -> HeadingMessage:
        fuzz_stdev = self._fuzz_stdev()
        h = self._ins.heading
        if fuzz_stdev == 0:
            return h
        fuzz = np.random.normal(loc=0, scale=fuzz_stdev)
        return HeadingMessage(timestamp_ms=h.timestamp_ms, heading=(h.heading + fuzz + self._bias) % 360)

from abc import ABC, abstractmethod

from generated.core.controls.frame.model.heading_message import HeadingMessage
from lib.common.protocol.channel.base import SubscriberChannel, Topic


class HeadingEstimator(ABC):
    """
    Refer README.
    """

    def __init__(self, topic: Topic):
        self._topic = topic

    @property
    def topic(self) -> Topic:
        return self._topic

    @abstractmethod
    def get_heading_subscription(self) -> SubscriberChannel[HeadingMessage]:
        pass

    async def tick(self) -> None:
        pass

import asyncio
import threading
from contextlib import contextmanager
from typing import Any, Awaitable, Callable, Dict, Generator, List, Optional, Set, Tuple, cast

from core.controls.driver.rotary_encoder import RotaryEncoderNode
from core.controls.frame.config import FrameConfigFile
from core.controls.frame.heading_distance import HeadingDistanceAccumulator
from core.controls.frame.mechanical import MechanicalConfig
from core.controls.frame.model.topics import FrameTopic
from core.controls.frame.parameter import FrameParameter, FrameParameters
from core.controls.frame.pose.body import RobotBody, RobotBodyChannel
from core.controls.frame.pose.heading.base import HeadingEstimator
from core.controls.frame.pose.heading.mag import MagHeadingEstimator
from core.controls.frame.pose.heading.unity.estimator import UnityHeadingEstimator
from core.controls.frame.pose.velocity.base import VelocityEstimator
from core.controls.frame.pose.velocity.rotary_encoders import RotaryEncoderVelocityEstimator
from core.controls.frame.pose.velocity.zero import AlwaysZeroVelocityEstimator
from core.controls.frame.rotary_encoders_snapshot import <PERSON>ota<PERSON><PERSON><PERSON><PERSON><PERSON>nap<PERSON>, RotaryEncoderStats
from core.controls.frame.sensor.fuel_sensor.fuel_sensor import FuelSensorNode
from core.controls.frame.sensor.geolocation.gps_spaced_history import GPSSpacedHistory
from core.controls.frame.sensor.geolocation.node import GPSNode
from core.controls.frame.sensor.imu.node import IMUNode
from core.controls.frame.sensor.ins.node import INSNode
from core.controls.frame.sensor.magnetometer.base import Magnetometer
from core.controls.frame.sensor.magnetometer.calibration.calibration import MagneticCalibration
from core.model.configuration import ServiceConfiguration
from core.model.node.classic import ClassicNode
from core.model.node.type import NodeType
from core.model.parameter import ParameterValueType
from core.model.path import RelativeInstance
from core.model.sensor import Sensor
from core.model.topics import Topics
from core.unity.drivers.unity_ins import UnityINSNode
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from generated.core.controls.frame.model.heading_message import HeadingMessage
from generated.core.controls.frame.model.mag_message import MagMessage
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import SubscriberChannel, Topic
from lib.common.protocol.channel.callback import CallbackSubscriberChannel
from lib.common.tasks.manager import get_event_loop_by_name, wait_on_event_loop_forever
from lib.common.time import maka_control_timestamp_ms
from lib.common.time.sleep import async_sleep_ms

LOG = get_logger(__name__)

FUEL_SAMPLE_HZ = 0.1


class FrameConfiguration(ServiceConfiguration[FrameConfigFile, FrameParameters]):
    def __init__(self, mechanical_config: MechanicalConfig, **kwargs: Any):
        super().__init__(**kwargs)
        self._mechanical_config = mechanical_config

    def _load_initial_configuration(self) -> None:
        self._magnetic_calibration = MagneticCalibration()
        if "calibration" in self._initial_configuration:
            if "magnetic_calibration" in self._initial_configuration["calibration"]:
                self._magnetic_calibration = MagneticCalibration.from_json(
                    self._initial_configuration["calibration"]["magnetic_calibration"]
                )

    @property
    def mechanical(self) -> MechanicalConfig:
        return self._mechanical_config

    @property
    def magnetic_calibration(self) -> MagneticCalibration:
        return self._magnetic_calibration


class Frame(Sensor):
    """
    Refer README.
    """

    def __init__(
        self, *argv: Any, unity: bool = False, config: FrameConfiguration, **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)

        # heading estimator
        self._heading_estimator: HeadingEstimator
        self._gps_spaced_history: Optional[GPSSpacedHistory] = None
        self._publish_sensor_channels()

        # Mechanical configuration
        self._config = config

        # heading estimator
        heading_estimator: Optional[HeadingEstimator] = None
        heading_topic = self.topic.sub("heading")

        self._tick_task_timeout = 1.0
        if unity:
            LOG.info(f"{self.device_path} Using Unity Pose Estimator")
            assert self._ins is not None and isinstance(self._ins, UnityINSNode)
            heading_estimator = UnityHeadingEstimator(
                topic=heading_topic,
                ins=self._ins,
                fuzz_stdev=lambda: self.parameters[FrameParameter.UNITY_HEADING_STD_DEV].float_value,
            )
            self._tick_task_timeout *= 10

            self._ins.set_gps_outage_freq_ms_callback(
                lambda: self.parameters[FrameParameter.UNITY_GPS_OUTAGE_FREQ_MS].int_value
            )
            self._ins.set_gps_outage_length_ms_callback(
                lambda: self.parameters[FrameParameter.UNITY_GPS_OUTAGE_LENGTH_MS].int_value
            )
        else:
            # TODO allow switching between different modes
            if self._gps is not None:
                heading_estimator = self._gps.get_heading_estimator(topic=heading_topic)

            if heading_estimator is None:
                heading_estimator = MagHeadingEstimator(
                    topic=heading_topic,
                    mag_subscription=self.feed.subscribe(topic=Topic("/frame/magnetometer"), msg_type=MagMessage),
                    mag_calibration=config.magnetic_calibration,
                )
        assert heading_estimator is not None
        self._heading_estimator = heading_estimator
        self.feed.publish(self._heading_estimator.topic, self._heading_estimator.get_heading_subscription())

        self._build_rotary_encoder_stats()
        self._latest_rotary_encoder_snapshot = RotaryEncodersSnapshot(
            timestamp_ms=maka_control_timestamp_ms(),
            ticks_front_left=None,
            ticks_front_right=None,
            ticks_back_left=None,
            ticks_back_right=None,
            rotary_encoders_stats=self._rotary_encoders_stats,
        )

        self._publish_body_subscription()

        # Heading distance accumulators
        self._distance_accumulators: Set[HeadingDistanceAccumulator] = set()
        self._distance_accumulators_lock = threading.Lock()

        self._velocity_estimator: VelocityEstimator = AlwaysZeroVelocityEstimator()
        rotary_encoders_snapshot_subscription = self.feed.subscribe_if_published(
            topic=Topics.ROTARY_ENCODERS_SNAPSHOT.topic, msg_type=RotaryEncodersSnapshot
        )
        if rotary_encoders_snapshot_subscription is not None:
            self._velocity_estimator = RotaryEncoderVelocityEstimator(unity=unity)
        self.feed.publish_callback(
            topic=Topics.VELOCITY_MPH.topic, callback=self._velocity_estimator.get_latest_velocity
        )

        # zed x offset and angle
        self.feed.publish_callback(
            topic=Topics.DRIVE_CAM_MECH_CFG_BACK_LEFT.topic,
            callback=lambda: self._config.mechanical.drive_cam_config("back_left"),
        )
        self.feed.publish_callback(
            topic=Topics.DRIVE_CAM_MECH_CFG_BACK_RIGHT.topic,
            callback=lambda: self._config.mechanical.drive_cam_config("back_right"),
        )
        self.feed.publish_callback(
            topic=Topics.DRIVE_CAM_MECH_CFG_FRONT_LEFT.topic,
            callback=lambda: self._config.mechanical.drive_cam_config("front_left"),
        )
        self.feed.publish_callback(
            topic=Topics.DRIVE_CAM_MECH_CFG_FRONT_RIGHT.topic,
            callback=lambda: self._config.mechanical.drive_cam_config("front_right"),
        )

        self._ticking_async_tasks: List[Tuple[str, asyncio.Task[Any]]] = []

        # fuel
        self.feed.publish_callback(topic=self.topic.sub("fuel_lvl"), callback=lambda: self.fuel_lvl)

    def _publish_body_subscription(self) -> None:
        lla_subscription = self.feed.subscribe_if_published(
            topic=Topics.GEOPOSITION_LLA.topic, msg_type=GeopositionLatLonAltMessage
        )
        if lla_subscription is None:
            return

        self._robot_body_channel = RobotBodyChannel(
            topic=Topics.BODY_POSITION.topic,
            heading_subscription=self.feed.subscribe(self._heading_estimator.topic, HeadingMessage),
            lla_subscription=lla_subscription,
            mechanical_config=self.config.mechanical,
            force_field_distance_in=lambda: self.parameters[FrameParameter.FORCE_FIELD_DISTANCE_IN].float_value,
        )
        self._latest_robot_body: Optional[RobotBody] = None
        self.feed.publish_callback(topic=self._robot_body_channel.topic, callback=self.robot_body)

        if len(self._rotary_encoders) > 0:
            self.feed.publish_callback(
                topic=self.topic.sub("rotary_encoder_ticks"), callback=self.rotary_encoders_snapshot
            )

            self.feed.publish_callback(
                topic=self.topic.sub("heading_distance"), callback=self.heading_distance_accumulator
            )

            self._gps_spaced_history = GPSSpacedHistory(
                topic=self.topic.sub("gps_spaced_history"),
                rotary_encoders_snapshot_subscription=self.feed.subscribe(
                    topic=Topic("/frame/rotary_encoder_ticks"), msg_type=RotaryEncodersSnapshot
                ),
                robot_body_subscription=self.feed.subscribe(topic=Topic("/frame/body"), msg_type=RobotBody),
            )
            self.feed.publish_callback(
                topic=self.topic.sub("gps_spaced_history"), callback=self._gps_spaced_history.get_ecef_projection,
            )

    @property
    def config(self) -> FrameConfiguration:
        return self._config

    @property
    def parameters(self) -> FrameParameters:
        return self.config.parameters

    @property
    def parameters_json(self) -> Dict[str, Dict[str, ParameterValueType]]:
        return {k: v.to_json() for k, v in self.parameters.items()}

    def save_parameter(self, parameter: str) -> None:
        self.config.save_parameter(parameter)

    def set_parameters(self, **kwargs: float) -> None:
        assert len(kwargs) > 0, "No parameters passed"
        for k, v in kwargs.items():
            if k not in self.parameters:
                LOG.warning(f"Attempt to set unknown parameter: {k} -> {v}")
            else:
                previous_value = self.parameters[k].value
                LOG.info(f"Update: {k}: {previous_value} -> {v}")
                self.parameters[k].set_value(v)

    def _build_rotary_encoder_stats(self) -> None:
        # build out the rotary encoder tpr and diameter configs
        self._rotary_encoders_stats: Dict[str, RotaryEncoderStats] = {}
        for instance, encoder in self._rotary_encoders.items():
            diameter: float = 0
            diameter_key = "diameter_" + instance + "_inches"
            try:
                diameter = self.config.mechanical["wheel_specs"][diameter_key]
            except Exception:
                pass
            self._rotary_encoders_stats[instance] = RotaryEncoderStats(encoder.TPR, diameter)

    def dump_callback(self) -> Dict[str, Any]:
        return {"status": self.status_callback(), "parameters": self.parameters_json}

    def status_callback(self) -> Dict[str, Any]:
        result: Dict[str, Any] = {}

        # heading
        heading_message: Optional[HeadingMessage] = self.feed.subscribe(
            self._heading_estimator.topic, HeadingMessage
        ).read()
        result["heading"] = heading_message.heading if heading_message is not None else None

        # mag
        if self.magnetometer is not None:
            result["mag"] = self.magnetometer.mag.to_json()

        # position
        body_subscription = self.feed.subscribe_if_published(topic=Topic("/frame/body"), msg_type=RobotBody)
        if body_subscription is not None:
            body: Optional[RobotBody] = body_subscription.read()
            if body is not None:
                result["antenna_lla"] = [body.antenna_lla.lat, body.antenna_lla.lon, body.antenna_lla.alt]
                result["geoposition_lla"] = [
                    body.wheel_centroid_lla.lat,
                    body.wheel_centroid_lla.lon,
                    body.wheel_centroid_lla.alt,
                ]
                result["force_field_lla"] = [[ll.lat, ll.lon, ll.alt] for ll in body.force_field]
                result["vertices_lla"] = [[ll.lat, ll.lon, ll.alt] for ll in body.frame_vertices_lat_lons]
                result["wheels_lla"] = [[ll.lat, ll.lon, ll.alt] for ll in body.wheels_lat_lons]

        # INS velocity
        if self._ins is not None:
            result["ins/velocity"] = self._ins.velocity.to_json()

        # authoritative velocity
        velocity_message = self.feed.subscribe(topic=Topics.VELOCITY_MPH.topic, msg_type=VelocityMessage).read()
        assert velocity_message is not None
        result["velocity_mph"] = velocity_message.to_json()

        return result

    def robot_body(self) -> Optional[RobotBody]:
        return self._latest_robot_body

    @property
    def magnetometer(self) -> Optional[Magnetometer]:
        return self._magnetometer

    @property
    def rotary_encoders(self) -> Dict[str, RotaryEncoderNode]:
        return self._rotary_encoders

    def _cache_named_references(self) -> None:

        # GPS
        self._gps: Optional[GPSNode] = cast(Optional[GPSNode], self.get_node(NodeType.GPS))

        # INS
        self._ins: Optional[INSNode] = cast(Optional[INSNode], self.get_node(NodeType.INS))

        # IMU
        self._imu: Optional[IMUNode] = cast(Optional[IMUNode], self.get_node(NodeType.IMU))

        # Magnetometer
        self._magnetometer: Optional[Magnetometer] = None
        if self._ins is not None and isinstance(self._ins, Magnetometer):
            self._magnetometer = self._ins
        elif self._imu is not None and isinstance(self._imu, Magnetometer):
            self._magnetometer = self._imu

        # Get rotary encoders - grep_subscriptions can return a single instance or a dict :(
        self._rotary_encoders: Dict[str, RotaryEncoderNode] = {}
        encoders_subs = self.grep_subscriptions("/driver/drive_system/rotary_encoder")
        if isinstance(encoders_subs, ClassicNode):
            self._rotary_encoders = cast(
                Dict[str, RotaryEncoderNode], {encoders_subs.device_path.instance_str, encoders_subs}
            )
        elif encoders_subs is not None:
            self._rotary_encoders = cast(
                Dict[str, RotaryEncoderNode], {k.instance_str: v for (k, v) in encoders_subs.items()},
            )

        # Fuel Sensor
        self._fuel_sensor: Optional[FuelSensorNode] = cast(
            Optional[FuelSensorNode], self.get_node(NodeType.FUEL_SENSOR)
        )
        self.fuel_lvl: float = 0

    def _publish_sensor_channels(self,) -> None:
        mag_subscription: Optional[SubscriberChannel[MagMessage]] = None
        geoposition_ecef_subscription: Optional[SubscriberChannel[GeopositionEcefMessage]] = None
        geoposition_lla_subscription: Optional[SubscriberChannel[GeopositionLatLonAltMessage]] = None
        velocity_subscription: Optional[SubscriberChannel[VelocityMessage]] = None

        if self._ins is not None:
            ins: INSNode = self._ins

            if mag_subscription is None and isinstance(ins, Magnetometer):
                magnetometer: Magnetometer = ins
                mag_subscription = CallbackSubscriberChannel(
                    self.topic.sub(FrameTopic.MAGNETOMETER), lambda: magnetometer.mag
                )

            geoposition_ecef_subscription = CallbackSubscriberChannel(
                self.topic.sub(FrameTopic.GEOPOSITION_ECEF), lambda: ins.geoposition_ecef_m
            )
            geoposition_lla_subscription = CallbackSubscriberChannel(
                self.topic.sub(FrameTopic.GEOPOSITION_LLA), lambda: ins.geoposition_lat_lon_alt
            )
            velocity_subscription = CallbackSubscriberChannel(
                self.topic.sub(FrameTopic.VELOCITY_ECEF), lambda: ins.velocity
            )

        if self._gps is not None:
            gps: GPSNode = self._gps

            geoposition_lla_subscription = CallbackSubscriberChannel(
                self.topic.sub("/geoposition_lla"), lambda: gps.geoposition_lat_lon_alt
            )
            geoposition_ecef_subscription = CallbackSubscriberChannel(
                self.topic.sub("/geoposition_ecef"), lambda: gps.geoposition_ecef_m
            )

        if mag_subscription is not None:
            self.feed.publish(topic=mag_subscription.topic, subscription=mag_subscription)
        if geoposition_ecef_subscription is not None:
            self.feed.publish(topic=geoposition_ecef_subscription.topic, subscription=geoposition_ecef_subscription)
        if geoposition_lla_subscription is not None:
            self.feed.publish(topic=geoposition_lla_subscription.topic, subscription=geoposition_lla_subscription)
        if velocity_subscription is not None:
            self.feed.publish(topic=velocity_subscription.topic, subscription=velocity_subscription)

    def _control_loop(self) -> None:
        """
        Control loop just kicks off an asyncio loop instead of busy looping in a thread.
        """
        # Frame
        asyncio.run_coroutine_threadsafe(self._update_loop(), get_event_loop_by_name()).result()
        wait_on_event_loop_forever()
        assert False, "Update Loop Should Not Exit"

    async def _tick_hdas(self) -> None:
        lock_acquisition_timeout_ms = 1000

        @contextmanager
        def acquire_timeout(lock: threading.Lock, timeout_ms: int) -> Generator[bool, None, None]:
            result = lock.acquire(timeout=timeout_ms * 1000)
            yield result
            if result:
                lock.release()

        # Heading Distance Accumulators
        # TODO it might be nice to build the HDAs based on the heading/velocity estimators as input primitives
        with acquire_timeout(self._distance_accumulators_lock, 1000) as acquired:
            if not acquired:
                raise TimeoutError(f"Could not acquire hda lock after {lock_acquisition_timeout_ms}ms")
            for hda in self._distance_accumulators:
                await hda.tick()

    async def _tick_gps(self) -> None:
        # GPS
        if self._gps is not None:
            await self._gps.tick()

    async def _tick_robot_body(self) -> None:
        self._latest_robot_body = self._robot_body_channel.read()

    async def _tick_gps_spaced_history(self) -> None:
        # GPS
        if self._gps_spaced_history is not None:
            await self._gps_spaced_history.tick()

    async def _tick_fuel_lvl(self) -> None:
        # Fuel Lvl
        if self._fuel_sensor is not None:
            self.fuel_lvl = await self._fuel_sensor.level

    @staticmethod
    async def _ticked_task_wrapper(
        interval_ms: int, tick_task_timeout: float, coroutine: Callable[[], Awaitable[None]], name: str
    ) -> None:
        while True:
            start_time_ms = maka_control_timestamp_ms()

            try:
                await asyncio.wait_for(coroutine(), timeout=tick_task_timeout)
            except asyncio.TimeoutError:
                LOG.exception(f"Timeout awaiting coroutine: {name}!")

            duration_ms = maka_control_timestamp_ms() - start_time_ms
            sleep_time_ms = max(interval_ms - duration_ms, 0)
            await async_sleep_ms(sleep_time_ms)

    async def _update_loop(self) -> None:
        """
        Control loop's async cousin.
        """
        # Runs at 4hz (match the driver)
        default_sleep_ms = 250

        # GPS only updates at 1hz so poll at twice that
        gps_sleep_ms = 500

        self._ticking_async_tasks = [
            (
                "velocity_estimator",
                asyncio.get_event_loop().create_task(
                    self._ticked_task_wrapper(
                        default_sleep_ms,
                        self._tick_task_timeout,
                        self._velocity_estimator.tick,
                        name="velocity_estimator",
                    )
                ),
            ),
            (
                "heading_estimator",
                asyncio.get_event_loop().create_task(
                    self._ticked_task_wrapper(
                        gps_sleep_ms, self._tick_task_timeout, self._heading_estimator.tick, name="heading_estimator",
                    )
                ),
            ),
            # (
            #     "hdas",
            #     asyncio.get_event_loop().create_task(
            #         self._ticked_task_wrapper(default_sleep_ms, self._tick_task_timeout, self._tick_hdas, name="hdas")
            #     ),
            # ),
            (
                "gps",
                asyncio.get_event_loop().create_task(
                    self._ticked_task_wrapper(gps_sleep_ms, self._tick_task_timeout, self._tick_gps, name="gps")
                ),
            ),
            (
                "robot_body",
                asyncio.get_event_loop().create_task(
                    self._ticked_task_wrapper(
                        gps_sleep_ms, self._tick_task_timeout, self._tick_robot_body, name="robot_body"
                    )
                ),
            ),
            (
                "gps_spaced_history",
                asyncio.get_event_loop().create_task(
                    self._ticked_task_wrapper(
                        gps_sleep_ms, self._tick_task_timeout, self._tick_gps_spaced_history, name="gps_spaced_history",
                    )
                ),
            ),
            (
                "fuel_sensor",
                # NanoPB boards on always on their own event loop XXX This needs to be fixed
                get_event_loop_by_name().create_task(
                    self._ticked_task_wrapper(
                        int(1000 / FUEL_SAMPLE_HZ), self._tick_task_timeout, self._tick_fuel_lvl, name="fuel_lvl",
                    )
                ),
            ),
            (
                "rotary_encoders",
                get_event_loop_by_name().create_task(
                    self._ticked_task_wrapper(
                        default_sleep_ms, self._tick_task_timeout, self._tick_rotary_encoders, name="rotary_encoders",
                    )
                ),
            ),
        ]

    def stop(self) -> None:
        super().stop()
        for name, t in self._ticking_async_tasks:
            LOG.info(f"Cancelling async task: {name}")
            t.cancel()

    #
    # Heading Distance Accumulators
    #
    def heading_distance_accumulator(self) -> HeadingDistanceAccumulator:
        """
        Hand out heading distance accumulator objects to clients who need to track heading distances.
        """

        # on close to remove from the ticks list
        def on_close(hda: HeadingDistanceAccumulator) -> None:
            with self._distance_accumulators_lock:
                self._distance_accumulators.remove(hda)

        # heading distance accumulator needs heading and inches information
        hda = HeadingDistanceAccumulator(
            heading_subscription=self.feed.subscribe(self._heading_estimator.topic, HeadingMessage),
            rotary_encoders_snapshot_subscription=self.feed.subscribe(
                topic=Topic("/frame/rotary_encoder_ticks"), msg_type=RotaryEncodersSnapshot
            ),
            on_close=on_close,
        )

        # add to the ticks list
        self._distance_accumulators.add(hda)
        return hda

    #
    # Rotary Encoders Snapshots
    #
    def rotary_encoders_snapshot(self) -> RotaryEncodersSnapshot:
        return self._latest_rotary_encoder_snapshot

    async def _tick_rotary_encoders(self) -> None:
        wheel_ticks: List[int] = await asyncio.gather(*[x.ticks for x in self.rotary_encoders.values()])
        wheel_instances: List[Optional[str]] = [x for x in self.rotary_encoders.keys()]

        instance_ticks: Dict[str, int] = {}
        for instance, ticks in zip(wheel_instances, wheel_ticks):
            if instance is not None:
                instance_ticks[instance] = ticks
        back_left = self.rotary_encoders.get(RelativeInstance.BACK_LEFT)
        self._latest_rotary_encoder_snapshot = RotaryEncodersSnapshot(
            timestamp_ms=back_left.timestamp_ms if back_left is not None else maka_control_timestamp_ms(),
            ticks_front_left=instance_ticks.get(RelativeInstance.FRONT_LEFT) or None,
            ticks_front_right=instance_ticks.get(RelativeInstance.FRONT_RIGHT) or None,
            ticks_back_left=instance_ticks.get(RelativeInstance.BACK_LEFT) or None,
            ticks_back_right=instance_ticks.get(RelativeInstance.BACK_RIGHT) or None,
            rotary_encoders_stats=self._rotary_encoders_stats,
        )

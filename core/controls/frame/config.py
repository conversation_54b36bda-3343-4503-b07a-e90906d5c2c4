from typing import Any, Dict

import yaml

from core.fs.defaults import CONFIG_DIR
from core.model.node.type import NodeType
from lib.common.config_file import ConfigFile

_FRAME = str(NodeType.FRAME).lower()


class FrameConfig(Dict[str, Any]):
    pass


class FrameConfigFile(ConfigFile):
    FILENAME = f"{_FRAME}.yaml"

    def __init__(self, config_dir: str = CONFIG_DIR, filename: str = FILENAME) -> None:
        super().__init__(
            config_dir=config_dir,
            filename=filename,
            serialize=yaml.dump,
            deserialize=yaml.safe_load,
            root_element=_FRAME,
        )

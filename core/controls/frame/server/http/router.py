from typing import Any, Dict

from core.controls.frame.frame import Frame, FrameParameter
from core.model.node.type import NodeType
from core.model.parameter import ParameterValueType
from lib.common.logging import get_logger
from lib.common.web.http.router import ComponentRouter<PERSON>uilder, RouterBuilder
from lib.common.web.http.router_utils import merge_add, optional

LOG = get_logger(__name__)


class FrameRouterBuilder(ComponentRouterBuilder):
    def __init__(self, frame: Frame):
        super().__init__(base_route=f"/{NodeType.FRAME.serialize()}")
        self._frame: Frame = frame

    def _handle_set_parameters(self, bot: Any, **param_to_values: float) -> None:
        assert len(param_to_values) > 0, "No parameters passed"
        self._frame.set_parameters(**{k: v for k, v in param_to_values.items() if v is not None})

    def _handle_get_parameters(self, bot: Any) -> Dict[str, Dict[str, ParameterValueType]]:
        return self._frame.parameters_json

    def _handle_save_parameter(self, bot: Any, parameter: str) -> None:
        self._frame.save_parameter(parameter)

    def add_onto(self, bot_routes: RouterBuilder) -> None:
        bot_routes.add("GET", self.suburl("/parameters"))(merge_add(self._handle_get_parameters))
        params_body_desc = {str(k): optional(float) for k in FrameParameter}
        bot_routes.add("POST", self.suburl("/parameter"), body_desc=params_body_desc)(
            merge_add(self._handle_set_parameters)
        )
        bot_routes.add("POST", self.suburl("/parameter/save"), body_desc={"parameter": str})(
            merge_add(self._handle_save_parameter)
        )

import math
from typing import TYPE_CHECKING, Callable, List, Optional

import lib.common.logging
from core.controls.frame.rotary_encoders_snapshot import RotaryEncodersSnapshot
from generated.core.controls.frame.model.heading_message import HeadingMessage
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.time.time import maka_control_timestamp_ms

if TYPE_CHECKING:
    from core.controls.driver.plan.subscription_types import RotaryEncodersSnapshotSubscription

LOG = lib.common.logging.get_logger(__name__)


class HeadingDistanceSample:
    def __init__(self, timestamp_ms: int, heading: float, encoder_snapshot: RotaryEncodersSnapshot):
        self.timestamp_ms = timestamp_ms
        self.heading = heading
        self.encoder_snapshot = encoder_snapshot


class HeadingDistanceAccumulator:
    def __init__(
        self,
        heading_subscription: SubscriberChannel[HeadingMessage],
        rotary_encoders_snapshot_subscription: "RotaryEncodersSnapshotSubscription",
        on_close: Optional[Callable[["HeadingDistanceAccumulator"], None]] = None,
    ):
        self._heading_subscription = heading_subscription
        self._rotary_encoders_snapshot_subscription = rotary_encoders_snapshot_subscription
        self._samples: List[HeadingDistanceSample] = []

        self._on_close = on_close

    def heading_distance_inches(self, heading: float, expect_at_least_one_sample: bool = False) -> float:
        # Distance added up and returned
        distance: float = 0

        # Accumulators for tracking current - last
        first: bool = True
        last_normalized_heading: float = 0
        last_encoder_snapshot: Optional[RotaryEncodersSnapshot] = None

        if expect_at_least_one_sample and len(self._samples) == 0:
            LOG.warning("Cannot compute heading_distance_inches: no samples!")

        # Go through all the sample points
        for sample in self._samples:

            # Normalize the heading so that everything is requested heading relative
            normalized_heading = sample.heading - heading
            if normalized_heading > 180:
                normalized_heading = normalized_heading - 360
            if normalized_heading < -180:
                normalized_heading = 360 + normalized_heading
            assert abs(normalized_heading) <= 180

            if first:
                # Skip the first one because it is base to compare against
                first = False
            else:
                # Average the last two headings together - if one of them is on the opposite side of zero
                # then we need to average + 360
                normalized_heading_same = (normalized_heading + last_normalized_heading) / 2
                normalized_heading_opposite = (normalized_heading + last_normalized_heading + 360) / 2
                if abs(normalized_heading - normalized_heading_same) < abs(
                    normalized_heading - normalized_heading_opposite
                ):
                    normalized_heading = normalized_heading_same
                else:
                    normalized_heading = normalized_heading_opposite
                # Convert to radians
                angle = (normalized_heading / 180) * math.pi
                # Distance to add is the amount in the direction of the heading we are about
                assert last_encoder_snapshot
                distance += math.cos(angle) * (sample.encoder_snapshot.inches_since(last_encoder_snapshot))
            last_encoder_snapshot = sample.encoder_snapshot
            last_normalized_heading = normalized_heading

        return distance

    async def tick(self) -> None:
        now = maka_control_timestamp_ms()

        heading: Optional[HeadingMessage] = self._heading_subscription.read()
        if heading is None:
            LOG.warning("Cannot tick HDA because heading None")
            return

        assert self._rotary_encoders_snapshot_subscription
        encoder_snapshot = self._rotary_encoders_snapshot_subscription.read()

        if encoder_snapshot is None:
            LOG.warning("Cannot tick HDA because encoder_snapshot None")
            return

        self._samples.append(HeadingDistanceSample(now, heading.heading, encoder_snapshot))

    def close(self) -> None:
        """
        You MUST call close on these when done.
        """
        if self._on_close is not None:
            self._on_close(self)

from abc import ABC

from core.controls.frame.sensor.accelerometer.base import Accelerometer
from core.controls.frame.sensor.gyroscope.base import Gyroscope
from core.controls.frame.sensor.magnetometer.base import Magnetometer


class InertialMeasurementUnit(Accelerometer, Gyroscope, ABC):
    """
    Refer README
    """

    pass


class MagnetometerIMU(InertialMeasurementUnit, Magnetometer, ABC):
    """
    Refer README - not all IMUs have a magnetometer.
    """

    pass

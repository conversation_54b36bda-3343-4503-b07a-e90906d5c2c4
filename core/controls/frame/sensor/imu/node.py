import abc
from typing import Any, Dict, <PERSON><PERSON>

import numpy.typing as npt

from core.controls.frame.sensor.imu.base import InertialMeasurementUnit
from core.model.sensor import Sensor

SENSOR_SAMPLE_SHAPE = Sensor.DEFAULT_SENSOR_DIMENSIONS


def _measurement_to_named_coords(t: npt.NDArray[Any]) -> Dict[str, float]:
    assert t.shape == SENSOR_SAMPLE_SHAPE
    return {"x": float(t[0]), "y": float(t[1]), "z": float(t[2])}


class IMUNode(InertialMeasurementUnit, Sensor, abc.ABC):
    """
    From wikipedia:
        An inertial measurement unit (IMU) is an electronic device that measures and reports a body's specific force,
        angular rate, and sometimes the orientation of the body, using a combination of accelerometers, gyroscopes, and
        sometimes magnetometers.
    """

    def status_callback(self) -> Dict[str, Any]:
        timestamp_ms, accel, gyro = self.accel_gyro
        return {
            "timestamp_ms": timestamp_ms,
            "accelerometer_mps2": _measurement_to_named_coords(accel),
            "gyroscope_dps": _measurement_to_named_coords(gyro),
        }

    # TODO do we need this if we already have acceleration() and gyroscope() separately?
    @property
    @abc.abstractmethod
    def accel_gyro(self) -> Tuple[int, npt.NDArray[Any], npt.NDArray[Any]]:
        pass

    @property
    def aligned_accel_gyro(self) -> Tuple[int, npt.NDArray[Any], npt.NDArray[Any]]:
        (timestamp_ms, accel, gyro) = self.accel_gyro
        assert accel.shape == SENSOR_SAMPLE_SHAPE
        assert gyro.shape == SENSOR_SAMPLE_SHAPE

        if self._rotation is not None:
            assert self._rotation.shape == (Sensor.N_DIMS, Sensor.N_DIMS)
            aligned_accel = self._rotation @ accel
            aligned_gyro = self._rotation @ gyro
            return timestamp_ms, aligned_accel, aligned_gyro
        else:
            return timestamp_ms, accel, gyro

from typing import List, Optional, <PERSON><PERSON>

import numpy as np

from core.controls.driver.plan.subscription_types import RobotBodySubscription, RotaryEncodersSnapshotSubscription
from core.controls.frame.rotary_encoders_snapshot import RotaryEncodersSnapshot
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from lib.common.geo.ecef_utils import EcefFieldGridConvert, EcefVector
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import Topic

LOG = get_logger(__name__)


class GPSSpace:
    def __init__(self, ecef: GeopositionEcefMessage, re_snapshot: RotaryEncodersSnapshot):
        self._ecef = ecef
        self._re_snapshot = re_snapshot


MIN_DISTANCE_INCHES = 6  # Capture one snapshot every 1/2 feet
MAX_HISTORY = 100  # Save 100 snapshots (50 feet)


class GPSSpacedHistory:
    def __init__(
        self,
        topic: Topic,
        rotary_encoders_snapshot_subscription: RotaryEncodersSnapshotSubscription,
        robot_body_subscription: RobotBodySubscription,
        projection_multiplier: float = 10.0,
    ):
        self._topic = topic
        self._rotary_encoders_snapshot_subscription = rotary_encoders_snapshot_subscription
        self._robot_body_subscription = robot_body_subscription
        self._projection_multiplier = projection_multiplier
        self._last_snapshot: Optional[RotaryEncodersSnapshot] = None
        self._history: List[GPSSpace] = []

    async def tick(self) -> None:
        # Get rotary encoders snapshot
        if self._rotary_encoders_snapshot_subscription is None:
            return
        re_snapshot = self._rotary_encoders_snapshot_subscription.read()
        if re_snapshot is None:
            return

        # Get geo position ecef
        if self._robot_body_subscription is None:
            return
        robot_body = self._robot_body_subscription.read()
        if robot_body is None:
            return
        ecef = robot_body.wheel_centroid_ecef
        if ecef.x == 0 and ecef.y == 0 and ecef.z == 0:
            return

        # If we've move far enough since the last one then keep this one
        if self._last_snapshot is None or re_snapshot.inches_since(self._last_snapshot) >= MIN_DISTANCE_INCHES:
            gspace = GPSSpace(ecef, re_snapshot)
            self._history.append(gspace)
            self._last_snapshot = re_snapshot
            if len(self._history) > MAX_HISTORY:
                self._history = self._history[1:]

    def _regress_ecef(self) -> Tuple[Tuple[float, float], Tuple[float, float], Tuple[float, float]]:
        """
        Get y = ax + b for x, y, z ecef.
        """
        dist: List[float] = []
        x: List[float] = []
        y: List[float] = []
        z: List[float] = []

        # Get our distances and points lined up
        for at in range(0, len(self._history)):
            if at > 0:
                # Get the distance since the last one
                dist_diff = self._history[at]._re_snapshot.inches_since(self._history[at - 1]._re_snapshot)
                # These are cummulative, so add this to the last distance
                dist.append(dist_diff + dist[at - 1])
            else:
                dist.append(0.0)  # first distance is the start, so 0
            x.append(self._history[at]._ecef.x)
            y.append(self._history[at]._ecef.y)
            z.append(self._history[at]._ecef.z)

        M = np.vstack([dist, np.ones(len(dist))]).T
        ax, bx = np.linalg.lstsq(M, x, rcond=None)[0]
        ay, by = np.linalg.lstsq(M, y, rcond=None)[0]
        az, bz = np.linalg.lstsq(M, z, rcond=None)[0]
        return ((ax, bx), (ay, by), (az, bz))

    def get_ecef_projection(self) -> EcefVector:

        LOG.info("Getting gps spaced history for {} samples".format(len(self._history)))

        if len(self._history) < 2:
            return EcefVector.empty()

        last = self._history[-1]
        point = np.array([last._ecef.x, last._ecef.y, last._ecef.z])
        field_convert = EcefFieldGridConvert.from_surface_normal(point)
        """
        Get y = ax + b for x, y, z ecef.
        """
        dist: List[float] = []
        x: List[float] = []
        y: List[float] = []

        # Get our distances and points lined up
        for at in range(0, len(self._history)):
            if at > 0:
                # Get the distance since the last one
                dist_diff = self._history[at]._re_snapshot.inches_since(self._history[at - 1]._re_snapshot)
                # These are cummulative, so add this to the last distance
                dist.append(dist_diff + dist[at - 1])
            else:
                dist.append(0.0)  # first distance is the start, so 0

            point = np.array([self._history[at]._ecef.x, self._history[at]._ecef.y, self._history[at]._ecef.z])
            point = field_convert.point_convert(point)
            x.append(point[0])
            y.append(point[1])

        M = np.vstack([dist, np.ones(len(dist))]).T
        ax, bx = np.linalg.lstsq(M, x, rcond=None)[0]
        ay, by = np.linalg.lstsq(M, y, rcond=None)[0]

        run_dist = self._history[-1]._re_snapshot.inches_since(self._history[0]._re_snapshot)

        # The start is the zero point on the "horizontal"
        x1 = bx
        y1 = by

        # The end is the dist on the "horizontal"
        x2 = run_dist * self._projection_multiplier * ax + bx
        y2 = run_dist * self._projection_multiplier * ay + by

        return EcefVector(
            start=field_convert.point_revert(np.array([x1, y1]), field_alt=0.4),
            end=field_convert.point_revert(np.array([x2, y2]), field_alt=0.4),
        )

    def dump_stats(self) -> None:
        last: Optional[GPSSpace] = None

        for gspace in self._history:
            dist: float = 0.0
            if last is not None:
                dist = gspace._re_snapshot.inches_since(last._re_snapshot)
            last = gspace
            LOG.info("{}, {}, {}, {}".format(dist, gspace._ecef.x, gspace._ecef.y, gspace._ecef.z))

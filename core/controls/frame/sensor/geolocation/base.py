from abc import ABC, abstractmethod

import navpy
import numpy as np

from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage


class GeographicLocationReceiver(ABC):
    """
    Refer README.

    This is the full generalization of "where am I", encompassing systems like GPS/GNSS,
    and coordinate systems like latitude/longitude, ECEF, etc.
    """

    pass

    # TODO type ECEF as a generated class
    @property
    @abstractmethod
    def geoposition_ecef_m(self) -> GeopositionEcefMessage:
        """
        Retrieve the x/y/z position in the ECEF coordinate system, in meters.

        Returns:
            A tuple with time and a 3-by numpy array of ECEF X, Y, Z.
        """
        pass

    # TODO type lat/lon/alt as a generated class
    @property
    def geoposition_lat_lon_alt(self) -> GeopositionLatLonAltMessage:
        """
        Retrieve the position in the latitude/longitude/altitude frame.

        Returns:
            A tuple with time and a 3-by numpy array of ECEF X, Y, Z.
        """
        geoposition_ecef_m_msg: GeopositionEcefMessage = self.geoposition_ecef_m
        as_array = np.array([geoposition_ecef_m_msg.x, geoposition_ecef_m_msg.y, geoposition_ecef_m_msg.z])
        is_zero = geoposition_ecef_m_msg.x == 0 and geoposition_ecef_m_msg.y == 0 and geoposition_ecef_m_msg.z == 0
        lla = navpy.ecef2lla(as_array, "deg") if not is_zero else [0, 0, 0]
        return GeopositionLatLonAltMessage(
            timestamp_ms=geoposition_ecef_m_msg.timestamp_ms, lat=lla[0], lon=lla[1], alt=lla[2]
        )

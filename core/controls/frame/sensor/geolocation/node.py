from abc import ABC, abstractmethod
from typing import Optional

import navpy

from core.controls.frame.pose.heading.base import HeadingEstimator
from core.controls.frame.sensor.geolocation.base import GeographicLocationReceiver
from core.model.sensor import Sensor
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from lib.common.protocol.channel.base import Topic


class GPSNode(GeographicLocationReceiver, Sensor, ABC):
    @property
    @abstractmethod
    def geoposition_lat_lon_alt(self) -> GeopositionLatLonAltMessage:
        pass

    @property
    def geoposition_ecef_m(self) -> GeopositionEcefMessage:
        lla = self.geoposition_lat_lon_alt
        ecef = navpy.lla2ecef(lla.lat, lla.lon, lla.alt, "deg")
        return GeopositionEcefMessage(timestamp_ms=lla.timestamp_ms, x=ecef[0], y=ecef[1], z=ecef[2])

    def get_heading_estimator(self, topic: Topic) -> Optional[HeadingEstimator]:
        return None

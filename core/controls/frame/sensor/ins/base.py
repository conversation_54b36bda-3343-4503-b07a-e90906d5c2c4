from abc import ABC

from core.controls.frame.sensor.accelerometer.base import Accelerometer
from core.controls.frame.sensor.geolocation.base import GeographicLocationReceiver
from core.controls.frame.sensor.gyroscope.base import Gyroscope
from core.controls.frame.sensor.magnetometer.base import Magnetometer
from core.controls.frame.sensor.velocimeter.base import Velocimeter


class InertialNavigationSystem(Accelerometer, GeographicLocationReceiver, Gyroscope, Velocimeter, ABC):
    """
    Refer README
    """

    pass


class MagnetometerINS(InertialNavigationSystem, Magnetometer, ABC):
    """
    Refer README - not all INSs have a magnetometer.
    """

    pass

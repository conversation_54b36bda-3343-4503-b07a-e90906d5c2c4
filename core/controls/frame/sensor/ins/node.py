from abc import ABC
from typing import Any

from core.controls.frame.model.topics import FrameTopic
from core.controls.frame.sensor.ins.base import MagnetometerINS
from core.model.sensor import Sensor
from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from generated.core.controls.frame.model.mag_message import MagMessage
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.time import maka_control_timestamp_ms


class INSNode(MagnetometerINS, Sensor, ABC):
    """
    AN INS exposes the same interface as an IMU, and more, including (GNSS) position and velocity.

    From wikipedia:
        An inertial navigation system (INS) is a navigation device that uses a computer, motion sensors (accelerometers)
        and rotation sensors (gyroscopes) to continuously calculate by dead reckoning the position, the orientation, and
        the velocity (direction and speed of movement) of a moving object without the need for external references.
    """

    pass


class SubscriptionProxyINSNode(INSNode):
    """
    An INS node constructed as a thin proxy over a set of underlying subscriptions.

    Developed for use case of a shmem implementation that just reads along from shmem.
    """

    def __init__(self, *argv: Any, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        self._angular_velocity_subscription: SubscriberChannel[AngularVelocityMessage] = self.feed.subscribe(
            self.topic.sub(FrameTopic.ANGULAR_VELOCITY), AngularVelocityMessage
        )
        self._geoposition_ecef_subscription: SubscriberChannel[GeopositionEcefMessage] = self.feed.subscribe(
            self.topic.sub(FrameTopic.GEOPOSITION_ECEF), GeopositionEcefMessage
        )
        self._geoposition_lla_subscription: SubscriberChannel[GeopositionLatLonAltMessage] = self.feed.subscribe(
            self.topic.sub(FrameTopic.GEOPOSITION_LLA), GeopositionLatLonAltMessage
        )
        self._mag_subscription: SubscriberChannel[MagMessage] = self.feed.subscribe(
            self.topic.sub(FrameTopic.MAGNETOMETER), MagMessage
        )
        self._velocity_subscription: SubscriberChannel[VelocityMessage] = self.feed.subscribe(
            self.topic.sub(FrameTopic.VELOCITY_ECEF), VelocityMessage
        )

    @property
    def angular_velocity(self) -> AngularVelocityMessage:
        result = self._angular_velocity_subscription.read()
        if result is None:
            return AngularVelocityMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

        return result

    @property
    def geoposition_ecef_m(self) -> GeopositionEcefMessage:
        result = self._geoposition_ecef_subscription.read()
        if result is None:
            return GeopositionEcefMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

        return result

    @property
    def geoposition_lat_lon_alt(self) -> GeopositionLatLonAltMessage:
        result = self._geoposition_lla_subscription.read()
        if result is None:
            return GeopositionLatLonAltMessage(timestamp_ms=maka_control_timestamp_ms(), lat=0, lon=0, alt=0)

        return result

    @property
    def mag(self) -> MagMessage:
        result = self._mag_subscription.read()
        if result is None:
            return MagMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

        return result

    @property
    def velocity(self) -> VelocityMessage:
        result = self._velocity_subscription.read()
        if result is None:
            return VelocityMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

        return result

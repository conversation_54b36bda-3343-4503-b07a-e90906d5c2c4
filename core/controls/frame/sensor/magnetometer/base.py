from abc import ABC, abstractmethod

from generated.core.controls.frame.model.mag_message import MagMessage


class Magnetometer(ABC):
    """
    Base magnetometer interface.
    """

    # TODO output a generated type (this is inconsistent with the "MagMessage" typing type we have elsewhere)
    @property
    @abstractmethod
    def mag(self) -> MagMessage:
        """
        Report the magnetic field this magnetometer is detecting, in microTeslas.

        Returns:
            timestamp_ms, [mag_x_uT, mag_y_uT, mag_z_uT].T
            Timestamp is in milliseconds from unix epoch, magnetic measurements in microTeslas.
        """
        pass

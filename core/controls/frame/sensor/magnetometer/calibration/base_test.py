import sys
from typing import Any, List, Optional, Union, cast

import numpy as np
import numpy.typing as npt
import pytest

from core.controls.frame.sensor.magnetometer.calibration.base import CalibratedMagnetometer
from core.controls.frame.sensor.magnetometer.calibration.calibration import (
    MagneticCalibration,
    MagneticCalibrationLimits,
)
from core.model.sensor import Sensor
from generated.core.controls.frame.model.mag_message import MagMessage
from lib.common.recipes import eval_2d_matrix_strs
from lib.common.time import maka_control_timestamp_ms

FP_EPSILON = sys.float_info.epsilon


class FakeCalibratedMagnetometer(CalibratedMagnetometer):
    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self._sample = np.zeros(Sensor.DEFAULT_SENSOR_DIMENSIONS)

    @property
    def mag(self) -> MagMessage:
        """
        Gets the most recent sample, with a default timestamp of 0.

        Returns:
            A tuple of time (always 0 in this case) and the last set sample.
        """
        return MagMessage(
            timestamp_ms=maka_control_timestamp_ms(), x=self._sample[0], y=self._sample[1], z=self._sample[2]
        )

    def set_sample(self, sample: npt.NDArray[Any]) -> None:
        """
        Sets the sample to use in subsequent calls to mag and related functions.
        Note that this function intentionally does not check for the dimensions of the sample
        argument, so that we can test correct handling of downstream components that should also
        be checking for correct dimensions.

        Parameters:
            sample: The sample to set for subsequent magnetometer samples.
        """
        self._sample = sample


def magnetometer_factory(
    calibration: Optional[MagneticCalibration] = None, rotation: Optional[npt.NDArray[Any]] = None
) -> FakeCalibratedMagnetometer:
    """
    Build a test magnetometer where the test case can control the values that the magnetometer returns.

    Parameters:
        calibration: The optional calibration with which to instantiate the test magnetometer. A
                     default calibration will be used if unspecified.
        rotation: The optional rotation matrix to apply to the magnetometer readings. A default
                  rotation matrix will be used if unspecified.

    Returns:
        An instance of a TestMagnetometer.
    """

    # We pass in arguments by name here, which mypy doesn't like because it wants a dict instead.
    return FakeCalibratedMagnetometer(magnetic_calibration=calibration, rotation=rotation)


def test_magnetometer_init() -> None:
    """
    Test that we can boot our test magnetometer, and that all the basic properties are correct.
    """
    mag = magnetometer_factory()
    assert mag is not None
    assert mag.calibration is not None

    x = 16
    y = 32
    z = 64
    test_sample = np.array([x, y, z]).reshape(3, 1)
    mag.set_sample(test_sample)
    assert mag.mag.x == x
    assert mag.mag.y == y
    assert mag.mag.z == z

    # With a default calibration and alignment, the aligned magnetometer and calibrated magnetometer
    # should produce the same result as the raw sample.
    assert np.array_equal(mag.aligned_mag[1], test_sample), f"{mag.aligned_mag[1]} vs {test_sample}"
    assert np.array_equal(mag.calibrated_mag[1], test_sample), f"{mag.calibrated_mag[1]} vs {test_sample}"


def test_magnetometer_update_bad_sample_shape() -> None:
    mag = magnetometer_factory()
    # various bad shapes
    with pytest.raises(AssertionError, match="shape"):
        wrong_shape_sample = np.array([[1, 2, 3], [4, 5, 6], [7, 8, 9]]).reshape(3, 3)
        mag.calibration.add_sample(wrong_shape_sample)
    with pytest.raises(AssertionError, match="shape"):
        wrong_shape_sample = np.array([[1, 2]]).reshape(2, 1)
        mag.calibration.add_sample(wrong_shape_sample)
    with pytest.raises(AssertionError, match="shape"):
        wrong_shape_sample = np.array([[1, 2, 3, 4]]).reshape(4, 1)
        mag.calibration.add_sample(wrong_shape_sample)
    with pytest.raises(AssertionError, match="shape"):
        wrong_shape_sample = np.array([[1, 2, 3]]).reshape(1, 3)
        mag.calibration.add_sample(wrong_shape_sample)


def test_calibration_calculation() -> None:
    """
    Test the logic of applying calibrations to the raw sensor data.
    """

    # All bounds are the same, easy case.
    cal = MagneticCalibration(
        limits=MagneticCalibrationLimits(x_min=-100, x_max=100, y_min=-100, y_max=100, z_min=-100, z_max=100),
        mean_magnitude=100,
        n_samples=100,
    )
    mag = magnetometer_factory(calibration=cal)
    assert mag.calibration == cal
    test_cases = [
        (np.array([100, 100, 100]).reshape(3, 1), np.ones(Sensor.DEFAULT_SENSOR_DIMENSIONS)),
        (np.array([-100, -100, -100]).reshape(3, 1), -1 * np.ones(Sensor.DEFAULT_SENSOR_DIMENSIONS)),
        (np.array([0, 0, 0]).reshape(3, 1), np.zeros(Sensor.DEFAULT_SENSOR_DIMENSIONS)),
        # Note that for float point reasons, the test values here are chosen such that their
        # calibrated values can be expressed without a loss of precision as sums of powers of 2.
        # This just makes it easier to use the array_equal function for testing correctness.
        (np.array([25, -50, 75]).reshape(3, 1), np.array([0.25, -0.5, 0.75]).reshape(3, 1)),
    ]
    for test_case in test_cases:
        mag.set_sample(test_case[0])
        assert mag.calibrated_mag[1].shape == test_case[1].shape
        assert np.array_equal(mag.calibrated_mag[1], test_case[1])

    # This tests how we clip samples and whether it updates the calibration limits in the process or
    # not.
    cal = MagneticCalibration(
        limits=MagneticCalibrationLimits(x_min=-50, x_max=100, y_min=-100, y_max=50, z_min=-25, z_max=25),
        mean_magnitude=(50 + 100 + 100 + 50 + 25 + 25) / 6,
        n_samples=100,
    )
    mag = magnetometer_factory(calibration=cal)
    test_cases = [
        # Clipping the y and z measurements
        (np.array([100, 100, 100]).reshape(3, 1), np.ones(Sensor.DEFAULT_SENSOR_DIMENSIONS)),
        # Clipping the x and z measurements
        (np.array([-100, -100, -100]).reshape(3, 1), -1 * np.ones(Sensor.DEFAULT_SENSOR_DIMENSIONS)),
        # Check that the midpoints haven't moved
        (np.array([25, -25, 0]).reshape(3, 1), np.zeros(Sensor.DEFAULT_SENSOR_DIMENSIONS)),
        # Max readings for all the limits as originally defined.
        (np.array([100, 50, 25]).reshape(3, 1), np.ones(Sensor.DEFAULT_SENSOR_DIMENSIONS)),
    ]
    for test_case in test_cases:
        mag.set_sample(test_case[0])
        assert mag.calibrated_mag[1].shape == test_case[1].shape
        assert np.array_equal(mag.calibrated_mag[1], test_case[1])

    # Test loading the calibrations from a dictionary, as would be loaded from a JSON config file.
    limits = {"x": {"min": -10, "max": 10}, "y": {"min": -20, "max": 20}, "z": {"min": -30, "max": 30}}
    mag_calibration = MagneticCalibrationLimits.from_json(limits)
    assert mag_calibration.x_min == -10
    assert mag_calibration.x_max == 10
    assert mag_calibration.y_min == -20
    assert mag_calibration.y_max == 20
    assert mag_calibration.z_min == -30
    assert mag_calibration.z_max == 30


def test_rotations() -> None:
    """
    Test the rotation matrix logic works correctly.
    """
    rotation = np.array([[0, 1, 0], [1, 0, 0], [0, 0, -1]]).reshape(3, 3)
    mag = magnetometer_factory(rotation=rotation)
    test_sample = np.array([25, 50, 75]).reshape(3, 1)
    mag.set_sample(test_sample)
    assert np.array_equal(mag.aligned_mag[1], np.array([50, 25, -75]).reshape(3, 1))

    # Now test that a calibrated magnetometer will produce its calibrated readings in a rotated
    # space.
    cal = MagneticCalibration(
        limits=MagneticCalibrationLimits(x_min=-100, x_max=100, y_min=-100, y_max=100, z_min=-100, z_max=100),
        mean_magnitude=100,
        n_samples=100,
    )
    mag = magnetometer_factory(calibration=cal, rotation=rotation)
    mag.set_sample(test_sample)
    assert np.array_equal(mag.calibrated_mag[1], np.array([0.5, 0.25, -0.75]).reshape(3, 1))

    # Test that parsing a rotation as a collection of string-like objects works. The constructor
    # should parse simple mathematical expressions in the rotation matrix and produce a usable
    # rotation matrix.
    rotation_str: List[List[Union[int, float, str]]] = [
        ["cos(0)", "-sin(0)", "0"],
        ["sin(0)", "cos(0)", "0"],
        ["0.123", "0.456", "cos(pi / 3)"],
    ]
    rotation = eval_2d_matrix_strs(rotation_str)
    mag = magnetometer_factory(rotation=rotation)
    expected_rotation = np.array([[1, -0.0, 0], [0, 1, 0], [0.123, 0.456, 0.5]]).reshape(3, 3)
    # Don't use the array_equal here because of small floating point imprecision issues. cos(pi / 3)
    # produces 0.5 + some epsilon
    for i, row in enumerate(cast(List[List[float]], mag._rotation)):
        for j, cell in enumerate(row):
            assert np.abs(cell - expected_rotation[i][j]) < FP_EPSILON


def test_calibration_updates() -> None:
    """
    Test the logic of updating the magnetic calibration with new samples.
    """

    # With no calibration set, we just expect the same values out as given in.
    cal = MagneticCalibration()
    assert cal.mean_magnitude == 0
    test_sample = np.array([25, 50, 75]).reshape(3, 1)
    assert np.array_equal(cal.apply(test_sample), test_sample)

    # Check the behavior with only one test sample.
    cal.add_sample(test_sample)
    assert np.array_equal(cal.apply(test_sample), -1 * np.ones(Sensor.DEFAULT_SENSOR_DIMENSIONS))
    assert cal.mean_magnitude == 0

    # Test the behavior when a negated sample is added. The original test_sample should map to all
    # 1s, and the negated one should map to all -1s. Since the midpoint between them should be all
    # 0s, we'd expect that an input all 0s produces an output all zeros.
    cal.add_sample(-1 * test_sample)
    assert np.array_equal(cal.apply(test_sample), np.ones(Sensor.DEFAULT_SENSOR_DIMENSIONS))
    assert np.array_equal(cal.apply(-1 * test_sample), -1 * np.ones(Sensor.DEFAULT_SENSOR_DIMENSIONS))
    assert np.array_equal(
        cal.apply(np.zeros(Sensor.DEFAULT_SENSOR_DIMENSIONS)), np.zeros(Sensor.DEFAULT_SENSOR_DIMENSIONS)
    )
    assert cal.mean_magnitude == np.linalg.norm(test_sample)

    # Now add 2 more measurements that should set the scale to +-100 on all axes, and verify that
    # the test sample is mapped correctly.
    cal.add_sample(100 * np.ones(Sensor.DEFAULT_SENSOR_DIMENSIONS))
    cal.add_sample(-100 * np.ones(Sensor.DEFAULT_SENSOR_DIMENSIONS))
    assert np.array_equal(cal.apply(test_sample), np.array([0.25, 0.5, 0.75]).reshape(3, 1))
    assert np.array_equal(cal.apply(-1 * test_sample), -1 * np.array([0.25, 0.5, 0.75]).reshape(3, 1))

import json
from typing import Any, Dict, Optional

import numpy as np
import numpy.typing as npt
from numpy.linalg import norm

from core.controls.frame.sensor.magnetometer.constants import MAGNETOMETER_SAMPLE_SHAPE
from lib.common.logging import get_logger
from lib.common.math import scale_value
from lib.common.serialization.json import JsonSerializable

LOG = get_logger(__name__)


_INDEX_012_TO_XYZ = {0: "x", 1: "y", 2: "z"}


class MagneticCalibrationLimits(Dict[str, Dict[str, Optional[float]]], JsonSerializable):
    def __init__(
        self,
        x_min: Optional[float] = None,
        x_max: Optional[float] = None,
        y_min: Optional[float] = None,
        y_max: Optional[float] = None,
        z_min: Optional[float] = None,
        z_max: Optional[float] = None,
    ):
        super().__init__(
            {"x": {"min": x_min, "max": x_max}, "y": {"min": y_min, "max": y_max}, "z": {"min": z_min, "max": z_max}}
        )

    @property
    def x_min(self) -> Optional[float]:
        return self["x"]["min"]

    @property
    def x_max(self) -> Optional[float]:
        return self["x"]["max"]

    @property
    def y_min(self) -> Optional[float]:
        return self["y"]["min"]

    @property
    def y_max(self) -> Optional[float]:
        return self["y"]["max"]

    @property
    def z_min(self) -> Optional[float]:
        return self["z"]["min"]

    @property
    def z_max(self) -> Optional[float]:
        return self["z"]["max"]

    def to_json(self) -> Dict[str, Any]:
        return self

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "MagneticCalibrationLimits":
        x: Dict[str, float] = data["x"]
        y: Dict[str, float] = data["y"]
        z: Dict[str, float] = data["z"]
        return MagneticCalibrationLimits(
            x_min=x["min"], x_max=x["max"], y_min=y["min"], y_max=y["max"], z_min=z["min"], z_max=z["max"],
        )


class MagneticCalibration(JsonSerializable):
    """
    Manage a magnetometer's calibrations and applies the calibration to samples.
    """

    def __init__(
        self,
        limits: Optional[MagneticCalibrationLimits] = None,
        mean_magnitude: Optional[float] = None,
        n_samples: int = 0,
    ):
        assert n_samples >= 0
        # validate input parameters are consistent. If any know about at least one sample, they all must
        if n_samples > 0 or (limits is not None and limits.x_min is not None) or mean_magnitude is not None:
            assert n_samples > 0
            assert limits is not None
            assert mean_magnitude is not None
            assert limits.x_min is not None
            assert limits.x_max is not None
            assert limits.y_min is not None
            assert limits.y_max is not None
            assert limits.z_min is not None
            assert limits.z_max is not None

        self._axes_limits: MagneticCalibrationLimits = limits if limits is not None else MagneticCalibrationLimits()
        self._n_samples: int = n_samples
        self._mean_magnitude: float = mean_magnitude if mean_magnitude is not None else 0

    @property
    def axes_limits(self) -> MagneticCalibrationLimits:
        return self._axes_limits

    def __str__(self) -> str:
        return json.dumps(self.to_json())

    def to_json(self) -> Dict[str, Any]:
        return {"limits": self._axes_limits, "mean_magnitude": self._mean_magnitude, "n_samples": self._n_samples}

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "MagneticCalibration":
        limits = data["limits"]
        mean_magnitude: float = data["mean_magnitude"]
        n_samples: int = data["n_samples"]
        return MagneticCalibration(
            limits=MagneticCalibrationLimits.from_json(limits), mean_magnitude=mean_magnitude, n_samples=n_samples
        )

    def add_sample(self, mag_sample: npt.NDArray[Any]) -> None:
        """
        Adds a sample to the calibration process.

        Parameters:
            mag_sample (np.ndarray): The sample to add. Expected to be a 3x1 measurement vector.
        """
        assert (
            mag_sample.shape == MAGNETOMETER_SAMPLE_SHAPE
        ), f"Wrong shape. Expected: {MAGNETOMETER_SAMPLE_SHAPE}. Got: {mag_sample.shape}"
        if self.calibration_valid:
            self._n_samples += 1
        normalized_measurements = np.zeros(MAGNETOMETER_SAMPLE_SHAPE)
        for index, (limits, measurement_array) in enumerate(zip(self._axes_limits.values(), mag_sample)):
            # unpack measurement value
            assert measurement_array.shape == (1,)
            # Note: val be a numpy type and not a float but will still be float-like
            measurement_val: float = measurement_array[0]

            # update limits (this effects self._axes_limits)
            self._update_limits(_INDEX_012_TO_XYZ[index], limits, measurement_val)
            if limits["min"] is not None and limits["max"] is not None:
                limit_midpoint: float = (limits["min"] + limits["max"]) / 2.0
                normalized_measurements[index] = measurement_val - limit_midpoint
            else:
                normalized_measurements[index] = 0

        measurement_magnitude = norm(normalized_measurements)
        if self._n_samples > 0:
            # Update the average using the formula:
            # mean[n] = mean[n-1] * (n-1)/(n) + sample / (n)
            # where n is the number of samples.
            # This works because mean[t-1] = (sum of n - 1 samples) / (n - 1)
            # so then mean[n-1] * (n - 1) == sum of n - 1 samples
            # Then we can factor out the common denominator of n:
            # mean[n] = ((sum of n-1 samples) + sample) / (n)
            #         = (sum of n samples) / (n)
            self._mean_magnitude = (
                self._mean_magnitude * float(self._n_samples - 1) / float(self._n_samples)
                + measurement_magnitude / self._n_samples
            ).item()

    def apply(self, sample: npt.NDArray[Any]) -> npt.NDArray[Any]:
        """
        Apply the magnetic calibration to a sample.

        Parameters:
            sample (np.ndarray): The sample to apply the current calibration to.

        Returns:
            A calibrated sample.
        """
        calibrated_sample = np.zeros(MAGNETOMETER_SAMPLE_SHAPE)
        for index, (limits, measurement) in enumerate(zip(self.axes_limits.values(), sample)):
            if limits["min"] is not None and limits["max"] is not None:
                calibrated_sample[index] = scale_value(measurement, limits["min"], limits["max"], -1, 1)
            else:
                calibrated_sample[index] = measurement

        return calibrated_sample

    @property
    def mean_magnitude(self) -> float:
        """
        Report the average magnitude of all the measurements that were used in the calibration.
        """
        return self._mean_magnitude

    @property
    def calibration_valid(self) -> bool:
        for limits in self.axes_limits.values():
            for limit in limits.values():
                if limit is None:
                    return False
        return True

    def _update_limits(self, field_name: str, limits: Dict[str, Optional[float]], measurement: float) -> None:
        """
        Update the limits of an axis using the current axis measurement.

        Parameters:
            limits (dict): The current limits that apply to the measurement axis.
            measurement (float): The most recent measurement, that may be used to update the limits.
        """
        assert not isinstance(
            measurement, np.ndarray
        ), f"Unexpected array: {measurement} ({type(measurement)}). Expected a number type."
        existing_min = limits["min"]
        if existing_min is None or measurement < existing_min:
            LOG.debug(f"Updated min {field_name} from {existing_min} to {measurement}")
            limits["min"] = measurement
        existing_max = limits["max"]
        if existing_max is None or measurement > existing_max:
            LOG.debug(f"Updated {field_name} from {existing_max} to {measurement}")
            limits["max"] = measurement

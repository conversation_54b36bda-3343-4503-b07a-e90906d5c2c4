import abc
import functools
from concurrent.futures import Future
from typing import Any, Callable, Optional, Tuple

import numpy as np
import numpy.typing as npt

import lib.common.math
from core.controls.frame.sensor.magnetometer.base import Magnetometer
from core.controls.frame.sensor.magnetometer.calibration.calibration import MagneticCalibration
from core.controls.frame.sensor.magnetometer.constants import MAGNETOMETER_SAMPLE_SHAPE
from core.model.sensor import Sensor
from generated.core.controls.frame.model.mag_message import MagMessage
from lib.common.logging import get_logger
from lib.common.tasks import MakaTask, get_current

LOG = get_logger(__name__)


class CalibratedMagnetometer(Magnetometer, abc.ABC):
    """
    A calibrated magnetometer.
    """

    def __init__(
        self, rotation: Optional[npt.NDArray[Any]], magnetic_calibration: Optional[MagneticCalibration] = None,
    ):
        self._rotation = rotation

        self._magnetic_calibration: MagneticCalibration = magnetic_calibration if magnetic_calibration is not None else MagneticCalibration()

        self._mag_sample_task: Optional[MakaTask] = None

    @property
    def aligned_mag(self) -> Tuple[int, npt.NDArray[Any]]:
        unaligned_mag_message: MagMessage = self.mag
        mag_sample = np.array([unaligned_mag_message.x, unaligned_mag_message.y, unaligned_mag_message.z]).reshape(
            MAGNETOMETER_SAMPLE_SHAPE
        )

        if self._rotation is not None:
            assert self._rotation.shape == (Sensor.N_DIMS, Sensor.N_DIMS)
            aligned_mag = self._rotation @ mag_sample
            return unaligned_mag_message.timestamp_ms, aligned_mag
        else:
            return unaligned_mag_message.timestamp_ms, mag_sample

    @property
    def calibrated_mag(self) -> Tuple[int, npt.NDArray[Any]]:
        timestamp_ms, mag_sample = self.aligned_mag
        return timestamp_ms, self._magnetic_calibration.apply(mag_sample)

    @property
    def calibration(self) -> MagneticCalibration:
        return self._magnetic_calibration

    #
    # Magnetometer Calibration
    #
    def toggle_sample_magnetometer(self) -> None:
        """
        Gather our magnetometer samples for calibration. We assume there is only one imu in frame right now (because
        currently there is) but eventually this should take some identifier for which mag we're calibrating).
        """
        if self._mag_sample_task is not None:
            self._mag_sample_task.cancel()
            LOG.debug("Disabled Magnetometer sampling.")
            return

        calibration = MagneticCalibration()
        self._mag_sample_task = lib.common.tasks.start(
            "Calibrate Magnetometer", functools.partial(self._magnetometer_sample_task, calibration)
        )

        def unset(f: Future[Callable[[], Any]]) -> None:
            LOG.info("=== mag sample task done, calibration ===\n{}".format(calibration))
            if self._mag_sample_task is not None:
                self._mag_sample_task = None

        self._mag_sample_task.add_done_callback(unset)

    def _magnetometer_sample_task(self, calibration: MagneticCalibration) -> None:
        LOG.debug("Starting Magnetometer sampling...")
        task: Optional[MakaTask] = get_current()
        assert task is not None
        while True:
            task.tick()
            timestamp_ms, mag_sample = self.aligned_mag
            calibration.add_sample(mag_sample)

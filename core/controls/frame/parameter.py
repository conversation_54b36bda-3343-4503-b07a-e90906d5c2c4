from enum import Enum
from typing import Dict

from core.controls.frame.constants import (
    DEFAULT_FORCE_FIELD_IN,
    DEFAULT_VELOCITY_ESTIMATOR_MOVING_AVG_SMOOTH_FACTOR,
    DEFAULT_VELOCITY_ESTIMATOR_SNAP_ZERO_PRECISION,
)
from core.model.parameter import FloatParameter, IntParameter, Parameter
from core.unity.constants import (
    DEFAULT_UNITY_GPS_OUTAGE_FREQ_MS,
    DEFAULT_UNITY_GPS_OUTAGE_LENGTH_MS,
    DEFAULT_UNITY_HEADING_STD_DEV,
)

FrameParameters = Dict[str, Parameter]


class FrameParameter(str, Enum):
    FORCE_FIELD_DISTANCE_IN = "force_field_distance_in"

    VELOCITY_ESTIMATOR_MOVING_AVG_SMOOTH_FACTOR = "velocity_estimator_moving_avg_smooth_factor"
    VELOCITY_ESTIMATOR_SNAP_ZERO_PRECISION = "velocity_estimator_snap_zero_precision"

    UNITY_GPS_OUTAGE_FREQ_MS = "unity_gps_outage_freq_ms"
    UNITY_GPS_OUTAGE_LENGTH_MS = "unity_gps_outage_length_ms"
    UNITY_HEADING_STD_DEV = "unity_heading_std_dev"

    def __str__(self) -> str:
        return str(self.value)

    @property
    def cmdline_arg(self) -> str:
        return f"--{self.replace('_', '-')}"


DEFAULT_FRAME_PARAMETERS: FrameParameters = {
    FrameParameter.FORCE_FIELD_DISTANCE_IN: FloatParameter(
        name=FrameParameter.FORCE_FIELD_DISTANCE_IN, value=DEFAULT_FORCE_FIELD_IN, min=0, max=1000000, step=1,
    ),
    FrameParameter.VELOCITY_ESTIMATOR_SNAP_ZERO_PRECISION: FloatParameter(
        name=FrameParameter.VELOCITY_ESTIMATOR_SNAP_ZERO_PRECISION,
        value=DEFAULT_VELOCITY_ESTIMATOR_SNAP_ZERO_PRECISION,
        min=0,
        max=1,
        step=0.001,
    ),
    FrameParameter.VELOCITY_ESTIMATOR_MOVING_AVG_SMOOTH_FACTOR: FloatParameter(
        name=FrameParameter.VELOCITY_ESTIMATOR_MOVING_AVG_SMOOTH_FACTOR,
        value=DEFAULT_VELOCITY_ESTIMATOR_MOVING_AVG_SMOOTH_FACTOR,
        min=0,
        max=1,
        step=0.01,
    ),
}


DEFAULT_FRAME_PARAMETERS_UNITY: FrameParameters = {
    FrameParameter.UNITY_GPS_OUTAGE_FREQ_MS: IntParameter(
        name=FrameParameter.UNITY_GPS_OUTAGE_FREQ_MS,
        value=DEFAULT_UNITY_GPS_OUTAGE_FREQ_MS,
        min=0,
        max=1000000,
        step=1000,
    ),
    FrameParameter.UNITY_GPS_OUTAGE_LENGTH_MS: IntParameter(
        name=FrameParameter.UNITY_GPS_OUTAGE_LENGTH_MS,
        value=DEFAULT_UNITY_GPS_OUTAGE_LENGTH_MS,
        min=0,
        max=1000000,
        step=100,
    ),
    FrameParameter.UNITY_HEADING_STD_DEV: FloatParameter(
        name=FrameParameter.UNITY_HEADING_STD_DEV, value=DEFAULT_UNITY_HEADING_STD_DEV, min=0, max=90, step=0.01,
    ),
}
for k in FrameParameter:
    p = DEFAULT_FRAME_PARAMETERS.get(k, DEFAULT_FRAME_PARAMETERS_UNITY.get(k))
    assert p is not None, f"Missing default parameter for: {k}"
    assert p.name == k

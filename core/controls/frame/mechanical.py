from typing import Any, Dict, List, Tuple, cast

import yaml

from core.fs.defaults import CONFIG_DIR
from lib.common.config_file import ConfigFile


class DriveCamMechanicalConfig(Dict[str, Any]):
    @property
    def x_offset_px(self) -> int:
        return int(self.get("x_offset_px", 0))

    @property
    def angle_offset_deg(self) -> float:
        return float(self.get("angle_offset_deg", 0.0))


class MechanicalConfig(Dict[str, Any]):
    @property
    def gps_antenna_offset_in(self) -> Tuple[float, float]:
        return cast(Tuple[float, float], self.get("gps_antenna", []))

    @property
    def track_width_in(self) -> float:
        val = self.get("wheel_specs", {}).get("track_width_in", 0)
        assert isinstance(val, (float, int))
        return val

    @property
    def wheelbase_length_in(self) -> float:
        val = self.get("wheel_specs", {}).get("wheelbase_length_in", 0)
        assert isinstance(val, (float, int))
        return val

    @property
    def frame_vertices_in(self) -> List[Tuple[float, float]]:
        return cast(List[Tuple[float, float]], self.get("frame", {}).get("vertices", []))

    def drive_cam_config(self, instance: str) -> DriveCamMechanicalConfig:
        return DriveCamMechanicalConfig(self.get("drive_cam_specs", {}).get(instance, {}))


class MechanicalConfigFile(ConfigFile):
    FILENAME = "mechanical.yaml"

    def __init__(self, config_dir: str = CONFIG_DIR, filename: str = FILENAME) -> None:
        super().__init__(
            config_dir=config_dir,
            filename=filename,
            serialize=yaml.dump,
            deserialize=yaml.safe_load,
            root_element="mechanical",
        )

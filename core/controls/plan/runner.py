import threading
from typing import <PERSON><PERSON>, <PERSON><PERSON>

from core.controls.plan.instruction import <PERSON><PERSON><PERSON><PERSON><PERSON>ruction, PlanInstruction, TerminateInstruction
from core.controls.plan.ops.op import <PERSON>, OpTick, OpTickGen
from core.controls.plan.ops.prog import OpRoutine
from core.controls.plan.state.execution_state import Plan<PERSON><PERSON>cutionState
from core.controls.plan.state.info import PlanInfo
from core.controls.plan.state.run_data import RunData
from lib.common.logging import get_logger
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)


class OpRunner:
    """
    This is the top level class the interfaces between the procedural api and the generator api of the
    events.
    """

    def __init__(self) -> None:
        # The main routine
        self._subroutine: Optional[OpRoutine] = None
        # the full routine, including any on-completes
        self._routine: Optional[OpRoutine] = None
        self._lock: threading.Lock = threading.Lock()
        self._iter: Optional[OpTickGen] = None
        self._data: Optional[RunData] = None
        self._outstanding_terminate_instruction: Optional[TerminateInstruction] = None

        self._paused = False
        self._cancelled = False
        self._completed = False

    def _reset(self) -> None:
        self._subroutine = None
        self._routine = None
        self._iter = None
        self._data = None

    @property
    def program_name(self) -> str:
        return self._subroutine.__class__.__name__ if self._subroutine is not None else "unset"

    @property
    def paused(self) -> bool:
        return self._paused

    @property
    def active(self) -> bool:
        return self._routine is not None

    def pause(self) -> None:
        self._paused = True

    def unpause(self) -> None:
        self._paused = False

    @property
    def execution_state(self) -> PlanExecutionState:
        if self._completed:
            return PlanExecutionState.COMPLETED
        elif self.active:
            if self.paused:
                return PlanExecutionState.PAUSED
            else:
                return PlanExecutionState.RUNNING
        elif self._cancelled:
            return PlanExecutionState.CANCELLED
        else:
            return PlanExecutionState.UNSET

    # This tick only: return None == end of program
    def _tick(self) -> Tuple[Optional[PlanInfo], Optional[PlanInstruction]]:
        assert self.execution_state in [PlanExecutionState.PAUSED, PlanExecutionState.RUNNING]

        if self.execution_state == PlanExecutionState.PAUSED:
            return None, NoOpInstruction()

        tick_time_ms = maka_control_timestamp_ms()
        with self._lock:
            if self._routine is None:
                ret = self._outstanding_terminate_instruction
                self._outstanding_terminate_instruction = None
                return None, ret
            try:
                # grab and cache the iterator
                if self._iter is None:
                    self._data = RunData()
                    self._iter = self._routine.tick(self._data)
                op_tick: OpTick = next(self._iter)
            except StopIteration as e:
                LOG.exception(f"Not supposed to stop iterating: {type(self._routine)}")
                self._reset()
                return None, TerminateInstruction(reason=f"{type(e).__name__}({e})")
            except Exception as e:
                LOG.exception("Generic plan exception")
                self._reset()
                return None, TerminateInstruction(reason=f"{type(e).__name__}({e})")
            if op_tick.done:
                # Do not be tempted to call destroy here - you will deadlock with _lock
                self._outstanding_terminate_instruction = TerminateInstruction(reason="prog complete")
                self._reset()
                self._completed = True

            status_text = (
                op_tick.status_text if op_tick.status_text is not None else "|" + self._routine.__class__.__name__ + "|"
            )
            assert op_tick.status_text is not None

            # Convert event response into plan info
            plan_info: PlanInfo = PlanInfo(
                timestamp_ms=tick_time_ms,
                status_text=status_text,
                run_data=self._data.copy() if self._data is not None else RunData(),
                run_data_local=op_tick.local_data,
            )
            return plan_info, op_tick.instruction

    def _build(self, subroutine: OpRoutine, on_complete: Optional[Op] = None) -> None:
        with self._lock:
            # the primary routine
            self._subroutine = subroutine

            # the full routine to be executed, optionally including any sequence to do at the end
            ops = subroutine.ops.copy()
            if on_complete is not None:
                ops.append(on_complete)
            self._routine = OpRoutine(*ops)

            # reset plan execution state
            self.unpause()
            self._completed = False
            self._cancelled = False

    def destroy(self, reason: str = "") -> None:
        with self._lock:
            # nothing to destroy
            if not self.execution_state == PlanExecutionState.UNSET:
                self._cancelled = True
                self._outstanding_terminate_instruction = TerminateInstruction(reason=reason)
                self._reset()

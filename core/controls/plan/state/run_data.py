from typing import Any, Dict, Iterator, MutableMapping, Optional

from lib.common.serialization.json import JsonSerializable, JsonSerializableTypeUnion


class RunData(MutableMapping[str, JsonSerializableTypeUnion], JsonSerializable):
    def __init__(self, data: Optional[Dict[str, JsonSerializableTypeUnion]] = None):
        self._data: Dict[str, JsonSerializableTypeUnion] = data if data is not None else {}

    def __setitem__(self, k: str, v: JsonSerializableTypeUnion) -> None:
        self._data[k] = v

    def __delitem__(self, v: str) -> None:
        del self._data[v]

    def __getitem__(self, k: str) -> JsonSerializableTypeUnion:
        return self._data[k]

    def __len__(self) -> int:
        return len(self._data)

    def __iter__(self) -> Iterator[str]:
        return iter(self._data)

    def to_json(self) -> Dict[str, Any]:
        return {k: JsonSerializable.serialize(v) for k, v in self.items()}

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "RunData":
        raise NotImplementedError

    def copy(self) -> "RunData":
        return RunData(data=self._data.copy())

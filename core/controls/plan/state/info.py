from typing import Any, Dict, Optional

from core.controls.plan.state.run_data import RunData
from lib.common.logging import get_logger
from lib.common.serialization.json import JsonSerializable
from lib.common.time import Timestamp

LOG = get_logger(__name__)


class PlanInfo(JsonSerializable, Timestamp):
    """
    State of the plan.
    """

    def __init__(
        self,
        timestamp_ms: int,
        run_data: RunData,
        status_text: Optional[str],
        run_data_local: Optional[RunData] = None,
    ):
        self._timestamp_ms = timestamp_ms
        self._status_text: str = status_text if status_text is not None else "---"
        self._run_data: RunData = run_data
        self._run_data_local: RunData = run_data_local if run_data_local is not None else RunData()

    @property
    def run_data(self) -> RunData:
        return self._run_data

    @property
    def run_data_local(self) -> RunData:
        return self._run_data_local

    @property
    def status_text(self) -> str:
        return self._status_text

    @property
    def timestamp_ms(self) -> int:
        return self._timestamp_ms

    def to_json(self) -> Dict[str, Any]:
        return {
            "timestamp_ms": self.timestamp_ms,
            "status_text": self.status_text,
            "run_data": self.run_data.to_json(),
            "run_data_local": self.run_data_local.to_json(),
        }

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "PlanInfo":
        raise NotImplementedError

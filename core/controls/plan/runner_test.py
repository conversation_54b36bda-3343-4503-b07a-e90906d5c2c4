import uuid
from typing import Callable, Generator, Optional, cast

import pytest

from core.controls.plan.ops.condition import count_add, count_down
from core.controls.plan.ops.loop import loop
from core.controls.plan.ops.op import Done, Op, OpTickGen
from core.controls.plan.ops.prog import Op<PERSON>out<PERSON>
from core.controls.plan.runner import <PERSON><PERSON><PERSON><PERSON>
from core.controls.plan.state.run_data import RunData
from lib.common.logging import get_logger

LOG = get_logger(__name__)


pytestmark = pytest.mark.drive_plan_test


@pytest.fixture(scope="function")
def op_runner() -> Generator[OpRunner, None, None]:
    yield OpRunner()


class AccumOp(Op):
    """Increments one to data count each time. scope_update flag says weather it relearns data each time"""

    SLOTNAME = "count"

    def __init__(self, scope_update: bool = True):
        super().__init__()
        self._scope_update: bool = scope_update

    def tick(self, data: RunData) -> OpTickGen:
        count: int = 0
        while True:
            if AccumOp.SLOTNAME in data and self._scope_update:
                count = cast(int, data[AccumOp.SLOTNAME])
            count += 1
            data[AccumOp.SLOTNAME] = count
            LOG.info(f"AccumOp count is: {data[AccumOp.SLOTNAME]}")
            yield Done()
            if self._scope_update:
                count = cast(int, data[AccumOp.SLOTNAME])


class ValOpData(Op):
    """Returns whatever the last data passthrough was"""

    def __init__(self) -> None:
        super().__init__()
        self._val: Optional[RunData] = None

    @property
    def val(self) -> Optional[RunData]:
        return self._val

    def tick(self, data: RunData) -> OpTickGen:
        self._val = data
        while True:
            LOG.info(f"ValOpData yielding: {self._val}")
            yield Done()
            assert False


class ValOpInit(Op):
    """Returns whatever the initialized value was"""

    def __init__(self, val: int, count_slotname: str) -> None:
        super().__init__()
        self._val = val
        self._slotname = count_slotname

    def tick(self, data: RunData) -> OpTickGen:
        while True:
            data[self._slotname] = self._val
            yield Done()
            assert False


def tick_up_to(op_runner: OpRunner, *, n: int, done_condition: Callable[[], bool]) -> None:
    for i in range(n):
        op_runner._tick()
        if done_condition():
            return
    assert False, f"{op_runner.program_name}: Failed to achieve done condition after {n} ticks"


def test_plan_looping(op_runner: OpRunner) -> None:
    """
    Basic looping test
    """
    count = 3
    accum = AccumOp()
    sop = ValOpData()
    op_runner._build(OpRoutine(loop(count_down(count, log=True), accum), sop))
    tick_up_to(op_runner, n=50, done_condition=lambda: sop.val is not None)
    assert sop.val is not None
    assert sop.val[AccumOp.SLOTNAME] == count


def test_plan_looping_nested(op_runner: OpRunner) -> None:
    """
    Nested looping test
    """
    # Test that basic count passthrough works
    sop = ValOpData()
    op_runner._build(
        OpRoutine(
            loop(count_down(3, log=True), AccumOp()),
            loop(count_down(3, log=True), loop(count_down(2, log=True), AccumOp())),
            sop,
        )
    )
    tick_up_to(op_runner, n=50, done_condition=lambda: sop.val is not None)
    assert sop.val is not None
    assert sop.val[AccumOp.SLOTNAME] == 3 + 3 * 2


def test_plan_looping_nested_scoped_reset(op_runner: OpRunner) -> None:
    # Test that scoped reset works
    sop = ValOpData()
    op_runner._build(
        OpRoutine(loop(count_down(3, log=True), loop(count_down(2, log=True), AccumOp(scope_update=False))), sop,)
    )
    tick_up_to(op_runner, n=50, done_condition=lambda: sop.val is not None)
    assert sop.val is not None
    assert sop.val[AccumOp.SLOTNAME] == 1


def test_plan_looping_nested_scoped_accum(op_runner: OpRunner) -> None:
    # Test that scoped accum works
    sop = ValOpData()
    op_runner._build(
        OpRoutine(loop(count_down(3, log=True), loop(count_down(2, log=True), AccumOp(scope_update=True))), sop,)
    )
    tick_up_to(op_runner, n=50, done_condition=lambda: sop.val is not None)
    assert sop.val is not None
    assert sop.val[AccumOp.SLOTNAME] == 3 * 2


def test_plan_loop_initializer(op_runner: OpRunner) -> None:
    """
    Data passing looping test
    """
    slotname = str(uuid.uuid4())
    sop = ValOpData()
    op_runner._build(
        OpRoutine(
            ValOpInit(3, count_slotname=slotname),
            loop(count_add(5, count_slotname=slotname, log=True), AccumOp()),
            sop,
        )
    )

    tick_up_to(op_runner, n=50, done_condition=lambda: sop.val is not None)
    assert sop.val is not None
    assert sop.val.get(slotname) == 5 + 3, f"Bad val: {sop.val.get(slotname)}"

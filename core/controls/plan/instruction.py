from abc import ABC
from typing import Any, Dict

from lib.common.time import Timestamp, maka_control_timestamp_ms


class PlanInstruction(Timestamp, ABC):
    """
    Base Plan Instruction.

    Not coupled to any particular module or service. Just a generic construct.
    """

    def __init__(self, *, msg_type: str, msg: Dict[str, Any]):
        self._timestamp_ms = maka_control_timestamp_ms()
        self._msg_type: str = msg_type
        self._msg: Dict[str, Any] = msg

    @property
    def timestamp_ms(self) -> int:
        return self._timestamp_ms

    @property
    def msg_type(self) -> str:
        return self._msg_type

    @property
    def msg(self) -> Dict[str, Any]:
        return self._msg


class NoOpInstruction(PlanInstruction):
    def __init__(self) -> None:
        super().__init__(msg_type="no_op", msg={})


class TerminateInstruction(PlanInstruction):
    def __init__(self, reason: str) -> None:
        super().__init__(msg_type="terminate", msg={"reason": reason})
        self._reason = reason

    @property
    def reason(self) -> str:
        return self._reason

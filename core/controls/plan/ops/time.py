from core.controls.plan.ops.op import Again, Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunData
from lib.common.time import maka_control_timestamp_ms


class DelayMs(Op):
    def __init__(self, ms: int):
        super().__init__()
        self._ms: int = ms

    def tick(self, data: RunData) -> OpTickGen:
        start = maka_control_timestamp_ms()
        done = start + self._ms
        while maka_control_timestamp_ms() < done:
            yield Again()
        yield Done()


def delay_ms(ms: int) -> DelayMs:
    return DelayMs(ms)

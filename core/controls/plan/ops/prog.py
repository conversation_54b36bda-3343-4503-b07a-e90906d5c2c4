from typing import List, Optional

from core.controls.plan.ops.op import Again, NestedOpTick, Op, OpTick, OpTickGen
from core.controls.plan.state.run_data import RunData
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class GenNodes:
    """
    management of caching generators across the nodes
    """

    def __init__(self, *ops: Op) -> None:
        self._nodes: List[Op] = list(ops)
        self._index: int = 0
        # cache of generators for each node initialized to None
        self._gens: List[Optional[OpTickGen]] = [None for _ in self._nodes]

    @property
    def ops(self) -> List[Op]:
        return self._nodes

    def reset(self) -> None:
        self._index = 0
        self._gens = [None for _ in self._nodes]

    def current_gen(self, gen_func: OpTickGen) -> OpTickGen:
        gen = self._gens[self._index]
        if gen is None:
            gen = gen_func
            self._gens[self._index] = gen
        return gen

    def reset_current_gen(self) -> None:
        self._gens[self._index] = None


class OpRoutine(Op, GenNodes):
    """
    Basic single run event iteration
    """

    def __init__(self, *ops: Op):
        # set up nodes and initialize generator caching
        assert len(ops) > 0, "Must pass at least one Op"
        assert isinstance(ops[0], Op)
        GenNodes.__init__(self, *ops)

        # initialize basic eventing
        Op.__init__(self)

    @property
    def index(self) -> int:
        return self._index

    @property
    def current(self) -> Op:
        return self._nodes[self._index]

    def _nest(self, resp: OpTick) -> NestedOpTick:
        return NestedOpTick(resp, outer_status=f"{self.index + 1} {self.current.__class__.__name__}()")

    def tick(self, data: RunData) -> OpTickGen:
        try:
            while True:
                # Remember running generators per node - this get reset on restart
                gen = self.current_gen(self.current.tick(data))

                # Loop until this node is done
                while True:
                    LOG.debug(f"ProgOp{id(self)} ticking {type(self.current)}, index of {self._index}")
                    resp = next(gen)
                    assert isinstance(resp, OpTick)
                    if resp.done:
                        LOG.debug(f"ProgOp{id(self)} done from {type(self.current)}")
                        if self._index == (len(self._nodes) - 1):
                            LOG.debug(f"ProgOp{id(self)} last done from {type(self.current)}")
                            yield_result = self._nest(resp)
                            self.reset()  # out of scope
                            yield yield_result
                            LOG.warning(f"ProgOp{id(self)} tick after done: {type(self.current)}")
                            assert False  # should never be called again

                        # Prog is not done until nodes are done
                        self._index += 1
                        LOG.debug(f"ProgOp{id(self)} moving to next op {type(self.current)}, index of {self._index}")
                        yield self._nest(Again.wrap(resp))

                        # Done with this node need to get new generator and continue on
                        break
                    else:
                        # Not done so wait for next tick
                        LOG.debug(f"ProgOp{id(self)} not done from {type(self.current)}")
                        yield self._nest(resp)

        except StopIteration:
            LOG.warning(f"ProgOp{id(self)} not supposed to stop: {type(self.current)}")
            raise


def prog(*args: Op) -> OpRoutine:
    return OpRoutine(*args)

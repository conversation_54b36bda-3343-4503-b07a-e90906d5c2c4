from abc import ABC, abstractmethod
from typing import Any, Dict, Generator, Optional, Union, cast

from aenum import AutoNumberEnum

from core.controls.plan.instruction import PlanInstruction
from core.controls.plan.state.run_data import RunData


class OpTickType(AutoNumberEnum):
    AGAIN: "OpTickType" = cast("OpTickType", ())
    DONE: "OpTickType" = cast("OpTickType", ())


class OpTick(ABC):
    def __init__(
        self,
        # this parameter effects behavior
        instruction: Optional[PlanInstruction] = None,
        # these params are just for annotations
        local_data: Optional[Union[Dict[str, Any], RunData]] = None,
        status_text: Optional[str] = None,
    ):
        self._local_data: RunData = local_data if local_data is not None and isinstance(
            local_data, RunData
        ) else RunData(local_data) if local_data is not None else RunData()
        self._instruction = instruction
        self._status_text: Optional[str] = status_text

    @property
    @abstractmethod
    def state(self) -> OpTickType:
        pass

    @property
    def done(self) -> bool:
        return self.state == OpTickType.DONE

    @property
    def local_data(self) -> RunData:
        return self._local_data

    @property
    def instruction(self) -> Optional[PlanInstruction]:
        return self._instruction

    @property
    def status_text(self) -> Optional[str]:
        return self._status_text


OpTickGen = Generator[OpTick, None, None]


class NestedOpTick(OpTick):
    """
    A wrapped event, for recording in formation while propogating up through the yield chain
    """

    def __init__(self, other: OpTick, outer_status: str):
        # build nested status text
        delimiter = " > " if isinstance(other, NestedOpTick) else ": "
        status_suffix = f"{delimiter}{other.status_text}" if other.status_text is not None else ""
        status_text: str = f"{outer_status}{status_suffix}"
        super().__init__(
            instruction=other.instruction, local_data=other.local_data, status_text=status_text,
        )
        self._delegate: OpTick = other
        self._depth: int = 1 if not isinstance(other, NestedOpTick) else (other.depth + 1)

    @property
    def depth(self) -> int:
        return self._depth

    @property
    def state(self) -> OpTickType:
        return self._delegate.state


class Again(OpTick):
    @property
    def state(self) -> OpTickType:
        return OpTickType.AGAIN

    @classmethod
    def wrap(cls, other: OpTick) -> "Again":
        return Again(status_text=other.status_text, local_data=other.local_data, instruction=other.instruction)


class Done(OpTick):
    @property
    def state(self) -> OpTickType:
        return OpTickType.DONE


class BaseOp(ABC):
    """
    Base interface for all Ops
    """

    pass


class Op(BaseOp):
    """
    Generator version of state transitions
    """

    @abstractmethod
    def tick(self, data: RunData) -> OpTickGen:
        pass

from typing import List, Optional, Tu<PERSON>, cast

from core.controls.plan.ops.condition import ConditionGenType, ConditionOp
from core.controls.plan.ops.op import Done
from core.controls.plan.state.run_data import RunData


class And(ConditionOp):
    """
    Logical "and" condition node - short circuit validate all true
    """

    def __init__(self, *args: ConditionOp):
        super().__init__()
        self._conditions: Tuple[ConditionOp, ...] = args
        self._condition_gens: List[Optional[ConditionGenType]] = [None for _ in self._conditions]

    def condition(self, data: RunData) -> ConditionGenType:
        while True:
            for i, cond in enumerate(self._conditions):
                # Cache the generators for the conditions for scope
                if self._condition_gens[i] is None:
                    self._condition_gens[i] = cond.condition(data)
                gen: ConditionGenType = cast(ConditionGenType, self._condition_gens[i])
                assert gen is not None  # in case mypy cheat leads to bug

                # While the current cond says it's not done keep asking
                cont, evt = next(gen)
                while not evt.done:
                    cont, evt = next(gen)

                # Early exit from "and" if fail
                if not cont:
                    # Reset our cached generators
                    self._condition_gens = [None for _ in self._conditions]
                    yield cont, evt
                    break

            # Keep going and keep using the cached generators
            yield True, Done()


class Or(ConditionOp):
    """
    Logical "or" condition node - short circuit validate all true
    """

    def __init__(self, *args: ConditionOp):
        super().__init__()
        self._conditions: Tuple[ConditionOp, ...] = args
        self._condition_gens: List[Optional[ConditionGenType]] = [None for _ in self._conditions]

    def condition(self, data: RunData) -> ConditionGenType:
        while True:
            for i, cond in enumerate(self._conditions):
                # Cache the generators for the conditions for scope
                if self._condition_gens[i] is None:
                    self._condition_gens[i] = cond.condition(data)

                # While the current cond says it's not done keep asking
                cont, evt = next(self._condition_gens[i])  # type: ignore
                while not evt.done:
                    cont, evt = next(self._condition_gens[i])  # type: ignore

            # Keep going and keep using the cached generators
            yield True, Done()


def _and(*args: ConditionOp) -> And:
    return And(*args)


def _or(*args: ConditionOp) -> Or:
    return Or(*args)

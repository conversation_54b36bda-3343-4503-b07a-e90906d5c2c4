from core.controls.plan.ops.op import Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunData
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class PlanLog(Op):
    def __init__(self, string: str):
        self._string = string

    def tick(self, data: RunData) -> OpTickGen:
        while True:
            LOG.info(self._string)
            yield Done()


def plan_log(string: str) -> PlanLog:
    return PlanLog(string)

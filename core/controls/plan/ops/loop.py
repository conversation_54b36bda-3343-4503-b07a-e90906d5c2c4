from enum import Enum
from typing import Optional, Union

from core.controls.plan.ops.condition import ConditionGenType, ConditionOp
from core.controls.plan.ops.op import Again, NestedOpTick, Op, OpTick, OpTickGen
from core.controls.plan.ops.prog import OpRoutine
from core.controls.plan.state.run_data import RunData
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class InvalidLoopStateError(Exception):
    pass


class LoopState(Enum):
    INIT = 1
    RESET = 2
    CONDITION = 3
    BODY = 4


class Loop(OpRoutine):
    def __init__(self, *args: Union[Op, ConditionOp]):
        # set up nodes and initialize generator caching
        prog_args = args[1:]
        super().__init__(*prog_args)  # type: ignore

        # store the condition event and generator cache, first arg is always condition
        assert isinstance(args[0], ConditionOp)
        self._condition: ConditionOp = args[0]
        self._cond_gen: Optional[ConditionGenType] = None

        # this is used to help with _nest function
        self._state: LoopState = LoopState.INIT

    def reset(self) -> None:
        """
        reset our cached condition generator and let the base class reset the nodes.
        """
        self._cond_gen = None
        self._state = LoopState.RESET
        super().reset()

    def current_cond(self, gen_func: ConditionGenType) -> ConditionGenType:
        """
        Get the current cached version of the condition generator.
        """
        cond = self._cond_gen
        if cond is None:
            cond = gen_func
            self._cond_gen = cond
        return cond

    def _nest(self, resp: OpTick) -> NestedOpTick:
        # re-implemented here because we need to use the condition type
        # as the class name if we are in the condition part
        if self._state == LoopState.CONDITION:
            return NestedOpTick(resp, outer_status=f"- {self._condition.__class__.__name__}()")
        elif self._state == LoopState.BODY:
            return NestedOpTick(resp, outer_status=f"{self.index + 1} {self.current.__class__.__name__}()")
        else:
            raise InvalidLoopStateError("Cannot nest when in invalid state.")

    def tick(self, data: RunData) -> OpTickGen:
        while True:
            #
            # Loop conditional section
            #

            # if condition completes ensure we restart at condition
            reset: bool = False
            self._state = LoopState.CONDITION

            # current cached condition generator
            cond_gen: ConditionGenType = self.current_cond(self._condition.condition(data))

            while True:
                try:
                    # Ask condition for status
                    cond: bool
                    op_tick: OpTick
                    LOG.debug(f"Loop about to condition: {type(self._condition)}")
                    cond, op_tick = next(cond_gen)
                except StopIteration:
                    LOG.exception(f"Not supposed to stop: {type(self._condition)}")
                    raise

                # If not done then wait for next tick
                if not op_tick.done:
                    LOG.debug(f"Loop condition still true: {type(self._condition)}")
                    yield self._nest(op_tick)
                # If we are done and False then reset our state and yield out
                elif not cond:
                    LOG.debug(f"Loop condition turned false: {type(self._condition)}")
                    yield_result = self._nest(op_tick)
                    self.reset()
                    yield yield_result
                    LOG.debug(f"Loop condition resetting: {type(self._condition)}")
                    reset = True
                    # exit loop
                    break
                # condition done and True so move on to events
                else:
                    # exit loop
                    break

            # our condition completed - ensure next time through we start at condition
            if reset:
                continue

            #
            # Loop context event nodes section
            #
            self._state = LoopState.BODY
            while True:
                # Some loops have 0 event nodes
                if len(self._nodes) == 0:
                    break

                gen: OpTickGen = self.current_gen(self.current.tick(data))

                # Some EventGens would prefer to be reset each iteration
                try:
                    LOG.debug(f"Loop ticking {type(self.current)}")
                    op_tick = next(gen)
                except StopIteration:
                    LOG.debug(f"Loop stop iteration resetting {type(self.current)}")
                    self.reset_current_gen()
                    gen = self.current_gen(self.current.tick(data))
                    op_tick = next(gen)

                # If event is done then move to next event
                assert isinstance(op_tick, OpTick)
                if op_tick.done:
                    LOG.debug(f"Loop iteration done for {type(self.current)}")
                    self._index = self._index + 1

                    # end of the loop, reset things for the next run through
                    if self._index == len(self._nodes):
                        super().reset()

                    # loop is not done until condition is False, only condition is allowed to say we are done
                    yield self._nest(Again.wrap(op_tick))
                    break

                # not done so wait until next tick
                else:
                    LOG.debug(f"Loop iteration not done for {type(self.current)}")
                    yield self._nest(op_tick)


def loop(until: ConditionOp, *args: Op) -> Loop:
    return Loop(until, *args)

from abc import abstractmethod
from typing import Generator, <PERSON><PERSON>, cast

from core.controls.plan.ops.op import BaseOp, Done, OpTick
from core.controls.plan.state.run_data import RunData
from lib.common.logging import get_logger
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)


ConditionGenType = Generator[Tuple[bool, OpTick], None, None]


class ConditionOp(BaseOp):
    """
    Generator version of loop conditional
    """

    @abstractmethod
    def condition(self, data: RunData) -> ConditionGenType:
        pass


class AlwaysFalse(ConditionOp):
    """Use this as base case condition op. Can be helpful to avoid null references."""

    def condition(self, data: RunData) -> ConditionGenType:
        while True:
            yield False, Done()


class AlwaysTrue(ConditionOp):
    """Use this as condition op in loop to go forever"""

    def condition(self, data: RunData) -> ConditionGenType:
        while True:
            yield True, Done()


class CountDown(ConditionOp):
    def __init__(self, count: int, log: bool = False):
        super().__init__()
        self._start: int = count
        self._log: bool = log

    def condition(self, data: RunData) -> ConditionGenType:
        if self._log:
            LOG.info("count down starting from {}".format(self._start))
        count = self._start
        while count > 0:
            count -= 1
            yield True, Done(local_data={"start": self._start, "count": count})
        if self._log:
            LOG.info("count down finished from {}, going to yield {}".format(self._start, data))
        yield False, Done(local_data={"start": self._start, "count": count})
        assert False


class CountAdd(ConditionOp):
    """This is a node that runs for count runs and writes the count values plus some starting
       value back to a registered slot. Mostly (only) useful for testing."""

    def __init__(self, count: int, count_slotname: str, log: bool = False) -> None:
        super().__init__()
        self._count: int = count
        self._log: bool = log
        self._slotname = count_slotname

    def condition(self, data: RunData) -> ConditionGenType:
        for _ in range(self._count):
            yield True, Done()

            # increment
            current_value: int = cast(int, data.get(self._slotname, 0))
            data[self._slotname] = current_value + 1
        yield False, Done()
        assert False


class UntilTimestamp(ConditionOp):
    """Use this as condition to loop until a certain time."""

    def __init__(self, end_timestamp_ms: int):
        super().__init__()
        self._end_timestamp_ms = end_timestamp_ms

    def condition(self, data: RunData) -> ConditionGenType:
        remaining_ms = self._end_timestamp_ms - maka_control_timestamp_ms()
        while remaining_ms > 0:
            yield True, Done(local_data={"remaining_ms": remaining_ms})
            remaining_ms = self._end_timestamp_ms - maka_control_timestamp_ms()
        yield False, Done(local_data={"remaining_ms": remaining_ms})


class UntilElapsed(ConditionOp):
    """Use this as condition to loop until a certain time."""

    def __init__(self, elapsed_ms: int):
        super().__init__()
        self._threshold_elapsed_ms = elapsed_ms

    def condition(self, data: RunData) -> ConditionGenType:
        start_ms: int = maka_control_timestamp_ms()
        elapsed_ms = 0
        while elapsed_ms < self._threshold_elapsed_ms:
            yield True, Done(local_data={"elapsed_ms": elapsed_ms})
            elapsed_ms = maka_control_timestamp_ms() - start_ms
        yield False, Done(local_data={"elapsed_ms": elapsed_ms})


class Once(ConditionOp):
    """Will allow the body to run exactly once."""

    def __init__(self) -> None:
        super().__init__()
        self._once: bool = False

    def condition(self, data: RunData) -> ConditionGenType:
        while True:
            if not self._once:
                self._once = True
                yield True, Done()
            yield False, Done()


#
# Easy Creators
#


def count_down(count: int, log: bool) -> CountDown:
    return CountDown(count, log)


def count_add(count: int, count_slotname: str, log: bool) -> CountAdd:
    return CountAdd(count, count_slotname, log)


def never() -> AlwaysFalse:
    return AlwaysFalse()


def forever() -> AlwaysTrue:
    return AlwaysTrue()


def until_timestamp_ms(timestamp_ms: int) -> UntilTimestamp:
    return UntilTimestamp(timestamp_ms)


def until_elapsed_ms(elapsed_ms: int) -> UntilElapsed:
    return UntilElapsed(elapsed_ms)


def once() -> Once:
    return Once()

from typing import Callable, Optional

from core.controls.plan.ops.op import Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunData


class Callback(Op):
    """
    Fire a callback when we are executed. Used to notify the outer scope when the program finishes.
    """

    def __init__(self, cb: Optional[Callable[[], None]] = None):
        super().__init__()
        self._cb = cb

    def tick(self, data: RunData) -> OpTickGen:
        while True:
            if self._cb is not None:
                self._cb()
            yield Done()


def callback(cb: Optional[Callable[[], None]] = None) -> Callback:
    return Callback(cb=cb)

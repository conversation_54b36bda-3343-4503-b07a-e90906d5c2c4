#
# This Make<PERSON>le builds the Controls
DIR=core/controls

.PHONY: \
    all \
    clean \
    driver \
    frame \
    integ_tests \
    unit_tests

all: driver frame

driver:
	make -C driver all
	make -C frame all

clean:
	make -C driver clean
	make -C frame clean

integ_tests:
	make -C exterminator integ_tests

unit_tests:
	make -C driver unit_tests
	make -C exterminator unit_tests
	make -C frame unit_tests
	cd ../.. && \
		python -m pytest $(DIR) \
		--ignore $(DIR)/driver \
		--ignore $(DIR)/exterminator \
		--ignore $(DIR)/frame \
		--ignore $(DIR)/vision/pathway/weed_tracking \
		--durations=0

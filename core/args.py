import argparse
import os
from typing import Any, Optional, Sequence, Union

import core.defaults as defs
from core.boot.args import add_boot_arguments
from core.config.config import Config<PERSON><PERSON><PERSON><PERSON>, MakaConfigException, load_config
from core.controls.frame.parameter import FrameParameter
from core.fs.defaults import CALIBRATION_DIR, CONFIG_DIR, DATA_DIR, LOG_DIR, MEDIA_DIR
from core.model.path import DevicePath
from core.unity.constants import (
    DEFAULT_UNITY_GPS_OUTAGE_FREQ_MS,
    DEFAULT_UNITY_GPS_OUTAGE_LENGTH_MS,
    DEFAULT_UNITY_HEADING_STD_DEV,
    DEFAULT_UNITY_HOST,
    DEFAULT_UNITY_PORT,
)
from lib.common.logging import get_logger
from lib.common.time.sleep import set_sleep_detune
from tools.calibration.generate_charuco_image import add_charuco_board_arguments

LOG = get_logger(__name__)


def _positive_int(value: str) -> int:
    ivalue = int(value)
    if ivalue <= 0:
        raise argparse.ArgumentTypeError("%s is an invalid positive int value" % value)
    return ivalue


class SleepDetuneAction(argparse.Action):
    def __call__(
        self,
        parser: argparse.ArgumentParser,
        namespace: argparse.Namespace,
        values: Union[str, Sequence[Any], None],
        option_string: Optional[str] = None,
    ) -> None:
        detune = float(values)  # type: ignore
        set_sleep_detune(detune)


USER_DEFAULT_ARGS_FILEPATH = os.path.join(CONFIG_DIR, "default.args")


class RobotArgumentParser(argparse.ArgumentParser):
    """
    Customized argument parser.

    Supports @filename syntax for passing in file references which will be read for their command-line contents
    """

    def __init__(
        self,
        *argv: Any,
        description: str = "Run Robot",
        fromfile_prefix_chars: str = "@",
        default_args_filepath: str = USER_DEFAULT_ARGS_FILEPATH,
        **kwargs: Any,
    ) -> None:

        # Put explict named args into kwargs; avoids mypy error "multiple values for keyword argument" below
        kwargs.update({"description": description})
        kwargs.update({"fromfile_prefix_chars": fromfile_prefix_chars})

        super().__init__(*argv, **kwargs)
        try:
            # TODO We need to move where default.args is being loaded
            robot_config = load_config()
            self._default_args_filepath: str = robot_config.get(ConfigCategory.BOOT, "default.args")
        except MakaConfigException:
            self._default_args_filepath = default_args_filepath

        self._add_common_args()

        add_boot_arguments(self)

    def convert_arg_line_to_args(self, line: str) -> Any:
        """
        Override to support comments in files that get parsed
        """
        for arg in line.split():
            if not arg.strip():
                continue
            if arg[0] == "#":
                break
            yield arg

    def _handle_arg_parse(self, args: Any = None) -> Any:
        """
        Override to support default files getting added
        """
        if args is None:
            # Use existing parameters
            import sys
            import copy

            args = copy.copy(sys.argv[1:])

        # pass in args from default file as first arg, if it exists
        if os.path.exists(self._default_args_filepath):
            args = ["@" + self._default_args_filepath] + args
        else:
            # TODO logging may not be initialized yet
            LOG.warning(f"Missing {self._default_args_filepath}")
        return args

    def parse_args(self, args: Any = None, namespace: Any = None) -> Any:
        args = self._handle_arg_parse(args)

        return super().parse_args(args=args, namespace=namespace)

    def parse_known_args(self, args: Any = None, namespace: Any = None) -> Any:
        args = self._handle_arg_parse(args)

        return super().parse_known_args(args=args, namespace=namespace)

    def _add_common_args(self) -> None:
        # Device mode
        self.add_argument(
            "--sim-image",
            help="If you are in simulator mode, please use this image " "(instead of aruco generated fiducials)",
        )
        self.add_argument(
            "--sim-image-dir", help="If you are in simulator mode, please iterate this directory for images",
        )

        # Infrastructure
        self.add_argument(
            "--shmem-buffer", action="store_true", default=False, help="Enables the use of Shared Memory Buffer"
        )

        # Visual Cortex
        self.add_argument("--pathway", type=str, default=[], action="append", help="Enabled visual cortex pathways")

        # Simulated Virtual World
        self.add_argument("--virtual", help="Name of virtual robot to simulate", default=None, type=str)
        self.add_argument(
            "--virtual-treadkill", help="Sets Gounds Views to Treadkill", default=False, action="store_true"
        )

        # Simulated Unity World
        unity_group = self.add_argument_group("Unity MakaSim Args")
        unity_group.add_argument("--unity", help="Connect to a Unity MakaSim robot", action="store_true")
        unity_group.add_argument(
            FrameParameter.UNITY_HEADING_STD_DEV.cmdline_arg,
            default=DEFAULT_UNITY_HEADING_STD_DEV,
            type=float,
            help="Unity heading standard deviation from truth",
        )
        unity_group.add_argument(
            FrameParameter.UNITY_GPS_OUTAGE_FREQ_MS.cmdline_arg,
            default=DEFAULT_UNITY_GPS_OUTAGE_FREQ_MS,
            type=float,
            help="Unity GPS outage freqency (ms)",
        )
        unity_group.add_argument(
            FrameParameter.UNITY_GPS_OUTAGE_LENGTH_MS.cmdline_arg,
            default=DEFAULT_UNITY_GPS_OUTAGE_LENGTH_MS,
            type=float,
            help="Unity GPS outage length (ms)",
        )
        unity_group.add_argument(
            "--unity-host", help="Host Address of the Unity MakaSim robot", default=DEFAULT_UNITY_HOST, type=str
        )
        unity_group.add_argument(
            "--unity-port", help="Port of the Unity MakaSim robot", default=DEFAULT_UNITY_PORT, type=int
        )

        # Slow time down
        self.add_argument("--detune", help="Slow down polling", default=1.0, action=SleepDetuneAction)

        # Devices
        self.add_argument("--scanner-safety-dilate", type=int, nargs=2, action="append")

        # Image/Video options
        filesystem_group = self.add_argument_group("FileSystem Args")
        filesystem_group.add_argument("--fs-force-tmp-dir", help="Ignore other --fs arguments and use given arg")
        filesystem_group.add_argument("--fs-calibration-dir", help="calibration directory", default=CALIBRATION_DIR)
        filesystem_group.add_argument("--fs-config-dir", help="config directory", default=CONFIG_DIR)
        filesystem_group.add_argument("--fs-data-dir", help="data directory", default=DATA_DIR)
        filesystem_group.add_argument("--fs-log-dir", help="logs directory", default=LOG_DIR)
        filesystem_group.add_argument("--fs-media-dir", help="image directory", default=MEDIA_DIR)

        # Prediction
        self.add_argument(
            "--target-interpolate-num-strata-pan", type=_positive_int, default=defs.INTERPOLATE_TARGET_NUM_STRATA_PAN
        )
        self.add_argument(
            "--target-interpolate-num-strata-tilt", type=_positive_int, default=defs.INTERPOLATE_TARGET_NUM_STRATA_TILT
        )
        self.add_argument(
            "--target-interpolate-strata-min-samples",
            type=_positive_int,
            default=defs.INTERPOLATE_TARGET_MIN_STRATUM_SAMPLES,
        )

        # Calibration
        self.add_argument(
            "-c",
            "--calibrate",
            type=str,
            default="load",
            choices=["load", "new", "sim", "simulator"],
            help='Specify which calibration to load. One of: ["load", "new", "sim"]',
        )
        self.add_argument(
            "--calibrate-uniformity-attempts-markers",
            type=_positive_int,
            default=defs.CALIBRATE_UNIFORMITY_ATTEMPTS_MARKERS,
        )
        self.add_argument(
            "--calibrate-uniformity-attempts-diamonds",
            type=_positive_int,
            default=defs.CALIBRATE_UNIFORMITY_ATTEMPTS_DIAMONDS,
        )
        self.add_argument(
            "--calibrate-num-samples-per-strata", type=_positive_int, default=defs.CALIBRATE_MIN_STRATUM_SAMPLES
        )
        self.add_argument("--calibrate-num-strata-pan", type=_positive_int, default=defs.CALIBRATE_NUM_STRATA_PAN)
        self.add_argument("--calibrate-num-strata-tilt", type=_positive_int, default=defs.CALIBRATE_NUM_STRATA_TILT)
        self.add_argument(
            "--calibrate-predict-capture-stack", type=_positive_int, default=defs.CALIBRATE_PREDICT_CAPTURE_STACK
        )
        self.add_argument(
            "--calibrate-target-capture-stack", type=_positive_int, default=defs.CALIBRATE_TARGET_CAPTURE_STACK
        )

        self.add_argument("--fiducials", default=False, action="store_true")
        self.add_argument(
            "--show-samples",
            default=False,
            action="store_true",
            help="Whether to display spline samples on predict image view",
        )
        charuco_board_group = self.add_argument_group("Charuco Board Arguments")
        add_charuco_board_arguments(charuco_board_group)

        # Extermination
        extermination_group = self.add_argument_group("Extermination")
        extermination_group.add_argument(
            "--exterminator-partition-size-x", type=int, default=defs.DEFAULT_PARTITION_SIZE
        )
        extermination_group.add_argument(
            "--exterminator-partition-size-y", type=int, default=defs.DEFAULT_PARTITION_SIZE
        )
        extermination_group.add_argument("--sns-exterminate-strategy", type=str, default=None)
        extermination_group.add_argument("--sns-scoot-inches", type=int, default=None)

        # Device Registry
        device_registry_group = self.add_argument_group("Device Registry")
        device_registry_group.add_argument(
            "--skip-device", type=str, default=[], action="append", help="Skip Booting Devices of specified ID. (Debug)"
        )

        # Debugging
        debug_group = self.add_argument_group("Debugging")
        debug_group.add_argument("--pdb", action="store_true", help="Set up for pdb debugging")
        debug_group.add_argument(
            "--debug-shoot-plan-time",
            type=int,
            default=0,
            help="Time to wait before and after each plan while displaying debug annotations.",
        )

        # Web UI
        self.add_argument(
            "--preview-scale",
            type=float,
            default=defs.UI_PREVIEW_SCALE,
            help="Scale preview image by this amount (eg. 0.5)",
        )
        self.add_argument(
            "--preview-fps", type=float, default=defs.RATE_LIMIT_ANNOTATION_FPS, help="Rate limit annotations",
        )

        # Driving
        self.add_argument(
            "--no-auto-drive", default=False, action="store_true", help="Disallow accidental non-human driving"
        )

        self.add_argument("--no-gps", default=False, action="store_true", help="Disable reliance on GPS")

        self.add_argument("--http-port", type=int, default=defs.HTTP_PORT, help="HTTP server port")

        self.add_argument("--root-node", type=DevicePath.parse, default=DevicePath.robot(), help="Root node to boot")

import os
import time
from argparse import Namespace
from typing import Any, Callable, Dict, Optional, Set, Tuple, Union, cast

import cv2
import numpy as np
import numpy.typing as npt

import lib.common.annotate
import lib.common.logging
import lib.common.tasks as tasks
from core.config.config import Config<PERSON>ategory, PhysicalRobotConfiguration
from core.virtual.devices import VirtualDevice, load_virtual_devices_file
from lib.common.annotate import BGR_BLACK
from lib.common.error import MakaException
from lib.common.fiducials.charuco_board import CharucoBoard, generate_aruco_marker_image
from lib.common.image.cam import load_image
from lib.common.time.sleep import sleep_ms
from lib.common.treadkill import ArucoPlateTreadKill

LOG = lib.common.logging.get_logger(__name__)


class VirtualWorldException(MakaException):
    pass


class WorldView:
    """
        This class represent a view in the world. For example, the ground. It has an image which represents the
        current ground as well as an alpha mask which is used to keep the state of the view.
    """

    def __init__(self, img: npt.NDArray[Any]):
        self._img = img
        assert len(self._img.shape) == 3
        self._alpha_mask = np.full((img.shape[0], img.shape[1], 3), 255, np.uint8)
        self._annotations: Set[Callable[[npt.NDArray[Any]], npt.NDArray[Any]]] = set()

    @property
    def shape(self) -> Tuple[int, int, int]:
        return cast(Tuple[int, int, int], tuple(self._img.shape))

    def step(self) -> None:
        pass

    def add_annotation(self, f: Callable[[npt.NDArray[Any]], npt.NDArray[Any]]) -> None:
        self._annotations.add(f)

    def remove_annotation(self, f: Callable[[npt.NDArray[Any]], npt.NDArray[Any]]) -> None:
        if f in self._annotations:
            self._annotations.remove(f)

    def get(self, x1: int, x2: int, y1: int, y2: int) -> npt.NDArray[Any]:
        # TODO Annotations (Red Dot) Disabled for speed
        # for annotation in list(self._annotations):
        #     image = annotation(image)
        #     mask = annotation(mask)

        image = cv2.bitwise_and(self._img[y1:y2, x1:x2], self._alpha_mask[y1:y2, x1:x2])

        return cast(npt.NDArray[Any], image)

    def block_circle(self, pos: Tuple[float, float], radius: float) -> None:
        lib.common.annotate.circle(self._alpha_mask, pos, radius, BGR_BLACK, -1)


class TreadKillView(WorldView):
    def __init__(self, resolution: Tuple[int, int], n_markers: int, marker_size_px: int):
        self._treadkill: ArucoPlateTreadKill = ArucoPlateTreadKill(
            resolution, n_markers=n_markers, marker_size_px=marker_size_px
        )
        super().__init__(self._treadkill.field_image)

    def step(self) -> None:
        velocity_int = self._treadkill.step()
        self._alpha_mask = np.roll(self._alpha_mask, (velocity_int[1], velocity_int[0]), axis=(0, 1))
        self._img = self._treadkill.field_image


class VirtualWorld:
    """
        This class holds the simulated state of the world
    """

    GROUND = "ground"
    FORWARD = "forward"

    def __init__(self, image: Optional[str] = None, treadkill: bool = False) -> None:
        self._image = image
        self._treadkill = treadkill
        self._charuco_board: Optional[CharucoBoard] = None
        self._views: Dict[str, WorldView] = {}
        self._active_devices: Dict[str, VirtualDevice] = {}

        if image is not None:
            for key in self._views:
                self.set_view(key, image)

        self._time_step = 5
        self._sub_steps = 10
        self._task = tasks.start("VirtualWorld-Loop", target=self._world_loop)

    def _world_loop(self) -> None:
        task = tasks.get_current()
        assert task is not None
        while True:
            start_time = time.time()
            task.tick()
            for _, view in self._views.items():
                view.step()
            for i in range(self._sub_steps):
                self.step()
            delta = time.time() - start_time
            sleep_time = self._time_step * self._sub_steps - delta
            sleep_time = 0 if sleep_time < 0 else sleep_time
            if sleep_time == 0:
                LOG.error("Overloading Virtual World Loop: %s", delta)
            sleep_ms(sleep_time)

    @property
    def time_step(self) -> float:
        return self._time_step

    def get_view(self, source: str) -> WorldView:
        return self._views[source]

    def add_view(self, source: str, resolution: Tuple[int, int], view_type: str) -> None:
        if source in self._views:
            raise VirtualWorldException(
                f"View Already Exists: {source}. This likely occurs when there are more than 1 master camera per view source in the robot definition"
            )
        elif self._image:
            image = load_image(self._image, ppi=100).image_bgr
            self.set_view(source, cv2.resize(image, tuple(resolution)))
        elif view_type == VirtualWorld.FORWARD:
            self.set_view(source, generate_aruco_marker_image(resolution))
        elif self._treadkill:
            self._views[source] = TreadKillView(resolution, 25, int(resolution[0] / 100))
        else:
            charuco_board = CharucoBoard.create_with_default(
                res_x=resolution[0], res_y=resolution[1], pixels_per_bit=10, bits=4
            )
            self._charuco_board = charuco_board
            self.set_view(source, charuco_board.image)

    def export_board_args(self, args: Namespace) -> None:
        if self._charuco_board is not None:
            self._charuco_board.export_board_args(args)

    def set_view(self, source: str, image: Union[str, npt.NDArray[Any]]) -> None:
        self._views[source] = WorldView(cv2.imread(image)) if isinstance(image, str) else WorldView(image)

    def add_active(self, device: VirtualDevice) -> None:
        self._active_devices[device.id] = device

    def remove_active(self, device: VirtualDevice) -> None:
        if device.id in self._active_devices:
            del self._active_devices[device.id]

    def step(self) -> None:
        # TODO Consider a time delta that can be used with laser power
        try:
            for device in list(self._active_devices.values()):
                device.step()
        except Exception:
            LOG.exception("Error Occured While Stepping Virtual World")


class VirtualRobotConfiguration:
    def __init__(self, hw_cfg_filepath: str, retina_cfg_filepath: str, boot_args_filepath: str):
        self._hw_cfg_filepath: str = hw_cfg_filepath
        self._retina_cfg_filepath: str = retina_cfg_filepath
        self._boot_args_filepath: str = boot_args_filepath

    @property
    def hw_cfg_filepath(self) -> str:
        return self._hw_cfg_filepath

    @property
    def retina_cfg_filepath(self) -> str:
        return self._retina_cfg_filepath

    @property
    def boot_args_filepath(self) -> str:
        return self._boot_args_filepath


def load_virtual_robot(
    physical_robot_config: PhysicalRobotConfiguration, world: VirtualWorld, name: str
) -> VirtualRobotConfiguration:
    """
    Loads a Virtual Robot in the given Virtual World
    :param world: VirtualWorld to load the robot into
    :param name: Name of the robot to load
    :return: hardware.json and boot.args
    """
    hw_cfg_filepath = physical_robot_config.get(ConfigCategory.BOOT, "hardware.yaml")
    retina_cfg_filepath = physical_robot_config.get(ConfigCategory.BOOT, "retina.json")
    virtual_devices_cfg_filepath = physical_robot_config.get(ConfigCategory.BOOT, "virtual_devices.json")
    boot_args_filepath = physical_robot_config.get(ConfigCategory.BOOT, "boot.args")
    for f in [hw_cfg_filepath, retina_cfg_filepath, virtual_devices_cfg_filepath, boot_args_filepath]:
        if not os.path.exists(f):
            raise FileNotFoundError(f)

    # process and load the devices
    load_virtual_devices_file(world, virtual_devices_cfg_filepath)

    return VirtualRobotConfiguration(
        hw_cfg_filepath=hw_cfg_filepath, retina_cfg_filepath=retina_cfg_filepath, boot_args_filepath=boot_args_filepath
    )

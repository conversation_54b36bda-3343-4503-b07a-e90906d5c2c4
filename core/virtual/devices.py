import abc
import json
import math
import threading
from abc import ABC
from typing import TYPE_CHECKING, Any, Callable, Dict, Iterable, List, Optional, Tuple, Type, TypeVar, cast

import cv2
import numpy.typing as npt

import lib.common.annotate
from lib.common.error import MakaException
from lib.common.logging import get_logger

if TYPE_CHECKING:
    from core.virtual.world import VirtualWorld

LOG = get_logger(__name__)

################
# Exceptions
################


class VirtualDeviceException(MakaException):
    pass


################
# Registry
################


_virtual_device_registry: Dict[str, "VirtualDevice"] = {}


TT = TypeVar("TT", bound="VirtualDevice")


def get_device(virtual_id: str, T: Type[TT]) -> "TT":
    assert virtual_id in _virtual_device_registry, f"VirtualDevice not found: {virtual_id}"
    result = _virtual_device_registry[virtual_id]
    assert isinstance(result, T)
    return result


def add_device(device: "VirtualDevice") -> None:
    _virtual_device_registry[device.id] = device


################
# Devices
################


class VirtualDevice(abc.ABC):
    """
        Abstract class for virtual devices
    """

    def __init__(self, world: "VirtualWorld", id: str):
        self._world = world
        self._id = id

    @property
    def id(self) -> str:
        return self._id

    @abc.abstractmethod
    def step(self) -> None:
        raise NotImplementedError()


class VirtualActuator(VirtualDevice, ABC):
    """
    Refer comment for core Actuator class
    """

    pass


class VirtualSensor(VirtualDevice, ABC):
    """
    Refer comment for core Sensor class
    """

    pass


class VirtualServo(VirtualActuator):
    def __init__(self, world: "VirtualWorld", id: str, resolution: int, min: int, max: int):
        super().__init__(world, id)
        self._resolution = resolution
        self._min = min
        self._max = max
        self._position = int(min + ((max - min) / 2))
        self._step_size = 0
        self._steps_left = 0
        self._completed = threading.Event()
        self._completed.set()

    def step(self) -> None:
        if self._steps_left > 0:
            self._steps_left -= 1
            self._move(self._position + self._step_size)
        if self._steps_left == 0:
            self._world.remove_active(self)
            self._completed.set()

    @property
    def position(self) -> int:
        return int(self._position)

    @property
    def min(self) -> int:
        return self._min

    @property
    def max(self) -> int:
        return self._max

    @property
    def resolution(self) -> int:
        return self._resolution

    def _move(self, position: int) -> None:
        if position < self._min or position > self._max:
            raise VirtualDeviceException(f"Position out of bounds for servos: {position}")
        self._position = position

    def move(self, position: int, time_ms: int = 0) -> None:
        self._completed.wait()
        self._completed.clear()
        self._steps_left = 0
        delta = position - self._position
        steps = int(time_ms / (self._world.time_step * 1000)) + 1
        self._step_size = int(delta / steps)
        self._steps_left = steps
        self._world.add_active(self)

    def wait_for_position(self, timeout: Optional[float] = None) -> None:
        self._completed.wait(timeout=timeout)

    def get_relative_position(self) -> float:
        return self.position / self._resolution


class VirtualCamera(VirtualSensor):
    def __init__(
        self,
        world: "VirtualWorld",
        id: str,
        resolution: Tuple[int, int],
        scale: float,
        get_pos: Dict[str, str],
        source: str,
        focus_offset: float = 0,
        master_type: Optional[str] = None,
    ):
        super().__init__(world, id)
        assert scale != 0 and scale <= 1
        self._resolution = resolution
        self._scale = scale
        self._get_pos = _get_pos_maker(get_pos)
        self._source = source
        self._focus_offset = focus_offset
        self._master_type = master_type
        self._focus: float = 0
        self._properties: Dict[str, Any] = {}

        if master_type is not None:
            self._world.add_view(source, resolution, master_type)

    def step(self) -> None:
        super().step()

    @property
    def source(self) -> str:
        return self._source

    def get_focus(self) -> float:
        return self._focus

    def set_focus(self, focus: float) -> None:
        self._focus = focus
        self.set_property(focus=focus)

    def get_view_bounds(self) -> Tuple[int, int, int, int]:
        view = self._world.get_view(self._source)
        assert view is not None
        area = view.shape[0] * view.shape[1] * self._scale
        ratio = self._resolution[1] / self._resolution[0]
        if self._scale < 1:
            cam_width = int(math.sqrt(area / ratio))
            cam_height = int(cam_width * ratio)
        else:
            cam_width = self._resolution[0]
            cam_height = self._resolution[1]
        source_height = view.shape[0]
        source_width = view.shape[1]
        space_height = source_height - cam_height
        space_width = source_width - cam_width
        pos = self._get_pos()
        assert pos[0] <= 1 and pos[1] <= 1
        pos_x1 = math.floor(space_width * pos[0])
        pos_y1 = math.floor(space_height * pos[1])
        pos_x2 = pos_x1 + cam_width
        pos_y2 = pos_y1 + cam_height
        return pos_x1, pos_y1, pos_x2, pos_y2

    def get_center(self) -> Tuple[float, float]:
        pos_x1, pos_y1, pos_x2, pos_y2 = self.get_view_bounds()
        half_x = (pos_x2 - pos_x1) / 2
        half_y = (pos_y2 - pos_y1) / 2
        return pos_x1 + half_x, pos_y1 + half_y

    @property
    def resolution(self) -> Tuple[int, int]:
        return self._resolution

    def capture_image(self) -> npt.NDArray[Any]:
        view = self._world.get_view(self._source)
        pos_x1, pos_y1, pos_x2, pos_y2 = self.get_view_bounds()
        image = view.get(pos_x1, pos_x2, pos_y1, pos_y2)
        # Don't resize if not needed
        if pos_x2 != self._resolution[0] or pos_y2 != self._resolution[1]:
            image = cv2.resize(image, tuple(self._resolution))

        newfocus = abs(self._focus - self._focus_offset)
        if newfocus != 0:
            image = cv2.blur(image, (int(newfocus), int(newfocus)))

        return image

    def get_properties(self) -> Dict[str, Any]:
        return self._properties

    def set_property(self, **kwargs: Any) -> None:
        self._properties.update(**kwargs)


class VirtualLaser(VirtualActuator):
    def __init__(
        self,
        world: "VirtualWorld",
        id: str,
        get_pos: Dict[str, str],
        offset: Tuple[float, float],
        radius: float,
        power: float,
        source: str,
    ):
        super().__init__(world, id)
        self._offset: Tuple[float, float] = offset
        self._get_pos: Callable[[], Tuple[float, float]] = _get_pos_maker(get_pos)
        self._radius: float = radius
        self._power: float = power
        self._source: str = source
        self._armed: bool = False
        self._on: bool = False

    def relative_position(self) -> Tuple[int, int]:
        pos = self._get_pos()
        return int(pos[0] + self._offset[0]), int(pos[1] + self._offset[1])

    def beam_radius(self) -> float:
        # This is temporarily hardcoded as target space pixels
        # TODO Change this to real world units once Real World Calibration is improved
        return math.sqrt((self._radius ** 2) / 0.06)

    def step(self) -> None:
        if not self._armed or not self._on:
            return
        view = self._world.get_view(self._source)
        pos = self.relative_position()
        view.block_circle(pos, self._radius)

    def draw_dot(self, image: npt.NDArray[Any]) -> npt.NDArray[Any]:
        if not self._armed or not self._on:
            return image
        lib.common.annotate.circle(image, self.relative_position(), self._radius, lib.common.annotate.BGR_RED, -1)
        return image

    def arm(self) -> None:
        self._armed = True

    def disarm(self) -> None:
        self._armed = False

    def on(self) -> None:
        self._on = True
        self._world.add_active(self)
        view = self._world.get_view(self._source)
        view.add_annotation(self.draw_dot)

    def off(self) -> None:
        self._on = False
        self._world.remove_active(self)
        view = self._world.get_view(self._source)
        view.remove_annotation(self.draw_dot)


################
# Robot Def Parsing
################


VIRTUAL_DEVICE_TYPE_MAP = {"servo": VirtualServo, "laser": VirtualLaser, "cam": VirtualCamera}


def _get_pos_servo(servo0: VirtualServo, servo1: VirtualServo) -> Tuple[float, float]:
    return servo0.get_relative_position(), servo1.get_relative_position()


def _get_pos_maker(get_pos: Dict[str, str]) -> Callable[[], Tuple[float, float]]:
    if get_pos["type"] == "servo":
        servos = get_pos["servos"]
        return lambda: _get_pos_servo(get_device(servos[0], VirtualServo), get_device(servos[1], VirtualServo))
    elif get_pos["type"] == "target":
        target = get_device(get_pos["target"], VirtualCamera)
        return lambda: target.get_center()
    elif get_pos["type"] == "fixed":
        return lambda: cast(Tuple[float, float], tuple(get_pos["offset"]))
    else:
        raise VirtualDeviceException("%s Get Position type not supported", get_pos["type"])


def load_virtual_devices(world: "VirtualWorld", virtual_devices: Iterable[Dict[str, Any]]) -> None:
    for virtual_device in virtual_devices:
        device_class = VIRTUAL_DEVICE_TYPE_MAP[virtual_device["type"]]
        del virtual_device["type"]
        device = device_class(world=world, **virtual_device)
        add_device(device)


_VIRTUAL_ROBOT_DEVICES_KEY: str = "virtual_devices"


def load_virtual_devices_file(world: "VirtualWorld", filepath: str) -> None:
    with open(filepath, "r") as f:
        robot_def = json.load(f)
    virtual_devices: List[Dict[str, Any]] = robot_def[_VIRTUAL_ROBOT_DEVICES_KEY]
    load_virtual_devices(world, virtual_devices)

from typing import Any, Optional

import lib.common.logging
from core.controls.exterminator.model.laser import Laser
from core.virtual.devices import VirtualL<PERSON>r, get_device

LOG = lib.common.logging.get_logger(__name__)


class VirtualLaserDriver(Laser):
    def __init__(self, virtual_id: Optional[str] = None, **kwargs: Any):
        super().__init__(**kwargs)
        assert virtual_id is not None
        self._virtual_id = virtual_id
        self._virtual_device = get_device(self._virtual_id, VirtualLaser)

    def beam_radius(self) -> float:
        return self._virtual_device.beam_radius()

    def arm(self) -> None:
        self._virtual_device.arm()
        super().arm()

    def disarm(self) -> None:
        self._virtual_device.disarm()
        super().disarm()

    def on(self, duration: Optional[int] = None) -> None:
        if self.armed:
            self._virtual_device.on()

    def off(self) -> None:
        super().off()
        self._virtual_device.off()

    def firing(self) -> bool:
        return False

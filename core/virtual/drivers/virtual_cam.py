import time
from typing import Any, Dict, Optional, <PERSON>ple

import numpy.typing as npt

import lib.common.logging
import lib.common.tasks
from core.cv.retina.camera.node import Cam
from core.virtual.devices import VirtualCamera, get_device
from lib.common.tasks import MakaTask
from lib.common.time import maka_control_timestamp_ms

LOG = lib.common.logging.get_logger(__name__)


class VirtualCamDriver(Cam):
    def __init__(self, *argv: Any, virtual_id: Optional[str] = None, **kwargs: Any):
        super().__init__(*argv, **kwargs)

        assert virtual_id is not None
        self._virtual_id = virtual_id
        self._virtual_device = get_device(self._virtual_id, VirtualCamera)

    def _control_loop(self) -> None:
        """simulator loop function"""
        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None
        while True:
            task.tick()
            self._new_frame(self._virtual_device.capture_image(), frame_timestamp_ms=maka_control_timestamp_ms())
            time.sleep(0.025)

    def capture_image(self) -> npt.NDArray[Any]:
        return self._virtual_device.capture_image()

    def get_properties(self) -> Dict[str, Any]:
        return self._virtual_device.get_properties()

    def set_property(self, **kwargs: Any) -> None:
        self._virtual_device.set_property(**kwargs)

    @property
    def resolution(self) -> Tuple[int, int]:
        return self._virtual_device.resolution

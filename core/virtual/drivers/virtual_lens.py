from typing import Any, List, <PERSON><PERSON>

import lib.common.logging
from core.cv.retina.lens import LensNode
from core.virtual.devices import VirtualCamera, get_device

LOG = lib.common.logging.get_logger(__name__)


class VirtualLensNodeDriver(LensNode):
    def __init__(self, virtual_id: str, cam_id: str, focus: float = 0, **kwargs: Any):
        super().__init__(**kwargs)

        self._virtual_id = virtual_id
        self._cam_device = get_device(cam_id, VirtualCamera)
        assert self._cam_device is not None

        self._cam_device.set_focus(focus)

    def get_focus(self) -> float:
        return self._cam_device.get_focus()

    def set_focus(self, focus: float) -> None:
        self._cam_device.set_focus(focus)

    def get_id(self) -> str:
        return self._virtual_id

    def focus_range(self) -> Tuple[float, float]:
        return (0, 1000)

    def focus_step_sizes(self) -> List[float]:
        return [100, 20, 5]

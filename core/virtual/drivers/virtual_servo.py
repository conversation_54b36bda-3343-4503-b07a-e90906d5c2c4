from typing import Any, Optional

from core.controls.exterminator.model.servo import Servo
from core.virtual.devices import VirtualServo, get_device


class VirtualServoDriver(Servo):
    def __init__(self, virtual_id: Optional[str] = None, **kwargs: Any):
        assert virtual_id is not None
        self._virtual_id = virtual_id
        self._virtual_device = get_device(self._virtual_id, VirtualServo)
        super().__init__(
            resolution=self._virtual_device.resolution,
            min=self._virtual_device.min,
            max=self._virtual_device.max,
            max_range_degrees=360,
            **kwargs
        )
        self._enabled = True

    def _compute_velocity_from_delta(self, position: int, time_ms: int) -> int:
        # Velocity unused in virtual world
        return 0

    def _set_position(self, position: int, time_ms: int = 0, await_settle: bool = False) -> None:
        if not self._enabled:
            raise Exception("Servo not enabled")
        self._virtual_device.move(position, time_ms)
        if await_settle:
            self._wait_position_complete()

    def _wait_position_complete(self) -> None:
        if not self._enabled:
            raise Exception("Servo not enabled")
        self._virtual_device.wait_for_position()

    def _get_position(self) -> int:
        return self._virtual_device.position

    def _get_goal_position_inc(self) -> int:
        return self._virtual_device.position

    def _disable(self) -> None:
        self._enabled = False

    def _enable(self) -> None:
        self._enabled = True

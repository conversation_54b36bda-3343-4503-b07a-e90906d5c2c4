import threading
from typing import Any, Callable, Dict, Optional, Union

# this module gets imported by the Node class so it can't import from core.model
# This module will go away sometime reasonably soon so its not a concern
from core.model.id import ReferenceId  # isort:skip
from core.model.path import DevicePath  # isort:skip


class StatusReporter:
    """
    Status reporter which manages callbacks to report status-related state.
    """

    def __init__(self) -> None:
        self.infos: Dict[str, Dict[str, Callable[[], Any]]] = {}

    def register_reference_pool_cb(self, reference_id: ReferenceId, cb: Callable[[], Any]) -> None:
        self.register_cb("pool", reference_id, cb)

    def register_device_tree_cb(self, device_path: DevicePath, cb: Callable[[], Any]) -> None:
        self.register_cb("tree", device_path, cb)

    def register_cb(self, category: str, key: Union[DevicePath, ReferenceId], cb: Callable[[], Any]) -> None:
        if category not in self.infos:
            self.infos[category] = {}
        cat_dict = self.infos[category]

        cat_dict[str(key)] = cb

    def poll_status(self) -> Dict[str, Any]:
        status = {}
        for category, category_dict in self.infos.items():
            category_status = {}
            for k, cb in category_dict.items():
                category_status[k] = cb()
            status[category] = category_status

        return status


# This is the global status reporter
_init_lock = threading.Lock()
_system_status_reporter: Optional[StatusReporter] = None


def status_reporter() -> StatusReporter:
    """
    :return: the global status reporter
    """
    global _init_lock
    with _init_lock:
        global _system_status_reporter
        if _system_status_reporter is None:
            _system_status_reporter = StatusReporter()
    return _system_status_reporter

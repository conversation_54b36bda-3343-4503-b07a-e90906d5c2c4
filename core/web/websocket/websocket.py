#!/usr/bin/env python
import asyncio
import concurrent.futures
import inspect
from asyncio import Event
from collections import defaultdict
from typing import Any, Awaitable, Callable, Dict, Optional, Union

import websockets

import lib.common.logging
import lib.common.tasks
from core.web.websocket.connection import JsonWebSocketConnection, WebSocketConnection
from core.web.websocket.defaults import HOST
from core.web.websocket.message import IncomingMessage, MessageType
from lib.common.asyncio.utils import wrap_async

LOG = lib.common.logging.get_logger(__name__)
WSLOG = lib.common.logging.get_websocket_logger(__name__)


def _prefix_websocket_name(name: str) -> str:
    return name if name.startswith("ws://") else f"ws://{name}"


class WebSocketParameter:
    # optional consumer/producer throttle
    # we might want to split this for consuming / producing, but right now sockets do one or the other primarily
    FREQ_HZ = "freq_hz"


def assert_function_declaration(f: Callable[..., Any], log_prefix: str = "") -> None:
    if f is not None:
        assert (
            len([a for a in inspect.getfullargspec(f).args if a != "self"]) >= 1
        ), f'{log_prefix}{f} must accept at least one argument (other than "self")'


class WebSocket:
    """
    Robot WebSocket manager, handling multiple concurrent connections.
    """

    def __init__(
        self,
        name: str,
        produce: Optional[Callable[[WebSocketConnection], Any]] = None,
        consumers: Optional[Dict[str, Callable[[Dict[str, Any]], Union[Dict[str, Any], None]]]] = None,
        host: str = HOST,
        port: Optional[int] = None,
        default_connection_params: Optional[Dict[str, Any]] = None,
    ):
        assert name is not None
        name = _prefix_websocket_name(name)
        self._name = name
        assert host is not None
        self._host = host
        assert port is not None and port > 0
        self._port = port

        assert produce is not None or consumers is not None
        if consumers is not None:
            assert len(consumers) > 0
        if produce is not None:
            assert_function_declaration(produce, log_prefix=f"{name} produce function: ")
        # async producer callback
        self._produce: Optional[Callable[[WebSocketConnection], Any]] = wrap_async(
            produce
        ) if produce is not None else None

        # msg_type router of callables to invoke
        self._consumers: Dict[str, Callable[[Dict[str, Any]], Awaitable[Any]]] = {}
        for msg_type, f in (consumers if consumers is not None else {}).items():
            assert_function_declaration(f, log_prefix=f"{name} consumers[{msg_type}] function: ")
            self._consumers[msg_type] = wrap_async(f)

        self._base_consumers: Dict[str, Callable[[Dict[str, Any], WebSocketConnection], None]] = {
            MessageType.HELLO: self._hello,
            MessageType.NEGOTIATE: self._negotiate,
        }

        self._connections: Dict[str, WebSocketConnection] = {}
        self._connections_params: Dict[str, Dict[str, Any]] = defaultdict(
            lambda: default_connection_params.copy() if default_connection_params is not None else {}
        )  # per-connection parameters
        self._connections_hellos: Dict[str, Event] = defaultdict(asyncio.Event)

    @property
    def name(self) -> str:
        return self._name

    @property
    def host(self) -> str:
        return self._host

    @property
    def port(self) -> Optional[int]:
        return self._port

    @property
    def connection_params(self) -> Dict[str, Dict[str, Any]]:
        return self._connections_params

    @property
    def addr(self) -> str:
        return f"{self._host}:{self._port}"

    def setup_async(self, loop: asyncio.BaseEventLoop) -> None:
        LOG.info(f"Ensuring future [async] Robot WebSocket Server at ws://{self._host}:{self._port}")
        # TODO update to asyncio.create_task (3.7) and add name (3.8)
        self._loop = loop
        self._ws = websockets.serve(self._handler, self._host, self._port)
        self._wstask = asyncio.ensure_future(self._ws, loop=loop)

    def shutdown_async(self) -> None:
        self._wstask.cancel()
        self._loop.call_soon_threadsafe(self._loop.stop)

        LOG.info(f"Closed Robot WebSocket Server at ws://{self._host}:{self._port}")

    def _hello(self, data: Dict[str, Any], connection: WebSocketConnection) -> None:
        """
        Standard hello handler
        """
        LOG.debug(f"{connection.id} hello: {data}")
        self._connections_params[connection.id].update(data)
        self._connections_hellos[connection.id].set()

    def _negotiate(self, data: Dict[str, Any], connection: WebSocketConnection) -> None:
        """
        Standard negotiate handler
        """
        LOG.debug(f"{connection.id} negotiate: {data}")
        self._connections_params[connection.id].update(data)

    def _register(self, connection: WebSocketConnection) -> None:
        """
        Register a new connection
        """
        self._connections[connection.id] = connection

    def _unregister(self, connection: WebSocketConnection) -> None:
        """
        Unregister a connection. Does nothing if the connection is not already registered.
        """
        self._connections.pop(connection.id, None)
        self._connections_params.pop(connection.id, None)
        self._connections_hellos.pop(connection.id, None)

    async def _throttle(self, connection: WebSocketConnection) -> None:
        hertz = self._connections_params[connection.id].get(WebSocketParameter.FREQ_HZ)
        if hertz is not None:
            if float(hertz) == 0:
                await asyncio.sleep(999999)
            else:
                throttle_sleep_ms = 1000 / float(hertz)
                await asyncio.sleep(throttle_sleep_ms / 1000)

    async def _consumer_handler(self, connection: WebSocketConnection) -> None:
        """
        https://websockets.readthedocs.io/en/stable/intro.html#common-patterns

        Iteration terminates when the client disconnects.
        """
        assert self._consumers is not None
        async for message in connection:
            # this assume JSON incoming messages. Might need to abstract that in future
            msg_type, data = IncomingMessage.parse(message)

            # run base functionality for this msg_type, if present
            base_f = self._base_consumers.get(msg_type)
            if base_f is not None:
                base_f(data, connection)

            # custom router
            f = self._consumers.get(msg_type)
            assert f is not None or base_f is not None
            if f is not None:
                # response is optional
                response = await f(data)
                if response is not None:
                    try:
                        await connection.send(response)
                    except websockets.ConnectionClosedOK:
                        return

            # optional throttle if a max hertz is set for this connection
            await self._throttle(connection)

    async def _producer_handler(self, connection: WebSocketConnection) -> None:
        """
        https://websockets.readthedocs.io/en/stable/intro.html#common-patterns

        send() raises a ConnectionClosed exception when the client disconnects, which breaks out of the while True loop.
        """
        assert self._produce is not None

        # the consumer and producer fire off at approximately the same time.
        # If there is an initial hello message, then we want to give the async scheduler a chance to receive it
        # before starting the producer
        try:
            await asyncio.wait_for(self._connections_hellos[connection.id].wait(), timeout=1)
        except concurrent.futures.TimeoutError:
            LOG.exception(f"{connection.id} timeout waiting for hello")

        while True:  # we want to exit when the connection closes, even if we don't have something to produce
            message = await self._produce(connection)

            if message is not None:
                await connection.send(message)

            # optional throttle if a max hertz is set for this connection
            await self._throttle(connection)

    async def _handler(self, websocket: websockets.WebSocketServerProtocol, path: str) -> Awaitable[None]:
        """
        https://websockets.readthedocs.io/en/stable/intro.html#common-patterns

        Runs both consumer/producer tasks
        """
        # initialize connection
        connection = JsonWebSocketConnection(self.name, websocket, path)
        cookie = websocket.request_headers.get("cookie", websocket.request_headers.get("Cookie"))
        LOG.info(
            f"{connection.id} (active: {len(self._connections)}) connected to {websocket.origin} (cookie: {cookie})"
        )

        # Register.
        self._register(connection)
        try:
            tasks = []

            if self._consumers is not None:
                consumer_task = asyncio.ensure_future(self._consumer_handler(connection))
                tasks.append(consumer_task)

            if self._produce is not None:
                producer_task = asyncio.ensure_future(self._producer_handler(connection))
                tasks.append(producer_task)

            done, pending = await asyncio.wait(tasks, return_when=asyncio.FIRST_COMPLETED)
            for task in pending:
                task.cancel()
        finally:
            # Unregister.
            self._unregister(connection)

        LOG.info(f"{connection.id} connection closed")
        return None  # type: ignore

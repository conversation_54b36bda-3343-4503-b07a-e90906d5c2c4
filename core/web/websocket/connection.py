import abc
import j<PERSON>
from typing import Any, Async<PERSON>tera<PERSON>, Callable, Dict, Optional, Union

from websockets import ConnectionClosedOK, WebSocketServerProtocol

import lib.common.logging
from lib.common.uuid import friendly_id

LOG = lib.common.logging.get_logger(__name__)
WSLOG = lib.common.logging.get_websocket_logger(__name__)


class WebSocketConnection(abc.ABC):
    """
    A WebSocketConnection with a single client
    """

    def __init__(
        self,
        name: str,
        websocket: WebSocketServerProtocol,
        path: str,
        serialize: Optional[Callable[[Any], str]] = None,
        deserialize: Optional[Callable[[Union[str, bytes]], Dict[str, Any]]] = None,
    ):
        assert name is not None
        self._id: str = f"{name}-{friendly_id()}"

        assert websocket is not None
        self._websocket: WebSocketServerProtocol = websocket
        assert path is not None
        self._path: str = path

        assert serialize is not None
        self._serialize = serialize
        assert deserialize is not None
        self._deserialize = deserialize

    @property
    def open(self) -> bool:
        return self._websocket.open

    @property
    def closed(self) -> bool:
        return self._websocket.closed

    @property
    def id(self) -> str:
        return self._id

    async def __aiter__(self) -> AsyncIterator[Dict[str, Any]]:
        """
        Iterate on received messages.

        Exit normally when the connection is closed with code 1000 or 1001.

        Raise an exception in other cases.
        """
        try:
            while True:
                yield await self.recv()
        except ConnectionClosedOK:
            return

    async def recv(self) -> Dict[str, Any]:
        msg: Union[str, bytes] = await self._websocket.recv()
        dmsg: Dict[str, Any] = self._deserialize(msg)
        # this logger will work as long as we always deserialize into a dict
        WSLOG.emit(dmsg)
        return dmsg

    async def send(self, message: Dict[str, Any]) -> None:
        try:
            WSLOG.emit(message)
        except TypeError:
            LOG.exception(f"Bad type cannot be logged as JSON. Got: {message}")
            raise
        serialized_msg = self._serialize(message)
        return await self._websocket.send(serialized_msg)


class JsonWebSocketConnection(WebSocketConnection):
    """
    A websocket connection that speaks JSON.
    """

    def __init__(self, *argv: Any, **kwargs: Any):
        assert "serialize" not in kwargs
        assert "deserialize" not in kwargs
        super().__init__(*argv, **kwargs, serialize=json.dumps, deserialize=json.loads)  # type: ignore

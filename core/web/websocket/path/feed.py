import json
from typing import Any, Dict, Optional

import websocket
from core.web.websocket.connection import WebSocketConnection
from core.web.websocket.defaults import FEED_PORT, HOST
from core.web.websocket.message import HelloMessage
from core.web.websocket.websocket import WebSocket
from lib.common.protocol.channel.base import Topic
from lib.common.protocol.feed.base import Feed
from lib.common.serialization.json import JsonSerializable


class FeedHelloMessage(HelloMessage):
    def __init__(self, topic: Topic, **kwargs: Any):
        super().__init__(**kwargs)
        self._topic = topic

    @property
    def topic(self) -> Topic:
        return self._topic

    def to_json(self) -> Dict[str, Any]:
        result = super().to_json()
        result["data"]["topic"] = self.topic
        return result

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "FeedHelloMessage":
        return FeedHelloMessage(freq_hz=data["freq_hz"], topic=Topic(data["topic"]))


class FeedWebSocket(WebSocket):
    def __init__(self, *, feed: Feed, host: str = HOST, port: int = FEED_PORT):
        self._feed = feed
        super().__init__(name="feed", produce=self._callback, host=host, port=port)

    @property
    def feed(self) -> Feed:
        return self._feed

    def _callback(self, connection: WebSocketConnection) -> Any:
        topic = self.connection_params[connection.id]["topic"]
        channel = self.feed.subscribe(topic, object)
        result: Optional[Any] = channel.read()
        if result is None:
            return None
        if not isinstance(result, JsonSerializable):
            return "not serializable"
        return result.to_json()


class FeedWebSocketClient:
    def __init__(self, host: str = HOST, port: int = FEED_PORT):
        self._host: str = host
        self._port: int = port

        self._websocket = websocket.WebSocket()

    @property
    def host(self) -> str:
        return self._host

    @property
    def port(self) -> int:
        return self._port

    @property
    def url(self) -> str:
        return f"ws://{self.host}:{self.port}"

    def connect(self, hello: FeedHelloMessage) -> None:
        self._websocket.connect(self.url)
        self._websocket.send(json.dumps(hello.to_json()))

    async def recv(self) -> Any:
        return self._websocket.recv()

from typing import Any, Dict, Optional

import lib.common.logging
import lib.common.tasks
from core.web.websocket.connection import WebSocketConnection
from core.web.websocket.defaults import HOST, LOGS_PORT
from core.web.websocket.message import MessageType
from core.web.websocket.websocket import WebSocket


class RollingLogsWebSocket(WebSocket):
    def __init__(self, *, host: str = HOST, port: int = LOGS_PORT):
        super().__init__(
            name="logs",
            consumers={MessageType.HELLO: lambda _: lib.common.logging.get_logs(0)},
            produce=self._produce_logs,
            host=host,
            port=port,
        )

    def _produce_logs(self, connection: WebSocketConnection) -> Optional[Dict[str, Any]]:
        p = self._connections_params[connection.id]
        msg = lib.common.logging.get_logs(p.get("last", -1) + 1)
        p["last"] = msg["first"] - 1 + len(msg["logs"])
        return msg if len(msg["logs"]) > 0 else None

from typing import TYPE_CHECKING, Any, Dict

from core.web.websocket.defaults import HOST, STATUS_PORT
from core.web.websocket.websocket import WebSocket

if TYPE_CHECKING:
    from core.robot import Robot


class StatusWebSocket(WebSocket):
    def __init__(self, *, bot: "Robot", host: str = HOST, port: int = STATUS_PORT):
        def status_callback(_: Any) -> Dict[str, Any]:
            return bot.status_callback()

        super().__init__(name="status", produce=status_callback, host=host, port=port)

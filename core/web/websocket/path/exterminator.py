from typing import TYPE_CHECKING, Any, Dict

from core.web.websocket.defaults import EXTERMINATOR_PORT, HOST
from core.web.websocket.requests.servo_request import ServoRequest
from core.web.websocket.websocket import WebSocket

if TYPE_CHECKING:
    from core.controls.exterminator.model.exterminator import Exterminator


MSG_TYPE_SERVO = "servo"


class ExterminatorWebSocket(WebSocket):
    def __init__(
        self,
        *,
        name: str = "exterminator",
        exterminator: "Exterminator",
        host: str = HOST,
        port: int = EXTERMINATOR_PORT,
    ):
        assert exterminator is not None
        self.exterminator = exterminator
        super().__init__(name=name, consumers={MSG_TYPE_SERVO: self.handle_raw_servo_move_json}, host=host, port=port)

    def handle_raw_servo_move_json(self, data: Dict[str, Any]) -> Dict[str, Any]:
        servo_request: ServoRequest = ServoRequest.from_json(data)
        response = self.exterminator.handle_servo_move(servo_request)
        return response.to_json()

from typing import Any, Callable, Dict

from core.web.websocket.defaults import DRIVE_PORT, HOST
from core.web.websocket.websocket import WebSocket
from generated.core.controls.driver.model.manual_drive_request import ManualDriveRequest
from generated.core.controls.driver.model.manual_drive_response import ManualDriveResponse

MSG_TYPE_DRIVE = "drive"


class DriveWebSocket(WebSocket):
    def __init__(
        self,
        *,
        name: str = MSG_TYPE_DRIVE,
        manual_drive_callback: Callable[[ManualDriveRequest], ManualDriveResponse],
        host: str = HOST,
        port: int = DRIVE_PORT
    ):
        # use this instead of lambda to hide response from being sent back which is not helpful in UI today
        def manual_drive(j: Dict[str, Any]) -> None:
            manual_drive_callback(ManualDriveRequest.from_json(j))

        super().__init__(
            name=name, consumers={MSG_TYPE_DRIVE: manual_drive}, host=host, port=port,
        )

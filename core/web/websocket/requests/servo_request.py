from typing import Any, Dict, Optional, Tuple

from core.model.path import Device<PERSON><PERSON>
from core.simulator.utils import SERVO_RESOLUTION
from lib.common.serialization.json import JsonSerializable


class ServoRequest(JsonSerializable):
    def __init__(self, gimbal_path: str, servo_path: Optional[str] = None, servo_value: Optional[str] = None):
        assert gimbal_path is not None
        self._gimbal_path: DevicePath = DevicePath.parse(gimbal_path)

        self._servo_path: Optional[DevicePath] = DevicePath.parse(servo_path) if servo_path else None
        self._servo_value: Optional[int] = int(servo_value) if servo_value else None

        self._pan: Optional[int] = None
        self._tilt: Optional[int] = None
        self._recommended_servo_step: Optional[int] = None

    def set_recommended_servo_step(self, gimbal_resolution: int) -> None:
        self._recommended_servo_step = round(gimbal_resolution / SERVO_RESOLUTION)

    def set_servos(self, servos: Tuple[int, int]) -> None:
        pan, tilt = servos
        self._pan = pan
        self._tilt = tilt

    @property
    def gimbal_path(self) -> DevicePath:
        return self._gimbal_path

    @property
    def servo_path(self) -> Optional[DevicePath]:
        return self._servo_path

    @property
    def servo_value(self) -> Optional[int]:
        return self._servo_value

    def to_json(self) -> Dict[str, Any]:
        return {"pan": self._pan, "tilt": self._tilt, "recommended_step": self._recommended_servo_step}

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "ServoRequest":
        return ServoRequest(
            gimbal_path=data["gimbal_path"], servo_path=data["servo_path"], servo_value=data["servo_value"]
        )

from typing import Any, Dict, <PERSON>ple

from lib.common.logging import get_logger
from lib.common.serialization.json import JsonSerializable

LOG = get_logger(__name__)


class MessageFormatException(Exception):
    pass


class MessageType:
    # initial hello - should only happen once per connection
    HELLO = "hello"

    # change or negotiate parameters (e.g. camera bandwidth, status rate)
    NEGOTIATE = "negotiate"


class IncomingMessage:
    MSG_TYPE: str = "msg_type"
    DATA: str = "data"

    @staticmethod
    def parse(message: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        for k in [IncomingMessage.MSG_TYPE, IncomingMessage.DATA]:
            if k not in message:
                raise MessageFormatException(f"Missing key: {k}")

        try:
            return message[IncomingMessage.MSG_TYPE], message[IncomingMessage.DATA]
        except KeyError:
            LOG.error(f"Cannot parse: {message}")
            raise
        except Exception:
            LOG.error(f"Cannot parse: {message}")
            raise


class HelloMessage(JsonSerializable):
    def __init__(self, freq_hz: float = 1.0):
        self._freq_hz = freq_hz

    @property
    def freq_hz(self) -> float:
        return self._freq_hz

    def to_json(self) -> Dict[str, Any]:
        return {"msg_type": "hello", "data": {"freq_hz": self.freq_hz}}

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "HelloMessage":
        assert data["msg_type"] == MessageType.HELLO
        return HelloMessage(data["data"]["freq_hz"])

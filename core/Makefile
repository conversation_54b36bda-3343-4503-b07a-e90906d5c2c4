#
# Root robot Makefile
#
# TODO: make web will move here once web/ moves to core/web/

all: controls unity

controls:
	make -C controls all

unity:
	make -C unity all

clean:
	make -C controls clean
	make -C unity clean
	rm -rf ../generated/core

integ_tests:
	make -C controls integ_tests

unit_tests:
	cd .. && \
		python -m pytest core \
		--ignore core/controls \
		--ignore core/unity \
		--durations=0
	make -C controls unit_tests

.PHONY: all \
		controls \
		clean \
		integ_tests \
		unit_tests

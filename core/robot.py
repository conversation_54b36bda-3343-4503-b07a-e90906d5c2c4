import asyncio
import os
import threading
from argparse import Namespace
from typing import Any, Dict, Optional, cast

import lib.common.tasks
from config.client.cpp.config_client_python import ConfigClient
from core.controls.commander.node import Commander<PERSON><PERSON>
from core.controls.driver.driver import Driver
from core.controls.driver.server.http.router import Driver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from core.controls.exterminator.api.proxy import ExterminatorProxy
from core.controls.exterminator.calibration.config import CalibrationConfigFile
from core.controls.exterminator.model.exterminator import Exterminator
from core.controls.exterminator.server.http.router import ExterminatorRouterBuilder
from core.controls.frame.frame import Frame
from core.controls.frame.server.http.router import Frame<PERSON>outer<PERSON>uilder
from core.cv.retina.retina import Retina
from core.cv.retina.server.http.router import Retina<PERSON>outer<PERSON>uilder
from core.cv.visual_cortex.server.http.router import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er
from core.cv.visual_cortex.visual_cortex import <PERSON><PERSON><PERSON>
from core.defaults import HTTP_PORT
from core.model.feed.router import Feed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from core.model.id import ReferenceId
from core.model.node.base import Node, NodeAccessPolicy
from core.model.node.classic import ClassicNode
from core.model.node.type import NodeType
from core.model.path import DevicePath
from core.web.websocket.path.feed import FeedWebSocket
from core.web.websocket.path.logs import RollingLogsWebSocket
from core.web.websocket.path.status import StatusWebSocket
from lib.common.commander.client import CommanderClient
from lib.common.devices.registry import DeviceRegistry
from lib.common.generation import is_slayer
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import Topic
from lib.common.time import maka_control_timestamp_ms
from lib.common.web.http.router import Router

LOG = get_logger(__name__)


class DuplicateRobotDetectedError(Exception):
    """
    Raises when a duplicate robot instance is detected, whether that is in the same process or another.

    Only a single Robot instance should be alive for a single host.
    """

    pass


class Robot(ClassicNode):
    def __init__(self, *argv: Any, args: Namespace, device_registry: DeviceRegistry, **kwargs: Any):
        LOG.info("Initializing Robot...")
        super().__init__(*argv, device_path=DevicePath.robot(), **kwargs)
        self.args: Namespace = args
        self._device_registry = device_registry

        self._tree: Dict[DevicePath, ReferenceId] = {k: v.id for k, v in self.subtree.items()}
        self._tree[self.device_path] = self.id

        self._laser_enabled: bool = False
        self._config_file: CalibrationConfigFile = CalibrationConfigFile()
        self._web_server: Optional[Router] = None

        self._logs_websocket: Optional[RollingLogsWebSocket] = None
        self._status_websocket: Optional[StatusWebSocket] = None
        self._feed_websocket: Optional[FeedWebSocket] = None

        self._commander_addr = "*********" if is_slayer() else "127.0.0.1"
        row: str = str(os.getenv("MAKA_ROW")) if os.getenv("MAKA_ROW") is not None else "1"
        self.row_number = int(row) if row.isdigit() else 1
        self.commander_client: CommanderClient = CommanderClient(self._commander_addr)
        self.config_client: ConfigClient = ConfigClient(f"{self._commander_addr}:61001")
        self.async_loop = lib.common.tasks.manager.get_event_loop_by_name()

        self._stopped = threading.Event()

        # Done
        LOG.info("Initialized Robot.")

    @property
    def access_policy(self) -> NodeAccessPolicy:
        return NodeAccessPolicy.PRIVATE

    @property
    def topic(self) -> Topic:
        return Topic.root()

    def get_cameras_for_ui(self) -> Dict[str, Dict[str, DevicePath]]:
        # This is an inelegant way for the UI to know about our camera dependency injections into Actuators
        # We should work towards making these this more declarative, but there are a variety of special cases
        # making that difficult when this was committed
        result = {}

        for dp, obj in self.subtree.items():
            if hasattr(obj, "get_cameras_for_ui"):
                obj_cam_tree = {}
                for cam_id in obj.get_cameras_for_ui():
                    # sloppy but this needs a total rework. Just
                    cam_dp = self._lookup_device_path_by_id(cam_id)
                    assert cam_dp is not None, "Impossible code path - developer error"
                    obj_cam_tree[str(cam_dp)] = cam_id
                result[str(dp)] = obj_cam_tree
        return result

    def _lookup_device_path_by_id(self, ref_id: ReferenceId) -> Optional[DevicePath]:
        for dp, obj in self.subtree.items():
            if ref_id == obj.id:
                return dp
        return None

    def web_shutdown(self, delay_ms: int = 0) -> None:
        # immediately stop
        LOG.debug("web_shutdown called")

        # mark robot as done
        self._stopped.set()

        # shutdown web server (will cause serving loop to exit)
        if self._web_server:
            self._web_server.shutdown()

    def _cache_named_references(self) -> None:
        def get_node_or_log_warning(node_type: NodeType) -> Optional[Node]:
            result: Optional[Node] = self.get_node(node_type)
            if result is None:
                LOG.warning(f"Initializing with no {node_type}")
            return result

        self._driver: Optional["Driver"] = cast(Optional["Driver"], get_node_or_log_warning(NodeType.DRIVER))
        self._exterminator: Optional[Exterminator] = cast(
            Optional[Exterminator], get_node_or_log_warning(NodeType.EXTERMINATOR)
        )
        self._exterminator_proxy: Optional[ExterminatorProxy] = ExterminatorProxy(
            self._exterminator
        ) if self._exterminator is not None else None
        self._commander: Optional[CommanderNode] = cast(
            Optional[CommanderNode], get_node_or_log_warning(NodeType.COMMANDER)
        )
        self._frame: Optional[Frame] = cast(Optional[Frame], get_node_or_log_warning(NodeType.FRAME))
        self._retina: Optional[Retina] = cast(Optional[Retina], get_node_or_log_warning(NodeType.RETINA))
        self._visual_cortex: Optional[VisualCortex] = cast(
            Optional[VisualCortex], get_node_or_log_warning(NodeType.VISUAL_CORTEX)
        )

    @property
    def driver(self) -> Optional[Driver]:
        return self._driver

    @property
    def exterminator(self) -> Optional[Exterminator]:
        return self._exterminator

    @property
    def exterminator_proxy(self) -> Optional[ExterminatorProxy]:
        return self._exterminator_proxy

    @property
    def frame(self) -> Optional[Frame]:
        return self._frame

    @property
    def commander(self) -> Optional[CommanderNode]:
        return self._commander

    @property
    def retina(self) -> Optional[Retina]:
        return self._retina

    @property
    def visual_cortex(self) -> Optional[VisualCortex]:
        return self._visual_cortex

    @property
    def tree(self) -> Dict[DevicePath, ReferenceId]:
        return self._tree

    def status_callback(self) -> Dict[str, Any]:
        result = {}

        dp: DevicePath
        obj: Node
        for dp, obj in sorted(self.subtree.items()):
            result[str(dp)] = obj.status_callback()

        return result

    def setup_async(self, loop: asyncio.BaseEventLoop) -> None:
        assert self._logs_websocket is None
        assert self._status_websocket is None
        assert self._feed_websocket is None

        self._logs_websocket = RollingLogsWebSocket()
        self._status_websocket = StatusWebSocket(bot=self)
        self._feed_websocket = FeedWebSocket(feed=self.feed)

        self._logs_websocket.setup_async(loop)
        self._status_websocket.setup_async(loop)
        self._feed_websocket.setup_async(loop)

    def shutdown_async(self) -> None:
        if not self._logs_websocket:
            return

        self._logs_websocket.shutdown_async()
        self._logs_websocket = None

        assert self._status_websocket is not None
        self._status_websocket.shutdown_async()
        self._status_websocket = None

        assert self._feed_websocket is not None
        self._feed_websocket.shutdown_async()
        self._feed_websocket = None

    def serve_forever(self, block: bool = True, http_port: int = HTTP_PORT) -> None:
        from web.maka_routes import bot_routes

        FeedRouterBuilder(feed=self.feed).add_onto(bot_routes)

        if self.driver is not None:
            DriverRouterBuilder(driver=self.driver).add_onto(bot_routes)

        if self.exterminator is not None:
            ExterminatorRouterBuilder(exterminator=self.exterminator).add_onto(bot_routes)

        if self.frame is not None:
            FrameRouterBuilder(frame=self.frame).add_onto(bot_routes)

        if self.retina is not None:
            RetinaRouterBuilder(retina=self.retina).add_onto(bot_routes)

        self._web_server = bot_routes.build_with(bot=self)(("", http_port))
        if self.visual_cortex is not None:
            VisualCortexRouterBuilder(visual_cortex=self.visual_cortex).add_onto(bot_routes)

        # start asyncio setup
        loop: asyncio.BaseEventLoop = cast(asyncio.BaseEventLoop, asyncio.new_event_loop())
        asyncio.set_event_loop(loop)

        dp: DevicePath
        obj: Node
        for dp, obj in self.subtree.items():
            obj.setup_async(loop)

        self.setup_async(loop)
        self.add_subtask(lib.common.tasks.start(name="web/sockets", target=loop.run_forever))
        self.add_subtask(lib.common.tasks.start(name="web/http", target=self._web_server.serve_forever))

    def is_manually_commandable(self) -> bool:
        assert self.commander
        return True

    def config_save(self, debug: bool = False) -> None:
        """
        Backup the currently saved config to the config folder, then saves the currently loaded calibration config.
        """
        LOG.info("Saving config...")
        assert self._exterminator_proxy is not None
        config = {}
        device_path, postfix, exterminator_config = self._exterminator_proxy.config_save(debug=debug)
        config[str(device_path)] = exterminator_config
        LOG.info("Writing Config...")
        self._config_file.save(config, backup_postfix=postfix)
        LOG.info("Saved config.")

    def stop(self) -> None:
        # No web server means no start()
        if self._web_server:
            self._web_server.shutdown()
            self.shutdown_async()

        super().stop()

        retry_wait_until_complete = {}
        for dp, obj in self.subtree.items():
            if obj.task is not None:
                timeout_ms = 100
                if not obj.task.wait_until_complete(timeout_ms=timeout_ms):
                    LOG.warning(f"{dp} slow task did not finish within {timeout_ms:.0f}ms of cancellation!!!")
                    retry_wait_until_complete[dp] = obj

        # Wait for slow devices before terminating
        # e.g. simulated camera threads running on Macbook Air
        timeout_ms = 3000
        now = maka_control_timestamp_ms()
        timeout = now + timeout_ms
        for dp, obj in retry_wait_until_complete.items():
            now = maka_control_timestamp_ms()
            wait_ms = max(0, timeout - now)
            assert obj.task
            if obj.task.done:
                LOG.info(f"{dp} is now finished.")
                continue

            LOG.info(f"Waiting additional {wait_ms}ms for {dp} shutdown...")
            if not obj.task.wait_until_complete(timeout_ms=wait_ms):
                LOG.critical(f"{dp} SLOW TASK DID NOT FINISH WITHIN {wait_ms}ms OF CANCELLATION!!!")

        # Stop Device Registry
        self._device_registry.stop_all_devices_from_sync()

        # stop the world
        tasks = list(lib.common.tasks.list_tasks())
        orphan_tasks = [o for o in tasks if not o.done]
        LOG.warning(f"Found {len(orphan_tasks)} orphan tasks not done (total: {len(tasks)})")
        if len(orphan_tasks) > 0:
            LOG.info("Force stopping all tasks")
            lib.common.tasks.stop_all()

            # now wait
            for task in orphan_tasks:
                timeout_ms = 100
                if not task.wait_until_complete(timeout_ms=timeout_ms):
                    LOG.warning(f"{task.name} failed to terminate despite cancellation request")

from typing import Any, Dict, List, Optional, Tuple, cast

import core.unity.proto.helacs.helac_pb2 as proto_helac
import core.unity.proto.helacs.helac_pb2_grpc as grpc_helac
import core.unity.unity as unity
from core.controls.driver.drivebot.client.client import DrivebotClientSynchronous
from core.controls.driver.hydraulics.hydraulics import Hydraulics
from core.controls.driver.rotary import RotaryNode
from core.model.node.base import Node
from core.model.node.type import NodeType
from core.model.path import Instance, RelativeInstance
from core.unity.constants import DEFAULT_UNITY_HYDRAULICS_FORWARD_DEAD_ZONE
from core.unity.drivers.unity_motor import UnityMotor
from lib.common.logging import get_logger

LOG = get_logger(__name__)


def instance_name(n: Node) -> str:
    instance: Optional[Instance] = n.device_path.leaf().instance
    assert instance
    return str(instance)


class UnityHydraulics(Hydraulics):
    def __init__(
        self,
        *argv: Any,
        unity_id: str,
        wheels_static_friction_threshold: float = DEFAULT_UNITY_HYDRAULICS_FORWARD_DEAD_ZONE,
        **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)
        self._unity_id = unity_id
        self._speed_mph: float = 0.0
        self._client = grpc_helac.HelacStub(unity.get_unity_connector().channel)
        self._load_devices_config()
        self._load_rotaries_position()

        assert 0 <= wheels_static_friction_threshold < 1
        self._wheels_static_friction_threshold = wheels_static_friction_threshold
        unity_connector = unity.get_unity_connector()
        self._drivebot = DrivebotClientSynchronous(hostname=unity_connector.host, port=unity_connector.port)

    def _load_devices_config(self) -> None:
        result: Dict[str, proto_helac.RotaryConfig] = {}
        for r in self._rotaries.values():
            rotary_id = proto_helac.RotaryId(id=r.id)
            config: proto_helac.RotaryConfig = self._client.GetRotaryConfig(rotary_id)
            result[instance_name(r)] = config

        # update all at once, atomically to avoid race condition with status callback
        self._rotaries_config = result

    def _load_rotaries_position(self) -> None:
        result: Dict[str, float] = {}
        for r in self._rotaries.values():
            rotary_id = proto_helac.RotaryId(id=r.id)
            value: proto_helac.HelacValue = self._client.GetValue(rotary_id)
            result[instance_name(r)] = value.value

        # update all at once, atomically to avoid race condition with status callback
        self._rotaries_position = result

    def _cache_named_references(self) -> None:
        self._back_left_motor: UnityMotor = cast(
            UnityMotor, self.get_node(NodeType.DRIVE_MOTOR, instance=RelativeInstance.BACK_LEFT, required=True)
        )
        self._back_right_motor: UnityMotor = cast(
            UnityMotor, self.get_node(NodeType.DRIVE_MOTOR, instance=RelativeInstance.BACK_RIGHT, required=True)
        )
        self._front_left_motor: UnityMotor = cast(
            UnityMotor, self.get_node(NodeType.DRIVE_MOTOR, instance=RelativeInstance.FRONT_LEFT, required=True)
        )
        self._front_right_motor: UnityMotor = cast(
            UnityMotor, self.get_node(NodeType.DRIVE_MOTOR, instance=RelativeInstance.FRONT_RIGHT, required=True)
        )
        self._front_left_rotary: RotaryNode = cast(
            RotaryNode, self.get_node(NodeType.ROTARY, instance=RelativeInstance.FRONT_LEFT, required=True)
        )
        self._front_right_rotary: RotaryNode = cast(
            RotaryNode, self.get_node(NodeType.ROTARY, instance=RelativeInstance.FRONT_RIGHT, required=True)
        )
        self._wheels: List[UnityMotor] = [
            self._front_left_motor,
            self._front_right_motor,
            self._back_right_motor,
            self._back_left_motor,
        ]
        self._rotaries: Dict[str, RotaryNode] = {
            instance_name(self._front_left_rotary): self._front_left_rotary,
            instance_name(self._front_right_rotary): self._front_right_rotary,
        }

    @property
    def forward(self) -> float:
        return self._speed_mph

    @property
    def rotaries(self) -> List[str]:
        return list(self._rotaries.keys())

    @property
    def rotary_position_frame_angles_deg(self) -> Dict[str, float]:
        self._load_rotaries_position()
        res: Dict[str, float] = {}
        for r in self._rotaries:
            res[r] = self._rotaries_position[r]
        return res

    @property
    def rotary_limits_frame_angles_deg(self) -> Dict[str, Tuple[float, float]]:
        res: Dict[str, Tuple[float, float]] = {}
        for r in self._rotaries:
            c = self._rotaries_config[r]
            res[r] = cast(Tuple[float, float], [c.min_angle, c.max_angle])
        return res

    @property
    def wheels(self) -> List[str]:
        return [instance_name(w) for w in self._wheels]

    def drive(self, speed_mph: float) -> None:
        self._speed_mph = speed_mph
        self._drivebot.drive(caller_id="unity", velocity_mph=speed_mph)

    def set_rotary_position(self, *, rotary: str, frame_angle_deg: float, log: bool = False) -> None:
        if log:
            LOG.debug(f"{self.device_path} rotary {rotary} -> {frame_angle_deg}")
        self._rotaries[rotary].set_angle_deg(frame_angle_deg)

    def notify_positive_control(self) -> None:
        pass

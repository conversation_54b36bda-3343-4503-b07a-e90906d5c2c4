from typing import Any

import core.unity.proto.helacs.helac_pb2 as proto_helac
import core.unity.proto.helacs.helac_pb2_grpc as grpc_helac
import core.unity.unity as unity
from core.controls.driver.rotary import RotaryNode


class UnityHelac(RotaryNode):
    def __init__(self, *args: Any, unity_id: str, **kwargs: Any):
        super().__init__(*args, **kwargs)
        assert unity_id is not None
        self._unity_id = unity_id
        self._client = grpc_helac.HelacStub(unity.get_unity_connector().channel)
        self._val: float = 0

    @property
    def angle_deg(self) -> float:
        return self._val

    def set_angle_deg(self, frame_angle_deg: float) -> None:
        scaled_to_0_1 = frame_angle_deg / 45
        self._client.SetValue(proto_helac.HelacValue(id=self._unity_id, value=scaled_to_0_1))
        self._val = scaled_to_0_1

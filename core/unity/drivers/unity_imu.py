from typing import Any, <PERSON><PERSON>

import numpy as np

import core.unity.proto.imus.imu_pb2 as proto_imu
import core.unity.proto.imus.imu_pb2_grpc as grpc_imu
import core.unity.unity as unity
import lib.common.logging
from core.controls.frame.sensor.imu.node import IMUNode
from core.model.sensor import Sensor
from generated.core.controls.frame.model.acceleration_message import AccelerationMessage
from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
from generated.core.controls.frame.model.heading_message import HeadingMessage
from lib.common.time import maka_control_timestamp_ms

LOG = lib.common.logging.get_logger(__name__)

SAMPLE_DIMENSIONS = Sensor.DEFAULT_SENSOR_DIMENSIONS


class UnityIMUNode(IMUNode):
    def __init__(self, *args: Any, unity_id: str, **kwargs: Any):
        assert unity_id is not None
        super().__init__(*args, **kwargs)

        self._client = grpc_imu.IMUStub(unity.get_unity_connector().channel)
        self._unity_id: str = unity_id

    @property
    def heading(self) -> HeadingMessage:
        """
        Request grpc unity resolution on demand. If this becomes a bottleneck we could periodically poll
        on a thread and return the cached value.
        """
        ret: proto_imu.IMUResponse = self._client.GetHeading(proto_imu.IMURequest(id=self._unity_id))
        return HeadingMessage(timestamp_ms=maka_control_timestamp_ms(), heading=float(ret.heading))

    @property
    def acceleration(self) -> AccelerationMessage:
        return AccelerationMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def angular_velocity(self) -> AngularVelocityMessage:
        return AngularVelocityMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def accel_gyro(self) -> Tuple[int, np.ndarray, np.ndarray]:
        return maka_control_timestamp_ms(), np.zeros(SAMPLE_DIMENSIONS), np.zeros(SAMPLE_DIMENSIONS)

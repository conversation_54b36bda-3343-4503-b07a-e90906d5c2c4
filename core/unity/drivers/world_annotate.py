from typing import Dict, List, Optional, Tuple, cast

import navpy
import numpy as np

import core.unity.proto.worldannotate.world_annotate_pb2_grpc as grpc_world_annotate
from core.controls.driver.plan.field_location_notifier import FieldLocationNotifier
from core.unity import unity
from core.unity.proto.worldannotate.world_annotate_pb2 import (
    ClearGroupRequest,
    DrawBallRequest,
    DrawLineRequest,
    EcefPoint,
    LineType,
)
from lib.common.geo import ecef_utils
from lib.common.geo.boundary import Boundary
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class UnityWorldAnnotate:
    def __init__(self) -> None:
        self._client = grpc_world_annotate.WorldAnnotateStub(unity.get_unity_connector().channel)

    def draw_line(
        self, group: str, start_ecef: Tuple[float, float, float], end_ecef: Tuple[float, float, float], type: LineType
    ) -> None:
        self._client.DrawLine(
            DrawLineRequest(
                start=EcefPoint(x=start_ecef[0], y=start_ecef[1], z=start_ecef[2]),
                end=EcefPoint(x=end_ecef[0], y=end_ecef[1], z=end_ecef[2]),
                type=type,
                group=group,
            )
        )

    def add_geo_ball(self, group: str, point: np.ndarray) -> None:
        self._client.DrawBall(DrawBallRequest(ball=EcefPoint(x=point[0], y=point[1], z=point[2]), group=group))

    def clear_group(self, group: str) -> None:
        self._client.ClearGroup(ClearGroupRequest(group=group))


class UnityFieldLocationNotifier(FieldLocationNotifier):
    def __init__(self) -> None:
        self._world_annotate = UnityWorldAnnotate()

    def notify_eorta(self, vector: ecef_utils.EcefVector) -> None:
        start_ecef, end_ecef = (vector.start, vector.end)
        start_ecef = ecef_utils.fixed_alt(start_ecef, 0.8)
        end_ecef = ecef_utils.fixed_alt(end_ecef, 0.8)
        self._world_annotate.draw_line(
            "entry_vector",
            cast(Tuple[float, float, float], tuple(*start_ecef)),
            cast(Tuple[float, float, float], tuple(*end_ecef)),
            LineType.TARGET,
        )

    def notify_furrow(self, vector: ecef_utils.EcefVector) -> None:
        pass

    def _send_lines(self, fences: Dict[str, Boundary], alt: float, type: LineType) -> None:
        lines: List[Tuple[Tuple[float, float], Tuple[float, float]]] = []
        # The values coming out of Boundary are in string format. This should be revisited.
        first: Optional[Tuple[str, str]] = None
        last: Optional[Tuple[str, str]] = None

        for boundary in fences.values():
            for point in boundary.coordinates:
                if last is None:
                    first = point
                else:
                    lines.append(((float(last[1]), float(last[0])), (float(point[1]), float(point[0]))))
                last = point
            if first is not None and last is not None:
                lines.append(((float(last[1]), float(last[0])), (float(first[1]), float(first[0]))))
            first = None
            last = None

        for line in lines:
            start = line[0]
            end = line[1]
            start_ecef = navpy.lla2ecef(start[0], start[1], alt, "deg")
            end_ecef = navpy.lla2ecef(end[0], end[1], alt, "deg")
            self._world_annotate.draw_line("field", start_ecef, end_ecef, type)

    def set_field_geofence(self, field: Dict[str, Boundary]) -> None:
        self._send_lines(field, 0.5, LineType.FIELD)

    def set_turn_around_geofence(self, ta: Dict[str, Boundary]) -> None:
        self._send_lines(ta, 1.0, LineType.TURN_AROUND)

    def clear_geofences(self) -> None:
        self._world_annotate.clear_group("field")

    def add_geo_ball(self, point: np.ndarray) -> None:
        self._world_annotate.add_geo_ball("route", point)

    def add_path_vector(self, vector: ecef_utils.EcefVector) -> None:
        self._world_annotate.draw_line(
            "path",
            cast(Tuple[float, float, float], tuple(*vector.start)),
            cast(Tuple[float, float, float], tuple(*vector.end)),
            LineType.TARGET,
        )

    def clear_path_vectors(self) -> None:
        self._world_annotate.clear_group("path")
        self._world_annotate.clear_group("target")

    def clear_route_vectors(self) -> None:
        self._world_annotate.clear_group("route")
        self._world_annotate.clear_group("entry_vector")

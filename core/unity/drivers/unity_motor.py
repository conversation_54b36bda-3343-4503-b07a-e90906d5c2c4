from typing import Any

import core.unity.proto.motors.wheel_motor_pb2 as proto_motor
import core.unity.proto.motors.wheel_motor_pb2_grpc as grpc_motor
import core.unity.unity as unity
import lib.common.logging
from core.controls.driver.motor import DriveMotor

LOG = lib.common.logging.get_logger(__name__)


class UnityMotor(DriveMotor):
    def __init__(self, *argv: Any, unity_id: str, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        assert unity_id is not None
        self._unity_id = unity_id
        self._client = grpc_motor.WheelMotorStub(unity.get_unity_connector().channel)
        self._speed_mph: float = 0

    @property
    def speed_mph(self) -> float:
        return self._speed_mph

    def set_speed_mph(self, speed_mph: float) -> None:
        self._client.SetSpeed(proto_motor.MotorSpeed(id=self._unity_id, speed_mph=speed_mph))
        self._speed_mph = speed_mph

    def stop(self) -> None:
        super().stop()
        self.set_speed_mph(0)

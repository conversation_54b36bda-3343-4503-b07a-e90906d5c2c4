import asyncio
import math
import random
from typing import Any, Callable, Optional, Tuple

import numpy as np
import pyquaternion as pquat

import core.unity.proto.ins.ins_pb2 as proto_ins
import core.unity.proto.ins.ins_pb2_grpc as grpc_ins
import lib.common.logging
import lib.common.tasks
from core.controls.frame.sensor.ins.node import INSNode
from core.model.sensor import Sensor
from core.unity import unity
from core.unity.constants import DEFAULT_UNITY_GPS_OUTAGE_FREQ_MS, DEFAULT_UNITY_GPS_OUTAGE_LENGTH_MS
from generated.core.controls.frame.model.acceleration_message import AccelerationMessage
from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.heading_message import HeadingMessage
from generated.core.controls.frame.model.mag_message import MagMessage
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.quaternion import quat2euler
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.tasks.scheduler import dispatch
from lib.common.time import maka_control_timestamp_ms

LOG = lib.common.logging.get_logger(__name__)

SAMPLE_DIMENSIONS = Sensor.DEFAULT_SENSOR_DIMENSIONS
ABSOLUTE_GPS_ERROR = 0.3
RELATIVE_GPS_SIGMA = 0.05
MAX_RELATIVE_GPS_ERROR = 0.2

GPS_X_OFFSET = -ABSOLUTE_GPS_ERROR if random.random() < 0.5 else ABSOLUTE_GPS_ERROR
GPS_Y_OFFSET = -ABSOLUTE_GPS_ERROR if random.random() < 0.5 else ABSOLUTE_GPS_ERROR
GPS_Z_OFFSET = -ABSOLUTE_GPS_ERROR if random.random() < 0.5 else ABSOLUTE_GPS_ERROR


class UnityINSNode(INSNode):
    """
    Device to receive PoseEstimator data from unity server and hand it back to PoseEstimator. This does
    more than a regular INS in that it also hands back simulator derived heading and velocity. This device
    is not a Magnetometer nor a Velocimeter because those things had back raw device information that one
    could use to calculate a heading or velocity - we don't need that because we already know.
    """

    def __init__(
        self,
        *argv: Any,
        unity_id: str,
        gps_outage_freq_ms: int = DEFAULT_UNITY_GPS_OUTAGE_FREQ_MS,
        gps_outage_length_ms: int = DEFAULT_UNITY_GPS_OUTAGE_LENGTH_MS,
        **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)
        self._gps_outage_freq_ms: Callable[[], int] = lambda: gps_outage_freq_ms
        self._gps_outage_length_ms: Callable[[], int] = lambda: gps_outage_length_ms
        assert self._gps_outage_freq_ms() >= self._gps_outage_length_ms()

        self._client = grpc_ins.INSStub(unity.get_unity_connector().channel)
        self._unity_id = unity_id

        # These values must be set before task starts
        self._last_poll = maka_control_timestamp_ms()
        self._poll_rate_hz = 10

        # Data
        # TODO Use proper mathematical data structures
        self._data: Optional[proto_ins.INSDataResponse] = None
        now = maka_control_timestamp_ms()
        self._latest_heading_message = HeadingMessage(timestamp_ms=now, heading=0)

    # TODO workaround until we can boot directly with the correct callback reference
    def set_gps_outage_freq_ms_callback(self, c: Callable[[], int]) -> None:
        self._gps_outage_freq_ms = c

    # TODO workaround until we can boot directly with the correct callback reference
    def set_gps_outage_length_ms_callback(self, c: Callable[[], int]) -> None:
        self._gps_outage_length_ms = c

    def _control_loop(self) -> None:
        """
        Main Unity INS polling loop - never terminates.
        """
        LOG.debug(f"{self.device_path} Starting Unity INS Thread...")
        asyncio.run_coroutine_threadsafe(
            dispatch(
                name=str(self.device_path), callback=self._update_data, poll_ms=int(round(1000 / self._poll_rate_hz))
            ),
            get_event_loop_by_name(),
        ).result()
        assert False, "Unity INS Loop Should Not Exit"

    async def _update_data(self) -> None:
        ins_data_response = self._client.GetData(proto_ins.INSRequest(id=self._unity_id))
        self._data = ins_data_response
        # Position is in ECEF (Meters)
        # Velocity is in ECEF (Meters per second)
        # Quaternion is a relative quaternion
        # Accel is relative (Meters per second^2)
        # Gyro is relative (Degrees per second)

        # Unity uses ZXY euler conventions
        # The unity coordinate axis are different than pyquaternion expects so "pitch" is actually yaw.
        now = maka_control_timestamp_ms()
        pitchindex = 1
        q = pquat.Quaternion(
            w=self._data.quaternion.w, x=self._data.quaternion.x, y=self._data.quaternion.y, z=self._data.quaternion.z
        )
        heading = (quat2euler(q, convention="ZXY")[pitchindex] * (180 / math.pi)) % 360
        self._latest_heading_message = HeadingMessage(timestamp_ms=now, heading=heading)

    def _relative_fuzz_gps(self) -> float:
        return float(
            np.clip(random.gauss(mu=0, sigma=RELATIVE_GPS_SIGMA), -MAX_RELATIVE_GPS_ERROR, MAX_RELATIVE_GPS_ERROR)
        )

    @property
    def geoposition_ecef_m(self) -> GeopositionEcefMessage:
        now = maka_control_timestamp_ms()
        gps_outage_freq_ms = self._gps_outage_freq_ms()
        gps_outage_length_ms = self._gps_outage_length_ms()
        if self._data is None or (gps_outage_freq_ms > 0 and now % gps_outage_freq_ms < gps_outage_length_ms):
            return GeopositionEcefMessage(timestamp_ms=now, x=0, y=0, z=0)
        return GeopositionEcefMessage(
            timestamp_ms=now,
            x=self._data.position.x + GPS_X_OFFSET + self._relative_fuzz_gps(),
            y=self._data.position.y + GPS_Y_OFFSET + self._relative_fuzz_gps(),
            z=self._data.position.z + GPS_Z_OFFSET + self._relative_fuzz_gps(),
        )

    @property
    def mag(self) -> MagMessage:
        return MagMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def velocity(self) -> VelocityMessage:
        return VelocityMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def heading(self) -> HeadingMessage:
        return self._latest_heading_message

    #
    # IMU Methods
    #

    @property
    def acceleration(self) -> AccelerationMessage:
        return AccelerationMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def angular_velocity(self) -> AngularVelocityMessage:
        return AngularVelocityMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)

    @property
    def accel_gyro(self) -> Tuple[int, np.ndarray, np.ndarray]:
        return lib.common.time.maka_control_timestamp_ms(), np.zeros(SAMPLE_DIMENSIONS), np.zeros(SAMPLE_DIMENSIONS)

from typing import Any, Optional, <PERSON><PERSON>

import cv2
import numpy as np

import core.unity.proto.cameras.camera_pb2 as proto_camera
import core.unity.proto.cameras.camera_pb2_grpc as grpc_camera
import core.unity.unity as unity
import lib.common.logging
import lib.common.tasks
from core.cv.retina.camera.node import Cam
from lib.common.image import apply_transforms
from lib.common.tasks import MakaTask
from lib.common.time import maka_control_timestamp_ms
from lib.common.time.sleep import sleep_ms

LOG = lib.common.logging.get_logger(__name__)


class UnityCamProperties:
    def get_all(self) -> None:
        return None


class UnityCamera(Cam):
    def __init__(self, *args: Any, unity_id: str, **kwargs: Any):
        assert unity_id is not None
        super().__init__(*args, **kwargs)

        self._client = grpc_camera.CameraStub(unity.get_unity_connector().channel)
        self._properties = UnityCamProperties()
        self._unity_id = unity_id
        self._width_px: int = 0
        self._height_px: int = 0

    @property
    def resolution(self) -> Tuple[int, int]:
        return self._width_px, self._height_px

    def _control_loop(self) -> None:
        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None
        while True:
            task.tick()
            try:
                ret = self._client.GetImage(proto_camera.CameraRequest(id=self._unity_id))
                timestamp_ms = ret.timestamp
                # TODO Fix Unity timestamp bug
                trusted_timestamp_ms = maka_control_timestamp_ms()
                if abs(timestamp_ms - trusted_timestamp_ms) > 60 * 1000:
                    timestamp_ms = trusted_timestamp_ms
                else:
                    LOG.warning("Did Unity camera timestamp bug get fixed? We can remove the if statement here.")
                npbuf = np.frombuffer(ret.bytes, np.byte)

                # Reshape to w x h x [BGRA] and chop off the A (these alpha are all -1 anyway)
                img = npbuf.reshape([ret.size.height, ret.size.width, -1])[:, :, :3]
                img = img.astype(np.uint8)

                # Resize to 1920 x ???
                self._width_px = 1920
                self._height_px = int(ret.size.height * self._width_px / ret.size.width)
                img = cv2.resize(img, (self._width_px, self._height_px))

                # Convert to type and rotate to orientation
                img = apply_transforms(img, flip=True)

                # Publish to subscribers
                self._new_frame(img, frame_timestamp_ms=timestamp_ms)
            except Exception:
                LOG.exception("unity cam loop")
            sleep_ms(100)

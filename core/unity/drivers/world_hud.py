import core.unity.proto.hud.hud_pb2_grpc as grpc_hud
from core.controls.driver.plan.world_hud import WorldHUD
from core.unity import unity
from core.unity.proto.hud.hud_pb2 import SetTextRequest


class UnityWorldHUD(WorldHUD):
    def __init__(self) -> None:
        self._client = grpc_hud.HUDStub(unity.get_unity_connector().channel)

    def set_text(self, text: str) -> None:
        self._client.SetText(SetTextRequest(text=text))

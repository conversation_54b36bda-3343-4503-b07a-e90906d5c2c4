from typing import Any

import core.unity.proto.rotaryencoders.rotary_encoders_pb2 as proto_encoder
import core.unity.proto.rotaryencoders.rotary_encoders_pb2_grpc as grpc_encoder
import core.unity.unity as unity
from core.controls.driver.rotary_encoder import RotaryEncoderNode
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class UnityRotaryEncoder(RotaryEncoderNode):
    def __init__(self, unity_id: str, *args: Any, **kwargs: Any):
        super().__init__(*args, **kwargs)
        assert unity_id is not None
        self._unity_id = unity_id
        self._client = grpc_encoder.RotaryEncoderStub(unity.get_unity_connector().channel)
        resp: proto_encoder.RotaryEncoderConfig = self._client.GetRotaryEncoderConfig(
            proto_encoder.RotaryEncoderRequest(id=self._unity_id)
        )
        self._wheel_ticks_per_revolution: int = resp.TPR
        self._last_timestamp_ms: int = 0

    @property
    async def ticks(self) -> int:
        # Would be nice to use async grpc here - when that works with asyncio we will
        resp: proto_encoder.RotaryEncoderTicks = self._client.GetRotaryEncoderTicks(
            proto_encoder.RotaryEncoderRequest(id=self._unity_id)
        )
        self._last_timestamp_ms = resp.timeMS
        return int(resp.ticks)

    @property
    def timestamp_ms(self) -> int:
        return self._last_timestamp_ms

    @property
    def TPR(self) -> int:
        return self._wheel_ticks_per_revolution

import json
import subprocess
import tempfile
import time
from signal import SIGINT

import pytest

import websocket
from core.web.websocket.defaults import STATUS_PORT

WAIT_UNTIL_SECS = 60

pytestmark = pytest.mark.unity


def bringup_and_query(robot: str) -> None:
    botd_process = None
    unity = None
    idir = None
    try:
        # Start unity response server
        unity = subprocess.Popen(f"python -m core.unity.test.unity_response_server --robot {robot}".split())
        retcode = unity.poll()
        assert retcode is None

        # Start bot
        idir = tempfile.TemporaryDirectory()
        tmpdir_params = f"--fs-force-tmp-dir {idir.name} --logfile {idir.name + '/robot.log'} --data-logfile {idir.name + '/data.log'}"
        unity_args = "--unity --unity-heading-std-dev 0 --unity-gps-outage-freq-ms 0 --unity-gps-outage-length-ms 0"
        botd_process = subprocess.Popen(f"python -m gil_load botd.py {unity_args} {tmpdir_params}".split())
        retcode = botd_process.poll()
        assert retcode is None

        # Connect a websocket being mindful of the fact that the server may not be up yet
        ws = websocket.WebSocket()
        start_time = time.time()
        while True:  # need to wait until the server is up
            assert botd_process.poll() is None
            try:
                ws.connect(f"ws://0.0.0.0:{STATUS_PORT}")
                break
            except ConnectionRefusedError:
                if time.time() - start_time > WAIT_UNTIL_SECS:
                    raise
                time.sleep(0.200)  # wait 200ms for other processes to go

        # Ask for current bot status
        ws.send(json.dumps({"data": {"freq_hz": 1}, "msg_type": "hello"}))
        ws.close()

        # Pull the heading out
        # res_dict = json.loads(result)
        # heading = float(res_dict["/frame"]["heading"])

        # This value comes from the hard coded data in test.unity_response_server
        # assert heading == 42.61401659558411

        # Pull the position out
        # lat, lon, _ = res_dict["/frame"]["antenna_lla"]

        # assert lat == 47.619100454434665 and lon == -122.35906585577334

    finally:
        # Shut down bot first because it's trying to talk to unity and has problems if it goes away
        # SIGINT is ^C
        if botd_process is not None and botd_process.poll() is None:
            botd_process.send_signal(SIGINT)
            try:
                botd_process.wait(10)
            except subprocess.TimeoutExpired:
                # If it's not down by now then be more forceful
                botd_process.terminate()
                botd_process.wait()

        if unity is not None and unity.poll() is None:
            unity.terminate()
            unity.wait()

        if idir is not None:
            idir.cleanup()


def test_bringup_and_query_veggie() -> None:
    bringup_and_query("veggie")

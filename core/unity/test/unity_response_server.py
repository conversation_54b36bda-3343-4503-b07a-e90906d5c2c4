#!/usr/bin/env python

import argparse
from concurrent import futures
from typing import Any

import grpc
import numpy as np

from core.unity.constants import DEFAULT_UNITY_PORT
from core.unity.proto.cameras.camera_pb2 import CameraRequest, ImageData, Size
from core.unity.proto.cameras.camera_pb2_grpc import Camera<PERSON>ervicer, add_CameraServicer_to_server
from core.unity.proto.config.robot_config_pb2 import (
    <PERSON><PERSON><PERSON>,
    <PERSON>ceConfig,
    <PERSON><PERSON><PERSON>ce,
    GetConfigArgs,
    MakaConfig,
    Position,
)
from core.unity.proto.config.robot_config_pb2_grpc import RobotConfigServicer, add_RobotConfigServicer_to_server
from core.unity.proto.helacs.helac_pb2 import <PERSON>lac<PERSON><PERSON>ue, RotaryConfig, RotaryId
from core.unity.proto.helacs.helac_pb2_grpc import Hela<PERSON>Service<PERSON>, add_HelacServicer_to_server
from core.unity.proto.ins.ins_pb2 import INSDataResponse, INSRequest, Quaternion, Vector3, Vector3d
from core.unity.proto.ins.ins_pb2_grpc import INSServicer, add_INSServicer_to_server
from core.unity.proto.motors.wheel_motor_pb2 import MotorResponse, MotorSpeed
from core.unity.proto.motors.wheel_motor_pb2_grpc import WheelMotorServicer, add_WheelMotorServicer_to_server
from core.unity.proto.prediction.prediction_pb2 import CameraPredictionRequest, FurrowPredictionResponse
from core.unity.proto.prediction.prediction_pb2_grpc import PredictionServicer, add_PredictionServicer_to_server
from core.unity.proto.rotaryencoders.rotary_encoders_pb2 import (
    RotaryEncoderConfig,
    RotaryEncoderRequest,
    RotaryEncoderTicks,
)
from core.unity.proto.rotaryencoders.rotary_encoders_pb2_grpc import (
    RotaryEncoderServicer,
    add_RotaryEncoderServicer_to_server,
)
from core.unity.proto.worldannotate.world_annotate_pb2 import (
    ClearGroupRequest,
    ClearGroupResponse,
    DrawBallRequest,
    DrawBallResponse,
    DrawLineRequest,
    DrawLineResponse,
)
from core.unity.proto.worldannotate.world_annotate_pb2_grpc import (
    WorldAnnotateServicer,
    add_WorldAnnotateServicer_to_server,
)
from generated.core.controls.driver.drivebot.proto.drivebot_pb2 import DriveRequest, DriveResponse
from generated.core.controls.driver.drivebot.proto.drivebot_pb2_grpc import (
    DriveServiceServicer,
    add_DriveServiceServicer_to_server,
)

img_width = 800
img_height = 600

dc = DeviceConfig
devices = {
    "veggie": [
        dc(
            id="unity_veggiehydraulics",
            bootloader="unity_hydraulics",
            devicePath="/driver/drive_system/hydraulics",
            deviceType="hydraulics",
        ),
        dc(
            id="motor2",
            bootloader="unity_wheel_motor",
            devicePath="/driver/drive_system/hydraulics/drive_motor:back_right",
            deviceType="drive_motor",
        ),
        dc(
            id="motor3",
            bootloader="unity_wheel_motor",
            devicePath="/driver/drive_system/hydraulics/drive_motor:back_left",
            deviceType="drive_motor",
        ),
        dc(
            id="motor1",
            bootloader="unity_wheel_motor",
            devicePath="/driver/drive_system/hydraulics/drive_motor:front_right",
            deviceType="drive_motor",
        ),
        dc(
            id="motor0",
            bootloader="unity_wheel_motor",
            devicePath="/driver/drive_system/hydraulics/drive_motor:front_left",
            deviceType="drive_motor",
        ),
        dc(id="front_right", bootloader="unity_camera", devicePath="/retina/camera:front_right", deviceType="camera"),
        dc(id="predict1", bootloader="unity_camera", devicePath="/retina/camera:predict1", deviceType="camera"),
        dc(
            id="helac1",
            bootloader="unity_wheel_motor",
            devicePath="/driver/drive_system/hydraulics/rotary:front_right",
            deviceType="rotary",
        ),
        dc(
            id="helac0",
            bootloader="unity_wheel_motor",
            devicePath="/driver/drive_system/hydraulics/rotary:front_left",
            deviceType="rotary",
        ),
        dc(id="ins0", bootloader="unity_ins", devicePath="/frame/ins", deviceType="ins"),
        dc(id="imu0", bootloader="unity_imu", devicePath="/frame/imu", deviceType="imu"),
        dc(id="sim", bootloader=None, devicePath="/exterminator", deviceType="sim"),
        dc(id="target1", bootloader=None, devicePath="/retina/camera:target1", deviceType="sim"),
        dc(id="target2", bootloader=None, devicePath="/retina/camera:target2", deviceType="sim"),
        dc(id="target3", bootloader=None, devicePath="/retina/camera:target3", deviceType="sim"),
        dc(id="target4", bootloader=None, devicePath="/retina/camera:target4", deviceType="sim"),
        dc(
            id="rotary_encoder0",
            bootloader="unity_rotary_encoder",
            devicePath="/driver/drive_system/rotary_encoder:front_left",
            deviceType="rotary_encoder",
        ),
        dc(
            id="rotary_encoder1",
            bootloader="unity_rotary_encoder",
            devicePath="/driver/drive_system/rotary_encoder:front_right",
            deviceType="rotary_encoder",
        ),
        dc(
            id="rotary_encoder2",
            bootloader="unity_rotary_encoder",
            devicePath="/driver/drive_system/rotary_encoder:back_left",
            deviceType="rotary_encoder",
        ),
        dc(
            id="rotary_encoder3",
            bootloader="unity_rotary_encoder",
            devicePath="/driver/drive_system/rotary_encoder:back_right",
            deviceType="rotary_encoder",
        ),
    ],
}

geofence = Geofence(
    crops=Boundary(
        positions=[
            Position(x=-2305259.084404527, y=-3638249.8628898393, z=4688434.276443477),
            Position(x=-2305258.084404527, y=-3638249.8628898393, z=4688434.276443477),
            Position(x=-2305258.084404527, y=-3638248.8628898393, z=4688434.276443477),
        ]
    ),
    field=Boundary(
        positions=[
            Position(x=-2305259.084404527, y=-3638249.8628898393, z=4688434.276443477),
            Position(x=-2305258.084404527, y=-3638249.8628898393, z=4688434.276443477),
            Position(x=-2305258.084404527, y=-3638248.8628898393, z=4688434.276443477),
        ]
    ),
)


ins_resp = INSDataResponse(
    position=Vector3d(x=-2305259.084404527, y=-3638249.8628898393, z=4688434.276443477),
    velocity=Vector3d(x=2.165325034379774e-06, y=3.3993274733273865e-06, z=-4.377216198531156e-06),
    quaternion=Quaternion(x=-0.007981526665389538, y=0.36331599950790405, z=0.003500137012451887, w=0.9316251873970032),
    accel=Vector3(x=-5.379351364354079e-07, y=-0.0007449449622072279, z=-1.2975062418263406e-05),
    gyro=Vector3(x=-0.00152587890625, z=-2.5331974029541016e-05),
    timestamp=1597116945716,
)


class UnityResponseServer(
    RobotConfigServicer,
    CameraServicer,
    INSServicer,
    HelacServicer,
    RotaryEncoderServicer,
    WheelMotorServicer,
    WorldAnnotateServicer,
    PredictionServicer,
    DriveServiceServicer,
):
    def __init__(self, *args, robot, **kwargs):
        super().__init__(*args, **kwargs)
        self._robot = robot

    def GetConfig(self, request: GetConfigArgs, context: Any) -> MakaConfig:
        return MakaConfig(devices=devices[self._robot], geofence=geofence)

    def GetImage(self, request: CameraRequest, context: Any) -> ImageData:
        img = np.zeros((img_height, img_width))
        return ImageData(size=Size(width=img_width, height=img_height), bytes=img.tobytes())

    def GetData(self, request: INSRequest, context: Any) -> INSDataResponse:
        return ins_resp

    def GetValue(self, request: RotaryId, context: Any) -> HelacValue:
        return HelacValue(id=request.id, value=0)

    def GetRotaryConfig(self, request: RotaryId, context: Any) -> RotaryConfig:
        return RotaryConfig(offset_angle=0, min_angle=-45, max_angle=45, center_angle=0)

    def GetRotaryEncoderConfig(self, request: RotaryEncoderRequest, context: Any) -> RotaryEncoderConfig:
        return RotaryEncoderConfig(TPR=40)

    def GetRotaryEncoderTicks(self, request: RotaryEncoderRequest, context: Any) -> RotaryEncoderTicks:
        return RotaryEncoderTicks(ticks=1)

    def SetSpeed(self, request: MotorSpeed, context: Any) -> MotorResponse:
        return MotorResponse()

    def DrawLine(self, request: DrawLineRequest, context: Any) -> DrawLineResponse:
        return DrawLineResponse()

    def DrawBall(self, request: DrawBallRequest, context: Any) -> DrawBallResponse:
        return DrawBallResponse()

    def ClearGroup(self, request: ClearGroupRequest, context: Any) -> ClearGroupResponse:
        return ClearGroupResponse()

    def FurrowPrediction(self, request: CameraPredictionRequest, context: Any) -> FurrowPredictionResponse:
        return FurrowPredictionResponse()

    def Drive(self, request: DriveRequest, context: Any) -> DriveResponse:
        return DriveResponse()


def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=DEFAULT_UNITY_PORT)
    parser.add_argument("--robot", default="seedy")

    args = parser.parse_args()

    print("[unity_response_server] Construct GRPC server...")
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=1))
    urs = UnityResponseServer(robot=args.robot)
    print("[unity_response_server] Adding robot configs to server...")
    add_RobotConfigServicer_to_server(urs, server)
    add_CameraServicer_to_server(urs, server)
    add_INSServicer_to_server(urs, server)
    add_HelacServicer_to_server(urs, server)
    add_RotaryEncoderServicer_to_server(urs, server)
    add_WheelMotorServicer_to_server(urs, server)
    add_WorldAnnotateServicer_to_server(urs, server)
    add_PredictionServicer_to_server(urs, server)
    add_DriveServiceServicer_to_server(urs, server)
    print(f"[unity_response_server] Serve: [::]:{args.port}")
    server.add_insecure_port("[::]:{}".format(args.port))
    server.start()
    server.wait_for_termination()


if __name__ == "__main__":
    main()

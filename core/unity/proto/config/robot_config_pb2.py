# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Config/robot_config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='Config/robot_config.proto',
  package='MakaSim',
  syntax='proto3',
  serialized_options=b'\252\002\025Maka.Sim.Proto.Config',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x19\x43onfig/robot_config.proto\x12\x07MakaSim\"\x0f\n\rGetConfigArgs\"V\n\x0c\x44\x65viceConfig\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\nbootloader\x18\x02 \x01(\t\x12\x12\n\ndevicePath\x18\x03 \x01(\t\x12\x12\n\ndeviceType\x18\x04 \x01(\t\"+\n\x08Position\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\t\n\x01z\x18\x03 \x01(\x01\"0\n\x08\x42oundary\x12$\n\tpositions\x18\x01 \x03(\x0b\x32\x11.MakaSim.Position\"N\n\x08Geofence\x12 \n\x05\x63rops\x18\x01 \x01(\x0b\x32\x11.MakaSim.Boundary\x12 \n\x05\x66ield\x18\x02 \x01(\x0b\x32\x11.MakaSim.Boundary\"Y\n\nMakaConfig\x12&\n\x07\x64\x65vices\x18\x01 \x03(\x0b\x32\x15.MakaSim.DeviceConfig\x12#\n\x08geofence\x18\x02 \x01(\x0b\x32\x11.MakaSim.Geofence2I\n\x0bRobotConfig\x12:\n\tGetConfig\x12\x16.MakaSim.GetConfigArgs\x1a\x13.MakaSim.MakaConfig\"\x00\x42\x18\xaa\x02\x15Maka.Sim.Proto.Configb\x06proto3'
)




_GETCONFIGARGS = _descriptor.Descriptor(
  name='GetConfigArgs',
  full_name='MakaSim.GetConfigArgs',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=38,
  serialized_end=53,
)


_DEVICECONFIG = _descriptor.Descriptor(
  name='DeviceConfig',
  full_name='MakaSim.DeviceConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='MakaSim.DeviceConfig.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bootloader', full_name='MakaSim.DeviceConfig.bootloader', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='devicePath', full_name='MakaSim.DeviceConfig.devicePath', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deviceType', full_name='MakaSim.DeviceConfig.deviceType', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=55,
  serialized_end=141,
)


_POSITION = _descriptor.Descriptor(
  name='Position',
  full_name='MakaSim.Position',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='MakaSim.Position.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='MakaSim.Position.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='MakaSim.Position.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=143,
  serialized_end=186,
)


_BOUNDARY = _descriptor.Descriptor(
  name='Boundary',
  full_name='MakaSim.Boundary',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='positions', full_name='MakaSim.Boundary.positions', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=188,
  serialized_end=236,
)


_GEOFENCE = _descriptor.Descriptor(
  name='Geofence',
  full_name='MakaSim.Geofence',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='crops', full_name='MakaSim.Geofence.crops', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='field', full_name='MakaSim.Geofence.field', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=238,
  serialized_end=316,
)


_MAKACONFIG = _descriptor.Descriptor(
  name='MakaConfig',
  full_name='MakaSim.MakaConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='devices', full_name='MakaSim.MakaConfig.devices', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='geofence', full_name='MakaSim.MakaConfig.geofence', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=318,
  serialized_end=407,
)

_BOUNDARY.fields_by_name['positions'].message_type = _POSITION
_GEOFENCE.fields_by_name['crops'].message_type = _BOUNDARY
_GEOFENCE.fields_by_name['field'].message_type = _BOUNDARY
_MAKACONFIG.fields_by_name['devices'].message_type = _DEVICECONFIG
_MAKACONFIG.fields_by_name['geofence'].message_type = _GEOFENCE
DESCRIPTOR.message_types_by_name['GetConfigArgs'] = _GETCONFIGARGS
DESCRIPTOR.message_types_by_name['DeviceConfig'] = _DEVICECONFIG
DESCRIPTOR.message_types_by_name['Position'] = _POSITION
DESCRIPTOR.message_types_by_name['Boundary'] = _BOUNDARY
DESCRIPTOR.message_types_by_name['Geofence'] = _GEOFENCE
DESCRIPTOR.message_types_by_name['MakaConfig'] = _MAKACONFIG
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetConfigArgs = _reflection.GeneratedProtocolMessageType('GetConfigArgs', (_message.Message,), {
  'DESCRIPTOR' : _GETCONFIGARGS,
  '__module__' : 'Config.robot_config_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.GetConfigArgs)
  })
_sym_db.RegisterMessage(GetConfigArgs)

DeviceConfig = _reflection.GeneratedProtocolMessageType('DeviceConfig', (_message.Message,), {
  'DESCRIPTOR' : _DEVICECONFIG,
  '__module__' : 'Config.robot_config_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.DeviceConfig)
  })
_sym_db.RegisterMessage(DeviceConfig)

Position = _reflection.GeneratedProtocolMessageType('Position', (_message.Message,), {
  'DESCRIPTOR' : _POSITION,
  '__module__' : 'Config.robot_config_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.Position)
  })
_sym_db.RegisterMessage(Position)

Boundary = _reflection.GeneratedProtocolMessageType('Boundary', (_message.Message,), {
  'DESCRIPTOR' : _BOUNDARY,
  '__module__' : 'Config.robot_config_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.Boundary)
  })
_sym_db.RegisterMessage(Boundary)

Geofence = _reflection.GeneratedProtocolMessageType('Geofence', (_message.Message,), {
  'DESCRIPTOR' : _GEOFENCE,
  '__module__' : 'Config.robot_config_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.Geofence)
  })
_sym_db.RegisterMessage(Geofence)

MakaConfig = _reflection.GeneratedProtocolMessageType('MakaConfig', (_message.Message,), {
  'DESCRIPTOR' : _MAKACONFIG,
  '__module__' : 'Config.robot_config_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.MakaConfig)
  })
_sym_db.RegisterMessage(MakaConfig)


DESCRIPTOR._options = None

_ROBOTCONFIG = _descriptor.ServiceDescriptor(
  name='RobotConfig',
  full_name='MakaSim.RobotConfig',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=409,
  serialized_end=482,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetConfig',
    full_name='MakaSim.RobotConfig.GetConfig',
    index=0,
    containing_service=None,
    input_type=_GETCONFIGARGS,
    output_type=_MAKACONFIG,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_ROBOTCONFIG)

DESCRIPTOR.services_by_name['RobotConfig'] = _ROBOTCONFIG

# @@protoc_insertion_point(module_scope)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Helacs/helac.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='Helacs/helac.proto',
  package='MakaSim',
  syntax='proto3',
  serialized_options=b'\252\002\025Maka.Sim.Proto.Helacs',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x12Helacs/helac.proto\x12\x07MakaSim\"\'\n\nHelacValue\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02\"\x0f\n\rHelacResponse\"\x16\n\x08RotaryId\x12\n\n\x02id\x18\x01 \x01(\t\"`\n\x0cRotaryConfig\x12\x14\n\x0coffset_angle\x18\x01 \x01(\x02\x12\x11\n\tmin_angle\x18\x02 \x01(\x02\x12\x11\n\tmax_angle\x18\x03 \x01(\x02\x12\x14\n\x0c\x63\x65nter_angle\x18\x04 \x01(\x02\x32\xb7\x01\n\x05Helac\x12\x39\n\x08SetValue\x12\x13.MakaSim.HelacValue\x1a\x16.MakaSim.HelacResponse\"\x00\x12=\n\x0fGetRotaryConfig\x12\x11.MakaSim.RotaryId\x1a\x15.MakaSim.RotaryConfig\"\x00\x12\x34\n\x08GetValue\x12\x11.MakaSim.RotaryId\x1a\x13.MakaSim.HelacValue\"\x00\x42\x18\xaa\x02\x15Maka.Sim.Proto.Helacsb\x06proto3'
)




_HELACVALUE = _descriptor.Descriptor(
  name='HelacValue',
  full_name='MakaSim.HelacValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='MakaSim.HelacValue.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='MakaSim.HelacValue.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=31,
  serialized_end=70,
)


_HELACRESPONSE = _descriptor.Descriptor(
  name='HelacResponse',
  full_name='MakaSim.HelacResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=72,
  serialized_end=87,
)


_ROTARYID = _descriptor.Descriptor(
  name='RotaryId',
  full_name='MakaSim.RotaryId',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='MakaSim.RotaryId.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=89,
  serialized_end=111,
)


_ROTARYCONFIG = _descriptor.Descriptor(
  name='RotaryConfig',
  full_name='MakaSim.RotaryConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='offset_angle', full_name='MakaSim.RotaryConfig.offset_angle', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_angle', full_name='MakaSim.RotaryConfig.min_angle', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_angle', full_name='MakaSim.RotaryConfig.max_angle', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_angle', full_name='MakaSim.RotaryConfig.center_angle', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=113,
  serialized_end=209,
)

DESCRIPTOR.message_types_by_name['HelacValue'] = _HELACVALUE
DESCRIPTOR.message_types_by_name['HelacResponse'] = _HELACRESPONSE
DESCRIPTOR.message_types_by_name['RotaryId'] = _ROTARYID
DESCRIPTOR.message_types_by_name['RotaryConfig'] = _ROTARYCONFIG
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

HelacValue = _reflection.GeneratedProtocolMessageType('HelacValue', (_message.Message,), {
  'DESCRIPTOR' : _HELACVALUE,
  '__module__' : 'Helacs.helac_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.HelacValue)
  })
_sym_db.RegisterMessage(HelacValue)

HelacResponse = _reflection.GeneratedProtocolMessageType('HelacResponse', (_message.Message,), {
  'DESCRIPTOR' : _HELACRESPONSE,
  '__module__' : 'Helacs.helac_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.HelacResponse)
  })
_sym_db.RegisterMessage(HelacResponse)

RotaryId = _reflection.GeneratedProtocolMessageType('RotaryId', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYID,
  '__module__' : 'Helacs.helac_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.RotaryId)
  })
_sym_db.RegisterMessage(RotaryId)

RotaryConfig = _reflection.GeneratedProtocolMessageType('RotaryConfig', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYCONFIG,
  '__module__' : 'Helacs.helac_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.RotaryConfig)
  })
_sym_db.RegisterMessage(RotaryConfig)


DESCRIPTOR._options = None

_HELAC = _descriptor.ServiceDescriptor(
  name='Helac',
  full_name='MakaSim.Helac',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=212,
  serialized_end=395,
  methods=[
  _descriptor.MethodDescriptor(
    name='SetValue',
    full_name='MakaSim.Helac.SetValue',
    index=0,
    containing_service=None,
    input_type=_HELACVALUE,
    output_type=_HELACRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetRotaryConfig',
    full_name='MakaSim.Helac.GetRotaryConfig',
    index=1,
    containing_service=None,
    input_type=_ROTARYID,
    output_type=_ROTARYCONFIG,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetValue',
    full_name='MakaSim.Helac.GetValue',
    index=2,
    containing_service=None,
    input_type=_ROTARYID,
    output_type=_HELACVALUE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_HELAC)

DESCRIPTOR.services_by_name['Helac'] = _HELAC

# @@protoc_insertion_point(module_scope)

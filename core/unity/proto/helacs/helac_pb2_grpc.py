# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from core.unity.proto.helacs import helac_pb2 as Helacs_dot_helac__pb2


class HelacStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SetValue = channel.unary_unary(
                '/MakaSim.Helac/SetValue',
                request_serializer=Helacs_dot_helac__pb2.HelacValue.SerializeToString,
                response_deserializer=Helacs_dot_helac__pb2.HelacResponse.FromString,
                )
        self.GetRotaryConfig = channel.unary_unary(
                '/MakaSim.Helac/GetRotaryConfig',
                request_serializer=Helacs_dot_helac__pb2.RotaryId.SerializeToString,
                response_deserializer=Helacs_dot_helac__pb2.RotaryConfig.FromString,
                )
        self.GetValue = channel.unary_unary(
                '/MakaSim.Helac/GetValue',
                request_serializer=Helacs_dot_helac__pb2.RotaryId.SerializeToString,
                response_deserializer=Helacs_dot_helac__pb2.HelacValue.FromString,
                )


class HelacServicer(object):
    """Missing associated documentation comment in .proto file."""

    def SetValue(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRotaryConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetValue(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_HelacServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SetValue': grpc.unary_unary_rpc_method_handler(
                    servicer.SetValue,
                    request_deserializer=Helacs_dot_helac__pb2.HelacValue.FromString,
                    response_serializer=Helacs_dot_helac__pb2.HelacResponse.SerializeToString,
            ),
            'GetRotaryConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRotaryConfig,
                    request_deserializer=Helacs_dot_helac__pb2.RotaryId.FromString,
                    response_serializer=Helacs_dot_helac__pb2.RotaryConfig.SerializeToString,
            ),
            'GetValue': grpc.unary_unary_rpc_method_handler(
                    servicer.GetValue,
                    request_deserializer=Helacs_dot_helac__pb2.RotaryId.FromString,
                    response_serializer=Helacs_dot_helac__pb2.HelacValue.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'MakaSim.Helac', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Helac(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def SetValue(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.Helac/SetValue',
            Helacs_dot_helac__pb2.HelacValue.SerializeToString,
            Helacs_dot_helac__pb2.HelacResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetRotaryConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.Helac/GetRotaryConfig',
            Helacs_dot_helac__pb2.RotaryId.SerializeToString,
            Helacs_dot_helac__pb2.RotaryConfig.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetValue(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.Helac/GetValue',
            Helacs_dot_helac__pb2.RotaryId.SerializeToString,
            Helacs_dot_helac__pb2.HelacValue.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

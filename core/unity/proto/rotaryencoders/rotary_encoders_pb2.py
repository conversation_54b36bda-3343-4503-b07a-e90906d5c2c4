# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: RotaryEncoders/rotary_encoders.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='RotaryEncoders/rotary_encoders.proto',
  package='MakaSim',
  syntax='proto3',
  serialized_options=b'\252\002\035Maka.Sim.Proto.RotaryEncoders',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n$RotaryEncoders/rotary_encoders.proto\x12\x07MakaSim\"\"\n\x14RotaryEncoderRequest\x12\n\n\x02id\x18\x01 \x01(\t\"3\n\x12RotaryEncoderTicks\x12\r\n\x05ticks\x18\x01 \x01(\x05\x12\x0e\n\x06timeMS\x18\x02 \x01(\x03\"\"\n\x13RotaryEncoderConfig\x12\x0b\n\x03TPR\x18\x01 \x01(\x05\x32\xbf\x01\n\rRotaryEncoder\x12U\n\x15GetRotaryEncoderTicks\x12\x1d.MakaSim.RotaryEncoderRequest\x1a\x1b.MakaSim.RotaryEncoderTicks\"\x00\x12W\n\x16GetRotaryEncoderConfig\x12\x1d.MakaSim.RotaryEncoderRequest\x1a\x1c.MakaSim.RotaryEncoderConfig\"\x00\x42 \xaa\x02\x1dMaka.Sim.Proto.RotaryEncodersb\x06proto3'
)




_ROTARYENCODERREQUEST = _descriptor.Descriptor(
  name='RotaryEncoderRequest',
  full_name='MakaSim.RotaryEncoderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='MakaSim.RotaryEncoderRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=49,
  serialized_end=83,
)


_ROTARYENCODERTICKS = _descriptor.Descriptor(
  name='RotaryEncoderTicks',
  full_name='MakaSim.RotaryEncoderTicks',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ticks', full_name='MakaSim.RotaryEncoderTicks.ticks', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timeMS', full_name='MakaSim.RotaryEncoderTicks.timeMS', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=85,
  serialized_end=136,
)


_ROTARYENCODERCONFIG = _descriptor.Descriptor(
  name='RotaryEncoderConfig',
  full_name='MakaSim.RotaryEncoderConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='TPR', full_name='MakaSim.RotaryEncoderConfig.TPR', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=138,
  serialized_end=172,
)

DESCRIPTOR.message_types_by_name['RotaryEncoderRequest'] = _ROTARYENCODERREQUEST
DESCRIPTOR.message_types_by_name['RotaryEncoderTicks'] = _ROTARYENCODERTICKS
DESCRIPTOR.message_types_by_name['RotaryEncoderConfig'] = _ROTARYENCODERCONFIG
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RotaryEncoderRequest = _reflection.GeneratedProtocolMessageType('RotaryEncoderRequest', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYENCODERREQUEST,
  '__module__' : 'RotaryEncoders.rotary_encoders_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.RotaryEncoderRequest)
  })
_sym_db.RegisterMessage(RotaryEncoderRequest)

RotaryEncoderTicks = _reflection.GeneratedProtocolMessageType('RotaryEncoderTicks', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYENCODERTICKS,
  '__module__' : 'RotaryEncoders.rotary_encoders_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.RotaryEncoderTicks)
  })
_sym_db.RegisterMessage(RotaryEncoderTicks)

RotaryEncoderConfig = _reflection.GeneratedProtocolMessageType('RotaryEncoderConfig', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYENCODERCONFIG,
  '__module__' : 'RotaryEncoders.rotary_encoders_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.RotaryEncoderConfig)
  })
_sym_db.RegisterMessage(RotaryEncoderConfig)


DESCRIPTOR._options = None

_ROTARYENCODER = _descriptor.ServiceDescriptor(
  name='RotaryEncoder',
  full_name='MakaSim.RotaryEncoder',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=175,
  serialized_end=366,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetRotaryEncoderTicks',
    full_name='MakaSim.RotaryEncoder.GetRotaryEncoderTicks',
    index=0,
    containing_service=None,
    input_type=_ROTARYENCODERREQUEST,
    output_type=_ROTARYENCODERTICKS,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetRotaryEncoderConfig',
    full_name='MakaSim.RotaryEncoder.GetRotaryEncoderConfig',
    index=1,
    containing_service=None,
    input_type=_ROTARYENCODERREQUEST,
    output_type=_ROTARYENCODERCONFIG,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_ROTARYENCODER)

DESCRIPTOR.services_by_name['RotaryEncoder'] = _ROTARYENCODER

# @@protoc_insertion_point(module_scope)

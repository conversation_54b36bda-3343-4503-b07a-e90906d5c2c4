# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from core.unity.proto.rotaryencoders import rotary_encoders_pb2 as RotaryEncoders_dot_rotary__encoders__pb2


class RotaryEncoderStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetRotaryEncoderTicks = channel.unary_unary(
                '/MakaSim.RotaryEncoder/GetRotaryEncoderTicks',
                request_serializer=RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderRequest.SerializeToString,
                response_deserializer=RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderTicks.FromString,
                )
        self.GetRotaryEncoderConfig = channel.unary_unary(
                '/MakaSim.RotaryEncoder/GetRotaryEncoderConfig',
                request_serializer=RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderRequest.SerializeToString,
                response_deserializer=RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderConfig.FromString,
                )


class RotaryEncoderServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetRotaryEncoderTicks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRotaryEncoderConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RotaryEncoderServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetRotaryEncoderTicks': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRotaryEncoderTicks,
                    request_deserializer=RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderRequest.FromString,
                    response_serializer=RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderTicks.SerializeToString,
            ),
            'GetRotaryEncoderConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRotaryEncoderConfig,
                    request_deserializer=RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderRequest.FromString,
                    response_serializer=RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderConfig.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'MakaSim.RotaryEncoder', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class RotaryEncoder(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetRotaryEncoderTicks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.RotaryEncoder/GetRotaryEncoderTicks',
            RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderRequest.SerializeToString,
            RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderTicks.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetRotaryEncoderConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.RotaryEncoder/GetRotaryEncoderConfig',
            RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderRequest.SerializeToString,
            RotaryEncoders_dot_rotary__encoders__pb2.RotaryEncoderConfig.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Motors/wheel_motor.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='Motors/wheel_motor.proto',
  package='MakaSim',
  syntax='proto3',
  serialized_options=b'\252\002\025Maka.Sim.Proto.Motors',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x18Motors/wheel_motor.proto\x12\x07MakaSim\"+\n\nMotorSpeed\x12\n\n\x02id\x18\x01 \x01(\t\x12\x11\n\tspeed_mph\x18\x02 \x01(\x02\"\x0f\n\rMotorResponse2G\n\nWheelMotor\x12\x39\n\x08SetSpeed\x12\x13.MakaSim.MotorSpeed\x1a\x16.MakaSim.MotorResponse\"\x00\x42\x18\xaa\x02\x15Maka.Sim.Proto.Motorsb\x06proto3'
)




_MOTORSPEED = _descriptor.Descriptor(
  name='MotorSpeed',
  full_name='MakaSim.MotorSpeed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='MakaSim.MotorSpeed.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_mph', full_name='MakaSim.MotorSpeed.speed_mph', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=37,
  serialized_end=80,
)


_MOTORRESPONSE = _descriptor.Descriptor(
  name='MotorResponse',
  full_name='MakaSim.MotorResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=82,
  serialized_end=97,
)

DESCRIPTOR.message_types_by_name['MotorSpeed'] = _MOTORSPEED
DESCRIPTOR.message_types_by_name['MotorResponse'] = _MOTORRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

MotorSpeed = _reflection.GeneratedProtocolMessageType('MotorSpeed', (_message.Message,), {
  'DESCRIPTOR' : _MOTORSPEED,
  '__module__' : 'Motors.wheel_motor_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.MotorSpeed)
  })
_sym_db.RegisterMessage(MotorSpeed)

MotorResponse = _reflection.GeneratedProtocolMessageType('MotorResponse', (_message.Message,), {
  'DESCRIPTOR' : _MOTORRESPONSE,
  '__module__' : 'Motors.wheel_motor_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.MotorResponse)
  })
_sym_db.RegisterMessage(MotorResponse)


DESCRIPTOR._options = None

_WHEELMOTOR = _descriptor.ServiceDescriptor(
  name='WheelMotor',
  full_name='MakaSim.WheelMotor',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=99,
  serialized_end=170,
  methods=[
  _descriptor.MethodDescriptor(
    name='SetSpeed',
    full_name='MakaSim.WheelMotor.SetSpeed',
    index=0,
    containing_service=None,
    input_type=_MOTORSPEED,
    output_type=_MOTORRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_WHEELMOTOR)

DESCRIPTOR.services_by_name['WheelMotor'] = _WHEELMOTOR

# @@protoc_insertion_point(module_scope)

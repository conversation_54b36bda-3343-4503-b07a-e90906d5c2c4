# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from core.unity.proto.motors import wheel_motor_pb2 as Motors_dot_wheel__motor__pb2


class WheelMotorStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SetSpeed = channel.unary_unary(
                '/MakaSim.WheelMotor/SetSpeed',
                request_serializer=Motors_dot_wheel__motor__pb2.MotorSpeed.SerializeToString,
                response_deserializer=Motors_dot_wheel__motor__pb2.MotorResponse.FromString,
                )


class WheelMotorServicer(object):
    """Missing associated documentation comment in .proto file."""

    def SetSpeed(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_WheelMotorServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SetSpeed': grpc.unary_unary_rpc_method_handler(
                    servicer.SetSpeed,
                    request_deserializer=Motors_dot_wheel__motor__pb2.MotorSpeed.FromString,
                    response_serializer=Motors_dot_wheel__motor__pb2.MotorResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'MakaSim.WheelMotor', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class WheelMotor(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def SetSpeed(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.WheelMotor/SetSpeed',
            Motors_dot_wheel__motor__pb2.MotorSpeed.SerializeToString,
            Motors_dot_wheel__motor__pb2.MotorResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: IMUs/imu.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='IMUs/imu.proto',
  package='MakaSim',
  syntax='proto3',
  serialized_options=b'\252\002\023Maka.Sim.Proto.IMUs',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x0eIMUs/imu.proto\x12\x07MakaSim\"\x18\n\nIMURequest\x12\n\n\x02id\x18\x01 \x01(\t\"\x1e\n\x0bIMUResponse\x12\x0f\n\x07heading\x18\x01 \x01(\x05\x32@\n\x03IMU\x12\x39\n\nGetHeading\x12\x13.MakaSim.IMURequest\x1a\x14.MakaSim.IMUResponse\"\x00\x42\x16\xaa\x02\x13Maka.Sim.Proto.IMUsb\x06proto3'
)




_IMUREQUEST = _descriptor.Descriptor(
  name='IMURequest',
  full_name='MakaSim.IMURequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='MakaSim.IMURequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=27,
  serialized_end=51,
)


_IMURESPONSE = _descriptor.Descriptor(
  name='IMUResponse',
  full_name='MakaSim.IMUResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='heading', full_name='MakaSim.IMUResponse.heading', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=53,
  serialized_end=83,
)

DESCRIPTOR.message_types_by_name['IMURequest'] = _IMUREQUEST
DESCRIPTOR.message_types_by_name['IMUResponse'] = _IMURESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

IMURequest = _reflection.GeneratedProtocolMessageType('IMURequest', (_message.Message,), {
  'DESCRIPTOR' : _IMUREQUEST,
  '__module__' : 'IMUs.imu_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.IMURequest)
  })
_sym_db.RegisterMessage(IMURequest)

IMUResponse = _reflection.GeneratedProtocolMessageType('IMUResponse', (_message.Message,), {
  'DESCRIPTOR' : _IMURESPONSE,
  '__module__' : 'IMUs.imu_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.IMUResponse)
  })
_sym_db.RegisterMessage(IMUResponse)


DESCRIPTOR._options = None

_IMU = _descriptor.ServiceDescriptor(
  name='IMU',
  full_name='MakaSim.IMU',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=85,
  serialized_end=149,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetHeading',
    full_name='MakaSim.IMU.GetHeading',
    index=0,
    containing_service=None,
    input_type=_IMUREQUEST,
    output_type=_IMURESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_IMU)

DESCRIPTOR.services_by_name['IMU'] = _IMU

# @@protoc_insertion_point(module_scope)

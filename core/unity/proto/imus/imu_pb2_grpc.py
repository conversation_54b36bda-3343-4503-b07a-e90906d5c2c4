# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from core.unity.proto.imus import imu_pb2 as IMUs_dot_imu__pb2


class IMUStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetHeading = channel.unary_unary(
                '/MakaSim.IMU/GetHeading',
                request_serializer=IMUs_dot_imu__pb2.IMURequest.SerializeToString,
                response_deserializer=IMUs_dot_imu__pb2.IMUResponse.FromString,
                )


class IMUServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetHeading(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_IMUServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetHeading': grpc.unary_unary_rpc_method_handler(
                    servicer.GetHeading,
                    request_deserializer=IMUs_dot_imu__pb2.IMURequest.FromString,
                    response_serializer=IMUs_dot_imu__pb2.IMUResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'MakaSim.IMU', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class IMU(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetHeading(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.IMU/GetHeading',
            IMUs_dot_imu__pb2.IMURequest.SerializeToString,
            IMUs_dot_imu__pb2.IMUResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: INS/ins.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='INS/ins.proto',
  package='MakaSim',
  syntax='proto3',
  serialized_options=b'\252\002\022Maka.Sim.Proto.INS',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\rINS/ins.proto\x12\x07MakaSim\"\x18\n\nINSRequest\x12\n\n\x02id\x18\x01 \x01(\t\"+\n\x08Vector3d\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\t\n\x01z\x18\x03 \x01(\x01\"*\n\x07Vector3\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\t\n\x01z\x18\x03 \x01(\x02\"8\n\nQuaternion\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\t\n\x01z\x18\x03 \x01(\x02\x12\t\n\x01w\x18\x04 \x01(\x02\"\xd8\x01\n\x0fINSDataResponse\x12#\n\x08position\x18\x01 \x01(\x0b\x32\x11.MakaSim.Vector3d\x12#\n\x08velocity\x18\x02 \x01(\x0b\x32\x11.MakaSim.Vector3d\x12\'\n\nquaternion\x18\x03 \x01(\x0b\x32\x13.MakaSim.Quaternion\x12\x1f\n\x05\x61\x63\x63\x65l\x18\x04 \x01(\x0b\x32\x10.MakaSim.Vector3\x12\x1e\n\x04gyro\x18\x05 \x01(\x0b\x32\x10.MakaSim.Vector3\x12\x11\n\ttimestamp\x18\x06 \x01(\x03\x32\x41\n\x03INS\x12:\n\x07GetData\x12\x13.MakaSim.INSRequest\x1a\x18.MakaSim.INSDataResponse\"\x00\x42\x15\xaa\x02\x12Maka.Sim.Proto.INSb\x06proto3'
)




_INSREQUEST = _descriptor.Descriptor(
  name='INSRequest',
  full_name='MakaSim.INSRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='MakaSim.INSRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=26,
  serialized_end=50,
)


_VECTOR3D = _descriptor.Descriptor(
  name='Vector3d',
  full_name='MakaSim.Vector3d',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='MakaSim.Vector3d.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='MakaSim.Vector3d.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='MakaSim.Vector3d.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=52,
  serialized_end=95,
)


_VECTOR3 = _descriptor.Descriptor(
  name='Vector3',
  full_name='MakaSim.Vector3',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='MakaSim.Vector3.x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='MakaSim.Vector3.y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='MakaSim.Vector3.z', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=97,
  serialized_end=139,
)


_QUATERNION = _descriptor.Descriptor(
  name='Quaternion',
  full_name='MakaSim.Quaternion',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='MakaSim.Quaternion.x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='MakaSim.Quaternion.y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='MakaSim.Quaternion.z', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='w', full_name='MakaSim.Quaternion.w', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=141,
  serialized_end=197,
)


_INSDATARESPONSE = _descriptor.Descriptor(
  name='INSDataResponse',
  full_name='MakaSim.INSDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='position', full_name='MakaSim.INSDataResponse.position', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='MakaSim.INSDataResponse.velocity', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='quaternion', full_name='MakaSim.INSDataResponse.quaternion', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='accel', full_name='MakaSim.INSDataResponse.accel', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gyro', full_name='MakaSim.INSDataResponse.gyro', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='MakaSim.INSDataResponse.timestamp', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=200,
  serialized_end=416,
)

_INSDATARESPONSE.fields_by_name['position'].message_type = _VECTOR3D
_INSDATARESPONSE.fields_by_name['velocity'].message_type = _VECTOR3D
_INSDATARESPONSE.fields_by_name['quaternion'].message_type = _QUATERNION
_INSDATARESPONSE.fields_by_name['accel'].message_type = _VECTOR3
_INSDATARESPONSE.fields_by_name['gyro'].message_type = _VECTOR3
DESCRIPTOR.message_types_by_name['INSRequest'] = _INSREQUEST
DESCRIPTOR.message_types_by_name['Vector3d'] = _VECTOR3D
DESCRIPTOR.message_types_by_name['Vector3'] = _VECTOR3
DESCRIPTOR.message_types_by_name['Quaternion'] = _QUATERNION
DESCRIPTOR.message_types_by_name['INSDataResponse'] = _INSDATARESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

INSRequest = _reflection.GeneratedProtocolMessageType('INSRequest', (_message.Message,), {
  'DESCRIPTOR' : _INSREQUEST,
  '__module__' : 'INS.ins_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.INSRequest)
  })
_sym_db.RegisterMessage(INSRequest)

Vector3d = _reflection.GeneratedProtocolMessageType('Vector3d', (_message.Message,), {
  'DESCRIPTOR' : _VECTOR3D,
  '__module__' : 'INS.ins_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.Vector3d)
  })
_sym_db.RegisterMessage(Vector3d)

Vector3 = _reflection.GeneratedProtocolMessageType('Vector3', (_message.Message,), {
  'DESCRIPTOR' : _VECTOR3,
  '__module__' : 'INS.ins_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.Vector3)
  })
_sym_db.RegisterMessage(Vector3)

Quaternion = _reflection.GeneratedProtocolMessageType('Quaternion', (_message.Message,), {
  'DESCRIPTOR' : _QUATERNION,
  '__module__' : 'INS.ins_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.Quaternion)
  })
_sym_db.RegisterMessage(Quaternion)

INSDataResponse = _reflection.GeneratedProtocolMessageType('INSDataResponse', (_message.Message,), {
  'DESCRIPTOR' : _INSDATARESPONSE,
  '__module__' : 'INS.ins_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.INSDataResponse)
  })
_sym_db.RegisterMessage(INSDataResponse)


DESCRIPTOR._options = None

_INS = _descriptor.ServiceDescriptor(
  name='INS',
  full_name='MakaSim.INS',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=418,
  serialized_end=483,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetData',
    full_name='MakaSim.INS.GetData',
    index=0,
    containing_service=None,
    input_type=_INSREQUEST,
    output_type=_INSDATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_INS)

DESCRIPTOR.services_by_name['INS'] = _INS

# @@protoc_insertion_point(module_scope)

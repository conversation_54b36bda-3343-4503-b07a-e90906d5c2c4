# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from core.unity.proto.prediction import prediction_pb2 as Prediction_dot_prediction__pb2


class PredictionStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.FurrowPrediction = channel.unary_unary(
                '/MakaSim.Prediction/FurrowPrediction',
                request_serializer=Prediction_dot_prediction__pb2.CameraPredictionRequest.SerializeToString,
                response_deserializer=Prediction_dot_prediction__pb2.FurrowPredictionResponse.FromString,
                )


class PredictionServicer(object):
    """Missing associated documentation comment in .proto file."""

    def FurrowPrediction(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PredictionServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'FurrowPrediction': grpc.unary_unary_rpc_method_handler(
                    servicer.FurrowPrediction,
                    request_deserializer=Prediction_dot_prediction__pb2.CameraPredictionRequest.FromString,
                    response_serializer=Prediction_dot_prediction__pb2.FurrowPredictionResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'MakaSim.Prediction', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Prediction(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def FurrowPrediction(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.Prediction/FurrowPrediction',
            Prediction_dot_prediction__pb2.CameraPredictionRequest.SerializeToString,
            Prediction_dot_prediction__pb2.FurrowPredictionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

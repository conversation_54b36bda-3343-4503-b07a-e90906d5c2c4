# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Prediction/prediction.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='Prediction/prediction.proto',
  package='MakaSim',
  syntax='proto3',
  serialized_options=b'\252\002\031Maka.Sim.Proto.Prediction',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1bPrediction/prediction.proto\x12\x07MakaSim\"%\n\x17\x43\x61meraPredictionRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\x1f\n\x07Vector2\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\"L\n\nFurrowLine\x12\x1f\n\x05start\x18\x01 \x01(\x0b\x32\x10.MakaSim.Vector2\x12\x1d\n\x03\x65nd\x18\x02 \x01(\x0b\x32\x10.MakaSim.Vector2\"@\n\x18\x46urrowPredictionResponse\x12$\n\x07\x66urrows\x18\x01 \x03(\x0b\x32\x13.MakaSim.FurrowLine2g\n\nPrediction\x12Y\n\x10\x46urrowPrediction\x12 .MakaSim.CameraPredictionRequest\x1a!.MakaSim.FurrowPredictionResponse\"\x00\x42\x1c\xaa\x02\x19Maka.Sim.Proto.Predictionb\x06proto3'
)




_CAMERAPREDICTIONREQUEST = _descriptor.Descriptor(
  name='CameraPredictionRequest',
  full_name='MakaSim.CameraPredictionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='MakaSim.CameraPredictionRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=40,
  serialized_end=77,
)


_VECTOR2 = _descriptor.Descriptor(
  name='Vector2',
  full_name='MakaSim.Vector2',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='MakaSim.Vector2.x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='MakaSim.Vector2.y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=79,
  serialized_end=110,
)


_FURROWLINE = _descriptor.Descriptor(
  name='FurrowLine',
  full_name='MakaSim.FurrowLine',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='start', full_name='MakaSim.FurrowLine.start', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end', full_name='MakaSim.FurrowLine.end', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=112,
  serialized_end=188,
)


_FURROWPREDICTIONRESPONSE = _descriptor.Descriptor(
  name='FurrowPredictionResponse',
  full_name='MakaSim.FurrowPredictionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='furrows', full_name='MakaSim.FurrowPredictionResponse.furrows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=190,
  serialized_end=254,
)

_FURROWLINE.fields_by_name['start'].message_type = _VECTOR2
_FURROWLINE.fields_by_name['end'].message_type = _VECTOR2
_FURROWPREDICTIONRESPONSE.fields_by_name['furrows'].message_type = _FURROWLINE
DESCRIPTOR.message_types_by_name['CameraPredictionRequest'] = _CAMERAPREDICTIONREQUEST
DESCRIPTOR.message_types_by_name['Vector2'] = _VECTOR2
DESCRIPTOR.message_types_by_name['FurrowLine'] = _FURROWLINE
DESCRIPTOR.message_types_by_name['FurrowPredictionResponse'] = _FURROWPREDICTIONRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CameraPredictionRequest = _reflection.GeneratedProtocolMessageType('CameraPredictionRequest', (_message.Message,), {
  'DESCRIPTOR' : _CAMERAPREDICTIONREQUEST,
  '__module__' : 'Prediction.prediction_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.CameraPredictionRequest)
  })
_sym_db.RegisterMessage(CameraPredictionRequest)

Vector2 = _reflection.GeneratedProtocolMessageType('Vector2', (_message.Message,), {
  'DESCRIPTOR' : _VECTOR2,
  '__module__' : 'Prediction.prediction_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.Vector2)
  })
_sym_db.RegisterMessage(Vector2)

FurrowLine = _reflection.GeneratedProtocolMessageType('FurrowLine', (_message.Message,), {
  'DESCRIPTOR' : _FURROWLINE,
  '__module__' : 'Prediction.prediction_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.FurrowLine)
  })
_sym_db.RegisterMessage(FurrowLine)

FurrowPredictionResponse = _reflection.GeneratedProtocolMessageType('FurrowPredictionResponse', (_message.Message,), {
  'DESCRIPTOR' : _FURROWPREDICTIONRESPONSE,
  '__module__' : 'Prediction.prediction_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.FurrowPredictionResponse)
  })
_sym_db.RegisterMessage(FurrowPredictionResponse)


DESCRIPTOR._options = None

_PREDICTION = _descriptor.ServiceDescriptor(
  name='Prediction',
  full_name='MakaSim.Prediction',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=256,
  serialized_end=359,
  methods=[
  _descriptor.MethodDescriptor(
    name='FurrowPrediction',
    full_name='MakaSim.Prediction.FurrowPrediction',
    index=0,
    containing_service=None,
    input_type=_CAMERAPREDICTIONREQUEST,
    output_type=_FURROWPREDICTIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_PREDICTION)

DESCRIPTOR.services_by_name['Prediction'] = _PREDICTION

# @@protoc_insertion_point(module_scope)

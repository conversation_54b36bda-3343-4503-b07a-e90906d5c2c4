# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: OpticalFlow/optical_flow.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='OpticalFlow/optical_flow.proto',
  package='MakaSim',
  syntax='proto3',
  serialized_options=b'\252\002\032Maka.Sim.Proto.OpticalFlow',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1eOpticalFlow/optical_flow.proto\x12\x07MakaSim\"O\n\x11\x43\x61meraFlowRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x17\n\x0fstart_timestamp\x18\x02 \x01(\x03\x12\x15\n\rend_timestamp\x18\x03 \x01(\x03\"1\n\x13OpticalFlowResponse\x12\x0c\n\x04xoff\x18\x01 \x01(\x05\x12\x0c\n\x04yoff\x18\x02 \x01(\x05\x32Q\n\x0bOpticalFlow\x12\x42\n\x04\x46low\x12\x1a.MakaSim.CameraFlowRequest\x1a\x1c.MakaSim.OpticalFlowResponse\"\x00\x42\x1d\xaa\x02\x1aMaka.Sim.Proto.OpticalFlowb\x06proto3'
)




_CAMERAFLOWREQUEST = _descriptor.Descriptor(
  name='CameraFlowRequest',
  full_name='MakaSim.CameraFlowRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='MakaSim.CameraFlowRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_timestamp', full_name='MakaSim.CameraFlowRequest.start_timestamp', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_timestamp', full_name='MakaSim.CameraFlowRequest.end_timestamp', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=43,
  serialized_end=122,
)


_OPTICALFLOWRESPONSE = _descriptor.Descriptor(
  name='OpticalFlowResponse',
  full_name='MakaSim.OpticalFlowResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='xoff', full_name='MakaSim.OpticalFlowResponse.xoff', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='yoff', full_name='MakaSim.OpticalFlowResponse.yoff', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=124,
  serialized_end=173,
)

DESCRIPTOR.message_types_by_name['CameraFlowRequest'] = _CAMERAFLOWREQUEST
DESCRIPTOR.message_types_by_name['OpticalFlowResponse'] = _OPTICALFLOWRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CameraFlowRequest = _reflection.GeneratedProtocolMessageType('CameraFlowRequest', (_message.Message,), {
  'DESCRIPTOR' : _CAMERAFLOWREQUEST,
  '__module__' : 'OpticalFlow.optical_flow_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.CameraFlowRequest)
  })
_sym_db.RegisterMessage(CameraFlowRequest)

OpticalFlowResponse = _reflection.GeneratedProtocolMessageType('OpticalFlowResponse', (_message.Message,), {
  'DESCRIPTOR' : _OPTICALFLOWRESPONSE,
  '__module__' : 'OpticalFlow.optical_flow_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.OpticalFlowResponse)
  })
_sym_db.RegisterMessage(OpticalFlowResponse)


DESCRIPTOR._options = None

_OPTICALFLOW = _descriptor.ServiceDescriptor(
  name='OpticalFlow',
  full_name='MakaSim.OpticalFlow',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=175,
  serialized_end=256,
  methods=[
  _descriptor.MethodDescriptor(
    name='Flow',
    full_name='MakaSim.OpticalFlow.Flow',
    index=0,
    containing_service=None,
    input_type=_CAMERAFLOWREQUEST,
    output_type=_OPTICALFLOWRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_OPTICALFLOW)

DESCRIPTOR.services_by_name['OpticalFlow'] = _OPTICALFLOW

# @@protoc_insertion_point(module_scope)

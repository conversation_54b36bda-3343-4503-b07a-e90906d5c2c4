# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from core.unity.proto.opticalflow import optical_flow_pb2 as OpticalFlow_dot_optical__flow__pb2


class OpticalFlowStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Flow = channel.unary_unary(
                '/MakaSim.OpticalFlow/Flow',
                request_serializer=OpticalFlow_dot_optical__flow__pb2.CameraFlowRequest.SerializeToString,
                response_deserializer=OpticalFlow_dot_optical__flow__pb2.OpticalFlowResponse.FromString,
                )


class OpticalFlowServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Flow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OpticalFlowServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Flow': grpc.unary_unary_rpc_method_handler(
                    servicer.Flow,
                    request_deserializer=OpticalFlow_dot_optical__flow__pb2.CameraFlowRequest.FromString,
                    response_serializer=OpticalFlow_dot_optical__flow__pb2.OpticalFlowResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'MakaSim.OpticalFlow', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class OpticalFlow(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Flow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.OpticalFlow/Flow',
            OpticalFlow_dot_optical__flow__pb2.CameraFlowRequest.SerializeToString,
            OpticalFlow_dot_optical__flow__pb2.OpticalFlowResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from core.unity.proto.worldannotate import world_annotate_pb2 as WorldAnnotate_dot_world__annotate__pb2


class WorldAnnotateStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.DrawLine = channel.unary_unary(
                '/MakaSim.WorldAnnotate/DrawLine',
                request_serializer=WorldAnnotate_dot_world__annotate__pb2.DrawLineRequest.SerializeToString,
                response_deserializer=WorldAnnotate_dot_world__annotate__pb2.DrawLineResponse.FromString,
                )
        self.DrawBall = channel.unary_unary(
                '/MakaSim.WorldAnnotate/DrawBall',
                request_serializer=WorldAnnotate_dot_world__annotate__pb2.DrawBallRequest.SerializeToString,
                response_deserializer=WorldAnnotate_dot_world__annotate__pb2.DrawBallResponse.FromString,
                )
        self.ClearGroup = channel.unary_unary(
                '/MakaSim.WorldAnnotate/ClearGroup',
                request_serializer=WorldAnnotate_dot_world__annotate__pb2.ClearGroupRequest.SerializeToString,
                response_deserializer=WorldAnnotate_dot_world__annotate__pb2.ClearGroupResponse.FromString,
                )


class WorldAnnotateServicer(object):
    """Missing associated documentation comment in .proto file."""

    def DrawLine(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DrawBall(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClearGroup(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_WorldAnnotateServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'DrawLine': grpc.unary_unary_rpc_method_handler(
                    servicer.DrawLine,
                    request_deserializer=WorldAnnotate_dot_world__annotate__pb2.DrawLineRequest.FromString,
                    response_serializer=WorldAnnotate_dot_world__annotate__pb2.DrawLineResponse.SerializeToString,
            ),
            'DrawBall': grpc.unary_unary_rpc_method_handler(
                    servicer.DrawBall,
                    request_deserializer=WorldAnnotate_dot_world__annotate__pb2.DrawBallRequest.FromString,
                    response_serializer=WorldAnnotate_dot_world__annotate__pb2.DrawBallResponse.SerializeToString,
            ),
            'ClearGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.ClearGroup,
                    request_deserializer=WorldAnnotate_dot_world__annotate__pb2.ClearGroupRequest.FromString,
                    response_serializer=WorldAnnotate_dot_world__annotate__pb2.ClearGroupResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'MakaSim.WorldAnnotate', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class WorldAnnotate(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def DrawLine(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.WorldAnnotate/DrawLine',
            WorldAnnotate_dot_world__annotate__pb2.DrawLineRequest.SerializeToString,
            WorldAnnotate_dot_world__annotate__pb2.DrawLineResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DrawBall(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.WorldAnnotate/DrawBall',
            WorldAnnotate_dot_world__annotate__pb2.DrawBallRequest.SerializeToString,
            WorldAnnotate_dot_world__annotate__pb2.DrawBallResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClearGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.WorldAnnotate/ClearGroup',
            WorldAnnotate_dot_world__annotate__pb2.ClearGroupRequest.SerializeToString,
            WorldAnnotate_dot_world__annotate__pb2.ClearGroupResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: WorldAnnotate/world_annotate.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='WorldAnnotate/world_annotate.proto',
  package='MakaSim',
  syntax='proto3',
  serialized_options=b'\252\002\034Maka.Sim.Proto.WorldAnnotate',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\"WorldAnnotate/world_annotate.proto\x12\x07MakaSim\",\n\tEcefPoint\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\t\n\x01z\x18\x03 \x01(\x01\"\x85\x01\n\x0f\x44rawLineRequest\x12!\n\x05start\x18\x01 \x01(\x0b\x32\x12.MakaSim.EcefPoint\x12\x1f\n\x03\x65nd\x18\x02 \x01(\x0b\x32\x12.MakaSim.EcefPoint\x12\x1f\n\x04type\x18\x03 \x01(\x0e\x32\x11.MakaSim.LineType\x12\r\n\x05group\x18\x04 \x01(\t\"B\n\x0f\x44rawBallRequest\x12 \n\x04\x62\x61ll\x18\x01 \x01(\x0b\x32\x12.MakaSim.EcefPoint\x12\r\n\x05group\x18\x02 \x01(\t\"\"\n\x11\x43learGroupRequest\x12\r\n\x05group\x18\x01 \x01(\t\"\x12\n\x10\x44rawLineResponse\"\x12\n\x10\x44rawBallResponse\"\x14\n\x12\x43learGroupResponse*2\n\x08LineType\x12\n\n\x06TARGET\x10\x00\x12\t\n\x05\x46IELD\x10\x01\x12\x0f\n\x0bTURN_AROUND\x10\x02\x32\xde\x01\n\rWorldAnnotate\x12\x41\n\x08\x44rawLine\x12\x18.MakaSim.DrawLineRequest\x1a\x19.MakaSim.DrawLineResponse\"\x00\x12\x41\n\x08\x44rawBall\x12\x18.MakaSim.DrawBallRequest\x1a\x19.MakaSim.DrawBallResponse\"\x00\x12G\n\nClearGroup\x12\x1a.MakaSim.ClearGroupRequest\x1a\x1b.MakaSim.ClearGroupResponse\"\x00\x42\x1f\xaa\x02\x1cMaka.Sim.Proto.WorldAnnotateb\x06proto3'
)

_LINETYPE = _descriptor.EnumDescriptor(
  name='LineType',
  full_name='MakaSim.LineType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TARGET', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FIELD', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TURN_AROUND', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=395,
  serialized_end=445,
)
_sym_db.RegisterEnumDescriptor(_LINETYPE)

LineType = enum_type_wrapper.EnumTypeWrapper(_LINETYPE)
TARGET = 0
FIELD = 1
TURN_AROUND = 2



_ECEFPOINT = _descriptor.Descriptor(
  name='EcefPoint',
  full_name='MakaSim.EcefPoint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='MakaSim.EcefPoint.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='MakaSim.EcefPoint.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='MakaSim.EcefPoint.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=47,
  serialized_end=91,
)


_DRAWLINEREQUEST = _descriptor.Descriptor(
  name='DrawLineRequest',
  full_name='MakaSim.DrawLineRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='start', full_name='MakaSim.DrawLineRequest.start', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end', full_name='MakaSim.DrawLineRequest.end', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='MakaSim.DrawLineRequest.type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='group', full_name='MakaSim.DrawLineRequest.group', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=94,
  serialized_end=227,
)


_DRAWBALLREQUEST = _descriptor.Descriptor(
  name='DrawBallRequest',
  full_name='MakaSim.DrawBallRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ball', full_name='MakaSim.DrawBallRequest.ball', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='group', full_name='MakaSim.DrawBallRequest.group', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=229,
  serialized_end=295,
)


_CLEARGROUPREQUEST = _descriptor.Descriptor(
  name='ClearGroupRequest',
  full_name='MakaSim.ClearGroupRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='group', full_name='MakaSim.ClearGroupRequest.group', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=297,
  serialized_end=331,
)


_DRAWLINERESPONSE = _descriptor.Descriptor(
  name='DrawLineResponse',
  full_name='MakaSim.DrawLineResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=333,
  serialized_end=351,
)


_DRAWBALLRESPONSE = _descriptor.Descriptor(
  name='DrawBallResponse',
  full_name='MakaSim.DrawBallResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=353,
  serialized_end=371,
)


_CLEARGROUPRESPONSE = _descriptor.Descriptor(
  name='ClearGroupResponse',
  full_name='MakaSim.ClearGroupResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=373,
  serialized_end=393,
)

_DRAWLINEREQUEST.fields_by_name['start'].message_type = _ECEFPOINT
_DRAWLINEREQUEST.fields_by_name['end'].message_type = _ECEFPOINT
_DRAWLINEREQUEST.fields_by_name['type'].enum_type = _LINETYPE
_DRAWBALLREQUEST.fields_by_name['ball'].message_type = _ECEFPOINT
DESCRIPTOR.message_types_by_name['EcefPoint'] = _ECEFPOINT
DESCRIPTOR.message_types_by_name['DrawLineRequest'] = _DRAWLINEREQUEST
DESCRIPTOR.message_types_by_name['DrawBallRequest'] = _DRAWBALLREQUEST
DESCRIPTOR.message_types_by_name['ClearGroupRequest'] = _CLEARGROUPREQUEST
DESCRIPTOR.message_types_by_name['DrawLineResponse'] = _DRAWLINERESPONSE
DESCRIPTOR.message_types_by_name['DrawBallResponse'] = _DRAWBALLRESPONSE
DESCRIPTOR.message_types_by_name['ClearGroupResponse'] = _CLEARGROUPRESPONSE
DESCRIPTOR.enum_types_by_name['LineType'] = _LINETYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

EcefPoint = _reflection.GeneratedProtocolMessageType('EcefPoint', (_message.Message,), {
  'DESCRIPTOR' : _ECEFPOINT,
  '__module__' : 'WorldAnnotate.world_annotate_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.EcefPoint)
  })
_sym_db.RegisterMessage(EcefPoint)

DrawLineRequest = _reflection.GeneratedProtocolMessageType('DrawLineRequest', (_message.Message,), {
  'DESCRIPTOR' : _DRAWLINEREQUEST,
  '__module__' : 'WorldAnnotate.world_annotate_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.DrawLineRequest)
  })
_sym_db.RegisterMessage(DrawLineRequest)

DrawBallRequest = _reflection.GeneratedProtocolMessageType('DrawBallRequest', (_message.Message,), {
  'DESCRIPTOR' : _DRAWBALLREQUEST,
  '__module__' : 'WorldAnnotate.world_annotate_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.DrawBallRequest)
  })
_sym_db.RegisterMessage(DrawBallRequest)

ClearGroupRequest = _reflection.GeneratedProtocolMessageType('ClearGroupRequest', (_message.Message,), {
  'DESCRIPTOR' : _CLEARGROUPREQUEST,
  '__module__' : 'WorldAnnotate.world_annotate_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.ClearGroupRequest)
  })
_sym_db.RegisterMessage(ClearGroupRequest)

DrawLineResponse = _reflection.GeneratedProtocolMessageType('DrawLineResponse', (_message.Message,), {
  'DESCRIPTOR' : _DRAWLINERESPONSE,
  '__module__' : 'WorldAnnotate.world_annotate_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.DrawLineResponse)
  })
_sym_db.RegisterMessage(DrawLineResponse)

DrawBallResponse = _reflection.GeneratedProtocolMessageType('DrawBallResponse', (_message.Message,), {
  'DESCRIPTOR' : _DRAWBALLRESPONSE,
  '__module__' : 'WorldAnnotate.world_annotate_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.DrawBallResponse)
  })
_sym_db.RegisterMessage(DrawBallResponse)

ClearGroupResponse = _reflection.GeneratedProtocolMessageType('ClearGroupResponse', (_message.Message,), {
  'DESCRIPTOR' : _CLEARGROUPRESPONSE,
  '__module__' : 'WorldAnnotate.world_annotate_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.ClearGroupResponse)
  })
_sym_db.RegisterMessage(ClearGroupResponse)


DESCRIPTOR._options = None

_WORLDANNOTATE = _descriptor.ServiceDescriptor(
  name='WorldAnnotate',
  full_name='MakaSim.WorldAnnotate',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=448,
  serialized_end=670,
  methods=[
  _descriptor.MethodDescriptor(
    name='DrawLine',
    full_name='MakaSim.WorldAnnotate.DrawLine',
    index=0,
    containing_service=None,
    input_type=_DRAWLINEREQUEST,
    output_type=_DRAWLINERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DrawBall',
    full_name='MakaSim.WorldAnnotate.DrawBall',
    index=1,
    containing_service=None,
    input_type=_DRAWBALLREQUEST,
    output_type=_DRAWBALLRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ClearGroup',
    full_name='MakaSim.WorldAnnotate.ClearGroup',
    index=2,
    containing_service=None,
    input_type=_CLEARGROUPREQUEST,
    output_type=_CLEARGROUPRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_WORLDANNOTATE)

DESCRIPTOR.services_by_name['WorldAnnotate'] = _WORLDANNOTATE

# @@protoc_insertion_point(module_scope)

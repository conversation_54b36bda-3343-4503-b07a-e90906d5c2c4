# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Cameras/camera.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='Cameras/camera.proto',
  package='MakaSim',
  syntax='proto3',
  serialized_options=b'\252\002\026Maka.Sim.Proto.Cameras',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x14\x43\x61meras/camera.proto\x12\x07MakaSim\"\x1b\n\rCameraRequest\x12\n\n\x02id\x18\x01 \x01(\t\"%\n\x04Size\x12\r\n\x05width\x18\x01 \x01(\x05\x12\x0e\n\x06height\x18\x02 \x01(\x05\"J\n\tImageData\x12\x11\n\ttimestamp\x18\x01 \x01(\x03\x12\x1b\n\x04size\x18\x02 \x01(\x0b\x32\r.MakaSim.Size\x12\r\n\x05\x62ytes\x18\x03 \x01(\x0c\x32\x42\n\x06\x43\x61mera\x12\x38\n\x08GetImage\x12\x16.MakaSim.CameraRequest\x1a\x12.MakaSim.ImageData\"\x00\x42\x19\xaa\x02\x16Maka.Sim.Proto.Camerasb\x06proto3'
)




_CAMERAREQUEST = _descriptor.Descriptor(
  name='CameraRequest',
  full_name='MakaSim.CameraRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='MakaSim.CameraRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=33,
  serialized_end=60,
)


_SIZE = _descriptor.Descriptor(
  name='Size',
  full_name='MakaSim.Size',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='width', full_name='MakaSim.Size.width', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='MakaSim.Size.height', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=62,
  serialized_end=99,
)


_IMAGEDATA = _descriptor.Descriptor(
  name='ImageData',
  full_name='MakaSim.ImageData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='MakaSim.ImageData.timestamp', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size', full_name='MakaSim.ImageData.size', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bytes', full_name='MakaSim.ImageData.bytes', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=101,
  serialized_end=175,
)

_IMAGEDATA.fields_by_name['size'].message_type = _SIZE
DESCRIPTOR.message_types_by_name['CameraRequest'] = _CAMERAREQUEST
DESCRIPTOR.message_types_by_name['Size'] = _SIZE
DESCRIPTOR.message_types_by_name['ImageData'] = _IMAGEDATA
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CameraRequest = _reflection.GeneratedProtocolMessageType('CameraRequest', (_message.Message,), {
  'DESCRIPTOR' : _CAMERAREQUEST,
  '__module__' : 'Cameras.camera_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.CameraRequest)
  })
_sym_db.RegisterMessage(CameraRequest)

Size = _reflection.GeneratedProtocolMessageType('Size', (_message.Message,), {
  'DESCRIPTOR' : _SIZE,
  '__module__' : 'Cameras.camera_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.Size)
  })
_sym_db.RegisterMessage(Size)

ImageData = _reflection.GeneratedProtocolMessageType('ImageData', (_message.Message,), {
  'DESCRIPTOR' : _IMAGEDATA,
  '__module__' : 'Cameras.camera_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.ImageData)
  })
_sym_db.RegisterMessage(ImageData)


DESCRIPTOR._options = None

_CAMERA = _descriptor.ServiceDescriptor(
  name='Camera',
  full_name='MakaSim.Camera',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=177,
  serialized_end=243,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetImage',
    full_name='MakaSim.Camera.GetImage',
    index=0,
    containing_service=None,
    input_type=_CAMERAREQUEST,
    output_type=_IMAGEDATA,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CAMERA)

DESCRIPTOR.services_by_name['Camera'] = _CAMERA

# @@protoc_insertion_point(module_scope)

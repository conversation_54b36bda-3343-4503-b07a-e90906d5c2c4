# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: HUD/hud.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='HUD/hud.proto',
  package='MakaSim',
  syntax='proto3',
  serialized_options=b'\252\002\022Maka.Sim.Proto.HUD',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\rHUD/hud.proto\x12\x07MakaSim\"\x1e\n\x0eSetTextRequest\x12\x0c\n\x04text\x18\x01 \x01(\t\"\x11\n\x0fSetTextResponse2E\n\x03HUD\x12>\n\x07SetText\x12\x17.MakaSim.SetTextRequest\x1a\x18.MakaSim.SetTextResponse\"\x00\x42\x15\xaa\x02\x12Maka.Sim.Proto.HUDb\x06proto3'
)




_SETTEXTREQUEST = _descriptor.Descriptor(
  name='SetTextRequest',
  full_name='MakaSim.SetTextRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='text', full_name='MakaSim.SetTextRequest.text', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=26,
  serialized_end=56,
)


_SETTEXTRESPONSE = _descriptor.Descriptor(
  name='SetTextResponse',
  full_name='MakaSim.SetTextResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=58,
  serialized_end=75,
)

DESCRIPTOR.message_types_by_name['SetTextRequest'] = _SETTEXTREQUEST
DESCRIPTOR.message_types_by_name['SetTextResponse'] = _SETTEXTRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SetTextRequest = _reflection.GeneratedProtocolMessageType('SetTextRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETTEXTREQUEST,
  '__module__' : 'HUD.hud_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.SetTextRequest)
  })
_sym_db.RegisterMessage(SetTextRequest)

SetTextResponse = _reflection.GeneratedProtocolMessageType('SetTextResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETTEXTRESPONSE,
  '__module__' : 'HUD.hud_pb2'
  # @@protoc_insertion_point(class_scope:MakaSim.SetTextResponse)
  })
_sym_db.RegisterMessage(SetTextResponse)


DESCRIPTOR._options = None

_HUD = _descriptor.ServiceDescriptor(
  name='HUD',
  full_name='MakaSim.HUD',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=77,
  serialized_end=146,
  methods=[
  _descriptor.MethodDescriptor(
    name='SetText',
    full_name='MakaSim.HUD.SetText',
    index=0,
    containing_service=None,
    input_type=_SETTEXTREQUEST,
    output_type=_SETTEXTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_HUD)

DESCRIPTOR.services_by_name['HUD'] = _HUD

# @@protoc_insertion_point(module_scope)

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from core.unity.proto.hud import hud_pb2 as HUD_dot_hud__pb2


class HUDStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SetText = channel.unary_unary(
                '/MakaSim.HUD/SetText',
                request_serializer=HUD_dot_hud__pb2.SetTextRequest.SerializeToString,
                response_deserializer=HUD_dot_hud__pb2.SetTextResponse.FromString,
                )


class HUDServicer(object):
    """Missing associated documentation comment in .proto file."""

    def SetText(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_HUDServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SetText': grpc.unary_unary_rpc_method_handler(
                    servicer.SetText,
                    request_deserializer=HUD_dot_hud__pb2.SetTextRequest.FromString,
                    response_serializer=HUD_dot_hud__pb2.SetTextResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'MakaSim.HUD', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class HUD(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def SetText(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/MakaSim.HUD/SetText',
            HUD_dot_hud__pb2.SetTextRequest.SerializeToString,
            HUD_dot_hud__pb2.SetTextResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

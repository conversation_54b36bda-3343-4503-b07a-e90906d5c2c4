import os
from collections import defaultdict
from typing import Dict, List, Optional

import grpc
import navpy
import yaml
from shapely.geometry.polygon import Polygon

import core.unity.proto.config.robot_config_pb2 as proto_cfg
import core.unity.proto.config.robot_config_pb2_grpc as grpc_cfg
from core.boot.args import ARG_SIM, UnparsedBootArgsType
from core.boot.device.hardware import HardwareConfigType
from core.controls.driver.config import DriverConfigFile
from core.controls.frame.config import FrameConfigFile
from core.controls.frame.mechanical import MechanicalConfig
from lib.common.file import repo_root
from lib.common.geo.boundary import Boundary, BoundaryType, GeofencesConfigFile
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class UnityRobotConfiguration:
    def __init__(self, hw_cfg: HardwareConfigType, unparsed_boot_args: List[str], boundaries: Dict[str, Boundary]):
        self._hw_cfg: HardwareConfigType = hw_cfg
        self._unparsed_boot_args: List[str] = unparsed_boot_args
        self._boundaries = boundaries

    @property
    def hw_cfg(self) -> HardwareConfigType:
        return self._hw_cfg

    @property
    def unparsed_boot_args(self) -> List[str]:
        return self._unparsed_boot_args

    @property
    def driver_cfg_file(self) -> DriverConfigFile:
        filedir = os.path.join(repo_root(), "core/config/robots/unity/unity-bud/calibration")
        assert os.path.exists(filedir), "Did you move files in the repo?"
        return DriverConfigFile(config_dir=filedir, filename=DriverConfigFile.FILENAME)

    @property
    def frame_cfg_file(self) -> FrameConfigFile:
        filedir = os.path.join(repo_root(), "core/config/robots/unity/unity-bud/calibration")
        assert os.path.exists(filedir), "Did you move files in the repo?"
        return FrameConfigFile(config_dir=filedir, filename=FrameConfigFile.FILENAME)

    @property
    def mechanical_cfg(self) -> MechanicalConfig:
        filepath = os.path.join(repo_root(), "core/config/robots/unity/unity-bud/boot/mechanical.yaml")
        assert os.path.exists(filepath), f"Need access to {filepath} to get mechanical measurements"
        mechanical = yaml.load(open(filepath), Loader=yaml.FullLoader)
        return MechanicalConfig(mechanical["mechanical"])

    @property
    def geofences_cfg_file(self) -> Optional[GeofencesConfigFile]:
        # filedir = os.path.join(repo_root(), "core/config/geo/unity")
        # assert os.path.exists(filedir), "Did you move files in the repo?"
        # return GeofencesConfigFile(config_dir=filedir, filename=GeofencesConfigFile.FILENAME)
        return None

    @property
    def boundaries(self) -> Dict[str, Boundary]:
        return self._boundaries


class UnityConnector:
    def __init__(self, host: str, port: int):
        self._host: str = host
        self._port: int = port
        options = [
            ("grpc.max_send_message_length", 1024 * 1024 * 1024),
            ("grpc.max_receive_message_length", 1024 * 1024 * 1024),
        ]
        LOG.debug(f"Connecting to Unity at {host}:{port}...")
        self._channel: grpc.Channel = grpc.insecure_channel(f"{host}:{port}", options)
        self._config_client: grpc_cfg.RobotConfigStub = grpc_cfg.RobotConfigStub(self._channel)

    @property
    def host(self) -> str:
        return self._host

    @property
    def port(self) -> int:
        return self._port

    @property
    def channel(self) -> grpc.Channel:
        return self._channel

    def get_config(self) -> UnityRobotConfiguration:
        LOG.info("Retrieving Unity config...")
        result = self._config_client.GetConfig(proto_cfg.GetConfigArgs())
        hardware_cfg: HardwareConfigType = defaultdict(lambda: {})
        boot_args_list: UnparsedBootArgsType = []
        for device in result.devices:
            if device.devicePath == "None":
                continue
            elif device.deviceType != ARG_SIM:
                hardware_cfg[device.deviceType][device.id] = {
                    "unity_id": device.id,
                    "bootloader": device.bootloader,
                }
                boot_args_list.extend(["-d", device.devicePath, device.id])
            else:
                boot_args_list.extend(["-d", device.devicePath, ARG_SIM])

        points_crops = []
        points_field = []

        for position in result.geofence.crops.positions:
            lla = navpy.ecef2lla([position.x, position.y, position.z])[:2]
            points_crops.append([lla[1], lla[0]])

        for position in result.geofence.field.positions:
            lla = navpy.ecef2lla([position.x, position.y, position.z])[:2]
            points_field.append([lla[1], lla[0]])

        boundaries = {
            "crops": Boundary(Polygon(points_crops), BoundaryType.CROPS, "crops"),
            "field": Boundary(Polygon(points_field), BoundaryType.FIELD, "field"),
        }

        LOG.info("Retrieved Unity config.")

        return UnityRobotConfiguration(hw_cfg=hardware_cfg, unparsed_boot_args=boot_args_list, boundaries=boundaries)


################
# Global Connector
################


_unity_connector: Optional[UnityConnector] = None


def get_unity_connector() -> UnityConnector:
    assert _unity_connector
    return _unity_connector


def initialize_connector(host: str, port: int) -> None:
    global _unity_connector
    _unity_connector = UnityConnector(host, port)

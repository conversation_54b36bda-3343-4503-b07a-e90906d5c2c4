from typing import Any, Dict, List, Optional

import cv2

from core.cv.retina.camera.node import Cam
from core.cv.visual_cortex.graph.graph import ComputerVisionGraph
from core.cv.visual_cortex.graph.node import ComputerVisionNode
from core.cv.visual_cortex.graph.output import ComputerVisionNodeOutput, FurrowsContext, FurrowsOutput, NodeInput
from core.cv.visual_cortex.graph.pathway import ComputerVisionPathway
from core.cv.visual_cortex.graph.pathway_name import PathwayName
from core.cv.visual_cortex.graph.pathway_output import PathwayOutput
from core.cv.visual_cortex.graph.utility_nodes.subscription import SubscriptionNode
from core.cv.visual_cortex.model.ungenerated import FurrowSlope
from core.unity.cv.model import UnityModel
from core.unity.unity import get_unity_connector
from lib.common.annotate import BGR_CYAN
from lib.common.image import CamImage


class UnityFurrowsNode(ComputerVisionNode[NodeInput[None], FurrowsOutput]):
    def __init__(self, furrows_context: FurrowsContext, cam: Cam, **kwargs: Any):
        super().__init__(f=self._req, **kwargs)
        self._furrows_context = furrows_context
        self._unity_connector = get_unity_connector()
        self._unity_model = UnityModel(self._unity_connector.host, self._unity_connector.port)
        self._cam = cam

    def _req(self, input: NodeInput[None]) -> FurrowsOutput:
        width, height = self._cam.resolution
        target = (width // 2, 3 * height)  # Reference FurrowsDeepLearningNode for the 3x
        slopes, timestamp_ms = self._unity_model.predict(self._cam.id, width, height, target)
        return FurrowsOutput(timestamp_ms=timestamp_ms, slopes=slopes,)

    def annotate(self, cam_image: CamImage, last_output: FurrowsOutput) -> CamImage:
        closest: Optional[FurrowSlope] = last_output.closest_furrow
        if closest is not None:
            cv2.line(cam_image.image_bgr, closest.start, closest.end, color=BGR_CYAN, thickness=3)

        return cam_image


class UnityComputerVisionGraph(ComputerVisionGraph):
    def __init__(
        self, **kwargs: Any,
    ):
        self._unity_connector = get_unity_connector()
        super().__init__(**kwargs)

    @property
    def pathways(self) -> Dict[PathwayName, ComputerVisionPathway[ComputerVisionNodeOutput]]:
        return self._pathways

    def pathway_exists(self, pathway_name: PathwayName) -> bool:
        return pathway_name in self.pathways.keys()

    def _create_pathways(
        self,
        enable_pathways: List[PathwayName],
        back_left_cam: Optional[Cam] = None,
        back_right_cam: Optional[Cam] = None,
        front_left_cam: Optional[Cam] = None,
        front_right_cam: Optional[Cam] = None,
        predict_cams: Optional[List[Cam]] = None,
    ) -> None:
        uc = self._unity_connector
        back_left_cam_image_subscription = back_left_cam.subscription if back_left_cam is not None else None
        back_right_cam_image_subscription = back_right_cam.subscription if back_right_cam is not None else None
        front_left_cam_image_subscription = front_left_cam.subscription if front_left_cam is not None else None
        front_right_cam_image_subscription = front_right_cam.subscription if front_right_cam is not None else None

        self._furrows_nodes = []
        self._furrows_subscriptions = []

        if front_left_cam_image_subscription is not None and uc is not None:
            front_left_subscription_node: SubscriptionNode[NodeInput[None]] = SubscriptionNode(
                front_left_cam_image_subscription
            )

            assert front_left_cam is not None
            furrows_node = UnityFurrowsNode(furrows_context=self._front_right_furrows_context, cam=front_left_cam)

            furrows_pathway = ComputerVisionPathway[FurrowsOutput](
                name=PathwayName.from_single_source(
                    source_id=front_left_cam_image_subscription.topic, output=PathwayOutput.FURROWS
                ),
                output=PathwayOutput.FURROWS,
                subscription_node=front_left_subscription_node,
                computer_vision_node=furrows_node,
            )

            self._register_pathways([furrows_pathway], enable=True)

        if front_right_cam_image_subscription is not None and uc is not None:
            front_right_subscription_node: SubscriptionNode[NodeInput[None]] = SubscriptionNode(
                front_right_cam_image_subscription
            )

            assert front_right_cam is not None
            furrows_node = UnityFurrowsNode(furrows_context=self._front_right_furrows_context, cam=front_right_cam)

            furrows_pathway = ComputerVisionPathway[FurrowsOutput](
                name=PathwayName.from_single_source(
                    source_id=front_right_cam_image_subscription.topic, output=PathwayOutput.FURROWS
                ),
                output=PathwayOutput.FURROWS,
                subscription_node=front_right_subscription_node,
                computer_vision_node=furrows_node,
            )

            self._register_pathways([furrows_pathway], enable=True)

        if back_left_cam_image_subscription is not None and uc is not None:
            back_left_subscription_node: SubscriptionNode[NodeInput[None]] = SubscriptionNode(
                back_left_cam_image_subscription
            )

            assert back_left_cam is not None
            furrows_node = UnityFurrowsNode(furrows_context=self._front_right_furrows_context, cam=back_left_cam)

            furrows_pathway = ComputerVisionPathway[FurrowsOutput](
                name=PathwayName.from_single_source(
                    source_id=back_left_cam_image_subscription.topic, output=PathwayOutput.FURROWS
                ),
                output=PathwayOutput.FURROWS,
                subscription_node=back_left_subscription_node,
                computer_vision_node=furrows_node,
            )

            self._register_pathways([furrows_pathway], enable=True)

        if back_right_cam_image_subscription is not None and uc is not None:
            back_right_subscription_node: SubscriptionNode[NodeInput[None]] = SubscriptionNode(
                back_right_cam_image_subscription
            )

            assert back_right_cam is not None
            furrows_node = UnityFurrowsNode(furrows_context=self._front_right_furrows_context, cam=back_right_cam)

            furrows_pathway = ComputerVisionPathway[FurrowsOutput](
                name=PathwayName.from_single_source(
                    source_id=back_right_cam_image_subscription.topic, output=PathwayOutput.FURROWS
                ),
                output=PathwayOutput.FURROWS,
                subscription_node=back_right_subscription_node,
                computer_vision_node=furrows_node,
            )

            self._register_pathways([furrows_pathway], enable=True)

        for p in enable_pathways:
            if p in self.pathways:
                self.enable_pathway(p)

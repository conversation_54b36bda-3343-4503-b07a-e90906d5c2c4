from typing import List, <PERSON><PERSON>

import numpy as np

import core.unity.proto.prediction.prediction_pb2 as proto_prediction
import core.unity.proto.prediction.prediction_pb2_grpc as grpc_prediction
import core.unity.unity as unity
from core.cv.visual_cortex.model.ungenerated import FurrowSlope
from lib.common.time import maka_control_timestamp_ms

MAX_FURROWS_TO_RETURN_IN_ALL_LINES = 5


class UnityModel:
    def __init__(self, unity_host: str, unity_port: int) -> None:
        unity.initialize_connector(unity_host, unity_port)
        self._client = grpc_prediction.PredictionStub(unity.get_unity_connector().channel)

    def predict(
        self, camera_id: str, width: int, height: int, target: Tuple[int, int]
    ) -> Tuple[List[FurrowSlope], int]:

        furrows: List[FurrowSlope] = []

        timestamp_ms = maka_control_timestamp_ms()

        # get furrows for this camera from Unity GRPC
        ret = self._client.FurrowPrediction(proto_prediction.CameraPredictionRequest(id=camera_id))

        # find closest furrow to p
        # https://stackoverflow.com/questions/39840030/distance-between-point-and-a-line-from-two-points
        p = np.asarray(target)

        furrows_by_distance: List[Tuple[float, proto_prediction.FurrowLine]] = []
        for furrow in ret.furrows:
            s = np.asarray((furrow.start.x * width, furrow.start.y * height))
            e = np.asarray((furrow.end.x * width, furrow.end.y * height))
            distance = np.linalg.norm(np.cross(e - p, s - p)) / np.linalg.norm(e - s)
            furrows_by_distance.append((distance, furrow))

        center_xy = (width // 2, height // 2)

        sorted_list = sorted(furrows_by_distance, key=lambda t: t[0])
        for ct, (distance, furrow) in enumerate(sorted_list):
            # draw closest furrow on masks[idx] == line_expr
            start = np.int32(furrow.start.x * width), np.int32(furrow.start.y * height)
            end = np.int32(furrow.end.x * width), np.int32(furrow.end.y * height)
            if ct == 0:
                furrows.append(
                    FurrowSlope(
                        timestamp_ms=timestamp_ms,
                        image_center=center_xy,
                        image_height=height,
                        image_width=width,
                        start=start,
                        end=end,
                    )
                )
            # WE SHOULD REALLY DRAW ALL LINES BUT UNITY RETURNS A BUNCH OF THINGS THAT
            # AREN"T FURROWS SO WE HALF ATTEMPT TO IT HERE
            if len(furrows) >= MAX_FURROWS_TO_RETURN_IN_ALL_LINES:
                break

        return furrows, timestamp_ms

import abc
import random
import string
from typing import Dict, List, Optional, Sequence, Tuple

import numpy as np

from core.coords.errors import (
    ERROR_INTERPOLATION_BUILD,
    ERROR_INTERPOLATION_SAMPLES_BAD_FORMAT,
    ERROR_INTERPOLATION_SAMPLES_OUTSIDE_STRATA,
    ERROR_INTERPOLATION_SAMPLES_TOO_FEW,
)
from core.coords.hyperparameters import TrainHyperParameters
from core.coords.samples import DeltaPoint, DeltaTargetToDeltaServosSample, Point, ServosPosition
from core.coords.spline import BivariateSplinePair
from core.coords.strata import BivariateStrata
from lib.common.error import MakaException
from lib.common.logging import get_logger

LOG = get_logger(__name__)

_ID_LENGTH = 4
_MAX_EXPANSION_FACTOR = 2  # if we need to expand more than this, something is wrong


class Interpolation(abc.ABC):
    """Base class for interpolations between coordinate systems."""

    def __init__(self, name: str, num_samples: int):
        assert name is not None and isinstance(name, str) and len(name) > 0, 'Bad name: "%s"' % name
        self.name = name
        self.num_samples = num_samples  # how many samples were used during construction
        self.id = Interpolation.generate_id(_ID_LENGTH)

    @staticmethod
    def generate_id(length: int) -> str:
        return "".join(random.choice(string.ascii_lowercase) for _ in range(length))


class BivariateSplinePairInterpolation(Interpolation):
    """Base class for interpolating using a pair of Bivariate Splines"""

    SAMPLES_MIN = 75  # minimum number of samples to build interpolation

    def __init__(self, name: str, num_samples: int, spline_pair: BivariateSplinePair):
        super().__init__(name, num_samples)
        self.spline_pair = spline_pair

    def calibrated(self) -> bool:
        """Whether this interpolation is calibrated and ready to use"""
        return self.spline_pair is not None and self.spline_pair.calibrated()

    @staticmethod
    def _validate_samples(samples: Sequence[Tuple[Point, Point, DeltaPoint]]) -> int:
        if samples is None:
            LOG.error("Samples is None")
            return ERROR_INTERPOLATION_SAMPLES_TOO_FEW

        if len(samples) < BivariateSplinePairInterpolation.SAMPLES_MIN:
            LOG.warning("Insufficient samples for constructing interpolation: %d", len(samples))
            return ERROR_INTERPOLATION_SAMPLES_TOO_FEW

        # accept tuples with > 2 entries so we can carry forward information for debugging
        if (not isinstance(samples[0], tuple) and type(samples[0]).__module__ != np.__name__) or len(samples[0]) < 2:
            LOG.warning("Sample row should be a >2-tuple. Got: %s", type(samples[0]))
            return ERROR_INTERPOLATION_SAMPLES_BAD_FORMAT

        return 0

    @staticmethod
    def construct(
        name: str,
        samples: Sequence[Tuple[Point, Point, DeltaPoint]],
        train_hyperparams: TrainHyperParameters,
        debug_context: Optional[str] = None,
        ignore_validation: bool = False,
    ) -> Tuple[int, Optional["BivariateSplinePairInterpolation"]]:
        ret = BivariateSplinePairInterpolation._validate_samples(samples)
        if ret != 0:
            return ret, None

        spline_pair = BivariateSplinePair(
            name, samples, train_hyperparams, debug_context=debug_context, ignore_validation=ignore_validation
        )

        if not spline_pair.calibrated():
            return ERROR_INTERPOLATION_BUILD, None

        result = BivariateSplinePairInterpolation(name, len(samples), spline_pair)

        return 0, result

    def interpolate(self, xy: Tuple[float, float]) -> Tuple[int, Optional[Tuple[float, float]]]:
        """Interpolate the given point"""
        return self.spline_pair.lookup(xy)

    def raw_interpolate(self, xy: Tuple[float, float]) -> Tuple[float, float]:
        """Interpolate the given point"""
        return self.spline_pair.raw_lookup(xy)


class InterpolationException(MakaException):
    pass


class IndexedBivariateInterpolation(Interpolation):
    """Base class for interpolations between coordinate systems, one interpolation per index."""

    def __init__(self, name: str, num_samples: int, size: int):
        super().__init__(name, num_samples)
        self.size = size
        self.splines: Dict[int, BivariateSplinePairInterpolation] = {}

    def is_index_calibrated(self, pindex: int) -> bool:
        return pindex in self.splines and self.splines[pindex].calibrated()

    def calibrated(self) -> bool:
        """Whether this interpolation is calibrated and ready to use"""
        return 0 < len(self.splines) == len([x for x in self.splines.values() if x.calibrated()])

    @staticmethod
    def construct(
        name: str,
        samples: Sequence[Tuple[int, Point, Point, DeltaPoint]],
        train_hyperparams: TrainHyperParameters,
        debug_context: Optional[str] = None,
        ignore_validation: bool = False,
    ) -> "IndexedBivariateInterpolation":
        indexed_samples: Dict[int, List[Tuple[Point, Point, DeltaPoint]]] = {}
        for sample in samples:
            if sample[0] not in indexed_samples:
                indexed_samples[sample[0]] = []
            indexed_samples[sample[0]].append((sample[1], sample[2], sample[2]))

        interp = IndexedBivariateInterpolation(name, len(samples), len(indexed_samples.keys()))
        for pindex, psamples in indexed_samples.items():
            if len(psamples) < BivariateSplinePairInterpolation.SAMPLES_MIN:
                continue  # We Skip Calibration if there are to few
            ret, spline = BivariateSplinePairInterpolation.construct(
                f"{name}:{pindex}", psamples, train_hyperparams, debug_context, ignore_validation
            )
            if ret != 0:
                raise InterpolationException(f"Failed to construct spline: {name}:{pindex}")
            if spline is not None:
                interp.splines[pindex] = spline

        if len(interp.splines) == 0:
            raise InterpolationException(f"Failed to construct at least 1 spline: {name}")

        return interp

    def interpolate(self, pindex: int, xy: Tuple[float, float]) -> Tuple[int, Optional[Tuple[float, float]]]:
        """Interpolate the given point"""
        if pindex not in self.splines:
            raise InterpolationException(f"PIndex: {pindex} not in interpolation")
        return self.splines[pindex].interpolate(xy)

    def raw_interpolate(self, pindex: int, xy: Tuple[float, float]) -> Tuple[float, float]:
        """Interpolate the given point"""
        if pindex not in self.splines:
            raise InterpolationException(f"PIndex: {pindex} not in interpolation")
        return self.splines[pindex].raw_interpolate(xy)


class StratifiedInterpolation(Interpolation):
    """Base class for interpolations between coordinate systems, one interpolation per stratum."""

    def __init__(self, name: str, num_samples: int, strata: BivariateStrata):
        super().__init__(name, num_samples)

        assert strata is not None
        self.strata = strata


FlatSample = Tuple[DeltaPoint, DeltaPoint, Tuple[int, int], ServosPosition]


class StratifiedBivariateSplinePairInterpolation(StratifiedInterpolation):
    """Base class for interpolating using a pair of Bivariate Splines, one pair per stratum"""

    def __init__(self, name: str, num_samples: int, strata: BivariateStrata, min_samples_per_stratum: int):
        """
        :type strata: BivariateStrata
        """
        super().__init__(name, num_samples, strata)
        assert min_samples_per_stratum > 0
        self.min_samples = min_samples_per_stratum

        self.spline_bounds = {}
        for stratum_id, stratum_bounds_x, stratum_bounds_y in self.strata.generate_strata_bounds():
            self.spline_bounds[stratum_id] = stratum_bounds_x, stratum_bounds_y
        self.interpolations_by_stratum: Dict[str, BivariateSplinePairInterpolation] = {}

        self.samples_by_stratum: Optional[Dict[str, List[FlatSample]]] = None  # cache

    def _validate_samples(self, samples: List[DeltaTargetToDeltaServosSample]) -> int:
        if samples is None:
            LOG.error("Samples is None")
            return ERROR_INTERPOLATION_SAMPLES_TOO_FEW

        # validate sufficient samples
        if len(samples) < self.min_samples:
            LOG.warning("Insufficient samples for constructing interpolation: %d", len(samples))
            return ERROR_INTERPOLATION_SAMPLES_TOO_FEW

        # validate samples are within strata bounds
        samples_outside_limits_ct: int = len(
            [
                s
                for s in samples
                if s.servos[0] < self.strata.x_limits[0]
                or s.servos[0] > self.strata.x_limits[1]
                or s.servos[1] < self.strata.y_limits[0]
                or s.servos[1] > self.strata.y_limits[1]
            ]
        )
        if samples_outside_limits_ct > 0:
            LOG.warning(
                "{} {}/{} ({:.2f}%) samples outside strata limits {},{}".format(
                    self.name,
                    samples_outside_limits_ct,
                    len(samples),
                    100 * samples_outside_limits_ct / len(samples),
                    self.strata.x_limits,
                    self.strata.y_limits,
                )
            )

        return 0

    def _cache_samples_by_strata(self, samples: List[DeltaTargetToDeltaServosSample]) -> None:
        """
        Store the samples by stratum
        """
        LOG.debug("Caching {} samples by stratum...".format(len(samples)))
        self.samples_by_stratum = {}
        for stratum_id, (stratum_bounds_x, stratum_bounds_y) in sorted(self.spline_bounds.items()):
            stratum_samples = [
                s.tuples()
                for s in samples
                if stratum_bounds_x[0] <= s.servos[0] <= stratum_bounds_x[1]
                and stratum_bounds_y[0] <= s.servos[1] <= stratum_bounds_y[1]
            ]
            self.samples_by_stratum[stratum_id] = stratum_samples
            if len(stratum_samples) < self.min_samples:
                LOG.warning("Stratum [%s] has only %d samples", stratum_id, len(stratum_samples))
        LOG.debug("Finished caching {} samples by stratum.".format(len(samples)))

    def expand_stratum_samples(self, stratum_id: str, expansion_factor: int = 0) -> List[FlatSample]:
        """
        Get all the samples in the given stratum_id, along with the neighbor strata as defined by the expansion factor.
        A factor of 1 means do not expand. A factor of 2 means take all 8 neighbors, etc.
        """
        assert expansion_factor >= 0 and isinstance(expansion_factor, int), "Bad expansion factor: %s" % str(
            expansion_factor
        )
        base_stratum_id_x, base_stratum_id_y = tuple([int(s) for s in stratum_id.split(",")])
        samples: List[FlatSample] = []  # create a new list instance so as to not modify underlying data
        assert self.samples_by_stratum is not None
        samples += self.samples_by_stratum[stratum_id]

        # base case of 0 means no loop to add anything
        for x_i in range(1, expansion_factor + 1):  # loop starting at 1 because we don't expand in base case
            id_x_plus = base_stratum_id_x + x_i
            id_x_minus = base_stratum_id_x - x_i
            for y_i in range(1, expansion_factor + 1):
                id_y_plus = base_stratum_id_y + y_i
                id_y_minus = base_stratum_id_y - y_i

                neighbors = [
                    (id_x_plus, id_y_plus),
                    (id_x_plus, id_y_minus),
                    (id_x_minus, id_y_minus),
                    (id_x_minus, id_y_plus),
                ]

                # expand to include neighbor samples
                # TODO improvements to consider:
                #   - use numpy everywhere
                #   - add a balanced ratio of samples from each neighbor
                #   - add closest samples up until we have the desired amount (put all in np array,
                #   compute dist from stratum centroid, sort by dist, take top n)
                for id_xy in neighbors:
                    stratum_id = BivariateStrata.get_stratum_id(*id_xy)
                    neighbor_samples = self.samples_by_stratum.get(stratum_id, [])
                    if len(neighbor_samples) > 0:
                        LOG.debug("Adding %d samples from %s for %s", len(neighbor_samples), id_xy, stratum_id)
                        samples.extend(neighbor_samples)
        return samples

    @staticmethod
    def construct(
        name: str,
        strata: BivariateStrata,
        min_samples_per_stratum: int,
        samples: List[DeltaTargetToDeltaServosSample],
        train_hyperparams: TrainHyperParameters,
        debug_context: Optional[str] = None,
        ignore_validation: bool = False,
    ) -> Tuple[int, Optional["StratifiedBivariateSplinePairInterpolation"]]:
        """Construct the underlying pairs of Bivariate Splines for each stratum"""
        result: StratifiedBivariateSplinePairInterpolation = StratifiedBivariateSplinePairInterpolation(
            name, len(samples), strata, min_samples_per_stratum
        )

        try:
            # validate samples
            ret: int = result._validate_samples(samples)
            if ret != 0:
                return ret, None
            result.num_samples = len(samples)

            # cache samples
            result._cache_samples_by_strata(samples)

            # recursively expand samples for each stratum until there are sufficiently many
            for stratum_id, stratum_bounds_x, stratum_bounds_y in result.strata.generate_strata_bounds(
                snake_iteration=False
            ):
                stratum_interpolation_name = "%s[%s]" % (name, stratum_id)

                interpolation: Optional[BivariateSplinePairInterpolation] = None
                expansion_factor: int = 0
                ret = -1
                # Try to construct interpolation
                #   Base case with expansion_factor 0: Only use samples from this stratum
                #   N+1 case: bring in neighbor samples based on rising expansion factor
                #   End when enough samples + valid interpolation constructed
                while ret != 0 and expansion_factor <= min(
                    _MAX_EXPANSION_FACTOR, max(result.strata.num_strata_x, result.strata.num_strata_y)
                ):
                    if expansion_factor != 0:
                        LOG.debug(
                            "%s expanding to include neighbor stratum up to %d away...",
                            stratum_interpolation_name,
                            expansion_factor,
                        )
                    flat_samples: List[FlatSample] = result.expand_stratum_samples(stratum_id, expansion_factor)

                    if len(flat_samples) < result.min_samples:
                        LOG.warning(
                            "%s Found only %d samples for stratum %s",
                            stratum_interpolation_name,
                            len(flat_samples),
                            stratum_id,
                        )
                        ret = ERROR_INTERPOLATION_SAMPLES_TOO_FEW  # this may be updated in next loop as we try again
                    else:
                        LOG.info(
                            "%s Constructing interpolation with %d samples", stratum_interpolation_name, len(samples),
                        )
                        ret, interpolation = BivariateSplinePairInterpolation.construct(
                            stratum_interpolation_name,
                            [f[:3] for f in flat_samples],
                            train_hyperparams,
                            debug_context=debug_context,
                            ignore_validation=ignore_validation,
                        )

                    if ret != 0:
                        LOG.warning(
                            "%s Constructing interpolation for stratum %s with expansion factor %d failed with error "
                            "%d",
                            stratum_interpolation_name,
                            stratum_id,
                            expansion_factor,
                            ret,
                        )

                    expansion_factor += 1

                if ret != 0:
                    LOG.error(
                        "{} Constructing interpolation for stratum {} failed with error {}".format(
                            stratum_interpolation_name, stratum_id, ret
                        )
                    )
                    return ret, None

                # store result
                assert (
                    interpolation is not None
                ), f"Bad state! Interpolation is None but ret = {ret}, expansion_factor = {expansion_factor}. Refactor gone bad?"
                result.interpolations_by_stratum[stratum_id] = interpolation

            return 0, result
        finally:
            result.samples_by_stratum = None  # Deleting Cache

    def interpolate(
        self, strata_xy: Tuple[int, int], xy: Tuple[float, float], nearest_stratum: bool = True, logging: bool = False
    ) -> Tuple[int, Optional[Tuple[float, float]]]:
        """Interpolate the given point"""
        stratum_id = self.strata.compute_bounding_stratum(strata_xy, nearest=nearest_stratum)
        if stratum_id is None:
            LOG.error(
                "{} Unable to interpolate. No stratum found for {}, so no matching interpolation.".format(
                    self.name, strata_xy
                )
            )
            return ERROR_INTERPOLATION_SAMPLES_OUTSIDE_STRATA, None

        if logging and len(self.spline_bounds) > 1:
            LOG.debug("Interpolating using stratum id %s", stratum_id)
        spline_pair_interpolation = self.interpolations_by_stratum[stratum_id]
        return spline_pair_interpolation.interpolate(xy)

    def raw_interpolate(
        self, strata_xy: Tuple[int, int], xy: Tuple[float, float], nearest_stratum: bool = True, logging: bool = False
    ) -> Tuple[float, float]:
        """Interpolate the given point"""
        stratum_id = self.strata.compute_bounding_stratum(strata_xy, nearest=nearest_stratum)
        if stratum_id is None:
            raise MakaException(
                "{} Unable to interpolate. No stratum found for {}, so no matching interpolation.".format(
                    self.name, strata_xy
                )
            )
        spline_pair_interpolation = self.interpolations_by_stratum[stratum_id]
        return spline_pair_interpolation.raw_interpolate(xy)

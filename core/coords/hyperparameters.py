import math

from attr import dataclass

MAX_ERROR_RATIO = 0.02
DROPOUT_THRESHOLD_RATIO = 0.04
SMOOTHING_FACTOR_RATIO = 1000


@dataclass
class TrainHyperParameters:
    """
    Container class for hyperparameters related to training a model.

    This first pass implementation makes a lot of assumptions about the current way we fit a spline.

    This will probably grow as we have different optimization procedures for different functions we are learning (camera calibration, PID, etc.).
    """

    max_error: float
    dropout_threshold: float
    max_dropout_ratio: float
    dropout_step_size: float
    max_iterations: int
    smoothing_factor: float

    def __str__(self) -> str:
        return str(vars(self))

    # TEMPORARY WAY UNTIL WE MODEL THIS BETTER
    @staticmethod
    def guess(resolution_range: float) -> "TrainHyperParameters":
        return TrainHyperParameters(
            max_error=math.ceil(MAX_ERROR_RATIO * resolution_range),
            dropout_threshold=math.ceil(DROPOUT_THRESHOLD_RATIO * resolution_range),
            max_dropout_ratio=0.1,
            dropout_step_size=0.05,
            max_iterations=10,
            smoothing_factor=math.ceil(SMOOTHING_FACTOR_RATIO * resolution_range),
        )

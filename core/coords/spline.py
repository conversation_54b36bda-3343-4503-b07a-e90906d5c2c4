from typing import Any, List, Optional, Sequence, Tuple, Union

import numpy as np
import numpy.typing as npt
from scipy.interpolate import SmoothBivariateSpline

import lib.common.logging
from core.coords.errors import ERROR_INTERPOLATION_NO_X_SPLINE, ERROR_INTERPOLATION_NO_Y_SPLINE
from core.coords.hyperparameters import TrainHyperParameters
from core.coords.samples import DeltaPoint, Point
from lib.common.statistics import Errors, errors

LOG = lib.common.logging.get_logger(__name__)


class BivariateSplinePair:
    """
    Convert between two two dimensional coordinate systems given a set of samples that coincide in both coordinate
    systems. The two systems must smoothly translate.

    The error rate of the spline is typically proportional to data quality.
    """

    DIMENSIONS = (2, 2)  # (x, y) -> (X, Y)

    # Attempt to construct an X/Y spline pair from pair of tuples
    def __init__(
        self,
        name: str,
        train_list: Sequence[Tuple[Point, Point, DeltaPoint]],
        train_hyperparams: TrainHyperParameters,
        debug_context: Optional[str] = None,
        ignore_validation: bool = False,
    ):
        """
        :param name: The name of the built spline.
        :param train: The training data.
        :type train_hyperparams: TrainHyperParameters
        :param debug_context: debug information to print at the end of the line-item logs
        """
        """
        The given samples will be converted into an array and flattened,
        after which it should have at least 4 column dimensions
        """
        self.name: str = name
        self.num_train: int = len(train_list)
        self.train_hyperparams: TrainHyperParameters = train_hyperparams

        self.X_spline: Optional[SmoothBivariateSpline] = None  # (x, y) -> X spline
        self.Y_spline: Optional[SmoothBivariateSpline] = None  # (x, y) -> Y spline
        self.errs_x: Optional[Errors] = None  # errors for X spline on training samples
        self.errs_y: Optional[Errors] = None  # errors for Y spline on training samples
        self._debug_context: str = debug_context if debug_context is not None else ""
        self._unique_debug_logs: List[str] = []
        self._ignore_validation: bool = ignore_validation

        # TODO Move construction logic outside of init
        if train_list is None or len(train_list) == 0:
            return

        train = np.array(train_list)
        # flatten
        train = train.reshape(train.shape[0], -1)
        assert train.shape[1] >= sum(BivariateSplinePair.DIMENSIONS), (
            "Insufficient sample dimensions: %s. This is a bug." % train.shape
        )
        # append indices as a column so we have them later for debugging
        train = np.concatenate([train, np.array(np.arange(train.shape[0]), ndmin=2).T], axis=1)
        subsamples = train
        dropout_ratio: float = 0

        # TODO model this whole loop construct as an optimization procedure that accepts TrainHyperParameters
        # and a generic model builder for splines that has an API for train/test, etc.
        #
        # Remove outliers until we have a good set of splines that works with the majority of the dataset
        LOG.debug(
            "{} Fitting BiVariateSplinePair to {}x{}x{} training matrix using hyper parameters {}".format(
                self.name, *BivariateSplinePair.DIMENSIONS, self.num_train, self.train_hyperparams
            )
        )
        errs_x: Errors
        errs_y: Errors
        for _ in range(self.train_hyperparams.max_iterations):
            # randomize samples to make a filtering spline
            idx = np.random.randint(subsamples.shape[0], size=int(train.shape[0] * (1 - dropout_ratio)))

            # Construct and test
            test_samples = subsamples[idx, :]
            self.construct(test_samples)
            errs_x, errs_y = self.test(test_samples, logging=False)  # type: ignore
            # If the maximum error from the training set is too high try again with a different random set
            if max(errs_x.max, errs_y.max) > self.train_hyperparams.dropout_threshold:
                if dropout_ratio < self.train_hyperparams.max_dropout_ratio:
                    dropout_ratio = min(
                        self.train_hyperparams.max_dropout_ratio,
                        dropout_ratio + self.train_hyperparams.dropout_step_size,
                    )
                continue

            # Filter subsamples with high error
            # TODO this I think is pointless to do if we are already at maximum dropout since if we filter anything
            #  we will fail the dropout ratio test. We should just go straight into the next logic
            subsamples = self.filter(subsamples, train_hyperparams.max_error)
            # Restart the loop with a new set of points if we've thrown out too much
            if subsamples.shape[0] < train.shape[0] * (1 - self.train_hyperparams.max_dropout_ratio):
                filtered = train.shape[0] - subsamples.shape[0]
                max_allowed = self.train_hyperparams.max_dropout_ratio * train.shape[0]
                LOG.info(
                    "{} Filtered {}/{} samples but max allowed was {}".format(
                        self.name, filtered, train.shape[0], max_allowed
                    )
                )
                old_dropout_ratio = dropout_ratio
                if dropout_ratio < self.train_hyperparams.max_dropout_ratio:
                    dropout_ratio = min(
                        self.train_hyperparams.max_dropout_ratio,
                        dropout_ratio + self.train_hyperparams.dropout_step_size,
                    )
                LOG.info("{} Old dropout was {}, moving to {}".format(self.name, old_dropout_ratio, dropout_ratio))

                # TODO this is really confusing that this "subsample" variable gets update to include everything.
                # There are better ways to model this
                subsamples = train
                continue

            # Construct and test after filtering
            self.construct(subsamples)
            errs_x, errs_y = self.test(subsamples, log_prefix="{} Resulting spline".format(self.name))  # type: ignore

            # Exit if success
            if max(errs_x.max, errs_y.max) <= self.train_hyperparams.max_error:
                # Success
                self.errs_x = errs_x
                self.errs_y = errs_y
                return

        if self._ignore_validation:
            LOG.info("Ignoring Spline Validation and Constructing Anyway")
            if self.X_spline is None or self.Y_spline is None:
                self.construct(train)
        else:
            self.X_spline = None
            self.Y_spline = None
        LOG.error(
            "%s Failed to construct spline. Max error (%.02f, %.02f) > %.02f",
            self.name,
            errs_x.max,
            errs_y.max,
            self.train_hyperparams.max_error,
        )
        for log_str in self._unique_debug_logs:
            LOG.debug(log_str)

    def log_errors(self, log_prefix: str, errs_x: Errors, errs_y: Errors, num_samples: int) -> None:
        log_line = (
            "%s min (%.02f, %.02f) mean (%.02f, %.02f) max (%.02f, %.02f) stddev (%.02f, %.02f) "
            "spline error, samples %s"
            % (
                log_prefix,
                errs_x.min,
                errs_y.min,
                errs_x.mean,
                errs_y.mean,
                errs_x.max,
                errs_y.max,
                errs_x.stdev,
                errs_y.stdev,
                num_samples,
            )
        )
        if max(errs_x.max, errs_y.max) > self.train_hyperparams.max_error:
            LOG.warning(log_line)
        else:
            LOG.info(log_line)

    def calibrated(self) -> bool:
        return self.X_spline is not None and self.Y_spline is not None

    def construct(self, samples: npt.NDArray[Any]) -> None:
        assert samples.shape[1] >= 4, "Sample data has bad shape: %s" % samples.shape
        x = samples[:, 0]
        y = samples[:, 1]
        X = samples[:, 2]
        Y = samples[:, 3]
        self.X_spline = SmoothBivariateSpline(x, y, X, kx=5, ky=5, s=self.train_hyperparams.smoothing_factor)
        self.Y_spline = SmoothBivariateSpline(x, y, Y, kx=5, ky=5, s=self.train_hyperparams.smoothing_factor)

    def test(
        self, samples: npt.NDArray[Any], raw_errors: bool = False, logging: bool = True, log_prefix: str = ""
    ) -> Union[Tuple[Errors, Errors], Tuple[Errors, Errors, List[float], List[float]]]:
        """
        Compute the avg, max, std dev of error for given known samples, vs spline prediction
        """
        errs_x_list: List[float] = []
        errs_y_list: List[float] = []
        count_bad_x = count_bad_y = count_bad = 0
        for s in samples:
            ret, predict_xy = self.lookup((s[0], s[1]))
            if ret != 0:
                LOG.error("Unable to lookup sample %s due to error %d", s, ret)
                continue
            assert predict_xy is not None
            predict_x, predict_y = predict_xy

            bad = False
            err_x = abs(predict_x - s[2])
            if err_x > self.train_hyperparams.dropout_threshold:
                bad = True
                count_bad_x += 1

            err_y = abs(predict_y - s[3])
            if err_y > self.train_hyperparams.dropout_threshold:
                bad = True
                count_bad_y += 1
            count_bad += int(bad)

            errs_x_list.append(err_x)
            errs_y_list.append(err_y)

        errs_x = errors(errs_x_list)
        errs_y = errors(errs_y_list)

        # log worst error if above threshold for logging
        if (
            logging
            and errs_x.max > self.train_hyperparams.dropout_threshold
            or errs_y.max > self.train_hyperparams.dropout_threshold
        ):
            # log really bad errors
            if errs_x.max >= errs_y.max:
                errs_index = np.array(errs_x_list).argmax()
            else:
                errs_index = np.array(errs_y_list).argmax()
            bad_row = samples[errs_index]
            bad_row_index = int(bad_row[-1])
            LOG.warning(
                "{} {}/{} failed dropout error threshold = {}".format(
                    self.name, count_bad, len(samples), self.train_hyperparams.dropout_threshold
                )
            )
            log_str = "{} Worst: error=({:.2f}, {:.2f}) for train[{}] = ".format(
                self.name, errs_x.max, errs_y.max, bad_row_index
            )
            log_str += "({:.2f}, {:.2f}) -> ({:.2f}, {:.2f})".format(*bad_row[0:4])
            log_str += (
                ", debug data = {}, debug context = {}".format(bad_row[4:-1].tolist(), self._debug_context)
                .replace(".0, ", ", ")
                .replace(".0]", "]")
            )  # kind of hack way but makes nicer logs dT->dS
            LOG.warning(log_str)
            if log_str not in self._unique_debug_logs:
                self._unique_debug_logs.append(log_str)

        if logging:
            self.log_errors(log_prefix, errs_x, errs_y, samples.shape[0])

        if raw_errors:
            return errs_x, errs_y, errs_x_list, errs_y_list
        else:
            return errs_x, errs_y

    def filter(self, samples: npt.NDArray[Any], err_threshold: float, logging: bool = True) -> npt.NDArray[Any]:
        """
        Drop samples with prediction error greater than given threshold
        """
        errs_x, errs_y, errs_x_list, errs_y_list = self.test(  # type: ignore
            samples, raw_errors=True, logging=logging, log_prefix="%s Filtering spline" % self.name
        )

        filtered = [s for i, s in enumerate(samples) if max(errs_x_list[i], errs_y_list[i]) <= err_threshold]

        return np.array(filtered)

    def lookup(self, coord: Tuple[float, float]) -> Tuple[int, Optional[Tuple[int, int]]]:
        if self.X_spline is None:
            return ERROR_INTERPOLATION_NO_X_SPLINE, None
        if self.Y_spline is None:
            return ERROR_INTERPOLATION_NO_Y_SPLINE, None
        # round and then cast to ints
        x = int(np.around(self.X_spline(*coord)))
        y = int(np.around(self.Y_spline(*coord)))
        return 0, (x, y)

    def raw_lookup(self, coord: Tuple[float, float]) -> Tuple[float, float]:
        assert self.X_spline is not None and self.Y_spline is not None
        return float(np.around(self.X_spline(*coord))), float(np.around(self.Y_spline(*coord)))

from typing import Tuple

import numpy as np

from lib.common.math import distance_2d, multiply_tuple, solve_adjacent


class Dimensions:
    """
    Container object for represent the below coordinate system as a system of variables {x,y,z,Ѳ}
    (and xy computable from x and y) based on the below coordinate system:

              /|
             /Ѳ|
            /  |
           /   | z
          /    |
         /     |
        --------
           xy

    where xy = sqrt(x^2 + y^2) based on below coordinate system:

            /|
           / |
          /  |
      xy /   | y
        /    |
       /     |
      --------
          x

    satisfying:
      Ѳ, x, y any real number
      z > 0 if x != 0 or y != 0, else 0

    with units:
      Ѳ specified in radians
      x, y, z may be specified in any units (same as each other)
    """

    def __init__(self, theta: float, x: float, y: float):
        # by design, try to accept any values which can satisfy the underlying math
        assert theta is not None
        assert not np.isnan(theta)
        self.theta: float = theta  # radians

        assert x is not None
        assert not np.isnan(x)
        self.x: float = x

        assert y is not None
        assert not np.isnan(y)
        self.y: float = y

        self.xy: float = distance_2d((self.x, self.y))
        assert self.xy is not None
        assert not np.isnan(self.xy), "Could not compute distance for (x, y) = ({}, {})".format(self.x, self.y)

        # tan(Ѳ) = xy / z ==> so z = xy / tan(Ѳ)
        self.z: float = solve_adjacent(theta=self.theta, opposite=self.xy)
        assert self.z is not None
        assert not np.isnan(self.z), "Could not solve triangle with theta = {} and opposite side = {}".format(
            self.theta, self.xy
        )


class ServoDimensions:
    """Container object for estimates of dimensions related to servos"""

    def __init__(
        self, pan_theta: float, pan_means: Tuple[float, float], tilt_theta: float, tilt_means: Tuple[float, float],
    ):
        assert 1 > pan_theta > 0, "Unexpected pan theta: %s" % pan_theta
        assert len(pan_means) == 2, f"Expected pan_means = (x, y) but got: {pan_means}"
        self.pan: Dimensions = Dimensions(pan_theta, *pan_means)

        assert 1 > tilt_theta > 0, "Unexpected tilt theta: %s" % tilt_theta
        assert len(tilt_means) == 2, f"Expected tilt_means = (x, y) but got: {tilt_means}"
        self.tilt: Dimensions = Dimensions(tilt_theta, *tilt_means)

        self.z = np.mean([self.pan.z, self.tilt.z]).item()
        assert self.z is not None
        assert not np.isnan(self.z)


# This can be modeled better later as we integrate it more.
# For now this is just a simple container to easily bundle the concepts.
class RealWorldDimensions:
    """Container object for various estimates of real world dimensions"""

    def __init__(self, servo_tdims: ServoDimensions, mm_per_tpx: float):
        assert mm_per_tpx is not None and mm_per_tpx > 0
        # millimeters
        self.mm_per_tpx = mm_per_tpx
        self.tpx_per_mm = 1 / self.mm_per_tpx
        # inches
        self.in_per_tpx = mm_per_tpx / 25.4
        self.tpx_per_in = 1 / self.in_per_tpx

        # dimensions in mm
        self.mm = ServoDimensions(
            servo_tdims.pan.theta,
            multiply_tuple((servo_tdims.pan.x, servo_tdims.pan.y), self.mm_per_tpx),
            servo_tdims.tilt.theta,
            multiply_tuple((servo_tdims.tilt.x, servo_tdims.tilt.y), self.mm_per_tpx),
        )

        # dimensions in inches (can't use name "in" because its reserved)
        self.inches = ServoDimensions(
            servo_tdims.pan.theta,
            multiply_tuple((servo_tdims.pan.x, servo_tdims.pan.y), self.in_per_tpx),
            servo_tdims.tilt.theta,
            multiply_tuple((servo_tdims.tilt.x, servo_tdims.tilt.y), self.in_per_tpx),
        )

        # sanity check
        assert (
            abs(self.mm.z / 25.4 - self.inches.z) < 0.00001
        ), "Unexpected self.dim_mm.z / 25.4 = %f != %f = self.dim_in.z" % (self.mm.z / 25.4, self.inches.z)

from typing import <PERSON>ple

from core.coords.strata import BivariateStrata


def get_strata(
    x_limits: <PERSON><PERSON>[float, float], y_limits: <PERSON><PERSON>[float, float], num_strata_x: int, num_strata_y: int
) -> BivariateStrata:
    return BivariateStrata("x", "y", x_limits, y_limits, num_strata_x, num_strata_y)


def test_strata_1by1_limits_1by1() -> None:
    limits = (0, 0)
    num = 1
    strata1 = get_strata(limits, limits, num, num)
    assert strata1.compute_bounding_stratum((0, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((min(limits) - 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, min(limits) - 1)) is None
    assert strata1.compute_bounding_stratum((max(limits) + 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, max(limits) + 1)) is None


def test_strata_1by1_limits_2by2() -> None:
    limits = (0, 1)
    num_x = num_y = 1
    strata1 = get_strata(limits, limits, num_x, num_y)
    assert strata1.x_limits == limits
    assert strata1.y_limits == limits
    assert strata1.num_strata_x == num_x
    assert strata1.num_strata_y == num_y
    assert strata1.compute_bounding_stratum((0, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((0, 1)) == "0,0"
    assert strata1.compute_bounding_stratum((1, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((1, 1)) == "0,0"
    assert strata1.compute_bounding_stratum((min(limits) - 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, min(limits) - 1)) is None
    assert strata1.compute_bounding_stratum((max(limits) + 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, max(limits) + 1)) is None


def test_strata_1by1_limits_2by2_negative() -> None:
    limits = (-1, 0)
    num_x = num_y = 1
    strata1 = get_strata(limits, limits, num_x, num_y)
    assert strata1.x_limits == limits
    assert strata1.y_limits == limits
    assert strata1.num_strata_x == num_x
    assert strata1.num_strata_y == num_y
    assert strata1.compute_bounding_stratum((-1, -1)) == "0,0"
    assert strata1.compute_bounding_stratum((-1, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((0, -1)) == "0,0"
    assert strata1.compute_bounding_stratum((0, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((min(limits) - 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, min(limits) - 1)) is None
    assert strata1.compute_bounding_stratum((max(limits) + 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, max(limits) + 1)) is None


def test_strata_2by2_limits_2by2() -> None:
    limits = (0, 1)
    num_x = num_y = 2
    strata1 = get_strata(limits, limits, num_x, num_y)
    assert strata1.x_limits == limits
    assert strata1.y_limits == limits
    assert strata1.num_strata_x == num_x
    assert strata1.num_strata_x == num_y
    assert strata1.compute_bounding_stratum((0, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((0, 1)) == "0,1"
    assert strata1.compute_bounding_stratum((1, 0)) == "1,0"
    assert strata1.compute_bounding_stratum((1, 1)) == "1,1"
    assert strata1.compute_bounding_stratum((min(limits) - 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, min(limits) - 1)) is None
    assert strata1.compute_bounding_stratum((max(limits) + 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, max(limits) + 1)) is None


def test_strata_2by2_limits_2by2_negative() -> None:
    limits = (-1, 0)
    num_x = num_y = 2
    strata1 = get_strata(limits, limits, num_x, num_y)
    assert strata1.x_limits == limits
    assert strata1.y_limits == limits
    assert strata1.num_strata_x == num_x
    assert strata1.num_strata_x == num_y
    assert strata1.compute_bounding_stratum((-1, -1)) == "0,0"
    assert strata1.compute_bounding_stratum((-1, 0)) == "0,1"
    assert strata1.compute_bounding_stratum((0, -1)) == "1,0"
    assert strata1.compute_bounding_stratum((0, 0)) == "1,1"
    assert strata1.compute_bounding_stratum((min(limits) - 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, min(limits) - 1)) is None
    assert strata1.compute_bounding_stratum((max(limits) + 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, max(limits) + 1)) is None


def test_strata_2by2_limits_4by4() -> None:
    limits = (0, 3)
    num_x = num_y = 2
    strata1 = get_strata(limits, limits, num_x, num_y)
    assert strata1.x_limits == limits
    assert strata1.y_limits == limits
    assert strata1.num_strata_x == num_x
    assert strata1.num_strata_x == num_y
    assert strata1.compute_bounding_stratum((0, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((0, 1)) == "0,0"
    assert strata1.compute_bounding_stratum((0, 2)) == "0,1"
    assert strata1.compute_bounding_stratum((0, 3)) == "0,1"
    assert strata1.compute_bounding_stratum((1, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((1, 1)) == "0,0"
    assert strata1.compute_bounding_stratum((1, 2)) == "0,1"
    assert strata1.compute_bounding_stratum((1, 3)) == "0,1"
    assert strata1.compute_bounding_stratum((2, 0)) == "1,0"
    assert strata1.compute_bounding_stratum((2, 1)) == "1,0"
    assert strata1.compute_bounding_stratum((2, 2)) == "1,1"
    assert strata1.compute_bounding_stratum((2, 3)) == "1,1"
    assert strata1.compute_bounding_stratum((3, 0)) == "1,0"
    assert strata1.compute_bounding_stratum((3, 1)) == "1,0"
    assert strata1.compute_bounding_stratum((3, 2)) == "1,1"
    assert strata1.compute_bounding_stratum((3, 3)) == "1,1"
    assert strata1.compute_bounding_stratum((min(limits) - 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, min(limits) - 1)) is None
    assert strata1.compute_bounding_stratum((max(limits) + 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, max(limits) + 1)) is None


def test_strata_2by2_limits_4by4_negative() -> None:
    limits = (-3, 0)
    num_x = num_y = 2
    strata1 = get_strata(limits, limits, num_x, num_y)
    assert strata1.x_limits == limits
    assert strata1.y_limits == limits
    assert strata1.num_strata_x == num_x
    assert strata1.num_strata_x == num_y
    assert strata1.compute_bounding_stratum((-3, -3)) == "0,0"
    assert strata1.compute_bounding_stratum((-3, -2)) == "0,0"
    assert strata1.compute_bounding_stratum((-3, -1)) == "0,1"
    assert strata1.compute_bounding_stratum((-3, 0)) == "0,1"
    assert strata1.compute_bounding_stratum((-2, -3)) == "0,0"
    assert strata1.compute_bounding_stratum((-2, -2)) == "0,0"
    assert strata1.compute_bounding_stratum((-2, -1)) == "0,1"
    assert strata1.compute_bounding_stratum((-2, 0)) == "0,1"
    assert strata1.compute_bounding_stratum((-1, -3)) == "1,0"
    assert strata1.compute_bounding_stratum((-1, -2)) == "1,0"
    assert strata1.compute_bounding_stratum((-1, -1)) == "1,1"
    assert strata1.compute_bounding_stratum((-1, 0)) == "1,1"
    assert strata1.compute_bounding_stratum((0, -3)) == "1,0"
    assert strata1.compute_bounding_stratum((0, -2)) == "1,0"
    assert strata1.compute_bounding_stratum((0, -1)) == "1,1"
    assert strata1.compute_bounding_stratum((0, 0)) == "1,1"
    assert strata1.compute_bounding_stratum((min(limits) - 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, min(limits) - 1)) is None
    assert strata1.compute_bounding_stratum((max(limits) + 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, max(limits) + 1)) is None


def test_strata_1by2_limits_4by4() -> None:
    limits = (0, 3)
    num_x = 1
    num_y = 2
    strata1 = get_strata(limits, limits, num_x, num_y)
    assert strata1.x_limits == limits
    assert strata1.y_limits == limits
    assert strata1.num_strata_x == num_x
    assert strata1.num_strata_y == num_y
    assert strata1.compute_bounding_stratum((0, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((0, 1)) == "0,0"
    assert strata1.compute_bounding_stratum((1, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((1, 1)) == "0,0"
    assert strata1.compute_bounding_stratum((2, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((2, 1)) == "0,0"
    assert strata1.compute_bounding_stratum((3, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((3, 1)) == "0,0"
    assert strata1.compute_bounding_stratum((0, 2)) == "0,1"
    assert strata1.compute_bounding_stratum((0, 3)) == "0,1"
    assert strata1.compute_bounding_stratum((1, 2)) == "0,1"
    assert strata1.compute_bounding_stratum((1, 3)) == "0,1"
    assert strata1.compute_bounding_stratum((2, 2)) == "0,1"
    assert strata1.compute_bounding_stratum((2, 3)) == "0,1"
    assert strata1.compute_bounding_stratum((3, 2)) == "0,1"
    assert strata1.compute_bounding_stratum((3, 3)) == "0,1"
    assert strata1.compute_bounding_stratum((min(limits) - 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, min(limits) - 1)) is None
    assert strata1.compute_bounding_stratum((max(limits) + 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, max(limits) + 1)) is None


def test_strata_1by2_limits_4by4_negative() -> None:
    limits = (-3, 0)
    num_x = 1
    num_y = 2
    strata1 = get_strata(limits, limits, num_x, num_y)
    assert strata1.x_limits == limits
    assert strata1.y_limits == limits
    assert strata1.num_strata_x == num_x
    assert strata1.num_strata_y == num_y
    assert strata1.compute_bounding_stratum((-3, -3)) == "0,0"
    assert strata1.compute_bounding_stratum((-3, -2)) == "0,0"
    assert strata1.compute_bounding_stratum((-3, -1)) == "0,1"
    assert strata1.compute_bounding_stratum((-3, 0)) == "0,1"
    assert strata1.compute_bounding_stratum((-2, -3)) == "0,0"
    assert strata1.compute_bounding_stratum((-2, -2)) == "0,0"
    assert strata1.compute_bounding_stratum((-2, -1)) == "0,1"
    assert strata1.compute_bounding_stratum((-2, 0)) == "0,1"
    assert strata1.compute_bounding_stratum((-1, -3)) == "0,0"
    assert strata1.compute_bounding_stratum((-1, -2)) == "0,0"
    assert strata1.compute_bounding_stratum((-1, -1)) == "0,1"
    assert strata1.compute_bounding_stratum((-1, 0)) == "0,1"
    assert strata1.compute_bounding_stratum((0, -3)) == "0,0"
    assert strata1.compute_bounding_stratum((0, -2)) == "0,0"
    assert strata1.compute_bounding_stratum((0, -1)) == "0,1"
    assert strata1.compute_bounding_stratum((0, 0)) == "0,1"
    assert strata1.compute_bounding_stratum((min(limits) - 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, min(limits) - 1)) is None
    assert strata1.compute_bounding_stratum((max(limits) + 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, max(limits) + 1)) is None


def test_strata_2by1_limits_4by4() -> None:
    limits = (0, 3)
    num_x = 2
    num_y = 1
    strata1 = get_strata(limits, limits, num_x, num_y)
    assert strata1.x_limits == limits
    assert strata1.y_limits == limits
    assert strata1.num_strata_x == num_x
    assert strata1.num_strata_y == num_y
    assert strata1.compute_bounding_stratum((0, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((0, 1)) == "0,0"
    assert strata1.compute_bounding_stratum((1, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((1, 1)) == "0,0"
    assert strata1.compute_bounding_stratum((2, 0)) == "1,0"
    assert strata1.compute_bounding_stratum((2, 1)) == "1,0"
    assert strata1.compute_bounding_stratum((3, 0)) == "1,0"
    assert strata1.compute_bounding_stratum((3, 1)) == "1,0"
    assert strata1.compute_bounding_stratum((0, 2)) == "0,0"
    assert strata1.compute_bounding_stratum((0, 3)) == "0,0"
    assert strata1.compute_bounding_stratum((1, 2)) == "0,0"
    assert strata1.compute_bounding_stratum((1, 3)) == "0,0"
    assert strata1.compute_bounding_stratum((2, 2)) == "1,0"
    assert strata1.compute_bounding_stratum((2, 3)) == "1,0"
    assert strata1.compute_bounding_stratum((3, 2)) == "1,0"
    assert strata1.compute_bounding_stratum((3, 3)) == "1,0"
    assert strata1.compute_bounding_stratum((min(limits) - 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, min(limits) - 1)) is None
    assert strata1.compute_bounding_stratum((max(limits) + 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, max(limits) + 1)) is None


def test_strata_2by1_limits_4by4_negative() -> None:
    limits = (-3, 0)
    num_x = 2
    num_y = 1
    strata1 = get_strata(limits, limits, num_x, num_y)
    assert strata1.x_limits == limits
    assert strata1.y_limits == limits
    assert strata1.num_strata_x == num_x
    assert strata1.num_strata_y == num_y
    assert strata1.compute_bounding_stratum((-3, -3)) == "0,0"
    assert strata1.compute_bounding_stratum((-3, -2)) == "0,0"
    assert strata1.compute_bounding_stratum((-3, -1)) == "0,0"
    assert strata1.compute_bounding_stratum((-3, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((-2, -3)) == "0,0"
    assert strata1.compute_bounding_stratum((-2, -2)) == "0,0"
    assert strata1.compute_bounding_stratum((-2, -1)) == "0,0"
    assert strata1.compute_bounding_stratum((-2, 0)) == "0,0"
    assert strata1.compute_bounding_stratum((-1, -3)) == "1,0"
    assert strata1.compute_bounding_stratum((-1, -2)) == "1,0"
    assert strata1.compute_bounding_stratum((-1, -1)) == "1,0"
    assert strata1.compute_bounding_stratum((-1, 0)) == "1,0"
    assert strata1.compute_bounding_stratum((0, -3)) == "1,0"
    assert strata1.compute_bounding_stratum((0, -2)) == "1,0"
    assert strata1.compute_bounding_stratum((0, -1)) == "1,0"
    assert strata1.compute_bounding_stratum((0, 0)) == "1,0"
    assert strata1.compute_bounding_stratum((min(limits) - 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, min(limits) - 1)) is None
    assert strata1.compute_bounding_stratum((max(limits) + 1, 0)) is None
    assert strata1.compute_bounding_stratum((0, max(limits) + 1)) is None

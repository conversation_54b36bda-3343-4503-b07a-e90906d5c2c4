from typing import List, Tuple

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)

Point = Tuple[float, float]
ServosPosition = Tuple[int, int]
DeltaPoint = Tuple[float, float]


class PredictToServosSample:
    def __init__(self, predict_index: int, pcoords: Point, servos: ServosPosition, errors: DeltaPoint):
        self.predict_index = predict_index
        self.pcoords = pcoords
        self.servos = servos
        self.error = errors

    def tuples(self) -> Tuple[int, Point, ServosPosition, DeltaPoint]:
        return self.predict_index, self.pcoords, self.servos, self.error


class DeltaTargetToDeltaServosSample:
    def __init__(
        self,
        delta_tcoords: DeltaPoint,
        delta_servos: DeltaPoint,
        frame_index_pair: Tuple[int, int],
        servos: ServosPosition,
    ):
        self.delta_tcoords = delta_tcoords
        self.delta_servos = delta_servos
        self.frame_index_pair = frame_index_pair
        self.servos = servos

    def tuples(self) -> <PERSON>ple[DeltaPoint, DeltaPoint, Tuple[int, int], ServosPosition]:
        return self.delta_tcoords, self.delta_servos, self.frame_index_pair, self.servos

    def flat_tuple(self) -> Tuple[float, float, float, float, int, int, int, int]:
        return (
            self.delta_tcoords[0],
            self.delta_tcoords[1],
            self.delta_servos[0],
            self.delta_servos[1],
            self.frame_index_pair[0],
            self.frame_index_pair[1],
            self.servos[0],
            self.servos[1],
        )


class CalibrationSamples:
    def __init__(self, pre_calibrated: bool = False) -> None:
        self.predict_to_servos: List[PredictToServosSample] = []
        self.delta_target_to_delta_servos: List[DeltaTargetToDeltaServosSample] = []
        self.pre_calibrated = pre_calibrated

    def lens(self) -> Tuple[int, int]:
        """Return the lengths of the underling lists as a tuple"""
        return len(self.predict_to_servos), len(self.delta_target_to_delta_servos)

    def is_calibrated(self) -> bool:
        return self.pre_calibrated or (len(self.predict_to_servos) > 0 and len(self.delta_target_to_delta_servos) > 0)

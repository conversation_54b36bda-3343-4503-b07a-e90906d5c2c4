import random
from typing import Any, Gene<PERSON>, Optional, <PERSON><PERSON>

import numpy as np

import lib.common.logging
from core.model.path import ServoInstance
from lib.common.math import floordiv_tuples, subtract_tuples

LOG = lib.common.logging.get_logger(__name__)


BivariatePoint = Tuple[float, float]


class BivariateStrata:
    """
    A strata across two variables. Provides behavior for generating stratum bounds.
    Does not provide behavior for generating sample coordinates within a stratum.

    https://en.wikipedia.org/wiki/Stratified_sampling
    """

    def __init__(
        self,
        x_name: str,
        y_name: str,
        x_limits: Tuple[float, float],
        y_limits: Tuple[float, float],
        num_strata_x: int,
        num_strata_y: int,
    ):
        self.x_name = x_name  # the name of the x variable (e.g. px, tx, pan)
        self.y_name = y_name  # the name of the y variable (e.g. py, ty, tilt)
        assert len(x_limits) == 2
        self.x_limits = x_limits
        assert len(y_limits) == 2
        self.y_limits = y_limits
        self.num_strata_x = num_strata_x
        self.num_strata_y = num_strata_y

        # derived values
        min_x, max_x = self.x_limits
        x_min_max_delta = max_x + 1 - min_x
        self.x_step_size = x_min_max_delta / self.num_strata_x
        assert self.x_step_size > 0, "Unexpected x_step_size = {} based on min/max = {}".format(
            self.x_step_size, self.x_limits
        )

        min_y, max_y = self.y_limits
        y_min_max_delta = max_y + 1 - min_y
        self.y_step_size = y_min_max_delta / self.num_strata_y
        assert self.y_step_size > 0, "Unexpected y_step_size = {} based on min/max = {}".format(
            self.y_step_size, self.y_limits
        )

    def __str__(self) -> str:
        return "%dx%d:[%s,%s]" % (self.num_strata_x, self.num_strata_y, list(self.x_limits), list(self.y_limits))

    def __eq__(self, obj: Any) -> bool:
        return (
            isinstance(obj, BivariateStrata)
            and self.x_name == obj.x_name
            and self.y_name == obj.y_name
            and self.x_limits == obj.x_limits
            and self.y_limits == obj.y_limits
            and self.num_strata_x == obj.num_strata_x
            and self.num_strata_y == obj.num_strata_y
        )

    def __ne__(self, obj: Any) -> bool:
        return not self == obj

    @staticmethod
    def from_servo_limits(
        servo_min: Tuple[int, int], servo_max: Tuple[int, int], num_strata_pan: int, num_strata_tilt: int
    ) -> "BivariateStrata":
        # initialize strata definition for sample generation
        pan_limits = servo_min[0], servo_max[0]
        tilt_limits = servo_min[1], servo_max[1]
        return BivariateStrata(
            ServoInstance.PAN, ServoInstance.TILT, pan_limits, tilt_limits, num_strata_pan, num_strata_tilt
        )

    @staticmethod
    def get_stratum_id(x_id: float, y_id: float) -> str:
        return "%d,%d" % (x_id, y_id)

    def compute_bounding_stratum(self, point: Tuple[float, float], nearest: bool = False) -> Optional[str]:
        """
        Return the strata id for this point, or None if outside the limits
        """
        if not (self.x_limits[0] <= point[0] <= self.x_limits[1]):
            # x outside region
            if nearest:
                point = min(self.x_limits[1], max(self.x_limits[0], point[0])), point[1]
            else:
                return None
        if not (self.y_limits[0] <= point[1] <= self.y_limits[1]):
            # y outside region
            if nearest:
                point = point[0], min(self.y_limits[1], max(self.y_limits[0], point[1]))
            else:
                return None

        return BivariateStrata.get_stratum_id(
            *floordiv_tuples(
                # shift to be 0-aligned at min value. 0 <= val <= max
                subtract_tuples(point, (self.x_limits[0], self.y_limits[0])),
                # floordiv through by the size of a stratum interval to get 0-based stratum index
                (self.x_step_size, self.y_step_size),
            )
        )

    def compute_stratum_bounds(self, x_strata_num: int, y_strata_num: int) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """
        Compute the x, y bounds for the given strata id
        """
        min_strata_x = self.x_limits[0] + x_strata_num * self.x_step_size
        max_strata_x = min_strata_x + self.x_step_size

        min_strata_y = self.y_limits[0] + y_strata_num * self.y_step_size
        max_strata_y = min_strata_y + self.y_step_size

        # strata is defined by (min_strata_x, min_strata_y) to (max_strata_x, max_strata_y)
        stratum_bounds_x = int(min_strata_x), int(max_strata_x)
        stratum_bounds_y = int(min_strata_y), int(max_strata_y)

        return stratum_bounds_x, stratum_bounds_y

    def generate_strata_bounds(
        self, snake_iteration: bool = True
    ) -> Generator[Tuple[str, Tuple[int, int], Tuple[int, int]], None, None]:
        """
        Generator function for the set of strata bounds for all stratum defined by this instance.

        If we use this a lot, we can cache this during __init__.
        """

        iterate_y_low_to_high = True
        for x_strata_num in range(0, self.num_strata_x):

            # To minimize servo movement, we want to go low to high, and then high to low, back and forth
            y_generator = range(0, self.num_strata_y)
            if snake_iteration and not iterate_y_low_to_high:
                y_generator = reversed(y_generator)  # type: ignore
            if snake_iteration:
                iterate_y_low_to_high = not iterate_y_low_to_high

            for y_strata_num in y_generator:
                strata_id = BivariateStrata.get_stratum_id(x_strata_num, y_strata_num)
                stratum_bounds_x, stratum_bounds_y = self.compute_stratum_bounds(x_strata_num, y_strata_num)

                yield strata_id, stratum_bounds_x, stratum_bounds_y


class BivariateStratifiedSampler:
    """Generates (x, y) sample coordinates based on a strata definition"""

    def __init__(self, strata: BivariateStrata):
        self.strata: BivariateStrata = strata

    @staticmethod
    def _generate_randint_within_strata(
        x_limits: Tuple[int, int], y_limits: Tuple[int, int], samples_per_strata: int
    ) -> Generator[Tuple[int, int], None, None]:
        """
        Generate n=samples_per_strata within the given strata bounds
        """
        assert len(x_limits) == 2
        assert len(y_limits) == 2
        for _ in range(samples_per_strata):
            x: int = random.randint(*x_limits)
            y: int = random.randint(*y_limits)
            yield x, y

    def _generate_uniform_samples(self) -> Generator[Tuple[int, int], None, None]:
        """
        Generate uniformly spaced samples.
        """
        assert len(self.strata.x_limits) == 2
        assert self.strata.num_strata_x >= 10  # need a reasonable number
        x_values = np.linspace(self.strata.x_limits[0], self.strata.x_limits[1], self.strata.num_strata_x)

        assert len(self.strata.y_limits) == 2
        assert self.strata.num_strata_y >= 10  # need a reasonable number
        y_values = np.linspace(self.strata.y_limits[0], self.strata.y_limits[1], self.strata.num_strata_y)

        iterate_y_low_to_high = True
        for x in x_values:
            # To minimize servo movement, we want to go low to high, and then high to low, back and forth
            y_generator = list(y_values)
            if not iterate_y_low_to_high:
                y_generator = list(reversed(y_generator))
            iterate_y_low_to_high = not iterate_y_low_to_high

            for y in y_generator:
                yield int(x), int(y)

    def generate_samples(self, samples_per_strata: int) -> Generator[Tuple[int, int], None, None]:
        """
        Generator function for generating (x, y) pairs to sample
        """
        assert samples_per_strata > 0 and samples_per_strata == int(samples_per_strata)  # should be a positive integer

        if samples_per_strata == 1:
            for servos in self._generate_uniform_samples():
                yield servos
            return

        # Add Axis points for Bi-Linear unit movement
        center_x = int((self.strata.x_limits[1] - self.strata.x_limits[0]) / 2 + self.strata.x_limits[0])
        center_y = int((self.strata.y_limits[1] - self.strata.y_limits[0]) / 2 + self.strata.y_limits[0])
        move_x = int((self.strata.x_limits[1] - self.strata.x_limits[0]) / 10)
        move_y = int((self.strata.y_limits[1] - self.strata.y_limits[0]) / 10)
        yield center_x, center_y
        yield center_x + move_x, center_y
        yield center_x, center_y + move_y

        for _, stratum_bounds_x, stratum_bounds_y in self.strata.generate_strata_bounds():
            for x, y in BivariateStratifiedSampler._generate_randint_within_strata(
                stratum_bounds_x, stratum_bounds_y, samples_per_strata
            ):
                yield x, y

import os
import shutil
import sys
from argparse import Namespace
from typing import Any, Dict, List

from core.fs.defaults import (
    MEDIA_SUBDIR_ARCHIVE,
    MEDIA_SUBDIR_CALIBRATION,
    MEDIA_SUBDIR_DRIVE_DATA,
    MEDIA_SUBDIR_EXTERMINATION,
    MEDIA_SUBDIR_HUBBLE,
    MEDIA_SUBDIR_PREDICT_DATA,
    MEDIA_SUBDIR_PREDICT_PANO,
    MEDIA_SUBDIR_STREAM,
    MEDIA_SUBDIR_TARGET_DATA,
    MEDIA_SUBDIR_UI_CAPTURE,
)

TMP_DIR = "/tmp"


class FileSystem:
    """
    Robot Filesystem. Knows where things go and what they are called.

    Future:
      * Resource management
      * Nicer namespacing / subdirectory management
    """

    def __init__(
        self,
        calibration_dir: str,
        config_dir: str,
        data_dir: str,
        media_dir: str,
        log_dir: str,
        i_dont_care: bool = False,
    ):
        if i_dont_care:
            return
        # TODO express FileSubSystem type
        # absolute paths
        self._abs_calibration_dir: str = calibration_dir
        self._abs_config_dir: str = config_dir
        self._abs_data_dir: str = data_dir
        self._abs_media_dir: str = media_dir
        self._abs_log_dir: str = log_dir
        assert os.path.exists(self._abs_config_dir), f"Missing config directory: {self._abs_config_dir}"
        assert os.path.exists(self._abs_media_dir), f"Missing media directory: {self._abs_media_dir}"

        # create if not exists
        if not os.path.exists(self._abs_log_dir):
            # Can't log what we're doing...because might not have a log dir yet!
            os.makedirs(self._abs_log_dir)

        # relative paths
        self._rel_media_subdir_stream: str = MEDIA_SUBDIR_STREAM
        self._rel_media_subdir_archive: str = MEDIA_SUBDIR_ARCHIVE
        self._rel_media_subdir_calibration: str = MEDIA_SUBDIR_CALIBRATION
        self._rel_media_subdir_extermination: str = MEDIA_SUBDIR_EXTERMINATION
        self._rel_media_subdir_hubble: str = MEDIA_SUBDIR_HUBBLE
        self._rel_media_subdir_predict_pano: str = MEDIA_SUBDIR_PREDICT_PANO
        self._rel_media_subdir_ui_capture: str = MEDIA_SUBDIR_UI_CAPTURE
        self._rel_media_subdir_predict_data: str = MEDIA_SUBDIR_PREDICT_DATA
        self._rel_media_subdir_target_data: str = MEDIA_SUBDIR_TARGET_DATA
        self._rel_media_subdir_drive_data: str = MEDIA_SUBDIR_DRIVE_DATA
        # TODO this can be much better. Should not need hard-coding. Can we have a spot where we register subdirs?
        # How can this scale?
        self._rel_media_subdirs: List[str] = [
            self._rel_media_subdir_archive,
            self._rel_media_subdir_calibration,
            self._rel_media_subdir_extermination,
            self._rel_media_subdir_predict_pano,
            self._rel_media_subdir_ui_capture,
            self._rel_media_subdir_predict_data,
            self._rel_media_subdir_target_data,
            self._rel_media_subdir_drive_data,
        ]

        # Stream memfs setup
        if sys.platform == "linux":
            if os.path.islink(os.path.join(self._abs_media_dir, self._rel_media_subdir_stream)):
                os.remove(os.path.join(self._abs_media_dir, self._rel_media_subdir_stream))
            elif os.path.isdir(os.path.join(self._abs_media_dir, self._rel_media_subdir_stream)):
                shutil.rmtree(os.path.join(self._abs_media_dir, self._rel_media_subdir_stream))
            if os.path.exists(os.path.join("/dev/shm", self._rel_media_subdir_stream)):
                shutil.rmtree(os.path.join("/dev/shm", self._rel_media_subdir_stream))
            os.makedirs(os.path.join("/dev/shm", self._rel_media_subdir_stream))
            os.symlink(
                os.path.join("/dev/shm", self._rel_media_subdir_stream),
                os.path.join(self._abs_media_dir, self._rel_media_subdir_stream),
            )
        else:
            os.makedirs(os.path.join(self._abs_media_dir, self._rel_media_subdir_stream), exist_ok=True)

        # TODO validate permissions are writable where needed

    @staticmethod
    def from_args(args: Namespace) -> "FileSystem":
        if args.fs_force_tmp_dir is not None:
            return TemporaryFileSystem(tmp_dir="/tmp")
        return FileSystem(
            calibration_dir=args.fs_calibration_dir,
            config_dir=args.fs_config_dir,
            data_dir=args.fs_data_dir,
            media_dir=args.fs_media_dir,
            log_dir=args.fs_log_dir,
        )

    def to_json(self) -> Dict[str, Dict[str, Any]]:
        # TODO express FileSubSystem type and call into its to_json()
        return {
            "calibration": {"root": self._abs_calibration_dir, "subdirs": []},
            "config": {"root": self._abs_config_dir, "subdirs": []},
            "data": {"root": self._abs_data_dir, "subdirs": []},
            "media": {"root": self._abs_media_dir, "subdirs": self._rel_media_subdirs},
            "logs": {"root": self._abs_log_dir, "subdirs": []},
        }

    @property
    def abs_config_dir(self) -> str:
        return self._abs_config_dir

    @property
    def abs_calibration_dir(self) -> str:
        return self._abs_calibration_dir

    @property
    def abs_data_dir(self) -> str:
        return self._abs_data_dir

    @property
    def abs_media_dir(self) -> str:
        return self._abs_media_dir

    @property
    def abs_media_stream_dir(self) -> str:
        return os.path.join(self._abs_media_dir, self._rel_media_subdir_stream)

    @property
    def abs_log_dir(self) -> str:
        return self._abs_log_dir

    @property
    def rel_media_subdir_archive(self) -> str:
        return self._rel_media_subdir_archive

    @property
    def abs_media_subdir_archive(self) -> str:
        return os.path.join(self._abs_media_dir, self._rel_media_subdir_archive)

    @property
    def rel_media_subdir_calibration(self) -> str:
        return self._rel_media_subdir_calibration

    @property
    def abs_media_subdir_calibration(self) -> str:
        return os.path.join(self._abs_media_dir, self._rel_media_subdir_calibration)

    @property
    def rel_media_subdir_extermination(self) -> str:
        return self._rel_media_subdir_extermination

    @property
    def abs_media_subdir_extermination(self) -> str:
        return os.path.join(self._abs_media_dir, self._rel_media_subdir_extermination)

    @property
    def abs_media_subdir_hubble(self) -> str:
        return os.path.join(self._abs_media_dir, self._rel_media_subdir_hubble)

    @property
    def rel_media_subdir_predict_pano(self) -> str:
        return self.rel_media_subdir_predict_pano

    @property
    def abs_media_subdir_predict_pano(self) -> str:
        return os.path.join(self._abs_media_dir, self.rel_media_subdir_predict_pano)

    @property
    def rel_media_subdir_ui_capture(self) -> str:
        return self._rel_media_subdir_ui_capture

    @property
    def abs_media_subdir_ui_capture(self) -> str:
        return os.path.join(self._abs_media_dir, self._rel_media_subdir_ui_capture)

    @property
    def rel_media_subdir_predict_data(self) -> str:
        return self._rel_media_subdir_predict_data

    @property
    def abs_media_subdir_predict_data(self) -> str:
        return os.path.join(self._abs_media_dir, self._rel_media_subdir_predict_data)

    @property
    def rel_media_subdir_target_data(self) -> str:
        return self._rel_media_subdir_target_data

    @property
    def abs_media_subdir_target_data(self) -> str:
        return os.path.join(self._abs_media_dir, self._rel_media_subdir_target_data)

    @property
    def rel_media_subdir_drive_data(self) -> str:
        return self._rel_media_subdir_drive_data

    @property
    def abs_media_subdir_drive_data(self) -> str:
        return os.path.join(self._abs_media_dir, self._rel_media_subdir_drive_data)

    @property
    def rel_media_subdirs(self) -> List[str]:
        return self._rel_media_subdirs


class TemporaryFileSystem(FileSystem):
    def __init__(self, tmp_dir: str = "/tmp"):
        assert tmp_dir.startswith("/"), f"Must be absolute path. Got: {tmp_dir}"
        super().__init__(
            calibration_dir=tmp_dir, config_dir=tmp_dir, data_dir=tmp_dir, log_dir=tmp_dir, media_dir=tmp_dir,
        )

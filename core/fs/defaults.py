import os

# Directories
from core.fs.envvars import MAKA_CALIBRATION_DIR, MAKA_CONFIG_DIR, MAKA_DATA_DIR, MAKA_LOG_DIR

#
# Config
#
CONFIG_DIR = os.environ.get(MAKA_CONFIG_DIR, os.path.expanduser("~/maka/config"))
CALIBRATION_DIR = os.environ.get(MAKA_CALIBRATION_DIR, "~/maka/calibration")

#
# Data
#
DATA_DIR = os.environ.get(MAKA_DATA_DIR, os.path.expanduser("~/maka/data"))

#
# Logs
#
LOG_DIR = os.environ.get(MAKA_LOG_DIR, os.path.join(DATA_DIR, "logs"))

#
# Media
#
# overridable with -i from commandline
MEDIA_DIR = os.path.join(DATA_DIR, "media")
# cannot be statically declared since command-line can override base dir.
MEDIA_SUBDIR_STREAM = "stream"
MEDIA_SUBDIR_ARCHIVE = "archive"
MEDIA_SUBDIR_CALIBRATION = "calibration"
MEDIA_SUBDIR_EXTERMINATION = "extermination"
MEDIA_SUBDIR_HUBBLE = "hubble"
MEDIA_SUBDIR_PREDICT_PANO = "predict_pano"
MEDIA_SUBDIR_UI_CAPTURE = "ui-capture"
MEDIA_SUBDIR_PREDICT_DATA = "deep_predicts"
MEDIA_SUBDIR_TARGET_DATA = "deep_targets"
MEDIA_SUBDIR_DRIVE_DATA = "drive_exterminate"

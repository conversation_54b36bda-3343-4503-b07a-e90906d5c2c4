from uuid import uuid4


def camera_filename(*, camera_id: str, filename: str, annotated: bool) -> str:
    prefix = camera_id
    if not annotated:
        prefix = prefix + "_unannotated"

    filename = (
        f"{prefix}{'' if len(filename) > 0 and filename[0] == '.' else '' if len(filename) == 0 else '_'}{filename}"
    )

    return filename


def tmp_filename(base_filename: str) -> str:
    return f".tmp_{uuid4()}_{base_filename}"

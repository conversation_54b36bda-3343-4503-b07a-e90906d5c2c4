import asyncio
import logging
from typing import Any, Optional

from core.controls.frame.pose.heading.base import HeadingEstimator
from core.controls.frame.sensor.geolocation.node import GPSNode
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from generated.core.controls.frame.model.heading_message import HeadingMessage
from lib.common.protocol.channel.base import SubscriberChannel, Topic
from lib.common.protocol.channel.callback import CallbackSubscriberChannel
from lib.drivers.nanopb.benjamin_gps_board.benjamin_gps_board_connector import (
    BenjaminGPSBoardConnector,
    Heading,
    Position,
)
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

BENJAMIN_GPS_PORT = 4244


class BenjaminGPSHeadingEstimator(HeadingEstimator):
    def __init__(self, topic: Topic, channel: SubscriberChannel[HeadingMessage]):
        super().__init__(topic)
        self._channel = channel

    def get_heading_subscription(self) -> SubscriberChannel[HeadingMessage]:
        return self._channel

    async def tick(self) -> None:
        pass


class BenjaminGPSNode(GPSNode):
    def __init__(self, *argv: Any, ip: str, heading_offset: float = 0, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        self._ip = ip
        self._heading_offset = heading_offset
        self._connector: Optional[PsocMEthernetConnector] = None
        self._board: Optional[BenjaminGPSBoardConnector] = None
        self._position: Optional[Position] = None
        self._heading: Optional[Heading] = None
        self._error_count = 0

    async def tick(self) -> None:
        if self._connector is None:
            # Lazy initialization to bind to correct event loop
            connector = PsocMEthernetConnector(self._ip, BENJAMIN_GPS_PORT, asyncio.get_event_loop())
            await connector.open()
            self._board = BenjaminGPSBoardConnector(connector)
            self._connector = connector

        try:
            self._position = await self._board.position()
            self._error_count = 0
        except:  # noqa
            logging.exception("Error requesting GPS position")
            self._error_count += 1
            if self._error_count >= 5:
                self._position = None

        try:
            self._heading = await self._board.heading()
        except:  # noqa
            logging.exception("Error requesting GPS heading")
            self._heading = None

    @property
    def geoposition_lat_lon_alt(self) -> GeopositionLatLonAltMessage:
        position = self._position
        if position is None or not position.have_approx_fix:
            return GeopositionLatLonAltMessage(timestamp_ms=0, lat=0, lon=0, alt=0)
        return GeopositionLatLonAltMessage(
            timestamp_ms=position.timestamp_ms, lat=position.latitude, lon=position.longitude, alt=0
        )

    def _heading_callback(self) -> Optional[HeadingMessage]:
        heading = self._heading

        if heading is None or not heading.have_approx_fix:
            return None

        return HeadingMessage(
            timestamp_ms=self._heading.timestamp_ms, heading=(self._heading.heading_deg + self._heading_offset) % 360
        )

    def get_heading_estimator(self, topic: Topic) -> Optional[HeadingEstimator]:
        channel = CallbackSubscriberChannel(topic=topic.sub("heading"), callback=self._heading_callback,)
        return BenjaminGPSHeadingEstimator(topic, channel)

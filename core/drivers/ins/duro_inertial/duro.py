from typing import Optional, <PERSON><PERSON>

import numpy as np
from sbp.msg import SBP

import lib.common.logging
import lib.common.tasks
from core.controls.frame.sensor.ins.base import MagnetometerINS
from generated.core.controls.frame.model.acceleration_message import AccelerationMessage
from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.mag_message import MagMessage
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.time import maka_control_timestamp_ms
from lib.drivers.swift_nav.configuration import ANGULAR_RANGE_DEGREES_PER_SECOND
from lib.drivers.swift_nav.constants import RADIUS_EARTH_M, SAMPLE_DIMENSIONS
from lib.drivers.swift_nav.message import SBPMessage

LOG = lib.common.logging.get_logger(__name__)

# All samples we get from the Duro are in 3D space, and we require all measurements to be column
# vectors.


class DuroInertialNavigationSystem(MagnetometerINS):
    """
    Transforms a set of SubscriberChannels into a INS interface.
    """

    def __init__(
        self,
        imu_raw_subscription: SubscriberChannel[SBPMessage],
        pos_ecef_subscription: SubscriberChannel[SBPMessage],
        mag_raw_subscription: SubscriberChannel[SBPMessage],
        vel_ecef_subscription: SubscriberChannel[SBPMessage],
    ):
        self._imu_raw_subscription = imu_raw_subscription
        self._pos_ecef_subscription = pos_ecef_subscription
        self._mag_raw_subscription = mag_raw_subscription
        self._vel_ecef_subscription = vel_ecef_subscription

    ###################################################
    # Interface properties
    ###################################################

    @property
    def accel_gyro(self) -> Tuple[int, np.ndarray, np.ndarray]:
        """
        Report the most recent accelerometer and gyroscope readings.

        Returns:
            (timestamp_ms, accel_mps2[x, y, z] (3x1), gyro_dps[x, y, z] (3x1))
            Accelerometer samples are in m/s^2, gyro samples are in degrees/s, timestamp is in milliseconds.
        """
        sbp_msg: Optional[SBPMessage] = self._imu_raw_subscription.read()
        if sbp_msg is None:
            LOG.warning("Received accel_gyro request before accel_gyro in buffer")
            return maka_control_timestamp_ms(), np.zeros(SAMPLE_DIMENSIONS), np.zeros(SAMPLE_DIMENSIONS)
        raw_msg: SBP = sbp_msg.msg

        # The accelerometer is configured for +-2g range on a signed 16bit measurement.
        # 1g = 9.81m/s^2
        accel_scale = 9.81 * 2.0 / 2 ** 15
        accel = np.array([raw_msg.acc_x, raw_msg.acc_y, raw_msg.acc_z]).reshape(SAMPLE_DIMENSIONS) * accel_scale

        # Gyros are configured for +-125 degrees/s range on a signed 16bit measurement.
        gyro_scale = ANGULAR_RANGE_DEGREES_PER_SECOND / 2 ** 15
        gyro = np.array([raw_msg.gyr_x, raw_msg.gyr_y, raw_msg.gyr_z]).reshape(SAMPLE_DIMENSIONS) * gyro_scale

        return sbp_msg.timestamp_ms, accel, gyro

    @property
    def acceleration(self) -> AccelerationMessage:
        accel_gryo: Tuple[int, np.ndarray, np.ndarray] = self.accel_gyro
        return AccelerationMessage(
            timestamp_ms=accel_gryo[0], x=accel_gryo[1][0], y=accel_gryo[1][1], z=accel_gryo[1][2]
        )

    @property
    def angular_velocity(self) -> AngularVelocityMessage:
        accel_gryo: Tuple[int, np.ndarray, np.ndarray] = self.accel_gyro
        return AngularVelocityMessage(
            timestamp_ms=accel_gryo[0], x=accel_gryo[2][0], y=accel_gryo[2][1], z=accel_gryo[2][2]
        )

    @property
    def geoposition_ecef_m(self) -> GeopositionEcefMessage:
        """
        Report the ECEF coordinates of the latest position update.

        Returns:
            (timestamp_ms, ecef_m[x, y, z] (3x1)).
            Positions are in units of meters, timestamp in milliseconds. Euclidean magnitude of x,
            y, z vectors will be close to 6378100m (radius of earth) if solution is valid, or all 0s
            except for timestamp when no valid solution exists.
        """
        sbp_msg: Optional[SBPMessage] = self._pos_ecef_subscription.read()
        if sbp_msg is None:
            LOG.warning("Received geoposition request before geoposition in buffer")
            return GeopositionEcefMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)
        raw_msg: SBP = sbp_msg.msg

        position = [raw_msg.x, raw_msg.y, raw_msg.z]

        # A simple sanity check that the measurements are reasonable. If they aren't, report a
        # position in the middle of the earth that is obviously not a real measurement.
        # The average radius of earth is approximately 6378.1km
        #   Source: https://arxiv.org/pdf/1510.07674.pdf
        magnitude = np.linalg.norm(position)
        if magnitude < RADIUS_EARTH_M * 0.9 or magnitude > RADIUS_EARTH_M * 1.1:
            position = [0, 0, 0]

        return GeopositionEcefMessage(timestamp_ms=sbp_msg.timestamp_ms, x=position[0], y=position[1], z=position[2])

    @property
    def mag(self) -> MagMessage:
        """
        Report the magnetic field strength, in micro-Teslas.

        Returns:
            timestamp_ms, mag_uT[x, y, z] (3x1)
            Magnetic field samples in microTeslas, timestamp in milliseconds.
        """
        sbp_msg: Optional[SBPMessage] = self._mag_raw_subscription.read()
        if sbp_msg is None:
            return MagMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)
        raw_msg: SBP = sbp_msg.msg

        return MagMessage(timestamp_ms=sbp_msg.timestamp_ms, x=raw_msg.mag_x, y=raw_msg.mag_y, z=raw_msg.mag_z)

    @property
    def velocity(self) -> VelocityMessage:
        """
        Report the velocity in mm/s.

        Returns:
            (timestamp_ms, ecef_mmps[x, y, z] (3x1)).
            Velocities are in mm/s, timestamp_ms in milliseconds.
        """
        sbp_msg: Optional[SBPMessage] = self._vel_ecef_subscription.read()
        if sbp_msg is None:
            LOG.warning("Received velocity request before velocity in buffer")
            return VelocityMessage(timestamp_ms=maka_control_timestamp_ms(), x=0, y=0, z=0)
        raw_msg: SBP = sbp_msg.msg

        return VelocityMessage(timestamp_ms=sbp_msg.timestamp_ms, x=raw_msg.x, y=raw_msg.y, z=raw_msg.z)

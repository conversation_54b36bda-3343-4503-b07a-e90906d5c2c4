import json
from json.decoder import <PERSON>SONDecodeE<PERSON><PERSON>
from typing import Any, Callable, Optional

from sbp.imu import MsgImuRaw
from sbp.mag import MsgMagRaw
from sbp.navigation import MsgPosECEF, MsgVelBody, MsgVelECEF

from core.controls.frame.sensor.ins.node import INSNode
from core.drivers.ins.duro_inertial.duro import DuroInertialNavigationSystem
from generated.core.controls.frame.model.acceleration_message import AccelerationMessage
from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.mag_message import MagMessage
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import Topic
from lib.common.protocol.channel.shmem import ShmemSubscriberChannel
from lib.drivers.swift_nav.message import (
    SBPMessageImuRaw,
    SB<PERSON>essageMagRaw,
    SBPMessagePosECEF,
    SBPMessageVelBody,
    SBPMessageVelECEF,
)

LOG = get_logger(__name__)


def race_condition_deserialize_json(s: str, deserialize: Callable[..., Any]) -> Optional[Any]:
    try:
        json_loaded = json.loads(s)
        if json_loaded is not None:
            return deserialize(json_loaded)
    except JSONDecodeError:
        LOG.warning("Race condition due to unlocked shared memory access!")
    return None


class ShmemChannelImuRaw(ShmemSubscriberChannel[SBPMessageImuRaw]):
    def __init__(self, base_topic: Topic):
        super().__init__(
            topic=base_topic.sub(str(MsgImuRaw).split("'")[1].split(".")[-1]),
            deserialize=lambda s: race_condition_deserialize_json(s, SBPMessageImuRaw.from_json),  # type: ignore
            file_suffix=".json",
        )


class ShmemChannelMagRaw(ShmemSubscriberChannel[SBPMessageMagRaw]):
    def __init__(self, base_topic: Topic):
        super().__init__(
            topic=base_topic.sub(str(MsgMagRaw).split("'")[1].split(".")[-1]),
            deserialize=lambda s: race_condition_deserialize_json(s, SBPMessageMagRaw.from_json),  # type: ignore
            file_suffix=".json",
        )


class ShmemChannelPosECEF(ShmemSubscriberChannel[SBPMessagePosECEF]):
    def __init__(self, base_topic: Topic):
        super().__init__(
            topic=base_topic.sub(str(MsgPosECEF).split("'")[1].split(".")[-1]),
            deserialize=lambda s: race_condition_deserialize_json(s, SBPMessagePosECEF.from_json),  # type: ignore
            file_suffix=".json",
        )


class ShmemChannelVelBody(ShmemSubscriberChannel[SBPMessageVelBody]):
    def __init__(self, base_topic: Topic):
        super().__init__(
            topic=base_topic.sub(str(MsgVelBody).split("'")[1].split(".")[-1]),
            deserialize=lambda s: race_condition_deserialize_json(s, SBPMessageVelBody.from_json),  # type: ignore
            file_suffix=".json",
        )


class ShmemChannelVelECEF(ShmemSubscriberChannel[SBPMessageVelECEF]):
    def __init__(self, base_topic: Topic):
        super().__init__(
            topic=base_topic.sub(str(MsgVelECEF).split("'")[1].split(".")[-1]),
            deserialize=lambda s: race_condition_deserialize_json(s, SBPMessageVelECEF.from_json),  # type: ignore
            file_suffix=".json",
        )


class ShmemDuroInertialNavigationSystem(DuroInertialNavigationSystem):
    def __init__(self, base_topic: Topic):
        shmem_imu = ShmemChannelImuRaw(base_topic=base_topic)
        shmem_mag = ShmemChannelMagRaw(base_topic=base_topic)
        shmem_pos = ShmemChannelPosECEF(base_topic=base_topic)
        shmem_vel = ShmemChannelVelECEF(base_topic=base_topic)

        super().__init__(
            imu_raw_subscription=shmem_imu,
            mag_raw_subscription=shmem_mag,
            pos_ecef_subscription=shmem_pos,
            vel_ecef_subscription=shmem_vel,
        )


class ShmemDuroInertialINSNode(INSNode):
    def __init__(self, *argv: Any, shmem_ins: ShmemDuroInertialNavigationSystem, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        self._shmem_ins: ShmemDuroInertialNavigationSystem = shmem_ins

    @property
    def acceleration(self) -> AccelerationMessage:
        return self._shmem_ins.acceleration

    @property
    def geoposition_ecef_m(self) -> GeopositionEcefMessage:
        return self._shmem_ins.geoposition_ecef_m

    @property
    def angular_velocity(self) -> AngularVelocityMessage:
        return self._shmem_ins.angular_velocity

    @property
    def mag(self) -> MagMessage:
        return self._shmem_ins.mag

    @property
    def velocity(self) -> VelocityMessage:
        return self._shmem_ins.velocity

    def serve_forever(self, block: bool, http_port: int) -> None:
        pass

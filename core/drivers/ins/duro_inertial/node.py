import asyncio
import signal
import threading
from typing import Any, Dict

from core.controls.frame.sensor.ins.node import INSNode
from core.drivers.ins.duro_inertial.duro import DuroInertialNavigationSystem
from core.model.sensor import Sensor
from generated.core.controls.frame.model.acceleration_message import AccelerationMessage
from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.mag_message import MagMessage
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.logging import get_logger
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.tasks.scheduler import dispatch
from lib.drivers.swift_nav.driver import MAX_RETRY_TIMEOUT_MS, DuroInertialDriver

LOG = get_logger(__name__)


class DuroInertialINSNode(INSNode):
    """
    A classical Node implementation wrapping a Duro driver.
    """

    def __init__(
        self, *argv: Any, driver: DuroInertialDriver, ins_interface: DuroInertialNavigationSystem, **kwargs: Any
    ):
        super().__init__(*argv, **kwargs)
        self._driver: DuroInertialDriver = driver
        self._ins_interface: DuroInertialNavigationSystem = ins_interface

        self._control_loop_started = threading.Event()

    ###################################################
    # INS APIs
    ###################################################

    @property
    def acceleration(self) -> AccelerationMessage:
        return self._ins_interface.acceleration

    @property
    def angular_velocity(self) -> AngularVelocityMessage:
        return self._ins_interface.angular_velocity

    @property
    def geoposition_ecef_m(self) -> GeopositionEcefMessage:
        return self._ins_interface.geoposition_ecef_m

    @property
    def mag(self) -> MagMessage:
        return self._ins_interface.mag

    @property
    def velocity(self) -> VelocityMessage:
        return self._ins_interface.velocity

    ###################################################
    # Node APIs
    ###################################################

    def status_callback(self) -> Dict[str, Any]:
        return {
            "geoposition_ecef_m": self.geoposition_ecef_m.to_json(),
            "geoposition_lat_lon_alt": self.geoposition_lat_lon_alt.to_json(),
            "mag": self.mag.to_json(),
            "velocity": self.velocity.to_json(),
        }

    def _control_loop(self) -> None:
        self._driver.connect()
        self._driver.write_configuration_settings()
        self._control_loop_started.set()

        # This mechanism is built to keep the Node running when it is in process-separated mode.
        # The serve_forever function will wait on this loop to finish, which will only happen
        #
        # SIGINT/SIGTERM
        #   --> cancel() all maka tasks under our scope
        #   --> dispatch loop ends
        #   --> serve_forever exits
        #
        # For this, we pay one dispatch tick per poll rate.
        #
        # At 10hz I don't think this is a real issue for now. If it is, we'll see it on the profiler.
        async def keep_alive() -> None:
            pass

        LOG.debug("Starting Duro Inertial Keep Alive Thread...")
        asyncio.run_coroutine_threadsafe(
            dispatch(name="Duro Inertial Keep Alive", callback=keep_alive, poll_ms=100), get_event_loop_by_name()
        ).result()
        assert False, "Duro Inertial Keep Alive Should Not Exit"

    def start(self) -> None:
        # asynchronously starts the control loop
        super().start()

        # But we don't want to yield until we've connected and set the settings
        self._control_loop_started.wait(MAX_RETRY_TIMEOUT_MS / 1000)

    def stop(self) -> None:
        """
        Waits until the watchdog task terminates. Under normal operations, the watchdog should never
        terminate - doing so means there was a fatal error that requires the process to restart and
        likely alert a human.
        """
        super(Sensor, self).stop()
        self._driver.stop()

    def _signal_exit_safe(self, signum: int, _: Any) -> None:
        LOG.info(f"{signal.Signals(signum).name}!")
        self.stop()

    def serve_forever(self, block: bool, http_port: int) -> None:
        # Only botd invokes this for the Duro, so that's when we must attach listeners to close
        #   - Not the most elegant, but suffices for now. Rather focus elsewhere
        signal.signal(signal.SIGINT, self._signal_exit_safe)
        signal.signal(signal.SIGTERM, self._signal_exit_safe)
        if hasattr(signal, "SIGQUIT"):  # Doesn't exist on windows
            signal.signal(signal.SIGQUIT, self._signal_exit_safe)

        assert block, "Must block!"
        assert self.task is not None
        self.task.wait_until_complete()

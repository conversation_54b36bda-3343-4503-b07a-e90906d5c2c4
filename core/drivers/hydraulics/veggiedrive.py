from typing import Any, Dict, List, <PERSON><PERSON>

import grpc

from core.controls.driver.drivebot.client.client import DrivebotClientSynchronous
from core.controls.driver.hydraulics.hydraulics import Hydraulics, UnreachableHydraulicRotaryAngleException
from core.model.path import Instance, RelativeInstance
from lib.common.logging import get_logger
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder

LOG = get_logger(__name__)


class Veggiedrive(Hydraulics):
    def __init__(
        self,
        *argv: Any,
        rotary_limits_frame_angles_deg: Dict[str, Tuple[float, float]],  # Actually a list of length 2
        can_device: str,
        min_drive_current_mA: int,
        max_drive_current_mA: int,
        **kwargs: Any,
    ):
        """
        Constructor.

        Parameters:
            rotary_limits_frame_angles_deg (dict): A dictionary of reachable limits of the hydraulic
                                                   rotaries.
            can_device (str): The CAN interface that's enumerated on the system that should be used
                              to communicate with the Veggie drive controller.
            max_drive_current_mA (int): The maximum amount of drive current to send to the drive
                                        system wheels. This is used to scale the wheel values into
                                        torque request currents. This value will correspond with
                                        wheel values of 1.
        """
        super().__init__(*argv, **kwargs)
        import logging

        get_logger("can.interfaces.socketcan.socketcan").setLevel(logging.INFO)

        # The controller can output a maximum of 4 Amps to the drive system. We likely want a
        # tighter bound than this - experimentally 1.8A is enough to fully open the solenoid valve,
        # corresponding to full speed.
        assert 0 <= min_drive_current_mA <= 4000
        assert 0 <= max_drive_current_mA <= 4000
        assert min_drive_current_mA <= max_drive_current_mA
        self._min_drive_current_mA = min_drive_current_mA
        self._max_drive_current_mA = max_drive_current_mA

        # Assume that the left rotary should contain the name 'left' somewhere in it, and there
        # should only be one such rotary. Likewise for the right.
        left_rotary_names = [x for x in rotary_limits_frame_angles_deg if "left" in x.lower()]
        assert len(left_rotary_names) == 1
        self._left_rotary_name: str = next(iter(left_rotary_names))

        right_rotary_names = [x for x in rotary_limits_frame_angles_deg if "right" in x.lower()]
        assert len(right_rotary_names) == 1
        self._right_rotary_name: str = next(iter(right_rotary_names))

        self._rotary_limits_frame_angle_deg = rotary_limits_frame_angles_deg

        # Create some initial state variables that will be used for reporting back what the
        # setpoints are.
        self._speed_mph: float = 0

        self._rotaries: Dict[str, float] = {self._left_rotary_name: 0, self._right_rotary_name: 0}
        self._name_to_direction_map: Dict[str, Instance] = {
            self._left_rotary_name: RelativeInstance.LEFT,
            self._right_rotary_name: RelativeInstance.RIGHT,
        }

        # TODO(evanbro) We should make the drive loop async to avoid the need for this.
        self._drivebot = DrivebotClientSynchronous()

    @property
    def pc_control(self) -> bool:
        # TODO(asergeev): query drivebot. OK for now because drivebot will deadman-fail to accept
        # commands if PC is in control.
        return True

    @property
    def forward(self) -> float:
        return self._speed_mph

    @property
    def rotaries(self) -> List[str]:
        """
        Get a list of all the rotaries' names.

        Returns:
            A list of names of rotaries that can be used in commands like `set_rotary_position` to
            name specific wheels.
        """
        return list(self._rotaries.keys())

    @property
    def rotary_position_frame_angles_deg(self) -> Dict[str, float]:
        """
        Get the current wheels' angles off from center of the frame of the robot.

        Returns:
            Dict(str, float) of wheel name to current value (values are in range of -90 to +90)
        """
        with duration_perf_recorder(PerfCategory.OPERATIONS, "VeggieDriveRotaryAngles"):
            try:
                response = self._drivebot.get_status()
                self._rotaries = {
                    self._left_rotary_name: response.actual_left_wheel_angle_deg,
                    self._right_rotary_name: response.actual_right_wheel_angle_deg,
                }
            except grpc.RpcError as e:
                LOG.exception(e)
        return self._rotaries

    @property
    def rotary_limits_frame_angles_deg(self) -> Dict[str, Tuple[float, float]]:
        """
        Get the limits of motion for each wheel in the body frame.

        Returns:
            A dictionary of the reachable limits of each hydraulic rotary.
        """
        return self._rotary_limits_frame_angle_deg

    def drive(self, speed_mph: float) -> None:
        with duration_perf_recorder(PerfCategory.OPERATIONS, "VeggieDriveDrive"):
            try:
                self._drivebot.drive(caller_id="veggiedrive", velocity_mph=speed_mph)
            except grpc.RpcError as e:
                LOG.exception(e)
        self._speed_mph = speed_mph
        LOG.debug(f"{self.device_path} speed_mph = {self._speed_mph:.2f}")

    def set_rotary_position(self, *, rotary: str, frame_angle_deg: float, log: bool = False) -> None:
        """
        Set the named hydraulic rotary to the given angle in the vehicle frame - such that
        frame_angle_deg == 0 corresponds to aligned with the vehicle frame's y-axis. Positive values
        are CCW rotations from straight-ahead, negative are CW.

        Parameters:
            rotary (str): The name of the rotary being commanded.
            frame_angle_deg (float): The angle to set the rotary to, in degrees from 0. Rotations
                                     are in the Cartesian convention of positive == CCW.
            log (bool): Whether to log this command. Defaults to False.
        """

        # check against limits
        assert rotary in self._rotary_limits_frame_angle_deg
        limits: Tuple[float, float] = self._rotary_limits_frame_angle_deg[rotary]
        if not limits[0] <= frame_angle_deg <= limits[1]:
            raise UnreachableHydraulicRotaryAngleException(
                f"Rotary {rotary} cannot achieve requested frame angle {frame_angle_deg:.2f} due to limits {limits}"
            )

        LOG.debug(f"{self.device_path} {rotary} = {frame_angle_deg: .2f}deg")

        try:
            if self._name_to_direction_map[rotary] == RelativeInstance.LEFT:
                with duration_perf_recorder(PerfCategory.OPERATIONS, "VeggieDriveSteerLeft"):
                    self._drivebot.drive(caller_id="veggiedrive", left_wheel_angle_deg=frame_angle_deg)
            else:
                with duration_perf_recorder(PerfCategory.OPERATIONS, "VeggieDriveSteerRight"):
                    self._drivebot.drive(caller_id="veggiedrive", right_wheel_angle_deg=frame_angle_deg)
        except grpc.RpcError as e:
            LOG.exception(e)

        self._rotaries[rotary] = frame_angle_deg

    def notify_positive_control(self) -> None:
        try:
            with duration_perf_recorder(PerfCategory.OPERATIONS, "VeggieDriveNotifyControl"):
                self._drivebot.drive(caller_id="veggiedrive")
        except grpc.RpcError as e:
            LOG.exception(e)

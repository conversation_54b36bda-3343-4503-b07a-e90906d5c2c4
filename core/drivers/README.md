# Robot Drivers
This directory is for Opinionated wrappers around device libraries that fit the robot model abstraction onto a device library.

As a first attempt, this directory is split by device type, e.g., Camera vs Laser, etc. This is chosen over a, say, vendor-based split because we care about WHAT the driver is for, not HOW it is implemented, in Robot model world. We will see how this directory structure holds up.

from typing import Any, Dict, Optional, <PERSON><PERSON>

import cv2
import numpy as np
import urllib.request

from core.cv.retina.camera.node import Cam
from lib.common.logging import get_logger
from lib.common.tasks import MakaTask, get_current
from lib.common.time import maka_control_timestamp_ms
from lib.drivers.axis.properties import AxisCamProperties

LOG = get_logger(__name__)


class AxisEthernetCam(Cam):
    IMAGE_JPG_URL = "axis-cgi/jpg/image.cgi"  # 1/10th size and quality, 10x speed
    IMAGE_BMP_URL = "axis-cgi/bitmap/image.cgi"  # 10x size and quality, 1/10th speed

    def __init__(self, *args: Any, ip: str, resolution: Optional[Dict[str, int]] = None, **kwargs: Any):
        super().__init__(*args, **kwargs)
        self._properties = AxisCamProperties()
        self._width = (resolution or {}).get("width")
        self._height = (resolution or {}).get("height")
        assert (self._width is None) == (self._height is None)
        self._ip = ip
        LOG.info("axis cam with ip: {}".format(ip))

    @property
    def resolution(self) -> Optional[Tuple[int, int]]:
        if self._width is None or self._height is None:
            return None
        assert self._width is not None
        assert self._height is not None
        return self._width, self._height

    def _control_loop(self) -> None:
        task: Optional[MakaTask] = get_current()
        assert task is not None
        while True:
            task.tick()
            try:
                # Grab Image
                if self._width is None and self._height is None:
                    url = f"http://{self._ip}/{AxisEthernetCam.IMAGE_JPG_URL}"
                else:
                    url = f"http://{self._ip}/{AxisEthernetCam.IMAGE_JPG_URL}?resolution={self._width}x{self._height}"

                # TODO(#961): make timestamps trustworthy
                timestamp_ms = maka_control_timestamp_ms()
                request = urllib.request.urlopen(url)
                bytes = request.read()
                image = cv2.imdecode(np.frombuffer(bytes, np.byte), cv2.IMREAD_COLOR)

                if self._width is None and self._height is None:
                    self._width = image.shape[1]
                    self._height = image.shape[0]

                # Push Image
                self._new_frame(image, frame_timestamp_ms=timestamp_ms)
            except Exception:
                LOG.exception("Axis Ethernet Failure: {}".format(url))

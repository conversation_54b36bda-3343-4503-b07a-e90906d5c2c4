from typing import Any, Dict, Optional, <PERSON><PERSON>

import cv2
import numpy as np
from arena_api.buffer import _Buffer as ThinkLucidBuffer
from arena_api.system import system as thinklucid

import lib.common.logging
import lib.common.tasks
from core.cv.retina.camera.node import Cam
from core.drivers.camera import <PERSON><PERSON>otFoundException
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder
from lib.common.tasks import MakaTask
from lib.common.time import maka_control_timestamp_ms

LOG = lib.common.logging.get_logger(__name__)


class ThinkLucidCamProperties:
    def get_all(self) -> None:
        return None


class ThinkLucidCam(Cam):
    PIXEL_FORMAT_TO_COLOR_FORMAT: Dict[str, int] = {
        "BayerRG8": cv2.COLOR_BAYER_RG2BGR,
        "BayerBG8": cv2.COLOR_BAYER_BG2BGR,
        "BayerGR8": cv2.COLOR_BAYER_GR2BGR,
        "BayerGB8": cv2.COLOR_BAYER_GB2BGR,
    }

    def __init__(
        self,
        *argv: Any,
        ip: str,
        flip: bool = False,
        mirror: bool = False,
        strobing: Optional[bool] = None,
        ptp: bool = False,
        **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)

        self._internal_flip = flip
        self._internal_mirror = mirror
        self._strobing = strobing
        self._ptp = ptp

        all_dev_infos = thinklucid.device_infos
        dev_info = None
        for candidate_dev_info in all_dev_infos:
            if candidate_dev_info["ip"] == ip:
                dev_info = candidate_dev_info
        if dev_info is None:
            raise CameraNotFoundException(f"Camera with IP {ip} was not found. Device Infos: {all_dev_infos}")

        self._camera = thinklucid.create_device(dev_info)[0]
        self._properties = ThinkLucidCamProperties()

        self._width = self._camera.nodemap["Width"].value
        self._height = self._camera.nodemap["Height"].value
        if not self._transpose:
            self._resolution = (self._width, self._height)
        else:
            self._resolution = (self._height, self._width)
        self._pixel_format = ""

        self._set_settings_overrides()

    def _get_color_format(self) -> int:
        return self.PIXEL_FORMAT_TO_COLOR_FORMAT[self._pixel_format]

    def _set_settings_overrides(self) -> None:
        # Resetting Reverse Setting to obtain common PixelFormat profile
        # Reverse settings have an effect on the PixelFormat:
        # https://docs.baslerweb.com/reverse-x-and-reverse-y#effective-bayer-filter-alignments-color-cameras-only
        self._camera.nodemap["ReverseX"].value = False
        self._camera.nodemap["ReverseY"].value = False

        # We support only camera type for now, so we'll hardcode this.
        self._camera.nodemap["PixelFormat"].value = "BayerRG8"
        self._camera.nodemap["ADCBitDepth"].value = "Bits8"
        self._pixel_format = self._camera.nodemap["PixelFormat"].value

        # These are much faster to do inside camera compared to cv2. We disable cv2 processing
        # by resetting them to false.
        self._camera.nodemap["ReverseX"].value = self._internal_flip
        self._camera.nodemap["ReverseY"].value = self._internal_mirror

        if self._strobing:
            self._camera.nodemap["LineSelector"].value = "Line0"
            self._camera.nodemap["LineMode"].value = "Input"
            self._camera.nodemap["TriggerMode"].value = "On"
            self._camera.nodemap["TriggerSelector"].value = "FrameStart"
            self._camera.nodemap["TriggerSource"].value = "Line0"
            self._camera.nodemap["TriggerActivation"].value = "RisingEdge"
            self._camera.nodemap["LineSelector"].value = "Line1"
            self._camera.nodemap["LineMode"].value = "Output"
            self._camera.nodemap["LineSource"].value = "ExposureActive"
        else:
            self._camera.nodemap["TriggerMode"].value = "Off"

        if self._ptp:
            self._camera.nodemap["PtpSlaveOnly"].value = True
            self._camera.nodemap["PtpEnable"].value = True
        else:
            self._camera.nodemap["PtpEnable"].value = False

        self._camera.nodemap["ChunkModeActive"].value = True
        self._camera.nodemap["ChunkSelector"].value = "ExposureTime"
        self._camera.nodemap["ChunkEnable"].value = True

        self._camera.tl_stream_nodemap["StreamBufferHandlingMode"].value = "NewestOnly"
        self._camera.tl_stream_nodemap["StreamAutoNegotiatePacketSize"].value = True
        self._camera.tl_stream_nodemap["StreamPacketResendEnable"].value = True

        # TODO: Set common settings, disable once we have properties.
        self._camera.nodemap["ExposureAuto"].value = "Off"
        self._camera.nodemap["ExposureTime"].value = 5000.0
        self._camera.nodemap["Gamma"].value = 0.5
        self._camera.nodemap["GainAuto"].value = "Off"
        self._camera.nodemap["Gain"].value = 20.0

    def _diagnose_buffer(self, buf: ThinkLucidBuffer) -> str:
        if buf.is_incomplete:
            return "Incomplete buffer"
        if not buf.is_valid_crc:
            return "Invalid CRC"
        return "Unknown error"

    def _control_loop(self) -> None:
        LOG.info(
            "%s Starting camloop. resolution %dx%d...", self.short_name, *self.resolution,
        )
        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None

        color_format = self._get_color_format()
        try:
            with self._camera.start_stream():
                while True:
                    task.tick()

                    # grab image
                    img: Optional[np.ndarray] = None
                    with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.id}", "ThinkLucidGrab"):
                        buf = self._camera.get_buffer(timeout=5000)
                        try:
                            if not buf.is_incomplete:
                                if self._ptp:
                                    timestamp_ms = int(
                                        (buf.timestamp_ns / 1000 + buf.get_chunk("ChunkExposureTime").value / 2) / 1000
                                    )
                                else:
                                    timestamp_ms = maka_control_timestamp_ms()

                                with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.id}", "ThinkLucidCopy"):
                                    img = cv2.cvtColor(
                                        np.ctypeslib.as_array(buf.pdata, (self._height, self._width)), color_format
                                    )
                            else:
                                LOG.warning(f"{self.short_name} grab failed: {self._diagnose_buffer(buf)}")
                        finally:
                            self._camera.requeue_buffer(buf)
                        if img is not None:
                            self._new_frame(img, frame_timestamp_ms=timestamp_ms)

        except lib.common.tasks.MakaTaskCancelledException:
            pass
        except Exception:
            LOG.exception("Camera Failure")
        finally:
            thinklucid.destroy_device(self._camera)

    @property
    def resolution(self) -> Tuple[int, int]:
        return self._resolution

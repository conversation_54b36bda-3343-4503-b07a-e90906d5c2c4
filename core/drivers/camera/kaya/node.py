from typing import Any, Dict, List, Optional, Tuple, cast

import lib.common.logging
import lib.common.tasks
from core.cv.retina.camera.node import Cam
from core.cv.retina.lens import LensNode
from lib.common.tasks import MakaTask
from lib.drivers.kaya.client import KayaClient

LOG = lib.common.logging.get_logger(__name__)


class KayaCamProperties:
    def __init__(self) -> None:
        self._exposure_us = 500
        self._analog_gain_level = 1.25
        self._analog_black_level = -1250.0
        self._wb_ratio_red = 1.7
        self._wb_ratio_green = 1.0
        self._wb_ratio_blue = 2.5

    def get_all(self) -> Dict[str, Any]:
        return {
            "id": "root",
            "display_name": "Root",
            "type": "section",
            "children": [
                {
                    "id": "exposure_us",
                    "display_name": "Exposure (us)",
                    "type": "integer",
                    "value": self._exposure_us,
                    "step": 10,
                    "min": 10,
                    "max": 10000,
                },
                {
                    "id": "analog_gain_level",
                    "display_name": "Analog Gain Level",
                    "type": "float",
                    "value": self._analog_gain_level,
                    "step": 0.25,
                    "min": 0.75,
                    "max": 6.0,
                },
                {
                    "id": "analog_black_level",
                    "display_name": "Analog Black Level",
                    "type": "float",
                    "value": self._analog_black_level,
                    "step": 1.0,
                    "min": -8192,
                    "max": 8191,
                },
                {
                    "id": "wb_ratio_red",
                    "display_name": "White Balance Ratio Red",
                    "type": "float",
                    "value": self._wb_ratio_red,
                    "step": 0.01,
                    "min": 0.0,
                    "max": 7.95,
                },
                {
                    "id": "wb_ratio_green",
                    "display_name": "White Balance Ratio Green",
                    "type": "float",
                    "value": self._wb_ratio_green,
                    "step": 0.01,
                    "min": 0.0,
                    "max": 7.95,
                },
                {
                    "id": "wb_ratio_blue",
                    "display_name": "White Balance Ratio Blue",
                    "type": "float",
                    "value": self._wb_ratio_blue,
                    "step": 0.01,
                    "min": 0.0,
                    "max": 7.95,
                },
            ],
        }

    def set(self, key: str, value: Any) -> Dict[str, Any]:
        if key == "exposure_us":
            self._exposure_us = int(value)
        elif key == "analog_gain_level":
            self._analog_gain_level = float(value)
        elif key == "analog_black_level":
            self._analog_black_level = float(value)
        elif key == "wb_ratio_red":
            self._wb_ratio_red = float(value)
        elif key == "wb_ratio_green":
            self._wb_ratio_green = float(value)
        elif key == "wb_ratio_blue":
            self._wb_ratio_blue = float(value)
        else:
            raise ValueError(f"Unknown property: {key}")
        return self.get_all()

    @property
    def exposure_us(self) -> int:
        return self._exposure_us

    @property
    def analog_gain_level(self) -> float:
        return self._analog_gain_level

    @property
    def analog_black_level(self) -> float:
        return self._analog_black_level

    @property
    def wb_ratio_red(self) -> float:
        return self._wb_ratio_red

    @property
    def wb_ratio_green(self) -> float:
        return self._wb_ratio_green

    @property
    def wb_ratio_blue(self) -> float:
        return self._wb_ratio_blue


class KayaCam(Cam):
    def __init__(self, *args: Any, ip: str, resolution: Optional[Dict[str, int]] = None, **kwargs: Any):
        super().__init__(*args, **kwargs)
        self._properties = KayaCamProperties()
        self._focus = 0.0
        self._focusing_mode = False
        self._width = (resolution or {}).get("width")
        self._height = (resolution or {}).get("height")
        assert (self._width is None) == (self._height is None)
        self._ip = ip
        self._cam = KayaClient(ip)
        LOG.info(f"{self.device_path}: Kaya cam with ip: {ip}")

    @property
    def resolution(self) -> Optional[Tuple[int, int]]:
        if self._width is None and self._height is None:
            return None
        assert self._width is not None
        assert self._height is not None
        return self._width, self._height

    def set_focus(self, focus: float) -> None:
        self._focus = focus

    @property
    def focus(self) -> float:
        return self._focus

    def set_focusing_mode(self, focusing_mode: bool) -> None:
        self._focusing_mode = focusing_mode

    @property
    def focusing_mode(self) -> bool:
        return self._focusing_mode

    def _control_loop(self) -> None:
        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None
        assert self._properties is not None
        self.pause()
        while True:
            task.tick()
            try:
                ret = self._cam.grab(
                    exposure_us=self._properties.exposure_us,
                    analog_gain_level=self._properties.analog_gain_level,
                    analog_black_level=self._properties.analog_black_level,
                    focus=self.focus,
                    wb_ratio_red=self._properties.wb_ratio_red,
                    wb_ratio_green=self._properties.wb_ratio_green,
                    wb_ratio_blue=self._properties.wb_ratio_blue,
                    focusing_mode=self.focusing_mode,
                )

                if self._width is None and self._height is None:
                    self._width = ret.data.shape[1]
                    self._height = ret.data.shape[0]

                # Push Image
                self._new_frame(ret.data, frame_timestamp_ms=ret.timestamp_ms)
            except Exception:
                LOG.exception(f"{self.device_path}: Kaya Camera Failure: {self._ip}")


class KayaLensNodeDriver(LensNode):
    def __init__(self, camera: KayaCam, **kwargs: Any):
        super().__init__(**kwargs)

        self._camera = camera
        self._ref_id: str = cast(str, kwargs.get("ref_id"))

    def get_focus(self) -> float:
        return self._camera.focus

    def set_focus(self, focus: float) -> None:
        self._camera.set_focus(focus)

    def get_id(self) -> str:
        return self._ref_id

    def focus_range(self) -> Tuple[float, float]:
        return (-1.0, 1.0)

    def focus_step_sizes(self) -> List[float]:
        return [0.2, 0.05, 0.01]

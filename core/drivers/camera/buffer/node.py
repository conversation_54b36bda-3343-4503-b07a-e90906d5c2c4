import threading
import time
from typing import Any, <PERSON>tional, <PERSON><PERSON>

import numpy as np

import lib.common.logging
import lib.common.tasks
from core.cv.retina.camera.node import Cam
from cv.runtime.cpp.shmem_types_python import Image<PERSON>atestBuffer, ShmemImageOutput, create_image_latest_buffer
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.image import Buffered<PERSON>am<PERSON>mage, CamImage
from lib.common.perf.perf_tracker import Perf<PERSON>ategory, duration_perf_recorder
from lib.common.shmem.cpp.shmem_python import ImageBuffer, ImageRefBuffer, ShmemTimeoutException
from lib.common.tasks import MakaTask
from lib.common.time import TimeoutException, maka_control_timestamp_ms

LOG = lib.common.logging.get_logger(__name__)


class BufferCam(Cam):
    def __init__(
        self,
        *argv: Any,
        ref_buffer_name: str,
        timeout_ms: int = 5000,
        looping: bool = True,
        has_depth: bool = False,
        **kwargs: Any,
    ):
        # Has depth is needed for super() constructor.
        self._has_depth = has_depth
        super().__init__(*argv, **kwargs)

        # Note, in non-looping mode, the BufferCam is more efficient since it is on demand
        # However several camera features (e.g. recording) rely on the loop right now.
        self._looping = looping
        self._timeout_ms = timeout_ms
        self._acquire_sleep_ms = timeout_ms / 2
        self._ref_buffer_name = ref_buffer_name
        self._ref_buffer: Optional[ImageRefBuffer] = None
        self._last_extract_timestamp_ms: Optional[int] = None
        self._timeout_on_last: bool = False
        self._ready_event = threading.Event()

    def _acquire_shmem_buffer(self, timeout_ms: Optional[int] = None) -> None:
        last_warning_ms = 0
        start_time_ms = maka_control_timestamp_ms()
        while not bot_stop_handler.stopped:
            try:
                self._ref_buffer = ImageRefBuffer(self._ref_buffer_name)
                buf = self._ref_buffer.open_latest_ref()
                shape = buf.get_numpy_array(buf).shape
                self._resolution = shape[1], shape[0]
                self._ready_event.set()
                return
            except RuntimeError:
                timestamp_ms = maka_control_timestamp_ms()
                if timestamp_ms - last_warning_ms > 60000:  # 1 Warning per Minute
                    last_warning_ms = timestamp_ms
                    LOG.warning(f"Failing to Open ImageRefBuffer: {self._ref_buffer_name}")
            if timeout_ms is not None and maka_control_timestamp_ms() - start_time_ms > timeout_ms:
                raise TimeoutException("Timeout Trying to Acquire Ref Buffer")
            time.sleep(self._acquire_sleep_ms / 1000)

    def _control_loop(self) -> None:
        self._acquire_shmem_buffer()
        assert self._ref_buffer is not None
        if not self._looping:
            return
        LOG.info(f"Starting Buffer Cam Loop: {self.id}")
        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None
        last_timestamp_ms = None
        stop_blocker_event = bot_stop_handler.create_stop_blocker()
        try:
            while not bot_stop_handler.stopped:
                task.tick()
                try:
                    with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.id}", "BufferCamNext"):
                        while True:
                            # This is workaround until we figure out how to use open_next_ref()
                            # without cross-process hanging.
                            buf = self._ref_buffer.open_next_ref(timeout_ms=5000)
                            if buf.get_timestamp_ms() != last_timestamp_ms:
                                break
                            time.sleep(0.01)
                        img_buf, depth_buf = self.extract_img_and_depth_bufs(buf)
                        self._new_frame_from_buffer(img_buf, depth_buf, frame_timestamp_ms=buf.get_timestamp_ms())
                        last_timestamp_ms = buf.get_timestamp_ms()
                except (RuntimeError, ShmemTimeoutException):
                    # Reacquire the ref buffer
                    self._acquire_shmem_buffer()
        except lib.common.tasks.MakaTaskCancelledException:
            pass
        finally:
            stop_blocker_event.set()

    def extract_img_and_depth_bufs(self, buf: ImageBuffer) -> Tuple[ImageBuffer, Optional[ImageBuffer]]:
        if self._has_depth:
            if buf.get_timestamp_ms() != self._last_extract_timestamp_ms:
                img_buf = ImageBuffer(f"{buf.get_name()}_bgr", (self._resolution[1], self._resolution[0], 3))
                img = img_buf.get_numpy_array(img_buf)
                img[:] = buf.get_numpy_array(buf)[:, :, 0:3]

                depth_buf = ImageBuffer(f"{buf.get_name()}_depth", (self._resolution[1], self._resolution[0], 1))
                depth = depth_buf.get_numpy_array(depth_buf)
                depth[:] = buf.get_numpy_array(buf)[:, :, 3:4]

                self._last_extract_timestamp_ms = buf.get_timestamp_ms()
            else:
                img_buf = ImageBuffer(f"{buf.get_name()}_bgr")
                depth_buf = ImageBuffer(f"{buf.get_name()}_depth")

            return img_buf, depth_buf
        else:
            return buf, None

    def next(self, timeout_ms: int = 5000) -> CamImage:
        if self._looping:
            return super().next(timeout_ms)

        if self._ref_buffer is None:
            ready = self._ready_event.wait(timeout=timeout_ms / 1000)
            if not ready:
                raise lib.common.time.time.TimeoutException(f"BufferCam not Ready: {self.id}")
            assert self._ref_buffer is not None

        if self._timeout_on_last:
            self._acquire_shmem_buffer(timeout_ms=timeout_ms)
            self._timeout_on_last = False

        try:
            buf = self._ref_buffer.open_next_ref(self._timeout_ms)
        except ShmemTimeoutException:
            self._timeout_on_last = True
            raise TimeoutException("Timeout waiting for next Ref")

        img_buf, depth_buf = self.extract_img_and_depth_bufs(buf)
        return BufferedCamImage(
            device_path=self.device_path,
            camera_id=self.id,
            timestamp_ms=buf.get_timestamp_ms(),
            ppi=self._ppi,
            buf=img_buf,
            depth_buf=depth_buf,
        )

    def last(self) -> Optional[CamImage]:
        if self._looping:
            return super().last()

        assert self._ref_buffer is not None

        if self._timeout_on_last:
            self._acquire_shmem_buffer(timeout_ms=0)
            self._timeout_on_last = False

        buf = self._ref_buffer.open_latest_ref()
        img_buf, depth_buf = self.extract_img_and_depth_bufs(buf)
        return BufferedCamImage(
            device_path=self.device_path,
            camera_id=self.id,
            timestamp_ms=buf.get_timestamp_ms(),
            ppi=self._ppi,
            buf=img_buf,
            depth_buf=depth_buf,
        )

    @property
    def resolution(self) -> Tuple[int, int]:
        """
        This must be called after camera.Open is called (which happens on our constructor).
        """
        return self._resolution

    @property
    def has_depth(self) -> bool:
        return self._has_depth


class LatestBufferCam(Cam):
    def __init__(
        self,
        *argv: Any,
        latest_buffer_name: str,
        timeout_ms: int = 5000,
        looping: bool = True,
        has_depth: bool = False,
        **kwargs: Any,
    ):
        # Has depth is needed for super() constructor.
        self._has_depth = has_depth
        # Note, in non-looping mode, the LatestBufferCam is more efficient since it is on demand
        # However several camera features (e.g. recording) rely on the loop right now.
        self._looping = looping
        self._timeout_ms = timeout_ms
        self._acquire_sleep_ms = timeout_ms / 2
        self._latest_buffer_name = latest_buffer_name
        self._latest_buffer: Optional[ImageLatestBuffer] = None
        self._last_extract_timestamp_ms: Optional[int] = None
        self._timeout_on_last: bool = False
        self._ready_event = threading.Event()
        self._cached_output = ShmemImageOutput()

        # NOTE: Running super() kicks off the annotation task. If super() is called first thing then it will sometimes
        # crash because it uses member variables initialized in the constructor. Moving it here was a quick fix until a
        # redesign is possible.
        super().__init__(*argv, **kwargs)

    def _acquire_shmem_buffer(self, timeout_ms: Optional[int] = None) -> None:
        last_warning_ms = 0
        start_time_ms = maka_control_timestamp_ms()
        while not bot_stop_handler.stopped:
            try:
                self._latest_buffer = create_image_latest_buffer(self._latest_buffer_name)
                self._latest_buffer.get(self._cached_output)
                shape = self._cached_output.get_numpy_array(self._cached_output).shape
                self._resolution = shape[1], shape[0]
                self._ready_event.set()
                return
            except RuntimeError:
                timestamp_ms = maka_control_timestamp_ms()
                if timestamp_ms - last_warning_ms > 60000:  # 1 Warning per Minute
                    last_warning_ms = timestamp_ms
                    LOG.warning(f"Failing to Open Image from LatestBuffer: {self._latest_buffer_name}")
            if timeout_ms is not None and maka_control_timestamp_ms() - start_time_ms > timeout_ms:
                raise TimeoutException("Timeout Trying to Acquire Latest Buffer")
            time.sleep(self._acquire_sleep_ms / 1000)

    def _control_loop(self) -> None:
        self._acquire_shmem_buffer()
        assert self._latest_buffer is not None
        if not self._looping:
            return
        LOG.info(f"Starting Latest Buffer Cam Loop: {self.id}")
        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None
        last_timestamp_ms: int = 0
        stop_blocker_event = bot_stop_handler.create_stop_blocker()
        try:
            while not bot_stop_handler.stopped:
                task.tick()
                try:
                    with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.id}", "BufferCamNext"):
                        while True:
                            self._latest_buffer.get_next(
                                self._cached_output, timeout_ms=5000, last_timestamp_ms=last_timestamp_ms
                            )
                            if self._cached_output.timestamp_ms != last_timestamp_ms:
                                break
                            time.sleep(0.01)
                        img = self._cached_output.get_numpy_array(self._cached_output).copy()
                        img, depth = self.extract_img_and_depth_bufs(img)
                        self._new_frame(
                            img,
                            depth,
                            frame_timestamp_ms=self._cached_output.timestamp_ms,
                            focus_value=self._cached_output.focus_metric,
                        )
                        last_timestamp_ms = self._cached_output.timestamp_ms
                except (RuntimeError, ShmemTimeoutException):
                    # Reacquire the ref buffer
                    self._acquire_shmem_buffer()
        except lib.common.tasks.MakaTaskCancelledException:
            pass
        finally:
            stop_blocker_event.set()

    def extract_img_and_depth_bufs(self, img: np.ndarray) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        if self._has_depth:
            rgb_img = img[:, :, 0:3]
            depth_img = img[:, :, 3:4]

            return rgb_img, depth_img
        else:
            return img, None

    def next(self, timeout_ms: int = 5000) -> CamImage:
        if self._looping:
            return super().next(timeout_ms)

        if self._latest_buffer is None:
            ready = self._ready_event.wait(timeout=timeout_ms / 1000)
            if not ready:
                raise lib.common.time.time.TimeoutException(f"BufferCam not Ready: {self.id}")
            assert self._latest_buffer is not None

        if self._timeout_on_last:
            self._acquire_shmem_buffer(timeout_ms=timeout_ms)
            self._timeout_on_last = False

        try:
            self._latest_buffer.get_next(
                self._cached_output, self._timeout_ms, last_timestamp_ms=maka_control_timestamp_ms()
            )
        except ShmemTimeoutException:
            self._timeout_on_last = True
            raise TimeoutException("Timeout waiting for next Ref")

        img, depth = self.extract_img_and_depth_bufs(self._cached_output.get_numpy_array(self._cached_output).copy())
        return CamImage(
            device_path=self.device_path,
            camera_id=self.id,
            timestamp_ms=self._cached_output.timestamp_ms,
            ppi=self._ppi,
            image_bgr=img,
            depth=depth,
            focus_value=self._cached_output.focus_metric if self._cached_output.focus_metric != -1 else None,
        )

    def last(self) -> Optional[CamImage]:
        if self._looping:
            return super().last()

        assert self._latest_buffer is not None

        if self._timeout_on_last:
            self._acquire_shmem_buffer(timeout_ms=0)
            self._timeout_on_last = False

        self._latest_buffer.get(self._cached_output)
        img, depth = self.extract_img_and_depth_bufs(self._cached_output.get_numpy_array(self._cached_output).copy())
        return CamImage(
            device_path=self.device_path,
            camera_id=self.id,
            timestamp_ms=self._cached_output.timestamp_ms,
            ppi=self._ppi,
            image_bgr=img,
            depth=depth,
            focus_value=self._cached_output.focus_metric if self._cached_output.focus_metric != -1 else None,
        )

    @property
    def resolution(self) -> Tuple[int, int]:
        """
        This must be called after camera.Open is called (which happens on our constructor).
        """
        ready = self._ready_event.wait(timeout=5)
        if not ready:
            raise lib.common.time.time.TimeoutException(f"BufferCam not Ready: {self.id}")
        return self._resolution

    @property
    def has_depth(self) -> bool:
        return self._has_depth

from typing import Any, Dict, List, Optional, Tuple, cast

import lib.common.logging
import lib.common.tasks
from core.cv.retina.camera.node import Cam
from core.cv.retina.lens import LensNode
from lib.common.tasks import MakaTask
from lib.drivers.emergent.client import ColorTemp, EmergentClient

LOG = lib.common.logging.get_logger(__name__)


class EmergentCamProperties:
    def __init__(self) -> None:
        self._color_temp = ColorTemp.CT_Off
        self._exposure_us = 450
        self._gain_db = 0.0
        self._wb_r_gain_db = 6.5
        self._wb_gr_gain_db = 0.0
        self._wb_gb_gain_db = 0.0
        self._wb_b_gain_db = 8.5

    def get_all(self) -> Dict[str, Any]:
        return {
            "id": "root",
            "display_name": "Root",
            "type": "section",
            "children": [
                {
                    "id": "color_temp",
                    "display_name": "Color Temperature",
                    "type": "select",
                    "options": [k for k in ColorTemp.keys() if k != ColorTemp.Name(ColorTemp.CT_UNKNOWN)],
                    "value": ColorTemp.Name(self._color_temp),
                },
                {
                    "id": "exposure_us",
                    "display_name": "Exposure (us)",
                    "type": "integer",
                    "value": self._exposure_us,
                    "step": 10,
                    "min": 10,
                    "max": 10000,
                },
                {
                    "id": "gain_db",
                    "display_name": "Gain (db)",
                    "type": "float",
                    "value": self._gain_db,
                    "step": 0.1,
                    "min": 0.0,
                    "max": 24.0,
                },
                {
                    "id": "wb_r_gain_db",
                    "display_name": "White Balance R Gain (db)",
                    "type": "float",
                    "value": self._wb_r_gain_db,
                    "step": 0.1,
                    "min": 0.0,
                    "max": 20.0,
                },
                {
                    "id": "wb_gr_gain_db",
                    "display_name": "White Balance GR Gain (db)",
                    "type": "float",
                    "value": self._wb_gr_gain_db,
                    "step": 0.1,
                    "min": 0.0,
                    "max": 20.0,
                },
                {
                    "id": "wb_gb_gain_db",
                    "display_name": "White Balance GB Gain (db)",
                    "type": "float",
                    "value": self._wb_gb_gain_db,
                    "step": 0.1,
                    "min": 0.0,
                    "max": 20.0,
                },
                {
                    "id": "wb_b_gain_db",
                    "display_name": "White Balance B Gain (db)",
                    "type": "float",
                    "value": self._wb_b_gain_db,
                    "step": 0.1,
                    "min": 0.0,
                    "max": 20.0,
                },
            ],
        }

    def set(self, key: str, value: Any) -> Dict[str, Any]:
        if key == "color_temp":
            self._color_temp = ColorTemp.Value(value)
        elif key == "exposure_us":
            self._exposure_us = int(value)
        elif key == "gain_db":
            self._gain_db = float(value)
        elif key == "wb_r_gain_db":
            self._wb_r_gain_db = float(value)
        elif key == "wb_gr_gain_db":
            self._wb_gr_gain_db = float(value)
        elif key == "wb_gb_gain_db":
            self._wb_gb_gain_db = float(value)
        elif key == "wb_b_gain_db":
            self._wb_b_gain_db = float(value)
        else:
            raise ValueError(f"Unknown property: {key}")
        return self.get_all()

    @property
    def color_temp(self) -> ColorTemp:
        return self._color_temp

    @property
    def exposure_us(self) -> int:
        return self._exposure_us

    @property
    def gain_db(self) -> float:
        return self._gain_db

    @property
    def wb_r_gain_db(self) -> float:
        return self._wb_r_gain_db

    @property
    def wb_gr_gain_db(self) -> float:
        return self._wb_gr_gain_db

    @property
    def wb_gb_gain_db(self) -> float:
        return self._wb_gb_gain_db

    @property
    def wb_b_gain_db(self) -> float:
        return self._wb_b_gain_db


class EmergentCam(Cam):
    def __init__(self, *args: Any, ip: str, resolution: Optional[Dict[str, int]] = None, **kwargs: Any):
        super().__init__(*args, **kwargs)
        self._properties = EmergentCamProperties()
        self._focus = 0.0
        self._focusing_mode = False
        self._width = (resolution or {}).get("width")
        self._height = (resolution or {}).get("height")
        assert (self._width is None) == (self._height is None)
        self._ip = ip
        self._cam = EmergentClient(ip)
        LOG.info(f"{self.device_path}: Emergent cam with ip: {ip}")

    @property
    def resolution(self) -> Optional[Tuple[int, int]]:
        if self._width is None and self._height is None:
            return None
        assert self._width is not None
        assert self._height is not None
        return self._width, self._height

    def set_focus(self, focus: float) -> None:
        self._focus = focus

    @property
    def focus(self) -> float:
        return self._focus

    def set_focusing_mode(self, focusing_mode: bool) -> None:
        self._focusing_mode = focusing_mode

    @property
    def focusing_mode(self) -> bool:
        return self._focusing_mode

    def _control_loop(self) -> None:
        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None
        assert self._properties is not None
        self.pause()
        while True:
            task.tick()
            try:
                ret = self._cam.grab(
                    color_temp=self._properties.color_temp,
                    exposure_us=self._properties.exposure_us,
                    gain_db=self._properties.gain_db,
                    focus=self.focus,
                    wb_r_gain_db=self._properties.wb_r_gain_db,
                    wb_gr_gain_db=self._properties.wb_gr_gain_db,
                    wb_gb_gain_db=self._properties.wb_gb_gain_db,
                    wb_b_gain_db=self._properties.wb_b_gain_db,
                    focusing_mode=self.focusing_mode,
                )

                if self._width is None and self._height is None:
                    self._width = ret.data.shape[1]
                    self._height = ret.data.shape[0]

                # Push Image
                self._new_frame(ret.data, frame_timestamp_ms=ret.timestamp_ms)
            except Exception:
                LOG.exception(f"{self.device_path}: Emergent Camera Failure: {self._ip}")


class EmergentLensNodeDriver(LensNode):
    def __init__(self, camera: EmergentCam, **kwargs: Any):
        super().__init__(**kwargs)

        self._camera = camera
        self._ref_id: str = cast(str, kwargs.get("ref_id"))

    def get_focus(self) -> float:
        return self._camera.focus

    def set_focus(self, focus: float) -> None:
        self._camera.set_focus(focus)

    def get_id(self) -> str:
        return self._ref_id

    def focus_range(self) -> Tuple[float, float]:
        return (-1.0, 1.0)

    def focus_step_sizes(self) -> List[float]:
        return [0.2, 0.04, 0.005]

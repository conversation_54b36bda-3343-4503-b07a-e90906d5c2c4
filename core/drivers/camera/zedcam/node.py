from typing import Any, Dict, Optional, Tuple

import lib.common.logging
import lib.common.tasks
from core.cv.retina.camera.node import Cam
from lib.common.perf.perf_tracker import Perf<PERSON>ategory, duration_perf_recorder
from lib.common.shmem.cpp.shmem_python import ImageBuffer
from lib.common.shmem.name import shared_mem_path_generator
from lib.common.tasks import MakaTask
from lib.drivers.zedcam.client import ZEDClient

LOG = lib.common.logging.get_logger(__name__)


class ZedCamProperties:
    def get_all(self) -> None:
        return None


class ZedCam(Cam):
    def __init__(
        self,
        *args: Any,
        ip: str,
        resolution: Optional[Dict[str, int]] = None,
        shmem_enabled: bool = False,
        **kwargs: Any,
    ):
        super().__init__(*args, **kwargs)
        self._properties = ZedCamProperties()
        self._width = (resolution or {}).get("width")
        self._height = (resolution or {}).get("height")
        assert (self._width is None) == (self._height is None)
        self._shmem_enabled = shmem_enabled
        self._ip = ip
        self._zed = ZEDClient(ip)
        LOG.info(f"{self.device_path}: ZED cam with ip: {ip}")

    @property
    def resolution(self) -> Optional[Tuple[int, int]]:
        if self._width is None and self._height is None:
            return None
        assert self._width is not None
        assert self._height is not None
        return self._width, self._height

    @property
    def has_depth(self) -> bool:
        return True

    def _control_loop(self) -> None:
        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None
        gen = shared_mem_path_generator(self.id)
        while True:
            task.tick()
            try:
                with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.id}", "Grab"):
                    ret = self._zed.grab()

                if self._width is None and self._height is None:
                    self._width = ret.view_image.shape[1]
                    self._height = ret.view_image.shape[0]

                if self._shmem_enabled:
                    # no benefit of constructing early because nothing is happening until grab() is executed
                    # TODO - async grabbing?
                    with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.id}", "BufferConstruct"):
                        buffer_path = next(gen)

                        assert self._height is not None and self._width is not None
                        img_buf = ImageBuffer(f"{buffer_path}_bgr", (self._height, self._width, 3))
                        img = img_buf.get_numpy_array(img_buf)
                        img[:] = ret.view_image

                        depth_buf = ImageBuffer(f"{buffer_path}_depth", (self._height, self._width, 1))
                        depth = depth_buf.get_numpy_array(depth_buf)
                        depth[:] = ret.view_depth.reshape(self._height, self._width, 1)

                    self._new_frame_from_buffer(img_buf, depth_buf, frame_timestamp_ms=ret.timestamp_ms)
                else:
                    # Push Image
                    self._new_frame(ret.view_image, depth_frame=ret.view_depth, frame_timestamp_ms=ret.timestamp_ms)
            except Exception:
                LOG.exception(f"{self.device_path}: ZED Failure: {self._ip}")

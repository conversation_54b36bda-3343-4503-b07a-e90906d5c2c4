import glob
import os
import re
import shutil
import threading
from queue import Empty, Full, Queue
from typing import Any, Dict, List, Optional, Tuple

import cv2
import numpy as np
from pypylon import pylon

import lib.common.logging
import lib.common.tasks
from core.cv.retina.camera.node import Cam
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder
from lib.common.shmem.cpp.shmem_python import ImageBuffer
from lib.common.shmem.name import shared_mem_path_generator
from lib.common.tasks import MakaTask
from lib.common.time import maka_control_timestamp_ms
from lib.common.time.sleep import sleep_ms
from lib.drivers.pylon.properties import PylonCamProperties
from lib.drivers.pylon.runner import PylonDevice

LOG = lib.common.logging.get_logger(__name__)


class PylonCam(Cam):
    CAM_FILE_PREFIX = ".pylonrc_"
    # Not sure why RGB ends up being the correct format instead of BGR
    PIXEL_FORMAT_TO_COLOR_FORMAT: Dict[str, int] = {
        "BayerRG8": cv2.COLOR_BAYER_RG2RGB,
        "BayerBG8": cv2.COLOR_BAYER_BG2RGB,
        "BayerGR8": cv2.COLOR_BAYER_GR2RGB,
        "BayerGB8": cv2.COLOR_BAYER_GB2RGB,
        "Mono8": cv2.COLOR_GRAY2BGR,
    }

    def __init__(
        self,
        *argv: Any,
        flip: bool = False,
        mirror: bool = False,
        pylon_device: Optional[PylonDevice] = None,
        shmem_enabled: bool = False,
        strobing: Optional[bool] = None,
        roi_width: Optional[int] = None,
        roi_height: Optional[int] = None,
        roi_offset_x: Optional[int] = None,
        roi_offset_y: Optional[int] = None,
        ptp: bool = False,
        **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)

        assert pylon_device is not None

        self._shmem_enabled = shmem_enabled
        self._pylon_flip = flip
        self._pylon_mirror = mirror
        self._pylon_device = pylon_device
        self._pylon_roi_width = roi_width
        self._pylon_roi_height = roi_height
        self._pylon_roi_offset_x = roi_offset_x
        self._pylon_roi_offset_y = roi_offset_y
        self._ptp = ptp

        tl_factory = pylon.TlFactory.GetInstance()
        self.camera = pylon.InstantCamera(tl_factory.CreateDevice(pylon_device.device))
        self._resolution: Optional[Tuple[int, int]] = None
        self._raw_resolution: Optional[Tuple[int, int]] = None
        self._strobing = strobing
        self.camera.Open()
        pf = self.pylon_rc_filename()
        self._active_settings_filepath: str = pf
        LOG.info("Loading camera settings from {}".format(pf))
        try:
            pylon.FeaturePersistence.Load(pf, self.camera.GetNodeMap(), True)
        except Exception:
            LOG.exception("Caught exception while loading {}".format(pf))
            self.save_settings(save_directory=self.fs.abs_config_dir)

        self._set_settings_overrides()
        self._properties = PylonCamProperties(self.camera.GetNodeMap())
        try:
            LOG.info(f"Expected Maximum Frame Rate: {self.camera.ResultingFrameRate.GetValue()}")
        except:  # noqa
            LOG.info("Expected Maximum Frame Rate is unknown.")
        # to store web requests
        self.requests: "Queue[Tuple[str, threading.Event]]" = Queue(1)

    def _get_color_format(self) -> int:
        return self.PIXEL_FORMAT_TO_COLOR_FORMAT[self._pixel_format]

    def _set_settings_overrides(self) -> None:
        # Not Sure why this is needed, but without it the value does not get set
        sleep_ms(200)

        # Resetting Reverse Setting to obtain common PixelFormat profile
        # Reverse settings have an effect on the PixelFormat:
        # https://docs.baslerweb.com/reverse-x-and-reverse-y#effective-bayer-filter-alignments-color-cameras-only
        self.camera.ReverseX.SetValue(False)
        self.camera.ReverseY.SetValue(False)

        # Set ROI
        if self._pylon_roi_width is not None:
            self.camera.Width.SetValue(self._pylon_roi_width)
        if self._pylon_roi_height is not None:
            self.camera.Height.SetValue(self._pylon_roi_height)
        if self._pylon_roi_offset_x is not None:
            self.camera.OffsetX.SetValue(self._pylon_roi_offset_x)
        if self._pylon_roi_offset_y is not None:
            self.camera.OffsetY.SetValue(self._pylon_roi_offset_y)

        assert self._pylon_device
        # Different pylon devices have a different default bayer,
        # This is a temporary way of handling it
        if self._pylon_device.type_str in ["acA1300-200uc", "acA1300-75gc"]:
            self.camera.PixelFormat.SetValue("BayerBG8")
        elif "gm" in self._pylon_device.type_str:
            self.camera.PixelFormat.SetValue("Mono8")
        else:
            self.camera.PixelFormat.SetValue("BayerRG8")
        # These are much faster to do inside camera compared to cv2. We disable cv2 processing
        # by resetting them to false.
        self.camera.ReverseX.SetValue(self._pylon_flip)
        self.camera.ReverseY.SetValue(self._pylon_mirror)

        self._pixel_format = self.camera.PixelFormat.GetValue()

        if self._strobing:
            self.camera.LineSelector.SetValue("Line1")
            self.camera.TriggerSelector.SetValue("FrameStart")
            self.camera.TriggerSource.SetValue("Line1")
            self.camera.TriggerMode.SetValue("On")
            self.camera.TriggerActivation.SetValue("RisingEdge")
            self.camera.LineSelector.SetValue("Line2")
            self.camera.LineSource.SetValue("ExposureActive")
        else:
            self.camera.TriggerMode.SetValue("Off")

        if self._ptp:
            self.camera.GevIEEE1588.SetValue(True)
            self.camera.ChunkModeActive.SetValue(True)
            self.camera.ChunkSelector.SetValue("Timestamp")
            self.camera.ChunkEnable.SetValue(True)
            self.camera.ChunkSelector.SetValue("ExposureTime")
            self.camera.ChunkEnable.SetValue(True)
        else:
            if not self.camera.IsUsb():
                self.camera.GevIEEE1588.SetValue(False)
            self.camera.ChunkModeActive.SetValue(False)

        self._raw_resolution = self.camera.Height.Value, self.camera.Width.Value

    def copy_settings_to(self, dir_: str) -> None:
        pylonrc_filepath = self.pylon_rc_filename()
        LOG.debug(f"Coping {pylonrc_filepath} to {dir_}...")
        _, pylonrc_filename = os.path.split(pylonrc_filepath)
        pylonrc_filename_not_hidden = pylonrc_filename[1:]  # drop leading .
        copy_to = os.path.join(dir_, pylonrc_filename_not_hidden)
        shutil.copy2(self.pylon_rc_filename(), copy_to)

    def _load_settings(self) -> None:
        try:
            cur_filepath, event = self.requests.get(block=False)
            self.load_settings(cur_filepath)
            event.set()
        except Empty:
            pass

    def _control_loop(self) -> None:
        LOG.info(
            "%s Starting camloop. resolution %dx%d, preview scale %.2f, saving to %s...",
            self.short_name,
            *self.resolution,
            self.preview_scale,
            self.fs.abs_media_dir,
        )
        task: Optional[MakaTask] = lib.common.tasks.get_current()
        assert task is not None
        self.camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)
        color_format = self._get_color_format()
        consecutive_failures = 0
        try:
            gen = shared_mem_path_generator(self.id)
            while self.camera.IsGrabbing():
                task.tick()

                self._load_settings()

                try:
                    # grab image
                    res = self.raw_resolution
                    with duration_perf_recorder(f"{PerfCategory.IMAGE}:{res}", "BufferConstruct"):
                        img: Optional[np.ndarray] = None
                        if self._shmem_enabled:
                            buf = ImageBuffer(next(gen), (res[0], res[1], 3))
                            img = buf.get_numpy_array(buf)
                    with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.id}", "PylonGrab"):
                        with self.camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException) as grabResult:
                            if grabResult.GrabSucceeded():
                                if self._ptp:
                                    timestamp_ms = int(
                                        (
                                            grabResult.ChunkTimestamp.GetValue() / 1000
                                            + grabResult.ChunkExposureTime.GetValue() / 2
                                        )
                                        / 1000
                                    )
                                else:
                                    timestamp_ms = maka_control_timestamp_ms()

                                with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.id}", "PylonZeroCopy"):
                                    mv = grabResult.GetImageMemoryView()
                                    shape, dtype, format = grabResult.GetImageFormat()
                                    mv = mv.cast(format, shape)
                                    if self._shmem_enabled:
                                        cv2.cvtColor(np.asarray(mv), color_format, dst=img)
                                    else:
                                        img = cv2.cvtColor(np.asarray(mv), color_format)
                                    mv.release()
                            else:
                                LOG.warning(
                                    f"{self.short_name} grab result failed: {grabResult.GetErrorCode()}: {grabResult.GetErrorDescription()}"
                                )

                        if self._shmem_enabled:
                            self._new_frame_from_buffer(buf, frame_timestamp_ms=timestamp_ms)
                        elif img is not None:
                            self._new_frame(img, frame_timestamp_ms=timestamp_ms)

                        consecutive_failures = 0
                except lib.common.tasks.MakaTaskCancelledException:
                    raise
                except Exception:
                    consecutive_failures = consecutive_failures + 1
                    LOG.exception(f"Camera failure ({consecutive_failures})")
                    if consecutive_failures > 10:
                        raise
        except lib.common.tasks.MakaTaskCancelledException:
            pass
        except Exception:
            LOG.exception(f"Complete Camera Failure (after {consecutive_failures})")
        finally:
            self.camera.StopGrabbing()
            self.camera.Close()

    def pylon_rc_filename(
        self,
        save_directory: Optional[str] = None,
        file_name_prefix: str = CAM_FILE_PREFIX,
        save_name: Optional[str] = None,
    ) -> str:
        save_full_name = file_name_prefix + self.short_name
        if save_name is not None:
            save_full_name = "{}.{}".format(save_full_name, save_name)
        if save_directory is None:
            save_directory = self.fs.abs_config_dir
        return os.path.join(save_directory, save_full_name)

    def save_settings(
        self, save_directory: Optional[str] = None, save_name: Optional[str] = None, logging: bool = True
    ) -> str:
        # clients who don't specify a save directory will by default save to MAKA_CONFIG_DIR
        if save_directory is None:
            save_directory = self.fs.abs_config_dir
        save_path = (
            save_name
            if save_name is not None and re.match(save_directory, save_name)
            else self.pylon_rc_filename(save_directory=save_directory, save_name=save_name)
        )
        pf = save_path
        self._active_settings_filepath = pf

        if logging:
            LOG.info("Saving {}".format(pf))
        try:
            pylon.FeaturePersistence.Save(pf, self.camera.GetNodeMap())
            if logging:
                LOG.info("Successfully saved {}".format(pf))
            return self._active_settings_filepath
        except Exception:
            LOG.exception("Exception while saving pylon settings")
        return self._active_settings_filepath

    def get_camera_config_files(self) -> List[str]:
        files = glob.glob(os.path.join(self.fs.abs_config_dir, "{}{}*".format(self.CAM_FILE_PREFIX, self.short_name)))
        return files

    def load_settings(self, filepath: str) -> None:
        self.camera.StopGrabbing()
        try:
            LOG.info(f"{self.id} Loading config file {filepath}")
            pylon.FeaturePersistence.Load(filepath, self.camera.GetNodeMap(), True)
            LOG.info(f"{self.id} Successfully loaded file settings from file {filepath}")
            self._active_settings_filepath = filepath
            self._set_settings_overrides()
            self._properties = PylonCamProperties(self.camera.GetNodeMap())
        except Exception:
            LOG.exception(f"{self.id} Caught exception while loading {filepath}")
        finally:
            self.camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)

    def change_camera_config_file(self, filename: Optional[str] = None) -> None:
        file_path = self.pylon_rc_filename() if filename is None else filename
        event = threading.Event()
        try:
            self.requests.put((file_path, event), block=False)
            event.wait()
        except Full:
            LOG.error(f"{self.id} There already is a request for changing the config file.")
            raise

    @property
    def active_settings_filepath(self) -> str:
        return self._active_settings_filepath

    @property
    def raw_resolution(self) -> Tuple[int, int]:
        """
        This must be called after camera.Open is called (which happens on our constructor).
        """
        if self._raw_resolution is None:
            self._raw_resolution = self.camera.Height.Value, self.camera.Width.Value
        return self._raw_resolution

    @property
    def resolution(self) -> Tuple[int, int]:
        """
        This must be called after camera.Open is called (which happens on our constructor).
        """
        if self._resolution is None:
            if not self._transpose:
                self._resolution = self.camera.Width.Value, self.camera.Height.Value
            else:
                self._resolution = self.camera.Height.Value, self.camera.Width.Value
        return self._resolution

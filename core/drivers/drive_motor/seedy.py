from typing import Any, Dict

from core.controls.driver.motor import DriveMotor
from lib.drivers.maka.seedy.board import MAX_STEPS_4251, CypressBoard


class CypressDriveMotor(DriveMotor):
    def __init__(
        self,
        *argv: Any,
        serial_device: str,
        motor_no: int,
        inverted: bool = False,
        min_speed: float = 0,
        max_speed: float = 1,
        **kwargs: Dict[str, Any]
    ):
        super().__init__(*argv, min_speed=min_speed, max_speed=max_speed, **kwargs)
        assert serial_device is not None
        self.board = CypressBoard.by_serial(serial_device)
        assert motor_no is not None
        self.motor_no = motor_no
        self.inverted = inverted

        self._val = 0.0

    @property
    def value(self) -> float:
        return self._val

    def set_value(self, val: float) -> None:
        board_val = int(round((val / 1.0) * MAX_STEPS_4251))
        self.board.set_motor(self.motor_no, board_val if not self.inverted else -board_val)
        self._val = val

    def stop(self) -> None:
        super().stop()
        self.set_value(0)

import threading
import time
from typing import Any, List, Tuple, cast

import lib.common.logging
from core.cv.retina.lens import LensNode
from lib.drivers.corning.connector import CorningConnector

LOG = lib.common.logging.get_logger(__name__)


class LiquidLensNodeDriver(LensNode):
    def __init__(self, serial_dev: str, **kwargs: Any):
        super().__init__(**kwargs)

        self._ref_id: str = cast(str, kwargs.get("ref_id"))
        self._serial_dev: str = serial_dev
        self._connector: CorningConnector = CorningConnector(serial_dev)
        self._lock = threading.Lock()

        # Attempt to set focus to test board
        self.set_focus(self.get_focus())

    def get_focus(self) -> float:
        with self._lock:
            return self._connector.get_focus()

    def set_focus(self, focus: float) -> None:
        with self._lock:
            self._connector.set_focus(int(focus))
        # liquid lens takes some time to execute adjustment
        time.sleep(0.2)

        new = self.get_focus()
        # Ensure the board is setting focus correctly
        assert new == focus

    def get_id(self) -> str:
        return self._ref_id

    def focus_range(self) -> Tuple[float, float]:
        return (0, 46000)  # per scochrane, per spec

    def focus_step_sizes(self) -> List[float]:
        return [5000, 1000, 250, 50]

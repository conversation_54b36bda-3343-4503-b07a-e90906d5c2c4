import threading
import time
from typing import Any, <PERSON>, Tuple, cast

import lib.common.logging
from config.client.cpp.config_client_python import get_global_config_subscriber
from core.controls.exterminator.controllers.aimbot.process.client.aimbot_client import shared_aimbot_client
from core.cv.retina.lens import LensNode

LOG = lib.common.logging.get_logger(__name__)


class AimbotProcessLens(LensNode):
    def __init__(
        self, *args: Any, scanner_id: int, **kwargs: Any,
    ):
        super().__init__(**kwargs)
        self._aimbot_client = shared_aimbot_client
        self._scanner_id = scanner_id
        self._aimbot_client.await_connection_from_sync()

        self._ref_id: str = cast(str, kwargs.get("ref_id"))
        self._lock = threading.Lock()

    def auto_focus(self) -> None:
        auto_focus_enabled = (
            get_global_config_subscriber().get_config_node("aimbot", "enable_auto_focus").get_bool_value()
        )

        if auto_focus_enabled:
            LOG.info("Single auto focus not triggered. Constant autofocus enabled.")
            return

        with self._lock:
            self._aimbot_client.lens_auto_focus_from_sync(self._scanner_id)

    def get_focus(self) -> float:
        with self._lock:
            return self._aimbot_client.lens_get_from_sync(self._scanner_id)

    def set_focus(self, focus: float) -> None:
        auto_focus_enabled = (
            get_global_config_subscriber().get_config_node("aimbot", "enable_auto_focus").get_bool_value()
        )

        if auto_focus_enabled:
            LOG.info("Lens focus value not set. Autofocus enabled.")
            return

        focus_range = self.focus_range()
        assert focus_range[0] <= focus <= focus_range[1]
        with self._lock:
            self._aimbot_client.lens_set_from_sync(self._scanner_id, int(focus))
        # liquid lens takes some time to execute adjustment
        time.sleep(0.1)

        new = self.get_focus()
        # Ensure the board is setting focus correctly
        assert new == focus

    def get_id(self) -> str:
        return self._ref_id

    def focus_range(self) -> Tuple[float, float]:
        return (0, 255)

    def focus_step_sizes(self) -> List[float]:
        return [20, 10, 5, 1]

import threading
import time
from typing import Any, <PERSON>, Tuple, cast

import lib.common.logging
from core.cv.retina.lens import LensNode
from lib.common.devices.boards.pulczar.pulczar_board_device import PulczarBoardDevice
from lib.common.devices.registry import DeviceRegistry

LOG = lib.common.logging.get_logger(__name__)


class PulczarBoardLens(LensNode):
    def __init__(
        self, *args: Any, device_id: str, device_registry: DeviceRegistry, **kwargs: Any,
    ):
        super().__init__(**kwargs)

        self._ref_id: str = cast(str, kwargs.get("ref_id"))
        self._device_registry = device_registry
        self._device_id = device_id
        self._board = device_registry.get_device_from_sync(PulczarBoardDevice, device_id)
        self._lock = threading.Lock()

        # Attempt to set focus to test board
        self.set_focus(self.get_focus())

    def get_focus(self) -> float:
        with self._lock:
            return self._board.lens_get_from_sync()

    def set_focus(self, focus: float) -> None:
        focus_range = self.focus_range()
        assert focus_range[0] <= focus <= focus_range[1]
        with self._lock:
            self._board.lens_set_from_sync(int(focus))
        # liquid lens takes some time to execute adjustment
        time.sleep(0.1)

        new = self.get_focus()
        # Ensure the board is setting focus correctly
        assert new == focus

    def get_id(self) -> str:
        return self._ref_id

    def focus_range(self) -> Tuple[float, float]:
        return (0, 255)

    def focus_step_sizes(self) -> List[float]:
        return [20, 10, 5, 1]

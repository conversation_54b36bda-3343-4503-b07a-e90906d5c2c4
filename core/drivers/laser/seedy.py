from typing import Any, Dict

from core.controls.exterminator.model.laser import Laser
from lib.drivers.maka.seedy.board import CypressBoard


class CypressLaser(Laser):
    def __init__(self, *argv: Any, serial_device: str, laser_no: int, **kwargs: Dict[str, Any]):
        super().__init__(*argv, **kwargs)
        assert serial_device is not None
        self._board = CypressBoard.by_serial(serial_device)
        assert laser_no is not None
        self._laser_no = laser_no
        self.disarm()

    def arm(self) -> None:
        super().arm()
        self._board.set_armed(True)

    def disarm(self) -> None:
        super().disarm()
        self._board.set_armed(False)

    def on(self) -> None:
        self._board.set_laser(self._laser_no, True)

    def off(self) -> None:
        super().off()
        self._board.set_laser(self._laser_no, False)

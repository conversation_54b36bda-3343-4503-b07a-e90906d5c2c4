import contextlib
import os
from dataclasses import dataclass
from io import Text<PERSON><PERSON>rapper
from typing import Generator, Optional

from dataclass_wizard import JSONWizard

import lib.common.logging
from lib.common.file import open_file_descriptor

LOG = lib.common.logging.get_logger(__name__)

MODULE_IDENTITY_FILE = "/bot/module_identity.json"
PERMISSIONS = 0o664


@dataclass
class ModuleIdentity(JSONWizard):
    id: int
    serial: str

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, ModuleIdentity):
            # don't attempt to compare against unrelated types
            return NotImplemented

        return self.id == other.id and self.serial == other.serial


def load_module_identity() -> Optional[ModuleIdentity]:
    try:
        with open(MODULE_IDENTITY_FILE, "r") as f:
            data = f.read()
    except FileNotFoundError:
        LOG.error(f"Module identity file not found: {MODULE_IDENTITY_FILE}")
        return None
    except Exception as e:
        LOG.error(f"Error loading module identity: {e}")
        return None
    result = ModuleIdentity.from_json(data)
    assert isinstance(result, ModuleIdentity)
    return result


@contextlib.contextmanager
def atomic_file(file_path: str) -> Generator[TextIOWrapper, None, None]:
    temp_file_path = file_path + ".tmp"
    with open(temp_file_path, "w") as temp_file:
        yield temp_file
        temp_file.flush()
        os.fsync(temp_file.fileno())
    os.rename(temp_file_path, file_path)
    os.chmod(file_path, PERMISSIONS)

    # Ensure the file update is persisted to disk
    with open_file_descriptor(file_path, os.O_RDWR | os.O_DIRECT) as fd:
        os.fsync(fd)

    # Ensure the directory update is persisted to disk
    with open_file_descriptor(os.path.dirname(file_path) or ".", os.O_DIRECTORY) as fd:
        os.fsync(fd)


def save_module_identity(identity: ModuleIdentity) -> bool:
    try:
        par = os.path.dirname(MODULE_IDENTITY_FILE)
        os.makedirs(par, exist_ok=True)
        with atomic_file(MODULE_IDENTITY_FILE) as f:
            f.write(f"{identity.to_json()}\n")
    except Exception as e:
        LOG.error(f"Error saving module identity: {e}")
        return False
    return True

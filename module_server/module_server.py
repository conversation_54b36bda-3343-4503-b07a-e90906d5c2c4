import asyncio
import uuid
from copy import deepcopy
from pathlib import Path
from typing import Optional

import grpc.aio

import generated.hardware_manager.proto.hardware_manager_service_pb2 as hardware_manager_pb
import generated.proto.module.server.server_pb2 as module_server_pb
import generated.proto.module.server.server_pb2_grpc as module_server_grpc
import generated.proto.module.types.types_pb2 as module_types_pb
import lib.common.logging
import lib.drivers
import lib.drivers.nanopb
from hardware_manager.python.types import ModulePcSensorData
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.commander.client import CommanderClient
from lib.common.generation import is_reaper
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_oob_connector import (
    ReaperModuleOobConnector,
    get_board,
)
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_types import (
    ModuleIdentityConfig as MCBIdentity,
)
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_types import (
    NetworkAddress,
    NetworkAddressSource,
    NetworkState,
)
from lib.drivers.nanopb.reaper_module_controller.serial_coredump_receiver import CoredumpReceiver as McbCoredumpReceiver
from lib.drivers.serial.serial_connector import SerialConnector
from module_server.identity import ModuleIdentity, load_module_identity, save_module_identity
from module_server.module_network import (
    DHCP_IP_PREFIX,
    STATIC_IP_PREFIX,
    GlobalInterfaceAddresses,
    configure_network,
    get_ipmi_address,
)
from module_server.sensors import SensorReader

COMMANDER_ADDR = "*********"
MCB_SERIAL_PORT = "/dev/ttyS0"

HEARTBEAT_INTERVAL = 1
DATA_REFRESH_INTERVAL = 300
BOARD_BOOT_TIMEOUT = 10
NUM_RETRIES = 6
RETRY_TIMEOUT = 0.5

UNASSIGNED_MODULE_ID = 0
UNSET_SERIAL_PREFIX = "UNSET_"

HEARTBEAT_LOG_INTERVAL = 60

LOG = lib.common.logging.get_logger(__name__)


class ModuleOps:
    def __init__(self) -> None:
        self._ops_lock = asyncio.Lock()
        self._data_lock = asyncio.Lock()
        self._booted: asyncio.Event = asyncio.Event()
        self._board: Optional[ReaperModuleOobConnector] = None
        self._module_identity_in_mem: ModuleIdentity = ModuleIdentity(id=0, serial="")
        self._module_identity_in_disk: Optional[ModuleIdentity] = None
        self._mcb_identity: Optional[MCBIdentity] = None
        self._mcb_network_interface_config: Optional[NetworkState] = None
        self._mcb_dhcp_address: Optional[NetworkAddress] = None
        self._ipmi_dhcp_address: Optional[str] = None
        self._global_interface_addresses: Optional[GlobalInterfaceAddresses] = None
        self._commander_client = CommanderClient(hostname=COMMANDER_ADDR)
        self._sensor_reader = SensorReader()
        self._sensor_data: Optional[ModulePcSensorData] = None
        self._coredump: Optional[McbCoredumpReceiver] = None

    @property
    def booted(self) -> bool:
        return self._booted.is_set()

    @property
    def sensor_data(self) -> Optional[ModulePcSensorData]:
        return self._sensor_data

    async def boot(self) -> None:
        while self._board is None:
            try:
                self._board = await get_board(MCB_SERIAL_PORT)
            except Exception as e:
                LOG.error(f"Error connecting to board, retrying after {BOARD_BOOT_TIMEOUT} seconds: {e}")
                await asyncio.sleep(BOARD_BOOT_TIMEOUT)

        self._coredump = McbCoredumpReceiver(self._board, Path("/data/logs/"))

        module_identity = load_module_identity()
        if module_identity:
            self._module_identity_in_mem = module_identity
            if self._module_identity_in_mem.serial == "":
                LOG.info("Module identity found but missing serial number, creating new identity")
                self._module_identity_in_mem = ModuleIdentity(
                    id=UNASSIGNED_MODULE_ID, serial=UNSET_SERIAL_PREFIX + str(uuid.uuid4())
                )
                if not save_module_identity(self._module_identity_in_mem):
                    raise Exception("Error saving module identity")
        else:
            LOG.info("No module identity found, creating new identity")
            self._module_identity_in_mem = ModuleIdentity(
                id=UNASSIGNED_MODULE_ID, serial=UNSET_SERIAL_PREFIX + str(uuid.uuid4())
            )
            if not save_module_identity(self._module_identity_in_mem):
                raise Exception("Error saving module identity")

        self._booted.set()

    async def get_mcb_identity(self) -> Optional[MCBIdentity]:
        try:
            if not self._board:
                raise Exception("Board not initialized")
            return await self._board.get_module_identity()
        except Exception as e:
            LOG.error(f"Error getting module identity: {e}")
            return None

    async def get_mcb_network_interface_config(self) -> Optional[NetworkState]:
        try:
            if not self._board:
                raise Exception("Board not initialized")
            return await self._board.get_network_interface_config()
        except Exception as e:
            LOG.error(f"Error getting network config: {e}")
            return None

    def parse_mcb_dhcp_address(self, config: Optional[NetworkState]) -> Optional[NetworkAddress]:
        if config and config.addresses:
            # Guaranteed to have at most one DHCP address
            for addr in config.addresses:
                if addr.provenance == NetworkAddressSource.DHCP:
                    return addr
        return None

    async def set_mcb_identity(self, mcb_identity: MCBIdentity) -> Optional[MCBIdentity]:
        try:
            if not self._board:
                raise Exception("Board not initialized")
            await self._board.set_module_identity(mcb_identity)
        except Exception as e:
            LOG.error(f"Error setting MCB identity to {mcb_identity}: {e}")
            return None
        LOG.info(f"Set MCB identity to {mcb_identity}")
        return mcb_identity

    async def sensor_loop(self) -> None:
        await self._booted.wait()

        while not bot_stop_handler.stopped:
            try:
                self._sensor_data = await self._sensor_reader.get()
            except Exception as e:
                LOG.error(f"Failed to read module sensor data: {e}")

            # TODO: make this configurable?
            await asyncio.sleep(10)

    # flake8: noqa: C901
    async def heartbeat_loop(self) -> None:

        await self._booted.wait()

        heartbeat_count = 0
        while not bot_stop_handler.stopped:
            async with self._data_lock:
                heartbeat_count += 1
                if heartbeat_count % DATA_REFRESH_INTERVAL == 0:
                    # Reset data to force refresh
                    self._module_identity_in_disk = None
                    self._mcb_identity = None
                    self._mcb_network_interface_config = None
                    self._mcb_dhcp_address = None
                    self._ipmi_dhcp_address = None
                    self._global_interface_addresses = None

                # Get copies of the data for lock free operations
                module_identity_in_mem = deepcopy(self._module_identity_in_mem)
                module_identity_in_disk = deepcopy(self._module_identity_in_disk)

                mcb_identity = deepcopy(self._mcb_identity)
                mcb_network_interface_config = deepcopy(self._mcb_network_interface_config)
                mcb_dhcp_address = deepcopy(self._mcb_dhcp_address)
                ipmi_dhcp_address = deepcopy(self._ipmi_dhcp_address)
                global_interface_addresses = deepcopy(self._global_interface_addresses)

            # pull states if necessary
            if not module_identity_in_disk:
                module_identity_in_disk = load_module_identity()

            if not mcb_identity:
                mcb_identity = await self.get_mcb_identity()

            if not mcb_network_interface_config:
                mcb_network_interface_config = await self.get_mcb_network_interface_config()

            if not mcb_dhcp_address:
                mcb_dhcp_address = self.parse_mcb_dhcp_address(mcb_network_interface_config)

            if not ipmi_dhcp_address:
                ipmi_dhcp_address = get_ipmi_address()

            if not global_interface_addresses:
                global_interface_addresses = GlobalInterfaceAddresses()

            # verify/update states
            if not module_identity_in_disk or module_identity_in_mem != module_identity_in_disk:
                if save_module_identity(module_identity_in_mem):
                    module_identity_in_disk = module_identity_in_mem

            if not mcb_identity or mcb_identity.module != module_identity_in_mem.id:
                mcb_identity = await self.set_mcb_identity(MCBIdentity(module=module_identity_in_mem.id))

            # verify the global interface the correct static address for the id
            if not global_interface_addresses or not global_interface_addresses.verify_static_address_for_id(
                module_identity_in_mem.id
            ):
                network_warn_msg = f"""               
                Global interface address not set or incorrect, reconfiguring network. 
                Able to read global interface: {"Yes" if global_interface_addresses else "No"}
                DHCP address in {DHCP_IP_PREFIX}: {"Missing" if not global_interface_addresses or not global_interface_addresses.dhcp_address else global_interface_addresses.dhcp_address }
                Correct static address for id {module_identity_in_mem.id}: {"No" if not global_interface_addresses or not global_interface_addresses.verify_static_address_for_id(module_identity_in_mem.id) else "Yes"}
                """
                LOG.warn(network_warn_msg)
                if configure_network(module_identity_in_mem.id):
                    # post configure the fetch call can take some time to return the right result
                    LOG.info("Network configured, waiting for network to stabilize")
                    retry_count = 0
                    network_correct = False
                    while retry_count < NUM_RETRIES:
                        LOG.info(f"Fetch configuration retry {retry_count}")
                        await asyncio.sleep(RETRY_TIMEOUT)
                        retry_count += 1
                        global_interface_addresses = GlobalInterfaceAddresses()
                        if global_interface_addresses.verify_static_address_for_id(module_identity_in_mem.id):
                            network_correct = True
                            break
                    if network_correct:
                        LOG.info("Network configured successfully")
                        # close and recreate the commander client to force a TCP reconnect on new IP
                        await self._commander_client.close()
                        self._commander_client = CommanderClient(hostname=COMMANDER_ADDR)
                    else:
                        LOG.error("Unable to configure network, will retry, check network configuration")
                        global_interface_addresses = None

                else:
                    LOG.error("Unable to configure network, will retry, check network configuration")
                    global_interface_addresses = None

            # write back the states states
            async with self._data_lock:
                self._module_identity_in_disk = module_identity_in_disk
                self._mcb_identity = mcb_identity
                self._mcb_network_interface_config = mcb_network_interface_config
                self._mcb_dhcp_address = mcb_dhcp_address
                self._ipmi_dhcp_address = ipmi_dhcp_address
                self._global_interface_addresses = global_interface_addresses

            if heartbeat_count % HEARTBEAT_LOG_INTERVAL == 1:
                debug_info = f"""
                -- DEBUG INFO --
                Module Identity: {module_identity_in_mem.id if module_identity_in_mem else 'None'}
                Module Serial Number: {module_identity_in_mem.serial if module_identity_in_mem else 'None'}
                Global Interface Addresses: {global_interface_addresses}
                Module DHCP Address: {global_interface_addresses.dhcp_address if global_interface_addresses else 'None'}
                IPMI DHCP Address: {ipmi_dhcp_address if ipmi_dhcp_address else 'None'}
                MCB ID: {mcb_identity.module if mcb_identity else "None"}
                MCB DHCP Address: {str(mcb_dhcp_address.unicast) if mcb_dhcp_address else 'None'}
                MCB Network Interface Config: {mcb_network_interface_config}
                """
                LOG.info(debug_info)

            try:
                await self._commander_client.Heartbeat(
                    id=module_identity_in_mem.id if module_identity_in_mem else 0,
                    serial=module_identity_in_mem.serial if module_identity_in_mem else "",
                    pc_ip=global_interface_addresses.dhcp_address
                    if global_interface_addresses and global_interface_addresses.dhcp_address
                    else "",
                    ipmi_ip=ipmi_dhcp_address if ipmi_dhcp_address else "",
                    mcb_ip=str(mcb_dhcp_address.unicast) if mcb_dhcp_address else "",
                )
            except Exception as e:
                LOG.error(f"Error sending heartbeat to commander: {e}")

            await asyncio.sleep(HEARTBEAT_INTERVAL)

    async def get_module_identity(self) -> ModuleIdentity:
        async with self._ops_lock:
            async with self._data_lock:
                if not self._module_identity_in_mem:
                    raise Exception("Module identity is not initialized")
                return deepcopy(self._module_identity_in_mem)

    async def set_module_serial(self, serial: str, force: bool) -> None:
        async with self._ops_lock:
            async with self._data_lock:
                if not self._module_identity_in_mem:
                    raise Exception("Module identity is not initialized")
                if (
                    self._module_identity_in_mem.serial != ""
                    and not self._module_identity_in_mem.serial.startswith(UNSET_SERIAL_PREFIX)
                    and self._module_identity_in_mem.serial != serial
                    and not force
                ):
                    # serial is not empty, not unset, and not equal to the new serial
                    # and force is not set, then raise an exception
                    raise Exception("Serial already set")
                elif (
                    force
                    or self._module_identity_in_mem.serial == ""
                    or self._module_identity_in_mem.serial.startswith(UNSET_SERIAL_PREFIX)
                ):
                    self._module_identity_in_mem.serial = serial

    async def set_module_identity(self, identity: ModuleIdentity) -> None:
        async with self._ops_lock:
            async with self._data_lock:
                if not self._module_identity_in_mem:
                    raise Exception("Module identity is not initialized")
                elif self._module_identity_in_mem.serial == "":
                    raise Exception("Module serial number not set")
                elif self._module_identity_in_mem.serial != identity.serial:
                    raise Exception("Serial number mismatch")
                self._module_identity_in_mem.id = identity.id


class ModuleGRPCServicer(module_server_grpc.ModuleServerServiceServicer):
    def __init__(self, ops: ModuleOps) -> None:
        self._ops = ops

    async def GetModuleIdentity(
        self, request: module_types_pb.Empty, context: grpc.ServicerContext
    ) -> module_server_pb.GetModuleIdentityResponse:
        LOG.info(f"GetModuleIdentity: {request}")
        if not self._ops.booted:
            raise Exception("Module server operations not booted")
        identity = await self._ops.get_module_identity()
        return module_server_pb.GetModuleIdentityResponse(
            identity=module_types_pb.ModuleIdentity(id=identity.id, serial=identity.serial)
        )

    async def SetModuleSerialNumber(
        self, request: module_server_pb.SetModuleSerialNumberRequest, context: grpc.ServicerContext
    ) -> module_types_pb.Empty:
        LOG.info(f"SetModuleSerialNumber: {request}")
        if not self._ops.booted:
            raise Exception("Module server operations not booted")
        await self._ops.set_module_serial(request.serial_number, request.force)
        return module_types_pb.Empty()

    async def SetModuleIdentity(
        self, request: module_server_pb.SetModuleIdentityRequest, context: grpc.ServicerContext
    ) -> module_types_pb.Empty:
        LOG.info(f"SetModuleIdentity: {request}")
        if not self._ops.booted:
            raise Exception("Module server operations not booted")
        await self._ops.set_module_identity(ModuleIdentity(id=request.identity.id, serial=request.identity.serial))
        return module_types_pb.Empty()

    async def GetModuleSensors(
        self, request: module_types_pb.Empty, context: grpc.ServicerContext
    ) -> hardware_manager_pb.ReaperPcSensorData:
        if not self._ops.booted:
            raise Exception("Module server operations not booted")

        data = self._ops.sensor_data

        if not data:
            raise Exception("No module sensor data available")

        return data.to_message()


class ModuleServer:
    def __init__(self, event_loop: asyncio.AbstractEventLoop, port: int = 61016,) -> None:
        self._loop = event_loop
        self._ops = ModuleOps()
        self._server = grpc.aio.server()
        self._servicer = ModuleGRPCServicer(self._ops)
        self._port = port
        self._server.add_insecure_port(f"[::]:{self._port}")
        module_server_grpc.add_ModuleServerServiceServicer_to_server(self._servicer, self._server)

        self._heartbeat_task: Optional[asyncio.Task[None]] = None
        self._sensor_task: Optional[asyncio.Task[None]] = None
        self._coredump_task: Optional[asyncio.Task[None]] = None

    async def run(self) -> None:
        if is_reaper():
            bot_stop_handler.add_callback(self.shutdown)
            LOG.info("Starting Module Server")
            await self._ops.boot()
            LOG.info("Booted ops")
            await self._server.start()
            LOG.info(f"Server started at 0.0.0.0:{self._port}")

            self._heartbeat_task = asyncio.get_event_loop().create_task(self._ops.heartbeat_loop())
            self._sensor_task = asyncio.get_event_loop().create_task(self._ops.sensor_loop())

            assert self._ops._coredump
            self._coredump_task = await self._ops._coredump.start()

            await self._heartbeat_task
            await self._sensor_task
            await self._coredump_task

            await self._server.wait_for_termination()
        else:
            LOG.error("Module Server is only supported on Reaper")

    async def stop_tasks(self) -> None:
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
            await self._heartbeat_task
            self._heartbeat_task = None

        if self._sensor_task:
            self._sensor_task.cancel()
            await self._sensor_task
            self._sensor_task = None

        if self._coredump_task:
            self._coredump_task.cancel()
            await self._coredump_task
            self._coredump_task = None

        await self._server.stop(0)
        LOG.info("Module Server stopped")

    def shutdown(self) -> None:
        asyncio.run_coroutine_threadsafe(self.stop_tasks(), self._loop)

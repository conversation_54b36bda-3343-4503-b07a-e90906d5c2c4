"""
Module sensor readout support

Handles reading out the values of various hardware sensors on the module PC. They are aggregated
for later retrieval by an RPC call from command.
"""

import asyncio
import datetime
import os
import re
from typing import Dict, <PERSON>ple

import psutil
from psutil._common import snicstats

from hardware_manager.python.types import EthernetLinkState, LinkSpeed, ModulePcSensorData
from lib.common.logging import get_logger, init_log
from lib.common.process.commands import run_command_with_output
from lib.common.units.duration import Duration
from lib.common.units.temperature import Temperature
from lib.common.units.voltage import Voltage

LOG = get_logger(__name__)

# Mapping of psutil link state (duplex, speed) -> our own enum value
LINK_SPEED_MAP: Dict[Tuple[int, int], LinkSpeed] = {
    (psutil.NIC_DUPLEX_HALF, 10): LinkSpeed.SPEED_10M_HALF,
    (psutil.NIC_DUPLEX_FULL, 10): LinkSpeed.SPEED_10M_FULL,
    (psutil.NIC_DUPLEX_HALF, 100): LinkSpeed.SPEED_100M_HALF,
    (psutil.NIC_DUPLEX_FULL, 100): LinkSpeed.SPEED_100M_FULL,
    (psutil.NIC_DUPLEX_FULL, 1000): LinkSpeed.SPEED_1G_FULL,
    (psutil.NIC_DUPLEX_FULL, 2500): LinkSpeed.SPEED_2G5_FULL,
    (psutil.NIC_DUPLEX_FULL, 5000): LinkSpeed.SPEED_5G_FULL,
    (psutil.NIC_DUPLEX_FULL, 10000): LinkSpeed.SPEED_10G_FULL,
}

# Expected link speeds by interface name
EXPECTED_LINK_SPEEDS: Dict[str, LinkSpeed] = {
    # NOTE: what is the expected link speed for `ext` on a real machine - probably disconnected?
    "ext": LinkSpeed.SPEED_1G_FULL,
    "global": LinkSpeed.SPEED_1G_FULL,
    "predict1": LinkSpeed.SPEED_2G5_FULL,
    "scanner1": LinkSpeed.SPEED_100M_FULL,
    "target1": LinkSpeed.SPEED_1G_FULL,
    "scanner2": LinkSpeed.SPEED_100M_FULL,
    "target2": LinkSpeed.SPEED_1G_FULL,
}


class SensorReader:
    """
    Handles reading all sensors on module PC

    This calls ipmitool and parses the output; most of the sensors aren't actually exposed to
    Linux due to driver support (or rather, lack thereof)
    """

    async def _get_ipmi_sensors(self) -> Dict[str, float]:
        """
        Invoke ipmitool to get all IPMI sensors.
        """
        _, sensors = await run_command_with_output(["ipmitool", "-c", "sdr", "list"])

        # split into sensor name, sensor value
        exp = re.compile(r"^([^,]+)(?:,)([^,]+)", re.MULTILINE)
        result = exp.findall(sensors)

        return {name: float(value) for name, value in result if value.replace(".", "", 1).isnumeric()}

    def _convert_link_state(self, input: snicstats, expected_speed: LinkSpeed) -> EthernetLinkState:
        in_speed = (input.duplex, input.speed)
        actual_speed = LINK_SPEED_MAP.get(in_speed, LinkSpeed.UNKNOWN)

        return EthernetLinkState(link_up=input.isup, link_speed_expected=expected_speed, link_speed_actual=actual_speed)

    async def get(self) -> ModulePcSensorData:
        # fetch all of the data
        ipmi_sensors = await self._get_ipmi_sensors()

        nic_data = psutil.net_if_stats()
        nic_data_formatted = {
            ifname: self._convert_link_state(state, EXPECTED_LINK_SPEEDS.get(ifname, LinkSpeed.UNKNOWN))
            for ifname, state in nic_data.items()
        }

        # TODO: specify different root path for disk space (data drive)?
        cpu_load_1, _, _ = os.getloadavg()
        disk_usage = psutil.disk_usage("/")
        memory = psutil.virtual_memory()

        boot_time = datetime.datetime.fromtimestamp(psutil.boot_time())
        now_time = datetime.datetime.now()
        uptime = (now_time - boot_time).total_seconds()

        # placeholder for a link that's unavailable/down
        link_down = EthernetLinkState(
            link_up=False, link_speed_actual=LinkSpeed.UNKNOWN, link_speed_expected=LinkSpeed.UNKNOWN
        )

        # then format it
        return ModulePcSensorData(
            cpu_core_temp=Temperature.from_c(ipmi_sensors.get("CPU Temp", 0)),
            system_temp=Temperature.from_c(ipmi_sensors.get("System Temp", 0)),
            gpu_1_temp=Temperature.from_c(ipmi_sensors.get("GPU1 Temp", 0)),
            # XXX: I have no idea why the second GPU shows up as GPU 3
            gpu_2_temp=Temperature.from_c(ipmi_sensors.get("GPU3 Temp", 0)),
            psu_12v=Voltage.from_volts(ipmi_sensors.get("12V", 0)),
            psu_5v=Voltage.from_volts(ipmi_sensors.get("5VCC", 0)),
            psu_3v3=Voltage.from_volts(ipmi_sensors.get("3.3VCC", 0)),
            uptime=Duration.from_seconds(uptime),
            cpu_load=cpu_load_1,
            ram_usage=memory.percent,
            disk_usage=disk_usage.percent,
            scanner_link=(nic_data_formatted.get("scanner1", link_down), nic_data_formatted.get("scanner2", link_down)),
            target_link=(nic_data_formatted.get("target1", link_down), nic_data_formatted.get("target2", link_down)),
            predict_link=nic_data_formatted.get("predict1", link_down),
            # TODO: can we get IPMI link state somehow?
            ipmi_link=link_down,
            ext_link=nic_data_formatted.get("ext", link_down),
            global_link=nic_data_formatted.get("global", link_down),
        )


# When run as standalone, just print the data over and over again
async def main() -> None:
    LOG.info("Starting sensor reader…")
    poller = SensorReader()

    while True:
        data = await poller.get()
        LOG.info(f"Got sensor data: {data}")
        await asyncio.sleep(10)


if __name__ == "__main__":
    init_log(level="DEBUG")

    loop = asyncio.get_event_loop()
    loop.run_until_complete(main())

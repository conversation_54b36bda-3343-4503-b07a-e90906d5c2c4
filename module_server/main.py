"""
Module Server

Currently, this module is a simple test script to test the communication with the Reaper Module Controller Board (MCB) over serial.

This server is responsible for collecting data from the MCB, IPMI and PC, and heartbeating to the command computer.
The server will also be responsible for configuring the MCB and the PC.
"""

import asyncio
import logging
import sys

import lib.common.logging
import lib.drivers
import lib.drivers.nanopb
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.tasks.manager import get_event_loop_by_name
from module_server.module_server import ModuleServer

LOG = lib.common.logging.get_logger(__name__)


async def main() -> None:
    try:
        server = ModuleServer(asyncio.get_event_loop())
        await server.run()
    except Exception as e:
        bot_stop_handler.exit_with_exception(e)
    else:
        bot_stop_handler.exit_gracefully()


if __name__ == "__main__":
    lib.common.logging.logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s.%(msecs)03d %(levelname)-s [%(module)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    LOG.info("Starting Module Server")
    loop = get_event_loop_by_name()
    asyncio.run_coroutine_threadsafe(main(), loop)
    bot_stop_handler.ready_for_termination_event.wait()
    sys.exit(bot_stop_handler.error_code)

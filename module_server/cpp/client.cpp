#include "module_server/cpp/client.hpp"
#include "lib/common/cpp/exceptions.h"

#include <exception>
#include <grpcpp/grpcpp.h>

namespace carbon::module_server::client {

void ModuleServerClient::reset() { this->reset_stub(); }

std::tuple<uint32_t, std::string> ModuleServerClient::GetModuleIdentity() {
  grpc::ClientContext context;
  module::types::Empty req;
  module::server::GetModuleIdentityResponse resp;
  std::shared_ptr<module::server::ModuleServerService::Stub> stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(
      std::bind(&module::server::ModuleServerService::Stub::GetModuleIdentity, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw maka_error("Failed to get module identity");
  }
  auto identity = resp.identity();
  return std::tuple<uint32_t, std::string>(identity.id(), identity.serial());
}

grpc::Status ModuleServerClient::exec_grpc(std::function<grpc::Status()> func) {
  try {
    return func();
  } catch (const std::exception &ex) {
    this->reset_stub();
    throw maka_error(ex.what());
  }
}
std::shared_ptr<module::server::ModuleServerService::Stub> ModuleServerClient::get_grpc_stub() {
  if (this->channel_ == nullptr) {
    this->channel_ = grpc::CreateChannel(this->addr_, grpc::InsecureChannelCredentials());
  }
  if (this->stub_ == nullptr) {
    this->stub_ = std::make_shared<module::server::ModuleServerService::Stub>(this->channel_);
  }
  return this->stub_;
}

void ModuleServerClient::reset_stub() {
  this->stub_ = nullptr;
  this->channel_ = nullptr;
}

} // namespace carbon::module_server::client
#pragma once

#include <generated/proto/module/server/server.grpc.pb.h>
#include <generated/proto/module/server/server.pb.h>
#include <generated/proto/module/types/types.pb.h>
#include <grpcpp/grpcpp.h>
#include <memory>

namespace carbon::module_server::client {

class ModuleServerClient {
public:
  ModuleServerClient(const std::string &addr = "localhost:61016") : addr_(addr), channel_(nullptr), stub_(nullptr){};

  void reset();
  std::tuple<uint32_t, std::string> GetModuleIdentity();

private:
  grpc::Status exec_grpc(std::function<grpc::Status()> func);
  std::shared_ptr<module::server::ModuleServerService::Stub> get_grpc_stub();
  void reset_stub();

  std::string addr_;
  std::shared_ptr<grpc::Channel> channel_;
  std::shared_ptr<module::server::ModuleServerService::Stub> stub_;
};

} // namespace carbon::module_server::client
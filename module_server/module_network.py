import json
import subprocess
from typing import Dict, List, Optional, cast

import lib.common.logging

NETWORK_TOOL = "/carbon/sbin/carbon-module-network"

DHCP_IP_PREFIX = "10.10.1."
STATIC_IP_PREFIX = "10.10.20."


LOG = lib.common.logging.get_logger(__name__)


def execute_network_tool_command(command: str, *args: str) -> Optional[bytes]:
    try:
        result = subprocess.run([NETWORK_TOOL, command, *args], check=True, capture_output=True)
    except subprocess.CalledProcessError as e:
        err_str = str(e.output, "utf-8")
        LOG.error(f"Error executing network tool command: {err_str}")
        return None
    except FileNotFoundError as e:
        LOG.error(f"Network tool not found: {e}")
        return None
    except Exception as e:
        LOG.error(f"Error executing network tool command: {e}")
        return None
    return result.stdout


def configure_network(id: int) -> bool:
    result = execute_network_tool_command("configure", "--id", str(id))
    if result is None:
        return False
    return True


def get_ipmi_address() -> Optional[str]:
    result = execute_network_tool_command("ipmi-address")
    if result is None:
        return None
    out = cast(Dict[str, str], json.loads(result))
    if "IP Address" not in out:
        return None
    return out["IP Address"]


def get_global_interface_addresses() -> Optional[List[str]]:
    result = execute_network_tool_command("global-interface-addresses")
    if result is None:
        return None
    return cast(List[str], json.loads(result))


class GlobalInterfaceAddresses:
    def __init__(self) -> None:
        self.addresses: Optional[List[str]] = get_global_interface_addresses()
        self.dhcp_address: Optional[str] = self.get_module_dhcp_address()
        self.static_address: Optional[str] = self.get_module_static_address()

    def __str__(self) -> str:
        return f"{self.addresses}"

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, GlobalInterfaceAddresses):
            return NotImplemented
        return self.dhcp_address == other.dhcp_address and self.static_address == other.static_address

    def get_module_dhcp_address(self) -> Optional[str]:
        if self.addresses is not None:
            for addr in self.addresses:
                if addr.startswith(DHCP_IP_PREFIX):
                    return addr
        return None

    def get_module_static_address(self) -> Optional[str]:
        if self.addresses is not None:
            for addr in self.addresses:
                if addr.startswith(STATIC_IP_PREFIX):
                    return addr
        return None

    def verify_static_address_for_id(self, id: int) -> bool:
        # id of 0 should have no static ip
        if not self.static_address:
            return id == 0
        return self.static_address == f"{STATIC_IP_PREFIX}{id}"

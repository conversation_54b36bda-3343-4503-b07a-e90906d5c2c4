from typing import Any, Optional, <PERSON><PERSON>

import numpy as np
import numpy.typing as npt

from cv.optical_flow.optical_flow_utils import calculate_maps
from lib.common.geometric.geometric_device import GeometricDevice
from lib.common.geometric.height_estimator import HeightEstimator


class GeometricCamUndistorter:
    def __init__(
        self,
        u2d_mapx: npt.NDArray[Any],
        u2d_mapy: npt.NDArray[Any],
        d2u_mapx: npt.NDArray[Any],
        d2u_mapy: npt.NDArray[Any],
    ) -> None:
        self._u2d_mapx = u2d_mapx
        self._u2d_mapy = u2d_mapy
        self._d2u_mapx = d2u_mapx
        self._d2u_mapy = d2u_mapy

    def undistort_point(self, point: Tuple[float, float]) -> <PERSON><PERSON>[float, float]:
        return self._d2u_mapx[round(point[1]), round(point[0])], self._d2u_mapy[round(point[1]), round(point[0])]


class GeometricCam(GeometricDevice):
    """
    Frame Cam Position is located at the center outside tip of the Lens Bulb
    """

    def __init__(
        self,
        name: str,
        abs_position_mm: Tuple[float, float, float],
        offset_position_mm: Tuple[float, float, float],
        height_estimator: HeightEstimator,
        resolution: Tuple[int, int],
        focal_length_mm: float,
        top_to_vertex_mm: float,
        sensor_dimensions_mm: Tuple[float, float],
        undistorted: bool = False,
    ) -> None:
        super().__init__(name, abs_position_mm, offset_position_mm)
        self._height_estimator = height_estimator
        self._resolution = resolution
        self._focal_length_mm = focal_length_mm
        self._top_to_vertex_mm = top_to_vertex_mm
        self._sensor_dimensions_mm = sensor_dimensions_mm
        self._undistorted = undistorted

        if self._undistorted:
            u2d_mapx, u2d_mapy, d2u_mapx, d2u_mapy = calculate_maps(
                self._name, self._resolution[0], self._resolution[1]
            )
            self._undistorter: Optional[GeometricCamUndistorter] = GeometricCamUndistorter(
                u2d_mapx, u2d_mapy, d2u_mapx, d2u_mapy
            )
        else:
            self._undistorter = None

    @property
    def abs_position_mm(self) -> Tuple[float, float, float]:
        return super(GeometricCam, self).abs_position_mm

    def _get_vertex_height(self) -> float:
        return (
            self._height_estimator.ground_position_z_mm
            - (self._abs_position_mm[2] + self._offset_position_mm[2])
            + self._top_to_vertex_mm
        )

    def _get_sensor_mm_from_px(self, pos: Tuple[float, float]) -> Tuple[float, float]:
        return (
            (((pos[0] / self._resolution[0]) - 0.5) * self._sensor_dimensions_mm[0]),
            (((pos[1] / self._resolution[1]) - 0.5) * self._sensor_dimensions_mm[1]),
        )

    def get_sensor_mm_from_centered_px(self, pos: Tuple[float, float]) -> Tuple[float, float]:
        return (
            ((pos[0] / self._resolution[0]) * self._sensor_dimensions_mm[0]),
            ((pos[1] / self._resolution[1]) * self._sensor_dimensions_mm[1]),
        )

    def compute_vertex_height(
        self, p1_px: Tuple[float, float], p2_px: Tuple[float, float], distance_mm: float
    ) -> float:
        """
        This method is used to calibrate the right bulb_to_vertex value
        """
        centered_sensor_mm = self.get_sensor_mm_from_centered_px(np.array(p1_px) - np.array(p2_px))
        sensor_distance_mm = float(np.linalg.norm(np.array(centered_sensor_mm)))
        return distance_mm * self._focal_length_mm / sensor_distance_mm

    def get_abs_position_from_px(self, pos: Tuple[float, float]) -> Tuple[float, float, float]:
        if self._undistorter is not None:
            pos = self._undistorter.undistort_point(pos)
        return self.get_abs_position_from_undistorted_px(pos)

    def get_abs_position_from_undistorted_px(self, pos: Tuple[float, float]) -> Tuple[float, float, float]:
        height_ratio = self._get_vertex_height() / self._focal_length_mm
        sensor_pos_mm = self._get_sensor_mm_from_px(pos)
        relative_ground_pos_mm = sensor_pos_mm[0] * height_ratio, sensor_pos_mm[1] * height_ratio
        return (
            relative_ground_pos_mm[0] + self._abs_position_mm[0] + self._offset_position_mm[0],
            relative_ground_pos_mm[1] + self._abs_position_mm[1] + self._offset_position_mm[1],
            self._height_estimator.ground_position_z_mm,
        )

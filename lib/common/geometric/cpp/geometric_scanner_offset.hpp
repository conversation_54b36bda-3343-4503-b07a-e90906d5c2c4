#pragma once

#include <array>
#include <config/tree/cpp/config_scoped_callback.hpp>
#include <lib/common/cpp/utils/default_unordered_map.hpp>
#include <lib/common/cpp/utils/median_estimator.hpp>
#include <lib/common/cpp/utils/moving_window_percent.hpp>
#include <memory>
#include <mutex>
#include <tuple>
#include <unordered_map>
#include <vector>

namespace lib::common::geometric {
class GeometricScannerOffset {
public:
  using PercentileTuple = std::tuple<std::array<std::string, 4>, float>;
  using OffsetsTuple = std::tuple<std::array<std::string, 5>, float>;

private:
  using Params = std::shared_ptr<carbon::common::MedianEstimator<float>::Params>;
  class ActualOffset {
  private:
    carbon::common::MedianEstimator<float> offset_x_;
    carbon::common::MedianEstimator<float> offset_y_;
    carbon::common::MovingWindowPercent<float> dist_window_;

  public:
    ActualOffset(Params params);
    void add_offset_val(float x, float y, float z);
    std::tuple<float, float, float> get() const;
    void add_dist(float pan_mm, float tilt_mm);
    void add_failure();
    bool dist_in_range() const;
    inline float percentile() const { return dist_window_.percent(); }
  };

  class GridOffset {
  public:
    GridOffset(const std::string &scanner_id, size_t pcam_id, Params params);
    GridOffset(const GridOffset &rhs);
    void add_offset_val(float pos_x, float pos_y, float pos_z, float offset_x, float offset_y, float offset_z);
    std::tuple<float, float, float> get(float pos_x, float pos_y, float pos_z);
    void add_dist(float pos_x, float pos_y, float pos_z, float pan_mm, float tilt_mm);
    void add_failure(float pos_x, float pos_y, float pos_z);
    bool dist_in_range(float pos_x, float pos_y, float pos_z) const;
    void percentiles(std::vector<PercentileTuple> *data_out) const;
    void offsets(std::vector<OffsetsTuple> *data_out) const;

  private:
    int32_t pos_to_key(float pos) const;
    const std::string &scanner_id_;
    const std::string pcam_id_;
    mutable std::mutex mut_;
    Params params_;
    template <typename T>
    using DefParamMap = carbon::common::ParameterPackDefaultUnorderedMap<int32_t, T, Params>;
    DefParamMap<DefParamMap<ActualOffset>> offsets_;
  };
  const std::string scanner_id_;
  Params params_;
  carbon::config::ConfigScopedCallback estimator_cfg_;
  std::vector<GridOffset> offsets_;

  void update_cfg();

public:
  GeometricScannerOffset(const std::string &scanner_id);
  void add_offset_val(uint32_t pcam_id, float pos_x, float pos_y, float pos_z, float offset_x, float offset_y,
                      float offset_z);
  std::tuple<float, float, float> get(uint32_t pcam_id, float pos_x, float pos_y, float pos_z);
  void add_avg_dist(uint32_t pcam_id, float pos_x, float pos_y, float pos_z, float pan_mm, float tilt_mm);
  void add_failure(uint32_t pcam_id, float pos_x, float pos_y, float pos_z);
  bool get_dist_in_range(uint32_t pcam_id, float pos_x, float pos_y, float pos_z) const;
  void percentiles(std::vector<PercentileTuple> *data_out) const;
  void offsets(std::vector<OffsetsTuple> *data_out) const;
};
} // namespace lib::common::geometric
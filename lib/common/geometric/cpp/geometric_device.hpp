#pragma once
#include <config/tree/cpp/config_tree.hpp>
#include <string>
#include <tuple>

namespace lib {
namespace common {
namespace geometric {

using Tuple3d = std::tuple<double, double, double>;
class GeometricDevice {
protected:
  std::string name_;
  Tuple3d abs_position_mm_;
  Tuple3d offset_position_mm_;
  std::shared_ptr<carbon::config::ConfigTree> conf_;
  std::shared_ptr<carbon::config::ConfigTree> x_offset_;
  std::shared_ptr<carbon::config::ConfigTree> y_offset_;
  std::shared_ptr<carbon::config::ConfigTree> z_offset_;
  bool use_cached_offset_;

public:
  GeometricDevice(std::string name, std::string config_key, std::tuple<double, double, double> abs_position_mm);

  std::string get_name();
  Tuple3d get_abs_position_mm();
  Tuple3d get_offset_position_mm();
  Tuple3d get_position_mm();
  void override_offset_position_mm(Tuple3d offset) { offset_position_mm_ = offset; }
};

} // namespace geometric
} // namespace common
} // namespace lib
#include <cmath>

#include <iostream>
#include <lib/common/geometric/cpp/geometric_cam.hpp>
#include <lib/common/geometric/cpp/geometric_height_estimator.hpp>
#include <lib/common/geometric/cpp/global_height_estimator_collection.hpp>
#include <opencv2/calib3d.hpp>
#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>
#include <spdlog/spdlog.h>

using namespace lib::common::geometric;
GeometricCam::GeometricCam(std::string name, std::tuple<double, double, double> abs_position_mm,
                           std::tuple<uint32_t, uint32_t> resolution, double focal_length_mm, double top_to_vertex_mm,
                           std::tuple<double, double> sensor_dimensions_mm, torch::Tensor u2d_mapx,
                           torch::Tensor u2d_mapy, torch::Tensor d2u_mapx, torch::Tensor d2u_mapy)
    : GeometricDevice(name, "predicts", abs_position_mm), resolution_(resolution), focal_length_mm_(focal_length_mm),
      top_to_vertex_mm_(top_to_vertex_mm), sensor_dimensions_mm_(sensor_dimensions_mm), u2d_mapx_(u2d_mapx),
      u2d_mapy_(u2d_mapy), d2u_mapx_(d2u_mapx), d2u_mapy_(d2u_mapy) {}

GeometricCam::GeometricCam(std::string name, std::string config_key, std::tuple<double, double, double> abs_position_mm,
                           std::tuple<uint32_t, uint32_t> resolution, double focal_length_mm, double top_to_vertex_mm,
                           std::tuple<double, double> sensor_dimensions_mm, torch::Tensor u2d_mapx,
                           torch::Tensor u2d_mapy, torch::Tensor d2u_mapx, torch::Tensor d2u_mapy)
    : GeometricDevice(name, config_key, abs_position_mm), resolution_(resolution), focal_length_mm_(focal_length_mm),
      top_to_vertex_mm_(top_to_vertex_mm), sensor_dimensions_mm_(sensor_dimensions_mm), u2d_mapx_(u2d_mapx),
      u2d_mapy_(u2d_mapy), d2u_mapx_(d2u_mapx), d2u_mapy_(d2u_mapy) {}

// SWIG constructor
GeometricCam::GeometricCam(std::string name, double abs_position_mm_0, double abs_position_mm_1,
                           double abs_position_mm_2, uint32_t resolution_0, uint32_t resolution_1,
                           double focal_length_mm, double top_to_vertex_mm, double sensor_dimensions_mm_0,
                           double sensor_dimensions_mm_1, const std::vector<std::vector<double>> camera_matrix,
                           const std::vector<double> distortion_coefficients)
    : GeometricDevice(name, "predicts", std::make_tuple(abs_position_mm_0, abs_position_mm_1, abs_position_mm_2)),
      resolution_(std::make_tuple(resolution_0, resolution_1)), focal_length_mm_(focal_length_mm),
      top_to_vertex_mm_(top_to_vertex_mm),
      sensor_dimensions_mm_(std::make_tuple(sensor_dimensions_mm_0, sensor_dimensions_mm_1)) {
  int matrix_size[] = {3, 3};
  cv::Mat camera_matrix_ia = cv::Mat(2, matrix_size, CV_32FC1);
  for (size_t h = 0; h < camera_matrix.size(); h++) {
    for (size_t w = 0; w < camera_matrix[h].size(); w++) {
      camera_matrix_ia.at<float>((int)h, (int)w) = (float)camera_matrix[h][w];
    }
  }
  int coef_size[] = {1, (int)distortion_coefficients.size()};
  cv::Mat distortion_coefficients_ia = cv::Mat(2, coef_size, CV_32FC1);
  for (size_t i = 0; i < distortion_coefficients.size(); i++) {
    distortion_coefficients_ia.at<float>((int)0, (int)i) = (float)distortion_coefficients[i];
  }
  auto width = std::get<0>(resolution_);
  auto height = std::get<1>(resolution_);
  cv::Size size = cv::Size(width, height);

  cv::Mat refined_camera_matrix =
      cv::getOptimalNewCameraMatrix(camera_matrix_ia, distortion_coefficients_ia, size, 1, size);

  int map_size[] = {(int)height, (int)width};
  u2d_mapx_mat_ = cv::Mat(2, map_size, CV_32FC1);
  u2d_mapy_mat_ = cv::Mat(2, map_size, CV_32FC1);
  cv::Mat empty;

  cv::initUndistortRectifyMap(camera_matrix_ia, distortion_coefficients_ia, empty, refined_camera_matrix, size,
                              CV_32FC1, u2d_mapx_mat_, u2d_mapy_mat_);

  int distorted_points_size[] = {(int)width * (int)height, 1};
  cv::Mat distorted_points = cv::Mat(2, distorted_points_size, CV_32FC2);
  for (size_t h = 0; h < height; h++) {
    for (size_t w = 0; w < width; w++) {
      int idx = (int)h * width + (int)w;
      distorted_points.at<cv::Vec2f>(idx, 0)[0] = (float)w;
      distorted_points.at<cv::Vec2f>(idx, 0)[1] = (float)h;
    }
  }

  cv::Mat undistorted_points(2, distorted_points_size, CV_32FC2);
  cv::undistortPoints(distorted_points, undistorted_points, camera_matrix_ia, distortion_coefficients_ia, empty,
                      refined_camera_matrix);
  d2u_mapx_mat_ = cv::Mat(2, map_size, CV_32FC1);
  d2u_mapy_mat_ = cv::Mat(2, map_size, CV_32FC1);
  for (size_t h = 0; h < height; h++) {
    for (size_t w = 0; w < width; w++) {
      int idx = (int)h * width + (int)w;
      d2u_mapx_mat_.at<float>((int)h, (int)w) = undistorted_points.at<cv::Vec2f>(idx, 0)[0];
      d2u_mapy_mat_.at<float>((int)h, (int)w) = undistorted_points.at<cv::Vec2f>(idx, 0)[1];
    }
  }

  d2u_mapx_ = torch::from_blob(d2u_mapx_mat_.data, {height, width});
  d2u_mapy_ = torch::from_blob(d2u_mapy_mat_.data, {height, width});
  u2d_mapx_ = torch::from_blob(u2d_mapx_mat_.data, {height, width});
  u2d_mapy_ = torch::from_blob(u2d_mapy_mat_.data, {height, width});
}

// Given an absolute position in mm, cast it into distorted pixel coordinates and check if the point is in the camera's
// field of view
bool GeometricCam::is_point_in_camera(std::tuple<double, double, double> point_xyz_mm) {
  auto [double_x_px, double_y_px] = this->get_distorted_px_from_abs_position(point_xyz_mm);
  int64_t x_px = static_cast<int64_t>(round(double_x_px));
  int64_t y_px = static_cast<int64_t>(round(double_y_px));
  int64_t y_size = d2u_mapx_.size(0);
  int64_t x_size = d2u_mapy_.size(1);
  return x_px >= 0 && y_px >= 0 && x_px < x_size && y_px < y_size;
}

// Given an absolute position in mm, cast it into distorted pixel coordinates and check if the point is below the
// camera's field of view
bool GeometricCam::is_point_below_camera(std::tuple<double, double, double> point_xyz_mm) {
  auto [double_x_px, double_y_px] = this->get_distorted_px_from_abs_position(point_xyz_mm);
  int64_t y_px = static_cast<int64_t>(round(double_y_px));
  int64_t y_size = d2u_mapx_.size(0);
  return y_px >= y_size; // assuming y is the vertical axis and y increases as we move down
}

std::tuple<double, double> GeometricCam::undistort_point(std::tuple<double, double> point) {
  auto [x, y] = point;
  return std::make_tuple(this->d2u_mapx_.index({int(round(y)), int(round(x))}).item<double>(),
                         this->d2u_mapy_.index({int(round(y)), int(round(x))}).item<double>());
}

std::tuple<double, double> GeometricCam::distort_point(std::tuple<double, double> point) {
  auto [x, y] = point;
  if ((int(round(y)) < 0 || int(round(y)) >= this->u2d_mapx_.size(0)) ||
      (int(round(x)) < 0 || int(round(x)) >= this->u2d_mapx_.size(1))) {
    return point;
  }
  return std::make_tuple(this->u2d_mapx_.index({int(round(y)), int(round(x))}).item<double>(),
                         this->u2d_mapy_.index({int(round(y)), int(round(x))}).item<double>());
}

double GeometricCam::get_vertex_height() {
  return GlobalHeightEstimatorCollection::instance().get_height_estimator()->get_ground_position_z_mm_by_cam_name(
             name_, std::get<0>(resolution_) / 2) -
         std::get<2>(this->get_position_mm()) + this->top_to_vertex_mm_;
}

double GeometricCam::get_vertex_height_from_height_estimate(double ground_position_mm) {
  return ground_position_mm - std::get<2>(this->get_position_mm()) + this->top_to_vertex_mm_;
}

std::tuple<double, double> GeometricCam::get_sensor_mm_from_px(std::tuple<double, double> pos) {
  return std::make_tuple(
      (((std::get<0>(pos) / std::get<0>(this->resolution_)) - 0.5) * std::get<0>(this->sensor_dimensions_mm_)),
      (((std::get<1>(pos) / std::get<1>(this->resolution_)) - 0.5) * std::get<1>(this->sensor_dimensions_mm_)));
}

std::tuple<double, double> GeometricCam::get_sensor_mm_from_centered_px(std::tuple<double, double> pos) {
  return std::make_tuple(
      ((std::get<0>(pos) / std::get<0>(this->resolution_)) * std::get<0>(this->sensor_dimensions_mm_)),
      ((std::get<1>(pos) / std::get<1>(this->resolution_)) * std::get<1>(this->sensor_dimensions_mm_)));
}

std::tuple<double, double> GeometricCam::get_sensor_px_from_mm(std::tuple<double, double> mm) {
  auto [sensor_mm_x, sensor_mm_y] = mm;
  auto [sensor_dimension_mm_x, sensor_dimension_mm_y] = this->sensor_dimensions_mm_;
  auto [resolution_x, resolution_y] = this->resolution_;
  return std::make_tuple((((sensor_mm_x / sensor_dimension_mm_x) + 0.5) * resolution_x),
                         (((sensor_mm_y / sensor_dimension_mm_y) + 0.5) * resolution_y));
}

double GeometricCam::compute_vertex_height(std::tuple<double, double> delta_px, std::tuple<double, double> delta_mm) {
  /*
   * This method is used to calibrate the right bulb_to_vertex value
   */
  auto centered_sensor_mm = this->get_sensor_mm_from_centered_px(delta_px);
  double sensor_distance_mm =
      std::sqrt(std::pow(std::get<0>(centered_sensor_mm), 2) + std::pow(std::get<1>(centered_sensor_mm), 2));
  double ground_distance_mm = std::sqrt(std::pow(std::get<0>(delta_mm), 2) + std::pow(std::get<1>(delta_mm), 2));
  return ground_distance_mm * this->focal_length_mm_ / sensor_distance_mm;
}

double GeometricCam::compute_ground_height(std::tuple<double, double> delta_px, std::tuple<double, double> delta_mm) {
  double vertex_height = this->compute_vertex_height(delta_px, delta_mm);
  return vertex_height - this->top_to_vertex_mm_ + std::get<2>(this->get_position_mm());
}

std::tuple<double, double, double> GeometricCam::get_abs_position_from_px(std::tuple<double, double> pos) {
  pos = this->undistort_point(pos);
  return this->get_abs_position_from_undistorted_px(pos);
}

std::tuple<double, double, double>
GeometricCam::get_abs_position_from_px_from_height_estimate(std::tuple<double, double> pos, double height_mm) {
  pos = this->undistort_point(pos);
  return this->get_abs_position_from_undistorted_px_from_height_estimate(pos, height_mm);
}

std::tuple<double, double, double> GeometricCam::get_abs_position_from_undistorted_px(std::tuple<double, double> pos) {
  double height_ratio = this->get_vertex_height() / this->focal_length_mm_;
  auto [sensor_pos_mm_x, sensor_pos_mm_y] = this->get_sensor_mm_from_px(pos);
  auto relative_ground_pos_mm_x = sensor_pos_mm_x * height_ratio;
  auto relative_ground_pos_mm_y = sensor_pos_mm_y * height_ratio;
  return std::make_tuple(
      relative_ground_pos_mm_x + std::get<0>(this->get_position_mm()),
      relative_ground_pos_mm_y + std::get<1>(this->get_position_mm()),
      GlobalHeightEstimatorCollection::instance().get_height_estimator()->get_ground_position_z_mm_by_cam_name(
          name_, uint32_t(std::get<0>(pos))));
}

std::tuple<double, double, double>
GeometricCam::get_abs_position_from_undistorted_px_from_height_estimate(std::tuple<double, double> pos,
                                                                        double height_mm) {
  double height_ratio = this->get_vertex_height_from_height_estimate(height_mm) / this->focal_length_mm_;
  auto [sensor_pos_mm_x, sensor_pos_mm_y] = this->get_sensor_mm_from_px(pos);
  auto relative_ground_pos_mm_x = sensor_pos_mm_x * height_ratio;
  auto relative_ground_pos_mm_y = sensor_pos_mm_y * height_ratio;
  return std::make_tuple(relative_ground_pos_mm_x + std::get<0>(this->get_position_mm()),
                         relative_ground_pos_mm_y + std::get<1>(this->get_position_mm()), height_mm);
}

std::tuple<double, double>
GeometricCam::get_undistorted_px_from_abs_position(std::tuple<double, double, double> abs_pos) {
  auto [x_mm, y_mm, z_mm] = abs_pos;

  double relative_ground_pos_mm_x = x_mm - std::get<0>(this->get_position_mm());
  double relative_ground_pos_mm_y = y_mm - std::get<1>(this->get_position_mm());

  double height_ratio = this->get_vertex_height_from_height_estimate(z_mm) / this->focal_length_mm_;
  double sensor_mm_x = relative_ground_pos_mm_x / height_ratio;
  double sensor_mm_y = relative_ground_pos_mm_y / height_ratio;

  std::tuple<double, double> sensor_px = this->get_sensor_px_from_mm(std::make_tuple(sensor_mm_x, sensor_mm_y));

  return sensor_px;
}

std::tuple<double, double>
GeometricCam::get_distorted_px_from_abs_position(std::tuple<double, double, double> abs_pos) {
  auto px = this->get_undistorted_px_from_abs_position(abs_pos);
  auto [x, y] = px;
  if (y >= 0 && y < (double)(u2d_mapx_.size(0)) && x >= 0 && x < (double)(u2d_mapx_.size(1))) {
    px = this->distort_point(px);
  }
  return px;
}

// SWIG version, deprecated vector version due to memory leak
void GeometricCam::get_distorted_px_from_abs_position_swig(double abs_pos_0, double abs_pos_1, double abs_pos_2,
                                                           double *x_ptr, double *y_ptr) {
  auto [x, y] = get_distorted_px_from_abs_position(std::make_tuple(abs_pos_0, abs_pos_1, abs_pos_2));
  *x_ptr = x;
  *y_ptr = y;
}

std::tuple<double, double> GeometricCam::get_delta_mm_from_delta_px(std::tuple<double, double> delta_px) {
  double height_ratio = this->get_vertex_height() / this->focal_length_mm_;
  auto [sensor_pos_mm_x, sensor_pos_mm_y] = this->get_sensor_mm_from_px(delta_px);
  return std::make_tuple(sensor_pos_mm_x * height_ratio, sensor_pos_mm_y * height_ratio);
}

std::tuple<double, double> GeometricCam::get_min_max_abs_x_mm_for_cam() {
  double center_y = double(std::get<1>(resolution_)) / 2.0;
  std::tuple<double, double, double> min_abs = get_abs_position_from_undistorted_px(std::make_tuple(0.0, center_y));
  std::tuple<double, double, double> max_abs =
      get_abs_position_from_undistorted_px(std::make_tuple(double(std::get<0>(resolution_)), center_y));
  return std::make_tuple(std::get<0>(min_abs), std::get<0>(max_abs));
}
double GeometricCam::get_size_mm_from_size_px(double px, double height_mm) {
  if (height_mm == 0.0) {
    height_mm = this->get_vertex_height();
  } else {
    height_mm = get_vertex_height_from_height_estimate(height_mm);
  }
  double height_ratio = height_mm / this->focal_length_mm_;
  auto sensor_mm_x = ((px / std::get<0>(this->resolution_)) * std::get<0>(this->sensor_dimensions_mm_));
  auto sensor_mm_y = ((px / std::get<1>(this->resolution_)) * std::get<1>(this->sensor_dimensions_mm_));
  return height_ratio * (sensor_mm_x + sensor_mm_y) / 2.0;
}
double GeometricCam::get_size_px_from_size_mm(double mm, double height_mm) {
  if (height_mm == 0.0) {
    height_mm = this->get_vertex_height();
  } else {
    height_mm = get_vertex_height_from_height_estimate(height_mm);
  }
  double height_ratio = this->focal_length_mm_ / height_mm;
  auto sensor_mm = height_ratio * mm;
  auto sensor_px = ((sensor_mm * std::get<0>(this->resolution_)) / std::get<0>(this->sensor_dimensions_mm_));
  auto sensor_py = ((sensor_mm * std::get<1>(this->resolution_)) / std::get<1>(this->sensor_dimensions_mm_));
  return (sensor_px + sensor_py) / 2.0;
}
#include "lib/common/geometric/cpp/global_height_estimator_collection.hpp"

#include <memory>
#include <mutex>
#include <optional>
#include <shared_mutex>
#include <vector>

#include "lib/common/geometric/cpp/geometric_cam.hpp"
#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <lib/common/geometric/cpp/geometric_height_estimator.hpp>

#include <spdlog/spdlog.h>

namespace lib::common::geometric {

GlobalHeightEstimatorCollection::GlobalHeightEstimatorCollection()
    : scoped_tree_(carbon::config::get_global_config_subscriber()->get_config_node(
                       "aimbot", "height_estimation/global_height_estimator_collection"),
                   std::bind(&GlobalHeightEstimatorCollection::reload, this)),
      sized_based_enabled_(false), bucket_size_(0.0), number_of_buckets_(0),
      global_height_estimator_(construct_geometric_height_estimator()) {
  reload();
}

std::shared_ptr<GeometricHeightEstimator>
GlobalHeightEstimatorCollection::get_height_estimator(std::optional<float> size_mm) {
  std::shared_lock lock(mut_);
  if (size_mm.has_value() && sized_based_enabled_) {
    return sized_height_estimators_[get_bucket(size_mm.value())];
  }
  return global_height_estimator_;
}

GlobalHeightEstimatorCollection::EstimatorDict GlobalHeightEstimatorCollection::get_all_height_estimators() {
  std::shared_lock lock(mut_);
  GlobalHeightEstimatorCollection::EstimatorDict retval;
  retval.emplace_back("global", global_height_estimator_);
  if (sized_based_enabled_) {
    for (size_t i = 0; i < sized_height_estimators_.size(); ++i) {
      std::string key;
      double i_d = static_cast<double>(i);
      if (i != sized_height_estimators_.size() - 1) {
        key = fmt::format("{}-{}", i_d * bucket_size_, (i_d + 1) * bucket_size_);
      } else {
        key = fmt::format("{}-{}", i_d * bucket_size_, "inf");
      }
      retval.emplace_back(key, sized_height_estimators_[i]);
    }
  }
  return retval;
}

void GlobalHeightEstimatorCollection::configure(const std::vector<std::string> &predict_ids, int number_lanes,
                                                int predict_width_px) {
  std::unique_lock lock(mut_); // unique lock to prevent reload while configuring or changing the configuration
  estimator_configuration_ = std::make_unique<EstimatorConfiguration>(predict_ids, number_lanes, predict_width_px);
  global_height_estimator_->configure(predict_ids, number_lanes, predict_width_px);
  for (auto &sized_height_estimator : sized_height_estimators_) {
    sized_height_estimator->configure(predict_ids, number_lanes, predict_width_px);
  }
}

void GlobalHeightEstimatorCollection::toggle_estimation(bool enabled) {
  std::unique_lock lock(mut_);
  toggle_estimation_ = enabled;
  global_height_estimator_->toggle_estimation(enabled);
  for (auto &sized_height_estimator : sized_height_estimators_) {
    sized_height_estimator->toggle_estimation(enabled);
  }
}

// forced height estimation is lost on config reload but i don't think we really use it
void GlobalHeightEstimatorCollection::force_update_height(double ground_position_z_mm) {
  std::shared_lock lock(mut_);
  global_height_estimator_->force_update_height(ground_position_z_mm);
  for (auto &sized_height_estimator : sized_height_estimators_) {
    sized_height_estimator->force_update_height(ground_position_z_mm);
  }
}

// also lost on config reload, again, not sure if we use this
void GlobalHeightEstimatorCollection::set_min_max_x_mm_for_cam(GeometricCam &cam) {
  std::shared_lock lock(mut_);
  global_height_estimator_->set_min_max_x_mm_for_cam(cam);
  for (auto &sized_height_estimator : sized_height_estimators_) {
    sized_height_estimator->set_min_max_x_mm_for_cam(cam);
  }
}

void GlobalHeightEstimatorCollection::push_height_datapoint(GeometricCam &cam, std::tuple<double, double> delta_px,
                                                            std::tuple<double, double> delta_mm, uint32_t lane,
                                                            std::optional<float> size_mm) {
  // push to the global and then the sized if enabled
  std::shared_lock lock(mut_);
  if (size_mm.has_value() && sized_based_enabled_) {
    sized_height_estimators_[get_bucket(size_mm.value())]->push_height_datapoint(cam, delta_px, delta_mm, lane);
  }
  global_height_estimator_->push_height_datapoint(cam, delta_px, delta_mm, lane);
}

void GlobalHeightEstimatorCollection::push_computed_height_datapoint(GeometricCam &cam, double height_mm, uint32_t lane,
                                                                     std::optional<float> size_mm) {
  // push to the global and then the sized if enabled
  std::shared_lock lock(mut_);
  if (size_mm.has_value() && sized_based_enabled_) {
    sized_height_estimators_[get_bucket(size_mm.value())]->push_computed_height_datapoint(cam, height_mm, lane);
  }
  global_height_estimator_->push_computed_height_datapoint(cam, height_mm, lane);
}

void GlobalHeightEstimatorCollection::push_height_datapoint_with_pos_x(GeometricCam &cam,
                                                                       std::tuple<double, double> delta_px,
                                                                       std::tuple<double, double> delta_mm,
                                                                       uint32_t pos_x_px,
                                                                       std::optional<float> size_mm) {
  // push to the global and then the sized if enabled
  std::shared_lock lock(mut_);
  if (size_mm.has_value() && sized_based_enabled_) {
    sized_height_estimators_[get_bucket(size_mm.value())]->push_height_datapoint_with_pos_x(cam, delta_px, delta_mm,
                                                                                            pos_x_px);
  }
  global_height_estimator_->push_height_datapoint_with_pos_x(cam, delta_px, delta_mm, pos_x_px);
}

void GlobalHeightEstimatorCollection::push_computed_height_datapoint_with_pos_x(GeometricCam &cam, double height_mm,
                                                                                uint32_t pos_x_px,
                                                                                std::optional<float> size_mm) {
  // push to the global and then the sized if enabled
  std::shared_lock lock(mut_);
  if (size_mm.has_value() && sized_based_enabled_) {
    sized_height_estimators_[get_bucket(size_mm.value())]->push_computed_height_datapoint_with_pos_x(cam, height_mm,
                                                                                                     pos_x_px);
  }
  global_height_estimator_->push_computed_height_datapoint_with_pos_x(cam, height_mm, pos_x_px);
}

void GlobalHeightEstimatorCollection::run_on_all(std::function<void(GeometricHeightEstimator &)> func) {
  std::shared_lock lock(mut_);
  func(*global_height_estimator_);
  for (auto &sized_height_estimator : sized_height_estimators_) {
    func(*sized_height_estimator);
  }
}

bool GlobalHeightEstimatorCollection::_reload_config() {
  double old_bucket_size = bucket_size_;
  size_t old_number_of_buckets = number_of_buckets_;
  sized_based_enabled_ = scoped_tree_->get_node("enable_size_buckets")->get_value<bool>();
  bucket_size_ = scoped_tree_->get_node("bucket_size")->get_value<double>();
  number_of_buckets_ = scoped_tree_->get_node("number_of_buckets")->get_value<size_t>();
  return old_bucket_size != bucket_size_ || old_number_of_buckets != number_of_buckets_;
}

void GlobalHeightEstimatorCollection::_reload_buckets() {
  sized_height_estimators_.clear();
  for (size_t i = 0; i < number_of_buckets_; i++) {
    sized_height_estimators_.push_back(construct_geometric_height_estimator());
    sized_height_estimators_.back()->toggle_estimation(toggle_estimation_);
  }
  if (estimator_configuration_) {
    for (auto &sized_height_estimator : sized_height_estimators_) {
      sized_height_estimator->configure(estimator_configuration_->get_predict_ids(),
                                        estimator_configuration_->get_number_lanes(),
                                        estimator_configuration_->get_predict_width_px());
    }
  }
}

void GlobalHeightEstimatorCollection::reload() {
  std::unique_lock lock(mut_);
  if (_reload_config()) {
    _reload_buckets();
  }
}

size_t GlobalHeightEstimatorCollection::get_bucket(float size_mm) {
  if (size_mm < bucket_size_) {
    return 0;
  } else if (size_mm >= static_cast<double>(number_of_buckets_) * bucket_size_) {
    return number_of_buckets_ - 1;
  } else {
    return static_cast<size_t>(size_mm / bucket_size_);
  }
}

} // namespace lib::common::geometric

#pragma once

#include <lib/common/geometric/cpp/geometric_device.hpp>
#include <memory>
#include <opencv2/core.hpp>
#include <opencv2/core/mat.hpp>
#include <stdbool.h>
#include <torch/torch.h>

namespace lib {
namespace common {
namespace geometric {

class GeometricCam : public GeometricDevice {
protected:
  std::tuple<uint32_t, uint32_t> resolution_;
  double focal_length_mm_;
  double top_to_vertex_mm_;
  std::tuple<double, double> sensor_dimensions_mm_;

  torch::Tensor u2d_mapx_;
  torch::Tensor u2d_mapy_;
  torch::Tensor d2u_mapx_;
  torch::Tensor d2u_mapy_;

  cv::Mat u2d_mapx_mat_;
  cv::Mat u2d_mapy_mat_;
  cv::Mat d2u_mapx_mat_;
  cv::Mat d2u_mapy_mat_;

public:
  GeometricCam(std::string name, Tuple3d abs_position_mm, std::tuple<uint32_t, uint32_t> resolution,
               double focal_length_mm, double top_to_vertex_mm, std::tuple<double, double> sensor_dimensions_mm,
               torch::Tensor u2d_mapx, torch::Tensor u2d_mapy, torch::Tensor d2u_mapx, torch::Tensor d2u_mapy);

  GeometricCam(std::string name, std::string config_key, Tuple3d abs_position_mm,
               std::tuple<uint32_t, uint32_t> resolution, double focal_length_mm, double top_to_vertex_mm,
               std::tuple<double, double> sensor_dimensions_mm, torch::Tensor u2d_mapx, torch::Tensor u2d_mapy,
               torch::Tensor d2u_mapx, torch::Tensor d2u_mapy);

  // SWIG Constructor
  GeometricCam(std::string name, double abs_position_mm_0, double abs_position_mm_1, double abs_position_mm_2,
               uint32_t resolution_0, uint32_t resolution_1, double focal_length_mm, double top_to_vertex_mm,
               double sensor_dimensions_mm_0, double sensor_dimensions_mm_1,
               const std::vector<std::vector<double>> camera_matrix, const std::vector<double> distortion_coefficients);

  bool is_point_in_camera(std::tuple<double, double, double> point_xyz_mm);
  bool is_point_below_camera(std::tuple<double, double, double> point_xyz_mm);
  std::tuple<double, double> undistort_point(std::tuple<double, double> point);
  std::tuple<double, double> distort_point(std::tuple<double, double> point);

  std::tuple<double, double> get_sensor_mm_from_centered_px(std::tuple<double, double> pos);
  std::tuple<double, double> get_sensor_px_from_mm(std::tuple<double, double> mm);
  double get_size_mm_from_size_px(double px, double height_mm = 0.0);
  double get_size_px_from_size_mm(double mm, double height_mm = 0.0);

  double compute_vertex_height(std::tuple<double, double> delta_px, std::tuple<double, double> delta_mm);
  double compute_ground_height(std::tuple<double, double> delta_px, std::tuple<double, double> delta_mm);
  Tuple3d get_abs_position_from_px(std::tuple<double, double> pos);
  Tuple3d get_abs_position_from_undistorted_px(std::tuple<double, double> pos);

  Tuple3d get_abs_position_from_px_from_height_estimate(std::tuple<double, double> pos, double height_mm);
  Tuple3d get_abs_position_from_undistorted_px_from_height_estimate(std::tuple<double, double> pos, double height_mm);

  std::tuple<double, double> get_undistorted_px_from_abs_position(Tuple3d abs_pos);
  std::tuple<double, double> get_distorted_px_from_abs_position(Tuple3d abs_pos);

  // SWIG version, deprecated vector version due to memory leak
  void get_distorted_px_from_abs_position_swig(double abs_pos_0, double abs_pos_1, double abs_pos_2, double *x_ptr,
                                               double *y_ptr);

  std::tuple<double, double> get_delta_mm_from_delta_px(std::tuple<double, double> delta_px);
  std::tuple<double, double> get_min_max_abs_x_mm_for_cam();

  inline const std::tuple<uint32_t, uint32_t> &resolution(void) { return resolution_; }
  inline torch::Tensor d2u_mapx() { return d2u_mapx_; }
  inline torch::Tensor d2u_mapy() { return d2u_mapy_; }

protected:
  double get_vertex_height();
  double get_vertex_height_from_height_estimate(double ground_position_mm);
  std::tuple<double, double> get_sensor_mm_from_px(std::tuple<double, double> pos);
};

} // namespace geometric
} // namespace common
} // namespace lib

link_directories(/usr/lib/python3.8/dist-packages/torch/lib)
add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp)

add_library(geometric SHARED ${SOURCES})
target_link_directories(geometric PUBLIC /usr/local/lib /usr/local/cuda/lib64/)
target_compile_definitions(geometric PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_compile_options(geometric PUBLIC ${TORCH_CXX_FLAGS})
target_include_directories(geometric SYSTEM PUBLIC /usr/local/include/opencv4 ${nanoflann_DIR})
target_link_libraries(geometric PUBLIC m stdc++fs boost_fiber boost_chrono pthread rt boost_date_time boost_thread spdlog fmt exceptions ${TORCH_LIBRARIES} config_client_lib opencv_core opencv_calib3d opencv_imgproc
    opencv_cudaimgproc opencv_imgcodecs opencv_allocator utils robot_definition)

pybind11_add_module(geometric_python SHARED geometric_python.cpp)
target_compile_options(geometric_python PUBLIC ${TORCH_CXX_FLAGS})
target_link_libraries(geometric_python PUBLIC geometric ${PYTHON_LIBRARIES} torch_python)
target_compile_definitions(geometric_python PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
set_target_properties(geometric_python  PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

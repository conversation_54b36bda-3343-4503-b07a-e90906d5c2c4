#ifdef PYBIND
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#endif

#include <lib/common/geometric/cpp/geometric_cam.hpp>
#include <lib/common/geometric/cpp/geometric_device.hpp>
#include <lib/common/geometric/cpp/geometric_height_estimator.hpp>
#include <lib/common/geometric/cpp/geometric_scanner.hpp>
#include <lib/common/geometric/cpp/global_height_estimator_collection.hpp>
#include <torch/torch.h>

#ifdef PYBIND
#include <torch/extension.h>
namespace py = pybind11;
#endif

namespace lib {
namespace common {
namespace geometric {

std::shared_ptr<GeometricHeightEstimator> get_global_height_estimator() {
  return GlobalHeightEstimatorCollection::instance().get_height_estimator();
}

#ifdef PYBIND

PYBIND11_MODULE(geometric_python, m) {
  py::class_<GeometricDevice, std::shared_ptr<GeometricDevice>>(m, "GeometricDevice")
      .def(py::init<std::string, std::string, std::tuple<double, double, double>>(), py::arg("name"),
           py::arg("config_key"), py::arg("abs_position_mm"), py::call_guard<py::gil_scoped_release>())
      .def("get_name", &GeometricDevice::get_name, py::call_guard<py::gil_scoped_release>())
      .def("get_abs_position_mm", &GeometricDevice::get_abs_position_mm, py::call_guard<py::gil_scoped_release>())
      .def("get_offset_position_mm", &GeometricDevice::get_offset_position_mm, py::call_guard<py::gil_scoped_release>())
      .def("get_position_mm", &GeometricDevice::get_position_mm, py::call_guard<py::gil_scoped_release>())
      .def("override_offset_position_mm", &GeometricDevice::override_offset_position_mm, py::arg("offset"),
           py::call_guard<py::gil_scoped_release>());

  py::class_<GeometricCam, std::shared_ptr<GeometricCam>, GeometricDevice>(m, "GeometricCam")
      .def(py::init<std::string, std::tuple<double, double, double>, std::tuple<uint32_t, uint32_t>, double, double,
                    std::tuple<double, double>, torch::Tensor, torch::Tensor, torch::Tensor, torch::Tensor>(),
           py::arg("name"), py::arg("abs_position_mm"), py::arg("resolution"), py::arg("focal_length_mm"),
           py::arg("top_to_vertex_mm"), py::arg("sensor_dimensions_mm"), py::arg("u2d_mapx"), py::arg("u2d_mapy"),
           py::arg("d2u_mapx"), py::arg("d2u_mapy"), py::call_guard<py::gil_scoped_release>())
      .def("undistort_point", &GeometricCam::undistort_point, py::arg("point"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_sensor_mm_from_centered_px", &GeometricCam::get_sensor_mm_from_centered_px, py::arg("pos"),
           py::call_guard<py::gil_scoped_release>())
      .def("compute_vertex_height", &GeometricCam::compute_vertex_height, py::arg("delta_px"), py::arg("delta_mm"),
           py::call_guard<py::gil_scoped_release>())
      .def("compute_ground_height", &GeometricCam::compute_ground_height, py::arg("delta_px"), py::arg("delta_mm"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_abs_position_from_px", &GeometricCam::get_abs_position_from_px, py::arg("pos"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_abs_position_from_px_from_height_estimate",
           &GeometricCam::get_abs_position_from_px_from_height_estimate, py::arg("pos"), py::arg("height_mm"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_abs_position_from_undistorted_px", &GeometricCam::get_abs_position_from_undistorted_px, py::arg("pos"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_distorted_px_from_abs_position", &GeometricCam::get_distorted_px_from_abs_position, py::arg("abs_pos"),
           py::call_guard<py::gil_scoped_release>())
      .def("resolution", &GeometricCam::resolution, py::call_guard<py::gil_scoped_release>())
      .def("get_size_mm_from_size_px", &GeometricCam::get_size_mm_from_size_px, py::arg("px"), py::arg("height_mm"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_size_px_from_size_mm", &GeometricCam::get_size_px_from_size_mm, py::arg("mm"), py::arg("height_mm"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_abs_position_mm", &GeometricCam::get_abs_position_mm, py::call_guard<py::gil_scoped_release>());
  py::class_<GeometricScanner, std::shared_ptr<GeometricScanner>, GeometricDevice>(m, "GeometricScanner")
      .def(py::init<std::string, uint32_t, std::tuple<double, double, double>, std::tuple<uint32_t, uint32_t>, double,
                    double, std::tuple<double, double>, torch::Tensor, torch::Tensor, torch::Tensor, torch::Tensor,
                    float, float, float, std::tuple<uint32_t, uint32_t>, std::tuple<uint32_t, uint32_t>,
                    std::tuple<double, double>, std::tuple<double, double>>(),
           py::arg("name"), py::arg("id"), py::arg("abs_position_mm"), py::arg("resolution"),
           py::arg("focal_length_mm"), py::arg("top_to_vertex_mm"), py::arg("sensor_dimensions_mm"),
           py::arg("u2d_mapx"), py::arg("u2d_mapy"), py::arg("d2u_mapx"), py::arg("d2u_mapy"), py::arg("pan_to_tilt"),
           py::arg("tilt_to_combiner"), py::arg("tilt_to_vertex_offset"), py::arg("roi_size"),
           py::arg("servo_resolution"), py::arg("delta_servo_factors"), py::arg("angular_offset"),
           py::call_guard<py::gil_scoped_release>())
      .def("set_servo_centers", &GeometricScanner::set_servo_centers, py::arg("servo_centers"),
           py::call_guard<py::gil_scoped_release>())
      .def("set_override_skews", &GeometricScanner::set_override_skews, py::arg("pan_skew"), py::arg("tilt_skew"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_servo_position_from_ground", &GeometricScanner::get_servo_position_from_ground, py::arg("pos"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_abs_position_from_servo", &GeometricScanner::get_abs_position_from_servo, py::arg("pos"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_abs_position_from_servo_with_z", &GeometricScanner::get_abs_position_from_servo_with_z, py::arg("pos"),
           py::arg("abs_z"), py::call_guard<py::gil_scoped_release>())
      .def("get_height_key_from_servo_pos", &GeometricScanner::get_height_key_from_servo_pos, py::arg("pos_pan"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_adjusted_tilt_velocity_with_pos_from_vel",
           &GeometricScanner::get_adjusted_tilt_velocity_with_pos_from_vel, py::arg("pan_pos"), py::arg("tilt_pos"),
           py::arg("velocity_mph"), py::arg("z_height_mm") = py::none(), py::call_guard<py::gil_scoped_release>())
      .def("get_servo_position_for_delta_px",
           py::overload_cast<std::tuple<int32_t, int32_t>, std::tuple<int32_t, int32_t>>(
               &GeometricScanner::get_servo_position_for_delta_px),
           py::arg("delta_px"), py::arg("pos_servos"), py::call_guard<py::gil_scoped_release>())
      .def("get_servo_delta_for_delta_px",
           py::overload_cast<std::tuple<int32_t, int32_t>, std::tuple<int32_t, int32_t>>(
               &GeometricScanner::get_servo_delta_for_delta_px),
           py::arg("delta_px"), py::arg("pos_servos"), py::call_guard<py::gil_scoped_release>())
      .def("get_servo_delta_for_crosshair",
           py::overload_cast<std::tuple<int32_t, int32_t>, std::tuple<int32_t, int32_t>>(
               &GeometricScanner::get_servo_delta_for_crosshair),
           py::arg("crosshair_px"), py::arg("pos_servos"), py::call_guard<py::gil_scoped_release>())
      .def("get_size_mm_from_size_px", &GeometricCam::get_size_mm_from_size_px, py::arg("px"), py::arg("height_mm"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_size_px_from_size_mm", &GeometricCam::get_size_px_from_size_mm, py::arg("mm"), py::arg("height_mm"),
           py::call_guard<py::gil_scoped_release>())
      .def("add_offset_val", &GeometricScanner::add_offset_val, py::arg("pcam_id"), py::arg("pos_x"), py::arg("pos_y"),
           py::arg("pos_z"), py::arg("offset_x"), py::arg("offset_y"), py::arg("offset_z"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_offset", &GeometricScanner::get_offset, py::arg("pcam_id"), py::arg("pos_x"), py::arg("pos_y"),
           py::arg("pos_z"), py::call_guard<py::gil_scoped_release>())
      .def("add_dist", &GeometricScanner::add_dist, py::arg("pcam_id"), py::arg("pos_x"), py::arg("pos_y"),
           py::arg("pos_z"), py::arg("pan_mm"), py::arg("tilt_mm"), py::call_guard<py::gil_scoped_release>())
      .def("add_failure", &GeometricScanner::add_failure, py::arg("pcam_id"), py::arg("pos_x"), py::arg("pos_y"),
           py::arg("pos_z"), py::call_guard<py::gil_scoped_release>())
      .def("get_dist_in_range", &GeometricScanner::get_dist_in_range, py::arg("pcam_id"), py::arg("pos_x"),
           py::arg("pos_y"), py::arg("pos_z"), py::call_guard<py::gil_scoped_release>())
      .def("get_center", &GeometricScanner::get_center, py::call_guard<py::gil_scoped_release>());

  py::class_<GeometricHeightEstimator, std::shared_ptr<GeometricHeightEstimator>>(m, "GeometricHeightEstimator")
      .def(py::init<double, double, double, double, double, double, double, double>(), py::arg("ground_position_z_mm"),
           py::arg("smoothing_factor"), py::arg("min_height_mm"), py::arg("max_height_mm"), py::arg("min_delta_px"),
           py::arg("max_delta_px"), py::arg("min_delta_mm"), py::arg("max_delta_mm"),
           py::call_guard<py::gil_scoped_release>())
      .def("configure", &GeometricHeightEstimator::configure, py::arg("predict_ids"), py::arg("number_lanes"),
           py::arg("predict_width_px"), py::call_guard<py::gil_scoped_release>())
      .def("force_update_height", &GeometricHeightEstimator::force_update_height, py::arg("ground_position_z_mm"),
           py::call_guard<py::gil_scoped_release>())
      .def("push_height_datapoint", &GeometricHeightEstimator::push_height_datapoint, py::arg("cam"),
           py::arg("delta_px"), py::arg("delta_mm"), py::arg("lane") = 0, py::call_guard<py::gil_scoped_release>())
      .def("toggle_estimation", &GeometricHeightEstimator::toggle_estimation, py::arg("enabled"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_ground_position_z_mm", &GeometricHeightEstimator::get_ground_position_z_mm,
           py::call_guard<py::gil_scoped_release>())
      .def("get_ground_position_z_mm", &GeometricHeightEstimator::get_ground_position_z_mm_by_cam, py::arg("cam"),
           py::arg("pos_x_px"), py::call_guard<py::gil_scoped_release>())
      .def("get_ground_position_z_mm", &GeometricHeightEstimator::get_ground_position_z_mm_by_cam_name,
           py::arg("cam_name"), py::arg("pos_x_px"), py::call_guard<py::gil_scoped_release>())
      .def("set_min_max_x_mm_for_cam", &GeometricHeightEstimator::set_min_max_x_mm_for_cam, py::arg("cam"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_keys_for_abs_x", &GeometricHeightEstimator::get_keys_for_abs_x, py::arg("pos_x_mm"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_height_mm_for_key", &GeometricHeightEstimator::get_height_mm_for_key, py::arg("key"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_height_mm_for_abs_x", &GeometricHeightEstimator::get_height_mm_for_abs_x, py::arg("pos_x_mm"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_max_height_mm", &GeometricHeightEstimator::get_max_height_mm, py::call_guard<py::gil_scoped_release>());

  m.def("get_global_height_estimator", &get_global_height_estimator, py::call_guard<py::gil_scoped_release>());

  py::class_<GlobalHeightEstimatorCollection, std::shared_ptr<GlobalHeightEstimatorCollection>>(
      m, "GlobalHeightEstimatorCollection")
      .def_static("instance", &GlobalHeightEstimatorCollection::instance, py::call_guard<py::gil_scoped_release>(),
                  py::return_value_policy::reference)
      .def("get_height_estimator", &GlobalHeightEstimatorCollection::get_height_estimator,
           py::arg("size_mm") = py::none(), py::call_guard<py::gil_scoped_release>())
      .def("configure", &GlobalHeightEstimatorCollection::configure, py::arg("predict_ids"), py::arg("number_lanes"),
           py::arg("predict_width_px"), py::call_guard<py::gil_scoped_release>())
      .def("toggle_estimation", &GlobalHeightEstimatorCollection::toggle_estimation, py::arg("enabled"),
           py::call_guard<py::gil_scoped_release>())
      .def("set_min_max_x_mm_for_cam", &GlobalHeightEstimatorCollection::set_min_max_x_mm_for_cam, py::arg("cam"),
           py::call_guard<py::gil_scoped_release>())
      .def("force_update_height", &GlobalHeightEstimatorCollection::force_update_height,
           py::arg("ground_position_z_mm"), py::call_guard<py::gil_scoped_release>());
}

#endif

} // namespace geometric
} // namespace common
} // namespace lib
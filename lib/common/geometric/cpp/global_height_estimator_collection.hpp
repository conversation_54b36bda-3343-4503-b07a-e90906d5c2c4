#pragma once

#include <memory>
#include <mutex>
#include <optional>
#include <shared_mutex>
#include <vector>

#include <config/tree/cpp/config_scoped_callback.hpp>
#include <config/tree/cpp/config_tree.hpp>

namespace lib::common::geometric {

class GeometricCam;
class GeometricHeightEstimator;

/**
 * Global wrapper for height estimators.
 * Contains a global height estimator and a collection of height estimators for different sized plants
 */
class GlobalHeightEstimatorCollection {
public:
  using EstimatorDict = std::vector<std::pair<std::string, std::shared_ptr<GeometricHeightEstimator>>>;
  /**
   * Get the instance of the GlobalHeightEstimatorCollection.
   * Configuration is locked after construction and will not reload.
   * @return the instance of the GlobalHeightEstimatorCollection
   */
  static GlobalHeightEstimatorCollection &instance() {
    static GlobalHeightEstimatorCollection instance;
    return instance;
  }
  GlobalHeightEstimatorCollection();
  GlobalHeightEstimatorCollection(const GlobalHeightEstimatorCollection &) = delete;
  GlobalHeightEstimatorCollection &operator=(const GlobalHeightEstimatorCollection &) = delete;

  /**
   * Get the height estimator for a given size plant
   * @param size_mm the size of the plant in mm, if not provided, the global height estimator is returned
   */
  std::shared_ptr<GeometricHeightEstimator> get_height_estimator(std::optional<float> size_mm = std::nullopt);
  EstimatorDict get_all_height_estimators();
  void configure(const std::vector<std::string> &predict_ids, int number_lanes, int predict_width_px);
  void toggle_estimation(bool enabled);
  void force_update_height(double ground_position_z_mm);
  void set_min_max_x_mm_for_cam(GeometricCam &cam);
  void push_height_datapoint(GeometricCam &cam, std::tuple<double, double> delta_px,
                             std::tuple<double, double> delta_mm, uint32_t lane = 0,
                             std::optional<float> size_mm = std::nullopt);
  void push_computed_height_datapoint(GeometricCam &cam, double height_mm, uint32_t lane = 0,
                                      std::optional<float> size_mm = std::nullopt);
  void push_height_datapoint_with_pos_x(GeometricCam &cam, std::tuple<double, double> delta_px,
                                        std::tuple<double, double> delta_mm, uint32_t pos_x_px,
                                        std::optional<float> size_mm = std::nullopt);
  void push_computed_height_datapoint_with_pos_x(GeometricCam &cam, double height_mm, uint32_t pos_x_px,
                                                 std::optional<float> size_mm = std::nullopt);
  void run_on_all(std::function<void(GeometricHeightEstimator &)> func);

private:
  std::shared_mutex mut_;
  carbon::config::ConfigScopedCallback scoped_tree_;
  bool sized_based_enabled_;
  double bucket_size_;
  size_t number_of_buckets_;

  std::shared_ptr<GeometricHeightEstimator> global_height_estimator_;
  std::vector<std::shared_ptr<GeometricHeightEstimator>> sized_height_estimators_;

  class EstimatorConfiguration {
  public:
    EstimatorConfiguration(std::vector<std::string> predict_ids, int number_lanes, int predict_width_px)
        : predict_ids_(predict_ids), number_lanes_(number_lanes), predict_width_px_(predict_width_px){};

    inline const std::vector<std::string> &get_predict_ids() const { return predict_ids_; }
    inline int get_number_lanes() const { return number_lanes_; }
    inline int get_predict_width_px() const { return predict_width_px_; }

  private:
    std::vector<std::string> predict_ids_;
    int number_lanes_;
    int predict_width_px_;
  };
  std::unique_ptr<EstimatorConfiguration> estimator_configuration_;
  bool toggle_estimation_;

  /**
   * @returns true if the bucket size or number of buckets has changed
   */
  bool _reload_config();
  void _reload_buckets();
  void reload();
  size_t get_bucket(float size_mm);
};

} // namespace lib::common::geometric

#include "lib/common/geometric/cpp/geometric_scanner_offset.hpp"

#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_atomic_accessor.hpp>
#include <lib/common/cpp/utils/generation.hpp>
#include <lib/common/robot_definition/cpp/robot_definition.hpp>

#include <cmath>
#include <cstdlib>
#include <string>

#include <spdlog/spdlog.h>

size_t constexpr avg_window_size = 100;
namespace lib::common::geometric {
constexpr float mm_to_key_units(float mm) { return mm * 10.0f; }
constexpr float key_units_to_mm(int32_t ku) { return static_cast<float>(ku) / 10.0f; }

size_t find_num_cams() {
  size_t cfg_count = carbon::config::get_global_config_subscriber()
                         ->get_config_node("aimbot", "geometric/calibrator/num_pcam_override")
                         ->get_value<uint64_t>();
  if (cfg_count > 0) {
    return cfg_count;
  }

  if (carbon::common::is_reaper()) {
    return lib::common::robot_definition::RobotDefinition::get()
        ->get_local_row_definition()
        ->get_number_of_predict_cams();
  }

  if (carbon::common::is_bud() || carbon::common::is_slayer()) {
    return 4;
  }
  // Allow for adding new generation info here
  spdlog::warn("Generation '{}' does not have defined num pcams.", carbon::common::generation_str());
  return 4;
}
bool in_acceptable_dist(const float &dist_sqrd) {
  static carbon::config::ConfigAtomicAccessor<float> acceptable_dist_sqrd(
      carbon::config::get_global_config_subscriber()->get_config_node(
          "aimbot", "geometric/calibrator/max_dist_speculative_laser"),
      [](float dist) { return std::pow(dist, 2.0f); });
  return dist_sqrd <= acceptable_dist_sqrd.get_value();
}
std::string pcam_id_to_str(size_t id) { return fmt::format("predict{}", id); }
GeometricScannerOffset::ActualOffset::ActualOffset(Params params)
    : offset_x_(params, 0), offset_y_(params, 0), dist_window_(avg_window_size, in_acceptable_dist, true) {}
void GeometricScannerOffset::ActualOffset::add_offset_val(float x, float y, float z) {
  static carbon::config::ConfigAtomicAccessor<float> max_jump_cfg(
      carbon::config::get_global_config_subscriber()->get_config_node("aimbot",
                                                                      "geometric/calibrator/max_usable_offset"));
  (void)z; // currently not doing anythin with Z, but may do so in the future
  float max_jump = max_jump_cfg.get_value();
  if (std::abs(x) < max_jump) {
    offset_x_.add(x);
  }
  if (std::abs(y) < max_jump) {
    offset_y_.add(y);
  }
}
std::tuple<float, float, float> GeometricScannerOffset::ActualOffset::get() const {
  return std::make_tuple<float, float, float>(offset_x_.value(), offset_y_.value(), 0.0f);
}
void GeometricScannerOffset::ActualOffset::add_dist(float pan_mm, float tilt_mm) {
  dist_window_.add(std::pow(pan_mm, 2.0f) + std::pow(tilt_mm, 2.0f));
}
void GeometricScannerOffset::ActualOffset::add_failure() { dist_window_.add_direct(false); }
bool GeometricScannerOffset::ActualOffset::dist_in_range() const {
  static carbon::config::ConfigAtomicAccessor<float> required_pct(
      carbon::config::get_global_config_subscriber()->get_config_node(
          "aimbot", "geometric/calibrator/speculative_laser_percentile"));
  return dist_window_.percent() >= required_pct.get_value();
}

int32_t grid_dist() {
  auto dist = carbon::config::get_global_config_subscriber()
                  ->get_config_node("aimbot", "geometric/calibrator/grid_size")
                  ->get_value<float>();
  return static_cast<int32_t>(mm_to_key_units(dist));
}
GeometricScannerOffset::GridOffset::GridOffset(const std::string &scanner_id, size_t pcam_id, Params params)
    : scanner_id_(scanner_id), pcam_id_(pcam_id_to_str(pcam_id)), params_(params),
      offsets_(std::forward<Params>(params_)) {}
GeometricScannerOffset::GridOffset::GridOffset(const GridOffset &rhs)
    : scanner_id_(rhs.scanner_id_), pcam_id_(rhs.pcam_id_), params_(rhs.params_),
      offsets_(std::forward<Params>(params_)) {}
void GeometricScannerOffset::GridOffset::add_offset_val(float pos_x, float pos_y, float pos_z, float offset_x,
                                                        float offset_y, float offset_z) {
  (void)pos_z;
  int32_t key_x(pos_to_key(pos_x));
  int32_t key_y(pos_to_key(pos_y));
  {
    const std::unique_lock lk(mut_);
    return offsets_[key_x][key_y].add_offset_val(offset_x, offset_y, offset_z);
  }
}
std::tuple<float, float, float> GeometricScannerOffset::GridOffset::get(float pos_x, float pos_y, float pos_z) {
  (void)pos_z;
  int32_t key_x(pos_to_key(pos_x));
  int32_t key_y(pos_to_key(pos_y));
  {
    const std::unique_lock lk(mut_);
    return offsets_[key_x][key_y].get();
  }
}
void GeometricScannerOffset::GridOffset::add_dist(float pos_x, float pos_y, float pos_z, float pan_mm, float tilt_mm) {
  (void)pos_z;
  int32_t key_x(pos_to_key(pos_x));
  int32_t key_y(pos_to_key(pos_y));
  {
    const std::unique_lock lk(mut_);
    return offsets_[key_x][key_y].add_dist(pan_mm, tilt_mm);
  }
}
void GeometricScannerOffset::GridOffset::add_failure(float pos_x, float pos_y, float pos_z) {
  (void)pos_z;
  int32_t key_x(pos_to_key(pos_x));
  int32_t key_y(pos_to_key(pos_y));
  {
    const std::unique_lock lk(mut_);
    return offsets_[key_x][key_y].add_failure();
  }
}
bool GeometricScannerOffset::GridOffset::dist_in_range(float pos_x, float pos_y, float pos_z) const {
  (void)pos_z;
  int32_t key_x(pos_to_key(pos_x));
  int32_t key_y(pos_to_key(pos_y));
  {
    const std::unique_lock lk(mut_);
    auto it_x = offsets_.find(key_x);
    if (it_x == offsets_.end()) {
      return false;
    } else {
      auto it_y = it_x->second.find(key_y);
      if (it_y == it_x->second.end()) {
        return false;
      } else {
        return it_y->second.dist_in_range();
      }
    }
  }
  return false;
}
void GeometricScannerOffset::GridOffset::percentiles(std::vector<PercentileTuple> *data_out) const {
  const std::unique_lock lk(mut_);
  for (const auto &it : offsets_) {
    for (const auto &off_it : it.second) {
      std::array<std::string, 4> arr({scanner_id_, pcam_id_, std::to_string(key_units_to_mm(it.first)),
                                      std::to_string(key_units_to_mm(off_it.first))});
      data_out->emplace_back(arr, off_it.second.percentile());
    }
  }
}
void GeometricScannerOffset::GridOffset::offsets(std::vector<OffsetsTuple> *data_out) const {
  const std::unique_lock lk(mut_);
  for (const auto &it : offsets_) {
    for (const auto &off_it : it.second) {
      std::array<std::string, 5> arr(
          {scanner_id_, pcam_id_, std::to_string(it.first), std::to_string(off_it.first), "x"});
      auto offset = off_it.second.get();
      data_out->emplace_back(arr, std::get<0>(offset));
      arr[4] = "y";
      data_out->emplace_back(arr, std::get<1>(offset));
      arr[4] = "z";
      data_out->emplace_back(arr, std::get<2>(offset));
    }
  }
}

int32_t GeometricScannerOffset::GridOffset::pos_to_key(float pos) const {
  static int32_t dist(grid_dist());
  static float distf(static_cast<float>(dist));
  return static_cast<int32_t>(std::round(mm_to_key_units(pos) / distf)) * dist;
}

GeometricScannerOffset::GeometricScannerOffset(const std::string &scanner_id)
    : scanner_id_(scanner_id),
      params_(std::make_shared<carbon::common::MedianEstimator<float>::Params>(0.1f, 0.09f, 0.9f, 10.0f)),
      estimator_cfg_(carbon::config::get_global_config_subscriber()->get_config_node(
                         "aimbot", "geometric/calibrator/median_estimator"),
                     std::bind(&GeometricScannerOffset::update_cfg, this), true) {
  static size_t num_cams = find_num_cams(); // calculated exactly 1 time per aimbot
  offsets_.reserve(num_cams);
  for (size_t i = 0; i < num_cams; ++i) {
    offsets_.emplace_back(scanner_id_, i + 1, params_);
  }
}
void GeometricScannerOffset::add_offset_val(uint32_t pcam_id, float pos_x, float pos_y, float pos_z, float offset_x,
                                            float offset_y, float offset_z) {
  // pcam is 1 based so need to subtract 1
  offsets_[pcam_id - 1].add_offset_val(pos_x, pos_y, pos_z, offset_x, offset_y, offset_z);
}
std::tuple<float, float, float> GeometricScannerOffset::get(uint32_t pcam_id, float pos_x, float pos_y, float pos_z) {
  static carbon::config::ConfigAtomicAccessor<bool> enabled(
      carbon::config::get_global_config_subscriber()->get_config_node("common",
                                                                      "feature_flags/geo_calibrator_feature"));
  if (enabled.get_value()) {
    // pcam is 1 based so need to subtract 1
    return offsets_[pcam_id - 1].get(pos_x, pos_y, pos_z);
  }
  return std::make_tuple<float, float, float>(0.0f, 0.0f, 0.0f);
}
void GeometricScannerOffset::add_avg_dist(uint32_t pcam_id, float pos_x, float pos_y, float pos_z, float pan_mm,
                                          float tilt_mm) {
  offsets_[pcam_id - 1].add_dist(pos_x, pos_y, pos_z, pan_mm, tilt_mm);
}
void GeometricScannerOffset::add_failure(uint32_t pcam_id, float pos_x, float pos_y, float pos_z) {
  offsets_[pcam_id - 1].add_failure(pos_x, pos_y, pos_z);
}
bool GeometricScannerOffset::get_dist_in_range(uint32_t pcam_id, float pos_x, float pos_y, float pos_z) const {
  return offsets_[pcam_id - 1].dist_in_range(pos_x, pos_y, pos_z);
}
void GeometricScannerOffset::percentiles(std::vector<PercentileTuple> *data_out) const {
  for (size_t i = 0; i < offsets_.size(); ++i) {
    offsets_[i].percentiles(data_out);
  }
}
void GeometricScannerOffset::offsets(std::vector<OffsetsTuple> *data_out) const {
  for (size_t i = 0; i < offsets_.size(); ++i) {
    offsets_[i].offsets(data_out);
  }
}
void GeometricScannerOffset::update_cfg() {
  params_->step_size = estimator_cfg_->get_node("step_size")->get_value<float>();
  params_->convergence_threshold = estimator_cfg_->get_node("convergence_threshold")->get_value<float>();
  params_->convergence_smoothing = estimator_cfg_->get_node("convergence_smoothing")->get_value<float>();
  params_->convergence_factor = estimator_cfg_->get_node("convergence_factor")->get_value<float>();
}
} // namespace lib::common::geometric
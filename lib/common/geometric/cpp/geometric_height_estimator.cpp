#include <lib/common/geometric/cpp/geometric_cam.hpp>
#include <lib/common/geometric/cpp/geometric_height_estimator.hpp>
#include <spdlog/spdlog.h>

namespace lib {
namespace common {
namespace geometric {
GeometricCamHeightCollector::GeometricCamHeightCollector(const std::string &id, double default_ground_position_z_mm,
                                                         double smoothing_factor, double min_height_mm,
                                                         double max_height_mm, uint32_t width_px)
    : id_(id), default_ground_position_z_mm_(default_ground_position_z_mm), smoothing_factor_(smoothing_factor),
      average_(default_ground_position_z_mm), min_height_mm_(min_height_mm), max_height_mm_(max_height_mm),
      width_px_(width_px), lane_width_px_(width_px), dirty_(false) {
  averages_.emplace_back(default_ground_position_z_mm_, smoothing_factor_, true);
}

void GeometricCamHeightCollector::set_columns(size_t lanes) {
  std::lock_guard<std::mutex> lk(height_mutex_);
  dirty_ = true;
  if (lanes == averages_.size()) {
    return;
  }
  if (lanes < 1u) {
    spdlog::warn("Each camera must have at least 1 lane");
    lanes = 1;
  }
  if (lanes < averages_.size()) {
    averages_.resize(lanes,
                     lib::common::average::MovingAverage(default_ground_position_z_mm_, smoothing_factor_, true));
  } else {
    averages_.reserve(lanes);
    size_t required = lanes - averages_.size();
    for (size_t i = 0; i < required; ++i) {
      averages_.emplace_back(default_ground_position_z_mm_, smoothing_factor_, true);
    }
  }
  lane_width_px_ = width_px_ / uint32_t(averages_.size());
}

void GeometricCamHeightCollector::load_average() {
  std::lock_guard<std::mutex> lk(height_mutex_);
  double local_avg = 0.0;
  for (auto &avg : averages_) {
    local_avg += avg.get_value();
  }
  if (!averages_.empty()) {
    local_avg /= double(averages_.size());
  }
  average_ = local_avg;
  dirty_ = false;
}

double GeometricCamHeightCollector::average() {
  // TODO(jfroel): fix the race on the dirty bit
  if (dirty_.load()) {
    load_average();
  }
  return average_;
}

double GeometricCamHeightCollector::average(uint32_t pos_x_px) {
  uint32_t index = pos_x_px / lane_width_px_;
  return average_for_column(index);
}

double GeometricCamHeightCollector::average_for_column(uint32_t column) {
  std::lock_guard<std::mutex> lk(height_mutex_);
  if (column >= averages_.size()) {
    column = uint32_t(averages_.size()) - 1;
  }
  return averages_[column].get_value();
}

std::vector<double> GeometricCamHeightCollector::averages_for_all_columns() {
  std::lock_guard<std::mutex> lk(height_mutex_);
  std::vector<double> avgs_for_all_columns;
  for (size_t col_ind = 0; col_ind < averages_.size(); col_ind++) {
    avgs_for_all_columns.push_back(averages_[col_ind].get_value());
  }
  return avgs_for_all_columns;
}

void GeometricCamHeightCollector::push_datapoint(double height_mm, size_t lane) {
  std::lock_guard<std::mutex> lk(height_mutex_);
  dirty_ = true;
  if (lane > averages_.size()) {
    spdlog::warn("Lane {} out of range, using {} instead", lane, averages_.size() - 1);
    lane = averages_.size() - 1;
  }
  spdlog::debug("adding height {} for cam {}, lane {}", height_mm, id_, lane);
  averages_[lane].update(height_mm);
  // Clamp Height Estimates Out of Range
  auto new_avg = averages_[lane].get_value();
  if (new_avg < min_height_mm_) {
    averages_[lane].override_value(min_height_mm_);
    spdlog::debug("Height Clamped to Minimum: {}, tried to push height value {} to get new average {} in lane {}",
                  min_height_mm_, height_mm, new_avg, lane);
  } else if (new_avg > max_height_mm_) {
    averages_[lane].override_value(max_height_mm_);
    spdlog::debug("Height Clamped to Maximum: {}, tried to push height value {} to get new average {} in lane {}",
                  max_height_mm_, height_mm, new_avg, lane);
  }
}

void GeometricCamHeightCollector::force_update_height(double ground_position_z_mm) {
  std::lock_guard<std::mutex> lk(height_mutex_);
  for (auto &avg : averages_) {
    avg.override_value(ground_position_z_mm);
  }
  default_ground_position_z_mm_ = ground_position_z_mm;
}
uint32_t GeometricCamHeightCollector::get_column_for_abs_pos_mm(double pos_x_mm,
                                                                std::tuple<double, double> min_max_pos_mm) {
  std::lock_guard<std::mutex> lk(height_mutex_);
  double lane_width_mm = (std::get<1>(min_max_pos_mm) - std::get<0>(min_max_pos_mm)) / double(averages_.size());
  uint32_t index = uint32_t((pos_x_mm - std::get<0>(min_max_pos_mm)) / lane_width_mm);
  if (index >= averages_.size()) {
    index = uint32_t(averages_.size()) - 1;
  }
  return index;
}

uint32_t GeometricCamHeightCollector::get_column_for_pos_x_px(double pos_x_px) {
  return (uint32_t)((uint32_t)pos_x_px / lane_width_px_);
}

GeometricHeightEstimator::GeometricHeightEstimator(double ground_position_z_mm, double smoothing_factor,
                                                   double min_height_mm, double max_height_mm, double min_delta_px,
                                                   double max_delta_px, double min_delta_mm, double max_delta_mm)
    : default_ground_position_z_mm_(ground_position_z_mm), smoothing_factor_(smoothing_factor),
      min_height_mm_(min_height_mm), max_height_mm_(max_height_mm), min_delta_px_(min_delta_px),
      max_delta_px_(max_delta_px), min_delta_mm_(min_delta_mm), max_delta_mm_(max_delta_mm) {}

void GeometricHeightEstimator::configure(const std::vector<std::string> &predict_ids, int number_lanes,
                                         int predict_width_px) {
  // Get the cam_ids and widths, then emplace into averages the  number of lanes
  std::lock_guard<std::mutex> lk(height_mutex_);
  averages_.clear(); // Theoretically this function should only be called at startup so no data yet, but this will
                     // prevent issues
  for (const auto &predict_id : predict_ids) {
    auto response =
        averages_.emplace(std::piecewise_construct, std::forward_as_tuple(predict_id),
                          std::forward_as_tuple(predict_id, default_ground_position_z_mm_, smoothing_factor_,
                                                min_height_mm_, max_height_mm_, predict_width_px));
    auto it = response.first;
    it->second.set_columns(number_lanes);
  }
}

GeometricCamHeightCollector &GeometricHeightEstimator::get_average(GeometricCam &cam) {
  auto it = averages_.find(cam.get_name());
  if (it == averages_.end()) {
    auto response =
        averages_.emplace(std::piecewise_construct, std::forward_as_tuple(cam.get_name()),
                          std::forward_as_tuple(cam.get_name(), default_ground_position_z_mm_, smoothing_factor_,
                                                min_height_mm_, max_height_mm_, std::get<0>(cam.resolution())));
    it = response.first;
  }
  return it->second;
}

void GeometricHeightEstimator::push_computed_height_datapoint(GeometricCam &cam, double height_mm, uint32_t lane) {
  if (height_mm > max_height_mm_ || height_mm < min_height_mm_) {
    spdlog::debug("Height Out of Bound, not pushed to average: {}", height_mm);
  } else {
    std::lock_guard<std::mutex> lk(height_mutex_);
    get_average(cam).push_datapoint(height_mm, lane);
  }
}

void GeometricHeightEstimator::push_height_datapoint(GeometricCam &cam, std::tuple<double, double> delta_px,
                                                     std::tuple<double, double> delta_mm, uint32_t lane) {
  std::string cam_name = cam.get_name();

  // Real World Delta Out of Range
  double ground_distance_mm = std::sqrt(std::pow(std::get<0>(delta_mm), 2) + std::pow(std::get<1>(delta_mm), 2));
  if (ground_distance_mm < min_delta_mm_ || ground_distance_mm > max_delta_mm_) {
    spdlog::debug("{} Ground Distance Out Of Limits {}", cam_name, ground_distance_mm);
    return;
  }

  // Pixel Flow Out of Range
  double cam_distance_px = std::sqrt(std::pow(std::get<0>(delta_px), 2) + std::pow(std::get<1>(delta_px), 2));
  if (cam_distance_px < min_delta_px_ || cam_distance_px > max_delta_px_) {
    spdlog::debug("{} Cam Distance Out Of Limits {}", cam_name, cam_distance_px);
    return;
  }

  double height_mm = cam.compute_ground_height(delta_px, delta_mm);
  push_computed_height_datapoint(cam, height_mm, lane);
}

void GeometricHeightEstimator::push_height_datapoint_with_pos_x(GeometricCam &cam, std::tuple<double, double> delta_px,
                                                                std::tuple<double, double> delta_mm,
                                                                uint32_t pos_x_px) {
  uint32_t lane;
  {
    std::lock_guard<std::mutex> lk(height_mutex_);
    lane = get_average(cam).get_column_for_pos_x_px(pos_x_px);
  }
  push_height_datapoint(cam, delta_px, delta_mm, lane);
}

void GeometricHeightEstimator::push_computed_height_datapoint_with_pos_x(GeometricCam &cam, double height_mm,
                                                                         uint32_t pos_x_px) {
  uint32_t lane;
  {
    std::lock_guard<std::mutex> lk(height_mutex_);
    lane = get_average(cam).get_column_for_pos_x_px(pos_x_px);
  }
  push_computed_height_datapoint(cam, height_mm, lane);
}

void GeometricHeightEstimator::force_update_height(double ground_position_z_mm) {
  std::lock_guard<std::mutex> lk(height_mutex_);
  for (auto &[_, avg] : averages_) {
    (void)_;
    avg.force_update_height(ground_position_z_mm);
  }
  default_ground_position_z_mm_ = ground_position_z_mm;
}

double GeometricHeightEstimator::compute_average() {
  double sum = 0;
  uint8_t count = 0;
  for (auto &[_, avg] : averages_) {
    (void)_;
    sum += avg.average();
    count++;
  }

  if (count == 0) {
    return default_ground_position_z_mm_;
  }

  return sum / count;
}
double GeometricHeightEstimator::get_ground_position_z_mm() {
  if (!enabled_) {
    return default_ground_position_z_mm_;
  }

  {
    std::lock_guard<std::mutex> lk(height_mutex_);
    return compute_average();
  }
}
double GeometricHeightEstimator::get_ground_position_z_mm_by_cam(GeometricCam &cam, uint32_t pos_x_px) {
  std::string cam_name = cam.get_name();
  return get_ground_position_z_mm_by_cam_name(cam_name, pos_x_px);
}
double GeometricHeightEstimator::get_ground_position_z_mm_by_cam_name(const std::string &cam_name, uint32_t pos_x_px) {
  if (!enabled_) {
    return default_ground_position_z_mm_;
  }
  {
    std::lock_guard<std::mutex> lk(height_mutex_);
    auto it = averages_.find(cam_name);
    if (it == averages_.end()) {
      return default_ground_position_z_mm_;
    }
    return it->second.average(pos_x_px);
  }
}

std::vector<double> GeometricHeightEstimator::get_averages_for_all_columns(GeometricCam &cam) {
  std::string cam_name = cam.get_name();
  std::lock_guard<std::mutex> lk(height_mutex_);
  auto it = averages_.find(cam_name);
  if (it == averages_.end()) {
    return std::vector<double>{default_ground_position_z_mm_};
  }
  return it->second.averages_for_all_columns();
}

void GeometricHeightEstimator::toggle_estimation(bool enabled) { enabled_ = enabled; }

void GeometricHeightEstimator::set_min_max_x_mm_for_cam(GeometricCam &cam) {
  auto min_max = cam.get_min_max_abs_x_mm_for_cam();
  {
    std::lock_guard<std::mutex> lk(height_mutex_);
    min_max_x_mm_[cam.get_name()] = min_max;
  }
}

std::tuple<std::string, uint32_t> GeometricHeightEstimator::get_keys_for_abs_x(double pos_x_mm) {
  std::lock_guard<std::mutex> lk(height_mutex_);
  auto it = min_max_x_mm_.begin();
  for (; it != min_max_x_mm_.end(); ++it) {
    if (pos_x_mm >= std::get<0>(it->second) && pos_x_mm <= std::get<1>(it->second)) {
      break;
    }
  }
  if (it == min_max_x_mm_.end() || averages_.count(it->first) == 0) {
    return std::make_tuple("", 0);
  }
  auto it_avg = averages_.find(it->first);
  uint32_t index = it_avg->second.get_column_for_abs_pos_mm(pos_x_mm, it->second);
  return std::make_tuple(it->first, index);
}

double GeometricHeightEstimator::get_height_mm_for_key(std::tuple<std::string, uint32_t> key) {
  std::lock_guard<std::mutex> lk(height_mutex_);
  auto it = averages_.find(std::get<0>(key));
  if (it == averages_.end()) {
    return compute_average();
  }
  return it->second.average_for_column(std::get<1>(key));
}
double GeometricHeightEstimator::get_height_mm_for_abs_x(double pos_x_mm) {
  auto key = get_keys_for_abs_x(pos_x_mm);
  return get_height_mm_for_key(key);
}

double GeometricHeightEstimator::get_max_height_mm() { return max_height_mm_; }

} // namespace geometric
} // namespace common
} // namespace lib

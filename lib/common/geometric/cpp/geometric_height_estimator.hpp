#pragma once
#include <atomic>
#include <lib/common/average/cpp/moving_average.hpp>
#include <lib/common/cpp/utils/generation.hpp>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

#define HEIGHT_46_INCHES_MM 1170
#define HEIGHT_32_INCHES_MM 810
#define HEIGHT_36_INCHES_MM 914.4
#define HEIGHT_17_INCHES_MM 431.8
#define HEIGHT_23_INCHES_MM 584.2
#define HEIGHT_27_INCHES_MM 685.8
#define HEIGHT_30_INCHES_MM 762
#define MIN_DELTA_PX 10
#define MAX_DELTA_PX 2000
#define MIN_DELTA_MM 3
#define MAX_DELTA_MM 500
#define GLOBAL_HEIGHT_ESTIMATE_SMOOTHING_FACTOR 0.9

constexpr double IN_2_MM(double inches) { return inches * 25.4; }

namespace lib {
namespace common {
namespace geometric {

class GeometricCam;

class GeometricCamHeightCollector {
private:
  std::string id_;
  double default_ground_position_z_mm_;
  double smoothing_factor_;
  double average_;
  double min_height_mm_;
  double max_height_mm_;
  uint32_t width_px_;
  uint32_t lane_width_px_;
  std::atomic_bool dirty_;
  std::mutex height_mutex_;
  std::vector<lib::common::average::MovingAverage> averages_;
  void load_average();

public:
  GeometricCamHeightCollector(const std::string &id, double default_ground_position_z_mm, double smoothing_factor,
                              double min_height_mm, double max_height_mm, uint32_t width_px);
  void set_columns(size_t lanes);
  double average();
  double average(uint32_t pos_x_px);
  double average_for_column(uint32_t column);
  void push_datapoint(double height_mm, size_t lane = 0);
  void force_update_height(double ground_position_z_mm);
  uint32_t get_column_for_abs_pos_mm(double pos_x_mm, std::tuple<double, double> min_max_pos_mm);
  uint32_t get_column_for_pos_x_px(double pos_x_px);
  std::vector<double> averages_for_all_columns();
};

class GeometricHeightEstimator {
protected:
  std::mutex height_mutex_;
  double default_ground_position_z_mm_;
  double smoothing_factor_;
  std::unordered_map<std::string, GeometricCamHeightCollector> averages_;
  std::map<std::string, std::tuple<double, double>> min_max_x_mm_;
  double min_height_mm_;
  double max_height_mm_;
  double min_delta_px_;
  double max_delta_px_;
  double min_delta_mm_;
  double max_delta_mm_;
  bool enabled_;
  double compute_average();
  GeometricCamHeightCollector &get_average(GeometricCam &cam);

public:
  GeometricHeightEstimator(double ground_position_z_mm, double smoothing_factor, double min_height_mm,
                           double max_height_mm, double min_delta_px, double max_delta_px, double min_delta_mm,
                           double max_delta_mm);
  GeometricHeightEstimator(const GeometricHeightEstimator &) = delete;
  void configure(const std::vector<std::string> &predict_ids, int number_lanes,
                 int predict_width_px); // Needed so we can de-couple cv runtime from this as it breaks ci testing.
  void push_computed_height_datapoint(GeometricCam &cam, double height_mm, uint32_t lane = 0);
  void push_height_datapoint(GeometricCam &cam, std::tuple<double, double> delta_px,
                             std::tuple<double, double> delta_mm, uint32_t lane = 0);
  void push_height_datapoint_with_pos_x(GeometricCam &cam, std::tuple<double, double> delta_px,
                                        std::tuple<double, double> delta_mm, uint32_t pos_x_px);
  void push_computed_height_datapoint_with_pos_x(GeometricCam &cam, double height_mm, uint32_t pos_x_px);
  void force_update_height(double ground_position_z_mm);
  void toggle_estimation(bool enabled);
  double get_ground_position_z_mm();
  double get_ground_position_z_mm_by_cam(GeometricCam &cam, uint32_t pos_x_px);
  double get_ground_position_z_mm_by_cam_name(const std::string &cam_name, uint32_t pos_x_px);
  std::vector<double> get_averages_for_all_columns(GeometricCam &cam);
  void set_min_max_x_mm_for_cam(GeometricCam &cam);
  std::tuple<std::string, uint32_t> get_keys_for_abs_x(double pos_x_mm);
  double get_height_mm_for_key(std::tuple<std::string, uint32_t> key);
  double get_height_mm_for_abs_x(double pos_x_mm);
  double get_max_height_mm();
};

inline std::shared_ptr<GeometricHeightEstimator> construct_geometric_height_estimator() {
  if (carbon::common::is_slayer()) {
    return std::make_shared<GeometricHeightEstimator>(HEIGHT_27_INCHES_MM, GLOBAL_HEIGHT_ESTIMATE_SMOOTHING_FACTOR,
                                                      HEIGHT_23_INCHES_MM, HEIGHT_46_INCHES_MM, MIN_DELTA_PX,
                                                      MAX_DELTA_PX, MIN_DELTA_MM, MAX_DELTA_MM);
  } else if (carbon::common::is_reaper()) { // TODO verify this will work
    return std::make_shared<GeometricHeightEstimator>(IN_2_MM(24.5), GLOBAL_HEIGHT_ESTIMATE_SMOOTHING_FACTOR,
                                                      IN_2_MM(16.0), IN_2_MM(46.0), MIN_DELTA_PX, MAX_DELTA_PX,
                                                      MIN_DELTA_MM, MAX_DELTA_MM);
  } else {
    return std::make_shared<GeometricHeightEstimator>(HEIGHT_36_INCHES_MM, GLOBAL_HEIGHT_ESTIMATE_SMOOTHING_FACTOR,
                                                      HEIGHT_32_INCHES_MM, HEIGHT_46_INCHES_MM, MIN_DELTA_PX,
                                                      MAX_DELTA_PX, MIN_DELTA_MM, MAX_DELTA_MM);
  }
}

} // namespace geometric
} // namespace common
} // namespace lib
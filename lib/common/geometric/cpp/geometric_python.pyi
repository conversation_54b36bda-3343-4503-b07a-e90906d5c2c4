from typing import Callable, List, Optional, Tuple, overload

import torch

Tuple3D = Tuple[float, float, float]

class GeometricHeightEstimator:
    def __init__(
        self,
        ground_position_z_mm: float,
        smoothing_factor: float,
        min_height_mm: float,
        max_height_mm: float,
        min_delta_px: float,
        max_delta_px: float,
        min_delta_mm: float,
        max_delta_mm: float,
    ) -> None: ...
    def configure(self, predict_ids: List[str], number_lanes: int, predict_width_px: int) -> None: ...
    def force_update_height(self, ground_position_z_mm: float) -> None: ...
    def push_height_datapoint(
        self, cam: "GeometricCam", delta_px: Tuple[float, float], delta_mm: Tuple[float, float], lane: int = 0
    ) -> None: ...
    def toggle_estimation(self, enabled: bool) -> None: ...
    def get_ground_position_z_mm(self) -> float: ...
    def get_ground_position_z_mm_by_cam(self, cam: "GeometricCam", pos_x_px: int) -> float: ...
    def get_ground_position_z_mm_by_cam_name(self, cam_name: str, pos_x_px: int) -> float: ...
    def set_min_max_x_mm_for_cam(self, cam: "GeometricCam") -> None: ...
    def get_keys_for_abs_x(self, pos_x_mm: float) -> Tuple[str, int]: ...
    def get_height_mm_for_key(self, key: Tuple[str, int]) -> float: ...
    def get_height_mm_for_abs_x(self, pos_x_mm: float) -> float: ...
    def get_max_height_mm(self) -> float: ...

class GeometricDevice:
    def __init__(self, name: str, config_key: str, abs_position_mm: Tuple3D) -> None: ...
    def get_name(self) -> str: ...
    def get_abs_position_mm(self) -> Tuple3D: ...
    def get_offset_position_mm(self) -> Tuple3D: ...
    def override_offset_position_mm(self, offset: Tuple3D) -> None: ...

class GeometricCam(GeometricDevice):
    def __init__(
        self,
        name: str,
        abs_position_mm: Tuple3D,
        resolution: Tuple[int, int],
        focal_length_mm: float,
        top_to_vertex_mm: float,
        sensor_dimensions_mm: Tuple[float, float],
        u2d_mapx: torch.Tensor,
        u2d_mapy: torch.Tensor,
        d2u_mapx: torch.Tensor,
        d2u_mapy: torch.Tensor,
    ) -> None: ...
    def undistort_point(self, point: Tuple[float, float]) -> Tuple[float, float]: ...
    def get_sensor_mm_from_centered_px(self, pos: Tuple[float, float]) -> Tuple[float, float]: ...
    def compute_vertex_height(self, delta_px: Tuple[float, float], delta_mm: Tuple[float, float]) -> float: ...
    def compute_ground_height(self, delta_px: Tuple[float, float], delta_mm: Tuple[float, float]) -> float: ...
    def get_abs_position_from_px(self, pos: Tuple[float, float]) -> Tuple3D: ...
    def get_abs_position_from_px_from_height_estimate(self, pos: Tuple[float, float], height_mm: float) -> Tuple3D: ...
    def get_abs_position_from_undistorted_px(self, pos: Tuple[float, float]) -> Tuple3D: ...
    def get_distorted_px_from_abs_position(self, pos: Tuple3D) -> Tuple[float, float]: ...
    def resolution(self) -> Tuple[float, float]: ...
    def get_size_mm_from_size_px(self, px: float, height_mm: float) -> float: ...
    def get_size_px_from_size_mm(self, mm: float, height_mm: float) -> float: ...
    def get_abs_position_mm(self) -> Tuple3D: ...

class GeometricScanner(GeometricDevice):
    def __init__(
        self,
        name: str,
        id: int,
        abs_position_mm: Tuple3D,
        resolution: Tuple[int, int],
        focal_length_mm: float,
        top_to_vertex_mm: float,
        sensor_dimensions_mm: Tuple[float, float],
        u2d_mapx: torch.Tensor,
        u2d_mapy: torch.Tensor,
        d2u_mapx: torch.Tensor,
        d2u_mapy: torch.Tensor,
        pan_to_tilt: float,
        tilt_to_combiner: float,
        tilt_to_vertex_offset: float,
        roi_size: Tuple[int, int],
        servo_resolution: Tuple[int, int],
        delta_servo_factors: Tuple[float, float],
        angular_offset: Tuple[float, float],
    ) -> None: ...
    def set_servo_centers(self, servo_centers: Tuple[int, int]) -> None: ...
    def set_override_skews(self, pan_skew: float, tilt_skew: float) -> None: ...
    def get_servo_position_from_ground(self, pos: Tuple3D) -> Tuple[int, int]: ...
    def get_abs_position_from_servo(self, pos: Tuple[int, int]) -> Tuple3D: ...
    def get_abs_position_from_servo_with_z(self, pos: Tuple[int, int], abs_z: float) -> Tuple3D: ...
    def get_height_key_from_servo_pos(self, pos_pan: int) -> Tuple[str, int]: ...
    def get_adjusted_tilt_velocity_with_pos_from_vel(
        self, pan_pos: int, tilt_pos: int, velocity_mph: float, z_height_mm: Optional[float]
    ) -> float: ...
    def get_servo_position_for_delta_px(
        self, delta_px: Tuple[int, int], pos_servos: Tuple[int, int]
    ) -> Tuple[int, int]: ...
    def get_servo_delta_for_delta_px(
        self, delta_px: Tuple[int, int], pos_servos: Tuple[int, int]
    ) -> Tuple[int, int]: ...
    def get_servo_delta_for_crosshair(
        self, crosshair_px: Tuple[int, int], pos_servos: Tuple[int, int]
    ) -> Tuple[int, int]: ...
    def add_offset_val(
        self, pcam_id: int, pos_x: float, pos_y: float, pos_z: float, offset_x: float, offset_y: float, offset_z: float
    ) -> None: ...
    def get_offset(self, pcam_id: int, pos_x: float, pos_y: float, pos_z: float) -> Tuple3D: ...
    def add_dist(
        self, pcam_id: int, pos_x: float, pos_y: float, pos_z: float, pan_mm: float, tilt_mm: float
    ) -> None: ...
    def add_failure(self, pcam_id: int, pos_x: float, pos_y: float, pos_z: float) -> None: ...
    def get_dist_in_range(self, pcam_id: int, pos_x: float, pos_y: float, pos_z: float) -> bool: ...
    def get_center(self) -> Tuple[int, int]: ...

def get_global_height_estimator() -> GeometricHeightEstimator: ...

class GlobalHeightEstimatorCollection:
    @staticmethod
    def instance() -> "GlobalHeightEstimatorCollection": ...
    def get_height_estimator(self, size_mm: Optional[float] = None) -> GeometricHeightEstimator: ...
    def configure(self, predict_ids: List[str], number_lanes: int, predict_width_px: int) -> None: ...
    def toggle_estimation(self, enabled: bool) -> None: ...
    def set_min_max_x_mm_for_cam(self, cam: "GeometricCam") -> None: ...
    def force_update_height(self, ground_position_z_mm: float) -> None: ...

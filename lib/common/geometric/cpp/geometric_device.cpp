#include <config/client/cpp/config_subscriber.hpp>
#include <fmt/format.h>
#include <lib/common/geometric/cpp/geometric_device.hpp>

using namespace lib::common::geometric;

GeometricDevice::GeometricDevice(std::string name, std::string config_key, Tuple3d abs_position_mm)
    : name_(name), abs_position_mm_(abs_position_mm), conf_(nullptr), use_cached_offset_(true) {
  auto subscriber = carbon::config::get_global_config_subscriber();
  if (subscriber->started()) {
    auto geo_cfg = subscriber->get_config_node("aimbot", fmt::format("geometric/{}", config_key));
    for (auto child : geo_cfg->get_children_nodes()) {
      if (child->get_name() == name_) {
        conf_ = child;
      }
    }
  }
  if (conf_) {
    x_offset_ = conf_->get_node("offset/x");
    y_offset_ = conf_->get_node("offset/y");
    z_offset_ = conf_->get_node("offset/z");
    offset_position_mm_ = std::make_tuple<double, double, double>(
        x_offset_->get_value<double>(), y_offset_->get_value<double>(), z_offset_->get_value<double>());
  } else {
    use_cached_offset_ = true;
    offset_position_mm_ = std::make_tuple<double, double, double>(0, 0, 0);
  }
}

std::string GeometricDevice::get_name() { return this->name_; }
Tuple3d GeometricDevice::get_abs_position_mm() { return this->abs_position_mm_; }
Tuple3d GeometricDevice::get_offset_position_mm() { return this->offset_position_mm_; }
Tuple3d GeometricDevice::get_position_mm() {

  if (!use_cached_offset_) {
    offset_position_mm_ = std::make_tuple<double, double, double>(
        x_offset_->get_value<double>(), y_offset_->get_value<double>(), z_offset_->get_value<double>());
  }

  return std::make_tuple(std::get<0>(this->abs_position_mm_) + std::get<0>(this->offset_position_mm_),
                         std::get<1>(this->abs_position_mm_) + std::get<1>(this->offset_position_mm_),
                         std::get<2>(this->abs_position_mm_) + std::get<2>(this->offset_position_mm_));
}
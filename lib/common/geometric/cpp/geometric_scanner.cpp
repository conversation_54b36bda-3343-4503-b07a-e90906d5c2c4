#define _USE_MATH_DEFINES

#include <cmath>
#include <config/client/cpp/config_subscriber.hpp>
#include <lib/common/geometric/cpp/geometric_height_estimator.hpp>
#include <lib/common/geometric/cpp/geometric_scanner.hpp>
#include <lib/common/geometric/cpp/global_height_estimator_collection.hpp>
#include <spdlog/spdlog.h>

using namespace lib::common::geometric;

GeometricScanner::GeometricScanner(std::string name, uint32_t id, Tuple3d abs_position_mm,
                                   std::tuple<uint32_t, uint32_t> resolution, double focal_length_mm,
                                   double top_to_vertex_mm, std::tuple<double, double> sensor_dimensions_mm,
                                   torch::Tensor u2d_mapx, torch::Tensor u2d_mapy, torch::Tensor d2u_mapx,
                                   torch::Tensor d2u_mapy, float pan_to_tilt, float tilt_to_combiner,
                                   float tilt_to_vertex_offset, std::tuple<uint32_t, uint32_t> roi_size,
                                   std::tuple<uint32_t, uint32_t> servo_resolution,
                                   std::tuple<double, double> delta_servo_factors,
                                   std::tuple<double, double> angular_offset)
    : GeometricCam(name, "scanners", abs_position_mm, resolution, focal_length_mm, top_to_vertex_mm,
                   sensor_dimensions_mm, u2d_mapx, u2d_mapy, d2u_mapx, d2u_mapy),
      id_(id), pan_to_tilt_distance_mm_(pan_to_tilt), tilt_to_combiner_distance_mm_(tilt_to_combiner),
      tilt_to_vertex_offset_(tilt_to_vertex_offset), pan_skew_factor_cache_(1.0), tilt_skew_factor_cache_(1.0),
      use_override_skews_(false), use_cached_skews_cache_(true), delta_servo_factors_(delta_servo_factors),
      roi_size_(roi_size), servo_resolutions_(servo_resolution), offset_(name), roi_tree_(nullptr),
      angular_offset_(angular_offset) {
  if (conf_) {
    pan_skew_factor_ = conf_->get_node("pan_skew_factor");
    pan_skew_factor_cache_ = pan_skew_factor_->get_value<float>();
    tilt_skew_factor_ = conf_->get_node("tilt_skew_factor");
    tilt_skew_factor_cache_ = tilt_skew_factor_->get_value<float>();
    use_config_factor_ = conf_->get_node("use_config_factor");
    use_config_factor_cache_ = use_config_factor_->get_value<bool>();
    use_cached_skews_ = conf_->get_node("use_cached_skews");
    use_cached_skews_cache_ = use_cached_skews_->get_value<bool>();
  }
  auto subscriber = carbon::config::get_global_config_subscriber();
  if (subscriber->started()) {
    std::string cv_name = fmt::format("target{}", id_);
    auto cam_cfgs = subscriber->get_config_node("cv", "cameras");
    for (auto child : cam_cfgs->get_children_nodes()) {
      if (child->get_name() == cv_name) {
        auto tree = child->get_node("roi");
        if (tree) {
          roi_tree_ = std::make_unique<carbon::config::AtomicFlagConfigScopedCallback>(tree, false);
        }
        break;
      }
    }
  }
  servo_centers_ = std::make_tuple<uint32_t, uint32_t>(0, 0);
}
void GeometricScanner::_update_roi() {
  if (!roi_tree_ || !roi_tree_->reload_required()) {
    return;
  }
  roi_size_ = std::make_tuple<uint32_t, uint32_t>(
      (uint32_t)roi_tree_->tree()->get_node("height")->get_value<float>(),
      (uint32_t)roi_tree_->tree()
          ->get_node("width")
          ->get_value<float>()); // NOTE: height and width are reversed here intentionally IN CV these are pre-
                                 // rotation, but in aimbot they are post rotation

  roi_offsets_ = std::make_tuple<int32_t, int32_t>(
      (int32_t)roi_tree_->tree()->get_node("offset_y")->get_value<float>() -
          static_cast<int32_t>((std::get<0>(resolution_) - std::get<0>(roi_size_)) / 2),
      (int32_t)roi_tree_->tree()->get_node("offset_x")->get_value<float>() -
          static_cast<int32_t>((std::get<1>(resolution_) - std::get<1>(roi_size_)) /
                               2)); // NOTE: X and y are reversed here intentionally IN CV these are pre-
                                    // rotation, but in aimbot they are post rotation
}
void GeometricScanner::set_servo_centers(std::tuple<int32_t, int32_t> servo_centers) {
  auto [pan_center, tilt_center] = servo_centers;
  // divide by 2 as well to account for optical angle vs actual angle.
  double offset_pan =
      static_cast<double>(std::get<0>(servo_resolutions_)) * std::get<0>(angular_offset_) / (360.0 * 2.0);
  double offset_tilt =
      static_cast<double>(std::get<1>(servo_resolutions_)) * std::get<1>(angular_offset_) / (360.0 * 2.0);
  servo_centers_ = std::make_tuple<int32_t, int32_t>(pan_center - static_cast<int32_t>(offset_pan),
                                                     tilt_center - static_cast<int32_t>(offset_tilt));
}

std::tuple<int32_t, int32_t> GeometricScanner::get_servo_position_from_ground(Tuple3d pos) {
  auto scanner_pos = this->get_position_mm();
  double x = std::get<0>(pos) - std::get<0>(scanner_pos);
  double y = std::get<1>(pos) - std::get<1>(scanner_pos);
  double z = std::get<2>(pos) - std::get<2>(scanner_pos);
  return get_servo_position_for_relative_pos(x, y, z);
}
Tuple3d GeometricScanner::_get_abs_position_from_servo(std::tuple<int32_t, int32_t> pos, std::optional<double> abs_z) {
  double pan_optical_angle_rad =
      ((double(std::get<0>(this->servo_centers_) - std::get<0>(pos)) / double(std::get<0>(this->servo_resolutions_))) *
       (2 * M_PI)) *
      2;
  double tilt_optical_angle_rad =
      ((double(std::get<1>(this->servo_centers_) - std::get<1>(pos)) / double(std::get<1>(this->servo_resolutions_))) *
       (2 * M_PI)) *
      2;
  auto scanner_pos = this->get_position_mm();
  double abs_z_val = 0.0;
  if (abs_z) {
    abs_z_val = abs_z.value();
  } else {
    double tmp_abs_z = GlobalHeightEstimatorCollection::instance().get_height_estimator()->get_ground_position_z_mm();
    double z = tmp_abs_z - std::get<2>(scanner_pos);
    double x = std::tan(pan_optical_angle_rad) * z;
    abs_z_val = GlobalHeightEstimatorCollection::instance().get_height_estimator()->get_height_mm_for_abs_x(
        x + std::get<0>(scanner_pos));
  }
  double z = abs_z_val - std::get<2>(scanner_pos);
  double x = std::tan(pan_optical_angle_rad) * z;
  double y =
      tan(tilt_optical_angle_rad) * (std::sqrt(std::pow(z, 2) + std::pow(x, 2)) + this->pan_to_tilt_distance_mm_);
  return std::make_tuple(x + std::get<0>(scanner_pos), y + std::get<1>(scanner_pos), abs_z_val);
}
Tuple3d GeometricScanner::get_abs_position_from_servo_with_z(std::tuple<int32_t, int32_t> pos, double abs_z) {
  return _get_abs_position_from_servo(pos, abs_z);
}
std::tuple<double, double, double> GeometricScanner::get_abs_position_from_servo(std::tuple<int32_t, int32_t> pos) {
  return _get_abs_position_from_servo(pos, {});
}
std::tuple<std::string, uint32_t> GeometricScanner::get_height_key_from_servo_pos(int32_t pos_pan) {
  double pan_optical_angle_rad =
      ((double(std::get<0>(this->servo_centers_) - pos_pan) / double(std::get<0>(this->servo_resolutions_))) *
       (2 * M_PI)) /
      2;
  auto scanner_pos = this->get_position_mm();
  // Use global Z to get initial X so we can find localized Z
  double abs_z = GlobalHeightEstimatorCollection::instance().get_height_estimator()->get_ground_position_z_mm();
  double z = abs_z - std::get<2>(scanner_pos);
  double x = std::tan(pan_optical_angle_rad) * z;
  abs_z = GlobalHeightEstimatorCollection::instance().get_height_estimator()->get_height_mm_for_abs_x(
      x + std::get<0>(scanner_pos));
  z = abs_z - std::get<2>(scanner_pos);
  x = std::tan(pan_optical_angle_rad) * z;
  return GlobalHeightEstimatorCollection::instance().get_height_estimator()->get_keys_for_abs_x(
      x + std::get<0>(scanner_pos));
}

double GeometricScanner::get_adjusted_tilt_velocity_with_pos_from_vel(int pan_pos, int tilt_pos, double velocity_mph,
                                                                      std::optional<double> z_height_mm) {
  double ground_height_mm =
      z_height_mm.has_value()
          ? z_height_mm.value()
          : GlobalHeightEstimatorCollection::instance().get_height_estimator()->get_ground_position_z_mm();
  double velocity_mm_ms = 0.44704 * std::abs(velocity_mph);
  double z_pan = ground_height_mm - std::get<2>(this->get_position_mm());

  double servo_angle_position_pan =
      (std::abs(((double)pan_pos) - std::get<0>(this->servo_centers_)) / std::get<0>(this->servo_resolutions_)) * 2 *
      M_PI;
  double optical_angle_position_pan = servo_angle_position_pan * 2;

  double servo_angle_position_tilt = (std::abs((double)tilt_pos - (double)std::get<1>(this->servo_centers_)) /
                                      (double)std::get<1>(this->servo_resolutions_)) *
                                     2.0 * M_PI;
  double optical_angle_position_tilt = servo_angle_position_tilt * 2;

  double pos_x = std::tan(optical_angle_position_pan) * z_pan;
  double adjacent_len_tilt = std::sqrt(std::pow(z_pan, 2) + std::pow(pos_x, 2)) + this->pan_to_tilt_distance_mm_;
  double pos_y = std::tan(optical_angle_position_tilt) * (adjacent_len_tilt);

  double optical_angle_rad = std::atan((velocity_mm_ms + pos_y) / (adjacent_len_tilt)) - optical_angle_position_tilt;
  double servo_angle_rad = optical_angle_rad / 2;
  double servo_angle_ticks = (servo_angle_rad / (2.0 * M_PI)) * std::get<1>(this->servo_resolutions_);
  if (velocity_mph < 0) {
    servo_angle_ticks *= -1.0;
  }
  return servo_angle_ticks; // This is ticks per ms
}

std::tuple<int32_t, int32_t>
GeometricScanner::get_servo_position_for_delta_px(std::tuple<int32_t, int32_t> delta_px,
                                                  std::tuple<int32_t, int32_t> pos_servos) {
  auto delta_servo = get_servo_delta_for_delta_px(delta_px, pos_servos);
  return std::make_tuple<int32_t, int32_t>(std::get<0>(delta_servo) + std::get<0>(pos_servos),
                                           std::get<1>(delta_servo) + std::get<1>(pos_servos));
}

void GeometricScanner::set_override_skews(double pan_skew, double tilt_skew) {
  use_override_skews_ = true;
  pan_skew_override_ = pan_skew;
  tilt_skew_override_ = tilt_skew;
}

std::tuple<int32_t, int32_t> GeometricScanner::get_servo_delta_for_crosshair(std::tuple<int32_t, int32_t> crosshair_px,
                                                                             std::tuple<int32_t, int32_t> pos_servos) {
  _update_roi();
  std::tuple<int32_t, int32_t> delta_px = {
      std::get<0>(crosshair_px) + std::get<0>(roi_offsets_) - (std::get<0>(roi_size_) / 2),
      std::get<1>(crosshair_px) + std::get<1>(roi_offsets_) - (std::get<1>(roi_size_) / 2),
  };
  return get_servo_delta_for_delta_px(delta_px, pos_servos);
}

std::tuple<int32_t, int32_t> GeometricScanner::get_servo_delta_for_delta_px(std::tuple<int32_t, int32_t> delta_px,
                                                                            std::tuple<int32_t, int32_t> pos_servos) {
  (void)pos_servos; // Leaving here for future iteration
  auto [sensor_pos_mm_x, sensor_pos_mm_y] = get_sensor_mm_from_centered_px(delta_px);

  double pan_skew = std::get<0>(delta_servo_factors_);
  double tilt_skew = std::get<1>(delta_servo_factors_);

  if (use_override_skews_ && !use_config_factor_cache_) {
    pan_skew = pan_skew_override_;
    tilt_skew = tilt_skew_override_;
  } else if (!use_cached_skews_cache_ && pan_skew_factor_ != nullptr && tilt_skew_factor_ != nullptr) {
    pan_skew *= pan_skew_factor_->get_value<float>();
    tilt_skew *= tilt_skew_factor_->get_value<float>();
  } else {
    pan_skew *= pan_skew_factor_cache_;
    tilt_skew *= tilt_skew_factor_cache_;
  }

  double pan_delta_theta = atan(sensor_pos_mm_x / focal_length_mm_) / pan_skew;
  double tilt_delta_theta = atan(sensor_pos_mm_y / focal_length_mm_) / tilt_skew;

  auto delta_servos = std::make_tuple<int32_t, int32_t>(
      (int32_t)round(pan_delta_theta / (2 * M_PI) * double(std::get<0>(servo_resolutions_))),
      (int32_t)round(tilt_delta_theta / (2 * M_PI) * double(std::get<1>(servo_resolutions_))));
  return delta_servos;
}

std::tuple<int32_t, int32_t> GeometricScanner::get_servo_position_for_relative_pos(double x, double y, double z) {
  double pan_optical_angle_rad = std::atan(x / z);
  double tilt_optical_angle_rad =
      std::atan(y / (std::sqrt(std::pow(z, 2) + std::pow(x, 2)) + this->pan_to_tilt_distance_mm_));
  int32_t pan = (int32_t)round(-(((pan_optical_angle_rad / 2) / (2 * M_PI)) * std::get<0>(this->servo_resolutions_)) +
                               std::get<0>(this->servo_centers_));
  int32_t tilt = (int32_t)round(-(((tilt_optical_angle_rad / 2) / (2 * M_PI)) * std::get<1>(this->servo_resolutions_)) +
                                std::get<1>(this->servo_centers_));
  return std::make_tuple(pan, tilt);
}

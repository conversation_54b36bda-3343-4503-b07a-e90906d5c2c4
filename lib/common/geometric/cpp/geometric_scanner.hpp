#pragma once
#include <config/tree/cpp/config_scoped_callback.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <lib/common/geometric/cpp/geometric_cam.hpp>
#include <lib/common/geometric/cpp/geometric_scanner_offset.hpp>

#include <memory>
#include <optional>
#include <torch/torch.h>
#include <tuple>

namespace lib {
namespace common {
namespace geometric {

class GeometricScanner : public GeometricCam {
protected:
  uint32_t id_;
  double pan_to_tilt_distance_mm_;
  double tilt_to_combiner_distance_mm_;
  double tilt_to_vertex_offset_;
  std::shared_ptr<carbon::config::ConfigTree> pan_skew_factor_;
  std::shared_ptr<carbon::config::ConfigTree> tilt_skew_factor_;
  std::shared_ptr<carbon::config::ConfigTree> use_config_factor_;
  std::shared_ptr<carbon::config::ConfigTree> use_cached_skews_;
  double pan_skew_factor_cache_;
  double tilt_skew_factor_cache_;
  double use_config_factor_cache_;
  double pan_skew_override_;
  double tilt_skew_override_;
  double use_override_skews_;
  bool use_cached_skews_cache_;
  std::tuple<double, double> delta_servo_factors_;
  std::tuple<uint32_t, uint32_t> roi_size_;
  std::tuple<uint32_t, uint32_t> servo_resolutions_;
  std::tuple<int32_t, int32_t> roi_offsets_;
  std::tuple<int32_t, int32_t> servo_centers_;
  GeometricScannerOffset offset_;
  std::unique_ptr<carbon::config::AtomicFlagConfigScopedCallback> roi_tree_;
  std::tuple<double, double> angular_offset_;

  std::tuple<int32_t, int32_t> get_servo_position_for_relative_pos(double x, double y, double z);
  void _update_roi();

public:
  GeometricScanner(std::string name, uint32_t id, Tuple3d abs_position_mm, std::tuple<uint32_t, uint32_t> resolution,
                   double focal_length_mm, double top_to_vertex_mm, std::tuple<double, double> sensor_dimensions_mm,
                   torch::Tensor u2d_mapx, torch::Tensor u2d_mapy, torch::Tensor d2u_mapx, torch::Tensor d2u_mapy,
                   float pan_to_tilt, float tilt_to_combiner, float tilt_to_vertex_offset,
                   std::tuple<uint32_t, uint32_t> roi_size, std::tuple<uint32_t, uint32_t> servo_resolution,
                   std::tuple<double, double> delta_servo_factors, std::tuple<double, double> angular_offset);
  void set_servo_centers(std::tuple<int32_t, int32_t> servo_centers);
  void set_override_skews(double pan_skew, double tilt_skew);
  Tuple3d _get_abs_position_from_servo(std::tuple<int32_t, int32_t> pos, std::optional<double> abs_z);

  std::tuple<int32_t, int32_t> get_servo_position_from_ground(Tuple3d pos);
  Tuple3d get_abs_position_from_servo(std::tuple<int32_t, int32_t> pos);
  Tuple3d get_abs_position_from_servo_with_z(std::tuple<int32_t, int32_t> pos, double abs_z);
  std::tuple<std::string, uint32_t> get_height_key_from_servo_pos(int32_t pos_pan);
  double get_adjusted_tilt_velocity_with_pos_from_vel(int pan_pos, int tilt_pos, double velocity_mph,
                                                      std::optional<double> z_height_mm);
  std::tuple<int32_t, int32_t> get_servo_position_for_delta_px(std::tuple<int32_t, int32_t> delta_px,
                                                               std::tuple<int32_t, int32_t> pos_servos);

  std::tuple<int32_t, int32_t> get_servo_delta_for_delta_px(std::tuple<int32_t, int32_t> delta_px,
                                                            std::tuple<int32_t, int32_t> pos_servos);
  std::tuple<int32_t, int32_t> get_servo_delta_for_crosshair(std::tuple<int32_t, int32_t> crosshair_px,
                                                             std::tuple<int32_t, int32_t> pos_servos);

  inline std::tuple<float, float, float> get_offset(uint32_t pcam_id, float pos_x, float pos_y, float pos_z) {
    return offset_.get(pcam_id, pos_x, pos_y, pos_z);
  }
  inline void add_offset_val(uint32_t pcam_id, float pos_x, float pos_y, float pos_z, float offset_x, float offset_y,
                             float offset_z) {
    return offset_.add_offset_val(pcam_id, pos_x, pos_y, pos_z, offset_x, offset_y, offset_z);
  }
  inline void add_dist(uint32_t pcam_id, float pos_x, float pos_y, float pos_z, float pan_mm, float tilt_mm) {
    offset_.add_avg_dist(pcam_id, pos_x, pos_y, pos_z, pan_mm, tilt_mm);
  }
  inline void add_failure(uint32_t pcam_id, float pos_x, float pos_y, float pos_z) {
    offset_.add_failure(pcam_id, pos_x, pos_y, pos_z);
  }
  inline bool get_dist_in_range(uint32_t pcam_id, float pos_x, float pos_y, float pos_z) const {
    return offset_.get_dist_in_range(pcam_id, pos_x, pos_y, pos_z);
  }
  inline void percentiles(std::vector<GeometricScannerOffset::PercentileTuple> *data_out) const {
    return offset_.percentiles(data_out);
  }
  inline void offsets(std::vector<GeometricScannerOffset::OffsetsTuple> *data_out) const {
    return offset_.offsets(data_out);
  }
  inline std::tuple<int32_t, int32_t> get_center() const { return servo_centers_; }
};
} // namespace geometric
} // namespace common
} // namespace lib
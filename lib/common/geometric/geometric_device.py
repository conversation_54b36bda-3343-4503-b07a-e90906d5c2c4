from typing import Tuple


class GeometricDevice:
    def __init__(
        self, name: str, abs_position_mm: <PERSON><PERSON>[float, float, float], offset_position_mm: <PERSON><PERSON>[float, float, float]
    ) -> None:
        self._name = name
        self._abs_position_mm = abs_position_mm
        self._offset_position_mm = offset_position_mm

    @property
    def name(self) -> str:
        return self._name

    @property
    def abs_position_mm(self) -> <PERSON><PERSON>[float, float, float]:
        return self._abs_position_mm

    @property
    def offset_position_mm(self) -> <PERSON><PERSON>[float, float, float]:
        return self._offset_position_mm

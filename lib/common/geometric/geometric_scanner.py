import math
from typing import <PERSON>ple

from lib.common.geometric.geometric_device import GeometricDevice


class GeometricScanner(GeometricDevice):
    def __init__(
        self,
        name: str,
        abs_position_mm: Tuple[float, float, float],
        offset_position_mm: Tuple[float, float, float],
        servo_middles: Tuple[int, int],
        servo_resolutions: Tuple[int, int],
        pan_to_tilt_distance_mm: float,
        exit_window_thickness_mm: float,
        exit_window_refraction_index: float,
        exit_window_to_pan_distance_mm: float,
    ) -> None:
        super().__init__(name, abs_position_mm, offset_position_mm)
        self._servo_middles = servo_middles
        self._servo_resolutions = servo_resolutions
        self._pan_to_tilt_distance_mm = pan_to_tilt_distance_mm
        self._exit_window_thickness_mm = exit_window_thickness_mm
        self._exit_window_refraction_index = exit_window_refraction_index
        self._exit_window_to_pan_distance_mm = exit_window_to_pan_distance_mm

    def get_servo_position_from_ground(self, pos: Tuple[float, float, float]) -> Tuple[int, int]:
        x = pos[0] - self._abs_position_mm[0] - self._offset_position_mm[0]
        y = pos[1] - self._abs_position_mm[1] - self._offset_position_mm[1]
        z = (
            pos[2]
            - self._abs_position_mm[2]
            - self._offset_position_mm[2]
            + self._exit_window_thickness_mm
            + self._exit_window_to_pan_distance_mm
        )

        pan_optical_angle_rad = math.atan(x / z)
        tilt_optical_angle_rad = math.atan(y / (math.sqrt(z ** 2 + x ** 2) + self._pan_to_tilt_distance_mm))

        pan = -(((pan_optical_angle_rad / 2) / (2 * math.pi)) * self._servo_resolutions[0]) + self._servo_middles[0]
        tilt = -(((tilt_optical_angle_rad / 2) / (2 * math.pi)) * self._servo_resolutions[1]) + self._servo_middles[1]
        return round(pan), round(tilt)

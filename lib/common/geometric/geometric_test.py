import math
from typing import Generator, <PERSON><PERSON>, cast

import numpy as np
import pytest

from config.client.cpp.config_client_python import get_computer_config_prefix, get_global_config_subscriber
from lib.common.generation import GENERATION
from lib.common.geometric import geometric_calibration
from lib.common.geometric.cpp.geometric_python import GeometricCam, GeometricScanner, get_global_height_estimator
from lib.common.geometric.geometric_space import GeometricSpace

PREDICT_TOLERANCE_MM = 3
PREDICT_TOLERANCE_PX = 10
TICKS_PER_MM = 25
DELTA_PX_TOLERANCE = 5

TEST_27_IN_HEIGHT_MM = 1094.521
TEST_30_5_IN_HEIGHT_MM = 1183.421


@pytest.fixture(scope="session")
def space() -> Generator[GeometricSpace, None, None]:
    config_subscriber = get_global_config_subscriber()
    config_subscriber.add_config_tree("aimbot", f"{get_computer_config_prefix()}/aimbot", "services/aimbot.yaml")
    predict_cal = geometric_calibration.get_predict_cam_calibration(GENERATION.BUD)  # Always use bud here
    scanner_cal = geometric_calibration.get_target_cam_calibration(GENERATION.BUD)  # Always use bud here
    # from budtb
    predict_cal.focal_length = 12
    predict_cal.top_to_vertex = -7.771
    space = GeometricSpace(predict_cal=predict_cal, scanner_cal=scanner_cal)
    space.load_devices()
    predict2 = space.get_device(GeometricCam, "predict2")
    predict2.override_offset_position_mm((12.75087019, 1.29949641, 0))  # from budtb
    yield space


def predict_euclidean_delta(
    space: GeometricSpace, p1_point: Tuple[float, float], p2_point: Tuple[float, float], height: float
) -> float:
    predict1 = space.get_device(GeometricCam, "predict1")
    predict2 = space.get_device(GeometricCam, "predict2")

    get_global_height_estimator().force_update_height(height)

    p1_abs_point = predict1.get_abs_position_from_undistorted_px(p1_point)
    p2_abs_point = predict2.get_abs_position_from_undistorted_px(p2_point)

    return float(np.linalg.norm(np.array(p1_abs_point) - np.array(p2_abs_point)))


def _test_predict_points(
    space: GeometricSpace, p1_point: Tuple[float, float], p2_point: Tuple[float, float], height: float
) -> None:
    delta = predict_euclidean_delta(space, p1_point, p2_point, height)
    assert delta <= PREDICT_TOLERANCE_MM


def test_1_predict_points(space: GeometricSpace) -> None:
    _test_predict_points(space, (2654, 994), (1189, 996), TEST_27_IN_HEIGHT_MM)


def test_2_predict_points(space: GeometricSpace) -> None:
    _test_predict_points(space, (2246, 1523), (780, 1520), TEST_27_IN_HEIGHT_MM)


def test_3_predict_points(space: GeometricSpace) -> None:
    _test_predict_points(space, (1928, 1146), (460, 1140), TEST_27_IN_HEIGHT_MM)


def test_4_predict_points(space: GeometricSpace) -> None:
    _test_predict_points(space, (2480, 1210), (1174, 1212), TEST_30_5_IN_HEIGHT_MM)


def test_5_predict_points(space: GeometricSpace) -> None:
    _test_predict_points(space, (2105, 1669), (796, 1667), TEST_30_5_IN_HEIGHT_MM)


def test_height_computation(space: GeometricSpace) -> None:
    predict1 = space.get_device(GeometricCam, "predict1")
    expected_distance_mm = np.linalg.norm(np.array((50, 50, 0)) - np.array((0, 0, 0))).item()
    vertex_height = predict1.compute_vertex_height(
        cast(Tuple[float, float], tuple(np.array((1924, 1601)) - np.array((2122, 1815)))), (0, expected_distance_mm)
    )
    expected_height_mm = 841
    assert math.isclose(
        vertex_height, expected_height_mm, abs_tol=PREDICT_TOLERANCE_MM
    ), f"{vertex_height} is not close to {expected_height_mm}"


def test_ground_height_computation(space: GeometricSpace) -> None:
    predict3 = space.get_device(GeometricCam, "predict3")
    expected_distance_mm = 24.093380768872635
    height = predict3.compute_ground_height((12.83, 210.81597805023193), (0, expected_distance_mm))
    expected_height_mm = 739
    assert math.isclose(
        height, expected_height_mm, abs_tol=PREDICT_TOLERANCE_MM
    ), f"{height} is not close to {expected_height_mm}"


def _test_servo_point(
    space: GeometricSpace, p_point: Tuple[float, float], s_point: Tuple[int, int], height: float
) -> None:
    predict1 = space.get_device(GeometricCam, "predict1")
    scanner1 = space.get_device(GeometricScanner, "scanner1")

    get_global_height_estimator().force_update_height(height)

    p_abs_point = predict1.get_abs_position_from_px(p_point)
    s_servo_point = scanner1.get_servo_position_from_ground(p_abs_point)
    delta = np.linalg.norm(np.array(s_servo_point) - np.array(s_point))

    assert delta <= PREDICT_TOLERANCE_MM * TICKS_PER_MM, f"Expected: {s_point}, Actual: {s_servo_point}"


def test_servo_point_1(space: GeometricSpace) -> None:
    _test_servo_point(space, (500, 1000), (3233, 7564), TEST_30_5_IN_HEIGHT_MM)


def test_servo_point_2(space: GeometricSpace) -> None:
    _test_servo_point(space, (100, 2372), (5040, 1537), TEST_30_5_IN_HEIGHT_MM)


def test_servo_point_3(space: GeometricSpace) -> None:
    _test_servo_point(space, (620, 1564), (2636, 5183), TEST_30_5_IN_HEIGHT_MM)


def _test_servo_pos_from_px_delta(
    space: GeometricSpace, delta_px: Tuple[int, int], pos_servos: Tuple[int, int], pos_expected: Tuple[int, int]
) -> None:
    scanner1 = space.get_device(GeometricScanner, "scanner1")
    scanner1.set_servo_centers((5200, 5200))
    pos_output = scanner1.get_servo_position_for_delta_px(delta_px, pos_servos)
    delta = np.linalg.norm(np.array(pos_output) - np.array(pos_expected))
    assert delta <= DELTA_PX_TOLERANCE, f"Expected pos: {pos_expected}, actual pos: {pos_output}"


def test_servo_pos_from_px_delta_point_1(space: GeometricSpace) -> None:
    _test_servo_pos_from_px_delta(space, (-146, -197), (6491, 7865), (5893, 7101))


def test_servo_pos_from_px_delta_point_2(space: GeometricSpace) -> None:
    _test_servo_pos_from_px_delta(space, (290, 290), (6400, 5071), (7586, 6195))


def test_servo_pos_from_px_delta_point_3(space: GeometricSpace) -> None:
    _test_servo_pos_from_px_delta(space, (-218, 303), (8844, 2288), (7952, 3462))


def test_servo_pos_from_px_delta_point_4(space: GeometricSpace) -> None:
    _test_servo_pos_from_px_delta(space, (-136, -55), (4072, 4297), (3515, 4084))


def _test_get_size_mm_from_size_px(space: GeometricSpace, px: float, height_mm: float, expected_size_mm: float) -> None:
    predict1 = space.get_device(GeometricCam, "predict1")
    size_mm = predict1.get_size_mm_from_size_px(px, height_mm)
    assert math.isclose(
        size_mm, expected_size_mm, abs_tol=PREDICT_TOLERANCE_MM
    ), f"{size_mm} is not close to {expected_size_mm}"


def test_get_size_mm_from_size_px_1(space: GeometricSpace) -> None:
    _test_get_size_mm_from_size_px(space, 148, 650, 13)


def test_get_size_mm_from_size_px_2(space: GeometricSpace) -> None:
    _test_get_size_mm_from_size_px(space, 148, 800, 19.5)


def test_get_size_mm_from_size_px_3(space: GeometricSpace) -> None:
    _test_get_size_mm_from_size_px(space, 30, 650, 2.6)


def test_get_size_mm_from_size_px_4(space: GeometricSpace) -> None:
    _test_get_size_mm_from_size_px(space, 148, 0.0, 35.8)


def _test_get_size_px_from_size_mm(space: GeometricSpace, mm: float, height_mm: float, expected_size_px: float) -> None:
    predict1 = space.get_device(GeometricCam, "predict1")
    size_px = predict1.get_size_px_from_size_mm(mm, height_mm)
    assert math.isclose(
        size_px, expected_size_px, abs_tol=PREDICT_TOLERANCE_PX
    ), f"{size_px} is not close to {expected_size_px}"


def test_get_size_px_from_size_mm_1(space: GeometricSpace) -> None:
    _test_get_size_px_from_size_mm(space, 20, 650, 226.7)


def test_get_size_px_from_size_mm_2(space: GeometricSpace) -> None:
    _test_get_size_px_from_size_mm(space, 20, 800, 152.1)


def test_get_size_px_from_size_mm_3(space: GeometricSpace) -> None:
    _test_get_size_px_from_size_mm(space, 3, 800, 22.8)


def test_get_size_px_from_size_mm_4(space: GeometricSpace) -> None:
    _test_get_size_px_from_size_mm(space, 20, 0.0, 82.6)


def test_mm_to_px_to_mm(space: GeometricSpace) -> None:
    mm = 20
    height_mm = 650
    predict1 = space.get_device(GeometricCam, "predict1")
    size_px = predict1.get_size_px_from_size_mm(mm, height_mm)
    size_mm = predict1.get_size_mm_from_size_px(size_px, height_mm)
    assert math.isclose(mm, size_mm, abs_tol=PREDICT_TOLERANCE_MM), f"{mm} is not close to {size_mm}"

from typing import Any, Dict, List, Optional, <PERSON><PERSON>

import cv2
import numpy as np
import numpy.typing as npt
import torch

from lib.common.generation import GENERATION, optional_gen_to_gen

Pos2F = Tuple[float, float]
Pos3F = Tuple[float, float, float]


class GeometricCamCalibration:
    def __init__(
        self,
        camera_matrix: npt.NDArray[Any],
        dist_coeff: npt.NDArray[Any],
        image_size: Tuple[int, int],
        sensor_size: <PERSON>ple[float, float],
        focal_length: float,
        top_to_vertex: float,
    ):
        self.camera_matrix = camera_matrix
        self.dist_coeff = dist_coeff
        self.image_size = image_size
        self.sensor_size = sensor_size
        self.focal_length = focal_length
        self.top_to_vertex = top_to_vertex

    def build_maps(self) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        (width, height) = self.image_size
        refined_camera_matrix, _ = cv2.getOptimalNewCameraMatrix(
            self.camera_matrix, self.dist_coeff, self.image_size, 1, self.image_size
        )

        u2d_mapx, u2d_mapy = cv2.initUndistortRectifyMap(  # type: ignore
            self.camera_matrix, self.dist_coeff, None, refined_camera_matrix, self.image_size, 5
        )

        distorted_points = np.zeros((width * height, 1, 2), dtype=float)

        indices = np.indices((height, width))
        distorted_points[:, 0, 0] = np.resize(indices[1], (height * width))
        distorted_points[:, 0, 1] = np.resize(indices[0], (height * width))

        undistorted_points = cv2.undistortPoints(
            distorted_points, self.camera_matrix, self.dist_coeff, None, refined_camera_matrix
        )

        assert u2d_mapx is not None
        assert u2d_mapy is not None

        d2u_mapx = np.zeros_like(u2d_mapx)
        d2u_mapy = np.zeros_like(u2d_mapy)

        d2u_mapx = np.resize(undistorted_points[:, 0, 0], (height, width))
        d2u_mapy = np.resize(undistorted_points[:, 0, 1], (height, width))
        return (
            torch.from_numpy(u2d_mapx),
            torch.from_numpy(u2d_mapy),
            torch.from_numpy(d2u_mapx),
            torch.from_numpy(d2u_mapy),
        )


class GeometricScannerCalibration(GeometricCamCalibration):
    def __init__(
        self,
        pan_to_tilt: float,
        tilt_to_combiner: float,
        combiner_to_cam: float,
        vertex_offset: float,
        roi_size: Tuple[int, int],
        servo_resolution: Tuple[int, int],
        delta_servo_factors: Tuple[float, float],
        **kwargs: Any
    ):
        super().__init__(**kwargs)
        self.pan_to_tilt = pan_to_tilt
        self.tilt_to_combiner = tilt_to_combiner
        self.combiner_to_cam = combiner_to_cam
        self.vertex_offset = vertex_offset
        self.roi_size = roi_size
        self.servo_resolution = servo_resolution
        self.delta_servo_factors = delta_servo_factors


_CAMERA_CALIBRATIONS = {
    GENERATION.BUD: GeometricCamCalibration(
        camera_matrix=np.array(
            [
                [1006.3710589605587, 0.0, 1621.6664178214266],
                [0.0, 1006.3710589605587, 2002.343597445625],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeff=np.array(
            [
                -0.552499191577874,
                0.07693273039211276,
                0.00020447888247918274,
                -0.0008494952428191154,
                0.000323338690178194,
                -0.5456589134113623,
                0.07312978352182732,
                0.0008639958352123348,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
            ]
        ),
        image_size=(3000, 4096),
        sensor_size=(10.3776, 14.1864),
        focal_length=16,
        top_to_vertex=-23.292,
    ),
    GENERATION.SLAYER: GeometricCamCalibration(
        camera_matrix=np.array(
            [
                [1006.8596609987492, 0.0, 2213.1361596237657],
                [0.0, 1006.8596609987492, 1635.7397296767194],
                [0.0, 0.0, 1.0],
            ]
        ),
        dist_coeff=np.array(
            [
                0.36242610759266347,
                0.011046237292746433,
                -0.0005676734136916729,
                -0.000831387913648286,
                -0.0007575470947153185,
                0.3691801677550639,
                0.012950732527897201,
                -0.0005677492012479488,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
            ]
        ),
        image_size=(4096, 3000),
        sensor_size=(14.1864, 10.3776),
        focal_length=16,
        top_to_vertex=-26.643,
    ),
    GENERATION.REAPER: GeometricCamCalibration(
        camera_matrix=np.array(
            [[3432.94473671, 0.0, 2179.54878509], [0.0, 3432.94473671, 1485.1083119], [0.0, 0.0, 1.0]]
        ),
        dist_coeff=np.array(
            [
                -3.83510556,
                26.22968881,
                0.00090246,
                -0.00388574,
                -2.7041084,
                -3.72115075,
                25.88156482,
                -0.01106616,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
            ]
        ),
        image_size=(4096, 3000),
        sensor_size=(14.1864, 10.3776),
        focal_length=16,
        top_to_vertex=-24.419200088060506,
    ),
}
_SCANNER_CALIBRATIONS = {
    GENERATION.BUD: GeometricScannerCalibration(
        camera_matrix=np.array(
            [[969.9442287886664, 0.0, 833.4156289527002], [0.0, 969.9442287886664, 734.9228817490266], [0.0, 0.0, 1.0]]
        ),
        dist_coeff=np.array(
            [
                -0.022368620505597188,
                -0.09224143142747435,
                -0.0009524314332388872,
                -0.004698580253370263,
                -0.18739580804864384,
                -0.019603449914601775,
                -0.09661573481048537,
                -0.1833176539291289,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
            ]
        ),
        image_size=(1024, 1280),
        sensor_size=(4.9, 6.1),
        focal_length=25,
        top_to_vertex=0,
        pan_to_tilt=23,
        tilt_to_combiner=30,
        combiner_to_cam=0,
        vertex_offset=21,
        roi_size=(800, 1280),
        servo_resolution=(2 ** 18, 2 ** 18),
        delta_servo_factors=(1.95, 2.05),
    ),
    GENERATION.SLAYER: GeometricScannerCalibration(
        camera_matrix=np.array(
            [[835.2529059815123, 0.0, 705.9639115790128], [0.0, 835.2529059815123, 920.279138297855], [0.0, 0.0, 1.0]]
        ),
        dist_coeff=np.array(
            [
                -1.1763031213082955,
                -7.9675727006140535,
                0.003082889188609472,
                -0.0032775407011810345,
                -121.39782129638427,
                -0.6376005065151558,
                -14.60870757365073,
                -100.77611589877257,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
            ]
        ),
        image_size=(1464, 1936),
        sensor_size=(6.588, 8.712),
        focal_length=25,
        top_to_vertex=0,
        pan_to_tilt=24,
        tilt_to_combiner=40,
        combiner_to_cam=36.576,
        vertex_offset=0,
        roi_size=(800, 1280),
        servo_resolution=(2 ** 18, 2 ** 18),
        delta_servo_factors=(1.90, 1.94),
    ),
    GENERATION.REAPER: GeometricScannerCalibration(
        camera_matrix=np.array(
            [[835.2529059815123, 0.0, 705.9639115790128], [0.0, 835.2529059815123, 920.279138297855], [0.0, 0.0, 1.0]]
        ),
        dist_coeff=np.array(
            [
                -1.1763031213082955,
                -7.9675727006140535,
                0.003082889188609472,
                -0.0032775407011810345,
                -121.39782129638427,
                -0.6376005065151558,
                -14.60870757365073,
                -100.77611589877257,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
            ]
        ),
        image_size=(1464, 1936),
        sensor_size=(6.588, 8.712),
        focal_length=25,
        top_to_vertex=0,
        pan_to_tilt=50,
        tilt_to_combiner=40.93,
        combiner_to_cam=28.21,
        vertex_offset=0,
        roi_size=(800, 1280),
        servo_resolution=(2 ** 18, 2 ** 18),
        delta_servo_factors=(1.64, 1.77),
    ),
}

_PREDICT_POS: Dict[GENERATION, List[Pos3F]] = {
    GENERATION.BUD: [
        (660.4, 2224.272, 336.35),
        (965.2, 2224.272, 336.35),
        (1676.4, 2224.272, 336.35),
        (1981.2, 2224.272, 336.35),
    ],
    GENERATION.SLAYER: [(2000, 2000.0, 0.0), (2454.2, 2000.0, 0.0), (2911.4, 2000.0, 0.0), (3368.6, 2000.0, 0.0)],
    # TODO Figure out if I want to add offsets to keep things positive
    GENERATION.REAPER: [(0, 0, 0)],
}

_SCANNER_POS: Dict[GENERATION, List[Tuple[Pos3F, Pos2F]]] = {
    GENERATION.BUD: [
        ((584.20, 2376.67191702, 167.64), (0.0, 0.0)),
        ((736.60, 2376.67191702, 167.64), (0.0, 0.0)),
        ((889.00, 2376.67191702, 167.64), (0.0, 0.0)),
        ((1041.4, 2376.67191702, 167.64), (0.0, 0.0)),
        ((1600.2, 2376.67191702, 167.64), (0.0, 0.0)),
        ((1752.6, 2376.67191702, 167.64), (0.0, 0.0)),
        ((1905.0, 2376.67191702, 167.64), (0.0, 0.0)),
        ((2057.4, 2376.67191702, 167.64), (0.0, 0.0)),
    ],
    GENERATION.SLAYER: [
        # Since we are using predict cam body not lens as 0 point we need to offset Z height
        # by length of predict lens (67.5) compared to refrence document
        #  https://docs.google.com/spreadsheets/d/1QgHz2eCV0iC7abvgsSatxmE2Dy-xH8F6lGzAwO_NbwA/edit#gid=1574815929
        ((1923.8, 2508.0, -275.4), (0.0, 0.0)),
        ((2076.2, 2508.0, -275.4), (0.0, 0.0)),
        ((2228.6, 2508.0, -275.4), (0.0, 0.0)),
        ((2457.2, 2508.0, -275.4), (0.0, 0.0)),
        ((2609.6, 2508.0, -275.4), (0.0, 0.0)),
        ((2767.0, 2508.0, -275.4), (0.0, 0.0)),
        ((2914.4, 2508.0, -275.4), (0.0, 0.0)),
        ((3143.0, 2508.0, -275.4), (0.0, 0.0)),
        ((3295.4, 2508.0, -275.4), (0.0, 0.0)),
        ((3447.8, 2508.0, -275.4), (0.0, 0.0)),
    ],
    GENERATION.REAPER: [((-70.64, 558.8, -92.24), (-2.75, 0.0)), ((70.64, 558.8, -92.24), (2.75, 0.0)),],
}

_HEIGHT_TO_LIGHTS_MM_OFFSET: Dict[GENERATION, float] = {
    GENERATION.BUD: -359,
    GENERATION.SLAYER: -3.75,
    GENERATION.REAPER: -586.75,  # TODO figure this number out
}


def get_predict_cam_calibration(gen: Optional[GENERATION] = None) -> GeometricCamCalibration:
    return _CAMERA_CALIBRATIONS[optional_gen_to_gen(gen)]


def get_target_cam_calibration(gen: Optional[GENERATION] = None) -> GeometricScannerCalibration:
    return _SCANNER_CALIBRATIONS[optional_gen_to_gen(gen)]


def get_predict_cam_pos(gen: Optional[GENERATION] = None) -> List[Pos3F]:
    return _PREDICT_POS[optional_gen_to_gen(gen)]


def get_target_cam_pos(gen: Optional[GENERATION] = None) -> List[Tuple[Pos3F, Pos2F]]:
    return _SCANNER_POS[optional_gen_to_gen(gen)]


def get_height_to_lights_mm_offset(gen: Optional[GENERATION] = None) -> float:
    return _HEIGHT_TO_LIGHTS_MM_OFFSET[optional_gen_to_gen(gen)]

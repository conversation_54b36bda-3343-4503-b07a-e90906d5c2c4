class HeightEstimator:
    def __init__(self, ground_position_z_mm: float) -> None:
        self._ground_position_z_mm = ground_position_z_mm

    def update_height(self, ground_position_z_mm: float) -> None:
        self._ground_position_z_mm = ground_position_z_mm

    @property
    def ground_position_z_mm(self) -> float:
        return self._ground_position_z_mm


temporary_global_height_estimator = HeightEstimator(1183.421)

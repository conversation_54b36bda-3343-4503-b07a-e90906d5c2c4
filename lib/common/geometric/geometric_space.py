from threading import Lock
from typing import Dict, Iterable, List, Optional, Tuple, Type, TypeVar, cast

import lib.common.logging
from lib.common.error import MakaException
from lib.common.generation import GENERATION, is_reaper
from lib.common.geometric import geometric_calibration
from lib.common.geometric.cpp.geometric_python import GeometricCam, GeometricDevice, GeometricScanner

LOG = lib.common.logging.get_logger(__name__)

T = TypeVar("T", bound=GeometricDevice)


class GeometricException(MakaException):
    pass


class GeometricSpace:
    def __init__(
        self,
        gen: Optional[GENERATION] = None,
        predict_pos: Optional[List[Tuple[float, float, float]]] = None,
        predict_cal: Optional[geometric_calibration.GeometricCamCalibration] = None,
        scanner_pos: Optional[List[Tuple[Tuple[float, float, float], Tuple[float, float]]]] = None,
        scanner_cal: Optional[geometric_calibration.GeometricScannerCalibration] = None,
    ) -> None:
        self._geometric_devices: Dict[str, GeometricDevice] = {}
        self._predict_pos = geometric_calibration.get_predict_cam_pos(gen)
        self._predict_cal = geometric_calibration.get_predict_cam_calibration(gen)
        self._scanner_pos = geometric_calibration.get_target_cam_pos(gen)
        self._scanner_cal = geometric_calibration.get_target_cam_calibration(gen)
        if predict_pos is not None:
            self._predict_pos = predict_pos
        if predict_cal is not None:
            self._predict_cal = predict_cal
        if scanner_pos is not None:
            self._scanner_pos = scanner_pos
        if scanner_cal is not None:
            self._scanner_cal = scanner_cal

    def get_device(self, device_type: Type[T], name: str) -> T:
        device = self._geometric_devices.get(name)
        if not isinstance(device, device_type):
            raise GeometricException(f"Device Not Found: {name} in {list(self._geometric_devices.keys())}")
        return device

    def get_devices_by_type(self, device_type: Type[T]) -> Iterable[T]:
        return filter(
            lambda device: isinstance(device, device_type), cast(Iterable[T], self._geometric_devices.values())
        )

    def append_offsets_for_reaper(self, offsets_mm: List[float]) -> None:
        LOG.info(f"Appending offsets for Reaper: {offsets_mm}")
        new_predict_pos = []
        new_scanner_pos = []
        for offset in offsets_mm:
            for pos in self._predict_pos:
                new_predict_pos.append((pos[0] + offset, pos[1], pos[2]))
            for pos_scanner in self._scanner_pos:
                new_scanner_pos.append(
                    ((pos_scanner[0][0] + offset, pos_scanner[0][1], pos_scanner[0][2]), pos_scanner[1])
                )
        for pos in new_predict_pos:
            self._predict_pos.append(pos)
        for pos_scanner in new_scanner_pos:
            self._scanner_pos.append(pos_scanner)

    def load_devices(self, offsets_mm: Optional[List[float]] = None) -> None:
        # add more cams for reaper
        if is_reaper() and offsets_mm is not None:
            self.append_offsets_for_reaper(offsets_mm)

        for i, pos in enumerate(self._predict_pos):
            name = f"predict{i + 1}"
            cal = self._predict_cal
            u2d_mapx, u2d_mapy, d2u_mapx, d2u_mapy = cal.build_maps()
            self._geometric_devices[name] = GeometricCam(
                name,
                pos,
                cal.image_size,
                cal.focal_length,
                cal.top_to_vertex,
                cal.sensor_size,
                u2d_mapx,
                u2d_mapy,
                d2u_mapx,
                d2u_mapy,
            )
        for i, pos_scanner in enumerate(self._scanner_pos):
            name = f"scanner{i + 1}"
            cal = self._scanner_cal
            u2d_mapx, u2d_mapy, d2u_mapx, d2u_mapy = cal.build_maps()
            self._geometric_devices[name] = GeometricScanner(
                name,
                i + 1,
                pos_scanner[0],
                cal.image_size,
                cal.focal_length,
                cal.top_to_vertex,
                cal.sensor_size,
                u2d_mapx,
                u2d_mapy,
                d2u_mapx,
                d2u_mapy,
                cal.pan_to_tilt,
                cal.tilt_to_combiner,
                cal.vertex_offset,
                cal.roi_size,
                cal.servo_resolution,
                cal.delta_servo_factors,
                pos_scanner[1],
            )


_instance: Optional[GeometricSpace] = None
_lock: Lock = Lock()


def get_geometric_space(offsets_mm: Optional[List[float]] = None) -> GeometricSpace:
    """
    Since this loading function requires a thread lock to be thread safe
    we should never call any async functions from within this code.
    Additionally, we should never directly call this code from within an
    async coro, instead use run_in_executor
    """
    global _instance
    if _instance is None:
        with _lock:
            if _instance is None:  # Double locking check
                _instance = GeometricSpace()
                try:
                    _instance.load_devices(offsets_mm)
                except GeometricException:
                    pass
    return _instance

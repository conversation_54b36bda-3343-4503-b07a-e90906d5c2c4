from multiprocessing import get_context
from typing import Callable

from lib.common.shmem.cpp.shmem_python import ImageBuffer


def _cross_process_helper(path: str) -> None:
    buf = ImageBuffer(path)
    img = buf.get_numpy_array(buf)
    img[0][0][0] = 120
    buf.export_path()  # Export back


def _cross_process_helper_bad(path: str) -> None:
    buf = ImageBuffer(path)
    img = buf.get_numpy_array(buf)
    img[0][0][0] = 120
    # No Export back


def _init_buffer() -> str:
    buf = ImageBuffer("/crossp", (1, 1, 1))
    img = buf.get_numpy_array(buf)
    img[0][0][0] = 42
    return buf.export_path()


def _run_in_process(path: str, func: Callable[[str], None]) -> None:
    ctx = get_context("spawn")
    p = ctx.Process(target=func, args=(path,))
    p.start()
    p.join()


def test_cross_process_buffer() -> None:
    """
    This test should ensure that data can be transmitted across processes using the buffers.
    """
    path = _init_buffer()
    _run_in_process(path, _cross_process_helper)
    buf = ImageBuffer(path)
    img = buf.get_numpy_array(buf)
    assert img[0][0][0] == 120


def test_cross_process_buffer_error() -> None:
    """
    This test ensures that the shared memory gets properly deleted
    """
    path = _init_buffer()
    _run_in_process(path, _cross_process_helper_bad)
    try:
        ImageBuffer(path)
        # TODO Temporarily disabled until we parametrize short lived shmem
        # assert False
    except RuntimeError:
        pass

import glob
import os
from typing import Generator


def shared_mem_path_generator(name: str) -> Generator[str, None, None]:
    """
    At 100 FPS, this generator will overflow after 500 days of continuous use
    Upon overflow, one would hope that the images from 500 days ago have been released
    This potential problem is ignored right now.
    """
    old_shmem_list = glob.glob(f"/dev/shm/bot_{name}*") + glob.glob(f"/dev/shm/sem.bot_{name}*")
    for f in old_shmem_list:
        os.remove(f)
    count: int = 0
    while True:
        yield f"/bot_{name}_{count:010}"
        count += 1

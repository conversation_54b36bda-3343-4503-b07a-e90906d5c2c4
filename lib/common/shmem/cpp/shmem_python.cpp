#ifdef <PERSON>
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#endif

#include <boost/cstdfloat.hpp>
#include <lib/common/shmem/cpp/buffer/image_buffer.hpp>
#include <lib/common/shmem/cpp/buffer/ref_buffer.hpp>
#include <lib/common/shmem/cpp/buffer/shared_memory_buffer.hpp>
#include <lib/common/shmem/cpp/exceptions.hpp>

#ifdef PYBIND
namespace py = pybind11;
#endif

namespace lib {
namespace common {
namespace shmem {

extern template class ImageBuffer<uint8_t>;
extern template class ImageBuffer<boost::float32_t>;
extern template class RefBuffer<ImageBuffer<uint8_t>>;

#ifdef PYBIND
/**
 * Note that the Image Buffer is passed twice to the function, once as the
 * "self" And a second time as the first arg for the handle base. This is
 * necessary so that the buffer lifetime can be tied to any numpy array that
 * depends on it.
 */
template <typename T>
py::array_t<T> get_numpy_array(ImageBuffer<T> &buf, py::handle base) {
  std::tuple<uint32_t, uint32_t, uint32_t> shape;
  std::vector<uint32_t> vec_shape;
  T *address;
  {
    py::gil_scoped_release release;
    shape = buf.get_shape();
    vec_shape = std::vector<uint32_t>({std::get<0>(shape), std::get<1>(shape), std::get<2>(shape)});
    address = buf.get_image_data_address();
  }
  return py::array_t<T>(vec_shape, address, base);
}

PYBIND11_MODULE(shmem_python, m) {
  py::class_<SharedMemoryBuffer>(m, "SharedMemoryBuffer")
      .def("export_path", &SharedMemoryBuffer::export_path, py::call_guard<py::gil_scoped_release>())
      .def("release_path", &SharedMemoryBuffer::release_path, py::arg("path"), py::call_guard<py::gil_scoped_release>())
      .def("get_name", &SharedMemoryBuffer::get_name, py::call_guard<py::gil_scoped_release>());

  py::class_<ImageBuffer<uint8_t>, SharedMemoryBuffer>(m, "ImageBuffer")
      .def(py::init<std::string, std::tuple<uint32_t, uint32_t, uint32_t>>(), py::call_guard<py::gil_scoped_release>())
      .def(py::init<std::string>(), py::call_guard<py::gil_scoped_release>())
      .def("get_numpy_array", &get_numpy_array<uint8_t>, py::arg("base"))
      .def("get_timestamp_ms", &ImageBuffer<uint8_t>::get_timestamp_ms);

  py::class_<ImageBuffer<float>, SharedMemoryBuffer>(m, "Float32ImageBuffer")
      .def(py::init<std::string, std::tuple<uint32_t, uint32_t, uint32_t>>(), py::call_guard<py::gil_scoped_release>())
      .def(py::init<std::string>(), py::call_guard<py::gil_scoped_release>())
      .def("get_numpy_array", &get_numpy_array<boost::float32_t>, py::arg("base"))
      .def("get_timestamp_ms", &ImageBuffer<float>::get_timestamp_ms);

  py::class_<RefBuffer<ImageBuffer<uint8_t>>, SharedMemoryBuffer>(m, "ImageRefBuffer")
      .def(py::init<std::string, bool>(), py::call_guard<py::gil_scoped_release>())
      .def(py::init<std::string>(), py::call_guard<py::gil_scoped_release>())
      .def("open_latest_ref", &RefBuffer<ImageBuffer<uint8_t>>::open_latest_ref,
           py::call_guard<py::gil_scoped_release>())
      .def("open_next_ref", &RefBuffer<ImageBuffer<uint8_t>>::open_next_ref, py::arg("timeout_ms"),
           py::call_guard<py::gil_scoped_release>());

  auto py_shmem_exception = py::register_exception<shmem_error>(m, "ShmemException");
  py::register_exception<shmem_timeout_error>(m, "ShmemTimeoutException", py_shmem_exception.ptr());
}

#endif

} // namespace shmem
} // namespace common
} // namespace lib
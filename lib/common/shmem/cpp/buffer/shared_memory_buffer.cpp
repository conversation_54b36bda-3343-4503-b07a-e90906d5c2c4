#ifdef <PERSON>Y<PERSON>
#include <pybind11/pybind11.h>
#endif

#include <lib/common/shmem/cpp/buffer/shared_memory_buffer.hpp>
#include <lib/common/shmem/cpp/exceptions.hpp>

#ifdef PYBIND
namespace py = pybind11;
#endif

namespace lib {
namespace common {
namespace shmem {

SharedMemoryBuffer::SharedMemoryBuffer(std::string name, std::size_t size, const HeaderDescriptor &header)
    : buffer_(new MemoryMappedBuffer(name, (uint32_t)(size + header.get_size()))), header_(header),
      unlink_cleanup_(nullptr) {
  this->header_.initialize(this->buffer_->get_address(), size);
}

SharedMemoryBuffer::SharedMemoryBuffer(std::string name, const HeaderDescriptor &header)
    : buffer_(new MemoryMappedBuffer(name)), header_(header), unlink_cleanup_(nullptr) {
  const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
  this->header_.increment_ref_count(this->buffer_->get_address());
  this->header_.decrement_export_count(this->buffer_->get_address());
}

void SharedMemoryBuffer::unlink() {
  if (this->unlink_cleanup_ != nullptr) {
    this->unlink_cleanup_();
  }
  // The Buffers we use for now are cleaned on recreation
  // TODO Investigate parametrizing for short lived buffers
  // this->buffer_->unlink();
}

SharedMemoryBuffer::~SharedMemoryBuffer() {
#ifdef PYBIND
  py::gil_scoped_release release;
#endif
  {
    try {
      const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
      uint32_t ref_count = this->header_.decrement_ref_count(this->buffer_->get_address());
      if (ref_count == 0 && this->header_.get_export_count(this->buffer_->get_address()) == 0) {
        this->unlink();
      }
    } catch (shmem_timeout_error &ex) {
      // If we fail to acquire the lock, then it is likely the shmem is corrupted and will need to be cleaned
      // So we don't really have to worry about the ref count.
      // Also this is a destructor so we want to avoid exceptions
    }
  }
  delete this->buffer_;
}

void SharedMemoryBuffer::execute_on_memory(std::function<void(void *address, std::size_t size)> func) {
  const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
  void *address = this->buffer_->get_address();
  return func(this->header_.get_data_address(address), this->header_.get_data_size(address));
}

std::string SharedMemoryBuffer::export_path() {
  const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
  this->header_.increment_export_count(this->buffer_->get_address());
  return this->buffer_->get_shared_memory_ref().get_shared_memory_ref().get_name();
}

void SharedMemoryBuffer::release_path(std::string path) {
  (void)path; // Intentionally unused
  const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
  this->header_.decrement_export_count(this->buffer_->get_address());
}

std::string SharedMemoryBuffer::get_name() {
  return this->buffer_->get_shared_memory_ref().get_shared_memory_ref().get_name();
}

} // namespace shmem
} // namespace common
} // namespace lib
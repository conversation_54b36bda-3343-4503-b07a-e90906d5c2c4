#pragma once

#include <lib/common/shmem/cpp/buffer/shared_memory_buffer.hpp>
#include <tuple>

namespace lib {
namespace common {
namespace shmem {

template <typename T>
class ImageBuffer : public SharedMemoryBuffer {
public:
  ImageBuffer(std::string name, std::tuple<uint32_t, uint32_t, uint32_t> shape, int64_t timestamp_ms = 0);
  ImageBuffer(std::string name);

  std::tuple<uint32_t, uint32_t, uint32_t> get_shape();
  int64_t get_timestamp_ms();
  T *get_image_data_address();
};

} // namespace shmem
} // namespace common
} // namespace lib

#include <boost/date_time/posix_time/posix_time.hpp>
#include <filesystem>
#include <lib/common/shmem/cpp/buffer/image_buffer.hpp>
#include <lib/common/shmem/cpp/buffer/latest_buffer.hpp>
#include <lib/common/shmem/cpp/exceptions.hpp>
#include <lib/common/shmem/cpp/header/ref_header_descriptor.hpp>
#include <lib/common/shmem/cpp/system/shmem_mutex_guard.hpp>
#include <spdlog/spdlog.h>

namespace lib {
namespace common {
namespace shmem {

// Should hold Mutex before calling
template <class T>
void LatestBuffer<T>::retrieve_struct(T *buffer) {
  uint32_t *address = (uint32_t *)this->header_.get_data_address(this->buffer_->get_address());

  uint32_t data_size = this->header_.get_data_size(this->buffer_->get_address());

  if (custom_copy_retrieve_.has_value()) {
    custom_copy_retrieve_.value()(buffer, address, data_size);
  } else {
    std::memcpy(buffer, address, data_size);
  }
}

template <class T>
LatestBuffer<T>::LatestBuffer(std::string name, bool publisher)
    : SharedMemoryBuffer(name, sizeof(T), RefHeaderDescriptor::kInstance), publisher_(publisher),
      custom_copy_retrieve_(std::nullopt) {}

template <class T>
LatestBuffer<T>::LatestBuffer(std::string name, bool publisher,
                              std::function<void(void *dst, void *src, uint32_t size)> custom_copy_retrieve)
    : SharedMemoryBuffer(name, sizeof(T), RefHeaderDescriptor::kInstance), publisher_(publisher),
      custom_copy_retrieve_(custom_copy_retrieve) {}

template <class T>
LatestBuffer<T>::LatestBuffer(std::string name,
                              std::function<void(void *dst, void *src, uint32_t size)> custom_copy_retrieve)
    : SharedMemoryBuffer(name, RefHeaderDescriptor::kInstance), publisher_(false),
      custom_copy_retrieve_(custom_copy_retrieve) {}

template <class T>
LatestBuffer<T>::LatestBuffer(std::string name)
    : SharedMemoryBuffer(name, RefHeaderDescriptor::kInstance), publisher_(false), custom_copy_retrieve_(std::nullopt) {
}

template <class T>
LatestBuffer<T>::~LatestBuffer() {}

template <class T>
void LatestBuffer<T>::update(T *input, int64_t timestamp_ms) {
  this->custom_update([input](void *address, uint32_t size) { std::memcpy(address, input, size); }, timestamp_ms);
}

template <class T>
void LatestBuffer<T>::custom_update(std::function<void(void *address, uint32_t size)> custom_func,
                                    int64_t timestamp_ms) {
  const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
  uint32_t *address = (uint32_t *)this->header_.get_data_address(this->buffer_->get_address());
  uint32_t data_size = this->header_.get_data_size(this->buffer_->get_address());
  custom_func(address, data_size);
  this->header_.set_timestamp_ms(this->buffer_->get_address(), timestamp_ms);
  this->header_.get_condition(this->buffer_->get_address())->notify_all();
}

template <class T>
void LatestBuffer<T>::get(T &buffer) {
  const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
  retrieve_struct(&buffer);
}

template <class T>
void LatestBuffer<T>::get_next(T &buffer, uint32_t timeout_ms, int64_t last_timestamp_ms) {
  const boost::posix_time::ptime abs_timeout =
      boost::posix_time::microsec_clock::universal_time() + boost::posix_time::milliseconds(timeout_ms);

  {
    ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
    if (!this->header_.get_condition(this->buffer_->get_address())->timed_wait(lock.get_lock(), abs_timeout, [&]() {
          return this->header_.get_timestamp_ms(this->buffer_->get_address()) > last_timestamp_ms;
        })) {
      throw shmem_timeout_error("Timed out waiting for next buffer reference from instance of LatestBuffer");
    }
    retrieve_struct(&buffer);
  }
}

} // namespace shmem
} // namespace common
} // namespace lib

#pragma once

#include <lib/common/shmem/cpp/buffer/shared_memory_buffer.hpp>

namespace lib {
namespace common {
namespace shmem {

template <class T>
class RefBuffer : public SharedMemoryBuffer {
  uint32_t next_buf_id_;
  bool publisher_;

  std::string get_formatted_name(uint32_t buf_id);
  void remove_old_if_exists(uint32_t buf_id);

public:
  RefBuffer(std::string name, bool publisher);
  RefBuffer(std::string name);
  ~RefBuffer();

  void clean_old();
  std::unique_ptr<T> create_buffer_and_export_ref(std::function<std::unique_ptr<T>(std::string)> constructor);
  std::unique_ptr<T> open_latest_ref();
  std::unique_ptr<T> open_next_ref(uint32_t timeout_ms);
};

} // namespace shmem
} // namespace common
} // namespace lib
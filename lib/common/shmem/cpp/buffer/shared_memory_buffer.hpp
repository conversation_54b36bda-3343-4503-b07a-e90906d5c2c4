#pragma once

#include <functional>
#include <lib/common/shmem/cpp/buffer/memory_mapped_buffer.hpp>
#include <lib/common/shmem/cpp/header/header_descriptor.hpp>
#include <lib/common/shmem/cpp/system/shmem_mutex_guard.hpp>

namespace lib {
namespace common {
namespace shmem {

class SharedMemoryBuffer {
protected:
  // Note: The below parameter is a pointer because we need to be able to
  // control the release of the GIL when the destructor is called from pybind.
  // If pybind changes and we can automatically release the GIL on destructor
  // we can move these back to local
  MemoryMappedBuffer *buffer_;
  const HeaderDescriptor &header_;
  std::string name_;
  std::function<void()> unlink_cleanup_;

public:
  SharedMemoryBuffer(std::string name, std::size_t size, const HeaderDescriptor &header);
  SharedMemoryBuffer(std::string name, const HeaderDescriptor &header);
  virtual ~SharedMemoryBuffer();

  void unlink();
  void execute_on_memory(std::function<void(void *address, std::size_t size)> func);
  std::string export_path();
  void release_path(std::string path);
  std::string get_name();
};

} // namespace shmem
} // namespace common
} // namespace lib
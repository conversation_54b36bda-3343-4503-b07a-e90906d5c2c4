#include <lib/common/shmem/cpp/buffer/memory_mapped_buffer.hpp>

namespace lib {
namespace common {
namespace shmem {

void MemoryMappedBuffer::unlink() { this->shared_mem_.unlink(); }

void *MemoryMappedBuffer::get_address() { return this->mapped_region_.get_address(); }

std::size_t MemoryMappedBuffer::get_size() { return this->mapped_region_.get_size(); }

SharedMemory &MemoryMappedBuffer::get_shared_memory_ref() { return this->shared_mem_; }

MemoryMappedBuffer::MemoryMappedBuffer(std::string name, uint32_t size)
    : shared_mem_(name, size),
      mapped_region_(this->shared_mem_.get_shared_memory_ref(), boost::interprocess::read_write) {}

MemoryMappedBuffer::MemoryMappedBuffer(std::string name)
    : shared_mem_(name), mapped_region_(this->shared_mem_.get_shared_memory_ref(), boost::interprocess::read_write) {}

MemoryMappedBuffer::~MemoryMappedBuffer() {
  // Note that we don't actually want to release the resources here,
  // ref-counting should release the resources by calling unlink()
}

} // namespace shmem
} // namespace common
} // namespace lib
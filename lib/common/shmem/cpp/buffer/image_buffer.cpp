#include <boost/cstdfloat.hpp>
#include <lib/common/cpp/time.h>
#include <lib/common/shmem/cpp/buffer/image_buffer.hpp>
#include <lib/common/shmem/cpp/header/image_header_descriptor.hpp>

namespace lib {
namespace common {
namespace shmem {

template <typename T>
ImageBuffer<T>::ImageBuffer(std::string name, std::tuple<uint32_t, uint32_t, uint32_t> shape, int64_t timestamp_ms)
    : SharedMemoryBuffer(name, std::get<0>(shape) * std::get<1>(shape) * std::get<2>(shape) * sizeof(T),
                         ImageHeaderDescriptor::kInstance) {
  const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
  ((ImageHeaderDescriptor &)this->header_).set_shape(this->buffer_->get_address(), shape);
  ((ImageHeaderDescriptor &)this->header_).set_timestamp_ms(this->buffer_->get_address(), timestamp_ms);
}

template <typename T>
ImageBuffer<T>::ImageBuffer(std::string name) : SharedMemoryBuffer(name, ImageHeaderDescriptor::kInstance) {}

template <typename T>
std::tuple<uint32_t, uint32_t, uint32_t> ImageBuffer<T>::get_shape() {
  const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
  return ((ImageHeaderDescriptor &)this->header_).get_shape(this->buffer_->get_address());
}

template <typename T>
int64_t ImageBuffer<T>::get_timestamp_ms() {
  const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
  return ((ImageHeaderDescriptor &)this->header_).get_timestamp_ms(this->buffer_->get_address());
}

template <typename T>
T *ImageBuffer<T>::get_image_data_address() {
  const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
  // Note that subsequent accesses to the data by the caller are intentionally
  // no protected by the semaphore. ImageBuffer is intended as read-only once
  // it has been published.
  return (T *)this->header_.get_data_address(this->buffer_->get_address());
}

template class ImageBuffer<uint8_t>;
template class ImageBuffer<boost::float32_t>;

} // namespace shmem
} // namespace common
} // namespace lib
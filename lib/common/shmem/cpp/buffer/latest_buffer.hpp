#pragma once

#include <boost/interprocess/sync/named_condition.hpp>
#include <boost/interprocess/sync/named_mutex.hpp>
#include <lib/common/shmem/cpp/buffer/shared_memory_buffer.hpp>
#include <optional>

namespace lib {
namespace common {
namespace shmem {

template <class T>
class LatestBuffer : public SharedMemoryBuffer {
  bool publisher_;
  std::optional<std::function<void(void *dst, void *src, uint32_t size)>> custom_copy_retrieve_;

  void retrieve_struct(T *buffer);

public:
  LatestBuffer(std::string name, bool publisher);
  LatestBuffer(std::string name, bool publisher,
               std::function<void(void *dst, void *src, uint32_t size)> custom_copy_retrieve);
  LatestBuffer(std::string name, std::function<void(void *dst, void *src, uint32_t size)> custom_copy_retrieve);
  LatestBuffer(std::string name);
  ~LatestBuffer();

  void update(T *input, int64_t timestamp_ms);
  void custom_update(std::function<void(void *address, uint32_t size)> custom_func, int64_t timestamp_ms);
  void get(T &buffer);
  void get_next(T &buffer, uint32_t timeout_ms, int64_t last_timestamp_ms);
};

} // namespace shmem
} // namespace common
} // namespace lib

#include "latest_buffer.inl"
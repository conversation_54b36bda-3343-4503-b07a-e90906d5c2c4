#pragma once

#include <boost/interprocess/mapped_region.hpp>
#include <lib/common/shmem/cpp/system/shared_memory.hpp>

namespace lib {
namespace common {
namespace shmem {

/**
 *  MemoryMappedBuffer has its lifetime managed by inode (non-shareable)
 *  It can still be used even if the underlying memory has been unlinked
 *  from the file system.
 */
class MemoryMappedBuffer {
  SharedMemory shared_mem_;
  boost::interprocess::mapped_region mapped_region_;

public:
  MemoryMappedBuffer(std::string name, uint32_t size); // Create
  MemoryMappedBuffer(std::string name);                // Load
  ~MemoryMappedBuffer();
  void *get_address();
  std::size_t get_size();
  void unlink();
  SharedMemory &get_shared_memory_ref();
};

} // namespace shmem
} // namespace common
} // namespace lib
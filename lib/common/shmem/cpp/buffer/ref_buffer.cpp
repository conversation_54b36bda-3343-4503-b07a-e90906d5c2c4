
#include <boost/date_time/posix_time/posix_time.hpp>
#include <filesystem>
#include <lib/common/shmem/cpp/buffer/image_buffer.hpp>
#include <lib/common/shmem/cpp/buffer/ref_buffer.hpp>
#include <lib/common/shmem/cpp/exceptions.hpp>
#include <lib/common/shmem/cpp/header/ref_header_descriptor.hpp>
#include <lib/common/shmem/cpp/system/shmem_mutex_guard.hpp>

namespace lib {
namespace common {
namespace shmem {

template <class T>
RefBuffer<T>::RefBuffer(std::string name, bool publisher)
    : SharedMemoryBuffer(name, sizeof(uint32_t), RefHeaderDescriptor::kInstance), next_buf_id_(0),
      publisher_(publisher) {}

template <class T>
RefBuffer<T>::RefBuffer(std::string name)
    : SharedMemory<PERSON>uffer(name, RefHeaderDescriptor::kInstance), next_buf_id_(0), publisher_(false) {}

template <class T>
RefBuffer<T>::~RefBuffer() {}

template <class T>
std::string RefBuffer<T>::get_formatted_name(uint32_t buf_id) {
  std::stringstream name_stream;
  name_stream << this->buffer_->get_shared_memory_ref().get_shared_memory_ref().get_name() << "_" << buf_id;
  return name_stream.str();
}

template <class T>
std::unique_ptr<T>
RefBuffer<T>::create_buffer_and_export_ref(std::function<std::unique_ptr<T>(std::string)> constructor) {
  if (!this->publisher_) {
    throw shmem_error("Not a Publisher for this Ref Buffer");
  }

  // Get The Next ID
  uint32_t prev_ref_id;
  uint32_t next_ref_id;
  uint32_t *address = static_cast<uint32_t *>(this->header_.get_data_address(this->buffer_->get_address()));
  {
    const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
    next_ref_id = static_cast<const RefHeaderDescriptor &>(this->header_).get_next_ref_id(this->buffer_->get_address());

    // This is a condition that occurs with the first buffer
    if (*address == next_ref_id) {
      next_ref_id =
          static_cast<const RefHeaderDescriptor &>(this->header_).get_next_ref_id(this->buffer_->get_address());
    }
  }

  // Construct and initialize buffer, caller provided, may take a long time to
  // finish
  std::unique_ptr<T> buf = constructor(this->get_formatted_name(next_ref_id));
  buf->export_path();

  // Set the Buffer Ref ID to the newly constructed buffer
  {
    const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
    prev_ref_id = *address;
    *address = next_ref_id;
    this->header_.get_condition(this->buffer_->get_address())->notify_all();
  }

  // Clean
  this->remove_old_if_exists(prev_ref_id);
  return buf;
}

template <class T>
std::unique_ptr<T> RefBuffer<T>::open_latest_ref() {
  std::unique_ptr<T> buf;
  uint32_t *address = (uint32_t *)this->header_.get_data_address(this->buffer_->get_address());
  {
    const ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
    uint32_t buf_id = *address;
    buf = std::make_unique<T>(this->get_formatted_name(buf_id));
    buf->export_path();
  }
  return buf;
}

template <class T>
std::unique_ptr<T> RefBuffer<T>::open_next_ref(uint32_t timeout_ms) {
  const boost::posix_time::ptime abs_timeout =
      boost::posix_time::microsec_clock::universal_time() + boost::posix_time::milliseconds(timeout_ms);
  {
    ShmemMutexGuard lock(this->header_.get_mutex(this->buffer_->get_address()));
    if (!this->header_.get_condition(this->buffer_->get_address())->timed_wait(lock.get_lock(), abs_timeout)) {
      throw shmem_timeout_error("Timed out waiting for next buffer reference from instance of RefBuffer");
    }
  }
  return this->open_latest_ref();
}

template <class T>
void RefBuffer<T>::remove_old_if_exists(uint32_t buf_id) {
  std::string old_name = this->get_formatted_name(buf_id);
  try {
    // By opening the buffer, we automatically decrement the export count
    std::unique_ptr<T> old_buf = std::make_unique<T>(old_name);
  } catch (boost::interprocess::interprocess_exception &) {
  }
}

template <class T>
void RefBuffer<T>::clean_old() {
  uint32_t *address = (uint32_t *)this->header_.get_data_address(this->buffer_->get_address());
  this->remove_old_if_exists(*address);
}

template class RefBuffer<ImageBuffer<uint8_t>>;

} // namespace shmem
} // namespace common
} // namespace lib
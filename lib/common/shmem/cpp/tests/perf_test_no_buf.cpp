#include <boost/chrono.hpp>
#include <boost/format.hpp>
#include <iostream>
#include <lib/common/shmem/cpp/buffer/image_buffer.hpp>
#include <numeric>
#include <vector>

using namespace lib::common::shmem;

void perf_test_no_buf(int n) {
  const std::size_t size = 3000 * 4000 * 3;
  uint8_t *source = new uint8_t[size];
  for (uint i = 0; i < size; i++) {
    source[i] = (uint8_t)(i % 256);
  }

  std::vector<double> creation;
  std::vector<double> copy;
  std::vector<double> deletion;

  for (int j = 0; j < n; j++) {
    // Create
    boost::chrono::system_clock::time_point start = boost::chrono::system_clock::now();
    uint8_t *buf = new uint8_t[size];
    boost::chrono::duration<double, boost::milli> sec = boost::chrono::system_clock::now() - start;
    creation.push_back(sec.count());

    // Copy
    start = boost::chrono::system_clock::now();
    memcpy(buf, source, size);
    sec = boost::chrono::system_clock::now() - start;
    copy.push_back(sec.count());

    // Delete
    start = boost::chrono::system_clock::now();
    delete[] buf;
    sec = boost::chrono::system_clock::now() - start;
    deletion.push_back(sec.count());
  }

  double creation_avg = std::accumulate(creation.begin(), creation.end(), 0) / n;
  double copy_avg = std::accumulate(copy.begin(), copy.end(), 0) / n;

  double deletion_avg = std::accumulate(deletion.begin(), deletion.end(), 0) / n;

  std::cout << "Creation Avg: " << creation_avg << std::endl;
  std::cout << "Copy Avg: " << copy_avg << std::endl;
  std::cout << "Deletion Avg: " << deletion_avg << std::endl;

  delete[] source;
}

int main() {
  perf_test_no_buf(1000);
  return 0;
}
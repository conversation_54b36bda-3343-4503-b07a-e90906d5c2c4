#include <boost/chrono.hpp>
#include <boost/format.hpp>
#include <iostream>
#include <lib/common/shmem/cpp/buffer/image_buffer.hpp>
#include <numeric>
#include <thread>
#include <vector>

using namespace lib::common::shmem;

void thread_runner(int id, int n) {
  std::tuple<uint32_t, uint32_t, uint32_t> shape = std::tuple<uint32_t, uint32_t, uint32_t>(3000, 4000, 3);
  const std::size_t size = 3000 * 4000 * 3;
  uint8_t *source = new uint8_t[size];
  for (uint i = 0; i < size; i++) {
    source[i] = (uint8_t)(i % 256);
  }

  std::vector<double> creation;
  std::vector<double> copy;
  std::vector<double> deletion;

  for (int j = 0; j < n; j++) {
    // Create
    std::string name = str(boost::format("%d_perf_test_%d") % id % j);
    boost::chrono::system_clock::time_point start = boost::chrono::system_clock::now();
    ImageBuffer<uint8_t> *buf = new ImageBuffer<uint8_t>(name, shape);
    boost::chrono::duration<double, boost::milli> sec = boost::chrono::system_clock::now() - start;
    creation.push_back(sec.count());

    // Copy
    uint8_t *addr = buf->get_image_data_address();
    start = boost::chrono::system_clock::now();
    memcpy(addr, source, size);
    sec = boost::chrono::system_clock::now() - start;
    copy.push_back(sec.count());

    // Delete
    start = boost::chrono::system_clock::now();
    delete buf;
    sec = boost::chrono::system_clock::now() - start;
    deletion.push_back(sec.count());
  }

  double creation_avg = std::accumulate(creation.begin(), creation.end(), 0) / n;
  double copy_avg = std::accumulate(copy.begin(), copy.end(), 0) / n;

  double deletion_avg = std::accumulate(deletion.begin(), deletion.end(), 0) / n;

  std::cout << id << " Creation Avg: " << creation_avg << std::endl;
  std::cout << id << " Copy Avg: " << copy_avg << std::endl;
  std::cout << id << " Deletion Avg: " << deletion_avg << std::endl;

  delete[] source;
}

void thread_runner_reuse(int id, int n) {
  std::tuple<uint32_t, uint32_t, uint32_t> shape = std::tuple<uint32_t, uint32_t, uint32_t>(3000, 4000, 3);
  const std::size_t size = 3000 * 4000 * 3;
  uint8_t *source = new uint8_t[size];
  for (uint i = 0; i < size; i++) {
    source[i] = (uint8_t)(i % 256);
  }

  std::vector<double> creation;
  std::vector<double> copy;
  std::vector<double> deletion;

  std::string name = str(boost::format("%d_perf_test_%d") % id % 1);
  ImageBuffer<uint8_t> *buf = new ImageBuffer<uint8_t>(name, shape);
  uint8_t *addr = buf->get_image_data_address();

  for (int j = 0; j < n; j++) {
    // Create
    boost::chrono::system_clock::time_point start = boost::chrono::system_clock::now();
    boost::chrono::duration<double, boost::milli> sec = boost::chrono::system_clock::now() - start;
    creation.push_back(sec.count());

    // Copy
    start = boost::chrono::system_clock::now();
    memcpy(addr, source, size);
    sec = boost::chrono::system_clock::now() - start;
    copy.push_back(sec.count());

    // Delete
    start = boost::chrono::system_clock::now();
    sec = boost::chrono::system_clock::now() - start;
    deletion.push_back(sec.count());
  }

  delete buf;

  double creation_avg = std::accumulate(creation.begin(), creation.end(), 0) / n;
  double copy_avg = std::accumulate(copy.begin(), copy.end(), 0) / n;

  double deletion_avg = std::accumulate(deletion.begin(), deletion.end(), 0) / n;

  std::cout << id << " Creation Avg: " << creation_avg << std::endl;
  std::cout << id << " Copy Avg: " << copy_avg << std::endl;
  std::cout << id << " Deletion Avg: " << deletion_avg << std::endl;

  delete[] source;
}

int main() {
  std::vector<std::thread> mythreads;
  int i = 0;
  for (i = 0; i < 16; i++) {
    mythreads.push_back(std::thread(thread_runner_reuse, i, 1000));
  }
  auto originalthread = mythreads.begin();
  // Do other stuff here.
  while (originalthread != mythreads.end()) {
    originalthread->join();
    originalthread++;
  }
  return 0;
}
#include <assert.h>

#include <boost/chrono.hpp>
#include <boost/format.hpp>
#include <boost/thread.hpp>
#include <functional>
#include <iostream>
#include <lib/common/shmem/cpp/buffer/image_buffer.hpp>
#include <lib/common/shmem/cpp/buffer/ref_buffer.hpp>
#include <numeric>
#include <vector>

using namespace lib::common::shmem;

extern template class lib::common::shmem::ImageBuffer<uint8_t>;
extern template class lib::common::shmem::RefBuffer<ImageBuffer<uint8_t>>;

std::unique_ptr<ImageBuffer<uint8_t>> create_image_buffer(std::string name, uint8_t val) {
  std::unique_ptr<ImageBuffer<uint8_t>> buf =
      std::make_unique<ImageBuffer<uint8_t>>(name, std::tuple<uint32_t, uint32_t, uint32_t>(1, 1, 1));
  uint8_t *address = buf->get_image_data_address();
  *address = val;
  return buf;
}

std::unique_ptr<ImageBuffer<uint8_t>> create_random_buffer(std::string name) {
  std::unique_ptr<ImageBuffer<uint8_t>> buf =
      std::make_unique<ImageBuffer<uint8_t>>(name, std::tuple<uint32_t, uint32_t, uint32_t>(800, 1200, 3));
  uint8_t *address = buf->get_image_data_address();
  for (int i = 0; i < 800 * 1200 * 3; i++) {
    address[i] = (uint8_t)(rand() % 256);
  }
  return buf;
}

void thread1(RefBuffer<ImageBuffer<uint8_t>> *ref1) {
  ref1->create_buffer_and_export_ref(std::bind(create_image_buffer, std::placeholders::_1, 43));
}

void thread2() {
  RefBuffer<ImageBuffer<uint8_t>> ref1_thread("ref1");
  std::unique_ptr<ImageBuffer<uint8_t>> buf = ref1_thread.open_next_ref(2000);
  assert(*buf->get_image_data_address() == 43);
}

int main() {
  RefBuffer<ImageBuffer<uint8_t>> ref1("ref1", true);
  RefBuffer<ImageBuffer<uint8_t>> ref1_duplicate("ref1");

  ref1.create_buffer_and_export_ref(std::bind(create_image_buffer, std::placeholders::_1, 42));
  std::unique_ptr<ImageBuffer<uint8_t>> buf = ref1_duplicate.open_latest_ref();
  assert(*buf->get_image_data_address() == 42);

  boost::thread t2{thread2};
  boost::this_thread::sleep_for(boost::chrono::seconds{1});
  boost::thread t1{boost::bind(thread1, &ref1)};

  t1.join();
  t2.join();

  return 0;
}
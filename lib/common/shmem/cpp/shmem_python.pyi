from typing import Any, <PERSON><PERSON>, overload

import numpy as np
import numpy.typing as npt

class SharedMemoryBuffer:
    def export_path(self) -> str: ...
    def get_name(self) -> str: ...
    def release_path(self, path: str) -> None: ...

class ImageBuffer(SharedMemoryBuffer):
    @overload
    def __init__(self, name: str, shape: Tuple[int, int, int]) -> None: ...
    @overload
    def __init__(self, name: str) -> None: ...
    def get_numpy_array(self, base: ImageBuffer) -> npt.NDArray[Any]: ...
    def get_timestamp_ms(self) -> int: ...

class Float32ImageBuffer(SharedMemoryBuffer):
    @overload
    def __init__(self, name: str, shape: Tuple[int, int, int]) -> None: ...
    @overload
    def __init__(self, name: str) -> None: ...
    def get_numpy_array(self, base: Float32ImageBuffer) -> npt.NDArray[Any]: ...
    def get_timestamp_ms(self) -> int: ...

class ImageRefBuffer(SharedMemoryBuffer):
    @overload
    def __init__(self, name: str, publisher: bool) -> None: ...
    @overload
    def __init__(self, name: str) -> None: ...
    def open_latest_ref(self) -> ImageBuffer: ...
    def open_next_ref(self, timeout_ms: int) -> ImageBuffer: ...

class ShmemException(Exception):
    pass

class ShmemTimeoutException(ShmemException):
    pass

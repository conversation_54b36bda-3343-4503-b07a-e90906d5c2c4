#pragma once

#include <boost/interprocess/sync/interprocess_mutex.hpp>
#include <boost/interprocess/sync/scoped_lock.hpp>

namespace lib {
namespace common {
namespace shmem {

class ShmemMutexGuard {
  boost::interprocess::scoped_lock<boost::interprocess::interprocess_mutex> lock_;

public:
  ShmemMutexGuard(boost::interprocess::interprocess_mutex *mutex);
  ~ShmemMutexGuard();

  boost::interprocess::scoped_lock<boost::interprocess::interprocess_mutex> &get_lock();
};

} // namespace shmem
} // namespace common
} // namespace lib
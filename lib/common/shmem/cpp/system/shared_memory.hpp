#pragma once

#include <boost/interprocess/shared_memory_object.hpp>

namespace lib {
namespace common {
namespace shmem {

/**
 * SharedMemory is a shared memory region of memory with access synchronized by
 * semaphore.
 */
class SharedMemory {
  boost::interprocess::shared_memory_object shared_memory_;

public:
  SharedMemory(std::string name, uint32_t size); // Create
  SharedMemory(std::string name);                // Load
  ~SharedMemory();
  boost::interprocess::shared_memory_object &get_shared_memory_ref();
  void unlink();
};

} // namespace shmem
} // namespace common
} // namespace lib
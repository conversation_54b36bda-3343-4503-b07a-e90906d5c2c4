#include <chrono>
#include <lib/common/shmem/cpp/exceptions.hpp>
#include <lib/common/shmem/cpp/system/shmem_mutex_guard.hpp>
#include <thread>

#define DEFAULT_MUTEX_GUARD_TIMEOUT_S 5

namespace lib {
namespace common {
namespace shmem {

ShmemMutexGuard::ShmemMutexGuard(boost::interprocess::interprocess_mutex *mutex)
    : lock_(*mutex, boost::interprocess::try_to_lock) {
  auto start = std::chrono::steady_clock::now();
  while (this->lock_ == nullptr) {
    std::chrono::duration<double> duration_s = std::chrono::steady_clock::now() - start;
    if (duration_s.count() > DEFAULT_MUTEX_GUARD_TIMEOUT_S) {
      throw shmem_timeout_error("Timeout Acquiring Shmem Mutex");
    }
    std::this_thread::yield();
    this->lock_ = boost::interprocess::scoped_lock<boost::interprocess::interprocess_mutex>(
        *mutex, boost::interprocess::try_to_lock);
  }
}

ShmemMutexGuard::~ShmemMutexGuard() {}

boost::interprocess::scoped_lock<boost::interprocess::interprocess_mutex> &ShmemMutexGuard::get_lock() {
  return this->lock_;
}

} // namespace shmem
} // namespace common
} // namespace lib
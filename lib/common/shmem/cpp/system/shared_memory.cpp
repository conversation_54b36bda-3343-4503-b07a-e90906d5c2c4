#include <lib/common/shmem/cpp/system/shared_memory.hpp>

namespace lib {
namespace common {
namespace shmem {

boost::interprocess::shared_memory_object &SharedMemory::get_shared_memory_ref() { return this->shared_memory_; }

void SharedMemory::unlink() { boost::interprocess::shared_memory_object::remove(this->shared_memory_.get_name()); }

static std::string clear_name(std::string name) {
  boost::interprocess::shared_memory_object::remove(name.c_str());
  return name;
}

SharedMemory::SharedMemory(std::string name, uint32_t size)
    : shared_memory_(boost::interprocess::create_only, clear_name(name).c_str(), boost::interprocess::read_write) {
  this->shared_memory_.truncate(size);
}

SharedMemory::SharedMemory(std::string name)
    : shared_memory_(boost::interprocess::open_only, name.c_str(), boost::interprocess::read_write) {}

SharedMemory::~SharedMemory() {
  // Note that we don't actually want to release the resources here,
  // ref-counting should release the resources by calling unlink()
}

} // namespace shmem
} // namespace common
} // namespace lib
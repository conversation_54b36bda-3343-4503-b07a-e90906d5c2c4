add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp buffer/*.cpp header/*.cpp system/*.cpp)
list(FILTER SOURCES EXCLUDE REGEX ".*_python\\.cpp$")

pybind11_add_module(shmem_python SHARED shmem_python.cpp)

add_library(shmem SHARED ${SOURCES})
target_compile_definitions(shmem PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(shmem PUBLIC exceptions m stdc++fs boost_fiber boost_chrono pthread rt spdlog fmt boost_date_time boost_thread dl)

target_link_libraries(shmem_python PUBLIC shmem)
target_compile_definitions(shmem_python PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
set_target_properties(shmem_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

file(GLOB TEST_SOURCES CONFIGURE_DEPENDS tests/*.cpp)
foreach(SOURCE ${TEST_SOURCES})
    get_filename_component(EXE_NAME ${SOURCE} NAME_WE)
    add_executable(${EXE_NAME} ${SOURCE})
    set_target_properties(${EXE_NAME} PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/tests/ OUTPUT_NAME ${EXE_NAME}.out)
    target_link_libraries(${EXE_NAME} shmem)
endforeach()
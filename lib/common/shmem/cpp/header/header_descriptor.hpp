#pragma once

#include <lib/common/shmem/cpp/header/header_field_def.hpp>
#include <lib/common/shmem/cpp/header/mutex_header_field_def.hpp>

namespace lib {
namespace common {
namespace shmem {

class HeaderDescriptor {
protected:
  FieldIndexer indexer_;

private:
  HeaderFieldDef<uint32_t> ref_count_;
  HeaderFieldDef<uint32_t> export_count_;
  HeaderFieldDef<uint32_t> data_offset_;
  HeaderFieldDef<uint32_t> data_size_;
  HeaderFieldDef<int64_t> timestamp_ms_;
  MutexHeaderFieldDef mutex_;
  ConditionHeaderFieldDef condition_;

protected:
  HeaderDescriptor();

public:
  static const HeaderDescriptor kInstance;
  std::size_t get_size() const;
  uint32_t get_ref_count(void *address) const;
  uint32_t get_export_count(void *address) const;
  uint32_t get_data_offset(void *address) const;
  uint32_t get_data_size(void *address) const;
  int64_t get_timestamp_ms(void *address) const;
  boost::interprocess::interprocess_mutex *get_mutex(void *address) const;
  boost::interprocess::interprocess_condition *get_condition(void *address) const;
  void *get_data_address(void *address) const;
  void initialize(void *address, std::size_t data_size) const;
  uint32_t increment_ref_count(void *address) const;
  uint32_t decrement_ref_count(void *address) const;
  uint32_t increment_export_count(void *address) const;
  uint32_t decrement_export_count(void *address) const;
  void set_timestamp_ms(void *address, int64_t timestamp_ms) const;
};

} // namespace shmem
} // namespace common
} // namespace lib
#pragma once

#include <lib/common/shmem/cpp/header/header_descriptor.hpp>
#include <tuple>

namespace lib {
namespace common {
namespace shmem {

class ImageHeaderDescriptor : public HeaderDescriptor {
  HeaderFieldDef<uint32_t> height_;
  HeaderFieldDef<uint32_t> width_;
  HeaderFieldDef<uint32_t> depth_;
  HeaderFieldDef<int64_t> timestamp_ms_;

protected:
  ImageHeaderDescriptor();

public:
  static const ImageHeaderDescriptor kInstance;
  void set_shape(void *address, std::tuple<uint32_t, uint32_t, uint32_t> &shape) const;
  std::tuple<uint32_t, uint32_t, uint32_t> get_shape(void *address) const;

  void set_timestamp_ms(void *address, int64_t timestamp_ms) const;
  int64_t get_timestamp_ms(void *address) const;
};

} // namespace shmem
} // namespace common
} // namespace lib
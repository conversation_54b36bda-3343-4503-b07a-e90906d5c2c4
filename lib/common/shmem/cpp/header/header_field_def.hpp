#pragma once

#include <lib/common/shmem/cpp/header/base_header_field_def.hpp>
#include <lib/common/shmem/cpp/header/field_indexer.hpp>

namespace lib {
namespace common {
namespace shmem {

template <class F>
class HeaderFieldDef : public BaseHeaderFieldDef {
public:
  HeaderFieldDef(FieldIndexer &indexer) : BaseHeaderFieldDef(indexer, sizeof(F)){};
  ~HeaderFieldDef(){};

  void write(void *address, F data) const { *(F *)this->get_indexed_address(address) = data; };
  virtual F read(void *address) const { return *(F *)this->get_indexed_address(address); };
};

} // namespace shmem
} // namespace common
} // namespace lib
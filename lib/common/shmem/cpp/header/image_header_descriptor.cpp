#include <lib/common/shmem/cpp/header/image_header_descriptor.hpp>

namespace lib {
namespace common {
namespace shmem {

ImageHeaderDescriptor::ImageHeaderDescriptor()
    : HeaderDescriptor(), height_(this->indexer_), width_(this->indexer_), depth_(this->indexer_),
      timestamp_ms_(this->indexer_) {}

const ImageHeaderDescriptor ImageHeaderDescriptor::kInstance = ImageHeaderDescriptor();

void ImageHeaderDescriptor::set_shape(void *address, std::tuple<uint32_t, uint32_t, uint32_t> &shape) const {
  this->height_.write(address, std::get<0>(shape));
  this->width_.write(address, std::get<1>(shape));
  this->depth_.write(address, std::get<2>(shape));
}

std::tuple<uint32_t, uint32_t, uint32_t> ImageHeaderDescriptor::get_shape(void *address) const {
  return std::tuple<uint32_t, uint32_t, uint32_t>(this->height_.read(address), this->width_.read(address),
                                                  this->depth_.read(address));
}

void ImageHeaderDescriptor::set_timestamp_ms(void *address, int64_t timestamp_ms) const {
  this->timestamp_ms_.write(address, timestamp_ms);
}

int64_t ImageHeaderDescriptor::get_timestamp_ms(void *address) const { return this->timestamp_ms_.read(address); }

} // namespace shmem
} // namespace common
} // namespace lib
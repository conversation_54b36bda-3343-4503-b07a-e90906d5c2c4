#include <lib/common/shmem/cpp/header/header_descriptor.hpp>

namespace lib {
namespace common {
namespace shmem {

HeaderDescriptor::HeaderDescriptor()
    : indexer_(), ref_count_(this->indexer_), export_count_(this->indexer_), data_offset_(this->indexer_),
      data_size_(this->indexer_), timestamp_ms_(this->indexer_), mutex_(this->indexer_), condition_(this->indexer_) {}

std::size_t HeaderDescriptor::get_size() const { return this->indexer_.get_total_size(); }

uint32_t HeaderDescriptor::get_ref_count(void *address) const { return this->ref_count_.read(address); }

uint32_t HeaderDescriptor::get_export_count(void *address) const { return this->export_count_.read(address); }

uint32_t HeaderDescriptor::get_data_offset(void *address) const { return this->data_offset_.read(address); }

uint32_t HeaderDescriptor::get_data_size(void *address) const { return this->data_size_.read(address); }

int64_t HeaderDescriptor::get_timestamp_ms(void *address) const { return this->timestamp_ms_.read(address); };

boost::interprocess::interprocess_mutex *HeaderDescriptor::get_mutex(void *address) const {
  return (boost::interprocess::interprocess_mutex *)this->mutex_.get_indexed_address(address);
};

boost::interprocess::interprocess_condition *HeaderDescriptor::get_condition(void *address) const {
  return (boost::interprocess::interprocess_condition *)this->condition_.get_indexed_address(address);
};

void *HeaderDescriptor::get_data_address(void *address) const {
  return (void *)((uint8_t *)address + this->get_data_offset(address));
}

void HeaderDescriptor::initialize(void *address, std::size_t data_size) const {
  this->ref_count_.write(address, 1);
  this->export_count_.write(address, 0);
  this->data_offset_.write(address, (unsigned int)this->get_size());
  this->data_size_.write(address, (unsigned int)data_size);
  this->timestamp_ms_.write(address, 0);
  new (this->mutex_.get_indexed_address(address)) boost::interprocess::interprocess_mutex;
  new (this->condition_.get_indexed_address(address)) boost::interprocess::interprocess_condition;
}

uint32_t HeaderDescriptor::increment_ref_count(void *address) const {
  uint32_t count = this->get_ref_count(address);
  count++;
  this->ref_count_.write(address, count);
  return count;
}

uint32_t HeaderDescriptor::decrement_ref_count(void *address) const {
  uint32_t count = this->get_ref_count(address);
  if (count == 0) {
    return 0;
  }
  count--;
  this->ref_count_.write(address, count);
  return count;
}

uint32_t HeaderDescriptor::increment_export_count(void *address) const {
  uint32_t count = this->get_export_count(address);
  count++;
  this->export_count_.write(address, count);
  return count;
}

uint32_t HeaderDescriptor::decrement_export_count(void *address) const {
  uint32_t count = this->get_export_count(address);
  if (count == 0) {
    return 0;
  }
  count--;
  this->export_count_.write(address, count);
  return count;
}

void HeaderDescriptor::set_timestamp_ms(void *address, int64_t timestamp_ms) const {
  this->timestamp_ms_.write(address, timestamp_ms);
}

const HeaderDescriptor HeaderDescriptor::kInstance = HeaderDescriptor();

} // namespace shmem
} // namespace common
} // namespace lib
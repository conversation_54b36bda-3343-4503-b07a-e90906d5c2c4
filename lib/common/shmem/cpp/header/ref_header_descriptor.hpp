#pragma once

#include <lib/common/shmem/cpp/header/header_descriptor.hpp>
#include <tuple>

namespace lib {
namespace common {
namespace shmem {

class RefHeaderDescriptor : public HeaderDescriptor {
  HeaderFieldDef<uint32_t> next_ref_id_;

protected:
  RefHeaderDescriptor();

public:
  static const RefHeaderDescriptor kInstance;
  uint32_t get_next_ref_id(void *address) const;
};

} // namespace shmem
} // namespace common
} // namespace lib
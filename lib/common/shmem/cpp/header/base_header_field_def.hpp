#pragma once

#include <cstdint>
#include <lib/common/shmem/cpp/header/field_indexer.hpp>

namespace lib {
namespace common {
namespace shmem {

class BaseHeaderFieldDef {
  uint32_t index_;
  std::size_t size_;

public:
  BaseHeaderFieldDef(FieldIndexer &indexer, std::size_t size);
  ~BaseHeaderFieldDef(){};

  void set_index(uint32_t index);
  std::size_t get_field_size() const;
  void *get_indexed_address(void *address) const;
};

} // namespace shmem
} // namespace common
} // namespace lib
#include <lib/common/shmem/cpp/header/ref_header_descriptor.hpp>

namespace lib {
namespace common {
namespace shmem {

RefHeaderDescriptor::RefHeaderDescriptor() : HeaderDescriptor(), next_ref_id_(this->indexer_) {}

const RefHeaderDescriptor RefHeaderDescriptor::kInstance = RefHeaderDescriptor();

uint32_t RefHeaderDescriptor::get_next_ref_id(void *address) const {
  uint32_t next_ref_id = this->next_ref_id_.read(address);
  this->next_ref_id_.write(address, next_ref_id + 1);
  return next_ref_id;
}

} // namespace shmem
} // namespace common
} // namespace lib
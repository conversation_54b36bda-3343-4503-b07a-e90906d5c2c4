#include <lib/common/shmem/cpp/header/base_header_field_def.hpp>
#include <lib/common/shmem/cpp/header/field_indexer.hpp>

namespace lib {
namespace common {
namespace shmem {

FieldIndexer::FieldIndexer() : total_size_(0) {}

void FieldIndexer::index_field(BaseHeaderFieldDef &field) {
  field.set_index((uint32_t)this->total_size_);
  total_size_ += field.get_field_size();
}

std::size_t FieldIndexer::get_total_size() const { return this->total_size_; }

} // namespace shmem
} // namespace common
} // namespace lib
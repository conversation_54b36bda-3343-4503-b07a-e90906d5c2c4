#include <lib/common/shmem/cpp/header/base_header_field_def.hpp>

namespace lib {
namespace common {
namespace shmem {

void BaseHeaderFieldDef::set_index(uint32_t index) { this->index_ = index; }

std::size_t BaseHeaderFieldDef::get_field_size() const { return this->size_; }

void *BaseHeaderFieldDef::get_indexed_address(void *address) const {
  return (void *)((uint8_t *)address + this->index_);
}

BaseHeaderFieldDef::BaseHeaderFieldDef(FieldIndexer &indexer, std::size_t size) : index_(0), size_(size) {
  indexer.index_field(*this);
}

} // namespace shmem
} // namespace common
} // namespace lib
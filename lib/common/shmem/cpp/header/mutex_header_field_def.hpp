#pragma once

#include <boost/interprocess/sync/interprocess_condition.hpp>
#include <boost/interprocess/sync/interprocess_mutex.hpp>
#include <lib/common/shmem/cpp/header/base_header_field_def.hpp>
#include <lib/common/shmem/cpp/header/field_indexer.hpp>

namespace lib {
namespace common {
namespace shmem {

class MutexHeaderFieldDef : public BaseHeaderFieldDef {
public:
  MutexHeaderFieldDef(FieldIndexer &indexer)
      : BaseHeaderFieldDef(indexer, sizeof(boost::interprocess::interprocess_mutex)){};
  ~MutexHeaderFieldDef(){};
};

class ConditionHeaderFieldDef : public BaseHeaderFieldDef {
public:
  ConditionHeaderFieldDef(FieldIndexer &indexer)
      : BaseHeaderFieldDef(indexer, sizeof(boost::interprocess::interprocess_condition)){};
  ~ConditionHeaderFieldDef(){};
};

} // namespace shmem
} // namespace common
} // namespace lib
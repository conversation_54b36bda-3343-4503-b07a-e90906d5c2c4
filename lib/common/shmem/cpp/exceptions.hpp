#pragma once

#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace common {
namespace shmem {

class shmem_error : public maka_error {
public:
  explicit shmem_error(const std::string &what, size_t stack_skip = 1) : maka_error(what, stack_skip + 1) {}
};

class shmem_timeout_error : public shmem_error {
public:
  explicit shmem_timeout_error(const std::string &what, size_t stack_skip = 1) : shmem_error(what, stack_skip + 1) {}
};

} // namespace shmem
} // namespace common
} // namespace lib
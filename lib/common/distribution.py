from numpy.random import normal


class NormalDistribution:
    def __init__(self, mean: float, std: float):
        self._mean = mean
        self._std = std

    @property
    def mean(self) -> float:
        return self._mean

    @property
    def std(self) -> float:
        return self._std

    @property
    def value(self) -> float:
        return float(normal(self._mean, self._std))


class PointDistribution(NormalDistribution):
    def __init__(self, mean: float):
        super().__init__(mean, 0)

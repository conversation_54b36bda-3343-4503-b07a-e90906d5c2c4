class EscapeColor:
    """
    The following colors works with most terminals and terminals emulators.

    https://misc.flogisoft.com/bash/tip_colors_and_formatting
    """

    RESET_ALL: str = "\033[0m"
    BOLD: str = "\033[1m"
    DIM: str = "\033[2m"
    ITALIC: str = "\033[2m"
    UNDERLINED: str = "\033[4m"
    BLINKING: str = "\033[5m"
    # 6 also blinking
    REVERSE: str = "\033[7m"
    HIDDEN: str = "\033[8m"  # useful for passwords
    STRIKETHROUGH: str = "\033[9m"
    # 10-20 no effect
    RESET_BOLD: str = "\033[21m"
    RESET_DIM: str = "\033[22m"
    # 23 no effect
    RESET_UNDERLINED: str = "\033[24m"
    RESET_BLINK: str = "\033[25m"
    # 26 no effect
    RESET_REVERSE: str = "\033[27m"
    RESET_HIDDEN: str = "\033[28m"
    # 29 no effect
    BLACK: str = "\033[30m"
    RED: str = "\033[31m"
    GREEN: str = "\033[32m"
    YELLOW: str = "\033[33m"
    BLUE: str = "\033[34m"
    MAGENTA: str = "\033[35m"
    CYAN: str = "\033[36m"
    LIGHT_GREY: str = "\033[37m"
    # 38-39 no effect
    BACKGROUND_BLACK: str = "\033[40m"
    BACKGROUND_RED: str = "\033[41m"
    BACKGROUND_GREEN: str = "\033[42m"
    BACKGROUND_YELLOW: str = "\033[43m"
    BACKGROUND_BLUE: str = "\033[44m"
    BACKGROUND_MAGENTA: str = "\033[45m"
    BACKGROUND_CYAN: str = "\033[46m"
    BACKGROUND_LIGHT_GREY: str = "\033[47m"
    # 48-51 no effect
    # 52 seems to be underline again
    # 53-89 no effect
    DARK_GREY: str = "\033[90m"
    LIGHT_RED: str = "\033[91m"
    LIGHT_GREEN: str = "\033[92m"
    LIGHT_YELLOW: str = "\033[93m"
    LIGHT_BLUE: str = "\033[94m"
    LIGHT_MAGENTA: str = "\033[95m"
    LIGHT_CYAN: str = "\033[96m"
    WHITE: str = "\033[97m"
    # 98-99 no effect
    BACKGROUND_DARK_GREY: str = "\033[90m"
    BACKGROUND_LIGHT_RED: str = "\033[91m"
    BACKGROUND_LIGHT_GREEN: str = "\033[92m"
    BACKGROUND_LIGHT_YELLOW: str = "\033[93m"
    BACKGROUND_LIGHT_BLUE: str = "\033[94m"
    BACKGROUND_LIGHT_MAGENTA: str = "\033[95m"
    BACKGROUND_LIGHT_CYAN: str = "\033[96m"
    BACKGROUND_WHITE: str = "\033[97m"

    @staticmethod
    def apply(prefix: str, s: str) -> str:
        return f"{prefix}{s}{EscapeColor.RESET_ALL}"

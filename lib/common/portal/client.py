import json
import os
import time
from typing import TYPE_CHECKING, Any, Dict, cast

import grpc
import requests

import generated.frontend.proto.status_bar_pb2 as status_bar_pb
import generated.portal.proto.health_pb2 as health_pb
import generated.portal.proto.health_pb2_grpc as health_grpc
import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)

STUB_HEALTH_SERVICE = "HealthService"

STUB_TYPE = {
    STUB_HEALTH_SERVICE: health_grpc.HealthServiceStub,
}
if TYPE_CHECKING:
    StatusType = status_bar_pb.StatusValue
else:
    StatusType = status_bar_pb.Status


M2M_TOKEN_FILE = ".auth0-m2m-token.json"


class PortalClient:
    def __init__(self, url: str) -> None:
        self._url = url if url is not None else "customer-stg.cloud.carbonrobotics.com:443"
        self._channel = None
        self._stubs: Dict[str, Any] = {}
        self._client_id = os.getenv("AUTH0_CLIENT_ID", os.environ.get("MAKA_CLIENT_ID", None))
        self._client_secret = os.getenv("AUTH0_CLIENT_SECRET", os.environ.get("MAKA_CLIENT_SECRET", None),)
        self._domain = os.getenv("MAKA_AUTH_DOMAIN", os.environ.get("MAKA_AUTH_DOMAIN", None))

    def _maybe_connect(self, service: str) -> None:
        if self._channel is None:
            token = None

            if os.path.exists(M2M_TOKEN_FILE):
                with open(M2M_TOKEN_FILE, "r") as f:
                    try:
                        token = json.load(f)
                        if "error" in token:
                            token = None
                    except ValueError:
                        token = None
                        pass

            if token is None:
                response = requests.post(
                    f"https://{self._domain}/oauth/token",
                    json={
                        "client_id": self._client_id,
                        "client_secret": self._client_secret,
                        "audience": "https://robot.carbonrobotics.com",
                        "grant_type": "client_credentials",
                    },
                )

                token = response.json()

                if response.status_code != 200 or "error" in token:
                    raise RuntimeError(f"Failed to get M2M token: {response.text}")

                with open(M2M_TOKEN_FILE, "w+") as outfile:
                    json.dump(token, outfile)

            if self._url.endswith(":443"):
                credentials = grpc.composite_channel_credentials(
                    grpc.ssl_channel_credentials(), grpc.access_token_call_credentials(token["access_token"]),
                )
                self._channel = grpc.aio.secure_channel(self._url, credentials)
            else:
                # if it's not on port 443, use an insecure channel but hack in the credentials
                class CredentialsInterceptor(grpc.aio.UnaryUnaryClientInterceptor):
                    async def intercept_unary_unary(
                        self, continuation: Any, client_call_details: grpc.ClientCallDetails, request: Any
                    ) -> Any:
                        metadata = client_call_details.metadata if client_call_details.metadata else grpc.aio.Metadata()
                        metadata.add("authorization", "Bearer %s" % token["access_token"] if token else "")
                        updated_call_details = grpc.aio.ClientCallDetails(
                            client_call_details.method,
                            client_call_details.timeout,
                            metadata,
                            client_call_details.credentials,
                            client_call_details.wait_for_ready,
                        )
                        response = await continuation(updated_call_details, request)
                        return response

                self._channel = grpc.aio.insecure_channel(self._url, interceptors=[CredentialsInterceptor()])

        if service not in self._stubs:
            self._stubs[service] = STUB_TYPE[service](self._channel)

    def _get_health_stub(self) -> health_grpc.HealthServiceStub:
        self._maybe_connect(STUB_HEALTH_SERVICE)
        assert STUB_HEALTH_SERVICE in self._stubs and self._stubs[STUB_HEALTH_SERVICE] is not None
        return cast(health_grpc.HealthServiceStub, self._stubs[STUB_HEALTH_SERVICE])

    async def LogHealth(
        self, location: str, alarms: str, model: str, status: int, status_changed_at: int, robot: str
    ) -> None:
        stub = self._get_health_stub()
        loaded_location = json.loads(location)
        loaded_alarms = []
        now = int(time.time() * 1000)
        for alarm in json.loads(alarms):
            loaded_alarms.append(health_pb.AlarmRow(**alarm))
        req = health_pb.HealthLog(
            location=health_pb.Location(
                x=loaded_location.get("latitude", None),
                y=loaded_location.get("longitude", None),
                z=loaded_location.get("altitude", None),
            ),
            alarms=loaded_alarms,
            model=model,
            status=cast(StatusType, status),
            status_changed_at=status_changed_at,
            reported_at=now,
            robot_serial=robot,
        )
        await stub.LogHealth(req)

    async def ReportIssue(self, description: str, phone: str) -> None:
        stub = self._get_health_stub()
        now = int(time.time() * 1000)
        req = health_pb.IssueReport(description=description, phone_number=phone, reported_at=now)
        await stub.ReportIssue(req)

import importlib
from types import ModuleType
from typing import Any, Callable, Dict, TypeVar

from lib.common.devices.device import Device

T = TypeVar("T", bound=Device)


class DeviceDefinition:
    def __init__(self, device_id: str, module_name: str, boot_func_name: str, kwargs: Dict[str, Any]):
        self._device_id = device_id
        self._module: ModuleType = importlib.import_module(module_name)
        self._boot_func: Callable[..., T] = getattr(self._module, boot_func_name)
        self._kwargs = kwargs

    def boot(self) -> Device:
        return self._boot_func(device_id=self._device_id, **self._kwargs)

    @property
    def device_id(self) -> str:
        return self._device_id

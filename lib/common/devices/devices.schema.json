{"$id": "https://maka-ars.com/schema/devices.schema.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "Devices", "type": "object", "properties": {"devices": {"type": "object", "description": "Dictionary of Devices", "propertyNames": {"pattern": "^[A-Za-z_][A-Za-z0-9_]*$"}, "additionalProperties": {"type": "object", "description": "Device Definition", "properties": {"module": {"type": "string", "description": "Python Module Name where to boot func is defined"}, "boot_func": {"type": "string", "description": "Name of the Python Function to boot the device with. (Often a class constructor)"}, "kwargs": {"type": "object", "description": "Keyword arguments to pass to the boot func.", "propertyNames": {"pattern": "^[A-Za-z_][A-Za-z0-9_]*$"}, "additionalProperties": true}}, "required": ["module", "boot_func"], "additionalProperties": false}}}, "required": ["devices"]}
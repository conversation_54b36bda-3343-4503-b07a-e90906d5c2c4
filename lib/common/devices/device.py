import abc
import enum
from typing import Any, Dict

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)


class DeviceStatusCode(enum.Enum):
    NOT_IMPLEMENTED = 0
    NEW = 1  # Device Newly Created
    BOOTING = 2  # Device Currently in its booting process
    OK = 3  # Device is booted and ready
    STOPPING = 4  # Device is stopping
    STOPPED = 5  # Device is stopped
    WARNING = 6  # Device is responding but overloaded or near failure
    ERROR = 7  # Device is in an error state
    NETWORK_ERROR = 8  # Device not responding to network requests


RUNNING_SET = set([DeviceStatusCode.OK, DeviceStatusCode.WARNING])


class DeviceStatus:
    def __init__(self, code: DeviceStatusCode, msg: str = ""):
        self._code = code
        self._msg = msg

    @property
    def code(self) -> DeviceStatusCode:
        return self._code

    @property
    def msg(self) -> str:
        return self._msg

    def is_running(self) -> bool:
        return self._code in RUNNING_SET

    def to_json(self) -> Dict[str, Any]:
        return {"code": str(self.code), "msg": self._msg}


class DeviceDiagnostic:
    def __init__(self, status: DeviceStatus, diagnostic: Dict[str, Any]):
        self._status = status
        self._diagnostic = diagnostic

    @property
    def status(self) -> DeviceStatus:
        return self._status

    @property
    def diagnostic(self) -> Dict[str, Any]:
        return self._diagnostic

    def to_json(self) -> Dict[str, Any]:
        return {"status": self.status.to_json(), "diagnostic": self.diagnostic}


class Device(abc.ABC):
    """
    A Device represents a connection to a physical piece of hardware that the robot communicates directly with.

    A device must respect the Device Contract:
    1. Fault Free Object Construction: __init__ function cannot raise an exception or fail
    2. Enable: Actual Device connection is only established in enable
    3. Reset: Device connection can be reset
    """

    def __init__(self, device_id: str, **kwargs: Any) -> None:
        self._device_id = device_id
        self._status = DeviceStatus(DeviceStatusCode.NEW)
        LOG.warning(f"Unused args {kwargs}")

    @property
    def device_id(self) -> str:
        return self._device_id

    async def get_status(self) -> DeviceStatus:
        return self._status

    def get_status_sync(self) -> DeviceStatus:
        return self._status

    def _set_status(self, code: DeviceStatusCode, msg: str = "") -> None:
        self._status = DeviceStatus(code, msg)

    async def get_diagnostic(self) -> DeviceDiagnostic:
        status = await self.get_status()
        diagnostic = {}
        if status.code == DeviceStatusCode.OK or status.code == DeviceStatusCode.WARNING:
            diagnostic = await self._get_diagnostic()
        return DeviceDiagnostic(status, diagnostic)

    async def _get_diagnostic(self) -> Dict[str, Any]:
        return {}

    @abc.abstractmethod
    async def enable(self) -> None:
        pass

    @abc.abstractmethod
    async def disable(self) -> None:
        pass

    async def reset(self) -> None:
        await self.disable()
        await self.enable()

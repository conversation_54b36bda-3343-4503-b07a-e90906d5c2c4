from typing import Dict

from lib.common.devices.device import Device, DeviceStatusCode


class MockDevice(Device):
    def __init__(self, device_id: str, x: int = 42):
        super().__init__(device_id)
        self._x = x

    @property
    def x(self) -> int:
        return self._x

    async def _get_diagnostic(self) -> Dict[str, str]:
        return {}

    async def enable(self) -> None:
        self._set_status(DeviceStatusCode.OK)

    async def disable(self) -> None:
        self._set_status(DeviceStatusCode.STOPPED)

import json
import os
import tempfile
from typing import Any, Dict

import pytest
from jsonschema import Draft7Validator

from lib.common.devices.registry import SCHEMA_PATH, DeviceRegistry
from lib.common.devices.tests.mock_device import MockDevice

test_file = {
    "devices": {
        "test1": {"module": "lib.common.devices.tests.mock_device", "boot_func": "MockDevice"},
        "test2": {"module": "lib.common.devices.tests.mock_device", "boot_func": "MockDevice", "kwargs": {"x": 89}},
    }
}


# This function should only be relevant to tests
async def boot_devices_from_json(registry: DeviceRegistry, device_json: Dict[str, Any], enable: bool = False) -> None:
    with tempfile.TemporaryDirectory() as td:
        temp_file_path = os.path.join(td, "devices.json")
        with open(temp_file_path, "w") as f:
            json.dump(device_json, f)
        await registry.boot_devices(temp_file_path, enable=enable)


def test_validate_schema() -> None:
    with open(SCHEMA_PATH) as f:
        schema = json.load(f)
    Draft7Validator.check_schema(schema)


@pytest.mark.asyncio
async def test_device_registry() -> None:
    registry = DeviceRegistry()
    await boot_devices_from_json(registry, test_file, enable=True)
    test1 = await registry.get_device(MockDevice, "test1")
    test2 = await registry.get_device(MockDevice, "test2")
    assert test1 is not None
    assert test2 is not None
    assert test1.x == 42
    assert test2.x == 89
    assert test1.device_id == "test1"
    assert test2.device_id == "test2"

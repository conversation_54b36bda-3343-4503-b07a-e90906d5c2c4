import asyncio
import json
import os
from asyncio import AbstractEventLoop
from typing import Dict, Generator, Iterable, List, Optional, Set, Type, TypeVar

import jsonschema

import lib.common.logging
from lib.common.devices.device import Device, DeviceDiagnostic, DeviceStatus
from lib.common.devices.device_definition import DeviceDefinition
from lib.common.error import MakaException
from lib.common.tasks.manager import get_event_loop_by_name

LOG = lib.common.logging.get_logger(__name__)

SCHEMA_PATH = os.path.realpath(os.path.join(os.getcwd(), os.path.dirname(__file__), "devices.schema.json"))


T = TypeVar("T", bound=Device)


class DeviceRegistryException(MakaException):
    pass


class DeviceRegistryFile:

    NAME = "devices.json"
    DEVICES = "devices"
    MODULE = "module"
    BOOT_FUNC = "boot_func"
    KWARGS = "kwargs"

    def __init__(self, device_file_path: str):
        with open(SCHEMA_PATH) as schema_file:
            self._schema = json.load(schema_file)
        with open(device_file_path) as device_file:
            self._device_definitions = json.load(device_file)
        jsonschema.validate(self._device_definitions, self._schema)

    def device_defs(self) -> Generator[DeviceDefinition, None, None]:
        if self.DEVICES not in self._device_definitions or type(self._device_definitions[self.DEVICES]) != dict:
            LOG.error("Device file is not properly formatted")
            return
        for device_id, device_def in self._device_definitions[self.DEVICES].items():
            yield DeviceDefinition(
                device_id,
                device_def[self.MODULE],
                device_def[self.BOOT_FUNC],
                device_def[self.KWARGS] if self.KWARGS in device_def else {},
            )


class DeviceRegistry:
    def __init__(self) -> None:
        self._registry: Dict[str, Device] = {}
        self._event_loop = get_event_loop_by_name()

    # Sync API Temporary because the robot is not async enough yet
    def boot_devices_from_sync(
        self,
        device_file_path: str,
        event_loop: AbstractEventLoop,
        enable: bool = True,
        skip_list: Optional[Set[str]] = None,
    ) -> None:
        future = asyncio.run_coroutine_threadsafe(self.boot_devices(device_file_path, enable, skip_list), event_loop)
        return future.result()

    def stop_all_devices_from_sync(self) -> None:
        future = asyncio.run_coroutine_threadsafe(self.stop_all_devices(), self._event_loop)
        return future.result()

    def get_device_from_sync(self, device_type: Type[T], device_id: str) -> T:
        future = asyncio.run_coroutine_threadsafe(self.get_device(device_type, device_id), self._event_loop)
        return future.result()

    # boot flag is exposed so that it can be False for testing
    async def boot_devices(
        self, device_file_path: str, enable: bool = True, skip_list: Optional[Set[str]] = None
    ) -> None:
        LOG.info("Loading Registry")
        device_registry_file = DeviceRegistryFile(device_file_path)
        await self.boot_devices_from_iterable(device_registry_file.device_defs(), enable=enable, skip_list=skip_list)

    async def boot_devices_from_iterable(
        self, device_iterable: Iterable[DeviceDefinition], enable: bool = True, skip_list: Optional[Set[str]] = None
    ) -> None:
        LOG.info("Booting Registry Devices")
        if skip_list is None:
            skip_list = set()
        for device_def in device_iterable:
            if device_def.device_id not in skip_list:
                LOG.info(f"Loading Device into Registry: {device_def.device_id}")
                device: Device = device_def.boot()
                self._registry[device.device_id] = device
        if enable:
            await asyncio.gather(*[device.enable() for device in self._registry.values()])

    async def stop_all_devices(self) -> None:
        await asyncio.gather(*[device.disable() for device in self._registry.values()])

    async def get_device(self, device_type: Type[T], device_id: str) -> T:
        device = await self.get_generic_device(device_id)
        if not isinstance(device, device_type):
            raise DeviceRegistryException("Device Not Found")
        return device

    async def get_devices_by_type(self, device_type: Type[T]) -> List[T]:
        return [dev for dev in self._registry.values() if isinstance(dev, device_type)]

    async def get_device_list(self) -> List[Device]:
        return list(self._registry.values())

    async def get_device_list_of_type(self, device_type: Type[T]) -> List[T]:
        return [x for x in self._registry.values() if isinstance(x, device_type)]

    async def get_generic_device(self, device_id: str) -> Optional[Device]:
        return self._registry.get(device_id)

    async def get_status(self) -> Dict[str, DeviceStatus]:
        return {device_id: await device.get_status() for device_id, device in self._registry.items()}

    async def get_diagnostic(self) -> Dict[str, DeviceDiagnostic]:
        return {device_id: await device.get_diagnostic() for device_id, device in self._registry.items()}

"""This file stores the base device configuration for each service and generation"""
import json
from typing import Any, Callable, Dict, Generator, Optional, Set, Tuple, cast

import lib.common.logging
from lib.common.abstract.config.config import TreeNode
from lib.common.devices.device_definition import DeviceDefinition
from lib.common.generation import GENERATION, generation, is_bud, is_reaper, is_rtc, is_slayer

LOG = lib.common.logging.get_logger(__name__)


class DeviceConfig:
    def __init__(
        self,
        device_id: str,
        module_name: str,
        boot_func_name: str,
        kwargs_func: Callable[[bool], Dict[str, Any]],
        id_key: Optional[str] = None,
    ):
        self.device_id = device_id
        self.module_name = module_name
        self.boot_func_name = boot_func_name
        self._kwargs_func = kwargs_func
        self._id_key = id_key

    def kwargs(self, id: int, use_treadkill: bool, conf_tree: Optional[TreeNode]) -> Dict[str, Any]:
        addtl_kwargs: Dict[str, Any] = {}
        if conf_tree is not None:
            str_val = conf_tree.get_string_value()
            if str_val:
                addtl_kwargs = json.loads(str_val)
        kwargs = self._kwargs_func(use_treadkill)
        for key, value in kwargs.items():
            if key not in addtl_kwargs:
                addtl_kwargs[key] = value
        if self._id_key is not None:
            addtl_kwargs[self._id_key] = id
        LOG.info(f"kwargs = {addtl_kwargs}")
        return addtl_kwargs


def get_nofx_kwargs(use_treadkill: bool, version_check: bool) -> Dict[str, Any]:
    if is_bud():
        return {
            "firmware_name": "NoFX_Bud",
            "version_check": version_check,
            "reversed_polarity": use_treadkill,
        }
    elif is_slayer():
        return {
            "reversed_polarity": use_treadkill,
            "firmware_name": "wheel_encoder_board",
            "bootloader_type": "mcuboot",
            "pps_enabled": False,
            "version_check": version_check,
            "use_broadcast": True,
            "owner": version_check,
        }
    elif is_reaper():
        return {
            "reversed_polarity": use_treadkill,
            "firmware_name": "wheel_encoder_board",
            "bootloader_type": "mcuboot",
            "pps_enabled": False,
            "version_check": version_check,
            "use_broadcast": True,
            "owner": version_check,
        }
    raise Exception("Unsupported Gen")


def get_strobe_kwargs(version_check: bool) -> Dict[str, Any]:
    if is_bud():
        return {}
    elif is_slayer():
        return {
            "bootloader_type": "mcuboot",
            "firmware_name": "strobe_mother_board",
            "pps_enabled": False,
            "version_check": version_check,
        }
    elif is_reaper():
        return {
            "bootloader_type": "mcuboot",
            "firmware_name": "strobe_mother_board",
            "pps_enabled": False,
            "version_check": version_check,
        }
    raise Exception("Unsupported Gen")


def get_pulczar_kwargs() -> Dict[str, Any]:
    if is_bud():
        return {
            "settle_timeout_ms": 1000,
            "settle_window": 50,
            "fail_on_bad_status": False,
            "pps_enabled": True,
            "default_intensity": 0.7,
        }
    elif is_slayer():
        return {
            "settle_timeout_ms": 1000,
            "settle_window": 50,
            "fail_on_bad_status": False,
            "bootloader_type": "mcuboot",
            "firmware_name": "pulsar_board",
            "pps_enabled": False,
            "pan_limit_range": [14000, 17000],
            "tilt_limit_range": [12000, 15000],
            "default_intensity": 0.7,
            "boot_gimbal_mrpm": 100000,
        }
    elif is_reaper():
        return {
            "settle_timeout_ms": 1000,
            "settle_window": 50,
            "fail_on_bad_status": False,
            "bootloader_type": "mcuboot",
            "firmware_name": "pulsar_board",
            "pps_enabled": False,
            "pan_limit_range": [12000, 16000],
            "tilt_limit_range": [12000, 16000],
            "default_intensity": 1.0,
            "boot_gimbal_mrpm": 100000,
            "home_offset": [1200, 1200],
        }
    raise Exception("Unsupported Gen")


def get_gps_kwargs(owner: bool = True) -> Dict[str, Any]:
    if is_bud():
        return {
            "bootloader_type": "mcuboot",
            "firmware_name": "gps_board",
            "pps_enabled": False,
            "version_check": False,
            "initialize_firmware": False,
        }
    elif is_slayer() or is_reaper() or is_rtc():
        return {
            "bootloader_type": "mcuboot",
            "firmware_name": "gps_board",
            "pps_enabled": False,
            "version_check": owner,
            "initialize_firmware": True,
        }
    raise Exception("Unsupported Gen")


def get_safety_plc_kwargs() -> Dict[str, Any]:
    return {}


def get_supervisory_plc_kwargs() -> Dict[str, Any]:
    return {}


def get_jimbox_kwargs() -> Dict[str, Any]:
    if is_bud():
        return {}
    else:
        return {
            "bootloader_type": "mcuboot",
            "firmware_name": "jimbox_board",
            "pps_enabled": False,
            "version_check": False,
        }


def get_hh_kwargs() -> Dict[str, Any]:
    return {
        "bootloader_type": "mcuboot",
        "pps_enabled": False,
        "version_check": False,  # TODO fix once we have fw versions for board
    }


CONFIGS = {
    GENERATION.BUD: {
        "aimbot": [
            {
                "count": 8,
                "config": DeviceConfig(
                    device_id="pulczar",
                    module_name="lib.common.devices.boards.pulczar.pulczar_board_device",
                    boot_func_name="PulczarBoardDevice",
                    kwargs_func=lambda _: get_pulczar_kwargs(),
                    id_key="scanner_id",
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="nofx_board_",
                    module_name="lib.common.devices.boards.nofx.nofx_board_device",
                    boot_func_name="NoFXBoardDevice",
                    kwargs_func=lambda use_treadkill: get_nofx_kwargs(use_treadkill=use_treadkill, version_check=False),
                ),
            },
        ],
        "hardware_manager": [
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="nofx_board_",
                    module_name="lib.common.devices.boards.nofx.nofx_board_device",
                    boot_func_name="NoFXBoardDevice",
                    kwargs_func=lambda use_treadkill: get_nofx_kwargs(use_treadkill=use_treadkill, version_check=True),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="strobe_control_",
                    module_name="lib.common.devices.boards.strobe_control.strobe_control_board_device",
                    boot_func_name="StrobeControlBoardDevice",
                    kwargs_func=lambda _: get_strobe_kwargs(version_check=True),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="gps_",
                    module_name="lib.common.devices.boards.gps.gps_board_device",
                    boot_func_name="GPSBoardDevice",
                    kwargs_func=lambda _: get_gps_kwargs(),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="safety_plc_",
                    module_name="lib.common.devices.boards.safety_plc.safety_plc_device",
                    boot_func_name="SafetyPLCDevice",
                    kwargs_func=lambda _: get_safety_plc_kwargs(),
                ),
            },
        ],
        "metrics_aggregator": [
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="nofx_board_",
                    module_name="lib.common.devices.boards.nofx.nofx_board_device",
                    boot_func_name="NoFXBoardDevice",
                    kwargs_func=lambda use_treadkill: get_nofx_kwargs(use_treadkill=use_treadkill, version_check=False),
                ),
            },
        ],
    },
    GENERATION.SLAYER: {
        "aimbot": [
            {
                "count": 10,
                "config": DeviceConfig(
                    device_id="pulczar",
                    module_name="lib.common.devices.boards.pulczar.pulczar_board_device",
                    boot_func_name="PulczarBoardDevice",
                    kwargs_func=lambda _: get_pulczar_kwargs(),
                    id_key="scanner_id",
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="nofx_board_",
                    module_name="lib.common.devices.boards.nofx.nofx_board_device",
                    boot_func_name="NoFXBoardDevice",
                    kwargs_func=lambda use_treadkill: get_nofx_kwargs(use_treadkill=use_treadkill, version_check=False),
                ),
            },
        ],
        "hardware_manager": [
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="nofx_board_",
                    module_name="lib.common.devices.boards.nofx.nofx_board_device",
                    boot_func_name="NoFXBoardDevice",
                    kwargs_func=lambda use_treadkill: get_nofx_kwargs(use_treadkill=use_treadkill, version_check=True),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="strobe_control_",
                    module_name="lib.common.devices.boards.strobe_control.strobe_control_board_device",
                    boot_func_name="StrobeControlBoardDevice",
                    kwargs_func=lambda _: get_strobe_kwargs(version_check=True),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="gps_",
                    module_name="lib.common.devices.boards.gps.gps_board_device",
                    boot_func_name="GPSBoardDevice",
                    kwargs_func=lambda _: get_gps_kwargs(owner=True),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="supervisory_plc_",
                    module_name="lib.common.devices.boards.supervisory_plc.supervisory_plc_device",
                    boot_func_name="SupervisoryPLCDevice",
                    kwargs_func=lambda _: get_supervisory_plc_kwargs(),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="safety_plc_",
                    module_name="lib.common.devices.boards.safety_plc.safety_plc_device",
                    boot_func_name="SafetyPLCDevice",
                    kwargs_func=lambda _: get_safety_plc_kwargs(),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="jimbox_",
                    module_name="lib.common.devices.boards.jimbox.jimbox_device",
                    boot_func_name="JimboxBoardDevice",
                    kwargs_func=lambda _: get_jimbox_kwargs(),
                ),
            },
        ],
        "metrics_aggregator": [
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="nofx_board_",
                    module_name="lib.common.devices.boards.nofx.nofx_board_device",
                    boot_func_name="NoFXBoardDevice",
                    kwargs_func=lambda use_treadkill: get_nofx_kwargs(use_treadkill=use_treadkill, version_check=False),
                ),
            },
        ],
    },
    GENERATION.REAPER: {
        "aimbot": [
            {
                "count": 2,
                "config": DeviceConfig(
                    device_id="pulczar",
                    module_name="lib.common.devices.boards.pulczar.pulczar_board_device",
                    boot_func_name="PulczarBoardDevice",
                    kwargs_func=lambda _: get_pulczar_kwargs(),
                    id_key="scanner_id",
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="nofx_board_",
                    module_name="lib.common.devices.boards.nofx.nofx_board_device",
                    boot_func_name="NoFXBoardDevice",
                    kwargs_func=lambda use_treadkill: get_nofx_kwargs(use_treadkill=use_treadkill, version_check=False),
                ),
            },
        ],
        "hardware_manager": [
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="nofx_board_",
                    module_name="lib.common.devices.boards.nofx.nofx_board_device",
                    boot_func_name="NoFXBoardDevice",
                    kwargs_func=lambda use_treadkill: get_nofx_kwargs(use_treadkill=use_treadkill, version_check=True),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="gps_",
                    module_name="lib.common.devices.boards.gps.gps_board_device",
                    boot_func_name="GPSBoardDevice",
                    kwargs_func=lambda _: get_gps_kwargs(),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="safety_plc_",
                    module_name="lib.common.devices.boards.safety_plc.safety_plc_device",
                    boot_func_name="SafetyPLCDevice",
                    kwargs_func=lambda _: get_safety_plc_kwargs(),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="supervisory_plc_",
                    module_name="lib.common.devices.boards.supervisory_plc.supervisory_plc_device",
                    boot_func_name="SupervisoryPLCDevice",
                    kwargs_func=lambda _: get_supervisory_plc_kwargs(),
                ),
            },
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="jimbox_",
                    module_name="lib.common.devices.boards.jimbox.jimbox_device",
                    boot_func_name="JimboxBoardDevice",
                    kwargs_func=lambda _: get_jimbox_kwargs(),
                ),
            },
        ],
        "metrics_aggregator": [
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="nofx_board_",
                    module_name="lib.common.devices.boards.nofx.nofx_board_device",
                    boot_func_name="NoFXBoardDevice",
                    kwargs_func=lambda use_treadkill: get_nofx_kwargs(use_treadkill=use_treadkill, version_check=False),
                ),
            },
        ],
    },
    GENERATION.RTC: {
        "hardware_manager": [
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="gps_",
                    module_name="lib.common.devices.boards.gps.gps_board_device",
                    boot_func_name="GPSBoardDevice",
                    kwargs_func=lambda _: get_gps_kwargs(),
                ),
            },
        ],
        "tractor_ctl": [
            {
                "count": 1,
                "config": DeviceConfig(
                    device_id="hh_",
                    module_name="lib.common.devices.boards.hasselhoff.hasselhoff_board_device",
                    boot_func_name="HasselhoffBoardDevice",
                    kwargs_func=lambda _: get_hh_kwargs(),
                ),
            },
        ],
    },
}


def _build_map(parent_conf: TreeNode) -> Dict[str, TreeNode]:
    conf_nodes: Dict[str, TreeNode] = {}
    for child in parent_conf.get_children_nodes():
        conf_nodes[child.get_name()] = child
    return conf_nodes


def _get_board_params_map(device_id: str, device_num: int, override_conf: TreeNode) -> Dict[str, Any]:
    if device_id == "pulczar":
        root_conf = override_conf.get_node("scanner_params")
        scanner_list = root_conf.get_children_nodes()
        ov = [s for s in scanner_list if s.get_name() == "scanner" + str(device_num)]
        if len(ov) > 0:
            return _build_map(ov[0])
        return {}

    device_type_overrides = override_conf.get_node(device_id + "params")
    if device_type_overrides is not None:
        return _build_map(device_type_overrides)

    return {}


def get_devices_for_service(
    service_name: str,
    override_conf: TreeNode,
    common_conf: TreeNode,
    gen: GENERATION = generation(),
    sid_to_address_and_ports: Dict[int, Tuple[str, int, int]] = {},  # sid -> (board_address, port, boot_loader_port)
) -> Generator[DeviceDefinition, None, None]:
    assert gen in CONFIGS
    assert service_name in CONFIGS[gen]
    conf_nodes = _build_map(override_conf.get_node("extra_params_encoded"))
    use_treadkill = False
    if not is_rtc():
        use_treadkill = common_conf.get_node("use_treadkill").get_bool_value()
    LOG.info(f"Booting with treadkill? {use_treadkill}")
    for dev_type in CONFIGS[gen][service_name]:
        dev_conf = cast(DeviceConfig, dev_type["config"])
        dev_count = dev_type["count"]
        if gen == GENERATION.REAPER and dev_conf.device_id == "pulczar":
            dev_count = len(sid_to_address_and_ports)

        for i in range(cast(int, dev_count)):
            numeric_id = i + 1
            str_id = f"{dev_conf.device_id}{numeric_id}"
            kwargs = dev_conf.kwargs(id=numeric_id, use_treadkill=use_treadkill, conf_tree=conf_nodes.get(str_id, None))
            kwargs.update(_get_board_params_map(dev_conf.device_id, numeric_id, override_conf))

            if gen == GENERATION.REAPER and dev_conf.device_id == "pulczar":
                address_and_ports = sid_to_address_and_ports.get(numeric_id, None)
                if address_and_ports is None:
                    LOG.error(f"Could not find address and ports for sid: {numeric_id}")
                    continue
                kwargs["board_address"] = address_and_ports[0]
                kwargs["port"] = address_and_ports[1]
                kwargs["boot_loader_port"] = address_and_ports[2]

            yield DeviceDefinition(
                str_id, dev_conf.module_name, dev_conf.boot_func_name, kwargs,
            )


def get_skip_list(override_conf: TreeNode) -> Set[str]:
    children = override_conf.get_node("skip_list").get_children_nodes()
    return set([node.get_name() for node in children])

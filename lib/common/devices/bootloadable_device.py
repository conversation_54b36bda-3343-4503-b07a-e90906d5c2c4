import abc
import asyncio
from typing import Any, Callable, Optional, Tuple, TypeVar

import lib.common.logging
from firmware.release.firmware_release_manager import Firmware, FirmwareVersion, get_latest_firmware_version
from lib.common.devices.device import Device, DeviceStatusCode
from lib.common.error import MakaException
from lib.common.role import is_simulator
from lib.common.time import maka_control_timestamp_ms
from lib.drivers.errors.error import FatalMakaDeviceException
from lib.drivers.nanopb.bootloadable_connector import (
    BOOTLOADER_PORT,
    PROGRAM_WAIT_TIME_MS,
    RESET_BOOTLOADER_WAIT_TIME_MS,
    BootloadableConnector,
)
from lib.drivers.psoc_ethernet.psoc_ethernet_bootloader import program
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector
from lib.drivers.zephyr.mcumgr.mcumgr import MCUMGR_PORT, MCUMGRClient

LOG = lib.common.logging.get_logger(__name__)


C = TypeVar("C", bound=BootloadableConnector)


class BootloadableDeviceException(MakaException):
    pass


class PsocEthernetBootloadableDevice(Device, abc.ABC):
    def __init__(
        self,
        *args: Any,
        strict_version: bool = True,
        auto_updates: bool = True,
        version_check: bool = True,
        force_update: bool = False,
        pps_enabled: bool = True,
        pps_kp: int = 900,
        pps_ki: int = 900,
        pps_offset: int = 0,
        pps_offsync_limit_ms: int = 100,
        pps_resync_interval_ms: int = 10000,
        bootloader_type: str = "psoc",
        firmware_name: str = "Invalid",
        owner: bool = True,
        boot_loader_port: Optional[int] = None,
        **kwargs: Any,
    ) -> None:
        super().__init__(*args, **kwargs)
        self._strict_version = strict_version
        self._auto_updates = auto_updates
        self._version_check = version_check
        self._force_update = force_update
        self._pps_enabled = pps_enabled
        self._pps_kp = pps_kp
        self._pps_ki = pps_ki
        self._pps_offset = pps_offset
        self._pps_offsync_limit_ms = pps_offsync_limit_ms
        self._pps_resync_interval_ms = pps_resync_interval_ms
        self._pps_resync_task: Optional[asyncio.Task[None]] = None
        self._bootloadable_board: Optional[BootloadableConnector] = None
        self._bootloader_type = bootloader_type
        self._firmware_name = firmware_name
        self._firmware_initialized = False
        self._owner = owner
        self._boot_loader_port = boot_loader_port or MCUMGR_PORT

    async def _get_timestamp_ms(self) -> int:
        raise BootloadableDeviceException("Get Timestamp Not Implemented")

    async def _set_epoch_timestamp(self) -> None:
        raise BootloadableDeviceException("Set Epoch Timestamp Not Implemented")

    async def _pps_resync_routine(self) -> None:
        while True:
            try:
                python_timestamp_ms = maka_control_timestamp_ms()
                board_timestamp_ms = await self._get_timestamp_ms()
                if abs(python_timestamp_ms - board_timestamp_ms) > self._pps_offsync_limit_ms:
                    await self._set_epoch_timestamp()
                await asyncio.sleep(self._pps_resync_interval_ms / 1000)
            except asyncio.CancelledError:
                break
            except MakaException:
                pass
            except Exception:
                LOG.exception(f"Error in time sync of: {self.device_id}")

    async def _enable_pps_sync(self) -> None:
        if self._pps_enabled:
            if self._pps_resync_task is not None:
                self._pps_resync_task.cancel()
                await self._pps_resync_task
            self._pps_resync_task = asyncio.get_event_loop().create_task(self._pps_resync_routine())

    async def _disable_pps_sync(self) -> None:
        if self._pps_resync_task is not None:
            self._pps_resync_task.cancel()
            await self._pps_resync_task

    async def reset(self) -> None:
        if self._bootloadable_board is not None:
            await self._bootloadable_board.hard_reset()
        else:
            await super().reset()

    @staticmethod
    async def _update_psoc_firmware(
        board: BootloadableConnector, bootloader_ip: str, version: Optional[FirmwareVersion], firmware: Firmware
    ) -> None:
        await board.hard_reset()
        LOG.info(f"Auto Updating Firmware for {bootloader_ip}: {version} -> {firmware.version}")
        await asyncio.sleep(RESET_BOOTLOADER_WAIT_TIME_MS / 1000)  # Reset Time
        await asyncio.wait_for(program(bootloader_ip, BOOTLOADER_PORT, firmware.path), 30)
        await asyncio.sleep(PROGRAM_WAIT_TIME_MS / 1000)
        version = await board.get_version()
        if version is None or not firmware.version.is_equal(version):
            raise FatalMakaDeviceException(
                f"Firmware Update Failed for: {bootloader_ip}, {version} -> {firmware.version}"
            )

    async def wait_for_system_ready_for_update(self) -> None:
        pass

    async def update_firmware(
        self, board_ip: str, bootloader_ip: str, version: Optional[FirmwareVersion], firmware: Firmware,
    ) -> None:
        await self.wait_for_system_ready_for_update()
        if self._bootloader_type == "psoc" and self._bootloadable_board is not None:
            await self._update_psoc_firmware(self._bootloadable_board, bootloader_ip, version, firmware)
        elif self._bootloader_type == "mcuboot":
            await MCUMGRClient(ip=board_ip, port=self._boot_loader_port).upgrade(
                self._firmware_name
            )  # TODO Pass Validation Callback
        else:
            raise BootloadableDeviceException(f"Invalid Bootloader Type: {self._bootloader_type}")

    async def _get_latest_firmware(self, board_ip: str) -> Optional[Firmware]:
        if self._bootloader_type == "psoc":
            return await asyncio.get_event_loop().run_in_executor(
                None, lambda: get_latest_firmware_version(self._firmware_name)
            )
        elif self._bootloader_type == "mcuboot":
            revision = await MCUMGRClient(ip=board_ip, port=self._boot_loader_port).get_board_revision()
            return await asyncio.get_event_loop().run_in_executor(
                None, lambda: get_latest_firmware_version(f"{self._firmware_name}_{revision}")
            )
        else:
            raise BootloadableDeviceException(f"Invalid Bootloader Type: {self._bootloader_type}")

    async def _get_version(self, board_ip: str) -> FirmwareVersion:
        if self._bootloader_type == "psoc" and self._bootloadable_board is not None:
            return await self._bootloadable_board.get_version()
        elif self._bootloader_type == "mcuboot":
            return await MCUMGRClient(ip=board_ip, port=self._boot_loader_port).get_version()
        else:
            raise BootloadableDeviceException(f"Invalid Bootloader Type: {self._bootloader_type}")

    async def get_revision(self, board_ip: str) -> str:
        if is_simulator():
            return ""
        if self._bootloader_type == "mcuboot":
            return await MCUMGRClient(ip=board_ip, port=self._boot_loader_port).get_board_revision()
        return ""

    async def get_board(
        self, board_ip: str, port: int, constructor: Callable[[PsocMEthernetConnector], C],
    ) -> Tuple[PsocMEthernetConnector, C]:
        assert self._status.code == DeviceStatusCode.BOOTING
        event_loop = asyncio.get_event_loop()
        protocol_connector = PsocMEthernetConnector(board_ip, port, event_loop, self._bootloader_type)
        await protocol_connector.open()
        board = constructor(protocol_connector)
        return protocol_connector, board

    async def initialize_firmware(self, bootloader_ip: str, board_ip: str, board: C) -> None:
        if self._firmware_initialized:
            return
        if is_simulator():
            return
        assert self._status.code == DeviceStatusCode.BOOTING
        self._bootloadable_board = board
        if not self._version_check or not self._owner:
            self._firmware_initialized = True
            return
        firmware = await self._get_latest_firmware(board_ip)
        if firmware is None:
            raise FatalMakaDeviceException(f"No {self._firmware_name} Firmware Available, Ensure this is intentional")
        try:
            version: FirmwareVersion = await self._get_version(board_ip)
        except MakaException:
            LOG.warning(f"Failed to retrieve firmware version from board. Device ID={self.device_id}")
            if not self._force_update and not self._auto_updates:
                raise FatalMakaDeviceException(
                    f"No Firmware Version Detected and auto updates disabled, Latest in Repo: {firmware.version}"
                )
            await self.update_firmware(board_ip, bootloader_ip, None, firmware)
            self._firmware_initialized = True
            return

        if firmware.version.has_major_difference(version) or (
            not firmware.version.is_equal(version) and self._strict_version
        ):
            if self._auto_updates:
                await self.update_firmware(board_ip, bootloader_ip, version, firmware)
            else:
                raise FatalMakaDeviceException(
                    f"{self._firmware_name} Firmware Version Mismatch: {version}. Latest in Repo: {firmware.version}"
                )
        elif self._force_update:
            await self.update_firmware(board_ip, bootloader_ip, version, firmware)
        else:
            LOG.debug(f"Board Firmware Version Matches Latest: {firmware.version}")
        self._firmware_initialized = True

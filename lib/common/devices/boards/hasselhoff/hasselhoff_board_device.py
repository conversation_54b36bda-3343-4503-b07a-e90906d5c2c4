import asyncio
from types import TracebackType
from typing import TYPE_CHECKING, Any, Awaitable, Callable, List, Optional, Type, TypeVar

from typing_extensions import ParamSpec

import lib.common.logging
from generated.lib.drivers.nanopb.proto.carbon_tractor_pb2 import TractorStatus
from lib.common.asyncio.data_event import <PERSON><PERSON><PERSON>
from lib.common.devices.bootloadable_device import PsocEthernetBootloadableDevice
from lib.common.devices.device import <PERSON><PERSON><PERSON><PERSON>us<PERSON>ode
from lib.drivers.nanopb.hasselhoff_board.hasselhoff_board_connector import (
    HASSELHOFF_IP,
    HASSELHOFF_PORT,
    HasselhoffBoardConnector,
)

LOG = lib.common.logging.get_logger(__name__)

# Make typechecker see all the dynamically added attributes
if TYPE_CHECKING:
    DynamicBase = HasselhoffBoardConnector
else:
    DynamicBase = object


P = ParamSpec("P")
R = TypeVar("R")


class HasselhoffBoardDevice(PsocEthernetBootloadableDevice, DynamicBase):
    def __init__(self, firmware_name: str = "hh_board", dawg_petting_interval_ms: int = 50, **kwargs: Any):
        super().__init__(firmware_name=firmware_name, **kwargs)
        self._dawg_petting_interval_ms = dawg_petting_interval_ms
        self.__added_attrs: List[str] = []
        self.__enable_called = False
        self.__data_event: Optional[DataEvent[TractorStatus]] = None
        self._petting_task: Optional[asyncio.Task[None]] = None
        self._loop: Optional[asyncio.AbstractEventLoop] = None

    async def enable(self) -> None:
        LOG.info("HH board device BOOTING...")
        self.__data_event = DataEvent()
        self._set_status(DeviceStatusCode.BOOTING)
        try:
            self._connector, self._board = await self.get_board(
                HASSELHOFF_IP, HASSELHOFF_PORT, HasselhoffBoardConnector,
            )
            await self.initialize_firmware(HASSELHOFF_IP, HASSELHOFF_IP, self._board)
        except Exception as e:
            self._set_status(DeviceStatusCode.ERROR)
            raise e

        for name in dir(self._board):
            if name.startswith("_"):
                continue
            setattr(self, name, getattr(self._board, name))
            self.__added_attrs.append(name)
        self._set_status(DeviceStatusCode.OK)
        self.__enable_called = True
        self._loop = asyncio.get_event_loop()
        assert self._loop is not None
        self._petting_task = self._loop.create_task(self._petting_routine())

    async def disable(self) -> None:
        for attr in self.__added_attrs:
            delattr(self, attr)
        self.__added_attrs = []
        if not self.__enable_called:
            return
        await self._stop_petting_task()
        self._set_status(DeviceStatusCode.STOPPING)
        await self._board.stop()
        await self._connector.close()
        self._set_status(DeviceStatusCode.STOPPED)

    async def __aenter__(self) -> "HasselhoffBoardDevice":
        await self.enable()
        return self

    async def __aexit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        await self.disable()

    async def _reconnect_routine(self) -> None:
        self._set_status(DeviceStatusCode.NETWORK_ERROR)
        assert self._board is not None
        while True:
            try:
                await self._board.ping()
                break
            except Exception as e:
                LOG.error(f"Unknown error trying to reconnect to {self.device_id}: {e}")
            await asyncio.sleep(5)

    async def _petting_routine(self) -> None:
        miss_count = 0
        assert self.__data_event is not None
        de = self.__data_event
        while True:
            if miss_count >= 5:
                LOG.error(f"Cannot communicate with {self.device_id},  Controls in degraded state")
                await self._reconnect_routine()
                miss_count = 0
                LOG.info(f"Connection restored to {self.device_id}")
            try:
                status = await self._board.pet()
                de.put(status)
                miss_count = 0
                await asyncio.sleep(self._dawg_petting_interval_ms / 1000)
            except asyncio.CancelledError:
                break
            except (Exception, asyncio.TimeoutError):
                miss_count += 1
                LOG.exception(f"Error in Watchdog of: {self.device_id}")

    async def _stop_petting_task(self) -> None:
        if self._petting_task is not None:
            self._petting_task.cancel()
            await self._petting_task
            self._petting_task = None

    async def await_status(self) -> TractorStatus:
        assert self.__data_event is not None
        return await self.__data_event.get()

    def from_sync(self, async_callable: Callable[P, Awaitable[R]], *args: P.args, **kwargs: P.kwargs) -> R:
        assert self._loop is not None
        future = asyncio.run_coroutine_threadsafe(async_callable(*args, **kwargs), self._loop)
        return future.result()

import asyncio
from asyncio import AbstractE<PERSON>Loop
from typing import Any, Dict, List, Optional, Tuple

from tenacity import retry, stop_after_attempt

import lib.common.logging
from lib.common.devices.device import Device, DeviceStatusCode
from lib.common.types import GimbalPosition, GimbalVelocity
from lib.drivers.carbon_serial.carbon_serial import CarbonSerialConnector
from lib.drivers.nanopb.row_module_board.row_module_board_connector import (
    GoToModeType,
    RowModuleBoardConnector,
    RowModuleBoardException,
    servo_pb,
)
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

LOG = lib.common.logging.get_logger(__name__)

MIN_SAFETY_POSITION = 0
MAX_SAFETY_POSITION = 30000


class RowModuleBoardDevice(Device):
    def __init__(
        self,
        device_id: str,
        serial_port: str,
        max_velocity_mrpm: int,
        settle_timeout_ms: int,
        settle_window: int,
        home_offset: int,
        home_step: int,
        scanners: List[Dict[str, int]],
        dawg_timeout_ms: int,
        dawg_petting_interval_ms: int,
    ):
        super().__init__(device_id)
        self._serial_port = serial_port
        self._max_velocity_mrpm = max_velocity_mrpm
        self._settle_timeout_ms = settle_timeout_ms
        self._settle_window = settle_window
        self._home_offset = home_offset
        self._home_step = home_step
        self._scanners = scanners
        self._dawg_timeout_ms = dawg_timeout_ms
        self._dawg_petting_interval_ms = dawg_petting_interval_ms
        self._protocol_connector: Optional[MakaProtocolConnector] = None
        self._board: Optional[RowModuleBoardConnector] = None
        self._event_loop: Optional[AbstractEventLoop] = None
        self._petting_task: Optional[asyncio.Task[None]] = None

        # Sanity Check To avoid double definitions (which can occur on copy paste of config)
        scanner_ids = [scanner["scanner_id"] for scanner in scanners]
        node_ids = [scanner["pan_id"] for scanner in scanners] + [scanner["tilt_id"] for scanner in scanners]
        assert len(scanner_ids) == len(set(scanner_ids))
        assert len(node_ids) == len(set(node_ids))

    @property
    def max_velocity_mrpm(self) -> int:
        return self._max_velocity_mrpm

    @property
    def settle_timeout_ms(self) -> int:
        return self._settle_timeout_ms

    @property
    def settle_window(self) -> int:
        return self._settle_window

    @property
    def home_offset(self) -> int:
        return self._home_offset

    @property
    def home_step(self) -> int:
        return self._home_step

    async def _get_diagnostic(self) -> Dict[str, str]:
        return {}

    async def _configure_scanner(self, scanner_id: int, pan_id: int, tilt_id: int, intensity: float) -> None:
        assert self._status.code == DeviceStatusCode.BOOTING
        assert self._board is not None
        assert 0 <= intensity <= 1
        await self._board.scanner_config(
            scanner_id, pan_id, tilt_id, self._max_velocity_mrpm, self._settle_timeout_ms, self._settle_window
        )
        await self._board.scanner_intensity(scanner_id, int(intensity * ((2 ** 16) - 1)))

    @retry(stop=stop_after_attempt(3))
    async def _boot_scanner(self, scanner_id: int, **kwargs: Any) -> None:
        assert self._status.code == DeviceStatusCode.BOOTING
        assert self._board is not None
        await self._board.scanner_boot(
            scanner_id,
            self._max_velocity_mrpm,
            MIN_SAFETY_POSITION,
            MAX_SAFETY_POSITION,
            self._home_step,
            self._home_offset,
        )
        result = await self._board.scanner_get_limits(scanner_id)
        LOG.info(f"Scanner {scanner_id} Limits: {result}")

    async def _stop_scanner(self, scanner_id: int, **kwargs: Any) -> None:
        assert self._status.code == DeviceStatusCode.STOPPING
        assert self._board is not None
        await self._board.scanner_stop(scanner_id)

    async def _configure(self) -> None:
        assert self._status.code == DeviceStatusCode.BOOTING
        assert self._board is not None
        await self._board.clear_config()
        await self._board.dawg_config(self._dawg_timeout_ms)
        for scanner in self._scanners:
            await self._configure_scanner(**scanner)

    async def _boot(self) -> None:
        await asyncio.gather(*[self._boot_scanner(**scanner) for scanner in self._scanners])

    async def _stop(self) -> None:
        await asyncio.gather(*[self._stop_scanner(**scanner) for scanner in self._scanners])

    async def enable(self) -> None:
        self._set_status(DeviceStatusCode.BOOTING)
        self._event_loop = asyncio.get_event_loop()
        self._protocol_connector = CarbonSerialConnector(self._serial_port, self._event_loop)
        await self._protocol_connector.open()
        self._board = RowModuleBoardConnector(self._protocol_connector)
        await self._board.dawg_arm(False)
        await self._board.clear_config()
        await self._configure()
        await self._boot()
        self._petting_task = self._event_loop.create_task(self.petting_routine())
        self._set_status(DeviceStatusCode.OK)

    async def disable(self) -> None:
        self._set_status(DeviceStatusCode.STOPPING)
        if self._board is not None:
            await self._board.dawg_arm(False)
        if self._petting_task is not None:
            self._petting_task.cancel()
            await self._petting_task
        await self._stop()
        if self._board is not None:
            await self._board.stop()
        if self._protocol_connector is not None:
            await self._protocol_connector.close()
        self._set_status(DeviceStatusCode.STOPPED)

    async def petting_routine(self) -> None:
        error_state: bool = False
        while True:
            try:
                assert self._board is not None
                await self._board.dawg_pet()
                await asyncio.sleep(self._dawg_petting_interval_ms / 1000)
                error_state = False
            except asyncio.CancelledError:
                break
            except RowModuleBoardException:
                pass
            except Exception:
                if not error_state:
                    LOG.exception(f"Error in Watchdog of: {self.device_id}")
                error_state = True
                pass

    def _assert_operational(self) -> None:
        assert self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING

    async def arm(self, armed: bool) -> None:
        self._assert_operational()
        assert self._board is not None
        await self._board.dawg_arm(armed)

    @retry(stop=stop_after_attempt(3))
    async def get_pos_vel(self, scanner_id: int) -> Tuple[GimbalPosition, GimbalVelocity]:
        assert self._board is not None
        assert self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING
        result = await self._board.scanner_get_pos_vel(scanner_id)
        return GimbalPosition(*result[0]), GimbalVelocity(*result[1])

    async def _attempt_recovery(self, scanner_id: int) -> None:
        assert self._board is not None
        pan_code, tilt_code = await self._board.scanner_get_gimbal_error_codes(scanner_id)
        if pan_code != 0 or tilt_code != 0:
            LOG.error(f"Clearing Faults on Scanner: {scanner_id}, Pan Code: {pan_code}, Tilt Code: {tilt_code}")
            await self._board.scanner_clear_gimbal_fault(scanner_id)
        LOG.error("No Controller Failure Detected")

    @retry(stop=stop_after_attempt(3))
    async def go_to(
        self,
        scanner_id: int,
        position: GimbalPosition,
        velocity: Optional[GimbalVelocity] = None,
        await_settle: bool = False,
    ) -> None:
        self._assert_operational()
        assert self._board is not None
        if velocity is None:
            velocity = GimbalVelocity(self._max_velocity_mrpm, self._max_velocity_mrpm)
        try:
            await self._board.scanner_go_to(scanner_id, position, velocity, await_settle)
        except RowModuleBoardException:
            await self._attempt_recovery(scanner_id)
            raise

    async def go_to_delta(
        self,
        scanner_id: int,
        delta_position: GimbalPosition,
        velocity: Optional[GimbalVelocity] = None,
        mode: GoToModeType = servo_pb.IMMEDIATE,
    ) -> GimbalPosition:
        self._assert_operational()
        assert self._board is not None
        if velocity is None:
            velocity = GimbalVelocity(self._max_velocity_mrpm, self._max_velocity_mrpm)
        try:
            return GimbalPosition(*await self._board.scanner_go_to_delta(scanner_id, delta_position, velocity, mode))
        except RowModuleBoardException:
            await self._attempt_recovery(scanner_id)
            raise

    async def go_to_delta_follow(
        self,
        scanner_id: int,
        delta_position: GimbalPosition,
        velocity: Optional[GimbalVelocity] = None,
        follow_velocity_vector: GimbalPosition = GimbalPosition(0, 0),
        follow_velocity_mrpm: GimbalVelocity = GimbalVelocity(0, 0),
        interval_sleep_time_ms: int = 0,
        mode: GoToModeType = servo_pb.IMMEDIATE,
    ) -> GimbalPosition:
        self._assert_operational()
        assert self._board is not None
        if velocity is None:
            velocity = GimbalVelocity(self._max_velocity_mrpm, self._max_velocity_mrpm)
        return GimbalPosition(
            *await self._board.scanner_go_to_delta_follow(
                scanner_id,
                delta_position,
                velocity,
                follow_velocity_vector,
                follow_velocity_mrpm,
                interval_sleep_time_ms,
                mode,
            )
        )

    async def go_to_pan(
        self, scanner_id: int, position: int, velocity: Optional[int] = None, await_settle: bool = False,
    ) -> None:
        assert self._board is not None
        assert self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING
        if velocity is None:
            velocity = self._max_velocity_mrpm
        await self._board.scanner_go_to_pan(scanner_id, position, velocity, await_settle)

    async def go_to_tilt(
        self, scanner_id: int, position: int, velocity: Optional[int] = None, await_settle: bool = False,
    ) -> None:
        assert self._board is not None
        assert self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING
        if velocity is None:
            velocity = self._max_velocity_mrpm
        await self._board.scanner_go_to_tilt(scanner_id, position, velocity, await_settle)

    async def get_limits(self, scanner_id: int) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        assert self._board is not None
        assert self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING
        return await self._board.scanner_get_limits(scanner_id)

    async def laser(self, scanner_id: int, on: bool) -> None:
        self._assert_operational()
        assert self._board is not None
        await self._board.scanner_laser(scanner_id, on)

    # Sync API because the robot isn't async yet :)

    def arm_from_sync(self, armed: bool) -> None:
        assert self._event_loop is not None
        future = asyncio.run_coroutine_threadsafe(self.arm(armed), self._event_loop)
        return future.result()

    def go_to_from_sync(
        self,
        scanner_id: int,
        position: GimbalPosition,
        velocity: Optional[GimbalVelocity] = None,
        await_settle: bool = False,
    ) -> None:
        assert self._event_loop is not None
        future = asyncio.run_coroutine_threadsafe(
            self.go_to(scanner_id, position, velocity, await_settle), self._event_loop
        )
        return future.result()

    def go_to_pan_from_sync(
        self, scanner_id: int, position: int, velocity: Optional[int] = None, await_settle: bool = False,
    ) -> None:
        assert self._event_loop is not None
        future = asyncio.run_coroutine_threadsafe(
            self.go_to_pan(scanner_id, position, velocity, await_settle), self._event_loop
        )
        return future.result()

    def go_to_tilt_from_sync(
        self, scanner_id: int, position: int, velocity: Optional[int] = None, await_settle: bool = False,
    ) -> None:
        assert self._event_loop is not None
        future = asyncio.run_coroutine_threadsafe(
            self.go_to_tilt(scanner_id, position, velocity, await_settle), self._event_loop
        )
        return future.result()

    def get_pos_vel_from_sync(self, scanner_id: int) -> Tuple[GimbalPosition, GimbalVelocity]:
        assert self._event_loop is not None
        future = asyncio.run_coroutine_threadsafe(self.get_pos_vel(scanner_id), self._event_loop)
        return future.result()

    def get_limits_from_sync(self, scanner_id: int) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        assert self._event_loop is not None
        future = asyncio.run_coroutine_threadsafe(self.get_limits(scanner_id), self._event_loop)
        return future.result()

    def laser_from_sync(self, scanner_id: int, on: bool) -> None:
        assert self._event_loop is not None
        future = asyncio.run_coroutine_threadsafe(self.laser(scanner_id, on), self._event_loop)
        return future.result()

import asyncio
from argparse import Argument<PERSON>arser
from typing import Any, Dict

import lib.common.logging
from lib.common.devices.boards.row_module.row_module_board_device import GimbalPosition, RowModuleBoardDevice
from lib.common.devices.registry import DeviceRegistry
from lib.common.devices.tests.registry_test import boot_devices_from_json
from lib.common.tasks.manager import get_event_loop_by_name

LOG = lib.common.logging.get_logger(__name__)


def _construct_file(serial_port: str, n_scanners: int) -> Dict[str, Any]:
    return {
        "devices": {
            "row_module_1": {
                "module": "lib.common.devices.boards.row_module.row_module_board_device",
                "boot_func": "RowModuleBoardDevice",
                "kwargs": {
                    "serial_port": serial_port,
                    "max_velocity_mrpm": 590000,
                    "settle_timeout_ms": 1000,
                    "settle_window": 50,
                    "home_offset": 1000,
                    "home_step": 1000,
                    "dawg_timeout_ms": 1000,
                    "dawg_petting_interval_ms": 300,
                    "scanners": [
                        {"scanner_id": i, "pan_id": (2 * i) - 1, "tilt_id": 2 * i, "intensity": 1}
                        for i in range(1, n_scanners + 1)
                    ],
                },
            },
        }
    }


async def _test(test_file: Dict[str, Any], n_scanners: int) -> None:
    registry = DeviceRegistry()
    await boot_devices_from_json(registry, test_file, enable=True)
    row_module = await registry.get_device(RowModuleBoardDevice, "row_module_1")

    assert row_module is not None

    for i in range(1, n_scanners + 1):
        await row_module.arm(True)
        await asyncio.sleep(1)
        print(await row_module.get_limits(i))
        await row_module.go_to(i, GimbalPosition(1000, 1000), None, True)
        await asyncio.sleep(1)
        await row_module.laser(i, True)
        await row_module.get_pos_vel(i)
        await asyncio.sleep(1)
        await row_module.laser(i, False)
        await asyncio.sleep(1)
        await row_module.arm(False)

    await row_module.disable()


def main() -> None:
    parser = ArgumentParser("Row Module Board Tester")
    parser.add_argument("serial_port", type=str)
    parser.add_argument("-n", type=int, default=1)
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _test(_construct_file(args.serial_port, args.n), args.n)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

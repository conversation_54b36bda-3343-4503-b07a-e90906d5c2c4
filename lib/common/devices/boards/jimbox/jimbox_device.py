from typing import Any

import lib.common.logging
from lib.common.devices.bootloadable_device import PsocEthernetBootloadableDevice
from lib.common.devices.device import DeviceStatusCode
from lib.drivers.nanopb.jimbox_board.jimbox_board_connector import (
    JIMBOX_IP,
    JIMBOX_PORT,
    JimboxBoardConnector,
    TractorVariantType,
)

LOG = lib.common.logging.get_logger(__name__)


class JimboxBoardDevice(PsocEthernetBootloadableDevice):
    def __init__(self, firmware_name: str = "Jimbox", **kwargs: Any):
        LOG.info(f"BOOTING JIMBOX {firmware_name}")
        super().__init__(firmware_name=firmware_name, **kwargs)

    async def enable(self) -> None:
        self._set_status(DeviceStatusCode.BOOTING)
        try:
            self._connector, self._board = await self.get_board(JIMBOX_IP, JIMBOX_PORT, JimboxBoardConnector)
            await self.initialize_firmware("nonexistant", self._connector.get_identifier(), self._board)
        except Exception as e:
            self._set_status(DeviceStatusCode.ERROR)
            raise e
        self._set_status(DeviceStatusCode.OK)

    async def disable(self) -> None:
        await self._connector.close()

    async def ping(self) -> None:
        assert self._board is not None
        await self._board.ping()

    async def set_speed(self, speed: float) -> float:
        assert self._board is not None
        return await self._board.set_speed(speed)

    async def get_speed(self) -> float:
        assert self._board is not None
        return await self._board.get_speed()

    async def get_lever_pos(self) -> float:
        assert self._board is not None
        return await self._board.get_lever_pos()

    async def get_lever_s1(self) -> bool:
        assert self._board is not None
        return await self._board.get_lever_s1()

    def set_speed_mapping(self, speed_mapping: Any, scale: float) -> None:
        assert self._board is not None
        return self._board.set_speed_mapping(speed_mapping, scale)

    async def send_speed_wheel_ticks(self, ticks: int) -> bool:
        assert self._board is not None
        return await self._board.send_speed_wheel_ticks(ticks)

    async def set_enabled(self, enabled: bool) -> None:
        assert self._board is not None
        return await self._board.set_enabled(enabled)

    async def get_enabled(self) -> bool:
        assert self._board is not None
        return await self._board.get_enabled()

    async def get_allow_enable(self) -> bool:
        assert self._board is not None
        return await self._board.get_allow_enable()

    async def set_tractor_variant(self, variant: TractorVariantType) -> TractorVariantType:
        assert self._board is not None
        return await self._board.set_tractor_variant(variant)

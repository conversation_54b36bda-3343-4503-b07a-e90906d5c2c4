import asyncio
from typing import Any, Dict, Optional, Union, cast

from ping3 import ping as _ping

import lib.common.devices.boards.supervisory_plc.supervisory_messages as supervisory_messages
import lib.common.logging
from config.client.cpp.config_client_python import get_global_config_subscriber
from lib.common.devices.device import Device, DeviceStatusCode
from lib.common.generation import is_reaper, is_slayer
from lib.common.protocol.async_udp_client import UDPConnector
from lib.common.role import is_simulator
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.keyence.kv8000.firmware_updater import initialize_firmware

LOG = lib.common.logging.get_logger(__name__)

UDP_IP = "*********"
UDP_PORT = 8501
RESP_TYPE = Optional[Union[bool, float]]


class SupervisoryPLCDevice(Device):
    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self._config_subscriber = get_global_config_subscriber()
        loop = get_event_loop_by_name()
        self._connector = UDPConnector(UDP_IP, UDP_PORT, loop)

    async def enable(self) -> None:
        if not is_simulator() and not is_reaper():
            initialize_firmware(
                variant=self._config_subscriber.get_config_node(
                    "hardware_manager", "supervisory_plc_variant"
                ).get_string_value()
            )
        await self._connector.open()
        self._set_status(DeviceStatusCode.OK)

    async def disable(self) -> None:
        await self._connector.close()
        self._set_status(DeviceStatusCode.STOPPED)

    async def _get_diagnostic(self) -> Dict[str, Any]:
        return {}

    async def enable_row(self, row_ind: int) -> bool:
        if is_slayer():
            assert row_ind in [1, 2, 3]
            return await self.write_message(
                self._get_write_messages()[supervisory_messages.ENABLE_SERVER_MESSAGES[row_ind - 1]]
            )
        else:
            raise Exception("Row power control available only on Slayer")

    async def disable_row(self, row_ind: int) -> bool:
        if is_slayer():
            assert row_ind in [1, 2, 3]
            return await self.write_message(
                self._get_write_messages()[supervisory_messages.DISABLE_SERVER_MESSAGES[row_ind - 1]]
            )
        else:
            raise Exception("Row power control available only on Slayer")

    async def enable_scanners(self, row_ind: int) -> bool:
        if is_slayer():
            assert row_ind in [1, 2, 3]
            return await self.write_message(
                self._get_write_messages()[supervisory_messages.ENABLE_SCANNERS_MESSAGES[row_ind - 1]]
            )
        else:
            raise Exception("Scanner/target power (via PLC) available only on Slayer")

    async def disable_scanners(self, row_ind: int) -> bool:
        if is_slayer():
            assert row_ind in [1, 2, 3]
            return await self.write_message(
                self._get_write_messages()[supervisory_messages.DISABLE_SCANNERS_MESSAGES[row_ind - 1]]
            )
        else:
            raise Exception("Scanner/target power (via PLC) available only on Slayer")

    async def enable_btl(self, row_ind: int) -> bool:
        if is_slayer():
            assert row_ind in [1, 2, 3]
            return await self.write_message(
                self._get_write_messages()[supervisory_messages.ENABLE_BTL_MESSAGES[row_ind - 1]]
            )
        else:
            raise Exception("BTL control (via PLC) available only on Slayer")

    async def disable_btl(self, row_ind: int) -> bool:
        if is_slayer():
            assert row_ind in [1, 2, 3]
            return await self.write_message(
                self._get_write_messages()[supervisory_messages.DISABLE_BTL_MESSAGES[row_ind - 1]]
            )
        else:
            raise Exception("BTL control (via PLC) available only on Slayer")

    async def enable_wheel_encoder(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.WHEEL_ENCODER_ENABLE])

    async def disable_wheel_encoder(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.WHEEL_ENCODER_DISABLE])

    async def enable_strobe(self) -> bool:
        if is_slayer():
            return await self.write_message(self._get_write_messages()[supervisory_messages.STROBE_ENABLE])
        else:
            raise Exception("Strobe board available only on Slayer")

    async def disable_strobe(self) -> bool:
        if is_slayer():
            return await self.write_message(self._get_write_messages()[supervisory_messages.STROBE_DISABLE])
        else:
            raise Exception("Strobe board available only on Slayer")

    async def enable_gps(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.GPS_ENABLE])

    async def disable_gps(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.GPS_DISABLE])

    async def command_computer_power_cycle(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.COMMAND_COMPUTER_RESET])

    async def enable_main_contactor_disabled(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.ENABLE_MAIN_CONTACTOR_DISABLED])

    async def disable_main_contactor_disabled(self) -> bool:
        return await self.write_message(
            self._get_write_messages()[supervisory_messages.DISABLE_MAIN_CONTACTOR_DISABLED]
        )

    async def enable_air_conditioner(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.AIR_CONDITIONER_ENABLE])

    async def disable_air_conditioner(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.AIR_CONDITIONER_DISABLE])

    async def enable_chiller(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.CHILLER_ENABLE])

    async def disable_chiller(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.CHILLER_DISABLE])

    async def set_chiller_temp(self, temp: float) -> bool:
        # PLC expects the temperature in Celcius * 10
        return await self.write_message(
            self._get_write_messages()[supervisory_messages.CHILLER_SET_TEMP_FORMAT].format(int(temp * 10))
        )

    async def enable_temp_bypass(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.TEMP_BYPASS_ENABLE])

    async def enable_humidity_bypass(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.HUMIDITY_BYPASS_ENABLE])

    async def disable_temp_bypass(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.TEMP_BYPASS_DISABLE])

    async def disable_humidity_bypass(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.HUMIDITY_BYPASS_DISABLE])

    async def get_240v_uptime(self) -> int:

        total = 0
        total += await self._read_to_int(self._get_status_messages()[supervisory_messages.RUNTIME_240V_HOURS]) * 3600
        total += await self._read_to_int(self._get_status_messages()[supervisory_messages.RUNTIME_240V_MINUTES]) * 60
        total += await self._read_to_int(self._get_status_messages()[supervisory_messages.RUNTIME_240V_SECONDS])
        return total

    async def get_12v_lifetime_hours(self) -> int:
        return await self._read_to_int(self._get_status_messages()[supervisory_messages.LIFETIME_HOURS_12V])

    async def get_240v_lifetime_hours(self) -> int:
        return await self._read_to_int(self._get_status_messages()[supervisory_messages.LIFETIME_HOURS_240V])

    async def get_chiller_lifetime_hours(self) -> int:
        return await self._read_to_int(self._get_status_messages()[supervisory_messages.LIFETIME_HOURS_CHILLER])

    async def get_ac_lifetime_hours(self) -> int:
        return await self._read_to_int(self._get_status_messages()[supervisory_messages.LIFETIME_HOURS_AC])

    async def get_btl_lifetime_hours(self) -> int:
        return await self._read_to_int(self._get_status_messages()[supervisory_messages.LIFETIME_HOURS_BTL])

    async def get_main_contactor_lifetime_cycles(self) -> int:
        return await self._read_to_int(self._get_status_messages()[supervisory_messages.LIFETIME_CYCLES_MAIN_CONTACTOR])

    async def get_battery_lifetime_cycles(self) -> int:
        return await self._read_to_int(self._get_status_messages()[supervisory_messages.LIFETIME_CYCLES_BATTERY])

    async def enable_strobe_while_lifted(self) -> bool:
        return await self.write_message(self._get_write_messages()[supervisory_messages.ENABLE_STROBE_WHILE_LIFTED])

    async def _read_to_int(self, msg: str) -> int:
        return int(await self.read_message(msg))

    async def read_message(self, message: str) -> str:
        byte_message = message.encode("ascii")
        response = await self._connector.write_await_response(byte_message, 1000)
        if response is not None:
            return response.decode("ascii").rstrip()
        raise ValueError(f"Couldn't read message {message}")

    async def _get_values(self, msg_dict: Dict[str, str], out_dict: Dict[str, str]) -> None:
        for name, msg in msg_dict.items():
            out_dict[name] = await self.read_message(msg)

    async def write_message(self, message: str) -> bool:
        try:
            resp = await self.read_message(message)
            return resp.upper() == "OK"
        except Exception as e:
            LOG.error(f"Couldn't write message {message}: {e}")
            return False

    async def get_plc_output(self) -> Dict[str, str]:
        out: Dict[str, str] = {}
        await self._get_values(self._get_status_messages(), out)
        await self._get_values(supervisory_messages.MAIN_BREAKER_MEASUREMENTS_MESSAGES, out)
        await self._get_values(self._get_sensor_messages(), out)

        return out

    async def ping(self) -> bool:
        resp = await asyncio.get_event_loop().run_in_executor(None, lambda: cast(RESP_TYPE, _ping(UDP_IP, timeout=1)))
        return bool(resp)

    def _get_status_messages(self) -> Dict[str, str]:
        """
        Get the status messages for the current robot generation
        """
        if is_slayer():
            return supervisory_messages.STATUS_MESSAGES_SLAYER
        elif is_reaper():
            return supervisory_messages.STATUS_MESSAGES_REAPER
        else:
            raise Exception("Unsupported generation")

    def _get_sensor_messages(self) -> Dict[str, str]:
        """
        Get the PLC messages for sensor readout for the current robot generation
        """
        if is_slayer():
            return supervisory_messages.SENSOR_MESSAGES_SLAYER
        elif is_reaper():
            return supervisory_messages.SENSOR_MESSAGES_REAPER
        else:
            raise Exception("Unsupported generation")

    def _get_write_messages(self) -> Dict[str, str]:
        """
        Get the PLC messages for writing commands
        """
        if is_slayer():
            return supervisory_messages.WRITE_MESSAGES_SLAYER
        elif is_reaper():
            return supervisory_messages.WRITE_MESSAGES_REAPER
        else:
            raise Exception("Unsupported generation")

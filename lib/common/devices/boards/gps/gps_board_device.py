from typing import Any, Dict

import lib.common.logging
from core.drivers.gps.benjamin_gps.node import BENJAMIN_GPS_PORT as NON_INITIALIZE_FIRMWARE_GPS_PORT
from lib.common.devices.bootloadable_device import PsocEthernetBootloadableDevice
from lib.common.devices.device import Device<PERSON>tatusCode
from lib.common.units.angle import Angle
from lib.drivers.nanopb.benjamin_gps_board.benjamin_gps_board_connector import (
    BENJAMIN_GPS_BOOTLOADER_IP,
    BENJAMIN_GPS_IP,
    BENJAMIN_GPS_PORT,
    SPARTN,
    BenjaminGPSBoardConnector,
    Position,
    PtpMode,
)

LOG = lib.common.logging.get_logger(__name__)


class GPSBoardDevice(PsocEthernetBootloadableDevice):
    def __init__(self, firmware_name: str = "GPS", initialize_firmware: bool = False, **kwargs: Any):
        LOG.info(f"BOOTING GPS {firmware_name}")
        self._initialize_firmware = initialize_firmware
        self._supports_new_nanopb = False
        super().__init__(firmware_name=firmware_name, **kwargs)

    async def _get_diagnostic(self) -> Dict[str, str]:
        return {}

    async def enable(self) -> None:
        self._set_status(DeviceStatusCode.BOOTING)
        try:
            port = BENJAMIN_GPS_PORT if self._initialize_firmware else NON_INITIALIZE_FIRMWARE_GPS_PORT
            self._connector, self._board = await self.get_board(BENJAMIN_GPS_IP, port, BenjaminGPSBoardConnector,)
            if self._initialize_firmware:
                await self.initialize_firmware(
                    BENJAMIN_GPS_BOOTLOADER_IP, self._connector.get_identifier(), self._board
                )

            # get if this supports the "new" nanopb endpoints (e.g. HH dual gps)
            rev = await self.get_revision(BENJAMIN_GPS_IP)
            self._supports_new_nanopb = rev == "hh_dual"
            LOG.info(f"GPS board type: {rev} (new nanopb support = {self._supports_new_nanopb})")
        except Exception as e:
            self._set_status(DeviceStatusCode.ERROR)
            self._supports_new_nanopb = False
            raise e
        self._set_status(DeviceStatusCode.OK)

    async def disable(self) -> None:
        self._set_status(DeviceStatusCode.STOPPING)
        await self._board.stop()
        await self._connector.close()
        self._set_status(DeviceStatusCode.STOPPED)

    async def position(self) -> Position:
        return await self._board.position()

    async def spartn(self, msg: SPARTN) -> None:
        return await self._board.spartn(msg)

    async def rtcm(self, msg: bytes) -> None:
        return await self._board.rtcm(msg)

    async def get_latest_gga(self) -> bytes:
        return await self._board.get_latest_gga()

    async def ping(self) -> None:
        await self._board.ping()

    async def set_heading_correction(self, offset: Angle) -> None:
        return await self._board.set_heading_correction(offset)

    async def set_ptp_mode(self, mode: PtpMode) -> None:
        return await self._board.set_ptp_mode(mode)

    @property
    def supports_new_nanopb(self) -> bool:
        """
        Whether the firmware version on this board supports the new nanopb enddpoints

        Currently this is just the HH dual GPS as there are issues with firmware updates for
        existing Slayer GPS boards

        ref: https://carbonrobotics.atlassian.net/browse/SOFTWARE-1852
        """
        return self._supports_new_nanopb

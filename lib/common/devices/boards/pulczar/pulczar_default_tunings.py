from typing import Dict

from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import F<PERSON>haberServoControlConfig


def load_faulhaber_tuning(tuning: Dict[str, int]) -> FaulhaberServoControlConfig:
    conf = FaulhaberServoControlConfig()
    conf.fromJSON(tuning)
    return conf


BEST_REAPER_PAN_PROD = {  # Min Max Sampling 10 Mean Square Score: 85.45
    "gain_kv": 219,
    "velocity_feedforward_factor": 96,
    "velocity_feedforward_delay": 12,
    "gain_factor_kpn": 199,
    "gain_factor_kv": 156,
    "gain_kp": 24966,
    "integral_time_tn": 203,
    "integral_part_option": 0,
    "current_feedforward_factor": 31,
    "current_feedforward_delay": 0,
    "actual_velocity_filter_tf": 4,
    "display_velocity_filter": 22,
    "setpoint_velocity_filter_tf": 1,
    "setpoint_filter_enable": 1,
    "torque_gain_kpi": 2915,
    "torque_integral_time_tni": 150,
    "flux_gain_kpi": 1178,
    "flux_integral_time_tni": 644,
    "motion_profile_type": 1,
    "max_motor_speed": 200000,
    "profile_velocity": 200000,
    "profile_acceleration": 1831,
    "profile_deceleration": 650,
    "quick_stop_deceleration": 800,
    "move_velocity_factor": 15107,
}

BEST_REAPER_TILT_PROD = {  # Min Max Sampling 10 Mean Square Score: 61.10
    "gain_kv": 225,
    "velocity_feedforward_factor": 102,
    "velocity_feedforward_delay": 10,
    "gain_factor_kpn": 174,
    "gain_factor_kv": 135,
    "gain_kp": 23945,
    "integral_time_tn": 53,
    "integral_part_option": 0,
    "current_feedforward_factor": 26,
    "current_feedforward_delay": 2,
    "actual_velocity_filter_tf": 3,
    "display_velocity_filter": 62,
    "setpoint_velocity_filter_tf": 2,
    "setpoint_filter_enable": 1,
    "torque_gain_kpi": 5214,
    "torque_integral_time_tni": 151,
    "flux_gain_kpi": 9042,
    "flux_integral_time_tni": 222,
    "motion_profile_type": 1,
    "max_motor_speed": 623089,
    "profile_velocity": 16811,
    "profile_acceleration": 2355,
    "profile_deceleration": 1518,
    "quick_stop_deceleration": 1225,
    "move_velocity_factor": 15725,
}

import asyncio
import logging
from asyncio import AbstractEventLoop
from collections import deque
from typing import TYPE_CHECKING, Any, Callable, Deque, Dict, List, Optional, Tuple

import serial_asyncio
from tenacity import retry, stop_after_attempt

import lib.common.logging
from config.client.cpp.config_client_python import ConfigTree, get_global_config_subscriber
from lib.common.asyncio.event_loop import use_specific_loop
from lib.common.db.carbon_robot_db import CarbonRobotDBLite
from lib.common.db.records.laser_records import LaserRecord
from lib.common.devices.boards.pulczar.pulczar_default_tunings import (
    BEST_REAPER_PAN_PROD,
    BEST_REAPER_TILT_PROD,
    load_faulhaber_tuning,
)
from lib.common.devices.bootloadable_device import PsocEthernetBootloadableDevice
from lib.common.devices.device import DeviceStatusCode
from lib.common.generation import is_bud, is_reaper
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time import maka_control_timestamp_ms
from lib.common.types import GimbalPosition, GimbalVelocity
from lib.common.units.temperature import Temperature
from lib.drivers.errors.error import RetryableMakaDeviceException
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import (  # PULCZAR_PORT,
    PULCZAR_PORT,
    ArcDetectorConfig,
    ArcDetectorStatus,
    PositionAt,
    PulczarBoardConnector,
    PulczarBoardException,
    PulczarBoardFireStatus,
    PulczarGimbalException,
    PulczarServoPIDConfig,
    PulczarStatus,
    make_pulczar_board_ip,
    make_pulczar_bootloader_ip,
    servo_pb,
)
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)
ARM_LOG = LOG.getChild("arm_enforcer")

if TYPE_CHECKING:
    GoToModeType = servo_pb.GoToModeValue
else:
    GoToModeType = servo_pb.GoToMode


def id_location_key(record: logging.LogRecord) -> str:
    return f"{lib.common.logging.record_location_key_func(record)}:{record.getMessage().split(':')[0]}"


ARM_LOG.addFilter(lib.common.logging.RateFilter(id_location_key))

MIN_SAFETY_POSITION = 0
MAX_SAFETY_POSITION = 15000
DEFAULT_MAX_VEL_MRPM = 590000
DEFAULT_SMALL_MOVE_VEL_MRPM = 7500


def default_max_intensity() -> float:
    if is_bud():
        return float((2 ** 16) - 1)
    return 1000.0


def _get_invert(scanner_id: int) -> Tuple[bool, bool]:
    if is_reaper():
        return (True, True)
    return (False, False)


class PulczarBoardDevice(PsocEthernetBootloadableDevice):
    def __init__(
        self,
        scanner_id: int,
        intensity: Optional[ConfigTree] = None,
        default_intensity: float = 1.0,
        laser_id: int = 0,
        max_velocity_mrpm: int = 590000,
        small_move_velocity_mrpm: int = 7500,
        settle_timeout_ms: int = 250,
        settle_window: int = 50,
        home_offset: Tuple[int, int] = (1000, 1000),
        home_step: int = 1000,
        max_diff_millis: int = 300,
        dawg_timeout_ms: int = 3000,
        dawg_petting_interval_ms: int = 1000,
        pan_id: int = 1,
        tilt_id: int = 2,
        fail_on_bad_status: bool = True,
        door_override: bool = False,
        flow_override: bool = False,
        water_override: bool = False,
        stf_alt_override: bool = False,
        laser_disabled: bool = False,
        firmware_name: str = "Pulczar",
        pan_limit_range: Tuple[float, float] = (12000, 13000),
        tilt_limit_range: Tuple[float, float] = (12000, 13000),
        max_raw_laser_intensity: float = default_max_intensity(),
        boot_gimbal_mrpm: int = 590000,
        use_bwt_over_serial: bool = False,
        bwt_serial_port: str = "/dev/ttyUSB0",
        board_address: Optional[str] = None,
        port: Optional[int] = None,
        boot_loader_port: Optional[int] = None,
        **kwargs: Any,
    ) -> None:
        super().__init__(firmware_name=firmware_name, boot_loader_port=boot_loader_port, **kwargs)
        self._scanner_id = scanner_id
        self._laser_id = scanner_id if laser_id == 0 else laser_id
        self._pan_id = pan_id
        self._tilt_id = tilt_id
        self._intensity_conf = intensity
        if self._intensity_conf is not None:
            self._intensity_conf.register_callback(self._update_intensity)
        self._default_intensity = default_intensity
        self._max_velocity_mrpm = max_velocity_mrpm
        self._boot_gimbal_mrpm = boot_gimbal_mrpm
        self._small_move_velocity_mrpm = (small_move_velocity_mrpm, small_move_velocity_mrpm)
        self._settle_timeout_ms = settle_timeout_ms
        self._settle_window = settle_window
        self._home_offset = home_offset
        self._home_step = home_step
        self._max_diff_millis = max_diff_millis
        self._dawg_timeout_ms = dawg_timeout_ms
        self._dawg_petting_interval_ms = dawg_petting_interval_ms
        self._connector: Optional[PsocMEthernetConnector] = None
        self._board: Optional[PulczarBoardConnector] = None
        self._event_loop = get_event_loop_by_name()
        self._petting_task: Optional[asyncio.Task[None]] = None
        self._pulczar_status: Optional[PulczarStatus] = None
        self._fail_on_bad_status = fail_on_bad_status
        self._door_override = door_override
        self._flow_override = flow_override
        self._water_override = water_override
        self._stf_alt_override = stf_alt_override
        self._laser_disabled = laser_disabled
        self._pan_limit_range = (
            pan_limit_range[0] - (2 * self._home_offset[0]),
            pan_limit_range[1] - (2 * self._home_offset[0]),
        )
        self._tilt_limit_range = (
            tilt_limit_range[0] - (2 * self._home_offset[1]),
            tilt_limit_range[1] - (2 * self._home_offset[1]),
        )
        self._max_raw_laser_intensity = max_raw_laser_intensity
        self._laser_requested_on_timestamp_ms: Optional[int] = None
        self._db = CarbonRobotDBLite.get_instance()
        self._laser_record: Optional[LaserRecord] = None
        self._firing = False

        self._max_lens_value = 255
        self._min_lens_value = 0
        self._cached_lens_value: Optional[int] = None
        self._reboot_callback: List[Callable[[], None]] = []
        self._armed: bool = False
        self._desired_armed: bool = self._armed
        self._currents: Deque[float] = deque(maxlen=100)
        self._expected_status = DeviceStatusCode.NEW
        self._enabled: Callable[[], bool] = lambda: True
        with use_specific_loop(self._event_loop):
            self._current_lock = asyncio.Lock()
            self._armed_change = asyncio.Event()
        self._arm_enforcer_task = self._event_loop.create_task(self._arm_enforcer())
        self._settle_time = 0
        self._settle_time_node: Optional[ConfigTree] = None
        self._requested_reset = False

        self._use_bwt_over_serial = use_bwt_over_serial
        if self._use_bwt_over_serial:
            self._bwt_serial_port = bwt_serial_port
            self._bwt_reader: Optional[asyncio.StreamReader] = None
            self._bwt_writer: Optional[asyncio.StreamWriter] = None

        self._board_address = board_address
        self._port = port

    async def _bwt_command(self, cmd: str) -> None:
        assert self._bwt_writer is not None and self._bwt_reader is not None
        self._bwt_writer.write(f"{cmd}\r\n".encode("ascii"))
        await asyncio.wait_for(self._bwt_writer.drain(), timeout=1)
        await asyncio.wait_for(self._bwt_reader.readline(), timeout=1)

    def set_enabled_func(self, cb: Callable[[], bool]) -> None:
        self._enabled = cb

    async def request_reset(self) -> None:
        self._requested_reset = True

    @property
    def is_enabled(self) -> bool:
        return self._enabled()

    @property
    def armed(self) -> bool:
        return self._armed

    @property
    def scanner_id(self) -> int:
        return self._scanner_id

    @property
    def pan_id(self) -> int:
        return self._pan_id

    @property
    def tilt_id(self) -> int:
        return self._tilt_id

    @property
    def max_velocity_mrpm(self) -> int:
        return self._max_velocity_mrpm

    @property
    def small_move_velocity_mrpm(self) -> Tuple[int, int]:
        return self._small_move_velocity_mrpm

    @property
    def settle_timeout_ms(self) -> int:
        return self._settle_timeout_ms

    @property
    def settle_window(self) -> int:
        return self._settle_window

    @property
    def home_offset(self) -> Tuple[int, int]:
        return self._home_offset

    @property
    def home_step(self) -> int:
        return self._home_step

    @property
    def pan_limit_range(self) -> Tuple[float, float]:
        return self._pan_limit_range

    @property
    def tilt_limit_range(self) -> Tuple[float, float]:
        return self._tilt_limit_range

    @property
    def min_lens_value(self) -> float:
        return self._min_lens_value

    @property
    def max_lens_value(self) -> float:
        return self._max_lens_value

    @property
    def firing(self) -> bool:
        return self._firing

    def _intensity(self) -> float:
        if self._intensity_conf is not None:
            return self._intensity_conf.get_float_value()
        return self._default_intensity

    def intensity_percent_to_val(self, val: float) -> int:
        return round(val * self._max_raw_laser_intensity)

    @property
    def intensity(self) -> int:
        return self.intensity_percent_to_val(self._intensity())

    def add_callback(self, callback: Callable[[], None]) -> None:
        self._reboot_callback.append(callback)

    async def _get_diagnostic(self) -> Dict[str, Any]:
        assert self._board is not None
        status = await self._board.get_status()
        return status.to_json()

    async def _configure_gimbal(self) -> None:
        assert self._status.code == DeviceStatusCode.BOOTING
        assert self._board is not None
        assert 0 <= self._intensity() <= 1
        await self._board.gimbal_config(
            self._pan_id,
            self._tilt_id,
            self._max_velocity_mrpm,
            self._settle_timeout_ms,
            self._settle_window,
            max_diff_millis=self._max_diff_millis,
        )
        await self._board.gimbal_configure_pids(*PulczarServoPIDConfig.default_pids())

    async def _boot_lens(self) -> None:
        focus_value = 128
        if (
            get_global_config_subscriber().get_config_node("aimbot", "scanners").get_node(f"scanner{self._scanner_id}")
            is not None
        ):
            focus_value = (
                get_global_config_subscriber()
                .get_config_node("aimbot", f"scanners/scanner{self._scanner_id}/focus")
                .get_int_value()
            )
        await self.lens_set(focus_value)

    @retry(stop=stop_after_attempt(3))
    async def _boot_gimbal(self) -> None:
        assert self._status.code == DeviceStatusCode.BOOTING
        assert self._board is not None

        # TODO Figure out how to set tunings before boot, maybe read them first and compare, if not expected write and save
        await self._board.gimbal_boot(
            self._boot_gimbal_mrpm,
            MIN_SAFETY_POSITION,
            MAX_SAFETY_POSITION,
            self._home_step,
            self._home_offset,
            _get_invert(self.scanner_id),
        )

        if False and is_reaper():
            reaper_pan = load_faulhaber_tuning(BEST_REAPER_PAN_PROD)
            reaper_tilt = load_faulhaber_tuning(BEST_REAPER_TILT_PROD)
            self._small_move_velocity_mrpm = (
                reaper_pan.move_velocity_factor.value,
                reaper_tilt.move_velocity_factor.value,
            )
            await self._board.gimbal_configure_faulhaber_tuning_config(reaper_pan, reaper_tilt)

        await self._board.gimbal_configure_pids(*PulczarServoPIDConfig.default_pids())
        limits = await self._board.gimbal_get_limits()
        if not (self.pan_limit_range[0] < limits[0][1] < self.pan_limit_range[1]):
            msg = f"{self.device_id} Pan Limit {limits[0][1]} is not in ({self.pan_limit_range[0]}, {self.pan_limit_range[1]}). Something may have gone wrong."
            LOG.error(msg)
            raise RetryableMakaDeviceException(msg)
        if not (self.tilt_limit_range[0] < limits[1][1] < self.tilt_limit_range[1]):
            msg = f"{self.device_id} Tilt Limit {limits[1][1]} is not in ({self.tilt_limit_range[0]}, {self.tilt_limit_range[1]}). Something may have gone wrong."
            LOG.error(msg)
            raise RetryableMakaDeviceException(msg)
        LOG.info(f"Scanner {self.scanner_id} Limits: {limits}")

    async def _stop_gimbal(self) -> None:
        assert self._status.code == DeviceStatusCode.STOPPING
        assert self._board is not None
        await self._board.gimbal_stop()

    def _update_intensity(self) -> None:
        if self._board is not None:
            asyncio.run_coroutine_threadsafe(self._board.laser_intensity(self.intensity), self._event_loop)

    async def _configure(self) -> None:
        assert self._status.code == DeviceStatusCode.BOOTING
        assert self._board is not None
        await self._board.clear_config()
        await self._board.set_override(
            door=self._door_override,
            water=self._water_override,
            flow=self._flow_override,
            stf_alt=self._stf_alt_override,
        )
        await self._board.laser_intensity(int(self._intensity() * self._max_raw_laser_intensity))
        await self._board.dawg_config(self._dawg_timeout_ms)
        await self._configure_gimbal()
        await self._configure_arc_detector()
        await self._configure_fh_ambient_temp()

    async def _configure_fh_ambient_temp(self) -> None:
        """
        Update the settings for the ambient temperature reporting
        """
        assert self._board is not None
        assert self._connector is not None

        # only supported for FH drives
        board_rev = await self.get_revision(self._connector.get_identifier())
        if board_rev not in ["scanner_gd", "scanner_h753_reaper", "scanner_h753_slayer"]:
            return

        # upload config
        node = get_global_config_subscriber().get_config_node(
            "aimbot", f"scanners/scanner{self._scanner_id}/fh_ambient_temp"
        )

        enabled = node.get_node("enabled").get_bool_value()
        override_enabled = node.get_node("use_fixed_temp").get_bool_value()
        override_temp_raw = node.get_node("fixed_temp").get_float_value()
        override_temp = Temperature.from_c(override_temp_raw) if override_enabled else None

        LOG.info(f"Scanner {self._scanner_id} ambient temp reporting enabled={enabled}, temp={override_temp}")
        await self._board.set_drive_ambient_temp_reporting(enabled, override_temp if enabled else None)

    async def _configure_arc_detector(self) -> None:
        assert self._board is not None

        node = get_global_config_subscriber().get_config_node(
            "aimbot", f"scanners/scanner{self._scanner_id}/arc_detector"
        )

        config = ArcDetectorConfig()
        config.enabled = node.get_node("enabled").get_bool_value()

        # firmware expects period in seconds
        config.alarmPeriod = node.get_node("count_period").get_uint_value()
        config.alarmThreshold = node.get_node("count_threshold").get_uint_value()
        config.initialDelay = node.get_node("sample_delay").get_uint_value()
        config.sampleInterval = node.get_node("sample_rate").get_uint_value()
        config.lowerLimit = node.get_node("current_min").get_float_value()
        config.upperLimit = node.get_node("current_max").get_float_value()

        await self._board.set_arc_detector_config(config)

    async def _get_timestamp_ms(self) -> int:
        assert self._board is not None
        return await self._board.get_timestamp()

    async def _set_epoch_timestamp(self) -> None:
        assert self._board is not None
        return await self._board.set_epoch_time()

    async def _boot_dev(self) -> None:
        self._set_status(DeviceStatusCode.BOOTING)
        assert self._board is not None
        assert self._connector is not None
        ip: str
        if self._board_address is not None and self._boot_loader_port is not None:
            ip = self._board_address
        else:
            ip = make_pulczar_bootloader_ip(self._scanner_id)
        await self.initialize_firmware(
            ip, self._connector.get_identifier(), self._board,
        )
        await self._board.dawg_arm(False)
        await self._board.laser_set(False)
        self._firing = False
        await self._configure()
        await self._enable_pps_sync()
        await self._boot_gimbal()
        await self._boot_lens()
        self._set_status(DeviceStatusCode.OK)
        self._expected_status = DeviceStatusCode.OK
        self._armed = False  # update so arm enforcer can know state
        if self._settle_time == 0:
            self._settle_time = 30  # Default value if no config val is set
            try:
                board_rev = await self.get_revision(self._connector.get_identifier())
                hw_rev = await self._board.get_hw_rev_config()
                scanner_rev = f"{board_rev}_{hw_rev.name}"
                tree = get_global_config_subscriber().get_config_node("common", "shooting/scanner_settle_time")
                for node in tree.get_children_nodes():
                    if node.get_name() == scanner_rev:
                        self._settle_time_node = node
                        break
            except Exception:
                LOG.warning("Failed to find specific settle time node, using defaults.")

    @property
    def settle_time(self) -> int:
        if self._settle_time_node is None:
            return self._settle_time
        return self._settle_time_node.get_uint_value()

    async def enable(self) -> None:
        await self._stop_petting_task()
        self._set_status(DeviceStatusCode.BOOTING)
        self._expected_status = DeviceStatusCode.BOOTING
        if self._laser_record is None:
            self._laser_record = await self._db.laser_record_owner.get(self._laser_id)
        LOG.info(f"Enabling pulczar: {self.device_id}")
        ip: str
        port: int
        if self._board_address is not None and self._port is not None:
            ip = self._board_address
            port = self._port
        else:
            ip = make_pulczar_board_ip(self._scanner_id)
            port = PULCZAR_PORT
        self._connector, self._board = await self.get_board(ip, port, PulczarBoardConnector,)
        error_state = False
        try:
            await self._boot_dev()
        except Exception:
            self._expected_status = DeviceStatusCode.OK
            LOG.exception(f"Error enabling: {self.device_id}")
            error_state = True
        self._petting_task = self._event_loop.create_task(self._petting_routine(error_state))

    async def laser_intensity(self, intensity: int) -> None:
        assert self._board is not None
        return await self._board.laser_intensity(intensity)

    async def disable(self) -> None:
        self._set_status(DeviceStatusCode.STOPPING)
        self._expected_status = DeviceStatusCode.STOPPING
        if self._board is not None:
            await self._board.dawg_arm(False)
            await self._board.laser_set(False)
        self._firing = False
        await self._stop_petting_task()
        await self._stop_gimbal()
        await self._disable_pps_sync()
        if self._board is not None:
            await self._board.stop()
        if self._connector is not None:
            await self._connector.close()
        self._set_status(DeviceStatusCode.STOPPED)
        self._expected_status = DeviceStatusCode.STOPPED

    async def _stop_petting_task(self) -> None:
        if self._petting_task is not None:
            self._petting_task.cancel()
            await self._petting_task
            self._petting_task = None

    async def _reconnect_routine(self) -> None:
        self._set_status(DeviceStatusCode.NETWORK_ERROR)
        assert self._board is not None
        while True:
            try:
                if self._enabled():
                    await self._board.ping()
                    # Successfully re-connected, now need to boot
                    await self._boot_dev()
                    break
            except PulczarGimbalException:
                LOG.info(f"{self.device_id}: Failed to boot gimbal")
            except (PulczarBoardException, RetryableMakaDeviceException):
                LOG.info(f"Unable to reconnect and boot {self.device_id}")
            except Exception as e:
                LOG.error(f"Unknown error trying to reconnect to {self.device_id}: {e}")
            await asyncio.sleep(5)
        for callback in self._reboot_callback:
            await asyncio.get_event_loop().run_in_executor(None, callback)

    async def _petting_routine(self, error_state: bool) -> None:
        if error_state:
            # starting in error state
            await self._reconnect_routine()
        miss_count = 0
        last_reset_request = 0
        while True:
            requested_reset = self._requested_reset
            if self._requested_reset and maka_control_timestamp_ms() - last_reset_request < 30000:
                LOG.warning("Requested reset too quickly after another reset, ignoring request")
                self._requested_reset = False
                requested_reset = False
            if miss_count >= 5 or requested_reset:
                if self._enabled():
                    LOG.error(f"Cannot communicate with {self.device_id}, weeding in degraded state or reset requested")
                else:
                    LOG.info(f"Cannot communicate with disabled device {self.device_id} or reset requested")
                await self._reconnect_routine()
                miss_count = 0
                self._requested_reset = False
                last_reset_request = maka_control_timestamp_ms()
                LOG.info(f"Connection restored to {self.device_id}")
            try:
                assert self._board is not None
                await self._board.dawg_pet(self._firing)
                self._pulczar_status = await self._board.get_status()
                async with self._current_lock:
                    self._currents.append(self._pulczar_status.lpsu_current)
                self._armed = self._pulczar_status.arm
                await asyncio.sleep(self._dawg_petting_interval_ms / 1000)
                error_state = False
                miss_count = 0
            except asyncio.CancelledError:
                break
            except (PulczarBoardException, RetryableMakaDeviceException):
                miss_count += 1
                pass
            except Exception:
                if not error_state:
                    LOG.exception(f"Error in Watchdog of: {self.device_id}")
                error_state = True
                pass

    async def clear_current(self) -> None:
        async with self._current_lock:
            self._currents.clear()

    async def get_current(self) -> List[float]:
        async with self._current_lock:
            return list(self._currents)

    def _assert_operational(self) -> None:
        assert (
            self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING
        ), f"Status code {self._status.code} for scanner {self.scanner_id}"

    async def _arm_enforcer(self) -> None:
        await self._armed_change.wait()
        self._armed_change.clear()
        assert self._board is not None
        try:
            while True:
                try:
                    if self._armed == self._desired_armed:
                        try:
                            await asyncio.wait_for(self._armed_change.wait(), timeout=1)
                        except asyncio.TimeoutError:
                            pass
                        self._armed_change.clear()
                        continue
                    ARM_LOG.info(
                        f"pulczar {self._scanner_id}: arm enforcer missmatch, expected = {self._desired_armed}, current = {self._armed}"
                    )
                    self._assert_operational()
                    armed = self._desired_armed
                    await self._board.dawg_arm(armed)
                    self._armed = armed
                    if not self._armed and self._firing:
                        self._firing = False
                except asyncio.CancelledError:
                    return
                except Exception as e:
                    ARM_LOG.info(f"pulczar {self._scanner_id}: arm enforcer failed with exception = {e}")
                    await asyncio.sleep(1)
        except asyncio.CancelledError:
            pass
        except Exception as e:
            ARM_LOG.info(f"pulczar {self._scanner_id}: arm enforcer unknown exception = {e}")

    async def arm(self, armed: bool) -> None:
        if self._use_bwt_over_serial:
            if self._bwt_reader is None:
                self._bwt_reader, self._bwt_writer = await asyncio.wait_for(
                    serial_asyncio.open_serial_connection(url=self._bwt_serial_port, baudrate=115200), timeout=1
                )

            if armed:
                # laser one-time setup
                # TODO - only change these if current settings are incorrect;
                #        presumably they get saved to EEPROM, and having too
                #        many overwrites might be detrimental
                await self._bwt_command("OLOGIN")
                # set water inlet temps to 15C-35C
                await self._bwt_command("WTLH 3 150 350")
                # set chip temps to 15C-40C
                await self._bwt_command("WTLH 4 150 400")

                # laser operational setup
                # external enable signal
                await self._bwt_command("WENMODE 0")
                # internal modulation, 5khz, 100% duty cycle
                await self._bwt_command("WMMODE 1")
                await self._bwt_command("WCMF 5000")
                await self._bwt_command("WCDC 100")
                # internal power, 100%
                await self._bwt_command("WPIMODE 1")
                await self._bwt_command("WCPR 100.0")
                # disable red light
                await self._bwt_command("WRALR 0")
            else:
                # switch power to 0%
                await self._bwt_command("WCPR 0.0")

        if armed != self._desired_armed:
            self._desired_armed = armed
            self._armed_change.set()

    @retry(stop=stop_after_attempt(3))
    async def get_pos_vel(self) -> Tuple[GimbalPosition, GimbalVelocity]:
        assert self._board is not None
        assert self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING
        result = await self._board.gimbal_get_pos_vel()
        return GimbalPosition(*result[0]), GimbalVelocity(*result[1])

    @retry(stop=stop_after_attempt(3))
    async def get_pos(self) -> Tuple[int, int]:
        assert self._board is not None
        assert self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING
        result = await self._board.gimbal_get_pos_at_time(maka_control_timestamp_ms() * 1000)
        return result[1].pan, result[1].tilt

    async def _attempt_recovery(self) -> None:
        assert self._board is not None
        pan_code, tilt_code = await self._board.get_gimbal_error_codes()
        if pan_code != 0 or tilt_code != 0:
            LOG.error(f"Clearing Faults on Scanner: {self.scanner_id}, Pan Code: {pan_code}, Tilt Code: {tilt_code}")
            await self._board.clear_gimbal_fault()
        LOG.error("No Controller Failure Detected")

    @retry(stop=stop_after_attempt(3))
    async def go_to(
        self, position: GimbalPosition, velocity: Optional[GimbalVelocity] = None, await_settle: bool = False,
    ) -> None:
        self._assert_operational()
        assert self._board is not None
        if velocity is None:
            velocity = GimbalVelocity(self._max_velocity_mrpm, self._max_velocity_mrpm)
        try:
            await self._board.gimbal_go_to(position, velocity, await_settle)
        except PulczarBoardException:
            await self._attempt_recovery()
            raise

    async def go_to_timestamp(
        self,
        timestamp_ms: int,
        mode: GoToModeType,
        position: Tuple[int, int],
        velocity_mrpm: Tuple[int, int],
        follow_velocity: Tuple[int, int],
        follow_accel: Tuple[int, int],
        interval_sleep_time_ms: int,
        timeout_ms: int = 10000,
    ) -> Tuple[Tuple[Tuple[int, int], Tuple[int, int]], Tuple[Tuple[int, int], Tuple[int, int]]]:
        assert self._board is not None
        return await self._board.gimbal_go_to_timestamp(
            timestamp_ms,
            mode,
            position,
            velocity_mrpm,
            follow_velocity,
            follow_accel,
            interval_sleep_time_ms,
            timeout_ms=timeout_ms,
        )

    async def go_to_follow(
        self,
        timestamp_ms: int,
        position: Tuple[int, int],
        velocity_mrpm: Tuple[int, int],
        follow_velocity: Tuple[int, int],
        follow_accel: Tuple[int, int],
        interval_sleep_time_ms: int,
    ) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        assert self._board is not None
        return await self._board.gimbal_go_to_follow(
            timestamp_ms, position, velocity_mrpm, follow_velocity, follow_accel, interval_sleep_time_ms
        )

    async def follow_timestamp(
        self, timestamp_ms: int, follow_velocity: Tuple[int, int], follow_accel: Tuple[int, int],
    ) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        assert self._board is not None
        return await self._board.gimbal_follow_timestamp(timestamp_ms, follow_velocity, follow_accel)

    async def go_to_delta(
        self,
        delta_position: GimbalPosition,
        velocity: Optional[GimbalVelocity] = None,
        mode: GoToModeType = servo_pb.IMMEDIATE,
    ) -> GimbalPosition:
        self._assert_operational()
        assert self._board is not None
        if velocity is None:
            velocity = GimbalVelocity(self._max_velocity_mrpm, self._max_velocity_mrpm)
        try:
            return GimbalPosition(*await self._board.gimbal_go_to_delta(delta_position, velocity, mode))
        except PulczarBoardException:
            await self._attempt_recovery()
            raise

    async def go_to_delta_follow(
        self,
        delta_position: GimbalPosition,
        velocity: Optional[GimbalVelocity] = None,
        follow_velocity_vector: GimbalPosition = GimbalPosition(0, 0),
        follow_velocity_mrpm: GimbalVelocity = GimbalVelocity(0, 0),
        interval_sleep_time_ms: int = 0,
        mode: GoToModeType = servo_pb.IMMEDIATE,
        fast_return: bool = False,
        timeout_ms: int = 1000,
    ) -> GimbalPosition:
        self._assert_operational()
        assert self._board is not None
        if velocity is None:
            velocity = GimbalVelocity(self._max_velocity_mrpm, self._max_velocity_mrpm)
        return GimbalPosition(
            *await self._board.gimbal_go_to_delta_follow(
                delta_position,
                velocity,
                follow_velocity_vector,
                follow_velocity_mrpm,
                interval_sleep_time_ms,
                mode,
                fast_return,
                timeout_ms=timeout_ms,
            )
        )

    async def go_to_pan(self, position: int, velocity: Optional[int] = None, await_settle: bool = False,) -> None:
        assert self._board is not None
        assert self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING
        if velocity is None:
            velocity = self._max_velocity_mrpm
        await self._board.gimbal_go_to_pan(position, velocity, await_settle)

    async def go_to_tilt(self, position: int, velocity: Optional[int] = None, await_settle: bool = False,) -> None:
        assert self._board is not None
        assert self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING
        if velocity is None:
            velocity = self._max_velocity_mrpm
        await self._board.gimbal_go_to_tilt(position, velocity, await_settle)

    async def get_limits(self) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        assert self._board is not None
        assert self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING
        return await self._board.gimbal_get_limits()

    async def get_pos_at_time(self, timestamp_us: int) -> Tuple[PositionAt, PositionAt]:
        assert self._board is not None
        assert self._status.code == DeviceStatusCode.OK or self._status.code == DeviceStatusCode.WARNING
        return await self._board.gimbal_get_pos_at_time(timestamp_us)

    def get_shootable_status(self) -> bool:
        if self._pulczar_status is None:
            return False
        return self._pulczar_status.is_shootable()

    async def laser_set(self, on: bool) -> PulczarBoardFireStatus:
        self._assert_operational()
        assert self._board is not None
        if (
            on
            and self._fail_on_bad_status
            and self._pulczar_status is not None
            and not self._pulczar_status.is_safe_to_fire()
            and not self._stf_alt_override
        ):
            raise PulczarBoardException("Laser is not Safe to Fire")
        if (not self._laser_disabled and self._armed) or not on:
            self._firing = on
            response = await self._board.laser_set(on)
            if on and self._laser_requested_on_timestamp_ms is None and self.get_shootable_status():
                if self._laser_record is not None:
                    self._laser_record.triggers += 1
                self._laser_requested_on_timestamp_ms = maka_control_timestamp_ms()
            elif not on and self._laser_requested_on_timestamp_ms is not None:
                duration_ms = maka_control_timestamp_ms() - self._laser_requested_on_timestamp_ms
                if self._laser_record is not None:
                    self._laser_record.millis += duration_ms
                self._laser_requested_on_timestamp_ms = None

            if self._use_bwt_over_serial:
                await self._bwt_command(f"WEN {'Y' if on else 'N'}")
        else:
            response = PulczarBoardFireStatus(False, False, False, 0, 0)
        return response

    async def lens_set(self, value: int) -> None:
        assert self._board is not None
        await self._board.lens_set(value)
        self._cached_lens_value = value

    async def lens_get(self) -> int:
        assert self._board is not None
        self._cached_lens_value = await self._board.lens_get()
        return self._cached_lens_value

    async def lens_get_cached(self) -> int:
        if not self._cached_lens_value:
            assert self._board is not None
            self._cached_lens_value = await self._board.lens_get()

        return self._cached_lens_value

    async def get_arc_state(self) -> ArcDetectorStatus:
        assert self._board is not None
        return await self._board.get_arc_detector_status()

    async def reset_arc_state(self) -> None:
        assert self._board is not None
        await self._board.reset_arc_detector_alarm()

    async def get_delta_target_config(self) -> Tuple[float, float]:
        assert self._board is not None
        try:
            return await self._board.get_delta_target_config()
        except PulczarBoardException:
            return 0, 0
        except Exception as ex:
            LOG.error(f"Exception Occured getting delta target config from Scanner: {self.scanner_id} -> {ex}")
            # TODO Consider what should really be done here....
            return 0, 0

    async def get_serial_number_config(self) -> str:
        assert self._board is not None
        return await self._board.get_serial_number_config()

    # Sync API because the robot isn't async yet :)

    def arm_from_sync(self, armed: bool) -> None:
        future = asyncio.run_coroutine_threadsafe(self.arm(armed), self._event_loop)
        return future.result()

    def get_delta_target_config_from_sync(self) -> Tuple[float, float]:
        future = asyncio.run_coroutine_threadsafe(self.get_delta_target_config(), self._event_loop)
        return future.result()

    def go_to_from_sync(
        self, position: GimbalPosition, velocity: Optional[GimbalVelocity] = None, await_settle: bool = False,
    ) -> None:
        future = asyncio.run_coroutine_threadsafe(self.go_to(position, velocity, await_settle), self._event_loop)
        return future.result()

    def go_to_pan_from_sync(self, position: int, velocity: Optional[int] = None, await_settle: bool = False,) -> None:
        future = asyncio.run_coroutine_threadsafe(self.go_to_pan(position, velocity, await_settle), self._event_loop)
        return future.result()

    def go_to_tilt_from_sync(self, position: int, velocity: Optional[int] = None, await_settle: bool = False,) -> None:
        future = asyncio.run_coroutine_threadsafe(self.go_to_tilt(position, velocity, await_settle), self._event_loop)
        return future.result()

    def get_pos_vel_from_sync(self) -> Tuple[GimbalPosition, GimbalVelocity]:
        future = asyncio.run_coroutine_threadsafe(self.get_pos_vel(), self._event_loop)
        return future.result()

    def get_limits_from_sync(self) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        future = asyncio.run_coroutine_threadsafe(self.get_limits(), self._event_loop)
        return future.result()

    def laser_set_from_sync(self, on: bool) -> PulczarBoardFireStatus:
        future = asyncio.run_coroutine_threadsafe(self.laser_set(on), self._event_loop)
        return future.result()

    def lens_set_from_sync(self, value: int) -> None:
        future = asyncio.run_coroutine_threadsafe(self.lens_set(value), self._event_loop)
        return future.result()

    def lens_get_from_sync(self) -> int:
        future = asyncio.run_coroutine_threadsafe(self.lens_get(), self._event_loop)
        return future.result()

    def get_serial_number_config_sync(self) -> str:
        future = asyncio.run_coroutine_threadsafe(self.get_serial_number_config(), self._event_loop)
        return future.result()

    def get_best_velocity_for_move(self, delta_ticks: int, is_tilt: bool) -> int:
        factor = self._small_move_velocity_mrpm[0]
        if is_tilt:
            factor = self._small_move_velocity_mrpm[1]
        val = factor * (abs(delta_ticks) / 200)
        return int(max(factor / 2, min(val, self.max_velocity_mrpm)))

    @property
    def powered(self) -> bool:
        if self._pulczar_status is not None:
            return self._pulczar_status.power
        return False

    @property
    def event_loop(self) -> AbstractEventLoop:
        return self._event_loop

    @property
    def state_match(self) -> bool:
        return self.get_status_sync().code == self._expected_status

    @property
    def pulczar_status(self) -> Optional[PulczarStatus]:
        return self._pulczar_status

    @property
    def board(self) -> PulczarBoardConnector:
        assert self._board is not None
        return self._board

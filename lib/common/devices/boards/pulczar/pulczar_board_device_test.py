import asyncio
from argparse import Argument<PERSON>arser
from typing import Any, Dict

import lib.common.logging
from lib.common.devices.boards.pulczar.pulczar_board_device import GimbalPosition, PulczarBoardDevice
from lib.common.devices.registry import DeviceRegistry
from lib.common.devices.tests.registry_test import boot_devices_from_json
from lib.common.tasks.manager import get_event_loop_by_name

LOG = lib.common.logging.get_logger(__name__)


def _construct_file(scanner_id: int) -> Dict[str, Any]:
    return {
        "devices": {
            "pulczar_1": {
                "module": "lib.common.devices.boards.pulczar.pulczar_board_device",
                "boot_func": "PulczarBoardDevice",
                "kwargs": {
                    "scanner_id": scanner_id,
                    "max_velocity_mrpm": 590000,
                    "settle_timeout_ms": 1000,
                    "settle_window": 50,
                    "home_offset": 1000,
                    "home_step": 1000,
                    "dawg_timeout_ms": 1000,
                    "dawg_petting_interval_ms": 300,
                },
            },
        }
    }


async def _test(test_file: Dict[str, Any]) -> None:
    registry = DeviceRegistry()
    await boot_devices_from_json(registry, test_file, enable=True)
    pulczar = await registry.get_device(PulczarBoardDevice, "pulczar_1")

    assert pulczar is not None

    await pulczar.arm(True)
    await asyncio.sleep(1)
    print(await pulczar.get_limits())
    await pulczar.go_to(GimbalPosition(1000, 1000), None, True)
    await asyncio.sleep(1)
    await pulczar.laser_set(True)
    await pulczar.get_pos_vel()
    await asyncio.sleep(1)
    await pulczar.laser_set(False)
    await asyncio.sleep(1)
    await pulczar.arm(False)

    await pulczar.disable()


def main() -> None:
    parser = ArgumentParser("Pulczar Board Tester")
    parser.add_argument("scanner_id", type=int)
    args = parser.parse_args()
    lib.common.logging.init_log(level="DEBUG")
    future = _test(_construct_file(args.scanner_id))
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

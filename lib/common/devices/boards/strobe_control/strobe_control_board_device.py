import asyncio
from typing import Any, Dict, Optional

import lib.common.logging
from config.client.cpp.config_client_python import ConfigTree
from lib.common.devices.bootloadable_device import PsocEthernetBootloadableDevice
from lib.common.devices.device import Devi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from lib.common.role import is_simulator
from lib.drivers.nanopb.strobe_control_board.strobe_control_board_connector import (
    STROBE_CONTROL_BOOTLOADER_IP,
    STROBE_CONTROL_IP,
    STROBE_CONTROL_PORT,
    StrobeControlBoardConnector,
)

LOG = lib.common.logging.get_logger(__name__)


class StrobeControlBoardDevice(PsocEthernetBootloadableDevice):
    def __init__(
        self,
        exposure_us: ConfigTree,
        period_us: ConfigTree,
        targets_per_predict_ratio: ConfigTree,
        slayer_strobe_recommendation_override: ConfigTree,
        firmware_name: str = "StrobeControl",
        **kwargs: Any,
    ):
        super().__init__(firmware_name=firmware_name, **kwargs)

        self._loop: Optional[asyncio.AbstractEventLoop] = None

        self._exposure_us = exposure_us.get_int_value()
        self._period_us = period_us.get_int_value()
        self._targets_per_predict_ratio = targets_per_predict_ratio.get_int_value()

        self._exposure_us_config: ConfigTree = exposure_us
        self._period_us_config: ConfigTree = period_us
        self._targets_per_predict_ratio_config: ConfigTree = targets_per_predict_ratio
        self._slayer_strobe_recommendation_override_config: ConfigTree = slayer_strobe_recommendation_override

        self._exposure_us_config.register_callback(self.set_strobe_settings_safe)
        self._period_us_config.register_callback(self.set_strobe_settings_safe)
        self._targets_per_predict_ratio_config.register_callback(self.set_strobe_settings_safe)
        self._slayer_strobe_recommendation_override_config.register_callback(self.set_strobe_settings_safe)

    @property
    def exposure_us(self) -> int:
        return self._exposure_us

    @property
    def period_us(self) -> int:
        return self._period_us

    @property
    def targets_per_predict_ratio(self) -> int:
        return self._targets_per_predict_ratio

    def set_strobe_settings_safe(self) -> None:
        if self._loop is not None:
            self._loop.run_until_complete(
                self.set_strobe_settings(self._exposure_us, self._period_us, self._targets_per_predict_ratio)
            )

    async def set_strobe_settings(self, exposure_us: int, period_us: int, targets_per_predict_ratio: int) -> None:
        if self._slayer_strobe_recommendation_override_config.get_bool_value():
            exposure_us = self._exposure_us_config.get_int_value()
            period_us = self._period_us_config.get_int_value()
            targets_per_predict_ratio = self._targets_per_predict_ratio_config.get_int_value()
        await self._board.strobe_control(exposure_us, period_us, targets_per_predict_ratio)
        self._exposure_us = exposure_us
        self._period_us = period_us
        self._targets_per_predict_ratio = targets_per_predict_ratio

    async def _get_diagnostic(self) -> Dict[str, str]:
        return {}

    async def enable(self) -> None:
        if self._loop is None:
            self._loop = asyncio.get_event_loop()
        self._set_status(DeviceStatusCode.BOOTING)
        try:
            self._connector, self._board = await self.get_board(
                STROBE_CONTROL_IP, STROBE_CONTROL_PORT, StrobeControlBoardConnector,
            )
            await self.initialize_firmware(STROBE_CONTROL_BOOTLOADER_IP, self._connector.get_identifier(), self._board)
            if not is_simulator():
                await self._board.strobe_control(self._exposure_us, self._period_us, self._targets_per_predict_ratio)
        except Exception as e:
            self._set_status(DeviceStatusCode.ERROR)
            raise e
        self._set_status(DeviceStatusCode.OK)

    async def disable(self) -> None:
        self._set_status(DeviceStatusCode.STOPPING)
        await self._board.stop()
        await self._connector.close()
        self._set_status(DeviceStatusCode.STOPPED)

    async def ping(self) -> None:
        assert self._board is not None
        await self._board.ping()

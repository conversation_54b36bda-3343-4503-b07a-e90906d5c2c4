import asyncio
from typing import Any, Dict, cast

from pymodbus.client.sync import ModbusTcpClient
from pymodbus.exceptions import ModbusIOException

import lib.common.logging
from lib.common.devices.device import Device, DeviceStatusCode
from lib.common.error import MakaException
from lib.common.generation import is_bud, is_reaper, is_slayer

LOG = lib.common.logging.get_logger(__name__)

# import logging
# logging.getLogger("pymodbus").setLevel(logging.DEBUG)
# logging.getLogger("pymodbus.client.sync").setLevel(logging.DEBUG)

if is_bud():
    MODBUS_IP = "*********"
else:
    MODBUS_IP = "*********"
MODBUS_PORT = 9504


class SafetyPLCStatus:
    def __init__(self) -> None:
        self.in_cab_e_stop = False  # True when cab E-Stop is pressed
        self.left_e_stop = False  # True when left side E-Stop is pressed
        self.right_e_stop = False  # True when right side E-Stop is pressed
        self.lift_prox = False  # True when implemented is lifted
        self.laser_key = False  # True when key is turned in / False if key is removed
        self.interlock = False  # True if interlock is intalled / False if removed
        self.water_protect = False  # True if chiller is happy
        self.overall_e_stop = False  # True if machine is E-Stopped for any reason
        self.reset_required = False  # True on boot if everything is happy and we need positive confirmation
        self.center_e_stop = False  # True when center E-Stop is pressed
        self.power_button_e_stop = False  # True when power button E-Stop is pressed
        self.left_lpsu_door_interlock = False  # True if left LPSU door is open
        self.right_lpsu_door_interlock = False  # True if right LPSU door is open
        self.debug_mode = False  # True if debug is active and LPSU door interlocks are ignored when in debug mode


class SafetyPLCDevice(Device):
    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self._client = ModbusTcpClient(MODBUS_IP, timeout=10, debug=True)
        self._client.set_debug(True)

    def __del__(self) -> None:
        if not self._client.is_socket_open():
            return
        self._client.close()

    async def _connect(self) -> None:
        if self._client.is_socket_open():
            return
        connected = await asyncio.get_event_loop().run_in_executor(None, lambda: cast(bool, self._client.connect()))
        if not connected:
            raise MakaException(f"Failed to connect to safety PLC modbus server at {MODBUS_IP}")

    async def enable(self) -> None:
        self._set_status(DeviceStatusCode.OK)

    async def disable(self) -> None:
        self._set_status(DeviceStatusCode.STOPPED)

    async def _get_diagnostic(self) -> Dict[str, Any]:
        return {}

    async def get_safety_status(self) -> SafetyPLCStatus:
        try:
            return await self._get_safety_status()
        except (ValueError, asyncio.TimeoutError):
            LOG.info("Reset modbus connection for stale socket")
            # the connection can be ended by the other side and we would be stuck with an open socket still
            self._client.close()
            return await self._get_safety_status()
        except Exception as ex:
            LOG.warning(f"Unknown exception getting safety status: {ex}")
            raise ex

    async def _get_safety_status(self) -> SafetyPLCStatus:
        await self._connect()
        status = SafetyPLCStatus()
        if is_bud():
            result = await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(None, lambda: self._client.read_coils(MODBUS_PORT, 6)), 1
            )
            if isinstance(result, ModbusIOException):
                raise result
            status.in_cab_e_stop = False
            status.left_e_stop = not result.bits[3]
            status.right_e_stop = not result.bits[4]
            status.lift_prox = False
            status.laser_key = True
            status.interlock = True
            status.water_protect = True
            status.overall_e_stop = (
                not result.bits[0]
                or not result.bits[1]
                or not result.bits[2]
                or not result.bits[3]
                or not result.bits[4]
                or not result.bits[5]
            )
            status.reset_required = False
            status.center_e_stop = False
            status.power_button_e_stop = False
            status.left_lpsu_door_interlock = True
            status.right_lpsu_door_interlock = True
            status.debug_mode = False
        elif is_slayer():
            result = await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(None, lambda: self._client.read_coils(MODBUS_PORT, 14)), 1
            )
            if isinstance(result, ModbusIOException):
                LOG.error(f"Modbus Error: {result}")
                raise result

            status.in_cab_e_stop = not result.bits[0]
            status.left_e_stop = not result.bits[1]
            status.right_e_stop = not result.bits[2]
            status.lift_prox = result.bits[3]
            status.laser_key = result.bits[4]
            status.interlock = result.bits[5]
            status.water_protect = result.bits[6]
            status.overall_e_stop = not result.bits[7]

            if len(result.bits) >= 14:
                status.reset_required = result.bits[8]
                status.center_e_stop = not result.bits[9]
                status.power_button_e_stop = not result.bits[10]
                status.left_lpsu_door_interlock = result.bits[11]
                status.right_lpsu_door_interlock = result.bits[12]
                status.debug_mode = result.bits[13]
            else:
                status.reset_required = False
                status.center_e_stop = False
                status.power_button_e_stop = False
                status.left_lpsu_door_interlock = True
                status.right_lpsu_door_interlock = True
                status.debug_mode = False
        elif is_reaper():
            result = await asyncio.wait_for(
                asyncio.get_event_loop().run_in_executor(None, lambda: self._client.read_coils(MODBUS_PORT, 14)), 1
            )
            if isinstance(result, ModbusIOException):
                LOG.error(f"Modbus Error: {result}")
                raise result

            status.in_cab_e_stop = not result.bits[0]
            status.left_e_stop = not result.bits[1]
            status.right_e_stop = not result.bits[2]
            status.lift_prox = result.bits[3]
            status.laser_key = result.bits[4]
            status.interlock = result.bits[5]
            status.water_protect = result.bits[6]
            status.overall_e_stop = not result.bits[7]
            status.reset_required = result.bits[8]
            status.center_e_stop = not result.bits[9]

            # No power button e-stop on Reaper
            status.power_button_e_stop = False

            # Reaper does not have LPSU interlocks
            status.left_lpsu_door_interlock = True
            status.right_lpsu_door_interlock = True

            # no support for debug mode as LPSU interlocks are not used
            status.debug_mode = False
        else:
            raise MakaException("Unsupported generation")

        return status

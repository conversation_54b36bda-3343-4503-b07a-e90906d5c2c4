import asyncio
from ipaddress import IPv4Address
from typing import TYPE_CHECKING, Any, Dict, List, Optional

from config.client.cpp.config_client_python import get_global_config_subscriber
from lib.common.devices.bootloadable_device import PsocEthernetBootloadableDevice
from lib.common.devices.device import Devi<PERSON><PERSON><PERSON><PERSON><PERSON>ode
from lib.common.logging import RateFilter, get_logger, record_location_key_func
from lib.common.units.duration import Duration
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_connector import (
    REAPER_MODULE_CONTROLLER_PORT,
    ReaperModuleConnector,
)
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_types import StrobeConfig
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector
from software_manager.client import SoftwareManagerClient

LOG = get_logger(__name__)


# allow static analysis to see the connector methods
if TYPE_CHECKING:
    DynamicBase = ReaperModuleConnector
else:
    DynamicBase = object


class ReaperMcbDevice(PsocEthernetBootloadableDevice, DynamicBase):
    """
    Device/board class for a Reaper Module Control Board (MCB)

    Handles firmware updating and exposes a connector if the device is booted.
    """

    def __init__(
        self, address: IPv4Address, module_id: Optional[int] = None, pc_ip: Optional[IPv4Address] = None, **kwargs: Any
    ) -> None:
        mcb_id = str(module_id) if module_id else str(address)
        allow_updates: bool = get_global_config_subscriber().get_config_node(
            "hardware_manager", "allow_reaper_mcb_firmware_updates"
        ).get_bool_value()
        super().__init__(
            device_id=f"reaper_mcb_{mcb_id}",
            pps_enabled=False,
            # always update to the latest firmware
            strict_version=allow_updates,
            auto_updates=allow_updates,
            version_check=allow_updates,
            # bootloader type and firmware type
            bootloader_type="mcuboot",
            firmware_name="reaper_module_controller",
            **kwargs,
        )

        self._mcb_address = address
        self._module_id = module_id
        self._pc_ip = pc_ip

        self._connector: PsocMEthernetConnector
        self._board: Optional[ReaperModuleConnector] = None

        self.__added_attrs: List[str] = []

        self._has_config_callbacks: bool = False

    @property
    def board(self) -> ReaperModuleConnector:
        assert self._board is not None
        return self._board

    @property
    def module_id(self) -> Optional[int]:
        return self._module_id

    async def _get_diagnostic(self) -> Dict[str, str]:
        """
        Retrieve diagnostic information

        This includes the firmware and hardware versions of the board.
        """
        if self._board:
            board_version = await self._board.get_board_version()
            firmware_version = await self._board.get_firmware_version()

            return {
                "hw_model": board_version.model,
                "hw_rev": str(board_version.rev),
                "firmware": str(firmware_version),
            }
        else:
            return {}

    async def wait_for_system_ready_for_update(self) -> None:
        should_wait = (
            get_global_config_subscriber()
            .get_config_node("hardware_manager", "validate_system_status_before_mcb_update")
            .get_bool_value()
        )
        if not should_wait:
            return
        ll = LOG.getChild(f"fw_update (module{self._module_id})")
        ll.addFilter(RateFilter(record_location_key_func))
        if self._pc_ip is None:
            ll.info("no ip address for PC cannot check system status")
            return
        ll.info("Waiting for system ready...")
        client = SoftwareManagerClient(hostname=str(self._pc_ip))
        while True:
            try:
                success = await client.ping(timeout_seconds=10)
                if not success:
                    ll.info("waiting for software manager ...")
                else:
                    ll.info("Software manager ready")
                    return
            except Exception:
                ll.exception("failed to reach software manager")
            await asyncio.sleep(10)

    async def enable(self) -> None:
        """
        Prepare communication with the MCB

        This will attempt to determine if the board needs a firmware update
        """
        self._set_status(DeviceStatusCode.BOOTING)

        # create connector and try to update firmware
        try:
            self._connector, self._board = await self.get_board(
                str(self._mcb_address), REAPER_MODULE_CONTROLLER_PORT, ReaperModuleConnector,
            )
            await self.initialize_firmware(str(self._mcb_address), str(self._mcb_address), self._board)

            # cool trick to export all methods from the connector on the board class (as long as enabled)
            for name in dir(self._board):
                if name.startswith("_"):
                    continue
                setattr(self, name, getattr(self._board, name))
                self.__added_attrs.append(name)

            # once booted, upload initial config and watch for subsequent changes
            await self._upload_strobe_config()

            if not self._has_config_callbacks:
                await self._init_config_watchers()

                self._has_config_callbacks = True

            # TODO: forward lifted state to MCB?
        except BaseException as e:
            self._set_status(DeviceStatusCode.ERROR)
            raise e

        self._set_status(DeviceStatusCode.OK)

    async def disable(self) -> None:
        """
        Clean up MCB communication
        """
        self._set_status(DeviceStatusCode.STOPPING)

        # remove the forwarded methods from connector
        for attr in self.__added_attrs:
            delattr(self, attr)
        self.__added_attrs.clear()

        self._board = None
        await self._connector.close()
        self._set_status(DeviceStatusCode.STOPPED)

    async def _init_config_watchers(self) -> None:
        """
        Set up the config tree watchers for all configs to be applied to the MCB.
        """
        config_subscriber = get_global_config_subscriber()
        local_loop = asyncio.get_running_loop()

        def strobe_callback() -> None:
            asyncio.run_coroutine_threadsafe(self._upload_strobe_config(), local_loop).result()

        node = config_subscriber.get_config_node("hardware_manager", "device_overrides/strobe_control_params")
        node.register_callback(strobe_callback)

    async def _upload_strobe_config(self) -> None:
        """
        Apply the strobe configuration from config

        Currently this uses the `hardware_manager/device_overrides/strobe_control_params` path
        """
        status = await self.get_status()
        if status.code not in [DeviceStatusCode.OK, DeviceStatusCode.WARNING, DeviceStatusCode.BOOTING]:
            return

        config_subscriber = get_global_config_subscriber()
        exposure = config_subscriber.get_config_node(
            "hardware_manager", "device_overrides/strobe_control_params/exposure_us"
        ).get_int_value()
        period = config_subscriber.get_config_node(
            "hardware_manager", "device_overrides/strobe_control_params/period_us"
        ).get_int_value()
        ratio = config_subscriber.get_config_node(
            "hardware_manager", "device_overrides/strobe_control_params/targets_per_predict_ratio"
        ).get_int_value()

        config = StrobeConfig(
            period=Duration.from_microseconds(period),
            exposureDuration=Duration.from_microseconds(exposure),
            targetPredictRatio=ratio,
        )
        LOG.debug(f"Applying strobe settings from config: {config}")

        await self.board.set_strobe_config(config)

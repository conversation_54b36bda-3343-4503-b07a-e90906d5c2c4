import abc
import math
from typing import Any, Dict, List, <PERSON>ple

import pandas as pd

from lib.common.devices.boards.nofx.nofx_board_device import DEFAULT_ROTARY_RESOLUTION
from lib.common.devices.device import Device
from lib.common.logging import get_logger
from lib.common.time import maka_control_timestamp_ms
from wheel_encoder.pybind.wheel_encoder_python import WheelEncoder, WheelEncoderCB

LOG = get_logger(__name__)


class MockNoFXBoardDevice(Device, abc.ABC):
    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self._timestamps_ms: List[int] = []
        self._rotary_resolution = DEFAULT_ROTARY_RESOLUTION
        # self._circum_mm = math.pi * DEFAULT_WHEEL_DIAMETER_IN * 25.4
        self._circum_mm = math.pi * 4.096 * 25.4
        self._circums_mm = {
            "front_left": math.pi * 4.096 * 25.4,
            "front_right": math.pi * 4.096 * 25.4,
            "back_left": math.pi * 4.096 * 25.4,
            "back_right": math.pi * 4.096 * 25.4,
        }
        # self._reversed_polarity = False
        self._reversed_polarity = True
        self._wheel_encoder_types = {
            "front_left": "quad",
            "front_right": "quad",
            "back_left": "quad",
            "back_right": "quad",
        }
        self._wheel_encoders_enabled = {
            "front_left": False,
            "front_right": False,
            "back_left": True,
            "back_right": False,
        }
        self._poll_routine_interval_ms = 30
        WheelEncoder.set(
            WheelEncoderCB(
                self.get_latest_snapshot_poll_callback,
                True,
                self._reversed_polarity,
                self._rotary_resolution,
                self._poll_routine_interval_ms,
            )
        )
        self._wheel_encoder = WheelEncoder.get()

    @property
    def rotary_resolution(self) -> int:
        return self._rotary_resolution

    @property
    def circum_mm(self) -> float:
        return self._circum_mm

    @property
    def circums_mm(self) -> Dict[str, float]:
        return self._circums_mm

    @property
    def wheel_encoder_types(self) -> Dict[str, Any]:
        return self._wheel_encoder_types

    @property
    def reversed_polarity(self) -> bool:
        return self._reversed_polarity

    def read_latest_snapshot_data(
        self, csv_file: str, time_bump_usec: int, time_adjust_usec: int, timestamps_ms: List[int],
    ) -> pd.DataFrame:
        self._timestamps_ms = timestamps_ms
        self._time_bump_usec = time_bump_usec
        self._time_adjust_usec = time_adjust_usec
        self._df = pd.read_csv(csv_file)
        self._df.timestamp_ms = (time_bump_usec + (self._df.timestamp_ms * 1000 - time_adjust_usec)) // 1000

    def _interpolate(self, x1: int, x2: int, t1: int, t2: int, tm: int) -> float:
        d_t = t2 - t1
        d_x = x2 - x1
        portion = tm - t1

        proportion = portion / d_t if d_t > 0 else 0

        return proportion * d_x + x1

    async def _get_diagnostic(self) -> Dict[str, str]:
        return {}

    async def enable(self) -> None:
        pass

    async def disable(self) -> None:
        self._wheel_encoder.stop()

    def get_next_mm_velocity_ms(self, last_timestamp_ms: int) -> Tuple[int, float]:
        raise NotImplementedError()

    def get_latest_snapshot_poll_callback(self) -> Tuple[int, int, int, int, int]:
        current_time_ms = maka_control_timestamp_ms()

        if len(self._timestamps_ms) == 0:
            return current_time_ms * 1000, 0, 0, 0, 0

        if (current_time_ms < self._timestamps_ms[0]) or (current_time_ms > self._timestamps_ms[-1]):
            return current_time_ms * 1000, 0, 0, 0, 0

        closest_row_before = self._df.loc[(self._df.timestamp_ms <= current_time_ms)].iloc[-1]
        closest_row_after = self._df.loc[(self._df.timestamp_ms >= current_time_ms)].iloc[0]

        fr = int(
            self._interpolate(
                closest_row_before.at["snapshot_fr"],
                closest_row_after.at["snapshot_fr"],
                closest_row_before.at["timestamp_ms"],
                closest_row_after.at["timestamp_ms"],
                current_time_ms,
            )
        )
        fl = int(
            self._interpolate(
                closest_row_before.at["snapshot_fl"],
                closest_row_after.at["snapshot_fl"],
                closest_row_before.at["timestamp_ms"],
                closest_row_after.at["timestamp_ms"],
                current_time_ms,
            )
        )
        br = int(
            self._interpolate(
                closest_row_before.at["snapshot_br"],
                closest_row_after.at["snapshot_br"],
                closest_row_before.at["timestamp_ms"],
                closest_row_after.at["timestamp_ms"],
                current_time_ms,
            )
        )
        bl = int(
            self._interpolate(
                closest_row_before.at["snapshot_bl"],
                closest_row_after.at["snapshot_bl"],
                closest_row_before.at["timestamp_ms"],
                closest_row_after.at["timestamp_ms"],
                current_time_ms,
            )
        )

        return (
            int(current_time_ms * 1000),
            fl if self._wheel_encoders_enabled["front_left"] else 0,
            fr if self._wheel_encoders_enabled["front_right"] else 0,
            bl if self._wheel_encoders_enabled["back_left"] else 0,
            br if self._wheel_encoders_enabled["back_right"] else 0,
        )

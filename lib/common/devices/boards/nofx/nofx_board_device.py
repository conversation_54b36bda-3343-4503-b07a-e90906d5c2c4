import asyncio
import concurrent
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional, Tuple, Union, cast

from config.client.cpp.config_client_python import ConfigTree, get_global_config_subscriber
from hardware_manager.python.client import HardwareManagerClient
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.db.carbon_robot_db import CarbonRobotDBLite
from lib.common.db.records.latest_rotary_samples import LatestRotarySamples
from lib.common.devices.boards.nofx.metrics import (
    WHEEL_ENCODER_AVG_VEL_GAUGE,
    WHEEL_ENCODER_DELTA_TICKS_GAUGE,
    WHEEL_ENCODER_ERROR_GAUGE,
    WHEEL_ENCODER_TICKS_GAUGE,
)
from lib.common.devices.bootloadable_device import PsocEthernetBootloadableDevice
from lib.common.devices.device import Device<PERSON>tatusCode
from lib.common.generation import get_command_ip
from lib.common.logging import get_logger
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.nofx_board.nofx_board_connector import (
    NOFX_BOOTLOADER_IP,
    NoFXBoardConnector,
    NoFXRotaryType,
    str_to_nofxrotarytype,
)
from lib.drivers.nanopb.pybind.nanopb_python import WheelEncoderNOFX
from wheel_encoder.pybind.wheel_encoder_python import (
    AvgVelocityData,
    DeltaPosData,
    EncoderData,
    VelocityData,
    WheelEncoder,
)

DEFAULT_NOFX_FIRMWARE_BASE = "NoFX"
UNKNOWN = "unknown"
FRONT_LEFT = "front_left"
FRONT_RIGHT = "front_right"
BACK_LEFT = "back_left"
BACK_RIGHT = "back_right"
ENCODER_NAMES = [FRONT_LEFT, FRONT_RIGHT, BACK_LEFT, BACK_RIGHT]

REASONABLE_UPPER_RANGE_USEC = 10000
REASONABLE_LOWER_RANGE_USEC = 200

DEFAULT_ROTARY_RESOLUTION = 20000
DEFAULT_WHEEL_DIAMETER_IN = 30.88
DEFAULT_WHEEL_DIAMETERS = {
    BACK_LEFT: DEFAULT_WHEEL_DIAMETER_IN,
    BACK_RIGHT: DEFAULT_WHEEL_DIAMETER_IN,
    FRONT_LEFT: DEFAULT_WHEEL_DIAMETER_IN,
    FRONT_RIGHT: DEFAULT_WHEEL_DIAMETER_IN,
}

DEFAULT_TREADKILL_WHEEL_DIAMETERS = {
    BACK_LEFT: 4.096,
    BACK_RIGHT: 4.096,
    FRONT_LEFT: 4.096,
    FRONT_RIGHT: 4.096,
}

DEFAULT_LOOP_WARNING_MS = 50

if TYPE_CHECKING:
    EncoderDataQueue = asyncio.Queue[EncoderData]
else:
    EncoderDataQueue = asyncio.Queue

LOG = get_logger(__name__)


def _enc_to_dict(enc_val: Union[EncoderData, VelocityData]) -> Dict[str, float]:
    return {
        FRONT_LEFT: enc_val.front_left,
        FRONT_RIGHT: enc_val.front_right,
        BACK_LEFT: enc_val.back_left,
        BACK_RIGHT: enc_val.back_right,
    }


def _pos_to_dict(enc_val: DeltaPosData) -> Dict[str, float]:
    return {
        FRONT_LEFT: enc_val.front_left_mm,
        FRONT_RIGHT: enc_val.front_right_mm,
        BACK_LEFT: enc_val.back_left_mm,
        BACK_RIGHT: enc_val.back_right_mm,
    }


class NoFXBoardDevice(PsocEthernetBootloadableDevice):
    def __init__(
        self,
        poll_interval_ms: int = 30,
        firmware_name: str = DEFAULT_NOFX_FIRMWARE_BASE,
        reversed_polarity: bool = False,
        rotary_resolution: int = DEFAULT_ROTARY_RESOLUTION,
        use_broadcast: bool = False,
        log_ticks: bool = False,
        **kwargs: Any,
    ):
        super().__init__(firmware_name=firmware_name, **kwargs)
        self._poll_interval_ms = poll_interval_ms
        self._wheel_encoder_types = {
            FRONT_LEFT: "quad",
            FRONT_RIGHT: "quad",
            BACK_LEFT: "quad",
            BACK_RIGHT: "quad",
        }

        self._reversed_polarity = reversed_polarity
        self._event_loop = get_event_loop_by_name()
        self._rotary_resolution = rotary_resolution
        self._db = CarbonRobotDBLite.get_instance()
        self._rows_to_commit_latest: List[LatestRotarySamples] = []
        self._use_broadcast = use_broadcast
        self._miss_count = 0
        self._log = log_ticks
        self._tick_task: Optional[asyncio.Task[None]] = None
        self._encoder_error_msg: Optional[str] = None
        self._consecutive_errors: Dict[str, int] = {}
        self._hw_client = HardwareManagerClient(hostname=get_command_ip())
        self._unknown_anomaly = False
        self._nofx_executors = concurrent.futures.ThreadPoolExecutor(thread_name_prefix="nofx_", max_workers=10)
        self._board = NoFXBoardConnector()
        WheelEncoder.set(
            WheelEncoderNOFX(
                self._board.connector, self._reversed_polarity, self._rotary_resolution, self._poll_interval_ms
            )
        )
        self._wheel_encoder = WheelEncoder.get()
        get_global_config_subscriber().get_config_node("common", "wheel_encoders").register_callback(self._cfg_callback)

    def _cfg_callback(self) -> None:
        self._unknown_anomaly = False

    @property
    def rotary_resolution(self) -> int:
        return self._rotary_resolution

    # @property
    # def circum_mm(self) -> float:
    #    return self.wheel_diameter_mm * math.pi

    # @property
    # def circums_mm(self) -> Dict[str, float]:
    #    return self._circums_mm

    @property
    def reversed_polarity(self) -> bool:
        return self._reversed_polarity

    @property
    def error(self) -> bool:
        return self._miss_count >= 5 or not self._status.is_running()

    @property
    def wheel_encoder_types(self) -> Dict[str, str]:
        return self._wheel_encoder_types

    def wheel_encoder_type(self, v: str) -> NoFXRotaryType:
        return str_to_nofxrotarytype(self._wheel_encoder_types[v])

    async def _get_timestamp_ms(self) -> int:
        return await self._board.get_timestamp()

    async def _set_epoch_timestamp(self) -> None:
        return await self._board.set_epoch_time()

    async def enable(self) -> None:
        if self._status.is_running():
            return
        await self._stop_tick_task()
        self._set_status(DeviceStatusCode.BOOTING)
        try:
            await self._boot_dev()
        except Exception:
            LOG.exception(f"Error enabling: {self.device_id}")
            self._set_status(DeviceStatusCode.ERROR)
        self._tick_task = self._event_loop.create_task(self._ticks_poll_task())

    async def disable(self) -> None:
        self._set_status(DeviceStatusCode.STOPPING)
        await self._stop_tick_task()
        await self._disable_pps_sync()
        self._set_status(DeviceStatusCode.STOPPED)

    async def _stop_tick_task(self) -> None:
        if self._tick_task is not None:
            try:
                self._tick_task.cancel()
                await self._tick_task
            except asyncio.CancelledError:
                pass
            self._tick_task = None

    async def _boot_dev(self) -> None:
        await self.initialize_firmware(NOFX_BOOTLOADER_IP, self._board.ip, self._board)
        await self._enable_pps_sync()
        await self._board.config(
            self.wheel_encoder_type(FRONT_LEFT),
            self.wheel_encoder_type(FRONT_RIGHT),
            self.wheel_encoder_type(BACK_LEFT),
            self.wheel_encoder_type(BACK_RIGHT),
        )
        self._set_status(DeviceStatusCode.OK)

    async def get_next(self, timestamp_ms: int) -> EncoderData:
        return cast(
            EncoderData,
            await asyncio.get_event_loop().run_in_executor(
                self._nofx_executors, lambda: self._wheel_encoder.get_next(timestamp_ms)
            ),
        )

    async def get_cur(self) -> EncoderData:
        return cast(
            EncoderData,
            await asyncio.get_event_loop().run_in_executor(self._nofx_executors, self._wheel_encoder.get_cur),
        )

    async def get_next_vel(self, timestamp_ms: int) -> VelocityData:
        return cast(
            VelocityData,
            await asyncio.get_event_loop().run_in_executor(
                self._nofx_executors, lambda: self._wheel_encoder.get_next_vel(timestamp_ms)
            ),
        )

    async def get_cur_vel(self) -> VelocityData:
        return cast(
            VelocityData,
            await asyncio.get_event_loop().run_in_executor(self._nofx_executors, self._wheel_encoder.get_cur_vel),
        )

    async def get_next_avg_vel(self, timestamp_ms: int) -> AvgVelocityData:
        return cast(
            AvgVelocityData,
            await asyncio.get_event_loop().run_in_executor(
                self._nofx_executors, lambda: self._wheel_encoder.get_next_avg_vel(timestamp_ms)
            ),
        )

    async def get_cur_avg_vel(self) -> AvgVelocityData:
        return cast(
            AvgVelocityData,
            await asyncio.get_event_loop().run_in_executor(self._nofx_executors, self._wheel_encoder.get_cur_avg_vel),
        )

    def total_meters(self) -> float:
        return self._wheel_encoder.total_meters()

    def get_cur_pos(self) -> Tuple[int, float]:
        return self._wheel_encoder.get_cur_pos()

    def avg_dist(self, ticks1: Tuple[int, int, int, int], ticks2: Tuple[int, int, int, int]) -> float:
        return self._wheel_encoder.avg_dist(ticks1, ticks2)

    async def _ticks_poll_task(self) -> None:
        last_timestamp_ms = 0
        prev = None
        anomally_enabled = get_global_config_subscriber().get_config_node(
            "common", "wheel_encoders/anomaly_detection_enabled"
        )
        anomally_delta_time = get_global_config_subscriber().get_config_node(
            "common", "wheel_encoders/anomaly_detection_delta_time"
        )
        queue: EncoderDataQueue = asyncio.Queue()
        metrics_task: Optional[asyncio.Task[None]] = None
        if self._owner:
            metrics_task = self._event_loop.create_task(self._collect_we_metrics(queue, anomally_delta_time))
        try:
            while not bot_stop_handler.stopped:
                try:
                    if not self._status.is_running():
                        await asyncio.sleep(1)
                        try:
                            await self._boot_dev()
                        except asyncio.CancelledError:
                            break
                        except Exception as e:
                            LOG.error(f"Nofx device failing to boot. Err: {e}")
                        continue
                    latest = await asyncio.wait_for(self.get_next(last_timestamp_ms), 1)
                    if self._owner:
                        await queue.put(latest)
                    if prev is None:
                        prev = latest
                    else:
                        assert prev is not None
                        if (
                            anomally_enabled.get_bool_value()
                            and latest.msec - prev.msec > anomally_delta_time.get_uint_value()
                        ):
                            await self._wheel_encoder_anomaly_detection(prev, latest)
                            prev = latest
                    last_timestamp_ms = latest.msec
                    self._miss_count = 0
                except asyncio.CancelledError:
                    break
                except asyncio.TimeoutError:
                    self._miss_count += 1
                    LOG.error(f"Timed out waiting for next rotary data: {last_timestamp_ms}")
                except Exception as ex:
                    self._miss_count += 1
                    await asyncio.sleep(0.5)
                    LOG.error(f"Exception Occured while polling rotary ticks: {ex}")
        finally:
            if metrics_task is not None:
                try:
                    metrics_task.cancel()
                    await metrics_task
                except asyncio.CancelledError:
                    pass

    async def _is_lifted(self) -> bool:
        try:
            safety_status = await self._hw_client.safety_status()
            return safety_status.lifted
        except Exception as e:
            LOG.warning(f"Failed to get lifted state err: {e}")
        return False

    async def _wheel_encoder_anomaly_detection(self, pos1: EncoderData, pos2: EncoderData) -> None:
        bad_encoders = await self._wheel_encoder_anomaly_detector(pos1, pos2)
        if bad_encoders is None:
            # clear all data
            self._consecutive_errors = {}
        elif len(bad_encoders) == 0:
            if UNKNOWN not in self._consecutive_errors:
                self._consecutive_errors[UNKNOWN] = pos2.msec
            for name in ENCODER_NAMES:
                if name in self._consecutive_errors:
                    del self._consecutive_errors[name]
        else:
            for encoder in bad_encoders:
                if encoder not in self._consecutive_errors:
                    self._consecutive_errors[encoder] = pos2.msec
            not_bad = set(ENCODER_NAMES)
            not_bad.add(UNKNOWN)
            not_bad.difference_update(bad_encoders)
            for name in not_bad:
                if name in self._consecutive_errors:
                    del self._consecutive_errors[name]
        await self._alarm_and_disable_encoders(pos2.msec)

    async def _wheel_encoder_anomaly_detector(self, pos1: EncoderData, pos2: EncoderData) -> Optional[List[str]]:
        if await self._is_lifted():
            # TODO clear error data
            return None
        non_zero_delta_mm = (
            get_global_config_subscriber().get_config_node("common", "wheel_encoders/non_zero_vel").get_float_value()
        ) * (pos2.msec - pos1.msec)

        delta = self._wheel_encoder.delta_pos(pos1, pos2)
        enabled = self._wheel_encoder.enabled_encoders()
        values = []
        if enabled.front_left:
            values.append((delta.front_left_mm, FRONT_LEFT))
        if enabled.front_right:
            values.append((delta.front_right_mm, FRONT_RIGHT))
        if enabled.back_left:
            values.append((delta.back_left_mm, BACK_LEFT))
        if enabled.back_right:
            values.append((delta.back_right_mm, BACK_RIGHT))

        if len(values) <= 1:
            # Cannot filter out bad data with only 1 enabled encoder
            return None
        key_func: Callable[[Tuple[float, str]], float] = lambda val: val[0]
        values.sort(key=key_func)
        if values[0][0] == values[-1][0]:
            # perfect match no need to do anything else
            return None
        zero_count = 0
        non_zero_count = 0
        for val in values:
            if val[0] == 0:
                zero_count += 1
            elif abs(val[0]) > non_zero_delta_mm:
                non_zero_count += 1
        if zero_count > 0 and non_zero_count > 0:
            return [val[1] for val in values if val[0] == 0]
        elif non_zero_count > 0:
            return await self._check_invalid_cases(values)
        return None

    async def _check_invalid_cases(self, values: List[Tuple[float, str]]) -> Optional[List[str]]:
        max_val = max([abs(val[0]) for val in values])
        max_delta = (
            max_val
            * get_global_config_subscriber()
            .get_config_node("common", "wheel_encoders/max_allowable_percent_off")
            .get_float_value()
        )
        if abs(values[-1][0] - values[0][0]) < max_delta:
            # delta within allowable range assume wheels are fine
            return None
        if len(values) == 2:
            return []
        if len(values) == 3:
            if abs(values[1][0] - values[0][0]) < max_delta and abs(values[2][0] - values[1][0]) >= max_delta:
                return [values[2][1]]
            elif abs(values[1][0] - values[0][0]) >= max_delta and abs(values[2][0] - values[1][0]) < max_delta:
                return [values[0][1]]
            else:
                return []
        if len(values) == 4:
            if (abs(values[3][0] - values[1][0]) >= max_delta and abs(values[3][0] - values[2][0]) < max_delta) or abs(
                values[2][0] - values[1][0]
            ) >= max_delta:
                return []
            elif abs(values[2][0] - values[0][0]) < max_delta and abs(values[3][0] - values[2][0]) >= max_delta:
                return [values[3][1]]
            elif abs(values[1][0] - values[0][0]) >= max_delta and abs(values[3][0] - values[1][0]) < max_delta:
                return [values[0][1]]
            else:
                return []
        return None

    async def _alarm_and_disable_encoders(self, timestamp_ms: int) -> None:
        delta_ms = (
            get_global_config_subscriber()
            .get_config_node("common", "wheel_encoders/anomaly_detection_span")
            .get_uint_value()
        )
        to_disable = []
        for key, ts in self._consecutive_errors.items():
            if timestamp_ms - ts > delta_ms:
                to_disable.append(key)
        enabled = self._wheel_encoder.enabled_encoders()
        for encoder in to_disable:
            if encoder == FRONT_LEFT:
                enabled.front_left = False
            elif encoder == FRONT_RIGHT:
                enabled.front_right = False
            elif encoder == BACK_LEFT:
                enabled.back_left = False
            elif encoder == BACK_RIGHT:
                enabled.back_right = False
            self._wheel_encoder.override_enabled_encoders(enabled)
        enabled_cfg = self._wheel_encoder.cfg_enabled_encoders()
        modified = []
        if enabled.front_left != enabled_cfg.front_left:
            modified.append(FRONT_LEFT)
        if enabled.front_right != enabled_cfg.front_right:
            modified.append(FRONT_RIGHT)
        if enabled.back_left != enabled_cfg.back_left:
            modified.append(BACK_LEFT)
        if enabled.back_right != enabled_cfg.back_right:
            modified.append(BACK_RIGHT)

        if modified:
            self._encoder_error_msg = f"Encoder(s) {modified} have been overriden, as they appear to be malfunctioning."
        elif to_disable:
            self._unknown_anomaly = True
            self._encoder_error_msg = "Error detecting wheel speed, contact support."
        elif not self._unknown_anomaly:
            # once set we cannot remove this unless we reset config tree
            self._encoder_error_msg = None

    async def _collect_we_metrics(self, queue: EncoderDataQueue, delta_time: ConfigTree) -> None:
        prev = await queue.get()
        while not bot_stop_handler.stopped:
            try:
                latest = await queue.get()
                for enc, val in _enc_to_dict(latest).items():
                    WHEEL_ENCODER_TICKS_GAUGE.labels(enc).set(val)
                dt = latest.msec - prev.msec
                if dt >= delta_time.get_uint_value():
                    delta_pos = self._wheel_encoder.delta_pos(prev, latest)
                    delta_ticks = self._wheel_encoder.delta_ticks(prev, latest)
                    for enc, val in _enc_to_dict(delta_ticks).items():
                        WHEEL_ENCODER_DELTA_TICKS_GAUGE.labels(enc).set(val)
                    for enc, val in _pos_to_dict(delta_pos).items():
                        WHEEL_ENCODER_AVG_VEL_GAUGE.labels(enc).set(val / dt)
                    prev = latest

                    error_state = {name: 0 for name in ENCODER_NAMES}
                    error_state[UNKNOWN] = 0
                    for name in self._consecutive_errors:
                        error_state[name] = 1
                    for name, val in error_state.items():
                        WHEEL_ENCODER_ERROR_GAUGE.labels(name).set(val)

            except asyncio.CancelledError:
                break

    def _log_latest_rotary_snapshot(self, rotary_ticks: Tuple[int, Tuple[int, int, int, int]]) -> None:
        if (
            not self._log
            or not get_global_config_subscriber().get_config_node("aimbot", "log_wheel_encoders").get_bool_value()
        ):
            return
        try:
            row = LatestRotarySamples(
                timestamp_ms=rotary_ticks[0] // 1000,
                snapshot_fl=rotary_ticks[1][0],
                snapshot_fr=rotary_ticks[1][1],
                snapshot_bl=rotary_ticks[1][2],
                snapshot_br=rotary_ticks[1][3],
            )
            self._rows_to_commit_latest.append(row)
            if len(self._rows_to_commit_latest) > 100:
                asyncio.run_coroutine_threadsafe(
                    self._db.db_processor.add_rows(self._rows_to_commit_latest), self._event_loop
                ).result()
                self._rows_to_commit_latest = []
        except Exception:
            LOG.warning("Failed to add rows to db.")

    async def park_brake_on(self) -> None:
        await self._board.park_brake(True)

    async def park_brake_off(self) -> None:
        await self._board.park_brake(False)

    async def fuel_lvl(self) -> float:
        return await self._board.fuel_gauge()

    async def ping(self) -> None:
        await self._board.ping()

    @property
    def encoder_error_msg(self) -> Optional[str]:
        return self._encoder_error_msg

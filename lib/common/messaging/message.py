from dataclasses import dataclass
from functools import wraps
from typing import Any, Awaitable, Callable, Dict, Optional, Protocol, Type, TypeVar, Union

from dataclass_wizard import JSONWizard, json_field
from dataclass_wizard.abstractions import AbstractJ<PERSON><PERSON>Wizard

from lib.common.logging import get_logger

JSONObject = Dict[str, Any]


class Dictable(Protocol):
    def to_dict(self) -> JSONObject:
        ...


ContentType = Union[JSONObject, Dictable]
__monotonic_inc = 0


def next_id() -> int:
    """
    This is not thread safe
    This is asyncio safe, if we need to handle thread safety we should either use an atomic or add a lock for threading.
    since we don't currently use python threads no need to add the lock overhead yet
    """
    global __monotonic_inc
    __monotonic_inc += 1
    return __monotonic_inc


@dataclass
class Empty(JSONWizard):
    ...


@dataclass
class ErrorMsg(JSONWizard):
    msg: Optional[str] = None
    code: Optional[int] = None


@dataclass
class Message(JSONWizard):
    class _(JSONWizard.Meta):
        # skip default values for dataclass fields when `to_dict` is called
        skip_defaults = True
        recursive = False

    msg_type: str = json_field("type", all=True)
    content: Optional[JSONObject] = None
    error: Optional[ErrorMsg] = None
    id: int = -1
    response_to: Optional[int] = None

    @classmethod
    def build(
        cls, msg_type: str, content: ContentType, id: Optional[int] = None, response_to: Optional[int] = None,
    ) -> "Message":
        if hasattr(content, "to_dict"):
            converted: JSONObject = content.to_dict()
        else:
            converted = content
        if id is None:
            id = next_id()
        return cls(msg_type=msg_type, content=converted, id=id, response_to=response_to)

    @classmethod
    def build_error(
        cls, msg_type: str, error: ErrorMsg, id: Optional[int] = None, response_to: Optional[int] = None,
    ) -> "Message":
        if id is None:
            id = next_id()
        return cls(msg_type=msg_type, error=error, id=id, response_to=response_to)

    def set_response_to(self, req: "Message") -> None:
        self.response_to = req.id

    def set_id_if_invalid(self) -> None:
        if self.id == -1:
            self.id = next_id()

    def to_json_bytes(self) -> bytes:
        jsn = self.to_json()
        if isinstance(jsn, bytes):
            return jsn
        assert isinstance(jsn, str)
        return jsn.encode()

    def update_id(self) -> None:
        self.id = next_id()


T = TypeVar("T", bound=AbstractJSONWizard)
R = TypeVar("R")
M = TypeVar("M", bound=Message)


def msg_decoder(msg: Type[T]) -> Callable[[Callable[[Any, T], Awaitable[R]]], Callable[[Any, Message], Awaitable[R]]]:
    def inner(func: Callable[[Any, T], Awaitable[R]]) -> Callable[[Any, Message], Awaitable[R]]:
        @wraps(func)
        async def wrapper(self: Any, message: Message) -> R:
            return await func(self, msg.from_dict(message.content))

        return wrapper

    return inner


_IFHandler = Callable[[Any, Message], Awaitable[ContentType]]
_IFErrorHandler = Callable[[Any, Message], Awaitable[Optional[ErrorMsg]]]
_Callback = Callable[[Any, Message], Awaitable[Message]]


class ErrorMsgException(Exception):
    def __init__(self, err_msg: ErrorMsg) -> None:
        self.error_msg = err_msg

    def __str__(self) -> str:
        return str(self.error_msg.to_json())


def empty_sender_builder(msg_type: Type[M], req_resp_map: Dict[str, str]) -> Callable[[_IFErrorHandler], _Callback]:
    def empty_sender(func: _IFErrorHandler) -> _Callback:
        local_logger = get_logger(func.__name__)

        @wraps(func)
        async def wrapper(self: Any, message: Message) -> Message:
            resp_type = req_resp_map[message.msg_type]
            try:
                opt_err = await func(self, message)
                if opt_err is not None:
                    return msg_type.build_error(resp_type, opt_err)
                else:
                    return msg_type.build(resp_type, {})
            except Exception as ex:
                resp = ErrorMsg(msg=f"Error handling msg {message.msg_type}, ex: {ex}")
                local_logger.error(resp.msg)
                return msg_type.build_error(resp_type, resp)

        return wrapper

    return empty_sender


def msg_sender_builder(msg_type: Type[M], req_resp_map: Dict[str, str]) -> Callable[[_IFHandler], _Callback]:
    def msg_sender(func: _IFHandler) -> _Callback:
        local_logger = get_logger(func.__name__)

        @wraps(func)
        async def wrapper(self: Any, message: Message) -> Message:
            resp_type = req_resp_map[message.msg_type]
            try:
                content = await func(self, message)
                return msg_type.build(resp_type, content)
            except ErrorMsgException as ex:
                local_logger.error(ex.error_msg)
                return msg_type.build_error(resp_type, ex.error_msg)
            except Exception as ex:
                resp = ErrorMsg(msg=f"Error handling msg {message.msg_type}, ex: {ex}")
                local_logger.error(resp.msg)
                return msg_type.build_error(resp_type, resp)

        return wrapper

    return msg_sender

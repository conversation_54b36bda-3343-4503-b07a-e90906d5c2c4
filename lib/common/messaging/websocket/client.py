import asyncio
from abc import ABC
from contextlib import Async<PERSON>xitStack, asynccontextmanager
from typing import TYPE_CHECKING, AsyncIterator, Awaitable, Callable, Dict, List, Optional, Type, cast

import websockets
from dataclass_wizard import <PERSON><PERSON><PERSON>Wizard
from websockets.exceptions import ConnectionClosed, ConnectionClosedError, InvalidStatusCode

from lib.common.asyncio.cancellable_await import cancellable_await
from lib.common.asyncio.wait_map import WaitMap
from lib.common.logging import get_logger
from lib.common.messaging.message import Empty, ErrorMsgException, Message

LOG = get_logger(__name__)

if TYPE_CHECKING:
    MSG_QUEUE = asyncio.Queue[Message]
else:
    MSG_QUEUE = asyncio.Queue

EventMsgHandler = Callable[[Message], Awaitable[None]]
ConnCallback = Callable[[], Awaitable[None]]


class Client(ABC):
    def __init__(
        self,
        url: str,
        req_resp_map: Dict[str, str],
        resp_map: Dict[str, Type[JSONWizard]],
        ws: Optional[websockets.WebSocketClientProtocol] = None,
        reconnect_timeout: int = 1,
    ) -> None:
        """Init should not be called directly, but instead should use a async builder function"""
        self._url = url
        self._req_resp_map = req_resp_map
        self._resp_map = resp_map
        self._ws = ws
        self._rc_timeout = reconnect_timeout
        self._wait_map: WaitMap[int, Message] = WaitMap()
        self._tasks: List[asyncio.Future[None]] = []
        self._cond = asyncio.Condition()
        self._on_conn_event = asyncio.Event()
        self._stop_event = asyncio.Event()
        self._event_handlers: Dict[str, EventMsgHandler] = {}
        self._event_queues: Dict[str, MSG_QUEUE] = {}
        self._started = False
        self._on_connect_cb: List[ConnCallback] = []

    async def reconnect(self) -> None:
        async with self._cond:
            self._ws = await websockets.connect(self._url, open_timeout=10)
            self._cond.notify_all()
        self._on_conn_event.set()

    async def connected(self) -> bool:
        async with self._cond:
            return self._check_connected()

    def _check_connected(self) -> bool:
        if self._ws is None:
            return False
        return not self._ws.closed

    def register(self, msg_type: str, handler: EventMsgHandler) -> None:
        assert not self._started
        self._event_handlers[msg_type] = handler

    async def start(self) -> None:
        assert not self._started
        self._started = True
        self._stop_event.clear()
        for msg_type, handler in self._event_handlers.items():
            queue: MSG_QUEUE = asyncio.Queue()
            self._event_queues[msg_type] = queue
            self._tasks.append(asyncio.get_event_loop().create_task(self._handle_event(handler, queue)))
        self._tasks.append(asyncio.get_event_loop().create_task(self._run()))
        self._tasks.append(asyncio.get_event_loop().create_task(self._on_connect()))

    async def stop(self) -> None:
        self._stop_event.set()
        await asyncio.gather(*self._tasks)
        self._tasks = []
        self._event_queues = {}
        self._started = False

    @asynccontextmanager
    async def run(self) -> AsyncIterator[None]:
        await self.start()
        try:
            yield
        finally:
            await self.stop()

    def on_connect(self, cb: ConnCallback) -> None:
        assert not self._started
        self._on_connect_cb.append(cb)

    async def _on_connect(self) -> None:
        while not self._stop_event.is_set():
            await cancellable_await(self._on_conn_event.wait(), self._stop_event)
            self._on_conn_event.clear()
            if self._stop_event.is_set():
                return
            try:
                await asyncio.gather(*[cb() for cb in self._on_connect_cb])
            except Exception:
                LOG.exception("OnConnect callback failed")

    async def __auto_reconnect(self) -> None:
        if self._rc_timeout <= 0:
            return
        while not self._stop_event.is_set():
            try:
                LOG.info("Attempting reconnect...")
                await self.reconnect()
                LOG.info("Auto reconnect successful")
                return
            except (ConnectionRefusedError, asyncio.exceptions.CancelledError, InvalidStatusCode, asyncio.TimeoutError):
                LOG.info("Reconnect attempt failed")
            except Exception:
                LOG.exception("Reconnect attempt failed with unknown error")
            await cancellable_await(asyncio.sleep(self._rc_timeout), self._stop_event)

    async def _handle_event(self, handler: EventMsgHandler, queue: MSG_QUEUE) -> None:
        while not self._stop_event.is_set():
            try:
                opt_msg = await cancellable_await(queue.get(), self._stop_event)
                if opt_msg is None:
                    continue
                await handler(opt_msg)
            except Exception:
                LOG.exception("Unknown exception handling event msg")

    async def _handle_msg(self, m: Message) -> None:
        if m.response_to is not None:
            try:
                await self._wait_map.set_resp(m.response_to, m)
            except KeyError as err:
                LOG.error(f"{err} for {m}")
            except Exception as err:
                LOG.error(err)
        else:
            if m.msg_type in self._event_queues:
                try:
                    await self._event_queues[m.msg_type].put(m)
                except Exception as err:
                    LOG.error(err)
            else:
                LOG.info(f"No handler defined for received server event type {m.msg_type}.")

    async def _run(self) -> None:
        while not self._stop_event.is_set():
            task = None
            async with self._cond:
                if not self._check_connected():
                    task = asyncio.create_task(self.__auto_reconnect())
                    await cancellable_await(self._cond.wait(), self._stop_event)
                    if self._stop_event.is_set():
                        return
                assert self._ws is not None
                ws = self._ws
            if task is not None:
                await task
            try:
                opt_ws_msg = await cancellable_await(ws.recv(), self._stop_event)
                if not opt_ws_msg:
                    continue
                m = cast(Message, Message.from_json(opt_ws_msg))
                await self._handle_msg(m)
            except ConnectionClosed:
                LOG.info("connection failed waiting for reconnect")
            except Exception:
                LOG.exception("Unknown exception in ws client:")
            else:
                continue
            # Hit for any exception case
            await self.__auto_reconnect()

    async def close(self) -> None:
        for task in self._tasks:
            task.cancel()
            try:
                await task
            except BaseException:
                pass
        async with self._cond:
            if self._ws is not None:
                await self._ws.close()

    async def send_recv_raw(self, in_msg: Message, timeout: Optional[float] = None) -> Message:
        if timeout is None:
            timeout = 1.0
        async with AsyncExitStack() as stack:
            await stack.enter_async_context(self._cond)
            if not self._check_connected():
                raise ConnectionClosedError(None, None)
            send_stack = stack.pop_all()

        async def send_with_lock() -> None:
            async with send_stack:
                assert self._ws is not None, f"Websocket to {self._url} is none"
                await self._ws.send(in_msg.to_json())

        msg = await self._wait_map.await_resp(in_msg.id, send_with_lock(), timeout)
        resp_type = self._req_resp_map[in_msg.msg_type]
        if msg.msg_type != resp_type:
            raise TypeError(f"Expected response of type {resp_type}, got type {msg.msg_type}")
        if msg.error is not None:
            raise ErrorMsgException(msg.error)
        return msg

    async def send(self, in_msg: Message, timeout: Optional[float] = None) -> JSONWizard:
        msg = await self.send_recv_raw(in_msg, timeout)
        if Empty == self._resp_map[msg.msg_type]:
            return Empty()
        assert msg.content is not None
        return self._resp_map[msg.msg_type].from_dict(msg.content)

import asyncio
import ssl
from contextlib import asynccontextmanager
from dataclasses import dataclass
from typing import TYPE_CHECKING, AsyncIterator, Awaitable, Callable, Dict, List, Optional, Set

from websockets import broadcast as ws_broadcast
from websockets.exceptions import ConnectionClosedError
from websockets.server import WebSocketServerProtocol, serve

from lib.common.logging import get_logger
from lib.common.messaging.message import ErrorMsg, ErrorMsgException, Message

LOG = get_logger(__name__)

Callback = Callable[[Message], Awaitable[Message]]
ConnCallback = Callable[[], Awaitable[List[Message]]]


@dataclass
class MsgWrapper:
    msg: Message
    ws: WebSocketServerProtocol


if TYPE_CHECKING:
    MSG_QUEUE = asyncio.Queue[MsgWrapper]
else:
    MSG_QUEUE = asyncio.Queue


class _Handler:
    def __init__(self, msg_type: str, msg_handler: Callback, resp_type: str) -> None:
        self.msg_type = msg_type
        self._msg_handler = msg_handler
        self._resp_type = resp_type
        self._queue: Optional[MSG_QUEUE] = None

    @property
    def queue(self) -> MSG_QUEUE:
        assert self._queue is not None
        return self._queue

    def set_resp_if_empty(self, resp_type: Optional[str]) -> None:
        if resp_type is not None and self._resp_type is None:
            self._resp_type = resp_type

    async def init(self) -> None:
        if self._queue is None:
            self._queue = asyncio.Queue()

    async def run(self, stop_event: asyncio.Event) -> None:
        read_queue = self.queue
        while not stop_event.is_set():
            try:
                msg_wrapper = await read_queue.get()
                try:
                    resp = await self._msg_handler(msg_wrapper.msg)
                except ErrorMsgException as e:
                    resp = Message.build_error(self._resp_type, error=e.error_msg)
                except Exception as e:
                    resp = Message.build_error(self._resp_type, error=ErrorMsg(msg=str(e)))
                resp.set_response_to(msg_wrapper.msg)
                await msg_wrapper.ws.send(resp.to_json())
            except Exception:
                LOG.exception(f"Unknown error processing msg for {self.msg_type}")


class Server:
    def __init__(self, port: int, req_resp_map: Dict[str, str] = {}, ssl: Optional[ssl.SSLContext] = None):
        self._port = port
        self._ssl = ssl
        self._handlers: Dict[str, _Handler] = {}
        self._queues: Dict[str, MSG_QUEUE] = {}
        self._req_resp_map = req_resp_map

        self._clients: Set[WebSocketServerProtocol] = set()
        self.__lock: Optional[asyncio.Lock] = None
        self._on_connect_cb: List[ConnCallback] = []
        self._running = False

    def register(self, msg_type: str, callback: Callback) -> None:
        assert not self._running
        self._handlers[msg_type] = _Handler(msg_type, callback, self._req_resp_map[msg_type])

    def on_connect(self, callback: ConnCallback) -> None:
        assert not self._running
        self._on_connect_cb.append(callback)

    async def __on_connect(self, websocket: WebSocketServerProtocol) -> None:
        async with self._lock:
            self._clients.add(websocket)

        if not self._on_connect_cb:
            return
        try:
            opt_connect_msgs: List[List[Message]] = await asyncio.gather(*[cb() for cb in self._on_connect_cb])
        except BaseException:
            LOG.exception(f"On connect failed for {websocket}")
            return
        for msg_list in opt_connect_msgs:
            for msg in msg_list:
                try:
                    await websocket.send(msg.to_json())
                except BaseException:
                    LOG.exception(f"Failed to send on connect msg {msg} to {websocket}")

    async def _process_msgs(self, websocket: WebSocketServerProtocol, _: str) -> None:
        await self.__on_connect(websocket)
        try:
            async for message in websocket:
                try:
                    msg = Message.from_json(message)
                except Exception as ex:
                    LOG.error(f"Failed to read msg err: {ex}")
                    continue
                if msg.msg_type in self._queues:
                    self._queues[msg.msg_type].put_nowait(MsgWrapper(msg=msg, ws=websocket))
                else:
                    if msg.msg_type in self._req_resp_map:
                        info = f"No handler defined for message type: {msg.msg_type}"
                        LOG.info(info)
                        resp = Message.build_error(self._req_resp_map[msg.msg_type], ErrorMsg(info, 404))
                        resp.set_response_to(msg)
                        await websocket.send(resp.to_json())
                    else:
                        LOG.error(f"No handler defined for message type: {msg.msg_type}, and no known response type")
        except ConnectionClosedError as ex:
            LOG.info(f"Client closed connection: {ex}")
        finally:
            async with self._lock:
                self._clients.discard(websocket)

    async def clients(self) -> Set[WebSocketServerProtocol]:
        async with self._lock:
            return self._clients.copy()

    async def client_ips(self) -> Set[str]:
        clients = await self.clients()
        return set([client.remote_address[0] for client in clients])

    async def broadcast(self, msg: Message) -> None:
        encoded = msg.to_json()
        clients = await self.clients()
        if clients:
            try:
                ws_broadcast(clients, encoded)
            except BaseException:
                LOG.exception(f"Failed to broadcast {msg}")

    @property
    def _lock(self) -> asyncio.Lock:
        assert self.__lock is not None
        return self.__lock

    @asynccontextmanager
    async def run(self) -> AsyncIterator[None]:
        if self.__lock is None:
            self.__lock = asyncio.Lock()
        stop_event = asyncio.Event()
        for handler in self._handlers.values():
            await handler.init()
        self._running = True
        tasks = [asyncio.get_event_loop().create_task(handler.run(stop_event)) for handler in self._handlers.values()]
        self._queues = {handler.msg_type: handler.queue for handler in self._handlers.values()}
        async with serve(self._process_msgs, "", self._port, ssl=self._ssl):
            yield
        stop_event.set()
        [task.cancel() for task in tasks]
        await asyncio.gather(*tasks)
        self._running = False

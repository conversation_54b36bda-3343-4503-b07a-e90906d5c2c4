import abc
from typing import Any, Dict, List, Union


class JsonSerializationException(Exception):
    pass


class JsonSerializable(abc.ABC):
    """
    Interface for to/from json.

    We assume the JSON will always be formatted as a Dict[str, Any].
    This is technically a subset of valid JSON, but is generally much easier to deal with.

    This class is designed to be a raw interface, with no behavior.
    """

    @abc.abstractmethod
    def to_json(self) -> Dict[str, Any]:
        pass

    @classmethod
    @abc.abstractmethod
    def from_json(cls, data: Dict[str, Any]) -> "JsonSerializable":
        pass

    @staticmethod
    def serialize(obj: "JsonSerializableTypeUnion") -> Union[str, int, float, Dict[str, Any], List[Any]]:
        if isinstance(obj, (str, int, float)):
            return obj
        elif isinstance(obj, list):
            return [JsonSerializable.serialize(v) for v in obj]
        elif isinstance(obj, JsonSerializable):
            return obj.to_json()
        else:
            raise JsonSerializationException(f"Could not serialize type: {type(obj)}")


JsonSerializableTypeUnion = Union[JsonSerializable, str, int, float]


class JsonBox:
    """
    Typing wrappers
    """

    def __init__(self, obj: JsonSerializableTypeUnion):
        self._obj = obj

    @property
    def obj(self) -> JsonSerializableTypeUnion:
        return self._obj

    @property
    def int_value(self) -> int:
        assert isinstance(self._obj, int)
        return self._obj

    @property
    def float_value(self) -> float:
        assert isinstance(self._obj, int)
        return self._obj

    @property
    def str_value(self) -> str:
        assert isinstance(self._obj, str)
        return self._obj

    @property
    def json_value(self) -> JsonSerializable:
        assert isinstance(self._obj, JsonSerializable)
        return self._obj

import base64
import os
from typing import Any

import cv2
import numpy.typing as npt


def _is_screen() -> bool:
    term = os.getenv("TERM")
    return term is not None and term.startswith("screen")


# tmux requires unrecognized OSC sequences to be wrapped with DCS tmux;
# <sequence> ST, and for all ESCs in <sequence> to be replaced with ESC ESC. It
# only accepts ESC backslash for ST. We use TERM instead of TMUX because TERM
# gets passed through ssh.
def _print_osc() -> None:
    if _is_screen():
        print("\033Ptmux;\033\033]", end="")
    else:
        print("\033]", end="")


# More of the tmux workaround described above.
def _print_st() -> None:
    if _is_screen():
        print("\a\033\\", end="")
    else:
        print("\a", end="")


def imgcat(img: npt.NDArray[Any], width: int = 80) -> None:
    """
    Displays images in iTerm2.  Expects image in CV2 BGR format.

    Based on https://www.iterm2.com/utilities/imgcat
    """
    ret, data = cv2.imencode(".png", img)
    assert ret != 0, "Failed to encode image"
    _print_osc()
    print("1337;File=inline=1;width={width}:".format(width=width), end="")
    print(base64.encodebytes(data.tobytes()).decode().replace("\n", "").replace("\r", ""), end="")
    _print_st()
    print()

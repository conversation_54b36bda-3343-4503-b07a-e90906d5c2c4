from typing import Any, Callable, List, Optional, <PERSON><PERSON>

import cv2
import numpy as np
import numpy.typing as npt

import lib.common.logging
from lib.common.fiducials.aruco_markers import ArucoMarkers
from lib.common.fiducials.charuco_diamonds import CharucoDiamonds
from lib.common.fiducials.fiducials import Fiducials

LOG = lib.common.logging.get_logger(__name__)


def _get_nearest_target_ids(
    fiducials: Fiducials, target: Tuple[int, int], last_frame_callback: Callable[[], Optional[npt.NDArray[Any]]]
) -> Optional[List[int]]:
    # See if we can find a target frame
    last_target_frame = last_frame_callback()
    if last_target_frame is None:
        return None

    # See if we can find a nearest marker
    # annotate thread so don't assert unique
    markers = fiducials.find_markers(last_target_frame, unique=False)
    target_ids = None
    if len(markers) > 0:
        the_diamonds = fiducials.find_diamonds(last_target_frame, markers)
        nearest = the_diamonds.get_nearest(target)
        if nearest is not None and len(nearest.ids) >= 4:
            target_ids = nearest.ids[:4].tolist()

    return target_ids


def find_draw_fiducials(
    fiducials: Fiducials,
    img: npt.NDArray[Any],
    diamonds: bool = True,
    target_callback: Optional[Callable[[], Any]] = None,
    last_target_frame_callback: Optional[Callable[[], Optional[npt.NDArray[Any]]]] = None,
    existing_markers: Optional[ArucoMarkers] = None,
    existing_diamonds: Optional[CharucoDiamonds] = None,
    ignore_small: bool = False,
) -> None:
    """Find the markers and diamonds on the given image and then draw them on the image"""
    if existing_markers is not None:
        markers = existing_markers
    else:
        # for annotations don't worry about them being unique. E.g. people might have multiple mats in view
        # or people might swap out mats while camera is running, and there might be frames with duplicates
        markers = fiducials.find_markers(img, unique=False, ignore_small=ignore_small)
    if len(markers) > 0:
        if existing_diamonds is not None:
            the_diamonds = existing_diamonds
        else:
            the_diamonds = (
                fiducials.find_diamonds(img, markers) if diamonds else CharucoDiamonds(corners=None, ids=None)
            )
        Fiducials.draw_markers(img, markers)
        if len(the_diamonds) > 0:
            Fiducials.draw_diamond_corners(img, the_diamonds)
            # TODO split below logic into separate function which can be annotated on / off independently
            # but then how do we share the markers/diamonds? Need better modeling of fiducial compute/storage
            if last_target_frame_callback is None:
                return

            assert target_callback is not None
            target = target_callback()
            if target is None:
                return

            target_ids = _get_nearest_target_ids(fiducials, target, last_target_frame_callback)
            if target_ids is None:
                return

            highlight_diamonds = the_diamonds.get_multiple(target_ids)
            if highlight_diamonds is None:
                return

            # Our corners are out of order for drawing
            pts = np.array(
                [
                    highlight_diamonds.corners[0],
                    highlight_diamonds.corners[1],
                    highlight_diamonds.corners[3],
                    highlight_diamonds.corners[2],
                ]
            )
            # We need to reshape for 1 polygon, X points, 2 ints per point (x, y)
            pts.reshape()
            pts_list = [np.int32(pts).reshape((-1, 1, 2))]
            # purple polygon
            cv2.polylines(img, pts=pts_list, isClosed=True, color=[255, 0, 255], thickness=4)


def draw_fiducials(img: npt.NDArray[Any], markers: Optional[ArucoMarkers], diamonds: Optional[CharucoDiamonds]) -> None:
    """Draw the given markers and diamonds on the given image"""
    if markers is not None and len(markers) > 0:
        Fiducials.draw_markers(img, markers)
        if diamonds is not None and len(diamonds) > 0:
            Fiducials.draw_diamond_corners(img, diamonds)

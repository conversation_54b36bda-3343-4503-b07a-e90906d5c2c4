from typing import Any, List, Optional, <PERSON><PERSON>

import cv2
import numpy as np
import numpy.typing as npt

import lib.common.logging
from lib.common.fiducials.aruco_markers import ArucoMarkers
from lib.common.fiducials.charuco_board import CHARUCO_MAT, CharucoBoard
from lib.common.fiducials.charuco_diamonds import CharucoDiamonds

LOG = lib.common.logging.get_logger(__name__)

try:
    from cv2 import aruco
except ImportError as e:
    LOG.error("cv2.aruco import error. Is your virtualenv setup properly?")
    LOG.exception(e)
    pass


class NoArucoMarkersFoundException(Exception):
    """
    Clients should throw this when they need to indicate an exceptional state as a result of no markers being found.
    It is generally up to the client to determine when it is appropriate to do so. Sometimes, it's reasonable for no
    markers to be found, in which case the client may not throw this, or catch this exception.
    """

    pass


class Fiducials:
    """
    The primary interface for finding fiducials. Uses the rest of the library.

    Helper functions for cv2.aruco
    """

    def __init__(self, aruco_dict: Optional[cv2.aruco.Dictionary] = None, charuco_board: CharucoBoard = CHARUCO_MAT):
        # ArUco marker dictionary
        self.aruco_dict = aruco_dict if aruco_dict is not None else charuco_board.markers_dict
        # grid of charuco diamonds

        self.charuco_board = charuco_board
        # Big seedy1 mat:
        # self.charuco_board = aruco.CharucoBoard_create(18, 27, 2, 1, self.aruco_dict)
        # charuco square lengths in real world units
        self.charuco_square_length_in = charuco_board.square_size_in
        self.charuco_square_length_mm = charuco_board.square_size_mm

    def find_markers(
        self,
        img: npt.NDArray[Any],
        params: Optional[cv2.aruco.DetectorParameters] = None,
        unique: bool = True,
        ignore_small: bool = False,
    ) -> ArucoMarkers:
        params = params if params is not None else self.get_aruco_parameters(np.array(img.shape), ignore_small)
        arucoDetector = aruco.ArucoDetector(self.aruco_dict, detectorParams=params)
        while params.minMarkerPerimeterRate < 1:
            corners, ids, rejected_img_points = arucoDetector.detectMarkers(img)
            result = ArucoMarkers(list(corners), ids)
            if unique:
                if len(result) != len(set(result.ids.flatten())):
                    params.minMarkerPerimeterRate = 1.2 * params.minMarkerPerimeterRate
                    LOG.warning(f"Increasing Min Perimeter rate: {params.minMarkerPerimeterRate}")
                    continue
            Fiducials.draw_markers(img, result)
            break
        return result

    def find_marker(
        self, img: npt.NDArray[Any], marker_id: int, ignore_small: bool = False, logging: bool = True
    ) -> Optional[ArucoMarkers]:
        arucoDetector = aruco.ArucoDetector(
            self.aruco_dict, detectorParams=self.get_aruco_parameters(np.array(img.shape), ignore_small)
        )
        corners, ids, rejected_img_points = arucoDetector.detectMarkers(img)
        if ids is None or len(ids) == 0 or corners is None or len(corners) == 0:
            # if we are in this function trying to find markers, then there should
            # be a fiducial mat down and we should always be able to find markers.
            # If we can't find any, then something is wrong. Hence, warning level
            # Can reduce to debug if we expect to call this function off the
            # fiducial mat.
            if logging:
                LOG.warning("Found no ArUco markers in given image.")
            return None
        m = ArucoMarkers(list(corners), ids)
        return m.get(marker_id)  # possibly None

    def find_diamonds(self, img: npt.NDArray[Any], markers: ArucoMarkers) -> CharucoDiamonds:
        if len(markers) == 0:
            # Without this check, cv2 can raise cv2.error with message:
            #
            # OpenCV(4.1.0) /io/opencv_contrib/modules/aruco/src/charuco.cpp:490:
            # error: (-215:Assertion failed) _markerCorners.total() == _markerIds.getMat().total()
            # && _markerIds.getMat().total() > 0 in function '_interpolateCornersCharucoLocalHom'
            #
            # https://github.com/opencv/opencv_contrib/blob/master/modules/aruco/src/charuco.cpp#L404
            return CharucoDiamonds(None, None)

        # don't swallow cv2.error. You can trace the code and guard against calling with bad inputs
        diamond_corners: npt.NDArray[Any]
        ret, diamond_corners, diamond_ids = aruco.interpolateCornersCharuco(
            markers.corners, markers.ids, img, self.charuco_board.board
        )

        return CharucoDiamonds(diamond_corners.tolist(), diamond_ids)

    def find_camera_lens_undistortion_parameters(
        self, diamond_corners: List[npt.NDArray[Any]], diamond_ids: List[npt.NDArray[Any]], image_size: Tuple[int, int]
    ) -> Tuple[Optional[int], Optional[npt.NDArray[Any]], Optional[npt.NDArray[Any]]]:
        camera_matrix_init = np.array(
            [[1000.0, 0.0, image_size[0] / 2.0], [0.0, 1000.0, image_size[1] / 2.0], [0.0, 0.0, 1.0]]
        )

        distortion_coefficients_init = np.zeros((14, 1))

        flags = (
            cv2.CALIB_RATIONAL_MODEL
            + cv2.CALIB_FIX_ASPECT_RATIO
            + cv2.CALIB_THIN_PRISM_MODEL
            + cv2.CALIB_FIX_TAUX_TAUY
            + cv2.CALIB_TILTED_MODEL
        )

        criteria = (cv2.TERM_CRITERIA_EPS & cv2.TERM_CRITERIA_MAX_ITER, 100000, 1e-9)

        try:
            ret, camera_matrix, distortion_coefficients, _, _, _, _, _ = aruco.calibrateCameraCharucoExtended(
                charucoCorners=diamond_corners,
                charucoIds=diamond_ids,
                board=self.charuco_board.board,
                imageSize=image_size,
                cameraMatrix=camera_matrix_init,
                distCoeffs=distortion_coefficients_init,
                flags=flags,
                criteria=criteria,
            )
        except Exception:
            LOG.warning("Camera calibration failed")
            return None, None, None

        return int(ret), camera_matrix, distortion_coefficients

    @staticmethod
    def find_lens_undistortion_mapping_parameters(
        image_shape: Tuple[int, int], camera_matrix: npt.NDArray[Any], distortion_coefficients: npt.NDArray[Any]
    ) -> Tuple[npt.NDArray[Any], npt.NDArray[Any]]:
        h, w = image_shape
        refined_camera_matrix, _ = cv2.getOptimalNewCameraMatrix(
            camera_matrix, distortion_coefficients, (w, h), 1, (w, h)
        )

        mapx, mapy = cv2.initUndistortRectifyMap(
            camera_matrix, distortion_coefficients, np.eye(3), refined_camera_matrix, (w, h), 5
        )

        mapping_parameters = (mapx, mapy)
        return mapping_parameters

    @staticmethod
    def get_aruco_parameters(shape: npt.NDArray[Any], ignore_small: bool = True) -> cv2.aruco.DetectorParameters:
        dp = aruco.DetectorParameters()
        # Enable marker corner refinement. This brought average marker corners error down from:
        # Before: Marker corners error: min: 0.04, mean: 1.30, max: 10.63, stdev: 0.92, count: 100
        # After:  Marker corners error: min: 0.00, mean: 0.70, max:  9.19, stdev: 0.53, count: 100
        dp.cornerRefinementMethod = aruco.CORNER_REFINE_CONTOUR
        # Magic Factor number based on minimum camera resolution circumference
        if ignore_small:
            factor = 2240 / sum(shape)
            dp.minMarkerPerimeterRate = 0.2 * factor
        else:
            factor = 3120 / sum(shape)
            dp.minMarkerPerimeterRate = 0.1 * factor
        return dp

    @staticmethod
    def draw_markers(img: npt.NDArray[Any], markers: ArucoMarkers) -> None:
        if len(markers.ids) > 0:
            aruco.drawDetectedMarkers(img, markers.corners, markers.ids)

    @staticmethod
    def draw_diamonds(img: npt.NDArray[Any], diamonds: CharucoDiamonds, draw_ids: bool = True) -> None:
        if len(diamonds.ids) > 0:
            aruco.drawDetectedDiamonds(img, diamonds.corners, diamonds.ids if draw_ids else None)

    @staticmethod
    def draw_diamond_corners(img: npt.NDArray[Any], diamonds: CharucoDiamonds) -> None:
        aruco.drawDetectedCornersCharuco(img, np.array(diamonds.corners), diamonds.ids, (0, 0, 255))

from copy import deepcopy
from typing import Any, Dict, Iterable, List, Optional, Set, Tuple, cast

import numpy as np
import numpy.typing as npt

import lib.common.logging
from lib.common.fiducials.uniformity import FiducialUniform<PERSON>hecker
from lib.common.math import distance_2d

LOG = lib.common.logging.get_logger(__name__)


class ArucoMarkers(FiducialUniformChecker):
    """A set of ArUco markers. This is a wrapper class for the marker corners, ids returned from aruco library."""

    def __init__(self, corners: List[npt.NDArray[Any]], ids: npt.NDArray[Any]):
        # don't store None because clients will check length by calling len(markers.ids)
        self.ids: npt.NDArray[Any] = ids if ids is not None else np.empty((0, 1), dtype=int)
        # yes this is really a List and the Diamonds one is a npt.NDArray[Any]. This is not a mistake.
        self.corners: List[npt.NDArray[Any]] = corners if corners is not None else []
        assert len(self.ids) == len(self.corners), "Mismatch size of ids (size=%d) and corners (size=%d) " % (
            len(self.ids),
            len(self.corners),
        )
        self._cache_id_to_index: Optional[Dict[int, int]] = None  # lazy cache

    def __len__(self) -> int:
        return len(self.ids)

    def _memoize_indices(self) -> Dict[int, int]:
        """Lazy initializer for _id_to_index map. This way we only compute the cache if we need it."""
        if self._cache_id_to_index is None:
            self._cache_id_to_index = {int(id_): i for i, id_ in enumerate(self.ids)}
        return self._cache_id_to_index

    def get(self, marker_id: int) -> Optional["ArucoMarkers"]:
        """Get the given Markers corner, or None if not present"""
        index = self._memoize_indices().get(int(marker_id))
        if index is None:
            return None

        corners = self.corners[index]
        id_ = self.ids[index]
        return ArucoMarkers([corners], np.array(id_))

    def primary_corner(self, corners: npt.NDArray[Any]) -> npt.NDArray[Any]:
        return cast(npt.NDArray[Any], corners[0])

    @staticmethod
    def get_centroid(four_corners: npt.NDArray[Any]) -> npt.NDArray[Any]:
        """
        Get the centroid of the given four corners.
        """
        assert four_corners.shape == (4, 2)

        # Centroid of finite set of points is the sum of the points divided by the number of points
        # https://en.wikipedia.org/wiki/Centroid#Of_a_finite_set_of_points
        return cast(npt.NDArray[Any], np.mean(four_corners, axis=0))

    @staticmethod
    def intersect_ids(markers: List["ArucoMarkers"]) -> Set[int]:
        assert len(markers) > 0
        # intersect the first set with all the others
        common_markers = set(markers[0].ids.flatten()).intersection(*[list(m.ids.flatten()) for m in markers])
        return common_markers

    @staticmethod
    def get_all(id_: int, markers: List["ArucoMarkers"]) -> List[Optional["ArucoMarkers"]]:
        """
        Get marker with given id in each of the given markers
        """
        return [m.get(id_) for m in markers]

    @staticmethod
    def mean(markers: Iterable["ArucoMarkers"]) -> npt.NDArray[Any]:
        """
        Average marker with given id across the given markers, returning the corners as a numpy array
        """
        all_points = []
        for m in markers:
            assert len(m) == 1, "Can only take mean of markers wrapping one markers"
            all_points.append(m.corners[0][0])
        return cast(npt.NDArray[Any], np.mean(all_points, axis=0))

    @staticmethod
    def errors(markers_list: Iterable["ArucoMarkers"], markers_mean: npt.NDArray[Any]) -> npt.NDArray[Any]:
        """
        Compute the error = sqrt((x-x')^2 + (y-y')^2) of each of the given markers to the given markers mean.
        """
        for m in markers_list:
            assert len(m) == 1, "This API only meant to work for markers wrapping a single ArUco marker"
        # reduce (x, y) coordinate pairs to errors = sqrt(x_delta^s + y_delta^2)
        corners_deltas = [np.subtract(m.corners[0][0], markers_mean) for m in markers_list]
        return np.array([np.sqrt(np.sum(a * a, axis=1)) for a in corners_deltas])

    def get_nearest(self, loc: Tuple[float, float], corner: Optional[int] = None) -> Optional["ArucoMarkers"]:
        """
        Find the nearest fiducial to the given loc (x, y). If corner is left unspecified then
        it will find the nearest based on average distance to the four corners of the various
        fiducials. If corner is specified then it will only concern itself with the corner
        number requested.
        """
        min_distance = None
        nearest_fiducial_index = None
        for i, p in enumerate(self.corners):
            # p is now a numpy array (1, 4, 2)

            # fiducial_corners is a numpy array (4, 2) representing the four corners of the fiducial
            fiducial_corners = p[0]

            # Determine test point to compare distance against: either specified corner or the centroid
            test_point = fiducial_corners[corner] if corner is not None else ArucoMarkers.get_centroid(fiducial_corners)

            # compute distance
            d = distance_2d(loc, test_point)

            # update closest fiducial
            if min_distance is None or d < min_distance:
                min_distance = d
                nearest_fiducial_index = i

        # return nearest fiducial as a Markers instance
        if nearest_fiducial_index is not None:
            return ArucoMarkers(
                [self.corners[nearest_fiducial_index]], self.ids[nearest_fiducial_index].reshape((1, 1))
            )
        return None

    def merged(self, other: "ArucoMarkers") -> "ArucoMarkers":
        """
        Reutrn merged version of self and other.
        """
        ids = deepcopy(self.ids)
        corners = deepcopy(self.corners)
        for i in other.ids:
            if i not in ids:
                ids = np.append(ids, [i], axis=0)
                corners.append(cast("ArucoMarkers", other.get(i[0])).corners[0])
        return ArucoMarkers(corners, ids)

    def mean_marker_height(self) -> float:
        """
        Avarage mean height of all markers.
        """
        all_heights = 0
        num_heights = 0
        for c in self.corners:
            c = c[0]
            all_heights += abs(c[0][1] - c[2][1])
            num_heights += 1
        return all_heights / num_heights

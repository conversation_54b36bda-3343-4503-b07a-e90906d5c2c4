from copy import deepcopy
from typing import Any, Dict, Iterable, List, Optional, Set, Tuple, cast

import numpy as np
import numpy.typing as npt

from lib.common.fiducials.uniformity import FiducialUniformChecker
from lib.common.logging import get_logger
from lib.common.math import deg2rad, distance_2d

LOG = get_logger(__name__)

# 10 degrees; threshold at which three points are considered approx. colinear
_COLINEAR_THRESHOLD_RADIANS: float = deg2rad(10)


CornersType = List[Any]


class DiamondSizeSummary:
    def __init__(self, hori_distances: List[float], vert_distances: List[float]) -> None:
        self.max = (np.max(hori_distances), np.max(vert_distances))
        self.min = (np.min(hori_distances), np.min(vert_distances))
        self.mean = (np.mean(hori_distances), np.mean(vert_distances))
        self.median = (np.median(hori_distances), np.median(vert_distances))
        self.std_dev = (np.std(hori_distances), np.std(vert_distances))
        self.p10 = (np.percentile(hori_distances, 10), np.percentile(vert_distances, 10))
        self.p90 = (np.percentile(hori_distances, 90), np.percentile(vert_distances, 90))

    def __repr__(self) -> str:
        return "\n".join(
            [
                "--------------------------",
                f"Max: {self.max}",
                f"Min: {self.min}",
                f"Mean: {self.mean}",
                f"Median: {self.median}",
                f"Std Dev: {self.std_dev}",
                f"P10: {self.p10}",
                f"P90: {self.p90}",
                "--------------------------",
            ]
        )


class CharucoDiamonds(FiducialUniformChecker):
    """A set of ChArUco diamonds. This is a wrapper class for the diamond corners, ids returned from aruco library."""

    def __init__(self, corners: Optional[CornersType], ids: Optional[npt.NDArray[Any]]):
        self.ids = ids if ids is not None else np.empty((0, 1), dtype=int)
        # TODO This takes on two types: List and npt.NDArray[Any] - can probably be cleaned up
        self.corners: CornersType = corners if corners is not None else []
        assert len(self.ids) == len(self.corners), "Mismatch size of ids (size=%d) and corners (size=%d) " % (
            len(self.ids),
            len(self.corners),
        )
        self._cache_id_to_index: Optional[Dict[int, int]] = None  # lazy cache

    def __len__(self) -> int:
        return len(self.ids)

    def _memoize_indices(self) -> Dict[int, int]:
        """Lazy initializer for _id_to_index map. This way we only compute the cache if we need it."""
        if self._cache_id_to_index is None:
            self._cache_id_to_index = {int(id_): i for i, id_ in enumerate(self.ids)}
        return self._cache_id_to_index

    def diamond_size(self, cell_per_row: int = 47) -> DiamondSizeSummary:
        id_to_corner = {}

        def get_vert_nearby_ids(id: int) -> List[int]:
            ret_val = [id + cell_per_row, id - cell_per_row]
            return ret_val

        def get_hori_nearby_ids(id: int) -> List[int]:
            ret_val = []
            if (id + 1) % cell_per_row != 0:
                ret_val.append(id + 1)
            if id % cell_per_row != 0:
                ret_val.append(id - 1)
            return ret_val

        for i, npid in enumerate(self.ids):
            id = npid[0]
            corner = self.corners[i]
            id_to_corner[id] = corner

        vert_distances = []
        hori_distances = []

        for npid in self.ids:
            id = npid[0]
            vert_ids = get_vert_nearby_ids(id)
            for nid in vert_ids:
                if nid in id_to_corner:
                    diff = np.sqrt(np.sum(np.square(id_to_corner[id] - id_to_corner[nid])))
                    vert_distances.append(float(diff))
            hori_ids = get_hori_nearby_ids(id)
            for nid in hori_ids:
                if nid in id_to_corner:
                    diff = np.sqrt(np.sum(np.square(id_to_corner[id] - id_to_corner[nid])))
                    hori_distances.append(float(diff))

        return DiamondSizeSummary(hori_distances, vert_distances)

    def get(self, corner_id: int) -> Optional["CharucoDiamonds"]:
        """Get the given Diamond corner, or None if not present"""
        assert corner_id is not None
        index = self._memoize_indices().get(int(corner_id))
        if index is None:
            return None

        corner = self.corners[index]
        id_ = self.ids[index]
        return CharucoDiamonds([corner], np.array(id_))

    def get_multiple(self, corner_ids: List[int]) -> Optional["CharucoDiamonds"]:
        """Just like get, but does this for a list of ids"""
        assert corner_ids is not None
        corners = None
        ids = []

        for id in corner_ids:
            index = self._memoize_indices().get(int(id))
            if index is None:
                return None
            ids.append(self.ids[index])
            if corners is None:
                corners = list(np.array(self.corners[index]))
            else:
                corners = list(np.append(corners, self.corners[index], axis=0))

        return CharucoDiamonds(corners, np.array(ids))

    def get_all(self, corner_ids: List[int]) -> Optional["CharucoDiamonds"]:
        ids_list = []
        corners_list = []
        for corner_id in corner_ids:
            d = self.get(corner_id)
            if d is not None:
                ids_list.append(d.ids[0])
                corners_list.append(d.corners[0])

        if len(ids_list) == 0:
            return None
        return CharucoDiamonds(corners_list, np.array(ids_list))

    @staticmethod
    def mean(diamonds: Iterable["CharucoDiamonds"]) -> npt.NDArray[Any]:
        """
        Average marker with given id across the given markers, returning the corners as a numpy array
        """
        return cast(npt.NDArray[Any], np.mean([d.corners[0][0] for d in diamonds], axis=0))

    @staticmethod
    def errors(diamonds_list: Iterable["CharucoDiamonds"], diamonds_mean: npt.NDArray[Any]) -> npt.NDArray[Any]:
        """
        Compute the error = sqrt((x-x')^2 + (y-y')^2) of each of the given markers to the given markers mean.
        """
        for d in diamonds_list:
            assert len(d) == 1, "This API only meant to work for diamonds wrapping a single ChArUco corner"
        # reduce (x, y) coordinate pairs to errors = sqrt(x_delta^s + y_delta^2)
        corners_deltas = [np.subtract(d.corners[0][0], diamonds_mean) for d in diamonds_list]
        return np.array([np.sqrt(np.sum(a * a)) for a in corners_deltas])

    @staticmethod
    def intersect_ids(diamonds_list: List["CharucoDiamonds"]) -> Set[int]:
        assert len(diamonds_list) > 0
        # intersect the first set with all the others
        common_ids = set(diamonds_list[0].ids.flatten()).intersection(*[list(d.ids.flatten()) for d in diamonds_list])
        return common_ids

    def get_nearest(self, loc: Tuple[float, float]) -> Optional["CharucoDiamonds"]:
        """
        Find the nearest four corners to the given loc (x, y). Avoid choosing any three points which are colinear.
        """
        if len(self) < 4:
            return None

        # The if/else logic for finding top 4 closest in a single pass is quite difficult to write.
        # So we will compute all distances, sort, and the take closest 4
        distances: List[float] = [distance_2d(loc, c[0]) for c in self.corners]

        corners_list = []
        ids: List[int] = []

        for i, d in sorted(enumerate(distances), key=lambda x: x[1]):
            if len(ids) == 4:
                break

            corner = self.corners[i][0]

            #
            # Base Case: Always add first two points since two points cannot form a collinear triangle
            #
            if len(ids) < 2:
                # First two points always add
                ids.append(self.ids[i])
                corners_list.append(corner)
                continue

            #
            # Inductive Case: Third, Fourth point means the set of points has triangles amongst it
            # So, we must validate none of the triangles that can be formed by these 3 or 4 points
            # are too flat, which would result in a bad "diamond" result from this function.
            #

            # build set of triangles that can be formed with the corner and two existing points chosen
            triangles = []
            for j in range(len(ids)):
                for k in range(j + 1, len(ids)):
                    triangles.append(np.array([corner, corners_list[j], corners_list[k]]))

            # validate next point to be added
            # check that we are not adding a colinear point
            valid = True
            for tr in triangles:
                # check each triangle to ensure it is not too flat
                if CharucoDiamonds.is_collinear(tr):
                    valid = False
                    break

            if valid:
                # it is ok to add this point. We do not create any "flat" triangles by adding this point to the set
                ids.append(self.ids[i])
                corners_list.append(corner)
            # else the points would be too flat, so try a different point in the outer loop

        if len(ids) < 4:
            # There were at least 4 points total but three were co-linear
            return None

        # now sort them by corner_id to match same as how we get them back from aruco library
        # (nothing should depend on this but it is simple to maintain parity in case it is useful later)
        corners_list = [corners_list[i] for i, _ in sorted(enumerate(ids), key=lambda x: x[1])]
        ids = sorted(ids)

        return CharucoDiamonds(corners_list, np.array(ids))

    @staticmethod
    def is_collinear(points: npt.NDArray[Any]) -> bool:
        """Return true if and only if the given set of three points are approximately collinear."""
        assert points.shape == (3, 2), "Expected shape (3, 2) but got: %s" % points.shape

        a = distance_2d(points[0], points[1])
        assert a > 0, "Bug detected. distance from %s to %s is 0" % (points[0], points[1])
        b = distance_2d(points[0], points[2])
        assert b > 0, "Bug detected. distance from %s to %s is 0" % (points[0], points[2])
        c = distance_2d(points[1], points[2])
        assert c > 0, "Bug detected. distance from %s to %s is 0" % (points[1], points[2])

        # skip if triangle is a straight line
        # This also protects against rounding errors
        if a + b + c - 2 * max(a, b, c) < min(a, b, c) / 100:
            return True

        aa = a * a
        bb = b * b
        cc = c * c

        # Use law of cosigns to derive angles
        # angles opposite sides (A is opposite of side a)

        # a^2 = b^2 + c^2 - 2 * b * c * cos(A)
        A = np.arccos((bb + cc - aa) / (2 * b * c))
        assert not np.isnan(A), "Computed A = nan, a = %s, b = %s, c = %s, aa = %s, bb = %s, cc = %s" % (
            a,
            b,
            c,
            aa,
            bb,
            cc,
        )

        # b^2 = a^2 + c^2 - 2 * a * c * cos(B)
        B = np.arccos((cc + aa - bb) / (2 * c * a))
        assert not np.isnan(B), "Computed B = nan, a = %s, b = %s, c = %s, aa = %s, bb = %s, cc = %s" % (
            a,
            b,
            c,
            aa,
            bb,
            cc,
        )

        # c^2 = b^2 + b^2 - 2 * a * b * cos(C)
        C = np.arccos((aa + bb - cc) / (2 * a * b))
        assert not np.isnan(C), "Computed C = nan, a = %s, b = %s, c = %s, aa = %s, bb = %s, cc = %s" % (
            a,
            b,
            c,
            aa,
            bb,
            cc,
        )

        assert abs(A + B + C - np.pi) < 0.001, "Bug detected. Bad angles computed. A + B + C = %.2f" % (A + B + C)

        # skip if this creates three colinear points
        min_angle = min(A, B, C)
        if min_angle < _COLINEAR_THRESHOLD_RADIANS:
            return True

        return False

    def primary_corner(self, corners: npt.NDArray[Any]) -> npt.NDArray[Any]:
        return corners

    def merged(self, other: "CharucoDiamonds") -> "CharucoDiamonds":
        """
        Reutrn merged version of self and other.
        """
        ids = deepcopy(self.ids)
        corners = deepcopy(self.corners)
        for i in other.ids:
            if i not in ids:
                ids = np.append(ids, [i], axis=0)
                corners.append(cast("CharucoDiamonds", other.get(i[0])).corners[0])
        return CharucoDiamonds(corners, ids)

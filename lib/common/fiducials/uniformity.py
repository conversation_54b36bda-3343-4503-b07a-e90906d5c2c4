import abc
from typing import Any, List, Optional

import numpy as np
import numpy.typing as npt

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)


class FiducialUniformChecker(abc.ABC):
    ids: npt.NDArray[Any]

    @abc.abstractmethod
    def primary_corner(self, corners: npt.NDArray[Any]) -> npt.NDArray[Any]:
        pass

    @abc.abstractmethod
    def get(self, id: int) -> Any:
        pass

    def is_row_major(self) -> Optional[bool]:
        """
        Decide based on xy locations if these fiducials are laid out in row major or column major order on the screen.
        """

        # Find the first two fiducials that are adjacently existing
        last = None
        next = None
        for id in range(int(min(self.ids)), int(max(self.ids) + 1)):
            # Not there, so keep going
            if not self.get(id):
                continue

            # First one we've seen
            if last is None:
                last = id
            else:
                # If they are in order then remember them and break
                if id == last + 1:
                    next = id
                    break
                # not in order, start looking from this one then
                else:
                    last = id

        # If we never found two in a row use None to deliver that
        if next is None or last is None:
            return None

        last_corner = self.primary_corner(np.array(self.get(last).corners).squeeze())
        next_corner = self.primary_corner(np.array(self.get(next).corners).squeeze())
        # ids in rows will move out in the x directions, ids in columns in the y direction
        if abs(last_corner[0] - next_corner[0]) > abs(last_corner[1] - next_corner[1]):
            return True
        return False

    def is_uniform(self) -> bool:
        """Validate this these fiducials are regularly spaced within threshold std deviations."""

        # Future - we should also check the y offsets. We currently only do this row wise and check x

        # Decide which way this mat is laid out and which corner index we should be comparing
        row_major = self.is_row_major()
        if row_major is None:
            return False

        if row_major:
            corner_id = 0
        else:
            corner_id = 1

        ids: List[int] = []

        # Last marker seen and comparative x direction
        last_xy = None
        # Last direction calculated between the previous two markers
        last_dir = None

        for id in range(int(min(self.ids)), int(max(self.ids) + 1)):
            # see if we can even see the next marker. If the mat is bigger than the camera we will have
            # gaps in markers sequence space. This is fin.e
            m = self.get(id)
            if m is None:
                continue

            corner = self.primary_corner(np.array(m.corners).squeeze())

            # First element seen, nothing to compare yet
            if last_xy is None:
                last_xy = corner
                continue

            # Which direction are we headed in x space
            dir = corner[corner_id] - last_xy[corner_id]

            # After second element we can get comparitive direction
            if last_dir is None:
                last_dir = dir
                last_xy = corner
                continue

            # If we are at the end of the row, validate it and then reset for a new row. Check for new row
            # by seeing if we just switched x direction (we went back to the begging of the next row).
            if (dir > 0 and last_dir < 0) or (dir < 0 and last_dir > 0) or (id == int(max(self.ids))):
                # The max gap in a row of fiducial ids should be 1
                if len(ids) > 1 and max([abs(ids[i] - ids[i - 1]) for i in range(1, len(ids))]) > 1:
                    return False
                # Reset all row tracking state
                ids = []
                last_dir = None
                last_xy = None
            else:
                # Update row tracking state
                last_dir = dir
                ids.append(id)
                last_xy = corner
        return True

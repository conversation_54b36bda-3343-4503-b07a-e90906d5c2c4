import math
from argparse import Namespace
from typing import Any, Dict, Tuple, cast

import cv2
import numpy as np
import numpy.typing as npt
from cv2 import aruco

import lib.common.logging
from lib.common.math import floordiv_tuple

LOG = lib.common.logging.get_logger(__name__)

ARUCO_DICTS: Dict[Tuple[int, int], int] = {
    (4, 50): cv2.aruco.DICT_4X4_50,
    (4, 100): cv2.aruco.DICT_4X4_100,
    (4, 250): cv2.aruco.DICT_4X4_250,
    (4, 1000): cv2.aruco.DICT_4X4_1000,
    (5, 50): cv2.aruco.DICT_5X5_50,
    (5, 100): cv2.aruco.DICT_5X5_100,
    (5, 250): cv2.aruco.DICT_5X5_250,
    (5, 1000): cv2.aruco.DICT_5X5_1000,
    (6, 50): cv2.aruco.DICT_6X6_50,
    (6, 100): cv2.aruco.DICT_6X6_100,
    (6, 250): cv2.aruco.DICT_6X6_250,
    (6, 1000): cv2.aruco.DICT_6X6_1000,
    (7, 50): cv2.aruco.DICT_7X7_50,
    (7, 100): cv2.aruco.DICT_7X7_100,
    (7, 250): cv2.aruco.DICT_7X7_250,
    (7, 1000): cv2.aruco.DICT_7X7_1000,
}


def get_aruco_dict(marker_bits: int, n_markers: int) -> cv2.aruco.Dictionary:
    for size in [50, 100, 250, 1000]:
        if n_markers <= size and (marker_bits, size) in ARUCO_DICTS:
            return cv2.aruco.getPredefinedDictionary(ARUCO_DICTS[marker_bits, size])
    return cv2.aruco.extendDictionary(n_markers, marker_bits)


class CharucoBoard:
    def __init__(
        self, res_x: int, res_y: int, pixels_per_bit: int, bits: int, ratio: Tuple[float, float], diagonal_size: float
    ):
        self._res_x: int = res_x
        self._res_y: int = res_y
        self._pixels_per_bit: int = pixels_per_bit
        self._bits: int = bits
        self._ratio = ratio
        self._diagonal_size = diagonal_size

        self._pixels_per_square = pixels_per_bit * (bits + 2) * ratio[0] / ratio[1]
        self._squares_x = int(res_x / self._pixels_per_square)
        self._squares_y = int(res_y / self._pixels_per_square)
        n_markers = int(self._squares_x * self._squares_y / 2)
        self._square_size_in: float = math.sqrt(
            (diagonal_size ** 2) / (1 + ((self._res_x / self._res_y) ** 2))
        ) / self._squares_y
        self._markers_dict: cv2.aruco.Dictionary = get_aruco_dict(bits, n_markers)
        self._board = cv2.aruco.CharucoBoard((self._squares_x, self._squares_y), ratio[0], ratio[1], self._markers_dict)

    @property
    def resolution(self) -> Tuple[int, int]:
        return self._res_x, self._res_y

    @property
    def board(self) -> cv2.aruco.CharucoBoard:
        return self._board

    @property
    def markers_dict(self) -> cv2.aruco.Dictionary:
        return self._markers_dict

    @property
    def square_size_in(self) -> float:
        return self._square_size_in

    @property
    def square_size_mm(self) -> float:
        return self._square_size_in * 25.4

    @property
    def squares_x(self) -> int:
        return self._squares_x

    @property
    def squares_y(self) -> int:
        return self._squares_y

    @property
    def pixels_per_square(self) -> float:
        return self._pixels_per_square

    @property
    def image(self) -> npt.NDArray[Any]:
        im = self._board.generateImage(self.resolution)
        return cast(npt.NDArray[Any], cv2.cvtColor(im, cv2.COLOR_GRAY2BGR)[:, 0 : im.shape[1], :])

    def export_board_args(self, args: Namespace) -> None:
        args.board_res_x = self._res_x
        args.board_res_y = self._res_y
        args.board_pixels_per_bit = self._pixels_per_bit
        args.board_bits_per_marker = self._bits
        args.board_ratio = self._ratio
        args.board_diagonal_size = self._diagonal_size

    @staticmethod
    def create_with_default(
        res_x: int = 0,
        res_y: int = 0,
        pixels_per_bit: int = 0,
        bits: int = 0,
        ratio: Tuple[float, float] = (8, 6),
        diagonal_size: float = 54.6,
    ) -> "CharucoBoard":
        if pixels_per_bit == 0:
            # Current Board for printed Mats, kept for backward compatibility
            return CHARUCO_MAT
        return CharucoBoard(res_x, res_y, pixels_per_bit, bits, ratio, diagonal_size)

    @staticmethod
    def create_from_args(args: Namespace) -> "CharucoBoard":
        return CharucoBoard.create_with_default(
            res_x=args.board_res_x,
            res_y=args.board_res_y,
            pixels_per_bit=args.board_pixels_per_bit,
            bits=args.board_bits_per_marker,
            ratio=args.board_ratio,
            diagonal_size=args.board_diagonal,
        )


# Old Mat still in use
CHARUCO_MAT = CharucoBoard(512, 640, 1, 6, (2, 1), 44.82)


def generate_aruco_marker_image(
    resolution: Tuple[int, int],
    aruco_id: int = 19,
    fill_pct: float = 0.25,
    x_center: float = 0.5,
    y_center: float = 0.5,
) -> npt.NDArray[Any]:
    """
    :param resolution: img resolution to generate
    :param fill_pct: How much of the minimum resolution axis to fill with aruco
    :param x_center: If there is extra x space, how to center it left vs right.
    :param y_center: If there is extra y space, how to center it top vs bottom.
    :return: generated img.
    """
    assert 0 < fill_pct <= 1
    assert 0 <= x_center <= 1
    assert 0 <= y_center <= 1
    marker_img = aruco.generateImageMarker(
        dictionary=aruco.getPredefinedDictionary(aruco.DICT_6X6_100),
        id=aruco_id,
        sidePixels=int(min(resolution) * fill_pct),
        borderBits=1,
    )
    marker_img_greyscale = cv2.cvtColor(marker_img, cv2.COLOR_GRAY2BGR)

    # grey
    result = np.full(shape=(resolution[1], resolution[0], 3), fill_value=128, dtype=np.uint8)

    # center marker in drive resolution
    extra_width = result.shape[0] - marker_img.shape[0]
    # HMM - I had to reverse x_center vs y_center compared with my expectations?
    marker_x_start = int(extra_width * y_center)
    marker_x_end = marker_x_start + marker_img_greyscale.shape[0]

    extra_height = result.shape[1] - marker_img.shape[1]
    marker_y_start = int(extra_height * x_center)
    marker_y_end = marker_y_start + marker_img_greyscale.shape[1]

    result[marker_x_start:marker_x_end, marker_y_start:marker_y_end, :] = marker_img_greyscale

    return result


def generate_cropped_charuco_image(
    resolution: Tuple[int, int], cropped_pct: float = 0.5, x_center: float = 0.5, y_center: float = 0.5
) -> npt.NDArray[Any]:
    """
    Generate a cropped ChArUco image
    :param resolution: output image resolution
    :return: generated image
    """
    assert 0 < cropped_pct < 1
    assert 0 <= x_center <= 1
    assert 0 <= y_center <= 1

    charuco_board = aruco.CharucoBoard((32, 40), 2, 1, aruco.getPredefinedDictionary(aruco.DICT_6X6_1000))

    board_resolution = tuple(map(int, floordiv_tuple(resolution, cropped_pct)))
    charuco_img_2d = charuco_board.generateImage(board_resolution, marginSize=5)
    charuco_img_greyscale = cv2.cvtColor(charuco_img_2d, cv2.COLOR_GRAY2BGR)[:, 0 : charuco_img_2d.shape[1], :]

    # grey
    result = np.full(shape=(resolution[1], resolution[0], 3), fill_value=128, dtype=np.uint8)

    # center crop in resolution
    extra_width = charuco_img_greyscale.shape[0] - result.shape[0]
    # HMM - I had to reverse x_center vs y_center compared with my expectations?
    crop_x_start = int(extra_width * y_center)
    crop_x_end = crop_x_start + result.shape[0]

    extra_height = charuco_img_greyscale.shape[1] - result.shape[1]
    crop_y_start = int(extra_height * x_center)
    crop_y_end = crop_y_start + result.shape[1]

    cropped_greyscale = charuco_img_greyscale[crop_x_start:crop_x_end, crop_y_start:crop_y_end, :]
    # Make the picture dim so we can still detect the laser
    return cast(npt.NDArray[Any], cropped_greyscale)

import contextlib
import math
import os
from io import BufferedWriter
from pathlib import Path
from typing import Generator, Union

_DEFAULT_PRECISION: int = 1


def sizeof_fmt(num: int, precision: int = _DEFAULT_PRECISION) -> str:
    """
    Format a given numerical value as a human-readable string
    :return:
    """
    assert num >= 0
    assert 0 <= precision <= 3
    if num <= 1024:
        return "{}B".format(num)
    magnitude = int(math.floor(math.log(num, 1024)))
    val = num / math.pow(1024, magnitude)
    if magnitude > 7:
        return ("{:.%df}{}" % precision).format(val, "YB")
    return ("{:3.%df}{}{}" % precision).format(val, ["", "K", "M", "G", "T", "P", "E", "Z"][magnitude], "B")


def getsize_formatted(filepath: str, precision: int = _DEFAULT_PRECISION) -> str:
    return sizeof_fmt(os.path.getsize(filepath), precision=precision)


def repo_root() -> Path:
    # the directory of this file you are reading
    current_file_dir = Path(__file__).parent.absolute()
    lib_common_dir = current_file_dir
    return Path(os.path.normpath(os.path.join(lib_common_dir, "../..")))


def repo_relpath(path: Union[str, Path]) -> Path:
    return Path(os.path.relpath(Path(path), repo_root()))


@contextlib.contextmanager
def open_file_descriptor(path: Union[str, Path], flags: int) -> Generator[int, None, None]:
    """
    Open a file by path, returning the underlying file descriptor
    """
    fd = os.open(path, flags)
    try:
        yield fd
    finally:
        os.close(fd)


@contextlib.contextmanager
def atomic_file_binary(file_path: Union[str, Path]) -> Generator[BufferedWriter, None, None]:
    """
    Open a file in such a way that when closed, it is atomically written to the desired location
    """
    temp_file_path = str(file_path) + ".tmp"
    with open(temp_file_path, "wb") as temp_file:
        yield temp_file
        temp_file.flush()
        os.fsync(temp_file.fileno())

    os.rename(temp_file_path, file_path)

    if hasattr(os, "O_DIRECT"):
        with open_file_descriptor(file_path, os.O_RDWR | os.O_DIRECT) as fd:
            os.fsync(fd)

    with open_file_descriptor(os.path.dirname(file_path) or ".", os.O_DIRECTORY) as fd:
        os.fsync(fd)

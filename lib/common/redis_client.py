import asyncio
from typing import Any, Dict, cast

import aioredis

from lib.common.generation import get_command_ip
from lib.common.logging import get_logger

LOG = get_logger(__name__)


async def wait_for_redis() -> None:
    LOG.info("Waiting for redis ...")
    c = RedisClient()
    await c.connect()
    await c.client.close()
    LOG.info("Redis is up.")


class RedisClient:
    @staticmethod
    async def build(decode_responses: bool = True) -> aioredis.Redis:
        host = get_command_ip()
        return cast(
            aioredis.Redis,
            await aioredis.from_url(
                f"redis://{host}:6379", password="T2Yw28ctIZ3gwI1t", decode_responses=decode_responses
            ),
        )

    async def wait_until_ready(self) -> None:
        while True:
            try:
                await self._redis.ping()
                break
            except Exception:
                LOG.warning("Awaiting connection to Redis")
                await asyncio.sleep(0.1)

    async def connect(self) -> None:
        self._redis = await RedisClient.build()
        await self.wait_until_ready()

    async def get(self, key: str) -> Any:
        val = await self._redis.get(key)
        return val

    async def get_def(self, key: str, def_: str) -> Any:
        val = await self.get(key)
        if val is None:
            return def_
        return val

    async def set(self, key: str, val: str) -> None:
        await self._redis.set(key, val)

    async def hmset(self, key: str, vals: Dict[str, str]) -> None:
        await self._redis.hmset(key, vals)

    async def hgetall(self, key: str) -> Dict[str, str]:
        return cast(Dict[str, str], await self._redis.hgetall(key))

    @property
    def client(self) -> aioredis.Redis:
        return self._redis

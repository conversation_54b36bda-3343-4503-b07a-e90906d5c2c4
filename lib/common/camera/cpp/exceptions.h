#pragma once

#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace common {
namespace camera {

class camera_error : public maka_error {
public:
  explicit camera_error(const std::string &what, bool include_stacktrace = true, size_t stack_skip = 1)
      : maka_error(what, include_stacktrace, stack_skip + 1) {}
};

class grab_timeout_error : public camera_error {
public:
  explicit grab_timeout_error(const std::string &what, size_t stack_skip = 1)
      : camera_error(what, false, stack_skip + 1) {}
};

class grab_incomplete_error : public camera_error {
public:
  explicit grab_incomplete_error(const std::string &what, size_t stack_skip = 1)
      : camera_error(what, false, stack_skip + 1) {}
};

} // namespace camera
} // namespace common
} // namespace lib
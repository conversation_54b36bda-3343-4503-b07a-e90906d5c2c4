#pragma once

#include <functional>
#include <memory>
#include <shared_mutex>
#include <spdlog/spdlog.h>
#include <thread>

#include "camera_image.h"
#include "camera_info.h"
#include "camera_settings.h"
#include "cv/runtime/cpp/config/from_config_tree.h"
#include <config/tree/cpp/config_tree.hpp>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>

namespace lib {
namespace common {
namespace camera {

class CameraImpl {
public:
  CameraImpl() = delete;
  CameraImpl(const CameraInfo &info, const CameraSettings &settings,
             std::shared_ptr<carbon::config::ConfigTree> camera_config)
      : settings_(settings), camera_config_(camera_config), info_(info) {}
  virtual ~CameraImpl() = default;

  const CameraInfo &get_info() const { return info_; }
  const CameraSettings &get_settings() const { return settings_; }
  PixelFormat get_pixel_format() const { return pixel_format_; }
  int get_width() const { return width_; }
  int get_height() const { return height_; }
  virtual void start_grabbing() = 0;
  virtual CameraImage grab() = 0;
  virtual void stop_grabbing() = 0;
  virtual int64_t update_settings(const CameraSettings &settings) = 0;
  virtual double get_temperature() = 0;
  virtual void sync_settings() = 0;
  void set_auto_whitebalance(bool enable) {
    auto settings = get_settings();
    settings.auto_whitebalance = enable;
    update_settings(settings);
  }
  virtual int64_t get_link_speed() = 0;

protected:
  // These protected fields are used to cache camera state and avoid re-querying.
  // We own the camera, so after initial configuration these values should reflect reality for the
  // duration of this object being alive.
  PixelFormat pixel_format_ = PixelFormat::kUnknown;
  int width_ = 0;
  int height_ = 0;

  void set_settings(const CameraSettings &new_settings) { settings_ = new_settings; }
  void set_info(const CameraInfo &new_info) { info_ = new_info; }
  CameraSettings settings_;
  std::shared_ptr<carbon::config::ConfigTree> camera_config_;

private:
  CameraInfo info_;
};

class Camera {
public:
  using CamImplFunc = std::function<std::unique_ptr<CameraImpl>(const CameraInfo &)>;
  Camera(CamImplFunc mk_impl, CameraInfo info)
      : mk_impl_(mk_impl), impl_(std::make_shared<std::unique_ptr<CameraImpl>>()), info_(info),
        ready_(std::make_shared<bool>(false)), ready_mutex_(std::make_shared<std::mutex>()),
        ready_condition_variable_(std::make_shared<std::condition_variable>()),
        impl_mutex_(std::make_shared<std::shared_mutex>()) {}
  void reset() {
    {
      std::unique_lock<std::mutex> lck(*ready_mutex_);
      *ready_ = false;
    }
    info_.refresh();
    {
      std::unique_lock<std::shared_mutex> lock(*impl_mutex_);
      impl_->reset();
      impl_->reset(mk_impl_(info_).release());
    }

    {
      std::unique_lock<std::mutex> lck(*ready_mutex_);
      *ready_ = true;
    }
    ready_condition_variable_->notify_all();
  }
  const CameraInfo &get_info() const { return info_; }
  const CameraInfo &get_synced_info(const std::optional<std::chrono::milliseconds> &wait_time = {}) const {
    return get_impl(wait_time)->get_info();
  }
  const CameraSettings &get_settings() const { return get_impl({})->get_settings(); }
  PixelFormat get_pixel_format() const { return get_impl({})->get_pixel_format(); }
  int get_width() const { return get_impl({})->get_width(); }
  int get_height() const { return get_impl({})->get_height(); }
  void start_grabbing() { return get_impl({})->start_grabbing(); }
  CameraImage grab() { return get_impl({})->grab(); }
  void stop_grabbing() { return get_impl({})->stop_grabbing(); }
  int64_t update_settings(const CameraSettings &settings) { return get_impl({})->update_settings(settings); }
  void set_auto_whitebalance(bool enable) { get_impl({})->set_auto_whitebalance(enable); }

  std::shared_ptr<CameraImpl> get_impl(const std::optional<std::chrono::milliseconds> &wait_time) {
    {
      std::unique_lock<std::mutex> lck(*ready_mutex_);
      if (wait_time) {
        if (!ready_condition_variable_->wait_for(lck, *wait_time, [&]() { return *ready_; })) {
          return nullptr;
        }
      } else {
        ready_condition_variable_->wait(lck, [&]() { return *ready_; });
      }
    }
    impl_mutex_->lock_shared();
    return std::shared_ptr<CameraImpl>(impl_->get(), [&](CameraImpl *) { impl_mutex_->unlock_shared(); });
  }
  const std::shared_ptr<CameraImpl> get_impl(const std::optional<std::chrono::milliseconds> &wait_time) const {
    // Cast away the const on this to call non-const version of get_impl
    return const_cast<Camera *>(this)->get_impl(wait_time);
  }

private:
  CamImplFunc mk_impl_;
  std::shared_ptr<std::unique_ptr<CameraImpl>> impl_;
  CameraInfo info_;
  mutable std::shared_ptr<bool> ready_;
  mutable std::shared_ptr<std::mutex> ready_mutex_;
  mutable std::shared_ptr<std::condition_variable> ready_condition_variable_;
  mutable std::shared_ptr<std::shared_mutex> impl_mutex_;
};

} // namespace camera
} // namespace common
} // namespace lib

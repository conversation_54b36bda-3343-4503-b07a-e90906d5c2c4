#pragma once

#include <fmt/format.h>
#include <fmt/ostream.h>
#include <functional>
#include <memory>
#include <ostream>
#include <spdlog/spdlog.h>
#include <string>

#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace common {
namespace camera {

enum class CameraVendor {
  kUnknown,
  kBasler,
  kThinkLucid,
  kZed,
  kKaya,
  kSimulated,
  kMock,
};

inline std::ostream &operator<<(std::ostream &os, const CameraVendor &v) {
  switch (v) {
  case CameraVendor::kUnknown:
    return os << "UNKNOWN";
  case CameraVendor::kB<PERSON><PERSON>:
    return os << "BASLER";
  case CameraVendor::kThinkLucid:
    return os << "THINKLUCID";
  case CameraVendor::kZed:
    return os << "ZED";
  case CameraVendor::kKaya:
    return os << "KAYA";
  case CameraVendor::kSimulated:
    return os << "SIMULATED";
  case CameraVendor::kMock:
    return os << "MOCK";
  default:
    return os << "INVALID";
  }
}

struct CameraInfo {
  std::string camera_id;
  enum CameraVendor vendor;
  std::string serial_number;
  std::string ip_address;
  std::string model;
  float ppi;
  std::string firmware_version;
  std::function<std::string(const std::string &)> serial_refresher;
  // Vendor-specific handle to a camera information pointer.
  std::shared_ptr<void> handle;

  CameraInfo() : vendor(CameraVendor::kUnknown), ppi(0.0f), serial_refresher(nullptr) {}
  CameraInfo(const CameraInfo &rhs)
      : camera_id(rhs.camera_id), vendor(rhs.vendor), serial_number(rhs.serial_number), ip_address(rhs.ip_address),
        model(rhs.model), ppi(rhs.ppi), firmware_version(rhs.firmware_version), serial_refresher(rhs.serial_refresher),
        handle(rhs.handle) {}
  CameraInfo &operator=(const CameraInfo &rhs) {
    this->camera_id = rhs.camera_id;
    this->vendor = rhs.vendor;
    this->serial_number = rhs.serial_number;
    this->ip_address = rhs.ip_address;
    this->model = rhs.model;
    this->ppi = rhs.ppi;
    this->firmware_version = rhs.firmware_version;
    this->serial_refresher = rhs.serial_refresher;
    this->handle = rhs.handle;
    return *this;
  }

  void refresh() {
    if (serial_refresher) {
      serial_number = serial_refresher(camera_id);
    }
  }

  bool matches(const CameraInfo &pattern) const {
    if (!pattern.model.empty() && model != pattern.model) {
      return false;
    }
    if (!pattern.ip_address.empty() && ip_address != pattern.ip_address) {
      return false;
    }
    if (!pattern.serial_number.empty() && serial_number != pattern.serial_number) {
      return false;
    }
    return true;
  }

  CameraInfo combine(const CameraInfo &other) const {
    CameraInfo result;

    if (other.camera_id.empty() || camera_id == other.camera_id) {
      result.camera_id = camera_id;
    } else if (camera_id.empty()) {
      result.camera_id = other.camera_id;
    } else {
      throw maka_error(fmt::format("Cannot combine unrelated {} with {}", *this, other));
    }

    if (other.vendor == CameraVendor::kUnknown || vendor == other.vendor) {
      result.vendor = vendor;
    } else if (vendor == CameraVendor::kUnknown) {
      result.vendor = other.vendor;
    } else {
      throw maka_error(fmt::format("Cannot combine unrelated {} with {}", *this, other));
    }

    if (other.serial_number.empty() || serial_number == other.serial_number) {
      result.serial_number = serial_number;
    } else if (serial_number.empty()) {
      result.serial_number = other.serial_number;
    } else {
      throw maka_error(fmt::format("Cannot combine unrelated {} with {}", *this, other));
    }

    if (other.ip_address.empty() || ip_address == other.ip_address) {
      result.ip_address = ip_address;
    } else if (ip_address.empty()) {
      result.ip_address = other.ip_address;
    } else {
      throw maka_error(fmt::format("Cannot combine unrelated {} with {}", *this, other));
    }

    if (other.model.empty() || model == other.model) {
      result.model = model;
    } else if (model.empty()) {
      result.model = other.model;
    } else {
      throw maka_error(fmt::format("Cannot combine unrelated {} with {}", *this, other));
    }

    if (other.ppi == 0.0 || ppi == other.ppi) {
      result.ppi = ppi;
    } else if (ppi == 0.0) {
      result.ppi = other.ppi;
    } else {
      throw maka_error(fmt::format("Cannot combine unrelated {} with {}", *this, other));
    }

    if (other.firmware_version.empty() || firmware_version == other.firmware_version) {
      result.firmware_version = firmware_version;
    } else if (firmware_version.empty()) {
      result.firmware_version = other.firmware_version;
    } else {
      throw maka_error(fmt::format("Cannot combine unrelated {} with {}", *this, other));
    }

    if (other.handle.get() == nullptr || handle == other.handle) {
      result.handle = handle;
    } else if (handle.get() == nullptr) {
      result.handle = other.handle;
    } else {
      result.handle = handle;
    }

    if (!other.serial_refresher) {
      result.serial_refresher = serial_refresher;
    } else {
      result.serial_refresher = other.serial_refresher;
    }

    return result;
  }

  friend std::ostream &operator<<(std::ostream &os, const CameraInfo &info) {
    using namespace std::string_literals;

    std::vector<std::string> fields;
    if (!info.camera_id.empty()) {
      fields.emplace_back(fmt::format("id={}", info.camera_id));
    }
    fields.emplace_back(fmt::format("vendor={}", info.vendor));
    if (!info.serial_number.empty()) {
      fields.emplace_back(fmt::format("serial_number={}", info.serial_number));
    }
    if (!info.ip_address.empty()) {
      fields.emplace_back(fmt::format("ip_address={}", info.ip_address));
    }
    if (!info.model.empty()) {
      fields.emplace_back(fmt::format("model={}", info.model));
    }
    if (info.ppi != 0.0) {
      fields.emplace_back(fmt::format("ppi={}", info.ppi));
    }
    if (!info.firmware_version.empty()) {
      fields.emplace_back(fmt::format("firmware_version={}", info.firmware_version));
    }

    os << fmt::format("CameraInfo[{}]", fmt::join(fields, ", "));
    return os;
  }
};

} // namespace camera
} // namespace common
} // namespace lib

#pragma once

#include "generated/lib/common/camera/proto/camera.pb.h"
#include <optional>
#include <ostream>
#include <string>

namespace lib {
namespace common {
namespace camera {

struct CameraSettings {
  bool mirror = false;
  bool flip = false;
  bool strobing = false;
  bool ptp = false;
  bool stream_packet_resend = false;
  std::optional<int> roi_width;
  std::optional<int> roi_height;
  std::optional<int> roi_offset_x;
  std::optional<int> roi_offset_y;
  std::optional<float> exposure_us;
  std::optional<float> gamma;
  std::optional<float> gain_db;
  std::optional<LightSourcePreset> light_source_preset;
  std::optional<float> wb_ratio_red;
  std::optional<float> wb_ratio_green;
  std::optional<float> wb_ratio_blue;
  std::optional<std::string> line_filter_selector;
  std::optional<float> line_filter_width;
  std::optional<int> gpu_id;

  std::optional<float> sim_fps;

  std::optional<bool> auto_whitebalance;
};

enum class PixelFormat {
  kUnknown,
  kBayerGR8,
  kBayerRG8,
  kBayerGB8,
  kBayerBG8,
  kMono8,
  kRGB8,
};

inline std::ostream &operator<<(std::ostream &os, const LightSourcePreset &lsp) {
  switch (lsp) {
  case LightSourcePreset::kOff:
    return os << "Off";
  case LightSourcePreset::kDaylight5000K:
    return os << "Daylight5000K";
  case LightSourcePreset::kDaylight6500K:
    return os << "Daylight6500K";
  case LightSourcePreset::kTungsten2800K:
    return os << "Tungsten2800K";
  default:
    return os << "INVALID";
  }
}

inline std::ostream &operator<<(std::ostream &os, const PixelFormat &pf) {
  switch (pf) {
  case PixelFormat::kUnknown:
    return os << "UNKNOWN";
  case PixelFormat::kBayerGR8:
    return os << "BayerGR8";
  case PixelFormat::kBayerRG8:
    return os << "BayerRG8";
  case PixelFormat::kBayerGB8:
    return os << "BayerGB8";
  case PixelFormat::kBayerBG8:
    return os << "BayerBG8";
  case PixelFormat::kMono8:
    return os << "Mono8";
  case PixelFormat::kRGB8:
    return os << "RGB8";
  default:
    return os << "INVALID";
  }
}

} // namespace camera
} // namespace common
} // namespace lib
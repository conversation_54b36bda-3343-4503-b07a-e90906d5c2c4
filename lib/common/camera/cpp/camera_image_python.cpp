#include "lib/common/camera/cpp/camera_image.h"

#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <torch/extension.h>

namespace py = pybind11;

namespace lib::common::camera {

PYBIND11_MODULE(camera_image_python, m) {
  py::class_<CameraImage>(m, "CameraImage")
      .def(py::init([]() {
             auto img = CameraImage();
             img.pixel_format = PixelFormat::kRGB8;
             return img;
           }),
           py::call_guard<py::gil_scoped_release>())
      .def_readwrite("camera_id", &CameraImage::camera_id)
      .def_readwrite("timestamp_ms", &CameraImage::timestamp_ms)
      .def_readwrite("ppi", &CameraImage::ppi)
      .def_readwrite("focus_metric", &CameraImage::focus_metric)
      .def_readwrite("exposure_us", &CameraImage::exposure_us)
      .def_readwrite("gain_db", &CameraImage::gain_db)
      .def_readwrite("image", &CameraImage::image)
      .def_readwrite("depth", &CameraImage::depth)
      .def_readwrite("geo_lla_data", &CameraImage::geo_lla_data)
      .def_readwrite("geo_ecef_data", &CameraImage::geo_ecef_data);
}

} // namespace lib::common::camera
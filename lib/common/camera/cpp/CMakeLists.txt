CompileProto(../proto/camera.proto GENERATED_PATH GOPKG proto/camera LANGS grpc_python python mypy grpc cpp go go-grpc)

add_library(camera_proto SHARED ${GENERATED_PATH}/camera.pb.cc ${GENERATED_PATH}/camera.pb.h)

pybind11_add_module(camera_image_python SHARED camera_image_python.cpp)
target_link_libraries(camera_image_python PUBLIC fmt torch torch_python simulator_proto)
target_compile_options(camera_image_python PRIVATE -fvisibility=default)
set_target_properties(camera_image_python PROPERTIES OUTPUT_NAME camera_image_python.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

#pragma once

#include <vector>

#include "camera.h"
#include "camera_info.h"
#include "camera_settings.h"

namespace lib {
namespace common {
namespace camera {

class CameraFactory {
public:
  virtual ~CameraFactory() = default;
  virtual std::vector<CameraInfo> list_devices() = 0;
  virtual Camera create_device(const CameraInfo &info, const CameraSettings &settings,
                               std::shared_ptr<carbon::config::ConfigTree> camera_config) = 0;
  virtual CameraVendor get_vendor() const = 0;
};

} // namespace camera
} // namespace common
} // namespace lib

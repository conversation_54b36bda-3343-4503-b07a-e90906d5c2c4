from typing import Any, Dict


class Singleton(type):
    """
    Metaclass for singleton objects. Allows only one instance of a given type to exist in the system, with an optional
    override to aide in testing which allows more than one singleton to exist.
    """

    _instances: Dict[type, Any] = {}

    def __call__(cls, *args: Any, **kwargs: Any) -> Any:
        allow_multiple = kwargs.pop("allow_multiple", False)
        if cls not in cls._instances or allow_multiple:
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]

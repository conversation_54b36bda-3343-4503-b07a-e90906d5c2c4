import os
from typing import Dict, Optional

from bot.generation import GENERATION

_gen = GENERATION.from_str_with_default(os.environ.get("MAKA_GEN", ""), GENERATION.BUD)


def generation() -> GENERATION:
    return _gen


def is_bud() -> bool:
    return _gen == GENERATION.BUD


def is_slayer() -> bool:
    return _gen == GENERATION.SLAYER


def is_reaper() -> bool:
    return _gen == GENERATION.REAPER


def is_rtc() -> bool:
    return _gen == GENERATION.RTC


def get_command_ip() -> str:
    if is_bud() or is_rtc():
        return "127.0.0.1"
    else:
        return "*********"


def rows() -> Dict[str, str]:
    if is_bud() or is_rtc():
        return {"row1": "127.0.0.1"}
    if is_slayer():
        return {
            "row1": "*********1",
            "row2": "*********2",
            "row3": "**********",
        }
    return {}


def optional_gen_to_gen(gen: Optional[GENERATION] = None) -> GENERATION:
    if gen is None:
        return _gen
    return gen

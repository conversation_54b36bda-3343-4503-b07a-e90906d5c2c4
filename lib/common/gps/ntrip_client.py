import asyncio
import datetime
import logging
import time
from abc import ABC, abstractmethod
from argparse import ArgumentParser
from base64 import b64encode
from collections import defaultdict
from types import TracebackType
from typing import AsyncGenerator, Awaitable, Callable, DefaultDict, Generic, Optional, Tuple, Type, TypeVar

from lib.common.asyncio.cancel_task import cancel_and_wait
from lib.common.asyncio.cancellable_await import cancellable_await
from lib.common.gps.spartn_messages import BASE_TIME, SPARTN, CRCType, MsgType, SPARTN_Header, TimeType

POINT_PERFECT_ENDPOINT = "ppntrip.services.u-blox.com"
PP_HTTP_PORT = 2101

CRLF = "\r\n"
CRLF_BYTES = CRLF.encode()
NEXT_HEADER_TIMEOUT = 120
IN_FRAME_TIMEOUT = 300

LOG = logging.getLogger(__name__)

ConCB = Optional[Callable[[bool], None]]


class NTRIP_Reader:
    def __init__(self, uri: str, mount: str, username: str, passwd: str, connect_change: ConCB = None):
        auth = b64encode(f"{username}:{passwd}".encode()).decode()
        uri_parts = uri.split(":", 3)
        assert len(uri_parts) == 3
        self._server = uri_parts[1].strip("/")
        self._port = int(uri_parts[2])
        if not mount:
            mount = "/"
        elif mount[0] != "/":
            mount = f"/{mount}"
        headers = [
            f"GET {mount} HTTP/1.1",
            "Ntrip-Version: Ntrip/2.0",
            "User-Agent: NTRIP python/carbon/v0.0.1",
            "Accept: */*",
            "Connection: close",
            f"Authorization: Basic {auth}",
        ]
        self._request = (CRLF.join(headers) + CRLF + CRLF).encode()  # need 2 trailing line breaks per http standards
        self._connect_change = connect_change

    async def __aenter__(self) -> "NTRIP_Reader":
        self._reader, self._writer = await asyncio.open_connection(self._server, self._port)
        await self.write(self._request)
        headers = await self.read(1024)
        header_lines = headers.split(CRLF_BYTES)
        if len(header_lines) < 2:
            raise Exception("Invalid response from server")
        status = header_lines[0].decode().split(" ")
        if status[1] != "200":
            raise Exception(f"Server returned bad response {status[1]}")
        if self._connect_change is not None:
            self._connect_change(True)
        return self

    async def __aexit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        self._writer.close()
        await self._writer.wait_closed()
        if self._connect_change is not None:
            self._connect_change(False)

    async def write(self, data: bytes) -> None:
        self._writer.write(data)
        await self._writer.drain()

    async def read(self, count: int) -> bytes:
        return await self._reader.read(count)

    async def readexactly(self, count: int, timeout: Optional[float] = None) -> bytes:
        if timeout is None:
            return await self._reader.readexactly(count)
        else:
            return await asyncio.wait_for(self._reader.readexactly(count), timeout)


def bytesToInt(bit_field: bytes, start: int, length: int) -> int:
    lbb = len(bit_field) * 8
    if start + length > lbb:
        raise IndexError(f"Attribute size {length} exceeds remaining payload length {lbb - start}")
    mask = (1 << length) - 1  # make mask of 1bits with len length
    return int.from_bytes(bit_field, "big") >> (lbb - start - length) & mask


def _half_day_base() -> int:
    half_day_in_sec = 12 * 60 * 60
    now = datetime.datetime.now(tz=datetime.timezone.utc) - BASE_TIME
    to_sub = now.seconds
    if now.seconds > half_day_in_sec:
        to_sub -= half_day_in_sec
    return int(now.total_seconds()) - to_sub


T = TypeVar("T")


class NTRIP_parser(ABC, Generic[T]):
    def __init__(
        self, reader: NTRIP_Reader, gga_producer: Callable[[], Awaitable[bytes]], gga_interval: int = 10
    ) -> None:
        self._log = LOG.getChild(self.__class__.__name__)
        self._reader = reader
        self._gga_producer = gga_producer
        self._interval = gga_interval

    async def _write_gga_loop(self, stop_event: asyncio.Event) -> None:
        last_ts = 0.0
        try:
            while not stop_event.is_set():
                now = time.monotonic()
                delta = now - last_ts
                if delta >= self._interval:
                    msg = await self._gga_producer()
                    if not msg.startswith("$".encode()) or len(msg) < 5:
                        self._log.warning(f"Received bad GGA msg {msg!r}")
                        continue
                    if not msg.endswith(CRLF_BYTES):
                        msg += CRLF_BYTES
                    await self._reader.write(msg)
                    last_ts = now
                else:
                    await cancellable_await(asyncio.sleep(self._interval - delta), stop_event)
        finally:
            stop_event.set()

    async def parse(self) -> AsyncGenerator[T, None]:
        await self._reset()
        async with self._reader as stream:
            stop_event = asyncio.Event()
            write_task = asyncio.get_event_loop().create_task(self._write_gga_loop(stop_event))
            try:
                while not stop_event.is_set():
                    yield await self._parse(stream)
            finally:
                stop_event.set()
                await cancel_and_wait(write_task)

    async def _reset(self) -> None:
        pass

    @abstractmethod
    async def _parse(self, stream: NTRIP_Reader) -> T:
        ...


class SPARTN_NTRIP_parser(NTRIP_parser[SPARTN]):
    SPARTN_LEADER = 115
    ALN_ENUM = {0: 8, 1: 12, 2: 16, 3: 32, 4: 64}

    def __init__(
        self, reader: NTRIP_Reader, gga_producer: Callable[[], Awaitable[bytes]], gga_interval: int = 10
    ) -> None:
        super().__init__(reader, gga_producer, gga_interval)
        self._base_times: DefaultDict[Tuple[int, int], int] = defaultdict(_half_day_base)

    async def _reset(self) -> None:
        self._base_times = defaultdict(_half_day_base)

    async def _parse(self, stream: NTRIP_Reader) -> SPARTN:
        preamble = await stream.readexactly(4, NEXT_HEADER_TIMEOUT)
        assert preamble[0] == SPARTN_NTRIP_parser.SPARTN_LEADER
        msg_type = bytesToInt(preamble[1:2], 0, 7)
        if (msg_type >= 5 and msg_type < 120) or msg_type > 120:
            msg_type = 5
        data_size = bytesToInt(preamble[1:4], 7, 10)
        eaf = bytesToInt(preamble[1:4], 17, 1)
        crc_type = bytesToInt(preamble[1:4], 18, 2)
        payload_desc = await stream.readexactly(4, IN_FRAME_TIMEOUT)
        msg_subtype = bytesToInt(payload_desc, 0, 4)
        time_type = bytesToInt(payload_desc, 4, 1)
        time_len = 32 if time_type == 1 else 16
        if time_type:
            payload_desc += await stream.readexactly(2, IN_FRAME_TIMEOUT)
        time_val = bytesToInt(payload_desc, 5, time_len)
        if time_type:
            self._base_times[(msg_type, msg_subtype)] = time_val
        auth_idx = 0
        if eaf:
            payload_desc += await stream.readexactly(2, IN_FRAME_TIMEOUT)
            auth_idx = bytesToInt(payload_desc, time_len + 26, 3)
            emb_auth_len = bytesToInt(payload_desc, time_len + 29, 3)
        payload = await stream.readexactly(data_size, IN_FRAME_TIMEOUT)
        if auth_idx > 1:
            aln = self.ALN_ENUM.get(emb_auth_len, 0)
            emb_auth = await stream.readexactly(aln, IN_FRAME_TIMEOUT)
        else:
            emb_auth = bytes()
        crcb = await stream.readexactly(crc_type + 1, IN_FRAME_TIMEOUT)
        buffer = preamble + payload_desc + payload + emb_auth + crcb
        return SPARTN(
            header=SPARTN_Header(
                msg_type=MsgType(msg_type),
                payload_len=data_size,
                eaf=bool(eaf),
                crc_type=CRCType(crc_type),
                msg_subtype=msg_subtype,
                time_type=TimeType(time_type),
                time=time_val,
                time_ref=self._base_times[(msg_type, msg_subtype)],
            ),
            raw=buffer,
        )


class RTCM_NTRIP_parser(NTRIP_parser[bytes]):
    RTCM_LEADER = 0xD3

    async def _parse(self, stream: NTRIP_Reader) -> bytes:
        header = await stream.readexactly(3, NEXT_HEADER_TIMEOUT)
        assert header[0] == self.RTCM_LEADER
        msg_len = bytesToInt(header[1:3], 6, 10)
        data = await stream.readexactly(msg_len, IN_FRAME_TIMEOUT)
        checksum = await stream.readexactly(3, IN_FRAME_TIMEOUT)
        buffer = header + data + checksum
        return buffer


async def main() -> None:
    parser = ArgumentParser("NTRIP SPARTN reader")
    parser.add_argument("-e", "--endpoint", type=str, default=POINT_PERFECT_ENDPOINT, help="Endpoint to talk to")
    parser.add_argument("-m", "--mount", type=str, default="/NEAR-SPARTN", help="Mount to load")
    parser.add_argument("-p", "--port", type=int, default=PP_HTTP_PORT, help="port to use")
    parser.add_argument(
        "-r", "--rtcm", action="store_true", default=False, help="Use rtcm correction instead of SPARTN"
    )
    parser.add_argument("--secure", action="store_true", default=False, help="Use https")
    parser.add_argument("username", type=str, help="Username to log into ntrip server")
    parser.add_argument("passwd", type=str, help="password to log into ntrip server")

    args = parser.parse_args()
    conn = "http"
    if args.secure:
        conn = "https"
    uri = f"{conn}://{args.endpoint}:{args.port}"
    reader = NTRIP_Reader(uri=uri, mount=args.mount, username=args.username, passwd=args.passwd)

    async def _mock_producer() -> bytes:
        # See http://lefebure.com/articles/nmea-gga/ and https://receiverhelp.trimble.com/alloy-gnss/en-us/NMEA-0183messages_GGA.html for gga message explanation
        ts = datetime.datetime.now(datetime.timezone.utc).strftime("%H%M%S.00")
        lat = "4737.61881,N"
        lon = "12220.64176,W"
        fix = "1"
        num_sat = "12"
        hdop = "1.02"
        elevation_data = "32.6,M,-18.8,M"
        correction_age = ""
        correction_station = ""
        msg = f"GNGGA,{ts},{lat},{lon},{fix},{num_sat},{hdop},{elevation_data},{correction_age},{correction_station}"
        encoded = msg.encode()
        checksum = 0
        for c in encoded:
            checksum ^= int(c)
        chskstr = hex(checksum)[2:]
        msg = f"${msg}*{chskstr}\r\n"
        return msg.encode()

    if not args.rtcm:
        spartn_parser = SPARTN_NTRIP_parser(reader, _mock_producer)
        async for spartn_msg in spartn_parser.parse():
            print(f"Got SPARTN msg of size {len(spartn_msg.raw)} for time: {spartn_msg.header.epoch_time()}")
            print("--------------------------------------------------------------------------------")
            print(spartn_msg)
            print("--------------------------------------------------------------------------------")
    else:

        rtcm_parser = RTCM_NTRIP_parser(reader, _mock_producer)
        async for msg in rtcm_parser.parse():
            print(f"Got RTCM msg of size {len(msg)}")
            print("--------------------------------------------------------------------------------")
            print(msg)
            print("--------------------------------------------------------------------------------")


if __name__ == "__main__":
    asyncio.run(main())

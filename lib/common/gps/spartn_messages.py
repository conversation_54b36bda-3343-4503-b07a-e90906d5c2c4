from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum

BASE_TIME = datetime(2010, 1, 1, tzinfo=timezone.utc)


class MsgType(Enum):
    MESSAGE_OCB = 0
    MESSAGE_HPAC = 1
    MESSAGE_GAD = 2
    MESSAGE_BPAC = 3
    MESSAGE_EAS = 4
    MESSAGE_TBD = 5  # 5-119
    MESSAGE_PROPRIETARY = 120


class TimeType(Enum):
    TIME_16_BIT = 0
    TIME_32_BIT = 1


class CRCType(Enum):
    CRC_8_CCITT = 0
    CRC_16_CCITT = 1
    CRC_24_RADIX_64 = 2
    CRC_32_CCITT = 3


@dataclass
class SPARTN_Header:
    msg_type: MsgType = MsgType.MESSAGE_OCB
    payload_len: int = 0
    eaf: bool = False
    crc_type: CRCType = CRCType.CRC_8_CCITT
    msg_subtype: int = 0
    time_type: TimeType = TimeType.TIME_16_BIT
    time: int = 0
    time_ref: int = 0

    def epoch_time(self) -> int:
        epoch_time = self.time + int(BASE_TIME.timestamp())
        if self.time_type == TimeType.TIME_16_BIT:
            epoch_time += self.time_ref
        return epoch_time


@dataclass
class SPARTN:
    header: SPARTN_Header = field(default_factory=SPARTN_Header)
    raw: bytes = field(default_factory=bytes)

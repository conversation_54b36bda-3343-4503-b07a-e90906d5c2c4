from dataclasses import dataclass
from typing import Optional

import numpy as np
from dataclass_wizard import <PERSON><PERSON><PERSON><PERSON><PERSON>rd
from pyproj import Geod

from lib.common.geo.geojson import Position
from lib.common.logging import get_logger
from lib.common.units.angle import RIGHT_ANGLE, Angle
from lib.common.units.distance import Distance

LOG = get_logger(__name__)


@dataclass
class RelPos(JSONWizard):
    x_mm: float = 0.0
    y_mm: float = 0.0

    def __iadd__(self, rhs: "RelPos") -> "RelPos":
        self.x_mm += rhs.x_mm
        self.y_mm += rhs.y_mm
        return self

    def __isub__(self, rhs: "RelPos") -> "RelPos":
        self.x_mm -= rhs.x_mm
        self.y_mm -= rhs.y_mm
        return self

    def __add__(self, rhs: "RelPos") -> "RelPos":
        return RelPos(x_mm=self.x_mm + rhs.x_mm, y_mm=self.y_mm + rhs.y_mm)

    def __sub__(self, rhs: "RelPos") -> "RelPos":
        return RelPos(x_mm=self.x_mm - rhs.x_mm, y_mm=self.y_mm - rhs.y_mm)


# Not true polar coordinate system as 0 deg is Y+ not X+ and azimuth+ is CW not CCW
@dataclass
class _RelPosPolar:
    dist: Distance
    azimuth: Angle


class RobotPose:
    def __init__(self) -> None:
        self._geod = Geod(ellps="WGS84")
        self._fixed_point: Optional[RelPos] = None
        self._internal_fixed_point: Optional[_RelPosPolar] = None

    def set_robot_pose(self, fixed_point: RelPos) -> None:
        self._fixed_point = fixed_point
        self._internal_fixed_point = self._rel_pos_to_rel_pos_polar(fixed_point)

    def _rel_pos_to_rel_pos_polar(self, pos: RelPos) -> _RelPosPolar:
        azimuth = RIGHT_ANGLE.radians - np.arctan2(pos.y_mm, pos.x_mm)
        dist = np.sqrt(np.sum(np.square(np.array([pos.x_mm, pos.y_mm]))))
        return _RelPosPolar(dist=Distance.from_mm(dist), azimuth=Angle.from_radians(azimuth))

    def _rel_pos_to_ll(self, pos_gps: Position, heading: Angle, rel_pos: _RelPosPolar) -> Position:
        azimuth = heading + rel_pos.azimuth
        long, lat, _ = self._geod.fwd(pos_gps.longitude, pos_gps.latitude, azimuth.degrees, rel_pos.dist.meters)
        return Position(x=long, y=lat)

    def get_fixed_point(self, pos_gps: Position, heading: Angle) -> Position:
        assert self._internal_fixed_point is not None
        return self._rel_pos_to_ll(pos_gps=pos_gps, heading=heading, rel_pos=self._internal_fixed_point)

    @property
    def fixed_point(self) -> RelPos:
        assert self._fixed_point is not None
        return self._fixed_point


ROBOT_POSE = RobotPose()

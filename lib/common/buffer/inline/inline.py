from generated.lib.common.buffer.inline.inline_pb2 import InlineBufferProto


class InlineBufferImpl:
    def __init__(self, data: bytes):
        self._data = data

    def release(self) -> None:
        pass

    def to_proto(self) -> InlineBufferProto:
        return InlineBufferProto(data=self._data, not_empty=True)

    @staticmethod
    def from_proto(proto: InlineBufferProto) -> "InlineBufferImpl":
        assert proto.not_empty
        return InlineBufferImpl(proto.data)

    def to_bytes(self) -> bytes:
        return self._data

    @staticmethod
    def from_bytes(data: bytes) -> "InlineBufferImpl":
        return InlineBufferImpl(data)

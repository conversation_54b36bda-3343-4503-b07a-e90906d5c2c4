import atexit
import glob
import logging
import os
import threading
from typing import Any, Optional, Type, Union

import numpy as np

from generated.lib.common.buffer.shm.shm_pb2 import MemFileProto, ShmemBufferProto
from lib.common.shmem.cpp.shmem_python import <PERSON>loat<PERSON><PERSON><PERSON><PERSON>uffer, ImageBuffer, SharedMemoryBuffer

_PID = os.getpid()
_CPUSET = "/"
if os.path.exists("/proc/1/cpuset"):
    with open("/proc/1/cpuset", "r") as fp:
        _CPUSET = fp.read()
_CONTAINER_ID = os.path.basename(_CPUSET) if _CPUSET.startswith("/docker") else "host"
_FILE_INDEX = 0
_FILE_INDEX_LOCK = threading.Lock()


def _mk_shm_path(suffix: str) -> str:
    return os.path.join("/dev/shm", f"robot-{_CONTAINER_ID}-{_PID}-{suffix}.buffer")


@atexit.register
def _cleanup_shm_files() -> None:
    for f in glob.glob(_mk_shm_path("*")):
        try:
            os.remove(f)
        except:  # noqa
            logging.exception(f"Unable to cleanup {f}")


class MemFileBufferImpl:
    def __init__(self, path: str):
        self._shm_path = path

    def release(self) -> None:
        try:
            os.remove(self._shm_path)
        except:  # noqa
            logging.exception(f"Unable to cleanup {self._shm_path}")

    def to_proto(self) -> MemFileProto:
        return MemFileProto(path=self._shm_path)

    @staticmethod
    def from_proto(proto: MemFileProto) -> "MemFileBufferImpl":
        assert proto.path != ""
        return MemFileBufferImpl(proto.path)

    def to_bytes(self) -> bytes:
        with open(self._shm_path, "rb") as f:
            return f.read()

    @staticmethod
    def from_bytes(data: bytes) -> "MemFileBufferImpl":
        global _FILE_INDEX_LOCK, _FILE_INDEX
        with _FILE_INDEX_LOCK:
            file_idx = _FILE_INDEX
            _FILE_INDEX += 1

        shm_path = _mk_shm_path(str(file_idx))
        assert not os.path.exists(shm_path), f"SHM file exists before being allocated: {shm_path}"

        with open(shm_path, "wb") as f:
            f.write(data)

        return MemFileBufferImpl(shm_path)


class ShmemBufferImpl:
    def __init__(self, path: str, data_type: Optional[Type[Any]] = None):
        self._shm_path = path
        self._data_type = data_type

    def release(self) -> None:
        pass

    def to_proto(self) -> ShmemBufferProto:
        return ShmemBufferProto(path=self._shm_path)

    @staticmethod
    def from_proto(proto: ShmemBufferProto, data_type: Optional[Type[Any]] = None) -> "ShmemBufferImpl":
        assert proto.path != ""
        return ShmemBufferImpl(proto.path, data_type)

    def to_buf(self) -> Union[ImageBuffer, Float32ImageBuffer]:
        if self._data_type is np.float32:
            return Float32ImageBuffer(self._shm_path)
        return ImageBuffer(self._shm_path)

    @staticmethod
    def from_buf(buf: SharedMemoryBuffer) -> "ShmemBufferImpl":
        return ShmemBufferImpl(buf.export_path())

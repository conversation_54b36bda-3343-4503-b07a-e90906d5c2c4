import glob
import os
import sys

import pytest

from lib.common.buffer.shm import MemFileBufferImpl


@pytest.mark.skipif(sys.platform == "darwin", reason="No /dev/shm on MacOSX")
def test_round_trip() -> None:
    data = b"hello world!"

    # round trip
    buf = MemFileBufferImpl.from_bytes(data)
    proto = buf.to_proto()
    rcvd_buf = MemFileBufferImpl.from_proto(proto)
    assert data == rcvd_buf.to_bytes()

    with open("/proc/1/cpuset", "r") as fp:
        cpuset = fp.read()
    container_id = os.path.basename(cpuset) if cpuset.startswith("/docker") else "host"
    assert 1 == len(glob.glob(f"/dev/shm/robot-{container_id}-{os.getpid()}-*.buffer"))

    # release buffer & verify /dev/shm is empty
    rcvd_buf.release()
    assert 0 == len(glob.glob(f"/dev/shm/robot-{container_id}-{os.getpid()}-*.buffer"))

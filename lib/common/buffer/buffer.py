from typing import Any, Optional, Type, Union

from generated.lib.common.buffer.buffer_pb2 import <PERSON><PERSON>er<PERSON>roto
from lib.common.buffer.inline import InlineBufferImpl
from lib.common.buffer.shm.shm import MemFileBufferImpl, ShmemBufferImpl
from lib.common.buffer.type import BufferType
from lib.common.shmem.cpp.shmem_python import <PERSON>loat<PERSON><PERSON><PERSON><PERSON>uffer, ImageBuffer


class Buffer:
    """
    Class that allows converting byte buffers to protobuf objects and back.

    Protobuf objects can have bytes either encoded inline (BufferType.INLINE) or
    saved to shared memory to avoid serialization costs (BufferType.MEM_FILE) or
    shared memory pages (BufferType.SHMEM).

    Note that some data transfer mechanism allocate resources that need to be
    released using Buffer.release() on the protobuf object returned from to_proto().
    This can be done on either sending or receiving end, allowing flexible lifetime
    management.

    In the future, this class can be expanded to support CUDA and RDMA.
    """

    @staticmethod
    def to_proto(data: Union[bytes, ImageBuffer], type_: BufferType) -> BufferProto:
        if type_ == BufferType.INLINE:
            assert isinstance(data, bytes)
            return BufferProto(inline=InlineBufferImpl.from_bytes(data).to_proto())
        if type_ == BufferType.MEM_FILE:
            assert isinstance(data, bytes)
            return BufferProto(memfile=MemFileBufferImpl.from_bytes(data).to_proto())
        if type_ == BufferType.SHMEM:
            assert isinstance(data, ImageBuffer)
            return BufferProto(shmem=ShmemBufferImpl.from_buf(data).to_proto())
        assert False, f"Unknown type: {type_}"

    @staticmethod
    def from_proto(
        proto: BufferProto, data_type: Optional[Type[Any]] = None
    ) -> Union[bytes, ImageBuffer, Float32ImageBuffer]:
        assert not Buffer.is_empty(proto)
        if proto.WhichOneof("type") == "inline":
            return InlineBufferImpl.from_proto(proto.inline).to_bytes()
        if proto.WhichOneof("type") == "memfile":
            return MemFileBufferImpl.from_proto(proto.memfile).to_bytes()
        if proto.WhichOneof("type") == "shmem":
            return ShmemBufferImpl.from_proto(proto.shmem, data_type).to_buf()
        assert False, f"Unknown type: {proto.WhichOneof('type')}"

    @staticmethod
    def get_type(proto: BufferProto) -> BufferType:
        if proto.WhichOneof("type") == "inline":
            return BufferType.INLINE
        if proto.WhichOneof("type") == "memfile":
            return BufferType.MEM_FILE
        if proto.WhichOneof("type") == "shmem":
            return BufferType.SHMEM
        assert False, f"Unknown type: {proto.WhichOneof('type')}"

    @staticmethod
    def is_empty(proto: Optional[BufferProto]) -> bool:
        return proto is None or proto.WhichOneof("type") is None

    @staticmethod
    def release(proto: BufferProto) -> None:
        if Buffer.is_empty(proto):
            pass
        elif proto.WhichOneof("type") == "inline":
            InlineBufferImpl.from_proto(proto.inline).release()
        elif proto.WhichOneof("type") == "memfile":
            MemFileBufferImpl.from_proto(proto.memfile).release()
        elif proto.WhichOneof("type") == "shmem":
            ShmemBufferImpl.from_proto(proto.shmem).release()
        else:
            assert False, f"Unknown type: {proto.WhichOneof('type')}"

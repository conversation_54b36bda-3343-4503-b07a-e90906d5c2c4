#pragma once

#include <cstddef>

#include "pybind11/numpy.h"

#include "glm/gtc/type_ptr.hpp" // includes all vector and matrix types too

namespace pybind11::detail {

/**
 * https://github.com/patrikhuber/eos/blob/fbcfa576ddd8daa49488e8d033d3be24d574deb0/utils/pybind11_glm.hpp
 * @file utils/pybind11_glm.hpp
 * @brief Transparent conversion to and from Python for glm vector and matrix types.
 *
 * All converters for matrices assume col-major storage of glm, the default.
 * Things will likely break if non-default storage order is used.
 */

template <typename T, glm::precision P>
struct type_caster<glm::tvec2<T, P>> {
  using vector_type = glm::tvec2<T, P>;
  typedef T Scalar;
  static constexpr std::size_t num_elements = 2;

  bool load(handle src, bool) {
    array_t<Scalar> buf = array_t<Scalar>::ensure(src);

    if (buf.ndim() == 1) // a 1-dimensional vector
    {
      if (buf.shape(0) != num_elements) {
        return false; // not a 2-elements vector
      }
      if (buf.strides(0) != sizeof(Scalar)) {
        std::cout << "An array with non-standard strides is given. Please pass a contiguous array." << std::endl;
        return false;
      }
      value = glm::make_vec2(buf.mutable_data()); // make_vec* copies the data (unnecessarily)
    } else {                                      // buf.ndim() != 1
      return false;
    }
    return true;
  }

  static handle cast(const vector_type &src, return_value_policy /* policy */, handle /* parent */) {
    return array(num_elements,       // shape
                 glm::value_ptr(src) // data
                 )
        .release();
  }

  PYBIND11_TYPE_CASTER(vector_type, const_name("glm::tvec2"));
};

template <typename T, glm::precision P>
struct type_caster<glm::tvec3<T, P>> {
  using vector_type = glm::tvec3<T, P>;
  typedef T Scalar;
  static constexpr std::size_t num_elements = 3;

  bool load(handle src, bool) {
    array_t<Scalar> buf = array_t<Scalar>::ensure(src);

    if (buf.ndim() == 1) // a 1-dimensional vector
    {
      if (buf.shape(0) != num_elements) {
        return false; // not a 3-elements vector
      }
      if (buf.strides(0) != sizeof(Scalar)) {
        std::cout << "An array with non-standard strides is given. Please pass a contiguous array." << std::endl;
        return false;
      }
      value = glm::make_vec3(buf.mutable_data()); // make_vec* copies the data (unnecessarily)
    } else {                                      // buf.ndim() != 1
      return false;
    }
    return true;
  }

  static handle cast(const vector_type &src, return_value_policy /* policy */, handle /* parent */) {
    return array(num_elements,       // shape
                 glm::value_ptr(src) // data
                 )
        .release();
  }

  PYBIND11_TYPE_CASTER(vector_type, const_name("glm::tvec3"));
};

template <typename T, glm::precision P>
struct type_caster<glm::tvec4<T, P>> {
  using vector_type = glm::tvec4<T, P>;
  typedef T Scalar;
  static constexpr std::size_t num_elements = 4;

  bool load(handle src, bool) {
    array_t<Scalar> buf = array_t<Scalar>::ensure(src);

    if (buf.ndim() == 1) // a 1-dimensional vector
    {
      if (buf.shape(0) != num_elements) {
        return false; // not a 4-elements vector
      }
      if (buf.strides(0) != sizeof(Scalar)) {
        std::cout << "An array with non-standard strides is given. Please pass a contiguous array." << std::endl;
        return false;
      }
      value = glm::make_vec4(buf.mutable_data()); // make_vec* copies the data (unnecessarily)
    } else {                                      // buf.ndim() != 1
      return false;
    }
    return true;
  }

  static handle cast(const vector_type &src, return_value_policy /* policy */, handle /* parent */) {
    return array(num_elements,       // shape
                 glm::value_ptr(src) // data
                 )
        .release();
  }

  PYBIND11_TYPE_CASTER(vector_type, const_name("glm::tvec4"));
};

template <typename T, glm::precision P>
struct type_caster<glm::tmat4x4<T, P>> {
  using matrix_type = glm::tmat4x4<T, P>;
  typedef T Scalar;
  static constexpr std::size_t num_rows = 4;
  static constexpr std::size_t num_cols = 4;

  bool load(handle src, bool) {
    array_t<Scalar> buf = array_t<Scalar>::ensure(src);

    if (buf.ndim() == 2) // a 2-dimensional matrix
    {
      if (buf.shape(0) != num_rows || buf.shape(1) != num_cols) {
        return false; // not a 4x4 matrix
      }
      if (buf.strides(0) / sizeof(Scalar) != num_cols || buf.strides(1) != sizeof(Scalar)) {
        std::cout << "An array with non-standard strides is given. Please pass a contiguous array." << std::endl;
        return false;
      }
      // What we get from Python is laid out in row-major memory order, while GLM's
      // storage is col-major, thus, we transpose.
      value = glm::transpose(glm::make_mat4x4(buf.mutable_data())); // make_mat*() copies the data (unnecessarily)
    } else {                                                        // buf.ndim() != 2
      return false;
    }
    return true;
  }

  static handle cast(const matrix_type &src, return_value_policy /* policy */, handle /* parent */) {
    return array({num_rows, num_cols},                        // shape
                 {sizeof(Scalar), sizeof(Scalar) * num_rows}, // strides - flip the row/col layout!
                 glm::value_ptr(src)                          // data
                 )
        .release();
  }

  PYBIND11_TYPE_CASTER(matrix_type, const_name("glm::tmat4x4"));
};

} // namespace pybind11::detail
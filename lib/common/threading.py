import logging
import threading
from logging import Logger
from typing import Any, List

import lib.common.recipes

LOG = logging.getLogger(__name__)


class MakaThread(threading.Thread):
    """Custom Thread. For now, just enforces that thread name is set for nicer logging, and defaults to daemon."""

    def __init__(self, logger: Logger = LOG, *args: Any, **kwargs: Any):
        assert kwargs["name"] is not None, "Must pass name"
        super().__init__(*args, **kwargs)
        # Future note: we may want to only do this override when forking main thread
        # aka check `threading.current_thread() is threading.main_thread()` first
        self.daemon = True  # default to daemon threads
        self.logger = logger

    def start(self) -> None:
        try:
            super().start()
        except Exception:
            # Prefer the context logger, but use the file default one if no other option
            self.logger.exception("Uncaught exception on thread: {}".format(self.name))

    def run(self) -> None:
        try:
            super().run()

        except Exception:
            # Prefer the context logger, but use the file default one if no other option
            self.logger.exception("Uncaught exception on thread: {}".format(self.name))


def _start_thread(thread: MakaThread, logging: bool = True) -> None:
    if logging:
        LOG.info("Starting %s thread...", thread.name)
    thread.start()


def start_threads(*argv: Any, logging: bool = True) -> List[MakaThread]:
    """
    Start the given threads. Supports passed in generator, list, or varargs
    """
    if logging:
        LOG.info("Starting threads...")

    result = []  # input might be a generator so need to build a list for return
    for thread in lib.common.recipes.generate_flatten(*argv):
        _start_thread(thread, logging=logging)
        result.append(thread)
    return result


def _join_thread(thread: MakaThread, logging: bool = True) -> None:
    if logging:
        LOG.info("Joining %s thread...", thread.name)
    thread.join()


def join_threads(*argv: Any, logging: bool = True) -> None:
    """
    Start the given threads. Supports passed in generator, list, or varargs
    """
    for thread in lib.common.recipes.generate_flatten(*argv):
        _join_thread(thread, logging=logging)

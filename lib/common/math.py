import functools
import math
from operator import add, mul, sub
from typing import Callable, Iterable, List, Optional, Sequence, Tuple, TypeVar, Union, cast

import numpy as np
from pyquaternion import Quaternion
from shapely import Point, Polygon

TupleVar = TypeVar("TupleVar", bound=Tuple[float, ...])


# ROUNDING


def round_tuple(t: TupleVar, digits: int) -> TupleVar:
    """Round each value in the tuple to the given number of digits and return as tuple. Cast to int if digits <= 0"""
    if digits <= 0:
        return cast(TupleVar, tuple(int(round(val, digits)) for val in t))
    else:
        return cast(TupleVar, tuple([round(val, digits) for val in t]))


def simplify(ratio: Iterable[int]) -> List[int]:
    return list(map(functools.reduce(math.gcd, ratio).__rfloordiv__, ratio))


# ADDITION


def add_tuples(t1: TupleVar, t2: TupleVar) -> TupleVar:
    """Return the pairwise addition between the two tuples"""
    assert len(t1) == len(t2)
    return cast(TupleVar, tuple(map(add, t1, t2)))


# SUBTRACTION


def subtract_tuples(t1: TupleVar, t2: TupleVar) -> TupleVar:
    """Return the pairwise subtraction between the two tuples"""
    assert len(t1) == len(t2)
    return cast(TupleVar, tuple(map(sub, t1, t2)))


# MULTIPLICATION


def multiply_tuple(t: TupleVar, scalar: float) -> TupleVar:
    """Multiply each value in the tuple by the given value and return as a tuple"""
    return cast(TupleVar, tuple([val * scalar for val in t]))


def multiply_tuples(t1: TupleVar, t2: TupleVar) -> TupleVar:
    """Return the pairwise multiplication between the two tuples"""
    assert len(t1) == len(t2)
    return cast(TupleVar, tuple(map(mul, t1, t2)))


def inverse_tuple(t: TupleVar) -> TupleVar:
    """Flip the sign of each value in the tuple"""
    return multiply_tuple(t, -1)


# DIVISION


def div_tuple(t: TupleVar, div_val: float) -> TupleVar:
    """Ceildiv each value in the tuple by the given value and return as a tuple"""
    return cast(TupleVar, tuple([val / div_val for val in t]))


def div_tuples(t1: TupleVar, t2: TupleVar) -> TupleVar:
    """Return the pairwise div between the two tuples"""
    assert len(t1) == len(t2)
    func: Callable[[float, float], float] = lambda a, b: a / b
    return cast(TupleVar, tuple(map(func, t1, t2)))


def floordiv_tuple(t: TupleVar, floordiv_val: float) -> TupleVar:
    """Floordiv each value in the tuple by the given value and return as a tuple"""
    return cast(TupleVar, tuple([int(val // floordiv_val) for val in t]))


def floordiv_tuples(t1: TupleVar, t2: TupleVar) -> TupleVar:
    """Return the pairwise floordiv between the two tuples"""
    assert len(t1) == len(t2)
    func: Callable[[float, float], int] = lambda a, b: int(a // b)
    return cast(TupleVar, tuple(map(func, t1, t2)))


def ceildiv_tuple(t: TupleVar, ceildiv_val: float) -> TupleVar:
    """Ceildiv each value in the tuple by the given value and return as a tuple"""
    return cast(TupleVar, tuple([math.ceil(val / ceildiv_val) for val in t]))


# MIN


def min_tuples(t1: TupleVar, t2: TupleVar) -> TupleVar:
    """Return the pairwise min value between the two tuples"""
    assert len(t1) == len(t2)
    func: Callable[[float, float], float] = lambda x1, x2: min(x1, x2)
    return cast(TupleVar, tuple(map(func, t1, t2)))


# MEAN


def mean_tuple(t: TupleVar) -> float:
    """Return the mean of the values in the tuple"""
    val: float = sum([*t]) / len(t)
    return val


def mean_tuples(t1: TupleVar, t2: TupleVar) -> TupleVar:
    """Return the pairwise mean value between the two tuples"""
    assert len(t1) == len(t2)
    return div_tuple(add_tuples(t1, t2), 2)


def std_dev_count(t: TupleVar) -> float:
    """Return number of std deviations represented in the set"""
    mt = mean_tuple(t)
    # Largest distance from mean could be negative
    return max(max(t) - mt, mt - min(t)) / np.std(t)  # type: ignore


# MAX
def max_tuples(t1: TupleVar, t2: TupleVar) -> TupleVar:
    """Return the pairwise max value between the two tuples"""
    assert len(t1) == len(t2)
    func: Callable[[float, float], float] = lambda x1, x2: max(x1, x2)
    return cast(TupleVar, tuple(map(func, t1, t2)))


# Trigonometry


def rad2deg(radians_: float) -> float:
    """Return the given radians converted to degrees"""
    return cast(float, np.rad2deg(radians_))


def deg2rad(degrees_: float) -> float:
    """Return the given degrees converted to radians"""
    return cast(float, np.deg2rad(degrees_))  # deg2rad returns float if input is a scaler


# force named parameter usage to avoid mix-up
def solve_adjacent(*, theta: float, opposite: float) -> float:
    """Return a = o / tan(Ѳ) (implied from on tan(Ѳ) = o/a)"""
    return opposite / cast(float, np.tan(theta))


# Distance between two points (or from (0, 0) to given point if one point passed)
def distance_2d(p1: Tuple[float, float], p2: Optional[Tuple[float, float]] = None) -> float:
    if p2 is None:
        p2 = (0, 0)
    (x1, y1) = p1
    (x2, y2) = p2
    return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)


# Geometry


def counterclockwise(a: Tuple[float, float], b: Tuple[float, float], c: Tuple[float, float]) -> bool:
    return (c[1] - a[1]) * (b[0] - a[0]) > (b[1] - a[1]) * (c[0] - a[0])


# Return true if line segments AB and CD intersect
def intersects(a: Tuple[float, float], b: Tuple[float, float], c: Tuple[float, float], d: Tuple[float, float]) -> bool:
    return counterclockwise(a, c, d) != counterclockwise(b, c, d) and counterclockwise(a, b, c) != counterclockwise(
        a, b, d
    )


def point_euclidean_distance(p1: Tuple[float, float], p2: Tuple[float, float]) -> float:
    return cast(float, Point(*p1).distance(Point(*p2)))


def polygon_intersects(poly1: Sequence[Tuple[float, float]], poly2: Sequence[Tuple[float, float]]) -> bool:
    return cast(bool, Polygon(poly1).intersects(Polygon(poly2)))


def polygon_contains_point(poly: Sequence[Tuple[float, float]], point: Tuple[float, float]) -> bool:
    return cast(bool, Polygon(poly).contains(Point(point)))


def polygon_contains(poly1: Sequence[Tuple[float, float]], poly2: Sequence[Tuple[float, float]]) -> bool:
    """
    Returns true if poly2 is container within poly1
    :param poly1: Polygon or Ring Container
    :param poly2: Polygon or Ring Content
    :return:
    """
    p1 = Polygon(poly1)
    p2 = Polygon(poly2)
    return cast(bool, p1.contains(p2))


# Coordinate System transforms


def cart2pol(x: float, y: float) -> Tuple[float, float]:
    rho = np.sqrt(x ** 2 + y ** 2)
    phi = np.arctan2(y, x)
    return rho, phi


def pol2cart(rho: float, phi: float) -> Tuple[float, float]:
    x = rho * np.cos(phi)
    y = rho * np.sin(phi)
    return x, y


def pol2heading(*, angle_deg: float) -> float:
    # y axis is trigonometry is 90deg. So make it 0
    trig_angle_deg = (angle_deg - 90) % 360

    # we consider positive angle to go counterclockwise but in heading/bearing, positive is clockwise
    return 360 - trig_angle_deg


# defer adding other trig functions until needed later, as we may change function signatures / patterns

# Ranges


def scale_value(value: float, min_range: float, max_range: float, scale_min: float, scale_max: float) -> float:
    """
    Scale a value that is between the specified ranges to be within the scaled ranges.

    Parameters:
        value (float): The value to scale.
        min_range (float): The lowest value that's expected for the value. Values lower than this
                           will be clamped to this value.
        max_range (float): The highest value that's expected for the value. Values higher than this
                           will be clamped to this value.
        scale_min (float): The corresponding scaled value to the min_range. Values at or below
                           min_range will be mapped to this value.
        scale_max (float): The corresponding scaled value to the max_range. Values are or above
                           max_range will be mapped to this value.

    Returns:
        A rescaled value to be within the bounds [scale_min, scale_max]
    """
    assert min_range <= max_range
    if value < min_range:
        value = min_range
    if value > max_range:
        value = max_range

    denominator = max_range - min_range
    if denominator > 0:
        pct = (value - min_range) / denominator
    else:
        pct = 0
    return (pct * (scale_max - scale_min)) + scale_min


def fit_value_between(value: float, min_max: Union[Tuple[float, float], List[float]]) -> float:
    """
    :param value: Value to fit between min_max
    :param min_max: Two element iterable specifying min/max. Can be given in either order.
    :return: if value > max(min_max), returns max(min_max). If value < min(min_max), returns min(min_max). Else value.
    """
    assert len(min_max) == 2, f"Invalid input length: {len(min_max)}"
    # robust against [min, max] or [max, min] ordering of input min_max
    return min(max(min_max), max(min(min_max), value))


# Quaternions


def quaternion_to_euler_angles(q: Quaternion) -> np.ndarray:  # type: ignore
    """
    Get the Euler angles representation of a quaternion.
    Angles are in the aerospace convention: zyx, which means we first Yaw about the Z axis,
    Pitch about the Y axis, and Roll about the X axis.
    Angles are in radians.

    Equations derived from
    http://www.euclideanspace.com/maths/geometry/rotations/conversions/quaternionToEuler/

    and specifically the paper linked by Noel Hughes
    http://www.euclideanspace.com/maths/geometry/rotations/conversions/quaternionToEuler/quat_2_euler_paper_ver2-1.pdf

    Parameters:
        q (Quaternion): The quaternion to convert.

    Returns:
        A 3-by numpy array of Roll, Pitch, and Yaw.
    """
    q = q.unit
    roll_rad = np.arctan2(2 * (q.w * q.x + q.y * q.z), 1 - 2 * (q.x ** 2 + q.y ** 2))

    sin_pitch = 2 * (q.w * q.y - q.z * q.x)
    if np.fabs(sin_pitch) >= 1:
        # Hit the gimbal lock singularity, so have to deal with it in some sane way.
        pitch_rad = np.copysign(np.pi / 2, sin_pitch)
    else:
        pitch_rad = np.arcsin(sin_pitch)

    yaw_rad = np.arctan2(2 * (q.w * q.z + q.x * q.y), 1 - 2 * (q.y ** 2 + q.z ** 2))

    return np.array([roll_rad, pitch_rad, yaw_rad]).reshape(3, 1)


def heading(orientation: Quaternion) -> float:
    """
    Calculate the heading of an orientation quaternion. It's assumed that this is a quaternion in the world frame such
    that when it's converted to euler angles, the "yaw" is in the same plane as heading. This function will convert that
    yaw angle into a heading following the usual heading conventions of clockwise from 0 degrees at due North.

    Parameters:
        orientation (Quaternion): The orientation from which to extract a heading.

    Returns:
        float: The heading in degrees.
    """
    euler_angles = quaternion_to_euler_angles(orientation).T[0]
    yaw = euler_angles[2]
    heading_rad = np.pi / 2 - yaw
    heading_wrapped_rad = (2 * np.pi + heading_rad) % (2 * np.pi)
    return float(np.rad2deg(heading_wrapped_rad))  # rad2deg returns float if input is a scaler


def heading_error_angle_rad(*, heading: float, target: float) -> float:

    # normalize target as 0 offset
    ndiff = target - heading

    # move to [0:360]
    ndiff = (ndiff + 360) % 360

    # angles > 180 go the other way
    if ndiff > 180:
        ndiff -= 360

    # make radians
    error_angle = (ndiff / 180) * math.pi

    # return as correction
    return error_angle


class MovingAverage:
    def __init__(
        self, *, initial_value: float, smooth_factor: Union[float, Callable[[], float]], pre_initialized: bool = False
    ):
        self._initialized = pre_initialized
        self._val: float = initial_value

        # allow clients to pass in a callback if they want to modify it dynamically
        self._smooth_factor: Callable[[], float]
        if isinstance(smooth_factor, (float, int)):
            self._smooth_factor = lambda: cast(float, smooth_factor)
        else:
            self._smooth_factor = smooth_factor
        self.smooth_factor()

    def override_value(self, value: float) -> None:
        self._val = value

    def reset(self) -> None:
        self._initialized = False

    @property
    def value(self) -> float:
        return self._val

    def smooth_factor(self) -> float:
        smooth_factor = self._smooth_factor()
        if not isinstance(smooth_factor, (int, float)) and 0 <= smooth_factor <= 1:
            raise ValueError(f"Bad smooth factor: {smooth_factor}")
        return smooth_factor

    def __format__(self, format_spec: str) -> str:
        return self.value.__format__(format_spec)

    def __str__(self) -> str:
        return str(self.value)

    def update(self, value: float) -> float:
        if not self._initialized:
            self._val = value
            self._initialized = True
        else:
            self._val = self.dry_update(value)
        return self._val

    def dry_update(self, value: float) -> float:
        smooth_factor = self.smooth_factor()
        result = (self._val * smooth_factor) + (value * (1.0 - smooth_factor))
        return result

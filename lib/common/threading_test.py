from typing import Generator

from lib.common.threading import <PERSON><PERSON><PERSON>hread, join_threads, start_threads


def thread_target_func() -> int:
    return 1 + 1


def generator_create_threads() -> Generator[MakaThread, None, None]:
    thread1 = MakaThread(name="test-1", target=thread_target_func)
    yield thread1

    thread2 = MakaThread(name="test-2", target=thread_target_func)
    yield thread2


def generator_create_and_start_threads() -> Generator[MakaThread, None, None]:
    thread1 = MakaThread(name="test-1", target=thread_target_func)
    thread1.start()
    yield thread1

    thread2 = MakaThread(name="test-2", target=thread_target_func)
    thread2.start()
    yield thread2


def test_start_threads_with_generator_and_join_threads_with_list() -> None:
    threads = start_threads(generator_create_threads)
    assert len(threads) == len([t for t in generator_create_threads()])
    join_threads(threads)


def test_join_threads_with_generator() -> None:
    join_threads(generator_create_and_start_threads)


def test_start_threads_and_join_threads_with_list() -> None:
    l_threads = [t for t in generator_create_threads()]
    threads = start_threads(l_threads)
    assert len(threads) == len(l_threads)
    join_threads(threads)


def test_start_threads_and_join_threads_with_individual_args() -> None:
    l_threads = [t for t in generator_create_threads()]
    assert len(l_threads) == 2
    threads = start_threads(l_threads[0], l_threads[1])
    assert len(threads) == len(l_threads)
    join_threads(threads[0], threads[1])

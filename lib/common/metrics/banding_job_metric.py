import asyncio
from typing import Dict

import aioredis

from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.job_metric import JobMetric
from lib.common.metrics.utils import get_banding_def_name

LOG = get_logger(__name__)


class BandingJobMetric(JobMetric):
    def __init__(self, redis: aioredis.Redis):
        super().__init__("BandingJobMetric")
        self._redis = redis
        self._enabled = False
        self._curr_config_name = ""

    async def _do_reload(self, metrics: Dict[str, str]) -> None:
        LOG.info(f"JobMetrics: Banding reloading with paused={self._paused}, metrics={metrics}")
        self._enabled = metrics["banding_enabled"] == "True" if "banding_enabled" in metrics else False
        self._curr_config_name = metrics["banding_config_name"] if "banding_config_name" in metrics else ""

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        key_map["banding_enabled"] = str(self._enabled)
        key_map["banding_config_name"] = self._curr_config_name

    async def _loop(self) -> None:
        with bot_stop_handler.scoped_bot_stop_blocker("banding_job_metric") as bot_stop:
            while not bot_stop.is_stopped():
                try:
                    if not await self.paused():
                        async with self._lock:
                            enabled = self._enabled
                        if not enabled:
                            enabled = await self._redis.get("banding/enabled") == "1"
                        curr_config_name = await get_banding_def_name(self._redis)
                        async with self._lock:
                            self._enabled = enabled
                            self._curr_config_name = curr_config_name
                except Exception:
                    LOG.exception("Metric reporting loop failed")
                await asyncio.sleep(10)

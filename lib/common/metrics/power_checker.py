import asyncio
from typing import Optional, Union, cast

from ping3 import ping

from hardware_manager.python.client import HardwareManagerClient
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.generation import get_command_ip, is_bud, is_reaper, rows
from lib.common.logging import get_logger
from lib.common.robot_definition.pybind.robot_definition_python import RobotDefinition
from lib.common.tasks.owner import Owner

LOG = get_logger(__name__)

RESP_TYPE = Optional[Union[bool, float]]


class PowerChecker(Owner):
    def __init__(self, rate_ms: int = 10000) -> None:
        self._rate = rate_ms / 1000
        self._hw_client = HardwareManagerClient(hostname=get_command_ip())
        self._is_bud = is_bud()
        if is_reaper():
            self._rows = RobotDefinition.get().get_all_computer_addresses()
            LOG.info(f"Reaper detected, using all computer addresses: {self._rows}")
        else:
            self._rows = rows()
        self._task: Optional[asyncio.Task[None]] = None
        self._powered = False
        self._estopped = False

    @property
    def powered(self) -> bool:
        return self._powered

    @property
    def estopped(self) -> bool:
        return self._estopped

    async def start(self) -> None:
        if self._task is None:
            self._task = asyncio.get_event_loop().create_task(self._loop())

    async def stop(self) -> None:
        if self._task is not None:
            self._task.cancel()
            await self._task
            self._task = None

    async def async_ping(self, ip: str) -> bool:
        resp = await asyncio.get_event_loop().run_in_executor(None, lambda: cast(RESP_TYPE, ping(ip, timeout=1)))
        return bool(resp)

    async def _ping_rows(self) -> bool:
        try:
            responses = await asyncio.gather(*[self.async_ping(ip) for _, ip in self._rows.items()])
            for resp in responses:
                if resp:
                    # if any row is available then we are online
                    return True
        except Exception as e:
            LOG.warning(f"Failed to ping rows with err: {e}")
        return False

    async def _get_240_state(self) -> bool:
        try:
            status = await self._hw_client.supervisory_status()
            return cast(bool, status["main_contactor_status_fb"])
        except Exception as e:
            LOG.warning(f"Failed to get 240v state with err: {e}")
            LOG.info("Attempting to detect up state with row ping")
            return await self._ping_rows()

    async def _get_estop_state(self) -> bool:
        try:
            safety_status = await self._hw_client.safety_status()
            return safety_status.estopped
        except Exception as e:
            LOG.warning(f"Failed to get e-stopped state err: {e}")
        return False

    async def _set_240_state(self) -> None:
        if self._is_bud:
            self._powered = True  # If running then 240v is up on a bud
            return
        self._powered = await self._get_240_state()

    async def _set_estop_state(self) -> None:
        self._estopped = await self._get_estop_state()

    async def _loop(self) -> None:
        while not bot_stop_handler.stopped:
            await asyncio.gather(self._set_240_state(), self._set_estop_state())
            await asyncio.sleep(self._rate)

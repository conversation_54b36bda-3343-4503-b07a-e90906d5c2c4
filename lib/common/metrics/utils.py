from typing import Optional, cast

import aioredis
from google.protobuf import json_format

from generated.frontend.proto.banding_pb2 import BandingDef


async def get_active_model_id(redis: aioredis.Redis) -> Optional[str]:
    return cast(Optional[str], await redis.get("/almanac/active_model_id"))


async def get_active_crop_id(redis: aioredis.Redis) -> Optional[str]:
    return cast(Optional[str], await redis.get("/almanac/active_crop_id"))


async def get_crop_model_id(redis: aioredis.Redis) -> str:
    crop = await get_active_crop_id(redis)
    model = await get_active_model_id(redis)
    if crop is None:
        crop = "NONE"
    if model is None:
        model = "NONE"
    return f"{crop}:{model}"


async def get_banding_def_name(redis: aioredis.Redis) -> str:
    curr_config_name = ""
    curr_config_uuid = await redis.get("banding/active_def_uuid")
    if curr_config_uuid is not None:
        curr_config = BandingDef()
        json_format.Parse(await redis.hget("banding/banding_defs_uuid", curr_config_uuid), curr_config)
        curr_config_name = curr_config.name
    return curr_config_name

import asyncio
from typing import Dict

from config.client.cpp.config_client_python import ConfigTree
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.banding_percentage_checker import BandingPercentageChecker
from lib.common.metrics.daily_metric import DailyMetric

LOG = get_logger(__name__)


class BandingPercentageDailyMetric(DailyMetric):
    def __init__(self, common_conf: ConfigTree) -> None:
        super().__init__("BandingPercentageDailyMetric")
        self._checker = BandingPercentageChecker(common_conf)
        self._percentage = 0.0

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        key_map["banding_percentage"] = str(self._percentage)

    async def _loop(self) -> None:
        with bot_stop_handler.scoped_bot_stop_blocker("banding_daily_metric") as bot_stop:
            while not bot_stop.is_stopped():
                try:
                    new_percentage = await self._checker.get_percentage_banded()
                    async with self._lock:
                        self._percentage = new_percentage
                except Exception as e:
                    LOG.info(f"BandingPercentage failing Err: {e}")
                await asyncio.sleep(10)

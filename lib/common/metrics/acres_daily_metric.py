import asyncio
from collections import defaultdict
from typing import DefaultDict, Dict

import aioredis

from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.daily_metric import DailyMetric
from lib.common.metrics.utils import get_crop_model_id
from metrics.pybind.metrics_python import DailyTimezone

LOG = get_logger(__name__)

EXP_TIME = 864000


def in_prog_key(cur_day: str) -> str:
    return f"{cur_day}/area_weeded_in_progress"


class AcresDailyMetric(DailyMetric):
    def __init__(self, redis: aioredis.Redis, dtz: DailyTimezone) -> None:
        super().__init__("Acres")
        self._redis = redis
        self._dtz = dtz
        self._last_val = 0.0
        self._per_id: DefaultDict[str, float] = defaultdict(float)

    async def _load(self, cur_day: str) -> None:
        while not bot_stop_handler.stopped:
            try:
                in_progress_data: Dict[str, str] = await self._redis.hgetall(in_prog_key(cur_day))
                break
            except Exception as e:
                LOG.info(f"Failed to initialize data: {e}")
        async with self._lock:
            for key, val in in_progress_data.items():
                if key == "latest":
                    self._last_val = float(val)
                else:
                    self._per_id[key] = float(val)

    async def _reset(self) -> None:
        async with self._lock:
            self._last_val = 0.0
            self._per_id = defaultdict(float)

    async def _loop(self) -> None:
        cur_day = self._dtz.get_day()
        await self._load(cur_day)
        with bot_stop_handler.scoped_bot_stop_blocker("area_weeded_metric") as bot_stop:
            while not bot_stop.is_stopped():
                acres_str = await self._redis.get(f"{self._dtz.get_day()}/commander/area_weeded_today")
                if acres_str is not None:
                    acres = float(acres_str)
                    async with self._lock:
                        delta = acres - self._last_val
                        self._last_val = acres
                    try:
                        key = await get_crop_model_id(self._redis)
                        key = f"acres_weeded:{key}"
                        async with self._lock:
                            self._per_id[key] += delta
                            acres_weeded = self._per_id[key]
                            last_val = self._last_val
                        await self._redis.hset(in_prog_key(cur_day), key, acres_weeded)
                        await self._redis.hset(in_prog_key(cur_day), "latest", last_val)
                        await self._redis.expire(in_prog_key(cur_day), EXP_TIME)
                    except Exception as e:
                        LOG.warn(f"Failed to update in progress data Error={e}")
                day = self._dtz.get_day()
                if day != cur_day:
                    cur_day = day
                    await self._reset()
                await asyncio.sleep(10)

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        key_map["acres_weeded"] = str(self._last_val)
        for key, val in self._per_id.items():
            key_map[key] = str(val)

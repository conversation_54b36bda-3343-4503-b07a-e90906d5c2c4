import asyncio
from typing import Dict

import aioredis

from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.daily_metric import DailyMetric
from lib.common.metrics.distance_checker import DistanceChecker
from lib.common.metrics.redis_weeding_state import is_weeding
from metrics.pybind.metrics_python import DailyTimezone

LOG = get_logger(__name__)
EXP_TIME = 864000


class DistanceDailyMetric(DailyMetric):
    def __init__(self, redis: aioredis.Redis, dtz: DailyTimezone) -> None:
        super().__init__("Uptime")
        self._redis = redis
        self._dtz = dtz
        self._checker = DistanceChecker()
        self._distance = 0.0

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        key_map["distance_weeded"] = str(self._distance)

    async def _loop(self) -> None:
        cur_day = self._dtz.get_day()
        pos = await self._checker.get_pos()
        while not bot_stop_handler.stopped:
            try:
                distance_temp = await self._redis.get(f"{cur_day}/distance")
                break
            except Exception as e:
                LOG.info(f"Failed to get distance data Err: {e}")
        if distance_temp is not None:
            async with self._lock:
                self._distance = float(distance_temp)
        with bot_stop_handler.scoped_bot_stop_blocker("distance_daily_metric") as bot_stop:
            while not bot_stop.is_stopped():
                weeding = await is_weeding(self._redis)
                cur = await self._checker.get_pos()
                if weeding:
                    travelled = self._checker.avg_dist(pos, cur) / 1000.0  # convert to meters
                    if travelled > 0:
                        async with self._lock:
                            self._distance += travelled
                            distance = self._distance
                        try:
                            await self._redis.set(f"{cur_day}/distance", str(distance), ex=EXP_TIME)
                        except Exception as e:
                            LOG.info(f"Failed to set distance Err: {e}")
                pos = cur
                day = self._dtz.get_day()
                if day != cur_day:
                    async with self._lock:
                        self._distance = 0.0
                        cur_day = day
                await asyncio.sleep(10)

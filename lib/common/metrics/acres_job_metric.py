import asyncio
from typing import Dict

import aioredis

from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.job_metric import JobMetric

LOG = get_logger(__name__)


class AcresJobMetric(JobMetric):
    def __init__(self, redis: aioredis.Redis) -> None:
        super().__init__("AcresJobMetric")
        self._redis = redis
        self._acres = 0.0

    async def _do_reload(self, metrics: Dict[str, str]) -> None:
        LOG.info(f"JobMetrics: Acres reloading with paused={self._paused}, metrics={metrics}")
        self._acres = float(metrics["acres_weeded"]) if "acres_weeded" in metrics else 0.0

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        key_map["acres_weeded"] = str(self._acres)

    async def _get_acres(self) -> float:
        area = await self._redis.get("commander/area_weeded_total")
        if area is None:
            return 0.0
        return float(area)

    async def _loop(self) -> None:
        prev = await self._get_acres()
        with bot_stop_handler.scoped_bot_stop_blocker("acres_job_metric") as bot_stop:
            while not bot_stop.is_stopped():
                if not await self.paused():
                    cur = await self._get_acres()
                    delta = cur - prev
                    prev = cur
                    async with self._lock:
                        self._acres += delta
                else:
                    prev = await self._get_acres()
                await asyncio.sleep(10)

import asyncio
from time import monotonic
from typing import Dict, List, Optional, Set, Tuple

import aioredis

from lib.common.asyncio.event_loop import use_specific_loop
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.power_checker import PowerChecker
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.tasks.owner import Owner
from lib.common.time.time import maka_control_timestamp_ms

LOG = get_logger(__name__)
EXPIRE_TIME = 2592000


class LaserDetails:
    def __init__(self, row: int, slot: int, serial: str, timestamp_sec: int):
        self.row = row
        self.slot = slot
        self.serial = serial
        self.timestamp_sec = timestamp_sec


class LaserTimeMetric(Owner):
    SLOT_TO_SERIAL_MAP = "/lasers/ids"
    LIFE_TRACKING = "/lasers/usage_time"
    EXPIRE_TRACKING = "/lasers/old_serials"
    INSTALL_TRACKING = "/lasers/install_time"

    def __init__(self, redis: aioredis.Redis, checker: PowerChecker) -> None:
        self._task: Optional[asyncio.Task[None]] = None
        self._redis = redis
        self._checker = checker
        self._lifetimes: Dict[str, int] = {}
        self._event_loop = get_event_loop_by_name()
        with use_specific_loop(self._event_loop):
            self._update_early = asyncio.Event()

    @property
    def lifetimes(self) -> Dict[str, int]:
        return self._lifetimes

    async def set_mapping(self, serial: str, row: int, pos: int) -> None:
        if serial:
            await self._redis.hset(self.SLOT_TO_SERIAL_MAP, f"row_{row}/pos_{pos}", serial)
            await self._redis.hset(self.INSTALL_TRACKING, serial, maka_control_timestamp_ms() // 1000)
        else:
            await self._redis.hdel(self.SLOT_TO_SERIAL_MAP, f"row_{row}/pos_{pos}")
        self._update_early.set()

    async def override_mapping(self, serial: str, row: int, pos: int, lifetime_s: int) -> None:
        if serial:
            lasers = await self.get_lasers()
            key = (row, pos)
            if key in lasers:
                old_serial = lasers[key]
                install_time = await self.get_laser_install_time(old_serial)
                if install_time == 0:
                    LOG.error("Install time was 0, assuming new install")
                    install_time = maka_control_timestamp_ms() // 1000
                LOG.info(f"Setting Install Date for Laser: {serial} to {install_time}")
                await self._redis.hset(self.INSTALL_TRACKING, serial, install_time)
            else:
                # No old serial found
                LOG.error("Couldn't find old laser so assuming new install")
                await self._redis.hset(self.INSTALL_TRACKING, serial, maka_control_timestamp_ms() // 1000)

            await self._redis.hset(self.SLOT_TO_SERIAL_MAP, f"row_{row}/pos_{pos}", serial)
            await self._redis.hset(self.LIFE_TRACKING, serial, lifetime_s * 1000)
        else:
            await self._redis.hdel(self.SLOT_TO_SERIAL_MAP, f"row_{row}/pos_{pos}")
        self._update_early.set()

    async def get_lasers(self) -> Dict[Tuple[int, int], str]:
        mapping: Dict[str, str] = await self._redis.hgetall(self.SLOT_TO_SERIAL_MAP)
        lasers = {}
        for k, v in mapping.items():
            row, pos = k.split("/", 1)
            row = row.replace("row_", "")
            pos = pos.replace("pos_", "")
            lasers[(int(row), int(pos))] = v
        return lasers

    async def get_laser_install_time(self, serial: str) -> int:
        install_times: Dict[str, str] = await self._redis.hgetall(self.INSTALL_TRACKING)
        if serial in install_times:
            return int(install_times[serial])
        LOG.error(f"Couldn't find install time for laser: {serial} in {install_times}")
        return 0

    async def get_laser_details(self) -> Tuple[List[LaserDetails], List[LaserDetails]]:
        installed = await self.get_lasers()
        intsall_times: Dict[str, str] = await self._redis.hgetall(self.INSTALL_TRACKING)
        installed_details: List[LaserDetails] = []
        for pos, serial in installed.items():
            install_ts = 0
            if serial in intsall_times:
                install_ts = int(intsall_times[serial])
            installed_details.append(LaserDetails(pos[0], pos[1], serial, install_ts))
        removed = await self._get_laser_expire_times()
        removed_details: List[LaserDetails] = []
        for serial, exp_time in removed.items():
            removed_details.append(LaserDetails(0, 0, serial, exp_time - EXPIRE_TIME))
        return (installed_details, removed_details)

    async def _get_current_lasers(self) -> Set[str]:
        try:
            mapping: Dict[str, str] = await self._redis.hgetall(self.SLOT_TO_SERIAL_MAP)
            return set(mapping.values())
        except Exception as e:
            LOG.warning(f"Failed to fetch current lasers err: {e}")
        return set()

    async def _get_laser_lifetimes(self) -> Dict[str, int]:
        try:
            lifetime: Dict[str, str] = await self._redis.hgetall(self.LIFE_TRACKING)
            return {k: int(v) for k, v in lifetime.items()}
        except Exception as e:
            LOG.warning(f"Failed to fetch current lasers lifetimes err: {e}")
        return {}

    async def _get_laser_expire_times(self) -> Dict[str, int]:
        try:
            expire: Dict[str, str] = await self._redis.hgetall(self.EXPIRE_TRACKING)
            return {k: int(v) for k, v in expire.items()}
        except Exception as e:
            LOG.warning(f"Failed to fetch expired lasers err: {e}")
        return {}

    async def _remove_expired_laser(self, serial: str) -> None:
        try:
            pass
            # Decided not to actually remove stuff so everything makes it to portal
            # await self._redis.hdel(self.LIFE_TRACKING, serial)
            # await self._redis.hdel(self.EXPIRE_TRACKING, serial)
        except Exception as e:
            LOG.warning(f"Failed to untrack {serial} err: {e}")

    async def _remove_expired_serials(self) -> Set[str]:
        tracked_serials = await self._get_laser_expire_times()
        now = maka_control_timestamp_ms() // 1000
        not_expired = set()
        for serial, exp in tracked_serials.items():
            if now > exp:
                await self._remove_expired_laser(serial)
            else:
                not_expired.add(serial)
        return not_expired

    async def _update(self, lifetimes: Dict[str, int], rm_from_not_used: Set[str], add_to_not_used: Set[str]) -> None:
        lifetimes_str = {k: str(v) for k, v in lifetimes.items()}
        expire = str(maka_control_timestamp_ms() // 1000 + EXPIRE_TIME)
        to_expire = {}
        unused = await self._get_laser_expire_times()
        for k in add_to_not_used:
            if k in unused:
                continue
            to_expire[k] = expire
        try:
            await self._redis.hmset(self.LIFE_TRACKING, lifetimes_str)
            if to_expire:
                await self._redis.hmset(self.EXPIRE_TRACKING, to_expire)
            for serial in rm_from_not_used:
                await self._redis.hdel(self.EXPIRE_TRACKING, serial)
            # Decided to remove the below so make sure all install / uninstall data makes it to portal
            # for serial in to_expire:
            #     await self._redis.hdel(self.INSTALL_TRACKING, serial)
        except Exception as e:
            LOG.warning(f"Failed to update tracking err: {e}")

    async def _loop(self) -> None:
        not_used = await self._remove_expired_serials()
        prev = monotonic()
        with bot_stop_handler.scoped_bot_stop_blocker("laser_time_metric") as bot_stop:
            while not bot_stop.is_stopped():
                self._update_early.clear()
                cur = monotonic()
                cur_lasers = await self._get_current_lasers()
                if cur_lasers:
                    lifetimes = await self._get_laser_lifetimes()
                    rm_from_not_used = cur_lasers.intersection(
                        not_used
                    )  # serials currently mapped but also in not used can occur if laser gets re-used
                    add_to_not_used = set(lifetimes.keys()).difference(
                        cur_lasers
                    )  # serials that were tracked in lifetime, but are no longer mapped, a laser was replaced
                    not_used.difference_update(
                        rm_from_not_used
                    )  # remove from not used set as this laser is no longer not used
                    not_used.update(add_to_not_used)  # add newly not used lasers
                    for expired in add_to_not_used:
                        del lifetimes[expired]
                    for new_serial in cur_lasers.difference(lifetimes.keys()):
                        lifetimes[new_serial] = 0
                    if self._checker.powered:
                        to_add = round((cur - prev) * 1000)
                        for key in lifetimes:
                            lifetimes[key] += to_add
                    await self._update(lifetimes, rm_from_not_used, add_to_not_used)
                    self._lifetimes = lifetimes
                prev = cur
                try:
                    await asyncio.wait_for(self._update_early.wait(), timeout=10)
                except asyncio.TimeoutError:
                    pass  # default case no changes just need to update

    async def _loop_catch(self) -> None:
        while not bot_stop_handler.stopped:
            try:
                await self._loop()
            except Exception as e:
                LOG.warning(f"Failed with unhandled exception {e}")

    async def start(self) -> None:
        if self._task is None:
            self._task = asyncio.get_event_loop().create_task(self._loop_catch())

    async def stop(self) -> None:
        if self._task is not None:
            self._task.cancel()
            await self._task
            self._task = None

    def __repr__(self) -> str:
        return self.__str__()

    def __str__(self) -> str:
        return "Laser time metric"

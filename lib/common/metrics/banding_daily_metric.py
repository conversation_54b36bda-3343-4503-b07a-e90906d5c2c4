import asyncio
from typing import Dict, Optional, cast

import aioredis

from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.daily_metric import DailyMetric
from lib.common.metrics.utils import get_banding_def_name
from metrics.pybind.metrics_python import DailyTimezone

LOG = get_logger(__name__)

EXP_TIME = 864000


class BandingDailyMetric(DailyMetric):
    def __init__(self, redis: aioredis.Redis, dtz: DailyTimezone) -> None:
        super().__init__("Banding")
        self._redis = redis
        self._dtz = dtz
        self._enabled = False
        self._curr_config_name = ""

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        key_map["banding_enabled"] = str(self._enabled)
        key_map["banding_config_name"] = self._curr_config_name

    async def _read_str(self, key: str) -> Optional[str]:
        try:
            val = await self._redis.get(key)
            return cast(Optional[str], val)
        except Exception as e:
            LOG.info(f"Failed to get {key} Err: {e}")
        return None

    async def _read_str_default(self, key: str, default: str) -> str:
        opt_val = await self._read_str(key)
        if opt_val is not None:
            return opt_val
        return default

    async def _read_str_int_as_bool(self, key: str) -> bool:
        val_str = await self._read_str_default(key, "0")
        return bool(int(val_str))

    async def _write_bool_as_int(self, key: str, val: bool) -> None:
        str_val = "1" if val else "0"
        try:
            await self._redis.set(key, str_val, ex=EXP_TIME)
        except Exception as e:
            LOG.info(f"Failed to set {key} to {val} Err: {e}")

    async def _was_banding_enabled(self, cur_day: str) -> bool:
        return await self._read_str_int_as_bool(f"{cur_day}/banding/enabled")

    async def _is_banding_enabled(self) -> bool:
        return await self._read_str_int_as_bool("banding/enabled")

    async def _loop(self) -> None:
        cur_day = self._dtz.get_day()
        async with self._lock:
            self._enabled = await self._was_banding_enabled(cur_day)
        with bot_stop_handler.scoped_bot_stop_blocker("banding_daily_metric") as bot_stop:
            while not bot_stop.is_stopped():
                try:
                    enabled = await self._is_banding_enabled()
                    day = self._dtz.get_day()
                    async with self._lock:
                        if day != cur_day:
                            self._enabled = enabled
                            cur_day = day
                        else:
                            self._enabled |= enabled  # latching to stay enabled if ever true for today
                        enabled_today = self._enabled
                    if enabled_today:
                        await self._write_bool_as_int(f"{cur_day}/banding/enabled", True)
                    async with self._lock:
                        self._curr_config_name = await get_banding_def_name(self._redis)
                except Exception:
                    LOG.exception("Metric reporting loop failed")
                await asyncio.sleep(10)

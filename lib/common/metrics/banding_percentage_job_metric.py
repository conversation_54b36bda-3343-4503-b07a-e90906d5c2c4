import asyncio
from typing import Dict

from config.client.cpp.config_client_python import ConfigTree
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.banding_percentage_checker import BandingPercentageChecker
from lib.common.metrics.job_metric import JobMetric

LOG = get_logger(__name__)


class BandingPercentageJobMetric(JobMetric):
    def __init__(self, common_conf: ConfigTree):
        super().__init__("BandingPercentageJobMetric")
        self._checker = BandingPercentageChecker(common_conf)
        self._percentage = 0.0

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        key_map["banding_percentage"] = str(self._percentage)

    async def _do_reload(self, metrics: Dict[str, str]) -> None:
        LOG.info(f"BandingPercentage reloaded with {metrics}")
        self._percentage = float(metrics["banding_percentage"]) if "banding_percentage" in metrics else 0.0

    async def _loop(self) -> None:
        with bot_stop_handler.scoped_bot_stop_blocker("banding_job_metric") as bot_stop:
            while not bot_stop.is_stopped():
                try:
                    if not await self.paused():
                        p = await self._checker.get_percentage_banded()
                        async with self._lock:
                            self._percentage = p
                except Exception as e:
                    LOG.exception(e)

                await asyncio.sleep(10)

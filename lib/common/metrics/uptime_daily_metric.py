import asyncio
from collections import defaultdict
from time import monotonic
from typing import DefaultDict, Dict

import aioredis

from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.daily_metric import DailyMetric
from lib.common.metrics.power_checker import PowerChecker
from lib.common.metrics.redis_weeding_state import is_weeding
from lib.common.metrics.utils import get_crop_model_id
from metrics.pybind.metrics_python import DailyTimezone

LOG = get_logger(__name__)

EXP_TIME = 864000


def in_prog_key(cur_day: str) -> str:
    return f"{cur_day}/time_used_in_progress"


class UptimeDailyMetric(DailyMetric):
    def __init__(self, redis: aioredis.Redis, dtz: DailyTimezone, checker: PowerChecker) -> None:
        super().__init__("Uptime")
        self._redis = redis
        self._dtz = dtz
        self._checker = checker
        self._uptime = 0.0
        self._active_uptime = 0.0
        self._per_id: DefaultDict[str, float] = defaultdict(float)

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        key_map["uptime_seconds"] = str(self._uptime)
        key_map["weeding_uptime_seconds"] = str(self._active_uptime)
        for key, val in self._per_id.items():
            key_map[key] = str(val)

    async def _load(self, cur_day: str) -> None:
        while not bot_stop_handler.stopped:
            try:
                uptime_tmp = await self._redis.get(f"{cur_day}/uptime")
                active_uptime_tmp = await self._redis.get(f"{cur_day}/active_uptime")
                in_progress_data: Dict[str, str] = await self._redis.hgetall(in_prog_key(cur_day))
                break
            except Exception as e:
                LOG.info(f"Failed to initialize data: {e}")
        async with self._lock:
            if uptime_tmp is not None:
                self._uptime = float(uptime_tmp)
            if active_uptime_tmp is not None:
                self._active_uptime = float(active_uptime_tmp)
            for key, val in in_progress_data.items():
                self._per_id[key] = float(val)

    async def _reset(self) -> None:
        self._per_id = defaultdict(float)
        self._uptime = 0
        self._active_uptime = 0

    async def _loop(self) -> None:
        cur_day = self._dtz.get_day()
        await self._load(cur_day)
        prev = monotonic()
        with bot_stop_handler.scoped_bot_stop_blocker("uptime_daily_metric") as bot_stop:
            while not bot_stop.is_stopped():
                weeding = await is_weeding(self._redis)
                cur = monotonic()
                delta = cur - prev
                prev = cur
                if self._checker.powered:
                    crop_model_id = ""
                    if weeding:
                        crop_model_id = f"active_use:{await get_crop_model_id(self._redis)}"

                    async with self._lock:
                        self._uptime += delta
                        if weeding:
                            self._active_uptime += delta
                            if crop_model_id:
                                self._per_id[crop_model_id] += delta
                        uptime = self._uptime
                        active_uptime = self._active_uptime
                        crop_model_uptime = self._per_id[crop_model_id] if crop_model_id else 0.0

                    try:
                        await self._redis.set(f"{cur_day}/uptime", str(uptime), ex=EXP_TIME)
                        if weeding:
                            await self._redis.set(f"{cur_day}/active_uptime", str(active_uptime), ex=EXP_TIME)
                            if crop_model_id:
                                await self._redis.hset(in_prog_key(cur_day), crop_model_id, crop_model_uptime)
                                await self._redis.expire(in_prog_key(cur_day), EXP_TIME)
                    except Exception:
                        LOG.exception("Failed to set uptime metrics in Redis")
                day = self._dtz.get_day()
                if day != cur_day:
                    cur_day = day
                    await self._reset()
                await asyncio.sleep(10)

import asyncio
from typing import <PERSON>ple

from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from wheel_encoder.pybind.wheel_encoder_python import WheelEncoder

LOG = get_logger(__name__)


class DistanceChecker:
    def __init__(self) -> None:
        self._encoder = WheelEncoder.get()

    async def get_pos(self) -> Tuple[int, int, int, int]:
        while not bot_stop_handler.stopped:
            try:
                data = self._encoder.get_cur()
                return (data.front_left, data.front_right, data.back_left, data.back_right)
            except Exception as e:
                LOG.info(f"Failed to get current position Err: {e}")
                await asyncio.sleep(1)
        return (0, 0, 0, 0)

    def avg_dist(self, pos: Tuple[int, int, int, int], cur: <PERSON><PERSON>[int, int, int, int]) -> float:
        return self._encoder.avg_dist(pos, cur)

import asyncio
from typing import Dict

import aioredis

from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.distance_checker import DistanceChecker
from lib.common.metrics.job_metric import JobMetric
from lib.common.metrics.redis_weeding_state import is_weeding

LOG = get_logger(__name__)


class DistanceJobMetric(JobMetric):
    def __init__(self, redis: aioredis.Redis) -> None:
        super().__init__("DistanceJobMetric")
        self._redis = redis
        self._checker = DistanceChecker()
        self._distance = 0.0

    async def _do_reload(self, metrics: Dict[str, str]) -> None:
        LOG.info(f"JobMetrics: Distance reloading with paused={self._paused}, metrics={metrics}")
        self._distance = float(metrics["distance_weeded"]) if "distance_weeded" in metrics else 0.0

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        key_map["distance_weeded"] = str(self._distance)

    async def _loop(self) -> None:
        pos = await self._checker.get_pos()
        with bot_stop_handler.scoped_bot_stop_blocker("distance_job_metric") as bot_stop:
            while not bot_stop.is_stopped():
                if not await self.paused():
                    weeding = await is_weeding(self._redis)
                    cur = await self._checker.get_pos()
                    if weeding:
                        travelled = self._checker.avg_dist(pos, cur) / 1000.0  # convert to meters
                        async with self._lock:
                            self._distance += travelled
                    pos = cur
                else:
                    pos = await self._checker.get_pos()

                await asyncio.sleep(10)

import asyncio
from time import monotonic
from typing import Dict

import aioredis

from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.job_metric import JobMetric
from lib.common.metrics.power_checker import PowerChecker
from lib.common.metrics.redis_weeding_state import is_weeding

LOG = get_logger(__name__)


class UptimeJobMetric(JobMetric):
    def __init__(self, redis: aioredis.Redis, checker: PowerChecker):
        super().__init__("UptimeJobMetric")
        self._uptime = 0.0
        self._active_uptime = 0.0
        self._checker = checker
        self._redis = redis

    async def _do_reload(self, metrics: Dict[str, str]) -> None:
        LOG.info(f"JobMetrics: Uptime reloading with paused={self._paused}, metrics={metrics}")
        self._uptime = float(metrics["uptime_seconds"]) if "uptime_seconds" in metrics else 0.0
        self._active_uptime = float(metrics["weeding_uptime_seconds"]) if "weeding_uptime_seconds" in metrics else 0.0

    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        key_map["uptime_seconds"] = str(self._uptime)
        key_map["weeding_uptime_seconds"] = str(self._active_uptime)

    async def _loop(self) -> None:
        prev = monotonic()
        with bot_stop_handler.scoped_bot_stop_blocker("uptime_job_metric") as bot_stop:
            while not bot_stop.is_stopped():
                if not await self.paused():
                    weeding = await is_weeding(self._redis)
                    cur = monotonic()
                    delta = cur - prev
                    prev = cur
                    if self._checker.powered:
                        async with self._lock:
                            self._uptime += delta
                            if weeding:
                                self._active_uptime += delta
                else:
                    prev = monotonic()
                await asyncio.sleep(10)

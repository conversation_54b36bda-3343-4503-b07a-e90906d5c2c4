from typing import Generator

import grpc

from config.client.cpp.config_client_python import ConfigTree
from lib.common.generation import is_reaper, rows
from lib.common.logging import get_logger
from lib.common.robot_definition.pybind.robot_definition_python import RobotDefinition
from weed_tracking.python.client.weed_tracking_client import WeedTrackingClient

LOG = get_logger(__name__)


def get_row_ips(common_conf: ConfigTree) -> Generator[str, None, None]:
    disabled = common_conf.get_node("disabled_rows")
    disabled_names = set([f"row{ch.get_name()}" for ch in disabled.get_children_nodes()])
    aimbot_addresses = rows()
    if is_reaper():
        aimbot_addresses = RobotDefinition.get().get_aimbot_addresses()
        LOG.info(f"Is Reaper, aimbot addresses: {aimbot_addresses}")
    for name, ip in aimbot_addresses.items():
        if name not in disabled_names:
            yield ip


class BandingCheckerError(Exception):
    def __init__(self, grpc_ex: grpc.aio.AioRpcError, client: WeedTrackingClient) -> None:
        self.code = grpc_ex.code()
        self.hostname = client.hostname

    def __repr__(self) -> str:
        return super().__str__()

    def __str__(self) -> str:
        return f"BandCheckerError(code={self.code}, hostname={self.hostname})"


class BandingPercentageChecker:
    def __init__(self, common_conf: ConfigTree):
        self._common_conf = common_conf
        self._wt_clients = [WeedTrackingClient(hostname=ip) for ip in get_row_ips(common_conf)]

    async def get_percentage_banded(self) -> float:
        row_width_mm = self._common_conf.get_node("row_width_in").get_float_value() * 25.4
        total_width_mm = 0.0
        bands_width_mm = 0.0
        for wt_client in self._wt_clients:
            total_width_mm += row_width_mm
            try:
                bands = await wt_client.get_bands()
            except grpc.aio.AioRpcError as ex:
                raise BandingCheckerError(ex, wt_client)
            if not bands.banding_enabled or not bands.row_has_bands_defined:
                bands_width_mm += row_width_mm
            else:
                for band in bands.bands:
                    bands_width_mm += band.width_mm
        percent = 100 * (bands_width_mm / total_width_mm)
        return percent

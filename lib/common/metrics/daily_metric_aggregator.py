import asyncio
from copy import deepcopy
from typing import Dict, List, Optional

import aioredis

from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.metrics.daily_metric import DailyMetric
from lib.common.tasks.owner import Owner
from metrics.pybind.metrics_python import DailyTimezone

LOG = get_logger(__name__)


class DailyMetricAggregator(Owner):
    USED_KEYS_SET = "daily_metrics/used_keys"

    def __init__(self, redis: aioredis.Redis) -> None:
        self._redis = redis
        self._dtz = DailyTimezone(owner=True)
        self._computed: Dict[str, Dict[str, str]] = {}
        self._task: Optional[asyncio.Task[None]] = None
        self._async_inited = False
        self._metrics: List[DailyMetric] = []

    def add_metric(self, metric: DailyMetric) -> None:
        self._metrics.append(metric)

    def _build_hash_key(self, day: Optional[str] = None) -> str:
        if day is None:
            day = self.dtz.get_day()
        return f"{day}/daily_metrics"

    async def _init_async(self) -> None:
        if self._async_inited:
            return
        self._lock = asyncio.Lock()

        # Keep at end of function
        self._async_inited = True

    async def start(self) -> None:
        try:
            await self._init_async()
            await asyncio.gather(*[metric.start() for metric in self._metrics])
            if self._task is None:
                self._task = asyncio.get_event_loop().create_task(self._loop())
        except Exception:
            LOG.exception("Error starting metrics task")

    async def stop(self) -> None:
        await asyncio.gather(*[metric.stop() for metric in self._metrics])
        if self._task is not None:
            self._task.cancel()
            await self._task
            self._task = None

    async def get_metrics(self) -> Dict[str, Dict[str, str]]:
        async with self._lock:
            return deepcopy(self._computed)

    async def remove_keys(self, keys: List[str]) -> None:
        assert self._redis is not None
        today = self.dtz.get_day()
        rm_keys = map(lambda day: self._build_hash_key(day), filter(lambda key: key != today, keys))
        for key in rm_keys:
            LOG.info(f"key to rm = {key}")
            await self._redis.delete(key)
            await self._redis.srem(self.USED_KEYS_SET, key)

    async def _loop(self) -> None:
        assert self._redis is not None
        with bot_stop_handler.scoped_bot_stop_blocker("daily_metrics_aggregator") as bot_stop:
            while not bot_stop.is_stopped():
                try:
                    today_key = self._build_hash_key()
                    prev_keys = set(await self._redis.smembers(self.USED_KEYS_SET))
                    if today_key in prev_keys:
                        prev_keys.remove(today_key)
                    to_send: Dict[str, Dict[str, str]] = {}
                    for key in prev_keys:
                        if await self._redis.exists(key) > 0:
                            LOG.info(f"adding prev key {key}")
                            to_send[key.split("/")[0]] = await self._redis.hgetall(key)
                    await self._redis.sadd(self.USED_KEYS_SET, today_key)
                    curr_metrics: Dict[str, str] = {}
                    for metric in self._metrics:
                        try:
                            await metric.add_metrics(curr_metrics)
                        except Exception as e:
                            LOG.info(f"Exception occured adding metrics for '{metric}'. Err: {e}")
                    for key, val in curr_metrics.items():
                        await self._redis.hset(today_key, key, val)
                    async with self._lock:
                        self._computed = deepcopy(to_send)
                        self._computed[today_key.split("/")[0]] = deepcopy(curr_metrics)
                except Exception as e:
                    LOG.info(f"Daily metrics aggregator exception: {e}")
                await asyncio.sleep(5)

    @property
    def dtz(self) -> DailyTimezone:
        return self._dtz

from typing import List, Optional, TypeVar, Union

T = TypeVar("T")


def flatten(x: Union[T, List[T], List[List[T]], List[List[List[T]]]], result: Optional[List[T]] = None) -> List[T]:
    def _flatten(x: Union[T, List[T], List[List[T]], List[List[List[T]]]], result: Optional[List[T]] = None) -> List[T]:
        if result is None:
            result = []
        if isinstance(x, list):
            for e in x:
                _flatten(e, result)
        else:
            result.append(x)
        return result

    return _flatten(x)

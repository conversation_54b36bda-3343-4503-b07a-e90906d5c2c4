from collections import deque
from typing import Deque, cast


class MovingAverage:
    def __init__(self, window_size: int) -> None:
        self._window: Deque[float] = deque(maxlen=window_size)
        self._total = 0.0

    def push(self, val: float) -> None:
        if len(self._window) == self._window.maxlen:
            self._total -= self._window.popleft()
        self._total += val
        self._window.append(val)

    def avg(self) -> float:
        return self._total / len(self._window)

    @property
    def window_size(self) -> int:
        return cast(int, self._window.maxlen)  # maxlen is required so will not be None

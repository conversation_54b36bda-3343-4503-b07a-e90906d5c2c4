from collections import OrderedDict
from typing import Any, Generic, Optional, TypeVar

KT = TypeVar("KT")
VT = TypeVar("VT")


# TODO use typing.OrderedDict in python 3.7 - can't mypy this properly until then
#
# mypy is really weird here
#
# https://stackoverflow.com/a/43583996
#
# mypy strict complains that OrderedDict is not OrderedDict[KT, VT]
#   * but if you add that, then mypy is happy, but the interpreter fails at runtime
#   * and stranger, if you type: ignore, then mypy strict is happy, but regular mypy is not?
#
# But there is a solution:
#   1) use type: ignore here
#   2) and always mypy strict this file
class RollingOrderedDict(Generic[KT, VT], OrderedDict):  # type: ignore
    """
    A thing wrapper around OrderedDict that enforces a maximum size.

    The last element added is expired when a new element is added that breaches
    the max size.
    """

    def __init__(self, *argv: Any, maxlen: int, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        assert maxlen > 0
        self._maxlen: int = maxlen
        self._enforce_size_limit()

    @property
    def maxlen(self) -> int:
        return self._maxlen

    @property
    def full(self) -> bool:
        return len(self) == self._maxlen

    def last(self) -> Optional[VT]:
        """
        :return: the last element inserted
        """

        if len(self) == 0:
            return None

        # The class OrderedDict uses a doubly linked list for the dictionary
        # items and implements __reversed__(), so this implementation gives
        # O(1) access to the desired element.
        return self.get(next(reversed(self)))

    def __setitem__(self, key: KT, value: VT) -> None:
        super().__setitem__(key, value)
        self._enforce_size_limit()

    def _enforce_size_limit(self) -> None:
        while len(self) > self._maxlen:
            self.popitem(last=False)

import asyncio
import logging
import time
from asyncio import Abstract<PERSON><PERSON><PERSON><PERSON>
from concurrent.futures import Future
from queue import Empty, Queue
from threading import Event, Thread, current_thread, get_ident
from typing import Any, Callable, Dict, Iterator, List, Optional, Tuple
from uuid import UUID, uuid4

import lib.common.recipes
from lib.common.asyncio.event_loop import get_event_loop
from lib.common.error import MakaException
from lib.common.threading import MakaThread
from lib.common.time import maka_control_timestamp_ms

LOG = logging.getLogger(__name__)

"""
Task which can be launched and manager by the task manager
"""


class MakaTask:
    def __init__(
        self,
        name: str,
        target: Callable[..., Any],
        register_tid: Optional[Callable[["MakaTask", int], Any]],
        unregister_tid: Optional[Callable[[int], Any]],
        context: Optional[Dict[str, Any]],
        *args: Any,
        **kwargs: Any,
    ):
        # Identifying fields
        self._id: UUID = uuid4()
        self._name = name
        self._context: Optional[Dict[str, Any]] = context

        # Lifetime Signals
        self._started = Event()
        # think of "_unpaused" as the opposite of a paused event. Awaiting it to be set ==> we are paused.
        self._unpaused = Event()
        self._unpaused.set()  # to start, we are not awaiting an unpause event. That is, we are not paused
        self._cancelled = Event()
        self._completed = Event()
        self._register_tid = register_tid
        self._unregister_tid = unregister_tid

        # Messages
        self._queue: Queue[Any] = Queue()  # this type is task specific

        # Return Value
        self._future: Future[Any] = Future()

        # Task hierarchy
        self._children: List[MakaTask] = []

        # Metrics
        self._start_time_ms: int = 0
        self._end_time_ms: int = 0
        self._ticks: int = 0

        # Thread
        self._target = target  # Target functions needs to take in kwargs
        self._thread = MakaThread(target=self._task_wrapper, name=name, *args, **kwargs)

    def _task_wrapper(self, *args: Any, **kwargs: Any) -> None:
        try:
            self._start_time_ms = maka_control_timestamp_ms()
            self._started.set()
            if self._register_tid is not None:
                self._register_tid(self)
            ret = self._target(*args, **kwargs)
            self._future.set_result(ret)
        except MakaTaskCancelledException as e:
            for child in self._children:
                child.cancel()
            self._future.set_exception(e)
        except Exception as e:
            for child in self._children:
                child.cancel()
            self._future.set_exception(e)
            LOG.exception("{} threw an Exception!".format(self.name))
        finally:
            for child in self._children:
                child.wait_until_complete()
            if self._unregister_tid is not None:
                self._unregister_tid()
            self._end_time_ms = maka_control_timestamp_ms()
            self._completed.set()
            LOG.debug("Task End: ID: %s, Name: %s, Status: %s" % (self._id, self._name, self.status))

    # Should only be called by task manager
    def link_child_task(self, maka_task: "MakaTask") -> None:
        if self.cancelled:
            maka_task.cancel()
        self._children.append(maka_task)

    # Should only be called by task manager
    def start(self) -> None:
        LOG.debug("Starting Task, ID: %s, Name: %s" % (self._id, self._name))
        self._thread.start()

    @property
    def id(self) -> UUID:
        return self._id

    @property
    def name(self) -> str:
        return self._name

    @property
    def ticks(self) -> int:
        return self._ticks

    @property
    def context(self) -> Optional[Dict[str, Any]]:
        return self._context

    @property
    def error_msg(self) -> str:
        if not self._future.done():
            return ""
        exception = self._future.exception()
        return str(exception) if exception else ""

    @property
    def started(self) -> bool:
        return self._started.is_set()

    @property
    def alive(self) -> bool:
        """
        started and not done
        """
        return self.started and not self._future.done()

    @property
    def done(self) -> bool:
        """
        fully done
        """
        return self._completed.is_set()

    @property
    def success(self) -> bool:
        """
        done but not because we were cancelled
        """
        return self._future.done() and self.get_exception() is None

    @property
    def failed(self) -> bool:
        """
        done but didn't succeed and wasn't cancelled
        """
        return self._future.done() and not self.success and not self.cancelled

    @property
    def cancelled(self) -> bool:
        """
        whether cancel() was called, even if it was called after completion
        """
        return self._cancelled.is_set()

    @property
    def paused(self) -> bool:
        """
        whether pause() was called without unpause() being called afterward
        """
        return not self._unpaused.is_set()

    @property
    def status(self) -> str:
        if not self.started:
            return "New"
        elif self.alive:
            if self.cancelled:
                return "Cancelling"
            elif self.paused:
                return "Paused"
            else:
                return "Running"
        elif self.success:
            return "Success"
        elif self.cancelled:
            return "Cancelled"
        else:
            return "Failed"

    @property
    def duration_ms(self) -> float:
        if not self.started:
            return 0
        end_time_ms = self._end_time_ms
        last_time_ms = end_time_ms if end_time_ms else int(round(time.time() * 1000))
        return last_time_ms - self._start_time_ms

    @property
    def json(self) -> Dict[str, Any]:
        return {
            "id": str(self.id),
            "name": self.name,
            "ticks": self.ticks,
            "started": self.started,
            "alive": self.alive,
            "done": self.done,
            "success": self.success,
            "failed": self.failed,
            "cancelled": self.cancelled,
            "paused": self.paused,
            "status": self.status,
            "start_time_ms": self._start_time_ms,
            "end_time_ms": self._end_time_ms,
            "duration_ms": self.duration_ms,
            "error_msg": self.error_msg,
            "context": self.context,
            "children": [t.json for t in self._children],
        }

    # require kwargs to be passed to ensure correct units
    def wait_until_started(self, *, timeout_ms: Optional[int] = None) -> bool:
        return self._started.wait(timeout=timeout_ms / 1000 if timeout_ms is not None else None)

    # require kwargs to be passed to ensure correct units
    def wait_until_complete(self, *, timeout_ms: Optional[int] = None) -> bool:
        return self._completed.wait(timeout=timeout_ms / 1000 if timeout_ms is not None else None)

    # require kwargs to be passed to ensure correct units
    def get_result(self, *, timeout_ms: Optional[int] = None) -> Any:
        self.wait_until_complete(timeout_ms=timeout_ms)
        return self._future.result()

    def add_done_callback(self, cb: Callable[[Any], Any]) -> None:
        self._future.add_done_callback(cb)

    # require kwargs to be passed to ensure correct units
    def get_exception(self, *, timeout_ms: Optional[int] = None) -> Optional[BaseException]:
        return self._future.exception(timeout=timeout_ms / 1000 if timeout_ms is not None else None)

    def asyncio_wrap(self, loop: AbstractEventLoop) -> "asyncio.Future[Any]":
        return asyncio.wrap_future(self._future, loop=loop)

    def tick(self) -> None:
        """
        Main client function to "tick" a task.

        This is responsible for:
          * checking if cancelled
          * checking if paused (and loop until cancelled/played)
          * maybe metrics?
        """
        # will be set on cancellation
        self._unpaused.wait()

        # check if cancelled
        self.raise_if_cancelled()

        self._ticks += 1

    def raise_if_cancelled(self) -> None:
        if self.cancelled:
            raise MakaTaskCancelledException("Maka Task Cancelled")

    def get_children_ids(self) -> List[UUID]:
        return [child.id for child in self._children]

    def get_from_queue(self, block: bool = False, timeout: Optional[float] = None) -> Any:
        """get_from_queue: any opaque client specific object"""
        try:
            return self._queue.get(block=block, timeout=timeout)
        except Empty:
            return None

    def add_to_queue(self, message: Any) -> None:
        """add_to_queue: any opaque client specific object"""
        self._queue.put(message, block=False)

    def pause(self) -> None:
        if self.cancelled:
            LOG.warning(f"{self.name} paused but was already cancelled")
            return
        self._unpaused.clear()
        LOG.info("{} Paused!".format(self.name))
        for child in self._children:
            child.pause()

    def unpause(self) -> None:
        # this function will be called during cancel()
        was_paused: bool = self.paused
        for child in self._children:
            child.unpause()
        self._unpaused.set()
        if was_paused:
            LOG.info("{} Unpaused!".format(self.name))

    def cancel(self) -> None:
        self._cancelled.set()
        self.unpause()
        LOG.info("{} Cancelled!".format(self.name))
        for child in self._children:
            child.cancel()

    def depends_on(self, *argv: "MakaTask") -> None:
        def cancel_if_other_failed_or_cancelled(task: "MakaTask", other_task: "MakaTask") -> None:
            assert other_task.started

            if task.done:
                # Happy Case #1: we are already done. Nothing to do
                return

            exception = other_task.get_exception(timeout_ms=0)
            if exception is None:
                return
            elif isinstance(exception, MakaTaskCancelledException):
                LOG.warning("Cancelling {} since {} cancelled".format(task.name, other_task.name))
                task.cancel()
                return
            else:
                LOG.warning("Cancelling {} since {} failed".format(task.name, other_task.name))
                task.cancel()
                return

        for other in lib.common.recipes.generate_flatten(*argv):
            LOG.debug("{} depends on: {}".format(self.name, other.name))

            other.add_done_callback(lambda f: cancel_if_other_failed_or_cancelled(self, other))

    def bidepends_on(self, *argv: "MakaTask") -> None:
        for other in lib.common.recipes.generate_flatten(*argv):
            assert isinstance(other, MakaTask), "Unexpected non-MakaTask type received: {}".format(type(other))
            self.depends_on(other)
            other.depends_on(self)


"""
Task Manager which allows the launching of task on separate threads and manages their lifetime and dependencies
"""


def nonesafe_to_sec(timestamp_ms: Optional[int]) -> Optional[int]:
    return timestamp_ms * 1000 if timestamp_ms is not None else None


class MakaTaskManager:
    DEFAULT_NAME = "default"

    def __init__(self) -> None:
        # Below fields should only be accessed on the Event Loop
        self._running_tasks: Dict[UUID, Tuple[bool, "MakaTask"]] = {}
        self._completed_tasks: Dict[UUID, Tuple[bool, "MakaTask"]] = {}
        self._tid_map: Dict[str, "MakaTask"] = {}

        # Start Task Manager Event Loop
        self._tid: Optional[Thread] = None
        self._ready = Event()
        self._event_loop = get_event_loop()
        self._event_loop_registry: Dict[str, AbstractEventLoop] = {"TaskManager": self._event_loop}
        self._loop_map: Dict[str, "MakaTask"] = {}
        self._thread = MakaThread(name="MakaTaskManager-EventLoop", logger=LOG, target=self._run_loop)
        self._thread.start()
        self._ready.wait()
        self.get_event_loop_by_name(self.DEFAULT_NAME)  # init the default loop
        asyncio.set_event_loop(self._event_loop_registry[self.DEFAULT_NAME])

    def _run_loop(self) -> None:
        self._tid = current_thread()
        self._ready.set()
        self._event_loop.run_forever()

    async def _handle_task(self, maka_task: "MakaTask") -> None:
        try:
            await maka_task.asyncio_wrap(self._event_loop)
        finally:
            self._running_tasks.pop(maka_task.id)

    async def _start_task(self, maka_task: "MakaTask", parent_task: Optional["MakaTask"]) -> None:
        if parent_task is not None:
            parent_task.link_child_task(maka_task)
        self._running_tasks[maka_task.id] = (parent_task is None, maka_task)
        maka_task.start()
        await self._handle_task(maka_task)

    async def _pause_task(self, uuid: UUID) -> None:
        task = await self._get_task(uuid)
        if task is None:
            LOG.warning("Couldn't pause task, task not found")
            return
        task.pause()

    async def _unpause_task(self, uuid: UUID) -> None:
        task = await self._get_task(uuid)
        if task is None:
            LOG.warning("Couldn't unpause task, task not found")
            return
        task.unpause()

    async def _cancel_task(self, uuid: UUID) -> None:
        task = await self._get_task(uuid)
        if task is None:
            LOG.warning("Couldn't cancel task, task not found")
            return
        task.cancel()

    # This really is optional return - some of our unit tests create this condition
    async def _get_task(self, uuid: UUID) -> Optional["MakaTask"]:
        task = self._running_tasks.get(uuid, (None, None))[1]
        return task if task else self._completed_tasks.get(uuid, (None, None))[1]

    async def _list_tasks(self) -> Iterator["MakaTask"]:
        return map(lambda t: t[1], list(self._running_tasks.values()) + list(self._completed_tasks.values()))

    async def _json_list_running_tasks(self) -> List[Dict[str, Any]]:
        return [t[1].json for t in list(self._running_tasks.values())]

    async def _json_list_tasks(self) -> List[Dict[str, Any]]:
        all_tasks = list(self._running_tasks.values()) + list(self._completed_tasks.values())
        root_tasks = filter(lambda t: t[0], all_tasks)
        return [t[1].json for t in root_tasks]

    async def _stop_manager(self) -> None:
        self._event_loop.close()

    async def _get_current_task(self, tid: str) -> Optional["MakaTask"]:
        return self._tid_map.get(tid)

    async def _register_task_tid(self, tid: str, maka_task: "MakaTask") -> None:
        self._tid_map[tid] = maka_task

    async def _unregister_task_tid(self, tid: str) -> None:
        del self._tid_map[tid]

    async def _get_event_loop(self, name: str) -> AbstractEventLoop:
        if name not in self._event_loop_registry:
            self._event_loop_registry[name] = get_event_loop()
            task = MakaTask(
                f"Event-Loop-{name}",
                self._event_loop_registry[name].run_forever,
                self._register_tid,
                self._unregister_tid,
                None,
            )
            self._loop_map[name] = task
            asyncio.ensure_future(self._start_task(task, None))
        return self._event_loop_registry[name]

    def _register_tid(self, maka_task: "MakaTask", timeout_ms: Optional[int] = None) -> None:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(
            self._register_task_tid(str(get_ident()), maka_task), self._event_loop
        )
        return future.result(nonesafe_to_sec(timeout_ms))

    def _unregister_tid(self, timeout: Optional[int] = None) -> None:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(self._unregister_task_tid(str(get_ident())), self._event_loop)
        return future.result(timeout)

    # Public API

    def stop_all(self) -> None:
        assert current_thread() != self._tid
        # parse into list before hand to avoid RunTimeError: dictionary changed size during iteration
        # ( we del from the list on task completion )
        tasks = [t for _, t in self._running_tasks.values()]
        for task in tasks:
            task.cancel()
        asyncio.run_coroutine_threadsafe(self._stop_manager(), self._event_loop)

    def get_current(self, timeout_ms: Optional[int] = None) -> Optional["MakaTask"]:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(self._get_current_task(str(get_ident())), self._event_loop)
        return future.result(nonesafe_to_sec(timeout_ms))

    def get(self, uuid: UUID, timeout_ms: Optional[int] = None) -> Optional["MakaTask"]:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(self._get_task(uuid), self._event_loop)
        return future.result(nonesafe_to_sec(timeout_ms))

    def get_task_by_tid(self, tid: str, timeout_ms: Optional[int] = None) -> Optional["MakaTask"]:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(self._get_current_task(tid), self._event_loop)
        return future.result(nonesafe_to_sec(timeout_ms))

    def list(self, timeout_ms: Optional[int] = None) -> Iterator["MakaTask"]:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(self._list_tasks(), self._event_loop)
        return future.result(nonesafe_to_sec(timeout_ms))

    def json_list(self, timeout_ms: Optional[int] = None) -> List[Dict[str, Any]]:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(self._json_list_tasks(), self._event_loop)
        return future.result(nonesafe_to_sec(timeout_ms))

    def json_list_running(self, timeout_ms: Optional[int] = None) -> List[Dict[str, Any]]:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(self._json_list_running_tasks(), self._event_loop)
        return future.result(nonesafe_to_sec(timeout_ms))

    def start(
        self,
        name: str,
        target: Callable[..., Any],
        parent_task: Optional["MakaTask"] = None,
        context: Optional[Any] = None,
        *args: Any,
        **kwargs: Any,
    ) -> "MakaTask":
        assert current_thread() != self._tid
        maka_task = MakaTask(name, target, self._register_tid, self._unregister_tid, context, *args, **kwargs)
        asyncio.run_coroutine_threadsafe(self._start_task(maka_task, parent_task), self._event_loop)
        return maka_task

    def pause(self, uuid: UUID, timeout_ms: Optional[int] = None) -> None:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(self._pause_task(uuid), self._event_loop)
        return future.result(nonesafe_to_sec(timeout_ms))

    def unpause(self, uuid: UUID, timeout_ms: Optional[int] = None) -> None:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(self._unpause_task(uuid), self._event_loop)
        return future.result(nonesafe_to_sec(timeout_ms))

    def cancel(self, uuid: UUID, timeout_ms: Optional[int] = None) -> None:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(self._cancel_task(uuid), self._event_loop)
        return future.result(nonesafe_to_sec(timeout_ms))

    def get_event_loop_by_name(self, name: str, timeout_ms: Optional[int] = None) -> AbstractEventLoop:
        assert current_thread() != self._tid
        future = asyncio.run_coroutine_threadsafe(self._get_event_loop(name), self._event_loop)
        return future.result(nonesafe_to_sec(timeout_ms))

    def wait_on_event_loop_forever(self, name: str) -> None:
        if name in self._loop_map:
            self._loop_map[name].wait_until_complete()


_global_task_manager = MakaTaskManager()


def stop_all() -> None:
    _global_task_manager.stop_all()


def get_current(timeout_ms: Optional[int] = None) -> "MakaTask":
    current_task = _global_task_manager.get_current(timeout_ms=timeout_ms)
    assert current_task  # This always gets called from within a running task loop so this needs to be valid
    return current_task


def maybe_get_current(timeout_ms: Optional[int] = None) -> Optional["MakaTask"]:
    return _global_task_manager.get_current(timeout_ms=timeout_ms)


def get(uuid: UUID, timeout_ms: Optional[int] = None) -> Optional["MakaTask"]:
    return _global_task_manager.get(uuid=uuid, timeout_ms=timeout_ms)


def get_task_by_tid(tid: str, timeout_ms: Optional[int] = None) -> Optional["MakaTask"]:
    return _global_task_manager.get_task_by_tid(tid=tid, timeout_ms=timeout_ms)


def list_tasks(timeout_ms: Optional[int] = None) -> Iterator["MakaTask"]:
    return _global_task_manager.list(timeout_ms=timeout_ms)


def json_list(timeout_ms: Optional[int] = None) -> List[Dict[str, Any]]:
    return _global_task_manager.json_list(timeout_ms=timeout_ms)


def json_list_running(timeout_ms: Optional[int] = None) -> List[Dict[str, Any]]:
    return _global_task_manager.json_list_running(timeout_ms=timeout_ms)


def start(
    name: str,
    target: Callable[..., Any],
    parent_task: Optional["MakaTask"] = None,
    context: Optional[Any] = None,
    *args: Any,
    **kwargs: Any,
) -> "MakaTask":
    return _global_task_manager.start(name, target, parent_task, context, *args, **kwargs)


def pause(uuid: UUID, timeout_ms: Optional[int] = None) -> None:
    _global_task_manager.pause(uuid=uuid, timeout_ms=timeout_ms)


def unpause(uuid: UUID, timeout_ms: Optional[int] = None) -> None:
    _global_task_manager.unpause(uuid=uuid, timeout_ms=timeout_ms)


def cancel(uuid: UUID, timeout_ms: Optional[int] = None) -> None:
    _global_task_manager.cancel(uuid=uuid, timeout_ms=timeout_ms)


def get_event_loop_by_name(
    name: str = _global_task_manager.DEFAULT_NAME, timeout_ms: Optional[int] = None
) -> AbstractEventLoop:
    return _global_task_manager.get_event_loop_by_name(name, timeout_ms)


def wait_on_event_loop_forever(name: str = _global_task_manager.DEFAULT_NAME) -> None:
    return _global_task_manager.wait_on_event_loop_forever(name)


def wait(*tasks: List["MakaTask"], timeout_ms: Optional[int] = None) -> bool:
    deadline = maka_control_timestamp_ms() + timeout_ms if timeout_ms else None
    for task in lib.common.recipes.generate_flatten(*tasks):
        d_timeout = deadline - maka_control_timestamp_ms() if deadline else None
        if not task.wait_until_complete(timeout_ms=d_timeout):
            return False
    return True


"""
Exception raised by MakaTask when the task is cancelled
"""


class MakaTaskCancelledException(MakaException):
    pass

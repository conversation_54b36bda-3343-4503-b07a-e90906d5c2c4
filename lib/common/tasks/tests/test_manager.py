from threading import Event
from typing import Generator, cast
from uuid import uuid4

import pytest

from lib.common.tasks.manager import <PERSON><PERSON><PERSON><PERSON>, Maka<PERSON><PERSON><PERSON>ancelledException, MakaTaskManager

_STANDARD_WAIT_MS: int = 2000

_TEST_VAL = 42


@pytest.fixture(scope="session")
def task_manager() -> Generator[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, None, None]:
    manager = MakaTaskManager()
    yield manager
    manager.stop_all()


def task_function() -> int:
    return _TEST_VAL


def task_exception(failed_event: Event) -> None:
    failed_event.wait()
    raise Exception("Hello")


def running_task_func(completed_event: Event, task_manager: <PERSON>ka<PERSON>askManager) -> None:
    completed_event.wait()
    current_task = task_manager.get_current()
    assert current_task is not None
    current_task.raise_if_cancelled()


def cancellable_task(cancelled_event: Event, task_manager: MakaTaskManager) -> None:
    cancelled_event.wait()
    current_task = task_manager.get_current()
    assert current_task is not None
    current_task.raise_if_cancelled()
    # FIXME lib/common/tasks/tests/test_manager.py::test_cancellable_task_success_before_cancel prints this msg
    print("ERROR: this message should not print. Task should have been cancelled")


def double_task(task_manager: MakaTaskManager) -> int:
    maka_task = task_manager.get_current()
    child1 = task_manager.start("Child1", task_function, parent_task=maka_task)
    child2 = task_manager.start("Child2", task_function, parent_task=maka_task)
    child1.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    child2.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    return cast(int, child1.get_result(timeout_ms=_STANDARD_WAIT_MS)) + cast(
        int, child2.get_result(timeout_ms=_STANDARD_WAIT_MS)
    )


def double_cancellable_task(cancelled_event: Event, task_manager: MakaTaskManager) -> None:
    task = task_manager.get_current()
    assert task is not None
    child1 = task_manager.start(
        "Child1",
        cancellable_task,
        parent_task=task,
        kwargs={"cancelled_event": cancelled_event, "task_manager": task_manager},
    )
    child2 = task_manager.start(
        "Child2",
        cancellable_task,
        parent_task=task,
        kwargs={"cancelled_event": cancelled_event, "task_manager": task_manager},
    )
    cancelled_event.wait()
    child1.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    assert child1.done
    assert child1.cancelled
    assert child1.status == "Cancelled"
    child2.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    assert child2.done
    assert child2.cancelled
    assert child2.status == "Cancelled"
    task.raise_if_cancelled()


def queue_task(task_manager: MakaTaskManager) -> int:
    maka_task = task_manager.get_current()
    assert maka_task is not None
    return cast(int, maka_task.get_from_queue(block=True, timeout=5)) + cast(
        int, maka_task.get_from_queue(block=True, timeout=5)
    )


def assert_alive(task: MakaTask) -> None:
    assert task.started
    assert task.alive
    assert task.status == "Running"
    assert not task.success
    assert not task.failed
    assert not task.done


def assert_success(task: MakaTask, cancelled: bool = False) -> None:
    assert task.done
    assert task.cancelled == cancelled
    assert not task.failed
    assert task.get_exception() is None, "Unexpected task exception: {}".format(task.get_exception())
    assert task.success
    assert task.status == "Success"


def assert_cancelled(task: MakaTask) -> None:
    assert task.done
    assert not task.success
    assert not task.failed
    assert task.get_exception() is not None
    assert isinstance(task.get_exception(), MakaTaskCancelledException), "Unexpected task exception type: {}".format(
        type(task.get_exception())
    )
    assert task.cancelled
    assert task.status == "Cancelled"


def assert_failed(task: MakaTask) -> None:
    assert task.done
    assert not task.cancelled
    assert not task.success
    assert task.failed
    assert task.status == "Failed"
    assert task.error_msg == "Hello"


def test_get_missing_task(task_manager: MakaTaskManager) -> None:
    assert task_manager.get(uuid4()) is None


def test_simple_task(task_manager: MakaTaskManager) -> None:
    # start
    task = task_manager.start("SimpleTask", task_function)
    task.wait_until_started(timeout_ms=_STANDARD_WAIT_MS)
    assert task.started
    # alive
    # done
    assert task.get_result(timeout_ms=_STANDARD_WAIT_MS) == _TEST_VAL
    assert_success(task)


def test_exception_task(task_manager: MakaTaskManager) -> None:
    failed_event = Event()
    # start
    task = task_manager.start("ExceptionTask", task_exception, kwargs={"failed_event": failed_event})
    task.wait_until_started(timeout_ms=_STANDARD_WAIT_MS)
    # alive
    assert_alive(task)
    failed_event.set()
    task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    # done
    assert_failed(task)


def test_not_started() -> None:
    # start
    task = MakaTask("NotStartedTask", task_function, None, None, None)
    assert not task.started
    assert not task.alive
    assert not task.done
    assert not task.cancelled
    assert not task.success
    assert not task.failed
    assert task.status == "New"


def test_running_task(task_manager: MakaTaskManager) -> None:
    completed_event = Event()
    # start
    task = task_manager.start(
        "RunningTask", running_task_func, kwargs={"completed_event": completed_event, "task_manager": task_manager}
    )
    task.wait_until_started()
    # alive
    assert_alive(task)
    completed_event.set()
    task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    # done
    assert_success(task)


def test_cancellable_task(task_manager: MakaTaskManager) -> None:
    cancelled_event = Event()
    # start
    task = task_manager.start(
        "CancellableTask", cancellable_task, kwargs={"cancelled_event": cancelled_event, "task_manager": task_manager}
    )
    task.wait_until_started(timeout_ms=_STANDARD_WAIT_MS)
    # alive
    assert_alive(task)
    task_manager.cancel(task.id)
    assert task.status == "Cancelling"
    cancelled_event.set()
    task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    # done
    assert_cancelled(task)


def test_cancellable_task_success_before_cancel(task_manager: MakaTaskManager) -> None:
    cancelled_event = Event()
    # start
    task = task_manager.start(
        "CancellableTask", cancellable_task, kwargs={"cancelled_event": cancelled_event, "task_manager": task_manager}
    )
    task.wait_until_started(timeout_ms=_STANDARD_WAIT_MS)
    # alive
    assert_alive(task)
    cancelled_event.set()
    task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    # done
    assert_success(task)
    task_manager.cancel(task.id)
    assert_success(task)


def test_stop_all_cancellable_task(task_manager: MakaTaskManager) -> None:
    cancelled_event = Event()
    # start
    task = task_manager.start(
        "CancellableTask", cancellable_task, kwargs={"cancelled_event": cancelled_event, "task_manager": task_manager}
    )
    task.wait_until_started(timeout_ms=_STANDARD_WAIT_MS)
    # alive
    assert_alive(task)
    task_manager.stop_all()
    assert task.status == "Cancelling"
    cancelled_event.set()
    task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    # done
    assert_cancelled(task)


def test_children_tasks(task_manager: MakaTaskManager) -> None:
    # start
    task = task_manager.start("DoubleTask", double_task, kwargs={"task_manager": task_manager})
    assert task.get_result(timeout_ms=_STANDARD_WAIT_MS) == _TEST_VAL * 2
    assert task.status == "Success"


def test_cancellable_children(task_manager: MakaTaskManager) -> None:
    cancelled_event = Event()
    # start
    task = task_manager.start(
        "DoubleCancellableTask",
        double_cancellable_task,
        kwargs={"cancelled_event": cancelled_event, "task_manager": task_manager},
    )
    task.wait_until_started(timeout_ms=_STANDARD_WAIT_MS)
    # alive
    assert_alive(task)
    task_manager.cancel(task.id)
    assert task.status == "Cancelling"
    cancelled_event.set()
    task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    # done
    assert_cancelled(task)


def test_queue_task(task_manager: MakaTaskManager) -> None:
    # start
    task = task_manager.start("QueueTask", queue_task, kwargs={"task_manager": task_manager})
    task.add_to_queue(_TEST_VAL)
    task.add_to_queue(10)
    task.wait_until_started(timeout_ms=_STANDARD_WAIT_MS)
    # alive
    assert task.started
    # done
    assert task.get_result(timeout_ms=_STANDARD_WAIT_MS) == _TEST_VAL + 10
    assert task.done
    assert not task.cancelled
    assert task.get_exception() is None, "Unexpected task exception: {}".format(task.get_exception())
    assert task.success
    assert not task.failed
    assert task.status == "Success"


def test_depends_on_cancelled(task_manager: MakaTaskManager) -> None:
    """
    cancel base_task => cancel depends_on_task
    """
    cancelled_base_event = Event()
    depends_on_event = Event()
    # start
    base_task = task_manager.start(
        "BaseTask", cancellable_task, kwargs={"cancelled_event": cancelled_base_event, "task_manager": task_manager},
    )
    base_task.wait_until_started()
    depends_on_base_task = task_manager.start(
        "DependsOnBaseTask",
        running_task_func,
        kwargs={"completed_event": depends_on_event, "task_manager": task_manager},
    )
    depends_on_base_task.depends_on(base_task)
    depends_on_base_task.wait_until_started()
    # alive
    assert_alive(base_task)
    assert_alive(depends_on_base_task)
    task_manager.cancel(base_task.id)
    assert base_task.status == "Cancelling"
    cancelled_base_event.set()
    base_task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    # done
    assert_cancelled(base_task)
    depends_on_event.set()
    depends_on_base_task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    assert_cancelled(depends_on_base_task)


def test_depends_on_failed(task_manager: MakaTaskManager) -> None:
    """
    fail base_task => cancel depends_on_task
    """
    failed_base_event = Event()
    depends_on_event = Event()
    # start
    base_task = task_manager.start("BaseTask", task_exception, kwargs={"failed_event": failed_base_event})
    base_task.wait_until_started()
    depends_on_base_task = task_manager.start(
        "DependsOnBaseTask",
        running_task_func,
        kwargs={"completed_event": depends_on_event, "task_manager": task_manager},
    )
    depends_on_base_task.depends_on(base_task)
    depends_on_base_task.wait_until_started()
    # alive
    assert_alive(depends_on_base_task)
    failed_base_event.set()
    base_task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    assert_failed(base_task)
    # done
    depends_on_event.set()
    depends_on_base_task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    assert_cancelled(depends_on_base_task)


def test_bidepends_on_cancelled(task_manager: MakaTaskManager) -> None:
    """
    cancel depends_on_task => cancel base_task
    """
    cancelled_depends_on_event = Event()
    base_event = Event()
    # start
    base_task = task_manager.start(
        "BaseTask", running_task_func, kwargs={"completed_event": base_event, "task_manager": task_manager},
    )
    base_task.wait_until_started()
    depends_on_base_task = task_manager.start(
        "DependsOnBaseTask",
        cancellable_task,
        kwargs={"cancelled_event": cancelled_depends_on_event, "task_manager": task_manager},
    )
    depends_on_base_task.bidepends_on(base_task)
    depends_on_base_task.wait_until_started()
    # alive
    assert_alive(base_task)
    assert_alive(depends_on_base_task)
    task_manager.cancel(depends_on_base_task.id)
    assert depends_on_base_task.status == "Cancelling"
    cancelled_depends_on_event.set()
    depends_on_base_task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    # done
    assert_cancelled(depends_on_base_task)
    assert base_task.status == "Cancelling"
    base_event.set()
    base_task.wait_until_complete(timeout_ms=_STANDARD_WAIT_MS)
    assert_cancelled(base_task)

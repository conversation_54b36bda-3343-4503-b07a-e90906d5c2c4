from typing import Sequence

import lib.common.tasks as tasks
from lib.common.tasks.job import End<PERSON>ignal<PERSON><PERSON>, Job, JobExecutor

_STANDARD_WAIT_MS = 3000


class _TestJob(Job[int]):
    pass


def complete_job(job: Job[int]) -> None:
    job.finish(result=42)


def fail_job(job: Job[int]) -> None:
    job.finish(exception=Exception("Test"))


def _test_setup(executor_def: JobExecutor) -> Sequence[Job[int]]:
    executors = []
    for i in range(8):
        executors.append(tasks.start("{} Executor".format(i), executor_def))

    jobs = []
    for _ in range(42):
        job = _TestJob()
        jobs.append(job)
        for executor in executors:
            executor.add_to_queue(job)

    for executor in executors:
        executor.add_to_queue(EndSignalJob())

    tasks.wait(executors, timeout_ms=_STANDARD_WAIT_MS)
    return jobs


def test_job_execution() -> None:
    executor_def = JobExecutor(complete_job)
    jobs = _test_setup(executor_def)
    for job in jobs:
        assert job.get_result(timeout_ms=_STANDARD_WAIT_MS) == 42
        assert job.get_exception(timeout_ms=_STANDARD_WAIT_MS) is None


def test_job_exception() -> None:
    executor_def = JobExecutor(fail_job)
    jobs = _test_setup(executor_def)
    for job in jobs:
        assert job.get_exception(timeout_ms=_STANDARD_WAIT_MS) is not None

import logging
import threading
from abc import ABC
from concurrent.futures import Future
from typing import Any, Callable, Generic, Optional, TypeVar

from lib.common.tasks.manager import get_current

LOG = logging.getLogger(__name__)


T = TypeVar("T")


class Job(Generic[T], ABC):
    """
    Jobs define a unit of work that needs to be done

    Typically used to communicate job requests across tasks
    Unlike Tasks, Jobs are not associated with a thread and are not managed
    """

    def __init__(self) -> None:
        self._lock = threading.Lock()
        self._reserved = False
        self._future: Future[T] = Future()

    def checkout(self) -> bool:
        if self.completed:
            return False
        with self._lock:
            prev = self._reserved
            self._reserved = True
            return not prev

    def finish(self, result: Optional[T] = None, exception: Optional[BaseException] = None) -> None:
        if exception is None:
            self._future.set_result(result)  # type: ignore     # (will be None for EndSignalJob)
        else:
            self._future.set_exception(exception)

    @property
    def completed(self) -> bool:
        return self._future.done()

    # require kwargs to be passed to ensure correct units
    def wait_until_completed(self, *, timeout_ms: Optional[int] = None) -> None:
        self.get_exception(timeout_ms=timeout_ms)

    # require kwargs to be passed to ensure correct units
    def get_result(self, *, timeout_ms: Optional[int] = None) -> T:
        return self._future.result(timeout_ms / 1000 if timeout_ms is not None else None)

    # require kwargs to be passed to ensure correct units
    def get_exception(self, *, timeout_ms: Optional[int] = None) -> Optional[BaseException]:
        return self._future.exception(timeout_ms / 1000 if timeout_ms is not None else None)

    def add_done_callback(self, callback: Callable[["Future[T]"], Any]) -> None:
        self._future.add_done_callback(callback)


class EndSignalJob(Job[None]):
    def __init__(self) -> None:
        super().__init__()


class JobExecutor:
    def __init__(self, job_processor: Callable[..., None]):
        # first arg should be a Job but we can't really tell mypy that
        self._job_processor: Callable[..., None] = job_processor

    def __call__(self, *args: Any, **kwargs: Any) -> None:
        task = get_current()
        assert task is not None
        while True:
            task.tick()

            # Get Next Job
            job = task.get_from_queue(block=True, timeout=5)

            # Ignore Unknown Messages
            if not isinstance(job, Job):
                continue

            # Reserve Job
            if not job.checkout():
                continue  # Job is already taken

            # Terminate Task when Signalled
            if isinstance(job, EndSignalJob):
                job.finish()
                break

            # Execute Job
            try:
                self._job_processor(job, *args, **kwargs)
            except Exception as e:
                LOG.exception("Job Failed")
                job.finish(exception=e)

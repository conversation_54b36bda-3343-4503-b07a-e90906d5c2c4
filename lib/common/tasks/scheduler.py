from typing import Any, Callable, Coroutine, Optional

from lib.common.logging import get_logger
from lib.common.tasks.manager import MakaT<PERSON>, get_current
from lib.common.time import maka_control_timestamp_ms
from lib.common.time.sleep import async_sleep_ms, get_sleep_detune

LOG = get_logger(__name__)


class SchedulerException(Exception):
    pass


# First attempts at building out scheduler primitives
async def dispatch(
    *, name: str, callback: Callable[[], Coroutine[Any, Any, None]], poll_ms: int, fail_consecutive_exceptions: int = 1
) -> None:
    if fail_consecutive_exceptions < 0:  # 0/1 treated the same essentially
        raise SchedulerException(f"Out of range value: fail_consecutive_exceptions={fail_consecutive_exceptions}")
    if poll_ms < 1:
        raise SchedulerException(f"poll rate too fast: {poll_ms}ms")

    sleep_detune = get_sleep_detune()
    # TODO this is probably not the best spot for the conversion
    poll_ms = int(sleep_detune * poll_ms)
    log_line = f"Dispatching {name} every {poll_ms}ms"
    if sleep_detune != 1:
        log_line += f" (since detuned by: {sleep_detune})"
    LOG.debug(log_line)

    consecutive_exceptions: int = 0
    prev_time_ms = maka_control_timestamp_ms()
    task: Optional[MakaTask] = get_current()
    assert task is not None

    while True:
        task.tick()

        success = False
        try:
            await callback()
            success = True
        except Exception:
            consecutive_exceptions += 1
            if consecutive_exceptions >= fail_consecutive_exceptions:
                LOG.critical(f"Dispatch failed after {consecutive_exceptions} consecutive exceptions")
                raise
            LOG.exception("Continuing after Exception")
        if success:
            consecutive_exceptions = 0

        # always yield to scheduler
        now_ms = maka_control_timestamp_ms()
        elapsed_ms = now_ms - prev_time_ms
        sleep_for = max(0.0, poll_ms - elapsed_ms)
        # divide by sleep_detune because sleep multiplies by it
        await async_sleep_ms(sleep_for / sleep_detune)
        now_ms = maka_control_timestamp_ms()
        prev_time_ms = now_ms

import numpy as np
import pytest
from pyquaternion import Quaternion

from lib.common.math import fit_value_between, heading, quaternion_to_euler_angles


def test_fit_value_between() -> None:
    min_max = [-1.0, 1.0]
    assert fit_value_between(-2, min_max) == -1
    assert fit_value_between(-1, min_max) == -1
    assert fit_value_between(0, min_max) == 0
    assert fit_value_between(1, min_max) == 1
    assert fit_value_between(2, min_max) == 1


def fit_value_between_input_reversed() -> None:
    max_min = [1.0, -1.0]
    assert fit_value_between(-2, max_min) == -1
    assert fit_value_between(-1, max_min) == -1
    assert fit_value_between(0, max_min) == 0
    assert fit_value_between(1, max_min) == 1
    assert fit_value_between(2, max_min) == 1


def test_fit_value_between_bad_input_too_short() -> None:
    with pytest.raises(AssertionError, match=r"length"):
        assert fit_value_between(0, [0])


def test_fit_value_between_bad_input_too_long() -> None:
    with pytest.raises(AssertionError, match=r"length"):
        assert fit_value_between(0, [0, 0, 0])


def test_euler_angles() -> None:
    """
    Test that quaternion to euler angles conversions work correctly, using a few known test vectors.
    """
    # Test cases generated from https://www.andre-gaschler.com/rotationconverter/
    test_cases = [
        (Quaternion(1, 0, 0, 0), np.array((0, 0, 0)).reshape(3, 1)),
        (Quaternion(1, 1, 0, 0), np.array((np.pi / 2, 0, 0)).reshape(3, 1)),
        (Quaternion(1, -1, 0, 0), np.array((-np.pi / 2, 0, 0)).reshape(3, 1)),
        (Quaternion(-1, 1, 0, 0), np.array((-np.pi / 2, 0, 0)).reshape(3, 1)),
        (Quaternion(-1, -1, 0, 0), np.array((np.pi / 2, 0, 0)).reshape(3, 1)),
        (Quaternion(1, 0, 1, 0), np.array((0, np.pi / 2, 0)).reshape(3, 1)),
        (Quaternion(1, 0, -1, 0), np.array((0, -np.pi / 2, 0)).reshape(3, 1)),
        (Quaternion(-1, 0, 1, 0), np.array((0, -np.pi / 2, 0)).reshape(3, 1)),
        (Quaternion(-1, 0, -1, 0), np.array((0, np.pi / 2, 0)).reshape(3, 1)),
        (Quaternion(1, 0, 0, 1), np.array((0, 0, np.pi / 2)).reshape(3, 1)),
        (Quaternion(1, 0, 0, -1), np.array((0, 0, -np.pi / 2)).reshape(3, 1)),
        (Quaternion(-1, 0, 0, 1), np.array((0, 0, -np.pi / 2)).reshape(3, 1)),
        (Quaternion(-1, 0, 0, -1), np.array((0, 0, np.pi / 2)).reshape(3, 1)),
        (Quaternion(-0.755, -0.132, -0.074, 0.638), np.array((0.1094947, 0.2839823, -1.387539)).reshape(3, 1)),
        (
            Quaternion(0.6286741, -0.02734, -0.5023482, 0.5930158),
            np.array((-0.9061444, -0.6425021, 1.8336725)).reshape(3, 1),
        ),
    ]

    for quat, expected_euler_angles in test_cases:
        calculated_euler_angles = quaternion_to_euler_angles(quat)
        np.testing.assert_array_almost_equal(expected_euler_angles, calculated_euler_angles, decimal=7)


def test_heading() -> None:
    """
    Test that the math for getting a heading from an orientation quaternion is correct, using some known test vectors.
    """
    # Test cases generated from https://www.andre-gaschler.com/rotationconverter/
    # Remember that we generate Euler angles in the ZYX order, if using ^ to generate more cases or check the existing
    # ones.
    test_cases = [
        (Quaternion(-1, -1, 0, 0), 90),
        (Quaternion(1, 0, 0, 0), 90),
        (Quaternion(1, 0, 0, 1), 0),
        (Quaternion(0, 1, 1, 0), 0),
        (Quaternion(1, 0, 0, -1), 180),
        (Quaternion(0, -1, 1, 0), 180),
        (Quaternion(0, 0, 1, 0), 270),
        (Quaternion(0, 0, 0, 1), 270),
    ]

    for quat, expected_heading in test_cases:
        calculated_heading = heading(quat)
        assert expected_heading == pytest.approx(calculated_heading, 1e-6)

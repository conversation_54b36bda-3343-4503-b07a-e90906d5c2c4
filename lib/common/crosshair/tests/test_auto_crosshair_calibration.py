import os
import unittest

import cv2

from generated.frontend.proto.image_stream_pb2 import Image
from lib.common.crosshair.auto_crosshair_calibration import get_laser_centroid_position


class TestAutoCrosshairCalibration(unittest.TestCase):
    def test_fake_image(self) -> None:
        test_image_path = f"{os.path.dirname(__file__)}/target1_1701459221766.png"
        test_image_cv = cv2.imread(test_image_path)

        test_image = Image(data=bytes(test_image_cv.data), width=test_image_cv.shape[1], height=test_image_cv.shape[0])
        laser_centroid = get_laser_centroid_position(test_image, "target1")

        self.assertIsNotNone(laser_centroid)
        assert laser_centroid is not None
        self.assertEqual(laser_centroid[0], 372)
        self.assertEqual(laser_centroid[1], 424)

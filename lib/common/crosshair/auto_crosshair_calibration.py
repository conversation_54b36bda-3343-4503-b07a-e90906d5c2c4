import asyncio
import os
from typing import Any, List, Optional, Set, Tuple

import cv2
import numpy as np
import torch
import torchvision.transforms.functional as TF

from config.client.cpp.config_client_python import get_computer_config_prefix, get_global_config_subscriber
from core.controls.exterminator.controllers.aimbot.process.client.aimbot_client import AimbotClient
from cv.runtime.client.image_service_client import ImageServiceClient
from generated.frontend.proto.image_stream_pb2 import Image
from lib.common.logging import get_logger
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time.time import maka_control_timestamp_ms

LOG = get_logger(__name__)

MAKA_DATA_DIR = os.getenv("MAKA_DATA_DIR", "/data")


def save_image(image: Image, name: str) -> None:
    np_array = np.frombuffer(image.data, dtype=np.uint8)
    np_array = np_array.reshape((image.height, image.width, 3))
    dir = os.path.join(MAKA_DATA_DIR, "media/failed_crosshair_calibrations/")
    os.makedirs(dir, exist_ok=True)
    cv2.imwrite(os.path.join(dir, f"{name}_{image.ts.timestamp_ms}.png"), np_array)


def get_laser_centroid_position(image: Image, name: str) -> Optional[Tuple[int, int]]:
    np_array = np.frombuffer(image.data, dtype=np.uint8)
    np_array = np_array.reshape((image.height, image.width, 3))

    target, _ = TF.to_tensor(np.copy(np_array)).max(0)
    threshold_value = target.max() - 0.2
    output = torch.where(target < threshold_value, torch.zeros_like(target), torch.ones_like(target))

    _, _, stats, centroids = cv2.connectedComponentsWithStats(output.numpy().astype(np.uint8))
    # Need to see at least background and crosshair centroids in output
    if len(centroids) < 2:
        return None

    indices = np.argsort(stats[:, -1])
    # Crosshair should be the second largest cluster
    crosshair_index = indices[-2]

    if stats[crosshair_index, -1] == 0:
        return None

    cx, cy = centroids[crosshair_index]
    cx = int(round(cx))
    cy = int(round(cy))
    return (cx, cy)


class AutoCrossHairCalManager:
    def __init__(self) -> None:
        self._aimbot_client = AimbotClient()
        self._img_svc_client = ImageServiceClient()
        self._task: Optional[asyncio.Task[Any]] = None
        self._progress = 0.0
        self._config_subscriber = get_global_config_subscriber()
        self._cv_config_node = self._config_subscriber.get_config_node("cv", "cameras")
        self._ids: Set[int] = set()
        self._failed: Set[int] = set()

    async def start(self, scanner_id_filter: List[int] = []) -> bool:
        if self._task is not None:
            if self._task.done():
                await self._task
            else:
                return False
        self._task = asyncio.get_event_loop().create_task(self._cal_with_progress(scanner_id_filter))
        return True

    async def wait(self) -> None:
        if self._task is not None:
            await self._task

    async def stop(self) -> None:
        if self._task is not None:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
            except Exception as ex:
                LOG.warning(f"unknown exception occured while stopping x-hair call {ex}")
            self._task = None

    def in_progress(self) -> bool:
        if self._task is None:
            return False
        if self._task.done():
            return False
        return True

    def in_progress_for_scanner(self, scanner_id: int) -> bool:
        if not self.in_progress():
            return False
        if len(self._ids) == 0:
            return True
        return scanner_id in self._ids

    def scanner_failed(self, scanner_id: int) -> bool:
        return scanner_id in self._failed

    @property
    def progress(self) -> float:
        return self._progress

    async def _cal_with_progress(self, scanner_id_filter: List[int]) -> None:
        self._progress = 0.0
        self._failed.clear()
        self._ids = set(scanner_id_filter)
        try:
            await self._auto_crosshair_calibration(scanner_id_filter)
        except Exception as e:
            LOG.warning(f"Calibration failed with err: {e}")
        finally:
            self._progress = 1.0

    async def _fire_laser(self, scanner_id: int, done: asyncio.Event) -> None:
        try:
            await self._aimbot_client.laser_set(scanner_id=scanner_id, on=True)
            await done.wait()
        finally:
            await self._aimbot_client.laser_set(scanner_id=scanner_id, on=False)

    async def _auto_crosshair_calibration(self, scanner_id_filter: List[int]) -> None:  # noqa
        DRY_TIME = 5
        WAIT_TIME = 2
        CALIBRATE_TIME = 2
        self._progress = 0.01
        scanner_states = await self._aimbot_client.get_scanner_state()
        valid_ids = []
        for scanner_state in scanner_states.states:
            if not scanner_state.laser_state.enabled:
                continue
            scanner_id = scanner_state.scanner_descriptor.id

            if scanner_id_filter and scanner_id not in scanner_id_filter:
                continue
            valid_ids.append(scanner_id)
        if not valid_ids:
            LOG.info("No Scanners to calibrate")
            return
        aimbot_state = await self._aimbot_client.get_aimbot_state()
        if aimbot_state.running:
            self._failed = set(valid_ids)
            raise RuntimeError("Please stop weeding before starting crosshair calibration")

        if not aimbot_state.armed:
            self._failed = set(valid_ids)
            raise RuntimeError("Please arm lasers before starting crosshair calibration")

        self._progress = 0.05
        total_expected_time = DRY_TIME + WAIT_TIME + (len(valid_ids) * CALIBRATE_TIME)
        dry_percent = (DRY_TIME / total_expected_time) * 0.95
        wait_percent = (WAIT_TIME / total_expected_time) * 0.95
        cal_percent = (CALIBRATE_TIME / total_expected_time) * 0.95

        done = asyncio.Event()

        # Burn the ground for 10 seconds to dry out the soil
        try:
            fire_tasks = [
                asyncio.get_event_loop().create_task(self._fire_laser(scanner_id, done)) for scanner_id in valid_ids
            ]
            await asyncio.sleep(DRY_TIME)
        finally:
            done.set()
            await asyncio.gather(*fire_tasks)
        self._progress += dry_percent

        # Wait a bit to let ground cool
        await asyncio.sleep(WAIT_TIME)
        self._progress += wait_percent

        # Fire each scanner and find crosshair position
        for scanner_id in valid_ids:
            camera_id = f"target{scanner_id}"
            camera_node = self._cv_config_node.get_node(camera_id)

            LOG.info(f"Finding crosshair for camera {camera_id}")

            original_auto_brightness_value = camera_node.get_node("auto_brightness_enabled").get_bool_value()
            self._config_subscriber.get_client().set_bool_value(
                f"{get_computer_config_prefix()}/cv/cameras/{camera_id}/auto_brightness_enabled", False
            )

            original_exposure_value = camera_node.get_node("exposure_us").get_float_value()
            self._config_subscriber.get_client().set_float_value(
                f"{get_computer_config_prefix()}/cv/cameras/{camera_id}/exposure_us",
                camera_node.get_node("auto_brightness/min_exposure_us").get_float_value(),
            )

            await asyncio.sleep(0.5)

            success = False
            try:
                attempts = 0
                while attempts < 8:
                    if attempts > 0:
                        # Wait a little bit before trying again
                        await asyncio.sleep(1)
                    attempts += 1
                    try:
                        done.clear()
                        try:
                            fire_task = asyncio.get_event_loop().create_task(self._fire_laser(scanner_id, done))

                            await asyncio.sleep(0.5)

                            timestamp_ms = maka_control_timestamp_ms()

                            image = await self._img_svc_client.get_next_camera_image(
                                camera_id, timestamp_ms, False, False, True, False, True
                            )
                        finally:
                            done.set()
                            await fire_task

                        crosshair = None
                        try:
                            crosshair = await asyncio.get_event_loop().run_in_executor(
                                None, lambda: get_laser_centroid_position(image, camera_id)
                            )
                        except Exception as e:
                            LOG.warning(f"Failed to find crosshair for {camera_id}", exc_info=e)
                            save_image(image, camera_id)
                            continue

                        if crosshair is None:
                            save_image(image, camera_id)
                            LOG.info(f"Failed to find crosshair for {camera_id}")
                            continue

                        half_width = image.width // 2
                        quarter_width = image.width // 4
                        half_height = image.height // 2
                        quarter_height = image.height // 4
                        if (
                            crosshair[0] < half_width - quarter_width
                            or crosshair[0] > half_width + quarter_width
                            or crosshair[1] < half_height - quarter_height
                            or crosshair[1] > half_height + quarter_height
                        ):
                            save_image(image, camera_id)
                            LOG.info(f"Found crosshair outside of expected bounds for {camera_id}: {crosshair}")
                            continue

                        LOG.info(f"Updating the crosshair for {camera_id} to {crosshair}")

                        await self._aimbot_client.set_crosshair_position(scanner_id, crosshair[0], crosshair[1])
                        success = True
                        break
                    except Exception as e:
                        LOG.warning(f"Failed to find crosshair for {camera_id}: {e}")
            finally:
                self._config_subscriber.get_client().set_bool_value(
                    f"{get_computer_config_prefix()}/cv/cameras/{camera_id}/auto_brightness_enabled",
                    original_auto_brightness_value,
                )
                self._config_subscriber.get_client().set_float_value(
                    f"{get_computer_config_prefix()}/cv/cameras/{camera_id}/exposure_us", original_exposure_value
                )
                self._progress += cal_percent
                if not success:
                    self._failed.add(scanner_id)


async def main() -> None:
    aimbot_client = AimbotClient()
    aimbot_state = await aimbot_client.get_aimbot_state()
    if aimbot_state.running:
        raise RuntimeError("Please stop weeding before starting crosshair calibration")

    if not aimbot_state.armed:
        await aimbot_client.arm_lasers()
    manager = AutoCrossHairCalManager()
    await manager.start()

    await manager.wait()

    if not aimbot_state.armed:
        await aimbot_client.disarm_lasers()


if __name__ == "__main__":
    config_subscriber = get_global_config_subscriber()
    config_subscriber.add_config_tree("cv", f"{get_computer_config_prefix()}/cv", "services/cv.yaml")
    config_subscriber.start()
    config_subscriber.wait_until_ready()

    asyncio.run_coroutine_threadsafe(main(), get_event_loop_by_name()).result()

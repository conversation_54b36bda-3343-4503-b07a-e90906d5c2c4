import inspect
from typing import Any, Callable, TypeVar, cast

trace_indent: int = 0


# Refer https://mypy.readthedocs.io/en/stable/generics.html#declaring-decorators
F = TypeVar("F", bound=Callable[..., Any])


def trace(f: F, params: bool = False) -> F:
    """
    prints ENTER/EXIT + function name to sysout

    Adapted from https://cscheid.net/2017/12/11/minimal-tracing-decorator-python-3.html
    """
    sig = inspect.signature(f)

    def wrapper(*args: Any, **kwargs: Any) -> Any:
        global trace_indent

        # ENTER
        # compute leading whitespace
        ws = " " * (trace_indent * 2)
        func_name = f.__name__
        print(f"{ws}ENTER {func_name}")
        if params:
            for ix, param in enumerate(sig.parameters.values()):
                print("%s    %s: %s" % (ws, param.name, args[ix]))
        trace_indent += 1

        # EXECUTE
        result = f(*args, **kwargs)

        # EXIT
        trace_indent -= 1
        print(f"{ws}EXIT {func_name}")
        return result

    return cast(F, wrapper)

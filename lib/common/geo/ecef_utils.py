import math
from typing import Any, List, Optional, Tuple, Union, cast

import navpy
import numpy as np
import numpy.typing as npt
from shapely import LineString, Point

from lib.common.angle_direction import AngleDirection
from lib.common.geo.boundary import Boundary
from lib.common.logging import get_logger

MM_PER_INCH = 25.4


LOG = get_logger(__name__)


# Types
class EcefVector:
    @staticmethod
    def empty() -> "EcefVector":
        return EcefVector(np.array([0.0, 0.0]), np.array([0.0, 0.0]))

    def is_empty(self) -> bool:
        cmp_start = self.start == np.array([0.0, 0.0])
        cmp_end = self.end == np.array([0.0, 0.0])
        if isinstance(cmp_start, np.ndarray):
            cmp_start = cmp_start.all()
        if isinstance(cmp_end, np.ndarray):
            cmp_end = cmp_end.all()
        return cast(bool, cmp_start and cmp_end)

    def __init__(self, start: npt.NDArray[Any], end: npt.NDArray[Any]):
        self.start = start
        self.end = end

    @property
    def scalar(self) -> npt.NDArray[Any]:
        return cast(npt.NDArray[Any], self.end - self.start)

    def __add__(self, other: Union["EcefVector", npt.NDArray[Any]]) -> "EcefVector":
        if isinstance(other, EcefVector):
            n = other.scalar
        else:
            n = other
        return EcefVector(self.start + n, self.end + n)

    def __sub__(self, other: Union["EcefVector", npt.NDArray[Any]]) -> "EcefVector":
        if isinstance(other, EcefVector):
            n = other.scalar
        else:
            n = other
        try:
            return EcefVector(self.start - n, self.end - n)
        except ValueError:
            LOG.exception(f"start: f{self.start}, end: {self.end}, n: {n})")
            raise

    def __mul__(self, other: float) -> "EcefVector":
        return EcefVector(other * self.start, other * self.end)

    def change_basis(self, basis: npt.NDArray[Any]) -> "EcefVector":
        start = np.dot(basis, self.start)
        end = np.dot(basis, self.end)
        return EcefVector(start, end)

    @property
    def reverse(self) -> "EcefVector":
        return EcefVector(self.end, self.start)

    def angle_between(self, other: "EcefVector") -> float:
        # Get absolute angles of both
        ascalar = self.scalar
        bscalar = other.scalar
        aangle = np.arctan2(ascalar[1], ascalar[0])
        bangle = np.arctan2(bscalar[1], bscalar[0])

        # Subract b from a and mod a circle
        aangle = (bangle - aangle) % (2 * math.pi)

        # Go negative if it's past pi
        if abs(aangle) > math.pi:
            aangle = -(2 * math.pi - aangle)
        return cast(float, aangle)

    @property
    def angle(self) -> float:
        ascalar = self.scalar
        aangle = np.arctan2(ascalar[1], ascalar[0])
        # Go negative if it's past pi
        if abs(aangle) > math.pi:
            aangle = -(2 * math.pi - aangle)
        return cast(float, aangle)

    def __str__(self) -> str:
        return "EcefVector(np.array([{}, {}]), np.array([{}, {}]))".format(
            self.start[0], self.start[1], self.end[0], self.end[1]
        )

    @property
    def len(self) -> float:
        return cast(float, np.linalg.norm(self.scalar))


class EcefPoly:
    def __init__(self, *args: Any):
        self._points: List[npt.NDArray[Any]] = []
        if len(args) > 0:
            if isinstance(args[0], Boundary):
                poly = args[0]
                lats = [x for x in poly.geometry.exterior.coords.xy[1]]
                lons = [x for x in poly.geometry.exterior.coords.xy[0]]
                # These boundary types always have the first point equal to the last. We're going to define this
                # as closed so we will de-dupe these first/last points.
                for lat, lon in zip(lats[:-1], lons[:-1]):
                    self._points.append(navpy.lla2ecef(lat, lon, 0, "deg"))

    def __str__(self) -> str:
        return str(self._points)

    def change_basis(self, basis: npt.NDArray[Any]) -> "EcefPoly":
        ret = EcefPoly()
        for p in self._points:
            ret._points.append(np.dot(basis, p))
        return ret

    @property
    def lines(self) -> List[Tuple[npt.NDArray[Any], npt.NDArray[Any]]]:
        ls: List[Tuple[npt.NDArray[Any], npt.NDArray[Any]]] = []
        first: Optional[npt.NDArray[Any]] = None
        last: Optional[npt.NDArray[Any]] = None
        for p in self._points:
            if first is None:
                first = p
                last = p
            else:
                assert last is not None
                ls.append((last, p))
                last = p
        if first is not None and last is not None:
            ls.append((last, first))
        return ls

    def intersection_xy(self, vector: EcefVector) -> List[npt.NDArray[Any]]:
        try:
            polygon = LineString(self._points)
            start = vector.start
            end = vector.end
            if len(start) > 2:
                start = start[:2]
            if len(end) > 2:
                end = end[:2]
            lsv = LineString((start, end))
            intersections = [polygon.intersection(lsv)]
            intersections = [x for x in intersections if isinstance(x, Point)]
        except Exception:
            LOG.exception(f"start: {vector.start}, end: {vector.end}")
            raise
        return [np.array([inter.coords.xy[0][0], inter.coords.xy[1][0]]) for inter in intersections]

    def add_position(self, position: npt.NDArray[Any]) -> "EcefPoly":
        ret = EcefPoly()
        for p in self._points:
            ret._points.append(p + position)
        return ret

    def entry_point_xy(self, vector: EcefVector) -> Optional[npt.NDArray[Any]]:
        ret: Optional[npt.NDArray[Any]] = None
        # Get intersections with the geofence
        points = self.intersection_xy(vector)
        if points is None:
            return ret
        # Sort by distance to first point to get our entry point
        points_distance = sorted([(np.linalg.norm(p - vector.start[:-1]), p) for p in points])
        if len(points_distance) > 0:
            ret = points_distance[0][1]
        return ret

    def project(self, position: npt.NDArray[Any]) -> Tuple[Optional[float], Optional[npt.NDArray[Any]]]:
        ret: Tuple[Optional[float], Optional[npt.NDArray[Any]]] = (None, None)
        try:
            points = [
                LineString([l[0][:-1], l[1][:-1]]).interpolate(
                    LineString([l[0][:-1], l[1][:-1]]).project(Point(position))
                )
                for l in self.lines
            ]
            points = [x for x in points if isinstance(x, Point)]
        except Exception:
            LOG.exception(f"position: {position}")
            raise

        points_distance = sorted([(np.linalg.norm(np.array(p) - position).item(), p) for p in points])
        if len(points_distance) > 0:
            ret = points_distance[0]
        return ret


class EcefFieldGridConvert:
    @staticmethod
    def from_surface_normal(surface_normal: npt.NDArray[Any]) -> "EcefFieldGridConvert":
        """Calculate Basis from surface normal and return grid convert"""

        # longitude vector is cross product between normal and vertical z axis
        long = np.cross(np.array([0, 0, 1]), surface_normal)

        # lattitude vector is cross between surface normal and longitude vec
        lat = np.cross(surface_normal, long)

        # now we have all 3 - return object
        return EcefFieldGridConvert(
            xvec=point_norm(long), yvec=point_norm(lat), zvec=point_norm(surface_normal), position_offset=surface_normal
        )

    def __init__(
        self, xvec: npt.NDArray[Any], yvec: npt.NDArray[Any], zvec: npt.NDArray[Any], position_offset: npt.NDArray[Any]
    ):
        self._basis_grid_to_ecef = np.array([xvec, yvec, zvec]).T
        self._basis_ecef_to_grid = np.linalg.inv(self._basis_grid_to_ecef)
        self._position_offset = position_offset

    def poly_convert(self, poly: EcefPoly) -> EcefPoly:
        return poly.add_position(-self._position_offset).change_basis(self._basis_ecef_to_grid)

    def vector_convert(self, vec: EcefVector) -> EcefVector:
        return (vec - self._position_offset).change_basis(self._basis_ecef_to_grid)

    def point_revert(self, point: npt.NDArray[Any], field_alt: float) -> npt.NDArray[Any]:
        return cast(
            npt.NDArray[Any], np.dot(self._basis_grid_to_ecef, np.append(point, field_alt)) + self._position_offset
        )

    def vector_revert(self, vector: EcefVector, field_alt: float) -> EcefVector:
        return EcefVector(
            start=self.point_revert(vector.start, field_alt=field_alt),
            end=self.point_revert(vector.end, field_alt=field_alt),
        )

    def point_convert(self, point: npt.NDArray[Any]) -> npt.NDArray[Any]:
        point = point - self._position_offset
        point = np.dot(self._basis_ecef_to_grid, point)
        return cast(npt.NDArray[Any], point[:-1])

    def path_convert(self, path: List[npt.NDArray[Any]]) -> List[npt.NDArray[Any]]:
        return [self.point_convert(p) for p in path]

    def vector2heading(self, vector: EcefVector) -> float:
        vector = self.vector_convert(vector)
        return angle2heading(vector.angle)


# Conversions


def ecef_distance(a: npt.NDArray[Any], b: npt.NDArray[Any]) -> float:
    return float(np.linalg.norm(b - a))


def fixed_alt(ecef: npt.NDArray[Any], alt: float) -> npt.NDArray[Any]:
    lla = navpy.ecef2lla(ecef, "deg")
    return cast(npt.NDArray[Any], navpy.lla2ecef(lla[0], lla[1], alt, "deg"))


def inplace_fixed_alt_iterable(ecefl: List[npt.NDArray[Any]], alt: float) -> None:
    for i in range(len(ecefl)):
        ecefl[i] = fixed_alt(ecefl[i], alt)


def ecef2lla_iterable(ecefl: List[npt.NDArray[Any]]) -> List[npt.NDArray[Any]]:
    ret: List[npt.NDArray[Any]] = []
    for ecef in ecefl:
        ret.append(navpy.ecef2lla(ecef, "deg"))
    return ret


def lla2ecef_iterable(llal: List[npt.NDArray[Any]]) -> List[npt.NDArray[Any]]:
    ret: List[npt.NDArray[Any]] = []
    for lla in llal:
        ret.append(navpy.lla2ecef(lla[0], lla[1], lla[2], "deg"))
    return ret


def translate_vector(vector: EcefVector, direction: AngleDirection) -> Optional[EcefVector]:
    if direction == AngleDirection.DIRECT:
        return None

    dirvec: Optional[npt.NDArray[Any]] = None

    if direction == AngleDirection.LEFT:
        dirvec = np.cross(vector.end, vector.scalar)
    if direction == AngleDirection.RIGHT:
        dirvec = np.cross(vector.scalar, vector.end)

    assert dirvec is not None
    return EcefVector(np.array([0.0, 0.0, 0.0]), dirvec / np.linalg.norm(dirvec))


def point_norm(point: npt.NDArray[Any]) -> npt.NDArray[Any]:
    return cast(npt.NDArray[Any], point / np.linalg.norm(point))


def inches2meters(inches: float) -> float:
    return (inches * MM_PER_INCH) / 1000


def point_distance(p1: npt.NDArray[Any], p2: npt.NDArray[Any]) -> float:
    return cast(float, abs(np.linalg.norm(p2 - p1)))


def eorta_turn_center_point(
    exit_point: npt.NDArray[Any], entry_point: npt.NDArray[Any], turn_radius_inches: float
) -> npt.NDArray[Any]:
    relative_point_entry = entry_point - exit_point
    hypotenuse = math.sqrt(math.pow(relative_point_entry[0], 2) + math.pow(relative_point_entry[1], 2))
    alpha = math.asin(relative_point_entry[1] / hypotenuse)
    beta = math.asin(relative_point_entry[0] / hypotenuse)
    ydelta = inches2meters(turn_radius_inches) * math.sin(alpha)
    xdelta = inches2meters(turn_radius_inches) * math.sin(beta)
    point = np.array([relative_point_entry[0] - xdelta, relative_point_entry[1] - ydelta])
    point += exit_point
    return point


def heading2angle(heading: float) -> float:
    return cast(float, ((math.pi / 2) - np.deg2rad(heading)) % (2 * math.pi))


def angle2heading(angle: float) -> float:
    return cast(float, (90 - np.rad2deg(angle)) % 360.0)


def heading2vector(heading: float) -> npt.NDArray[Any]:
    heading_angle = heading2angle(heading)
    return np.array([math.cos(heading_angle), math.sin(heading_angle)])

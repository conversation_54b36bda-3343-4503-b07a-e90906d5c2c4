from dataclasses import dataclass, field
from typing import Any, Dict, List, Literal, Optional, Union

from dataclass_wizard import JSONWizard

PointType = List[float]


@dataclass
class Position:
    x: float
    y: float
    z: Optional[float] = None

    @staticmethod
    def from_list(lst: List[float]) -> "Position":
        assert len(lst) <= 3
        return Position(*lst)

    def to_point(self) -> PointType:
        ret = [self.x, self.y]
        if self.z is not None:
            ret.append(self.z)
        return ret

    @property
    def longitude(self) -> float:
        return self.x

    @property
    def easting(self) -> float:
        return self.x

    @property
    def latitude(self) -> float:
        return self.y

    @property
    def northing(self) -> float:
        return self.y

    @property
    def altitude(self) -> Optional[float]:
        return self.z

    @property
    def elevation(self) -> Optional[float]:
        return self.z


@dataclass
class Point(JSONWizard):
    coordinates: PointType


@dataclass
class MultiPoint(JSONWizard):
    coordinates: List[PointType]


@dataclass
class LineString(JSONWizard):
    coordinates: List[PointType]


@dataclass
class MultiLineString(JSONWizard):
    coordinates: List[List[PointType]]


@dataclass
class Polygon(JSONWizard):
    coordinates: List[List[PointType]]


@dataclass
class MultiPolygon(JSONWizard):
    coordinates: List[List[List[PointType]]]


GeometryType = Union[Point, MultiPoint, LineString, MultiLineString, Polygon, MultiPolygon]


@dataclass
class Feature(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    geometry: GeometryType
    properties: Optional[Dict[str, Any]] = field(default_factory=dict)
    type: Literal["Feature"] = "Feature"


@dataclass
class FeatureCollection(JSONWizard):
    class _(JSONWizard.Meta):
        tag_key = "type"
        auto_assign_tags = True

    features: List[Feature] = field(default_factory=list)
    type: Literal["FeatureCollection"] = "FeatureCollection"

import os
from enum import Enum
from typing import Any, Dict, Iterator, List, Optional, Tuple, cast

import numpy as np
from attr import dataclass
from shapely import Polygon, wkt

from lib.common.config_file import ConfigFile, noncompact_serialize
from lib.common.file import repo_root
from lib.common.logging import get_logger
from lib.common.math import intersects
from lib.common.serialization.json import JsonSerializable

LOG = get_logger(__name__)


@dataclass
class LatLonStrs:
    lon: str
    lat: str


PRECISION = 15


class BoundaryType(str, Enum):
    """
    The different kinds of boundaries we know and care about.
    """

    FIELD = "FIELD"
    TURN_AROUND = "TURN_AROUND"
    CROPS = "CROPS"


class Boundary(JsonSerializable):
    """
    Represents a geofences.
    """

    ID = "id"
    COORDINATES = "coordinates"
    TYPE = "type"

    def __init__(self, geometry: Polygon, boundary_type: BoundaryType, id: str) -> None:
        self._id = id
        self._type = boundary_type
        self._geometry: Polygon = geometry

    @staticmethod
    def from_wkt(coordinates_wkt: str, boundary_type: BoundaryType, id: str) -> "Boundary":
        precise_coordinates_wkt = Boundary.round_precision_wkt(coordinates_wkt)
        if precise_coordinates_wkt != coordinates_wkt:
            LOG.warning("WKT coordinates changed due to precision in Shapely library")
            # LOG.warning(f"Before: {coordinates_wkt}")
            # LOG.warning(f"After:  {precise_coordinates_wkt}")

        geometry = wkt.loads(precise_coordinates_wkt).simplify(0.00001)
        if type(geometry) != Polygon:
            raise TypeError("Invalid geometry type loaded from WKT.")
        return Boundary(geometry, boundary_type, id)

    @staticmethod
    def round_precision_wkt(wkt_str: str) -> str:
        # 15 chosen by looking at Duro logs
        return cast(str, wkt.dumps(wkt.loads(wkt_str), rounding_precision=PRECISION))

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Boundary):
            raise NotImplementedError

        if other.id != self.id or other.type != self.type or other.geometry != self.geometry:
            # Note that there's no reason to check the WKT since that's a direct result of the
            # geometry - if the geometry is equal the WKT will be as well.
            return False
        return True

    @property
    def id(self) -> str:
        return self._id

    @property
    def type(self) -> BoundaryType:
        return self._type

    @property
    def coordinates_wkt(self) -> str:
        return cast(str, wkt.dumps(self._geometry, rounding_precision=PRECISION))

    @property
    def coordinates(self) -> List[Tuple[str, str]]:
        tokens: List[str] = self.coordinates_wkt.replace("POLYGON ((", "").replace("))", "").split(", ")
        return [cast(Tuple[str, str], tuple(s.split(" "))) for s in tokens]

    @property
    def geometry(self) -> Polygon:
        return self._geometry

    def with_replaced_coordinate(self, index: int, ll: LatLonStrs) -> "Boundary":
        """
        Return a new Boundary object, with a coordinate replaced.

        first/last index are the same thing.
        """
        coords = self.coordinates
        assert index < len(coords)

        new_point = (ll.lon, ll.lat)
        coords[index] = new_point
        if index == 0 or index == len(coords) - 1:
            # one of these will be repeat of coords[index] and one will be the wraparound point
            coords[0] = new_point
            coords[len(coords) - 1] = new_point

        coords_wkt = f"POLYGON (({', '.join([' '.join(coord) for coord in coords])}))"

        result = Boundary.from_wkt(id=self.id, boundary_type=self.type, coordinates_wkt=coords_wkt)
        return result

    def to_json(self) -> Dict[str, Any]:
        return {
            Boundary.ID: self.id,
            Boundary.COORDINATES: self.coordinates_wkt,
            Boundary.TYPE: self.type,
        }

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "Boundary":
        return Boundary.from_wkt(
            coordinates_wkt=data[Boundary.COORDINATES], boundary_type=data[Boundary.TYPE], id=data[Boundary.ID],
        )


class GeofencesConfigFile(ConfigFile):
    """
    Boundaries configuration file manager.
    """

    FILENAME = "geofences.json"
    ROOT_ELEMENT = "geofences"

    # extra level of indirection required to be more symmetric with how other ConfigFiles work
    #  - specifically, the load() API expects a root_element named like the file
    #    (so the JSON itself is self-descriptive)
    #    and then another dictionary underneath that.
    BOUNDARIES_KEY = "boundaries"
    EMPTY: Dict[str, Dict[str, Any]] = {ROOT_ELEMENT: {BOUNDARIES_KEY: []}}

    def __init__(self, config_dir: str, filename: str = FILENAME, root_element: str = ROOT_ELEMENT) -> None:
        super().__init__(
            config_dir=config_dir, filename=filename, root_element=root_element, serialize=noncompact_serialize
        )


class GeofenceAlreadyExistsException(Exception):
    pass


class Boundaries:
    """
    Loader and manager of multiple boundaries that are defined in a config file. Can be used to add or remove boundaries
    during runtime and automatically persist them to the same config file that was used for loading.
    """

    def __init__(
        self, config_file: Optional[GeofencesConfigFile] = None, boundaries: Optional[Dict[str, Boundary]] = None
    ) -> None:
        assert (
            config_file is not None or boundaries is not None
        ), "Must supply either geofence config file or boundaries"

        self._config = config_file
        self._point_buffer: List[Tuple[float, float]] = []
        self._buildable_point_buffer: List[Tuple[float, float]] = []
        self._boundaries: Dict[str, Boundary] = {}

        if self._config is not None:
            geofences_cfg: Dict[str, Any] = self._config.load(default=GeofencesConfigFile.EMPTY)
            boundaries_cfg: List[Dict[str, Any]] = geofences_cfg[GeofencesConfigFile.BOUNDARIES_KEY]
            self._boundaries = {boundary[Boundary.ID]: Boundary.from_json(boundary) for boundary in boundaries_cfg}
            LOG.debug(f"Loaded {len(self)} geofences from {self._config.filepath}")
        else:
            assert boundaries is not None
            self._boundaries = boundaries
            self._config = GeofencesConfigFile(os.path.join(repo_root(), "core/config/geo/unity"))

        self._available_types: List[BoundaryType] = [b.type for b in self._boundaries.values()]

    def __iter__(self) -> Iterator[Tuple[str, Boundary]]:
        return iter(self._boundaries.items())

    def __len__(self) -> int:
        return len(self._boundaries)

    def to_json(self) -> Dict[str, Any]:
        return {
            "boundaries": [boundary.to_json() for boundary in sorted(self._boundaries.values(), key=lambda b: b.id)]
        }

    def filter(self, type: BoundaryType) -> Dict[str, Boundary]:
        filtered: Dict[str, Boundary] = {}
        for k, v in self._boundaries.items():
            if v._type == type:
                filtered[k] = v
        return filtered

    def add(self, boundary: Boundary) -> None:
        existing_num: int = len(self._boundaries)
        if boundary.id in self._boundaries:
            raise GeofenceAlreadyExistsException(f"{boundary.id} already exists!")
        self._boundaries[boundary.id] = boundary
        LOG.info(f"Added geofence: {boundary.id}")
        LOG.info(f"Geofence total: {existing_num} -> {len(self._boundaries)}")
        self._updated()

    def update(self, boundary_id: str, index: int, lat: str, lon: str) -> None:
        assert index >= 0
        assert lat != 0
        assert lon != 0

        # get existing
        existing: Boundary = self._boundaries[boundary_id]
        LOG.debug("Existing geofence:")

        # log expected delta
        LOG.info(f"{boundary_id}[{index}] := {(lon, lat)}")

        # create new
        LOG.debug("Updated geofence:")
        new_boundary: Boundary = existing.with_replaced_coordinate(index, LatLonStrs(lat=lat, lon=lon))

        # update
        self._boundaries[boundary_id] = new_boundary

        # save
        self._updated()

    def add_point_to_buffer(self, latitude: float, longitude: float) -> bool:
        """
        Adds a single point to an uncommitted point buffer in memory.

        Parameters:
            latitude (float): The latitude of the point. Only latitudes between +-70 degrees are valid (no farming in the arctic circle).
            longitude (float): The longitude of the point.

        Returns:
            True on success, False if there was an issue with the point.
        """
        point: Tuple[float, float] = (longitude, latitude)

        # check vs previous point
        last_index = len(self._point_buffer) - 1
        previous_point = self._point_buffer[last_index] if last_index >= 0 else None
        if point == previous_point:
            LOG.warning(f"Point repeated consecutively: {point}")
            return False

        if not -70 <= latitude <= 70:
            LOG.warning(f"Bad latitude: {latitude}")
            LOG.warning(f"Add point failed: {point}")
            return False
        elif not -180 <= longitude < 180.0:
            LOG.warning(f"Bad longitude: {longitude}")
            LOG.warning(f"Add point failed: {point}")
            return False

        # check for existing intersections
        if len(self._point_buffer) >= 3:
            last_pt = self._point_buffer[-1]
            for i in range(1, len(self._point_buffer) - 1):
                if intersects(self._point_buffer[i - 1], self._point_buffer[i], last_pt, point):
                    LOG.warning(
                        f"Lat/Lon ({longitude}, {latitude}) intersects buffered line from point {i-1} to point {i}"
                    )
                    return False

        # add it to buffer
        self._point_buffer.append(point)

        # check if it is buildable
        if len(self.point_buffer) >= 3:
            candidate_polygon = self.point_buffer_polygon
            if not candidate_polygon.is_valid:
                LOG.warning(f"New point {point} would break buffered polygon")
            else:
                self._buildable_point_buffer = self._point_buffer.copy()

        LOG.info(f"Added point #{len(self._point_buffer)} to buffer: {point}")
        if len(self.buildable_point_buffer) == 0:
            LOG.info("Buffer is not yet buildable into a valid polygon")
        elif len(self.buildable_point_buffer) == len(self.point_buffer):
            LOG.info("Polygon can be built")
        else:
            LOG.warning(
                f"Only the first {len(self.buildable_point_buffer)}/{len(self.point_buffer)} points form a valid polygon"
            )

        return True

    def delete_point_from_buffer(self, index: int) -> None:
        assert index == len(self._point_buffer) - 1, (
            "Can only remove last point from buffer. "
            "Support for arbitrary removal is more complicated and requires re-resting buildability"
        )

        self._point_buffer = self._point_buffer[:-1]
        if len(self.buildable_point_buffer) > len(self.point_buffer):
            self._buildable_point_buffer = self._buildable_point_buffer[:-1]
            if len(self._buildable_point_buffer) < 3:
                self._buildable_point_buffer = []

    def clear_buffer(self) -> None:
        """
        Clears the current point buffer in memory.
        """
        self.point_buffer.clear()
        self.buildable_point_buffer.clear()
        LOG.info("Cleared geofence point buffer")

    @property
    def available_types(self) -> List[BoundaryType]:
        return self._available_types

    @property
    def point_buffer(self) -> List[Tuple[float, float]]:
        return self._point_buffer

    @property
    def buildable_point_buffer(self) -> List[Tuple[float, float]]:
        return self._buildable_point_buffer

    @property
    def buildable_point_buffer_polygon(self) -> Polygon:
        # order of points clockwise vs counter clockwise does not matter
        # there is no need to duplicate first/last point
        return Polygon(np.array(self.buildable_point_buffer))

    @property
    def point_buffer_polygon(self) -> Polygon:
        # order of points clockwise vs counter clockwise does not matter
        # there is no need to duplicate first/last point
        return Polygon(np.array(self._point_buffer))

    def commit_buffer(self, boundary_type: BoundaryType, name: str) -> Optional[Boundary]:
        """
        Commit the current point buffer as a polygon to the boundaries list and persist it to disk.
        Upon successful commit of the current buffer, the buffer is automatically cleared and ready
        for further additions. However if the point buffer encounters any errors before being
        successfully committed, the point buffer will remain.

        Parameters:
            boundary_type (BoundaryType): The kind of boundary this is.
            name (str): An optional identifier for this boundary. A name will be generated if not provided.

        Returns:
            True on successfully adding the buffer as a boundary.
        """
        if len(self.point_buffer) <= 2:
            LOG.error(f"Insufficient points in buffer to build polygon: {len(self.point_buffer)}")
            return None

        LOG.info(f"Committing geofence buffer of {len(self.point_buffer)} points as a {boundary_type} geofence: {name}")
        candidate_polygon = self.point_buffer_polygon

        if not candidate_polygon.is_valid:
            LOG.warning(
                f"Geofence point buffer (size={len(self.point_buffer)}) does not make a valid Polygon geometry."
            )
            candidate_polygon = self.buildable_point_buffer_polygon
            if not candidate_polygon.is_valid:
                LOG.error("Buildable point buffer was somehow invalid?")
                return None

        result: Optional[Boundary] = None
        try:
            candidate_result = Boundary.from_wkt(candidate_polygon.wkt, boundary_type=boundary_type, id=name)
            self.add(candidate_result)
            result = candidate_result
            self.clear_buffer()
        except ValueError:
            LOG.exception(f"Failed to add geofence point buffer (size={len(self.point_buffer)}) to boundaries.")
        except AssertionError:
            LOG.exception(f"Failed to add geofence point buffer (size={len(self.point_buffer)}) to boundaries.")
        return result

    def remove(self, geofence_id: str) -> bool:
        """
        Removes a boundary from the collection being managed by this object. Persists the change to the source config file immediately.

        Parameters:
            id (str): The ID to remove.

        Returns:
            True if the requested ID was removed, False if no matches were found for this ID.
        """
        if geofence_id not in self._boundaries:
            return False
        del self._boundaries[geofence_id]
        self._updated()
        return True

    def _updated(self) -> None:
        assert self._config is not None
        self._available_types = [b.type for b in self._boundaries.values()]
        self._config.save(self.to_json())

from typing import Callable, Generator, Iterable, Iterator, List, TypeVar

T = TypeVar("T")


class CircularBuffer(Iterable[T]):
    def __init__(self, size: int, default_factory: Callable[[], T]) -> None:
        self._list: List[T] = [default_factory() for _ in range(size)]
        self._tail = -1
        self._filled = False

    def push(self, val: T) -> None:
        not_empty = self._tail != -1
        self._tail = (self._tail + 1) % len(self._list)
        if not_empty and self._tail == 0:
            self._filled = True
        self._list[self._tail] = val

    def __getitem__(self, ind: int) -> T:
        if self._filled:
            ind = (ind + self._tail + 1) % len(self._list)
        else:
            ind = ind % len(self)
        return self._list[ind]

    def __iter__(self) -> Iterator[T]:
        if self._filled:
            start = self._tail + 1
            size = len(self._list)
        else:
            start = 0
            if self._tail == -1:
                size = 0
            else:
                size = self._tail + 1

        def __inner() -> Generator[T, None, None]:
            for i in range(start, start + size):
                ind = i % len(self._list)
                yield self._list[ind]

        return __inner()

    def __len__(self) -> int:
        if self._filled:
            return len(self._list)
        return self._tail + 1

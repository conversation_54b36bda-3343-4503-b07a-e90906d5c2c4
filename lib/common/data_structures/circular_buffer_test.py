from lib.common.data_structures.circular_buffer import Cir<PERSON><PERSON><PERSON><PERSON>


def test_single_elem_insert() -> None:
    val = 42
    b = CircularBuffer(1, int)
    assert len(b) == 0
    b.push(val)
    assert len(b) == 1
    assert b[0] == val
    l = list(b)
    assert l == [val]
    val += 1
    b.push(val)
    assert len(b) == 1
    assert b[0] == val
    l = list(b)
    assert l == [val]


def test_multi_elem() -> None:
    vals = list(range(0, 5))
    b = CircularBuffer(len(vals), lambda: 2024)
    for i, v in enumerate(vals):
        b.push(v)
        assert len(b) == i + 1
        assert b[-1] == v
    assert len(b) == len(vals)
    for i in range(0, len(vals)):
        assert b[i] == vals[i]
    l = list(b)
    assert l == vals
    val = len(vals)
    b.push(val)
    assert len(b) == len(vals)
    assert b[-1] == val
    assert b[0] == vals[1]

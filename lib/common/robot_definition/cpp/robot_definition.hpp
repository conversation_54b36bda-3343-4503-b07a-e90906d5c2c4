#pragma once

#include <atomic>
#include <memory>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <nlohmann/json.hpp>

namespace lib::common {

class RedisClient;

namespace robot_definition {

using json = nlohmann::json;

const std::string kRobotDefinitionKey = "robot_definition/";
const std::string kCurrentRobotDefinitionKey = "current";

const std::string kModuleIpPrefix = "10.10.20";
const std::string kMcbIpPrefix = "10.10.22";

constexpr int kRedisRetryDelay = 5;

const std::string kScanner1InternalIp = "*********";
const std::string kScanner2InternalIp = "*********";

constexpr uint32_t kScannerInternalPort = 4243;
constexpr uint32_t kModuleScanner1ExternalPort = 42431;
constexpr uint32_t kModuleScanner2ExternalPort = 42432;

constexpr uint32_t kScannerBootLoaderInternalPort = 1337;
constexpr uint32_t kModuleScanner1BootLoaderExternalPort = 13371;
constexpr uint32_t kModuleScanner2BootLoaderExternalPort = 13372;

class RowDefinition {
public:
  using ModuleSlotPair = std::tuple<uint32_t, uint32_t>;

  RowDefinition(json &row_definition, uint32_t local_module_id);
  inline uint32_t get_aimbot_leader() const { return aimbot_leader_id_; }
  inline std::string get_aimbot_address() const { return aimbot_address_; }
  inline std::unordered_map<std::string, std::string> get_computer_addresses() const { return computer_to_address_; }
  inline std::unordered_map<std::string, std::string> get_mcb_addresses() const { return computer_to_mcb_address_; }
  inline size_t get_number_of_predict_cams() const { return pid_to_cv_address_.size(); }
  inline size_t get_number_of_target_cams() const { return tid_to_cv_address_.size(); }
  inline std::unordered_map<uint32_t, std::string> get_sid_to_board_address() const { return sid_to_board_address_; }
  inline std::unordered_map<uint32_t, uint32_t> get_sid_to_port() const { return sid_to_port_; }
  inline std::unordered_map<uint32_t, uint32_t> get_sid_to_boot_loader_port() const { return sid_to_boot_loader_port_; }
  inline std::unordered_map<std::string, std::string> get_pid_to_cv_address() const { return pid_to_cv_address_; }
  inline std::unordered_map<std::string, std::string> get_tid_to_cv_address() const { return tid_to_cv_address_; }
  inline float get_predict_space_center_mm() const {
    return cumulative_offsets_mm_.empty() ? 0.0f : cumulative_offsets_mm_.back() / 2.0f;
  }
  inline std::vector<float> get_cumulative_offsets_mm() const { return cumulative_offsets_mm_; }
  inline std::unordered_set<std::string> get_disabled_predicts() const { return disabled_predicts_; }
  inline std::unordered_set<std::string> get_disabled_pulczars() const { return disabled_pulczars_; }
  inline ModuleSlotPair get_module_for_camera(const std::string camera_name) const {
    return camera_to_module_.at(camera_name);
  }
  inline ModuleSlotPair get_module_for_scanner(const uint32_t scanner_id) const {
    return sid_to_module_.at(scanner_id);
  }

private:
  std::shared_ptr<json> json_row_definition_;
  uint32_t aimbot_leader_id_;
  std::string aimbot_address_;
  std::unordered_map<std::string, std::string> computer_to_address_;
  std::unordered_map<std::string, std::string> computer_to_mcb_address_;
  std::unordered_set<std::string> disabled_predicts_;
  std::unordered_set<std::string> disabled_pulczars_;
  std::vector<float> cumulative_offsets_mm_;
  std::unordered_map<uint32_t, std::string> sid_to_board_address_;
  std::unordered_map<uint32_t, uint32_t> sid_to_port_;
  std::unordered_map<uint32_t, uint32_t> sid_to_boot_loader_port_;
  std::unordered_map<std::string, std::string> pid_to_cv_address_;
  std::unordered_map<std::string, std::string> tid_to_cv_address_;
  std::unordered_map<std::string, ModuleSlotPair> camera_to_module_;
  std::unordered_map<uint32_t, ModuleSlotPair> sid_to_module_;
};

class RobotDefinition {
public:
  RobotDefinition(const RobotDefinition &robot_definition) = delete;
  RobotDefinition &operator=(const RobotDefinition &) = delete;
  ~RobotDefinition();

  static std::shared_ptr<RobotDefinition> get();
  void initialize_for_module(uint32_t module_id);
  std::unordered_map<std::string, std::string> get_aimbot_addresses() const;
  std::unordered_map<std::string, std::string> get_all_computer_addresses() const;
  std::unordered_map<std::string, std::string> get_all_mcb_addresses() const;
  inline size_t get_number_of_rows() const { return row_definitions_.size(); }
  inline std::shared_ptr<RowDefinition> get_row_definition(uint32_t row_id) const {
    return row_definitions_.at(row_id);
  }
  inline std::shared_ptr<RowDefinition> get_local_row_definition() const { return local_row_definition_; }

private:
  lib::common::bot::ScopedBotStopEvent bse_;
  std::shared_ptr<RedisClient> redis_client_;
  std::shared_ptr<json> json_robot_definition_;
  std::unordered_map<uint32_t, std::shared_ptr<RowDefinition>> row_definitions_;

  std::atomic<uint32_t> module_id_;
  std::atomic<uint32_t> row_id_;
  std::shared_ptr<RowDefinition> local_row_definition_;

  RobotDefinition();
  void shutdown();
  void get_current_robot_definition_from_redis();
};

} // namespace robot_definition
} // namespace lib::common
#include <limits>

#include "lib/common/robot_definition/cpp/robot_definition.hpp"

#include <fmt/format.h>

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/exceptions.h>
#include <lib/common/cpp/utils/generation.hpp>
#include <lib/common/redis/redis_client.hpp>

#include <spdlog/spdlog.h>

namespace lib::common::robot_definition {

RowDefinition::RowDefinition(json &row_definition, uint32_t local_module_id = 0)
    : json_row_definition_(std::make_shared<json>(row_definition)) {
  uint32_t module_id;
  std::string module_str;
  std::string module_ip;
  std::string mcb_ip;
  std::string s1_ip;
  std::string s2_ip;
  uint32_t s1_port;
  uint32_t s2_port;
  uint32_t s1_bl_port;
  uint32_t s2_bl_port;
  uint32_t pid;
  uint32_t s1id;
  uint32_t s2id;

  int i = 0;
  float cumulative_offset = 0.0f;
  for (auto &module : row_definition["modules"]) {
    if (i != 0) {
      cumulative_offset += module["module_spacing_mm"].get<float>();
      cumulative_offsets_mm_.push_back(cumulative_offset);
    }

    auto disabled = module["disabled"].get<bool>();
    if (disabled) {
      disabled_predicts_.insert(fmt::format("predict{}", i + 1));
      disabled_pulczars_.insert(fmt::format("pulczar{}", 2 * i + 1));
      disabled_pulczars_.insert(fmt::format("pulczar{}", 2 * i + 2));
    }

    module_id = module["module_id"].get<uint32_t>();
    module_str = fmt::format("module{}", module_id);
    module_ip = "localhost";
    mcb_ip = fmt::format("{}.{}", kMcbIpPrefix, module_id);
    s1_ip = kScanner1InternalIp;
    s2_ip = kScanner2InternalIp;
    s1_port = kScannerInternalPort;
    s2_port = kScannerInternalPort;
    s1_bl_port = kScannerBootLoaderInternalPort;
    s2_bl_port = kScannerBootLoaderInternalPort;
    pid = i + 1;
    s1id = 2 * i + 1;
    s2id = 2 * i + 2;
    if (module_id != local_module_id) {
      module_ip = fmt::format("{}.{}", kModuleIpPrefix, module_id);
      s1_ip = module_ip;
      s2_ip = module_ip;
      s1_port = kModuleScanner1ExternalPort;
      s2_port = kModuleScanner2ExternalPort;
      s1_bl_port = kModuleScanner1BootLoaderExternalPort;
      s2_bl_port = kModuleScanner2BootLoaderExternalPort;
    }

    if (module["aimbot_host"].get<bool>()) {
      aimbot_leader_id_ = module_id;
      aimbot_address_ = module_ip;
    }

    if (!disabled) {
      computer_to_address_[module_str] = module_ip;
      computer_to_mcb_address_[module_str] = mcb_ip;
    }

    sid_to_board_address_[s1id] = s1_ip;
    sid_to_board_address_[s2id] = s2_ip;
    sid_to_port_[s1id] = s1_port;
    sid_to_port_[s2id] = s2_port;
    sid_to_boot_loader_port_[s1id] = s1_bl_port;
    sid_to_boot_loader_port_[s2id] = s2_bl_port;
    pid_to_cv_address_[fmt::format("predict{}", pid)] = module_ip;
    tid_to_cv_address_[fmt::format("target{}", s1id)] = module_ip;
    tid_to_cv_address_[fmt::format("target{}", s2id)] = module_ip;

    camera_to_module_[fmt::format("predict{}", pid)] = std::make_tuple(module_id, std::numeric_limits<uint32_t>::max());
    camera_to_module_[fmt::format("target{}", s1id)] = std::make_tuple(module_id, 0);
    sid_to_module_[s1id] = std::make_tuple(module_id, 0);
    camera_to_module_[fmt::format("target{}", s2id)] = std::make_tuple(module_id, 1);
    sid_to_module_[s2id] = std::make_tuple(module_id, 1);

    ++i;
  }
}

RobotDefinition::RobotDefinition()
    : bse_(lib::common::bot::BotStopHandler::get().create_scoped_event("GlobalRobotDefinition",
                                                                       std::bind(&RobotDefinition::shutdown, this))) {
  // at some point we can make this backwards compatible with slayer
  if (carbon::common::is_reaper()) {
    redis_client_ = std::make_shared<RedisClient>();
    redis_client_->wait_until_ready();
    get_current_robot_definition_from_redis();
    if (json_robot_definition_->contains("rows")) {
      for (auto &row : (*json_robot_definition_)["rows"]) {
        row_definitions_[row["row_id"].get<uint32_t>()] = std::make_shared<RowDefinition>(row);
      }
    }
  }
}

std::shared_ptr<RobotDefinition> RobotDefinition::get() {
  static std::shared_ptr<RobotDefinition> inst{new RobotDefinition()};
  return inst;
}

RobotDefinition::~RobotDefinition() { shutdown(); }

void RobotDefinition::shutdown() { bse_.set(); }

void RobotDefinition::get_current_robot_definition_from_redis() {
  while (!bse_.is_stopped()) {
    try {
      bool exists = redis_client_->hexists("robot_definition/",
                                           "current"); // current key wont exist if definition is yet to be set
      if (!exists) {
        spdlog::warn("No robot definition available in redis, setting as empty");
        json_robot_definition_ = std::make_shared<json>();
        return;
      }
      auto encoded_robot_def = redis_client_->hget("robot_definition/", "current");
      if (!encoded_robot_def) {
        throw maka_error("Failed to get robot definition from redis");
      }
      std::string robot_def_str = encoded_robot_def.value();
      spdlog::info("Got robot definition: {}", robot_def_str);
      auto json_robot_def = nlohmann::json::parse(robot_def_str);
      json_robot_definition_ = std::make_shared<json>(json_robot_def);
      return;
    } catch (const std::exception &e) {
      spdlog::error("Failed to get robot definition: {}, retrying", e.what());
      std::this_thread::sleep_for(std::chrono::seconds(kRedisRetryDelay));
    }
  }
}

void RobotDefinition::initialize_for_module(uint32_t module_id) {
  module_id_ = module_id;
  if (json_robot_definition_->contains("rows")) {
    for (auto &row : (*json_robot_definition_)["rows"]) {
      for (auto &module : row["modules"]) {
        if (module["module_id"].get<uint32_t>() == module_id) {
          row_id_ = row["row_id"].get<uint32_t>();
          local_row_definition_ = std::make_shared<RowDefinition>(row, module_id);
          return;
        }
      }
    }
  } else {
    spdlog::warn("No rows found in robot definition");
    return;
  }
}

std::unordered_map<std::string, std::string> RobotDefinition::get_aimbot_addresses() const {
  std::unordered_map<std::string, std::string> aimbot_addresses;
  for (const auto &[id, row_def] : row_definitions_) {
    // row{id} for consistency with the rest of the system
    aimbot_addresses[fmt::format("row{}", id)] = row_def->get_aimbot_address();
  }
  return aimbot_addresses;
}

std::unordered_map<std::string, std::string> RobotDefinition::get_all_computer_addresses() const {
  std::unordered_map<std::string, std::string> computer_addresses;
  for (const auto &[_, row_def] : row_definitions_) {
    auto computer_to_address = row_def->get_computer_addresses();
    computer_addresses.insert(computer_to_address.begin(), computer_to_address.end());
  }
  return computer_addresses;
}

std::unordered_map<std::string, std::string> RobotDefinition::get_all_mcb_addresses() const {
  std::unordered_map<std::string, std::string> mcb_addresses;
  for (const auto &[_, row_def] : row_definitions_) {
    auto computer_to_mcb_address = row_def->get_mcb_addresses();
    mcb_addresses.insert(computer_to_mcb_address.begin(), computer_to_mcb_address.end());
  }
  return mcb_addresses;
}

} // namespace lib::common::robot_definition
from typing import Dict, List, Set, Tuple

class RowDefinition:
    def get_aimbot_leader(self) -> int: ...
    def get_aimbot_address(self) -> str: ...
    def get_computer_addresses(self) -> Dict[str, str]: ...
    def get_mcb_addresses(self) -> Dict[str, str]: ...
    def get_number_of_predict_cams(self) -> int: ...
    def get_number_of_target_cams(self) -> int: ...
    def get_sid_to_board_address(self) -> Dict[int, str]: ...
    def get_sid_to_port(self) -> Dict[int, int]: ...
    def get_sid_to_boot_loader_port(self) -> Dict[int, int]: ...
    def get_pid_to_cv_address(self) -> Dict[str, str]: ...
    def get_tid_to_cv_address(self) -> Dict[str, str]: ...
    def get_cumulative_offsets_mm(self) -> List[float]: ...
    def get_disabled_predicts(self) -> Set[str]: ...
    def get_disabled_pulczars(self) -> Set[str]: ...
    def get_module_for_camera(self, cam_name: str) -> Tuple[int, int]: ...
    def get_module_for_scanner(self, scanner_id: int) -> Tuple[int, int]: ...

class RobotDefinition:
    @staticmethod
    def get() -> "RobotDefinition": ...
    def initialize_for_module(self, module_id: int) -> None: ...
    def get_aimbot_addresses(self) -> Dict[str, str]: ...
    def get_row_definition(self, row_id: int) -> "RowDefinition": ...
    def get_all_computer_addresses(self) -> Dict[str, str]: ...
    def get_all_mcb_addresses(self) -> Dict[str, str]: ...
    def get_local_row_definition(self) -> "RowDefinition": ...

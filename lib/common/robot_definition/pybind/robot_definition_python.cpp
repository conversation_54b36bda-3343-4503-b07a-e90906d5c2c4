#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "lib/common/robot_definition/cpp/robot_definition.hpp"

namespace py = pybind11;

namespace lib::common::robot_definition {

PYBIND11_MODULE(robot_definition_python, m) {
  py::class_<RowDefinition, std::shared_ptr<RowDefinition>>(m, "RowDefinition")
      .def("get_aimbot_leader", &RowDefinition::get_aimbot_leader, py::call_guard<py::gil_scoped_release>())
      .def("get_aimbot_address", &RowDefinition::get_aimbot_address, py::call_guard<py::gil_scoped_release>())
      .def("get_computer_addresses", &RowDefinition::get_computer_addresses, py::call_guard<py::gil_scoped_release>())
      .def("get_mcb_addresses", &RowDefinition::get_mcb_addresses, py::call_guard<py::gil_scoped_release>())
      .def("get_number_of_predict_cams", &RowDefinition::get_number_of_predict_cams,
           py::call_guard<py::gil_scoped_release>())
      .def("get_number_of_target_cams", &RowDefinition::get_number_of_target_cams,
           py::call_guard<py::gil_scoped_release>())
      .def("get_sid_to_board_address", &RowDefinition::get_sid_to_board_address,
           py::call_guard<py::gil_scoped_release>())
      .def("get_sid_to_port", &RowDefinition::get_sid_to_port, py::call_guard<py::gil_scoped_release>())
      .def("get_sid_to_boot_loader_port", &RowDefinition::get_sid_to_boot_loader_port,
           py::call_guard<py::gil_scoped_release>())
      .def("get_pid_to_cv_address", &RowDefinition::get_pid_to_cv_address, py::call_guard<py::gil_scoped_release>())
      .def("get_tid_to_cv_address", &RowDefinition::get_tid_to_cv_address, py::call_guard<py::gil_scoped_release>())
      .def("get_cumulative_offsets_mm", &RowDefinition::get_cumulative_offsets_mm,
           py::call_guard<py::gil_scoped_release>())
      .def("get_disabled_predicts", &RowDefinition::get_disabled_predicts, py::call_guard<py::gil_scoped_release>())
      .def("get_disabled_pulczars", &RowDefinition::get_disabled_pulczars, py::call_guard<py::gil_scoped_release>())
      .def("get_module_for_camera", &RowDefinition::get_module_for_camera, py::arg("cam_name"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_module_for_scanner", &RowDefinition::get_module_for_scanner, py::arg("scanner_id"),
           py::call_guard<py::gil_scoped_release>());

  py::class_<RobotDefinition, std::shared_ptr<RobotDefinition>>(m, "RobotDefinition")
      .def_static("get", &RobotDefinition::get, py::call_guard<py::gil_scoped_release>())
      .def("initialize_for_module", &RobotDefinition::initialize_for_module, py::arg("module_id"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_aimbot_addresses", &RobotDefinition::get_aimbot_addresses, py::call_guard<py::gil_scoped_release>())
      .def("get_row_definition", &RobotDefinition::get_row_definition, py::arg("row_id"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_all_computer_addresses", &RobotDefinition::get_all_computer_addresses,
           py::call_guard<py::gil_scoped_release>())
      .def("get_all_mcb_addresses", &RobotDefinition::get_all_mcb_addresses, py::call_guard<py::gil_scoped_release>())
      .def("get_local_row_definition", &RobotDefinition::get_local_row_definition,
           py::call_guard<py::gil_scoped_release>());
}
} // namespace lib::common::robot_definition

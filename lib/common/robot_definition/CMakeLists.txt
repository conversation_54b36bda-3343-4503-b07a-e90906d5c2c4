file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.hpp)
add_library(robot_definition SHARED ${SOURCES})
target_compile_definitions(robot_definition PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(robot_definition PUBLIC m stdc++fs pthread rt exceptions fmt redis_client utils bot_stop)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(robot_definition_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(robot_definition_python PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
target_link_libraries(robot_definition_python PUBLIC robot_definition)
set_target_properties(robot_definition_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)


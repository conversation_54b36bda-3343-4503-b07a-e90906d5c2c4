import itertools
from typing import Any, Callable, Dict, Optional, TypeVar, Union

from lib.common.logging import get_logger
from lib.common.web.http.error import HTTPError

LOG = get_logger(__name__)

F = TypeVar("F", bound=Callable[..., Any])


def int_range(low: int, high: int) -> Callable[[Union[str, float]], int]:  # half-open
    def validate_and_parse(v: Union[str, float]) -> int:
        v = int(v)
        if v >= high or v < low:
            raise ValueError("{} is outside range {}-{}".format(v, low, high))
        return v

    return validate_and_parse


def str_choice(*args: str) -> Callable[[str], str]:
    def validate_and_parse(v: str) -> str:
        v = str(v)
        if v not in args:
            raise ValueError("Invalid choice: {} (expected one of {})".format(v, args))
        return v

    return validate_and_parse


def based_int(v: str) -> int:
    v = v.lower()
    if v.startswith("0x"):
        return int(v[2:], 16)
    elif v.startswith("0b"):
        return int(v[2:], 2)
    elif v.startswith("0o"):  # just don't.
        return int(v[2:], 8)
    else:
        return int(v)


def singleton(constructor: F) -> Callable[[Any], Any]:
    def validate_and_parse(v: Any) -> Any:
        if len(v) > 1:
            raise ValueError("Not a singleton: {}".format(v))
        return constructor(v[0])

    return validate_and_parse


def optional(constructor: F) -> Callable[[Optional[Any]], Optional[Any]]:
    """This argument may not be present and it it's not will be come None"""

    def validate_and_parse(v: Optional[Any]) -> Optional[Any]:
        if v is None:
            return None
        return constructor(v)

    return validate_and_parse


# decorator for merging parameters
D = TypeVar("D", bound=Dict[str, Any])


def merge_add(func: F) -> Callable[[D, D, D, D], Any]:
    def merger(
        bound_params: Dict[str, Any], rparams: Dict[str, Any], qparams: Dict[str, Any], bparams: Dict[str, Any]
    ) -> Any:
        # The same param may be specified multiple times in a query string.
        # This is silly, so assume singletons so the caller doesn't have to unpack stuff
        if [v for v in qparams.values() if isinstance(v, list) and len(v) != 1]:
            raise HTTPError(400, "Multiple query string params with the same name")
        merged_params = {
            k: v
            for k, v in itertools.chain(
                bound_params.items(),
                rparams.items(),
                ((k, v[0] if isinstance(v, list) else v) for k, v in qparams.items()),  # qparams are always lists
                bparams.items(),
            )
        }
        try:
            return func(**merged_params)
        except TypeError:
            LOG.error(f"merged params: {merged_params}")
            raise

    return merger

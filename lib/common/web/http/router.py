# Maka Web Router MkII
# Designed to allow creation of JSON REST endpoints without a lot of typing,
# without menehune dependencies,
# and with a medium-effort attempt at input/output validation and normalization
# see web/maka_routes.py for how to create it, and /botd.py for how to run it
#
# Success is returned as JSON serialization of a dict returned by route handlers.
# Other things can be returned by setting content_type for the route.
# None is translated into null
# Errors are all returned as JSON with at least a "message" field. More fields are
# optional

import functools
import http.server
import json
import re
import traceback
from abc import ABC, abstractmethod
from asyncio import Future
from collections import OrderedDict, defaultdict
from typing import Any, Callable, DefaultDict, Dict, List, Optional, Pattern, Tuple, Type, Union

import requests
from urllib.parse import parse_qs, urlparse

import lib.common.logging
import lib.common.tasks.manager
from lib.common.error import MakaException
from lib.common.web.http.error import HTTPError

LOG = lib.common.logging.get_logger(__name__)

_METHODS = set(["GET", "HEAD", "POST", "PUT", "DELETE", "CONNECT", "OPTIONS", "TRACE", "PATCH"])


class ContentType(object):
    json = "application/json; charset=utf-8"
    png = "image/png"
    js = "text/javascript; charset=utf-8"
    html = "text/html; charset=utf-8"
    text = "text/plain; charset=utf-8"


# singleton that can be passed to body_desc or query_desc to indicate that we don't care
class Whatever(object):
    @staticmethod
    def keys() -> List[Any]:
        return []


# Used for non-200, non-error responses.
# 'value' can mean different things depending on the code (redirects, etc), but is currently unused.
# because the only actual use of this at the time of writing is 304, which has no body
# If we need true redirects, additional logic will need to be added.
class HTTPReturn(MakaException):
    def __init__(self, code: int):
        self.code = code


class _RouterRequestHandler(http.server.BaseHTTPRequestHandler):
    error_content_type = ContentType.json

    def log_error(self, *args: Any, **kwargs: Any) -> None:
        LOG.error(*args, **kwargs)

    def log_message(self, *args: Any, **kwargs: Any) -> None:
        # LOG.info(*args, **kwargs)
        pass

    def send_error(self, code: int, message: Optional[str] = None, _: Optional[str] = None) -> None:
        LOG.error("HTTP error {}: {}".format(code, message))
        self.send_response(code, json.dumps({"message": message}))

    def __getattr__(self, attr: str) -> Callable[..., Any]:
        if attr.startswith("do_") and attr[3:] in _METHODS:
            return lambda: self.server.route(self)  # type: ignore
        raise AttributeError("%r object has no attribute %r" % (self.__class__.__name__, attr))


class RouteHandler:
    """
    RouteHandler is responsible for handling the metadata associated with the specific route. Minimally
    this is the callback to call for handling the route.
    """

    def __init__(self, func: Callable[..., Any], content_type: str):
        self.func = func
        self.content_type = content_type


IpPort = Tuple[str, int]


class Router(http.server.HTTPServer):
    def __init__(
        self,
        address: IpPort,
        routes_by_method: DefaultDict[str, "OrderedDict[Pattern[str], RouteHandler]"],
        bound_params: Any,
    ):
        self._routes_by_method: DefaultDict[str, "OrderedDict[Pattern[str], RouteHandler]"] = routes_by_method

        self._bound_params = bound_params  # Args to RouteHandler.func; can be anything
        super().__init__(address, _RouterRequestHandler)

    def serve_forever(self, poll_interval: float = 0.5) -> None:
        LOG.info("Starting Robot HTTP Server at http://{}:{}...".format(*self.server_address))

        self.stop = False
        while not self.stop:
            try:
                self.handle_request()
            except ValueError:
                # ValueError means Invalid file descriptor; socket closed
                self.stop = True
            except ConnectionRefusedError:
                self.stop = True

        LOG.info("Robot HTTP Server stopped")

    def shutdown(self) -> None:
        self.stop = True

        try:
            self.server_close()
            requests.get("http://{}:{}/maka/ping".format(*self.server_address), timeout=0.001)
        except requests.exceptions.ReadTimeout:
            pass
        except requests.exceptions.ConnectionError:
            pass

        LOG.info("Closing http://{}:{}/maka/ping".format(*self.server_address))

    def route(self, request_handler: _RouterRequestHandler) -> None:  # noqa
        method = request_handler.command
        body: Optional[bytes]
        if "Content-Length" in request_handler.headers:
            body = request_handler.rfile.read(int(request_handler.headers["Content-Length"]))
        else:
            body = None
        res: Optional[bytes]
        path_obj = urlparse(request_handler.path)
        path = path_obj.path
        # ⚠️️⚠️⚠️🚨🚨🚨 ❗SUBOPTIMAL ALGORITHM ALERT ❗🚨🚨🚨⚠️️⚠️⚠️ #
        # This search is O(n) in the total number of routes, it could theorhetically be O(n) in the request path length
        # There are a few reasons for this
        #  We want to be able to put arbitrary characters in route params. Specifically '/' as a device tree separator
        #  because of this, we can't 'split' paths on / (because they'd break DT paths into multiple paths)
        #  this requirement also makes most off-the-shelf routers infeasible
        #  so we want full-on regex based path matching
        #  an FSM based regex engine that supports regex sets and not backreferences would solve this problem by compiling the REs into an O(len(requested_path)) state machine
        #  standard python does not have such a device, which leaves a few options, none of which are fantastic
        #   A) Bodge one together by putting each group in its own capture group and joining all the routes into a disjunction
        #       - This would perform well cause AIUI the python re library is reasonably fast and VM ovearhead is minimal
        #       - It's difficult to implement correctly and the resultant implementation would be difficult to read
        #       - This is difficult to implement and requires quite a bit of metadata to maintain sanity
        #           (otherwise you end up with "route 142 param 2, overall group 300 is the wrong type"
        #   B) use a non-standard library that does what we want
        #       - We could use one that doesn't support backreferences, and is guaranteed to build an efficient FSM
        #       - I did some looking and didn't find a one with python bindings that looked reliable
        #       - We could do our own bindings to a good C++ or C one, but that seems like a lot of work for not much gain
        #   C) build our own route parsing logic not using REs
        #       - more work, more code,
        #       - and given how slow python attribute lookup and dispatch is, would probably only be a win for mugh bigger route tables than we care about
        #   D) use a crappier algorithm that's easy to write
        #       - and easy to read
        #       - the route table is unlikely to go beyond hundreds of routes
        #       - something something premature optimisation
        #
        # I went with D for now. If our route table gets big enough that this shows up in a profiler, we can
        # re-evaluate that decision (and likely implement option A or B)

        def finish_request(res_code: int, res: bytes, content_type: str) -> None:
            """Finish headers, write the body, close up."""
            request_handler.send_response(res_code)
            request_handler.send_header("Access-Control-Allow-Origin", "*")
            request_handler.send_header("Access-Control-Allow-Methods", "GET,POST,OPTIONS")
            request_handler.send_header("Access-Control-Allow-Headers", "x-api-key,Content-Type")
            if res is not None:
                request_handler.send_header("Content-Type", content_type)
                request_handler.send_header("Content-Length", str(len(res)))
                request_handler.end_headers()
                try:
                    request_handler.wfile.write(res)  # res is opaque data
                except BrokenPipeError:
                    pass
                except ConnectionResetError:
                    pass
            else:
                request_handler.end_headers()

        def process_handler_return(ret: Any, content_type: str) -> Tuple[int, bytes]:
            """Handle method returns and convert them to the appropriate internal type based in mime type."""
            res_code = 200
            assert route_handler is not None
            if route_handler.content_type.startswith("application/json"):
                res = bytes(json.dumps(ret), "utf-8")
            elif route_handler.content_type.startswith("text/plain"):
                res = bytes(ret, "utf-8")
            else:
                res = ret
            return res_code, res

        def future_glue(content_type: str, f: "Future[Any]") -> None:
            """Take the result from our future, process it, send it out with the headers, and close up."""
            ret = f.result()
            res_code, res = process_handler_return(ret, content_type)
            finish_request(res_code, res, content_type)
            request_handler.close_connection = True

        route_handler = None
        try:
            for route_re, route_handler in self._routes_by_method[method].items():
                m = route_re.match(path)
                if m is not None:
                    try:
                        ret = route_handler.func(self._bound_params, m.groupdict(), path_obj.query, body)
                    except Exception:
                        LOG.error(f"Route failed: {path}")
                        LOG.error(f"deps: {self._bound_params}")
                        LOG.error(f"params: {m.groupdict()}")
                        LOG.error(f"query: {path_obj.query}")
                        LOG.error(f"body: {str(body)}")
                        raise

                    # If we have been given a MakaTask then this response will be async on the future of the task
                    if isinstance(ret, lib.common.tasks.manager.MakaTask):
                        request_handler.close_connection = False
                        ret.add_done_callback(functools.partial(future_glue, route_handler.content_type))
                        return

                    # Not MakaTask, so the response is synchronous directly from calling the method
                    res_code, res = process_handler_return(ret, route_handler.content_type)
                    break
            else:
                raise HTTPError(404, "Not Found")
        except HTTPError as e:
            LOG.exception(f"HTTP error requesting {path}")
            res = bytes(json.dumps({"message": e.message}), "utf-8")
            res_code = e.code
        except HTTPReturn as e:
            res = None
            res_code = e.code
            # TODO 303, 307, 308 would require additional processing for actual redirection
        except Exception as e:
            LOG.exception("Exception requesting {}".format(path))
            res = bytes(
                json.dumps({"message": str(e), "exc_type": str(type(e)), "traceback": traceback.format_exc()}), "utf-8"
            )
            res_code = 500

        content_type = route_handler.content_type if route_handler else "text/html"
        assert res
        finish_request(res_code, res, content_type)


def validate_params(params: Dict[str, Any], desc: Union[Type[Whatever], Dict[str, Any], None]) -> Dict[str, Any]:
    if desc is Whatever:
        return params
    validated = {}
    assert isinstance(desc, dict)
    for k, validator in desc.items():
        if k not in params:
            try:
                validated[k] = validator(None)
            except ValueError:
                raise HTTPError(400, "Missing parameter {}".format(k))
            except TypeError:
                LOG.error(f"Could not validate: {k}")
                raise HTTPError(400, "Missing parameter {}".format(k))
        else:
            validated[k] = validator(params[k])
    if len(validated) != len(desc):
        raise HTTPError(400, "Extra parameters: {}".format(", ".join(set(params.keys()) - set(desc.keys()))))
    return validated


class RouterBuilder:
    def __init__(self, *argnames: str):
        self._argnames = set(argnames)
        self._routes_by_method: DefaultDict[str, "OrderedDict[Pattern[str], RouteHandler]"] = defaultdict(OrderedDict)

    # binds args as the positional parameters to a server class, which can be intantiated
    def build_with(self, **kwargs: Any) -> Callable[..., Router]:
        assert set(kwargs.keys()) == self._argnames, "Missing bound arguments"
        return lambda server_address=("", 6666): Router(server_address, self._routes_by_method, kwargs)

    # Route decorator
    #
    # method should be HTTP method as a string, 'GET', 'POST', ETC
    # route_str should be a regex to match the route
    # query_desc should be a map of "name": type (where type is any constructor, int, str, etc)
    # body_desc should be the same
    # route params are automatically determined by the names of RE capture groups, and always strings
    # when the route
    # func will be called with (bound_params, rparams, qparams, bparams) where
    #     bound_params: dict of kwargs passed to build_with(...)
    #     (r|q|p)params: dicts of parameters from HTTP route, query, and body, respectively
    def add(
        self,
        method: str,
        route_str: str,
        body_desc: Optional[Union[Type[Whatever], Dict[str, Any]]] = None,
        query_desc: Optional[Union[Type[Whatever], Dict[str, Any]]] = None,
        content_type: str = ContentType.json,
    ) -> Callable[[Any], Any]:  # decorator generator, called once, return decorator method = method.upper()
        assert method in _METHODS, "Route {} has invalid method: {}".format(route_str, method)
        body_desc = body_desc if body_desc is not None else {}
        query_desc = query_desc if query_desc is not None else {}
        route = re.compile(route_str + "/*$")
        all_param_names = (
            list(self._argnames) + list(body_desc.keys()) + list(query_desc.keys()) + list(route.groupindex.keys())
        )
        assert len(all_param_names) == len(set(all_param_names)), "Parameters do not all have unique names: {}".format(
            all_param_names
        )

        def decor(func: Callable[..., Any]) -> None:  # actual decorator, called once, returns nothing
            def handle(
                bound_params: str, rparams: str, query: str, body: Optional[str]
            ) -> Any:  # HTTP handler, called for each request
                if body is not None:
                    if not isinstance(body, str):
                        body = body.decode()

                    bparams = validate_params(json.loads(body), body_desc)
                else:
                    bparams = {}
                if query:
                    qparams = validate_params(parse_qs(query, keep_blank_values=True, strict_parsing=True), query_desc)
                else:
                    qparams = {}
                return func(bound_params, rparams, qparams, bparams)

            self._routes_by_method[method][route] = RouteHandler(handle, content_type=content_type)
            # doesn't return anything cause the caller really shouldn't be invoking the result of this

        return decor


class ComponentRouterBuilder(ABC):
    def __init__(self, base_route: str):
        assert base_route.startswith("/")
        assert not base_route.startswith("/maka")
        self._base_route = f"/maka{base_route}"

    def suburl(self, s: str) -> str:
        assert s.startswith("/")
        return f"{self._base_route}{s}"

    @abstractmethod
    def add_onto(sef, bot_routes: RouterBuilder) -> None:
        pass

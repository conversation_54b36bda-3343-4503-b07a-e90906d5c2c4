import functools
import inspect
import json
import os
import time
from contextlib import contextmanager
from pathlib import Path
from queue import Empty, Queue
from typing import Any, Callable, Dict, Iterator, List, Optional, Set, Tuple, TypeVar, Union, cast

from lib.common.logging import get_perf_logger
from lib.common.perf.perf_categories import PerfCategory
from lib.common.tasks import MakaTask, get_current, start
from lib.common.time import maka_control_timestamp_ms

LOG = get_perf_logger(__name__)
MAX_QUEUE_SIZE = 10000


class UnitAverage:
    def __init__(self, unit_name: str) -> None:
        self._total: float = 0
        self._count: float = 0
        self._unit_name = unit_name

    def update(self, value: float) -> None:
        self._total += value
        self._count += 1

    @property
    def unit_name(self) -> str:
        return self._unit_name

    @property
    def count(self) -> float:
        return self._count

    @property
    def total(self) -> float:
        return self._total

    @property
    def value(self) -> float:
        return self._total / self._count


class TreeNode:
    def __init__(self, id: str, name: str, parent: Any) -> None:
        self.id = id
        self.name = name
        self.parent: TreeNode = parent
        self.children: List[TreeNode] = []

    def __str__(self) -> str:
        return f"TreeNode(name={self.name}, children={[str(child) for child in self.children]}"

    def __repr__(self) -> str:
        return f"TreeNode(name={self.name}, children={[str(child) for child in self.children]}"

    def print_tree(self, level: int = 0) -> None:  # Purely for debugging
        indent = "  " * level
        print(f"{indent}{self.id}")
        for child in self.children:
            child.print_tree(level + 1)


class PerfStepTracker:
    def __init__(self) -> None:
        self._data: Dict[str, Dict[str, UnitAverage]] = {}
        self._queue: Queue[Tuple[str, str, str, float]] = Queue()
        self._verbose = True
        self._task = start("PerfProcessor", self._process)
        self.context: Dict[str, TreeNode] = {}
        self.tree: Dict[str, TreeNode] = {}
        self.used: Dict[str, int] = {}
        self.data_points: Dict[str, Set[str]] = {}
        self.filename: Optional[str] = None
        self.autowrite_frequency: int = 60000
        self.tree_context: Dict[str, TreeNode] = {}
        self.pid = os.getpid()

    def _add_data_point(self, category: str, name: str, unit_name: str, metric: float) -> None:
        if category not in self._data:
            self._data[category] = {}
        if name not in self._data[category]:
            self._data[category][name] = UnitAverage(unit_name)
        assert self._data[category][name].unit_name == unit_name
        self._data[category][name].update(metric)

        # NO LOGGING HERE! This needs to be high performance

    def add_data_point(self, category: str, name: str, unit_name: str, metric: float, noop: bool = True) -> None:
        if noop:
            return
        if self._queue.qsize() >= MAX_QUEUE_SIZE:
            return
        self._queue.put((category, name, unit_name, metric))

    def get_category(self, category: str) -> Dict[str, UnitAverage]:
        return self._data[category]

    def get_total_time(self, category: str) -> float:
        if category in self.tree:
            total_time = sum(
                [
                    self._data[category][node.id].value * self._data[category][node.id].count
                    for node in self.tree[category].children
                    if node.id in self._data[category]
                ]
            )
            return total_time

        return 0

    def print_tree(
        self, node: TreeNode, category: str, total_time: float = 0, level: int = 0, global_time: float = 0
    ) -> Tuple[str, float]:
        line = ""
        indent = "\t" * level
        avg = None
        if node.name != "Root":
            avg = self._data[category][node.id]
        times = 0.0
        childrenline = ""
        for child in node.children:
            if child.id in self._data[category]:
                linadd, t = self.print_tree(
                    child, category, avg.value * avg.count if avg else total_time, level + 1, global_time
                )
                childrenline += linadd
                times += t

        if global_time == 0 or total_time == 0:
            return "", 0

        if node.name != "Root" and avg:
            line += f"{indent}({avg.value * avg.count * 100 / global_time:.2f}% Global) ({avg.value * avg.count * 100 / total_time if total_time else 100:.2f}% Local) {node.name}: {avg.value:.2f} {avg.unit_name} ({avg.count} obs) Total: {avg.value * avg.count:.2f} {avg.unit_name}\n"
            if times and total_time > 1e-5:
                line += f"{indent}\t({(avg.value * avg.count - times) * 100 / global_time:.2f}% Global) ({100 - (times * 100 / (avg.value * avg.count)):.2f}% Local) (Uncategorized): {avg.value * avg.count - times:.2f} {avg.unit_name}\n"

        line += childrenline

        if node.name == "Root" and category in self.data_points:
            line += "\n\n"
            line += "Data Points: \n"
            for name in self.data_points[category]:
                if name in self._data[category]:
                    avg = self._data[category][name]
                    line += f"\t{name}: {avg.value:.2f} {avg.unit_name} ({avg.count} obs) Total: {avg.value * avg.count:.2f} {avg.unit_name}\n"

        return line, avg.value * avg.count if avg else 0

    def write_category_to_dict(
        self,
        node: TreeNode,
        category: str,
        filename: Optional[str] = None,
        total_time: float = 0,
        global_time: float = 0,
    ) -> Union[Tuple[Dict[str, Any], float]]:
        if filename:
            total_time = self.get_total_time(category)
            global_time = total_time

        if total_time == 0 or global_time == 0:
            return {}, 0

        out: Dict[str, Any] = {}
        avg = None
        out["label"] = node.name
        if node.name != "Root":
            avg = self._data[category][node.id]
            out["global_%"] = avg.value * avg.count * 100 / global_time
            out["local_%"] = avg.value * avg.count * 100 / total_time if total_time else 100
            out["average_time"] = avg.value
            out["unit"] = avg.unit_name
            out["loops"] = avg.count
            out["total_time"] = avg.count * avg.value
        else:
            out["total_time"] = total_time
        times = 0.0
        children = []
        for child in node.children:
            if child.id in self._data[category]:
                childdict, t = self.write_category_to_dict(
                    child, category, None, avg.value * avg.count if avg else total_time, global_time
                )
                times += t
                children.append(childdict)

        out["children"] = []
        if node.name != "Root" and times and avg:
            emptychild: Dict[str, Any] = {}
            emptychild["label"] = "(Uncategorized)"
            emptychild["global_%"] = (avg.value * avg.count - times) * 100 / global_time
            emptychild["local_%"] = 100 - (times * 100 / (avg.value * avg.count))
            emptychild["average_time"] = (avg.value * avg.count - times) / avg.count
            emptychild["loops"] = avg.count
            emptychild["unit"] = avg.unit_name
            emptychild["total_time"] = avg.value * avg.count - times
            out["children"].append(emptychild)

        out["children"] += children

        if not filename and avg:
            return out, avg.value * avg.count
        if category in self.data_points:
            out["data_points"] = []
            for name in self.data_points[category]:
                if name in self._data[category]:
                    pointinfo: Dict[str, Any] = {}
                    avg = self._data[category][name]
                    pointinfo["label"] = name
                    pointinfo["average_time"] = avg.value
                    pointinfo["unit"] = avg.unit_name
                    pointinfo["loops"] = avg.count
                    pointinfo["total_time"] = avg.count * avg.value

                    out["data_points"].append(pointinfo)

        return (out, global_time)

    def get_log_line(self, filter_category: Optional[str] = None) -> str:
        line = "\n---- Perf Step Tracker ----\n\n"
        for category, _ in self._data.items():
            if (
                filter_category is not None
                and category != filter_category
                or category == "Examples"
                or category not in self.tree
            ):
                continue
            line += f"Perf Category: {category}\n\n"
            root_time = self.get_total_time(category)
            line += f"Total Time in blocks: {root_time} {next(iter(self._data[category].values())).unit_name}\n"
            line += self.print_tree(self.tree[category], category, root_time, level=0, global_time=root_time)[0]
            line += "\n"

        line += "\n"

        return line

    def _write_perf_to_json(self, filename: str) -> None:
        basename = os.path.basename(filename)
        basename = os.path.splitext(basename)[0]
        ext = ".json"
        dir = os.path.dirname(filename)
        out = {}
        for category in self._data:
            if category != "Examples":
                catdict, _ = self.write_category_to_dict(
                    self.tree[category], category, os.path.join(dir, f"{basename}_{category}_{ext}")
                )
                out[category] = catdict

        Path(dir).mkdir(exist_ok=True, parents=True)
        with open(os.path.join(dir, basename + ext), "w") as f:
            json.dump(out, f, indent=3)

        return None

    def _process(self) -> None:
        last_print: int = maka_control_timestamp_ms()
        task: MakaTask = get_current()
        while True:
            time_till_print = self.autowrite_frequency - (maka_control_timestamp_ms() - last_print)
            if time_till_print > 0:
                try:
                    category, name, unit_name, metric = self._queue.get(timeout=0)
                    self._add_data_point(category, name, unit_name, metric)
                except Empty:
                    task.tick()
                    time.sleep(1)
                continue
            start_time = maka_control_timestamp_ms()
            try:  # This should hopefully prevent crashes because of perf logging
                if self._verbose:
                    line = self.get_log_line()
                    self.add_data_point(
                        "Examples", "Perf String Build Time", "ms", maka_control_timestamp_ms() - start_time
                    )
                    LOG.info(line)

                if self.filename:
                    self._write_perf_to_json(self.filename)
                self.add_data_point("Examples", "Perf Log Time", "ms", maka_control_timestamp_ms() - start_time)
            except Exception as e:
                LOG.warning(f"Perf tracker error: {e}")
            last_print = maka_control_timestamp_ms()


# Global
_perf_tracker: Optional[PerfStepTracker] = None


def init_perf_tracker() -> None:
    global _perf_tracker
    _perf_tracker = PerfStepTracker()


def add_perf_data_point(category: str, name: str, unit_name: str, metric: float) -> None:
    if category is None:
        category = PerfCategory.TRAINING
    if _perf_tracker is None:
        return
    _perf_tracker.add_data_point(category, name, unit_name, metric)
    if category not in _perf_tracker.data_points:
        _perf_tracker.data_points[category] = set()
    _perf_tracker.data_points[category].add(name)


def get_data_category(category: str) -> Dict[str, UnitAverage]:
    if _perf_tracker is None:
        return {}
    return _perf_tracker.get_category(category)


def set_verbosity(verbosity: bool) -> None:
    if _perf_tracker is None:
        return
    _perf_tracker._verbose = verbosity


def get_perf_log_line(filter_category: Optional[str] = None) -> str:
    if _perf_tracker is None:
        return ""
    return _perf_tracker.get_log_line(filter_category)


def write_perf_to_json(filename: str) -> None:
    if _perf_tracker is None:
        return
    _perf_tracker._write_perf_to_json(filename)


def set_autowrite_filename(filename: str) -> None:
    if _perf_tracker is None:
        return

    if _perf_tracker.filename is None:
        _perf_tracker.filename = filename
    else:
        if _perf_tracker.filename != filename:
            LOG.info(
                f"Perf tracker is already writing to a file, cannot rename from {_perf_tracker.filename} to {filename}"
            )


def set_autowrite_frequency(freq_ms: int) -> None:
    if _perf_tracker is None:
        return
    _perf_tracker.autowrite_frequency = freq_ms


@contextmanager
def duration_perf_recorder(category: str, name: str, no_thread: bool = False, noop: bool = True) -> Iterator[None]:
    if noop:
        yield
        return

    global _perf_tracker
    if _perf_tracker is None:
        yield
        return

    if os.getpid() != _perf_tracker.pid:
        _perf_tracker = PerfStepTracker()

    id = name
    if category not in _perf_tracker.tree:
        _perf_tracker.tree[category] = TreeNode("Root", "Root", None)
        _perf_tracker.tree[category].children.append(TreeNode(name, name, _perf_tracker.tree[category]))
        _perf_tracker.context[category] = _perf_tracker.tree[category].children[0]
        _perf_tracker.used[name] = 1
    elif name in _perf_tracker.used:
        found = False
        for child in _perf_tracker.context[category].children:
            if child.name == name:
                _perf_tracker.context[category] = child
                id = child.id
                found = True
                break
        if not found:
            _perf_tracker.used[name] += 1
            id = f"{name}_{_perf_tracker.used[name]}"
            newchild = TreeNode(id, name, _perf_tracker.context[category])
            _perf_tracker.context[category].children.append(newchild)
            _perf_tracker.context[category] = newchild
    else:
        newchild = TreeNode(name, name, _perf_tracker.context[category])
        _perf_tracker.context[category].children.append(newchild)
        _perf_tracker.context[category] = newchild
        _perf_tracker.used[name] = 1

    start_time = maka_control_timestamp_ms()

    yield

    stop_time = maka_control_timestamp_ms()

    _perf_tracker.add_data_point(category, id, "ms", stop_time - start_time)
    _perf_tracker.context[category] = _perf_tracker.context[category].parent

    if no_thread:  # For issues within pytorch lightning's elastic laucher
        category, name, unit_name, metric = _perf_tracker._queue.get(timeout=0)
        _perf_tracker._add_data_point(category, name, unit_name, metric)

    if _perf_tracker.filename and _perf_tracker.context[category].id == "Root":
        write_perf_to_json(_perf_tracker.filename)


F = TypeVar("F", bound=Callable[..., Any])


def duration_perf_recorder_decorator(
    category: str, name: Optional[str] = None, no_thread: bool = False
) -> Callable[[F], F]:
    @functools.wraps(duration_perf_recorder)
    def wrapper(func: F) -> F:
        @functools.wraps(func)
        def wrapped(*args: Any, **kwargs: Any) -> Any:
            module = inspect.getmodule(func)
            if module:
                with duration_perf_recorder(
                    category, f"{module.__name__}.{func.__name__}" if not name else name, no_thread
                ):
                    return func(*args, **kwargs)

        return cast(F, wrapped)

    return wrapper

from time import monotonic
from typing import Callable

from lib.common.collections.moving_average import MovingAverage

OutputCallback = Callable[[float], None]


class DeltaTimer:
    def __init__(self, sample_size: int, output: OutputCallback) -> None:
        self._prev = -1.0
        self._count = 0
        self._avg = MovingAverage(sample_size)
        self._output = output

    def mark(self) -> None:
        now = monotonic()
        if self._prev > 0.0:
            self._avg.push(now - self._prev)
            self._count = (self._count + 1) % self._avg.window_size
            if self._count == 0:
                self._output(self._avg.avg())
        self._prev = now


class DeltaTimerWithOutlier:
    def __init__(
        self, sample_size: int, avg_output: OutputCallback, outlier_threshold: float, outlier_output: OutputCallback
    ) -> None:
        self._prev = -1.0
        self._count = 0
        self._avg = MovingAverage(sample_size)
        self._avg_output = avg_output
        self._threshold = outlier_threshold
        self._outlier_output = outlier_output

    def mark(self) -> None:
        now = monotonic()
        if self._prev > 0.0:
            delta = now - self._prev
            self._avg.push(delta)
            self._count = (self._count + 1) % self._avg.window_size
            if self._count == 0:
                self._avg_output(self._avg.avg())
            if delta > self._threshold:
                self._outlier_output(delta)
        self._prev = now

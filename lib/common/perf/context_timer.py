from contextlib import AbstractContextManager, contextmanager
from logging import Logger
from time import monotonic
from types import TracebackType
from typing import TYPE_CHECKING, Callable, Generator, Optional, Type

from lib.common.collections.moving_average import MovingAverage

if TYPE_CHECKING:
    ACM = AbstractContextManager[None]
else:
    ACM = AbstractContextManager


class ContextTimer(ACM):
    def __init__(self, sample_size: int, output: Callable[[float], None]) -> None:
        self._count = 0
        self._avg = MovingAverage(sample_size)
        self._output = output
        self._start_time = 0.0

    def __enter__(self) -> None:
        self._start_time = monotonic()
        return None

    def __exit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        elapsed = monotonic() - self._start_time
        self._avg.push(elapsed)
        self._count = (self._count + 1) % self._avg.window_size
        if self._count == 0:
            self._output(self._avg.avg())


@contextmanager
def oneshot_timer(output: Callable[[float], None]) -> Generator[None, None, None]:
    start = monotonic()
    yield
    output(monotonic() - start)


class IncrementalTimer:
    def __init__(self, prefix: str, threshold: float, logger: Logger) -> None:
        self._prefix = prefix
        self._threshold = threshold
        self._logger = logger
        self._increments = [("start", monotonic())]

    def mark(self, id: str) -> None:
        self._increments.append((id, monotonic()))

    def end(self) -> None:
        self._increments.append(("end", monotonic()))
        total = self._increments[-1][1] - self._increments[0][1]
        if total > self._threshold:
            msg = f"{self._prefix}: Total delta = {total*1000:.3f}ms; "
            msg += ", ".join(
                [
                    f"{self._increments[i-1][0]} -> {self._increments[i][0]} = {(self._increments[i][1] - self._increments[i-1][1]) * 100 / total:.2f}%"
                    for i in range(1, len(self._increments))
                ]
            )
            self._logger.info(msg)


@contextmanager
def incremental_timer(prefix: str, threshold: float, logger: Logger) -> Generator[IncrementalTimer, None, None]:
    timer = IncrementalTimer(prefix, threshold, logger)
    try:
        yield timer
    finally:
        timer.end()

from dataclasses import dataclass
from typing import Optional


@dataclass
class PIConfig:
    kp: float
    ki: float
    dt: Optional[float] = None
    integral_limit: Optional[float] = None


class PIController:
    def __init__(self, config: PIConfig) -> None:
        self._cfg = config
        self._integral = 0.0

    def reset(self, config: Optional[PIConfig] = None) -> None:
        if config is not None:
            self._cfg = config
        self._integral = 0.0

    def update(self, error: float, dt: Optional[float] = None) -> float:
        if dt is None:
            dt = self._cfg.dt
        assert dt is not None, "No timestep provided"
        self._integral += error * dt
        if self._cfg.integral_limit is not None:
            self._integral = max(
                min(self._integral, self._cfg.integral_limit), -self._cfg.integral_limit
            )  # Anti-windup
        output = self._cfg.kp * error + self._cfg.ki * self._integral
        return output

import time
from typing import Any, Callable, Dict, Optional, Tuple, Union, cast

from lib.common.logging import get_logger
from lib.common.serialization.json import JsonSerializable

LOG = get_logger(__name__)


FloatOrCallable = Union[float, Callable[[], float]]


def as_callback(f: FloatOrCallable) -> Callable[[], float]:
    if isinstance(f, (float, int)):

        def wrapped() -> float:
            return cast(float, f)

        return wrapped
    return f


class PIDControl(JsonSerializable):
    """
    PID Control structure
    """

    def __init__(self, Kp: FloatOrCallable, Ki: FloatOrCallable, Kd: FloatOrCallable):
        # Pid Control Values
        self._Kp: Callable[[], float] = as_callback(Kp)
        self._Ki: Callable[[], float] = as_callback(Ki)
        self._Kd: Callable[[], float] = as_callback(Kd)

        self.reset()

    def set_gain_p(self, callback: FloatOrCallable) -> None:
        self._Kp = as_callback(callback)

    def set_gain_i(self, callback: FloatOrCallable) -> None:
        self._Ki = as_callback(callback)

    def set_gain_d(self, callback: FloatOrCallable) -> None:
        self._Kd = as_callback(callback)

    def Kp(self) -> float:
        return self._Kp()

    def Ki(self) -> float:
        return self._Ki()

    def Kd(self) -> float:
        return self._Kd()

    def gain_values(self) -> Tuple[float, float, float]:
        return self.Kp(), self.Ki(), self.Kd()

    def c_values(self) -> Tuple[float, float, float]:
        return self.Cp, self.Ci, self.Cd

    def reset(self) -> None:
        """
        Reset the pid state to neutral
        """
        self.dt: float = 0
        self.last_time: Optional[float] = None
        self.derivative: float = 0
        self.integral: float = 0
        self.integral_error: float = 0
        self.last_error_i: float = 0
        self.last_error: float = 0

        # Resultant Coefficient Values (cache only)
        self.Cp: float = 0
        self.Ci: float = 0
        self.Cd: float = 0

    def output(self, error: float, derivative: Optional[float] = None) -> float:
        """
        output is the classic name for PID control sampling. Pass in the currently observed
        error and return the prediction.
        """

        # integral adds the error as if it was this value for the entire time duration
        # TODO use maka control timestamp
        now = time.time()
        dt = 0.0
        if self.last_time is not None:
            dt = now - self.last_time
        self.dt = dt
        self.last_time = now

        # next integral chunk is the average over the dt
        # next integral chunk is only meaningful if dt != 0, which also means last_error is valid
        if dt != 0:
            self.integral_error = dt * ((error + self.last_error) / 2)
            self.integral = self.integral + self.integral_error

        # derivative takes the slope over the time interval
        if derivative is not None:
            self.derivative = derivative
        elif dt == 0:
            self.derivative = 0.0
        else:
            self.derivative = (error - self.last_error) / dt

        self.last_error = error

        # Proportional + Integral + Derivative
        Kp, Ki, Kd = self.gain_values()

        self.Cp = Kp * self.last_error
        self.Ci = Ki * self.integral
        self.Cd = Kd * self.derivative

        result = Kp * error + Ki * self.integral + Kd * self.derivative
        return result

    def to_json(self) -> Dict[str, float]:
        return {
            "p": self.Kp(),
            "i": self.Ki(),
            "d": self.Kd(),
        }

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "PIDControl":
        return PIDControl(Kp=data["p"], Ki=data["i"], Kd=data["d"])

import glob
import json
import os
import shutil
import threading
from typing import Any, Callable, Dict, Optional, cast

import jsonpickle

import lib.common.logging
import lib.common.time

LOG = lib.common.logging.get_logger(__name__)


ConfigDictType = Dict[str, Any]


def compact_serialize(config: ConfigDictType) -> str:
    pickled: str = jsonpickle.encode(config)
    pickled_as_dict: Dict[str, Any] = json.loads(pickled)
    pickled_as_str = json.dumps(pickled_as_dict, separators=(",", ":"))
    return pickled_as_str


def noncompact_serialize(config: ConfigDictType) -> str:
    pickled: str = jsonpickle.encode(config)
    pickled_as_dict: Dict[str, Any] = json.loads(pickled)
    pickled_as_str = json.dumps(pickled_as_dict, separators=(",", ":"), indent=4)
    return pickled_as_str


def compact_deserialize(serialized: str) -> ConfigDictType:
    return cast(ConfigDictType, jsonpickle.decode(serialized))


class ConfigFile:
    """
    Generic JSON config file with nice API.
    """

    DEFAULT_NUM_BACKUPS = 50

    def __init__(
        self,
        config_dir: str,
        filename: str,
        backups: int = DEFAULT_NUM_BACKUPS,
        root_element: Optional[str] = None,
        serialize: Callable[[ConfigDictType], str] = compact_serialize,
        deserialize: Callable[[str], ConfigDictType] = compact_deserialize,
    ):
        self.dir = config_dir
        self.filename = filename
        self.filepath = os.path.join(self.dir, self.filename)
        self.backups = backups
        self._data: Optional[ConfigDictType] = None
        self._root_element = root_element
        self._lock = threading.Lock()

        # Can be things like:
        # - json loads/dumps
        # - yaml safe_load / dumps?
        # - jsonpickle encode/decode
        self._serialize: Callable[[ConfigDictType], str] = serialize
        self._deserialize: Callable[[str], ConfigDictType] = deserialize

    def _config_save_overwrite(self, config: ConfigDictType, compact: bool = False) -> None:
        """
        Overwrite the currently loaded config.
        Generally, this function should only be called from config_save so that previously saved config data
        is backed up before overwriting the currently saved config
        """
        if self._root_element is not None:
            config = {self._root_element: config}
        self._data = None
        config_serialized = self._serialize(config)
        with open(self.filepath, "wt") as f:
            f.write(config_serialized)

    def _config_backup(self, backup_postfix: Optional[str] = None) -> None:
        """Backup the currently saved config to the config folder"""
        if backup_postfix is None:
            return

        backup_filename = lib.common.time.timestamp_filename(self.filepath)
        backup_filename += backup_postfix

        # copy2 chosen due to https://stackoverflow.com/a/123238
        if os.path.isfile(self.filepath):
            shutil.copy2(self.filepath, backup_filename)

    def _config_delete_old_backups(self) -> None:
        """Delete the oldest backup configs"""

        backup_files = glob.glob(self.filepath + ".20*")

        i = 0
        for backup_file in reversed(sorted(backup_files)):
            # TODO enhance this to be smarter and consider keeping files from last week / yesterday rather than based
            # on number of recent files
            if i >= self.backups:
                os.remove(backup_file)
            i += 1

    def delete(self) -> None:
        """
        Deletes the current configuration file.
        """
        with self._lock:
            if os.path.exists(self.filepath):
                os.remove(self.filepath)

    def save(self, config: ConfigDictType, backup_postfix: Optional[str] = None) -> None:
        """
        Backups the currently saved config to the config folder, then saves the currently loaded config.
        """
        LOG.debug(f"Saving {self.filepath}")
        with self._lock:
            self._config_backup(backup_postfix=backup_postfix)
            self._config_save_overwrite(config)
            self._config_delete_old_backups()
        LOG.debug(f"Saved {self.filepath}")

    def load(self, default: Optional[ConfigDictType] = None, create_if_not_exists: bool = False) -> ConfigDictType:
        """
        Load the config file and json decode it.
        If the config file does not exist, create it, containing 'default' and return that.
        """
        with self._lock:
            if self._data is not None:
                return self._data
            config: ConfigDictType = default if default is not None else {}

            # create empty config on disk if it doesn't already exist
            if not os.path.exists(self.filepath) and create_if_not_exists:
                LOG.warning(f"Config file {self.filepath} does not exist. Creating empty")
                with open(self.filepath, "w+") as f:
                    f.write(self._serialize(config))

            # now read the config if we know it exists
            if os.path.exists(self.filepath):
                with open(self.filepath, "r") as f:
                    # Handle the case where the file exists but it's completely empty
                    file_contents: str = f.read()
                    if file_contents:
                        config = self._deserialize(file_contents)

            # for clarity, the root_element is often self-descriptive. e.g. "retina.json" root element is "retina".
            # this allows us to not rely on the file name for context
            # consequently, when returning data to the client, we take care of that abstraction here and give
            # them a useful dict with no root element
            if self._root_element is not None:
                if config is not None and self._root_element in config:
                    config = config[self._root_element]
                else:
                    LOG.warning(
                        f"Config file {self.filepath} does not have root element {self._root_element}, using default."
                    )
                    config = default or {}
            self._data = config
            return config

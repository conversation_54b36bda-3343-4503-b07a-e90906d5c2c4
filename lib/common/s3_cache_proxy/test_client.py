import unittest
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, mock_open, patch

from lib.common.s3_cache_proxy.client import S3CacheProxyClient


class TestS3CacheProxyClient(unittest.TestCase):
    def setUp(self) -> None:
        self.client = S3CacheProxyClient(s3_cache_proxy_host="example.com")

    def test_initialization(self) -> None:
        self.assertEqual(self.client._s3_cache_proxy_host, "example.com")
        self.assertEqual(self.client._timeout, 30)

    def test_make_url(self) -> None:
        bucket: str = "my-bucket"
        key: str = "my-key"
        expected_url: str = "http://example.com/my-bucket/my-key"
        self.assertEqual(self.client._make_url(bucket, key), expected_url)

    @patch("requests.Session")
    def test_get_success(self, mock_session_class: Mock) -> None:
        # Mock the session and its get method
        mock_session = MagicMock()
        mock_session_class.return_value.__enter__.return_value = mock_session
        mock_response = MagicMock()
        mock_response.ok = True
        mock_response.content = b"mocked data"
        mock_session.get.return_value = mock_response

        # Call the method
        data: bytes = self.client.get("my-bucket", "my-key")

        # Assert the URL was called correctly
        mock_session.get.assert_called_once_with("http://example.com/my-bucket/my-key", timeout=30)
        # Assert the response data is returned
        self.assertEqual(data, b"mocked data")

    @patch("os.path.exists")
    @patch("os.makedirs")
    @patch("builtins.open", new_callable=mock_open)
    @patch.object(S3CacheProxyClient, "get", return_value=b"mocked file content")
    def test_download_success(
        self, mock_get: Mock, mock_open_file: Mock, mock_makedirs: Mock, mock_exists: Mock
    ) -> None:
        mock_exists.return_value = False  # Simulate that the file does not exist

        # Call the method
        self.client.download("my-bucket", "my-key", "/path/to/file.txt", make_dirs=True)

        # Ensure directories are created
        mock_makedirs.assert_called_once_with("/path/to")
        # Ensure file is opened and written to
        mock_open_file.assert_called_once_with("/path/to/file.txt", "wb")
        mock_open_file().write.assert_called_once_with(b"mocked file content")

    @patch("os.path.exists")
    @patch("os.makedirs")
    @patch("builtins.open", new_callable=mock_open)
    @patch.object(S3CacheProxyClient, "get", return_value=b"mocked file content")
    def test_download_file_exists(
        self, mock_get: Mock, mock_open_file: Mock, mock_makedirs: Mock, mock_exists: Mock
    ) -> None:
        # Simulate that the file already exists
        mock_exists.side_effect = [True]

        # Test for the FileExistsError if exist_ok is False
        with self.assertRaises(FileExistsError):
            self.client.download("my-bucket", "my-key", "/path/to/existing_file.txt", exist_ok=False)

        # Ensure file is not opened or directories created
        mock_open_file.assert_not_called()
        mock_makedirs.assert_not_called()

    @patch("os.path.exists")
    @patch("os.makedirs")
    @patch("builtins.open", new_callable=mock_open)
    @patch.object(S3CacheProxyClient, "get", return_value=b"mocked file content")
    def test_download_make_dirs_false_raises_error(
        self, mock_get: Mock, mock_open_file: Mock, mock_makedirs: Mock, mock_exists: Mock
    ) -> None:
        # Simulate that the directory does not exist
        mock_exists.side_effect = [False, False]

        # Test for the RuntimeError if make_dirs is False and directory doesn't exist
        with self.assertRaises(RuntimeError):
            self.client.download("my-bucket", "my-key", "/path/to/file.txt", make_dirs=False)

        # Ensure file is not opened and directories are not created
        mock_open_file.assert_not_called()
        mock_makedirs.assert_not_called()


if __name__ == "__main__":
    unittest.main()

from typing import Callable, Dict, List, Protocol, Union

from config.client.cpp.config_client_python import ConfigTree


class TreeNode(Protocol):
    def get_name(self) -> str:
        return ""

    def get_int_value(self) -> int:
        return 0

    def get_uint_value(self) -> int:
        return 0

    def get_float_value(self) -> float:
        return 0.0

    def get_bool_value(self) -> bool:
        return False

    def get_string_value(self) -> str:
        return ""

    def register_callback(self, callback: Callable[[], None]) -> int:
        return 0

    def get_node(self, key: str) -> "TreeNode":
        raise NotImplementedError()
        return TreeNode()

    def get_children_nodes(
        self,
    ) -> Union[
        List["TreeNode"], List[ConfigTree]
    ]:  # Due to typing issues in python need to explicitly call out List of types
        return []


class AtomicFlagLeafNode(Protocol):
    def reload_required(self) -> bool:
        return False

    def tree(self) -> TreeNode:
        ...


# --------------------------------------------------------------------------------
class MockNode(TreeNode):
    def __init__(self, name: str) -> None:
        self._name = name

    def get_name(self) -> str:
        return self._name


class MockIntLeafNode(MockNode):
    def __init__(self, name: str, val: int) -> None:
        super().__init__(name)
        self._val = val

    def get_int_value(self) -> int:
        return self._val


class MockUintLeafNode(MockNode):
    def __init__(self, name: str, val: int) -> None:
        super().__init__(name)
        self._val = val

    def get_uint_value(self) -> int:
        return self._val


class MockFloatLeafNode(MockNode):
    def __init__(self, name: str, val: float) -> None:
        super().__init__(name)
        self._val = val

    def get_float_value(self) -> float:
        return self._val


class MockBoolLeafNode(MockNode):
    def __init__(self, name: str, val: bool) -> None:
        super().__init__(name)
        self._val = val

    def get_bool_value(self) -> bool:
        return self._val


class MockStringLeafNode(MockNode):
    def __init__(self, name: str, val: str) -> None:
        super().__init__(name)
        self._val = val

    def get_string_value(self) -> str:
        return self._val


class MockTreeNode(MockNode):
    def __init__(self, name: str) -> None:
        super().__init__(name)
        self._data: Dict[str, TreeNode] = {}

    def add(self, key: str, val: TreeNode) -> None:
        ks = key.rstrip("/").split("/", 1)
        if len(ks) > 1:
            if ks[0] in self._data:
                child = self._data[ks[0]]
            else:
                child = MockTreeNode(ks[0])
                self._data[ks[0]] = child
            assert isinstance(child, MockTreeNode)
            child.add(ks[1], val)
        else:
            self._data[key] = val

    def get_node(self, key: str) -> TreeNode:
        ks = key.rstrip("/").split("/", 1)
        assert ks[0] in self._data, f"{ks[0]} is not a child of {self.get_name()}"
        if len(ks) > 1:
            return self._data[ks[0]].get_node(ks[1])
        return self._data[ks[0]]

    def get_children_nodes(self) -> List["TreeNode"]:
        return list(self._data.values())


# --------------------------------------------------------------------------------
class MockAtomicFlagLeafNode(AtomicFlagLeafNode):
    def __init__(self, node: TreeNode) -> None:
        self._node = node

    def tree(self) -> TreeNode:
        return self._node

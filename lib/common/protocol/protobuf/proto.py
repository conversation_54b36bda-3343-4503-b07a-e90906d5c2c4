import json
from abc import ABC, abstractmethod
from typing import Any, Callable, Dict, Generic, List, Type, TypeVar

from google.protobuf.descriptor import Descriptor
from google.protobuf.reflection import GeneratedProtocolMessageType

from lib.common.protocol.protobuf.json_format import proto_to_dict
from lib.common.serialization.binary import BinarySerializable
from lib.common.serialization.json import JsonSerializable

# The generated proto message being composed
M = TypeVar("M", bound=GeneratedProtocolMessageType)

# this will be used to type factory functions that return
# he same instance as the class providing the factory func
#
# re ignore: I'm not sure how to mypy this better than
#            static subclass factory methods are tough and need more thought to mypy well
#
# Following pattern: https://mypy.readthedocs.io/en/stable/generics.html#generic-methods-and-generic-self
ProtobufSerializationMixinSubclassType = TypeVar("ProtobufSerializationMixinSubclassType", bound="ProtobufSerializationMixin")  # type: ignore


class ProtobufSerializationMixin(Generic[M], BinarySerializable, JsonSerializable, ABC):
    """
    Base class providing serialization / deserialization and other related utilities.

    The general idea is to use this class to COMPOSE functionality.
    This class should be limited to what can be inferred from the proto.

    Anything that can't be inferred from the proto (e.g. required fields, complex validation)
    must be implemented in a higher level class that composes this Proto implementation only for
    serialization / deserialization

    The general pattern is as follows:
      1) Define a codegen template file: foo.yaml
      2) Run codegen

    Readers / Clients: You must understand https://mypy.readthedocs.io/en/stable/generics.html#generic-methods-and-generic-self to understand the mypy typing
    """

    #
    # ABSTRACT METHODS - only a single method for clients!
    #

    @classmethod
    @abstractmethod
    def proto_type(cls: Type[ProtobufSerializationMixinSubclassType]) -> GeneratedProtocolMessageType:
        """
        Return the generated Protobuf type that is being managed
        """
        pass

    #
    # IMPLEMENTED METHODS
    #

    #
    # object methods
    #

    def __eq__(self, obj: Any) -> bool:
        """
        Return false if any proto field differs
        """
        if not isinstance(obj, type(self)):
            return False

        for f in self.proto_fields():
            if getattr(self, f) != getattr(obj, f):
                return False

        return True

    def __str__(self) -> str:
        """Serialize to string"""
        return json.dumps(self.to_dict())

    #
    # 0) Protobuf helper methods
    #

    @classmethod
    def proto_fields(cls: Type[ProtobufSerializationMixinSubclassType]) -> List[str]:
        # mypy doesn't realize that there will be a "DESCRIPTOR" field that we can read
        # we know this is a Descriptor by looking at the generated proto
        descriptor: Descriptor = cls.proto_type().DESCRIPTOR  # type: ignore

        return [f.name for f in descriptor.fields]

    #
    # Protobuf to/from
    #

    def to_proto(self) -> M:
        """
        Convert to a proto object

        Not cached.
        """

        # we get back a GeneratedProtocolMessageType but it basically provides constructor behavior
        # (this is my best typing effort thus far)
        protobuf_object_constructor: Callable[..., M] = self.proto_type()
        return protobuf_object_constructor(**self.to_dict())

    @classmethod
    def from_proto(
        cls: Type[ProtobufSerializationMixinSubclassType], proto: M
    ) -> ProtobufSerializationMixinSubclassType:
        """
        Factory function to create a new instance from the given proto object
        """
        return cls.from_dict(params=proto_to_dict(proto))

    #
    # bytes to/from
    #

    def to_bytes(self) -> bytes:
        """
        Convert to a proto object in binary

        Not cached.
        """
        return self.to_proto().SerializeToString()  # type: ignore

    @classmethod
    def from_bytes(
        cls: Type[ProtobufSerializationMixinSubclassType], data: bytes
    ) -> ProtobufSerializationMixinSubclassType:
        """
        Factory function to create a new instance from the given proto binary
        """
        # allocate object (all fields will be default value / missing)
        result_as_proto: M = cls.proto_type()()
        # load data into the object
        result_as_proto.ParseFromString(data)  # type: ignore
        # convert from protobuf --> this class' subclass
        return cls.from_proto(result_as_proto)

    #
    # Dict to/from
    #

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to Dict by mapping proto field names to attrs

        Not cached.
        """
        return {f: getattr(self, f) for f in self.proto_fields()}

    @classmethod
    def from_dict(
        cls: Type[ProtobufSerializationMixinSubclassType], params: Dict[str, Any]
    ) -> ProtobufSerializationMixinSubclassType:
        """
        Factory function to create a new instance from the given dict
        """
        # map field names -> params lookup
        args: Dict[str, Any] = {k: params.get(k) for k in cls.proto_fields()}

        # mypy doesn't realize that subclasses will have named arguments
        return cls(**args)

    #
    # JSON to/from
    #

    def to_json(self) -> Dict[str, Any]:
        """
        Convert to valid JSON

        Not cached.
        """
        return self.to_dict()

    @classmethod
    def from_json(
        cls: Type[ProtobufSerializationMixinSubclassType], data: Dict[str, Any]
    ) -> ProtobufSerializationMixinSubclassType:
        """
        Factory function to create a new instance from the given dict
        """
        # WEIRD BEHAVIOR WARNING:
        #
        # An earlier version of this code had below line as
        # > ParseDict(json, message=cls.proto_descriptor())
        #
        # However, when doing that, calling to_json() -> from_json() -> to_json() -> from_json() would
        # fail the second time around with this error:
        #
        # > E       ValueError: Protocol message ManualDriveRequestProto has no "forward" field.
        #
        # I do not understand why.
        #################################
        #
        # Follow-up: Working on tackling this in https://github.com/carbonrobotics/robot/pull/1337
        #
        proto: M = cls.proto_type()(**data)

        return cls.from_proto(proto)

    #
    # str to/from
    #

    # this method provided only for symmetry
    def to_str(self) -> str:
        """
        Convert to str

        Not cached.
        """
        return str(self)

    @classmethod
    def from_str(cls: Type[ProtobufSerializationMixinSubclassType], s: str) -> ProtobufSerializationMixinSubclassType:
        return cls.from_json(json.loads(s))

from typing import Any, Dict, List

from google.protobuf.descriptor import FieldDescriptor
from google.protobuf.reflection import GeneratedProtocolMessageType


#
# json_format.MessageToDict throws:
# robot    | TypeError: descriptor 'ListFields' of 'google.proto.pyext._message.CMessage' object needs an argument
#
# so write our own via https://stackoverflow.com/a/57359749
#
def proto_to_dict(message: GeneratedProtocolMessageType) -> Dict[str, Any]:
    message_dict: Dict[str, Any] = {}

    # mypy: no way? to tell mypy about generated DESCRIPTOR field
    # but we do know what we get is a Descriptor here and it will be checked later below
    fields: List[FieldDescriptor] = message.DESCRIPTOR.fields  # type: ignore
    for descriptor in fields:
        key = descriptor.name
        value = getattr(message, descriptor.name)

        if descriptor.label == descriptor.LABEL_REPEATED:
            message_list = []

            for subMessage in value:
                if descriptor.type == descriptor.TYPE_MESSAGE:
                    # recursive
                    message_list.append(proto_to_dict(subMessage))
                else:
                    message_list.append(subMessage)

            message_dict[key] = message_list
        else:
            if descriptor.type == descriptor.TYPE_MESSAGE:
                # recursive
                message_dict[key] = proto_to_dict(value)
            else:
                message_dict[key] = value

    return message_dict

from typing import Any, Awaitable, Callable, Dict, Optional, Sequence, Type, TypeVar, Union

from lib.common.protocol.channel.async_channel import AsyncCallbackSubscriberChannel, AsyncSubscriberChannel
from lib.common.protocol.channel.base import SubscriberChannel, Topic
from lib.common.protocol.channel.callback import CallbackSubscriberChannel
from lib.common.protocol.feed.base import Feed
from lib.common.protocol.feed.error import (
    TopicAlreadyPublishedException,
    TopicNotYetPublishedException,
    WrongChannelException,
)

T = TypeVar("T")


class InprocFeed(Feed):
    """
    The one and only feed as of today.
    """

    def __init__(self) -> None:
        self._subscriptions: Dict[Topic, Union[AsyncSubscriberChannel[Any], SubscriberChannel[Any]]] = {}

    def is_async(self, topic: Topic) -> bool:
        if topic not in self._subscriptions:
            raise TopicNotYetPublishedException(f"Unexpected topic: {topic}")

        return isinstance(self._subscriptions[topic], AsyncSubscriberChannel)

    @property
    def topics(self) -> Sequence[Topic]:
        return sorted([t for t in self._subscriptions.keys()])

    def publish(self, topic: Topic, subscription: Union[AsyncSubscriberChannel[Any], SubscriberChannel[Any]]) -> None:
        if topic in self._subscriptions:
            raise TopicAlreadyPublishedException(f"Topic already published: {topic}")
        self._subscriptions[topic] = subscription

    def publish_callback(self, topic: Topic, callback: Callable[[], Optional[T]]) -> CallbackSubscriberChannel[T]:
        channel: CallbackSubscriberChannel[T] = CallbackSubscriberChannel(topic=topic, callback=callback)
        self.publish(topic=topic, subscription=channel)
        return channel

    def publish_async_callback(
        self, topic: Topic, callback: Callable[[], Awaitable[Optional[T]]]
    ) -> AsyncCallbackSubscriberChannel[T]:
        channel: AsyncCallbackSubscriberChannel[T] = AsyncCallbackSubscriberChannel(topic=topic, callback=callback)
        self.publish(topic=topic, subscription=channel)
        return channel

    def _subscribe(self, topic: Topic, msg_type: Type[T]) -> Union[AsyncSubscriberChannel[T], SubscriberChannel[T]]:
        # TODO if a topic is not registered we could return a hot-swappable SubscriberChannel
        # that just emits None until something is attached and published on the back end then it delegates through
        if topic not in self._subscriptions:
            raise TopicNotYetPublishedException(f"Unregistered topic: {topic}")

        subscription = self._subscriptions[topic]
        # TODO assert msg_type matches (currently that information is only known to mypy

        return subscription

    def subscribe(self, topic: Topic, msg_type: Type[T]) -> SubscriberChannel[T]:
        result: Union[AsyncSubscriberChannel[T], SubscriberChannel[T]] = self._subscribe(topic=topic, msg_type=msg_type)
        if not isinstance(result, SubscriberChannel):
            raise WrongChannelException(f"Expected synchronous channel for: {topic} but found async channel")
        return result

    def subscribe_async(self, topic: Topic, msg_type: Type[T]) -> AsyncSubscriberChannel[T]:
        result: Union[AsyncSubscriberChannel[T], SubscriberChannel[T]] = self._subscribe(topic=topic, msg_type=msg_type)
        if not isinstance(result, AsyncSubscriberChannel):
            raise WrongChannelException(f"Expected async channel for: {topic} but found synchronous channel")
        return result

from abc import ABC, abstractmethod
from typing import Awaitable, Callable, Optional, Sequence, Type, TypeVar, Union

from lib.common.protocol.channel.async_channel import AsyncCallbackSubscriberChannel, AsyncSubscriberChannel
from lib.common.protocol.channel.base import Subscriber<PERSON>hannel, Topic
from lib.common.protocol.channel.callback import CallbackSubscriberChannel
from lib.common.protocol.feed.error import TopicNotYetPublishedException

T = TypeVar("T")


class Feed(ABC):
    """
    A Feed is the fundamental communication abstraction for accessing Channels.
    """

    @property
    @abstractmethod
    def topics(self) -> Sequence[Topic]:
        """
        Returns the list of topics in the feed.
        """
        pass

    @abstractmethod
    def is_async(self, topic: Topic) -> bool:
        pass

    @abstractmethod
    def publish(self, topic: Topic, subscription: Union[AsyncSubscriberChannel[T], SubscriberChannel[T]]) -> None:
        """
        Publish for a new topic.
        """
        pass

    @abstractmethod
    def publish_callback(self, topic: Topic, callback: Callable[[], Optional[T]]) -> CallbackSubscriberChannel[T]:
        """
        Factory function for creating a new callback subscription for a given topic.

        This should throw an error if the topic is already registered.
        """
        pass

    @abstractmethod
    def publish_async_callback(
        self, topic: Topic, callback: Callable[[], Awaitable[Optional[T]]]
    ) -> AsyncCallbackSubscriberChannel[T]:
        """
        Factory function for creating a new async callback subscription for a given topic.

        This should throw an error if the topic is already registered.
        """
        pass

    @abstractmethod
    def subscribe(self, topic: Topic, msg_type: Type[T]) -> SubscriberChannel[T]:
        """
        Get a new subscriber channel.

        This will not necessarily be the same instance that was published.
        """
        pass

    @abstractmethod
    def subscribe_async(self, topic: Topic, msg_type: Type[T]) -> AsyncSubscriberChannel[T]:
        """
        Get a new subscriber channel.

        This will not necessarily be the same instance that was published.
        """
        pass

    def subscribe_either(
        self, topic: Topic, msg_type: Type[T]
    ) -> Union[AsyncSubscriberChannel[T], SubscriberChannel[T]]:
        return (
            self.subscribe(topic=topic, msg_type=msg_type)
            if not self.is_async(topic=topic)
            else self.subscribe_async(topic=topic, msg_type=msg_type)
        )

    def subscribe_async_if_published(self, topic: Topic, msg_type: Type[T]) -> Optional[AsyncSubscriberChannel[T]]:
        """
        Get a new subscriber channel.

        This will not necessarily be the same instance that was published.
        """
        try:
            return self.subscribe_async(topic=topic, msg_type=msg_type)
        except TopicNotYetPublishedException:
            return None

    def subscribe_if_published(self, topic: Topic, msg_type: Type[T]) -> Optional[SubscriberChannel[T]]:
        """
        Get a new subscriber channel.

        This will not necessarily be the same instance that was published.
        """
        try:
            return self.subscribe(topic=topic, msg_type=msg_type)
        except TopicNotYetPublishedException:
            return None

# This file is called async_channel because async is a python reserved word and flake8 complains on import
from abc import ABC, abstractmethod
from typing import Awaitable, Callable, Generic, Optional, TypeVar

from lib.common.protocol.channel.base import PubSubChannel, Topic

COVARIANT_M = TypeVar("COVARIANT_M", covariant=True)
M = TypeVar("M")


class AsyncSubscriberChannel(PubSubChannel, Generic[COVARIANT_M], ABC):
    @abstractmethod
    async def read(self) -> Optional[COVARIANT_M]:
        pass


class AsyncCallbackSubscriberChannel(AsyncSubscriberChannel[M], Generic[M]):
    def __init__(self, topic: Topic, callback: Callable[[], Awaitable[Optional[COVARIANT_M]]]):
        super().__init__(topic=topic)
        self._callback = callback

    async def read(self) -> Optional[COVARIANT_M]:
        return await self._callback()

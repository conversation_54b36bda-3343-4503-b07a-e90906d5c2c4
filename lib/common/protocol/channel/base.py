from abc import ABC, abstractmethod
from typing import Generic, Optional, TypeVar

from lib.common.namespace.base import Path

# Refer README for design


class Topic(Path):
    """
    A topic for a channel.
    """

    def __new__(cls, topic: str) -> "Topic":
        assert len(topic) > 0, "Topic cannot be empty"
        assert topic.startswith(Topic.separator()), f"Invalid topic: {topic}"
        assert (Topic.separator() + Topic.separator()) not in topic, f"Invalid topic: {topic}"
        instance: Topic = super().__new__(cls, topic)
        instance._depth = instance.count(Topic.separator()) if instance != Topic.separator() else 0
        return instance

    @staticmethod
    def root() -> "Topic":
        return Topic(Topic.separator())

    @property
    def depth(self) -> int:
        return self._depth  # type: ignore

    # TODO restrict to SubTopic type once enums are plummed
    def sub(self, subtopic: str) -> "Topic":
        if not subtopic.startswith(Topic.separator()):
            subtopic = Topic.separator() + subtopic
        if self == "/":
            subtopic = subtopic[1:]
        return Topic(self + subtopic)


class Channel(ABC):
    """
    A Channel is the fundamental communication abstraction for exchanging data between components.

    A channel may be asynchronous or synchronous.

    The concept of a channel is independent of process, threads, memory.

    All inter-node, inter-process, and inter-thread communication should be modeled as a Channel.
    """

    pass


class AsynchronousChannel(Channel, ABC):
    """
    Examples of Asynchronous channels:
    - Message Queue
    - Publish / Subscribe (message queue with multi subscriber)
    """

    pass


class PubSubChannel(AsynchronousChannel, ABC):
    """
    A publish-subscribe implementation of an asynchronous channel.

    Publish-subscribe pattern (https://en.wikipedia.org/wiki/Publish%E2%80%93subscribe_pattern):
        In software architecture, publish–subscribe is a messaging pattern
        where senders of messages, called publishers, do not program the
        messages to be sent directly to specific receivers, called
        subscribers, but instead categorize published messages into classes
        without knowledge of which subscribers, if any, there may be.
        Similarly, subscribers express interest in one or more classes and only
        receive messages that are of interest, without knowledge of which
        publishers, if any, there are.


    """

    def __init__(self, topic: Topic):
        self._topic: Topic = topic

    @property
    def topic(self) -> Topic:
        return self._topic

    @staticmethod
    def separator() -> str:
        return "/"


M = TypeVar("M")


class PublisherChannel(PubSubChannel, Generic[M]):
    """
    The publisher side of a publish-subscribe channel.
    """

    @abstractmethod
    def send(self, message: M, topic_suffix: str) -> None:
        pass


COVARIANT_M = TypeVar("COVARIANT_M", covariant=True)


class SubscriberChannel(PubSubChannel, Generic[COVARIANT_M]):
    """
    The subscriber side of a publish-subscribe channel.
    """

    @abstractmethod
    def read(self) -> Optional[COVARIANT_M]:
        pass

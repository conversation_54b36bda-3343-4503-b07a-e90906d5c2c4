import os
import shutil
from typing import Callable, Generic, Optional

from lib.common.logging import get_logger
from lib.common.protocol.channel.base import M, PublisherChannel, SubscriberChannel, Topic

LOG = get_logger(__name__)

DEV_SHM = "/dev/shm"

LATEST_MESSAGE_FILENAME_NO_EXTENSION = "latest"


class ShmemInbox(str):
    """
    A location where messages are stored in shared memory.

    Literally just a directory with typing
    """


def validate_shmem_topic(topic: str) -> None:
    assert topic.startswith("/"), "Shmem channel topic should start with /"


def validate_file_suffix(file_suffix: str) -> None:
    supported = [".pb", ".json"]
    assert file_suffix in supported, f"Supported file types: {supported}. Got: {file_suffix}"


# each topic/topic_suffix gets store in it's own inbox
def make_shmem_inbox(topic: str, topic_suffix: str) -> ShmemInbox:
    output_dir = os.path.join(DEV_SHM, topic[1:])
    if topic_suffix != "":
        # Make this more robust after we see how its used - for now just fail
        assert not topic_suffix.startswith(
            "/"
        ), "Corner case! We don't support subtopics with leading slash. Try removing it."
        output_dir = os.path.join(output_dir, topic_suffix)
    os.makedirs(output_dir, exist_ok=True)
    return ShmemInbox(output_dir)


class ShmemPublisherChannel(PublisherChannel[M], Generic[M]):
    """
    A Shared Memory implementation of a PublisherChannel.
    """

    def __init__(self, topic: Topic, serialize: Callable[[M], bytes], file_suffix: str):
        super().__init__(topic=topic)
        validate_shmem_topic(topic)
        validate_file_suffix(file_suffix)
        self._serialize = serialize

        # the other side of a PublisherChannel is uses a read method.
        # So let's name our message as the latest one
        self._filename = f"{LATEST_MESSAGE_FILENAME_NO_EXTENSION}{file_suffix}"

        self._base_inbox: ShmemInbox = make_shmem_inbox(topic=topic, topic_suffix="")

    def send(self, message: M, topic_suffix: str = "") -> None:
        shmem_inbox = self._base_inbox
        if topic_suffix != "":
            shmem_inbox = make_shmem_inbox(topic=self.topic, topic_suffix=topic_suffix)

        # The other side of
        tmpfile = os.path.join(shmem_inbox, f"{self._filename}.tmp")
        with open(tmpfile, "wb") as f:
            f.write(self._serialize(message))

        filepath = os.path.join(shmem_inbox, f"{self._filename}")
        shutil.move(tmpfile, filepath)


class ShmemSubscriberChannel(SubscriberChannel[M], Generic[M]):
    """
    A Shared Memory implementation of a SubscriberChannel.
    """

    def __init__(self, topic: Topic, deserialize: Callable[[bytes], M], file_suffix: str):
        super().__init__(topic=topic)
        validate_shmem_topic(topic)
        validate_file_suffix(file_suffix)
        self._deserialize: Callable[[bytes], M] = deserialize

        # the other side of a PublisherChannel is uses a read method.
        # So let's name our message as the latest one
        self._filename = f"{LATEST_MESSAGE_FILENAME_NO_EXTENSION}{file_suffix}"

        self._base_inbox: ShmemInbox = make_shmem_inbox(topic=topic, topic_suffix="")
        self._latest_message_filepath = os.path.join(self._base_inbox, self._filename)

        self._latest: Optional[M] = None

    def read(self) -> Optional[M]:
        # The other side of
        try:
            with open(self._latest_message_filepath, "rb") as f:
                contents = f.read()
                if contents is not None:
                    new = self._deserialize(contents)
                    if new is not None:
                        self._latest = new
                else:
                    LOG.warning("Race condition due to unlocked shared memory access!")
        except FileNotFoundError:
            LOG.warning(f"No message found at {self._latest_message_filepath}")
        return self._latest

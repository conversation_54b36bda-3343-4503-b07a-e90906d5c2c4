from typing import Generic

from lib.common.logging import get_logger
from lib.common.protocol.channel.base import COVARIANT_M, SubscriberChannel

LOG = get_logger(__name__)


class Cache(SubscriberChannel[COVARIANT_M], Generic[COVARIANT_M]):
    def __init__(self, delegate: SubscriberChannel[COVARIANT_M], initial_value: COVARIANT_M):
        super().__init__(topic=delegate.topic)
        self._delegate: SubscriberChannel[COVARIANT_M] = delegate
        self._latest: COVARIANT_M = initial_value

    def read(self) -> COVARIANT_M:
        latest = self._delegate.read()
        if latest is not None:
            self._latest = latest
        return self._latest

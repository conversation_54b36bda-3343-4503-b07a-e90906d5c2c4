# Channel
A **Channel** (or **Protocol Channel**) is a communication abstraction between two components.

## Properties
* A channel is either **synchronous** or **asynchronous**

### Asynchronous Channels
Asynchronous Channels communicate via **Messages**.

Structurally, this can take the form of:
 * message queue
   * in-process buffers
   * inter-process message queue
 * publish-subscribe pattern

Some possible implementations can look like:
 * callbacks
 * python awaitable buffer stream
 * shared memory with signaling
 * ZMQ pub-sub messaging
   * inproc
   * TCP
   * UDP
 
### Synchronous Channel
Synchronous Channels communicate via **Requests** and **Responses**.

Structurally, this can take the form of:
 * method call
 * client/server request/response architecture
 
Some possible implementations can look like:
 * python method call
 * REST
 * gRPC
 * USB m_proto (2020-Q3 firmware)

# Fabric
A _set of channels_ is a **Fabric**. This exists only as an idea today.

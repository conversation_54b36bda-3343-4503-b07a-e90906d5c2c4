from typing import Any, Callable, Generic, Optional, TypeVar

from lib.common.protocol.channel.base import PublisherChannel, SubscriberChannel, Topic

M = TypeVar("M", bound=Any)


class CallbackPublisherChannel(PublisherChannel[M], Generic[M]):
    """
    A callback implementation of a PublisherChannel.
    """

    def __init__(self, topic: Topic, callback: Callable[[M], None]):
        super().__init__(topic=topic)
        self._callback = callback

    def send(self, message: M, topic_suffix: str = "") -> None:
        return self._callback(message)


class CallbackSubscriberChannel(SubscriberChannel[M], Generic[M]):
    """
    A callback implementation of a SubscriberChannel.
    """

    def __init__(self, topic: Topic, callback: Callable[[], Optional[M]]):
        super().__init__(topic=topic)
        self._callback = callback

    def read(self) -> Optional[M]:
        return self._callback()

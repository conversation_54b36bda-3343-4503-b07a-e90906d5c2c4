from typing import List, Optional

from attr import dataclass

from lib.common.protocol.codegen.type_definition import TypeDefinition
from lib.common.protocol.codegen.validation_definition import Validation


@dataclass(frozen=True)
class FieldDefinition:
    """
    A field of a generated classfile.

    Not coupled to any particular language.
    """

    name: str
    type_definition: TypeDefinition
    required: bool = True
    default: Optional[str] = None
    validations: Optional[List[Validation]] = None

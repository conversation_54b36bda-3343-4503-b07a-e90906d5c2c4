# codegen
`codegen` is a declarative code generation tool designed to solve the following problems:

# Problem Statement
We desire for the following features:
  * Multi-language support
  * Multi-host communication
  * Serialize / deserialize in multiple formats
      * Binary
      * JSON

In solving this problem, we want the following:
  * Write as little code as possible
  * Types should behave and act the same across our programming environments

This allows us to build complex logic over a set of primitive objects defined by our domain model.

# Vision
Declarative, generated protocol objects which can be
 * serialized/deserialized across a myriad of languages (C, C++, Python, TypeScript, JavaScript, ..., golang, rust),
 * and hosts (browsers, Cloud, Robot OS, MCUs),
 * with complex validation

from typing import List

from lib.common.protocol.codegen.compile.common.file_generator import BaseFileGenerator
from lib.common.protocol.codegen.compile.common.writer import BaseTextWriter
from lib.common.protocol.codegen.compile.proto.writer import GeneratedProtoTextWriter
from lib.common.protocol.codegen.definition import Definition
from lib.common.terminal import EscapeColor


class GoogleProtoFileGenerator(BaseFileGenerator):
    """
    Write .proto files
    """

    def __init__(self, definitions: List[Definition], filename_no_extension: str):
        super().__init__()
        self._writer = GeneratedProtoTextWriter(definitions=definitions, filename_no_extension=filename_no_extension)

    @classmethod
    def colorize_color(cls) -> str:
        return EscapeColor.MAGENTA

    @property
    def writer(self) -> BaseTextWriter:
        return self._writer

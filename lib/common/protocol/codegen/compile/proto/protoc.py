import os
import subprocess
from pathlib import Path
from typing import List

from lib.common.terminal import EscapeColor


def _log_sysout_command(command_args: List[str]) -> None:
    print_align = len("--grpc_python_out")

    # nicely print what we are going to run]=
    multi_line_print_text: str = " ".join(command_args[:3]) + "\\\n    "
    command_args = command_args[3:]
    for i, s in enumerate(command_args):
        # alignment of prefix block
        if s.startswith("--"):
            s = s.ljust(print_align)
        multi_line_print_text += f"{s} "
        # newline
        if i > 0 and command_args[i - 1].startswith("--") and i != len(command_args) - 1:
            multi_line_print_text += "\\\n    "

    print(EscapeColor.apply(EscapeColor.WHITE, f"> {multi_line_print_text}"))


def build_protoc_args(input_files: List[str], output_dir: Path, python_out: bool) -> List[str]:
    out_dir: str = str(output_dir)
    # args to output python files
    python_out_args = []
    if python_out:
        python_out_args += [
            "--python_out",
            out_dir,
            "--mypy_out",
            out_dir,
            "--grpc_python_out",
            out_dir,
        ]

    # TODO args to output JS, C, C++, etc. files

    # proto_path args
    proto_path_args = ["--proto_path", out_dir] + [os.path.join(output_dir, f) for f in input_files]
    protoc_args: List[str] = python_out_args + proto_path_args
    return ["python", "-m", "grpc_tools.protoc"] + protoc_args


def run_protoc(*, input_files: List[str], output_dir: Path, python_out: bool) -> None:
    """
    Runs the Google protoc compiler via `python -m grpc_tools.protoc`
    """
    command_args: List[str] = build_protoc_args(input_files=input_files, output_dir=output_dir, python_out=python_out)

    # _log_sysout_command(command_args)
    command: str = f"{' '.join([s.strip() for s in command_args])}"
    subprocess.run(command, shell=True, check=True)

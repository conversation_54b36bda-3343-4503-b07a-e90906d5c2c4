from typing import List

from lib.common.protocol.codegen.compile.common.writer import BaseTextWriter
from lib.common.protocol.codegen.compile.proto.compose_mapping import COMPOSE_MAPPING, GoogleProtoDefinition
from lib.common.protocol.codegen.compile.proto.type_mapping import TYPE_MAPPING
from lib.common.protocol.codegen.definition import Definition

INDENT = "    "


class GeneratedProtoTextWriter(BaseTextWriter):
    """
    Writer class to write a valid google .proto file based on a provided definition.
    """

    def __init__(
        self, definitions: List[Definition], filename_no_extension: str,
    ):
        self._definitions: List[Definition] = definitions
        self._filename_no_ext: str = filename_no_extension

    @property
    def definitions(self) -> List[Definition]:
        return self._definitions

    @property
    def comment_escape(self) -> str:
        return "//"

    @classmethod
    def file_extension(cls) -> str:
        return ".proto"

    @property
    def filename(self) -> str:
        return f"{self._filename_no_ext}{GeneratedProtoTextWriter.file_extension()}"

    def write_header(self) -> List[str]:
        return super().write_header() + ['syntax = "proto3";']

    def write_definition(self, definition: Definition) -> List[str]:
        result = [f"message {definition.name_pascal_case}Proto {{"]

        # google protobuf field number
        #    """
        #    Note that field numbers in the range 1 through 15 take one byte to
        #    encode, including the field number and the field's type (you can
        #    find out more about this in Protocol Buffer Encoding). Field numbers
        #    in the range 16 through 2047 take two bytes. So you should reserve
        #    the numbers 1 through 15 for very frequently occurring message
        #    elements.
        #    """
        #
        field_number = 1
        for compose in definition.compose_definitions:
            google_proto_defintion: GoogleProtoDefinition = COMPOSE_MAPPING[compose]
            result.append(f"{INDENT}{google_proto_defintion.type} {google_proto_defintion.name} = {field_number};")
            field_number += 1

        for field in definition.fields:
            result.append(f"{INDENT}{TYPE_MAPPING[field.type_definition.type]} {field.name} = {field_number};")
            field_number += 1

        result.append("}")
        return result

    def write_text(self) -> str:
        lines: List[str] = []

        # file header
        lines.extend(self.write_header() + ["", ""])

        definition: Definition
        for definition in self.definitions:
            lines.extend(self.write_definition(definition))
            lines.append("")

        # as one string
        return "\n".join(lines)

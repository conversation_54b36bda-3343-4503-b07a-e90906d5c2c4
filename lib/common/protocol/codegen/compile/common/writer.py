from abc import ABC, abstractmethod
from typing import List

from lib.common.protocol.codegen.compile.common.header import write_header


class BaseTextWriter(ABC):
    """
    The base class for writer defining the common structure across various file types

    Not much can be assumed here
    """

    @property
    @abstractmethod
    def comment_escape(self) -> str:
        pass

    @property
    @abstractmethod
    def filename(self) -> str:
        pass

    @classmethod
    @abstractmethod
    def file_extension(cls) -> str:
        pass

    # can be overridden by subclass if they want, but they should call super() or write the base header themselves
    def write_header(self) -> List[str]:
        """
        Base implementation uses standard header
        """
        return write_header(comment_escape=self.comment_escape)

    @abstractmethod
    def write_text(self) -> str:
        pass

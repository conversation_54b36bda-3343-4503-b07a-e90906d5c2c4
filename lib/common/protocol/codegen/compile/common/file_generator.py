import os
from abc import ABC, abstractmethod
from pathlib import Path

from lib.common.protocol.codegen.compile.common.constants import FILE_EXTENSION_PREFIX_SPACING
from lib.common.protocol.codegen.compile.common.writer import BaseTextWriter
from lib.common.terminal import EscapeColor


class BaseFileGenerator(ABC):
    """
    Base class for generating new files.
    """

    @classmethod
    @abstractmethod
    def colorize_color(cls) -> str:
        """
        Every type of file should try to have a unique color of sysout
        """
        pass

    @property
    @abstractmethod
    def writer(self) -> BaseTextWriter:
        """
        The underlying writer which forms the string to be written to the file
        """
        pass

    def log_file_generated(self, output_filepath: str, colorize: bool = False, verbose: bool = False) -> None:
        print_prefix = f"[{self.writer.file_extension()}]".ljust(FILE_EXTENSION_PREFIX_SPACING)
        print_str = f"{print_prefix}{output_filepath}"
        if colorize:
            print(EscapeColor.apply(self.colorize_color(), print_str))
        else:
            print(print_str)

        # if verbose, print the whole file
        if verbose:
            with open(output_filepath, "r") as f:
                print(f.read())

    def write(self, output_dir: Path, *, sysout_colorize: bool = False, sysout_verbose: bool = False) -> None:
        # construct text
        output_text: str = self.writer.write_text()

        # write to file
        output_filepath = os.path.join(output_dir, self.writer.filename)
        with open(output_filepath, "w+") as f:
            f.write(output_text)

        # log to sysout
        # self.log_file_generated(output_filepath=output_filepath, colorize=sysout_colorize, verbose=sysout_verbose)

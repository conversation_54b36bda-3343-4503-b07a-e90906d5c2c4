import importlib
from collections import defaultdict
from typing import Any, Dict, List, Optional, Type

from sortedcontainers import SortedSet

from lib.common.protocol.codegen.compile.common.writer import BaseTextWriter
from lib.common.protocol.codegen.compile.python.field import Python<PERSON>ieldWriter
from lib.common.protocol.codegen.compile.python.prototype.prototype import Prototype
from lib.common.protocol.codegen.compile.python.prototype.timestamp import TimestampPrototype
from lib.common.protocol.codegen.compile.python.validation import VALIDATION_MAPPING, required
from lib.common.protocol.codegen.definition import Definition
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin

TAB = "    "
INDENT = TAB
DEFAULT_MAX_LINE_LENGTH = 120

DEFAULT_AVAILABLE_PROTOTYPES = [TimestampPrototype]


def write_known_import(obj: Any) -> str:
    """
    Write an import line of a python file as:

    > from __module__ import __name__

    Since import is provided and used directly, no need to import it again to ensure validity.
    """
    return f"from {obj.__module__} import {obj.__name__}"


def write_import(*, module: str, names: List[str]) -> str:
    """
    Write an import line of a python file as:

    > from __module__ import __name__

    Module will be imported via importlib to ensure it is a valid import
    """
    # check that import is valid
    imported_module = importlib.import_module(module)
    for name in names:
        imported_symbol = getattr(imported_module, name)
        assert imported_symbol is not None
    # write import as single line - black will merge it later
    return f"from {module} import {', '.join(SortedSet(names))}"


class GeneratedPythonTextWriter(BaseTextWriter):
    """
    Writer class to write a valid python classfile based on a provided definition.

    Future: The guts of this class can be pulled out and abstracted away from "python"
    This will allow us to re-use common code generation techniques when serializing classfiles
    for JS, C, etc.
    """

    def __init__(
        self,
        *,
        definition: Definition,
        google_proto_type: str,
        prototypes: Optional[List[Prototype]] = None,
        max_line_length: int = DEFAULT_MAX_LINE_LENGTH,
    ):
        super().__init__()
        self._definition = definition
        # the fully specified name of the google protobuf type
        # e.g. generated.foo.bar.baz.HelloProto
        self._google_proto_type = google_proto_type
        # don't really expect clients to override this except maybe for testing
        self._prototypes = prototypes if prototypes is not None else DEFAULT_AVAILABLE_PROTOTYPES
        self._max_line_length = max_line_length

    @property
    def definition(self) -> Definition:
        return self._definition

    @property
    def comment_escape(self) -> str:
        return "#"

    @classmethod
    def file_extension(cls) -> str:
        return ".py"

    @property
    def filename(self) -> str:
        return f"{self.definition.name_snake_case}.py"

    @property
    def google_proto_type(self) -> str:
        return self._google_proto_type

    @property
    def prototypes_to_compose(self) -> List[Prototype]:
        return list(filter(lambda p: p.compose_definition in self.definition.compose_definitions, self._prototypes))

    def write_thirdparty_imports(self) -> List[str]:
        # THIRD PARTY PACKAGES
        thirdparty_import_block: SortedSet[str] = SortedSet()
        # currently none (there used to be some)
        return [s for s in thirdparty_import_block]

    def write_typing_imports(self) -> List[str]:
        # THIRD PARTY PACKAGES
        typing_import_block: SortedSet[str] = SortedSet()

        typing_module_types_to_import = []

        # Type
        typing_module_str, typing_type_str = str(Type).split(".")
        typing_module_types_to_import.append(typing_type_str)

        # Optional
        need_to_import_optional: bool = any(map(lambda f: not f.required, self.definition.fields))
        if need_to_import_optional:
            typing_module_types_to_import.append(str(Optional).split(".")[1])

        typing_import_block.add(write_import(module=typing_module_str, names=typing_module_types_to_import))

        return [s for s in typing_import_block]

    def write_generated_imports(self) -> List[str]:
        # GENERATED PACKAGES
        generated_import_block: SortedSet[str] = SortedSet()
        split_proto_type = self.google_proto_type.split(".")
        generated_import_block.add(write_import(module=".".join(split_proto_type[:-1]), names=[split_proto_type[-1]]))

        return [s for s in generated_import_block]

    def write_lib_common_imports(self) -> List[str]:
        # lib/common PACKAGES
        lib_common_import_block: Dict[str, SortedSet[str]] = defaultdict(SortedSet)
        # Proto
        lib_common_import_block[ProtobufSerializationMixin.__module__].add(ProtobufSerializationMixin.__name__)

        # composes
        for prototype in self.prototypes_to_compose:
            if prototype.compose_definition in self.definition.compose_definitions:
                lib_common_import_block[prototype.classtype.__module__].add(prototype.classtype.__name__)

        # validation
        possible_validation_imports = list(sorted(VALIDATION_MAPPING.values(), key=lambda o: o.__name__,))
        validations: List[str] = []
        for f in self.definition.fields:
            if f.validations is not None:
                validations.extend([VALIDATION_MAPPING[v].__name__ for v in f.validations])
        validation_imports: SortedSet[Any] = SortedSet(key=lambda o: o.__name__)

        # IMPLIED
        # if required, then add required because we will import it later
        #     (this hints that we need a much more smart design that can build this information as we go)
        if any(map(lambda f: f.required, self.definition.fields)):
            validation_imports.add(required)

        # EXPLICIT
        # now cover the validations that we will be using later
        for v in validations:
            for i in possible_validation_imports:
                if i.__name__ in v:
                    validation_imports.add(i)
        # now that we've figured out all the validation imports, implied or explicit, import them
        for imp in validation_imports:
            lib_common_import_block[imp.__module__].add(imp.__name__)

        # TODO if this gets too long it will break isort rules and we'll want to implement splitting
        # across lines in the same way (self-sufficiently without relying on a Makefile to isort)
        return [f"from {k} import {', '.join(v)}" for k, v in sorted(lib_common_import_block.items())]

    def write_imports(self) -> List[str]:
        thirdparty_imports_block = self.write_thirdparty_imports()
        if len(thirdparty_imports_block) > 0:
            thirdparty_imports_block += [""]
        return (
            self.write_typing_imports()
            + [""]
            + thirdparty_imports_block
            + self.write_generated_imports()
            + self.write_lib_common_imports()
        )

    def write_class_comment(self) -> List[str]:
        class_comment: List[str] = [f'{TAB}"""', f"{TAB}Auto-generated {self.definition.name_pascal_case}", f'{TAB}"""']
        return class_comment

    def write_class_declaration(self) -> List[str]:
        class_header: str = f"class {self.definition.name_pascal_case}("
        for c in self.prototypes_to_compose:
            class_header += f"{c.name}, "
        class_header += f"{ProtobufSerializationMixin.__name__}[{self.definition.name_pascal_case}Proto]"
        class_header += "):"

        return [class_header] + self.write_class_comment()

    def write__init__(self) -> List[str]:
        # empty object
        if len(self.definition.fields) + len(self.definition.compose_definitions) == 0:
            return []

        # __init__
        # manage as set of tokens so we can do proper spacing in case too long.
        # we don't want to externally depend on someone else to re-format our generated output
        # so we do that hard work to pass linter here

        first_token = "def __init__("
        variable_tokens = ["self, ", "*"]
        prototype: Prototype
        for prototype in self.prototypes_to_compose:
            variable_tokens[len(variable_tokens) - 1] += ", "
            variable_tokens.append(prototype.init_param.init_definition())
        for f in self.definition.fields:
            variable_tokens[len(variable_tokens) - 1] += ", "
            variable_tokens.append(PythonFieldWriter(f).init_param())
        last_token = "):"
        tokens = [first_token] + variable_tokens + [last_token]

        init_declaration = []
        double_indent = f"{TAB}{TAB}"
        if len(INDENT) + sum([len(s) for s in tokens]) <= self._max_line_length:
            # fits on one line
            init_declaration.append(INDENT + "".join(tokens))
        elif len(double_indent) + sum([len(s) for s in variable_tokens]) <= self._max_line_length:
            # variables fits on one line with indent
            init_declaration.append(INDENT + first_token)
            init_declaration.append(double_indent + "".join(variable_tokens))
            init_declaration.append(INDENT + last_token)
        else:
            # one variable per line
            init_declaration.append(INDENT + first_token)
            for v in variable_tokens:
                init_declaration.append(double_indent + v.strip())
            init_declaration.append(INDENT + last_token)

        init_body: List[str] = []

        # COMPOSE injection
        # TODO add more types of composable behavior
        for prototype in self.prototypes_to_compose:
            # whitespace
            init_body.append("")

            param_name = prototype.init_param.param
            # comment
            init_body.append(f"{TAB}{TAB}# {param_name}")

            # for now, no support for validation (e.g. don't called required(param_name))
            # leave that up to the Prototype class being injected

            # __init__
            init_body.append(f"{TAB}{TAB}{prototype.name}.__init__(self, {param_name}={param_name})")

        # store fields
        for f in self.definition.fields:
            # whitespace
            init_body.append("")

            # comment
            init_body.append(f"{TAB}{TAB}# {f.name}")

            # validation
            if f.required:
                # mypy won't catch everything
                init_body.append(f"{TAB}{TAB}required({f.name})")

            if f.validations is not None:
                indent_next_line = False
                if not f.required:
                    init_body.append(f"{TAB}{TAB}if {f.name} is not None:")
                    indent_next_line = True
                for v in f.validations:
                    indents = f"{TAB}{TAB}{TAB if indent_next_line else ''}"
                    # v1: validation is always a function that accepts a single positional parameter
                    validation = f"{VALIDATION_MAPPING[v].__name__}({f.name})"
                    init_body.append(f"{indents}{validation}")

            # variable assignment
            init_body.append(f"{TAB}{TAB}self._{f.name}: {PythonFieldWriter(f).mypy_type()} = {f.name}")

        return init_declaration + init_body

    def write_cls_proto_type(self) -> List[str]:
        proto_type_str = self.google_proto_type.split(".")[-1]
        proto_type_lines: List[str] = [
            f"{TAB}@classmethod",
            f"{TAB}def proto_type(cls) -> {str(Type).split('.')[1]}[{proto_type_str}]:",
            f"{TAB}{TAB}return {proto_type_str}",
        ]

        return proto_type_lines

    def write_properties(self) -> List[str]:
        property_funcs: List[str] = []
        first = True
        for f in self.definition.fields:
            if not first:
                property_funcs.append("")
            first = False
            # property
            property_funcs.append(f"{TAB}@property")
            property_funcs.append(f"{TAB}def {f.name}(self) -> {PythonFieldWriter(f).mypy_type()}:")
            property_funcs.append(f"{TAB}{TAB}return self._{f.name}")
        return property_funcs

    def write_class(self) -> List[str]:
        class_declaration: List[str] = self.write_class_declaration()
        init_func: List[str] = self.write__init__()
        proto_type: List[str] = self.write_cls_proto_type()
        property_funcs: List[str] = self.write_properties()

        result = class_declaration + [""] + (init_func + [""] if len(init_func) > 0 else []) + proto_type
        if len(property_funcs) > 0:
            result += [""] + property_funcs
        return result

    def write_text(self) -> str:
        lines: List[str] = []

        # file header
        lines.extend(self.write_header())

        # imports
        lines.extend(self.write_imports())
        lines.append("")
        lines.append("")

        # class
        lines.extend(self.write_class())

        # trailing newline
        lines.append("")

        # as one string
        return "\n".join(lines)

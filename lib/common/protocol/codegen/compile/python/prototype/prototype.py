from typing import Any, Type

from attr import dataclass

from lib.common.protocol.codegen.compose_definition import ComposeDefinition


@dataclass(frozen=True)
class InitParam:
    param: str
    type: Type[Any]

    def init_definition(self) -> str:
        return f"{self.param}: {self.type.__name__}"


class Prototype:
    """
    A prototype defines a set of properties to be written into a target class.

    In the case of name collision, the prototype property registered last shall be used.
      e.g. if we have Prototype(clas=Foo) and Prototype(clas=Bar) and both Foo, Bar define
      a property baz(), then the Prototype registered second will provide the target object's
      definition for baz()

    Intuitively, this should behave the same way as JavaScript's Object.assign. We are just building
    declarative scaffolding around it so we can do this across languages in a standard, but opinionated
    manner.
        > https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign
    """

    def __init__(self, compose_definition: ComposeDefinition, clas: Type[Any], init_param: InitParam):
        self._compose_definition = compose_definition
        self._class = clas
        self._init_param = init_param

    @property
    def compose_definition(self) -> ComposeDefinition:
        return self._compose_definition

    @property
    def name(self) -> str:
        return self.classtype.__name__

    @property
    def init_param(self) -> InitParam:
        return self._init_param

    @property
    def classtype(self) -> Type[Any]:
        return self._class

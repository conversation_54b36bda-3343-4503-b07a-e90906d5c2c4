import importlib
import os
import shutil
import tempfile
from pathlib import Path
from typing import List

from lib.common.file import repo_relpath
from lib.common.protocol.codegen.compile.python.writer import GeneratedPythonTextWriter
from lib.common.protocol.codegen.compile.test.utils import FAKE_OBJECT, FAKE_OBJECT_FILEPATH, TEST_DIR
from lib.common.protocol.codegen.definition import Definition, DefinitionParser
from lib.common.time import maka_control_timestamp_ms


def test_compile_test_object() -> None:
    output_filename = f"{FAKE_OBJECT}.py"

    definitions: List[Definition] = DefinitionParser.load_from_file(FAKE_OBJECT_FILEPATH)
    assert len(definitions) == 1
    definition: Definition = definitions[0]
    gogole_proto_type = (
        os.path.join(repo_relpath(TEST_DIR), "protobuf").replace("/", ".") + ".fake_object_pb2.FakeObjectProto"
    )
    writer = GeneratedPythonTextWriter(definition=definition, google_proto_type=gogole_proto_type)

    with tempfile.TemporaryDirectory() as tmp_dir:
        tmp_output_filepath = os.path.normpath(os.path.join(tmp_dir, output_filename))

        # write file to tmp dir
        text: str = writer.write_text()
        with open(tmp_output_filepath, "w+") as f:
            f.write(text)

        # now copy it into a tmp/ dir locally that we can import it with importlib
        # TODO: can we figure out how to import it from the test dir directly?
        #       maybe look at modifying sys.path
        new_tmp_dir = Path(os.path.join(TEST_DIR, "tmp"))
        importable_init_path = Path(os.path.join(new_tmp_dir, "__init__.py"))
        importable_filepath = Path(os.path.join(new_tmp_dir, output_filename))
        try:
            if new_tmp_dir.exists():
                shutil.rmtree(new_tmp_dir)
            new_tmp_dir.mkdir()
            # Mark as module with an __init__.py
            importable_init_path.touch()
            shutil.move(tmp_output_filepath, importable_filepath)

            # import the object
            module_path = ".".join(test_compile_test_object.__module__.split(".")[:-2] + ["test", "tmp", FAKE_OBJECT])
            module = importlib.import_module(module_path)
            fake_object_init = getattr(module, "FakeObject")
            assert fake_object_init is not None

            fake_object = fake_object_init(
                timestamp_ms=maka_control_timestamp_ms(), field_string="foo", field_float1=1, field_float2=0
            )
            assert fake_object is not None
        finally:
            # cleanup
            if new_tmp_dir.exists():
                shutil.rmtree(new_tmp_dir)

from lib.common.protocol.codegen.compile.common.file_generator import BaseFileGenerator
from lib.common.protocol.codegen.compile.common.writer import BaseTextWriter
from lib.common.protocol.codegen.compile.python.writer import GeneratedPythonTextWriter
from lib.common.protocol.codegen.definition import Definition
from lib.common.terminal import EscapeColor


class PythonFileGenerator(BaseFileGenerator):
    """
    Write .py files
    """

    def __init__(self, definition: Definition, google_proto_type: str):
        super().__init__()
        self._writer = GeneratedPythonTextWriter(definition=definition, google_proto_type=google_proto_type)

    @classmethod
    def colorize_color(cls) -> str:
        return EscapeColor.CYAN

    @property
    def writer(self) -> BaseTextWriter:
        return self._writer

from typing import Optional

from lib.common.protocol.codegen.compile.python.type_mapping import TYPE_MAPPING
from lib.common.protocol.codegen.field_definition import FieldDefinition


class PythonFieldWriter:
    """
    Provides utility methods for writing a FieldDefinition to a python output
    """

    def __init__(self, field: FieldDefinition):
        self._field_definition: FieldDefinition = field
        self._type: type = TYPE_MAPPING[self._field_definition.type_definition.type]

    def mypy_type(self) -> str:
        """
        Create string of form:
            str
        or
            Optional[str]
        """
        type_str = self._type.__name__
        if not self._field_definition.required:
            optional_str = str(Optional).split(".")[-1]  # no .__name__
            type_str = f"{optional_str}[{type_str}]"
        return type_str

    def init_param(self) -> str:
        """
        Create string of form:
          name: type = default
        """
        init_def = f"{self._field_definition.name}: {self.mypy_type()}"
        if self._field_definition.default is not None:
            init_def = f"{init_def} = {self._field_definition.default}"
        elif not self._field_definition.required:
            init_def = f"{init_def} = None"

        return init_def

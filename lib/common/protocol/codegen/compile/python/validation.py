from typing import Any, Callable, Dict, List, Optional, TypeVar

from lib.common.protocol.codegen.validation_definition import Validation


class ValidationError(ValueError):
    """
    A ValueError detected during validation.
    """

    pass


def nonzero(val: float) -> float:
    """
    raise error if val is not zero
    """
    if val != 0:
        return val
    raise ValidationError(f"Invalid: not {nonzero.__name__}. val = {val}")


def positive(val: float) -> float:
    """
    raise error if val is negative or zero
    """
    if val > 0:
        return val
    raise ValidationError(f"Invalid: not {positive.__name__}. val = {val}")


def nonpositive(val: float) -> float:
    """
    raise error if val is positive
    """
    if val <= 0:
        return val
    raise ValidationError(f"Invalid: not {nonpositive.__name__}. val = {val}")


def negative(val: float) -> float:
    """
    raise error if val is positive or zero
    """
    if val < 0:
        return val
    raise ValidationError(f"Invalid: not {negative.__name__}. val = {val}")


def nonnegative(val: float) -> float:
    """
    raise error if val is negative
    """
    if val >= 0:
        return val
    raise ValidationError(f"Invalid: not {nonnegative.__name__}. val = {val}")


def less_than(val: float, other: float) -> float:
    """
    raise error if not val < other
    """
    if val < other:
        return val
    raise ValidationError(f"Invalid: not {less_than.__name__}. val = {val} >= {other}")


def less_than_or_equal(val: float, other: float) -> float:
    """
    raise error if not val <= other
    """
    if val <= other:
        return val
    raise ValidationError(f"Invalid: not {less_than_or_equal.__name__}. val = {val} > {other}")


def greater_than(val: float, other: float) -> float:
    """
    raise error if not val > other
    """
    if val > other:
        return val
    raise ValidationError(f"Invalid: not {greater_than.__name__}. val = {val} <= {other}")


def greater_than_or_equal(val: float, other: float) -> float:
    """
    raise error if not val >= other
    """
    if val >= other:
        return val
    raise ValidationError(f"Invalid: not {greater_than_or_equal.__name__}. val = {val} < {other}")


def between_inclusive(val: float, a: float, b: float) -> float:
    """
    raise error if not in [a, b]
    """
    assert b >= a
    return less_than_or_equal(greater_than_or_equal(val, a), b)


def between_exclusive(val: float, a: float, b: float) -> float:
    """
    raise error if not in (a, b)
    """
    assert b >= a
    return less_than(greater_than(val, a), b)


def between_second_half_open(val: float, a: float, b: float) -> float:
    """
    raise error if not in [a, b)
    """
    assert b >= a
    return less_than(greater_than_or_equal(val, a), b)


def unit_interval_inclusive(val: float) -> float:
    """
    raise error if not inside [-1, 1]
    """
    return between_inclusive(val, -1.0, 1.0)


def zero_to_360(val: float) -> float:
    """
    raise error if not inside [0, 360)
    """
    return between_second_half_open(val, 0, 360)


T = TypeVar("T")


def whitelist(val: T, choices: List[T]) -> T:
    """
    raise error if not in whitelist
    """
    if val in choices:
        return val
    raise ValidationError(f"Invalid: not {whitelist.__name__}. val = {val} not in {choices}")


def required(val: Optional[T]) -> T:
    """
    raise error if None
    """
    if val is not None:
        return val
    raise ValidationError(f"Invalid: not {required.__name__}. val = {val}")


VALIDATION_MAPPING: Dict[Validation, Callable[[Any], Any]] = {
    Validation.NEGATIVE: negative,
    Validation.POSITIVE: positive,
    Validation.NONNEGATIVE: nonnegative,
    Validation.NONPOSITIVE: nonpositive,
    Validation.NONZERO: nonzero,
    Validation.UNIT_INTERVAL_INCLUSIVE: unit_interval_inclusive,
    Validation.ZERO_TO_360: zero_to_360,
}

from argparse import ArgumentParser
from typing import Any


class CprotocArgumentParser(ArgumentParser):
    def __init__(self, *argv: Any, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        # required
        self.add_argument("--input", required=True, type=str, nargs="+")
        self.add_argument("--output", required=True)

        # at least one of below needs to be set
        self.add_argument("--python-out", action="store_true")
        # javascript-out
        # c-out
        # etc.

        # optional
        self.add_argument("--colorize", action="store_true")
        self.add_argument("--verbose", action="store_true")

import os
from pathlib import Path

TEST_DIR = str(Path(__file__).parent.absolute())

# empty message
EMPTY_MESSAGE = "empty_message"
EMPTY_MESSAGE_FILENAME = f"{EMPTY_MESSAGE}.yaml"
EMPTY_MESSAGE_FILEPATH = os.path.normpath(os.path.join(TEST_DIR, EMPTY_MESSAGE_FILENAME))

# fake object
FAKE_OBJECT = "fake_object"
FAKE_OBJECT_FILENAME = f"{FAKE_OBJECT}.yaml"
FAKE_OBJECT_FILEPATH = os.path.normpath(os.path.join(TEST_DIR, FAKE_OBJECT_FILENAME))

# fake object bad type
FAKE_OBJECT_BAD_TYPE_FILENAME = f"{FAKE_OBJECT}_bad_type.yaml"
FAKE_OBJECT_BAD_TYPE_FILEPATH = os.path.normpath(os.path.join(TEST_DIR, FAKE_OBJECT_BAD_TYPE_FILENAME))

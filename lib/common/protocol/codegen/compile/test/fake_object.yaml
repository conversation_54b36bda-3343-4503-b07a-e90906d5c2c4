fake_object:

    compose:
      - timestamp

    fields:
      field_string:
        type: string
        required: true
        
      field_float1:
        type: float
        required: false
        validations:
         - unit_interval_inclusive
        
      field_float2:
        type: float
        required: false
        validations:
         - unit_interval_inclusive
         # future syntax to be supported:
         #- greater_than:
         #    - 5

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: fake_object.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='fake_object.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x11\x66\x61ke_object.proto\"i\n\x0f\x46\x61keObjectProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x14\n\x0c\x66ield_string\x18\x02 \x01(\t\x12\x14\n\x0c\x66ield_float1\x18\x03 \x01(\x02\x12\x14\n\x0c\x66ield_float2\x18\x04 \x01(\x02\x62\x06proto3')
)




_FAKEOBJECTPROTO = _descriptor.Descriptor(
  name='FakeObjectProto',
  full_name='FakeObjectProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='FakeObjectProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='field_string', full_name='FakeObjectProto.field_string', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='field_float1', full_name='FakeObjectProto.field_float1', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='field_float2', full_name='FakeObjectProto.field_float2', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21,
  serialized_end=126,
)

DESCRIPTOR.message_types_by_name['FakeObjectProto'] = _FAKEOBJECTPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

FakeObjectProto = _reflection.GeneratedProtocolMessageType('FakeObjectProto', (_message.Message,), {
  'DESCRIPTOR' : _FAKEOBJECTPROTO,
  '__module__' : 'fake_object_pb2'
  # @@protoc_insertion_point(class_scope:FakeObjectProto)
  })
_sym_db.RegisterMessage(FakeObjectProto)


# @@protoc_insertion_point(module_scope)

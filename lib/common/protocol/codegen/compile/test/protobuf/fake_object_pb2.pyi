# @generated by generate_proto_mypy_stubs.py.  Do not edit!
import sys
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class FakeObjectProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    field_string: typing___Text = ...
    field_float1: builtin___float = ...
    field_float2: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        field_string : typing___Optional[typing___Text] = None,
        field_float1 : typing___Optional[builtin___float] = None,
        field_float2 : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"field_float1",b"field_float1",u"field_float2",b"field_float2",u"field_string",b"field_string",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___FakeObjectProto = FakeObjectProto

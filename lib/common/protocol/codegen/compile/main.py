#!/usr/bin/env python
import os
from pathlib import Path
from typing import List

from attr import dataclass

from lib.common.file import repo_relpath
from lib.common.protocol.codegen.compile.args import CprotocArgumentParser
from lib.common.protocol.codegen.compile.proto.file_generator import GoogleProtoFileGenerator
from lib.common.protocol.codegen.compile.proto.protoc import run_protoc
from lib.common.protocol.codegen.compile.python.file_generator import PythonFileGenerator
from lib.common.protocol.codegen.definition import Definition, DefinitionParser


def process(
    *,
    input_filepath: Path,
    output_dir: Path,
    python_out: bool,
    sysout_colorize: bool = False,
    sysout_verbose: bool = False,
) -> None:
    """
    Workhorse method to process the given input file all the way through
    """
    # load definition from input file
    definitions: List[Definition] = DefinitionParser.load_from_file(str(input_filepath))
    input_filename_no_extension = os.path.basename(input_filepath).split(".")[0]

    # 2. Generate Google .proto files
    # one input file = one output file for the .proto files
    google_proto_output_dir = Path(os.path.join(output_dir, "protobuf"))
    google_proto_output_dir.mkdir(exist_ok=True)
    google_proto_generator = GoogleProtoFileGenerator(
        definitions=definitions, filename_no_extension=input_filename_no_extension
    )
    google_proto_generator.write(
        output_dir=google_proto_output_dir, sysout_colorize=sysout_colorize, sysout_verbose=sysout_verbose
    )

    # 3. Run protoc compiler
    run_protoc(
        input_files=[google_proto_generator.writer.filename], python_out=python_out, output_dir=google_proto_output_dir
    )

    # 4. Generate final python output
    # one python file per definition
    base_module_name = f"{str(repo_relpath(output_dir)).replace('/', '.')}.protobuf"
    for definition in definitions:
        google_proto_type = f"{base_module_name}.{input_filename_no_extension}_pb2.{definition.name_pascal_case}Proto"
        code_file_generator = PythonFileGenerator(definition=definition, google_proto_type=google_proto_type)
        code_file_generator.write(output_dir=output_dir, sysout_colorize=sysout_colorize, sysout_verbose=sysout_verbose)


@dataclass(frozen=True)
class MainProgArgs:
    """
    The arguments for this main prog
    """

    input_filepaths: List[Path]
    output_dir: Path
    python_out: bool
    colorize: bool
    verbose: bool


def parse_args() -> MainProgArgs:
    """
    Nicely parse args
    """
    args = CprotocArgumentParser().parse_args()
    return MainProgArgs(
        input_filepaths=[Path(inp) for inp in args.input],
        output_dir=Path(args.output),
        python_out=args.python_out,
        colorize=args.colorize,
        verbose=args.verbose,
    )


def main() -> None:
    """
    Run the command line tool
    """
    args: MainProgArgs = parse_args()
    assert (
        args.python_out
    ), "Must pass at least one of: --python-out (only python supported today so you better pass it!)"

    # 1. Generate module level files in the output dir
    if args.python_out:
        # touch __init__.py
        init_py_filepath = Path(os.path.join(args.output_dir, "__init__.py"))
        init_py_filepath.touch()
        # print(
        #     EscapeColor.apply(EscapeColor.YELLOW, f"{'[.py]'.ljust(FILE_EXTENSION_PREFIX_SPACING)}{init_py_filepath}")
        # )

    for f in sorted(args.input_filepaths):
        process(
            input_filepath=f,
            output_dir=args.output_dir,
            python_out=args.python_out,
            sysout_colorize=args.colorize,
            sysout_verbose=args.verbose,
        )


if __name__ == "__main__":
    main()

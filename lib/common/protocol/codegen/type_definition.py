SUPPORTED_TYPES = [
    "boolean",
    "float",
    "string",
    "int32",
    "uint32",
    "uint64",
]


class TypeDefinition:
    """
    A field of a generated classfile.

    Not coupled to any particular language.
    """

    def __init__(self, type: str):
        assert type in SUPPORTED_TYPES, f"Unexpected type: {type} not in {SUPPORTED_TYPES}"
        self._type = type

    @property
    def type(self) -> str:
        return self._type

import pytest

from lib.common.protocol.codegen.compile.test.utils import (
    EMPTY_MESSAGE_FILEPATH,
    FAKE_OBJECT_BAD_TYPE_FILEPATH,
    FAKE_OBJECT_FILEPATH,
)
from lib.common.protocol.codegen.definition import Definition<PERSON>ars<PERSON>


def test_load_from_file() -> None:
    DefinitionParser.load_from_file(FAKE_OBJECT_FILEPATH)


def test_load_from_file_with_empty_object() -> None:
    DefinitionParser.load_from_file(EMPTY_MESSAGE_FILEPATH)


def test_load_from_file_bad_def() -> None:
    with pytest.raises(AssertionError, match="Unexpected type") as _:
        DefinitionParser.load_from_file(FAKE_OBJECT_BAD_TYPE_FILEPATH)

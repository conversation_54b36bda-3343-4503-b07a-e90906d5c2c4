import os
import re
from typing import Any, Dict, List, Optional

import yaml

from lib.common.protocol.codegen.compose_definition import ComposeDefinition
from lib.common.protocol.codegen.field_definition import FieldDefinition
from lib.common.protocol.codegen.type_definition import TypeDefinition
from lib.common.protocol.codegen.validation_definition import Validation


def to_pascal_case(s: str) -> str:
    # capitalize to title case then remove spaces and underscores
    return "".join(c for c in s.title() if not c.isspace() and c != "_")


def to_snake_case(s: str) -> str:
    return re.sub(r"(?<!^)(?=[A-Z])", "_", s).lower()


class Definition:
    """
    The compose definition.

    Not coupled to any particular language.
    """

    def __init__(self, name: str, compose: List[ComposeDefinition], fields: List[FieldDefinition]):
        self._name = name
        # surface bugs
        assert len(compose) == 0 or isinstance(compose[0], ComposeDefinition)
        self._compose_definitions: List[ComposeDefinition] = compose
        self._fields: List[FieldDefinition] = fields

    @property
    def name(self) -> str:
        return self._name

    @property
    def name_pascal_case(self) -> str:
        return to_pascal_case(self.name)

    @property
    def name_snake_case(self) -> str:
        return to_snake_case(self.name)

    @property
    def compose_definitions(self) -> List[ComposeDefinition]:
        return self._compose_definitions

    @property
    def fields(self) -> List[FieldDefinition]:
        return self._fields


class DefinitionParser:
    @classmethod
    def load_from_file(cls, file: str) -> List[Definition]:
        # validate filename
        filename = os.path.basename(file)
        assert filename.lower() == filename

        with open(file, "r") as f:
            contents = yaml.safe_load(f)

            result: List[Definition] = []
            for name, entry in contents.items():
                result.append(DefinitionParser.from_dict({name: entry}))

            return result

    @classmethod
    def from_dict(cls, d: Dict[str, Any]) -> Definition:

        assert len(d) == 1, f"Expected one root element. Got: {list(d.keys())}"
        name: str
        body: Any  # can be None in case of empty message
        name, body = next(iter(d.items()))

        # prototype behavior to be composed
        compose: List[ComposeDefinition] = [ComposeDefinition(name=c) for c in (body or {}).get("compose", [])]

        fields: List[FieldDefinition] = []
        for field_name, field_details in (body or {}).get("fields", {}).items():
            validations: Optional[List[Validation]] = None
            validations_strs = field_details.get("validations")
            if validations_strs is not None:
                validations = []
                for v in validations_strs:
                    # mypy doesn't know it has bracket syntax
                    validation: Validation = Validation[v.upper()]  # type: ignore
                    # future: support validations that take parameters
                    validations.append(validation)
            fields.append(
                FieldDefinition(
                    name=field_name,
                    type_definition=TypeDefinition(type=field_details["type"]),
                    required=field_details["required"],
                    default=field_details.get("default"),
                    validations=validations,
                )
            )

        return Definition(name=name, compose=compose, fields=fields)

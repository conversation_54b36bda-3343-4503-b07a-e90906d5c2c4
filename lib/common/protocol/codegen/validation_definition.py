from typing import cast

from aenum import AutoNumberEnum


class Validation(AutoNumberEnum):
    """
    The category keys of the hardware config.
    """

    NEGATIVE: "Validation" = cast("Validation", ())

    POSITIVE: "Validation" = cast("Validation", ())

    NONNEGATIVE: "Validation" = cast("Validation", ())

    NONPOSITIVE: "Validation" = cast("Validation", ())

    NONZERO: "Validation" = cast("Validation", ())

    UNIT_INTERVAL_INCLUSIVE: "Validation" = cast("Validation", ())

    ZERO_TO_360: "Validation" = cast("Validation", ())


class ValidationDefinition:
    """
    A validation of a generated classfile.

    Not coupled to any particular language.
    """

    def __init__(self, validation: Validation):
        self._validation: Validation = validation

    @property
    def validation(self) -> Validation:
        return self._validation

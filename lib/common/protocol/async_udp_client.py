import asyncio
import functools
from typing import TYPE_CHECKING, Optional, Tuple, cast

import lib.common.logging
from lib.common.asyncio.event_loop import use_specific_loop

LOG = lib.common.logging.get_logger(__name__)


if TYPE_CHECKING:
    AsyncQueue = asyncio.Queue[bytes]
else:
    AsyncQueue = asyncio.Queue


class AsyncUDPProtocol(asyncio.Protocol):
    def __init__(self, read_queue: AsyncQueue, loop: asyncio.AbstractEventLoop):
        super().__init__()
        self._transport: Optional[asyncio.DatagramTransport] = None
        self._loop = loop
        self._read_queue = read_queue
        with use_specific_loop(loop):
            self._ready_event = asyncio.Event()

    async def ready(self) -> None:
        await self._ready_event.wait()

    # Overwritten from asyncio.Protocol
    def connection_made(self, transport: asyncio.BaseTransport) -> None:
        self._transport = cast(asyncio.DatagramTransport, transport)
        self._ready_event.set()

    # Overwritten from asyncio.Protocol
    def datagram_received(self, data: bytes, addr: Tuple[str, int]) -> None:
        asyncio.ensure_future(self._read_queue.put(data), loop=self._loop)

    def error_received(self, exc: Exception) -> None:
        pass

    def connection_lost(self, exc: Optional[Exception]) -> None:
        pass

    async def write(self, data: bytes) -> None:
        assert self._transport is not None
        assert asyncio.get_event_loop() == self._loop
        self._transport.sendto(data)


class UDPConnector:
    def __init__(self, host: str, port: int, loop: asyncio.AbstractEventLoop):
        self._host = host
        self._port = port
        self._loop = loop
        self._ready = False
        self._closed_success = True
        with use_specific_loop(loop):
            self._lock = asyncio.Lock()
            self._read_queue: AsyncQueue = asyncio.Queue()
        self._transport: Optional[asyncio.DatagramTransport] = None
        self._protocol: Optional[AsyncUDPProtocol] = None

    def get_identifier(self) -> str:
        return self._host

    async def open(self) -> None:
        async with self._lock:
            await self._open()

    async def _open(self) -> None:
        if self._transport is not None:
            return
        self._read_queue = asyncio.Queue()
        self._transport, self._protocol = await self._create_connection()
        await self._protocol.ready()
        self._ready = True
        LOG.info(f"{self.__class__.__name__} Opened Connection to {self._host}:{self._port}")

    async def close(self) -> None:
        async with self._lock:
            await self._close()

    async def _close(self) -> None:
        self._ready = False
        self._closed_success = False
        if self._transport is not None:
            self._transport.close()

        self._closed_success = True
        self._transport = None
        self._protocol = None

    async def _create_connection(self) -> Tuple[asyncio.DatagramTransport, "AsyncUDPProtocol"]:
        return cast(
            Tuple[asyncio.DatagramTransport, "AsyncUDPProtocol"],
            await self._loop.create_datagram_endpoint(
                functools.partial(AsyncUDPProtocol, read_queue=self._read_queue, loop=self._loop),
                remote_addr=(self._host, self._port),
            ),
        )

    async def _read(self, timeout_ms: Optional[int] = None) -> Optional[bytes]:
        if not self._closed_success:
            await self._close()
        if not self._ready:
            await self._open()
        assert self._protocol is not None
        success = False
        if timeout_ms is None:
            return await self._read_queue.get()
        else:
            try:
                if True:  # Use this for debugging timeout issues: random.random() > 0.005:
                    val = await asyncio.wait_for(self._read_queue.get(), timeout_ms / 1000)
                    success = True
                    return val
                else:
                    LOG.warn("Inducing Timeout at supervisory")
                    return None
            except asyncio.TimeoutError:
                LOG.debug("Actual timeout at supervisory")
                return None
            finally:
                if not success:
                    await self._close()

    async def _write(self, data: bytes) -> None:
        if not self._closed_success:
            await self._close()
        if not self._ready:
            await self._open()
        assert self._protocol is not None
        await self._protocol.write(data)

    async def write(self, data: bytes) -> None:
        async with self._lock:
            await self._write(data)

    async def write_await_response(self, data: bytes, timeout_ms: Optional[int] = None) -> Optional[bytes]:
        async with self._lock:
            await self._write(data)
            return await self._read(timeout_ms)

syntax = "proto3";

import "lib/common/buffer/buffer.proto";

package lib.common.image;

message Size {
	int32 width = 1;
	int32 height = 2;
}

message CamImageProto {
    string device_path = 1;
    string camera_id = 2;
    // both unit32, fixed32 throw range errors, e.g., "ValueError: Value out of range: 15923418228541"
    uint64 timestamp_ms = 3;
    Size size = 4;
    lib.common.buffer.BufferProto bytes = 5;
    lib.common.buffer.BufferProto depth_bytes = 6;
    float ppi = 7;
}

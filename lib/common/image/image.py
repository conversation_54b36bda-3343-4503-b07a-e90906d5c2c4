import logging
import math
from typing import Any, Dict, Optional, Tuple, cast

import cv2
import numba
import numba.cuda
import numpy as np
import numpy.typing as npt

# Numba default loglevel is broken, set it here
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder

logging.getLogger("numba.interpreter").setLevel("INFO")
logging.getLogger("numba.cuda.cudadrv.driver").setLevel("CRITICAL")

LensUndistortionMappingParametersType = Optional[Tuple[Any, Any]]


class ColorCorrectionProfile:
    """
    Color correction profile contains:
    1. beta - beta correction coefficients for each channel (R, G, B)
    2. n - refractive index for each channel (R, G, B)

    Color correction is done as follows:
    1. For a given pixel, determine its angle of incidence based on pixel_angle and provided pan/tilt angles
    2. Multiply pixel intensity by `math.exp(beta * (1 / math.sqrt(1 - (sin(aoi) / n) ** 2) - 1)`
    """

    def __init__(
        self, beta: Tuple[float, float, float], n: Tuple[float, float, float],
    ):
        self.beta = beta
        self.n = n

    def to_json(self) -> Dict[str, Any]:
        return {"beta": self.beta, "n": self.n}

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "ColorCorrectionProfile":
        return ColorCorrectionProfile(data["beta"], data["n"])

    def __repr__(self) -> str:
        return "ColorCorrectionProfile[beta: {}, n: {}]".format(self.beta, self.n,)


def compute_aoi_rad(angle_rad: npt.NDArray[Any]) -> npt.NDArray[Any]:
    # compute AOI using rectangle
    return cast(npt.NDArray[Any], np.arctan(np.sqrt(np.sum(np.tan(angle_rad) ** 2, axis=0))))


def compute_decay(angle_rad: npt.NDArray[Any], profile: ColorCorrectionProfile) -> npt.NDArray[Any]:
    aoi_rad = compute_aoi_rad(angle_rad)
    decay_mesh = np.zeros(angle_rad.shape[1:] + (3,))
    for c in range(3):
        mesh_slice = tuple([slice(None)] * len(angle_rad.shape[1:])) + tuple([c])
        decay_mesh[mesh_slice] = np.exp(-profile.beta[c] * (1 / np.sqrt(1 - (np.sin(aoi_rad) / profile.n[c]) ** 2) - 1))
    return decay_mesh


def _color_correct_cpu(
    image: npt.NDArray[Any],
    profile: ColorCorrectionProfile,
    top_left_angle_rad: Tuple[float, float],
    pixel_angle_rad: Tuple[float, float],
) -> npt.NDArray[Any]:
    # make a mesh of angular positions, pan & tilt
    angle_mesh = np.mgrid[0 : image.shape[0], 0 : image.shape[1]].astype(np.float32)[::-1]
    (pixel_angle_pan_rad, pixel_angle_tilt_rad) = pixel_angle_rad
    (top_left_angle_pan_rad, top_left_angle_tilt_rad) = top_left_angle_rad
    angle_mesh[0] = angle_mesh[0] * pixel_angle_pan_rad + top_left_angle_pan_rad
    angle_mesh[1] = angle_mesh[1] * pixel_angle_tilt_rad + top_left_angle_tilt_rad
    # rescale RGB values based on decay
    decay_mesh = compute_decay(angle_mesh, profile)
    return cast(npt.NDArray[Any], np.clip(image / decay_mesh, 0, 255))


# Numba doesn't support passing objects or Tuples to CUDA JIT'ed functions yet
@numba.cuda.jit
def _color_correct_cuda(
    image: npt.NDArray[Any],
    beta_r: float,
    beta_g: float,
    beta_b: float,
    n_r: float,
    n_g: float,
    n_b: float,
    top_left_angle_pan_rad: int,
    top_left_angle_tilt_rad: int,
    pixel_angle_pan_rad: float,
    pixel_angle_tilt_rad: float,
) -> None:
    x, y = numba.cuda.grid(2)
    if y >= image.shape[0] or x >= image.shape[1]:
        return
    # turn coordinate into angular positions
    angle_pan_rad_xy = top_left_angle_pan_rad + x * pixel_angle_pan_rad
    angle_tilt_rad_xy = top_left_angle_tilt_rad + y * pixel_angle_tilt_rad
    # compute AOI using rectangle
    aoi_rad = math.atan(math.sqrt(math.tan(angle_pan_rad_xy) ** 2 + math.tan(angle_tilt_rad_xy) ** 2))
    # correction based on AOI
    image[y, x, 0] = min(
        image[y, x, 0] * math.exp(beta_r * (1 / math.sqrt(1 - (math.sin(aoi_rad) / n_r) ** 2) - 1)), 255
    )
    image[y, x, 1] = min(
        image[y, x, 1] * math.exp(beta_g * (1 / math.sqrt(1 - (math.sin(aoi_rad) / n_g) ** 2) - 1)), 255
    )
    image[y, x, 2] = min(
        image[y, x, 2] * math.exp(beta_b * (1 / math.sqrt(1 - (math.sin(aoi_rad) / n_b) ** 2) - 1)), 255
    )


def color_correct(
    image: npt.NDArray[Any],
    profile: ColorCorrectionProfile,
    top_left_angle_rad: Tuple[float, float],
    pixel_angle_rad: Tuple[float, float],
) -> npt.NDArray[Any]:
    """
    Corrects colors in the image based on the ColorCorrectionProfile.
    top_left_angle_rad is pan/tilt optical angles of top left corner of the target image.
    """
    if numba.cuda.is_available():
        image_dev = numba.cuda.to_device(np.ascontiguousarray(image))
        threadsperblock = (16, 16)
        blockspergrid_x = math.ceil(image.shape[1] / threadsperblock[0])
        blockspergrid_y = math.ceil(image.shape[0] / threadsperblock[1])
        blockspergrid = (blockspergrid_x, blockspergrid_y)
        _color_correct_cuda[blockspergrid, threadsperblock](
            image_dev, *profile.beta, *profile.n, *top_left_angle_rad, *pixel_angle_rad
        )
        return cast(npt.NDArray[Any], image_dev.copy_to_host())

    return _color_correct_cpu(image, profile, top_left_angle_rad, pixel_angle_rad)


def apply_transforms(
    image: npt.NDArray[Any],
    mirror: bool = False,
    flip: bool = False,
    transpose: bool = False,
    recover_blue: bool = False,
    crop: Optional[Dict[str, int]] = None,
    lens_undistortion_mapping_parameters: LensUndistortionMappingParametersType = None,
    color_correction_profile: Optional[ColorCorrectionProfile] = None,
    top_left_angle_rad: Optional[Tuple[float, float]] = None,
    pixel_angle_rad: Optional[Tuple[float, float]] = None,
) -> npt.NDArray[Any]:
    if mirror and flip:
        image = cv2.flip(image, -1)
    elif mirror:
        image = cv2.flip(image, 1)
    elif flip:
        image = cv2.flip(image, 0)

    if lens_undistortion_mapping_parameters is not None:
        mapx, mapy = lens_undistortion_mapping_parameters
        with duration_perf_recorder(f"{PerfCategory.IMAGE}:{image.shape}", "Remap"):
            pass
            # TODO Disabling undistortion since it is too slow and seems to affect deepweed perf
            # We may need to renenable for OF work eperimentation
            # image = cv2.remap(image, mapx.transpose(), mapy.transpose(), cv2.INTER_LINEAR)

    if transpose:
        with duration_perf_recorder(f"{PerfCategory.IMAGE}:{image.shape}", "Transpose"):
            image = cv2.transpose(image)
            # The below returns a view and is much faster, but makes the copies
            # that happen later in the code much slower...
            # image = image.transpose((1, 0, 2))
    # Crop after mirror and flip so the user has some reference image
    if crop is not None:
        x1 = int(crop["x1"])
        y1 = int(crop["y1"])
        x2 = int(crop["x2"])
        y2 = int(crop["y2"])
        image = image[y1:y2, x1:x2]

    if color_correction_profile is not None and top_left_angle_rad is not None and pixel_angle_rad is not None:
        image = color_correct(image, color_correction_profile, top_left_angle_rad, pixel_angle_rad)

    if recover_blue:
        # The light that passed through beam combined and laser window has lost all the blue light.
        # We can recover it by using the `B = 0.75 * G + 0.25 * R` formula, that is derived by experiments.
        image[:, :, 0] = 0.75 * image[:, :, 1] + 0.25 * image[:, :, 2]

    return image


def tiled_9crop(img: npt.NDArray[Any], v_pct: float, h_pct: float) -> npt.NDArray[Any]:
    def _h_left_slice(img: npt.NDArray[Any], h_pct: float) -> slice:
        return slice(0, int(img.shape[1] * h_pct / 100))

    def _h_middle_slice(img: npt.NDArray[Any], h_pct: float) -> slice:
        return slice(int(img.shape[1] * (1 - h_pct / 100) / 2), int(img.shape[1] * (1 + h_pct / 100) / 2))

    def _h_right_slice(img: npt.NDArray[Any], h_pct: float) -> slice:
        return slice(int(img.shape[1] * (1 - h_pct / 100)), img.shape[1])

    def _v_top_slice(img: npt.NDArray[Any], v_pct: float) -> slice:
        return slice(0, int(img.shape[0] * v_pct / 100))

    def _v_middle_slice(img: npt.NDArray[Any], v_pct: float) -> slice:
        return slice(int(img.shape[0] * (1 - v_pct / 100) / 2), int(img.shape[0] * (1 + v_pct / 100) / 2))

    def _v_bottom_slice(img: npt.NDArray[Any], v_pct: float) -> slice:
        return slice(int(img.shape[0] * (1 - v_pct / 100)), img.shape[0])

    return np.vstack(
        [
            np.hstack(
                [
                    img[_v_top_slice(img, v_pct), _h_left_slice(img, h_pct)],
                    img[_v_top_slice(img, v_pct), _h_middle_slice(img, h_pct)],
                    img[_v_top_slice(img, v_pct), _h_right_slice(img, h_pct)],
                ]
            ),
            np.hstack(
                [
                    img[_v_middle_slice(img, v_pct), _h_left_slice(img, h_pct)],
                    img[_v_middle_slice(img, v_pct), _h_middle_slice(img, h_pct)],
                    img[_v_middle_slice(img, v_pct), _h_right_slice(img, h_pct)],
                ]
            ),
            np.hstack(
                [
                    img[_v_bottom_slice(img, v_pct), _h_left_slice(img, h_pct)],
                    img[_v_bottom_slice(img, v_pct), _h_middle_slice(img, h_pct)],
                    img[_v_bottom_slice(img, v_pct), _h_right_slice(img, h_pct)],
                ]
            ),
        ]
    )

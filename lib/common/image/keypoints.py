from typing import Any, List, Optional, <PERSON><PERSON>

import cv2
import numpy as np
import numpy.typing as npt


def detect_and_describe(image: npt.NDArray[Any]) -> Tuple[npt.NDArray[np.float32], npt.NDArray[Any]]:
    # detect and extract features from the image
    descriptor = cv2.AKAZE.create()
    (kps, features) = descriptor.detectAndCompute(image, np.array([]))

    # convert the keypoints from KeyPoint objects to NumPy arrays
    kps_float = np.array([kp.pt for kp in kps], dtype=np.float32)

    # return a tuple of keypoints and features
    return (kps_float, features)


def match_keypoints(
    kpsA: npt.NDArray[Any],
    kpsB: npt.NDArray[Any],
    featuresA: npt.NDArray[Any],
    featuresB: npt.NDArray[Any],
    ratio: float = 0.75,
    reproj_thresh: float = 4.0,
    min_matches: int = 4,
) -> Optional[Tuple[List[Tuple[int, int]], npt.NDArray[Any], npt.NDArray[Any]]]:
    assert min_matches >= 4, f"Need at least 4 matches: {min_matches}"

    # compute the raw matches and initialize the list of actual matches
    matcher = cv2.BFMatcher(cv2.NORM_HAMMING)
    rawMatches = matcher.knnMatch(featuresA, featuresB, 2)
    matches = []

    # loop over the raw matches
    for m in rawMatches:
        # ensure the distance is within a certain ratio of each other (i.e. Lowe's ratio test)
        if len(m) == 2 and m[0].distance < m[1].distance * ratio:
            matches.append((m[0].trainIdx, m[0].queryIdx))

    # computing a homography requires at least 4 matches
    if len(matches) >= min_matches:
        # construct the two sets of points
        ptsA = np.array([kpsA[i] for (_, i) in matches], dtype=np.float32)
        ptsB = np.array([kpsB[i] for (i, _) in matches], dtype=np.float32)

        # compute the homography between the two sets of points
        (H, status) = cv2.findHomography(ptsA, ptsB, cv2.RANSAC, reproj_thresh)

        if H is None:
            # no homography could be computed
            return None

        # return the matches along with the homograpy matrix and status of each matched point
        return (matches, H, status)

    # otherwise, no homograpy could be computed
    return None

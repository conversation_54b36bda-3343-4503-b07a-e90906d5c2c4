from typing import Any, <PERSON><PERSON>, cast

import cv2
import numpy.typing as npt


def resize_image(image: npt.NDArray[Any], new_size_xy: Tuple[int, int]) -> npt.NDArray[Any]:
    assert len(image.shape) in [2, 3], f"Wrong input shape: {image.shape}"
    if len(image.shape) == 3:
        assert image.shape[2] == 3, f"Only support BGR channel-last image: {image.shape}"
    if new_size_xy[0] < image.shape[1] and new_size_xy[1] < image.shape[0]:
        interpolation = cv2.INTER_AREA
    else:
        interpolation = cv2.INTER_LINEAR
    return cast(npt.NDArray[Any], cv2.resize(image, new_size_xy, interpolation=interpolation))


def rescale_image(img: npt.NDArray[Any], scale: int = 4) -> npt.NDArray[Any]:
    newsize = int(round(img.shape[1] / scale)), int(round(img.shape[0] / scale))
    return cast(npt.NDArray[Any], cv2.resize(img, newsize, interpolation=cv2.INTER_AREA))


def adjust_size_ppi(size: Tuple[int, int], ppi: float, new_ppi: float) -> Tuple[int, int]:
    width, height = size
    return int(width * new_ppi / ppi), int(height * new_ppi / ppi)


def adjust_coord_ppi(coord: Tuple[float, float], ppi: float, new_ppi: float) -> Tuple[float, float]:
    cx, cy = coord
    return cx * new_ppi / ppi, cy * new_ppi / ppi

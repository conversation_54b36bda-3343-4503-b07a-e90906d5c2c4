from typing import <PERSON><PERSON>, cast

import torch
from torchvision.transforms.functional import resize as torch_resize


def resize_image_torch(img: torch.Tensor, new_size_xy: Tuple[int, int]) -> torch.Tensor:
    assert len(img.shape) == 3 and img.shape[0] == 3, f"Image must lead with channels first, {img.shape}"
    assert img.is_cuda, f"img must be cuda: {img.device}"

    # Interpolation 3 == BICUBIC
    return cast(torch.Tensor, torch_resize(img, size=(new_size_xy[1], new_size_xy[0]), interpolation=3))


def rescale_image_torch(img: torch.Tensor, scale: int = 4) -> torch.Tensor:
    assert len(img.shape) == 3 and img.shape[0] == 3, f"Image must lead with channels first, {img.shape}"
    newsize = int(round(img.shape[2] / scale)), int(round(img.shape[1] / scale))
    return resize_image_torch(img, newsize)

from typing import Any, Op<PERSON>, <PERSON><PERSON>, Union, cast

import cv2
import numpy as np
import numpy.typing as npt

import lib.common.logging
from generated.lib.common.image.cam_pb2 import Cam<PERSON>mageProto, Size
from lib.common.buffer import Buffer, BufferType
from lib.common.error import MakaException
from lib.common.image.resize import resize_image
from lib.common.namespace.base import Path
from lib.common.perf.perf_tracker import PerfCategory, duration_perf_recorder
from lib.common.shmem.cpp.shmem_python import ImageBuffer
from lib.common.time import TimestampedObject

LOG = lib.common.logging.get_logger(__name__)


class CamTimeoutException(MakaException):
    pass


def load_image(img_file: str, ppi: Optional[float]) -> "CamImage":
    if img_file.endswith(".npz"):
        data = np.load(img_file)
        image_bgr = data["view_image"][:, :, ::-1]
        depth = data["view_depth"]
        return CamImage.dummy(image_bgr=image_bgr, depth=depth, ppi=ppi)
    else:
        return CamImage.dummy(image_bgr=cv2.imread(img_file), ppi=ppi)


class CamImage(TimestampedObject):
    """
    A timestamped image
    """

    def __init__(
        self,
        *,
        device_path: Path,
        camera_id: str,
        timestamp_ms: int,
        ppi: Optional[float],
        image_bgr: npt.NDArray[Any],
        depth: Optional[npt.NDArray[Any]],
        focus_value: Optional[float] = None,
    ):
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)
        self._device_path: Path = device_path
        self._camera_id: str = camera_id
        self._ppi: Optional[float] = ppi
        self._image_bgr: npt.NDArray[Any] = image_bgr
        self._depth: Optional[npt.NDArray[Any]] = depth
        self._focus_value = focus_value

        # size should be even by even so that we can safely return center point as ints
        assert self.image_bgr.shape[0] % 2 == 0, f"Uneven shape: {self.image_bgr.shape}"
        assert self.image_bgr.shape[1] % 2 == 0, f"Uneven shape: {self.image_bgr.shape}"

    @property
    def device_path(self) -> Path:
        return self._device_path

    @property
    def camera_id(self) -> str:
        return self._camera_id

    @property
    def ppi(self) -> Optional[float]:
        return self._ppi

    @property
    def image_bgr(self) -> npt.NDArray[Any]:
        """
        :return: the 3D blue-green-red image, HxWxC
        """
        return self._image_bgr

    @property
    def height(self) -> int:
        return self.image_bgr.shape[0]

    @property
    def width(self) -> int:
        return self.image_bgr.shape[1]

    @property
    def center_xy(self) -> Tuple[int, int]:
        """
        :return: the (x_mid, y_mid) center point as a pair of ints.
        """
        return int(self.width / 2), int(self.height / 2)

    @property
    def corners(self) -> npt.NDArray[Any]:
        """
        [
           [0, 0],
           [x, 0],
           [x, y],
           [0, y]
        ]
        """
        return np.array([[0, 0], [self.width, 0], [self.width, self.height], [0, self.height]])

    @property
    def depth(self) -> Optional[npt.NDArray[Any]]:
        """
        :return: grayscale depth image
        """
        return self._depth

    def compute_focus(self) -> float:
        if self._focus_value is not None:
            return self._focus_value
        return cast(float, cv2.Laplacian(self.image_bgr, cv2.CV_32F).var() / max(np.mean(self.image_bgr), 1.0))

    def copy(self) -> "CamImage":
        return CamImage(
            device_path=self._device_path,
            camera_id=self._camera_id,
            timestamp_ms=self.timestamp_ms,
            ppi=self._ppi,
            image_bgr=self._image_bgr.copy(),
            depth=self._depth.copy() if self._depth is not None else None,
            focus_value=self._focus_value,
        )

    def get_resized_cam_image(self, new_size_xy: Tuple[int, int]) -> "CamImage":
        assert new_size_xy[0] % 2 == 0 and new_size_xy[1] % 2 == 0, f"New dimensions must be even, got {new_size_xy}"

        resized_image_bgr = resize_image(self._image_bgr, new_size_xy)
        resized_depth = None
        if self._depth is not None:
            resized_depth = resize_image(self._depth, new_size_xy)

        delta_height_ratio = resized_image_bgr.shape[0] / self._image_bgr.shape[0]
        delta_width_ratio = resized_image_bgr.shape[1] / self._image_bgr.shape[1]
        avg_delta = (delta_height_ratio + delta_width_ratio) / 2
        new_ppi = avg_delta * self.ppi if self.ppi is not None else None

        return CamImage(
            device_path=self._device_path,
            camera_id=self._camera_id,
            timestamp_ms=self.timestamp_ms,
            ppi=new_ppi,
            image_bgr=resized_image_bgr,
            depth=resized_depth,
            focus_value=self._focus_value,
        )

    def to_proto(self, buffer_type: BufferType = BufferType.INLINE) -> CamImageProto:
        with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.image_bgr.shape}", "CamToBytes"):
            image_data = bytes(self.image_bgr)
        with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.image_bgr.shape}", "CamImageProto"):
            return CamImageProto(
                device_path=str(self.device_path),
                camera_id=self.camera_id,
                timestamp_ms=self.timestamp_ms,
                ppi=self._ppi,
                size=Size(height=self.height, width=self.width),
                bytes=Buffer.to_proto(image_data, buffer_type),
                depth_bytes=Buffer.to_proto(bytes(self.depth), buffer_type) if self.depth is not None else None,
            )

    def release_export(self) -> None:
        pass

    @staticmethod
    def get_buffer_type(proto: CamImageProto) -> BufferType:
        return Buffer.get_type(proto.bytes)

    @staticmethod
    def release(proto: CamImageProto) -> None:
        Buffer.release(proto.bytes)
        Buffer.release(proto.depth_bytes)

    @staticmethod
    def from_proto(proto: CamImageProto) -> "CamImage":
        # parse identifiers
        device_path: Path = Path(proto.device_path)
        camera_id: str = proto.camera_id

        # parse timestamp
        timestamp_ms: int = proto.timestamp_ms

        # parse ppi
        ppi: Optional[float] = proto.ppi if proto.ppi != 0 else None

        # parse size
        size_hw: Tuple[int, int] = (proto.size.height, proto.size.width)

        # parse image
        buf = Buffer.from_proto(proto.bytes)

        # parse depth
        depth: Optional[Union[npt.NDArray[Any], ImageBuffer]] = None
        if not Buffer.is_empty(proto.depth_bytes):
            buf_proto = Buffer.from_proto(proto.depth_bytes)
            if isinstance(buf_proto, ImageBuffer):
                depth = buf_proto
            else:
                depth = np.frombuffer(cast(bytes, Buffer.from_proto(proto.depth_bytes)), dtype=np.uint8).reshape(
                    size_hw
                )

        # ready to go
        if isinstance(buf, ImageBuffer) and (depth is None or isinstance(depth, ImageBuffer)):
            cam_image: CamImage = BufferedCamImage(
                device_path=device_path,
                camera_id=camera_id,
                timestamp_ms=timestamp_ms,
                ppi=ppi,
                buf=buf,
                depth_buf=depth,
            )
        else:
            assert depth is None or isinstance(depth, np.ndarray)
            img: npt.NDArray[Any] = np.frombuffer(cast(bytes, buf), dtype=np.uint8).reshape(size_hw + (3,))
            cam_image = CamImage(
                device_path=device_path,
                camera_id=camera_id,
                timestamp_ms=timestamp_ms,
                ppi=ppi,
                image_bgr=img,
                depth=depth,
            )
        return cam_image

    @staticmethod
    def dummy(
        *,
        ppi: Optional[float] = None,
        image_bgr: npt.NDArray[Any],
        depth: Optional[npt.NDArray[Any]] = None,
        timestamp_ms: int = 0,
        camera_id: str = "dummy",
        device_path: Path = Path("/retina/camera:dummy"),
    ) -> "CamImage":

        return CamImage(
            device_path=device_path,
            camera_id=camera_id,
            timestamp_ms=timestamp_ms,
            ppi=ppi,
            image_bgr=image_bgr,
            depth=depth,
        )


def cam_image_depth_save(filename: str, cam_image: CamImage) -> None:
    if cam_image.depth is not None:
        np.savez_compressed(
            filename, view_image=cv2.cvtColor(cam_image.image_bgr, cv2.COLOR_BGR2RGB), view_depth=cam_image.depth,
        )


class BufferedCamImage(CamImage):
    """
    A timestamped image inside a shared memory buffer
    """

    def __init__(
        self,
        *,
        device_path: Path,
        camera_id: str,
        timestamp_ms: int,
        ppi: Optional[float],
        buf: ImageBuffer,
        depth_buf: Optional[ImageBuffer],
    ):
        super().__init__(
            device_path=device_path,
            camera_id=camera_id,
            timestamp_ms=timestamp_ms,
            ppi=ppi,
            image_bgr=buf.get_numpy_array(buf),
            depth=depth_buf.get_numpy_array(depth_buf).squeeze(-1) if depth_buf is not None else None,
        )
        self._buf = buf
        self._depth_buf = depth_buf

    def release_export(self) -> None:
        self._buf.release_path(self._buf.get_name())
        if self._depth_buf is not None:
            self._depth_buf.release_path(self._depth_buf.get_name())

    @property
    def buf(self) -> ImageBuffer:
        return self._buf

    @property
    def depth_buf(self) -> ImageBuffer:
        assert self._depth_buf is not None
        return self._depth_buf

    def to_proto(self, buffer_type: BufferType = BufferType.SHMEM) -> CamImageProto:
        with duration_perf_recorder(f"{PerfCategory.IMAGE}:{self.image_bgr.shape}", "ShmemCamImageProto"):
            return CamImageProto(
                device_path=str(self.device_path),
                camera_id=self.camera_id,
                timestamp_ms=self.timestamp_ms,
                ppi=self._ppi,
                size=Size(height=self.height, width=self.width),
                bytes=Buffer.to_proto(self._buf, BufferType.SHMEM),
                depth_bytes=Buffer.to_proto(self._depth_buf, BufferType.SHMEM) if self._depth_buf is not None else None,
            )

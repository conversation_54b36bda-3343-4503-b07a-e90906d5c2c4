#pragma once

#include <filesystem>
#include <fmt/format.h>
#include <fmt/ostream.h>
#include <mutex>
#include <opencv2/opencv.hpp>
#include <queue>
#include <spdlog/spdlog.h>
#include <torch/torch.h>

#include "lib/common/cpp/utils.h"

namespace lib {
namespace common {
namespace image {

class AsyncImageIO {
public:
  AsyncImageIO() : AsyncImageIO(1) {}
  AsyncImageIO(int num_workers) : shutdown_(false) {
    for (int i = 0; i < num_workers; i++) {
      worker_threads_.push_back(std::thread(std::bind(&AsyncImageIO::worker_loop, this)));
    }
  }

  DELETE_COPY_AND_MOVE(AsyncImageIO)

  ~AsyncImageIO() {
    shutdown_ = true;
    queue_push_.notify_all();
    for (auto &thread : worker_threads_) {
      thread.join();
    }
    worker_threads_.clear();
  }

  void save(const std::filesystem::path &path, std::function<torch::Tensor()> image_producer) {
    {
      std::unique_lock<std::mutex> lck(queue_lock_);
      task_queue_.push({path, image_producer});
    }
    queue_push_.notify_one();
  }

private:
  void worker_loop() {
    while (!shutdown_) {
      std::function<torch::Tensor()> image_producer;
      std::filesystem::path path;
      {
        std::unique_lock<std::mutex> lck(queue_lock_);
        queue_push_.wait(lck, [&]() { return shutdown_ || !task_queue_.empty(); });
        if (task_queue_.empty()) {
          return;
        }
        std::tie(path, image_producer) = task_queue_.front();
        task_queue_.pop();
      }

      try {
        if (!path.parent_path().empty()) {
          std::filesystem::create_directories(path.parent_path());
        }

        torch::Tensor image = image_producer();

        if (image.ndimension() != 3) {
          spdlog::warn(fmt::format("Images must have 3 dimensions. Found {} dimensions. Unable to save {}",
                                   image.ndimension(), path));
          continue;
        }

        // Assure tensor data is contiguous since we will be passing its pointer to opencv
        image = image.contiguous().clone();

        const int data_type = CV_8UC((int)image.size(2));
        cv::Mat image_opencv((int)image.size(0), (int)image.size(1), data_type, image.data_ptr());
        cv::cvtColor(image_opencv, image_opencv, cv::COLOR_RGB2BGR);
        if (!cv::imwrite(path, image_opencv)) {
          spdlog::warn(fmt::format("Failed to save {}", path.string()));
          continue;
        }
      } catch (const std::exception &ex) {
        spdlog::warn(fmt::format("Failed to save {}. {}", path.string(), ex.what()));
      }
    }
  }

  bool shutdown_;
  std::mutex queue_lock_;
  std::condition_variable queue_push_;
  std::queue<std::tuple<std::filesystem::path, std::function<torch::Tensor()>>> task_queue_;
  std::vector<std::thread> worker_threads_;
};

} // namespace image

} // namespace common

} // namespace lib

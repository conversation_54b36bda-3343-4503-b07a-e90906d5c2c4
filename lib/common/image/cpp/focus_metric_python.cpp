#ifdef <PERSON>
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <torch/extension.h>
#include <torch/torch.h>
namespace py = pybind11;
#endif

#include "focus_metric.h"

namespace lib {
namespace common {
namespace image {

#ifdef PYBIND

PYBIND11_MODULE(focus_metric_python, m) {
  py::class_<FocusMetric, std::shared_ptr<FocusMetric>>(m, "FocusMetric")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def("compute", &FocusMetric::compute, py::arg("cam_image"), py::call_guard<py::gil_scoped_release>());

  py::class_<FrequencyBasedFocusMetric, std::shared_ptr<FrequencyBasedFocusMetric>>(m, "FrequencyBasedFocusMetric")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def("compute", &FrequencyBasedFocusMetric::compute, py::arg("cam_image"), py::arg("sample_frequency") = 0.5f,
           py::call_guard<py::gil_scoped_release>());
}

#endif

} // namespace image
} // namespace common
} // namespace lib
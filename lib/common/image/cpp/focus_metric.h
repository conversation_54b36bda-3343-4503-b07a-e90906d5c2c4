#pragma once

#include "lib/common/camera/cpp/camera.h"

namespace lib {
namespace common {
namespace image {
class FocusMetric {
public:
  FocusMetric();

  float compute(torch::Tensor cam_image_tensor);

private:
  const int kGaussianBlurRadius = 3;
  const float kGaussianBlurSigma = 0.8f;

  torch::Tensor gaussian_weights_;
  torch::Tensor laplacian_weights_;
};

class FrequencyBasedFocusMetric {
public:
  FrequencyBasedFocusMetric();

  float compute(torch::Tensor cam_image_tensor, float sample_frequency = 0.5f);

private:
  // Helper function to compute radial distances from center
  torch::Tensor compute_radial_distances(int height, int width, torch::Devi<PERSON> device);

  // Helper function to compute radial sums (equivalent to np.bincount)
  std::pair<torch::Tensor, torch::Tensor> compute_radial_sums(torch::Tensor fft_spectrum,
                                                              torch::Tensor radial_distances, int max_radius);
};

} // namespace image
} // namespace common
} // namespace lib

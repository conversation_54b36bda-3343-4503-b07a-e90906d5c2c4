#pragma once

#include <vector>

#include <opencv2/core.hpp>

namespace lib {
namespace common {
namespace image {

cv::Rect tile_rect(cv::Mat image, float x0, float y0, float x1, float y1) {
  auto width = image.size().width;
  auto height = image.size().height;
  return cv::Rect(width * x0 / 100, height * y0 / 100, width * (x1 - x0) / 100, height * (y1 - y0) / 100);
}

cv::Mat tiled_crops(cv::Mat image) {
  // Crop image as follows:
  //       5%      10%     5%
  //     +-----+---------+-----+
  // 5%  |,25% |   .5%   |,25% |
  //     +-----+---------+-----+
  //     |     |         |     |
  // 10% | .5% |    1%   | .5% |
  //     |     |         |     |
  //     +-----+---------+-----+
  // 5%  |,25% |   .5%   |,25% |
  //     +-----+---------+-----+

  cv::Mat x00 = image(tile_rect(image, 0, 0, 5, 5));
  cv::Mat x10 = image(tile_rect(image, 45, 0, 55, 5));
  cv::Mat x20 = image(tile_rect(image, 95, 0, 100, 5));
  cv::Mat x01 = image(tile_rect(image, 0, 45, 5, 55));
  cv::Mat x11 = image(tile_rect(image, 45, 45, 55, 55));
  cv::Mat x21 = image(tile_rect(image, 95, 45, 100, 55));
  cv::Mat x02 = image(tile_rect(image, 0, 95, 5, 100));
  cv::Mat x12 = image(tile_rect(image, 45, 95, 55, 100));
  cv::Mat x22 = image(tile_rect(image, 95, 95, 100, 100));

  cv::Mat x_0, x_1, x_2;
  cv::hconcat(std::vector<cv::Mat>{x00, x10, x20}, x_0);
  cv::hconcat(std::vector<cv::Mat>{x01, x11, x21}, x_1);
  cv::hconcat(std::vector<cv::Mat>{x02, x12, x22}, x_2);

  cv::Mat result;
  cv::vconcat(std::vector<cv::Mat>{x_0, x_1, x_2}, result);

  return result;
}

} // namespace image
} // namespace common
} // namespace lib

#include "focus_metric.h"

#include "lib/common/cpp/cuda_util.h"

#include <c10/cuda/CUDAStream.h>

namespace lib {
namespace common {
namespace image {

FocusMetric::FocusMetric() {
  gaussian_weights_ = torch::zeros({kGaussianBlurRadius, kGaussianBlurRadius});
  for (int y = 0; y < gaussian_weights_.size(0); y++) {
    for (int x = 0; x < gaussian_weights_.size(1); x++) {
      const float exp_component = (float)((pow((float)x - ((float)gaussian_weights_.size(1) - 1.0f) / 2.0f, 2) +
                                           pow((float)y - ((float)gaussian_weights_.size(0) - 1.0f) / 2.0f, 2)) /
                                          (2.0f * pow(kGaussianBlurSigma, 2)));
      gaussian_weights_[y][x] = exp(-exp_component);
    }
  }
  gaussian_weights_ = (gaussian_weights_.unsqueeze(0).unsqueeze(0) / gaussian_weights_.sum()).repeat({3, 1, 1, 1});

  laplacian_weights_ = torch::zeros({3, 3});
  laplacian_weights_[0][1] = 1.0f;
  laplacian_weights_[1][0] = 1.0f;
  laplacian_weights_[1][1] = -4.0f;
  laplacian_weights_[1][2] = 1.0f;
  laplacian_weights_[2][1] = 1.0f;
  laplacian_weights_ = (laplacian_weights_.unsqueeze(0).unsqueeze(0)).repeat({3, 1, 1, 1});
}

float FocusMetric::compute(torch::Tensor cam_image_tensor) {
  gaussian_weights_ = gaussian_weights_.to(cam_image_tensor.device());
  laplacian_weights_ = laplacian_weights_.to(cam_image_tensor.device());

  cam_image_tensor = cam_image_tensor.unsqueeze(0).to(torch::kF32);

  // gaussian blur
  auto cam_image_tensor_blur = torch::nn::functional::conv2d(cam_image_tensor, gaussian_weights_,
                                                             F::Conv2dFuncOptions().groups(3).padding(torch::kSame));

  // laplacian
  auto laplacian_tensor = torch::nn::functional::conv2d(cam_image_tensor_blur, laplacian_weights_,
                                                        F::Conv2dFuncOptions().groups(3).padding(torch::kSame));

  return (float)torch::var(laplacian_tensor).item().to<float>() /
         std::max(cam_image_tensor.mean().item().to<float>(), 1.0f);
}

FrequencyBasedFocusMetric::FrequencyBasedFocusMetric() {}

torch::Tensor FrequencyBasedFocusMetric::compute_radial_distances(int height, int width, torch::Device device) {
  auto y_coords = torch::arange(height, torch::TensorOptions().dtype(torch::kFloat32).device(device));
  auto x_coords = torch::arange(width, torch::TensorOptions().dtype(torch::kFloat32).device(device));

  auto y_grid = y_coords.unsqueeze(1).expand({height, width});
  auto x_grid = x_coords.unsqueeze(0).expand({height, width});

  float cy = (float)(height - 1) / 2.0f;
  float cx = (float)(width - 1) / 2.0f;

  auto r = torch::sqrt(torch::pow(x_grid - cx, 2) + torch::pow(y_grid - cy, 2));

  return r.to(torch::kInt32);
}

std::pair<torch::Tensor, torch::Tensor> FrequencyBasedFocusMetric::compute_radial_sums(torch::Tensor fft_spectrum,
                                                                                       torch::Tensor radial_distances,
                                                                                       int max_radius) {

  auto fft_flat = fft_spectrum.flatten();
  auto r_flat = radial_distances.flatten();

  auto valid_mask = (r_flat >= 0) & (r_flat < max_radius);

  auto valid_radii = r_flat.masked_select(valid_mask);
  auto valid_values = fft_flat.masked_select(valid_mask);

  auto radial_sum =
      torch::zeros({max_radius}, torch::TensorOptions().dtype(torch::kFloat32).device(fft_spectrum.device()));
  auto radial_count =
      torch::zeros({max_radius}, torch::TensorOptions().dtype(torch::kFloat32).device(fft_spectrum.device()));

  radial_sum.scatter_add_(0, valid_radii.to(torch::kLong), valid_values);
  radial_count.scatter_add_(0, valid_radii.to(torch::kLong), torch::ones_like(valid_values));

  return std::make_pair(radial_sum, radial_count);
}

float FrequencyBasedFocusMetric::compute(torch::Tensor cam_image_tensor, float sample_frequency) {
  cam_image_tensor = cam_image_tensor.to(torch::kF32);

  if (cam_image_tensor.dim() == 3 && cam_image_tensor.size(0) == 3) {
    cam_image_tensor = cam_image_tensor.mean(0);
  }

  int height = static_cast<int>(cam_image_tensor.size(-2));
  int width = static_cast<int>(cam_image_tensor.size(-1));

  auto fft_input = cam_image_tensor.unsqueeze(0).unsqueeze(0);
  auto fft_result = torch::fft::rfft2(fft_input);
  auto fft_magnitude = torch::abs(fft_result).squeeze();

  fft_magnitude = torch::fft::fftshift(fft_magnitude, {0, 1});

  auto radial_distances = compute_radial_distances(height, width / 2 + 1, cam_image_tensor.device());
  int max_radius = static_cast<int>(std::sqrt(height * height + width * width) / 2.0f) + 1;
  auto [radial_sum, radial_count] = compute_radial_sums(fft_magnitude, radial_distances, max_radius);
  auto radial_avg = torch::where(radial_count > 0, radial_sum / radial_count, torch::zeros_like(radial_sum));

  int truncate_length = std::min(width / 2, height / 2);
  radial_avg = radial_avg.slice(0, 0, truncate_length);

  auto mean_val = radial_avg.mean();
  radial_avg = radial_avg / torch::clamp_min(mean_val, 1e-8f); // Avoid division by zero

  auto x_values = torch::arange(radial_avg.size(0),
                                torch::TensorOptions().dtype(torch::kFloat32).device(cam_image_tensor.device()));
  x_values = x_values / static_cast<float>(radial_avg.size(0));

  auto freq_diff = torch::abs(x_values - sample_frequency);
  auto min_index = torch::argmin(freq_diff).item<int>();

  float focus_value = radial_avg[min_index].item<float>();

  return focus_value;
}
} // namespace image

} // namespace common
} // namespace lib

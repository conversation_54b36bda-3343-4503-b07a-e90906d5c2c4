add_library(focus_metric SHARED focus_metric.cpp)
target_compile_definitions(focus_metric PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(focus_metric PUBLIC torch opencv_core opencv_calib3d opencv_imgproc
opencv_cudaimgproc opencv_imgcodecs opencv_allocator spdlog simulator_proto fmt)
target_include_directories(focus_metric SYSTEM PUBLIC /usr/local/include/opencv4 ${nanoflann_DIR})

pybind11_add_module(focus_metric_python SHARED focus_metric_python.cpp)
target_link_libraries(focus_metric_python PUBLIC focus_metric torch_python)
target_compile_definitions(focus_metric_python PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
set_target_properties(focus_metric_python  PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})
target_include_directories(focus_metric_python SYSTEM PUBLIC /usr/local/include/opencv4)

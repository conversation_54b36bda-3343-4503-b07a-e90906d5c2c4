import threading
import time
from typing import Dict, List, Optional, cast

from yappi import YThreadStat

import lib.common.logging
from lib.common.tasks.manager import get_task_by_tid

try:
    import gil_load
except ImportError:
    pass

LOG = lib.common.logging.get_logger(__name__)


def gil_load_profiling(seconds: int) -> str:
    try:
        gil_load.start()
        time.sleep(seconds)
        gil_load.stop()
    except RuntimeError as ex:
        return f"It is likely that gil-load is not enabled in botd.py: \n {ex}\n"

    def _task_name(thread_name_dict: Dict[str, str], tid: str) -> str:
        task = get_task_by_tid(str(tid))
        if task is not None:
            return task.name
        if tid in thread_name_dict:
            thread_name = thread_name_dict[tid]
            if "WeedTracking" in thread_name:
                return thread_name
        return "Unknown Thread"

    total_stats: Dict[str, float] = {}
    thread_stats: Dict[str, Dict[str, float]] = {}

    total_stats, thread_stats = gil_load.get()
    out = f"""
    Total Stats:
    Held: {total_stats["held"]}
    Wait: {total_stats["wait"]}
    """

    class ThreadStat:
        def __init__(self, name: str, held: float, wait: float):
            self.name = name
            self.held = held
            self.wait = wait

    # List[List[str, float, float]]
    thread_name_dict = {str(t.ident): t.name for t in threading.enumerate()}
    thread_stats_list: List[ThreadStat] = [
        ThreadStat(_task_name(thread_name_dict, k), v["held"], v["wait"]) for k, v in thread_stats.items()
    ]

    named_thread_list: List[ThreadStat] = [t for t in thread_stats_list if t.name != "Unknown Thread"]
    unknown = ThreadStat("Unknown", 0, 0)
    count = 0
    for tstat in thread_stats_list:
        if tstat.name != "Unknown Thread":
            continue
        count += 1
        unknown.held += tstat.held
        unknown.wait += tstat.wait
    unknown.name = f"Unknown Count {count}"

    named_thread_list.append(unknown)
    named_thread_list.sort(key=lambda x: x.held, reverse=True)
    for tstat in named_thread_list:
        out += f"\n{tstat.name}: H: {tstat.held}, W: {tstat.wait}"
    out += "\n"
    return out


def yappi_profile_text_output_async(seconds: int, outfile: Optional[str]) -> str:
    """
    Use yappi to generate multi-thread profiling data. Optionally takes a filename to write to for callgrind.
    """
    import yappi

    yappi.clear_stats()
    LOG.info("Starting yappi...")
    yappi.start()
    time.sleep(seconds)
    yappi.stop()
    LOG.info("Stopped yappi.")

    stats = yappi.get_func_stats()
    ttot_stats_list = sorted([(stat.ttot, stat.ncall, stat.name) for stat in stats])
    ncall_stats_list = sorted([(stat.ncall, stat.ttot, stat.name) for stat in stats])

    t_stats = yappi.get_thread_stats()

    def _task_name(stat: YThreadStat) -> str:
        task = get_task_by_tid(str(stat.tid))
        return task.name if task is not None else cast(str, stat.name)

    t_stats_time = sorted(([(stat.ttot, stat.sched_count, _task_name(stat),) for stat in t_stats]))

    if outfile is not None:
        LOG.info("Saving callgrind to {}".format(outfile))
        stats.save(outfile, type="callgrind")

    return (
        "By Total Time:\n"
        + "\n".join([str(s) for s in ttot_stats_list])
        + "\n\nBy Number Calls:\n"
        + "\n".join([str(s) for s in ncall_stats_list])
        + "\n\nBy Thread:\n"
        + "\n".join([str(s) for s in t_stats_time])
        + "\n"
    )

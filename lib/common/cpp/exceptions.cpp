#include "exceptions.h"

#include <fmt/format.h>

maka_error::maka_error(const std::string &message, bool include_stacktrace, size_t stack_skip) {
  std::stringstream ss;
  ss << message;
  ss << "\n";
  if (include_stacktrace) {
    ss << boost::stacktrace::stacktrace(stack_skip, 128);
  }
  ex_str_ = ss.str();
}
maka_error::~maka_error() {}

const char *maka_error::what() const noexcept { return ex_str_.c_str(); }

std::string exception_to_string(const std::exception &e, int level) {
  const std::string exception_str = fmt::format(std::string(level, ' ') + "exception: {}\n", e.what());
  try {
    std::rethrow_if_nested(e);
  } catch (const std::exception &nested_exception) {
    return exception_str + exception_to_string(nested_exception, level + 1);
  }
  return exception_str;
}
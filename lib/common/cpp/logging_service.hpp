#pragma once
#include "generated/proto/logging/logging.grpc.pb.h"

#include <thread>

namespace lib {
namespace common {
namespace logging {
class LoggingServiceImpl final : public carbon::logging::LoggingService::Service {
public:
  LoggingServiceImpl(std::string filename);
  ~LoggingServiceImpl(){};
  ::grpc::Status SetLevel(::grpc::ServerContext *context, const ::carbon::logging::SetLevelRequest *request,
                          ::carbon::logging::Empty *response) override;
};
} // namespace logging
} // namespace common
} // namespace lib
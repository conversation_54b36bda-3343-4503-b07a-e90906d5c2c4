add_library(exceptions SHARED exceptions.cpp)
target_link_libraries(exceptions PUBLIC dl backtrace fmt)

add_library(opencv_allocator SHARED opencv_allocator.cpp)
target_link_libraries(opencv_allocator PU<PERSON><PERSON> opencv_core torch)
target_include_directories(opencv_allocator SYSTEM PUBLIC /usr/local/include/opencv4)

file(GLOB UTIL_SOURCES CONFIGURE_DEPENDS utils/*.cpp utils/*.hpp)
add_library(utils SHARED ${UTIL_SOURCES})
target_compile_definitions(utils PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(utils PUBLIC fmt bot_stop)

add_library(logging_service SHARED logging_service.hpp logging_service.cpp)
target_compile_definitions(logging_service PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(logging_service PUBLIC pthread gRPC::grpc++ logging_proto logging)

add_library(interest_scores SHARED interest_scores.hpp interest_scores.cpp)
target_compile_definitions(interest_scores PUBLIC -DSPDLOG_FMT_EXTERNAL=1)

add_subdirectory(tests)

pybind11_add_module(geo_data_python SHARED geo_data_python.cpp)
target_compile_options(geo_data_python PRIVATE -fvisibility=default)
set_target_properties(geo_data_python PROPERTIES OUTPUT_NAME geo_data_python.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

add_executable(min_max_heap_test ../min_max_heap.h min_max_heap_test.cpp)
target_compile_definitions(min_max_heap_test PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(min_max_heap_test gtest_main exceptions spdlog fmt)

gtest_discover_tests(min_max_heap_test DISCOVERY_TIMEOUT 10)


add_executable(interest_scores_test ../interest_scores.hpp ../interest_scores.cpp interest_scores_test.cpp)
target_compile_definitions(interest_scores_test PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_link_directories(interest_scores_test PUBLIC /usr/local/lib /usr/local/cuda/lib64/ /opt/hpcx/ompi/lib/)
target_link_libraries(interest_scores_test gtest_main exceptions spdlog fmt simulator_proto cv_runtime_proto weed_tracking_client ${TORCH_LIBRARIES})
target_include_directories(interest_scores_test SYSTEM PUBLIC ${CV_RUNTIME_INCLUDES})

gtest_discover_tests(interest_scores_test DISCOVERY_TIMEOUT 10)

add_executable(fixed_buffer_test ../fixed_buffer.h fixed_buffer_test.cpp)
target_link_libraries(fixed_buffer_test gtest_main exceptions fmt)
target_include_directories(fixed_buffer_test SYSTEM PUBLIC)

gtest_discover_tests(fixed_buffer_test DISCOVERY_TIMEOUT 10)
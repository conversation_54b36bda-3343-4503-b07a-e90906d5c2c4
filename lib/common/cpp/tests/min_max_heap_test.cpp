#include "lib/common/cpp/min_max_heap.h"
#include "gtest/gtest.h"

TEST(MinMaxHeap, PopMax) {
  lib::common::MinMaxHeap<int> min_max_heap;

  std::vector<int> elements{22, 93, 1, 23, 84, 92, 56, 89, 6, 10, 9, 76};

  for (auto item : elements) {
    min_max_heap.push(item);
  }

  sort(elements.begin(), elements.end(), std::greater<int>());

  for (size_t i = 0; i < 12; i++) {
    auto item = min_max_heap.pop_max();
    ASSERT_EQ(item, elements[i]);
  }
}

TEST(MinMaxHeap, PopMaxSizeOne) {
  lib::common::MinMaxHeap<int> min_max_heap;

  min_max_heap.push(22);

  auto item = min_max_heap.pop_max();

  ASSERT_EQ(item, 22);
  ASSERT_EQ(min_max_heap.size(), 0);
}

TEST(MinMaxHeap, PopMin) {
  lib::common::MinMaxHeap<int> min_max_heap;

  std::vector<int> elements{22, 93, 1, 23, 84, 92, 56, 89, 6, 10, 9, 76};

  for (auto item : elements) {
    min_max_heap.push(item);
  }

  sort(elements.begin(), elements.end());

  for (size_t i = 0; i < 12; i++) {
    auto item = min_max_heap.pop_min();
    ASSERT_EQ(item, elements[i]);
  }
}

TEST(MinMaxHeap, PeekMaxMin) {
  lib::common::MinMaxHeap<int> min_max_heap;

  std::vector<int> elements{22, 93, 1, 23, 84, 92, 56, 89, 6, 10, 9, 76};

  for (auto item : elements) {
    min_max_heap.push(item);
  }

  ASSERT_EQ(min_max_heap.peek_max(), 93);
  ASSERT_EQ(min_max_heap.peek_min(), 1);
}

TEST(MinMaxHeap, Clear) {
  lib::common::MinMaxHeap<int> min_max_heap;

  std::vector<int> elements{22, 93, 1, 23, 84, 92, 56, 89, 6, 10, 9, 76};

  for (auto item : elements) {
    min_max_heap.push(item);
  }

  ASSERT_EQ(min_max_heap.size(), 12);
  min_max_heap.clear();
  ASSERT_EQ(min_max_heap.size(), 0);
}
#include "generated/cv/runtime/proto/cv_runtime.pb.h"
#include "lib/common/cpp/interest_scores.hpp"
#include "gtest/gtest.h"

TEST(InterestScores, AmbiguityInterestScore) {
  std::vector<std::string> embedding_categories{"broadleaf", "offshoot", "purslane", "grass", "crop"};
  std::vector<float> embedding_scores{-0.8f, -0.82f, -0.6f, -0.3f, 0.5f};

  const auto [ambiguity_score, category_1, category_2] =
      lib::common::ambiguity_interest_score(embedding_scores, embedding_categories);

  ASSERT_NEAR(ambiguity_score, 50.f, 1e-3);
  ASSERT_EQ(category_1, "offshoot");
  ASSERT_EQ(category_2, "broadleaf");
}

TEST(InterestScores, AmbiguityInterestScoreEven) {
  std::vector<std::string> embedding_categories{"broadleaf", "crop"};
  std::vector<float> embedding_scores{-0.8f, -0.8};

  const auto [ambiguity_score, category_1, category_2] =
      lib::common::ambiguity_interest_score(embedding_scores, embedding_categories);

  ASSERT_NEAR(ambiguity_score, std::numeric_limits<float>::max(), 1e-3);
  ASSERT_EQ(category_1, "broadleaf");
  ASSERT_EQ(category_2, "crop");
}

TEST(InterestScores, WeakInterestScore) {
  std::vector<std::string> embedding_categories{"broadleaf", "offshoot", "purslane", "grass", "crop"};
  std::vector<float> embedding_scores{-0.5f, -0.62f, -0.6f, -0.3f, 0.5f};

  const auto [ambiguity_score, category] = lib::common::weak_interest_score(embedding_scores, embedding_categories);

  ASSERT_FLOAT_EQ(ambiguity_score, -0.62f);
  ASSERT_EQ(category, "offshoot");
}

TEST(InterestScores, DisagreementInterestScore) {
  {
    std::vector<std::string> embedding_categories{"broadleaf", "offshoot", "purslane", "grass", "crop"};
    std::vector<float> embedding_scores{-0.5f, -0.62f, -0.6f, -0.3f, 0.5f};

    const auto [disagreement_score, category] =
        lib::common::disagreement_interest_score(embedding_scores, 0.1f, 0.9f, embedding_categories);

    ASSERT_FLOAT_EQ(disagreement_score, 0.62f);
    ASSERT_EQ(category, "offshoot");
  }

  {
    std::vector<std::string> embedding_categories{"BROADLEAF", "OFFSHOOT", "PURSLANE", "GRASS", "CROP"};
    std::vector<float> embedding_scores{0.5f, 0.62f, 0.6f, -0.3f, -0.522f};

    const auto [disagreement_score, category] =
        lib::common::disagreement_interest_score(embedding_scores, 0.9f, 0.1f, embedding_categories);

    ASSERT_FLOAT_EQ(disagreement_score, 0.522f);
    ASSERT_EQ(category, "CROP");
  }

  {
    std::vector<std::string> embedding_categories{"broadleaf", "offshoot", "purslane", "grass", "crop"};
    std::vector<float> embedding_scores{-0.5f, -0.62f, -0.6f, -0.3f, 0.5f};

    const auto [disagreement_score, category] =
        lib::common::disagreement_interest_score(embedding_scores, 0.9f, 0.1f, embedding_categories);

    ASSERT_NEAR(disagreement_score, 0.062f, 1e-3);
    ASSERT_EQ(category, "offshoot");
  }
}

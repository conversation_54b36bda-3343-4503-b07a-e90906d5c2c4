#pragma once
#include <map>
#include <mutex>
#include <optional>

namespace lib {
namespace common {

class BedtopProfile {
public:
  BedtopProfile() {}

  void update(std::map<std::string, std::map<std::string, std::vector<double>>> height_profiles, double bbh_offset_mm) {
    std::lock_guard<std::mutex> lk(this->mutex_);
    height_profiles_ = height_profiles;
    bbh_offset_mm_ = bbh_offset_mm;
  }

  std::tuple<std::optional<std::map<std::string, std::vector<double>>>, double> retrieve(std::string pcam_id) {
    std::lock_guard<std::mutex> lk(this->mutex_);
    if (height_profiles_.count(pcam_id) == 0) {
      return std::make_tuple(std::nullopt, bbh_offset_mm_);
    }

    return std::make_tuple(height_profiles_[pcam_id], bbh_offset_mm_);
  }

private:
  // {pcam_id: {'weed': [], 'crop': []}}
  std::map<std::string, std::map<std::string, std::vector<double>>> height_profiles_;
  double bbh_offset_mm_;
  std::mutex mutex_;
};

} // namespace common
} // namespace lib
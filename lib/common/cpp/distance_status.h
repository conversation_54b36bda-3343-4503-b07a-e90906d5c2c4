#pragma once
#include <shared_mutex>
#include <tuple>

namespace lib {
namespace common {

class DistanceStatus {
public:
  DistanceStatus(double distance) : distance_(distance) {}

  void update(double distance) {
    std::unique_lock<std::shared_mutex> lk(this->mutex_);
    distance_ = distance;
  }

  double retrieve() {
    std::shared_lock<std::shared_mutex> lk(this->mutex_);
    return distance_;
  }

private:
  double distance_;
  std::shared_mutex mutex_;
};

} // namespace common
} // namespace lib

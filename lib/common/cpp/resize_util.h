#pragma once

#include <glm/glm.hpp>
#include <torch/torch.h>

namespace F = torch::nn::functional;

namespace lib {
namespace common {
inline torch::Tensor interpolate(torch::Tensor input, glm::ivec2 size) {
  auto input_dtype = input.dtype();
  F::InterpolateFuncOptions options;
  options = options.size(std::vector<int64_t>{static_cast<int64_t>(size.y), static_cast<int64_t>(size.x)});

  if (size.x < input.size(-1) && size.y < input.size(-2)) {
    options = options.mode(torch::kArea);
  } else {
    options = options.mode(torch::kBilinear).align_corners(false);
  }
  return F::interpolate(input.to(torch::kFloat), options).to(input_dtype);
}
} // namespace common
} // namespace lib

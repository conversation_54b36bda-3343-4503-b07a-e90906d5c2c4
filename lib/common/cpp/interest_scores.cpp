#include "interest_scores.hpp"
#include <algorithm>
#include <cctype>
#include <climits>
#include <limits>
#include <random>
#include <string>
#include <tuple>
#include <vector>

namespace lib {
namespace common {
std::tuple<float, std::string, std::string>
ambiguity_interest_score(const std::vector<float> &embedding_scores,
                         const std::vector<std::string> &embedding_categories) {
  float score = 0.0f;
  if (embedding_scores.size() <= 1) {
    return std::tie(score, "", "");
  }

  int lowest_index = 0;
  int second_lowest_index = -1;
  float lowest_value = embedding_scores[0];
  float second_lowest_value = std::numeric_limits<float>::max();

  for (size_t i = 1; i < embedding_scores.size(); i++) {
    if (embedding_scores[i] < lowest_value) {
      second_lowest_value = lowest_value;
      second_lowest_index = lowest_index;
      lowest_value = embedding_scores[i];
      lowest_index = (int)i;
    } else if (embedding_scores[i] <= second_lowest_value) {
      second_lowest_value = embedding_scores[i];
      second_lowest_index = (int)i;
    }
  }

  score = (second_lowest_value - lowest_value) > 0 ? (float)(1.0 / (second_lowest_value - lowest_value))
                                                   : std::numeric_limits<float>::max();
  ;
  auto nearest_category = embedding_categories[lowest_index];
  auto second_nearest_category = embedding_categories[second_lowest_index];

  return std::tie(score, nearest_category, second_nearest_category);
}

std::tuple<float, std::string> weak_interest_score(const std::vector<float> &embedding_scores,
                                                   const std::vector<std::string> &embedding_categories) {
  float score = 0.0f;
  if (embedding_scores.size() == 0) {
    return std::tie(score, embedding_categories[0]);
  }

  size_t lowest_index = 0;
  float lowest_value = embedding_scores[0];
  for (size_t i = 1; i < embedding_scores.size(); i++) {
    if (embedding_scores[i] < lowest_value) {
      lowest_value = embedding_scores[i];
      lowest_index = i;
    }
  }

  score = lowest_value;
  return std::tie(score, embedding_categories[lowest_index]);
}

std::tuple<float, std::string> disagreement_interest_score(const std::vector<float> &embedding_scores, float weed_score,
                                                           float crop_score,
                                                           const std::vector<std::string> &embedding_categories) {
  float score = 0.0f;
  if (embedding_scores.size() == 0) {
    return std::tie(score, embedding_categories[0]);
  }

  size_t lowest_index = 0;
  float lowest_value = embedding_scores[0];
  for (size_t i = 1; i < embedding_scores.size(); i++) {
    if (embedding_scores[i] < lowest_value) {
      lowest_value = embedding_scores[i];
      lowest_index = i;
    }
  }

  // Dumb string matching that we have elsewhere for the time being as well
  // TODO: RAVEN make this less shitty once we change how we identify the crop category
  std::string category = embedding_categories[lowest_index];
  std::transform(category.begin(), category.end(), category.begin(), [](unsigned char c) { return std::tolower(c); });
  if ((crop_score < weed_score && category == "crop") || (crop_score > weed_score && category != "crop")) {
    score = -1.0f * lowest_value;
  } else {
    score = -0.1f * lowest_value;
  }
  return std::tie(score, embedding_categories[lowest_index]);
}

float random_interest_score() { return (float)std::rand() / ((float)(RAND_MAX) + 1.0f); }

} // namespace common
} // namespace lib

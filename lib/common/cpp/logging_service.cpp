#include "logging_service.hpp"
#include "carbon_logging/cpp/logging.hpp"

#include <spdlog/async.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/spdlog.h>

namespace lib::common::logging {

LoggingServiceImpl::LoggingServiceImpl(std::string filename) { carbon::logging::init_logger(filename); }

::grpc::Status LoggingServiceImpl::SetLevel(::grpc::ServerContext *context,
                                            const ::carbon::logging::SetLevelRequest *request,
                                            ::carbon::logging::Empty *response) {
  if (!context || !request || !response) {
    return ::grpc::Status::CANCELLED;
  }
  if (!carbon::logging::set_log_level(request->loglevel())) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, "Log level doesn't exist");
  }
  return ::grpc::Status::OK;
}

} // namespace lib::common::logging
#pragma once
#include <iostream>
#include <limits>
#include <string>
#include <tuple>
#include <vector>

namespace lib {
namespace common {
std::tuple<float, std::string, std::string>
ambiguity_interest_score(const std::vector<float> &embedding_scores,
                         const std::vector<std::string> &embedding_categories);

std::tuple<float, std::string> weak_interest_score(const std::vector<float> &embedding_scores,
                                                   const std::vector<std::string> &embedding_categories);

std::tuple<float, std::string> disagreement_interest_score(const std::vector<float> &embedding_scores, float weed_score,
                                                           float crop_score,
                                                           const std::vector<std::string> &embedding_categories);

float random_interest_score();

} // namespace common
} // namespace lib

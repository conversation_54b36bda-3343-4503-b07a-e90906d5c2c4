#include "lib/common/cpp/utils/string_utils.hpp"

namespace carbon::common {
void split_string(std::string input, const std::string_view delim, std::vector<std::string> *output) {
  size_t pos = 0;
  while ((pos = input.find(delim)) != std::string::npos) {
    output->emplace_back(input.substr(0, pos));
    input.erase(0, pos + delim.length());
  }
  output->emplace_back(input);
}
} // namespace carbon::common
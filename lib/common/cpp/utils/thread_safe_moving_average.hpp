#pragma once
#include "lib/common/cpp/utils/moving_average.hpp"

#include <mutex>

namespace carbon {
namespace common {

template <typename T>
class ThreadSafeMovingAverage {
private:
  mutable std::mutex mut_;
  MovingAverage<T> avg_;

public:
  ThreadSafeMovingAverage(size_t max_size, T default_value = 0.0, bool require_full = false)
      : avg_(max_size, default_value, require_full) {}
  ThreadSafeMovingAverage(const ThreadSafeMovingAverage<T> &rhs) : avg_(rhs.avg_) {}
  template <typename N>
  void add(const N &val) {
    const std::lock_guard<std::mutex> lk(mut_);
    return avg_.add(val);
  }
  T avg() const {
    const std::lock_guard<std::mutex> lk(mut_);
    return avg_.avg();
  }
  bool full() const {
    const std::lock_guard<std::mutex> lk(mut_);
    return avg_.full();
  }
  void resize(size_t max_size) {
    const std::lock_guard<std::mutex> lk(mut_);
    return avg_.resize(max_size);
  }
};
} // namespace common
} // namespace carbon
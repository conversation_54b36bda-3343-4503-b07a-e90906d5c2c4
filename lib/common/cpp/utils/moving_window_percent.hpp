#pragma once
#include <boost/circular_buffer.hpp>
#include <functional>

namespace carbon {
namespace common {

template <typename T>
class MovingWindowPercent {
public:
  using Comparitor = std::function<bool(const T &)>;
  MovingWindowPercent(size_t max_size, Comparitor cmp, bool require_full = false)
      : data_(max_size > 0 ? max_size : 1), cmp_(cmp), cur_val_(0), require_full_(require_full) {}
  MovingWindowPercent(const MovingWindowPercent<T> &rhs)
      : data_(rhs.data_.capacity()), cmp_(rhs.cmp_), cur_val_(rhs.cur_val_), require_full_(rhs.require_full_) {
    for (const auto &val : rhs.data_) {
      data_.push_back(val);
    }
  }
  void add(const T &val) {
    bool success = cmp_(val);
    add_direct(success);
  }
  void add_direct(bool success) {
    if (data_.full() && data_.front() && cur_val_ > 0) {
      --cur_val_;
    }
    data_.push_back(success);
    if (success) {
      ++cur_val_;
    }
  }
  float percent() const {
    if (data_.empty() || (require_full_ && !data_.full())) {
      return 0.0f;
    }
    return static_cast<float>(cur_val_) / (static_cast<float>(data_.size()));
  }
  bool full() const { return data_.full(); }
  void resize(size_t max_size) {
    if (max_size == 0) {
      max_size = 1;
    }
    if (max_size == data_.max_size()) {
      return;
    }
    while (data_.size() > max_size) {
      if (data_.front()) {
        --cur_val_;
      }
      data_.pop_front();
    }
    data_.set_capacity(max_size);
  }

protected:
  boost::circular_buffer<bool> data_;
  Comparitor cmp_;
  size_t cur_val_;
  bool require_full_;
};
} // namespace common
} // namespace carbon
#pragma once
#include <boost/circular_buffer.hpp>
#include <type_traits>

namespace carbon {
namespace common {

template <typename T = float>
class MovingAverage {
protected:
  boost::circular_buffer<T> data_;
  T cur_val_;
  const T default_val_;

public:
  MovingAverage(size_t max_size, T default_value = 0.0, bool require_full = false)
      : data_(max_size > 0 ? max_size : 1), cur_val_(0.0), default_val_(default_value) {
    static_assert(std::is_floating_point<T>::value, "Moving average requires floating point type.");
    if (require_full) {
      for (size_t i = 0; i < data_.capacity(); ++i) {
        data_.push_back(default_val_);
        cur_val_ += default_val_;
      }
    }
  }
  MovingAverage(const MovingAverage<T> &rhs)
      : data_(rhs.data_.capacity()), cur_val_(rhs.cur_val_), default_val_(rhs.default_val_) {
    for (const auto &val : rhs.data_) {
      data_.push_back(val);
    }
  }
  MovingAverage<T> &operator=(const MovingAverage<T> &rhs) {
    data_.clear();
    data_.resize(rhs.data_.capacity());
    for (const auto &val : rhs.data_) {
      data_.push_back(val);
    }
    cur_val_ = rhs.cur_val_;
    return *this;
  }
  template <typename N>
  void add(const N &val) {
    static_assert(std::is_arithmetic<N>::value, "Moving average requires adding numeric type.");
    if (data_.full()) {
      cur_val_ -= data_.front();
    }
    cur_val_ += static_cast<T>(val);
    data_.push_back(static_cast<T>(val));
  }
  T avg() const {
    if (data_.empty()) {
      return default_val_;
    }
    return cur_val_ / (static_cast<T>(data_.size()));
  }
  bool full() const { return data_.full(); }
  void clear() {
    cur_val_ = default_val_;
    data_.clear();
  }
  void resize(size_t max_size) {
    if (max_size == 0) {
      max_size = 1;
    }
    if (max_size == data_.max_size()) {
      return;
    }
    while (data_.size() > max_size) {
      cur_val_ -= data_.front();
      data_.pop_front();
    }
    data_.set_capacity(max_size);
  }
  size_t size() { return data_.size(); }
};
} // namespace common
} // namespace carbon
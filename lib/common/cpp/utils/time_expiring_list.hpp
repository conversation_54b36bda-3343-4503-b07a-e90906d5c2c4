#pragma once

#include <chrono>
#include <functional>
#include <list>

namespace carbon {
namespace common {
template <typename T>
class TimeExpiringList {
  /*
  Elements are always in sorted order from oldest to newest. However any element can be removed from the list at
  anytime. Elements will automatically be removed from the list once expired.
  */
private:
  struct Element {
    T data;
    std::chrono::system_clock::time_point insert_time;
    Element(const Element &elem) : data(elem.data), insert_time(elem.insert_time) {}
    template <typename... Args>
    Element(Args &&... args) : data(std::forward<Args>(args)...), insert_time(std::chrono::system_clock::now()) {}
  };

  std::list<Element> list_;
  std::chrono::seconds expiration_;
  bool clean_on_write_;

  void cleanup() {
    auto now = std::chrono::system_clock::now();
    for (auto it = list_.begin(); it != list_.end();) {
      if (std::chrono::duration_cast<std::chrono::seconds>(now - it->insert_time) > expiration_) {
        it = list_.erase(it);
      } else {
        break;
      }
    }
  }

public:
  using FilterFunc = std::function<bool(const T &)>;
  TimeExpiringList(std::chrono::seconds expiration, bool clean_on_write = false)
      : expiration_(expiration), clean_on_write_(clean_on_write) {}
  size_t size(bool clean = true) {
    // If clean is false this can give inflated results
    if (clean) {
      cleanup();
    }
    return list_.size();
  }
  bool empty(bool clean = true) {
    // If clean is false this can give invalid results
    if (clean) {
      cleanup();
    }
    return list_.empty();
  }
  void clear() { list_.clear(); }
  template <typename... Args>
  void emplace_back(Args &&... args) {
    if (clean_on_write_) {
      cleanup();
    }
    list_.emplace_back(std::forward<Args>(args)...);
  }
  void push_back(const T &data) {
    if (clean_on_write_) {
      cleanup();
    }
    list_.push_back(Element(data));
  }
  class iterator {
  public:
    using list_it = typename std::list<Element>::iterator;
    iterator(list_it p) : pos_(p) {}

    T &operator*() { return pos_->data; }

    T *operator->() { return &(pos_->data); }

    bool operator!=(const iterator &rhs) { return this->pos_ != rhs.pos_; }

    iterator operator++() {
      ++pos_;
      return *this;
    }

  private:
    list_it pos_;
    friend class TimeExpiringList;
  };

  iterator begin() {
    cleanup();
    return iterator(list_.begin());
  }
  iterator end() { return iterator(list_.end()); }
  iterator erase(iterator it) { return iterator(list_.erase(it.pos_)); }
  void filter(FilterFunc func) {
    cleanup();
    for (auto it = list_.begin(); it != list_.end();) {
      if (func(it->data)) {
        ++it;
      } else {
        it = list_.erase(it);
      }
    }
  }
};
} // namespace common
} // namespace carbon
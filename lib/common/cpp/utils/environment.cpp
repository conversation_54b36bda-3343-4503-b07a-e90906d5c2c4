#include "lib/common/cpp/utils/environment.hpp"
#include <lib/common/cpp/utils/generation.hpp>
#include <lib/common/cpp/utils/role.hpp>

#include <cstdlib>
#include <string>

#define ROW_VAR "MAKA_ROW"
namespace carbon::common {

std::string getenv(const std::string &key) {
  char *val_ptr = std::getenv(key.c_str());
  std::string val = "";
  if (val_ptr != NULL) {
    val += val_ptr;
  }
  return val;
}
bool is_aimbot() {
  static bool is_aimbot_val = getenv("HOSTNAME") == "aimbot";
  return is_aimbot_val;
}

std::string _get_row() {
  std::string row_id = "row";
  row_id += getenv(ROW_VAR);
  return row_id;
}
int _get_row_id() {
  char *val_ptr = std::getenv(ROW_VAR);
  if (val_ptr) {
    return atoi(val_ptr);
  }
  return 0;
}
std::string get_row() {
  const static std::string row(_get_row());
  return row;
}

int get_row_id() {
  const static int row(_get_row_id());
  return row;
}

std::string get_command_ip() {
  if (is_bud() || is_role_simulator() || get_role() == Role::ROLE_COMMAND) {
    return "127.0.0.1";
  } else {
    return "*********";
  }
}

std::string get_secondary_ip() {
  auto role = get_role();

  if (role == Role::ROLE_SIMULATOR_MINICOMPUTERS) {
    return "localhost";
  } else if (role == Role::ROLE_ROW_PRIMARY) {
    return "**********";
  }
  return "";
}
} // namespace carbon::common
#include "lib/common/cpp/utils/thread_pool_background.hpp"

#include <spdlog/spdlog.h>

/**
 * Implementation Notes:
 * Two data structures are used to manage the jobs. The first is a ThreadSafeQueue that holds the jobs (id, func) that
 * need to be done. The second is an unordered_set that holds the ids of the jobs that are currently queued or being
 * run. The two data structures operate with their own atomic protection. The TreadSafeQueue is used to notify runners
 * of new jobs but the unordered_set is used to track jobs overall.
 *
 * Invariants:
 * 1. ThreadSafeQueue is a subset of the unordered_set; a job is in the ThreadSafeQueue if and only if it is in the
 * unordered_set.
 * 2. Jobs being run by a thread are a subset of the unordered_set; a job is being run by a thread if and only if it is
 * in the unordered_set.
 * 3. If a job is in the unordered_set, it is either in the ThreadSafeQueue or being run by a thread.
 */

namespace carbon::common {
ThreadPoolBackground::ThreadPoolBackground(size_t num_threads)
    : shutdown_(false), next_id_(0), bse_(lib::common::bot::BotStopHandler::get().create_event(
                                         "ThreadPoolBackground", std::bind(&ThreadPoolBackground::terminate, this))) {
  for (size_t i = 0; i < num_threads; ++i) {
    threads_.emplace_back(&ThreadPoolBackground::runner, this);
  }
}

ThreadPoolBackground::~ThreadPoolBackground() { terminate(); }
void ThreadPoolBackground::add(Func func) {
  if (shutdown_) {
    return;
  }
  auto id = ++next_id_;
  {
    const std::unique_lock lock(mut_);
    in_progress_.emplace(id);
  }
  jobs_.emplace_add(id, func);
}

void ThreadPoolBackground::clear() {
  auto queued_jobs = jobs_.pop_all();
  {
    const std::unique_lock lock(mut_);
    for (auto &job : queued_jobs) {
      in_progress_.erase(job.first);
    }
  }
}

void ThreadPoolBackground::await() {
  while (!shutdown_) {
    {
      std::unique_lock lock(mut_);
      if (in_progress_.empty()) {
        return;
      }
      cv_.wait(lock);
    }
  }
}

size_t ThreadPoolBackground::in_progress() {
  std::unique_lock lock(mut_);
  return in_progress_.size();
}

void ThreadPoolBackground::terminate() {
  bool local(false);
  if (!shutdown_.compare_exchange_strong(local, true)) {
    return;
  }
  jobs_.terminate();
  for (auto &thrd : threads_) {
    thrd.join();
  }
  if (!jobs_.empty()) {
    spdlog::warn("Shutting down background pool with {} unfinished jobs", jobs_.size());
  }
  bse_->set();
}

void ThreadPoolBackground::runner() {
  while (!shutdown_) {
    auto opt_job = jobs_.wait_pop(0);
    if (opt_job) {
      try {
        opt_job->second();
      } catch (const std::exception &ex) {
        spdlog::warn("Background process threw exception {}", ex.what());
      }
      auto id = opt_job->first;
      {
        const std::unique_lock lock(mut_);
        bool erased = in_progress_.erase(id);
        if (!erased) {
          throw std::runtime_error("Background process completed but was not in the set of in progress jobs");
        }
      }
      cv_.notify_all();
    }
  }
}
} // namespace carbon::common
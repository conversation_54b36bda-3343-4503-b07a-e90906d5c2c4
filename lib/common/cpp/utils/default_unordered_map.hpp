#pragma once
#include <functional>
#include <tuple>
#include <unordered_map>

namespace carbon::common {
template <typename K, typename V, class Hash = std::hash<K>>
class DefaultUnorderedMap : public std::unordered_map<K, V, Hash> {
public:
  using ConstructorFunc = std::function<V(K)>;
  DefaultUnorderedMap(ConstructorFunc func) : cf_(func) {}
  V &operator[](const K &key) {
    auto it = this->find(key);
    if (it == this->end()) {
      auto ins = this->emplace(key, cf_(key));
      return ins.first->second;
    }
    return it->second;
  }

private:
  ConstructorFunc cf_;
};
template <typename K, typename V, typename... Args>
class ParameterPackDefaultUnorderedMap : public std::unordered_map<K, V> {
public:
  ParameterPackDefaultUnorderedMap(Args &&... args) : pack_(std::make_tuple(std::forward<Args...>(args)...)) {}
  ParameterPackDefaultUnorderedMap(const ParameterPackDefaultUnorderedMap &rhs)
      : std::unordered_map<K, V>(rhs), pack_(rhs.pack_) {}
  V &operator[](const K &key) {
    auto it = this->find(key);
    if (it == this->end()) {
      std::tuple<Args...> pack_copy(pack_);
      auto ins = this->emplace(key, std::make_from_tuple<V>(std::forward<std::tuple<Args...>>(pack_copy)));
      return ins.first->second;
    }
    return it->second;
  }

private:
  std::tuple<Args...> pack_;
};
} // namespace carbon::common
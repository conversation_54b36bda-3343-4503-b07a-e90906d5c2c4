#include <type_traits>
namespace carbon::common {
template <typename T>
constexpr T in_to_mm(T inch) {
  static_assert(std::is_floating_point<T>::value, "requires floating point type.");
  return inch * static_cast<T>(25.4);
}
template <typename T>
constexpr T ft_to_in(T ft) {
  static_assert(std::is_floating_point<T>::value, "requires floating point type.");
  return ft * static_cast<T>(12.0);
}
template <typename T>
constexpr T ft_to_mm(T ft) {
  static_assert(std::is_floating_point<T>::value, "requires floating point type.");
  return in_to_mm(ft_to_in(ft));
}
} // namespace carbon::common
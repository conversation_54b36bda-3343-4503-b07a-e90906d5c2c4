#pragma once
#include <functional>

namespace carbon::common {
class ScopeGuard {
public:
  using ScopeFunc = std::function<void(void)>;
  ScopeGuard(const ScopeFunc &func) : func_(func) {}
  ~ScopeGuard() { func_(); }

private:
  const ScopeFunc func_;

  auto operator=(ScopeGuard const &) -> ScopeGuard & = delete;
  ScopeGuard(ScopeGuard const &) = delete;
  auto operator=(ScopeGuard &&) -> ScopeGuard & = delete;
  ScopeGuard() = delete;
  ScopeGuard(ScopeGuard &&) = delete;
};
} // namespace carbon::common
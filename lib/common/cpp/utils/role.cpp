#include "lib/common/cpp/utils/role.hpp"
#include <lib/common/cpp/utils/environment.hpp>

namespace carbon::common {
const std::string_view role_str() {
  const static std::string _role(getenv("MAKA_ROLE"));
  return _role;
}
Role get_role() {
  static Role _role([]() -> Role {
    auto role(role_str());
    if (role == ROLE_BUD_STR) {
      return Role::ROLE_BUD;
    } else if (role == ROLE_COMMAND_STR) {
      return Role::ROLE_COMMAND;
    } else if (role == ROLE_ROW_STR) {
      return Role::ROLE_ROW;
    } else if (role == ROLE_ROW_PRIMARY_STR) {
      return Role::ROLE_ROW_PRIMARY;
    } else if (role == ROLE_ROW_SECONDARY_STR) {
      return Role::ROLE_ROW_SECONDARY;
    } else if (role == ROLE_VESELKA_CV_STR) {
      return Role::ROLE_VESELKA_CV;
    } else if (role == ROLE_PRODUCTION_STR) {
      return Role::ROLE_PRODUCTION;
    } else if (role == ROLE_DEEPLEARNING_STR) {
      return Role::ROLE_DEEPLEARNING;
    } else if (role == ROLE_ROBOT_UI_STR) {
      return Role::ROLE_ROBOT_UI;
    } else if (role == ROLE_MODULE_STR) {
      return Role::ROLE_MODULE;
    } else if (role == ROLE_SIMULATOR_MINICOMPUTERS_STR) {
      return Role::ROLE_SIMULATOR_MINICOMPUTERS;
    } else if (role == ROLE_SIMULATOR_STR) {
      return Role::ROLE_SIMULATOR;
    }
    return Role::ROLE_BUD; // Default to bud if no role found
  }());
  return _role;
}
bool has_secondary() {
  const static bool _has_secondary(get_role() == Role::ROLE_ROW_PRIMARY ||
                                   get_role() == Role::ROLE_SIMULATOR_MINICOMPUTERS);
  return _has_secondary;
}
bool is_role_simulator() {
  const static bool _is_sim(get_role() == Role::ROLE_SIMULATOR || get_role() == Role::ROLE_SIMULATOR_MINICOMPUTERS);
  return _is_sim;
}
} // namespace carbon::common
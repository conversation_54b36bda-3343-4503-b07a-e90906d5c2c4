#pragma once

#include <string>

namespace carbon::common {
static constexpr std::string_view ROLE_BUD_STR("bud");
static constexpr std::string_view ROLE_COMMAND_STR("command");
static constexpr std::string_view ROLE_ROW_STR("row");
static constexpr std::string_view ROLE_ROW_PRIMARY_STR("row-primary");
static constexpr std::string_view ROLE_ROW_SECONDARY_STR("row-secondary");
static constexpr std::string_view ROLE_VESELKA_CV_STR("veselka_cv");
static constexpr std::string_view ROLE_PRODUCTION_STR("production");
static constexpr std::string_view ROLE_DEEPLEARNING_STR("deeplearning");
static constexpr std::string_view ROLE_ROBOT_UI_STR("robot-ui");
static constexpr std::string_view ROLE_MODULE_STR("module");
static constexpr std::string_view ROLE_SIMULATOR_MINICOMPUTERS_STR("simulator_minicomputers");
static constexpr std::string_view ROLE_SIMULATOR_STR("simulator");
enum Role {
  ROLE_BUD = 0,
  ROLE_COMMAND = 1,
  ROLE_ROW = 2,
  ROLE_ROW_PRIMARY = 3,
  ROLE_ROW_SECONDARY = 4,
  ROLE_VESELKA_CV = 5,
  ROLE_PRODUCTION = 6,
  ROLE_DEEPLEARNING = 7,
  ROLE_ROBOT_UI = 8,
  ROLE_MODULE = 9,
  ROLE_SIMULATOR_MINICOMPUTERS = 10,
  ROLE_SIMULATOR = 11,
};
Role get_role();
const std::string_view role_str();
bool has_secondary();
bool is_role_simulator();
} // namespace carbon::common
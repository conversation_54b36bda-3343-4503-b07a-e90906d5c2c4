#pragma once
#include <atomic>
#include <condition_variable>
#include <cstddef>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include <unordered_set>
#include <utility>
#include <vector>

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/utils/thread_safe_queue.hpp>

namespace carbon::common {

template <typename Arg>
class ThreadPoolBatch {
public:
  using Func = std::function<void(Arg)>;
  using JobID = size_t;
  ThreadPoolBatch(Func func, size_t num_threads)
      : next_id_(0), shutdown_(false), bse_(lib::common::bot::BotStopHandler::get().create_event(
                                           "ThreadPoolBatch", std::bind(&ThreadPoolBatch<Arg>::terminate, this))),
        func_(func) {
    for (size_t i = 0; i < num_threads; ++i) {
      threads_.emplace_back(&ThreadPoolBatch<Arg>::runner, this);
    }
  }

  ~ThreadPoolBatch() { terminate(); }
  void queue_job(const Arg &arg) {
    if (shutdown_) {
      return;
    }
    auto id = ++next_id_;
    {
      std::unique_lock<std::mutex> lk(mut_);
      active_jobs_.insert(id);
    }
    requested_jobs_.emplace_add_no_notify(id, arg);
  }
  void run_jobs() {
    if (!requested_jobs_.empty()) {
      requested_jobs_.wake();
    }
    while (!shutdown_) {
      std::unique_lock<std::mutex> lk(mut_);
      if (active_jobs_.empty()) {
        return;
      }
      cv_.wait(lk);
    }
  }
  void terminate() {
    if (shutdown_) {
      return;
    }
    shutdown_ = true;
    requested_jobs_.terminate();
    cv_.notify_all();
    for (auto &thrd : threads_) {
      if (thrd.joinable()) {
        thrd.join();
      }
    }
    bse_->set();
  }
  void clear() {
    requested_jobs_.clear();
    {
      std::unique_lock<std::mutex> lk(mut_);
      active_jobs_.clear();
    }
    cv_.notify_all();
  }

private:
  std::atomic<JobID> next_id_;
  std::atomic<bool> shutdown_;
  std::shared_ptr<lib::common::bot::BotStopEvent> bse_;
  Func func_;
  std::unordered_set<JobID> active_jobs_;
  ThreadSafeQueue<std::pair<JobID, Arg>> requested_jobs_;
  std::mutex mut_;
  std::condition_variable cv_;
  std::vector<std::thread> threads_;

  void runner() {
    while (!shutdown_) {
      auto opt_job = requested_jobs_.wait_pop(0);
      if (!opt_job) {
        continue;
      }
      func_(opt_job.value().second);
      {
        std::unique_lock<std::mutex> lk(mut_);
        active_jobs_.erase(opt_job.value().first);
      }
      cv_.notify_all();
    }
  }
};
} // namespace carbon::common
#pragma once
#include <boost/circular_buffer.hpp>
#include <memory>
#include <type_traits>

namespace carbon {
namespace common {

template <typename T>
class MedianEstimator {
public:
  struct Params {
    T step_size;
    T convergence_threshold;
    T convergence_smoothing;
    T convergence_factor;
    Params(T _step_size = 1.0f, T _convergence_threshold = 0.9f, T _convergence_smoothing = 0.9f,
           T _convergence_factor = 10.0f)
        : step_size(_step_size), convergence_threshold(_convergence_threshold),
          convergence_smoothing(_convergence_smoothing), convergence_factor(_convergence_factor) {}
  };

private:
  MedianEstimator() = delete;

protected:
  T cur_val_;
  T convergence_val_;
  std::shared_ptr<MedianEstimator<T>::Params> params_;

public:
  MedianEstimator(T default_value = 0.0f, T step_size = 1.0f, T convergence_threshold = 0.9f,
                  T convergence_smoothing = 0.9f, T convergence_factor = 10.0f)
      : cur_val_(default_value), convergence_val_(0),
        params_(std::make_shared<MedianEstimator<T>::Params>(step_size, convergence_threshold, convergence_smoothing,
                                                             convergence_factor)) {
    static_assert(std::is_floating_point<T>::value, "Median Estimator requires floating point type.");
  }
  MedianEstimator(std::shared_ptr<MedianEstimator<T>::Params> params, T default_value = 0.0)
      : cur_val_(default_value), convergence_val_(0), params_(params) {
    static_assert(std::is_floating_point<T>::value, "Median Estimator requires floating point type.");
  }

  MedianEstimator(const MedianEstimator<T> &rhs)
      : cur_val_(rhs.cur_val_), convergence_val_(rhs.convergence_val_), params_(rhs.params_) {}

  MedianEstimator<T> &operator=(const MedianEstimator<T> &rhs) {
    cur_val_ = rhs.cur_val_;
    convergence_val_ = rhs.convergence_val_;
    params_ = rhs.params_;
  }
  std::shared_ptr<MedianEstimator<T>::Params> get_params() { return params_; }

  template <typename N>
  void add(const N &val) {
    static_assert(std::is_arithmetic<N>::value, "Median Estimator requires adding numeric type.");
    T delta = static_cast<T>(val) - cur_val_;

    // Scale down to step size
    if (delta > params_->step_size) {
      delta = params_->step_size;
    } else if (delta < -params_->step_size) {
      delta = -params_->step_size;
    }

    convergence_val_ =
        (convergence_val_ * params_->convergence_smoothing) + (delta * (1 - params_->convergence_smoothing));

    if (convergence_val_ >= (params_->convergence_threshold * params_->step_size) ||
        convergence_val_ <= -(params_->convergence_threshold * params_->step_size)) {
      delta *= params_->convergence_factor;
    }

    cur_val_ += delta;
  }

  T value() const { return cur_val_; }
};
} // namespace common
} // namespace carbon
#pragma once
/*
 * Create a thread pool to run a singular function many times.
 * This function will run in the background, and keep track of
 * tasks that need to be done.
 */
#include <atomic>
#include <condition_variable>
#include <cstddef>
#include <functional>
#include <mutex>
#include <thread>
#include <unordered_set>
#include <vector>

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/utils/thread_safe_queue.hpp>

namespace carbon::common {

class ThreadPoolBackground {
public:
  using Func = std::function<void(void)>;
  ThreadPoolBackground(size_t num_threads);
  ~ThreadPoolBackground();
  /**
   * Add a function to the thread pool to be run in the background.
   */
  void add(Func func);
  /**
   * Clear all queued jobs from the thread pool, currently running jobs will continue.
   */
  void clear();
  /**
   * Wait for all queued and running jobs to finish.
   */
  void await();

  /**
   * Get the number of jobs currently in progress.
   */
  size_t in_progress();

private:
  std::atomic<bool> shutdown_;
  std::atomic<uint32_t> next_id_;
  std::shared_ptr<lib::common::bot::BotStopEvent> bse_;
  ThreadSafeQueue<std::pair<size_t, Func>> jobs_;
  std::mutex mut_;
  std::condition_variable cv_;
  std::unordered_set<size_t> in_progress_;
  std::vector<std::thread> threads_;

  void terminate();
  void runner();
};
} // namespace carbon::common
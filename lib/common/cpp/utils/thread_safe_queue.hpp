#pragma once

#include <chrono>
#include <condition_variable>
#include <memory>
#include <mutex>
#include <optional>
#include <vector>

#include <spdlog/spdlog.h>

namespace carbon {
namespace common {
template <typename T>
class ThreadSafeQueue {
private:
  struct TSQ_Node {
    T data;
    TSQ_Node *next;
    template <typename... Args>
    TSQ_Node(Args &&... args) : data(std::forward<Args>(args)...), next(nullptr) {}
    ~TSQ_Node() {}
  };
  size_t size_;
  std::mutex mut_;
  std::condition_variable cv_;
  TSQ_Node *head_;
  TSQ_Node *tail_;
  bool no_wait_;

  std::optional<T> _peek() {
    if (size_ == 0) {
      return std::optional<T>(std::nullopt);
    }
    return {head_->data};
  }

  void _pop(TSQ_Node **node) {
    if (size_ == 0) {
      return;
    }
    *node = head_;
    if (head_ == tail_) {
      tail_ = nullptr;
    }
    head_ = head_->next;
    --size_;
  }

  size_t _add(TSQ_Node *node, bool notify) {
    size_t cur_size;
    {
      std::unique_lock<std::mutex> lk(mut_);
      if (size_ == 0) {
        head_ = node;
      } else {
        tail_->next = node;
      }
      tail_ = node;
      cur_size = ++size_;
    }
    if (notify) {
      cv_.notify_one();
    }
    return cur_size;
  }

public:
  class ResultList {
  private:
    size_t size_;
    TSQ_Node *head_;

  public:
    struct ConstIterator {
    private:
      TSQ_Node *node_;

    public:
      ConstIterator(TSQ_Node *node) : node_(node) {}
      const T &operator*() const { return node_->data; }

      // Prefix increment
      ConstIterator &operator++() {
        node_ = node_->next;
        return *this;
      }

      // Postfix increment
      ConstIterator operator++(int) {
        ConstIterator tmp = *this;
        ++(*this);
        return tmp;
      }

      friend bool operator==(const ConstIterator &lhs, const ConstIterator &rhs) { return lhs.node_ == rhs.node_; };
      friend bool operator!=(const ConstIterator &lhs, const ConstIterator &rhs) { return lhs.node_ != rhs.node_; };
    };

    ResultList(size_t size, TSQ_Node *head) : size_(size), head_(head) {}
    // Move constructor
    ResultList(ResultList &&rhs) : size_(rhs.size_), head_(rhs.head_) {
      rhs.size_ = 0;
      rhs.head_ = nullptr;
    }
    ~ResultList() {
      TSQ_Node *prev(nullptr);
      while (head_) {
        prev = head_;
        head_ = head_->next;
        delete prev;
      }
    }
    ConstIterator begin() const { return ConstIterator(head_); }
    ConstIterator end() const { return ConstIterator(nullptr); }
    inline size_t size() const { return size_; }
    inline bool empty() const { return size_ == 0; }
  };

private:
  ResultList _pop_all() {
    ResultList output(size_, head_);
    head_ = nullptr;
    tail_ = nullptr;
    size_ = 0;
    return output;
  }

public:
  ThreadSafeQueue() : size_(0), head_(nullptr), tail_(nullptr), no_wait_(false) {}
  ThreadSafeQueue(ThreadSafeQueue &&rhs) {
    std::unique_lock<std::mutex> lk(rhs.mut_);
    size_ = rhs.size_;
    head_ = rhs.head_;
    tail_ = rhs.tail_;
    no_wait_ = false;
    rhs.size_ = 0;
    rhs.head_ = nullptr;
    rhs.tail_ = nullptr;
  }
  inline size_t size() {
    std::unique_lock<std::mutex> lk(mut_);
    return size_;
  }
  inline void disable_wait() {
    std::unique_lock<std::mutex> lk(mut_);
    no_wait_ = true;
  }
  inline void enable_wait() {
    std::unique_lock<std::mutex> lk(mut_);
    no_wait_ = true;
  }

  inline void terminate() {
    disable_wait();
    wake();
  }

  ~ThreadSafeQueue() {
    terminate();
    clear();
  }
  inline bool empty() {
    std::unique_lock<std::mutex> lk(mut_);
    return size_ == 0;
  }
  void wake() { cv_.notify_all(); }

  size_t add(T data, bool notify = true) {
    TSQ_Node *node = new TSQ_Node(data);
    return _add(node, notify);
  }
  template <typename... Args>
  size_t emplace_add(Args &&... args) {
    TSQ_Node *node = new TSQ_Node(std::forward<Args>(args)...);
    return _add(node, true);
  }

  template <typename... Args>
  size_t emplace_add_no_notify(Args &&... args) {
    TSQ_Node *node = new TSQ_Node(std::forward<Args>(args)...);
    return _add(node, false);
  }
  size_t add(const std::vector<T> &data_vec, bool notify = true) {
    if (data_vec.empty()) {
      return size();
    }
    // Build new list section outside of lock
    TSQ_Node *head(nullptr), *tail(nullptr);
    for (auto &data : data_vec) {
      TSQ_Node *node = new TSQ_Node(data);
      if (!head) {
        head = node;
      } else {
        tail->next = node;
      }
      tail = node;
    }
    {
      // lock and do quick pointer manipulation
      std::unique_lock<std::mutex> lk(mut_);
      if (size_ == 0) {
        head_ = head;
        tail_ = tail;
      } else {
        tail_->next = head;
        tail_ = tail;
      }
      size_ += data_vec.size();
      if (notify) {
        cv_.notify_all();
      }
      return size_;
    }
  }
  std::optional<T> peek() {
    std::unique_lock<std::mutex> lk(mut_);
    return _peek();
  }
  std::optional<T> pop() {
    TSQ_Node *node = nullptr;
    {
      std::unique_lock<std::mutex> lk(mut_);
      _pop(&node);
    }
    if (!node) {
      return std::optional<T>(std::nullopt);
    }
    T p = node->data;
    delete node;
    return {p};
  }
  ResultList pop_all() {
    std::unique_lock<std::mutex> lk(mut_);
    return _pop_all();
  }
  ResultList wait_pop_all(uint32_t timeout_ms) {
    std::unique_lock<std::mutex> lk(mut_);
    if (size_ == 0 && !no_wait_) {
      if (timeout_ms == 0) {
        cv_.wait(lk);
      } else {
        cv_.wait_for(lk, std::chrono::milliseconds(timeout_ms));
      }
    }
    return _pop_all();
  }

  std::optional<T> wait_peek(uint32_t timeout_ms) {
    std::unique_lock<std::mutex> lk(mut_);
    if (size_ == 0 && !no_wait_) {
      if (timeout_ms == 0) {
        cv_.wait(lk);
      } else {
        cv_.wait_for(lk, std::chrono::milliseconds(timeout_ms));
      }
    }
    return _peek();
  }

  std::optional<T> wait_pop(uint32_t timeout_ms) {
    TSQ_Node *node = nullptr;
    {
      std::unique_lock<std::mutex> lk(mut_);
      if (size_ == 0 && !no_wait_) {
        if (timeout_ms == 0) {
          cv_.wait(lk);
        } else {
          cv_.wait_for(lk, std::chrono::milliseconds(timeout_ms));
        }
      }
      _pop(&node);
    }
    if (!node) {
      return std::optional<T>(std::nullopt);
    }
    T p = node->data;
    delete node;
    return {p};
  }
  void clear() {
    std::unique_lock<std::mutex> lk(mut_);
    TSQ_Node *node = nullptr;
    while (size_ > 0) {
      _pop(&node);
      if (!node) {
        // We should never get here
        return;
      }
      delete node;
      node = nullptr;
    }
  }
};
} // namespace common
} // namespace carbon
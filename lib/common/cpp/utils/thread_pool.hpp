#pragma once
#include <atomic>
#include <condition_variable>
#include <cstddef>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include <unordered_set>
#include <utility>
#include <vector>

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/utils/thread_safe_queue.hpp>

namespace carbon::common {
class ThreadPool {
public:
  using Func = std::function<void()>;
  using JobID = size_t;
  ThreadPool(size_t num_threads);
  ~ThreadPool();
  JobID add_job(Func func);
  bool await_job(JobID id);
  void terminate();

private:
  std::atomic<JobID> next_id_;
  std::atomic<bool> shutdown_;
  std::shared_ptr<lib::common::bot::BotStopEvent> bse_;
  std::unordered_set<JobID> active_jobs_;
  ThreadSafeQueue<std::pair<JobID, Func>> requested_jobs_;
  std::mutex mut_;
  std::condition_variable cv_;
  std::vector<std::thread> threads_;

  void runner();
};
} // namespace carbon::common
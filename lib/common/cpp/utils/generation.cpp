#include "lib/common/cpp/utils/generation.hpp"
#include <lib/common/cpp/utils/environment.hpp>

namespace carbon::common {
const std::string_view generation_str() {
  const static std::string _gen(getenv("MAKA_GEN"));
  return _gen;
}
Generation get_generation() {
  static Generation _gen([]() -> Generation {
    auto gen(generation_str());
    if (gen == GEN_BUD) {
      return Generation::BUD;
    } else if (gen == GEN_SLAYER) {
      return Generation::SLAYER;
    } else if (gen == GEN_REAPER) {
      return Generation::REAPER;
    } else if (gen == GEN_RTC) {
      return Generation::RTC;
    }
    return Generation::BUD; // Default to bud if no gen found
  }());
  return _gen;
}
bool is_bud() {
  const static bool _is_bud(get_generation() == Generation::BUD);
  return _is_bud;
}
bool is_slayer() {
  const static bool _is_slayer(get_generation() == Generation::SLAYER);
  return _is_slayer;
}
bool is_reaper() {
  const static bool _is_reaper(get_generation() == Generation::REAPER);
  return _is_reaper;
}
bool is_rtc() {
  const static bool _is_rtc(get_generation() == Generation::RTC);
  return _is_rtc;
}
} // namespace carbon::common
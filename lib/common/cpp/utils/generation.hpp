#pragma once

#include <string>

namespace carbon::common {
static constexpr std::string_view GEN_BUD("bud");
static constexpr std::string_view GEN_SLAYER("slayer");
static constexpr std::string_view GEN_REAPER("reaper");
static constexpr std::string_view GEN_RTC("rtc");
enum Generation {
  BUD = 0,
  SLAYER = 1,
  REAPER = 2,
  RTC = 3,
};
Generation get_generation();

const std::string_view generation_str();
bool is_bud();
bool is_slayer();
bool is_reaper();
bool is_rtc();
} // namespace carbon::common
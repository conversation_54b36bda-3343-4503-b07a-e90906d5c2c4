#pragma once
#include <array>
#include <functional>
namespace carbon::common {
template <std::size_t N>
struct MultiStringHash : public std::unary_function<std::array<std::string, N>, std::size_t> {
  std::size_t operator()(const std::array<std::string, N> &arr) const {
    std::string tmp = "";
    for (size_t i = 0; i < N; ++i) {
      if (i > 0) {
        tmp += "/";
      }
      tmp += arr[i];
    }
    return std::hash<std::string>{}(tmp);
  }
};

} // namespace carbon::common
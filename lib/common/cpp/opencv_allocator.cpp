#include "opencv_allocator.h"

#include "c10/cuda/CUDACachingAllocator.h"

#include "lib/common/cpp/cuda_util.h"

namespace lib {
namespace common {

OpenCVAllocator::OpenCVAllocator() {
  int device_count;
  CUDA_ERROR_CHECK(cudaGetDeviceCount(&device_count));
  // Verify allocator is initialized. No-op if already initialized.
  c10::cuda::CUDACachingAllocator::init(device_count);
}

bool OpenCVAllocator::allocate(cv::cuda::GpuMat *mat, int rows, int cols, size_t elemSize) {
  mat->data = (unsigned char *)c10::cuda::CUDACachingAllocator::raw_alloc(rows * cols * elemSize);
  mat->step = elemSize * cols;

  mat->refcount = (int *)cv::fastMalloc(sizeof(int));

  return true;
}

void OpenCVAllocator::free(cv::cuda::GpuMat *mat) {
  c10::cuda::CUDACachingAllocator::raw_delete(mat->datastart);
  cv::fastFree(mat->refcount);
}

OpenCVAllocator &OpenCVAllocator::get() {
  static OpenCVAllocator inst;
  return inst;
}

} // namespace common
} // namespace lib
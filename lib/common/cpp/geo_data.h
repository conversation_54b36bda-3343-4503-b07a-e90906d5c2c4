#pragma once
#include <mutex>
#include <tuple>

namespace lib {
namespace common {

class GeoLLAData {
public:
  GeoLLAData(double lat, double lng, double alt, int64_t timestamp_ms)
      : lat_(lat), lng_(lng), alt_(alt), timestamp_ms_(timestamp_ms) {}

  double get_lat() { return lat_; }

  double get_lng() { return lng_; }

  double get_alt() { return alt_; }

  int64_t get_timestamp_ms() { return timestamp_ms_; }

private:
  double lat_;
  double lng_;
  double alt_;
  int64_t timestamp_ms_;
};

class GeoECEFData {
public:
  GeoECEFData(double x, double y, double z, int64_t timestamp_ms) : x_(x), y_(y), z_(z), timestamp_ms_(timestamp_ms) {}

  double get_x() { return x_; }

  double get_y() { return y_; }

  double get_z() { return z_; }

  int64_t get_timestamp_ms() { return timestamp_ms_; }

private:
  double x_;
  double y_;
  double z_;
  int64_t timestamp_ms_;
};

class GeoData {
public:
  GeoData(GeoLLAData lla, GeoECEFData ecef) : lla_(lla), ecef_(ecef) {}

  void update(GeoLLAData lla, GeoECEFData ecef) {
    std::lock_guard<std::mutex> lk(this->mutex_);
    lla_ = lla;
    ecef_ = ecef;
  }

  std::tuple<GeoLLAData, GeoECEFData> retrieve() {
    std::lock_guard<std::mutex> lk(this->mutex_);
    return std::make_tuple(lla_, ecef_);
  }

private:
  GeoLLAData lla_;
  GeoECEFData ecef_;
  std::mutex mutex_;
};

} // namespace common
} // namespace lib
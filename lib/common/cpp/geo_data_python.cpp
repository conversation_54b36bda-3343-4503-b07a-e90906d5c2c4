#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "lib/common/cpp/geo_data.h"

namespace py = pybind11;

namespace lib::common {

PYBIND11_MODULE(geo_data_python, m) {
  py::class_<GeoLLAData>(m, "GeoLLAData")
      .def(py::init<float, float, float, int64_t>(), py::arg("lat"), py::arg("lng"), py::arg("alt"),
           py::arg("timestamp_ms"), py::call_guard<py::gil_scoped_release>());

  py::class_<GeoECEFData>(m, "GeoECEFData")
      .def(py::init<float, float, float, int64_t>(), py::arg("x"), py::arg("y"), py::arg("z"), py::arg("timestamp_ms"),
           py::call_guard<py::gil_scoped_release>());
}

} // namespace lib::common

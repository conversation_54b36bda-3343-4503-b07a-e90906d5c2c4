#pragma once
#include <mutex>
#include <tuple>

namespace lib {
namespace common {

class AimbotState {
public:
  AimbotState(bool running, bool armed) : running_(running), armed_(armed) {}

  void update(bool running, bool armed) {
    std::lock_guard<std::mutex> lk(this->mutex_);
    running_ = running;
    armed_ = armed;
  }

  std::tuple<bool, bool> retrieve() {
    std::lock_guard<std::mutex> lk(this->mutex_);
    return std::make_tuple(running_, armed_);
  }

private:
  bool running_;
  bool armed_;
  std::mutex mutex_;
};

} // namespace common
} // namespace lib
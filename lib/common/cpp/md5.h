#include <fstream>
#include <openssl/evp.h>
#include <sstream>

namespace lib::common {

std::string md5sum(std::string filepath) {
  EVP_MD_CTX *context = EVP_MD_CTX_new();
  const EVP_MD *md = EVP_md5();
  unsigned char md_value[EVP_MAX_MD_SIZE];
  unsigned int md_len;
  std::string output;

  std::ifstream file(filepath, std::ios::binary);
  if (!file) {
    return "";
  }

  std::ostringstream ss;
  ss << file.rdbuf();

  std::string file_content = ss.str();

  EVP_DigestInit_ex(context, md, NULL);
  EVP_DigestUpdate(context, file_content.c_str(), file_content.size());
  EVP_DigestFinal_ex(context, md_value, &md_len);
  EVP_MD_CTX_free(context);

  output.resize(md_len * 2);
  for (unsigned int i = 0; i < md_len; ++i) {
    std::sprintf(&output[i * 2], "%02x", md_value[i]);
  }
  return output;
}

} // namespace lib::common
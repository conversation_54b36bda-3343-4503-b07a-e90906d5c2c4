#pragma once
#include <lib/common/cpp/utils/generation.hpp>
#include <shared_mutex>
#include <tuple>

namespace lib {
namespace common {

class ImplementStatus {
public:
  ImplementStatus(bool lifted, bool estopped) : lifted_(lifted), estopped_(estopped) {}

  void update(bool lifted, bool estopped) {
    std::unique_lock<std::shared_mutex> lk(this->mutex_);
    lifted_ = lifted;
    estopped_ = estopped;
  }

  std::tuple<bool, bool> retrieve() {
    // Force return here to be false, false as an extra measure to make sure that buds don't try to use implement status
    if (carbon::common::is_bud()) {
      return std::make_tuple<bool, bool>(false, false);
    }

    std::shared_lock<std::shared_mutex> lk(this->mutex_);
    return std::make_tuple(lifted_, estopped_);
  }

private:
  bool lifted_;
  bool estopped_;
  std::shared_mutex mutex_;
};

} // namespace common
} // namespace lib
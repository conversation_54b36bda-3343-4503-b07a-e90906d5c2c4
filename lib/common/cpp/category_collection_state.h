#pragma once

#include "category/proto/category.pb.h"
#include "lib/common/redis/redis_client.hpp"

namespace lib::common {
struct CategoryCollection {
  carbon::category::CategoryCollection category_collection;
  std::vector<carbon::category::Category> category_definitions;
  int64_t last_updated_timestamp_ms;
};

/**
 * 341) "7ac01a0d-e300-445e-8a4f-2d213b942434"
342) "{\"profileType\":\"CATEGORY_COLLECTION\",\"lastUpdatedTsMs\":\"1741737512687\"}"

hget profile_sync/items
 */

class CategoryCollectionState {
public:
  CategoryCollectionState(std::shared_ptr<RedisClient> redis_client) : redis_client_(redis_client) {}
  std::optional<CategoryCollection> get() {
    carbon::category::CategoryCollection category_collection_proto;
    std::vector<carbon::category::Category> category_definitions_proto;
    auto category_collection_id_opt = redis_client_->get(kActiveCategoryCollectionKey);
    if (!category_collection_id_opt) {
      spdlog::warn("Can't load active collection key");
      return {};
    }
    std::string category_collection_id = category_collection_id_opt.value();

    auto category_collection_opt = redis_client_->hget(kCategoryCollectionsKey, category_collection_id);
    if (!category_collection_opt || !category_collection_proto.ParseFromString(category_collection_opt.value())) {
      spdlog::warn("Can't load or parse category collection");
      return {};
    }

    for (const auto &category_id : category_collection_proto.category_ids()) {
      auto category_opt = redis_client_->hget(kCategoriesKey, category_id);
      carbon::category::Category category_proto;
      if (!category_opt || !category_proto.ParseFromString(category_opt.value())) {
        spdlog::warn("Can't load or parse category proto: {}", category_id);
        return {};
      }
      category_definitions_proto.push_back(category_proto);
    }
    if ((int)category_definitions_proto.size() != category_collection_proto.category_ids().size()) {
      spdlog::warn("Loaded categories size doesn't match category ids in proto");
      return {};
    }
    CategoryCollection category_collection;
    category_collection.category_collection = category_collection_proto;
    category_collection.category_definitions = category_definitions_proto;

    auto profile_sync_opt = redis_client_->hget(kProfileSyncKey, category_collection_proto.id());
    if (!profile_sync_opt) {
      spdlog::warn("Can't load active collection key");
      return {};
    }
    nlohmann::json profile_sync_json = nlohmann::json::parse(profile_sync_opt.value());
    // Load from profile sync redis
    std::string last_updated_timestamp_ms = std::string(profile_sync_json["lastUpdatedTsMs"]);
    // std::cout << last_updated_timestamp_ms << std::endl;
    // last_updated_timestamp_ms = std::string(profile_sync_json["lastUpdatedTsMs"]).substr(1,
    // profile_sync_json["lastUpdatedTsMs"].size() - 2)
    category_collection.last_updated_timestamp_ms = std::stol(last_updated_timestamp_ms);
    return category_collection;
  }

private:
  std::shared_ptr<RedisClient> redis_client_;
  const std::string kActiveCategoryCollectionKey = "/category_collection/active";
  const std::string kCategoryCollectionsKey = "/category_collection/config";
  const std::string kCategoriesKey = "/category/config";
  const std::string kProfileSyncKey = "profile_sync/items";
};
} // namespace lib::common
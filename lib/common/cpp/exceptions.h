#pragma once

#include <iostream>
#include <sstream>

#define BOOST_STACKTRACE_USE_BACKTRACE
#include <boost/stacktrace.hpp>

class maka_error : public std::exception {
public:
  explicit maka_error(const std::string &message, bool include_stacktrace = true, size_t stack_skip = 1);
  virtual ~maka_error();

  virtual const char *what() const noexcept override;

private:
  std::string ex_str_;
};

std::string exception_to_string(const std::exception &e, int level = 0);

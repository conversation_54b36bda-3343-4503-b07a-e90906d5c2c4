#pragma once

#include <torch/torch.h>

#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace common {

// Needed because of https://github.com/pytorch/pytorch/issues/54058
inline c10::List<c10::optional<torch::Tensor>> tensor_vector_to_list(const std::vector<torch::Tensor> &v) {
  c10::List<c10::optional<torch::Tensor>> output{};
  output.reserve(v.size());
  for (auto &t : v) {
    output.push_back(t);
  }
  return output;
}

} // namespace common
} // namespace lib

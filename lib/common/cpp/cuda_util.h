#pragma once

#include <stdexcept>
#include <string>

#include <cuda.h>
#include <cuda_runtime.h>
#include <fmt/format.h>
#include <nvtx3/nvToolsExt.h>

#include "exceptions.h"

inline void CudaErrorCheck(std::string op_name, cudaError_t cuda_result) {
  if (cuda_result != cudaSuccess) {
    throw std::runtime_error(std::string(op_name) + " failed: " + cudaGetErrorString(cuda_result));
  }
}

inline void cuda_api_check(std::string op_name, CUresult result) {
  if (result != CUDA_SUCCESS) {
    const char *err_name = NULL;
    cuGetErrorName(result, &err_name);
    throw maka_error(fmt::format("{} failed: {}", op_name, err_name));
  }
}

#define CUDA_ERROR_CHECK(EXP) CudaErrorCheck(#EXP, EXP);
#define CUDA_API_CHECK(EXP) cuda_api_check(#EXP, EXP);

class with_device {
public:
  with_device(int device) {
    original_device_ = -1;
    CUDA_ERROR_CHECK(cudaGetDevice(&original_device_));
    CUDA_ERROR_CHECK(cudaSetDevice(device));
  }

  ~with_device() {
    if (original_device_ != -1) {
      CUDA_ERROR_CHECK(cudaSetDevice(original_device_));
    }
  }

private:
  int original_device_;
};

class NVTXRange {
public:
  NVTXRange(const std::string &range_name) { nvtxRangePushA(range_name.c_str()); }

  ~NVTXRange() { nvtxRangePop(); }
};

#define NVTX_RANGE_FUNCTION NVTXRange __FUNCTION__##_range(__FUNCTION__);

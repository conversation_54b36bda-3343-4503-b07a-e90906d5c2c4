
#include <opencv2/core.hpp>
#include <opencv2/core/cuda.hpp>

namespace lib {
namespace common {

// https://github.com/opencv/opencv/blob/09c71aed141210bf2b14582974ed9d231c24edd5/modules/core/src/cuda/gpu_mat.cu#L130
class OpenCVAllocator : public cv::cuda::GpuMat::Allocator {
public:
  OpenCVAllocator();
  bool allocate(cv::cuda::GpuMat *mat, int rows, int cols, size_t elemSize) CV_OVERRIDE;
  void free(cv::cuda::GpuMat *mat) CV_OVERRIDE;

  static OpenCVAllocator &get();
};

} // namespace common
} // namespace lib
#pragma once

#include <fmt/format.h>
#include <fmt/ostream.h>
#include <opencv2/imgproc.hpp>
#include <torch/torch.h>

#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace common {

inline torch::ScalarType torch_type_from_opencv(int type) {
  switch (CV_MAT_DEPTH(type)) {
  case CV_8U:
    return torch::kU8;
  case CV_8S:
    return torch::kI8;
  case CV_16S:
    return torch::kI16;
  case CV_16F:
    return torch::kF16;
  case CV_32S:
    return torch::kI32;
  case CV_32F:
    return torch::kF32;
  case CV_64F:
    return torch::kF64;

  default:
    throw maka_error("Unknown opencv type");
  }
}

inline int opencv_type_from_torch(torch::ScalarType type) {
  switch (type) {
  case torch::kU8:
    return CV_8U;
  case torch::kI8:
    return CV_8S;
  case torch::kI16:
    return CV_16S;
  case torch::kF16:
    return CV_16F;
  case torch::kI32:
    return CV_32S;
  case torch::kF32:
    return CV_32F;
  case torch::kF64:
    return CV_64F;

  default:
    throw maka_error("Unknown torch type");
  }
}

inline cv::Mat wrap_to_opencv(torch::Tensor tensor) {
  if (!tensor.is_contiguous()) {
    throw maka_error("Non-contiguous tensors unsupported");
  } else if (tensor.ndimension() != 3) {
    throw maka_error(fmt::format("Unsupported tensor shape {}", tensor.sizes()));
  }

  const int cv_type = opencv_type_from_torch(tensor.scalar_type());

  cv::Mat image_opencv((int)tensor.size(0), (int)tensor.size(1), CV_MAKETYPE(cv_type, (int)tensor.size(2)),
                       tensor.data_ptr());
  return image_opencv;
}

inline torch::Tensor to_tensor(cv::Mat input) {
  std::vector<int64_t> dimensions;
  dimensions.push_back(input.rows);
  dimensions.push_back(input.cols);
  dimensions.push_back(input.channels());

  torch::ScalarType type = torch_type_from_opencv(input.type());

  torch::Tensor output = torch::empty(dimensions, torch::TensorOptions().dtype(type));

  cv::Mat wrap_output = wrap_to_opencv(output);
  input.copyTo(wrap_output);
  return output;
}

} // namespace common
} // namespace lib

#pragma once

#include "lib/common/cpp/exceptions.h"
#include <math.h>
#include <optional>
#include <spdlog/spdlog.h>
#include <vector>

namespace lib {
namespace common {

template <typename T>
class MinMaxHeap {
public:
  MinMaxHeap() {}

  void push(T item) {
    heap_.push_back(item);
    bubble_up((int)heap_.size() - 1);
  }

  T pop_min() {
    if (heap_.size() == 0) {
      throw maka_error("No elements in heap");
    }

    T min_item = heap_[0];

    delete_element(0);

    return min_item;
  }

  T pop_max() {
    if (heap_.size() == 0) {
      throw maka_error("No elements in heap");
    }

    if (heap_.size() == 1) {
      auto item = heap_[0];
      delete_element(0);
      return item;
    }

    int max_position = get_max_position().value();
    T max_item = heap_[max_position];

    delete_element(max_position);

    return max_item;
  }

  std::optional<T> pop_item(T item) {
    if (heap_.size() == 0) {
      throw maka_error("No elements in heap");
    }

    bool item_found = false;

    size_t i;
    for (i = 0; i < heap_.size(); i++) {
      if (item == heap_[i]) {
        item_found = true;
        break;
      }
    }

    if (item_found) {
      T return_item = heap_[i];
      delete_element((int)i);
      return return_item;
    }

    return std::nullopt;
  }

  std::vector<T> enumerate_heap() { return heap_; }

  T peek_max() {
    if (heap_.size() == 0) {
      throw maka_error("No elements in heap");
    }

    auto max_position = get_max_position();

    if (!max_position.has_value()) {
      return heap_[0];
    }

    return heap_[max_position.value()];
  }

  T peek_min() {
    if (heap_.size() == 0) {
      throw maka_error("No elements in heap");
    }

    return heap_[0];
  }

  size_t size() { return heap_.size(); };

  void clear() { heap_.clear(); }

private:
  bool is_min_level(int position) {
    // Even levels are min, odd are max
    int level = (int)floor(log2(position + 1));
    return level % 2 == 0;
  }

  void delete_element(int position) {
    if (position >= (int)heap_.size()) {
      throw maka_error("Deleting element that doesn't exist in heap");
    }

    if (position == (int)heap_.size() - 1) {
      heap_.pop_back();
      return;
    }

    std::swap(heap_[position], heap_[heap_.size() - 1]);
    heap_.pop_back();

    trickle_down(position);
  }

  int get_left_child(int position) { return 2 * position + 1; }
  int get_right_child(int position) { return get_left_child(position) + 1; }
  int get_parent(int position) { return (int)floor((position - 1) / 2); }

  std::vector<int> get_children(int position) {
    auto left_child = get_left_child(position);
    auto right_child = get_right_child(position);

    std::vector<int> children;

    if (left_child < (int)heap_.size()) {
      children.push_back(left_child);
    }

    if (right_child < (int)heap_.size()) {
      children.push_back(right_child);
    }

    return children;
  }

  std::vector<int> get_grandchildren(int position) {
    std::vector<int> grandchildren;
    auto children = get_children(position);
    for (auto child : children) {
      auto gchildren = get_children(child);
      grandchildren.insert(grandchildren.end(), gchildren.begin(), gchildren.end());
    }

    return grandchildren;
  }

  void trickle_down(int position) {
    if (is_min_level(position)) {
      trickle_down_min(position);
    } else {
      trickle_down_max(position);
    }
  }

  void trickle_down_min(int position) {
    auto children = get_children(position);
    auto grandchildren = get_grandchildren(position);
    if (children.size()) {
      auto min_child = get_min_from_positions(children).value();
      auto min_grandchild = get_min_from_positions(grandchildren);
      auto min_descendant = min_child;
      bool min_is_child = true;

      if (min_grandchild.has_value() && heap_[min_grandchild.value()] < heap_[min_descendant]) {
        min_descendant = min_grandchild.value();
        min_is_child = false;
      }

      if (min_is_child) {
        if (heap_[min_descendant] < heap_[position]) {
          std::swap(heap_[min_descendant], heap_[position]);
        }
      } else {
        // else, min is grandchild
        if (heap_[min_descendant] < heap_[position]) {
          std::swap(heap_[min_descendant], heap_[position]);

          if (heap_[min_descendant] > heap_[get_parent(min_descendant)]) {
            std::swap(heap_[min_descendant], heap_[get_parent(min_descendant)]);
          }

          trickle_down_min(min_descendant);
        }
      }
    }
  }

  void trickle_down_max(int position) {
    auto children = get_children(position);
    auto grandchildren = get_grandchildren(position);
    if (children.size()) {
      auto max_child = get_max_from_positions(children).value();
      auto max_grandchild = get_max_from_positions(grandchildren);
      auto max_descendant = max_child;
      bool max_is_child = true;

      if (max_grandchild.has_value() && heap_[max_grandchild.value()] > heap_[max_descendant]) {
        max_descendant = max_grandchild.value();
        max_is_child = false;
      }

      if (max_is_child) {
        if (heap_[max_descendant] > heap_[position]) {
          std::swap(heap_[max_descendant], heap_[position]);
        }
      } else {
        // else, max is grandchild
        if (heap_[max_descendant] > heap_[position]) {
          std::swap(heap_[max_descendant], heap_[position]);

          if (heap_[max_descendant] > heap_[get_parent(max_descendant)]) {
            std::swap(heap_[max_descendant], heap_[get_parent(max_descendant)]);
          }

          trickle_down_max(max_descendant);
        }
      }
    }
  }

  void bubble_up(int position) {
    if (is_min_level(position)) {
      if (get_parent(position) >= 0 && heap_[position] > heap_[get_parent(position)]) {
        std::swap(heap_[position], heap_[get_parent(position)]);
        bubble_up_max(get_parent(position));
      } else {
        bubble_up_min(position);
      }
    } else {
      if (get_parent(position) >= 0 && heap_[position] < heap_[get_parent(position)]) {
        std::swap(heap_[position], heap_[get_parent(position)]);
        bubble_up_min(get_parent(position));
      } else {
        bubble_up_max(position);
      }
    }
  }

  void bubble_up_min(int position) {
    auto parent = get_parent(position);
    if (parent <= 0) {
      return;
    }
    auto grandparent = get_parent(parent);
    if (grandparent >= 0) {
      if (heap_[position] < heap_[grandparent]) {
        std::swap(heap_[position], heap_[grandparent]);
        bubble_up_min(grandparent);
      }
    }
  }

  void bubble_up_max(int position) {
    auto parent = get_parent(position);
    if (parent <= 0) {
      return;
    }
    auto grandparent = get_parent(parent);
    if (grandparent >= 0) {
      if (heap_[position] > heap_[grandparent]) {
        std::swap(heap_[position], heap_[grandparent]);
        bubble_up_max(grandparent);
      }
    }
  }

  std::optional<int> get_max_position() {
    if (heap_.size() == 0) {
      throw maka_error("No elements in heap");
    }

    if (heap_.size() == 1) {
      return 0;
    }

    return get_max_from_positions(get_children(0));
  }

  std::optional<int> get_min_from_positions(std::vector<int> positions) {
    if (heap_.size() == 0) {
      throw maka_error("No elements in heap");
    }

    if (positions.size() == 0) {
      return std::nullopt;
    }

    int min_position = positions[0];

    for (auto position : positions) {
      if (heap_[position] < heap_[min_position]) {
        min_position = position;
      }
    }

    return min_position;
  }

  std::optional<int> get_max_from_positions(std::vector<int> positions) {
    if (heap_.size() == 0) {
      throw maka_error("No elements in heap");
    }

    if (positions.size() == 0) {
      return std::nullopt;
    }

    int max_position = positions[0];

    for (auto position : positions) {
      if (heap_[position] > heap_[max_position]) {
        max_position = position;
      }
    }

    return max_position;
  }

  std::vector<T> heap_;
};

} // namespace common
} // namespace lib

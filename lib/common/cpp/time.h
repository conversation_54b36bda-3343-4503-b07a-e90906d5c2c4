#pragma once

#include <algorithm>
#include <atomic>
#include <chrono>
#include <iomanip>
#include <iostream>
#include <sstream>

inline int64_t maka_control_timestamp_ms() {
  return std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
      .count();
}

template <typename T>
inline std::string iso8601_timestamp(std::chrono::time_point<T> time, bool replace_colon, bool replace_period = false) {
  auto ms = std::chrono::duration_cast<std::chrono::microseconds>(time.time_since_epoch()) % 1000000;
  auto itt = std::chrono::system_clock::to_time_t(time);
  std::string millisecond_delineator = ".";
  if (replace_period) {
    millisecond_delineator = "-";
  }
  std::ostringstream ss;
  ss << std::put_time(gmtime(&itt), "%FT%T");
  ss << millisecond_delineator << std::setfill('0') << std::setw(6) << ms.count() << "Z";
  std::string time_str = ss.str();
  if (replace_colon) {
    std::replace(time_str.begin(), time_str.end(), ':', '-');
  }
  return time_str;
}

inline std::string iso8601_timestamp(bool replace_colon, bool replace_period = false) {
  auto now = std::chrono::system_clock::now();
  return iso8601_timestamp(now, replace_colon, replace_period);
}

inline std::string date_timestamp() {
  auto now = std::chrono::system_clock::now();
  auto in_time_t = std::chrono::system_clock::to_time_t(now);

  std::stringstream ss;
  ss << std::put_time(std::localtime(&in_time_t), "%Y-%m-%d");
  return ss.str();
}

class TimingBlock {
public:
  TimingBlock(std::string description) : begin_(std::chrono::system_clock::now()), description_(description) {}
  ~TimingBlock() {
    std::cout
        << description_ << " took "
        << std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - begin_).count()
        << " ms" << std::endl;
  }

private:
  std::chrono::system_clock::time_point begin_;
  std::string description_;
};

// singleton class to provide a clock that can be controlled for testing
class CarbonClock {
public:
  CarbonClock(const CarbonClock &) = delete;
  CarbonClock &operator=(const CarbonClock &) = delete;

  static CarbonClock &instance() {
    static CarbonClock instance;
    return instance;
  }

  inline int64_t timestamp_ms() {
    if (manual_control_.load(std::memory_order_relaxed)) {
      return timestamp_ms_.load();
    } else {
      return maka_control_timestamp_ms();
    }
  }

  inline int64_t operator+(int64_t ms) { return timestamp_ms_.load() + ms; }
  inline int64_t operator-(int64_t ms) { return timestamp_ms_.load() - ms; }
  inline int64_t operator=(int64_t ms) { return timestamp_ms_ = ms; }
  inline int64_t operator+=(int64_t ms) { return timestamp_ms_ += ms; }
  inline int64_t operator-=(int64_t ms) { return timestamp_ms_ -= ms; }
  inline void set_timestamp_ms(int64_t timestamp_ms) { timestamp_ms_.store(timestamp_ms); }
  inline void set_manual_control(bool manual_control) {
    manual_control_.store(manual_control, std::memory_order_relaxed);
  }

private:
  CarbonClock() : timestamp_ms_(maka_control_timestamp_ms()) {}
  std::atomic<int64_t> timestamp_ms_;
  std::atomic<bool> manual_control_;
};

#pragma once

#include <boost/circular_buffer.hpp>
#include <condition_variable>
#include <fmt/format.h>
#include <functional>
#include <map>
#include <mutex>

#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace common {

template <typename K, typename T>
class FixedBuffer {
public:
  using BufferApplyFunc = std::function<void(const K &, const T &)>;
  FixedBuffer(size_t capacity) : items_(capacity) {}

  void push(K key, T item) {
    if (item_map_.count(key) > 0) {
      throw maka_error(fmt::format("Duplicate key {} pushed in FixedBuffer", key));
    }
    _push(key, item);
  }

  void push_ignore_dup(K key, T item) {
    if (item_map_.count(key) > 0) {
      return;
    }
    _push(key, item);
  }

  T get(K key) {
    std::unique_lock<std::mutex> lck(mutex_);
    auto it = item_map_.find(key);
    if (it == item_map_.end()) {
      throw maka_error(fmt::format("Key {} does not exist in FixedBuffer", key));
    }
    return items_[it->second].second;
  }

  std::optional<T> get_opt(K key) {
    std::unique_lock<std::mutex> lck(mutex_);
    auto it = item_map_.find(key);
    if (it == item_map_.end()) {
      return {};
    }
    return items_[it->second].second;
  }

  T get_next(std::chrono::milliseconds timeout) {
    std::unique_lock<std::mutex> lck(mutex_);
    if (item_added_.wait_for(lck, timeout) == std::cv_status::timeout) {
      throw maka_error("Timeout waiting for next item");
    }
    return items_.back().second;
  }

  T get_latest() {
    if (size() <= 0) {
      throw maka_error("No items in fixed buffer");
    }
    std::unique_lock<std::mutex> lck(mutex_);
    return items_.back().second;
  }

  bool contains(K key) { return item_map_.count(key) != 0; }

  size_t size() { return items_.size(); }

  std::pair<K, T> get_nearest(K key, std::function<float(K, K)> distance_func) {
    std::unique_lock<std::mutex> lck(mutex_);
    if (items_.size() == 0) {
      throw maka_error("No items in fixed buffer");
    }

    K best_key = items_.front().first;

    for (auto &item : items_) {
      if (distance_func(item.first, key) < distance_func(best_key, key)) {
        best_key = item.first;
      }
    }

    auto it = item_map_.find(best_key);

    return {best_key, items_[it->second].second};
  }

  std::vector<K> keys() {
    std::vector<K> retval;
    std::unique_lock<std::mutex> lck(mutex_);
    for (auto const &element : item_map_) {
      retval.push_back(element.first);
    }
    return retval;
  }
  void clear() {
    const std::unique_lock<std::mutex> lck(mutex_);
    items_.clear();
    item_map_.clear();
  }
  void apply(BufferApplyFunc func) {
    const std::unique_lock<std::mutex> lck(mutex_);
    for (const auto &kv : item_map_) {
      func(kv.first, items_[kv.second].second);
    }
  }

private:
  boost::circular_buffer<std::pair<K, T>> items_;
  std::map<K, size_t> item_map_;
  std::mutex mutex_;
  std::condition_variable item_added_;

  void _push(K key, T item) {
    {
      std::unique_lock<std::mutex> lck(mutex_);
      if (items_.full()) {
        item_map_.erase(items_.begin()->first);
        for (auto &kv : item_map_) {
          --kv.second;
        }
      }
      items_.push_back({key, item});
      item_map_[key] = items_.size() - 1;
    }
    item_added_.notify_all();
  }
};

} // namespace common
} // namespace lib

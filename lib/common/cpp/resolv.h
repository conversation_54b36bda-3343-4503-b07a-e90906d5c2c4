#include <arpa/inet.h>
#include <netdb.h>
#include <netinet/in.h>
#include <string>
#include <sys/socket.h>

#include <fmt/format.h>
#include <spdlog/spdlog.h>

#include "exceptions.h"

namespace {
constexpr size_t kDnsBufferSize = 256;
}

inline std::string dns_resolve(const std::string &hostname) {
  struct hostent hbuf;
  struct hostent *r = nullptr;
  int herr;
  char buf[kDnsBufferSize];
  if (gethostbyname2_r(hostname.c_str(), AF_INET, &hbuf, buf, kDnsBufferSize, &r, &herr) < 0 || r == nullptr) {
    throw maka_error(fmt::format("Could not resolve hostname {}: errno {}", hostname, herr));
  }
  return inet_ntop(AF_INET, ((struct in_addr **)r->h_addr_list)[0], buf, kDnsBufferSize);
}
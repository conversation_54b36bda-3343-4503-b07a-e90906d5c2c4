from abc import ABC, abstractmethod
from datetime import datetime
from time import time
from typing import Optional

# Note: Don't use requirements from pip in this file. It is designed to work out of box with python 3.6


class TimeoutException(Exception):
    pass


def maka_control_timestamp_ms() -> int:
    """
    Generates a control-time timestamp. This is the number of milliseconds since UNIX epoch,
    expressed as an integer. Finer precision than this for control logic use should not be necessary
    and only contributes to complicated floating point handling.

    https://stackoverflow.com/questions/5998245/get-current-time-in-milliseconds-in-python argue
    for using datetime instead of time() for obtaining higher precision timestamp.

    Returns:
        The number of milliseconds since UNIX epoch as an integer.
    """
    return int((datetime.utcnow() - datetime(1970, 1, 1)).total_seconds() * 1000)


def epoch_ms() -> int:
    return int(time() * 1000)


def get_datetime(timestamp: str) -> datetime:

    if "." in timestamp:
        time = datetime.strptime(timestamp, "%Y-%m-%dT%H-%M-%S.%fZ")
    else:
        time = datetime.strptime(timestamp, "%Y-%m-%dT%H-%M-%S-%fZ")

    return time


def image_timestamp_to_control_timestamp_ms(timestamp: str) -> int:
    """
    Convert an image timestamp into a Maka control timestamp (milliseconds from UNIX epoch).

    Parameters:
        timestamp (str): The timestamp to convert like 2020-10-22T22-55-46.767227Z

    Returns:
        Number of milliseconds from UNIX epoch.
    """
    time = get_datetime(timestamp)

    return int(time.timestamp() * 1000)


def format_seconds(s: float) -> str:
    """Format seconds as HH:MM:SS"""
    assert s >= 0, "seconds should be positive. Got: %s" % str(s)
    hours = s // 3600
    s = s - (hours * 3600)
    minutes = s // 60
    seconds = s - (minutes * 60)
    return "{:02}:{:02}:{:02}".format(int(hours), int(minutes), int(seconds))


# You should always use this
# Avoid local time zones whenever possible
def iso8601_timestamp(ms: bool = True, replace_colon: bool = False, seconds: Optional[float] = None) -> str:
    """
    https://en.wikipedia.org/wiki/ISO_8601

    Date                    2019-11-22
    Date and time in UTC    2019-11-22T04:57:59+00:00
                            2019-11-22T04:57:59Z
                            20191122T045759Z
    Week                    2019-W47
    Date with week number   2019-W47-5
    Date without year       --11-22
    Ordinal date            2019-326=
    -------
    :param ms: whether to add millis
    """
    when: Optional[datetime] = None
    if seconds:
        when = datetime.fromtimestamp(seconds)
    else:
        when = datetime.utcnow()

    iso_8601_with_millis = when.isoformat()
    if not ms:
        result = iso_8601_with_millis[:19]
    else:
        result = iso_8601_with_millis
    if replace_colon:
        result = result.replace(":", "-")

    return result.replace(".", "-") + "Z"


def timestamp_filename(prefix: str, ext: str = "", ms: bool = True, ts: Optional[float] = None) -> str:
    # Windows doesn't like : in filenames.
    time_fname = iso8601_timestamp(ms=ms, replace_colon=True, seconds=ts)
    return f"{prefix}.{time_fname}{ext}"


class Timestamp(ABC):
    """
    The base timestamp interface upon which all time-based typing is expected to be built.

    Everything ends here, at this simple interface.

    It is up to the client to decide how to provide the timestamp_ms.
    """

    @property
    @abstractmethod
    def timestamp_ms(self) -> int:
        pass


class TimestampedObject(Timestamp):
    """
    An object that has a timestamp
    """

    def __init__(self, *, timestamp_ms: Optional[int]):
        if timestamp_ms is not None:
            assert timestamp_ms >= 0
            self._timestamp_ms = timestamp_ms
        else:
            self._timestamp_ms = maka_control_timestamp_ms()

    @property
    def timestamp_ms(self) -> int:
        return self._timestamp_ms

    @property
    def timestamp_sec(self) -> float:
        return self._timestamp_ms / 1000

import asyncio
import time

from lib.common.logging import get_logger

sleep_detune: float = 1.0

LOG = get_logger(__name__)


def set_sleep_detune(divisor: float) -> None:
    global sleep_detune
    sleep_detune = divisor


def get_sleep_detune() -> float:
    global sleep_detune
    return sleep_detune


def sleep_ms(ms: float) -> None:
    time.sleep((ms / 1000.0) * sleep_detune)


async def async_sleep_ms(ms: float) -> None:
    await asyncio.sleep((ms / 1000.0) * sleep_detune)

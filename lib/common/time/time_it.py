from functools import wraps
from time import perf_counter
from typing import Any, Callable, Dict, Tuple, TypeVar

RT = TypeVar("RT")  # return type


def time_it(func: Callable[..., RT]) -> Callable[..., RT]:
    @wraps(func)
    def wrapper(*args: Tuple[Any, ...], **kwargs: Dict[str, Any]) -> RT:
        start = perf_counter()
        out = func(*args, **kwargs)
        total = (perf_counter() - start) * 1000
        print(f"Took {total} ms to run {func.__name__}")
        return out

    return wrapper

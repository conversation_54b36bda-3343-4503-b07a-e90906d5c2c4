import abc
import datetime
from typing import Any, Callable, Dict, Optional, Tuple, cast

import cv2
import numpy as np
import numpy.typing as npt

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)

# fmt: off
BGR_BLACK          = (  0,   0,   0)  # noqa
BGR_BLUE           = (255,   0,   0)  # noqa
BGR_CYAN           = (255, 255,   0)  # noqa
BGR_CYAN_MEDIUM    = (192, 192,   0)  # noqa
BGR_GREEN          = (  0, 255,   0)  # noqa
BGR_GREEN_MEDIUM   = (  0, 192,   0)  # noqa
BGR_GREEN_DARK     = (  0, 128,   0)  # noqa
BGR_PINK           = (255,  20, 200)  # noqa
BGR_PURPLE         = (255,   0, 255)  # noqa
BGR_RED            = (  0,   0, 255)  # noqa
BGR_YELLOW         = (  0, 255, 255)  # noqa
BGR_WHITE          = (255, 255, 255)  # noqa
BGR_BROWN          = (140, 180, 210)  # noqa
RGB_BLACK          = (  0,   0,   0)  # noqa
RGB_GREY           = ( 64,  64,  64)  # noqa
BGR_GREY = RGB_GREY
# fmt: on


def format_time_text(*, timestamp_ms: int, ymd: bool = True) -> str:
    return f"{datetime.datetime.utcfromtimestamp(timestamp_ms / 1000).isoformat(sep=' ', timespec='milliseconds')[(0 if ymd else 11):]}Z"


def timestamp(image: npt.NDArray[Any], timestamp_ms: Optional[int] = None) -> npt.NDArray[Any]:
    if timestamp_ms is not None:
        timestamp_text = format_time_text(timestamp_ms=timestamp_ms)
    else:
        timestamp_text = str(datetime.datetime.now())

    annotate_text_line(image, timestamp_text, y_index=0, color=BGR_PINK)
    return image


LINE_HEIGHT = 40


def annotate_text_line(
    image: npt.NDArray[Any],
    text: str,
    y_index: int = 0,
    color: Tuple[int, int, int] = BGR_GREEN,
    x_center: bool = False,
) -> None:
    # org: It is the coordinates of the bottom-left corner of the text string in the image.
    # The coordinates are represented as tuples of two values i.e. (X coordinate value, Y coordinate value).
    x: int = 5 if not x_center else int(image.shape[1] / 2)
    y: int = (y_index + 1) * LINE_HEIGHT
    org: Tuple[int, int] = (x, y)
    cv2.putText(
        image,
        text=text,
        org=org,
        fontFace=cv2.FONT_HERSHEY_COMPLEX_SMALL,
        fontScale=1.25,
        color=color,
        thickness=2,
        bottomLeftOrigin=False,
    )


def format_float_text(v: float, decimals: int = 4, positive_symbol: str = " ") -> str:
    decimals_str = "0" * decimals
    if int(v) != v:
        decimals_str = str(round(v, decimals)).split(".")[-1]
        while len(decimals_str) < decimals:
            decimals_str = decimals_str + "0"

    return f"{positive_symbol if v >= 0 else '-'}{int(abs(v))}.{decimals_str}"


def circle(
    img: npt.NDArray[Any], center: Tuple[float, float], radius: float, *argv: Any, **kwargs: Any
) -> npt.NDArray[Any]:
    """
    float-safe wrapper around cv2.circle
    """
    assert len(center) == 2
    center = int(round(center[0])), int(round(center[1]))
    radius = int(round(radius))

    return cast(npt.NDArray[Any], cv2.circle(img, center, radius, *argv, **kwargs))


def arrowedLine(
    img: npt.NDArray[Any], origin: Tuple[float, float], destination: Tuple[float, float], *args: Any, **kwargs: Any
) -> npt.NDArray[Any]:
    """
    float-safe wrapper around cv2.arrowedLine
    """
    assert len(origin) == 2
    assert len(destination) == 2
    origin = int(round(origin[0])), int(round(origin[1]))
    destination = int(round(destination[0])), int(round(destination[1]))
    return cast(npt.NDArray[Any], cv2.arrowedLine(img, origin, destination, *args, **kwargs))


def draw_vector_field(
    img: npt.NDArray[Any],
    flow: npt.NDArray[Any],
    num_vectors_to_draw_per_dimension: int = 3,
    scalar: float = 1,
    thickness: int = 4,
    color: Tuple[int, int, int] = BGR_CYAN,
) -> npt.NDArray[Any]:
    """
    :param img: The image to draw on
    :param flow: The base vector field
    :param draw_resolution: The number of vectors to draw for each of the x and y. So if the value is 3, it draws a 3x3 grid of vectors.
    :return:
    """

    # count the grid
    num_partitions_y, num_partitions_x = (
        int(flow.shape[0] / num_vectors_to_draw_per_dimension),
        int(flow.shape[1] / num_vectors_to_draw_per_dimension),
    )
    divisor_height_width = img.shape[1] / flow.shape[1], img.shape[0] / flow.shape[0]

    class VectorDrawer:
        """
        Draws the vector for the x/y grid coordinate.
        """

        def __init__(self, *, xi: int, yi: int, color: Tuple[int, int, int] = color, thickness: int = thickness):
            x_low, x_mid, x_hi = VectorDrawer._partition_start_mid_end(partition_size=num_partitions_x, index=xi)
            y_low, y_mid, y_hi = VectorDrawer._partition_start_mid_end(partition_size=num_partitions_y, index=yi)

            # flow.shape == (Y, X, 2)
            assert flow.shape[0] >= y_hi, f"flow shape: {flow.shape} has y shape < y_hi={y_hi}"
            assert flow.shape[1] >= x_hi, f"flow shape: {flow.shape} has x shape < x_hi={x_hi}"
            flow_section = flow[y_low:y_hi, x_low:x_hi, :]
            flow_section_mean_yx = np.mean(np.mean(flow_section, axis=1), axis=0)
            vector_length = scalar * flow_section_mean_yx

            self._origin_xy = int(round(divisor_height_width[1] * x_mid)), int(round(divisor_height_width[0] * y_mid))
            self._dest_xy = (
                int(round(self._origin_xy[0] + vector_length[0])),
                int(round(self._origin_xy[1] + vector_length[1])),
            )
            self._thickness = thickness
            self._color = color

        @staticmethod
        def _partition_start_mid_end(partition_size: int, index: int) -> Tuple[int, float, int]:
            start = index * partition_size
            mid = start + 0.5 * partition_size
            end = start + partition_size
            return start, mid, end

        def draw(self, draw_on_image: npt.NDArray[Any]) -> None:
            if self._origin_xy != self._dest_xy:
                arrowedLine(
                    img=draw_on_image,
                    origin=self._origin_xy,
                    destination=self._dest_xy,
                    color=self._color,
                    thickness=self._thickness,
                )
            else:
                circle(
                    img=draw_on_image,
                    center=self._origin_xy,
                    color=self._color,
                    radius=self._thickness,
                    thickness=self._thickness,
                )

    for xi in range(num_vectors_to_draw_per_dimension):
        for yi in range(num_vectors_to_draw_per_dimension):
            VectorDrawer(xi=xi, yi=yi).draw(img)

    return img


Color = Tuple[int, int, int]


class Text:
    def __init__(self, *, text: str, color: Color):
        self._text: str = text
        self._color: Color = color

    @property
    def text(self) -> str:
        return self._text

    @property
    def color(self) -> Color:
        return self._color


def draw_text(image: npt.NDArray[Any], text: Text, y_index: int = 0, x_center: bool = False) -> None:
    annotate_text_line(image=image, text=text.text, color=text.color, y_index=y_index, x_center=x_center)


class Annotation:
    """
    Abstraction for drawing an annotation on an image. Wraps a callable with extra state, e.g. name/layer/enabled.

    I like the following annotation style: https://twitter.com/TheTeslaShow/status/1223049982191685633
        and we should build towards that visual styling / consistency
    """

    def __init__(self, name: str, layer: int, f: Callable[..., Any], enabled: bool = True):
        self._name: str = name
        self._layer: int = layer
        self._f = f
        self._enabled: bool = enabled

    @property
    def name(self) -> str:
        return self._name

    @property
    def layer(self) -> int:
        return self._layer

    @property
    def enabled(self) -> bool:
        return self._enabled

    @property
    def json(self) -> Dict[str, Any]:
        return {"name": self.name, "level": self.layer, "enabled": self.enabled}

    def enable(self) -> None:
        self._enabled = True

    def set_enabled(self, val: bool) -> None:
        self._enabled = val

    def disable(self) -> None:
        self._enabled = False

    def toggle(self) -> None:
        self._enabled = not self._enabled

    def __str__(self) -> str:
        return "Annotation[name={}, layer={}, enabled={}, f={}]".format(self._name, self._layer, self._enabled, self._f)

    def apply(self, *argv: Any, **kwargs: Any) -> Any:
        return self._f(*argv, **kwargs)

    def safe_apply(self, *argv: Any, **kwargs: Any) -> Any:
        try:
            return self._f(*argv, **kwargs)
        except Exception:
            LOG.exception("Annotation Failure: {}".format(self))

    def apply_if_enabled(self, *argv: Any, **kwargs: Any) -> Optional[Any]:
        if self._enabled:
            return self.apply(*argv, **kwargs)
        return None

    def safe_apply_if_enabled(self, *argv: Any, **kwargs: Any) -> Optional[Any]:
        if self._enabled:
            return self.safe_apply(*argv, **kwargs)
        return None


class Annotator(abc.ABC):
    """
    A base class that does annotation
    """

    @abc.abstractmethod
    def attach(self) -> None:
        pass

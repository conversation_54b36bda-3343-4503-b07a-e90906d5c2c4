import sys
from typing import Any, Callable, Iterable, List, Optional

_valid = {"yes": True, "ye": True, "y": True, "no": False, "n": False}

"""
ask 'prompt' until constructor(answer) doesn't except, return the result
if nothing, constructor(default) is returned.
    therefore, default should be a string displayable to the user
if no constructor, identity is used and any input is valid
if no default is specified, ask until the user says something.

if constructor(default) is broken, asks continuously. Probably not what you want.
"""


def query(prompt: str, default: Any = None, constructor: Optional[Callable[[Any], Any]] = None) -> Any:
    if constructor is None:
        constructor = lambda _: _
    if default is not None and default != "":  # '' can be used for things to which "nothing" is an answer
        prompt = prompt + " (" + str(default) + ")"
    prompt = prompt + "? "
    while True:
        sys.stdout.write(prompt)
        inp = input()
        try:
            if inp == "" and default is not None:
                answer = default
            elif inp == "":
                continue
            else:
                answer = inp
            return constructor(answer)
        except Exception as e:
            print("{}: {}".format(type(e).__name__, e))


def query_yes_no(prompt: str, default: Any) -> Any:
    return query(prompt + " [y/n]", default, constructor=lambda s: _valid[s.lower()])


def query_integer(prompt: str, default: Any = None) -> Any:
    return query(prompt, default, constructor=int)


# choices is an iterable, one element of which will be returned
def query_choice(prompt: str, choices: Iterable[Any], default: Any = None) -> Any:
    choices_by_idx = {str(k): v for k, v in enumerate(choices)}
    def_idx = None
    if default:
        for k, v in choices_by_idx.items():
            if v == default:
                def_idx = k
                break
    prompt += "\n" + (
        "[No options]"
        if not choices
        else "\n".join(("{}{}: {}".format("*" if k == def_idx else " ", k, v) for k, v in choices_by_idx.items()))
    )
    return query(prompt, default=def_idx, constructor=lambda idx: choices_by_idx[idx])


def query_list_choice(
    named_thing: str, list_choices: List[Any], current: Any = None, exclude_indices: Optional[Iterable[int]] = None
) -> Any:
    """
    Continually prompt until user selects one of the list choices or empty for None.
    """
    exclude_indices = exclude_indices or []
    val = None
    bad_choice_msg = "Invalid choice! Please type selected index {}".format(
        [i for i in range(len(list_choices)) if i not in exclude_indices]
    )

    while val is None or not (0 <= val < len(list_choices)):
        for i, element in enumerate(list_choices):
            if exclude_indices is not None and i in exclude_indices:
                continue
            print("%d:    %s" % (i, element))
        current_str = ""
        if current is not None:
            current_str = "current is {}, ".format(current)
        prompt = "Please chose one of {} from the list ({}empty for None): ".format(named_thing, current_str)
        val = input(prompt)
        if val == "":
            val = None
        if val is not None:
            try:
                val = int(val)  # type: ignore
            except Exception:
                print(bad_choice_msg)
                val = None
        if val is not None and (val in exclude_indices or not (0 <= val < len(list_choices))):  # type: ignore
            print(bad_choice_msg)
            val = None

    return val

from typing import Type, TypeVar

T = TypeVar("T", bound="Pressure")

# convert bar -> Pa
BAR_MULTIPLIER = 100000
# convert psi -> Pa
PSI_MULTIPLIER = 6894.7572931783


class Pressure:
    def __init__(self, val: float) -> None:
        """Do not use directly, instead use the from_* class methods"""
        self._val = val  # stored internally as Pascal (Pa)

    @classmethod
    def from_pa(cls: Type[T], val: float) -> T:
        return cls(val)

    @classmethod
    def from_hpa(cls: Type[T], val: float) -> T:
        return cls(val * 100)

    @classmethod
    def from_kpa(cls: Type[T], val: float) -> T:
        return cls(val * 1000)

    @classmethod
    def from_bar(cls: Type[T], val: float) -> T:
        return cls(val * BAR_MULTIPLIER)

    @classmethod
    def from_psi(cls: Type[T], val: float) -> T:
        return cls(val * PSI_MULTIPLIER)

    @property
    def pa(self) -> float:
        return self._val

    @pa.setter
    def pa(self, val: float) -> None:
        self._val = val

    @property
    def hpa(self) -> float:
        return self._val / 100.0

    @hpa.setter
    def hpa(self, val: float) -> None:
        self._val = val * 100.0

    @property
    def kpa(self) -> float:
        return self._val / 1000.0

    @kpa.setter
    def kpa(self, val: float) -> None:
        self._val = val * 1000.0

    @property
    def bar(self) -> float:
        return self._val / BAR_MULTIPLIER

    @bar.setter
    def bar(self, val: float) -> None:
        self._val = val * BAR_MULTIPLIER

    @property
    def psi(self) -> float:
        return self._val / PSI_MULTIPLIER

    @psi.setter
    def psi(self, val: float) -> None:
        self._val = val * PSI_MULTIPLIER

    def __gt__(self, rhs: "Pressure") -> bool:
        return self._val > rhs._val

    def __ge__(self, rhs: "Pressure") -> bool:
        return self._val >= rhs._val

    def __lt__(self, rhs: "Pressure") -> bool:
        return self._val < rhs._val

    def __le__(self, rhs: "Pressure") -> bool:
        return self._val <= rhs._val

    def __eq__(self, rhs: object) -> bool:
        if not isinstance(rhs, Pressure):
            raise NotImplementedError()
        return self._val == rhs._val

    def __repr__(self) -> str:
        if self._val < 10000:
            return f"{self.pa:.3f} Pa"
        elif self._val >= 10000 and self._val < 1000000:
            return f"{self.hpa:.3f} hPa"
        else:
            return f"{self.kpa:.3f} kPa"

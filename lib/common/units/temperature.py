from typing import Type, TypeVar

T = TypeVar("T", bound="Temperature")

# multiply °C by this to get °F (after adding offset)
DEG_F_MULTIPLIER = 1.8
# °F for 0°C
DEG_F_OFFSET = 32


class Temperature:
    def __init__(self, val: float) -> None:
        """Do not use directly, instead use the from_* class methods"""
        self._val = val  # stored internally as °C

    @classmethod
    def from_c(cls: Type[T], val: float) -> T:
        return cls(val)

    @classmethod
    def from_f(cls: Type[T], val: float) -> T:
        return cls((val - DEG_F_OFFSET) / DEG_F_MULTIPLIER)

    @property
    def deg_c(self) -> float:
        return self._val

    @deg_c.setter
    def deg_c(self, val: float) -> None:
        self._val = val

    @property
    def deg_f(self) -> float:
        return (self._val * DEG_F_MULTIPLIER) + DEG_F_OFFSET

    @deg_f.setter
    def deg_f(self, val: float) -> None:
        self._val = (val - DEG_F_OFFSET) / DEG_F_MULTIPLIER

    def __gt__(self, rhs: "Temperature") -> bool:
        return self._val > rhs._val

    def __ge__(self, rhs: "Temperature") -> bool:
        return self._val >= rhs._val

    def __lt__(self, rhs: "Temperature") -> bool:
        return self._val < rhs._val

    def __le__(self, rhs: "Temperature") -> bool:
        return self._val <= rhs._val

    def __eq__(self, rhs: object) -> bool:
        if not isinstance(rhs, Temperature):
            raise NotImplementedError()
        return self._val == rhs._val

    def __repr__(self) -> str:
        return f"{self._val:.2f} °C"

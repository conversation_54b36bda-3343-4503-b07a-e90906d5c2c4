from typing import Type, TypeVar

T = TypeVar("T", bound="Duration")

MSEC_PER_SECOND = 1000
USEC_PER_MSEC = 1000


class Duration:
    def __init__(self, val: float) -> None:
        """Do not use directly, instead use the from_* class methods"""
        self._val = val  # stored internally as msec

    @classmethod
    def from_seconds(cls: Type[T], val: float) -> T:
        return cls(val * MSEC_PER_SECOND)

    @classmethod
    def from_milliseconds(cls: Type[T], val: float) -> T:
        return cls(val)

    @classmethod
    def from_microseconds(cls: Type[T], val: float) -> T:
        return cls(val / USEC_PER_MSEC)

    @classmethod
    def from_hz(cls: Type[T], val: float) -> T:
        return cls((1 / val) * MSEC_PER_SECOND)

    @property
    def seconds(self) -> float:
        return self._val / MSEC_PER_SECOND

    @seconds.setter
    def seconds(self, val: float) -> None:
        self._val = val * MSEC_PER_SECOND

    @property
    def milliseconds(self) -> float:
        return self._val

    @milliseconds.setter
    def milliseconds(self, val: float) -> None:
        self._val = val

    @property
    def microseconds(self) -> float:
        return self._val * USEC_PER_MSEC

    @microseconds.setter
    def microseconds(self, val: float) -> None:
        self._val = val / USEC_PER_MSEC

    @property
    def hz(self) -> float:
        return 1 / (self._val / MSEC_PER_SECOND)

    @hz.setter
    def hz(self, val: float) -> None:
        self._val = (1 / val) * MSEC_PER_SECOND

    def __gt__(self, rhs: "Duration") -> bool:
        return self._val > rhs._val

    def __ge__(self, rhs: "Duration") -> bool:
        return self._val >= rhs._val

    def __lt__(self, rhs: "Duration") -> bool:
        return self._val < rhs._val

    def __le__(self, rhs: "Duration") -> bool:
        return self._val <= rhs._val

    def __eq__(self, rhs: object) -> bool:
        if not isinstance(rhs, Duration):
            raise NotImplementedError()
        return self._val == rhs._val

    def __repr__(self) -> str:
        if self._val < 1:
            return f"{self.microseconds:.3f} µS"
        elif self._val >= 1 and self._val < 1000:
            return f"{self.milliseconds:.3f} ms"
        else:
            return f"{self.seconds:.3f} sec"

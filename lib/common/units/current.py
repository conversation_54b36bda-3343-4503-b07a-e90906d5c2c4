from typing import Type, TypeVar

T = TypeVar("T", bound="Current")


class Current:
    def __init__(self, val: float) -> None:
        """Do not use directly, instead use the from_* class methods"""
        self._val = val  # stored internally as amps

    @classmethod
    def from_amps(cls: Type[T], val: float) -> T:
        return cls(val)

    @classmethod
    def from_milliamps(cls: Type[T], val: float) -> T:
        return cls(val / 1000.0)

    @property
    def amps(self) -> float:
        return self._val

    @amps.setter
    def amps(self, val: float) -> None:
        self._val = val

    @property
    def milliamps(self) -> float:
        return self._val * 1000.0

    @milliamps.setter
    def milliamps(self, val: float) -> None:
        self._val = val / 1000.0

    def __gt__(self, rhs: "Current") -> bool:
        return self._val > rhs._val

    def __ge__(self, rhs: "Current") -> bool:
        return self._val >= rhs._val

    def __lt__(self, rhs: "Current") -> bool:
        return self._val < rhs._val

    def __le__(self, rhs: "Current") -> bool:
        return self._val <= rhs._val

    def __eq__(self, rhs: object) -> bool:
        if not isinstance(rhs, Current):
            raise NotImplementedError()
        return self._val == rhs._val

    def __repr__(self) -> str:
        if self._val >= 1.0:
            return f"{self.amps:.3f} A"
        else:
            return f"{self.milliamps:.3f} mA"

from typing import Type, TypeVar

T = TypeVar("T", bound="Voltage")


class Voltage:
    def __init__(self, val: float) -> None:
        """Do not use directly, instead use the from_* class methods"""
        self._val = val  # stored internally as volts

    @classmethod
    def from_volts(cls: Type[T], val: float) -> T:
        return cls(val)

    @classmethod
    def from_millivolts(cls: Type[T], val: float) -> T:
        return cls(val / 1000.0)

    @property
    def volts(self) -> float:
        return self._val

    @volts.setter
    def volts(self, val: float) -> None:
        self._val = val

    @property
    def millivolts(self) -> float:
        return self._val * 1000.0

    @millivolts.setter
    def millivolts(self, val: float) -> None:
        self._val = val / 1000.0

    def __gt__(self, rhs: "Voltage") -> bool:
        return self._val > rhs._val

    def __ge__(self, rhs: "Voltage") -> bool:
        return self._val >= rhs._val

    def __lt__(self, rhs: "Voltage") -> bool:
        return self._val < rhs._val

    def __le__(self, rhs: "Voltage") -> bool:
        return self._val <= rhs._val

    def __eq__(self, rhs: object) -> bool:
        if not isinstance(rhs, Voltage):
            raise NotImplementedError()
        return self._val == rhs._val

    def __repr__(self) -> str:
        if self._val >= 1.0:
            return f"{self.volts:.3f} V"
        else:
            return f"{self.millivolts:.3f} mV"

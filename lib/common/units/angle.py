import math
from typing import Type, TypeVar

T = TypeVar("T", bound="Angle")


class Angle:
    def __init__(self, val: float) -> None:
        """Do not use directly, instead use the from_* class methods"""
        self._val = val  # stored as degrees

    @classmethod
    def from_degrees(cls: Type[T], val: float) -> T:
        return cls(val)

    @classmethod
    def from_radians(cls: Type[T], val: float) -> T:
        return cls(math.degrees(val))

    @property
    def degrees(self) -> float:
        return self._val

    @degrees.setter
    def degrees(self, val: float) -> None:
        self._val = val

    @property
    def radians(self) -> float:
        return math.radians(self._val)

    @radians.setter
    def radians(self, val: float) -> None:
        self._val = math.degrees(val)

    def half_circle_normalize(self) -> None:
        if self._val > 180:
            self._val -= 360
        elif self._val < -180:
            self._val += 360

    def copy(self) -> "Angle":
        return Angle(self._val)

    def __gt__(self, rhs: "Angle") -> bool:
        return self._val > rhs._val

    def __ge__(self, rhs: "Angle") -> bool:
        return self._val >= rhs._val

    def __lt__(self, rhs: "Angle") -> bool:
        return self._val < rhs._val

    def __le__(self, rhs: "Angle") -> bool:
        return self._val <= rhs._val

    def __eq__(self, rhs: object) -> bool:
        if not isinstance(rhs, Angle):
            raise NotImplementedError()
        return self._val == rhs._val

    def __mul__(self, rhs: float) -> "Angle":
        return Angle(self._val * rhs)

    def __truediv__(self, rhs: float) -> "Angle":
        return Angle(self._val / rhs)

    def __imul__(self, rhs: float) -> "Angle":
        self._val *= rhs
        return self

    def __iadd__(self, rhs: "Angle") -> "Angle":
        self._val += rhs._val
        return self

    def __isub__(self, rhs: "Angle") -> "Angle":
        self._val -= rhs._val
        return self

    def __add__(self, rhs: "Angle") -> "Angle":
        return Angle(self._val + rhs._val)

    def __sub__(self, rhs: "Angle") -> "Angle":
        return Angle(self._val - rhs._val)

    def __neg__(self) -> "Angle":
        return Angle(-self._val)

    def __abs__(self) -> "Angle":
        return Angle(abs(self._val))

    def __repr__(self) -> str:
        return self.__str__()

    def __str__(self) -> str:
        return f"Angle({self.degrees} degrees)"


RIGHT_ANGLE = Angle.from_degrees(90)


def min_circular_dist(a: Angle, b: Angle) -> Angle:
    a_deg = a.degrees % 360
    b_deg = b.degrees % 360
    c = (b_deg - a_deg) % 360
    if c > 180:
        return Angle.from_degrees(c - 360)
    return Angle.from_degrees(c)

from typing import Type, TypeVar

T = TypeVar("T", bound="Speed")
MPH_TO_MS_MULT = 0.44704


class Speed:
    def __init__(self, val: float) -> None:
        """Do not use directly, instead use the from_* class methods"""
        self._val = val  # stored as m/s

    def copy(self) -> "Speed":
        return Speed(self._val)

    @classmethod
    def from_mph(cls: Type[T], val: float) -> T:
        return cls(val * MPH_TO_MS_MULT)

    @classmethod
    def from_meters_per_sec(cls: Type[T], val: float) -> T:
        return cls(val)

    @classmethod
    def from_mm_per_ms(cls: Type[T], val: float) -> T:
        return cls(val)

    @property
    def mph(self) -> float:
        return self._val / MPH_TO_MS_MULT

    @mph.setter
    def mph(self, val: float) -> None:
        self._val = val * MPH_TO_MS_MULT

    @property
    def meters_per_sec(self) -> float:
        return self._val

    @meters_per_sec.setter
    def meters_per_sec(self, val: float) -> None:
        self._val = val

    @property
    def mm_per_ms(self) -> float:
        return self._val

    @mm_per_ms.setter
    def mm_per_ms(self, val: float) -> None:
        self._val = val

    @property
    def mm_per_sec(self) -> float:
        return self._val * 1000

    @mm_per_sec.setter
    def mm_per_sec(self, val: float) -> None:
        self._val = val / 1000

    def __gt__(self, rhs: "Speed") -> bool:
        return self._val > rhs._val

    def __ge__(self, rhs: "Speed") -> bool:
        return self._val >= rhs._val

    def __lt__(self, rhs: "Speed") -> bool:
        return self._val < rhs._val

    def __le__(self, rhs: "Speed") -> bool:
        return self._val <= rhs._val

    def __eq__(self, rhs: object) -> bool:
        if not isinstance(rhs, Speed):
            raise NotImplementedError()
        return self._val == rhs._val

    def __repr__(self) -> str:
        return self.__str__()

    def __str__(self) -> str:
        return f"Speed({self.meters_per_sec} meters/sec)"

    def __mul__(self, rhs: float) -> "Speed":
        return Speed(self._val * rhs)

    def __truediv__(self, rhs: float) -> "Speed":
        return Speed(self._val / rhs)

    def __imul__(self, rhs: float) -> "Speed":
        self._val *= rhs
        return self

    def __iadd__(self, rhs: "Speed") -> "Speed":
        self._val += rhs._val
        return self

    def __isub__(self, rhs: "Speed") -> "Speed":
        self._val -= rhs._val
        return self

    def __add__(self, rhs: "Speed") -> "Speed":
        return Speed(self._val + rhs._val)

    def __sub__(self, rhs: "Speed") -> "Speed":
        return Speed(self._val - rhs._val)

    def __neg__(self) -> "Speed":
        return Speed(-self._val)


_ZERO_SPEED = Speed.from_meters_per_sec(0)


def ZERO_SPEED() -> Speed:
    return _ZERO_SPEED.copy()

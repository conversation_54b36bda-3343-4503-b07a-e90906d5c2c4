from typing import Type, TypeVar

T = TypeVar("T", bound="Distance")


_M_TO_MM = 1000
_IN_TO_MM = 25.4
_FT_TO_MM = 304.8


class Distance:
    def __init__(self, val: float) -> None:
        """Do not use directly, instead use the from_* class methods"""
        self._val = val  # stored internally as mm

    @classmethod
    def from_mm(cls: Type[T], val: float) -> T:
        return cls(val)

    @classmethod
    def from_meters(cls: Type[T], val: float) -> T:
        return cls(val * _M_TO_MM)

    @classmethod
    def from_in(cls: Type[T], val: float) -> T:
        return cls(val * _IN_TO_MM)

    @classmethod
    def from_ft(cls: Type[T], val: float) -> T:
        return cls(val * _FT_TO_MM)

    @property
    def mm(self) -> float:
        return self._val

    @mm.setter
    def mm(self, val: float) -> None:
        self._val = val

    @property
    def meters(self) -> float:
        return self._val / _M_TO_MM

    @meters.setter
    def meters(self, val: float) -> None:
        self._val = val * _M_TO_MM

    @property
    def inches(self) -> float:
        return self._val / _IN_TO_MM

    @inches.setter
    def inches(self, val: float) -> None:
        self._val = val * _IN_TO_MM

    @property
    def ft(self) -> float:
        return self._val / _FT_TO_MM

    @ft.setter
    def ft(self, val: float) -> None:
        self._val = val * _FT_TO_MM

    def __gt__(self, rhs: "Distance") -> bool:
        return self._val > rhs._val

    def __ge__(self, rhs: "Distance") -> bool:
        return self._val >= rhs._val

    def __lt__(self, rhs: "Distance") -> bool:
        return self._val < rhs._val

    def __le__(self, rhs: "Distance") -> bool:
        return self._val <= rhs._val

    def __eq__(self, rhs: object) -> bool:
        if not isinstance(rhs, Distance):
            raise NotImplementedError()
        return self._val == rhs._val

    def __repr__(self) -> str:
        return self.__str__()

    def __str__(self) -> str:
        return f"Distance({self.mm} mm)"

    def __mul__(self, rhs: float) -> "Distance":
        return Distance(self._val * rhs)

    def __truediv__(self, rhs: float) -> "Distance":
        return Distance(self._val / rhs)

    def __iadd__(self, rhs: "Distance") -> "Distance":
        self._val += rhs._val
        return self

    def __isub__(self, rhs: "Distance") -> "Distance":
        self._val -= rhs._val
        return self

    def __add__(self, rhs: "Distance") -> "Distance":
        return Distance(self._val + rhs._val)

    def __sub__(self, rhs: "Distance") -> "Distance":
        return Distance(self._val - rhs._val)

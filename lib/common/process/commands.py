import asyncio
import logging
import subprocess
from typing import Any, List, Optional, Tuple

LOG = logging.getLogger(__name__)


def log_cmd(cmd: List[str]) -> None:
    LOG.debug(f"Running Command: {' '.join(cmd)}")


def _run_command(
    cmd: List[str], timeout_ms: Optional[int] = None, stdout: Optional[int] = None
) -> "subprocess.Popen[Any]":
    log_cmd(cmd)
    process = subprocess.Popen(cmd, stdout=stdout, text=stdout is not None)
    try:
        process.wait(timeout=None if timeout_ms is None else timeout_ms / 1000)
    except subprocess.TimeoutExpired:
        process.kill()
    process.wait()
    return process


async def run_command_with_output(cmd: List[str], timeout_ms: Optional[int] = None) -> Tuple[int, str]:
    process = await asyncio.get_event_loop().run_in_executor(
        None, lambda: _run_command(cmd, timeout_ms, stdout=subprocess.PIPE)
    )
    return process.returncode, process.stdout.read() if process.stdout is not None else ""


async def run_command_without_output(cmd: List[str], timeout_ms: Optional[int] = None) -> int:
    process = await asyncio.get_event_loop().run_in_executor(None, lambda: _run_command(cmd, timeout_ms))
    return process.returncode

import asyncio
import signal
import threading
import traceback
from contextlib import contextmanager
from typing import Any, Callable, Generator, List, Optional

import lib.common.logging
from lib.common.bot.cpp.bot_stop_python import BotStopEvent
from lib.common.bot.cpp.bot_stop_python import BotStopHandler as BotStopEventHandler
from lib.common.error import MakaException

LOG = lib.common.logging.get_logger(__name__)


class SegfaultException(MakaException):
    pass


class BotStopHandler:
    def __init__(self) -> None:
        self._called = False
        self._stopped_event = threading.Event()
        self._aio_stopped_event: Optional[asyncio.Event] = None
        self._ready_for_termination_event = threading.Event()
        self._callbacks: List[Callable[[], None]] = []
        signal.signal(signal.SIGINT, self.exit_gracefully)
        signal.signal(signal.SIGTERM, self.exit_gracefully)
        self._bse_handler = BotStopEventHandler()
        self._stop_blockers: List[threading.Event] = []
        self._error_code: int = 0

    def add_callback(self, cb: Callable[[], None]) -> None:
        self._callbacks.append(cb)

    def create_stop_blocker(self) -> threading.Event:
        stop_blocker_event = threading.Event()
        self._stop_blockers.append(stop_blocker_event)
        return stop_blocker_event

    def create_bot_stop_blocker(self, name: str) -> BotStopEvent:
        return self._bse_handler.create_event(name)

    @contextmanager
    def scoped_bot_stop_blocker(self, name: str) -> Generator[BotStopEvent, None, None]:
        try:
            bse = self._bse_handler.create_event(name)
            yield bse
        finally:
            bse.set()

    @property
    def error_code(self) -> int:
        return self._error_code

    @property
    def stopped(self) -> bool:
        return self._stopped_event.is_set()

    @property
    def ready_for_termination_event(self) -> threading.Event:
        return self._ready_for_termination_event

    async def get_stop_event(self) -> asyncio.Event:
        if self._aio_stopped_event is None:
            self._aio_stopped_event = asyncio.Event()
        return self._aio_stopped_event

    def exit_gracefully(self, signum: Any = 0, frame: Any = 0) -> None:
        if self._called:
            return
        self._stopped_event.set()
        if self._aio_stopped_event is not None:
            self._aio_stopped_event.set()
        self._bse_handler.stop()
        for stop_blocker_event in self._stop_blockers:
            stop_blocker_event.wait()
        self._bse_handler.wait()
        self._ready_for_termination_event.set()
        for cb in self._callbacks:
            cb()
        self._called = True

    def exit_with_exception(self, ex: Exception, error_code: int = 1) -> None:
        stack = "".join(traceback.format_exception(type(ex), ex, ex.__traceback__))
        LOG.error(f"Exiting due to Exception:\n{stack}")
        self._error_code = error_code
        self.exit_gracefully(0, 0)

    def sleep_safe(self, timeout_ms: int) -> None:
        self._bse_handler.sleep_safe(timeout_ms)

    async def async_sleep_safe(self, timeout_ms: int) -> None:
        await asyncio.get_event_loop().run_in_executor(None, lambda: self.sleep_safe(timeout_ms))


bot_stop_handler = BotStopHandler()

#pragma once

#include <atomic>
#include <condition_variable>
#include <functional>
#include <memory>
#include <mutex>
#include <stdint.h>
#include <unordered_map>

namespace lib {
namespace common {
namespace bot {

class BotStopEvent {
public:
  using StopCallback = std::function<void(void)>;

protected:
  std::mutex mutex_;
  std::condition_variable wait_cv_;
  std::atomic<bool> set_var_;
  std::atomic<bool> stop_var_;
  std::string name_;
  StopCallback cb_;

public:
  BotStopEvent(const std::string &name, StopCallback cb);
  ~BotStopEvent();
  void stop();
  void set();
  void wait(int32_t timeout_ms);
  inline bool is_stopped() const { return stop_var_; }
  inline const std::string &name() const { return name_; }
  inline bool is_set() const { return set_var_; }
};

class ScopedBotStopEvent {
private:
  std::shared_ptr<BotStopEvent> bse_;

public:
  ScopedBotStopEvent() = delete;
  ScopedBotStopEvent(std::shared_ptr<BotStopEvent> bse);
  ScopedBotStopEvent(ScopedBotStopEvent &&sbse);
  ~ScopedBotStopEvent();
  inline void stop() { bse_->stop(); }
  inline void set() { bse_->set(); }
  inline void wait(int32_t timeout_ms) { bse_->wait(timeout_ms); }
  inline bool is_stopped() { return bse_->is_stopped(); }
  inline BotStopEvent *get() { return bse_.get(); }
};

class BotStopHandler {
private:
  friend ScopedBotStopEvent;
  std::mutex mutex_;
  std::condition_variable sleep_cv_;
  std::unordered_map<BotStopEvent *, std::shared_ptr<BotStopEvent>> events_;
  std::atomic<bool> stop_var_;
  BotStopHandler();
  void remove(BotStopEvent *key);

public:
  BotStopHandler(const BotStopHandler &) = delete;
  BotStopHandler(BotStopHandler &&) = delete;
  static BotStopHandler &get();
  std::shared_ptr<BotStopEvent> create_event(const std::string &name, BotStopEvent::StopCallback cb = nullptr);
  ScopedBotStopEvent create_scoped_event(const std::string &name, BotStopEvent::StopCallback cb = nullptr);
  void stop();
  inline bool is_stopped() const { return stop_var_; }
  void wait();
  void sleep_safe(int32_t timeout_ms);
};

} // namespace bot
} // namespace common
} // namespace lib
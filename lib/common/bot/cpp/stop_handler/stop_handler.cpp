#include <chrono>
#include <lib/common/bot/cpp/exceptions.hpp>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>

#include <spdlog/spdlog.h>

namespace lib {
namespace common {
namespace bot {
static constexpr auto stop_timeout = std::chrono::seconds(10);
BotStopEvent::BotStopEvent(const std::string &name, StopCallback cb)
    : set_var_(false), stop_var_(false), name_(name), cb_(cb) {}

BotStopEvent::~BotStopEvent() {}

void BotStopEvent::stop() {
  if (this->stop_var_) {
    // Do not call callback again as it could no longer exist
    return;
  }
  this->stop_var_ = true;
  if (cb_) {
    cb_();
  }
}

void BotStopEvent::set() {
  this->set_var_ = true;
  this->wait_cv_.notify_all();
}

void BotStopEvent::wait(int32_t timeout_ms) {
  if (set_var_) {
    return;
  }
  std::unique_lock<std::mutex> lk(this->mutex_);
  this->wait_cv_.wait_for(lk, std::chrono::milliseconds(timeout_ms), [&] { return this->set_var_.load(); });
  if (!this->set_var_) {
    throw bot_stop_timeout_error("Timeout Waiting for Bot Stop Event");
  }
}

ScopedBotStopEvent::ScopedBotStopEvent(std::shared_ptr<BotStopEvent> bse) : bse_(bse) {}
ScopedBotStopEvent::ScopedBotStopEvent(ScopedBotStopEvent &&sbse) : bse_(std::move(sbse.bse_)) {}
ScopedBotStopEvent::~ScopedBotStopEvent() {
  if (bse_) {
    bse_->stop();
    bse_->set();
    BotStopHandler::get().remove(bse_.get());
  }
}

BotStopHandler::BotStopHandler() : stop_var_(false) {}

BotStopHandler &BotStopHandler::get() {
  static BotStopHandler bsh;
  return bsh;
}
std::shared_ptr<BotStopEvent> BotStopHandler::create_event(const std::string &name, BotStopEvent::StopCallback cb) {
  auto tmp = std::make_shared<BotStopEvent>(name, cb);
  {
    std::unique_lock<std::mutex> lk(mutex_);
    events_.emplace(tmp.get(), tmp);
  }
  return tmp;
}
ScopedBotStopEvent BotStopHandler::create_scoped_event(const std::string &name, BotStopEvent::StopCallback cb) {
  return std::move(ScopedBotStopEvent(create_event(name, cb)));
}

void BotStopHandler::stop() {
  std::unique_lock<std::mutex> lk(mutex_);
  stop_var_ = true;
  for (auto &pair : events_) {
    pair.second->stop();
  }
}
void BotStopHandler::wait() {
  std::unique_lock<std::mutex> lk(mutex_);
  auto start = std::chrono::system_clock::now();
  while (std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now() - start) < stop_timeout) {
    size_t i = 0;
    size_t cur_size = events_.size();
    if (cur_size == 0) {
      break;
    }
    for (auto it = events_.begin(); it != events_.end();) {
      ++i;
      if (it->second->is_set()) {
        spdlog::debug("bse {} is done. {}/{}", it->second->name(), i, cur_size);
        it = events_.erase(it);
      } else {
        spdlog::debug("Waiting on bse {} {}/{}", it->second->name(), i, cur_size);
        ++it;
      }
    }
    if (!events_.empty()) {
      try {
        events_.begin()->second->wait(1000);
      } catch (const bot_stop_timeout_error &err) {
      }
    }
  }
  for (auto &pair : events_) {
    spdlog::info("Failed to shutdown bse {}", pair.second->name());
  }
}

void BotStopHandler::remove(BotStopEvent *key) {
  std::unique_lock<std::mutex> lk(mutex_);
  events_.erase(key);
}
void BotStopHandler::sleep_safe(int32_t timeout_ms) {
  std::unique_lock<std::mutex> lk(this->mutex_);
  this->sleep_cv_.wait_for(lk, std::chrono::milliseconds(timeout_ms), [&] { return this->stop_var_.load(); });
}

} // namespace bot
} // namespace common
} // namespace lib
add_compile_options(-fvisibility=default)
file(GLOB SOURCES CONFIGURE_DEPENDS stop_handler/*.cpp)

pybind11_add_module(bot_stop_python SHARED bot_stop_python.cpp)

add_library(bot_stop SHARED ${SOURCES})
target_compile_definitions(bot_stop PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(bot_stop PUBLIC m pthread rt exceptions)

target_link_libraries(bot_stop_python PUBLIC bot_stop)
target_compile_definitions(bot_stop_python PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
set_target_properties(bot_stop_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})
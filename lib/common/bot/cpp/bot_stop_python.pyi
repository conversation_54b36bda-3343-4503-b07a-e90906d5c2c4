from typing import Callable, Optional, overload

class BotStopEvent:
    def set(self) -> None: ...
    def wait(self, timeout_ms: int) -> None: ...
    def stop(self) -> None: ...
    def is_stopped(self) -> bool: ...

class BotStopHandler:
    def __init_(self) -> None: ...
    def stop(self) -> None: ...
    def wait(self) -> None: ...
    def create_event(self, name: str, cb: Optional[Callable[[], None]] = None) -> BotStopEvent: ...
    def sleep_safe(self, timeout_ms: int) -> None: ...

class BotStopException(Exception):
    pass

class BotStopTimeoutException(BotStopException):
    pass

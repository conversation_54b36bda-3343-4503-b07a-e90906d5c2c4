#pragma once

#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace common {
namespace bot {

class bot_stop_error : public maka_error {
public:
  explicit bot_stop_error(const std::string &what, size_t stack_skip = 1) : maka_error(what, stack_skip + 1) {}
};

class bot_stop_timeout_error : public bot_stop_error {
public:
  explicit bot_stop_timeout_error(const std::string &what, size_t stack_skip = 1)
      : bot_stop_error(what, stack_skip + 1) {}
};

} // namespace bot
} // namespace common
} // namespace lib
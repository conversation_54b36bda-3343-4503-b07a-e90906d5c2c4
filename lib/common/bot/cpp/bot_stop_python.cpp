#ifdef PY<PERSON>ND
#include <pybind11/functional.h>
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#endif

#include <lib/common/bot/cpp/exceptions.hpp>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>

#ifdef PYBIND
namespace py = pybind11;
#endif

namespace lib {
namespace common {
namespace bot {

#ifdef PYBIND

PYBIND11_MODULE(bot_stop_python, m) {

  py::class_<BotStopEvent, std::shared_ptr<BotStopEvent>>(m, "BotStopEvent")
      .def("set", &BotStopEvent::set, py::call_guard<py::gil_scoped_release>())
      .def("wait", &BotStopEvent::wait, py::call_guard<py::gil_scoped_release>(), py::arg("timeout_ms"))
      .def("is_stopped", &BotStopEvent::is_stopped, py::call_guard<py::gil_scoped_release>())
      .def("stop", &BotStopEvent::stop, py::call_guard<py::gil_scoped_release>());

  py::class_<BotStopHandler, std::unique_ptr<BotStopHandler, py::nodelete>>(m, "BotStopHandler")
      .def(py::init([]() { return std::unique_ptr<BotStopHandler, py::nodelete>(&BotStopHandler::get()); }))
      .def("create_event", &BotStopHandler::create_event, py::arg("name"), py::arg("cb") = nullptr,
           py::call_guard<py::gil_scoped_release>())
      .def("stop", &BotStopHandler::stop, py::call_guard<py::gil_scoped_release>())
      .def("sleep_safe", &BotStopHandler::sleep_safe, py::arg("timeout_ms"), py::call_guard<py::gil_scoped_release>())
      .def("wait", &BotStopHandler::wait, py::call_guard<py::gil_scoped_release>());

  auto py_bot_stop_exception = py::register_exception<bot_stop_error>(m, "BotStopException");
  py::register_exception<bot_stop_timeout_error>(m, "BotStopTimeoutException", py_bot_stop_exception.ptr());
}

#endif

} // namespace bot
} // namespace common
} // namespace lib
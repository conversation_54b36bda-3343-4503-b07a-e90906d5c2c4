class LowPassFilter:
    def __init__(self, alpha: float, initial_value: float) -> None:
        self._alpha = alpha
        self._filtered_value = initial_value

    def set_alpha(self, alpha: float) -> None:
        self._alpha = alpha

    def reset(self, initial_value: float) -> None:
        self._filtered_value = initial_value

    def update(self, new_value: float) -> float:
        self._filtered_value = self._alpha * new_value + (1 - self._alpha) * self._filtered_value
        return self._filtered_value

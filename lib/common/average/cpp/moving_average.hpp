#pragma once

namespace lib {
namespace common {
namespace average {

class MovingAverage {
public:
  MovingAverage(double initial_value, double smooth_factor, bool pre_initialized)
      : val_(initial_value), smooth_factor_(smooth_factor), initialized_(pre_initialized){};
  void override_value(double value) { val_ = value; };
  double get_value() { return val_; };
  double get_smooth_factor() { return smooth_factor_; };
  double update(double value) {
    if (!initialized_) {
      val_ = value;
      initialized_ = true;
    } else {
      val_ = dry_update(value);
    }

    return val_;
  };
  double dry_update(double value) { return (val_ * smooth_factor_) + (value * (1.0 - smooth_factor_)); };

private:
  double val_;
  double smooth_factor_;
  bool initialized_;
};

} // namespace average
} // namespace common
} // namespace lib

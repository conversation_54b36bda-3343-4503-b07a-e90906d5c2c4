import inspect
from collections.abc import Iterable
from queue import Empty, Queue
from typing import TYPE_CHECKING, Any, List, Optional, Union

import numpy as np
import numpy.typing as npt

if TYPE_CHECKING:
    from lib.common.tasks import MakaTask


def generate_flatten(*argv: Any) -> Any:
    """
    Common helper function to gracefully merge arguments together whether they are generator functions, lists, or other
    objects.

    Iterate through the given arguments, and yield each value they represent. If an argument is a generator function,
    the generator function will be invoked and looped through. if the argument is a list, each element will be
    yielded in order. Else the argument is yielded as an object.
    """
    for arg in argv:
        if inspect.isgeneratorfunction(arg):
            for subarg in arg():
                yield subarg
        elif isinstance(arg, Iterable):
            for subarg in arg:
                yield subarg
        else:
            yield arg


def cancellable_blocking_queue_get(
    task: "MakaTask", q: "Queue[Any]", periodic_cancel_check_ms: int = 1000
) -> Optional[Any]:
    """
    Get the next item from the given queue, blocking forever,
    but periodically checking if the given maka task has been cancelled.

    Author note: passing in the maka_task makes this file usable by the MakaTask itself

    :param task: the task to periodically check for cancellation
    :param q: The queue to block and get the next item for
    :param periodic_cancel_check_ms: The number of milliseconds between checking if the task is cancelled
    :return: the next item in the queue
    """
    while True:
        task.tick()
        try:
            # possibly None
            return q.get(block=True, timeout=periodic_cancel_check_ms / 1000)
        except Empty:
            pass


def eval_2d_matrix_strs(matrix: List[List[Union[int, float, str]]]) -> npt.NDArray[Any]:
    """
    Evaluates a 2D matrix of equations, provided as a list of list of strs.

    Example input: [["cos(0)", "-sin(0)", "0"], ["sin(0)", "cos(0)", "0"], ["0.123", "0.456", "cos(pi / 3)"]]
    """
    # import some common math functions that could be used in frame rotation definitions.
    from math import cos, pi, sin  # noqa

    result = []
    for row in matrix:
        r = []
        for cell in row:
            # evaluate the string, so this handles cases like "cos(0)" or just "0".
            r.append(eval(str(cell)))
        result.append(r)

    return np.array(result)

#pragma once

#include <map>
#include <nlohmann/json.hpp>
#include <optional>
#include <string>

#include "deeplearning/model_io/proto/metadata.pb.h"
#include "deeplearning/server/trt_runtime/cpp/runtime.h"
#include "lib/common/model/cpp/atomic_model.h"
#include "lib/common/model/cpp/model_types.h"

namespace lib::common::model {

template <typename T>
class ModelRegistry {
public:
  ModelRegistry() {}
  ModelRegistry(const std::string &model_registry_path);
  void clear_path_from_cache(std::string path);

  AtomicModel get(T use_case, int gpu_id) const;
  bool contains(T use_case) const;
  void set_model_for_use_case(T use_case, std::string model_path);
  void clear_model_for_use_case(T use_case);
  std::map<T, std::string> get_model_paths();
  std::string get_path_for_use_case(T use_case);
  const ModelMetadataProto &get_internal_metadata_by_use_case(T use_case) const;
  const nlohmann::json &get_external_metadata_by_use_case(T use_case) const;

private:
  mutable std::mutex cache_mutex_;
  mutable std::mutex paths_mutex_;
  mutable std::map<T, std::string> model_paths_;
  mutable std::map<std::string, std::map<int, ModelData>> model_cache_;
  mutable std::map<std::string, ModelMetadataProto> internal_metadata_cache_;
  mutable std::map<std::string, nlohmann::json> external_metadata_cache_;
};

} // namespace lib::common::model
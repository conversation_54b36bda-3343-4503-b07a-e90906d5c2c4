#pragma once

#include <map>
#include <nlohmann/json.hpp>
#include <optional>
#include <string>

#include "deeplearning/model_io/proto/metadata.pb.h"
#include "deeplearning/server/trt_runtime/cpp/runtime.h"

namespace lib::common::model {

nlohmann::json external_metadata_from_model_path(std::string model_path);

struct ModelData {
  std::shared_ptr<deeplearning::server::trt_runtime::TRTRuntime> runtime;
  ModelMetadataProto internal_metadata;
  nlohmann::json external_metadata;
  std::string path;
  int gpu_id;
};

ModelData load_model(const std::string &model_path, int gpu_id);

class AtomicModel {
public:
  AtomicModel() {}
  AtomicModel(const std::string &model_path, int gpu_id);
  AtomicModel(ModelData &model_data);
  std::vector<torch::Tensor> operator()(const std::vector<torch::Tensor> &inputs);
  operator bool() const { return (bool)model_data_.runtime; }
  const ModelMetadataProto &get_internal_metadata() const;
  const nlohmann::json &get_external_metadata() const;
  const std::string &get_model_path() const;
  int get_gpu_id() const { return model_data_.gpu_id; }

private:
  ModelData model_data_;
  deeplearning::server::trt_runtime::TRTContext context_;
};

} // namespace lib::common::model
add_library(lib_common_model atomic_model.cpp model_registry.cpp)
target_link_libraries(lib_common_model PUBLIC trt_runtime fmt spdlog model_io yaml-cpp)

pybind11_add_module(model_python SHARED model_python.cpp)
target_link_libraries(model_python PUBLIC lib_common_model torch_python)
target_compile_options(model_python PRIVATE -fvisibility=default)
set_target_properties(model_python PROPERTIES OUTPUT_NAME model_python.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

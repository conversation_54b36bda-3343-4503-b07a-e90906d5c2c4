#pragma once

namespace lib::common::model {

enum class ModelUseCase {
  kPredict = 0,
  kTarget = 1,
  kDriving = 2,
  kP2P = 3,
};

inline std::optional<ModelUseCase> model_use_case_from_string(const std::string &use_case) {
  if (use_case == "PREDICT") {
    return ModelUseCase::kPredict;
  } else if (use_case == "TARGET") {
    return ModelUseCase::kTarget;
  } else if (use_case == "DRIVING") {
    return ModelUseCase::kDriving;
  } else if (use_case == "P2P") {
    return ModelUseCase::kP2P;
  } else {
    return std::make_optional<ModelUseCase>();
  }
}

inline std::string model_use_case_to_string(const ModelUseCase &use_case) {
  if (use_case == ModelUseCase::kPredict) {
    return "PREDICT";
  } else if (use_case == ModelUseCase::kTarget) {
    return "TARGET";
  } else if (use_case == ModelUseCase::kDriving) {
    return "DRIVING";
  } else {
    return "P2P";
  }
}

} // namespace lib::common::model
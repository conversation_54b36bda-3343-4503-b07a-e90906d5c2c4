#include "model_registry.h"

#include <filesystem>
#include <fmt/format.h>
#include <fstream>
#include <map>
#include <spdlog/spdlog.h>
#include <yaml-cpp/yaml.h>

#include "deeplearning/model_io/cpp/tensorrt.h"
#include "generated/deeplearning/model_io/proto/trt_model.pb.h"
#include "lib/common/cpp/exceptions.h"

namespace lib::common::model {

template <>
ModelRegistry<ModelUseCase>::ModelRegistry(const std::string &model_registry_path) {
  YAML::Node config = YAML::LoadFile(model_registry_path);
  if (!config["models"] || !config["models"].IsMap()) {
    throw maka_error("Malformed models file, missing 'models' section.");
  }

  for (auto kv : config["models"]) {
    if (!kv.second["path"])
      continue;

    const std::optional<ModelUseCase> model_use_case = model_use_case_from_string(kv.first.as<std::string>());
    if (!model_use_case) {
      throw maka_error(fmt::format("Unknown model type in models file {}", kv.first.as<std::string>()));
    }
    const std::string &model_path = kv.second["path"].as<std::string>();

    model_paths_[*model_use_case] = model_path;
  }
}

template <>
ModelRegistry<std::string>::ModelRegistry(const std::string &model_registry_path) {
  YAML::Node config = YAML::LoadFile(model_registry_path);
  if (!config["models"] || !config["models"].IsMap()) {
    throw maka_error("Malformed models file, missing 'models' section.");
  }

  for (auto kv : config["models"]) {
    if (!kv.second["path"])
      continue;

    const std::optional<ModelUseCase> model_use_case = model_use_case_from_string(kv.first.as<std::string>());
    if (!model_use_case) {
      throw maka_error(fmt::format("Unknown model type in models file {}", kv.first.as<std::string>()));
    }
    const std::string &model_path = kv.second["path"].as<std::string>();

    model_paths_[model_use_case_to_string(*model_use_case)] = model_path;
  }
}

template <typename T>
bool ModelRegistry<T>::contains(T use_case) const {
  std::unique_lock<std::mutex> paths_lock(paths_mutex_);
  return model_paths_.count(use_case) > 0;
}

template <typename T>
AtomicModel ModelRegistry<T>::get(T use_case, int gpu_id) const {
  std::string path;
  {
    std::unique_lock<std::mutex> paths_lock(paths_mutex_);
    try {
      path = model_paths_.at(use_case);
    } catch (std::out_of_range &ex) {
      throw maka_error(fmt::format("No model for use case {} in registry.", use_case));
    }
  }

  std::unique_lock<std::mutex> cache_lock(cache_mutex_);
  if (model_cache_.count(path) == 0) {
    model_cache_[path] = std::map<int, ModelData>();
  }

  if (model_cache_.at(path).count(gpu_id) == 0) {
    model_cache_[path][gpu_id] = load_model(path, gpu_id);
  }
  return AtomicModel(model_cache_.at(path).at(gpu_id));
}

template <typename T>
const ModelMetadataProto &ModelRegistry<T>::get_internal_metadata_by_use_case(T use_case) const {
  std::string path;
  {
    std::unique_lock<std::mutex> paths_lock(paths_mutex_);
    try {
      path = model_paths_.at(use_case);
    } catch (std::out_of_range &ex) {
      throw maka_error(fmt::format("No model for use case {} in registry.", use_case));
    }
  }

  std::unique_lock<std::mutex> cache_lock(cache_mutex_);
  if (internal_metadata_cache_.count(path) == 0) {
    internal_metadata_cache_[path] = deeplearning::model_io::load_trt_metadata(path);
  }

  return internal_metadata_cache_[path];
}

template <typename T>
const nlohmann::json &ModelRegistry<T>::get_external_metadata_by_use_case(T use_case) const {
  std::string path;
  {
    std::unique_lock<std::mutex> paths_lock(paths_mutex_);
    try {
      path = model_paths_.at(use_case);
    } catch (std::out_of_range &ex) {
      throw maka_error(fmt::format("No model for use case {} in registry.", use_case));
    }
  }

  std::unique_lock<std::mutex> cache_lock(cache_mutex_);
  if (external_metadata_cache_.count(path) == 0) {
    external_metadata_cache_[path] = external_metadata_from_model_path(path);
  }

  return external_metadata_cache_[path];
}

template <typename T>
void ModelRegistry<T>::clear_path_from_cache(std::string path) {
  std::unique_lock<std::mutex> cache_lock(cache_mutex_);
  if (model_cache_.count(path) != 0) {
    model_cache_.at(path).clear();
    model_cache_.erase(path);
  }
}

template <typename T>
std::string ModelRegistry<T>::get_path_for_use_case(T use_case) {
  std::string path;

  try {
    std::unique_lock<std::mutex> paths_lock(paths_mutex_);
    path = model_paths_.at(use_case);
  } catch (std::out_of_range &ex) {
    throw maka_error(fmt::format("No model for use case {} in registry.", use_case));
  }

  return path;
}

template <typename T>
void ModelRegistry<T>::set_model_for_use_case(T use_case, std::string model_path) {
  std::unique_lock<std::mutex> paths_lock(paths_mutex_);
  model_paths_[use_case] = model_path;
}

template <typename T>
void ModelRegistry<T>::clear_model_for_use_case(T use_case) {
  std::unique_lock<std::mutex> paths_lock(paths_mutex_);
  model_paths_.erase(use_case);
}

template <typename T>
std::map<T, std::string> ModelRegistry<T>::get_model_paths() {
  std::unique_lock<std::mutex> paths_lock(paths_mutex_);
  return model_paths_;
}

template class ModelRegistry<ModelUseCase>;
template class ModelRegistry<std::string>;

} // namespace lib::common::model
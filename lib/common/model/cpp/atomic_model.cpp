#include "atomic_model.h"

#include <filesystem>
#include <fmt/format.h>
#include <fstream>
#include <map>
#include <spdlog/spdlog.h>
#include <yaml-cpp/yaml.h>

#include "deeplearning/model_io/cpp/tensorrt.h"
#include "generated/deeplearning/model_io/proto/trt_model.pb.h"
#include "lib/common/cpp/exceptions.h"

namespace lib::common::model {

nlohmann::json external_metadata_from_model_path(std::string model_path) {
  auto metadata_path = std::filesystem::path(model_path).replace_extension(".json");

  nlohmann::json metadata;
  if (std::filesystem::exists(metadata_path)) {
    std::ifstream metadata_file(metadata_path);
    metadata_file >> metadata;
  }
  return metadata;
}

ModelData load_model(const std::string &path, int gpu_id) {
  ModelData model_data;
  model_data.path = path;
  std::tie(model_data.runtime, model_data.internal_metadata) = deeplearning::model_io::load_trt_model(path, gpu_id);
  model_data.external_metadata = external_metadata_from_model_path(path);
  model_data.gpu_id = gpu_id;
  return model_data;
}

AtomicModel::AtomicModel(ModelData &model_data) : model_data_(model_data) {
  context_ = model_data_.runtime->create_context();
}

AtomicModel::AtomicModel(const std::string &path, int gpu_id) {
  model_data_ = load_model(path, gpu_id);
  context_ = model_data_.runtime->create_context();
}

std::vector<torch::Tensor> AtomicModel::operator()(const std::vector<torch::Tensor> &inputs) {
  return model_data_.runtime->infer(context_, inputs);
}

const ModelMetadataProto &AtomicModel::get_internal_metadata() const { return model_data_.internal_metadata; }
const nlohmann::json &AtomicModel::get_external_metadata() const { return model_data_.external_metadata; }
const std::string &AtomicModel::get_model_path() const { return model_data_.path; }

} // namespace lib::common::model

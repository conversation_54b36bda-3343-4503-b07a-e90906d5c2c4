#include "sentry_reporter.h"

#include <boost/asio/ip/host_name.hpp>
#include <fmt/format.h>

#include "lib/common/cpp/exceptions.h"
#include <lib/common/cpp/utils/generation.hpp>

namespace lib {
namespace common {

bool SentryReporter::initialized_ = false;

void SentryReporter::initialize() {
  if (initialized_) {
    return;
  }

  std::string role = "invalid";
  char *role_ptr = std::getenv("MAKA_ROLE");
  if (role_ptr != NULL) {
    role = role_ptr;
  }

  sentry_options_t *options = sentry_options_new();
  sentry_init(options);

  char *row_ptr = std::getenv("MAKA_ROW");
  std::string row = "1";
  if (row_ptr != NULL) {
    row = row_ptr;
  }

  char *name_ptr = std::getenv("MAKA_ROBOT_NAME");
  std::string name = "";
  if (name_ptr != NULL) {
    name = name_ptr;
  }

  const auto host_name = boost::asio::ip::host_name();
  std::string gen(carbon::common::generation_str());

  SentryReporter::set_tag("service", host_name);
  SentryReporter::set_tag("row", row);
  SentryReporter::set_tag("gen", gen);
  SentryReporter::set_tag("role", role);
  SentryReporter::set_tag("name", name);
  initialized_ = true;
}

void SentryReporter::shutdown() {
  if (initialized_) {
    sentry_close();
    initialized_ = false;
  }
}

void SentryReporter::report_exception(const std::exception &e) {
  if (!initialized_) {
    return;
  }
  std::string exception_string = exception_to_string(e);
  sentry_value_t exc = sentry_value_new_object();
  sentry_value_set_by_key(exc, "type", sentry_value_new_string("Exception"));
  sentry_value_set_by_key(exc, "value", sentry_value_new_string(exception_string.c_str()));

  sentry_value_t exceptions = sentry_value_new_object();
  sentry_value_t values = sentry_value_new_list();

  sentry_value_set_by_key(exceptions, "values", values);
  sentry_value_append(values, exc);

  sentry_value_t event = sentry_value_new_event();
  sentry_value_set_by_key(event, "exception", exceptions);

  sentry_capture_event(event);
}
void SentryReporter::set_tag(const std::string &key, const std::string &value) {
  sentry_set_tag(key.c_str(), value.c_str());
}
} // namespace common

} // namespace lib

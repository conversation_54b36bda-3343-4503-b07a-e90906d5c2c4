import os
import socket
from typing import Any, Dict, Optional

import sentry_sdk

import lib.common.logging

initialized = False

LOG = lib.common.logging.get_logger(__name__)


def only_send_unhandled(event: Dict[str, Any], _: Any) -> Optional[Dict[str, Any]]:
    if event["exception"]["values"][0].get("mechanism", {}).get("handled", False):
        return None
    return event


def initialize() -> None:
    global initialized
    try:
        if not initialized:
            role = os.environ.get("MAKA_ROLE", "")
            row = os.environ.get("MAKA_ROW", "")
            gen = os.environ.get("MAKA_GEN", "")
            name = os.environ.get("MAKA_ROBOT_NAME", "")
            sentry_sdk.init(before_send=only_send_unhandled)
            sentry_sdk.set_tag("service", socket.gethostname())
            sentry_sdk.set_tag("role", role)
            sentry_sdk.set_tag("gen", gen)
            sentry_sdk.set_tag("row", row)
            sentry_sdk.set_tag("name", name)
            initialized = True
    except Exception as e:
        LOG.warning(f"Failed to initialize sentry: {e}")

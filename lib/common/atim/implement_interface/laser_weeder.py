from dataclasses import dataclass
from enum import Enum
from typing import Dict, Type

from dataclass_wizard import JSONWizard

from lib.common.messaging.message import ContentType, Empty, Message, empty_sender_builder, msg_sender_builder


@dataclass
class SafeOpState(JSONWizard):
    is_safe: bool


@dataclass
class SafeStateEnforced(JSONWizard):
    enforced: bool


class MessageType(str, Enum):
    # Req/Resp
    IMP_SAFETY_POLICY_REQ = "LW_IMP_SAFETY_POLICY_REQ"
    IMP_SAFETY_POLICY_RESP = "LW_IMP_SAFETY_POLICY_RESP"

    # Sever Event Driven
    SAFE_TO_OPERATE_STATE = "LW_SAFE_TO_OPERATE_STATE"


REQ_RESP_MAP: Dict[str, str] = {
    MessageType.IMP_SAFETY_POLICY_REQ: MessageType.IMP_SAFETY_POLICY_RESP,
}
REQ_MAP: Dict[str, Type[ContentType]] = {
    MessageType.IMP_SAFETY_POLICY_REQ: SafeStateEnforced,
}
RESP_MAP: Dict[str, Type[JSONWizard]] = {
    MessageType.IMP_SAFETY_POLICY_RESP: Empty,
}
EVENT_MAP: Dict[str, Type[JSONWizard]] = {
    MessageType.SAFE_TO_OPERATE_STATE: SafeOpState,
}

empty_sender = empty_sender_builder(Message, REQ_RESP_MAP)
msg_sender = msg_sender_builder(Message, REQ_RESP_MAP)

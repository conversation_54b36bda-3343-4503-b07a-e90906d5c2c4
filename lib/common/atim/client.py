from typing import Optional, cast

import lib.common.atim.implement_interface.laser_weeder as lw_if
import lib.common.atim.interface as atim_if
from lib.common.atim.implement_interface.supported_implements import ATIMImplementSpecificId
from lib.common.messaging.websocket.client import Client, Message
from lib.common.units.speed import Speed


class AtimClient(Client):
    @staticmethod
    async def build(url: str = atim_if.default_ws_url(), reconnect_timeout: int = 1) -> "AtimClient":
        c = AtimClient(
            url=url,
            req_resp_map={**atim_if.REQ_RESP_MAP, **lw_if.REQ_RESP_MAP},
            resp_map={**atim_if.RESP_MAP, **lw_if.RESP_MAP},
            reconnect_timeout=reconnect_timeout,
        )
        return c

    async def get_version(self, timeout: Optional[float] = None) -> atim_if.VersionInfo:
        return cast(atim_if.VersionInfo, await self.send(Message.build(atim_if.MessageType.VERSION_REQ, {}), timeout))

    async def set_interface(self, timeout: Optional[float] = None) -> None:
        await self.send_recv_raw(
            Message.build(
                atim_if.MessageType.SET_IMP_IF_REQ,
                atim_if.ImplementIF(implement_if=ATIMImplementSpecificId.LASER_WEEDER),
            ),
            timeout,
        )

    async def set_speed(self, speed: Speed, timeout: Optional[float] = None) -> None:
        await self.send_recv_raw(
            Message.build(atim_if.MessageType.SET_SPEED_REQ, atim_if.SpeedStatus(speed_mph=speed.mph)), timeout
        )

    async def set_implement_state(
        self, active: bool, error: bool, error_msg: Optional[str], timeout: Optional[float] = None
    ) -> None:
        await self.send_recv_raw(
            Message.build(
                atim_if.MessageType.SET_IMP_STATE_REQ,
                atim_if.ImplementState(active=active, error=error, error_msg=error_msg),
            ),
            timeout,
        )

    async def set_implement_safety_policy(self, enforced: bool, timeout: Optional[float] = None) -> None:
        await self.send_recv_raw(
            Message.build(lw_if.MessageType.IMP_SAFETY_POLICY_REQ, lw_if.SafeStateEnforced(enforced=enforced)), timeout
        )

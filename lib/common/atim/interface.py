from dataclasses import dataclass, field
from enum import Enum, IntEnum
from typing import Dict, Optional, Type

from dataclass_wizard import JSONWizard

from lib.common.atim.implement_interface.supported_implements import ATIMImplementSpecificId
from lib.common.generation import GENERATION, generation
from lib.common.messaging.message import ContentType, Empty, Message, empty_sender_builder, msg_sender_builder
from lib.common.role import is_simulator


################################################################################
# Deprecated for WS use individual statuses
@dataclass
class AscStatus(JSONWizard):
    in_gear: bool = False  # tractor must be in F to engage ASC
    speed_mph: float = 0.0


@dataclass
class ATIMStatusResp(JSONWizard):
    rtc_ready: bool = False  # The hh board is ready for control
    active: bool = False  # ATIM has been engaged on tractor
    asc_status: AscStatus = field(default_factory=AscStatus)


################################################################################


@dataclass
class ImplementState(JSONWizard):
    active: Optional[bool] = None  # smart implements can tell atim if they are currently doing work
    error: Optional[bool] = None
    error_msg: Optional[str] = None


@dataclass
class SpeedStatus(JSONWizard):
    speed_mph: float


class ATIMGear(IntEnum):
    UNKNOWN = 0
    PARK = 1
    FORWARD = 2
    REVERSE = 3
    NEUTRAL = 4
    POWER_ZERO = 5


@dataclass
class GearStatus(JSONWizard):
    gear: ATIMGear


@dataclass
class ATIMStatus(JSONWizard):
    active: bool


@dataclass
class ImplementIF(JSONWizard):
    implement_if: ATIMImplementSpecificId


@dataclass
class VersionInfo(JSONWizard):
    major: int
    minor: int
    point: int

    def __ge__(self, rhs: "VersionInfo") -> bool:
        return (
            self.major > rhs.major
            or (self.major == rhs.major and self.minor > rhs.minor)
            or (self.major == rhs.major and self.minor == rhs.minor and self.point >= rhs.point)
        )

    def __lt__(self, rhs: "VersionInfo") -> bool:
        return (
            self.major < rhs.major
            or (self.major == rhs.major and self.minor < rhs.minor)
            or (self.major == rhs.major and self.minor == rhs.minor and self.point < rhs.point)
        )


class Routes(str, Enum):
    VERSION = "/version"
    STATUS = "/status"
    SET_SPEED = "/set_speed"
    SET_IMPLEMENT_STATE = "/set_state"


DEFAULT_REST_PORT = 8181
DEFAULT_WS_PORT = 8182


def default_server() -> str:
    gen = generation()
    if gen == GENERATION.RTC:
        return "127.0.0.1"
    elif gen in [GENERATION.REAPER, GENERATION.SLAYER]:
        if is_simulator():
            return "127.0.0.1"
        return "10.12.3.1"
    raise Exception(f"{gen.name} does not support ATIM")


def default_rest_url() -> str:
    server = default_server()
    return f"http://{server}:{DEFAULT_REST_PORT}"


def default_ws_url() -> str:
    server = default_server()
    return f"ws://{server}:{DEFAULT_WS_PORT}"


class MessageType(str, Enum):
    # Req/Resp
    VERSION_REQ = "VERSION_REQ"
    VERSION_RESP = "VERSION_RESP"
    SET_IMP_IF_REQ = "SET_IMP_IF_REQ"
    SET_IMP_IF_RESP = "SET_IMP_IF_RESP"
    SET_SPEED_REQ = "SET_SPEED_REQ"
    SET_SPEED_RESP = "SET_SPEED_RESP"
    SET_IMP_STATE_REQ = "SET_IMP_STATE_REQ"
    SET_IMP_STATE_RESP = "SET_IMP_STATE_RESP"

    # Sever Event Driven
    ATIM_STATUS = "ATIM_STATUS"
    SPEED_STATUS = "SPEED_STATUS"
    GEAR_STATUS = "GEAR_STATUS"


REQ_RESP_MAP: Dict[str, str] = {
    MessageType.VERSION_REQ: MessageType.VERSION_RESP,
    MessageType.SET_SPEED_REQ: MessageType.SET_SPEED_RESP,
    MessageType.SET_IMP_STATE_REQ: MessageType.SET_IMP_STATE_RESP,
    MessageType.SET_IMP_IF_REQ: MessageType.SET_IMP_IF_RESP,
}
REQ_MAP: Dict[str, Type[ContentType]] = {
    MessageType.VERSION_REQ: Empty,
    MessageType.SET_SPEED_REQ: SpeedStatus,
    MessageType.SET_IMP_STATE_REQ: ImplementState,
    MessageType.SET_IMP_IF_REQ: ImplementIF,
}
RESP_MAP: Dict[str, Type[JSONWizard]] = {
    MessageType.VERSION_RESP: VersionInfo,
    MessageType.SET_SPEED_RESP: Empty,
    MessageType.SET_IMP_STATE_RESP: Empty,
    MessageType.SET_IMP_IF_RESP: Empty,
}
EVENT_MAP: Dict[str, Type[JSONWizard]] = {
    MessageType.ATIM_STATUS: ATIMStatus,
    MessageType.SPEED_STATUS: SpeedStatus,
    MessageType.GEAR_STATUS: GearStatus,
}

empty_sender = empty_sender_builder(Message, REQ_RESP_MAP)
msg_sender = msg_sender_builder(Message, REQ_RESP_MAP)

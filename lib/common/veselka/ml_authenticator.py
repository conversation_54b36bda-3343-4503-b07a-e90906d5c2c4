import enum
import json
import logging
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Optional

import requests
from requests import PreparedRequest
from requests.auth import AuthBase

AUTH0_TOKEN_CACHE_FILE = ".auth_token.json"

LOG = logging.getLogger(__name__)


class Environment(enum.Enum):
    PRODUCTION = 0
    STAGE = 1
    TEST = 2


class MlAuthenticator(AuthBase):
    """
    The MlAuthenticator can be used with the requests package ex: requests.get(url, auth=MlAuthenticator()) to provide
    required auth0 oauth2 tokens for accessing veselka ml endpoints.
    When creating an instance of this class, you should pass in auth_domain, client_id, and client_secret when being
    used outside the training cluster. When running in cluster, it will automatically detect host and port of
    the credentials proxy via  CREDENTIAL_PROXY_SERVICE_HOST and CREDENTIAL_PROXY_SERVICE_PORT environment variables
    existing.

    The reset method should be used by the caller in instances where auth parameters change or requests fail in order to
    delete the cached token file.
    Otherwise, the cached token file will be used (successfully or not) until the token residing inside expires.
    """

    def __init__(
        self,
        auth_domain: str = "",
        client_id: str = "",
        client_secret: str = "",
        audience: str = "https://ml.carbonrobotics.com",
        token_cache_file: str = AUTH0_TOKEN_CACHE_FILE,
        environment: Environment = Environment.PRODUCTION,
    ):
        self.auth_domain = auth_domain
        self.client_id = client_id
        self.client_secret = client_secret
        self.audience = audience
        self.token_cache_file = token_cache_file
        self.environment = environment

        self._log_token_from_cache = True

    def __call__(self, r: PreparedRequest) -> PreparedRequest:
        token: Optional[Any] = None
        try:
            with open(self.token_cache_file) as f:
                token = json.load(f)

            if token.get("client_id", self.client_id) != self.client_id:
                raise ValueError("cached token does not match the provided client_id")
            if token.get("audience", self.audience) != self.audience:
                raise ValueError("cached token does not match the provided audience")

        except Exception as e:
            LOG.info(f"Failed to load token file: {e}. Retrieving token.")
            token = None

        if not token or token.get("expiry") <= datetime.now().timestamp():
            LOG.info("Refreshing Token")
            host = os.getenv("CREDENTIAL_PROXY_SERVICE_HOST")
            port = os.getenv("CREDENTIAL_PROXY_SERVICE_PORT")
            if host and port:
                route_prefix = ""
                if self.environment == Environment.STAGE:
                    route_prefix = "/stg"
                elif self.environment == Environment.TEST:
                    route_prefix = "/test"

                url = f"http://{host}:{port}{route_prefix}/ml/oauth/token"
                LOG.info(f"Token URL: {url}")
                resp = requests.get(url)
            else:
                body = {
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "audience": self.audience,
                    "grant_type": "client_credentials",
                }
                resp = requests.post(f"https://{self.auth_domain}/oauth/token", json=body)

            if resp.ok:
                token = resp.json()
                token["expiry"] = int((datetime.now() + timedelta(seconds=token.get("expires_in", 0))).timestamp())
                token["client_id"] = self.client_id
                token["audience"] = self.audience
                try:
                    with open(self.token_cache_file, "w") as f:
                        json.dump(token, f)
                except Exception as e:
                    LOG.info(f"Failed to store token file: {e}. Something went wrong!")
            else:
                token = {}
        else:
            if self._log_token_from_cache:
                LOG.info("Token From Cache")
                self._log_token_from_cache = False

        # add headers for authenticated request.
        r.headers["authorization"] = f"Bearer {token.get('access_token')}"
        return r

    def reset(self) -> None:
        try:
            os.remove(self.token_cache_file)
        except Exception:
            pass

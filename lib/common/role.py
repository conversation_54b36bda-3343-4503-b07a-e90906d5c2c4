import os

from bot.role import ROL<PERSON>
from bot.role import is_numeric_role as _is_numeric_role
from bot.role import is_row as _is_row

_role = ROLE.from_str_with_default(os.environ.get("MAKA_ROLE", ""), ROLE.BUD)
_row = int(os.environ.get("MAKA_ROW", "-1"))


def role() -> ROLE:
    return _role


def row() -> int:
    return _row


def is_bud() -> bool:
    return _role == ROLE.BUD


def is_command() -> bool:
    return _role == ROLE.COMMAND


def is_module() -> bool:
    return _role == ROLE.MODULE


def is_row() -> bool:
    return _is_row(_role)


def has_secondary() -> bool:
    return _role == ROLE.ROW_PRIMARY or _role == ROLE.ROW_SECONDARY or _role == ROLE.SIMULATOR_MINICOMPUTERS


def is_simulator() -> bool:
    return _role == ROLE.SIMULATOR or _role == ROLE.SIMULATOR_MINICOMPUTERS


def is_numeric_role() -> bool:
    return _is_numeric_role(_role)

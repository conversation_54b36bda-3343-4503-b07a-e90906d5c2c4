import os
from typing import List, Optional

_EMOJI_TTF_PATHS: List[str] = [
    # https://github.com/eosrei/twemoji-color-font/releases
    "/usr/share/fonts/truetype/emoji/TwitterColorEmoji-SVGinOT.ttf",
    # Doesn't actually work when getting drawn. Maybe fixable?
    # <NAME_EMAIL>:potyt/fonts.git ~/.fonts
    os.path.expanduser("~/.fonts/macfonts/Apple Color Emoji/Apple Color Emoji.ttf"),
]

_MONOSPACE_TTF_PATHS: List[str] = [
    # Linux
    "/usr/share/fonts/truetype/noto/NotoMono-Regular.ttf",
    # Mac
    "/Library/Fonts/Courier New.ttf",
]


def emoji_ttf() -> Optional[str]:
    return next((f for f in _EMOJI_TTF_PATHS if os.path.exists(f)), None)


def monospace_ttf() -> Optional[str]:
    return next((f for f in _MONOSPACE_TTF_PATHS if os.path.exists(f)), None)

import streamlit as st

from lib.common.db.carbon_db_lite import CarbonDBLite


class DBTableViewer:
    def __init__(self, db: CarbonDBLite, table_name: str) -> None:
        self._db = db
        self._table_name = table_name
        self._formatted_name = self._table_name.replace("_", " ").title()
        self._title = st.header(self._formatted_name)
        self._placeholder = st.empty()

    def update(self) -> None:
        pass

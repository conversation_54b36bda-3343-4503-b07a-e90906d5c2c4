import os
import threading
from typing import List, Optional

from lib.common.db.carbon_db_lite import CarbonDBLite, CarbonRobotTableBase
from lib.common.db.carbon_db_processor import CarbonDBProcessor
from lib.common.db.records.first_weed_detection_samples import FirstWeedDetectionSamples
from lib.common.db.records.laser_records import Laser<PERSON><PERSON>ord, LaserRecordRowOwner
from lib.common.db.records.weed_accuracy_samples import WeedAccuracySamples
from lib.common.db.records.weed_tracking_error_by_distance_samples import WeedTrackingErrorByDistanceSamples


class CarbonRobotDBLite(CarbonDBLite):

    INSTANCE_LOCK: threading.Lock = threading.Lock()
    INSTANCE: Optional["CarbonRobotDBLite"] = None

    def __init__(self, load_async: bool = True) -> None:
        super().__init__(
            filepath=f"{os.environ.get('MAKA_DATA_DIR', '/data')}/carbon_robot_db_lite.db", load_async=load_async
        )
        CarbonRobotTableBase.metadata.create_all(self._engine)
        if load_async:
            self._laser_record_owner = LaserRecordRowOwner(self)
            self._db_processor = CarbonDBProcessor(self)

    @property
    def laser_record_owner(self) -> LaserRecordRowOwner:
        return self._laser_record_owner

    @property
    def db_processor(self) -> CarbonDBProcessor:
        return self._db_processor

    def list_tables(self) -> List[str]:
        return [
            LaserRecord.__tablename__,
            WeedAccuracySamples.__tablename__,
            FirstWeedDetectionSamples.__tablename__,
            WeedTrackingErrorByDistanceSamples.__tablename__,
        ]

    @classmethod
    def get_instance(cls) -> "CarbonRobotDBLite":
        with cls.INSTANCE_LOCK:
            if cls.INSTANCE is None:
                cls.INSTANCE = CarbonRobotDBLite()
            return cls.INSTANCE

import asyncio
import traceback
from typing import List

from lib.common.asyncio.event_loop import use_specific_loop
from lib.common.db.carbon_db_lite import CarbonDBLite, CarbonRobotTableBase
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time.time import maka_control_timestamp_ms


class CarbonDBRowOwner:
    def __init__(self, db: CarbonDBLite, commit_interval_ms: int = 60000) -> None:
        self._db = db
        self._session = self._db.get_async_session()
        self._event_loop = get_event_loop_by_name()
        with use_specific_loop(self._event_loop):
            self._session_lock = asyncio.Lock()
        self._persist_task = self._event_loop.create_task(self._persist_loop(commit_interval_ms=commit_interval_ms))

    async def _persist_loop(self, commit_interval_ms: int) -> None:
        while True:
            try:
                last_commit: int = maka_control_timestamp_ms()
                while True:
                    time_till_commit = commit_interval_ms - (maka_control_timestamp_ms() - last_commit)
                    if time_till_commit <= 0:
                        await self.commit()
                        last_commit = maka_control_timestamp_ms()
                        continue
                    else:
                        await asyncio.sleep(time_till_commit / 1000)
            except Exception:
                traceback.print_exc()
                await asyncio.sleep(5)

    async def commit(self) -> None:
        async with self._session_lock:
            await self._session.commit()

    async def ensure_commit(self) -> None:
        asyncio.get_event_loop().create_task(self.commit())


class CarbonDBProcessor:
    def __init__(self, db: CarbonDBLite, commit_interval_ms: int = 60000) -> None:
        self._db = db
        self._commit_interval_ms = commit_interval_ms
        self._event_loop = get_event_loop_by_name()
        with use_specific_loop(self._event_loop):
            self._insert_queue: asyncio.Queue[CarbonRobotTableBase] = asyncio.Queue()
        self._insert_task = self._event_loop.create_task(self._insert_loop())

    async def add_row(self, row: CarbonRobotTableBase) -> None:
        await self._insert_queue.put(row)

    async def _insert_loop(self) -> None:
        while True:
            try:
                last_commit: int = maka_control_timestamp_ms()
                session = self._db.get_async_session()
                while True:
                    time_till_commit = self._commit_interval_ms - (maka_control_timestamp_ms() - last_commit)
                    if time_till_commit > 0:
                        try:
                            row = await self._insert_queue.get()
                            session.add(row)
                        except asyncio.QueueEmpty:
                            await asyncio.sleep(1)
                        continue
                    await session.commit()
                    last_commit = maka_control_timestamp_ms()
            except Exception:
                traceback.print_exc()
                await asyncio.sleep(5)

    async def add_rows(self, rows: List[CarbonRobotTableBase]) -> None:
        if len(rows) > 0:
            await asyncio.wait(
                [self._insert_queue.put(row) for row in rows],
                loop=asyncio.get_event_loop(),
                return_when=asyncio.ALL_COMPLETED,
            )

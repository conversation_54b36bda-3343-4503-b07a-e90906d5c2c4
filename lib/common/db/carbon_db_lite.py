from sqlalchemy import create_engine
from sqlalchemy.engine.base import Engine
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, sessionmaker


class CarbonDBLite:
    def __init__(self, filepath: str, load_async: bool = False) -> None:
        self._filepath = filepath
        self._engine = create_engine(f"sqlite:///{filepath}")
        self._async_engine = create_async_engine(f"sqlite+aiosqlite:///{filepath}") if load_async else None
        self._session_maker = sessionmaker(bind=self._engine)
        self._async_session_maker = sessionmaker(bind=self._async_engine, expire_on_commit=False, class_=AsyncSession)

    @property
    def engine(self) -> Engine:
        return self._engine

    @property
    def async_engine(self) -> AsyncEngine:
        assert self._async_engine is not None
        return self._async_engine

    def get_session(self) -> Session:
        return self._session_maker()

    def get_async_session(self) -> AsyncSession:
        return self._async_session_maker()


CarbonRobotTableBase = declarative_base()

from typing import Any, Optional

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.sql.expression import select

from lib.common.db.carbon_db_lite import CarbonDBLite, CarbonRobotTableBase
from lib.common.db.carbon_db_processor import CarbonDBRowOwner


class LaserRecord(CarbonRobotTableBase):
    __tablename__ = "laser_records"

    laser_id = Column(Integer, primary_key=True)
    millis = Column(BigInteger)
    triggers = Column(Integer)


class LaserRecordRowOwner(CarbonDBRowOwner):
    def __init__(self, db: CarbonDBLite, commit_interval_ms: int = 60000) -> None:
        super().__init__(db, commit_interval_ms=commit_interval_ms)

    async def get(self, laser_id: int, **kwaargs: Any) -> LaserRecord:
        data = await self._session.execute(select(LaserRecord).filter_by(laser_id=laser_id).limit(1))
        record: Optional[LaserRecord] = data.scalars().first()
        if record is None:
            record = LaserRecord(laser_id=laser_id, millis=0, triggers=0)
            self._session.add(record)
        return record

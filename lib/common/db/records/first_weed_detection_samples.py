from sqlalchemy import BigInteger, Column, Float, Integer

from lib.common.db.carbon_db_lite import CarbonRobotTableBase


class FirstWeedDetectionSamples(CarbonRobotTableBase):
    __tablename__ = "first_weed_detection_samples"

    pcam_id = Column(Integer, primary_key=True)
    session = Column(BigInteger, primary_key=True)
    weed_id = Column(Integer, primary_key=True)
    timestamp = Column(BigInteger, primary_key=True)
    vertical_pixel_offset = Column(Float)  # The pixel y location of the first detected weed
    number_missed_detection_opportunities = Column(
        Integer
    )  # The number of times a weed could have been detected before it was actually initially detected

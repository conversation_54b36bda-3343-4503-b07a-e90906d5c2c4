from sqlalchemy import BigInteger, Column, Float, Integer

from lib.common.db.carbon_db_lite import CarbonRobotTableBase


class WeedTrackingErrorByDistanceSamples(CarbonRobotTableBase):
    __tablename__ = "weed_tracking_error_by_distance_samples"

    pcam_id = Column(Integer, primary_key=True)
    session = Column(BigInteger, primary_key=True)
    weed_id = Column(Integer, primary_key=True)
    timestamp = Column(BigInteger, primary_key=True)
    request_id = Column(BigInteger, primary_key=True)
    distance_px = Column(Float)  # Distance from first detection
    error_px = Column(Float)  # offset error

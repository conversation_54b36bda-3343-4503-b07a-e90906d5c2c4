from sqlalchemy import BigInteger, Column, Float, Integer

from lib.common.db.carbon_db_lite import CarbonRobotTableBase


class WeedAccuracySamples(CarbonRobotTableBase):
    __tablename__ = "weed_accuracy_samples"

    scanner_id = Column(Integer, primary_key=True)
    timestamped_weed_id = Column(BigInteger, primary_key=True)
    sample_count = Column(Integer, primary_key=True)
    offset_y_px = Column(Float)
    offset_x_px = Column(Float)
    pos_pan_ticks = Column(Float)
    pos_tilt_ticks = Column(Float)
    offset_pan_ticks = Column(Float)
    offset_tilt_ticks = Column(Float)
    offset_y_mm = Column(Float)
    offset_x_mm = Column(Float)

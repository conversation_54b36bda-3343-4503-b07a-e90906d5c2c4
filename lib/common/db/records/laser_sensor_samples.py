from sqlalchemy import BigInteger, Boolean, Column, Float, Integer

from lib.common.db.carbon_db_lite import CarbonRobotTableBase


class LaserSensorSamples(CarbonRobotTableBase):
    __tablename__ = "laser_sensor_samples"

    laser_id = Column(Integer, primary_key=True)
    timestamp_ms = Column(BigInteger, primary_key=True)
    e_stopped = Column(Boolean)
    firing = Column(Boolean)
    intensity = Column(Float)
    current_sensor = Column(Float)
    thermistor1 = Column(Float)
    thermistor2 = Column(Float)

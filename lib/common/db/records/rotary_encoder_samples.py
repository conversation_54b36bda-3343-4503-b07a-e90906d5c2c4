from sqlalchemy import Big<PERSON><PERSON><PERSON>, Column, Integer, String

from lib.common.db.carbon_db_lite import CarbonRobotTableBase


class RotaryEncoderSamples(CarbonRobotTableBase):
    __tablename__ = "rotary_encoder_samples"

    request_id = Column(BigInteger, primary_key=True)
    timestamp_requested_from = Column(BigInteger, primary_key=True)
    timestamp_requested_to = Column(BigInteger, primary_key=True)
    requesting_tracker = Column(String, primary_key=True)

    request_first = Column(BigInteger)
    request_last = Column(BigInteger)

    snapshot_first_before_time = Column(BigInteger)
    snapshot_first_after_time = Column(BigInteger)
    snapshot_last_before_time = Column(BigInteger)
    snapshot_last_after_time = Column(BigInteger)

    snapshot_first_before_fl = Column(Integer)
    snapshot_first_before_fr = Column(Integer)
    snapshot_first_before_bl = Column(Integer)
    snapshot_first_before_br = Column(Integer)

    snapshot_first_after_fl = Column(Integer)
    snapshot_first_after_fr = Column(Integer)
    snapshot_first_after_bl = Column(Integer)
    snapshot_first_after_br = Column(Integer)

    snapshot_last_before_fl = Column(Integer)
    snapshot_last_before_fr = Column(Integer)
    snapshot_last_before_bl = Column(Integer)
    snapshot_last_before_br = Column(Integer)

    snapshot_last_after_fl = Column(Integer)
    snapshot_last_after_fr = Column(Integer)
    snapshot_last_after_bl = Column(Integer)
    snapshot_last_after_br = Column(Integer)

    interpolated_from_fl = Column(Integer)
    interpolated_from_fr = Column(Integer)
    interpolated_from_bl = Column(Integer)
    interpolated_from_br = Column(Integer)

    interpolated_to_fl = Column(Integer)
    interpolated_to_fr = Column(Integer)
    interpolated_to_bl = Column(Integer)
    interpolated_to_br = Column(Integer)

# Copied from here: ttps://github.com/KieranWynn/pyquaternion/pull/38/files
# Hopefully this makes it in to pyquaternion some day.

import math
from typing import <PERSON>ple

from pyquaternion import Quaternion


def quat2euler(q: Quaternion, convention: str = "XYZ") -> Tuple[float, float, float]:
    """
    pyquaternion doesn't have a way to do these conversions with different conventions.

    return yaw, pitch, roll according to convention to be used
    source from https://www.andre-gaschler.com/rotationconverter/
    """
    q._normalise()
    s = q.transformation_matrix
    a = s[0, 0]
    f = s[0, 1]
    g = s[0, 2]
    h = s[1, 0]
    k = s[1, 1]
    l = s[1, 2]
    m = s[2, 0]
    n = s[2, 1]
    e = s[2, 2]

    yaw: float = 0
    pitch: float = 0
    roll: float = 0
    if convention == "XYZ":
        pitch = math.asin(g)
        if 0.99999 > math.fabs(g):
            roll = math.atan2(-l, e)
            yaw = math.atan2(-f, a)
        else:
            roll = math.atan2(n, k)
            yaw = 0
    elif convention == "YXZ":
        roll = math.asin(-l)
        if 0.99999 > math.fabs(l):
            pitch = math.atan2(g, e)
            yaw = math.atan2(h, k)
        else:
            pitch = math.atan2(-m, a)
            yaw = 0
    elif convention == "ZXY":
        roll = math.asin(n)
        if 0.99999 > math.fabs(n):
            pitch = math.atan2(-m, e)
            yaw = math.atan2(-f, k)
        else:
            pitch = 0
            yaw = math.atan2(h, a)
    elif convention == "ZYX":
        pitch = math.asin(-m)
        if 0.99999 > math.fabs(m):
            roll = math.atan2(n, e)
            yaw = math.atan2(h, a)
        else:
            roll = 0
            yaw = math.atan2(-f, k)
    elif convention == "YZX":
        yaw = math.asin(h)
        if 0.99999 > math.fabs(h):
            roll = math.atan2(-l, k)
            pitch = math.atan2(-m, a)
        else:
            roll = 0
            pitch = math.atan2(g, e)
    elif convention == "XZY":
        yaw = math.asin(-f)
        if 0.99999 > math.fabs(f):
            roll = math.atan2(n, k)
            pitch = math.atan2(g, a)
        else:
            roll = math.atan2(-l, e)
            pitch = 0
    else:
        raise ValueError("unknown convention name")

    return yaw, pitch, roll

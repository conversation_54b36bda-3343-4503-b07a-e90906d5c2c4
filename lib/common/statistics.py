import statistics
from typing import Any, <PERSON><PERSON>, cast

import numpy as np
import numpy.typing as npt

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)


class Errors:
    def __init__(self, min_: float, mean: float, max_: float, stdev: float):
        self.min = min_
        self.mean = mean
        self.max = max_
        self.stdev = stdev


def min_mean_max_stdev(data: Any) -> Tuple[float, float, float, float]:
    assert len(data) > 0

    # numpy doesn't work with statistics module in python
    if type(data[0]).__module__ == np.__name__:
        darray = np.array(data)
        min_ = darray.min()
        max_ = darray.max()
        mean = darray.mean()
        stdev = darray.std()
    else:
        min_ = min(data)
        max_ = max(data)
        mean = statistics.mean(data)
        stdev = statistics.stdev(data)

    return min_, mean, max_, stdev


def errors(data: Any) -> Errors:
    return Errors(*min_mean_max_stdev(data))


def remove_outliers_index(elements: npt.NDArray[Any], deviations: int) -> npt.NDArray[Any]:
    mean = np.mean(elements)
    std_dev = np.std(elements)
    distances = abs(elements - mean)
    good = distances <= deviations * std_dev
    return cast(npt.NDArray[Any], good)


def remove_outliers(elements: npt.NDArray[Any], deviations: int) -> npt.NDArray[Any]:
    return cast(npt.NDArray[Any], elements[remove_outliers_index(elements, deviations)])

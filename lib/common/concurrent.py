import threading
from datetime import datetime
from typing import Any, Callable, Generic, List, Optional, Tuple, TypeVar, Union
from uuid import UUID, uuid4

import lib.common.logging
from lib.common.collections import RollingOrderedDict
from lib.common.time.time import TimeoutException, maka_control_timestamp_ms

LOG = lib.common.logging.get_logger(__name__)


_MS_SINCE_EPOCH = int(datetime.now().timestamp() * 1000)


# TODO make custom objects and use those instead
def wait_avoid_spurious_wakeup(
    waitable: Union[threading.Event, threading.Condition], timeout_ms: Optional[int] = None
) -> bool:
    """
    Wait at least timeout_ms on the given waitable object.

    Avoid spurious wakeup by re-waiting if we wake up but wait returns False
    and it has not been at least as long as we asked to wait for.

    https://en.wikipedia.org/wiki/Spurious_wakeup
    https://stackoverflow.com/a/1051816
    """
    if timeout_ms is None:
        # effectively wait forever
        timeout_ms = _MS_SINCE_EPOCH

    # result variable
    gotit: bool = False

    # We need to keep our own timestamps to make sure we don't spuriously wakeup early
    now: int = maka_control_timestamp_ms()
    wait_until: int = now + timeout_ms

    # Don't trust that wait() will wait at least as long as we desire.
    first = True
    while not gotit and now < wait_until:
        # calculate and do our wait
        remaining: int = wait_until - now

        # log if we detected a spurious wakeup
        if not first:
            # TODO record metric
            LOG.warning(f"Spurious wakeup detected! {remaining}ms remaining")

        gotit = waitable.wait(timeout=remaining / 1000)

        # if we didn't get it
        if not gotit:
            now = maka_control_timestamp_ms()

        # loop variables
        first = False

    return gotit


T = TypeVar("T")


class LockedObject(Generic[T]):
    """
    A concurrent queue of size one. Basically allows repeated sets of an object that can be popped.
    """

    def __init__(self, initial_data: Optional[T] = None):
        self._uuid: UUID = uuid4()
        self._lock = threading.Lock()
        self._obj: Optional[T] = initial_data

    @property
    def uuid(self) -> UUID:
        return self._uuid

    @property
    def obj(self) -> Optional[T]:
        return self._obj

    def get(self) -> Optional[T]:
        """
        Get the underlying object
        """
        with self._lock:
            ret = self._obj
        return ret

    def set(self, obj: T) -> None:
        """
        Set the underlying object
        """
        with self._lock:
            self._obj = obj

    def pop(self) -> Optional[T]:
        """
        Get and remove the underlying object
        """
        with self._lock:
            ret = self._obj
            self._obj = None
        return ret


class MutableList(Generic[T]):
    """
    A concurrent list. Also tracks a UUID to allow for a notion of fully replacing a list.
    Clients should track the UUID they know about if they care about getting data back from the list.

    Provides set()/append() APIs for modifying list. copy()/get() for reading list.
    """

    def __init__(self, initial_data: Optional[List[T]] = None):
        self._uuid = uuid4()
        self._data: List[T] = initial_data or []
        self._lock = threading.Lock()

    @property
    def uuid(self) -> UUID:
        return self._uuid

    @property
    def data(self) -> List[T]:
        return self._data

    def __len__(self) -> int:
        with self._lock:
            return len(self._data)

    def __setitem__(self, key: int, value: T) -> None:
        self._data[key] = value

    def __getitem__(self, key: int) -> T:
        return self._data[key]

    def __delitem__(self, key: int) -> None:
        del self._data[key]

    def append(self, element: T) -> None:
        """
        Append to the currently stored list.
        """
        assert element is not None
        with self._lock:
            self._data.append(element)

    def extend(self, element_list: List[T]) -> None:
        with self._lock:
            self._data.extend(element_list)

    def copy(self, uuid: bool = False) -> Union[List[T], Tuple[UUID, List[T]]]:
        """
        Return a copy of the underlying list, and optionally the uuid as well

        :param uuid whether to return the uuid as well
        """
        with self._lock:
            return self._data.copy() if not uuid else (self._uuid, self._data.copy())

    def get(self, uuid: UUID, index: int) -> Any:
        """
        Get the element from the underlying list, as long as the full underlying list has not changed.
        """
        assert index >= 0
        with self._lock:
            if self._uuid == uuid:
                return self._data[index]

        return None

    def set(self, data: List[T]) -> UUID:
        """
        Set the elements in the underlying list.

        Returns the new UUID.
        """
        assert data is not None
        with self._lock:
            self._uuid = uuid4()
            self._data = data
            return self._uuid

    def pop(self, index: int) -> T:
        """
        Removes and returns the element at index
        """
        with self._lock:
            self._uuid = uuid4()
            return self._data.pop(index)


class RollingOrderedBuffer(Generic[T]):
    """
    A simple API providing a simple buffer / stream interface for dealing with
    common streams of data in a similar manner.

    This is written by composing a few simple functionalities:
    - a RollingOrderedDict which acts as the underlying buffer with O(1) access
         to first/last element or any element by timestamp
    - a threading.Condition object for notifying of new insertions into the buffer.

    While this class will ultimately be superseded by a more feature-complete
    async messaging architecture, this is a simple start that allows us to
    express concepts and connect our modules in async ways. As we improve the
    underlying communication primitives, our control loops will not change much
    as they have already been built around a buffered stream of timestamped
    objects, which is what much of our codebase is trending towards.

    For callers who need more control of the underlying threading.Lock associated
    with the condition, they can provide an instantiated threading.Condition.
    """

    def __init__(
        self,
        maxlen: int,
        ordering_func: Callable[[T], int],
        condition: Optional[threading.Condition] = None,
        notify_on_full: bool = False,
    ):
        # this function maps value objects to their order, expressed as an integer
        # canonically, this can be:
        #   lambda t: t.timestamp_ms
        self._ordering_func: Callable[[T], int] = ordering_func
        self._output: RollingOrderedDict[int, T] = RollingOrderedDict(maxlen=maxlen)

        if condition is None:
            condition = threading.Condition()

        self._next_object_condition: threading.Condition = condition
        self._notify_on_full = notify_on_full

    @property
    def buffer(self) -> RollingOrderedDict[int, T]:
        return self._output

    @property
    def full(self) -> bool:
        return self._output.full

    def __len__(self) -> int:
        return len(self.buffer)

    # Note that we create a new RollingOrderedDict vs calling reset() to
    # allow any reference holders to continue to access the underlying data
    def reset(self, maxlen: Optional[int] = None) -> None:
        if maxlen is None:
            maxlen = self._output.maxlen

        self._output = RollingOrderedDict(maxlen=maxlen)

    def publish(self, output: T) -> None:
        """
        Make the given output available
        """
        with self._next_object_condition:
            self._output[self._ordering_func(output)] = output

            if not self._notify_on_full or self.full:
                self._next_object_condition.notify_all()

    # the buffer
    def last(self) -> Optional[T]:
        """
        :return: the last object in the buffer
        """
        return self._output.last()

    def after_or_next(self, order_value: int) -> T:
        """
        Return the latest object satisfying:

        - after the given timestamp
        OR
        - the next object

        This will be either the last or next object depending on the timestamps.

        This is a useful function for connecting a consumer to a producer if the
        consumer only needs to make a decision once per event
        """
        obj: Optional[T] = self.last()
        if obj is not None and self._ordering_func(obj) > order_value:
            return obj
        else:
            return self.next()

    # the stream
    def next(self, timeout_ms: int = 3000) -> T:
        """
        Returns the next TimestampedObject

        raises TimeoutException if there is a timeout in retrieving the next camera image
        """
        assert timeout_ms is not None, "Must pass timeout or could block forever"

        with self._next_object_condition:
            gotit = wait_avoid_spurious_wakeup(waitable=self._next_object_condition, timeout_ms=timeout_ms)
            # check if we timed out
            if not gotit:
                raise TimeoutException(f"{timeout_ms}ms timeout")

            # immediately grab the next object. This is a race condition but
            # the beautiful thing is we don't actually care. If we get an even
            # newer object, great!
            result: Optional[T] = self.last()

            # defensive programming so we can rely on this assumption elsewhere
            # that this function and its clients don't need to handle the None case
            assert (
                result is not None
            ), "Bug detected. Result shouldn't be None after we successfully got a new object event"

            return result

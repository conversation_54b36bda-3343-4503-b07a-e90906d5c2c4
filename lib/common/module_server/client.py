from typing import Optional

import grpc

import generated.proto.module.server.server_pb2 as module_server_pb
import generated.proto.module.server.server_pb2_grpc as module_server_grpc
import generated.proto.module.types.types_pb2 as module_types_pb
from generated.hardware_manager.proto import hardware_manager_service_pb2 as hardware_manager_pb
from hardware_manager.python.types import ModulePcSensorData


class ModuleServerClient:
    def __init__(self, hostname: str = "127.0.0.1", port: int = 61016):
        self._hostname = hostname
        self._port: int = port
        self._channel: Optional[grpc.Channel] = None
        self._stub: Optional[module_server_grpc.ModuleServerServiceStub] = None

    def _maybe_connect(self) -> None:
        if self._channel is None:
            self._channel = grpc.aio.insecure_channel(f"{self._hostname}:{self._port}")
        if self._stub is None:
            self._stub = module_server_grpc.ModuleServerServiceStub(self._channel)

    def _get_stub(self) -> module_server_grpc.ModuleServerServiceStub:
        self._maybe_connect()
        assert self._stub is not None
        return self._stub

    async def GetModuleIdentity(self) -> module_types_pb.ModuleIdentity:
        stub = self._get_stub()
        response: module_server_pb.GetModuleIdentityResponse = await stub.GetModuleIdentity(module_types_pb.Empty())
        return response.identity

    async def SetModuleSerialNumber(self, serial: str, force: bool) -> None:
        stub = self._get_stub()
        request = module_server_pb.SetModuleSerialNumberRequest(serial_number=serial, force=force)
        await stub.SetModuleSerialNumber(request)

    async def SetModuleIdentity(self, id: int, serial: str) -> None:
        stub = self._get_stub()
        request = module_server_pb.SetModuleIdentityRequest(
            identity=module_types_pb.ModuleIdentity(id=id, serial=serial)
        )
        await stub.SetModuleIdentity(request)

    async def GetModuleSensors(self) -> ModulePcSensorData:
        stub = self._get_stub()
        response: hardware_manager_pb.ReaperPcSensorData = await stub.GetModuleSensors(module_types_pb.Empty())
        return ModulePcSensorData.FromMessage(response)

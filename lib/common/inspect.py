import traceback
from functools import wraps
from typing import Any, Callable, TypeVar, cast

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)
FuncT = TypeVar("FuncT", bound=Callable[..., Any])


def qualified_classname(obj: Any) -> str:
    """
    Returns the fully qualified class name of an object. e.g. "foo.inspect.Example"
    """
    return ".".join(
        [obj.__module__ if hasattr(obj, "__module__") else obj.__class__.__module__, obj.__class__.__name__]
    )


def show_trace(func: FuncT) -> FuncT:
    @wraps(func)
    def wrapped(*args: Any, **kwargs: Any) -> Any:
        LOG.info(f"Traceback for calling {func.__name__}: {traceback.extract_stack()}")
        return func(*args, **kwargs)

    return cast(FuncT, wrapped)

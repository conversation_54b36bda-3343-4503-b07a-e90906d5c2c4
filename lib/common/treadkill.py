import math
from typing import Any, Dict, List, Optional, Tuple, cast

import cv2
import numpy as np
import numpy.typing as npt
from cv2 import aruco

from lib.common.annotate import BGR_BROWN
from lib.common.coord import TimestampedCoord
from lib.common.math import add_tuples
from lib.common.time import maka_control_timestamp_ms


class ArucoPlateTreadKill:
    def __init__(
        self,
        resolution: Tuple[int, int],
        n_markers: int = 1,
        marker_size_px: int = 10,
        diag_size_in: float = 55,
        velocity_mph: Tuple[float, float] = (0, 0.1),
        marker_positions: Optional[List[Tuple[int, int]]] = None,
        background_color: Tuple[int, int, int] = BGR_BROWN,
    ):
        # Args
        self._resolution = resolution
        self._n_markers = n_markers
        self._marker_size_px = marker_size_px
        self._diag_size_in = diag_size_in
        self._velocity_mph = velocity_mph
        self._marker_positions = marker_positions
        self._background_color = background_color

        # Deduced Args
        self._ppi = math.sqrt((self._resolution[0] ** 2) + (self._resolution[1] ** 2)) / self._diag_size_in
        inches_per_mile = 63360
        seconds_per_hour = 3600
        self._velocity_pxs = tuple((x * self._ppi * inches_per_mile / seconds_per_hour for x in velocity_mph))
        self._last_step: Optional[int] = None
        self._last_remainder: Tuple[float, ...] = (0, 0)

        # Marker Positions
        assert marker_positions is None or len(marker_positions) == n_markers
        if marker_positions is None:
            self._marker_positions = []
            for _ in range(n_markers):
                self._marker_positions.append(
                    (
                        np.random.randint(0, self._resolution[0] - self.buffer_size_px()),
                        np.random.randint(0, self._resolution[1] - self.buffer_size_px()),
                    )
                )

        # Views
        self._field_image = np.full(
            [self._resolution[1], self._resolution[0], 3], cast(complex, self._background_color), dtype=np.uint8
        )
        self.reset()

    def reset(self) -> None:
        assert self._marker_positions is not None
        for i, pos in enumerate(self._marker_positions):
            plate = self.get_marker_plate(i)
            self._field_image[pos[1] : pos[1] + plate.shape[0], pos[0] : pos[0] + plate.shape[1]] = plate

    def get_marker_plate(self, id: int) -> npt.NDArray[Any]:
        plate = np.zeros([self._marker_size_px * 5, self._marker_size_px * 3, 3], dtype=np.uint8)
        plate.fill(255)
        marker_img = aruco.generateImageMarker(
            dictionary=aruco.getPredefinedDictionary(aruco.DICT_4X4_50),
            id=id,
            sidePixels=self._marker_size_px,
            borderBits=1,
        )
        marker_img = cv2.cvtColor(marker_img, cv2.COLOR_GRAY2BGR)
        plate[
            self._marker_size_px * 3 : 3 * self._marker_size_px + self._marker_size_px,
            self._marker_size_px : self._marker_size_px + self._marker_size_px,
        ] = marker_img
        return plate

    @property
    def field_image(self) -> npt.NDArray[Any]:
        return self._field_image

    def buffer_size_px(self) -> int:
        return 5 * self._marker_size_px

    def step(self) -> Tuple[int, ...]:
        if self._last_step is None:
            self._last_step = maka_control_timestamp_ms()
            return 0, 0
        current = maka_control_timestamp_ms()
        delta: float = (current - self._last_step) / 1000
        self._last_step = current
        velocity: Tuple[float, ...] = tuple((x * delta for x in self._velocity_pxs))
        velocity = add_tuples(velocity, self._last_remainder)
        velocity_int: Tuple[int, ...] = tuple((int(x) for x in velocity))
        self._last_remainder = tuple((x % 1 for x in velocity))
        self._field_image = np.roll(self._field_image, (velocity_int[1], velocity_int[0]), axis=(0, 1))
        return velocity_int

    @staticmethod
    def get_plate_points(image: npt.NDArray[Any]) -> Dict[int, TimestampedCoord]:
        arucoDetector = aruco.ArucoDetector(
            cv2.aruco.getPredefinedDictionary(cv2.aruco.DICT_4X4_50), aruco.DetectorParameters()
        )
        corners, ids, _ = arucoDetector.detectMarkers(image)
        pcoords = [(sum([x[0] for x in m[0]]) / 4, sum([x[1] for x in m[0]]) / 4) for m in corners]
        points = {}
        list_ids = list(ids.flatten())
        for id, pcoord in zip(ids, pcoords):
            i = list_ids.index(id)
            first_x = corners[i][0][0][0]
            total_x = sum([x[0] for x in corners[i][0]])
            first_y = corners[i][0][0][1]
            total_y = sum([x[1] for x in corners[i][0]])
            side = abs(((2 * first_x - total_x / 2) + (2 * first_y - total_y / 2)) / 2)
            points[id] = TimestampedCoord(add_tuples(pcoord, (0, -2 * side)))
        return points

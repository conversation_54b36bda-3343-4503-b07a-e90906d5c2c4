from typing import Any, Callable, TypeVar, overload

import retry as r

F = TypeVar("F", bound=Callable[..., Any])


@overload
def retry(fn: F) -> F:
    """Type signature for @retry as a raw decorator."""
    pass


@overload
def retry(*args: Any, **kwargs: Any) -> Callable[[F], F]:
    """Type signature for the @retry() decorator constructor."""
    pass


def retry(*args: Any, **kwargs: Any) -> Any:
    return r.retry(*args, **kwargs)

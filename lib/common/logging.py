import argparse
import json
import logging
import logging.handlers
import os
import sys
import threading
import time
from datetime import datetime
from logging import LogRecord
from typing import Any, Callable, Dict, List, Optional, Tuple, Union, cast

import grpc

import carbon_logging.pybind.logging_python as cpp_logger
import generated.proto.logging.logging_pb2 as logging_pb
import generated.proto.logging.logging_pb2_grpc as logging_pb_grpc
from lib.common.serialization.json import JsonSerializable

logging.getLogger("asyncio").setLevel(logging.ERROR)
logging.getLogger("asyncio.coroutines").setLevel(logging.ERROR)
logging.getLogger("websockets.server").setLevel(logging.ERROR)
logging.getLogger("websockets.protocol").setLevel(logging.ERROR)

# Format is as follows:
# 2019-05-10 11:11:21,644 DEBUG    [foo.bar.baz:1] (MainThread) test debug log
# 2019-05-10 11:11:21,645 INFO     [foo.bar.baz:2] (MainThread) test info log
# 2019-05-10 11:11:21,646 WARNING  [foo.bar.baz:3] (MainThread) test warn log
# 2019-05-10 11:11:21,647 ERROR    [foo.bar.baz:4] (MainThread) test error log
# 2019-05-10 11:11:21,649 CRITICAL [foo.bar.baz:5] (MainThread) test critical log
#
# use NO_TS_FORMAT to omit timestamps
NO_TS_FORMAT = "%(levelname)-8s [%(name)s:%(lineno)s] (%(threadName)s) %(message)s"
ASCTIME_FORMAT = "%(asctime)s "
TS_FORMAT = ASCTIME_FORMAT + NO_TS_FORMAT
LOG_DIR = "/data/logs"


def get_logger(name: str) -> logging.Logger:
    """
    Shim for logging.getLogger.

    Currently doesn't do anything else, and that's intentional.

    Mostly just gives us get_logger with underscore instead. And has nice symmetry with get_data_logger defined below.
    """
    return logging.getLogger(name)


LOG = get_logger(__name__)


def logger_level_to_levelstr(level: int) -> str:
    if level == logging.DEBUG:
        return "DEBUG"
    if level == logging.CRITICAL:
        return "CRITICAL"
    if level == logging.ERROR:
        return "ERROR"
    if level == logging.FATAL:
        return "FATAL"
    if level == logging.INFO:
        return "INFO"
    if level == logging.WARNING:
        return "WARNING"
    if level == logging.NOTSET:
        return "NOTSET"

    raise ValueError(f"Invalid logging level {level}")


def logger_levelstr_to_level(levelstr: str) -> int:
    if hasattr(logging, levelstr.upper()):
        return cast(int, getattr(logging, levelstr.upper()))

    raise ValueError(f"Invalid logging level string {levelstr}")


def set_level(levelstr: str) -> None:
    root_logger = logging.getLogger()
    root_logger.setLevel(levelstr)


class LoggingGRPCServicer(logging_pb_grpc.LoggingServiceServicer):
    def __init__(self, server: grpc.aio.Server, logfile: str):
        logging_pb_grpc.add_LoggingServiceServicer_to_server(self, server)
        # dont fail in case log dir was deleted
        os.makedirs(LOG_DIR, exist_ok=True)
        self.fh_ = logging.FileHandler(os.path.join(LOG_DIR, logfile))
        self.fh_.setFormatter(logging.Formatter(TS_FORMAT))
        logging.getLogger().addHandler(self.fh_)
        cpp_logger.init_logger(logfile)

    async def SetLevel(self, request: logging_pb.SetLevelRequest, context: grpc.ServicerContext) -> Any:
        set_level(logging_pb.LogLevel.Name(request.logLevel))
        cpp_logger.set_log_level(request.logLevel)
        context.set_code(grpc.StatusCode.OK)
        return logging_pb.Empty()


# Custom formatter
class ColorFormatter(logging.Formatter):

    GRAY = "\033[17m"
    YELLOW = "\033[33m"
    GRAY = "\033[2;49;39m"
    RED = "\033[01;31m"
    BOLD_RED = "\033[01;31m"
    ENDC = "\033[0m"

    def __init__(self, stdout_timestamps: bool = True):
        super().__init__(fmt=TS_FORMAT if stdout_timestamps else NO_TS_FORMAT)

        base_custom_format = f"{NO_TS_FORMAT}{ColorFormatter.ENDC}"

        self.debug_format = f"{ColorFormatter.GRAY}{base_custom_format}"
        self.info_format = NO_TS_FORMAT
        self.warning_format = f"{ColorFormatter.YELLOW}{base_custom_format}"
        self.error_format = f"{ColorFormatter.RED}{base_custom_format}"
        self.critical_format = f"{ColorFormatter.BOLD_RED}{base_custom_format}"

        if stdout_timestamps:
            ts_format = f"{ColorFormatter.GRAY}{ASCTIME_FORMAT}{ColorFormatter.ENDC}"
            self.debug_format = f"{ts_format}{self.debug_format}"
            self.info_format = f"{ts_format}{self.info_format}"
            self.warning_format = f"{ts_format}{self.warning_format}"
            self.error_format = f"{ts_format}{self.error_format}"
            self.critical_format = f"{ts_format}{self.critical_format}"

    def format(self, record: LogRecord) -> str:

        # Save the original format configured by the user
        # when the logger formatter was instantiated
        format_orig = self._style._fmt

        # Replace the original format with one customized by logging level
        if record.levelno == logging.DEBUG:
            self._style._fmt = self.debug_format

        elif record.levelno == logging.INFO:
            self._style._fmt = self.info_format

        elif record.levelno == logging.WARNING:
            self._style._fmt = self.warning_format

        elif record.levelno == logging.ERROR:
            self._style._fmt = self.error_format

        elif record.levelno == logging.CRITICAL:
            self._style._fmt = self.critical_format

        # Call the original formatter class to do the grunt work
        result = logging.Formatter.format(self, record)

        # Restore the original format configured by the user
        self._style._fmt = format_orig

        return result


def standard_hourly_rotating_file_handler(logfile: str) -> logging.handlers.TimedRotatingFileHandler:
    """
    A TimedRotatingFileHandler with a standard set of arguments for UTC hourly rotation and 90 day persistence.
    """
    # TODO these would be nice to move into argparse
    logfile_handler = logging.handlers.TimedRotatingFileHandler(
        logfile, when="H", interval=1, backupCount=24 * 90, utc=True,  # once per hour, # keep last 90 days
    )
    return logfile_handler


def init_log(
    *,
    logfile: Optional[str] = None,
    level: str = "INFO",
    data_logfile: Optional[str] = None,
    websocket_logfile: Optional[str] = None,
    perf_logfile: Optional[str] = None,
    stdout: bool = True,
    stdout_timestamps: bool = True,
    colorize: bool = False,
) -> None:
    # Set logger level
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    # set up in-memory logging cache
    queue_handler = RollingLogCacheHandler()
    queue_handler.setFormatter(logging.Formatter(TS_FORMAT))
    root_logger.addHandler(queue_handler)

    # don't actually log until we're all setup
    lazy_logs: List[Callable[[], None]] = []

    # set up file logging
    if logfile is not None:
        os.makedirs(LOG_DIR, exist_ok=True)
        logfile = os.path.join(LOG_DIR, logfile)
        logfile_handler = standard_hourly_rotating_file_handler(logfile)
        logfile_handler.setFormatter(logging.Formatter(TS_FORMAT))
        root_logger.addHandler(logfile_handler)
        lazy_logs.append(lambda: LOG.info(f"Logging to {logfile}"))
    else:
        lazy_logs.append(lambda: LOG.warning("Root logger has no file"))

    # set up stdout logging
    if stdout:
        stdout_handler = logging.StreamHandler(sys.stdout)
        if colorize:
            formatter: logging.Formatter = ColorFormatter(stdout_timestamps=stdout_timestamps)
        else:
            formatter = logging.Formatter(TS_FORMAT if stdout_timestamps else NO_TS_FORMAT)
        stdout_handler.setFormatter(formatter)
        root_logger.addHandler(stdout_handler)

    # ok now sysout is attached, user will see logs, go ahead
    for lazy_log_callable in lazy_logs:
        lazy_log_callable()

    # data.log
    init_data_log(data_logfile=data_logfile)

    # websocket.log
    init_websocket_log(websocket_logfile=websocket_logfile)

    # perf.log
    init_perf_log(perf_logfile=perf_logfile)


def _init_log(root_name: str, formatter: logging.Formatter, logfile: Optional[str] = None) -> logging.Logger:
    logger = logging.getLogger(root_name)
    # only log this log, don't propagate to root logger
    logger.propagate = False

    if logfile is not None:
        logfile_handler = standard_hourly_rotating_file_handler(logfile)

        # set up formatter
        logfile_handler.setFormatter(formatter)

        # attach handler
        logger.addHandler(logfile_handler)
    return logger


DATA_ROOT_LOGGER_NAME = "data"
# Emit logs as JSON
DATA_FORMAT = (
    "{"
    '"meta": {'
    '"time": "%(asctime)s.%(msecs)03d", '
    '"logger": "%(name)s", '
    '"lineno": %(lineno)s, '
    '"threadName": "%(threadName)s"'
    "}, "
    '"data": %(message)s'
    "}"
)

ROLLOVER_LIMIT_BYTES = 2 ** 30 * 8
BACKUP_COUNT = 100


def init_data_log(data_logfile: Optional[str] = None) -> None:
    _init_log(DATA_ROOT_LOGGER_NAME, logging.Formatter(fmt=DATA_FORMAT, datefmt="%Y-%m-%d %H:%M:%S"), data_logfile)


WEBSOCKET_ROOT_LOGGER_NAME = "ws"
WEBSOCKET_FORMAT = (
    "{"
    '"meta": {'
    '"time": "%(asctime)s.%(msecs)03d", '
    '"logger": "%(name)s", '
    '"lineno": %(lineno)s, '
    '"threadName": "%(threadName)s"'
    "}, "
    '"msg": %(message)s'
    "}"
)


def init_websocket_log(websocket_logfile: Optional[str] = None) -> None:
    logger = _init_log(
        WEBSOCKET_ROOT_LOGGER_NAME,
        logging.Formatter(fmt=WEBSOCKET_FORMAT, datefmt="%Y-%m-%d %H:%M:%S"),
        websocket_logfile,
    )
    # swallow any attempt at logging unless someone attaches a logfile
    if websocket_logfile is not None:
        logger.setLevel(logging.CRITICAL)


PERF_ROOT_LOGGER_NAME = "perf"


def init_perf_log(perf_logfile: Optional[str] = None, level: Optional[Union[int, str]] = None) -> None:
    logger = _init_log(PERF_ROOT_LOGGER_NAME, logging.Formatter(TS_FORMAT), perf_logfile)
    if level is not None:
        logger.setLevel(level)


def add_log_args(
    parser: argparse.ArgumentParser,
    default_logfile: Optional[str] = None,
    default_data_logfile: Optional[str] = None,
    default_perf_logfile: Optional[str] = None,
) -> None:
    parser.add_argument("--logfile", type=str, help="Log to file", default=default_logfile)
    parser.add_argument("--data-logfile", type=str, help="Log to data file", default=default_data_logfile)
    parser.add_argument("--perf-logfile", type=str, help="Log for perf tracker", default=default_perf_logfile)
    parser.add_argument("--no-timestamps", action="store_true", help="disable timestamps on stdout logs")
    parser.add_argument("--no-colorize", action="store_true", help="disable colorized stdout logs")
    parser.add_argument(
        "--log-level", type=str, choices=["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG", "NOTSET"], default="INFO",
    )


def get_log_args(args: argparse.Namespace) -> Dict[str, Any]:
    return {
        "level": args.log_level,
        "logfile": args.logfile,
        "data_logfile": args.data_logfile,
        "perf_logfile": args.perf_logfile,
        "stdout_timestamps": not args.no_timestamps,
        "colorize": not args.no_colorize,
    }


class JsonLogger:
    """
    Streams data to a log in JSON format
    """

    def __init__(self, name: str):
        assert name is not None and len(name) > 0 and name[-1] != "."
        self._logger = logging.getLogger(name)

    def emit(self, datum: Union[JsonSerializable, Dict[str, Any]]) -> None:
        """
        Emit the given datum
        """
        if isinstance(datum, JsonSerializable):
            datum = datum.to_json()
        self._logger.info(json.dumps(datum))


class DataLogger(JsonLogger):
    """
    Streams to data log
    """

    def __init__(self, name: str):
        assert name is not None and len(name) > 0 and name[-1] != "."
        super().__init__(f"{DATA_ROOT_LOGGER_NAME}.{name}")


class WebSocketLogger(JsonLogger):
    """
    Streams to websocket log
    """

    def __init__(self, name: str):
        assert name is not None and name != ""
        super().__init__(f"{WEBSOCKET_ROOT_LOGGER_NAME}.{name}")


# functional get method for module clients
def get_websocket_logger(name: str) -> WebSocketLogger:
    """
    Get a websocket logger which logs to the websocket log with no further propogation.
    """
    return WebSocketLogger(name)


def get_perf_logger(name: str) -> logging.Logger:
    return logging.getLogger(f"{PERF_ROOT_LOGGER_NAME}.{name}")


class _RollingLogCache:
    """
    Concurrent rolling cache of recent LogRecords. Expires-on-insertion up to max size.

    TODO: if this gets much more complicated, it deserves some unit tests.
    For now, assertions with manual testing was sufficient to get it working.
    """

    _DEFAULT_MAX_SIZE = 1000  # same as JS

    def __init__(self, max_size: int = _DEFAULT_MAX_SIZE):
        assert max_size is not None and isinstance(max_size, int)
        self._max = max_size
        # internally, a list is used so we get indexed access
        # otherwise i would use a queue with max length.
        # I didn't find any out-of-the box lists with max length properties,
        # nor do I see any bounded queues with index access
        self._log_cache: List[LogRecord] = []
        self._num_expired = 0
        self._resize_threshold = 100 + 1.1 * self._max  # robust to small numbers or big numbers
        self._level_counts = {
            logging._levelToName[logging.DEBUG]: 0,
            logging._levelToName[logging.INFO]: 0,
            logging._levelToName[logging.WARNING]: 0,
            logging._levelToName[logging.ERROR]: 0,
            logging._levelToName[logging.CRITICAL]: 0,
        }

        self._lock = threading.RLock()

    @property
    def level_counts(self) -> Dict[str, int]:
        return self._level_counts.copy()

    def append(self, log_record: LogRecord) -> None:
        assert log_record is not None and isinstance(log_record, logging.LogRecord)
        with self._lock:
            self._log_cache.append(log_record)
            self._level_counts[log_record.levelname] = self._level_counts[log_record.levelname] + 1

            new_len = len(self._log_cache)
            if new_len > self._resize_threshold:
                too_big = new_len - self._max
                self._log_cache = self._log_cache[-self._max :]
                self._num_expired += too_big
                LOG.debug(
                    "Expired {} of {} logs from in-memory log cache. New size = {}. Total logs since boot: {}.".format(
                        too_big, new_len, len(self._log_cache), self._num_expired + len(self._log_cache),
                    )
                )

    def get_since(self, index: int) -> Tuple[int, List[LogRecord], Dict[str, int]]:
        """
        Get the available logs since the given index.
        """
        assert index >= 0
        assert index <= 1 + self._num_expired + len(self._log_cache), (
            "Bug! Asked for log index = {} but last known index is {}. "
            "You should only ask for a known index, or 1 higher.".format(
                index, self._num_expired + len(self._log_cache)
            )
        )
        with self._lock:
            index_into_log_cache = index - self._num_expired
            if len(self._log_cache) - index_into_log_cache > self._max:
                # we have held onto more than the max number of logs (so that we don't repeatedly re-size)
                # this is just an optimization of the internal memory model
                # For the user, they would not expect us to have more logs than the maximum requested,
                # so artificially return less
                index_into_log_cache = len(self._log_cache) - self._max

            result = self._log_cache[index_into_log_cache:]

            # assertions are cheap; don't delete unless you write an entire unit test suite
            assert len(result) <= self._max, "Bug! We should only return up to the max number of logs"

            since_boot_index = self._num_expired + index_into_log_cache
            return since_boot_index, result, self.level_counts


# Designed for client usage by robot (e.g. to serve logs over web)
_ROLLING_LOG_CACHE = _RollingLogCache()


class RollingLogCacheHandler(logging.handlers.QueueHandler):
    """
    Override for thread-safety.
    """

    def __init__(self) -> None:
        super().__init__(_ROLLING_LOG_CACHE)  # type: ignore

    # QueueHandler implementation calls putnowait.
    # This method is design to be overridden
    # We can also change prepare() if desired
    def enqueue(self, record: LogRecord) -> None:
        _ROLLING_LOG_CACHE.append(record)


def _get_log_records_since(index: int = 0) -> Tuple[int, List[LogRecord], Dict[str, int]]:
    """
    This method should be used by internally by the module to get the latest log records by index.
    """
    return _ROLLING_LOG_CACHE.get_since(index)


def get_logs(index: int = 0) -> Dict[str, Any]:
    """
    This method should be used by clients to get the latest logs by index, nicely formatted as a dictionary
    """
    first_index, log_records, level_counts = _get_log_records_since(index)
    logs = []
    for log in log_records:
        formatter = NO_TS_FORMAT
        msg = formatter.format() % vars(log)
        response_entry = {
            "time": datetime.fromtimestamp(log.created).isoformat(),
            "level": log.levelname,
            "msg": msg,
            "exc_text": log.exc_text,
        }
        logs.append(response_entry)

    return {
        # Contractually we tell you the first index and then give you a list of logs.
        # That is information complete.
        # Client JS should "compute" (addition) the last index, and call back with last + 1 next time.
        "first": first_index,
        "logs": logs,
        "counts": level_counts,
    }


class RateRecord:
    def __init__(self) -> None:
        self._time = time.monotonic()
        self._count = 0

    def reset(self) -> None:
        self._time = time.monotonic()
        self._count = 0

    def increment(self) -> None:
        self._count += 1

    @property
    def time(self) -> float:
        return self._time

    @property
    def count(self) -> int:
        return self._count


def record_location_key_func(record: logging.LogRecord) -> str:
    return f"{record.pathname}:{record.lineno}"


def record_msg_key_func(record: logging.LogRecord) -> str:
    return record.getMessage()


class RateFilter(logging.Filter):
    def __init__(self, key_func: Callable[[logging.LogRecord], str], delta_s: int = 60, name: str = "") -> None:
        super().__init__(name)
        self._key_func = key_func
        self._delta_s = delta_s
        self._records: Dict[str, RateRecord] = {}

    def filter(self, record: logging.LogRecord) -> bool:
        if not super().filter(record):
            return False
        key = self._key_func(record)
        if key not in self._records:
            self._records[key] = RateRecord()
            return True
        if time.monotonic() < self._records[key].time + self._delta_s:
            self._records[key].increment()
            return False
        record.msg = os.linesep.join([f"{self._records[key].count} more generated", record.msg])
        self._records[key].reset()
        return True

from typing import Callable, Generic, Optional, Type, TypeVar, Union, cast

from lib.common.abstract.config.config import TreeNode


class uint(int):  # Dummy type to differentiate uint from int which doesn't exist in python
    pass


T = TypeVar("T", bound=Union[int, float, bool, str, uint])
R = TypeVar("R")


def type_to_getter(cfg_type: Type[T], node: TreeNode) -> Callable[[], T]:
    if cfg_type is bool:
        return cast(Callable[[], T], node.get_bool_value)
    elif cfg_type is uint:
        return cast(Callable[[], T], node.get_uint_value)
    elif cfg_type is int:
        return cast(Callable[[], T], node.get_int_value)
    elif cfg_type is float:
        return cast(Callable[[], T], node.get_float_value)
    elif cfg_type is str:
        return cast(Callable[[], T], node.get_string_value)
    raise NotImplementedError(f"type {cfg_type} not supported")


class ConfigAccessor(Generic[T]):
    def __init__(
        self,
        cfg_type: Type[T],
        node: Optional[TreeNode],
        default_value: Optional[T] = None,
        initializer: Optional[Callable[[T], T]] = None,
        side_effect: Optional[Callable[[], None]] = None,
    ) -> None:
        assert not (node is None and default_value is None)
        if node is not None:
            assert node is not None
            self._node = node
            self._cfg_func = type_to_getter(cfg_type, self._node)
            self._initializer = initializer
            self._value = self._cfg_func()
            if self._initializer is not None:
                self._value = self._initializer(self._value)
            self._cb_id = self._node.register_callback(self._changed)
        else:
            assert default_value is not None
            self._value = default_value
        self._side_effect = side_effect

    def _changed(self) -> None:
        tmp = self._cfg_func()
        if self._initializer is not None:
            tmp = self._initializer(tmp)
        self._value = tmp
        if self._side_effect is not None:
            self._side_effect()

    @property
    def value(self) -> T:
        return self._value


class TypedConfigAccessor(Generic[T, R]):
    def __init__(
        self, cfg_type: Type[T], node: TreeNode, converter: Callable[[T], R], default_value: Optional[R] = None
    ) -> None:
        self._node = node
        self._cfg_func = type_to_getter(cfg_type, self._node)
        self._converter = converter
        if default_value is not None:
            self._value = default_value
        else:
            self._value = self._converter(self._cfg_func())
        self._cb_id = self._node.register_callback(self._changed)

    def _changed(self) -> None:
        self._value = self._converter(self._cfg_func())

    @property
    def value(self) -> R:
        return self._value

import asyncio
from typing import Any, <PERSON>wai<PERSON>, Dict, Generic, TypeVar

KT = TypeVar("KT")
VT = TypeVar("VT")


class WaitMap(Generic[KT, VT]):
    def __init__(self) -> None:
        self._lock = asyncio.Lock()
        self._events: Dict[KT, asyncio.Event] = {}
        self._responses: Dict[KT, VT] = {}

    async def set_resp(self, key: KT, val: VT) -> None:
        async with self._lock:
            if key not in self._events:
                raise KeyError(f"no listener found for '{key}'")
            self._responses[key] = val
            self._events[key].set()

    async def await_resp(self, key: KT, producer: Awaitable[Any], timeout: float = 0) -> VT:
        e = asyncio.Event()
        async with self._lock:
            self._events[key] = e
        try:
            await producer
            await asyncio.wait_for(e.wait(), timeout=timeout)
        finally:
            async with self._lock:
                if key in self._responses:
                    val = self._responses[key]
                    del self._responses[key]
                del self._events[key]
        return val

import asyncio
from abc import ABC
from typing import TYPE_CHECKING, List

if TYPE_CHECKING:
    TASK_TYPE = asyncio.Task[None]
else:
    TASK_TYPE = asyncio.Task


class TaskMaster(ABC):
    def __init__(self) -> None:
        self._tasks: List[TASK_TYPE] = []
        self._lock = asyncio.Lock()

    async def add_task(self, task: TASK_TYPE) -> None:
        async with self._lock:
            self._tasks.append(task)

    async def stop(self) -> None:
        async with self._lock:
            for task in self._tasks:
                if not task.done():
                    task.cancel()
                try:
                    await task
                except BaseException:
                    pass
            self._tasks = []

import asyncio
from typing import List


class WaitList:
    def __init__(self) -> None:
        self._lock = asyncio.Lock()
        self._events: List[asyncio.Event] = []

    async def wake(self) -> None:
        async with self._lock:
            for event in self._events:
                event.set()
            self._events = []  # clear all pending events in prep for next

    async def await_notify(self) -> None:
        e = asyncio.Event()
        async with self._lock:
            self._events.append(e)
        await e.wait()

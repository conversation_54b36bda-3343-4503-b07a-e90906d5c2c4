import asyncio
from types import TracebackType
from typing import Any, Coroutine, Generator, List, Optional, Type, TypeVar, Union

T = TypeVar("T")


class SimpleTaskGroup:
    def __init__(self) -> None:
        self._tasks: List[asyncio.Task[Any]] = []

    async def __aenter__(self) -> "SimpleTaskGroup":
        return self

    async def __aexit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        if exc_type is None:
            await asyncio.gather(*self._tasks, return_exceptions=True)  # just want to clear all missed tasks
        else:
            for task in self._tasks:
                if not task.done():
                    task.cancel()
                try:
                    await task
                except BaseException:
                    pass

        self._tasks = []

    async def create_task(self, coro: Union[Generator[Any, None, T], Coroutine[Any, Any, T]]) -> "asyncio.Task[T]":
        task: asyncio.Task[T] = asyncio.get_event_loop().create_task(coro)
        self._tasks.append(task)
        return task

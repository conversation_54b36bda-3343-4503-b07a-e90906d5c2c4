import inspect
from typing import Any, Callable


def wrap_async(f: Callable[..., Any]) -> Callable[..., Any]:
    """
    Wrap the given function in an async proxy, if not already a coroutine, else return original function.
    """
    assert f is not None

    async_f = f
    if not inspect.iscoroutinefunction(f):

        async def wrapped_async(*argv: Any, **kwargs: Any) -> Any:
            return f(*argv, **kwargs)

        async_f = wrapped_async

    return async_f

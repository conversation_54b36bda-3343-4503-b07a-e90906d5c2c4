import asyncio
from typing import Any, Coroutine, Generator, Iterable, List, Optional, Set, Tuple, TypeVar, Union

from lib.common.asyncio.cancel_task import cancel_and_wait

T = TypeVar("T")


async def cancellable_await(
    coro: Union[Generator[Any, None, T], Coroutine[Any, Any, T]],
    cancel_event: Union[asyncio.Event, Iterable[asyncio.Event]],
) -> Optional[T]:
    task_list: List[asyncio.Task[Any]] = []
    try:
        coro_task: asyncio.Task[T] = asyncio.get_event_loop().create_task(coro)
        task_list.append(coro_task)
        if not isinstance(cancel_event, Iterable):
            cancel_event = [cancel_event]
        for event in cancel_event:
            task_list.append(asyncio.get_event_loop().create_task(event.wait()))

        all_tasks: Tuple[Set[asyncio.Task[Any]], Set[asyncio.Task[Any]]] = await asyncio.wait(
            task_list, return_when=asyncio.FIRST_COMPLETED
        )
        done = all_tasks[0]
        for pending in all_tasks[1]:
            await cancel_and_wait(pending)
        if coro_task in done:
            return await coro_task
        return None
    except asyncio.CancelledError:
        for task in task_list:
            await cancel_and_wait(task)
        raise

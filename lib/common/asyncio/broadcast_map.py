import asyncio
from typing import Dict, Generic, TypeVar

VT = TypeVar("VT")


class BroadcastMap(Generic[VT]):
    def __init__(self) -> None:
        self._lock = asyncio.Lock()
        self._queues: Dict[int, asyncio.Queue[VT]] = {}
        self._id = 0

    async def broadcast(self, val: VT) -> None:
        async with self._lock:
            for _, queue in self._queues.items():
                queue.put_nowait(val)

    async def register(self) -> int:
        async with self._lock:
            self._id += 1
            self._queues[self._id] = asyncio.Queue()
            return self._id

    async def unregister(self, key: int) -> None:
        async with self._lock:
            if key in self._queues:
                del self._queues[key]

    async def await_msg(self, key: int) -> VT:
        async with self._lock:
            queue = self._queues[key]
        return await queue.get()

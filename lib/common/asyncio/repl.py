import ast
import asyncio
import atexit
import code
import concurrent.futures
import inspect
import os
import readline
import rlcompleter
import sys
import threading
import types
import warnings
from typing import Any, Dict, Optional, cast

history_file = os.path.join(os.path.expanduser("~"), ".pyhistory")


def save_history(history: str = history_file) -> None:
    import readline

    readline.write_history_file(history)


def load_history(history: str = history_file) -> None:
    try:
        readline.read_history_file(history)
    except IOError:
        pass


load_history()
atexit.register(save_history)

vars = globals()


class AsyncIOInteractiveConsole(code.InteractiveConsole):
    def __init__(self, locals: Dict[str, Any], loop: asyncio.AbstractEventLoop):
        super().__init__(locals)
        self.compile.compiler.flags |= ast.PyCF_ALLOW_TOP_LEVEL_AWAIT

        self.loop = loop

    async def _runner(self, coro: Any) -> Any:
        results = await coro
        vars.update(self.locals)
        return results

    def runcode(self, code: types.CodeType) -> Any:
        future: concurrent.futures.Future[Any] = concurrent.futures.Future()

        def callback() -> None:
            global repl_future
            global repl_future_interrupted
            global vars

            repl_future = None
            repl_future_interrupted = False

            func = types.FunctionType(code, cast(Dict[str, Any], self.locals))
            try:
                coro = func()
            except SystemExit:
                raise
            except KeyboardInterrupt as ex:
                repl_future_interrupted = True
                future.set_exception(ex)
                return
            except BaseException as ex:
                future.set_exception(ex)
                return

            if not inspect.iscoroutine(coro):
                future.set_result(coro)
                vars.update(self.locals)
                return

            try:
                repl_future = self.loop.create_task(self._runner(coro))
                asyncio.futures._chain_future(repl_future, future)  # type: ignore
            except BaseException as exc:
                future.set_exception(exc)

        self.loop.call_soon_threadsafe(callback)

        try:
            return future.result()
        except SystemExit:
            raise
        except BaseException:
            if repl_future_interrupted:
                self.write("\nKeyboardInterrupt\n")
            else:
                self.showtraceback()


class REPLThread(threading.Thread):
    def __init__(self, console: code.InteractiveConsole):
        super().__init__()
        self._console = console

    def run(self) -> None:
        try:
            banner = (
                f"asyncio REPL {sys.version} on {sys.platform}\n"
                f'Use "await" directly instead of "asyncio.run()".\n'
                f'Type "help", "copyright", "credits" or "license" '
                f"for more information.\n"
                f'{getattr(sys, "ps1", ">>> ")}import asyncio'
            )
            self._console.interact(banner=banner, exitmsg="exiting asyncio REPL...")
        finally:
            warnings.filterwarnings("ignore", message=r"^coroutine .* was never awaited$", category=RuntimeWarning)

            loop.call_soon_threadsafe(loop.stop)


loop = asyncio.new_event_loop()
asyncio.set_event_loop(loop)

repl_locals = {"asyncio": asyncio}
for key in {"__name__", "__package__", "__loader__", "__spec__", "__builtins__", "__file__"}:
    repl_locals[key] = locals()[key]


repl_future: Optional["asyncio.Task[Any]"] = None
repl_future_interrupted = False
vars.update(repl_locals)
readline.set_completer(rlcompleter.Completer(vars).complete)
readline.parse_and_bind("tab: complete")


def start(extra: Dict[str, Any] = {}) -> None:
    global loop
    global repl_future
    global repl_future_interrupted
    global repl_locals
    if extra:
        vars.update(extra)
    for k, v in extra.items():
        repl_locals[k] = v

    console = AsyncIOInteractiveConsole(repl_locals, loop)
    repl_thread = REPLThread(console)
    repl_thread.daemon = True
    repl_thread.start()
    while True:
        try:
            loop.run_forever()
        except KeyboardInterrupt:
            if repl_future is not None and not repl_future.done():
                repl_future.cancel()
                repl_future_interrupted = True
            continue
        else:
            break


if __name__ == "__main__":
    start()

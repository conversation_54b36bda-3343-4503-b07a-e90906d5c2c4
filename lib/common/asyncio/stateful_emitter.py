from contextlib import contextmanager
from typing import Generator, TypeVar

from lib.common.asyncio.async_emitter import AsyncEmitter

T = TypeVar("T")


class StatefulEmitter(AsyncEmitter[T]):
    def __init__(self, initial: T) -> None:
        super().__init__()
        self._cur = initial

    @contextmanager
    def modify(self) -> Generator[T, None, None]:
        yield self._cur
        self.emit(self._cur)

    @property
    def current(self) -> T:
        return self._cur

    def set(self, val: T) -> bool:
        if val != self._cur:
            self._cur = val
            self.emit(val)
            return True
        return False

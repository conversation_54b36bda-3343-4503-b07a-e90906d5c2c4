import asyncio
from typing import Generic, Optional, TypeVar

T = TypeVar("T")


class DataEvent(Generic[T]):
    """
    Readers will get the latest data, but are not guaranteed to get all data.
    """

    def __init__(self) -> None:
        self._event = asyncio.Event()
        self._data: Optional[T] = None

    def put(self, data: T) -> None:
        self._data = data
        self._event.set()

    async def get(self) -> T:
        await self._event.wait()
        self._event.clear()
        assert self._data is not None
        return self._data

    def get_no_wait(self) -> Optional[T]:
        return self._data

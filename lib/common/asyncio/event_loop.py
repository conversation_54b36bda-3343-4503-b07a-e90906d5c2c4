import asyncio
from asyncio import Abstract<PERSON><PERSON><PERSON><PERSON>
from contextlib import contextmanager
from typing import Generator


def get_event_loop() -> AbstractEventLoop:
    return asyncio.new_event_loop()


@contextmanager
def use_specific_loop(loop: AbstractEventLoop) -> Generator[None, None, None]:
    needs_change = False
    try:
        cur_loop = asyncio.get_event_loop()
        needs_change = cur_loop == loop
    except RuntimeError:
        asyncio.set_event_loop(loop)  # No current loop so set this as default
    if needs_change:
        asyncio.set_event_loop(loop)
    try:
        yield
    finally:
        if needs_change:
            asyncio.set_event_loop(cur_loop)

import asyncio
from asyncio import Future, ensure_future
from logging import getLogger
from typing import TYPE_CHECKING, Any, Awaitable, Callable, Generic, List, Set, TypeVar, cast

from lib.common.bot.stop_handler import bot_stop_handler

if TYPE_CHECKING:
    FUT_TYPE = Future[None]
else:
    FUT_TYPE = Future
LOG = getLogger(__name__)

T = TypeVar("T")


class AsyncEmitter(Generic[T]):
    def __init__(self) -> None:
        self._fut: Set[FUT_TYPE] = set()
        self._callbacks: List[Callable[[T], Awaitable[None]]] = []
        if TYPE_CHECKING:
            self._queue: asyncio.Queue[T] = asyncio.Queue()
        else:
            self._queue = asyncio.Queue()
        fut: FUT_TYPE = ensure_future(cast(Any, self._run()))

        def callback(f: FUT_TYPE) -> None:
            self._fut.remove(f)

        fut.add_done_callback(callback)
        self._fut.add(fut)

    async def _run(self) -> None:
        stop_event = await bot_stop_handler.get_stop_event()
        while not stop_event.is_set():
            next = await self._queue.get()
            for cb in self._callbacks:
                try:
                    await cb(next)
                except Exception:
                    LOG.exception("Unknown exception in emitter callback")

    def emit(self, val: T) -> None:
        self._queue.put_nowait(val)

    def watch(self, cb: Callable[[T], Awaitable[None]]) -> None:
        self._callbacks.append(cb)

import asyncio
from typing import Any, Optional, Set


class InterruptibleSleep:
    """
    Class to allow one or more tasks to sleep (for differing amounts of time) but allow
    another task to interrupt all threads sleeping. Only threads that slept using the same
    instance of this class are woken up, other instances (or plain asyncio.sleep) is not
    affected.
    """

    def __init__(self) -> None:
        self.tasks: Set[asyncio.Task[Any]] = set()

    async def sleep(self, delay: float, result: Optional[Any] = None) -> Any:
        """
        Block the calling task for the given amount of time.
        """
        coro = asyncio.sleep(delay, result=result)
        task = asyncio.ensure_future(coro)
        self.tasks.add(task)

        try:
            return await task
        except asyncio.CancelledError:
            return result
        finally:
            self.tasks.remove(task)

    def _cancel_tasks(self) -> "Set[asyncio.Task[Any]]":
        cancelled = set()
        for task in self.tasks:
            if task.cancel():
                cancelled.add(task)
        return cancelled

    async def cancel_all(self) -> int:
        """
        Cancel all sleeping tasks. Returns number of sleeps interrupted
        """
        cancelled = self._cancel_tasks()
        if len(cancelled):
            await asyncio.wait(self.tasks)
            self.tasks -= cancelled

        return len(cancelled)

import asyncio
from typing import Any, Callable, Coroutine, List

Callback = Callable[[], Coroutine[Any, Any, None]]


class CallbackList:
    def __init__(self) -> None:
        self._lock = asyncio.Lock()
        self._callbacks: List[Callback] = []

    async def add(self, callback: Callback) -> None:
        async with self._lock:
            self._callbacks.append(callback)

    async def call(self) -> None:
        async with self._lock:
            if len(self._callbacks) > 0:
                await asyncio.gather(*[cb() for cb in self._callbacks])

#include <stdio.h>
#include <stdlib.h>

#include "tim_auth.h"
#include "authlib.h"

int verify(CryptoLib_Crt_t *pmanu_c, int revision, int year, int labid, int labtype, int refnum, int code) {
  /*
   * code, testyear, revision, labid, refnum
   */

  printf("Trying: revision %d, year %d, labid %d, labtype %d, refnum %d, code %d\n",
         revision, year, labid, labtype, refnum, code);
  int ret = AuthLib_CheckPeerCCID(pmanu_c,
    code,
    year,
    revision,
    labid,
    refnum);
  return ret;
}

int main(int argc, char *argv[]) {
  char *pmanu;
  int manu_size;
  CryptoLib_Crt_t manu_c;

  tim_auth_init();

  if (get_manufacturer_series_cert(&pmanu, &manu_size) < 0) {
    printf("Failed to get manu\n");
    exit(-1);
  }

  printf("Loaded manu size: %d\n", manu_size);
  if (AuthLib_ParseCertificate(pmanu, manu_size, &manu_c) != 0) {
    printf("Parse manu fail\n");
    exit(-1);
  }
  printf("Parse manu success\n");

  int revision = 0;
  int year = 24;
  int labid = 0;
  int labtype = 0;
  int refnum = 0;
  int carbon_code = 1205;
  int fake_lab = 522;

  int revisions[] = {0, 1};
  int years[] = {19, 24, 0};
  int labids[] = {0, fake_lab, carbon_code};
  int refnums[] = {7936, 0, 1, 2};
  int labtypes[] = {0, 1, 2, 3};

  for (int i = 0; i < sizeof(revisions) / sizeof(int); i++) {
    for (int j = 0; j < sizeof(years) / sizeof(int); j++) {
      for (int k = 0; k < sizeof(labids) / sizeof(int); k++) {
        for (int l = 0; l < sizeof(refnums) / sizeof(int); l++) {
          for (int m = 0; m < sizeof(labtypes) / sizeof(int); m++) {
            for (int n = 0; n < sizeof(labids) / sizeof(int); n++) {
              int ret = verify(&manu_c, revisions[i], years[j], labids[k], labtypes[m], refnums[l], labids[n]);
              printf("check peer ccid results: %d\n", ret);
              if (ret == 0) {
                printf("OK! *************************************************************\n");
              }
            }
          }
        }
      }
    }
  }

  return 0;
}

VERSION=1.2-1
CC=gcc
CPP=g++
# CFLAGS= -c -O2
CFLAGS= -c -g
INCLUDES=-I ../src
# LIBS=-lvt  <== dynamic
LIBS=-l:libvt.a
LINKDIR=-L../src
LFLAGS=$(shell pkg-config --libs opencv4) -lyaml -lpthread

AUTHLIB=itk_authlib.1.5.2
AUTHLIB_SRC=../${AUTHLIB}/src
AUTHLIB_INCLUDES=-I${AUTHLIB_SRC}/AuthLib/include -I${AUTHLIB_SRC}/CryptoLib/include

C_SRC=$(wildcard *.c)
CPP_SRC=$(wildcard *.cpp)
C_OBJ=$(C_SRC:%.c=%.o)
CPP_OBJ=$(CPP_SRC:%.cpp=%.o)
BIN=ccid_check

all: ${BIN}

${BIN}: ${C_OBJ} ${CPP_OBJ} ../src/libvt.a
	${CPP} ${LINKDIR} $^ ${LIBS} -o $@ ${LFLAGS}

${C_OBJ}: ${C_SRC}
	${CC} ${CFLAGS} ${INCLUDES} ${AUTHLIB_INCLUDES} $^

clean:
	rm -f *.o
	rm -f ${BIN}

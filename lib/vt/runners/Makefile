VERSION=1.2-1
CC=gcc
CPP=g++
# CFLAGS= -c -O2
CFLAGS= -c -g
INCLUDES=-I ../src
# LIBS=-lvt  <== dynamic
LIBS=
LINKDIR=-L../build
LFLAGS=$(shell pkg-config --libs opencv4) -lyaml -lpthread

C_SRC=$(wildcard *.c)
CPP_SRC=$(wildcard *.cpp)
C_OBJ=$(C_SRC:%.c=%.o)
CPP_OBJ=$(CPP_SRC:%.cpp=%.o)

OS := $(shell uname)
ifeq ($(OS),Darwin)
	HOMEBREW_PREFIX := $(shell brew --prefix)
	INCLUDES += -I$(HOMEBREW_PREFIX)/include
	LINKDIR += -L$(HOMEBREW_PREFIX)/lib
endif

BIN=crunner

all: ${BIN}

${BIN}: ${C_OBJ} ${CPP_OBJ} ../build/libvt.a
	${CPP} ${LINKDIR} $^ ${LIBS} -o $@ ${LFLAGS}

${C_OBJ}: ${C_SRC}
	${CC} ${CFLAGS} ${INCLUDES} $^

${CPP_OBJ}: ${CPP_SRC}
	${CPP} ${CFLAGS} ${INCLUDES} $^

clean:
	rm -f *.o
	rm -f ${BIN}

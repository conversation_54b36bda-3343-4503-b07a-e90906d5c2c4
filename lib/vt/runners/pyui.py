#!/usr/bin/env python

import asyncio
import re

import python.vt as vt


class RowModule:
    def __init__(self, name: str):
        self.name = name
        self.off()

    def off(self) -> None:
        self.state = "DISABLED"
        self.directions = "CLICK TO ENABLE"
        self.font = "buttonred"

    def switch_state(self) -> None:
        if self.state == "DISABLED":
            self.state = "ENABLED"
            self.directions = "CLICK TO DISABLE"
            self.font = "buttonblack"
        else:
            self.off()


class Laser:
    def __init__(self) -> None:
        self.off()

    def off(self) -> None:
        self.state = "DISABLED"
        self.directions = "CLICK TO ENABLE"
        self.bgcolor = "d1d1d1"

    def switch_state(self) -> None:
        if self.state == "DISABLED":
            self.state = "ENABLED"
            self.directions = "CLICK TO DISABLE"
            self.bgcolor = "be2c1c"
        else:
            self.off()


rows = [RowModule("row1"), RowModule("row2"), RowModule("row3")]
laser = Laser()


def button_state_str(code: int) -> str:
    for e in vt.ButtonState.__members__.values():
        if e.value == code:
            return str(e.name)

    return "UNKNOWN"


async def write_laser(name: str, laser: Laser) -> None:
    await vt.set_string_value(name + "/status", laser.state)
    await vt.set_string_value(name + "/directions", laser.directions)
    await vt.set_button_bgcolor(name, laser.bgcolor)


async def write_row(name: str, rm: RowModule) -> None:
    await vt.replace_string(name + "/status", rm.state, rm.font)
    await vt.set_string_value(name + "/directions", rm.directions)


async def sync_state() -> None:
    global rows
    await write_laser("main/laser_state", laser)
    for rm in rows:
        await write_row(f"main/{rm.name}_enable", rm)


async def button_cb(name: str, code: int) -> int:
    if code != vt.ButtonState.PRESSED.value:
        return 0

    if name == "maintenance":
        await vt.set_active_mask("maintenance")
    elif name == "main":
        await vt.set_active_mask("main")
    elif name == "alarms":
        await vt.set_active_mask("alarms")
    elif name == "checklist":
        await vt.set_active_mask("checklist")
    elif name == "screenswitch":
        await vt.switch_screen()
        await sync_state()
    elif name == "main/laser_state":
        laser.switch_state()
        await write_laser(name, laser)
    elif name == "main/estop":
        laser.off()
        for rm in rows:
            rm.off()
        await sync_state()
    else:
        m = re.match("main/row([0-9])_enable", name)
        if m is not None and m.group(0):
            row = int(m.group(1))
            rm = rows[row - 1]
            rm.switch_state()
            await write_row(name, rm)
    return 0


async def inputlist_cb(name: str, val: str) -> int:
    if name == "main/crop_select":
        print(f"CROP: {val}")
    return 0


async def speed_cb(speed: float) -> int:
    await vt.set_string_value("main/current_speed", "%.2f MPH" % speed)
    await vt.set_string_value("main/target_speed", "%.2f MPH" % (1.1 * speed))
    return 0


async def hitch_cb(position: int) -> int:
    await vt.set_string_value("main/hitch_position", "%d %%" % position)
    return 0


async def main() -> None:
    vt.set_event_loop(asyncio.get_running_loop())
    vt.set_inputlist_dispatch(inputlist_cb)
    vt.set_button_dispatch(button_cb)
    vt.set_speed_dispatch(speed_cb)
    vt.set_hitch_dispatch(hitch_cb)
    # vt.set_preferred_vt(38) // example usage
    await vt.start_isobus(0, "10.10.1.42", 4488, vt.OPTIONS.DO_VT, vt.LOG_LEVEL.INFO, "-")
    while True:
        await asyncio.sleep(999999)


if __name__ == "__main__":
    asyncio.run(main())

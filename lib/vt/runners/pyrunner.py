#!/usr/bin/env python

import asyncio
import curses
import sys
from typing import Any, Callable, Coroutine, List

import python.vt as vt

last_speed_mmps: int = 0


async def tim_connect() -> None:
    connecting: bool = False
    authenticating: bool = False

    while True:
        status: int = vt.get_tim_status()
        if status == vt.TIM_OK:
            if not connecting:
                print("Connecting ...")
                connecting = True
            if not authenticating:
                print("Authenticating ...")
                authenticating = True
            break

        if status == vt.TIM_CONNECTING:
            print("Connecting ...")
            connecting = True

        if status == vt.TIM_AUTHENTICATING:
            if not connecting:
                print("Connecting ...")
                connecting = True
            if not authenticating:
                print("Authenticating ...")
                authenticating = True

        await asyncio.sleep(1)


async def do_window(
    val: float, speed_f: Callable[[int], Coroutine[Any, Any, None]], stop_f: Callable[[], Coroutine[Any, Any, None]],
) -> None:
    async def curses_main(stdscr: "curses.window") -> None:
        global last_speed_mmps
        # Initialize curses
        curses.curs_set(0)
        curses.raw()  # Enable raw mode
        stdscr.nodelay(True)  # Non-blocking mode
        stdscr.keypad(True)  # Enable keypad mode for arrow keys
        stdscr.timeout(10)  # Short timeout

        try:
            max_width: int = curses.COLS - 4  # Leave some padding
            current_val: float = 0.0
            step: float = 0.01

            # Conversion factor from mph to mm/sec
            MPH_TO_MM_PER_SEC: float = 447.04
            # Conversion factor from mph to kph
            MPH_TO_KPH: float = 1.60934

            current_val = int(last_speed_mmps / MPH_TO_MM_PER_SEC)

            while True:
                stdscr.clear()
                stdscr.addstr(0, 2, "Press Left/Right to adjust, Q to quit")

                # Calculate bar length
                bar_length: int = max_width
                filled_length: int = int((current_val / val) * bar_length) if val > 0 else 0

                # Draw bar
                bar_str: str = "=" * filled_length + "-" * (bar_length - filled_length)
                stdscr.addstr(2, 2, bar_str)

                # Display numeric values in both mph and kph
                kph_val: float = current_val * MPH_TO_KPH
                mmps_val: int = int(current_val * MPH_TO_MM_PER_SEC)
                stdscr.addstr(4, 2, f"Current Value: {mmps_val:.2f} mmps / {current_val:.2f} mph / {kph_val:.2f} kph")

                # Display play status if playing
                if vt.tim_is_playing():
                    stdscr.addstr(5, 2, "[Play]")

                try:
                    # Get user input
                    key: int = stdscr.getch()
                    if key == ord("\x1b"):  # ESC
                        next_char: int = stdscr.getch()
                        if next_char == ord("["):
                            key = stdscr.getch()
                            if key == ord("C"):  # Right arrow
                                current_val = min(val, current_val + step)
                            elif key == ord("D"):  # Left arrow
                                current_val = max(0, current_val - step)
                    elif key == curses.KEY_RIGHT:
                        current_val = min(val, current_val + step)
                    elif key == curses.KEY_LEFT:
                        current_val = max(0, current_val - step)
                    elif key in (ord("q"), ord("Q")):
                        break

                except Exception:
                    pass  # Silently handle any errors

                last_speed_mmps = int(current_val * MPH_TO_MM_PER_SEC)
                await speed_f(int(current_val * MPH_TO_MM_PER_SEC))
                stdscr.refresh()
                await asyncio.sleep(0.01)

        finally:
            # Cleanup curses
            curses.nocbreak()
            stdscr.keypad(False)
            curses.echo()
            curses.endwin()

    await curses.wrapper(curses_main)


async def main() -> None:
    tecu: bool = False
    global last_speed_mmps
    if "--tecu" in sys.argv:
        tecu = True

    options: int = vt.OPTIONS.TRACE_PACKETS
    if not tecu:
        options |= vt.OPTIONS.DO_TIM
    else:
        options |= vt.OPTIONS.DO_TECU

    vt.initialize_vt("build")
    vt.set_event_loop(asyncio.get_running_loop())
    vt.set_isobus_name(
        vt.SELF_ASSIGNED_ADDR,
        vt.AGRICULTURAL_INDUSTRY_GROUP,
        0,  # DEVICE_CLASS_INSTANCE
        0,  # DEVICE_CLASS
        0,  # FUNCTION
        0,  # FUNCTION_INSTANCE
        0,  # ECU_INSTANCE
        vt.CARBON_MANUFACTURER_CODE,
        vt.CARBON_IDENTIFICATION_NUMBER,
    )
    val: int = await vt.start_isobus(
        0, "10.10.15.2", 4488, options, vt.LOG_LEVEL.INFO, "build/certificates/timbox_v2", "output.log",
    )
    if val != 0:
        print("Failed with val: %d" % val)
    else:
        prompt: str = "Enter command (speed, stop, quit)> "

        if not tecu:
            await tim_connect()
            speed_f: Callable[[int], Coroutine[Any, Any, None]] = vt.tim_set_vehicle_speed
            stop_f: Callable[[], Coroutine[Any, Any, None]] = vt.tim_stop_set_vehicle_speed
        else:
            speed_f = vt.tecu_set_vehicle_speed
            stop_f = vt.tecu_stop_set_vehicle_speed

        while True:
            command: str = input(prompt)
            if command.startswith("speed"):
                parts: List[str] = command.split()
                val = int(parts[1])
                print("vehicle speed to %d" % val)
                last_speed_mmps = val
                await speed_f(val)
            elif command.startswith("stop"):
                print("stop vehicle speed set")
                await stop_f()
            elif command.startswith("hitch"):
                parts = command.split()
                fval = float(parts[1])
                print("hitch set to %f" % fval)
                await vt.tim_set_rear_hitch(fval)
            elif command.startswith("window"):
                await do_window(2, speed_f, stop_f)
            elif command.startswith("quit"):
                return
            else:
                print("I don't know what that means.")


if __name__ == "__main__":
    asyncio.run(main())

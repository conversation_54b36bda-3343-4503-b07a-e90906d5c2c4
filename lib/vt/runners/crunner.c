#include <stdio.h>
#include <unistd.h>
#include <string.h>

#include <vt.h>

int main(int argc, char *argv[]) {
  int trace = 0;
  int tecu = 0;

  for (int i = 0; i < argc; i++) {
    if (!strcmp(argv[i], "--trace")) {
      trace = 1;
    } else if (!strcmp(argv[i], "--tecu")) {
      tecu = 1;
    }
  }

  isobus_options opts = {0, "10.10.15.2", 4488, TRACE_PACKETS, "", "../build/certificates/timbox_v2",
                         DEBUG_PROTO, "-", 0.0};

  if (trace) {
    opts.options = DO_NOTHING | TRACE_PACKETS;
  } else {
    if (tecu) {
      opts.options |= DO_TECU;
    } else {
      opts.options |= DO_TIM;
    }
  }

  set_isobus_name(SELF_ASSIGNED_ADDR, 
                  AGRICULTURAL_INDUSTRY_GROUP,
                  0, // DEVICE_CLASS_INSTANCE
                  0, // DEVICE_CLASS
                  0, // FUNCTION
                  0, // FUNCTION_INSTANCE
                  0, // ECU_INSTANCE
                  CARBON_MANUFACTURER_CODE,
                  CARBON_IDENTIFICATION_NUMBER);

  int res = start_isobus(&opts);

  if (res) {
    if (!tecu && !trace) {
      int status = TIM_AUTH_FAIL;
      while(status != TIM_OK) {
        usleep(1000 * 1000);
        status = get_tim_status();
      }
    }
    while (1) {
      if (!tecu && !trace) {
        printf("--> Setting TIM speed\n");
        tim_set_vehicle_speed(10);
      } else if (tecu) {
        printf("--> Setting TECU speed\n");
        tecu_set_vehicle_speed(10);
      }
      usleep(1000 * 1000);
    }
  } else {
    printf("isobus start fail with %d\n", res);
  }
  return 0;
}

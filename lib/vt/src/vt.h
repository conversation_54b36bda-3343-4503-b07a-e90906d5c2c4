#ifndef _VT_H
#define _VT_H

#include <stdint.h>

enum log_level { OFF, ERROR, WARN, INFO, DEBUG, DEBUG_PROTO_ESCAPES, DEBUG_PROTO, DEBUG_PROTO_MINE };

#define SELF_ASSIGNED_ADDR 1
#define AGRICULTURAL_INDUSTRY_GROUP 2
#define CARBON_MANUFACTURER_CODE 1205
#define CARBON_IDENTIFICATION_NUMBER 348

/* 11783-5 has the name definition and layout */
void set_isobus_name(int self_assigned_address, int industry_group, int device_class_instance, int device_class,
                     int function, int function_instance, int ecu_instance, int manufacturer_code,
                     int identity_number);

#define DO_NOTHING 0
#define DO_VT 1
#define DO_TIM 2
#define TRACE_PACKETS 4
#define DO_TECU 8

typedef struct isobus_options {
  /* Can interface */
  int can_id;
  const char *ip;
  int port;

  /* options */
  uint32_t options;

  /* ui */
  const char *layout_dir;

  /* TIM */
  const char *certificate_dir;

  /* logging */
  enum log_level level;
  const char *log_filename;

  /* can level */
  float status_msg_hz;
} isobus_options;

int start_isobus(struct isobus_options *opts);

/* High Level Language Calls */
void set_active_mask(const char *name);
void vt_set_string_value(const char *name, const char *value);
void set_string_value(const char *name, const char *value);
void set_string_font(const char *name, const char *fontname);
void set_button_bgcolor(const char *name, const char *color);
void set_meter_value(const char *name, int value);
void set_preferred_vt(int add);
void switch_screen();

void append_table_row(const char *name, const char *cols[], int len);
void set_table_cell(const char *name, int row, int col, const char *value);

typedef int (*f_button)(const char *name, int code);
typedef int (*f_inputlist)(const char *name, const char *value);
typedef int (*f_speed)(float speed, uint64_t ts_us);
typedef int (*f_hitch)(int position, uint64_t ts_us);
typedef int (*f_wakeup)();

void set_button_dispatch(f_button f);
void set_inputlist_dispatch(f_inputlist f);
void set_speed_dispatch(f_speed f);
void set_hitch_dispatch(f_hitch f);
void set_wakeup_dispatch(f_wakeup);

/* TIM Statuses */
#define TIM_OK 0
#define TIM_AUTH_FAIL -1

#define TIM_NONE 1
#define TIM_CONNECTING 2
#define TIM_AUTHENTICATING 3

int get_tim_status();
int tim_is_playing();

/* TECU Statuses */
#define TECU_OK 0

/* Language Tractor Control Functions */

void threaded_tim_set_vehicle_speed(int mm_per_sec);
void threaded_tim_stop_set_vehicle_speed();
void threaded_tecu_set_vehicle_speed(int mm_per_sec);
void threaded_tecu_stop_set_vehicle_speed();
void threaded_tim_set_rear_hitch(float percentage);
void threaded_tim_stop_set_rear_hitch();

/* Straight Request Returns */
int tim_set_vehicle_speed(int mm_per_sec);
int tim_stop_set_vehicle_speed();
int tecu_set_vehicle_speed(int mm_per_sec);
int tecu_stop_set_vehicle_speed();
int tim_set_rear_hitch(float percentage);
int tim_stop_set_rear_hitch();

#endif // _VT_H

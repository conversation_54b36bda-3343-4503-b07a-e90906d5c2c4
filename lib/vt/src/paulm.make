VERSION=1.2-1
CC=gcc
CPP=g++
CFLAGS= -c -g $(shell pkg-config --cflags opencv4)
LFLAGS=$(shell pkg-config --libs opencv4) -lyaml -lpthread
INCLUDES=

LINUX_CAN_H := /usr/include/linux/can.h
LINUX_CAN_EXISTS := $(wildcard $(LINUX_CAN_H))

ifneq ($(LINUX_CAN_EXISTS),)
	CFLAGS += -DHAS_LINUX_CAN
endif

C_SRC=$(wildcard *.c)
CPP_SRC=$(wildcard *.cpp)
C_OBJ=$(C_SRC:%.c=%.o)
CPP_OBJ=$(CPP_SRC:%.cpp=%.o)
LIB=libvt.so
ARCHIVE=libvt.a

AUTHLIB_SRC=../${AUTHLIB}/src
AUTHLIB_INCLUDES=-I${AUTHLIB_SRC}/AuthLib/include -I${AUTHLIB_SRC}/CryptoLib/include

AUTHLIB=itk_authlib.1.5.2
AUTHLIB_PATH=../${AUTHLIB}/src/libauthlib.a

LINKDIR=-L../${AUTHLIB}/src
LIBS=-lauthlib

OS := $(shell uname)
ifeq ($(OS),Darwin)
	HOMEBREW_PREFIX := $(shell brew --prefix)
	INCLUDES += -I$(HOMEBREW_PREFIX)/include
	LINKDIR += -L$(HOMEBREW_PREFIX)/lib
endif

all: ${LIB} ${ARCHIVE}

${LIB}: ${C_OBJ} ${CPP_OBJ}
	${CPP} -shared -o $@ $^ ${LINKDIR} ${LIBS} ${LFLAGS}

${ARCHIVE}: ${C_OBJ} ${CPP_OBJ} ../${AUTHLIB}/src/libauthlib.a
	ar x ${AUTHLIB_PATH}
	ar rcs $@ *.o

${C_OBJ}: ${C_SRC}
	${CC} -fPIC ${CFLAGS} ${AUTHLIB_INCLUDES} ${INCLUDES} $^

${CPP_OBJ}: ${CPP_SRC}
	${CPP} -std=c++11 -fPIC ${CFLAGS} ${INCLUDES} $^

deb: libvt.so vt.h
	mkdir -p libvt_${VERSION}/DEBIAN
	cat control | sed "s/%VERSION%/${VERSION}/" > libvt_${VERSION}/DEBIAN/control
	mkdir -p libvt_${VERSION}/usr/local/include/libvt
	mkdir -p libvt_${VERSION}/usr/local/lib
	cp -v vt.h libvt_${VERSION}/usr/local/include/libvt/.
	cp -v libvt.so libvt_${VERSION}/usr/local/lib/.
	dpkg-deb --build libvt_${VERSION}

clean:
	rm -f *.o
	rm -f ${ARCHIVE}
	rm -f ${LIB}

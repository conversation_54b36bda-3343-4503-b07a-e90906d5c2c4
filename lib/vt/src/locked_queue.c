#include "locked_queue.h"

void lq_init(struct locked_queue *lq, int size) {
  pthread_mutex_init(&lq->mutex, NULL);
  lq->size = size;
  lq->head = 0;
  lq->count = 0;
  lq->elements = (void **)malloc(sizeof(void *) * size);
}

void lq_add(struct locked_queue *lq, void *element) {
  if (lq->count == lq->size) {
    clerr("locked queue overrun!\n");
    return;
  }
  pthread_mutex_lock(&lq->mutex);
  lq->elements[lq->head] = element;
  lq->head = (lq->head + 1) % lq->size;
  lq->count++;
  pthread_mutex_unlock(&lq->mutex);
}

void *lq_remove(struct locked_queue *lq) {
  pthread_mutex_lock(&lq->mutex);
  if (lq->count == 0) {
    pthread_mutex_unlock(&lq->mutex);
    return NULL;
  }
  void *ret = lq->elements[(lq->head + lq->size - lq->count) % lq->size];
  lq->count--;
  pthread_mutex_unlock(&lq->mutex);
  return ret;
}

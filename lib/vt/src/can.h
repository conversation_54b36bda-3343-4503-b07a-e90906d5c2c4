#include "logging.h"
#include "transport_buffer.h"
#include "network.h"
#include "schedule.h"

#define WORKING_SET_MASTER_PF 254
#define WORKING_SET_MASTER_PS 13

/* PGNs */
#define WORKING_SET_MASTER_PGN ((WORKING_SET_MASTER_PF << 8) | WORKING_SET_MASTER_PS)
#define VT2ECU_PGN 0xe600
#define ADDRESS_CLAIMED_PGN 0xee00              // 11783-5
#define ISOBUS_ACK_PGN 0x00e800                 // 11783-3
#define REQUEST_PGN 0xea00                      // 11783-3

/* Request PGNs - these may be requested with the REQUEST_PGN */

#define ISOBUS_COMPLIANCE_CERTIFICATION_PGN 0xfd42  // 11783-7

#define ISOBUS_CERTIFICATION_LAB_NON                0 // SUT EDITOR
#define ISOBUS_CERTIFICATION_LAB_AEF                1
#define ISOBUS_CERTIFICATION_LAB_SELF               2
#define ISOBUS_CERTIFICATION_LAB_NOT                3

#define DLG_TESTSERVICE_GMBH                        251 // AEF Database CCID Generation Page
#define ISOBUS_TEST_CENTER                          507
#define FONDAZIONE_REI                              253
#define NEBRASKA_TRACTOR_TEST_LABORATORY            522
#define KEREVAL                                     603


#define TRANSPORT_PROTOCOL_CM 236
#define TRANSPORT_PROTOCOL_PGN (TRANSPORT_PROTOCOL_CM << 8)

#define EXTENDED_TRANSPORT_PROTOCOL_CM 200
#define EXTENDED_TRANSPORT_PROTOCOL_PGN (EXTENDED_TRANSPORT_PROTOCOL_CM << 8)

/* TIM PGNS */
#define TIM_SERVER_2_TIM_CLIENT 8960
#define TIM_CLIENT_2_TIM_SERVER 9216

// server to client
#define TIM_AUTH12_PGN 0x7000
// client to server
#define TIM_AUTH21_PGN 0x6f00

/* tractor pgns from 11783-7 */
#define WHEEL_BASED_SPEED_AND_DISTANCE_PGN 0x00FE48
#define GROUND_BASED_SPEED_AND_DISTANCE_PGN 0x00FE49
#define PRIMARY_HITCH_STATUS_PGN 0x00FE45
#define VEHICLE_POSITION_PGN 0x00FEF3
#define VEHICLE_SPEED_AND_DIRECTION_PGN 0x00FEE8

/* Transport Control */
#define CM_RTS 16
#define CM_CTS 17
#define CM_EOM_ACK 19
#define ETP_CM_RTS 20
#define ETP_CM_CTS 21
#define ETP_CM_DPO 22
#define ETP_CM_EOM_ACK 23
#define CM_ABORT 255

/* Other numbers */

#define ECU2VT_PDU 231
#define VT2ECU_PDU 230
#define TPDT_PF 235
#define TRANSPORT_PROTOCOL_DATA_TRANSPORT_PGN (TPDT_PF << 8)
#define ETPDT_PF 199

/* VT Functions */
#define GET_MEMORY_FUNCTION 192
#define GET_HARDWARE_FUNCTION 199
#define VT_STATUS_FUNCTION 254
#define OBJECT_POOL_TRANSFER_FUNCTION 17
#define CHANGE_STRING_VALUE_COMMAND 179
#define OBJECT_POOL_DONE_FUNCTION 18
#define DELETE_OBJECT_POOL_FUNCTION 178
#define WORKING_SET_MAINTENANCE_FUNCTION 255
#define SOFTKEY_ACTIVATION_FUNCTION 0
#define BUTTON_ACTIVATION_FUNCTION 1
#define POINTING_FUNCTION 2
#define CHANGE_NUMERIC_VALUE_FUNCTION 5
#define CHANGE_ACTIVE_MASK_FUNCTION 173
#define CHANGE_ATTRIBUTE_FUNCTION 175
#define INPUT_SELECT_OBJECT_FUNCTION 3
#define CHANGE_CHILD_LOCATION_FUNCTION 165
#define CHANGE_OBJECT_SIZE_FUNCTION 166

#define J1939_MEMORY_ACCESS_REQUEST_PF 217

/* ISO 11783 CFs that do not have an assigned preferred address or cannot claim their preferred address
 * shall claim an address in the range of 128 to 247 */
#define MIN_ADDR 128
#define MAX_ADDR 247
#define GLOBAL_ADDRESS 255

#define NULL_ADDRESS 254

#define CAN_BUF_SIZE 2048 // big enough for can gateway frames

struct object_pool;

typedef struct isopacket {
  int sof;
  int priority;
  int pf;
  int ps;
  int sa;
  int pgn; // Do not set this, it is only computed
  int edp;
  int dp;
  unsigned char data[8];
  int data_len;
} isopacket;

isopacket *make_isopacket();

typedef void (*fcanpacket)(sock_pair *ss, isopacket *, unsigned char *data, int data_len, struct timeval *now);

typedef void (*fpacket_process)(unsigned char *buf, int len, isopacket *pac, int *pconsumed);
typedef int (*fencode_packet)(unsigned char *buf, int len, isopacket *pac);

struct can_opts {
  fpacket_process fpacket;
  fencode_packet fencode;
  int trace_packets;
};

/* CAN scheduling */
void can_loop(sock_pair *ss, wake_info *wakes, int num_wakes, struct can_opts *opts);
void register_can_callback(int pgn, fcanpacket, int addr_check);
void unregister_can_callback(int pgn, fcanpacket, int addr_check);
void register_can_addr(int addr, fcanpacket);

/* Packet Inspection */
void display_char_message(enum log_level level, const char *pdata);
int dest_addr(isopacket *p);

/* Print handlers */
void print_hexdata(enum log_level level, unsigned char *p, int len);
void print_binary(enum log_level level, unsigned char b);
void print_binary_chars(enum log_level level, unsigned char *d, int count);
void print_vt_msg(enum log_level level, unsigned char sa, unsigned char *data);
void print_request_address_claimed(enum log_level level, unsigned char sa, unsigned char ps, unsigned char *data);
void print_isobus_compliance_message(enum log_level level, unsigned char sa, unsigned char ps, unsigned char *data);

void print_address_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_get_memory_response_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_get_hardware_response_message(enum log_level level, unsigned char s, unsigned char *pdata);
void print_ack_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_change_string_value_response_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_change_attribute_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_end_of_msg_ack_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_delete_object_pool_response(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_end_of_object_pool_response(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_etp_conn_abort(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_tp_conn_abort(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_button_activation_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_pointing_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_softkey_activation_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_change_numeric_value_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_change_string_value_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_change_attribute_response(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_wheel_speed_distance_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_ground_speed_distance_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_hitch_status_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_change_child_location_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_change_object_size_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_vehicle_position_message(enum log_level level, unsigned char sa, unsigned char *pdata);
void print_vehicle_speed_and_direction_message(enum log_level level, unsigned char sa, unsigned char *pdata);

void print_packet(enum log_level level, isopacket *p, unsigned char *data, int data_len);

// VT2ECU
int object_pool_error_codes(unsigned char *pdata);

/* Packet creation */

// Generic messages
void request_address_claimed(isopacket *p, int addr);
void claim_address_packet(isopacket *p, int addr);
void working_set_master_packet(isopacket *p, int addr, int num_members);

// ISOBUS messages
void isobus_certification_packet_v3(isopacket *pac, int addr);
int get_request_pgn(unsigned char *data);

// ECU 2 VT messages
void get_memory_packet(isopacket *p, int addr, int vt_addr, int memory);
void get_hardware_packet(isopacket *p, int addr, int vt_addr);
int can_support_memory(unsigned char *pdata);
void working_set_maintenance_packet(isopacket *p, int addr, int vt_addr, int initializing);
void object_pool_transfer_packet(isopacket *p, int addr, int vt_addr, unsigned char *bytes, int len);
void end_of_object_pool_packet(isopacket *p, int addr, int vt_addr);
void delete_object_pool_packet(isopacket *p, int addr, int vt_addr);

/* Transport */
int send_packet(int s, isopacket *pac);
int ack_is_nack(unsigned char *pdata);
void request_to_send_packet(isopacket *p, int addr, int vt_addr, int pgn, int bytes);
void clear_to_send_packet(isopacket *p, int addr, int vt_addr, int packets, int pgn);
void eom_ack_packet(isopacket *p, int addr, int vt_addr, int bytes, int packets, int pgn);
void request_to_send_etp_packet(isopacket *p, int addr, int vt_addr, int pgn, int bytes);
void etp_data_packet_offset_packet(isopacket *p, int addr, int vt_addr, int pgn, int offset, int bytes);
void print_transport_protocol_cm_packet(enum log_level level, unsigned char sa, unsigned char *pdata);
int cts(unsigned char *pdata);
int etp_cts(unsigned char *pdata);
int cts_num(unsigned char *pdata);
int etp_cts_num(unsigned char *pdata);
int cts_start_packet(unsigned char *pdata);
int etp_cts_start_packet(unsigned char *pdata);
int end_of_msg_ack(unsigned char *pdata);
int etp_end_of_msg_ack(unsigned char *pdata);
int etp_conn_abort(unsigned char *pdata);
int tp_conn_abort(unsigned char *pdata);
int data_transport_packet(isopacket *p, int addr, int vt_addr, struct transport_buffer *opb);
int e_data_transport_packet(isopacket *p, int addr, int vt_addr, struct transport_buffer *opb);

/* VT Info */
int vt_function(unsigned char *data);
void hardware_datamask_resolution(unsigned char *pdata, int *width, int *height);
void hardware_vt_version(unsigned char *pdata, int *version);

/* Button Info */
int button_activation_id(unsigned char *pdata);
int button_activation_code(unsigned char *pdata);

/* Input List Info */
int input_list_id(unsigned char *pdata);
int input_list_index(unsigned char *pdata);

/* Commands */
int set_active_mask_command(isopacket *p, int addr, int vt_addr, int working_set_object_id, int active_mask_id);
int set_string_value_command(isopacket *p, int add, int vt_addr, int string_id, const char *value);
void vt_set_string_value_command(isopacket *p, int add, int vt_addr, int string_id, const char *value);
int set_string_font_command(isopacket *p, int addr, int vt_addr, int string_id, int font_id); // does not work
int set_button_bgcolor_command(isopacket *p, int addr, int vt_addr, int button_id, int color_idx);
int change_numeric_value_command(isopacket *p, int addr, int vt_addr, int object_id, int value, int bytes);
int set_object_position_relative_command(isopacket *p, int addr, int vt_addr, int parent_id, int object_id, int xoff,
                                         int yoff);
int set_object_size_command(isopacket *p, int addr, int vt_addr, int object_id, int width, int height);

/* Tractor Messages */
float get_speed_mph(unsigned char *pdata);
int get_hitch_position(unsigned char *pdata);

/* Tractor CAN Messages - used for testing */
int hitch_position_packet(isopacket *p, int addr, int src_addr, int position);

/* only call me from transport.c - this is artificial packet pump from multi-packet transport protocol */
void handle_isopacket(sock_pair *ss, struct timeval *now, isopacket *pac, unsigned char *data, int data_len);

/* Request Messages */
int request_pgn(unsigned char *data);

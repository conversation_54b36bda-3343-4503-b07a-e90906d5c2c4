unsigned char indexed_color(unsigned char r, unsigned char g, unsigned char b);

/* Open this image and convert it to isobus indexed color format. If width and height are not zero (the value
 * the pointer is pointing to) then convert to the requested width, height. Otherwise width / height are returned
 * as discovered from the image file. */
unsigned char *open_image(const char name[], int *width, int *height);

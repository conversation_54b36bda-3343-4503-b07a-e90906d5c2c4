#include <functional>
#include <list>
#include <memory>
#include <string>
#include <unordered_map>

#include <string.h>

#include "ui_table.h"
#include "yaml_node.h"

extern "C" {
#include "dispatch.h"
#include "imageconvert.h"
#include "isostate.h"
#include "logging.h"
#include "object_pool.h"
}

using namespace std;

#define NPLUS string("/")

struct ISOString {
  ISOString(int id, int var_id, string val, int font_id, int justification)
      : id(id), var_id(var_id), val(val), font_id(font_id), justification(justification) {}
  int id;
  int var_id;
  string val;
  int font_id;
  int justification;
};

struct ISOFont {
  ISOFont(int id, string name, int color, int size, int style);
  int id;
  string name;
  int color;  // indexed color
  int size;   // see isobus part 6
  int style;  // see isobus part 6
  int width;  // calculated from size
  int height; // calculated from size
};

ISOFont::ISOFont(int id, string name, int color, int size, int style)
    : id(id), name(name), color(color), size(size), style(style) {
  switch (size) {
  case 0:
    width = 6;
    height = 8;
    break;
  case 1:
    width = 8;
    height = 8;
    break;
  case 2:
    width = 8;
    height = 12;
    break;
  case 3:
    width = 12;
    height = 16;
    break;
  case 4:
    width = 16;
    height = 16;
    break;
  case 5:
    width = 16;
    height = 24;
    break;
  case 6:
    width = 24;
    height = 32;
    break;
  case 7:
    width = 32;
    height = 32;
    break;
  case 8:
    width = 32;
    height = 48;
    break;
  case 9:
    width = 48;
    height = 64;
    break;
  case 10:
    width = 64;
    height = 64;
    break;
  case 11:
    width = 64;
    height = 96;
    break;
  case 12:
    width = 86;
    height = 128;
    break;
  case 13:
    width = 128;
    height = 128;
    break;
  case 14:
    width = 128;
    height = 192;
    break;
  default:
    width = 0;
    height = 0;
    break;
  }
}

struct ISOImage {
  ISOImage(int id, string val, int resize_width, int resize_height)
      : id(id), val(val), resize_width(resize_width), resize_height(resize_height) {}
  int id;
  string val;
  int resize_width;
  int resize_height;
};

struct ISOLineAttributes {
  ISOLineAttributes(int id, int color, int thickness, int pattern)
      : id(id), color(color), thickness(thickness), pattern(pattern) {}
  int id;
  int color;
  int thickness;
  int pattern;
};

struct ISOFillAttributes {
  ISOFillAttributes(int id, int color) : id(id), color(color) {}
  int id;
  int color;
};

struct ISORectangle {
  ISORectangle(int id, int line_attributes, int fill_attributes, int width, int height)
      : id(id), line_attributes(line_attributes), fill_attributes(fill_attributes), width(width), height(height) {}
  int id;
  int line_attributes;
  int fill_attributes;
  int width;
  int height;
};

struct ISOLine {
  ISOLine(int id, int line_attributes, int width, int height, int direction)
      : id(id), line_attributes(line_attributes), width(width), height(height), direction(direction) {}
  int id;
  int line_attributes;
  int width;
  int height;
  int direction;
};

enum ButtonState { RELEASED, PRESSED, HELD, ABORTED };

struct ISOButton {
  ISOButton() {}
  ISOButton(int id, string name, int width, int height, int bg_color, int border_color, int key_code, int options,
            YamlNode *layout)
      : id(id), name(name), width(width), height(height), bg_color(bg_color), border_color(border_color),
        key_code(key_code), options(options), layout(layout) {}
  int id;
  string name;
  int width;
  int height;
  int bg_color;
  int border_color;
  int key_code;
  int options;
  YamlNode *layout; // button can only be partially parsed on first pass
};

struct ISOContainer {
  ISOContainer(int id, string name, int width, int height, YamlNode *layout)
      : id(id), name(name), width(width), height(height), layout(layout) {}
  int id;
  string name;
  int width;
  int height;
  YamlNode *layout; // containers can only be partiall parsed on first pass
};

struct ISOInputList {
  ISOInputList(int id, string name, int width, int height, int options, YamlNode *layout)
      : id(id), name(name), width(width), height(height), options(options), layout(layout) {}
  int id;
  string name;
  int width;
  int height;
  int options;
  YamlNode *layout; // special layout - only strings, no location
};

struct ISOCheckbox {
  ISOCheckbox(int id, int font_id, int bg_color, int width)
      : id(id), font_id(font_id), bg_color(bg_color), width(width) {}
  int id;
  int font_id;
  int bg_color;
  int width;
};

struct ISOMeter {
  ISOMeter(int id, string name, int width, int needle_color, int border_color, int arc_color, int options,
           int ticks_count, int start_angle, int end_angle, int min_value, int max_value, int value)
      : id(id), name(name), width(width), needle_color(needle_color), border_color(border_color), arc_color(arc_color),
        options(options), ticks_count(ticks_count), start_angle(start_angle), end_angle(end_angle),
        min_value(min_value), max_value(max_value), value(value) {}
  int id;
  string name;
  int width;
  int needle_color;
  int border_color;
  int arc_color;
  int options;
  int ticks_count;
  int start_angle;
  int end_angle;
  int min_value;
  int max_value;
  int value;
};

typedef std::function<int(UITable *, int, int)> ui_button_func;

struct object_reference {
  /* Specific Types */
  const ISOFont *font_by_name(const char *name);
  int image_by_name(const char *name);
  int datamask_by_name(const char *name);
  int softkeys_by_name(const char *name);
  int line_attributes_by_name(const char *name);
  int fill_attributes_by_name(const char *name);
  list<ISOString> strings_to_add;
  unordered_map<std::string, ISOFont> fonts_to_add;
  unordered_map<std::string, ISOImage> images_to_add;
  unordered_map<std::string, int> datamask_ids_to_add;
  unordered_map<std::string, int> softkeys_ids_to_add;
  unordered_map<std::string, ISOLineAttributes> line_attributes_to_add;
  unordered_map<std::string, ISOFillAttributes> fill_attributes_to_add;
  list<ISORectangle> rectangles_to_add;
  list<ISOLine> lines_to_add;
  list<ISOMeter> meters_to_add;
  list<ISOButton> buttons_to_add_and_parse;
  list<ISOContainer> containers_to_add_and_parse;
  list<ISOInputList> input_lists_to_add_and_parse;
  list<ISOCheckbox> checkboxes_to_add;
  unordered_map<std::string, UITable> tables_to_add;

  /* Map of all action names to ids, and all ids to names */
  void button_ref(string name, int id);
  unordered_map<int, std::string> button_id2name;
  unordered_map<std::string, int> button_name2id;

  void datamask_ref(string name, int id);
  unordered_map<int, std::string> datamask_id2name;
  unordered_map<std::string, int> datamask_name2id;

  void input_list_ref(string name, int id);
  void input_list_element(int id, int index, string val);
  unordered_map<int, std::string> input_list_id2name;
  unordered_map<int, unordered_map<int, string>> input_list_id2values;
  unordered_map<std::string, int> input_list_name2id;

  void string_ref(string name, int id, int var_id);
  unordered_map<int, std::string> string_id2name;
  unordered_map<std::string, int> string_name2id;
  unordered_map<std::string, int> string_var_name2id;

  void meter_ref(string name, int id);
  unordered_map<std::string, int> meter_name2id;

  unordered_map<int, std::pair<UITable *, ui_button_func>> ui_button_map;
};

void object_reference::button_ref(string name, int id) {
  button_id2name.emplace(id, name);
  button_name2id.emplace(name, id);
}

void object_reference::datamask_ref(string name, int id) {
  datamask_id2name.emplace(id, name);
  datamask_name2id.emplace(name, id);
}

void object_reference::input_list_ref(string name, int id) {
  input_list_id2name.emplace(id, name);
  input_list_name2id.emplace(name, id);
  input_list_id2values.emplace(id, unordered_map<int, string>());
}

void object_reference::string_ref(string name, int id, int var_id) {
  string_id2name.emplace(id, name);
  string_name2id.emplace(name, id);
  string_var_name2id.emplace(name, var_id);
}

void object_reference::meter_ref(string name, int id) { meter_name2id.emplace(name, id); }

void object_reference::input_list_element(int id, int index, string val) {
  input_list_id2values[id].emplace(index, val);
}

const ISOFont *object_reference::font_by_name(const char *name) {
  unordered_map<string, ISOFont>::const_iterator got = this->fonts_to_add.find(name);
  if (got == this->fonts_to_add.end()) {
    return NULL;
  }
  return &got->second;
}

int object_reference::image_by_name(const char *name) {
  unordered_map<string, ISOImage>::const_iterator got = this->images_to_add.find(name);
  if (got == this->images_to_add.end()) {
    return -1;
  }
  return got->second.id;
}

int object_reference::datamask_by_name(const char *name) {
  unordered_map<string, int>::const_iterator got = this->datamask_ids_to_add.find(name);
  if (got == this->datamask_ids_to_add.end()) {
    return -1;
  }
  return got->second;
}

int object_reference::softkeys_by_name(const char *name) {
  unordered_map<string, int>::const_iterator got = this->softkeys_ids_to_add.find(name);
  if (got == this->softkeys_ids_to_add.end()) {
    return -1;
  }
  return got->second;
}

int object_reference::line_attributes_by_name(const char *name) {
  unordered_map<string, ISOLineAttributes>::const_iterator got = this->line_attributes_to_add.find(name);
  if (got == this->line_attributes_to_add.end()) {
    return -1;
  }
  return got->second.id;
}

int object_reference::fill_attributes_by_name(const char *name) {
  unordered_map<string, ISOFillAttributes>::const_iterator got = this->fill_attributes_to_add.find(name);
  if (got == this->fill_attributes_to_add.end()) {
    return -1;
  }
  return got->second.id;
}

int hex2(const char *pbuf) { return (unhex(*pbuf) << 4) | unhex(*(pbuf + 1)); }

int hex4(const char *pbuf) {
  return unhex(*pbuf) << 12 | unhex(*(pbuf + 1)) << 8 | unhex(*(pbuf + 2)) << 4 | unhex(*(pbuf + 3));
}

int parse_boolean(const char *str) {
  if (str[0] == '0') {
    return 0;
  }
  if (str[0] == '1') {
    return 1;
  }
  if (!strcmp(str, "true")) {
    return 1;
  }
  if (!strcmp(str, "false")) {
    return 0;
  }
  return 1;
}

int parse_position_number(const char *str, int maxval) {
  int retval = 0;
  int len = strlen(str);
  if (len >= 2) {
    if (!strcmp(str + len - 2, "px")) {
      retval = atoi(str);
    }
    if (!strcmp(str + len - 1, "%")) {
      if (maxval != 0) {
        double pct = atof(str) / 100.0;
        retval = pct * maxval;
        if (retval > maxval) {
          retval = maxval;
        }
      } else {
        clerr("Position percentage in a non relative context.\n");
      }
    }
  }
  return retval;
}

int color_index_from_chars(const char *pbuf) {
  if (strlen(pbuf) < 6) {
    return -1;
  }
  int r = 0;
  int g = 0;
  int b = 0;
  r = hex2(pbuf);
  pbuf += 2;
  g = hex2(pbuf);
  pbuf += 2;
  b = hex2(pbuf);
  return indexed_color(r, g, b);
}

int color_index_from_val(string *color) {
  const char *pbuf = color->c_str();
  return color_index_from_chars(pbuf);
}

int parse_width_height(YamlNode *item, struct vt_info *vt_info, int *width, int *height) {
  auto width_str = item->mfind("width");
  auto height_str = item->mfind("height");
  if (width_str == nullptr) {
    clerr("shape without width.\n");
    return 0;
  }
  if (height_str == nullptr) {
    clerr("shape without height.\n");
    return 0;
  }
  *width = parse_position_number(width_str->value()->c_str(), vt_info->width);
  *height = parse_position_number(height_str->value()->c_str(), vt_info->height);
  return 1;
}

std::string parse_name(YamlNode *item, const char *prefix, int id) {
  YamlNode *name = item->mfind("name");
  std::string str_name;
  if (name == nullptr) {
    char buffer[128];
    snprintf(buffer, 128, "%s%d", prefix, id);
    str_name = std::string(buffer);
  } else {
    str_name = *name->value();
  }
  return str_name;
}

const ISOFont *parse_font(YamlNode *item, object_reference *things) {
  YamlNode *font_name = item->mfind("font");
  if (font_name == nullptr) {
    return NULL;
  }
  return things->font_by_name(font_name->value()->c_str());
}

int parse_string(object_pool *op, YamlNode *item, object_reference *things, string parent, string *out) {

  const ISOFont *font = parse_font(item, things);
  if (font == nullptr) {
    clerr("font for string not found\n");
    return 0;
  }

  int justification_val = 0;
  auto justification = item->mfind("justification");
  if (justification != nullptr) {
    justification_val = atoi(justification->value()->c_str());
  }

  auto got = item->mfind("value");
  if (got == nullptr) {
    clerr("menu item string must have a value\n");
    return 0;
  }
  if (out != nullptr) {
    *out = *got->value();
  }

  /* Adding items to show */
  int string_id = next_object_id(op);
  int string_var_id = NULL_OBJECT_ID;
  /*
   * Create string vars here - wasn't able to get this to work with vt_set_string_value
  auto name_node = item->mfind("name");
  if (name_node != nullptr)
  {
      string_var_id = next_object_id(op);
  }
  */
  string name = parent + NPLUS + parse_name(item, "string_", string_id);
  things->string_ref(name, string_id, string_var_id);
  things->strings_to_add.emplace_back(string_id, string_var_id, *got->value(), font->id, justification_val);
  return string_id;
}

int parse_color(YamlNode *item, const char *key, int *color_index) {
  auto color_str = item->mfind(key);
  if (color_str == nullptr) {
    return 0;
  }
  *color_index = color_index_from_val(color_str->value());
  return 1;
}

int parse_button(object_pool *op, YamlNode *item, object_reference *things, string parent, struct vt_info *vt_info,
                 int button_id, ISOButton *button) {

  int width, height;
  if (!parse_width_height(item, vt_info, &width, &height)) {
    clerr("BUTTON failed to parse width / height\n");
    return 0;
  }

  int bg_color_index;
  if (!parse_color(item, "bg_color", &bg_color_index)) {
    clerr("Button with no bg_color\n");
    return 0;
  }

  int border_color_index;
  if (!parse_color(item, "border_color", &border_color_index)) {
    clerr("Button with no border color\n");
    return 0;
  }

  auto key_code = item->mfind("key_code");

  int options_val = 0;
  auto options = item->mfind("options");
  if (options != nullptr) {
    options_val = atoi(options->value()->c_str());
  }

  auto latchable = item->mfind("latchable");
  if (latchable != nullptr) {
    int latchable_val = atoi(latchable->value()->c_str());
    if (latchable_val) {
      options_val |= 1 << 0;
    }
  }

  /* This doesn't appear to work even with version 4 - we get object pool errors
  auto disabled = item->mfind("disabled");
  if (disabled != nullptr)
  {
      if (op->vt_info.vt_version >= 4)
      {
          int disabled_val = atoi(disabled->value()->c_str());
          if (disabled_val)
          {
              options_val |= 1 << 4;
          }
      }
      else
      {
          clwarn("disabled buttons only available in vt version 4 and above. (%d)\n",
                 op->vt_info.vt_version);
      }
  }
  */

  YamlNode *layout = item->mfind("layout");
  if (layout == nullptr) {
    clerr("Button with no layout\n");
    return 0;
  }

  if (button_id == 0) {
    button_id = next_object_id(op);
  }
  int key_code_val;
  if (key_code != nullptr) {
    key_code_val = atoi(key_code->value()->c_str());
  } else {
    key_code_val = button_id;
  }

  std::string button_name = parent + NPLUS + parse_name(item, "button_", button_id);
  *button = ISOButton(button_id, button_name, width, height, bg_color_index, border_color_index, key_code_val,
                      options_val, layout);
  return 1;
}

void add_element_loc(object_pool *op, int object_id, int x_loc, int y_loc) {
  opb_add16(&op->opb, object_id);
  opb_add16(&op->opb, x_loc); // x location
  opb_add16(&op->opb, y_loc); // y location
}

int set_element(object_pool *op, YamlNode *item, object_reference *things, string parent, struct vt_info *vt_info) {
  enum { STRING, IMAGE, LINE, RECTANGLE, BUTTON, INPUT_LIST, CONTAINER, CHECKBOX, METER, TABLE } type;

  int object_id;
  int x_loc, y_loc;
  x_loc = y_loc = 0;
  int width, height;

  /* Figure out type */
  YamlNode *got = item->mfind("type");
  if (got == nullptr) {
    clerr("No type found\n");
    return 0;
  }

  if (got->value()->compare("string") == 0) {
    type = STRING;
  } else if (got->value()->compare("image") == 0) {
    type = IMAGE;
  } else if (got->value()->compare("rectangle") == 0) {
    type = RECTANGLE;
  } else if (got->value()->compare("line") == 0) {
    type = LINE;
  } else if (got->value()->compare("button") == 0) {
    type = BUTTON;
  } else if (got->value()->compare("inputlist") == 0) {
    type = INPUT_LIST;
  } else if (got->value()->compare("container") == 0) {
    type = CONTAINER;
  } else if (got->value()->compare("checkbox") == 0) {
    type = CHECKBOX;
  } else if (got->value()->compare("meter") == 0) {
    type = METER;
  } else if (got->value()->compare("table") == 0) {
    type = TABLE;
  } else {
    clerr("Don't know about element type %s\n", got->value()->c_str());
    return 0;
  }

  /* Handle basic x,y location things */
  /* Find location */
  got = item->mfind("x_loc");
  if (got == nullptr) {
    clerr("no x_loc for menuitem!\n");
    return 0;
  }
  x_loc = parse_position_number(got->value()->c_str(), vt_info->width);

  got = item->mfind("y_loc");
  if (got == nullptr) {
    clerr("no y_loc for menuitem!\n");
    return 0;
  }
  y_loc = parse_position_number(got->value()->c_str(), vt_info->height);

  /* Type specific items */
  if (type == INPUT_LIST) {
    if (!parse_width_height(item, vt_info, &width, &height)) {
      clerr("INPUT LIST with no width / height\n");
      return 0;
    }

    YamlNode *layout = item->mfind("layout");
    object_id = next_object_id(op);
    string name = parent + NPLUS + parse_name(item, "input_list_", object_id);
    things->input_list_ref(name, object_id);
    things->input_lists_to_add_and_parse.emplace_back(object_id, name, width, height, 1, layout);
  } else if (type == STRING) {
    object_id = parse_string(op, item, things, parent, NULL);
    if (object_id <= 0) {
      clerr("Failed to parse string\n");
      return 0;
    }
  } else if (type == IMAGE) {
    auto image_name = item->mfind("image");
    if (image_name == nullptr) {
      clerr("image name not specified for element.\n");
      return 0;
    }
    int image_id = things->image_by_name(image_name->value()->c_str());
    if (image_id < 0) {
      clerr("image for %s not found\n", image_name->value()->c_str());
      return 0;
    }
    object_id = image_id;
  } else if (type == RECTANGLE || type == LINE) {
    auto line_attributes = item->mfind("line_attributes");
    if (line_attributes == nullptr) {
      clerr("no line attributes for shape\n");
      return 0;
    }

    int line_attributes_id = things->line_attributes_by_name(line_attributes->value()->c_str());
    if (line_attributes_id <= 0) {
      clerr("line attributes for %s not found.\n", line_attributes->value()->c_str());
      return 0;
    }

    if (!parse_width_height(item, vt_info, &width, &height)) {
      clerr("LINE / RECTANGLE failed to parse width / height\n");
      return 0;
    }

    if (type == LINE) {
      auto direction = item->mfind("direction");
      if (direction == nullptr) {
        clerr("Line with no direction\n");
        return 0;
      }
      int line_id = next_object_id(op);
      things->lines_to_add.emplace_back(line_id, line_attributes_id, width, height, atoi(direction->value()->c_str()));
      object_id = line_id;
    } else if (type == RECTANGLE) {
      int fill_attributes_id = -1;
      auto fill_attributes = item->mfind("fill_attributes");
      if (fill_attributes != nullptr) {
        fill_attributes_id = things->fill_attributes_by_name(fill_attributes->value()->c_str());
        if (fill_attributes_id <= 0) {
          clerr("Fill attributes %s not found for rectangle.\n", fill_attributes->value()->c_str());
          return 0;
        }
      }
      int rect_id = next_object_id(op);
      things->rectangles_to_add.emplace_back(rect_id, line_attributes_id, fill_attributes_id, width, height);
      object_id = rect_id;
    }
  } else if (type == METER) {
    YamlNode *width_str = item->mfind("width");
    if (width_str == nullptr) {
      clerr("meter with no width\n");
      return 0;
    }
    int width = parse_position_number(width_str->value()->c_str(), vt_info->width);

    int needle_color_index;
    if (!parse_color(item, "needle_color", &needle_color_index)) {
      clerr("meter with no needle color\n");
      return 0;
    }

    int border_color_index;
    if (!parse_color(item, "border_color", &border_color_index)) {
      clerr("meter with no border color\n");
      return 0;
    }

    int arc_color_index;
    if (!parse_color(item, "arc_color", &arc_color_index)) {
      clerr("meter with no arc color\n");
      return 0;
    }

    int options = 0b00001111; // default clockwise, draw ticks, draw border, draw arc
    YamlNode *opt = item->mfind("clockwise");
    if (opt != nullptr) {
      int val = parse_boolean(opt->value()->c_str());
      if (!val) {
        options &= 0b00000111;
      }
    }

    opt = item->mfind("draw_ticks");
    if (opt != nullptr) {
      int val = parse_boolean(opt->value()->c_str());
      if (!val) {
        options &= 0b00001011;
      }
    }

    opt = item->mfind("draw_border");
    if (opt != nullptr) {
      int val = parse_boolean(opt->value()->c_str());
      if (!val) {
        options &= 0b00001101;
      }
    }

    opt = item->mfind("draw_arc");
    if (opt != nullptr) {
      int val = parse_boolean(opt->value()->c_str());
      if (!val) {
        options &= 0b00001110;
      }
    }

    YamlNode *ticks = item->mfind("ticks");
    if (ticks == nullptr) {
      clerr("meter without ticks\n");
      return 0;
    }

    int ticks_count = atoi(ticks->value()->c_str());

    int start_angle = 0;
    YamlNode *sa = item->mfind("start_angle");
    if (sa != nullptr) {
      start_angle = atoi(sa->value()->c_str());
    }

    int end_angle = 180;
    YamlNode *ea = item->mfind("end_angle");
    if (ea != nullptr) {
      end_angle = atoi(ea->value()->c_str());
    }

    int min_value = 0;
    YamlNode *mv = item->mfind("min_value");
    if (mv != nullptr) {
      min_value = atoi(mv->value()->c_str());
    }

    int max_value = 100;
    mv = item->mfind("max_value");
    if (mv != nullptr) {
      max_value = atoi(mv->value()->c_str());
    }

    int value = min_value;
    mv = item->mfind("value");
    if (mv != nullptr) {
      value = atoi(mv->value()->c_str());
    }

    object_id = next_object_id(op);
    string name = parent + NPLUS + parse_name(item, "meter_", object_id);
    things->meter_ref(name, object_id);
    things->meters_to_add.emplace_back(object_id, name, width, needle_color_index, border_color_index, arc_color_index,
                                       options, ticks_count, start_angle, end_angle, min_value, max_value, value);

  } else if (type == BUTTON) {
    ISOButton button;
    if (!parse_button(op, item, things, parent, vt_info, 0, &button)) {
      return 0;
    }
    things->button_ref(button.name, button.id);
    things->buttons_to_add_and_parse.push_back(button);
    object_id = button.id;
  } else if (type == CONTAINER) {
    if (!parse_width_height(item, vt_info, &width, &height)) {
      clerr("CONTAINER failed to parse width / height\n");
      return 0;
    }
    YamlNode *layout = item->mfind("layout");
    if (layout == nullptr) {
      clerr("container with no layout\n");
      return 0;
    }
    object_id = next_object_id(op);
    things->containers_to_add_and_parse.emplace_back(object_id, parent, width, height, layout);
  } else if (type == TABLE) {
    if (!parse_width_height(item, vt_info, &width, &height)) {
      clerr("TABLE failed to parse width / height\n");
      return 0;
    }

    int line_attributes_id, fill_attributes_id;
    auto line_attributes = item->mfind("line_attributes");
    if (line_attributes == nullptr) {
      clerr("No line_attributes for TABLE\n");
      return 0;
    }
    line_attributes_id = things->line_attributes_by_name(line_attributes->value()->c_str());
    if (line_attributes_id <= 0) {
      clerr("line_attributes for table not found\n");
      return 0;
    }

    auto fill_attributes = item->mfind("fill_attributes");
    if (fill_attributes == nullptr) {
      clerr("No fill_attributes for TABLE\n");
      return 0;
    }
    fill_attributes_id = things->fill_attributes_by_name(fill_attributes->value()->c_str());
    if (fill_attributes_id <= 0) {
      clerr("fill_attributes for TABLE not found\n");
      return 0;
    }

    int header_fill_attributes = -1;
    YamlNode *header_fill = item->mfind("header_fill_attributes");
    if (header_fill != nullptr) {
      header_fill_attributes = things->fill_attributes_by_name(header_fill->value()->c_str());
      if (header_fill_attributes <= 0) {
        clerr("header_fill_attributes for TABLE not found\n");
        return 0;
      }
    }

    YamlNode *columns = item->mfind("columns");

    int header_height = 0;
    YamlNode *general = item->mfind("header_height");
    if (general != nullptr) {
      header_height = parse_position_number(general->value()->c_str(), height);
    }

    int rows = 1;
    general = item->mfind("rows");
    if (general != nullptr) {
      rows = atoi(general->value()->c_str());
    }

    bool hide_row_dividers = false;
    general = item->mfind("hide_row_dividers");
    if (general != nullptr) {
      hide_row_dividers = parse_boolean(general->value()->c_str());
    }

    const ISOFont *font = parse_font(item, things);
    if (font == nullptr) {
      clerr("Table font not found\n");
      return 0;
    }

    YamlNode *upscroll = item->mfind("upscroll");
    YamlNode *downscroll = item->mfind("downscroll");

    object_id = next_object_id(op);
    std::string name = parent + NPLUS + parse_name(item, "table_", object_id);
    things->tables_to_add.emplace(name, UITable(object_id, name, width, height, line_attributes_id, fill_attributes_id,
                                                header_fill_attributes, header_height, rows, hide_row_dividers, font,
                                                columns, upscroll, downscroll));
  } else if (type == CHECKBOX) {
    object_id = next_object_id(op);
    const ISOFont *font = parse_font(item, things);
    if (font == nullptr) {
      clerr("font for checkbox not found\n");
      return 0;
    }
    int bg_color_index;
    if (!parse_color(item, "bg_color", &bg_color_index)) {
      clerr("Checkbox with no bg_color\n");
      return 0;
    }
    YamlNode *width_str = item->mfind("width");
    if (width_str == nullptr) {
      clerr("checkbox with no width\n");
      return 0;
    }
    int width = parse_position_number(width_str->value()->c_str(), vt_info->width);
    things->checkboxes_to_add.emplace_back(object_id, font->id, bg_color_index, width);
  }

  add_element_loc(op, object_id, x_loc, y_loc);
  return 1;
}

int set_layout(object_pool *op, YamlNode *layout, object_reference *things, string parent, struct vt_info *vt_info) {
  if (layout == nullptr) {
    return 1;
  }
  for (auto element = layout->list()->begin(); element != layout->list()->end(); ++element) {
    if (!set_element(op, element->get(), things, parent, vt_info)) {
      return 0;
    }
  }
  return 1;
}

int set_menu_item(object_pool *op, YamlNode *config, object_reference *things) {
  YamlNode *menuitem = config->mfind("menuitem");
  if (menuitem == nullptr) {
    clerr("No menuitem found\n");
    return 0;
  }

  int bg_color_index;
  if (!parse_color(menuitem, "bg_color", &bg_color_index)) {
    clerr("menuitem has no bg_color\n");
    return 0;
  }

  /* Basic set up */
  opb_add8(&op->opb, bg_color_index); // bg color, ISO11783-6 Table A.4
  opb_add8(&op->opb, 1);              // selectable

  auto default_datamask = menuitem->mfind("default_datamask");
  if (default_datamask == nullptr) {
    clerr("menuitem has no default datamask.\n");
    return 0;
  }

  int active_mask_id = things->datamask_by_name(default_datamask->value()->c_str());
  if (active_mask_id <= 0) {
    active_mask_id = next_object_id(op);
    things->datamask_ids_to_add.emplace(*default_datamask->value(), active_mask_id);
  }
  opb_add16(&op->opb, active_mask_id); // default data mask

  YamlNode *layout = menuitem->mfind("layout");
  if (layout == nullptr) {
    clerr("menuitem has no layout.\n");
    return 0;
  }

  int objects = layout->list()->size();
  opb_add8(&op->opb, objects); // number of objects to follow as identifier
  opb_add8(&op->opb, 0);       // number of macros to follow

  opb_add8(&op->opb, 1); // number of languages to follow

  if (!set_layout(op, layout, things, "", &op->vt_info)) // menuitem has no name
  {
    clerr("menuitem failed to set layout.\n");
    return 0;
  }
  return 1;
}

int set_fonts(object_pool *op, YamlNode *config, object_reference *things) {
  YamlNode *fonts = config->mfind("font_attributes");
  if (fonts == nullptr) {
    clerr("No font attributes found\n");
    return 0;
  }

  for (auto it = fonts->list()->begin(); it != fonts->list()->end(); ++it) {
    YamlNode *fa = it->get();

    YamlNode *size = fa->mfind("size");
    if (size == nullptr) {
      clerr("font with no size\n");
      return 0;
    }
    YamlNode *style = fa->mfind("style");
    if (style == nullptr) {
      clerr("font with no style\n");
      return 0;
    }
    int color_idx;
    if (!parse_color(fa, "color", &color_idx)) {
      clerr("font with not color\n");
      return 0;
    }
    int font_id = next_object_id(op);
    string name = parse_name(fa, "font_", font_id);
    things->fonts_to_add.emplace(
        name, ISOFont(font_id, name, color_idx, atoi(size->value()->c_str()), atoi(style->value()->c_str())));
  }

  return 1;
}

int set_fill_attributes(object_pool *op, YamlNode *config, object_reference *things) {
  YamlNode *fas = config->mfind("fill_attributes");
  if (fas == nullptr) {
    return 1; // no fill attributes is fine
  }
  for (auto it = fas->list()->begin(); it != fas->list()->end(); ++it) {
    YamlNode *fa = it->get();

    int color_idx;
    if (!parse_color(fa, "color", &color_idx)) {
      clerr("fill attributes color index failed\n");
      return 0;
    }
    int faid = next_object_id(op);
    string name = parse_name(fa, "fill_attributes_", faid);
    things->fill_attributes_to_add.emplace(name, ISOFillAttributes(faid, color_idx));
  }
  return 1;
}

int set_line_attributes(object_pool *op, YamlNode *config, object_reference *things) {
  YamlNode *las = config->mfind("line_attributes");
  if (las == nullptr) {
    return 1; // no line attributes is fine
  }

  for (auto it = las->list()->begin(); it != las->list()->end(); ++it) {
    YamlNode *la = it->get();

    int color_idx;
    if (!parse_color(la, "color", &color_idx)) {
      clerr("line attributes color index failed\n");
      return 0;
    }
    YamlNode *thickness = la->mfind("thickness");
    if (thickness == nullptr) {
      clerr("line attributes has no thickness\n");
      return 0;
    }
    int pattern = 0xffff; // default pattern
    YamlNode *pattern_str = la->mfind("pattern");
    if (pattern_str != nullptr) {
      const char *pbuf = pattern_str->value()->c_str();
      if (strlen(pbuf) != 4) {
        clerr("Need a 2 byte hex number for line attributes pattern, got %lu\n", strlen(pbuf));
        return 0;
      }
      pattern = hex4(pbuf);
    }
    int laid = next_object_id(op);
    string name = parse_name(la, "line_attributes_", laid);
    things->line_attributes_to_add.emplace(
        name, ISOLineAttributes(laid, color_idx, atoi(thickness->value()->c_str()), pattern));
  }
  return 1;
}

int set_images(const char *layout_dir, object_pool *op, YamlNode *config, object_reference *things) {
  YamlNode *images = config->mfind("images");
  if (images == nullptr) {
    clerr("No images section in layout.\n");
    return 1;
  }

  for (auto it = images->list()->begin(); it != images->list()->end(); ++it) {
    YamlNode *im = it->get();

    int new_width = 0, new_height = 0;

    YamlNode *path = im->mfind("path");
    if (path == nullptr) {
      clerr("image has no path\n");
    }
    YamlNode *resize = im->mfind("resize");
    if (resize != nullptr) {
      YamlNode *rv = resize->mfind("width");
      if (rv != nullptr) {
        new_width = atoi(rv->value()->c_str());
      }
      rv = resize->mfind("height");
      if (rv != nullptr) {
        new_height = atoi(rv->value()->c_str());
      } else {
        new_width = 0;
      }
    }
    int image_id = next_object_id(op);
    string name = parse_name(im, "image_", image_id);
    string relpath = string(layout_dir) + string("/") + *path->value();
    things->images_to_add.emplace(name, ISOImage(image_id, relpath, new_width, new_height));
  }
  return 1;
}

int set_data_masks(object_pool *op, YamlNode *config, object_reference *things) {
  YamlNode *datamasks = config->mfind("datamasks");
  if (datamasks == nullptr) {
    clerr("no datamasks defined.\n"); // must have at least one datamask
    return 0;
  }

  for (auto it = datamasks->list()->begin(); it != datamasks->list()->end(); ++it) {
    string name = parse_name(it->get(), "data_mask_", 0);

    int data_mask_id = things->datamask_by_name(name.c_str());

    if (data_mask_id <= 0) {
      data_mask_id = next_object_id(op);
      things->datamask_ids_to_add.emplace(name, data_mask_id);
    }

    things->datamask_ref(name, data_mask_id);

    int bg_color_index;
    if (!parse_color(it->get(), "bg_color", &bg_color_index)) {
      clerr("Datamask has no bg_color");
      return 0;
    }

    int objects = 0;
    YamlNode *layout = (*it)->mfind("layout");
    if (layout != nullptr) {
      objects = layout->list()->size();
    }

    /* get set for the soft key mask */
    YamlNode *soft_key_mask = (*it)->mfind("softkeys");
    int soft_key_mask_id = NULL_OBJECT_ID;
    if (soft_key_mask != nullptr) {
      soft_key_mask_id = things->softkeys_by_name(soft_key_mask->value()->c_str());
      if (soft_key_mask_id <= 0) {
        soft_key_mask_id = next_object_id(op);
        things->softkeys_ids_to_add.emplace(*soft_key_mask->value(), soft_key_mask_id);
      }
    }
    add_data_mask(op, data_mask_id, bg_color_index, soft_key_mask_id, objects);

    if (!set_layout(op, layout, things, name, &op->vt_info)) {
      return 0;
    }
  }
  return 1;
}

#define MAX_BUTTONS 10
int set_softkey_masks(object_pool *op, YamlNode *config, object_reference *things) {
  YamlNode *softkeys = config->mfind("softkeys");
  if (softkeys == nullptr) {
    return 1; // no soft keys defined is ok
  }

  int button_ids[MAX_BUTTONS];
  for (auto it = softkeys->list()->begin(); it != softkeys->list()->end(); ++it) {
    YamlNode *soft_key_mask = it->get();

    string name = parse_name(soft_key_mask, "soft_key_mask_", 0);

    int soft_key_mask_id = things->softkeys_by_name(name.c_str());
    if (soft_key_mask_id <= 0) {
      soft_key_mask_id = next_object_id(op);
      things->softkeys_ids_to_add.emplace(*soft_key_mask->value(), soft_key_mask_id);
    }

    int bg_color_index;
    if (!parse_color(soft_key_mask, "bg_color", &bg_color_index)) {
      clerr("Soft key mask has no bg_color\n");
      return 0;
    }

    auto buttons = soft_key_mask->mfind("buttons");
    int num_buttons = 0;
    if (buttons != nullptr) {
      num_buttons = buttons->list()->size();
      for (int i = 0; i < num_buttons; i++) {
        button_ids[i] = next_object_id(op);
      }
    }
    add_soft_key_mask(op, soft_key_mask_id, bg_color_index, button_ids, num_buttons);
    if (buttons) {
      int i = 0;
      for (auto key = buttons->list()->begin(); key != buttons->list()->end(); ++key) {
        YamlNode *layout = (*key)->mfind("layout");
        if (layout == nullptr) {
          clerr("Button without layout\n");
          return 0;
        }
        int objects = layout->list()->size();
        if (!parse_color(key->get(), "bg_color", &bg_color_index)) {
          clerr("Key with no bg_color\n");
          return 0;
        }
        add_soft_key(op, button_ids[i], bg_color_index, objects);

        /* bring the soft key name into the button map */
        string button_name = parse_name(key->get(), "button_", button_ids[i]);
        things->button_ref(button_name, button_ids[i]);

        /* set button layout */
        if (!set_layout(op, layout, things, name + NPLUS + button_name, &op->vt_info)) {
          clerr("Setting button layout failed.\n");
          return 0;
        }

        i++;
      }
    }
  }
  return 1;
}

int set_container(object_pool *op, ISOContainer &container, object_reference *things) {
  int objects = container.layout->list()->size();
  add_container(op, container.id, container.width, container.height, objects);
  /* Construct new vt_info from container context */
  struct vt_info vt_info;
  vt_info.width = container.width;
  vt_info.height = container.height;
  vt_info.vt_version = op->vt_info.vt_version;

  /* call set layout to parse out the layout in container context space */
  if (!set_layout(op, container.layout, things, container.name, &vt_info)) {
    clerr("Failed to set container layout.\n");
    return 0;
  }
  return 1;
}

int set_button(object_pool *op, ISOButton &button, object_reference *things) {
  int objects = button.layout->list()->size();
  add_button(op, button.id, button.width, button.height, button.bg_color, button.border_color, button.key_code,
             button.options, objects);
  /* Construct new vt_info from button context */
  struct vt_info vt_info;
  vt_info.width = button.width;
  vt_info.height = button.height;
  vt_info.vt_version = op->vt_info.vt_version;

  /* call set layout to parse out the layout in button context space */
  if (!set_layout(op, button.layout, things, button.name, &vt_info)) {
    clerr("Failed to set button layout.\n");
    return 0;
  }
  return 1;
}

int set_input_list(object_pool *op, ISOInputList &list, object_reference *things) {
  int string_id;
  int objects = list.layout->list()->size();
  add_input_list(op, list.id, list.width, list.height, list.options, objects, 0);

  // can't use set_layout here - things in the list have no x, y
  int index = 0;
  for (auto element = list.layout->list()->begin(); element != list.layout->list()->end(); ++element) {
    YamlNode *item = element->get();
    YamlNode *type = item->mfind("type");
    if (type == nullptr) {
      clerr("Input list item has no type\n");
      return 0;
    }
    if (type->value()->compare("string")) {
      clerr("Input list layout types must all be strings\n");
      return 0;
    }
    string val;
    string_id = parse_string(op, item, things, list.name, &val);
    things->input_list_element(list.id, index, val);
    opb_add16(&op->opb, string_id);
    index++;
  }
  return 1;
}

int set_working_set(object_pool *op, YamlNode *config, object_reference *things) {
  int working_set_object_id = next_object_id(op);
  opb_add16(&op->opb, working_set_object_id);
  opb_add8(&op->opb, WORKING_SET_TYPE);

  /* Set up our menu item and pre-allocate default data mask id */
  if (!set_menu_item(op, config, things)) {
    return 0;
  }

  /* Add Languages here */
  opb_add8(&op->opb, 'e');
  opb_add8(&op->opb, 'n');

  return working_set_object_id;
}

int table_upscroll(UITable *table, int key, int value) {
  if (value != PRESSED) {
    return 0;
  }
  table->press_upscroll();
  return 0;
}

int table_downscroll(UITable *table, int key, int value) {
  if (value != PRESSED) {
    return 0;
  }
  table->press_downscroll();
  return 0;
}

void add_table(object_pool *op, UITable *table, object_reference *things) {
  /* For relative sizing from this table */
  struct vt_info vt_info;
  vt_info.width = table->width;
  vt_info.height = table->height;
  vt_info.vt_version = op->vt_info.vt_version;

  // object id for the table is the container
  int rect_id;
  int header_id;
  int objects = 1; // 1 for the rect
  int usable_width = table->width;

  /* Allocated object IDs */
  int base_rows_id = 0;
  int base_columns_id = 0;
  int base_header_string_id = 0;
  int count_header_strings;
  int upscrollid, downscrollid, upscrollw, upscrollh, downscrollw, downscrollh;

  /* Add the header rect to object count */
  if (table->header_fill_attributes > 0) {
    objects++;
  }

  /* Add upscroll / downscroll */
  if (table->upscroll != nullptr && table->downscroll != nullptr) {
    objects += 3;
    upscrollid = next_object_id(op);
    downscrollid = next_object_id(op);
    table->scrollbar_id = next_object_id(op);

    if (!parse_width_height(table->upscroll, &vt_info, &upscrollw, &upscrollh)) {
      clerr("Failed to parse upscroll size\n");
    }

    if (!parse_width_height(table->downscroll, &vt_info, &downscrollw, &downscrollh)) {
      clerr("Failed to parse downscroll size\n");
    }

    usable_width = table->width - max(upscrollw, downscrollw);
    vt_info.width = usable_width;
  }

  if (table->columns != nullptr) {
    /* Add the columns dividers to object count */
    objects += table->columns->list()->size() - 1;

    if (table->header_fill_attributes > 0) {
      for (auto column = table->columns->list()->begin(); column != table->columns->list()->end(); ++column) {
        YamlNode *layout = column->get()->mfind("title_layout");
        if (layout != nullptr) {
          /* Add each column header string to object count */
          objects++;

          /* And allocate object IDs */
          if (base_header_string_id == 0) {
            base_header_string_id = next_object_id(op);
          } else {
            next_object_id(op);
          }
        }
      }
    }
  }

  /* Allocate Object IDs for all line and rects */

  /* Table has more than one rows then we need horizontal dividers */
  if (!table->hide_row_dividers && table->display_rows > 1) {
    base_rows_id = next_object_id(op);
    objects++;

    /* For all rows after the first one we have to manually bump the object ids */
    for (int i = 0; i < table->display_rows - 2; i++) {
      next_object_id(op);
      objects++;
    }
  }

  /* If columns is more than one then we need vertical dividers */
  if (table->columns != nullptr) {
    if (table->columns->list()->size() > 1) {
      base_columns_id = next_object_id(op);
      for (int i = 0; i < table->columns->list()->size() - 2; i++) {
        next_object_id(op);
      }
    }
  }

  /* make ids for all the cell strings */
  for (int i = 0; i < table->display_rows; i++) {
    for (int j = 0; j < table->numcols; j++) {
      table->display_cells[j + i * table->numcols] = next_object_id(op);
      objects++;
    }
  }

  /* Top level container */
  add_container(op, table->id, table->width, table->height, objects);

  /* locate all sub objects */

  /* locate the outside rectangle */
  rect_id = next_object_id(op);
  add_element_loc(op, rect_id, 0, 0);

  /* locate the header */
  if (table->header_fill_attributes > 0) {
    header_id = next_object_id(op);
    add_element_loc(op, header_id, 0, 0);
  }

  /* locate the row heights */
  int header_height_offset = 0;
  if (table->header_fill_attributes > 0) {
    if (table->header_height > 0) {
      header_height_offset = table->header_height;
    } else {
      header_height_offset = table->height / (table->display_rows + 1);
    }
  }
  int row_gaps = (table->height - header_height_offset) / table->display_rows;

  /* locate the row dividers */
  if (!table->hide_row_dividers) {
    for (int i = 0; i < table->display_rows - 1; i++) {
      add_element_loc(op, base_rows_id + i, 0, header_height_offset + (i + 1) * row_gaps);
    }
  }

  /* locate the first column strings */
  for (int i = 0; i < table->display_rows; i++) {
    add_element_loc(op, table->display_cells[i * table->numcols], 1, header_height_offset + i * row_gaps + 1);
  }

  int which_column_string = 0;
  if (table->columns != nullptr) {
    int count = 0;
    int last_w = 0;
    for (auto column = table->columns->list()->begin(); column != table->columns->list()->end(); ++column) {

      /* locate column header strings */
      YamlNode *layout = column->get()->mfind("title_layout");
      if (layout != nullptr) {
        add_element_loc(op, base_header_string_id + which_column_string, last_w + 2, 0.1 * header_height_offset);
        which_column_string++;
      }

      /* locate column dividers */
      if (count + 1 == table->columns->list()->size()) {
        break; // don't do the last one
      }
      YamlNode *wnode = column->get()->mfind("width");
      int width = 0;
      if (wnode != nullptr) {
        width = parse_position_number(wnode->value()->c_str(), usable_width);
      }
      last_w += width;
      add_element_loc(op, base_columns_id + count, last_w, 0);

      count++;

      /* locate all the row strings for this column */
      for (int i = 0; i < table->display_rows; i++) {
        add_element_loc(op, table->display_cells[i * table->numcols + count], last_w + 1,
                        header_height_offset + i * row_gaps + 1);
      }
    }
  }

  /* locate scroll buttons */
  if (table->upscroll != nullptr && table->downscroll != nullptr) {
    add_element_loc(op, upscrollid, usable_width + 1, 0);
    add_element_loc(op, downscrollid, usable_width + 1, table->height - downscrollh);
    table->scrollbar_x = usable_width + 1;
    table->scrollbar_ymin = upscrollh + 1;
    table->scrollbar_ymax = table->height - downscrollh - 1;
    table->scrollbar_w = table->width - usable_width;
    table->scrollbar_y = table->scrollbar_ymin;
    add_element_loc(op, table->scrollbar_id, table->scrollbar_x, table->scrollbar_y);
  }

  /* Add all the sub objects */

  /* Add the outside rectangle */
  add_rectangle(op, rect_id, table->line_attributes, table->fill_attributes, usable_width, table->height);

  /* Add the header */
  if (table->header_fill_attributes > 0) {
    add_rectangle(op, header_id, table->line_attributes, table->header_fill_attributes, usable_width,
                  header_height_offset);
  }

  /* Add the row dividiers */
  if (!table->hide_row_dividers) {
    for (int i = 0; i < table->display_rows - 1; i++) {
      add_line(op, base_rows_id + i, table->line_attributes, usable_width, 1, 0);
    }
  }

  /* Add the column dividiers */
  int justification = 0;
  char base_string[4096];
  memset(base_string, ' ', 4096);
  if (table->columns != nullptr) {
    int count = 0;
    which_column_string = 0;
    for (auto column = table->columns->list()->begin(); column != table->columns->list()->end(); ++column) {

      /* add the text */
      int width = 0;
      YamlNode *layout = column->get()->mfind("title_layout");
      if (layout != nullptr) {
        const ISOFont *font = parse_font(layout, things);
        if (font == nullptr) {
          clerr("No found found for table\n");
        }
        YamlNode *value = layout->mfind("value");
        if (value == nullptr) {
          clerr("table column header string has no value!\n");
        }

        YamlNode *wnode = column->get()->mfind("width");
        if (wnode != nullptr) {
          width = parse_position_number(wnode->value()->c_str(), usable_width);
        }
        add_string(op, base_header_string_id + which_column_string, NULL_OBJECT_ID, value->value()->c_str(), width,
                   header_height_offset, font->id, 0b0000); // justifaction is bad
        which_column_string++;
      }

      /* Add the strings for all the rows for this column */

      /* zero term the base string for this column */
      int cell_chars = ((width - 2) / table->font->width) * (row_gaps / table->font->height);
      base_string[cell_chars] = 0;
      for (int i = 0; i < table->display_rows; i++) {
        add_string(op, table->display_cells[i * table->numcols + count], NULL_OBJECT_ID, base_string, width - 2,
                   row_gaps, table->font->id, justification);
      }

      /* reset the bast string */
      base_string[cell_chars] = base_string[cell_chars - 1];

      /* add the line */
      if (count + 1 == table->columns->list()->size()) {
        break; // don't do the last one
      }
      add_line(op, base_columns_id + count, table->line_attributes, 1, table->height, 0);

      count++;
    }
  } else {
    /* Add the first column strings */

    /* add zero string terminator at the end of our W x H chars */
    int cell_chars = ((usable_width - 2) / table->font->width) * row_gaps / table->font->height;
    base_string[cell_chars] = 0;

    for (int i = 0; i < table->display_rows; i++) {
      add_string(op, table->display_cells[i], NULL_OBJECT_ID, base_string, usable_width - 2, row_gaps, table->font->id,
                 justification);
    }

    /* reset the base string */
    base_string[cell_chars] = base_string[cell_chars - 1];
  }

  /* Add scroll arrows */
  if (table->upscroll != nullptr && table->downscroll != nullptr) {
    ISOButton button;
    parse_button(op, table->upscroll, things, parse_name(table->upscroll, "TABLE_", table->id), &vt_info, upscrollid,
                 &button);
    set_button(op, button, things);

    parse_button(op, table->downscroll, things, parse_name(table->downscroll, "TABLE_", table->id), &vt_info,
                 downscrollid, &button);
    set_button(op, button, things);

    /* Register with the ui button map */
    ui_button_func us = table_upscroll;
    ui_button_func ds = table_downscroll;
    things->ui_button_map.emplace(upscrollid, std::make_pair(table, us));
    things->ui_button_map.emplace(downscrollid, std::make_pair(table, ds));

    /* Put scrollbar */
    add_rectangle(op, table->scrollbar_id, table->line_attributes, table->fill_attributes, table->scrollbar_w,
                  table->scrollbar_ymax - table->scrollbar_ymin);
  }
}

object_reference *things = NULL;

void print_all_paths() {
  clinfo("NAMESPACE:\n");
  clinfo("- datamasks:\n");
  for (auto it : things->datamask_name2id) {
    clinfo("\t[%d] %s\n", it.second, it.first.c_str());
  }
  clinfo("- buttons:\n");
  for (auto it : things->button_name2id) {
    clinfo("\t[%d] %s\n", it.second, it.first.c_str());
  }
  clinfo(" - input lists:\n");
  for (auto it : things->input_list_name2id) {
    clinfo("\t[%d] %s\n", it.second, it.first.c_str());
  }
  clinfo(" - meters:\n");
  for (auto it : things->meter_name2id) {
    clinfo("\t[%d] %s\n", it.second, it.first.c_str());
  }
  clinfo(" - strings:\n");
  for (auto it : things->string_name2id) {
    clinfo("\t[%d] %s\n", it.second, it.first.c_str());
  }
  clinfo(" - tables:\n");
  for (auto it = things->tables_to_add.begin(); it != things->tables_to_add.end(); ++it) {
    clinfo("\t[%d] %s\n", it->second.id, it->second.name.c_str());
  }
}

int build_object_pool(const char *layout_dir, object_pool *op, YamlNode *config) {
  if (strlen(layout_dir) == 0) {
    layout_dir = ".";
  }

  if (things != NULL) {
    delete things;
    things = NULL;
  }
  things = new object_reference();

  YamlNode *node;

  /* Build up the maps of the top level components that compound components might use ---------------- */

  /* set up our fonts */
  if (!set_fonts(op, config, things)) {
    clerr("Failed to set fonts\n");
    return 0;
  }

  /* set up our line attributes */
  if (!set_line_attributes(op, config, things)) {
    clerr("Failed to set line attributes\n");
    return 0;
  }

  /* set up our fill attributes */
  if (!set_fill_attributes(op, config, things)) {
    clerr("Failed to set fill attributes\n");
    return 0;
  }

  /* set up our images */
  if (!set_images(layout_dir, op, config, things)) {
    clerr("Failed to set images\n");
    return 0;
  }

  /* Configure our main working set - This must be the first thing we actually upload --------------- */
  int working_set_object_id = set_working_set(op, config, things);
  if (!working_set_object_id) {
    clerr("Failed to build working set.\n");
    return 0;
  }

  /* Upload datamasks and match and named to their preset ids - this must happen before strings,
     lines, rectangles, buttons --------------------------------------------------------------------- */
  if (!set_data_masks(op, config, things)) {
    clerr("Failed to write data masks.\n");
    return 0;
  }

  /* Upload soft key masks and match and named to their preset ids - this must happen before strings,
     lines, rectangles, buttons --------------------------------------------------------------------- */
  if (!set_softkey_masks(op, config, things)) {
    clerr("Failed to write soft key masks.\n");
    return 0;
  }

  /* Update and configure elements which can also have layout - IE layout must still be parsed so this
     must happen before strings, lines, rectangles -------------------------------------------------- */
  for (ISOContainer c : things->containers_to_add_and_parse) {
    if (!set_container(op, c, things)) {
      clerr("Failed to set container\n");
      return 0;
    }
  }
  for (ISOButton b : things->buttons_to_add_and_parse) {
    if (!set_button(op, b, things)) {
      clerr("Failed to set button\n");
      return 0;
    }
  }

  for (ISOInputList l : things->input_lists_to_add_and_parse) {
    if (!set_input_list(op, l, things)) {
      clerr("Failed to set input list\n");
      return 0;
    }
  }

  /* Upload the low level objects that have no dependencies, or have been fulfilled already --------- */

  /* Add all images */
  for (auto it : things->images_to_add) {
    ISOImage *i = &it.second;

    int width = i->resize_width;
    int height = i->resize_height;

    unsigned char *bytes = open_image(i->val.c_str(), &width, &height);
    if (bytes == nullptr) {
      clerr("failed to load %s\n", i->val.c_str());
      return 0;
    }
    add_picture_graphic(op, i->id, bytes, width, height);
    free(bytes);
  }

  /* Add all strings */
  for (ISOString s : things->strings_to_add) {
    add_string(op, s.id, s.var_id, s.val.c_str(), 480, 480, s.font_id, s.justification);
    if (s.var_id != NULL_OBJECT_ID) {
      add_string_var(op, s.var_id, s.val.c_str());
    }
  }

  /* Add all font attributes */
  for (auto it : things->fonts_to_add) {
    ISOFont *f = &it.second;
    add_font_attributes(op, f->id, f->color, f->size, f->style);
  }

  /* Add all line attributes */
  for (auto it : things->line_attributes_to_add) {
    ISOLineAttributes *la = &it.second;
    add_line_attributes(op, la->id, la->color, la->thickness, la->pattern);
  }

  /* Add all the fill attributes */
  for (auto it : things->fill_attributes_to_add) {
    ISOFillAttributes *fa = &it.second;
    add_fill_attributes(op, fa->id, fa->color);
  }

  /* Add all rectangles */
  for (ISORectangle r : things->rectangles_to_add) {
    add_rectangle(op, r.id, r.line_attributes, r.fill_attributes, r.width, r.height);
  }

  /* Add all lines */
  for (ISOLine l : things->lines_to_add) {
    add_line(op, l.id, l.line_attributes, l.width, l.height, l.direction);
  }

  /* Add all checkboxes */
  for (ISOCheckbox cb : things->checkboxes_to_add) {
    add_checkbox(op, cb.id, cb.font_id, cb.bg_color, cb.width);
  }

  /* Add all meters */
  for (ISOMeter m : things->meters_to_add) {
    add_meter(op, m.id, m.width, m.needle_color, m.border_color, m.arc_color, m.options, m.ticks_count, m.start_angle,
              m.end_angle, m.min_value, m.max_value, m.value);
  }

  /* Add all UI tables */
  for (auto table = things->tables_to_add.begin(); table != things->tables_to_add.end(); ++table) {
    add_table(op, &(table->second), things);
  }

  print_all_paths();
  return working_set_object_id;
}

extern "C" {
const char *button_str_by_id(int id) {
  unordered_map<int, std::string>::const_iterator got = things->button_id2name.find(id);
  if (got == things->button_id2name.end()) {
    return "";
  }
  return got->second.c_str();
}

int button_id_by_str(const char *name) {
  unordered_map<std::string, int>::const_iterator got = things->button_name2id.find(name);
  if (got == things->button_name2id.end()) {
    return -1;
  }
  return got->second;
}

int datamask_id_by_str(const char *name) {
  unordered_map<std::string, int>::const_iterator got = things->datamask_name2id.find(name);
  if (got == things->datamask_name2id.end()) {
    return -1;
  }
  return got->second;
}

const char *input_list_str_by_id(int id) {
  unordered_map<int, std::string>::const_iterator got = things->input_list_id2name.find(id);
  if (got == things->input_list_id2name.end()) {
    return "";
  }
  return got->second.c_str();
}

const char *input_list_select_str_by_index(int id, int index) {
  unordered_map<int, unordered_map<int, string>>::const_iterator got = things->input_list_id2values.find(id);
  if (got == things->input_list_id2values.end()) {
    return "";
  }
  unordered_map<int, string>::const_iterator vgot = got->second.find(index);
  if (vgot == got->second.end()) {
    return "";
  }
  return vgot->second.c_str();
}

int string_id_by_str(const char *name) {
  auto got = things->string_name2id.find(name);
  if (got == things->string_name2id.end()) {
    return -1;
  }
  return got->second;
}

int string_var_id_by_str(const char *name) {
  auto got = things->string_var_name2id.find(name);
  if (got == things->string_var_name2id.end()) {
    return -1;
  }
  return got->second;
}

int meter_id_by_str(const char *name) {
  auto got = things->meter_name2id.find(name);
  if (got == things->meter_name2id.end()) {
    return -1;
  }
  return got->second;
}

int font_id_by_str(const char *fontname) { return things->font_by_name(fontname)->id; }

int color_index(const char *val) { return color_index_from_chars(val); }

int trigger_ui_button(int key, int value) {
  auto got = things->ui_button_map.find(key);
  if (got == things->ui_button_map.end()) {
    return 0;
  }
  // found it, dispatch with the table and key
  got->second.second(got->second.first, key, value);
  return 1;
}

void set_table_cell(const char *name, int row, int col, const char *value) {
  auto it = things->tables_to_add.find(name);
  if (it == things->tables_to_add.end()) {
    clerr("Table not found for set table cell\n");
    dispatch_wakeup();
    return;
  }
  if (col > it->second.numcols) {
    clerr("Cell column request outside bounds\n");
    dispatch_wakeup();
    return;
  }

  if (!it->second.set_cell(row, col, value)) {
    /* Only do this next part if no redraw necessary */
    dispatch_wakeup();
    return;
  }
}

void append_table_row(const char *name, const char *cols[], int len) {
  auto it = things->tables_to_add.find(name);
  if (it == things->tables_to_add.end()) {
    clerr("Table not found for append table row\n");
    dispatch_wakeup();
    return;
  }
  if (len != it->second.numcols) {
    clerr("append_table_row called with incorrect collumn length on table %s\n", name);
    dispatch_wakeup();
    return;
  }

  int row = it->second.num_store_rows;
  bool nowake = false;
  for (int i = 0; i < len; i++) {
    if (it->second.set_cell(row, i, cols[i])) {
      nowake = true;
    }
  }
  if (!nowake) {
    dispatch_wakeup();
  }
}

void delete_table_row(const char *name, int row) {
  auto it = things->tables_to_add.find(name);
  if (it == things->tables_to_add.end()) {
    clerr("Table not found for delete table row\n");
    dispatch_wakeup();
    return;
  }

  bool nowake = it->second.delete_row(row);
  if (!nowake) {
    dispatch_wakeup();
  }
}

void clear_table_cell(const char *name, int row, int col) {
  auto it = things->tables_to_add.find(name);
  if (it == things->tables_to_add.end()) {
    clerr("Table not found for set table cell\n");
  }
}

} // extern C

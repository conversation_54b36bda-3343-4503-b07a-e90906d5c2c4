#ifndef _OP_BUFFER_H
#define _OP_BUFFER_H

#include <stdint.h>

typedef struct transport_buffer {
  /* for transport */
  unsigned char *pbuf;
  int size;
  int used;
  int next_packet;
  int transferred;
  int packets_sent;
} transport_buffer;

#define DEFAULT_OPB_SIZE 32

/* ISOBUS defines */
#define NULL_OBJECT_ID 0xffff
#define WORKING_SET_TYPE 0
#define DATA_MASK_TYPE 1
#define CONTAINER_TYPE 3
#define STRING_TYPE 11
#define FONT_ATTRIBUTES_TYPE 23
#define PICTURE_GRAPHIC_TYPE 20
#define SOFT_KEY_MASK_TYPE 4
#define KEY_TYPE 5

/* maintenance */

/* These are for embedded structures that which th control the size */
void opb_zero(transport_buffer *opb);
void opb_reset_sized(transport_buffer *opb, int size);

/* These are for single objects that want a default size from single step initialization */
transport_buffer *make_transport_buffer();
transport_buffer *make_transport_buffer_sized(int size);
void opb_init(transport_buffer *opb);

/* Everybody frees the same way */
void opb_free(transport_buffer *opb);

/* buffer adds */

/* data types */
void opb_add16(transport_buffer *opb, uint16_t v);
void opb_add8(transport_buffer *opb, uint8_t v);
void opb_add32(transport_buffer *opb, uint32_t v);
void opb_addbytes(transport_buffer *opb, const char *bytes, int len);

int opb_memory_needed(transport_buffer *opb);
int opb_transport_done(transport_buffer *opb);

/* isobus types */
void opb_add_data_mask(transport_buffer *opb, int object_id, int bg_color, int soft_key_mask_id, int objects);
void opb_add_string(transport_buffer *opb, int my_id, int var_id, const char *s, int width, int height,
                    uint16_t font_attributes, int justification);
void opb_add_string_var(transport_buffer *opb, int var_id, const char *s);
void opb_add_picture_graphic(transport_buffer *opb, int my_id, unsigned char *bytes, int width, int height);
void opb_add_font_attributes(transport_buffer *opb, int object_id, int color, int size, int style);
void opb_add_soft_key(transport_buffer *opb, int my_id, int bg_color, int objects);
void opb_add_soft_key_mask(transport_buffer *opb, int my_id, int bg_color, int *button_ids, int num_buttons);
void opb_add_line_attributes(transport_buffer *opb, int id, int color, int thickness, int pattern);
void opb_add_fill_attributes(transport_buffer *opb, int id, int color);
void opb_add_rectangle(transport_buffer *opb, int id, int line_attributes, int fill_attributes, int width, int height);
void opb_add_line(transport_buffer *opb, int id, int line_attributes, int width, int height, int direction);
void opb_add_button(transport_buffer *opb, int id, int width, int height, int bg_color, int border_color, int key_code,
                    int options, int objects);
void opb_add_container(transport_buffer *opb, int id, int width, int height, int objects);
void opb_add_input_list(transport_buffer *opb, int id, int width, int height, int options, int objects, int selected);
void opb_add_checkbox(transport_buffer *opb, int id, int font_id, int bg_color, int width);
void opb_add_meter(transport_buffer *opb, int id, int width, int needle_color, int border_color, int arc_color, int options,
                   int ticks_count, int start_angle, int end_angle, int min_value, int max_value, int value);

/* commands bigger than a packet */
void opb_set_string(transport_buffer *opb, int my_id, const char *s);

/* packetizing */
void opb_set_start_packet(transport_buffer *opb, int start);
int opb_next_packet(transport_buffer *opb, unsigned char *buf);

#endif // _OP_BUFFER_H

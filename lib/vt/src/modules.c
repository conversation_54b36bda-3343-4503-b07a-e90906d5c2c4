#include <sys/time.h>

#include "vt.h"

#include "network.h"
#include "isostate.h"
#include "tractor_status.h"
#include "tim.h"

/*
 * Initialization bootstrapping for the various isobus modules
 */
void modules_bootstrap(struct isobus_options *opts) {
  tractor_callback_init(opts);
  if (opts->options & DO_TIM) {
    tim_init(opts->certificate_dir);
  }
  if (opts->options & DO_TECU) {
    tecu_init();
  }
}

#include "ui_table.h"

extern "C" {
#include "isostate.h"
}

using namespace std;

TableRow::TableRow(int x) : num_cols(0), row(nullptr) {}

TableRow::TableRow() : num_cols(0), row(nullptr) {}

void TableRow::copy(const TableRow &other) {
  num_cols = other.num_cols;
  if (num_cols > 0) {
    row = new std::string[num_cols];
    for (int i = 0; i < num_cols; i++) {
      row[i] = other.row[i];
    }
  }
}

TableRow::TableRow(const TableRow &other) : row(nullptr) { copy(other); }

TableRow &TableRow::operator=(const TableRow &other) {
  copy(other);
  return *this;
}

TableRow::~TableRow() {
  if (this->row) {
    delete[] this->row;
  }
}

void TableRow::set_cols(int col_count) {
  num_cols = col_count;
  if (row != nullptr) {
    delete[] row;
  }
  row = new std::string[num_cols];
}

void TableRow::set_col(int col, const char *value) { row[col] = std::string(value); }

UITable::UITable(int id, string name, int width, int height, int line_attributes, int fill_attributes,
                 int header_fill_attributes, int header_height, int display_rows, bool hide_row_dividers,
                 const ISOFont *font, YamlNode *columns, YamlNode *upscroll, YamlNode *downscroll)
    : id(id), name(name), width(width), height(height), line_attributes(line_attributes),
      fill_attributes(fill_attributes), header_fill_attributes(header_fill_attributes), header_height(header_height),
      display_rows(display_rows), hide_row_dividers(hide_row_dividers), font(font), columns(columns),
      upscroll(upscroll), downscroll(downscroll), display_start_row(0), scrollbar_id(0) {
  numcols = 1;
  if (columns != nullptr) {
    numcols = columns->list()->size();
  }
  display_cells = new int[display_rows * numcols];
  store_rows = nullptr;
  num_store_rows = 0;
}

UITable::UITable(const UITable &other) {
  id = other.id;
  name = other.name;
  width = other.width;
  height = other.height;
  line_attributes = other.line_attributes;
  fill_attributes = other.fill_attributes;
  header_fill_attributes = other.header_fill_attributes;
  header_height = other.header_height;
  display_rows = other.display_rows;
  hide_row_dividers = other.hide_row_dividers;
  display_start_row = other.display_start_row;
  font = other.font;
  columns = other.columns;
  upscroll = other.upscroll;
  downscroll = other.downscroll;
  numcols = other.numcols;
  display_cells = new int[display_rows * numcols];
  memcpy(display_cells, other.display_cells, sizeof(int) * display_rows * numcols);
  store_rows = new TableRow[display_rows];
  num_store_rows = other.num_store_rows;
}

UITable::~UITable() {
  delete[] display_cells;
  delete[] store_rows;
}

bool UITable::set_cell(int row, int col, const char *value) {
  /* return code is true if a iso request went out */
  bool sent_packet = false;

  /* If we are adding new rows ... */
  if (row >= num_store_rows) {
    TableRow *new_store_rows = new TableRow[row + 1];
    for (int i = 0; i < num_store_rows; i++) {
      new_store_rows[i] = store_rows[i];
    }
    for (int i = num_store_rows; i < row + 1; i++) {
      new_store_rows[i].set_cols(numcols);
    }
    delete[] store_rows;
    store_rows = new_store_rows;
    num_store_rows = row + 1;

    if (scrollbar_id) {
      update_scrollbar_height();
      sent_packet = true;
    }
  }
  store_rows[row].set_col(col, value);

  /* If we are changing (or adding) a row in our display area ... */
  if (row >= display_start_row && row < display_start_row + display_rows) {
    int string_id = display_cells[col + (row - display_start_row) * numcols];
    set_string_id_value(string_id, value);
    sent_packet = true;
  }

  return sent_packet;
}

bool UITable::delete_row(int row) {
  bool sent_packet = false;
  if (row < num_store_rows) {
    for (int i = row + 1; i < num_store_rows; i++) {
      store_rows[i - 1] = store_rows[i];
    }
    num_store_rows--;
    if (scrollbar_id) {
      update_scrollbar_height();
      sent_packet = true;
    }
    if (row < display_start_row + display_rows) {
      redraw(USER);
      sent_packet = true;
    }
  }
  return sent_packet;
}

int UITable::get_scrollbar_height() {
  int maxh = scrollbar_ymax - scrollbar_ymin;
  if (num_store_rows <= display_rows) {
    return maxh;
  }
  return (int)((display_rows / (float)num_store_rows) * maxh);
}

void UITable::update_scrollbar_height() {
  int scrollbar_h = get_scrollbar_height();
  set_object_id_size(scrollbar_id, scrollbar_w, scrollbar_h);
}

void UITable::update_scrollbar_position() {
  int scrollbar_h = get_scrollbar_height();
  int scroll_area_h = scrollbar_ymax - scrollbar_ymin;
  int scroll_row_chunk = (scroll_area_h - scrollbar_h) / display_rows;
  int newy = scrollbar_ymin + display_start_row * scroll_row_chunk;
  int yoff = newy - scrollbar_y;
  scrollbar_y = scrollbar_y + yoff;

  set_object_id_position_relative_typed(id, scrollbar_id, 0, yoff, UI_ELEMENT);
}

void UITable::redraw(enum ui_request_type type) {
  update_scrollbar_position();
  for (int i = display_start_row; i < display_start_row + display_rows; i++) {
    if (i < num_store_rows) {
      for (int j = 0; j < numcols; j++) {
        set_string_id_value_typed(display_cells[(i - display_start_row) * numcols + j], store_rows[i].row[j].c_str(),
                                  type);
      }
    } else {
      for (int j = 0; j < numcols; j++) {
        set_string_id_value_typed(display_cells[(i - display_start_row) * numcols + j], "", type);
      }
    }
  }
}

void UITable::press_upscroll() {
  if (display_start_row > 0) {
    display_start_row--;
    redraw(UI_ELEMENT);
  }
}

void UITable::press_downscroll() {
  if (display_start_row + display_rows < num_store_rows) {
    display_start_row++;
    redraw(UI_ELEMENT);
  }
}

#include <stddef.h>

#include "logging.h"
#include "can.h"
#include "network.h"

#include "ack_nack.h"

struct ack_nack_cb {
  int pgn;
  fack_nack cb;
};

struct ack_nack_cb ack_nacks[512];
static int cbcount = 0;

void register_ack_nack(int pgn, fack_nack cb) {
  ack_nacks[cbcount].pgn = pgn;
  ack_nacks[cbcount].cb = cb;
  cbcount++;
}

void handle_ack_nack(sock_pair *ss, isopacket *p, unsigned char *data, int data_len, struct timeval *now) {
  print_ack_message(DEBUG, p->sa, data);
  for (int i = 0; i < cbcount; i++) {
    cldebug("checking acknack for %d / %d\n", ack_nacks[i].pgn, *(uint16_t*)&data[5]);
    if (ack_nacks[i].pgn == *(uint16_t*)&data[5]) {
      ack_nacks[i].cb(ss, p, data, data_len, now);
    }
  }
}

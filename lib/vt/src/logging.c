#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include <sys/time.h>
#include <time.h>

#include "logging.h"

struct log_info {
  enum log_level lvl;
  FILE *out;
  int line_continue;
  int allow_escapes;
};

struct log_info g_iso_log = {.lvl = INFO, .out = NULL, .line_continue = 0, .allow_escapes = 1};

enum log_level get_logging_level() {
  return g_iso_log.lvl;
}

void set_logging_level(enum log_level lvl) {
  g_iso_log.lvl = lvl;
}

void kill_escapes() {
  g_iso_log.allow_escapes = 0;
}

int allow_escapes() {
  return g_iso_log.allow_escapes;
}

int clg_init(enum log_level level, const char *filename) {

  g_iso_log.lvl = level;
  if (!strcmp(filename, "-")) {
    g_iso_log.out = stdout;
  } else {
    g_iso_log.out = fopen(filename, "a");
  }
  if (g_iso_log.out == NULL) {
    return -1;
  }
  return 0;
}

const char *lvl_str(enum log_level level) {
  switch (level) {
  case OFF:
    return "OFF";
  case ERROR:
    return "ERROR";
  case WARN:
    return "WARN";
  case INFO:
    return "INFO";
  case DEBUG:
    return "DEBUG";
  case DEBUG_PROTO:
    return "DEBUG_PROTO";
  case DEBUG_PROTO_ESCAPES:
    return "DEBUG_PROTO_ESCAPES";
  default:
    return "<unkown log level>";
  }
}

void log_preamble(FILE *out, enum log_level level) {
  struct timeval tv;
  time_t nowtime;
  struct tm *nowtm;
  char tmbuf[64], buf[128];

  gettimeofday(&tv, NULL);
  nowtime = tv.tv_sec;
  nowtm = localtime(&nowtime);
  strftime(tmbuf, sizeof tmbuf, "%Y-%m-%d::%H:%M:%S", nowtm);
  snprintf(buf, sizeof buf, "%s.%06ld", tmbuf, (long)tv.tv_usec);
  fprintf(out, "%s %s ", buf, lvl_str(level));
}

void vclg(enum log_level level, const char *fmt, va_list ap) {
  if (level > g_iso_log.lvl) {
    return;
  }
  if (!g_iso_log.line_continue) {
    log_preamble(g_iso_log.out, level);
  }
  vfprintf(g_iso_log.out, fmt, ap);
  if (fmt[strlen(fmt) - 1] == '\n') {
    g_iso_log.line_continue = 0;
  } else {
    g_iso_log.line_continue = 1;
  }
  fflush(g_iso_log.out);
}

void clg(enum log_level level, const char *fmt, ...) {
  va_list ap;
  va_start(ap, fmt);
  vclg(level, fmt, ap);
  va_end(ap);
}

void clinfo(const char *fmt, ...) {
  va_list ap;
  va_start(ap, fmt);
  vclg(INFO, fmt, ap);
  va_end(ap);
}

void cldebug(const char *fmt, ...) {
  va_list ap;
  va_start(ap, fmt);
  vclg(DEBUG, fmt, ap);
  va_end(ap);
}

void cldebug_proto(const char *fmt, ...) {
  va_list ap;
  va_start(ap, fmt);
  vclg(DEBUG_PROTO, fmt, ap);
  va_end(ap);
}

void clwarn(const char *fmt, ...) {
  va_list ap;
  va_start(ap, fmt);
  vclg(WARN, fmt, ap);
  va_end(ap);
}

void clerr(const char *fmt, ...) {
  va_list ap;
  va_start(ap, fmt);
  vclg(ERROR, fmt, ap);
  va_end(ap);
}

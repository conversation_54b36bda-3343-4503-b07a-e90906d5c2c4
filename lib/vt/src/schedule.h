#ifndef _SCHEDULE_H
#define _SCHEDULE_H
#include <sys/time.h>

typedef void (*freadcb)(sock_pair *, struct timeval *now);
typedef void (*ftcb)(sock_pair *, struct timeval *scheduled, struct timeval *now);
typedef void (*fwake)(struct timeval *now, void *arg);

typedef struct wake_info {
  int p;
  fwake wake_cb;
  void *arg;
} wake_info;

void add_sec(struct timeval *tv, long sec);
void add_usec(struct timeval *tv, long usec);
void add_msec(struct timeval *tv, long msec);

void run_scheduler(sock_pair *ss, freadcb readcb, wake_info *wakes, int num_wakes);
void schedule_callback(ftcb, struct timeval *when);
void schedule_relative(ftcb, struct timeval *when, long sec, long usec);
struct timeval getnow();

#endif /* _SCHEDULE_H */

#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <termios.h>
#include <unistd.h>

#include <arpa/inet.h>

#ifdef HAS_LINUX_CAN
#include <linux/can.h>
#endif

#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/types.h>

#include "can.h"
#include "object_pool.h"
#include "network.h"
#include "schedule.h"
#include "can_gateway.h"
#include "tim.h"
#include "tim_packet_print.h"
#include "tecu_packet.h"
#include "isostate.h"

#define DATA_OFFSET 4

// NAME info

struct name_info {
  int set;
  int self_assigned_address;
  int industry_group;
  int device_class_instance;
  int device_class;
  int function;
  int function_instance;
  int ecu_instance;
  int manufacturer_code;
  int identity_number;
} g_name_info = {.set = 0};

#define PUT16(_buf, _val) *(uint16_t *)_buf = _val
#define GET16(_buf) *(uint16_t *)_buf

int can_received_packet_counter = 0;

typedef struct can_callback {
  int pgn;
  int addr_check;
  fcanpacket f;
} can_callback;

can_callback callbacks[512];
fwake wake_cb = NULL;
int registered_callbacks = 0;
int my_addr = 0; // we may have multiple of these at some point
fcanpacket my_addr_callback = NULL;

static struct can_opts can_opts;

int encode_bindata(unsigned char *databuf, isopacket *pac, int datalen) {
  databuf[0] = 0;
  databuf[0] |= pac->priority << 2;
  databuf[0] |= pac->edp << 1;
  databuf[0] |= pac->dp;

  databuf[1] = pac->pf;

  databuf[2] = pac->ps;

  databuf[3] = pac->sa;

  memcpy(&databuf[DATA_OFFSET], pac->data, datalen);
  return datalen + 4;
}

void print_canhex(enum log_level level, unsigned char *p, int len) {
  clg(level, "[%d]:", len);
  for (int i = 0; i < len; i++) {
    clg(level, "%.2x", p[i]);
    if (i == 3) {
      clg(level, "#");
    }
  }
  clg(level, " ");
}

void print_hexdata(enum log_level level, unsigned char *p, int len) {
  for (int i = 0; i < len; i++) {
    clg(level, "%.2x", p[i]);
  }
}

int encode_candata(unsigned char *data, isopacket *pac) {
  uint32_t header32 = 0;
  uint32_t control;
  unsigned char *header = (unsigned char *)&header32;

  header[0] |= pac->sof << 7;
  header[0] |= pac->priority << 2;
  header[0] |= pac->edp << 1;
  header[0] |= pac->dp;

  header[1] = pac->pf;
  header[2] = pac->ps;
  header[3] = pac->sa;

  header32 = htonl(header32);
  *((uint32_t *)data) = header32;

  control = pac->data_len;

  *(uint32_t *)(data + sizeof(uint32_t)) = control;

  memcpy(data + 2 * sizeof(uint32_t), pac->data, pac->data_len);

  return pac->data_len + 2 * sizeof(uint32_t);
}

int dest_addr(isopacket *p) {
  return p->ps;
}

void set_isobus_name(int self_assigned, int industry_group, int device_class_instance, int device_class,
                     int function, int function_instance, int ecu_instance, int manufacturer_code,
                     int identity_number) {
  g_name_info.set = 1;
  g_name_info.self_assigned_address = self_assigned;
  g_name_info.industry_group = industry_group;
  g_name_info.device_class_instance = device_class_instance;
  g_name_info.device_class = device_class;
  g_name_info.function = function;
  g_name_info.function_instance = function_instance;
  g_name_info.ecu_instance = ecu_instance;
  g_name_info.manufacturer_code = manufacturer_code;
  g_name_info.identity_number = identity_number;
}

void set_pgn(isopacket *pac) {
  /* ISO783-3 PG6
   * The procedure for the bit fields to be converted to PGN is as follows. The six MSB of the PGN are set
   * to zero. Then the Extended Data Page bit, Data Page bit and PDU Format field are copied into the next
   * 10 bit. If the PF value is less than 240 (F016) then the LSB of the PGN is set to zero. Otherwise, it
   * is set to the value of the PS Field
   */
  pac->pgn = (pac->pf << 8) | (pac->dp << 16) | (pac->edp << 17);
  if (pac->pf >= 240) { // only for setting the pgn
    pac->pgn |= pac->ps;
  }
}

int parse_candata(isopacket *pac, unsigned char *header, unsigned char *data, int len) {
  pac->sof = (header[0] & 0x80) >> 7;
  pac->priority = (header[0] & 0b11100) >> 2;

  pac->edp = (header[0] & 0b10) >> 1;
  pac->dp = (header[0] & 0b1);

  pac->pf = header[1];
  pac->ps = header[2];
  pac->sa = header[3];

  set_pgn(pac);

  memcpy(pac->data, data, len);
  pac->data_len = len;
  return 0;
}

void print_binary(enum log_level level, unsigned char b) {
  for (int i = 7; i >= 0; i--) {
    if (b & (1 << i)) {
      clg(level, "1");
    } else {
      clg(level, "0");
    }
  }
}

void print_binary_chars(enum log_level level, unsigned char *d, int count) {
  for (int i = 0; i < count; i++) {
    print_binary(level, d[i]);
    clg(level, " ");
  }
}

isopacket *make_isopacket() { return (isopacket *)malloc(sizeof(isopacket)); }

int make_name(char data[8], struct name_info *ni) {
  memset(data, 0, 8);

  data[7] |= ni->self_assigned_address << 7;
  data[7] |= ni->industry_group << 4;
  data[7] |= ni->device_class_instance;

  data[6] |= ni->device_class << 1;

  data[5] = ni->function;

  data[4] |= ni->function_instance << 3;
  data[4] |= ni->ecu_instance;

  uint16_t mfc_bits = ni->manufacturer_code;

  data[3] = (mfc_bits & 0x7f8) >> 3;
  data[2] = (mfc_bits & 0x7) << 5;

  uint32_t idn_bits = ni->identity_number;

  data[2] |= idn_bits >> 16;
  data[1] = idn_bits >> 8;
  data[0] = idn_bits;

  clinfo("NAME: ");
  print_hexdata(INFO, (unsigned char *)data, 8);
  clinfo("\n");
  return 8;
}

int vt_function(unsigned char *data) { return data[0]; }

void print_vt_msg(enum log_level level, unsigned char sa, unsigned char *pdata) {
  unsigned char vt_function = pdata[0];

  switch (vt_function) {
  case POINTING_FUNCTION:
    print_pointing_message(level, sa, pdata);
    break;
  case VT_STATUS_FUNCTION:
    clg(level, "\tVT Status\n");
    unsigned char source_addr = pdata[1];
    clg(level, "\tWS Master Address: %d [0x%x]\n", source_addr, source_addr);
    unsigned int visible_data_object_id = *(uint16_t *)&pdata[2];
    clg(level, "\tVisible Data Mask: %d [0x%x]\n", visible_data_object_id, visible_data_object_id);
    unsigned int visible_soft_key_mask_object_id = *(uint16_t *)&pdata[4];
    clg(level, "\tVisible Soft Key Mask: %d [0x%x]\n", visible_soft_key_mask_object_id,
        visible_soft_key_mask_object_id);
    unsigned char vt_busy_codes = pdata[6];
    clg(level, "\tvt busy codes: ");
    print_binary(level, vt_busy_codes);
    clg(level, "\n");
    unsigned char vt_function_code = pdata[7];
    clg(level, "\tVT function code: %d [0x%x]\n", vt_function_code, vt_function_code);
    break;
  case GET_MEMORY_FUNCTION:
    clg(level, "\tGet Memory Response\n");
    clg(level, "\tVersion Number: %d\n", pdata[1]);
    if (!pdata[2]) {
      clg(level, "\tCan support this allocation.\n");
    } else {
      clg(level, "\tNot enough memory.\n");
    }
    break;
  case OBJECT_POOL_DONE_FUNCTION:
    print_end_of_object_pool_response(level, sa, pdata);
    break;
  case BUTTON_ACTIVATION_FUNCTION:
    print_button_activation_message(level, sa, pdata);
    break;
  case SOFTKEY_ACTIVATION_FUNCTION:
    print_softkey_activation_message(level, sa, pdata);
    break;
  case CHANGE_NUMERIC_VALUE_FUNCTION:
    print_change_numeric_value_message(level, sa, pdata);
    break;
  case CHANGE_ATTRIBUTE_FUNCTION:
    print_change_attribute_message(level, sa, pdata);
    break;
  case CHANGE_CHILD_LOCATION_FUNCTION:
    print_change_child_location_message(level, sa, pdata);
    break;
  case CHANGE_STRING_VALUE_COMMAND:
    print_change_string_value_message(level, sa, pdata);
    break;
  case CHANGE_OBJECT_SIZE_FUNCTION:
    print_change_object_size_message(level, sa, pdata);
    break;
  default:
    clg(level, "Unknown VT Message %d\n", vt_function);
    break;
  }
}

const char *request_str(int pgn) {
  static char buf[1024];

  switch (pgn) {
    case ADDRESS_CLAIMED_PGN:
      snprintf(buf, 1024, "REQUEST - ADDRESS CLAIMED");
      return buf;

    case ISOBUS_COMPLIANCE_CERTIFICATION_PGN:
      return "ISOBUS_COMPLIANCE_CERTIFICATION_PGN";

    default:
      return "UNKNOWN REQUEST PGN";
  }
}

void print_request_message(enum log_level level, unsigned char sa, unsigned char ps, unsigned char *data) {
  int pgn = get_request_pgn(data);
  clg(level, "Request message: %d -> %d - PGN %d [0x%x], %s\n", sa, ps, pgn, pgn, request_str(pgn));
}

void print_isobus_compliance_message_v3(enum log_level level,
                                        unsigned char sa, unsigned char ps, unsigned char *data) {
  int protocol_revision;
  int publication_year;
  int labid;
  int labtype;
  int refnum;
  protocol_revision = (data[0] & ((1 >> 7) | (1 << 6))) >> 6;
  publication_year = data[0] & 0x3f;
  labid = data[1] >> 5;
  protocol_revision |= data[1] & ((1 << 4) | (1 << 3));
  labtype = (data[1] & ((1 << 2) | ( 1 << 1))) >> 1;
  protocol_revision |= (data[1] & 0x1) << 2;
  labid |= ((int)data[2]) << 3;
  refnum = ntohs(*(uint16_t *)&data[6]);

  clg(level, "ISOBUS compliance certification message v3: %d -> %d\n", sa, ps);
  clg(level, "\tprotocol_revison: %d, publication_year: %d, labid: %d, labtype: %d, refnum: %d\n",
      protocol_revision, publication_year, labid, labtype, refnum);
}

void print_isobus_compliance_message_v2(enum log_level level,
                                        unsigned char sa, unsigned char ps, unsigned char *data) {
  int protocol_revision;
  int publication_year;
  int labid;
  int labtype;
  int refnum;
  protocol_revision = (data[0] & ((1 >> 7) | (1 << 6))) >> 6;
  publication_year = data[0] & 0x3f;
  labid = (data[1] & ((1 << 7) | (1 << 6) | (1 << 5))) >> 5;
  // protocol_revision |= data[1] & ((1 << 4) | (1 << 3));
  labtype = (data[1] & ((1 << 2) | ( 1 << 1))) >> 1;
  protocol_revision |= (data[1] & 0x1) << 2;
  labid |= ((int)data[2]) << 2;
  refnum = ntohs(*(uint16_t *)&data[6]);

  clg(level, "ISOBUS compliance certification message v2: %d -> %d\n", sa, ps);
  clg(level, "\tprotocol_revison: %d, publication_year: %d, labid: %d, labtype: %d, refnum: %d\n",
      protocol_revision, publication_year, labid, labtype, refnum);
  clg(level, "byte 4 bits:");
  print_binary(level, data[3]);
  clg(level, ", byte 5 bits:");
  print_binary(level, data[4]);
  clg(level, "\n");
}

void print_isobus_compliance_message(enum log_level level, unsigned char sa, unsigned char ps, unsigned char *data) {
  int message_revision;
  message_revision = data[5] >> 7;
  if (message_revision) {
    print_isobus_compliance_message_v3(level, sa, ps, data);
  } else {
    print_isobus_compliance_message_v2(level, sa, ps, data);
  }
}

void print_address_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tAddress Claimed: %d\n", sa);
  clg(level, "\t");
  print_canhex(level, pdata, 8);
  clg(level, "\n");
}

int can_support_memory(unsigned char *pdata) { return !pdata[2]; }

void print_get_memory_response_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tGet Memory Response: %d\n", sa);
  clg(level, "\tVersion Number: %d\n", pdata[1]);
  if (can_support_memory(pdata)) {
    clg(level, "\tCan support this allocation.\n");
  } else {
    clg(level, "\tNot enough memory.\n");
  }
}

void hardware_vt_version(unsigned char *pdata, int *version) { *version = pdata[1]; }

void print_get_hardware_response_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tGet Hardware Response: %d\n", sa);
  clg(level, "\tMax boot time seconds: %d\n", pdata[1]);

  clg(level, "\tSupported graphic modes: ");
  print_binary(level, pdata[2]);
  clg(level, "\n");

  clg(level, "\tHardware: ");
  print_binary(level, pdata[3]);
  clg(level, "\n");

  clg(level, "\tX-Pixels: %d\n", *(uint16_t *)&pdata[4]);
  clg(level, "\tY-Pixels: %d\n", *(uint16_t *)&pdata[6]);
}

void hardware_datamask_resolution(unsigned char *pdata, int *width, int *height) {
  *width = *(uint16_t *)&pdata[4];
  *height = *(uint16_t *)&pdata[6];
}

int ack_is_nack(unsigned char *pdata) { return pdata[0]; }

void print_ack_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tRESPONSE: %d\n", sa);
  clg(level, "\t%s\n", pdata[0] ? "NACK" : "ACK");
  clg(level, "\tGroup Function Value: 0x%x\n", pdata[1]);
  clg(level, "\tAddress NACK: %d\n", pdata[4]);
  int pgn = *(uint16_t *)&pdata[5];
  if (pgn == ECU2VT_PDU << 8 && &pdata[4]) {
    clg(level, "\tVT is NACKing your request to it [0x%x]\n", ECU2VT_PDU << 8);
  } else {
    clg(level, "\tPGN of requested: 0x%x\n", *(uint16_t *)&pdata[5]);
  }
}

void print_change_string_value_response_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tCHANGE STRING VALUE RESPONSE: %d\n", sa);
  clg(level, "\tobject id changed: %d\n", *(uint16_t *)&pdata[3]);
  clg(level, "\terror codes: ");
  print_binary(level, pdata[5]);
  clg(level, "\n");
}

void print_change_child_location_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tCHANGE CHILD LOCATION RESPONSE: %d\n", sa);
  clg(level, "\tparent object id: %d\n", GET16(&pdata[1]));
  clg(level, "\tobject id: %d\n", GET16(&pdata[3]));
  clg(level, "\terror codes: ");
  print_binary(level, pdata[5]);
  clg(level, "\n");
}

void print_change_object_size_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tCHANGE OBJECT SIZE RESPONSE: %d\n", sa);
  clg(level, "\tobject id: %d\n", GET16(&pdata[1]));
  clg(level, "\terror codes: ");
  print_binary(level, pdata[3]);
  clg(level, "\n");
}

void print_change_attribute_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tCHANGE_ATTRIBUTE_RESPONSE: %d\n", sa);
  clg(level, "\tobject id changed: %d\n", *(uint16_t *)&pdata[1]);
  clg(level, "\tattributed id: %d\n", pdata[3]);
  clg(level, "\terror codes: ");
  print_binary(level, pdata[4]);
  clg(level, "\n");
}

void print_end_of_msg_ack_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tEnd of transport ack from: %d\n", sa);
  clg(level, "\tTotal size: %d\n", *(uint16_t *)&pdata[1]);
  clg(level, "\tTotal packets: %d\n", pdata[3]);
  clg(level, "\tPGN of packeted message: 0x%x\n", *(uint16_t *)&pdata[5]);
}

void print_delete_object_pool_response(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tDelete object pool response error codes: ");
  print_binary(level, pdata[1]);
  clg(level, "\n");
}

void print_end_of_object_pool_response(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tObject Pool Done Response from %d\n", sa);
  clg(level, "\tError Codes: ");
  print_binary(level, pdata[1]);
  clg(level, "\n");

  clg(level, "\tParent Object ID of faulty: %d [0x%x]\n", *(uint16_t *)&pdata[2], *(uint16_t *)&pdata[2]);
  clg(level, "\tObject ID of faulty: %d [0x%x]\n", *(uint16_t *)&pdata[4], *(uint16_t *)&pdata[4]);

  clg(level, "\tObject Pool Error codes: ");
  print_binary(level, pdata[6]);
  clg(level, "\n");
}

void print_etp_conn_abort(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tETP Conn abort from %d [0x%x]\n", sa, sa);
  clg(level, "\tReason: %d\n", pdata[1]);
}

void print_tp_conn_abort(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tTP Conn abort from %d [0x%x]\n", sa, sa);
  clg(level, "\tReason: %d\n", pdata[1]);
}

void print_key_activation_code(enum log_level level, uint8_t code) {
  switch (code) {
  case 0:
    clg(level, "released\n");
    break;
  case 1:
    clg(level, "pressed\n");
    break;
  case 2:
    clg(level, "still held\n");
    break;
  case 3:
    clg(level, "aborted\n");
    break;
  default:
    clg(level, "%d -> UNKNOWN\n", code);
  }
}

void print_softkey_activation_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tsoftkey activation from %d [0x%x]\n", sa, sa);
  clg(level, "\t");
  print_key_activation_code(level, pdata[1]);
  clg(level, "\tobject id %d [0x%x]\n", *(uint16_t *)&pdata[2], *(uint16_t *)&pdata[2]);
  clg(level, "\tparent object id %d [0x%x]\n", *(uint16_t *)&pdata[4], *(uint16_t *)&pdata[2]);
  clg(level, "\tkey number: %d\n", pdata[6]);
}

void print_change_numeric_value_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tchange numeric value from %d [0x%x]\n", sa, sa);
  clg(level, "\tobject id %d [0x%x]\n", *(uint16_t *)&pdata[1], *(uint16_t *)&pdata[1]);
  clg(level, "\tlist index %d\n", pdata[4]);
}

void print_change_string_value_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tchange string value from %d [0x%x]\n", sa, sa);
  clg(level, "\tobject id: %d\n", GET16(&pdata[3]));
  clg(level, "\terror codes: ");
  print_binary(level, pdata[5]);
  clg(level, "\n");
}

void print_change_attribute_response(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tchange attribute response from %d [0x%x]\n", sa, sa);
  clg(level, "\tobject id %d [0x%x]\n", *(uint16_t *)&pdata[1], *(uint16_t *)&pdata[1]);
  clg(level, "\tattribute AID: %d\n", pdata[3]);
  clg(level, "\terror codes: ");
  print_binary(level, pdata[4]);
  clg(level, "\n");
}

int button_activation_id(unsigned char *pdata) { return *(uint16_t *)&pdata[2]; }

int button_activation_code(unsigned char *pdata) { return pdata[1]; }

int input_list_id(unsigned char *pdata) { return *(uint16_t *)&pdata[1]; }

int input_list_index(unsigned char *pdata) { return pdata[4]; }

void print_button_activation_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tbutton activation from %d [0x%x]\n", sa, sa);
  clg(level, "\t");
  print_key_activation_code(level, button_activation_code(pdata));
  clg(level, "\tobject id %d [0x%x]\n", button_activation_id(pdata), button_activation_id(pdata));
  clg(level, "\tparent object id %d [0x%x]\n", *(uint16_t *)&pdata[4], *(uint16_t *)&pdata[2]);
  clg(level, "\tkey number: %d\n", pdata[6]);
}

void print_pointing_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tpointing from %d [0x%x]\n", sa, sa);
  clg(level, "\tX-position: %d\n", *(uint16_t *)&pdata[1]);
  clg(level, "\tY-position: %d\n", *(uint16_t *)&pdata[3]);
}

void print_vehicle_position_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tvehicle position from %d [0x%x]\n", sa, sa);
  clg(level, "\tlatitude: %f\n", *(uint32_t *)&pdata[0] / 1e7 - 210.0f);
  clg(level, "\tlongitude: %f\n", *(uint32_t *)&pdata[4] / 1e7 - 210.0f);
}

void print_vehicle_speed_and_direction_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tvehicle speed and direction from %d [0x%x]\n", sa, sa);
  clg(level, "\theading: %.2f deg\n", *(uint16_t *)&pdata[0] / 128.0f);
  clg(level, "\tspeed: %.2f km/h\n", *(uint16_t *)&pdata[2] / 256.0f);
  clg(level, "\tpitch: %.2f deg\n", *(uint16_t *)&pdata[4] / 128.0f - 200.0f);
  clg(level, "\taltitude: %.2f m\n", *(uint16_t *)&pdata[6] * 0.125f - 2500.0f);
}

void print_address_claimed_message(enum log_level level, int sa, int ps, unsigned char *data) {
  clg(level, "Address claimed from %d to %d: ", sa, ps);
  print_hexdata(level, data, 8);
  clg(level, "\n");
}

void print_packet(enum log_level level, isopacket *pac, unsigned char *data, int data_len) {
  /* special case for filtering out all things not mine - the rest of this will take care of itself
     from level filtering */
  if (get_logging_level() == DEBUG_PROTO_MINE) {
    int dolog = 0;
    if (pac->pf >= 240 ||
        (pac->pf < 240 && // dest address is valid
        (pac->ps == get_iso_addr() || pac->ps == NULL_ADDRESS || pac->ps == GLOBAL_ADDRESS)) ||
        (pac->sa == get_iso_addr() || pac->sa == NULL_ADDRESS || pac->ps == GLOBAL_ADDRESS)) {
        dolog = 1;
    }
    if (!dolog) {
      return;
    }
  }
  clg(level, "[pac: %d -> %d]", pac->sa, pac->ps);
  print_canhex(level, data, data_len);
  if (pac->pf == J1939_MEMORY_ACCESS_REQUEST_PF) {
    clg(level, "\tMemory Access Request");
  } else {
    if (pac->pgn == ISOBUS_COMPLIANCE_CERTIFICATION_PGN) {
      print_isobus_compliance_message(level, pac->sa, pac->ps, data);
    } else if (pac->pgn == MACHINE_SELECTED_SPEED_PGN) {
      print_machine_selected_speed_packet(level, pac->sa, data);
    } else if (pac->pgn == WHEEL_BASED_SPEED_AND_DISTANCE_PGN) {
      print_wheel_speed_distance_message(level, pac->sa, data);
    } else if (pac->pgn == VEHICLE_POSITION_PGN) {
      print_vehicle_position_message(level, pac->sa, data);
    } else if (pac->pgn == VEHICLE_SPEED_AND_DIRECTION_PGN) {
      print_vehicle_speed_and_direction_message(level, pac->sa, data);
    } else if (pac->pgn == VT2ECU_PGN) {
      if (GET_MEMORY_FUNCTION == vt_function(data)) {
        print_get_memory_response_message(DEBUG, pac->sa, data);
      } else if (GET_HARDWARE_FUNCTION == vt_function(data)) {
        print_get_hardware_response_message(level, pac->sa, data);
      } else if (CHANGE_STRING_VALUE_COMMAND == vt_function(data)) {
        print_change_string_value_response_message(level, pac->sa, data);
      } else {
        print_vt_msg(level, pac->sa, data);
      }
    } else if (pac->pgn == TRANSPORT_PROTOCOL_PGN) {
      if (pac->data[0] == CM_CTS) {
        clg(level, "transport protocol clear to send\n");
      } else if (pac->data[0] == CM_RTS) {
        int pgn = pac->data[5] | (pac->data[6] << 8) | (pac->data[7] << 16);
        if (pgn == TIM_AUTH12_PGN) {
          clg(level, "transport rts for TIM_AUTH12\n");
        } else if (pgn == TIM_AUTH21_PGN) {
          clg(level, "transport rts for TIM_AUTH21\n");
        } else if (pgn == TIM_SERVER_2_TIM_CLIENT) {
          clg(level, "transport rts for TIM_SERVER_2_TIM_CLIENT\n");
        } else if (pgn == TIM_CLIENT_2_TIM_SERVER) {
          clg(level, "transport rts for TIM_CLIENT_2_TIM_SERVER\n");
        } else {
          clg(level, "transport protocol rts for pgn %d [0x%x]\n", pgn, pgn);
        }
      } else if (pac->data[0] == CM_EOM_ACK) {
        clg(level, "transport protocol eom ack\n");
      } else {
        clg(level, "transport protocol control byte: %d\n", pac->data[0]);
      }
    } else if (pac->pgn == TRANSPORT_PROTOCOL_DATA_TRANSPORT_PGN) {
      clg(level, "tranport protocol data transport:");
      print_hexdata(level, data, data_len);
      clg(level, "\n");
    } else if (pac->pgn == TIM_SERVER_2_TIM_CLIENT) {
      print_tim_server_2_client(level, pac->sa, data);
    } else if (pac->pgn == TIM_CLIENT_2_TIM_SERVER) {
      print_tim_client_2_server(level, pac->sa, data);
    } else if (pac->pgn == TIM_AUTH12_PGN) {
      print_tim_auth12(level, pac->sa, data);
    } else if (pac->pgn == TIM_AUTH21_PGN) {
      print_tim_auth21(level, pac->sa, data);
    } else if (pac->pgn == REQUEST_PGN) {
      print_request_message(level, pac->sa, pac->ps, data);
    } else if (pac->pgn ==  ADDRESS_CLAIMED_PGN) {
      print_address_claimed_message(level, pac->sa, pac->ps, data);
    } else {
      if (allow_escapes()) {
        clg(DEBUG_PROTO_ESCAPES, "sof: %d\n", pac->sof);
        clg(DEBUG_PROTO_ESCAPES, "priority: %d\n", pac->priority);
        clg(DEBUG_PROTO_ESCAPES, "edp (Extended Data Page): %d\n", pac->edp);
        clg(DEBUG_PROTO_ESCAPES, "dp (Data Page): %d\n", pac->dp);
        clg(DEBUG_PROTO_ESCAPES, "pf (PDU format #): %d [0x%x]\n", pac->pf, pac->pf);
        clg(DEBUG_PROTO_ESCAPES, "ps (PDU Specific #): %d [0x%x] [%s]", pac->ps, pac->ps,
            pac->pf < 240 ? "Destination Address" : "Group Extension");
        if (pac->pf < 240 && pac->ps == GLOBAL_ADDRESS) {
          clg(DEBUG_PROTO_ESCAPES, " -> GLOBAL\n");
        } else {
          clg(DEBUG_PROTO_ESCAPES, "\n");
        }
        clg(DEBUG_PROTO_ESCAPES, "sa (Source Address): %d [0x%x]\n", pac->sa, pac->sa);
        clg(DEBUG_PROTO_ESCAPES, "pgn: %d [0x%x]\n", pac->pgn, pac->pgn);
      } else {
        clg(level, "\n");
      }
      if (pac->pgn == 0xe600) {
        print_vt_msg(level, pac->sa, data);
      }
      if (pac->pgn == 0xee00) {
        print_address_message(level, pac->sa, data);
      }
    }
  }
}

int object_pool_error_codes(unsigned char *pdata) { return pdata[6]; }

int unhex(char c) {
  if (c >= '0' && c <= '9') {
    return c - '0';
  } else if (c >= 'a' && c <= 'f') {
    return c - 'a' + 10;
  } else if (c >= 'A' && c <= 'F') {
    return c - 'A' + 10;
  }
  return 0;
}

int dehex(char *pout, const char *pin) {
  int len = 0;
  while (*pin && *(pin + 1)) {
    if (*pin == '#') {
      pin++;
      continue;
    }
    *pout = (unhex(*pin) << 4) | unhex(*(pin + 1));
    pout++;
    len++;
    pin += 2;
  }
  return len;
}

void display_char_message(enum log_level level, const char *pdata) {
  int len;
  unsigned char bindata[4 * 8];
  isopacket packet;

  len = dehex((char *)bindata, (const char *)pdata);

  clg(level, "len: %d -> ", len);
  for (int i = 0; i < len; i++) {
    clg(level, "%.2x", bindata[i]);
  }
  clg(level, "\n");

  parse_candata(&packet, bindata, bindata + 4, len - 4);
  clg(DEBUG, "display char message!\n");
  print_packet(level, &packet, packet.data, packet.data_len);
}

#define CHAR_PACKET_LEN 60

char *strnchr(char *buf, int len, int c) {
  int at = 0;
  while (at < len) {
    if (buf[at] == c) {
      return &buf[at];
    }
    at++;
  }
  return NULL;
}

void common_packet(isopacket *p) {
  memset(p, 0, sizeof(*p));
  p->sof = 1;
}

/* Output CCID Information */
void isobus_certification_packet_v3(isopacket *p, int addr) {
  int protocol_revision = 1;
  int publication_year = 24;
  int refnum = 2;

  int labid = ISOBUS_TEST_CENTER;
  // int labid = CARBON_MANUFACTURER_CODE;
  int labtype = ISOBUS_CERTIFICATION_LAB_SELF;

  common_packet(p);
  p->priority = 6;
  p->pf = ISOBUS_COMPLIANCE_CERTIFICATION_PGN >> 8;
  p->ps = ISOBUS_COMPLIANCE_CERTIFICATION_PGN & 0xff;
  p->sa = addr;

  p->data[0] |= protocol_revision << 6;
  p->data[0] |= publication_year & 0x3f;

  p->data[1] |= labid << 5;
  p->data[1] |= protocol_revision & ((1 << 4) | (1 << 3));
  p->data[1] |= (labtype & 0x3) << 1;
  p->data[1] |= (protocol_revision >> 2) & 0x1;

  p->data[2] |= labid >> 3;

  p->data[3] = 0;
  p->data[4] = 0;

  p->data[5] = 1 << 7; // message revision, 1 == v3

  // *(uint16_t *)&(p->data[6]) = htons(refnum);
  *(uint16_t *)&(p->data[6]) = refnum;

  p->data_len = 8;
}

void ecu2vt_packet(isopacket *p, int addr, int vt_addr) {
  common_packet(p);
  p->priority = 5;
  p->pf = ECU2VT_PDU;
  p->ps = vt_addr;
  p->sa = addr;
}

void pgn_packet(isopacket *p, int pgn, int addr, int src_addr) {
  common_packet(p);
  p->sa = src_addr;
  p->pf = (pgn >> 8) & 0xff;
  if (p->pf < 240) {
    p->ps = addr;
  } else {
    p->ps = pgn & 0xff;
  }
}

void get_memory_packet(isopacket *p, int addr, int vt_addr, int memory) {
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = GET_MEMORY_FUNCTION;
  p->data[1] = 0xff;
  *(uint32_t *)(&p->data[2]) = memory;
  p->data[6] = 0xff;
  p->data[7] = 0xff;
  p->data_len = 8;
}

void get_hardware_packet(isopacket *p, int addr, int vt_addr) {
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = GET_HARDWARE_FUNCTION;
  memset(&p->data[1], 0xff, 7);
  p->data_len = 8;
}

void working_set_maintenance_packet(isopacket *p, int addr, int vt_addr, int initializing) {
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = WORKING_SET_MAINTENANCE_FUNCTION;
  // p->data[1] = 0; // This is where we would set inititating bit if required.
  p->data[1] = initializing;
  p->data[3] = 6; // We support VT version 6
  p->data[4] = 0xff;
  p->data[5] = 0xff;
  p->data[6] = 0xff;
  p->data[7] = 0xff;
  p->data_len = 8;
}

void object_pool_transfer_packet(isopacket *p, int addr, int vt_addr, unsigned char *bytes, int len) {
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = OBJECT_POOL_TRANSFER_FUNCTION;
  p->data_len = 1;
}

void end_of_object_pool_packet(isopacket *p, int addr, int vt_addr) {
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = OBJECT_POOL_DONE_FUNCTION;
  memset(&p->data[1], 0xff, 7);
  p->data_len = 8;
}

void delete_object_pool_packet(isopacket *p, int addr, int vt_addr) {
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = DELETE_OBJECT_POOL_FUNCTION;
  memset(&p->data[1], 0xff, 7);
  p->data_len = 8;
}

int get_request_pgn(unsigned char *data) {
  return data[0] | (data[1] << 8) | (data[2] << 16);
}

void request_address_claimed(isopacket *p, int addr) {
  common_packet(p);
  p->priority = 6;
  p->pf = REQUEST_PGN >> 8;
  p->ps = GLOBAL_ADDRESS;
  p->sa = addr;
  int pgn = ADDRESS_CLAIMED_PGN;
  p->data[0] = pgn & 0xff;
  p->data[1] = (pgn >> 8) & 0xff;
  p->data[2] = (pgn >> 16) & 0xff;
  p->data_len = 3;
}

void claim_address_packet(isopacket *p, int addr) {
  common_packet(p);
  p->priority = 6;
  p->pf = ADDRESS_CLAIMED_PGN >> 8;
  p->ps = GLOBAL_ADDRESS;
  p->sa = addr;
  p->data_len = make_name((char *)p->data, &g_name_info);
}

void working_set_master_packet(isopacket *p, int addr, int num_members) {
  common_packet(p);
  p->priority = 7;
  p->pf = WORKING_SET_MASTER_PF; // pdu format
  p->ps = WORKING_SET_MASTER_PS;  // pdu specific number for this message
  p->sa = addr;
  p->data[0] = 1; // number of members in working set
  p->data_len = 8;
}

void print_candump_format(enum log_level level, unsigned char *buffer, int count) {
  int bytes;
  isopacket packet;
  uint32_t header;
  uint32_t arbitration;
  uint32_t control;
  unsigned char *pdata;

  arbitration = ntohl(*(uint32_t *)buffer);
  header = arbitration & 0xffffff7f;
  control = *(uint32_t *)(buffer + sizeof(uint32_t));
  bytes = control & 0b00001111;
  pdata = buffer + 2 * sizeof(uint32_t);

  print_hexdata(level, (unsigned char *)&header, sizeof(uint32_t));
  clg(level, "#");
  print_hexdata(level, pdata, bytes);
  clg(level, "\n");
  parse_candata(&packet, (unsigned char *)&arbitration, pdata, bytes);

  clg(DEBUG, "CANDUMP FORMAT OUTPUT\n");
  print_packet(level, &packet, packet.data, packet.data_len);
}

void handle_packet_buffer(unsigned char *buffer, int count, isopacket *pac) {
  int bytes;
  uint32_t header;
  uint32_t arbitration;
  uint32_t control;
  unsigned char *pdata;

  arbitration = ntohl(*(uint32_t *)buffer);
  header = arbitration & 0xffffff7f;
  control = *(uint32_t *)(buffer + sizeof(uint32_t));
  bytes = control & 0b00001111;
  pdata = buffer + 2 * sizeof(uint32_t);

  parse_candata(pac, (unsigned char *)&arbitration, pdata, bytes);
}

void handle_isopacket(sock_pair *ss, struct timeval *now, isopacket *pac, unsigned char *data, int data_len) {

  int handled = 0;

  if (pac->sa == my_addr) {
    clerr("saw my own packet, FAIL\n");
    return;
  }

  if (can_opts.trace_packets) {
    print_packet(DEBUG_PROTO, pac, data, data_len);
  }

  can_received_packet_counter++;

  /* Split this into 2 different sets instead of iterating twice */

  /* Do address targetted first */
  for (int i = 0; i < registered_callbacks; i++) {
    if (pac->pgn == callbacks[i].pgn) {
      if (callbacks[i].addr_check && my_addr == dest_addr(pac)) {
        handled = 1;
        callbacks[i].f(ss, pac, data, data_len, now);
        break;
      }
    }
  }

  /* Do non address targetted next (This should probably be global) */
  for (int i = 0; i < registered_callbacks && !handled; i++) {
    if (pac->pgn == callbacks[i].pgn) {
      if (!callbacks[i].addr_check) {
        handled = 1;
        callbacks[i].f(ss, pac, data, data_len, now);
        break;
      }
    }
  }

  /* Anything we don't know about to our address */
  if (!handled && my_addr != 0 && my_addr == dest_addr(pac)) {
    my_addr_callback(ss, pac, data, data_len, now);
  }

}

void handle_socket(sock_pair *ss, struct timeval *now) {
  isopacket packet;
  int count;
  unsigned char buffer[CAN_BUF_SIZE];

  count = recv(ss->read, buffer, CAN_BUF_SIZE, 0);

  if (can_opts.fpacket) {
    int packet_used = 0;
    int consumed = 0;
    can_opts.fpacket(&buffer[consumed], count - consumed, &packet, &packet_used);
    while (packet_used > 0) {
      handle_isopacket(ss, now, &packet, packet.data, packet.data_len);
      consumed += packet_used;
      can_opts.fpacket(&buffer[consumed], count - consumed, &packet, &packet_used);
    }
  } else {
    handle_packet_buffer(buffer, count, &packet);
    handle_isopacket(ss, now, &packet, packet.data, packet.data_len);
  }
}

void can_loop(sock_pair *ss, wake_info *wakes, int num_wakes, struct can_opts *opts) {
  can_opts = *opts;
  run_scheduler(ss, handle_socket, wakes, num_wakes);
}

void register_can_callback(int pgn, fcanpacket f, int addr_check) {
  int where;
  for (where = 0; where < registered_callbacks; where++) {
    if (callbacks[where].pgn == pgn && callbacks[where].addr_check == addr_check) {
      break;
    }
  }
  if (where == registered_callbacks) {
    registered_callbacks++;
  }
  callbacks[where].pgn = pgn;
  callbacks[where].f = f;
  callbacks[where].addr_check = addr_check;
}

void unregister_can_callback(int pgn, fcanpacket f, int addr_check) {
  int where;
  for (where = 0; where < registered_callbacks; where++) {
    if (callbacks[where].pgn == pgn && callbacks[where].addr_check == addr_check &&
        callbacks[where].f == f) {
      memmove(&callbacks[where], &callbacks[where + 1], registered_callbacks - 1 - where);
      registered_callbacks--;
      break;
    }
  }
}

void register_can_addr(int addr, fcanpacket f) {
  my_addr = addr;
  my_addr_callback = f;
}

int send_packet(int s, isopacket *pac) {
  int bytes;
  unsigned char buffer[CAN_BUF_SIZE];

  set_pgn(pac);
  if (can_opts.trace_packets) {
    print_packet(DEBUG_PROTO, pac, pac->data, pac->data_len);
  }

  if (can_opts.fencode) {
    bytes = can_opts.fencode(buffer, CAN_BUF_SIZE, pac);
  } else {
    bytes = encode_candata(buffer, pac);
  }
  int ret = send(s, buffer, bytes, 0);
  if (ret < 0) {
    clerr("Failed to send packet with %d\n", ret);
  }
  return ret;
}

void transport_set_pgn(isopacket *p, int pgn) {
  unsigned char *ppgn = (unsigned char *)&pgn;
  p->data[5] = ppgn[2];
  p->data[6] = ppgn[1];
  p->data[7] = ppgn[0];
}

void request_to_send_packet(isopacket *p, int addr, int vt_addr, int pgn, int bytes) {
  common_packet(p);
  p->priority = 7;
  p->pf = TRANSPORT_PROTOCOL_CM;
  p->ps = vt_addr;
  p->sa = addr;
  p->data[0] = CM_RTS; // destination specific RTS (request to send)
  *(uint16_t *)&p->data[1] = bytes;
  int packets = bytes / 7;
  if (bytes % 7) {
    packets++;
  }
  p->data[3] = packets; // total number of packets
  p->data[4] = 0xff;    // no limit of packets to send per CTS (clear to send)

  transport_set_pgn(p, pgn);
  p->data_len = 8;
}


void clear_to_send_packet(isopacket *p, int addr, int vt_addr, int packets, int pgn) {
  common_packet(p);
  p->priority = 7;
  p->pf = TRANSPORT_PROTOCOL_CM;
  p->ps = vt_addr;
  p->sa = addr;
  p->data[0] = CM_CTS;
  p->data[1] = packets;
  p->data[2] = 1; // start at packet number 1
  p->data[3] = 0xff;
  p->data[4] = 0xff;

  unsigned char *ppgn = (unsigned char *)&pgn;
  p->data[5] = ppgn[2];
  p->data[6] = ppgn[1];
  p->data[7] = ppgn[0];

  p->data_len = 8;
}

void eom_ack_packet(isopacket *p, int addr, int vt_addr, int bytes, int packets, int pgn) {
  common_packet(p);
  p->priority = 7;
  p->pf = TRANSPORT_PROTOCOL_CM;
  p->ps = vt_addr;
  p->sa = addr;
  p->data[0] = CM_EOM_ACK;
  *(uint16_t*)&p->data[1] = bytes;
  p->data[3] = packets;
  p->data[4] = 0xff;

  unsigned char *ppgn = (unsigned char *)&pgn;
  p->data[5] = ppgn[2];
  p->data[6] = ppgn[1];
  p->data[7] = ppgn[0];

  p->data_len = 8;
}

void request_to_send_etp_packet(isopacket *p, int addr, int vt_addr, int pgn, int bytes) {
  common_packet(p);
  p->priority = 7;
  p->pf = TRANSPORT_PROTOCOL_CM;
  p->ps = vt_addr;
  p->sa = addr;
  p->data[0] = ETP_CM_RTS; // ETP RTS (request to send)
  *(uint32_t *)&p->data[1] = bytes;

  transport_set_pgn(p, pgn);
  p->data_len = 8;
}

void etp_data_packet_offset_packet(isopacket *p, int addr, int vt_addr, int pgn, int offset, int bytes) {
  common_packet(p);
  p->priority = 7;
  p->pf = TRANSPORT_PROTOCOL_CM;
  p->ps = vt_addr;
  p->sa = addr;
  p->data[0] = ETP_CM_DPO; // ETP DPO
  int packets = bytes / 7;
  if (bytes % 7) {
    packets++;
  }
  if (packets > 0xff) {
    packets = 0xff;
  }
  *(uint32_t *)(&p->data[1]) = offset << 8; // see etp_cts_start_packet
  p->data[1] = packets;

  transport_set_pgn(p, pgn);
  p->data_len = 8;
}

void print_transport_protocol_cm_packet(enum log_level level, unsigned char sa, unsigned char *data) {
  clg(level, "\tTransport Protocol Connection Management from %d\n", sa);
  int pgn = 0;
  unsigned char *ppgn = (unsigned char *)&pgn;
  ppgn[2] = data[5];
  ppgn[1] = data[6];
  ppgn[0] = data[7];

  if (data[0] == CM_CTS) {
    clg(level, "\tClear to send (CTS)\n");
    clg(level, "\tCan send %d packets\n", data[1]);
    clg(level, "\tNext packet to send: %d\n", data[2]);
    clg(level, "\tPGN of packeted messaged: %d\n", pgn);
  } else if (data[0] == 0xff) {
    clg(level, "\tConnection ABORT!\n");
    if (data[1] == 2) {
      clg(level, "\tSystem Resources Need For Another Task\n");
    } else {
      clg(level, "\tReason: %d\n", data[1]);
    }
    clg(level, "\tPGN of packeted messaged: %d\n", pgn);
  } else if (data[0] == CM_EOM_ACK) {
    clg(level, "End of data ACK!\n");
  } else {
    print_hexdata(level, data, 8);
    clg(level, "\n");
  }
}

int cts(unsigned char *data) { return data[0] == CM_CTS; }

int etp_cts(unsigned char *data) { return data[0] == ETP_CM_CTS; }

int etp_cts_num(unsigned char *data) { return data[1]; }

int cts_num(unsigned char *data) { return data[1]; }

int end_of_msg_ack(unsigned char *data) { return data[0] == CM_EOM_ACK; }

int etp_end_of_msg_ack(unsigned char *data) { return data[0] == ETP_CM_EOM_ACK; }

int etp_conn_abort(unsigned char *data) { return data[0] == CM_ABORT; }

int tp_conn_abort(unsigned char *data) { return data[0] == CM_ABORT; }

int cts_start_packet(unsigned char *data) { return data[2]; }

int etp_cts_start_packet(unsigned char *data) { return (*(uint32_t *)(&data[1])) >> 8; }

int _data_transport_packet(isopacket *p, int addr, int vt_addr, struct transport_buffer *opb, int pf) {
  /* This is kind of funny - we have to tell the object pool what operation to claim the data is for */
  common_packet(p);
  int bytes = opb_next_packet(opb, p->data);
  if (bytes == 0) {
    return 0;
  }
  p->data_len = 8;
  p->priority = 7;
  p->data_len = 8;
  p->pf = pf;
  p->ps = vt_addr;
  p->sa = addr;
  return bytes;
}

int data_transport_packet(isopacket *p, int addr, int vt_addr, struct transport_buffer *opb) {
  return _data_transport_packet(p, addr, vt_addr, opb, TPDT_PF);
}

int e_data_transport_packet(isopacket *p, int addr, int vt_addr, struct transport_buffer *opb) {
  return _data_transport_packet(p, addr, vt_addr, opb, ETPDT_PF);
}

int set_active_mask_command(isopacket *p, int addr, int vt_addr, int working_set_object_id, int active_mask_id) {
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = CHANGE_ACTIVE_MASK_FUNCTION; // change active mask command
  *(uint16_t *)&p->data[1] = working_set_object_id;
  *(uint16_t *)&p->data[3] = active_mask_id;
  memset(&p->data[5], 0xff, 3);
  p->data_len = 8;
  return CHANGE_ACTIVE_MASK_FUNCTION;
}

void vt_set_string_value_command(isopacket *p, int addr, int vt_addr, int string_id, const char *value) {
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = 8; // change string value
  *(uint16_t *)&p->data[1] = string_id;
  int len = strlen(value);
  if (len > 4) {
    len = 4; // this should never happen
  }
  p->data[3] = len;
  memcpy(&p->data[4], value, len);
  if (len < 4) {
    memset(&p->data[8 - (4 - len)], 0xff, 4 - len);
  }
  p->data_len = 8;
}

int set_string_value_command(isopacket *p, int addr, int vt_addr, int string_id, const char *value) {
  /* XXX For reasons unknown, strings that can be modified need an extra space padding (no this is not
   * just for making them longer later on. */
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = CHANGE_STRING_VALUE_COMMAND;
  *(uint16_t *)&p->data[1] = string_id;
  int len = strlen(value) + 1; // pad one space
  if (len > 3) {
    len = 3; // this should never happen
  }

  *(uint16_t *)&p->data[3] = len;
  memcpy(&p->data[5], value, len);
  p->data[5 + len - 1] = ' '; // pad one space
  if (len < 3) {
    memset(&p->data[8 - (3 - len)], 0xff, 3 - len);
  }
  p->data_len = 8;
  return CHANGE_STRING_VALUE_COMMAND;
}

void _change_attribute_common(isopacket *p, int addr, int vt_addr, int object_id, int aid) {
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = CHANGE_ATTRIBUTE_FUNCTION;
  *(uint16_t *)&p->data[1] = object_id;
  p->data[3] = aid;
  p->data_len = 8;
}

int set_string_font_command(isopacket *p, int addr, int vt_addr, int string_id, int font_id) {
  /* XXX This does not work - isobus spec says you can't use it on strings ... and you can't */
  _change_attribute_common(p, addr, vt_addr, string_id, 4);
  *(uint16_t *)&p->data[4] = font_id;
  p->data[6] = 0;
  p->data[7] = 0;
  return CHANGE_ATTRIBUTE_FUNCTION;
}

int set_button_bgcolor_command(isopacket *p, int addr, int vt_addr, int button_id, int color_idx) {
  _change_attribute_common(p, addr, vt_addr, button_id, 3);
  p->data[4] = color_idx;
  p->data[5] = 0;
  p->data[6] = 0;
  p->data[7] = 0;
  return CHANGE_ATTRIBUTE_FUNCTION;
}

int change_numeric_value_command(isopacket *p, int addr, int vt_addr, int object_id, int value, int bytes) {
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = 168; // Change numberic value command
  *(uint16_t *)&p->data[1] = object_id;
  p->data[3] = 0xff;
  memset(&p->data[4], 0, 4);
  *(uint16_t *)&p->data[4] = value;
  p->data_len = 8;
  return 168;
}

int set_object_position_relative_command(isopacket *p, int addr, int vt_addr, int parent_id, int object_id, int xoff,
                                         int yoff) {
  /* There is an absolute version of this command, but the data payload is 9 bytes which means it has to
   * be sent via multi-packet DT, so can't come from here */

  /* relative movements have a 127 pixel offset (because negative numbers?) */
  xoff += 127;
  yoff += 127;
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = CHANGE_CHILD_LOCATION_FUNCTION;
  PUT16(&p->data[1], parent_id);
  PUT16(&p->data[3], object_id);
  p->data[5] = xoff;
  p->data[6] = yoff;
  p->data[7] = 0xff;
  p->data_len = 8;
  return CHANGE_CHILD_LOCATION_FUNCTION;
}

int set_object_size_command(isopacket *p, int addr, int vt_addr, int object_id, int width, int height) {
  ecu2vt_packet(p, addr, vt_addr);
  p->data[0] = CHANGE_OBJECT_SIZE_FUNCTION;
  PUT16(&p->data[1], object_id);
  PUT16(&p->data[3], width);
  PUT16(&p->data[5], height);
  p->data[7] = 0xff;
  p->data_len = 8;
  return CHANGE_OBJECT_SIZE_FUNCTION;
}

/* Tractor Messages */
void print_wheel_speed_distance_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\twheel based speed / distance: %d\n", sa);
  clg(level, "\tspeed: %d\n", *(uint16_t *)&pdata[0]);
  clg(level, "\tdistance: %d\n", *(uint32_t *)&pdata[2]);
  clg(level, "\tmax time of tractor power: %d\n", pdata[6]);
  clg(level, "\toperator states: ");
  print_binary(level, pdata[7]);
  clg(level, "\n");
}

void print_ground_speed_distance_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\tground based speed / distance: %d\n", sa);
  clg(level, "\tspeed: %d\n", *(uint16_t *)&pdata[0]);
  clg(level, "\tdistance: %d\n", *(uint32_t *)&pdata[2]);
}

float get_speed_mph(unsigned char *pdata) {
  /* data comes in as meters per second * 1000 */
  return 2.237 * (*(uint16_t *)&pdata[0]) / 1000.0;
}

void print_hitch_status_message(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "\thitch status: %d\n", sa);
  clg(level, "\tstatus: %d\n", pdata[0]);
}

int get_hitch_position(unsigned char *pdata) {
  /* 0.4% per bit */
  return (int)(pdata[0] * 0.4);
}

int hitch_position_packet(isopacket *p, int addr, int src_addr, int position) {
  pgn_packet(p, PRIMARY_HITCH_STATUS_PGN, addr, src_addr);
  p->data[0] = (int)(position / 0.4);
  for (int i = 1; i < 8; i++) {
    p->data[i] = 0xff;
  }
  p->data_len = 8;
  return PRIMARY_HITCH_STATUS_PGN;
}

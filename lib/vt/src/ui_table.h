#include <cstring>

#include "yaml_node.h"

extern "C" {
#include "isostate.h"
}

struct ISOFont;

class UITable;

class TableRow {
public:
  TableRow(int);
  TableRow();
  TableRow(const TableRow &other);
  TableRow &operator=(const TableRow &other);
  ~TableRow();
  void set_cols(int col_count);
  void set_col(int col, const char *value);

private:
  void copy(const TableRow &other);
  int num_cols;
  std::string *row;
  friend class UITable;
};

class UITable {
public:
  UITable(int id, std::string name, int width, int height, int line_attributes, int fill_attributes,
          int header_fill_attributes, int header_height, int display_rows, bool hide_row_dividers, const ISOFont *font,
          YamlNode *columns, YamlNode *upscroll, YamlNode *downscoll);
  UITable(const UITable &other);
  ~UITable();
  void press_upscroll();
  void press_downscroll();
  bool set_cell(int row, int col, const char *value);
  bool delete_row(int row);

  int numcols;
  int display_rows;
  bool hide_row_dividers;
  std::string name;
  int id;

  const ISOFont *font;

  YamlNode *columns;
  YamlNode *upscroll;
  YamlNode *downscroll;

  int width;
  int height;
  int line_attributes;
  int fill_attributes;
  int header_fill_attributes;
  int header_height;

  int *display_cells;

  int scrollbar_id;
  int scrollbar_x;
  int scrollbar_ymin;
  int scrollbar_ymax;
  int scrollbar_y;
  int scrollbar_w;

  int num_store_rows;

private:
  int display_start_row;
  TableRow *store_rows;

  int get_scrollbar_height();
  void update_scrollbar_position();
  void update_scrollbar_height();
  void redraw(enum ui_request_type);
};

#include <stdio.h>
#include <string.h>

#include "object_pool.h"

/*
 * Object pool items:
 * [Object ID][Type][Attributes and data]
 */

void op_zero(object_pool *op) {
  memset(op, 0, sizeof(*op));
  opb_zero(&op->opb);
  op->next_object_id = 128;
}

void op_command(object_pool *op, int cmd_val) { opb_add8(&op->opb, cmd_val); }

void op_init(object_pool *op, int cmd_val) {
  /* object_pool forces size on the op_buffer */
  opb_reset_sized(&op->opb, DEFAULT_OP_SIZE);
  op_command(op, cmd_val);
}

void op_free(object_pool *op) { opb_free(&op->opb); }

/* Object level builders ******************************************************************/

int next_object_id(object_pool *op) {
  int ret = op->next_object_id;
  op->next_object_id++;
  return ret;
}

/* passthroughs to op_buffer */
int op_memory_needed(object_pool *op) { return opb_memory_needed(&op->opb); }
int op_transport_done(object_pool *op) { return opb_transport_done(&op->opb); }

int op_next_packet(object_pool *op, unsigned char *buf) { return opb_next_packet(&op->opb, buf); }

void op_set_start_packet(object_pool *op, int start) { opb_set_start_packet(&op->opb, start); }

void add_data_mask(object_pool *op, int object_id, int bg_color, int soft_key_mask_id, int objects) {
  opb_add_data_mask(&op->opb, object_id, bg_color, soft_key_mask_id, objects);
}

void add_string(object_pool *op, int my_id, int var_id, const char *s, int width, int height, uint16_t font_attributes,
                int justification) {
  opb_add_string(&op->opb, my_id, var_id, s, width, height, font_attributes, justification);
}

void add_string_var(object_pool *op, int var_id, const char *s) { opb_add_string_var(&op->opb, var_id, s); }

void add_picture_graphic(object_pool *op, int my_id, unsigned char *bytes, int width, int height) {
  opb_add_picture_graphic(&op->opb, my_id, bytes, width, height);
}

void add_font_attributes(object_pool *op, int object_id, int color, int size, int style) {
  opb_add_font_attributes(&op->opb, object_id, color, size, style);
}

void add_soft_key(object_pool *op, int my_id, int bg_color, int objects) {
  opb_add_soft_key(&op->opb, my_id, bg_color, objects);
}

void add_soft_key_mask(object_pool *op, int my_id, int bg_color, int *button_ids, int num_buttons) {
  opb_add_soft_key_mask(&op->opb, my_id, bg_color, button_ids, num_buttons);
}

void add_line_attributes(object_pool *op, int id, int color, int thickness, int pattern) {
  opb_add_line_attributes(&op->opb, id, color, thickness, pattern);
}

void add_fill_attributes(object_pool *op, int id, int color) { opb_add_fill_attributes(&op->opb, id, color); }

void add_rectangle(object_pool *op, int id, int line_attributes, int fill_attributes, int width, int height) {
  opb_add_rectangle(&op->opb, id, line_attributes, fill_attributes, width, height);
}

void add_line(object_pool *op, int id, int line_attributes, int width, int height, int direction) {
  opb_add_line(&op->opb, id, line_attributes, width, height, direction);
}

void add_button(object_pool *op, int id, int width, int height, int bg_color, int border_color, int key_code,
                int options, int objects) {
  opb_add_button(&op->opb, id, width, height, bg_color, border_color, key_code, options, objects);
}

void add_container(object_pool *op, int id, int width, int height, int objects) {
  opb_add_container(&op->opb, id, width, height, objects);
}

void add_input_list(object_pool *op, int id, int width, int height, int options, int objects, int selected) {
  opb_add_input_list(&op->opb, id, width, height, options, objects, selected);
}

void add_checkbox(object_pool *op, int id, int font_id, int bg_color, int width) {
  opb_add_checkbox(&op->opb, id, font_id, bg_color, width);
}

void add_meter(object_pool *op, int id, int width, int needle_color, int border_color, int arc_color, int options,
               int ticks_count, int start_angle, int end_angle, int min_value, int max_value, int value) {
  opb_add_meter(&op->opb, id, width, needle_color, border_color, arc_color, options, ticks_count, start_angle,
                end_angle, min_value, max_value, value);
}

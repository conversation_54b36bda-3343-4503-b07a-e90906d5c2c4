#ifndef _ISOSTATE_H
#define _ISOSTATE_H

#include "logging.h"
#include "vt.h"
#include "network.h"

enum ui_request_type { NONE, USER, UI_ELEMENT, SWITCH_SCREEN };

enum working_set_state {
  NOSET,
  INITIALIZING,
  GET_HARDWARE,
  CHECK_MEM,
  XFER_OBJECT_POOL,
  XFER_OBJECT_POOL_ETP,
  COMPLETE
};

enum working_set_state get_working_set_state();
int get_iso_addr();
sock_pair get_ss();

void async_exit();
int isostate_init();
void set_string_id_value(int id, const char *value);
void set_string_id_value_typed(int string_id, const char *value, enum ui_request_type);
void set_object_id_position_relative_typed(int parent_id, int object_id, int xoff, int yoff, enum ui_request_type);
void set_object_id_size(int object_it, int width, int height);

typedef void (*isocallback)(sock_pair *ss, struct timeval *now);
int register_address_callback(isocallback cb);

void trigger_can(); // wakeup from the language thread
void unload_op();

int vt_addr();
int working_set_object_id();

#define BUS_QUEUE_ELEMENTS 128

#endif // _ISOSTATE_H

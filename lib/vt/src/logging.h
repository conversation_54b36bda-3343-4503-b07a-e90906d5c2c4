/* clg is carbon logger.
 *
 * log() is already part of math.h. clog is already part of complex.h.
 */

#ifndef _LOGGING_H
#define _LOGGING_H

#include <stdarg.h>
#include "vt.h"

int clg_init(enum log_level level, const char *filename);
void clg(enum log_level level, const char *fmt, ...);
void clinfo(const char *fmt, ...);
void cldebug_proto(const char *fmt, ...);
void cldebug(const char *fmt, ...);
void clwarn(const char *fmt, ...);
void clerr(const char *fmt, ...);
void vclg(enum log_level level, const char *fmt, va_list ap);
enum log_level get_logging_level();
void set_logging_level(enum log_level);
void kill_escapes();
int allow_escapes();

#endif /* _LOGGING_H */

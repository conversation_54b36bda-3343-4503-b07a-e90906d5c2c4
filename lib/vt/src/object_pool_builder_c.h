#include "vt.h"

const char *button_str_by_id(int id);
int button_id_by_str(const char *name);
int datamask_id_by_str(const char *name);
const char *input_list_str_by_id(int id);
const char *input_list_select_str_by_index(int id, int index);
int string_var_id_by_str(const char *name);
int string_id_by_str(const char *name);
int meter_id_by_str(const char *name);
int font_id_by_str(const char *fontname);
int color_index(const char *val);
int trigger_ui_button(int key, int value);

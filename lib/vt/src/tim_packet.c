#include <string.h>

#include "can.h"
#include "tim.h"
#include "tim_packet.h"
#include "tim_packet_print.h"
#include "tim_packet.h"
#include "tim_auth.h"

#define TIM_VERSION 1

void print_hexdata(enum log_level level, unsigned char *p, int len);

/* Generic TIM Messages */
void tim_pac(isopacket *pac, int addr, int dest) {
  pac->edp = pac->dp = 0;
  pac->sof = 1;
  pac->pf = TIM_CLIENT_2_TIM_SERVER >> 8;
  pac->ps = dest;
  pac->sa = addr;
  pac->priority = 6;
  memset(pac->data, 0, 8);
}

void tim_connection_version_request_packet(isopacket *pac, int addr, int dest) {
  tim_pac(pac, addr, dest);
  pac->priority = 7;
  pac->data[0] = TIM_CONNECTION_VERSION;
  pac->data[2] = TIM_VERSION; // implemented version
  pac->data[3] = TIM_VERSION; // minimum version
  pac->data_len = 8;
}

void tim_functions_support_request_packet(isopacket *pac, int addr, int dest) {
  tim_pac(pac, addr, dest);
  pac->data[0] = TIM_FUNCTION_SUPPORT;
  pac->data_len = 8;
}

void tim_client_status_packet(isopacket *pac, int addr, int dest) {
  tim_pac(pac, addr, dest);
  pac->priority = 4;
  pac->data[0] = TIM_CLIENT_STATUS;
  memset(&pac->data[3], 0xff, 5);
  pac->data_len = 8;
}

void print_server_status(enum log_level level, struct server_status_bits *bits) {
  clg(level, "\theartbeat: %d", bits->heartbeat);
  clg(level, ", system_state: 0x%x", bits->system_state);
  clg(level, ", master_indication: 0x%x", bits->master_indication);
  clg(level, ", operation_state: 0x%x", bits->operation_state);
  clg(level, ", server_state: %s\n", automation_name(bits->server_state));
}

void print_connection_version(enum log_level level, unsigned char *pdata) {
  clg(level, "implemented: %d, minimum: %d\n", pdata[2], pdata[3]);
}

void aux_valve_message(enum log_level level, unsigned char *data) {
  clg(level, "valve %d, flow %d, %s\n", data[0], data[1] & 0xf, automation_name(data[2] & 0xf));
}

void pto_message(enum log_level level, unsigned char *data) {
  clg(level, "pto %s, state %d, %s\n", data[0] == 0x40 ? "FRONT" : "REAR", data[1] & 0xf,
      automation_name(data[2] & 0xf));
}

void rear_hitch_message(enum log_level level, unsigned char *data) {
  clg(level, "rear hitch %s, state %d, %s\n", data[0] == 0x42 ? "FRONT" : "REAR", data[1] & 0xf,
      automation_name(data[2] & 0xf));
}

void vehicle_speed_message(enum log_level level, unsigned char *data) {
  clg(level, "speed: counter %d, %s, slot 0x%x\n", data[1] & 0xf, automation_name(data[2] & 0xf),
      *(uint16_t*)&data[4]);
}

void external_guidance_message(enum log_level level, unsigned char *data) {
  clg(level, "guidance: counter %d, %s, slot 0x%x\n", data[1] & 0xf, automation_name(data[2] & 0xf),
      *(uint16_t*)&data[4]);
}

const char *function_status_string(int res) {
  int low = res & (0xf | 1 << 4);
  // 76543210
  int high = res >> 5;

  switch (high) {
    case 0x00:
      return "Not Assigned";
    case 0x01:
      return "Assigned";
    case 0x02:
    case 0x03:
    case 0x04:
      return "RESERVED";
    case 0x05:
      return "NOT successful";
    case 0x06:
      return "Error";
    case 0x07:
      return "Not available";
  }
  return "UNKNOWN";
}

void function_assignment_response_message(enum log_level level, unsigned char *data) {
  int i;
  int count = data[1];
  for (i = 0; i < count; i++) {
    clg(level, "\tFunction 0x%x -> %s\n", data[2+i*2], function_status_string(data[3+i*2]));
  }
}

void print_tim_server_2_client(enum log_level level, unsigned char sa, unsigned char *pdata) {
  struct server_status_bits *bits;
  clg(level, "%d [0x%x]: TIM_SERVER_2_TIM_CLIENT\n", sa, sa);
  switch(pdata[0]) {
    case TIM_SERVER_STATUS:
      bits = (void*)pdata;
      clg(level, "tim server status: ");
      print_server_status(level, bits);
      break;
      
    case TIM_SERVER_VERSION:
      clg(level, "tim server version:\n");
      print_hexdata(level, pdata, 8);
      clg(level, "\n");
      break;

    case TIM_CONNECTION_VERSION:
      clg(level, "connection version: ");
      print_connection_version(level, pdata);
      break;

    case TIM_FUNCTION_ASSIGNMENT:
      function_assignment_response_message(level, pdata);
      break;

    case TIM_FUNCTION_SUPPORT:
      clg(level, "function support received\n");
      break;

    case TIM_FUNCTION_ASSIGNMENT_STATUS:
      clg(level, "function assignment status:\n");
      print_hexdata(level, pdata, 8);
      clg(level, "\n");
      break;

	case AUX_VALVE_1:
	case AUX_VALVE_2:
	case AUX_VALVE_3:
	case AUX_VALVE_4:
	  aux_valve_message(level, pdata);
	  break;

	case FRONT_PTO:
	case REAR_PTO:
	  pto_message(level, pdata);
	  break;

	case FRONT_HITCH:
	case REAR_HITCH:
	  rear_hitch_message(level, pdata);
	  break;

  case VEHICLE_SPEED:
    vehicle_speed_message(level, pdata);
    break;

  case EXTERNAL_GUIDANCE:
    external_guidance_message(level, pdata);
    break;

  default:
    clg(level, "UNKNOWN tim server 2 client: %d [0x%x]\n", pdata[0], pdata[0]);
    print_hexdata(level, pdata, 8);
    clg(level, "\n");
  }
}

void print_client_status(enum log_level level, struct client_status_bits *bits) {
  clg(level, "\theartbeat: %d", bits->heartbeat);
  clg(level, ", client_state: %s\n", automation_name(bits->client_state));
}

void print_tim_client_2_server(enum log_level level, unsigned char sa, unsigned char *pdata) {
  struct client_status_bits *bits;
  clg(level, "%d [0x%x]: TIM_CLIENT_2_TIM_SERVER\n", sa, sa);

  switch(pdata[0]) {
    case TIM_CLIENT_STATUS:
      bits = (void*)pdata;
      clg(level, "tim client status: ");
      print_client_status(level, bits);
      break;
      
    case TIM_CLIENT_VERSION:
      clg(level, "tim client version:\n");
      print_hexdata(level, pdata, 8);
      clg(level, "\n");
      break;

    case TIM_CONNECTION_VERSION:
      clg(level, "connection version: ");
      print_connection_version(level, pdata);
      break;

    case TIM_FUNCTION_ASSIGNMENT:
      clg(level, "function assignment:\n");
      print_hexdata(level, pdata, 8);
      clg(level, "\n");
      break;

    case TIM_FUNCTION_SUPPORT:
      clg(level, "function support request\n");
      break;

    case TIM_FUNCTION_ASSIGNMENT_STATUS:
      clg(level, "function assignment status:\n");
      print_hexdata(level, pdata, 8);
      clg(level, "\n");
      break;

    case VEHICLE_SPEED:
      if (*(uint16_t*)&pdata[4] == READY_CONTROL) {
        clg(level, "requesting vehicle speed control.\n");
      } else {
        clg(level, "requesting vehicle speed: ");
        clg(level, "%d mm / s (raw %d)\n", *(uint16_t*)&pdata[4] - FORWARD_SPEED_MIN, *(uint16_t*)&pdata[4]);
      }
      break;

    default:
      clg(level, "UNKNOWN tim client 2 server: %d [0x%x]\n", pdata[0], pdata[0]);
      print_hexdata(level, pdata, 8);
      clg(level, "\n");
  }
}

void print_tim_auth12(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "%d [0x%x]: TIM_AUTH12\n", sa, sa);

  switch (pdata[0]) {
    case TIM_SERVER_AUTH_STATUS:
      clg(level, "Server auth status: ");
      clg(level, "err: %d, authenticated: %d, restart: %d, lwa: %d, csigned: %d, abits: ",
          pdata[1], pdata[2], (pdata[3] & 1 << 7) >> 7, (pdata[3] & 1 << 6) >> 6, (pdata[3] & 1 << 1) >> 1);
      print_binary(level, pdata[3]);
      clg(level, "\n");
      break;

    case SERVER_RANDOM_CHALLENGE_REQUEST:
      clg(level, "Server random challenge response\n");
      break;

    case CLIENT_RANDOM_CHALLENGE_REQUEST:
      clg(level, "Client random challenge request\n");
      break;

    case SERVER_SIGNED_CHALLENGE_REQUEST:
      clg(level, "Server signed challenge response\n");
      break;

    case CLIENT_SIGNED_CHALLENGE_REQUEST:
      clg(level, "Client signed challenge request\n");
      break;

    case CLIENT_CERTIFICATE_REQUEST:
      clg(level, "Client certificate request: ");
      switch (pdata[4]) {
        case TESTLAB_CERTIFICATE:
          clg(level, "TESTLAB\n");
          break;
        case MANUFACTURER_CERTIFICATE:
          clg(level, "MANUFACTURER\n");
          break;
        case MANUFACTURER_SERIES_CERTIFICATE:
          clg(level, "MANUFACTURER_SERIES\n");
          break;
        case DEVICE_CERTIFICATE:
          clg(level, "DEVICE\n");
          break;
      }
      break;

    case SERVER_CERTIFICATE_REQUEST:
      clg(level, "Server certificate response\n");
      break;

    default:
      clg(level, "Unknown AUTH12\n");
      print_hexdata(level, pdata, 8);
      clg(level, "\n");
      break;

  }
}
void print_tim_auth21(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "%d [0x%x]: TIM_AUTH21\n", sa, sa);

  switch (pdata[0]) {
    case TIM_CLIENT_AUTH_STATUS:
      clg(level, "Client auth status: ");
      clg(level, "err: %d, authenticated: %d, restart: %d, lwa: %d, csigned: %d, abits: ",
          pdata[1], pdata[2], (pdata[3] & 1 << 7) >> 7, (pdata[3] & 1 << 6) >> 6, (pdata[3] & 1 << 1) >> 1);
      print_binary(level, pdata[3]);
      clg(level, "\n");
      break;

    case SERVER_RANDOM_CHALLENGE_REQUEST:
      clg(level, "Server random challenge request\n");
      break;

    case CLIENT_RANDOM_CHALLENGE_REQUEST:
      clg(level, "Client random challenge response\n");
      break;

    case SERVER_SIGNED_CHALLENGE_REQUEST:
      clg(level, "Server signed challenge request\n");
      break;

    case CLIENT_SIGNED_CHALLENGE_REQUEST:
      clg(level, "Client signed challenge response\n");
      break;

    case SERVER_CERTIFICATE_REQUEST:
      clg(level, "Server certificate request\n");
      break;

    default:
      clg(level, "Unknown AUTH21\n");
      print_hexdata(level, pdata, 8);
      clg(level, "\n");
      break;
  }
}

const char *server_challenge_response_err(unsigned char err) {
  switch(err) {
    case 0x21:
      return "Request Not Allowed";
      break;

    default:
      return "UNKNOWN SERVER CHALLENGE ERR";
      break;
  }
}

const char *automation_name(int automation) {
  switch (automation) {
    case AUTOMATION_UNAVAILABLE:
      return "AUTOMATION_UNAVAILABLE";
    case AUTOMATION_NOT_READY:
      return "AUTOMATION_NOT_READY";
    case AUTOMATION_READY:
      return "AUTOMATION_READY";
    case AUTOMATION_ENABLED:
      return "AUTOMATION_ENABLED";
    case AUTOMATION_PENDING:
      return "AUTOMATION_PENDING";
    case AUTOMATION_ACTIVE:
      return "AUTOMATION_ACTIVE";
    default:
      return "UNKNOWN AUTOMATION";
  }
  return "WHAT?";
}

/* TIM Auth Messages */

void tim_auth_pac(isopacket *pac, int addr, int dest) {
  tim_pac(pac, addr, dest);
  pac->pf = TIM_AUTH21_PGN >> 8;
}

void tim_client_auth_status_packet(isopacket *pac, int addr, int dest, int authenticated, int restart, int lwa,
                                   int challenge_signed, int certs_valid_bits) {
  tim_auth_pac(pac, addr, dest);
  pac->data[0] = TIM_CLIENT_AUTH_STATUS;
  pac->data[1] = 0; // error code
  pac->data[2] = authenticated & 0xf;
  pac->data[3] = (restart << 7) | (lwa << 6) | (challenge_signed << 1) | certs_valid_bits;

  // XXX AEF
  pac->data[4] = 0xff;
  pac->data[5] = 0xff;
  pac->data[6] = 0x02;
  pac->data[7] = 0x01;

  pac->data_len = 8;
}

void server_random_challenge_request_packet(isopacket *pac, int addr, int dest) {
  tim_auth_pac(pac, addr, dest);
  pac->data[0] = SERVER_RANDOM_CHALLENGE_REQUEST;
  pac->data_len = 8;
}

void server_certificate_request_packet(isopacket *pac, int addr, int dest, int cert_type) {
  tim_auth_pac(pac, addr, dest);
  pac->data[0] = SERVER_CERTIFICATE_REQUEST;
  pac->data[1] = 0; // reserved
  pac->data[2] = 0 | 0xf; // auth type
  pac->data[3] = DER_BINARY_FORMAT;
  pac->data[4] = cert_type;
  pac->data[5] = 0xff;
  pac->data[6] = 0xff;
  pac->data[7] = 0xff;
  pac->data_len = 8;
}

void server_signed_challenge_request_packet(isopacket *pac, int addr, int dest) {
  tim_auth_pac(pac, addr, dest);
  pac->data[0] = SERVER_SIGNED_CHALLENGE_REQUEST;
  pac->data[1] = 0; // reserved
  pac->data[2] = 0 | 0xf; // auth type
  pac->data[3] = 0xff;
  pac->data[4] = 0xff;
  pac->data[5] = 0xff;
  pac->data[6] = 0xff;
  pac->data[7] = 0xff;
  pac->data_len = 8;
}

void vehicle_speed_request_packet(isopacket *pac, int addr, int dest, int mm_per_s) {
  tim_pac(pac, addr, dest);
  pac->priority = 3;
  pac->data[0] = VEHICLE_SPEED;
  pac->data[1] = REQUEST_COUNTER_DONT_CARE;
  pac->data[2] = 0xff;
  pac->data[3] = 0xff;
  *(uint16_t*)&pac->data[4] = mm_per_s + FORWARD_SPEED_MIN;
  pac->data[6] = 0xff;
  pac->data[7] = 0xff;
  pac->data_len = 8;
}

void rear_hitch_request_packet(isopacket *pac, int addr, int dest, int percentage) {
  tim_pac(pac, addr, dest);
  pac->priority = 3;
  pac->data[0] = REAR_HITCH;
  pac->data[1] = REQUEST_COUNTER_DONT_CARE;
  pac->data[2] = 0xff;
  pac->data[3] = 0xff;
  *(uint16_t*)&pac->data[4] = percentage;
  pac->data[6] = 0xff;
  pac->data[7] = 0xff;
  pac->data_len = 8;
}

void vehicle_speed_release_packet(isopacket *pac, int addr, int dest) {
  tim_pac(pac, addr, dest);
  pac->priority = 3;
  pac->data[0] = VEHICLE_SPEED;
  pac->data[1] = REQUEST_COUNTER_DONT_CARE;
  pac->data[2] = 0xff;
  pac->data[3] = 0xff;
  *(uint16_t*)&pac->data[4] = SPEED_RELEASE_WITH_OPERATOR;
  pac->data[6] = 0xff;
  pac->data[7] = 0xff;
  pac->data_len = 8;
}

void rear_hitch_release_packet(isopacket *pac, int addr, int dest) {
  tim_pac(pac, addr, dest);
  pac->priority = 3;
  pac->data[0] = REAR_HITCH;
  pac->data[1] = REQUEST_COUNTER_DONT_CARE;
  pac->data[2] = 0xff;
  pac->data[3] = 0xff;
  *(uint16_t*)&pac->data[4] = SPEED_RELEASE_WITH_OPERATOR;
  pac->data[6] = 0xff;
  pac->data[7] = 0xff;
  pac->data_len = 8;
}

void vehicle_speed_control_packet(isopacket *pac, int addr, int dest) {
  tim_pac(pac, addr, dest);
  pac->priority = 3;
  pac->data[0] = VEHICLE_SPEED;
  pac->data[1] = REQUEST_COUNTER_DONT_CARE;
  pac->data[2] = 0xff;
  pac->data[3] = 0xff;
  *(uint16_t*)&pac->data[4] = READY_CONTROL;
  pac->data[6] = 0xff;
  pac->data[7] = 0xff;
  pac->data_len = 8;
}

void rear_hitch_control_packet(isopacket *pac, int addr, int dest) {
  tim_pac(pac, addr, dest);
  pac->priority = 3;
  pac->data[0] = REAR_HITCH;
  pac->data[1] = REQUEST_COUNTER_DONT_CARE;
  pac->data[2] = 0xff;
  pac->data[3] = 0xff;
  *(uint16_t*)&pac->data[4] = READY_CONTROL;
  pac->data[6] = 0xff;
  pac->data[7] = 0xff;
  pac->data_len = 8;
}

uint16_t vehicle_speed(unsigned char *data) {
  return *(uint16_t*)&data[4];
}

int automation_state(unsigned char *data) {
  return (data[2] & 0xf);
}

void tim_function_assignment_request_packet(isopacket *pac, int addr, int dest, int *functions, int count) {
  int i;
  tim_pac(pac, addr, dest);
  pac->data[0] = FUNCTIONS_ASSIGNMENT_REQUEST;
  pac->data[1] = count;
  // set all the bits to reserved
  for (i = 2; i < 8; i++) {
    pac->data[i] = 0xff;
  }
  // set all functions and request type
  for (i = 0; i < count; i++) {
    pac->data[2+i*2] = functions[i];
    // bits 1-5 as all 1s because reserved, function assign is bit 6
    pac->data[3+i*2] = (TIM_FUNCTION_ASSIGN << 5) | (1 << 4) | 0xf;
  }
  pac->data_len = 8;
}

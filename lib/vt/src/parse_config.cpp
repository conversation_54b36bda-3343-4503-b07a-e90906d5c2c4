#include <list>
#include <memory>
#include <string>
#include <unordered_map>

#include <yaml.h>

#include "object_pool.h"
#include "object_pool_builder.h"

extern "C" {
#include "logging.h"
}

using namespace std;

void print_event_type(enum log_level level, yaml_event_t *event) {
  if (event->type == YAML_NO_EVENT) {
    clg(level, "YAML_NO_EVENT\n");
  } else if (event->type == YAML_STREAM_START_EVENT) {
    clg(level, "YAML_STREAM_START_EVENT\n");
  } else if (event->type == YAML_STREAM_END_EVENT) {
    clg(level, "YAML_STREAM_END_EVENT\n");
  } else if (event->type == YAML_DOCUMENT_START_EVENT) {
    clg(level, "YAML_DOCUMENT_START_EVENT\n");
  } else if (event->type == YAML_DOCUMENT_END_EVENT) {
    clg(level, "YAML_DOCUMENT_END_EVENT\n");
  } else if (event->type == YAML_ALIAS_EVENT) {
    clg(level, "YAML_ALIAS_EVENT\n");
  } else if (event->type == YAML_SCALAR_EVENT) {
    clg(level, "YAML_SCALAR_EVENT\n");
  } else if (event->type == YAML_SEQUENCE_START_EVENT) {
    clg(level, "YAML_SEQUENCE_START_EVENT\n");
  } else if (event->type == YAML_SEQUENCE_END_EVENT) {
    clg(level, "YAML_SEQUENCE_END_EVENT\n");
  } else if (event->type == YAML_MAPPING_START_EVENT) {
    clg(level, "YAML_MAPPING_START_EVENT\n");
  } else if (event->type == YAML_MAPPING_END_EVENT) {
    clg(level, "YAML_MAPPING_END_EVENT\n");
  }
}

int handle_yaml_map(yaml_parser_t *yaml, YamlNode *map, int depth);

int handle_yaml_list(yaml_parser_t *yaml, YamlNode *l, int depth) {
  yaml_event_t event;
  while (1) {
    if (!yaml_parser_parse(yaml, &event)) {
      return 0;
    }

    if (event.type == YAML_MAPPING_START_EVENT) {
      YamlNode *child = new YamlNode(MAP);
      if (!handle_yaml_map(yaml, child, depth + 1)) {
        clerr("Failed to handle yaml map\n");
        delete child;
        yaml_event_delete(&event);
        return 0;
      }
      (*l->data.list).push_back(unique_ptr<YamlNode>(child));
    } else if (event.type == YAML_MAPPING_END_EVENT) {
      clerr("Not supposed to get map end in the sequence\n");
      yaml_event_delete(&event);
      return 0;
    } else if (event.type == YAML_SEQUENCE_END_EVENT) {
      yaml_event_delete(&event);
      return 1;
    }
  }
  return 0;
}

int handle_yaml_map(yaml_parser_t *yaml, YamlNode *map, int depth) {
  int need_key = 1;
  yaml_event_t event;
  string key;

  while (1) {
    if (!yaml_parser_parse(yaml, &event)) {
      return 0;
    }

    if (event.type == YAML_SCALAR_EVENT) {
      if (!need_key) {
        YamlNode *child = new YamlNode(VALUE);
        child->data.value = new string((char *)event.data.scalar.value);
        (*map->data.map)[key] = unique_ptr<YamlNode>(child);
        need_key = 1;
      } else {
        key = string((char *)event.data.scalar.value);
        need_key = 0;
      }
    } else if (event.type == YAML_MAPPING_START_EVENT) {
      YamlNode *child = new YamlNode(MAP);
      if (!handle_yaml_map(yaml, child, depth + 1)) {
        clerr("Failed to handle yaml map\n");
        delete child;
        yaml_event_delete(&event);
        return 0;
      }
      (*map->data.map)[key] = unique_ptr<YamlNode>(child);
      need_key = 1;
    } else if (event.type == YAML_MAPPING_END_EVENT) {
      yaml_event_delete(&event);
      return 1;
    } else if (event.type == YAML_SEQUENCE_START_EVENT) {
      YamlNode *child = new YamlNode(LIST);
      if (!handle_yaml_list(yaml, child, depth + 1)) {
        clerr("Failed to handle yaml list\n");
        delete child;
        yaml_event_delete(&event);
        return 0;
      }
      (*map->data.map)[key] = unique_ptr<YamlNode>(child);
      need_key = 1;
    } else if (event.type == YAML_SEQUENCE_END_EVENT) {
      clerr("Not supposed to get sequence end in the map\n");
      yaml_event_delete(&event);
      return 0;
    }
    yaml_event_delete(&event);
  }
  return 0;
}

int parse_yaml(const char *filename, YamlNode *map) {
  /*
   * Parse our layout
   */
  yaml_parser_t yaml;
  yaml_event_t event;
  yaml_parser_initialize(&yaml);

  FILE *fp = fopen(filename, "r");
  if (fp == NULL) {
    clerr("Unable to open %s.\n", filename);
    return 0;
  }

  yaml_parser_set_input_file(&yaml, fp);
  while (1) {
    if (!yaml_parser_parse(&yaml, &event)) {
      clerr("yaml parser event parse failed!\n");
      return 0;
    }

    if (event.type == YAML_MAPPING_START_EVENT) {
      if (!handle_yaml_map(&yaml, map, 1)) {
        clerr("Failed to handle yaml\n");
        yaml_event_delete(&event);
        return 0;
      }
    }

    if (event.type == YAML_STREAM_END_EVENT) {
      yaml_event_delete(&event);
      break;
    }
    yaml_event_delete(&event);
  }
  yaml_parser_delete(&yaml);
  fclose(fp);
  return 1;
}

int fill_object_pool(const char *layout_dir, const char *filename, object_pool *op) {
  YamlNode yaml(MAP);

  if (strlen(layout_dir) == 0) {
    layout_dir = ".";
  }

  int maxlen = strlen(layout_dir + strlen(filename) + 1);
  char *filepath = (char *)malloc(maxlen);
  snprintf(filepath, maxlen, "%s/%s", layout_dir, filename); 
  int ret = parse_yaml(filepath, &yaml);
  free(filepath);

  if (!ret) {
    return ret;
  }

  ret = build_object_pool(layout_dir, op, &yaml);

  return ret;
}

extern "C" {

int parse_config(const char *layout_dir, const char *filename, object_pool *op) {
  int ret = fill_object_pool(layout_dir, filename, op);
  return ret;
}

} // extern "C"

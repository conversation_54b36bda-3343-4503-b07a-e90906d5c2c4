CC=gcc
CPP=g++
# CFLAGS= -c -O2
CFLAGS= -c -g $(shell pkg-config --cflags opencv) -I ../

LFLAGS=-l readline -L ../ -lvt

C_SRC=$(wildcard *.c)
CPP_SRC=$(wildcard *.cpp)
C_OBJ=$(C_SRC:%.c=%.o)
CPP_OBJ=$(CPP_SRC:%.cpp=%.o)
PROG=parsobus

all: ${PROG}

${PROG}: ${C_OBJ} ${CPP_OBJ}
	${CPP} -o $@ $^ ${LFLAGS} 

${C_OBJ}: ${C_SRC}
	${CC} ${CFLAGS} $^

${CPP_OBJ}: ${CPP_SRC}
	${CPP} ${CFLAGS} $^

clean:
	rm -f *.o
	rm -f ${PROG}

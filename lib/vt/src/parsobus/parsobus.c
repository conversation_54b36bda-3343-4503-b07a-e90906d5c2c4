#include <stdio.h>
#include <stdlib.h>
#include <readline/readline.h>
#include "can.h"
#include "logging.h"

#define PACKET_SIZE (3 * 8 + 1)

int main(int argc, char *argv[])
{
  char *line;
  char *packet;

  clg_init(DEBUG, "-");

  for (;;)
  {
    line = readline(NULL);
    if (strlen(line) >= PACKET_SIZE)
    {
      packet = line + (strlen(line) - PACKET_SIZE);
    }
    else
    {
      packet = line;
    }
    clg(DEBUG, "%s\n", packet);
    display_char_message(DEBUG, packet);
    free(line);
  }
  return 0;
}

#ifndef _YAML_NODE_H
#define _YAML_NODE_H

#include <list>
#include <memory>
#include <string>
#include <unordered_map>

typedef enum { MAP, VALUE, LIST } yaml_node_type;

struct YamlNode {
  YamlNode(yaml_node_type type);
  ~YamlNode();
  yaml_node_type type;
  union {
    std::unordered_map<std::string, std::unique_ptr<YamlNode>> *map;
    std::list<std::unique_ptr<YamlNode>> *list;
    std::string *value;
  } data;

  YamlNode *mfind(const char *what);
  std::string *value();
  std::list<std::unique_ptr<YamlNode>> *list();
  std::string type_str();
};

#endif // _YAML_NODE_H

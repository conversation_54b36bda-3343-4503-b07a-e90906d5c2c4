#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "logging.h"
#include "network.h"
#include "can.h"
#include "isostate.h"

#include "transport.h"

#define XFER_OBJECT_POOL_USEC_DELAY 200

/****************************************************************************************************************
 * INCOMING
 ****************************************************************************************************************/
struct transport_in_buffer {
  unsigned char *data;
  int capacity;
  int data_len;
};

void tb_alloc(struct transport_in_buffer *tb, int count) {
  tb->data = (unsigned char *)malloc(count);
  tb->capacity = count;
  tb->data_len = 0;
}

void tb_append(struct transport_in_buffer *tb, unsigned char *bytes, int count) {
  if (count > tb->capacity - tb->data_len) {
    count = tb->capacity - tb->data_len;
    clerr("tb overrun!!!!!\n");
  }
  memcpy(&tb->data[tb->data_len], bytes, count);
  tb->data_len += count;
}

void tb_free(struct transport_in_buffer *tb) {
  free(tb->data);
  tb->capacity = tb->data_len = 0;
}

enum transport_type {TP_NONE, TP_DT, TP_ETP_DT };

/* in */
struct transport_state_in {
  int data_sa;
  int data_ps;
  uint32_t data_pgn;
  uint16_t data_bytes;
  uint16_t received_bytes;
  unsigned char data_packets;
  struct transport_in_buffer buffer;
};

/* out */
struct transport_state_out {
  int data_sa;
  int data_ps;
  uint32_t data_pgn;
  struct transport_buffer *current_out_buffer;
  enum transport_type transport_type;
  ftransport_done donecb;
  int wts;
  int can_send;
};

struct transport_state_in transport_state_in = {.data_bytes = 0};

struct transport_state_out transport_state_out = {.current_out_buffer = NULL,
                                                  .transport_type = TP_NONE,
                                                  .donecb = NULL};

/****************************************************************************************************************
 * OUTGOING
 ****************************************************************************************************************/

int initiate_transport(sock_pair *ss, transport_buffer *tpb, int sa, int ps, int pgn, ftransport_done donecb) {
  transport_state_out.current_out_buffer = tpb;
  transport_state_out.donecb = donecb;
  transport_state_out.data_sa = sa;
  transport_state_out.data_ps = ps;
  transport_state_out.data_pgn = pgn;

  isopacket packet;
  int bytes = opb_memory_needed(tpb);

  if ((bytes + 1) > 0x00ffffff * 7) {
    clerr("OBJECT POOL TOO BIG FOR ETP, do multiple sessions.\n");
    return -1;
  }

  if ((bytes + 1) > 255 * 7) {
    clg(DEBUG_PROTO, "Sending ETP rts for %d bytes\n", opb_memory_needed(tpb));
    transport_state_out.transport_type = TP_ETP_DT;
    request_to_send_etp_packet(&packet, transport_state_out.data_sa, transport_state_out.data_ps, pgn,
                               opb_memory_needed(tpb));
    transport_state_out.wts = packet.data[3];
    transport_state_out.can_send = 0;
  } else {
    transport_state_out.transport_type = TP_DT;
    request_to_send_packet(&packet, transport_state_out.data_sa, transport_state_out.data_ps, pgn,
                           opb_memory_needed(tpb));
    transport_state_out.wts = packet.data[3];
    transport_state_out.can_send = 0;
    clg(DEBUG_PROTO, "Sending rts for %d bytes, %d packets\n", opb_memory_needed(tpb), packet.data[3]);
  }
  send_packet(ss->write, &packet);
  return 0;
}

void xfer_dt(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
  isopacket packet;
  int bytes = 0;

  transport_buffer *opb = transport_state_out.current_out_buffer;
  if (!opb) {
    clerr("xfer_dt called with no active object pool buffer.\n");
    return;
  }

  if (transport_state_out.transport_type == TP_ETP_DT) {
    if (opb_transport_done(opb)) {
      clg(DEBUG_PROTO, "Did all 255 packets of TRANSPORT for ETP, waiting for CTS\n");
      return;
    }
    bytes = e_data_transport_packet(&packet, transport_state_out.data_sa, transport_state_out.data_ps, opb);
  } else if (transport_state_out.transport_type == TP_DT) {
    bytes = data_transport_packet(&packet, transport_state_out.data_sa, transport_state_out.data_ps, opb);
  } else {
    clerr("xfer_dt scheduled with no XFER in progress\n");
    return;
  }

  if (transport_state_out.can_send > 0) {
    transport_state_out.can_send--;
  }

  if (bytes == 0) {
    clg(DEBUG_PROTO, "DATA from transport sent, should receive ack now\n");
    return;
  } else if (transport_state_out.transport_type == TP_DT && transport_state_out.can_send == 0) {
    /* This has only been tested with TP_DT, if they want to multi-part the multi-part we may have bugs */
    clg(DEBUG_PROTO, "DATA from transport that we are cts sent, should receive ack now\n");
  } else {
    schedule_relative(xfer_dt, now, 0, XFER_OBJECT_POOL_USEC_DELAY);
  }

  send_packet(ss->write, &packet);
}

int ondone(int code, sock_pair *ss, struct timeval *now) {
  ftransport_done donecb = transport_state_out.donecb;
  transport_buffer *tpb = transport_state_out.current_out_buffer;
  transport_state_out.donecb = NULL;
  transport_state_out.current_out_buffer = NULL;
  transport_state_out.transport_type = TP_NONE;
  if (donecb) {
    donecb(code, tpb, ss, now);
    return 1;
  }
  return 0;
}

int transport_outgoing_message(sock_pair *ss, isopacket *p, struct timeval *now, int *res) {

  int handled = 0;
  if (res) {
    *res = 0;
  }

  if (p->data[0] == CM_EOM_ACK) {
    print_end_of_msg_ack_message(DEBUG, p->sa, p->data);
    clg(DEBUG_PROTO, "TP ACK - Sending end of object pool!\n");
    handled = ondone(0, ss, now);

  } else if (p->data[0] == ETP_CM_EOM_ACK) {
    clg(DEBUG_PROTO, "ETP ACK Sending end of object pool!\n");
    handled = ondone(0, ss, now);

  } else if (p->data[0] == CM_CTS) {
    if (transport_state_out.current_out_buffer == NULL) {
      clerr("CM_CTS but no transport buffer?\n");
      return 0;
    }
    clg(DEBUG_PROTO, "recieved cts for %d packets\n", cts_num(p->data));
    transport_state_out.can_send = cts_num(p->data);
    int start = cts_start_packet(p->data);
    int num_cts = cts_num(p->data);
    clg(DEBUG_PROTO, "Starting TP.DT messages at %d for %d packets\n", start, num_cts);
    opb_set_start_packet(transport_state_out.current_out_buffer, start);
    schedule_relative(xfer_dt, now, 0, XFER_OBJECT_POOL_USEC_DELAY);
    handled = 1;

  } else if (p->data[0] == ETP_CM_CTS) {
    int num_cts = etp_cts_num(p->data);
    transport_state_out.can_send = cts_num(p->data);
    if (0 == num_cts) {
      clwarn("ETP CTS with 0\n");
      if (res) {
        *res = -1;
      }
      return 0;
    }
    isopacket packet;
    int start = etp_cts_start_packet(p->data);
    clg(DEBUG_PROTO, "ETP CTS - Starting ETP.DT messages at %d for %d packets\n", start, num_cts);
    opb_set_start_packet(transport_state_out.current_out_buffer, 1);
    etp_data_packet_offset_packet(&packet, get_iso_addr(), transport_state_out.data_sa,
                                  transport_state_out.data_pgn, start - 1,
                                  opb_memory_needed(transport_state_out.current_out_buffer));
    send_packet(ss->write, &packet);
    schedule_relative(xfer_dt, now, 0, 1000);
    handled = 1;

  } else if (p->data[0] == CM_ABORT) {
    const char *str = "";
    if (p->data[1] == 3) {
      str = " TIMEOUT";
    } else if (p->data[1] == 4) {
      str = " TRANSFER IN PROGRESS";
    }
    cldebug("TP CON ABORT, reason: %d%s\n", p->data[1], str);
    if (res) {
      *res = -1;
    }
    handled = ondone(-1, ss, now);
  } else {
    clwarn("unknown transport packet\n");
    print_packet(WARN, p, p->data, p->data_len); // unknown transport packet
  }
  return handled;
}

/****************************************************************************************************************
 * INCOMING
 ****************************************************************************************************************/

void transport_data_callback(sock_pair *ss, isopacket *p, unsigned char *data, int data_len,
                             struct timeval *now) {
  int seq = p->data[0];
  // cldebug("Transport data packet: %d\n", seq);

  int bytes = 7;
  if (bytes > transport_state_in.data_bytes - transport_state_in.received_bytes) {
    bytes = transport_state_in.data_bytes - transport_state_in.received_bytes;
  }

  tb_append(&transport_state_in.buffer, &p->data[1], bytes);
  transport_state_in.received_bytes += bytes;

  // cldebug("%d bytes left to receive ...\n", transport_state.data_bytes - transport_state.received_bytes);

  if (seq >= transport_state_in.data_packets) {
    // cldebug("Transport data DONE - sending EOM ACK\n");
    isopacket packet;
    eom_ack_packet(&packet, get_iso_addr(), transport_state_in.data_sa, transport_state_in.data_bytes,
                   transport_state_in.data_packets, transport_state_in.data_pgn);
    send_packet(ss->write, &packet);
    unregister_can_callback(TPDT_PF << 8, transport_data_callback, 1);

    /* fake up isopacket for the engine and receivers to pull fields from */
    isopacket fake;
    fake.pgn = transport_state_in.data_pgn;
    fake.pf = transport_state_in.data_pgn >> 8;
    fake.sa = p->sa;
    fake.ps = get_iso_addr();

    clg(DEBUG, "handling fake iso packet: [%d]\n", transport_state_in.buffer.data[0]);
    handle_isopacket(ss, now, &fake, transport_state_in.buffer.data, transport_state_in.buffer.data_len);
    tb_free(&transport_state_in.buffer);
    clg(DEBUG, "done fake iso packet\n");
  }
}

int transport_incoming_message(sock_pair *ss, isopacket *p, struct timeval *now, int *res) {

  int handled = 0;
  if (res) {
    *res = 0;
  }

  if (p->data[0] == CM_RTS) {

    transport_state_in.data_bytes = *(uint16_t*)&p->data[1];
    transport_state_in.received_bytes = 0;

    transport_state_in.data_packets = p->data[3];
    transport_state_in.data_sa = p->sa;

    /* unpack pgn */
    transport_state_in.data_pgn = 0;
    unsigned char *ppgn = (unsigned char *)&transport_state_in.data_pgn;
    ppgn[0] = p->data[7];
    ppgn[1] = p->data[6];
    ppgn[2] = p->data[5];
    clg(DEBUG_PROTO, "CM WTS %d bytes for pgn %d [%x] across %d packets\n", transport_state_in.data_bytes,
            transport_state_in.data_pgn, transport_state_in.data_pgn, transport_state_in.data_packets);

    tb_alloc(&transport_state_in.buffer, transport_state_in.data_bytes);

    isopacket packet;
    register_can_callback(TPDT_PF << 8, transport_data_callback, 1);
    clg(DEBUG, "TRANSPORT_MESSAGE CLEAR_TO_SEND\n");
    clear_to_send_packet(&packet, get_iso_addr(), transport_state_in.data_sa, transport_state_in.data_packets,
                         transport_state_in.data_pgn);
    send_packet(ss->write, &packet);
    clg(DEBUG, "DONE TRANSPORT_MESSAGE CLEAR_TO_SEND\n");
    handled = 1;
  }
  return handled;
}

/****************************************************************************************************************
 * MAIN MESSAGING
 ****************************************************************************************************************/

int transport_message(sock_pair *ss, isopacket *p, struct timeval *now, int *res) {
  if (p->data[0] == CM_RTS) {
    return transport_incoming_message(ss, p, now, res);
  } else {
    return transport_outgoing_message(ss, p, now, res);
  }
}

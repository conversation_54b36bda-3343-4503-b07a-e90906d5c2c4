#include <sys/time.h>

#include "util.h"
#include "can.h"
#include "network.h"
#include "dispatch.h"

static float status_msg_hz;

/* Tractor Callbacks */
#define TRACTOR_STATUS_THROTTLE()                                                                                      \
  do {                                                                                                                 \
    static uint64_t _t_last = 0;                                                                                       \
    float hz = status_msg_hz;                                                                           \
    if (tv2usec(now) - _t_last > (1000 * 1000) / hz) {                                                  \
      _t_last = tv2usec(now);                                                                                          \
    } else {                                                                                                           \
      return;                                                                                                          \
    }                                                                                                                  \
  } while (0)

#define DEFAULT_TRACTOR_STATUS_HZ 1

void wheel_speed_distance_callback(sock_pair *ss, isopacket *p, unsigned char *data, int data_len,
                                   struct timeval *now) {
  TRACTOR_STATUS_THROTTLE();
  float speed = get_speed_mph(data);
  dispatch_speed_update(speed, tv2usec(now));
}

void ground_speed_distance_callback(sock_pair *ss, isopacket *p, unsigned char *data, int data_len,
                                    struct timeval *now) {
  // print_ground_speed_distance_message(DEBUG, p->sa, data); // don't know what equipment may emit these
}

void primary_hitch_status_callback(sock_pair *ss, isopacket *p, unsigned char *data, int data_len,
                                   struct timeval *now) {
  TRACTOR_STATUS_THROTTLE();
  int position = get_hitch_position(data);
  dispatch_hitch_position(position, tv2usec(now));
}

void tractor_callback_init(struct isobus_options *opts) {

  status_msg_hz = opts->status_msg_hz;
  if (status_msg_hz == 0.0) {
    status_msg_hz = DEFAULT_TRACTOR_STATUS_HZ;
  }

  /* These ones are tractor update messages available for all on the bus - no address check */
  register_can_callback(WHEEL_BASED_SPEED_AND_DISTANCE_PGN, wheel_speed_distance_callback, 0);
  register_can_callback(GROUND_BASED_SPEED_AND_DISTANCE_PGN, ground_speed_distance_callback, 0);
  register_can_callback(PRIMARY_HITCH_STATUS_PGN, primary_hitch_status_callback, 0);
}



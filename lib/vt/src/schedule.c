#include <errno.h>
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <sys/select.h>
#include <sys/time.h>
#include <unistd.h>

#include "logging.h"
#include "network.h"
#include "schedule.h"
#include "util.h"

typedef struct time_callback {
  struct timeval tv;
  ftcb cb;
} time_callback;

#define TIME_CALLBACKS_ALLOC 32
time_callback time_callbacks[TIME_CALLBACKS_ALLOC];
int num_time_callbacks = 0;
int max_time_callbacks = 0;

void add_sec(struct timeval *tv, long secs) { tv->tv_sec += secs; }

void add_usec(struct timeval *tv, long usecs) {
  tv->tv_usec += usecs;
  while (tv->tv_usec > (1000 * 1000)) {
    tv->tv_sec += 1;
    tv->tv_usec -= (1000 * 1000);
  }
}

void add_msec(struct timeval *tv, long msec) { add_usec(tv, msec * 1000); }

int past(struct timeval *now, struct timeval *compare) {
  if (compare->tv_sec < now->tv_sec) {
    return 1;
  }
  if (compare->tv_sec == now->tv_sec && compare->tv_usec < now->tv_usec) {
    return 1;
  }
  return 0;
}

void schedule_callback(ftcb cb, struct timeval *when) {
  int index;

  if (num_time_callbacks == max_time_callbacks) {
    index = max_time_callbacks;
    max_time_callbacks++;
  } else {
    for (int i = 0; i < max_time_callbacks; i++) {
      time_callback *pcb = &time_callbacks[i];
      if (pcb->tv.tv_sec == 0 && pcb->tv.tv_usec == 0) {
        index = i;
        break;
      }
    }
  }
  if (num_time_callbacks >= TIME_CALLBACKS_ALLOC) {
    clerr("FAIL NO MORE TIME CALLBACKS SPACE\n");
    return;
  }
  time_callback *pcb = &time_callbacks[index];
  num_time_callbacks++;
  pcb->tv = *when;
  pcb->cb = cb;
}

void schedule_relative(ftcb cb, struct timeval *when, long sec, long usec) {
  struct timeval tv = *when;
  add_sec(&tv, sec);
  add_usec(&tv, usec);
  schedule_callback(cb, &tv);
}

int get_earliest(struct timeval *tv) {
  if (num_time_callbacks == 0) {
    return 0;
  }
  int found = 0;
  for (int i = 0; i < max_time_callbacks; i++) {
    time_callback *pcb = &time_callbacks[i];
    if (pcb->tv.tv_sec) {
      if (!found || past(tv, &pcb->tv)) {
        *tv = pcb->tv;
        found = 1;
      }
    }
  }
  return 1;
}

void subtract_time(struct timeval *big, struct timeval *little) {
  if (big->tv_sec < little->tv_sec || (big->tv_sec == little->tv_sec && big->tv_usec <= little->tv_usec)) {
    big->tv_sec = 0;
    big->tv_usec = 0;
    return;
  }
  big->tv_sec -= little->tv_sec;
  if (big->tv_usec > little->tv_usec) {
    big->tv_usec -= little->tv_usec;
  } else {
    big->tv_sec--;
    big->tv_usec = big->tv_usec + (1000 * 1000) - little->tv_usec;
  }
}

struct timeval getnow() {
  struct timeval now;
  gettimeofday(&now, NULL);
  return now;
}

void run_scheduler(sock_pair *ss, freadcb readcb, wake_info *wakes, int num_wakes) {
  int ready;
  fd_set set;
  struct timeval now;
  struct timeval earliest;
  struct timeval *ptv = NULL;
  unsigned char wakebuf;
  int nfds = ss->read;

  for (int i = 0; i < num_wakes; i++) {
    nfds = max(nfds, wakes[i].p);
  }
  nfds += 1;

  while (1) {
    FD_ZERO(&set);
    FD_SET(ss->read, &set);
    for (int i = 0; i < num_wakes; i++) {
      FD_SET(wakes[i].p, &set);
    }
    gettimeofday(&now, NULL);
    ptv = NULL;
    if (get_earliest(&earliest)) {
      subtract_time(&earliest, &now);
      ptv = &earliest;
    }
    ready = select(nfds, &set, NULL, NULL, ptv);
    gettimeofday(&now, NULL);
    if (ready != 0) {
      if (ready < 0) {
        clwarn("select() RETURNED %d -> %s\n", ready, strerror(errno));
      } else {
        if (FD_ISSET(ss->read, &set)) {
          readcb(ss, &now);
        }
        for (int i = 0; i < num_wakes; i++) {
          if (FD_ISSET(wakes[i].p, &set)) {
            wakes[i].wake_cb(&now, wakes[i].arg);
          }
        }
      }
    }
    for (int i = 0; i < max_time_callbacks; i++) {
      time_callback *pcb = &time_callbacks[i];
      if (pcb->tv.tv_sec != 0 && pcb->cb != NULL && past(&now, &pcb->tv)) {

        /* store to call */
        ftcb cb = pcb->cb;
        struct timeval tv = pcb->tv;

        /* clean out the entry */
        pcb->tv.tv_sec = 0;
        pcb->tv.tv_usec = 0;
        pcb->cb = NULL;
        if (num_time_callbacks == 0) {
          clerr("HOW WE HAVE NONE?");
        }
        num_time_callbacks--;

        /* Do the call */
        cb(ss, &tv, &now);
      }
    }
  }
}

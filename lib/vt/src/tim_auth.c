#include <string.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <dirent.h>

#include "logging.h"
#include "tim_auth.h"
#include "authlib.h"

#define TESTLAB_CERT_PATH "../certificates/timbox_v2/02 Test Lab Sub CA/c=DE,o=ISOBUS Test Center,cn=01FB#XDC.der"

#define TESTLAB_MANUFACTURER_CERT_PATH \
  "../certificates/timbox_v2/03 Test Lab Manufacturer Sub CA/c=US,o=CARBONROBOTICS,cn=01FB#04B5#XDC.der"

#define MANUFACTURER_CERT_PATH "../certificates/timbox_v2/04 Manufacturer Sub CA/"\
                               "2.5.4.65=014402550F,ou=04B5#18#01#01FB#0002,o=02#00#00#01#01#01#04B5.der"

#define MANUFACTURER_SERIES_CERT_PATH "../certificates/timbox_v2/04 Manufacturer Sub CA/"\
                                      "2.5.4.65=014402550F,ou=04B5#18#01#01FB#0002,o=02#00#00#01#01#01#04B5.der"

#define DEVICE_CERT_PATH "../certificates/timbox_v2/05 End-entity certificate/"\
                         "cn=x.der"

#define TESTLAB_SUB_VALUE "02"
#define TESTLAB_MANUFACTURER_SUB_VALUE "03"
#define MANUFACTURER_SUB_VALUE "04"
#define END_ENTITY_VALUE "05"

// This came from the openssl key generation step. The .pub was uploaded to AEF to make the certs.
#define TIMBOX_PRIVATE_KEY "F8930DC7578B77C44C03B2D783E47EFAFABA969124AB5AED6A30837FF53E5D4C"
char* privateKey = TIMBOX_PRIVATE_KEY;

static unsigned char *seed = (unsigned char *)"worldwarweeds";

#define SIGNED_CHALLENGE_LEN 16 // where does this come from?

struct tim_auth_state {
  unsigned char client_random_challenge_buf[CHALLENGE_LEN];
  unsigned char common_secret_buf[CHALLENGE_LEN];
  unsigned char server_random_challenge_buf[CHALLENGE_LEN];
  unsigned char signed_challenge[SIGNED_CHALLENGE_LEN];

  CryptoLib_Crt_t device;
  CryptoLib_Crt_t server_device;
};

static struct tim_auth_state tim_auth_state;

typedef struct cert_buffer {
  char *buf;
  int size;
} cert_buffer;

cert_buffer *ptestlab_cert = NULL;
cert_buffer *pmanufacturer_cert = NULL;
cert_buffer *pmanufacturer_series_cert = NULL;
cert_buffer *pdevice_cert = NULL;
cert_buffer *pserver_device_cert = NULL;

int cert_type(unsigned char *data) {
  return data[4];
}

cert_buffer *make_cert_buffer(int size) {
  cert_buffer *pbuf = (cert_buffer *)malloc(sizeof(cert_buffer));
  pbuf->size = size;
  pbuf->buf = (char *)malloc(size);
  return pbuf;
}

void destroy_cert_buffer(cert_buffer *p) {
  free(p->buf);
  free(p);
}

int load_certificate(const char *path, cert_buffer **pbuf) {
  int ret;
  struct stat statbuf;
  FILE *fp;

  ret = stat(path, &statbuf);
  if (ret != 0) {
    clerr("Failed to load cert: %s\n", path);
    return ret;
  }
  fp = fopen(path, "rb");
  if (!fp) {
    clerr("Failed to open cert: %s\n", path);
    return -1;
  }
  cldebug("Loading cert %d bytes\n", statbuf.st_size);
  *pbuf = make_cert_buffer(statbuf.st_size);
  int got = 0;
  while (got < (*pbuf)->size) {
    int ret = fread((*pbuf)->buf + got, 1, (*pbuf)->size - got, fp);
    if (ret < 0) {
      clerr("Failed buffer reading: %s\n", path);
      destroy_cert_buffer(*pbuf);
      *pbuf = NULL;
      return -1;
    }
    if (ret == 0) {
      clerr("Incomplete cert read: %s\n", path);
      destroy_cert_buffer(*pbuf);
      *pbuf = NULL;
      return -1;
    }
    got += ret;
  }
  fclose(fp);
  return 0;
}

int copy_certificate(unsigned char *data, int len, cert_buffer **pbuf) {
  *pbuf = make_cert_buffer(len);
  memcpy((*pbuf)->buf, data, len);
  return 0;
}

char *find_dir_prefix(const char *indir, const char *prefix) {
    struct dirent *entry;
    char *psubdir = NULL;
    DIR *dir = opendir(indir);

    if (!dir) {
      clerr("opendir failed");
      return NULL;
    }

    while ((entry = readdir(dir)) != NULL) {
      if (!strncmp(entry->d_name, prefix, strlen(prefix))) {
        // Construct full path
        int subdirlen = strlen(indir) + strlen("/") + strlen(entry->d_name) + 1;
        psubdir = (char *)malloc(subdirlen);
        if (indir[strlen(indir) - 1] == '/') {
          snprintf(psubdir, subdirlen, "%s%s", indir, entry->d_name);
        } else {
          snprintf(psubdir, subdirlen, "%s/%s", indir, entry->d_name);
        }

        // Check if it's a directory
        struct stat statbuf;
        if (stat(psubdir, &statbuf) == 0 && S_ISDIR(statbuf.st_mode)) {
            break;
        } else {
          free(psubdir);
          psubdir = NULL;
        }
      }
    }
    closedir(dir);
    return psubdir;
}

char *find_file_postfix(const char *indir, const char *postfix) {
    struct dirent *entry;
    char *ppath = NULL;
    DIR *dir = opendir(indir);

    if (!dir) {
      clerr("opendir failed");
      return NULL;
    }

    while ((entry = readdir(dir)) != NULL) {
      if (strlen(entry->d_name) >= strlen(postfix)) {
        if (!strncmp(&entry->d_name[strlen(entry->d_name) - strlen(postfix)], postfix, strlen(postfix))) {
          // Construct full path
          int pathlen = strlen(indir) + strlen("/") + strlen(entry->d_name) + 1;
          ppath = (char *)malloc(pathlen);
          if (indir[strlen(indir) - 1] == '/') {
            snprintf(ppath, pathlen, "%s%s", indir, entry->d_name);
          } else {
            snprintf(ppath, pathlen, "%s/%s", indir, entry->d_name);
          }

          // Check if it's a directory
          struct stat statbuf;
          if (stat(ppath, &statbuf) == 0 && S_ISREG(statbuf.st_mode)) {
              break;
          } else {
            free(ppath);
            ppath = NULL;
          }
        }
      }
    }
    closedir(dir);
    return ppath;
}

char *find_cert_in_dir(const char *certificate_dir, const char *prefix) {
  char *subpath = find_dir_prefix(certificate_dir, prefix);
  char *path;

  if (!subpath) {
    return NULL;
  }
  path = find_file_postfix(subpath, ".der");
  free(subpath);
  return path;
}

int load_certificates(const char *certificate_dir) {
  char *p;

  p = find_cert_in_dir(certificate_dir, TESTLAB_SUB_VALUE);
  if (!p || 0 != load_certificate(p, &ptestlab_cert)) {
    clerr("Failed to load TESTLAB CERT: %s\n", p);
    if (p)
      free(p);
    return -1;
  }
  free(p);

  p = find_cert_in_dir(certificate_dir, TESTLAB_MANUFACTURER_SUB_VALUE);
  if (!p || 0 != load_certificate(p, &pmanufacturer_cert)) {
    clerr("Failed to load TESTLAB MANUFACTURER CERT: %s\n", p);
    if (p)
      free(p);
    return -1;
  }
  free(p);

  p = find_cert_in_dir(certificate_dir, MANUFACTURER_SUB_VALUE);
  if (!p || 0 != load_certificate(p, &pmanufacturer_series_cert)) {
    clerr("Failed to load MANUFACTURER SERIES CERT: %s\n", p);
    if (p)
      free(p);
    return -1;
  }
  free(p);

  p = find_cert_in_dir(certificate_dir, END_ENTITY_VALUE);
  if (!p || 0 != load_certificate(p, &pdevice_cert)) {
    clerr("Failed to load DEVICE CERT: %s\n", p);
    if (p)
      free(p);
    return -1;
  }
  free(p);

  return 0;
}

void parse_cert(cert_buffer *pcert, CryptoLib_Crt_t *out) {
  AuthLib_ParseCertificate((unsigned char *)pcert->buf, pcert->size, out);
}

void tim_auth_init(const char *certificate_dir) {
  if (0 != load_certificates(certificate_dir)) {
    clerr("TIM certificate load failure.\n");
    return;
  }
  AuthLib_Init();
  AuthLib_InitCertificate(&tim_auth_state.device);
  parse_cert(pdevice_cert, &tim_auth_state.device);
}

int tim_auth_set_server_random_challenge(unsigned char *data, int len) {
  if (len != CHALLENGE_LEN) {
    clerr("CHALLENG_LEN is %d but handed %d bytes for server challenge\n", CHALLENGE_LEN, len);
  }
  memcpy(tim_auth_state.server_random_challenge_buf, data, CHALLENGE_LEN);
  return 0;
}

int tim_auth_set_server_device_certificate(unsigned char *data, int len) {
  copy_certificate(data, len, &pserver_device_cert);
  AuthLib_InitCertificate(&tim_auth_state.server_device);
  parse_cert(pserver_device_cert, &tim_auth_state.server_device);
  return 0;
}

int auth_random_challenge(unsigned char **pout, int *poutlen) {
  *poutlen = 0;
  *pout = NULL;
  int ret = AuthLib_GenerateRandomChallenge(seed, strlen((const char *)seed) + 1, tim_auth_state.client_random_challenge_buf);
  if (ret == 0) {
    *pout = tim_auth_state.client_random_challenge_buf;
    *poutlen = CHALLENGE_LEN;
  }
  return ret;
}

const char *common_secret_err(int err) {
  switch(err) {
    case 12:
      return "CRYPTO_ERR_BAD_INPUT";
      break;

    case 2:
      return "CRYPTO_ERR_CALL_AGAIN";
      break;

    default:
      return "UNKNOWN Bad Common Secret Err";
  }
}

int auth_signed_challenge(unsigned char **pout, int *poutlen) {
  *poutlen = 0;
  *pout = NULL;
  int ret;
  ret = AuthLib_ComputeCommonSecret(
                              // EC Private Key, null terminated, hex
                              (const unsigned char *)privateKey,

                              // EC Public Key, null terminated, hex
                              (const unsigned char *)tim_auth_state.server_device.subjectPublicKey.ecPublicKey,

                              // Random number provided by server (fixed 256 bit)
                              tim_auth_state.server_random_challenge_buf,

                              // Random number provided by client (fixed 256 bit) (generated by us above)
                              tim_auth_state.client_random_challenge_buf,

                              // OUT - LwA key on success (fixed 256 bit)
                              tim_auth_state.common_secret_buf,

                              // Algorithm
                              // CURVE_25519_KDF_ITK_HMAC_SHA256,
                              CURVE_25519_KDF_NIST_SP800_56A_HMAC_SHA256,

                              // Slicing
                              // SLICE_NO_SLICING
                              // SLICE_RESET
                              SLICE_INIT
                              );
  while (ret == 2) {
    cldebug("common secret slice continue ...\n");
    ret = AuthLib_ComputeCommonSecret(
                                // EC Private Key, null terminated, hex
                                (const unsigned char *)privateKey,

                                // EC Public Key, null terminated, hex
                                (const unsigned char *)tim_auth_state.server_device.subjectPublicKey.ecPublicKey,

                                // Random number provided by server (fixed 256 bit)
                                tim_auth_state.server_random_challenge_buf,

                                // Random number provided by client (fixed 256 bit) (generated by us above)
                                tim_auth_state.client_random_challenge_buf,

                                // OUT - LwA key on success (fixed 256 bit)
                                tim_auth_state.common_secret_buf,

                                // Algorithm
                                // CURVE_25519_KDF_ITK_HMAC_SHA256,
                                CURVE_25519_KDF_NIST_SP800_56A_HMAC_SHA256,

                                // Slicing
                                // SLICE_NO_SLICING
                                // SLICE_RESET
                                SLICE_CONTINUE
                                );
  }
  cldebug("compute common secret ret: %d\n", ret);
  if (ret != 0) {
    clerr("compute common secret err: %s\n", common_secret_err(ret));
  }

  ret = AuthLib_ComputeCMAC(tim_auth_state.server_random_challenge_buf,
                      tim_auth_state.common_secret_buf,
                      tim_auth_state.signed_challenge,
                      CRYPTO_CLIENT);
  cldebug("compute cmac ret: %d\n", ret);
  *pout = tim_auth_state.signed_challenge;
  *poutlen = SIGNED_CHALLENGE_LEN;
  return 0;
}

int get_testlab_cert(char **pout, int *sout) {
  if (!ptestlab_cert) {
    return -1;
  }

  *sout = ptestlab_cert->size;
  *pout = ptestlab_cert->buf;
  return 0;
}

int get_manufacturer_cert(char **pout, int *sout) {
  if (!pmanufacturer_cert) {
    return -1;
  }

  *sout = pmanufacturer_cert->size;
  *pout = pmanufacturer_cert->buf;
  return 0;
}

int get_manufacturer_series_cert(char **pout, int *sout) {
  if(!pmanufacturer_series_cert) {
    return -1;
  }
  *sout = pmanufacturer_series_cert->size;
  *pout = pmanufacturer_series_cert->buf;
  return 0;
}

int get_device_certificate(char **pout, int *sout) {
  if (!pdevice_cert) {
    return -1;
  }
  *sout = pdevice_cert->size;
  *pout = pdevice_cert->buf;
  return 0;
}

#include <errno.h>
#include <pthread.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <termios.h>
#include <unistd.h>

#include <sys/socket.h>

#include "can.h"
#include "dispatch.h"
#include "imageconvert.h"
#include "isostate.h"
#include "logging.h"
#include "network.h"
#include "object_pool.h"
#include "object_pool_builder_c.h"
#include "parse_config.h"
#include "schedule.h"
#include "util.h"
#include "can_gateway.h"
#include "modules.h"
#include "language_interface.h"
#include "tim.h"
#include "transport_buffer.h"
#include "transport.h"
#include "ack_nack.h"

#include "vendor_code.h"

#define LAYOUT_FILE "layout.yaml"

#define MAX_NUM_VTS 10

#define DO_REGISTER_ADDR 1

enum registered_state { UNREGISTERED, LISTENING, PROPOSING, REGISTERED };

typedef void (*thread_woke)(struct timeval *);

typedef struct thread_info {
  pthread_t thread;
  int wake_pipe[2];
  thread_woke on_wake;
} thread_info;

typedef struct can_state {
  /*
   * Networking and can address info
   */
  sock_pair ss;
  char *layout_dir;
  int registered_state;
  int addr;
  int vt_addrs[MAX_NUM_VTS];
  int num_vts;
  int vt_index;

  /*
   * managing our main object pool
   */
  int main_op_loaded;
  enum working_set_state working_set_state;
  int working_set_object_id;
  object_pool op;

  /*
   * Network bus thread management
   */
  thread_info bus_thread;

} can_state;

unsigned char claimed_addrs[255 - MIN_ADDR];

can_state g_iso_state = {
                     .layout_dir = NULL,
                     .registered_state = UNREGISTERED,
                     .working_set_state = NOSET,
                     .addr = 0,
                     .num_vts = 0,
                     .vt_index = 0,
                     .main_op_loaded = 0};

enum working_set_state get_working_set_state() {
  return g_iso_state.working_set_state;
}

void set_working_set_state(enum working_set_state state) {
  g_iso_state.working_set_state = state;
}

void unload_op() {
  // reset the whole iso state
  set_working_set_state(NOSET);
  g_iso_state.main_op_loaded = 0;
}

int get_iso_addr() {
  return g_iso_state.addr;
}

sock_pair get_ss() {
  return g_iso_state.ss;
}

int working_set_object_id() {
  return g_iso_state.working_set_object_id;
}

volatile int exiting = 0;

void async_exit() { exiting = 1; }

int vt_addr() { return g_iso_state.vt_addrs[g_iso_state.vt_index]; }

static struct can_opts can_opts; // global so we can pass it as thread args

/*
 * address management
 */
void vt_check_preferred() {
  if (get_preferred_vt()) {
    return;
  }
  for (int i = 0; i < g_iso_state.num_vts; i++) {
    if (g_iso_state.vt_addrs[i] == get_preferred_vt()) {
      g_iso_state.vt_index = i;
      break;
    }
  }
}

int add_vt_addr(int addr) {
  for (int i = 0; i < g_iso_state.num_vts; i++) {
    if (g_iso_state.vt_addrs[i] == addr) {
      return 0;
    }
  }
  g_iso_state.vt_addrs[g_iso_state.num_vts] = addr;
  g_iso_state.num_vts++;
  return addr;
}

int next_vt_addr() {
  g_iso_state.vt_index++;
  if (g_iso_state.vt_index == g_iso_state.num_vts) {
    g_iso_state.vt_index = 0;
  }
  return g_iso_state.vt_addrs[g_iso_state.vt_index];
}

void begin_new_screen(sock_pair *ss, struct timeval *now);

/* Main Flow */

/*
 * Every second we tell the vt about us and our status - this is required to keep connected.
 */
void periodic_working_set_maintenance(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
  if (g_iso_state.working_set_state == NONE) {
    cldebug("canceling working set maintenance\n");
    return;
  }
  schedule_relative(periodic_working_set_maintenance, scheduled, 1, 0);

  isopacket packet;
  int initializing = 0; // optionally may set this to 1 on initial call, not sure why we would though
  working_set_maintenance_packet(&packet, g_iso_state.addr, vt_addr(), initializing);
  send_packet(ss->write, &packet);

  if (g_iso_state.working_set_state == INITIALIZING) {
    isopacket packet;
    cldebug("Requesting hardware info.\n");
    g_iso_state.working_set_state = GET_HARDWARE;
    get_hardware_packet(&packet, g_iso_state.addr, vt_addr());
    send_packet(ss->write, &packet);
  }
}

void working_set_ack(sock_pair *ss, isopacket *p, unsigned char *data, int data_len, struct timeval *now) {
  print_ack_message(DEBUG, p->sa, data);
  if (ack_is_nack(data)) {
    if (g_iso_state.working_set_state == INITIALIZING) {
      clerr("Failed during working set initialization\n");
      g_iso_state.working_set_state = NOSET;
      exiting = 1;
    }
  }
}

void object_pool_transport_done(int code, transport_buffer *tbp, sock_pair *ss, struct timeval *now) {
  isopacket packet;
  end_of_object_pool_packet(&packet, get_iso_addr(), vt_addr());
  send_packet(ss->write, &packet);
}

void object_transport(sock_pair *ss, isopacket *p, unsigned char *data, int data_len, struct timeval *now) {
  cldebug("+ VT +++++++++++ %d ++++++++++++++++++++++++++\n", g_iso_state.addr);
  if (GET_HARDWARE_FUNCTION == vt_function(data)) {
    op_init(&g_iso_state.op, OBJECT_POOL_TRANSFER_FUNCTION);
    isopacket packet;

    hardware_datamask_resolution(data, &g_iso_state.op.vt_info.width, &g_iso_state.op.vt_info.height);

    cldebug("Calling parse config with w: %d, h: %d, vt %d\n", g_iso_state.op.vt_info.width,
            g_iso_state.op.vt_info.height, g_iso_state.op.vt_info.vt_version);

    /* Before we can check mem for object pool we need to parse it from the config. We can't do that
       until now because we just learned about the datamask width and height */
    int working_set_object_id = parse_config(g_iso_state.layout_dir, LAYOUT_FILE, &g_iso_state.op);
    if (!working_set_object_id) {
      clerr("failed to parse config, exiting\n");
      async_exit();
      return;
    }

    g_iso_state.working_set_object_id = working_set_object_id;

    g_iso_state.working_set_state = CHECK_MEM;
    object_pool *op = &g_iso_state.op;

    int bytes = op_memory_needed(op);
    cldebug("Checking for object pool space: %d\n", bytes);

    get_memory_packet(&packet, g_iso_state.addr, vt_addr(), bytes);
    send_packet(ss->write, &packet);
  } else if (GET_MEMORY_FUNCTION == vt_function(data)) {
    print_get_memory_response_message(DEBUG, p->sa, data);

    /* This is in response to our address validation message - we also use this to learn vt version */
    if (g_iso_state.working_set_state == NOSET) {
      hardware_vt_version(data, &g_iso_state.op.vt_info.vt_version);
      g_iso_state.working_set_state = INITIALIZING;
      schedule_relative(periodic_working_set_maintenance, now, 1, 0);

      cldebug("Declaring working set master\n");
      register_can_callback(ISOBUS_ACK_PGN, working_set_ack, 1);
      isopacket packet;
      working_set_master_packet(&packet, g_iso_state.addr, 1);
      send_packet(ss->write, &packet);
    }
    /* This is in response to our actual object pool request size */
    if (g_iso_state.working_set_state == CHECK_MEM) {
      if (can_support_memory(data)) {
        if (initiate_transport(ss, &g_iso_state.op.opb, get_iso_addr(), vt_addr(), ECU2VT_PDU << 8,
            object_pool_transport_done) < 0) {
          exiting = 1;
        }
      } else {
        clerr("No room for our object pool.\n");
      }
    }
  } else if (DELETE_OBJECT_POOL_FUNCTION == vt_function(data)) {
    print_delete_object_pool_response(DEBUG, p->sa, data);
    if (exiting) {
      exit(0);
    } else {
      /* Clean up the request queue */
      iso_request_done(ss);

      /* Restart the vt initialization */
      int new_addr = next_vt_addr();
      cldebug("Moving to next screen %d\n", new_addr);
      op_zero(&g_iso_state.op);
      begin_new_screen(ss, now);
    }
  } else if (OBJECT_POOL_DONE_FUNCTION == vt_function(data)) {
    cldebug("OBJECT POOL DONE from %d\n", p->sa);
    if (object_pool_error_codes(data)) { // errors on object pool
      clwarn("Object Pool Errors Detected!\n");
      print_end_of_object_pool_response(WARN, p->sa, data);

      /* We can get object pool errors during loading of things like replacement strings ...
       * this doesn't appear to cause any problems but the errors do show up. The
       * CHANGE STRING command comes back as successful, however.
       */
      if (!g_iso_state.main_op_loaded) {
        clerr("Errors on main object pool upload - exiting.\n");
        exiting = 1;
      }
    } else { // no errors on object pool
      g_iso_state.working_set_state = COMPLETE;

      if (!g_iso_state.main_op_loaded) {
        cldebug("main object pool is now loaded\n");
        op_free(&g_iso_state.op);

        /* If we just finished loading the main object pool then the python will be waiting for us */
        g_iso_state.main_op_loaded = 1;

        iso_request_done(ss); // XXXPAM - I think this is right, but need to test w python

      } else { // end main op load

        cldebug("non-main object pool load complete\n");
        iso_request_done(ss);

      } // end not main op load
    }   // no errors on object pool done
  } else if (BUTTON_ACTIVATION_FUNCTION == vt_function(data)) {
    print_vt_msg(DEBUG, p->sa, data);
    /* Check for internal ui object - if not then dispatch out to user */
    if (!trigger_ui_button(button_activation_id(data), button_activation_code(data))) {
      dispatch_button_activation(button_str_by_id(button_activation_id(data)), button_activation_code(data));
    }
  } else if (SOFTKEY_ACTIVATION_FUNCTION == vt_function(data)) {
    print_vt_msg(DEBUG, p->sa, data);
    dispatch_button_activation(button_str_by_id(button_activation_id(data)), button_activation_code(data));
  } else if (CHANGE_NUMERIC_VALUE_FUNCTION == vt_function(data)) {
    print_vt_msg(DEBUG, p->sa, data);
    dispatch_inputlist_select(input_list_str_by_id(input_list_id(data)),
                              input_list_select_str_by_index(input_list_id(data), input_list_index(data)));
  } else {

    user_vt_function(ss, vt_function(data));

    print_vt_msg(DEBUG, p->sa, data);
  }
}

/*
 * Step 5+: We should now be receiving packets to our claimed ISOBUS address.
 */

void handle_working_set_ack(sock_pair *ss, unsigned char *data, int data_len, struct timeval *now) {
  if (ack_is_nack(data) && g_iso_state.working_set_state == INITIALIZING) {
    clerr("Failed during working set initialization\n");
    g_iso_state.working_set_state = NOSET;
  }
}

void on_addr_callback(sock_pair *ss, isopacket *p, unsigned char *data, int data_len, struct timeval *now) {
  if (p->pgn == TRANSPORT_PROTOCOL_PGN || p->pgn == EXTENDED_TRANSPORT_PROTOCOL_PGN) {
    /* do not need to pass data here, this is always a raw can msg */
    int code;
    int handled = transport_message(ss, p, now, &code);
    if (!handled && code < 0) {
      clerr("FAIL TRANSPORT!!!!\n");
      // exit(0);
    }
  } else if (p->pgn == ISOBUS_ACK_PGN) {

    /* Have to do this here because this message is global and the dest addr gets overriden with the ps */
    /* XXX Double check this is true or not */
    if (data[0] == WORKING_SET_MASTER_PF) {
      handle_working_set_ack(ss, data, data_len, now);
    } else {
      if (dest_addr(p) == get_iso_addr()) {
        handle_ack_nack(ss, p, data, data_len, now);
      }
    }
  } else {
    print_packet(DEBUG_PROTO, p, data, data_len); // on addr callback
  }
}

/*
 * Step 4: We've claimed our address on the network, validate we can talk to the VT with get memory request.
 * We should receive this response directly to our newly claimed address.
 */
void validate_address(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
  cldebug("Address %d validated, registering vt callback for that address.\n", g_iso_state.addr);
  g_iso_state.registered_state = REGISTERED;
  register_can_addr(g_iso_state.addr, on_addr_callback);
}

void check_memory_callback(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
  cldebug("CHECK MEMORY\n");
  /* We do this to ensure we are actually on the network and also to learn the vt version before parsing */
  isopacket packet;
  cldebug("HELLO, CHECK MEMORY from %d\n", g_iso_state.addr);
  get_memory_packet(&packet, g_iso_state.addr, vt_addr(), 0);
  send_packet(ss->write, &packet);
}

void begin_new_screen(sock_pair *ss, struct timeval *now) {
  cldebug("BEGIN NEW SCREEN\n");

  /* we kick things off by making sure we can talk to the new screen */
  vt_check_preferred();

  cldebug("register can callback for VT2ECU_PGN for object_transport\n");
  register_can_callback(VT2ECU_PGN, object_transport, 1);
  schedule_relative(check_memory_callback, now, 1, 0);
}

/*
 * Keep track of registered callbacks for completion of address negotiation. This is a common place to want to do
 * something.
 */
#define MAX_ISO_CALLBACKS 10 // XXX lazy
typedef struct address_callbacks {
  int num_callbacks;
  isocallback callbacks[MAX_ISO_CALLBACKS];
} address_callbacks;

address_callbacks g_addr_callbacks = {.num_callbacks = 0};

int register_address_callback(isocallback cb) {
  if (g_addr_callbacks.num_callbacks == MAX_ISO_CALLBACKS)
    return -1;
  g_addr_callbacks.callbacks[g_addr_callbacks.num_callbacks] = cb;
  g_addr_callbacks.num_callbacks++;
  return 0;
}

void fire_addr_callbacks(sock_pair *ss, struct timeval *now) {
  for (int i = 0; i < g_addr_callbacks.num_callbacks; i++) {
    g_addr_callbacks.callbacks[i](ss, now);
  }
}

/*
 * Step 3: We've waited long enough for all responders to the address claimed request, now we
 * claim our own address.
 */
void claim_address(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
  cldebug("Searching for unused addresses:\n");
  for (int i = 0; i < 255 - MIN_ADDR; i++) {
    cldebug("Checking %d ...", i + MIN_ADDR);
    if (claimed_addrs[i]) {
      cldebug("Conflict at %d\n", i + MIN_ADDR);
    } else {
      cldebug("%d -> AVAILABLE.\n", i + MIN_ADDR);
      g_iso_state.addr = i + MIN_ADDR;
      break;
    }
  }
  cldebug("claim %d\n", g_iso_state.addr);
  isopacket packet;

  /*
   * Claim our new address and maintain it via the on addr callback
   */
  g_iso_state.registered_state = PROPOSING;

  request_address_claimed(&packet, NULL_ADDRESS);
  send_packet(ss->write, &packet);

  claim_address_packet(&packet, g_iso_state.addr);
  send_packet(ss->write, &packet);

  validate_address(ss, scheduled, now);

  /*
   * Address done, fire everyone waiting on us.
   */
  fire_addr_callbacks(ss, now);
}

/*
 * Step 2: Keep track of who is on network and look for conflitcs. Find an addr not in use.
 */
void address_claimed_callback(sock_pair *ss, isopacket *p, unsigned char *data, int data_len, struct timeval *now) {
  /* We need to check for conflift with our address during PROPOSING */
  cldebug("Address claimed: %d\n", p->sa - MIN_ADDR);
  if (g_iso_state.registered_state == LISTENING) {
    if (p->sa <= MIN_ADDR) {
      claimed_addrs[p->sa - MIN_ADDR] = 1;
    }
  }
}

/*
 * Step 1: wait for VT status message, then ask for who is on network, set timeout for responders.
 */
void delete_object_pool() {
  isopacket packet;
  delete_object_pool_packet(&packet, g_iso_state.addr, vt_addr());
  send_packet(g_iso_state.ss.write, &packet);
}

void send_request_claimed(sock_pair *ss) {
    isopacket packet;
    cldebug("Asking for who is there on what addresses ...\n");
    request_address_claimed(&packet, NULL_ADDRESS);
    send_packet(ss->write, &packet);
}

void isobus_compliance_callback(sock_pair *ss, isopacket *p, unsigned char *data, int data_len, struct timeval *now) {
  cldebug("ISOBUS compliance message\n");
}

void isobus_request_callback(sock_pair *ss, isopacket *p, unsigned char *data, int data_len, struct timeval *now) {
  /* If request and coming to me or global address and we are registered on the network */
  if ((p->ps == get_iso_addr() || p->ps == GLOBAL_ADDRESS) && g_iso_state.registered_state == REGISTERED) {

    int pgn = get_request_pgn(data);

    if (pgn == ADDRESS_CLAIMED_PGN) {
      cldebug("Asking for address claimed to addr %d [0x%x], responding with ours: %d\n", p->ps, p->ps, get_iso_addr());
      isopacket packet;
      claim_address_packet(&packet, get_iso_addr());
      send_packet(ss->write, &packet);
    } else if (pgn == ISOBUS_COMPLIANCE_CERTIFICATION_PGN) {
      cldebug("Sending ISOBUS certification\n");
      send_isobus_certification(ss);
    } else {
      cldebug("Requesting for pgn %d [0x%x] to addr %d [0x%x] that I don't handle\n", pgn, p->ps, p->ps);
    }
  }
}

void start_addressing(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
#if DO_REGISTER_ADDR
  if (g_iso_state.registered_state == UNREGISTERED) {
    g_iso_state.registered_state = LISTENING;
    memset(claimed_addrs, 0, 255 - MIN_ADDR);
    if (g_iso_state.addr == 0) {
      g_iso_state.addr = MIN_ADDR;
    }

    /* Request everybody to respond with their claimed addresses and then one second from now we will
       claim ours .*/
    send_request_claimed(ss);

    cldebug("scheduling claim check in 1 second\n");
    schedule_relative(claim_address, now, 1, 0);
  }
#else
  g_iso_state.addr = MIN_ADDR;
  register_can_addr(g_iso_state.addr, on_addr_callback);
  clg(DEBUG, "fire addr callbacks, no register addr\n");
  fire_addr_callbacks(ss, now);
#endif
}

void vt_callback(sock_pair *ss, isopacket *p, unsigned char *data, int data_len, struct timeval *now) {
  cldebug("vt callback\n");
  if (exiting == 1) {
    cldebug("REMOVING OBJECT POOL!\n");
    delete_object_pool();
    exiting = 2;
  }

  if (VT_STATUS_FUNCTION == vt_function(data)) {
    if (add_vt_addr(p->sa)) {
      clinfo("seeing vt %d\n", p->sa);
    }
  }
}

void transfer_op_callback(struct timeval *now) { next_outstanding_command(&g_iso_state.ss); }

/* Steal the ^C handler. Would prefer to let the higher level language do this, but python needs the GIL
 * and won't be able to acquire when we are in can loop */
void control_c_handler(int signum) {
  clinfo("^C!\n");
  async_exit();
}

void thread_triggered(struct timeval *now, void *arg) {
  thread_info *tif = (thread_info *)arg;
  unsigned char wake_buf = 0;
  int read_count;
  read_count = read(tif->wake_pipe[0], &wake_buf, 1);
  if (read_count <=0 ) {
    clerr("Failed to read wake pipe!\n");
  }
  tif->on_wake(now);
}

void trigger_thread(thread_info *tif) {
  unsigned char wake_buf = 0;
  int write_count;
  write_count = write(tif->wake_pipe[1], &wake_buf, 1);
  if (write_count <= 0) {
    clerr("Failed to write wake pipe!\n");
  }
}

void *thread_can_loop(void *arg) {
  struct can_opts *can_opts = (struct can_opts *)arg;
  wake_info wakes[2];

  /* schedule level */
  wakes[0].p = g_iso_state.bus_thread.wake_pipe[0];
  wakes[0].wake_cb = thread_triggered;

  /* thread trigger level */
  wakes[0].arg = (void *)&g_iso_state.bus_thread;
  g_iso_state.bus_thread.on_wake = transfer_op_callback;

  /* start and never return */
  can_loop(&g_iso_state.ss, wakes, 1, can_opts);

  return arg;
}

void trigger_can() { trigger_thread(&g_iso_state.bus_thread); } // transfer_op_callback will be called

/****************************************************************************************************************
 Calldowns from python land.
 ****************************************************************************************************************/

int start_isobus(struct isobus_options *opts) {
  if (clg_init(opts->level, opts->log_filename) < 0) {
    printf("Failed to initialize logging to file %s\n", opts->log_filename);
    return 0;
  }

  if (opts->options & TRACE_PACKETS) {
    can_opts.trace_packets = 1;
  } else {
    can_opts.trace_packets = 0;
  }

  g_iso_state.ss.read = 0;
  g_iso_state.ss.write = 0;
  /* Basic can networking */
  if (opts->ip != NULL) {
    can_opts.fpacket = cangateway_read;
    can_opts.fencode = cangateway_encode;
    cldebug("opening %s:%d\n", opts->ip, opts->port);
    g_iso_state.ss.read = priv_openSocketIn(opts->port, SOCK_DGRAM, 10);
    g_iso_state.ss.write = socket(AF_INET, SOCK_DGRAM, 0);
    if (connect_udp(g_iso_state.ss.write, opts->ip, opts->port) < 0) {
      clerr("Failed udp connect -> %s:%d\n", opts->ip, opts->port);
    }
  } else {
#ifdef HAS_LINUX_CAN
    can_opts.fpacket = NULL;
    can_opts.fencode = NULL;
    g_iso_state.ss.read = open_can_if(opts->can_id);
    g_iso_state.ss.write = g_iso_state.ss.read;
#else
    clerr("No linux can available.\n");
    exit(0);
#endif
  }

  op_zero(&g_iso_state.op);

  /* Optional UI VT UI */
  if (opts->options & DO_VT && opts->layout_dir) {
    cldebug("opening layout dir: %s\n", opts->layout_dir);
    g_iso_state.layout_dir = (char *)malloc(strlen(opts->layout_dir) + 1);
    strcpy(g_iso_state.layout_dir, opts->layout_dir);

    /* Once we have our address then start the vt */
    register_address_callback(begin_new_screen);

    /* Capture early vt messages to learn who is out there */
    register_can_callback(VT2ECU_PGN, vt_callback, 0); // call this one on global

    /* install clean isobus vt shutdown handler */
    signal(SIGINT, control_c_handler);

  } else {
    g_iso_state.layout_dir = NULL;
  }
  
  /* LANGUAGES INTERFACE */

  init_language_interface();

  /* Multi process async handling between higher level language and us */
  if (0 != pipe(g_iso_state.bus_thread.wake_pipe)) {
    clerr("Failed to create wake pipe.\n");
    return 0;
  }

  /* MODULES INTERFACE */

  /* Intialize various isobus dependent modules */
  modules_bootstrap(opts);

  if (opts->options & DO_VT || opts->options & DO_TIM || opts->options & DO_TECU) {
    /* Schedule callback to initialize j1939 addressing - this needs to run on can thread, so schedule it */
    struct timeval now;
    gettimeofday(&now, NULL);
    schedule_relative(start_addressing, &now, 1, 0);
  }

  register_can_callback(ISOBUS_COMPLIANCE_CERTIFICATION_PGN, isobus_compliance_callback, 0);

  /* Need this to track responses to our address claims */
  register_can_callback(ADDRESS_CLAIMED_PGN, address_claimed_callback, 0);

  /* Need to handle address claimed requests to the global address */
  register_can_callback(REQUEST_PGN, isobus_request_callback, 0);

  if (0 != pthread_create(&g_iso_state.bus_thread.thread, NULL, thread_can_loop, (void *)&can_opts)) {
    clerr("Failed to start thread for can loop.\n");
    return 0;
  }

  return 1;
}

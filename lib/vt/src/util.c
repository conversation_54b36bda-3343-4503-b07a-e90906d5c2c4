#include <sys/time.h>
#include <stdio.h>
#include "util.h"

uint64_t tv2usec(struct timeval *tv) { return (uint64_t)tv->tv_sec * (1000 * 1000) + tv->tv_usec; }

int msdiff(struct timeval *later, struct timeval *earlier) {
	// Calculate the difference in seconds and microseconds
	time_t sec_diff = later->tv_sec - earlier->tv_sec;
	suseconds_t usec_diff = later->tv_usec - earlier->tv_usec;

	// Adjust the differences if microseconds are negative
	if (usec_diff < 0) {
		sec_diff -= 1;
		usec_diff += 1000000;
	}

	// Convert the total difference to milliseconds
	int ms_difference = (sec_diff * 1000) + (usec_diff / 1000);
	return ms_difference;
}

int max(int a, int b) {
  if (a > b) {
    return a;
  }
  return b;
}

int hexval(unsigned char c) {
  if (c >= 'a' && c <= 'f') {
    return 10 + c - 'a';
  }
  if (c >= 'A' && c <= 'F') {
    return 10 + c - 'A';
  }
  if (c >= '0' && c <= '9') {
    return c - '0';
  }
  return -1;
}

unsigned char hexbyte(unsigned char *pinhex) {
  return (hexval(pinhex[0]) << 4) | hexval(pinhex[1]);
}

void hexstr2bin(unsigned char *pinhex, unsigned char *pout, int *pout_fill) {
  *pout_fill = 0;
  while (*pinhex != '\0') {
    *pout = hexbyte(pinhex);
    pout++;
    (*pout_fill)++;

    pinhex++;
    if (!*pinhex) {
      break;
    }
    pinhex++;
  }
}

char hexchar(unsigned char c) {
  if (c < 10) {
    return '0' + c;
  }
  return c - 10 + 'A';
}

void bytehex(unsigned char *pout, unsigned char v) {
  pout[0] = hexchar(v >> 4);
  pout[1] = hexchar(v & 0xf);
}

void bin2hexstr(unsigned char *pinbin, int inbinsize, unsigned char *pout) {
  for (int i = 0; i < inbinsize; i++) {
    bytehex(pout, pinbin[i]);
    pout += 2;
  }
  *pout = '\0';
}

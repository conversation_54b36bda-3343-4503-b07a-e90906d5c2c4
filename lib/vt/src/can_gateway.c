#include <stdio.h>
#include <string.h>
#include <arpa/inet.h>

#include "can.h"

// from host_rx.c example code
#define DLC_OFFSET 21
#define DATA_OFFSET 28
#define FIXED_PACKET_SIZE 36
#define CHANNEL_OFFSET 20
#define FLAGS_OFFSET 22
#define ID_OFFSET 24
#define CLASSIC_CAN 0x80

void cangateway_read(unsigned char *pbuf, int len, isopacket *pac, int *pconsumed) {
  if (len <= 0) {
    *pconsumed = 0;
    return;
  }

  int size = ntohs(*(uint16_t*)pbuf);
  if (size != FIXED_PACKET_SIZE) {
    clerr("bad can gateway msg length!\n");
    *pconsumed = -1;
    return;
  }

  /* parse out can gateway fields */
  int dlc = pbuf[DLC_OFFSET];
  int flags = ntohs(*(uint16_t*)&pbuf[FLAGS_OFFSET]);
  int id = ntohl(*(uint32_t*)&pbuf[ID_OFFSET]);

  /* convert fields */
  pac->sof = 1; // not sure if this can always be 1
  pac->priority = 0; // not sure if this can always be 0

  pac->edp = (id & (1 << 25)) >> 25;
  pac->dp = (id & (1 << 24)) >> 24;
  pac->pf = (id & (0xff << 16)) >> 16;
  pac->priority = (id & (0x7 << 26)) >> 26;
  pac->ps = (id >> 8) & 0xff;
  pac->sa = id & 0xff;
  /* ISO783-3 PG6
   * The procedure for the bit fields to be converted to PGN is as follows. The six MSB of the PGN are set
   * to zero. Then the Extended Data Page bit, Data Page bit and PDU Format field are copied into the next
   * 10 bit. If the PF value is less than 240 (F016) then the LSB of the PGN is set to zero. Otherwise, it
   * is set to the value of the PS Field
   */
  pac->pgn = (pac->pf << 8) | (pac->dp << 16) | (pac->edp << 17);
  if (pac->pf >= 240) { // only for setting the pgn
    pac->pgn |= pac->ps;
  }

  memset(pac->data, 0, 8);
  memcpy(pac->data, &pbuf[DATA_OFFSET], dlc);
  pac->data_len = dlc;

  *pconsumed = size;

  /*
  cldebug("CGR: ");
  print_hexdata(DEBUG, pbuf, FIXED_PACKET_SIZE);
  cldebug("\n");
  */
}

int cangateway_encode(unsigned char *pbuf, int len, isopacket *pac) {
  *(uint16_t*)&(pbuf[0]) = htons(FIXED_PACKET_SIZE);
  *(uint16_t*)&(pbuf[2]) = htons(CLASSIC_CAN);
  *(uint64_t*)&(pbuf[4]) = 0; // tag not used
  *(uint64_t*)&(pbuf[12]) = 0; // timestamp don't care
  pbuf[CHANNEL_OFFSET] = 0; // channel doesn't matter but is actually 0
  pbuf[DLC_OFFSET] = pac->data_len;
  *(uint16_t*)&(pbuf[FLAGS_OFFSET]) = htons(0x2); // extended ID, no rtr

  int id = (pac->sa & 0xff) | (pac->ps << 8) | ((pac->pf & 0xff) << 16) | 
            ((pac->dp & 0x1) << 24) | ((pac->edp & 0x1) << 25) | (pac->priority & 0x7) << 26;
  id |= 1 << 31; // extended frame (yes, here too)
  *(uint32_t*)&(pbuf[ID_OFFSET]) = htonl(id);

  memset(&pbuf[DATA_OFFSET], 0, 8);
  memcpy(&pbuf[DATA_OFFSET], pac->data, pac->data_len);

  /*
  cldebug("CGW: ");
  print_hexdata(DEBUG, pbuf, FIXED_PACKET_SIZE);
  cldebug("\n");
  */

  return FIXED_PACKET_SIZE;
}

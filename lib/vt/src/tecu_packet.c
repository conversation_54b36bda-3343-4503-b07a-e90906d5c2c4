#include <string.h>
#include <arpa/inet.h>

#include "can.h"
#include "tecu_packet.h"

void tecu_pac(isopacket *pac, int addr) {
  pac->edp = pac->dp = 0;
  pac->sof = 1;
  pac->sa = addr;
  pac->priority = 3;
  memset(pac->data, 0, 8);
  pac->data_len = 8;
}

void machine_selected_speed_request_packet(isopacket *pac, int addr, int mm_per_s) {
  tecu_pac(pac, addr);
  pac->pf = MACHINE_SELECTED_SPEED_PGN >> 8;
  pac->ps = MACHINE_SELECTED_SPEED_PGN & 0xFF;
/*
11783-7
Numerical parameters consisting of two or more data bytes shall be transmitted least significant byte first.

PM Note: This is opposite of network byte ordering
*/
  *(uint16_t*)&pac->data[0] = mm_per_s;
  *(uint16_t*)&pac->data[2] = 0xFFFF;   // set point limit
  *(uint16_t*)&pac->data[4] = 0xFFFF;   // reserved
  pac->data[6] = 0xFF;   // reserved
  pac->data[7] = (unsigned char)(0xFF << 2);
  pac->data[7] |= 0x01;
}

void machine_selected_speed_relinquish_packet(isopacket *pac, int addr) {
  machine_selected_speed_request_packet(pac, addr, 0xFFFF);
}

void print_machine_selected_speed_packet(enum log_level level, unsigned char sa, unsigned char *pdata) {
  clg(level, "Machine selected speed: set point %d, set point limit %d\n",
      *(uint16_t*)&pdata[0], *(uint16_t*)&pdata[2]);
}

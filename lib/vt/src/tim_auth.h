#define DER_BINARY_FORMAT 0x00

#define TESTLAB_CERTIFICATE 0x01
#define TESTLAB_BIT 5

#define MANUFACTURER_CERTIFICATE 0x02
#define MANUFACTURER_BIT 4

#define MANUFACTURER_SERIES_CERTIFICATE 0x03
#define MANUFACTURER_SERIES_BIT 3

#define DEVICE_CERTIFICATE 0x04
#define DEVICE_BIT 2

#define CRL_SIGNING_CERTIFICATE 0x05
#define CRL_CERTIFICATE 0x06
#define CRL_SIGN_SUB_CA 0x07


void tim_auth_init(const char *certificate_dir);
int auth_random_challenge(unsigned char **pout, int *poutlen);
int auth_signed_challenge(unsigned char **pout, int *poutlen);
int cert_type(unsigned char *data);

int get_testlab_cert(char **pout, int *sout);
int get_manufacturer_cert(char **pout, int *sout);
int get_manufacturer_series_cert(char **pout, int *sout);
int get_device_certificate(char **pout, int *sout);

int tim_auth_set_server_random_challenge(unsigned char *data, int len);
int tim_auth_set_server_device_certificate(unsigned char *data, int len);

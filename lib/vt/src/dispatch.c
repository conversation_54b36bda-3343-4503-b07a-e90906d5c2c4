#include <stdio.h>
#include <stdint.h>
#include <sys/time.h>

#include "network.h"
#include "isostate.h"
#include "dispatch.h"

f_button button_func = NULL;
f_inputlist inputlist_func = NULL;
f_speed speed_func = NULL;
f_hitch hitch_func = NULL;
f_wakeup wakeup_user_func = NULL;

/* Wakeup Dispatch */
void set_wakeup_dispatch(f_wakeup fw) { wakeup_user_func = fw; }

void dispatch_wakeup() {
  if (wakeup_user_func != NULL) {
    wakeup_user_func();
  }
}

/* Button Dispatch */

void set_button_dispatch(f_button fb) { button_func = fb; }

void dispatch_button_activation(const char *name, int code) {
  if (button_func != NULL) {
    button_func(name, code);
  }
}

/* Input List Dispatch */

void set_inputlist_dispatch(f_inputlist fil) { inputlist_func = fil; }

void dispatch_inputlist_select(const char *name, const char *value) {
  if (inputlist_func != NULL) {
    inputlist_func(name, value);
  }
}

/* Speed Dispatch */
void set_speed_dispatch(f_speed f) { speed_func = f; }

void dispatch_speed_update(float speed, uint64_t ts_us) {
  if (speed_func != NULL) {
    speed_func(speed, ts_us);
  }
}

/* Hitch Dispatch */
void set_hitch_dispatch(f_hitch f) { hitch_func = f; }

void dispatch_hitch_position(int position, uint64_t ts_us) {
  if (hitch_func != NULL) {
    hitch_func(position, ts_us);
  }
}

#include <stdlib.h>
#include <string.h>

#include "logging.h"
#include "transport_buffer.h"

#define BLUE 9 // just used for default text bg color, should be unused because transparent background


/* making */


/* maintenance */

void opb_zero(transport_buffer *opb) { memset(opb, 0, sizeof(*opb)); }

void opb_reset(transport_buffer *opb) {
  opb->used = 0;
  opb->next_packet = 0;
  opb->transferred = 0;
  opb->packets_sent = 0;
}

void opb_reset_sized(transport_buffer *opb, int size) {
  /* If we're increasing the size then free for realloc */
  if (size > opb->size) {
    opb->size = size;
    if (opb->pbuf != NULL) {
      free(opb->pbuf);
      opb->pbuf = NULL;
    }
  }
  /* If we have no buffer yet then make one */
  if (opb->pbuf == NULL) {
    opb->pbuf = (unsigned char *)malloc(opb->size);
  }
  /* reset all the state */
  opb_reset(opb);
}

transport_buffer *make_transport_buffer() {
  transport_buffer *opb = (transport_buffer *)malloc(sizeof(transport_buffer));
  opb_init(opb);
  return opb;
}

transport_buffer *make_transport_buffer_sized(int size) {
  transport_buffer *p = make_transport_buffer();
  opb_reset_sized(p, size);
  return p;
}

void opb_init(transport_buffer *opb) {
  opb_zero(opb);
  opb_reset_sized(opb, DEFAULT_OPB_SIZE);
}

void opb_free(transport_buffer *opb) {
  free(opb->pbuf);
  opb->pbuf = NULL;
  opb->size = 0;
  opb->used = 0;
}

void check_buf(transport_buffer *opb, int len) {
  /* if we would overrun our buffer then double the size */
  while (opb->size < opb->used + len) {
    unsigned char *newbuf = (unsigned char *)malloc(opb->size * 2);
    memcpy(newbuf, opb->pbuf, opb->used);
    free(opb->pbuf);
    opb->pbuf = newbuf;
    opb->size = opb->size * 2;
  }
}

/* buf level builders ******************************************************************/

void opb_add16(transport_buffer *opb, uint16_t v) {
  check_buf(opb, 2);
  *(uint16_t *)(opb->pbuf + opb->used) = v;
  opb->used += 2;
}

void opb_add8(transport_buffer *opb, uint8_t v) {
  check_buf(opb, 1);
  *(uint8_t *)(opb->pbuf + opb->used) = v;
  opb->used += 1;
}

void opb_add32(transport_buffer *opb, uint32_t v) {
  check_buf(opb, 4);
  *(uint32_t *)(opb->pbuf + opb->used) = v;
  opb->used += 4;
}

void opb_addbytes(transport_buffer *opb, const char *bytes, int len) {
  check_buf(opb, len);
  memcpy(opb->pbuf + opb->used, bytes, len);
  opb->used += len;
}

int opb_memory_needed(transport_buffer *opb) { return opb->used - opb->transferred; }
int opb_transport_done(transport_buffer *opb) { return opb->packets_sent == 0xff; }

/* isobus data types */
void opb_add_string(transport_buffer *opb, int my_id, int var_id, const char *s, int width, int height,
                    uint16_t font_attributes, int justification) {
  opb_add16(opb, my_id);
  opb_add8(opb, STRING_TYPE);
  opb_add16(opb, width);           // max pixel width
  opb_add16(opb, height);          // max pixel height
  opb_add8(opb, BLUE);             // bg color
  opb_add16(opb, font_attributes); // font attributes
  opb_add8(opb, 0b11);             // options - transparent background and autowrap
  opb_add16(opb, var_id);          // variable reference (NULL)
  opb_add8(opb, justification);    // justification
  if (var_id == NULL_OBJECT_ID) {
    /* XXX For reasons unknown, strings that can be modified need an extra space padding (no this is not
     * just for making them longer later on. */
    opb_add16(opb, strlen(s) + 1); // length - pad one space
    opb_addbytes(opb, s, strlen(s));
    opb_add8(opb, ' ');
  } else {
    opb_add16(opb, 0); // length
  }
  opb_add8(opb, 0); // number of macros to follow
}

void opb_set_string(transport_buffer *opb, int my_id, const char *s) {
  opb_add16(opb, my_id);
  opb_add16(opb, strlen(s) + 1);
  opb_addbytes(opb, s, strlen(s));
  opb_add8(opb, ' ');
}

void opb_add_string_var(transport_buffer *opb, int var_id, const char *s) {
  opb_add16(opb, var_id);
  opb_add8(opb, 22);         // string variable
  opb_add16(opb, strlen(s)); // length
  opb_addbytes(opb, s, strlen(s));
}

void opb_add_data_mask(transport_buffer *opb, int object_id, int bg_color, int soft_key_mask_id, int objects) {
  opb_add16(opb, object_id);
  opb_add8(opb, DATA_MASK_TYPE);
  opb_add8(opb, bg_color);
  opb_add16(opb, soft_key_mask_id); // soft key mask

  opb_add8(opb, objects); // objects to follow
  opb_add8(opb, 0);       // macros to follow
}

void opb_add_button(transport_buffer *opb, int object_id, int width, int height, int bg_color, int border_color, int key_code,
                    int options, int objects) {
  opb_add16(opb, object_id);
  opb_add8(opb, 6); // button type
  opb_add16(opb, width);
  opb_add16(opb, height);
  opb_add8(opb, bg_color);
  opb_add8(opb, border_color);
  opb_add8(opb, key_code);
  opb_add8(opb, options);
  opb_add8(opb, objects);
  opb_add8(opb, 0); // macros to follow
}

void opb_add_container(transport_buffer *opb, int object_id, int width, int height, int objects) {
  opb_add16(opb, object_id);
  opb_add8(opb, 3); // container
  opb_add16(opb, width);
  opb_add16(opb, height);
  opb_add8(opb, 0); // hidden
  opb_add8(opb, objects);
  opb_add8(opb, 0); // macros to follow
}

void opb_add_input_list(transport_buffer *opb, int object_id, int width, int height, int options, int objects, int selected) {
  opb_add16(opb, object_id);
  opb_add8(opb, 10); // input list type
  opb_add16(opb, width);
  opb_add16(opb, height);
  opb_add16(opb, NULL_OBJECT_ID); // variable reference (NULL)
  opb_add8(opb, selected);        // 0 indexed value of which one is selected
  opb_add8(opb, objects);
  opb_add8(opb, options);
  opb_add8(opb, 0); // macros to follow
}

void opb_add_font_attributes(transport_buffer *opb, int object_id, int color, int size, int style) {
  opb_add16(opb, object_id);
  opb_add8(opb, FONT_ATTRIBUTES_TYPE);
  opb_add8(opb, color); // text colour, ISO11783-6 table A.4
  opb_add8(opb, size);  // font size - 4 = 16 x 16
  opb_add8(opb, 0);     // Font type - 0 = ISO Latin 1
  opb_add8(opb, style); // font style
  opb_add8(opb, 0);     // number of macros to follow
}

void opb_add_picture_graphic(transport_buffer *opb, int my_id, unsigned char *bytes, int width, int height) {
  opb_add16(opb, my_id);
  opb_add8(opb, PICTURE_GRAPHIC_TYPE);
  opb_add16(opb, width);          // target width
  opb_add16(opb, width);          // actual width
  opb_add16(opb, height);         // actual height
  opb_add8(opb, 2);               // 8 bit color
  opb_add8(opb, 0);               // options = opaque, normal (not flashing), raw data
  opb_add8(opb, 0);               // transparency color
  opb_add32(opb, width * height); // number of bytes
  opb_add8(opb, 0);               // macros to follow
  opb_addbytes(opb, (const char *)bytes, width * height);
}

void opb_add_soft_key(transport_buffer *opb, int my_id, int bg_color, int objects) {
  opb_add16(opb, my_id);
  opb_add8(opb, KEY_TYPE);
  opb_add8(opb, bg_color); // key color
  opb_add8(opb, my_id);    // key code
  opb_add8(opb, objects);  // number of objects
  opb_add8(opb, 0);        // macros to follow
}

void opb_add_soft_key_mask(transport_buffer *opb, int my_id, int bg_color, int *button_ids, int num_buttons) {
  opb_add16(opb, my_id);
  opb_add8(opb, SOFT_KEY_MASK_TYPE);
  opb_add8(opb, bg_color);    // background color
  opb_add8(opb, num_buttons); // number of objects to follow
  opb_add8(opb, 0);           // number of macros to follow
  for (int i = 0; i < num_buttons; i++) {
    opb_add16(opb, button_ids[i]);
  }
}

void opb_add_line_attributes(transport_buffer *opb, int id, int color, int thickness, int pattern) {
  opb_add16(opb, id);
  opb_add8(opb, 24); // line attributes
  opb_add8(opb, color);
  opb_add8(opb, thickness); // or also called width
  opb_add16(opb, pattern);
  opb_add8(opb, 0); // number of macros
}

void opb_add_fill_attributes(transport_buffer *opb, int id, int color) {
  opb_add16(opb, id);
  opb_add8(opb, 25); // fill attributes
  opb_add8(opb, 2);  // fill with color attribute
  opb_add8(opb, color);
  opb_add16(opb, NULL_OBJECT_ID); // fill pattern, ignored if not type 3
  opb_add8(opb, 0);               // number of macros
}

void opb_add_rectangle(transport_buffer *opb, int id, int line_attributes, int fill_attributes, int width, int height) {
  opb_add16(opb, id);
  opb_add8(opb, 14); // rectangle
  opb_add16(opb, line_attributes);
  opb_add16(opb, width);
  opb_add16(opb, height);
  opb_add8(opb, 0); // closed rectangle - see Line Suppression
  if (fill_attributes <= 0) {
    opb_add16(opb, NULL_OBJECT_ID); // fill attributes
  } else {
    opb_add16(opb, fill_attributes);
  }
  opb_add8(opb, 0); // number of macros
}

void opb_add_line(transport_buffer *opb, int id, int line_attributes, int width, int height, int direction) {
  opb_add16(opb, id);
  opb_add8(opb, 13);
  opb_add16(opb, line_attributes);
  opb_add16(opb, width);
  opb_add16(opb, height);
  opb_add8(opb, direction);
  opb_add8(opb, 0); // macros to add
}

void opb_add_checkbox(transport_buffer *opb, int id, int font_id, int bg_color, int width) {
  opb_add16(opb, id);
  opb_add8(opb, 7); // checkbox
  opb_add8(opb, bg_color);
  opb_add16(opb, width);
  opb_add16(opb, font_id);
  opb_add16(opb, NULL_OBJECT_ID); // variable reference
  opb_add8(opb, 0);               // default unchecked
  opb_add8(opb, 1);               // enabled
  opb_add8(opb, 0);               // number of macros
}

void opb_add_meter(transport_buffer *opb, int id, int width, int needle_color, int border_color, int arc_color, int options,
                   int ticks_count, int start_angle, int end_angle, int min_value, int max_value, int value) {
  opb_add16(opb, id);
  opb_add8(opb, 17); // output meter
  opb_add16(opb, width);
  opb_add8(opb, needle_color);
  opb_add8(opb, border_color);
  opb_add8(opb, arc_color);
  opb_add8(opb, options);
  opb_add8(opb, ticks_count);
  opb_add8(opb, start_angle / 2); // spec says these angles are / 2
  opb_add8(opb, end_angle / 2);
  opb_add16(opb, min_value);
  opb_add16(opb, max_value);
  opb_add16(opb, NULL_OBJECT_ID); // variable reference
  opb_add16(opb, value);
  opb_add8(opb, 0); // number of macros
}

/* Packetization */
void opb_set_start_packet(transport_buffer *opb, int start) {
  opb->next_packet = start;
  opb->packets_sent = 0;
}

int opb_next_packet(transport_buffer *opb, unsigned char *buf) {
  int data_space = 7;
  int data_start = 1;
  int to_transfer = opb->used - opb->transferred;
  if (to_transfer == 0) {
    return 0;
  }
  if (to_transfer > data_space) {
    to_transfer = data_space;
  }
  buf[0] = opb->next_packet++;
  memcpy(&buf[data_start], &opb->pbuf[opb->transferred], to_transfer);
  opb->transferred += to_transfer;

  if (to_transfer < data_space) {
    memset(&buf[data_start + to_transfer], 0xff, data_space - to_transfer);
  }
  opb->packets_sent++;
  return to_transfer;
}

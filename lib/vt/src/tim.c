#include <sys/time.h>
#include <stdlib.h>
#include <arpa/inet.h>
#include <stdio.h>
#include <string.h>
#include <stdarg.h>

#include "network.h"
#include "logging.h"
#include "can.h"
#include "isostate.h"
#include "transport.h"

#include "tim.h"
#include "tim_packet.h"
#include "tim_packet_print.h"

#include "ack_nack.h"
#include "tim_auth.h"
#include "util.h"
#include "packet_track.h"

#define STATUS_REPETITION_MS 100
#define STATUS_AUTH_REPETITION_MS 1000

void timdebug(const char *fmt, ...) {
  static char obuf[1024];
  memcpy(obuf, "timstate: ", 10);
  memcpy(&obuf[10], fmt, strlen(fmt) + 1);

  enum log_level level = DEBUG;

  va_list ap;
  va_start(ap, fmt);
  vclg(level, obuf, ap);
  va_end(ap);
}

// int tim_functions[] = {VEHICLE_SPEED, REAR_HITCH};
int tim_functions[] = {VEHICLE_SPEED};

typedef void (*fperiodic)(sock_pair *, int);

struct periodic_req {
  int value;
  int active;
  int waiting_ack;
  struct timeval request_play_time;
  int automation_state;
  fperiodic f;
};

struct tim_state {
  int master_id;
  int client_heartbeat;
  int client_state;
  int server_state;
  int no_nack;
  int status;
  struct {
    struct timeval connection_version_request_time;
  } connection;

  /* periodic requests */
  int periodic_active;
  struct periodic_req rear_hitch_req;
  struct periodic_req speed_req;

  struct {
    int authenticated;
    int restart;
    int lwa;
    int challenge_signed;
    int certs_valid_bits;
    int has_server_signed;
    int authenticated_send_count;
  } auth;
};

#define HITCH_PERCENTAGE_DENOMINATOR 0x2710
static void set_rear_hitch(sock_pair *, int val);
static void set_vehicle_speed(sock_pair *, int val);

struct tim_state tim_state = {.master_id = 0,
                              .client_heartbeat = HEARTBEAT_RESET,
                              .client_state = AUTOMATION_UNAVAILABLE,
                              .server_state = AUTOMATION_UNAVAILABLE,
                              .no_nack = 0,
                              .status = TIM_NONE,
                              .connection = {
                                .connection_version_request_time = {0},
                              },
                              .periodic_active = 0,
                              .rear_hitch_req = {
                                .value = 0,
                                .active = 0,
                                .waiting_ack = 0,
                                .request_play_time = {0},
                                .automation_state = 0,
                                .f = set_rear_hitch,
                              },
                              .speed_req = {
                                .value = 0,
                                .active = 0,
                                .waiting_ack = 0,
                                .automation_state = 0,
                                .f = set_vehicle_speed,
                              },
                              .auth = {
                                .authenticated = NOT_AUTHENTICATED,
                                .restart = NO_RESTART,
                                .lwa = NO_LWA,
                                .challenge_signed = CHALLENGE_UNSIGNED,
                                .certs_valid_bits = 0,
                                .has_server_signed = 0,
                                .authenticated_send_count = 0
                              }
                             };

int get_tim_status() {
  return tim_state.status;
}

int tim_is_playing() {
  return tim_state.server_state == AUTOMATION_ACTIVE;
}

/* TIM tractor control functions */

/* "percentage" is as a number divided by 0x2710 */
static void set_rear_hitch(sock_pair *ss, int percentage) {
  cldebug("requesting hitch %f\n", ((float)percentage / HITCH_PERCENTAGE_DENOMINATOR));
  isopacket packet;
  rear_hitch_request_packet(&packet, get_iso_addr(), tim_state.master_id, percentage);
  send_packet(ss->write, &packet);

}

static void set_vehicle_speed(sock_pair *ss, int mm_per_sec) {
  cldebug("requesting vehicle speed %d\n", mm_per_sec);
  isopacket packet;
  vehicle_speed_request_packet(&packet, get_iso_addr(), tim_state.master_id, mm_per_sec);
  send_packet(ss->write, &packet);
}

#define TRACTOR_FUNCTION_REPETITION_MS 2000
static void periodic_set(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {

  /* Approaching abstraction here on these callbacks */
  int ran = 0;
  if (tim_state.speed_req.active) {
    ran = 1;
    tim_state.speed_req.f(ss, tim_state.speed_req.value);
  }
  if (tim_state.rear_hitch_req.active) {
    ran = 1;
    tim_state.rear_hitch_req.f(ss, tim_state.rear_hitch_req.value);
  }
  if (!ran) {
    tim_state.periodic_active = 0;
  } else {
    schedule_relative(periodic_set, now, 0, TRACTOR_FUNCTION_REPETITION_MS * 1000);
  }
}

void static start_periodic_set(sock_pair *ss, struct timeval *now) {
  if (!tim_state.periodic_active) {
    if (tim_state.speed_req.active || tim_state.rear_hitch_req.active) {
      tim_state.periodic_active = 1;
      schedule_relative(periodic_set, now, 0, STATUS_REPETITION_MS * 1000);
    }
  }
}

void request_vehicle_speed(sock_pair *ss) {
  isopacket packet;
  vehicle_speed_control_packet(&packet, get_iso_addr(), tim_state.master_id);
  send_packet(ss->write, &packet);
}

void request_rear_hitch(sock_pair *ss) {
  isopacket packet;
  rear_hitch_control_packet(&packet, get_iso_addr(), tim_state.master_id);
  send_packet(ss->write, &packet);
}

int tim_set_vehicle_speed(int mm_per_sec) {
  int modified = 0;
  if (tim_state.speed_req.value != mm_per_sec) {
    modified = 1;
  }
  tim_state.speed_req.value = mm_per_sec;

  if (tim_state.server_state != AUTOMATION_ENABLED &&
      tim_state.server_state != AUTOMATION_PENDING &&
      tim_state.server_state != AUTOMATION_ACTIVE) {
    clinfo("Not going to request speed %d because server state: %d\n", mm_per_sec, tim_state.server_state);
    return tim_state.status;
  }

  sock_pair ss = get_ss();
  if (!tim_state.speed_req.active) {
    cldebug("Request Vehicle Speed in function automation state: %s\n",
            automation_name(tim_state.speed_req.automation_state));
    struct timeval now = getnow();
    if (!tim_state.speed_req.waiting_ack ||
        (tim_state.speed_req.request_play_time.tv_sec == 0 && tim_state.speed_req.request_play_time.tv_usec == 0) ||
        abs(msdiff(&now, &tim_state.speed_req.request_play_time)) > 1000) {
      tim_state.speed_req.request_play_time = now;
      if (!tim_state.speed_req.waiting_ack) {
        clinfo("TIM Waiting Operator Ack\n");
      }
      tim_state.speed_req.waiting_ack = 1;
      request_vehicle_speed(&ss); // Asks to start motion - special packet
    }
  } else {
    if (modified) {
      tim_state.speed_req.f(&ss, tim_state.speed_req.value);
    }
  }
  return TIM_OK;
}

int tim_set_rear_hitch(float percentage) {
  tim_state.rear_hitch_req.value = (int)(percentage * HITCH_PERCENTAGE_DENOMINATOR);

  if (tim_state.server_state != AUTOMATION_ENABLED) {
    return tim_state.status;
  }

  sock_pair ss = get_ss();
  if (!tim_state.rear_hitch_req.active) {
    request_rear_hitch(&ss); // Asks to start motion - special packet
    tim_state.rear_hitch_req.active = 1;
  }

  struct timeval now = getnow();
  /* At some point we need to add code to fire off an instant speed update request if we are already active */
  start_periodic_set(&ss, &now);
  return TIM_OK;

}

int tim_stop_set_vehicle_speed() {
  tim_state.speed_req.active = 0;
  tim_state.speed_req.waiting_ack = 0;
  if (tim_state.server_state != AUTOMATION_ENABLED) {
    return tim_state.status;
  }

  cldebug("requesting release vehicle speed\n");
  isopacket packet;
  vehicle_speed_release_packet(&packet, get_iso_addr(), tim_state.master_id);
  sock_pair ss = get_ss();
  send_packet(ss.write, &packet);

  return TIM_OK;
}

int tim_stop_set_rear_hitch() {
  tim_state.rear_hitch_req.active = 0;
  if (tim_state.server_state != AUTOMATION_ENABLED) {
    return tim_state.status;
  }
  cldebug("requesting release hitch\n");
  isopacket packet;
  rear_hitch_release_packet(&packet, get_iso_addr(), tim_state.master_id);
  sock_pair ss = get_ss();
  send_packet(ss.write, &packet);

  return TIM_OK;
}

/* TIM state and protocol functions */

void tim_restart() {
  timdebug("TIM RESTART\n");
  tim_state.master_id = 0;

  memset(&tim_state.auth, 0, sizeof(tim_state.auth));

  tim_state.status = TIM_NONE;
  tim_state.client_state = AUTOMATION_UNAVAILABLE;

  tim_state.no_nack = 0;
  tim_state.rear_hitch_req.active = 0;
  tim_state.speed_req.active = 0;
}

void scheduled_tim_restart(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
  tim_restart();
}

void tim_ack_nack(sock_pair *ss, isopacket *p, unsigned char *data, int data_len, struct timeval *now) {
  if (data[1] == TIM_CLIENT_STATUS) {
    if (ack_is_nack(data)) {
      tim_state.no_nack = 1;
      schedule_relative(scheduled_tim_restart, now, 1, 0);
      timdebug("NACK CLIENT STATUS!\n");
    }
  }
}

void request_functions_single(sock_pair *ss, int *functions, int count) {
  isopacket packet;
  /* Supposed to send 3 status messages with "authenticated" before sending this. */
  tim_function_assignment_request_packet(&packet, get_iso_addr(), tim_state.master_id, functions, count);
  timdebug("Requesting vehicle speed function\n");
  send_packet(ss->write, &packet);
}

void request_tim_functions(sock_pair *ss) {
  int count = sizeof(tim_functions) / sizeof(int);
  if (count < 4 ) {
    request_functions_single(ss, tim_functions, count);
  } else {
    clerr("no impl for more than 4 functions\n");
    // transport_buffer *ptbp = make_transport_buffer_sized(2 + 2 * count);
  }
}

int mph(double v) {
  /* convert to mm per second */
  double meters_per_hour = v * METERS_PER_MILE;
  return (meters_per_hour * 1000) / SECONDS_PER_HOUR;
}

int tim_connection_version_response(sock_pair *ss, isopacket *p, struct timeval *now) {
  if (p->data[1] == 0) {
    timdebug("TIM Connection Version: %d\n", p->data[2]);
    return 0;
  } else {
    clerr("TIM Connection Version Error: %d: [0x%x]\n", p->data[1], p->data[1]);
    return -1;
  }
}

void client_function_request(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
  isopacket packet;
  tim_functions_support_request_packet(&packet, get_iso_addr(), tim_state.master_id);
  send_packet(ss->write, &packet);
}

void client_status(sock_pair *ss) {
  isopacket packet;
  tim_client_status_packet(&packet, get_iso_addr(), tim_state.master_id);
  struct client_status_bits *bits = (void*)&(packet.data);

  /* heartbeat load up and reset */
  bits->heartbeat = tim_state.client_heartbeat;
  tim_state.client_heartbeat++;
  if (tim_state.client_heartbeat >= HEARTBEAT_RESET) {
    tim_state.client_heartbeat = 0;
  }

  /* client state */
  bits->client_state = tim_state.client_state;
  bits->xxx = 0xf;

  send_packet(ss->write, &packet);
}

void repeated_send_client_status(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {

  if (tim_state.no_nack) {
    timdebug("client status no nack\n");
    return;
  }

  client_status(ss);
  schedule_relative(repeated_send_client_status, now, 0, STATUS_REPETITION_MS * 1000);
}

void send_auth_status(sock_pair *ss) {
  isopacket pac;
  tim_client_auth_status_packet(&pac, get_iso_addr(), tim_state.master_id,
                                tim_state.auth.authenticated, tim_state.auth.restart, tim_state.auth.lwa,
                                tim_state.auth.challenge_signed, tim_state.auth.certs_valid_bits);
  if (tim_state.auth.authenticated) {
    timdebug("Tim client is AUTHENTICATED.\n");
  }
  send_packet(ss->write, &pac);
}

void repeated_send_client_auth_status(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
  send_auth_status(ss);
  if (tim_state.auth.authenticated) {
    tim_state.auth.authenticated_send_count++;
    if (tim_state.auth.authenticated_send_count < 3) {
      schedule_relative(repeated_send_client_auth_status, now, 0, STATUS_AUTH_REPETITION_MS * 1000);
    } else {
      tim_state.client_state = AUTOMATION_ENABLED;
      request_tim_functions(ss);
    }
  } else {
    schedule_relative(repeated_send_client_auth_status, now, 0, STATUS_AUTH_REPETITION_MS * 1000);
  }
}

void print_tim_function_name(int f) { if (f < 32) {
    timdebug("AUX Valve %d\n", f);
  } else {
    switch (f) {
      case 0x40:
        timdebug("Front PTO\n");
        break;
      case 0x41:
        timdebug("Rear PTO\n");
        break;
      case 0x42:
        timdebug("Front Hitch\n");
        break;
      case 0x43:
        timdebug("Rear Hitch\n");
        break;
      case 0x44:
        timdebug("Vehicle Speed\n");
        break;
      case 0x46:
        timdebug("External Guidance\n");
        break;
      default:
        timdebug("UKNOWN Function\n");
        break;
    }
  }
}

void server_random_challenge_request(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
  isopacket pac;
  server_random_challenge_request_packet(&pac, get_iso_addr(), tim_state.master_id);
  timdebug("Sending tim server_random_challenge_request AUTH\n");
  send_packet(ss->write, &pac);
}

void client_auth_signed(sock_pair *ss, struct timeval *scheduled,  struct timeval *now) {
  tim_state.auth.challenge_signed = CHALLENGE_SIGNED;
  timdebug("Sending tim client AUTH SIGNED!\n");
  send_auth_status(ss);
}

void client_auth_unsigned(sock_pair *ss, struct timeval *scheduled,  struct timeval *now) {
  tim_state.auth.challenge_signed = CHALLENGE_UNSIGNED;
  timdebug("Sending tim client AUTH UNSIGNED!\n");
  send_auth_status(ss);
}

void client_auth_norestart(sock_pair *ss, struct timeval *scheduled,  struct timeval *now) {
  tim_state.auth.restart = NO_RESTART;
  timdebug("Sending tim client AUTH NO RESTART!\n");
  send_auth_status(ss);
}

void server_signed_challenge_request(sock_pair *ss) {
  timdebug("Sending server signed challenge request\n");
  isopacket pac;
  server_signed_challenge_request_packet(&pac, get_iso_addr(), tim_state.master_id);
  send_packet(ss->write, &pac);
}

const char *tim_error_str(int error) {
  switch (error) {
    case 0x15:
      return "ISOBUS Name Mismatch";
      break;

    case 0x16:
      return "ECU CCID Mismatch";
      break;

    case 0x06:
      return "Manufacturer Certificate Signature Invalid";

    case 0x05:
      return "Lab Certificate Invalid";

    case 0x07:
      return "Manufacturer Series Signature Invalid";

    case 0x09:
      return "Challenge Mismatch";

    default:
      return "UNKNOWN TIM ERROR";
      break;
  }
}

void tim_server_auth_status(sock_pair *ss, isopacket *p, unsigned char *data, int data_len,
    struct timeval *now) {
  int auth_status = data[2] & 0xf;
  int sub_status = data[3];
  int restart = sub_status & (1<<7) ? 1 : 0;
  int lwa = sub_status & (1 << 6) ? 1 : 0;
  int type = data[2] >> 4;
  int error = data[1];
  int challenge_signed = sub_status & (1 << 1);
  if (auth_status == AUTHENTICATED) {
    clinfo("TIM server AUTHENTICATED.\n");
  }

  if (error) {
    tim_state.status = TIM_AUTH_FAIL;
    clerr("ERROR FAIL %d [0x%x] %s\n", error, error, tim_error_str(error));
    return;
  }
  if (challenge_signed && !tim_state.auth.has_server_signed) {
    server_signed_challenge_request(ss);
  }
  if (restart) {
    // now we are working together, send no restart
    tim_state.client_state = AUTOMATION_NOT_READY; // supposed to transition this when doing auth workflow
    client_status(ss);
    client_auth_norestart(ss, NULL, now);
  } else {
    if (tim_state.client_state == AUTOMATION_NOT_READY) {
      tim_state.client_state = AUTOMATION_READY;
      server_random_challenge_request(ss, now, now);
    }
  }
}

void random_challenge_transport_done(int code, transport_buffer *ptpb, sock_pair *ss, struct timeval *now) {
  opb_free(ptpb);
  free(ptpb);
}

void client_random_challenge_request(sock_pair *ss) {
  unsigned char *challenge;

  int clen;
  if (auth_random_challenge(&challenge, &clen) != 0) {
    clerr("failed authlib random challenge gen\n");
  }

  transport_buffer *ptpb = make_transport_buffer_sized(clen+7);
  opb_add8(ptpb, CLIENT_RANDOM_CHALLENGE_REQUEST);
  opb_add8(ptpb, 0); // error code
  opb_add8(ptpb, 0); // authentication type
  opb_add8(ptpb, 0); // reserved
  opb_add8(ptpb, 0); // reserved
  opb_add16(ptpb, clen);
  opb_addbytes(ptpb, (const char *)challenge, clen);

  initiate_transport(ss, ptpb, get_iso_addr(), tim_state.master_id, TIM_AUTH21_PGN, random_challenge_transport_done);
}

void send_isobus_certification(sock_pair *ss) {
  isopacket pac;
  isobus_certification_packet_v3(&pac, get_iso_addr());
  send_packet(ss->write, &pac);
}

void certificate_request_transport_done(int code, transport_buffer *ptpb, sock_pair *ss, struct timeval *now) {
  opb_free(ptpb);
  free(ptpb);
  timdebug("client certificate transport done.\n");
}

void client_certificate_request(sock_pair *ss, unsigned char *data) {
  char *pcert = NULL;
  int certlen = 0;

  switch (data[4]) {
    case TESTLAB_CERTIFICATE:
      timdebug("asking testlab cert\n");
      if (get_testlab_cert(&pcert, &certlen) != 0) {
        clerr("Failed to read loaded testlab cert\n");
      }
      break;

    case MANUFACTURER_CERTIFICATE:
      timdebug("asking manufacturer cert\n");
      if (get_manufacturer_cert(&pcert, &certlen) != 0) {
        clerr("Failed to read loaded manufacturer cert\n");
      }
      break;

    case MANUFACTURER_SERIES_CERTIFICATE:
      timdebug("asking manufacturer series cert\n");
      if (get_manufacturer_series_cert(&pcert, &certlen) != 0) {
        clerr("Failed to read loaded manufacturer series cert\n");
      }
      break;

    case DEVICE_CERTIFICATE:
      timdebug("asking device cert\n");
      if (get_device_certificate(&pcert, &certlen) != 0) {
        clerr("Failed to read loaded device cert\n");
      }
      break;

    default:
      timdebug("Asking cert type %d\n", data[4]);
      break;
  }
  if (pcert) {
    transport_buffer *ptpb = make_transport_buffer_sized(certlen+7);
    opb_add8(ptpb, CLIENT_CERTIFICATE_REQUEST);
    opb_add8(ptpb, 0); // error code
    opb_add8(ptpb, 0xf); // authentication type (low bits reserved)
    opb_add8(ptpb, DER_BINARY_FORMAT);
    opb_add8(ptpb, data[4]);
    opb_add16(ptpb, certlen);
    opb_addbytes(ptpb, pcert, certlen);
    initiate_transport(ss, ptpb, get_iso_addr(), tim_state.master_id, TIM_AUTH21_PGN,
                       certificate_request_transport_done);
  } else {
    clerr("No cert to send!\n");
  }
}

void server_testlab_certificate_request(sock_pair *ss) {
  isopacket pac;
  server_certificate_request_packet(&pac, get_iso_addr(), tim_state.master_id, TESTLAB_CERTIFICATE);
  send_packet(ss->write, &pac);
}

void signed_challenge_transport_done(int code, transport_buffer *ptpb, sock_pair *ss, struct timeval *now) {
  opb_free(ptpb);
  free(ptpb);
  tim_state.auth.authenticated = AUTHENTICATED;
}

void client_signed_challenge_response(sock_pair *ss) {
  unsigned char *signed_challenge;
  int clen;
  if (auth_signed_challenge(&signed_challenge, &clen) != 0) {
    clerr("failed authlib signed challenge gen\n");
  }
  transport_buffer *ptpb = make_transport_buffer_sized(clen + 7);
  opb_add8(ptpb, CLIENT_SIGNED_CHALLENGE_REQUEST);

  opb_add8(ptpb, 0); // error code
  opb_add8(ptpb, 0); // authentication type
  opb_add8(ptpb, 0); // reserved
  opb_add8(ptpb, 0); // reserved
  opb_add16(ptpb, clen);
  opb_addbytes(ptpb, (const char *)signed_challenge, clen);

  initiate_transport(ss, ptpb, get_iso_addr(), tim_state.master_id, TIM_AUTH21_PGN, signed_challenge_transport_done);
}

void tim_auth12(sock_pair *ss, isopacket *p, unsigned char *data, int data_len, struct timeval *now) {
  int challenge_len;
  int error;
  switch (data[0]) {
    case TIM_SERVER_AUTH_STATUS:
      tim_server_auth_status(ss, p, data, data_len, now);
      break;
    case SERVER_RANDOM_CHALLENGE_REQUEST:
      challenge_len = *(uint16_t*)&data[5];
      error = data[1];
      timdebug("Server Random Challenge Response data len: %d, challenge len: %d\n", data_len, challenge_len);
      if (error != 0) {
        clerr("Server Random Challenge Response err: 0x%x [%s]\n", error, server_challenge_response_err(error));
      } else {
        tim_auth_set_server_random_challenge(&data[7], challenge_len);
        server_testlab_certificate_request(ss);
      }
      break;
    case CLIENT_RANDOM_CHALLENGE_REQUEST:
      timdebug("Client random challenge request coming in, generating ...\n");
      client_random_challenge_request(ss);
      break;
    case CLIENT_CERTIFICATE_REQUEST:
      timdebug("Client certificate request\n");
      client_certificate_request(ss, data);
      break;
    case SERVER_CERTIFICATE_REQUEST:
      timdebug("Server certificate response for type %d\n", cert_type(data));

      /* This is cheap - need to actually track these */
      switch (cert_type(data)) {
        case DEVICE_CERTIFICATE:
          tim_auth_set_server_device_certificate(&data[7], *(uint16_t*)(&data[5]));
          tim_state.auth.certs_valid_bits |= (1 << DEVICE_BIT);
          break;
        case MANUFACTURER_SERIES_CERTIFICATE:
          tim_state.auth.certs_valid_bits |= (1 << MANUFACTURER_SERIES_BIT);
          break;
        case MANUFACTURER_CERTIFICATE:
          tim_state.auth.certs_valid_bits |= (1 << MANUFACTURER_BIT);
          break;
        case TESTLAB_CERTIFICATE:
          tim_state.auth.certs_valid_bits |= (1 << TESTLAB_BIT);
          break;
      }

      int next_cert = cert_type(data) + 1;
      if (next_cert <= DEVICE_CERTIFICATE) {
        timdebug("Sending next server certificate request for type %d\n", next_cert);
        isopacket pac;
        server_certificate_request_packet(&pac, get_iso_addr(), tim_state.master_id, next_cert);
        send_packet(ss->write, &pac);
      } else {
        /*
        timdebug("Requested all server certificates, challenge unsigned (1x?).\n");
        client_auth_unsigned(ss, now, now);
        */

        timdebug("Requested all server certificates, challenge signed.\n");
        client_auth_signed(ss, now, now);
      }
      break;
    case CLIENT_SIGNED_CHALLENGE_REQUEST:
      timdebug("Client signed challenge request\n");
      client_signed_challenge_response(ss);
      break;
    case SERVER_SIGNED_CHALLENGE_REQUEST:
      tim_state.auth.has_server_signed = 1;
      timdebug("Server signed challenge response\n");
      break;
    default:
      timdebug("UNKNOWN AUTH12 %d [0x%x]\n", data[0], data[0]);
  }
}

void client_initiate_auth_packet(sock_pair *ss, struct timeval *scheduled,  struct timeval *now) {
  tim_state.auth.authenticated = NOT_AUTHENTICATED;
  tim_state.auth.restart = RESTART;
  tim_state.auth.lwa = NO_LWA;
  tim_state.auth.challenge_signed = CHALLENGE_UNSIGNED;

  timdebug("Sending tim client AUTH w RESTART\n");
  repeated_send_client_auth_status(ss, scheduled, now);
}

void tim_server_2_client(sock_pair *ss, isopacket *p, unsigned char *data, int data_len, struct timeval *now) {

  int has_velocity = 0;
  int num_tim = data[1];
  int tim_start = 2;
  int which_tim;

  int tim_code = data[0];
  switch (tim_code) {
    case TIM_CONNECTION_VERSION:
      timdebug("CONNECTION VERSION RESPONSE\n");
      if (0 == tim_connection_version_response(ss, p, now)) {
        tim_state.master_id = p->sa;
        repeated_send_client_status(ss, now, now);
        register_can_callback(TIM_AUTH12_PGN, tim_auth12, 1);
        schedule_relative(client_function_request, now, 0, 10 * 1000);
      }
      break;

    case TIM_FUNCTION_SUPPORT:
      /*
      timdebug("TIM_FUNCTION_SUPPORT:\n");
      timdebug("\t(data length is: %d)\n", data_len);
      timdebug("\tnumber of functions: %d\n", data[1]);
      */
      for (which_tim = 0; which_tim < num_tim; which_tim++) {
        int facils = data[tim_start + 1];
        /*
        timdebug("\ttim function id: %d\n", data[tim_start]);
        print_tim_function_name(data[tim_start]);
        */
        if (data[tim_start] == VEHICLE_SPEED) {
          has_velocity = 1;
        }
        cldebug("\tnumber of bytes of facilities of function 0x%x: %d\n", data[tim_start], facils);
        for (int which_facil = 0; which_facil < facils; which_facil++) {
          int facil_byte = data[tim_start + 2 + which_facil];
          cldebug("\tFacil %d: [", which_facil);
          print_binary(DEBUG, facil_byte);
          cldebug("]\n");
        }
        tim_start = tim_start + facils + 2;
      }

      /* Check for speed */
      if (has_velocity) {
        clinfo("TIM - tractor accepts Velocity control.\n");
      } else {
        clwarn("TIM - tractor does not accept Velocity control.\n");
      }

      tim_state.status = TIM_AUTHENTICATING;
      clinfo("TIM Initiating Auth\n");
      client_initiate_auth_packet(ss, now, now);

      break;

    case FUNCTIONS_ASSIGNMENT_REQUEST:
      timdebug("Function assignment response from server, function 0x%x value %d\n", data[2], (data[3] >> 5));
      break;

    default:
      timdebug("server sending tim code: %d, what's this?\n", tim_code);
      break;

  }
}

void send_connection_version_request(sock_pair *ss, int id) {
  isopacket packet;
  tim_connection_version_request_packet(&packet, get_iso_addr(), id);
  timdebug("Sending TIM connection version request\n");
  send_packet(ss->write, &packet);
}

void tim_server_status(sock_pair *ss, isopacket *p, struct timeval *now) {
  struct server_status_bits *bits;
  bits = (void *)p->data;

  if (bits->master_indication == 0x1) {
    // Found Master

    if (tim_state.server_state != AUTOMATION_ACTIVE && bits->server_state == AUTOMATION_ACTIVE) {
      clinfo("TIM Server Automation Active [Play].\n");
    }
    if (tim_state.server_state == AUTOMATION_ACTIVE && bits->server_state != AUTOMATION_ACTIVE) {
      clinfo("TIM Server Dropped Active.\n");
    }
    tim_state.server_state = bits->server_state;
    if (tim_state.master_id == 0) {
      timdebug("Found master for FIRST TIME\n");
      if (tim_state.connection.connection_version_request_time.tv_sec == 0 &&
          tim_state.connection.connection_version_request_time.tv_usec == 0) {

        tim_state.no_nack = 0;
        register_can_callback(TIM_SERVER_2_TIM_CLIENT, tim_server_2_client, 1);
      }

      struct timeval now = getnow();
      if (abs(msdiff(&now, &tim_state.connection.connection_version_request_time)) > 1000) {
        tim_state.status = TIM_CONNECTING;
        tim_state.connection.connection_version_request_time = getnow();
        send_connection_version_request(ss, p->sa);
      }

    } else {
      switch(bits->server_state) {
        case AUTOMATION_ENABLED:
          tim_state.status = TIM_OK;
          cldebug("We see server is automation enabled\n");
          break;

        case AUTOMATION_NOT_READY:
          /* If we think we are authenticated and the server says it isn't, then the server must have
             restarted */
          if (tim_state.status == TIM_OK) {
            cldebug("RESTARTING SERVER AUTH\n");
            tim_restart();
          }
          break;
      }
    }
  }
}

void tim_server_broadcast(sock_pair *ss, isopacket *p, unsigned char *data, int data_len,
                         struct timeval *now) {
  int tim_code = p->data[0];

  switch (tim_code) {
    case TIM_SERVER_STATUS:
      tim_server_status(ss, p, now);
      break;

    case TIM_CLIENT_VERSION:
      timdebug("ASKING FOR CLIENT VERSION!\n");
      break;

    case VEHICLE_SPEED:
      tim_state.speed_req.automation_state = automation_state(data);
      cldebug("Speed Automation State: d\n", automation_state(data));

      switch (tim_state.speed_req.automation_state) {
        case AUTOMATION_READY:
        case AUTOMATION_ENABLED:
          cldebug("Automation state fallback, speed req inactive\n");
          tim_state.speed_req.active = 0;
          break;

        case AUTOMATION_PENDING:
          cldebug("Speed Automation Pending\n");

          /* This is how to see operator ack */
          if (tim_state.speed_req.waiting_ack) {
            clinfo("TIM Operator Ack Movement.\n");
            tim_state.speed_req.waiting_ack = 0;
            tim_state.speed_req.active = 1;
            start_periodic_set(ss, now);
          }
          break;
        }
      break;
  }
}

void start_tim(sock_pair *ss, struct timeval *now) {
  register_ack_nack(TIM_CLIENT_2_TIM_SERVER, tim_ack_nack);
  register_can_callback(TIM_SERVER_2_TIM_CLIENT, tim_server_broadcast, 0);
  timdebug("TIM START\n");
}

int tim_init(const char *certificate_dir) {
  tim_auth_init(certificate_dir);
  register_address_callback(start_tim);
  start_can_packet_track();
  return 0;
}

int tecu_init() {
  start_can_packet_track();
  return 0;
}

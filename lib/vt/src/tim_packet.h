#define AUTHENTICATED 1
#define NOT_AUTHENTICATED 0
#define CHALLENGE_SIGNED 1
#define CHALLENGE_UNSIGNED 0
#define RESTART 1
#define NO_RESTART 0
#define DO_LWA 1
#define NO_LWA 0
void tim_connection_version_request_packet(isopacket *pac, int addr, int dest);
void tim_functions_support_request_packet(isopacket *pac, int addr, int dest);
void tim_client_status_packet(isopacket *pac, int addr, int dest);
void tim_client_auth_status_packet(isopacket *pac, int addr, int dest, int authenticated, int restart, int lwa,
                                   int challenge_signed, int certs_valid_bits);
void server_random_challenge_request_packet(isopacket *pac, int addr, int dest);
void server_certificate_request_packet(isopacket *pac, int addr, int dest, int cert_type);
void server_signed_challenge_request_packet(isopacket *pac, int addr, int dest);
void vehicle_speed_request_packet(isopacket *pac, int addr, int dest, int mm_per_s);
void rear_hitch_request_packet(isopacket *pac, int addr, int dest, int percentage);
void vehicle_speed_release_packet(isopacket *pac, int addr, int dest);
void rear_hitch_release_packet(isopacket *pac, int addr, int dest);
void vehicle_speed_control_packet(isopacket *pac, int addr, int dest);
void rear_hitch_control_packet(isopacket *pac, int addr, int dest);
void tim_function_assignment_request_packet(isopacket *pac, int addr, int dest, int *functions, int count);
const char *automation_name(int automation);
uint16_t vehicle_speed(unsigned char *data);
int automation_state(unsigned char *data);
const char *server_challenge_response_err(unsigned char err);

/* Server to client messages */
#define TIM_SERVER_STATUS 0xFA
#define TIM_SERVER_VERSION 0xF8

/* Server operation state */
#define REQUIREMENTS_NOT_FULFILLED 0x00
#define REQUIREMENTS_NORMAL_OPERATION 0x01
#define REQUIREMENTS_STANDSTILL_OPERATION 0x02
#define REQUIREMENTS_STATIONARY_OPERATION 0x03

/* Server Auth messages */
#define TIM_SERVER_AUTH_STATUS 0xF9

/* Client to server messages */
#define TIM_CLIENT_VERSION 0xF7
#define TIM_CLIENT_STATUS 0xF9

/* Client Auth Messages */
#define TIM_CLIENT_AUTH_STATUS 0xFA
#define CLIENT_RANDOM_CHALLENGE_REQUEST 0x05
#define SERVER_RANDOM_CHALLENGE_REQUEST 0x04

#define SERVER_SIGNED_CHALLENGE_REQUEST 0x06
#define CLIENT_SIGNED_CHALLENGE_REQUEST 0x07

#define CLIENT_CERTIFICATE_REQUEST 0x3
#define SERVER_CERTIFICATE_REQUEST 0x2

#define TIM_CONNECTION_VERSION 0xF6
#define TIM_FUNCTION_ASSIGNMENT 0xF5
#define TIM_FUNCTION_SUPPORT 0xF4
#define TIM_FUNCTION_ASSIGNMENT_STATUS 0xF3

#define AUTOMATION_UNAVAILABLE 0
#define AUTOMATION_NOT_READY 1
#define AUTOMATION_READY 2
#define AUTOMATION_ENABLED 3
#define AUTOMATION_PENDING 4
#define AUTOMATION_ACTIVE 5

#define HEARTBEAT_RESET 0xFB

/* Slot Functions */
#define AUX_VALVE_1 0x01
#define AUX_VALVE_2 0x02
#define AUX_VALVE_3 0x03
#define AUX_VALVE_4 0x04
#define FRONT_PTO 0x40
#define REAR_PTO 0x41
#define FRONT_HITCH 0x42
#define REAR_HITCH 0x43
#define VEHICLE_SPEED 0x44
#define EXTERNAL_GUIDANCE 0x46

/* Function Requests */
#define REQUEST_COUNTER_DONT_CARE 0xF
#define FORWARD_SPEED_MIN 0x7D80
#define FORWARD_SPEED_MAX 0xFAFF
#define SPEED_RELEASE_NO_OPERATOR 0xFBFE
#define SPEED_RELEASE_WITH_OPERATOR 0xFBFD
#define READY_CONTROL 0xFBFF

#define FUNCTIONS_ASSIGNMENT_REQUEST 0xF5
#define TIM_FUNCTION_ASSIGN 0x01
#define TIM_FUNCTION_RELEASE 0x00

/* Constants */
#define METERS_PER_MILE 1609.34
#define SECONDS_PER_HOUR (60 * 60)

int mph(double v);

int tim_init(const char *certificate_dir);
int tecu_init();

struct server_status_bits {
  unsigned char code;
  unsigned char heartbeat;

  unsigned char system_state: 4;
  unsigned char master_indication: 4;

  unsigned char operation_state: 4;
  unsigned char server_state: 4;
};

struct client_status_bits {
  unsigned char code;
  unsigned char heartbeat;

  unsigned char xxx: 4;
  unsigned char client_state: 4;
};

void send_isobus_certification(sock_pair *ss);



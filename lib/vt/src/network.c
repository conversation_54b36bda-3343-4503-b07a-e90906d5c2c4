#include <stdio.h>
#include <unistd.h>
#include <string.h>

#include <arpa/inet.h>

#ifdef HAS_LINUX_CAN
#include <linux/can.h>
#endif

#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/types.h>

#include "logging.h"
#include "network.h"

#ifdef HAS_LINUX_CAN
int open_can_if(int which) {
  int s;
  struct sockaddr_can addr;
  struct ifreq ifr;
  char canname[8];

  s = socket(PF_CAN, SOCK_RAW, CAN_RAW);
  if (s == -1) {
    clerr("Failed to open CAN socket.\n");
    return -1;
  }

  sprintf(ifr.ifr_name, "can%d", which);
  if (-1 == ioctl(s, SIOCGIFINDEX, &ifr)) {
    clerr("ioctl get if index failure.\n");
    close(s);
    return -1;
  }

  addr.can_family = AF_CAN;
  addr.can_ifindex = ifr.ifr_ifindex;

  if (-1 == bind(s, (struct sockaddr *)&addr, sizeof(addr))) {
    clerr("Failed to bind to socket can socket.\n");
    close(s);
    return -1;
  }
  return s;
}
#endif

int connect_udp(int s, const char *host, int port) {
  struct sockaddr_in addr;
  memset(&addr, 0, sizeof(struct sockaddr_in));
  addr.sin_family = AF_INET;
  if (inet_aton(host, &addr.sin_addr) < 0) {
    return -1;
  }
  addr.sin_port = htons(port);
  return connect(s, (struct sockaddr *)&addr, sizeof(struct sockaddr_in));
}

#include <string.h>
#include "can.h"
#include "isostate.h"
#include "dispatch.h"
#include "locked_queue.h"
#include "object_pool.h"
#include "object_pool_builder_c.h"
#include "transport.h"
#include "tim.h"

#include "language_interface.h"

/* bus control */

enum iso_request_type { ISO_PACKET, ISO_OP_BUFFER, ISO_PACKET_RESET, INT_METHOD, VOID_METHOD, FLOAT_METHOD };

typedef void (*int_method)(int);
typedef void (*void_method)();
typedef void (*float_method)(float);

typedef struct iso_request {
  enum iso_request_type type;
  enum ui_request_type requester;
  int user_response_code;
  union {
    isopacket *packet;
    struct {
      transport_buffer *opb;
      enum working_set_state op_state;
    } op;
		struct {
			int_method f;
			int val;
		} int_f;
		struct {
			void_method f;
		} void_f;
    struct {
      float_method f;
      float val;
    } float_f;
  } data;
} iso_request;

static iso_request *current_request;
static int user_requests = 0;
static int preferred_vt = 0;

static locked_queue lqq;

int get_preferred_vt() {
  return preferred_vt;
}

/*
 * iso_request management
 */
void send_iso_packet(isopacket *packet, int response_code, enum ui_request_type type) {
  // ISO_PACKET
  iso_request *req = (iso_request *)malloc(sizeof(iso_request));
  if (type == SWITCH_SCREEN) {
    req->type = ISO_PACKET_RESET;
  } else {
    req->type = ISO_PACKET;
  }
  req->requester = type;
  req->user_response_code = response_code;
  req->data.packet = packet;

  if (type == USER || type == SWITCH_SCREEN) {
    user_requests++;
  }

  lq_add(&lqq, req);
}

void send_transport_buffer(transport_buffer *opb, enum ui_request_type type) {
  /* assumes always NOT etp */
  // XFER_OBJECT_POOL
  // ISO_OP_BUFFER

  iso_request *req = (iso_request *)malloc(sizeof(iso_request));
  req->type = ISO_OP_BUFFER;
  req->requester = type;
  req->user_response_code = 0;
  req->data.op.op_state = XFER_OBJECT_POOL;
  req->data.op.opb = opb;

  if (type == USER) {
    user_requests++;
    cldebug("send_transport_buffer: user requests to %d\n", user_requests);
  }

  lq_add(&lqq, req);
}

void send_int_method(int_method f, int val) {
	iso_request *req = (iso_request *)malloc(sizeof(iso_request));
	req->type = INT_METHOD;
	req->requester = USER;
	req->user_response_code = 0;
	req->data.int_f.f = f;
	req->data.int_f.val = val;
	user_requests++;
	lq_add(&lqq, req);
}

void send_void_method(void_method f) {
	iso_request *req = (iso_request *)malloc(sizeof(iso_request));
	req->type = VOID_METHOD;
	req->requester = USER;
	req->user_response_code = 0;
	req->data.void_f.f = f;
	user_requests++;
	lq_add(&lqq, req);
}

void send_float_method(float_method f, float val) {
	iso_request *req = (iso_request *)malloc(sizeof(iso_request));
	req->type = FLOAT_METHOD;
	req->requester = USER;
	req->user_response_code = 0;
	req->data.float_f.f = f;
	req->data.float_f.val = val;
	user_requests++;
	lq_add(&lqq, req);
}

void iso_request_free(iso_request *req) {
  if (req == NULL) {
    return;
  }
  if (req->type == ISO_OP_BUFFER) {
    opb_free(req->data.op.opb);
    free(req->data.op.opb);
  } else if (req->type == ISO_PACKET || req->type == ISO_PACKET_RESET) {
    free(req->data.packet);
  }
  free(req);
}

void free_current_iso_request() {
  iso_request *req = current_request;
  iso_request_free(req);
  current_request = NULL;
}

void next_outstanding_command(sock_pair *ss) {
  while (1) {
    if (!current_request) {
      iso_request *req = (iso_request *)lq_remove(&lqq);

      if (req) {
        current_request = req;

        if (req->type == ISO_PACKET_RESET) {
          unload_op();

          cldebug("ISO_PACKET_RESET clearing the queue\n");

          // clear out the queue (these should all be UI_ELEMENT types - eg nobody waiting on them
          iso_request *del = (iso_request *)lq_remove(&lqq);
          while (del) {
            iso_request_free(del);
            del = (iso_request *)lq_remove(&lqq);
          }
          break;
        }
        if (req->type == ISO_PACKET || req->type == ISO_PACKET_RESET) {
          send_packet(ss->write, req->data.packet); // check does this always request done?
          break;
        } else if (req->type == ISO_OP_BUFFER) {
          initiate_transport(ss, req->data.op.opb, get_iso_addr(), vt_addr(), ECU2VT_PDU << 8, NULL);
          break;
        } else {
          if (req->type == INT_METHOD) {
            req->data.int_f.f(req->data.int_f.val);
          } else if (req->type == VOID_METHOD) {
            req->data.void_f.f();
          } else if (req->type == FLOAT_METHOD) {
            req->data.float_f.f(req->data.float_f.val);
          } else {
            clerr("UKNOWN REQUEST TYPE: %d\n", req->type);
          }
          if (user_requests > 0) {
            user_requests--;
          }
          iso_request_free(req);
          current_request = NULL;
          if (user_requests == 0) {
            dispatch_wakeup();
            break;
          }
        }
      }
    }
  }
}

int op_transport_state() {
  /* check the outstanding object pool transport type. This handles either the global object pool or an update */
  iso_request *req = current_request;
  if (req) {
    return req->data.op.op_state;
  }
  return get_working_set_state();
}

void iso_request_done(sock_pair *ss) {
  struct iso_request *req = current_request;
  current_request = NULL;

  if (req) {

    if (req->requester == USER) {
      user_requests--;
      /* Only dispatch back to user if all requests have been completed */
      if (user_requests == 0) {
        dispatch_wakeup();
      }
    }
    iso_request_free(req);
    next_outstanding_command(ss);
  }
}

void user_vt_function(sock_pair *ss, int op) {
  iso_request *req = current_request;

  if (req) {
    /* If we have an outstanding request, and it is a packet request, and the response code matches this code,
     * then they are replying to us. */
    if (req->type == ISO_PACKET && req->user_response_code == op) {
      cldebug("response code %d matches my request\n", req->user_response_code);
      iso_request_done(ss);

    /* If we are resetting the ISOBUS then just clear the request - there are more steps coming */
    } else if (req->type == ISO_PACKET_RESET && req->user_response_code == op) {
      free_current_iso_request();
      iso_request_free(req);
    }
  }
}

#define WORKING_SET_GUARD()                                                                                        \
  do {                                                                                                             \
    if (get_working_set_state() != COMPLETE) {                                                                     \
      dispatch_wakeup();                                                                                           \
      return;                                                                                                      \
    }                                                                                                              \
  } while (0)

/* Tractor Control Interface */

/* TIM SPEED */
void _interface_tim_set_vehicle_speed(int mm_per_sec) {
	tim_set_vehicle_speed(mm_per_sec);
}

void threaded_tim_set_vehicle_speed(int mm_per_sec) {
	send_int_method(_interface_tim_set_vehicle_speed, mm_per_sec);
	trigger_can();
}

void _interface_tim_stop_set_vehicle_speed() {
	tim_stop_set_vehicle_speed();
}
void threaded_tim_stop_set_vehicle_speed() {
	send_void_method(_interface_tim_stop_set_vehicle_speed);
	trigger_can();
}

/* TECU SPEED */
void _interface_tecu_set_vehicle_speed(int mm_per_sec) {
  tecu_set_vehicle_speed(mm_per_sec);
}

void threaded_tecu_set_vehicle_speed(int mm_per_sec) {
  send_int_method(_interface_tecu_set_vehicle_speed, mm_per_sec);
  trigger_can();
}

void _interface_tecu_stop_set_vehicle_speed() {
  tecu_stop_set_vehicle_speed();
}

void threaded_tecu_stop_set_vehicle_speed() {
  send_void_method(_interface_tecu_stop_set_vehicle_speed);
  trigger_can();
}

/* REAR HITCH */
void _interface_tim_set_rear_hitch(float percentage) {
  tim_set_rear_hitch(percentage);
}

void threaded_tim_set_rear_hitch(float percentage) {
  send_float_method(_interface_tim_set_rear_hitch, percentage);
  trigger_can();
}

void _interface_tim_stop_set_rear_hitch() {
  tim_stop_set_rear_hitch();
}

void threaded_tim_stop_set_rear_hitch() {
  send_void_method(_interface_tim_stop_set_rear_hitch);
  trigger_can();
}

/* VT Interface */
void set_active_mask(const char *name) {
  WORKING_SET_GUARD();
  int datamask_id = datamask_id_by_str(name);

  isopacket *packet = make_isopacket();
  int code = set_active_mask_command(packet, get_iso_addr(), vt_addr(), working_set_object_id(), datamask_id);
  send_iso_packet(packet, code, USER);

  trigger_can();
}

void switch_screen() {
  WORKING_SET_GUARD();
  isopacket *packet = make_isopacket();
  delete_object_pool_packet(packet, get_iso_addr(), vt_addr());
  send_iso_packet(packet, 0, SWITCH_SCREEN);

  trigger_can();
}

void set_string_id_value_typed(int string_id, const char *value, enum ui_request_type type) {
  WORKING_SET_GUARD();
  if (strlen(value) <= 2) // 3 character strings go direct (but we have to pad 1 space)
  {
    isopacket *packet = make_isopacket();
    int code = set_string_value_command(packet, get_iso_addr(), vt_addr(), string_id, value);
    send_iso_packet(packet, code, type);

    trigger_can();
  } else {

    transport_buffer *opb = make_transport_buffer();
    opb_add8(opb, CHANGE_STRING_VALUE_COMMAND);
    opb_set_string(opb, string_id, value);
    send_transport_buffer(opb, type);

    trigger_can();
  }
}

void set_string_id_value(int string_id, const char *value) {
  WORKING_SET_GUARD();
  set_string_id_value_typed(string_id, value, USER);
}

void set_string_value(const char *name, const char *value) {
  WORKING_SET_GUARD();
  int string_id = string_id_by_str(name);
  set_string_id_value(string_id, value);
}

void replace_string(const char *name, const char *value, const char *fontname) {
  WORKING_SET_GUARD();
  int string_id = string_id_by_str(name);
  int font_id = font_id_by_str(fontname);

  transport_buffer *opb = make_transport_buffer();
  opb_add8(opb, OBJECT_POOL_TRANSFER_FUNCTION);

  opb_add_string(opb, string_id, NULL_OBJECT_ID, value, 480, 480, font_id, 0);
  send_transport_buffer(opb, USER);

  trigger_can();
}

void set_string_font(const char *name, const char *fontname) {
  WORKING_SET_GUARD();
  int string_id = string_id_by_str(name);
  int font_id = font_id_by_str(fontname);

  isopacket *packet = make_isopacket();
  int code = set_string_font_command(packet, get_iso_addr(), vt_addr(), string_id, font_id);
  send_iso_packet(packet, code, USER);

  trigger_can();
}

void set_button_bgcolor(const char *name, const char *color) {
  WORKING_SET_GUARD();
  int button_id = button_id_by_str(name);
  int color_idx = color_index(color);

  isopacket *packet = make_isopacket();
  int code = set_button_bgcolor_command(packet, get_iso_addr(), vt_addr(), button_id, color_idx);
  send_iso_packet(packet, code, USER);

  trigger_can();
}

void set_meter_value(const char *name, int value) {
  WORKING_SET_GUARD();
  int meter_id = meter_id_by_str(name);

  isopacket *packet = make_isopacket();
  int code = change_numeric_value_command(packet, get_iso_addr(), vt_addr(), meter_id, value, 2);
  send_iso_packet(packet, code, USER);

  trigger_can();
}

void set_object_id_position_relative_typed(int parent_id, int object_id, int xoff, int yoff,
                                           enum ui_request_type type) {
  WORKING_SET_GUARD();
  isopacket *packet = make_isopacket();
  int code = set_object_position_relative_command(packet, get_iso_addr(), vt_addr(), parent_id, object_id, xoff, yoff);
  send_iso_packet(packet, code, type);

  trigger_can();
}

void set_object_id_size(int object_id, int width, int height) {
  WORKING_SET_GUARD();
  if (get_working_set_state() == NOSET) {
    clerr("change object id size while no working set!\n");
  }
  isopacket *packet = make_isopacket();
  int code = set_object_size_command(packet, get_iso_addr(), vt_addr(), object_id, width, height);

  send_iso_packet(packet, code, USER);

  trigger_can();
}

void set_preferred_vt(int addr) { preferred_vt = addr; }

void init_language_interface() {
  lq_init(&lqq, BUS_QUEUE_ELEMENTS);
}

#include "transport_buffer.h"

struct vt_info {
  int width;
  int height;
  int vt_version;
};

typedef struct object_pool {
  /* object ids */
  int next_object_id;

  transport_buffer opb;

  /* vt information necessary to parse */
  struct vt_info vt_info;

} object_pool;

void op_zero(object_pool *op);
void op_init(object_pool *op, int cmd_val);
void op_command(object_pool *op, int cmd_val);
void op_free(object_pool *op);
int op_memory_needed(object_pool *op);
int op_transport_done(object_pool *op);
int op_next_packet(object_pool *op, unsigned char *buf);
void op_set_start_packet(object_pool *op, int start);

/* isobus types */
void add_data_mask(object_pool *op, int object_id, int bg_color, int soft_key_mask_id, int objects);
void add_string(object_pool *op, int my_id, int var_id, const char *s, int width, int height, uint16_t font_attributes,
                int justification);
void add_string_var(object_pool *op, int var_id, const char *s);
void add_picture_graphic(object_pool *op, int my_id, unsigned char *bytes, int width, int height);
void add_font_attributes(object_pool *op, int object_id, int color, int size, int style);
void add_soft_key(object_pool *op, int my_id, int bg_color, int objects);
void add_soft_key_mask(object_pool *op, int my_id, int bg_color, int *button_ids, int num_buttons);
void add_line_attributes(object_pool *op, int id, int color, int thickness, int pattern);
void add_fill_attributes(object_pool *op, int id, int color);
void add_rectangle(object_pool *op, int id, int line_attributes, int fill_attributes, int width, int height);
void add_line(object_pool *op, int id, int line_attributes, int width, int height, int direction);
void add_button(object_pool *op, int id, int width, int height, int bg_color, int border_color, int key_code,
                int options, int objects);
void add_container(object_pool *op, int id, int width, int height, int objects);
void add_input_list(object_pool *op, int id, int width, int height, int options, int objects, int selected);
void add_checkbox(object_pool *op, int id, int font_id, int bg_color, int width);
void add_meter(object_pool *op, int id, int width, int needle_color, int border_color, int arc_color, int options,
               int ticks_count, int start_angle, int end_angle, int min_value, int max_value, int value);

#define DEFAULT_OP_SIZE (1000 * 1000)

int next_object_id(object_pool *op);
int unhex(char c);

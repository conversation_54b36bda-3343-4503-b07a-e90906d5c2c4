#include <sys/time.h>
#include "isostate.h"
#include "network.h"
#include "logging.h"
#include "can.h"
#include "tecu_packet.h"

#define STATUS_REPETITION_MS 100

struct tecu_state {
  struct {
    int mm_per_sec;
    int speed_request_active;
    int periodic_active;
  } speed_req;
};

struct tecu_state tecu_state = {.speed_req = {
                                  .mm_per_sec = 0,
                                  .speed_request_active = 0,
                                  .periodic_active = 0
                                }};


static void set_vehicle_speed(sock_pair *ss, int mm_per_sec) {
  cldebug("TECU set speed packet for %d\n", mm_per_sec);
  isopacket packet;
  machine_selected_speed_request_packet(&packet, get_iso_addr(), mm_per_sec);
  send_packet(ss->write, &packet);
}

static void periodic_set_vehicle_speed(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
  cldebug("TECU periodic_set_vehicle_speed\n");
  if (tecu_state.speed_req.speed_request_active) {
    set_vehicle_speed(ss, tecu_state.speed_req.mm_per_sec);
    schedule_relative(periodic_set_vehicle_speed, now, 0, STATUS_REPETITION_MS * 1000);
  } else {
    tecu_state.speed_req.periodic_active = 0;
  }
}

void static start_periodic_set_vehicle_speed(sock_pair *ss, struct timeval *now) {
  cldebug("Start periodic TECU vehicle speed\n");
  if (!tecu_state.speed_req.periodic_active && tecu_state.speed_req.speed_request_active) {
    tecu_state.speed_req.periodic_active = 1;
    set_vehicle_speed(ss, tecu_state.speed_req.mm_per_sec);
    cldebug("Scheduling first TECU periodic_set_vehicle_speed\n");
    schedule_relative(periodic_set_vehicle_speed, now, 0, STATUS_REPETITION_MS * 1000);
  }
}

int tecu_set_vehicle_speed(int mm_per_sec) {
  cldebug("TECU set vehicle speed: %d\n", mm_per_sec);
  tecu_state.speed_req.speed_request_active = 1;
  tecu_state.speed_req.mm_per_sec = mm_per_sec;
  struct timeval now = getnow();
  sock_pair ss = get_ss();
  if (!tecu_state.speed_req.periodic_active) {
    start_periodic_set_vehicle_speed(&ss, &now);
  }
  return TIM_OK;
}

int tecu_stop_set_vehicle_speed() {
  tecu_state.speed_req.speed_request_active = 0;
  isopacket packet;
  machine_selected_speed_relinquish_packet(&packet, get_iso_addr());
  sock_pair ss = get_ss();
  send_packet(ss.write, &packet);
  return 0;
}

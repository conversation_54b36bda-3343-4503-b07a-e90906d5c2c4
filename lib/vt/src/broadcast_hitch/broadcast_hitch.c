#include <stdio.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/time.h>

#include "can.h"
#include "network.h"

int main(int argc, char *argv[]) {
  struct isopacket packet;
  int s = open_can_if(0);
  int position = 90;
  int addr = 0;

  while (1) {
    printf("hitch position %d\n", position);
    hitch_position_packet(&packet, GLOBAL_ADDRESS, GLOBAL_ADDRESS, position);
    if (position == 0) {
      addr = 10;
    } else if (position == 90) {
      addr = -10;
    }
    position += addr;
    send_packet(s, &packet);
    usleep(1000 * 100);
  }
  return 0;
}

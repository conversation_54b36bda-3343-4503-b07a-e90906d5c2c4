#include <opencv2/opencv.hpp>

extern "C" {

#include "logging.h"

using namespace cv;
using namespace std;

unsigned char offset_color(unsigned char c) {
  if (c < (0x00 + 0x33) / 2) {
    return 0;
  }
  if (c < (0x33 + 0x66) / 2) {
    return 1;
  }
  if (c < (0x66 + 0x99) / 2) {
    return 2;
  }
  if (c < (0x99 + 0xcc) / 2) {
    return 3;
  }
  return 4;
}

unsigned char indexed_color(unsigned char r, unsigned char g, unsigned char b) {
  r = offset_color(r);
  g = offset_color(g);
  b = offset_color(b);
  unsigned char index = 16 + r * (52 - 16) + g * (22 - 16) + b;
  return index;
}

unsigned char *open_image(const char name[], int *width, int *height) {
  unsigned char *ret = NULL;
  Mat small;
  Mat image = imread(name);

  if (image.empty()) {
    return ret;
  }

  if (*width != 0 && *height != 0) {
    cv::resize(image, small, cv::Size(*width, *height), 0, 0, INTER_LINEAR);
    image = small;
  }
  if (!image.isContinuous()) {
    clerr("IMAGE IS NOT CONTINUOUS\n");
    return ret;
  }
  Size size = image.size();

  cldebug("image is w %d X h %d\n", size.width, size.height);
  cldebug("row spacing is %d ...", (int)(image.ptr<unsigned char>(1) - image.ptr<unsigned char>(0)));
  if ((int)(image.ptr<unsigned char>(1) - image.ptr<unsigned char>(0)) == size.width * 3) {
    cldebug("Checks.\n");
  } else {
    clerr("image layout FAILS!.\n");
  }
  ret = (unsigned char *)malloc(size.height * size.width);
  unsigned char *pdata = image.ptr<unsigned char>();
  *width = size.width;
  *height = size.height;
  for (int row = 0; row < size.height; row++) {
    for (int col = 0; col < size.width; col++) {
      unsigned char index =
          indexed_color(pdata[2 + col * 3 + row * 3 * size.width], pdata[1 + col * 3 + row * 3 * size.width],
                        pdata[col * 3 + row * 3 * size.width]);
      ret[col + row * size.width] = index;
    }
  }
  return ret;
}

} // extern C

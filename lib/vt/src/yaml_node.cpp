#include <list>
#include <memory>
#include <string>
#include <unordered_map>

#include "yaml_node.h"

extern "C" {
#include "logging.h"
}

using namespace std;

YamlNode::YamlNode(yaml_node_type type) : type(type) {
  if (type == MAP) {
    this->data.map = new unordered_map<string, unique_ptr<YamlNode>>;
  } else if (type == LIST) {
    this->data.list = new std::list<unique_ptr<YamlNode>>;
  } else if (type == VALUE) {
    this->data.value = nullptr;
  }
}

YamlNode::~YamlNode() {
  if (type == MAP) {
    delete this->data.map;
    this->data.map = NULL;
  } else if (type == LIST) {
    delete this->data.list;
    this->data.list = NULL;
  } else if (type == VALUE) {
    if (this->data.value) {
      delete this->data.value;
      this->data.value = nullptr;
    }
  }
}

string YamlNode::type_str() {
  if (type == MAP) {
    return "MAP";
  } else if (type == LIST) {
    return "LIST";
  } else if (type == VALUE) {
    return "VALUE";
  }
  return "UNKNOWN";
}

YamlNode *YamlNode::mfind(const char *what) {
  if (type != MAP) {
    clerr("QUERYING MAP ON A %s\n", type_str().c_str());
    return nullptr;
  }
  std::unordered_map<std::string, std::unique_ptr<YamlNode>>::const_iterator got = this->data.map->find(what);
  if (got == this->data.map->end()) {
    return nullptr;
  }
  return got->second.get();
}

std::string *YamlNode::value() {
  if (type != VALUE) {
    clerr("QUERYING VALUE ON A NON VALUE!\n");
  }
  return this->data.value;
}

std::list<std::unique_ptr<YamlNode>> *YamlNode::list() {
  if (type != LIST) {
    clerr("QUERING LIST ON A NON LIST\n");
  }
  return this->data.list;
}

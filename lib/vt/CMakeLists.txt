cmake_minimum_required(VERSION 3.10)

# Project name and version
project(libvt VERSION 1.2.1)

# Set C and C++ compilers
set(CMAKE_C_COMPILER gcc)
set(CMAKE_CXX_COMPILER g++)

# Set flags
set(CMAKE_C_FLAGS "-fPIC -g")
set(CMAKE_CXX_FLAGS "-std=c++11 -fPIC -g")

# Set the path to the Linux CAN header
set(LINUX_CAN_H "/usr/include/linux/can.h")

# Check if the file exists
if(EXISTS "${LINUX_CAN_H}")
    # Add the definition if the file exists
    add_compile_definitions(HAS_LINUX_CAN)
endif()

# Find OpenCV
find_package(OpenCV REQUIRED)

# Include directories
include_directories(${OpenCV_INCLUDE_DIRS})

# Custom includes for authlib
set(AUTHLIB_PATH "itk_authlib.1.5.2/src")
include_directories(
    ${AUTHLIB_PATH}/AuthLib/include
    ${AUTHLIB_PATH}/CryptoLib/include
    ${AUTHLIB_PATH}/CryptoLib/libs/mbedtls/include
)

# authlib defines
add_compile_definitions(
    MBEDTLS_CONFIG_FILE="cryptolib/cryptolib_mbedtls_config.h"
)

# Source files
file(GLOB C_SRC
    "src/*.c"
    "${AUTHLIB_PATH}/CryptoLib/src/*.c"
    "${AUTHLIB_PATH}/AuthLib/src/*.c"
    "${AUTHLIB_PATH}/CryptoLib/libs/mbedtls/library/*.c"
)
file(GLOB CPP_SRC "src/*.cpp")

# Object files
add_library(c_objects OBJECT ${C_SRC})
add_library(cpp_objects OBJECT ${CPP_SRC})

link_directories(${AUTHLIB_PATH})

# OS-specific settings
if(APPLE)
    execute_process(COMMAND brew --prefix OUTPUT_VARIABLE HOMEBREW_PREFIX OUTPUT_STRIP_TRAILING_WHITESPACE)
    include_directories(${HOMEBREW_PREFIX}/include)
    link_directories(${HOMEBREW_PREFIX}/lib)
endif()

# Create shared library
add_library(vt SHARED $<TARGET_OBJECTS:c_objects> $<TARGET_OBJECTS:cpp_objects>)
if(APPLE)
    set_target_properties(vt PROPERTIES SUFFIX ".so")
endif()
target_link_libraries(vt ${OpenCV_LIBS} yaml pthread)


# Create static library
add_library(vtstatic STATIC $<TARGET_OBJECTS:c_objects> $<TARGET_OBJECTS:cpp_objects>)
set_target_properties(vtstatic PROPERTIES OUTPUT_NAME vt)
target_link_libraries(vt ${OpenCV_LIBS} yaml pthread)

# Packaging (Debian)
set(CPACK_GENERATOR "DEB")
set(CPACK_PACKAGE_NAME "libvt")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_CONTACT "Your Name <<EMAIL>>")
set(CPACK_DEBIAN_PACKAGE_MAINTAINER "Your Name")

install(TARGETS vt DESTINATION /usr/local/lib)
install(FILES vt.h DESTINATION /usr/local/include/libvt)

include(CPack)

add_custom_target(copy_certificates ALL
    COMMAND ${CMAKE_COMMAND} -E copy_directory
        ${CMAKE_CURRENT_SOURCE_DIR}/certificates
        ${CMAKE_CURRENT_BINARY_DIR}/certificates
    COMMENT "Copying over AEF certificates"
)

#include <stdio.h>
#include <string.h>
#include <../src/util.h>

extern char *privateKey;

int main(int argc, char *argv[]) {
  unsigned char binbuf[128];
  unsigned char hexbuf[128];
  int len;
  printf("privateKey: %s\n", privateKey);
  hexstr2bin(privateKey, binbuf, &len);
  printf("%d chars encoded as %d bytes\n", (int)strlen(privateKey), len);
  bin2hexstr(binbuf, len, hexbuf);
  printf("after trx : %s\n", hexbuf);
}

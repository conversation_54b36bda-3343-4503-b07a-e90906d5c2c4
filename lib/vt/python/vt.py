import asyncio
import ctypes
import queue
import threading
from enum import Enum
from typing import TYPE_CHECKING, Any, Awaitable, Callable, List, Optional

if TYPE_CHECKING:
    FutureQueue = queue.Queue[asyncio.Future[Any]]
else:
    FutureQueue = queue.Queue

libname = "libvt.so"

SELF_ASSIGNED_ADDR = 1
AGRICULTURAL_INDUSTRY_GROUP = 2
CARBON_MANUFACTURER_CODE = 1205
CARBON_IDENTIFICATION_NUMBER = 348

TIM_OK = 0
TIM_AUTH_FAIL = -1

TIM_NONE = 1
TIM_CONNECTING = 2
TIM_AUTHENTICATING = 3


# enum log_level { OFF, ERROR, WARN, INFO, DEBUG, DEBUG_PROTO_ESCAPES, DEBUG_PROTO_MINE, DEBUG_PROTO };
class LOG_LEVEL(Enum):
    OFF = 0
    ERROR = 1
    WARN = 2
    INFO = 3
    DEBUG = 4
    DEBUG_PROTO_ESCAPES = 5
    DEBUG_PROTO = 6
    DEBUG_PROTO_MINE = 7


class OPTIONS(int):
    DO_NOTHING = 0
    DO_VT = 1
    DO_TIM = 2
    TRACE_PACKETS = 4
    DO_TECU = 8


class isobus_opts(ctypes.Structure):
    _fields_ = [
        ("can_id", ctypes.c_int),
        ("ip", ctypes.c_char_p),
        ("port", ctypes.c_int),
        ("options", ctypes.c_uint),
        ("layout_dir", ctypes.c_char_p),
        ("certificate_dir", ctypes.c_char_p),
        ("log_level", ctypes.c_int),
        ("log_filename", ctypes.c_char_p),
        ("status_msg_hz", ctypes.c_float),
    ]


#
# Initialize all the callins
#
vtdll = None
set_preferred_vt = None
_start_isobus = None
_set_button_dispatch = None
_set_inputlist_dispatch = None
_set_speed_dispatch = None
_set_hitch_dispatch = None
_set_wakeup_dispatch = None
_set_active_mask = None
_switch_screen = None
_set_string_value = None
_set_string_font = None
_set_button_bgcolor = None
_replace_string = None
_set_meter_value = None
_set_table_cell = None
_append_table_row = None
_delete_table_row = None
_set_isobus_name = None
_tim_set_vehicle_speed = None
_tim_stop_set_vehicle_speed = None
_tecu_set_vehicle_speed = None
_tecu_stop_set_vehicle_speed = None
_tim_set_rear_hitch = None
_tim_stop_set_rear_hitch = None
set_preferred_vt = None
get_tim_status = None
tim_is_playing = None


def initialize_vt(libdir: Optional[str]) -> None:
    global vtll, _start_isobus, _set_button_dispatch, _set_inputlist_dispatch, _set_speed_dispatch, _set_hitch_dispatch, _set_wakeup_dispatch, _set_active_mask, _switch_screen, _set_string_value, _set_string_font, _set_button_bgcolor, _replace_string, _set_meter_value, _set_table_cell, _append_table_row, _delete_table_row, _set_isobus_name, _tim_set_vehicle_speed, _tim_stop_set_vehicle_speed, _tecu_set_vehicle_speed, _tecu_stop_set_vehicle_speed, set_preferred_vt, get_tim_status, tim_is_playing, _tim_set_rear_hitch, _tim_stop_set_rear_hitch

    if libdir is not None:
        vtdll = ctypes.CDLL(libdir + "/" + libname)
    else:
        vtdll = ctypes.CDLL(libname)

    #
    # These require translation
    #
    _start_isobus = vtdll.start_isobus
    _set_button_dispatch = vtdll.set_button_dispatch
    _set_inputlist_dispatch = vtdll.set_inputlist_dispatch
    _set_speed_dispatch = vtdll.set_speed_dispatch
    _set_hitch_dispatch = vtdll.set_hitch_dispatch
    _set_wakeup_dispatch = vtdll.set_wakeup_dispatch
    _set_active_mask = vtdll.set_active_mask
    _switch_screen = vtdll.switch_screen
    _set_string_value = vtdll.set_string_value
    _set_string_font = vtdll.set_string_font
    _set_button_bgcolor = vtdll.set_button_bgcolor
    _replace_string = vtdll.replace_string
    _set_meter_value = vtdll.set_meter_value
    _set_table_cell = vtdll.set_table_cell
    _append_table_row = vtdll.append_table_row
    _delete_table_row = vtdll.delete_table_row
    _set_isobus_name = vtdll.set_isobus_name
    _tim_set_vehicle_speed = vtdll.threaded_tim_set_vehicle_speed
    _tim_stop_set_vehicle_speed = vtdll.threaded_tim_stop_set_vehicle_speed
    _tecu_set_vehicle_speed = vtdll.threaded_tecu_set_vehicle_speed
    _tecu_stop_set_vehicle_speed = vtdll.threaded_tecu_stop_set_vehicle_speed
    _tim_set_rear_hitch = vtdll.threaded_tim_set_rear_hitch
    _tim_stop_set_rear_hitch = vtdll.threaded_tim_stop_set_rear_hitch

    #
    # These are directly callable
    #
    set_preferred_vt = vtdll.set_preferred_vt
    get_tim_status = vtdll.get_tim_status
    tim_is_playing = vtdll.tim_is_playing


# translation layers
_user_button_dispatch = None
_user_inputlist_dispatch = None
_user_speed_dispatch = None
_user_hitch_dispatch = None
_user_event_loop = None


# Async wakeups
_user_future_queue: Optional[FutureQueue] = None
_user_future_lock: Optional[asyncio.Lock] = None

# Async schedule throttle - this is because speed / hitch messages come in faster than set string can process
speed_throttle_sem = threading.Semaphore(value=1)
hitch_throttle_sem = threading.Semaphore(value=1)


async def wakeup_user_onloop() -> None:
    global _user_future_queue
    global _user_future_lock
    assert _user_future_queue is not None
    assert _user_future_lock is not None
    if _user_future_queue.empty():
        print("!!!!!!!!!!!!!!!! --------wakeup user QUEUE is empty?")
    else:
        future = _user_future_queue.get()
        future.set_result(None)
        _user_future_lock.release()


def wakeup_user_func() -> int:
    assert _user_event_loop is not None
    asyncio.run_coroutine_threadsafe(wakeup_user_onloop(), _user_event_loop)
    return 0


async def get_future() -> Awaitable[Any]:
    global _user_future_queue
    global _user_future_lock

    assert _user_event_loop

    await _user_future_lock.acquire()
    future = _user_event_loop.create_future()
    _user_future_queue.put(future)
    return future


class ButtonState(Enum):
    RELEASED = 0
    PRESSED = 1
    HELD = 2
    ABORTED = 3


# Bounce Methods


def exception_protection(cb: Callable[[], Awaitable[Any]]) -> Callable[[], Awaitable[None]]:
    async def f() -> None:
        try:
            await cb()
        except Exception as e:
            print(f"Got exception: {e}")

    return f


def common_dispatch(cb: Callable[[], Awaitable[Any]], loop: asyncio.AbstractEventLoop) -> None:
    protected_cb = exception_protection(cb)
    asyncio.run_coroutine_threadsafe(protected_cb(), loop)


def button_dispatch_xlate(s: ctypes.c_char_p, code: ctypes.c_int) -> int:
    global _user_button_dispatch
    if _user_button_dispatch is not None:
        common_dispatch(_user_button_dispatch(ctypes.string_at(s).decode("utf-8"), code))
    return 0


def inputlist_dispatch_xlate(n: ctypes.c_char_p, v: ctypes.c_char_p) -> int:
    global _user_inputlist_dispatch
    if _user_inputlist_dispatch is not None:
        common_dispatch(
            _user_inputlist_dispatch(ctypes.string_at(n).decode("utf-8"), ctypes.string_at(v).decode("utf-8"))
        )
    return 0


async def speed_dispatch_throttle(cb: Callable[[], Awaitable[Any]]) -> None:
    global speed_throttle_sem
    await cb()
    speed_throttle_sem.release()


def speed_dispatch_xlate(speed: float) -> int:
    global _user_speed_dispatch
    if _user_speed_dispatch is not None:
        common_dispatch(speed_dispatch_throttle(_user_speed_dispatch(speed)))
    return 0


async def hitch_dispatch_throttle(cb: Callable[[], Awaitable[Any]]) -> None:
    global hitch_throttle_sem
    await cb()
    hitch_throttle_sem.release()


def hitch_dispatch_xlate(position: int) -> int:
    global _user_hitch_dispatch
    if _user_hitch_dispatch is not None:
        common_dispatch(hitch_dispatch_throttle(_user_hitch_dispatch(position)))
    return 0


# The PYFUNCTYPE factory needs to stay in scope, so these are global
button_c_cb = ctypes.PYFUNCTYPE(ctypes.c_int, ctypes.c_char_p, ctypes.c_int)(button_dispatch_xlate)
inputlist_c_cb = ctypes.PYFUNCTYPE(ctypes.c_int, ctypes.c_char_p, ctypes.c_char_p)(inputlist_dispatch_xlate)
speed_c_cb = ctypes.PYFUNCTYPE(ctypes.c_int, ctypes.c_float)(speed_dispatch_xlate)
hitch_c_cb = ctypes.PYFUNCTYPE(ctypes.c_int, ctypes.c_int)(hitch_dispatch_xlate)
wakeup_c_cb = ctypes.PYFUNCTYPE(ctypes.c_int)(wakeup_user_func)


# User Call-ins for setup
def set_event_loop(loop: asyncio.AbstractEventLoop) -> None:
    global _user_event_loop
    _user_event_loop = loop


def set_button_dispatch(cb: Callable[[str, int], None]) -> None:
    global _user_button_dispatch
    assert _set_button_dispatch is not None
    _user_button_dispatch = cb
    _set_button_dispatch(button_c_cb)


def set_inputlist_dispatch(cb: Callable[[str, str], None]) -> None:
    global _user_inputlist_dispatch
    assert _user_inputlist_dispatch is not None
    _user_inputlist_dispatch = cb
    _set_inputlist_dispatch(inputlist_c_cb)


def set_speed_dispatch(cb: Callable[[float], None]) -> None:
    global _user_speed_dispatch
    assert _user_speed_dispatch is not None
    _user_speed_dispatch = cb
    _set_speed_dispatch(speed_c_cb)


def set_hitch_dispatch(cb: Callable[[int], None]) -> None:
    global _user_hitch_dispatch
    assert _user_hitch_dispatch is not None
    _user_hitch_dispatch = cb
    _set_hitch_dispatch(hitch_c_cb)


def set_isobus_name(
    self_assigned: int,
    industry_group: int,
    device_class_instance: int,
    device_class: int,
    function: int,
    function_instance: int,
    ecu_instance: int,
    manufacturer_code: int,
    identity_number: int,
) -> None:
    assert _set_isobus_name is not None
    _set_isobus_name(
        self_assigned,
        industry_group,
        device_class_instance,
        device_class,
        function,
        function_instance,
        ecu_instance,
        manufacturer_code,
        identity_number,
    )


# User await-ables


def cstring(v: str) -> bytes:
    return ctypes.create_string_buffer(bytes(v, "utf-8")).raw


async def start_isobus(
    can: int, ip: str, port: int, options: int, log_level: LOG_LEVEL, certificate_dir: str, filename: str
) -> int:
    if _start_isobus is None:
        return -1

    #
    # This is a direct call - this this is the method in libvt which makes the other thread so there's no
    # contention here.
    #
    global _user_future_queue
    global _user_future_lock
    global _set_wakeup_dispatch

    assert _set_wakeup_dispatch is not None
    _user_future_queue = queue.Queue()
    _user_future_lock = asyncio.Lock()
    _set_wakeup_dispatch(wakeup_c_cb)
    opts = isobus_opts(
        can,
        bytes(ip, "utf-8"),
        port,
        options,
        bytes("layout", "utf-8"),
        bytes(certificate_dir, "utf-8"),
        log_level.value,
        bytes(filename, "utf-8"),
        0.0,
    )
    if not _start_isobus(ctypes.byref(opts)):
        raise Exception("Failed to start ISOBUS can connection")

    return 0


#
# Tractor Control Functions
#


async def tim_set_vehicle_speed(mm_per_sec: int) -> None:
    global _tim_set_vehicle_speed
    assert _tim_set_vehicle_speed is not None
    future = await get_future()
    _tim_set_vehicle_speed(mm_per_sec)
    await future


async def tim_stop_set_vehicle_speed() -> None:
    global _tim_stop_set_vehicle_speed
    assert _tim_stop_set_vehicle_speed is not None
    future = await get_future()
    _tim_stop_set_vehicle_speed()
    await future


async def tecu_set_vehicle_speed(mm_per_sec: int) -> None:
    global _tecu_set_vehicle_speed
    assert _tecu_set_vehicle_speed is not None
    future = await get_future()
    _tecu_set_vehicle_speed(mm_per_sec)
    await future


async def tecu_stop_set_vehicle_speed() -> None:
    global _tecu_stop_set_vehicle_speed
    assert _tecu_stop_set_vehicle_speed is not None
    future = await get_future()
    _tecu_stop_set_vehicle_speed()
    await future


async def tim_set_rear_hitch(percentage: float) -> None:
    global _tim_set_rear_hitch
    assert _tim_set_rear_hitch is not None
    future = await get_future()
    _tim_set_rear_hitch(ctypes.c_float(percentage))
    await future


async def tim_stop_set_rear_hitch() -> None:
    global _tim_stop_set_rear_hitch
    assert _tim_stop_set_rear_hitch is not None
    future = await get_future()
    _tim_stop_set_rear_hitch()
    await future


#
# VT Control Functions
#
async def set_active_mask(name: str) -> None:
    global _set_active_mask
    assert _set_active_mask is not None
    future = await get_future()
    _set_active_mask(cstring(name))
    await future


async def switch_screen() -> None:
    global _switch_screen
    assert _switch_screen is not None
    future = await get_future()
    _switch_screen()
    await future


async def set_string_value(name: str, value: str) -> None:
    global _set_string_value
    assert _set_string_value is not None
    future = await get_future()
    _set_string_value(cstring(name), cstring(value))
    await future


async def replace_string(name: str, value: str, fontname: str) -> None:
    global _replace_string
    assert _replace_string is not None
    future = await get_future()
    _replace_string(cstring(name), cstring(value), cstring(fontname))
    await future


async def set_string_font(name: str, fontname: str) -> None:
    global _set_string_font
    assert _set_string_font is not None
    future = await get_future()
    _set_string_font(cstring(name), cstring(fontname))
    await future


async def set_meter_value(name: str, value: int) -> None:
    global _set_meter_value
    assert _set_meter_value is not None
    future = await get_future()
    _set_meter_value(cstring(name), value)
    await future


async def set_button_bgcolor(name: str, color: str) -> None:
    global _set_button_bgcolor
    assert _set_button_bgcolor is not None
    future = await get_future()
    _set_button_bgcolor(cstring(name), cstring(color))
    await future


async def set_table_cell(name: str, row: int, col: int, value: str) -> None:
    global _set_table_cell
    assert _set_table_cell is not None
    future = await get_future()
    _set_table_cell(cstring(name), row, col, cstring(value))
    await future


async def append_table_row(name: str, cols: List[str]) -> None:
    global _append_table_row
    assert _append_table_row is not None
    future = await get_future()
    num = len(cols)
    mylen_type = ctypes.c_char_p * num
    convert_cols = mylen_type()
    for i, col in enumerate(cols):
        convert_cols[i] = cstring(col)
    _append_table_row(cstring(name), convert_cols, len(cols))
    await future


async def delete_table_row(name: str, row: int) -> None:
    global _delete_table_row
    assert _delete_table_row is not None
    future = await get_future()
    _delete_table_row(cstring(name), row)
    await future

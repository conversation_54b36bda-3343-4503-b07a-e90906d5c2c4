paulm@ubuntu-linux-22-04-02-desktop:~/code/libvt/certificates$ cd timbox/
paulm@ubuntu-linux-22-04-02-desktop:~/code/libvt/certificates/timbox$ ls
'01 AEF Root CA'/      '03 Test Lab Manufacturer Sub CA'/  '05 End-entity certificate'/  'CRL for CRL Signing CA'/
'02 Test Lab Sub CA'/  '04 Manufacturer Sub CA'/	   'CRL Signing CA'/		  CRLs/
paulm@ubuntu-linux-22-04-02-desktop:~/code/libvt/certificates/timbox$ cd ..
paulm@ubuntu-linux-22-04-02-desktop:~/code/libvt/certificates$ openssl genpkey -algorithm X25519 -outform PEM -out X25519.sec
paulm@ubuntu-linux-22-04-02-desktop:~/code/libvt/certificates$ ls -ltr
total 8
drwxrwxr-x 10 <USER> <GROUP> 4096 Nov 27 13:07 timbox/
-rw-------  1 <USER> <GROUP>  119 Dec 30 17:17 X25519.sec
paulm@ubuntu-linux-22-04-02-desktop:~/code/libvt/certificates$ openssl pkey -in X25519.sec -outform pem -pubout -out X25519.pub
paulm@ubuntu-linux-22-04-02-desktop:~/code/libvt/certificates$ openssl asn1parse -inform pem -in X25519.pub 
    0:d=0  hl=2 l=  42 cons: SEQUENCE          
    2:d=1  hl=2 l=   5 cons: SEQUENCE          
    4:d=2  hl=2 l=   3 prim: OBJECT            :X25519
    9:d=1  hl=2 l=  33 prim: BIT STRING        
paulm@ubuntu-linux-22-04-02-desktop:~/code/libvt/certificates$ openssl asn1parse -inform pem -in X25519.sec
    0:d=0  hl=2 l=  46 cons: SEQUENCE          
    2:d=1  hl=2 l=   1 prim: INTEGER           :00
    5:d=1  hl=2 l=   5 cons: SEQUENCE          
    7:d=2  hl=2 l=   3 prim: OBJECT            :X25519
   12:d=1  hl=2 l=  34 prim: OCTET STRING      [HEX DUMP]:0420F8930DC7578B77C44C03B2D783E47EFAFABA969124AB5AED6A30837FF53E5D4C
paulm@ubuntu-linux-22-04-02-desktop:~/code/libvt/certificates$ openssl asn1parse -inform pem -in X25519.sec -strparse 12
    0:d=0  hl=2 l=  32 prim: OCTET STRING      [HEX DUMP]:F8930DC7578B77C44C03B2D783E47EFAFABA969124AB5AED6A30837FF53E5D4C

<SUT_Description xmlns="http://www.certtech.com/schema/conformance/SUTDescription" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.certtech.com/schema/conformance/SUTDescription SUTDescription_2023.2.0.0.xsd">
  <!--ECUData version - 1.0.5932.0-->
  <ManufacturerSpecific>
    <ProductName>Carbon TIM Box</ProductName>
    <ProductIdentification />
    <Model />
    <Brand />
    <HardwareVersion />
    <StartUpTime>10</StartUpTime>
    <PowerDownTime>10</PowerDownTime>
    <InputPowerConnections>0</InputPowerConnections>
    <CanDetectIgnitionKeyState>0</CanDetectIgnitionKeyState>
  </ManufacturerSpecific>
  <CertificationData>
    <LaboratoryType>0</LaboratoryType>
    <LaboratoryId>0</LaboratoryId>
    <Year>2024</Year>
    <Revision>0</Revision>
    <ManufacturerCode>1205</ManufacturerCode>
    <ReferenceNumber>0</ReferenceNumber>
    <PhysicalLayerTestRequired>0</PhysicalLayerTestRequired>
    <DlgTestReportAvailable>0</DlgTestReportAvailable>
  </CertificationData>
  <ISO11783-2_Data>
    <CANSamplePoint>80</CANSamplePoint>
    <MonitorTbcPower>0</MonitorTbcPower>
    <EsdCompliance>0</EsdCompliance>
    <SchematicsProvided>0</SchematicsProvided>
    <CanXcvrMfg>Invalid</CanXcvrMfg>
    <CanXcvrPartNo>Invalid</CanXcvrPartNo>
    <CanXcvr11898_2Compliant>0</CanXcvr11898_2Compliant>
    <SignalInformation />
  </ISO11783-2_Data>
  <Devices>
    <NumberOfDevices>1</NumberOfDevices>
    <Device>
      <DeviceName>Control Function 1</DeviceName>
      <!--DefaultSourceAddress has been removed from SUT edit capability.  8/25/2016.-->
      <DefaultSourceAddress>0</DefaultSourceAddress>
      <NAME>0x0000000000000000</NAME>
      <Functionalities>
        <Functionality>
          <FunctionalityName>MinimumECU</FunctionalityName>
          <Capability>1</Capability>
          <FunctionalityGeneration>1</FunctionalityGeneration>
          <FunctionalityCharacteristics>0</FunctionalityCharacteristics>
          <FunctionalityOptions>0</FunctionalityOptions>
          <!--Minimum ECU WSMaster element will be used for Legacy Device until 2018-->
          <WSMaster>false</WSMaster>
        </Functionality>
        <Functionality>
          <FunctionalityName>TIM</FunctionalityName>
          <Capability>1</Capability>
          <Type>Client</Type>
          <FunctionalityGeneration>1</FunctionalityGeneration>
          <FunctionalityCharacteristics>16</FunctionalityCharacteristics>
          <FunctionalityOptions>0x000000000000000000000000037F01</FunctionalityOptions>
          <!--The WSMaster element will be used for TIM Server Master (Master)-->
          <WSMaster>false</WSMaster>
        </Functionality>
      </Functionalities>
      <ISO11783-5_Data>
        <SelfConfigurable>0</SelfConfigurable>
        <IndustryGroup>0</IndustryGroup>
        <IndustryDescription>Global</IndustryDescription>
        <DeviceClassInstance>0</DeviceClassInstance>
        <DeviceClassNumber>0</DeviceClassNumber>
        <DeviceClassDescription />
        <ReservedBit>0</ReservedBit>
        <FunctionNumber>0</FunctionNumber>
        <FunctionDescription>Engine</FunctionDescription>
        <FunctionInstance>0</FunctionInstance>
        <ECU_Instance>0</ECU_Instance>
        <ManufacturerCode>0</ManufacturerCode>
        <Part5ManufacturerName />
        <IdentityNumber>0</IdentityNumber>
        <ReceiveCommandedAddress>0</ReceiveCommandedAddress>
        <IssueCommandedAddress>0</IssueCommandedAddress>
        <IssueCmdAddrList />
      </ISO11783-5_Data>
      <ISO11783-7_Data>
        <ComplianceCertificationType>0x000000</ComplianceCertificationType>
      </ISO11783-7_Data>
      <ISO11783-12_Data>
        <ECU_PartNumber />
        <ECU_SerialNumber />
        <ECU_Location />
        <ECU_Type />
        <ECU_MfgName />
        <DiagnosticProtocolNum>1</DiagnosticProtocolNum>
        <SoftwareIdentification />
        <ISO11783-12_Level_2 />
        <PrevActive_DTC>0</PrevActive_DTC>
        <FaultOccurrences>0</FaultOccurrences>
        <ClearPrevActive_DTC>0</ClearPrevActive_DTC>
      </ISO11783-12_Data>
      <TIM_Client_Data>
        <TimMinVersion>1</TimMinVersion>
        <TimImplVersion>1</TimImplVersion>
        <TimMinAuthVersion>2</TimMinAuthVersion>
        <TimImplAuthVersion>2</TimImplAuthVersion>
        <!--TIM Functionality Options are written as individual bytes-->
        <TimFuncOptByte1>0</TimFuncOptByte1>
        <TimFuncOptByte2>0</TimFuncOptByte2>
        <TimFuncOptByte3>0</TimFuncOptByte3>
        <TimFuncOptByte4>0</TimFuncOptByte4>
        <TimFuncOptByte5>0</TimFuncOptByte5>
        <TimFuncOptByte6>0</TimFuncOptByte6>
        <TimFuncOptByte7>0</TimFuncOptByte7>
        <TimFuncOptByte8>0</TimFuncOptByte8>
        <TimFuncOptByte9>0</TimFuncOptByte9>
        <TimFuncOptByte10>0</TimFuncOptByte10>
        <TimFuncOptByte11>0</TimFuncOptByte11>
        <TimFuncOptByte12>0</TimFuncOptByte12>
        <TimFuncOptByte13>3</TimFuncOptByte13>
        <TimFuncOptByte14>127</TimFuncOptByte14>
        <TimFuncOptByte15>1</TimFuncOptByte15>
        <!--The following bytes are for Client Functions that are Optional-->
        <TimFunctionOptionBytes1_2>0</TimFunctionOptionBytes1_2>
        <TimFunctionOptionBytes3_4>0</TimFunctionOptionBytes3_4>
        <TimFunctionOptionBytes5_6>0</TimFunctionOptionBytes5_6>
        <TimFunctionOptionBytes7_8>0</TimFunctionOptionBytes7_8>
        <TimFunctionOptionBytes10_12>1344</TimFunctionOptionBytes10_12>
        <TimClientStartupTime>1</TimClientStartupTime>
        <TimClientAuthorizationTime>1</TimClientAuthorizationTime>
        <TIMFunction>
          <!-- ***** Rear Hitch ***** -->
          <TIMFunctionId>43</TIMFunctionId>
          <Optional>false</Optional>
          <TIMFunctionFacility>
            <TIMFunctionFacilityName>HitchMotion</TIMFunctionFacilityName>
            <TIMFunctionFacilitySupported>1</TIMFunctionFacilitySupported>
          </TIMFunctionFacility>
          <TIMFunctionFacility>
            <TIMFunctionFacilityName>HitchPosition</TIMFunctionFacilityName>
            <TIMFunctionFacilitySupported>1</TIMFunctionFacilitySupported>
          </TIMFunctionFacility>
        </TIMFunction>
        <TIMFunction>
          <!-- ***** Vehicle Speed ***** -->
          <TIMFunctionId>44</TIMFunctionId>
          <Optional>false</Optional>
          <TIMFunctionFacility>
            <TIMFunctionFacilityName>InForwardDirection</TIMFunctionFacilityName>
            <TIMFunctionFacilitySupported>1</TIMFunctionFacilitySupported>
          </TIMFunctionFacility>
          <TIMFunctionFacility>
            <TIMFunctionFacilityName>InReverseDirection</TIMFunctionFacilityName>
            <TIMFunctionFacilitySupported>1</TIMFunctionFacilitySupported>
          </TIMFunctionFacility>
          <TIMFunctionFacility>
            <TIMFunctionFacilityName>StartMotion</TIMFunctionFacilityName>
            <TIMFunctionFacilitySupported>1</TIMFunctionFacilitySupported>
          </TIMFunctionFacility>
          <TIMFunctionFacility>
            <TIMFunctionFacilityName>StopMotion</TIMFunctionFacilityName>
            <TIMFunctionFacilitySupported>1</TIMFunctionFacilitySupported>
          </TIMFunctionFacility>
          <TIMFunctionFacility>
            <TIMFunctionFacilityName>ForwardSpeedByServer</TIMFunctionFacilityName>
            <TIMFunctionFacilitySupported>1</TIMFunctionFacilitySupported>
          </TIMFunctionFacility>
          <TIMFunctionFacility>
            <TIMFunctionFacilityName>ReverseSpeedByServer</TIMFunctionFacilityName>
            <TIMFunctionFacilitySupported>1</TIMFunctionFacilitySupported>
          </TIMFunctionFacility>
          <TIMFunctionFacility>
            <TIMFunctionFacilityName>ChangeDirection</TIMFunctionFacilityName>
            <TIMFunctionFacilitySupported>1</TIMFunctionFacilitySupported>
          </TIMFunctionFacility>
        </TIMFunction>
        <TIMFunction>
          <!-- ***** External Guidance ***** -->
          <TIMFunctionId>46</TIMFunctionId>
          <Optional>false</Optional>
          <TIMFunctionFacility>
            <TIMFunctionFacilityName>Curvature</TIMFunctionFacilityName>
            <TIMFunctionFacilitySupported>1</TIMFunctionFacilitySupported>
          </TIMFunctionFacility>
        </TIMFunction>
        <!-- This bit set defines the TIM AUX valves that have been selected for testing -->
        <TimAuxValveTestSelected>0</TimAuxValveTestSelected>
      </TIM_Client_Data>
    </Device>
  </Devices>
</SUT_Description>
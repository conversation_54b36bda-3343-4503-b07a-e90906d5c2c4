/**
 * @file  authlib_auth.h
 * @brief Interface file for authentication related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef AUTHLIB_AUTH_H
#define AUTHLIB_AUTH_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib/cryptolib_types.h"

#ifdef __cplusplus
extern "C" {
#endif

#if (CRYPTOLIB_ALT_ECDH == ENABLED)

/**
 * \note Only available if either \ref CRYPTOLIB_ALT_ECDH_EXT or \ref CRYPTOLIB_ALT_ECDH_INT is enabled.
 *
 * This function computes a common secret based on the ECDH algorithm.
 * As input the host private key and the peer public key are used. The function
 * also uses the KDF with a random number to avoid "weak keys". This
 * random number is concatenated with the randomNumberServer and the randomNumberClient.
 * The derived key after the KDF is referred to as the LwA key.
 * This key is split into two parts, and stored as the given lwaKeyClient and lwaKeyServer.
 *
 * If the user wants to abort during time slicing, the function shall be called
 * with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 * and all sub-state machine
 *
 * @param[in] privateKey                     The own private ECDH key
 * @param[in] peerPublicKeyStr               pointer to the string representation of the ECDH public key, null terminated (hexadecimal)
 * @param[in] randomNumberServer             pointer to a random number provided by the server (fixed size of 256bit)
 * @param[in] randomNumberClient             pointer to a random number provided by the client (fixed size of 256bit)
 * @param[in] lwaKeyClient                   key to hold the client LwA key on success
 * @param[in] lwaKeyServer                   key to hold the derived server LwA key on success
 * @param[in] algorithm                      0 - CURVE_25519_KDF_ITK_HMAC_SHA256: use curve 25519 and KDF deviating from NIST SP800-56A r2<br>
 *                                           1 - CURVE_25519_KDF_NIST_SP800_56A_HMAC_SHA256: use curve 25519 and KDF compliant with NIST SP800-56A r2<br>
 * @param[in] slicing                        0 - SLICE_INIT: first step of the computation<br>
 *                                           1 - SLICE_CONTINUE: during computation<br>
 *                                           254 - SLICE_RESET: reset slicing state machines<br>
 *                                           255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                                  0 - CRYPTO_ERR_SUCCESS: common secret computed<br>
 *                                          1 - CRYPTO_ERR_INVALID: computation failed<br>
 *                                          2 - CRYPTO_ERR_CALLAGAIN: computation not complete<br>
 *                                          4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                          7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                         12 - CRYPTO_ERR_BAD_INPUT: wrong slicing parameter given<br>
 *                                         16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 *                                         18 - CRYPTO_ERR_ALT_FAILED: Alt. API returned an error<br>
 */
uint8_t AuthLib_ComputeCommonSecret(CRYPTOLIB_ALT_KEY_TYPE privateKey,
                                    CRYPTOLIB_HUGE const uint8_t* const peerPublicKeyStr,
                                    CRYPTOLIB_HUGE const uint8_t* const randomNumberServer,
                                    CRYPTOLIB_HUGE const uint8_t* const randomNumberClient,
                                    CRYPTOLIB_ALT_KEY_TYPE lwaKeyClient,
                                    CRYPTOLIB_ALT_KEY_TYPE lwaKeyServer,
                                    const uint8_t algorithm,
                                    const uint8_t slicing);

#else

/**
 * This function computes a common secret based on the ECDH algorithm.
 * As input the host private key and the peer public key are used. The function
 * also uses the KDF with a random number to avoid "weak keys". This
 * random number is concatenated with the randomNumberServer and the randomNumberClient.
 * The derived key after the KDF is referred to as the LwA key.
 *
 * If the user wants to abort during time slicing, the function shall be called
 * with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 * and all sub-state machines.
 *
 * @param[in] privateKey                     Pointer to the string representation of
 *                                           the EC private key, null-terminated (hexadecimal)
 * @param[in] peerPublicKeyStr               Pointer to the string representation of
 *                                           the EC public key, null terminated (hexadecimal)
 * @param[in] randomNumberServer             pointer to a random number provided by
 *                                           the server (fixed size of 256bit)
 * @param[in] randomNumberClient             pointer to a random number provided by
 *                                           the client (fixed size of 256bit)
 * @param[in] algorithm                      0 - CURVE_25519_KDF_ITK_HMAC_SHA256: use curve 25519 and KDF deviating from NIST SP800-56A r2<br>
 *                                           1 - CURVE_25519_KDF_NIST_SP800_56A_HMAC_SHA256: use curve 25519 and KDF compliant with NIST SP800-56A r2<br>
 * @param[in] slicing                        0 - SLICE_INIT: first step of the validation<br>
 *                                           1 - SLICE_CONTINUE: during validation<br>
 *                                           254 - SLICE_RESET: reset slicing state machines<br>
 *                                           255 - SLICE_NO_SLICING: no slicing shall be used<br>
 * @param[out] commonSecret                  pointer to the LwA key on success (fixed size
 *                                           of 256bit)
 *
 * @return                                  0 - CRYPTO_ERR_SUCCESS: common secret computed<br>
 *                                          1 - CRYPTO_ERR_INVALID: computation failed<br>
 *                                          2 - CRYPTO_ERR_CALLAGAIN: computation not complete<br>
 *                                          4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                          7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                         12 - CRYPTO_ERR_BAD_INPUT: wrong slicing or algorithm parameter given<br>
 *                                         14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                         15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 *                                         16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t AuthLib_ComputeCommonSecret(CRYPTOLIB_HUGE const uint8_t* const privateKey,
                                    CRYPTOLIB_HUGE const uint8_t* const peerPublicKeyStr,
                                    CRYPTOLIB_HUGE const uint8_t* const randomNumberServer,
                                    CRYPTOLIB_HUGE const uint8_t* const randomNumberClient,
                                    CRYPTOLIB_HUGE uint8_t* commonSecret,
                                    const uint8_t algorithm,
                                    const uint8_t slicing);

#endif /* #if (CRYPTOLIB_ALT_ECDH == ENABLED) */

#ifdef __cplusplus
}
#endif

#endif /* AUTHLIB_AUTH_H */

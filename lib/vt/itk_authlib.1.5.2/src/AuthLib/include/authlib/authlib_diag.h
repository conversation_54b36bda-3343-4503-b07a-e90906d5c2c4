/**
 * @file  authlib_diag.h
 * @brief Interface file for diagnostic internal state information
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef AUTHLIB_DIAG_H
#define AUTHLIB_DIAG_H

#include "authlib_types.h"

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif


#ifdef __cplusplus
extern "C" {
#endif

/**
 * This function can be used to retrieve detailed information about the
 * latest error that occurred. This includes an ID of the code snippet
 * from where the error emerged as well as a detailed error code, if available.
 *
 * @return Struct holding detailed information about the last error occured.
 */
CryptoLib_LastError_t AuthLib_GetLastError(void);

#ifdef __cplusplus
}
#endif

#endif /* AUTHLIB_DIAG_H */

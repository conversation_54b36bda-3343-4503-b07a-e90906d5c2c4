/**
 * @file  authlib_fid.h
 * @brief Contains function IDs for error identification
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im <PERSON>yerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef AUTHLIB_FID_H
#define AUTHLIB_FID_H

/**@ \{*/
#define FID_AUTHLIB_INIT 0x1a010000UL
#define FID_AUTHLIB_COMPUTECOMMONSECRET 0x1b010000UL
#define FID_AUTHLIB_COMPUTECMAC 0x1b020000UL
#define FID_AUTHLIB_VERIFYCMAC 0x1b030000UL
#define FID_AUTHLIB_CHECKCRLVALIDITY 0x1c010000UL
#define FID_AUTHLIB_CHECKCERTIFICATEONCRL 0x1c020000UL
#define FID_AUTHLIB_VERIFYPEERCERTIFICATE 0x1c030000UL
#define FID_AUTHLIB_CHECKPEERNAME 0x1c040000UL
#define FID_AUTHLIB_CHECKPEERCCID 0x1c050000UL
#define FID_AUTHLIB_PARSECERTIFICATE 0x1c060000UL
#define FID_AUTHLIB_INITCERTIFICATE 0x1c070000UL
#define FID_AUTHLIB_FREECERTIFICATE 0x1c080000UL
#define FID_HANDLERETURN 0x1d010000UL
#define FID_HANDLESINGLESTEP 0x1d020000UL
#define FID_AUTHLIB_VERIFYPEERCERTIFICATESCHAIN 0x1d030000UL
#define FID_AUTHLIB_GETLASTERROR 0x1e010000UL
#define FID_AUTHLIB_GENERATERANDOMCHALLENGE 0x1f010000UL
#define FID_AUTHLIB_STOREPRIVATEKEY 0x20010000UL
/**@ \}*/

#endif /* AUTHLIB_FID_H */

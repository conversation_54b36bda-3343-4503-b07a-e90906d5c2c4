/**
 * @file  authlib_types.h
 * @brief typedefs of the AuthLib
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef AUTHLIB_TYPES_H
#define AUTHLIB_TYPES_H

#include "cryptolib/cryptolib_types.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CHALLENGE_LEN 32    /**< Length of CMAC challenge */

/** Enumeration to choose between TIM server or client authentication */
typedef enum {
    CRYPTO_SERVER = 0,   /*!< used by a TIM server */
    CRYPTO_CLIENT        /*!< used by a TIM client */
} TIMType_t;

#ifdef __cplusplus
}
#endif

#endif /* AUTHLIB_TYPES_H */

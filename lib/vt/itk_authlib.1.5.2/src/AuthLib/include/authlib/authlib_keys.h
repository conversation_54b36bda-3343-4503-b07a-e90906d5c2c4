/**
 * @file
 * @brief Interface file for key related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef AUTHLIB_ALT_KEYS_H
#define AUTHLIB_ALT_KEYS_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif


#if (CRYPTOLIB_ALT_KEYS == ENABLED)

#include "cryptolib/cryptolib_types.h"

#ifdef __cplusplus
extern "C"
{
#endif

/**
 * \note Only available if \ref CRYPTOLIB_ALT_KEYS is enabled.
 *
 * This function persists the given private X25519 key with the given key
 * in the alternative crypto provider's key storage.
 *
 * The private key shall be passed as a null-terminated hey string
 * with a length of 64 byte. Before storing the key string is converted to
 * a byte array with a size of 32 byte.
 *
 * @note If the user wants to abort during time slicing, the function shall be called
 *       with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 *       and all sub-state machines.
 *
 * @param[in] privateKey            private X25519 key (null-terminated hex string)
 * @param[in] key                   key used to persist the given key string
 * @param[in] slicing               0 - SLICE_INIT: first step of the storing process<br>
 *                                  1 - SLICE_CONTINUE: during storing process<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *
 * @return                           0 - CRYPTO_ERR_SUCCESS: storing successfully completed<br>
 *                                   4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                   7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedTLS returned error<br>
 *                                  18 - CRYPTO_ERR_ALT_FAILED: HSM returned an error <br>
 */
uint8_t AuthLib_StorePrivateKey(CRYPTOLIB_HUGE const char* privateKey,
                                CRYPTOLIB_ALT_KEY_TYPE key,
                                uint8_t slicing);

#ifdef __cplusplus
}
#endif
#endif /* #if (CRYPTOLIB_ALT_KEYS == ENABLED) */
#endif /* AUTHLIB_ALT_KEYS_H */

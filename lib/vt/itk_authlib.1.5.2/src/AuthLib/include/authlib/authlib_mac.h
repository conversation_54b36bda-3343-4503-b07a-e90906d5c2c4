/**
 * @file
 * @brief Interface file for authentication related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef AUTHLIB_MAC_H
#define AUTHLIB_MAC_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib/cryptolib_types.h"

#ifdef __cplusplus
extern "C"
{
#endif

#if (CRYPTOLIB_ALT_CMAC == ENABLED)

/**
 * \note Only available if \ref CRYPTOLIB_ALT_CMAC is enabled.
 *
 * This function computes a CMAC (response) to a given 256 bit challenge
 * received from the peer by using the CMAC algorithm. Included in the
 * calculation are the received challenge and the respective part of the
 * derived LwA key for the peer.
 *
 * @note: Please use the correct lwa key for client/server side.
 *
 * @param[in] challenge           pointer to the challenge, size is 32 bytes fixed
 * @param[in] lwaKey              symmetric AES key, generated by the keyexchange
 * @param[out] signedChallenge    pointer to the computed MAC (response) on success,
 *                                size is 16 bytes fixed, NULL on error
 *
 * @return                        0 - CRYPTO_ERR_SUCCESS: CMAC successfully completed<br>
 *                                4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                               12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                               14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                               15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedTLS returned error<br>
 *                               18 - CRYPTO_ERR_ALT_FAILED: Alt. provider specific error<br>
 */
uint8_t AuthLib_ComputeCMAC(CRYPTOLIB_HUGE const uint8_t* const challenge,
                            CRYPTOLIB_ALT_KEY_TYPE lwaKey,
                            CRYPTOLIB_HUGE uint8_t signedChallenge[AES128_KEY_LEN]);

/**
 * \note Only available if \ref CRYPTOLIB_ALT_CMAC is enabled.
 *
 * This function verifies the received CMAC (response) computed by the peer
 * with the self-computed CMAC. Both CMACs are compared for equality. The
 * function is called after receiving a response from the counterpart to a
 * provided 256 bit challenge. This step performs the response verification
 * in the FwA respectively LwA.
 *
 * @param[in] peerSignedChallenge    pointer to the MAC received from the peer,
 *                                   size is 16 bytes fixed
 * @param[in] lwaKey                 symmetric AES key, generated by the keyexchange
 * @param[in] challenge              pointer to the original challenge before signing that was sent to the peer,
 *                                   size is 32 bytes fixed
 *
 * @return                           0 - CRYPTO_ERR_SUCCESS: validation successfully completed<br>
 *                                   1 - CRYPTO_ERR_INVALID: validation completed,
 *                                                           but the result is not valid<br>
 *                                   7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED:  mbedTLS returned error<br>
 *                                  18  - CRYPTO_ERR_ALT_FAILED: Alt. provider specific error<br>
 */
uint8_t AuthLib_VerifyCMAC(CRYPTOLIB_HUGE const uint8_t* const peerSignedChallenge,
                           CRYPTOLIB_ALT_KEY_TYPE lwaKey,
                           CRYPTOLIB_HUGE const uint8_t* const challenge);

#else

/**
 * This function computes a CMAC (response) to a given 256 bit challenge
 * received from the peer by using the CMAC algorithm. Included in the
 * calculation are the received challenge and the respective part of the
 * derived LwA key for the peer. The parameter sysconfig defines which part of
 * the 256 bit LwA key is used for the MAC generation (in case of client-to-server
 * authentication the first 128 bit, in case of server-to-client authentication
 * the last 128 bit of the LwA key).
 *
 * @param[in] challenge           pointer to the challenge, size is 32 bytes fixed
 * @param[in] commonSecret        pointer to the LwA key, size is 32 bytes fixed
 * @param[in] sysconfig           0 - CRYPTO_SERVER: AuthLib is used by a TIM server<br>
 *                                1 - CRYPTO_CLIENT: AuthLib is used by a TIM client<br>
 * @param[out] signedChallenge    pointer to the computed MAC (response) on success,
 *                                size is 16 bytes fixed, NULL on error
 *
 * @return                        0 - CRYPTO_ERR_SUCCESS: CMAC successfully completed<br>
 *                                4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                               12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                               14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                               15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedTLS returned error<br>
 */
uint8_t AuthLib_ComputeCMAC(CRYPTOLIB_HUGE const uint8_t* const challenge,
                            CRYPTOLIB_HUGE const uint8_t* const commonSecret,
                            CRYPTOLIB_HUGE uint8_t signedChallenge[AES128_KEY_LEN],
                            const uint8_t sysconfig);

/**
 * This function verifies the received CMAC (response) computed by the peer
 * with the self-computed CMAC. Both CMACs are compared for equality. The
 * function is called after receiving a response from the counterpart to a
 * provided 256 bit challenge. This step performs the response verification
 * in the FwA respectively LwA. The parameter sysconfig defines which part of
 * the 256 bit LwA key is used for the MAC generation (in case of client-to-server
 * authentication the first 128 bit, in case of server-to-client authentication
 * the last 128 bit of the LwA key).
 *
 * @param[in] peerSignedChallenge    pointer to the MAC received from the peer,
 *                                   size is 16 bytes fixed
 * @param[in] commonSecret           pointer to the LwA key, size is 32 bytes fixed
 * @param[in] challenge              pointer to the original challenge before signing that was sent to the peer,
 *                                   size is 32 bytes fixed
 * @param[in] sysconfig              0 - CRYPTO_SERVER: AuthLib is used by a TIM server<br>
 *                                   1 - CRYPTO_CLIENT: AuthLib is used by a TIM client<br>
 *
 * @return                           0 - CRYPTO_ERR_SUCCESS: validation successfully completed<br>
 *                                   1 - CRYPTO_ERR_INVALID: validation completed,
 *                                                           but the result is not valid<br>
 *                                   7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED:  mbedTLS returned error<br>
 */
uint8_t AuthLib_VerifyCMAC(CRYPTOLIB_HUGE const uint8_t* const peerSignedChallenge,
                           CRYPTOLIB_HUGE const uint8_t* const commonSecret,
                           CRYPTOLIB_HUGE const uint8_t* const challenge,
                           const uint8_t sysconfig);

#endif /* #if (CRYPTOLIB_ALT_CMAC == ENABLED) */

#ifdef __cplusplus
}
#endif

#endif /* AUTHLIB_MAC_H */

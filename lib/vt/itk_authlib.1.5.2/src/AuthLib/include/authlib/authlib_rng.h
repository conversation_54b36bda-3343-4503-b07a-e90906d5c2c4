/**
 * @file  authlib_rng.h
 * @brief Interface file for RNG related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef AUTHLIB_RNG_H
#define AUTHLIB_RNG_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib/cryptolib_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This function generates a random number based on a seed, that is provided by
 * the caller. The generated challenge has a size of 256 bit. These random bits
 * can be used by the host application for the challenge (FwA, LwA).
 * When this function is called for the first time, the pointer to the seed must not be NULL.
 * For any further calls the pointer may be NULL (to use the same seed for the random
 * number generation). If CRYPTO_ERR_RESEED is returned, the function shall be called
 * with a new seed.
 *
 * @param[in] seed                  pointer to the seed
 * @param[in] seedSize              size of the seed
 * @param[out] randomChallenge      pointer to the generated random challenge of 32 bytes
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: generation successfully completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error/generation failed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: NULL pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  13 - CRYPTO_ERR_RESEED: random number generation failed due to depleted entropy source<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls error occured<br>
 */
uint8_t AuthLib_GenerateRandomChallenge(CRYPTOLIB_HUGE const uint8_t* const seed,
                                        const uint16_t seedSize,
                                        CRYPTOLIB_HUGE uint8_t* randomChallenge);

#ifdef __cplusplus
}
#endif

#endif /* AUTHLIB_RNG_H */

/**
 * @file
 * @brief Interface file for certificate verification related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef AUTHLIB_CRT_VERIFY_H
#define AUTHLIB_CRT_VERIFY_H

#include "authlib_types.h"

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif


#ifdef __cplusplus
extern "C" {
#endif

#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)

/**
 * \note Only available if \ref CRYPTOLIB_ALT_CERT_VERIFY is enabled.
 *
 * This function checks the validity of a single certificate (only the signature).
 * The function uses the public key of the validationCertificate to verify the
 * validity of the certificate.
 *
 * @note
 * On some alternative crypto providers, the provision of a parent certificate for a hierarchical
 * signature check is not needed and certificate verification works by automatically verifiying
 * certificate signatures against already loaded certificates.
 * In those cases, the provision of the parentCertificate parameter can be omitted but it is often
 * expected to perform verification or injection steps in a particular order. I.e., from root
 * to testlab certificate to end-entity certificate.
 *
 * @note
 * If the user wants to abort during slicing, the function shall be called
 * with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 * and all sub-state machines as well as the internal state of the certificate chain.
 *
 * @param[in] certificate           pointer to the certificate to be verified (byte array in DER format)
 * @param[in] certSize              size of the certificate to be verified in bytes
 * @param[in] validationCertificate pointer to the certificate of the next higher PKI level
 * @param[in] validationCertSize    size of the certificate of the next higher PKI level
 * @param[in] slicing               0 - SLICE_INIT: first step of the validation<br>
 *                                  1 - SLICE_CONTINUE: during validation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *
 * @return                          0 -  CRYPTO_ERR_SUCCESS: validation successfully completed<br>
 *                                  1 -  CRYPTO_ERR_INVALID: certificate invalid<br>
 *                                  2 -  CRYPTO_ERR_CALLAGAIN: check not complete<br>
 *                                  4 -  CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  6 -  CRYPTO_ERR_SIGNATURE_INVALID: signature invalid<br>
 *                                  7 -  CRYPTO_ERR_NULL_POINTER: NULL pointer<br>
 *                                  19 - CRYPTO_ERR_ALT_FAILED: Alt. provider specific error<br>
 */
uint8_t AuthLib_VerifyPeerCertificate(CRYPTOLIB_HUGE const uint8_t* certificate,
                                      const size_t certSize,
                                      CRYPTOLIB_HUGE const uint8_t* validationCertificate,
                                      const size_t validationCertSize,
                                      const uint8_t slicing);

/**
 * \note Only available if \ref CRYPTOLIB_ALT_CERT_VERIFY is enabled.
 *
 * This function checks if the certificate chain of the peer is valid
 * (only the signature of the certificates). The function is needed to build
 * up the certificate chain of the peer and validate this against the
 * application internal certificates. The function starts with the verification
 * of the root certificate downwards towards the end-entity certificate.
 * The error code returned is always the first offending certificate.
 *
 * @note
 * On some alternative crypto providers, the provision of the root certificate for a hierarchical
 * signature check is not needed and certificate verification works by automatically verifiying
 * certificate signatures against already loaded certificates.
 * In this case, the provision of the root certificate parameters can be omitted but it is
 * expected that the root certificate is already loaded into the provider before calling this
 * function. Ultimately, it is up to the user's implementation of \ref CryptoLib_VerifyCertificate
 * if the root certificate is needed here.
 *
 * @note
 * If the user wants to abort during time slicing, the function shall be called
 * with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 * and all sub-state machines.
 *
 * @note
 * If the structure of the certificate chain varies, use AuthLib_VerifyPeerCertificate()
 * to manually validate the chain.
 *
 * @param[in] rootCertificate               pointer to the root certificate (byte array in DER format)
 * @param[in] rootCertificateSize           size of the root certificate in bytes
 * @param[in] peerTestlabCert               pointer to the peer testlab certificate (byte array in DER format)
 * @param[in] peerTestlabCertSize           size of the peer testlab certificate in bytes
 * @param[in] peerManufacCert               pointer to the peer manufacturer certificate (byte array in DER format)
 * @param[in] peerManufacCertSize           size of the peer manufacturer certificate
 * @param[in] peerManufacSeriesCert         pointer to the peer manufacturer series certificate (byte array in DER format)
 * @param[in] peerManufacSeriesCertSize     size of the peer manufacturer series certificate in bytes
 * @param[in] peerDeviceCert                pointer to the peer device certificate (byte array in DER format)
 * @param[in] peerDeviceCertSize            size of the peer device certificate in bytes
 * @param[in] slicing                       0 - SLICE_INIT: first step of the validation<br>
 *                                          1 - SLICE_CONTINUE: during validation<br>
 *                                          254 - SLICE_RESET: reset slicing state machines<br>
 *
 * @return                          0  - CRYPTO_ERR_SUCCESS: validation successfully completed<br>
 *                                  1  - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                                  2  - CRYPTO_ERR_CALLAGAIN: validation not complete<br>
 *                                  4  - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7  - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  8  - CRYPTO_ERR_LAB_CERT_INVALID: lab certificate verification failed<br>
 *                                  9  - CRYPTO_ERR_MANU_CERT_INVALID: manufacturer certificate verification failed<br>
 *                                  10 - CRYPTO_ERR_MANU_SERIES_CERT_INVALID: manufacturer series certificate verification failed<br>
 *                                  11 - CRYPTO_ERR_DEVICE_CERT_INVALID: device certificate verification failed<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 *                                  19 - CRYPTO_ERR_ALT_FAILED: Alt. provider specific error<br>
 */

uint8_t AuthLib_VerifyPeerCertificatesChain(CRYPTOLIB_HUGE const uint8_t* rootCertificate,
                                            const size_t rootCertificateSize,
                                            CRYPTOLIB_HUGE const uint8_t* peerTestlabCert,
                                            const size_t peerTestlabCertSize,
                                            CRYPTOLIB_HUGE const uint8_t* peerManufacCert,
                                            const size_t peerManufacCertSize,
                                            CRYPTOLIB_HUGE const uint8_t* peerManufacSeriesCert,
                                            const size_t peerManufacSeriesCertSize,
                                            CRYPTOLIB_HUGE const uint8_t* peerDeviceCert,
                                            const size_t peerDeviceCertSize,
                                            const uint8_t slicing);

#else

/**
 * This function checks the validity of a single certificate (only the signature).
 * The function uses the public key of the validationCertificate to verify the
 * validity of the certificate. If slicing is used and the validation has not
 * finished, the function returns CRYPTO_ERR_CALLAGAIN.
 * If the user wants to abort during time slicing, the function shall be called
 * with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 * and all sub-state machines.
 *
 * @param[in] certificate                  pointer to the certificate which shall be checked
 * @param[in] validationCertificate        pointer to the certificate of the next higher PKI level
 * @param[in] slicing                      0 - SLICE_INIT: first step of the validation<br>
 *                                         1 - SLICE_CONTINUE: during validation<br>
 *                                         254 - SLICE_RESET: reset slicing state machines<br>
 *                                         255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: validation successfully completed<br>
 *                                  1 - CRYPTO_ERR_INVALID: certificate invalid<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: check not complete<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  6 - CRYPTO_ERR_SIGNATURE_INVALID: signature invalid<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: NULL pointer<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
uint8_t AuthLib_VerifyPeerCertificate(CRYPTOLIB_HUGE const CryptoLib_Crt_t* const certificate,
                                      CRYPTOLIB_HUGE const CryptoLib_Crt_t* const validationCertificate,
                                      const uint8_t slicing);

/**
 * This function checks if the certificate chain of the peer is valid
 * (only the signature of the certificates). The function is needed to build
 * up the certificate chain of the peer and validate this against the
 * application internal certificates. The function starts with the verification
 * of the root certificate downwards towards the end-entity certificate.
 * The error code returned is always the first offending certificate.
 * If the user wants to abort during time slicing, the function shall be called
 * with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 * and all sub-state machines.
 *
 * @param[in] rootCertificate                           pointer to the root certificate
 * @param[in] peerTestlabCertificate                    pointer to the peer testlab certificate
 * @param[in] peerManufacturerCertificate               pointer to the peer manufacturer certificate
 * @param[in] peerManufacturerSeriesCertificate         pointer to the peer manufacturer series certificate
 * @param[in] peerDeviceCertificate                     pointer to the peer device certificate
 * @param[in] slicing                                   0 - SLICE_INIT: first step of the validation<br>
 *                                                      1 - SLICE_CONTINUE: during validation<br>
 *                                                      254 - SLICE_RESET: reset slicing state machines<br>
 *                                                      255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0  - CRYPTO_ERR_SUCCESS: validation successfully completed<br>
 *                                  1  - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                                  2  - CRYPTO_ERR_CALLAGAIN: validation not complete<br>
 *                                  4  - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7  - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  8  - CRYPTO_ERR_LAB_CERT_INVALID: lab certificate verification failed<br>
 *                                  9  - CRYPTO_ERR_MANU_CERT_INVALID: manufacturer certificate verification failed<br>
 *                                  10 - CRYPTO_ERR_MANU_SERIES_CERT_INVALID: manufacturer series certificate verification failed<br>
 *                                  11 - CRYPTO_ERR_DEVICE_CERT_INVALID: device certificate verification failed<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 *                                  16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t AuthLib_VerifyPeerCertificatesChain(CRYPTOLIB_HUGE const CryptoLib_Crt_t* const rootCertificate,
                                            CRYPTOLIB_HUGE const CryptoLib_Crt_t* const peerTestlabCertificate,
                                            CRYPTOLIB_HUGE const CryptoLib_Crt_t* const peerManufacturerCertificate,
                                            CRYPTOLIB_HUGE const CryptoLib_Crt_t* const peerManufacturerSeriesCertificate,
                                            CRYPTOLIB_HUGE const CryptoLib_Crt_t* const peerDeviceCertificate,
                                            const uint8_t slicing);

#endif /* #if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */

#ifdef __cplusplus
}
#endif

#endif /* AUTHLIB_CRT_VERIFY_H */

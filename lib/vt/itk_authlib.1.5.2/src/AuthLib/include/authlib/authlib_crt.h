/**
 * @file  authlib_crt.h
 * @brief Interface file for certificate related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON>lzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef AUTHLIB_CRT_H
#define AUTHLIB_CRT_H

#include "authlib_types.h"

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif


#ifdef __cplusplus
extern "C" {
#endif

/**
 * This function checks the validity of the host application's CRL.
 * As it could be possible, that the CRL is not stored in a coherent
 * accessible memory area, the function provides a possibility to check
 * the CRL in several slices. Therefore the parameter slicingMem shall be used
 * by the host app to indicate, if the CRL is provided in slices or complete
 * (SLICE_NO_SLICING). If slices are used, for the first call of the function
 * the parameter slicing has to be set to SLICE_INIT, for the last slice the
 * value SLICE_FINISH has to be used. For all "middle" slices the parameter
 * slicing has to be set to SLICE_CONTINUE. The parameter crlBuffer shall
 * point to the first byte of the CRL or the slice that shall be read.
 * The parameter crlLength shall be the length of the CRL or the slice in bytes.
 * The first slice shall have a length of at least 12 bytes in order to get the
 * length of the complete CRL.
 *
 * Since this function uses the RSA algorithm to verify the CRL signature,
 * an additonal slicing parameter slicingTime is provided to use the time slicing
 * option.
 *
 * As long as parsing the CRL is not finished, the value of slicingTime
 * is ignored. And vice versa, the value of slicingMem is ignored when the time
 * sliced verification process starts.
 * crlSigningCA may be NULL during parsing the CRL and crlBuffer may be
 * NULL during time slicing.
 *
 * If only one of the slicing mechanisms is used, the verification is
 * started directly after parsing of the CRL is finished. I.e. if no memory
 * slicing is used but time slicing, the first call to AuthLib_CheckCRLValidity
 * shall use SLICE_INIT as slicingTime parameter and hand over the complete CRL.
 *
 * If the user wants to abort during time slicing, the function shall be called with
 * SLICE_RESET as time slicing parameter in order to reset the time slicing state machines
 * and all sub-state machines.
 * The same applies if the user wants to abort during memory slicing: The function
 * shall be called with SLICE_RESET as memory slicing parameter in order to reset
 * the memory slicing state machines.
 *
 * \code
 * AuthLib_CheckCRLValidity(crl, crlLength, &caCert, SLICE_NO_SLICING, SLICE_INIT);
 * \endcode
 *
 * If memory slicing is used but no time slicing, the verification is done in
 * the call to AuthLib_CheckCRLValidity with SLICE_FINISH.
 *
 * \code
 * AuthLib_CheckCRLValidity(crlSlice1, crlSlice1Len, &caCert, SLICE_INIT, SLICE_NO_SLICING);
 * AuthLib_CheckCRLValidity(crlSlice2, crlSlice2Len, &caCert, SLICE_CONTINUE, SLICE_NO_SLICING);
 * .
 * .
 * .
 * // Verification is done without time slicing in the next call
 * AuthLib_CheckCRLValidity(crlSliceN, crlSliceNLen, &caCert, SLICE_FINISH, SLICE_NO_SLICING);
 * \endcode
 *
 * If both slicing mechanisms are used, the verification process starts with
 * the function call where slicingMem = SLICE_FINISH is passed.
 *
 * \code
 * AuthLib_CheckCRLValidity(crlSlice1, crlSlice1Len, &caCert, SLICE_INIT, SLICE_NO_SLICING);
 * AuthLib_CheckCRLValidity(crlSlice2, crlSlice2Len, &caCert, SLICE_CONTINUE, SLICE_NO_SLICING);
 * .
 * .
 * .
 * // Verification starts with time slicing, within the same call to the
 * // function where the last piece of the CRL is handed over.
 * AuthLib_CheckCRLValidity(crlSliceN, crlSliceNLen, &caCert, SLICE_FINISH, SLICE_INIT);
 * AuthLib_CheckCRLValidity(NULL, 0, &caCert, SLICE_NO_SLICING, SLICE_CONTINUE);
 * .
 * .
 * .
 * // Last call will return CRYPTO_ERR_SUCCESS
 * AuthLib_CheckCRLValidity(NULL, 0, &caCert, SLICE_NO_SLICING, SLICE_CONTINUE);
 * \endcode
 *
 *
 * @param[in] crlBuffer             pointer to the buffer where the piece of CRL is stored
 * @param[in] crlLength             size of the piece of CRL which shall be read
 * @param[in] crlSigningCA          pointer to the CRL-Signing sub CA certificate
 * @param[in] slicingMem            0 - SLICE_INIT: first slice of the CRL is provided<br>
 *                                  1 - SLICE_CONTINUE: "middle" slices of the CRL are provided<br>
 *                                  2 - SLICE_FINISH: last slice of the CRL is provided<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no memory slicing shall be used<br>
 * @param[in] slicingTime           0 - SLICE_INIT: first step of the validation<br>
 *                                  1 - SLICE_CONTINUE: during validation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no time slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: CRL valid<br>
 *                                  1 - CRYPTO_ERR_INVALID: CRL invalid<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: check not complete<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  6 - CRYPTO_ERR_SIGNATURE_INVALID: Validation failed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: first CRL slice is to small or the slicing parameters are invalid<br>
 *                                  16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t AuthLib_CheckCRLValidity(CRYPTOLIB_HUGE const uint8_t* const crlBuffer,
                                 const uint16_t crlLength,
                                 CRYPTOLIB_HUGE const CryptoLib_Crt_t* const crlSigningCA,
                                 const uint8_t slicingMem,
                                 const uint8_t slicingTime);

/**
 * This function checks if a certificate's serial number is listed on the host
 * application's CRL.
 * As it could be possible, that the CRL is not stored in a coherent accessible
 * memory area, this function shall provide a possibility to check the CRL in
 * several slices. Therefore the parameter slicing shall be used by the host app
 * to indicate, if the CRL is provided in slices or complete (SLICE_NO_SLICING).
 * If slices are used, for the first call of the function the parameter slicing
 * has to be set to SLICE_INIT, for the last slice the value SLICE_FINISH has
 * to be used. For all "middle" slices the parameter slicing has to be set to
 * "SLICE_CONTINUE". The parameter crlBuffer shall point to the first byte the
 * CRL or the slice that shall be read. The parameter crlLength shall be the
 * length of the CRL or the slice in bytes.
 *
 * If the user wants to abort during memory slicing: The function shall be called
 * with SLICE_RESET as memory slicing parameter in order to reset the slicing
 * state machines and all sub-state machines.
 *
 * @param[in] serialNumber          pointer to serial number in binary format
 * @param[in] serialNumberLength    length of the serial
 * @param[in] crlBuffer             pointer to the buffer where the piece of CRL is stored
 * @param[in] crlLength             size of the slice of CRL which shall be read
 * @param[in] slicing               0 - SLICE_INIT: first slice of the CRL is provided<br>
 *                                  1 - SLICE_CONTINUE: "middle" slices of the CRL are provided<br>
 *                                  2 - SLICE_FINISH: last slice of the CRL is provided<br>
 *                                  254 - SLICE_RESET: reset memory slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no memory slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: certificate not listed on CRL<br>
 *                                  1 - CRYPTO_ERR_INVALID: the input CRL is invalid<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: not finished yet, call again<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  5 - CRYPTO_ERR_CERT_ON_CRL: Certificate listed on CRL<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter, e.g. slicing<br>
 *                                  16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t AuthLib_CheckCertificateOnCRL(CRYPTOLIB_HUGE const uint8_t* const serialNumber,
                                      const uint8_t serialNumberLength,
                                      CRYPTOLIB_HUGE const uint8_t* const crlBuffer,
                                      const uint16_t crlLength,
                                      const uint8_t slicing);

/**
 * This function compares the product identification information that is found in the manufacturer series certificate's
 * organization string with the NAME that is communicated over the ISOBUS. If they match, the function returns
 * CRYPTO_ERR_SUCCESS or CRYPTO_ERR_INVALID otherwise.
 *
 * @param[in] organizationStr       pointer to a manufacturer series certificate's organization string
 * @param[in] peerIsoname           pointer to the peers ISO NAME (length is 8 bytes fixed)
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: name valid<br>
 *                                  1 - CRYPTO_ERR_INVALID: name invalid<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: organizationStr has insufficient length<br>
 */
uint8_t AuthLib_CheckPeerName(CRYPTOLIB_HUGE const uint8_t* const organizationStr,
                              CRYPTOLIB_HUGE const uint8_t* const peerIsoname);

/**
 * This function compares the subject field with organizational unit attribute
 * of the peer's manufacturer series certificate with the CCID that is communicated
 * over the ISOBUS. If they match, the return value will be CRYPT_ERR_SUCCESS
 * otherwise CRYPT_ERR_INVALID. In case of development certificates with their
 * organization's certificate usage field set accordingly, the returned value
 * will always be CRYPTO_ERR_SUCCESS.
 * \note The parameter testYear expects a one-byte integer with an offset of 2000,
 * i.e. testYear shall be 18 for the year 2018.
 *
 * @param[in] peerManuSeriesCert        Pointer to the peer's manufacturer series certificate struct
 * @param[in] manufacturerCode          peer manufacturer code
 * @param[in] testYear                  peer compliance test year
 * @param[in] testRevision              peer compliance test protocol revision
 * @param[in] testlabID                 peer compliance testlab ID
 * @param[in] testReferenceNumber       peer compliance test reference number
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: CCID valid<br>
 *                                  1 - CRYPTO_ERR_INVALID: CCID invalid<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 */
uint8_t AuthLib_CheckPeerCCID(CRYPTOLIB_HUGE const CryptoLib_Crt_t* peerManuSeriesCert,
                              const uint16_t manufacturerCode,
                              const uint8_t testYear,
                              const uint8_t testRevision,
                              const uint16_t testlabID,
                              const uint16_t testReferenceNumber);

/**
 * This function parses certificates and provides the content of these
 * certificates to the host application. The content of these certificates is
 * provided as a structure (CryptoLib_Crt_t).
 *
 * @param[in] certificate            pointer to the certificate that shall be parsed
 * @param[in] certificateLength      size of the certificate
 * @param[out] certificateData       pointer to the structure,
 *                                   where the parsed data is stored
 *
 * @return                           0 - CRYPTO_ERR_SUCCESS: parsing successfully completed<br>
 *                                   1 - CRYPTO_ERR_INVALID: parsing failed<br>
 *                                   4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                   7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
uint8_t AuthLib_ParseCertificate(CRYPTOLIB_HUGE const uint8_t* const certificate,
                                 const uint16_t certificateLength,
                                 CRYPTOLIB_HUGE CryptoLib_Crt_t* certificateData);

/**
 * This function initializes a given CryptoLib_Crt_t instance. All non-pointer members
 * are set to a default value that can be distinguished from any valid value, all
 * pointers are set to NULL. Note: this can lead to memory leaks if one manually
 * allocates memory for any of the members.
 *
 * @param[out] certificate          Pointer to the certificate struct
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: initialization successful<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER null pointer given<br>
 */
uint8_t AuthLib_InitCertificate(CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate);

/**
 * This function frees any allocated ressources in the given CryptoLib_Crt_t struct and
 * resets any member that needs no allocation to a default value.
 *
 * @param[out] certificate
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: successfully freed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 */
uint8_t AuthLib_FreeCertificate(CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate);

#ifdef __cplusplus
}
#endif

#endif /* AUTHLIB_CRT_H */

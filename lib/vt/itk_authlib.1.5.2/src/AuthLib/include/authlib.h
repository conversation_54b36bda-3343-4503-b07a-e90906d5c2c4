/**
 * @file  authlib.h
 * @brief Interfaces of the AuthLib
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef AUTH_LIB_H
#define AUTH_LIB_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib/cryptolib_types.h"
#include "authlib/authlib_auth.h"
#include "authlib/authlib_mac.h"
#include "authlib/authlib_crt.h"
#include "authlib/authlib_crt_verify.h"
#include "authlib/authlib_diag.h"
#include "authlib/authlib_rng.h"
#include "authlib/authlib_types.h"
#if (CRYPTOLIB_ALT_KEYS == ENABLED)
    #include "authlib/authlib_keys.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This function initializes the AuthLib.
 *
 * @return          0 - CRYPTO_ERR_SUCCESS: Initialization successfully completed<br>
 */
uint8_t AuthLib_Init(void);

/**
 * This function deinitializes the AuthLib.
 *
 * @return          0 - CRYPTO_ERR_SUCCESS: Initialization successfully completed<br>
 */
uint8_t AuthLib_Deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* AUTH_LIB_H */

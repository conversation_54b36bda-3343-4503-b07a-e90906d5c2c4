/**
 * @file  authlib_keys.c
 * @brief This file contains the key related functions of the AuthLib
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "authlib.h"
#include "authlib/authlib_fid.h"

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if (CRYPTOLIB_ALT_KEYS == ENABLED)

#define X25519_PRIVKEY_LEN 32u /**< Length of X25519 priv. key in binary representation */

uint8_t AuthLib_StorePrivateKey(CRYPTOLIB_HUGE const char* privateKey,
                                CRYPTOLIB_ALT_KEY_TYPE key,
                                uint8_t slicing)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint8_t privateKeyBuffer[X25519_PRIVKEY_LEN];
    CryptoLib_Slicing_t slc = (CryptoLib_Slicing_t)slicing;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static enum {
        SLC_INACTIVE = 0,
        SLC_ACTIVE
    } slicingState = SLC_INACTIVE;
    CRYPTOLIB_MEMORY_SECTION_END

    if (slc == SLICE_RESET)
    {
        (void)CryptoLib_StoreKey(NULL, 0, key, SLICE_RESET);
    }
    else if ((slc != SLICE_NO_SLICING) && (slc != SLICE_INIT) && (slc != SLICE_CONTINUE))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_AUTHLIB_STOREPRIVATEKEY | 0x00000001UL));
    }
    else if ((slicingState == SLC_ACTIVE) && (slc != SLICE_CONTINUE))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_AUTHLIB_STOREPRIVATEKEY | 0x00000002UL));
    }
    else
    {
        switch (slicingState)
        {
            case SLC_INACTIVE:
                ret = CryptoLib_ConvertKeyStringToByteArray(privateKey, privateKeyBuffer, sizeof(privateKeyBuffer));
                if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    ret = CryptoLib_StoreKey(privateKeyBuffer, strlen(privateKey) / 2u, key, slicing);
                    if (ret == (uint8_t)CRYPTO_ERR_CALLAGAIN)
                    {
                        slicingState = SLC_ACTIVE;
                    }
                }
                break;

            case SLC_ACTIVE:
                ret = CryptoLib_StoreKey(privateKeyBuffer, strlen(privateKey) / 2u, key, slicing);
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                break;
                /* LCOV_EXCL_STOP */
        }
    }

    /* current invocation is completed? */
    if (ret != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        /* reset slicing if invocation ended with an error */
        if ((ret != (uint8_t)CRYPTO_ERR_SUCCESS) && (slicingState == SLC_ACTIVE))
        {
            (void)CryptoLib_StoreKey(NULL, 0, key, SLICE_RESET);
        }

        slicingState = SLC_INACTIVE;
    }

    return ret;
}

#endif /* (CRYPTOLIB_ALT_KEYS == ENABLED) */

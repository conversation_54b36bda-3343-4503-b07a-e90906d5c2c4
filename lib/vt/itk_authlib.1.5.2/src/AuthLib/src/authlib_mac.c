/**
 * @file
 * @brief This file contains all mac related functions of the AuthLib
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif


#include "authlib/authlib_mac.h"
#include "authlib/authlib_types.h"
#include "authlib/authlib_fid.h"

#include "cryptolib/cryptolib_mac.h"
#include "cryptolib/cryptolib_diag.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if (CRYPTOLIB_ALT_CMAC == ENABLED)

uint8_t AuthLib_ComputeCMAC(CRYPTOLIB_HUGE const uint8_t* const challenge,
                            CRYPTOLIB_ALT_KEY_TYPE lwaKey,
                            CRYPTOLIB_HUGE uint8_t signedChallenge[AES128_KEY_LEN])
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    if ((challenge == NULL) || (signedChallenge == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_AUTHLIB_COMPUTECMAC | 0x00000100UL));
    }
    else
    {
        ret = CryptoLib_GenerateCmac(lwaKey, challenge, CHALLENGE_LEN, signedChallenge, (size_t)AES128_KEY_LEN);
    }

    return ret;
}

uint8_t AuthLib_VerifyCMAC(CRYPTOLIB_HUGE const uint8_t* const peerSignedChallenge,
                           CRYPTOLIB_ALT_KEY_TYPE lwaKey,
                           CRYPTOLIB_HUGE const uint8_t* const challenge)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    if ((peerSignedChallenge == NULL) || (challenge == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_AUTHLIB_VERIFYCMAC | 0x00000100UL));
    }
    else
    {
        ret = CryptoLib_VerifyCMac(lwaKey, challenge, CHALLENGE_LEN, peerSignedChallenge, (size_t)AES128_KEY_LEN);
    }

    return ret;
}

#else

/* polyspace +1 MISRA-C3:8.7 [Not a defect:low] "AuthLib_ComputeCMAC is a public interface thus needs external linkage" */
uint8_t AuthLib_ComputeCMAC(CRYPTOLIB_HUGE const uint8_t* const challenge,
                            CRYPTOLIB_HUGE const uint8_t* const commonSecret,
                            CRYPTOLIB_HUGE uint8_t signedChallenge[AES128_KEY_LEN],
                            const uint8_t sysconfig)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint8_t lwaKey[AES128_KEY_LEN];

    if ((challenge == NULL) || (commonSecret == NULL) || (signedChallenge == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_AUTHLIB_COMPUTECMAC | 0x00000001UL));
    }
    else if ((sysconfig != (uint8_t)CRYPTO_SERVER) && (sysconfig != (uint8_t)CRYPTO_CLIENT))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_AUTHLIB_COMPUTECMAC | 0x00000002UL));
    }
    else
    {
        if (sysconfig == (uint8_t)CRYPTO_CLIENT)
        {
            (void)memcpy(lwaKey, commonSecret, (size_t)AES128_KEY_LEN);
        }
        else
        {
            (void)memcpy(lwaKey, &commonSecret[AES128_KEY_LEN], (size_t)AES128_KEY_LEN);
        }

        ret = CryptoLib_GenerateCmac(challenge, CHALLENGE_LEN, lwaKey, signedChallenge);
    }

    return ret;
}

uint8_t AuthLib_VerifyCMAC(CRYPTOLIB_HUGE const uint8_t* const peerSignedChallenge,
                           CRYPTOLIB_HUGE const uint8_t* const commonSecret,
                           CRYPTOLIB_HUGE const uint8_t* const challenge,
                           const uint8_t sysconfig)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint8_t signedChallenge[AES128_KEY_LEN] = {0};
    uint8_t type;

    if (sysconfig == (uint8_t)CRYPTO_SERVER)
    {
        type = (uint8_t)CRYPTO_CLIENT;
    }
    else if (sysconfig == (uint8_t)CRYPTO_CLIENT)
    {
        type = (uint8_t)CRYPTO_SERVER;
    }
    else
    {
        type = 2u; /* invalid value (handled by AuthLib_ComputeCMAC) */
    }

    if ((peerSignedChallenge == NULL) || (commonSecret == NULL) || (challenge == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_AUTHLIB_VERIFYCMAC | 0x00000001UL));
    }
    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    else if ((ret = AuthLib_ComputeCMAC(challenge, commonSecret, signedChallenge, type)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else
    {
        ret = CryptoLib_VerifyCmac(peerSignedChallenge, signedChallenge, (uint8_t)AES128_KEY_LEN);
    }

    return ret;
}

#endif /* #if (CRYPTOLIB_ALT_CMAC == ENABLED) */

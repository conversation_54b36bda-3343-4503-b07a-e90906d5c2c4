/**
 * @file  authlib_rng.c
 * @brief This file contains all RNG related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "authlib/authlib_rng.h"
#include "authlib/authlib_types.h"
#include "authlib/authlib_fid.h"

#include "cryptolib/cryptolib_rng.h"
#include "cryptolib/cryptolib_diag.h"

#include <stddef.h>

uint8_t AuthLib_GenerateRandomChallenge(CRYPTOLIB_HUGE const uint8_t* const seed,
                                        const uint16_t seedSize,
                                        CRYPTOLIB_HUGE uint8_t* randomChallenge)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (randomChallenge == NULL)
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_AUTHLIB_GENERATERANDOMCHALLENGE | 0x00000001UL));
    }
    else
    {
        retVal = CryptoLib_Random(randomChallenge, CHALLENGE_LEN,
                                  seed, seedSize);
    }

    return retVal;
}

/**
 * @file  authlib_crt.c
 * @brief This file contains all certificate related functions of the AuthLib
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "authlib/authlib_crt.h"
#include "authlib/authlib_crt_verify.h"
#include "authlib/authlib_fid.h"

#include "cryptolib/cryptolib_crt.h"
#include "cryptolib/cryptolib_crl.h"
#include "cryptolib/cryptolib_diag.h"

#include <stddef.h>
#include <limits.h>
#include <errno.h>
#include <string.h>
#include <stdlib.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#define MAX_LEN_INDUSTRY_GROUP 2u      /**< max. length of the industry group hexstring (without null termination) */
#define MAX_LEN_DEVICE_CLASS 2u        /**< max. length of the device class hexstring (without null termination) */

#define EXPECTED_OU_LEN 20u            /**< Expected length of the organizational unit string (without null termination) */
#define MAX_FIELD_LEN 4u               /**< Length of the longest base16 entry within the OU string */
#define MANUFAC_CODE_LEN 4u            /**< Length of the base16 manufacturer code */
#define MANUFAC_CODE_OFS 0u            /**< Offset of the manufacturer code within the OU string */
#define TEST_YEAR_LEN 2u               /**< Length of the base16 test year */
#define TEST_YEAR_OFS 5u               /**< Offset of the test year within the OU string */
#define TEST_REV_LEN 2u                /**< Length of the base16 test revision */
#define TEST_REV_OFS 8u                /**< Offset of the test revision within the OU string */
#define TESTLAB_ID_LEN 4u              /**< Length of the base16 testlab ID */
#define TESTLAB_ID_OFS 11u             /**< Offset of the testlab ID within the OU string */
#define TEST_REF_LEN 4u                /**< Length of the base16 test reference number */
#define TEST_REF_OFS 16u               /**< Offset of the test reference number within the OU string */

#define HEX_RADIX 16                    /**< Base for Hex-Formatted strings */

/** Enum for sliced ValidatePeereCertificate states */
typedef enum {
    IDLE = 0,   /**< Ready for new validation requests */
    ACTIVE      /**< Active slicing process */
} VerifyPeerCertificateState_t;

uint8_t AuthLib_CheckCRLValidity(CRYPTOLIB_HUGE const uint8_t* const crlBuffer,
                                 const uint16_t crlLength,
                                 CRYPTOLIB_HUGE const CryptoLib_Crt_t* const crlSigningCA,
                                 const uint8_t slicingMem,
                                 const uint8_t slicingTime)
{
    return CryptoLib_CheckCRLValidity(crlBuffer, crlLength, crlSigningCA, slicingMem, slicingTime);
}

uint8_t AuthLib_CheckCertificateOnCRL(CRYPTOLIB_HUGE const uint8_t* const serialNumber,
                                      const uint8_t serialNumberLength,
                                      CRYPTOLIB_HUGE const uint8_t* const crlBuffer,
                                      const uint16_t crlLength,
                                      const uint8_t slicing)
{
    /* polyspace +1 MISRA-C3:10.5 [No action planned:low] "Invalid values for enum CryptoLib_Slicing_t are handled within the callee." */
    return CryptoLib_CheckCertificateOnCrl(crlBuffer, crlLength, (CryptoLib_Slicing_t)slicing, serialNumber, serialNumberLength);
}

#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)

uint8_t AuthLib_VerifyPeerCertificate(CRYPTOLIB_HUGE const uint8_t* certificate,
                                      const size_t certSize,
                                      CRYPTOLIB_HUGE const uint8_t* validationCertificate,
                                      const size_t validationCertSize,
                                      const uint8_t slicing)
#else

uint8_t AuthLib_VerifyPeerCertificate(CRYPTOLIB_HUGE const CryptoLib_Crt_t* const certificate,
                                      CRYPTOLIB_HUGE const CryptoLib_Crt_t* const validationCertificate,
                                      const uint8_t slicing)
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */
{
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static VerifyPeerCertificateState_t state = IDLE;
    CRYPTOLIB_MEMORY_SECTION_END

    /* polyspace +1 MISRA-C3:10.5 [No action planned:low] "Invalid values for enum CryptoLib_Slicing_t are handled within the callee." */
    const CryptoLib_Slicing_t slc = (CryptoLib_Slicing_t)slicing;

    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slc == SLICE_RESET)
    {
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
        (void)CryptoLib_VerifyCertificate(NULL, 0u, NULL, 0u, SLICE_RESET);
#else
        (void)CryptoLib_VerifyCertificate(NULL, NULL, SLICE_RESET);
#endif
        state = IDLE;
    }
    else
    {
        switch (state)
        {
            case IDLE:
                if ((slc != SLICE_NO_SLICING) &&
                    (slc != SLICE_INIT))
                {
                    /* unexpected slicing param, do nothing */
                    CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_AUTHLIB_VERIFYPEERCERTIFICATE | 0x00000001UL));
                }
                else if (  (certificate == NULL)
#if (CRYPTOLIB_ALT_CERT_VERIFY == DISABLED)
                        || (validationCertificate == NULL)
#endif
                           )
                {
                    CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_AUTHLIB_VERIFYPEERCERTIFICATE | 0x00000002UL));
                }
                else
                {
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
                    retVal = CryptoLib_VerifyCertificate(certificate,
                                                         certSize,
                                                         validationCertificate,
                                                         validationCertSize,
                                                         slc);
#else
                    retVal = CryptoLib_VerifyCertificate(certificate, validationCertificate, slc);
#endif

                    if (retVal == (uint8_t)CRYPTO_ERR_CALLAGAIN)
                    {
                        state = ACTIVE;
                    }
                }
                break;

            case ACTIVE:
                if (slc == SLICE_CONTINUE)
                {
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
                    retVal = CryptoLib_VerifyCertificate(certificate,
                                                         certSize,
                                                         validationCertificate,
                                                         validationCertSize,
                                                         slc);
#else
                    retVal = CryptoLib_VerifyCertificate(certificate, validationCertificate, slc);
#endif

                    if (retVal != (uint8_t)CRYPTO_ERR_CALLAGAIN)
                    {
                        state = IDLE;
                    }
                }
                else
                {
                    /* unexpected slicing param, reset */
                    CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_AUTHLIB_VERIFYPEERCERTIFICATE | 0x00000003UL));
                    state = IDLE;
                }
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_AUTHLIB_VERIFYPEERCERTIFICATE | 0x00000004UL));
                state = IDLE;
                break;
                /* LCOV_EXCL_STOP */
        }
    }

    if ((retVal != (uint8_t)CRYPTO_ERR_SUCCESS) && (retVal != (uint8_t)CRYPTO_ERR_CALLAGAIN))
    {
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
        (void)CryptoLib_VerifyCertificate(NULL, 0u, NULL, 0u, SLICE_RESET);
#else
        (void)CryptoLib_VerifyCertificate(NULL, NULL, SLICE_RESET);
#endif
    }

    return retVal;
}

uint8_t AuthLib_CheckPeerName(CRYPTOLIB_HUGE const uint8_t* const organizationStr,
                              CRYPTOLIB_HUGE const uint8_t* const peerIsoname)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint32_t industryGroup = 0;
    uint8_t industryGroupIso = 0;
    uint32_t deviceClass = 0;
    uint8_t deviceClassIso = 0;
    const uint8_t maskIndustryGroupIso = 0x70;
    const uint8_t maskDeviceClassIso = 0xFE;
    uint8_t offsetIndustryGroup = 0;
    uint8_t offsetDeviceClass = 0;
    uint8_t idx = 0;
    uint8_t strIndustryGroup[MAX_LEN_INDUSTRY_GROUP + 1u];
    uint8_t strDeviceClass[MAX_LEN_DEVICE_CLASS + 1u];
    errno = 0;

    if ((organizationStr == NULL) || (peerIsoname == NULL))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_AUTHLIB_CHECKPEERNAME | 0x00000001UL));
    }
    else if (strlen((const char*)organizationStr) < 6u)
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_AUTHLIB_CHECKPEERNAME | 0x00000002UL));
    }
    else
    {
        /* copy industry group, device class. manufacturer code
         * according to ISOBUS NAME bit fields (ISO 11783-1) */
        (void)memcpy(&industryGroupIso, &peerIsoname[7], (size_t)1);
        industryGroupIso = (industryGroupIso & maskIndustryGroupIso) >> 4u;
        (void)memcpy(&deviceClassIso, &peerIsoname[6], (size_t)1);
        deviceClassIso = (deviceClassIso & maskDeviceClassIso) >> 1u;

        while (  (organizationStr[offsetIndustryGroup] != (uint8_t)'#')
              && (offsetIndustryGroup < MAX_LEN_INDUSTRY_GROUP))
        {
            offsetIndustryGroup++;
        }
        (void)memcpy(strIndustryGroup, &organizationStr[idx], (size_t)offsetIndustryGroup);
        strIndustryGroup[offsetIndustryGroup] = 0x00; /* String terminator */

        idx = offsetIndustryGroup + 1u;
        while (  (organizationStr[idx + offsetDeviceClass] != (uint8_t)'#')
              && (offsetDeviceClass < MAX_LEN_DEVICE_CLASS))
        {
            offsetDeviceClass++;
        }
        (void)memcpy(strDeviceClass, &organizationStr[idx], (size_t)offsetDeviceClass);
        strDeviceClass[offsetDeviceClass] = 0x00; /* String terminator */

        /* polyspace +4 MISRA-C3:22.9 [Nor a defect:low] "strtoul is used in a secure way. Errno is checked after all calls to strtoul,
         *                                                a possible invalid return value will be detected once comparing the parsed results
         *                                                with the expected ones." */
        industryGroup = strtoul((const char*)strIndustryGroup, NULL, HEX_RADIX);
        deviceClass = strtoul((const char*)strDeviceClass, NULL, HEX_RADIX);

        if (  (industryGroupIso != industryGroup)
           || (deviceClassIso != deviceClass)
           || (errno != 0))
        {
            CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_AUTHLIB_CHECKPEERNAME | 0x00000003UL));
        }
    }

    return retVal;
}

uint8_t AuthLib_CheckPeerCCID(CRYPTOLIB_HUGE const CryptoLib_Crt_t* peerManuSeriesCert,
                              const uint16_t manufacturerCode,
                              const uint8_t testYear,
                              const uint8_t testRevision,
                              const uint16_t testlabID,
                              const uint16_t testReferenceNumber)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    char buf[MAX_FIELD_LEN + 1u];

    uint16_t extManufacturerCode;
    uint8_t extTestYear;
    uint8_t extTestRevision;
    uint16_t extTestRefNum;
    uint16_t extTestlabID;
    errno = 0;

    if ((peerManuSeriesCert == NULL) ||
        (peerManuSeriesCert->subject.organizationalUnit == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_AUTHLIB_CHECKPEERCCID | 0x00000001UL));
    }
    else if (strlen((const char*)peerManuSeriesCert->subject.organizationalUnit) != EXPECTED_OU_LEN)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_AUTHLIB_CHECKPEERCCID | 0x00000002UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:22.9 [Nor a defect:low] "strtoul is used in a secure way. Errno is checked after all calls to strtoul,
         *                                                   a possible invalid return value will be detected once comparing the parsed results
         *                                                   with the expected ones." */
        (void)memset(buf, 0, (size_t)(MAX_FIELD_LEN + 1u));
        (void)memcpy(buf, &peerManuSeriesCert->subject.organizationalUnit[MANUFAC_CODE_OFS], (size_t)MANUFAC_CODE_LEN);
        extManufacturerCode = (uint16_t)strtoul(buf, NULL, HEX_RADIX);

        (void)memset(buf, 0, (size_t)(MAX_FIELD_LEN + 1u));
        (void)memcpy(buf, &peerManuSeriesCert->subject.organizationalUnit[TEST_YEAR_OFS], (size_t)TEST_YEAR_LEN);
        extTestYear = (uint8_t)strtoul(buf, NULL, HEX_RADIX);

        (void)memset(buf, 0, (size_t)(MAX_FIELD_LEN + 1u));
        (void)memcpy(buf, &peerManuSeriesCert->subject.organizationalUnit[TEST_REV_OFS], (size_t)TEST_REV_LEN);
        extTestRevision = (uint8_t)strtoul(buf, NULL, HEX_RADIX);

        (void)memset(buf, 0, (size_t)(MAX_FIELD_LEN + 1u));
        (void)memcpy(buf, &peerManuSeriesCert->subject.organizationalUnit[TEST_REF_OFS], (size_t)TEST_REF_LEN);
        extTestRefNum = (uint16_t)strtoul(buf, NULL, HEX_RADIX);

        (void)memset(buf, 0, (size_t)(MAX_FIELD_LEN + 1u));
        (void)memcpy(buf, &peerManuSeriesCert->subject.organizationalUnit[TESTLAB_ID_OFS], (size_t)TESTLAB_ID_LEN);
        extTestlabID = (uint16_t)strtoul(buf, NULL, HEX_RADIX);
        /* polyspace-end */

        if ((manufacturerCode != extManufacturerCode) ||
            (testYear != extTestYear) ||
            (testRevision != extTestRevision) ||
            (testReferenceNumber != extTestRefNum) ||
            (testlabID != extTestlabID) ||
            (errno != 0))
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_AUTHLIB_CHECKPEERCCID | 0x00000004UL));
        }
    }

    return ret;
}

uint8_t AuthLib_ParseCertificate(CRYPTOLIB_HUGE const uint8_t* const certificate,
                                 const uint16_t certificateLength,
                                 CRYPTOLIB_HUGE CryptoLib_Crt_t* certificateData)
{
    return CryptoLib_ParseCertificate(certificate, certificateLength, certificateData);
}

uint8_t AuthLib_InitCertificate(CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate)
{
    return CryptoLib_InitCertificate(certificate);
}

uint8_t AuthLib_FreeCertificate(CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate)
{
    return CryptoLib_FreeCertificate(certificate);
}

/**
 * @file  authlib_crt_chain.c
 * @brief This file contains all functions related to validation of a certificate chain.
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "authlib/authlib_crt_verify.h"
#include "authlib/authlib_crt.h"
#include "authlib/authlib_fid.h"

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib/cryptolib_diag.h"

#include <stddef.h>

#define CONTINUE 0 /**< Statemachine state signaling "continue" */
#define EXIT 1     /**< Statemachine state signaling "exit" */

/** Enum to define slice states for validation of the certificate chain */
typedef enum {
    SLICE_TESTLAB_CERT = 0,             /**< Slice of testlab certificate validation */
    SLICE_MANUFACTURER_CERT,            /**< Slice of manufacturer certificate validation */
    SLICE_MANUFACTURER_SERIES_CERT,     /**< Slice of manufacturer series certificate validation */
    SLICE_PEER_CERT                     /**< Slice of peer device certificate validation */
} ValidateCertificateChainState_t;

/** Enum to define state of slicing in ValidatePeerCertificate */
typedef enum {
    IDLE = 0,                           /**< Slicing of ValidatePeerCertificate not active */
    ACTIVE = 1                          /**< Slicing of ValidatePeerCertificate active */
} ValidateCertificateState_t;

/**
 * Analyzes the return value of an operation in its current ValidateCertificateChainState_t state and
 * determines the next state to be engaged. The return value indicates whether the
 * statemachine shall exit (= 1) or not (= 0). The return value is alway 1 for slicing.
 *
 * @param[out] retVal               Pointer to the return value
 * @param[out] state                Pointer to the internal state
 * @param[out] subState             Pointer to the internal state of sliced sub-functions
 * @param[in] nextState             Value of the next state on success
 * @param[in] certInvalidErr        Value to be returned if validation failed
 * @param[in] slicing               Slicing parameter
 *
 * @return                          1 if the statemachine should exit, 0 otherwise
 */
static uint8_t handleReturn(uint8_t* retVal, ValidateCertificateChainState_t* state, ValidateCertificateState_t* subState,
                            const ValidateCertificateChainState_t nextState, const CryptoLib_Return_t certInvalidErr,
                            const uint8_t slicing);

#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)

/**
 * Performs a single step, i.e. either a single slice or if no slicing is applied one call
 * to ValidatePeerCertificate. Returns the return value of the call to ValidatePeerCertificate.
 *
 * @param[in] issuerCert                    Pointer to the issuer certificate
 * @param[in] issuerCertSize                      Size of the certificate to be verified (in bytes)
 * @param[in] subjectCert                   Pointer to the certificate to be validated
 * @param[in] subjectCertSize                      Size of the certificate to be verified (in bytes)
 * @param[in] validateCertificateState      State of ValidatePeerCertificate (slicing active or not)
 * @param[in] slicing                       Slicing parameter
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: validation successfully completed<br>
 *                                  1 - CRYPTO_ERR_INVALID: certificate invalid<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: check not complete<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  6 - CRYPTO_ERR_SIGNATURE_INVALID: signature invalid<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: NULL pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter<br>
 *                                  19 - CRYPTO_ERR_ALT_FAILED: Alt. provider specific error<br>
 */
static uint8_t handleSingleStep(const uint8_t* issuerCert,
                                const size_t issuerCertSize,
                                const uint8_t* subjectCert,
                                const size_t subjectCertSize,
                                ValidateCertificateState_t validateCertificateState,
                                uint8_t slicing);

#else

/**
 * Performs a single step, i.e. either a single slice or if no slicing is applied one call
 * to ValidatePeerCertificate. Returns the return value of the call to ValidatePeerCertificate.
 *
 * @param[in] issuerCert                    Pointer to the issuer certificate
 * @param[in] subjectCert                   Pointer to the certificate to be validated
 * @param[in] validateCertificateState      State of ValidatePeerCertificate (slicing active or not)
 * @param[in] slicing                       Slicing parameter
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: validation successfully completed<br>
 *                                  1 - CRYPTO_ERR_INVALID: certificate invalid<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: check not complete<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  6 - CRYPTO_ERR_SIGNATURE_INVALID: signature invalid<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: NULL pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter<br>
 */
static uint8_t handleSingleStep(const CryptoLib_Crt_t* const issuerCert,
                                const CryptoLib_Crt_t* const subjectCert,
                                ValidateCertificateState_t validateCertificateState,
                                uint8_t slicing);

#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */

static uint8_t handleReturn(uint8_t* retVal, ValidateCertificateChainState_t* state, ValidateCertificateState_t* subState,
                            const ValidateCertificateChainState_t nextState, const CryptoLib_Return_t certInvalidErr,
                            const uint8_t slicing)
{
    uint8_t ret = CONTINUE;
    if (*retVal == (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        *state = nextState;
        *subState = IDLE;
        /* change retVal to CALLAGAIN if slicing is set and it's not the last step */
        if ((slicing != (uint8_t)SLICE_NO_SLICING) && (nextState != SLICE_TESTLAB_CERT))
        {
            *retVal = (uint8_t)CRYPTO_ERR_CALLAGAIN;
            ret = EXIT;
        }
    }
    else if (*retVal == (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        *subState = ACTIVE;
        ret = EXIT;
    }
    else
    {
        if ((*retVal == (uint8_t)CRYPTO_ERR_SIGNATURE_INVALID) || (*retVal == (uint8_t)CRYPTO_ERR_INVALID))
        {
            CryptoLib_SetError(retVal, (int32_t)certInvalidErr, (uint32_t)(FID_HANDLERETURN | 0x00000001UL));
        }
        else
        {
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
            (void)AuthLib_VerifyPeerCertificate(NULL, 0u, NULL, 0u, (uint8_t)SLICE_RESET);
#else
            (void)AuthLib_VerifyPeerCertificate(NULL, NULL, (uint8_t)SLICE_RESET);
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */
        }

        *state = SLICE_TESTLAB_CERT; /* reset statemachine */
        *subState = IDLE;
        ret = EXIT;
    }

    return ret;
}

#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)

static uint8_t handleSingleStep(const uint8_t* issuerCert,
                                const size_t issuerCertSize,
                                const uint8_t* subjectCert,
                                const size_t subjectCertSize,
                                ValidateCertificateState_t validateCertificateState,
                                uint8_t slicing)
#else

static uint8_t handleSingleStep(const CryptoLib_Crt_t* const issuerCert,
                                const CryptoLib_Crt_t* const subjectCert,
                                ValidateCertificateState_t validateCertificateState,
                                uint8_t slicing)
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    if ((slicing != (uint8_t)SLICE_CONTINUE) && (slicing != (uint8_t)SLICE_NO_SLICING))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_HANDLESINGLESTEP | 0x00000001UL));
    }
    else
    {
        if (slicing == (uint8_t)SLICE_CONTINUE)
        {
            if (validateCertificateState == IDLE)
            {
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
                retVal = AuthLib_VerifyPeerCertificate(subjectCert,
                                                       subjectCertSize,
                                                       issuerCert,
                                                       issuerCertSize,
                                                       (uint8_t)SLICE_INIT);
#else
                retVal = AuthLib_VerifyPeerCertificate(subjectCert,
                                                       issuerCert,
                                                       (uint8_t)SLICE_INIT);
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */
            }
            else if (validateCertificateState == ACTIVE)
            {
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
                retVal = AuthLib_VerifyPeerCertificate(subjectCert,
                                                       subjectCertSize,
                                                       issuerCert,
                                                       issuerCertSize,
                                                       (uint8_t)SLICE_CONTINUE);
#else
                retVal = AuthLib_VerifyPeerCertificate(subjectCert,
                                                       issuerCert,
                                                       (uint8_t)SLICE_CONTINUE);
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */
            }
            else
            {
                /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_HANDLESINGLESTEP | 0x00000002UL));
                /* LCOV_EXCL_STOP */
            }
        }
        else
        {
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
            retVal = AuthLib_VerifyPeerCertificate(subjectCert,
                                                   subjectCertSize,
                                                   issuerCert,
                                                   issuerCertSize,
                                                   slicing);
#else
            retVal = AuthLib_VerifyPeerCertificate(subjectCert,
                                                   issuerCert,
                                                   slicing);
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */
        }
    }

    return retVal;
}

#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)

uint8_t AuthLib_VerifyPeerCertificatesChain(CRYPTOLIB_HUGE const uint8_t* rootCertificate,
                                            const size_t rootCertificateSize,
                                            CRYPTOLIB_HUGE const uint8_t* peerTestlabCert,
                                            const size_t peerTestlabCertSize,
                                            CRYPTOLIB_HUGE const uint8_t* peerManufacCert,
                                            const size_t peerManufacCertSize,
                                            CRYPTOLIB_HUGE const uint8_t* peerManufacSeriesCert,
                                            const size_t peerManufacSeriesCertSize,
                                            CRYPTOLIB_HUGE const uint8_t* peerDeviceCert,
                                            const size_t peerDeviceCertSize,
                                            const uint8_t slicing)
#else

uint8_t AuthLib_VerifyPeerCertificatesChain(CRYPTOLIB_HUGE const CryptoLib_Crt_t* const rootCertificate,
                                            CRYPTOLIB_HUGE const CryptoLib_Crt_t* const peerTestlabCertificate,
                                            CRYPTOLIB_HUGE const CryptoLib_Crt_t* const peerManufacturerCertificate,
                                            CRYPTOLIB_HUGE const CryptoLib_Crt_t* const peerManufacturerSeriesCertificate,
                                            CRYPTOLIB_HUGE const CryptoLib_Crt_t* const peerDeviceCertificate,
                                            const uint8_t slicing)
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static ValidateCertificateChainState_t state = SLICE_TESTLAB_CERT;
    static ValidateCertificateState_t validateCertificateState;
    CRYPTOLIB_MEMORY_SECTION_END

    /* check for NULL here, although this is done again in ValidatePeerCertificate,
     * this way the user is notified immediately not after possibly 2 succesful checks */
    if (slicing == (uint8_t)SLICE_RESET)
    {
        state = SLICE_TESTLAB_CERT;
        validateCertificateState = IDLE;

#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
        (void)AuthLib_VerifyPeerCertificate(NULL, 0u, NULL, 0u, (uint8_t)SLICE_RESET);
#else
        (void)AuthLib_VerifyPeerCertificate(NULL, NULL, (uint8_t)SLICE_RESET);
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */
    }
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
    else if ((peerTestlabCert != NULL) &&
             (peerManufacCert != NULL) &&
             (peerManufacSeriesCert != NULL) &&
             (peerDeviceCert != NULL))
#else
    else if ((rootCertificate != NULL) &&
             (peerTestlabCertificate != NULL) &&
             (peerManufacturerCertificate != NULL) &&
             (peerManufacturerSeriesCertificate != NULL) &&
             (peerDeviceCertificate != NULL))
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case SLICE_TESTLAB_CERT:
                if ((validateCertificateState == IDLE) && ((slicing != (uint8_t)SLICE_NO_SLICING) && (slicing != (uint8_t)SLICE_INIT)))
                {
                    CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_AUTHLIB_VERIFYPEERCERTIFICATESCHAIN | 0x00000001UL));
                }
                else if ((validateCertificateState == ACTIVE) && (slicing != (uint8_t)SLICE_CONTINUE))
                {
                    CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_AUTHLIB_VERIFYPEERCERTIFICATESCHAIN | 0x00000002UL));
                }
                else if ((validateCertificateState != IDLE) && (validateCertificateState != ACTIVE))
                {
                    /* LCOV_EXCL_START: internal enum out of bounds */
                    CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_AUTHLIB_VERIFYPEERCERTIFICATESCHAIN | 0x00000003UL));
                    /* LCOV_EXCL_STOP */
                }
                else
                {
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
                    retVal = AuthLib_VerifyPeerCertificate(peerTestlabCert,
                                                           peerTestlabCertSize,
                                                           rootCertificate,
                                                           rootCertificateSize,
                                                           slicing);
#else
                    retVal = AuthLib_VerifyPeerCertificate(peerTestlabCertificate,
                                                           rootCertificate,
                                                           slicing);
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */
                }

                if (handleReturn(&retVal, &state, &validateCertificateState, SLICE_MANUFACTURER_CERT, CRYPTO_ERR_LAB_CERT_INVALID, slicing) == (uint8_t)EXIT)
                {
                    break;
                }
            /* fallthrough */

            case SLICE_MANUFACTURER_CERT:
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
                retVal = handleSingleStep(peerTestlabCert, peerTestlabCertSize, peerManufacCert, peerManufacCertSize, validateCertificateState, slicing);
#else
                retVal = handleSingleStep(peerTestlabCertificate, peerManufacturerCertificate, validateCertificateState, slicing);
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */

                if (handleReturn(&retVal, &state, &validateCertificateState, SLICE_MANUFACTURER_SERIES_CERT, CRYPTO_ERR_MANU_CERT_INVALID, slicing) == (uint8_t)EXIT)
                {
                    break;
                }
            /* fallthrough */

            case SLICE_MANUFACTURER_SERIES_CERT:
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
                retVal = handleSingleStep(peerManufacCert, peerManufacCertSize, peerManufacSeriesCert, peerManufacSeriesCertSize, validateCertificateState, slicing);
#else
                retVal = handleSingleStep(peerManufacturerCertificate, peerManufacturerSeriesCertificate, validateCertificateState, slicing);
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */

                if (handleReturn(&retVal, &state, &validateCertificateState, SLICE_PEER_CERT, CRYPTO_ERR_MANU_SERIES_CERT_INVALID, slicing) == (uint8_t)EXIT)
                {
                    break;
                }
            /* fallthrough */

            case SLICE_PEER_CERT:
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
                retVal = handleSingleStep(peerManufacSeriesCert, peerManufacSeriesCertSize, peerDeviceCert, peerDeviceCertSize, validateCertificateState, slicing);
#else
                retVal = handleSingleStep(peerManufacturerSeriesCertificate, peerDeviceCertificate, validateCertificateState, slicing);
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */

                (void)handleReturn(&retVal, &state, &validateCertificateState, SLICE_TESTLAB_CERT, CRYPTO_ERR_DEVICE_CERT_INVALID, slicing);

                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds*/
                /* all possible values for the states are covered by the above cases
                 * the default is only for MISRA, if we really get here something
                 * went terribly wrong. */
                CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_AUTHLIB_VERIFYPEERCERTIFICATESCHAIN | 0x00000004UL));
                state = SLICE_TESTLAB_CERT;
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    } /* NULL check */
    else
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_AUTHLIB_VERIFYPEERCERTIFICATESCHAIN | 0x00000005UL));
        state = SLICE_TESTLAB_CERT;

        /* reset AuthLib_ValidatePeerCertificate */
#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)
        (void)AuthLib_VerifyPeerCertificate(NULL, 0u, NULL, 0u, (uint8_t)SLICE_RESET);
#else
        (void)AuthLib_VerifyPeerCertificate(NULL, NULL, (uint8_t)SLICE_RESET);
#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */
    }

    return retVal;
}

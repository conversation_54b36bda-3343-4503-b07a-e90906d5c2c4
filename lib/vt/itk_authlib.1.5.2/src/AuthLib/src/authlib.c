/**
 * @file  authlib.c
 * @brief This file contains the initialization function of the AuthLib
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "authlib.h"
#include "cryptolib.h"

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

uint8_t AuthLib_Init(void)
{
    uint8_t ret = CryptoLib_Init();
#if (CRYPTOLIB_ALT_INIT == ENABLED)
    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        ret = CryptoLib_Alt_Init();
    }
#endif

    return ret;
}

uint8_t AuthLib_Deinit(void)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

#if (CRYPTOLIB_ALT_INIT == ENABLED)
    ret = CryptoLib_Alt_Deinit();
#endif

    return ret;
}

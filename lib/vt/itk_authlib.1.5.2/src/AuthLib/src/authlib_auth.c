/**
 * @file  authlib_auth.c
 * @brief This file contains all authentifcation related functions of the AuthLib
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "authlib/authlib_auth.h"
#include "authlib/authlib_types.h"
#include "authlib/authlib_fid.h"

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib/cryptolib_ecdh.h"
#include "cryptolib/cryptolib_hash.h"
#include "cryptolib/cryptolib_mac.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_keys.h"
#include "cryptolib/cryptolib_utils.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#define SALT_LEN 32         /**< Length of salt */

/** Enum for sliced ComputeCommonSecret states */
typedef enum {
    IDLE = 0,               /**< Inactive */
    ECDH,                   /**< Processing ECDH */
    LOAD_SHARED_SECRET,     /**< Optional ALT-API: Retrieval of ECDH secret */
    KDF,                    /**< Processig KDF */
    STORE_LWA_CLIENT_KEY,   /**< Optional ALT-API: Storage of LwA client key material */
    STORE_LWA_SERVER_KEY    /**< Optional ALT-API: Storage of LwA server key material */
} ComputeCommonSecretState_t;

/** Context for ComputeCommonSecret */
typedef struct {
#if (CRYPTOLIB_ALT_ECDH == ENABLED)
    CRYPTOLIB_ALT_KEY_TYPE commonSecret;                    /**< Alt. API representation of the common secret */
#endif /* #if (CRYPTOLIB_ALT_ECDH == ENABLED) */

#if ((CRYPTOLIB_ALT_ECDH == DISABLED) || (CRYPTOLIB_ALT_ECDH_EXT == ENABLED) || (CRYPTOLIB_ALT_KDF == DISABLED))
    uint8_t commonSecretBytes[ECDH_COMMON_SECRET_LEN];      /**< Plain representation of the common secret */
    size_t commonSecretLen;                                 /**< Length of the common secret retrieved from alt API */
#endif /* #if ((CRYPTOLIB_ALT_ECDH == DISABLED) || (CRYPTOLIB_ALT_ECDH_EXT == ENABLED) || (CRYPTOLIB_ALT_KDF == DISABLED)) */

#if ((CRYPTOLIB_ALT_ECDH == ENABLED) && (CRYPTOLIB_ALT_KDF == DISABLED))
    uint8_t derivedKeyBytes[2 * AES128_KEY_LEN];            /**< Plain representation of the derived key */
#endif /* #if ((CRYPTOLIB_ALT_ECDH == ENABLED) && (CRYPTOLIB_ALT_KDF == DISABLED)) */

    CryptoLib_Curve_t curve;                                /**< Chosen curve parameter */
    CryptoLib_Kdf_t kdf;                                    /**< Chosen KDF parameter */

    /** Slicing sub-state enum */
    enum SlicingState_tag {
        SLC_INACTIVE = 0,  /**< Slicing not active */
        SLC_ACTIVE = 1     /**< Slicing active */
    } slicingState; /**< Slicing sub-state */
} ComputeCommonSecretSlicingContext_t;

/**
 * Assigns the correct value to the given slicing parameter based on current slicing state:
 * - slicing remains unchanged for SLICE_NO_SLICING
 * - slicing remains SLICE_CONTINUE if context indicates active slicing
 * - slicing is forced to SLICE_INIT if context does not indicate active slicing
 *   (context sub-state will also be set to SLC_ACTIVE)
 *
 * @param[in,out]  slicing      ptr to slicing param to be manipulated
 * @param[in,out]  context      ptr to context to be manupulated
 */
STATICFCN void prepareSlicing(CryptoLib_Slicing_t* const slicing, ComputeCommonSecretSlicingContext_t* const context);

/**
 * Resets the sliced downstream Cryptolib functions.
 */
STATICFCN void resetSlicing(void);

/**
 * Initializes a ComputeCommonSecretContext_t to default state with chosen
 * ECDH and KDF parameters
 *
 * @param[in]      algorithm   algortihm to derive parameters from
 * @param[in,out]  context     ptr to context to be manupulated
 *
 * @return 0 - CRYPTO_ERR_SUCCESS<br>
 *         1 - CRYPTO_ERR_BAD_INPUT: invalid algorithm parameter passed<br>
 */
STATICFCN uint8_t initComputeCommonSecret(const uint8_t algorithm, ComputeCommonSecretSlicingContext_t* const context);

/**
 * Translate the algorithm input value into enum values for the curve and KDF
 * to be used.
 *
 * @param[in]  algorithm: value for the algorithm used to compute the common secret
 * @param[out] curve: pointer to which the to be used curve will be written
 * @param[out] kdf: pointer to which the to be used kdf will be written
 * @return 0 - CRYPTO_ERR_SUCCESS<br>
 *         1 - CRYPTO_ERR_BAD_INPUT: invalid algorithm parameter passed<br>
 */
STATICFCN uint8_t getAlgorithms(const uint8_t algorithm,
                                CryptoLib_Curve_t* curve,
                                CryptoLib_Kdf_t* kdf);

#if (CRYPTOLIB_ALT_ECDH == ENABLED)

uint8_t AuthLib_ComputeCommonSecret(CRYPTOLIB_ALT_KEY_TYPE privateKey,
                                    CRYPTOLIB_HUGE const uint8_t* const peerPublicKeyStr,
                                    CRYPTOLIB_HUGE const uint8_t* const randomNumberServer,
                                    CRYPTOLIB_HUGE const uint8_t* const randomNumberClient,
                                    CRYPTOLIB_ALT_KEY_TYPE lwaKeyClient,
                                    CRYPTOLIB_ALT_KEY_TYPE lwaKeyServer,
                                    const uint8_t algorithm,
                                    const uint8_t slicing)
#else

uint8_t AuthLib_ComputeCommonSecret(CRYPTOLIB_HUGE const uint8_t* const privateKey,
                                    CRYPTOLIB_HUGE const uint8_t* const peerPublicKeyStr,
                                    CRYPTOLIB_HUGE const uint8_t* const randomNumberServer,
                                    CRYPTOLIB_HUGE const uint8_t* const randomNumberClient,
                                    CRYPTOLIB_HUGE uint8_t* commonSecret,
                                    const uint8_t algorithm,
                                    const uint8_t slicing)
#endif /* #if #if (CRYPTOLIB_ALT_ECDH == ENABLED */
{
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static ComputeCommonSecretState_t state = IDLE;
    static ComputeCommonSecretSlicingContext_t context;
    CRYPTOLIB_MEMORY_SECTION_END

    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint8_t salt[2 * SALT_LEN];

    /* polyspace +1 MISRA-C3:10.5 [No action planned:low] "Invalid values for enum CryptoLib_Slicing_t are handled within the callee." */
    CryptoLib_Slicing_t slc = (CryptoLib_Slicing_t)slicing;

    if (slc == SLICE_RESET)
    {
        resetSlicing();
        state = IDLE;
        CryptoLib_Zeroize(&context, sizeof(context));
    }
    else if ((slc != SLICE_NO_SLICING) && (slc != SLICE_INIT) && (slc != SLICE_CONTINUE))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_AUTHLIB_COMPUTECOMMONSECRET | 0x00000001UL));
    }
    else if ((state != IDLE) && (slc == SLICE_NO_SLICING))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_AUTHLIB_COMPUTECOMMONSECRET | 0x00000004UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case IDLE:
                ret = initComputeCommonSecret(algorithm, &context);
                if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    state = ECDH;
                }
                else
                {
                    break;
                }
            /* fallthrough */

            case ECDH:
                prepareSlicing(&slc, &context);
#if (CRYPTOLIB_ALT_ECDH_INT == ENABLED)
                ret = CryptoLib_Ecdh(privateKey,
                                     (const char*)peerPublicKeyStr,
                                     context.curve,
                                     slc,
                                     &context.commonSecret);

#elif (CRYPTOLIB_ALT_ECDH_EXT == ENABLED)
                ret = CryptoLib_Ecdh(privateKey,
                                     (const char*)peerPublicKeyStr,
                                     context.curve,
                                     slc,
                                     context.commonSecretBytes);
#else
                ret = CryptoLib_Ecdh((const char*)privateKey,
                                     (const char*)peerPublicKeyStr,
                                     context.curve,
                                     slc,
                                     context.commonSecretBytes);
#endif /* #if (CRYPTOLIB_ALT_ECDH_INT == ENABLED) */
                if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    context.slicingState = SLC_INACTIVE;
#if ((CRYPTOLIB_ALT_ECDH_INT == ENABLED) && (CRYPTOLIB_ALT_KDF == DISABLED))
                    /* ECDH output stored in alt. crypto provider needs to be retrieved for further processing
                     * with default KDF implementation */
                    state = LOAD_SHARED_SECRET;
#else
                    /* ECDH output either stored in alt. crypto provider that can be used directly by alt. KDF
                     * implementation or no alt API was used at all and result in plain bytes can be used */
                    state = KDF;
#endif /* #if ((CRYPTOLIB_ALT_ECDH_INT == ENABLED) && (CRYPTOLIB_ALT_KDF == DISABLED)) */
                }
                else
                {
                    break;
                }
                /* fallthrough */

#if ((CRYPTOLIB_ALT_KDF == DISABLED) && (CRYPTOLIB_ALT_ECDH_INT == ENABLED))
            case LOAD_SHARED_SECRET:
                prepareSlicing(&slc, &context);
                ret = CryptoLib_LoadKey(context.commonSecretBytes, &context.commonSecretLen, context.commonSecret, slc);
                if ((ret == (uint8_t)CRYPTO_ERR_SUCCESS) && (context.commonSecretLen == ECDH_COMMON_SECRET_LEN))
                {
                    context.slicingState = SLC_INACTIVE;
                    state = KDF;
                }
                else
                {
                    break;
                }
#endif /* ((CRYPTOLIB_ALT_KDF == DISABLED) && (CRYPTOLIB_ALT_ECDH_INT == ENABLED)) */
            /* fallthrough */

            case KDF:
                /* concatenate random numbers of server and client to form the salt */
                (void)memcpy(&salt[0], randomNumberServer, (size_t)SALT_LEN);
                (void)memcpy(&salt[SALT_LEN], randomNumberClient, (size_t)SALT_LEN);

                prepareSlicing(&slc, &context);
#if (CRYPTOLIB_ALT_KDF == ENABLED)
                ret = CryptoLib_KeyDerivationFunction(context.commonSecret,
                                                      salt,
                                                      (uint16_t)sizeof(salt),
                                                      context.kdf,
                                                      lwaKeyClient,
                                                      lwaKeyServer,
                                                      slc);
#elif (CRYPTOLIB_ALT_ECDH == ENABLED)
                ret = CryptoLib_KeyDerivationFunction(context.commonSecretBytes,
                                                      ECDH_COMMON_SECRET_LEN,
                                                      salt,
                                                      (uint16_t)sizeof(salt),
                                                      context.kdf,
                                                      context.derivedKeyBytes);
#else
                ret = CryptoLib_KeyDerivationFunction(context.commonSecretBytes,
                                                      ECDH_COMMON_SECRET_LEN,
                                                      salt,
                                                      (uint16_t)sizeof(salt),
                                                      context.kdf,
                                                      commonSecret);
#endif /* #if (CRYPTOLIB_ALT_KDF == ENABLED) */
                if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    context.slicingState = SLC_INACTIVE;
#if ((CRYPTOLIB_ALT_ECDH == ENABLED) && (CRYPTOLIB_ALT_KDF == DISABLED))
                    /* AuthLib_ComputeCommonSecret was called with alt. API but default KDF was used. Resulting
                     * derived key needs to be split and stored as two LwA keys */
                    state = STORE_LWA_CLIENT_KEY;
#else
                    /* AuthLib_ComputeCommonSecret was called either called with regular API or the KDF result is
                     * already split and stored as two separate keys -> everything is done */
                    state = IDLE;
                    break;
#endif
                }
                else
                {
                    break;
                }
                /* fallthrough */

#if ((CRYPTOLIB_ALT_ECDH == ENABLED) && (CRYPTOLIB_ALT_KDF == DISABLED))
            case STORE_LWA_CLIENT_KEY:
                prepareSlicing(&slc, &context);
                ret = CryptoLib_StoreKey(&context.derivedKeyBytes[0], AES128_KEY_LEN, lwaKeyClient, slc);
                if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    context.slicingState = SLC_INACTIVE;
                    state = STORE_LWA_SERVER_KEY;
                }
                else
                {
                    break;
                }
            /* fallthrough */

            case STORE_LWA_SERVER_KEY:
                prepareSlicing(&slc, &context);
                ret = CryptoLib_StoreKey(&context.derivedKeyBytes[AES128_KEY_LEN], AES128_KEY_LEN, lwaKeyServer, slc);
                if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    context.slicingState = SLC_INACTIVE;
                    state = IDLE;
                }
                break;
#endif

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                /* something went terribly wrong to reach this case. reset */
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_AUTHLIB_COMPUTECOMMONSECRET | 0x00000003UL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }

    /* current invocation is completed? */
    if (ret != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        /* reset slicing if invocation ended with an error */
        if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
        {
            resetSlicing();
        }

        /* reset internal state and context */
        state = IDLE;
        CryptoLib_Zeroize(&context, sizeof(context));
    }

    return ret;
}

STATICFCN uint8_t getAlgorithms(const uint8_t algorithm,
                                CryptoLib_Curve_t* curve,
                                CryptoLib_Kdf_t* kdf)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    switch (algorithm)
    {
        case 0:
            *curve = CURVE_25519_BE;
            *kdf = KDF_ITK_HMAC_SHA256;
            break;
        case 1:
            *curve = CURVE_25519_LE;
            *kdf = KDF_NIST_SP800_56A_HMAC_SHA256;
            break;
        default:
            ret = (uint8_t)CRYPTO_ERR_BAD_INPUT;
            break;
    }

    return ret;
}

STATICFCN uint8_t initComputeCommonSecret(const uint8_t algorithm, ComputeCommonSecretSlicingContext_t* const context)
{
    (void)memset(context, 0, sizeof(*context));
#if ((CRYPTOLIB_ALT_ECDH == DISABLED) || (CRYPTOLIB_ALT_ECDH_EXT == ENABLED) || (CRYPTOLIB_ALT_KDF == DISABLED))
    context->commonSecretLen = ECDH_COMMON_SECRET_LEN;
#endif /* #if ((CRYPTOLIB_ALT_ECDH == DISABLED) || (CRYPTOLIB_ALT_ECDH_EXT == ENABLED) || (CRYPTOLIB_ALT_KDF == DISABLED)) */

    return getAlgorithms(algorithm, &context->curve, &context->kdf);
}

STATICFCN void prepareSlicing(CryptoLib_Slicing_t* const slicing, ComputeCommonSecretSlicingContext_t* const context)
{
    if ((*slicing != SLICE_NO_SLICING) && (context->slicingState == SLC_INACTIVE))
    {
        context->slicingState = SLC_ACTIVE;
        *slicing = SLICE_INIT;
    }
}

STATICFCN void resetSlicing(void)
{
#if ((CRYPTOLIB_ALT_ECDH == ENABLED) || (CRYPTOLIB_ALT_KDF == ENABLED))
    CRYPTOLIB_ALT_KEY_TYPE nullkey;
    (void)memset(&nullkey, 0, sizeof(nullkey));
#endif

#if (CRYPTOLIB_ALT_ECDH == ENABLED)
    (void)CryptoLib_Ecdh(nullkey, NULL, CURVE_25519_BE, SLICE_RESET, NULL);
#else
    (void)CryptoLib_Ecdh(NULL, NULL, CURVE_25519_BE, SLICE_RESET, NULL);
#endif

#if (CRYPTOLIB_ALT_KDF == ENABLED)
    (void)CryptoLib_KeyDerivationFunction(nullkey, NULL, 0u, KDF_NIST_SP800_56A_HMAC_SHA256, nullkey, nullkey, SLICE_RESET);
#endif

#if ((CRYPTOLIB_ALT_KDF == DISABLED) && (CRYPTOLIB_ALT_ECDH_INT == ENABLED))
    (void)CryptoLib_LoadKey(NULL, NULL, nullkey, SLICE_RESET);
#endif

#if ((CRYPTOLIB_ALT_ECDH == ENABLED) && (CRYPTOLIB_ALT_KDF == DISABLED))
    (void)CryptoLib_StoreKey(NULL, 0u, nullkey, SLICE_RESET);
#endif
}

CC=gcc
CPP=g++
# CFLAGS= -c -O2
CFLAGS= -c -g
INCLUDES=-ICryptoLib/include -ICryptoLib/libs/mbedtls/include/ -IAuthLib/include -DMBEDTLS_CONFIG_FILE="<cryptolib/cryptolib_mbedtls_config.h>"

LFLAGS=

C_SRC=$(wildcard CryptoLib/src/*.c) $(wildcard AuthLib/src/*.c) $(wildcard CryptoLib/libs/mbedtls/library/*.c)
C_OBJ=$(C_SRC:%.c=%.o)
LIB=libauthlib.so
ARCHIVE=libauthlib.a

all: ${LIB} ${ARCHIVE}

${LIB}: ${C_OBJ}
	${CC} -shared -o $@ $^ ${LFLAGS} 

${ARCHIVE}: ${C_OBJ}
	ar rcs $@ $^

%.o: %.c
	${CC} -fPIC ${INCLUDES} ${CFLAGS} -c $< -o $@

clean:
	rm -f *.o
	rm -f ${ARCHIVE}
	rm -f ${LIB}

/**
 * @file  cryptolib_config.h
 * @brief Configuration file for CryptoLib
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON>lzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_CONFIG_H
#define CRYPTOLIB_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

#define ENABLED 1  /**< Enable a feature */
#define DISABLED 0 /**< Disable a feature */

/**\defgroup mem-alloc User defined memory allocation settings
 *
 * Per default, the mbedTLS memory abstraction layer is enabled to emulate dynamic
 * memory allocations on a statically allocated working buffer. You can opt out of
 * this feature and use your platform's default allocation functions by commenting
 * out the STATIC_MEM_ALLOC define.
 * Depending on your platform, you might need to increase the size of the buffer and/or
 * set the alignment according to your word-size.
 *
 * @note If STATIC_MEM_ALLOC is not defined, the other settings remain without effect.
 *
 * @warning A wrong alignment leads to a "strict aliasing rule violation", resulting in
 *          undefined behavior. Use your word size in bytes as an alignment!
 */
/** @addtogroup mem-alloc */
/*@{*/
#define STATIC_MEM_ALLOC ENABLED         /**< Use a static buffer for alloc and free */
#define STATIC_MEM_ALLOC_SIZE 21000      /**< Size of static buffer */
#define STATIC_MEM_ALIGNMENT sizeof(int) /**< Alignment of the static buffer */
/*@}*/

/**\defgroup mem-qual User defined memory type qualifier for mixed memory models
 *
 * On 16-Bit platforms, the AuthLib needs to be built with a huge or segmented
 * huge memory model. If the rest of the application is built with memory model
 * near, special measures need to be put in place for correct interfacing. Thus,
 * public function parameter pointers have a CRYPTOLIB_HUGE tag that needs to be
 * defined according to the selected memory model for the library and your compiler.
 *
 * Example:\n
 * \code{.c}
 * #define CRYPTOLIB_HUGE __huge
 * \endcode
 *
 * @note There are issues with mixed memory model builds beyond their interfaces.
 * It is entirely possible that your linker cannot choose the correct standard
 * library functionality (memset, memcpy, ...) for both, the application (near
 * functions needed) and the library (huge/shuge functions needed) itself. Refer
 * to your compiler manual and enter the specific implementation's names
 * in cryptolib_redef_stdlib.h to act as replacements within the library. The
 * library needs to be compiled with the define "CRYPTOLIB_REDEF_STDLIB" set
 * globally.
 *
 * @warning There is not always an implementation at hand in your compiler's
 * standard library. If this is the case a custom implementation needs to be
 * created and put into the cryptolib_libc module.
 */
/** @addtogroup mem-qual */
/*@{*/
#define CRYPTOLIB_HUGE      /**< Qualifier for (s)huge pointers */
/*@}*/

/** \defgroup pragma-def User defined pragma statements
 * User defined pragma statements to move globals into specific memory section
 *
 * On some platforms global variables and function static variables need to be
 * placed in a specific memory section. This may be necessary on a multi-core
 * platform where a MPU is used to manage concurrent write access to those
 * variables.
 *
 * Example:\n
 * \code{.c}
 * #define CRYPTOLIB_MEMORY_SECTION_BEGIN _Pragma ("section \".TIM_Task_data\"")
 * #define CRYPTOLIB_MEMORY_SECTION_END _Pragma ("section")
 * \endcode
 *
 */
/** @addtogroup pragma-def */
/*@{*/
#define CRYPTOLIB_MEMORY_SECTION_BEGIN      /**< start of memory section */
#define CRYPTOLIB_MEMORY_SECTION_END        /**< end of memory section */
/*@}*/

/** \defgroup trade-opt Memory and performance trade-off optimization
 * Allows user directed optimization strategies that will affect the memory
 * footprint and/or performance.
 */
/** @addtogroup trade-opt */
/*@{*/
/**
 * Trade-off setting to shift roughly 8 kB of memory between RAM or ROM
 * storage by precalculating AES tables.
 *
 * DISABLED: Store more in RAM, use less ROM (default)<br>
 * ENABLED: Store more in ROM, use less RAM
 */
#define CRYPTOLIB_OPT_RAM_ROM_TRADEOFF DISABLED

/**
 * Trade-off setting to decide between gains in performance or reduction in
 * memory footprint.
 *
 * @warning Only deviate from the default setting if you absolutely have to!
 * Memory savings will be minimal (about 1 kB RAM/ROM) compared to the significant
 * performance penalties to elliptic curve operations (up to 60% less ECDH
 * performance, your results may vary).
 *
 * 0: Uses most RAM and ROM for best performance (default)<br>
 * 1: Compromise between memory demands and performance<br>
 * 2: Uses the least amount of RAM and ROM at a significant performance hit
 */
#define CRYPTOLIB_OPT_MEM_PERF_TRADEOFF 0
/*@}*/

/** \defgroup slice-def Disable time slicing mechanism
 * If time slicing is not necessary, CRYPTOLIB_NO_TIME_SLICING can be set to ENABLED.
 * Thus, the time slicing mechanism is deactivated what results in memory savings
 * of up to 30k ROM and 3k RAM.
 * \warning
 * The option of not using time slicing (SLICE_NO_SLICING) is not available when
 * the time slicing mechanism is enabled.
 *
 * DISABLED: Time slicing mechanism is applied (default)<br>
 * ENABLED: Time slicing mechanism is not applied<br>
 */
/** @addtogroup slice-def */
/*@{*/
#define CRYPTOLIB_NO_TIME_SLICING DISABLED /**< Removes the time slicing mechanism \
                                            *    during compile time if set to ENABLED*/
/*@}*/

/**
 * Enable the use of built-in system entropy sources such as /dev/urandom or
 * Windows CryptoAPI for random number generation.
 *
 * This option can be enabled if platform entropy sources are available and the
 * manual provision of a seed to CryptoLib_Random is not desired.
 *
 * DISABLED: Entropy provision by manual seeding (default)<br>
 * ENABLED: Entropy provision by usage of system sources<br>
 */
#define CRYPTOLIB_USE_SYSTEM_ENTROPY DISABLED /**< Enable the use of built-in system \
                                               *    entropy sources */

/**
 * \defgroup alt-def Alternative API configuration
 *
 * The following parameters allow the user to selectively activate an alternative API in order to support alternative
 * crypto providers such as HSMs or 3rd party crypto libraries in addition or instead of CryptoLib.
 *
 * This is useful when the AEF library is to be integrated into an environment where reusable crypto functionality is
 * already present (e.g. to reduce memory-footprint) or sensitive cryptographic key material should be stored in a
 * secure domain such as an HSM and certain operations need to be handled outside of the CryptoLib.
 *
 * By setting the feature switches such as CRYPTOLIB_ALT_KDF, CRYPTOLIB_ALT_ECDH_X etc. to ENABLED, the respective CryptoLib
 * implementation is disabled and library internal calls will be made to the alternative API counterparts. These functions
 * will have the same name as the disabled counterparts but a different and customizable function signatures, and are
 * expected to be implemented by the user as a user-defined callout.
 *
 * User code example, to be implemented outside of CryptoLib:
 * @code
 * uint8_t CryptoLib_Ecdh(CRYPTOLIB_ALT_KEY_TYPE privateKey,
 *                        CRYPTOLIB_HUGE const char* peerPublicKey,
 *                        const CryptoLib_Curve_t curve,
 *                        const CryptoLib_Slicing_t slicing,
 *                        CRYPTOLIB_HUGE uint8_t sharedSecret[ECDH_COMMON_SECRET_LEN])
 * {
 *      (void)slicing;                                          // useful for asynch calls, but not used in this example
 *      myCurveType myCurveParam = translateIntoMyCurveType(curve);
 *      myRetvalType retval = myCrypto(privateKey, peerPublicKey, myCurveParam, sharedSecret);
 *
 *      return translateIntoCryptoLibRetvalType(retval);
 * }
 * @endcode
 *
 */
/** @addtogroup alt-def */
/*@{ */
/**
 * Defines how a cryptographic key is passed to the alternative API function.
 *
 * This definition is used as a data type placeholder whenever a confidential key (i.e. LwA or private key) is passed
 * to an alternative API function. This is to be edited by the library user and can be anything ranging from integer
 * based HSM or CSM key ids, complex structs  with additional meta data or simple byte array pointers.
 */
#define CRYPTOLIB_ALT_KEY_TYPE uint32_t     /**< Key data type for alternative API */

/**
 * Enables user-defined init and deinit callouts.
 *
 * Useful if you need to perform some preparation or cleanup tasks before and after CryptoLib performs other callouts.
 *
 * Expects the user to provide implementations for some functions found in cryptolib.h:
 * - CryptoLib_Alt_Init()
 * - CryptoLib_Alt_Deinit()
 *
 * DISABLED: Do not invoke callouts (default)<br>
 * ENABLED:  Invoke user-defined callouts<br>
 */
#define CRYPTOLIB_ALT_INIT DISABLED        /**< Whether a user-defined init function shall be called when the CryptoLib
                                            *    is initialized */

/**
 * Enables user-defined key derivation function callouts.
 *
 * Useful if you already have a HMAC SHA256 based KDF according to NIST SP 800 56Ar2 available and want to disable the
 * CryptoLib implementation.
 *
 * Expects the user to provide implementations for some functions found in cryptolib_hash.h:
 * - CryptoLib_KeyDerivationFunction()
 *
 * DISABLED: Do not invoke callouts (default)<br>
 * ENABLED:  Invoke user-defined callouts<br>
 */
#define CRYPTOLIB_ALT_KDF DISABLED        /**< Whether a user-defined KDF function shall be called */

/**
 * Enables user-defined ECDH function callouts.
 *
 * Useful if you already have a ECDH over Curve25519 implementation available and want to disable the CryptoLib
 * implementation.
 *
 * Expects the user to provide an implementation for a function found in cryptolib_ecdh.h:
 * - CryptoLib_Ecdh()
 *
 * DISABLED: Do not invoke callouts (default)<br>
 * ENABLED:  Invoke user-defined callouts<br>
 *
 * @details
 * Two different (but mutually exclusive) kinds of alternative implementation types are supported:
 * - ECDH function with result as CRYPTOLIB_ALT_KEY_TYPE (e.g. Autosar CSM)
 * - ECDH function with result as as plain byte array shall be called (e.g. SW library)
 *
 * @note
 * Depending on the chosen constellation of ECDH and KDF implementations, CRYPTOLIB_ALT_KEYS might need to be enabled.
 * For example, using an ECDH implementation that stores the result as CRYPTOLIB_ALT_KEY_TYPE requires CryptoLib_LoadKey()
 * before it can be used by the default CryptoLib KDF.
 * Invalid configurations will be detected by the compiler.
 */
#define CRYPTOLIB_ALT_ECDH_INT DISABLED        /**< Whether a user-defined ECDH function with result as CRYPTOLIB_ALT_KEY_TYPE
                                                *    shall be called */
#define CRYPTOLIB_ALT_ECDH_EXT DISABLED        /**< Whether a user-defined ECDH function with result as plain byte array
                                                *    shall be called */

/**
 * Enables user-defined MAC function callouts.
 *
 * Useful if you already have an AES-128 CMAC implementation available and want to disable the CryptoLib implementation.
 *
 * Expects the user to provide implementations for some functions found in cryptolib_mac.h:
 * - CryptoLib_GenerateCmac()
 * - CryptoLib_VerifyCMac()
 *
 * DISABLED: Do not invoke callouts (default)<br>
 * ENABLED:  Invoke user-defined callouts<br>
 */
#define CRYPTOLIB_ALT_CMAC DISABLED        /**< Whether user-defined CMAC functions shall be called */

/**
 * Enables user-defined certificate verification function callouts.
 *
 * Useful if you already have a certificate parser and verifier implementation available and want to disable the
 * CryptoLib implementation.
 *
 * Expects the user to provide implementations for some functions found in cryptolib_mac.h:
 * - CryptoLib_VerifyCertificate()
 *
 * DISABLED: Do not invoke callouts (default)<br>
 * ENABLED:  Invoke user-defined callouts<br>
 */
#define CRYPTOLIB_ALT_CERT_VERIFY DISABLED        /**< Whether user-defined certificate functions shall be called */

/**
 * Enables user-defined random number generator function callouts.
 *
 * Useful if you already have a CSPRNG and want to disable the CryptoLib implementation.
 *
 * Expects the user to provide implementations for some functions found in cryptolib_rng.h:
 * - CryptoLib_Random()
 *
 * DISABLED: Do not invoke callouts (default)<br>
 * ENABLED:  Invoke user-defined callouts<br>
 */
#define CRYPTOLIB_ALT_RNG DISABLED        /**< Whether user-defined RNG implementation shall be called */

/**
 * Enables user-defined callouts for key management functions.
 *
 * Necessary depending on chosen ECDH and KDF alt. API settings.
 *
 * Expects the user to provide implementations for functions found in cryptoLib_keys.h:
 * - CryptoLib_StoreKey()
 * - CryptoLib_LoadKey()
 *
 * DISABLED: Do not invoke callouts (default)<br>
 * ENABLED:  Invoke user-defined callouts<br>
 */
#define CRYPTOLIB_ALT_KEYS DISABLED        /**< Whether user-defined store key function shall be called */
/* @} */

/**
 * Enable debugging of buffer allocator memory issues. Automatically prints
 * (to stderr) all (fatal) messages on memory allocation issues. Enables
 * function for 'debug output' of allocated memory.
 *
 * DISABLED: Do not print debug information (default)<br>
 * ENABLED:  Print debug information<br>
 */
#define CRYPTOLIB_MEMORY_DEBUG DISABLED                 /**< Memory allocator debug information shall be printed */


/*
 * Configuration sanity checks and utility macros
 *
 * DO NOT EDIT!
 */
#if ((CRYPTOLIB_ALT_ECDH_INT == ENABLED) || (CRYPTOLIB_ALT_ECDH_EXT == ENABLED))
#define CRYPTOLIB_ALT_ECDH ENABLED
#else
#define CRYPTOLIB_ALT_ECDH DISABLED
#endif

#if ((CRYPTOLIB_ALT_ECDH_INT == ENABLED) && (CRYPTOLIB_ALT_ECDH_EXT == ENABLED))
#error "CRYPTOLIB_ALT_ECDH_INT and CRYPTOLIB_ALT_ECDH_EXT are mutually exclusive!"
#endif

#if ((CRYPTOLIB_ALT_KDF == ENABLED) && (CRYPTOLIB_ALT_ECDH_INT == DISABLED))
#error "CRYPTOLIB_ALT_KDF requires CRYPTOLIB_ALT_ECDH_INT to be enabled!"
#endif

#if ((CRYPTOLIB_ALT_KDF == DISABLED) && (CRYPTOLIB_ALT_ECDH == ENABLED) && (CRYPTOLIB_ALT_KEYS == DISABLED))
#error "CRYPTOLIB_ALT_KEYS must be enabled to convert ECDH output for default KDF implementation!"
#endif

#if ((CRYPTOLIB_ALT_CMAC == ENABLED) && (CRYPTOLIB_ALT_ECDH == DISABLED) && (CRYPTOLIB_ALT_KEYS == DISABLED))
#error "Either CRYPTOLIB_ALT_ECDH or CRYPTOLIB_ALT_KEYS is required to use CRYPTOLIB_ALT_CMAC"
#endif

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_CONFIG_H */

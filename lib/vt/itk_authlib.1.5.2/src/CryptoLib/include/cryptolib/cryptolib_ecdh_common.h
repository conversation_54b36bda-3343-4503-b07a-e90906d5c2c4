/**
 * @file  cryptolib_ecdh_common.h
 * @brief Interface file for commonly shared ecdh helper functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_ECDH_COMMON_H
#define CRYPTOLIB_ECDH_COMMON_H

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"
#include "mbedtls/ecp.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Reduce a mbedtls_mpi mod grp->p in-place, to use after mbedtls_mpi_add_mpi and mbedtls_mpi_mul_int.
 * We known P, N and the result are positive, so sub_abs is correct, and
 * a bit faster.
 *
 * @param[in,out] N                 mpi to operate on
 * @param[in] grp                   curve grp
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: calculation successful<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
uint8_t mod_add(mbedtls_mpi* N, const mbedtls_ecp_group* grp);

/**
 * Wrapper around fast quasi-modp functions, with fall-back to mbedtls_mpi_mod_mpi.
 * See the documentation of struct mbedtls_ecp_group.
 *
 * This function is in the critial loop for mbedtls_ecp_mul, so pay attention to perf.
 *
 * @param[in,out] N                 mpi to operate on
 * @param[in] grp                   curve grp
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: calculation successful<br>
 *                                  1 - CRYPTO_ERR_INVALID: invalid input paramter<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
uint8_t mod_mul(mbedtls_mpi* N, const mbedtls_ecp_group* grp);

/**
 * Reduce a mbedtls_mpi mod p in-place, to use after mbedtls_mpi_sub_mpi
 * N->s < 0 is a very fast test, which fails only if N is 0
 *
 * @param[in,out] N                 mpi to operate on
 * @param[in] grp                   curve grp
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: calculation successful<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
uint8_t mod_sub(mbedtls_mpi* N, const mbedtls_ecp_group* grp);

/**
 * A callback function used by the mbed TLS to be able to use CryptoLib_Random
 * to generate random number e.g. during ECDH.
 *
 * @param[out] p_rng        pointer to the DRBG context, is ignored as CryptoLib_Random holds it own value
 * @param[out] output       pointer to the generated pseudo-random number
 * @param[in] len           length of the pseudo-random number to be generated
 *
 * @return                  0  - CRYPTO_ERR_SUCCESS: random number successfully generated<br>
 *                          15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls error occured<br>
 */
int CryptoLib_RandomCallback(void* p_rng, unsigned char* output, size_t len);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_ECDH_COMMON_H */

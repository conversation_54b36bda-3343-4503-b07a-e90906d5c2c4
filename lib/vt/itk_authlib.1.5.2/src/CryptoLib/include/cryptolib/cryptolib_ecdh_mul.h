/**
 * @file  cryptolib_ecdh_mul.h
 * @brief Interface file for ECDH Montgomery multiply function definition
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_ECDH_MUL_H
#define CRYPTOLIB_ECDH_MUL_H

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"
#include "mbedtls/ecp.h"

#ifdef __cplusplus
extern "C" {
#endif

/** Struct to hold input/output and local variables of gcd subfunctions*/
typedef struct {
    uint8_t initFlag; /**< MPI/ECP malloc flag */
    size_t i; /**< Iteration counter */
    uint8_t b; /**< bit of factor */
    mbedtls_ecp_point RP; /**< working copy of point */
    mbedtls_mpi PX; /**< working copy of X coordinate of point */

    /** Enum tag for multiply slicing process */
    enum IterationSubstate_tag {
        PART_1 = 0, /**< first part of ecp_mul */
        PART_2 = 1, /**< second part of ecp_mul */
        PART_3 = 2 /**< third part of ecp_mul */
    } IterationSubstate; /**< Enum for multiply slicing process */

    /** Enum tag for double add slicing process */
    enum DoubleAddState_tag {
        DA_IDLE = 0, /**< GCD function not active */
        DA_ACTIVE = 1 /**< GCD function in slice mode */
    } DoubleAddState; /**< Enum for double add slicing process */

    /** Enum tag for normalize slicing process */
    enum NormalizeState_tag {
        NM_IDLE = 0, /**< GCD function not active */
        NM_INIT = 1, /**< GCD function before init */
        NM_ACTIVE = 2 /**< GCD function in slice mode */
    } NormalizeState; /**< Enum for normalize slicing process */

    enum RandomizeState_tag {
        RND_IDLE = 0, /**< Randomize function not active */
        RND_ACTIVE = 1 /**< Randomize function in slice mode */
    } RandomizeState;
} MulSlicingContext_t;

/**
 * Performs a Montgomery Multiplication: result = factor * point
 * Time sliced replacement for ecp_mul_mxz
 *
 * @param[in] grp                   pointer to curve parameter
 * @param[out] result               pointer to resulting output
 * @param[in] factor                pointer to factor to multiply with
 * @param[in] point                 pointer to ec point to be multiplied
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: divisor successfully calculated<br>
 *                                  1 - CRYPTO_ERR_INVALID: invalid slicing parameter for current state<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid parameter or bad input data<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedEcpMul(const mbedtls_ecp_group* grp,
                               mbedtls_ecp_point* result,
                               const mbedtls_mpi* factor,
                               const mbedtls_ecp_point* point,
                               const CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_ECDH_MUL_H */

/**
 * @file  cryptolib_keys.h
 * @brief Interface file for key related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_KEYS_H
#define CRYPTOLIB_KEYS_H

#include "cryptolib_config.h"
#include "cryptolib_types.h"

#ifdef __cplusplus
extern "C"
{
#endif

#if (CRYPTOLIB_ALT_KEYS == ENABLED)

/**
 * \note Only available if \ref CRYPTOLIB_ALT_KEYS is enabled.
 *
 * This function stores plain key bytes as the given CRYPTOLIB_ALT_KEY_TYPE key in the
 * alternative crypto provider's key storage.
 *
 * @param[in] keyBytes           key bytes to be stored
 * @param[in] keyLength          size of key bytes to be stored
 * @param[in] key                key used to persist the given key bytes
 * @param[in] slicing            0 - SLICE_INIT: first step of the storing process<br>
 *                               1 - SLICE_CONTINUE: during storing process<br>
 *                               254 - SLICE_RESET: reset slicing state machines<br>
 *
 * @return                           0 - CRYPTO_ERR_SUCCESS: storing successfully completed<br>
 *                                   4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                   7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedTLS returned error<br>
 *                                  18 - CRYPTO_ERR_ALT_FAILED: Alt. API returned an error <br>
 */
uint8_t CryptoLib_StoreKey(CRYPTOLIB_HUGE const uint8_t* keyBytes,
                           size_t keyLength,
                           CRYPTOLIB_ALT_KEY_TYPE key,
                           CryptoLib_Slicing_t slicing);

/**
 * \note Only available if \ref CRYPTOLIB_ALT_KEYS is enabled.
 *
 * This function loads a CRYPTOLIB_ALT_KEY_TYPE key from the alternative crypto
 * provider's key storage and
 *
 * @param[in] keyBytes       key bytes to be loaded
 * @param[in,out] keyLength  in: size of keyBytes buffer / out: size of loaded key
 * @param[in] key            key used to load the given key bytes
 * @param[in] slicing               0 - SLICE_INIT: first step of the storing process<br>
 *                                  1 - SLICE_CONTINUE: during storing process<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *
 * @return                           0 - CRYPTO_ERR_SUCCESS: storing successfully completed<br>
 *                                   4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                   7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedTLS returned error<br>
 *                                  18 - CRYPTO_ERR_ALT_FAILED: Alt. API returned an error <br>
 */
uint8_t CryptoLib_LoadKey(CRYPTOLIB_HUGE uint8_t* keyBytes,
                          size_t* keyLength,
                          CRYPTOLIB_ALT_KEY_TYPE key,
                          CryptoLib_Slicing_t slicing);

#endif /* (CRYPTOLIB_ALT_KEYS == ENABLED) */

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_KEYS_H */

/**
 * @file  cryptolib_rsa_montmul.h
 * @brief Interface file for Montgomery multiplication: A = A * B * R^-1 mod N
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 R<PERSON>lzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_RSA_MONTMUL_H
#define CRYPTOLIB_RSA_MONTMUL_H

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"

#ifdef __cplusplus
extern "C" {
#endif

/** Struct to hold local variables of Montgomery multiplication */
typedef struct {
    mbedtls_mpi A;          /**< Working copy of A */
    mbedtls_mpi B;          /**< Working copy of B */
    mbedtls_mpi N;          /**< Working copy of N */
    mbedtls_mpi T;          /**< Working copy of T */
    mbedtls_mpi_uint u0;    /**< Local */
    mbedtls_mpi_uint u1;    /**< Local */
    mbedtls_mpi_uint* d;    /**< Local */
    size_t n;               /**< Local */
    size_t m;               /**< Local */
    uint16_t ctr;             /**< local iteration counter */
} MontMultSlicingContext_t;

/**
 * This function performs the following Montgomery multiplication:
 * A = A * B * R^-1 mod N
 *
 * @param[in,out] A                 pointer to A
 * @param[in] B                     pointer to B
 * @param[in] N                     pointer to N
 * @param[in] mm                    pointer to mm
 * @param[in] T                     pointer to T
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicingsicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: multiplication successful<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: multiplication not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter for current state<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMontMul(mbedtls_mpi* A,
                                const mbedtls_mpi* B,
                                const mbedtls_mpi* N,
                                mbedtls_mpi_uint mm,
                                const mbedtls_mpi* T,
                                const CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_RSA_MONTMUL_H */

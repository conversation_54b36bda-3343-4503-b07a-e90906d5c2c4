/**
 * @file  cryptolib_x509.h
 * @brief Public interfaces to X509 parser
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */
#ifndef CRYPTOLIB_CRT_PARSER_H
#define CRYPTOLIB_CRT_PARSER_H

#include "cryptolib_types.h"

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Parses a TIM-specific X509 certificate and fills the given CryptoLib_Crt_t
 * struct with the data that has been read out.
 *
 * @param[in]  input        The byte array of the DER encoded raw certificate.
 * @param[in]  inputLen     The length of the input byte array.
 * @param[out] certificate  A pointer to a CryptoLib_Crt_t, that will hold data.
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: verification successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
uint8_t CryptoLib_X509_Parse(CRYPTOLIB_HUGE const uint8_t* const input,
                             const uint16_t inputLen,
                             CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_CRT_PARSER_H */

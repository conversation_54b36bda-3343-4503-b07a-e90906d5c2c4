/**
 * @file  cryptolib_rsa_div.h
 * @brief Function prototypes for sliced implementation of multi-precision division
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_RSA_DIV_H
#define CRYPTOLIB_RSA_DIV_H

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"

#ifdef __cplusplus
extern "C" {
#endif

/** Enum for statemachine states */
typedef enum {
    MPIDIV_IDLE = 0, /**< Ready for new calls */
    MPIDIV_STEP = 1, /**< Perform loop iterations */
    MPIDIV_FINAL = 2 /**< Ready to write output */
} MpiDivSlicingState_t;

/** Struct to hold input/output and local variables of mpi_div_mpi subfunctions*/
typedef struct {
    /* input output */
    mbedtls_mpi X; /**< Working copy of divisor */
    mbedtls_mpi Y; /**< Working copy of integer a */
    mbedtls_mpi Z; /**< Working copy of integer b */
    mbedtls_mpi T1; /**< Temp */
    mbedtls_mpi T2; /**< Temp */

    /* internals */
    struct {
        /**@ \{*/
        size_t i;
        size_t n;
        size_t t;
        size_t k;
        /**@ \}*/
    } internals; /**< Local variables */
} MpiDivSlicingContext_t;

/**
 * Implements the division of to mbedtls bignums. A = Q*B + R using the
 * Multiple-precision division described in HAC 14.20.
 * Either Q or R can be NULL. In this case only the quotient or the remainder is
 * computed.
 *
 * @param[out] Q        Pointer to the destination bignum for the quotient
 * @param[out] R        Pointer to the destination bginum for the remainder
 * @param[in]  A        Pointer to the input bignum holding the nominator
 * @param[in]  B        Pointer to the input bignum holding the denominator
 * @param[in] slicing   0 - SLICE_INIT: first step of the computation<br>
 *                      1 - SLICE_CONTINUE: during computation<br>
 *                      254 - SLICE_RESET: reset slicing state machines<br>
 *                      255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return              0 - CRYPTO_ERR_SUCCESS: division successful<br>
 *                      2 - CRYPTO_ERR_CALLAGAIN: more slices needed, call again<br>
 *                      7 - CRYPTO_ERR_NULL_POINTER: null pointer given<br>
 *                      12 - CRYPTO_ERR_BAD_INPUT: bad input parameter, e.g. B == 0<br>
 *                      14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                      16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMpiDivMpi(mbedtls_mpi* Q, mbedtls_mpi* R,
                                  const mbedtls_mpi* A, const mbedtls_mpi* B,
                                  const CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif


#endif /* CRYPTOLIB_RSA_DIV_H */

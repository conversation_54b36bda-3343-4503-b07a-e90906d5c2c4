/**
 * @file  cryptolib_asn1.h
 * @brief Interface file for ASN.1 parsing related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im S<PERSON>yerer <PERSON>l 6
 * @n 76761 <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_ASN1_H
#define CRYPTOLIB_ASN1_H

#include "cryptolib_types.h"

#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

#define ASN1_SET 0xA0                     /**< Value for the tag of the first element of an ASN.1 set (array) */
#define ASN1_INDEFINITE_LEN 0x80          /**< Value for a tag with indefinite length */
#define ASN1_DEFINITE_LEN_MASK 0x7F       /**< Mask for low 7 bits in length byte of ASN.1 */
#define ASN1_MAX_LEN_TAG 0x06             /**< maximum length of an ASN.1 tag including length of content */
#define ASN1_MAX_LEN_SERIAL 0x17          /**< maximum length of serial number in X509.v3 including ASN.1 tag and length of content */

/**
 * Get the tag and length of the tag. Check for the requested tag.
 * Updates the pointer to immediately behind the tag and length.
 *
 * @param[out] p     The position in the ASN.1 data
 * @param[in]  end   End of data
 * @param[out] len   The variable that will receive the length
 * @param[in]  tag   The expected tag
 *
 * @return      0 if successful, MBEDTLS_ERR_ASN1_UNEXPECTED_TAG if tag did
 *              not match requested tag, or another specific ASN.1 error code.
 */
int16_t CryptoLib_Asn1GetTag(const uint8_t** p,
                             const uint8_t* end,
                             uint32_t* len, uint8_t tag);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_ASN1_H */

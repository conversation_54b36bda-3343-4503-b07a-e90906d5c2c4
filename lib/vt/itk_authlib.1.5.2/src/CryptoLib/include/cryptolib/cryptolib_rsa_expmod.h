/**
 * @file  cryptolib_rsa_expmod.h
 * @brief Interface file for CryptoLib_SlicedExpMod
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_RSA_EXPMOD_H
#define CRYPTOLIB_RSA_EXPMOD_H

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Sliced sliding-window exponentiation: X = A^E mod N
 *
 * @param[out] X                    Destination MPI
 * @param[in] A                     Left-hand MPI
 * @param[in] E                     Exponent MPI
 * @param[in] N                     Modular MPI
 * @param[in,out] RR               Speed-up MPI used for recalculations
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: result successfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter for current state<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 *
 * @note           RR is used to avoid re-computing R*R mod N across
 *                 multiple calls, which speeds up things a bit. It can
 *                 be set to NULL if the extra performance is unneeded.
 */
uint8_t CryptoLib_SlicedMpiExpMod(mbedtls_mpi* X,
                                  const mbedtls_mpi* A,
                                  const mbedtls_mpi* E,
                                  const mbedtls_mpi* N,
                                  mbedtls_mpi* RR,
                                  const CryptoLib_Slicing_t slicing);


#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_RSA_EXPMOD_H */

/**
 * @file  cryptolib_crt.h
 * @brief Interface file for certificate related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_CRT_H
#define CRYPTOLIB_CRT_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib_types.h"

#define X509_ATTR_OID_LEN 3 /**< Length of OID in byte format for standard naming attributes */

#ifdef __cplusplus
extern "C" {
#endif

#if (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED)

/**
 * \note Only available if \ref CRYPTOLIB_ALT_CERT_VERIFY is enabled.
 *
 * This function checks the validity of a certificate's signature with the help of
 * the public key of its parent certificate.
 *
 * @note
 * On some alternative crypto providers, the provision of a parent certificate for a hierarchical
 * signature check is not needed and certificate verification works by automatically verifiying
 * certificate signatures against already loaded certificates.
 * In those cases, the provision of the parentCertificate parameter can be omitted but it is often
 * expected to perform verification or injection steps in a particular order. I.e., from root
 * to testlab certificate to end-entity certificate.
 *
 * @note
 * If the function is called with SLICE_RESET as slicing parameter, all pending operations
 * shall be canceled.
 *
 * @param[in] certificate           pointer to the certificate to be verified (byte array in DER format)
 * @param[in] certSize              size of the certificate to be verified in bytes
 * @param[in] parentCertificate     pointer to the certificate of the next higher PKI-level
 * @param[in] parentCertSize        size of the certificate tof the next higher PKI-level in bytes
 * @param[in] slicing               0 - SLICE_INIT: first step of the validation<br>
 *                                  1 - SLICE_CONTINUE: during validation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *
 * @return                          0 -  CRYPTO_ERR_SUCCESS: validation successfully completed<br>
 *                                  1 -  CRYPTO_ERR_INVALID: certificate invalid<br>
 *                                  2 -  CRYPTO_ERR_CALLAGAIN: check not complete<br>
 *                                  4 -  CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  6 -  CRYPTO_ERR_SIGNATURE_INVALID: signature invalid<br>
 *                                  7 -  CRYPTO_ERR_NULL_POINTER: NULL pointer<br>
 *                                  19 - CRYPTO_ERR_ALT_FAILED: Alt. API specific error<br>
 */
uint8_t CryptoLib_VerifyCertificate(CRYPTOLIB_HUGE const uint8_t* certificate,
                                    const uint16_t certSize,
                                    CRYPTOLIB_HUGE const uint8_t* parentCertificate,
                                    const uint16_t parentCertSize,
                                    const CryptoLib_Slicing_t slicing);

#else

/**
 * This function checks the validity of a certificate's signature with the help of
 * the public key of its parent certificate. If slicing is used and the validation has not
 * finished, the function returns CRYPTO_ERR_CALLAGAIN.
 * If the user wants to abort during time slicing, the function shall be called
 * with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 * and all sub-state machines.
 *
 * @param[in] certificate           pointer to the struct where the certificate
 *                                  is stored.
 * @param[in] parentCertificate     pointer to the struct where the certificate
 *                                  of the next higher PKI-level is stored
 * @param[in] slicing               0 - SLICE_INIT: first step of the validation<br>
 *                                  1 - SLICE_CONTINUE: during validation<br>
 *                                  2 - SLICE_FINISH: last step of the validation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: verification successfully completed<br>
 *                                  1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: sliced RSA verification not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  6 - CRYPTO_ERR_SIGNATURE_INVALID: verification failed because signatures differ<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
uint8_t CryptoLib_VerifyCertificate(CRYPTOLIB_HUGE const CryptoLib_Crt_t* certificate,
                                    CRYPTOLIB_HUGE const CryptoLib_Crt_t* parentCertificate,
                                    const CryptoLib_Slicing_t slicing);

#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == ENABLED) */

/**
 * This function initializes a given CryptoLib_Crt_t instance. All non-pointer members
 * are set to a default value that can be distinguished from any valid value, all
 * pointers are set to NULL. Note: this can lead to memory leaks if one manually
 * allocates memory for any of the members.
 *
 * @param[out] certificate          Pointer to the certificate struct
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: initialization successful<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER null pointer given<br>
 */
uint8_t CryptoLib_InitCertificate(CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate);

/**
 * This function parses a certificate out of a buffer array and save its information in a struct.
 *
 * @param[in] input                 pointer to the buffer where the raw certificate date are stored
 * @param[in] inputLen              length of raw certificate buffer
 * @param[out] certificate          pointer to the certificate where the parsed data gets stored.
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: parsing successfully completed<br>
 *                                  1 - CRYPTO_ERR_INVALID: parsing failed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
uint8_t CryptoLib_ParseCertificate(CRYPTOLIB_HUGE const uint8_t* const input,
                                   const uint16_t inputLen,
                                   CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate);

/**
 * This function frees any allocated ressources in the given CryptoLib_Crt_t struct and
 * resets any member that needs no allocation to a default value.
 *
 * @param[out] certificate
 * @return                          0 - CRYPTO_ERR_SUCCESS: successfully freed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 */
uint8_t CryptoLib_FreeCertificate(CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_CRT_H */

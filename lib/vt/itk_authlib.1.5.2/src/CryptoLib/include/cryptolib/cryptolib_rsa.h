/**
 * @file  cryptolib_rsa.h
 * @brief Interface file for rsa related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON>lzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_RSA_H
#define CRYPTOLIB_RSA_H

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib_types.h"

/**
 * This function verifies the signature of a certificate using the RSASSA-PSS algorithm.
 * If the user wants to abort during time slicing, the function shall be called
 * with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 * and all sub-state machines.
 *
 * @param[in] hash                  hash digest of the certificate
 * @param[in] hashLen               length of the digest
 * @param[in] signature             signature to be verified in binary form
 * @param[in] signatureLen          length of binary signature
 * @param[in] publicModulus         public RSA modulus as terminated Hex string
 * @param[in] publicExponent        public RSA exponent as terminated Hex string
 * @param[in] slicing               0 - SLICE_INIT: first step of the validation<br>
 *                                  1 - SLICE_CONTINUE: during validation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: verification successfully completed<br>
 *                                  1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: sliced RSA verification not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  6 - CRYPTO_ERR_SIGNATURE_INVALID: verification failed because sigs differ<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: wrong slicing parameter given<br>
 */
uint8_t CryptoLib_VerifyRsa(CRYPTOLIB_HUGE const uint8_t* hash,
                            const uint8_t hashLen,
                            CRYPTOLIB_HUGE const uint8_t* signature,
                            const uint16_t signatureLen,
                            CRYPTOLIB_HUGE const char* publicModulus,
                            CRYPTOLIB_HUGE const char* publicExponent,
                            const CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_RSA_H */

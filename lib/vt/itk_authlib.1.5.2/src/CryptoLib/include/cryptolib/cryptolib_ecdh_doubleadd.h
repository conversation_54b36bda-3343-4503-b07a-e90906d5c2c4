/**
 * @file  cryptolib_ecdh_doubleadd.h
 * @brief Interface file for Montgomery Double-and-Add
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_ECDH_DOUBLEADD_H
#define CRYPTOLIB_ECDH_DOUBLEADD_H

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"
#include "mbedtls/ecp.h"

#ifdef __cplusplus
extern "C" {
#endif

/** Struct to hold local variables of Double-and-Add subfunctions*/
typedef struct {
    /**@ \{*/
    mbedtls_mpi A;
    mbedtls_mpi AA;
    mbedtls_mpi B;
    mbedtls_mpi BB;
    mbedtls_mpi E;
    mbedtls_mpi C;
    mbedtls_mpi D;
    mbedtls_mpi DA;
    mbedtls_mpi CB;
    /**@ \}*/
} DoubleAddSlicingContext_t;

/**
 * Double-and-add for Montgomery curves in x/z coordinates.
 *
 * @param[in] grp                   pointer to the ec group parameter
 * @param[out] pointR               pointer to the ecp R = 2P
 * @param[out] pointS               pointer to the ecp S = P + Q
 * @param[in] pointP                pointer to the ecp P
 * @param[in] pointQ                pointer to the ecp Q
 * @param[in] d                     pointer to d = X(P - Q)
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: divisor successfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter for current state<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMontDA(const mbedtls_ecp_group* grp,
                               mbedtls_ecp_point* pointR,
                               mbedtls_ecp_point* pointS,
                               const mbedtls_ecp_point* pointP,
                               const mbedtls_ecp_point* pointQ,
                               const mbedtls_mpi* d,
                               const CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_ECDH_DOUBLEADD_H */

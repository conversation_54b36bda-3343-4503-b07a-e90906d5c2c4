/**
 * @file  cryptolib_crl_revocationcheck.h
 * @brief Global typedefs for CRL revocation check.
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_CRL_REVOCATIONCHECK_H
#define CRYPTOLIB_CRL_REVOCATIONCHECK_H

#include "cryptolib_asn1.h"

#define INT_BUF_LEN ASN1_MAX_LEN_SERIAL     /**< length of the internal buffer to store data over several calls */

#ifdef __cplusplus
extern "C" {
#endif

/** Enum to define possible states while (sliced) checking revocation on CRL */
typedef enum {
    CERT = 0,                             /**< cert tag not yet parsed */
    TBS,                                  /**< TBS tag not yet parsed */
    VERSION,                              /**< version tag not yet parsed */
    SIG,                                  /**< signature algorithm not yet parsed */
    ISSUER,                               /**< issuer tag not yet parsed */
    THIS_UPDATE,                           /**< thisUpdate tag not yet parsed */
    NEXT_UPDATE,                           /**< nextUpdate tag not yet parsed */
    REVOCATION_LIST,                       /**< start of revocation list not yet parsed */
    REVOKED_CERTS,                         /**< in the process of parsing the entries of the revocation list in sub-statemachine */
    REVOKED_CERT,                          /**< substate, start next revoked certificate not yet parsed */
    SERIAL_NUMBER,                         /**< substate, serial number not yet parsed */
    REVOCATION_DATE                        /**< substate, revocation date not yet parsed */
} CryptoLib_CrlRevokeState_t;

/** Enum to define what will be parsed next and the remaining bytes will be used for this */
typedef enum {
    NONE = 0,                             /**< no remaining bytes */
    TAG,                                  /**< remaining bytes contain incomplete data for a tag */
    SERIAL                                /**< remaining bytes contain incomplete data for a serial */
} CryptoLib_CrlNext_t;

/** Struct holding stored data for context switches when parsing a CRL in slices */
typedef struct {
    uint32_t bytesToBeSkipped;                /**< number of bytes to be skipped in the next run */
    uint8_t remainingBytes[INT_BUF_LEN];        /**< bytes that need to be prepended to data in the next run */
    size_t remainingBytesLen;                 /**< length of valid data in remainingBytes array */
    CryptoLib_CrlRevokeState_t state;         /**< state of internal main statemachine */
    CryptoLib_CrlRevokeState_t subState;      /**< state of internal sub statemachine */
    CryptoLib_CrlNext_t next;                 /**< enum indicating what the remaining bytes should be used for */
    uint32_t tbsEnd;                          /**< size of length of TBS */
    uint32_t revCertsEnd;                     /**< size of sequence holding the revoked certificates */
    uint32_t pos;                             /**< current position inside TBS */
} CryptoLib_SlicedCrlContext_t;

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_CRL_REVOCATIONCHECK_H */

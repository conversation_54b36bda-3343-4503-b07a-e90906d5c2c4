/**
 * @file  cryptolib_rsa_expmod_subs.h
 * @brief Interface file for CryptoLib_SlicedExpMod sub-functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_RSA_EXPMOD_SUBS_H
#define CRYPTOLIB_RSA_EXPMOD_SUBS_H

#include "cryptolib/cryptolib_rsa_expmod_utils.h"
#include "cryptolib_types.h"

#include "mbedtls/bignum.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Prepares a Sliding-window exponentiation for the given MPIs
 * Performs line 1609 to 1653 of mbedtls_mpi_exp_mod
 *
 * @param[out] context              Context to work on
 * @param[in] base                  base to be stored
 * @param[in] exponent              exponent to be stored
 * @param[in] modulus               modulus to be stored
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: sucessfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter for current state<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMpiExpMod_P1_Preparations(RsaExpModContext_t* context,
                                                  const mbedtls_mpi* base,
                                                  const mbedtls_mpi* exponent,
                                                  const mbedtls_mpi* modulus);

/**
 * If 1st call, pre-compute R^2 mod N
 * Performs line 1658 to 1668 of mbedtls_mpi_exp_mod with sliced CryptoLib-Functions
 *
 * @param[in,out] context           Context to work on
 * @param[in,out] RR                R^2 mod N to be used if != NULL or pointer to
 *                                  MPI where calculated RR should be additionally stored
 * @param[in] slicing               Slicing parameter for subfunctions
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: sucessfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter for current state<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMpiExpMod_P2_RR(RsaExpModContext_t* context,
                                        mbedtls_mpi* RR,
                                        const CryptoLib_Slicing_t slicing);

/**
 * Prepares the Window for the exponentiation
 * Performs line 1670 to 1683 of mbedtls_mpi_exp_mod with sliced CryptoLib-Functions
 *
 * @param[in,out] context           Context to work on
 * @param[in] slicing               Slicing parameter for subfunctions
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: sucessfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter for current state<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMpiExpMod_P3_PrepareWindow(RsaExpModContext_t* context,
                                                   const CryptoLib_Slicing_t slicing);

/**
 * Expands the window if necessary (context->internals.windowBitSize > 1)
 * Performs line 1686 to 1716 of mbedtls_mpi_exp_mod with sliced CryptoLib-Functions
 *
 * @param[in,out] context           Context to work on
 * @param[in] slicing               Slicing parameter for subfunctions
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: sucessfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter for current state<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMpiExpMod_P4_CalculateWindow(RsaExpModContext_t* context,
                                                     const CryptoLib_Slicing_t slicing);

/**
 * Performs loop iterations
 * Performs line 1717 to 1773 of mbedtls_mpi_exp_mod with sliced CryptoLib-Functions
 *
 * @param[in,out] context           Context to work on
 * @param[in] slicing               Slicing parameter for subfunctions
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: sucessfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter for current state<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMpiExpMod_P5_Iterate(RsaExpModContext_t* context,
                                             const CryptoLib_Slicing_t slicing);

/**
 * Processes all remaining data after all iterations
 * Performs line 1778 to 1785 of mbedtls_mpi_exp_mod with sliced CryptoLib-Functions
 *
 * @param[in,out] context           Context to work on
 * @param[in] slicing               Slicing parameter for subfunctions
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: sucessfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter for current state<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMpiExpMod_P6_ProcessRemains(RsaExpModContext_t* context,
                                                    const CryptoLib_Slicing_t slicing);

/**
 * Finalizes the calculations and writes the result into context
 * Performs line 1791 to 1797 of mbedtls_mpi_exp_mod with sliced CryptoLib-Functions
 *
 * @param[in,out] context           Context to work on
 * @param[in] slicing               Slicing parameter for subfunctions
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: sucessfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter for current state<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMpiExpMod_P7_Finalize(RsaExpModContext_t* context,
                                              const CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_RSA_EXPMOD_SUBS_H */

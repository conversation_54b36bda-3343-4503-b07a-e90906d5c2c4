/**
 * @file  cryptolib_rsa_mod.h
 * @brief Interface file for CryptoLib_SlicedMpiModMpi
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_RSA_MOD_H
#define CRYPTOLIB_RSA_MOD_H

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Calculates Modulo: R = A mod B
 * Time sliced replacement for mbedtls_mpi_mod_mpi
 *
 * @param[out] R                    Resulting mpi
 * @param[in] A                     MPI A
 * @param[in] B                     MPI B
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: calculation completed sucessfully<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid parameter or bad input data<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMpiModMpi(mbedtls_mpi* R,
                                  const mbedtls_mpi* A,
                                  const mbedtls_mpi* B,
                                  const CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_RSA_MOD_H */

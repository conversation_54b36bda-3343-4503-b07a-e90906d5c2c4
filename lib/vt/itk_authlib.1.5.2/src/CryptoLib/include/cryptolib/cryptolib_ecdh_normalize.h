/**
 * @file  cryptolib_ecdh_normalize.h
 * @brief Interface file for Montgomery Normalization function
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_ECDH_NORMALIZE_H
#define CRYPTOLIB_ECDH_NORMALIZE_H

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"
#include "mbedtls/ecp.h"

#ifdef __cplusplus
extern "C" {
#endif

/** Struct to hold local variables of Normalization subfunctions */
typedef struct {
    mbedtls_mpi pz;          /**< Working copy of pointP's Z coordinate */
    mbedtls_mpi px;          /**< Working copy of pointP's X coordinate */

    /** InvMod slicing sub-state tag */
    enum InvModState_tag {
        INV_MOD_IDLE = 0,    /**< InvMod function not active */
        INV_MOD_ACTIVE = 1   /**< InvMod function in slice mode */
    } InvModState; /**< InvMod slicing sub-state */
} NormalizeSlicingContext_t;

/**
 * This function normalizes Montgomery x/z coordinates: X = X/Z, Z = 1
 *
 * @param[in] grp                   pointer to the ec group parameter
 * @param[out] pointP               pointer to the normalized ecp P
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: normalization successful<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter for current state<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedMontNorm(const mbedtls_ecp_group* grp,
                                 mbedtls_ecp_point* pointP,
                                 const CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_ECDH_NORMALIZE_H */

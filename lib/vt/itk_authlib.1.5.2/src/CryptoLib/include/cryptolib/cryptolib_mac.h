/**
 * @file  cryptolib_mac.h
 * @brief Interface file for MAC related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON>ülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_MAC_H
#define CRYPTOLIB_MAC_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib_types.h"

#ifdef __cplusplus
extern "C" {
#endif

#if (CRYPTOLIB_ALT_CMAC == ENABLED)

/**
 * This function calculates a CMAC for a given challenge by using the given key.
 * CMAC is based on AES-128.
 *
 * @param[in] lwaKey                key for the CMAC calculation
 * @param[in] msg                   pointer to the challenge
 * @param[in] msgLen                length of the challenge
 * @param[out] mac                  pointer to a buffer to write the CMAC
 * @param[in] macLen                length of the mac buffer
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: calculation successfully completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED:  mbedTLS returned error<br>
 */
uint8_t CryptoLib_GenerateCmac(CRYPTOLIB_ALT_KEY_TYPE lwaKey,
                               const uint8_t* msg,
                               const size_t msgLen,
                               uint8_t* mac,
                               size_t macLen);

/**
 * This function verifies a received peer and an own mac by comparison.
 *
 * @param[in] lwaKey                key for the CMAC calculation
 * @param[in] msg                   pointer to the challenge
 * @param[in] msgLen                length of the challenge
 * @param[in] referenceMac          pointer to the peer mac
 * @param[in] referenceMacLen       length of peer mac
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: verification successfully completed<br>
 *                                  1 - CRYPTO_ERR_INVALID: verification completed, but the result is not valid (macs differ)<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 */
uint8_t CryptoLib_VerifyCMac(CRYPTOLIB_ALT_KEY_TYPE lwaKey,
                             const uint8_t* msg,
                             const size_t msgLen,
                             const uint8_t* referenceMac,
                             const size_t referenceMacLen);

#else

/**
 * This function calculates a CMAC for a given challenge by using the given key.
 * CMAC is based on AES-128.
 *
 * @param[in] input                 pointer to the challenge
 * @param[in] inputLen              length of the challenge
 * @param[in] key                   pointer to the key, fixed length of 128 bit
 * @param[out] buf                  pointer to a buffer to write the CMAC to, fixed length of 128 bit
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: calculation successfully completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED:  mbedTLS returned error<br>
 */
uint8_t CryptoLib_GenerateCmac(CRYPTOLIB_HUGE const uint8_t* input,
                               const uint16_t inputLen,
                               const uint8_t key[AES128_KEY_LEN],
                               CRYPTOLIB_HUGE uint8_t buf[AES128_KEY_LEN]);

/**
 * This function verifies a received peer and an own mac by comparison.
 *
 * @param[in] peerMac               pointer to the peer mac
 * @param[in] ownMac                pointer to the own mac
 * @param[in] macLen                length of peer/own mac.
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: verification successfully completed<br>
 *                                  1 - CRYPTO_ERR_INVALID: verification completed, but the result is not valid (macs differ)<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 */
uint8_t CryptoLib_VerifyCmac(CRYPTOLIB_HUGE const uint8_t* peerMac,
                             CRYPTOLIB_HUGE const uint8_t* ownMac,
                             const uint8_t macLen);

#endif /* (CRYPTOLIB_ALT_CMAC == ENABLED) */
#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_MAC_H */

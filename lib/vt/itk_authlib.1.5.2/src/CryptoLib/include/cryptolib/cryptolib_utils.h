/**
 * @file
 * @brief Contains utility function declarations
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_UTILS_H
#define CRYPTOLIB_UTILS_H

#include "cryptolib_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Securely overwrites the given memory area of specified length with 0.
 *
 * @param[out]  memory      Pointer to memory to be filled
 * @param[in]  count        Number of bytes to be written
 */
void CryptoLib_Zeroize(CRYPTOLIB_HUGE void* memory, size_t count);

#if (CRYPTOLIB_ALT_KEYS == ENABLED)

/**
 * Converts a Base16 / Hex-Encoded key string into binary representation.
 * E.g. "abcdef" to {0xab, 0xcd, 0xef}
 *
 * @param[in] keyString     Key string to be converted
 * @param[out] keyArray     Buffer to receive the converted key bytes
 * @param[in] keyArraySize  Size of the keyArray buffer
 */
uint8_t CryptoLib_ConvertKeyStringToByteArray(const char* keyString, uint8_t* keyArray, size_t keyArraySize);

#endif /* #if (CRYPTOLIB_ALT_KEYS == ENABLED) */

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_UTILS_H */

/**
 * @file  cryptolib_ecdh_modinv.h
 * @brief Interface file for ecdh modular inverse
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_ECDH_MODINV_H
#define CRYPTOLIB_ECDH_MODINV_H

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"

#ifdef __cplusplus
extern "C" {
#endif

/** Enum for statemachine states */
typedef enum {
    MODINV_IDLE = 0, /**< Ready for new calls */
    MODINV_GCD = 1, /**< Perform sliced gcd calculation */
    MODINV_PREPARE = 2, /**< Prepare loop */
    MODINV_ITERATE = 3, /**< Perform loop iterations */
    MODINV_FINAL = 4 /**< Ready to write output */
} ModInvSlicingState_t;

/** Struct to hold variables of mod inv subfunctions*/
typedef struct {
    mbedtls_mpi G; /**< GCD(Base, Modulus) */
    mbedtls_mpi TA; /**< MOD(Base, Modulus) */
    mbedtls_mpi TU; /**< Working copy of MOD(Base, Modulus) */
    mbedtls_mpi U1; /**< Temp */
    mbedtls_mpi U2; /**< Temp */
    mbedtls_mpi TB; /**< Working copy of Modulus */
    mbedtls_mpi TV; /**< Working copy of Modulus */
    mbedtls_mpi V1; /**< Temp */
    mbedtls_mpi V2; /**< Temp */

    uint16_t lc;  /**< loop counter */

    /** GCD slicing sub-state tag */
    enum GcdState_tag {
        GCD_IDLE = 0, /**< GCD function not active */
        GCD_ACTIVE = 1 /**< GCD function in slice mode */
    } GcdState; /**< GCD slicing sub-state */
} ModInvSlicingContext_t;

/**
 * Calculates the Modular inverse: out = base^-1 mod modulus, implemented
 * after HAC 14.61 / 14.64
 *
 * @param[out] out                  pointer to output mpi
 * @param[in] base                  pointer to the base mpi
 * @param[in] modulus               pointer to the modulus mpi
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: inverse successfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid parameter given<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedModInv(mbedtls_mpi* out,
                               const mbedtls_mpi* base,
                               const mbedtls_mpi* modulus,
                               const CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_ECDH_MODINV_H */

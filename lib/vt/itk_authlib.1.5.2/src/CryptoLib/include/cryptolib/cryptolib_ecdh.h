/**
 * @file  cryptolib_ecdh.h
 * @brief Interface file for ECDH related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_ECDH_H
#define CRYPTOLIB_ECDH_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib_types.h"

#define ECDH_COMMON_SECRET_LEN 32   /**< Length of common ECDH secret*/

#ifdef __cplusplus
extern "C" {
#endif

#if (CRYPTOLIB_ALT_ECDH_EXT == ENABLED)

/**
 * \note Only available if \ref CRYPTOLIB_ALT_ECDH_EXT is enabled.
 *
 * This function calculates a common secret using the ECDH method.
 * If the user wants to abort during time slicing, the function shall be called
 * with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 * and all sub-state machines.
 *
 * @param[in] privateKey            The own private ECDH key
 * @param[in] peerPublicKey         pointer to the public key, null-terminated string (hexadecimal)
 * @param[in] curve                 0 - CURVE_25519_BE: use curve 25519 with big-endian output<br>
 *                                  1 - CURVE_25519_LE: use curve 25519 with little-endian output
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 * @param[out] sharedSecret         pointer to the calculated common secret, fixed length of 256bits
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: shared secret successfully calculated<br>
 *                                  1 - CRYPTO_ERR_INVALID: the given keys have an invalid format<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: sliced ECDH not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: wrong slicing parameter given<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_Ecdh(CRYPTOLIB_ALT_KEY_TYPE privateKey,
                       CRYPTOLIB_HUGE const char* peerPublicKey,
                       const CryptoLib_Curve_t curve,
                       const CryptoLib_Slicing_t slicing,
                       CRYPTOLIB_HUGE uint8_t sharedSecret[ECDH_COMMON_SECRET_LEN]);

#elif (CRYPTOLIB_ALT_ECDH_INT == ENABLED)

/**
 * \note Only available if \ref CRYPTOLIB_ALT_ECDH_INT is enabled.
 *
 * This function calculates a common secret using the ECDH method.
 * If the user wants to abort during time slicing, the function shall be called
 * with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 * and all sub-state machines.
 *
 * @param[in] privateKey            key ID of the own private ECDH key (respectively the ECDH key pair)
 * @param[in] peerPublicKey         pointer to the public key, null-terminated string (hexadecimal)
 * @param[in] curve                 0 - CURVE_25519_BE: use curve 25519 with big-endian output<br>
 *                                  1 - CURVE_25519_LE: use curve 25519 with little-endian output
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 * @param[out] sharedSecret         pointer to the calculated common secret, CRYPTOLIB_ALT_KEY_TYPE
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: shared secret successfully calculated<br>
 *                                  1 - CRYPTO_ERR_INVALID: the given keys have an invalid format<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: sliced ECDH not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: wrong slicing parameter given<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_Ecdh(CRYPTOLIB_ALT_KEY_TYPE privateKey,
                       CRYPTOLIB_HUGE const char* peerPublicKey,
                       const CryptoLib_Curve_t curve,
                       const CryptoLib_Slicing_t slicing,
                       CRYPTOLIB_ALT_KEY_TYPE* sharedSecret);

#else

/**
 * This function calculates a common secret using the ECDH method.
 * If the user wants to abort during time slicing, the function shall be called
 * with SLICE_RESET as slicing parameter in order to reset the slicing state machine
 * and all sub-state machines.
 *
 * @param[in] privateKey            pointer to the private key, null-terminated string (hexadecimal)
 * @param[in] peerPublicKey         pointer to the public key, null-terminated string (hexadecimal)
 * @param[in] curve                 0 - CURVE_25519_BE: use curve 25519 with big-endian output<br>
 *                                  1 - CURVE_25519_LE: use curve 25519 with little-endian output
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 * @param[out] sharedSecret         pointer to the calculated common secret, fixed length of 256bits
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: shared secret successfully calculated<br>
 *                                  1 - CRYPTO_ERR_INVALID: the given keys have an invalid format<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: sliced ECDH not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: wrong slicing parameter given<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_Ecdh(CRYPTOLIB_HUGE const char* privateKey,
                       CRYPTOLIB_HUGE const char* peerPublicKey,
                       const CryptoLib_Curve_t curve,
                       const CryptoLib_Slicing_t slicing,
                       CRYPTOLIB_HUGE uint8_t sharedSecret[ECDH_COMMON_SECRET_LEN]);

#endif /* #if (CRYPTOLIB_ALT_ECDH_EXT == ENABLED) */

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_ECDH_H */

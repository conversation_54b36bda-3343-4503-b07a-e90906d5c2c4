/**
 * @file  cryptolib_rng.h
 * @brief Interface file for RNG related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_RNG_H
#define CRYPTOLIB_RNG_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This function generates a pseudo-random number by using a given seed.
 * The security of the generated random numbers highly depends on the quality
 * of the seed. I.e. the seed has to provide sufficient entropy.
 * When this function is called for the first time, the pointer to the seed must not be NULL.
 * For any further calls the pointer may be NULL (to use the same seed for the random
 * number generation). If CRYPTO_ERR_RESEED is returned, the function shall be called
 * with a new seed.
 *
 * If CRYPTOLIB_ALT_RNG is ENABLED this function needs to be implemented by the user
 * to provide randomness to the library. The function should fill the buffer buf
 * with bufLen bytes of random data.
 *
 * @note
 * If CRYPTOLIB_USE_SYSTEM_ENTROPY is set the seed and seedLen parameter is omitted.
 * The PRNG is automatically (re-)seeded using platform entropy sources.
 *
 * @param[out] buf                  pointer to the generated pseudo-random number
 * @param[in] bufLen                length of the pseudo-random number to be generated
 * @param[in] seed                  pointer to the seed
 * @param[in] seedLen               length of the seed
 *
 * @return                          0  - CRYPTO_ERR_SUCCESS: random number successfully generated<br>
 *                                  7  - CRYPTO_ERR_NULL_POINTER: null pointer given<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  13 - CRYPTO_ERR_RESEED: random number generation failed due to depleted entropy source<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls error occured<br>
 */
uint8_t CryptoLib_Random(CRYPTOLIB_HUGE uint8_t* buf,
                         const uint16_t bufLen,
                         CRYPTOLIB_HUGE const uint8_t* seed,
                         const uint16_t seedLen);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_RNG_H */

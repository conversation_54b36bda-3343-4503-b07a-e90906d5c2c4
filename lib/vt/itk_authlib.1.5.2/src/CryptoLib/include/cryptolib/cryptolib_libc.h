/**
 * @file  cryptolib_libc.h
 * @brief Interface file for redefined standard library functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_LIBC_H
#define CRYPTOLIB_LIBC_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include <stddef.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This function is the huge pointer equivalent to the standard libary function memset().
 * It sets the first n bytes of the block of memory pointed by dest
 * to the specified value c (interpreted as an unsigned char).
 *
 * @param[in,out] dest         pointer to the block of memory to fill
 * @param[in] c                value to be set
 * @param[in] n                number of bytes to be set
 *
 * @return                     dstpp is returned
 */
CRYPTOLIB_HUGE void* CryptoLib_Memset(CRYPTOLIB_HUGE void* dest, int c, size_t n);

/**
 * This function is the huge pointer equivalent to the standard libary function
 * memcmp().
 * Compares the first n bytes of the block of memory pointed by vl to the
 * first n bytes pointed by vr, returning zero if they all match or a value
 * different from zero representing which is greater if they do not.
 *
 * @param[in] vl             pointer to the first block of memory
 * @param[in] vr             pointer to the second block of memory
 * @param[in] n              number of bytes to compare
 *
 * @return                     <0 - the first byte that does not match in both
 *                                  memory blocks has a lower value in ptr1 than
 *                                  in ptr2 (if evaluated as unsigned char values)<br>
 *                              0 - the contents of both memory blocks are equal<br>
 *                             >0 - the first byte that does not match in both
 *                                  memory blocks has a greater value in ptr1
 *                                  than in ptr2 (if evaluated as unsigned char
 *                                  values)<br>
 */
int32_t CryptoLib_Memcmp(CRYPTOLIB_HUGE const void* vl,
                         CRYPTOLIB_HUGE const void* vr,
                         size_t n);

/**
 * This function converts an ASCII string into an integer.
 * The return value is the integer equivalent of string. If endPtr is non-NULL,
 * then *endPtr is filled in with the character after the last one that was part
 * of the integer. For this implementation, base shall be 16 and the string must
 * not represent a negative integer.
 *
 * @param[in] string           string of ASCII digits (possibly preceded by white space)
 * @param[out] endPtr          where to store address of terminating character, or NULL
 * @param[in] base             base for conversion (here: must be 16)
 *
 * @return                     - unsigned long integer equivalent of the given string<br>
 *                             - 0, if string doesn't contain a valid integer<br>
 *                             - ULONG_MAX, if an overflow occured, the given base
 *                               is invalid or the string represents a negative integer<br>
 *
 */
uint32_t CryptoLib_Strtoul(CRYPTOLIB_HUGE const char* string,
                           CRYPTOLIB_HUGE char** endPtr,
                           int16_t base);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_LIBC_H */

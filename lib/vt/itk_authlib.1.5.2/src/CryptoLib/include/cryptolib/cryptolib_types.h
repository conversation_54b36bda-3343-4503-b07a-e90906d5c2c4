/**
 * @file  cryptolib_types.h
 * @brief Typedefs of CryptoLib
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_TYPES_H
#define CRYPTOLIB_TYPES_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifdef TEST
#define STATICFCN /**< Preprocessor directive to be able to test static functions */
#else
#define STATICFCN static /**< Preprocessor directive to be able to test static functions */
#endif

#define X509_SERIAL_LEN 21   /**< Length of serial number of a certificate (including leading byte indicating the sign) */
#define X509_SIG_OID_LEN 9   /**< Length of OID in byte format for signature and public key algorithms */
#define SHA256_DIGEST_LEN 32 /**< Length in bytes of a SHA256 message digest */
#define AES128_KEY_LEN 16    /**< Length of AES128 key */

/** Struct for exchanging diagnostic data of last occured error. */
typedef struct {
    uint32_t id;              /**< code snippet where the error emerged. */
    uint8_t errorCode;        /**< error code that was returned */
    int32_t detailedCode;     /**< detailed error code, e.g. mbedtls raw error */
} CryptoLib_LastError_t;

/** Struct for saving digest progress based on SHA256 */
typedef struct {
    uint32_t total[2];            /**< number of bytes processed  */
    uint32_t state[8];            /**< intermediate digest state  */
    unsigned char buffer[64];     /**< data block being processed */
    int is224;                    /**< 0 => SHA-256, else SHA-224 */
} CryptoLib_HashContext_t;

/** Enum to choose between different elliptic curves */
typedef enum {
    CURVE_25519_BE = 0,     /**< Curve25519 with output in big-endian */
    CURVE_25519_LE,         /**< Curve25519 with output in little-endian */
} CryptoLib_Curve_t;

/** Enum to choose between different KDFs */
typedef enum {
    KDF_NIST_SP800_56A_HMAC_SHA256 = 0u,    /**< One-step KDF NIST SP800 56A using HMAC (SHA-256) */
    KDF_ITK_HMAC_SHA256 = 1u                /**< One-step KDF diverging from NIST SP800 56A in the encoding of an internal counter */
} CryptoLib_Kdf_t;

/** Enum to choose between different algorithms for computation of common secret */
typedef enum {
    CURVE_25519_KDF_ITK_HMAC_SHA256 = 0u,            /**< ECDH using big-endian X25519 and one-step KDF diverging from NIST SP800 56A */
    CURVE_25519_KDF_NIST_SP800_56A_HMAC_SHA256 = 1u, /**< ECDH using RFC 7748 X25519 and one-step KDF NIST SP800 56A using HMAC */
} CryptoLib_Algorithm_t;

/** Enum to define slicing process state */
typedef enum {
    SLICE_INIT = 0,            /**< Begin an operation in slicing mode */
    SLICE_CONTINUE = 1,        /**< Continue an operation in slicing mode */
    SLICE_FINISH = 2,          /**< Complete an operation in slicing mode */
    SLICE_RESET = 254,         /**< Reset slicing statemachines */
    SLICE_NO_SLICING = 255     /**< Do not use slicing */
} CryptoLib_Slicing_t;

/** Enum to define types of error return values */
typedef enum {
    CRYPTO_ERR_SUCCESS = 0,                  /**< Computation successful */
    CRYPTO_ERR_INVALID,                      /**< Validation failed */
    CRYPTO_ERR_CALLAGAIN,                    /**< Computation not complete */
    CRYPTO_ERR_TYPE,                         /**< Certificate type invalid */
    CRYPTO_ERR_INTERNAL,                     /**< Internal error occured */
    CRYPTO_ERR_CERT_ON_CRL,                  /**< Certificate is revoked */
    CRYPTO_ERR_SIGNATURE_INVALID,            /**< Signature is invalid */
    CRYPTO_ERR_NULL_POINTER,                 /**< A null pointer was given */
    CRYPTO_ERR_LAB_CERT_INVALID,             /**< Lab certificate verification failed */
    CRYPTO_ERR_MANU_CERT_INVALID,            /**< Manufacturer certificate verification failed */
    CRYPTO_ERR_MANU_SERIES_CERT_INVALID,     /**< Manufacturer series certificate verification failed */
    CRYPTO_ERR_DEVICE_CERT_INVALID,          /**< Device certificate verification failed */
    CRYPTO_ERR_BAD_INPUT,                    /**< Bad input parameter */
    CRYPTO_ERR_RESEED,                       /**< Random number generation failed due to depleted entropy source */
    CRYPTO_ERR_ALLOC_FAILED,                 /**< Memory allocation failed */
    CRYPTO_ERR_MBEDTLS_FAILED,               /**< mbedTLS returned error other than MBEDTLS_ERR_XXX_ALLOC_FAILED */
    CRYPTO_ERR_ENUM_OUT_OF_BOUNDS,           /**< Internal enum is out of bounds */
    CRYPTO_ERR_RNG_FAILED,                   /**< The (external) RNG failed. */
    CRYPTO_ERR_ALT_TIMEOUT,                  /**< Alt. API timeout during operation */
    CRYPTO_ERR_ALT_FAILED                    /**< Alt. API specific error */
} CryptoLib_Return_t;

/** Enum to define public key types */
typedef enum {
    CRYPTO_PK_NONE = 0,     /**< none */
    CRYPTO_PK_RSA,          /**< RSA public key */
    CRYPTO_PK_ECKEY         /**< EC public key */
} CryptoLib_Pk_t;

/** X509 certificate */
typedef struct CryptoLib_Crt_t {
    uint8_t mdOfTbs[SHA256_DIGEST_LEN];     /**< Message digest over the certificate file without its signature */
    uint8_t version;                        /**< must be: 3 */
    uint8_t serial[X509_SERIAL_LEN];        /**< unique serial number of a single certificate */
    uint8_t serialLen;                      /**< number of bytes used in serial */

    /** Issuer */
    struct issuer_t {
        CRYPTOLIB_HUGE char* country;          /**< country of the issuing organization */
        CRYPTOLIB_HUGE char* organization;     /**< name of the issuing organization */
        CRYPTOLIB_HUGE char* commonName;       /**< email address of the issuing organization */
    } issuer;                                  /**< issuer struct */

    /** Validity */
    struct validity_t {
        CRYPTOLIB_HUGE char* notBefore;     /**< year/month/day/hour/minute/secondZ */
        CRYPTOLIB_HUGE char* notAfter;      /**< must be: "99991231235959Z" */
    } validity;                             /**< validity struct */

    /** Subject */
    struct subject_t {
        CRYPTOLIB_HUGE char* organization;           /**< example: 02#01#00#01#01#00#0153 */
        CRYPTOLIB_HUGE char* organizationalUnit;     /**< example: 003B#11#01#01FB#1234 */
        CRYPTOLIB_HUGE char* commonName;             /**< string holding the common name */
        CRYPTOLIB_HUGE char* country;                /**< string holding the country */
        CRYPTOLIB_HUGE char* pseudonym;              /**< string holding the pseudonym, example: 0301015F02015F44024003 */
    } subject;                                       /**< subject struct */

    /** Subject PK */
    struct subjectPublicKey_t {
        CRYPTOLIB_HUGE char* publicKeyAlgorithm;     /**< must be: "rsaEncryption" or "id-ecPublicKey" */
        CryptoLib_Pk_t publicKeyType;                /**< type of public key */
        CRYPTOLIB_HUGE char* ecPublicKey;            /**< if publicKeyAlgorithm is id-ecPublicKey */
        CRYPTOLIB_HUGE char* rsaModulus;             /**< if publicKeyAlgorithm is rsaEncryption, modulus of public key */
        CRYPTOLIB_HUGE char* rsaExponent;            /**< if publicKeyAlgorithm is rsaEncryption, exponent of public key */
    } subjectPublicKey;                              /**< public key struct */

    /** Extensions */
    struct extensions_t {
        uint8_t keyUsage;             /**< key usage */
        uint8_t basicConstraints;     /**< Basic constraint extension, true or false */
        uint16_t maxPathlen;          /**< Basic constraint extension, maxPathlen, 0 for undefined */
    } extensions;                     /**< extensions struct */

    /** Signaute */
    struct signature_t {
        CRYPTOLIB_HUGE char* algorithm;             /**< must be: "rsassa-pss" */
        uint8_t algorithmOid[X509_SIG_OID_LEN];     /**< OID of signature algorithm */
        CRYPTOLIB_HUGE uint8_t* value;              /**< signature value */
        uint16_t length;                            /**< signature length */
    } signature;                                    /**< signature struct */
} CryptoLib_Crt_t;

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_TYPES_H */

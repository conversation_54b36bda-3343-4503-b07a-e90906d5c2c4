/**
 * @file  cryptolib_ecdh_compshared.h
 * @brief Interface file for sliced ecdh compute shared
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_ECDH_COMPSHARED_H
#define CRYPTOLIB_ECDH_COMPSHARED_H

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"
#include "mbedtls/ecp.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Calculates a shared secret. Raw function that only does the core computation.
 *
 * @param[in] grp                   ECP group
 * @param[out] sharedSecret         Destination MPI (shared secret)
 * @param[in] peerPublicKey         Public key from other party
 * @param[in] privateKey            Our secret exponent (private key)
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: inverse successfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid parameter given<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedComputeShared(const mbedtls_ecp_group* grp,
                                      mbedtls_mpi* sharedSecret,
                                      const mbedtls_ecp_point* peerPublicKey,
                                      const mbedtls_mpi* privateKey,
                                      const CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_ECDH_COMPSHARED_H */

/**
 * @file  cryptolib_crl.h
 * @brief Interface file for CRL related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_CRL_H
#define CRYPTOLIB_CRL_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This function checks the validity of the host application's CRL.
 * As it could be possible, that the CRL is not stored in a coherent
 * accessible memory area, the function provides a possibility to check
 * the CRL in several slices. Therefore, the parameter slicingMem shall be used
 * by the host app to indicate, if the CRL is provided in slices or complete
 * (SLICE_NO_SLICING). If slices are used, for the first call of the function
 * the parameter slicing has to be set to SLICE_INIT, for the last slice the
 * value SLICE_FINISH has to be used. For all "middle" slices the parameter
 * slicing has to be set to SLICE_CONTINUE. The parameter crlBuffer shall
 * point to the first byte of the CRL or the slice that shall be read.
 * The parameter crlLength shall be the length of the CRL or the slice in bytes.
 * The first slice shall have a length of at least 12 bytes in order to get the
 * length of the complete CRL.
 * Since this function uses the RSA algorithm to verify the CRL signature,
 * an additonal slicing parameter slicingTime is provided to use the time slicing
 * option. As long as parsing the CRL is not finished, the value of slicingTime
 * is ignored. And vice versa, the value of slicingMem is ignored when the time
 * sliced verification process starts. The verification process starts with the
 * next function call after slicingMem = SLICE_FINISH has been used.
 * crlSigningCA may be NULL during parsing the CRL and crlBuffer may be
 * NULL during time slicing.
 * If the user wants to abort during time slicing, the function shall be called with
 * SLICE_RESET as time slicing parameter in order to reset the time slicing state machines
 * and all sub-state machines.
 * The same applies if the user wants to abort during memory slicing: The function
 * shall be called with SLICE_RESET as memory slicing parameter in order to reset
 * the memory slicing state machines.
 *
 * @param[in] crlBuffer             pointer to the buffer where the piece of CRL is stored
 * @param[in] crlLength             size of the piece of CRL which shall be read
 * @param[in] crlSigningCA          pointer to the CRL-Signing sub CA certificate
 * @param[in] slicingMem            0 - SLICE_INIT: first slice of the CRL is provided<br>
 *                                  1 - SLICE_CONTINUE: "middle" slices of the CRL are provided<br>
 *                                  2 - SLICE_FINISH: last slice of the CRL is provided<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no memory slicing shall be used<br>
 * @param[in] slicingTime           0 - SLICE_INIT: first step of the validation<br>
 *                                  1 - SLICE_CONTINUE: during validation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no time slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: CRL valid<br>
 *                                  1 - CRYPTO_ERR_INVALID: CRL invalid<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: check not complete<br>
 *                                  6 - CRYPTO_ERR_SIGNATURE_INVALID: Validation failed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: first CRL slice is to small or the slicing parameters are invalid<br>
 *                                  16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_CheckCRLValidity(CRYPTOLIB_HUGE const uint8_t* const crlBuffer,
                                   const uint16_t crlLength,
                                   CRYPTOLIB_HUGE const CryptoLib_Crt_t* const crlSigningCA,
                                   const uint8_t slicingMem,
                                   const uint8_t slicingTime);

/**
 * This function checks if the certificate is revoked by scanning the CRL.
 *
 * @param[in] crlBuff               pointer to the buffer where the crl is stored
 * @param[in] crlBuffLen            length of crl
 * @param[in] slicing               0 - SLICE_INIT: first step of the validation<br>
 *                                  1 - SLICE_CONTINUE: during validation<br>
 *                                  2 - SLICE_FINISH: last step of the validation<br>
 *                                  254 - SLICE_RESET: reset the function<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 * @param[in] serialNumber          pointer to the serial number which has to be checked
 * @param[in] serialNumberLength    length of the serial number
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: verification successfully completed<br>
 *                                  1 - CRYPTO_ERR_INVALID: the input CRL is invalid<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: not finished yet, call again<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  5 - CRYPTO_ERR_CERT_ON_CRL: Certificate listed on CRL<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: bad input parameter, e.g. slicing<br>
 *                                  16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_CheckCertificateOnCrl(CRYPTOLIB_HUGE const uint8_t* const crlBuff,
                                        const uint16_t crlBuffLen,
                                        const CryptoLib_Slicing_t slicing,
                                        CRYPTOLIB_HUGE const uint8_t* serialNumber,
                                        const uint8_t serialNumberLength);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_CRL_H */

/**
 * @file  cryptolib_diag.h
 * @brief Interface file for diag to provide internal state information
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */


#ifndef CRYPTOLIB_DIAG_H
#define CRYPTOLIB_DIAG_H

#include "cryptolib_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Stores the given data such that it can be retrieved by calling
 * CryptoLib_GetLastError.
 *
 * @param[in, out] err          CryptoLib error code
 * @param[in] detailedError     detailed error code, e.g. mbedtls error code
 * @param[in] id                ID of code snippet where the error emerged
 */
void CryptoLib_SetError(uint8_t* err, int32_t detailedError, uint32_t id);

/**
 * This function can be used to retrieve detailed information about the
 * latest error that occured. This includes an ID of the code snippet
 * from where the error emerged as well as a detailed error code, if available.
 *
 * @return Struct holding detailed information about the last error occured.
 */
CryptoLib_LastError_t CryptoLib_GetLastError(void);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_DIAG_H */

/**
 * @file  cryptolib_crl_validity.h
 * @brief Global typedefs for CRL validity check
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_CRL_VALIDITY_H
#define CRYPTOLIB_CRL_VALIDITY_H

#include "cryptolib_types.h"

#define RSASSA_PSS_SIGNATURE_LEN 256   /**< Length of RSASSA-PSS(2048) signature in bytes */

#ifdef __cplusplus
extern "C" {
#endif

/** Enum for sliced CheckCRLValidity states (memory slicing) */
typedef enum {
    INIT = 0,          /**< First step of the validation */
    HASH_UPDATE,       /**< Updating hash digest */
    SIGNATURE_UPDATE,  /**< Updating signature */
    VERIFY_RSA         /**< Verifying RSA signature */
} CheckCRLValidityState_t;

/** Struct holding stored data for memory slicing a CRL */
typedef struct {
    /**@ \{*/
    uint8_t flgInit;
    uint8_t offsetCrl;
    CheckCRLValidityState_t stateMemSlice;
    uint32_t crlLen;
    uint32_t tbsLen;
    uint32_t signatureStartLen;
    uint32_t crlLenDone;
    uint32_t tbsLenLeft;
    uint32_t signatureLenLeft;
    CryptoLib_HashContext_t hashCtx;
    uint8_t hashDigest[SHA256_DIGEST_LEN];
    uint8_t signature[RSASSA_PSS_SIGNATURE_LEN];
    /**@ \}*/
} CheckCRLMemorySlicedContext_t;

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_CRL_VALIDITY_H */

/**
 * @file  cryptolib_ecdh_gcd.h
 * @brief Interface file for ecdh sliced greatest common divisor
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_ECDH_GCD_H
#define CRYPTOLIB_ECDH_GCD_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"

#ifdef __cplusplus
extern "C" {
#endif

/** Struct to hold input/output and local variables of gcd subfunctions*/
typedef struct {
    /* input output */
    mbedtls_mpi ta; /**< Working copy of integer a */
    mbedtls_mpi tb; /**< Working copy of integer b */

    /* internals */
    struct {
        size_t lz; /**< Number of zero-bits before the least significant '1' bit in ta */
        size_t lzt; /**< Number of zero-bits before the least significant '1' bit in tb */
        uint16_t lc; /**< Loop counter */
    } internals; /**< Local variables */
} GcdSlicingContext_t;

/**
 * Calculates the Greatest common divisor = gcd(A, B), implemented after HAC 14.54
 *
 * @param[out] divisor              pointer to divisor mpi
 * @param[in] integerA              pointer to the first integer mpi
 * @param[in] integerB              pointer to the second integer mpi
 * @param[in] slicing               0 - SLICE_INIT: first step of the computation<br>
 *                                  1 - SLICE_CONTINUE: during computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: divisor successfully calculated<br>
 *                                  1 - CRYPTO_ERR_INVALID: invalid slicing parameter for current state<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedTLS returned error<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedGCD(mbedtls_mpi* divisor,
                            const mbedtls_mpi* integerA,
                            const mbedtls_mpi* integerB,
                            const CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_ECDH_GCD_H */

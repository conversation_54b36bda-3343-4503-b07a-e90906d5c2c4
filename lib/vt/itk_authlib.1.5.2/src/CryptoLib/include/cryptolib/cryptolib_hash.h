/**
 * @file  cryptolib_hash.h
 * @brief Interface file for hash related functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_HASH_H
#define CRYPTOLIB_HASH_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Calculates a message digest over max. 65535 bytes of data using the SHA256 algorithm.
 *
 * @param[in]  input            pointer to the data to be digested
 * @param[in]  inputLen         length of input
 * @param[out] output           pointer to the output array. Needs to be preallocated
 *                              with a length of at least 32 bytes
 *
 * @return                      0 - CRYPTO_ERR_SUCCESS: digest successfully created<br>
 *                              7 - CRYPTO_ERR_NULL_POINTER: null pointer given<br>
 *                              12 - CRYPTO_ERR_BAD_INPUT: Bad input parameter<br>
 */
uint8_t CryptoLib_Sha256(CRYPTOLIB_HUGE const uint8_t* input,
                         const uint16_t inputLen,
                         CRYPTOLIB_HUGE uint8_t output[SHA256_DIGEST_LEN]);

/**
 * Prepares a CryptoLib_HashContext_t to be used with CryptoLib_Sha256Update and
 * CryptoLib_Sha256Finish.
 *
 * @param[in,out]  context      pointer to the hash context
 *
 * @return                      0 - CRYPTO_ERR_SUCCESS: context successfully prepared<br>
 *                              7 - CRYPTO_ERR_NULL_POINTER: null pointer given<br>
 *                              12 - CRYPTO_ERR_BAD_INPUT: Bad input parameter<br>
 */
uint8_t CryptoLib_Sha256Start(CRYPTOLIB_HUGE CryptoLib_HashContext_t* context);

/**
 * Digests a chunk of data in addition to earlier operations with the same context.
 *
 * @param[in,out]  context      pointer to the prepared context
 * @param[in]  input            pointer to the data to be digested
 * @param[in]  inputLen         length of input
 *
 * @return                      0 - CRYPTO_ERR_SUCCESS: context successfully prepared<br>
 *                              7 - CRYPTO_ERR_NULL_POINTER: null pointer given<br>
 *                              12 - CRYPTO_ERR_BAD_INPUT: Bad input parameter<br>
 */
uint8_t CryptoLib_Sha256Update(CRYPTOLIB_HUGE CryptoLib_HashContext_t* context,
                               CRYPTOLIB_HUGE const uint8_t* input,
                               const uint16_t inputLen);

/**
 * Finalizes all data that has been passed to the given context into a SHA256 digest.
 *
 * @param[in]  context          pointer to the prepared context
 * @param[out] output           pointer to the preallocated output array
 *                              with a length of at least 32 bytes
 *
 * @return                      0 - CRYPTO_ERR_SUCCESS: context successfully prepared<br>
 *                              7 - CRYPTO_ERR_NULL_POINTER: null pointer given<br>
 *                              12 - CRYPTO_ERR_BAD_INPUT: Bad input parameter<br>
 */
uint8_t CryptoLib_Sha256Finish(CRYPTOLIB_HUGE CryptoLib_HashContext_t* context,
                               CRYPTOLIB_HUGE uint8_t output[SHA256_DIGEST_LEN]);

#if (CRYPTOLIB_ALT_KDF == ENABLED)

/**
 * \note Only available if \ref CRYPTOLIB_ALT_KDF is enabled.
 *
 * Derives two cryptographically stronger keys from a weaker key and stores the result as a server and a client key.
 *
 * \details
 * The KDF implementation is expected to derive a 32 byte long key from the shared secret and the given salt and use
 * the first 16 bytes as lwaKeyClient and the remaining 16 bytes as lwaKeyServer.
 *
 * @param[in]  sharedSecret         shared secret to be used to derive LwA keys
 * @param[in]  salt                 Pointer to a char array to be used as salt
 * @param[in]  saltLen              Length of salt
 * @param[in]  algorithm            The algorithm to be used for the KDF<br>
 *                                  CURVE_25519_KDF_ITK_HMAC_SHA256 - One-step KDF diverging from NIST SP800 56A<br>
 *                                  CURVE_25519_KDF_NIST_SP800_56A_HMAC_SHA256 - One-step KDF NIST SP800 56A using HMAC
 * @param[in]  lwaKeyClient         Key to hold the derived client LwA key
 * @param[in]  lwaKeyServer         Key to hold the derived server LwA key
 * @param[in]  slicing              1 - SLICE_CONTINUE: computation<br>
 *                                  254 - SLICE_RESET: reset slicing state machines<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: key successfully derived and persisted<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: computation not complete<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer given<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: Bad input parameter<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedTLS returned error<br>
 *                                  18 - CRYPTO_ERR_ALT_FAILED: Alt. API returned an error<br>
 */
uint8_t CryptoLib_KeyDerivationFunction(CRYPTOLIB_ALT_KEY_TYPE sharedSecret,
                                        CRYPTOLIB_HUGE const uint8_t* salt,
                                        const uint16_t saltLen,
                                        const CryptoLib_Kdf_t algorithm,
                                        CRYPTOLIB_ALT_KEY_TYPE lwaKeyClient,
                                        CRYPTOLIB_ALT_KEY_TYPE lwaKeyServer,
                                        const CryptoLib_Slicing_t slicing);

#else

/**
 * Derives a cryptographically stronger key from a weaker key. Based on HMAC SHA256 according to NIST SP 800 56Ar2.
 * Using the algorithm parameter it is possible to use the legacy implementation that deviates from NIST specification.
 *
 * @param[in]  sharedSecret         Pointer to a char array to be used for deriving a key
 * @param[in]  sharedSecretLen      Length of sharedSecret
 * @param[in]  salt                 Pointer to a char array to be used as salt
 * @param[in]  saltLen              Length of salt
 * @param[in]  algorithm            The algorithm to be used for the KDF<br>
 *                                  CURVE_25519_KDF_ITK_HMAC_SHA256 - One-step KDF diverging from NIST SP800 56A<br>
 *                                  CURVE_25519_KDF_NIST_SP800_56A_HMAC_SHA256 - One-step KDF NIST SP800 56A using HMAC
 * @param[out] derivedKey           Pointer to the derived key, fixed size of 256 bit
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: key successfully created<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer given<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: Bad input parameter<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedTLS returned error<br>
 */
uint8_t CryptoLib_KeyDerivationFunction(CRYPTOLIB_HUGE const uint8_t* sharedSecret,
                                        const uint16_t sharedSecretLen,
                                        CRYPTOLIB_HUGE const uint8_t* salt,
                                        const uint16_t saltLen,
                                        const CryptoLib_Kdf_t algorithm,
                                        CRYPTOLIB_HUGE uint8_t derivedKey[SHA256_DIGEST_LEN]);

#endif /* #if (CRYPTOLIB_ALT_KDF == ENABLED) */

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_HASH_H */

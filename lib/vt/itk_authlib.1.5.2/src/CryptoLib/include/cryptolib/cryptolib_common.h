/**
 * @file  cryptolib_common.h
 * @brief Definitions shared between modules
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_COMMON_H
#define CRYPTOLIB_COMMON_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "mbedtls/bignum.h"

#ifdef __cplusplus
extern "C" {
#endif

#define ciL    (sizeof(mbedtls_mpi_uint))         /**< chars in limb  */
#define biL    (ciL << 3)                         /**< bits in limb  */
#define biH    (ciL << 2)                         /**< half limb size */

#define HEX_RADIX 16             /**< Base for Hex-Formatted strings */

/**
 * Converts the given input from one endianness to the other, i.e. the hex string
 * is reversed byte-wise (0A1B2C -> 2C1B0A). A possible sign is preserved.
 * The input key must only consist of full bytes, that is any leading or trailing
 * zero must be present (invalid: A1B2C or 2C1BA, valid: 0A1B2C or 2C1BA0)
 *
 * @param[in]  inKey        Pointer to string holding the key that should be converted
 * @param[out] outKey       Pointer to output buffer
 * @param[in]  outKeyLen    Length of the ouptut buffer
 * @return                  0 - CRYPTO_ERR_SUCCESS: conversion successful
 *                          12 - CRYPTO_ERR_BAD_INPUT: output buffer too small
 */
uint8_t CryptoLib_ChangeEndiannessString(CRYPTOLIB_HUGE const char* inKey, char* outKey, const size_t outKeyLen);

/**
 * Converts the given data in-memory from one endianness to the other, i.e. the array
 * is reversed byte-wise ({0x0A, 0x1B, 0x2C} -> {0x2C, 0x1B, 0x0A}).
 *
 * @param[in,out]  data      Pointer to the buffer that should be converted
 * @param[in]  dataLength    Length of the input buffer
 */
void CryptoLib_ChangeEndiannessBinary(CRYPTOLIB_HUGE uint8_t* data, const size_t dataLength);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_COMMON_H */

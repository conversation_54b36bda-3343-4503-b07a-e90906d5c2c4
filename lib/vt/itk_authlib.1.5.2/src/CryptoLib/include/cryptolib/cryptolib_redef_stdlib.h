/**
 * @file  cryptolib_redef_stdlib.h
 * @brief Shadows stdlib implementation when different memory spaces are used.
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im S<PERSON>yerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_REDEF_STDLIB_H
#define CRYPTOLIB_REDEF_STDLIB_H

#include "cryptolib_libc.h"

/**@ \{*/
#define memcmp CryptoLib_Memcmp
#define memcpy __memcphhb
#define memset CryptoLib_Memset
#define strlen __hstrlen
#define strtoul CryptoLib_Strtoul
/**@ \}*/

#endif /* CRYPTOLIB_REDEF_STDLIB_H */

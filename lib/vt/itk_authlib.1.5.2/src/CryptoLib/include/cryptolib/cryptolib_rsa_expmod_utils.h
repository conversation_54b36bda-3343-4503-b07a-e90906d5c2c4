/**
 * @file  cryptolib_rsa_expmod_utils.h
 * @brief Interface file for CryptoLib_SlicedExpMod helper functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 R<PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_RSA_EXPMOD_UTILS_H
#define CRYPTOLIB_RSA_EXPMOD_UTILS_H

#include "cryptolib_types.h"

#include "mbedtls/bignum.h"

#ifdef __cplusplus
extern "C" {
#endif

#define EXP_MOD_WINDOW_SIZE (size_t)((size_t)1 << MBEDTLS_MPI_WINDOW_SIZE) /**< Window size for modular exponentiation */

/** Struct for saving modular exponentiation variables */
typedef struct {
    /* input output */
    mbedtls_mpi result; /**< Working copy of result */
    mbedtls_mpi exponent; /**< Working copy of exponent */
    mbedtls_mpi modulus; /**< Working copy of modulus */
    mbedtls_mpi base; /**< Working copy of base */

    /* internals */
    struct {
        unsigned char sign; /**< Sign of base */
        size_t exponentBitsInWindow; /**< window exponent bit size */
        size_t windowBitSize; /**< window bit size */
        size_t windowTableSize; /**< window table size */
        size_t j; /**< temp */
        size_t nblimbs; /**< mpi limbs */
        size_t bufsize; /**< buffer size to be processed */
        size_t nbits; /**< temp */
        mbedtls_mpi window[EXP_MOD_WINDOW_SIZE]; /**< expo window */
        mbedtls_mpi RR; /**< temp */
        mbedtls_mpi T; /**< temp */
        mbedtls_mpi_uint ei; /**< temp */
        mbedtls_mpi_uint mm; /**< temp */
        mbedtls_mpi_uint state; /**< state */
        uint8_t RR_active; /**< temp */
        mbedtls_mpi WW; /**< Working Window */
    } internals; /**< local variables of original mbedtls implementation */
} RsaExpModContext_t;

/**
 * Fast Montgomery initialization, taken from mpi_montg_init
 *
 * @param[out] mm                   Pointer to mm
 * @param[in] N                     Modulus MPI
 */
void CryptoLib_SlicedMpiExpMod_MontgInit(mbedtls_mpi_uint* mm, const mbedtls_mpi* N);

/**
 * Initializes an RsaExpModContext_t context
 *
 * @param[out] context              Context to initialize
 */
void CryptoLib_SlicedMpiExpMod_InitContext(RsaExpModContext_t* context);

/**
 * Clears an RsaExpModContext_t context
 *
 * @param[out] context              Context to clear
 */
void CryptoLib_SlicedMpiExpMod_ClearContext(RsaExpModContext_t* context);

/**
 * Resets all sub-statemachines
 */
void CryptoLib_SlicedMpiExpMod_ResetPhases(void);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_RSA_EXPMOD_UTILS_H */

/**
 * @file  cryptolib_fid.h
 * @brief Contains function IDs for error identification
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im <PERSON>yerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON><PERSON>
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_FID_H
#define CRYPTOLIB_FID_H

/**@ \{*/
#define FID_CRYPTOLIB_INIT 0x01010000UL
#define FID_CRYPTOLIB_ASN1GETLEN 0x02010000UL
#define FID_CRYPTOLIB_ASN1GETTAG 0x02020000UL
#define FID_CRYPTOLIB_CERTONCRL_SKIPCONTENT 0x03010000UL
#define FID_CRYPTOLIB_CERTONCRL_SKIPTIME 0x03020000UL
#define FID_CRYPTOLIB_CERTONCRL_MOVEAFTERTAG 0x03030000UL
#define FID_CRYPTOLIB_CERTONCRL_CHECKSERIAL 0x03040000UL
#define FID_CRYPTOLIB_CERTONCRL_COPYTOREMAININGBYTES 0x03050000UL
#define FID_CRYPTOLIB_CERTONCRL_CHECKSLICINGPARAMETER 0x03060000UL
#define FID_CRYPTOLIB_CERTONCRL_SETBUFFERS 0x03070000UL
#define FID_CRYPTOLIB_CERTONCRL_MSM 0x03080000UL
#define FID_CRYPTOLIB_CERTONCRL_LISTSTATEMACHINE 0x03090000UL
#define FID_CRYPTOLIB_CERTONCRL_RESETCTX 0x030a0000UL
#define FID_CRYPTOLIB_CHECKCERTIFICATEONCRL 0x030b0000UL
#define FID_CRYPTOLIB_RESETCRLVALIDATE 0x04010000UL
#define FID_CRYPTOLIB_INITCRLVALIDATE 0x04020000UL
#define FID_CRYPTOLIB_HASHUPDATECRLVALIDATE 0x04030000UL
#define FID_CRYPTOLIB_SIGNATUREUPDATECRLVALIDATE 0x04040000UL
#define FID_CRYPTOLIB_VERIFYRSACRLVALIDATE 0x04050000UL
#define FID_CRYPTOLIB_CHECKCRLVALIDITY 0x04060000UL
#define FID_CRYPTOLIB_VERIFYCERTIFICATE 0x05010000UL
#define FID_CRYPTOLIB_INITCERTIFICATE 0x05020000UL
#define FID_CRYPTOLIB_PARSECERTIFICATE 0x05030000UL
#define FID_CRYPTOLIB_FREECERTIFICATE 0x05040000UL
#define FID_CRYPTOLIB_X509_PARSE 0x05110000UL
#define FID_CRYPTOLIB_X509_GETCERTIFICATE 0x05120000UL
#define FID_CRYPTOLIB_X509_GETTBS 0x05130000UL
#define FID_CRYPTOLIB_X509_GETVERSION 0x05140000UL
#define FID_CRYPTOLIB_X509_GETSERIAL 0x05150000UL
#define FID_CRYPTOLIB_X509_GETALG 0x05160000UL
#define FID_CRYPTOLIB_X509_GETISSUER 0x05170000UL
#define FID_CRYPTOLIB_X509_GETDN 0x05180000UL
#define FID_CRYPTOLIB_X509_GETTIME 0x05190000UL
#define FID_CRYPTOLIB_X509_GETVALIDITY 0x051A0000UL
#define FID_CRYPTOLIB_X509_GETSUBJECTPUBLICKEYINFO 0x051B0000UL
#define FID_CRYPTOLIB_X509_GETBASICCONSTRAINTS 0x051C0000UL
#define FID_CRYPTOLIB_X509_GETKEYUSAGE 0x051D0000UL
#define FID_CRYPTOLIB_X509_GETEXTENSIONSSUB 0x051E0000UL
#define FID_CRYPTOLIB_X509_GETEXTENSIONS 0x051F0000UL
#define FID_CRYPTOLIB_X509_GETSIGNATURE 0x05200000UL
#define FID_CRYPTOLIB_X509_GETATTRTYPEVALUE 0x05210000UL
#define FID_CRYPTOLIB_X509_GETVALUEFROMOIDFIELD 0x05220000UL
#define FID_CRYPTOLIB_X509_CONVERTTOSIGNATURESTRUCT 0x05230000UL
#define FID_CRYPTOLIB_X509_GETPUBLICKEYEC 0x05240000UL
#define FID_CRYPTOLIB_X509_GETPUBLICKEYRSA 0x05250000UL
#define FID_CRYPTOLIB_MAPMBEDTLSERRORCODE 0x06010000UL
#define FID_CRYPTOLIB_SETERROR 0x06020000UL
#define FID_CRYPTOLIB_GETLASTERROR 0x06030000UL
#define FID_CRYPTOLIB_ECDH_INIT 0x06040000UL
#define FID_CRYPTOLIB_ECDH_FINALIZE 0x06050000UL
#define FID_CRYPTOLIB_ECDH_CLEAN 0x06060000UL
#define FID_CRYPTOLIB_ECDH 0x06070000UL
#define FID_MOD_ADD 0x07010000UL
#define FID_MOD_MUL 0x07020000UL
#define FID_MOD_SUB 0x07030000UL
#define FID_CRYPTOLIB_SLICEDCOMPUTESHARED 0x08010000UL
#define FID_ECP_MULSANITYCHECKS 0x08020000UL
#define FID_CRYPTOLIB_SLICEDCS_INIT 0x08030000UL
#define FID_CRYPTOLIB_SLICEDCS_CLEAR 0x08040000UL
#define FID_CRYPTOLIB_SLICEDMONTDA 0x09010000UL
#define FID_CRYPTOLIB_SLICEDMONTDA_INIT 0x09020000UL
#define FID_PART1 0x09030000UL
#define FID_PART2 0x09040000UL
#define FID_PART3 0x09050000UL
#define FID_PART4 0x09060000UL
#define FID_PART5 0x09070000UL
#define FID_PART6 0x09080000UL
#define FID_PART7 0x09090000UL
#define FID_PART8 0x090a0000UL
#define FID_CRYPTOLIB_SLICEDMONTDA_CLEAN 0x090b0000UL
#define FID_CRYPTOLIB_SLICEDGCD 0x0a010000UL
#define FID_CRYPTOLIB_SLICEDGCD_PREPARE 0x0a020000UL
#define FID_CRYPTOLIB_SLICEDGCD_STEP 0x0a030000UL
#define FID_CRYPTOLIB_SLICEDGCD_FINALIZE 0x0a040000UL
#define FID_CRYPTOLIB_SLICEDGCD_CLEAN 0x0a050000UL
#define FID_CRYPTOLIB_SLICEDMODINV 0x0b010000UL
#define FID_CRYPTOLIB_SLICEDMODINV_INIT 0x0b020000UL
#define FID_CRYPTOLIB_SLICEDMODINV_PREPARE 0x0b030000UL
#define FID_CRYPTOLIB_SLICEDMODINV_ITERATE 0x0b040000UL
#define FID_CRYPTOLIB_SLICEDMODINV_FINALIZE 0x0b050000UL
#define FID_CRYPTOLIB_SLICEDMODINV_CLEAN 0x0b060000UL
#define FID_CRYPTOLIB_SLICEDECPMUL 0x0c010000UL
#define FID_CRYPTOLIB_SLICEDECPMUL_PREPARE 0x0c020000UL
#define FID_CRYPTOLIB_SLICEDECPMUL_ITERATE 0x0c030000UL
#define FID_CRYPTOLIB_SLICEDECPMUL_FINALIZE 0x0c040000UL
#define FID_CRYPTOLIB_SLICEDECPMUL_CLEAN 0x0c050000UL
#define FID_CRYPTOLIB_SLICEDECPMUL_RANDOMIZE 0x0c060000UL
#define FID_CRYPTOLIB_SLICEDECPMUL_RANDOMIZE 0x0c060000UL
#define FID_RANDOMIZE_MXZ 0x0c070000UL
#define FID_CRYPTOLIB_SLICEDMONTNORM 0x0d010000UL
#define FID_MUL_MPU 0x0d020000UL
#define FID_LSET 0x0d030000UL
#define FID_CRYPTOLIB_SLICEDMONTNORM_INIT 0x0d040000UL
#define FID_CRYPTOLIB_SLICEDMONTNORM_CLEAN 0x0d050000UL
#define FID_CRYPTOLIB_SLICEDMONTNORM_FINALIZE 0x0d060000UL
#define FID_CRYPTOLIB_SHA256 0x0e010000UL
#define FID_CRYPTOLIB_SHA256START 0x0e020000UL
#define FID_CRYPTOLIB_SHA256UPDATE 0x0e030000UL
#define FID_CRYPTOLIB_SHA256FINISH 0x0e040000UL
#define FID_CRYPTOLIB_KEYDERIVATIONFUNCTION 0x0e050000UL
#define FID_INT2BIN 0x0e060000UL
#define FID_CRYPTOLIB_KDF_GENERAL 0x0e070000UL
#define FID_CRYPTOLIB_MEMSET 0x0f010000UL
#define FID_CRYPTOLIB_MEMCMP 0x0f020000UL
#define FID_CRYPTOLIB_STRTOUL 0x0f030000UL
#define FID_CRYPTOLIB_GENERATECMAC 0x10010000UL
#define FID_CRYPTOLIB_VERIFYCMAC 0x10020000UL
#define FID_CRYPTOLIB_ENTROPYSOURCE 0x11010000UL
#define FID_CRYPTOLIB_RANDOM 0x11020000UL
#define FID_CRYPTOLIB_GETRANDOM 0x11030000UL
#define FID_CRYPTOLIB_SEED 0x11040000UL
#define FID_CRYPTOLIB_VERIFYRSA_INIT 0x12010000UL
#define FID_CRYPTOLIB_VERIFYRSA 0x12020000UL
#define FID_CRYPTOLIB_VERIFYRSA_CLEAR 0x12030000UL
#define FID_CRYPTOLIB_SLICEDMPIDIVMPI 0x13010000UL
#define FID_INT_DIV_INT 0x13020000UL
#define FID_CRYPTOLIB_SLICEDMPIDIVMPI_PREPARE 0x13030000UL
#define FID_CRYPTOLIB_SLICEDMPIDIVMPI_FINALIZE 0x13040000UL
#define FID_CRYPTOLIB_SLICEDMPIDIVMPI_STEP 0x13050000UL
#define FID_CRYPTOLIB_SLICEDMPIDIVMPI_CLEAN 0x13060000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD 0x14010000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD_P1_PREPARATIONS 0x15010000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD_P2_RR 0x15020000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD_P3_PREPAREWINDOW 0x15030000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD_P4_CALCULATEWINDOW 0x15040000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD_P5_ITERATE 0x15050000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD_P6_PROCESSREMAINS 0x15060000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD_P7_FINALIZE 0x15070000UL
#define FID_CRYPTOLIB_SLICEDMPIMODMPI_STEP 0x15080000UL
#define FID_CRYPTOLIB_SLICEDMONTMUL_STEP 0x15090000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD_MONTGINIT 0x16010000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD_INITCONTEXT 0x16020000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD_CLEARCONTEXT 0x16030000UL
#define FID_CRYPTOLIB_SLICEDMPIEXPMOD_RESETPHASES 0x16040000UL
#define FID_MPIDIVMPI_FINALIZE 0x17010000UL
#define FID_MPIDIVMPI_DIVIDE 0x17020000UL
#define FID_CRYPTOLIB_SLICEDMPIMODMPI 0x17030000UL
#define FID_CRYPTOLIB_SLICEDMONTMUL_INIT 0x18010000UL
#define FID_CRYPTOLIB_SLICEDMONTMUL_CLEAN 0x18020000UL
#define FID_CRYPTOLIB_SLICEDMONTMUL_ITERATE 0x18030000UL
#define FID_MUL_HLP 0x18040000UL
#define FID_SUB_HLP 0x18050000UL
#define FID_CRYPTOLIB_SLICEDMONTMUL_FINALIZE 0x18060000UL
#define FID_CRYPTOLIB_SLICEDMONTMUL 0x18070000UL
#define FID_CRYPTOLIB_SLICEDVERIFYRSA 0x19010000UL
#define FID_ZEROIZE 0x19020000UL
#define FID_MGF_MASK 0x19030000UL
#define FID_PREPARERSA 0x19040000UL
#define FID_PREPAREMD 0x19050000UL
#define FID_HASHIT 0x19060000UL
#define FID_EXPMOD 0x19070000UL
#define FID_CRYPTOLIB_SLICEDVERIFYRSA_CLEAN 0x19080000UL
/**@ \}*/

#endif /* CRYPTOLIB_FID_H */

/**
 * @file  cryptolib_rsa_rsassa_pss_verify.h
 * @brief Function definition for sliced variant of mbedtls_rsassa_pss_verify.
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_RSA_RSASSA_PSS_VERIFY_H
#define CRYPTOLIB_RSA_RSASSA_PSS_VERIFY_H

#ifdef __cplusplus
extern "C" {
#endif

#include "cryptolib_types.h"

#include "mbedtls/rsa.h"
#include "mbedtls/md.h"

/** RSA slicing context */
typedef struct {
    uint8_t buf[MBEDTLS_MPI_MAX_SIZE];  /**< Temporary buffer to hold results over several slices */
    mbedtls_mpi rsaTemp;                /**< Temporary RSA MPI holding the results over several slices */
    size_t siglen;                      /**< Expected length of the signature */
    uint8_t hashlen;                    /**< Length of the input hash digest */
    uint8_t hlen;                       /**< Expected length of the hash digest */
    size_t observedSaltLen;             /**< length of salt */
    size_t msb;                         /**< bitlen of modulus */
    uint8_t* p;                         /**< position pointer for iteration over buf */
    uint8_t* hashStart;                 /**< pointer to hash digest */
    mbedtls_md_context_t md_ctx;        /**< message digest context */

    /** Enum for exp mod sub-statemachine states */
    enum ExpModSlicingState_tag {
        EXPMOD_IDLE = 0, /**< No sliced ExpMod active */
        EXPMOD_ACTIVE = 1 /**< Sliced ExpMod active */
    } ExpModState;   /**< ExpMod slicing state */
} RsaSsaPssSlicingContext_t;

/**
 * Performs a PKCS#1 v2.1 PSS verification (RSASSA-PSS-VERIFY). Sliced variant
 * of mbedtls_rsa_rsassa_pss_verify.
 *
 * @param[out] ctx            Pointer to a RSA public key
 * @param[in]  mode           RSA mode: MBEDTLS_RSA_PUBLIC or MBEDTLS_RSA_PRIVATE
 * @param[in]  md_alg         MD algorithm: MBEDTLS_MD_XXX
 * @param[in]  hashlen        length of the provided hash
 * @param[in]  hash           buffer holding the message digest
 * @param[out] sig            buffer holding the ciphertext
 * @param[in] slicing           0 - SLICE_INIT: first step of the validation<br>
 *                              1 - SLICE_CONTINUE: during validation<br>
 *                            254 - SLICE_RESET: reset slicing state machines<br>
 *                            255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                     0 - CRYPTO_ERR_SUCCESS: computation successful<br>
 *                             1 - CRYPTO_ERR_INVALID: verification failed<br>
 *                             2 - CRYPTO_ERR_CALLAGAIN: computation incomplete, call again<br>
 *                             6 - CRYPTO_ERR_SIGNATURE_INVALID: signature is invalid<br>
 *                             7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                            12 - CRYPTO_ERR_BAD_INPUT: e.g. wrong slicing parameter<br>
 *                            14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                            15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 *                            16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
uint8_t CryptoLib_SlicedVerifyRsa(mbedtls_rsa_context* ctx,
                                  int32_t mode,
                                  mbedtls_md_type_t md_alg,
                                  uint32_t hashlen,
                                  const uint8_t* hash,
                                  const uint8_t* sig,
                                  CryptoLib_Slicing_t slicing);

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_RSA_RSASSA_PSS_VERIFY_H */

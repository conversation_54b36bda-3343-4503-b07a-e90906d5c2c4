/**
 * @file  cryptolib.h
 * @brief Interfaces of the CryptoLib
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON>lzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#ifndef CRYPTOLIB_H
#define CRYPTOLIB_H

#if !defined(CRYPTOLIB_CONFIG_FILE)
#include "cryptolib/cryptolib_config.h"
#else
#include CRYPTOLIB_CONFIG_FILE
#endif

#include "cryptolib/cryptolib_types.h"

#include "cryptolib/cryptolib_ecdh.h"
#include "cryptolib/cryptolib_rsa.h"
#include "cryptolib/cryptolib_crt.h"
#include "cryptolib/cryptolib_crl.h"
#include "cryptolib/cryptolib_hash.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_mac.h"
#include "cryptolib/cryptolib_rng.h"
#include "cryptolib/cryptolib_keys.h"
#include "cryptolib/cryptolib_utils.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This function initializes the CryptoLib.
 *
 * @return          0 - CRYPTO_ERR_SUCCESS: Initialization successfully completed<br>
 */
uint8_t CryptoLib_Init(void);

#if (CRYPTOLIB_ALT_INIT == ENABLED)

/**
 * This user-defined function can be used to perform additional initialization steps during library initialization.
 *
 * See @ref CRYPTOLIB_ALT_INIT for details.
 */
uint8_t CryptoLib_Alt_Init(void);

/**
 * This user-defined function can be used to perform additional deinitialization steps during library deinitialization.
 */
uint8_t CryptoLib_Alt_Deinit(void);

#endif /* CRYPTOLIB_ALT_INIT == ENABLED */

#ifdef __cplusplus
}
#endif

#endif /* CRYPTOLIB_H */

/**
 * @file  src/cryptolib_x509.c
 * @brief Certificate parser
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_x509.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"
#include "cryptolib/cryptolib_crt.h"
#include "cryptolib/cryptolib_common.h"
#include "cryptolib/cryptolib_hash.h"

#include "mbedtls/pk_internal.h"
#include "mbedtls/x509.h"
#include "mbedtls/oid.h"
#include "mbedtls/platform.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

/**@ \{*/
#define ASN1_SET_ELEM3 3u
/**@ \}*/

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Extracts an attribute, i.e. its type and value, from the current byte stream.
 * The data is stored in a mbedtls_x509_name buffer.
 *
 * @param[in,out] p     Pointer to the current position in the byte encoded certificate
 * @param[in]     end   Pointer to the end of the current element
 * @param[out]    cur   Pointer to the mbedtls_x509_name buffer, that gets filled
 *
 * @return              0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                      1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                      14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetAttrTypeValue(uint8_t** p,
                                                  const uint8_t* end,
                                                  mbedtls_x509_name* cur);

/**
 * Extracts the value from a mbedtls_x509_name buffer, allocates memory for
 * the destination string and copies the value from the buffer to the string.
 *
 * @param[out] destination  Pointer to the char array, to which the value should be copied
 * @param[in]  source       Pointer to the mbedtls_x509_name buffer
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetValueFromOidField(CRYPTOLIB_HUGE char** destination,
                                                      const mbedtls_x509_name* const source);

/**
 * Reads the first tag and it's length from the certificate and sets the crtEnd
 * pointer, to the end of the certificate (including the signature).
 *
 * In detail the meta data of the first sequence of the certificate is parsed,
 * from which the length of the certificate is read.
 *
 * Certificate  ::=  SEQUENCE  {
 *     ...
 * }
 *
 * @param[in, out] p        Pointer to the position inside
 * @param[in]      end      Pointer to the end of the input buffer
 * @param[out]     crtEnd   Pointer to the end of the certificate inside the input buffer
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetCertificate(uint8_t** p,
                                                const uint8_t* end,
                                                uint8_t** crtEnd);

/**
 * Reads the first sub-child of the root certificate sequence, i.e. everything
 * that is included to compute the signature (TBS = to be signed). As for all
 * functions the current position pointer is moved, but also the end pointer is
 * set to the end of the TBSCertificate sequence. The pointer tbs will point
 * to the start of the tag of the TBSCertificate sequence and tbsLen will hold
 * the length of the sequence.
 *
 * Certificate ::= SEQUENCE  {
 *      TBSCertificate  ::=  SEQUENCE  { ... }
 *      Algorithm ::= ...
 *      Signature ::= ...
 * }
 *
 * @param[in,out] p         Pointer to the current position pointer
 * @param[in,out] end       Pointer to the end pointer
 * @param[out]    tbs       Pointer to the pointer pointing to the start of the TBS sequence
 * @param[out]    tbsLen    Pointer to an integer that holds the lenght of the TBS sequence
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetTBS(uint8_t** p, uint8_t** end,
                                        uint8_t** tbs, uint16_t* tbsLen);

/**
 * Extracts the value of the version integer field in the certificate.
 *
 * @param[in,out] p         Pointer to the current position pointer
 * @param[in]     end       Pointer to the end of the current sequence
 * @param[out]    ver       Pointer to the integer holding the version number
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: verification successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetVersion(uint8_t** p,
                                            const uint8_t* end,
                                            uint8_t* ver);

/**
 * Extracts the serial number and the length of the serial number. The value of
 * the serial number is copied to the given location.
 *
 * @param[in,out] p         Pointer to the current position pointer
 * @param[in]     end       Pointer to the end of the current sequence
 * @param[out]    serial    Array in which the serial number should be stored
 * @param[out]    serialLen Pointer to the integer in whicht the length of the
 *                          serial number is stored
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetSerial(uint8_t** p, const uint8_t* end,
                                           uint8_t* serial, uint8_t* serialLen);

/**
 * This function reads the tag as well as the length of the sequence that should
 * hold the information about the used signing algorithm. It then moves to the
 * current position pointer by the length and therefore just skips the readout
 * of the algorithm.
 *
 * @param[in,out] p     Pointer to the current position pointer
 * @param[in]     end   Pointer to the end of the current sequence
 *
 * @return              0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                      1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                      14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetAlg(uint8_t** p, const uint8_t* end);

/**
 * Extract a distinguished name from the certificate, this can either be the subject or
 * the issuer fields. The function loops through all child elements of the expected
 * root SET element and stores the values of any of the following attributes in the
 * given struct: country, organization, organizational unit, common name and
 * pseudonym.
 *
 * @param[in,out] p         Pointer to the current position pointer
 * @param[in]     end       Pointer to the expected end of the SET
 * @param[out]    subject   Pointer to the struct that will hold any found value
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetDN(uint8_t** p, const uint8_t* end, struct subject_t* subject);

/**
 * Uses CryptoLib_X509_GetDN to fill the given subject_t struct with the data
 * that was found in the subject set.
 *
 * @param[in,out] p         Pointer to the current position pointer
 * @param[in]     end       Pointer to the expected end of the subject set
 * @param[out]    subj      Pointer to the struct that will hold the found values
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetSubject(uint8_t** p, const uint8_t* end, struct subject_t* subj);

/**
 * Extracts a generalized time or UTC time from the DER byte stream and copies
 * it into the string timeOut. Memory is allocated to hold the string. The value
 * is null terminated, so that the length can be determined by using strlen.
 * If a UTC time is detected the year is appended with the century to make it
 * four digits long.
 *
 * @param[in,out] p         Pointer to the current position pointer
 * @param[in]     end       Pointer to the expected end of the time field
 * @param[out]    timeOut   Pointer to a string to which the time gets populated to
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetTime(uint8_t** p, const uint8_t* end, char** timeOut);

/**
 * Get the validity from the certificate, i.e. parse the time for not before and
 * not after and store the values in the given struct validity_t. The strings
 * that are stored in validity have the format "YYYYMMddHHmmssZ".
 *
 * @param[in,out] p         Pointer to the current position pointer
 * @param[in]     end       Pointer to the expected end of the validity sequence
 * @param[out]    validity  Pointer to a validity_t struct
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetValidity(uint8_t** p, const uint8_t* end, struct validity_t* validity);

/**
 * Uses CryptoLib_X509_GetDN to fill the given issuer_t struct with the data
 * that was found in the issuer set.
 *
 * @param[in,out] p         Pointer to the current position pointer
 * @param[in]     end       Pointer to the expected end of the subject set
 * @param[out]    issuer    Pointer to the struct that will hold the found values
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetIssuer(uint8_t** p, const uint8_t* end, struct issuer_t* issuer);

/**
 * Extracts information about the EC key from the mbedtls_pk_context and store
 * them in the subjectPublicKey_t.
 *
 * @param[in]  pk       Pointer to the mbedtls_pk_context
 * @param[out] info     Pointer to the subjectPublicKey_t struct
 *
 * @return              0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                      1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                      14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetPublicKeyEc(const mbedtls_pk_context* const pk, struct subjectPublicKey_t* info);

/**
 * Extracts information about the RSA key from the mbedtls_pk_context and store
 * them in the subjectPublicKey_t.
 *
 * @param[in]  pk       Pointer to the mbedtls_pk_context
 * @param[out] info     Pointer to the subjectPublicKey_t struct
 *
 * @return              0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                      1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                      14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetPublicKeyRsa(const mbedtls_pk_context* const pk, struct subjectPublicKey_t* info);

/**
 * Parses informationen about the public key of the certificate and stores the
 * result in the given subjectPublicKey_t info.
 *
 * @param[in,out] p         Pointer to the current position pointer
 * @param[in]     end       Pointer to the end of the public key sequence
 * @param[out]    info      Pointer to the subjectPublicKey_t struct
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetSubjectPublicKeyInfo(uint8_t** p, const uint8_t* end, struct subjectPublicKey_t* info);

/**
 * Reads the bool or integer that denotes whether basic constraints are set and
 * stores it in the given byte.
 *
 * @param[in,out] p                 Pointer to the current position pointer
 * @param[in]     end               Pointer to the expected end of the basic constraints content.
 * @param[out]    basicConstraints  Pointer to the byte that is written.
 * @param[out]    maxPathlen        Pointer to the integer to which the max pathlen is written. 0 if not defined
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                                  1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetBasicConstraints(uint8_t** p, const uint8_t* end, uint8_t* basicConstraints, uint16_t* maxPathlen);

/**
 * Reads the bitstring of the key usage content and converts it into a byte.
 *
 * @param[in,out] p         Pointer to the current position pointer.
 * @param[in]     end       Pointer to the expected end of the key usage content.
 * @param[out]    keyUsage  Pointer to byte to store key usage flags in.
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetKeyUsage(uint8_t** p, const uint8_t* end, uint8_t* keyUsage);

/**
 * Loops through all childs of the extension set and either processes the contents
 * (for basic constraints and key usage) or steps over them (all other extensions).
 *
 * @param[in,out] p     Pointer to the current position pointer.
 * @param[in]     end   Pointer to the expected end of the extensions set.
 * @param[out]    ext   Pointer to the extensions_t struct.
 *
 * @return              0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                      1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                      14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetExtensionsSub(uint8_t** p, const uint8_t* end, struct extensions_t* ext);

/**
 * Parses the information from the extensions set. Only the extensions basic constraints
 * and key usage are considered, all other types are ignored. The result is stored
 * in the given extensions_t struct.
 * This function actually only parses the tag and length of the set to call
 * CryptoLib_X509_GetExtensionsSub for further digging.
 *
 * @param[in,out] p     Pointer to the current position pointer.
 * @param[in]     end   Pointer to the expected end of the extensions set.
 * @param[out]    ext   Pointer to the extensions_t struct.
 *
 * @return              0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                      1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                      14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetExtensions(uint8_t** p, const uint8_t* end, struct extensions_t* ext);

/**
 * Parses the signature algorithm as well as the signature value from the certificate
 * and stores it in the given signature_t struct.
 *
 * @param[in,out] p             Pointer to the current position pointer.
 * @param[in]     end           Pointer to the end of the signature element.
 * @param[out]    signature     Pointer to the signature_t struct.
 *
 * @return                      0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                              1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                              14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_GetSignature(uint8_t** p, const uint8_t* end, struct signature_t* signature);

/**
 * Convert mbedtls_x509_buf information into a signature_t struct. Only the oid,
 * the description of the oid and the signature value are extracted.
 *
 * @param[out] signature    Pointer to the signature_t struct where the data should be stored in.
 * @param[in]  oid          Pointer to the mbedtls_x509_buf holding the oid data.
 * @param[in]  sig          Pointer to the mbedtls_x509_buf holding the signature data.
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: one or more invalid parameters passed<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: Memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_X509_ConvertToSignatureStruct(struct signature_t* signature,
                                                          const mbedtls_x509_buf* oid, const mbedtls_x509_buf* sig);

/*
 * Function definitions
 * ----------------------------------------------------------------------------
 */

STATICFCN uint8_t CryptoLib_X509_GetAttrTypeValue(uint8_t** p,
                                                  const uint8_t* end,
                                                  mbedtls_x509_name* cur)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t len;
    mbedtls_x509_buf* oid;
    mbedtls_x509_buf* val;

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_asn1_get_tag(p, end, &len, (int16_t)((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE))) != 0)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_NAME + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETATTRTYPEVALUE | 0x00000001UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
        oid = &cur->oid;
        oid->tag = (int16_t)(**p);

        /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((mret = mbedtls_asn1_get_tag(p, end, &oid->len, MBEDTLS_ASN1_OID)) != 0)
        {
            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_NAME + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETATTRTYPEVALUE | 0x00000003UL));
        }
        else
        {
            oid->p = *p;
            *p += oid->len;

            if ((**p != (uint8_t)MBEDTLS_ASN1_BMP_STRING) && (**p != (uint8_t)MBEDTLS_ASN1_UTF8_STRING) &&
                (**p != (uint8_t)MBEDTLS_ASN1_T61_STRING) && (**p != (uint8_t)MBEDTLS_ASN1_PRINTABLE_STRING) &&
                (**p != (uint8_t)MBEDTLS_ASN1_IA5_STRING) && (**p != (uint8_t)MBEDTLS_ASN1_UNIVERSAL_STRING) &&
                (**p != (uint8_t)MBEDTLS_ASN1_BIT_STRING))
            {
                CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_NAME + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG, (uint32_t)(FID_CRYPTOLIB_X509_GETATTRTYPEVALUE | 0x00000005UL));
            }
            else
            {
                val = &cur->val;
                val->tag = (int16_t)**p;
                (*p)++;

                /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
                if ((mret = mbedtls_asn1_get_len(p, end, &val->len)) != 0)
                {
                    CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_NAME + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETATTRTYPEVALUE | 0x00000006UL));
                }
                else
                {
                    val->p = *p;
                    *p += val->len;

                    cur->next = NULL;
                }
            }
        }
        /* polyspace-end */
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetValueFromOidField(CRYPTOLIB_HUGE char** destination,
                                                      const mbedtls_x509_name* const source)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    /* polyspace +1 MISRA-C3:11.5 [No action planned:Low] "Conversion from pointer to void to pointer to char does not result in undefined behaviour here." */
    *destination = mbedtls_calloc((size_t)1, source->val.len + (size_t)1);
    if (*destination != NULL)
    {
        (void)memset(*destination, 0, source->val.len + (size_t)1);
        /* polyspace +1 MISRA-C3:21.15 [Not a defect:low] "src is pointing to a pointer type as wide as dest" */
        (void)memcpy(*destination, source->val.p, source->val.len);
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_X509_GETVALUEFROMOIDFIELD | 0x00000001UL));
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetCertificate(uint8_t** p,
                                                const uint8_t* end,
                                                uint8_t** crtEnd)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t len;

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_asn1_get_tag(p, end, &len, (int16_t)((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE))) != 0)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_FORMAT + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETCERTIFICATE | 0x00000001UL));
    }
    else
    {
        /* polyspace +1 MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
        *crtEnd = *p + len;
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetTBS(uint8_t** p, uint8_t** end,
                                        uint8_t** tbs, uint16_t* tbsLen)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t len;
    ptrdiff_t d;

    /*
     * TBSCertificate  ::=  SEQUENCE  {
     */
    *tbs = *p;

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_asn1_get_tag(p, *end, &len, (int16_t)((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE))) != 0)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_FORMAT + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETTBS | 0x00000003UL));
    }
    else
    {
        /* polyspace +1 MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
        *end = *p + len;
        d = *end - *tbs;
        *tbsLen = (uint16_t)d;
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetVersion(uint8_t** p,
                                            const uint8_t* end,
                                            uint8_t* ver)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    const uint8_t* localEnd;
    int32_t mret;
    int16_t localVer;
    size_t len;

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_asn1_get_tag(p, end, &len, (int16_t)((uint8_t)MBEDTLS_ASN1_CONTEXT_SPECIFIC | (uint8_t)MBEDTLS_ASN1_CONSTRUCTED))) != 0)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_FORMAT + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETVERSION | 0x00000001UL));
        *ver = 0;
    }
    else
    {
        /* polyspace +1 MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
        localEnd = *p + len;

        /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((mret = mbedtls_asn1_get_int(p, localEnd, &localVer)) != 0)
        {
            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_VERSION + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETVERSION | 0x00000002UL));
        }
        else if (*p != localEnd)
        {
            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_VERSION + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH, (uint32_t)(FID_CRYPTOLIB_X509_GETVERSION | 0x00000003UL));
        }
        else if ((localVer < 0) || (localVer > 2))
        {
            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_UNKNOWN_VERSION, (uint32_t)(FID_CRYPTOLIB_X509_GETVERSION | 0x00000004UL));
        }
        else
        {
            *ver = ((uint8_t)localVer + 1u);
        }
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetSerial(uint8_t** p, const uint8_t* end,
                                           uint8_t* serial, uint8_t* serialLen)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t len;

    if ((ptrdiff_t)(end - *p) < 1)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_SERIAL + MBEDTLS_ERR_ASN1_OUT_OF_DATA, (uint32_t)(FID_CRYPTOLIB_X509_GETSERIAL | 0x00000001UL));
    }
    else if ((**p != ((uint8_t)MBEDTLS_ASN1_CONTEXT_SPECIFIC | (uint8_t)MBEDTLS_ASN1_PRIMITIVE | (uint8_t)MBEDTLS_ASN1_INTEGER)) &&
             (**p != (uint8_t)MBEDTLS_ASN1_INTEGER))
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_SERIAL + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG, (uint32_t)(FID_CRYPTOLIB_X509_GETSERIAL | 0x00000002UL));
    }
    else
    {
        /* move to next byte, p is still pointing to the valid tag of 1 byte */
        (*p)++;

        /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((mret = mbedtls_asn1_get_len(p, end, &len)) != 0)
        {
            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_SERIAL + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETSERIAL | 0x00000003UL));
        }
        else if (len > (size_t)X509_SERIAL_LEN)
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_X509_GETSERIAL | 0x00000004UL));
        }
        else
        {
            (void)memset(serial, 0, (size_t)X509_SERIAL_LEN);
            (void)memcpy(serial, *p, len);
            *serialLen = (uint8_t)len;
            *p += len; /* polyspace MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
        }
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetAlg(uint8_t** p, const uint8_t* end)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t len;

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_asn1_get_tag(p, end, &len, (int16_t)((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE))) != 0)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_FORMAT + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETALG | 0x00000001UL));
    }
    else
    {
        /* polyspace +1 MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
        *p += len;
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetDN(uint8_t** p, const uint8_t* end, struct subject_t* subject)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t setLen;
    const uint8_t* endSet;

    /* don't use recursion, we'd risk stack overflow if not optimized */
    while ((ret == (uint8_t)CRYPTO_ERR_SUCCESS) && (*p < end))
    {
        /*
         * parse SET
         */
        /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((mret = mbedtls_asn1_get_tag(p, end, &setLen, (int16_t)((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SET))) != 0)
        {
            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_NAME + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETDN | 0x00000001UL));
        }
        else
        {
            /* polyspace +1 MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
            endSet = *p + setLen;

            while ((ret == (uint8_t)CRYPTO_ERR_SUCCESS) && (*p < endSet))
            {
                mbedtls_x509_name cur;
                /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
                if ((ret = CryptoLib_X509_GetAttrTypeValue(p, endSet, &cur)) != (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    /* nop */
                }
                else
                {
                    /* polyspace-begin MISRA-C3:21.15 MISRA-C3:21.16 [Not a defect:low] "The byte size of both types of arguments are identical (unsigend char and char)." */
                    if (memcmp(cur.oid.p, MBEDTLS_OID_AT_ORGANIZATION, (size_t)X509_ATTR_OID_LEN) == 0)
                    {
                        ret = CryptoLib_X509_GetValueFromOidField(&subject->organization, &cur);
                    }
                    else if (memcmp(cur.oid.p, MBEDTLS_OID_AT_ORG_UNIT, (size_t)X509_ATTR_OID_LEN) == 0)
                    {
                        ret = CryptoLib_X509_GetValueFromOidField(&subject->organizationalUnit, &cur);
                    }
                    else if (memcmp(cur.oid.p, MBEDTLS_OID_AT_CN, (size_t)X509_ATTR_OID_LEN) == 0)
                    {
                        ret = CryptoLib_X509_GetValueFromOidField(&subject->commonName, &cur);
                    }
                    else if (memcmp(cur.oid.p, MBEDTLS_OID_AT_COUNTRY, (size_t)X509_ATTR_OID_LEN) == 0)
                    {
                        ret = CryptoLib_X509_GetValueFromOidField(&subject->country, &cur);
                    }
                    else if (memcmp(cur.oid.p, MBEDTLS_OID_AT_PSEUDONYM, (size_t)X509_ATTR_OID_LEN) == 0)
                    {
                        ret = CryptoLib_X509_GetValueFromOidField(&subject->pseudonym, &cur);
                    }
                    else
                    {
                        /* nop */
                    }
                    /* polyspace-end */
                }
            }
        }
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetSubject(uint8_t** p, const uint8_t* end, struct subject_t* subj)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t len;

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_asn1_get_tag(p, end, &len, (int16_t)((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE))) != 0)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_FORMAT + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETISSUER | 0x00000001UL));
    }
    else
    {
        /* polyspace +1 MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
        ret = CryptoLib_X509_GetDN(p, (const uint8_t*)(*p + len), subj);
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetTime(uint8_t** p, const uint8_t* end, char** timeOut)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t len;
    uint8_t yearlen = 0;
    uint8_t tag;

    /* polyspace-begin MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
    if ((ptrdiff_t)(end - *p) < 1)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_OUT_OF_DATA, (uint32_t)(FID_CRYPTOLIB_X509_GETTIME | 0x00000001UL));
    }
    else
    {
        tag = **p;

        if (tag == (uint8_t)MBEDTLS_ASN1_UTC_TIME)
        {
            yearlen = 2;
        }
        else if (tag == (uint8_t)MBEDTLS_ASN1_GENERALIZED_TIME)
        {
            yearlen = 4;
        }
        else
        {
            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG, (uint32_t)(FID_CRYPTOLIB_X509_GETTIME | 0x00000002UL));
        }

        if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
        {
            (*p)++;
            /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
            if ((mret = mbedtls_asn1_get_len(p, end, &len)) != 0)
            {
                CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_DATE + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETTIME | 0x00000003UL));
            }
            else
            {
                const uint8_t minSize = yearlen + 8u;
                if (len < (size_t)minSize)
                {
                    CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_DATE, (uint32_t)(FID_CRYPTOLIB_X509_GETTIME | 0x00000004UL));
                }
                else
                {
                    const uint8_t offset = 4u - yearlen; /* we need offset to handle UTC times */
                    /* polyspace +1 MISRA-C3:11.5 [No action planned:Low] "Conversion from pointer to void to pointer to char does not result in undefined behaviour here." */
                    uint8_t* tmpTime = mbedtls_calloc((size_t)1, offset + len + (size_t)1);

                    if (tmpTime != NULL)
                    {
                        (void)memset(tmpTime, 0, offset + len + (size_t)1);
                        (void)memcpy(&(tmpTime[offset]), *p, len);

                        /* handle UTC time, if year < 50 it's 20xx, else 19xx */
                        if (offset == 2u)
                        {
                            if (tmpTime[offset] < (uint8_t)'5')
                            {
                                tmpTime[0] = (uint8_t)'2';
                                tmpTime[1] = (uint8_t)'0';
                            }
                            else
                            {
                                tmpTime[0] = (uint8_t)'1';
                                tmpTime[1] = (uint8_t)'9';
                            }
                        }

                        *timeOut = (char*)tmpTime;
                    }
                    else
                    {
                        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_X509_GETTIME | 0x00000005UL));
                    }
                    *p += len;
                }
            }
        }
    }
    /* polyspace-end */

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetValidity(uint8_t** p, const uint8_t* end, struct validity_t* validity)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t len;
    const uint8_t* localEnd;

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_asn1_get_tag(p, end, &len, (int16_t)((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE))) != 0)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_DATE + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETVALIDITY | 0x00000001UL));
    }
    else
    {
        /* polyspace +1 MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
        localEnd = *p + len;

        /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((ret = CryptoLib_X509_GetTime(p, localEnd, &validity->notBefore)) != (uint8_t)CRYPTO_ERR_SUCCESS)
        {
        }
        else if ((ret = CryptoLib_X509_GetTime(p, localEnd, &validity->notAfter)) != (uint8_t)CRYPTO_ERR_SUCCESS)
        {
        }
        /* poylspace-end */
        else if (*p != localEnd)
        {
            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_DATE + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH, (uint32_t)(FID_CRYPTOLIB_X509_GETVALIDITY | 0x00000002UL));
        }
        else
        {
            /* nop */
        }
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetIssuer(uint8_t** p, const uint8_t* end, struct issuer_t* issuer)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t len;
    struct subject_t subj = { NULL, NULL, NULL, NULL, NULL };

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_asn1_get_tag(p, end, &len, (int16_t)((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE))) != 0)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_FORMAT + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETISSUER | 0x00000001UL));
    }
    else
    {
        /* polyspace +2 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        /* polyspace +1 MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
        if ((ret = CryptoLib_X509_GetDN(p, (const uint8_t*)(*p + len), &subj)) == (uint8_t)CRYPTO_ERR_SUCCESS)
        {
            issuer->commonName = subj.commonName;
            issuer->country = subj.country;
            issuer->organization = subj.organization;

            /* Free all members of subj, if they're not used above. This prevents
             * possible memory leaks, e.g. pseudonym and OU aren't used in peer
             * certificates but might be present due to inheritance from series
             * subject field */
            if (subj.pseudonym != NULL)
            {
                mbedtls_free(subj.pseudonym);
            }
            if (subj.organizationalUnit != NULL)
            {
                mbedtls_free(subj.organizationalUnit);
            }
        }
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetPublicKeyEc(const mbedtls_pk_context* const pk, struct subjectPublicKey_t* info)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret = 0;
    size_t size = 0;

    const mbedtls_ecp_keypair* const ecCtx = mbedtls_pk_ec(*pk);
    char* tmp;

    (void)mbedtls_mpi_write_string(&ecCtx->Q.X, HEX_RADIX, NULL, (size_t)0, &size);

    /* polyspace +1 MISRA-C3:11.5 [No action planned:Low] "Conversion from pointer to void to pointer to char does not result in undefined behaviour here." */
    info->ecPublicKey = mbedtls_calloc((size_t)1, size);
    if (info->ecPublicKey != NULL)
    {
        /* polyspace +1 MISRA-C3:11.5 [No action planned:Low] "Conversion from pointer to void to pointer to char does not result in undefined behaviour here." */
        tmp = mbedtls_calloc((size_t)1, size);
        if (tmp != NULL)
        {
            mret = mbedtls_mpi_write_string(&ecCtx->Q.X, HEX_RADIX, tmp, size, &size);
            if (mret != 0)
            {
                CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_X509_GETPUBLICKEYEC | 0x00000003UL));
                mbedtls_free(info->ecPublicKey);
            }
            else
            {
                /* mbedtls_mpi_write_string writes the string in big-endian order.
                 * To be consistent for the public key with the private key,
                 * that is expected in little-endian format by CryptoLib_Ecdh
                 * we reverse the order here */
                ret = CryptoLib_ChangeEndiannessString(tmp, info->ecPublicKey, size);
            }
            mbedtls_free(tmp);
        }
        else
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_X509_GETPUBLICKEYEC | 0x00000002UL));
            mbedtls_free(info->ecPublicKey);
        }
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_X509_GETPUBLICKEYEC | 0x00000001UL));
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetPublicKeyRsa(const mbedtls_pk_context* const pk, struct subjectPublicKey_t* info)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret = 0;
    size_t size = 0;
    const mbedtls_rsa_context* const rsaCtx = mbedtls_pk_rsa(*pk);

    /* get the length of the needed buffer by calling mpi_write_string with buffer length of 0 */
    (void)mbedtls_mpi_write_string(&rsaCtx->E, HEX_RADIX, NULL, (size_t)0, &size);

    /* polyspace +1 MISRA-C3:11.5 [No action planned:Low] "Conversion from pointer to void to pointer to char does not result in undefined behaviour here." */
    info->rsaExponent = mbedtls_calloc((size_t)1, size);
    if (info->rsaExponent != NULL)
    {
        mret = mbedtls_mpi_write_string(&rsaCtx->E, HEX_RADIX, info->rsaExponent, size, &size);
        if (mret == 0)
        {
            /* get the length of the needed buffer by calling mpi_write_string with buffer length of 0 */
            (void)mbedtls_mpi_write_string(&rsaCtx->N, HEX_RADIX, info->rsaModulus, (size_t)0, &size);

            /* polyspace +1 MISRA-C3:11.5 [No action planned:Low] "Conversion from pointer to void to pointer to char does not result in undefined behaviour here." */
            info->rsaModulus = mbedtls_calloc((size_t)1, size);
            if (info->rsaModulus != NULL)
            {
                mret = mbedtls_mpi_write_string(&rsaCtx->N, HEX_RADIX, info->rsaModulus, size, &size);
                if (mret != 0)
                {
                    CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_X509_GETPUBLICKEYRSA | 0x00000004UL));
                }
            }
            else
            {
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_X509_GETPUBLICKEYRSA | 0x00000003UL));
            }
        }
        else
        {
            CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_X509_GETPUBLICKEYRSA | 0x00000002UL));
        }
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_X509_GETPUBLICKEYRSA | 0x00000001UL));
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetSubjectPublicKeyInfo(uint8_t** p, const uint8_t* end, struct subjectPublicKey_t* info)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t size;

    mbedtls_pk_context pk;
    mbedtls_pk_init(&pk);

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_pk_parse_subpubkey(p, end, &pk)) != 0)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_X509_GETSUBJECTPUBLICKEYINFO | 0x00000003UL));
    }
    else if ((pk.pk_info->type == MBEDTLS_PK_RSA) || (pk.pk_info->type == MBEDTLS_PK_X25519))
    {
        size = strlen(pk.pk_info->name);

        /* polyspace +1 MISRA-C3:11.5 [No action planned:Low] "Conversion from pointer to void to pointer to char does not result in undefined behaviour here." */
        info->publicKeyAlgorithm = mbedtls_calloc((size_t)1, size + (size_t)1);
        if (info->publicKeyAlgorithm != NULL)
        {
            (void)memcpy(info->publicKeyAlgorithm, pk.pk_info->name, size);
            info->publicKeyAlgorithm[size] = '\0';

            if (pk.pk_info->type == MBEDTLS_PK_RSA)
            {
                info->publicKeyType = CRYPTO_PK_RSA;
                ret = CryptoLib_X509_GetPublicKeyRsa(&pk, info);
            }
            else
            {
                info->publicKeyType = CRYPTO_PK_ECKEY;
                ret = CryptoLib_X509_GetPublicKeyEc(&pk, info);
            }
        }
        else
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_X509_GETSUBJECTPUBLICKEYINFO | 0x00000002UL));
        }
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_X509_GETSUBJECTPUBLICKEYINFO | 0x00000001UL));
    }

    mbedtls_pk_free(&pk);

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetBasicConstraints(uint8_t** p, const uint8_t* end, uint8_t* basicConstraints, uint16_t* maxPathlen)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t len;
    *basicConstraints = 0;
    *maxPathlen = 0;

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_asn1_get_tag(p, end, &len, (int16_t)((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE))) != 0)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETBASICCONSTRAINTS | 0x00000001UL));
    }
    else if (*p == end)
    {
        /* just return, default is 0 */
    }
    else
    {
        int16_t tmpBC = 0;
        /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((mret = mbedtls_asn1_get_bool(p, end, &tmpBC)) != 0)
        {
            if (mret == (int32_t)MBEDTLS_ERR_ASN1_UNEXPECTED_TAG)
            {
                mret = mbedtls_asn1_get_int(p, end, &tmpBC);
                if (mret != 0)
                {
                    CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETBASICCONSTRAINTS | 0x00000002UL));
                }
                else
                {
                    if (tmpBC != 0)
                    {
                        tmpBC = 1;
                    }
                }
            }
            else
            {
                CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETBASICCONSTRAINTS | 0x00000003UL));
            }
        }
        *basicConstraints = (uint8_t)tmpBC;

        if ((ret == (uint8_t)CRYPTO_ERR_SUCCESS) && (*p != end))
        {
            /* pathlen is given */
            int16_t tmpPathlen = 0;
            /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
            if ((mret = mbedtls_asn1_get_int(p, end, &tmpPathlen)) != 0)
            {
                CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETBASICCONSTRAINTS | 0x00000004UL));
            }
            else if (*p != end)
            {
                CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH, (uint32_t)(FID_CRYPTOLIB_X509_GETBASICCONSTRAINTS | 0x00000005UL));
            }
            /* As the pathlen will be increased further below, add a check to avoid signed integer overflows which is undefined behavior */
            else if (tmpPathlen == INT16_MAX)
            {
                CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH, (uint32_t)(FID_CRYPTOLIB_X509_GETBASICCONSTRAINTS | 0x00000006UL));
            }
            else
            {
                if (tmpPathlen < 0)
                {
                    *maxPathlen = 0;
                }
                else
                {
                    (*maxPathlen)++;
                }
            }
        }
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetKeyUsage(uint8_t** p, const uint8_t* end, uint8_t* keyUsage)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    mbedtls_x509_bitstring bs;

    memset(&bs, 0, sizeof(mbedtls_x509_bitstring));

    *keyUsage = 0;

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_asn1_get_bitstring(p, end, &bs)) != 0)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETKEYUSAGE | 0x00000001UL));
    }
    else if (bs.len != 1u)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH, (uint32_t)(FID_CRYPTOLIB_X509_GETKEYUSAGE | 0x00000002UL));
    }
    else
    {
        /* in contrast to the mbedTls implementation we only consider a length of the keyusage of 1 */
        *keyUsage = (uint8_t)bs.p[0];
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetExtensionsSub(uint8_t** p, const uint8_t* end, struct extensions_t* ext)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t len;
    int16_t tmpIC;
    const uint8_t* endExtData;
    uint8_t* endExtOctet;
    const uint16_t basicConstraintId = (uint16_t)1 << 8u; /* see MBEDTLS_X509_EXT_BASIC_CONSTRAINTS */
    const uint16_t keyUsageId = (uint16_t)1u << 2u; /* see MBEDTLS_X509_EXT_KEY_USAGE */

    if (*p >= end)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_INVALID_LENGTH, (uint32_t)(FID_CRYPTOLIB_X509_GETEXTENSIONSSUB | 0x00000001UL));
    }
    else
    {
        while ((*p < end) && (ret == (uint8_t)CRYPTO_ERR_SUCCESS))
        {
            /* polyspace-begin MISRA-C3:D4.6 [No action planned:Low] "Interfacing with mbed TLS requires use of native type int. It is assured that values will fit in at least int16_t." */
            mbedtls_x509_buf extnOid = {0, 0, NULL};
            uint8_t isCritical = 0; /* DEFAULT FALSE */
            int extType = 0;

            /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
            if ((mret = mbedtls_asn1_get_tag(p, end, &len, (int16_t)((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE))) != 0)
            {
                CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETEXTENSIONSSUB | 0x00000002UL));
            }
            else
            {
                /* polyspace-begin MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
                endExtData = *p + len;

                if ((mret = mbedtls_asn1_get_tag(p, end, &extnOid.len, MBEDTLS_ASN1_OID)) != 0)
                {
                    CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETEXTENSIONSSUB | 0x00000003UL));
                }
                else
                {
                    extnOid.tag = MBEDTLS_ASN1_OID;
                    extnOid.p = *p;
                    *p += extnOid.len;
                    tmpIC = 0;

                    if (((mret = mbedtls_asn1_get_bool(p, endExtData, &tmpIC)) != 0) &&
                        (mret != (int32_t)MBEDTLS_ERR_ASN1_UNEXPECTED_TAG))
                    {
                        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETEXTENSIONSSUB | 0x00000005UL));
                    }
                    else if ((mret = mbedtls_asn1_get_tag(p, endExtData, &len,
                                                          (int32_t)MBEDTLS_ASN1_OCTET_STRING)) != 0)
                    {
                        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETEXTENSIONSSUB | 0x00000006UL));
                    }
                    else
                    {
                        isCritical = (uint8_t)tmpIC;
                        endExtOctet = *p + len;

                        if (endExtOctet != endExtData)
                        {
                            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH, (uint32_t)(FID_CRYPTOLIB_X509_GETEXTENSIONSSUB | 0x00000007UL));
                        }
                        else if ((mret = mbedtls_oid_get_x509_ext_type(&extnOid, &extType)) != 0)
                        {
                            /* ext type not supported, continue*/
                            if (isCritical == 1u)
                            {
                                CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_UNEXPECTED_TAG, (uint32_t)(FID_CRYPTOLIB_X509_GETEXTENSIONSSUB | 0x00000008UL));
                            }
                            else
                            {
                                /* unknown but also un-critical extension, just skip it */
                                *p = endExtOctet;
                            }
                        }
                        else if (extType == (int)basicConstraintId)
                        {
                            ret = CryptoLib_X509_GetBasicConstraints(p, endExtOctet, &ext->basicConstraints, &ext->maxPathlen);
                        }
                        else if (extType == (int)keyUsageId)
                        {
                            ret = CryptoLib_X509_GetKeyUsage(p, endExtOctet, &ext->keyUsage);
                        }
                        else
                        {
                            /* ignore current extension, so just jump to the end
                             * of the octet string and sequence */
                            *p = endExtOctet;
                        }
                    }
                }
                /* polyspace-end */ /* pointer arithmetic */
            }
            /* polyspace-end */ /* use of return value */
            /* polyspace-end */ /* use of int */
        }
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetExtensions(uint8_t** p, const uint8_t* end, struct extensions_t* ext)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    size_t extLen;
    size_t len;
    const uint8_t* localEnd;

    /* set default values */
    ext->basicConstraints = 0;
    ext->keyUsage = 0;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_asn1_get_tag(p, end, &extLen, (int16_t)((uint8_t)MBEDTLS_ASN1_CONTEXT_SPECIFIC | (uint8_t)MBEDTLS_ASN1_CONSTRUCTED | ASN1_SET_ELEM3))) != 0)
    {
        /* if we encountered an unexpected tag, there are no extensions, just go on */
        if (mret != MBEDTLS_ERR_ASN1_UNEXPECTED_TAG)
        {
            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETEXTENSIONS | 0x00000001UL));
        }
    }
    else
    {
        /* polyspace-begin MISRA-C3:18.4 [No action planned:Low] "pointer arithmetic is better readable than array indexing in this case" */
        localEnd = *p + extLen;

        if ((mret = mbedtls_asn1_get_tag(p, localEnd, &len, (int16_t)((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE))) != 0)
        {
            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + mret, (uint32_t)(FID_CRYPTOLIB_X509_GETEXTENSIONS | 0x00000002UL));
        }
        else if (localEnd != (*p + len))
        {
            CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_EXTENSIONS + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH, (uint32_t)(FID_CRYPTOLIB_X509_GETEXTENSIONS | 0x00000003UL));
        }
        else
        {
            ret = CryptoLib_X509_GetExtensionsSub(p, localEnd, ext);
        }
        /* polyspace-end */
    }
    /* polyspace-end */

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_ConvertToSignatureStruct(struct signature_t* signature, const mbedtls_x509_buf* oid, const mbedtls_x509_buf* sig)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    const char* value;

    (void)memcpy(signature->algorithmOid, oid->p, (size_t)X509_SIG_OID_LEN);

    /* polyspace +1 MISRA-C3:11.5 [No action planned:Low] "Conversion from pointer to void to pointer to char does not result in undefined behaviour here." */
    signature->value = mbedtls_calloc((size_t)1, sig->len);
    if (signature->value != NULL)
    {
        (void)memcpy(signature->value, sig->p, sig->len);
        signature->length = (uint16_t)sig->len;

        /* mbedtls_oid_get_sig_alg_desc sets value to pointer of internal struct, we want to copy its value */
        mret = mbedtls_oid_get_sig_alg_desc(oid, &value);

        if (mret == 0)
        {
            const size_t len = strlen(value);
            /* polyspace +1 MISRA-C3:11.5 [No action planned:Low] "Conversion from pointer to void to pointer to char does not result in undefined behaviour here." */
            signature->algorithm = mbedtls_calloc((size_t)1, len + (size_t)1);
            if (signature->algorithm != NULL)
            {
                (void)memcpy(signature->algorithm, value, len);
                signature->algorithm[len] = '\0';
            }
            else
            {
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_X509_CONVERTTOSIGNATURESTRUCT | 0x00000004UL));
            }
        }
        else
        {
            CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_X509_CONVERTTOSIGNATURESTRUCT | 0x00000005UL));
        }
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_X509_CONVERTTOSIGNATURESTRUCT | 0x00000006UL));
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_X509_GetSignature(uint8_t** p, const uint8_t* end, struct signature_t* signature)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    mbedtls_x509_buf params, oid, sig;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_x509_get_alg(p, end, &oid, &params)) != 0)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_X509_GETSIGNATURE | 0x00000001UL));
    }
    else if ((mret = mbedtls_x509_get_sig(p, end, &sig)) != 0)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_X509_GETSIGNATURE | 0x00000002UL));
    }
    else if (*p != end)
    {
        CryptoLib_SetError(&ret, MBEDTLS_ERR_X509_INVALID_FORMAT + MBEDTLS_ERR_ASN1_LENGTH_MISMATCH, (uint32_t)(FID_CRYPTOLIB_X509_GETSIGNATURE | 0x00000003UL));
    }
    else
    {
        ret = CryptoLib_X509_ConvertToSignatureStruct(signature, &oid, &sig);
    }
    /* polyspace-end */

    return ret;
}

uint8_t CryptoLib_X509_Parse(CRYPTOLIB_HUGE const uint8_t* const input,
                             const uint16_t inputLen,
                             CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate)
{
    uint8_t retval;

    uint16_t tbsLen = 0;
    uint8_t* p, * end, * crt_end, * tbs;

    /* polyspace +1 MISRA-C3:11.8 [No action planned:Medium] "It is assured that the content of input is not altered in this module." */
    p = input;
    crt_end = &p[inputLen];
    end = crt_end;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((retval = CryptoLib_X509_GetCertificate(&p, end, &crt_end)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else if ((retval = CryptoLib_X509_GetTBS(&p, &end, &tbs, &tbsLen)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else if ((retval = CryptoLib_Sha256(tbs, tbsLen, certificate->mdOfTbs)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else if ((retval = CryptoLib_X509_GetVersion(&p, end, &certificate->version)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else if ((retval = CryptoLib_X509_GetSerial(&p, end, certificate->serial, &certificate->serialLen)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else if ((retval = CryptoLib_X509_GetAlg(&p, end)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else if ((retval = CryptoLib_X509_GetIssuer(&p, end, &certificate->issuer)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else if ((retval = CryptoLib_X509_GetValidity(&p, end, &certificate->validity)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else if ((retval = CryptoLib_X509_GetSubject(&p, end, &certificate->subject)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else if ((retval = CryptoLib_X509_GetSubjectPublicKeyInfo(&p, end, &certificate->subjectPublicKey)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else if ((retval = CryptoLib_X509_GetExtensions(&p, end, &certificate->extensions)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else if ((retval = CryptoLib_X509_GetSignature(&p, crt_end, &certificate->signature)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* nop */
    }
    else
    {
        /* nop */
    }
    /* polyspace-end */

    return retval;
}

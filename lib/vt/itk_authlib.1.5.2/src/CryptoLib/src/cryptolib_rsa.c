/**
 * @file  src/cryptolib_rsa.c
 * @brief RSA function definitions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_rsa.h"
#include "cryptolib/cryptolib_rsa_rsassa_pss_verify.h"
#include "cryptolib/cryptolib_common.h"
#include "cryptolib/cryptolib_hash.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#if (CRYPTOLIB_NO_TIME_SLICING == DISABLED)
#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#endif

#if (CRYPTOLIB_NO_TIME_SLICING == DISABLED)
/** Enum for statemachine states */
typedef enum {
    IDLE = 0, /**< Ready for new calls */
    WORK = 1  /**< Perform sliced rsa calculation */
} RsaSlicingState_t;

/**Struct to hold the RSA context for sliced computing*/
typedef struct {
    mbedtls_rsa_context ctx; /**< mbedtls rsa context */

    /** RSASSA PSS slicing sub-state tag */
    enum {
        RSP_IDLE = 0u,       /**< RSASSA PSS not active */
        RSP_ACTIVE = 1u      /**< RSASSA PSS in slice mode */
    } RsaSsaPssState;        /**< RSASSA PSS slicing sub-state */
} RsaSlicingContext_t;

/*
 * Static variables
 * ----------------------------------------------------------------------------
 */
CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t CTX_INIT_FLAG = 0; /**< Init flag for slicing context */
CRYPTOLIB_MEMORY_SECTION_END

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Function to initialize the context for modular exponentation
 *
 * @param[in,out]  context          pointer to the context structure holding the sliced RSA verify data
 * @param[in]  signatureLen         Length of the signature
 * @param[in]  publicModulus        Pointer to the buffer holding a hex string representation of the RSA modulus
 * @param[in]  publicExponent       Pointer to the buffer holding a hex string representation of the RSA exponent
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: initialization successfully completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 */
STATICFCN uint8_t CryptoLib_VerifyRsa_Init(RsaSlicingContext_t* context,
                                           const uint16_t signatureLen,
                                           const char* publicModulus,
                                           const char* publicExponent);

/**
 * Clears an RsaSlicingContext_t
 *
 * @param[in,out] context       pointer to context
 */
STATICFCN void CryptoLib_VerifyRsa_Clear(RsaSlicingContext_t* context);

/*
 * Function definitions
 * ----------------------------------------------------------------------------
 */
STATICFCN uint8_t CryptoLib_VerifyRsa_Init(RsaSlicingContext_t* context,
                                           const uint16_t signatureLen,
                                           const char* publicModulus,
                                           const char* publicExponent)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t ret_mbedtls;

    /* prepare rsa context */
    mbedtls_rsa_init(&context->ctx, MBEDTLS_RSA_PKCS_V21, (int32_t)MBEDTLS_MD_SHA256);

    CTX_INIT_FLAG = 1;

    mbedtls_mpi_init(&context->ctx.N);
    mbedtls_mpi_init(&context->ctx.E);
    context->ctx.len = signatureLen;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((ret_mbedtls = mbedtls_mpi_read_string(&context->ctx.N, HEX_RADIX, publicModulus)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA_INIT | 0x00000001UL));
    }
    else if ((ret_mbedtls = mbedtls_mpi_read_string(&context->ctx.E, HEX_RADIX, publicExponent)) != (int32_t)CRYPTO_ERR_SUCCESS)
    /* polyspace-end */
    {
        CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA_INIT | 0x00000002UL));
    }
    else
    {
        /* nop */
    }

    return ret;
}

STATICFCN void CryptoLib_VerifyRsa_Clear(RsaSlicingContext_t* context)
{
    if (CTX_INIT_FLAG == 1u)
    {
        mbedtls_rsa_free(&context->ctx);
    }

    if (context->RsaSsaPssState != RSP_IDLE)
    {
        (void)CryptoLib_SlicedVerifyRsa(NULL,
                                        MBEDTLS_RSA_PUBLIC,
                                        MBEDTLS_MD_SHA256,
                                        0,
                                        NULL,
                                        NULL,
                                        SLICE_RESET);
    }

    (void)memset(context, 0, sizeof(RsaSlicingContext_t));
}

#endif

uint8_t CryptoLib_VerifyRsa(CRYPTOLIB_HUGE const uint8_t* hash,
                            const uint8_t hashLen,
                            CRYPTOLIB_HUGE const uint8_t* signature,
                            const uint16_t signatureLen,
                            CRYPTOLIB_HUGE const char* publicModulus,
                            CRYPTOLIB_HUGE const char* publicExponent,
                            const CryptoLib_Slicing_t slicing)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

#if CRYPTOLIB_NO_TIME_SLICING == 1
    int32_t ret_mbedtls;

    if ((hash == NULL) || (signature == NULL) || (publicModulus == NULL) || (publicExponent == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x00000001UL));
    }
    else if (slicing != SLICE_NO_SLICING)
    {
        /* invalid slicing parameter */
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x00000002UL));
    }
    else
    {
        /* prepare rsa context */
        mbedtls_rsa_context rsaContext;
        mbedtls_rsa_init(&rsaContext, MBEDTLS_RSA_PKCS_V21, (int32_t)MBEDTLS_MD_SHA256);
        mbedtls_mpi_init(&rsaContext.N);
        mbedtls_mpi_init(&rsaContext.E);
        rsaContext.len = signatureLen;

        /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((ret_mbedtls = mbedtls_mpi_read_string(&rsaContext.N, HEX_RADIX, publicModulus)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x00000003UL));
        }
        else if ((ret_mbedtls = mbedtls_mpi_read_string(&rsaContext.E, HEX_RADIX, publicExponent)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x00000004UL));
        }
        else if ((ret_mbedtls = mbedtls_rsa_rsassa_pss_verify(&rsaContext,
                                                              NULL,
                                                              NULL,
                                                              MBEDTLS_RSA_PUBLIC,
                                                              MBEDTLS_MD_SHA256,
                                                              hashLen,
                                                              hash,
                                                              signature)) != (int32_t)CRYPTO_ERR_SUCCESS)
        /* polyspace-end */
        {
            if (ret_mbedtls == MBEDTLS_ERR_MPI_ALLOC_FAILED)
            {
                CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x00000005UL));
            }
            else
            {
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_SIGNATURE_INVALID, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x00000006UL));
            }
        }
        else
        {
            /* nop */
        }

        mbedtls_rsa_free(&rsaContext);
    }
#else
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static RsaSlicingState_t state = IDLE;
    static RsaSlicingContext_t context;
    CRYPTOLIB_MEMORY_SECTION_END

    CryptoLib_Slicing_t slc = slicing;

    if (slc == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((publicModulus == NULL) || (publicExponent == NULL) ||
             (hash == NULL) || (signature == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x00000007UL));
    }
    else
    {
        switch (state)
        {
            case IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("r_i,");
                #endif
                if (slc == SLICE_INIT)
                {
                    ret = CryptoLib_VerifyRsa_Init(&context, signatureLen, publicModulus, publicExponent);

                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* initialization successful goto next slice */
                        state = WORK;
                        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x00000008UL));
                }
                break;

            case WORK:
                if (slc == SLICE_CONTINUE)
                {
                    if (context.RsaSsaPssState == RSP_IDLE)
                    {
                        ret = CryptoLib_SlicedVerifyRsa(&context.ctx, MBEDTLS_RSA_PUBLIC, MBEDTLS_MD_SHA256, hashLen, hash, signature, SLICE_INIT);

                        if (ret == (uint8_t)CRYPTO_ERR_CALLAGAIN)
                        {
                            context.RsaSsaPssState = RSP_ACTIVE;
                        }
                    }
                    else if (context.RsaSsaPssState == RSP_ACTIVE)
                    {
                        ret = CryptoLib_SlicedVerifyRsa(&context.ctx, MBEDTLS_RSA_PUBLIC, MBEDTLS_MD_SHA256, hashLen, hash, signature, SLICE_CONTINUE);

                        if (ret != (uint8_t)CRYPTO_ERR_CALLAGAIN)
                        {
                            context.RsaSsaPssState = RSP_IDLE;
                        }
                    }
                    else
                    {
                        /* LCOV_EXCL_START: internal enum out of bounds */
                        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x0000000aUL));
                        /* LCOV_EXCL_STOP */
                    }

                    /* Consolidate processing failures in this state to signature errors in order to mimic the non sliced behaviour*/
                    if ((ret != (uint8_t)CRYPTO_ERR_CALLAGAIN) && (ret != (uint8_t)CRYPTO_ERR_SUCCESS) && (ret != (uint8_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS))
                    {
                        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_SIGNATURE_INVALID, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x0000000eUL));
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x0000000cUL));
                }
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_VERIFYRSA | 0x0000000dUL));
                break;
                /* LCOV_EXCL_STOP */
        }
    }

    if (ret != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_VerifyRsa_Clear(&context);
        state = IDLE;
    }
#endif

    return ret;
}

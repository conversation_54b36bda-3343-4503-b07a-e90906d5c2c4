/**
 * @file   src/cryptolib_ecdh_normalize.c
 * @brief  Montgomery Normalization function definition
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_ecdh_normalize.h"
#include "cryptolib/cryptolib_ecdh_modinv.h"
#include "cryptolib/cryptolib_ecdh_common.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if ((CRYPTOLIB_NO_TIME_SLICING == DISABLED) && (CRYPTOLIB_ALT_ECDH == DISABLED))

/** Enum for statemachine states */
typedef enum {
    IDLE = 0,                 /**< Ready for new calls */
    NORMALIZE_INV_MOD = 1,    /**< Perform first set of instructions */
    NORMALIZE_MUL_MPU = 2,    /**< Perform second set of instructions */
    NORMALIZE_LSET = 3,       /**< Perform third set of instructions */
    NORMALIZE_FINAL = 4       /**< Ready to write output */
} NormalizeSlicingState_t;

/*
 * Static variables
 * ----------------------------------------------------------------------------
 */
CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t MPI_INIT_FLAG_NORM = 0; /**< Init flag for slicing context */
CRYPTOLIB_MEMORY_SECTION_END

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Baseline multiplication: X = A * B  (HAC 14.12).
 *
 * @param[in] grp                  pointer to ecp group
 * @param[in,out] context          pointer to context
 *
 * @return                         0 - CRYPTO_ERR_SUCCESS: context successfully initialized<br>
 *                                 1 - CRYPTO_ERR_INVALID: invalid input paramter<br>
 *                                14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t mul_mpu(NormalizeSlicingContext_t* context, const mbedtls_ecp_group* grp);

/**
 * Set value from integer.
 *
 * @param[in,out] context          pointer to context
 *
 * @return                         0 - CRYPTO_ERR_SUCCESS: context successfully initialized<br>
 *                                14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t lset(NormalizeSlicingContext_t* context);

/**
 * Initializes NormalizeSlicingContext_t data structure.
 *
 * @param[in] pz                   pointer to Z coordinate
 * @param[in] px                   pointer to X coordinate
 * @param[in,out] context          pointer to context
 *
 * @return                         0 - CRYPTO_ERR_SUCCESS: context successfully initialized<br>
 *                                14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedMontNorm_Init(const mbedtls_mpi* pz, const mbedtls_mpi* px, NormalizeSlicingContext_t* context);

/**
 * Frees allocated mpis within context.
 *
 * @param[in,out] context           pointer to context
 */
STATICFCN void CryptoLib_SlicedMontNorm_Clean(NormalizeSlicingContext_t* context);

/**
 * Finalizes data and writes to parent output.
 *
 * @param[out] pz                   pointer to z
 * @param[out] px                   pointer to x
 * @param[in] context               pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: output written<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedMontNorm_Finalize(mbedtls_mpi* pz, mbedtls_mpi* px, const NormalizeSlicingContext_t* context);

/*
 * Static function definitions
 * ----------------------------------------------------------------------------
 */

STATICFCN uint8_t mul_mpu(NormalizeSlicingContext_t* context, const mbedtls_ecp_group* grp)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (mbedtls_mpi_mul_mpi(&context->px, &context->px, &context->pz) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_MUL_MPU | 0x00000001UL));
    }
    else
    {
        retVal = (uint8_t)mod_mul(&context->px, grp);
    }

    return retVal;
}

STATICFCN uint8_t lset(NormalizeSlicingContext_t* context)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (mbedtls_mpi_lset(&context->pz, (mbedtls_mpi_sint)1) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_LSET | 0x00000001UL));
    }

    return retVal;
}

STATICFCN uint8_t CryptoLib_SlicedMontNorm_Init(const mbedtls_mpi* pz, const mbedtls_mpi* px, NormalizeSlicingContext_t* context)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    mbedtls_mpi_init(&context->pz);
    mbedtls_mpi_init(&context->px);

    MPI_INIT_FLAG_NORM = 1;

    if ((mbedtls_mpi_copy(&context->pz, pz) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_copy(&context->px, px) != (int32_t)CRYPTO_ERR_SUCCESS))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTNORM_INIT | 0x00000001UL));
    }

    return retVal;
}

STATICFCN void CryptoLib_SlicedMontNorm_Clean(NormalizeSlicingContext_t* context)
{
    if (MPI_INIT_FLAG_NORM == 1u)
    {
        mbedtls_mpi_free(&context->pz);
        mbedtls_mpi_free(&context->px);
    }

    if (context->InvModState != INV_MOD_IDLE)
    {
        (void)CryptoLib_SlicedModInv(NULL, NULL, NULL, SLICE_RESET);
    }

    (void)memset(context, 0, sizeof(NormalizeSlicingContext_t));
}

STATICFCN uint8_t CryptoLib_SlicedMontNorm_Finalize(mbedtls_mpi* pz, mbedtls_mpi* px, const NormalizeSlicingContext_t* context)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if ((mbedtls_mpi_copy(pz, &context->pz) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_copy(px, &context->px) != (int32_t)CRYPTO_ERR_SUCCESS))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTNORM_FINALIZE | 0x00000001UL));
    }

    return retval;
}

/*
 * Public function definition
 * ----------------------------------------------------------------------------
 */
uint8_t CryptoLib_SlicedMontNorm(const mbedtls_ecp_group* grp,
                                 mbedtls_ecp_point* pointP,
                                 const CryptoLib_Slicing_t slicing)
{
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static NormalizeSlicingState_t state = IDLE;
    static NormalizeSlicingContext_t context;
    CRYPTOLIB_MEMORY_SECTION_END

    CryptoLib_Slicing_t slc = slicing; /* write access to slicing needed */
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slc == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((pointP == NULL) || (grp == NULL))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTNORM | 0x00000001UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("no_i,");
                #endif
                if (slc == SLICE_INIT)
                {
                    CryptoLib_SlicedMontNorm_Clean(&context);
                    retVal = CryptoLib_SlicedMontNorm_Init(&pointP->Z, &pointP->X, &context);

                    /* set to next state if ready, reset in case of errors */
                    if (retVal == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = NORMALIZE_INV_MOD;
                        slc = SLICE_CONTINUE;
                    }
                    else
                    {
                        break;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTNORM | 0x00000002UL));
                    break;
                }
            /* fallthrough */

            case NORMALIZE_INV_MOD:
                if (slc == SLICE_CONTINUE)
                {
                    if (context.InvModState == INV_MOD_IDLE)
                    {
                        retVal = CryptoLib_SlicedModInv(&context.pz,
                                                        &context.pz,
                                                        &grp->P,
                                                        SLICE_INIT);

                        if (retVal == (uint8_t)CRYPTO_ERR_CALLAGAIN)
                        {
                            context.InvModState = INV_MOD_ACTIVE;
                        }
                    }
                    else if (context.InvModState == INV_MOD_ACTIVE)
                    {
                        retVal = CryptoLib_SlicedModInv(&context.pz,
                                                        &context.pz,
                                                        &grp->P,
                                                        SLICE_CONTINUE);

                        if (retVal != (uint8_t)CRYPTO_ERR_CALLAGAIN)
                        {
                            context.InvModState = INV_MOD_IDLE;
                        }
                    }
                    else /* LCOV_EXCL_START: internal enum out of bounds */
                    {
                        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTNORM | 0x00000004UL));
                        /* LCOV_EXCL_STOP */
                    }

                    /* set to next state if ready, reset in case of errors */
                    if (retVal == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* NORMALIZE_INV_MOD done, go to NORMALIZE_MUL_MPU */
                        state = NORMALIZE_MUL_MPU;
                    }
                    else
                    {
                        break;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTNORM | 0x00000005UL));
                    break;
                }
            /* fallthrough */

            case NORMALIZE_MUL_MPU:
                #ifdef BENCHMARK_ACTIVE
                BENCH("no_mm,");
                #endif
                retVal = mul_mpu(&context, grp);

                /* set to next state if ready, reset in case of errors */
                if (retVal == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    state = NORMALIZE_LSET;
                }
                else
                {
                    break;
                }
            /* fallthrough */

            case NORMALIZE_LSET:
                #ifdef BENCHMARK_ACTIVE
                BENCH("no_l,");
                #endif
                retVal = lset(&context);

                /* set to next state if ready, reset in case of errors */
                if (retVal == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    state = NORMALIZE_FINAL;
                }
                else
                {
                    break;
                }
            /* fallthrough */

            case NORMALIZE_FINAL:
                #ifdef BENCHMARK_ACTIVE
                BENCH("no_f,");
                #endif
                retVal = CryptoLib_SlicedMontNorm_Finalize(&pointP->Z, &pointP->X, &context);
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTNORM | 0x0000000bUL));
                break;
                /* LCOV_EXCL_STOP */
        }
    }

    if (retVal != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_SlicedMontNorm_Clean(&context);
        state = IDLE;
    }

    return retVal;
    /* polyspace-end */
}

#endif

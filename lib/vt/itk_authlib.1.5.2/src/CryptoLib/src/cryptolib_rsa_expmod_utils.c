/**
 * @file  src/cryptolib_rsa_expmod_utils.c
 * @brief Function definitions of CryptoLib_SlicedExpMod helper functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON><PERSON>heim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_rsa_expmod_utils.h"
#include "cryptolib/cryptolib_rsa_expmod_subs.h"
#include "cryptolib/cryptolib_common.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if (CRYPTOLIB_NO_TIME_SLICING == DISABLED)

/*
 * Static variables
 * ----------------------------------------------------------------------------
 */
CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t MPI_INIT_FLAG_EXPMOD = 0; /**< Init flag for slicing context */
CRYPTOLIB_MEMORY_SECTION_END

void CryptoLib_SlicedMpiExpMod_MontgInit(mbedtls_mpi_uint* mm,
                                         const mbedtls_mpi* N)
{
    mbedtls_mpi_uint x;
    const mbedtls_mpi_uint m0 = N->p[0];
    uint32_t i;

    x = m0;
    x += ((m0 + (mbedtls_mpi_uint)2) & (mbedtls_mpi_uint)4) << 1;

    for (i = (uint32_t)biL; i >= (uint32_t)8; i /= (uint32_t)2)
    {
        x *= ((mbedtls_mpi_uint)2 - (m0 * x));
    }

    *mm = ~x + (mbedtls_mpi_uint)1;
}

void CryptoLib_SlicedMpiExpMod_InitContext(RsaExpModContext_t* context)
{
    uint8_t i;

    mbedtls_mpi_init(&context->result);
    mbedtls_mpi_init(&context->exponent);
    mbedtls_mpi_init(&context->modulus);
    mbedtls_mpi_init(&context->base);
    mbedtls_mpi_init(&context->internals.RR);
    mbedtls_mpi_init(&context->internals.T);
    mbedtls_mpi_init(&context->internals.WW);

    for (i = 0; i < (uint8_t)EXP_MOD_WINDOW_SIZE; i++)
    {
        mbedtls_mpi_init(&context->internals.window[i]);
    }

    MPI_INIT_FLAG_EXPMOD = (uint8_t)1;
}

void CryptoLib_SlicedMpiExpMod_ClearContext(RsaExpModContext_t* context)
{
    uint8_t i;

    if (MPI_INIT_FLAG_EXPMOD == (uint8_t)1)
    {
        mbedtls_mpi_free(&context->result);
        mbedtls_mpi_free(&context->exponent);
        mbedtls_mpi_free(&context->modulus);
        mbedtls_mpi_free(&context->base);
        mbedtls_mpi_free(&context->internals.T);
        mbedtls_mpi_free(&context->internals.WW);

        for (i = 0; i < (uint8_t)EXP_MOD_WINDOW_SIZE; i++)
        {
            mbedtls_mpi_free(&context->internals.window[i]);
        }
    }

    /* If the full control of internals.RR belongs to rsa_expmod, we need
    * to free it here. Otherwise CryptoLib_VerifyRsa_Clear will do it. */
    if (context->internals.RR_active == 1u)
    {
        mbedtls_mpi_free(&context->internals.RR);
        context->internals.RR_active = 0u;
    }

    (void)memset(context, 0, sizeof(RsaExpModContext_t));
}

void CryptoLib_SlicedMpiExpMod_ResetPhases(void)
{
    (void)CryptoLib_SlicedMpiExpMod_P2_RR(NULL, NULL, SLICE_RESET);
    (void)CryptoLib_SlicedMpiExpMod_P3_PrepareWindow(NULL, SLICE_RESET);
    (void)CryptoLib_SlicedMpiExpMod_P4_CalculateWindow(NULL, SLICE_RESET);
    (void)CryptoLib_SlicedMpiExpMod_P5_Iterate(NULL, SLICE_RESET);
    (void)CryptoLib_SlicedMpiExpMod_P6_ProcessRemains(NULL, SLICE_RESET);
    (void)CryptoLib_SlicedMpiExpMod_P7_Finalize(NULL, SLICE_RESET);
}

#endif

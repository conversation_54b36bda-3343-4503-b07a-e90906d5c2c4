/**
 * @file  src/cryptolib_crl_revocationcheck.c
 * @brief CRL function definitions for checking revocation state of a certificate
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_crl.h"
#include "cryptolib/cryptolib_crl_revocationcheck.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#include "mbedtls/asn1.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Parses the given CRL buffer at the given position p for the expected tag and tries
 * to move the position pointer p to the end of the content of the tag.
 * If the current buffer doesn't hold all of the content of the tag, the number of
 * bytes to be skipped in the next runs is set in the context.
 * If moving the pointer is successful the state is set to nextState.
 *
 * @param[out] p            pointer to position pointer in the current slice/buffer
 * @param[in]  end          pointer to the end of the current buffer
 * @param[in]  tag          tag to be searched for
 * @param[out] len          Length of the block that is skipped
 * @param[out] ctx          pointer to the sliced Crl context
 * @param[out] state        pointer to the state variable
 * @param[in]  nextState    State to which the state variable is set to on success
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: the input CRL is invalid<br>
 *                          2 - CRYPTO_ERR_CALLAGAIN: not enough data in buffer, call again with next slice<br>
 *                          4 - CRYPTO_ERR_INTERNAL: internal error<br>
 */
STATICFCN uint8_t CryptoLib_CertOnCrl_SkipContent(const uint8_t** p, const uint8_t* end, const uint8_t tag, uint32_t* len, CryptoLib_SlicedCrlContext_t* ctx, CryptoLib_CrlRevokeState_t* state, const CryptoLib_CrlRevokeState_t nextState);

/**
 * Checks the given CRL buffer at the given position p for a valid time tag (0x17 or 0x18), if
 * found, moves the position pointer to the end of the content of this tag and sets the state to nextState.
 * If the current buffer/slice doesn't hold all of the content of the tag, the part of the current
 * buffer that is needed to parse the tag is stored in the remaining bytes buffer in the context to
 * be reused in the next run.
 *
 * @param[out] p            pointer to position pointer in the current slice/buffer
 * @param[in]  end          pointer to the end of the current buffer
 * @param[out] ctx          pointer to the sliced Crl context
 * @param[out] state        pointer to the state variable
 * @param[in]  nextState    State to which the state variable is set to on success
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: the input CRL is invalid<br>
 *                          2 - CRYPTO_ERR_CALLAGAIN: not enough data in buffer, call again with next slice<br>
 *                          4 - CRYPTO_ERR_INTERNAL: internal error<br>
 */
STATICFCN uint8_t CryptoLib_CertOnCrl_SkipTime(const uint8_t** p, const uint8_t* end, CryptoLib_SlicedCrlContext_t* ctx, CryptoLib_CrlRevokeState_t* state, const CryptoLib_CrlRevokeState_t nextState);

/**
 * Parses the given CRL buffer at the given position p for the expected tag and tries to move
 * the position pointer right after the tag definition, i.e. to first byte of the content.
 * If the current buffer doesn't hold all of the content of the tag, the number of
 * bytes to be skipped in the next runs is set in the context.
 * If moving the pointer is successful the state is set to nextState.
 *
 * @param[out] p            pointer to position pointer in the current slice/buffer
 * @param[in]  end          pointer to the end of the current buffer
 * @param[in]  tag          tag to be searched for
 * @param[out] len          Length of the content of the tag
 * @param[out] ctx          pointer to the sliced Crl context
 * @param[out] state        pointer to the state variable
 * @param[in]  nextState    State to which the state variable is set to on success
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                          1 - CRYPTO_ERR_INVALID: the input CRL is invalid<br>
 *                          2 - CRYPTO_ERR_CALLAGAIN: not enough data in buffer, call again with next slice<br>
 *                          4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                          12 - CRYPTO_ERR_BAD_INPUT: returned if tag not found but 0xa0<br>
 */
STATICFCN uint8_t CryptoLib_CertOnCrl_MoveAfterTag(const uint8_t** p, const uint8_t* end, const uint8_t tag, uint32_t* len, CryptoLib_SlicedCrlContext_t* ctx, CryptoLib_CrlRevokeState_t* state, const CryptoLib_CrlRevokeState_t nextState);

/**
 * Checks the correct tag for a serial number and attempts to read its content. If successful the found serial number is compared bytewise
 * with the given peerSerialNumber.
 * If the current buffer/slice doesn't hold all of the content of the tag, the part of the current
 * buffer that is needed to parse the tag is stored in the remaining bytes buffer in the context to
 * be reused in the next run.
 *
 * @param[out] p                    pointer to position pointer in the current slice/buffer
 * @param[in]  end                  pointer to the end of the current buffer
 * @param[in]  peerSerialNumber     pointer to buffer holding the serial number that should be checked
 * @param[in]  peerSerialNumberLen  length of the buffer holding the serial number
 * @param[out] ctx                  pointer to the sliced Crl content
 * @param[out] state                pointer to the state variable
 * @param[in]  nextState            State to which the state variable is set to on mismatch
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                                  1 - CRYPTO_ERR_INVALID: the input CRL is invalid<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: not enough data in buffer, call again with next slice<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  5 - CRYPTO_ERR_CERT_ON_CRL: the serial number is revoked<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: returned if tag not found but 0xa0<br>
 */
STATICFCN uint8_t CryptoLib_CertOnCrl_CheckSerial(const uint8_t** p, const uint8_t* end, const uint8_t* peerSerialNumber, const uint8_t peerSerialNumberLen, CryptoLib_SlicedCrlContext_t* ctx, CryptoLib_CrlRevokeState_t* state, const CryptoLib_CrlRevokeState_t nextState);

/**
 * Copies the bytes beginning from start to end into the remainingBytes buffer in the context. Additionally checks
 * whether there is enough space.
 *
 * @param[in]  start        Pointer to the start of the buffer
 * @param[in]  end          Pointer to the end of the buffer
 * @param[out] ctx          Pointer to the sliced Crl context
 * @param[in]  nxt          Enum value that denotes why the bytes should be stored
 *
 * @return                  2 - CRYPTO_ERR_CALLAGAIN: operation successful<br>
 *                          4 - CRYPTO_ERR_INTERNAL: not enough space in destination buffer<br>
 */
STATICFCN uint8_t CryptoLib_CertOnCrl_CopyToRemainingBytes(const uint8_t* start, const uint8_t* end, CryptoLib_SlicedCrlContext_t* ctx, const CryptoLib_CrlNext_t nxt);

/**
 * Checks the validity of the slicing parameter given. If the parameter is invalid an
 * error code other than CRYPTO_ERR_SUCCES is returned. Initializing or resetting
 * variables has to be handled in the main function.
 *
 * @param[in] sliced        Denotes whether a slicing has already started.
 * @param[in] slicing       Slicing parameter.
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: everything's correct<br>
 *                          12 - CRYPTO_ERR_BAD_INPUT: invalid slicing parameter
 */
STATICFCN uint8_t CryptoLib_CertOnCrl_CheckSlicingParameter(const CryptoLib_Slicing_t slicing, const uint8_t sliced);

/**
 * Sets the internally used buffers, i.e. checks for bytes to be skipped, remaining bytes
 * from an earlier call and sets the position pointer p and the end pointer end respectively.
 *
 * @param[out] ctx              Pointer to the sliced Crl context
 * @param[in]  crlBuff          Input buffer
 * @param[in]  crlBuffLen       input buffer len
 * @param[out] p                pointer to the position pointer
 * @param[out] end              pointer to the pointer to the end of the buffer
 * @param[out] len              Pointer to the number of bytes that were used from crlBuff to complete the remainingBytes
 *
 * @return                      0 - CRYPTO_ERR_SUCCES: buffers set successfully<br>
 *                              2 - CRYPTO_ERR_CALLAGAIN: all bytes in crlBuff are skipped<br>
 *                              4 - CRYPTO_ERR_INTERNAL: failed to allocate memory
 *
 */
STATICFCN uint8_t CryptoLib_CertOnCrl_SetBuffers(CryptoLib_SlicedCrlContext_t* ctx, const uint8_t* crlBuff, const uint16_t crlBuffLen, const uint8_t** p, const uint8_t** end, size_t* len);

/**
 * The main state machine handling the walk through the (sliced) CRL.
 *
 * @param[out] p                  Pointer to the position pointer in the input buffer
 * @param[in]  end                Pointer to the end of the input buffer
 * @param[out] ctx                Pointer to the sliced Crl context
 * @param[in]  serialNumber       Pointer to the serial number
 * @param[in]  serialNumberLen    Length of the serial number
 *
 * @return                        0 - CRYPTO_ERR_SUCCESS: verification successfully completed<br>
 *                                1 - CRYPTO_ERR_INVALID: the input CRL is invalid<br>
 *                                2 - CRYPTO_ERR_CALLAGAIN: callagain not finished yet<br>
 *                                4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                5 - CRYPTO_ERR_CERT_ON_CRL: Certificate listed on CRL<br>
 */
STATICFCN uint8_t CryptoLib_CertOnCrl_MSM(const uint8_t** p, const uint8_t* end, CryptoLib_SlicedCrlContext_t* ctx, const uint8_t* serialNumber, const uint8_t serialNumberLen);

/**
 * Internal statemachine to iterate through the entries in the list of revoked certificates.
 *
 * @param[out] ctx                Pointer to the sliced Crl context
 * @param[out] subState           Pointer to the substate of this statemachine
 * @param[in]  serialNumber       Pointer to the serial number
 * @param[in]  serialNumberLen    Length of the serial number
 * @param[out] p                  Pointer to the position pointer
 * @param[in]  end                Pointer to the end of the buffer
 *
 * @return                      0 - CRYPTO_ERR_SUCCESS: operation successfully completed<br>
 *                              1 - CRYPTO_ERR_INVALID: the input CRL is invalid<br>
 *                              2 - CRYPTO_ERR_CALLAGAIN: not enough data in buffer, call again with next slice<br>
 *                              4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                              5 - CRYPTO_ERR_CERT_ON_CRL: the serial number is revoked<br>
 *                              12 - CRYPTO_ERR_BAD_INPUT: returned if tag not found but 0xa0<br>
 */
STATICFCN uint8_t CryptoLib_CertOnCrl_ListStatemachine(CryptoLib_SlicedCrlContext_t* ctx, CryptoLib_CrlRevokeState_t* subState, const uint8_t* serialNumber, const uint8_t serialNumberLen, const uint8_t** p, const uint8_t* end);

/**
 * Resets the sliced Crl context to 0.
 *
 * @param[out] ctx          Pointer to the sliced crl context
 */
STATICFCN void CryptoLib_CertOnCrl_ResetCtx(CryptoLib_SlicedCrlContext_t* ctx);

/*
 * Function definitions
 * ----------------------------------------------------------------------------
 */

STATICFCN uint8_t CryptoLib_CertOnCrl_SkipTime(const uint8_t** p, const uint8_t* end, CryptoLib_SlicedCrlContext_t* ctx, CryptoLib_CrlRevokeState_t* state, const CryptoLib_CrlRevokeState_t nextState)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint8_t timeFormat = 0;
    uint32_t len;

    if ((**p == (uint8_t)MBEDTLS_ASN1_UTC_TIME) || (**p == (uint8_t)MBEDTLS_ASN1_GENERALIZED_TIME))
    {
        timeFormat = **p;
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_SKIPTIME | 0x00000001UL));
    }

    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        ret = CryptoLib_CertOnCrl_SkipContent(p, end, timeFormat, &len, ctx, state, nextState);
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_CertOnCrl_SkipContent(const uint8_t** p, const uint8_t* end, const uint8_t tag, uint32_t* len, CryptoLib_SlicedCrlContext_t* ctx, CryptoLib_CrlRevokeState_t* state, const CryptoLib_CrlRevokeState_t nextState)
{
    int16_t mret;
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    const uint8_t* pOrg = *p;

    if (*p != end)
    {
        mret = CryptoLib_Asn1GetTag(p, end, len, tag);
        if (mret == MBEDTLS_ERR_ASN1_OUT_OF_DATA) /* byte array doesn't contain all needed information */
        {
            ret = CryptoLib_CertOnCrl_CopyToRemainingBytes(pOrg, end, ctx, TAG);
        }
        else if (mret == MBEDTLS_ERR_ASN1_UNEXPECTED_TAG)
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_SKIPCONTENT | 0x00000001UL));
        }
        else if (mret != 0)
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_SKIPCONTENT | 0x00000002UL));
        }
        else
        {
            /* move ctx.pos by length of meta */
            ctx->pos += (uint32_t)(*p - pOrg);
            ctx->pos += *len;

            /* polyspace +1 MISRA-C3:18.4 [No action planned:low] "Pointer arithmetic is used carefully and in an understandable way." */
            if ((*p + *len) >= end)
            {
                /* more bytes need to be skipped than are available in the buffer */
                ctx->bytesToBeSkipped = (uint32_t)(*len - (uint32_t)(end - *p));
                ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                *state = nextState; /* all bytes that belong to this tag will be skipped,
                                     * so the next byte that has to be considered belongs
                                     * to the next tag respectively state -> set state */
            }
            else
            {
                /* polyspace +2 MISRA-C3:18.4 [No action planned:low] "Pointer arithmetic is used carefully and in an understandable way." */
                /* after skipping the bytes there are still bytes to be processed in the buffer */
                *p = *p + *len;
            }
        }

        if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
        {
            *state = nextState;
        }
    }
    else
    {
        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_CertOnCrl_CopyToRemainingBytes(const uint8_t* start, const uint8_t* end, CryptoLib_SlicedCrlContext_t* ctx, const CryptoLib_CrlNext_t nxt)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_CALLAGAIN; /* is only called when the caller should recall */

    const size_t len = (size_t)(end - start);

    if (len < (size_t)INT_BUF_LEN)
    {
        if (ctx->remainingBytes != start)
        {
            /* polyspace-begin MISRA-C3:D4.14 [Not a defect:Low] "The parameter len and its value are checked prior to being used in memcpy." */
            /* make a temp copy first to prevent possible issues with memcpy's
             * source and destination too close to each other */
            uint8_t temp[INT_BUF_LEN];
            (void)memcpy(temp, start, len);
            (void)memcpy(ctx->remainingBytes, temp, len);
            /* polyspace-end */
        }
        ctx->remainingBytesLen = len;
        ctx->next = nxt;
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INTERNAL, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_COPYTOREMAININGBYTES | 0x00000001UL));
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_CertOnCrl_MoveAfterTag(const uint8_t** p, const uint8_t* end, const uint8_t tag, uint32_t* len, CryptoLib_SlicedCrlContext_t* ctx, CryptoLib_CrlRevokeState_t* state, const CryptoLib_CrlRevokeState_t nextState)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int16_t mret;
    const uint8_t* pOrg = *p;

    if (*p != end)
    {
        mret = CryptoLib_Asn1GetTag(p, end, len, tag);
        if (mret == MBEDTLS_ERR_ASN1_OUT_OF_DATA) /* byte array doesn't contain all needed information */
        {
            ret = CryptoLib_CertOnCrl_CopyToRemainingBytes(pOrg, end, ctx, TAG);
        }
        else if (mret == MBEDTLS_ERR_ASN1_UNEXPECTED_TAG)
        {
            if (*pOrg == (uint8_t)ASN1_SET) /* ASN.1 set, index 0; check for the expected tag after the revocation list */
            {
                ret = (uint8_t)CRYPTO_ERR_BAD_INPUT; /* not really bad input, but a distinguishable code for the caller */
            }
            else
            {
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_MOVEAFTERTAG | 0x00000001UL));
            }
        }
        else if (mret != 0)
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_MOVEAFTERTAG | 0x00000002UL));
        }
        else
        {
            ctx->pos += (uint32_t)(*p - pOrg);
            *state = nextState;
        }
    }
    else
    {
        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_CertOnCrl_CheckSerial(const uint8_t** p, const uint8_t* end, const uint8_t* peerSerialNumber, const uint8_t peerSerialNumberLen, CryptoLib_SlicedCrlContext_t* ctx, CryptoLib_CrlRevokeState_t* state, const CryptoLib_CrlRevokeState_t nextState)
{
    uint8_t ret;
    uint32_t len;
    CryptoLib_CrlRevokeState_t dummyState;

    /* we store the current position in the buffer, as we want to
     * have the tag present when we have to recall the function. */
    const uint8_t* startOfInteger = *p;
    ret = CryptoLib_CertOnCrl_MoveAfterTag(p, end, MBEDTLS_ASN1_INTEGER, &len, ctx, &dummyState, SERIAL_NUMBER);

    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        if ((uint32_t)(end - *p) < len)
        {
            /* the integer is longer than the available data, so we store the
             * remaining data and return CALLAGAIN. */
            ctx->pos -= (uint32_t)(*p - startOfInteger); /* move pos back, MoveAfterTag incremented it */
            ret = CryptoLib_CertOnCrl_CopyToRemainingBytes(startOfInteger, end, ctx, SERIAL);
        }
        else
        {
            /* The serial number is completely contained in the remaining data
             * so if the length of the input serial number matches the len of the
             * serial in the CRL compare the next len bytes with the given
             * serial number. */
            if ((len == peerSerialNumberLen) && (memcmp(*p, peerSerialNumber, (size_t)len) == 0))
            {
                /* the numbers match, so the serialNumber is revoked */
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_CERT_ON_CRL, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_CHECKSERIAL | 0x00000001UL));
            }
            else
            {
                ctx->pos += len;

                /* polyspace +3 MISRA-C3:18.4 [No action planned:low] "Pointer arithmetic is used carefully and in an understandable way." */
                /* serial numbers doesn't match. Check if there is more data
                 * in the buffer, if so goto next step else return CALLAGAIN */
                *p = *p + len;
                *state = nextState;
                if ((uint32_t)(end - *p) < 1u)
                {
                    ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                }
            }
        }
    }

    return ret;
}

STATICFCN void CryptoLib_CertOnCrl_ResetCtx(CryptoLib_SlicedCrlContext_t* ctx)
{
    (void)memset(ctx, 0, sizeof(CryptoLib_SlicedCrlContext_t));
}

STATICFCN uint8_t CryptoLib_CertOnCrl_CheckSlicingParameter(const CryptoLib_Slicing_t slicing, const uint8_t sliced)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (sliced == 0u)
    {
        if ((slicing != SLICE_INIT) && (slicing != SLICE_NO_SLICING))
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_CHECKSLICINGPARAMETER | 0x00000001UL));
        }
    }
    else
    {
        if ((slicing != SLICE_CONTINUE) && (slicing != SLICE_FINISH))
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_CHECKSLICINGPARAMETER | 0x00000002UL));
        }
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_CertOnCrl_SetBuffers(CryptoLib_SlicedCrlContext_t* ctx, const uint8_t* crlBuff, const uint16_t crlBuffLen, const uint8_t** p, const uint8_t** end, size_t* len)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    size_t max;
    *len = 0;

    if (ctx->remainingBytesLen > (size_t)0)
    {
        if ((ctx->next == TAG) || (ctx->next == SERIAL))
        {
            if (ctx->next == TAG)
            {
                max = ASN1_MAX_LEN_TAG;
            }
            else
            {
                max = ASN1_MAX_LEN_SERIAL;
            }

            /* calculate number of bytes to be taken from crlBuff */
            *len = max - ctx->remainingBytesLen;
            if (*len > (size_t)crlBuffLen)
            {
                *len = crlBuffLen;
            }
            (void)memcpy(&ctx->remainingBytes[ctx->remainingBytesLen], crlBuff, *len);
            *p = ctx->remainingBytes;
            *end = &ctx->remainingBytes[ctx->remainingBytesLen + *len];

            /* reset remainingBytesLen to indicate that everything from remainingBytes was used,
             * p and end will still point to the correct locations in the buffer. */
            ctx->remainingBytesLen = 0;
        }
        else
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INTERNAL, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_SETBUFFERS | 0x00000001UL));
        }
    }
    /* if more bytes need to be skipped than are available in crlBuff
     * subtract the crlBuffLen from bytesToBeSkipped and indicate to exit this slice. */
    else if (ctx->bytesToBeSkipped >= (uint32_t)crlBuffLen)
    {
        ctx->bytesToBeSkipped -= crlBuffLen;
        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
    }
    else
    {
        *p = &crlBuff[ctx->bytesToBeSkipped];
        *end = &crlBuff[crlBuffLen];
        ctx->bytesToBeSkipped = 0;
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_CertOnCrl_ListStatemachine(CryptoLib_SlicedCrlContext_t* ctx, CryptoLib_CrlRevokeState_t* subState, const uint8_t* serialNumber, const uint8_t serialNumberLen, const uint8_t** p, const uint8_t* end)
{
    uint8_t leave = 0u;
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint32_t len;

    while (leave == 0u)
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (*subState)
        {
            case REVOKED_CERT:
                /* check if we've reached the end of the list */
                if (ctx->pos < ctx->revCertsEnd)
                {
                    ret = CryptoLib_CertOnCrl_MoveAfterTag(p, end, (uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE, &len, ctx, subState, SERIAL_NUMBER);

                    if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* any error or a request for callagain occured, so we return from the function. */
                        leave = 1;
                        break;
                    }
                }
                else
                {
                    /* no more entries, certificate not found */
                    ret = (uint8_t)CRYPTO_ERR_SUCCESS;
                    leave = 1;
                    break;
                }
            /* fallthrough */

            case SERIAL_NUMBER:
                ret = CryptoLib_CertOnCrl_CheckSerial(p, end, serialNumber, serialNumberLen, ctx, subState, REVOCATION_DATE);

                if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    leave = 1;
                    break;
                }
            /* fallthrough */

            case REVOCATION_DATE:
                /* skip the revocation date */
                ret = CryptoLib_CertOnCrl_SkipTime(p, end, ctx, subState, REVOKED_CERT);

                if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    leave = 1;
                }
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                leave = 1;
                *subState = REVOKED_CERT;
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_LISTSTATEMACHINE | 0x00000001UL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_CertOnCrl_MSM(const uint8_t** p, const uint8_t* end, CryptoLib_SlicedCrlContext_t* ctx, const uint8_t* serialNumber, const uint8_t serialNumberLen)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint32_t len = 0;

    /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
    switch (ctx->state)
    {
        case CERT:
            ret = CryptoLib_CertOnCrl_MoveAfterTag(p, end, (uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE, &len, ctx, &ctx->state, TBS);

            if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                break;
            }
        /* fallthrough */

        case TBS:
            ret = CryptoLib_CertOnCrl_MoveAfterTag(p, end, (uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE, &len, ctx, &ctx->state, VERSION);
            ctx->tbsEnd = ctx->pos + len;

            if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                break;
            }
        /* fallthrough */

        case VERSION:
            ret = CryptoLib_CertOnCrl_SkipContent(p, end, (uint8_t)MBEDTLS_ASN1_INTEGER, &len, ctx, &ctx->state, SIG);

            if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                break;
            }
        /* fallthrough */

        case SIG:
            ret = CryptoLib_CertOnCrl_SkipContent(p, end, (uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE, &len, ctx, &ctx->state, ISSUER);

            if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                break;
            }
        /* fallthrough */

        case ISSUER:
            ret = CryptoLib_CertOnCrl_SkipContent(p, end, (uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE, &len, ctx, &ctx->state, THIS_UPDATE);

            if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                break;
            }
        /* fallthrough */

        case THIS_UPDATE:
            ret = CryptoLib_CertOnCrl_SkipTime(p, end, ctx, &ctx->state, NEXT_UPDATE);

            if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                break;
            }
        /* fallthrough */

        case NEXT_UPDATE:
            ret = CryptoLib_CertOnCrl_SkipTime(p, end, ctx, &ctx->state, REVOCATION_LIST);

            if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                break;
            }
        /* fallthrough */

        case REVOCATION_LIST:
            if (ctx->pos < ctx->tbsEnd)
            {
                ctx->subState = REVOKED_CERT;
                ret = CryptoLib_CertOnCrl_MoveAfterTag(p, end, (uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE, &len, ctx, &ctx->state, REVOKED_CERTS);

                /* Found an unexpected tag, but it matches the SET tag that starts the CRL Extensions block,
                 * so there's no revoked certificate on this CRL. */
                if (ret == (uint8_t)CRYPTO_ERR_BAD_INPUT)
                {
                    ret = (uint8_t)CRYPTO_ERR_SUCCESS;
                    break;
                }
                else
                {
                    ctx->revCertsEnd = ctx->pos + len;
                }
            }
            else
            {
                /* empty certificate list, no certificate was revoked yet */
                ret = (uint8_t)CRYPTO_ERR_SUCCESS;
                break;
            }

            if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                break;
            }
        /* fallthrough */

        case REVOKED_CERTS:
            ret = CryptoLib_CertOnCrl_ListStatemachine(ctx, &ctx->subState, serialNumber, serialNumberLen, p, end);
            break;

        default: /* LCOV_EXCL_START: internal enum out of bounds */
            ctx->state = CERT;
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_CERTONCRL_MSM | 0x00000001UL));
            break;
            /* LCOV_EXCL_STOP */
    }
    /* polyspace-end */

    return ret;
}

uint8_t CryptoLib_CheckCertificateOnCrl(CRYPTOLIB_HUGE const uint8_t* const crlBuff,
                                        const uint16_t crlBuffLen,
                                        const CryptoLib_Slicing_t slicing,
                                        CRYPTOLIB_HUGE const uint8_t* serialNumber,
                                        const uint8_t serialNumberLength)
{
    /* context:
     *      bytesToBeSkipped
     *      state
     *      remainingBytes
     */
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static CryptoLib_SlicedCrlContext_t ctx;
    static uint8_t sliced = 0;
    CRYPTOLIB_MEMORY_SECTION_END

    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    size_t offset = 0;
    size_t len = 0;

    if (slicing == SLICE_RESET)
    {
        sliced = 0;
        CryptoLib_CertOnCrl_ResetCtx(&ctx);
    }
    else
    {
        if ((crlBuff != NULL) && (serialNumber != NULL))
        {
            /* PRE: only one of bytesToBeSkipped and remainingBytesLen has a value != 0 */
            const uint8_t* p;
            const uint8_t* end;

            ret = CryptoLib_CertOnCrl_CheckSlicingParameter(slicing, sliced);

            if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                /* slicing parameters are valid, if they equal to INIT or NO_SLICING
                 * we initialize our internal data. */
                if ((slicing == SLICE_INIT) || (slicing == SLICE_NO_SLICING))
                {
                    sliced = 1; /* doesn't do any harm if this is set also while using no slicing */
                    CryptoLib_CertOnCrl_ResetCtx(&ctx);
                    ctx.state = CERT;
                    ctx.subState = REVOKED_CERT;
                }

                /* while there are remainingBytes, the return value is CALLAGAIN and there is still data in the crlBuffLen,
                 * call SetBuffers and the MSM to empty the remainingBytesBuffer slice by slice. */
                ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                while ((ctx.remainingBytesLen != 0u) && (ret == (uint8_t)CRYPTO_ERR_CALLAGAIN) && (((size_t)crlBuffLen - offset) > 0u))
                {
                    /* set p and end */
                    ret = CryptoLib_CertOnCrl_SetBuffers(&ctx, &crlBuff[offset], crlBuffLen - (uint16_t)offset, &p, &end, &len);
                    offset += len;

                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        ret = CryptoLib_CertOnCrl_MSM(&p, end, &ctx, serialNumber, serialNumberLength);
                    }
                }

                /* the remainingBytes buffer is empty but there is still data in crlBuff */
                if ((ret == (uint8_t)CRYPTO_ERR_CALLAGAIN) && (((size_t)crlBuffLen - offset) > 0u))
                {
                    ret = CryptoLib_CertOnCrl_SetBuffers(&ctx, &crlBuff[offset], crlBuffLen - (uint16_t)offset, &p, &end, &offset);

                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        ret = CryptoLib_CertOnCrl_MSM(&p, end, &ctx, serialNumber, serialNumberLength);
                    }
                }
            }
        }
        else
        {
            CryptoLib_CertOnCrl_ResetCtx(&ctx);
            ctx.state = CERT;
            ctx.subState = REVOKED_CERT;
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_CHECKCERTIFICATEONCRL | 0x00000001UL));
        }

        /* if the user announces the last slice but we haven't finished parsing the
         * CRL, throw an error. */
        if ((slicing == SLICE_FINISH) && (ret == (uint8_t)CRYPTO_ERR_CALLAGAIN))
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_CHECKCERTIFICATEONCRL | 0x00000002UL));
        }

        /* if the function came to a result, reset internal state and context */
        if (ret != (uint8_t)CRYPTO_ERR_CALLAGAIN)
        {
            sliced = 0;
            CryptoLib_CertOnCrl_ResetCtx(&ctx);
            ctx.state = CERT;
            ctx.subState = REVOKED_CERT;
        }
    }

    return ret;
}

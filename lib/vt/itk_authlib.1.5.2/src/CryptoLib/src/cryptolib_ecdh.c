/**
 * @file  src/cryptolib_ecdh.c
 * @brief ECDH function definitions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_ecdh.h"
#include "cryptolib/cryptolib_ecdh_common.h"
#include "cryptolib/cryptolib_ecdh_compshared.h"
#include "cryptolib/cryptolib_rng.h"
#include "cryptolib/cryptolib_common.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#include "mbedtls/ecdh.h"
#include "mbedtls/platform.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if (CRYPTOLIB_ALT_ECDH == DISABLED)

#define X25519_KEY_LEN 32u      /**< Length of x25519 keys in binary representation */
#define X25519_KEY_STRLEN 64u   /**< Length of x25519 keys in base16 representation */

/**
 * Canonicalizes X25519 private keys according to RFC 7748 §5.
 *
 * @param[in,out] privateKey            private key mpi
 */
STATICFCN uint8_t CanonicalizePrivateKey(mbedtls_mpi* privateKey);

/**
 * Changes the endianness of a key, i.e. converts the given string representation of
 * the keys byte-wise. The function allocates memory for the resulting keys, that
 * needs to be freed by the caller.
 *
 * @param[in]  privateKey           Pointer to private key string
 * @param[in]  peerPublicKey        Pointer to public key string
 * @param[out] pePrivateKey         Pointer to pointer that will hold the converted private key string
 * @param[out] pePublicKey          Pointer to pointer that will hold the converted public key string
 * @return                          0 - CRYPTO_ERR_SUCCESS: keys successfully reversed<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: invalid input to sub-functions<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: allocation failed<br>
 */
STATICFCN uint8_t ChangeEndiannessOfKeys(const char* privateKey, const char* peerPublicKey,
                                         char** pePrivateKey, char** pePublicKey);

#if (CRYPTOLIB_NO_TIME_SLICING == DISABLED)

/** Enum for statemachine states */
typedef enum {
    IDLE = 0, /**< Ready for new calls */
    WORK = 1, /**< Perform sliced ecdh calculation */
    FINAL = 2 /**< Ready to write output */
} EcdhSlicingState_t;

/** Struct to hold pertinent ecdh variables */
typedef struct {
    mbedtls_ecdh_context ctx; /**< mbedtls ecdh context */

    /** GCD slicing sub-state tag */
    enum CompSharedState_tag {
        CS_IDLE = 0, /**< GCD function not active */
        CS_ACTIVE = 1 /**< GCD function in slice mode */
    } CsState; /**< GCD slicing sub-state */
} EcdhSlicingContext_t;

/*
 * Static variables
 * ----------------------------------------------------------------------------
 */
CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t CTX_INIT_FLG = 0; /**< Init flag for slicing context */
CRYPTOLIB_MEMORY_SECTION_END

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Prepares an EcdhSlicingContext_t context with parent input variables
 *
 * @param[in] privateKey            private key as base16 string
 * @param[in] peerPublicKey         peerPublic key as base16 string
 * @param[in] curve                 Curve type parameter
 * @param[in,out] context           pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: context successfully prepared<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t CryptoLib_Ecdh_Init(const char* privateKey,
                                      const char* peerPublicKey,
                                      const CryptoLib_Curve_t curve,
                                      EcdhSlicingContext_t* context);

/**
 * Writes output from context
 *
 * @param[out] sharedSecret         pointer to preallocated output array
 * @param[in] ctx                   pointer to context
 * @param[in] curve                 Curve type to be finalized
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: context successfully prepared<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t CryptoLib_Ecdh_Finalize(uint8_t sharedSecret[ECDH_COMMON_SECRET_LEN],
                                          const mbedtls_ecdh_context* ctx,
                                          const CryptoLib_Curve_t curve);

/**
 * Clears an EcdhSlicingContext_t
 *
 * @param[in] context               pointer to context
 */
STATICFCN void CryptoLib_Ecdh_Clean(EcdhSlicingContext_t* context);


/*
 * Function definitions
 * ----------------------------------------------------------------------------
 */

STATICFCN uint8_t CryptoLib_Ecdh_Init(const char* privateKey,
                                      const char* peerPublicKey,
                                      const CryptoLib_Curve_t curve,
                                      EcdhSlicingContext_t* context)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t ret_mbedtls = (int32_t)CRYPTO_ERR_SUCCESS;

    if (CTX_INIT_FLG == (uint8_t)1)
    {
        mbedtls_ecdh_free(&context->ctx);
    }

    mbedtls_ecdh_init(&context->ctx);
    CTX_INIT_FLG = (uint8_t)1;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "The result is not used in UB, but using it improves readability of the code." */
    if ((curve != CURVE_25519_BE) && (curve != CURVE_25519_LE))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_ECDH_INIT | 0x00000001UL));
    }
    else if ((ret_mbedtls = mbedtls_ecp_group_load(&context->ctx.grp, MBEDTLS_ECP_DP_CURVE25519)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_ECDH_INIT | 0x00000002UL));
    }
    else if ((ret_mbedtls = mbedtls_mpi_read_string(&context->ctx.d, HEX_RADIX, privateKey)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_ECDH_INIT | 0x00000003UL));
    }
    else if ((ret = CanonicalizePrivateKey(&context->ctx.d)) != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, (int32_t)ret, (uint32_t)(FID_CRYPTOLIB_ECDH_INIT | 0x00000006UL));
    }
    else if ((ret_mbedtls = mbedtls_mpi_read_string(&context->ctx.Qp.X, HEX_RADIX, peerPublicKey)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_ECDH_INIT | 0x00000004UL));
    }
    else if ((ret_mbedtls = mbedtls_mpi_lset(&context->ctx.Qp.Z, (mbedtls_mpi_sint)1)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_ECDH_INIT | 0x00000005UL));
    }
    /* polyspace-end */
    else
    {
        /* MISRA */
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_Ecdh_Finalize(uint8_t sharedSecret[ECDH_COMMON_SECRET_LEN],
                                          const mbedtls_ecdh_context* ctx,
                                          const CryptoLib_Curve_t curve)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (mbedtls_mpi_write_binary(&ctx->z, sharedSecret, (size_t)ECDH_COMMON_SECRET_LEN)
        != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_MBEDTLS_FAILED, (uint32_t)(FID_CRYPTOLIB_ECDH_FINALIZE | 0x00000001UL));
    }

    if (curve == CURVE_25519_LE)
    {
        CryptoLib_ChangeEndiannessBinary(sharedSecret, (size_t)ECDH_COMMON_SECRET_LEN);
    }

    return ret;
}

STATICFCN void CryptoLib_Ecdh_Clean(EcdhSlicingContext_t* context)
{
    if (context->CsState != CS_IDLE)
    {
        (void)CryptoLib_SlicedComputeShared(NULL, NULL, NULL, NULL, SLICE_RESET);
    }

    if (CTX_INIT_FLG == (uint8_t)1)
    {
        mbedtls_ecdh_free(&context->ctx);
    }

    (void)memset(context, 0, sizeof(EcdhSlicingContext_t));
}

#endif

STATICFCN uint8_t CanonicalizePrivateKey(mbedtls_mpi* privateKey)
{
    /* RFC 7748 §5.
     * set the three least significant bits of the first byte and the most significant bit of the last to zero,
     * set the second most significant bit of the last byte to 1 */
    int32_t mbedtlsRet = 0;
    CryptoLib_Return_t ret = CRYPTO_ERR_SUCCESS;
    const size_t bitsize = X25519_KEY_LEN * 8u;

    mbedtlsRet += mbedtls_mpi_set_bit(privateKey, (size_t)0u, 0u);
    mbedtlsRet += mbedtls_mpi_set_bit(privateKey, (size_t)1u, 0u);
    mbedtlsRet += mbedtls_mpi_set_bit(privateKey, (size_t)2u, 0u);
    mbedtlsRet += mbedtls_mpi_set_bit(privateKey, bitsize - 1u, 0u);
    mbedtlsRet += mbedtls_mpi_set_bit(privateKey, bitsize - 2u, 1u);

    if (mbedtlsRet != 0)
    {
        ret = CRYPTO_ERR_INTERNAL;
    }

    return (uint8_t)ret;
}

STATICFCN uint8_t ChangeEndiannessOfKeys(const char* privateKey, const char* peerPublicKey,
                                         char** pePrivateKey, char** pePublicKey)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    size_t privateKeyLen = strlen(privateKey);
    size_t peerPublicKeyLen = strlen(peerPublicKey);

    /* polyspace +1 MISRA-C3:11.5 [No action planned:low] "Conversion from pointer to void to pointer to char does not result in undefined behaviour here." */
    *pePrivateKey = mbedtls_calloc((size_t)1, privateKeyLen + (size_t)1);
    if (*pePrivateKey != NULL)
    {
        ret = CryptoLib_ChangeEndiannessString(privateKey, *pePrivateKey, privateKeyLen + (size_t)1);
        if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
        {
            mbedtls_free(*pePrivateKey);
            *pePrivateKey = NULL;
        }
    }
    else
    {
        ret = (uint8_t)CRYPTO_ERR_ALLOC_FAILED;
    }

    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        /* polyspace +1 MISRA-C3:11.5 [No action planned:low] "Conversion from pointer to void to pointer to char does not result in undefined behaviour here." */
        *pePublicKey = mbedtls_calloc((size_t)1, peerPublicKeyLen + (size_t)1);
        if (*pePublicKey != NULL)
        {
            ret = CryptoLib_ChangeEndiannessString(peerPublicKey, *pePublicKey, peerPublicKeyLen + (size_t)1);
            if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                mbedtls_free(*pePrivateKey);
                mbedtls_free(*pePublicKey);
                *pePrivateKey = NULL;
                *pePublicKey = NULL;
            }
        }
        else
        {
            ret = (uint8_t)CRYPTO_ERR_ALLOC_FAILED;
            mbedtls_free(*pePrivateKey);
            *pePrivateKey = NULL;
        }
    }

    return ret;
}

uint8_t CryptoLib_Ecdh(CRYPTOLIB_HUGE const char* privateKey,
                       CRYPTOLIB_HUGE const char* peerPublicKey,
                       const CryptoLib_Curve_t curve,
                       const CryptoLib_Slicing_t slicing,
                       CRYPTOLIB_HUGE uint8_t sharedSecret[ECDH_COMMON_SECRET_LEN])
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    char* bePrivateKey = NULL;
    char* bePeerPublicKey = NULL;

    #if CRYPTOLIB_NO_TIME_SLICING == 1
    int32_t ret_mbedtls = (int32_t)CRYPTO_ERR_SUCCESS;

    if ((privateKey == NULL) || (peerPublicKey == NULL) || (sharedSecret == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000001UL));
    }
    else if ((strlen(privateKey) != X25519_KEY_STRLEN) || (strlen(peerPublicKey) != X25519_KEY_STRLEN))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000011UL));
    }
    else if (slicing != SLICE_NO_SLICING)
    {
        /* invalid slicing parameter */
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000002UL));
    }
    else
    {
        ret = ChangeEndiannessOfKeys(privateKey, peerPublicKey,
                                     &bePrivateKey, &bePeerPublicKey);

        if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
        {
            mbedtls_ecdh_context ctx;
            mbedtls_ecdh_init(&ctx);

            /* initialize elliptic curve */
            /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
            if ((curve == CURVE_25519_LE) || (curve == CURVE_25519_BE))
            {
                if ((ret_mbedtls = mbedtls_ecp_group_load(&ctx.grp, MBEDTLS_ECP_DP_CURVE25519)) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000003UL));
                }
            }
            else
            {
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000004UL));
            }

            /* read keys and compute common secret */
            if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                if ((ret_mbedtls = mbedtls_mpi_read_string(&ctx.d, HEX_RADIX, bePrivateKey)) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000005UL));
                }
                else if ((ret = CanonicalizePrivateKey(&ctx.d)) != (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&ret, ret, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000013UL));
                }
                else if ((ret_mbedtls = mbedtls_mpi_read_string(&ctx.Qp.X, HEX_RADIX, bePeerPublicKey)) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000006UL));
                }
                else if ((ret_mbedtls = mbedtls_mpi_lset(&ctx.Qp.Z, (mbedtls_mpi_sint)1)) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000007UL));
                }
                else if ((ret_mbedtls = mbedtls_ecdh_compute_shared(&ctx.grp, &ctx.z, &ctx.Qp, &ctx.d, CryptoLib_RandomCallback, NULL)) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000008UL));
                }
                else
                {
                    if ((ret_mbedtls = mbedtls_mpi_write_binary(&ctx.z, sharedSecret, (size_t)ECDH_COMMON_SECRET_LEN)) != (int32_t)CRYPTO_ERR_SUCCESS)
                    {
                        CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000009UL));
                    }

                    if (curve == CURVE_25519_LE)
                    {
                        CryptoLib_ChangeEndiannessBinary(sharedSecret, (size_t)ECDH_COMMON_SECRET_LEN);
                    }
                }
            }
            /* polyspace-end */

            mbedtls_ecdh_free(&ctx);
            mbedtls_free(bePrivateKey);
            mbedtls_free(bePeerPublicKey);
        }
    }
    #else

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static EcdhSlicingState_t state = IDLE;
    static EcdhSlicingContext_t context;
    CRYPTOLIB_MEMORY_SECTION_END

    CryptoLib_Slicing_t slc = slicing;

    if (slc == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((privateKey == NULL) || (peerPublicKey == NULL) || (sharedSecret == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x0000000aUL));
    }
    else if ((strlen(privateKey) != X25519_KEY_STRLEN) || (strlen(peerPublicKey) != X25519_KEY_STRLEN))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000012UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("e_i,");
                #endif
                if (slc == SLICE_INIT)
                {
                    ret = ChangeEndiannessOfKeys(privateKey, peerPublicKey,
                                                 &bePrivateKey, &bePeerPublicKey);
                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        ret = CryptoLib_Ecdh_Init(bePrivateKey, bePeerPublicKey, curve, &context);

                        mbedtls_free(bePrivateKey);
                        mbedtls_free(bePeerPublicKey);

                        /* set to next state if ready, reset in case of errors */
                        if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                        {
                            /* Init done, go to WORK */
                            state = WORK;
                            slc = SLICE_CONTINUE;
                        }
                        else
                        {
                            /* error occured */
                            break;
                        }
                    }
                    else
                    {
                        break;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x0000000bUL));
                    break;
                }
            /* fallthrough */

            case WORK:
                if (slc == SLICE_CONTINUE)
                {
                    if (context.CsState == CS_IDLE)
                    {
                        /* Begin new CS slicing */
                        ret = CryptoLib_SlicedComputeShared(&context.ctx.grp,
                                                            &context.ctx.z,
                                                            &context.ctx.Qp,
                                                            &context.ctx.d,
                                                            SLICE_INIT);

                        if (ret == (uint8_t)CRYPTO_ERR_CALLAGAIN)
                        {
                            context.CsState = CS_ACTIVE;
                        }
                    }
                    else if (context.CsState == CS_ACTIVE)
                    {
                        /* Continue active CS slicing */
                        ret = CryptoLib_SlicedComputeShared(&context.ctx.grp,
                                                            &context.ctx.z,
                                                            &context.ctx.Qp,
                                                            &context.ctx.d,
                                                            SLICE_CONTINUE);

                        if (ret != (uint8_t)CRYPTO_ERR_CALLAGAIN)
                        {
                            context.CsState = CS_IDLE;
                        }
                    }
                    else /* LCOV_EXCL_START: internal enum out of bounds */
                    {
                        /* invalid CS sub-state. Mark as error and let the case
                         * below handle this error */
                        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x0000000cUL));
                        /* LCOV_EXCL_STOP */
                    }

                    /* set to next state if ready, reset in case of errors */
                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* WORK done, go to FINAL */
                        state = FINAL;
                        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x0000000dUL));
                }
                break;

            case FINAL:
                #ifdef BENCHMARK_ACTIVE
                BENCH("e_f,");
                #endif
                if (slc == SLICE_CONTINUE)
                {
                    ret = CryptoLib_Ecdh_Finalize(sharedSecret, &context.ctx, curve);
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x0000000fUL));
                }
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_ECDH | 0x00000010UL));
                break;
                /* LCOV_EXCL_STOP */
        }
    }
    /* polyspace-end */

    if (ret != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_Ecdh_Clean(&context);
        state = IDLE;
    }

    #endif

    return ret;
}

#endif /* #if (CRYPTOLIB_ALT_ECDH == DISABLED) */

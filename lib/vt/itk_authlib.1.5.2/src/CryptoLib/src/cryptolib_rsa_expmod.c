/**
 * @file  src/cryptolib_rsa_expmod.c
 * @brief Function definition of CryptoLib_SlicedExpMod
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */
#include "cryptolib/cryptolib_rsa_expmod.h"
#include "cryptolib/cryptolib_rsa_expmod_utils.h"
#include "cryptolib/cryptolib_rsa_expmod_subs.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#if (CRYPTOLIB_NO_TIME_SLICING == DISABLED)

uint8_t CryptoLib_SlicedMpiExpMod(mbedtls_mpi* X,
                                  const mbedtls_mpi* A,
                                  const mbedtls_mpi* E,
                                  const mbedtls_mpi* N,
                                  mbedtls_mpi* RR,
                                  const CryptoLib_Slicing_t slicing)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static RsaExpModContext_t context;
    static enum {
        IDLE = 0u,
        PHASE1 = 1u,
        PHASE2 = 2u,
        PHASE3 = 3u,
        PHASE4 = 4u,
        PHASE5 = 5u,
        PHASE6 = 6u,
        PHASE7 = 7u,
        FINAL = 9u
    } state = IDLE;
    CRYPTOLIB_MEMORY_SECTION_END

    if (slicing == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((X == NULL) || (A == NULL) || (E == NULL) || (N == NULL))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD | 0x00000002UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ex_i,");
                #endif
                if (slicing == SLICE_INIT)
                {
                    CryptoLib_SlicedMpiExpMod_InitContext(&context);
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD | 0x00000001UL));
                    break;
                }
            /* fallthrough */

            case PHASE1:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ex_p1,");
                #endif
                retval = CryptoLib_SlicedMpiExpMod_P1_Preparations(&context, A, E, N);

                if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    state = PHASE2;
                    retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                }
                break;

            case PHASE2:
                if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMpiExpMod_P2_RR(&context, RR, slicing);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = PHASE3;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD | 0x00000003UL));
                }
                break;

            case PHASE3:
                if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMpiExpMod_P3_PrepareWindow(&context, slicing);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = PHASE4;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD | 0x00000004UL));
                }
                break;

            case PHASE4:
                if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMpiExpMod_P4_CalculateWindow(&context, slicing);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = PHASE5;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD | 0x00000005UL));
                }
                break;

            case PHASE5:
                if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMpiExpMod_P5_Iterate(&context, slicing);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = PHASE6;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD | 0x00000006UL));
                }
                break;

            case PHASE6:
                if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMpiExpMod_P6_ProcessRemains(&context, slicing);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = PHASE7;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD | 0x00000007UL));
                }
                break;

            case PHASE7:
                if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMpiExpMod_P7_Finalize(&context, slicing);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = FINAL;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD | 0x00000008UL));
                }
                break;

            case FINAL:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ex_f,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    if (mbedtls_mpi_copy(X, &context.result) != (int32_t)CRYPTO_ERR_SUCCESS)
                    {
                        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD | 0x00000009UL));
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD | 0x0000000aUL));
                }

                if ((RR == NULL) || (RR->p == NULL))
                {
                    mbedtls_mpi_free(&context.internals.RR);
                }
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD | 0x0000000bUL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }

    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_SlicedMpiExpMod_ResetPhases();
        CryptoLib_SlicedMpiExpMod_ClearContext(&context);
        state = IDLE;
    }

    return retval;
}

#endif

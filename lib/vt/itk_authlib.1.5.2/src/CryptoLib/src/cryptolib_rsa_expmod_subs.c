/**
 * @file  src/cryptolib_rsa_expmod_subs.c
 * @brief Function definitions of CryptoLib_SlicedExpMod sub-functions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_rsa_expmod_subs.h"
#include "cryptolib/cryptolib_rsa_montmul.h"
#include "cryptolib/cryptolib_rsa_mod.h"
#include "cryptolib/cryptolib_common.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#include "mbedtls/constant_time.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if (CRYPTOLIB_NO_TIME_SLICING == DISABLED)

/**@ \{*/
enum slcState_tag {
    SLC_IDLE = 0,
    SLC_ACTIVE = 1
};

enum state_tag {
    S0 = 0,
    S1 = 1,
    S2 = 2,
    S3 = 3,
    S4 = 4,
    S5 = 5,
    S6 = 6,
    S7 = 7
};

#define RESULT_IDX_IN_W 0U /**< Fixed position of Expmod result MPI within the Window */

/**@ \}*/

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Calculates Modulo: R = A mod B (in sliced sub-sates)
 *
 * @param[out] R                    resulting mpi
 * @param[in] A                     mpi A
 * @param[in] B                     mpi B
 * @param[in,out] slcState          current sub-stste
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: calculation completed sucessfully<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid parameter or bad input data<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
STATICFCN uint8_t CryptoLib_SlicedMpiModMpi_Step(mbedtls_mpi* R,
                                                 const mbedtls_mpi* A,
                                                 const mbedtls_mpi* B,
                                                 enum slcState_tag* slcState);

/**
 * Calculates the following Montgomery multiplication (in sliced sub-states):
 * A = A * B * R^-1 mod N
 *
 * @param[out] A                    resulting mpi
 * @param[in] B                     mpi B
 * @param[in] N                     mpi N
 * @param[in] mm                    mpi_uint mm
 * @param[in] T                     mpi T
 * @param[in,out] slcState          current sub-stste
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: multiplication successful<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: multiplication not yet completed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid parameter or bad input data<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
STATICFCN uint8_t CryptoLib_SlicedMontMul_Step(mbedtls_mpi* A,
                                               const mbedtls_mpi* B,
                                               const mbedtls_mpi* N,
                                               mbedtls_mpi_uint mm,
                                               const mbedtls_mpi* T,
                                               enum slcState_tag* slcState);

/*
 * Static function definitions
 * ----------------------------------------------------------------------------
 */

STATICFCN uint8_t mpi_select(mbedtls_mpi* R,
                             const mbedtls_mpi* T,
                             size_t T_size,
                             size_t idx)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    for (size_t i = 0; i < T_size; i++)
    {
        if (mbedtls_mpi_safe_cond_assign(R, &T[i], (uint8_t)mbedtls_ct_size_bool_eq(i, idx)) != 0)
        {
            retval = (uint8_t)CRYPTO_ERR_BAD_INPUT;
            break;
        }
    }

    return retval;
}

STATICFCN uint8_t CryptoLib_SlicedMpiModMpi_Step(mbedtls_mpi* R,
                                                 const mbedtls_mpi* A,
                                                 const mbedtls_mpi* B,
                                                 enum slcState_tag* slcState)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (*slcState == SLC_IDLE)
    {
        retval = CryptoLib_SlicedMpiModMpi(R, A, B, SLICE_INIT);

        if (retval == (uint8_t)CRYPTO_ERR_CALLAGAIN)
        {
            *slcState = SLC_ACTIVE;
        }
    }
    else if (*slcState == SLC_ACTIVE)
    {
        retval = CryptoLib_SlicedMpiModMpi(R, A, B, SLICE_CONTINUE);

        if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
        {
            *slcState = SLC_IDLE;
        }
    }
    else
    {
        /* LCOV_EXCL_START: internal enum out of bounds */
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIMODMPI_STEP | 0x00000001UL));
        /* LCOV_EXCL_STOP */
    }

    return retval;
}

STATICFCN uint8_t CryptoLib_SlicedMontMul_Step(mbedtls_mpi* A,
                                               const mbedtls_mpi* B,
                                               const mbedtls_mpi* N,
                                               mbedtls_mpi_uint mm,
                                               const mbedtls_mpi* T,
                                               enum slcState_tag* slcState)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (*slcState == SLC_IDLE)
    {
        retval = CryptoLib_SlicedMontMul(A, B, N, mm, T, SLICE_INIT);

        if (retval == (uint8_t)CRYPTO_ERR_CALLAGAIN)
        {
            *slcState = SLC_ACTIVE;
        }
    }
    else if (*slcState == SLC_ACTIVE)
    {
        retval = CryptoLib_SlicedMontMul(A, B, N, mm, T, SLICE_CONTINUE);

        if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
        {
            *slcState = SLC_IDLE;
        }
    }
    else
    {
        /* LCOV_EXCL_START: internal enum out of bounds */
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTMUL_STEP | 0x00000001UL));
        /* LCOV_EXCL_STOP */
    }

    return retval;
}

/*
 * Public function definitions
 * ----------------------------------------------------------------------------
 */

uint8_t CryptoLib_SlicedMpiExpMod_P1_Preparations(RsaExpModContext_t* context,
                                                  const mbedtls_mpi* base,
                                                  const mbedtls_mpi* exponent,
                                                  const mbedtls_mpi* modulus)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if ((mbedtls_mpi_copy(&context->base, base) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_copy(&context->exponent, exponent) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_copy(&context->modulus, modulus) != (int32_t)CRYPTO_ERR_SUCCESS))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P1_PREPARATIONS | 0x00000001UL));
    }
    else if ((mbedtls_mpi_cmp_int(&context->modulus, (mbedtls_mpi_sint)0) <= 0) ||
             ((context->modulus.p[0] & (mbedtls_mpi_uint)1) == (mbedtls_mpi_uint)0))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P1_PREPARATIONS | 0x00000002UL));
    }
    else if (mbedtls_mpi_cmp_int(&context->exponent, (mbedtls_mpi_sint)0) < 0)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P1_PREPARATIONS | 0x00000003UL));
    }
    else if ((mbedtls_mpi_bitlen(&context->modulus) > (size_t)MBEDTLS_MPI_MAX_BITS) ||
             (mbedtls_mpi_bitlen(&context->exponent) > (size_t)MBEDTLS_MPI_MAX_BITS))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P1_PREPARATIONS | 0x00000005UL));
    }
    else
    {
        CryptoLib_SlicedMpiExpMod_MontgInit(&context->internals.mm, &context->modulus);

        size_t i = mbedtls_mpi_bitlen(&context->exponent);

        context->internals.windowBitSize = (i > (size_t)671) ?
                                           (size_t)6 : (i > (size_t)239) ?
                                           (size_t)5 : (i > (size_t)79) ?
                                           (size_t)4 : (i > (size_t)23) ?
                                           (size_t)3 : (size_t)1;

        #if (MBEDTLS_MPI_WINDOW_SIZE < 6)
        if (context->internals.windowBitSize > (size_t)MBEDTLS_MPI_WINDOW_SIZE)
        {
            context->internals.windowBitSize = MBEDTLS_MPI_WINDOW_SIZE;
        }
        #endif

        context->internals.windowTableSize = (size_t)1 << context->internals.windowBitSize;
        context->internals.j = context->modulus.n + (size_t)1;

        if ((mbedtls_mpi_copy(&context->internals.window[RESULT_IDX_IN_W], &context->result) != (int32_t)CRYPTO_ERR_SUCCESS) ||
            (mbedtls_mpi_grow(&context->internals.window[RESULT_IDX_IN_W], context->internals.j) != (int32_t)CRYPTO_ERR_SUCCESS) ||
            (mbedtls_mpi_grow(&context->internals.window[1], context->internals.j) != (int32_t)CRYPTO_ERR_SUCCESS) ||
            (mbedtls_mpi_grow(&context->internals.T, context->internals.j * (size_t)2) != (int32_t)CRYPTO_ERR_SUCCESS))
        {
            CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P1_PREPARATIONS | 0x00000004UL));
        }
        else
        {
            /* Compensate for negative A (and correct at the end) */
            if (base->s == -1)
            {
                context->internals.sign = (uint8_t)1;
                context->base.s = 1;
            }
            else
            {
                context->internals.sign = (uint8_t)0;
            }
        }
    }

    return retval;
}

uint8_t CryptoLib_SlicedMpiExpMod_P2_RR(RsaExpModContext_t* context,
                                        mbedtls_mpi* RR,
                                        const CryptoLib_Slicing_t slicing)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static enum state_tag state = S0;
    static enum slcState_tag slcState = SLC_IDLE;
    CRYPTOLIB_MEMORY_SECTION_END

    if (slicing == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((RR != NULL) && (RR->p != NULL))
    {
        (void)memcpy(&context->internals.RR, RR, sizeof(mbedtls_mpi));
    }
    else
    {
        if ((RR == NULL) || (RR->p == NULL))
        {
            /* As RR is null the following code will alloc memory for use in
             * internals.RR. As long as the CPY state is not called, rsa_expmod
             * is responsible to free it again. */
            context->internals.RR_active = 1u;
        }

        switch (state)
        {
            case S0:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ex_p2s0,");
                #endif
                if ((mbedtls_mpi_lset(&context->internals.RR, (mbedtls_mpi_sint)1) != (int32_t)CRYPTO_ERR_SUCCESS) ||
                    (mbedtls_mpi_shift_l(&context->internals.RR, context->modulus.n * (size_t)2 * biL) != (int32_t)CRYPTO_ERR_SUCCESS))
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P2_RR | 0x00000001UL));
                }
                else
                {
                    state = S1;
                    retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                }
                break;

            case S1:
                if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMpiModMpi_Step(&context->internals.RR,
                                                            &context->internals.RR,
                                                            &context->modulus,
                                                            &slcState);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = S2;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P2_RR | 0x00000003UL));
                }
                break;

            case S2:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ex_p2s2,");
                #endif
                if (RR != NULL)
                {
                    /* copy RR to given parameter (if not null) to speed up
                     * subsequent calls */
                    (void)memcpy(RR, &context->internals.RR, sizeof(mbedtls_mpi));

                    /* Freeing of RR now needs to be handled by CryptoLib_VerifyRsa */
                    context->internals.RR_active = 0u;
                }
                state = S0;
                break;

            /* LCOV_EXCL_START: internal enum out of bounds */
            case S3: /* not present in this function: fallthrough */
            case S4: /* not present in this function: fallthrough */
            case S5: /* not present in this function: fallthrough */
            case S6: /* not present in this function: fallthrough */
            case S7: /* not present in this function: fallthrough */
            default:
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P2_RR | 0x00000004UL));
                break;
                /* LCOV_EXCL_STOP */
        }
    }

    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        state = S0;

        if (slcState != SLC_IDLE)
        {
            slcState = SLC_IDLE;
            (void)CryptoLib_SlicedMpiModMpi(NULL, NULL, NULL, SLICE_RESET);
        }
    }

    return retval;
}

uint8_t CryptoLib_SlicedMpiExpMod_P3_PrepareWindow(RsaExpModContext_t* context,
                                                   const CryptoLib_Slicing_t slicing)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static enum state_tag state = S0;
    static enum slcState_tag slcState = SLC_IDLE;
    static mbedtls_mpi_uint z;
    static mbedtls_mpi U;
    CRYPTOLIB_MEMORY_SECTION_END

    if (slicing == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case S0:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ex_p3s0,");
                #endif
                if (mbedtls_mpi_cmp_mpi(&context->base, &context->modulus) >= (int32_t)0)
                {
                    state = S1;
                }
                else if (mbedtls_mpi_copy(&context->internals.window[1], &context->base) == (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    state = S2;
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P3_PREPAREWINDOW | 0x00000001UL));
                    break;
                }
            /* fallthrough */

            case S1:
                if (state == S2)
                {
                    /* Catch fallthrough from S0 meant for case S2 */
                }
                else if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMpiModMpi_Step(&context->internals.window[1],
                                                            &context->base,
                                                            &context->modulus,
                                                            &slcState);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = S2;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                    break;
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P3_PREPAREWINDOW | 0x00000003UL));
                    break;
                }
            /* fallthrough */

            case S2:
                if (mbedtls_mpi_grow(&context->internals.window[1], (context->modulus.n + 1u)) == (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    state = S3;
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P3_PREPAREWINDOW | 0x00000004UL));
                    break;
                }
            /* fallthrough */

            case S3:
                if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMontMul_Step(&context->internals.window[1],
                                                          &context->internals.RR,
                                                          &context->modulus,
                                                          context->internals.mm,
                                                          &context->internals.T,
                                                          &slcState);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = S4;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P3_PREPAREWINDOW | 0x00000005UL));
                }
                break;

            case S4:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ex_p3s3,");
                #endif
                if (mbedtls_mpi_copy(&context->internals.window[RESULT_IDX_IN_W], &context->internals.RR)
                    != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P3_PREPAREWINDOW | 0x00000006UL));
                    break;
                }
                else
                {
                    /* polyspace-begin MISRA-C3:D4.6 [No action planned:low] "Interfacing with mbed TLS requires use of native type int. It is assured that values will fit in at least int16_t." */
                    /* Do preparation for S5 aka mpi_montred */
                    z = 1;
                    U.s = (int)z;
                    U.n = (size_t)z;
                    U.p = &z;
                    state = S5;
                    /* polyspace-end */
                }
            /* fallthrough */

            case S5:
                if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMontMul_Step(&context->internals.window[RESULT_IDX_IN_W],
                                                          &U,
                                                          &context->modulus,
                                                          context->internals.mm,
                                                          &context->internals.T,
                                                          &slcState);
                }
                else
                {
                    retval = (uint8_t)CRYPTO_ERR_BAD_INPUT;
                }
                break;

            /* LCOV_EXCL_START: internal enum out of bounds */
            case S6: /* not present in this function: fallthrough */
            case S7: /* not present in this function: fallthrough */
            default:
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P3_PREPAREWINDOW | 0x00000009UL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }

    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        state = S0;

        if (slcState != SLC_IDLE)
        {
            slcState = SLC_IDLE;
            (void)CryptoLib_SlicedMontMul(NULL, NULL, NULL, (mbedtls_mpi_uint)0, NULL, SLICE_RESET);
            (void)CryptoLib_SlicedMpiModMpi(NULL, NULL, NULL, SLICE_RESET);
        }
    }

    return retval;
}

uint8_t CryptoLib_SlicedMpiExpMod_P4_CalculateWindow(RsaExpModContext_t* context, const CryptoLib_Slicing_t slicing)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static enum state_tag state = S0;
    static enum slcState_tag slcState = SLC_IDLE;
    static uint16_t l1c = 0;
    static uint16_t l2c = 0;
    CRYPTOLIB_MEMORY_SECTION_END

    if (slicing == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if (slicing != SLICE_CONTINUE)
    {
        retval = (uint8_t)CRYPTO_ERR_BAD_INPUT;
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case S0:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ex_p4s0,");
                #endif
                if (context->internals.windowBitSize <= (size_t)1)
                {
                    break;
                }
                else
                {
                    context->internals.j = context->internals.windowTableSize / 2;

                    if ((mbedtls_mpi_grow(&context->internals.window[context->internals.j],
                                          context->modulus.n + (size_t)1) != (int32_t)CRYPTO_ERR_SUCCESS) ||
                        (mbedtls_mpi_copy(&context->internals.window[context->internals.j],
                                          &context->internals.window[1]) != (int32_t)CRYPTO_ERR_SUCCESS))
                    {
                        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P4_CALCULATEWINDOW | 0x00000001UL));
                        break;
                    }
                    else
                    {
                        state = S1;

                        /* prepare loop counters */
                        l1c = 0;
                        l2c = (uint16_t)(context->internals.j + (size_t)1);
                    }
                }
            /* fallthrough */

            case S1:
                retval = CryptoLib_SlicedMontMul_Step(&context->internals.window[context->internals.j],
                                                      &context->internals.window[context->internals.j],
                                                      &context->modulus,
                                                      context->internals.mm,
                                                      &context->internals.T,
                                                      &slcState);

                /* success means we need to prepare the next iteration */
                if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;

                    if ((size_t)l1c == (context->internals.windowBitSize - (size_t)2))
                    {
                        state = S2;
                    }
                    else
                    {
                        l1c++;
                    }
                }
                break;

            case S2:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ex_p4s2,");
                #endif
                if (slcState == SLC_IDLE)
                {
                    /* start of a new sliced MontMul means a new loop iterations has just begun -> grow and copy */
                    if ((mbedtls_mpi_grow(&context->internals.window[l2c], context->modulus.n + (size_t)1)
                         != (int32_t)CRYPTO_ERR_SUCCESS) ||
                        (mbedtls_mpi_copy(&context->internals.window[l2c], &context->internals.window[l2c - (uint16_t)1])
                         != (int32_t)CRYPTO_ERR_SUCCESS))
                    {
                        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P4_CALCULATEWINDOW | 0x00000005UL));
                        break;
                    }

                    retval = CryptoLib_SlicedMontMul(&context->internals.window[l2c],
                                                     &context->internals.window[1],
                                                     &context->modulus,
                                                     context->internals.mm,
                                                     &context->internals.T,
                                                     SLICE_INIT);

                    if (retval == (uint8_t)CRYPTO_ERR_CALLAGAIN)
                    {
                        slcState = SLC_ACTIVE;
                    }
                }
                else if (slcState == SLC_ACTIVE)
                {
                    retval = CryptoLib_SlicedMontMul(&context->internals.window[l2c],
                                                     &context->internals.window[1],
                                                     &context->modulus,
                                                     context->internals.mm,
                                                     &context->internals.T,
                                                     SLICE_CONTINUE);

                    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
                    {
                        slcState = SLC_IDLE;
                    }
                }
                else
                {
                    /* LCOV_EXCL_START: internal enum out of bounds */
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P4_CALCULATEWINDOW | 0x00000006UL));
                    /* LCOV_EXCL_STOP */
                }

                /* success means we need to prepare the next iteration */
                if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    if ((size_t)l2c < (context->internals.windowTableSize - (size_t)1))
                    {
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                        l2c++;
                    }
                }
                break;

            /* LCOV_EXCL_START: internal enum out of bounds */
            case S3: /* not present in this function: fallthrough */
            case S4: /* not present in this function: fallthrough */
            case S5: /* not present in this function: fallthrough */
            case S6: /* not present in this function: fallthrough */
            case S7: /* not present in this function: fallthrough */
            default:
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P4_CALCULATEWINDOW | 0x00000008UL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */

        if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
        {
            context->internals.nblimbs = context->exponent.n;
            context->internals.bufsize = 0;
            context->internals.nbits = 0;
            context->internals.exponentBitsInWindow = 0;
            context->internals.state = 0;
        }
    }

    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        l1c = 0;
        l2c = 0;
        state = S0;

        if (slcState != SLC_IDLE)
        {
            slcState = SLC_IDLE;
            (void)CryptoLib_SlicedMontMul(NULL, NULL, NULL, (mbedtls_mpi_uint)0, NULL, SLICE_RESET);
        }
    }

    return retval;
}

uint8_t CryptoLib_SlicedMpiExpMod_P5_Iterate(RsaExpModContext_t* context, const CryptoLib_Slicing_t slicing)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
    uint8_t leave = 0;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static enum state_tag state = S0;
    static enum slcState_tag slcState = SLC_IDLE;
    static uint16_t windowLoopCounter = 0;
    CRYPTOLIB_MEMORY_SECTION_END

    if (slicing == SLICE_RESET)
    {
        /* handled at the end of the function */
    }
    else if (slicing != SLICE_CONTINUE)
    {
        retval = (uint8_t)CRYPTO_ERR_BAD_INPUT;
    }
    else
    {
        while (leave == (uint8_t)0)
        {
            switch (state)
            {
                case S0:
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p5s0,");
                    #endif
                    if (context->internals.bufsize == (size_t)0)
                    {
                        if (context->internals.nblimbs == (size_t)0)
                        {
                            retval = (uint8_t)CRYPTO_ERR_SUCCESS;
                            leave = 1;
                            break;
                        }

                        context->internals.nblimbs--;
                        context->internals.bufsize = sizeof(mbedtls_mpi_uint) << 3;
                    }

                    context->internals.bufsize--;
                    context->internals.ei = (mbedtls_mpi_uint)(
                        (context->exponent.p[context->internals.nblimbs] >> context->internals.bufsize)
                        & (mbedtls_mpi_uint)1);

                    if ((context->internals.ei == (mbedtls_mpi_uint)0) &&
                        (context->internals.state == (mbedtls_mpi_uint)0))
                    {
                        break;
                    }
                    else if ((context->internals.ei == (mbedtls_mpi_uint)0) &&
                             (context->internals.state == (mbedtls_mpi_uint)1))
                    {
                        state = S1;
                    }
                    else
                    {
                        state = S3;
                    }
                    break;

                case S1:
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p5s1,");
                    #endif
                    retval = mpi_select(&context->internals.WW, context->internals.window, context->internals.windowTableSize, RESULT_IDX_IN_W);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = S2;
                    }
                    else
                    {
                        /* error, reset state and exit loop */
                        leave = 1;
                        CryptoLib_SetError(&retval, (int32_t)retval, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P5_ITERATE | 0x00000001UL));
                    }
                    break;

                case S2:
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p5s2,");
                    #endif
                    /* prepare to exit the while loop after the now following sliced sub-fcn call*/
                    leave = 1;

                    retval = CryptoLib_SlicedMontMul_Step(&context->internals.window[RESULT_IDX_IN_W],
                                                          &context->internals.WW,
                                                          &context->modulus,
                                                          context->internals.mm,
                                                          &context->internals.T,
                                                          &slcState);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* Montgomery multiplication finished, go back to start */
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                        state = S0;
                    }
                    else if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
                    {
                        /* error, reset state and exit loop */
                        leave = 1;
                        CryptoLib_SetError(&retval, (int32_t)retval, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P5_ITERATE | 0x00000002UL));
                    }
                    else
                    {
                        /* MISRA */
                    }
                    break;

                case S3:
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p5s3,");
                    #endif
                    context->internals.state = 2;
                    context->internals.nbits++;

                    /* This works even on a 16-bit controller as ei = [0..1], windowBitSize = [0..6] and nbits = [0..1]
                     * => exponentBitsInWindow = [0..64] */
                    context->internals.exponentBitsInWindow |= (size_t)(context->internals.ei << (context->internals.windowBitSize - context->internals.nbits));

                    if (context->internals.nbits == context->internals.windowBitSize)
                    {
                        windowLoopCounter = 0;
                        state = S4;
                    }
                    else
                    {
                        state = S0;
                    }
                    break;

                case S4:
                    /* Implements loop head of "for( windowLoopCounter = 0; windowLoopCounter < window_bitsize; windowLoopCounter++ )" */
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p5s4,");
                    #endif
                    if ((size_t)windowLoopCounter < context->internals.windowBitSize)
                    {
                        retval = mpi_select(&context->internals.WW, context->internals.window, context->internals.windowTableSize, RESULT_IDX_IN_W);

                        if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                        {
                            state = S5;
                        }
                        else
                        {
                            /* error, reset state and exit loop */
                            leave = 1;
                            CryptoLib_SetError(&retval, (int32_t)retval, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P5_ITERATE | 0x00000003UL));
                        }
                    }
                    else
                    {
                        state = S6;     /* go after loop */
                    }
                    break;

                case S5:
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p5s5,");
                    #endif
                    /* prepare to exit the while loop after the now following sliced sub-fcn call*/
                    leave = 1;

                    retval = CryptoLib_SlicedMontMul_Step(&context->internals.window[RESULT_IDX_IN_W],
                                                          &context->internals.WW,
                                                          &context->modulus,
                                                          context->internals.mm,
                                                          &context->internals.T,
                                                          &slcState);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* Montgomery multiplication finished, go to next iteration */
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                        windowLoopCounter++;
                        state = S4;
                    }
                    break;

                case S6:
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p5s6,");
                    #endif
                    retval = mpi_select(&context->internals.WW, context->internals.window, context->internals.windowTableSize, context->internals.exponentBitsInWindow);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = S7;
                    }
                    else
                    {
                        leave = 1;
                        CryptoLib_SetError(&retval, (int32_t)retval, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P5_ITERATE | 0x00000004UL));
                    }
                    break;

                case S7:
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p5s7,");
                    #endif
                    /* prepare to exit the while loop after the now following sliced sub-fcn call*/
                    leave = 1;

                    retval = CryptoLib_SlicedMontMul_Step(&context->internals.window[RESULT_IDX_IN_W],
                                                          &context->internals.WW,
                                                          &context->modulus,
                                                          context->internals.mm,
                                                          &context->internals.T,
                                                          &slcState);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* Montgomery multiplication finished, go to next state */
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;

                        context->internals.state--;
                        context->internals.nbits = 0;
                        context->internals.exponentBitsInWindow = 0;
                        state = S0;
                    }
                    else if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
                    {
                        /* error, reset state and exit loop */
                        leave = 1;
                    }
                    else
                    {
                        /* MISRA */
                    }
                    break;

                default: /* LCOV_EXCL_START: internal enum out of bounds */
                    leave = 1;
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P5_ITERATE | 0x00000005UL));
                    break;
                    /* LCOV_EXCL_STOP */
            }
        }
    }

    if ((retval != (uint8_t)CRYPTO_ERR_CALLAGAIN) || (slicing == SLICE_RESET))
    {
        state = S0;
        windowLoopCounter = 0;
        if (slcState != SLC_IDLE)
        {
            slcState = SLC_IDLE;
            (void)CryptoLib_SlicedMontMul(NULL, NULL, NULL, (mbedtls_mpi_uint)0, NULL, SLICE_RESET);
        }
    }

    return retval;
}

uint8_t CryptoLib_SlicedMpiExpMod_P6_ProcessRemains(RsaExpModContext_t* context, const CryptoLib_Slicing_t slicing)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
    uint8_t leave = 0;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static enum state_tag state = S0;
    static enum slcState_tag slcState = SLC_IDLE;
    static uint16_t lc = 0;
    CRYPTOLIB_MEMORY_SECTION_END

    if (slicing == SLICE_RESET)
    {
        /* handled at the end of the function */
    }
    else if (slicing != SLICE_CONTINUE)
    {
        retval = (uint8_t)CRYPTO_ERR_BAD_INPUT;
    }
    else
    {
        while (leave == (uint8_t)0)
        {
            /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
            switch (state)
            {
                case S0:
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p6s0,");
                    #endif
                    lc = 0;
                    state = S1;
                    break;

                case S1:
                    /* Implements loop head of "for(lc = 0; lc <nbits; lc++)" */
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p6s1,");
                    #endif
                    if ((size_t)lc < context->internals.nbits)
                    {
                        retval = mpi_select(&context->internals.WW, context->internals.window, context->internals.windowTableSize, RESULT_IDX_IN_W);

                        if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                        {
                            state = S2;
                        }
                        else
                        {
                            /* error, reset state and exit loop */
                            leave = 1;
                            CryptoLib_SetError(&retval, (int32_t)retval, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P6_PROCESSREMAINS | 0x00000001UL));
                        }
                    }
                    else
                    {
                        leave = 1;
                        retval = (uint8_t)CRYPTO_ERR_SUCCESS;
                    }
                    break;

                case S2:
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p6s2,");
                    #endif
                    /* prepare to exit the while loop after the now following sliced sub-fcn call*/
                    leave = 1;

                    retval = CryptoLib_SlicedMontMul_Step(&context->internals.window[RESULT_IDX_IN_W],
                                                          &context->internals.WW,
                                                          &context->modulus,
                                                          context->internals.mm,
                                                          &context->internals.T,
                                                          &slcState);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* Montgomery multiplication finished, go to S4 */
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                        state = S3;
                    }
                    else if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
                    {
                        /* error, reset state and exit loop */
                        leave = 1;
                        CryptoLib_SetError(&retval, (int32_t)retval, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P6_PROCESSREMAINS | 0x00000002UL));
                    }
                    else
                    {
                        /* MISRA */
                    }
                    break;

                case S3:
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p6s3,");
                    #endif
                    context->internals.exponentBitsInWindow <<= 1;

                    if ((context->internals.exponentBitsInWindow & ((size_t)1U << context->internals.windowBitSize)) != (size_t)0)
                    {
                        state = S4;
                    }
                    else
                    {
                        /* iteration complete, increment counter and return to S1 */
                        state = S1;
                        lc++;
                        break;
                    }
                /* fallthrough */

                case S4:
                   #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p6s4,");
                    #endif
                    retval = mpi_select(&context->internals.WW, context->internals.window, context->internals.windowTableSize, 1);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = S5;
                    }
                    else
                    {
                        /* error, reset state and exit loop */
                        leave = 1;
                        CryptoLib_SetError(&retval, (int32_t)retval, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P6_PROCESSREMAINS | 0x00000003UL));
                        break;
                    }
                /* fallthrough */

                case S5:
                    #ifdef BENCHMARK_ACTIVE
                    BENCH("ex_p6s5,");
                    #endif
                    /* prepare to exit the while loop after the now following sliced sub-fcn call*/
                    leave = 1;

                    retval = CryptoLib_SlicedMontMul_Step(&context->internals.window[RESULT_IDX_IN_W],
                                                          &context->internals.WW,
                                                          &context->modulus,
                                                          context->internals.mm,
                                                          &context->internals.T,
                                                          &slcState);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* Montgomery multiplication finished, go to next iteration */
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                        /* iteration complete, increment counter and return to S1 */
                        state = S1;
                        lc++;
                    }
                    break;

                /* LCOV_EXCL_START: internal enum out of bounds */
                default:
                    leave = 1;
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P6_PROCESSREMAINS | 0x00000004UL));
                    break;
                    /* LCOV_EXCL_STOP */
            }
            /* polyspace-end */
        }
    }

    if ((retval != (uint8_t)CRYPTO_ERR_CALLAGAIN) || (slicing == SLICE_RESET))
    {
        state = S0;

        if (slcState != SLC_IDLE)
        {
            slcState = SLC_IDLE;
            (void)CryptoLib_SlicedMontMul(NULL, NULL, NULL, (mbedtls_mpi_uint)0, NULL, SLICE_RESET);
        }
    }

    return retval;
}

uint8_t CryptoLib_SlicedMpiExpMod_P7_Finalize(RsaExpModContext_t* context, const CryptoLib_Slicing_t slicing)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static enum state_tag state = S0;
    static enum slcState_tag slcState = SLC_IDLE;
    static mbedtls_mpi_uint z;
    static mbedtls_mpi U;
    CRYPTOLIB_MEMORY_SECTION_END

    if (slicing == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if (slicing != SLICE_CONTINUE)
    {
        retval = (uint8_t)CRYPTO_ERR_BAD_INPUT;
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case S0:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ex_p7s0,");
                #endif
                /* polyspace-begin MISRA-C3:D4.6 [No action planned:low] "Interfacing with mbed TLS requires use of native type int. It is assured that values will fit in at least int16_t." */
                z = 1;
                U.s = (int)z;
                U.n = (size_t)z;
                U.p = &z;
                state = S1;
            /* polyspace-end */
            /* fallthrough */

            case S1:
                retval = CryptoLib_SlicedMontMul_Step(&context->internals.window[RESULT_IDX_IN_W],
                                                      &U,
                                                      &context->modulus,
                                                      context->internals.mm,
                                                      &context->internals.T,
                                                      &slcState);

                if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    /* fallthrough */
                }
                else if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
                {
                    CryptoLib_SetError(&retval, (int32_t)retval, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P7_FINALIZE | 0x00000001UL));
                    break;
                }
                else
                {
                    break;
                }
            /* fallthrough */

            case S2:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ex_p7s2,");
                #endif
                if ((context->internals.sign != 0u) &&
                    (context->exponent.n != (size_t)0) &&
                    ((context->exponent.p[0] & (mbedtls_mpi_uint)1) != 0u))
                {
                    context->result.s = -1;
                    if (mbedtls_mpi_add_mpi(&context->result, &context->modulus, &context->internals.window[RESULT_IDX_IN_W])
                        != (int32_t)CRYPTO_ERR_SUCCESS)
                    {
                        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P7_FINALIZE | 0x00000003UL));
                    }
                }
                else
                {
                    if (mbedtls_mpi_copy(&context->result, &context->internals.window[RESULT_IDX_IN_W]) != (int32_t)CRYPTO_ERR_SUCCESS)
                    {
                        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P7_FINALIZE | 0x00000004UL));
                    }
                }
                state = S0;
                break;

            /* LCOV_EXCL_START: internal enum out of bounds */
            case S3: /* not present in this function: fallthrough */
            case S4: /* not present in this function: fallthrough */
            case S5: /* not present in this function: fallthrough */
            case S6: /* not present in this function: fallthrough */
            case S7: /* not present in this function: fallthrough */
            default:
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIEXPMOD_P7_FINALIZE | 0x00000005UL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }

    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        state = S0;

        if (slcState != SLC_IDLE)
        {
            slcState = SLC_IDLE;
            (void)CryptoLib_SlicedMontMul(NULL, NULL, NULL, (mbedtls_mpi_uint)0, NULL, SLICE_RESET);
        }
    }

    return retval;
}

#endif

/**
 * @file   src/cryptolib_ecdh_doubleadd.c
 * @brief  Montgomery Double-and-Add function definition
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im S<PERSON>yerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_ecdh_doubleadd.h"
#include "cryptolib/cryptolib_ecdh_common.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if ((CRYPTOLIB_NO_TIME_SLICING == DISABLED) && (CRYPTOLIB_ALT_ECDH == DISABLED))

/** Enum for statemachine states */
typedef enum {
    IDLE = 0,              /**< Ready for new calls */
    DOUBLE_ADD_PART1 = 1,  /**< Perform first set of instructions */
    DOUBLE_ADD_PART2 = 2,  /**< Perform second set of instructions */
    DOUBLE_ADD_PART3 = 3,  /**< Perform third set of instructions */
    DOUBLE_ADD_PART4 = 4,  /**< Perform fourth set of instructions */
    DOUBLE_ADD_PART5 = 5,  /**< Perform fifth set of instructions */
    DOUBLE_ADD_PART6 = 6,  /**< Perform sixth set of instructions */
    DOUBLE_ADD_PART7 = 7,  /**< Perform seventh set of instructions */
    DOUBLE_ADD_PART8 = 8   /**< Perform seventh set of instructions */
} DoubleAddSlicingState_t;

/*
 * Static variables
 * ----------------------------------------------------------------------------
 */
CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t MPI_INIT_FLAG_DOUBLE = 0;  /**< Init flag for slicing context */
CRYPTOLIB_MEMORY_SECTION_END

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Prepares a DoubleAddSlicingContext_t context
 *
 * @param[in,out] ctx               pointer to context
 */
STATICFCN void CryptoLib_SlicedMontDA_Init(DoubleAddSlicingContext_t* ctx);

/**
 * Performs first set of operations
 *
 * @param[in] grp                   pointer to grp
 * @param[in] pointP                pointer to pointP
 * @param[in,out] ctx               pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: part1 successfully calculated<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t part1(const mbedtls_ecp_group* grp,
                        const mbedtls_ecp_point* pointP,
                        DoubleAddSlicingContext_t* ctx);

/**
 * Performs second set of operations
 *
 * @param[in] grp                   pointer to grp
 * @param[in] pointQ                pointer to pointQ
 * @param[in,out] ctx               pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: part2 successfully calculated<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t part2(const mbedtls_ecp_group* grp,
                        const mbedtls_ecp_point* pointQ,
                        DoubleAddSlicingContext_t* ctx);

/**
 * Performs third set of operations
 *
 * @param[in] grp                   pointer to grp
 * @param[in,out] ctx               pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: part3 successfully calculated<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t part3(const mbedtls_ecp_group* grp,
                        DoubleAddSlicingContext_t* ctx);

/**
 * Performs fourth set of operations
 *
 * @param[in]  grp                  pointer to grp
 * @param[out] S                    pointer to S
 * @param[in]  ctx                  pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: part4 successfully calculated<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t part4(const mbedtls_ecp_group* grp,
                        mbedtls_ecp_point* S,
                        DoubleAddSlicingContext_t* ctx);

/**
 * Performs fifth set of operations
 *
 * @param[in]  grp                  pointer to grp
 * @param[out] S                    pointer to S
 * @param[in]  ctx                  pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: part5 successfully calculated<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t part5(const mbedtls_ecp_group* grp,
                        mbedtls_ecp_point* S,
                        const DoubleAddSlicingContext_t* ctx);

/**
 * Performs sixth set of operations
 *
 * @param[in]  grp                  pointer to grp
 * @param[out] R                    pointer to R
 * @param[out] S                    pointer to S
 * @param[in]  d                    pointer to d
 * @param[in]  ctx                  pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: part6 successfully calculated<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t part6(const mbedtls_ecp_group* grp,
                        mbedtls_ecp_point* R,
                        mbedtls_ecp_point* S,
                        const mbedtls_mpi* d,
                        const DoubleAddSlicingContext_t* ctx);

/**
 * Performs seventh set of operations
 *
 * @param[in]  grp                  pointer to grp
 * @param[out] R                    pointer to R
 * @param[in]  ctx                  pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: part7 successfully calculated<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t part7(const mbedtls_ecp_group* grp,
                        mbedtls_ecp_point* R,
                        const DoubleAddSlicingContext_t* ctx);

/**
 * Performs eighth set of operations
 *
 * @param[in]  grp                  pointer to grp
 * @param[out] R                    pointer to R
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: part8 successfully calculated<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t part8(const mbedtls_ecp_group* grp,
                        mbedtls_ecp_point* R);

/**
 * Frees allocated mpis within ctx
 *
 * @param[in,out] ctx               pointer to context
 */
STATICFCN void CryptoLib_SlicedMontDA_Clean(DoubleAddSlicingContext_t* ctx);

/*
 * Static function definitions
 * ----------------------------------------------------------------------------
 */
STATICFCN void CryptoLib_SlicedMontDA_Init(DoubleAddSlicingContext_t* ctx)
{
    MPI_INIT_FLAG_DOUBLE = 1;
    mbedtls_mpi_init(&ctx->A);
    mbedtls_mpi_init(&ctx->AA);
    mbedtls_mpi_init(&ctx->B);
    mbedtls_mpi_init(&ctx->BB);
    mbedtls_mpi_init(&ctx->E);
    mbedtls_mpi_init(&ctx->C);
    mbedtls_mpi_init(&ctx->D);
    mbedtls_mpi_init(&ctx->DA);
    mbedtls_mpi_init(&ctx->CB);
}

STATICFCN uint8_t part1(const mbedtls_ecp_group* grp,
                        const mbedtls_ecp_point* pointP,
                        DoubleAddSlicingContext_t* ctx)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_mpi_add_mpi(&ctx->A, &pointP->X, &pointP->Z)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART1 | 0x00000001UL));
    }
    else if ((mret = (int32_t)mod_add(&ctx->A, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART1 | 0x00000002UL));
    }
    else if ((mret = mbedtls_mpi_mul_mpi(&ctx->AA, &ctx->A, &ctx->A)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART1 | 0x00000003UL));
    }
    else if ((mret = (int32_t)mod_mul(&ctx->AA, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART1 | 0x00000004UL));
    }
    else if ((mret = mbedtls_mpi_sub_mpi(&ctx->B, &pointP->X, &pointP->Z)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART1 | 0x00000005UL));
    }
    else if ((mret = (int32_t)mod_sub(&ctx->B, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART1 | 0x00000006UL));
    }
    else
    {
        /* nop */
    }
    /* polyspace-end */

    return ret;
}

STATICFCN uint8_t part2(const mbedtls_ecp_group* grp,
                        const mbedtls_ecp_point* pointQ,
                        DoubleAddSlicingContext_t* ctx)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_mpi_mul_mpi(&ctx->BB, &ctx->B, &ctx->B)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART2 | 0x00000001UL));
    }
    else if ((mret = (int32_t)mod_mul(&ctx->BB, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART2 | 0x00000002UL));
    }
    else if ((mret = mbedtls_mpi_sub_mpi(&ctx->E, &ctx->AA, &ctx->BB)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART2 | 0x00000003UL));
    }
    else if ((mret = (int32_t)mod_sub(&ctx->E, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART2 | 0x00000004UL));
    }
    else if ((mret = mbedtls_mpi_add_mpi(&ctx->C, &pointQ->X, &pointQ->Z)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART2 | 0x00000005UL));
    }
    else if ((mret = (int32_t)mod_add(&ctx->C, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART2 | 0x00000006UL));
    }
    else if ((mret = mbedtls_mpi_sub_mpi(&ctx->D, &pointQ->X, &pointQ->Z)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART2 | 0x00000007UL));
    }
    else if ((mret = (int32_t)mod_sub(&ctx->D, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART2 | 0x00000008UL));
    }
    else
    {
        /* nop */
    }
    /* polyspace-end */

    return ret;
}

STATICFCN uint8_t part3(const mbedtls_ecp_group* grp,
                        DoubleAddSlicingContext_t* ctx)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_mpi_mul_mpi(&ctx->DA, &ctx->D, &ctx->A)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART3 | 0x00000001UL));
    }
    else if ((mret = (int32_t)mod_mul(&ctx->DA, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART3 | 0x00000002UL));
    }
    else if ((mret = mbedtls_mpi_mul_mpi(&ctx->CB, &ctx->C, &ctx->B)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART3 | 0x00000003UL));
    }
    else
    {
        /* nop */
    }
    /* polyspace-end */

    return ret;
}

STATICFCN uint8_t part4(const mbedtls_ecp_group* grp,
                        mbedtls_ecp_point* S,
                        DoubleAddSlicingContext_t* ctx)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = (int32_t)mod_mul(&ctx->CB, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART4 | 0x00000001UL));
    }
    else if ((mret = mbedtls_mpi_add_mpi(&S->X, &ctx->DA, &ctx->CB)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART4 | 0x00000002UL));
    }
    else if ((mret = (int32_t)mod_mul(&S->X, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART4 | 0x00000003UL));
    }
    else if ((mret = mbedtls_mpi_mul_mpi(&S->X, &S->X, &S->X)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART4 | 0x00000004UL));
    }
    else
    {
        /* nop */
    }
    /* polyspace-end */

    return ret;
}

STATICFCN uint8_t part5(const mbedtls_ecp_group* grp,
                        mbedtls_ecp_point* S,
                        const DoubleAddSlicingContext_t* ctx)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = (int32_t)mod_mul(&S->X, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART5 | 0x00000001UL));
    }
    else if ((mret = mbedtls_mpi_sub_mpi(&S->Z, &ctx->DA, &ctx->CB)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART5 | 0x00000002UL));
    }
    else if ((mret = (int32_t)mod_sub(&S->Z, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART5 | 0x00000003UL));
    }
    else if ((mret = mbedtls_mpi_mul_mpi(&S->Z, &S->Z, &S->Z)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART5 | 0x00000004UL));
    }
    else
    {
        /* nop */
    }
    /* polyspace-end */

    return ret;
}

STATICFCN uint8_t part6(const mbedtls_ecp_group* grp,
                        mbedtls_ecp_point* R,
                        mbedtls_ecp_point* S,
                        const mbedtls_mpi* d,
                        const DoubleAddSlicingContext_t* ctx)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = (int32_t)mod_mul(&S->Z, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART6 | 0x00000001UL));
    }
    else if ((mret = mbedtls_mpi_mul_mpi(&S->Z, d, &S->Z)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART6 | 0x00000002UL));
    }
    else if ((mret = (int32_t)mod_mul(&S->Z, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART6 | 0x00000003UL));
    }
    else if ((mret = mbedtls_mpi_mul_mpi(&R->X, &ctx->AA, &ctx->BB)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART6 | 0x00000004UL));
    }
    else
    {
        /* nop */
    }
    /* polyspace-end */

    return ret;
}

STATICFCN uint8_t part7(const mbedtls_ecp_group* grp,
                        mbedtls_ecp_point* R,
                        const DoubleAddSlicingContext_t* ctx)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = (int32_t)mod_mul(&R->X, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART7 | 0x00000001UL));
    }
    else if ((mret = mbedtls_mpi_mul_mpi(&R->Z, &grp->A, &ctx->E)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART7 | 0x00000002UL));
    }
    else if ((mret = (int32_t)mod_mul(&R->Z, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART7 | 0x00000003UL));
    }
    else if ((mret = mbedtls_mpi_add_mpi(&R->Z, &ctx->BB, &R->Z)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART7 | 0x00000004UL));
    }
    else if ((mret = (int32_t)mod_add(&R->Z, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART7 | 0x00000005UL));
    }
    else if ((mret = mbedtls_mpi_mul_mpi(&R->Z, &ctx->E, &R->Z)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART7 | 0x00000006UL));
    }
    else
    {
        /* nop */
    }
    /* polyspace-end */

    return ret;
}

STATICFCN uint8_t part8(const mbedtls_ecp_group* grp,
                        mbedtls_ecp_point* R)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = (int32_t)mod_mul(&R->Z, grp)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_PART8 | 0x00000008UL));
    }
    else
    {
        /* nop */
    }

    return ret;
}

STATICFCN void CryptoLib_SlicedMontDA_Clean(DoubleAddSlicingContext_t* ctx)
{
    if (MPI_INIT_FLAG_DOUBLE == (uint8_t)1)
    {
        mbedtls_mpi_free(&ctx->A);
        mbedtls_mpi_free(&ctx->AA);
        mbedtls_mpi_free(&ctx->B);
        mbedtls_mpi_free(&ctx->BB);
        mbedtls_mpi_free(&ctx->E);
        mbedtls_mpi_free(&ctx->C);
        mbedtls_mpi_free(&ctx->D);
        mbedtls_mpi_free(&ctx->DA);
        mbedtls_mpi_free(&ctx->CB);
    }

    (void)memset(ctx, 0, sizeof(DoubleAddSlicingContext_t));
}

/*
 * Public function definitions
 * ----------------------------------------------------------------------------
 */
uint8_t CryptoLib_SlicedMontDA(const mbedtls_ecp_group* grp,
                               mbedtls_ecp_point* pointR,
                               mbedtls_ecp_point* pointS,
                               const mbedtls_ecp_point* pointP,
                               const mbedtls_ecp_point* pointQ,
                               const mbedtls_mpi* d,
                               const CryptoLib_Slicing_t slicing)
{
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static DoubleAddSlicingState_t state = IDLE;
    static DoubleAddSlicingContext_t context;
    CRYPTOLIB_MEMORY_SECTION_END

    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slicing == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((grp == NULL) || (pointR == NULL) || (pointS == NULL) ||
             (pointP == NULL) || (pointQ == NULL) || (d == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTDA | 0x00000001UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("d_i,");
                #endif
                if (slicing == SLICE_INIT)
                {
                    /* prepare the context */
                    CryptoLib_SlicedMontDA_Clean(&context);
                    CryptoLib_SlicedMontDA_Init(&context);

                    state = DOUBLE_ADD_PART1;
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTDA | 0x00000002UL));
                    break;
                }
            /* fallthrough */

            case DOUBLE_ADD_PART1:
                #ifdef BENCHMARK_ACTIVE
                BENCH("d_p1,");
                #endif
                ret = part1(grp, pointP, &context);

                /* set to next state if ready, reset in case of errors */
                if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    /* STEP1 done, go to STEP2 */
                    state = DOUBLE_ADD_PART2;
                    ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                }
                break;

            case DOUBLE_ADD_PART2:
                #ifdef BENCHMARK_ACTIVE
                BENCH("d_p2,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    ret = part2(grp, pointQ, &context);

                    /* set to next state if ready, reset in case of errors */
                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* STEP2 done, go to STEP3 */
                        state = DOUBLE_ADD_PART3;
                        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTDA | 0x00000004UL));
                }
                break;

            case DOUBLE_ADD_PART3:
                #ifdef BENCHMARK_ACTIVE
                BENCH("d_p3,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    ret = part3(grp, &context);

                    /* set to next state if ready, reset in case of errors */
                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* STEP3 done, go to STEP4 */
                        state = DOUBLE_ADD_PART4;
                        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTDA | 0x00000006UL));
                }
                break;

            case DOUBLE_ADD_PART4:
                #ifdef BENCHMARK_ACTIVE
                BENCH("d_p4,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    ret = part4(grp, pointS, &context);

                    /* set to next state if ready, reset in case of errors */
                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* STEP4 done, go to STEP5 */
                        state = DOUBLE_ADD_PART5;
                        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTDA | 0x00000008UL));
                }
                break;

            case DOUBLE_ADD_PART5:
                #ifdef BENCHMARK_ACTIVE
                BENCH("d_p5,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    ret = part5(grp, pointS, &context);

                    /* set to next state if ready, reset in case of errors */
                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* STEP5 done, go to STEP6 */
                        state = DOUBLE_ADD_PART6;
                        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTDA | 0x0000000aUL));
                }
                break;

            case DOUBLE_ADD_PART6:
                #ifdef BENCHMARK_ACTIVE
                BENCH("d_p6,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    ret = part6(grp, pointR, pointS, d, &context);

                    /* set to next state if ready, reset in case of errors */
                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* STEP5 done, go to STEP7 */
                        state = DOUBLE_ADD_PART7;
                        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTDA | 0x0000000bUL));
                }
                break;

            case DOUBLE_ADD_PART7:
                #ifdef BENCHMARK_ACTIVE
                BENCH("d_p7,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    ret = part7(grp, pointR, &context);

                    /* set to next state if ready, reset in case of errors */
                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* STEP5 done, go to STEP8 */
                        state = DOUBLE_ADD_PART8;
                        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTDA | 0x0000000cUL));
                }
                break;

            case DOUBLE_ADD_PART8:
                #ifdef BENCHMARK_ACTIVE
                BENCH("d_p8,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    ret = part8(grp, pointR);
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTDA | 0x0000000dUL));
                }
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTDA | 0x0000000eUL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }

    if (ret != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_SlicedMontDA_Clean(&context);
        state = IDLE;
    }

    return ret;
}

#endif

/**
 * @file  src/cryptolib_ecdh_gcd.c
 * @brief ECDH greatest common divisor function definition
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im S<PERSON>yerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_ecdh_gcd.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#include <stdbool.h>
#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if ((CRYPTOLIB_NO_TIME_SLICING == DISABLED) && (CRYPTOLIB_ALT_ECDH == DISABLED))

#define MAX_ITERATIONS_PER_SLICE (size_t)16     /**< Max. iterations per slice */

/** Enum for statemachine states */
typedef enum {
    IDLE = 0, /**< Ready for new calls */
    STEP = 1, /**< Perform loop iterations */
    FINAL = 2 /**< Ready to write output */
} GcdSlicingState_t;

/*
 * Static variables
 * ----------------------------------------------------------------------------
 */
CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t MPI_INIT_FLAG_GCD = 0; /**< Init flag for slicing context */
CRYPTOLIB_MEMORY_SECTION_END

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Prepares a GcdSlicingContext_t context with parent input variables
 *
 * @param[in] intA                  pointer to the first integer mpi
 * @param[in] intB                  pointer to the second integer mpi
 * @param[out] ctx                  pointer to context
 * @param[out] divisor              pointer to divisor mpi
 * @param[out] divisorSet           indicator wheter the divisor was set or not. If true gcd is final
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: context successfully prepared<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedGCD_Prepare(const mbedtls_mpi* intA,
                                              const mbedtls_mpi* intB,
                                              GcdSlicingContext_t* ctx,
                                              mbedtls_mpi* divisor,
                                              bool* divisorSet);

/**
 * Performs loop iterations on ctx for the binary GCD
 *
 * @param[in,out] ctx               pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: divisor successfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedTLS returned error<br>
 */
STATICFCN uint8_t CryptoLib_SlicedGCD_Step(GcdSlicingContext_t* ctx);

/**
 * Finalizes data and writes to parent output
 *
 * @param[out] divisor              pointer to divisor mpi
 * @param[in] ctx                   pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: output written<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedGCD_Finalize(mbedtls_mpi* divisor, GcdSlicingContext_t* ctx);

/**
 * Frees allocated mpis within ctx
 *
 * @param[in,out] ctx               pointer to context
 */
STATICFCN void CryptoLib_SlicedGCD_Clean(GcdSlicingContext_t* ctx);

/*
 * Static function definitions
 * ----------------------------------------------------------------------------
 */
STATICFCN uint8_t CryptoLib_SlicedGCD_Prepare(const mbedtls_mpi* intA,
                                              const mbedtls_mpi* intB,
                                              GcdSlicingContext_t* ctx,
                                              mbedtls_mpi* divisor,
                                              bool* divisorSet)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    *divisorSet = false;

    mbedtls_mpi_init(&ctx->ta);
    mbedtls_mpi_init(&ctx->tb);

    MPI_INIT_FLAG_GCD = 1;

    if ((mbedtls_mpi_copy(&ctx->ta, intA) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_copy(&ctx->tb, intB) != (int32_t)CRYPTO_ERR_SUCCESS))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD_PREPARE | 0x00000001UL));
    }
    else
    {
        ctx->internals.lz = mbedtls_mpi_lsb(&ctx->ta);
        ctx->internals.lzt = mbedtls_mpi_lsb(&ctx->tb);

        if (ctx->internals.lzt < ctx->internals.lz)
        {
            ctx->internals.lz = ctx->internals.lzt;
        }

        /* The loop in CryptoLib_SlicedGCD_Step gives the correct result when A==0
         * but not when B==0.
         * So have a special case for B==0. Leverage the fact that we just
         * calculated the lsb and lsb(B)==0 if B is odd or 0 to make the test
         * slightly more efficient than cmp_int(). */
        if ((ctx->internals.lzt == 0u) && (mbedtls_mpi_get_bit(&ctx->tb, (size_t)0u) == 0))
        {
            int32_t mret = mbedtls_mpi_copy(divisor, &ctx->ta);

            if (mret != (int32_t)CRYPTO_ERR_SUCCESS)
            {
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD_PREPARE | 0x00000003UL));
            }
            else
            {
                retval = (uint8_t)CRYPTO_ERR_SUCCESS;
                *divisorSet = true;
            }
        }
        else
        {
            ctx->ta.s = 1;
            ctx->tb.s = 1;
        }
    }

    return retval;
}

STATICFCN uint8_t CryptoLib_SlicedGCD_Step(GcdSlicingContext_t* ctx)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    ctx->internals.lc = 0;

    /* continue as long as ta is not 0, max iterations are not reached and no error occured */
    while ((mbedtls_mpi_cmp_int(&ctx->ta, (mbedtls_mpi_sint)0) != 0) &&
           (ctx->internals.lc < MAX_ITERATIONS_PER_SLICE) &&
           (retval == (uint8_t)CRYPTO_ERR_SUCCESS))
    {
        ctx->internals.lc++;

        if (mbedtls_mpi_shift_r(&ctx->ta, mbedtls_mpi_lsb(&ctx->ta)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD_STEP | 0x00000001UL));
        }
        else if (mbedtls_mpi_shift_r(&ctx->tb, mbedtls_mpi_lsb(&ctx->tb)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD_STEP | 0x00000002UL));
        }
        else
        {
            if (mbedtls_mpi_cmp_mpi(&ctx->ta, &ctx->tb) >= 0)
            {
                /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
                if ((mret = mbedtls_mpi_sub_abs(&ctx->ta, &ctx->ta, &ctx->tb)) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&retval, mret, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD_STEP | 0x00000003UL));
                }
                else if (mbedtls_mpi_shift_r(&ctx->ta, (mbedtls_mpi_sint)1) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD_STEP | 0x00000004UL));
                }
                else
                {
                    /* nop */
                }
            }
            else
            {
                /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
                if ((mret = mbedtls_mpi_sub_abs(&ctx->tb, &ctx->tb, &ctx->ta)) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&retval, mret, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD_STEP | 0x00000005UL));
                }
                else if (mbedtls_mpi_shift_r(&ctx->tb, (mbedtls_mpi_sint)1) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD_STEP | 0x00000006UL));
                }
                else
                {
                    /* nop */
                }
            }
        }
    }

    /* if successful so far, find out if loop broke because of iteration
     * limit or calculation status */
    if ((retval == (uint8_t)CRYPTO_ERR_SUCCESS) &&
        (mbedtls_mpi_cmp_int(&ctx->ta, (mbedtls_mpi_sint)0) != 0))
    {
        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
    }

    return retval;
}

STATICFCN uint8_t CryptoLib_SlicedGCD_Finalize(mbedtls_mpi* divisor, GcdSlicingContext_t* ctx)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if ((mbedtls_mpi_shift_l(&ctx->tb, ctx->internals.lz) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_copy(divisor, &ctx->tb) != (int32_t)CRYPTO_ERR_SUCCESS))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD_FINALIZE | 0x00000001UL));
    }

    return retval;
}

STATICFCN void CryptoLib_SlicedGCD_Clean(GcdSlicingContext_t* ctx)
{
    if (MPI_INIT_FLAG_GCD == 1u)
    {
        mbedtls_mpi_free(&ctx->ta);
        mbedtls_mpi_free(&ctx->tb);
    }

    (void)memset(ctx, 0, sizeof(GcdSlicingContext_t));
}

/*
 * Public function definitions
 * ----------------------------------------------------------------------------
 */
uint8_t CryptoLib_SlicedGCD(mbedtls_mpi* divisor,
                            const mbedtls_mpi* integerA,
                            const mbedtls_mpi* integerB,
                            const CryptoLib_Slicing_t slicing)
{
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static GcdSlicingState_t state = IDLE;
    static GcdSlicingContext_t context;
    CRYPTOLIB_MEMORY_SECTION_END

    CryptoLib_Slicing_t slc = slicing; /* write access to slicing needed */
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slc == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((divisor == NULL) || (integerA == NULL) || (integerB == NULL))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD | 0x00000001UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("g_i,");
                #endif
                if (slc == SLICE_INIT)
                {
                    bool divisorSet = false;
                    CryptoLib_SlicedGCD_Clean(&context);
                    retval = CryptoLib_SlicedGCD_Prepare(integerA, integerB, &context, divisor, &divisorSet);

                    /* set to next state if ready, reset in case of errors */
                    if ((retval == (uint8_t)CRYPTO_ERR_SUCCESS) && (!divisorSet))
                    {
                        /* Init done, go to STEP */
                        state = STEP;
                        slc = SLICE_CONTINUE;
                    }
                    else
                    {
                        /* Either call again is expected or an error occurred or the divisor has been computed already in this step */
                        break;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD | 0x00000002UL));
                    break;
                }
            /* fallthrough */

            case STEP:
                #ifdef BENCHMARK_ACTIVE
                BENCH("g_s,");
                #endif
                if (slc == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedGCD_Step(&context);

                    /* set to next state if ready, reset in case of errors */
                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* STEP done, go to FINAL */
                        state = FINAL;
                    }
                    else
                    {
                        break;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD | 0x00000003UL));
                    break;
                }
            /* fallthrough */

            case FINAL:
                #ifdef BENCHMARK_ACTIVE
                BENCH("g_f,");
                #endif
                retval = CryptoLib_SlicedGCD_Finalize(divisor, &context);
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                /* something went terribly wrong to reach this case. reset */
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDGCD | 0x00000005UL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }

    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_SlicedGCD_Clean(&context);
        state = IDLE;
    }

    return retval;
}

#endif

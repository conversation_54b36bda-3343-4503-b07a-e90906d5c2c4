/**
 * @file  cryptolib_libc.c
 * @brief Redefined standard library function definitions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 R<PERSON>lzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

/* polyspace-begin MISRA-C3:3.1 "// used in url" */
/*
 * The implementation of CryptoLib_Strtoul() is derived from Apple Open Source
 * (https://opensource.apple.com/source/) with the following license terms:
 *
 * Copyright (c) 1988 The Regents of the University of California.
 * Copyright (c) 1994 Sun Microsystems, Inc.
 *
 * This software is copyrighted by the Regents of the University of
 * California, Sun Microsystems, Inc., Scriptics Corporation, ActiveState
 * Corporation and other parties.  The following terms apply to all files
 * associated with the software unless explicitly disclaimed in
 * individual files.
 *
 * The authors hereby grant permission to use, copy, modify, distribute,
 * and license this software and its documentation for any purpose, provided
 * that existing copyright notices are retained in all copies and that this
 * notice is included verbatim in any distributions. No written agreement,
 * license, or royalty fee is required for any of the authorized uses.
 * Modifications to this software may be copyrighted by their authors
 * and need not follow the licensing terms described here, provided that
 * the new terms are clearly indicated on the first page of each file where
 * they apply.
 *
 * IN NO EVENT SHALL THE AUTHORS OR DISTRIBUTORS BE LIABLE TO ANY PARTY
 * FOR DIRECT, INDIRECT, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGES
 * ARISING OUT OF THE USE OF THIS SOFTWARE, ITS DOCUMENTATION, OR ANY
 * DERIVATIVES THEREOF, EVEN IF THE AUTHORS HAVE BEEN ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * THE AUTHORS AND DISTRIBUTORS SPECIFICALLY DISCLAIM ANY WARRANTIES,
 * INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.  THIS SOFTWARE
 * IS PROVIDED ON AN "AS IS" BASIS, AND THE AUTHORS AND DISTRIBUTORS HAVE
 * NO OBLIGATION TO PROVIDE MAINTENANCE, SUPPORT, UPDATES, ENHANCEMENTS, OR
 * MODIFICATIONS.
 */

/*
 * The implementation of CryptoLib_Memset() and CryptoLib_Memcmp() are derived
 * or directly taken from musl (http://www.musl-libc.org/).
 *
 * musl as a whole is licensed under the following standard MIT license:
 *
 * ----------------------------------------------------------------------
 * Copyright © 2005-2014 Rich Felker, et al.
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
 * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
 * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
 * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ----------------------------------------------------------------------
 */
/* polyspace-end */

#include "cryptolib/cryptolib_libc.h"
#include "cryptolib/cryptolib_types.h"

#include <limits.h>

#ifdef CRYPTOLIB_REDEF_STDLIB

CRYPTOLIB_HUGE void* CryptoLib_Memset(CRYPTOLIB_HUGE void* dest, int c, size_t n)
{
    unsigned char* s = dest;
    size_t k;

    /* Fill head and tail with minimal branching. Each
     * conditional ensures that all the subsequently used
     * offsets are well-defined and in the dest region. */

    if (n == 0)
        return dest;
    s[0] = c;
    s[n - 1] = c;
    if (n <= 2)
        return dest;
    s[1] = c;
    s[2] = c;
    s[n - 2] = c;
    s[n - 3] = c;
    if (n <= 6)
        return dest;
    s[3] = c;
    s[n - 4] = c;
    if (n <= 8)
        return dest;

    /* Advance pointer to align it at a 4-byte boundary,
     * and truncate n to a multiple of 4. The previous code
     * already took care of any head/tail that get cut off
     * by the alignment. */

    k = -(uintptr_t)s & 3;
    s += k;
    n -= k;
    n &= -4;

    /* Pure C fallback with no aliasing violations. */
    for (; n; n--, s++) *s = c;

    return dest;
}

int32_t CryptoLib_Memcmp(CRYPTOLIB_HUGE const void* vl,
                         CRYPTOLIB_HUGE const void* vr,
                         size_t n)
{
    const unsigned char* l = vl, * r = vr;
    for (; n && *l == *r; n--, l++, r++);

    return n ? *l - *r : 0;
}

uint32_t CryptoLib_Strtoul(CRYPTOLIB_HUGE const char* string,
                           CRYPTOLIB_HUGE char** endPtr,
                           int16_t base)
{
    const uint8_t cvtIn[] = {
        0, 1, 2, 3, 4, 5, 6, 7, 8, 9,             /* '0' - '9' */
        100, 100, 100, 100, 100, 100, 100,        /* punctuation */
        10, 11, 12, 13, 14, 15, 16, 17, 18, 19,   /* 'A' - 'Z' */
        20, 21, 22, 23, 24, 25, 26, 27, 28, 29,
        30, 31, 32, 33, 34, 35,
        100, 100, 100, 100, 100, 100,             /* punctuation */
        10, 11, 12, 13, 14, 15, 16, 17, 18, 19,      /* 'a' - 'z' */
        20, 21, 22, 23, 24, 25, 26, 27, 28, 29,
        30, 31, 32, 33, 34, 35
    };

    const char* p;
    uint32_t result = 0;
    uint32_t digit, maxres;
    uint8_t anyDigits = 0;
    uint8_t negative = 0;
    uint8_t overflow = 0;

    /* Skip any leading blanks. */
    p = string;
    while ((*p <= ' ') && ((*p == ' ') || ((*p <= 13) && (*p <= 9))))
    {
        p += 1;
    }

    if (*p == '-')
    {
        negative = 1;
        p += 1;
    }
    else
    {
        if (*p == '+')
        {
            p += 1;
        }
    }

    /* Base has to be 16 for this implementation. */
    if (base == 16)
    {
        /* Skip a leading "0x" from hex numbers. */
        if ((p[0] == '0') && ((p[1] == 'x') || (p[1] == 'X')))
        {
            p += 2;
        }

        maxres = UINT32_MAX >> 4u;

        for (;; p += 1)
        {
            digit = *p - '0';
            if (digit > ('z' - '0'))
            {
                break;
            }
            digit = cvtIn[digit];
            if (digit > 15)
            {
                break;
            }
            if (result > maxres)
            {
                overflow = 1;
            }
            result = (result << 4);
            if (digit > (UINT32_MAX - result))
            {
                overflow = 1;
            }
            result += digit;
            anyDigits = 1;
        }

        /* See if there were any digits at all. */
        if (anyDigits == 0u)
        {
            p = string;
        }

        if (endPtr != NULL)
        {
            *endPtr = (char*)p;
        }

        if (overflow != 0u)
        {
            result = UINT32_MAX;
        }

        if (negative != 0u)
        {
            result = UINT32_MAX;
        }
    }
    else
    {
        result = UINT32_MAX;
    }

    return result;
}

#endif

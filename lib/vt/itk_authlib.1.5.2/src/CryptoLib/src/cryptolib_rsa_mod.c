/**
 * @file  src/cryptolib_rsa_mod.c
 * @brief CryptoLib_SlicedMpiModMpi function definition
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_rsa_mod.h"
#include "cryptolib/cryptolib_rsa_div.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#if (CRYPTOLIB_NO_TIME_SLICING == DISABLED)

/** Enum for statemachine states */
typedef enum {
    IDLE = 0, /**< Ready for new calls */
    DIV = 1, /**< Perform sliced division */
    FINAL = 2 /**< Finalize output */
} ModSlicingState_t;

/** Enum for division sub-statemachine states */
typedef enum {
    DIV_IDLE = 0, /**< No sliced Div active */
    DIV_ACTIVE = 1 /**< Sliced Div active */
} DivSlicingState_t;

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Performs R+=B as long as R is less than 0. Performs R-=B as long as R is greater than B.
 *
 * @param[out] R                    Resulting mpi
 * @param[in] B                     MPI B
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: calculation completed sucessfully<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t MpiDivMpi_Finalize(mbedtls_mpi* R, const mbedtls_mpi* B);

/**
 * Manages the sliced division states
 *
 * @param[out] R                    Resulting mpi
 * @param[in] A                     MPI A
 * @param[in] B                     MPI B
 * @param[in] slicing               1 - SLICE_CONTINUE: during computation<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: calculation completed sucessfully<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: more slices needed, call again<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer given<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid parameter or bad input data<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
STATICFCN uint8_t MpiDivMpi_Divide(mbedtls_mpi* R,
                                   const mbedtls_mpi* A,
                                   const mbedtls_mpi* B,
                                   const CryptoLib_Slicing_t slicing);

/**
 * Cleanup function for MpiDivMpi_Divide
 *
 * @param[in,out] state            State of the Sliced function to be reset if
 *                                 not IDLE
 */
STATICFCN void MpiDivMpi_Divide_Reset(DivSlicingState_t* state);

/*
 * Static function definitions
 * ----------------------------------------------------------------------------
 */
STATICFCN uint8_t MpiDivMpi_Finalize(mbedtls_mpi* R, const mbedtls_mpi* B)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    while (  (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
          && (mbedtls_mpi_cmp_int(R, (mbedtls_mpi_sint)0) < (mbedtls_mpi_sint)0))
    {
        /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((mret = mbedtls_mpi_add_mpi(R, R, B)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retval, mret, (uint32_t)(FID_MPIDIVMPI_FINALIZE | 0x00000002UL));
        }
    }

    while ((retval == (uint8_t)CRYPTO_ERR_SUCCESS) && (mbedtls_mpi_cmp_mpi(R, B) >= 0))
    {
        /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((mret = mbedtls_mpi_sub_mpi(R, R, B)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retval, mret, (uint32_t)(FID_MPIDIVMPI_FINALIZE | 0x00000003UL));
        }
    }

    return retval;
}

STATICFCN void MpiDivMpi_Divide_Reset(DivSlicingState_t* state)
{
    if (*state != DIV_IDLE)
    {
        (void)CryptoLib_SlicedMpiDivMpi(NULL, NULL, NULL, NULL, SLICE_RESET);
    }

    *state = DIV_IDLE;
}

STATICFCN uint8_t MpiDivMpi_Divide(mbedtls_mpi* R,
                                   const mbedtls_mpi* A,
                                   const mbedtls_mpi* B,
                                   const CryptoLib_Slicing_t slicing)
{
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static DivSlicingState_t state = DIV_IDLE;
    CRYPTOLIB_MEMORY_SECTION_END

    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slicing == SLICE_RESET)
    {
        MpiDivMpi_Divide_Reset(&state);
    }
    else if (slicing == SLICE_CONTINUE)
    {
        if (state == DIV_IDLE)
        {
            retval = CryptoLib_SlicedMpiDivMpi(NULL, R, A, B, SLICE_INIT);

            if (retval == (uint8_t)CRYPTO_ERR_CALLAGAIN)
            {
                state = DIV_ACTIVE;
            }
        }
        else if (state == DIV_ACTIVE)
        {
            retval = CryptoLib_SlicedMpiDivMpi(NULL, R, A, B, SLICE_CONTINUE);

            if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
            {
                state = DIV_IDLE;
            }
        }
        else
        {
            /* LCOV_EXCL_START: internal enum out of bounds */
            CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_MPIDIVMPI_DIVIDE | 0x00000001UL));
            MpiDivMpi_Divide_Reset(&state);
            /* LCOV_EXCL_STOP */
        }
    }
    else
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_MPIDIVMPI_DIVIDE | 0x00000002UL));
        MpiDivMpi_Divide_Reset(&state);
    }

    return retval;
}

/*
 * Public function definitions
 * ----------------------------------------------------------------------------
 */
uint8_t CryptoLib_SlicedMpiModMpi(mbedtls_mpi* R,
                                  const mbedtls_mpi* A,
                                  const mbedtls_mpi* B,
                                  const CryptoLib_Slicing_t slicing)
{
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static ModSlicingState_t state = IDLE;
    CRYPTOLIB_MEMORY_SECTION_END

    CryptoLib_Slicing_t slc = slicing;
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slc == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((R == NULL) || (A == NULL) || (B == NULL))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIMODMPI | 0x00000001UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("mod_i,");
                #endif
                if (slc == SLICE_INIT)
                {
                    if (mbedtls_mpi_cmp_int(B, (mbedtls_mpi_sint)0) < 0)
                    {
                        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIMODMPI | 0x00000002UL));
                        break;
                    }
                    else
                    {
                        state = DIV;
                        slc = SLICE_CONTINUE;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIMODMPI | 0x00000003UL));
                    break;
                }
            /* fallthrough */

            case DIV:
                if (slc == SLICE_CONTINUE)
                {
                    retval = MpiDivMpi_Divide(R, A, B, slc);

                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = FINAL;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIMODMPI | 0x00000005UL));
                }
                break;

            case FINAL:
                #ifdef BENCHMARK_ACTIVE
                BENCH("mod_f,");
                #endif
                if (slc == SLICE_CONTINUE)
                {
                    retval = MpiDivMpi_Finalize(R, B);
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIMODMPI | 0x00000007UL));
                }

                state = IDLE;
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIMODMPI | 0x00000008UL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }

    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        (void)MpiDivMpi_Divide(NULL, NULL, NULL, SLICE_RESET);
        state = IDLE;
    }

    return retval;
}

#endif

/**
 * @file  src/cryptolib_asn1.c
 * @brief ASN.1 function definitions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON>lzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_asn1.h"

#include "mbedtls/asn1.h"

/**
 * Get the length of an ASN.1 element. Updates the pointer to immediately behind the length.
 * In contrast to mbedtls_asn1_get_len the length of the buffer is not checked. I.e. if
 * the buffer is shorter than the length of the ASN.1 element no error is thrown.
 *
 * @param[out] p     The position in the ASN.1 data
 * @param[in]  end   End of data
 * @param[out] len   The variable that will receive the value
 *
 * @return      0 if successful, MBEDTLS_ERR_ASN1_OUT_OF_DATA on reaching
 *              end of data, MBEDTLS_ERR_ASN1_INVALID_LENGTH if length is
 *              unparseable.
 */
STATICFCN int16_t CryptoLib_Asn1GetLen(const uint8_t** p,
                                       const uint8_t* end,
                                       uint32_t* len);

/*
 * ASN.1 DER decoding routines
 */
STATICFCN int16_t CryptoLib_Asn1GetLen(const uint8_t** p,
                                       const uint8_t* end,
                                       uint32_t* len)
{
    int16_t ret = 0;

    /* polyspace-begin MISRA-C3:18.4 [No action planned:Low] "The pointer arithmetic here is more readable than the array indexing." */
    if ((uint32_t)(end - *p) < 1u)
    {
        ret = (int16_t)MBEDTLS_ERR_ASN1_OUT_OF_DATA;
    }
    else
    {
        if ((**p & (uint8_t)ASN1_INDEFINITE_LEN) == 0u)
        {
            *len = **p;
            (*p)++;
        }
        else
        {
            switch (**p & (uint8_t)ASN1_DEFINITE_LEN_MASK)
            {
                case 1:
                    if ((uint32_t)(end - *p) < 2u)
                    {
                        ret = (int16_t)MBEDTLS_ERR_ASN1_OUT_OF_DATA;
                    }
                    else
                    {
                        *len = (*p)[1];
                        (*p) += 2;
                    }
                    break;

                case 2:
                    if ((uint32_t)(end - *p) < 3u)
                    {
                        ret = (int16_t)MBEDTLS_ERR_ASN1_OUT_OF_DATA;
                    }
                    else
                    {
                        *len = ((uint32_t)(*p)[1] << 8) | (*p)[2];
                        (*p) += 3;
                    }
                    break;

                case 3:
                    if ((uint32_t)(end - *p) < 4u)
                    {
                        ret = (int16_t)MBEDTLS_ERR_ASN1_OUT_OF_DATA;
                    }
                    else
                    {
                        *len = ((uint32_t)(*p)[1] << 16) |
                               ((uint32_t)(*p)[2] << 8) | (*p)[3];
                        (*p) += 4;
                    }
                    break;

                case 4:
                    if ((uint32_t)(end - *p) < 5u)
                    {
                        ret = (int16_t)MBEDTLS_ERR_ASN1_OUT_OF_DATA;
                    }
                    else
                    {
                        *len = ((uint32_t)(*p)[1] << 24) | ((uint32_t)(*p)[2] << 16) |
                               ((uint32_t)(*p)[3] << 8) | (*p)[4];
                        (*p) += 5;
                    }
                    break;

                default:
                    ret = (int16_t)MBEDTLS_ERR_ASN1_INVALID_LENGTH;
                    break;
            }
        }
    }
    /* polyspace-end */

    return ret;
}

int16_t CryptoLib_Asn1GetTag(const uint8_t** p,
                             const uint8_t* end,
                             uint32_t* len, uint8_t tag)
{
    int16_t ret = 0;

    /* polyspace-begin MISRA-C3:18.4 [No action planned:Low] "The pointer arithmetic here is more readable than the array indexing." */
    if ((end - *p) < 1)
    {
        ret = (int16_t)MBEDTLS_ERR_ASN1_OUT_OF_DATA;
    }
    else
    {
        if (**p != tag)
        {
            ret = (int16_t)MBEDTLS_ERR_ASN1_UNEXPECTED_TAG;
        }
        else
        {
            (*p)++;

            ret = CryptoLib_Asn1GetLen(p, end, len);
        }
    }
    /* polyspace-end */

    return ret;
}

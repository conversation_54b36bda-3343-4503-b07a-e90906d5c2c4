/**
 * @file  src/cryptolib_rng.c
 * @brief RNG function definitions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_rng.h"

#include "cryptolib/cryptolib_fid.h"
#include "cryptolib/cryptolib_diag.h"

#if (CRYPTOLIB_ALT_RNG == DISABLED)
#include "mbedtls/ctr_drbg.h"
#include "mbedtls/entropy.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#define THRESHOLD (size_t)16 /**< Minimum bytes required before release */

CRYPTOLIB_MEMORY_SECTION_BEGIN
static const uint8_t* statSeed = NULL; /**< seed storage */
static uint16_t statSeedLen = 0; /**< Length of stored seed */
static uint8_t randomInit = 0; /**< Flag that inidicates if RND is initilized */ /* polyspace MISRA-C3:8.9 "Kept global for better readability and maintainability." */
static mbedtls_ctr_drbg_context ctr_drbg_ctx; /**< Context of RNG */
static mbedtls_entropy_context entropy_ctx; /**< Context of entropy source */
CRYPTOLIB_MEMORY_SECTION_END

#if (CRYPTOLIB_USE_SYSTEM_ENTROPY == DISABLED)

/* polyspace-begin MISRA-C3:D4.6 [No action planned:Low] "mbedtls_entropy_f_source_ptr reqiures functions with return type int
 *                                                         and unsigned char for the output (return value is always 0)" */

/* polyspace-begin MISRA-C3:8.13 [No action planned:Low] "mbedtls requires function pointer with data although it is not used here" */

/**
 * Entropy callback function that copies the given seed to the output buffer
 * (function protoype is in accordance with mbedtls_entropy_f_source_ptr)
 *
 * @param[in]  data          callback-specific data pointer (not used)
 * @param[in]  len           maximum size to provide (not used)
 * @param[out] output        output buffer (seed)
 * @param[out] olen          actual amount of bytes put into the buffer (seedLen)
 *
 * @return                   0 - CRYPTO_ERR_SUCCESS<br>
 *                           7 - CRYPTO_ERR_NULL_POINTER<br>
 */
STATICFCN int CryptoLib_EntropySource(void* data, unsigned char* output, size_t len, size_t* olen);

STATICFCN int CryptoLib_EntropySource(void* data, unsigned char* output, size_t len, size_t* olen)
{
    (void)data;
    (void)len;

    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (statSeed == NULL)
    {
        *olen = 0;
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_ENTROPYSOURCE | 0x00000001UL));
    }
    else
    {
        (void)memcpy(output, statSeed, (size_t)statSeedLen);
        *olen = statSeedLen;
    }

    return (int)retVal;
}

/* polyspace-end */
/* polyspace-end */

#endif  /* (CRYPTOLIB_USE_SYSTEM_ENTROPY == DISABLED) */

STATICFCN uint8_t CryptoLib_Seed(CRYPTOLIB_HUGE const uint8_t* seed,
                                 const uint16_t seedLen)
{
    (void)seedLen;
    const char* const personalization = "P9r5nf8S93lFv5gK";

    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

#if (CRYPTOLIB_USE_SYSTEM_ENTROPY == DISABLED)
    if ((seed == NULL) && (randomInit == 0u))
    {
        retVal = (uint8_t)CRYPTO_ERR_RESEED;
    }
    else if ((seed != NULL) && (randomInit == 1u))
    {
        /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((mret = mbedtls_ctr_drbg_reseed(&ctr_drbg_ctx, NULL, (size_t)0)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retVal, mret, (uint32_t)(FID_CRYPTOLIB_SEED | 0x00000002UL));
        }
    }
    else
#endif /* (CRYPTOLIB_USE_SYSTEM_ENTROPY == DISABLED) */
    if (randomInit == 0u)
    {
        mbedtls_entropy_init(&entropy_ctx);
        mbedtls_ctr_drbg_init(&ctr_drbg_ctx);

        /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
#if (CRYPTOLIB_USE_SYSTEM_ENTROPY == DISABLED)
        if ((mret = mbedtls_entropy_add_source(&entropy_ctx, CryptoLib_EntropySource,
                                               NULL, THRESHOLD, MBEDTLS_ENTROPY_SOURCE_STRONG)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retVal, mret, (uint32_t)(FID_CRYPTOLIB_SEED | 0x00000003UL));
        }
        else /* else if */
#endif /* (CRYPTOLIB_USE_SYSTEM_ENTROPY == DISABLED) */
        if ((mret = mbedtls_ctr_drbg_seed(&ctr_drbg_ctx, mbedtls_entropy_func, &entropy_ctx,
                                          (const uint8_t*)personalization,
                                          strlen((const char*)personalization))) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retVal, mret, (uint32_t)(FID_CRYPTOLIB_SEED | 0x00000004UL));
        }
        else
        {
            randomInit = 1u;
        }
        /* polyspace-end */
    }
    else
    {
        /* nothing to do here */
    }

    return retVal;
}

STATICFCN uint8_t CryptoLib_GetRandom(CRYPTOLIB_HUGE uint8_t* buf, const uint16_t bufLen)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if ((mret = mbedtls_ctr_drbg_random(&ctr_drbg_ctx, buf, (size_t)bufLen)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retVal, mret, (uint32_t)(FID_CRYPTOLIB_GETRANDOM | 0x00000005UL));
        if (mret == MBEDTLS_ERR_CTR_DRBG_ENTROPY_SOURCE_FAILED)
        {
            retVal = (uint8_t)CRYPTO_ERR_RESEED;
        }
    }

    return retVal;
}

uint8_t CryptoLib_Random(CRYPTOLIB_HUGE uint8_t* buf,
                         const uint16_t bufLen,
                         CRYPTOLIB_HUGE const uint8_t* seed,
                         const uint16_t seedLen)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    statSeed = seed;
    statSeedLen = seedLen;

    if (buf == NULL)
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_RANDOM | 0x00000001UL));
    }
    else
    {
        retVal = CryptoLib_Seed(seed, seedLen);
    }

    if (retVal == (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        retVal = CryptoLib_GetRandom(buf, bufLen);
    }

    statSeed = NULL;
    statSeedLen = 0;

    return retVal;
}

#endif /* (CRYPTOLIB_ALT_RNG == DISABLED) */

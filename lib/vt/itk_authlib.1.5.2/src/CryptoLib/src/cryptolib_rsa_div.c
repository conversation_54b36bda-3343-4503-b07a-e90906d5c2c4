/**
 * @file  src/cryptolib_rsa_div.c
 * @brief Implementation of sliced multi-precision division
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_rsa_div.h"
#include "cryptolib/cryptolib_common.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"
#include "cryptolib/cryptolib_mbedtls_config.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if (CRYPTOLIB_NO_TIME_SLICING == DISABLED)

#define MAX_ITERATIONS_PER_SLICE (size_t)1 /**< Max. iterations per slice */

CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t MPI_INIT_FLAG_RSA_DIV = 0; /**< Init flag for slicing context */
CRYPTOLIB_MEMORY_SECTION_END

/**
 * Resets the slicing context struct. This includes freeing the bignums if needed.
 *
 * @param[out] context  Pointer to the slicing context
 */
STATICFCN void CryptoLib_SlicedMpiDivMpi_Clean(MpiDivSlicingContext_t* context);

/**
 * Unsigned integer divide - double mbedtls_mpi_uint dividend, u1/u0, and
 * mbedtls_mpi_uint divisor, d
 *
 * @param[in]  u1    Integer value of nominator
 * @param[in]  u0    Integer value of denominator
 * @param[in]  d     Integer value of divisor
 * @param[out] r     Pointer to integer to which the remainder is written to.
 *
 * @return           The quotient
 */
STATICFCN mbedtls_mpi_uint int_div_int(mbedtls_mpi_uint u1, mbedtls_mpi_uint u0,
                                       mbedtls_mpi_uint d, mbedtls_mpi_uint* r);

/**
 * Prepare needed variables for the main computation (initialization slice).
 * Detects if a division by zero would occur. If A < B the output values are
 * set as follow: Q (quotient) = 0, R (remainder) = A.
 *
 * @param[out] Q        Pointer to the quotient
 * @param[out] R        Pointer to the remainder
 * @param[in]  A        Pointer to the nominator
 * @param[in]  B        Pointer to the denominator
 * @param[out] context  Pointer to the slicing context
 *
 * @return              0 - CRYPTO_ERR_SUCCES: A < B, result already computed<br>
 *                      2 - CRYPTO_ERR_CALLAGAIN: initialization successful<br>
 *                      12 - CRYPTO_ERR_BAD_INPUT: bad input, e.g. B == 0<br>
 *                      14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedMpiDivMpi_Prepare(mbedtls_mpi* Q, mbedtls_mpi* R,
                                                    const mbedtls_mpi* A, const mbedtls_mpi* B,
                                                    MpiDivSlicingContext_t* context);

/**
 * Final step of algorithm, copies the result into the proper variables.
 *
 * @param[out] Q        Pointer to the quotient
 * @param[out] R        Pointer to the remainder
 * @param[in] A         Pointer to the nominator
 * @param[in] B         Pointer to the denominator
 * @param[in] context   Pointer to the slicing context
 *
 * @return              0 - CRYPTO_ERR_SUCCESS: operation successful<br>
 *                      14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedMpiDivMpi_finalize(mbedtls_mpi* Q, mbedtls_mpi* R,
                                                     const mbedtls_mpi* A, const mbedtls_mpi* B,
                                                     MpiDivSlicingContext_t* context);

/**
 * Perform one step of the sliced functionality.
 *
 * @param[out] context  Pointer to the slicing context
 *
 * @return              0 - CRYPTO_ERR_SUCCESS: computation is complete<br>
 *                      2 - CRYPTO_ERR_CALLAGAIN: more slices are needed<br>
 *                      14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedMpiDivMpi_Step(MpiDivSlicingContext_t* context);

#if !defined(MBEDTLS_HAVE_UDBL)

/**
 * Count leading zero bits in a given integer. Copy of mbedtls function.
 */
STATICFCN size_t clz(const mbedtls_mpi_uint x)
{
    size_t j;
    mbedtls_mpi_uint mask = (mbedtls_mpi_uint)1 << (biL - 1u);

    for (j = 0; j < biL; j++)
    {
        if ((x & mask) != 0u)
        {
            break;
        }

        mask >>= 1;
    }

    return j;
}

#endif

/*
 * Unsigned integer divide - double mbedtls_mpi_uint dividend, u1/u0, and
 * mbedtls_mpi_uint divisor, d
 * Copy of mbedtls_int_div_int
 */
STATICFCN mbedtls_mpi_uint int_div_int(mbedtls_mpi_uint u1, mbedtls_mpi_uint u0,
                                       mbedtls_mpi_uint d, mbedtls_mpi_uint* r)
{
#if defined(MBEDTLS_HAVE_UDBL)
    mbedtls_t_udbl ret = 0;
    mbedtls_t_udbl dividend, quotient;
#else
    const mbedtls_mpi_uint radix = (mbedtls_mpi_uint)1 << biH;
    const mbedtls_mpi_uint uint_halfword_mask = ((mbedtls_mpi_uint)1 << biH) - 1u;
    mbedtls_mpi_uint d0, d1, q0, q1, rAX, r0, quotient;
    mbedtls_mpi_uint u0_msw, u0_lsw;
    mbedtls_mpi_uint ret;
    size_t s;
#endif

    /*
     * Check for overflow
     */
    if (((mbedtls_mpi_uint)0 == d) || (u1 >= d))
    {
        if (r != NULL)
        {
            *r = ~0u;
        }

        ret = ~0u;
    }
    else
    {
#if defined(MBEDTLS_HAVE_UDBL)
        dividend = (mbedtls_t_udbl)u1 << biL;
        dividend |= (mbedtls_t_udbl)u0;
        quotient = dividend / (mbedtls_t_udbl)d;

        if (quotient > (((mbedtls_t_udbl)1 << biL) - (mbedtls_t_udbl)1))
        {
            /* LCOV_EXCL_START: Code is unreachable.
             * Since the code is taken from mbedtls, it is maintained for completeness. */
            quotient = ((mbedtls_t_udbl)1 << biL) - (mbedtls_t_udbl)1;
            /* LCOV_EXCL_STOP */
        }

        if (r != NULL)
        {
            *r = (mbedtls_mpi_uint)(dividend - (quotient * d));
        }

        ret = quotient;
#else

        /*
         * Algorithm D, Section 4.3.1 - The Art of Computer Programming
         *   Vol. 2 - Seminumerical Algorithms, Knuth
         */

        /*
         * Normalize the divisor, d, and dividend, u0, u1
         */
        s = clz(d);
        d = d << s;

        u1 = u1 << s;
        u1 |= (u0 >> (biL - s)) & (-(mbedtls_mpi_sint)s >> (biL - 1u));
        u0 = u0 << s;

        d1 = d >> biH;
        d0 = d & uint_halfword_mask;

        u0_msw = u0 >> biH;
        u0_lsw = u0 & uint_halfword_mask;

        /*
         * Find the first quotient and remainder
         */
        q1 = u1 / d1;
        r0 = u1 - (d1 * q1);

        while ((q1 >= radix) || ((q1 * d0) > ((radix * r0) + u0_msw)))
        {
            q1 -= 1u;
            r0 += d1;

            if (r0 >= radix)
            {
                break;
            }
        }

        rAX = (u1 * radix) + (u0_msw - (q1 * d));
        q0 = rAX / d1;
        r0 = rAX - (q0 * d1);

        while ((q0 >= radix) || ((q0 * d0) > ((radix * r0) + u0_lsw)))
        {
            q0 -= 1u;
            r0 += d1;

            if (r0 >= radix)
            {
                break;
            }
        }

        if (r != NULL)
        {
            *r = ((rAX * radix) + u0_lsw - (q0 * d)) >> s;
        }

        quotient = (q1 * radix) + q0;

        ret = quotient;
#endif
    }

    return (mbedtls_mpi_uint)ret;
}

STATICFCN uint8_t CryptoLib_SlicedMpiDivMpi_Prepare(mbedtls_mpi* Q, mbedtls_mpi* R,
                                                    const mbedtls_mpi* A, const mbedtls_mpi* B,
                                                    MpiDivSlicingContext_t* context)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;

    mbedtls_mpi_init(&context->X);
    mbedtls_mpi_init(&context->Y);
    mbedtls_mpi_init(&context->Z);
    mbedtls_mpi_init(&context->T1);
    mbedtls_mpi_init(&context->T2);

    MPI_INIT_FLAG_RSA_DIV = 1;

    /* Check for division by zero */
    if (mbedtls_mpi_cmp_int(B, (mbedtls_mpi_sint)0) == 0)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_PREPARE | 0x00000001UL));
    }
    else if (mbedtls_mpi_cmp_abs(A, B) < 0)
    {
        /* This leads to a direct return in the caller. */
        ret = (uint8_t)CRYPTO_ERR_SUCCESS;

        /* either Q or R can be NULL */
        if ((Q != NULL) && (mbedtls_mpi_lset(Q, (mbedtls_mpi_sint)0) != (int32_t)CRYPTO_ERR_SUCCESS))
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_PREPARE | 0x00000002UL));
        }

        if ((R != NULL) && (mbedtls_mpi_copy(R, A) != (int32_t)CRYPTO_ERR_SUCCESS))
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_PREPARE | 0x00000003UL));
        }
    }
    else
    {
        if ((mbedtls_mpi_copy(&context->X, A) != (int32_t)CRYPTO_ERR_SUCCESS) ||
            (mbedtls_mpi_copy(&context->Y, B) != (int32_t)CRYPTO_ERR_SUCCESS))
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_PREPARE | 0x00000004UL));
        }
        else
        {
            context->X.s = 1;
            context->Y.s = 1;

            if ((mbedtls_mpi_grow(&context->Z, A->n + (size_t)2) != (int32_t)CRYPTO_ERR_SUCCESS) ||
                (mbedtls_mpi_lset(&context->Z, (mbedtls_mpi_sint)0) != (int32_t)CRYPTO_ERR_SUCCESS) ||
                (mbedtls_mpi_grow(&context->T1, (size_t)2) != (int32_t)CRYPTO_ERR_SUCCESS) ||
                (mbedtls_mpi_grow(&context->T2, (size_t)3) != (int32_t)CRYPTO_ERR_SUCCESS))
            {
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_PREPARE | 0x00000005UL));
            }
            else
            {
                context->internals.k = mbedtls_mpi_bitlen(&context->Y) % biL;
                if (context->internals.k < (size_t)(biL - 1u))
                {
                    context->internals.k = (size_t)((size_t)biL - ((size_t)1 + context->internals.k));
                    if ((mbedtls_mpi_shift_l(&context->X, context->internals.k) != (int32_t)CRYPTO_ERR_SUCCESS) ||
                        (mbedtls_mpi_shift_l(&context->Y, context->internals.k) != (int32_t)CRYPTO_ERR_SUCCESS))
                    {
                        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_PREPARE | 0x00000006UL));
                    }
                }
                else
                {
                    context->internals.k = 0;
                }

                /* no error occured yet */
                if (ret == (uint8_t)CRYPTO_ERR_CALLAGAIN)
                {
                    context->internals.n = context->X.n - (size_t)1;
                    context->internals.t = context->Y.n - (size_t)1;

                    if (mbedtls_mpi_shift_l(&context->Y, biL * (context->internals.n - context->internals.t)) != (int32_t)CRYPTO_ERR_SUCCESS)
                    {
                        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_PREPARE | 0x00000007UL));
                    }
                    else
                    {
                        while ((ret == (uint8_t)CRYPTO_ERR_CALLAGAIN) && (mbedtls_mpi_cmp_mpi(&context->X, &context->Y) >= 0))
                        {
                            context->Z.p[context->internals.n - context->internals.t]++;
                            if (mbedtls_mpi_sub_mpi(&context->X, &context->X, &context->Y) != (int32_t)CRYPTO_ERR_SUCCESS)
                            {
                                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_PREPARE | 0x00000008UL));
                            }
                        }

                        if ((ret == (uint8_t)CRYPTO_ERR_CALLAGAIN) && ((mbedtls_mpi_shift_r(&context->Y, biL * (context->internals.n - context->internals.t))) != (int32_t)CRYPTO_ERR_SUCCESS))
                        {
                            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_PREPARE | 0x00000009UL));
                        }
                        else
                        {
                            /* set starting index for main-for-loop in step */
                            context->internals.i = context->internals.n;
                        }
                    }
                }
            }
        }
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_SlicedMpiDivMpi_Step(MpiDivSlicingContext_t* context)
{
    size_t lc = 0;
    uint8_t ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;

    while ((lc < MAX_ITERATIONS_PER_SLICE) && (context->internals.i > context->internals.t))
    {
        lc++;
        if (context->X.p[context->internals.i] >= context->Y.p[context->internals.t])
        {
            context->Z.p[context->internals.i - (context->internals.t + (size_t)1)] = (mbedtls_mpi_uint) ~0u;
        }
        else
        {
            context->Z.p[context->internals.i - (context->internals.t + (size_t)1)] = int_div_int(context->X.p[context->internals.i], context->X.p[context->internals.i - (size_t)1],
                                                                                                  context->Y.p[context->internals.t], NULL);
        }

        context->Z.p[context->internals.i - (context->internals.t + (size_t)1)]++;
        do
        {
            context->Z.p[context->internals.i - (context->internals.t + (size_t)1)]--;

            if (mbedtls_mpi_lset(&context->T1, (mbedtls_mpi_sint)0) == (int32_t)CRYPTO_ERR_SUCCESS)
            {
                context->T1.p[0] = (context->internals.t < (size_t)1) ? (mbedtls_mpi_uint)0 : context->Y.p[context->internals.t - (size_t)1];
                context->T1.p[1] = context->Y.p[context->internals.t];
                if ((mbedtls_mpi_mul_int(&context->T1, &context->T1, context->Z.p[context->internals.i - (context->internals.t + (size_t)1)]) == (int32_t)CRYPTO_ERR_SUCCESS) &&
                    (mbedtls_mpi_lset(&context->T2, (mbedtls_mpi_sint)0) == (int32_t)CRYPTO_ERR_SUCCESS))
                {
                    context->T2.p[0] = (context->internals.i < (size_t)2) ? (mbedtls_mpi_uint)0 : context->X.p[context->internals.i - (size_t)2];
                    context->T2.p[1] = (context->internals.i < (size_t)1) ? (mbedtls_mpi_uint)0 : context->X.p[context->internals.i - (size_t)1];
                    context->T2.p[2] = context->X.p[context->internals.i];
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_STEP | 0x00000001UL));
                }
            }
            else
            {
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_STEP | 0x00000002UL));
            }
        }
        while ((ret == (uint8_t)CRYPTO_ERR_CALLAGAIN) && (mbedtls_mpi_cmp_mpi(&context->T1, &context->T2) > 0));

        if (mbedtls_mpi_mul_int(&context->T1, &context->Y, context->Z.p[context->internals.i - (context->internals.t + (size_t)1)]) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_STEP | 0x00000003UL));
        }
        else if (mbedtls_mpi_shift_l(&context->T1, biL * (context->internals.i - (context->internals.t + (size_t)1))) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_STEP | 0x00000004UL));
        }
        else if (mbedtls_mpi_sub_mpi(&context->X, &context->X, &context->T1) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_STEP | 0x00000005UL));
        }
        else
        {
            if (mbedtls_mpi_cmp_int(&context->X, (mbedtls_mpi_sint)0) < 0)
            {
                if ((mbedtls_mpi_copy(&context->T1, &context->Y) == (int32_t)CRYPTO_ERR_SUCCESS) &&
                    (mbedtls_mpi_shift_l(&context->T1, biL * (context->internals.i - (context->internals.t + (size_t)1))) == (int32_t)CRYPTO_ERR_SUCCESS) &&
                    ((mbedtls_mpi_add_mpi(&context->X, &context->X, &context->T1)) == (int32_t)CRYPTO_ERR_SUCCESS))
                {
                    context->Z.p[context->internals.i - (context->internals.t + (size_t)1)]--;
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_STEP | 0x00000006UL));
                }
            }
        }

        context->internals.i--; /* decrement loop index */
    }

    /* check if the loop ended due to max iterations or *real* end condition */
    if (ret == (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        if (context->internals.i == context->internals.t)
        {
            ret = (uint8_t)CRYPTO_ERR_SUCCESS;
        }
    }


    return ret;
}

STATICFCN uint8_t CryptoLib_SlicedMpiDivMpi_finalize(mbedtls_mpi* Q, mbedtls_mpi* R,
                                                     const mbedtls_mpi* A, const mbedtls_mpi* B,
                                                     MpiDivSlicingContext_t* context)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    /* either Q or R can be NULL */
    if (Q != NULL)
    {
        if (mbedtls_mpi_copy(Q, &context->Z) == (int32_t)CRYPTO_ERR_SUCCESS)
        {
            Q->s = A->s * B->s;
        }
        else
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_FINALIZE | 0x00000001UL));
        }
    }

    if (R != NULL)
    {
        if (mbedtls_mpi_shift_r(&context->X, context->internals.k) == (int32_t)CRYPTO_ERR_SUCCESS)
        {
            context->X.s = A->s;
            if (mbedtls_mpi_copy(R, &context->X) == (int32_t)CRYPTO_ERR_SUCCESS)
            {
                if (mbedtls_mpi_cmp_int(R, (mbedtls_mpi_sint)0) == (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    R->s = 1;
                }
            }
            else
            {
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_FINALIZE | 0x00000002UL));
            }
        }
        else
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI_FINALIZE | 0x00000003UL));
        }
    }

    CryptoLib_SlicedMpiDivMpi_Clean(context);

    return ret;
}

STATICFCN void CryptoLib_SlicedMpiDivMpi_Clean(MpiDivSlicingContext_t* context)
{
    if (MPI_INIT_FLAG_RSA_DIV == 1u)
    {
        mbedtls_mpi_free(&context->X);
        mbedtls_mpi_free(&context->Y);
        mbedtls_mpi_free(&context->Z);
        mbedtls_mpi_free(&context->T1);
        mbedtls_mpi_free(&context->T2);
        MPI_INIT_FLAG_RSA_DIV = 0;
    }

    (void)memset(context, 0, sizeof(MpiDivSlicingContext_t));
}

uint8_t CryptoLib_SlicedMpiDivMpi(mbedtls_mpi* Q, mbedtls_mpi* R,
                                  const mbedtls_mpi* A, const mbedtls_mpi* B,
                                  const CryptoLib_Slicing_t slicing)
{
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static MpiDivSlicingState_t state = MPIDIV_IDLE;
    static MpiDivSlicingContext_t context;
    CRYPTOLIB_MEMORY_SECTION_END

    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slicing == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((A == NULL) || (B == NULL) || ((Q == NULL) && (R == NULL)))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI | 0x00000001UL));
    }
    else
    {
        switch (state)
        {
            case MPIDIV_IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("rd_i,");
                #endif
                if (slicing == SLICE_INIT)
                {
                    CryptoLib_SlicedMpiDivMpi_Clean(&context);
                    retval = CryptoLib_SlicedMpiDivMpi_Prepare(Q, R, A, B, &context);

                    /* set to next state if ready, reset in case of errors */
                    if (retval == (uint8_t)CRYPTO_ERR_CALLAGAIN)
                    {
                        /* Init done, go to STEP */
                        state = MPIDIV_STEP;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI | 0x00000002UL));
                }
                break;

            case MPIDIV_STEP:
                #ifdef BENCHMARK_ACTIVE
                BENCH("rd_st,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMpiDivMpi_Step(&context);

                    /* set to next state if ready, reset in case of errors */
                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* STEP done, go to FINAL */
                        state = MPIDIV_FINAL;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI | 0x00000003UL));
                }
                break;

            case MPIDIV_FINAL:
                #ifdef BENCHMARK_ACTIVE
                BENCH("rd_f,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedMpiDivMpi_finalize(Q, R, A, B, &context);
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI | 0x00000005UL));
                }
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMPIDIVMPI | 0x00000006UL));
                break;
                /* LCOV_EXCL_STOP */
        }
    }

    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_SlicedMpiDivMpi_Clean(&context);
        state = MPIDIV_IDLE;
    }

    return retval;
}

#endif

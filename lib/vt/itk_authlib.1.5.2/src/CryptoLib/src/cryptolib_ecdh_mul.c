/**
 * @file  src/cryptolib_ecdh_mul.c
 * @brief ECDH Montgomery multiply function definition
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_ecdh_mul.h"
#include "cryptolib/cryptolib_ecdh_doubleadd.h"
#include "cryptolib/cryptolib_ecdh_normalize.h"
#include "cryptolib/cryptolib_ecdh_common.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"
#include "cryptolib/cryptolib_rng.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#include "mbedtls/bignum.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if ((CRYPTOLIB_NO_TIME_SLICING == DISABLED) && (CRYPTOLIB_ALT_ECDH == DISABLED))

/** Enum for statemachine states */
typedef enum {
    IDLE = 0, /**< Ready for new calls */
    PREP = 1, /**< Perform further preparations */
    ITERATE = 2, /**< Perform loop iterations */
    FINAL = 3 /**< Ready to write output */
} MulSlicingState_t;

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/* polyspace-begin MISRA-C3:8.2 MISRA-C3:D4.6 [Not a defect:Low] "Function pointer as parameter not properly detected." */

/**
 * Randomize projective x/z coordinates:
 * (X, Z) -> (l X, l Z) for random l
 * This is sort of the reverse operation of ecp_normalize_mxz().
 *
 * This countermeasure was first suggested in [2].
 * Cost: 2M
 *
 * @param[out] grp      curve grp
 * @param[in,out] P     point to operate on
 * @param[in]  f_rng    Callback to retrieve random bytes
 * @param[in]  p_rng    Pointer to context that is passed to f_rng
 * @param[in,out] ctx   pointer to context
 *
 * @return              0 - CRYPTO_ERR_SUCCESS: Pointer successfully randomized<br>
 *                      15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls error occured<br>
 *
 */
STATICFCN uint8_t randomize_mxz(const mbedtls_ecp_group* grp,
                                mbedtls_ecp_point* P,
                                int (* f_rng)(void*, unsigned char*, size_t),
                                void* p_rng,
                                MulSlicingContext_t* context);

/* polyspace-end */

/**
 * Prepares a MulSlicingContext_t context with parent input variables
 *
 * @param[in] grp                   pointer to curve parameter
 * @param[out] result               pointer to resulting output
 * @param[in] point                 pointer to ec point to be multiplied
 * @param[in,out] ctx               pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: context successfully prepared<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t CryptoLib_SlicedEcpMul_Prepare(const mbedtls_ecp_group* grp,
                                                 mbedtls_ecp_point* result,
                                                 const mbedtls_ecp_point* point,
                                                 MulSlicingContext_t* ctx);

/**
 * Substatemachine to iterate over ctx
 *
 * @param[in] grp                   pointer to curve parameter
 * @param[out] result               pointer to resulting output
 * @param[in] factor                pointer to factor to multiply with
 * @param[in,out] ctx               pointer to context
 * @param[in] slicing               1 - SLICE_CONTINUE: during computation<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: context successfully prepared<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                 12 - CRYPTO_ERR_BAD_INPUT: invalid parameter given<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 *                                 16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 *
 */
STATICFCN uint8_t CryptoLib_SlicedEcpMul_Iterate(MulSlicingContext_t* ctx,
                                                 mbedtls_ecp_point* result,
                                                 const mbedtls_mpi* factor,
                                                 const mbedtls_ecp_group* grp,
                                                 const CryptoLib_Slicing_t slicing);

/**
 * Finalizes the multiplication and writes output
 *
 * @param[in] grp                   pointer to curve parameter
 * @param[out] result               pointer to resulting output
 * @param[in] context               pointer to context
 * @param[in] slicing               1 - SLICE_CONTINUE: during computation<br>
 *                                  255 - SLICE_NO_SLICING: no slicing shall be used<br>
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: context successfully prepared<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: invalid parameter given<br>
 */
STATICFCN uint8_t CryptoLib_SlicedEcpMul_Finalize(mbedtls_ecp_point* result,
                                                  const mbedtls_ecp_group* grp,
                                                  MulSlicingContext_t* context,
                                                  const CryptoLib_Slicing_t slicing);

/**
 * Frees allocated mpis and ecps within ctx
 *
 * @param[in,out] ctx               pointer to context
 */
STATICFCN void CryptoLib_SlicedEcpMul_Clean(MulSlicingContext_t* ctx);

/*
 * Static function definitions
 * ----------------------------------------------------------------------------
 */

/* polyspace-begin MISRA-C3:8.2 MISRA-C3:D4.6 [Not a defect:Low] "Function pointer as parameter not properly detected." */
STATICFCN uint8_t randomize_mxz(const mbedtls_ecp_group* grp,
                                mbedtls_ecp_point* P,
                                int (* f_rng)(void*, unsigned char*, size_t),
                                void* p_rng,
                                MulSlicingContext_t* context)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret = (int32_t)CRYPTO_ERR_SUCCESS;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static mbedtls_mpi l = {0, 0u, NULL};
    CRYPTOLIB_MEMORY_SECTION_END

    if (context->RandomizeState == RND_IDLE)
    {
        mbedtls_mpi_free(&l);
        mbedtls_mpi_init(&l);

        /* Generate l such that 1 < l < p */
        /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((mret = mbedtls_mpi_random(&l, 2, &grp->P, f_rng, p_rng)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retval, mret, (uint32_t)(FID_RANDOMIZE_MXZ | 0x00000001UL));
        }
        else
        {
            context->RandomizeState = RND_ACTIVE;
            retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
        }
    }
    else
    {
        /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((mret = mbedtls_mpi_mul_mpi(&P->X, &P->X, &l)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retval, mret, (uint32_t)(FID_RANDOMIZE_MXZ | 0x00000004UL));
        }
        else if ((mret = mbedtls_mpi_mul_mpi(&P->Z, &P->Z, &l)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retval, mret, (uint32_t)(FID_RANDOMIZE_MXZ | 0x00000005UL));
        }
        /* polyspace-end */
        else
        {
            retval = mod_mul(&P->X, grp);
            if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                retval = mod_mul(&P->Z, grp);
            }
        }

        context->RandomizeState = RND_IDLE;
        mbedtls_mpi_free(&l);
    }

    return retval;
}

/* polyspace-end */

STATICFCN uint8_t CryptoLib_SlicedEcpMul_Prepare(const mbedtls_ecp_group* grp,
                                                 mbedtls_ecp_point* result,
                                                 const mbedtls_ecp_point* point,
                                                 MulSlicingContext_t* ctx)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (ctx->RandomizeState == RND_IDLE)
    {
        #ifdef BENCHMARK_ACTIVE
        BENCH("m_rnd1,");
        #endif
        mbedtls_ecp_point_init(&ctx->RP);
        mbedtls_mpi_init(&ctx->PX);

        ctx->initFlag = 1;

        if ((mbedtls_mpi_copy(&ctx->PX, &point->X) != (int32_t)CRYPTO_ERR_SUCCESS) ||
            (mbedtls_ecp_copy(&ctx->RP, point) != (int32_t)CRYPTO_ERR_SUCCESS))
        {
            CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL_PREPARE | 0x00000001UL));
        }
        else if ((mbedtls_mpi_lset(&result->X, (mbedtls_mpi_sint)1) != (int32_t)CRYPTO_ERR_SUCCESS) ||
                 (mbedtls_mpi_lset(&result->Z, (mbedtls_mpi_sint)0) != (int32_t)CRYPTO_ERR_SUCCESS))
        {
            CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL_PREPARE | 0x00000002UL));
        }
        else
        {
            mbedtls_mpi_free(&result->Y);

            retval = mod_add(&ctx->RP.X, grp);

            if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                retval = randomize_mxz(grp, &ctx->RP, CryptoLib_RandomCallback, NULL, ctx);
            }
        }
    }
    else
    {
        #ifdef BENCHMARK_ACTIVE
        BENCH("m_rnd2,");
        #endif
        retval = randomize_mxz(grp, &ctx->RP, CryptoLib_RandomCallback, NULL, ctx);

        if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
        {
            ctx->i = grp->nbits + 1; /* one past the (zero-based) required msb for private keys */
            ctx->IterationSubstate = PART_1;
        }
    }

    return retval;
}

STATICFCN uint8_t CryptoLib_SlicedEcpMul_Iterate(MulSlicingContext_t* ctx,
                                                 mbedtls_ecp_point* result,
                                                 const mbedtls_mpi* factor,
                                                 const mbedtls_ecp_group* grp,
                                                 const CryptoLib_Slicing_t slicing)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slicing == SLICE_RESET)
    {
        (void)CryptoLib_SlicedMontDA(NULL, NULL, NULL, NULL, NULL, NULL, SLICE_RESET);
    }
    else if (slicing == SLICE_CONTINUE)
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (ctx->IterationSubstate)
        {
            case PART_1:
                #ifdef BENCHMARK_ACTIVE
                BENCH("m_p1,");
                #endif
                if (ctx->i == (size_t)0)
                {
                    /* i is 0 -> no iteration necessary */
                    retval = (uint8_t)CRYPTO_ERR_SUCCESS;
                    break;
                }
                ctx->i--;

                ctx->b = (uint8_t)mbedtls_mpi_get_bit(factor, ctx->i);

                if (mbedtls_mpi_safe_cond_swap(&result->X, &ctx->RP.X, ctx->b)
                    != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL_ITERATE | 0x00000002UL));
                }
                else if (mbedtls_mpi_safe_cond_swap(&result->Z, &ctx->RP.Z, ctx->b)
                         != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL_ITERATE | 0x00000003UL));
                }
                else
                {
                    ctx->IterationSubstate = PART_2;
                }

                if (retval != (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    break;
                }
            /* fallthrough */

            case PART_2:
                if (ctx->DoubleAddState == DA_IDLE)
                {
                    /* Begin new DA slicing */
                    retval = CryptoLib_SlicedMontDA(grp,
                                                    result,
                                                    &ctx->RP,
                                                    result,
                                                    &ctx->RP,
                                                    &ctx->PX,
                                                    SLICE_INIT);

                    if (retval == (uint8_t)CRYPTO_ERR_CALLAGAIN)
                    {
                        ctx->DoubleAddState = DA_ACTIVE;
                    }
                }
                else if (ctx->DoubleAddState == DA_ACTIVE)
                {
                    /* Continue active DA slicing */
                    retval = CryptoLib_SlicedMontDA(grp,
                                                    result,
                                                    &ctx->RP,
                                                    result,
                                                    &ctx->RP,
                                                    &ctx->PX,
                                                    SLICE_CONTINUE);
                }
                else /* LCOV_EXCL_START: internal enum out of bounds */
                {
                    /* invalid DA sub-state */
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL_ITERATE | 0x00000004UL));
                    /* LCOV_EXCL_STOP */
                }

                if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    ctx->IterationSubstate = PART_3;
                    ctx->DoubleAddState = DA_IDLE;
                }
                else
                {
                    break;
                }
            /* fallthrough */

            case PART_3:
                #ifdef BENCHMARK_ACTIVE
                BENCH("m_p3,");
                #endif
                if (mbedtls_mpi_safe_cond_swap(&result->X, &ctx->RP.X, ctx->b)
                    != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL_ITERATE | 0x00000005UL));
                }
                else if (mbedtls_mpi_safe_cond_swap(&result->Z, &ctx->RP.Z, ctx->b)
                         != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL_ITERATE | 0x00000006UL));
                }
                else if (ctx->i == (size_t)0)
                {
                    /* this was the last iteration */
                    retval = (uint8_t)CRYPTO_ERR_SUCCESS;
                }
                else
                {
                    /* more iterations are needed. fallthrough so that PART_1 is executed
                     * within this same slice. */
                    ctx->IterationSubstate = PART_1;
                    retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                }
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL_ITERATE | 0x00000007UL));
                (void)CryptoLib_SlicedMontDA(NULL, NULL, NULL, NULL, NULL, NULL, SLICE_RESET);
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }
    else
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL_ITERATE | 0x00000008UL));
    }

    /* make sure CryptoLib_SlicedMontgomeryDoubleAndAdd is not left in an errorous state */
    if ((retval != (uint8_t)CRYPTO_ERR_SUCCESS) &&
        (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN) &&
        (ctx->DoubleAddState == DA_ACTIVE))
    {
        (void)CryptoLib_SlicedMontDA(NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     SLICE_RESET);
    }

    return retval;
}

STATICFCN uint8_t CryptoLib_SlicedEcpMul_Finalize(mbedtls_ecp_point* result,
                                                  const mbedtls_ecp_group* grp,
                                                  MulSlicingContext_t* context,
                                                  const CryptoLib_Slicing_t slicing)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slicing == SLICE_RESET)
    {
        (void)CryptoLib_SlicedMontNorm(NULL, NULL, SLICE_RESET);
    }
    else if (slicing == SLICE_CONTINUE)
    {
        if (context->NormalizeState == NM_IDLE)
        {
            #ifdef BENCHMARK_ACTIVE
            BENCH("no_rnd,");
            #endif
            retval = randomize_mxz(grp, result, CryptoLib_RandomCallback, NULL, context);

            if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                context->NormalizeState = NM_INIT;
                retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
            }
        }
        else if (context->NormalizeState == NM_INIT)
        {
            retval = CryptoLib_SlicedMontNorm(grp, result, SLICE_INIT);

            if (retval == (uint8_t)CRYPTO_ERR_CALLAGAIN)
            {
                context->NormalizeState = NM_ACTIVE;
            }
        }
        else if (context->NormalizeState == NM_ACTIVE)
        {
            retval = CryptoLib_SlicedMontNorm(grp, result, SLICE_CONTINUE);

            if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                context->NormalizeState = NM_IDLE;
            }
        }
        else
        {
            CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL_FINALIZE | 0x00000001UL));
        }
    }
    else
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL_FINALIZE | 0x00000002UL));
    }

    /* make sure CryptoLib_SlicedMontgomeryNormalize is not left in an errorous state */
    if ((retval != (uint8_t)CRYPTO_ERR_SUCCESS) &&
        (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN) &&
        (context->NormalizeState == NM_ACTIVE))
    {
        (void)CryptoLib_SlicedMontNorm(NULL,
                                       NULL,
                                       SLICE_RESET);
    }

    return retval;
}

STATICFCN void CryptoLib_SlicedEcpMul_Clean(MulSlicingContext_t* ctx)
{
    if (ctx->initFlag == (uint8_t)1)
    {
        mbedtls_mpi_free(&ctx->PX);
        mbedtls_ecp_point_free(&ctx->RP);
    }

    if (ctx->DoubleAddState != DA_IDLE)
    {
        (void)CryptoLib_SlicedEcpMul_Iterate(NULL, NULL, NULL, NULL, SLICE_RESET);
    }

    if (ctx->NormalizeState != NM_IDLE)
    {
        (void)CryptoLib_SlicedEcpMul_Finalize(NULL, NULL, NULL, SLICE_RESET);
    }

    ctx->RandomizeState = RND_IDLE;

    (void)memset(ctx, 0, sizeof(MulSlicingContext_t));
}

/*
 * Public function definitions
 * ----------------------------------------------------------------------------
 */
uint8_t CryptoLib_SlicedEcpMul(const mbedtls_ecp_group* grp,
                               mbedtls_ecp_point* result,
                               const mbedtls_mpi* factor,
                               const mbedtls_ecp_point* point,
                               const CryptoLib_Slicing_t slicing)
{
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static MulSlicingState_t state = IDLE;
    static MulSlicingContext_t context;
    CRYPTOLIB_MEMORY_SECTION_END

    CryptoLib_Slicing_t slc = slicing; /* write access to slicing needed */
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slc == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((grp == NULL) || (result == NULL) ||
             (factor == NULL) || (point == NULL))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL | 0x00000001UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("m_i,");
                #endif
                if (slc == SLICE_INIT)
                {
                    CryptoLib_SlicedEcpMul_Clean(&context);
                    state = PREP;
                    slc = SLICE_CONTINUE;
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL | 0x00000003UL));
                    break;
                }
            /* fallthrough */

            case PREP:
                if (slc == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedEcpMul_Prepare(grp, result, point, &context);

                    /* set to next state if ready, reset in case of errors */
                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* Init done, go to ITERATE */
                        state = ITERATE;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL | 0x00000002UL));
                }
                break;

            case ITERATE:
                if (slc == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedEcpMul_Iterate(&context, result, factor, grp, slc);

                    /* set to next state if ready, reset in case of errors */
                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* ITERATE done, go to FINAL */
                        state = FINAL;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL | 0x00000004UL));
                }
                break;

            case FINAL:
                if (slc == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedEcpMul_Finalize(result, grp, &context, slc);
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL | 0x00000006UL));
                }
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDECPMUL | 0x00000007UL));
                break;
                /* LCOV_EXCL_STOP */
        }
    }
    /* polyspace-end */

    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_SlicedEcpMul_Clean(&context);
        state = IDLE;
    }

    return retval;
}

#endif

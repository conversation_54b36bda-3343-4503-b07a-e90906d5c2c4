/**
 * @file  src/cryptolib_rsa_rsassa_pss_verify.c
 * @brief Implementation of sliced variant of mbedtls_rsa_rsassa_pss_verify
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 R<PERSON><PERSON><PERSON>heim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_rsa_rsassa_pss_verify.h"
#include "cryptolib/cryptolib_rsa_expmod.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if (CRYPTOLIB_NO_TIME_SLICING == DISABLED)

#define RSASSAPSS_SIG_END 0xBC  /**< Marks end of signature */
#define MGF_MAX_ITERATIONS 3u   /**< Maximum number of loops per slice in mgf_mask */

CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t MBEDTLS_INIT_FLAG = 0; /**< Init flag for slicing context */
CRYPTOLIB_MEMORY_SECTION_END

/** Enum for statemachine states */
typedef enum {
    IDLE = 0, /**< Ready for new calls */
    RSA = 1, /**< Perform rsa operation */
    MD_INIT = 2, /**< initialize MD */
    MGF = 3, /**< hash, write output and cleanup */
    FINAL = 4 /**< hash, write output and cleanup */
} RsaSsaPssSlicingState_t;

/**
 * Compiler optimization resistant memset-to-zero function
 *
 * @param[in]  v       pointer to memory
 * @param[in]  n       number of bytes to be set to 0
 */
STATICFCN void zeroize(void* v, size_t n)
{
    /* polyspace +1 MISRA-C3:11.5 [No action planned:Low] "Conversion from pointer to void to pointer to other type is necessary to keep this function flexible." */
    volatile uint8_t* p = (uint8_t*)v;

    size_t len = n;
    while (len != (size_t)0)
    {
        *p = 0;
        p++;
        len--;
    }
}

/**
 * Generate and apply the MGF1 operation (from PKCS#1 v2.1) to a buffer.
 *
 * @param[out] dst       buffer to mask
 * @param[in]  dlen      length of destination buffer
 * @param[in]  src       source of the mask generation
 * @param[in]  slen      length of the source buffer
 * @param[in]  md_ctx    message digest context to use
 * @param[in]  slc       slicing parameter
 */
STATICFCN uint8_t mgf_mask(uint8_t* dst, size_t dlen, const uint8_t* src,
                           size_t slen, mbedtls_md_context_t* md_ctx, CryptoLib_Slicing_t slc);

/**
 * Prepare the slicing and the RSA context with all needed data for RSA operation.
 *
 * @param[out] context      Pointer to the rsa slicing context
 * @param[in]  ctx          Pointer to the rsa context
 * @param[in]  sig          Pointer to the buffer holding the signature to validate
 * @param[in]  mode         MBEDTLS_RSA_XXX mode, only MBEDTLS_RSA_PUBLIC supported
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successful<br>
 *                          12 - CRYPTO_ERR_BAD_INPUT: invalid mode or RSA modulus<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t prepareRsa(RsaSsaPssSlicingContext_t* context,
                             const mbedtls_rsa_context* ctx,
                             const uint8_t* sig, int32_t mode);

/**
 * Prepare the slicing context to be used for hashing and comparing with expected output.
 *
 * @param[out] context      Pointer to the rsa slicing context
 * @param[in]  ctx          Pointer to the rsa context
 * @param[in]  md_alg       MD type of hashing algorithm to be used
 * @param[in]  hashlen      Length of the provided hash
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: operation successful<br>
 *                          12 - CRYPTO_ERR_BAD_INPUT: invalid MD type or signature incompatible<br>
 *                          14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                          15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t prepareMd(RsaSsaPssSlicingContext_t* context,
                            const mbedtls_rsa_context* ctx,
                            mbedtls_md_type_t md_alg, uint8_t hashlen);

/**
 * Creates the hash of the computed RSA result and compares it to the expected hash.
 *
 * @param[out] context      Pointer to the rsa slicing context
 * @param[in]  hash         Expected hash
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: signature valid<br>
 *                          6 - CRYPTO_ERR_SIGNATURE_INVALID: signature invalid<br>
 *                          12 - CRYPTO_ERR_BAD_INPUT: bad input, e.g. wrong MD type
 */
STATICFCN uint8_t hashIt(RsaSsaPssSlicingContext_t* context, const uint8_t* hash);

/**
 * Internal statemachine for performing a sliced exp_mod operation.
 *
 * @param[out] context      Pointer to the rsa slicing context
 * @param[in]  ctx          Pointer to the rsa context
 *
 * @return                  0 - CRYPTO_ERR_SUCCESS: exp mod finished successfully<br>
 *                          2 - CRYPTO_ERR_CALLAGAIN: more slices needed, call again<br>
 *                          12 - CRYPTO_ERR_BAD_INPUT: bad input, e.g. invalid slicing parameter<br>
 *                          16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
STATICFCN uint8_t expmod(RsaSsaPssSlicingContext_t* context,
                         mbedtls_rsa_context* ctx);

/**
 * Resets the slicing context to a clean state.
 *
 * @param[out] ctx          Pointer to the rsa slicing context.
 */
STATICFCN void CryptoLib_SlicedVerifyRsa_Clean(RsaSsaPssSlicingContext_t* ctx);

STATICFCN uint8_t mgf_mask(uint8_t* dst, size_t dlen, const uint8_t* src,
                           size_t slen, mbedtls_md_context_t* md_ctx, CryptoLib_Slicing_t slc)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static uint8_t mask[MBEDTLS_MD_MAX_SIZE];
    static uint8_t counter[4];
    static uint8_t* p;
    static size_t ldlen = 0;
    CRYPTOLIB_MEMORY_SECTION_END

    uint8_t hlen;
    size_t i, use_len;
    uint8_t num = 0;

    if (slc == SLICE_RESET)
    {
        /* will be handled at the end of this function */
    }
    else
    {
        if (ldlen == 0u)
        {
            ldlen = dlen;

            /* Generate and apply dbMask */
            p = dst;

            (void)memset(mask, 0, (size_t)MBEDTLS_MD_MAX_SIZE);
            (void)memset(counter, 0, (size_t)4);
        }

        hlen = mbedtls_md_get_size(md_ctx->md_info);

        while ((num < MGF_MAX_ITERATIONS) && (ldlen > 0u))
        {
            use_len = (size_t)hlen;
            if (ldlen < (size_t)hlen)
            {
                use_len = ldlen;
            }

            (void)mbedtls_md_starts(md_ctx);
            (void)mbedtls_md_update(md_ctx, src, slen);
            (void)mbedtls_md_update(md_ctx, counter, (size_t)4);
            (void)mbedtls_md_finish(md_ctx, mask);

            for (i = 0; i < use_len; ++i)
            {
                *p ^= mask[i];
                p++;
            }

            counter[3]++;

            ldlen -= use_len;
            num++;
        }

        if (ldlen != 0u)
        {
            ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
        }
    }

    if (ret != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        zeroize(mask, sizeof(mask));
        zeroize(counter, (size_t)4);
        ldlen = 0;
        p = NULL;
    }

    return ret;
}

STATICFCN uint8_t prepareRsa(RsaSsaPssSlicingContext_t* context,
                             const mbedtls_rsa_context* ctx,
                             const uint8_t* sig, int32_t mode)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    mbedtls_mpi_init(&context->rsaTemp);
    mbedtls_md_init(&context->md_ctx);
    MBEDTLS_INIT_FLAG = 1;

    if ((mode == MBEDTLS_RSA_PRIVATE) && (ctx->padding != MBEDTLS_RSA_PKCS_V21))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_PREPARERSA | 0x00000001UL));
    }
    else if ((mbedtls_mpi_read_binary(&context->rsaTemp, sig, ctx->len) != (int32_t)CRYPTO_ERR_SUCCESS))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_PREPARERSA | 0x00000002UL));
    }
    else if (mbedtls_mpi_cmp_mpi(&context->rsaTemp, &ctx->N) >= 0)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_PREPARERSA | 0x00000003UL));
    }
    else
    {
        context->siglen = ctx->len;

        if ((context->siglen < (size_t)16) || (context->siglen > sizeof(context->buf)))
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_PREPARERSA | 0x00000004UL));
        }
    }

    return ret;
}

STATICFCN uint8_t prepareMd(RsaSsaPssSlicingContext_t* context,
                            const mbedtls_rsa_context* ctx,
                            mbedtls_md_type_t md_alg, uint8_t hashlen)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t ret_mbedtls;
    size_t shift;

    const mbedtls_md_info_t* md_info;

    context->hashlen = hashlen;
    context->p = context->buf;

    if (mbedtls_mpi_write_binary(&context->rsaTemp, context->buf, context->siglen) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_MBEDTLS_FAILED, (uint32_t)(FID_PREPAREMD | 0x00000001UL));
    }
    else if (context->buf[context->siglen - (size_t)1] != (uint8_t)RSASSAPSS_SIG_END)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_SIGNATURE_INVALID, (uint32_t)(FID_PREPAREMD | 0x00000002UL));
    }
    else
    {
        if (md_alg != MBEDTLS_MD_NONE)
        {
            /* Gather length of hash to sign */
            md_info = mbedtls_md_info_from_type(md_alg);
            if (md_info == NULL)
            {
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_PREPAREMD | 0x00000003UL));
            }
            else
            {
                context->hashlen = mbedtls_md_get_size(md_info);
            }
        }

        if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
        {
            /* polyspace-begin MISRA-C3:10.5 [Not a defect:Low] "hash_id is constructed from mbedtls_md_type_t thus it is safe to cast it back." */
            mbedtls_md_type_t mgf1_hash_id;
            if (ctx->hash_id != (int32_t)MBEDTLS_MD_NONE)
            {
                mgf1_hash_id = (mbedtls_md_type_t)ctx->hash_id;
            }
            else
            {
                mgf1_hash_id = md_alg;
            }

            md_info = mbedtls_md_info_from_type(mgf1_hash_id);
            if (md_info == NULL)
            {
                ret = (uint8_t)CRYPTO_ERR_BAD_INPUT;
            }
            else
            {
                context->hlen = mbedtls_md_get_size(md_info);

                /*
                 * Note: EMSA-PSS verification is over the length of N - 1 bits
                 */
                context->msb = mbedtls_mpi_bitlen(&ctx->N) - (size_t)1;

                shift = (((size_t)8 - (context->siglen * (size_t)8)) + context->msb);
                if ((context->buf[0] >> (shift)) != 0u)
                {
                    ret = (uint8_t)CRYPTO_ERR_BAD_INPUT;
                }
                else
                {
                    /* Compensate for boundary condition when applying mask */
                    if ((context->msb % (size_t)8) == (size_t)0)
                    {
                        context->p++;
                        context->siglen -= (size_t)1;
                    }

                    if (context->siglen < (size_t)(context->hlen + (size_t)2))
                    {
                        ret = (uint8_t)CRYPTO_ERR_BAD_INPUT;
                    }
                    else
                    {
                        context->hashStart = &context->p[context->siglen - ((size_t)context->hlen + 1u)];

                        /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
                        if ((ret_mbedtls = mbedtls_md_setup(&context->md_ctx, md_info, 0)) != (int32_t)CRYPTO_ERR_SUCCESS)
                        {
                            CryptoLib_SetError(&ret, ret_mbedtls, (uint32_t)(FID_PREPAREMD | 0x00000004UL));
                            mbedtls_md_free(&context->md_ctx);
                        }
                    }
                }
            }
        }
    }

    return ret;
}

STATICFCN uint8_t hashIt(RsaSsaPssSlicingContext_t* context, const uint8_t* hash)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint8_t result[MBEDTLS_MD_MAX_SIZE];

    context->buf[0] &= (uint8_t)((size_t)0xFF >> ((context->siglen * (size_t)8) - context->msb));

    /* polyspace +1 MISRA-C3:18.4 [No action planned:Low] "It is safe and clear what is meant by performing the substraction." */
    while ((context->p < (context->hashStart - 1u)) && (*context->p == 0u))
    {
        context->p++;
    }

    /* polyspace +1 MISRA-C3:13.3 [No action planned:Low] "Readability would be reduced even more if ++ would be moved to an extra line." */
    if (*context->p++ != 0x01u)
    {
        mbedtls_md_free(&context->md_ctx);
        ret = (uint8_t)CRYPTO_ERR_BAD_INPUT;
    }
    else
    {
        context->observedSaltLen = (size_t)(context->hashStart - context->p);

        /*
         * Generate H = Hash( M' )
         */
        #define ZEROS_LEN 8
        uint8_t zeros[ZEROS_LEN];
        (void)memset(zeros, 0, (size_t)ZEROS_LEN);
        (void)mbedtls_md_starts(&context->md_ctx);
        (void)mbedtls_md_update(&context->md_ctx, zeros, (size_t)ZEROS_LEN);
        (void)mbedtls_md_update(&context->md_ctx, hash, (size_t)context->hashlen);
        (void)mbedtls_md_update(&context->md_ctx, context->p, context->observedSaltLen);
        (void)mbedtls_md_finish(&context->md_ctx, result);

        mbedtls_md_free(&context->md_ctx);

        if (memcmp(context->hashStart, result, (size_t)context->hlen) != 0)
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_SIGNATURE_INVALID, (uint32_t)(FID_HASHIT | 0x00000002UL));
        }
    }

    return ret;
}

STATICFCN uint8_t expmod(RsaSsaPssSlicingContext_t* context,
                         mbedtls_rsa_context* ctx)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (context->ExpModState == EXPMOD_IDLE)
    {
        ret = CryptoLib_SlicedMpiExpMod(&context->rsaTemp, &context->rsaTemp, &ctx->E, &ctx->N, &ctx->RN, SLICE_INIT);

        if (ret == (uint8_t)CRYPTO_ERR_CALLAGAIN)
        {
            context->ExpModState = EXPMOD_ACTIVE;
        }
    }
    else if (context->ExpModState == EXPMOD_ACTIVE)
    {
        ret = CryptoLib_SlicedMpiExpMod(&context->rsaTemp, &context->rsaTemp, &ctx->E, &ctx->N, &ctx->RN, SLICE_CONTINUE);

        if (ret != (uint8_t)CRYPTO_ERR_CALLAGAIN)
        {
            context->ExpModState = EXPMOD_IDLE;
        }
    }
    else
    {
        /* LCOV_EXCL_START: internal enum out of bounds */
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_EXPMOD | 0x00000001UL));
        /* LCOV_EXCL_STOP */
    }

    return ret;
}

STATICFCN void CryptoLib_SlicedVerifyRsa_Clean(RsaSsaPssSlicingContext_t* ctx)
{
    if (MBEDTLS_INIT_FLAG == 1u)
    {
        mbedtls_mpi_free(&ctx->rsaTemp);
        mbedtls_md_free(&ctx->md_ctx);
        MBEDTLS_INIT_FLAG = 0u;
    }

    if (ctx->ExpModState != EXPMOD_IDLE)
    {
        (void)CryptoLib_SlicedMpiExpMod(NULL, NULL, NULL, NULL, NULL, SLICE_RESET);
    }

    (void)mgf_mask(NULL, (size_t)0, NULL, (size_t)0, NULL, SLICE_RESET);

    (void)memset(ctx, 0, sizeof(RsaSsaPssSlicingContext_t));
}

uint8_t CryptoLib_SlicedVerifyRsa(mbedtls_rsa_context* ctx,
                                  int32_t mode,
                                  mbedtls_md_type_t md_alg,
                                  uint32_t hashlen,
                                  const uint8_t* hash,
                                  const uint8_t* sig,
                                  CryptoLib_Slicing_t slicing)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static RsaSsaPssSlicingContext_t context;
    static RsaSsaPssSlicingState_t state = IDLE;
    CRYPTOLIB_MEMORY_SECTION_END

    if (slicing == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((ctx == NULL) || (hash == NULL) || (sig == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SLICEDVERIFYRSA | 0x00000001UL));
    }
    else
    {
        switch (state)
        {
            case IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ss_i,");
                #endif
                if (slicing == SLICE_INIT)
                {
                    CryptoLib_SlicedVerifyRsa_Clean(&context);
                    ret = prepareRsa(&context, ctx, sig, mode);

                    /* set to next state if ready, reset in case of errors */
                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* Init done, go to STEP */
                        state = RSA;
                        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDVERIFYRSA | 0x00000002UL));
                }
                break;

            case RSA:
                if (slicing == SLICE_CONTINUE)
                {
                    if (mode == MBEDTLS_RSA_PUBLIC)
                    {
                        ret = expmod(&context, ctx);

                        if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                        {
                            state = MD_INIT;
                            ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                        }
                    }
                    else
                    {
                        /* mbedtls_rsa_private is not sliced therefore not supported here */
                        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDVERIFYRSA | 0x00000004UL));
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDVERIFYRSA | 0x00000005UL));
                }
                break;

            case MD_INIT:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ss_mdi,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    ret = prepareMd(&context, ctx, md_alg, (uint8_t)hashlen);

                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = MGF;
                        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDVERIFYRSA | 0x00000007UL));
                }
                break;

            case MGF:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ss_mgf,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    ret = mgf_mask(context.p, context.siglen - (context.hlen + (size_t)1),
                                   context.hashStart,
                                   (size_t)context.hlen, &context.md_ctx,
                                   SLICE_CONTINUE);

                    if (ret == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = FINAL;
                        ret = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDVERIFYRSA | 0x00000008UL));
                }
                break;

            case FINAL:
                #ifdef BENCHMARK_ACTIVE
                BENCH("ss_f,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    ret = hashIt(&context, hash);
                }
                else
                {
                    CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDVERIFYRSA | 0x00000009UL));
                }
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDVERIFYRSA | 0x0000000aUL));
                break;
                /* LCOV_EXCL_STOP */
        }
    }

    if (ret != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_SlicedVerifyRsa_Clean(&context);
        state = IDLE;
    }

    return ret;
}

#endif

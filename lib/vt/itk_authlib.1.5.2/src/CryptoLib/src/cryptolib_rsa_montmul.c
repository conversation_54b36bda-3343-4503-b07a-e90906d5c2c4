/**
 * @file  src/cryptolib_rsa_montmul.c
 * @brief Implementation of sliced Montgomery multiplication
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_rsa_montmul.h"
#include "cryptolib/cryptolib_common.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#include "mbedtls/bignum.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if (CRYPTOLIB_NO_TIME_SLICING == DISABLED)

/** Enum for statemachine states */
typedef enum {
    IDLE = 0,       /**< Ready for new calls */
    ITERATE = 1,    /**< Perform loop */
    FINAL = 2       /**< Ready to write output */
} MontMultSlicingState_t;

#define MAX_ITERATIONS_PER_SLICE (size_t)3      /**< Max. iterations per slice */

/*
 * Static variables
 * ----------------------------------------------------------------------------
 */
CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t MPI_INIT_FLAG_MONTMUL = 0; /**< Init flag for slicing context */
CRYPTOLIB_MEMORY_SECTION_END

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Prepares a MontMultSlicingContext_t data structures
 *
 * @param[in] A                     pointer to A
 * @param[in] B                     pointer to B
 * @param[in] N                     pointer to N
 * @param[in] T                     pointer to T
 * @param[in,out] ctx               pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: context successfully prepared<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: invalid parameter given<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedMontMul_Init(const mbedtls_mpi* A,
                                               const mbedtls_mpi* B,
                                               const mbedtls_mpi* N,
                                               const mbedtls_mpi* T,
                                               MontMultSlicingContext_t* ctx);

/**
 * Frees allocated mpis within context.
 *
 * @param[in,out] ctx           pointer to context
 */
STATICFCN void CryptoLib_SlicedMontMul_Clean(MontMultSlicingContext_t* ctx);

/**
 * Performs loop iterations on ctx for the Montgomery multipication
 *
 * @param[in,out] ctx               pointer to context
 * @param[in] mm                    mm
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: Montgomery multipication successfully completed<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: multipication not yet completed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedMontMul_Iterate(mbedtls_mpi_uint mm, MontMultSlicingContext_t* ctx);

/**
 * Finalizes data and writes to output
 *
 * @param[out] A                    pointer to A
 * @param[in] ctx                   pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: output written<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedMontMul_Finalize(mbedtls_mpi* A, const MontMultSlicingContext_t* ctx);

/**
 * Depending on the value of assign, copies the src to dest while not reveiling
 * if it has been done or not, i.e. runtime is the same if assign = 0 or 1
 *
 * @param[in] n         Length of in- and output arrays
 * @param[out] dest     Destination array
 * @param[in] src       Source array
 * @param[in] assign    If 1: copy value, if 0: do not copy value
 */
STATICFCN void safe_cond_assign(size_t n,
                                volatile mbedtls_mpi_uint* dest,
                                const volatile mbedtls_mpi_uint* src,
                                mbedtls_mpi_uint assign);

/*
 * Static function definitions
 * ----------------------------------------------------------------------------
 */

STATICFCN uint8_t CryptoLib_SlicedMontMul_Init(const mbedtls_mpi* A,
                                               const mbedtls_mpi* B,
                                               const mbedtls_mpi* N,
                                               const mbedtls_mpi* T,
                                               MontMultSlicingContext_t* ctx)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    mbedtls_mpi_init(&ctx->A);
    mbedtls_mpi_init(&ctx->B);
    mbedtls_mpi_init(&ctx->N);
    mbedtls_mpi_init(&ctx->T);

    MPI_INIT_FLAG_MONTMUL = 1;

    if ((mbedtls_mpi_copy(&ctx->A, A) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_grow(&ctx->A, A->n) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_copy(&ctx->B, B) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_grow(&ctx->B, B->n) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_copy(&ctx->N, N) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_grow(&ctx->N, N->n) != (int32_t)CRYPTO_ERR_SUCCESS) ||
        (mbedtls_mpi_grow(&ctx->T, T->n) != (int32_t)CRYPTO_ERR_SUCCESS))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTMUL_INIT | 0x00000001UL));
    }
    else
    {
        (void)memset(ctx->T.p, 0, ctx->T.n * sizeof(mbedtls_mpi_uint));

        ctx->ctr = 0;
        ctx->u0 = 0;
        ctx->u1 = 0;
        ctx->d = ctx->T.p;
        ctx->n = ctx->N.n;
        ctx->m = (ctx->B.n < ctx->n) ? ctx->B.n : ctx->n;
    }

    return retVal;
}

STATICFCN void CryptoLib_SlicedMontMul_Clean(MontMultSlicingContext_t* ctx)
{
    if (MPI_INIT_FLAG_MONTMUL == 1u)
    {
        mbedtls_mpi_free(&ctx->A);
        mbedtls_mpi_free(&ctx->B);
        mbedtls_mpi_free(&ctx->N);
        mbedtls_mpi_free(&ctx->T);
    }

    (void)memset(ctx, 0, sizeof(MontMultSlicingContext_t));
}

STATICFCN uint8_t CryptoLib_SlicedMontMul_Iterate(mbedtls_mpi_uint mm, MontMultSlicingContext_t* ctx)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;
    size_t i;

    for (i = 0; ((i < MAX_ITERATIONS_PER_SLICE) && ((size_t)ctx->ctr < ctx->n)); i++)
    {
        /*
         * T = (T + u0*B + u1*N) / 2^biL
         */
        ctx->u0 = ctx->A.p[ctx->ctr];
        ctx->u1 = (ctx->d[0] + (ctx->u0 * ctx->B.p[0])) * mm;

        mpi_mul_hlp(ctx->m, ctx->B.p, ctx->d, ctx->u0);
        mpi_mul_hlp(ctx->n, ctx->N.p, ctx->d, ctx->u1);

        *(ctx->d) = ctx->u0;
        ctx->d++;
        ctx->d[ctx->n + 1u] = 0;

        ctx->ctr++;
    }

    if ((size_t)ctx->ctr < ctx->n)
    {
        retVal = (uint8_t)CRYPTO_ERR_CALLAGAIN;
    }

    return retVal;
}

STATICFCN void safe_cond_assign(size_t n,
                                volatile mbedtls_mpi_uint* dest,
                                const volatile mbedtls_mpi_uint* src,
                                mbedtls_mpi_uint assign)
{
    size_t i;
    for (i = 0; i < n; i++)
    {
        /* guarantee time constant conditional assignment by volatile access */
        if (assign == 1u)
        {
            dest[i] = src[i];
        }
        else if (assign == 0u)
        {
            dest[i] = dest[i];
        }
        else
        {
            /* intentionally empty */
        }
    }
}

STATICFCN uint8_t CryptoLib_SlicedMontMul_Finalize(mbedtls_mpi* A,
                                                   const MontMultSlicingContext_t* ctx)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    /* At this point, d is either the desired result or the desired result
     * plus N. We now potentially subtract N, avoiding leaking whether the
     * subtraction is performed through side channels. */

    /* Copy the n least significant limbs of d to A, so that
     * A = d if d < N (recall that N has n limbs). */
    (void)memcpy(ctx->A.p, ctx->d, (size_t)(ctx->n * sizeof(mbedtls_mpi_uint)));

    /* If d >= N then we want to set A to d - N. To prevent timing attacks,
     * do the calculation without using conditional tests. */
    /* Set d to d0 + (2^biL)^n - N where d0 is the current value of d. */
    ctx->d[ctx->n] += 1u;
    ctx->d[ctx->n] -= mpi_sub_hlp(ctx->n, ctx->d, ctx->d, ctx->N.p);

    /* If d0 < N then d < (2^biL)^n
     * so d[n] == 0 and we want to keep A as it is.
     * If d0 >= N then d >= (2^biL)^n, and d <= (2^biL)^n + N < 2 * (2^biL)^n
     * so d[n] == 1 and we want to set A to the result of the subtraction
     * which is d - (2^biL)^n, i.e. the n least significant limbs of d.
     * This exactly corresponds to a conditional assignment. */
    safe_cond_assign(ctx->n, ctx->A.p, ctx->d, ctx->d[ctx->n]);

    if (mbedtls_mpi_copy(A, &ctx->A) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTMUL_FINALIZE | 0x00000001UL));
    }

    return retVal;
}

/*
 * Public function definition
 * ----------------------------------------------------------------------------
 */
uint8_t CryptoLib_SlicedMontMul(mbedtls_mpi* A,
                                const mbedtls_mpi* B,
                                const mbedtls_mpi* N,
                                mbedtls_mpi_uint mm,
                                const mbedtls_mpi* T,
                                const CryptoLib_Slicing_t slicing)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static MontMultSlicingState_t state = IDLE;
    static MontMultSlicingContext_t ctx;
    CRYPTOLIB_MEMORY_SECTION_END

    if (slicing == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((A == NULL) || (B == NULL) || (N == NULL) || (T == NULL))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTMUL | 0x00000001UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("mm_i,");
                #endif
                if (slicing == SLICE_INIT)
                {
                    CryptoLib_SlicedMontMul_Clean(&ctx);
                    retVal = CryptoLib_SlicedMontMul_Init(A, B, N, T, &ctx);

                    /* set to next state if ready, reset in case of errors */
                    if (retVal == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        state = ITERATE;
                        retVal = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTMUL | 0x00000002UL));
                }
                break;

            case ITERATE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("mod_it,");
                #endif
                if (slicing == SLICE_CONTINUE)
                {
                    retVal = CryptoLib_SlicedMontMul_Iterate(mm, &ctx);

                    /* set to next state if ready, reset in case of errors */
                    if (retVal == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* loop finished, go to FINAL */
                        state = FINAL;
                    }
                    else
                    {
                        break;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTMUL | 0x00000003UL));
                    break;
                }
            /* fallthrough */

            case FINAL:
                #ifdef BENCHMARK_ACTIVE
                BENCH("mod_f,");
                #endif
                retVal = CryptoLib_SlicedMontMul_Finalize(A, &ctx);
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMONTMUL | 0x00000004UL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }

    if (retVal != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_SlicedMontMul_Clean(&ctx);
        state = IDLE;
    }

    return retVal;
}

#endif

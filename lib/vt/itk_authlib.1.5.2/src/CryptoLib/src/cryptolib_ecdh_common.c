/**
 * @file  src/cryptolib_ecdh_common.c
 * @brief Commonly shared ecdh helper function definitions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_ecdh_common.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"
#include "cryptolib/cryptolib_rng.h"

#if ((CRYPTOLIB_NO_TIME_SLICING == DISABLED) && (CRYPTOLIB_ALT_ECDH == DISABLED))

uint8_t mod_add(mbedtls_mpi* N, const mbedtls_ecp_group* grp)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint32_t ret_mbedtls = (uint32_t)CRYPTO_ERR_SUCCESS;

    while (mbedtls_mpi_cmp_mpi(N, &grp->P) >= 0)
    {
        ret_mbedtls |= (uint32_t)mbedtls_mpi_sub_abs(N, N, &grp->P);
    }

    if (ret_mbedtls != (uint32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, (int32_t)ret_mbedtls, (uint32_t)(FID_MOD_ADD | 0x00000001UL));
    }

    return ret;
}

uint8_t mod_mul(mbedtls_mpi* N, const mbedtls_ecp_group* grp)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint32_t ret_medtls = (uint32_t)CRYPTO_ERR_SUCCESS;

    if (grp->modp == NULL)
    {
        ret_medtls = (uint32_t)mbedtls_mpi_mod_mpi(N, N, &grp->P);
    }
    else if (((N->s < 0) && (mbedtls_mpi_cmp_int(N, (mbedtls_mpi_sint)0) != 0)) ||
             (mbedtls_mpi_bitlen(N) > (size_t)((size_t)2 * grp->pbits)))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_MOD_MUL | 0x00000001UL));
    }
    else if (grp->modp(N) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_MOD_MUL | 0x00000002UL));
    }
    else
    {
        while ((N->s < 0) && (mbedtls_mpi_cmp_int(N, (mbedtls_mpi_sint)0) != 0))
        {
            ret_medtls |= (uint32_t)mbedtls_mpi_add_mpi(N, N, &grp->P);
        }

        while (mbedtls_mpi_cmp_mpi(N, &grp->P) >= 0)
        {
            /* we known P, N and the result are positive */
            ret_medtls |= (uint32_t)mbedtls_mpi_sub_abs(N, N, &grp->P);
        }
    }

    if ((ret_medtls != (uint32_t)CRYPTO_ERR_SUCCESS) && (ret != (uint8_t)CRYPTO_ERR_INVALID))
    {
        CryptoLib_SetError(&ret, (int32_t)ret_medtls, (uint32_t)(FID_MOD_MUL | 0x00000003UL));
    }

    return ret;
}

uint8_t mod_sub(mbedtls_mpi* N, const mbedtls_ecp_group* grp)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint32_t ret_medtls = (uint32_t)CRYPTO_ERR_SUCCESS;

    while ((N->s < 0) && (mbedtls_mpi_cmp_int(N, (mbedtls_mpi_sint)0) != 0))
    {
        ret_medtls |= (uint32_t)mbedtls_mpi_add_mpi(N, N, &grp->P);
    }

    if (ret_medtls != (uint32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, (int32_t)ret_medtls, (uint32_t)(FID_MOD_SUB | 0x00000001UL));
    }

    return ret;
}

#endif /* #if ((CRYPTOLIB_NO_TIME_SLICING == DISABLED) && (CRYPTOLIB_ALT_ECDH == DISABLED)) */

/* polyspace-begin MISRA-C3:D4.6 MISRA-C3:8.13 [No action planned:Low] "f_rng requires functions with return type int
 *                                                                      and unsigned char for the output (return value is always 0)" */
int CryptoLib_RandomCallback(void* p_rng, unsigned char* output, size_t len)
{
    /* p_rng should hold the drbg context, as this is a function static to
     * the CryptoLib_Random function this isn't needed here. */
    (void)p_rng;

    return (int)CryptoLib_Random((uint8_t*)output, (uint16_t)len, NULL, 0);
}

/* polyspace-end */

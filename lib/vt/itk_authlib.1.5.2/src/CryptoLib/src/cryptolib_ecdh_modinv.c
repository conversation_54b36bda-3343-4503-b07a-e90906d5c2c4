/**
 * @file  src/cryptolib_ecdh_modinv.c
 * @brief ECDH modular inverse function definition
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_ecdh_modinv.h"
#include "cryptolib/cryptolib_ecdh_gcd.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if ((CRYPTOLIB_NO_TIME_SLICING == DISABLED) && (CRYPTOLIB_ALT_ECDH == DISABLED))

#define MAX_ITERATIONS_PER_SLICE (size_t)1     /**< Max. iterations per slice */
/*
 * Static variables
 * ----------------------------------------------------------------------------
 */
CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t MPI_INIT_FLAG_MODINV = 0; /**< Init flag for slicing context */
CRYPTOLIB_MEMORY_SECTION_END

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Prepares a ModInvSlicingContext_t data structures
 *
 * @param[in] modulus               pointer to modulus
 * @param[in,out] ctx               pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: context successfully prepared<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: invalid parameter given<br>
 */
STATICFCN uint8_t CryptoLib_SlicedModInv_Init(const mbedtls_mpi* modulus,
                                              ModInvSlicingContext_t* ctx);

/**
 * Prepare loop iterations on ctx for the modular inverse
 *
 * @param[in] base                  pointer to base
 * @param[in] modulus               pointer to modulus
 * @param[in,out] ctx               pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: preparations completed<br>
 *                                  4 - CRYPTO_ERR_INTERNAL: internal error<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                 15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t CryptoLib_SlicedModInv_Prepare(const mbedtls_mpi* base,
                                                 const mbedtls_mpi* modulus,
                                                 ModInvSlicingContext_t* ctx);

/**
 * Performs loop iterations on ctx for the modular inverse
 *
 * @param[in,out] ctx               pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: inverse successfully calculated<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: calculation not yet completed<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedModInv_Iterate(ModInvSlicingContext_t* ctx);

/**
 * Finalizes data and writes to parent output
 *
 * @param[out] out                  pointer to output mpi
 * @param[in] modulus               pointer to modulus mpi
 * @param[in] ctx                   pointer to context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: output written<br>
 *                                 14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 */
STATICFCN uint8_t CryptoLib_SlicedModInv_Finalize(mbedtls_mpi* out,
                                                  const mbedtls_mpi* modulus,
                                                  ModInvSlicingContext_t* ctx);

/**
 * Frees allocated mpis within ctx
 *
 * @param[in,out] ctx               pointer to context
 */
STATICFCN void CryptoLib_SlicedModInv_Clean(ModInvSlicingContext_t* ctx);

/*
 * Static function definitions
 * ----------------------------------------------------------------------------
 */
STATICFCN uint8_t CryptoLib_SlicedModInv_Init(const mbedtls_mpi* modulus,
                                              ModInvSlicingContext_t* ctx)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (mbedtls_mpi_cmp_int(modulus, (mbedtls_mpi_sint)1) <= 0)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_INIT | 0x00000001UL));
    }
    else
    {
        mbedtls_mpi_init(&ctx->TA);
        mbedtls_mpi_init(&ctx->TU);
        mbedtls_mpi_init(&ctx->U1);
        mbedtls_mpi_init(&ctx->U2);
        mbedtls_mpi_init(&ctx->G);
        mbedtls_mpi_init(&ctx->TB);
        mbedtls_mpi_init(&ctx->TV);
        mbedtls_mpi_init(&ctx->V1);
        mbedtls_mpi_init(&ctx->V2);

        MPI_INIT_FLAG_MODINV = 1;

        ctx->GcdState = GCD_IDLE;
    }

    return retval;
}

STATICFCN uint8_t CryptoLib_SlicedModInv_Prepare(const mbedtls_mpi* base,
                                                 const mbedtls_mpi* modulus,
                                                 ModInvSlicingContext_t* ctx)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;

    /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
    if (mbedtls_mpi_cmp_int(&ctx->G, (mbedtls_mpi_sint)1) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_INTERNAL, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_PREPARE | 0x00000001UL));
    }
    else if ((mret = mbedtls_mpi_mod_mpi(&ctx->TA, base, modulus)) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retval, mret, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_PREPARE | 0x00000002UL));
    }
    else if (mbedtls_mpi_copy(&ctx->TU, &ctx->TA) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_PREPARE | 0x00000003UL));
    }
    else if (mbedtls_mpi_copy(&ctx->TB, modulus) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_PREPARE | 0x00000004UL));
    }
    else if (mbedtls_mpi_copy(&ctx->TV, modulus) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_PREPARE | 0x00000005UL));
    }
    else if (mbedtls_mpi_lset(&ctx->U1, (mbedtls_mpi_sint)1) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_PREPARE | 0x00000006UL));
    }
    else if (mbedtls_mpi_lset(&ctx->U2, (mbedtls_mpi_sint)0) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_PREPARE | 0x00000007UL));
    }
    else if (mbedtls_mpi_lset(&ctx->V1, (mbedtls_mpi_sint)0) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_PREPARE | 0x00000008UL));
    }
    else if (mbedtls_mpi_lset(&ctx->V2, (mbedtls_mpi_sint)1) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_PREPARE | 0x00000009UL));
    }
    else
    {
        /* MISRA */
    }
    /* polyspace-end */

    return retval;
}

STATICFCN uint8_t CryptoLib_SlicedModInv_Iterate(ModInvSlicingContext_t* ctx)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint32_t mret = (uint32_t)CRYPTO_ERR_SUCCESS;
    ctx->lc = 0;

    do
    {
        ctx->lc++;

        while (((ctx->TU.p[0] & (mbedtls_mpi_uint)1) == (mbedtls_mpi_uint)0) &&
               (mret == (int32_t)CRYPTO_ERR_SUCCESS))
        {
            mret |= (uint32_t)(mbedtls_mpi_shift_r(&ctx->TU, (size_t)1));

            if (((ctx->U1.p[0] & (mbedtls_mpi_uint)1) != (mbedtls_mpi_uint)0) ||
                ((ctx->U2.p[0] & (mbedtls_mpi_uint)1) != (mbedtls_mpi_uint)0))
            {
                mret |= (uint32_t)mbedtls_mpi_add_mpi(&ctx->U1, &ctx->U1, &ctx->TB);
                mret |= (uint32_t)mbedtls_mpi_sub_mpi(&ctx->U2, &ctx->U2, &ctx->TA);
            }

            mret |= (uint32_t)mbedtls_mpi_shift_r(&ctx->U1, (size_t)1);
            mret |= (uint32_t)mbedtls_mpi_shift_r(&ctx->U2, (size_t)1);
        }

        while (((ctx->TV.p[0] & (mbedtls_mpi_uint)1) == (mbedtls_mpi_uint)0) &&
               (mret == (uint32_t)CRYPTO_ERR_SUCCESS)) /* polyspace MISRA-C3:D4.14 [Not a defect:low] "Not a tainted value" */
        {
            mret |= (uint32_t)mbedtls_mpi_shift_r(&ctx->TV, (size_t)1);

            if (((ctx->V1.p[0] & (mbedtls_mpi_uint)1) != (mbedtls_mpi_uint)0) ||
                ((ctx->V2.p[0] & (mbedtls_mpi_uint)1) != (mbedtls_mpi_uint)0))
            {
                mret |= (uint32_t)mbedtls_mpi_add_mpi(&ctx->V1, &ctx->V1, &ctx->TB);
                mret |= (uint32_t)mbedtls_mpi_sub_mpi(&ctx->V2, &ctx->V2, &ctx->TA);
            }

            mret |= (uint32_t)mbedtls_mpi_shift_r(&ctx->V1, (size_t)1);
            mret |= (uint32_t)mbedtls_mpi_shift_r(&ctx->V2, (size_t)1);
        }

        if (mret != (uint32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_ITERATE | 0x00000001UL));
        }
        else if (mbedtls_mpi_cmp_mpi(&ctx->TU, &ctx->TV) >= 0)
        {
            mret |= (uint32_t)mbedtls_mpi_sub_mpi(&ctx->TU, &ctx->TU, &ctx->TV);
            mret |= (uint32_t)mbedtls_mpi_sub_mpi(&ctx->U1, &ctx->U1, &ctx->V1);
            mret |= (uint32_t)mbedtls_mpi_sub_mpi(&ctx->U2, &ctx->U2, &ctx->V2);
        }
        else
        {
            mret |= (uint32_t)mbedtls_mpi_sub_mpi(&ctx->TV, &ctx->TV, &ctx->TU);
            mret |= (uint32_t)mbedtls_mpi_sub_mpi(&ctx->V1, &ctx->V1, &ctx->U1);
            mret |= (uint32_t)mbedtls_mpi_sub_mpi(&ctx->V2, &ctx->V2, &ctx->U2);
        }
    }
    while ((mbedtls_mpi_cmp_int(&ctx->TU, (mbedtls_mpi_sint)0) != 0) &&
           (mret == (uint32_t)CRYPTO_ERR_SUCCESS) &&
           (ctx->lc <= MAX_ITERATIONS_PER_SLICE));

    /* if successful so far, find out if loop broke because of iteration
     * limit or calculation status */
    if ((mret == (uint32_t)CRYPTO_ERR_SUCCESS) &&
        (mbedtls_mpi_cmp_int(&ctx->TU, (mbedtls_mpi_sint)0) != 0))
    {
        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
    }
    else if (mret != (uint32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_ITERATE | 0x00000002UL));
    }
    else
    {
        /* MISRA */
    }

    return retval;
}

STATICFCN uint8_t CryptoLib_SlicedModInv_Finalize(mbedtls_mpi* out,
                                                  const mbedtls_mpi* modulus,
                                                  ModInvSlicingContext_t* ctx)
{
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    while ((retval == (uint8_t)CRYPTO_ERR_SUCCESS) &&
           (mbedtls_mpi_cmp_int(&ctx->V1, (mbedtls_mpi_sint)0) < 0))
    {
        retval = (uint8_t)mbedtls_mpi_add_mpi(&ctx->V1, &ctx->V1, modulus);
    }

    while ((retval == (uint8_t)CRYPTO_ERR_SUCCESS) &&
           (mbedtls_mpi_cmp_mpi(&ctx->V1, modulus) >= 0))
    {
        retval = (uint8_t)mbedtls_mpi_sub_mpi(&ctx->V1, &ctx->V1, modulus);
    }

    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        retval = (uint8_t)mbedtls_mpi_copy(out, &ctx->V1);
    }

    if (retval != (uint8_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV_FINALIZE | 0x00000001UL));
    }

    return retval;
}

STATICFCN void CryptoLib_SlicedModInv_Clean(ModInvSlicingContext_t* ctx)
{
    if (MPI_INIT_FLAG_MODINV == (uint8_t)1)
    {
        mbedtls_mpi_free(&ctx->TA);
        mbedtls_mpi_free(&ctx->TU);
        mbedtls_mpi_free(&ctx->U1);
        mbedtls_mpi_free(&ctx->U2);
        mbedtls_mpi_free(&ctx->G);
        mbedtls_mpi_free(&ctx->TB);
        mbedtls_mpi_free(&ctx->TV);
        mbedtls_mpi_free(&ctx->V1);
        mbedtls_mpi_free(&ctx->V2);
    }

    if (ctx->GcdState != GCD_IDLE)
    {
        (void)CryptoLib_SlicedGCD(NULL, NULL, NULL, SLICE_RESET);
    }

    (void)memset(ctx, 0, sizeof(ModInvSlicingContext_t));
}

/*
 * Public function definitions
 * ----------------------------------------------------------------------------
 */
uint8_t CryptoLib_SlicedModInv(mbedtls_mpi* out,
                               const mbedtls_mpi* base,
                               const mbedtls_mpi* modulus,
                               const CryptoLib_Slicing_t slicing)
{
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static ModInvSlicingState_t state = MODINV_IDLE;
    static ModInvSlicingContext_t context;
    CRYPTOLIB_MEMORY_SECTION_END

    CryptoLib_Slicing_t slc = slicing; /* write access to slicing needed */
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slc == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((out == NULL) || (base == NULL) || (modulus == NULL))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV | 0x00000001UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case MODINV_IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("mi_i,");
                #endif
                if (slc == SLICE_INIT)
                {
                    CryptoLib_SlicedModInv_Clean(&context);
                    retval = CryptoLib_SlicedModInv_Init(modulus, &context);

                    /* set to next state if ready, reset in case of errors */
                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* Init done, go to GCD */
                        state = MODINV_GCD;
                        slc = SLICE_CONTINUE;
                    }
                    else
                    {
                        /* error occured */
                        break;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV | 0x00000002UL));
                    break;
                }
            /* fallthrough */

            case MODINV_GCD:
                if (slc == SLICE_CONTINUE)
                {
                    if (context.GcdState == GCD_IDLE)
                    {
                        /* Begin new GCD slicing */
                        retval = CryptoLib_SlicedGCD(&context.G,
                                                     base,
                                                     modulus,
                                                     SLICE_INIT);

                        if (retval == (uint8_t)CRYPTO_ERR_CALLAGAIN)
                        {
                            context.GcdState = GCD_ACTIVE;
                        }
                    }
                    else if (context.GcdState == GCD_ACTIVE)
                    {
                        /* Continue active GCD slicing */
                        retval = CryptoLib_SlicedGCD(&context.G,
                                                     base,
                                                     modulus,
                                                     SLICE_CONTINUE);

                        if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
                        {
                            context.GcdState = GCD_IDLE;
                        }
                    }
                    else
                    {
                        /* LCOV_EXCL_START: internal enum out of bounds */
                        /* invalid GCD sub-state. Mark as error and let the case
                         * below handle this error */
                        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV | 0x00000004UL));
                        /* LCOV_EXCL_STOP */
                    }

                    /* set to next state if ready, reset in case of errors */
                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* STEP done, go to PREPARE */
                        state = MODINV_PREPARE;
                    }
                    else
                    {
                        break;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV | 0x00000005UL));
                    break;
                }
            /* fallthrough */

            case MODINV_PREPARE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("mi_p,");
                #endif
                retval = CryptoLib_SlicedModInv_Prepare(base, modulus, &context);

                /* set to next state if ready, reset in case of errors */
                if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    /* Init done, go to ITERATE */
                    state = MODINV_ITERATE;
                    retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                }
                break;

            case MODINV_ITERATE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("mi_it,");
                #endif
                if (slc == SLICE_CONTINUE)
                {
                    retval = CryptoLib_SlicedModInv_Iterate(&context);

                    /* set to next state if ready, reset in case of errors */
                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* STEP done, go to FINAL */
                        state = MODINV_FINAL;
                    }
                    else
                    {
                        break;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV | 0x00000007UL));
                    break;
                }
            /* fallthrough */

            case MODINV_FINAL:
                #ifdef BENCHMARK_ACTIVE
                BENCH("mi_f,");
                #endif
                /* FINAL can only be reached after iterate finished successfully,
                 * the slicing parameter is already checked there, so no need for
                 * checking it again. */
                retval = CryptoLib_SlicedModInv_Finalize(out, modulus, &context);
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDMODINV | 0x00000009UL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }

    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_SlicedModInv_Clean(&context);
        state = MODINV_IDLE;
    }

    return retval;
}

#endif

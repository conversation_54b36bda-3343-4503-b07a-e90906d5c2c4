/**
 * @file  src/cryptolib_crt.c
 * @brief Certificate function definitions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON>ülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_crt.h"
#include "cryptolib/cryptolib_x509.h"
#include "cryptolib/cryptolib_common.h"
#include "cryptolib/cryptolib_rsa.h"
#include "cryptolib/cryptolib_hash.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#include "mbedtls/asn1.h"
#include "mbedtls/x509.h"
#include "mbedtls/oid.h"
#include "mbedtls/pk_internal.h"
#include "mbedtls/platform.h"
#include "mbedtls/memory_buffer_alloc.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if (CRYPTOLIB_ALT_CERT_VERIFY == DISABLED)

uint8_t CryptoLib_VerifyCertificate(CRYPTOLIB_HUGE const CryptoLib_Crt_t* certificate,
                                    CRYPTOLIB_HUGE const CryptoLib_Crt_t* parentCertificate,
                                    const CryptoLib_Slicing_t slicing)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slicing == SLICE_RESET)
    {
        (void)CryptoLib_VerifyRsa(NULL, 0, NULL, 0, NULL, NULL, SLICE_RESET);
    }
    else if ((certificate == NULL) || (parentCertificate == NULL))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_VERIFYCERTIFICATE | 0x00000001UL));
    }
    else
    {
        retVal = CryptoLib_VerifyRsa(certificate->mdOfTbs,
                                     (uint8_t)SHA256_DIGEST_LEN,
                                     certificate->signature.value,
                                     certificate->signature.length,
                                     parentCertificate->subjectPublicKey.rsaModulus,
                                     parentCertificate->subjectPublicKey.rsaExponent,
                                     slicing);
    }

    return retVal;
}

#endif /* (CRYPTOLIB_ALT_CERT_VERIFY == DISABLED) */

uint8_t CryptoLib_ParseCertificate(CRYPTOLIB_HUGE const uint8_t* const input,
                                   const uint16_t inputLen,
                                   CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS; /* return value of this function */

    /* check for valid input */
    if ((input == NULL) || (certificate == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_PARSECERTIFICATE | 0x00000001UL));
    }
    else
    {
        ret = CryptoLib_X509_Parse(input, inputLen, certificate);

        if (ret != (uint8_t)CRYPTO_ERR_SUCCESS)
        {
            /* reset certificate to the freshly initialized state */
            const uint8_t lret = CryptoLib_FreeCertificate(certificate);
            if (lret == (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                (void)CryptoLib_InitCertificate(certificate);
            }
        }
    }

    return ret;
}

uint8_t CryptoLib_InitCertificate(CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (certificate == NULL)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_INITCERTIFICATE | 0x00000001UL));
    }
    else
    {
        /* reset first level members of cert struct */
        certificate->version = 0;
        (void)memset(certificate->serial, 0, (size_t)X509_SERIAL_LEN);
        (void)memset(certificate->signature.algorithmOid, 0, (size_t)X509_SIG_OID_LEN);
        (void)memset(certificate->mdOfTbs, 0, (size_t)SHA256_DIGEST_LEN);
        certificate->signature.algorithm = NULL;
        certificate->signature.value = NULL;
        certificate->signature.length = 0;

        /* reset member of sub-struct issuer */
        certificate->issuer.country = NULL;
        certificate->issuer.organization = NULL;
        certificate->issuer.commonName = NULL;

        /* reset members of sub-struct validity */
        certificate->validity.notBefore = NULL;
        certificate->validity.notAfter = NULL;

        /* reset members of sub-struct subject */
        certificate->subject.organization = NULL;
        certificate->subject.organizationalUnit = NULL;
        certificate->subject.commonName = NULL;
        certificate->subject.country = NULL;
        certificate->subject.pseudonym = NULL;

        /* reset member of sub-struct subjectPublicKeyInfo */
        certificate->subjectPublicKey.publicKeyAlgorithm = NULL;
        certificate->subjectPublicKey.publicKeyType = CRYPTO_PK_NONE;
        certificate->subjectPublicKey.ecPublicKey = NULL;
        certificate->subjectPublicKey.rsaModulus = NULL;
        certificate->subjectPublicKey.rsaExponent = NULL;

        /* reset member of sub-struct extensions */
        certificate->extensions.keyUsage = 0;
        certificate->extensions.basicConstraints = 0;
    }

    return ret;
}

uint8_t CryptoLib_FreeCertificate(CRYPTOLIB_HUGE CryptoLib_Crt_t* certificate)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (certificate == NULL)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_FREECERTIFICATE | 0x00000001UL));
    }
    else
    {
        /* reset members of sub-struct signature */
        if (certificate->signature.algorithm != NULL)
        {
            mbedtls_free(certificate->signature.algorithm);
            certificate->signature.algorithm = NULL;
        }
        if (certificate->signature.value != NULL)
        {
            mbedtls_free(certificate->signature.value);
            certificate->signature.value = NULL;
        }

        /* reset members of sub-struct issuer */
        if (certificate->issuer.country != NULL)
        {
            mbedtls_free(certificate->issuer.country);
            certificate->issuer.country = NULL;
        }
        if (certificate->issuer.organization != NULL)
        {
            mbedtls_free(certificate->issuer.organization);
            certificate->issuer.organization = NULL;
        }
        if (certificate->issuer.commonName != NULL)
        {
            mbedtls_free(certificate->issuer.commonName);
            certificate->issuer.commonName = NULL;
        }

        /* reset members of sub-struct subject */
        if (certificate->subject.organization != NULL)
        {
            mbedtls_free(certificate->subject.organization);
            certificate->subject.organization = NULL;
        }
        if (certificate->subject.organizationalUnit != NULL)
        {
            mbedtls_free(certificate->subject.organizationalUnit);
            certificate->subject.organizationalUnit = NULL;
        }
        if (certificate->subject.commonName != NULL)
        {
            mbedtls_free(certificate->subject.commonName);
            certificate->subject.commonName = NULL;
        }
        if (certificate->subject.country != NULL)
        {
            mbedtls_free(certificate->subject.country);
            certificate->subject.country = NULL;
        }
        if (certificate->subject.pseudonym != NULL)
        {
            mbedtls_free(certificate->subject.pseudonym);
            certificate->subject.pseudonym = NULL;
        }

        /* reset member of sub-struct subjectPublicKeyInfo */
        if (certificate->subjectPublicKey.publicKeyAlgorithm != NULL)
        {
            mbedtls_free(certificate->subjectPublicKey.publicKeyAlgorithm);
            certificate->subjectPublicKey.publicKeyAlgorithm = NULL;
        }
        if (certificate->subjectPublicKey.ecPublicKey != NULL)
        {
            mbedtls_free(certificate->subjectPublicKey.ecPublicKey);
            certificate->subjectPublicKey.ecPublicKey = NULL;
        }
        if (certificate->subjectPublicKey.rsaModulus != NULL)
        {
            mbedtls_free(certificate->subjectPublicKey.rsaModulus);
            certificate->subjectPublicKey.rsaModulus = NULL;
        }
        if (certificate->subjectPublicKey.rsaExponent != NULL)
        {
            mbedtls_free(certificate->subjectPublicKey.rsaExponent);
            certificate->subjectPublicKey.rsaExponent = NULL;
        }

        /* reset member of sub-struct validity */
        if (certificate->validity.notBefore != NULL)
        {
            mbedtls_free(certificate->validity.notBefore);
            certificate->validity.notBefore = NULL;
        }
        if (certificate->validity.notAfter != NULL)
        {
            mbedtls_free(certificate->validity.notAfter);
            certificate->validity.notAfter = NULL;
        }
    }

    return ret;
}

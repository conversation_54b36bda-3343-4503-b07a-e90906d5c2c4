/**
 * @file  src/cryptolib_ecdh_compshared.c
 * @brief ECDH sliced compute shared function definition
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_ecdh_compshared.h"
#include "cryptolib/cryptolib_ecdh_mul.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#ifdef BENCHMARK_ACTIVE
#include "benchmarks/cryptolib_benchmarks.h"
#endif

#include <string.h>

#if ((CRYPTOLIB_NO_TIME_SLICING == DISABLED) && (CRYPTOLIB_ALT_ECDH == DISABLED))

/** Enum for statemachine states */
typedef enum {
    IDLE = 0, /**< Ready for new calls */
    ACTIVE = 1, /**< Perform sliced multiplication */
    FINAL = 2 /**< Write output and cleanup */
} ComputeSharedSlicingState_t;

/** Struct to hold pertinent ecdh variables */
typedef struct {
    mbedtls_ecp_point P; /**< Multiplication result */

    /** GCD slicing sub-state tag */
    enum {
        MUL_IDLE = 0, /**< GCD function not active */
        MUL_ACTIVE = 1 /**< GCD function in slice mode */
    } MulState; /**< GCD slicing sub-state */
} ComputeSharedSlicingContext_t;

/*
 * Static variables
 * ----------------------------------------------------------------------------
 */
CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t ECP_INIT_FLG = 0;     /**< Init flag for slicing context */
CRYPTOLIB_MEMORY_SECTION_END

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * Performs common sanity checks for ec keys
 *
 * @param[in] grp                   ECP group
 * @param[in] P                     Public key from other party
 * @param[in] m                     Our secret exponent (private key)
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: inverse successfully calculated<br>
 *                                  1 - CRYPTO_ERR_INVALID: invalid key<br>
 */
STATICFCN uint8_t ecp_MulSanityChecks(const mbedtls_ecp_group* grp,
                                      const mbedtls_mpi* m,
                                      const mbedtls_ecp_point* P);

/**
 * Initializes the context
 *
 * @param[in] grp                   ECP group
 * @param[in] peerPublicKey         Public key from other party
 * @param[in] context               ComputeShared context
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: inverse successfully calculated<br>
 *                                  1 - CRYPTO_ERR_INVALID: invalid key<br>
 */
STATICFCN uint8_t CryptoLib_SlicedCS_Init(const mbedtls_ecp_group* grp,
                                          const mbedtls_ecp_point* peerPublicKey,
                                          ComputeSharedSlicingContext_t* context);

/**
 * Clears the context and resets sub-statemachines
 *
 * @param[in] context               ComputeShared context
 */
STATICFCN void CryptoLib_SlicedCS_Clear(ComputeSharedSlicingContext_t* context);

/*
 * Static function definitions
 * ----------------------------------------------------------------------------
 */
STATICFCN uint8_t ecp_MulSanityChecks(const mbedtls_ecp_group* grp,
                                      const mbedtls_mpi* m,
                                      const mbedtls_ecp_point* P)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (mbedtls_mpi_cmp_int(&P->Z, (mbedtls_mpi_sint)1) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_ECP_MULSANITYCHECKS | 0x00000001UL));
    }
    else if (mbedtls_ecp_check_privkey(grp, m) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_ECP_MULSANITYCHECKS | 0x00000002UL));
    }
    else
    {
        /* MISRA */
    }

    return ret;
}

STATICFCN uint8_t CryptoLib_SlicedCS_Init(const mbedtls_ecp_group* grp,
                                          const mbedtls_ecp_point* peerPublicKey,
                                          ComputeSharedSlicingContext_t* context)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    CryptoLib_SlicedCS_Clear(context);
    mbedtls_ecp_point_init(&context->P);
    ECP_INIT_FLG = (uint8_t)1;
    context->MulState = MUL_IDLE;

    if (mbedtls_ecp_check_pubkey(grp, peerPublicKey) != (int32_t)CRYPTO_ERR_SUCCESS)
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_SLICEDCS_INIT | 0x00000001UL));
    }

    return ret;
}

STATICFCN void CryptoLib_SlicedCS_Clear(ComputeSharedSlicingContext_t* context)
{
    if (ECP_INIT_FLG == (uint8_t)1)
    {
        mbedtls_ecp_point_free(&context->P);
    }

    if (context->MulState != MUL_IDLE)
    {
        (void)CryptoLib_SlicedEcpMul(NULL,
                                     NULL,
                                     NULL,
                                     NULL,
                                     SLICE_RESET);
    }

    (void)memset(context, 0, sizeof(ComputeSharedSlicingContext_t));
}

/*
 * Public function definitions
 * ----------------------------------------------------------------------------
 */

uint8_t CryptoLib_SlicedComputeShared(const mbedtls_ecp_group* grp,
                                      mbedtls_mpi* sharedSecret,
                                      const mbedtls_ecp_point* peerPublicKey,
                                      const mbedtls_mpi* privateKey,
                                      const CryptoLib_Slicing_t slicing)
{
    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static ComputeSharedSlicingContext_t context;
    static ComputeSharedSlicingState_t state = IDLE;
    CRYPTOLIB_MEMORY_SECTION_END

    CryptoLib_Slicing_t slc = slicing;
    uint8_t retval = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (slc == SLICE_RESET)
    {
        /* initial return value will trigger a reset further below */
    }
    else if ((grp == NULL) || (peerPublicKey == NULL) || (privateKey == NULL) ||
             (sharedSecret == NULL))
    {
        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SLICEDCOMPUTESHARED | 0x00000001UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        switch (state)
        {
            case IDLE:
                #ifdef BENCHMARK_ACTIVE
                BENCH("cs_i,");
                #endif
                if (slc == SLICE_INIT)
                {
                    retval = CryptoLib_SlicedCS_Init(grp, peerPublicKey, &context);

                    /* set to next state if ready, reset in case of errors */
                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* Init done, go to ACTIVE */
                        state = ACTIVE;
                        retval = (uint8_t)CRYPTO_ERR_CALLAGAIN;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDCOMPUTESHARED | 0x00000002UL));
                }
                break;

            case ACTIVE:
                if (slc == SLICE_CONTINUE)
                {
                    if (context.MulState == MUL_IDLE)
                    {
                        retval = ecp_MulSanityChecks(grp, privateKey, peerPublicKey);

                        if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                        {
                            /* Begin new MUL slicing */
                            retval = CryptoLib_SlicedEcpMul(grp,
                                                            &context.P,
                                                            privateKey,
                                                            peerPublicKey,
                                                            SLICE_INIT);
                        }

                        if (retval == (uint8_t)CRYPTO_ERR_CALLAGAIN)
                        {
                            context.MulState = MUL_ACTIVE;
                        }
                    }
                    else if (context.MulState == MUL_ACTIVE)
                    {
                        /* Continue active MUL slicing */
                        retval = CryptoLib_SlicedEcpMul(grp,
                                                        &context.P,
                                                        privateKey,
                                                        peerPublicKey,
                                                        SLICE_CONTINUE);

                        if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
                        {
                            context.MulState = MUL_IDLE;
                        }
                    }
                    else /* LCOV_EXCL_START: internal enum out of bounds */
                    {
                        /* invalid MUL sub-state. Mark as error and let the case
                         * below handle this error */
                        CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDCOMPUTESHARED | 0x00000004UL));
                        /* LCOV_EXCL_STOP */
                    }

                    /* set to next state if ready, reset in case of errors */
                    if (retval == (uint8_t)CRYPTO_ERR_SUCCESS)
                    {
                        /* WORK done, go to FINAL */
                        state = FINAL;
                    }
                    else
                    {
                        break;
                    }
                }
                else
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SLICEDCOMPUTESHARED | 0x00000005UL));
                    break;
                }
            /* fallthrough */

            case FINAL:
                #ifdef BENCHMARK_ACTIVE
                BENCH("cs_f,");
                #endif
                if (mbedtls_ecp_is_zero(&context.P) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_INTERNAL, (uint32_t)(FID_CRYPTOLIB_SLICEDCOMPUTESHARED | 0x00000006UL));
                }
                else if (mbedtls_mpi_copy(sharedSecret, &context.P.X) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ALLOC_FAILED, (uint32_t)(FID_CRYPTOLIB_SLICEDCOMPUTESHARED | 0x00000008UL));
                }
                else
                {
                    /* nop */
                }
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retval, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_SLICEDCOMPUTESHARED | 0x0000000aUL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
    }

    if (retval != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_SlicedCS_Clear(&context);
        state = IDLE;
    }

    return retval;
}

#endif /* #if ((CRYPTOLIB_NO_TIME_SLICING == DISABLED) && (CRYPTOLIB_ALT_ECDH == DISABLED)) */

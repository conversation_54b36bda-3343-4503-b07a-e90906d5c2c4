/**
 * @file  src/cryptolib_diag.c
 * @brief Error mapping of mbedTLS error codes to Cryptolib errors
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 R<PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_diag.h"

#include "mbedtls/bignum.h"
#include "mbedtls/x509.h"
#include "mbedtls/cipher.h"
#include "mbedtls/oid.h"

CRYPTOLIB_MEMORY_SECTION_BEGIN
static CryptoLib_LastError_t lastError;    /**< Global variable to hold information about the last occured error. */
CRYPTOLIB_MEMORY_SECTION_END

/**
 * This function maps mbedTLS error codes to CryptoLib's error codes.
 *
 * @param[in] mbedtlsError          mbedTLS error code
 *
 * @return                          12 - CRYPTO_ERR_BAD_INPUT: bad input parameter<br>
 *                                  14 - CRYPTO_ERR_ALLOC_FAILED: memory allocation failed<br>
 *                                  15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedtls specific error<br>
 */
STATICFCN uint8_t CryptoLib_MapMbedTlsErrorCode(int32_t mbedtlsError);

void CryptoLib_SetError(uint8_t* err, int32_t detailedError, uint32_t id)
{
    if (detailedError >= 0)
    {
        *err = (uint8_t)detailedError;
    }
    else
    {
        *err = CryptoLib_MapMbedTlsErrorCode(detailedError);
    }

    lastError.errorCode = *err;
    lastError.detailedCode = detailedError;
    lastError.id = id;
}

CryptoLib_LastError_t CryptoLib_GetLastError(void)
{
    return lastError;
}

STATICFCN uint8_t CryptoLib_MapMbedTlsErrorCode(int32_t mbedtlsError)
{
    uint8_t retval = 0;

    if (  (mbedtlsError == MBEDTLS_ERR_MPI_ALLOC_FAILED)
       || (mbedtlsError == MBEDTLS_ERR_X509_ALLOC_FAILED)
       || (mbedtlsError == MBEDTLS_ERR_MD_ALLOC_FAILED)
       || (mbedtlsError == MBEDTLS_ERR_ASN1_ALLOC_FAILED)
       || (mbedtlsError == MBEDTLS_ERR_ECP_ALLOC_FAILED)
       || (mbedtlsError == MBEDTLS_ERR_CIPHER_ALLOC_FAILED)
       || (mbedtlsError == MBEDTLS_ERR_PK_ALLOC_FAILED))
    {
        retval = (uint8_t)CRYPTO_ERR_ALLOC_FAILED;
    }
    else if (  (mbedtlsError == MBEDTLS_ERR_MPI_BAD_INPUT_DATA)
            || (mbedtlsError == MBEDTLS_ERR_CIPHER_BAD_INPUT_DATA)
            || (mbedtlsError == MBEDTLS_ERR_MD_BAD_INPUT_DATA)
            || (mbedtlsError == MBEDTLS_ERR_ECP_BAD_INPUT_DATA)
            || (mbedtlsError == MBEDTLS_ERR_RSA_BAD_INPUT_DATA))
    {
        retval = (uint8_t)CRYPTO_ERR_BAD_INPUT;
    }
    else if ((mbedtlsError == MBEDTLS_ERR_X509_SIG_MISMATCH))
    {
        retval = (uint8_t)CRYPTO_ERR_SIGNATURE_INVALID;
    }
    else if ((mbedtlsError == MBEDTLS_ERR_OID_NOT_FOUND) ||
             ((mbedtlsError <= MBEDTLS_ERR_X509_FEATURE_UNAVAILABLE) &&
              (mbedtlsError >= MBEDTLS_ERR_X509_FATAL_ERROR)) ||
             ((mbedtlsError <= MBEDTLS_ERR_PK_SIG_LEN_MISMATCH) &&
              (mbedtlsError >= MBEDTLS_ERR_PK_TYPE_MISMATCH)))
    {
        retval = (uint8_t)CRYPTO_ERR_INVALID;
    }
    else
    {
        retval = (uint8_t)CRYPTO_ERR_MBEDTLS_FAILED;
    }

    return retval;
}

/**
 * @file
 * @brief Contains utility function definitions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_utils.h"
#include "mbedtls/platform_util.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

/* polyspace +1 MISRA-C3:8.13 [Not a defect:Low] "Value of memory is changed in sub-functions" */
void CryptoLib_Zeroize(CRYPTOLIB_HUGE void* memory, size_t count)
{
#if defined(__STDC_LIB_EXT1__)
    /* polyspace +1 MISRA-C3:17.3 [Not a defect:Low] "Polyspace not working correctly, if STDC_LIB_EXT1 is defined also a prototype of memset_s is in string.h." */
    (void)memset_s(memory, count, 0, count);
#else
    mbedtls_platform_zeroize(memory, count);
#endif /* #ifdef (__STDC_LIB_EXT1__) */
}

#if (CRYPTOLIB_ALT_KEYS == ENABLED)
#define CHARS_PER_BYTE      2u      /**< Number of characters needed to represent one byte in Base16 */
#define NIBBLE_SHIFT_FACTOR 0x10u   /**< Factor equal to 4 bit left shift */
#define ASCII_0             0x30u   /**< ASCII value for '0' */
#define ASCII_9             0x39u   /**< ASCII value for '9' */
#define ASCII_NUM_OFFSET    0x30u   /**< '0' - 0x00 */
#define ASCII_A             0x41u   /**< ASCII value for 'A' */
#define ASCII_F             0x46u   /**< ASCII value for 'F' */
#define ASCII_UC_OFFSET     0x37u   /**< 'A' - 0xA */
#define ASCII_a             0x61u   /**< ASCII value for 'a' */
#define ASCII_f             0x66u   /**< ASCII value for 'f' */
#define ASCII_LC_OFFSET     0x57u   /**< 'a' - 0xa */

uint8_t CryptoLib_ConvertKeyStringToByteArray(const char* keyString, uint8_t* keyArray, size_t keyArraySize)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    size_t i = 0u;

    if ((keyString == NULL) || (keyArray == NULL))
    {
        ret = (uint8_t)CRYPTO_ERR_NULL_POINTER;
    }
    else
    {
        const size_t slen = strlen(keyString);
        if ((slen % CHARS_PER_BYTE) != 0u)
        {
            ret = (uint8_t)CRYPTO_ERR_BAD_INPUT;
        }
        else if ((slen / CHARS_PER_BYTE) > keyArraySize)
        {
            ret = (uint8_t)CRYPTO_ERR_BAD_INPUT;
        }
        else
        {
            for (i = 0u; i < slen; i++)
            {
                uint8_t c = (uint8_t)keyString[i];
                uint8_t factor = 1u;
                if (i % CHARS_PER_BYTE == 0)
                {
                    factor = NIBBLE_SHIFT_FACTOR;
                    keyArray[i / CHARS_PER_BYTE] = 0u;
                }

                if ((c >= ASCII_0) && (c <= ASCII_9))
                {
                    keyArray[i / CHARS_PER_BYTE] += (uint8_t)(c - ASCII_NUM_OFFSET) * factor;
                }
                else if ((c >= ASCII_A) && (c <= ASCII_F))
                {
                    keyArray[i / CHARS_PER_BYTE] += (uint8_t)(c - ASCII_UC_OFFSET) * factor;
                }
                else if ((c >= ASCII_a) && (c <= ASCII_f))
                {
                    keyArray[i / CHARS_PER_BYTE] += (uint8_t)(c - ASCII_LC_OFFSET) * factor;
                }
                else
                {
                    ret = (uint8_t)CRYPTO_ERR_BAD_INPUT;
                    break;
                }
            }
        }
    }

    return ret;
}

#endif /* #if (CRYPTOLIB_ALT_KEYS == ENABLED) */

/**
 * @file   cryptolib_common.h
 * @brief  Common functions used in multiple modules
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_common.h"
#include "cryptolib/cryptolib_types.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

uint8_t CryptoLib_ChangeEndiannessString(CRYPTOLIB_HUGE const char* inKey, char* outKey, const size_t outKeyLen)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;

    size_t len = strlen(inKey);
    size_t i;

    if (outKeyLen >= (len + 1u))
    {
        for (i = 0; i < len; i += 2u)
        {
            (void)memcpy(&outKey[i], &inKey[(len - i) - 2u], (size_t)2);
        }

        outKey[outKeyLen - 1u] = (char)0;
    }
    else
    {
        ret = (uint8_t)CRYPTO_ERR_BAD_INPUT;
    }

    return ret;
}

void CryptoLib_ChangeEndiannessBinary(CRYPTOLIB_HUGE uint8_t* data, const size_t dataLength)
{
    uint8_t temp = 0u;
    size_t i = 0u;
    size_t end = 0u;

    for (i = 0u; i < (dataLength / 2u); i++)
    {
        temp = data[i];
        end = dataLength - (i + 1u);

        data[i] = data[end];
        data[end] = temp;
    }
}

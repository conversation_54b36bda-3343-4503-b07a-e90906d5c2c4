/**
 * @file  src/cryptolib_hash.c
 * @brief Hash function definitions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_hash.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#include "mbedtls/sha256.h"
#include "mbedtls/md.h"
#include "mbedtls/md_internal.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if (CRYPTOLIB_ALT_KDF == DISABLED)

#define STRING_COUNTER_LEN 32u   /**< Size of the internal counter of KDF for string representation */
#define BINARY_COUNTER_LEN 4u   /**< Size of the internal counter of KDF for binary representation */
#define MAX_KDF_COUNTER_LEN 33u /**< Maximum counter length for internal counter of KDF */

/* polyspace-begin MISRA-C3:17.8 [No action planned:Low] "Helper function is used to convert the number to a string, no risk of misinterpretation." */

/**
 * Converts an uint32_t to a literal binary string representation.
 * I.e. 0b010101 -> ['0', '1', '0', '1', '0', '1', \0]
 *
 * @param[in]  number       The number to be converted.
 * @param[out] buffer       The buffer that will be filled with the binary string representation.
 */
STATICFCN void convertToLiteral(uint32_t number, char buffer[MAX_KDF_COUNTER_LEN])
{
    int8_t i;

    for (i = 31; i >= 0; i--)
    {
        buffer[i] = (char)((number & 1u) + '0');
        number >>= 1;
    }
    buffer[32] = (char)0;
}

/* polyspace-end */

/**
 * Converts an uint32_t to a binary array in big endian.
 *
 * @param[in]  number       The number to be converted.
 * @param[out] buffer       The buffer that will be filled with the binary representation.
 */
STATICFCN void uint32ToBE(uint32_t number, uint8_t buffer[BINARY_COUNTER_LEN])
{
    buffer[0] = (uint8_t)((number >> 24u) & 0xFFu);
    buffer[1] = (uint8_t)((number >> 16u) & 0xFFu);
    buffer[2] = (uint8_t)((number >> 8u) & 0xFFu);
    buffer[3] = (uint8_t)((number) & 0xFFu);
}

/**
 * General implementation of KDF according to NIST SP 800-56A rev2 5.8.1.1 using
 * HMAC and salt. OtherInfo is not supported.
 * Using algortihm also the legace implementation deviating from NIST specification
 * can be executed.
 *
 * @param[in]  mdInfo                 Struct holding the info about the MD to be used
 * @param[in]  inputKey               Pointer to input key material
 * @param[in]  inputKeyLen            Length of input key
 * @param[in]  salt                   Pointer to salt
 * @param[in]  saltLen                Length of salt
 * @param[in]  otherInfo              Bit string of well-defined other info, maybe NULL
 * @param[in]  otherInfoLen           Length of otherInfo
 * @param[in]  algorithm              Algorithm to be used in KDF
 * @param[out] outputKey              Pointer to output key material
 * @param[in]  outputKeyLen           Length of output key material
 *
 * @return                            0 - CRYPTO_ERR_SUCCESS: key successfully derived<br>
 *                                    1 - CRYPTO_ERR_INVALID: invalid input data<br>
 *                                    4 - CRYPTO_ERR_INTERNAL: an error occured<br>
 *                                    12 - CRYPTO_ERR_BAD_INPUT: Bad input parameter<br>
 *                                    15 - CRYPTO_ERR_MBEDTLS_FAILED: mbedTLS returned error<br>
 */
STATICFCN uint8_t CryptoLib_Kdf_General(const mbedtls_md_info_t* mdInfo,
                                        const uint8_t* inputKey,
                                        const uint16_t inputKeyLen,
                                        const uint8_t* salt,
                                        const uint16_t saltLen,
                                        const uint8_t* otherInfo,
                                        const uint16_t otherInfoLen,
                                        const CryptoLib_Kdf_t algorithm,
                                        uint8_t* outputKey,
                                        const uint32_t outputKeyLen);

#endif /* (CRYPTOLIB_ALT_KDF == DISABLED) */

uint8_t CryptoLib_Sha256(CRYPTOLIB_HUGE const uint8_t* input,
                         const uint16_t inputLen,
                         CRYPTOLIB_HUGE uint8_t output[SHA256_DIGEST_LEN])
{
    uint8_t ret;
    if ((input != NULL) && (output != NULL))
    {
        (void)mbedtls_sha256_ret(input, (size_t)inputLen, output, 0);
        ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SHA256 | 0x00000001UL));
    }

    return ret;
}

uint8_t CryptoLib_Sha256Start(CRYPTOLIB_HUGE CryptoLib_HashContext_t* context)
{
    uint8_t ret;

    if (context != NULL)
    {
        /* polyspace-begin MISRA-C3:11.3 [Not a defect:Low] "Cast between CryptoLib_HashContext_t and mbedtls_sha256_context is permissible,
         *                                                    since both structs are equivalent to each other." */
        mbedtls_sha256_init((mbedtls_sha256_context*)context);
        (void)mbedtls_sha256_starts_ret((mbedtls_sha256_context*)context, 0);
        ret = (uint8_t)CRYPTO_ERR_SUCCESS;
        /* polyspace-end */
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SHA256START | 0x00000001UL));
    }

    return ret;
}

uint8_t CryptoLib_Sha256Update(CRYPTOLIB_HUGE CryptoLib_HashContext_t* context,
                               CRYPTOLIB_HUGE const uint8_t* input,
                               const uint16_t inputLen)
{
    uint8_t ret;

    if ((context != NULL) && (input != NULL))
    {
        /* polyspace-begin MISRA-C3:11.3 [Not a defect:Low] "Cast between CryptoLib_HashContext_t and mbedtls_sha256_context is permissible,
         *                                                    since both structs are equivalent to each other." */
        (void)mbedtls_sha256_update_ret((mbedtls_sha256_context*)context, input, (size_t)inputLen);
        ret = (uint8_t)CRYPTO_ERR_SUCCESS;
        /* polyspace-end */
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SHA256UPDATE | 0x00000001UL));
    }

    return ret;
}

uint8_t CryptoLib_Sha256Finish(CRYPTOLIB_HUGE CryptoLib_HashContext_t* context,
                               CRYPTOLIB_HUGE uint8_t output[SHA256_DIGEST_LEN])
{
    uint8_t ret;
    if ((context != NULL) && (output != NULL))
    {
        /* polyspace-begin MISRA-C3:11.3 [Not a defect:Low] "Cast between CryptoLib_HashContext_t and mbedtls_sha256_context is permissible,
         *                                                    since both structs are equivalent to each other." */
        (void)mbedtls_sha256_finish_ret((mbedtls_sha256_context*)context, output);
        mbedtls_sha256_free((mbedtls_sha256_context*)context);
        ret = (uint8_t)CRYPTO_ERR_SUCCESS;
        /* polyspace-end */
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SHA256FINISH | 0x00000001UL));
    }

    return ret;
}

#if (CRYPTOLIB_ALT_KDF == DISABLED)

STATICFCN uint8_t CryptoLib_Kdf_General(const mbedtls_md_info_t* mdInfo,
                                        const uint8_t* inputKey,
                                        const uint16_t inputKeyLen,
                                        const uint8_t* salt,
                                        const uint16_t saltLen,
                                        const uint8_t* otherInfo,
                                        const uint16_t otherInfoLen,
                                        const CryptoLib_Kdf_t algorithm,
                                        uint8_t* outputKey,
                                        const uint32_t outputKeyLen)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    const uint16_t maxInputLen = 2 * MBEDTLS_MD_MAX_SIZE; /* arbitrary value */
    uint8_t tmp[MBEDTLS_MD_MAX_SIZE]; /* used for hmac */
    uint32_t mod;
    uint32_t i;
    uint32_t pos = 0;
    uint32_t counter = 0x1;
    uint8_t counterLen = BINARY_COUNTER_LEN;
    uint8_t counterBuf[MAX_KDF_COUNTER_LEN];

    if (algorithm == KDF_ITK_HMAC_SHA256)
    {
        counterLen = STRING_COUNTER_LEN;
    }

    mbedtls_md_context_t ctx;

    if ((mdInfo != NULL) && (mdInfo->size > 0) &&
        (((uint32_t)inputKeyLen + (uint32_t)counterLen + (uint32_t)otherInfoLen) <= (uint32_t)maxInputLen))
    {
        /* compute number of iterations: ceil(derivedKeyLen/hashLen) */
        uint32_t reps = outputKeyLen / (uint16_t)mdInfo->size;
        mod = (outputKeyLen % (uint16_t)mdInfo->size);
        if (mod != (uint16_t)0)
        {
            /* no overflow of reps possible at this point, as mod != 0 only if mdInfo->size > 1
             * but in this case reps <= UINT32_MAX / 2. */
            reps++;
        }

        /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "return value used in larger expression for clearer control flow that is less error prone. Use is safe." */
        /* print counter in binary format into Z */
        mbedtls_md_init(&ctx);
        if ((mret = mbedtls_md_setup(&ctx, mdInfo, 1)) == (int32_t)CRYPTO_ERR_SUCCESS)
        {
            for (i = 1; (ret == (uint8_t)CRYPTO_ERR_SUCCESS) && (i <= reps); i++)
            {
                if (algorithm == KDF_ITK_HMAC_SHA256)
                {
                    convertToLiteral(counter, (char*)counterBuf);
                }
                else
                {
                    uint32ToBE(counter, counterBuf);
                }

                if ((mret = mbedtls_md_hmac_starts(&ctx, salt, (size_t)saltLen)) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_KDF_GENERAL | 0x00000003UL));
                    ret = (uint8_t)CRYPTO_ERR_INTERNAL;
                }
                else if ((mret = mbedtls_md_hmac_update(&ctx, counterBuf, (size_t)counterLen)) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_KDF_GENERAL | 0x00000004UL));
                    ret = (uint8_t)CRYPTO_ERR_INTERNAL;
                }
                else if ((mret = mbedtls_md_hmac_update(&ctx, inputKey, (size_t)inputKeyLen)) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_KDF_GENERAL | 0x00000005UL));
                    ret = (uint8_t)CRYPTO_ERR_INTERNAL;
                }
                else if ((mret = mbedtls_md_hmac_update(&ctx, otherInfo, (size_t)otherInfoLen)) != (int32_t)CRYPTO_ERR_SUCCESS)
                {
                    CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_KDF_GENERAL | 0x00000006UL));
                    ret = (uint8_t)CRYPTO_ERR_INTERNAL;
                }
                else
                {
                    if ((mret = mbedtls_md_hmac_finish(&ctx, tmp)) == (int32_t)CRYPTO_ERR_SUCCESS)
                    {
                        (void)memcpy(&outputKey[pos], tmp, (i != reps) ? (size_t)mdInfo->size : (size_t)(outputKeyLen - pos));
                        pos += (uint16_t)mdInfo->size;

                        /* no need to handle overflow for counter, as counter is only incremented
                        * once per loop. Starting with 1 (as i) it can reach UINT32_MAX at max */
                        counter++;
                    }
                    else
                    {
                        CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_KDF_GENERAL | 0x00000007UL));
                        ret = (uint8_t)CRYPTO_ERR_INTERNAL;
                    }
                }
            }
        } /* mbedtls_md_setup */
        else
        {
            CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_KDF_GENERAL | 0x00000002UL));
            ret = (uint8_t)CRYPTO_ERR_INTERNAL;
        }
        mbedtls_md_free(&ctx);
        /* polyspace-end */
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_KDF_GENERAL | 0x00000001UL));
    }

    return ret;
}

uint8_t CryptoLib_KeyDerivationFunction(CRYPTOLIB_HUGE const uint8_t* sharedSecret,
                                        const uint16_t sharedSecretLen,
                                        CRYPTOLIB_HUGE const uint8_t* salt,
                                        const uint16_t saltLen,
                                        const CryptoLib_Kdf_t algorithm,
                                        CRYPTOLIB_HUGE uint8_t derivedKey[SHA256_DIGEST_LEN])
{
    uint8_t ret;
    if ((sharedSecret != NULL) && (salt != NULL) && (derivedKey != NULL))
    {
        const mbedtls_md_info_t* info = mbedtls_md_info_from_type(MBEDTLS_MD_SHA256);

        ret = CryptoLib_Kdf_General(info, sharedSecret, sharedSecretLen, salt, saltLen, NULL, 0, algorithm, derivedKey, SHA256_DIGEST_LEN);
    }
    else
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_KEYDERIVATIONFUNCTION | 0x00000001UL));
    }

    return ret;
}

#endif /* (CRYPTOLIB_ALT_KDF == ENABLED) */

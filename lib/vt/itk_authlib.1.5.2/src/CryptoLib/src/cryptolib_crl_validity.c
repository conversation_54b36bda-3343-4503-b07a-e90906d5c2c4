/**
 * @file  src/cryptolib_crl_validity.c
 * @brief CRL function definitions for checking validity of a CRL
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 Rülzheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_crl.h"
#include "cryptolib/cryptolib_rsa.h"
#include "cryptolib/cryptolib_hash.h"
#include "cryptolib/cryptolib_asn1.h"
#include "cryptolib/cryptolib_crl_validity.h"
#include "cryptolib/cryptolib_diag.h"
#include "cryptolib/cryptolib_fid.h"

#include "mbedtls/asn1.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

/*
 * Static prototypes
 * ----------------------------------------------------------------------------
 */

/**
 * This function resets the context needed for memory slicing the CRL.
 *
 * @param[out] ctx          Pointer to the sliced CRL context
 */
STATICFCN void CryptoLib_ResetCrlValidate(CheckCRLMemorySlicedContext_t* ctx);

/**
 * This function initializes the context needed for memory slicing the CRL.
 * Length of the complete CRL and TBS data is determined. TBS data of the
 * first slice is hashed.
 *
 * @param[in] crlBuffer             Pointer to the buffer where the piece of CRL is stored
 * @param[in] crlLength             Size of the piece of CRL which shall be read
 * @param[out] ctx                  Pointer to the sliced CRL context
 * @param[in,out] slcMem            Memory slicing parameter
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: Initialization successful<br>
 *                                  1 - CRYPTO_ERR_INVALID: Invalid CRL<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: CRL is not complete<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: Slicing parameter is invalid<br>
 */
STATICFCN uint8_t CryptoLib_InitCrlValidate(const uint8_t* crlBuffer,
                                            const uint16_t crlLength,
                                            CheckCRLMemorySlicedContext_t* ctx,
                                            uint8_t* slcMem);

/**
 * This function updates the hash context until all TBS data is received.
 *
 * @param[in] crlBuffer              Pointer to the buffer where the piece of CRL is stored
 * @param[in] crlLength             Size of the piece of CRL which shall be read
 * @param[in,out] ctx               Pointer to the sliced CRL context
 * @param[in] slcMem                Memory slicing parameter
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: Hash updating successful<br>
 *                                  1 - CRYPTO_ERR_INVALID: CRL invalid<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: CRL is not complete<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: Slicing parameter is invalid<br>
 */
STATICFCN uint8_t CryptoLib_HashUpdateCrlValidate(const uint8_t* crlBuffer,
                                                  const uint16_t crlLength,
                                                  CheckCRLMemorySlicedContext_t* ctx,
                                                  const uint8_t* slcMem);

/**
 * This function updates the local signature array until all signature bytes
 * are received.
 *
 * @param[in] crlBuffer              Pointer to the buffer where the piece of CRL is stored
 * @param[in] crlLength             Size of the piece of CRL which shall be read
 * @param[in,out] ctx               Pointer to the sliced CRL context
 * @param[in] slcMem                Memory slicing parameter
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: Signature updating successful<br>
 *                                  1 - CRYPTO_ERR_INVALID: CRL invalid<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: CRL is not complete<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: Slicing parameter is invalid<br>
 */
STATICFCN uint8_t CryptoLib_SignatureUpdateCrlValidate(const uint8_t* crlBuffer,
                                                       const uint16_t crlLength,
                                                       CheckCRLMemorySlicedContext_t* ctx,
                                                       const uint8_t* slcMem);

/**
 * This function verifies the signature of the CRL using the RSASSA-PSS algorithm.
 *
 * @param[in] crlSigningCA          Pointer to the CRL-Signing sub CA certificate
 * @param[in] ctx                   Pointer to the sliced CRL context
 * @param[in,out] slicingTime       Memory slicing parameter
 *
 * @return                          0 - CRYPTO_ERR_SUCCESS: Verifying signature successful<br>
 *                                  1 - CRYPTO_ERR_INVALID: One or more invalid parameters passed to CryptoLib_VerifyRsa<br>
 *                                  2 - CRYPTO_ERR_CALLAGAIN: Verification is not completed<br>
 *                                  6 - CRYPTO_ERR_SIGNATURE_INVALID: Verification failed<br>
 *                                  7 - CRYPTO_ERR_NULL_POINTER: null pointer<br>
 *                                  12 - CRYPTO_ERR_BAD_INPUT: Slicing parameter is invalid<br>
 *                                  16 - CRYPTO_ERR_ENUM_OUT_OF_BOUNDS: internal enum out of bounds<br>
 */
STATICFCN uint8_t CryptoLib_VerifyRsaCrlValidate(const CryptoLib_Crt_t* const crlSigningCA,
                                                 const CheckCRLMemorySlicedContext_t* ctx,
                                                 const uint8_t slicingTime);

/*
 * Function definitions
 * ----------------------------------------------------------------------------
 */

STATICFCN void CryptoLib_ResetCrlValidate(CheckCRLMemorySlicedContext_t* ctx)
{
    ctx->flgInit = 1;
    ctx->offsetCrl = 0;
    ctx->stateMemSlice = INIT;
    ctx->crlLen = 0;
    ctx->tbsLen = 0;
    ctx->signatureStartLen = 0;
    ctx->crlLenDone = 0;
    ctx->tbsLenLeft = 0;
    ctx->signatureLenLeft = RSASSA_PSS_SIGNATURE_LEN;
    (void)memset(ctx->hashDigest, 0, (size_t)SHA256_DIGEST_LEN);
    (void)memset(ctx->signature, 0, (size_t)RSASSA_PSS_SIGNATURE_LEN);
    (void)memset(&ctx->hashCtx, 0, sizeof(CryptoLib_HashContext_t));

#if (CRYPTOLIB_NO_TIME_SLICING == DISABLED)
    (void)CryptoLib_VerifyRsa(NULL, 0, NULL, 0, NULL, NULL, SLICE_RESET);
#endif
}

STATICFCN uint8_t CryptoLib_InitCrlValidate(const uint8_t* crlBuffer,
                                            const uint16_t crlLength,
                                            CheckCRLMemorySlicedContext_t* ctx,
                                            uint8_t* slcMem)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;
    const uint8_t* start;
    uint32_t len, offsetTbs;
    const uint8_t* end;

    if ((*slcMem != (uint8_t)SLICE_INIT) && (*slcMem != (uint8_t)SLICE_NO_SLICING))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_INITCRLVALIDATE | 0x00000001UL));
    }
    else if (crlBuffer == NULL)
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_INITCRLVALIDATE | 0x00000002UL));
    }
    else
    {
        CryptoLib_ResetCrlValidate(ctx);
        start = crlBuffer;
        end = &crlBuffer[crlLength];

        /* polyspace +1 MISRA-C3:17.8 [No action planned:low] "crlBuffer is used as an iterator thus changing the parameter is intended." */
        if (CryptoLib_Asn1GetTag(&crlBuffer, end, &len,
                                 ((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_INITCRLVALIDATE | 0x00000003UL));
        }
        else
        {
            ctx->offsetCrl = (uint8_t)(crlBuffer - start);
            start = crlBuffer;
            ctx->crlLen = len + ctx->offsetCrl;
            ctx->signatureStartLen = ctx->crlLen - (uint32_t)RSASSA_PSS_SIGNATURE_LEN;

            /* polyspace +1 MISRA-C3:17.8 [No action planned:low] "crlBuffer is used as an iterator thus changing the parameter is intended." */
            if (CryptoLib_Asn1GetTag(&crlBuffer, end, &len,
                                     ((uint8_t)MBEDTLS_ASN1_CONSTRUCTED | (uint8_t)MBEDTLS_ASN1_SEQUENCE)) == (int32_t)CRYPTO_ERR_SUCCESS)
            {
                offsetTbs = (uint32_t)(crlBuffer - start);
                ctx->tbsLen = len + offsetTbs;
                ctx->tbsLenLeft = ctx->tbsLen;
                ctx->stateMemSlice = HASH_UPDATE;
                *slcMem = (uint8_t)SLICE_CONTINUE;
            }
            else
            {
                CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_INITCRLVALIDATE | 0x00000004UL));
            }
        }
    }

    return retVal;
}

STATICFCN uint8_t CryptoLib_HashUpdateCrlValidate(const uint8_t* crlBuffer,
                                                  const uint16_t crlLength,
                                                  CheckCRLMemorySlicedContext_t* ctx,
                                                  const uint8_t* slcMem)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint16_t crlBufferIdx = 0;
    uint32_t tbsLenToHash = 0;

    if (ctx->stateMemSlice != HASH_UPDATE)
    {
        /* nothing to do here: fallthrough */
    }
    else if ((*slcMem != (uint8_t)SLICE_CONTINUE) && (*slcMem != (uint8_t)SLICE_FINISH))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_HASHUPDATECRLVALIDATE | 0x00000001UL));
    }
    else if (crlBuffer == NULL)
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_HASHUPDATECRLVALIDATE | 0x00000002UL));
    }
    else if (ctx->flgInit == 1u)
    {
        /* crlBuffer contains the complete tbs */
        if (crlLength >= (ctx->tbsLen + ctx->offsetCrl))
        {
            /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
            if ((retVal = CryptoLib_Sha256(&crlBuffer[ctx->offsetCrl], (const uint16_t)ctx->tbsLen, ctx->hashDigest)) == (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                ctx->tbsLenLeft = 0;
                ctx->stateMemSlice = SIGNATURE_UPDATE;
            }
        }
        /* crlBuffer doesn't contain the complete tbs */
        else
        {
            /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
            if ((retVal = CryptoLib_Sha256Start(&ctx->hashCtx)) == (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                crlBufferIdx = ctx->offsetCrl;
                tbsLenToHash = (uint32_t)((uint32_t)crlLength - (uint32_t)ctx->offsetCrl);
                ctx->crlLenDone = crlLength;
                ctx->tbsLenLeft = (ctx->tbsLen - (uint32_t)((uint32_t)crlLength - (uint32_t)ctx->offsetCrl));
                retVal = (uint8_t)CRYPTO_ERR_CALLAGAIN;
            }
        }
        ctx->flgInit = 0;
    }
    /* crlBuffer still doesn't contain the complete tbs */
    else if (crlLength < ctx->tbsLenLeft)
    {
        crlBufferIdx = 0;
        tbsLenToHash = crlLength;
        ctx->crlLenDone += crlLength;
        ctx->tbsLenLeft -= crlLength;
        retVal = (uint8_t)CRYPTO_ERR_CALLAGAIN;
    }
    /* crlBuffer now contains the complete tbs */
    else
    {
        crlBufferIdx = 0;
        tbsLenToHash = ctx->tbsLenLeft;
        ctx->tbsLenLeft = 0;
        ctx->stateMemSlice = SIGNATURE_UPDATE;
    }

    if ((tbsLenToHash != 0u) && (crlBuffer != NULL))
    {
        if (CryptoLib_Sha256Update(&ctx->hashCtx, &crlBuffer[crlBufferIdx], (uint16_t)tbsLenToHash) != (uint8_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_HASHUPDATECRLVALIDATE | 0x00000003UL));
        }
        else if (ctx->tbsLenLeft == 0u)
        {
            if (CryptoLib_Sha256Finish(&ctx->hashCtx, ctx->hashDigest) != (uint8_t)CRYPTO_ERR_SUCCESS)
            {
                CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_HASHUPDATECRLVALIDATE | 0x00000004UL));
            }
        }
        else
        {
            /* nothing to do here */
        }
    }

    if ((*slcMem == (uint8_t)SLICE_FINISH) && (ctx->crlLenDone != ctx->crlLen) && (ctx->tbsLenLeft != 0u))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_HASHUPDATECRLVALIDATE | 0x00000005UL));
    }

    return retVal;
}

STATICFCN uint8_t CryptoLib_SignatureUpdateCrlValidate(const uint8_t* crlBuffer,
                                                       const uint16_t crlLength,
                                                       CheckCRLMemorySlicedContext_t* ctx,
                                                       const uint8_t* slcMem)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;
    const uint16_t signatureIdx = (uint16_t)((uint16_t)RSASSA_PSS_SIGNATURE_LEN - ctx->signatureLenLeft);
    uint16_t crlBufferIdx = 0;
    uint32_t lenToCopy = 0;

    if ((*slcMem != (uint8_t)SLICE_CONTINUE) && (*slcMem != (uint8_t)SLICE_FINISH))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_SIGNATUREUPDATECRLVALIDATE | 0x00000001UL));
    }
    /* crlBuffer doesn't contain any signature bytes */
    else if ((crlLength + ctx->crlLenDone) <= ctx->signatureStartLen)
    {
        ctx->crlLenDone += crlLength;
        retVal = (uint8_t)CRYPTO_ERR_CALLAGAIN;
    }
    /* crlBuffer doesn't contain all remaining signature bytes */
    else if (((crlLength + ctx->crlLenDone) > ctx->signatureStartLen) && ((crlLength + ctx->crlLenDone) < ctx->crlLen))
    {
        /* no signature bytes have been copied yet */
        if (ctx->signatureStartLen >= ctx->crlLenDone)
        {
            crlBufferIdx = (uint16_t)(ctx->signatureStartLen - ctx->crlLenDone);
            lenToCopy = (uint32_t)(crlLength + ctx->crlLenDone) - ctx->signatureStartLen;
            ctx->signatureLenLeft -= ((crlLength + ctx->crlLenDone) - ctx->signatureStartLen);
        }
        /* some signature bytes have already been copied */
        else
        {
            crlBufferIdx = 0;
            lenToCopy = crlLength;
            ctx->signatureLenLeft -= crlLength;
        }
        ctx->crlLenDone += crlLength;
        retVal = (uint8_t)CRYPTO_ERR_CALLAGAIN;
    }
    /* crlBuffer contains all remaining signature bytes */
    else
    {
        /* no signature bytes have been copied yet */
        if (ctx->signatureStartLen >= ctx->crlLenDone)
        {
            crlBufferIdx = (uint16_t)(ctx->signatureStartLen - ctx->crlLenDone);
        }
        /* some signature bytes have already been copied */
        else
        {
            crlBufferIdx = 0;
        }
        lenToCopy = ctx->signatureLenLeft;
        ctx->crlLenDone = ctx->crlLen;
        ctx->signatureLenLeft = 0;
        ctx->stateMemSlice = VERIFY_RSA;
    }

    if (crlBuffer == NULL)
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_SIGNATUREUPDATECRLVALIDATE | 0x00000002UL));
    }
    else if (lenToCopy != 0u)
    {
        (void)memcpy(&ctx->signature[signatureIdx], &crlBuffer[crlBufferIdx], (size_t)lenToCopy);
    }
    else
    {
        /* nothing to do here */
    }


    if ((*slcMem == (uint8_t)SLICE_FINISH) && (ctx->crlLenDone != ctx->crlLen))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_SIGNATUREUPDATECRLVALIDATE | 0x00000003UL));
    }

    return retVal;
}

STATICFCN uint8_t CryptoLib_VerifyRsaCrlValidate(const CryptoLib_Crt_t* const crlSigningCA,
                                                 const CheckCRLMemorySlicedContext_t* ctx,
                                                 const uint8_t slicingTime)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;

    if (crlSigningCA == NULL)
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_VERIFYRSACRLVALIDATE | 0x00000001UL));
    }
    else
    {
        /* polyspace +5 MISRA-C3:10.5 [No action planned:low] "Invalid values for enum CryptoLib_Slicing_t are handled within the callee." */
        retVal = CryptoLib_VerifyRsa(ctx->hashDigest, SHA256_DIGEST_LEN,
                                     ctx->signature, RSASSA_PSS_SIGNATURE_LEN,
                                     crlSigningCA->subjectPublicKey.rsaModulus,
                                     crlSigningCA->subjectPublicKey.rsaExponent,
                                     (CryptoLib_Slicing_t)slicingTime);
    }

    return retVal;
}

uint8_t CryptoLib_CheckCRLValidity(CRYPTOLIB_HUGE const uint8_t* const crlBuffer,
                                   const uint16_t crlLength,
                                   CRYPTOLIB_HUGE const CryptoLib_Crt_t* const crlSigningCA,
                                   const uint8_t slicingMem, const uint8_t slicingTime)
{
    uint8_t retVal = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint8_t slcMem = slicingMem;   /* write access to slicingMem needed */

    CRYPTOLIB_MEMORY_SECTION_BEGIN
    static CheckCRLMemorySlicedContext_t ctx;
    CRYPTOLIB_MEMORY_SECTION_END

    if ((slcMem == (uint8_t)SLICE_RESET) || (slicingTime == (uint8_t)SLICE_RESET))
    {
        CryptoLib_ResetCrlValidate(&ctx);
    }
    else if ((slcMem == (uint8_t)SLICE_INIT) && (crlLength <= 12u))
    {
        CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_BAD_INPUT, (uint32_t)(FID_CRYPTOLIB_CHECKCRLVALIDITY | 0x00000001UL));
    }
    else
    {
        /* polyspace-begin MISRA-C3:16.3 MISRA-C3:16.1 [No action planned:Low] "Unconditional break respectively fallthrough is intended" */
        /* polyspace-begin MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        switch (ctx.stateMemSlice)
        {
            case INIT:
                if ((retVal = CryptoLib_InitCrlValidate(crlBuffer, crlLength,
                                                        &ctx, &slcMem)) != (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    break;
                }
            /* fallthrough */

            case HASH_UPDATE:
                if ((retVal = CryptoLib_HashUpdateCrlValidate(crlBuffer, crlLength,
                                                              &ctx, &slcMem)) != (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    break;
                }
            /* fallthrough */

            case SIGNATURE_UPDATE:
                if ((retVal = CryptoLib_SignatureUpdateCrlValidate(crlBuffer, crlLength,
                                                                   &ctx, &slcMem)) != (uint8_t)CRYPTO_ERR_SUCCESS)
                {
                    break;
                }
            /* fallthrough */

            case VERIFY_RSA:
                retVal = CryptoLib_VerifyRsaCrlValidate(crlSigningCA, &ctx, slicingTime);
                break;

            default: /* LCOV_EXCL_START: internal enum out of bounds */
                CryptoLib_SetError(&retVal, (int32_t)CRYPTO_ERR_ENUM_OUT_OF_BOUNDS, (uint32_t)(FID_CRYPTOLIB_CHECKCRLVALIDITY | 0x00000002UL));
                break;
                /* LCOV_EXCL_STOP */
        }
        /* polyspace-end */
        /* polyspace-end */
    }

    if (retVal != (uint8_t)CRYPTO_ERR_CALLAGAIN)
    {
        CryptoLib_ResetCrlValidate(&ctx);
    }

    return retVal;
}

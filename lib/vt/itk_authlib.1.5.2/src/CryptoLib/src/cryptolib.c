/**
 * @file  src/cryptolib.c
 * @brief This file contains the initializer function for CryptoLib
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im S<PERSON>yerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON><PERSON>heim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib.h"

#if (STATIC_MEM_ALLOC == ENABLED)
/** Static allocation buffer for mbedtls_calloc / mbedtls_free */

#include "mbedtls/memory_buffer_alloc.h"

CRYPTOLIB_MEMORY_SECTION_BEGIN
static uint8_t CRYPTOLIB_HUGE CryptoLib_AllocBuffer[STATIC_MEM_ALLOC_SIZE];
CRYPTOLIB_MEMORY_SECTION_END
#endif

uint8_t CryptoLib_Init(void)
{
    uint8_t errorCodeZero = 0;
    CryptoLib_SetError(&errorCodeZero, 0, (uint32_t)0);

#if (STATIC_MEM_ALLOC == ENABLED)
    mbedtls_memory_buffer_alloc_init(CryptoLib_AllocBuffer,
                                     sizeof(CryptoLib_AllocBuffer));
#endif

    return (uint8_t)CRYPTO_ERR_SUCCESS;
}

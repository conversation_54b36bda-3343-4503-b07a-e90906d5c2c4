/**
 * @file  src/cryptolib_mac.c
 * @brief MAC function definitions
 *
 * @n ------------------------------------------------------------------------
 * @n ITK-Engineering GmbH
 * @n
 * @n Im Speyerer Tal 6
 * @n 76761 <PERSON><PERSON><PERSON>zheim
 * @n
 * @n Email: <EMAIL>
 * @n ------------------------------------------------------------------------
 *
 */

#include "cryptolib/cryptolib_mac.h"
#include "cryptolib/cryptolib_fid.h"
#include "cryptolib/cryptolib_diag.h"

#include "mbedtls/cmac.h"

#include <string.h>

#ifdef CRYPTOLIB_REDEF_STDLIB_FILE
#include CRYPTOLIB_REDEF_STDLIB_FILE
#else
#ifdef CRYPTOLIB_REDEF_STDLIB
#include "cryptolib/cryptolib_redef_stdlib.h"
#endif
#endif

#if (CRYPTOLIB_ALT_CMAC == DISABLED)
#define AES128_KEY_BITLEN 128    /**< Length of AES128 key in bits */

uint8_t CryptoLib_GenerateCmac(CRYPTOLIB_HUGE const uint8_t* input,
                               const uint16_t inputLen,
                               const uint8_t key[AES128_KEY_LEN],
                               CRYPTOLIB_HUGE uint8_t buf[AES128_KEY_LEN])
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    int32_t mret;
    const mbedtls_cipher_info_t* info = mbedtls_cipher_info_from_type(MBEDTLS_CIPHER_AES_128_ECB);

    if ((input == NULL) || (key == NULL) || (buf == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_GENERATECMAC | 0x00000001UL));
    }
    else
    {
        /* polyspace +1 MISRA-C3:13.4 [No action planned:low] "Result of assignment is used in an understandable and safe way" */
        if ((mret = mbedtls_cipher_cmac(info, key, (size_t)AES128_KEY_BITLEN, input, (size_t)inputLen, buf)) != (int32_t)CRYPTO_ERR_SUCCESS)
        {
            CryptoLib_SetError(&ret, mret, (uint32_t)(FID_CRYPTOLIB_GENERATECMAC | 0x00000002UL));
        }
    }

    return ret;
}

uint8_t CryptoLib_VerifyCmac(CRYPTOLIB_HUGE const uint8_t* peerMac,
                             CRYPTOLIB_HUGE const uint8_t* ownMac,
                             const uint8_t macLen)
{
    uint8_t ret = (uint8_t)CRYPTO_ERR_SUCCESS;
    uint8_t i;

    if ((peerMac == NULL) || (ownMac == NULL))
    {
        CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_NULL_POINTER, (uint32_t)(FID_CRYPTOLIB_VERIFYCMAC | 0x00000001UL));
    }
    else
    {
        /* compare if peerMac and ownMac are equal in constant time */
        uint8_t matched = 0u;
        for (i = 0u; i < macLen; i++)
        {
            matched |= (peerMac[i] ^ ownMac[i]);
        }

        if (matched != 0u)
        {
            CryptoLib_SetError(&ret, (int32_t)CRYPTO_ERR_INVALID, (uint32_t)(FID_CRYPTOLIB_VERIFYCMAC | 0x00000002UL));
        }
    }

    return ret;
}

#endif /* #if (CRYPTOLIB_ALT_CMAC == DISABLED) */

# @n ------------------------------------------------------------------------
# @n ITK-Engineering GmbH
# @n 
# @n Im Speyerer Tal 6
# @n 76761 Rülzheim
# @n 
# @n Email: <EMAIL>
# @n ------------------------------------------------------------------------
#
#
.SUFFIXES: .c .obj .lib

ROOT_DIR 		= .

#-----------------------------------------------------------------------------
#	User specific compiler settings
#-----------------------------------------------------------------------------
COMPILER_PATH	= C:/c166/C166VX_v3_1r2/bin
AR				= $(COMPILER_PATH)/ar166.exe
CC				= $(COMPILER_PATH)/cc166.exe

CC_FLAGS		= --model=huge -O3 -Wc-AF --no-tasking-sfr -C"XC2288H-200F"

#-----------------------------------------------------------------------------
#	Input path and file settings
#-----------------------------------------------------------------------------
AUTHLIB_DIR		= $(ROOT_DIR)/src/AuthLib
CRYPTOLIB_DIR	= $(ROOT_DIR)/src/CryptoLib
MBEDTLS_DIR		= $(CRYPTOLIB_DIR)/libs/mbedtls

VPATH			= $(AUTHLIB_DIR)/src $(CRYPTOLIB_DIR)/src $(MBEDTLS_DIR)/library

#-----------------------------------------------------------------------------
#	Output path and file settings
#-----------------------------------------------------------------------------
OUT_DIR			= $(ROOT_DIR)/obj

CRYPTOLIB_OBJ	= 	cryptolib.obj \
					cryptolib_asn1.obj \
                    cryptolib_common.obj \
					cryptolib_crl_revocationcheck.obj \
					cryptolib_crl_validity.obj \
					cryptolib_crt.obj \
					cryptolib_diag.obj \
					cryptolib_ecdh.obj \
					cryptolib_ecdh_common.obj \
					cryptolib_ecdh_compshared.obj \
					cryptolib_ecdh_doubleadd.obj \
					cryptolib_ecdh_gcd.obj \
					cryptolib_ecdh_modinv.obj \
					cryptolib_ecdh_mul.obj \
					cryptolib_ecdh_normalize.obj \
					cryptolib_hash.obj \
					cryptolib_mac.obj \
					cryptolib_rng.obj \
					cryptolib_rsa.obj \
					cryptolib_rsa_div.obj \
					cryptolib_rsa_expmod.obj \
					cryptolib_rsa_expmod_subs.obj \
					cryptolib_rsa_expmod_utils.obj \
					cryptolib_rsa_mod.obj \
					cryptolib_rsa_montmul.obj \
					cryptolib_rsa_rsassa_pss_verify.obj \
					cryptolib_utils.obj \
					cryptolib_x509.obj
					
AUTHLIB_OBJ		= 	authlib.obj \
					authlib_auth.obj \
					authlib_crt.obj \
					authlib_crt_chain.obj \
					authlib_diag.obj \
					authlib_mac.obj \
					authlib_keys.obj \
					authlib_rng.obj 
					
MBEDTLS_OBJ		= 	aes.obj \
					asn1parse.obj \
					bignum.obj \
					cipher.obj \
					cipher_wrap.obj \
					cmac.obj \
					constant_time.obj \
					ctr_drbg.obj \
					ecdh.obj \
					ecp.obj \
					ecp_curves.obj \
					entropy.obj \
					entropy_poll.obj \
					md.obj \
					memory_buffer_alloc.obj \
					oid.obj \
					pk.obj \
					pk_wrap.obj \
					pkparse.obj \
					platform.obj \
					platform_util.obj \
					rsa.obj \
					sha256.obj \
					x509.obj
					
ALL_OBJ			=	$(addprefix $(OUT_DIR)/,$(CRYPTOLIB_OBJ) $(AUTHLIB_OBJ) $(MBEDTLS_OBJ))

LIBFILE 		= 	$(OUT_DIR)/libauthlib.lib
					
#-----------------------------------------------------------------------------
#	Rules and targets
#-----------------------------------------------------------------------------
all: $(LIBFILE)
	@echo $(LIBFILE) done
		
$(LIBFILE): $(ALL_OBJ)
	@echo - Archiving: $(LIBFILE)
	@$(AR) -rv $(LIBFILE) $^
	
$(OUT_DIR)/%.obj : %.c
	@mkdir -p $(@D)
	@echo - Compiling: $<
	@$(CC) $(CC_FLAGS) -c $< -o $(OUT_DIR)/$*.obj -I$(CRYPTOLIB_DIR)/include -I$(MBEDTLS_DIR)/include -I$(AUTHLIB_DIR)/include -DMBEDTLS_CONFIG_FILE="<cryptolib/cryptolib_mbedtls_config.h>"

clean:
	@-rm -f $(ALL_OBJ)
	@-rm -f $(LIBFILE)
	

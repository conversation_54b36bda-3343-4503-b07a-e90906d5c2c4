/* return value */
uint8_t ret;
/* seed for initializing the PRNG - DO NOT USE A HARDCODED VALUE */
uint8_t* seed = "abcdefghijklmnopqrstuvwxyz";
/* initialize the AuthLib, behaviour of the library can be changed by the defines in cryptolib_conf.h. */
AuthLib_Init();
CryptoLib_Crt_t root, testlab, manu, manuSeries, device, crlSubCA;
/* initialize and parse all certificates */
AuthLib_InitCertificate(&root);
AuthLib_InitCertificate(&testlab);
AuthLib_InitCertificate(&manu);
AuthLib_InitCertificate(&manuSeries);
AuthLib_InitCertificate(&device);
AuthLib_InitCertificate(&crlSubCA); 
AuthLib_ParseCertificate(rootBuf, rootBufLen, &root);
AuthLib_ParseCertificate(testlabBuf, testlabBufLen, &testlab);
AuthLib_ParseCertificate(manuBuf, manuBufLen, &manu);
AuthLib_ParseCertificate(manuSeriesBuf, manuSeriesBufLen, &manuSeries);
AuthLib_ParseCertificate(deviceBuf, deviceBufLen, &device);
AuthLib_ParseCertificate(crlCaBuf, crlSubCaBufLen, &crlCA);
/* check the signature of the CRL */
ret = AuthLib_CheckCRLValidity(crl, crlLen, &crlCA, SLICE_NO_SLICING, SLICE_NO_SLICING);
/* check return values */
if (ret == CRYPTO_ERR_SUCCESS)
{
    /* CRL's signature is valid */
}
else if (ret == CRYPTO_ERR_SIGNATURE_INVALID)
{
    /* CRL's signature is invalid */
}
else 
{
    /* an error occured */
}
AuthLib_FreeCertificate(&crlCA);
/* check that no certificate is revoked */
AuthLib_CheckCertificateOnCRL(testlab.serial, testlab.serialLen, crl, crlLen, SLICE_NO_SLICING);
AuthLib_CheckCertificateOnCRL(manu.serial, manu.serialLen, crl, crlLen, SLICE_NO_SLICING);
AuthLib_CheckCertificateOnCRL(manuSeries.serial, manuSeries.serialLen, crl, crlLen, SLICE_NO_SLICING);
AuthLib_CheckCertificateOnCRL(device.serial, device.serialLen, crl, crlLen, SLICE_NO_SLICING);
/* memory sliced check of revocation status of a certificate, crlLen is 589 e.g. */
ret = AuthLib_CheckCertificateOnCRL(testlab.serial, testlab.serialLen, crl, 100, SLICE_INIT);
for(size_t i = 100; ((i < crlLen) && (ret == CRYPTO_ERR_CALLAGAIN)); i += 100)
{
    ret = AuthLib_CheckCertificateOnCRL(testlab.serial, testlab.serialLen, &crl[i], 100, SLICE_CONTINUE);
}
if (ret == CRYPTO_ERR_CALLAGAIN)
{
    ret = AuthLib_CheckCertificateOnCRL(testlab.serial, testlab.serialLen, &crl[500], 89, SLICE_FINISH);
}
if (ret == CRYPTO_ERR_SUCCESS)
{
    /* crt not revoked * */
}
else if (ret == CRYPTO_ERR_INVALID)
{
    /* crt revoked */
}
else
{
    /* error occured */
}
/* validate the certificate chain */
AuthLib_VerifyPeerCertificatesChain(&root, &testlab, &manu, &manuSeries, &device, SLICE_NO_SLICING);
/* check return values */
if (ret == CRYPTO_ERR_SUCCESS)
{
    /* cert chain is valid */
}
else if (ret == CRYPTO_ERR_XXX_INVALID)
{
    /* cert XXX is invalid */
}
else 
{
    /* an error occured */
}
/* free all certificate that aren't needed anymore */
AuthLib_FreeCertificate(&root);
AuthLib_FreeCertificate(&testlab);
AuthLib_FreeCertificate(&manu);
/* check the content of the peer certificate */
AuthLib_CheckPeerName(manuSeries.subject.organization, peerName);
AuthLib_CheckPeerCCID(&manuSeries, manufacturerCode, testYear, testRevision, testlabID, testReferenceNumber);
/* generate a random value used as challenge */
uint8_t challengeBuf[32];
AuthLib_GenerateRandomChallenge(seed, sizeof(seed), challengeBuf);
uint8* challengeServer = challengeBuf;
char* privateKey = "710ED9450F75B26E77EFAE7E0A4E05E52B210FEA5DC9F8ADAE1AB6F08800AD28";
uint8 commonSecretBuf[32];
uint8 signedChallenge[16];
/* compute the LwA-key */
AuthLib_ComputeCommonSecret(privateKey, device.subjectPublicKey.ecPublicKey, challengeServer, challengeBuf, commonSecretBuf, CURVE_25519_KDF_NIST_SP800_56A_HMAC_SHA256, SLICE_NO_SLICING);
/* using the LwA-key sign the challenge received by the server */
AuthLib_ComputeCMAC(challengeServer, commonSecretBuf, signedChallenge, CRYPTO_CLIENT);
/* verify the challenge received from the server */
bool authenticated = (AuthLib_VerifyCMAC(signedChallenge, commonSecretBuf, challengeBuf, CRYPTO_CLIENT) == CRYPTO_ERR_SUCCESS);
/* free the remaining certificates */
AuthLib_FreeCertificate(&device);
AuthLib_FreeCertificate(&manuSeries);


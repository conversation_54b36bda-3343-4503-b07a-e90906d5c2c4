name: C/C++ CI

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: install deps
      run: sudo apt update -y && sudo apt install -y libopencv-dev && sudo apt install -y libyaml-dev
    - name: make
      run: make -C src
    - name: make deb
      run: make -C src deb
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2
    - name: publish
      run: aws s3 cp src/*.deb s3://maka-build-artifacts/libvt/
  

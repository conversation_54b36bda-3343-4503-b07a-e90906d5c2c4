from contextlib import asynccontextmanager
from dataclasses import dataclass
from typing import Any, AsyncIterator, Optional

import grpc


@dataclass
class GrpcConf:
    hostname: str
    port: int = 443


class CredentialsInterceptor(grpc.aio.UnaryUnaryClientInterceptor):
    def __init__(self, token: str) -> None:
        super().__init__()
        self._bearer = f"Bearer {token}"

    async def intercept_unary_unary(
        self, continuation: Any, client_call_details: grpc.ClientCallDetails, request: Any
    ) -> Any:
        metadata = client_call_details.metadata if client_call_details.metadata else grpc.aio.Metadata()
        metadata.add("authorization", self._bearer)
        updated_call_details = grpc.aio.ClientCallDetails(
            client_call_details.method,
            client_call_details.timeout,
            metadata,
            client_call_details.credentials,
            client_call_details.wait_for_ready,
        )
        response = await continuation(updated_call_details, request)
        return response


def get_raw_channel(conf: GrpcConf, token: str, use_ssl: Optional[bool] = None) -> grpc.aio.Channel:
    if use_ssl is None:
        use_ssl = conf.port == 443
    assert use_ssl is not None
    url = f"{conf.hostname}:{conf.port}"
    if use_ssl:
        credentials = grpc.composite_channel_credentials(
            grpc.ssl_channel_credentials(), grpc.access_token_call_credentials(token),
        )
        chan: grpc.aio.Channel = grpc.aio.secure_channel(url, credentials)
    else:
        chan = grpc.aio.insecure_channel(url, interceptors=[CredentialsInterceptor(token)])
    return chan


@asynccontextmanager
async def get_channel(conf: GrpcConf, token: str, use_ssl: Optional[bool] = None) -> AsyncIterator[grpc.aio.Channel]:
    chan = get_raw_channel(conf, token, use_ssl)
    async with chan as c:
        yield c

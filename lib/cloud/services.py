from os import environ
from typing import <PERSON><PERSON>


def env_to_var(name: str, default_port: int) -> Tuple[str, int]:
    host_split = environ.get(name, "").split(":", 1)
    if len(host_split) > 1:
        default_port = int(host_split[1])
    return (host_split[0], default_port)


PORTAL_GRPC_HOST, PORTAL_GRPC_PORT = env_to_var("CARBON_PORTAL_HOST", 443)
RTC_LOCATOR_GRPC_HOST, RTC_LOCATOR_GRPC_PORT = env_to_var("CARBON_RTC_LOCATOR_GRPC_HOST", 443)
RTC_JOBS_GRPC_HOST, RTC_JOBS_GRPC_PORT = env_to_var("CARBON_RTC_JOBS_GRPC_HOST", 443)

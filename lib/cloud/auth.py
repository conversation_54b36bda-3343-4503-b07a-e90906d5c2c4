import asyncio
import time
from dataclasses import dataclass, field
from http import H<PERSON><PERSON>tatus
from os import environ

import aiohttp
from dateutil.parser import isoparse

from lib.common.logging import get_logger

LOG = get_logger(__name__)


@dataclass
class AuthConf:
    domain: str = field(default_factory=lambda: environ.get("AUTH_DOMAIN", ""))
    client_id: str = field(default_factory=lambda: environ.get("AUTH_CLIENT_ID", ""))
    client_secret: str = field(default_factory=lambda: environ.get("AUTH_CLIENT_SECRET", ""))
    use_secure_socket: bool = False


class TokenStoreException(Exception):
    pass


class TokenStore:
    def __init__(self, conf: AuthConf) -> None:
        self._conf = conf
        self._token_lock = asyncio.Lock()
        self._token_exp = -1
        self._token = ""
        if self._conf.use_secure_socket:
            url_scheme = "https"
        else:
            url_scheme = "http"
        self._token_url = f"{url_scheme}://{self._conf.domain}/oauth/token"

    async def __fetch(self) -> str:
        if self._token_exp > time.time():
            return self._token
        try:
            async with aiohttp.ClientSession() as session:
                response = await session.post(
                    self._token_url,
                    data={"grant_type": "client_credentials"},
                    auth=aiohttp.BasicAuth(self._conf.client_id, self._conf.client_secret),
                )
            if response.status == HTTPStatus.OK:
                data = await response.json()
                if "access_token" not in data or "expiry" not in data:
                    raise TokenStoreException(f"Invalid json response from token server {self._conf.domain}")
                self._token_exp = int(isoparse(data["expiry"]).timestamp())
                self._token = data["access_token"]
                return self._token
            else:
                raise TokenStoreException(f"Invalid response {response.status} from token server {self._conf.domain}")
        except Exception as ex:
            raise TokenStoreException(ex)

    async def fetch(self, retry: bool = True) -> str:
        async with self._token_lock:
            if not retry:
                return await self.__fetch()
            while True:
                try:
                    token = await self.__fetch()
                    return token
                except Exception as ex:
                    LOG.error(f"Failed to fetch token: {ex}")

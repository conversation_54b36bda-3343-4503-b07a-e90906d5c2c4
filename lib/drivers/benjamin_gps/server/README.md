# Benjamin GPS

## LEDs

1. Software running (blinking)
2. Time valid (solid)
3. PPS signal (blinking)

### Rover

4. Position valid (solid)
5. Heading valid (solid)
6. Processing UDP packets (blinking)

### Base

4. Survey-in complete (solid)

## Updating u-Blox F9P GPS firmware

1. Download [u-center](https://www.u-blox.com/en/product/u-center)
2. Download [ZED-F9P HPG 1.13 firmware](https://www.u-blox.com/sites/default/files/UBX_F9_100_HPG_113_ZED_F9P.7e6e899c5597acddf2f5f2f70fdf5fbe.bin)
3. Program firmware using u-center to each GPS unit.

## Updating RFD 900x radio firmware

1. Download [modem tools](https://files.rfdesign.com.au/Files/tools/RFDTools-V2.49.zip)
2. Download [asynchronous firmware V2.69](https://files.rfdesign.com.au/Files/firmware/RFD900x-AsyncRelease_V2.69.bin)
3. Program firmware using RFDTools to each radio.

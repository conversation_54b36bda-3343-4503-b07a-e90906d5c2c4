#include <condition_variable>
#include <pthread.h>

#include <boost/algorithm/string.hpp>
#include <boost/date_time/posix_time/posix_time.hpp>
#include <fmt/format.h>
#include <spdlog/spdlog.h>

#include "gps_udp_multicast_comm.h"
#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {
namespace udp_multicast {

constexpr size_t kBufferSize = 65536;

GpsUdpMulticastCommunicator::GpsUdpMulticastCommunicator(std::string address, unsigned short port)
    : address_(boost::asio::ip::address::from_string(address)), port_(port), endpoint_(address_, port_),
      recv_socket_(io_service_), send_socket_(io_service_, endpoint_.protocol()) {
  try {
    boost::asio::ip::udp::endpoint listen_endpoint(boost::asio::ip::udp::v4(), port_);
    recv_socket_.open(listen_endpoint.protocol());
    recv_socket_.set_option(boost::asio::ip::udp::socket::reuse_address(true));
    recv_socket_.bind(listen_endpoint);
  } catch (boost::system::system_error &ex) {
    throw maka_error(fmt::format("Failed to create receive UDP socket on port {}: {}", port_, ex.what()));
  }

  try {
    recv_socket_.set_option(boost::asio::ip::multicast::join_group(address_));
  } catch (boost::system::system_error &ex) {
    throw maka_error(fmt::format("Failed to join multicast group {}: {}", address_.to_string(), ex.what()));
  }

  try {
    send_socket_.set_option(boost::asio::ip::multicast::enable_loopback(false));
  } catch (boost::system::system_error &ex) {
    throw maka_error(fmt::format("Failed to disable loopback on send UDP socket: {}", ex.what()));
  }

  try {
    send_socket_.set_option(boost::asio::ip::multicast::hops(255));
  } catch (boost::system::system_error &ex) {
    throw maka_error(fmt::format("Failed to set multicast TTL to 255 on send UDP socket: {}", ex.what()));
  }

  thread_ = std::thread([&] { run_forever(); });
  pthread_setname_np(thread_.native_handle(), "[gps_mcast_com]");
}

GpsUdpMulticastCommunicator::~GpsUdpMulticastCommunicator() {
  terminate_ = true;
  read_cv_.notify_all();
  io_service_.post([&] {
    boost::system::error_code ignore_error;
    recv_socket_.close(ignore_error);
  });
  if (thread_.joinable()) {
    thread_.join();
  }
}

char GpsUdpMulticastCommunicator::read_one() {
  std::unique_lock<std::mutex> lock(mutex_);
  read_cv_.wait(lock, [&] { return !read_q_.empty() || terminate_; });
  if (terminate_) {
    throw maka_error("Terminating.");
  }
  auto retval = read_q_.front();
  read_q_.pop();
  return retval;
}

std::string GpsUdpMulticastCommunicator::read_n(int count) {
  std::unique_lock<std::mutex> lock(mutex_);
  std::string retval;
  while (count) {
    read_cv_.wait(lock, [&] { return !read_q_.empty() || terminate_; });
    if (terminate_) {
      throw maka_error("Terminating.");
    }
    while (!read_q_.empty() && count) {
      auto chr = read_q_.front();
      read_q_.pop();
      retval += chr;
      count--;
    }
  }
  return retval;
}

std::string GpsUdpMulticastCommunicator::read_until(char stop_chr) {
  std::unique_lock<std::mutex> lock(mutex_);
  std::string retval;
  for (;;) {
    read_cv_.wait(lock, [&] { return !read_q_.empty() || terminate_; });
    if (terminate_) {
      throw maka_error("Terminating.");
    }
    while (!read_q_.empty()) {
      auto chr = read_q_.front();
      read_q_.pop();
      if (chr == stop_chr) {
        return retval;
      }
      retval += chr;
    }
  }
}

void GpsUdpMulticastCommunicator::write(const std::string &data) {
  io_service_.post([&, data] {
    bool write_in_progress = !write_q_.empty();
    write_q_.push(data);
    if (!write_in_progress) {
      async_write();
    }
  });
}

void GpsUdpMulticastCommunicator::async_write() {
  send_socket_.async_send_to(boost::asio::buffer(write_q_.front()), endpoint_,
                             [&](const boost::system::error_code &error, size_t) {
                               if (error) {
                                 if (terminate_) {
                                   return;
                                 }
                                 throw maka_error(fmt::format("Failed to send UDP mulicast to {}:{}: {}",
                                                              address_.to_string(), port_, error.message()));
                               }
                               write_q_.pop();
                               if (!write_q_.empty()) {
                                 async_write();
                               }
                             });
}

template <std::size_t SIZE>
void GpsUdpMulticastCommunicator::async_read(std::array<char, SIZE> &buffer) {
  recv_socket_.async_receive_from(boost::asio::buffer(buffer), sender_endpoint_,
                                  [&](const boost::system::error_code &error, size_t sz) {
                                    if (error) {
                                      if (terminate_) {
                                        return;
                                      }
                                      throw maka_error(fmt::format("Failed to receive UDP multicast from {}:{}: {}",
                                                                   address_.to_string(), port_, error.message()));
                                    }
                                    {
                                      std::unique_lock<std::mutex> lock(mutex_);
                                      for (size_t i = 0; i < sz; i++) {
                                        read_q_.push(buffer[i]);
                                      }
                                    }
                                    read_cv_.notify_all();
                                    async_read(buffer);
                                  });
}

void GpsUdpMulticastCommunicator::run_forever() {
  // Enqueue reads.
  std::array<char, kBufferSize> buffer;
  io_service_.post([&] { async_read(buffer); });

  // Run service until it's canceled.
  io_service_.reset();
  io_service_.run();
}

} // namespace udp_multicast
} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

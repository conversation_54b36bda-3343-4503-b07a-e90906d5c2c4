#pragma once

#include <array>
#include <atomic>
#include <condition_variable>
#include <mutex>
#include <queue>
#include <string>
#include <thread>

#include <boost/asio.hpp>

#include "lib/common/cpp/utils.h"
#include "lib/drivers/benjamin_gps/server/cpp/gps/gps_comm.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {
namespace udp_multicast {

class GpsUdpMulticastCommunicator : public GpsCommunicator {
public:
  GpsUdpMulticastCommunicator(std::string address, unsigned short port);
  ~GpsUdpMulticastCommunicator();
  DELETE_COPY_AND_MOVE(GpsUdpMulticastCommunicator)

  void read_initial_ffs() override{};
  char read_one() override;
  std::string read_n(int count) override;
  std::string read_until(char chr) override;
  void write(const std::string &data) override;

private:
  boost::asio::io_service io_service_;
  boost::asio::ip::address address_;
  unsigned short port_;
  boost::asio::ip::udp::endpoint endpoint_;
  boost::asio::ip::udp::socket recv_socket_;
  boost::asio::ip::udp::socket send_socket_;
  boost::asio::ip::udp::endpoint sender_endpoint_;
  std::thread thread_;
  std::atomic<bool> terminate_{false};
  std::mutex mutex_;
  std::queue<uint8_t> read_q_;
  std::condition_variable read_cv_;
  std::queue<std::string> write_q_;
  void async_write();
  template <std::size_t SIZE>
  void async_read(std::array<char, SIZE> &buffer);
  void run_forever();
};

} // namespace udp_multicast
} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
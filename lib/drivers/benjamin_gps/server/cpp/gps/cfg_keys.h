#pragma once

#include <cstdint>

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {

// Consult the following documents for message IDs and their meaning:
// * https://www.u-blox.com/en/docs/UBX-18010802
// * https://www.u-blox.com/en/docs/UBX-18010854
// * https://www.u-blox.com/en/docs/UBX-19009093

constexpr uint32_t k_CFG_SPIOUTPROT_NMEA = 0x107a0002;
constexpr uint32_t k_CFG_TMODE_MODE = 0x20030001;
const std::string k_CFG_TMODE_MODE_SURVEY_IN = {0x01};
constexpr uint32_t k_CFG_NAVSPG_CONSTR_DGNSSTO = 0x201100c4;
constexpr uint32_t k_CFG_MSGOUT_UBX_NAV_PVT_SPI = 0x2091000a;
constexpr uint32_t k_CFG_MSGOUT_UBX_NAV_SVIN_SPI = 0x2091008c;
constexpr uint32_t k_CFG_MSGOUT_UBX_NAV_RELPOSNED_SPI = 0x20910091;
constexpr uint32_t k_CFG_MSGOUT_RTCM_3X_TYPE1005_SPI = 0x209102c1;
constexpr uint32_t k_CFG_MSGOUT_RTCM_3X_TYPE4072_0_SPI = 0x20910302;
constexpr uint32_t k_CFG_MSGOUT_RTCM_3X_TYPE1230_SPI = 0x20910307;
constexpr uint32_t k_CFG_MSGOUT_RTCM_3X_TYPE1074_SPI = 0x20910362;
constexpr uint32_t k_CFG_MSGOUT_RTCM_3X_TYPE1084_SPI = 0x20910367;
constexpr uint32_t k_CFG_MSGOUT_RTCM_3X_TYPE1094_SPI = 0x2091036c;
constexpr uint32_t k_CFG_MSGOUT_RTCM_3X_TYPE1124_SPI = 0x20910371;
constexpr uint32_t k_CFG_NAVSPG_FIXMODE = 0x20110011;
const std::string k_CFG_NAVSPG_FIXMODE_PED = {0x03};
constexpr uint32_t k_CFG_NAVSPG_INFIL_MINSVS = 0x201100a1;
constexpr uint32_t k_CFG_NAVSPG_INFIL_MINCNO = 0x201100a3;
constexpr uint32_t k_CFG_NAVSPG_INFIL_MINELEV = 0x201100a4;
constexpr uint32_t k_CFG_RATE_MEAS = 0x30210001;
constexpr uint32_t k_CFG_TMODE_SVIN_MIN_DUR = 0x40030010;
constexpr uint32_t k_CFG_TMODE_SVIN_ACC_LIMIT = 0x40030011;
constexpr uint32_t k_CFG_TP_LEN_TP1 = 0x40050004;

} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
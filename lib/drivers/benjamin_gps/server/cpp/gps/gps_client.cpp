#include <boost/crc.hpp>
#include <fmt/format.h>
#include <spdlog/spdlog.h>

#include "gps_client.h"
#include "lib/drivers/benjamin_gps/server/cpp/utils/endian_string.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {

namespace {

typedef boost::crc_optimal<24, 0x1864CFBu, 0x0, 0x0, false, false> rtcm_crc;

constexpr uint8_t kRtcmMagic = 0xd3;
constexpr size_t kMaxRtcmMessageSize = 1024;
constexpr uint8_t kUbxMagic1 = 0xb5;
constexpr uint8_t kUbxMagic2 = 0x62;
constexpr size_t kMaxUbxMessageSize = 1024;
constexpr char kNmeaMagic = '$';

uint32_t compute_rtcm_crc(const std::string &data) {
  rtcm_crc calc;
  calc.process_byte(kRtcmMagic);
  auto sz_str = to_be_str(data.size(), 2);
  calc.process_bytes(sz_str.c_str(), sz_str.size());
  calc.process_bytes(data.c_str(), data.size());
  return calc.checksum();
}

std::tuple<uint8_t, uint8_t> compute_ubx_crc(uint8_t cls, uint8_t msgid, const std::string &data) {
  uint8_t ck_a = 0;
  uint8_t ck_b = 0;
  for (uint8_t chr : {cls, msgid}) {
    ck_a += chr;
    ck_b += ck_a;
  }
  for (uint8_t chr : to_le_str(data.size(), 2)) {
    ck_a += chr;
    ck_b += ck_a;
  }
  for (uint8_t chr : data) {
    ck_a += chr;
    ck_b += ck_a;
  }
  return {ck_a, ck_b};
}

uint8_t compute_nmea_crc(const std::string &data) {
  uint8_t result = 0;
  for (auto chr : data) {
    result ^= chr;
  }
  return result;
}

} // namespace

void GpsClient::send_rtcm(const std::string &data) {
  std::string packet = "";
  packet += kRtcmMagic;
  packet += to_be_str(data.size(), 2);
  packet += data;
  auto cksum = compute_rtcm_crc(data);
  packet += to_be_str(cksum, 3);
  comm_->write(packet);
}

void GpsClient::send_ubx(uint8_t cls, uint8_t msgid, const std::string &data) {
  std::string packet = "";
  packet += kUbxMagic1;
  packet += kUbxMagic2;
  packet += cls;
  packet += msgid;
  packet += to_le_str(data.size(), 2);
  packet += data;
  auto [ck_a, ck_b] = compute_ubx_crc(cls, msgid, data);
  packet += ck_a;
  packet += ck_b;
  comm_->write(packet);
}

void GpsClient::send_nmea(const std::string &data) {
  comm_->write(fmt::format("{}{}*{:02X}\r\n", kNmeaMagic, data, compute_nmea_crc(data)));
}

void GpsClient::run_forever() {
  try {
    while (!terminate_) {
      comm_->read_initial_ffs();
      auto message_type = comm_->read_one();
      switch (message_type) {
      case kRtcmMagic:
        read_rtcm();
        unknown_magic_reported_ = false;
        break;
      case kUbxMagic1:
        read_ubx();
        unknown_magic_reported_ = false;
        break;
      case kNmeaMagic:
        read_nmea();
        unknown_magic_reported_ = false;
        break;
      default:
        if (!unknown_magic_reported_) {
          spdlog::warn("Unknown message type: {}", message_type);
          statistics_.unknown_magic_error++;
          unknown_magic_reported_ = true;
        }
      }
    }
  } catch (std::exception &ex) {
    if (!terminate_) {
      spdlog::error("GPS client failed: {}", ex.what());
      throw;
    }
  }
}

void GpsClient::read_rtcm() {
  size_t sz = from_be_str(comm_->read_n(2));
  if (sz > kMaxRtcmMessageSize) {
    spdlog::warn("Received RTCM message with size: {}", sz);
    statistics_.rtcm_error++;
    return;
  }
  auto data = comm_->read_n(sz);
  auto crc_computed = compute_rtcm_crc(data);
  auto crc_provided = from_be_str(comm_->read_n(3));
  if (crc_computed != crc_provided) {
    spdlog::warn("Corrupted RTCM message. CRC computed {:#08x} != CRC provided {:#08x}", crc_computed, crc_provided);
    return;
  }
  statistics_.rtcm_ok++;

  std::lock_guard<std::mutex> lock(handlers_mutex_);
  for (auto &handler : handlers_) {
    handler->rtcm(data);
  }
}

void GpsClient::read_ubx() {
  auto second_byte = comm_->read_one();
  if (second_byte != kUbxMagic2) {
    spdlog::warn("Wrong second UBX byte: {}", second_byte);
    statistics_.ubx_error++;
    return;
  }

  auto cls = comm_->read_one();
  auto msgid = comm_->read_one();
  size_t sz = from_le_str(comm_->read_n(2));
  if (sz > kMaxUbxMessageSize) {
    spdlog::warn("Received UBX message CLS {:#04x} MSGID {:#04x} with size: {}", cls, msgid, sz);
    statistics_.ubx_error++;
    return;
  }
  auto data = comm_->read_n(sz);
  auto [ck_a_computed, ck_b_computed] = compute_ubx_crc(cls, msgid, data);
  auto crc_provided = comm_->read_n(2);
  if (ck_a_computed != crc_provided[0] || ck_b_computed != crc_provided[1]) {
    spdlog::warn("Corrupted UBX message CLS {:#04x} MSGID {:#04x}. CRC computed {:#04x} {:#04x} != CRC provided "
                 "{:#04x} {:#04x}",
                 cls, msgid, ck_a_computed, ck_b_computed, crc_provided[0], crc_provided[1]);
    statistics_.ubx_error++;
    return;
  }
  statistics_.ubx_ok++;

  std::lock_guard<std::mutex> lock(handlers_mutex_);
  for (auto &handler : handlers_) {
    handler->ubx(cls, msgid, data);
  }
}

void GpsClient::read_nmea() {
  auto data = comm_->read_until('*');
  uint8_t crc_computed = compute_nmea_crc(data);
  auto crc_provided_str = comm_->read_until('\n');
  uint32_t crc_provided;
  try {
    crc_provided = std::stoul(crc_provided_str, nullptr, 16);
  } catch (std::exception &ex) {
    spdlog::warn("Cannot deserialize NMEA CRC: {}", crc_provided_str);
    statistics_.nmea_error++;
    return;
  }

  if (crc_computed != crc_provided) {
    spdlog::warn("Corrupted NMEA message. CRC computed {:#04x} != CRC provided {:#04x}", crc_computed, crc_provided);
    statistics_.nmea_error++;
    return;
  }
  statistics_.nmea_ok++;

  std::lock_guard<std::mutex> lock(handlers_mutex_);
  for (auto &handler : handlers_) {
    handler->nmea(data);
  }
}

} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

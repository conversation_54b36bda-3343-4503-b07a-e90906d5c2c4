#include <fcntl.h>
#include <linux/spi/spidev.h>
#include <linux/types.h>
#include <pthread.h>
#include <sys/ioctl.h>
#include <unistd.h>

#include <fmt/format.h>
#include <spdlog/spdlog.h>

#include "gps_spi_comm.h"
#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {
namespace spi {

constexpr size_t kMaxFFs = 50;
// u-blox F9P is limited to 150 kByte/s transmissions.
constexpr uint32_t kMaxSpeedHz = 150000 * 8;
// Transfer data in 7ms chunks.
constexpr size_t kTransferBytes = 1024;

GpsSpiCommunicator::GpsSpiCommunicator(std::string device) : device_(device) {
  fd_ = open(device.c_str(), O_RDWR);
  if (fd_ < 0) {
    throw maka_error(fmt::format("Failed to open device: {}", device));
  }
  if (ioctl(fd_, SPI_IOC_WR_MAX_SPEED_HZ, &kMaxSpeedHz) < 0) {
    throw maka_error(fmt::format("Failed to set speed of device {} to {}", device, kMaxSpeedHz));
  }
  thread_ = std::thread([&] { run_forever(); });
  pthread_setname_np(thread_.native_handle(), "[gps_spi_comm]");
}

GpsSpiCommunicator::~GpsSpiCommunicator() {
  terminate_ = true;
  read_cv_.notify_all();
  if (thread_.joinable()) {
    thread_.join();
  }
  if (fd_ >= 0) {
    close(fd_);
  }
}

void GpsSpiCommunicator::read_initial_ffs() {
  std::unique_lock<std::mutex> lock(mutex_);
  for (;;) {
    read_cv_.wait(lock, [&] { return !read_q_.empty() || terminate_; });
    if (terminate_) {
      throw maka_error("Terminating.");
    }
    while (!read_q_.empty()) {
      auto retval = read_q_.front();
      if (retval != 0xff) {
        return;
      }
      read_q_.pop();
    }
  }
}

char GpsSpiCommunicator::read_one() {
  std::unique_lock<std::mutex> lock(mutex_);
  read_cv_.wait(lock, [&] { return !read_q_.empty() || terminate_; });
  if (terminate_) {
    throw maka_error("Terminating.");
  }
  auto retval = read_q_.front();
  read_q_.pop();
  return retval;
}

std::string GpsSpiCommunicator::read_n(int count) {
  std::unique_lock<std::mutex> lock(mutex_);
  std::string retval;
  while (count) {
    read_cv_.wait(lock, [&] { return !read_q_.empty() || terminate_; });
    if (terminate_) {
      throw maka_error("Terminating.");
    }
    while (!read_q_.empty() && count) {
      auto chr = read_q_.front();
      read_q_.pop();
      retval += chr;
      count--;
    }
  }
  return retval;
}

std::string GpsSpiCommunicator::read_until(char stop_chr) {
  std::unique_lock<std::mutex> lock(mutex_);
  std::string retval;
  for (;;) {
    read_cv_.wait(lock, [&] { return !read_q_.empty() || terminate_; });
    if (terminate_) {
      throw maka_error("Terminating.");
    }
    while (!read_q_.empty()) {
      auto chr = read_q_.front();
      read_q_.pop();
      if (chr == stop_chr) {
        return retval;
      }
      retval += chr;
    }
  }
}

void GpsSpiCommunicator::write(const std::string &data) {
  std::unique_lock<std::mutex> lock(mutex_);
  // Insert commands into the queue as a one big chunk. The other side does the same.
  for (auto chr : data) {
    write_q_.push(chr);
  }
}

void GpsSpiCommunicator::run_forever() {
  try {
    size_t read_ffs = 0;
    while (!terminate_) {
      uint8_t read_bytes[kTransferBytes];
      uint8_t write_bytes[kTransferBytes]{0xff};
      {
        std::unique_lock<std::mutex> lock(mutex_);
        for (size_t i = 0; i < kTransferBytes && !write_q_.empty(); i++) {
          write_bytes[i] = write_q_.front();
          write_q_.pop();
        }
      }

      struct spi_ioc_transfer xfer {};
      xfer.tx_buf = (__u64)write_bytes;
      xfer.rx_buf = (__u64)read_bytes;
      xfer.len = kTransferBytes;
      if (ioctl(fd_, SPI_IOC_MESSAGE(1), &xfer) < 0) {
        throw maka_error("Failed to execute a SPI transaction.");
      }

      {
        std::unique_lock<std::mutex> lock(mutex_);
        for (size_t i = 0; i < kTransferBytes; i++) {
          // Don't put 0xff bytes to reader queue after 50 0xff bytes were encountered.
          if (read_bytes[i] == 0xff) {
            if (read_ffs >= kMaxFFs) {
              continue;
            } else {
              read_ffs++;
            }
          } else {
            read_ffs = 0;
          }
          read_q_.push(read_bytes[i]);
        }
      }
      read_cv_.notify_all();
    }
  } catch (std::exception &ex) {
    if (!terminate_) {
      spdlog::error("GPS SPI communicator {} failed: {}", device_, ex.what());
      throw;
    }
  }
}

} // namespace spi
} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

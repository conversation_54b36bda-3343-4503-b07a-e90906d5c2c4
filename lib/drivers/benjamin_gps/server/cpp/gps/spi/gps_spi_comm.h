#pragma once

#include <atomic>
#include <condition_variable>
#include <mutex>
#include <queue>
#include <string>
#include <thread>

#include "lib/common/cpp/utils.h"
#include "lib/drivers/benjamin_gps/server/cpp/gps/gps_comm.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {
namespace spi {

class GpsSpiCommunicator : public GpsCommunicator {
public:
  GpsSpiCommunicator(std::string device);
  ~GpsSpiCommunicator();
  DELETE_COPY_AND_MOVE(GpsSpiCommunicator)

  void read_initial_ffs() override;
  char read_one() override;
  std::string read_n(int count) override;
  std::string read_until(char chr) override;
  void write(const std::string &data) override;

private:
  int fd_ = 0;
  std::thread thread_;
  std::atomic<bool> terminate_{false};
  std::string device_;
  std::mutex mutex_;
  std::queue<uint8_t> read_q_;
  std::condition_variable read_cv_;
  std::queue<uint8_t> write_q_;
  void run_forever();
};

} // namespace spi
} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

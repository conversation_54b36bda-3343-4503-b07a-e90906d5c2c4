#pragma once

#include <array>
#include <atomic>
#include <condition_variable>
#include <mutex>
#include <queue>
#include <string>
#include <thread>

#include <boost/asio.hpp>
#include <boost/asio/serial_port.hpp>

#include "lib/common/cpp/utils.h"
#include "lib/drivers/benjamin_gps/server/cpp/gps/gps_comm.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {
namespace radio {

class GpsRadioCommunicator : public GpsCommunicator {
public:
  GpsRadioCommunicator(std::string device, int node_id, int dest_id);
  ~GpsRadioCommunicator();
  DELETE_COPY_AND_MOVE(GpsRadioCommunicator)

  void read_initial_ffs() override{};
  char read_one() override;
  std::string read_n(int count) override;
  std::string read_until(char chr) override;
  void write(const std::string &data) override;

private:
  boost::asio::io_service io_service_;
  std::string device_;
  int node_id_;
  int dest_id_;
  boost::asio::serial_port port_;
  std::thread thread_;
  std::atomic<bool> terminate_{false};
  std::mutex mutex_;
  std::queue<uint8_t> read_q_;
  std::condition_variable read_cv_;
  std::queue<std::string> write_q_;
  void setup_radio_settings();
  void enter_command_mode();
  std::string execute_command(const std::string &command);
  std::string read_until_timeout(const boost::asio::deadline_timer::duration_type &timeout);
  void async_write();
  template <std::size_t SIZE>
  void async_read(std::array<char, SIZE> &buffer);
  void run_forever();
};

} // namespace radio
} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
#include <condition_variable>
#include <optional>
#include <pthread.h>

#include <boost/algorithm/string.hpp>
#include <boost/date_time/posix_time/posix_time.hpp>
#include <fmt/format.h>
#include <spdlog/spdlog.h>

#include "gps_radio_comm.h"
#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {
namespace radio {

constexpr uint32_t kBaudRate = 115200;
constexpr size_t kBufferSize = 65536;
const uint32_t kNetworkId = 42;
const std::string kEncryptionKey = "3008E5930005E153FFF70AFF1038E28D";

GpsRadioCommunicator::GpsRadioCommunicator(std::string device, int node_id, int dest_id)
    : device_(device), node_id_(node_id), dest_id_(dest_id), port_(boost::asio::serial_port(io_service_)) {
  try {
    port_.open(device);
  } catch (boost::system::system_error &ex) {
    throw maka_error(fmt::format("Failed to open device {}: {}", device, ex.what()));
  }
  try {
    port_.set_option(boost::asio::serial_port_base::baud_rate(kBaudRate));
  } catch (boost::system::system_error &ex) {
    throw maka_error(fmt::format("Failed to set speed of device {} to {}: {}", device, kBaudRate, ex.what()));
  }
  try {
    port_.set_option(
        boost::asio::serial_port_base::flow_control(boost::asio::serial_port_base::flow_control::hardware));
  } catch (boost::system::system_error &ex) {
    throw maka_error(fmt::format("Failed to enable hardware flow control on device {}: {}", device, ex.what()));
  }
  setup_radio_settings();
  thread_ = std::thread([&] { run_forever(); });
  pthread_setname_np(thread_.native_handle(), "[gps_radio_com]");
}

GpsRadioCommunicator::~GpsRadioCommunicator() {
  terminate_ = true;
  read_cv_.notify_all();
  io_service_.post([&] {
    boost::system::error_code ignore_error;
    port_.close(ignore_error);
  });
  if (thread_.joinable()) {
    thread_.join();
  }
}

void GpsRadioCommunicator::setup_radio_settings() {
  try {
    // Enter command mode.
    enter_command_mode();

    // Check version.
    std::string version_info = execute_command("ATI");
    spdlog::info("RADIO firmware version: {}", version_info);
    if (version_info.substr(0, 9) != "RFD ASYNC") {
      throw maka_error(fmt::format("Please update device {} to async firmware.", device_));
    }

    // Set air speed.
    if (execute_command("ATS2=125") != "OK") {
      throw maka_error(fmt::format("Failed to set air speed on device {}", device_));
    }

    // Set network ID.
    if (execute_command("ATS9=" + std::to_string(kNetworkId)) != "OK") {
      throw maka_error(fmt::format("Failed to set network ID on device {}", device_));
    }

    // Set node ID.
    if (execute_command("ATS10=" + std::to_string(node_id_)) != "OK") {
      throw maka_error(fmt::format("Failed to set node ID on device {}", device_));
    }

    // Set broadcast destination.
    if (execute_command("ATS11=" + std::to_string(dest_id_)) != "OK") {
      throw maka_error(fmt::format("Failed to set destination ID on device {}", device_));
    }

    // Set TX power to max value.
    if (execute_command("ATS12=30") != "OK") {
      throw maka_error(fmt::format("Failed to set TX power on device {}", device_));
    }

    // Set min/max frequency.
    if (execute_command("ATS14=902050") != "OK") {
      throw maka_error(fmt::format("Failed to set min frequency on device {}", device_));
    }
    if (execute_command("ATS15=921550") != "OK") {
      throw maka_error(fmt::format("Failed to set max frequency on device {}", device_));
    }

    // Enable all channels.
    if (execute_command("ATS16=51") != "OK") {
      throw maka_error(fmt::format("Failed to enable all channels on device {}", device_));
    }

    // Set 100% duty cycle.
    if (execute_command("ATS17=100") != "OK") {
      throw maka_error(fmt::format("Failed to set duty cycle on device {}", device_));
    }

    // Set RTSCTS.
    if (execute_command("ATS19=1") != "OK") {
      throw maka_error(fmt::format("Failed to enable RTSCTS on device {}", device_));
    }

    // Enable encryption.
    if (execute_command("ATS20=1") != "OK") {
      throw maka_error(fmt::format("Failed to enable encryption on device {}", device_));
    }

    // Set encryption key.
    if (execute_command("AT&E=" + kEncryptionKey) != "OK") {
      throw maka_error(fmt::format("Failed to save settings on device {}", device_));
    }

    // Write settings.
    if (execute_command("AT&W") != "OK") {
      throw maka_error(fmt::format("Failed to save settings on device {}", device_));
    }

    // Reboot modem.
    execute_command("ATZ");
  } catch (boost::system::system_error &ex) {
    throw maka_error(fmt::format("Failed to set up device {}: {}", device_, ex.what()));
  }
}

void GpsRadioCommunicator::enter_command_mode() {
  boost::asio::write(port_, boost::asio::buffer("+++", 3));
  std::this_thread::sleep_for(std::chrono::seconds(1));
  boost::asio::write(port_, boost::asio::buffer("\r\n"));
  read_until_timeout(boost::posix_time::milliseconds(250));
}

std::string GpsRadioCommunicator::execute_command(const std::string &command) {
  auto command_with_crlf = command + "\r\n";
  boost::asio::write(port_, boost::asio::buffer(command_with_crlf));
  std::string result = read_until_timeout(boost::posix_time::milliseconds(250));
  if (result.substr(0, command_with_crlf.size()) != command_with_crlf) {
    throw maka_error(fmt::format("Invalid readback for command {} on device {}: {}", command, device_, result));
  }
  return boost::trim_copy(result.substr(command_with_crlf.size()));
}

std::string GpsRadioCommunicator::read_until_timeout(const boost::asio::deadline_timer::duration_type &timeout) {
  // Prepare timeout.
  bool timed_out = false;
  boost::asio::deadline_timer timer(io_service_);
  timer.expires_from_now(timeout);
  timer.async_wait([&timed_out](const boost::system::error_code &error) { timed_out = true; });

  // Queue asynchronous read.
  std::string result;
  std::optional<boost::system::error_code> read_error;
  boost::asio::async_read(port_, boost::asio::dynamic_string_buffer(result),
                          [&read_error](const boost::system::error_code &error, size_t) { read_error = error; });

  // Run io service until there's an error or timeout expires.
  io_service_.reset();
  while (io_service_.run_one()) {
    if (read_error.has_value() && *read_error) {
      timer.cancel();
    } else if (timed_out) {
      port_.cancel();
    }
  }

  // If read_error evaluates to true, there was an error.
  if (*read_error && *read_error != boost::asio::error::operation_aborted) {
    throw maka_error(fmt::format("Failed to read from device {}: {}", device_, read_error->message()));
  }

  return result;
}

char GpsRadioCommunicator::read_one() {
  std::unique_lock<std::mutex> lock(mutex_);
  read_cv_.wait(lock, [&] { return !read_q_.empty() || terminate_; });
  if (terminate_) {
    throw maka_error("Terminating.");
  }
  auto retval = read_q_.front();
  read_q_.pop();
  return retval;
}

std::string GpsRadioCommunicator::read_n(int count) {
  std::unique_lock<std::mutex> lock(mutex_);
  std::string retval;
  while (count) {
    read_cv_.wait(lock, [&] { return !read_q_.empty() || terminate_; });
    if (terminate_) {
      throw maka_error("Terminating.");
    }
    while (!read_q_.empty() && count) {
      auto chr = read_q_.front();
      read_q_.pop();
      retval += chr;
      count--;
    }
  }
  return retval;
}

std::string GpsRadioCommunicator::read_until(char stop_chr) {
  std::unique_lock<std::mutex> lock(mutex_);
  std::string retval;
  for (;;) {
    read_cv_.wait(lock, [&] { return !read_q_.empty() || terminate_; });
    if (terminate_) {
      throw maka_error("Terminating.");
    }
    while (!read_q_.empty()) {
      auto chr = read_q_.front();
      read_q_.pop();
      if (chr == stop_chr) {
        return retval;
      }
      retval += chr;
    }
  }
}

void GpsRadioCommunicator::write(const std::string &data) {
  io_service_.post([&, data] {
    bool write_in_progress = !write_q_.empty();
    write_q_.push(data);
    if (!write_in_progress) {
      async_write();
    }
  });
}

void GpsRadioCommunicator::async_write() {
  boost::asio::async_write(
      port_, boost::asio::buffer(write_q_.front()), [&](const boost::system::error_code &error, size_t) {
        if (error) {
          if (terminate_) {
            return;
          }
          throw maka_error(fmt::format("Failed to write to device {}: {}", device_, error.message()));
        }
        write_q_.pop();
        if (!write_q_.empty()) {
          async_write();
        }
      });
}

template <std::size_t SIZE>
void GpsRadioCommunicator::async_read(std::array<char, SIZE> &buffer) {
  boost::asio::async_read(port_, boost::asio::buffer(buffer), boost::asio::transfer_at_least(1),
                          [&](const boost::system::error_code &error, size_t sz) {
                            if (error) {
                              if (terminate_) {
                                return;
                              }
                              throw maka_error(
                                  fmt::format("Failed to read from device {}: {}", device_, error.message()));
                            }
                            {
                              std::unique_lock<std::mutex> lock(mutex_);
                              for (size_t i = 0; i < sz; i++) {
                                read_q_.push(buffer[i]);
                              }
                            }
                            read_cv_.notify_all();
                            async_read(buffer);
                          });
}

void GpsRadioCommunicator::run_forever() {
  // Enqueue reads.
  std::array<char, kBufferSize> buffer;
  io_service_.post([&] { async_read(buffer); });

  // Run service until it's canceled.
  io_service_.reset();
  io_service_.run();
}

} // namespace radio
} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

#pragma once

#include <algorithm>
#include <atomic>
#include <memory>
#include <mutex>
#include <pthread.h>
#include <string>
#include <thread>
#include <vector>

#include "gps_comm.h"
#include "lib/common/cpp/utils.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {

struct GpsStatistics {
  std::atomic<int32_t> rtcm_ok = 0;
  std::atomic<int32_t> rtcm_error = 0;
  std::atomic<int32_t> ubx_ok = 0;
  std::atomic<int32_t> ubx_error = 0;
  std::atomic<int32_t> nmea_ok = 0;
  std::atomic<int32_t> nmea_error = 0;
  std::atomic<int32_t> unknown_magic_error = 0;

  void reset() {
    rtcm_ok = 0;
    rtcm_error = 0;
    ubx_ok = 0;
    ubx_error = 0;
    nmea_ok = 0;
    nmea_error = 0;
    unknown_magic_error = 0;
  }
};

class GpsMessageHandler {
public:
  virtual ~GpsMessageHandler() = default;
  virtual void rtcm(const std::string &data){};
  virtual void ubx(uint8_t cls, uint8_t msgid, const std::string &data){};
  virtual void nmea(const std::string &data){};
};

class GpsClient {
public:
  GpsClient(std::unique_ptr<GpsCommunicator> &&comm) : comm_(std::move(comm)) {
    thread_ = std::thread([&] { run_forever(); });
    pthread_setname_np(thread_.native_handle(), "[gps_client]");
  }
  ~GpsClient() {
    terminate_ = true;
    comm_.reset();
    thread_.join();
  }
  DELETE_COPY_AND_MOVE(GpsClient)

  void send_rtcm(const std::string &data);
  void send_ubx(uint8_t cls, uint8_t msgid, const std::string &data);
  void send_nmea(const std::string &data);

  void add_handler(std::shared_ptr<GpsMessageHandler> handler) {
    std::lock_guard<std::mutex> lock(handlers_mutex_);
    handlers_.push_back(handler);
  }

  void remove_handler(std::shared_ptr<GpsMessageHandler> handler) {
    std::lock_guard<std::mutex> lock(handlers_mutex_);
    auto match = std::find(handlers_.begin(), handlers_.end(), handler);
    if (match != handlers_.end()) {
      handlers_.erase(match);
    }
  }

  GpsStatistics &get_statistics() { return statistics_; }

private:
  std::thread thread_;
  std::atomic<bool> terminate_ = false;
  std::unique_ptr<GpsCommunicator> comm_;
  std::mutex handlers_mutex_;
  std::vector<std::shared_ptr<GpsMessageHandler>> handlers_;
  GpsStatistics statistics_;
  bool unknown_magic_reported_ = false;
  void run_forever();
  void read_rtcm();
  void read_ubx();
  void read_nmea();
};

} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

#pragma once

#include <string>

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {

class GpsCommunicator {
public:
  virtual ~GpsCommunicator() = default;
  virtual void read_initial_ffs() = 0;
  virtual char read_one() = 0;
  virtual std::string read_n(int count) = 0;
  virtual std::string read_until(char chr) = 0;
  virtual void write(const std::string &data) = 0;
};

} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

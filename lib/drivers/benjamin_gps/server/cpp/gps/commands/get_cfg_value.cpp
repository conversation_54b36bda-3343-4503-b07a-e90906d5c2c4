#include <future>
#include <optional>

#include <fmt/format.h>

#include "get_fw_version.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/utils.h"
#include "lib/drivers/benjamin_gps/server/cpp/utils/endian_string.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {
namespace commands {

namespace {

class GetConfigValueHandler : public GpsMessageHandler {
public:
  GetConfigValueHandler(std::promise<std::optional<std::string>> &result, uint32_t key) : result_(result), key_(key) {}

  void ubx(uint8_t cls, uint8_t msgid, const std::string &data) override {
    if (cls == 0x06 && msgid == 0x8b) {
      auto version = data[0];
      auto layer = data[1];
      auto key = from_le_str(data.substr(4, 4));
      if (version == 0x01 && layer == 0x00 && key == this->key_) {
        // Matching returned value
        retval_ = data.substr(8);
      }
    } else if (cls == 0x05 && msgid == 0x01) {
      // ACK
      result_.set_value(retval_);
    } else if (cls == 0x05 && msgid == 0x00) {
      // NAK
      result_.set_value({});
    }
  }

private:
  std::promise<std::optional<std::string>> &result_;
  std::optional<std::string> retval_;
  size_t key_;
};

} // namespace

std::string get_config_value(GpsClient &gps, uint32_t key) {
  std::promise<std::optional<std::string>> result;
  auto handler = std::make_shared<GetConfigValueHandler>(result, key);
  gps.add_handler(handler);
  final_action _f([&] { gps.remove_handler(handler); });

  // Read from RAM.
  std::string payload{0x00, 0x00, 0x00, 0x00};
  payload += to_le_str(key, 4);
  gps.send_ubx(0x06, 0x8b, payload);

  // Grab result.
  auto result_f = result.get_future();
  if (result_f.wait_for(std::chrono::seconds(1)) == std::future_status::timeout) {
    throw maka_error("Timed out waiting for configuration value.");
  }
  auto retval = result_f.get();
  if (!retval.has_value()) {
    std::vector<std::string> payload_hex;
    for (auto chr : payload) {
      payload_hex.push_back(fmt::format("{:#04x}", chr));
    }
    throw maka_error(fmt::format("GPS receiver rejected payload: {}", fmt::join(payload_hex, " ")));
  }
  return *retval;
}

} // namespace commands
} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
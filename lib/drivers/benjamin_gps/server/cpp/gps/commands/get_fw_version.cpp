#include <future>

#include "get_fw_version.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/utils.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {
namespace commands {

namespace {

class GetFirmwareVersionHandler : public GpsMessageHandler {
public:
  GetFirmwareVersionHandler(std::promise<std::string> &result) : result_(result) {}

  void ubx(uint8_t cls, uint8_t msgid, const std::string &data) override {
    if (cls == 0x0a && msgid == 0x04) {
      for (size_t i = 40; i < data.size(); i += 30) {
        if (data.rfind("FWVER=", i) == i) {
          // Re-parse result string stopping at first \0.
          result_.set_value(std::string(data.c_str() + i + 6));
        }
      }
    }
  }

private:
  std::promise<std::string> &result_;
};

} // namespace

std::string get_firmware_version(GpsClient &gps) {
  std::promise<std::string> result;
  auto handler = std::make_shared<GetFirmwareVersionHandler>(result);
  gps.add_handler(handler);
  final_action _f([&] { gps.remove_handler(handler); });

  gps.send_ubx(0x0a, 0x04, {});

  // Grab result.
  auto result_f = result.get_future();
  if (result_f.wait_for(std::chrono::seconds(1)) == std::future_status::timeout) {
    throw maka_error("Timed out waiting for firmware version.");
  }
  return result_f.get();
}

} // namespace commands
} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
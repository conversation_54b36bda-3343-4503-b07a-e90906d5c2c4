#pragma once

#include <string>

#include "lib/drivers/benjamin_gps/server/cpp/gps/gps_client.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {
namespace commands {

// Sets config value in RAM and BBR.
void set_config_value(GpsClient &gps, uint32_t key, const std::string &value);
} // namespace commands
} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
#include <future>

#include <fmt/format.h>

#include "get_fw_version.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/utils.h"
#include "lib/drivers/benjamin_gps/server/cpp/utils/endian_string.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gps {
namespace commands {

namespace {

class SetConfigValueHandler : public GpsMessageHandler {
public:
  SetConfigValueHandler(std::promise<bool> &result) : result_(result) {}

  void ubx(uint8_t cls, uint8_t msgid, const std::string &data) override {
    if (cls == 0x05 && msgid == 0x01) {
      // ACK
      result_.set_value(true);
    }
    if (cls == 0x05 && msgid == 0x00) {
      // NAK
      result_.set_value(false);
    }
  }

private:
  std::promise<bool> &result_;
};

} // namespace

void set_config_value(GpsClient &gps, uint32_t key, const std::string &value) {
  std::promise<bool> result;
  auto handler = std::make_shared<SetConfigValueHandler>(result);
  gps.add_handler(handler);
  final_action _f([&] { gps.remove_handler(handler); });

  // Update RAM & BBR. Do not write to flash.
  std::string payload{0x00, 0x03, 0x00, 0x00};
  payload += to_le_str(key, 4);
  payload += value;
  gps.send_ubx(0x06, 0x8a, payload);

  // Grab result.
  auto result_f = result.get_future();
  if (result_f.wait_for(std::chrono::seconds(1)) == std::future_status::timeout) {
    throw maka_error("Timed out waiting for configuration value acknowledgement.");
  }
  bool success = result_f.get();
  if (!success) {
    std::vector<std::string> payload_hex;
    for (auto chr : payload) {
      payload_hex.push_back(fmt::format("{:#04x}", chr));
    }
    throw maka_error(fmt::format("GPS receiver rejected payload: {}", fmt::join(payload_hex, " ")));
  }
}

} // namespace commands
} // namespace gps
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
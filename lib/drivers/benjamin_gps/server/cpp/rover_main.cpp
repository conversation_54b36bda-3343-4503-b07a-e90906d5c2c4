#include <spdlog/spdlog.h>

#include "common.h"
#include "gps/cfg_keys.h"
#include "gps/commands/set_cfg_value.h"
#include "gps/gps_client.h"
#include "gps/spi/gps_spi_comm.h"
#include "gps/udp_multicast/gps_udp_multicast_comm.h"
#include "location/heading.h"
#include "location/position.h"
#include "rover_main.h"
#include "server/udp_server.h"
#include "time/pps_monitor.h"
#include "time/time_sync.h"
#include "utils/endian_string.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {

int rover_main() {
  spdlog::info("Running as ROVER.");

  auto rover_gps = std::make_shared<gps::GpsClient>(std::make_unique<gps::spi::GpsSpiCommunicator>("/dev/spidev1.0"));
  check_firmware_version("ROVER", *rover_gps);

  auto mov_base_gps =
      std::make_shared<gps::GpsClient>(std::make_unique<gps::spi::GpsSpiCommunicator>("/dev/spidev0.0"));
  check_firmware_version("MOVING BASE", *mov_base_gps);

  auto base_gps = std::make_shared<gps::GpsClient>(
      std::make_unique<gps::udp_multicast::GpsUdpMulticastCommunicator>(kRtcmMulticastAddress, kRtcmMulticastPort));

  // moving base: enable PPS when there's no GPS signal
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_TP_LEN_TP1, to_le_str(100000, 4));

  // moving base: enable RTCM messages: 4072.0, 1074, 1084, 1094, 1124, 1230
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE4072_0_SPI, {1});
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE1074_SPI, {1});
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE1084_SPI, {1});
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE1094_SPI, {1});
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE1124_SPI, {1});
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE1230_SPI, {1});

  // moving base & rover: enable UBX-NAV-PVT message
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_MSGOUT_UBX_NAV_PVT_SPI, {1});
  gps::commands::set_config_value(*rover_gps, gps::k_CFG_MSGOUT_UBX_NAV_PVT_SPI, {1});

  // rover: enable UBX-NAV-RELPOSNED message
  gps::commands::set_config_value(*rover_gps, gps::k_CFG_MSGOUT_UBX_NAV_RELPOSNED_SPI, {1});

  // moving base & rover: disable NMEA messages
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_SPIOUTPROT_NMEA, {0});
  gps::commands::set_config_value(*rover_gps, gps::k_CFG_SPIOUTPROT_NMEA, {0});

  // moving base & rover: 1hz measurement
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_RATE_MEAS, to_le_str(1000, 2));
  gps::commands::set_config_value(*rover_gps, gps::k_CFG_RATE_MEAS, to_le_str(1000, 2));

  // moving base & rover: timeout RTK after 5 seconds if no RTCM messages are received
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_NAVSPG_CONSTR_DGNSSTO, {5});
  gps::commands::set_config_value(*rover_gps, gps::k_CFG_NAVSPG_CONSTR_DGNSSTO, {5});

  // moving base & rover: pedestrian navigational mode (<30m/s)
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_NAVSPG_FIXMODE, gps::k_CFG_NAVSPG_FIXMODE_PED);
  gps::commands::set_config_value(*rover_gps, gps::k_CFG_NAVSPG_FIXMODE, gps::k_CFG_NAVSPG_FIXMODE_PED);

  // moving base & rover: use at least 5 satellites for navigation.
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_NAVSPG_INFIL_MINSVS, {5});
  gps::commands::set_config_value(*rover_gps, gps::k_CFG_NAVSPG_INFIL_MINSVS, {5});

  // moving base & rover: use satellites with at least 20 dbHz signal for navigation.
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_NAVSPG_INFIL_MINCNO, {20});
  gps::commands::set_config_value(*rover_gps, gps::k_CFG_NAVSPG_INFIL_MINCNO, {20});

  // moving base & rover: use satellites with at least 15 deg of elevation for navigation.
  gps::commands::set_config_value(*mov_base_gps, gps::k_CFG_NAVSPG_INFIL_MINELEV, {15});
  gps::commands::set_config_value(*rover_gps, gps::k_CFG_NAVSPG_INFIL_MINELEV, {15});

  // transmit RTCM messages from base to moving base, and moving base to rover
  base_gps->add_handler(std::make_shared<RtcmForwardingHandler>(mov_base_gps));
  mov_base_gps->add_handler(std::make_shared<RtcmForwardingHandler>(rover_gps));

  auto position_handler = std::make_shared<location::PositionHandler>();
  mov_base_gps->add_handler(position_handler);
  auto heading_handler = std::make_shared<location::HeadingHandler>(position_handler);
  rover_gps->add_handler(heading_handler);
  auto udp_server = server::UdpServer(position_handler, heading_handler);

  // enable time sync
  auto ts_handler = std::make_shared<time::TimeSyncHandler>();
  mov_base_gps->add_handler(ts_handler);
  auto pps_monitor = time::PpsMonitor("/dev/pps0", ts_handler);

  // sleep forever
  blink_running_led_forever([&] {
    auto &radio_stats = base_gps->get_statistics();
    auto position = position_handler->get_position();
    auto heading = heading_handler->get_heading();
    spdlog::info("Position fix {}. Heading fix {}. Base station messages: {} ok / {} errors.",
                 position.have_fix ? "valid" : position.have_approx_fix ? "approximate" : "INVALID",
                 heading.have_fix ? "valid" : heading.have_approx_fix ? "approximate" : "INVALID", radio_stats.rtcm_ok,
                 radio_stats.rtcm_error + radio_stats.unknown_magic_error);
    radio_stats.reset();
  });
  return 0;
}

} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

#include <csignal>
#include <functional>

#include <spdlog/spdlog.h>

#include "common.h"
#include "gps/commands/get_fw_version.h"
#include "leds/status_leds.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {

const std::string kExpectedFwVersion = "HPG 1.13";

std::atomic<bool> shutdown = false;

void signal_handler(int) { shutdown = true; }

void check_firmware_version(const std::string &chip_name, gps::GpsClient &gps) {
  auto fw_version = gps::commands::get_firmware_version(gps);
  if (fw_version != kExpectedFwVersion) {
    spdlog::warn("{} firmware version: {}, expected: {}", chip_name, fw_version, kExpectedFwVersion);
  } else {
    spdlog::info("{} firmware version: {}", chip_name, fw_version);
  }
}

void blink_running_led_forever(std::function<void()> every_minute_callback) {
  std::signal(SIGINT, signal_handler);
  std::signal(SIGTERM, signal_handler);

  while (!shutdown) {
    for (int i = 0; i < 60 && !shutdown; i++) {
      leds::kStatusLeds.set_running(true);
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
      leds::kStatusLeds.set_running(false);
      std::this_thread::sleep_for(std::chrono::milliseconds(900));
    }
    if (every_minute_callback) {
      every_minute_callback();
    }
  }

  spdlog::info("Terminating...");
}

} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
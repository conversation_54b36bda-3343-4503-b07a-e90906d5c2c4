#pragma once

#include <array>
#include <atomic>
#include <memory>
#include <thread>

#include <boost/asio.hpp>

#include "generated/lib/drivers/nanopb/proto/benjamin_gps_board.pb.h"
#include "generated/lib/drivers/nanopb/proto/gps.pb.h"
#include "generated/lib/drivers/nanopb/proto/heading.pb.h"
#include "lib/common/cpp/utils.h"
#include "lib/drivers/benjamin_gps/server/cpp/location/heading.h"
#include "lib/drivers/benjamin_gps/server/cpp/location/position.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace server {

class UdpServer {
public:
  UdpServer(std::shared_ptr<location::PositionHandler> position_handler,
            std::shared_ptr<location::HeadingHandler> heading_handler);
  ~UdpServer();
  DELETE_COPY_AND_MOVE(UdpServer)

private:
  std::shared_ptr<location::PositionHandler> position_handler_;
  std::shared_ptr<location::HeadingHandler> heading_handler_;
  boost::asio::io_service io_service_;
  boost::asio::ip::udp::socket socket_;
  std::atomic<bool> terminate_ = false;
  std::thread thread_;
  void run_forever();
  template <std::size_t SIZE>
  void async_read(std::array<pb_byte_t, SIZE> &buffer);
  template <std::size_t SIZE>
  void process_message(std::array<pb_byte_t, SIZE> &buffer, size_t len, boost::asio::ip::udp::endpoint sender_endpoint);
  void handle_ping(diagnostic_Ping *req, diagnostic_Pong *resp);
  void handle_gps(gps_Request *req, gps_Reply *resp);
  void handle_gps_position(gps_Position_Request *req, gps_Position_Reply *resp);
  void handle_heading(heading_Request *req, heading_Reply *resp);
};

} // namespace server
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

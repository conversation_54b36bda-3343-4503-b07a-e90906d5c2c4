#include <memory>
#include <pthread.h>
#include <vector>

#include <fmt/format.h>
#include <pb_decode.h>
#include <pb_encode.h>
#include <spdlog/spdlog.h>

#include "lib/common/cpp/exceptions.h"
#include "lib/drivers/benjamin_gps/server/cpp/leds/status_leds.h"
#include "udp_server.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace server {

constexpr uint16_t kUdpPort = 4244;
constexpr size_t kBufferSize = 1024;

using boost::asio::ip::udp;

UdpServer::UdpServer(std::shared_ptr<location::PositionHandler> position_handler,
                     std::shared_ptr<location::HeadingHandler> heading_handler)
    : position_handler_(position_handler), heading_handler_(heading_handler),
      socket_(udp::socket(io_service_, udp::endpoint(udp::v4(), kUdpPort))) {
  thread_ = std::thread([&] { run_forever(); });
  pthread_setname_np(thread_.native_handle(), "[udp_server]");
}

UdpServer::~UdpServer() {
  terminate_ = true;
  io_service_.post([&] {
    boost::system::error_code ignore_error;
    socket_.close(ignore_error);
  });
  if (thread_.joinable()) {
    thread_.join();
  }
}

void UdpServer::run_forever() {
  // Enqueue reads.
  std::array<pb_byte_t, kBufferSize> buffer;
  io_service_.post([&] { async_read(buffer); });

  // Run service until it's canceled.
  io_service_.reset();
  io_service_.run();
}

template <std::size_t SIZE>
void UdpServer::async_read(std::array<pb_byte_t, SIZE> &buffer) {
  auto sender_endpoint = std::make_shared<udp::endpoint>();
  socket_.async_receive_from(boost::asio::buffer(buffer), *sender_endpoint, 0,
                             [&, sender_endpoint](const boost::system::error_code &error, size_t len) {
                               if (error) {
                                 if (terminate_) {
                                   return;
                                 }
                                 spdlog::warn("UDP receive error, skipping message: {}", error.message());
                               } else {
                                 leds::kStatusLeds.set_udp_handling(true);
                                 process_message(buffer, len, *sender_endpoint);
                                 leds::kStatusLeds.set_udp_handling(false);
                               }
                               async_read(buffer);
                             });
}

template <std::size_t SIZE>
void UdpServer::process_message(std::array<pb_byte_t, SIZE> &buffer, size_t len, udp::endpoint sender_endpoint) {
  // Decode request.
  benjamin_gps_board_Request req = benjamin_gps_board_Request_init_zero;
  {
    pb_istream_t stream = pb_istream_from_buffer(buffer.data(), len);
    bool status = pb_decode(&stream, benjamin_gps_board_Request_fields, &req);
    if (!status) {
      spdlog::warn("UDP deserialization failed, skipping message.");
      return;
    }
  }

  benjamin_gps_board_Reply resp = benjamin_gps_board_Reply_init_zero;
  resp.has_header = true;
  resp.header.requestId = req.header.requestId;
  switch (req.which_request) {
  case benjamin_gps_board_Request_ping_tag:
    resp.which_reply = benjamin_gps_board_Reply_pong_tag;
    handle_ping(&req.request.ping, &resp.reply.pong);
    break;
  case benjamin_gps_board_Request_gps_tag:
    resp.which_reply = benjamin_gps_board_Reply_gps_tag;
    handle_gps(&req.request.gps, &resp.reply.gps);
    break;
  case benjamin_gps_board_Request_heading_tag:
    resp.which_reply = benjamin_gps_board_Reply_heading_tag;
    handle_heading(&req.request.heading, &resp.reply.heading);
    break;
  default:
    // Unknown request.
    return;
  }

  // Encode response.
  size_t bytes_to_send;
  {
    pb_ostream_t stream = pb_ostream_from_buffer(buffer.data(), kBufferSize);
    bool status = pb_encode(&stream, benjamin_gps_board_Reply_fields, &resp);
    assert(status); // encoding should not fail
    bytes_to_send = stream.bytes_written;
  }

  boost::system::error_code ignored_error;
  auto buffer_copy_to_send = std::make_shared<std::vector<pb_byte_t>>(buffer.begin(), buffer.begin() + bytes_to_send);
  // Copying buffer_copy_to_send shared reference into callback ensures buffer will survive until the request is
  // complete.
  socket_.async_send_to(boost::asio::buffer(*buffer_copy_to_send), sender_endpoint, 0,
                        [buffer_copy_to_send](const boost::system::error_code &, size_t) {});
}

void UdpServer::handle_ping(diagnostic_Ping *req, diagnostic_Pong *resp) { resp->x = req->x; }

void UdpServer::handle_gps(gps_Request *req, gps_Reply *resp) {
  switch (req->which_request) {
  case gps_Request_position_tag:
    resp->which_reply = gps_Reply_position_tag;
    handle_gps_position(&req->request.position, &resp->reply.position);
    break;
  default:
    // Unknown request.
    return;
  }
}

void UdpServer::handle_gps_position(gps_Position_Request *unused, gps_Position_Reply *resp) {
  auto position = position_handler_->get_position();
  resp->have_fix = position.have_fix;
  resp->have_approx_fix = position.have_approx_fix;
  resp->latitude = position.latitude;
  resp->longitude = position.longitude;
  resp->height_mm = position.height_mm;
  resp->num_sats = position.num_sats;
  resp->hdop = position.hdop;
  resp->timestamp_ms = position.timestamp_ms;
}

void UdpServer::handle_heading(heading_Request *unused, heading_Reply *resp) {
  auto heading = heading_handler_->get_heading();
  resp->have_fix = heading.have_fix;
  resp->have_approx_fix = heading.have_approx_fix;
  resp->heading_deg = heading.heading_deg;
  resp->accuracy_deg = heading.accuracy_deg;
  resp->timestamp_ms = heading.timestamp_ms;
}

} // namespace server
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

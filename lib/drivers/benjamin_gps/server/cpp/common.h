#pragma once

#include <functional>
#include <string>

#include "gps/gps_client.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {

const std::string kRtcmMulticastAddress = "************";
const unsigned short kRtcmMulticastPort = 12883;

void check_firmware_version(const std::string &chip_name, gps::GpsClient &gps);
void blink_running_led_forever(std::function<void()> every_minute_callback = {});

class RtcmForwardingHandler : public gps::GpsMessageHandler {
public:
  RtcmForwardingHandler(std::shared_ptr<gps::GpsClient> target_gps, std::function<bool()> enabled = {})
      : target_gps_(target_gps), enabled_(enabled) {}

  void rtcm(const std::string &data) override {
    if (!enabled_ || enabled_()) {
      target_gps_->send_rtcm(data);
    }
  }

private:
  std::shared_ptr<gps::GpsClient> target_gps_;
  std::function<bool()> enabled_;
};

} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

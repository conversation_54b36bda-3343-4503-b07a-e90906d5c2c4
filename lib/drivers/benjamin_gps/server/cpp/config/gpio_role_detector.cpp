#include <fmt/format.h>

#include "gpio_role_detector.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/utils.h"
#include "lib/drivers/benjamin_gps/server/cpp/gpio/gpio_reader.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace config {

const char *kGpioRoleChip = "/dev/gpiochip1";
constexpr uint32_t kGpioRoverLine = 12;
constexpr uint32_t kGpioBaseLine = 13;

Role gpio_detect_role() {
  gpio::GpioReader reader(kGpioRoleChip);
  auto is_rover_set = reader.read_line(kGpioRoverLine);
  auto is_base_set = reader.read_line(kGpioBaseLine);
  if (is_rover_set && !is_base_set) {
    return Role::ROVER;
  }
  if (!is_rover_set && is_base_set) {
    return Role::BASE;
  }
  return Role::UNKNOWN;
}

} // namespace config
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
#include <fmt/format.h>

#include "gpio_reader.h"
#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gpio {

const char *kConsumerName = "benjamin_gps";

GpioReader::GpioReader(std::string device) : device_(device) {
  chip_ = gpiod_chip_open(device_.c_str());
  if (chip_ == nullptr) {
    throw maka_error(fmt::format("Failed to open GPIO chip {}", device_));
  }
}

GpioReader::~GpioReader() {
  if (chip_ != nullptr) {
    gpiod_chip_close(chip_);
  }
}

bool GpioReader::read_line(uint32_t line_num) {
  auto *line = gpiod_chip_get_line(chip_, line_num);
  if (line == nullptr) {
    throw maka_error(fmt::format("Failed to get line #{} on GPIO chip {}", line_num, device_));
  }
  if (gpiod_line_request_input(line, kConsumerName) < 0) {
    throw maka_error(fmt::format("Failed to reserve line #{} on GPIO chip {}", line_num, device_));
  }
  final_action _f_rover_line([&] { gpiod_line_release(line); });
  auto value = gpiod_line_get_value(line);
  if (value < 0) {
    throw maka_error(fmt::format("Failed to read line #{} on GPIO chip {}", line_num, device_));
  }
  return value;
}

} // namespace gpio
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
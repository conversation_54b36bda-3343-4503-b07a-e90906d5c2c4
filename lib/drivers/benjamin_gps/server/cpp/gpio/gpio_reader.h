#pragma once

#include <cstdint>
#include <string>

#include <gpiod.h>

#include "lib/common/cpp/utils.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace gpio {

class GpioReader {
public:
  GpioReader(std::string device);
  ~G<PERSON>Reader();
  DELETE_COPY_AND_MOVE(GpioReader)

  bool read_line(uint32_t line_num);

private:
  std::string device_;
  struct gpiod_chip *chip_;
};

} // namespace gpio
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
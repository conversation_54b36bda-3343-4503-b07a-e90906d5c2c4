#pragma once

#include <atomic>

#include "lib/drivers/benjamin_gps/server/cpp/gps/gps_client.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace time {

class TimeSyncHandler : public gps::GpsMessageHandler {
public:
  void ubx(uint8_t cls, uint8_t msgid, const std::string &data) override {
    if (cls == 0x01 && msgid == 0x07) {
      process_ubx_nav_pvt(data);
    }
  }

  // Returns whether GPS time is valid and system clock was adjusted to it.
  bool is_time_valid() const { return is_time_valid_; }

private:
  void process_ubx_nav_pvt(const std::string &data);
  std::atomic<bool> is_time_valid_ = false;
};

} // namespace time
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
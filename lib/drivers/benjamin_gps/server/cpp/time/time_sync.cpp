#include <sys/timex.h>

#include <spdlog/spdlog.h>

#include "lib/common/cpp/exceptions.h"
#include "lib/drivers/benjamin_gps/server/cpp/leds/status_leds.h"
#include "lib/drivers/benjamin_gps/server/cpp/utils/endian_string.h"
#include "lib/drivers/benjamin_gps/server/cpp/utils/ubx_nav_pvt_time.h"
#include "time_sync.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace time {

constexpr int32_t kSecondNs = 1000000000;
constexpr int32_t kHalfSecondNs = kSecondNs / 2;

void TimeSyncHandler::process_ubx_nav_pvt(const std::string &data) {
  std::optional<uint64_t> gpstime_ns = ubx_nav_pvt_to_time_ns(data);
  bool is_valid = gpstime_ns.has_value();
  if (!is_time_valid_ && is_valid) {
    spdlog::info("GPS time is now valid.");
    leds::kStatusLeds.set_time_valid(true);
  } else if (is_time_valid_ && !is_valid) {
    spdlog::info("GPS time became INVALID.");
    leds::kStatusLeds.set_time_valid(false);
  }
  if (!is_valid) {
    is_time_valid_ = false;
    return;
  }

  // Retrieve system clock and convert into nanoseconds since epoch.
  struct timex tx_read {};
  tx_read.modes = ADJ_NANO;
  if (adjtimex(&tx_read) < 0) {
    throw maka_error("Failed to query system clock.");
  }
  int64_t sysclock_ns = int64_t(tx_read.time.tv_sec) * kSecondNs + int64_t(tx_read.time.tv_usec);

  // Apply adjustment based on the whole number of seconds.
  int64_t delta_ns = *gpstime_ns - sysclock_ns;
  time_t delta_s = (delta_ns + kHalfSecondNs) / kSecondNs;
  if (delta_s != 0) {
    struct timex tx_write {};
    tx_write.modes = ADJ_SETOFFSET;
    tx_write.time.tv_sec = delta_s;
    if (adjtimex(&tx_write) < 0) {
      throw maka_error("Failed to set system time.");
    }
    spdlog::info("Adjusted system time by {} seconds.", delta_s);
  }

  // GPS time is valid and system clock has been adjusted to our best ability.
  is_time_valid_ = true;
}

} // namespace time
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
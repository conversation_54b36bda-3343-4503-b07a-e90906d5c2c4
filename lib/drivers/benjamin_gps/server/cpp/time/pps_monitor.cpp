#include <algorithm>
#include <fcntl.h>
#include <linux/pps.h>
#include <pthread.h>
#include <sys/ioctl.h>
#include <sys/timex.h>
#include <unistd.h>

#include <fmt/format.h>
#include <spdlog/spdlog.h>

#include "lib/common/cpp/exceptions.h"
#include "lib/drivers/benjamin_gps/server/cpp/leds/status_leds.h"
#include "pps_monitor.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace time {

constexpr int32_t kUnsyncThresholdUs = 1000;
constexpr int32_t kSecondNs = 1000000000;

PpsMonitor::PpsMonitor(std::string device, std::shared_ptr<TimeSyncHandler> time_sync_handler)
    : device_(device), time_sync_handler_(time_sync_handler) {
  fd_ = open(device.c_str(), O_RDWR);
  if (fd_ < 0) {
    throw maka_error(fmt::format("Failed to open device: {}", device));
  }

  struct pps_kparams params {};
  params.api_version = PPS_API_VERS;
  params.mode |= PPS_CAPTUREASSERT;
  if (ioctl(fd_, PPS_SETPARAMS, &params) < 0) {
    throw maka_error(fmt::format("Failed to set PPS parameters on device {}", device_));
  }

  thread_ = std::thread([&] { run_forever(); });
  pthread_setname_np(thread_.native_handle(), "[pps_monitor]");
}

PpsMonitor::~PpsMonitor() {
  terminate_ = true;
  if (thread_.joinable()) {
    thread_.join();
  }
  if (fd_ >= 0) {
    close(fd_);
  }
}

void PpsMonitor::run_forever() {
  try {
    while (!terminate_) {
      // Wait for a PPS tick.
      struct pps_fdata data {};
      data.timeout.flags = PPS_TIME_INVALID;
      if (ioctl(fd_, PPS_FETCH, &data) < 0) {
        throw maka_error("Failed to fetch PPS tick.");
      }

      // Verify that the time is valid.
      if (!time_sync_handler_->is_time_valid()) {
        continue;
      }

      // Read current system clock status flags.
      struct timex tx_read {};
      if (adjtimex(&tx_read) < 0) {
        throw maka_error(fmt::format("Failed to query system clock."));
      }
      int tx_status = tx_read.status & ~STA_UNSYNC;

      // Minimum of data.info.assert_tu.nsec and (kSecondNs - data.info.assert_tu.nsec) is our
      // clock accuracy. We will inform system about this accuracy, so it can synchronize RTC
      // and do other useful things with it. We will also remove STA_UNSYNC flag if the accuracy
      // is within 1ms.
      uint32_t accuracy_us = (std::min(data.info.assert_tu.nsec, kSecondNs - data.info.assert_tu.nsec) + 999) / 1000;
      struct timex tx_write {};
      tx_write.modes = ADJ_MAXERROR | ADJ_ESTERROR | ADJ_STATUS;
      tx_write.maxerror = accuracy_us;
      tx_write.esterror = accuracy_us;
      tx_write.status = tx_status | (accuracy_us > kUnsyncThresholdUs ? STA_UNSYNC : 0);
      if (adjtimex(&tx_write) < 0) {
        throw maka_error(fmt::format("Failed to set system clock maxerror to {}", accuracy_us));
      }

      // Blink LEDs to show PPS tick. We have plenty of time between ticks, so spending 100ms here
      // is not a problem.
      leds::kStatusLeds.set_pps_tick(true);
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
      leds::kStatusLeds.set_pps_tick(false);
    }
  } catch (std::exception &ex) {
    if (!terminate_) {
      spdlog::error("PPS monitor failed: {}", ex.what());
      throw;
    }
  }
}

} // namespace time
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
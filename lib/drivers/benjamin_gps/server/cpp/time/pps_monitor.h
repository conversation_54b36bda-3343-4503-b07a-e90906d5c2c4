#pragma once

#include <atomic>
#include <memory>
#include <string>
#include <thread>

#include "lib/common/cpp/utils.h"
#include "time_sync.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace time {

class PpsMonitor {
public:
  PpsMonitor(std::string device, std::shared_ptr<TimeSyncHandler> time_sync_handler);
  ~PpsMonitor();
  DELETE_COPY_AND_MOVE(PpsMonitor)

private:
  int fd_;
  std::string device_;
  std::shared_ptr<TimeSyncHandler> time_sync_handler_;
  std::atomic<bool> terminate_ = false;
  std::thread thread_;
  void run_forever();
};

} // namespace time
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
ROOT_DIR             = ../../../../..
BIN_DIR              = $(ROOT_DIR)/bin
TARGET               = $(BIN_DIR)/benjamin_gps
NANOPB_DIR           = nanopb
NANOPB_GENERATED_DIR = $(ROOT_DIR)/generated/lib/drivers/nanopb/proto
SRC                  = $(wildcard *.cpp) $(wildcard */*.cpp) $(wildcard */*/*.cpp) \
                       $(wildcard $(NANOPB_DIR)/*.c) $(wildcard $(NANOPB_GENERATED_DIR)/*.c) \
                       $(ROOT_DIR)/lib/common/cpp/exceptions.cpp
OBJ                  = $(patsubst %.cpp,%.o,$(patsubst %.c,%.o,$(SRC)))

# Buildroot supplies CXXFLAGS/LDFLAGS/etc, so we need to use override
# to append flags required for our code.
override CFLAGS   += -Wall -Wshadow -Werror -O2 -I$(ROOT_DIR) -I$(NANOPB_DIR)
override CXXFLAGS += -Wall -Wshadow -Werror -std=c++17 -O2 -DSPDLOG_FMT_EXTERNAL=1 -I$(ROOT_DIR) -I$(NANOPB_DIR)
override LDFLAGS  += -lgpiod -li2c -lfmt -lspdlog -ldl -lpthread -lbacktrace

.PHONY: all clean

all: $(TARGET)

$(TARGET): $(OBJ)
	$(CXX) -o $@ $^ $(LDFLAGS)

clean:
	rm -f $(OBJ) $(TARGET)

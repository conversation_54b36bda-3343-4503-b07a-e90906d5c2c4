#pragma once

#include <optional>
#include <string>
#include <time.h>

#include "endian_string.h"

constexpr uint8_t kUbxNavPvtDateTimeValidFlags = 0b111;
constexpr uint8_t kUbxNavPvtGnssFixOkFlags = 0b1;
constexpr int32_t kSecondNs = 1000000000;

static std::optional<int64_t> ubx_nav_pvt_to_time_ns(const std::string &data) {
  // Decode GPS clock.
  uint16_t year = from_le_str(data.substr(4, 2));
  uint8_t month = data[6];
  uint8_t day = data[7];
  uint8_t hour = data[8];
  uint8_t min = data[9];
  uint8_t sec = data[10];
  int32_t nano = from_le_str(data.substr(16, 4));

  // Determine if GPS clock is valid.
  uint8_t valid_flags = data[11];
  uint8_t fix_flags = data[21];
  bool is_valid = (valid_flags & kUbxNavPvtDateTimeValidFlags) == kUbxNavPvtDateTimeValidFlags &&
                  (fix_flags & kUbxNavPvtGnssFixOkFlags) == kUbxNavPvtGnssFixOkFlags;
  if (!is_valid) {
    return {};
  }

  // Convert GPS clock into nanoseconds since epoch.
  struct tm tm {};
  tm.tm_year = year - 1900;
  tm.tm_mon = month - 1;
  tm.tm_mday = day;
  tm.tm_hour = hour;
  tm.tm_min = min;
  tm.tm_sec = sec;
  time_t gpstime_s = mktime(&tm);
  int64_t gpstime_ns = int64_t(gpstime_s) * kSecondNs + int64_t(nano);
  return gpstime_ns;
}
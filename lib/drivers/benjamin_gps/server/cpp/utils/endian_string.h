#pragma once

#include <cassert>
#include <string>

inline uint32_t from_be_str(const std::string &data) {
  uint32_t result = 0;
  assert(data.size() <= 4);
  for (size_t i = 0; i < data.size(); i++) {
    result |= data[i] << (8 * (data.size() - (i + 1)));
  }
  return result;
}

inline uint32_t from_le_str(const std::string &data) {
  uint32_t result = 0;
  assert(data.size() <= 4);
  for (size_t i = 0; i < data.size(); i++) {
    result |= data[i] << (8 * i);
  }
  return result;
}

inline std::string to_be_str(uint32_t data, size_t sz) {
  std::string result = "";
  assert(sz <= 4);
  for (size_t i = 0; i < sz; i++) {
    result += data >> (8 * (sz - (i + 1)));
  }
  return result;
}

inline std::string to_le_str(uint32_t data, size_t sz) {
  std::string result = "";
  assert(sz <= 4);
  for (size_t i = 0; i < sz; i++) {
    result += data >> (8 * i);
  }
  return result;
}

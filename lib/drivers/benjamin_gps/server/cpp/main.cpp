#include <exception>

#include <spdlog/spdlog.h>

#include "base_main.h"
#include "config/gpio_role_detector.h"
#include "lib/common/cpp/exceptions.h"
#include "rover_main.h"

using namespace lib::drivers::benjamin_gps;

int main() {
  spdlog::info("Benjamin GPS booting.");

  try {
    auto role = config::gpio_detect_role();
    switch (role) {
    case config::Role::ROVER:
      return rover_main();
    case config::Role::BASE:
      return base_main();
    default:
      throw maka_error("Failed to detect role, please check ROVER/BASE jumper.");
    }
  } catch (std::exception &ex) {
    spdlog::error("{}", ex.what());
    return -1;
  }
}
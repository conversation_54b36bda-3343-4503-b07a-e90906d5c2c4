#pragma once

#include <mutex>
#include <string>

#include "lib/common/cpp/utils.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace leds {

class StatusLeds {
public:
  StatusLeds();
  ~StatusLeds();
  DELETE_COPY_AND_MOVE(StatusLeds)

  void set_running(bool value);
  void set_time_valid(bool value);
  void set_pps_tick(bool value);
  void set_position_valid(bool value);
  void set_heading_valid(bool value);
  void set_udp_handling(bool value);
  void set_survey_in_complete(bool value);

private:
  std::mutex mutex_;
  void safe_set(int led, bool value);
};

extern StatusLeds kStatusLeds;

} // namespace leds
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

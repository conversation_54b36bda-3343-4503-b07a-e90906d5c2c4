#include <fstream>
#include <iostream>

#include <fmt/format.h>

#include "lib/common/cpp/exceptions.h"
#include "status_leds.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace leds {

constexpr int kNumLeds = 6;
constexpr int kBrightnessOn = 0x20;
constexpr int kBrightnessOff = 0x0;

StatusLeds kStatusLeds;

StatusLeds::StatusLeds() {
  for (int i = 0; i < kNumLeds; i++) {
    safe_set(i, false);
  }
}

StatusLeds::~StatusLeds() {
  for (int i = 0; i < kNumLeds; i++) {
    safe_set(i, false);
  }
}

void StatusLeds::set_running(bool value) { safe_set(0, value); }

void StatusLeds::set_time_valid(bool value) { safe_set(1, value); }

void StatusLeds::set_pps_tick(bool value) { safe_set(2, value); }

void StatusLeds::set_position_valid(bool value) { safe_set(3, value); }

void StatusLeds::set_heading_valid(bool value) { safe_set(4, value); }

void StatusLeds::set_udp_handling(bool value) { safe_set(5, value); }

void StatusLeds::set_survey_in_complete(bool value) { safe_set(3, value); }

void StatusLeds::safe_set(int led, bool value) {
  std::lock_guard<std::mutex> lock(mutex_);
  std::ofstream f(fmt::format("/sys/class/leds/status:led{}/brightness", led));
  f << std::to_string(value ? kBrightnessOn : kBrightnessOff);
  if (!f) {
    throw maka_error(fmt::format("Failed to turn LED #{} {}", led, value ? "on" : "off"));
  }
}

} // namespace leds
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

#include <time.h>

#include <spdlog/spdlog.h>

#include "heading.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/time.h"
#include "lib/drivers/benjamin_gps/server/cpp/leds/status_leds.h"
#include "lib/drivers/benjamin_gps/server/cpp/utils/endian_string.h"
#include "lib/drivers/benjamin_gps/server/cpp/utils/ubx_nav_pvt_time.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace location {

constexpr uint32_t kUbxNavRelposnedGnssFixOkFlag = 0b01 << 0;
constexpr uint32_t kUbxNavRelposnedDiffSolnFlag = 0b01 << 1;
constexpr uint32_t kUbxNavRelposnedRelPosValidFlag = 0b01 << 2;
constexpr uint32_t kUbxNavRelposnedCarrSolnFloatingFlag = 0b1 << 3;
constexpr uint32_t kUbxNavRelposnedCarrSolnFixedFlag = 0b1 << 4;
constexpr uint32_t kUbxNavRelposnedRelPosHeadingValidFlag = 0b1 << 8;
constexpr uint32_t kUbxNavRelposnedFixFlags =
    kUbxNavRelposnedGnssFixOkFlag | kUbxNavRelposnedDiffSolnFlag | kUbxNavRelposnedRelPosValidFlag |
    kUbxNavRelposnedCarrSolnFloatingFlag | kUbxNavRelposnedCarrSolnFixedFlag | kUbxNavRelposnedRelPosHeadingValidFlag;
constexpr int32_t kMillisecondNs = 1000000;

void HeadingHandler::process_ubx_nav_pvt(const std::string &data) {
  // Parse UTC time.
  std::optional<uint64_t> gpstime_ns = ubx_nav_pvt_to_time_ns(data);
  if (gpstime_ns.has_value()) {
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wmaybe-uninitialized"
    last_timestamp_ms_ = *gpstime_ns / kMillisecondNs;
#pragma GCC diagnostic pop
  } else {
    last_timestamp_ms_ = maka_control_timestamp_ms();
  }

  // Each UBX-NAV message contains iTOW, which is used to link them up.
  last_itow_ = from_le_str(data.substr(0, 4));
}

void HeadingHandler::process_ubx_nav_relposned(const std::string &data) {
  uint32_t itow = from_le_str(data.substr(4, 4));
  if (itow != last_itow_) {
    // This is not supposed to happen.
    spdlog::error("UBX-NAV-RELPOSNED iTOW {} did not match UBX-NAV-PVT iTOW {}, skipping heading update.", itow,
                  last_itow_);
    return;
  }

  uint32_t fix_flags = from_le_str(data.substr(60, 4)) & kUbxNavRelposnedFixFlags;
  bool gnss_fix_ok = fix_flags & kUbxNavRelposnedGnssFixOkFlag;
  bool rtcm_applied = fix_flags & kUbxNavRelposnedDiffSolnFlag;
  bool floating_fix = fix_flags & kUbxNavRelposnedCarrSolnFloatingFlag;
  bool fixed_fix = fix_flags & kUbxNavRelposnedCarrSolnFixedFlag;
  bool heading_valid = fix_flags & kUbxNavRelposnedRelPosHeadingValidFlag;
  bool position_has_fix = position_handler_->get_position().have_fix;
  bool have_fix = gnss_fix_ok && rtcm_applied && fixed_fix && heading_valid && position_has_fix;
  if (last_fix_flags_ != fix_flags || last_position_had_fix_ != position_has_fix) {
    spdlog::info("Heading fix {}. GNSS fix: {}, RTCM applied: {}, RTK fix: {}, heading: {}, position: {}.",
                 have_fix ? "valid" : "INVALID", gnss_fix_ok ? "yes" : "NO", rtcm_applied ? "yes" : "NO",
                 fixed_fix ? "fixed" : floating_fix ? "FLOATING" : "NO", heading_valid ? "valid" : "INVALID",
                 position_has_fix ? "valid" : "INVALID");
    leds::kStatusLeds.set_heading_valid(have_fix);
    last_fix_flags_ = fix_flags;
    last_position_had_fix_ = position_has_fix;
  }

  std::lock_guard<std::mutex> lock(state_mu_);
  state_.have_fix = have_fix;
  state_.have_approx_fix = heading_valid;
  state_.heading_deg = double(int32_t(from_le_str(data.substr(24, 4)))) * 1e-5;
  state_.accuracy_deg = double(from_le_str(data.substr(52, 4))) * 1e-5;
  state_.timestamp_ms = last_timestamp_ms_;
}

} // namespace location
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
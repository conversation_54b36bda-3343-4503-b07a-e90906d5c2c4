#pragma once

#include <memory>
#include <mutex>

#include "lib/drivers/benjamin_gps/server/cpp/gps/gps_client.h"
#include "position.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace location {

struct HeadingState {
  bool have_fix;
  bool have_approx_fix;
  double heading_deg;
  double accuracy_deg;
  uint64_t timestamp_ms;
};

class HeadingHandler : public gps::GpsMessageHandler {
public:
  HeadingHandler(std::shared_ptr<PositionHandler> position_handler) : position_handler_(position_handler) {}

  void ubx(uint8_t cls, uint8_t msgid, const std::string &data) override {
    if (cls == 0x01 && msgid == 0x07) {
      process_ubx_nav_pvt(data);
    } else if (cls == 0x01 && msgid == 0x3c) {
      process_ubx_nav_relposned(data);
    }
  }

  HeadingState get_heading() {
    std::lock_guard<std::mutex> lock(state_mu_);
    HeadingState retval = state_;
    return retval;
  }

private:
  void process_ubx_nav_pvt(const std::string &data);
  void process_ubx_nav_relposned(const std::string &data);
  std::shared_ptr<PositionHandler> position_handler_;
  uint64_t last_timestamp_ms_ = 0;
  uint32_t last_itow_ = 0;
  bool last_position_had_fix_ = false;
  uint32_t last_fix_flags_ = 0;
  HeadingState state_;
  std::mutex state_mu_;
};

} // namespace location
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
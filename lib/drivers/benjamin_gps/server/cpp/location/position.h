#pragma once

#include <mutex>

#include "lib/drivers/benjamin_gps/server/cpp/gps/gps_client.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace location {

struct PositionState {
  bool have_fix;
  bool have_approx_fix;
  double latitude;
  double longitude;
  int height_mm;
  int num_sats;
  double hdop;
  uint64_t timestamp_ms;
};

class PositionHandler : public gps::GpsMessageHandler {
public:
  void ubx(uint8_t cls, uint8_t msgid, const std::string &data) override {
    if (cls == 0x01 && msgid == 0x07) {
      process_ubx_nav_pvt(data);
    }
  }

  PositionState get_position() {
    std::lock_guard<std::mutex> lock(state_mu_);
    PositionState retval = state_;
    return retval;
  }

private:
  void process_ubx_nav_pvt(const std::string &data);
  uint8_t last_fix_flags_ = 0;
  PositionState state_;
  std::mutex state_mu_;
};

} // namespace location
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
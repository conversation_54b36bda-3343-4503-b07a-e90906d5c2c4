#include <time.h>

#include <spdlog/spdlog.h>

#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/time.h"
#include "lib/drivers/benjamin_gps/server/cpp/leds/status_leds.h"
#include "lib/drivers/benjamin_gps/server/cpp/utils/endian_string.h"
#include "lib/drivers/benjamin_gps/server/cpp/utils/ubx_nav_pvt_time.h"
#include "position.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {
namespace location {

// Fix is valid if we're in fixed RTK mode.
constexpr uint8_t kUbxNavPvtGnssFixOkFlag = 0b01 << 0;
constexpr uint8_t kUbxNavPvtDiffSolnFlag = 0b01 << 1;
constexpr uint8_t kUbxNavPvtCarrSolnFloatingFlag = 0b1 << 6;
constexpr uint8_t kUbxNavPvtCarrSolnFixedFlag = 0b1 << 7;
constexpr uint8_t kUbxNavPvtFixFlags =
    kUbxNavPvtGnssFixOkFlag | kUbxNavPvtDiffSolnFlag | kUbxNavPvtCarrSolnFloatingFlag | kUbxNavPvtCarrSolnFixedFlag;
constexpr int32_t kMillisecondNs = 1000000;

void PositionHandler::process_ubx_nav_pvt(const std::string &data) {
  // Determine if fix status has changed.
  uint8_t fix_flags = data[21] & kUbxNavPvtFixFlags;
  bool gnss_fix_ok = fix_flags & kUbxNavPvtGnssFixOkFlag;
  bool rtcm_applied = fix_flags & kUbxNavPvtDiffSolnFlag;
  bool floating_fix = fix_flags & kUbxNavPvtCarrSolnFloatingFlag;
  bool fixed_fix = fix_flags & kUbxNavPvtCarrSolnFixedFlag;
  bool have_fix = gnss_fix_ok && rtcm_applied && fixed_fix;
  if (last_fix_flags_ != fix_flags) {
    spdlog::info("Position fix {}. GNSS fix: {}, RTCM applied: {}, RTK fix: {}.", have_fix ? "valid" : "INVALID",
                 gnss_fix_ok ? "yes" : "NO", rtcm_applied ? "yes" : "NO",
                 fixed_fix ? "fixed" : floating_fix ? "FLOATING" : "NO");
    leds::kStatusLeds.set_position_valid(have_fix);
    last_fix_flags_ = fix_flags;
  }

  // Parse UTC time.
  std::optional<uint64_t> gpstime_ns = ubx_nav_pvt_to_time_ns(data);

  std::lock_guard<std::mutex> lock(state_mu_);
  state_.have_fix = have_fix;
  state_.have_approx_fix = gnss_fix_ok;
  state_.latitude = double(int32_t(from_le_str(data.substr(28, 4)))) * 1e-7;
  state_.longitude = double(int32_t(from_le_str(data.substr(24, 4)))) * 1e-7;
  state_.height_mm = int32_t(from_le_str(data.substr(32, 4)));
  state_.num_sats = data[23];
  state_.hdop = double(from_le_str(data.substr(76, 2))) * 0.01;
  if (gpstime_ns.has_value()) {
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wmaybe-uninitialized"
    state_.timestamp_ms = *gpstime_ns / kMillisecondNs;
#pragma GCC diagnostic pop
  } else {
    state_.timestamp_ms = maka_control_timestamp_ms();
  }
}

} // namespace location
} // namespace benjamin_gps
} // namespace drivers
} // namespace lib
#include <atomic>

#include <spdlog/spdlog.h>

#include "base_main.h"
#include "common.h"
#include "gps/cfg_keys.h"
#include "gps/commands/set_cfg_value.h"
#include "gps/gps_client.h"
#include "gps/spi/gps_spi_comm.h"
#include "gps/udp_multicast/gps_udp_multicast_comm.h"
#include "leds/status_leds.h"
#include "time/pps_monitor.h"
#include "time/time_sync.h"
#include "utils/endian_string.h"

namespace lib {
namespace drivers {
namespace benjamin_gps {

namespace {

class SurveyInStatusHandler : public gps::GpsMessageHandler {
public:
  void ubx(uint8_t cls, uint8_t msgid, const std::string &data) override {
    if (cls == 0x01 && msgid == 0x3b) {
      uint32_t duration_s = from_le_str(data.substr(8, 4));
      uint32_t mean_acc = from_le_str(data.substr(28, 4));
      float mean_acc_m = (float)mean_acc / 10000;
      bool valid = data[36];
      bool active = data[37];
      if (active && !active_) {
        spdlog::info("Survey in progress ({}s elapsed). Accuracy: {:.2f}m", duration_s, mean_acc_m);
        leds::kStatusLeds.set_survey_in_complete(false);
        active_ = true;
        valid_ = false;
        position_shown_ = false;
      } else if (valid && !valid_) {
        spdlog::info("Survey complete after {}s. Accuracy: {:.2f}m", duration_s, mean_acc_m);
        leds::kStatusLeds.set_survey_in_complete(true);
        active_ = false;
        valid_ = true;
        position_shown_ = false;
      } else if (active && duration_s % 60 == 0) {
        spdlog::info("Survey in progress ({}s elapsed). Accuracy: {:.2f}m", duration_s, mean_acc_m);
      }
    } else if (cls == 0x01 && msgid == 0x07) {
      double latitude = double(int32_t(from_le_str(data.substr(28, 4)))) * 1e-7;
      double longitude = double(int32_t(from_le_str(data.substr(24, 4)))) * 1e-7;
      int32_t height_mm = int32_t(from_le_str(data.substr(32, 4)));
      if (valid_ && !position_shown_) {
        spdlog::info("Survey coordinates: {:.7f},{:.7f} @{:.2f}m", latitude, longitude, double(height_mm) / 1000.0);
        position_shown_ = true;
      }
    }
  }

  bool is_valid() { return valid_; }

private:
  bool active_ = false;
  std::atomic<bool> valid_ = false;
  bool position_shown_ = false;
};

} // namespace

int base_main() {
  spdlog::info("Running as BASE.");

  auto base_gps = std::make_shared<gps::GpsClient>(std::make_unique<gps::spi::GpsSpiCommunicator>("/dev/spidev0.0"));
  check_firmware_version("BASE", *base_gps);

  auto mov_base_gps = std::make_shared<gps::GpsClient>(
      std::make_unique<gps::udp_multicast::GpsUdpMulticastCommunicator>(kRtcmMulticastAddress, kRtcmMulticastPort));

  // enable PPS when there's no GPS signal
  gps::commands::set_config_value(*base_gps, gps::k_CFG_TP_LEN_TP1, to_le_str(100000, 4));

  // enable RTCM messages: 1005, 1074, 1084, 1094, 1124, 1230
  gps::commands::set_config_value(*base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE1005_SPI, {1});
  gps::commands::set_config_value(*base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE1074_SPI, {1});
  gps::commands::set_config_value(*base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE1084_SPI, {1});
  gps::commands::set_config_value(*base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE1094_SPI, {1});
  gps::commands::set_config_value(*base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE1124_SPI, {1});
  gps::commands::set_config_value(*base_gps, gps::k_CFG_MSGOUT_RTCM_3X_TYPE1230_SPI, {1});

  // enable UBX-NAV-PVT message
  gps::commands::set_config_value(*base_gps, gps::k_CFG_MSGOUT_UBX_NAV_PVT_SPI, {1});

  // disable NMEA messages
  gps::commands::set_config_value(*base_gps, gps::k_CFG_SPIOUTPROT_NMEA, {0});

  // 1hz measurement
  gps::commands::set_config_value(*base_gps, gps::k_CFG_RATE_MEAS, to_le_str(1000, 2));

  // use satellites with at least 20 dbHz signal for navigation
  gps::commands::set_config_value(*base_gps, gps::k_CFG_NAVSPG_INFIL_MINCNO, {20});

  // use satellites with at least 15 deg of elevation for surveying
  gps::commands::set_config_value(*base_gps, gps::k_CFG_NAVSPG_INFIL_MINELEV, {15});

  // enable 10 minute survey-in and 0.5m minimum accuracy, as well as status message
  gps::commands::set_config_value(*base_gps, gps::k_CFG_TMODE_MODE, gps::k_CFG_TMODE_MODE_SURVEY_IN);
  gps::commands::set_config_value(*base_gps, gps::k_CFG_TMODE_SVIN_MIN_DUR, to_le_str(1800, 4));
  gps::commands::set_config_value(*base_gps, gps::k_CFG_TMODE_SVIN_ACC_LIMIT, to_le_str(3000, 4));
  gps::commands::set_config_value(*base_gps, gps::k_CFG_MSGOUT_UBX_NAV_SVIN_SPI, {1});
  auto survey_handler = std::make_shared<SurveyInStatusHandler>();
  base_gps->add_handler(survey_handler);

  // transmit RTCM messages over radio to moving bases once survey is complete
  base_gps->add_handler(
      std::make_shared<RtcmForwardingHandler>(mov_base_gps, [&] { return survey_handler->is_valid(); }));

  // enable time sync
  auto ts_handler = std::make_shared<time::TimeSyncHandler>();
  base_gps->add_handler(ts_handler);
  auto pps_monitor = time::PpsMonitor("/dev/pps0", ts_handler);

  // sleep forever
  blink_running_led_forever();
  return 0;
}

} // namespace benjamin_gps
} // namespace drivers
} // namespace lib

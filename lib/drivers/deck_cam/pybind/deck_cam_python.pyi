from typing import List, Optional, Union

from config.client.cpp.config_client_python import ConfigTree
from cv.deck.deck_cv_python import DeckCV

class CameraType:
    STANDARD: "CameraType"
    RGBD: "CameraType"
    FISHEYE: "CameraType"
    LIDAR: "CameraType"

class Camera:
    def start(self) -> None: ...
    def stop(self) -> None: ...
    def set_config(self, name: str, value: Union[bool, int, float, str]) -> bool: ...
    def get_config_bool(self, name: str) -> bool: ...
    def get_config_double(self, name: str) -> float: ...
    def get_config_int(self, name: str) -> int: ...
    def get_config_str(self, name: str) -> str: ...
    def get_config_as_str(self, name: str) -> str: ...
    @property
    def name(self) -> str: ...
    @property
    def type(self) -> CameraType: ...
    @property
    def width(self) -> int: ...
    @property
    def height(self) -> int: ...
    @property
    def config(self) -> ConfigTree: ...

class ImgDirCamera(Camera):
    def __init__(self, tree: ConfigTree) -> None: ...

class VideoFileCamera(Camera):
    def __init__(self, tree: ConfigTree) -> None: ...

class LucidCamera(Camera):
    def __init__(self, tree: ConfigTree, cv: Optional[DeckCV]) -> None: ...

def get_cr_multi_cams(tree: ConfigTree, cv: Optional[DeckCV]) -> List[Camera]: ...

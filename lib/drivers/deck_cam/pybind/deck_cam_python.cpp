#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "lib/drivers/deck_cam/cpp/cr_multisense.hpp"

#include "lib/drivers/deck_cam/cpp/camera.hpp"
#include "lib/drivers/deck_cam/cpp/img_dir_camera.hpp"
#include "lib/drivers/deck_cam/cpp/lucid_camera.hpp"
#include "lib/drivers/deck_cam/cpp/video_file_camera.hpp"

#include <cv/deck/deck_cv.h>

namespace py = pybind11;

namespace carbon::deck_cam {
PYBIND11_MODULE(deck_cam_python, m) {
  py::enum_<CameraType>(m, "CameraType")
      .value("STANDARD", CameraType::STANDARD)
      .value("RGBD", CameraType::RGBD)
      .value("FISHEYE", CameraType::FISHEYE)
      .value("LIDAR", CameraType::LIDAR)
      .export_values();

  py::class_<Camera, std::shared_ptr<Camera>>(m, "Camera")
      .def("start", &Camera::start, py::call_guard<py::gil_scoped_release>())
      .def("stop", &Camera::stop, py::call_guard<py::gil_scoped_release>())
      .def("set_config", py::overload_cast<const std::string &, bool>(&Camera::set_config), py::arg("name"),
           py::arg("value"), py::call_guard<py::gil_scoped_release>())
      .def("set_config", py::overload_cast<const std::string &, double>(&Camera::set_config), py::arg("name"),
           py::arg("value"), py::call_guard<py::gil_scoped_release>())
      .def("set_config", py::overload_cast<const std::string &, int64_t>(&Camera::set_config), py::arg("name"),
           py::arg("value"), py::call_guard<py::gil_scoped_release>())
      .def("set_config", py::overload_cast<const std::string &, const std::string &>(&Camera::set_config),
           py::arg("name"), py::arg("value"), py::call_guard<py::gil_scoped_release>())
      .def("get_config_bool", &Camera::get_config_bool, py::arg("name"), py::call_guard<py::gil_scoped_release>())
      .def("get_config_double", &Camera::get_config_double, py::arg("name"), py::call_guard<py::gil_scoped_release>())
      .def("get_config_int", &Camera::get_config_int, py::arg("name"), py::call_guard<py::gil_scoped_release>())
      .def("get_config_str", &Camera::get_config_str, py::arg("name"), py::call_guard<py::gil_scoped_release>())
      .def("get_config_as_str", &Camera::get_config_as_str, py::arg("name"), py::call_guard<py::gil_scoped_release>())
      .def_property_readonly("name", &Camera::name)
      .def_property_readonly("type", &Camera::type)
      .def_property_readonly("connected", &Camera::connected)
      .def_property_readonly("width", &Camera::width)
      .def_property_readonly("height", &Camera::height)
      .def_property_readonly("config", &Camera::config);

  py::class_<ImgDirCamera, std::shared_ptr<ImgDirCamera>, Camera>(m, "ImgDirCamera")
      .def(py::init<std::shared_ptr<config::ConfigTree>>(), py::call_guard<py::gil_scoped_release>());

  py::class_<VideoFileCamera, std::shared_ptr<VideoFileCamera>, Camera>(m, "VideoFileCamera")
      .def(py::init<std::shared_ptr<config::ConfigTree>>(), py::call_guard<py::gil_scoped_release>());

  py::class_<LucidCamera, std::shared_ptr<LucidCamera>, Camera>(m, "LucidCamera")
      .def(py::init<std::shared_ptr<config::ConfigTree>, std::shared_ptr<cv::deck::DeckCV>>(),
           py::call_guard<py::gil_scoped_release>());

  m.def("get_cr_multi_cams", [](std::shared_ptr<config::ConfigTree> tree, std::shared_ptr<cv::deck::DeckCV> cv) {
    return CR_MultiSenseOwner::get().get_cameras(tree, cv);
  });
}

} // namespace carbon::deck_cam
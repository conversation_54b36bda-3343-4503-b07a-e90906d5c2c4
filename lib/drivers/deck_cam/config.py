import json
from dataclasses import InitVar, dataclass, field
from enum import IntEnum
from typing import Any, List, Optional, Union

from dataclass_wizard import JSON<PERSON><PERSON>rd

from lib.common.abstract.config.config import TreeNode
from lib.drivers.deck_cam.pybind.deck_cam_python import CameraType as _CameraType


class CameraType(IntEnum):
    STANDARD = _CameraType.STANDARD
    RGBD = _CameraType.RGBD
    FISHEYE = _CameraType.FISHEYE
    LIDAR = _CameraType.LIDAR


def is_enabled(tree: TreeNode) -> bool:
    return tree.get_node("enabled").get_bool_value()


@dataclass
class FisheyeConfig:
    k: List[List[float]]
    d: List[List[float]]

    @staticmethod
    def FromConfigSvc(tree: TreeNode, is_fisheye_required: bool = True) -> Optional["FisheyeConfig"]:
        if tree is None:
            return None
        if not is_fisheye_required or tree.get_node("is_fisheye").get_bool_value():
            return FisheyeConfig(
                k=json.loads(tree.get_node("k").get_string_value()), d=json.loads(tree.get_node("d").get_string_value())
            )
        return None


@dataclass
class Capture:
    persistence_rate: float = 0.0
    use_deck_cv: bool = False
    tree: InitVar[Optional[TreeNode]] = None

    def __post_init__(self, tree: Optional[TreeNode]) -> None:
        def callback() -> None:
            assert tree is not None
            self.persistence_rate = tree.get_node("persistence_rate").get_float_value()
            self.use_deck_cv = tree.get_node("use_deck_cv").get_bool_value()

        if tree is not None:
            callback()
            tree.register_callback(callback)


@dataclass
class CameraConfig:
    fisheye: Optional[FisheyeConfig] = None
    binning: Optional[int] = None
    # Width and height are post-binning
    width: Optional[int] = None
    height: Optional[int] = None
    cv_required: bool = False
    capture: Capture = field(default_factory=Capture)
    tree: InitVar[Optional[TreeNode]] = None

    def __post_init__(self, tree: Optional[TreeNode]) -> None:
        if tree is None:
            return
        self.fisheye = FisheyeConfig.FromConfigSvc(tree.get_node("fisheye"))
        if tree.get_node("binning").get_int_value() >= 0:
            self.binning = tree.get_node("binning").get_int_value()
        if tree.get_node("width").get_int_value() >= 0:
            self.width = tree.get_node("width").get_int_value()
        if tree.get_node("height").get_int_value() >= 0:
            self.height = tree.get_node("height").get_int_value()
        self.cv_required = tree.get_node("cv_required").get_bool_value()
        self.capture = Capture(tree=tree.get_node("capture"))

    @staticmethod
    def FromConfigSvc(tree: TreeNode) -> Optional["CameraConfig"]:
        raise NotImplementedError()


@dataclass
class RealSenseCameraConfig(CameraConfig):
    serial: Optional[str] = None

    def __post_init__(self, tree: Optional[TreeNode]) -> None:
        if tree is None:
            return
        super().__post_init__(tree)
        if tree.get_node("serial").get_string_value() != "":
            self.serial = tree.get_node("serial").get_string_value()

    @staticmethod
    def FromConfigSvc(tree: TreeNode) -> Optional["RealSenseCameraConfig"]:
        if not is_enabled(tree):
            return None
        return RealSenseCameraConfig(tree=tree)


@dataclass
class SICKCameraConfig(CameraConfig):
    ip: Optional[str] = None
    depth_min: float = 0
    depth_max: float = 20000
    phi_min: float = -35
    phi_max: float = 8
    phi_delta: float = 2
    theta_min: float = -45
    theta_max: float = 45
    theta_delta: float = 0.5

    def __post_init__(self, tree: Optional[TreeNode]) -> None:
        if tree is None:
            return
        super().__post_init__(tree)
        if tree.get_node("ip").get_string_value() != "":
            self.ip = tree.get_node("ip").get_string_value()
        self.depth_min = tree.get_node("depth_colorization/min_value").get_int_value()
        self.depth_max = tree.get_node("depth_colorization/max_value").get_int_value()
        self.phi_min = tree.get_node("lidar/phi_min").get_float_value()
        self.phi_max = tree.get_node("lidar/phi_max").get_float_value()
        self.phi_delta = tree.get_node("lidar/phi_delta").get_float_value()
        self.theta_min = tree.get_node("lidar/theta_min").get_float_value()
        self.theta_max = tree.get_node("lidar/theta_max").get_float_value()
        self.theta_delta = tree.get_node("lidar/theta_delta").get_float_value()

    @staticmethod
    def FromConfigSvc(tree: TreeNode) -> Optional["SICKCameraConfig"]:
        if not is_enabled(tree):
            return None
        return SICKCameraConfig(tree=tree)


@dataclass
class ZedXCameraConfig(CameraConfig):
    serial: Optional[int] = None
    depth_mode: str = "PERFORMANCE"
    resolution: str = "HD1080"
    depth_min: float = 1
    depth_max: float = 20

    def __post_init__(self, tree: Optional[TreeNode]) -> None:
        if tree is None:
            return
        super().__post_init__(tree)
        if tree.get_node("serial").get_string_value() != "":
            self.serial = int(tree.get_node("serial").get_string_value())
        self.depth_mode = tree.get_node("zed_depth_mode").get_string_value()
        self.resolution = tree.get_node("zed_resolution").get_string_value()
        self.depth_min = (tree.get_node("depth_colorization/min_value").get_int_value()) / 1000
        self.depth_max = (tree.get_node("depth_colorization/max_value").get_int_value()) / 1000

    @staticmethod
    def FromConfigSvc(tree: TreeNode) -> Optional["ZedXCameraConfig"]:
        if not is_enabled(tree):
            return None
        return ZedXCameraConfig(tree=tree)


@dataclass
class CR_MultiSenseConfig(CameraConfig):
    ip: Optional[str] = None
    sensor_types: List[str] = field(default_factory=list)

    def __post_init__(self, tree: Optional[TreeNode]) -> None:
        if tree is None:
            return
        super().__post_init__(tree)
        assert tree.get_node("ip").get_string_value() != ""
        self.ip = tree.get_node("ip").get_string_value()

        for node in tree.get_node("sensor_types").get_children_nodes():
            self.sensor_types.append(node.get_string_value())

    @staticmethod
    def FromConfigSvc(tree: TreeNode) -> Optional["CR_MultiSenseConfig"]:
        if not is_enabled(tree):
            return None
        return CR_MultiSenseConfig(tree=tree)


CamConfigType = Union[
    RealSenseCameraConfig, ZedXCameraConfig, SICKCameraConfig, CR_MultiSenseConfig,
]


@dataclass
class CamConfigNode(JSONWizard):
    description: str
    type: str
    access_mode: str
    value: Any = None
    min: Optional[float] = None
    max: Optional[float] = None
    options: List[str] = field(default_factory=list)

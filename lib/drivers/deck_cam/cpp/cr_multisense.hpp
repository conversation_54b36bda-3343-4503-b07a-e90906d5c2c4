#include <atomic>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_set>
#include <vector>

#include <lib/drivers/deck_cam/cpp/camera.hpp>

#include <opencv2/core/mat.hpp>
class ChannelWrapper;
namespace carbon::deck_cam {
class CR_MultiSenseOwner {
public:
  CR_MultiSenseOwner(const CR_MultiSenseOwner &) = delete;
  CR_MultiSenseOwner &operator=(const CR_MultiSenseOwner &) = delete;
  static CR_MultiSenseOwner &get();
  std::vector<std::shared_ptr<Camera>> get_cameras(std::shared_ptr<config::ConfigTree> tree,
                                                   std::shared_ptr<cv::deck::DeckCV> cv);

private:
  CR_MultiSenseOwner();
  std::unordered_set<std::string> cr_multi_cams_;
  std::mutex mut_;
};
} // namespace carbon::deck_cam
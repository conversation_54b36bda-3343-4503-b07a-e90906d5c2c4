#pragma once
#include <chrono>
#include <memory>
#include <optional>
#include <string>

#include <config/tree/cpp/config_tree.hpp>
#include <lib/drivers/deck_cam/cpp/fps_metric.hpp>

#include <opencv2/core/mat.hpp>

namespace cv::deck {
class DeckCV;
}
namespace carbon::deck_cam {
enum CameraType {
  STANDARD = 0,
  RGBD = 1,
  FISHEYE = 2,
  LIDAR = 3,
};

struct TimedImg {
  std::chrono::system_clock::time_point cap_time;
  cv::Mat img;
  TimedImg(cv::Mat _img) : cap_time(std::chrono::system_clock::now()), img(_img) {}
  TimedImg() {}
};
class Camera {
public:
  Camera(std::shared_ptr<config::ConfigTree> tree, CameraType type, std::shared_ptr<cv::deck::DeckCV> cv,
         std::optional<std::string> name_override = std::nullopt);

  virtual std::shared_ptr<TimedImg> get_next(std::chrono::system_clock::time_point last,
                                             std::chrono::milliseconds timeout) = 0;
  virtual uint32_t width() = 0;
  virtual uint32_t height() = 0;
  virtual void start() = 0;
  virtual void stop() = 0;

  virtual bool connected() { return true; }
  virtual bool set_config(const std::string &name, bool value);
  virtual bool set_config(const std::string &name, double value);
  virtual bool set_config(const std::string &name, int64_t value);
  virtual bool set_config(const std::string &name, const std::string &value);

  virtual bool get_config_bool(const std::string &name);
  virtual double get_config_double(const std::string &name);
  virtual int64_t get_config_int(const std::string &name);
  virtual std::string get_config_str(const std::string &name);
  virtual std::string get_config_as_str(const std::string &name);

  inline const std::string &name() const { return name_; }
  inline CameraType type() const { return type_; }
  inline std::shared_ptr<config::ConfigTree> config() const { return tree_; }

protected:
  const std::shared_ptr<config::ConfigTree> tree_;
  const std::string name_;
  const CameraType type_;
  const std::shared_ptr<cv::deck::DeckCV> deck_cv_;

  void push_img_to_cv(std::shared_ptr<TimedImg> img);
  void inc_frame();

private:
  FpsMetric fps_metric_;
};
} // namespace carbon::deck_cam
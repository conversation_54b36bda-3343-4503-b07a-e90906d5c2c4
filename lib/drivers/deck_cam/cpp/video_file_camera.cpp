#include "lib/drivers/deck_cam/cpp/video_file_camera.hpp"

#include <cmath>
#include <opencv2/imgproc.hpp>
#include <stdexcept>

#include <fmt/format.h>
#include <spdlog/spdlog.h>
namespace carbon::deck_cam {
VideoFileCamera::VideoFileCamera(std::shared_ptr<config::ConfigTree> tree)
    : Camera(tree, CameraType::STANDARD, nullptr), device_(tree->get_node("path")->get_value<std::string>()),
      running_(false) {
  if (!device_.isOpened()) {
    throw std::runtime_error(
        fmt::format("Camera {} failed to open video file {}", name_, tree->get_node("path")->get_value<std::string>()));
  }
  width_ = uint32_t(std::round(device_.get(cv::CAP_PROP_FRAME_WIDTH)));
  height_ = uint32_t(std::round(device_.get(cv::CAP_PROP_FRAME_HEIGHT)));
  double fps = device_.get(cv::CAP_PROP_FPS);
  delta_ = std::chrono::milliseconds(uint32_t(std::round(1000.0 / fps)));
}

std::shared_ptr<TimedImg> VideoFileCamera::get_next(std::chrono::system_clock::time_point last,
                                                    std::chrono::milliseconds timeout) {
  auto test = [&] { return this->next_ && this->next_->cap_time > last; };
  std::unique_lock<std::mutex> lk(mut_);
  if (test()) {
    return next_;
  }
  this->cv_.wait_for(lk, timeout, test);
  if (test()) {
    return next_;
  }
  spdlog::warn("{} failed to get next image", name_);
  return nullptr;
}
uint32_t VideoFileCamera::width() { return width_; }
uint32_t VideoFileCamera::height() { return height_; }
void VideoFileCamera::start() {
  if (running_.exchange(true)) {
    return;
  }
  thread_ = std::thread(std::bind(&VideoFileCamera::worker, this));
}
void VideoFileCamera::stop() {
  if (!running_.exchange(false)) {
    return;
  }
  thread_.join();
}
void VideoFileCamera::worker() {
  while (running_) {
    cv::Mat frame;
    if (!device_.read(frame)) {
      spdlog::info("{} reseting file back to start", name_);
      device_.set(cv::CAP_PROP_POS_FRAMES, 0);
      continue;
    }
    cv::Mat corrected;
    cv::cvtColor(frame, corrected, cv::COLOR_BGR2RGB);
    {
      std::unique_lock<std::mutex> lk(mut_);
      next_ = std::make_shared<TimedImg>(corrected);
      cv_.notify_all();
    }
    inc_frame();
    std::this_thread::sleep_for(delta_);
  }
}
} // namespace carbon::deck_cam
#include "lib/drivers/deck_cam/cpp/cr_multisense.hpp"

#include <condition_variable>
#include <functional>
#include <set>
#include <stdexcept>
#include <thread>
#include <vector>

#include <MultiSense/MultiSenseChannel.hh>
#include <MultiSense/MultiSenseTypes.hh>
#include <MultiSense/MultiSenseUtilities.hh>
#include <opencv2/imgproc.hpp>
#include <spdlog/spdlog.h>

#include "config/tree/cpp/config_atomic_accessor.hpp"

#define GET_CHILD_AS(cfg, name, type) cfg->get_node(name)->get_value<type>()
#define GET_CHILD_BOOL(cfg, name) GET_CHILD_AS(cfg, name, bool)
#define GET_CHILD_FLOAT(cfg, name) GET_CHILD_AS(cfg, name, float)
#define GET_CHILD_UINT(cfg, name) GET_CHILD_AS(cfg, name, uint32_t)
#define GET_CHILD_UINTS(cfg, name) (uint16_t) GET_CHILD_AS(cfg, name, uint32_t)
#define GET_CHILD_STR(cfg, name) GET_CHILD_AS(cfg, name, std::string)
namespace carbon::deck_cam {
typedef cv::Point3_<uint8_t> ColorPixel;
struct ImgData {
  std::mutex mut;
  std::condition_variable cv;
  std::shared_ptr<multisense::ImageFrame> frame;
};

class CR_MultiSenseCam {
public:
  CR_MultiSenseCam(std::shared_ptr<config::ConfigTree> tree)
      : width_(GET_CHILD_UINT(tree, "width")), height_(GET_CHILD_UINT(tree, "height")),
        channel_(multisense::Channel::create(multisense::Channel::Config{GET_CHILD_STR(tree, "ip")})),
        streaming_(false) {
    auto cfg = channel_->get_config();
    spdlog::info("Have cfg for '{}'", GET_CHILD_STR(tree, "ip"));
    cfg.width = width_;
    cfg.height = height_;
    cfg.frames_per_second = GET_CHILD_FLOAT(tree, "frame_rate");
    cfg.stereo_config.postfilter_strength = GET_CHILD_FLOAT(tree, "stereo_confidence_filter");

    auto img_cfg = tree->get_child("stereo_img_config");
    cfg.image_config.auto_exposure_enabled = true;
    cfg.image_config.gamma = GET_CHILD_FLOAT(img_cfg, "gamma");

    cfg.image_config.auto_exposure = multisense::MultiSenseConfig::AutoExposureConfig();
    auto auto_exposure = img_cfg->get_child("auto_exposure");
    cfg.image_config.auto_exposure->max_exposure_time =
        std::chrono::microseconds(GET_CHILD_UINT(auto_exposure, "max_exposure_time"));
    cfg.image_config.auto_exposure->decay = GET_CHILD_UINT(auto_exposure, "decay");
    cfg.image_config.auto_exposure->target_intensity = GET_CHILD_FLOAT(auto_exposure, "target_intensity");
    cfg.image_config.auto_exposure->target_threshold = GET_CHILD_FLOAT(auto_exposure, "target_threshold");
    cfg.image_config.auto_exposure->max_gain = GET_CHILD_FLOAT(auto_exposure, "max_gain");
    auto roi = auto_exposure->get_node("roi");
    cfg.image_config.auto_exposure->roi.top_left_x_position = GET_CHILD_UINTS(roi, "start_x");
    cfg.image_config.auto_exposure->roi.top_left_y_position = GET_CHILD_UINTS(roi, "start_y");
    cfg.image_config.auto_exposure->roi.width = GET_CHILD_UINTS(roi, "width");
    cfg.image_config.auto_exposure->roi.height = GET_CHILD_UINTS(roi, "height");
    if (!cfg.aux_config.has_value()) {
      cfg.aux_config = multisense::MultiSenseConfig::AuxConfig();
    }
    img_cfg = tree->get_child("aux_img_config");
    cfg.aux_config->image_config.auto_exposure_enabled = true;
    cfg.aux_config->image_config.auto_white_balance_enabled = true;
    cfg.aux_config->image_config.gamma = GET_CHILD_FLOAT(img_cfg, "gamma");
    if (!cfg.aux_config->image_config.auto_exposure.has_value()) {
      cfg.aux_config->image_config.auto_exposure = multisense::MultiSenseConfig::AutoExposureConfig();
    }
    auto_exposure = img_cfg->get_child("auto_exposure");
    cfg.aux_config->image_config.auto_exposure->max_exposure_time =
        std::chrono::microseconds(GET_CHILD_UINT(auto_exposure, "max_exposure_time"));
    cfg.aux_config->image_config.auto_exposure->decay = GET_CHILD_UINT(auto_exposure, "decay");
    cfg.aux_config->image_config.auto_exposure->target_intensity = GET_CHILD_FLOAT(auto_exposure, "target_intensity");
    cfg.aux_config->image_config.auto_exposure->target_threshold = GET_CHILD_FLOAT(auto_exposure, "target_threshold");
    cfg.aux_config->image_config.auto_exposure->max_gain = GET_CHILD_FLOAT(auto_exposure, "max_gain");
    roi = auto_exposure->get_node("roi");
    cfg.aux_config->image_config.auto_exposure->roi.top_left_x_position = GET_CHILD_UINTS(roi, "start_x");
    cfg.aux_config->image_config.auto_exposure->roi.top_left_y_position = GET_CHILD_UINTS(roi, "start_y");
    cfg.aux_config->image_config.auto_exposure->roi.width = GET_CHILD_UINTS(roi, "width");
    cfg.aux_config->image_config.auto_exposure->roi.height = GET_CHILD_UINTS(roi, "height");
    auto auto_wb = img_cfg->get_child("auto_white_balance");
    if (!cfg.aux_config->image_config.auto_white_balance.has_value()) {
      cfg.aux_config->image_config.auto_white_balance = multisense::MultiSenseConfig::AutoWhiteBalanceConfig();
    }
    cfg.aux_config->image_config.auto_white_balance->decay = GET_CHILD_UINT(auto_wb, "decay");
    cfg.aux_config->image_config.auto_white_balance->threshold = GET_CHILD_FLOAT(auto_wb, "threshold");
    auto status = channel_->set_config(cfg);
    if (multisense::Status::OK != status) {
      spdlog::error("Failed to set image config: status = {}", multisense::to_string(status));
      throw std::runtime_error("failed to set image config");
    }
  }
  inline bool add_left_mono_raw() { return add_source(multisense::DataSource::LEFT_MONO_RAW); }
  inline bool add_right_mono_raw() { return add_source(multisense::DataSource::RIGHT_MONO_RAW); }
  inline bool add_left_rectified_raw() { return add_source(multisense::DataSource::LEFT_RECTIFIED_RAW); }
  inline bool add_right_rectified_raw() { return add_source(multisense::DataSource::RIGHT_RECTIFIED_RAW); }
  inline bool add_left_disparity_raw() { return add_source(multisense::DataSource::LEFT_DISPARITY_RAW); }
  inline bool add_aux_luma_raw() { return add_source(multisense::DataSource::AUX_LUMA_RAW); }
  inline bool add_aux_luma_rectified_raw() { return add_source(multisense::DataSource::AUX_LUMA_RECTIFIED_RAW); }
  inline bool add_aux_chroma_raw() { return add_source(multisense::DataSource::AUX_CHROMA_RAW); }
  inline bool add_aux_chroma_rectified_raw() { return add_source(multisense::DataSource::AUX_CHROMA_RECTIFIED_RAW); }
  inline bool add_aux_raw() { return add_source(multisense::DataSource::AUX_RAW); }
  inline bool add_aux_rectified_raw() { return add_source(multisense::DataSource::AUX_RECTIFIED_RAW); }
  inline bool add_source(multisense::DataSource source_id) {
    std::lock_guard lg(stream_lock_);
    if (streaming_) {
      return false;
    }
    sources_.emplace(source_id);
    return true;
  }
  void start() {
    std::lock_guard lg(stream_lock_);
    if (streaming_) {
      return;
    }
    channel_->add_image_frame_callback(std::bind(&CR_MultiSenseCam::image_callback, this, std::placeholders::_1));
    auto status = channel_->start_streams(sources());
    if (multisense::Status::OK != status) {
      spdlog::error("unable to add isolated callbacks and start image streams");
    } else {
      streaming_ = true;
    }
  }
  void stop() {
    std::lock_guard lg(stream_lock_);
    if (!streaming_) {
      return;
    }
    channel_->stop_streams(sources());
    streaming_ = false;
    img_data_.cv.notify_all();
  }
  inline uint32_t width() const { return width_; }
  inline uint32_t height() const { return height_; }

  std::shared_ptr<multisense::ImageFrame> get_next_frame(std::shared_ptr<multisense::ImageFrame> prev,
                                                         std::chrono::milliseconds timeout) {
    int64_t prev_id = -1000000;
    if (prev) {
      prev_id = prev->frame_id;
    }
    auto test = [&] { return this->img_data_.frame && this->img_data_.frame->frame_id > prev_id; };
    std::unique_lock<std::mutex> lk(img_data_.mut);
    if (!test()) {
      img_data_.cv.wait_for(lk, timeout, test);
    }
    return img_data_.frame;
  }

private:
  const uint32_t width_;
  const uint32_t height_;
  std::set<multisense::DataSource> sources_;
  std::unique_ptr<multisense::Channel> channel_;
  std::mutex stream_lock_;
  bool streaming_;
  bool configured_;
  ImgData img_data_;

  inline std::vector<multisense::DataSource> sources() const {
    return std::vector<multisense::DataSource>(sources_.begin(), sources_.end());
  }
  void image_callback(const multisense::ImageFrame &frame) {
    std::shared_ptr<multisense::ImageFrame> next = std::make_shared<multisense::ImageFrame>(frame);
    std::lock_guard lk(img_data_.mut);
    img_data_.frame.swap(next);
    img_data_.cv.notify_all();
  }
};
class SingleCamFromMultiSensor : public Camera {
public:
  SingleCamFromMultiSensor(std::shared_ptr<CR_MultiSenseCam> handle, std::shared_ptr<config::ConfigTree> tree,
                           CameraType type, std::shared_ptr<cv::deck::DeckCV> cv, const std::string &name_suffix)
      : Camera(tree, type, cv, fmt::format("{}_{}", tree->get_name(), name_suffix)), handle_(handle),
        next_img_(nullptr), run_(false) {}
  virtual void start() override {
    handle_->start();
    if (run_) {
      return;
    }
    run_ = true;
    thread_ = std::thread(std::bind(&SingleCamFromMultiSensor::worker, this));
  }
  virtual void stop() override {
    handle_->stop();
    run_ = false;
    thread_.join();
  }
  virtual uint32_t width() override { return handle_->width(); }
  virtual uint32_t height() override { return handle_->height(); }
  virtual std::shared_ptr<TimedImg> get_next(std::chrono::system_clock::time_point last,
                                             std::chrono::milliseconds timeout) override {

    auto test = [&] { return this->next_img_ && this->next_img_->cap_time > last; };
    std::unique_lock<std::mutex> lk(this->mut_);
    if (!test()) {
      this->cv_.wait_for(lk, timeout, test);
    }
    if (test()) {
      return next_img_;
    }
    spdlog::warn("{} failed to get next image", name_);
    return nullptr;
  }

protected:
  std::shared_ptr<CR_MultiSenseCam> handle_;
  std::thread thread_;
  std::mutex mut_;
  std::condition_variable cv_;
  std::shared_ptr<TimedImg> next_img_;
  bool run_;
  virtual void handler(std::shared_ptr<multisense::ImageFrame> frame) = 0;

private:
  void worker() {
    std::shared_ptr<multisense::ImageFrame> frame = nullptr;
    while (run_) {
      frame = handle_->get_next_frame(frame, std::chrono::milliseconds(1000));
      if (!frame) {
        continue;
      }
      handler(frame);
      inc_frame();
    }
  }
};
cv::Mat find_edges(cv::Mat grayscale) {
  cv::Mat blurry, edgey, colory;
  cv::GaussianBlur(grayscale, blurry, cv::Size(3, 3), 0);
  cv::Sobel(blurry, edgey, CV_8U, 1, 1, 5);
  cvtColor(edgey, colory, cv::COLOR_GRAY2RGB);
  return colory;
}
class EdgeCam : public SingleCamFromMultiSensor {
public:
  EdgeCam(multisense::DataSource source, std::shared_ptr<CR_MultiSenseCam> handle,
          std::shared_ptr<config::ConfigTree> tree, std::shared_ptr<cv::deck::DeckCV> cv,
          const std::string &name_suffix)
      : SingleCamFromMultiSensor(handle, tree, CameraType::STANDARD, cv, name_suffix), source_(source) {
    handle_->add_source(source_);
  }

protected:
  multisense::DataSource source_;
  virtual void handler(std::shared_ptr<multisense::ImageFrame> frame) override {
    auto img_color = std::make_shared<TimedImg>(find_edges(frame->get_image(source_).cv_mat()));
    {
      std::lock_guard<std::mutex> lk(mut_);
      next_img_.swap(img_color);
      cv_.notify_all();
    }
  }
};
cv::Mat make_colorized_depth(const multisense::Image &mm_depth, uint32_t width, uint32_t height) {
  auto cv_depth = mm_depth.cv_mat();
  cv::Mat_<uint8_t> mono_depth(height, width);

  for (size_t r = 0; r < height; ++r) {
    for (size_t c = 0; c < width; ++c) {
      mono_depth.at<uint8_t>(static_cast<int>(r), static_cast<int>(c)) =
          static_cast<uint8_t>((cv_depth.at<uint16_t>(static_cast<int>(r), static_cast<int>(c)) % (256 * 8)) / 8);
    }
  }
  cv::Mat depth_color;
  cv::applyColorMap(mono_depth, depth_color, cv::COLORMAP_JET);
  return depth_color;
}
cv::Mat find_depth_lines(const multisense::Image &mm_depth, uint16_t start_mm, uint16_t delta_mm, uint16_t count) {
  auto cv_depth = mm_depth.cv_mat().clone(); // Need deep copy to modify
  cv_depth.forEach<uint16_t>([&](uint16_t &pixel, const int *position) {
    (void)position;
    uint16_t curr = start_mm;
    for (uint16_t i = 0; i < count; ++i) {
      if (pixel < curr) {
        pixel = curr;
        return;
      }
      curr = static_cast<uint16_t>(curr + delta_mm);
    }
    pixel = curr;
  });
  cv::Mat edgey;
  cv::Sobel(cv_depth, edgey, CV_32F, 1, 1, 5);
  return edgey;
}
class DepthCam : public SingleCamFromMultiSensor {
protected:
  virtual void handler(std::shared_ptr<multisense::ImageFrame> frame) override {
    auto mm_depth = multisense::create_depth_image(*frame, multisense::Image::PixelFormat::MONO16,
                                                   multisense::DataSource::LEFT_DISPARITY_RAW, 65535);
    if (!mm_depth) {
      return;
    }
    auto img_color = std::make_shared<TimedImg>(make_colorized_depth(mm_depth.value(), width(), height()));
    {
      std::lock_guard<std::mutex> lk(mut_);
      next_img_.swap(img_color);
      cv_.notify_all();
    }
  }

public:
  DepthCam(std::shared_ptr<CR_MultiSenseCam> handle, std::shared_ptr<config::ConfigTree> tree,
           std::shared_ptr<cv::deck::DeckCV> cv)
      : SingleCamFromMultiSensor(handle, tree, CameraType::STANDARD, cv, "depth") {
    handle_->add_left_disparity_raw();
  }
};

class EdgeDepthCam : public SingleCamFromMultiSensor {
public:
  EdgeDepthCam(std::shared_ptr<CR_MultiSenseCam> handle, std::shared_ptr<config::ConfigTree> tree,
               std::shared_ptr<cv::deck::DeckCV> cv)
      : SingleCamFromMultiSensor(handle, tree, CameraType::STANDARD, cv, "edge_depth") {
    handle_->add_left_disparity_raw();
    handle_->add_left_rectified_raw();
  }

protected:
  virtual void handler(std::shared_ptr<multisense::ImageFrame> frame) override {
    auto mm_depth = multisense::create_depth_image(*frame, multisense::Image::PixelFormat::MONO16,
                                                   multisense::DataSource::LEFT_DISPARITY_RAW, 65535);
    if (!mm_depth) {
      return;
    }
    cv::Mat out_img;
    cv::addWeighted(make_colorized_depth(mm_depth.value(), width(), height()), 0.5,
                    find_edges(frame->get_image(multisense::DataSource::LEFT_RECTIFIED_RAW).cv_mat()), 0.5, 0.0,
                    out_img);
    auto img_color = std::make_shared<TimedImg>(out_img);
    {
      std::lock_guard<std::mutex> lk(mut_);
      next_img_.swap(img_color);
      cv_.notify_all();
    }
  }
};

class AuxCam : public SingleCamFromMultiSensor {
public:
  AuxCam(std::shared_ptr<CR_MultiSenseCam> handle, std::shared_ptr<config::ConfigTree> tree,
         std::shared_ptr<cv::deck::DeckCV> cv)
      : SingleCamFromMultiSensor(handle, tree, CameraType::STANDARD, cv, "aux") {
    handle_->add_aux_raw();
  }
  virtual uint32_t height() override {
    return SingleCamFromMultiSensor::height() - 6;
  } // Not sure why this gets cutoff but it does

protected:
  virtual void handler(std::shared_ptr<multisense::ImageFrame> frame) override {
    auto bgr = create_bgr_image(*frame, multisense::DataSource::AUX_RAW);
    if (!bgr) {
      return;
    }
    cv::Mat rgb;
    cvtColor(bgr->cv_mat(), rgb, cv::COLOR_BGR2RGB);
    auto img_color = std::make_shared<TimedImg>(rgb);
    {
      std::lock_guard<std::mutex> lk(mut_);
      next_img_.swap(img_color);
      cv_.notify_all();
    }
  }
};
class AuxDepthCam : public SingleCamFromMultiSensor {
private:
  config::ConfigAtomicAccessor<float> threshold_;
  config::ConfigAtomicAccessor<uint32_t> start_mm_;
  config::ConfigAtomicAccessor<uint32_t> delta_mm_;
  config::ConfigAtomicAccessor<uint32_t> count_;

public:
  AuxDepthCam(std::shared_ptr<CR_MultiSenseCam> handle, std::shared_ptr<config::ConfigTree> tree,
              std::shared_ptr<cv::deck::DeckCV> cv)
      : SingleCamFromMultiSensor(handle, tree, CameraType::STANDARD, cv, "aux_depth"),
        threshold_(tree->get_node("aux_depth_lines/edge_threshold")),
        start_mm_(tree->get_node("aux_depth_lines/start_dist")),
        delta_mm_(tree->get_node("aux_depth_lines/delta_dist")), count_(tree->get_node("aux_depth_lines/count")) {
    handle_->add_left_disparity_raw();
    handle_->add_aux_rectified_raw();
  }
  virtual uint32_t height() override {
    return SingleCamFromMultiSensor::height() - 6;
  } // Not sure why this gets cutoff but it does

protected:
  virtual void handler(std::shared_ptr<multisense::ImageFrame> frame) override {
    auto mm_depth = multisense::create_depth_image(*frame, multisense::Image::PixelFormat::MONO16,
                                                   multisense::DataSource::LEFT_DISPARITY_RAW, 65535);
    if (!mm_depth) {
      return;
    }
    auto tmp = create_bgr_image(*frame, multisense::DataSource::AUX_RECTIFIED_RAW);
    if (!tmp) {
      return;
    }
    auto tmp_cv = tmp->cv_mat().clone(); // Need copy so we can modify
    cv::Mat lines =
        find_depth_lines(mm_depth.value(), static_cast<uint16_t>(start_mm_.get_value()),
                         static_cast<uint16_t>(delta_mm_.get_value()), static_cast<uint16_t>(count_.get_value()));
    auto threshold = threshold_.get_value();
    // Since we need to loop through all pixels anyways do BGR -> RGB here instead of using cvtColor
    tmp_cv.forEach<ColorPixel>([&](ColorPixel &pixel, const int position[]) -> void {
      if (lines.at<float>(position[0], position[1]) > threshold) {
        pixel.x = 255;
        pixel.y = 0;
        pixel.z = 0;
      } else {
        // BGR -> RGB
        auto orig = pixel.x;
        pixel.x = pixel.z;
        pixel.z = orig;
      }
    });
    auto img_color = std::make_shared<TimedImg>(tmp_cv);
    {
      std::lock_guard<std::mutex> lk(mut_);
      next_img_.swap(img_color);
      cv_.notify_all();
    }
  }
};

CR_MultiSenseOwner::CR_MultiSenseOwner() {}
CR_MultiSenseOwner &CR_MultiSenseOwner::get() {
  static CR_MultiSenseOwner owner;
  return owner;
}
std::vector<std::shared_ptr<Camera>> CR_MultiSenseOwner::get_cameras(std::shared_ptr<config::ConfigTree> tree,
                                                                     std::shared_ptr<cv::deck::DeckCV> cv) {
  std::string ip = GET_CHILD_STR(tree, "ip");
  std::lock_guard lk(mut_);
  auto it = cr_multi_cams_.find(ip);
  if (it != cr_multi_cams_.end()) {
    throw std::runtime_error(fmt::format("MultiSensor with ip={}, has already been used", ip));
  }
  cr_multi_cams_.emplace(ip);

  auto enabled_cfg = tree->get_node("cameras");
  bool depth = GET_CHILD_BOOL(enabled_cfg, "depth");
  bool edge = GET_CHILD_BOOL(enabled_cfg, "edge");
  bool edge_depth = GET_CHILD_BOOL(enabled_cfg, "edge_depth");
  bool aux = GET_CHILD_BOOL(enabled_cfg, "aux");
  bool aux_depth = GET_CHILD_BOOL(enabled_cfg, "aux_depth");
  if (!(depth || edge || edge_depth || aux || aux_depth)) {
    // No cameras enabled for this sensor
    return {};
  }

  std::shared_ptr<CR_MultiSenseCam> multi_sensor = std::make_shared<CR_MultiSenseCam>(tree);
  std::vector<std::shared_ptr<Camera>> cams;

  if (depth) {
    cams.emplace_back(std::make_shared<DepthCam>(multi_sensor, tree, cv));
  }
  if (edge) {
    cams.emplace_back(
        std::make_shared<EdgeCam>(multisense::DataSource::LEFT_RECTIFIED_RAW, multi_sensor, tree, cv, "edge"));
  }
  if (edge_depth) {
    cams.emplace_back(std::make_shared<EdgeDepthCam>(multi_sensor, tree, cv));
  }
  if (aux) {
    cams.emplace_back(std::make_shared<AuxCam>(multi_sensor, tree, cv));
  }
  if (aux_depth) {
    cams.emplace_back(std::make_shared<AuxDepthCam>(multi_sensor, tree, cv));
  }
  return cams;
}

} // namespace carbon::deck_cam
#pragma once
#include "lib/drivers/deck_cam/cpp/camera.hpp"

#include <atomic>
#include <condition_variable>
#include <mutex>
#include <thread>

#include <opencv2/videoio.hpp>

namespace carbon::deck_cam {
class VideoFileCamera : public Camera {
public:
  VideoFileCamera(std::shared_ptr<config::ConfigTree> tree);
  virtual std::shared_ptr<TimedImg> get_next(std::chrono::system_clock::time_point last,
                                             std::chrono::milliseconds timeout) override;
  virtual uint32_t width() override;
  virtual uint32_t height() override;
  virtual void start() override;
  virtual void stop() override;

private:
  cv::VideoCapture device_;
  uint32_t width_;
  uint32_t height_;
  std::chrono::milliseconds delta_;
  std::atomic_bool running_;
  std::mutex mut_;
  std::condition_variable cv_;
  std::shared_ptr<TimedImg> next_;

  std::thread thread_;
  void worker();
};
} // namespace carbon::deck_cam
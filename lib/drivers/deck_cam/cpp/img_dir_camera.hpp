#pragma once
#include "lib/drivers/deck_cam/cpp/camera.hpp"

#include <atomic>
#include <condition_variable>
#include <mutex>
#include <thread>
#include <vector>

namespace carbon::deck_cam {
class ImgDirCamera : public Camera {
public:
  ImgDirCamera(std::shared_ptr<config::ConfigTree> tree);
  virtual std::shared_ptr<TimedImg> get_next(std::chrono::system_clock::time_point last,
                                             std::chrono::milliseconds timeout) override;
  virtual uint32_t width() override;
  virtual uint32_t height() override;
  virtual void start() override;
  virtual void stop() override;

private:
  uint32_t width_;
  uint32_t height_;
  std::chrono::milliseconds delta_;
  std::atomic_bool running_;
  std::mutex mut_;
  std::condition_variable cv_;
  std::shared_ptr<TimedImg> next_;
  std::vector<cv::Mat> imgs_;

  std::thread thread_;
  void worker();
};
} // namespace carbon::deck_cam
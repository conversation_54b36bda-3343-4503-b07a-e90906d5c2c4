#include "lib/drivers/deck_cam/cpp/img_dir_camera.hpp"

#include <cmath>
#include <filesystem>
#include <stdexcept>

#include <fmt/format.h>
#include <opencv2/imgcodecs.hpp>
#include <opencv2/imgproc.hpp>
#include <spdlog/spdlog.h>
namespace carbon::deck_cam {
CameraType get_type_from_cfg(std::shared_ptr<config::ConfigTree> tree) {
  auto fisheye_node = tree->get_node("fisheye");
  if (!fisheye_node) {
    return CameraType::STANDARD;
  }
  if (fisheye_node->get_node("is_fisheye")->get_value<bool>()) {
    return CameraType::FISHEYE;
  } else {
    return CameraType::STANDARD;
  }
}

ImgDirCamera::ImgDirCamera(std::shared_ptr<config::ConfigTree> tree)
    : Camera(tree, get_type_from_cfg(tree), nullptr), running_(false) {
  for (auto const &dir_entry :
       std::filesystem::directory_iterator(std::filesystem::path(tree_->get_node("path")->get_value<std::string>()))) {
    if (dir_entry.path().has_filename()) {
      imgs_.emplace_back(cv::imread(dir_entry.path(), cv::IMREAD_COLOR));
    }
  }
  spdlog::info("{} contains {} images.", name_, imgs_.size());

  // Just assume all images are same size for now this is only a test cam so
  width_ = uint32_t(imgs_[0].cols);
  height_ = uint32_t(imgs_[0].rows);
  delta_ =
      std::chrono::milliseconds(uint32_t(std::round(1000.0 / double(tree_->get_node("fps")->get_value<uint32_t>()))));
}

std::shared_ptr<TimedImg> ImgDirCamera::get_next(std::chrono::system_clock::time_point last,
                                                 std::chrono::milliseconds timeout) {
  auto test = [&] { return this->next_ && this->next_->cap_time > last; };
  std::unique_lock<std::mutex> lk(mut_);
  if (test()) {
    return next_;
  }
  this->cv_.wait_for(lk, timeout, test);
  if (test()) {
    return next_;
  }
  spdlog::warn("{} failed to get next image", name_);
  return nullptr;
}
uint32_t ImgDirCamera::width() { return width_; }
uint32_t ImgDirCamera::height() { return height_; }
void ImgDirCamera::start() {
  if (running_.exchange(true)) {
    return;
  }
  thread_ = std::thread(std::bind(&ImgDirCamera::worker, this));
}
void ImgDirCamera::stop() {
  if (!running_.exchange(false)) {
    return;
  }
  thread_.join();
}
void ImgDirCamera::worker() {
  size_t ind = 0;
  while (running_) {
    {
      std::unique_lock<std::mutex> lk(mut_);
      next_ = std::make_shared<TimedImg>(imgs_[ind]);
      cv_.notify_all();
    }
    inc_frame();
    ind = (ind + 1) % imgs_.size();
    std::this_thread::sleep_for(delta_);
  }
}
} // namespace carbon::deck_cam
#include "lib/drivers/deck_cam/cpp/camera.hpp"

#include <chrono>
#include <mutex>
#include <stdexcept>

#include <config/client/cpp/config_subscriber.hpp>
#include <cv/deck/deck_cv.h>
#include <hardware_manager/cpp/grpc_client.h>
#include <lib/common/camera/cpp/camera_image.h>

#include <fmt/format.h>
#include <spdlog/spdlog.h>

constexpr int64_t max_gps_age_ms(3000);
namespace carbon::deck_cam {
class GpsFetcher {
public:
  static GpsFetcher &get() {
    static GpsFetcher inst;
    return inst;
  }
  lib::common::GeoLLAData get_geo(int64_t timestamp_ms);

private:
  GpsFetcher() : hwc_(config::make_robot_local_addr(61006)), cur_(0.0, 0.0, 0.0, 0) {}
  GpsFetcher(const GpsFetcher &) = delete;
  GpsFetcher &operator=(const GpsFetcher &) = delete;
  hardware_manager::HardwareManagerClient hwc_;
  lib::common::GeoLLAData cur_;
  std::mutex mut_;
};
lib::common::GeoLLAData GpsFetcher::get_geo(int64_t timestamp_ms) {
  std::unique_lock<std::mutex> lk(mut_);
  if ((timestamp_ms - cur_.get_timestamp_ms()) < max_gps_age_ms) {
    return cur_;
  }
  try {
    auto geo = hwc_.get_gps_data();
    cur_ = lib::common::GeoLLAData(geo.lla().lat(), geo.lla().lng(), geo.lla().alt(), geo.lla().timestamp_ms());
  } catch (std::exception &ex) {
    spdlog::warn("Failed to update gps pos data {}", ex.what());
  }
  return cur_;
}

Camera::Camera(std::shared_ptr<config::ConfigTree> tree, CameraType type, std::shared_ptr<cv::deck::DeckCV> cv,
               std::optional<std::string> name_override)
    : tree_(tree), name_(name_override.value_or(tree_->get_name())), type_(type), deck_cv_(cv), fps_metric_(name_) {}

void Camera::inc_frame() { fps_metric_.inc(); }
bool Camera::set_config(const std::string &name, bool value) {
  (void)name;
  (void)value;
  spdlog::warn("Set config not defined for camera {}", name_);
  return false;
}
bool Camera::set_config(const std::string &name, double value) {
  (void)name;
  (void)value;
  spdlog::warn("Set config not defined for camera {}", name_);
  return false;
}
bool Camera::set_config(const std::string &name, int64_t value) {
  (void)name;
  (void)value;
  spdlog::warn("Set config not defined for camera {}", name_);
  return false;
}
bool Camera::set_config(const std::string &name, const std::string &value) {
  (void)name;
  (void)value;
  spdlog::warn("Set config not defined for camera {}", name_);
  return false;
}
bool Camera::get_config_bool(const std::string &name) {
  (void)name;
  throw std::runtime_error(fmt::format("{} does not support get config", name_));
}
double Camera::get_config_double(const std::string &name) {
  (void)name;
  throw std::runtime_error(fmt::format("{} does not support get config", name_));
}
int64_t Camera::get_config_int(const std::string &name) {
  (void)name;
  throw std::runtime_error(fmt::format("{} does not support get config", name_));
}
std::string Camera::get_config_str(const std::string &name) {
  (void)name;
  throw std::runtime_error(fmt::format("{} does not support get config", name_));
}
std::string Camera::get_config_as_str(const std::string &name) {
  (void)name;
  throw std::runtime_error(fmt::format("{} does not support get config", name_));
}
void Camera::push_img_to_cv(std::shared_ptr<TimedImg> img) {
  if (!deck_cv_ || !img) {
    return;
  }
  if (img->img.type() != CV_8UC3) {
    spdlog::warn("Invalid img format from {} cannot push to cv", name_);
    return;
  }
  lib::common::camera::CameraImage cv_img;
  cv_img.camera_id = name_;
  cv_img.timestamp_ms = std::chrono::duration_cast<std::chrono::milliseconds>(img->cap_time.time_since_epoch()).count();
  cv_img.image =
      torch::from_blob(img->img.data, {img->img.rows, img->img.cols, 3}, torch::kUInt8).clone(); // need a copy to own
  cv_img.geo_lla_data = GpsFetcher::get().get_geo(cv_img.timestamp_ms);
  deck_cv_->push_image(cv_img);
}
} // namespace carbon::deck_cam
#include "lib/drivers/deck_cam/cpp/lucid_camera.hpp"

#include <atomic>
#include <chrono>
#include <stdexcept>
#include <thread>

#include <cv/deck/deck_cv.h>

#include <fmt/format.h>
#include <opencv2/core/mat.hpp>
#include <spdlog/spdlog.h>

const int kDeviceEnumerationTimeoutMs = 500;
const int kImgGrabTimeout = 200;
constexpr auto kRetryAcquireSleep = std::chrono::seconds(1);
const std::string kLucidVisionVendorName = "Lucid Vision Labs";
static std::atomic<bool> init_grace_passed(false);
static std::chrono::system_clock::time_point boot_time(std::chrono::system_clock::now());
namespace carbon::deck_cam {

class ArenaTimedImg : public TimedImg {
public:
  ArenaTimedImg(Arena::IDevice **dev, int timeout_ms, const std::atomic_bool &streaming, std::mutex &mut);
  ~ArenaTimedImg();
  bool is_valid() const;

private:
  Arena::IDevice **dev_;
  Arena::IImage *data_;
  const std::atomic_bool &streaming_;
  std::mutex &mut_;
};

ArenaTimedImg::ArenaTimedImg(Arena::IDevice **dev, int timeout_ms, const std::atomic_bool &streaming, std::mutex &mut)
    : TimedImg(), dev_(dev), data_(nullptr), streaming_(streaming), mut_(mut) {
  data_ = (*dev_)->GetImage(timeout_ms);
  if (is_valid()) {
    // Using computer time until cameras have ptp setup so we get valid timestamps
    cap_time = std::chrono::system_clock::now();
    int rows = static_cast<int>(data_->GetHeight());
    int cols = static_cast<int>(data_->GetWidth());
    if (data_->GetPixelFormat() == PFNC_Coord3D_C16) {
      img = cv::Mat(rows, cols, CV_16UC1, (void *)data_->GetData());
    } else {
      img = cv::Mat(rows, cols, CV_8UC3, (void *)data_->GetData());
    }
  }
}

bool ArenaTimedImg::is_valid() const {
  return data_ != nullptr && !data_->IsIncomplete() && data_->GetHeight() > 0 && data_->GetWidth() > 0 &&
         data_->HasImageData() && data_->GetData() != nullptr;
}

ArenaTimedImg::~ArenaTimedImg() {
  std::unique_lock<std::mutex> lk(mut_);
  if (data_ && *dev_) {
    try {
      (*dev_)->RequeueBuffer(data_);
    } catch (GenICam::InvalidArgumentException &ex) {
      if (streaming_) {
        spdlog::error("error trying to requeue buffer in GrabImageGuard dtor: {}", ex.what());
      } else {
        spdlog::info("Failed to requeue buffer after streaming stopped");
      }
    } catch (GenICam::GenericException &ex) {
      spdlog::error("GenICam error trying to requeue buffer in GrabImageGuard dtor: {}", ex.what());
    } catch (std::runtime_error &ex) {
      spdlog::error("Standard lib error trying to requeue buffer in GrabImageGuard dtor: {}", ex.what());
    } catch (...) {
      // catch all for safety
      spdlog::error("Unknown error trying to requeue buffer in GrabImageGuard dtor");
    }
  }
}

class ArenaSequencer {
private:
  ArenaSequencer(const ArenaSequencer &) = delete;
  ArenaSequencer &operator=(const ArenaSequencer &) = delete;
  bool init_;
  std::mutex mut_;

  ArenaSequencer() : init_(false) {}

public:
  static ArenaSequencer &get() {
    static ArenaSequencer inst;
    return inst;
  }
  Arena::IDevice *get_device(lib::drivers::thinklucid::ArenaAutoInitTerm &term, const std::optional<std::string> &ip,
                             const std::optional<std::string> &serial) {
    std::vector<Arena::DeviceInfo> device_infos;
    std::unique_lock<std::mutex> lk(mut_);
    int attempts = 2;
    try {
      auto *system = term.get_system();
      if (!init_) {
        init_ = true;
        attempts = 1;
        system->UpdateDevices(kDeviceEnumerationTimeoutMs);
      }
      for (int i = 0; i < attempts; ++i) {
        if (i > 0) {
          system->UpdateDevices(kDeviceEnumerationTimeoutMs);
        }
        device_infos = system->GetDevices();
        for (auto &dev : device_infos) {
          if (kLucidVisionVendorName == dev.VendorName().c_str()) {
            if (serial && serial.value() == dev.SerialNumber().c_str()) {
              return system->CreateDevice(dev);
            } else if (ip && ip.value() == dev.IpAddressStr().c_str()) {
              return system->CreateDevice(dev);
            }
          }
        }
      }
    } catch (const GenICam::GenericException &ex) {
      spdlog::error("Error searching for devices ex: {}", ex.what());
      init_ = false; // Force a reload of devices on next attempt
    }
    spdlog::warn("no matching device found");
    return nullptr;
  }
};

CameraType get_type_from_cfg_lucid(std::shared_ptr<config::ConfigTree> tree) {
  auto fisheye_node = tree->get_node("fisheye");
  if (!fisheye_node) {
    return CameraType::STANDARD;
  }
  if (fisheye_node->get_node("is_fisheye")->get_value<bool>()) {
    return CameraType::FISHEYE;
  } else {
    return CameraType::STANDARD;
  }
  // TODO support depth at some point
}
std::optional<std::string> get_optional_str(std::shared_ptr<config::ConfigTree> node) {
  if (!node) {
    return std::nullopt;
  }
  std::string val = node->get_value<std::string>();
  if (val == "") {
    return std::nullopt;
  }
  return val;
}
uint32_t int_cfg_to_uint(std::shared_ptr<config::ConfigTree> node) {
  if (!node) {
    return 0;
  }
  int val = node->get_value<int>();
  if (val < 0) {
    return 0;
  }
  return static_cast<uint32_t>(val);
}

int buf_count_from_cfg(std::shared_ptr<config::ConfigTree> tree) {
  int count = tree->get_node("buffer_count")->get_value<int>();
  if (count <= 0) {
    count = 15;
  }
  return count;
}
LucidCamera::LucidCamera(std::shared_ptr<config::ConfigTree> tree, std::shared_ptr<cv::deck::DeckCV> cv)
    : Camera(tree, get_type_from_cfg_lucid(tree), cv), ip_(get_optional_str(tree->get_node("ip"))),
      serial_(get_optional_str(tree->get_node("serial"))), device_(nullptr), started_(false), next_buf_(nullptr),
      buf_count_(buf_count_from_cfg(tree_)),
      bse_(lib::common::bot::BotStopHandler::get().create_scoped_event(name_, std::bind(&LucidCamera::shutdown, this))),
      first_connect_called_(false), width_(int_cfg_to_uint(tree->get_node("width"))),
      height_(int_cfg_to_uint(tree->get_node("height"))), use_cv_(false) {
  if (width_ == 0 || height_ == 0) {
    throw std::runtime_error(fmt::format("{} cannot have a zero sized image output", name_));
  }
  if (deck_cv_) {
    bool added = false;
    if (!added) {
      auto cv_cfg_node = tree_->get_node("cv");
      if (cv_cfg_node) {
        if (cv_cfg_node->get_node("required")->get_value<bool>() ||
            cv_cfg_node->get_node("furrows_enabled")->get_value<bool>()) {
          deck_cv_->add_camera(tree_);
          added = true;
        }
      }
    }
    if (!added) {
      auto ab_node = tree_->get_node("hdr/auto_brightness/enabled");
      if (ab_node && ab_node->get_value<bool>()) {
        deck_cv_->add_camera(tree_);
        added = true;
      }
    }
    if (added) {
      use_cv_ = true;
    }
  }
}
LucidCamera::~LucidCamera() {
  stop();
  if (device_) {
    auto *system = term_.get_system();
    system->DestroyDevice(device_);
  }
  device_ = nullptr;
}
void LucidCamera::shutdown() {
  stop();
  bse_.set();
}
void LucidCamera::start() {
  std::lock_guard lg(mut_);
  if (started_) {
    return;
  }
  thread_ = std::thread(&LucidCamera::worker, this);
  started_.exchange(true);
}
void LucidCamera::stop() {
  if (started_.exchange(false)) {
    cv_.notify_all();
    if (thread_.joinable()) {
      thread_.join();
    }
  }
}
std::shared_ptr<TimedImg> LucidCamera::get_next(std::chrono::system_clock::time_point last,
                                                std::chrono::milliseconds timeout) {
  auto test = [&] { return this->next_buf_ && this->next_buf_->is_valid() && this->next_buf_->cap_time > last; };
  std::unique_lock<std::mutex> lk(this->mut_);
  if (!test()) {
    this->cv_.wait_for(lk, timeout, test);
  }
  if (test()) {
    return next_buf_;
  }
  bool can_log = init_grace_passed;
  if (!can_log) {
    auto delta = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now() - boot_time);
    if (delta > std::chrono::seconds(15)) {
      can_log = true;
      init_grace_passed = true;
    }
  }
  if (can_log) {
    spdlog::warn("{} failed to get next image", name_); // TODO fix me
  }
  return nullptr;
}

bool LucidCamera::connected() {
  std::unique_lock<std::mutex> lk(dev_mut_);
  return device_ != nullptr;
}
void LucidCamera::stop_stream() {
  if (device_) {
    try {
      device_->StopStream();
    } catch (GenICam::LogicalErrorException &) {
      /*
       * Ideally we could check if is_streaming, but camera does not appear to
       * provide this information. Instead we can try to always stop it, and
       * when camera is not already streaming we get a LogicalError.
       */
    } catch (GenICam::GenericException &ex) {
      spdlog::info("Failed to stop stream for {} err: {}", name_, ex.what());
    }
  }
}
void LucidCamera::start_dev() {
  spdlog::info("Attempting to start device {}", name_);
  while (started_) {
    try {
      std::unique_lock<std::mutex> lk(dev_mut_);
      stop_stream();
      if (device_) {
        auto tmp_dev = device_;
        device_ = nullptr;
        spdlog::info("Attempting to destroy device {}", name_);
        term_.get_system()->DestroyDevice(tmp_dev);
        spdlog::info("device {} destroyed", name_);
        std::this_thread::sleep_for(kRetryAcquireSleep);
      }
      device_ = ArenaSequencer::get().get_device(term_, ip_, serial_);
      if (!device_) {
        spdlog::warn("Failed to find device {}", name_);
        first_connect_called_.exchange(false);
      } else {
        if (!first_connect_called_.exchange(true)) {
          try {
            lk.unlock();
            on_first_connect();
            lk.lock();
          } catch (const std::exception &ex) {
            spdlog::warn("Failed to run callback for {} err: {}", name_, ex.what());
            first_connect_called_.exchange(false);
            std::this_thread::sleep_for(kRetryAcquireSleep);
            continue;
          }
        }
        device_->StartStream(buf_count_);
        spdlog::info("Streaming started for {}", name_);
        return;
      }
    } catch (GenICam::AccessException &ex) {
      auto err_msg = fmt::format("Access denied cannot stream device {} device is owned by another process, may need "
                                 "to restart device and or host.",
                                 name_);
      spdlog::error(err_msg);
      throw std::runtime_error(err_msg);
    } catch (GenICam::GenericException &ex) {
      spdlog::warn("Failed to start device {}, ex: {}", name_, ex.what());
    }
    std::this_thread::sleep_for(kRetryAcquireSleep);
  }
}
void LucidCamera::worker() {
  constexpr size_t max_error_count = 10;
  size_t error_count = 0;
  start_dev();
  while (started_) {
    std::shared_ptr<ArenaTimedImg> buf(nullptr);
    try {
      buf = std::make_shared<ArenaTimedImg>(&device_, kImgGrabTimeout, started_, dev_mut_);
      if (!buf->is_valid()) {
        buf.reset(); // invalid img reset buffer to null
        spdlog::info("got bad img from {}", name_);
      }
    } catch (GenICam::TimeoutException &ex) {
      spdlog::warn("timed out waiting {}ms for image from {}", kImgGrabTimeout, name_);
    } catch (GenICam::GenericException &ex) {
      spdlog::warn("{} failed to grab image ex: {}", name_, ex.what());
    }
    if (!buf) {
      ++error_count;
      if (error_count > max_error_count) {
        start_dev();
        error_count = 0;
      }
      continue;
    }
    error_count = 0;
    {
      std::lock_guard lg(mut_);
      next_buf_.swap(buf);
      cv_.notify_all();
    }
    if (use_cv_) {
      push_img_to_cv(next_buf_);
    }
    inc_frame();
  }
  stop_stream();
}
template <typename T>
bool LucidCamera::_set_config(const std::string &name, const T &value) {
  std::unique_lock<std::mutex> lk(dev_mut_);
  if (!device_) {
    spdlog::warn("device {} is not connected. cannot configure", name_);
    return false;
  }
  return _set_config_from_node(device_->GetNodeMap(), name, value);
}
template <typename T>
bool LucidCamera::_set_config_tl_stream(const std::string &name, const T &value) {
  std::unique_lock<std::mutex> lk(dev_mut_);
  if (!device_) {
    spdlog::warn("device {} is not connected. cannot configure", name_);
    return false;
  }
  return _set_config_from_node(device_->GetTLStreamNodeMap(), name, value);
}
template <typename T>
bool LucidCamera::_set_config_from_node(GenApi::INodeMap *node_map, const std::string &name, const T &value) {
  try {
    Arena::SetNodeValue<T>(node_map, name.c_str(), value);
  } catch (GenICam::GenericException &ex) {
    spdlog::error("genIcam error trying to set node {} for camera {} err: {}", name, name_, ex.what());
    return false;
  } catch (const std::exception &ex) {
    spdlog::error("Unknown exception setting node {} for camera {}. Err: {}", name, name_, ex.what());
    return false;
  }
  return true;
}
bool LucidCamera::set_config(const std::string &name, bool value) { return _set_config(name, value); }
bool LucidCamera::set_config(const std::string &name, double value) { return _set_config(name, value); }
bool LucidCamera::set_config(const std::string &name, int64_t value) { return _set_config(name, value); }
bool LucidCamera::set_config(const std::string &name, const std::string &value) {
  return _set_config(name, GenICam::gcstring(value.c_str()));
}
bool LucidCamera::set_config(const std::string &name, const char *value) {
  return _set_config(name, GenICam::gcstring(value));
}
bool LucidCamera::set_config_tl_stream(const std::string &name, bool value) {
  return _set_config_tl_stream(name, value);
}
bool LucidCamera::set_config_tl_stream(const std::string &name, double value) {
  return _set_config_tl_stream(name, value);
}
bool LucidCamera::set_config_tl_stream(const std::string &name, int64_t value) {
  return _set_config_tl_stream(name, value);
}
bool LucidCamera::set_config_tl_stream(const std::string &name, const std::string &value) {
  return _set_config_tl_stream(name, GenICam::gcstring(value.c_str()));
}
bool LucidCamera::set_config_tl_stream(const std::string &name, const char *value) {
  return _set_config_tl_stream(name, GenICam::gcstring(value));
}

bool LucidCamera::get_config_bool(const std::string &name) { return _get_config<bool>(name); }
double LucidCamera::get_config_double(const std::string &name) { return _get_config<double>(name); }
int64_t LucidCamera::get_config_int(const std::string &name) { return _get_config<int64_t>(name); }
std::string LucidCamera::get_config_str(const std::string &name) {
  return std::string(_get_config<GenICam::gcstring>(name));
}
template <typename T>
T LucidCamera::_get_config(const std::string &name) {
  std::unique_lock<std::mutex> lk(dev_mut_);
  if (!device_) {
    spdlog::error("No device {} to read config from", name_);
    throw std::runtime_error("No device to read config from");
  }
  try {
    return Arena::GetNodeValue<T>(device_->GetNodeMap(), name.c_str());
  } catch (const std::exception &ex) {
    spdlog::error("Unhandled exception getting node {} for camera {}. err: {}", name, name_, ex.what());
    throw ex;
  } catch (GenICam::GenericException &ex) {
    spdlog::warn("{} get config {} failed with err: {}", name_, name, ex.what());
    throw std::runtime_error(fmt::format("{} failed to get config node {}", name_, name));
  }
}
std::string LucidCamera::get_config_as_str(const std::string &name) {
  std::unique_lock<std::mutex> lk(dev_mut_);
  if (!device_) {
    spdlog::error("No device {} to read config from", name_);
    throw std::runtime_error("No device to read config from");
  }
  GenApi::CValuePtr pValue = device_->GetNodeMap()->GetNode(name.c_str());
  return std::string(pValue->ToString());
}
void LucidCamera::on_first_connect() {
  set_config_tl_stream("StreamBufferHandlingMode", "NewestOnly");
  set_config_tl_stream("StreamAutoNegotiatePacketSize", true);
  // This helps us not to have pink frames for half a second trying to recover lost frames
  set_config_tl_stream("StreamPacketResendEnable", false);
  if (tree_->get_node("ptp")->get_value<bool>()) {
    set_config("PtpSlaveOnly", true);
    set_config("PtpEnable", true);
  } else {
    set_config("PtpEnable", false);
  }

  auto model = get_config_str("DeviceModelName");
  model_specific_cfgs(model);
  set_shape();
}
void LucidCamera::model_specific_cfgs(const std::string &model) {
  if (model == "HTR003S-001") {
    set_config("PixelFormat", "Coord3D_C16");
    set_config("Scan3dConfidenceThresholdEnable", true);
    set_config("Scan3dSpatialFilterEnable", true);
    set_config("Scan3dOperatingMode", "Distance8300mmMultiFreq");
    set_config("ExposureTimeSelector", "Exp350Us");
  } else {
    set_config("PixelFormat", "RGB8");
    set_config("ReverseX", tree_->get_node("mirror")->get_value<bool>());
    set_config("ReverseY", tree_->get_node("flip")->get_value<bool>());
  }
  if (model != "TDR054S-C" && model != "HTR003S-001") {
    set_config("GainAuto", "Continuous");
    set_config("ExposureAuto", "Continuous");
  } else if (model == "TDR054S-C") {
    hdr_cfgs();
  }
}
void LucidCamera::hdr_cfgs() {
  set_config("HDRTuningEnable", false);
  set_config("HDRImageEnhancementEnable", true);
  set_config("HDRDigitalClampingEnable", true);
  auto hdr_tree = tree_->get_node("hdr");
  if (!hdr_tree) {
    spdlog::warn("No hdr settings found for {}", name_);
    return;
  }

  float saturation = hdr_tree->get_node("saturation")->get_value<float>();
  float contrast = hdr_tree->get_node("contrast")->get_value<float>();
  int64_t brightness = static_cast<int64_t>(hdr_tree->get_node("brightness")->get_value<float>());
  float detail = hdr_tree->get_node("detail")->get_value<float>();
  int average_num = hdr_tree->get_node("average_num")->get_value<int>();
  float exposure_us = hdr_tree->get_node("exposure_us")->get_value<float>();

  if (saturation >= 0.0f) {
    set_config("HDRSaturation", saturation);
  }
  if (contrast >= 0.0f) {
    set_config("HDRContrast", contrast);
  }
  if (brightness > 0) {
    set_config("HDRBrightness", brightness);
  }
  if (detail >= 0.0f) {
    set_config("HDRDetail", hdr_tree->get_node("detail")->get_value<float>());
  }
  if (average_num > 0) {
    set_config("HDRAverageNum", fmt::format("HDRAverage{}", average_num));
  }
  if (exposure_us >= 0.0f) {
    set_config("ExposureTime", exposure_us);
  }
}
void LucidCamera::set_shape() {
  int binning = tree_->get_node("binning")->get_value<int>();
  if (binning <= 0) {
    binning = 1;
  }
  set_config("BinningHorizontal", binning);
  set_config("BinningVertical", binning);

  auto width_max = get_config_int("WidthMax");
  auto height_max = get_config_int("HeightMax");
  auto cur_width = get_config_int("Width");
  auto cur_height = get_config_int("Height");

  int64_t width_offset = 0;
  int64_t height_offset = 0;

  if (static_cast<int64_t>(width_) < width_max) {
    width_offset = (width_max / 2) - (static_cast<int64_t>(width_) / 2);
  }
  if (static_cast<int64_t>(height_) < height_max) {
    height_offset = (height_max / 2) - (static_cast<int64_t>(height_) / 2);
  }

  // If we are getting bigger - set the offset first so we don't blow off the end
  if (static_cast<int64_t>(width_) > cur_width) {
    set_config("OffsetX", width_offset);
  }
  if (static_cast<int64_t>(height_) > cur_height) {
    set_config("OffsetY", height_offset);
  }
  set_config("Width", static_cast<int64_t>(width_));
  set_config("Height", static_cast<int64_t>(height_));

  // If we are getting smaller - set the size first so we don't blow off the end
  if (static_cast<int64_t>(width_) < cur_width) {
    set_config("OffsetX", width_offset);
  }
  if (static_cast<int64_t>(height_) < cur_height) {
    set_config("OffsetY", height_offset);
  }
}

} // namespace carbon::deck_cam
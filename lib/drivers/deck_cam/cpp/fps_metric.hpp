#pragma once
#include <chrono>
#include <cstdint>
#include <functional>
#include <memory>
#include <string>

namespace prometheus {
class Gauge;
}
namespace carbon::deck_cam {
class FpsMetric {
public:
  FpsMetric(const std::string &name, uint32_t win_size = 300);
  void inc();

private:
  const uint32_t win_size_;
  uint32_t count_;
  std::chrono::system_clock::time_point win_start_time_;
  std::reference_wrapper<prometheus::Gauge> gauge_;
};
} // namespace carbon::deck_cam
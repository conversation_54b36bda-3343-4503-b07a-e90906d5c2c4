#include "lib/drivers/deck_cam/cpp/camera.hpp"
#include <atomic>
#include <condition_variable>
#include <functional>
#include <memory>
#include <mutex>
#include <optional>
#include <string>
#include <thread>

#include <ArenaApi.h>

#include <config/tree/cpp/config_tree.hpp>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/drivers/thinklucid/cpp/autoinit.h>

namespace carbon::deck_cam {

class ArenaTimedImg;
class LucidCamera : public Camera {
public:
  LucidCamera(std::shared_ptr<config::ConfigTree> tree, std::shared_ptr<cv::deck::DeckCV> cv);
  ~LucidCamera();
  void start() override;
  void stop() override;
  uint32_t width() override { return width_; }
  uint32_t height() override { return height_; }
  std::shared_ptr<TimedImg> get_next(std::chrono::system_clock::time_point last,
                                     std::chrono::milliseconds timeout) override;
  bool connected() override;

  bool set_config(const std::string &name, bool value) override;
  bool set_config(const std::string &name, double value) override;
  bool set_config(const std::string &name, int64_t value) override;
  bool set_config(const std::string &name, const std::string &value) override;
  bool get_config_bool(const std::string &name) override;
  double get_config_double(const std::string &name) override;
  int64_t get_config_int(const std::string &name) override;
  std::string get_config_str(const std::string &name) override;
  std::string get_config_as_str(const std::string &name) override;

private:
  lib::drivers::thinklucid::ArenaAutoInitTerm term_;
  std::optional<std::string> ip_;
  std::optional<std::string> serial_;
  Arena::IDevice *device_;
  std::atomic_bool started_;
  std::shared_ptr<ArenaTimedImg> next_buf_;
  std::mutex mut_;
  std::mutex dev_mut_;
  std::condition_variable cv_;
  int buf_count_;
  lib::common::bot::ScopedBotStopEvent bse_;
  std::atomic_bool first_connect_called_;
  const uint32_t width_;
  const uint32_t height_;
  bool use_cv_;

  std::thread thread_;

  void on_first_connect();
  void model_specific_cfgs(const std::string &model);
  void hdr_cfgs();
  void set_shape();

  bool set_config(const std::string &name, const char *value);
  bool set_config_tl_stream(const std::string &name, bool value);
  bool set_config_tl_stream(const std::string &name, double value);
  bool set_config_tl_stream(const std::string &name, int64_t value);
  bool set_config_tl_stream(const std::string &name, const std::string &value);
  bool set_config_tl_stream(const std::string &name, const char *value);
  inline bool set_config(const std::string &name, int value) { return set_config(name, static_cast<int64_t>(value)); }
  void worker();
  void start_dev();
  void stop_stream();
  void shutdown();
  template <typename T>
  bool _set_config(const std::string &name, const T &value);
  template <typename T>
  bool _set_config_tl_stream(const std::string &name, const T &value);
  template <typename T>
  bool _set_config_from_node(GenApi::INodeMap *node_map, const std::string &name, const T &value);
  template <typename T>
  T _get_config(const std::string &name);
};
} // namespace carbon::deck_cam
#include "lib/drivers/deck_cam/cpp/fps_metric.hpp"

#include <prometheus/gauge.h>
#include <prometheus/registry.h>

#include <metrics/cpp/prometheus/exposer_owner.hpp>

std::shared_ptr<prometheus::Registry> get_reg() {
  static std::shared_ptr<prometheus::Registry> reg(std::make_shared<prometheus::Registry>());
  return reg;
}
prometheus::Family<prometheus::Gauge> &get_family() {
  static prometheus::Family<prometheus::Gauge> &family =
      prometheus::BuildGauge().Name("camera_fps").Help("The frame rate of a camera").Register(*get_reg());
  return family;
}
bool _register_prom() {
  carbon::metrics::ExposerOwner::register_collectable(get_reg());
  return true;
}
void register_prom() {
  static bool registered(_register_prom());
  (void)registered;
}
namespace carbon::deck_cam {
FpsMetric::FpsMetric(const std::string &name, uint32_t win_size)
    : win_size_(win_size), count_(0), win_start_time_(std::chrono::system_clock::now()),
      gauge_(get_family().Add({{"camera_id", name}})) {
  register_prom();
}
void FpsMetric::inc() {
  count_ = (count_ + 1) % win_size_;
  if (count_ == 0) {
    auto now = std::chrono::system_clock::now();
    auto delta_ms = std::chrono::duration_cast<std::chrono::milliseconds>(now - win_start_time_);
    float fps = static_cast<float>(win_size_) / (static_cast<float>(delta_ms.count()) / 1000.0f);
    gauge_.get().Set(fps);
    win_start_time_ = now;
  }
}
} // namespace carbon::deck_cam
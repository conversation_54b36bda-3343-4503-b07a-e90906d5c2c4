add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)

add_library(deck_cam SHARED ${SOURCES})
target_include_directories(deck_cam SYSTEM PUBLIC ${OpenCV_INCLUDE_DIRS})

set(ARENA_ROOT /opt/ArenaSDK_Linux_x64)
set(ARENA_ROOT_ARM /opt/ArenaSDK_Linux_ARM64)
target_compile_definitions(deck_cam PRIVATE -DSPDLOG_FMT_EXTERNAL=1 HAVE_OPENCV)


target_link_directories(deck_cam PRIVATE ${ARENA_ROOT}/lib64
    ${ARENA_ROOT}/GenICam/library/lib/Linux64_x64)
target_link_directories(deck_cam PRIVATE ${ARENA_ROOT_ARM}/lib
    ${ARENA_ROOT_ARM}/GenICam/library/lib/Linux64_ARM)
target_include_directories(deck_cam PRIVATE ${ARENA_ROOT}/include/Arena
    ${ARENA_ROOT}/GenICam/library/CPP/include)
target_include_directories(deck_cam PRIVATE ${ARENA_ROOT_ARM}/include/Arena
    ${ARENA_ROOT_ARM}/GenICam/library/CPP/include)

#find_package(MultiSense REQUIRED)
target_link_libraries(deck_cam PRIVATE m pthread spdlog fmt arena gentl exceptions GCBase_gcc54_v3_3_LUCID GenApi_gcc54_v3_3_LUCID
    thinklucid_camera config_tree_lib bot_stop MultiSense opencv_core opencv_videoio deck_cv torch hardware_manager_client metrics_prometheus)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(deck_cam_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(deck_cam_python  PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
target_link_directories(deck_cam_python PRIVATE ${ARENA_ROOT}/lib64
    ${ARENA_ROOT}/GenICam/library/lib/Linux64_x64)
target_link_directories(deck_cam_python PRIVATE ${ARENA_ROOT_ARM}/lib
    ${ARENA_ROOT_ARM}/GenICam/library/lib/Linux64_ARM)
target_include_directories(deck_cam_python PRIVATE ${ARENA_ROOT}/include/Arena
    ${ARENA_ROOT}/GenICam/library/CPP/include)
target_include_directories(deck_cam_python PRIVATE ${ARENA_ROOT_ARM}/include/Arena
    ${ARENA_ROOT_ARM}/GenICam/library/CPP/include)
target_link_libraries(deck_cam_python PUBLIC deck_cam torch_python deck_cv_python)
set_target_properties(deck_cam_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)
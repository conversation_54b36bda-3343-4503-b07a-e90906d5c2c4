import datetime
import hashlib
import os
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List

from dataclass_wizard import JSONWizard

from hardware_manager.python.client import GPS_LLA
from lib.common.environment import robot_name, version_tag
from lib.common.time.time import iso8601_timestamp

IMG_CAPTURE_PATH = "/data/uploader/uploads"


class ArtifactType(str, Enum):
    FURROWS = "furrows"


class ArtifactSubtype(str, Enum):
    RGBD = "rgbd"
    RGB = "rgb"
    DEPTH = "depth"
    LIDAR = "lidar"


@dataclass
class GeoMetadataLLA(JSONWizard):
    lat: float
    lng: float
    alt: float
    timestamp_ms: int

    class _(JSONWizard.Meta):
        key_transform_with_dump = "SNAKE"


@dataclass
class GeoMetadata(JSONWizard):
    lla: GeoMetadataLLA

    @classmethod
    def from_LLA(cls, lla: GPS_LLA) -> "GeoMetadata":
        return GeoMetadata(lla=GeoMetadataLLA(lat=lla.lat, lng=lla.lng, alt=lla.alt, timestamp_ms=lla.timestamp_ms))

    class _(JSONWizard.Meta):
        key_transform_with_dump = "SNAKE"


@dataclass
class ImageMetadata(JSONWizard):
    artifact_type: str
    artifact_subtype: str
    files: List[Dict[str, str]]
    height: int
    width: int
    timestamp_ms: int
    cam_id: str
    geo: GeoMetadata
    robot_id: str = robot_name()
    software_version: str = version_tag()

    class _(JSONWizard.Meta):
        key_transform_with_dump = "SNAKE"


def md5sum(path: str) -> str:
    with open(path, "rb") as f:
        m = hashlib.md5()
        m.update(f.read())
        return m.hexdigest()


def get_image_filepath(camera_id: str, artifact_type: str) -> str:
    path = os.path.join(IMG_CAPTURE_PATH, datetime.date.today().isoformat())
    os.makedirs(path, exist_ok=True)
    return f"{path}/{artifact_type}_{robot_name()}_{camera_id}_{iso8601_timestamp(replace_colon=True)}"

from types import TracebackType
from typing import Any, Optional, Type

import numpy as np
import numpy.typing as npt

import pyrealsense2 as rs
from lib.drivers.deck_cam.config import CameraConfig, RealSenseCameraConfig


class RealsenseManagedImage:
    def __init__(self, img: npt.NDArray[Any]) -> None:
        super().__init__()

        self._img = img

    def get(self) -> npt.NDArray[np.uint8]:
        img = self._img
        img = (img % (256 * 8)) / 8
        img = img.astype(np.uint8)
        img = np.expand_dims(img, axis=2)
        img = np.repeat(img, 3, axis=2)
        return img


class RealsenseCamera:
    def __init__(self, name: str, config: RealSenseCameraConfig) -> None:
        super().__init__()
        self._name = name
        context = rs.context()
        devices = context.query_devices()
        self._device = None
        for device in devices:
            if device.get_info(rs.camera_info.serial_number) == config.serial:
                self._device = device
        if self._device is None:
            raise ValueError(f"Realsense camera with serial number {config.serial} not found")

        self._width = 640
        self._height = 480
        self._rs_config = rs.config()
        self._rs_config.enable_device(self._device.get_info(rs.camera_info.serial_number))
        self._rs_config.enable_stream(rs.stream.infrared, 1, self._width, self._height, rs.format.y8, 30)
        self._rs_config.enable_stream(rs.stream.depth, self._width, self._height, rs.format.z16, 30)

        self._hdr_merge = rs.hdr_merge()
        self._temporal_filter = rs.temporal_filter()

        self._pipeline = rs.pipeline()

        profile = self._pipeline.start(self._rs_config)
        device = profile.get_device()
        sensor = device.query_sensors()[0]
        sensor.set_option(rs.option.sequence_size, 2)
        sensor.set_option(rs.option.sequence_name, 1)

        sensor.set_option(rs.option.sequence_id, 0)
        sensor.set_option(rs.option.hdr_enabled, 1)

        self._config = config

    @property
    def name(self) -> str:
        return self._name

    @property
    def config(self) -> CameraConfig:
        return self._config

    @property
    def width(self) -> int:
        return self._width

    @property
    def height(self) -> int:
        return self._height

    def __exit__(
        self,
        __exc_type: Optional[Type[BaseException]],
        __exc_value: Optional[BaseException],
        __traceback: Optional[TracebackType],
    ) -> Optional[bool]:
        return None

    # async def get_next(self, timeout: int = 5000) -> Optional[ManagedImg]:
    #    frames = self._pipeline.wait_for_frames()

    #    frames = self._hdr_merge.process(frames).as_frameset()
    #    if frames.get_frame_metadata(rs.frame_metadata_value.sequence_id) != 0:
    #        return None

    #    depth_frame = frames.get_depth_frame()
    #    depth_frame = self._temporal_filter.process(depth_frame)

    #    if not depth_frame:
    #        return None
    #    depth_image = np.asanyarray(depth_frame.get_data())
    #    return RealsenseManagedImage(depth_image)

    def release(self) -> None:
        self._pipeline.stop()

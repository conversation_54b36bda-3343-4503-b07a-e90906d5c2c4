from types import TracebackType
from typing import Any, Optional, Type, cast

import cv2
import numpy as np
import numpy.typing as npt

from lib.common.logging import get_logger
from lib.drivers.deck_cam.config import CameraConfig, CameraType, SICKCameraConfig
from lib.drivers.sick.driver import SickLidar

LOG = get_logger(__name__)

sick_open_udp_port = 5000
SICK_PORT = 2111


class SICKCamera:
    def __init__(self, name: str, config: SICKCameraConfig) -> None:
        super().__init__()
        self._name = name
        self._config = config
        global sick_open_udp_port
        self._port = sick_open_udp_port
        sick_open_udp_port += 1
        assert self._config.ip is not None, "IP required for SICK camera type"
        self._cam = SickLidar("*********", self._port, self._config.ip, 2111)

        self._phi_delta = self._config.phi_delta
        self._theta_delta = self._config.theta_delta
        self._phi_begin = self._config.phi_min
        self._phi_end = self._config.phi_max
        self._phi_range = self._phi_end - self._phi_begin
        self._phi_num_values = int(np.ceil(self._phi_range / self._phi_delta))

        self._theta_begin = self._config.theta_min
        self._theta_end = self._config.theta_max
        self._theta_range = self._theta_end - self._theta_begin
        self._theta_num_values = int(np.ceil(self._theta_range / self._theta_delta))

        min_delta = min(self._phi_delta, self._theta_delta)
        self._phi_ratio = self._phi_delta / min_delta
        self._theta_ratio = self._theta_delta / min_delta
        self._height = int(np.ceil(self._phi_ratio * self._phi_num_values))
        self._width = int(np.ceil(self._theta_ratio * self._theta_num_values))

    @property
    def name(self) -> str:
        return self._name

    @property
    def config(self) -> CameraConfig:
        return self._config

    @property
    def height(self) -> int:
        return self._height

    @property
    def width(self) -> int:
        return self._width

    @property
    def cam_type(self) -> CameraType:
        return CameraType.STANDARD

    def __enter__(self) -> Any:
        self._cam.start_stream()
        return None

    def __exit__(
        self,
        __exc_type: Optional[Type[BaseException]],
        __exc_value: Optional[BaseException],
        __traceback: Optional[TracebackType],
    ) -> Optional[bool]:
        self._cam.stop_stream()
        return None

    # def _save_image(self, depth_projection: npt.NDArray[np.float64], point_cloud: SickFrame) -> None:
    #    artifact_type = ArtifactType.FURROWS
    #    filepath_no_ext = get_image_filepath(self.name, artifact_type)
    #    depth_filepath = f"{filepath_no_ext}.npz"
    #    point_cloud_filepath = f"{filepath_no_ext}.json"
    #    np.savez_compressed(depth_filepath, view_depth_fp32=depth_projection)

    #    with open(point_cloud_filepath, "w") as fp:
    #        fp.write(point_cloud.to_json())

    #    metadata = ImageMetadata(
    #        artifact_type=artifact_type,
    #        artifact_subtype=ArtifactSubtype.LIDAR,
    #        files=[
    #            {"name": os.path.basename(depth_filepath), "md5": md5sum(depth_filepath)},
    #            {"name": os.path.basename(point_cloud_filepath), "md5": md5sum(point_cloud_filepath)},
    #        ],
    #        height=depth_projection.shape[0],
    #        width=depth_projection.shape[1],
    #        timestamp_ms=maka_control_timestamp_ms(),
    #        cam_id=self.name,
    #        geo=GeoMetadata.from_LLA(TRACTOR_STATE.gps),
    #    )
    #    metadata_filepath = f"{filepath_no_ext}.metadata.json"
    #    with open(metadata_filepath, "w") as fp:
    #        fp.write(metadata.to_json())

    def __retrieve(self) -> Optional[npt.NDArray[np.uint8]]:
        frame = self._cam.grab()
        if frame is None:
            return None
        spherical_image: npt.NDArray[np.float64] = np.zeros(
            (self._phi_num_values, self._theta_num_values), dtype=np.float32
        )
        for segment in frame.Segments:
            for scan_line in segment.SegmentData:
                phi = scan_line.ChannelPhi[0]
                for i in range(len(scan_line.ChannelTheta)):
                    theta = -scan_line.ChannelTheta[i]

                    distance = scan_line.DistValues[0][i]
                    rssi = scan_line.RssiValues[0][i]

                    horiz_deg = np.rad2deg(theta)
                    if horiz_deg >= self._theta_end or horiz_deg < self._theta_begin or rssi == 0:
                        continue

                    phi_index = int(np.floor((np.rad2deg(phi) - self._phi_begin) / self._phi_delta))
                    theta_index = int(np.floor((np.rad2deg(theta) - self._theta_begin) / self._theta_delta))

                    spherical_image[phi_index, theta_index] = distance

        kernel = np.ones((3, 3), np.uint8)
        dilated_spherical_image = cv2.dilate(spherical_image, kernel, iterations=1)
        spherical_image = np.where(spherical_image != 0, spherical_image, dilated_spherical_image)
        spherical_image = cast(npt.NDArray[np.float64], cv2.resize(spherical_image, (self._width, self._height)))
        # if self._should_capture():
        #    try:
        #        self._save_image(spherical_image, frame)
        #    except Exception as ex:
        #        LOG.error(f"Error saving image for {self.name}. ex: {ex}")

        # Visualization
        spherical_image[spherical_image > self._config.depth_max] = self._config.depth_max
        spherical_image = (
            (spherical_image - self._config.depth_min) / (self._config.depth_max - self._config.depth_min)
        ) * 255
        spherical_image_u8 = cast(npt.NDArray[np.uint8], np.uint8(spherical_image))
        spherical_image_u8 = cast(npt.NDArray[np.uint8], cv2.applyColorMap(spherical_image_u8, cv2.COLORMAP_JET))
        return spherical_image_u8

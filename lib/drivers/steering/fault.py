from dataclasses import dataclass

from dataclass_wizard import JSONW<PERSON>rd


@dataclass
class FaultDetails(JSONWizard):
    less_phase: bool = False
    motor_stall: bool = False
    hall_failure: bool = False
    current_sensing: bool = False
    rs232_disconnected: bool = False
    can_disconnected: bool = False
    motor_stalled: bool = False
    disabled: bool = False
    overvoltage: bool = False
    hardware_protection: bool = False
    eeprom: bool = False
    undervoltage: bool = False
    overcurrent: bool = False
    mode_failure: bool = False

    @property
    def faulted(self) -> bool:
        details = self.to_dict()
        # disabled is not a fault as we can manually disable without a fault
        del details["disabled"]
        return any(details.values())

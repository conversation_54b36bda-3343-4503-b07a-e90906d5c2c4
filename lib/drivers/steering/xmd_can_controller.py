import asyncio
import struct
from time import sleep
from typing import Optional

import can

from lib.common.abstract.config.config import TreeNode
from lib.common.asyncio.cancellable_await import cancellable_await
from lib.common.asyncio.data_event import DataEvent
from lib.common.filter.low_pass_filter import LowPassFilter
from lib.common.logging import get_logger
from lib.common.pid.simple_pi import P<PERSON>onfig, PIController
from lib.common.units.angle import Angle
from lib.drivers.steering.angle_info import AngleInfo

LOG = get_logger(__name__)

# Constants
PGN_COMMAND_STEER_LEFT = 0xFF02
PGN_COMMAND_STEER_RIGHT = 0xFF01
PGN_TRANSMIT_WHEEL_ANGLE = 0xFF82
SA = 0x22
PRIORITY = 0x7
MAX_INT16 = 0x7FFF


Provider = DataEvent[AngleInfo]


def get_canbus(interface: str, channel: str, bitrate: int) -> can.Bus:
    # Due to driver issues canbus may not be available on first attempt
    for _ in range(5):
        bus = can.Bus(interface=interface, channel=channel, bitrate=bitrate)  # type: ignore
        try:
            bus.recv(timeout=0.5)
            return bus
        except Exception:
            LOG.warning(f"Canbus {channel} not yet available")
            sleep(1)
    raise Exception(f"Unable to connect the the {channel} canbus")


class XMD_CAN_Controller:
    def __init__(self, conf: TreeNode, provider: Provider, interface: str = "socketcan",) -> None:
        channel = conf.get_node("can_bus").get_string_value()
        self._bus = get_canbus(interface=interface, channel=channel, bitrate=conf.get_node("bitrate").get_int_value())
        self._angle_de = provider
        self._desired_angle = Angle.from_degrees(0.0)
        self._pid_task: Optional[asyncio.Task[None]] = None
        self._pid_stop_event = asyncio.Event()
        self._enabled = False
        self._pi_cfg = conf.get_node("pid")
        self._lpf_alpha = conf.get_node("lpf_alpha")
        self._lpf = LowPassFilter(self._lpf_alpha.get_float_value(), 0.0)
        self._lpf_alpha.register_callback(self.__alpha_update)
        self._pi_controller = PIController(self.__pid_update())
        self._pi_cfg.register_callback(lambda: self._pi_controller.reset(self.__pid_update()))

    @property
    def enabled(self) -> bool:
        return self._enabled

    def __alpha_update(self) -> None:
        self._lpf.set_alpha(self._lpf_alpha.get_float_value())

    def __pid_update(self) -> PIConfig:
        return PIConfig(
            kp=self._pi_cfg.get_node("kp").get_float_value(),
            ki=self._pi_cfg.get_node("ki").get_float_value(),
            integral_limit=self._pi_cfg.get_node("integral_limit").get_float_value(),
        )

    def set_angle(self, angle: Angle) -> None:
        self._desired_angle = angle

    async def _pid_loop(self) -> None:
        data = await cancellable_await(self._angle_de.get(), self._pid_stop_event)
        if data is None:
            assert self._pid_stop_event.is_set()
            return
        prev_time = data.ts
        while not self._pid_stop_event.is_set():
            data = await cancellable_await(self._angle_de.get(), self._pid_stop_event)
            if data is None:
                continue
            cur_angle = data.angle
            cur_time = data.ts
            if cur_time - prev_time < 0.01:
                continue
            dt = cur_time - prev_time
            prev_time = cur_time
            error = self._lpf.update(self._desired_angle.degrees - cur_angle.degrees)
            control_signal = self._pi_controller.update(error, dt)

            # Split control signal into steer left and right commands
            steer_left_signal = max(0, min(int(control_signal * MAX_INT16), MAX_INT16))
            steer_right_signal = max(0, min(int(-control_signal * MAX_INT16), MAX_INT16))
            self.__set_valves(steer_left_signal, steer_right_signal)

    async def __enable(self) -> None:
        if self._pid_task is not None:
            LOG.info("Already enabled not re-enabling")
            return
        self._pid_stop_event.clear()
        data = await self._angle_de.get()
        self._desired_angle = data.angle
        self._lpf.reset(0.0)
        self._pi_controller.reset()
        self._pid_task = asyncio.Task(self._pid_loop(), loop=asyncio.get_event_loop())
        self._enabled = True

    async def __disable(self) -> None:
        self._pid_stop_event.set()
        if self._pid_task is not None:
            await self._pid_task
            self._pid_task = None
        self._enabled = False
        # turn valves off since we don't want control though this is handled by signal to control solenoid as well
        self.__set_valves(0, 0)

    async def set_rtc_enable(self, enabled: bool) -> None:
        if self._enabled == enabled:
            return
        if enabled:
            await self.__enable()
        else:
            await self.__disable()

    def __set_valves(self, left: int, right: int) -> None:
        left_cmd = struct.pack("<H", left)
        right_cmd = struct.pack("<H", right)

        self.__send_can_message(PGN_COMMAND_STEER_LEFT, left_cmd)
        self.__send_can_message(PGN_COMMAND_STEER_RIGHT, right_cmd)

    def __send_can_message(self, pgn: int, data: bytes) -> None:
        message = can.Message(arbitration_id=(PRIORITY << 26) | (pgn << 8) | SA, data=data, is_extended_id=True)
        self._bus.send(message)

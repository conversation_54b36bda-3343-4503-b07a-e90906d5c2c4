#!/usr/bin/env -S python3 -u
from __future__ import annotations

import asyncio
from typing import Any, Callable, Coroutine, Dict, Optional, Tuple

import can

from lib.common.asyncio.broadcast_map import BroadcastMap
from lib.drivers.steering.fault import FaultDetails


def str_to_hex_list(input: str) -> bytes:
    items = input.split(" ")
    numbers = [int(x, 16) for x in items]
    return bytes(numbers)


CTRL_CAN_ID = 0x06000001
HEARTBEAT_CAN_ID = 0x07000001
RESPONSE_CAN_ID = 0x05800001

# Controls

ENABLE_REQ = str_to_hex_list("23 0d 20 01 00 00 00 00")
ENABLE_RESP = str_to_hex_list("60 0d 20 00")

DISABLE_REQ = str_to_hex_list("23 0c 20 01 00 00 00 00")
DISABLE_RESP = str_to_hex_list("60 0c 20 00")

SPEED_CONTROL_REQ = str_to_hex_list("23 00 20 01")
SPEED_CONTROL_RESP = str_to_hex_list("60 00 20 00")

TORQUE_CONTROL_REQ = str_to_hex_list("23 01 20 01")
TORQUE_CONTROL_RESP = str_to_hex_list("60 01 20 00")

POSITION_CONTROL_REQ = str_to_hex_list("23 02 20 01")
POSITION_CONTROL_RESP = str_to_hex_list("60 02 20 00")


def fill_control_request(req: bytes, input: int) -> bytes:
    req += bytes([(input >> 8) & 0xFF, input & 0xFF, (input >> 24) & 0xFF, (input >> 16) & 0xFF,])
    return req


# Queries

CURRENT_REQ = str_to_hex_list("40 00 21 01 00 00 00 00")
CURRENT_RESP = str_to_hex_list("60 00 21 01")

SPEED_REQ = str_to_hex_list("40 03 21 01 00 00 00 00")
SPEED_RESP = str_to_hex_list("60 03 21 01")

VOLTAGE_REQ = str_to_hex_list("40 0D 21 02 00 00 00 00")
VOLTAGE_RESP = str_to_hex_list("60 0D 21 02")

FAULT_REQ = str_to_hex_list("40 12 21 01 00 00 00 00")
FAULT_RESP = str_to_hex_list("60 12 21 01")

VERSION_REQ = str_to_hex_list("40 01 11 11 00 00 00 00")
VERSION_RESP = str_to_hex_list("60 01 11 11")

STATUS_REQ = str_to_hex_list("40 01 21 01 00 00 00 00")
STATUS_RESP = str_to_hex_list("60 01 21 01")

ENCODER_REQ = str_to_hex_list("40 04 21 01 00 00 00 00")
ENCODER_RESP = str_to_hex_list("60 04 21 01")

SENSOR_REQ = str_to_hex_list("40 05 21 01 00 00 00 00")
SENSOR_RESP = str_to_hex_list("60 05 21 01")

INPUT_REQ = str_to_hex_list("40 0A 21 01 00 00 00 00")
INPUT_RESP = str_to_hex_list("60 0A 21 01")

TEMP_REQ = str_to_hex_list("40 0F 21 01 00 00 00 00")
TEMP_RESP = str_to_hex_list("60 0F 21 01")


def resp_to_resp_header(msg: can.Message) -> bytes:
    return bytes(msg.data[:4])


################################################################################
# Link to docs about steering wheel
# https://bsg-i.nbxc.com/upload/669/442/pdf/0d6c506ea3ebf97650f00dc39d.pdf
################################################################################
class MotorizedSteeringWheel:
    def __init__(self, port: str = "can0", bitrate: int = 250000, interface: str = "socketcan") -> None:
        self._bus = can.Bus(interface=interface, channel=port, bitrate=bitrate)  # type: ignore
        self._reader = can.AsyncBufferedReader()
        self._notifier = can.Notifier(self._bus, [self._reader], loop=asyncio.get_event_loop())

        self._read_task = asyncio.Task(self.read_loop(), loop=asyncio.get_event_loop())

        self._queues_lock = asyncio.Lock()
        self._queues: Dict[bytes, asyncio.Queue[can.Message]] = {}

        self._heartbeat_lock = asyncio.Lock()
        self._ticks: int = 0
        self._angle: int = 0
        self._resolution: int = 10000
        self._error: MotorizedSteeringWheel.Fault = MotorizedSteeringWheel.Fault(bytes([0, 0, 0, 0, 0, 0, 0, 0]))
        self._fault_change: BroadcastMap[bool] = BroadcastMap()

    @property
    def resolution(self) -> int:
        return self._resolution

    def rotation_to_ticks(self, rotation: float) -> int:
        return int(rotation * self.resolution)

    def ticks_to_rotation(self, ticks: int) -> float:
        return ticks / self.resolution

    def deg_to_ticks(self, deg: float) -> int:
        return self.rotation_to_ticks(deg / 360)

    def ticks_to_deg(self, ticks: int) -> float:
        return self.ticks_to_rotation(ticks) * 360

    class Fault:
        def __init__(self, data: bytes, from_heartbeat: bool = False) -> None:
            # data0 and data1 are defined in the spec
            # Also note from_heartbeat has different position AND different endianness
            self.data0 = data[6 if from_heartbeat else 5]
            self.data0_str = format(self.data0, "#010b")
            self.data1 = data[7 if from_heartbeat else 4]
            self.data1_str = format(self.data1, "#010b")
            self.details = FaultDetails(
                # data0
                less_phase=bool(self.data0 & (0x1 << 0)),
                motor_stall=bool(self.data0 & (0x1 << 1)),
                # reserved = bool(self.data0 & (0x1 << 2)),
                hall_failure=bool(self.data0 & (0x1 << 3)),
                current_sensing=bool(self.data0 & (0x1 << 4)),
                rs232_disconnected=bool(self.data0 & (0x1 << 5)),
                can_disconnected=bool(self.data0 & (0x1 << 6)),
                motor_stalled=bool(self.data0 & (0x1 << 7)),
                # data1
                disabled=bool(self.data1 & (0x1 << 0)),
                overvoltage=bool(self.data1 & (0x1 << 1)),
                hardware_protection=bool(self.data1 & (0x1 << 2)),
                eeprom=bool(self.data1 & (0x1 << 3)),
                undervoltage=bool(self.data1 & (0x1 << 4)),
                # not_applicable = bool(self.data1 & (0x1 << 5)),
                overcurrent=bool(self.data1 & (0x1 << 6)),
                mode_failure=bool(self.data1 & (0x1 << 7)),
            )

        def __repr__(self) -> str:
            val = f"Data0: {self.data0_str}\nData1: {self.data1_str}\n"
            for k, v in self.details.to_dict().items():
                val += f"{'{: <20}'.format(k)}: {v}\n"
            return val

        @property
        def faulted(self) -> bool:
            return self.details.faulted

    async def get_ticks(self) -> int:
        async with self._heartbeat_lock:
            return self._ticks

    async def register_fault_state_change(
        self,
    ) -> Tuple[Callable[[], Coroutine[Any, Any, bool]], Callable[[], Coroutine[Any, Any, None]]]:
        key = await self._fault_change.register()

        async def _getter() -> bool:
            return await self._fault_change.await_msg(key)

        async def _clear() -> None:
            await self._fault_change.unregister(key)

        return (_getter, _clear)

    async def get_error(self) -> MotorizedSteeringWheel.Fault:
        async with self._heartbeat_lock:
            return self._error

    async def get_angle(self) -> int:
        async with self._heartbeat_lock:
            return self._angle

    async def start(self) -> None:
        await self.enable()

    async def close(self) -> None:
        await self.disable()
        self._notifier.stop()
        self._bus.shutdown()

    async def _add_to_queue(self, msg: can.Message) -> None:
        header = resp_to_resp_header(msg)
        queue: Optional[asyncio.Queue[can.Message]] = None
        async with self._queues_lock:
            if header not in self._queues:
                self._queues[header] = asyncio.Queue(maxsize=10)
            queue = self._queues[header]
        try:
            queue.put_nowait(msg)
        except asyncio.QueueFull:
            async with self._queues_lock:
                self._queues[header] = asyncio.Queue(maxsize=10)

    async def _wait_on_queue(self, header: bytes) -> can.Message:
        queue: Optional[asyncio.Queue[can.Message]] = None
        async with self._queues_lock:
            if header not in self._queues:
                self._queues[header] = asyncio.Queue(maxsize=10)
            queue = self._queues[header]
        return await asyncio.wait_for(queue.get(), 0.1)

    async def print_can_message(self, msg: can.Message) -> None:
        print(msg)

    async def _process_heartbeat(self, msg: can.Message) -> None:
        # print(f"Heartbeat: {msg}")
        async with self._heartbeat_lock:
            self._angle = int.from_bytes(msg.data[0:2], "big", signed=True)
            self._ticks = int((self._angle / 360) * self._resolution)  # Note the angle will reset at 2^16
            error = MotorizedSteeringWheel.Fault(msg.data, from_heartbeat=True)
            if self._error.details != error.details:
                await self._fault_change.broadcast(error.faulted)
            self._error = error

        # Note for some reason the heartbeat is bigendian even though the rest is little endian
        # print(f"Speed: {int.from_bytes(msg.data[2:4], 'big', signed=True)}")
        # print(f"Current: {int.from_bytes(msg.data[4:6], 'big', signed=True)}")
        # print(f"Error Code: {int.from_bytes(msg.data[6:8], 'big')}")

    async def read_loop(self) -> None:
        while True:
            msg = await self._reader.get_message()
            if HEARTBEAT_CAN_ID == msg.arbitration_id:
                await self._process_heartbeat(msg)
            elif RESPONSE_CAN_ID == msg.arbitration_id:
                await self._add_to_queue(msg)
            else:
                print(f"Unknown Message: {msg}")

    async def _send_can_message(self, data: bytes) -> None:
        self._bus.send(can.Message(arbitration_id=CTRL_CAN_ID, data=data))

    async def enable(self) -> None:
        await self._send_can_message(DISABLE_REQ)
        await self._wait_on_queue(DISABLE_RESP)
        await self._send_can_message(ENABLE_REQ)
        await self._wait_on_queue(ENABLE_RESP)

    async def disable(self) -> None:
        await self._send_can_message(DISABLE_REQ)
        await self._wait_on_queue(DISABLE_RESP)

    async def get_fault(self) -> MotorizedSteeringWheel.Fault:
        await self._send_can_message(FAULT_REQ)
        resp = await self._wait_on_queue(FAULT_RESP)
        return MotorizedSteeringWheel.Fault(resp.data)

    async def get_firmware_version(self) -> str:
        await self._send_can_message(VERSION_REQ)
        msg = await self._wait_on_queue(VERSION_RESP)
        return f"{int(msg.data[4])}.{int(msg.data[5])}.{int(msg.data[6])}.{int(msg.data[7])}"

    class Status:
        def __init__(self, data: bytes) -> None:
            # The Spec is a little wrong here, Position sensor and comms mode are shifted by 4 bits
            self.control_mode = {1: "Speed Control", 3: "Absolute Position", 4: "Relative Position"}.get(
                data[4] >> 4, "Invalid"
            )
            self.position_sensor = {12: "Linear Encoder"}.get(data[5] >> 4, "Invalid")
            self.communication_mode = {2: "CAN", 3: "RS232"}.get(data[5] & 0b1111, "Invalid")

        def __repr__(self) -> str:
            val = ""
            val += f"Control Mode: {self.control_mode}\n"
            val += f"Position Sensor: {self.position_sensor}\n"
            val += f"Communication Mode: {self.communication_mode}\n"
            return val

    async def get_status(self) -> MotorizedSteeringWheel.Status:
        await self._send_can_message(STATUS_REQ)
        msg = await self._wait_on_queue(STATUS_RESP)
        return MotorizedSteeringWheel.Status(msg.data)

    # In Amps (I think, observationally determined)
    async def get_current(self) -> float:
        await self._send_can_message(CURRENT_REQ)
        msg = await self._wait_on_queue(CURRENT_RESP)
        return float(msg.data[4])

    # In Volts (I think, observationally determined)
    async def get_voltage(self) -> float:
        await self._send_can_message(VOLTAGE_REQ)
        msg = await self._wait_on_queue(VOLTAGE_RESP)
        return float(msg.data[4])

    # Returns in RPM (I think, observationally determined)
    async def get_speed(self) -> float:
        await self._send_can_message(SPEED_REQ)
        msg = await self._wait_on_queue(SPEED_RESP)
        return float(int.from_bytes(msg.data[4:6], "little"))

    # External Sensor AD query: Note: Input 0-5V, corresponding to 0~5000
    async def get_sensor(self) -> float:
        await self._send_can_message(SENSOR_REQ)
        msg = await self._wait_on_queue(SENSOR_RESP)
        return float(int.from_bytes(msg.data[4:6], "little"))

    # Analog Input query: Note: Input 0-5V, corresponding to 002-499
    async def get_input(self) -> float:
        await self._send_can_message(INPUT_REQ)
        msg = await self._wait_on_queue(INPUT_RESP)
        return float(int.from_bytes(msg.data[4:6], "little"))

    # Returns in Celcius (I think, observationally determined)
    async def get_temp(self) -> float:
        await self._send_can_message(TEMP_REQ)
        msg = await self._wait_on_queue(TEMP_RESP)
        return float(msg.data[4])

    # In ticks, 10000 ticks/rev
    async def get_encoder(self) -> int:
        await self._send_can_message(ENCODER_REQ)
        msg = await self._wait_on_queue(ENCODER_RESP)
        return int.from_bytes(msg.data[4:8], "little", signed=True)

    async def set_position(self, pos: int) -> None:
        await self._send_can_message(fill_control_request(POSITION_CONTROL_REQ, pos))
        await self._wait_on_queue(POSITION_CONTROL_RESP)

    async def test_loop(self) -> None:
        position: int = 0
        alternate = False
        while True:
            try:
                alternate = not alternate
                position = (position + 1000) % 20000
                await self.set_position(position)
                await asyncio.sleep(1)
                if alternate:
                    print(f"Expected: {position} Read: {await self.get_encoder()}")
                    print(f"Status:\n{await self.get_fault()}")
                else:
                    print(f"Expected: {position} Read: {await self.get_ticks()}")
                    print(f"Status:\n{await self.get_error()}")
            except Exception as ex:
                print(ex)


async def main() -> None:
    # Testing Script
    wheel = MotorizedSteeringWheel()

    await asyncio.sleep(1)

    print(await wheel.get_error())

    try:
        await wheel.start()

        print(await wheel.get_status())

        asyncio.Task(wheel.test_loop())

        await asyncio.sleep(30)
    finally:
        await wheel.close()


if __name__ == "__main__":
    asyncio.run(main())

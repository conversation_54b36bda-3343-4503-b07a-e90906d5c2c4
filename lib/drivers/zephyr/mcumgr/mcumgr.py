import asyncio
import logging
from typing import Awaitable, Callable, Dict, List, Optional

from firmware.release.firmware_release_manager import (
    Firmware,
    FirmwareVersion,
    get_all_firmware_versions,
    get_latest_firmware_version,
)
from lib.common.process.commands import run_command_with_output, run_command_without_output

LOG = logging.getLogger(__name__)

MCUMGR_PORT = 1337

# TODO At Some Point we can write our own mcumgr client, for now it's easier to call the CLI tool which unfortunately
# is not as open sourced as I would have liked and also in GO


class MCUBootImage:
    def __init__(self, lines: List[str]):
        image_slot = lines[0].strip().split(" ")
        self.image = int(image_slot[0].split("=")[1])
        self.slot = int(image_slot[1].split("=")[1])
        self.version = lines[1].strip().split(":")[1].strip()
        self.bootable = lines[2].strip().split(":")[1].strip().lower() == "true"
        self.flags = lines[3].strip().split(":")[1].strip().split(" ")
        if "" in self.flags:
            self.flags.remove("")
        self.hash = lines[4].strip().split(":")[1].strip()

    @staticmethod
    def parse_image_dict(stdout: str) -> Dict[int, Dict[int, "MCUBootImage"]]:
        lines = stdout.splitlines()
        lines = lines[1:]
        images: Dict[int, Dict[int, MCUBootImage]] = {}
        while len(lines) >= 5:
            image = MCUBootImage(lines)
            if image.image not in images:
                images[image.image] = {}
            images[image.image][image.slot] = image
            lines = lines[5:]
        return images

    def __repr__(self) -> str:
        return str(self.__dict__)


class MCUMGRException(Exception):
    pass


class MCUMGRClient:
    def __init__(self, ip: str, port: int = MCUMGR_PORT, executable: str = "mcumgr", build_dir: str = "build") -> None:
        self._ip = ip
        self._port = port
        self._executable = executable
        self._build_dir = build_dir

    def _get_cmd_base(self) -> List[str]:
        return [self._executable, "--conntype", "udp", "--connstring", f"[{self._ip}]:{self._port}"]

    async def get_image_dict(self) -> Dict[int, Dict[int, "MCUBootImage"]]:
        rc, stdout = await run_command_with_output(self._get_cmd_base() + ["image", "list", "--timeout", "0.2"])
        if rc != 0:
            raise MCUMGRException(f"{self._ip}:{self._port}: List Images Failed With Error Code: {rc}")

        return MCUBootImage.parse_image_dict(stdout)

    async def get_version(self) -> FirmwareVersion:
        images = await self.get_image_dict()
        if 0 not in images or 0 not in images[0]:
            return FirmwareVersion(0, 0, 0)
        major, minor, rev = [int(x) for x in images[0][0].version.split(".")]
        return FirmwareVersion(major, minor, rev)

    async def mark_new_image_for_test(self) -> None:
        images = await self.get_image_dict()
        image_hash = images[0][1].hash
        rc = await run_command_without_output(self._get_cmd_base() + ["image", "test", image_hash])
        if rc != 0:
            raise MCUMGRException(f"{self._ip}:{self._port}: Failed to Mark Image for Test With Error Code: {rc}")

    async def confirm_current_image(self) -> None:
        rc = await run_command_without_output(self._get_cmd_base() + ["image", "confirm", ""])
        if rc != 0:
            raise MCUMGRException(f"{self._ip}:{self._port}: Failed to Confirm Image With Error Code: {rc}")

    async def reset_mcu(self) -> None:
        rc = await run_command_without_output(self._get_cmd_base() + ["reset"])
        if rc != 0:
            raise MCUMGRException(f"{self._ip}:{self._port}: Failed to Reset MCU With Error Code: {rc}")
        await asyncio.sleep(1)  # We sleep here to make sure the MCU had time to actually reset

    async def upload_new_dev_image(self, project: str) -> None:
        rc = await run_command_without_output(
            self._get_cmd_base() + ["image", "upload", f"{self._build_dir}/{project}/zephyr/zephyr.signed.bin"],
            timeout_ms=15000,
        )
        if rc != 0:
            raise MCUMGRException(f"{self._ip}:{self._port}: Failed to Upload Image With Error Code: {rc}")

    async def upload_new_release_image(self, firmware_path: str) -> None:
        rc = await run_command_without_output(
            self._get_cmd_base() + ["image", "upload", f"{firmware_path}"], timeout_ms=15000
        )
        if rc != 0:
            raise MCUMGRException(f"{self._ip}:{self._port}: Failed to Upload Image With Error Code: {rc}")

    async def get_board_revision(self) -> str:
        rc, stdout = await run_command_with_output(
            self._get_cmd_base() + ["shell", "exec", "revision", "--timeout", "0.2"]
        )
        if rc != 0:
            raise MCUMGRException(f"{self._ip}:{self._port}: Getting Board Revision Failed With Error Code: {rc}")
        for line in stdout.split("\n"):
            if line.startswith("revision="):
                return line.split("=")[-1]
        raise MCUMGRException(f"{self._ip}:{self._port}: Board Revision not present in output: {stdout}")

    async def check_image_availability(self) -> bool:
        rc = await run_command_without_output(self._get_cmd_base() + ["image", "list", "--timeout", "0.2"])
        if rc != 0:
            return False
        return True

    async def check_stats_availability(self, handle_first_boot: bool = False) -> bool:
        rc, stdout = await run_command_with_output(self._get_cmd_base() + ["stat", "list", "--timeout", "0.2"])
        if rc != 0 or stdout.startswith("Error:"):
            stdout = stdout.rstrip()
            if handle_first_boot and stdout == "Error: 8":
                try:
                    await self.get_board_revision()
                    return True
                except MCUMGRException:
                    pass
            return False
        return True

    async def wait_for_board_online(self) -> None:
        while not await self.check_stats_availability(handle_first_boot=True):  # TODO Consider a maximum timeout here
            await asyncio.sleep(
                6
            )  # Must be more than 5 seconds since the bootloader waits 5 seconds after the last SMP message

    async def wait_for_bootloader_online(self) -> None:
        while not await self.check_image_availability():  # TODO Consider a maximum timeout here
            await asyncio.sleep(1)

    async def upgrade(  # noqa: C901
        self,
        project: str,
        version: Optional[str] = None,
        validation_callback: Optional[Callable[[], Awaitable[bool]]] = None,
        timeout: int = 0,
    ) -> None:
        target_firmware: Optional[Firmware] = None
        revision = await self.get_board_revision()
        if version is None:
            target_firmware = get_latest_firmware_version(f"{project}_{revision}")
        else:
            firmwares = get_all_firmware_versions(f"{project}_{revision}")
            major, minor, rev = [int(x) for x in version.split(".")]
            searched_version = FirmwareVersion(major, minor, rev)
            for firmware in firmwares:
                if firmware.version.is_equal(searched_version):
                    target_firmware = firmware
                    break
        if target_firmware is None:
            raise MCUMGRException(f"{self._ip}:{self._port}: Firmware Version Not Found: {version}")

        target_version = target_firmware.version

        if timeout > 0:
            await asyncio.wait_for(self.wait_for_bootloader_online(), timeout=timeout)
        else:
            await self.wait_for_bootloader_online()

        old_images = await self.get_image_dict()

        if 0 in old_images and 0 in old_images[0]:
            current_image = old_images[0][0]
            if "confirmed" not in current_image.flags:
                LOG.warning(f"{self._ip}:{self._port}: Current Image is not validated, reverting first...")
                await self.reset_mcu()
                if timeout > 0:
                    await asyncio.wait_for(self.wait_for_board_online(), timeout=timeout)
                else:
                    await self.wait_for_board_online()
                old_images = await self.get_image_dict()

        await self.upload_new_release_image(target_firmware.path)
        images = await self.get_image_dict()

        if 0 not in images or 1 not in images[0]:
            if len(old_images) == 0:
                # If there was no image on the device, we reupload to reproduce the expected image swap
                await self.upload_new_release_image(target_firmware.path)
                images = await self.get_image_dict()
                assert 0 in images and 1 in images[0]
            else:
                raise MCUMGRException(
                    f"{self._ip}:{self._port}: Error Reading Images On the Device, the Secondary Slot is not Present"
                )

        image = images[0][1]
        old_image = images[0][0] if 0 in images[0] else None

        major, minor, rev = [int(x) for x in image.version.split(".")]
        uploaded_version = FirmwareVersion(major, minor, rev)

        if not uploaded_version.is_equal(target_version):
            raise MCUMGRException(
                f"{self._ip}:{self._port}: Upload Seems to have Failed, target version: {target_version}, version on board: {uploaded_version}"
            )

        if old_image is None or image.hash != old_image.hash:
            await self.mark_new_image_for_test()
        await self.reset_mcu()
        if timeout > 0:
            await asyncio.wait_for(self.wait_for_board_online(), timeout=timeout)
        else:
            await self.wait_for_board_online()

        if validation_callback is not None:
            valid = await validation_callback()
        else:
            valid = True

        if valid:
            await self.confirm_current_image()
        else:
            LOG.warning(f"{self._ip}:{self._port}: Validity Check Failed, Resetting MCU to trigger revert")
            await self.reset_mcu()

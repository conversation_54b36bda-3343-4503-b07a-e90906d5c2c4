import os
from typing import List

from lib.common.process.commands import run_command_without_output


class WestException(Exception):
    pass


class WestClient:
    def __init__(self, executable: str = "west", build_dir: str = "build", path_prefix: str = ""):
        self._executable = executable
        self._build_dir = build_dir
        self._path_prefix = path_prefix

    @property
    def build_dir(self) -> str:
        return self._build_dir

    async def manual_flash(self, hex_file_path: str, board: str) -> None:
        print(f"Flashing {hex_file_path} for board {board}")
        openocd_path = {
            "nucleo_h745zi_q_m7": "thirdparty/zephyr/boards/arm/nucleo_h745zi_q/support",
            "nucleo_h745zi_q_m4": "thirdparty/zephyr/boards/arm/nucleo_h745zi_q/support",
            "nucleo_h753zi": "thirdparty/zephyr/boards/arm/nucleo_h753zi/support",
            "scanner_gd": "boards/arm/scanner_gd",
        }[board]
        rc = await run_command_without_output(
            [
                "openocd",
                "-s",
                f"{self._path_prefix}{openocd_path}",
                "-f",
                f"{self._path_prefix}{openocd_path}/openocd.cfg",
                "-c",
                "init",
                "-c",
                "targets",
                "-c",
                "reset halt",
                "-c",
                f"flash write_image erase {hex_file_path}",
                "-c",
                "reset halt",
                "-c",
                f"verify_image {hex_file_path}",
                "-c",
                "reset run",
                "-c",
                "shutdown",
            ]
        )
        if rc != 0:
            raise WestException(f"Manual Flash Failed With Error Code: {rc}")

    async def gd_enterboot_build(self) -> None:
        bootloader_bin_path = f"{self._build_dir}/carbon_bootloader/zephyr/zephyr.bin"
        enterboot_bin_path = f"{self._build_dir}/carbon_bootloader/enterboot.bin"
        print(os.getcwd())
        print(enterboot_bin_path)
        # Copy enterboot bin
        rc = await run_command_without_output(
            ["dd", f"if={bootloader_bin_path}", f"of={enterboot_bin_path}", "bs=1024", "count=16",]
        )
        if rc != 0:
            raise WestException(f"Builing Failed With Error Code: {rc}")

    async def gd_erase(self) -> None:
        rc = await run_command_without_output(
            [
                "openocd",
                "-s",
                f"{self._path_prefix}boards/arm/scanner_gd",
                "-f",
                f"{self._path_prefix}boards/arm/scanner_gd/openocd.cfg",
                "-c",
                "init",
                "-c",
                "targets",
                "-c",
                "reset halt",
                "-c",
                "flash erase_address 0x08000000 0x08140000",
                "-c",
                "shutdown",
            ]
        )
        if rc != 0:
            raise WestException(f"Erasiing Failed With Error Code: {rc}")

    async def gd_enterboot_flash_from_build(self) -> None:
        enterboot_bin_path = f"{self._build_dir}/carbon_bootloader/enterboot.bin"
        await self.gd_enterboot_flash(enterboot_bin_path)

    async def gd_enterboot_flash(self, enterboot_bin_path: str) -> None:
        # Flash enterboot bin with openocd
        rc = await run_command_without_output(
            [
                "openocd",
                "-s",
                f"{self._path_prefix}boards/arm/scanner_gd",
                "-f",
                f"{self._path_prefix}boards/arm/scanner_gd/openocd.cfg",
                "-c",
                "init",
                "-c",
                "targets",
                "-c",
                "reset halt",
                "-c",
                f"flash write_image erase {enterboot_bin_path} 0x08000000",
                "-c",
                "reset halt",
                "-c",
                f"verify_image {enterboot_bin_path} 0x08000000",
                "-c",
                "reset run",
                "-c",
                "shutdown",
            ]
        )
        if rc != 0:
            raise WestException(f"Enterboot flashing failed With Error Code: {rc}")

    async def flash(self, project: str) -> None:
        rc = await run_command_without_output(
            [self._executable, "flash", "-d", f"{self._build_dir}/{project}"]
            + (
                [
                    "--bin-file",
                    f"{self._build_dir}/{project}/zephyr/zephyr.signed.bin",
                    "--hex-file",
                    f"{self._build_dir}/{project}/zephyr/zephyr.signed.hex",
                ]
                if "carbon_bootloader" not in project
                else []
            )
        )
        if rc != 0:
            raise WestException(f"Flashing Failed With Error Code: {rc}")

    async def clean_build(self, project: str) -> None:
        rc = await run_command_without_output(["rm", "-rf", f"{self._build_dir}/{project}"])
        if rc != 0:
            raise WestException(f"Build Cleaning Failed With Error Code: {rc}")

    async def build_project(self, project: str, board: str, revision: str, macro_defines: List[str]) -> None:
        cmd = [
            self._executable,
            "build",
            "-p",
            "auto",
            "-b",
            f"{board}@{revision}",
            "-s",
            project,
            "-d",
            f"{self._build_dir}/{project}",
        ]
        if macro_defines:
            defines = " ".join(map(lambda define: f"-D{define}", macro_defines))
            cmd.append("--")
            cmd.extend(["-D", f"CMAKE_C_FLAGS={defines}"])
        rc = await run_command_without_output(cmd)
        if rc != 0:
            raise WestException(f"Build Failed With Error Code: {rc}")

    async def build_bootloader_for_project(self, project: str, board: str, revision: str) -> None:
        rc = await run_command_without_output(
            [
                self._executable,
                "build",
                "-p",
                "auto",
                "-b",
                f"{board}@{revision}",
                "-s",
                "carbon_bootloader",
                "-d",
                f"{self._build_dir}/carbon_bootloader",
                "--",
                f"-DOVERLAY_CONFIG=configs/{project}.conf",
                f"-DDTC_OVERLAY_FILE=boards/{board}.overlay",
            ]
        )
        if rc != 0:
            raise WestException(f"Bootloader Build Failed With Error Code: {rc}")

    async def sign_project(self, project: str, version: str = "0.0.0") -> None:
        # The bootloader does not have a header and so cannot be signed
        if "carbon_bootloader" in project:
            return

        rc = await run_command_without_output(
            [
                self._executable,
                "sign",
                "-d",
                f"{self._build_dir}/{project}",
                "-p",
                "thirdparty/bootloader/mcuboot/scripts/imgtool.py",
                "-t",
                "imgtool",
                "--",
                "--align",
                "32",
                "--key",
                ".crypto/carbon_mcuboot_rsa_2048_key.pem",
                "--version",
                version,
            ]
        )
        if rc != 0:
            raise WestException(f"Binary Signature Failed With Error Code: {rc}")

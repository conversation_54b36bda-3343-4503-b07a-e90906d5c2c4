#pragma once

#include <mutex>
#include <string>

#include <pylon/PylonIncludes.h>
#include <spdlog/spdlog.h>
#include <torch/torch.h>

#include "autoinit.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/utils.h"
#include <config/tree/cpp/config_tree.hpp>

namespace lib {
namespace drivers {
namespace basler {

using namespace Pylon;
using namespace lib::common::camera;

class BaslerCameraImpl : public CameraImpl {
public:
  BaslerCameraImpl(const CameraInfo &info, const CameraSettings &settings,
                   std::shared_ptr<carbon::config::ConfigTree> camera_config);
  ~BaslerCameraImpl();
  DELETE_COPY_AND_MOVE(BaslerCameraImpl)

  virtual void start_grabbing() override;
  virtual CameraImage grab() override;
  virtual void stop_grabbing() override;
  virtual int64_t update_settings(const CameraSettings &settings) override;
  virtual double get_temperature() override;

  virtual void sync_settings() override;
  virtual int64_t get_link_speed() override;

private:
  void apply_settings();
  int compute_gain_raw(const CameraSettings &camera_settings);
  String_t format_light_source_preset(const CameraSettings &settings);
  void autodetect_pixel_format();
  void apply_online_updateable_settings(const CameraSettings &settings);
  void apply_offline_updateable_settings(const CameraSettings &settings);
  void settings_update_loop();
  void update_settings_from_config();

  // Used to keep Pylon runtime initialized during lifetime of the Camera.
  BaslerAutoInitTerm term_;
  CInstantCamera camera_;
  torch::Tensor pinned_temp_image_;
  std::mutex grabbing_mutex_;
  std::mutex settings_mutex_;
  std::thread settings_update_task_;
  std::atomic<bool> stopped_;
  std::atomic<bool> pause_updating_;
};

} // namespace basler
} // namespace drivers
} // namespace lib

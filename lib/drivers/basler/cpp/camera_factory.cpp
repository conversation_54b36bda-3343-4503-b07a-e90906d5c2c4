#include <fmt/format.h>
#include <fmt/ostream.h>

#include "camera.h"
#include "camera_factory.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace drivers {
namespace basler {

std::vector<CameraInfo> BaslerCameraFactory::list_devices() {
  DeviceInfoList_t pylon_devices;
  try {
    CTlFactory &tl_factory = CTlFactory::GetInstance();
    tl_factory.EnumerateDevices(pylon_devices);
  } catch (const GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to list <PERSON>sler devices: {}", ex.what()));
  }

  std::vector<CameraInfo> devices{};
  for (auto &dev : pylon_devices) {
    CameraInfo info;
    info.vendor = CameraVendor::kBasler;
    info.serial_number = dev.GetSerialNumber().c_str();
    info.ip_address = dev.GetIpAddress().c_str();
    info.model = dev.GetModelName().c_str();
    info.handle = std::make_shared<CDeviceInfo>(dev);
    devices.emplace_back(info);
  }
  return devices;
}

std::optional<CameraInfo> BaslerCameraFactory::get_device(const CameraInfo &target_info) {
  DeviceInfoList_t pylon_devices;
  try {
    CTlFactory &tl_factory = CTlFactory::GetInstance();
    tl_factory.EnumerateDevices(pylon_devices);
  } catch (const GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to list Basler devices: {}", ex.what()));
  }

  std::vector<CameraInfo> devices{};
  for (auto &dev : pylon_devices) {
    CameraInfo info;
    info.vendor = CameraVendor::kBasler;
    info.serial_number = dev.GetSerialNumber().c_str();
    info.ip_address = dev.GetIpAddress().c_str();
    info.model = dev.GetModelName().c_str();
    info.handle = std::make_shared<CDeviceInfo>(dev);
    if (info.matches(target_info)) {
      return info.combine(target_info);
    }
  }
  return {};
}

Camera BaslerCameraFactory::create_device(const CameraInfo &info, const CameraSettings &settings,
                                          std::shared_ptr<carbon::config::ConfigTree> camera_config) {
  return Camera(
      [=](const CameraInfo &info_inner) {
        return std::make_unique<BaslerCameraImpl>(info_inner, settings, camera_config);
      },
      info);
}

} // namespace basler
} // namespace drivers
} // namespace lib

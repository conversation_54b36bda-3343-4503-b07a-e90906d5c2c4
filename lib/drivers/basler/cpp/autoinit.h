#pragma once

namespace lib {
namespace drivers {
namespace basler {

// A version of PylonAutoInitTerm that supports copying and moving.
class BaslerAutoInitTerm {
public:
  BaslerAutoInitTerm() { init(); }
  ~BaslerAutoInitTerm() { fini(); }
  BaslerAutoInitTerm(const BaslerAutoInitTerm &) { init(); }
  BaslerAutoInitTerm &operator=(const BaslerAutoInitTerm &) = default;
  BaslerAutoInitTerm(BaslerAutoInitTerm &&) { init(); }
  BaslerAutoInitTerm &operator=(BaslerAutoInitTerm &&) = default;

private:
  void init();
  void fini();
};

} // namespace basler
} // namespace drivers
} // namespace lib

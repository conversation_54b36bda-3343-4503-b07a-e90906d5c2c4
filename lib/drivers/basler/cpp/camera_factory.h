#pragma once

#include "autoinit.h"
#include "lib/common/camera/cpp/camera_factory.h"

namespace lib {
namespace drivers {
namespace basler {

using namespace lib::common::camera;

// A version of PylonAutoInitTerm that supports copying and moving.
class BaslerCameraFactory : public CameraFactory {
public:
  std::vector<CameraInfo> list_devices() override;
  std::optional<CameraInfo> get_device(const CameraInfo &info);
  Camera create_device(const CameraInfo &info, const CameraSettings &settings,
                       std::shared_ptr<carbon::config::ConfigTree> camera_config) override;
  CameraVendor get_vendor() const override { return CameraVendor::kBasler; }

  static std::shared_ptr<BaslerCameraFactory> get_instance() {
    static std::shared_ptr<BaslerCameraFactory> inst{new BaslerCameraFactory()};
    return inst;
  }

private:
  // Used to keep Pylon runtime initialized during lifetime of the CameraFactory.
  BaslerAutoInitTerm term_;
};

} // namespace basler
} // namespace drivers
} // namespace lib

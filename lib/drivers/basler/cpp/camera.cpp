#include <fmt/format.h>
#include <fmt/ostream.h>
#include <spdlog/spdlog.h>

#include "camera.h"
#include "camera_factory.h"
#include "generated/lib/common/camera/proto/camera.pb.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/time.h"

namespace lib {
namespace drivers {
namespace basler {

const int kGrabTimeoutMs = 5000;

using namespace lib::common::camera;

BaslerCameraImpl::BaslerCameraImpl(const CameraInfo &info, const CameraSettings &settings,
                                   std::shared_ptr<carbon::config::ConfigTree> camera_config)
    : CameraImpl(info, settings, camera_config), stopped_(false), pause_updating_(false) {
  if (info.vendor != CameraVendor::kBasler) {
    throw camera_error(fmt::format("Received CameraInfo for vendor {}", info.vendor));
  }

  CameraInfo matched_info = info;
  auto matched_info_opt = BaslerCameraFactory::get_instance()->get_device(info);
  if (!matched_info_opt) {
    throw camera_error(fmt::format("Failed to create camera {}", info));
  }
  matched_info = *matched_info_opt;

  try {
    CDeviceInfo &device_info = *(static_cast<CDeviceInfo *>(matched_info.handle.get()));
    CTlFactory &tl_factory = CTlFactory::GetInstance();
    IPylonDevice *device = tl_factory.CreateDevice(device_info);
    camera_.Attach(device);
    camera_.Open();
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to create camera {}: {}", matched_info, ex.what()));
  }

  set_info(matched_info);

  apply_settings();
  autodetect_pixel_format();

  with_device device_guard(*get_settings().gpu_id);
  pinned_temp_image_ =
      torch::empty({1, height_, width_}, torch::TensorOptions().pinned_memory(true).dtype(torch::kUInt8));

  settings_update_task_ = std::thread(&BaslerCameraImpl::settings_update_loop, this);
}

void BaslerCameraImpl::apply_settings() {
  auto &settings = get_settings();
  try {
    auto &nodemap = camera_.GetNodeMap();

    CIntegerParameter width_param(nodemap, "Width");
    if (settings.roi_width.has_value()) {
      width_param.SetValue(*settings.roi_width);
    } else {
      width_param.SetToMaximum();
    }
    width_ = (int)width_param.GetValue();

    CIntegerParameter height_param(nodemap, "Height");
    if (settings.roi_height.has_value()) {
      height_param.SetValue(*settings.roi_height);
    } else {
      height_param.SetToMaximum();
    }
    height_ = (int)height_param.GetValue();

    apply_online_updateable_settings(settings);
    apply_offline_updateable_settings(settings);
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to apply settings for camera {}: {}", get_info(), ex.what()));
  }
}

void BaslerCameraImpl::settings_update_loop() {
  auto bse = lib::common::bot::BotStopHandler::get().create_scoped_event(
      fmt::format("camera_settings_update_loop_{}", get_info().camera_id));

  while (!bse.is_stopped() && !stopped_) {
    try {
      update_settings_from_config();
    } catch (std::exception &ex) {
      spdlog::warn("Error updating settings {}: {}", get_info().camera_id, ex.what());
    }
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }
}

void BaslerCameraImpl::update_settings_from_config() {
  if (pause_updating_) {
    return;
  }
  std::lock_guard<std::mutex> setting_lock(settings_mutex_);
  if (!camera_.IsOpen()) {
    return;
  }
  auto &previous_settings = get_settings();
  CameraSettings new_settings = previous_settings;
  bool set_offline_settings = false;
  auto config_settings = cv::runtime::get_camera_settings(camera_config_);

  if (!camera_config_->get_node("auto_brightness_enabled")->get_value<bool>()) {
    if (previous_settings.exposure_us.has_value() && config_settings.exposure_us.has_value() &&
        config_settings.exposure_us.value() != previous_settings.exposure_us.value()) {
      new_settings.exposure_us = config_settings.exposure_us;
    }
  }

  if (previous_settings.gamma.has_value() && config_settings.gamma.has_value() &&
      config_settings.gamma.value() != previous_settings.gamma.value()) {
    new_settings.gamma = config_settings.gamma;
  }
  if (previous_settings.gain_db.has_value() && config_settings.gain_db.has_value() &&
      config_settings.gain_db.value() != previous_settings.gain_db.value()) {
    new_settings.gain_db = config_settings.gain_db;
  }
  if (previous_settings.light_source_preset.has_value() && config_settings.light_source_preset.has_value() &&
      config_settings.light_source_preset.value() != previous_settings.light_source_preset.value()) {
    new_settings.light_source_preset = config_settings.light_source_preset;
  }
  if (previous_settings.wb_ratio_red.has_value() && config_settings.wb_ratio_red.has_value() &&
      config_settings.wb_ratio_red.value() != previous_settings.wb_ratio_red.value()) {
    new_settings.wb_ratio_red = config_settings.wb_ratio_red;
  }
  if (previous_settings.wb_ratio_green.has_value() && config_settings.wb_ratio_green.has_value() &&
      config_settings.wb_ratio_green.value() != previous_settings.wb_ratio_green.value()) {
    new_settings.wb_ratio_green = config_settings.wb_ratio_green;
  }
  if (previous_settings.wb_ratio_blue.has_value() && config_settings.wb_ratio_blue.has_value() &&
      config_settings.wb_ratio_blue.value() != previous_settings.wb_ratio_blue.value()) {
    new_settings.wb_ratio_blue = config_settings.wb_ratio_blue;
  }

  if (previous_settings.roi_offset_x.has_value() && config_settings.roi_offset_x.has_value() &&
      config_settings.roi_offset_x.value() != previous_settings.roi_offset_x.value()) {
    set_offline_settings = true;
    new_settings.roi_offset_x = config_settings.roi_offset_x;
  }
  if (previous_settings.roi_offset_y.has_value() && config_settings.roi_offset_y.has_value() &&
      config_settings.roi_offset_y.value() != previous_settings.roi_offset_y.value()) {
    set_offline_settings = true;
    new_settings.roi_offset_y = config_settings.roi_offset_y;
  }
  if (previous_settings.mirror != config_settings.mirror) {
    set_offline_settings = true;
    new_settings.mirror = config_settings.mirror;
  }
  if (previous_settings.flip != config_settings.flip) {
    set_offline_settings = true;
    new_settings.flip = config_settings.flip;
  }
  if (previous_settings.strobing != config_settings.strobing) {
    set_offline_settings = true;
    new_settings.strobing = config_settings.strobing;
  }
  if (previous_settings.ptp != config_settings.ptp) {
    set_offline_settings = true;
    new_settings.ptp = config_settings.ptp;
  }

  apply_online_updateable_settings(new_settings);

  if (set_offline_settings) {
    std::unique_lock<std::mutex> lock(grabbing_mutex_);
    try {
      stop_grabbing();
      apply_offline_updateable_settings(new_settings);
      start_grabbing();
    } catch (camera_error &ex) {
      start_grabbing();
      throw ex;
    }
  }
  set_settings(new_settings);
}

int64_t BaslerCameraImpl::update_settings(const CameraSettings &settings) {
  std::lock_guard<std::mutex> setting_lock(settings_mutex_);
  auto &previous_settings = get_settings();
  CameraSettings new_settings = previous_settings;

  if (settings.exposure_us.has_value()) {
    new_settings.exposure_us = settings.exposure_us;
  }

  apply_online_updateable_settings(new_settings);

  set_settings(new_settings);

  return maka_control_timestamp_ms();
}

void BaslerCameraImpl::apply_online_updateable_settings(const CameraSettings &settings) {
  try {
    auto &nodemap = camera_.GetNodeMap();

    CEnumParameter(nodemap, "ExposureAuto").SetValue("Off");
    if (camera_.IsUsb()) {
      CFloatParameter(nodemap, "ExposureTime").SetValue(settings.exposure_us.value_or(1000.0));
    } else {
      CFloatParameter(nodemap, "ExposureTimeAbs").SetValue(settings.exposure_us.value_or(1000.0));
    }
    if (!camera_.IsUsb()) {
      CBooleanParameter(nodemap, "GammaEnable").SetValue(settings.gamma.has_value());
    }
    if (settings.gamma.has_value()) {
      CEnumParameter(nodemap, "GammaSelector").SetValue("User");
      CFloatParameter(nodemap, "Gamma").SetValue(*settings.gamma);
    }
    CEnumParameter(nodemap, "GainAuto").SetValue("Off");
    if (camera_.IsUsb()) {
      if (settings.gain_db.has_value()) {
        CFloatParameter(nodemap, "Gain").SetValue(*settings.gain_db);
      } else {
        CFloatParameter(nodemap, "Gain").SetToMinimum();
      }
    } else {
      if (settings.gain_db.has_value()) {
        CIntegerParameter(nodemap, "GainRaw").SetValue(compute_gain_raw(settings));
      } else {
        CIntegerParameter(nodemap, "GainRaw").SetToMinimum();
      }
    }

    if (camera_.IsUsb()) {
      CEnumParameter(nodemap, "LightSourcePreset").SetValue(format_light_source_preset(settings));
    } else {
      CEnumParameter(nodemap, "LightSourceSelector").SetValue(format_light_source_preset(settings));
    }
    CEnumParameter(nodemap, "BalanceRatioSelector").SetValue("Red");
    if (camera_.IsUsb()) {
      CFloatParameter(nodemap, "BalanceRatio").SetValue(settings.wb_ratio_red.value_or(1.0));
    } else {
      CFloatParameter(nodemap, "BalanceRatioAbs").SetValue(settings.wb_ratio_red.value_or(1.0));
    }
    CEnumParameter(nodemap, "BalanceRatioSelector").SetValue("Green");
    if (camera_.IsUsb()) {
      CFloatParameter(nodemap, "BalanceRatio").SetValue(settings.wb_ratio_green.value_or(1.0));
    } else {
      CFloatParameter(nodemap, "BalanceRatioAbs").SetValue(settings.wb_ratio_green.value_or(1.0));
    }
    CEnumParameter(nodemap, "BalanceRatioSelector").SetValue("Blue");
    if (camera_.IsUsb()) {
      CFloatParameter(nodemap, "BalanceRatio").SetValue(settings.wb_ratio_blue.value_or(1.0));
    } else {
      CFloatParameter(nodemap, "BalanceRatioAbs").SetValue(settings.wb_ratio_blue.value_or(1.0));
    }
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to apply online settings for camera {}: {}", get_info(), ex.what()));
  }
}

void BaslerCameraImpl::apply_offline_updateable_settings(const CameraSettings &settings) {
  try {
    auto &nodemap = camera_.GetNodeMap();

    CIntegerParameter offset_x_param(nodemap, "OffsetX");
    offset_x_param.SetValue(settings.roi_offset_x.value_or(offset_x_param.GetMax() / 2));

    CIntegerParameter offset_y_param(nodemap, "OffsetY");
    offset_y_param.SetValue(settings.roi_offset_y.value_or(offset_y_param.GetMax() / 2));

    CBooleanParameter(nodemap, "ReverseX").SetValue(settings.flip);
    CBooleanParameter(nodemap, "ReverseY").SetValue(settings.mirror);

    if (settings.strobing) {
      CEnumParameter(nodemap, "LineSelector").SetValue("Line1");
      CEnumParameter(nodemap, "LineMode").SetValue("Input");
      CEnumParameter(nodemap, "TriggerSelector").SetValue("FrameStart");
      CEnumParameter(nodemap, "TriggerSource").SetValue("Line1");
      CEnumParameter(nodemap, "TriggerMode").SetValue("On");
      CEnumParameter(nodemap, "TriggerActivation").SetValue("RisingEdge");

      CEnumParameter(nodemap, "LineSelector").SetValue("Line2");
      CEnumParameter(nodemap, "LineMode").SetValue("Output");
      CEnumParameter(nodemap, "LineSource").SetValue("ExposureActive");
    } else {
      CEnumParameter(nodemap, "TriggerMode").SetValue("Off");
    }

    CBooleanParameter(nodemap, "ChunkModeActive").SetValue(true);
    CEnumParameter(nodemap, "ChunkSelector").SetValue("ExposureTime");
    CBooleanParameter(nodemap, "ChunkEnable").SetValue(true);

    if (settings.ptp) {
      CBooleanParameter(nodemap, "GevIEEE1588").SetValue(true);
      CEnumParameter(nodemap, "ChunkSelector").SetValue("Timestamp");
      CBooleanParameter(nodemap, "ChunkEnable").SetValue(true);
    } else {
      if (!camera_.IsUsb()) {
        CBooleanParameter(nodemap, "GevIEEE1588").SetValue(false);
      }
      CEnumParameter(nodemap, "ChunkSelector").SetValue("Timestamp");
      CBooleanParameter(nodemap, "ChunkEnable").SetValue(false);
    }
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to apply offline settings for camera {}: {}", get_info(), ex.what()));
  }
}

int BaslerCameraImpl::compute_gain_raw(const CameraSettings &camera_settings) {
  if (get_info().model == "acA1300-75gc") {
    // Gain = 20 * log10 (GainRaw / 136)
    return (int)(136 * exp10(*camera_settings.gain_db / 20));
  }
  throw camera_error(fmt::format("Don't know how to compute GainRaw for camera {}", get_info().model));
}

String_t BaslerCameraImpl::format_light_source_preset(const CameraSettings &settings) {
  auto val = settings.light_source_preset.value_or(LightSourcePreset::kOff);
  switch (val) {
  case LightSourcePreset::kOff:
    return "Off";
  case LightSourcePreset::kDaylight5000K:
    return (get_info().model == "acA1300-75gc") ? "Daylight" : "Daylight5000K";
  case LightSourcePreset::kDaylight6500K:
    return "Daylight6500K";
  case LightSourcePreset::kTungsten2800K:
    return (get_info().model == "acA1300-75gc") ? "Tungsten" : "Tungsten2800K";
  default:
    throw camera_error(fmt::format("Unsupported light source preset {} for camera {}", val, get_info().model));
  }
}

void BaslerCameraImpl::autodetect_pixel_format() {
  try {
    auto &nodemap = camera_.GetNodeMap();
    CEnumParameter pixel_format_param(nodemap, "PixelFormat");

    if (pixel_format_param.TrySetValue("BayerGB8")) {
      pixel_format_ = PixelFormat::kBayerGB8;
      return;
    }
    if (pixel_format_param.TrySetValue("BayerBG8")) {
      pixel_format_ = PixelFormat::kBayerBG8;
      return;
    }
    if (pixel_format_param.TrySetValue("BayerGR8")) {
      pixel_format_ = PixelFormat::kBayerGR8;
      return;
    }
    if (pixel_format_param.TrySetValue("BayerRG8")) {
      pixel_format_ = PixelFormat::kBayerRG8;
      return;
    }
    // Fallback for Mono cameras.
    if (pixel_format_param.TrySetValue("Mono8")) {
      pixel_format_ = PixelFormat::kMono8;
      return;
    }
    throw camera_error(fmt::format("Failed to find suitable PixelFormat for camera {}", get_info()));
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to autodetect PixelFormat for camera {}: {}", get_info(), ex.what()));
  }
}

void BaslerCameraImpl::start_grabbing() {
  try {
    camera_.StartGrabbing(GrabStrategy_LatestImageOnly);
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to start grabbing for camera {}: {}", get_info(), ex.what()));
  }
}

void BaslerCameraImpl::sync_settings() { spdlog::warn("sync_settings not implemented for basler cams"); }

CameraImage BaslerCameraImpl::grab() {
  try {
    std::unique_lock<std::mutex> lock(grabbing_mutex_);
    CGrabResultPtr grab_result;
    camera_.RetrieveResult(kGrabTimeoutMs, grab_result);
    if (!grab_result->GrabSucceeded()) {
      throw grab_incomplete_error(
          fmt::format("Incomplete grab for camera {}: {}", get_info(), grab_result->GetErrorDescription().c_str()));
    }

    int64_t timestamp_ms;
    int64_t exposure_time_us =
        (int64_t)CFloatParameter(grab_result->GetChunkDataNodeMap(), "ChunkExposureTime").GetValue();
    if (get_settings().ptp) {
      timestamp_ms = ((int64_t)grab_result->GetTimeStamp() / 1000 + exposure_time_us / 2) / 1000;
    } else {
      // TODO: adjust for the expected delay.
      timestamp_ms = maka_control_timestamp_ms() - exposure_time_us / 2000;
    }

    CameraImage cam_image;
    cam_image.camera_id = get_info().camera_id;
    cam_image.timestamp_ms = timestamp_ms;
    cam_image.pixel_format = pixel_format_;
    cam_image.ppi = get_info().ppi;
    auto wrapped_image = torch::from_blob(grab_result->GetBuffer(), {height_, width_}, torch::kUInt8);
    if (get_settings().gpu_id.has_value()) {
      pinned_temp_image_.copy_(wrapped_image);
      cam_image.image = pinned_temp_image_.to({torch::kCUDA, *get_settings().gpu_id}, true);
    } else {
      cam_image.image = wrapped_image.clone();
    }
    return cam_image;
  } catch (GenICam::TimeoutException &ex) {
    throw grab_timeout_error(fmt::format("Timed out trying to grab an image for camera {}: {}", get_info(), ex.what()));
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to grab an image for camera {}: {}", get_info(), ex.what()));
  }
}

void BaslerCameraImpl::stop_grabbing() {
  try {
    camera_.StopGrabbing();
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to stop grabbing for camera {}: {}", get_info(), ex.what()));
  }
}

BaslerCameraImpl::~BaslerCameraImpl() {
  spdlog::info("Destroying {}", get_info().camera_id);
  stopped_ = true;
  settings_update_task_.join();
  try {
    camera_.Close();
    camera_.DestroyDevice();
  } catch (GenICam::GenericException &ex) {
    spdlog::warn("Failed to destroy camera {}: {}", get_info(), ex.what());
  }
}

double BaslerCameraImpl::get_temperature() {
  try {
    auto &nodemap = camera_.GetNodeMap();
    double d = CFloatParameter(nodemap, "TemperatureAbs").GetValue();
    return d;
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to get temperature {}: {}", get_info(), ex.what()));
  }
}

int64_t BaslerCameraImpl::get_link_speed() {
  return -1; // not tested on basler cams, so alarm won't fire on buds
}

} // namespace basler
} // namespace drivers
} // namespace lib

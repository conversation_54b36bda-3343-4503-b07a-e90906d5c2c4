find_package(Pylon REQUIRED)

file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp)
add_library(basler_camera SHARED ${SOURCES})

target_compile_options(basler_camera PUBLIC ${TORCH_CXX_FLAGS})
target_compile_definitions(basler_camera PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(basler_camera PRIVATE ${TORCH_LIBRARIES} m spdlog fmt ${Pylon_LIBRARIES})
target_link_directories(basler_camera PRIVATE ${Pylon_LINK_DIRECTORIES})
target_include_directories(basler_camera PRIVATE ${Pylon_INCLUDE_DIRS})

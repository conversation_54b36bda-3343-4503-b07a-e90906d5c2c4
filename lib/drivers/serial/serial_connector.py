from __future__ import annotations

import asyncio
import functools
from typing import Optional, Tuple, Union, cast

from cobs import cobs
from serial_asyncio import SerialTransport, create_serial_connection

import lib.common.logging
from lib.drivers.errors.error import RecoverableMakaDeviceException
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

LOG = lib.common.logging.get_logger(__name__)


class SerialConnector(MakaProtocolConnector):
    """
    nanopb over a asyncio serial transport (e.g. RS232/UART)
    """

    def __init__(self, serial_port: str, loop: asyncio.AbstractEventLoop, baudrate: int, delay_ms: int = 0):
        self._serial_port = serial_port
        self._delay_ms = delay_ms
        self._loop = loop
        self._read_queue: asyncio.Queue[bytes] = asyncio.Queue()
        self._transport: Optional[SerialTransport] = None
        self._protocol: Optional[CobsSerialProtocol] = None
        self._baudRate = baudrate

    def get_identifier(self) -> str:
        return self._serial_port

    def get_bootloader_type(self) -> str:
        return "RS232"

    async def set_baud_rate(self, baudrate: int) -> None:
        """
        Update the connection's baud rate

        The underlying transport is torn down and recreated.
        """
        is_open = self._transport is not None
        LOG.info(f"Switching baud rate: {self._baudRate} -> {baudrate} (open = {is_open})")

        await self.close()
        self._baudRate = baudrate
        if is_open:
            LOG.info(f"Reopening serial transport at {baudrate} baud")
            await self.open()

    async def open(self) -> None:
        self._transport, self._protocol = await self._create_connection()
        await self._protocol.ready()
        await asyncio.sleep(self._delay_ms / 1000)

    async def close(self) -> None:
        if self._transport is not None:
            self._transport.close()
        self._transport = None
        self._protocol = None

    async def _create_connection(self) -> Tuple[SerialTransport, "CobsSerialProtocol"]:
        return cast(
            Tuple[SerialTransport, CobsSerialProtocol],
            await create_serial_connection(
                self._loop,
                functools.partial(CobsSerialProtocol, read_queue=self._read_queue),
                self._serial_port,
                baudrate=self._baudRate,
            ),
        )

    def _recovery_required(self) -> None:
        if self._protocol is None:
            raise RecoverableMakaDeviceException("serial transport not yet open")

    async def read(self, timeout_ms: Optional[int] = None) -> Union[bytes, None]:
        self._recovery_required()
        assert self._protocol is not None
        return await self._read_queue.get()

    async def write(self, data: bytes) -> None:
        self._recovery_required()
        assert self._protocol is not None
        await self._protocol.write(data)


class CobsSerialProtocol(asyncio.Protocol):
    """
    Protocol wrapper for the RS232 connection; all messages are framed using COBS and delimited
    using NULL bytes.
    """

    MSG_TERMINATOR = b"\x00"

    def __init__(self, read_queue: asyncio.Queue[bytes]):
        super().__init__()
        self._transport: Optional[SerialTransport] = None
        self._read_queue = read_queue
        self._read_buffer: bytes = b""
        self._read_size: int = 0
        self._ready_event = asyncio.Event()

    async def ready(self) -> None:
        await self._ready_event.wait()

    # Overwritten from asyncio.Protocol
    def connection_made(self, transport: SerialTransport) -> None:
        """
        Underlying transport is open
        """
        self._transport = transport
        self._ready_event.set()

    # Overwritten from asyncio.Protocol
    def data_received(self, data: bytes) -> None:
        """
        Handle some received data bytes

        Data is assembled in an intermediary buffer; we'll emit an entire message at a time (as
        deliniated by the NULL bytes)
        """
        # buffer message data until terminator (NULL byte) encountered
        nullOffset = data.find(self.MSG_TERMINATOR)

        if nullOffset != -1:
            self._read_buffer += data[: nullOffset + 1]
            self._push_buffer()

            # extra bytes are for subsequent message
            remaining = data[nullOffset + 1 :]
            if len(remaining):
                self.data_received(remaining)
        # no null byte found, append entire buffer
        else:
            self._read_buffer += data

    def _push_buffer(self) -> None:
        """
        Notify that a complete packet has been read and shoved into the transport queue; this is
        where COBS decoding is performed
        """
        assert self._read_buffer[-1:] == self.MSG_TERMINATOR
        message = self._read_buffer[:-1]

        if self._transport is not None:
            decoded = cobs.decode(message)
            asyncio.ensure_future(self._read_queue.put(decoded), loop=self._transport.loop)
        self._read_buffer = b""

    def connection_lost(self, exc: Optional[Exception]) -> None:
        pass

    async def write(self, data: bytes) -> None:
        """
        Frame raw data and send via serial
        """
        data = cobs.encode(data)
        data += bytes(self.MSG_TERMINATOR)
        self._write(data)

    def _write(self, packed_data: bytes) -> None:
        if self._transport is None:
            raise RecoverableMakaDeviceException("no underlying transport (connection not open?)")
        assert asyncio.get_event_loop() == self._transport.loop

        self._transport.write(packed_data)

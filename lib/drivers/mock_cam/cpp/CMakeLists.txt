file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp)
add_library(mock_camera SHARED ${SOURCES})

target_compile_options(mock_camera PUBLIC ${TORCH_CXX_FLAGS})
target_compile_definitions(mock_camera PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(mock_camera PRIVATE ${TORCH_LIBRARIES} m spdlog fmt opencv_core opencv_imgproc opencv_imgcodecs
opencv_cudaimgproc simulator_proto)
target_include_directories(mock_camera SYSTEM PUBLIC /usr/local/include/opencv4)

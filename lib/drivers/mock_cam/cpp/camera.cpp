#include <cmath>
#include <fmt/format.h>
#include <fmt/ostream.h>
#include <opencv2/core.hpp>
#include <opencv2/imgcodecs.hpp>
#include <opencv2/imgproc.hpp>
#include <spdlog/spdlog.h>

#include "camera.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/time.h"
#include "lib/drivers/thinklucid/cpp/firmware_updater.h"

namespace lib {
namespace drivers {
namespace mock_cam {

using namespace torch::indexing;

constexpr int kBorderThickness = 4;

const cv::Scalar kBorderBGR = cv::Scalar(255, 255, 255);
const cv::Scalar kDirtBGR = cv::Scalar(83, 118, 155);

struct Polygon {
  Polygon() : num_vert(0), r(0), g(0), b(0) {}
  Polygon(int num, uint8_t r_in, uint8_t g_in, uint8_t b_in) : num_vert(num), r(r_in), g(g_in), b(b_in) {}
  int num_vert;
  uint8_t r, g, b;
};

static std::unordered_map<std::string, Polygon> weedPolygons = {
    {"BROADLEAF", Polygon(4, 0, 255, 0)},  // green square
    {"GRASS", Polygon(3, 255, 255, 0)},    // yellow triangle
    {"OFFSHOOT", Polygon(5, 148, 0, 211)}, // purple pentagon
    {"PURSLANE", Polygon(6, 255, 20, 147)} // pink hexagon
};

bool compare_size(carbon::simulator::Prediction p1, carbon::simulator::Prediction p2) {
  return p2.size_px() < p1.size_px();
}

MockCameraImpl::MockCameraImpl(const CameraInfo &info, const CameraSettings &settings,
                               std::shared_ptr<carbon::config::ConfigTree> camera_config)
    : CameraImpl(info, settings, camera_config), next_frame_time_(std::chrono::system_clock::now()),
      latest_timestamp_ms_(maka_control_timestamp_ms()) {
  if (info.vendor != CameraVendor::kMock) {
    throw camera_error(fmt::format("Received CameraInfo for vendor {}", info.vendor));
  }

  sim_client_ = std::make_shared<SimulatorServiceClient>("127.0.0.1:64544");
  set_image();
  modify_CameraInfo();
}

int64_t MockCameraImpl::set_image() {
  std::unique_lock<std::mutex> lk(mutex_);

  if (!get_settings().roi_height.has_value() || !get_settings().roi_width.has_value()) {
    throw camera_error(
        fmt::format("Simulated camera {} requires roi_height and roi_width settings", get_info().camera_id));
  }
  static_image_ = torch::zeros({3, *get_settings().roi_height, *get_settings().roi_width},
                               torch::TensorOptions().dtype(torch::kUInt8));
  width_ = (int)static_image_.size(2);
  height_ = (int)static_image_.size(1);
  pixel_format_ = PixelFormat::kRGB8;

  mat_image_ = cv::Mat(height_, width_, CV_8UC3, cv::Scalar::all(0));

  with_device device_guard(*get_settings().gpu_id);
  pinned_temp_image_ =
      torch::empty({3, height_, width_}, torch::TensorOptions().pinned_memory(true).dtype(torch::kUInt8));
  latest_timestamp_ms_ = maka_control_timestamp_ms();
  return latest_timestamp_ms_;
}

void MockCameraImpl::clear_image() {
  cv::rectangle(mat_image_, cv::Point(0, 0), cv::Point(width_, height_), kDirtBGR, cv::FILLED, cv::LINE_8);
}

void MockCameraImpl::draw_image() {
  // sort largest to smallest
  std::sort(predictions_.begin(), predictions_.end(), compare_size);
  for (auto &d : predictions_) {
    if (d.is_real()) {
      int radius = (int)d.size_px();
      int x = (int)d.x_px();
      int y = (int)d.y_px();

      if (d.type() == "CROP") {
        cv::circle(mat_image_, cv::Point(x, y), radius, cv::Scalar(255, 0, 0), cv::FILLED, cv::LINE_8); // blue
        cv::circle(mat_image_, cv::Point(x, y), radius, kBorderBGR, kBorderThickness, cv::LINE_8);
        continue;
      } else if (auto search = weedPolygons.find(d.type()); search != weedPolygons.end()) {
        Polygon poly = search->second;
        // build the points of a polygon inscribed in the radius
        cv::Point vertices[1][poly.num_vert];
        const cv::Point *ppt[1] = {vertices[0]};
        int npt[] = {poly.num_vert};
        double rand_offset = (double)rand() / (double)RAND_MAX * 6.28;
        for (int i = 0; i < poly.num_vert; i++) {
          double angle = i * 6.28 / poly.num_vert + rand_offset;
          vertices[0][i] = cv::Point(x + (int)(sin(angle) * radius), y + (int)(cos(angle) * radius));
        }

        // draw polygon with white border
        cv::fillPoly(mat_image_, ppt, npt, 1, cv::Scalar(poly.b, poly.g, poly.r), cv::LINE_8);
        for (int i = 0; i < poly.num_vert; i++) {
          cv::line(mat_image_, vertices[0][i], vertices[0][(i + 1) % poly.num_vert], kBorderBGR, kBorderThickness,
                   cv::LINE_8);
        }
      } else {
        spdlog::error(fmt::format("Simulated camera {} can't draw unknown type: {}", get_info().camera_id, d.type()));
      }
    }
  }

  if (mat_image_.isContinuous() && static_image_.is_contiguous()) {
    // mat is BGRBGR..., need to convert to standard 3 channel image representation
    auto img_data = static_image_.data_ptr<uint8_t>();
    auto mat_data = mat_image_.data;
    for (int row = 0; row < height_; row++) {
      for (int col = 0; col < width_; col++) {
        *(img_data + row * width_ + col) = *(mat_data + (row * width_ + col) * 3 + 2);
        *(img_data + width_ * height_ + row * width_ + col) = *(mat_data + (row * width_ + col) * 3 + 1);
        *(img_data + 2 * width_ * height_ + row * width_ + col) = *(mat_data + (row * width_ + col) * 3);
      }
    }
  } else {
    // Since the opencv mat is created above in set_image, it should be guaranteed to be continuous:
    // "Matrices created with Mat::create() are always continuous."
    // https://docs.opencv.org/2.4/modules/core/doc/basic_structures.html#mat-iscontinuous
    if (!mat_image_.isContinuous()) {
      throw camera_error("Mat wasn't continuous?");
    }

    // same for the tensor, it should be contiguous when created above
    if (!static_image_.is_contiguous()) {
      throw camera_error("Tensor wasn't continuous?");
    }
  }
}

void MockCameraImpl::get_detections() {
  static size_t print_counter = 0;
  try {
    auto predictions = sim_client_->get_predictions(get_info().camera_id, latest_timestamp_ms_);
    latest_timestamp_ms_ = predictions->timestamp_ms();

    clear_image();

    predictions_.clear();
    for (auto &p : predictions->predictions()) {
      predictions_.push_back(p);
    }

    draw_image();
  } catch (std::exception &e) {
    if ((++print_counter % 1000000) == 1) {
      spdlog::error("Error during get_detections: {}", e.what());
    }
  }
}

void MockCameraImpl::start_grabbing() {}

CameraImage MockCameraImpl::grab() {
  std::unique_lock<std::mutex> lk(mutex_);
  // get_detections limits the speed of grab, removed old sleep
  get_detections();
  auto settings = get_settings();
  CameraImage cam_image;
  cam_image.camera_id = get_info().camera_id;
  cam_image.timestamp_ms = latest_timestamp_ms_;
  cam_image.pixel_format = pixel_format_;
  cam_image.ppi = get_info().ppi;
  if (settings.gpu_id.has_value()) {
    pinned_temp_image_.copy_(static_image_);
    cam_image.image = pinned_temp_image_.to({torch::kCUDA, *settings.gpu_id}, true);
  } else {
    cam_image.image = static_image_.clone();
  }
  SimulatorData sim_data;
  for (auto &p : predictions_) {
    sim_data.predictions.push_back(p);
  }
  cam_image.simulator_data = sim_data;

  return cam_image;
}

void MockCameraImpl::stop_grabbing() {}

int64_t MockCameraImpl::update_settings(const CameraSettings &settings) {
  set_settings(settings);
  return maka_control_timestamp_ms();
}

MockCameraImpl::~MockCameraImpl() {}

void MockCameraImpl::modify_CameraInfo() {
  CameraInfo mock_info(get_info());

  auto firmware_version_opt = lib::drivers::thinklucid::get_latest_firmware_version(get_info().model);
  if (firmware_version_opt) {
    mock_info.firmware_version = firmware_version_opt->first.to_string();
  } else {
    mock_info.firmware_version = "0.0.0.0";
  }

  set_info(mock_info);
}

} // namespace mock_cam
} // namespace drivers
} // namespace lib

#include "generated/golang/simulator/hardware/proto/sim/sim.grpc.pb.h"
#include "generated/golang/simulator/hardware/proto/sim/sim.pb.h"
#include <grpcpp/grpcpp.h>

namespace lib {
namespace drivers {
namespace mock_cam {

class SimulatorServiceClient {
private:
  std::string addr_;
  std::shared_ptr<grpc::Channel> channel_;
  std::shared_ptr<carbon::simulator::SimulatorService::Stub> stub_;

  std::shared_ptr<carbon::simulator::SimulatorService::Stub> get_grpc_stub();
  grpc::Status exec_grpc(std::function<grpc::Status()> func);
  void reset_stub();

public:
  SimulatorServiceClient(const std::string &address);
  void ping();
  std::shared_ptr<carbon::simulator::GetNextPredictionsResponse> get_predictions(const std::string name,
                                                                                 int64_t timestamp);
};
} // namespace mock_cam
} // namespace drivers
} // namespace lib
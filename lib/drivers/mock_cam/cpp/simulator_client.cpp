#include "simulator_client.hpp"
#include <fmt/format.h>

namespace lib {
namespace drivers {
namespace mock_cam {

using namespace carbon::simulator;

SimulatorServiceClient::SimulatorServiceClient(const std::string &address) : addr_{address} {}

std::shared_ptr<SimulatorService::Stub> SimulatorServiceClient::get_grpc_stub() {
  if (this->channel_ == nullptr) {
    grpc::ChannelArguments args;
    this->channel_ = grpc::CreateCustomChannel(this->addr_, grpc::InsecureChannelCredentials(), args);
  }
  if (this->stub_ == nullptr) {
    this->stub_ = std::make_shared<SimulatorService::Stub>(this->channel_);
  }
  return this->stub_;
}

void SimulatorServiceClient::reset_stub() {
  this->stub_ = nullptr;
  this->channel_ = nullptr;
}

grpc::Status SimulatorServiceClient::exec_grpc(std::function<grpc::Status()> func) {
  try {
    return func();
  } catch (const std::exception &ex) {
    this->reset_stub();
    throw ex;
  }
}

void SimulatorServiceClient::ping() {
  grpc::ClientContext context;
  Empty request;
  std::shared_ptr<Empty> response = std::make_shared<Empty>();

  auto stub = get_grpc_stub();

  grpc::Status status =
      this->exec_grpc(std::bind(&SimulatorService::Stub::Ping, stub, &context, request, response.get()));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw std::runtime_error(fmt::format("Failed Ping: {}", status.error_message()));
  }
}

std::shared_ptr<GetNextPredictionsResponse> SimulatorServiceClient::get_predictions(const std::string name,
                                                                                    int64_t timestamp) {
  grpc::ClientContext context;
  GetNextPredictionsRequest request;
  std::shared_ptr<GetNextPredictionsResponse> response = std::make_shared<GetNextPredictionsResponse>();

  auto stub = get_grpc_stub();
  request.set_name(name);
  request.set_timestamp_ms(timestamp);

  grpc::Status status =
      this->exec_grpc(std::bind(&SimulatorService::Stub::GetNextPredictions, stub, &context, request, response.get()));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw std::runtime_error(fmt::format("Failed GetNextPredictions: {}", status.error_message()));
  }

  return response;
}

} // namespace mock_cam
} // namespace drivers
} // namespace lib
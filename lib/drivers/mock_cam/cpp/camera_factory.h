#pragma once

#include "lib/common/camera/cpp/camera_factory.h"

namespace lib {
namespace drivers {
namespace mock_cam {

using namespace lib::common::camera;

class MockCameraFactory : public CameraFactory {
public:
  std::vector<CameraInfo> list_devices() override;
  Camera create_device(const CameraInfo &info, const CameraSettings &settings,
                       std::shared_ptr<carbon::config::ConfigTree> camera_config) override;
  CameraVendor get_vendor() const override { return CameraVendor::kMock; }
};

} // namespace mock_cam
} // namespace drivers
} // namespace lib

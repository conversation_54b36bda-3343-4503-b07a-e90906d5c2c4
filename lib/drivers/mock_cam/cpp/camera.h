#pragma once

#include <opencv2/opencv.hpp>
#include <torch/torch.h>

#include "generated/golang/simulator/hardware/proto/sim/sim.grpc.pb.h"
#include "generated/golang/simulator/hardware/proto/sim/sim.pb.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/utils.h"
#include "simulator_client.hpp"
#include <config/tree/cpp/config_tree.hpp>

namespace lib {
namespace drivers {
namespace mock_cam {

using namespace lib::common::camera;

class MockCameraImpl : public CameraImpl {
public:
  MockCameraImpl(const CameraInfo &info, const CameraSettings &settings,
                 std::shared_ptr<carbon::config::ConfigTree> camera_config);
  ~MockCameraImpl();
  DELETE_COPY_AND_MOVE(MockCameraImpl)

  virtual void start_grabbing() override;
  virtual CameraImage grab() override;
  virtual void stop_grabbing() override;
  virtual int64_t update_settings(const CameraSettings &settings) override;
  virtual double get_temperature() override {
    static size_t print_counter = 0;
    if ((++print_counter % 1000000) == 1) {
      spdlog::warn("get_temperature not implemented for sim cameras");
    }
    return -1;
  }

  virtual void sync_settings() override { spdlog::warn("sync_settings not implemented on sim cams"); }
  virtual int64_t get_link_speed() override { return 125000000; };

private:
  torch::Tensor static_image_;
  cv::Mat mat_image_;
  std::chrono::system_clock::time_point next_frame_time_;
  torch::Tensor pinned_temp_image_;
  int64_t set_image();
  int64_t latest_timestamp_ms_;
  std::mutex mutex_;
  std::condition_variable cv_;
  std::vector<carbon::simulator::Prediction> predictions_;
  std::shared_ptr<SimulatorServiceClient> sim_client_;

  void clear_image();
  void draw_image();
  void get_detections();
  void modify_CameraInfo();
};

} // namespace mock_cam
} // namespace drivers
} // namespace lib

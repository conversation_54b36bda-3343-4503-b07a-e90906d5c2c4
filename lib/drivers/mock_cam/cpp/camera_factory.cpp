#include <fmt/format.h>
#include <fmt/ostream.h>

#include "camera.h"
#include "camera_factory.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace drivers {
namespace mock_cam {

std::vector<CameraInfo> MockCameraFactory::list_devices() {
  // Simulated devices are created implicitly
  return {};
}

Camera MockCameraFactory::create_device(const CameraInfo &info, const CameraSettings &settings,
                                        std::shared_ptr<carbon::config::ConfigTree> camera_config) {
  return Camera(
      [=](const CameraInfo &info_inner) {
        return std::make_unique<MockCameraImpl>(info_inner, settings, camera_config);
      },
      info);
}

} // namespace mock_cam
} // namespace drivers
} // namespace lib

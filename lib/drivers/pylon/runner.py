#!/usr/bin/env python3
from typing import Any, Dict, List, Optional

from pypylon import pylon

import lib.common.logging
from lib.drivers.pylon.device import PylonDevice

LOG = lib.common.logging.get_logger(__name__)


class pylonctl:
    @staticmethod
    def get_devices() -> List[PylonDevice]:
        ret_devices = []
        tlFactory = pylon.TlFactory.GetInstance()
        devices = tlFactory.EnumerateDevices()

        for d in devices:
            ret_devices.append(PylonDevice(d))

        if len(devices) > 0:
            LOG.debug("Found {} devices".format(len(devices)))

        return ret_devices

    @staticmethod
    def get_device_by_identity(identity_config: Dict[str, Any]) -> Optional[PylonDevice]:
        """
        Find an extract a camera with this identity from the list of available devices.
        """
        result = None
        cams = pylonctl.get_devices()
        for i, dev in enumerate(cams):
            if dev.identity_config_equiv(identity_config):
                result = dev
                break
        assert result is not None, "Could not find {} in {}".format(identity_config, [str(c) for c in cams])
        return result


if __name__ == "__main__":
    """
    When run as a stand alone module this will print out the devices available.
    Run it like this:
        python -m devices.cam.pylon.runner
    """

    devices = pylonctl.get_devices()
    for i, d in enumerate(devices):
        print(i, d)

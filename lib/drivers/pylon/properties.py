from typing import Any, Dict, Optional

PylonNodeType = Any
PylonDictType = Dict[str, Any]


class PylonCamProperties:
    NOT_IMPLEMENTED = 0
    NOT_AVAILABLE = 1
    WRITE_ONLY = 2
    READ_ONLY = 3
    READ_WRITE = 4

    EXECUTE = "EXECUTE"

    # Pylon has opaque node types - we could discover this API and fake a types class for them
    def __init__(self, node_map: Any):
        self.node_map = node_map
        self.serialized: Optional[PylonDictType] = self.serialize_tree()

    def get_all(self) -> Optional[PylonDictType]:
        return self.serialized

    def get_node_by_id(self, id: str) -> PylonNodeType:
        return self.node_map.GetNode(id)

    def serialize_tree(self) -> Optional[PylonDictType]:
        return self.serialize_node(self.node_map.GetNode("Root"))

    def set(self, key: str, value: Any) -> Optional[PylonDictType]:
        if value == PylonCamProperties.EXECUTE:
            self.node_map.GetNode(key).Execute()
        else:
            self.node_map.GetNode(key).SetValue(value)
        # The underlying Genicam API states that any mutation to the property tree
        # can cause a cascade of arbitrary mutations across the tree
        # Therefore, on each mutation, we need to re-serialize
        self.serialized = self.serialize_tree()
        return self.get_all()

    @staticmethod
    def serialize_node(node: PylonNodeType) -> Optional[PylonDictType]:

        access_mode = node.Node.GetAccessMode()
        node_type = type(node).__name__
        # Don't clutter the UI with properties that lack meaning to the user
        if access_mode < PylonCamProperties.READ_ONLY and node_type != "ICommand":
            return None

        # For manually assigning to the register
        # Probably not useful on the UI
        if node_type == "IRegister":
            return None
        if node_type == "IBoolean":
            return PylonCamProperties.serialize_checkbox(node)
        if node_type == "ICommand":
            if access_mode < PylonCamProperties.WRITE_ONLY:
                return None
            return PylonCamProperties.serialize_button(node)
        if node_type == "IEnumeration":
            return PylonCamProperties.serialize_select(node)
        if node_type == "IInteger":
            return PylonCamProperties.serialize_integer(node)
        if node_type == "IFloat":
            return PylonCamProperties.serialize_float(node)
        if node_type == "IString":
            return PylonCamProperties.serialize_string(node)
        if node_type == "ICategory":
            return PylonCamProperties.serialize_section(node)
        raise Exception("Unknown node type {}".format(node_type))

    @staticmethod
    def serialize_common(node: PylonNodeType) -> PylonDictType:
        inner_node = node.Node
        return {
            "id": inner_node.Name,
            "display_name": inner_node.DisplayName,
        }

    @staticmethod
    def serialize_value(node: PylonNodeType) -> PylonDictType:
        return {
            "value": node.Value,
            "disabled": node.GetAccessMode() <= PylonCamProperties.READ_ONLY,
            **PylonCamProperties.serialize_common(node),
        }

    @staticmethod
    def serialize_numeric(node: PylonNodeType) -> PylonDictType:
        return {
            "min": node.Min,
            "max": node.Max,
            **PylonCamProperties.serialize_value(node),
        }

    @staticmethod
    def serialize_integer(node: PylonNodeType) -> PylonDictType:
        return {
            "type": "integer",
            "step": node.GetInc(),
            **PylonCamProperties.serialize_numeric(node),
        }

    @staticmethod
    def serialize_float(node: PylonNodeType) -> PylonDictType:
        return {
            "type": "float",
            "precision": node.DisplayPrecision,
            "step": None if not node.HasInc() else node.GetInc(),
            **PylonCamProperties.serialize_numeric(node),
        }

    @staticmethod
    def serialize_string(node: PylonNodeType) -> PylonDictType:
        return {
            "type": "string",
            **PylonCamProperties.serialize_value(node),
        }

    @staticmethod
    def serialize_button(node: PylonNodeType) -> PylonDictType:
        return {
            "type": "button",
            **PylonCamProperties.serialize_common(node),
        }

    @staticmethod
    def serialize_select(node: PylonNodeType) -> PylonDictType:
        return {
            "type": "select",
            "options": node.Symbolics,
            **PylonCamProperties.serialize_value(node),
        }

    @staticmethod
    def serialize_checkbox(node: PylonNodeType) -> PylonDictType:
        return {
            "type": "checkbox",
            **PylonCamProperties.serialize_value(node),
        }

    @staticmethod
    def serialize_section(node: PylonNodeType) -> Optional[PylonDictType]:
        children = [PylonCamProperties.serialize_node(child) for child in node.Node.Children]
        children = [child for child in children if child is not None]
        if not children:
            return None
        return {
            "type": "section",
            "children": children,
            **PylonCamProperties.serialize_common(node),
        }

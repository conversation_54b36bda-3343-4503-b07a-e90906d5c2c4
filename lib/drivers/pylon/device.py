from typing import Dict, Optional, cast

from pypylon import pylon


class PylonDevice:
    """
    The underlying pylon device
    """

    def __init__(self, device: pylon.DeviceInfo):
        self.device = device
        type_str = self._get_property("ModelName")
        if type_str is None:
            raise ValueError("<PERSON>sler camera has no ModelName?")
        self.type_str = type_str

        self.device_guid = self._get_property("DeviceGUID")
        self.sn = self._get_property("SerialNumber")
        assert (
            self.device_guid is not None or self.sn is not None
        ), f"Basler camera {self.type_str} has neither DeviceGUID nor SerialNumber"

    def _get_property(self, name: str) -> Optional[str]:
        ok, value = self.device.GetPropertyValue(name)
        if ok:
            return cast(str, value)
        else:
            return None

    def __str__(self) -> str:
        return f"{self.type_str} sn: {self.sn} guid: {self.device_guid}"

    def identity_config(self) -> Dict[str, str]:
        cfg = {"type": self.type_str}
        if self.device_guid is not None:
            cfg["device"] = self.device_guid
        if self.sn is not None:
            cfg["sn"] = self.sn
        return cfg

    def identity_config_equiv(self, cfg: Dict[str, str]) -> bool:
        return cfg["type"] == self.type_str and (
            (self.sn is not None and cfg.get("sn") == self.sn)
            or (self.device_guid is not None and cfg.get("device") == self.device_guid)
        )

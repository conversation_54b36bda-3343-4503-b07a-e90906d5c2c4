import socket
import struct
import threading
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Type

import msgpack
import numpy as np
import numpy.typing as npt
from dataclass_wizard import JSONWizard

from lib.common.bot.stop_handler import bot_stop_handler  # noqa: E402
from lib.common.logging import get_logger

LOG = get_logger(__name__)
__all__ = [
    "SickScan",
    "SickScanSegment",
    "SickFrame",
    "SickLidar",
]

# Docs:
# * Telegram listing - https://cdn.sick.com/media/docs/7/27/927/telegram_listing_telegram_listing_ranging_sensors_lms1xx_lms5xx_tim2xx_tim5xx_tim7xx_lms1000_mrs1000_mrs6000_nav310_ld_oem15xx_ld_lrs36xx_lms4000_lrs4000_multiscan100_picoscan100_en_im0045927.pdf
# * Data format description - https://cdn.sick.com/media/docs/3/23/623/technical_information_data_format_description_en_im0104623.pdf

SICK_classname = 0x10
SICK_data = 0x11
SICK_numOfElems = 0x12
SICK_elemSz = 0x13
SICK_endian = 0x14
SICK_elemTypes = 0x15

SICK_little = 0x30
SICK_float32 = 0x31
SICK_uint32 = 0x32
SICK_uint8 = 0x33
SICK_uint16 = 0x34

SICK_ChannelTheta = 0x50
SICK_ChannelPhi = 0x51
SICK_DistValues = 0x52
SICK_RssiValues = 0x53
SICK_PropertyValues = 0x54

SICK_Scan = 0x70
SICK_TimestampStart = 0x71
SICK_TimestampStop = 0x72
SICK_ThetaStart = 0x73
SICK_ThetaStop = 0x74
SICK_ScanNumber = 0x75
SICK_ModuleId = 0x76
SICK_BeamCount = 0x77
SICK_EchoCount = 0x78

SICK_ScanSegment = 0x90
SICK_SegmentCounter = 0x91
SICK_FrameNumber = 0x92
SICK_Availability = 0x93
SICK_SenderId = 0x94
SICK_SegmentData = 0x96

SICK_LayerId = 0xA0
SICK_TelegramCounter = 0xB0
SICK_TimestampTransmit = 0xB1


@dataclass
class SickScan(JSONWizard):
    TimestampStart: int
    TimestampStop: int
    ThetaStart: int
    ThetaStop: int
    ScanNumber: int
    ModuleId: int
    ChannelTheta: npt.NDArray[Any]
    ChannelPhi: npt.NDArray[Any]
    DistValues: npt.NDArray[Any]
    RssiValues: npt.NDArray[Any]
    PropertyValues: npt.NDArray[Any]
    BeamCount: int
    EchoCount: int


@dataclass
class SickScanSegment(JSONWizard):
    TelegramCounter: int
    TimestampTransmit: int
    SegmentCounter: int
    FrameNumber: int
    Availability: bool
    SenderId: int
    LayerId: npt.NDArray[Any]
    SegmentData: List[SickScan]


@dataclass
class SickFrame(JSONWizard):
    Segments: List[SickScanSegment]


def unpack_scan(o: Dict[Any, Any]) -> SickScan:
    return SickScan(
        TimestampStart=o[SICK_TimestampStart],
        TimestampStop=o[SICK_TimestampStop],
        ThetaStart=o[SICK_ThetaStart],
        ThetaStop=o[SICK_ThetaStop],
        ScanNumber=o[SICK_ScanNumber],
        ModuleId=o[SICK_ModuleId],
        ChannelTheta=o[SICK_ChannelTheta],
        ChannelPhi=o[SICK_ChannelPhi],
        DistValues=o[SICK_DistValues],
        RssiValues=o[SICK_RssiValues],
        PropertyValues=o[SICK_PropertyValues],
        BeamCount=o[SICK_BeamCount],
        EchoCount=o[SICK_EchoCount],
    )


def unpack_scan_segment(o: Dict[Any, Any]) -> SickScanSegment:
    return SickScanSegment(
        TelegramCounter=o[SICK_TelegramCounter],
        TimestampTransmit=o[SICK_TimestampTransmit],
        SegmentCounter=o[SICK_SegmentCounter],
        FrameNumber=o[SICK_FrameNumber],
        Availability=o[SICK_Availability],
        SenderId=o[SICK_SenderId],
        LayerId=o[SICK_LayerId],
        SegmentData=o[SICK_SegmentData],
    )


def unpack_array(o: Dict[Any, Any]) -> npt.NDArray[Any]:
    assert len(o[SICK_elemTypes]) == 1, o
    elemType = o[SICK_elemTypes][0]
    dtype: Type[Any]
    if elemType == SICK_float32:
        dtype = np.float32
    elif elemType == SICK_uint32:
        dtype = np.uint32
    elif elemType == SICK_uint8:
        dtype = np.uint8
    elif elemType == SICK_uint16:
        dtype = np.uint16
    else:
        assert False, o
    assert o[SICK_endian] == SICK_little
    return np.frombuffer(o[SICK_data], dtype=np.dtype(dtype).newbyteorder("<"))


def unpack_object(o: Dict[Any, Any]) -> Any:
    if SICK_classname in o and o[SICK_classname] == SICK_Scan:
        return unpack_scan(o[SICK_data])

    if SICK_classname in o and o[SICK_classname] == SICK_ScanSegment:
        return unpack_scan_segment(o[SICK_data])

    if SICK_numOfElems in o:
        return unpack_array(o)

    return o


class SickLidar:
    def __init__(self, bind_ip: str, bind_port: int, ip: str, port: int) -> None:
        self.bind_ip = bind_ip
        self.bind_port = bind_port
        self.udp_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.udp_sock.bind((bind_ip, bind_port))
        self._last_grab_timestamp: Optional[int] = None
        self._last_grab_timestamp_maka: Optional[int] = None

        self.tcp_sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.tcp_sock.connect((ip, port))

        self.latest_frame: Optional[SickFrame] = None
        self.latest_frame_lock = threading.Lock()
        self.stop_event = threading.Event()
        self.grab_loop_thread: Optional[threading.Thread] = None

    def _send_command(self, command: str) -> None:
        self.tcp_sock.send(b"\02" + command.encode() + b"\03")

    def start_stream(self) -> None:
        self._send_command("sMN SetAccessMode 3 ********")  # set authorization level for writing settings
        self._send_command(
            f"sWN ScanDataEthSettings 1 +{' +'.join(self.bind_ip.split('.'))} +{self.bind_port}"
        )  # configure destination scan data output destination
        self._send_command("sWN ScanDataFormat 1")  # set scan data output format to MSGPACK
        self._send_command("sWN ScanDataEnable 1")  # enable scan data ouput
        self._send_command("sWN FREchoFilter 2")  # Set echo filter to return the last echo
        self._send_command("sMN Run")  # apply the settings and logout

        self.grab_loop_thread = threading.Thread(target=self.grab_loop)
        self.grab_loop_thread.start()

    def stop_stream(self) -> None:
        self._send_command("sMN SetAccessMode 3 ********")  # set authorization level for writing settings
        self._send_command("sWN ScanDataEnable 0")  # disable scan data ouput
        self._send_command("sMN Run")  # apply the settings and logout
        self.stop_event.set()
        if self.grab_loop_thread is not None:
            self.grab_loop_thread.join()
            self.grab_loop_thread = None
        self.stop_event.clear()

    def grab_loop(self) -> None:
        segment_counter = 0
        while not bot_stop_handler.stopped and not self.stop_event.is_set():
            try:
                frame = SickFrame(Segments=[])
                while True:
                    data, addr = self.udp_sock.recvfrom(65536)
                    if data.startswith(b"\02\02\02\02"):
                        data_len = struct.unpack("<l", data[4:8])[0]
                        unpacked = msgpack.unpackb(
                            data[8 : (data_len + 8)], strict_map_key=False, object_hook=unpack_object
                        )
                        if segment_counter == unpacked.SegmentCounter:
                            frame.Segments.append(unpacked)
                            segment_counter += 1
                            if segment_counter == 12:
                                break
                        else:
                            # out of sequence segment
                            segment_counter = 0
                            frame.Segments = []
                    else:
                        LOG.warning("Received malformed packet in SICK driver")
                with self.latest_frame_lock:
                    self.latest_frame = frame
            except Exception as e:
                LOG.warning(f"Failed to retrieve frame: {e}")

    def grab(self) -> Optional[SickFrame]:
        with self.latest_frame_lock:
            latest_frame = self.latest_frame
        return latest_frame

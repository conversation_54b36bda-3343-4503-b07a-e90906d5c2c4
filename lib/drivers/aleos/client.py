import asyncio
import json
from typing import Any, Dict, Optional, cast

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)


class AleosException(Exception):
    pass


class AleosClient:
    # TODO At Some point if it becomes necessary we can look at reusing connections etc.
    def __init__(self, ip: str = "*********", port: int = 4243) -> None:
        self._ip = ip
        self._port = port
        self._reader: Optional[asyncio.StreamReader] = None
        self._writer: Optional[asyncio.StreamWriter] = None

    async def _close(self) -> None:
        if self._writer is not None:
            self._writer.close()
            await self._writer.wait_closed()
        self._writer = None
        self._reader = None

    async def _make_request(self, request: Dict[str, Any], timeout_ms: int = 3000) -> Dict[str, Any]:
        if self._reader is None or self._writer is None:
            if self._writer is not None:
                await self._close()
            self._reader, self._writer = await asyncio.wait_for(
                asyncio.open_connection(self._ip, self._port), timeout=timeout_ms / 1000
            )
        try:
            assert self._writer is not None
            assert self._reader is not None
            request_str: str = json.dumps(request)
            self._writer.write(request_str.encode())
            self._writer.write("\n".encode())
            await self._writer.drain()

            response_str = (await asyncio.wait_for(self._reader.read(), timeout=timeout_ms / 1000)).decode()
            response = json.loads(response_str)
            return cast(Dict[str, Any], response)
        except ValueError:
            raise AleosException(f"Malformed Response from ALEOS: {response_str}")
        finally:
            await self._close()

    async def get_param(self, param: str) -> Dict[str, Any]:
        request = {"req": "getParam", "data": {"path": param}}
        return await self._make_request(request)

    async def get_all_params(self, prefix: str = "") -> None:
        all_params: Dict[str, Any] = {}
        await self._get_all_params(prefix, all_params)
        print(all_params)

    async def _get_all_params(self, prefix: str, all_params: Dict[str, Any]) -> None:
        response = await self.get_param(prefix)
        if "subtree" in response:
            print(prefix)
            for sub in response["subtree"]:
                await self._get_all_params(sub, all_params)
        elif "value" in response:
            print(f"{prefix}: {response['value']}")
            all_params[prefix] = response["value"]
        else:
            print(response)

/**
 * Blink
 *
 * Turns on an LED on for one second,
 * then off for one second, repeatedly.
 */
#include "Arduino.h"
#include "ack.pb.h"
#include "generated/lib/drivers/nanopb/proto/pin_controller.pb.h"
#include <pb_decode.h>
#include <pb_encode.h>

#ifndef LED_BUILTIN
#define LED_BUILTIN 13
#endif

#define MAJOR_VERSION 1
#define MINOR_VERSION 0

#define SERIAL_MAGIC 0x42
#define SERIAL_PACKET_CMD 0x87
#define SERIAL_PING_CMD 0xFA
#define SERIAL_ECHO_CMD 0xBB

void setup() {
  Serial.begin(115200);
  while (!Serial)
    ;

  //---------------------------------------------- Set PWM frequency for D4 & D13 ------------------------------

  TCCR0B = TCCR0B & ********* | *********; // set timer 0 divisor to     1 for PWM frequency of 62500.00 Hz
  // TCCR0B = TCCR0B & ********* | *********;    // set timer 0 divisor to     8 for PWM frequency of  7812.50 Hz
  // TCCR0B = TCCR0B & ********* | *********;    <// set timer 0 divisor to    64 for PWM frequency of   976.56 Hz
  // (Default) TCCR0B = TCCR0B & ********* | *********;    // set timer 0 divisor to   256 for PWM frequency of   244.14
  // Hz TCCR0B = TCCR0B & ********* | *********;    // set timer 0 divisor to  1024 for PWM frequency of    61.04 Hz

  //---------------------------------------------- Set PWM frequency for D11 & D12 -----------------------------

  TCCR1B = TCCR1B & ********* | *********; // set timer 1 divisor to     1 for PWM frequency of 31372.55 Hz
  // TCCR1B = TCCR1B & ********* | *********;    // set timer 1 divisor to     8 for PWM frequency of  3921.16 Hz
  // TCCR1B = TCCR1B & ********* | *********;    // set timer 1 divisor to    64 for PWM frequency of   490.20 Hz
  // TCCR1B = TCCR1B & ********* | *********;    // set timer 1 divisor to   256 for PWM frequency of   122.55 Hz
  // TCCR1B = TCCR1B & ********* | *********;    // set timer 1 divisor to  1024 for PWM frequency of    30.64 Hz

  //---------------------------------------------- Set PWM frequency for D9 & D10 ------------------------------

  TCCR2B = TCCR2B & ********* | *********; // set timer 2 divisor to     1 for PWM frequency of 31372.55 Hz
  // TCCR2B = TCCR2B & ********* | *********;    // set timer 2 divisor to     8 for PWM frequency of  3921.16 Hz
  // TCCR2B = TCCR2B & ********* | *********;    // set timer 2 divisor to    32 for PWM frequency of   980.39 Hz
  // TCCR2B = TCCR2B & ********* | *********;    // set timer 2 divisor to    64 for PWM frequency of   490.20 Hz
  // TCCR2B = TCCR2B & ********* | *********;    // set timer 2 divisor to   128 for PWM frequency of   245.10 Hz
  // TCCR2B = TCCR2B & ********* | B00000110;    // set timer 2 divisor to   256 for PWM frequency of   122.55 Hz
  // TCCR2B = TCCR2B & ********* | B00000111;    // set timer 2 divisor to  1024 for PWM frequency of    30.64 Hz

  //---------------------------------------------- Set PWM frequency for D2, D3 & D5 ---------------------------

  TCCR3B = TCCR3B & ********* | *********; // set timer 3 divisor to     1 for PWM frequency of 31372.55 Hz
  // TCCR3B = TCCR3B & ********* | *********;    // set timer 3 divisor to     8 for PWM frequency of  3921.16 Hz
  // TCCR3B = TCCR3B & ********* | *********;    // set timer 3 divisor to    64 for PWM frequency of   490.20 Hz
  // TCCR3B = TCCR3B & ********* | *********;    // set timer 3 divisor to   256 for PWM frequency of   122.55 Hz
  // TCCR3B = TCCR3B & ********* | *********;    // set timer 3 divisor to  1024 for PWM frequency of    30.64 Hz

  //---------------------------------------------- Set PWM frequency for D6, D7 & D8 ---------------------------

  TCCR4B = TCCR4B & ********* | *********; // set timer 4 divisor to     1 for PWM frequency of 31372.55 Hz
  // TCCR4B = TCCR4B & ********* | *********;    // set timer 4 divisor to     8 for PWM frequency of  3921.16 Hz
  // TCCR4B = TCCR4B & ********* | *********;    // set timer 4 divisor to    64 for PWM frequency of   490.20 Hz
  // TCCR4B = TCCR4B & ********* | *********;    // set timer 4 divisor to   256 for PWM frequency of   122.55 Hz
  // TCCR4B = TCCR4B & ********* | *********;    // set timer 4 divisor to  1024 for PWM frequency of    30.64 Hz

  //---------------------------------------------- Set PWM frequency for D44, D45 & D46 ------------------------

  TCCR5B = TCCR5B & ********* | *********; // set timer 5 divisor to     1 for PWM frequency of 31372.55 Hz
  // TCCR5B = TCCR5B & ********* | *********;    // set timer 5 divisor to     8 for PWM frequency of  3921.16 Hz
  // TCCR5B = TCCR5B & ********* | *********;    // set timer 5 divisor to    64 for PWM frequency of   490.20 Hz
  // TCCR5B = TCCR5B & ********* | *********;    // set timer 5 divisor to   256 for PWM frequency of   122.55 Hz
  // TCCR5B = TCCR5B & ********* | *********;    // set timer 5 divisor to  1024 for PWM frequency of    30.64 Hz
}

uint8_t buffer[64];
bool ready = false;

void writeSerial(uint8_t *data, uint16_t size, uint8_t function) {
  uint8_t header[] = {SERIAL_MAGIC, function, (uint8_t)(size % 256), (uint8_t)(size / 256)};
  Serial.write(header, 4);
  Serial.write(data, size);
  Serial.flush();
}

int getPinMode(pin_controller_PinMode mode) {
  switch (mode) {
  case pin_controller_PinMode_INPUT:
    return INPUT;
  case pin_controller_PinMode_OUTPUT:
    return OUTPUT;
  case pin_controller_PinMode_INPUT_PULL_UP:
    return INPUT_PULLUP;
  default:
    return INPUT;
  }
}

int getPinValue(pin_controller_DigitalPinValue value) {
  switch (value) {
  case pin_controller_DigitalPinValue_HIGH:
    return HIGH;
  case pin_controller_DigitalPinValue_LOW:
    return LOW;
  default:
    return LOW;
  }
}

pin_controller_DigitalPinValue getNanoValue(int value) {
  switch (value) {
  case HIGH:
    return pin_controller_DigitalPinValue_HIGH;
  case LOW:
    return pin_controller_DigitalPinValue_LOW;
  default:
    return pin_controller_DigitalPinValue_LOW;
  }
}

void handlePinRequest(pin_controller_PinRequest *request, pin_controller_PinReply *reply) {
  switch (request->which_request) {
  case pin_controller_PinRequest_pin_tag:
    pinMode(request->request.pin.pin_number, getPinMode(request->request.pin.pin_mode));
    break;
  case pin_controller_PinRequest_pwm_tag:
    analogWrite(request->request.pwm.pin_number, request->request.pwm.value_8bit);
    break;
  case pin_controller_PinRequest_write_tag:
    digitalWrite(request->request.write.pin_number, getPinValue(request->request.write.value));
    break;
  case pin_controller_PinRequest_read_tag:
    reply->which_reply = pin_controller_PinReply_read_tag;
    reply->reply.read.pin_number = request->request.write.pin_number;
    reply->reply.read.value = getNanoValue(digitalRead(request->request.write.pin_number));
    break;
  case pin_controller_PinRequest_analog_tag:
    reply->which_reply = pin_controller_PinReply_analog_tag;
    reply->reply.analog.pin_number = request->request.analog.pin_number;
    reply->reply.analog.value = analogRead(request->request.analog.pin_number);
    break;
  }
}

void handlePinControllerRequest(pin_controller_Request *request, pin_controller_Reply *reply) {
  switch (request->which_request) {
  case pin_controller_Request_ping_tag:
    reply->which_reply = pin_controller_Reply_pong_tag;
    reply->reply.pong.x = request->request.ping.x;
    break;
  case pin_controller_Request_version_tag:
    reply->which_reply = pin_controller_Reply_version_tag;
    reply->reply.version.major = MAJOR_VERSION;
    reply->reply.version.minor = MINOR_VERSION;
    break;
  case pin_controller_Request_pin_tag:
    reply->which_reply = pin_controller_Reply_pin_tag;
    reply->reply.pin.which_reply = pin_controller_PinReply_ack_tag;
    reply->reply.pin.reply.ack = ack_Ack();
    handlePinRequest(&request->request.pin, &reply->reply.pin);
    break;
  default:
    break;
  }
}

void handlePacket(uint8_t *data, uint16_t size) {
  static pin_controller_Request request;
  static pb_istream_t istream;
  static bool status;

  istream = pb_istream_from_buffer(data, size);
  status = pb_decode(&istream, pin_controller_Request_fields, &request);

  if (!status) {

    return;
  }

  if (!request.has_header) {
    return;
  }

  static pin_controller_Reply reply;
  reply.has_header = true;
  reply.header = request.header;

  reply.which_reply = pin_controller_Reply_ack_tag;
  reply.reply.ack = ack_Ack();

  handlePinControllerRequest(&request, &reply);

  static uint8_t reply_buf[64];
  static pb_ostream_t ostream;
  ostream = pb_ostream_from_buffer(reply_buf, pin_controller_Reply_size);
  status = pb_encode(&ostream, pin_controller_Reply_fields, &reply);

  if (!status || ostream.bytes_written == 0) {
    return;
  }

  writeSerial(reply_buf, ostream.bytes_written, SERIAL_PACKET_CMD);
}

void serialEvent() {
  while (Serial.available()) {
    uint8_t header[4];
    size_t header_bytes_read = Serial.readBytes(header, 4);

    if (header_bytes_read != 4) {
      continue;
    }

    uint8_t magic = header[0];

    if (magic != SERIAL_MAGIC) {
      continue;
    }

    uint8_t function = header[1];

    if (function != SERIAL_ECHO_CMD && function != SERIAL_PING_CMD && function != SERIAL_PACKET_CMD) {
      continue;
    }

    uint8_t low_size = header[2];
    uint8_t high_size = header[3];

    uint16_t size = ((uint16_t)high_size) * 256 + low_size;
    size_t bytes_read = Serial.readBytes(buffer, size);

    switch (function) {
    case SERIAL_PACKET_CMD:
      handlePacket(buffer, bytes_read);
      break;
    case SERIAL_PING_CMD:
      writeSerial(buffer, bytes_read, SERIAL_PING_CMD);
      break;
    case SERIAL_ECHO_CMD:
      writeSerial(buffer, bytes_read, SERIAL_ECHO_CMD);
      break;
    }
  }
}

void loop() {}
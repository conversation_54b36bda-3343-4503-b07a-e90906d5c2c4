{"build": {"arduino": {"ldscript": "flash_with_bootloader.ld"}, "core": "a<PERSON><PERSON><PERSON>", "cpu": "cortex-m3", "extra_flags": "-DARDUI<PERSON>O_SAMD_MKRZERO -D__SAMD21G18A__ -DUSE_ARDUINO_MKR_PIN_LAYOUT", "f_cpu": "48000000L", "hwids": [["0x1354", "0x4000"], ["0x1354", "0x4001"]], "mcu": "samd21g18a", "usb_product": "P1AM-100", "variant": "mkrzero"}, "debug": {"jlink_device": "ATSAMD21G18", "openocd_chipname": "at91samd21g18", "openocd_target": "at91samdXX", "svd_path": "ATSAMD21G18A.svd"}, "frameworks": ["a<PERSON><PERSON><PERSON>"], "name": "P1AM-100", "upload": {"disable_flushing": true, "maximum_ram_size": 32768, "maximum_size": 262144, "native_usb": true, "offset_address": "0x2000", "protocol": "sam-ba", "protocols": ["sam-ba", "blackmagic", "jlink", "atmel-ice"], "require_upload_port": false, "use_1200bps_touch": false, "wait_for_upload_port": false}, "url": "https://www.automationdirect.com/adc/shopping/catalog/programmable_controllers/open_source_controllers_(arduino-compatible)", "vendor": "FACTS Engineering, LLC"}
; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:mkrzero]
platform = atmelsam
framework = arduino
board = mkrzero
lib_deps = 
	nanopb/Nanopb@^0.4.5
	facts-engineering/P1AM@^1.0.5
build_flags = -I ../../../../ -DPB_CONVERT_DOUBLE_FLOAT

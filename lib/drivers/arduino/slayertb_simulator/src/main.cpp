/*SLAYERTB_ARDUINO
         _____  _____  _____  _____  _____
        |  P  ||  S  ||  S  ||  S  ||  S  |
        |  1  ||  L  ||  L  ||  L  ||  L  |
        |  A  ||  O  ||  O  ||  O  ||  O  |
        |  M  ||  T  ||  T  ||  T  ||  T  |
        |  -  ||     ||     ||     ||     |
        |  1  ||  0  ||  0  ||  0  ||  0  |
        |  0  ||  1  ||  2  ||  3  ||  4  |
        |  0  ||     ||     ||     ||     |
         ¯¯¯¯¯  ¯¯¯¯¯  ¯¯¯¯¯  ¯¯¯¯¯  ¯¯¯¯¯

  SLOT 0 - P1AM-100
  SLOT 1 - P1-04DAL-2
  SLOT 2 - P1-16CDR
  SLOT 3 - P1-08TRS
  SLOT 4 - P1-08ADL-2

*/
#include "Arduino.h"
#include "ack.pb.h"
#include "generated/lib/drivers/nanopb/proto/slayertb_simulator.pb.h"
#include <P1AM.h>
#include <pb_decode.h>
#include <pb_encode.h>

#define SERIAL_MAGIC 0x42
#define SERIAL_PACKET_CMD 0x87
#define SERIAL_PING_CMD 0xFA
#define SERIAL_ECHO_CMD 0xBB

// SLOT 1
// P1-04DAL-2
// 4x Analog Output 0-10V
channelLabel tempOutput = {1,
                           1}; // Temperature sent to supervisory PLC.  0 to 4095 = -40 to 60C (might need calibration)
channelLabel humdOutput = {1, 2}; // Humidity sent to supervisory PLC.  0 to 4095 = 0 to 100% RH (might need
                                  // calibration)
channelLabel battOutput = {
    1, 3}; // Battery voltage sent to supervisory PLC.  0 to 4095 = 0 to 20V (might need calibration)

// SLOT 2
// P1-16CDR
// 8X DI
channelLabel chillerRun = {2, 1};  // Signal FROM supervisory PLC that commands chiller to run (old dIo way)
channelLabel cabEstopLED = {2, 2}; // Input FROM safety PLC for in-cab E-stop LED
channelLabel beaconLeft = {2, 3};  // Input FROM safety PLC for left rotating beacon
channelLabel beaconRight = {2, 4}; // Input FROM safety PLC for right rotating beacon
// 8x NO Relay
channelLabel leftEstopA = {2, 1}; // Output TO Safety PLC for Left E-stop (A) HIGH = E-Stop Released
channelLabel leftEstopB = {2, 2}; // Output TO Safety PLC for Left E-stop (B) HIGH = E-Stop Released
channelLabel rightEstopA = {2, 3};
channelLabel rightEstopB = {2, 4};
channelLabel laserKeyA = {2, 5};      // Output to Safety PLC for Laser Enable Key (A) HIGH = Key ON
channelLabel laserKeyB = {2, 6};      // Output to Safety PLC for Laser Enable Key (B) HIGH = Key ON
channelLabel laserInterlock = {2, 7}; // Output to Safety PLC for Laser Interlock HIGH = Enabled

// SLOT 3
// P1-08TRS
// 2x CO Relay, 6X NO Relay
channelLabel liftSensor = {3, 1}; // Output to Safety PLC for Lift Sensor HIGH = ?? (to be tested)
channelLabel cabEstopA = {3, 2};  // Output to Safety PLC for In-Cab E Stop (A) HIGH = E-Stop Released
channelLabel cabEstopB = {3, 3};  // Output to Safety PLC for In-Cab E Stop (B) HIGH = E-Stop Released
channelLabel waterProtect = {
    3, 4}; // Output to Supervisory PLC for water protect signal from SMC Chiller HIGH = Happy Chiller
channelLabel tractorPower = {3,
                             6}; // Output to Supervisory PLC for Tractor Power HIGH = ISOBUS cable connected to tractor

// SLOT 4
// P1-08ADL-2
// 8x Analog Input
channelLabel actualTemp = {4, 1}; // Input from actual temperature sensor mounted on outside of box 0 to 4096 = -40 to
                                  // 60 C (might need calibration)
channelLabel actualHumd = {4, 2}; // Input from actual humidity sensor mounted on outside of box  0 to 4096 = 0 to 100%
                                  // RH (might need calibration)

/*****I/O EXAMPLE USAGE*****/
// Analog Output Example:
// P1.writeAnalog(4095, tempOutput);

// Digital Input Example:
// bool cabEstopLEDState = P1.readDiscrete(cabEstopLED);

// Analog Input Example:
// float temperature = P1.readAnalog(actualTemp);

// Digital Output Example:
// P1.writeDiscrete(HIGH,leftEstopA);

uint8_t buffer[64];

// boolean values
bool lifted = false;
bool leftEstopPressed = false;
bool rightEstopPressed = false;
bool cabEstopPressed = false;
bool laserKeyArmed = true;
bool laserInterlockEngaged = true;
bool waterProtectSensor = true;
bool tractorPowerConnected = true;
// Analog values
int temperature = 2500;    // degrees C * 100
int humidity = 5000;       //% RH* 100
int batteryVoltage = 1300; //* 100

bool useTempSensor = false;

void setLifted(bool l) {
  P1.writeDiscrete(l ? HIGH : LOW, liftSensor); // Lift Sensor, HIGH = Lifted, LOW = Lowered
  lifted = l;
}

void setLeftEstop(bool e) {
  P1.writeDiscrete(e ? LOW : HIGH, leftEstopA);
  P1.writeDiscrete(e ? LOW : HIGH, leftEstopB);

  leftEstopPressed = e;
}

void setRightEstop(bool e) {
  P1.writeDiscrete(e ? LOW : HIGH, rightEstopA);
  P1.writeDiscrete(e ? LOW : HIGH, rightEstopB);

  rightEstopPressed = e;
}

void setCabEstop(bool e) {
  P1.writeDiscrete(e ? LOW : HIGH, cabEstopA);
  P1.writeDiscrete(e ? LOW : HIGH, cabEstopB);

  cabEstopPressed = e;
}

void setLaserKeyArmed(bool a) {
  P1.writeDiscrete(a ? HIGH : LOW, laserKeyA);
  P1.writeDiscrete(a ? HIGH : LOW, laserKeyB);

  laserKeyArmed = a;
}

void setLaserInterlockEngaged(bool e) {
  P1.writeDiscrete(e ? HIGH : LOW, laserInterlock);

  laserInterlockEngaged = e;
}

void setWaterProtectSensor(bool e) {
  P1.writeDiscrete(e ? HIGH : LOW, waterProtect);

  waterProtectSensor = e;
}

void setTractorPowerConnected(bool e) {
  P1.writeDiscrete(e ? HIGH : LOW, tractorPower);

  tractorPowerConnected = e;
}

int getTemperature() {
  if (!useTempSensor) {
    return temperature;
  }

  int t = P1.readAnalog(actualTemp);
  t = map(t, 0, 8191, -4000, 6000);
  return t;
}

void setTemperature(int temp) {
  if (useTempSensor) {
    useTempSensor = false;
  }
  int t = map(temp, -4000, 6000, 819, 4096); // convert temp from -40 to 60 scale to 819 to 4096 scale
  P1.writeAnalog(t, tempOutput);

  temperature = temp;
}

int getHumidity() {
  if (!useTempSensor) {
    return humidity;
  }

  int h = P1.readAnalog(actualHumd);
  h = map(h, 0, 8191, 0, 10000);
  return h;
}

void setHumidity(int hum) {
  if (useTempSensor) {
    useTempSensor = false;
  }

  int h = map(hum, 0, 10000, 819, 4096); // convert temp from 0 to 100 scale to 819 to 4096 scale
  P1.writeAnalog(h, humdOutput);

  humidity = hum;
}

void setBatteryVoltage(int volt) {
  int v = map(volt, 0, 2000, 0, 4096); // convert volts from 0 to 2000 (0-20V) to 0 to 4096 scale
  P1.writeAnalog(v, battOutput);

  batteryVoltage = volt;
}

void setup() {
  Serial.begin(115200);
  while (!P1.init()) {
    ; // Wait for Modules to Sign on
  }

  analogWriteResolution(10); // Set to use DAC at 10-bit resolution

  /*****RELEASE ALL E-STOPS AND SET INITIAL ANALOG VALUES*****/
  setLeftEstop(false);
  setRightEstop(false);
  setCabEstop(false);
  setLaserKeyArmed(true);
  setLaserInterlockEngaged(true);
  setWaterProtectSensor(true);
  setTractorPowerConnected(true);
  setLifted(false);

  setTemperature(temperature);
  setHumidity(humidity);
  setBatteryVoltage(batteryVoltage);
}

void writeSerial(uint8_t *data, uint16_t size, uint8_t function) {
  uint8_t header[] = {SERIAL_MAGIC, function, (uint8_t)(size % 256), (uint8_t)(size / 256)};
  Serial.write(header, 4);
  Serial.write(data, size);
  Serial.flush();
}

void handlePinControllerRequest(slayertb_simulator_Request *request, slayertb_simulator_Reply *reply) {
  switch (request->which_request) {
  case slayertb_simulator_Request_ping_tag:
    reply->which_reply = slayertb_simulator_Reply_pong_tag;
    reply->reply.pong.x = request->request.ping.x;
    break;
  case slayertb_simulator_Request_get_value_tag:
    reply->which_reply = slayertb_simulator_Reply_get_value_tag;
    switch (request->request.get_value.sensor) {
    case slayertb_simulator_Sensor_LIFTED:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = lifted;
      break;
    case slayertb_simulator_Sensor_LEFT_ESTOP:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = leftEstopPressed;
      break;
    case slayertb_simulator_Sensor_RIGHT_ESTOP:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = rightEstopPressed;
      break;
    case slayertb_simulator_Sensor_CAB_ESTOP:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = cabEstopPressed;
      break;
    case slayertb_simulator_Sensor_LASER_KEY:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = laserKeyArmed;
      break;
    case slayertb_simulator_Sensor_LASER_INTERLOCK:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = laserInterlockEngaged;
      break;
    case slayertb_simulator_Sensor_WATER_PROTECT:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = waterProtectSensor;
      break;
    case slayertb_simulator_Sensor_TRACTOR_POWER:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = tractorPowerConnected;
      break;
    case slayertb_simulator_Sensor_CHILLER_RUN_SIGNAL:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = P1.readDiscrete(chillerRun);
      break;
    case slayertb_simulator_Sensor_CAB_ESTOP_SIGNAL:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = P1.readDiscrete(cabEstopLED);
      break;
    case slayertb_simulator_Sensor_BEACON_LEFT_SIGNAL:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = P1.readDiscrete(beaconLeft);
      break;
    case slayertb_simulator_Sensor_BEACON_RIGHT_SIGNAL:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_boolean_value_tag;
      reply->reply.get_value.value.boolean_value = P1.readDiscrete(beaconRight);
      break;

    case slayertb_simulator_Sensor_TEMP:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_int_value_tag;
      reply->reply.get_value.value.int_value = getTemperature();
      break;
    case slayertb_simulator_Sensor_HUMIDITY:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_int_value_tag;
      reply->reply.get_value.value.int_value = getHumidity();
      break;
    case slayertb_simulator_Sensor_BATTERY_VOLTAGE:
      reply->reply.get_value.which_value = slayertb_simulator_GetValueReply_int_value_tag;
      reply->reply.get_value.value.int_value = batteryVoltage;
      break;

    default:
      break;
    }
    break;
  case slayertb_simulator_Request_set_value_tag:
    reply->which_reply = slayertb_simulator_Reply_set_value_tag;
    switch (request->request.set_value.sensor) {
    case slayertb_simulator_Sensor_LIFTED:
      setLifted(request->request.set_value.value.boolean_value);
      break;
    case slayertb_simulator_Sensor_LEFT_ESTOP:
      setLeftEstop(request->request.set_value.value.boolean_value);
      break;
    case slayertb_simulator_Sensor_RIGHT_ESTOP:
      setRightEstop(request->request.set_value.value.boolean_value);
      break;
    case slayertb_simulator_Sensor_CAB_ESTOP:
      setCabEstop(request->request.set_value.value.boolean_value);
      break;
    case slayertb_simulator_Sensor_LASER_KEY:
      setLaserKeyArmed(request->request.set_value.value.boolean_value);
      break;
    case slayertb_simulator_Sensor_LASER_INTERLOCK:
      setLaserInterlockEngaged(request->request.set_value.value.boolean_value);
      break;
    case slayertb_simulator_Sensor_WATER_PROTECT:
      setWaterProtectSensor(request->request.set_value.value.boolean_value);
      break;
    case slayertb_simulator_Sensor_TRACTOR_POWER:
      setTractorPowerConnected(request->request.set_value.value.boolean_value);
      break;
    case slayertb_simulator_Sensor_TEMP:
      setTemperature(request->request.set_value.value.int_value);
      break;
    case slayertb_simulator_Sensor_HUMIDITY:
      setHumidity(request->request.set_value.value.int_value);
      break;
    case slayertb_simulator_Sensor_BATTERY_VOLTAGE:
      setBatteryVoltage(request->request.set_value.value.int_value);
      break;
    default:
      break;
    }
    break;
  case slayertb_simulator_Request_use_temp_sensor_tag:
    reply->which_reply = slayertb_simulator_Reply_use_temp_sensor_tag;
    useTempSensor = request->request.use_temp_sensor.on;
    break;
  default:
    break;
  }
}

void handlePacket(uint8_t *data, uint16_t size) {
  static slayertb_simulator_Request request;
  static pb_istream_t istream;
  static bool status;

  istream = pb_istream_from_buffer(data, size);
  status = pb_decode(&istream, slayertb_simulator_Request_fields, &request);

  if (!status) {

    return;
  }

  if (!request.has_header) {
    return;
  }

  static slayertb_simulator_Reply reply;
  reply.has_header = true;
  reply.header = request.header;

  handlePinControllerRequest(&request, &reply);

  static uint8_t reply_buf[64];
  static pb_ostream_t ostream;
  ostream = pb_ostream_from_buffer(reply_buf, slayertb_simulator_Reply_size);
  status = pb_encode(&ostream, slayertb_simulator_Reply_fields, &reply);

  if (!status || ostream.bytes_written == 0) {
    return;
  }

  writeSerial(reply_buf, ostream.bytes_written, SERIAL_PACKET_CMD);
}

void serialEvent() {
  while (Serial.available()) {

    uint8_t header[4];
    size_t header_bytes_read = Serial.readBytes(header, 4);

    if (header_bytes_read != 4) {
      continue;
    }

    uint8_t magic = header[0];

    if (magic != SERIAL_MAGIC) {
      continue;
    }

    uint8_t function = header[1];

    if (function != SERIAL_ECHO_CMD && function != SERIAL_PING_CMD && function != SERIAL_PACKET_CMD) {
      continue;
    }

    uint8_t low_size = header[2];
    uint8_t high_size = header[3];

    uint16_t size = ((uint16_t)high_size) * 256 + low_size;
    size_t bytes_read = Serial.readBytes(buffer, size);

    switch (function) {
    case SERIAL_PACKET_CMD:
      handlePacket(buffer, bytes_read);
      break;
    case SERIAL_PING_CMD:
      writeSerial(buffer, bytes_read, SERIAL_PING_CMD);
      break;
    case SERIAL_ECHO_CMD:
      writeSerial(buffer, bytes_read, SERIAL_ECHO_CMD);
      break;
    }
  }
}

void loop() {
  if (Serial.available()) {
    serialEvent();
  }

  if (useTempSensor) {
    // read temp sensor
    int t = P1.readAnalog(actualTemp);
    int h = P1.readAnalog(actualHumd);
    t = map(t, 0, 8191, -4000, 6000);
    t = map(t, -4000, 6000, 819, 4096);
    h = map(h, 0, 8191, 0, 10000);
    h = map(h, 0, 10000, 819, 4096);
    P1.writeAnalog(t, tempOutput);
    P1.writeAnalog(h, humdOutput);
  } else {
    setTemperature(temperature);
    setHumidity(humidity);
  }
}

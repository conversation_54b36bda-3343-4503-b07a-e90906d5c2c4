add_custom_target(make_lib_drivers ALL make -C ${CMAKE_CURRENT_LIST_DIR}/nanopb/proto all
                  COMMAND make -C ${CMAKE_CURRENT_LIST_DIR}/emergent/proto all
                  COMMAND make -C ${CMAKE_CURRENT_LIST_DIR}/kaya/proto all)
add_dependencies(generated make_lib_drivers)

add_custom_target(clean_lib_drivers make -C ${CMAKE_CURRENT_LIST_DIR}/nanopb/proto clean
                  COMMAND make -C ${CMAKE_CURRENT_LIST_DIR}/emergent/proto clean
                  COMMAND make -C ${CMAKE_CURRENT_LIST_DIR}/kaya/proto clean)
add_dependencies(make_clean clean_lib_drivers)

add_subdirectory(basler/cpp)
add_subdirectory(thinklucid/cpp)
add_subdirectory(zedcam/client/cpp)
add_subdirectory(sim_cam/cpp)
add_subdirectory(mock_cam/cpp)
add_subdirectory(nanopb)
add_subdirectory(deck_cam)

add_custom_target(unit_tests_spencer_fluids COMMAND /bin/bash -c \"cd ${CMAKE_SOURCE_DIR} && python -m pytest lib/drivers/spencer_fluids\")
add_dependencies(unit_tests unit_tests_spencer_fluids)

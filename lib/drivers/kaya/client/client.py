import logging
from typing import TYPE_CHECKING, Any, Optional, <PERSON><PERSON>

import grpc
import numpy as np
import numpy.typing as npt

from generated.lib.drivers.kaya.proto.kaya_pb2 import (
    GetSensorTempRequest,
    GetSensorTempResponse,
    GrabRequest,
    GrabResponse,
    PixelFormat,
    PixelFormatValue,
)
from generated.lib.drivers.kaya.proto.kaya_pb2_grpc import KayaServiceStub
from lib.common.time import TimestampedObject, maka_control_timestamp_ms
from lib.common.time.sleep import sleep_ms

if TYPE_CHECKING:
    PixelFormatType = PixelFormatValue
else:
    PixelFormatType = PixelFormat


class KayaGrabResult(TimestampedObject):
    def __init__(self, timestamp_ms: int, data: npt.NDArray[Any]):
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)
        self._data = data

    @property
    def data(self) -> npt.NDArray[Any]:
        return self._data


class KayaClient:
    def __init__(
        self, hostname: str = "localhost", port: int = 50051, retries: int = 3, retry_time_ms: float = 1000
    ) -> None:
        self._hostname = hostname
        self._port = port
        self._retries = retries
        self._retry_time_ms = retry_time_ms
        self._channel = None
        self._stub: Optional[KayaServiceStub] = None

    def grab(
        self,
        pixel_format: PixelFormatType = PixelFormat.BGR8,
        exposure_us: int = 1000,
        analog_gain_level: float = 1.25,
        analog_black_level: float = 0.0,
        focus: float = 0.0,
        wb_ratio_red: float = 1.0,
        wb_ratio_green: float = 1.0,
        wb_ratio_blue: float = 1.0,
        focusing_mode: bool = False,
    ) -> KayaGrabResult:
        for attempt in range(self._retries):
            try:
                self._connect()
                assert self._stub is not None
                req = GrabRequest(
                    timestamp_ms=maka_control_timestamp_ms(),
                    pixel_format=pixel_format,
                    exposure_us=exposure_us,
                    analog_gain_level=analog_gain_level,
                    analog_black_level=analog_black_level,
                    focus=focus,
                    wb_ratio_red=wb_ratio_red,
                    wb_ratio_green=wb_ratio_green,
                    wb_ratio_blue=wb_ratio_blue,
                    focusing_mode=focusing_mode,
                )
                resp: GrabResponse = self._stub.Grab(req)
                if resp.bits_per_pixel == 8:
                    dtype = np.uint8
                    shape: Tuple[int, ...] = (resp.height, resp.width)
                elif resp.bits_per_pixel == 24:
                    dtype = np.uint8
                    shape = (resp.height, resp.width, 3)
                else:
                    raise Exception(f"Unknown bits per pixel: {resp.bits_per_pixel}")
                return KayaGrabResult(
                    timestamp_ms=resp.timestamp_ms, data=np.frombuffer(resp.data, dtype=dtype).reshape(shape)
                )
            except Exception:
                logging.exception("Error executing grab request")
                self._disconnect()
                sleep_ms(self._retry_time_ms)
        raise Exception(f"Failed to execute grab request after {self._retries} attempts")

    def get_sensor_temp_c(self) -> int:
        for attempt in range(self._retries):
            try:
                self._connect()
                assert self._stub is not None
                req = GetSensorTempRequest()
                resp: GetSensorTempResponse = self._stub.GetSensorTemp(req)
                return resp.sensor_temp_c
            except Exception:
                logging.exception("Error getting sensor temp")
                self._disconnect()
                sleep_ms(self._retry_time_ms)
        raise Exception(f"Failed to get sensor temp after {self._retries} attempts")

    def _connect(self) -> None:
        if self._channel is None:
            options = [
                ("grpc.max_send_message_length", 1024 * 1024 * 1024),
                ("grpc.max_receive_message_length", 1024 * 1024 * 1024),
            ]
            self._channel = grpc.insecure_channel(f"{self._hostname}:{self._port}", options)
            self._stub = KayaServiceStub(self._channel)

    def _disconnect(self) -> None:
        try:
            if self._channel is not None:
                self._channel.close()
        except:  # noqa
            pass

        self._channel = None
        self._stub = None

    def close(self) -> None:
        self._disconnect()

# Kaya Camera Setup (Hubble)

## Computer Setup

1. Install Ubuntu 18.04 via `ubuntu-bionic-netboot.iso` (use UEFI mode)

2. Create the standard `maka` user.

3. Install Kaya Vision Point SDK.

```
wget -O KAYA_Vision_Point_Setup_2020.3_Ubuntu_18.04_x64.tar.gz "https://storage.kayainstruments.com/s/vision-point/download?path=%2FVision%20Point%20Installation%20for%20Linux&files=KAYA_Vision_Point_Setup_2020.3_Ubuntu_18.04_x64.tar.gz"
tar zxvf KAYA_Vision_Point_Setup_2020.3_Ubuntu_18.04_x64.tar.gz
cd ~/KAYA_Vision_Point_Setup_2020.3_Ubuntu_18.04_x64__branches-sw_5_4_x_2020_3_build_5.4.0.7320_2021-01-03_10-38-09
sudo ./install.sh
sudo rm -rf ~/KAYA_Vision_Point_Setup_2020.3_Ubuntu_18.04_x64*
sudo reboot
```

4. Install Docker.

   1. Install `docker.io`:

   ```
   sudo apt-get install -y docker.io
   ```

   2. Add `maka` user to group `docker`:

   ```
   sudo usermod maka -G docker -a
   ```

5. Point docker to Robot registry.

   1. Create file `/etc/docker/daemon.json`:

   ```
   {
           "insecure-registries": ["registry:5000"]
   }
   ```

   2. Add host named `registry` to `/etc/hosts` that points to Robot registry:

   ```
   **********      registry
   ```

   3. Restart Docker daemon:

   ```
   sudo systemctl restart docker
   ```

6. Add user `maka` to `dialout` group:

```
sudo usermod maka -G dialout -a
```

7. Create systemd service.

   1. Create service file `/etc/systemd/system/kaya-server.service`:

   ```
   [Unit]
   Description=Kaya Camera server
   After=network.target
   
   [Service]
   Type=simple
   Restart=always
   RestartSec=5
   User=maka
   WorkingDirectory=/
   ExecStartPre=-/usr/bin/docker pull registry:5000/kaya_server:latest
   ExecStartPre=-/usr/bin/docker image prune -f
   ExecStart=/usr/bin/docker run --privileged --name=%n --rm --ulimit memlock=-1 --network=host -v /tmp:/tmp registry:5000/kaya_server:latest
   ExecStop=/usr/bin/docker stop %n
   TimeoutStartSec=300
   
   [Install]
   WantedBy=multi-user.target
   ```
   
   2. Enable and start service:

   ```
   sudo systemctl enable kaya-server
   sudo systemctl start kaya-server
   sudo systemctl status kaya-server
   ```

## Update software on Hubble

```
cd $MAKA_ROBOT_DIR
docker run -it -v /var/run/docker.sock:/var/run/docker.sock -v `pwd`:/robot ghcr.io/carbonrobotics/robot/build /bin/bash -c "cd lib/drivers/kaya; make"
ssh -t maka@********** sudo systemctl restart kaya-server
```

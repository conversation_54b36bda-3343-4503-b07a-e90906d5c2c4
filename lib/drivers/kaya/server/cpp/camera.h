#pragma once

#include <future>
#include <memory>
#include <stdexcept>
#include <string>
#include <vector>

#include <KYFGLib.h>

#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/utils.h"

namespace lib {
namespace drivers {
namespace kaya {
namespace server {

struct DeviceInfo {
  int index;
  std::string name;
};

std::vector<DeviceInfo> list_devices();

inline void SafeError(FGSTATUS status, const std::string &msg) {
  if (status != FGSTATUS_OK) {
    throw maka_error(std::string("Failed to ") + std::string(msg) + ": " + std::to_string(status));
  }
}

inline int64_t SafeGetInt(int64_t retval, const std::string &msg) {
  if (retval == INVALID_INT_PARAMETER_VALUE) {
    throw maka_error(std::string("Failed to ") + std::string(msg));
  }
  return retval;
}

inline double SafeGetFloat(double retval, const std::string &msg) {
  if (retval == INVALID_FLOAT_PARAMETER_VALUE) {
    throw maka_error(std::string("Failed to ") + std::string(msg));
  }
  return retval;
}

class CameraControl {
public:
  CameraControl(const DeviceInfo &device_info);
  ~CameraControl();
  DELETE_COPY_AND_MOVE(CameraControl)

  double get_sensor_temp_c() {
    SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "DeviceTemperatureSelector", "Sensor"),
              "set temperature selector");
    return SafeGetFloat(KYFG_GetCameraValueFloat(handle_, "DeviceTemperature"), "get temperature");
  }

  int64_t get_max_width() {
    int64_t max, min;
    SafeError(KYFG_GetCameraValueIntMaxMin(handle_, "Width", &max, &min), "get max width");
    return max;
  }
  int64_t get_width() { return SafeGetInt(KYFG_GetCameraValueInt(handle_, "Width"), "get width"); }
  void set_width(int64_t width) { SafeError(KYFG_SetCameraValueInt(handle_, "Width", width), "set width"); }

  int64_t get_max_height() {
    int64_t max, min;
    SafeError(KYFG_GetCameraValueIntMaxMin(handle_, "Height", &max, &min), "get max height");
    return max;
  }
  int64_t get_height() { return SafeGetInt(KYFG_GetCameraValueInt(handle_, "Height"), "get height"); }
  void set_height(int64_t height) { SafeError(KYFG_SetCameraValueInt(handle_, "Height", height), "set height"); }

  long long get_pixel_format() {
    return SafeGetInt(KYFG_GetCameraValueEnum(handle_, "PixelFormat"), "get pixel format");
  }
  void set_pixel_format(long long format) {
    SafeError(KYFG_SetCameraValueEnum(handle_, "PixelFormat", format), "set pixel format");
  }

  double get_exposure_us() {
    return SafeGetFloat(KYFG_GetCameraValueFloat(handle_, "ExposureTime"), "get exposure time");
  }
  void set_exposure_us(double exposure) {
    SafeError(KYFG_SetCameraValueFloat(handle_, "ExposureTime", exposure), "set exposure time");
  }

  double get_analog_gain_level() {
    return SafeGetFloat(KYFG_GetCameraValueFloat(handle_, "AnalogGainLevel"), "get analog gain level");
  }
  void set_analog_gain_level(double gain) {
    SafeError(KYFG_SetCameraValueFloat(handle_, "AnalogGainLevel", gain), "set analog gain level");
  }

  double get_analog_black_level() {
    return SafeGetFloat(KYFG_GetCameraValueFloat(handle_, "AnalogBlackLevel"), "get analog black level");
  }
  void set_analog_black_level(double black) {
    SafeError(KYFG_SetCameraValueFloat(handle_, "AnalogBlackLevel", black), "set analog black level");
  }

  double get_wb_ratio_red() {
    SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "BalanceRatioSelector", "Red"),
              "set balance ratio selector");
    return SafeGetFloat(KYFG_GetCameraValueFloat(handle_, "BalanceRatio"), "get balance ratio");
  }
  void set_wb_ratio_red(double value) {
    SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "BalanceRatioSelector", "Red"),
              "set balance ratio selector");
    SafeError(KYFG_SetCameraValueFloat(handle_, "BalanceRatio", value), "set balance ratio");
  }

  double get_wb_ratio_green() {
    SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "BalanceRatioSelector", "Green"),
              "set balance ratio selector");
    return SafeGetFloat(KYFG_GetCameraValueFloat(handle_, "BalanceRatio"), "get balance ratio");
  }
  void set_wb_ratio_green(double value) {
    SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "BalanceRatioSelector", "Green"),
              "set balance ratio selector");
    SafeError(KYFG_SetCameraValueFloat(handle_, "BalanceRatio", value), "set balance ratio");
  }

  double get_wb_ratio_blue() {
    SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "BalanceRatioSelector", "Blue"),
              "set balance ratio selector");
    return SafeGetFloat(KYFG_GetCameraValueFloat(handle_, "BalanceRatio"), "get balance ratio");
  }
  void set_wb_ratio_blue(double value) {
    SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "BalanceRatioSelector", "Blue"),
              "set balance ratio selector");
    SafeError(KYFG_SetCameraValueFloat(handle_, "BalanceRatio", value), "set balance ratio");
  }

  void start();
  bool is_running();
  void trigger();
  const std::vector<uint8_t> grab();
  void stop();

private:
  FGHANDLE fg_handle_ = INVALID_FGHANDLE;
  CAMHANDLE handle_ = INVALID_CAMHANDLE;
  STREAM_HANDLE stream_handle_ = INVALID_STREAMHANDLE;
  std::promise<std::vector<uint8_t>> data_promise_;

  static void stream_callback(void *userContext, STREAM_HANDLE streamHandle);
};

} // namespace server
} // namespace kaya
} // namespace drivers
} // namespace lib
#include "camera.h"

namespace lib {
namespace drivers {
namespace kaya {
namespace server {

std::vector<DeviceInfo> list_devices() {
  int detected;
  SafeError(KY_DeviceScan(&detected), "list devices");

  std::vector<DeviceInfo> result;
  for (int i = 0; i < detected; i++) {
    KY_DEVICE_INFO info{};
    info.version = 3;
    SafeError(KY_DeviceInfo(i, &info), "get device info");
    if (!info.isVirtual) {
      result.push_back(DeviceInfo{.index = i, .name = info.szDeviceDisplayName});
    }
  }
  return result;
}

CameraControl::CameraControl(const DeviceInfo &device_info) {
  fg_handle_ = KYFG_Open(device_info.index);
  if (fg_handle_ == INVALID_FGHANDLE) {
    throw maka_error("Failed to connect to frame grabber");
  }

  int count = 1;
  SafeError(KYFG_UpdateCameraList(fg_handle_, &handle_, &count), "list cameras");
  if (!count) {
    throw maka_error("No cameras detected");
  }

  SafeError(KYFG_CameraOpen2(handle_, nullptr), "open camera");
  SafeError(KYFG_CameraCallbackRegister(handle_, stream_callback, this), "register callback");

  SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "ConnectionConfig", "x1_CXP_12"), "set connection speed");
  SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "AcquisitionMode", "Continuous"), "set acquisition mode");
  SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "TriggerMode", "Off"), "disable trigger mode");
  float max_fps = SafeGetFloat(KYFG_GetCameraValueFloat(handle_, "AcquisitionFrameRateMax"), "get max frame rate");
  SafeError(KYFG_SetCameraValueFloat(handle_, "AcquisitionFrameRate", max_fps), "set frame rate");
  SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "TriggerMode", "On"), "enable trigger mode");
  SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "TriggerSource", "Software"), "set trigger source");
  SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "LineSelector", "Line3"), "select line");
  SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "LineMode", "Output"), "set line mode");
  SafeError(KYFG_SetCameraValueInt(handle_, "LineInverter", 1), "invert line");
  SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "LineSource", "ExposureActive"), "set line source");
  SafeError(KYFG_SetCameraValueEnum_ByValueName(handle_, "BalanceWhiteAuto", "Manual"), "set white balance mode");
  SafeError(KYFG_SetCameraValueBool(handle_, "ReverseX", true), "reverse X");
  SafeError(KYFG_SetCameraValueBool(handle_, "ReverseY", true), "reverse Y");
}

CameraControl::~CameraControl() {
  if (handle_ != INVALID_CAMHANDLE) {
    KYFG_CameraClose(handle_);
    handle_ = INVALID_CAMHANDLE;
  }

  if (fg_handle_ != INVALID_FGHANDLE) {
    KYFG_Close(fg_handle_);
    fg_handle_ = INVALID_FGHANDLE;
  }
}

void CameraControl::stream_callback(void *user_context, STREAM_HANDLE stream_handle) {
  if (stream_handle == 0) {
    return;
  }

  int64_t buffer_size = KYFG_StreamGetSize(stream_handle);
  if (buffer_size == -1) {
    return;
  }

  int frame_index = KYFG_StreamGetFrameIndex(stream_handle);
  if (frame_index == -1) {
    return;
  }

  uint8_t *data_ptr = static_cast<uint8_t *>(KYFG_StreamGetPtr(stream_handle, frame_index));
  if (data_ptr == nullptr) {
    return;
  }

  auto ctx = static_cast<CameraControl *>(user_context);
  ctx->data_promise_.set_value(std::vector<uint8_t>(data_ptr, data_ptr + buffer_size));
}

void CameraControl::start() {
  if (is_running()) {
    return;
  }

  SafeError(KYFG_StreamCreateAndAlloc(handle_, &stream_handle_, 16, 0), "create stream");
  SafeError(KYFG_CameraStart(handle_, stream_handle_, 0), "start acquisition");
}

bool CameraControl::is_running() { return stream_handle_ != INVALID_STREAMHANDLE; }

void CameraControl::trigger() {
  data_promise_ = {};
  SafeError(KYFG_CameraExecuteCommand(handle_, "TriggerSoftware"), "grab frame");
}

const std::vector<uint8_t> CameraControl::grab() {
  auto data_f = data_promise_.get_future();
  if (data_f.wait_for(std::chrono::seconds(5)) == std::future_status::timeout) {
    throw maka_error("Timed out waiting for the frame.");
  }
  return data_f.get();
}

void CameraControl::stop() {
  if (!is_running()) {
    return;
  }

  SafeError(KYFG_CameraStop(handle_), "stop acquisition");
  SafeError(KYFG_StreamDelete(stream_handle_), "delete stream");
  stream_handle_ = INVALID_STREAMHANDLE;
}

} // namespace server
} // namespace kaya
} // namespace drivers
} // namespace lib

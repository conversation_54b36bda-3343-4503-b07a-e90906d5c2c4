#include <chrono>
#include <csignal>
#include <exception>
#include <iostream>
#include <math.h>
#include <string>
#include <thread>

#include <grpcpp/grpcpp.h>
#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>

#include "camera.h"
#include "generated/lib/drivers/kaya/proto/kaya.grpc.pb.h"
#include "lib/common/image/cpp/crop.h"
#include "lib/drivers/dynamixel/cpp/focus.h"

constexpr double kFocusTolerance = 0.005;
constexpr int64_t kPixelFormatBayerRG8 = 0x321;

using namespace lib::drivers::kaya::server;
using namespace lib::drivers::dynamixel;

std::unique_ptr<grpc::Server> server;

void shutdown_signal_handler(int signum) {
  std::cout << "Shutdown requested." << std::endl;
  grpc::Server *server_ptr = server.get();
  if (server_ptr != nullptr) {
    server_ptr->Shutdown();
  }
  std::cout << "Server stopped." << std::endl;
}

class KayaServiceImpl final : public kaya::KayaService::Service {
public:
  KayaServiceImpl(CameraControl &camera_, FocusControl &focus_) : camera(camera_), focus(focus_) { camera.start(); }

  ~KayaServiceImpl() {
    try {
      camera.stop();
    } catch (std::exception &) {
      // ignore
    }
  }

  grpc::Status Grab(grpc::ServerContext *context, const kaya::GrabRequest *request, kaya::GrabResponse *response) {
    auto start_time = std::chrono::steady_clock::now();

    if (request->pixel_format() == kaya::PF_UNKNOWN) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "pixel_format is not set");
    }
    if (request->exposure_us() == 0) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "exposure is not set");
    }
    if (request->exposure_us() > 10000) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "exposure time must be no longer than 10ms");
    }
    if (request->analog_gain_level() < 0.75) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "analog gain level must be no smaller than 0.75");
    }
    if (request->analog_gain_level() > 6.0) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "analog gain level must be no larger than 6.0");
    }
    if (request->analog_black_level() < -8192) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "analog black level must be no smaller than -8192");
    }
    if (request->analog_black_level() > 8191) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "analog black level must be no larger than 8191");
    }
    if (request->focus() < -1 || request->focus() > 1) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "focus must be in range -1..1");
    }
    if (request->wb_ratio_red() > 7.95 || request->wb_ratio_green() > 7.95 || request->wb_ratio_blue() > 7.95) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "white balance ratios must be no larger than 7.95");
    }
    if (request->focusing_mode() && request->pixel_format() != kaya::PixelFormat::BGR8) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "focusing mode is only supported with BGR8 pixel format");
    }

    try {
      if (camera.get_pixel_format() != kPixelFormatBayerRG8) {
        camera.stop();
        camera.set_pixel_format(kPixelFormatBayerRG8);
      }
      camera.start();

      // These settings don't require restarting the feed.
      camera.set_exposure_us(request->exposure_us());
      camera.set_analog_gain_level(request->analog_gain_level());
      camera.set_analog_black_level(request->analog_black_level());
      camera.set_wb_ratio_red(request->wb_ratio_red());
      camera.set_wb_ratio_green(request->wb_ratio_green());
      camera.set_wb_ratio_blue(request->wb_ratio_blue());

      focus.set_focus(request->focus());
      auto focus_start_time = std::chrono::steady_clock::now();
      while (focus.is_moving() || std::abs(focus.get_focus() - request->focus()) > kFocusTolerance) {
        if (std::chrono::steady_clock::now() > focus_start_time + std::chrono::seconds(3)) {
          return grpc::Status(grpc::StatusCode::OUT_OF_RANGE, "unable to achieve desired focus " +
                                                                  std::to_string(request->focus()) + ", achieved " +
                                                                  std::to_string(focus.get_focus()));
        }
      }

      camera.trigger();
      std::vector<uint8_t> frame = camera.grab();
      auto grab_done_time = std::chrono::steady_clock::now();

      cv::Mat image;
      auto height = camera.get_height();
      auto width = camera.get_width();
      int32_t bits_per_pixel = 0;

      switch (request->pixel_format()) {
      case kaya::PixelFormat::BGR8: {
        // Debayering BayerRG8 to BGR8.
        cv::Mat bayer_image(height, width, CV_8UC1, frame.data());
        image = cv::Mat(height, width, CV_8UC3);
        cv::cvtColor(bayer_image, image, cv::COLOR_BayerBG2BGR);
        bits_per_pixel = 24;
        break;
      }

      case kaya::PixelFormat::BayerRG8: {
        image = cv::Mat(height, width, CV_8UC1, frame.data());
        bits_per_pixel = 8;
        break;
      }

      default:
        throw maka_error(std::string("Unknown pixel format: ") + kaya::PixelFormat_Name(request->pixel_format()));
      }

      // Optional cropping.
      if (request->focusing_mode()) {
        // Focus on left 80% of image because of furrow tracks in the right side. Round down to be divisible by 20.
        image = image(cv::Rect(0, 0, int(image.size().width * 0.8 / 20) * 20, image.size().height));
        image = lib::common::image::tiled_crops(image);
      }

      response->set_timestamp_ms(
          request->timestamp_ms() +
          std::chrono::duration_cast<std::chrono::milliseconds>(grab_done_time - start_time).count());
      response->set_height(image.size().height);
      response->set_width(image.size().width);
      response->set_bits_per_pixel(bits_per_pixel);
      response->set_data(image.data, image.total() * image.elemSize());

      return grpc::Status::OK;
    } catch (std::exception &e) {
      return handle_exception(e);
    }
  }

  grpc::Status GetSensorTemp(grpc::ServerContext *context, const kaya::GetSensorTempRequest *request,
                             kaya::GetSensorTempResponse *response) {
    try {
      response->set_sensor_temp_c(camera.get_sensor_temp_c());
      return grpc::Status::OK;
    } catch (std::exception &e) {
      return handle_exception(e);
    }
  }

private:
  grpc::Status handle_exception(const std::exception &e) {
    return grpc::Status(grpc::StatusCode::INTERNAL, std::string(e.what()));
  }

  CameraControl &camera;
  FocusControl &focus;
};

DeviceInfo find_device() {
  auto devices = list_devices();
  if (devices.empty()) {
    throw maka_error("No frame grabbers found!");
  }

  std::cout << "Found frame grabbers:" << std::endl;
  for (auto &device : devices) {
    std::cout << "- " << device.name << std::endl;
  }
  std::cout << std::endl;

  if (devices.size() > 1) {
    throw maka_error("More than one frame grabber found!");
  }
  return devices[0];
}

int main() {
  // Important to destroy CameraControl before exiting, otherwise camera will
  // hang.
  signal(SIGINT, shutdown_signal_handler);
  signal(SIGTERM, shutdown_signal_handler);

  try {
    auto device = find_device();
    CameraControl camera(device);
    FocusControl focus("/dev/ttyUSB0", 1);

    std::string server_address = "0.0.0.0:50051";
    KayaServiceImpl service(camera, focus);

    grpc::ServerBuilder builder;
    // Listen on the given address without any authentication mechanism.
    builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
    // Limit to a single thread for handling requests to avoid concurrency.
    // One thread is reserved for gRPC "completion queue", so we set this to 2.
    builder.SetResourceQuota(grpc::ResourceQuota().SetMaxThreads(2));
    // Register "service" as the instance through which we'll communicate with
    // clients. In this case it corresponds to an *synchronous* service.
    builder.RegisterService(&service);
    // Finally assemble the server.
    server = builder.BuildAndStart();
    std::cout << "Server listening on " << server_address << std::endl;

    // Wait for the server to shutdown. Note that some other thread must be
    // responsible for shutting down the server for this call to ever return.
    server->Wait();

    return 0;
  } catch (std::exception &e) {
    std::cerr << "Error: " << e.what() << std::endl;
    return 1;
  }
}

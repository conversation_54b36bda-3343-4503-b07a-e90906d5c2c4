FROM ubuntu:18.04
LABEL org.opencontainers.image.source https://github.com/carbonrobotics/robot

RUN apt-get update -qq && \
    apt-get install -y wget lsb-release sudo && \
    rm -rf /var/lib/apt/lists/*

RUN wget -O KAYA_Vision_Point_Setup_2020.3_Ubuntu_18.04_x64.tar.gz "https://storage.kayainstruments.com/s/vision-point/download?path=%2FVision%20Point%20Installation%20for%20Linux&files=KAYA_Vision_Point_Setup_2020.3_Ubuntu_18.04_x64.tar.gz" && \
    tar zxvf KAYA_Vision_Point_Setup_2020.3_Ubuntu_18.04_x64.tar.gz && \
    cd KAYA_Vision_Point_Setup_2020.3_Ubuntu_18.04_x64__branches-sw_5_4_x_2020_3_build_5.4.0.7320_2021-01-03_10-38-09 && \
    ./install.sh --keep_driver --keep_daemon --keep_tray --keep_conf && \
    cd .. && \
    rm -r KAYA_Vision_Point_Setup_2020.3_Ubuntu_18.04_x64*

RUN wget https://github.com/protocolbuffers/protobuf/releases/download/v3.12.4/protobuf-all-3.12.4.tar.gz && \
    tar zxf protobuf-all-3.12.4.tar.gz && \
    cd protobuf-3.12.4 && \
    ./configure && \
    make -j $(nproc) && \
    make install && \
    ldconfig && \
    cd .. && \
    rm -rf protobuf-3.12.4 protobuf-all-3.12.4.tar.gz

# Install openssl (to use instead of boringssl)
RUN apt-get update -qq && \
    apt-get install -y libssl-dev && \
    rm -rf /var/lib/apt/lists/*

# Install CMake 3.16
RUN apt-get update -qq && \
    apt-get install -y wget && \
    rm -rf /var/lib/apt/lists/* && \
    wget -q -O cmake-linux.sh https://github.com/Kitware/CMake/releases/download/v3.16.1/cmake-3.16.1-Linux-x86_64.sh && \
    sh cmake-linux.sh -- --skip-license --prefix=/usr && \
    rm cmake-linux.sh

# Install git and pkg-config
RUN apt-get update -qq && \
    apt-get install -y git pkg-config && \
    rm -rf /var/lib/apt/lists/*

RUN git clone --recursive https://github.com/grpc/grpc && \
    cd grpc && \
    git checkout v1.31.1 && \
    git submodule update && \
    mkdir -p cmake/build && \
    cd cmake/build && \
    cmake -DCMAKE_BUILD_TYPE=Release \
          -DBUILD_SHARED_LIBS=ON \
          -DgRPC_INSTALL=ON \
          -DgRPC_SSL_PROVIDER=package \
          -DgRPC_PROTOBUF_PROVIDER=package \
          ../.. && \
    make -j $(nproc) && \
    make install && \
    ldconfig && \
    cd ../../.. && \
    rm -rf grpc

# Install Dynamixel SDK
RUN git clone https://github.com/ROBOTIS-GIT/DynamixelSDK && \
    cd DynamixelSDK/c++/build/linux64 && \
    make install && \
    cd ../../../.. && \
    rm -rf DynamixelSDK

# Install OpenCV
RUN apt-get update -qq && \
    apt-get install -y libopencv-core-dev libopencv-imgproc-dev && \
    rm -rf /var/lib/apt/lists/*

# Install Boost
RUN apt-get update -qq && \
    apt-get install -y libboost-stacktrace-dev && \
    rm -rf /var/lib/apt/lists/*

COPY . /robot

RUN cd /robot/lib/drivers/kaya/server/cpp && \
    make

WORKDIR /robot
ENTRYPOINT ["/robot/bin/kaya_server"]

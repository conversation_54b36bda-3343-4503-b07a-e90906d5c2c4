syntax = "proto3";

package kaya;

enum PixelFormat {
    PF_UNKNOWN = 0;
    BGR8 = 1;
    BayerRG8 = 2;
}

message GrabRequest {
    int64 timestamp_ms = 1;
    PixelFormat pixel_format = 2;
    int32 exposure_us = 3;
    float analog_gain_level = 4;
    float analog_black_level = 5;
    // Focus value controls servo, -1..1, with best value expected to be around 0.
    float focus = 6;
    // W&B gain adjustments.
    float wb_ratio_red = 7;
    float wb_ratio_green = 8;
    float wb_ratio_blue = 9;
    float wb_b_gain_db = 10;
    bool focusing_mode = 11;
}

message GrabResponse {
    int64 timestamp_ms = 1;
    int32 height = 2;
    int32 width = 3;
    int32 bits_per_pixel = 4;
    bytes data = 5;
}

message GetSensorTempRequest {
}

message GetSensorTempResponse {
    int32 sensor_temp_c = 1;
}

service KayaService {
    rpc Grab(GrabRequest) returns (GrabResponse) {}
    rpc GetSensorTemp(GetSensorTempRequest) returns (GetSensorTempResponse) {}
}

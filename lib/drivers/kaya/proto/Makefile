ROOT_DIR      = $(shell realpath ../../../../)
REL_DIR       = $(shell echo ${CURDIR} | sed "s\#^${ROOT_DIR}/\#\#")
GENERATED_DIR = $(ROOT_DIR)/generated/$(REL_DIR)

.PHONY: all proto clean

all: proto
	
proto: $(GENERATED_DIR)/kaya_pb2_grpc.py \
	$(GENERATED_DIR)/kaya_pb2.py $(GENERATED_DIR)/kaya_pb2.pyi \
	$(GENERATED_DIR)/kaya.grpc.pb.cc $(GENERATED_DIR)/kaya.grpc.pb.h \
	$(GENERATED_DIR)/kaya.pb.cc $(GENERATED_DIR)/kaya.pb.h

$(GENERATED_DIR):
	mkdir -p $(GENERATED_DIR)

$(GENERATED_DIR)/%.proto: %.proto | $(GENERATED_DIR)
	cp $< $@

$(GENERATED_DIR)/%_pb2_grpc.py: $(GENERATED_DIR)/%.proto
	python -m grpc_tools.protoc --grpc_python_out=$(ROOT_DIR) --proto_path=$(ROOT_DIR) $<

$(GENERATED_DIR)/%_pb2.py: $(GENERATED_DIR)/%.proto
	python -m grpc_tools.protoc --python_out=$(ROOT_DIR) --proto_path=$(ROOT_DIR) $<

$(GENERATED_DIR)/%_pb2.pyi: $(GENERATED_DIR)/%.proto
	python -m grpc_tools.protoc --mypy_out=$(ROOT_DIR) --proto_path=$(ROOT_DIR) $<

$(GENERATED_DIR)/%.grpc.pb.cc $(GENERATED_DIR)/%.grpc.pb.h: $(GENERATED_DIR)/%.proto
	protoc -I$(ROOT_DIR) --plugin=protoc-gen-grpc=$(shell which grpc_cpp_plugin) --grpc_out=$(ROOT_DIR) $<
 
$(GENERATED_DIR)/%.pb.cc $(GENERATED_DIR)/%.pb.h: $(GENERATED_DIR)/%.proto
	protoc -I$(ROOT_DIR) --cpp_out=$(ROOT_DIR) $<

clean:
	rm -rf $(GENERATED_DIR)

import abc
from typing import Optional, Union


class MakaProtocolConnector(abc.ABC):
    async def open(self) -> None:
        """
        Opens the connection. Optional if not required by protocol.
        :return:
        """
        pass

    async def close(self) -> None:
        """
        Closes the connection. Optional if not required by protocol.
        :return:
        """
        pass

    async def wait_for_online(self) -> None:
        """
        Blocks until the connection is open. Optional if not required by protocol.
        :return:
        """
        pass

    async def reset(self) -> None:
        """
        Resets the connection. Potentially after the connection was miscellaneously lost.
        May be overriden by subclass for more specific behavior.
        :return:
        """
        await self.close()
        await self.open()

    @abc.abstractmethod
    def get_identifier(self) -> str:
        pass

    @abc.abstractmethod
    def get_bootloader_type(self) -> str:
        pass

    @abc.abstractmethod
    async def read(self, timeout_ms: Optional[int] = None) -> Union[bytes, None]:
        """
        Reads received data. The bytes returned should be the entire data assembled.
        :return: Bytes of the data
        """
        raise NotImplementedError("Read Method Not Implemented")

    @abc.abstractmethod
    async def write(self, data: bytes) -> None:
        """
        Writes the provided bytes of data.
        :param data: Bytes of the data to be written
        """
        raise NotImplementedError("Write Method Not Implemented")

    async def ping(self, data: bytes) -> float:
        """
        Sends the data over the protocol and awaits acknowledgment while measuring duration.
        Not supported by all protocols.
        :param data: Arbitrary sized data to send over the protocol
        :return: Duration in seconds between sending the data and receiving ACK
        """
        raise NotImplementedError("Ping Method Not Implemented")

    async def echo(self, data: bytes) -> bytes:
        """
        Sends the data over the protocol and returning the data received back. The expectation is that the data
        received is the same as the data sent. Not supported by all protocols.
        :param data: Arbitrary sized data to send over the protocol
        :return: Data received over the protocol
        """
        raise NotImplementedError("Echo Method Not Implemented")

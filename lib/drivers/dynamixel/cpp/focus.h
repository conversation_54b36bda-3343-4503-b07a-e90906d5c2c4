#pragma once

#include <algorithm>
#include <memory>

#include <dynamixel_sdk/dynamixel_sdk.h>

#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace drivers {
namespace dynamixel {

constexpr uint16_t kAddrTorqueEnable = 64;
constexpr uint16_t kAddrPositionPGain = 84;
constexpr uint16_t kAddrPositionIGain = 82;
constexpr uint16_t kAddrPositionDGain = 80;
constexpr uint16_t kAddrGoalPosition = 116;
constexpr uint16_t kAddrMoving = 122;
constexpr uint16_t kAddrPresentPosition = 132;

constexpr uint32_t kPositionTicks = 4096;
constexpr uint16_t kPositionPGain = 3000;
constexpr uint16_t kPositionIGain = 0;
constexpr uint16_t kPositionDGain = 0;

constexpr size_t kCommRetries = 10;

class FocusControl {
public:
  FocusControl(std::string port, uint8_t id)
      : port_(port), id_(id), port_handler_(::dynamixel::PortHandler::getPortHandler(port.c_str())),
        packet_handler_(::dynamixel::PacketHandler::getPacketHandler()) {
    if (!port_handler_->openPort()) {
      throw std::runtime_error("Unable to open port " + port);
    }

    set_torque(true);
    set_pid();
  }

  ~FocusControl() {
    set_torque(false);

    port_handler_->closePort();
    delete port_handler_;
  }

  void set_torque(bool value) {
    uint8_t error;
    size_t comm_retries = 0;
    int comm_result = -1;
    while (comm_result != COMM_SUCCESS && comm_retries++ < kCommRetries) {
      comm_result = packet_handler_->write1ByteTxRx(port_handler_, id_, kAddrTorqueEnable, (uint8_t)value, &error);
    }
    check_error(value ? "enable torque" : "disable torque", comm_result, error);
  }

  bool get_torque() {
    uint8_t error;
    uint8_t data;
    size_t comm_retries = 0;
    int comm_result = -1;
    while (comm_result != COMM_SUCCESS && comm_retries++ < kCommRetries) {
      comm_result = packet_handler_->read1ByteTxRx(port_handler_, id_, kAddrTorqueEnable, &data, &error);
    }
    check_error("check torque", comm_result, error);
    return (bool)data;
  }

  void set_pid() {
    uint8_t error;
    {
      size_t comm_retries = 0;
      int comm_result = -1;
      while (comm_result != COMM_SUCCESS && comm_retries++ < kCommRetries) {
        comm_result = packet_handler_->write2ByteTxRx(port_handler_, id_, kAddrPositionPGain, kPositionPGain, &error);
      }
      check_error("set position P gain", comm_result, error);
    }
    {
      size_t comm_retries = 0;
      int comm_result = -1;
      while (comm_result != COMM_SUCCESS && comm_retries++ < kCommRetries) {
        comm_result = packet_handler_->write2ByteTxRx(port_handler_, id_, kAddrPositionIGain, kPositionIGain, &error);
      }
      check_error("set position I gain", comm_result, error);
    }
    {
      size_t comm_retries = 0;
      int comm_result = -1;
      while (comm_result != COMM_SUCCESS && comm_retries++ < kCommRetries) {
        comm_result = packet_handler_->write2ByteTxRx(port_handler_, id_, kAddrPositionDGain, kPositionDGain, &error);
      }
      check_error("set position D gain", comm_result, error);
    }
  }

  // Focus range -1..1 corresponds to goal positions 0..kPositionTicks-1.
  void set_focus(float value) {
    uint8_t error;
    size_t comm_retries = 0;
    int comm_result = -1;
    while (comm_result != COMM_SUCCESS && comm_retries++ < kCommRetries) {
      comm_result = packet_handler_->write4ByteTxRx(port_handler_, id_, kAddrGoalPosition,
                                                    focus_to_position_ticks(value), &error);
    }
    check_error("set goal position", comm_result, error);
  }

  float get_focus() {
    uint8_t error;
    uint32_t data;
    size_t comm_retries = 0;
    int comm_result = -1;
    while (comm_result != COMM_SUCCESS && comm_retries++ < kCommRetries) {
      comm_result = packet_handler_->read4ByteTxRx(port_handler_, id_, kAddrPresentPosition, &data, &error);
    }
    check_error("get current position", comm_result, error);
    return position_ticks_to_focus(data);
  }

  bool is_moving() {
    uint8_t error;
    uint8_t data;
    size_t comm_retries = 0;
    int comm_result = -1;
    while (comm_result != COMM_SUCCESS && comm_retries++ < kCommRetries) {
      comm_result = packet_handler_->read1ByteTxRx(port_handler_, id_, kAddrMoving, &data, &error);
    }
    check_error("check moving state", comm_result, error);
    return (bool)data;
  }

private:
  std::string port_;
  uint8_t id_;
  ::dynamixel::PortHandler *port_handler_;
  ::dynamixel::PacketHandler *packet_handler_;

  void check_error(std::string action, int comm_result, uint8_t error) {
    if (comm_result != COMM_SUCCESS) {
      throw maka_error("Failed to " + action + ": " + std::string(packet_handler_->getTxRxResult(comm_result)));
    }
    if (error != 0) {
      throw maka_error("Failed to " + action + ": " + std::string(packet_handler_->getRxPacketError(error)));
    }
  }

  uint32_t focus_to_position_ticks(float value) {
    uint32_t half = kPositionTicks / 2;
    return uint32_t(std::max(std::min(half * (1.0f + value), kPositionTicks - 1.0f), 0.0f));
  }

  float position_ticks_to_focus(uint32_t value) {
    uint32_t half = kPositionTicks / 2;
    return float(value) / float(half) - 1.0f;
  }
};

} // namespace dynamixel
} // namespace drivers
} // namespace lib

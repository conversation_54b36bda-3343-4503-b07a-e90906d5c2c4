import asyncio
import time
from argparse import Argument<PERSON>arser
from functools import reduce

import cv2
import matplotlib
import numpy as np
import serial
import serial.tools.list_ports
from cv2 import aruco
from pypylon import pylon

import lib.common.logging
import qwiic_serlcd
from firmware.release.firmware_release_manager import FirmwareVersion, get_latest_firmware_version
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.errors.error import RetryableMakaDeviceException
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import (
    PulczarBoardConnector,
    PulczarBoardException,
    PulczarServoPIDConfig,
)
from lib.drivers.psoc_ethernet.psoc_ethernet_bootloader import program
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector
from lib.drivers.scanner_tester.sheets_interface import PulczarStatusSheet

LOG = lib.common.logging.get_logger(__name__)

PORT = 4243
SERVO_HIGH = 10000
REBOOT_TIME_S = 7

test_type = {
    "None": 255,
    "All Low": 0,
    "All High": 1,
    "WP High Others Low": 2,
    "Set Addr": 3,
    "All Input": 4,
    "Camera 10 FPS Trigger": 5,
}

# Disable Bare Except Restriction and Function Complexity
# flake8: noqa: E722, C901


def _make_pulczar_board_ip(scanner_id: int) -> str:
    return f"10.11.4.{scanner_id}"


def _make_pulczar_bootloader_ip(scanner_id: int) -> str:
    return f"10.11.4.{scanner_id + 10}"


async def _ping_test(board: PulczarBoardConnector) -> bool:
    print("Starting Connectivity Test...")
    count = 0
    while True:
        count += 1
        try:
            await board.ping()
            break
        except RetryableMakaDeviceException:
            if count > 10:
                return False
    print("Ping Connectivity Successful")
    return True


async def _continue_or_skip() -> bool:
    cmd = await asyncio.get_event_loop().run_in_executor(None, input)
    if cmd == "skip":
        print("Skipping Test")
        return True
    return False


"""
Spoof various inputs to Pulczar board
"""


async def _status_test(board: PulczarBoardConnector, ser: serial.Serial, timeout: int) -> bool:
    print("Door Test...")
    print("Door has been cut from the design, so Door pin should be jumped")
    while True:
        status = await board.get_status()
        if not status.door:
            print(f"Failure, DOOR is False: \n{status}\n Please ensure door pin is jumped and hit enter or type 'skip'")
            if await _continue_or_skip():
                break
        else:
            print("Door Test Successful")
            break

    print("All Input Status Test...")
    toWrite = (test_type["All Input"]).to_bytes(1, "big")
    numBytesWritten = ser.write(toWrite)
    ser.flush()
    print(f"wrote {numBytesWritten} bytes to Arduino: {int.from_bytes(toWrite, 'big')}")
    print(f"{ser.out_waiting} bytes waiting to be written")
    print(f"response from arduino: {ser.read(1)}")
    wpVoltage = int.from_bytes(ser.read(1), "big") / 255 * 5.0
    print(f"WP voltage read: {wpVoltage}")
    if wpVoltage < 1:
        print("Sucess, Water Protect pin has expected default value")
    else:
        print("Failure, Water Protect default voltage too high")
        return False
    flowMeterVoltage = int.from_bytes(ser.read(1), "big") / 255 * 5.0
    print(f"flow meter voltage read: {flowMeterVoltage}")
    if flowMeterVoltage < 0.5:
        print("Sucess, Flow meter reads 0 flow.")
    else:
        print("Failure, Flow meter reads non 0 flow, or constant non 0 voltage")
        return False

    print("All Low Status Test...")
    toWrite = (test_type["All Low"]).to_bytes(1, "big")
    numBytesWritten = ser.write(toWrite)
    ser.flush()
    print(f"wrote {numBytesWritten} bytes to Arduino: {int.from_bytes(toWrite, 'big')}")
    print(f"{ser.out_waiting} bytes waiting to be written")
    print(f"response from arduino: {ser.read(1)}")
    start_time = time.time()
    while True:
        status = await board.get_status()

        if status.arm or status.flow or status.water or status.fire or status.dog or status.power:
            print(
                f"Failure, not all statuses False: \n{status}\n Please disable all status signal except DOOR and hit enter or type 'skip'"
            )
        else:
            print("Low Status Test Successful")
            break
        if time.time() - start_time > timeout:
            return False
        time.sleep(0.1)

    print("Testing Overrides...")
    toWrite = (test_type["All Low"]).to_bytes(1, "big")
    numBytesWritten = ser.write(toWrite)
    ser.flush()
    print(f"wrote {numBytesWritten} bytes to Arduino: {int.from_bytes(toWrite, 'big')}")
    print(f"{ser.out_waiting} bytes waiting to be written")
    print(f"response from arduino: {ser.read(1)}")

    toWrite = (test_type["WP High Others Low"]).to_bytes(1, "big")
    numBytesWritten = ser.write(toWrite)
    ser.flush()
    print(f"wrote {numBytesWritten} bytes to Arduino: {int.from_bytes(toWrite, 'big')}")
    print(f"{ser.out_waiting} bytes waiting to be written")
    print(f"response from arduino: {ser.read(1)}")

    status = await board.get_status()
    if not status.water:
        print("Water Override Failed")
        return False

    toWrite = (test_type["All Low"]).to_bytes(1, "big")
    numBytesWritten = ser.write(toWrite)
    ser.flush()
    print(f"wrote {numBytesWritten} bytes to Arduino: {int.from_bytes(toWrite, 'big')}")
    print(f"{ser.out_waiting} bytes waiting to be written")
    print(f"response from arduino: {ser.read(1)}")
    print("Overrides Test Successful")

    print("All High Status Test...")
    toWrite = (test_type["All High"]).to_bytes(1, "big")
    numBytesWritten = ser.write(toWrite)
    ser.flush()
    print(f"wrote {numBytesWritten} bytes to Arduino: {int.from_bytes(toWrite, 'big')}")
    print(f"{ser.out_waiting} bytes waiting to be written")
    print(f"response from arduino: {ser.read(1)}")
    start_time = time.time()
    while True:
        status = await board.get_status()
        if not status.flow or not status.water or not status.door or not status.power:
            print(
                f"Failure, not all of [FLOW, WATER, DOOR, POWER] are True: \n{status}\n Please enable all status signal so they show up as True and hit enter or type 'skip'"
            )
        else:
            print("High Status Test Successful")
            break
        if time.time() - start_time > timeout:
            return False
        time.sleep(0.1)

    running: bool = True
    firing = False

    async def _watch_dog() -> None:
        await board.dawg_config(1000)
        value: int = 0
        while running:
            await board.laser_intensity(value % (2 ** 16))
            value += 5000
            await board.dawg_pet(firing)
            await asyncio.sleep(0.300)

    task = asyncio.get_event_loop().create_task(_watch_dog())
    try:
        await board.dawg_arm(True)
        firing = True
        await board.laser_set(firing)

    finally:
        running = False
        await board.dawg_arm(False)
        firing = False
        await board.laser_set(firing)
        await task

    return True


"""
Moves Servos
"""


async def _scanner_test(board: PulczarBoardConnector) -> bool:
    print("Gimbal/Servos Test...")
    await board.clear_config()
    await board.gimbal_config(1, 2, 590000, 1000, 50, 300)
    print("Gimbal Configured")
    await board.gimbal_configure_pids(*PulczarServoPIDConfig.default_pids())
    print("Configured Gimbal PID Values")
    start_time = time.time()
    try:
        await board.gimbal_boot(590000, 0, 30000, 1000, (1000, 1000))
        print(f"Gimbal Booted in {time.time() - start_time} seconds")
        await board.clear_gimbal_fault()

        limit_pan, limit_tilt = await board.gimbal_get_limits()
        print(f"Limits: {(limit_pan, limit_tilt)}")

        if limit_pan[1] < SERVO_HIGH or limit_tilt[1] < SERVO_HIGH:
            print(f"Servos Limits Should be above {SERVO_HIGH}, If limits are too low, please mark as failure")
            await _continue_or_skip()
            return False

        await board.gimbal_go_to((limit_pan[0], limit_tilt[0]), (590000, 590000), True)
        await asyncio.sleep(0.2)
        await board.gimbal_go_to((limit_pan[1], limit_tilt[1]), (590000, 590000), True)
        await asyncio.sleep(0.2)
        print("Successfully went to limits")

        await board.gimbal_go_to((int(limit_pan[1] / 2), int(limit_tilt[1] / 2)), (590000, 590000), True)
        await asyncio.sleep(0.2)
        print("Successfully Centered Servos")

        await board.gimbal_stop()
        print("Gimbal Test Successful")

        return True
    except PulczarBoardException as e:
        print(e)
        return False


"""
Check focus at various different levels.
"""


async def _lens_test(
    board: PulczarBoardConnector, camera: pylon.InstantCamera, ser: serial.Serial, timeout: int
) -> bool:
    print("Lens and Camera Test...")
    camera.Open()
    camera.ExposureTimeAbs.SetValue(5000.0)
    camera.ExposureAuto.SetValue("Off")
    camera.TriggerMode.SetValue("Off")

    camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)

    arucoDict = aruco.getPredefinedDictionary(aruco.DICT_ARUCO_ORIGINAL)
    arucoParams = aruco.DetectorParameters()
    arucoDetector = aruco.ArucoDetector(arucoDict, arucoParams)

    expected_fiducial_ids = [498, 414, 680, 935, 544, 529, 775, 927, 100, 336, 345, 859, 919, 137, 47]

    focus = 0
    await board.lens_set(focus)
    found_fiducials = False
    while camera.IsGrabbing() and focus < 255:
        grabResult = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)

        if grabResult.GrabSucceeded():
            img = grabResult.Array
            img = cv2.cvtColor(img, cv2.COLOR_BAYER_GB2RGB)
            # img = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
            alpha = 1.5  # contrast
            img = np.multiply(img, alpha)  # , dtype='uint8', casting='unsafe')
            img = np.minimum(img, 255)  # , dtype='uint8', casting='unsafe')
            # img = np.asarray([[[int(val) for val in pix] for pix in row] for row in img])
            img = img.astype("uint8")
            # print(f"dtype of img: {type(img)}, shape of img: {img.shape}")
            # print(f"rgb: {img[0][0]}")
            (corners, ids, rejected) = arucoDetector.detectMarkers(img)
            # print(f"at focus = {focus}, corners: {corners}, ids: {ids}, rejected: {rejected}")
            # img_to_save.AttachGrabResultBuffer(grabResult)
            # img_to_save.Save(pylon.ImageFileFormat_Png, f"image_at_focus_{focus}.png")
            # img_to_save.Release()
            if len(corners) == 20 and len(ids) == 20:
                found_expected_ids = reduce(lambda x, y: x and y, [id in expected_fiducial_ids for id in ids])
                print(f"\n\nFound all markers at focus {focus}.\n")
                if found_expected_ids:
                    print("all ids match expected ids")
                img_marked = aruco.drawDetectedMarkers(img.copy(), corners, ids)
                cv2.imwrite(f"image_at_focus_{focus}.png", img_marked)
                found_fiducials = True
            elif len(corners) > 0:
                print(f"found {len(corners)} fiducials at focus {focus}")
                img_marked = aruco.drawDetectedMarkers(img.copy(), corners, ids)
                cv2.imwrite(f"image_at_focus_{focus}.png", img_marked)
                if len(corners) >= 18:
                    found_fiducials = True
            else:
                cv2.imwrite(f"image_at_focus_{focus}.png", img)

            focus += 1
            await board.lens_set(focus)
            if await board.lens_get() != focus:
                print("Failed to Set Lens Value")
                return False
        else:
            print("Error: ", grabResult.ErrorCode, grabResult.ErrorDescription)
        grabResult.Release()
    camera.Close()

    if not found_fiducials:
        print("Failed to find all expected fiducials.")
        # return False

    print("Ensure that the Front Panel Fan is pushing air inward and the camera fan is turned on.")
    print("Ensure both lock screws in the front panel have no defect.")

    # print("Setup Line 2 for Exposure Active as Line Source in Digital I/O. Hit Enter")
    # print("Watch Oscilloscope and ensure that exposure time square waves are displayed right. Hit Enter")
    # the above two are related to the strobe daughter board. ignore here.

    print("Activating Trigger at 5 Hz")
    camera.Open()
    camera.StartGrabbing(pylon.GrabStrategy_LatestImageOnly)

    toWrite = (test_type["Camera 10 FPS Trigger"]).to_bytes(1, "big")
    numBytesWritten = ser.write(toWrite)
    camera.ExposureTimeAbs.SetValue(100.0)

    camera.TriggerSelector.SetValue("FrameStart")
    camera.TriggerSource.SetValue("Line1")
    camera.TriggerActivation.SetValue("RisingEdge")
    camera.TriggerMode.SetValue("On")

    camera.LineSelector.SetValue("Line2")
    camera.LineSource.SetValue("ExposureActive")
    start_time = time.time()  # in seconds
    frames = 0

    curr_frame_rate = frames / (time.time() - start_time)
    timeout_res = False
    while curr_frame_rate < 4.9 or curr_frame_rate > 5.1:
        grabResult = camera.RetrieveResult(5000, pylon.TimeoutHandling_ThrowException)
        if grabResult.GrabSucceeded():
            frames += 1
        curr_frame_rate = frames / (time.time() - start_time)
        if frames % 25 == 0:
            frames = 0
            start_time = time.time()
            print(f"Current framerate: {curr_frame_rate}")
        if time.time() - start_time > timeout:
            print("Timed out waiting for trigger frame rate")
            timeout_res = True
    if not timeout_res or not found_fiducials:
        print("Lens and Camera Test Failed")
        return False

    print("Lens and Camera Test Succeeded")

    return True


async def _scanner_id_test(board: PulczarBoardConnector, scanner_id: int) -> PulczarBoardConnector:
    print(f"Testing Scanner ID {scanner_id}")
    await board.hard_reset()
    await asyncio.sleep(REBOOT_TIME_S)
    ip = _make_pulczar_board_ip(scanner_id)
    connector = PsocMEthernetConnector(ip, PORT, asyncio.get_event_loop())
    await connector.open()
    board = PulczarBoardConnector(connector)
    await _ping_test(board)
    print(f"Scanner ID {scanner_id} Test Successful")
    return board


async def _scanner_addr_test(board: PulczarBoardConnector, scanner_id: int, msg: str) -> PulczarBoardConnector:
    while True:
        print(msg)

        await _reset_all()

        try:
            board = await _scanner_id_test(board, scanner_id)
            break
        except:
            print("Failed to Test Address, try again")

    return board


async def _addr_test(board: PulczarBoardConnector, ser: serial.Serial) -> bool:
    for addr in [2, 3, 5, 1]:
        ser.write((test_type["Set Addr"]).to_bytes(1, "big"))
        ser.write((addr).to_bytes(1, "big"))
        print(f"response from arduino: currAddr: {ser.read(1)}")
        print(f"response from arduino: currTest: {ser.read(1)}")

        board = await _scanner_addr_test(board, addr, f"Verifying Address {addr}")

    print("ADDR Test Successful")
    return True


async def _reset_all() -> None:
    for i in range(8):
        scanner_id = i + 1
        ip = _make_pulczar_board_ip(scanner_id)
        connector = PsocMEthernetConnector(ip, PORT, asyncio.get_event_loop())
        await connector.open()
        board = PulczarBoardConnector(connector)
        await board.hard_reset()


def _setup_serial() -> serial.Serial:
    chosenCom = ""
    ports = list(serial.tools.list_ports.comports())
    for p in ports:
        print(p)
        if "Arduino" in p.description or "ACM" in p.description or "cu.usbmodem" in p[0]:
            chosenCom = p[0]
            print("Chosen COM: {}".format(p))
    if not chosenCom:
        print(" Failure: No Valid Com Found")
        return None
    print("Chosen COM {}".format(chosenCom))
    baudrate = 9600
    print("Baud Rate {}".format(baudrate))
    try:
        ser = serial.Serial(chosenCom, baudrate, timeout=3)
    except Exception as e:
        print("Failure: Invalid Serial Connection")
        return None
    ser.flushInput()
    return ser


async def _flash_bad_firmware(scanner_id: int) -> None:
    print("Testing Bad Firmware...")
    ip = _make_pulczar_bootloader_ip(scanner_id)
    firmware = await asyncio.get_event_loop().run_in_executor(
        None, lambda: get_latest_firmware_version("BadPulczarTest")
    )
    assert firmware is not None
    print(f"Flashing Bad Firmware: {firmware.path}")
    await _reset_all()
    await asyncio.sleep(2)
    print(f"Reaching Bootloader at IP: {ip}")
    await program(ip, PORT, firmware.path)
    print("Successfully Programmed Bad Firmware...")
    print("Rebooting Board... (May take several seconds)")
    await asyncio.sleep(REBOOT_TIME_S)
    ip = _make_pulczar_board_ip(scanner_id)
    connector = PsocMEthernetConnector(_make_pulczar_board_ip(scanner_id), PORT, asyncio.get_event_loop())
    await connector.open()
    board = PulczarBoardConnector(connector)
    try:
        await board.ping()
    except AssertionError:
        pass
    print("Bad Firmware Test Successful")


async def _flash_latest_firmware(scanner_id: int) -> FirmwareVersion:
    ip = _make_pulczar_bootloader_ip(scanner_id)
    firmware = await asyncio.get_event_loop().run_in_executor(None, lambda: get_latest_firmware_version("Pulczar"))
    assert firmware is not None
    print(f"Flashing Firmware: {firmware.path}")
    await _reset_all()
    await asyncio.sleep(2)
    print(f"Reaching Bootloader at IP: {ip}")
    await program(ip, PORT, firmware.path)
    print(f"Successfully Programmed Firmware Version: {firmware.version}")
    print("Rebooting Board... (May take several seconds)")
    await asyncio.sleep(REBOOT_TIME_S)
    return firmware.version


def _bool_str(stat: bool) -> str:
    return "Pass" if stat else "Fail"


async def _validate(scanner_id: int) -> None:
    TIMEOUT = 20  # seconds
    lcd = qwiic_serlcd.QwiicSerlcd()  # 16x2
    if not lcd.connected:
        print("The Sparkfun LCD is not connected, or i2c is not enabled on this device.\n")
        return
    lcd.setBacklight(255, 255, 255)
    lcd.setContrast(5)
    lcd.clearScreen()
    # WIP:
    # keypad = qwiic_keypad.QwiicKeypad()
    # if not keypad.connected:
    #     print("Keypad not connected")
    #     lcd.print("Keypad not      connected!")
    #     return
    # keypad.begin()
    sheet = PulczarStatusSheet()
    sheet.get_all_logs()
    lcd.clearScreen()
    lcd.print("Connected to Sheet!")
    camera = pylon.InstantCamera(pylon.TlFactory.GetInstance().CreateFirstDevice())
    camera.Open()
    camera_sn = camera.GetDeviceInfo().GetSerialNumber()
    print(f"camera sn: {camera_sn}, type : {type(camera_sn)}")
    camera.Close()
    log_exists, existing_log = sheet.find_existing_log(camera_sn)
    lcd.clearScreen()
    if log_exists:
        lcd.print(f"SN: {list(existing_log['SN'])[0]}")
    else:
        lcd.print(f"New Scanner...  ")
        time.sleep(1)
        lcd.print("New Scanner...  ")
        time.sleep(0.5)
        lcd.print("Enter new SN    ")
        lcd.print("Press #")

    await _flash_bad_firmware(scanner_id)
    lcd.clearScreen()
    lcd.print("Succesfully flashed bad fw!")
    latest_version = await _flash_latest_firmware(scanner_id)
    lcd.clearScreen()
    lcd.print("Succesfully flashed latest fw!")
    ip = _make_pulczar_board_ip(scanner_id)
    connector = PsocMEthernetConnector(_make_pulczar_board_ip(scanner_id), PORT, asyncio.get_event_loop())
    print(f"Opening Ethernet Connector at IP: {ip}, All ADDR Pin should be connected")
    await connector.open()
    print(f"Ethernet Connector Successfully Opened")
    lcd.clearScreen()
    lcd.print("Eth connector successfully open")
    board = PulczarBoardConnector(connector)
    ser = _setup_serial()

    ping_status = await _ping_test(board)
    lcd.clearScreen()
    lcd.print(f"ping status: {_bool_str(ping_status)}")
    version = await board.get_version()
    flash_good_fw = version.is_equal(latest_version)
    lcd.clearScreen()
    lcd.print(f"version verification: {_bool_str(flash_good_fw)}")
    await board.clear_config()
    scanner_test = await _scanner_test(board)
    lcd.clearScreen()
    lcd.print(f"Gimbal test: {_bool_str(scanner_test)}")
    pin_status_test = await _status_test(board, ser, TIMEOUT)
    lcd.clearScreen()
    lcd.print(f"Pins Status test: {_bool_str(pin_status_test)}")
    addr_test = await _addr_test(board, ser)
    lcd.clearScreen()
    lcd.print(f"Address test: {_bool_str(addr_test)}")
    toWrite = (test_type["None"]).to_bytes(1, "big")
    numBytesWritten = ser.write(toWrite)

    lens_test = await _lens_test(board, camera, ser, TIMEOUT)
    lcd.clearScreen()
    lcd.print(f"Lens/ Cam test: {_bool_str(lens_test)}")
    if log_exists:
        sheet.update_log(
            camera_sn,
            latest_version.__repr__(),
            ping_status,
            scanner_test,
            pin_status_test,
            addr_test,
            lens_test,
            True,
            flash_good_fw,
        )
    else:
        sheet.add_log_to_sheet(
            camera_sn,
            latest_version.__repr__(),
            ping_status,
            scanner_test,
            pin_status_test,
            addr_test,
            lens_test,
            True,
            flash_good_fw,
        )

    if ping_status and scanner_test and pin_status_test and addr_test and lens_test and flash_good_fw:
        print("PULCZAR SCANNER VALIDATION SUCCESSFUL")
        lcd.clearScreen()
        lcd.print("All Tests Passed")
    else:
        print("FAIL FAIL FAIL FAIL")
        lcd.clearScreen()
        lcd.print("FAIL. See logs on google sheet")


def main() -> None:
    parser = ArgumentParser("Pulczar Board Validator")
    parser.add_argument("-s", "--scanner", default=1, type=int)
    args = parser.parse_args()
    lib.common.logging.init_log(level="DEBUG")
    future = _validate(args.scanner)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

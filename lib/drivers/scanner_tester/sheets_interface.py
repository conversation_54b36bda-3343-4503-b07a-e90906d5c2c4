from __future__ import print_function

import os.path
from datetime import datetime
from typing import Tuple

import pandas as pd
from google.oauth2 import service_account

from googleapiclient.discovery import build

SERVICE_ACCOUNT_FILE = os.path.realpath(
    os.path.join(os.getcwd(), os.path.dirname(__file__), "service-account-credentials.json")
)
SCOPES = ["https://www.googleapis.com/auth/spreadsheets"]

PULCZAR_STATUS_LOG_SHEET_ID = "1GFB63u4hXV7DcC-zoLCCDJ8gVm06Q6UFk-s-T9GNu6E"


def _bool_str(stat: bool) -> str:
    return "Pass" if stat else "Fail"


class PulczarStatusSheet:

    FULL_DATA_RANGE = "Sheet1!A:J"

    def __init__(self) -> None:
        creds = service_account.Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE, scopes=SCOPES)

        service = build("sheets", "v4", credentials=creds)

        # Call the Sheets API
        self.sheet = service.spreadsheets()

    def get_all_logs(self) -> None:
        result = (
            self.sheet.values().get(spreadsheetId=PULCZAR_STATUS_LOG_SHEET_ID, range=self.FULL_DATA_RANGE).execute()
        )
        values = result.get("values", [])
        statusLogs = {values[0][i]: [row[i] for row in values[1:]] for i in range(len(values[0]))}
        self.curr_data = pd.DataFrame.from_dict(statusLogs)

    def find_existing_log(self, camera_sn: str) -> Tuple[bool, pd.DataFrame]:
        if len(self.curr_data) == 0:
            self.get_all_logs()
        if camera_sn in self.curr_data["Camera SN"].to_list():
            print("found cam sn")
            print(self.curr_data[self.curr_data["Camera SN"] == camera_sn])
            return True, self.curr_data[self.curr_data["Camera SN"] == camera_sn]
        else:
            print("did not find cam sn")
            return False, pd.DataFrame()

    def has_existing_log(self, camera_sn: str) -> bool:
        if len(self.curr_data) == 0:
            self.get_all_logs()
        toreturn = camera_sn in self.curr_data["Camera SN"].to_list()
        print(f"value to be returned: {toreturn}")
        return toreturn

    def update_log(
        self,
        cam_sn: str,
        fw_version: str,
        ping_test: bool,
        scanner_test: bool,
        pin_test: bool,
        addr_test: bool,
        lens_test: bool,
        bad_fw_flash: bool,
        good_fw_flash: bool,
        last_updated: str = datetime.utcnow().isoformat(),
    ) -> None:
        print(f"last update: {last_updated}")
        values_to_write = [
            [
                int(cam_sn),
                fw_version,
                _bool_str(ping_test),
                _bool_str(scanner_test),
                _bool_str(pin_test),
                _bool_str(addr_test),
                _bool_str(lens_test),
                _bool_str(bad_fw_flash),
                _bool_str(good_fw_flash),
                last_updated,
            ]
        ]
        body = {"values": values_to_write}
        tmp_val = self.curr_data.index[self.curr_data["Camera SN"] == cam_sn].tolist()[0]
        print(f"supposed index: {tmp_val}")
        row_num = tmp_val + 2  # + 1 for 0 indexing, + 1 for top row headers
        result = (
            self.sheet.values()
            .update(
                spreadsheetId=PULCZAR_STATUS_LOG_SHEET_ID,
                range=f"B{row_num}:K{row_num}",
                valueInputOption="RAW",
                body=body,
            )
            .execute()
        )
        print(f"{result.get('updatedCells')} cells updated.")
        print(result)

    def add_log_to_sheet(
        self,
        cam_sn: str,
        fw_version: str,
        ping_test: bool,
        scanner_test: bool,
        pin_test: bool,
        addr_test: bool,
        lens_test: bool,
        bad_fw_flash: bool,
        good_fw_flash: bool,
        last_updated: str = datetime.utcnow().isoformat(),
    ) -> None:
        print(f"last updated: {last_updated}")
        sn = (int)(max(self.curr_data["SN"].to_numpy("int"))) + 1
        print(f"choosing new sn: {sn}")
        print(f"firmware version type: {type(fw_version)}, value: {fw_version}")
        values_to_write = [
            [
                sn,
                int(cam_sn),
                fw_version,
                _bool_str(ping_test),
                _bool_str(scanner_test),
                _bool_str(pin_test),
                _bool_str(addr_test),
                _bool_str(lens_test),
                _bool_str(bad_fw_flash),
                _bool_str(good_fw_flash),
                last_updated,
            ]
        ]
        body = {"values": values_to_write}
        result = (
            self.sheet.values()
            .update(
                spreadsheetId=PULCZAR_STATUS_LOG_SHEET_ID,
                range=f"A{len(self.curr_data)+2}:K{len(self.curr_data)+2}",
                valueInputOption="RAW",
                body=body,
            )
            .execute()
        )
        print(f"{result.get('updatedCells')} cells updated.")
        print(result)

import asyncio
from typing import List, cast

from lib.drivers.carbon_serial.carbon_serial import CarbonSerialConnector
from lib.drivers.nanopb.pin_controller.pin_controller_connector import (
    PIN_HIGH,
    PIN_LOW,
    PIN_MODE_INPUT_PULL_UP,
    PIN_MODE_OUTPUT,
    DPVVType,
    PinControllerConnector,
)


class MegaScannerTesterDevice:
    def __init__(
        self,
        serial_path: str = "/dev/serial/by-id/usb-Arduino__www.arduino.cc__0042_85130303538351214042-if00",
        addr0_pin: int = 50,
        addr1_pin: int = 51,
        addr2_pin: int = 52,
        addr3_pin: int = 53,
        fire_pin: int = 2,
        lpsu_pwr_pin: int = 3,
        cam_trigger_pin: int = 5,
        intensity_pin: int = 1,
        current_pin: int = 4,
    ) -> None:
        self._serial_path = serial_path
        self._addr0_pin = addr0_pin
        self._addr1_pin = addr1_pin
        self._addr2_pin = addr2_pin
        self._addr3_pin = addr3_pin
        self._fire_pin = fire_pin
        self._lpsu_pwr_pin = lpsu_pwr_pin
        self._cam_trigger_pin = cam_trigger_pin
        self._intensity_pin = intensity_pin
        self._current_pin = current_pin

        self._serial = CarbonSerialConnector(self._serial_path, asyncio.get_event_loop(), delay_ms=1000,)
        self._connector = PinControllerConnector(self._serial)

    async def start(self) -> None:
        await self._serial.open()

        await self._connector.configure_pin(self._addr0_pin, PIN_MODE_OUTPUT)
        await self._connector.digital_write(self._addr0_pin, PIN_HIGH)

        await self._connector.configure_pin(self._addr1_pin, PIN_MODE_OUTPUT)
        await self._connector.digital_write(self._addr1_pin, PIN_HIGH)

        await self._connector.configure_pin(self._addr2_pin, PIN_MODE_OUTPUT)
        await self._connector.digital_write(self._addr2_pin, PIN_HIGH)

        await self._connector.configure_pin(self._addr3_pin, PIN_MODE_OUTPUT)
        await self._connector.digital_write(self._addr3_pin, PIN_HIGH)

        await self._connector.configure_pin(self._fire_pin, PIN_MODE_INPUT_PULL_UP)

        await self._connector.configure_pin(self._lpsu_pwr_pin, PIN_MODE_OUTPUT)
        await self._connector.digital_write(self._lpsu_pwr_pin, PIN_HIGH)

        await self._connector.configure_pin(self._cam_trigger_pin, PIN_MODE_OUTPUT)
        await self._connector.digital_write(self._cam_trigger_pin, PIN_LOW)

        await self._connector.configure_pwm(self._current_pin, 0)

    async def stop(self) -> None:
        await self._connector.stop()
        await self._serial.close()

    @staticmethod
    def num_to_addr(num: int) -> List[int]:
        binary = format(num, "04b")[::-1]
        return [0 if int(binary[i]) == 1 else 1 for i in range(4)]

    async def write_addr(self, num: int) -> None:
        # However, the ip is still X.X.X.<num> instead of X.X.X.<num-1>
        addr = self.num_to_addr(num - 1)
        await self._connector.digital_write(self._addr0_pin, cast(DPVVType, addr[0]))
        await self._connector.digital_write(self._addr1_pin, cast(DPVVType, addr[1]))
        await self._connector.digital_write(self._addr2_pin, cast(DPVVType, addr[2]))
        await self._connector.digital_write(self._addr3_pin, cast(DPVVType, addr[3]))

    async def read_fire(self) -> int:
        return await self._connector.digital_read(self._fire_pin)

    async def read_intensity(self) -> float:
        return await self._connector.analog_read(self._intensity_pin)

    async def write_current(self, ratio: float) -> None:
        value = int(255 * ratio)
        await self._connector.configure_pwm(self._current_pin, value)

    async def write_lpsu_pwr(self, value: DPVVType) -> None:
        await self._connector.digital_write(self._lpsu_pwr_pin, value)

    async def trigger_cam(self, pulse_duration_us: int = 100000) -> None:
        await self._connector.digital_write(self._cam_trigger_pin, PIN_LOW)
        await asyncio.sleep(pulse_duration_us / 1000000)
        await self._connector.digital_write(self._cam_trigger_pin, PIN_HIGH)

import asyncio
import functools
import sys
from typing import Awaitable, Callable, <PERSON>ple

import numpy as np

import lib.common.logging
from lib.drivers.errors.error import FatalMakaDeviceException
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)

# ## Take from psoc header file
# define Bootloader_COMMAND_CHECKSUM          (0x31u)    /* Verify the checksum for the bootloadable project   */
# define Bootloader_COMMAND_REPORT_SIZE       (0x32u)    /* Report the programmable portions of flash          */
# define Bootloader_COMMAND_APP_STATUS        (0x33u)    /* Gets status info about the provided app status     */
# define Bootloader_COMMAND_ERASE             (0x34u)    /* Erase the specified flash row                      */
# define Bootloader_COMMAND_SYNC              (0x35u)    /* Sync the bootloader and host application           */
# define Bootloader_COMMAND_APP_ACTIVE        (0x36u)    /* Sets the active application                        */
# define Bootloader_COMMAND_DATA              (0x37u)    /* Queue up a block of data for programming           */
# define Bootloader_COMMAND_ENTER             (0x38u)    /* Enter the bootloader                               */
# define Bootloader_COMMAND_PROGRAM           (0x39u)    /* Program the specified row                          */
# define Bootloader_COMMAND_GET_ROW_CHKSUM    (0x3Au)    /* Compute flash row checksum for verification        */
# define Bootloader_COMMAND_EXIT              (0x3Bu)    /* Exits the bootloader & resets the chip             */
# define Bootloader_COMMAND_GET_METADATA      (0x3Cu)    /* Reports the metadata for a selected application    */
# define Bootloader_COMMAND_VERIFY_FLS_ROW    (0x45u)    /* Verifies data in buffer with specified row in flash*/

CMD_CHECKSUM = 0x31
CMD_REPORT_SIZE = 0x32
CMD_APP_STATUS = 0x33
CMD_ERASE = 0x34
CMD_SYNC = 0x35
CMD_APP_ACTIVE = 0x36
CMD_DATA = 0x37
CMD_ENTER = 0x38
CMD_PROGRAM = 0x39
CMD_GET_ROW_CHKSUM = 0x3A
CMD_EXIT = 0x3B
CMD_GET_METADATA = 0x3C
CMD_VERIFY_FLS_ROW = 0x45

STATUS_BYTE = 1
DATA_OFFSET = 4
HEADER_FOOTER_SIZE = 7

WAIT_SECS = 20


def get_data(res: bytes) -> bytes:
    return res[DATA_OFFSET:]


def int2rh(data: bytes, offset: int) -> int:
    return (data[offset] << 8) | data[offset + 1]


def int2r(data: bytes, offset: int) -> int:
    return (data[offset + 1] << 8) | data[offset]


def int2w(data: bytearray, offset: int, val: int) -> None:
    data[offset] = val & 0xFF
    data[offset + 1] = (val >> 8) & 0xFF


def put_size(data: bytearray, size: int) -> None:
    offset = 2
    data[offset] = size & 0xFF
    data[offset + 1] = (size >> 8) & 0xFF


def calc_checksum(data: bytearray) -> int:
    summation = int(np.sum(data))
    checksum = (((~summation) & 0xFFFF) + 1) & 0xFFFF
    return checksum


def finish_packet(data: bytearray, end_index: int) -> None:
    checksum = calc_checksum(data[0:end_index])
    data[end_index] = checksum & 0xFF
    end_index += 1
    data[end_index] = checksum >> 8
    end_index += 1
    data[end_index] = 0x17


def start_packet() -> bytearray:
    data = bytearray(HEADER_FOOTER_SIZE)
    data[0] = 0x1
    data[1] = CMD_ENTER
    put_size(data, 0)
    finish_packet(data, DATA_OFFSET)
    return data


def exit_packet() -> bytearray:
    packet = bytearray(HEADER_FOOTER_SIZE)
    packet[0] = 0x1
    packet[1] = CMD_EXIT
    put_size(packet, 0)
    finish_packet(packet, DATA_OFFSET)
    return packet


def flash_size_packet(array_id: int) -> bytearray:
    data = bytearray(HEADER_FOOTER_SIZE + 1)
    data[0] = 0x1
    data[1] = CMD_REPORT_SIZE
    put_size(data, 1)
    data[4] = array_id
    finish_packet(data, DATA_OFFSET + 1)
    return data


def send_data_packet(data: bytes) -> bytearray:
    packet = bytearray(len(data) + HEADER_FOOTER_SIZE)  # hello(1), cmd(1), size(2), cksum(2), end(1)
    packet[0] = 0x1
    packet[1] = CMD_DATA
    put_size(packet, len(data))
    packet[DATA_OFFSET : DATA_OFFSET + len(data)] = data
    finish_packet(packet, DATA_OFFSET + len(data))
    return packet


def program_row_packet(array_id: int, row_number: int) -> bytearray:
    packet = bytearray(3 + HEADER_FOOTER_SIZE)
    packet[0] = 0x1
    packet[1] = CMD_PROGRAM
    put_size(packet, 3)
    packet[DATA_OFFSET] = array_id
    int2w(packet, DATA_OFFSET + 1, row_number)
    finish_packet(packet, DATA_OFFSET + 3)
    return packet


def verify_row_packet(array_id: int, row_number: int) -> bytearray:
    packet = bytearray(3 + HEADER_FOOTER_SIZE)
    packet[0] = 0x1
    packet[1] = CMD_GET_ROW_CHKSUM
    put_size(packet, 3)
    packet[DATA_OFFSET] = array_id
    int2w(packet, DATA_OFFSET + 1, row_number)
    finish_packet(packet, DATA_OFFSET + 3)
    return packet


async def enter_bootloader(
    get_connector: Callable[[], Awaitable[PsocMEthernetConnector]]
) -> Tuple[PsocMEthernetConnector, int, str]:
    # Spin while attempting to connect to the board - it's booting
    cmd = start_packet()
    res = None

    connector = await get_connector()
    while True:
        sys.stdout.flush()
        await connector.write(cmd)
        res = await connector.read(500)
        if res is not None:
            break
        # reset the connector each time - the interface on the wiznet gets reset and
        # sometimes this messes with the connector
        await connector.close()
        connector = await get_connector()

    if res is None:
        raise FatalMakaDeviceException("Failed to Connect")

    if res[STATUS_BYTE] != 0:
        raise FatalMakaDeviceException(f"got error response {res[STATUS_BYTE]}")

    data = get_data(res)
    silicon_rev = data[4]
    bootloader_version = f"{data[5]}.{data[6]}.{data[7]}"
    return connector, silicon_rev, bootloader_version


async def get_flash_size(connector: PsocMEthernetConnector) -> Tuple[int, int]:
    cmd = flash_size_packet(0)
    await connector.write(cmd)
    res = await connector.read()

    if res is None:
        raise FatalMakaDeviceException("Failed to Read from Connector")

    if res[STATUS_BYTE] != 0:
        raise FatalMakaDeviceException(f"got error response {res[STATUS_BYTE]}")

    data = get_data(res)
    first_row = int2r(data, 0)
    last_row = int2r(data, 2)
    return first_row, last_row


async def send_data_row(connector: PsocMEthernetConnector, data: bytes) -> int:
    array_id = data[0]
    row_number = int2rh(data, 1)
    data_len = int2rh(data, 3)
    cksum = data[-1]

    assert data_len == len(data[5:-1])
    cmd = send_data_packet(data[5:-1])
    await connector.write(cmd)
    res = await connector.read()

    if res is None:
        raise FatalMakaDeviceException("Failed to Read from Connector")

    if res[STATUS_BYTE] != 0:
        raise FatalMakaDeviceException(f"send data fail: {res[STATUS_BYTE]}")

    cmd = program_row_packet(array_id, row_number)
    await connector.write(cmd)
    res = await connector.read()

    if res is None:
        raise FatalMakaDeviceException("Failed to Read from Connector")

    if res[STATUS_BYTE] != 0:
        if res[STATUS_BYTE] == 10:
            LOG.error("Bootloader too large.")
        raise FatalMakaDeviceException(f"program row fail: {res[STATUS_BYTE]}")

    cmd = verify_row_packet(array_id, row_number)
    await connector.write(cmd)

    res = await connector.read()
    if res is None:
        raise FatalMakaDeviceException("Failed to Read from Connector")

    if res[STATUS_BYTE] != 0:
        raise FatalMakaDeviceException(f"verify packet fail: {res[STATUS_BYTE]}")

    data = get_data(res)
    if False:
        # This always fails - we use the number from the file so that's confusing.
        if data[0] != cksum:
            LOG.debug(f"row {row_number} checksum fail {data[0]} vs {cksum}")

    return row_number


async def exit_bootloader(connector: PsocMEthernetConnector) -> None:
    cmd = exit_packet()
    await connector.write(cmd)


async def make_connector(host: str, port: int) -> PsocMEthernetConnector:
    connector = PsocMEthernetConnector(host, port, asyncio.get_event_loop())
    await connector.open()
    return connector


async def program(host: str, port: int, image: str) -> None:
    get_connector = functools.partial(make_connector, host, port)

    connector, silicon_rev, bootloader_version = await enter_bootloader(get_connector)
    LOG.debug(f"Silicon Rev: {silicon_rev}, BootLoader Version: {bootloader_version}")

    first_row, last_row = await get_flash_size(connector)
    LOG.debug(f"Flash info: first row {first_row}, last row {last_row}")

    f = open(image)
    f.readline()
    for l in f:
        data = bytes.fromhex(l.rstrip()[1:])
        row_number = await send_data_row(connector, data)
        LOG.debug(f"data row: {row_number}")

    await exit_bootloader(connector)

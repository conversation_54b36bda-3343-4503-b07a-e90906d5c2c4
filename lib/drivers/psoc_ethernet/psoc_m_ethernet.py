import asyncio
import functools
import socket
import time
from typing import TYPE_CHECKING, Optional, Tuple, Union, cast

import lib.common.logging
from lib.drivers.errors.error import RecoverableMakaDeviceException
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

LOG = lib.common.logging.get_logger(__name__)


if TYPE_CHECKING:
    AsyncQueue = asyncio.Queue[bytes]
else:
    AsyncQueue = asyncio.Queue


class PsocMEthernetConnector(MakaProtocolConnector):
    def __init__(
        self,
        host: str,
        port: int,
        loop: asyncio.AbstractEventLoop,
        bootloader_type: Optional[str] = None,
        interface_name: Optional[str] = None,
    ):
        """
        Create protocol connector for nanopb interface

        :param interface_name: System interface name to bind the socket to, if specified (only supported on Linux)
        """
        self._host = host
        self._port = port
        self._loop = loop
        self._bootloader_type = bootloader_type
        self._interface_name = interface_name
        self._read_queue: AsyncQueue = asyncio.Queue()
        self._transport: Optional[asyncio.DatagramTransport] = None
        self._protocol: Optional[PsocMEthernetProtocol] = None

    def get_identifier(self) -> str:
        return self._host

    def get_bootloader_type(self) -> str:
        return self._bootloader_type if self._bootloader_type is not None else "Invalid"

    async def open(self) -> None:
        self._transport, self._protocol = await self._create_connection()
        await self._protocol.ready()
        LOG.info(f"{self.__class__.__name__} Opened Connection to {self._host}:{self._port}")

    async def close(self) -> None:
        if self._transport is not None:
            self._transport.close()
        self._transport = None
        self._protocol = None

    async def _create_connection(self) -> Tuple[asyncio.DatagramTransport, "PsocMEthernetProtocol"]:
        remote_addr = (self._host, self._port)
        partial = functools.partial(
            PsocMEthernetProtocol, read_queue=self._read_queue, loop=self._loop, identifier=self.get_identifier(),
        )

        transport: Tuple[asyncio.DatagramTransport, "PsocMEthernetProtocol"]

        if self._interface_name:
            # create and prepare the socket manually
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

            s.setsockopt(socket.SOL_SOCKET, socket.SO_BINDTODEVICE, str(self._interface_name + "\0").encode("utf-8"))
            s.connect(remote_addr)

            # then create the datagram endpoint using this socket
            transport = cast(
                Tuple[asyncio.DatagramTransport, "PsocMEthernetProtocol"],
                await self._loop.create_datagram_endpoint(partial, sock=s),
            )
        else:
            transport = cast(
                Tuple[asyncio.DatagramTransport, "PsocMEthernetProtocol"],
                await self._loop.create_datagram_endpoint(
                    partial,
                    remote_addr=remote_addr,
                    # Required for some boards (namely Reaper MCB) which may be multihomed to work
                    # around issues in the Zephyr network stack, which leads to the wrong source
                    # address being selected. By enabling broadcast support, the kernel relaxes the
                    # acceptance checks for incoming packets. Since nanopb requests include a request
                    # id, we can still differentiate requests from different clients on the same
                    # system.
                    allow_broadcast=True,
                ),
            )

        return transport

    def _recovery_required(self) -> None:
        if self._protocol is None:
            raise RecoverableMakaDeviceException(
                f"{self.__class__.__name__} {self._host} connection has not been Opened yet"
            )

    async def read(self, timeout_ms: Optional[int] = None) -> Union[bytes, None]:
        self._recovery_required()
        assert self._protocol is not None
        if timeout_ms is None:
            return await self._read_queue.get()
        else:
            start = time.time()
            while True:
                try:
                    val = self._read_queue.get_nowait()
                    return val
                except asyncio.QueueEmpty:
                    now = time.time()
                    if (now - start) * 1000 > timeout_ms:
                        return None
                    await asyncio.sleep(0)

    async def write(self, data: bytes) -> None:
        self._recovery_required()
        assert self._protocol is not None
        await self._protocol.write(data)


class PsocMEthernetProtocol(asyncio.Protocol):
    def __init__(self, read_queue: AsyncQueue, identifier: str, loop: asyncio.AbstractEventLoop):
        super().__init__()
        self._identifier = identifier
        self._transport: Optional[asyncio.DatagramTransport] = None
        self._loop = loop
        self._read_queue = read_queue
        self._ready_event = asyncio.Event()

    async def ready(self) -> None:
        await self._ready_event.wait()

    # Overwritten from asyncio.Protocol
    def connection_made(self, transport: asyncio.BaseTransport) -> None:
        self._transport = cast(asyncio.DatagramTransport, transport)
        self._ready_event.set()

    # Overwritten from asyncio.Protocol
    def datagram_received(self, data: bytes, addr: Tuple[str, int]) -> None:
        asyncio.ensure_future(self._read_queue.put(data), loop=self._loop)

    def error_received(self, exc: Exception) -> None:
        LOG.error(f"Received Error in {self._identifier} Ethernet Connector: {exc}")

    def connection_lost(self, exc: Optional[Exception]) -> None:
        LOG.error(f"Connection Lost in {self._identifier} Ethernet Connector: {exc}")

    async def write(self, data: bytes) -> None:
        if self._transport is None:
            raise RecoverableMakaDeviceException(f"{type(self).__name__} Failure, Transport is None")
        assert asyncio.get_event_loop() == self._loop
        self._transport.sendto(data)

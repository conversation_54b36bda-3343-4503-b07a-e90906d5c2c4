#include <fmt/format.h>
#include <fmt/ostream.h>
#include <opencv2/core.hpp>
#include <opencv2/imgcodecs.hpp>
#include <opencv2/imgproc.hpp>
#include <spdlog/spdlog.h>

#include "camera.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/time.h"

namespace lib {
namespace drivers {
namespace sim_cam {

SimulatedCameraImpl::SimulatedCameraImpl(const CameraInfo &info, const CameraSettings &settings,
                                         std::shared_ptr<carbon::config::ConfigTree> camera_config)
    : CameraImpl(info, settings, camera_config), next_frame_time_(std::chrono::system_clock::now()),
      latest_timestamp_ms_(maka_control_timestamp_ms()) {
  if (info.vendor != CameraVendor::kSimulated) {
    throw camera_error(fmt::format("Received CameraInfo for vendor {}", info.vendor));
  }

  set_image();
}

int64_t SimulatedCameraImpl::set_image() {
  std::unique_lock<std::mutex> lk(mutex_);

  if (!get_settings().roi_height.has_value() || !get_settings().roi_width.has_value()) {
    throw camera_error(
        fmt::format("Simulated camera {} requires roi_height and roi_width settings", get_info().camera_id));
  }
  static_image_ = torch::zeros({3, *get_settings().roi_height, *get_settings().roi_width},
                               torch::TensorOptions().dtype(torch::kUInt8));

  width_ = (int)static_image_.size(2);
  height_ = (int)static_image_.size(1);
  pixel_format_ = PixelFormat::kRGB8;

  with_device device_guard(*get_settings().gpu_id);
  pinned_temp_image_ =
      torch::empty({3, height_, width_}, torch::TensorOptions().pinned_memory(true).dtype(torch::kUInt8));
  latest_timestamp_ms_ = maka_control_timestamp_ms();
  return latest_timestamp_ms_;
}

void SimulatedCameraImpl::start_grabbing() {}

CameraImage SimulatedCameraImpl::grab() {
  std::unique_lock<std::mutex> lk(mutex_);
  if (get_settings().sim_fps.has_value()) {
    std::this_thread::sleep_until(next_frame_time_);
  }
  if (get_settings().sim_fps.has_value()) {
    next_frame_time_ =
        std::chrono::system_clock::now() + std::chrono::milliseconds((int)(1000.0f / get_settings().sim_fps.value()));
  }

  CameraImage cam_image;
  cam_image.camera_id = get_info().camera_id;
  cam_image.timestamp_ms = latest_timestamp_ms_;
  cam_image.pixel_format = pixel_format_;
  cam_image.ppi = get_info().ppi;
  if (get_settings().gpu_id.has_value()) {
    pinned_temp_image_.copy_(static_image_);
    cam_image.image = pinned_temp_image_.to({torch::kCUDA, *get_settings().gpu_id}, true);
    if (static_depth_image_) {
      cam_image.depth = static_depth_image_->to({torch::kCUDA, *get_settings().gpu_id}, true);
    }
  } else {
    cam_image.image = static_image_.clone();
    if (static_depth_image_) {
      cam_image.depth = static_depth_image_->clone();
    }
  }

  latest_timestamp_ms_ = maka_control_timestamp_ms();
  return cam_image;
}

void SimulatedCameraImpl::stop_grabbing() {}

int64_t SimulatedCameraImpl::update_settings(const CameraSettings &settings) {
  set_settings(settings);
  return maka_control_timestamp_ms();
}

// void SimulatedCameraImpl::update_settings_from_config() {}

SimulatedCameraImpl::~SimulatedCameraImpl() {}

} // namespace sim_cam
} // namespace drivers
} // namespace lib

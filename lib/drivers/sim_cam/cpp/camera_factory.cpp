#include <fmt/format.h>
#include <fmt/ostream.h>

#include "camera.h"
#include "camera_factory.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace drivers {
namespace sim_cam {

std::vector<CameraInfo> SimulatedCameraFactory::list_devices() {
  // Simulated devices are created implicitly
  return {};
}

Camera SimulatedCameraFactory::create_device(const CameraInfo &info, const CameraSettings &settings,
                                             std::shared_ptr<carbon::config::ConfigTree> camera_config) {
  return Camera(
      [=](const CameraInfo &info_inner) {
        return std::make_unique<SimulatedCameraImpl>(info_inner, settings, camera_config);
      },
      info);
}

} // namespace sim_cam
} // namespace drivers
} // namespace lib

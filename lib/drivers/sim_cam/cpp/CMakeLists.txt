file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp)
add_library(simulated_camera SHARED ${SOURCES})

target_compile_options(simulated_camera PUBLIC ${TORCH_CXX_FLAGS})
target_compile_definitions(simulated_camera PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(simulated_camera PRIVATE ${TORCH_LIBRARIES} m spdlog fmt opencv_core opencv_imgproc opencv_imgcodecs
opencv_cudaimgproc)
target_include_directories(simulated_camera SYSTEM PUBLIC /usr/local/include/opencv4)

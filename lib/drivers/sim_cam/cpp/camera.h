#pragma once

#include <torch/torch.h>

#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/utils.h"
#include <config/tree/cpp/config_tree.hpp>

namespace lib {
namespace drivers {
namespace sim_cam {

using namespace lib::common::camera;

class SimulatedCameraImpl : public CameraImpl {
public:
  SimulatedCameraImpl(const CameraInfo &info, const CameraSettings &settings,
                      std::shared_ptr<carbon::config::ConfigTree> camera_config);
  ~SimulatedCameraImpl();
  DELETE_COPY_AND_MOVE(SimulatedCameraImpl)

  virtual void start_grabbing() override;
  virtual CameraImage grab() override;
  virtual void stop_grabbing() override;
  virtual int64_t update_settings(const CameraSettings &settings) override;
  virtual double get_temperature() override {
    spdlog::warn("get_temperature not implemented for sim cameras");
    return -1;
  }

  virtual void sync_settings() override { spdlog::warn("sync_settings not implemented on sim cams"); }
  virtual int64_t get_link_speed() override { return 125000000; };

private:
  torch::Tensor static_image_;
  std::optional<torch::Tensor> static_depth_image_;
  std::chrono::system_clock::time_point next_frame_time_;
  torch::Tensor pinned_temp_image_;
  int64_t set_image();
  int64_t latest_timestamp_ms_;
  std::mutex mutex_;
  std::condition_variable cv_;
};

} // namespace sim_cam
} // namespace drivers
} // namespace lib

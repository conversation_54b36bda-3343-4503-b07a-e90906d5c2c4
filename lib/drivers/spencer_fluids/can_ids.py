from lib.drivers.spencer_fluids.j1939 import J1939Source
from lib.drivers.spencer_fluids.messages import ecu, fort_controller, hfx


class CANId:
    HFX_TO_COMPUTER_STEERING_FEEDBACK = hfx.SteeringFeedback().get_can_id(J1939Source.HFX_TO_COMPUTER)
    HFX_TO_COMPUTER_SPEED_FEEDBACK = hfx.SpeedFeedback().get_can_id(J1939Source.HFX_TO_COMPUTER)
    HFX_TO_COMPUTER_PRESSURE_FEEDBACK = hfx.PressureFeedback().get_can_id(J1939Source.HFX_TO_COMPUTER)
    HFX_TO_COMPUTER_MISC_FEEDBACK = hfx.MiscFeedback().get_can_id(J1939Source.HFX_TO_COMPUTER)
    HFX_TO_COMPUTER_FAULTS_FEEDBACK = hfx.FaultsFeedback().get_can_id(J1939Source.HFX_TO_COMPUTER)
    HFX_TO_COMPUTER_DRIVE_FEEDBACK = hfx.DriveFeedback().get_can_id(J1939Source.HFX_TO_COMPUTER)
    HFX_TO_COMPUTER_CAN_DEVICE_STATE_FEEDBACK = hfx.CANDeviceStateFeedback().get_can_id(J1939Source.HFX_TO_COMPUTER)
    ESTOP_FEEDBACK = hfx.EStopFeedback().get_can_id(J1939Source.COMPUTER)
    FORT_HANDHELD_L = fort_controller.FortRXLeft().get_can_id(J1939Source.FORT_HANDHELD_L)
    FORT_HANDHELD_L_EXTENDED = fort_controller.FortRXLeftExtended().get_can_id(J1939Source.FORT_HANDHELD_L)
    FORT_HANDHELD_R = fort_controller.FortRXRight().get_can_id(J1939Source.FORT_HANDHELD_R)
    FORT_HANDHELD_R_EXTENDED = fort_controller.FortRXRightExtended().get_can_id(J1939Source.FORT_HANDHELD_R)
    FORT_HANDHELD_HEARTBEAT = fort_controller.FortRXHeartbeat().get_can_id(J1939Source.FORT_HANDHELD_HEARTBEAT)
    HFX_TO_FORT_TEXT_DISPLAY_MODE = fort_controller.FortTextDisplayMode().get_can_id(J1939Source.HFX_TO_FORT)
    HFX_TO_FORT_TEXT_OUTPUT = fort_controller.FortTextOutput().get_can_id(J1939Source.HFX_TO_FORT)
    ECU_FEEDBACK = ecu.ECUFeedback().get_can_id(J1939Source.ENGINE)

from lib.drivers.spencer_fluids.messages.ecu import ECUControl, ECUFeedback


def test_ecu_control_message() -> None:
    ecu_control = ECUControl(1, 2, 3, 1800, 25, 6, 14, 10, 4, 2)

    binary = ecu_control.encode()
    ecu_control_after = ECUControl.decode(binary)

    assert ecu_control.override_control_mode == ecu_control_after.override_control_mode
    assert ecu_control.requested_speed_control_conditions == ecu_control_after.requested_speed_control_conditions
    assert ecu_control.requested_speed_limit == ecu_control_after.requested_speed_limit
    assert ecu_control.requested_torque_limit == ecu_control_after.requested_torque_limit
    assert ecu_control.transmission_rate == ecu_control_after.transmission_rate
    assert ecu_control.control_purpose == ecu_control_after.control_purpose
    assert ecu_control.requested_torque_hr == ecu_control_after.requested_torque_hr
    assert ecu_control.message_counter == ecu_control_after.message_counter
    assert ecu_control.message_checksum == ecu_control_after.message_checksum


def test_ecu_feedback_message() -> None:
    ecu_feedback = ECUFeedback(1, 1.5, 53, 124, 1534, 22, 14, 10)

    binary = ecu_feedback.encode()
    ecu_feedback_after = ECUFeedback.decode(binary)

    assert ecu_feedback.engine_torque_mode == ecu_feedback_after.engine_torque_mode
    assert ecu_feedback.percent_torque_high_resolution == ecu_feedback_after.percent_torque_high_resolution
    assert ecu_feedback.drivers_demand_percent_torque == ecu_feedback_after.drivers_demand_percent_torque
    assert ecu_feedback.engine_percent_torque == ecu_feedback_after.engine_percent_torque
    assert ecu_feedback.engine_speed == ecu_feedback_after.engine_speed
    assert ecu_feedback.source_address_controlling_device == ecu_feedback_after.source_address_controlling_device
    assert ecu_feedback.engine_starter_mode == ecu_feedback_after.engine_starter_mode
    assert ecu_feedback.engine_demand_percent_torque == ecu_feedback_after.engine_demand_percent_torque

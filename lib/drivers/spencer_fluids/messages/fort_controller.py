from typing import Any, Dict, cast

import bitstruct

from lib.drivers.spencer_fluids.j1939 import J1939Message


class FortRXBasic:
    PACK_FORMAT = "u2b2b2b2u8 u2b2b2b2u8 p8 b2b2b2b2<"

    def __init__(
        self,
        x_positive_position_status: bool = False,
        x_negative_position_status: bool = False,
        x_neutral_position_status: bool = False,
        x_abs_pos: float = 0,
        y_positive_position_status: bool = False,
        y_negative_position_status: bool = False,
        y_neutral_position_status: bool = False,
        y_abs_pos: float = 0,
        button_left_status: bool = False,
        button_up_status: bool = False,
        button_right_status: bool = False,
        button_down_status: bool = False,
    ):
        self.x_abs_pos = x_abs_pos
        self.x_neutral_position_status = x_neutral_position_status
        self.x_negative_position_status = x_negative_position_status
        self.x_positive_position_status = x_positive_position_status
        self.y_abs_pos = y_abs_pos
        self.y_neutral_position_status = y_neutral_position_status
        self.y_negative_position_status = y_negative_position_status
        self.y_positive_position_status = y_positive_position_status
        self.button_left_status = button_left_status
        self.button_up_status = button_up_status
        self.button_right_status = button_right_status
        self.button_down_status = button_down_status

    @staticmethod
    def decode(data: bytearray) -> "FortRXBasic":
        vals = list(bitstruct.unpack(FortRXBasic.PACK_FORMAT, data))
        vals[4] = (vals[0] + (vals[4] << 2)) / 10
        vals[9] = (vals[5] + (vals[9] << 2)) / 10

        del vals[0]
        del vals[4]
        return FortRXBasic(*vals)

    def encode(self) -> bytearray:
        x_abs_pos_scaled = int(self.x_abs_pos * 10)
        y_abs_pos_scaled = int(self.y_abs_pos * 10)
        return cast(
            bytearray,
            bitstruct.pack(
                FortRXBasic.PACK_FORMAT,
                x_abs_pos_scaled & 0b11,
                self.x_positive_position_status,
                self.x_negative_position_status,
                self.x_neutral_position_status,
                x_abs_pos_scaled >> 2,
                y_abs_pos_scaled & 0b11,
                self.y_positive_position_status,
                self.y_negative_position_status,
                self.y_neutral_position_status,
                y_abs_pos_scaled >> 2,
                self.button_left_status,
                self.button_up_status,
                self.button_right_status,
                self.button_down_status,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "x_abs_pos": self.x_abs_pos,
            "x_neutral_position_status": self.x_neutral_position_status,
            "x_negative_position_status": self.x_negative_position_status,
            "x_positive_position_status": self.x_positive_position_status,
            "y_abs_pos": self.y_abs_pos,
            "y_neutral_position_status": self.y_neutral_position_status,
            "y_negative_position_status": self.y_negative_position_status,
            "y_positive_position_status": self.y_positive_position_status,
            "button_left_status": self.button_left_status,
            "button_up_status": self.button_up_status,
            "button_right_status": self.button_right_status,
            "button_down_status": self.button_down_status,
        }


class FortRXExtended:
    PACK_FORMAT = "u2b2b2b2u8<"

    def __init__(
        self,
        grip_positive_position_status: bool = False,
        grip_negative_position_status: bool = False,
        grip_neutral_position_status: bool = False,
        grip_abs_pos: float = 0,
    ):
        self.grip_positive_position_status = grip_positive_position_status
        self.grip_negative_position_status = grip_negative_position_status
        self.grip_neutral_position_status = grip_neutral_position_status
        self.grip_abs_pos = grip_abs_pos

    @staticmethod
    def decode(data: bytearray) -> "FortRXExtended":
        vals = list(bitstruct.unpack(FortRXExtended.PACK_FORMAT, data))
        vals[4] = (vals[0] + (vals[4] << 2)) / 10

        del vals[0]
        return FortRXExtended(*vals)

    def encode(self) -> bytearray:
        grip_abs_pos_scaled = int(self.grip_abs_pos * 10)
        return cast(
            bytearray,
            bitstruct.pack(
                FortRXExtended.PACK_FORMAT,
                grip_abs_pos_scaled & 0b11,
                self.grip_positive_position_status,
                self.grip_negative_position_status,
                self.grip_neutral_position_status,
                grip_abs_pos_scaled >> 2,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "grip_positive_position_status": self.grip_positive_position_status,
            "grip_negative_position_status": self.grip_negative_position_status,
            "grip_neutral_position_status": self.grip_neutral_position_status,
            "grip_abs_pos": self.grip_abs_pos,
        }


class FortRXLeft(J1939Message):
    PGN = 64982

    def __init__(
        self,
        x_positive_position_status: bool = False,
        x_negative_position_status: bool = False,
        x_neutral_position_status: bool = False,
        x_abs_pos: float = 0,
        y_positive_position_status: bool = False,
        y_negative_position_status: bool = False,
        y_neutral_position_status: bool = False,
        y_abs_pos: float = 0,
        button_left_status: bool = False,
        button_up_status: bool = False,
        button_right_status: bool = False,
        button_down_status: bool = False,
    ):
        super().__init__(FortRXLeft.PGN)

        self.basic_message = FortRXBasic(
            x_positive_position_status,
            x_negative_position_status,
            x_neutral_position_status,
            x_abs_pos,
            y_positive_position_status,
            y_negative_position_status,
            y_neutral_position_status,
            y_abs_pos,
            button_left_status,
            button_up_status,
            button_right_status,
            button_down_status,
        )

    @staticmethod
    def decode(data: bytearray) -> "FortRXLeft":
        result = FortRXLeft()
        result.basic_message = FortRXBasic.decode(data)
        return result

    def encode(self) -> bytearray:
        return self.basic_message.encode()

    def to_json(self) -> Dict[str, Any]:
        return self.basic_message.to_json()


class FortRXLeftExtended(J1939Message):
    PGN = 64983

    def __init__(
        self,
        grip_positive_position_status: bool = False,
        grip_negative_position_status: bool = False,
        grip_neutral_position_status: bool = False,
        grip_abs_pos: float = 0,
    ):
        super().__init__(FortRXLeftExtended.PGN)

        self.extended_message = FortRXExtended(
            grip_positive_position_status, grip_negative_position_status, grip_neutral_position_status, grip_abs_pos,
        )

    @staticmethod
    def decode(data: bytearray) -> "FortRXLeftExtended":
        result = FortRXLeftExtended()
        result.extended_message = FortRXExtended.decode(data)
        return result

    def encode(self) -> bytearray:
        return self.extended_message.encode()

    def to_json(self) -> Dict[str, Any]:
        return self.extended_message.to_json()


class FortRXRight(J1939Message):
    PGN = 64984

    def __init__(
        self,
        x_positive_position_status: bool = False,
        x_negative_position_status: bool = False,
        x_neutral_position_status: bool = False,
        x_abs_pos: float = 0,
        y_positive_position_status: bool = False,
        y_negative_position_status: bool = False,
        y_neutral_position_status: bool = False,
        y_abs_pos: float = 0,
        button_left_status: bool = False,
        button_up_status: bool = False,
        button_right_status: bool = False,
        button_down_status: bool = False,
    ):
        super().__init__(FortRXRight.PGN)

        self.basic_message = FortRXBasic(
            x_positive_position_status,
            x_negative_position_status,
            x_neutral_position_status,
            x_abs_pos,
            y_positive_position_status,
            y_negative_position_status,
            y_neutral_position_status,
            y_abs_pos,
            button_left_status,
            button_up_status,
            button_right_status,
            button_down_status,
        )

    @staticmethod
    def decode(data: bytearray) -> "FortRXRight":
        result = FortRXRight()
        result.basic_message = FortRXBasic.decode(data)
        return result

    def encode(self) -> bytearray:
        return self.basic_message.encode()

    def to_json(self) -> Dict[str, Any]:
        return self.basic_message.to_json()


class FortRXRightExtended(J1939Message):
    PGN = 64985

    def __init__(
        self,
        grip_positive_position_status: bool = False,
        grip_negative_position_status: bool = False,
        grip_neutral_position_status: bool = False,
        grip_abs_pos: float = 0,
    ):
        super().__init__(FortRXRightExtended.PGN)

        self.extended_message = FortRXExtended(
            grip_positive_position_status, grip_negative_position_status, grip_neutral_position_status, grip_abs_pos,
        )

    @staticmethod
    def decode(data: bytearray) -> "FortRXRightExtended":
        result = FortRXRightExtended()
        result.extended_message = FortRXExtended.decode(data)
        return result

    def encode(self) -> bytearray:
        return self.extended_message.encode()

    def to_json(self) -> Dict[str, Any]:
        return self.extended_message.to_json()


class FortRXHeartbeat(J1939Message):
    PGN = 65000
    PACK_FORMAT = "u8 u8 b32<"

    def __init__(
        self, vsc_mode: int = 0, autonomy_mode: int = 0, estop_state: bool = False,
    ):
        super().__init__(FortRXHeartbeat.PGN)

        self.vsc_mode = vsc_mode
        self.autonomy_mode = autonomy_mode
        self.estop_state = estop_state

    @staticmethod
    def decode(data: bytearray) -> "FortRXHeartbeat":
        size = bitstruct.calcsize(FortRXHeartbeat.PACK_FORMAT)
        vals = bitstruct.unpack(FortRXHeartbeat.PACK_FORMAT, data[:size])
        return FortRXHeartbeat(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray, bitstruct.pack(FortRXHeartbeat.PACK_FORMAT, self.vsc_mode, self.autonomy_mode, self.estop_state)
        )

    def to_json(self) -> Dict[str, Any]:
        return {"vsc_mode": self.vsc_mode, "autonomy_mode": self.autonomy_mode, "estop_state": self.estop_state}


class FortTextDisplayMode(J1939Message):
    PGN = 65001
    PACK_FORMAT = "u8 u32<"

    def __init__(
        self, key: int = 0, value: int = 0,
    ):
        super().__init__(FortTextDisplayMode.PGN)

        self.key = key
        self.value = value

    @staticmethod
    def decode(data: bytearray) -> "FortTextDisplayMode":
        vals = bitstruct.unpack(FortTextDisplayMode.PACK_FORMAT, data)
        return FortTextDisplayMode(*vals)

    def encode(self) -> bytearray:
        return cast(bytearray, bitstruct.pack(FortTextDisplayMode.PACK_FORMAT, self.key, self.value))

    def to_json(self) -> Dict[str, Any]:
        return {"key": self.key, "value": self.value}


class FortTextOutput(J1939Message):
    PGN = 65002
    PACK_FORMAT = "u8 u8 t8 t8 t8 t8 t8 t8<"

    def __init__(
        self,
        key: int = 0,
        segment: int = 0,
        char_1: str = "",
        char_2: str = "",
        char_3: str = "",
        char_4: str = "",
        char_5: str = "",
        char_6: str = "",
    ):
        super().__init__(FortTextOutput.PGN)

        self.key = key
        self.segment = segment
        self.char_1 = char_1
        self.char_2 = char_2
        self.char_3 = char_3
        self.char_4 = char_4
        self.char_5 = char_5
        self.char_6 = char_6

    @staticmethod
    def decode(data: bytearray) -> "FortTextOutput":
        vals = bitstruct.unpack(FortTextOutput.PACK_FORMAT, data)
        return FortTextOutput(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray,
            bitstruct.pack(
                FortTextOutput.PACK_FORMAT,
                self.key,
                self.segment,
                self.char_1,
                self.char_2,
                self.char_3,
                self.char_4,
                self.char_5,
                self.char_6,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "key": self.key,
            "segment": self.segment,
            "char_1": self.char_1,
            "char_2": self.char_2,
            "char_3": self.char_3,
            "char_4": self.char_4,
            "char_5": self.char_5,
            "char_6": self.char_6,
        }

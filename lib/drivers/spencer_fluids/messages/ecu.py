from typing import Any, Dict, cast

import bitstruct

from lib.drivers.spencer_fluids.j1939 import J1939Message


class ECUControl(J1939Message):
    PGN = 0
    PACK_FORMAT = "p2u2u2u2 u16 u8 u3u5 p4u4 p8 u4u4<"

    def __init__(
        self,
        override_control_mode_priority: int = 0,
        override_control_mode: int = 0,
        requested_speed_control_conditions: int = 0,
        requested_speed_limit: float = 0,
        requested_torque_limit: int = 0,
        transmission_rate: int = 0,
        control_purpose: int = 0,
        requested_torque_hr: int = 0,
        message_counter: int = 0,
        message_checksum: int = 0,
    ):
        super().__init__(ECUControl.PGN)

        self.override_control_mode = override_control_mode
        self.requested_speed_control_conditions = requested_speed_control_conditions
        self.override_control_mode_priority = override_control_mode_priority
        self.requested_speed_limit = requested_speed_limit
        self.requested_torque_limit = requested_torque_limit
        self.transmission_rate = transmission_rate
        self.control_purpose = control_purpose
        self.requested_torque_hr = requested_torque_hr
        self.message_counter = message_counter
        self.message_checksum = message_checksum

    @staticmethod
    def decode(data: bytearray) -> "ECUControl":
        vals = list(bitstruct.unpack(ECUControl.PACK_FORMAT, data))
        vals[3] = vals[3] * 0.125  # Requested speed limit
        vals[4] -= 125  # Requested torque limit
        return ECUControl(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray,
            bitstruct.pack(
                ECUControl.PACK_FORMAT,
                self.override_control_mode_priority,
                self.override_control_mode,
                self.requested_speed_control_conditions,
                int(self.requested_speed_limit / 0.125),
                self.requested_torque_limit + 125,
                self.transmission_rate,
                self.control_purpose,
                self.requested_torque_hr,
                self.message_counter,
                self.message_checksum,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "override_control_mode": self.override_control_mode,
            "requested_speed_control_conditions": self.requested_speed_control_conditions,
            "override_control_mode_priority": self.override_control_mode_priority,
            "requested_speed_limit": self.requested_speed_limit,
            "requested_torque_limit": self.requested_torque_limit,
            "transmission_rate": self.transmission_rate,
            "control_purpose": self.control_purpose,
            "requested_torque_hr": self.requested_torque_hr,
            "message_counter": self.message_counter,
            "message_checksum": self.message_checksum,
        }


class ECUFeedback(J1939Message):
    PGN = 61444
    PACK_FORMAT = "u4u4 u8 u8 u16 u8 p4u4 u8<"

    def __init__(
        self,
        engine_torque_mode: int = 0,
        percent_torque_high_resolution: float = 0,
        drivers_demand_percent_torque: int = 0,
        engine_percent_torque: int = 0,
        engine_speed: float = 0,
        source_address_controlling_device: int = 0,
        engine_starter_mode: int = 0,
        engine_demand_percent_torque: int = 0,
    ):
        super().__init__(ECUFeedback.PGN)

        self.engine_torque_mode = engine_torque_mode
        self.percent_torque_high_resolution = percent_torque_high_resolution
        self.drivers_demand_percent_torque = drivers_demand_percent_torque
        self.engine_percent_torque = engine_percent_torque
        self.engine_speed = engine_speed
        self.source_address_controlling_device = source_address_controlling_device
        self.engine_starter_mode = engine_starter_mode
        self.engine_demand_percent_torque = engine_demand_percent_torque

    @staticmethod
    def decode(data: bytearray) -> "ECUFeedback":
        vals = list(bitstruct.unpack(ECUFeedback.PACK_FORMAT, data))
        vals[1] = vals[1] * 0.125  # percent_torque_high_resolution
        vals[2] = vals[2] - 125  # drivers_demand_percent_torque
        vals[3] = vals[3] - 125  # engine_percent_torque
        vals[4] = vals[4] * 0.125  # engine_speed
        vals[7] = vals[7] - 125  # engine_demand_percent_torque
        return ECUFeedback(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray,
            bitstruct.pack(
                ECUFeedback.PACK_FORMAT,
                self.engine_torque_mode,
                int(self.percent_torque_high_resolution / 0.125),
                self.drivers_demand_percent_torque + 125,
                self.engine_percent_torque + 125,
                int(self.engine_speed / 0.125),
                self.source_address_controlling_device,
                self.engine_starter_mode,
                self.engine_demand_percent_torque + 125,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "engine_torque_mode": self.engine_torque_mode,
            "percent_torque_high_resolution": self.percent_torque_high_resolution,
            "drivers_demand_percent_torque": self.drivers_demand_percent_torque,
            "engine_percent_torque": self.engine_percent_torque,
            "engine_speed": self.engine_speed,
            "source_address_controlling_device": self.source_address_controlling_device,
            "engine_starter_mode": self.engine_starter_mode,
            "engine_demand_percent_torque": self.engine_demand_percent_torque,
        }

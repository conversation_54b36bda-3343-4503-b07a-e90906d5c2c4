from lib.drivers.spencer_fluids.messages.fort_controller import (
    FortRXBasic,
    FortRXExtended,
    FortRXHeartbeat,
    FortTextDisplayMode,
    FortTextOutput,
)


def test_fort_basic_message() -> None:
    fort_message = FortRXBasic(True, False, True, 90, False, True, True, 80, True, True, False, False)

    binary = fort_message.encode()
    fort_message_after = FortRXBasic.decode(binary)

    assert fort_message.x_positive_position_status == fort_message_after.x_positive_position_status
    assert fort_message.x_negative_position_status == fort_message_after.x_negative_position_status
    assert fort_message.x_neutral_position_status == fort_message_after.x_neutral_position_status
    assert fort_message.x_abs_pos == fort_message_after.x_abs_pos
    assert fort_message.y_positive_position_status == fort_message_after.y_positive_position_status
    assert fort_message.y_negative_position_status == fort_message_after.y_negative_position_status
    assert fort_message.y_neutral_position_status == fort_message_after.y_neutral_position_status
    assert fort_message.y_abs_pos == fort_message_after.y_abs_pos
    assert fort_message.button_left_status == fort_message_after.button_left_status
    assert fort_message.button_up_status == fort_message_after.button_up_status
    assert fort_message.button_right_status == fort_message_after.button_right_status
    assert fort_message.button_down_status == fort_message_after.button_down_status


def test_fort_extended_message() -> None:
    fort_message = FortRXExtended(True, False, True, 75)

    binary = fort_message.encode()
    fort_message_after = FortRXExtended.decode(binary)

    assert fort_message.grip_positive_position_status == fort_message_after.grip_positive_position_status
    assert fort_message.grip_negative_position_status == fort_message_after.grip_negative_position_status
    assert fort_message.grip_neutral_position_status == fort_message_after.grip_neutral_position_status
    assert fort_message.grip_abs_pos == fort_message_after.grip_abs_pos


def test_fort_heartbeat_message() -> None:
    fort_message = FortRXHeartbeat(2, 1, True)

    binary = fort_message.encode()
    fort_message_after = FortRXHeartbeat.decode(binary)

    assert fort_message.vsc_mode == fort_message_after.vsc_mode
    assert fort_message.autonomy_mode == fort_message_after.autonomy_mode
    assert fort_message.estop_state == fort_message_after.estop_state


def test_fort_display_mode_message() -> None:
    fort_message = FortTextDisplayMode(122, 4)

    binary = fort_message.encode()
    fort_message_after = FortTextDisplayMode.decode(binary)

    assert fort_message.key == fort_message_after.key
    assert fort_message.value == fort_message_after.value


def test_fort_text_output_message() -> None:
    fort_message = FortTextOutput(97, 1, "C", "A", "R", "B", "O", "N")

    binary = fort_message.encode()
    fort_message_after = FortTextOutput.decode(binary)

    assert fort_message.key == fort_message_after.key
    assert fort_message.segment == fort_message_after.segment
    assert fort_message.char_1 == fort_message_after.char_1
    assert fort_message.char_2 == fort_message_after.char_2
    assert fort_message.char_3 == fort_message_after.char_3
    assert fort_message.char_4 == fort_message_after.char_4
    assert fort_message.char_5 == fort_message_after.char_5

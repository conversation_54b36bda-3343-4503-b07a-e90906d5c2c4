from lib.drivers.spencer_fluids.messages.hfx import (
    DriveControl,
    DriveFeedback,
    EngineTrimControl,
    FaultsFeedback,
    MiscFeedback,
    PressureFeedback,
    SpeedFeedback,
    SteeringControl,
    SteeringFeedback,
)


def test_steering_feedback_message() -> None:
    hfx_message = SteeringFeedback(-122.8, 122.8, -145.3, 162.4)

    binary = hfx_message.encode()
    hfx_message_after = SteeringFeedback.decode(binary)

    assert hfx_message.actual_helac_fr == hfx_message_after.actual_helac_fr
    assert hfx_message.actual_helac_fl == hfx_message_after.actual_helac_fl
    assert hfx_message.desired_helac_fr == hfx_message_after.desired_helac_fr
    assert hfx_message.desired_helac_fl == hfx_message_after.desired_helac_fl


def test_speed_feedback_message() -> None:
    hfx_message = SpeedFeedback(122, 78, 145, 162)

    binary = hfx_message.encode()
    hfx_message_after = SpeedFeedback.decode(binary)

    assert hfx_message.fr_speed_rph == hfx_message_after.fr_speed_rph
    assert hfx_message.fl_speed_rph == hfx_message_after.fl_speed_rph
    assert hfx_message.rl_speed_rph == hfx_message_after.rl_speed_rph
    assert hfx_message.rr_speed_rph == hfx_message_after.rr_speed_rph


def test_pressure_feedback_message() -> None:
    hfx_message = PressureFeedback(122, 78, 145, 162)

    binary = hfx_message.encode()
    hfx_message_after = PressureFeedback.decode(binary)

    assert hfx_message.drive_pressure_a == hfx_message_after.drive_pressure_a
    assert hfx_message.drive_pressure_b == hfx_message_after.drive_pressure_b
    assert hfx_message.open_loop_pump_pressure == hfx_message_after.open_loop_pump_pressure
    assert hfx_message.generator_pressure == hfx_message_after.generator_pressure


def test_misc_feedback_message() -> None:
    hfx_message = MiscFeedback(122, True, True, False, False, True, False, False, True, True, True, False, True, True)

    binary = hfx_message.encode()
    hfx_message_after = MiscFeedback.decode(binary)

    assert hfx_message.pump_flow == hfx_message_after.pump_flow
    assert hfx_message.pc_control == hfx_message_after.pc_control
    assert hfx_message.handheld_control == hfx_message_after.handheld_control
    assert hfx_message.kill_engine == hfx_message_after.kill_engine
    assert hfx_message.high_idle_switch == hfx_message_after.high_idle_switch
    assert hfx_message.park_brake == hfx_message_after.park_brake
    assert hfx_message.traction == hfx_message_after.traction
    assert hfx_message.charge_filter == hfx_message_after.charge_filter
    assert hfx_message.return_filter == hfx_message_after.return_filter
    assert hfx_message.pressure_filter == hfx_message_after.pressure_filter
    assert hfx_message.rr_heco == hfx_message_after.rr_heco
    assert hfx_message.rl_heco == hfx_message_after.rl_heco
    assert hfx_message.fl_heco == hfx_message_after.fl_heco
    assert hfx_message.fr_heco == hfx_message_after.fr_heco


def test_faults_feedback_message() -> None:
    hfx_message = FaultsFeedback(True)

    binary = hfx_message.encode()
    hfx_message_after = FaultsFeedback.decode(binary)

    assert hfx_message.active_faults == hfx_message_after.active_faults


def test_drive_feedback_message() -> None:
    hfx_message = DriveFeedback(670, 910)

    binary = hfx_message.encode()
    hfx_message_after = DriveFeedback.decode(binary)

    assert hfx_message.drive_fwd_current == hfx_message_after.drive_fwd_current
    assert hfx_message.drive_rev_current == hfx_message_after.drive_rev_current


def test_steering_control_message() -> None:
    hfx_message = SteeringControl(-23.2, -24.5)

    binary = hfx_message.encode()
    hfx_message_after = SteeringControl.decode(binary)

    assert hfx_message.front_left_helac_position == hfx_message_after.front_left_helac_position
    assert hfx_message.front_right_helac_position == hfx_message_after.front_right_helac_position


def test_drive_control_message() -> None:
    hfx_message = DriveControl(-670)

    binary = hfx_message.encode()
    hfx_message_after = DriveControl.decode(binary)

    assert hfx_message.ground_speed == hfx_message_after.ground_speed


def test_engine_trim_control_message() -> None:
    hfx_message = EngineTrimControl(-15, False)

    binary = hfx_message.encode()
    hfx_message_after = EngineTrimControl.decode(binary)

    assert hfx_message.engine_trim == hfx_message_after.engine_trim
    assert hfx_message.kill_engine == hfx_message_after.kill_engine

from typing import Any, Dict, cast

import bitstruct

from lib.drivers.spencer_fluids.j1939 import J1939Message


class SteeringFeedback(J1939Message):
    PGN = 65504
    PACK_FORMAT = "s16 s16 s16 s16<"

    def __init__(
        self,
        actual_helac_fr: float = 0,
        actual_helac_fl: float = 0,
        desired_helac_fr: float = 0,
        desired_helac_fl: float = 0,
    ):
        super().__init__(SteeringFeedback.PGN)

        self.actual_helac_fr = actual_helac_fr
        self.actual_helac_fl = actual_helac_fl
        self.desired_helac_fr = desired_helac_fr
        self.desired_helac_fl = desired_helac_fl

    @staticmethod
    def decode(data: bytearray) -> "SteeringFeedback":
        vals = bitstruct.unpack(SteeringFeedback.PACK_FORMAT, data)
        vals = [x / 10 for x in vals]
        return SteeringFeedback(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray,
            bitstruct.pack(
                SteeringFeedback.PACK_FORMAT,
                self.actual_helac_fr * 10,
                self.actual_helac_fl * 10,
                self.desired_helac_fr * 10,
                self.desired_helac_fl * 10,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "actual_helac_fr": self.actual_helac_fr,
            "actual_helac_fl": self.actual_helac_fl,
            "desired_helac_fr": self.desired_helac_fr,
            "desired_helac_fl": self.desired_helac_fl,
        }


class SpeedFeedback(J1939Message):
    PGN = 65506
    PACK_FORMAT = "u16 u16 u16 u16<"

    def __init__(self, fr_speed_rph: int = 0, fl_speed_rph: int = 0, rl_speed_rph: int = 0, rr_speed_rph: int = 0):
        super().__init__(SpeedFeedback.PGN)

        self.fr_speed_rph = fr_speed_rph
        self.fl_speed_rph = fl_speed_rph
        self.rl_speed_rph = rl_speed_rph
        self.rr_speed_rph = rr_speed_rph

    @staticmethod
    def decode(data: bytearray) -> "SpeedFeedback":
        vals = bitstruct.unpack(SpeedFeedback.PACK_FORMAT, data)
        return SpeedFeedback(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray,
            bitstruct.pack(
                SpeedFeedback.PACK_FORMAT, self.fr_speed_rph, self.fl_speed_rph, self.rl_speed_rph, self.rr_speed_rph,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "fr_speed_rph": self.fr_speed_rph,
            "fl_speed_rph": self.fl_speed_rph,
            "rl_speed_rph": self.rl_speed_rph,
            "rr_speed_rph": self.rr_speed_rph,
        }


class PressureFeedback(J1939Message):
    PGN = 65509
    PACK_FORMAT = "u16 u16 u16 u16<"

    def __init__(
        self,
        drive_pressure_a: int = 0,
        drive_pressure_b: int = 0,
        open_loop_pump_pressure: int = 0,
        generator_pressure: int = 0,
    ):
        super().__init__(PressureFeedback.PGN)

        self.drive_pressure_a = drive_pressure_a
        self.drive_pressure_b = drive_pressure_b
        self.open_loop_pump_pressure = open_loop_pump_pressure
        self.generator_pressure = generator_pressure

    @staticmethod
    def decode(data: bytearray) -> "PressureFeedback":
        vals = bitstruct.unpack(PressureFeedback.PACK_FORMAT, data)
        return PressureFeedback(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray,
            bitstruct.pack(
                PressureFeedback.PACK_FORMAT,
                self.drive_pressure_a,
                self.drive_pressure_b,
                self.open_loop_pump_pressure,
                self.generator_pressure,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "drive_pressure_a": self.drive_pressure_a,
            "drive_pressure_b": self.drive_pressure_b,
            "open_loop_pump_pressure": self.open_loop_pump_pressure,
            "generator_pressure": self.generator_pressure,
        }


class MiscFeedback(J1939Message):
    PGN = 65510
    PACK_FORMAT = "u16 p6b1b1 p4b1b1b1b1 p5b1b1b1 p4b1b1b1b1<"

    def __init__(
        self,
        pump_flow: int = 0,
        pc_control: bool = False,
        handheld_control: bool = False,
        kill_engine: bool = False,
        high_idle_switch: bool = False,
        park_brake: bool = False,
        traction: bool = False,
        charge_filter: bool = False,
        return_filter: bool = False,
        pressure_filter: bool = False,
        rr_heco: bool = False,
        rl_heco: bool = False,
        fl_heco: bool = False,
        fr_heco: bool = False,
    ):
        super().__init__(MiscFeedback.PGN)

        self.pump_flow = pump_flow
        self.handheld_control = handheld_control
        self.pc_control = pc_control
        self.traction = traction
        self.park_brake = park_brake
        self.high_idle_switch = high_idle_switch
        self.kill_engine = kill_engine
        self.pressure_filter = pressure_filter
        self.return_filter = return_filter
        self.charge_filter = charge_filter
        self.fr_heco = fr_heco
        self.fl_heco = fl_heco
        self.rl_heco = rl_heco
        self.rr_heco = rr_heco

    @staticmethod
    def decode(data: bytearray) -> "MiscFeedback":
        vals = bitstruct.unpack(MiscFeedback.PACK_FORMAT, data)
        return MiscFeedback(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray,
            bitstruct.pack(
                MiscFeedback.PACK_FORMAT,
                self.pump_flow,
                self.pc_control,
                self.handheld_control,
                self.kill_engine,
                self.high_idle_switch,
                self.park_brake,
                self.traction,
                self.charge_filter,
                self.return_filter,
                self.pressure_filter,
                self.rr_heco,
                self.rl_heco,
                self.fl_heco,
                self.fr_heco,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "pump_flow": self.pump_flow,
            "handheld_control": self.handheld_control,
            "pc_control": self.pc_control,
            "traction": self.traction,
            "park_brake": self.park_brake,
            "high_idle_switch": self.high_idle_switch,
            "kill_engine": self.kill_engine,
            "pressure_filter": self.pressure_filter,
            "return_filter": self.return_filter,
            "charge_filter": self.charge_filter,
            "fr_heco": self.fr_heco,
            "fl_heco": self.fl_heco,
            "rl_heco": self.rl_heco,
            "rr_heco": self.rr_heco,
        }


class FaultsFeedback(J1939Message):
    PGN = 65520
    PACK_FORMAT = "b32<"

    def __init__(self, active_faults: bool = False):
        super().__init__(FaultsFeedback.PGN)

        self.active_faults = active_faults

    @staticmethod
    def decode(data: bytearray) -> "FaultsFeedback":
        vals = bitstruct.unpack(FaultsFeedback.PACK_FORMAT, data)
        return FaultsFeedback(*vals)

    def encode(self) -> bytearray:
        return cast(bytearray, bitstruct.pack(FaultsFeedback.PACK_FORMAT, self.active_faults))

    def to_json(self) -> Dict[str, Any]:
        return {"active_faults": self.active_faults}


class DriveFeedback(J1939Message):
    PGN = 65519
    PACK_FORMAT = "u16 u16 p32<"

    def __init__(self, drive_fwd_current: int = 0, drive_rev_current: int = 0):
        super().__init__(DriveFeedback.PGN)

        self.drive_fwd_current = drive_fwd_current
        self.drive_rev_current = drive_rev_current

    @staticmethod
    def decode(data: bytearray) -> "DriveFeedback":
        vals = bitstruct.unpack(DriveFeedback.PACK_FORMAT, data)
        return DriveFeedback(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray, bitstruct.pack(DriveFeedback.PACK_FORMAT, self.drive_fwd_current, self.drive_rev_current)
        )

    def to_json(self) -> Dict[str, Any]:
        return {"drive_fwd_current": self.drive_fwd_current, "drive_rev_current": self.drive_rev_current}


class CANDeviceStateFeedback(J1939Message):
    PGN = 65518
    PACK_FORMAT = "p2b1b1b1b1b1b1 p56<"

    def __init__(
        self,
        usb_can_state: bool = False,
        handheld_r_state: bool = False,
        handheld_l_state: bool = False,
        joral_fl_state: bool = False,
        joral_fr_state: bool = False,
        ecu_state: bool = False,
    ):
        super().__init__(CANDeviceStateFeedback.PGN)

        self.ecu_state = ecu_state
        self.joral_fr_state = joral_fr_state
        self.joral_fl_state = joral_fl_state
        self.handheld_l_state = handheld_l_state
        self.handheld_r_state = handheld_r_state
        self.usb_can_state = usb_can_state

    @staticmethod
    def decode(data: bytearray) -> "CANDeviceStateFeedback":
        vals = bitstruct.unpack(CANDeviceStateFeedback.PACK_FORMAT, data)
        return CANDeviceStateFeedback(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray,
            bitstruct.pack(
                CANDeviceStateFeedback.PACK_FORMAT,
                self.ecu_state,
                self.joral_fr_state,
                self.joral_fl_state,
                self.handheld_l_state,
                self.handheld_r_state,
                self.usb_can_state,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "ecu_state": self.ecu_state,
            "joral_fr_state": self.joral_fr_state,
            "joral_fl_state": self.joral_fl_state,
            "handheld_l_state": self.handheld_l_state,
            "handheld_r_state": self.handheld_r_state,
            "usb_can_state": self.usb_can_state,
        }


class SteeringControl(J1939Message):
    PGN = 65530
    PACK_FORMAT = "s16 s16 p32<"

    def __init__(self, front_right_helac_position: float = 0, front_left_helac_position: float = 0):
        super().__init__(SteeringControl.PGN)

        self.front_left_helac_position = front_left_helac_position
        self.front_right_helac_position = front_right_helac_position

    @staticmethod
    def decode(data: bytearray) -> "SteeringControl":
        vals = bitstruct.unpack(SteeringControl.PACK_FORMAT, data)
        vals = [x / 10 for x in vals]
        return SteeringControl(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray,
            bitstruct.pack(
                SteeringControl.PACK_FORMAT, self.front_right_helac_position * 10, self.front_left_helac_position * 10,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "front_left_helac_position": self.front_left_helac_position,
            "front_right_helac_position": self.front_right_helac_position,
        }


class DriveControl(J1939Message):
    PGN = 65531
    PACK_FORMAT = "s16 p48<"

    def __init__(self, ground_speed: int = 0):
        super().__init__(DriveControl.PGN)

        self.ground_speed = ground_speed

    @staticmethod
    def decode(data: bytearray) -> "DriveControl":
        vals = bitstruct.unpack(DriveControl.PACK_FORMAT, data)
        return DriveControl(*vals)

    def encode(self) -> bytearray:
        return cast(bytearray, bitstruct.pack(DriveControl.PACK_FORMAT, self.ground_speed))

    def to_json(self) -> Dict[str, Any]:
        return {"ground_speed": self.ground_speed}


class EngineTrimControl(J1939Message):
    PGN = 65532
    PACK_FORMAT = "s16 b1 p47<"

    def __init__(self, engine_trim: int = 0, kill_engine: bool = False):
        super().__init__(EngineTrimControl.PGN)

        self.engine_trim = engine_trim
        self.kill_engine = kill_engine

    @staticmethod
    def decode(data: bytearray) -> "EngineTrimControl":
        vals = bitstruct.unpack(EngineTrimControl.PACK_FORMAT, data)
        return EngineTrimControl(*vals)

    def encode(self) -> bytearray:
        return cast(bytearray, bitstruct.pack(EngineTrimControl.PACK_FORMAT, self.engine_trim, self.kill_engine))

    def to_json(self) -> Dict[str, Any]:
        return {"engine_trim": self.engine_trim, "kill_engine": self.kill_engine}


class SteeringPIDControlFirst(J1939Message):
    PGN = 65529
    PACK_FORMAT = "f32 f32<"

    def __init__(self, kp: float = 0, ki: float = 0):
        super().__init__(SteeringPIDControlFirst.PGN)

        self.kp = kp
        self.ki = ki

    @staticmethod
    def decode(data: bytearray) -> "SteeringPIDControlFirst":
        vals = bitstruct.unpack(SteeringPIDControlFirst.PACK_FORMAT, data)
        return SteeringPIDControlFirst(*vals)

    def encode(self) -> bytearray:
        return cast(bytearray, bitstruct.pack(SteeringPIDControlFirst.PACK_FORMAT, self.kp, self.ki))

    def to_json(self) -> Dict[str, Any]:
        return {"kp": self.kp, "ki": self.ki}


class SteeringPIDControlSecond(J1939Message):
    PGN = 65528
    PACK_FORMAT = "p7b1 f32 p24<"

    def __init__(self, enable_steering_pid_override: bool = False, kd: float = 0):
        super().__init__(SteeringPIDControlSecond.PGN)

        self.enable_steering_pid_override = enable_steering_pid_override
        self.kd = kd

    @staticmethod
    def decode(data: bytearray) -> "SteeringPIDControlSecond":
        vals = bitstruct.unpack(SteeringPIDControlSecond.PACK_FORMAT, data)
        return SteeringPIDControlSecond(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray, bitstruct.pack(SteeringPIDControlSecond.PACK_FORMAT, self.enable_steering_pid_override, self.kd)
        )

    def to_json(self) -> Dict[str, Any]:
        return {"enable_steering_pid_override": self.enable_steering_pid_override, "kd": self.kd}


class SteeringMiscControl(J1939Message):
    PGN = 65527
    PACK_FORMAT = "p7b1 f32 p24<"

    def __init__(self, enable_steering_misc_override: bool = False, deadband_angle: float = 0):
        super().__init__(SteeringMiscControl.PGN)

        self.enable_steering_misc_override = enable_steering_misc_override
        self.deadband_angle = deadband_angle

    @staticmethod
    def decode(data: bytearray) -> "SteeringMiscControl":
        vals = bitstruct.unpack(SteeringMiscControl.PACK_FORMAT, data)
        return SteeringMiscControl(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray,
            bitstruct.pack(SteeringMiscControl.PACK_FORMAT, self.enable_steering_misc_override, self.deadband_angle),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "enable_steering_misc_override": self.enable_steering_misc_override,
            "deadband_angle": self.deadband_angle,
        }


class EStopFeedback(J1939Message):
    PGN = 65526
    PACK_FORMAT = "p1b1b1b1b1b1b1b1 p48<"

    def __init__(
        self,
        e_stop_message_enabled: bool = False,
        scanner_e_stop: bool = False,
        left_e_stop: bool = False,
        right_e_stop: bool = False,
        fort_e_stop: bool = False,
        drive_e_stop: bool = False,
        laser_e_stop: bool = False,
    ):
        super().__init__(EStopFeedback.PGN)

        self.laser_e_stop = laser_e_stop
        self.drive_e_stop = drive_e_stop
        self.fort_e_stop = fort_e_stop
        self.right_e_stop = right_e_stop
        self.left_e_stop = left_e_stop
        self.scanner_e_stop = scanner_e_stop
        self.e_stop_message_enabled = e_stop_message_enabled

    @staticmethod
    def decode(data: bytearray) -> "EStopFeedback":
        vals = bitstruct.unpack(EStopFeedback.PACK_FORMAT, data)
        return EStopFeedback(*vals)

    def encode(self) -> bytearray:
        return cast(
            bytearray,
            bitstruct.pack(
                EStopFeedback.PACK_FORMAT,
                self.e_stop_message_enabled,
                self.scanner_e_stop,
                self.left_e_stop,
                self.right_e_stop,
                self.fort_e_stop,
                self.drive_e_stop,
                self.laser_e_stop,
            ),
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "laser_e_stop": self.laser_e_stop,
            "drive_e_stop": self.drive_e_stop,
            "fort_e_stop": self.fort_e_stop,
            "right_e_stop": self.right_e_stop,
            "left_e_stop": self.left_e_stop,
            "scanner_e_stop": self.scanner_e_stop,
            "e_stop_message_enabled": self.e_stop_message_enabled,
        }

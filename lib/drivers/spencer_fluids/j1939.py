import abc
from typing import Any, Dict

from lib.common.serialization.json import JsonSerializable


class J1939Source:
    HFX_TO_COMPUTER = 3
    HFX_TO_ECU = 7
    HFX_TO_FORT = 2
    JORAL_FR = 210
    JORAL_FL = 211
    ENGINE = 0
    FORT_HANDHELD_L = 51
    FORT_HANDHELD_R = 52
    FORT_HANDHELD_HEARTBEAT = 1
    COMPUTER = 17


class J1939Message(JsonSerializable):
    # Setup CAN ID mask for J1939 so that we consider both source address
    # and pgn when matching message ids and disregard priority.

    # 8 bit source address mask
    HFX_SOURCE_ADDRESS_MASK = 0b11111111

    # 18 bit source address mask
    HFX_PGN_MASK = 0b111111111111111111

    # 3 bit source address mask
    HFX_PRIORITY_MASK = 0b000

    HFX_CAN_ID_MASK = HFX_PRIORITY_MASK << 26 | HFX_PGN_MASK << 8 | HFX_SOURCE_ADDRESS_MASK

    def __init__(self, pgn: int):
        super().__init__()
        self._pgn = pgn

    def get_can_id(self, source_id: int) -> int:
        can_id = self._pgn << 8 | source_id
        can_id = can_id & J1939Message.HFX_CAN_ID_MASK
        return can_id

    @staticmethod
    @abc.abstractclassmethod
    def decode(data: bytearray) -> "J1939Message":
        ...

    @abc.abstractclassmethod
    def encode(self) -> bytearray:
        ...

    @abc.abstractmethod
    def to_json(self) -> Dict[str, Any]:
        ...

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "J1939Message":
        return cls(**data)

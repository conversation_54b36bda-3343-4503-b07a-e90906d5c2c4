import asyncio
import re
from typing import Callable, Dict, List, Optional, Tuple, Type, cast

import can

from lib.common.logging import get_logger, init_log
from lib.common.protocol.channel.base import Topic
from lib.common.protocol.feed.base import Feed
from lib.common.protocol.feed.inproc import Inproc<PERSON><PERSON>
from lib.common.tasks import MakaTask, maybe_get_current
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time import maka_control_timestamp_ms
from lib.drivers.spencer_fluids.can_ids import CANId
from lib.drivers.spencer_fluids.j1939 import J1939Message, J1939Source
from lib.drivers.spencer_fluids.messages import ecu, hfx
from lib.drivers.spencer_fluids.messages.fort_controller import (
    FortRXHeartbeat,
    FortRXLeft,
    FortRXLeftExtended,
    FortRXRight,
    FortRXRightExtended,
    FortTextDisplayMode,
    FortTextOutput,
)

LOG = get_logger(__name__)


class SetPoints:
    def __init__(
        self,
        front_left_helac_position: float = 0,
        front_right_helac_position: float = 0,
        ground_speed: int = 0,
        engine_speed_trim: int = 0,
        engine_kill: bool = False,
        steering_pid_p: float = 0.0125,
        steering_pid_i: float = 0.0,
        steering_pid_d: float = 0.0,
        steering_deadband_angle: float = 1,
    ):
        self._front_left_helac_position = front_left_helac_position
        self._front_right_helac_position = front_right_helac_position
        self._ground_speed = ground_speed
        self._engine_speed_trim = engine_speed_trim
        self._engine_kill = engine_kill
        self._steering_pid_p = steering_pid_p
        self._steering_pid_i = steering_pid_i
        self._steering_pid_d = steering_pid_d
        self._steering_deadband_angle = steering_deadband_angle

    @property
    def front_left_helac_position(self) -> float:
        return self._front_left_helac_position

    def set_front_left_helac_position(self, val: float) -> None:
        self._front_left_helac_position = val

    @property
    def front_right_helac_position(self) -> float:
        return self._front_right_helac_position

    def set_front_right_helac_position(self, val: float) -> None:
        self._front_right_helac_position = val

    @property
    def ground_speed(self) -> int:
        return self._ground_speed

    def set_ground_speed(self, val: int) -> None:
        self._ground_speed = val

    @property
    def engine_speed_trim(self) -> int:
        return self._engine_speed_trim

    def set_engine_speed_trim(self, val: int) -> None:
        self._engine_speed_trim = val

    @property
    def engine_kill(self) -> bool:
        return self._engine_kill

    def set_engine_kill(self, val: bool) -> None:
        self._engine_kill = val

    @property
    def steering_pid_p(self) -> float:
        return self._steering_pid_p

    def set_steering_pid_p(self, val: float) -> None:
        self._steering_pid_p = val

    @property
    def steering_pid_i(self) -> float:
        return self._steering_pid_i

    def set_steering_pid_i(self, val: float) -> None:
        self._steering_pid_i = val

    @property
    def steering_pid_d(self) -> float:
        return self._steering_pid_d

    def set_steering_pid_d(self, val: float) -> None:
        self._steering_pid_d = val

    @property
    def steering_deadband_angle(self) -> float:
        return self._steering_deadband_angle

    def set_steering_deadband_angle(self, val: float) -> None:
        self._steering_deadband_angle = val


class HfxInterface:
    """
    Interface to a Spencer Fluids supplied Eaton HFX Controller.
    This class implements the Maka to HFX messages for commanding the HFX for steering, velocity,
    and engine control; and the HFX to Maka messages for receiving feedback from the controller on
    what it's doing and the measurements its recording.
    """

    def __init__(self, can_bus: can.interface.BusABC, topic: Topic, feed: Feed):
        self._canbus = can_bus
        self._can_send_period_ms = 50
        self._periodic_commands: Dict[str, Tuple[can.Message, can.broadcastmanager.CyclicSendTaskABC]] = {}
        self._errors = 0
        self._running = False

        self._setpoints = SetPoints()
        self._packet_id_to_parser: Dict[int, Type[J1939Message]] = {
            CANId.HFX_TO_COMPUTER_STEERING_FEEDBACK: hfx.SteeringFeedback,
            CANId.HFX_TO_COMPUTER_SPEED_FEEDBACK: hfx.SpeedFeedback,
            CANId.HFX_TO_COMPUTER_PRESSURE_FEEDBACK: hfx.PressureFeedback,
            CANId.HFX_TO_COMPUTER_MISC_FEEDBACK: hfx.MiscFeedback,
            CANId.HFX_TO_COMPUTER_FAULTS_FEEDBACK: hfx.FaultsFeedback,
            CANId.HFX_TO_COMPUTER_DRIVE_FEEDBACK: hfx.DriveFeedback,
            CANId.HFX_TO_COMPUTER_CAN_DEVICE_STATE_FEEDBACK: hfx.CANDeviceStateFeedback,
            CANId.ESTOP_FEEDBACK: hfx.EStopFeedback,
            CANId.FORT_HANDHELD_L: FortRXLeft,
            CANId.FORT_HANDHELD_L_EXTENDED: FortRXLeftExtended,
            CANId.FORT_HANDHELD_R: FortRXRight,
            CANId.FORT_HANDHELD_R_EXTENDED: FortRXRightExtended,
            CANId.FORT_HANDHELD_HEARTBEAT: FortRXHeartbeat,
            CANId.HFX_TO_FORT_TEXT_DISPLAY_MODE: FortTextDisplayMode,
            CANId.HFX_TO_FORT_TEXT_OUTPUT: FortTextOutput,
            CANId.ECU_FEEDBACK: ecu.ECUFeedback,
        }
        self._canbus.set_filters(
            [
                {"can_id": can_id, "can_mask": J1939Message.HFX_CAN_ID_MASK}
                for can_id in self._packet_id_to_parser.keys()
            ]
        )
        self._latest_msg: Dict[int, Optional[J1939Message]] = {k: None for k in self._packet_id_to_parser.keys()}

        self._fort_button_presses: List[Tuple[int, str]] = []

        def _get_latest_msg_callback(can_id: int) -> Callable[[], Optional[J1939Message]]:
            return lambda: self._latest_msg[can_id]

        pattern = re.compile(r"(?<!^)(?=[A-Z])")
        for k in self._latest_msg.keys():
            camel_case_subtopic = self._packet_id_to_parser[k].__name__
            snake_case_subtopic = topic.sub(pattern.sub("_", camel_case_subtopic).lower())
            feed.publish_callback(topic=snake_case_subtopic, callback=_get_latest_msg_callback(k))

    @property
    def pc_control(self) -> bool:
        latest: Optional[hfx.MiscFeedback] = cast(
            Optional[hfx.MiscFeedback], self._latest_msg.get(CANId.HFX_TO_COMPUTER_MISC_FEEDBACK)
        )
        if latest is None:
            return False

        return latest.pc_control

    @property
    def actual_left_wheel_angle_deg(self) -> float:
        latest: Optional[hfx.SteeringFeedback] = cast(
            Optional[hfx.SteeringFeedback], self._latest_msg.get(CANId.HFX_TO_COMPUTER_STEERING_FEEDBACK)
        )
        if latest is None:
            return 0.0

        return latest.actual_helac_fl

    @property
    def actual_right_wheel_angle_deg(self) -> float:
        latest: Optional[hfx.SteeringFeedback] = cast(
            Optional[hfx.SteeringFeedback], self._latest_msg.get(CANId.HFX_TO_COMPUTER_STEERING_FEEDBACK)
        )
        if latest is None:
            return 0.0

        return latest.actual_helac_fr

    @property
    def target_left_wheel_angle_deg(self) -> float:
        latest: Optional[hfx.SteeringFeedback] = cast(
            Optional[hfx.SteeringFeedback], self._latest_msg.get(CANId.HFX_TO_COMPUTER_STEERING_FEEDBACK)
        )
        if latest is None:
            return 0.0

        return latest.desired_helac_fl

    @property
    def target_right_wheel_angle_deg(self) -> float:
        latest: Optional[hfx.SteeringFeedback] = cast(
            Optional[hfx.SteeringFeedback], self._latest_msg.get(CANId.HFX_TO_COMPUTER_STEERING_FEEDBACK)
        )
        if latest is None:
            return 0.0

        return latest.desired_helac_fr

    @property
    def fort_button_presses(self) -> List[Tuple[int, str]]:
        return self._fort_button_presses

    def run(self) -> None:
        """
        Runs the HFX interface. Sets up async loops and readers for pumping messages through the system.
        """
        asyncio.run_coroutine_threadsafe(self.run_async(), get_event_loop_by_name()).result()
        assert False, "Control loop should not exit."

    async def run_async(self) -> None:
        """
        Control loop manages collecting all inputs, processing them in a meaningful way, and
        producing outputs (including emitting telemetry)

        Parameters:
            run_once (bool): Whether to run the control loop only once. Used for testing.
        """
        if len(self._periodic_commands) == 0:
            send_period_s = float(self._can_send_period_ms) / 1000.0
            initial_data = self._generate_can_messages()
            steering_message = can.Message(
                arbitration_id=hfx.SteeringControl().get_can_id(J1939Source.COMPUTER),
                is_extended_id=True,
                data=initial_data["steering"],
                dlc=8,
            )
            speed_message = can.Message(
                arbitration_id=hfx.DriveControl().get_can_id(J1939Source.COMPUTER),
                is_extended_id=True,
                data=initial_data["speed"],
                dlc=8,
            )
            engine_message = can.Message(
                arbitration_id=hfx.EngineTrimControl().get_can_id(J1939Source.COMPUTER),
                is_extended_id=True,
                data=initial_data["engine"],
                dlc=8,
            )
            steering_pid_first_message = can.Message(
                arbitration_id=hfx.SteeringPIDControlFirst().get_can_id(J1939Source.COMPUTER),
                is_extended_id=True,
                data=initial_data["steering_pid_first"],
                dlc=8,
            )
            steering_pid_second_message = can.Message(
                arbitration_id=hfx.SteeringPIDControlSecond().get_can_id(J1939Source.COMPUTER),
                is_extended_id=True,
                data=initial_data["steering_pid_second"],
                dlc=8,
            )
            steering_misc_message = can.Message(
                arbitration_id=hfx.SteeringMiscControl().get_can_id(J1939Source.COMPUTER),
                is_extended_id=True,
                data=initial_data["steering_misc"],
                dlc=8,
            )
            self._periodic_commands = {
                "steering": (steering_message, self._canbus.send_periodic(steering_message, send_period_s)),
                "speed": (speed_message, self._canbus.send_periodic(speed_message, send_period_s)),
                "engine": (engine_message, self._canbus.send_periodic(engine_message, send_period_s)),
                "steering_pid_first": (
                    steering_pid_first_message,
                    self._canbus.send_periodic(steering_pid_first_message, send_period_s),
                ),
                "steering_pid_second": (
                    steering_pid_second_message,
                    self._canbus.send_periodic(steering_pid_second_message, send_period_s),
                ),
                "steering_misc": (
                    steering_misc_message,
                    self._canbus.send_periodic(steering_misc_message, send_period_s),
                ),
            }

        self._running = True

        class CANListener(can.Listener):
            def __init__(self, hfx_interface: "HfxInterface") -> None:
                self._hfx_interface = hfx_interface

            def on_error(self, e: Exception) -> None:
                self._hfx_interface._running = False

            def on_message_received(self, message: can.Message) -> None:
                pass

        on_error_listener = CANListener(self)

        can_reader = can.AsyncBufferedReader()
        notifier = can.Notifier(  # noqa
            self._canbus, [can_reader, on_error_listener], timeout=self._can_send_period_ms / 1000.0
        )

        task: Optional[MakaTask] = maybe_get_current()
        while self._running:
            if task is not None:
                task.tick()

            await self._gather_inputs(can_reader)

            self._generate_outputs()

    async def _gather_inputs(self, can_reader: can.AsyncBufferedReader) -> None:
        """
        Gather all the inputs into the system that are available at the moment. This will only
        gather one CAN message at a time, but any number of message bus commands that are available.
        Results are written directly to the appropriate telemetry unit. Downstream processes can
        retrieve values from the appropriate telemetry unit.

        Parameters:
            can_reader (AsyncBufferedReader): The CAN reader object that delivers buffered CAN messages.
        """
        message: Optional[can.Message] = None
        try:
            message = await asyncio.wait_for(can_reader.get_message(), self._can_send_period_ms / 1000.0)

            self._errors = 0
        except asyncio.TimeoutError:
            if self._errors % 10000 == 0:
                LOG.debug(
                    f"Timed out {self._can_send_period_ms / 1000} waiting for a CAN message to come in. {self._errors}"
                )

            self._errors += 1
            message = None

        if not message:
            return

        # This is the field we use to determine which CAN packet this is.
        packet_id = message.arbitration_id & J1939Message.HFX_CAN_ID_MASK
        if packet_id in self._packet_id_to_parser:
            # msg_type: Type[J1939Message] = self._packet_id_to_parser[packet_id]
            msg: J1939Message = self._packet_id_to_parser[packet_id].decode(message.data)
            # TODO add a mechanism to turn this log on/off or somehow make it not so noisy
            # print({"type": self._packet_id_to_parser[packet_id].__name__, "message": msg.to_json()})

            self._handle_fort_button_presses(packet_id, msg)

            self._fort_button_presses = self._fort_button_presses[-10:]

            self._latest_msg[packet_id] = msg

    def _handle_fort_button_presses(self, packet_id: int, msg: J1939Message) -> None:
        current_time_ms = maka_control_timestamp_ms()

        if packet_id == CANId.FORT_HANDHELD_L:
            previous_msg_fort_l = cast(FortRXLeft, self._latest_msg.get(packet_id))
            if previous_msg_fort_l is not None:
                msg_fort_l = cast(FortRXLeft, msg)
                if (
                    not msg_fort_l.basic_message.button_left_status
                    and previous_msg_fort_l.basic_message.button_left_status
                ):
                    self._fort_button_presses.append((current_time_ms, "left"))
                if not msg_fort_l.basic_message.button_up_status and previous_msg_fort_l.basic_message.button_up_status:
                    self._fort_button_presses.append((current_time_ms, "up"))
                if (
                    not msg_fort_l.basic_message.button_right_status
                    and previous_msg_fort_l.basic_message.button_right_status
                ):
                    self._fort_button_presses.append((current_time_ms, "right"))
                if (
                    not msg_fort_l.basic_message.button_down_status
                    and previous_msg_fort_l.basic_message.button_down_status
                ):
                    self._fort_button_presses.append((current_time_ms, "down"))
        elif packet_id == CANId.FORT_HANDHELD_R:
            previous_msg_fort_r = cast(FortRXRight, self._latest_msg.get(packet_id))
            if previous_msg_fort_r is not None:
                msg_fort_r = cast(FortRXRight, msg)
                if (
                    not msg_fort_r.basic_message.button_left_status
                    and previous_msg_fort_r.basic_message.button_left_status
                ):
                    self._fort_button_presses.append((current_time_ms, "4"))
                if not msg_fort_r.basic_message.button_up_status and previous_msg_fort_r.basic_message.button_up_status:
                    self._fort_button_presses.append((current_time_ms, "3"))
                if (
                    not msg_fort_r.basic_message.button_right_status
                    and previous_msg_fort_r.basic_message.button_right_status
                ):
                    self._fort_button_presses.append((current_time_ms, "2"))
                if (
                    not msg_fort_r.basic_message.button_down_status
                    and previous_msg_fort_r.basic_message.button_down_status
                ):
                    self._fort_button_presses.append((current_time_ms, "1"))

    def set_steering_angles(self, left_front_deg: float, right_front_deg: float) -> None:
        """
        Set the steering angle setpoints to be communicated to the HFX on the next cycle iteration.

        Parameters:
            left_front_deg (float): The angle to set the left-front wheel to, in degrees.
            right_front_deg (float): The angle to set the right-front wheel to, in degrees.
        """
        self.set_left_steering_angle(left_front_deg)
        self.set_right_steering_angle(right_front_deg)

    def set_left_steering_angle(self, angle_deg: float) -> None:
        """
        Set the left wheel's steering angle in the rotary frame.

        Parameters:
            angle_deg (float): The angle to set the left wheel to, in the rotary frame.
        """
        assert -90 <= angle_deg <= 90, f"Tried to set angle {angle_deg}."
        self._setpoints.set_front_left_helac_position(float(angle_deg))

    def set_right_steering_angle(self, angle_deg: float) -> None:
        """
        Set the right wheel's steering angle in the rotary frame.

        Parameters:
            angle_deg (float): The angle to set the left wheel to, in the rotary frame.
        """
        assert -90 <= angle_deg <= 90, f"Tried to set angle {angle_deg}."
        self._setpoints.set_front_right_helac_position(float(angle_deg))

    def set_ground_speed(self, ground_speed_mA: int) -> None:
        """
        Set the commanded ground speed valve to open using the specified amount of current. This
        does not correspond to a fixed speed and must be modulated with feedback from the HFX about
        how fast the wheels are actually turning to achieved closed loop velocity control.

        Parameters:
            ground_speed_mA (int): How much current to command to the wheels. Positive values mean
                                   forward, negative means reverse.
        """
        self._setpoints.set_ground_speed(int(ground_speed_mA))

    def set_engine_speed_trim(self, engine_trim_delta_rpm: int) -> None:
        """
        Set the engine speed trim for the ECU to seek. This only applies in the high-idle state of
        the controller, otherwise the command will be recorded by the HFX but not used in the
        low-idle state. This command always takes a delta from the pre-configured high-idle RPM,
        making this function idempotent (i.e. calling it with +100, and +100 again will keep the
        delta trim at +100 RPM, not +200).

        Parameters:
            engine_trim_delta_rpm (int): How to modify the engine speed (RPM). Positive values
                                         increase RPM by that much, negative values decrease.
                                         Setting this to 0 will reset all applied trims.
        """
        self._setpoints.set_engine_speed_trim(engine_trim_delta_rpm)

    def kill_engine(self) -> None:
        """
        Command the engine to stop. This function is only commandable in one direction - there is no
        way to reset from an engine kill because this is a safety related operation that should
        require human intervention to rectify.
        """
        self._setpoints.set_engine_kill(True)

    def set_steering_pid_p(self, kp: float) -> None:
        self._setpoints.set_steering_pid_p(kp)

    def set_steering_pid_i(self, ki: float) -> None:
        self._setpoints.set_steering_pid_i(ki)

    def set_steering_pid_d(self, kd: float) -> None:
        self._setpoints.set_steering_pid_d(kd)

    def set_steering_deadband_angle(self, deadband_angle: float) -> None:
        self._setpoints.set_steering_deadband_angle(deadband_angle)

    def _generate_can_messages(self) -> Dict[str, bytes]:
        """
        Produce a dictionary of messages by the message "type" with the bytes that encodes the current setpoints.

        Returns:
            A dict of message types to bytes of CAN message payloads.
        """
        return {
            "steering": hfx.SteeringControl(
                front_left_helac_position=self._setpoints.front_left_helac_position,
                front_right_helac_position=self._setpoints.front_right_helac_position,
            ).encode(),
            "speed": hfx.DriveControl(self._setpoints.ground_speed).encode(),
            "engine": hfx.EngineTrimControl(self._setpoints.engine_speed_trim, self._setpoints.engine_kill).encode(),
            "steering_pid_first": hfx.SteeringPIDControlFirst(
                self._setpoints.steering_pid_p, self._setpoints.steering_pid_i
            ).encode(),
            "steering_pid_second": hfx.SteeringPIDControlSecond(True, self._setpoints.steering_pid_d).encode(),
            "steering_misc": hfx.SteeringMiscControl(True, self._setpoints.steering_deadband_angle).encode(),
        }

    def _generate_outputs(self) -> None:
        """
        Generates and sends CAN messages to the HFX that reflect the current setpoints.
        """

        new_data = self._generate_can_messages()

        for key in new_data.keys():
            self._periodic_commands[key][0].data = new_data[key]  # type: ignore
            self._periodic_commands[key][1].modify_data(self._periodic_commands[key][0])  # type: ignore


def main() -> None:
    init_log(data_logfile="data.log")
    interface = HfxInterface(
        topic=Topic("/hfx"), can_bus=can.interface.Bus("can0", bustype="socketcan"), feed=InprocFeed()  # type: ignore
    )
    interface.run()


if __name__ == "__main__":
    main()

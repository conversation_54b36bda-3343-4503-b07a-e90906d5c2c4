import asyncio
import concurrent
import os
import signal
import sys
from typing import Any, List, Optional

from lib.common.db.carbon_db_processor import CarbonDBProcessor
from lib.common.db.records.laser_records import LaserRecordRowOwner
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.carbon_serial.carbon_serial import CarbonSerialConnector
from lib.drivers.lasertb.lasertb_lib import CarbonLaserDBLite, ContinuousLaserTest, PulseLaserTest, TBLaser
from lib.drivers.nanopb.pin_controller.pin_controller_connector import PinControllerConnector

sys.path.append(os.path.realpath(os.path.join(os.getcwd(), os.path.dirname(__file__), "../../..")))


lasers_ref: List[TBLaser] = []


async def async_main() -> None:
    db = CarbonLaserDBLite(load_async=True)
    db_processor = CarbonDBProcessor(db, commit_interval_ms=5000)
    db_row_owner = LaserRecordRowOwner(db, 5000)

    connector1 = CarbonSerialConnector(
        "/dev/serial/by-id/usb-Arduino__www.arduino.cc__Arduino_Mega_2560_75932313938351B0C0D0-if00",
        asyncio.get_event_loop(),
        delay_ms=1000,
    )
    connector2 = CarbonSerialConnector(
        "/dev/serial/by-id/usb-Arduino__www.arduino.cc__0042_55832343538351606012-if00",
        asyncio.get_event_loop(),
        delay_ms=1000,
    )

    await asyncio.gather(connector1.open(), connector2.open())

    board1 = PinControllerConnector(connector1)
    board2 = PinControllerConnector(connector2)
    await asyncio.gather(board1.ping(), board2.ping())

    laser1 = TBLaser(39, db, db_processor, db_row_owner, board1, 5, 9, 0, 1, 2, 22)  # Veggie
    laser2 = TBLaser(40, db, db_processor, db_row_owner, board1, 6, 10, 3, 4, 5, 22)  # Veggie
    laser3 = TBLaser(41, db, db_processor, db_row_owner, board1, 7, 11, 6, 7, 8, 22)
    laser4 = TBLaser(42, db, db_processor, db_row_owner, board1, 8, 12, 9, 10, 11, 22)
    laser5 = TBLaser(43, db, db_processor, db_row_owner, board2, 9, 5, 0, 1, 2, 22)
    laser6 = TBLaser(44, db, db_processor, db_row_owner, board2, 10, 6, 3, 4, 5, 22)
    laser7 = TBLaser(45, db, db_processor, db_row_owner, board2, 11, 7, 6, 7, 8, 22)
    laser8 = TBLaser(46, db, db_processor, db_row_owner, board2, 12, 8, 9, 10, 11, 22)

    lasers_ref.extend([laser1, laser2, laser3, laser4, laser5, laser6, laser7, laser8])

    sampling_interval_ms: int = 60000

    tests = [
        PulseLaserTest(laser1, sampling_interval_ms, 1, False, 0, 500),
        PulseLaserTest(laser2, sampling_interval_ms, 1, False, 0, 500),
        ContinuousLaserTest(laser3, sampling_interval_ms, 1),
        PulseLaserTest(laser4, sampling_interval_ms, 1, False, 0, 100),
        ContinuousLaserTest(laser5, sampling_interval_ms, 0.59),
        PulseLaserTest(laser6, sampling_interval_ms, 1, True, 0.2, 100),
        PulseLaserTest(laser7, sampling_interval_ms, 1, False, 0, 1000),
        PulseLaserTest(laser8, sampling_interval_ms, 1, True, 0.2, 1000),
    ]

    test_tasks = [await t.start() for t in tests]
    await asyncio.gather(*test_tasks)


main_task: "Optional[asyncio.Task[None]]" = None


def exit_gracefully(signum: Any = 0, frame: Any = 0) -> None:
    if main_task is not None:
        main_task.cancel()


def main() -> None:
    signal.signal(signal.SIGINT, exit_gracefully)
    signal.signal(signal.SIGTERM, exit_gracefully)

    global main_task
    loop = get_event_loop_by_name()
    main_task = loop.create_task(async_main())
    try:
        asyncio.run_coroutine_threadsafe(main_task, loop).result()
    except concurrent.futures.CancelledError:
        pass
    finally:
        for laser in lasers_ref:
            asyncio.run_coroutine_threadsafe(laser.fire_stop(), loop).result()


if __name__ == "__main__":
    main()

# isort:skip_file
import os
import sys

import matplotlib.pyplot as plt
import pandas as pd
import streamlit as st

sys.path.append(os.path.realpath(os.path.join(os.getcwd(), os.path.dirname(__file__), "../../..")))

from lib.common.db.carbon_db_lite import CarbonDBLite  # noqa: E402
from lib.common.db.records.laser_records import LaserRecord  # noqa: E402
from lib.common.db.records.laser_sensor_samples import LaserSensorSamples  # noqa: E402
from lib.drivers.lasertb.lasertb_lib import CarbonLaserDBLite  # noqa: E402


class DBTableViewer:
    def __init__(self, db: CarbonDBLite, table_name: str) -> None:
        self._db = db
        self._table_name = table_name
        self._formatted_name = self._table_name.replace("_", " ").title()
        self._title = st.header(self._formatted_name)
        self._placeholder = st.empty()

    def update(self) -> None:
        pass


class LaserRecordDBViewer(DBTableViewer):
    def __init__(self, db: CarbonDBLite) -> None:
        super().__init__(db, LaserRecord.__tablename__)
        df = pd.read_sql_query(
            f"""
            SELECT * FROM {self._table_name}
            """,
            con=self._db.engine,
        )

        hours = []
        for _, item in df.iterrows():
            hours.append(item["millis"] / 3600000)
        df["hours"] = hours
        del df["millis"]

        self._placeholder.dataframe(df)


class LaserSensorSamplesDBViewer(DBTableViewer):
    def __init__(self, db: CarbonDBLite) -> None:
        super().__init__(db, LaserSensorSamples.__tablename__)

        laser_ids = pd.read_sql_query(
            f"""
                SELECT DISTINCT laser_id
                FROM {self._table_name}
            """,
            con=db.engine,
        )

        self._laser_id = st.sidebar.selectbox("Choose Laser ID", tuple(x for x in laser_ids["laser_id"]))

        self._column = st.sidebar.selectbox(
            "Choose Column",
            ("current+thermistors", "current_sensor", "firing", "e_stopped", "intensity", "thermistor1", "thermistor2"),
        )

        self._n_samples = st.sidebar.number_input(
            "Choose Number of Distributed Samples", min_value=20, max_value=100000, value=1000
        )

        self._extra_condition = " AND e_stopped = 0" if self._column != "e_stopped" else ""
        self._extra_condition += " AND firing = 1" if self._column not in ["e_stopped", "firing"] else ""

        self._query = f"""
            SELECT row_id, *
            FROM (
            SELECT *, ROW_NUMBER() OVER(PARTITION BY laser_id ORDER BY timestamp_ms ASC) AS row_id
            FROM (SELECT * FROM {self._table_name} WHERE laser_id = {self._laser_id} {self._extra_condition})
            ORDER BY timestamp_ms ASC)
            WHERE row_id % (((SELECT count(*) FROM {self._table_name} WHERE laser_id = {self._laser_id} {self._extra_condition}) / {self._n_samples}) + 1) = 0
        """

        df = pd.read_sql_query(self._query, con=self._db.engine)

        df["timestamp"] = pd.to_datetime(df["timestamp_ms"], unit="ms")
        df.timestamp = df.timestamp.dt.tz_localize("UTC").dt.tz_convert("America/Los_Angeles")

        fig, ax = plt.subplots()

        if self._column == "current+thermistors":
            ax.plot(df["timestamp"], df["current_sensor"], label="Current")
            ax.plot(df["timestamp"], df["thermistor1"], label="Thermistor 1")
            ax.plot(df["timestamp"], df["thermistor2"], label="Thermistor 2")
        else:
            ax.plot(df["timestamp"], df[self._column], label=self._column)
        ax.legend()
        ax.set_xlabel("Timestamp")
        ax.set_ylabel(self._column.title())
        self._placeholder.pyplot(fig)


def get_db_hash(db: CarbonLaserDBLite) -> str:
    return db._filepath


@st.cache(hash_funcs={CarbonLaserDBLite: get_db_hash})  # type: ignore
def get_db() -> CarbonLaserDBLite:
    return CarbonLaserDBLite(load_async=False)


def main() -> None:
    db = get_db()

    st.title("Carbon Laser TB")

    LaserRecordDBViewer(db)
    LaserSensorSamplesDBViewer(db)


if __name__ == "__main__":
    main()

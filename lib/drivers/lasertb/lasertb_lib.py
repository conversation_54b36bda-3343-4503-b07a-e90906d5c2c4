import abc
import asyncio
import concurrent
import os
import sys
import traceback
from typing import Optional

from lib.common.db.carbon_db_lite import CarbonDBLite, CarbonRobotTableBase
from lib.common.db.carbon_db_processor import CarbonDBProcessor
from lib.common.db.records.laser_records import <PERSON>er<PERSON><PERSON>ord, LaserRecordRowOwner
from lib.common.db.records.laser_sensor_samples import LaserSensorSamples
from lib.common.time.time import maka_control_timestamp_ms
from lib.drivers.nanopb.pin_controller.pin_controller_connector import (
    PIN_HIGH,
    PIN_LOW,
    PIN_MODE_INPUT_PULL_UP,
    PIN_MODE_OUTPUT,
    PinControllerConnector,
)

sys.path.append(os.path.realpath(os.path.join(os.getcwd(), os.path.dirname(__file__), "../../..")))


class CarbonLaserDBLite(CarbonDBLite):
    def __init__(self, load_async: bool = False) -> None:
        super().__init__(filepath="/home/<USER>/laser_records.db", load_async=load_async)
        CarbonRobotTableBase.metadata.create_all(self._engine)

    def __hash__(self) -> int:
        return self._filepath.__hash__()


class TBLaser:
    def __init__(
        self,
        laser_id: int,
        db: CarbonLaserDBLite,
        db_processor: CarbonDBProcessor,
        row_owner: LaserRecordRowOwner,
        board: PinControllerConnector,
        fire_pin: int,
        intensity_pin: int,
        current_pin: int,
        thermistor_pin_1: int,
        thermistor_pin_2: int,
        e_stop_pin: int,
    ) -> None:
        self._laser_id = laser_id
        self._db = db
        self._db_processor = db_processor
        self._board = board
        self._fire_pin = fire_pin
        self._requested_fire = False
        self._intensity_pin = intensity_pin
        self._requested_intensity = 0.0
        self._current_pin = current_pin
        self._thermistor_pin_1 = thermistor_pin_1
        self._thermistor_pin_2 = thermistor_pin_2
        self._e_stop_pin = e_stop_pin
        self._latest_e_stop_state: bool = True
        self._latest_current_sensor: float = 0
        self._laser_record_owner = row_owner
        self._laser_record: Optional[LaserRecord] = None

    @property
    def laser_id(self) -> int:
        return self._laser_id

    async def setup(self) -> None:
        await self._board.configure_pin(self._fire_pin, PIN_MODE_OUTPUT)
        await self.fire_stop()
        await self._board.configure_pin(self._e_stop_pin, PIN_MODE_INPUT_PULL_UP)
        await self._refresh_estop_state()
        await self._refresh_current_sensor_state()

    async def add_to_record(self, triggers: int, duration_ms: int) -> None:
        if self._laser_record is None:
            self._laser_record = await self._laser_record_owner.get(self._laser_id)

        if not self._latest_e_stop_state and self._latest_current_sensor > 0.1:
            self._laser_record.millis += duration_ms
            self._laser_record.triggers += triggers

    async def set_intensity(self, ratio: float) -> None:
        assert 0 <= ratio <= 1
        await self._set_intensity_8bit(int(ratio * 255))
        self._requested_intensity = ratio

    async def _set_intensity_8bit(self, pwm_val_8_bit: int) -> None:
        await self._board.configure_pwm(self._intensity_pin, pwm_val_8_bit)

    async def fire_start(self) -> None:
        await self._board.digital_write(self._fire_pin, PIN_HIGH)
        self._requested_fire = True

    async def fire_stop(self) -> None:
        await self._board.digital_write(self._fire_pin, PIN_LOW)
        self._requested_fire = False

    async def _refresh_estop_state(self) -> None:
        self._latest_e_stop_state = await self._board.digital_read(self._e_stop_pin) != PIN_LOW

    async def _refresh_current_sensor_state(self) -> None:
        self._latest_current_sensor = await self._board.analog_read(self._current_pin)

    async def record_sample(self) -> None:
        _, thermistor1, thermistor2, _ = await asyncio.gather(
            self._refresh_current_sensor_state(),
            self._board.analog_read(self._thermistor_pin_1),
            self._board.analog_read(self._thermistor_pin_2),
            self._refresh_estop_state(),
        )

        sample = LaserSensorSamples(
            laser_id=self._laser_id,
            timestamp_ms=maka_control_timestamp_ms(),
            e_stopped=self._latest_e_stop_state,
            firing=self._requested_fire,
            intensity=self._requested_intensity,
            current_sensor=self._latest_current_sensor,
            thermistor1=thermistor1,
            thermistor2=thermistor2,
        )

        await self._db_processor.add_row(sample)


class LaserTest:
    def __init__(self, laser: TBLaser) -> None:
        self._laser = laser
        self._run_task: Optional[asyncio.Task[None]] = None

    async def start(self) -> "asyncio.Task[None]":
        if self._run_task is None:
            self._run_task = asyncio.get_event_loop().create_task(self.run())
        return self._run_task

    @abc.abstractmethod
    async def run(self) -> None:
        pass


class ContinuousLaserTest(LaserTest):
    def __init__(self, laser: TBLaser, sample_interval_ms: int, intensity: float) -> None:
        super().__init__(laser=laser)
        self._sample_interval_ms = sample_interval_ms
        self._intensity = intensity

    async def run(self) -> None:
        while True:
            try:
                await self._laser.setup()
                await self._laser.set_intensity(self._intensity)
                await self._laser.fire_start()
                await self._laser.add_to_record(triggers=1, duration_ms=0)
                start_time = maka_control_timestamp_ms()
                while True:
                    await asyncio.sleep(self._sample_interval_ms / 1000)
                    current_time = maka_control_timestamp_ms()
                    await self._laser.add_to_record(triggers=0, duration_ms=current_time - start_time)
                    await self._laser.record_sample()
                    start_time = current_time
            except concurrent.futures.CancelledError:
                print(f"Cancelling Test for Laser: {self._laser.laser_id}")
                raise
            except Exception:
                traceback.print_exc()
            finally:
                try:
                    await self._laser.fire_stop()
                except Exception:
                    traceback.print_exc()


class PulseLaserTest(LaserTest):
    def __init__(
        self,
        laser: TBLaser,
        sample_interval_ms: int,
        intensity: float,
        keep_alive: bool,
        keep_alive_intensity: float,
        pulse_interval_ms: int,
    ) -> None:
        super().__init__(laser=laser)
        self._sample_interval_ms = sample_interval_ms
        self._intensity = intensity
        self._keep_alive_intensity = keep_alive_intensity
        self._keep_alive = keep_alive
        self._pulse_interval_ms = pulse_interval_ms

    async def run(self) -> None:
        try:
            await self._laser.setup()
            while True:
                try:
                    await self._laser.set_intensity(self._intensity)
                    start_time = maka_control_timestamp_ms()
                    first_time: bool = True
                    while True:
                        pulse_start_time = maka_control_timestamp_ms()
                        if self._keep_alive:
                            await self._laser.set_intensity(self._intensity)
                            if first_time:
                                await self._laser.fire_start()
                                first_time = False
                        else:
                            await self._laser.fire_start()
                        await self._laser.add_to_record(triggers=1, duration_ms=0)

                        current_time = maka_control_timestamp_ms()
                        if current_time - start_time >= self._sample_interval_ms:
                            await asyncio.sleep((self._pulse_interval_ms / 2) / 1000)
                            await self._laser.record_sample()
                            await asyncio.sleep((self._pulse_interval_ms / 2) / 1000)
                            start_time = current_time
                        else:
                            await asyncio.sleep(self._pulse_interval_ms / 1000)

                        if self._keep_alive:
                            await self._laser.set_intensity(self._keep_alive_intensity)
                        else:
                            await self._laser.fire_stop()

                        current_time = maka_control_timestamp_ms()

                        await self._laser.add_to_record(triggers=0, duration_ms=current_time - pulse_start_time)

                        await asyncio.sleep(self._pulse_interval_ms / 1000)
                except concurrent.futures.CancelledError:
                    print(f"Cancelling Test for Laser: {self._laser.laser_id}")
                    raise
                except Exception:
                    traceback.print_exc()
        finally:
            try:
                await self._laser.fire_stop()
            except Exception:
                traceback.print_exc()

import asyncio
import functools
import time
import warnings
from typing import TYPE_CHECKING, List, Literal, Optional, Tuple, Union, cast

import lib.common.logging
from lib.drivers.errors.error import RecoverableMakaDeviceException
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

warnings.filterwarnings("ignore", category=DeprecationWarning)  # isort:skip
from serial_asyncio import SerialTransport, create_serial_connection  # isort:skip # noqa:E402


LOG = lib.common.logging.get_logger(__name__)


if TYPE_CHECKING:
    AsyncQueue = asyncio.Queue[bytes]
else:
    AsyncQueue = asyncio.Queue


class CarbonSerialConnector(MakaProtocolConnector):
    def __init__(self, serial_port: str, loop: asyncio.AbstractEventLoop, delay_ms: int = 0):
        self._serial_port = serial_port
        self._delay_ms = delay_ms
        self._loop = loop
        self._read_queue: AsyncQueue = asyncio.Queue()
        self._transport: Optional[SerialTransport] = None
        self._protocol: Optional[CarbonSerialProtocol] = None
        self._event: asyncio.Event = asyncio.Event()

    def get_identifier(self) -> str:
        return self._serial_port

    def get_bootloader_type(self) -> str:
        return "serial"

    async def open(self) -> None:
        self._transport, self._protocol = await self._create_connection()
        await self._protocol.ready()
        await asyncio.sleep(self._delay_ms / 1000)
        self._event.set()
        LOG.info(f"{self.__class__.__name__} Opened Connection on Port: {self._serial_port}")

    async def close(self) -> None:
        if self._transport is not None:
            self._transport.close()
        self._transport = None
        self._protocol = None

    async def wait_for_online(self) -> None:
        await self._event.wait()

    async def _create_connection(self) -> Tuple[SerialTransport, "CarbonSerialProtocol"]:
        # noinspection PyTypeChecker
        return cast(
            Tuple[SerialTransport, CarbonSerialProtocol],
            await create_serial_connection(
                self._loop,
                functools.partial(CarbonSerialProtocol, read_queue=self._read_queue),
                self._serial_port,
                baudrate=115200,
            ),
        )

    def _recovery_required(self) -> None:
        if self._protocol is None:
            raise RecoverableMakaDeviceException("f{self.__class__.__name} connection has not been Opened yet")

    async def read(self, timeout_ms: Optional[int] = None) -> Union[bytes, None]:
        self._recovery_required()
        assert self._protocol is not None
        return await self._read_queue.get()

    async def write(self, data: bytes) -> None:
        self._recovery_required()
        assert self._protocol is not None
        await self._protocol.write(data)

    async def ping(self, data: bytes) -> float:
        self._recovery_required()
        assert self._protocol is not None
        duration = await self._protocol.ping(data)
        LOG.debug(f"{self.__class__.__name__} on port {self._serial_port} Ping Duration: {duration}")
        return duration

    async def echo(self, data: bytes) -> bytes:
        self._recovery_required()
        assert self._protocol is not None
        result = await self._protocol.echo(data)
        LOG.debug(f"{self.__class__.__name__} on port {self._serial_port} Echo Identical: {data == result}")
        return result


class CarbonSerialProtocol(asyncio.Protocol):
    """
    PsocMUSBProtocol
    Magic(1 byte)|Command(1 byte)|Size(2 bytes)|Payload
    """

    MAGIC = 0x42
    PACKET_CMD = 0x87
    PING_CMD = 0xFA
    ECHO_CMD = 0xBB
    PING_WORD = b"OK"
    ENDIAN: Literal["little"] = "little"
    HEADER_SIZE = 4

    def __init__(self, read_queue: AsyncQueue):
        super().__init__()
        self._transport: Optional[SerialTransport] = None
        self._read_queue = read_queue
        self._read_buffer: List[bytes] = []
        self._pre_read_buffer: List[bytes] = []
        self._read_size: int = 0
        self._ready_event = asyncio.Event()

        self._ping_echo_event = asyncio.Event()
        self._ping_echo_lock = asyncio.Lock()
        self._ping_time: float = 0
        self._echoing: bool = False
        self._echo: bytes = b""

    async def ready(self) -> None:
        await self._ready_event.wait()

    # Overwritten from asyncio.Protocol
    def connection_made(self, transport: SerialTransport) -> None:
        self._transport = transport
        self._ready_event.set()

    # Overwritten from asyncio.Protocol
    def data_received(self, data: bytes) -> None:
        if len(self._read_buffer) == 0 and self._read_size == 0:
            self._first_data_packet_receive(data)
        elif self._echoing:
            self._process_echo(data)
        else:
            self._process_packet(data)

    def _first_data_packet_receive(self, data: bytes) -> None:
        if len(data) == 0:
            return

        if len(data) + len(self._pre_read_buffer) < self.HEADER_SIZE:
            self._pre_read_buffer.append(data)
            return

        if len(self._pre_read_buffer) > 0:
            data = b"".join(self._pre_read_buffer + [data])
            self._pre_read_buffer = []

        magic = int.from_bytes(data[0:1], self.ENDIAN)
        cmd = int.from_bytes(data[1:2], self.ENDIAN)
        size = int.from_bytes(data[2:4], self.ENDIAN)

        if magic != self.MAGIC:
            # Invalid Magic
            print(f"Invalid Magic, data: {str(data)}")
            return

        if size == 0:
            # Invalid Size
            print("Invalid Packet Size")
            return

        self._read_size = size

        if len(data) > self.HEADER_SIZE:
            if cmd == self.PACKET_CMD:
                self._process_packet(data[self.HEADER_SIZE :])
            elif cmd == self.PING_CMD:
                self._process_ping(size, data[self.HEADER_SIZE :])
            elif cmd == self.ECHO_CMD:
                self._echoing = True
                self._process_echo(data[self.HEADER_SIZE :])

    def _process_ping(self, size: int, data: bytes) -> None:
        if size != 2 or len(data) != 2 or data != self.PING_WORD:
            print(data)
            return
        self._ping_time = time.time()
        self._ping_echo_event.set()

    def _process_echo(self, data: bytes) -> None:
        end = min(self._read_size, len(data))
        self._read_buffer.append(data[:end])
        self._read_size -= end

        if self._read_size == 0:
            self._echo = b"".join(self._read_buffer)
            self._read_buffer = []
            self._echoing = False
            self._ping_echo_event.set()

        if len(data) > end:
            self._first_data_packet_receive(data[end:])

    def _process_packet(self, data: bytes) -> None:
        end = min(self._read_size, len(data))
        self._read_buffer.append(data[:end])
        self._read_size -= end

        if self._read_size == 0:
            self._push_buffer()

        if len(data) > end:
            self._first_data_packet_receive(data[end:])

    def _push_buffer(self) -> None:
        if self._transport is not None:
            asyncio.ensure_future(self._read_queue.put(b"".join(self._read_buffer)), loop=self._transport.loop)
        self._read_buffer = []

    def connection_lost(self, exc: Optional[Exception]) -> None:
        pass

    async def ping(self, data: bytes, timeout: float = 1) -> float:
        async with self._ping_echo_lock:
            self._ping_echo_event.clear()
            start_time = time.time()
            self._write(self.pack_data(data, cmd=self.PING_CMD))
            await asyncio.wait_for(self._ping_echo_event.wait(), timeout)
            return self._ping_time - start_time

    async def echo(self, data: bytes, timeout: float = 1) -> bytes:
        async with self._ping_echo_lock:
            self._ping_echo_event.clear()
            self._write(self.pack_data(data, cmd=self.ECHO_CMD))
            await asyncio.wait_for(self._ping_echo_event.wait(), timeout)
            return self._echo

    async def write(self, data: bytes) -> None:
        self._write(self.pack_data(data))

    def _write(self, packed_data: bytes) -> None:
        if self._transport is None:
            raise RecoverableMakaDeviceException(f"{type(self).__name__} Failure, Transport is None")
        assert asyncio.get_event_loop() == self._transport.loop
        self._transport.write(packed_data)

    @classmethod
    def pack_data(cls, data: bytes, cmd: int = PACKET_CMD) -> bytes:
        return (
            cls.MAGIC.to_bytes(1, cls.ENDIAN) + cmd.to_bytes(1, cls.ENDIAN) + len(data).to_bytes(2, cls.ENDIAN) + data
        )

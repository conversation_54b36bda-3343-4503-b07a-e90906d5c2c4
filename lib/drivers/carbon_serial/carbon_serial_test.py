import asyncio
from argparse import ArgumentParser

import numpy as np

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.psoc_usb.psoc_m_usb import CarbonSerialConnector

LOG = lib.common.logging.get_logger(__name__)


async def _ping_test(connector: CarbonSerialConnector) -> None:
    small_data = b"X" * 32
    large_data = b"X" * 1000
    small_times = np.array([await connector.ping(small_data) for x in range(1000)])
    large_times = np.array([await connector.ping(large_data) for x in range(1000)])
    LOG.info(
        f"Small 32 Bytes Data Ping: mean: {small_times.mean()}, 95th %tile: {np.percentile(small_times, 95)}, 99th %tile: {np.percentile(small_times, 99)}"
    )
    LOG.info(
        f"Large 1000 Bytes Data Ping: mean: {large_times.mean()}, 95th %tile: {np.percentile(large_times, 95)}, 99th %tile: {np.percentile(large_times, 99)}"
    )


async def _echo_test(connector: CarbonSerialConnector) -> None:
    small_data = b"X" * 60
    large_data = b"X" * 1000
    small_bounce = await connector.echo(small_data)
    large_bounce = await connector.echo(large_data)
    LOG.info(f"Small Bounce: {small_data == small_bounce}")
    LOG.info(f"Large Bounce: {large_data == large_bounce}")


async def _test(serial_port: str) -> None:
    connector = CarbonSerialConnector(serial_port, asyncio.get_event_loop())
    await connector.open()
    await _echo_test(connector)
    await _ping_test(connector)


def main() -> None:
    parser = ArgumentParser("PsocMUSB Tester")
    parser.add_argument("serial_port", type=str)
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _test(args.serial_port)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

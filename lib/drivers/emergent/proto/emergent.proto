syntax = "proto3";

package emergent;

enum PixelFormat {
    PF_UNKNOWN = 0;
    BayerGB8 = 1;
    BayerGB10 = 2;
    BayerGB10Packed = 3;
    RGB8Packed = 4;
    BGR8Packed = 5;
}

enum ColorTemp {
    CT_UNKNOWN = 0;
    CT_Off = 1;
    CT_2800K = 2;
    CT_3000K = 3;
    CT_4000K = 4;
    CT_5000K = 5;
    CT_6500K = 6;
}

message GrabRequest {
    int64 timestamp_ms = 1;
    PixelFormat pixel_format = 2;
    ColorTemp color_temp = 3;
    int32 exposure_us = 4;
    float gain_db = 5;
    // Focus value controls servo, -1..1, with best value expected to be around 0.
    float focus = 6;
    // W&B gain adjustments.
    float wb_r_gain_db = 7;
    float wb_gr_gain_db = 8;
    float wb_gb_gain_db = 9;
    float wb_b_gain_db = 10;
    bool focusing_mode = 11;
}

message GrabResponse {
    int64 timestamp_ms = 1;
    int32 height = 2;
    int32 width = 3;
    int32 bits_per_pixel = 4;
    bytes data = 5;
}

message GetSensorTempRequest {
}

message GetSensorTempResponse {
    int32 sensor_temp_c = 1;
}

service EmergentService {
    rpc Grab(GrabRequest) returns (GrabResponse) {}
    rpc GetSensorTemp(GetSensorTempRequest) returns (GetSensorTempResponse) {}
}

FROM ubuntu:18.04
LABEL org.opencontainers.image.source https://github.com/carbonrobotics/robot

RUN apt-get update -qq && \
    apt-get install -y unzip wget lsb-release sudo apt-utils libglib2.0-bin && \
    rm -rf /var/lib/apt/lists/*

RUN apt-get update -qq && \
    mkdir /tmp/emergent && \
    cd /tmp/emergent && \
    wget -O emergent_camera_2.23.5.20605-ubuntu18.04.01.x86_64.zip https://go.emergentvisiontec.com/l/677863/2020-03-20/37nc8 && \
    unzip emergent_camera_2.23.5.20605-ubuntu18.04.01.x86_64.zip && \
    tar zxvf emergent_camera_2.23.5.20605-ubuntu18.04.01.x86_64.tgz && \
    ./install_eSdk.sh no_myricom no_mellanox && \
    rm -rf /tmp/emergent && \
    rm -rf /var/lib/apt/lists/*
ENV EMERGENT_DIR=/opt/EVT
ENV GENICAM_ROOT_V2_4=${EMERGENT_DIR}/eSDK/genicam/
RUN chown -R root:root ${EMERGENT_DIR}

RUN /opt/EVT/mellanox/mlnxofedinstall --vma-eth --user-space-only --without-fw-update --force

RUN wget https://github.com/protocolbuffers/protobuf/releases/download/v3.12.4/protobuf-all-3.12.4.tar.gz && \
    tar zxf protobuf-all-3.12.4.tar.gz && \
    cd protobuf-3.12.4 && \
    ./configure && \
    make -j $(nproc) && \
    make install && \
    ldconfig && \
    cd .. && \
    rm -rf protobuf-3.12.4 protobuf-all-3.12.4.tar.gz

# Install openssl (to use instead of boringssl)
RUN apt-get update -qq && \
    apt-get install -y libssl-dev && \
    rm -rf /var/lib/apt/lists/*

# Install CMake 3.16
RUN apt-get update -qq && \
    apt-get install -y wget && \
    rm -rf /var/lib/apt/lists/* && \
    wget -q -O cmake-linux.sh https://github.com/Kitware/CMake/releases/download/v3.16.1/cmake-3.16.1-Linux-x86_64.sh && \
    sh cmake-linux.sh -- --skip-license --prefix=/usr && \
    rm cmake-linux.sh

# Install git
RUN apt-get update -qq && \
    apt-get install -y git && \
    rm -rf /var/lib/apt/lists/*

RUN git clone --recursive https://github.com/grpc/grpc && \
    cd grpc && \
    git checkout v1.31.1 && \
    git submodule update && \
    mkdir -p cmake/build && \
    cd cmake/build && \
    cmake -DCMAKE_BUILD_TYPE=Release \
          -DBUILD_SHARED_LIBS=ON \
          -DgRPC_INSTALL=ON \
          -DgRPC_SSL_PROVIDER=package \
          -DgRPC_PROTOBUF_PROVIDER=package \
          ../.. && \
    make -j $(nproc) && \
    make install && \
    ldconfig && \
    cd ../../.. && \
    rm -rf grpc

# Install Dynamixel SDK
RUN git clone https://github.com/ROBOTIS-GIT/DynamixelSDK && \
    cd DynamixelSDK/c++/build/linux64 && \
    make install && \
    cd ../../../.. && \
    rm -rf DynamixelSDK

# Install OpenCV
RUN apt-get update -qq && \
    apt-get install -y libopencv-core-dev && \
    rm -rf /var/lib/apt/lists/*

# Install Boost
RUN apt-get update -qq && \
    apt-get install -y libboost-stacktrace-dev && \
    rm -rf /var/lib/apt/lists/*

COPY . /robot

RUN cd /robot/lib/drivers/emergent/server/cpp && \
    make

WORKDIR /robot
ENTRYPOINT ["/robot/bin/emergent_server"]

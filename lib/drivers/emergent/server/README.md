# Emergent Camera Setup

## Computer Setup

1. Install Ubuntu 18.04 via `ubuntu-bionic-netboot.iso` (use UEFI mode)

2. Create the standard `maka` user.

3. Install Emergent SDK.

```
wget -O emergent_camera_2.23.5.20605-ubuntu18.04.01.x86_64.zip https://go.emergentvisiontec.com/l/677863/2020-03-20/37nc8
unzip emergent_camera_2.23.5.20605-ubuntu18.04.01.x86_64.zip
tar zxvf emergent_camera_2.23.5.20605-ubuntu18.04.01.x86_64.tgz
./install_eSdk.sh no_mellanox no_myricom
cd /opt/EVT/mellanox
sudo ./mlnxofedinstall --without-fw-update --vma --force --without-mlnx-rdma-rxe-dkms --without-librxe-1 --without-librxe-dev --without-librxe-1-dbg
sudo /etc/init.d/openibd restart
```

4. Configure netplan.

   1. Create `/etc/netplan/02-enp23s0f0.yaml` with the following contents:

   ```
   network:
     version: 2
     renderer: networkd
     ethernets:
       enp23s0f0:
         addresses:
           - ************/24
         mtu: 9000
   ```

   2. Create `/etc/netplan/02-eno1.yaml` with the following contents:

   ```
   network:
     version: 2
     renderer: networkd
     ethernets:
       eno1:
         addresses:
           - **********/16
         gateway4: *********
   ```

   3. Run `sudo netplan apply` and verify `ifconfig enp23s0f0` and `ifconfig eno1`.

5. Install Docker.

   1. Install `docker.io`:

   ```
   sudo apt-get install -y docker.io
   ```

   2. Add `maka` user to group `docker`:

   ```
   sudo usermod maka -G docker -a
   ```

6. Point docker to Robot registry.

   1. Create file `/etc/docker/daemon.json`:

   ```
   {
           "insecure-registries": ["registry:5000"]
   }
   ```

   2. Add host named `registry` to `/etc/hosts` that points to Robot registry:

   ```
   *********      registry
   ```

   3. Restart Docker daemon:

   ```
   sudo systemctl restart docker
   ```

7. Add user `maka` to `dialout` group:

```
sudo usermod maka -G dialout -a
```

8. Create systemd service.

   1. Create service file `/etc/systemd/system/emergent-server.service`:

   ```
   [Unit]
   Description=Emergent Camera server
   After=network.target
   
   [Service]
   Type=simple
   Restart=always
   RestartSec=5
   User=maka
   WorkingDirectory=/
   ExecStartPre=-/usr/bin/docker pull registry:5000/emergent_server:latest
   ExecStartPre=-/usr/bin/docker image prune -f
   ExecStartPre=/bin/bash -c "echo 1 > /dev/ttyACM0 && sleep 60"
   ExecStart=/usr/bin/docker run --privileged --name=%n --rm --ulimit memlock=-1 --network=host registry:5000/emergent_server:latest
   ExecStop=/usr/bin/docker stop %n
   ExecStopPost=/bin/bash -c "echo 0 > /dev/ttyACM0 && sleep 5"
   TimeoutStartSec=120
   
   [Install]
   WantedBy=multi-user.target
   ```
   
   2. Enable and start service:

   ```
   sudo systemctl enable emergent-server
   sudo systemctl start emergent-server
   sudo systemctl status emergent-server
   ```

## Update software on Hubble

```
cd $MAKA_ROBOT_DIR
docker run -it -v /var/run/docker.sock:/var/run/docker.sock -v `pwd`:/robot ghcr.io/carbonrobotics/robot/build /bin/bash -c "cd lib/drivers/emergent; make"
ssh -t maka@********** sudo systemctl restart emergent-server
```

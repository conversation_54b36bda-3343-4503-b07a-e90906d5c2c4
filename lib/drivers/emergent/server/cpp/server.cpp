#include <chrono>
#include <csignal>
#include <exception>
#include <iostream>
#include <math.h>
#include <string>
#include <thread>

#include <grpcpp/grpcpp.h>
#include <opencv2/core.hpp>

#include "camera.h"
#include "generated/lib/drivers/emergent/proto/emergent.grpc.pb.h"
#include "lib/common/image/cpp/crop.h"
#include "lib/drivers/dynamixel/cpp/focus.h"

#define FOCUS_TOLERANCE 0.005

using namespace emergent::server;
using namespace lib::drivers::dynamixel;

std::unique_ptr<grpc::Server> server;

void shutdown_signal_handler(int signum) {
  std::cout << "Shutdown requested." << std::endl;
  grpc::Server *server_ptr = server.get();
  if (server_ptr != nullptr) {
    server_ptr->Shutdown();
  }
  std::cout << "Server stopped." << std::endl;
}

class EmergentServiceImpl final : public emergent::EmergentService::Service {
public:
  EmergentServiceImpl(CameraControl &camera_, FocusControl &focus_) : camera(camera_), focus(focus_) { camera.start(); }

  ~EmergentServiceImpl() {
    try {
      camera.stop();
    } catch (Emergent::CEmergentException *e) {
      // ignore
    }
  }

  int bits_per_pixel_to_opencv(int bits_per_pixel) {
    switch (bits_per_pixel) {
    case 8:
      return CV_8U;
    case 16:
      return CV_16U;
    case 24:
      return CV_8UC3;
    default:
      throw std::logic_error("Unsupported bits per pixel " + std::to_string(bits_per_pixel));
    }
  }

  grpc::Status Grab(grpc::ServerContext *context, const emergent::GrabRequest *request,
                    emergent::GrabResponse *response) {
    auto start_time = std::chrono::steady_clock::now();

    if (request->pixel_format() == emergent::PF_UNKNOWN) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "pixel_format is not set");
    }
    if (request->color_temp() == emergent::CT_UNKNOWN) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "color_temp is not set");
    }
    if (request->exposure_us() == 0) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "exposure is not set");
    }
    if (request->exposure_us() > 10000) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "exposure time must be no longer than 10ms");
    }
    if (request->gain_db() > 24.0) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "gain must be no larger than 24dB");
    }
    if (request->focus() < -1 || request->focus() > 1) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "focus must be in range -1..1");
    }
    if (request->wb_r_gain_db() > 20.0 || request->wb_gr_gain_db() > 20.0 || request->wb_gb_gain_db() > 20.0 ||
        request->wb_b_gain_db() > 20.0) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "WB gains must be no larger than 20dB");
    }

    try {
      const std::string &pixel_format = emergent::PixelFormat_Name(request->pixel_format());
      if (camera.get_pixel_format() != pixel_format) {
        camera.stop();
        camera.set_pixel_format(pixel_format);
      }
      camera.start();

      // These settings don't require restarting the feed.
      camera.set_color_temp(emergent::ColorTemp_Name(request->color_temp()));
      camera.set_exposure(request->exposure_us());
      camera.set_gain(convert_gain_db_to_slider(request->gain_db()));
      camera.set_wb_r_gain(
          convert_gain_db_to_slider(convert_gain_slider_to_db(camera.get_wb_r_gain()) + request->wb_r_gain_db()));
      camera.set_wb_gr_gain(
          convert_gain_db_to_slider(convert_gain_slider_to_db(camera.get_wb_gr_gain()) + request->wb_gr_gain_db()));
      camera.set_wb_gb_gain(
          convert_gain_db_to_slider(convert_gain_slider_to_db(camera.get_wb_gb_gain()) + request->wb_gb_gain_db()));
      camera.set_wb_b_gain(
          convert_gain_db_to_slider(convert_gain_slider_to_db(camera.get_wb_b_gain()) + request->wb_b_gain_db()));

      focus.set_focus(request->focus());
      auto focus_start_time = std::chrono::steady_clock::now();
      while (focus.is_moving() || std::abs(focus.get_focus() - request->focus()) > FOCUS_TOLERANCE) {
        if (std::chrono::steady_clock::now() > focus_start_time + std::chrono::seconds(3)) {
          return grpc::Status(grpc::StatusCode::OUT_OF_RANGE, "unable to achieve desired focus " +
                                                                  std::to_string(request->focus()) + ", achieved " +
                                                                  std::to_string(focus.get_focus()));
        }
      }

      camera.trigger();
      auto &frame = camera.grab();
      auto grab_done_time = std::chrono::steady_clock::now();

      // Convert image to cv2 matrix, needed for optional cropping.
      cv::Mat image(frame.size_y, frame.size_x, bits_per_pixel_to_opencv(frame.GetPixBitDepth()), frame.imagePtr);
      if (request->focusing_mode()) {
        image = lib::common::image::tiled_crops(image);
      }

      response->set_timestamp_ms(
          request->timestamp_ms() +
          std::chrono::duration_cast<std::chrono::milliseconds>(grab_done_time - start_time).count());
      response->set_height(image.size().height);
      response->set_width(image.size().width);
      response->set_bits_per_pixel(frame.GetPixBitDepth());
      response->set_data(image.data, image.total() * image.elemSize());

      return grpc::Status::OK;
    } catch (std::exception e) {
      return handle_exception(e);
    } catch (std::exception *e) {
      return handle_exception(e);
    }
  }

  grpc::Status GetSensorTemp(grpc::ServerContext *context, const emergent::GetSensorTempRequest *request,
                             emergent::GetSensorTempResponse *response) {
    try {
      response->set_sensor_temp_c(camera.get_sensor_temp());
      return grpc::Status::OK;
    } catch (std::exception e) {
      return handle_exception(e);
    } catch (std::exception *e) {
      return handle_exception(e);
    }
  }

private:
  float convert_gain_slider_to_db(uint32_t slider) {
    // GdB = 20log10(Gslider/256).
    return 20 * log10(slider / 256.0);
  }

  int32_t convert_gain_db_to_slider(float gain_db) {
    // GdB = 20log10(Gslider/256).
    return 256 * pow(10, gain_db / 20);
  }

  grpc::Status handle_exception(std::exception e) {
    return grpc::Status(grpc::StatusCode::INTERNAL, std::string(e.what()));
  }

  grpc::Status handle_exception(std::exception *e) {
    auto emergent_exception = dynamic_cast<Emergent::CEmergentException *>(e);
    if (emergent_exception != nullptr && emergent_exception->ErrorNo() == EVT_ERROR_DEVICE_LOST_CONNECTION) {
      std::cout << "Camera lost connection, shutting down server." << std::endl;
      std::thread([] {
        server->Shutdown();
        std::cout << "Server stopped." << std::endl;
      }).detach();
    }
    return grpc::Status(grpc::StatusCode::INTERNAL, std::string(e->what()));
  }

  CameraControl &camera;
  FocusControl &focus;
};

struct GigEVisionDeviceInfo find_emergent_camera() {
  auto devices = list_devices();
  if (devices.empty()) {
    throw new std::runtime_error("No cameras found!");
  }

  std::vector<struct GigEVisionDeviceInfo> emergent_cameras;
  std::cout << "Found cameras:" << std::endl;
  for (auto &device : devices) {
    std::cout << "- " << device.manufacturerName << " " << device.modelName << " " << device.currentIp << std::endl;
    if (std::string(device.manufacturerName) == "EVT") {
      emergent_cameras.push_back(device);
    }
  }
  std::cout << std::endl;

  if (emergent_cameras.size() == 0) {
    throw new std::runtime_error("No Emergent cameras found!");
  }
  if (emergent_cameras.size() > 1) {
    throw new std::runtime_error("More than one Emergent cameras found!");
  }
  return emergent_cameras[0];
}

int main() {
  // Important to destroy CameraControl before exiting, otherwise camera will
  // hang.
  signal(SIGINT, shutdown_signal_handler);
  signal(SIGTERM, shutdown_signal_handler);

  try {
    auto emergent_device = find_emergent_camera();
    CameraControl camera(emergent_device);
    FocusControl focus("/dev/ttyUSB0", 1);

    std::string server_address = "0.0.0.0:50051";
    EmergentServiceImpl service(camera, focus);

    grpc::ServerBuilder builder;
    // Listen on the given address without any authentication mechanism.
    builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
    // Limit to a single thread for handling requests to avoid concurrency.
    builder.SetResourceQuota(grpc::ResourceQuota().SetMaxThreads(2));
    // Register "service" as the instance through which we'll communicate with
    // clients. In this case it corresponds to an *synchronous* service.
    builder.RegisterService(&service);
    // Finally assemble the server.
    server = builder.BuildAndStart();
    std::cout << "Server listening on " << server_address << std::endl;

    // Wait for the server to shutdown. Note that some other thread must be
    // responsible for shutting down the server for this call to ever return.
    server->Wait();

    return 0;
  } catch (std::exception e) {
    std::cerr << "Error: " << e.what() << std::endl;
    return 1;
  } catch (std::exception *e) {
    std::cerr << "Error: " << e->what() << std::endl;
    return 1;
  }
}

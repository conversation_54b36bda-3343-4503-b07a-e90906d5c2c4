#include "camera.h"

#define SAFE_ERROR(x, msg)                                                                                             \
  {                                                                                                                    \
    EVT_ERROR error = (x);                                                                                             \
    if (error != EVT_SUCCESS) {                                                                                        \
      std::string full_msg = std::string("Failed to ") + std::string(msg);                                             \
      throw new Emergent::CEmergentException(full_msg.c_str(), error);                                                 \
    }                                                                                                                  \
  }

namespace emergent {
namespace server {

std::vector<struct GigEVisionDeviceInfo> list_devices() {
  unsigned int buffer_size = 100;
  unsigned int actual_num = 0;
  struct GigEVisionDeviceInfo buffer[buffer_size];
  Emergent::CEmergentCamera::ListDevices(buffer, buffer_size, &actual_num, nullptr);
  return std::vector<struct GigEVisionDeviceInfo>(buffer, buffer + actual_num);
}

CameraControl::CameraControl(const struct GigEVisionDeviceInfo &device_info) {
  camera.Connect(device_info);
  // Use heartbeats to auto-release camera in the case of a disconnect.
  camera.SetBool("HBDisable", false);
}

CameraControl::~CameraControl() { camera.Disconnect(); }

std::string CameraControl::get_enum_string(const std::string &name) {
  unsigned long buffer_size = 1000;
  unsigned long actual_size = 0;
  char buffer[buffer_size];
  camera.GetEnum(name.c_str(), buffer, buffer_size, &actual_size);
  return std::string(buffer, actual_size - 1);
}

PIXEL_FORMAT
CameraControl::gvsp_pixel_format_from_string(const std::string &format) {
  if (format == "BayerGB8") {
    return GVSP_PIX_BAYGB8;
  }
  if (format == "BayerGB10") {
    return GVSP_PIX_BAYGB10;
  }
  if (format == "BayerGB10Packed") {
    return GVSP_PIX_BAYGB10_PACKED;
  }
  if (format == "RGB8Packed") {
    return GVSP_PIX_RGB8;
  }
  if (format == "BGR8Packed") {
    return GVSP_PIX_BGR8;
  }
  if (format == "YUV411Packed") {
    return GVSP_PIX_YUV411_PACKED;
  }
  if (format == "YUV422Packed") {
    return GVSP_PIX_YUV422_PACKED;
  }
  if (format == "YUV444Packed") {
    return GVSP_PIX_YUV444_PACKED;
  }
  throw std::logic_error("Unknown pixel format " + format);
}

void CameraControl::start() {
  if (is_running()) {
    return;
  }
  camera.SetEnumByString("AcquisitionMode", "Continuous");
  camera.SetEnumByString("TriggerSelector", "FrameStart");
  camera.SetEnumByString("TriggerMode", "On");
  camera.SetEnumByString("TriggerSource", "Software");
  camera.SetEnumByString("GPO_1_Mode", "Exposure");
  camera.SetBool("GPO_1_Polarity", false);
  camera.StartStream();
  camera.ExecuteCommand("AcquisitionStart");
  frame.size_x = get_width();
  frame.size_y = get_height();
  frame.pixel_type = gvsp_pixel_format_from_string(get_pixel_format());
  SAFE_ERROR(camera.AllocateFrameBuffer(frame, EVT_FRAME_BUFFER_ZERO_COPY), "allocate frame");
}

bool CameraControl::is_running() { return frame.BufferHandle() != 0; }

void CameraControl::trigger() {
  SAFE_ERROR(camera.QueueFrame(frame), "queue frame");
  camera.ExecuteCommand("TriggerSoftware");
}

const Emergent::CEmergentFrame &CameraControl::grab() {
  SAFE_ERROR(camera.GetFrame(frame, 5000), "grab frame");
  return frame;
}

void CameraControl::stop() {
  if (!is_running()) {
    return;
  }
  SAFE_ERROR(camera.ReleaseFrameBuffer(frame), "release frame");
  camera.ExecuteCommand("AcquisitionStop");
  camera.StopStream();
}

} // namespace server
} // namespace emergent

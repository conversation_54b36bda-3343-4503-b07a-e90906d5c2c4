ROOT_DIR                = $(shell realpath ../../../../../)
REL_DIR                 = $(shell echo ${CURDIR} | sed "s\#^${ROOT_DIR}/\#\#")
OBJ_DIR                 = $(ROOT_DIR)/obj/$(REL_DIR)
BIN_DIR                 = $(ROOT_DIR)/bin
GENERATED_PROTO_DIR     = $(ROOT_DIR)/generated/lib/drivers/emergent/proto
GENERATED_PROTO_OBJ_DIR = $(ROOT_DIR)/obj/generated/lib/drivers/emergent/proto
BINARY                  = $(BIN_DIR)/emergent_server
CXX                     = g++
CXXFLAGS                = -c -fPIC -I/opt/EVT/eSDK/include -I$(ROOT_DIR) -std=c++14 \
                          `pkg-config --cflags protobuf grpc++`
LDFLAGS                 = -L/opt/EVT/eSDK/lib -lEmergentCamera -lEmergentGigEVision \
                          `pkg-config --libs protobuf grpc++` -lpthread -ldxl_x64_cpp -lopencv_core -ldl

.PHONY: all clean

all: $(BIN_DIR)/emergent_server

$(OBJ_DIR):
	mkdir -p $@

$(BIN_DIR):
	mkdir -p $@

$(GENERATED_PROTO_OBJ_DIR):
	mkdir -p $@

$(OBJ_DIR)/%.o: %.cpp | $(OBJ_DIR)
	$(CXX) -o $@ $< $(CXXFLAGS)

$(GENERATED_PROTO_OBJ_DIR)/%.o: $(GENERATED_PROTO_DIR)/%.cc | $(GENERATED_PROTO_OBJ_DIR)
	$(CXX) -o $@ $< $(CXXFLAGS)

$(BINARY): $(OBJ_DIR)/camera.o $(OBJ_DIR)/server.o $(GENERATED_PROTO_OBJ_DIR)/emergent.pb.o $(GENERATED_PROTO_OBJ_DIR)/emergent.grpc.pb.o | $(BIN_DIR)
	$(CXX) -o $@ $^ $(LDFLAGS)

clean:
	rm -rf $(OBJ_DIR) $(BINARY)

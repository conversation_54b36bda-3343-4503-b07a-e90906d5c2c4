#ifndef LIB_DRIVERS_EMERGENT_SERVER_CPP_CAMERA_H
#define LIB_DRIVERS_EMERGENT_SERVER_CPP_CAMERA_H 1

#include <memory>
#include <stdexcept>
#include <vector>

#include <EmergentCamera.h>
#include <cemergentexception.h>

namespace emergent {
namespace server {

std::vector<struct GigEVisionDeviceInfo> list_devices();

class CameraControl {
public:
  CameraControl(const struct GigEVisionDeviceInfo &device_info);
  ~CameraControl();

  int32_t get_sensor_temp() { return camera.GetInt32("SensTemp"); }

  uint32_t get_max_width() { return camera.GetUInt32Max("Width"); }
  uint32_t get_width() { return camera.GetUInt32("Width"); }
  void set_width(uint32_t width) { camera.SetUInt32("Width", width); }

  uint32_t get_max_height() { return camera.GetUInt32Max("Height"); }
  uint32_t get_height() { return camera.GetUInt32("Height"); }
  void set_height(uint32_t height) { camera.SetUInt32("Height", height); }

  std::string get_pixel_format() { return get_enum_string("PixelFormat"); }
  void set_pixel_format(const std::string &format) { camera.SetEnumByString("PixelFormat", format.c_str()); }

  std::string get_color_temp() { return get_enum_string("ColorTemp"); }
  void set_color_temp(const std::string &color_temp) { camera.SetEnumByString("ColorTemp", color_temp.c_str()); }

  uint32_t get_exposure() { return camera.GetUInt32("Exposure"); }
  void set_exposure(uint32_t exposure) { camera.SetUInt32("Exposure", exposure); }

  uint32_t get_gain() { return camera.GetUInt32("Gain"); }
  void set_gain(uint32_t gain) { camera.SetUInt32("Gain", gain); }

  uint32_t get_wb_r_gain() { return camera.GetUInt32("WB_R_GAIN_Value"); }
  void set_wb_r_gain(uint32_t gain) { camera.SetUInt32("WB_R_GAIN_Value", gain); }

  uint32_t get_wb_gr_gain() { return camera.GetUInt32("WB_GR_GAIN_Value"); }
  void set_wb_gr_gain(uint32_t gain) { camera.SetUInt32("WB_GR_GAIN_Value", gain); }

  uint32_t get_wb_gb_gain() { return camera.GetUInt32("WB_GB_GAIN_Value"); }
  void set_wb_gb_gain(uint32_t gain) { camera.SetUInt32("WB_GB_GAIN_Value", gain); }

  uint32_t get_wb_b_gain() { return camera.GetUInt32("WB_B_GAIN_Value"); }
  void set_wb_b_gain(uint32_t gain) { camera.SetUInt32("WB_B_GAIN_Value", gain); }

  void start();
  bool is_running();
  void trigger();
  const Emergent::CEmergentFrame &grab();
  void stop();

private:
  std::string get_enum_string(const std::string &name);
  PIXEL_FORMAT gvsp_pixel_format_from_string(const std::string &format);
  Emergent::CEmergentCamera camera;
  Emergent::CEmergentFrame frame;
};

} // namespace server
} // namespace emergent

#endif

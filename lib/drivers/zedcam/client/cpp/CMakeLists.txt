file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp)

CompileProto(../../proto/zed.proto GENERATED_PATH LANGS grpc_python python mypy grpc cpp)

add_library(zed_camera_proto ${GENERATED_PATH}/zed.grpc.pb.cc ${GENERATED_PATH}/zed.pb.cc)
target_compile_options(zed_camera_proto PRIVATE "-w")

add_library(zed_camera SHARED ${SOURCES} )
target_compile_definitions(zed_camera PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_compile_options(zed_camera PUBLIC ${TORCH_CXX_FLAGS})
target_link_libraries(zed_camera PRIVATE m spdlog fmt grpc++ ${TORCH_LIBRARIES} zed_camera_proto)

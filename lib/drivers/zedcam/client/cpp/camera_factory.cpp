#include <fmt/format.h>
#include <fmt/ostream.h>

#include "camera.h"
#include "camera_factory.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/resolv.h"

namespace lib {
namespace drivers {
namespace zedcam {

const std::string kZedModel = "ZED 2";
const std::vector<std::string> kZedHostNames = {"zed_front_right", "zed_front_left", "zed_back_right", "zed_back_left"};

std::vector<CameraInfo> ZedCameraFactory::list_devices() {
  std::vector<CameraInfo> devices{};
  for (auto &hostname : kZedHostNames) {
    std::string ip_address;
    try {
      ip_address = dns_resolve(hostname);
    } catch (maka_error &e) {
      continue;
    }

    CameraInfo info{};
    info.vendor = CameraVendor::kZed;
    info.model = kZedModel;
    info.ip_address = ip_address;
    devices.emplace_back(info);
  }
  return devices;
}

Camera ZedCameraFactory::create_device(const CameraInfo &info, const CameraSettings &settings,
                                       std::shared_ptr<carbon::config::ConfigTree> camera_config) {
  return Camera(
      [=](const CameraInfo &info_inner) {
        return std::make_unique<ZedCameraImpl>(info_inner, settings, camera_config);
      },
      info);
}

} // namespace zedcam
} // namespace drivers
} // namespace lib

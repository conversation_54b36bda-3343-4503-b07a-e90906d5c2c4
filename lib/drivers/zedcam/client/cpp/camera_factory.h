#pragma once

#include "lib/common/camera/cpp/camera_factory.h"

namespace lib {
namespace drivers {
namespace zedcam {

using namespace lib::common::camera;

class ZedCameraFactory : public CameraFactory {
public:
  std::vector<CameraInfo> list_devices() override;
  Camera create_device(const CameraInfo &info, const CameraSettings &settings,
                       std::shared_ptr<carbon::config::ConfigTree> camera_config) override;
  CameraVendor get_vendor() const override { return CameraVendor::kZed; }
};

} // namespace zedcam
} // namespace drivers
} // namespace lib

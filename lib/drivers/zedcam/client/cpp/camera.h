#pragma once

#include <memory>
#include <optional>

#include <spdlog/spdlog.h>
#include <torch/torch.h>

#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/time.h"
#include "lib/common/cpp/utils.h"
#include "lib/drivers/zedcam/proto/zed.grpc.pb.h"
#include <config/tree/cpp/config_tree.hpp>

namespace lib {
namespace drivers {
namespace zedcam {

using namespace lib::common::camera;

class ZedCameraImpl : public CameraImpl {
public:
  ZedCameraImpl(const CameraInfo &info, const CameraSettings &settings,
                std::shared_ptr<carbon::config::ConfigTree> camera_config);
  ~ZedCameraImpl();
  DELETE_COPY_AND_MOVE(ZedCameraImpl)

  virtual void start_grabbing() override {}
  virtual CameraImage grab() override;
  virtual void stop_grabbing() override {}
  virtual int64_t update_settings(const CameraSettings &) override { return maka_control_timestamp_ms(); }
  virtual double get_temperature() override { return -1; }
  virtual void sync_settings() override { spdlog::warn("sync_settings not implemented on zed cams"); }
  virtual int64_t get_link_speed() override { return -1; };

private:
  void disconnect();
  void connect();
  std::unique_ptr<zed::ZED::Stub> stub_;
};

} // namespace zedcam
} // namespace drivers
} // namespace lib

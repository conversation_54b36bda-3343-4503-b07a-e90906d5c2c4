#include <fmt/format.h>
#include <fmt/ostream.h>
#include <grpcpp/create_channel.h>
#include <spdlog/spdlog.h>

#include "camera.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/time.h"
#include "lib/common/cpp/utils.h"

namespace lib {
namespace drivers {
namespace zedcam {

constexpr int kZedGrpcPort = 50051;

ZedCameraImpl::ZedCameraImpl(const CameraInfo &info, const CameraSettings &settings,
                             std::shared_ptr<carbon::config::ConfigTree> camera_config)
    : CameraImpl(info, settings, camera_config) {
  if (info.vendor != CameraVendor::kZed) {
    throw camera_error(fmt::format("Received CameraInfo for vendor {}", info.vendor));
  }
}

CameraImage ZedCameraImpl::grab() {
  if (stub_.get() == nullptr) {
    connect();
  }

  grpc::ClientContext context;
  zed::GrabRequest req;
  req.set_timestamp_ms(maka_control_timestamp_ms());
  zed::GrabResponse resp;
  auto status = stub_->Grab(&context, req, &resp);
  if (!status.ok()) {
    disconnect();
    if (status.error_code() == grpc::StatusCode::UNAVAILABLE) {
      throw grab_timeout_error(
          fmt::format("Timed out trying to grab an image for camera {}: {}", get_info(), status.error_message()));
    } else {
      // We'll assume that every gRPC error can be corrected by retrying. This will help to prevent CVRT from crashing
      // if ZED is being restarted.
      throw grab_incomplete_error(
          fmt::format("Failed to grab an image for camera {}: {}", get_info(), status.error_message()));
    }
  }

  CameraImage cam_image{};
  cam_image.camera_id = get_info().camera_id;
  cam_image.timestamp_ms = resp.timestamp_ms();
  cam_image.pixel_format = PixelFormat::kRGB8;
  auto wrapped_image = torch::from_blob(const_cast<char *>(resp.view_image_bytes().c_str()),
                                        {resp.height(), resp.width(), 3}, torch::kUInt8);
  // Convert BGR HWC to RGB CHW.
  wrapped_image = wrapped_image.permute({2, 0, 1}).flip(0);
  if (get_settings().gpu_id.has_value()) {
    cam_image.image = wrapped_image.to({torch::kCUDA, *get_settings().gpu_id}, true);
  } else {
    cam_image.image = wrapped_image.clone();
  }
  auto wrapped_depth = torch::from_blob(const_cast<char *>(resp.view_depth_bytes().c_str()),
                                        {resp.height(), resp.width()}, torch::kUInt8);
  if (get_settings().gpu_id.has_value()) {
    cam_image.depth = wrapped_depth.to({torch::kCUDA, *get_settings().gpu_id}, true);
  } else {
    cam_image.depth = wrapped_depth.clone();
  }
  return cam_image;
}

ZedCameraImpl::~ZedCameraImpl() { disconnect(); }

void ZedCameraImpl::disconnect() { stub_.reset(nullptr); }

void ZedCameraImpl::connect() {
  grpc::ChannelArguments args;
  args.SetMaxReceiveMessageSize(1024 * 1024 * 1024);
  auto channel = grpc::CreateCustomChannel(fmt::format("{}:{}", get_info().ip_address, kZedGrpcPort),
                                           grpc::InsecureChannelCredentials(), args);
  stub_ = zed::ZED::NewStub(channel);
}

} // namespace zedcam
} // namespace drivers
} // namespace lib

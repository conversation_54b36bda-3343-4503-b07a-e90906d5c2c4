import logging
import time
from typing import Any, Optional

import grpc
import numpy as np
import numpy.typing as npt

from generated.lib.drivers.zedcam.proto import zed_pb2, zed_pb2_grpc
from lib.common.time import TimestampedObject, maka_control_timestamp_ms


class ZEDGrabResult(TimestampedObject):
    def __init__(
        self, timestamp_ms: int, view_image: npt.NDArray[Any], view_depth: npt.NDArray[Any],
    ):
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)
        self._view_image = view_image
        self._view_depth = view_depth

    @property
    def view_image(self) -> npt.NDArray[Any]:
        return self._view_image

    @property
    def view_depth(self) -> npt.NDArray[Any]:
        return self._view_depth


class ZEDClient:
    def __init__(self, hostname: str = "localhost", port: int = 50051, retries: int = 3, retry_time: float = 1) -> None:
        self._hostname = hostname
        self._port = port
        self._retries = retries
        self._retry_time = retry_time
        self._channel = None
        self._stub: Optional[zed_pb2_grpc.ZEDStub] = None

    def grab(self) -> ZEDGrabResult:
        for attempt in range(self._retries):
            try:
                self._connect()
                assert self._stub is not None
                req = zed_pb2.GrabRequest(timestamp_ms=maka_control_timestamp_ms())
                resp = self._stub.Grab(req)
                view_image = np.frombuffer(resp.view_image_bytes, dtype=np.uint8).reshape((resp.height, resp.width, 3))
                view_depth = np.frombuffer(resp.view_depth_bytes, dtype=np.uint8).reshape((resp.height, resp.width))
                return ZEDGrabResult(timestamp_ms=resp.timestamp_ms, view_image=view_image, view_depth=view_depth)
            except Exception:
                logging.exception("Error executing grab request")
                self._disconnect()
                time.sleep(self._retry_time)
        raise Exception(f"Failed to execute grab request after {self._retries} attempts")

    def _connect(self) -> None:
        if self._channel is None:
            options = [
                ("grpc.max_send_message_length", 1024 * 1024 * 1024),
                ("grpc.max_receive_message_length", 1024 * 1024 * 1024),
            ]
            self._channel = grpc.insecure_channel(f"{self._hostname}:{self._port}", options)
            self._stub = zed_pb2_grpc.ZEDStub(self._channel)

    def _disconnect(self) -> None:
        try:
            if self._channel is not None:
                self._channel.close()
        except:  # noqa
            pass

        self._channel = None
        self._stub = None

    def close(self) -> None:
        self._disconnect()

# ZED 2 camera driver

## Camera IP addresses

1. Front Right: `**********`

2. Front Left: `**********`

3. Back Right: `**********`

4. Back Left: `**********`

## Installing Jetson Nano

1. Flash Jetson Nano using latest JetPack image from [here](https://developer.nvidia.com/jetson-nano-sd-card-imager-3231).

2. Connect Jetson Nano to monitor and keyboard, set up account `maka`.

3. Connect Jetson Nano to office network.

4. Install software: `wget -q -O - http://jumphost/zedstrap | sudo bash -s WHEEL`, where `WHEEL` is `front_right`, `front_left`, `back_right` or `back_left`.

5. Install Jetson Nano with ZED camera on the robot. Follow steps in [Swapping ZED camera](#swapping-zed-camera) below.

## Updating ZED installer

Update `/opt/maka/jh_static/zedstrap/index.html` on `jumphost` with the contents of `zedstrap` file.

## Swapping ZED camera

1. Obtain serial number of new ZED camera: `ssh maka@ZED_IP /usr/local/bin/zed_serial`, where `ZED_IP` is one of the IP addresses above, corresponding to the camera you're updating.

2. Download ZED calibration config: `wget -O zed.conf https://www.stereolabs.com/developers/calib/?SN=ZED_SERIAL`, where `ZED_SERIAL` is the serial number from the previous step.

3. Verify `zed.conf` is valid: `grep INVALID zed.conf` should return nothing.

4. Install new calibration config: `cat zed.conf | ssh maka@ZED_IP /usr/local/bin/zed_install ZED_SERIAL`, where `ZED_IP` is one of the IP addresses above, and `ZED_SERIAL` is the serial number from the previous steps.

5. Restart ZED's Jetson Nano: `ssh -t maka@ZED_IP sudo reboot`, where `ZED_IP` is one of the IP addresses above.

## Building zedcam docker

1. Run `./install-qemu.sh` in `server` directory.

2. Run `make all` in `server` directory.

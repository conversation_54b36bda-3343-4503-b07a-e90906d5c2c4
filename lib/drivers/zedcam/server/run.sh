#!/bin/bash -e

DOCKER_VERSION=$(docker version --format '{{.Server.Version}}' | awk -F. '{print $1}')

if [ -t 1 ]; then
    INTERACTIVE="-it"
fi

if [[ $DOCKER_VERSION -ge 19 ]]; then
	exec docker run --privileged --gpus=all -v /etc/zed:/usr/local/zed/settings --rm ${INTERACTIVE} localhost:5000/zedcam-$(uname -m)
else
	exec docker run --privileged --runtime=nvidia -v /etc/zed:/usr/local/zed/settings --rm ${INTERACTIVE} localhost:5000/zedcam:$(uname -m)
fi

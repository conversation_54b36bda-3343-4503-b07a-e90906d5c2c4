import logging
import threading
from concurrent import futures
from typing import Any, cast

import cv2
import grpc
import numpy as np
import numpy.typing as npt
import pyzed.sl as sl

from generated.lib.drivers.zedcam.proto import zed_pb2, zed_pb2_grpc


class ZEDServicer(zed_pb2_grpc.ZEDServicer):
    def __init__(self, fail_event: threading.Event):
        self._fail_event = fail_event

        try:
            self._zed = sl.Camera()

            init_params = sl.InitParameters()
            init_params.depth_mode = sl.DEPTH_MODE.ULTRA
            init_params.coordinate_units = sl.UNIT.METER
            init_params.camera_resolution = sl.RESOLUTION.HD1080
            init_params.depth_minimum_distance = 1
            init_params.depth_maximum_distance = 20

            err = self._zed.open(init_params)
            if err != sl.ERROR_CODE.SUCCESS:
                raise Exception(f"Error opening camera: {err}")

            info = self._zed.get_camera_information()
            err = self._zed.set_camera_settings_roi(
                sl.VIDEO_SETTINGS.AEC_AGC_ROI,
                sl.Rect(
                    0,
                    0.25 * info.camera_resolution.height,
                    info.camera_resolution.width,
                    0.25 * info.camera_resolution.height,
                ),
                sl.SIDE.BOTH,
            )
            if err != sl.ERROR_CODE.SUCCESS:
                raise Exception(f"Error setting ROI: {err}")
        except:  # noqa
            self._fail_event.set()
            raise

    def _retrieve_image(self, view: sl.VIEW) -> npt.NDArray[np.uint8]:
        mat = sl.Mat()
        self._zed.retrieve_image(mat, view)
        return cast(npt.NDArray[np.uint8], mat.numpy())

    def Grab(self, req: zed_pb2.GrabRequest, context: Any) -> zed_pb2.GrabResponse:
        try:
            # Create and set RuntimeParameters after opening the camera
            runtime_parameters = sl.RuntimeParameters()
            runtime_parameters.sensing_mode = sl.SENSING_MODE.STANDARD
            # Setting the depth confidence parameters
            runtime_parameters.confidence_threshold = 100
            runtime_parameters.textureness_confidence_threshold = 100

            # Grab an image, a RuntimeParameters object must be given to grab()
            err = self._zed.grab(runtime_parameters)
            if err != sl.ERROR_CODE.SUCCESS:
                raise Exception(f"Error grabbing frame: {err}")

            info = self._zed.get_camera_information()
            return zed_pb2.GrabResponse(
                # V0: we've grabbed frame upon request
                timestamp_ms=req.timestamp_ms,
                height=info.camera_resolution.height,
                width=info.camera_resolution.width,
                view_image_bytes=bytes(cv2.cvtColor(self._retrieve_image(sl.VIEW.LEFT), cv2.COLOR_BGRA2BGR)),
                view_depth_bytes=bytes(cv2.cvtColor(self._retrieve_image(sl.VIEW.DEPTH), cv2.COLOR_BGRA2GRAY)),
            )
        except:  # noqa
            self._fail_event.set()
            raise

    def shutdown(self) -> None:
        self._zed.close()


def serve(port: int = 50051) -> None:
    options = [
        ("grpc.max_send_message_length", 1024 * 1024 * 1024),
        ("grpc.max_receive_message_length", 1024 * 1024 * 1024),
    ]
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=1), options=options)
    fail_event = threading.Event()
    servicer = ZEDServicer(fail_event)
    zed_pb2_grpc.add_ZEDServicer_to_server(servicer, server)
    server.add_insecure_port(f"[::]:{port}")
    server.start()
    print(f"Started ZED camera at 0.0.0.0:{port}")
    fail_event.wait()
    servicer.shutdown()


if __name__ == "__main__":
    logging.basicConfig()
    serve()

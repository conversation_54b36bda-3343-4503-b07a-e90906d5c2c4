ARG BASE_TAG
FROM stereolabs/zed:${BASE_TAG}
LABEL org.opencontainers.image.source https://github.com/carbonrobotics/robot

RUN apt-get update && \
    apt-get install -y build-essential \
	python3-dev \
	python3-setuptools \
	python3-protobuf \
	protobuf-compiler

# Need to install wheel before installing grpcio-tools
RUN pip3 install wheel
RUN GRPC_PYTHON_BUILD_EXT_COMPILER_JOBS=$(nproc) pip3 install grpcio-tools

# Install OpenCV-Python
ADD ./lib/drivers/zedcam/server/jetson-keyring.gpg /etc/apt/trusted.gpg.d
RUN if [ "$(uname -m)" = "aarch64" ]; then \
        rm -f /etc/apt/sources.list.d/nvidia-l4t-apt-source.list && \
        echo "deb https://repo.download.nvidia.com/jetson/common r32 main" >> /etc/apt/sources.list.d/nvidia-l4t-apt-source.list && \
        echo "deb https://repo.download.nvidia.com/jetson/t210 r32 main" >> /etc/apt/sources.list.d/nvidia-l4t-apt-source.list && \
        apt-get update && \
        apt-get install -y libopencv-python; \
    else \
        apt-get update && \
        apt-get install -y libsm6 libxext6 libxrender-dev libglib2.0-0 && \
        pip3 install opencv-python; \
    fi

ADD . /robot

WORKDIR /robot
ENTRYPOINT python3 -m lib.drivers.zedcam.server.server

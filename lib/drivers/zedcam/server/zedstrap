#!/bin/bash -uxe

if [ "$(id -u)" != "0" ]; then
	echo "Run this script as root."
	exit 1
fi

if [ $# -lt 1 ]; then
	echo "Usage: $0 <front_right|front_left|back_right|back_left>"
	exit 1
fi

camera=$1
if [ "${camera}" == "front_right" ]; then
	ip="**********0"
elif [ "${camera}" == "front_left" ]; then
	ip="**********1"
elif [ "${camera}" == "back_right" ]; then
	ip="**********2"
elif [ "${camera}" == "back_left" ]; then
	ip="**********3"
else
	echo "Camera must be one of: front_right, front_left, back_right, back_left"
	exit 1
fi

# Disable UI
systemctl set-default multi-user.target

# Set IP address
cat << EOF > /etc/network/interfaces.d/eth0
auto eth0
auto eth0:0

iface eth0 inet static
	address ${ip}
	netmask ***********
	gateway *********
iface eth0:0 inet dhcp
EOF

# Allow user maka to invoke docker ops without sudo
usermod maka -G docker -a

# Point to robot docker registry
cat << EOF > /etc/docker/daemon.json
{
    "insecure-registries": ["registry:5000"],
    "runtimes": {
        "nvidia": {
            "path": "nvidia-container-runtime",
            "runtimeArgs": []
        }
    }
}
EOF

cat << EOF >> /etc/hosts
**********      registry
EOF

# Create /etc/zed directory for ZED configs
mkdir -p /etc/zed
chown maka:maka /etc/zed

# Create ZED service
cat << EOF > /etc/systemd/system/zedcam.service
[Unit]
Description=ZED IP server
After=network.target

[Service]
Type=simple
Restart=always
RestartSec=5
User=maka
WorkingDirectory=/
ExecStartPre=-/usr/bin/docker pull registry:5000/zedcam-aarch64:latest
ExecStart=/usr/bin/docker run --privileged --runtime=nvidia -v /etc/zed:/usr/local/zed/settings --name=%n --rm -p 50051:50051 registry:5000/zedcam-aarch64:latest
ExecStop=/usr/bin/docker kill %n
StartLimitBurst=3
StartLimitInterval=300
StartLimitAction=reboot
TimeoutStartSec=600

[Install]
WantedBy=multi-user.target
EOF

# Pull container used by ZED service
docker pull registry:5000/zedcam-aarch64:latest
systemctl enable zedcam

# Script to print ZED serial number
cat << 'EOF' > /usr/local/bin/zed_serial
#!/bin/bash -u

ZED_USB_ID=$(grep ZED-2 /sys/bus/usb/devices/*/product | cut -d "/" -f 6)
if [[ "${ZED_USB_ID}" == "" ]]; then
  echo "No ZED cameras found."
  exit 1
fi

SERIAL=$(cat "/sys/bus/usb/devices/${ZED_USB_ID}/serial")
if [[ "${SERIAL}" == "" ]]; then
  echo "Can't find ZED serial number."
  exit 1
fi
echo "ZED serial: ${SERIAL}"
EOF

chmod 755 /usr/local/bin/zed_serial

# Script to install new calibration file
cat << 'EOF' > /usr/local/bin/zed_install
#!/bin/bash -ue

if [ $# -lt 1 ]; then
  echo "Usage: $0 <serial number>"
  exit 1
fi

serial=$1
config_path=/etc/zed/SN${serial}.conf
cat > ${config_path}

echo "${config_path} installed successfully."
EOF

chmod 755 /usr/local/bin/zed_install

# Sync filesystem
sync

# All done
echo "This ZED's Jetson Nano is ready to be installed on robot."

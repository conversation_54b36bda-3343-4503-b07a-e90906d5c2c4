ROOT_DIR            = ../../../..
DOCKER_IMAGE_JETSON = localhost:5000/zedcam-aarch64:latest
DOCKER_IMAGE_X86    = localhost:5000/zedcam-x86_64:latest
BASE_TAG_JETSON     = 3.4-py-runtime-jetson-jp4.3
BASE_TAG_X86        = 3.4-runtime-cuda10.0-ubuntu18.04

.PHONY: all docker_jetson docker_x86 docker_push_jetson docker_push_x86 clean

all: docker_jetson docker_push_jetson

docker_jetson:
	docker build --build-arg BASE_TAG=$(BASE_TAG_JETSON) -f $(shell realpath Dockerfile) -t $(DOCKER_IMAGE_JETSON) $(ROOT_DIR)

docker_x86:
	docker build --build-arg BASE_TAG=$(BASE_TAG_X86) -f $(shell realpath Dockerfile) -t $(DOCKER_IMAGE_X86) $(ROOT_DIR)

docker_push_jetson: docker_jetson
	docker push $(DOCKER_IMAGE_JETSON)

docker_push_x86: docker_x86
	docker push $(DOCKER_IMAGE_X86)

clean:
	-docker rmi -f $(DOCKER_IMAGE_JETSON) $(DOCKR_IMAGE_X86)

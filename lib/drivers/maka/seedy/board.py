import atexit
import struct
import threading
from typing import Any, Dict, Tuple, Type, TypeVar, cast

import serial

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)

# these must match the type enum in command_hdr in the board firmware
_MOTOR_CMD = 1
_ARM_CMD = 2
_LED_CMD = 3
_LCD_CMD = 4  # not implemented in rev A
_FIRE_CMD = 5
_READ_MAG_COMMAND = 7
MAX_STEPS_4251 = 255

DEFAULT_SERIAL_DEVICE = "/dev/ttyACM0"

CB = TypeVar("CB", bound="CypressBoard")


class CypressBoard:

    _boards_by_serial: Dict[str, "CypressBoard"] = {}

    def __init__(self, serial_device: str):
        self.serial_device = serial_device
        self._ser_lock = threading.Lock()
        self._ser = serial.Serial(serial_device, 115200)
        self.sign = lambda a: (a > 0) - (a < 0)  # pasted from SO
        self.set_armed(False)
        for i in range(8):
            self.set_laser(i, False)

    def _send_cmd(self, type: int, args_fmt: str, *args: Any) -> None:
        fmt = "BBB" + args_fmt
        size = struct.calcsize(fmt)
        pkt = struct.pack(fmt, 2, type, size, *args)
        with self._ser_lock:
            self._ser.write(pkt)
            ret = self._ser.read(1)[0]
            if ret != 0:
                LOG.error("CypressMotor send cmd failure {}".format(ret))

    def read_mag(self) -> Tuple[int, int, int]:
        fmt = "BBB"
        size = struct.calcsize(fmt)
        pkt = struct.pack(fmt, 2, _READ_MAG_COMMAND, size)
        with self._ser_lock:
            self._ser.write(pkt)
            ret = self._ser.read(7)
            xyz = struct.unpack("<hhh", ret[1:])
        return cast(Tuple[int, int, int], xyz)

    def set_motor(self, number: int, speed: int) -> None:
        dir = self.sign(speed)
        speed = abs(speed)
        self._send_cmd(_MOTOR_CMD, "BbB", number, dir, speed)

    def set_armed(self, state: bool) -> None:
        self._send_cmd(_ARM_CMD, "B", bool(state))

    def set_laser(self, number: int, on: int) -> None:
        self._send_cmd(_FIRE_CMD, "BB", number, bool(on))

    def set_led(self, number: int, color: Tuple[int, int, int]) -> None:
        self._send_cmd(_LED_CMD, "BBBB", number, *color)

    @classmethod
    def by_serial(cls: Type[CB], serial_device: str) -> "CypressBoard":
        if serial_device not in cls._boards_by_serial:
            cls._boards_by_serial[serial_device] = cls(serial_device)
        return cls._boards_by_serial[serial_device]

    @classmethod
    def shutdown(cls: Type[CB]) -> None:  # noqa: F821
        for board in cls._boards_by_serial.values():
            board.set_armed(False)
            for i in range(8):
                board.set_laser(8, False)


atexit.register(CypressBoard.shutdown)

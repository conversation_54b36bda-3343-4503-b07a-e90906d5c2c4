# Duro Bring-up
Out of the box, a Duro will be listening on *************/24 with a default gateway of ***********

Give yourself an alias at the default Duro gateway: ***********
```
sudo ifconfig int:0 *********** netmask *********** up
```

Route the duro through the alias
```
sudo ip route add ***********/16 via ********* dev int:0
```

Connect to Duro
```
docker run -v `pwd`:/robot ghcr.io/carbonrobotics/robot/common python -m lib.drivers.swift_nav.settings ************* 55555 <command>
```

Check for connections
```
ss -peanut | grep "*************:55555"
```

Grep robot logs
```
tail -F data.log | jq '. | select(.data.msg_type | contains("Mag"))'
```

import json
from typing import Any, Dict, Optional, cast

import dateutil.parser as dp
from sbp.imu import <PERSON>gImuRaw
from sbp.mag import MsgMagRaw
from sbp.msg import SBP
from sbp.navigation import MsgPosECEF, MsgPosLLH, MsgVelBody, MsgVelECEF

from lib.common.logging import get_logger
from lib.common.serialization.json import JsonSerializable
from lib.common.time import TimestampedObject, maka_control_timestamp_ms

SBPMessageType = int

LOG = get_logger(__name__)


# note: this was pulled out of lib.common.time.time because we want to avoid any
# pip requirements in that file beyond raw python install
def _parse_timestamp(timestamp: str) -> int:
    """
    Convert an ISO8601 timestamp into a Maka control timestamp (milliseconds from UNIX epoch).

    Parameters:
        timestamp (str): The timestamp to convert.

    Returns:
        Number of milliseconds from UNIX epoch.
    """
    return int(dp.parse(timestamp).timestamp() * 1000)


class SBPMessage(TimestampedObject, JsonSerializable):
    """
    Base SBPMessage
    """

    def __init__(self, *, msg: SBP, metadata: Optional[Dict[str, str]] = None, timestamp_ms: Optional[int] = None):
        self._msg: SBP = msg
        self._metadata: Dict[str, str] = metadata if metadata is not None else {}

        # TODO parse GPS time to UTC
        if timestamp_ms is None and "time" in self._metadata:
            timestamp_ms = _parse_timestamp(self._metadata["time"])
        else:
            timestamp_ms = maka_control_timestamp_ms()
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)

    @property
    def msg_type(self) -> SBPMessageType:
        return cast(SBPMessageType, self._msg.msg_type)

    @property
    def msg_type_pretty(self) -> str:
        return cast(object, self._msg).__class__.__name__

    @property
    def msg(self) -> SBP:
        return self._msg

    @property
    def metadata(self) -> Dict[str, str]:
        return self._metadata

    def _metadata_time(self) -> str:
        return self._metadata["time"]

    def to_json(self) -> Dict[str, Any]:
        """
        Example output:

        {
          "msg_type": "MsgImuRaw",
          "tow_f": 0,
          "tow": 2147483648,
          "acc_y": -405,
          "acc_z": -17388,
          "gyr_x": -60,
          "gyr_y": -65,
          "gyr_z": 546,
          "acc_x": 358
        }
        """
        r = json.loads(self._msg.to_json())
        return cast(Dict[str, Any], r)
        # fields = exclude_fields(self._msg)
        # result = {str(k): v if isinstance(v, float) or isinstance(v, int) else str(v) for k, v in fields.items()}
        # result["msg_time"] = self._metadata_time()
        # result["msg_type"] = self.msg_type_pretty
        # return result

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "SBPMessage":
        sbp = SBP.from_json_dict(data)
        return SBPMessage(msg=sbp)


class SBPMessageImuRaw(SBPMessage):

    MSG_TYPE = MsgImuRaw.msg_type

    def __init__(self, *, msg: MsgImuRaw, **kwargs: Any):
        super().__init__(msg=msg, **kwargs)

    @property
    def msg(self) -> MsgImuRaw:
        return cast(MsgImuRaw, self._msg)

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "SBPMessageImuRaw":
        sbp = MsgImuRaw.from_json_dict(data)
        return SBPMessageImuRaw(msg=sbp)


class SBPMessageMagRaw(SBPMessage):
    def __init__(self, *, msg: MsgMagRaw, **kwargs: Any):
        super().__init__(msg=msg, **kwargs)

    @property
    def msg(self) -> MsgMagRaw:
        return cast(MsgMagRaw, self._msg)

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "SBPMessageMagRaw":
        sbp = MsgMagRaw.from_json_dict(data)
        return SBPMessageMagRaw(msg=sbp)


class SBPMessagePosECEF(SBPMessage):
    def __init__(self, *, msg: MsgPosECEF, **kwargs: Any):
        super().__init__(msg=msg, **kwargs)

    @property
    def msg(self) -> MsgPosECEF:
        return cast(MsgPosECEF, self._msg)

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "SBPMessagePosECEF":
        sbp = MsgPosECEF.from_json_dict(data)
        return SBPMessagePosECEF(msg=sbp)


class SBPMessagePosLLH(SBPMessage):
    def __init__(self, *, msg: MsgPosLLH, **kwargs: Any):
        super().__init__(msg=msg, **kwargs)

    @property
    def msg(self) -> MsgPosLLH:
        return cast(MsgPosLLH, self._msg)

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "SBPMessagePosLLH":
        sbp = MsgPosLLH.from_json_dict(data)
        return SBPMessagePosLLH(msg=sbp)


class SBPMessageVelBody(SBPMessage):
    def __init__(self, *, msg: MsgVelBody, **kwargs: Any):
        super().__init__(msg=msg, **kwargs)

    @property
    def msg(self) -> MsgVelBody:
        return cast(MsgVelBody, self._msg)

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "SBPMessageVelBody":
        sbp = MsgVelBody.from_json_dict(data)
        return SBPMessageVelBody(msg=sbp)


class SBPMessageVelECEF(SBPMessage):
    def __init__(self, *, msg: MsgVelECEF, **kwargs: Any):
        super().__init__(msg=msg, **kwargs)

    @property
    def msg(self) -> MsgVelECEF:
        return cast(MsgVelECEF, self._msg)

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "SBPMessageVelECEF":
        sbp = MsgVelECEF.from_json_dict(data)
        return SBPMessageVelECEF(msg=sbp)

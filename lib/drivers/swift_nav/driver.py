import array
import fcntl
import termios
from abc import ABC, abstractmethod
from typing import Any, Callable, Dict, List, Optional

from sbp.client import <PERSON><PERSON>r, Handler
from sbp.client.drivers.base_driver import BaseDriver
from sbp.client.drivers.network_drivers import TCPDriver

from generated.core.controls.frame.model.acceleration_message import AccelerationMessage
from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from generated.core.controls.frame.model.mag_message import MagMessage
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import PublisherChannel, Topic
from lib.common.protocol.channel.callback import CallbackPublisherChannel, CallbackSubscriberChannel
from lib.common.tasks import MakaTask, get_current, start
from lib.common.time import maka_control_timestamp_ms
from lib.drivers.swift_nav import sbp
from lib.drivers.swift_nav.buffer import SBPMessageRollingBuffer
from lib.drivers.swift_nav.configuration import DEFAULT_MESSAGES, DuroInertialConfigurer
from lib.drivers.swift_nav.constants import DEFAULT_IP, DEFAULT_PORT
from lib.drivers.swift_nav.message import SBPMessage, SBPMessageType
from lib.drivers.swift_nav.message_processor import SBPMessageProcessor
from lib.drivers.swift_nav.watchdog import DuroInertialWatchdog

LOG = get_logger(__name__)

# We should be able to establish a connection to the Duro within 10 seconds assuming it's
# actually powered on and connected.
MAX_RETRY_TIMEOUT_MS: int = 10 * 1000


class SBPBufferProcessor:
    def __init__(self, msg_buffers: Dict[SBPMessageType, SBPMessageRollingBuffer]):
        self._msg_buffers: Dict[SBPMessageType, SBPMessageRollingBuffer] = msg_buffers

    def process(self, m: SBPMessage) -> None:
        if m.msg_type in self._msg_buffers:
            self._msg_buffers[m.msg_type].push(m)
        else:
            LOG.warning(f"Unexpected msg: {m.msg_type_pretty}")


class DuroInertialDriver(ABC):
    """
    Main driver for Duro Inertial
    """

    def __init__(
        self,
        base_topic: Topic,
        enabled_messages: Optional[List[SBPMessageType]] = None,
        sbp_publishers: Optional[List[PublisherChannel[SBPMessage]]] = None,
        accel_publishers: Optional[List[PublisherChannel[AccelerationMessage]]] = None,
        angular_vel_publishers: Optional[List[PublisherChannel[AngularVelocityMessage]]] = None,
        geoposition_ecef_publishers: Optional[List[PublisherChannel[GeopositionEcefMessage]]] = None,
        geoposition_lla_publishers: Optional[List[PublisherChannel[GeopositionLatLonAltMessage]]] = None,
        mag_publishers: Optional[List[PublisherChannel[MagMessage]]] = None,
        vel_publishers: Optional[List[PublisherChannel[VelocityMessage]]] = None,
    ):
        self._base_topic: Topic = base_topic
        self._driver: Optional[BaseDriver] = None
        self._handler: Optional[Handler] = None
        sbp_publishers = sbp_publishers if sbp_publishers is not None else []
        accel_publishers = accel_publishers if accel_publishers is not None else []
        angular_vel_publishers = angular_vel_publishers if angular_vel_publishers is not None else []
        geoposition_ecef_publishers = geoposition_ecef_publishers if geoposition_ecef_publishers is not None else []
        geoposition_lla_publishers = geoposition_lla_publishers if geoposition_lla_publishers is not None else []
        mag_publishers = mag_publishers if mag_publishers is not None else []
        vel_publishers = vel_publishers if vel_publishers is not None else []

        self._enabled_messages: List[
            SBPMessageType
        ] = enabled_messages if enabled_messages is not None else DEFAULT_MESSAGES

        # Msg buffers
        self._msg_buffers: Dict[SBPMessageType, SBPMessageRollingBuffer] = {}
        for msg_type in self._enabled_messages:
            msg_rolling_buffer = SBPMessageRollingBuffer(msg_type)
            self._msg_buffers[msg_type] = msg_rolling_buffer

        # Setup watchdog
        self._watchdog = DuroInertialWatchdog(fire_callback=self.reconnect_and_configure)
        self._watchdog_task: Optional[MakaTask] = None

        # Prepare the message processor callback
        processor: SBPBufferProcessor = SBPBufferProcessor(msg_buffers=self._msg_buffers)
        in_memory_cache_channel: PublisherChannel[SBPMessage] = CallbackPublisherChannel(
            topic=self._base_topic, callback=processor.process,
        )

        self._on_new_message_callback = SBPMessageProcessor(
            sbp_publisher_channels=sbp_publishers + [in_memory_cache_channel],
            accel_publisher_channels=accel_publishers,
            angular_vel_publisher_channels=angular_vel_publishers,
            geoposition_ecef_publisher_channels=geoposition_ecef_publishers,
            geoposition_lla_publisher_channels=geoposition_lla_publishers,
            mag_publisher_channels=mag_publishers,
            vel_publisher_channels=vel_publishers,
            watchdog=self._watchdog,
            callables=self._create_new_sbp_msg_callbacks(),
        ).apply

    @abstractmethod
    def _create_driver(self) -> BaseDriver:
        pass

    @abstractmethod
    def _create_new_sbp_msg_callbacks(self) -> List[Callable[[SBPMessage], None]]:
        pass

    @property
    def base_topic(self) -> Topic:
        return self._base_topic

    @property
    def enabled_messages(self) -> List[SBPMessageType]:
        return self._enabled_messages

    def read_latest_by_msg_type(self, msg_type: SBPMessageType) -> Optional[SBPMessage]:
        """
        Get the last message in the buffer
        """
        return self._msg_buffers[msg_type].peek()

    def get_callback_subscription(self, msg_type: SBPMessageType) -> CallbackSubscriberChannel[SBPMessage]:
        return CallbackSubscriberChannel(
            topic=self.base_topic.sub(str(msg_type)), callback=lambda: self.read_latest_by_msg_type(msg_type)
        )

    ###################################################
    # Re-connect
    ###################################################

    def get_imu_raw_subscription(self) -> CallbackSubscriberChannel[SBPMessage]:
        return self.get_callback_subscription(sbp.MSG_IMU_RAW)

    def get_pos_ecef_subscription(self) -> CallbackSubscriberChannel[SBPMessage]:
        return self.get_callback_subscription(sbp.MSG_POS_ECEF)

    def get_mag_raw_subscription(self) -> CallbackSubscriberChannel[SBPMessage]:
        return self.get_callback_subscription(sbp.MSG_MAG_RAW)

    def get_vel_ecef_subscription(self) -> CallbackSubscriberChannel[SBPMessage]:
        return self.get_callback_subscription(sbp.MSG_VEL_ECEF)

    ###################################################
    # Re-connect
    ###################################################

    def disconnect(self) -> None:
        if self._handler is not None and self._handler.is_alive():
            self._handler.stop()
        self._handler = None
        self._driver = None

    def connect(self) -> None:
        if self._handler is not None and self._handler.is_alive():
            raise RuntimeError("Connection already established. Did you mean to call reconnect()?")

        start_connection_attempt_time_ms = maka_control_timestamp_ms()
        task = get_current()
        while True:
            if task is not None:
                task.tick()
            else:
                LOG.warning("Skipping cancellation check because no MakaTask found")

            time_waiting_for_tcp_connection_ms = maka_control_timestamp_ms() - start_connection_attempt_time_ms
            try:
                # TODO is this whole mechanism still necessary after adding reconnect=True to the TCPDriver?
                self._driver = self._create_driver()
                break
            except OSError:
                # No route to host errors are connectivity issues, we should keep retrying in this
                # case forever.
                LOG.exception("")
                pass

            if time_waiting_for_tcp_connection_ms >= MAX_RETRY_TIMEOUT_MS:
                # We've spent too long trying to make contact. Exit this driver program that a
                # higher level process manager can notice that it's unhealthy and fire off an alert
                # for a human to debug further.
                LOG.error(
                    "Spent too long trying to establish a connection with the Duro. "
                    "It may be unpowered or off the network."
                )
                if self._watchdog_task is not None:
                    self._watchdog_task.cancel()
                else:
                    import sys

                    sys.exit(1)

        assert self._driver is not None
        self._handler = Handler(Framer(self._driver.read, self._driver.write, verbose=False))
        self._handler.add_callback(self._on_new_message_callback)
        self._handler.start()

        if self._watchdog_task is None:
            self._watchdog_task = start(name="driver/duro/watchdog", target=self._watchdog.run_forever)

        LOG.info("Successfully connected to Duro Inertial.")

    def stop(self) -> None:
        if self._watchdog_task is not None:
            self._watchdog_task.cancel()
        self.disconnect()

    def reconnect(self) -> None:
        """
        (Re)connect to the Duro and configure it for operation like we want.
        """
        self.disconnect()
        self.connect()

    def reconnect_and_configure(self) -> None:
        LOG.info("Reconnecting and configuring Duro Inertial.")
        self.reconnect()
        self.write_configuration_settings()

    def write_configuration_settings(self) -> None:
        DuroInertialConfigurer(self._handler).configure(self._enabled_messages)


class DuroInertialTCPDriver(DuroInertialDriver):
    """
    TCP-based Duro Inertial Driver.

    Currently the only implementation.
    """

    def __init__(
        self,
        host: str = DEFAULT_IP,
        port: int = DEFAULT_PORT,
        tcp_buffer_size_check_interval_ms: int = 1000,
        log_alive_interval_ms: int = 5000,
        **kwargs: Any,
    ):
        # must be set before super() is called because they will be used in create_driver
        self._host = host
        self._port = port
        super().__init__(**kwargs)
        self._last_tcp_buffer_check_ms: int = 0
        self._tcp_buffer_size_check_interval_ms: int = tcp_buffer_size_check_interval_ms
        self._last_log_alive_ms: int = 0
        self._log_alive_interval_ms: int = log_alive_interval_ms

        self._num_msgs_received: int = 0

    def connect(self) -> None:
        LOG.debug(f"{self.base_topic} Connecting to Duro Inertial {self._host}:{self._port} ...")
        super().connect()

    def _create_driver(self) -> BaseDriver:
        return TCPDriver(self._host, str(self._port), reconnect=True)

    def _create_new_sbp_msg_callbacks(self) -> List[Callable[[SBPMessage], None]]:
        return [self._on_new_sbg_msg]

    def _on_new_sbg_msg(self, sbp_msg: SBPMessage) -> None:
        now = maka_control_timestamp_ms()

        self._num_msgs_received += 1

        # check size of TCP buffer
        if now - self._last_tcp_buffer_check_ms > self._tcp_buffer_size_check_interval_ms:
            self._log_tcp_buffer_depth()
            self._last_tcp_buffer_check_ms = now

        # log alive
        has_been_too_long_since_last_log = now - self._last_log_alive_ms > self._log_alive_interval_ms
        is_nice_number_to_log = self._num_msgs_received % 1000 == 0
        if is_nice_number_to_log or has_been_too_long_since_last_log:
            LOG.debug(f"{self.base_topic} Alive! Total msgs: {self._num_msgs_received}")
            self._last_log_alive_ms = now

    def _log_tcp_buffer_depth(self) -> None:
        num_bytes: int = self.tcp_buffer_depth
        if num_bytes >= 1000:
            LOG.warning(f"{self.base_topic} TCP Buffer Size: {num_bytes}")

    @property
    def tcp_buffer_depth(self) -> int:
        bytes_available = 0
        if self._driver is not None:
            fd = self._driver.handle.fileno()
            sock_size = array.array("i", [0])  # initialise outside While loop
            ret = fcntl.ioctl(fd, termios.FIONREAD, sock_size)
            assert ret == 0
            bytes_available = sock_size[0]
        return bytes_available

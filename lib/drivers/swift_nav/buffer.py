from collections import deque
from typing import Deque, Optional, cast

from lib.drivers.swift_nav.message import SB<PERSON>essage, SBPMessageType


class SBPMessageRollingBuffer:
    """
    Mimics the "tail" functionality in Unix, for an SBP message stream.

    Provides an interface to push/peek messages.
    """

    def __init__(self, msg_type: SBPMessageType, maxlen: int = 5):
        self.msg_type = msg_type
        # Individual msgs should be quite small, so a few minutes of data should be fine when we build this out later
        self._deque: Deque[SBPMessage] = deque(maxlen=maxlen)

    def push(self, msg: SBPMessage) -> None:
        self._deque.appendleft(msg)

    def peek(self) -> Optional[SBPMessage]:
        if len(self) == 0:
            return None
        return self._deque[0]

    def __len__(self) -> int:
        return len(self._deque)

    def __getattr__(self, item: str) -> SBPMessage:
        return cast(SBPMessage, self._deque.__getattribute__(item))

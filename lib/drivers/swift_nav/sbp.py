"""
Namespace wrapper around SBP protocol messages, so clients can consolidate their imports.

Import orders match order in documentation.

Refer https://support.swiftnav.com/customer/en/portal/articles/2492810-swift-binary-protocol

Self-hosted at
https://www.dropbox.com/s/bb5ap9gq89k0n4k/Swift%20Navigation%20Binary%20Protocol%20Specification%20v2.6.3.pdf?dl=0
"""

########################################################################################################################
########################################################################################################################
# STABLE
########################################################################################################################
########################################################################################################################

# Below are stable messages which can be reliably built upon
from sbp.ext_events import SBP_MSG_EXT_EVENT as MSG_EXT_EVENT
from sbp.imu import SBP_MSG_IMU_AUX as MSG_IMU_AUX
from sbp.imu import SBP_MSG_IMU_RAW as MSG_IMU_RAW
from sbp.mag import SBP_MSG_MAG_RAW as MSG_MAG_RAW
from sbp.navigation import SBP_MSG_AGE_CORRECTIONS as MSG_AGE_CORRECTIONS
from sbp.navigation import SBP_MSG_BASELINE_ECEF as MSG_BASELINE_ECEF
from sbp.navigation import SBP_MSG_BASELINE_NED as MSG_BASELINE_NED
from sbp.navigation import SBP_MSG_DOPS as MSG_DOPS
from sbp.navigation import SBP_MSG_GPS_TIME as MSG_GPS_TIME
from sbp.navigation import SBP_MSG_POS_ECEF as MSG_POS_ECEF
from sbp.navigation import SBP_MSG_POS_ECEF_COV as MSG_POS_ECEF_COV
from sbp.navigation import SBP_MSG_POS_LLH as MSG_POS_LLH
from sbp.navigation import SBP_MSG_POS_LLH_COV as MSG_POS_LLH_COV
from sbp.navigation import SBP_MSG_UTC_TIME as MSG_UTC_TIME
from sbp.navigation import SBP_MSG_VEL_BODY as MSG_VEL_BODY
from sbp.navigation import SBP_MSG_VEL_ECEF as MSG_VEL_ECEF
from sbp.navigation import SBP_MSG_VEL_ECEF_COV as MSG_VEL_ECEF_COV
from sbp.navigation import SBP_MSG_VEL_NED as MSG_VEL_NED
from sbp.navigation import SBP_MSG_VEL_NED_COV as MSG_VEL_NED_COV
from sbp.observation import SBP_MSG_ALMANAC_GLO as MSG_ALMANAC_GLO
from sbp.observation import SBP_MSG_ALMANAC_GPS as MSG_ALMANAC_GPS
from sbp.observation import SBP_MSG_BASE_POS_ECEF as MSG_BASE_POS_ECEF
from sbp.observation import SBP_MSG_BASE_POS_LLH as MSG_BASE_POS_LLH
from sbp.observation import SBP_MSG_EPHEMERIS_BDS as MSG_EPHEMERIS_BDS
from sbp.observation import SBP_MSG_EPHEMERIS_GAL as MSG_EPHEMERIS_GAL
from sbp.observation import SBP_MSG_EPHEMERIS_GAL_DEP_A as MSG_EPHEMERIS_GAL_DEP_A
from sbp.observation import SBP_MSG_EPHEMERIS_GLO as MSG_EPHEMERIS_GLO
from sbp.observation import SBP_MSG_EPHEMERIS_GLO_DEP_A as MSG_EPHEMERIS_GLO_DEP_A
from sbp.observation import SBP_MSG_EPHEMERIS_GLO_DEP_B as MSG_EPHEMERIS_GLO_DEP_B
from sbp.observation import SBP_MSG_EPHEMERIS_GLO_DEP_C as MSG_EPHEMERIS_GLO_DEP_C
from sbp.observation import SBP_MSG_EPHEMERIS_GLO_DEP_D as MSG_EPHEMERIS_GLO_DEP_D
from sbp.observation import SBP_MSG_EPHEMERIS_GPS as MSG_EPHEMERIS_GPS
from sbp.observation import SBP_MSG_EPHEMERIS_GPS_DEP_E as MSG_EPHEMERIS_GPS_DEP_E
from sbp.observation import SBP_MSG_EPHEMERIS_GPS_DEP_F as MSG_EPHEMERIS_GPS_DEP_F
from sbp.observation import SBP_MSG_EPHEMERIS_QZSS as MSG_EPHEMERIS_QZSS
from sbp.observation import SBP_MSG_EPHEMERIS_SBAS as MSG_EPHEMERIS_SBAS
from sbp.observation import SBP_MSG_EPHEMERIS_SBAS_DEP_A as MSG_EPHEMERIS_SBAS_DEP_A
from sbp.observation import SBP_MSG_EPHEMERIS_SBAS_DEP_B as MSG_EPHEMERIS_SBAS_DEP_B
from sbp.observation import SBP_MSG_GLO_BIASES as MSG_GLO_BIASES
from sbp.observation import SBP_MSG_GNSS_CAPB as MSG_GNSS_CAPB
from sbp.observation import SBP_MSG_GROUP_DELAY as MSG_GROUP_DELAY
from sbp.observation import SBP_MSG_GROUP_DELAY_DEP_A as MSG_GROUP_DELAY_DEP_A
from sbp.observation import SBP_MSG_GROUP_DELAY_DEP_B as MSG_GROUP_DELAY_DEP_B
from sbp.observation import SBP_MSG_IONO as MSG_IONO
from sbp.observation import SBP_MSG_OBS as MSG_OBS
from sbp.observation import SBP_MSG_OSR as MSG_OSR
from sbp.observation import SBP_MSG_SV_AZ_EL as MSG_SV_AZ_EL
from sbp.observation import SBP_MSG_SV_CONFIGURATION_GPS_DEP as MSG_SV_CONFIGURATION_GPS_DEP
from sbp.orientation import SBP_MSG_ANGULAR_RATE as MSG_ANGULAR_RATE
from sbp.orientation import SBP_MSG_BASELINE_HEADING as MSG_BASELINE_HEADING
from sbp.orientation import SBP_MSG_ORIENT_EULER as MSG_ORIENT_EULER
from sbp.orientation import SBP_MSG_ORIENT_QUAT as MSG_ORIENT_QUAT
from sbp.piksi import SBP_MSG_DEVICE_MONITOR as MSG_DEVICE_MONITOR
from sbp.settings import SBP_MSG_SETTINGS_READ_BY_INDEX_DONE as MSG_SETTINGS_READ_BY_INDEX_DONE
from sbp.settings import SBP_MSG_SETTINGS_READ_BY_INDEX_REQ as MSG_SETTINGS_READ_BY_INDEX_REQ
from sbp.settings import SBP_MSG_SETTINGS_READ_BY_INDEX_RESP as MSG_SETTINGS_READ_BY_INDEX_RESP
from sbp.settings import SBP_MSG_SETTINGS_READ_REQ as MSG_SETTINGS_READ_REQ
from sbp.settings import SBP_MSG_SETTINGS_READ_RESP as MSG_SETTINGS_READ_RESP
from sbp.settings import SBP_MSG_SETTINGS_SAVE as MSG_SETTINGS_SAVE
from sbp.settings import SBP_MSG_SETTINGS_WRITE as MSG_SETTINGS_WRITE
from sbp.settings import SBP_MSG_SETTINGS_WRITE_RESP as MSG_SETTINGS_WRITE_RESP
from sbp.system import SBP_MSG_DGNSS_STATUS as MSG_DGNSS_STATUS
from sbp.system import SBP_MSG_HEARTBEAT as MSG_HEARTBEAT
from sbp.system import SBP_MSG_INS_STATUS as MSG_INS_STATUS
from sbp.system import SBP_MSG_STARTUP as MSG_STARTUP

DESCRIPTIONS = {}  # MsgID -> Description
# description: taken from SBP docs. Text inside double parenthesis is extra comment to make the docs make sense
# code is self-commenting by referencing DESCRIPTION entries

LIST_MSGS = []  # All msgs

PACKAGES = {}  # 'package name' -> [list of MsgIDs]

############################################################
# Ext Events
############################################################
DESCRIPTIONS.update({MSG_EXT_EVENT: "Reports timestamped external pin event"})

LIST_MSGS_EXT_EVENTS = [MSG_EXT_EVENT]
LIST_MSGS.extend(LIST_MSGS_EXT_EVENTS)

PACKAGES["Ext Events"] = LIST_MSGS_EXT_EVENTS

############################################################
# IMU
############################################################
# imu_type[0:7]
IMU_TYPE_IMU_MASK = 0b11111111
IMU_TYPE_IMU_BOSCH_BMI160 = 0b00000000
IMU_TYPE_IMU_VALUES = {
    IMU_TYPE_IMU_BOSCH_BMI160: "Bosch BMI160",
}
# imu_conf[0:3]
IMU_CONF_ACCELEROMETER_RANGE_MASK = 0b00001111
IMU_CONF_ACCELEROMETER_RANGE_2_G = 0b00000000
IMU_CONF_ACCELEROMETER_RANGE_4_G = 0b00000001
IMU_CONF_ACCELEROMETER_RANGE_8_G = 0b00000010
IMU_CONF_ACCELEROMETER_RANGE_16_G = 0b00000011
IMU_CONF_ACCELEROMETER_RANGE_VALUES = {
    IMU_CONF_ACCELEROMETER_RANGE_2_G: "+/- 2g",
    IMU_CONF_ACCELEROMETER_RANGE_4_G: "+/- 4g",
    IMU_CONF_ACCELEROMETER_RANGE_8_G: "+/- 8g",
    IMU_CONF_ACCELEROMETER_RANGE_16_G: "+/- 16g",
}
# imu_conf[4:7]
IMU_CONF_GYROSCOPE_RANGE_MASK = 0b11110000
IMU_CONF_GYROSCOPE_RANGE_2000_DEG_S = 0b00000000
IMU_CONF_GYROSCOPE_RANGE_1000_DEG_S = 0b00010000
IMU_CONF_GYROSCOPE_RANGE_500_DEG_S = 0b00100000
IMU_CONF_GYROSCOPE_RANGE_250_DEG_S = 0b00110000
IMU_CONF_GYROSCOPE_RANGE_125_DEG_S = 0b01000000
IMU_CONF_GYROSCOPE_RANGE_VALUES = {
    IMU_CONF_GYROSCOPE_RANGE_2000_DEG_S: "+/- 2000 deg/s",
    IMU_CONF_GYROSCOPE_RANGE_1000_DEG_S: "+/- 1000 deg/s",
    IMU_CONF_GYROSCOPE_RANGE_500_DEG_S: "+/- 500 deg/s",
    IMU_CONF_GYROSCOPE_RANGE_250_DEG_S: "+/- 250 deg/s",
    IMU_CONF_GYROSCOPE_RANGE_125_DEG_S: "+/- 125 deg/s",
}


DESCRIPTIONS.update({MSG_IMU_RAW: "Raw IMU data", MSG_IMU_AUX: "Auxiliary IMU data"})

LIST_MSGS_IMU = [MSG_IMU_RAW, MSG_IMU_AUX]
LIST_MSGS.extend(LIST_MSGS_IMU)

PACKAGES["IMU"] = LIST_MSGS_IMU


############################################################
# Mag
############################################################
# no flags


DESCRIPTIONS.update({MSG_MAG_RAW: "Raw magnetometer data"})

LIST_MSGS_MAG = [MSG_MAG_RAW]
LIST_MSGS.extend(LIST_MSGS_MAG)

PACKAGES["Mag"] = LIST_MSGS_MAG

############################################################
# Navigation
############################################################
# msg.flags[3:4]
FLAG_INS_MODE_MASK = 0b00011000
FLAG_INS_MODE_NONE = 0b00000000
FLAG_INS_MODE_INS_USED = 0b00001000
FLAG_INS_MODE_VALUES = {
    FLAG_INS_MODE_NONE: "None",
    FLAG_INS_MODE_INS_USED: "INS Used",
}

# msg.flags[0:2]
FLAG_VEL_MODE_MASK = 0b00000111
FLAG_VEL_MODE_INVALID = 0b00000000
FLAG_VEL_MODE_MEASURED_DOPPLER_DERIVED = 0b00000001
FLAG_VEL_MODE_COMPUTED_DOPPLER_DERIVED = 0b00000010
FLAG_VEL_MODE_DEAD_RECKONING = 0b00000011
FLAG_VEL_MODE_VALUES = {
    FLAG_VEL_MODE_INVALID: "Invalid",
    FLAG_VEL_MODE_MEASURED_DOPPLER_DERIVED: "Measured Doppler Derived",
    FLAG_VEL_MODE_COMPUTED_DOPPLER_DERIVED: "Computed Doppler Derived",
    FLAG_VEL_MODE_DEAD_RECKONING: "Dead Reckoning",
}

# flags[0:2]
FLAG_FIX_MODE_MASK = 0b00000111
FLAG_FIX_MODE_INVALID = 0b00000000
FLAG_FIX_MODE_SINGLE_POINT_POSITION = 0b00000001
FLAG_FIX_MODE_DIFFERENTIAL_GNSS = 0b00000010
FLAG_FIX_MODE_FLOAT_RTK = 0b00000011
FLAG_FIX_MODE_FIXED_RTK = 0b00000100
FLAG_FIX_MODE_UNDEFINED = 0b00000101
FLAG_FIX_MODE_SBAS_POSITION = 0b00000110
FLAG_FIX_MODE_VALUES = {
    FLAG_FIX_MODE_INVALID: "Invalid",
    FLAG_FIX_MODE_SINGLE_POINT_POSITION: "Single Point Precision (SPP)",
    FLAG_FIX_MODE_DIFFERENTIAL_GNSS: "Differential GNSS (DGNSS)",
    FLAG_FIX_MODE_FLOAT_RTK: "Float RTK",
    FLAG_FIX_MODE_FIXED_RTK: "Fixed RTK",
    FLAG_FIX_MODE_UNDEFINED: "Undefined",
    FLAG_FIX_MODE_SBAS_POSITION: "SBAS Position",
}


DESCRIPTIONS.update(
    {
        MSG_GPS_TIME: "GPS Time",
        MSG_UTC_TIME: "UTC Time",
        MSG_DOPS: "Dilution of Precision",
        MSG_POS_ECEF: "Single-point position in ECEF",
        MSG_POS_ECEF_COV: "Single-point position in ECEF ((with covariance))",
        MSG_POS_LLH: "Geodetic Position",
        MSG_POS_LLH_COV: "Geodetic Position ((with covariance))",
        MSG_BASELINE_ECEF: "Baseline Position in ECEF",
        MSG_BASELINE_NED: "Baseline Position in NED",
        MSG_VEL_ECEF: "Velocity in ECEF",
        MSG_VEL_ECEF_COV: "Velocity in ECEF ((with covariance))",
        MSG_VEL_NED: "Velocity in NED",
        MSG_VEL_NED_COV: "Velocity in NED ((with covariance))",
        MSG_VEL_BODY: "Velocity in Body",
        MSG_AGE_CORRECTIONS: "Age of corrections",
    }
)

LIST_MSGS_NAVIGATION = [
    MSG_GPS_TIME,
    MSG_UTC_TIME,
    MSG_DOPS,
    MSG_POS_ECEF,
    MSG_POS_ECEF_COV,
    MSG_POS_LLH,
    MSG_POS_LLH_COV,
    MSG_BASELINE_ECEF,
    MSG_BASELINE_NED,
    MSG_VEL_ECEF,
    MSG_VEL_ECEF_COV,
    MSG_VEL_NED,
    MSG_VEL_NED_COV,
    MSG_VEL_BODY,
    MSG_AGE_CORRECTIONS,
]
LIST_MSGS.extend(LIST_MSGS_NAVIGATION)

PACKAGES["Navigation"] = LIST_MSGS_NAVIGATION


DESCRIPTIONS.update(
    {
        MSG_OBS: "GPS satellite observations",
        MSG_BASE_POS_LLH: "Base station position",
        MSG_BASE_POS_ECEF: "Base station position in ECEF",
        MSG_EPHEMERIS_GPS_DEP_E: "Satellite broadcast ephemeris for GPS",
        MSG_EPHEMERIS_GPS_DEP_F: "Deprecated",
        MSG_EPHEMERIS_GPS: "Satellite broadcast ephemeris for GPS",
        MSG_EPHEMERIS_QZSS: "Satellite broadcast ephemeris for QZSS",
        MSG_EPHEMERIS_BDS: "Satellite broadcast ephemeris for GLO",
        MSG_EPHEMERIS_GAL_DEP_A: "Deprecated",
        MSG_EPHEMERIS_GAL: "Satellite broadcast ephemeris for Galileo",
        MSG_EPHEMERIS_SBAS_DEP_A: "Satellite broadcast ephemeris for SBAS",
        MSG_EPHEMERIS_GLO_DEP_A: "Satellite broadcast ephemeris for GLO",
        MSG_EPHEMERIS_SBAS_DEP_B: "Deprecated",
        MSG_EPHEMERIS_SBAS: "Satellite broadcast ephemeris for SBAS",
        MSG_EPHEMERIS_GLO_DEP_B: "Satellite broadcast ephemeris for GLO",
        MSG_EPHEMERIS_GLO_DEP_C: "Satellite broadcast ephemeris for GLO",
        MSG_EPHEMERIS_GLO_DEP_D: "Deprecated",
        MSG_EPHEMERIS_GLO: "Satellite broadcast ephemeris for GLO",
        MSG_IONO: "Iono corrections",
        MSG_SV_CONFIGURATION_GPS_DEP: "BL2C capability mask",
        MSG_GNSS_CAPB: "GNSS capabilities",
        MSG_GROUP_DELAY_DEP_A: "Group Delay",
        MSG_GROUP_DELAY_DEP_B: "Group Delay",
        MSG_GROUP_DELAY: "Group Delay",
        MSG_ALMANAC_GPS: "Satellite broadcast ephemeris for GPS",
        MSG_ALMANAC_GLO: "Satellite broadcast ephemeris for GLO",
        MSG_GLO_BIASES: "GLONASS L1/L2 Code-Phase biases",
        MSG_SV_AZ_EL: "Satellite azimuths and elevations",
        MSG_OSR: "OSR corrections",
    }
)

LIST_MSGS_OBSERVATION = [
    MSG_OBS,
    MSG_BASE_POS_LLH,
    MSG_BASE_POS_ECEF,
    MSG_EPHEMERIS_GPS_DEP_E,
    MSG_EPHEMERIS_GPS_DEP_F,
    MSG_EPHEMERIS_GPS,
    MSG_EPHEMERIS_QZSS,
    MSG_EPHEMERIS_BDS,
    MSG_EPHEMERIS_GAL_DEP_A,
    MSG_EPHEMERIS_GAL,
    MSG_EPHEMERIS_SBAS_DEP_A,
    MSG_EPHEMERIS_GLO_DEP_A,
    MSG_EPHEMERIS_SBAS_DEP_B,
    MSG_EPHEMERIS_SBAS,
    MSG_EPHEMERIS_GLO_DEP_B,
    MSG_EPHEMERIS_GLO_DEP_C,
    MSG_EPHEMERIS_GLO_DEP_D,
    MSG_EPHEMERIS_GLO,
    MSG_IONO,
    MSG_SV_CONFIGURATION_GPS_DEP,
    MSG_GNSS_CAPB,
    MSG_GROUP_DELAY_DEP_A,
    MSG_GROUP_DELAY_DEP_B,
    MSG_GROUP_DELAY,
    MSG_ALMANAC_GPS,
    MSG_ALMANAC_GLO,
    MSG_GLO_BIASES,
    MSG_SV_AZ_EL,
    MSG_OSR,
]
LIST_MSGS.extend(LIST_MSGS_OBSERVATION)

PACKAGES["Observation"] = LIST_MSGS_OBSERVATION

DESCRIPTIONS.update({MSG_DEVICE_MONITOR: "Monitor the device's voltages and temperatures."})

LIST_MSGS_PIKSI = [MSG_DEVICE_MONITOR]
LIST_MSGS.extend(LIST_MSGS_PIKSI)

DESCRIPTIONS.update(
    {
        MSG_SETTINGS_SAVE: "Save settings to flash",
        MSG_SETTINGS_WRITE: "Write device configuration settings",
        MSG_SETTINGS_WRITE_RESP: "Acknowledgement with status of MSG_SETTINGS_WRITE",
        MSG_SETTINGS_READ_REQ: "Read device configuration settings ((request))",
        MSG_SETTINGS_READ_RESP: "Read device configuration settings ((response))",
        MSG_SETTINGS_READ_BY_INDEX_REQ: "Read settings by direct index ((request))",
        MSG_SETTINGS_READ_BY_INDEX_RESP: "Read settings by direct index ((response))",
        MSG_SETTINGS_READ_BY_INDEX_DONE: "Finished reading settings",
    }
)

LIST_MSGS_SETTINGS = [
    MSG_SETTINGS_SAVE,
    MSG_SETTINGS_WRITE,
    MSG_SETTINGS_WRITE_RESP,
    MSG_SETTINGS_READ_REQ,
    MSG_SETTINGS_READ_RESP,
    MSG_SETTINGS_READ_BY_INDEX_REQ,
    MSG_SETTINGS_READ_BY_INDEX_RESP,
    MSG_SETTINGS_READ_BY_INDEX_DONE,
]
LIST_MSGS.extend(LIST_MSGS_SETTINGS)

PACKAGES["Settings"] = LIST_MSGS_SETTINGS


DESCRIPTIONS.update(
    {
        MSG_STARTUP: "System start-up message",
        MSG_DGNSS_STATUS: "Status of received corrections",
        MSG_HEARTBEAT: "System heartbeat message",
        MSG_INS_STATUS: "Inertial Navigation System status message",
    }
)

LIST_MSGS_SYSTEM = [MSG_STARTUP, MSG_DGNSS_STATUS, MSG_HEARTBEAT, MSG_INS_STATUS]
LIST_MSGS.extend(LIST_MSGS_SYSTEM)

PACKAGES["System"] = LIST_MSGS_SYSTEM


########################################################################################################################
########################################################################################################################
# DRAFT
########################################################################################################################
########################################################################################################################


DESCRIPTIONS.update(
    {
        MSG_BASELINE_HEADING: "Heading relative to True North",
        MSG_ORIENT_QUAT: "Quaternion 4 component vector",
        MSG_ORIENT_EULER: "Euler angles",
        MSG_ANGULAR_RATE: "Vehicle Body Frame instantaneous angular rates",
    }
)

LIST_MSGS_ORIENTATION_DRAFT = [MSG_BASELINE_HEADING, MSG_ORIENT_EULER, MSG_ORIENT_QUAT, MSG_ANGULAR_RATE]
LIST_MSGS.extend(LIST_MSGS_ORIENTATION_DRAFT)

PACKAGES["Orientation"] = LIST_MSGS_ORIENTATION_DRAFT

# Remaining draft messages excluded until use case identified

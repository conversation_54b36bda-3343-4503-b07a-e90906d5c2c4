from abc import ABC
from typing import Callable, List, Optional, cast

from sbp.msg import SBP

from generated.core.controls.frame.model.acceleration_message import AccelerationMessage
from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from generated.core.controls.frame.model.mag_message import MagMessage
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import PublisherChannel
from lib.common.time import maka_control_timestamp_ms
from lib.drivers.swift_nav import sbp
from lib.drivers.swift_nav.message import (
    SBPMessage,
    SBPMessageImuRaw,
    SBPMessageMagRaw,
    SBPMessagePosECEF,
    SBPMessagePosLLH,
    SBPMessageVelBody,
)
from lib.drivers.swift_nav.watchdog import DuroInertialWatchdog

LOG = get_logger(__name__)


class SBPMessageProcessor(ABC):
    """
    Main processor for SBP messages
    """

    def __init__(
        self,
        watchdog: DuroInertialWatchdog,
        sbp_publisher_channels: List[PublisherChannel[SBPMessage]],
        accel_publisher_channels: List[PublisherChannel[AccelerationMessage]],
        angular_vel_publisher_channels: List[PublisherChannel[AngularVelocityMessage]],
        geoposition_ecef_publisher_channels: List[PublisherChannel[GeopositionEcefMessage]],
        geoposition_lla_publisher_channels: List[PublisherChannel[GeopositionLatLonAltMessage]],
        mag_publisher_channels: List[PublisherChannel[MagMessage]],
        vel_publisher_channels: List[PublisherChannel[VelocityMessage]],
        callables: Optional[List[Callable[[SBPMessage], None]]] = None,
        emit_data_log: bool = True,
    ):
        self._sbp_publisher_channels = sbp_publisher_channels
        self._accel_publisher_channels = accel_publisher_channels
        self._angular_vel_publisher_channels = angular_vel_publisher_channels
        self._geoposition_ecef_publisher_channels = geoposition_ecef_publisher_channels
        self._geoposition_lla_publisher_channels = geoposition_lla_publisher_channels
        self._mag_publisher_channels = mag_publisher_channels
        self._vel_publisher_channels = vel_publisher_channels
        self._watchdog = watchdog
        self._emit_data_log = emit_data_log
        self._callables = callables if callables is not None else []

        if self._watchdog is not None:
            self.add_callback(lambda _: self._watchdog.reset_timer())

    def add_callback(self, callback: Callable[[SBPMessage], None]) -> None:
        self._callables.append(callback)

    def apply(self, msg: SBP, **metadata: str) -> None:
        sbp_msg: SBPMessage = SBPMessage(msg=msg, metadata=metadata)

        for f in self._callables:
            f(sbp_msg)

        for channel in self._sbp_publisher_channels:
            channel.send(message=sbp_msg, topic_suffix=sbp_msg.msg_type_pretty)

        # TODO convert msg GPS time to UTC time
        now = maka_control_timestamp_ms()

        if msg.msg_type == sbp.MSG_IMU_RAW:
            imu_sbp_msg: SBPMessageImuRaw = cast(SBPMessageImuRaw, sbp_msg)
            accel_message = AccelerationMessage(
                timestamp_ms=now, x=imu_sbp_msg.msg.acc_x, y=imu_sbp_msg.msg.acc_y, z=imu_sbp_msg.msg.acc_z,
            )
            for channel in self._accel_publisher_channels:
                channel.send(message=accel_message, topic_suffix="")

            angular_vel_message = AngularVelocityMessage(
                timestamp_ms=now, x=imu_sbp_msg.msg.gyr_x, y=imu_sbp_msg.msg.gyr_y, z=imu_sbp_msg.msg.gyr_z,
            )
            for channel in self._angular_vel_publisher_channels:
                channel.send(message=angular_vel_message, topic_suffix="")

        if msg.msg_type == sbp.MSG_MAG_RAW:
            mag_sbp_msg: SBPMessageMagRaw = cast(SBPMessageMagRaw, sbp_msg)
            mag_message = MagMessage(
                timestamp_ms=now, x=mag_sbp_msg.msg.mag_x, y=mag_sbp_msg.msg.mag_y, z=mag_sbp_msg.msg.mag_z,
            )
            for channel in self._mag_publisher_channels:
                channel.send(message=mag_message, topic_suffix="")

        if msg.msg_type == sbp.MSG_POS_ECEF:
            pos_ecef_sbp_msg: SBPMessagePosECEF = cast(SBPMessagePosECEF, sbp_msg)
            pos_ecef_message = GeopositionEcefMessage(
                timestamp_ms=now, x=pos_ecef_sbp_msg.msg.x, y=pos_ecef_sbp_msg.msg.y, z=pos_ecef_sbp_msg.msg.z,
            )
            for channel in self._geoposition_ecef_publisher_channels:
                channel.send(message=pos_ecef_message, topic_suffix="")

        if msg.msg_type == sbp.MSG_POS_LLH:
            pos_llh_sbp_msg: SBPMessagePosLLH = cast(SBPMessagePosLLH, sbp_msg)
            geoposition_lla_message = GeopositionLatLonAltMessage(
                timestamp_ms=now,
                lat=pos_llh_sbp_msg.msg.lat,
                lon=pos_llh_sbp_msg.msg.lon,
                alt=pos_llh_sbp_msg.msg.height,
            )
            for channel in self._geoposition_lla_publisher_channels:
                channel.send(message=geoposition_lla_message, topic_suffix="")

        # TODO is Body correct or do we want ECEF. Nothing really uses this yet so just plumbed through
        if msg.msg_type == sbp.MSG_VEL_BODY:
            vel_sbp_msg: SBPMessageVelBody = cast(SBPMessageVelBody, sbp_msg)
            vel_message = VelocityMessage(
                timestamp_ms=now, x=vel_sbp_msg.msg.x, y=vel_sbp_msg.msg.y, z=vel_sbp_msg.msg.z,
            )
            for channel in self._vel_publisher_channels:
                channel.send(message=vel_message, topic_suffix="")

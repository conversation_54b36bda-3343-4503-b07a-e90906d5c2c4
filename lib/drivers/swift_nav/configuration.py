from typing import Dict, List, Optional, cast

from sbp.client import Handler
from sbp.msg import SBP
from sbp.settings import MsgSettingsReadReq, MsgSettingsWrite

import lib.drivers.swift_nav.sbp as sbp
from lib.common.logging import get_logger
from lib.common.time.sleep import sleep_ms
from lib.drivers.swift_nav.constants import DEFAULT_MAX_RETRIES, ENCODING
from lib.drivers.swift_nav.message import SBPMessageType

LOG = get_logger(__name__)

ANGULAR_RANGE_DEGREES_PER_SECOND = 125  # Gyroscope: 125 degrees/second maximum rate
ACCELERATION_RANGE_G = 2

# Enables messages:
#  165: Settings message read response.
#  166: Settings message read by index done.
#  167: Settings message read by index response.
#  175: Settings message write response.
#  181: Device temperature and voltage levels
#  258: GPS time (Note: Uses the GPS epoch of Jan 6, 1980 UTC)
#  259: UTC time (Note: Spec unclear how leapseconds are handled. Possibly just a static correction from GPS
#                       time, which would be about 18 seconds wrong (as of 2020-04-14 - consult
#                       https://www.ietf.org/timezones/data/leap-seconds.list for the official list of leap
#                       seconds))
#  521: Position in ECEF coordinates (cartesian)
#  522: Position in LLH coordinates (geodetic)
#  525: Velocity in ECEF coordinates (cartesian)
#  529: Position LLH covariance
#  531: Velocity of the body frame
#  532: Position ECEF covariance
#  533: Velocity ECEF covariance
#  2304: IMU Raw
#  2305: IMU Aux (has IMU temperature in it)
#  2306: Mag Raw
#  65280: Startup message, containing cause of startup and type of startup
#  65282: DGNSS status
#  65283: INS status
#  65535: Watchdog heatbeat, with status messages
DEFAULT_MESSAGES: List[SBPMessageType] = [
    sbp.MSG_SETTINGS_READ_RESP,
    sbp.MSG_SETTINGS_READ_BY_INDEX_DONE,
    sbp.MSG_SETTINGS_READ_BY_INDEX_RESP,
    sbp.MSG_SETTINGS_WRITE_RESP,
    sbp.MSG_DEVICE_MONITOR,
    sbp.MSG_GPS_TIME,
    sbp.MSG_UTC_TIME,
    sbp.MSG_POS_ECEF,
    sbp.MSG_POS_LLH,
    sbp.MSG_VEL_ECEF,
    sbp.MSG_VEL_NED,
    sbp.MSG_POS_LLH_COV,
    sbp.MSG_VEL_BODY,
    sbp.MSG_POS_ECEF_COV,
    sbp.MSG_VEL_ECEF_COV,
    sbp.MSG_IMU_RAW,
    sbp.MSG_IMU_AUX,
    sbp.MSG_MAG_RAW,
    sbp.MSG_STARTUP,
    sbp.MSG_DGNSS_STATUS,
    sbp.MSG_INS_STATUS,
    sbp.MSG_HEARTBEAT,
]


class DuroInertialConfigurer:
    """
    Handles setting configuration for the Duro
    """

    def __init__(self, handler: Handler):
        self._handler = handler

    def configure(self, enabled_messages: List[SBPMessageType]) -> None:
        """
        Configure the Duro with all the setting we care about.
        """
        # Settings are all defined in https://www.swiftnav.com/latest/piksi-multi-settings
        # Internal mirror as of 2020-04-14:
        # https://www.dropbox.com/home/<USER>/Manufacturers%20%26%20Vendors/Swift%20Navigation?preview=PiksiMulti-settings-v2.3.17.pdf
        settings = [
            # Sets the RTK's filter uncertainty of position, velocity, and acceleration in the
            # horizontal and vertical directions to the minimum, since our dynamics are very low.
            ("solution", "dynamic_motion_model", "Low Dynamics",),
            ("solution", "output_every_n_obs", "1"),  # We want to hear about every observation
            ("imu", "imu_raw_output", "True"),  # Turn on the IMU's raw outputs
            ("imu", "imu_rate", "100"),
            ("imu", "acc_range", f"{ACCELERATION_RANGE_G}g"),  # Accelerometer: +-2g maximum
            ("imu", "gyro_range", f"{ANGULAR_RANGE_DEGREES_PER_SECOND}"),
            ("imu", "mag_raw_output", "True"),  # Enable the raw magnetometer outputs.
            ("imu", "mag_rate", "25"),  # Magnetometer: Output at 25Hz, the fastest that's possible
            # output loosely coupled solutions, utilizing GNSS and inertial data
            ("ins", "output_mode", "Loosely Coupled"),
            (
                "tcp_server0",
                "enabled_sbp_messages",
                ",".join([str(msg_type) for msg_type in sorted(enabled_messages)]),
            ),
        ]

        for section, setting, value in settings:
            LOG.info(f"Configuring Duro :: {section}.{setting} = {value}")
            self._write_setting(section, setting, value)

    def _write_setting(self, section: str, setting: str, value: str) -> None:
        """
        Writes a specific setting to the Duro.

        Parameters:
            section (str): The section to write to. All settings are part of some section.
            setting (str): The name of the setting to write.
            value (str): The value to write. This must be a string, even if representing a floating point value.

        Raises:
            RuntimeError if writing the setting fails.
        """
        assert self._handler is not None
        max_write_attempts = DEFAULT_MAX_RETRIES

        write_status: Optional[int] = None

        def write_response_handler(msg: SBP, **metadata: Dict[str, str]) -> None:
            nonlocal write_status
            write_status = msg.status

        self._handler.add_callback(write_response_handler, sbp.MSG_SETTINGS_WRITE_RESP)

        setting_bytes = b"\0".join([section.encode(ENCODING), setting.encode(ENCODING), value.encode(ENCODING), b""])
        attempts = 0
        try:
            while attempts < max_write_attempts:
                attempts += 1

                write_command = MsgSettingsWrite(setting=setting_bytes)
                self._handler(write_command)

                if write_status == 0 and self._confirm_write_setting(section, setting, value):
                    self._handler.remove_callback(write_response_handler, sbp.MSG_SETTINGS_WRITE_RESP)
                    return

                if write_status == 1:
                    raise RuntimeError(
                        f'Unable to write setting "{setting}" in section "{section}" with value "{value}". '
                        f"Setting value rejected."
                    )
                elif write_status == 2:
                    raise RuntimeError(
                        f'Unable to write setting "{setting}" in section "{section}". Setting does not exist.'
                    )
                elif write_status is not None and write_status > 0:
                    raise RuntimeError("Unknown setting write status response.")
                else:
                    sleep_ms(10)
                    continue
            raise RuntimeError(
                f'Unable to write setting "{setting}" in section "{section}" with value "{value}". '
                f"Setting may be read-only or the value could be out of bounds."
            )
        except RuntimeError as e:
            # Cleanup before emitting an error about retries being exceeded.
            self._handler.remove_callback(write_response_handler, sbp.MSG_SETTINGS_WRITE_RESP)
            raise e

    def _confirm_write_setting(self, section: str, setting: str, value: str) -> bool:
        """
        Validates that a setting has been written successfully to the Duro,
        by reading that setting back and comparing to the expected value.

        Parameters:
            section (str): The section that has the requested setting.
            setting (str): The name of the setting to verify.
            value (str): The expected value.

        Returns:
            True if the value read back from the Duro matches the expected value.
        """
        max_attempts = DEFAULT_MAX_RETRIES

        float_comparison_epsilon = 1e-6

        attempts = 0
        while attempts < max_attempts:
            attempts += 1

            actual_value = self._read_setting(section, setting)
            if value != actual_value:
                try:
                    # We could have not matched because of awkward floating point rounding. Try to compare value
                    # numerically within an epsilon.
                    float_val = float(value)
                    float_actual = float(actual_value)
                    if abs(float_val - float_actual) > float_comparison_epsilon:
                        continue
                    else:
                        return True
                except (TypeError, ValueError):
                    continue
            else:
                return True
        # Value never matched if we got here.
        return False

    def _read_setting(self, section: str, setting: str) -> str:
        """
        Read the value of a specific setting.

        Parameters:
            section (str): The section that has the requested setting.
            setting (str): The name of the setting to verify.

        Raises:
            RuntimeError if reading the setting fails.

        Returns:
            The setting as read back. This is always a string.
        """
        assert self._handler is not None
        read_value = None

        def read_callback(msg: SBP, **metadata: str) -> None:
            _, _, value, _ = msg.payload[2:].split(b"\0")[:4]
            nonlocal read_value
            read_value = value.decode(ENCODING)

        self._handler.add_callback(read_callback, sbp.MSG_SETTINGS_READ_RESP)

        max_read_attempts = DEFAULT_MAX_RETRIES
        attempts = 0
        setting_bytes = b"\0".join([section.encode(ENCODING), setting.encode(ENCODING), b""])
        while read_value is None and attempts < max_read_attempts:
            attempts += 1
            self._handler(MsgSettingsReadReq(setting=setting_bytes))
            sleep_ms(10)

        self._handler.remove_callback(read_callback, sbp.MSG_SETTINGS_READ_RESP)
        if read_value is not None:
            return cast(str, read_value)
        else:
            raise RuntimeError(f'Unable to read setting "{setting}" in section "{section}". Setting may not exist.')

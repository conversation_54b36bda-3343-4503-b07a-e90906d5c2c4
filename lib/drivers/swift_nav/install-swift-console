#!/usr/bin/env bash

VERSION=2.3.21

echo "This script will download and install the Swift Navigation Console from the internet"
echo
echo "https://support.swiftnav.com/support/solutions/articles/44001903699-installing-swift-console"
echo
read -s -n 1 -p "Press any key to continue . . ."
echo
set +x
wget "https://www.swiftnav.com/resource-files/Swift%20Console/v${VERSION}/Installer/swift_console_v${VERSION}_linux.tar.gz"
tar xvfz "swift_console_v${VERSION}_linux.tar.gz"
cd "swift_console_v${VERSION}_ubuntu1804"
sudo ./configure_udev_rules.sh
set +x
`pwd`/console --tcp -p *************:55555


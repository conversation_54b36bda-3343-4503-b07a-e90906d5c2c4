import asyncio
from typing import Callable

from lib.common.logging import get_logger
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.tasks.scheduler import dispatch
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)

# Consider the watchdog to have timed out if we've gone 1 second without hearing anything
# from the Duro.
TIMEOUT_MS = 1000

# Evaluate the watchdog condition 10 times per second.
FREQ_HZ = 10
POLL_MS: int = int(round(1000 / FREQ_HZ))

MAX_CONSECUTIVE_FAILURES = 10


class DuroInertialWatchdog:
    def __init__(
        self, fire_callback: Callable[[], None], max_consecutive_failures: int = MAX_CONSECUTIVE_FAILURES,
    ):
        self._fire_callback = fire_callback
        self._last_watchdog_tick = maka_control_timestamp_ms()
        self._consecutive_watchdog_timeouts = 0
        self._max_consecutive_failures = max_consecutive_failures

    def reset_timer(self) -> None:
        # Update the watchdog's last time of a received message, regardless of the message that was
        # received.
        self._last_watchdog_tick = maka_control_timestamp_ms()

    async def check(self) -> None:
        if maka_control_timestamp_ms() - self._last_watchdog_tick > TIMEOUT_MS:
            self._consecutive_watchdog_timeouts += 1

            LOG.warning("Duro Inertial watchdog timeout detected")
            try:
                self._fire_callback()
            except SystemExit:
                return

            if self._consecutive_watchdog_timeouts >= self._max_consecutive_failures:
                # We're failing too much, this is beyond what the watchdog reconnects can
                # manage. Fail loudly and let an alert fire from a higher level process manager
                # for help from a human.
                LOG.error(
                    f"Failed too many consecutive watchdog checks ({self._consecutive_watchdog_timeouts}). Exiting."
                )
                return
        else:
            self._consecutive_watchdog_timeouts = 0

    def run_forever(self) -> None:
        LOG.debug("Starting Duro Inertial Watchdog Thread...")
        asyncio.run_coroutine_threadsafe(
            dispatch(name="Duro Inertial Watchdog", callback=self.check, poll_ms=POLL_MS), get_event_loop_by_name()
        ).result()
        assert False, "Duro Inertial Watchdog Should Not Exit"

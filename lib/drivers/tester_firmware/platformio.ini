; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[uno]
platform = atmelavr
board = uno
framework = arduino

[env:pulczar_simulator]
extends = uno
src_filter= -<*> +<pulczar_simulator.cpp>

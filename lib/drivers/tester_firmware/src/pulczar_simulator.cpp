#include <Arduino.h>

#define FLOW_OUT 11
#define FLOW_MONITOR A1
#define WP_OUT 10 // Water Protect
#define WP_MONITOR A0

#define CAM_SYNC_OUT 9 // pps signal to synchronize clocks

#define LaserControlBoardPowerOut 12
#define WP_IN 13
#define LI_IN 3 // Laser Intensity
#define LF_IN 2 // Laser Fire

#define OPTO_CAMERA_OUT 6 // naming fucked. relative to camera, not arduino.
#define OPTO_CAMERA_IN 5  // naming fucked. relative to camera, not arduino.

uint8_t ADDR[3] = {4, 7, 8};

uint8_t flow_freq = 0; // Hz (must be 0 or gte 1)
float flow_period = 0; // / flowFreq; // milli seconds

uint8_t cam_fps = 0;  // Hz
float cam_period = 0; // in ms

byte buffer[25];
uint8_t idx = 0;

const uint8_t NONE = 255;
const uint8_t ALL_LOW = 0;
const uint8_t ALL_HIGH = 1;
const uint8_t HIGH_WP_LOW_ELSE = 2;
const uint8_t SET_ADDR = 3;
const uint8_t ALL_INPUT = 4;
const uint8_t CAMERA_10_FPS = 5;

int curr_test = NONE;

uint8_t curr_addr =
    0x01; // sets the address. the addr pins set the values of different bits, so addr can go from 0 to 8

bool test_complete = false;

void setup() {
  Serial.begin(9600);
  while (!Serial)
    ;

  pinMode(FLOW_OUT, OUTPUT);
  pinMode(WP_OUT, OUTPUT);
  pinMode(CAM_SYNC_OUT, OUTPUT);
  pinMode(LaserControlBoardPowerOut, OUTPUT);
  pinMode(OPTO_CAMERA_IN, OUTPUT); // don't freak, this is correct

  pinMode(OPTO_CAMERA_OUT, INPUT); // don't freak, this is correct
  pinMode(WP_MONITOR, INPUT);
  pinMode(FLOW_MONITOR, INPUT);

  pinMode(WP_IN, INPUT);
  pinMode(LI_IN, INPUT);
  pinMode(LF_IN, INPUT);

  pinMode(ADDR[0], OUTPUT);
  pinMode(ADDR[1], OUTPUT);
  pinMode(ADDR[2], OUTPUT);

  digitalWrite(WP_OUT, LOW); // default pull down to prevent back power
  digitalWrite(FLOW_OUT, LOW);
  digitalWrite(LaserControlBoardPowerOut, LOW);
}

void loop() {
  if (Serial.available()) {
    Serial.readBytes(buffer, 3);

    switch (buffer[0]) {
    case ALL_LOW:
      curr_test = ALL_LOW;
      test_complete = false;
      break;
    case ALL_HIGH:
      curr_test = ALL_HIGH;
      test_complete = false;
      break;
    case HIGH_WP_LOW_ELSE:
      curr_test = HIGH_WP_LOW_ELSE;
      test_complete = false;
      break;
    case SET_ADDR:
      curr_addr = buffer[1];
      Serial.write(curr_addr);
    case ALL_INPUT:
      curr_test = ALL_INPUT;
      test_complete = false;
      break;
    case CAMERA_10_FPS:
      curr_test = CAMERA_10_FPS;
      test_complete = false;
      break;
    case NONE:
      curr_test = NONE;
      break;
    default:
      break;
    }

    Serial.write(curr_test);
    Serial.flush();
  }

  switch (curr_addr) {
  case 0x01:
    digitalWrite(ADDR[2], LOW);
    digitalWrite(ADDR[1], LOW);
    digitalWrite(ADDR[0], LOW);
    break;
  case 0x02:
    digitalWrite(ADDR[2], LOW);
    digitalWrite(ADDR[1], LOW);
    digitalWrite(ADDR[0], HIGH);
    break;
  case 0x03:
    digitalWrite(ADDR[2], LOW);
    digitalWrite(ADDR[1], HIGH);
    digitalWrite(ADDR[0], LOW);
    break;
  case 0x04:
    digitalWrite(ADDR[2], LOW);
    digitalWrite(ADDR[1], HIGH);
    digitalWrite(ADDR[0], HIGH);
    break;
  case 0x05:
    digitalWrite(ADDR[2], HIGH);
    digitalWrite(ADDR[1], LOW);
    digitalWrite(ADDR[0], LOW);
    break;
  case 0x06:
    digitalWrite(ADDR[2], HIGH);
    digitalWrite(ADDR[1], LOW);
    digitalWrite(ADDR[0], HIGH);
    break;
  case 0x07:
    digitalWrite(ADDR[2], HIGH);
    digitalWrite(ADDR[1], HIGH);
    digitalWrite(ADDR[0], LOW);
    break;
  case 0x08:
    digitalWrite(ADDR[2], HIGH);
    digitalWrite(ADDR[1], HIGH);
    digitalWrite(ADDR[0], HIGH);
    break;
  }

  if (curr_test == NONE) {
    digitalWrite(WP_OUT, LOW);
    flow_freq = 0;
    flow_period = 0;

    cam_fps = 0;
    cam_period = 0;
    digitalWrite(LaserControlBoardPowerOut, LOW);
  }

  if (curr_test == ALL_LOW && !test_complete) {
    digitalWrite(WP_OUT, HIGH); // inverse logic
    flow_freq = 0;
    flow_period = 0;

    cam_fps = 0;
    cam_period = 0;
    digitalWrite(LaserControlBoardPowerOut, LOW);
    test_complete = true;
  }

  if (curr_test == ALL_HIGH && !test_complete) {
    digitalWrite(WP_OUT, LOW); // inverse logic
    flow_freq = 10;
    flow_period = 1000 / flow_freq;

    cam_fps = 0;
    cam_period = 0;
    digitalWrite(LaserControlBoardPowerOut, HIGH);
    test_complete = true;
  }

  if (curr_test == HIGH_WP_LOW_ELSE && !test_complete) {
    digitalWrite(WP_OUT, LOW); // inverse logic
    flow_freq = 0;
    flow_period = 0;

    cam_fps = 0;
    cam_period = 0;
    digitalWrite(LaserControlBoardPowerOut, LOW);
    test_complete = true;
  }

  if (curr_test == ALL_INPUT && !test_complete) {
    uint8_t wp_analog_read = map(analogRead(WP_MONITOR), 0, 1023, 0, 255);
    Serial.write(wp_analog_read);
    uint8_t flow_read = map(analogRead(FLOW_MONITOR), 0, 1023, 0, 255);
    Serial.write(flow_read);
    test_complete = true;
  }

  if (curr_test == CAMERA_10_FPS && !test_complete) {
    digitalWrite(WP_OUT, LOW);
    flow_freq = 0;
    flow_period = 0;

    cam_fps = 5;
    cam_period = 1000 / cam_fps;
    digitalWrite(LaserControlBoardPowerOut, LOW);
  }

  if ((millis() % (int)flow_period) < (int)(flow_period / 2)) {
    digitalWrite(FLOW_OUT, HIGH);
  } else {
    digitalWrite(FLOW_OUT, LOW);
  }

  if ((millis() % (int)cam_period) < (int)(cam_period / 2)) {
    digitalWrite(OPTO_CAMERA_IN, HIGH);
  } else {
    digitalWrite(OPTO_CAMERA_IN, LOW);
  }

  if ((millis() % 1000) < 100) {
    digitalWrite(CAM_SYNC_OUT, HIGH);
  } else {
    digitalWrite(CAM_SYNC_OUT, LOW);
  }
}

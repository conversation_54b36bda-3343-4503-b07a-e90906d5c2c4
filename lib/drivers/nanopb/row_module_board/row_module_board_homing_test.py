import asyncio
from argparse import ArgumentParser
from typing import List

import numpy as np

import lib.common.logging
from lib.common.error import MakaException
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.row_module_board.row_module_board_connector import RowModuleBoardConnector
from lib.drivers.psoc_usb.psoc_m_usb import PsocMUSBConnector

LOG = lib.common.logging.get_logger(__name__)

PROFILE_VELOCITY = 590000


def _print_results(max_list: List[int], name: str) -> None:
    np_max_list = np.array(max_list)
    LOG.info(
        f"{name}: Mean: {np_max_list.mean()}, Variance: {np_max_list.var()}, Std Dev: {np_max_list.std()}, Min: {np.amin(np_max_list)}, Max: {np.amax(np_max_list)}"
    )


async def _scanner_test(
    board: RowModuleBoardConnector, scanner_id: int, pan_id: int, tilt_id: int, iterations: int, use_switch: bool
) -> None:
    await board.clear_config()
    await board.scanner_config(scanner_id, pan_id, tilt_id, PROFILE_VELOCITY, 250, 50)
    pan_max = []
    tilt_max = []
    error_list = []

    for i in range(iterations):
        try:
            if use_switch:
                await board.scanner_boot_switch(scanner_id, PROFILE_VELOCITY, 0, 20000, 1000, 10)
            else:
                await board.scanner_boot(scanner_id, PROFILE_VELOCITY, 0, 20000, 1000, 1000)
            await board.scanner_go_to(scanner_id, (5000, 5000), (PROFILE_VELOCITY, PROFILE_VELOCITY), True)
            limits = await board.scanner_get_limits(scanner_id)
            pan_max.append(limits[0][1])
            tilt_max.append(limits[1][1])
            await board.scanner_stop(scanner_id)
        except MakaException:
            error_list.append(i)

    LOG.info(f"Error List: {len(error_list)} {error_list}")
    _print_results(pan_max, "Pan")
    _print_results(tilt_max, "Tilt")


async def _test(serial_port: str, pan_id: int, tilt_id: int, iterations: int) -> None:
    connector = PsocMUSBConnector(serial_port, asyncio.get_event_loop())
    await connector.open()
    board = RowModuleBoardConnector(connector)
    await _scanner_test(board, 1, pan_id, tilt_id, iterations, False)
    await _scanner_test(board, 1, pan_id, tilt_id, iterations, True)


def main() -> None:
    parser = ArgumentParser("Row Module Board Tester")
    parser.add_argument("serial_port", type=str)
    parser.add_argument("--pan", type=int, default=1)
    parser.add_argument("--tilt", type=int, default=2)
    parser.add_argument("-n", type=int, default=10)
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _test(args.serial_port, args.pan, args.tilt, args.n)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

import time
from typing import TYPE_CHECKING, Any, Tuple

import generated.lib.drivers.nanopb.proto.can_open_pb2 as can_pb
import generated.lib.drivers.nanopb.proto.dawg_pb2 as dawg_pb
import generated.lib.drivers.nanopb.proto.epos_pb2 as epos_pb
import generated.lib.drivers.nanopb.proto.row_module_pb2 as row_module_pb
import generated.lib.drivers.nanopb.proto.scanner_pb2 as scanner_pb
import generated.lib.drivers.nanopb.proto.servo_pb2 as servo_pb
import lib.common.logging
from generated.lib.drivers.nanopb.proto.row_module_board_pb2 import Reply, Request
from lib.common.error import MakaException
from lib.drivers.nanopb.nano_connector import NanoPbConnector
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

LOG = lib.common.logging.get_logger(__name__)


class RowModuleBoardException(MakaException):
    pass


if TYPE_CHECKING:
    GoToModeType = servo_pb.GoToModeValue
else:
    GoToModeType = servo_pb.GoToMode


class RowModuleBoardConnector:
    def __init__(self, protocol_connector: MakaProtocolConnector):
        self._nano_connector: NanoPbConnector[Request, Reply] = NanoPbConnector(protocol_connector, Request, Reply)

    async def stop(self) -> None:
        await self._nano_connector.stop()

    async def ping(self) -> float:
        payload = 42
        request = self._nano_connector.create_request()
        request.ping.x = 42
        start_time = time.time()
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        duration = time.time() - start_time
        assert reply.WhichOneof("reply") == "pong"
        assert reply.pong.x == payload
        return duration

    async def _create_scanner_request(self, scanner_id: int, **kwargs: Any) -> Request:
        return self._nano_connector.create_request(
            row_module=row_module_pb.Request(
                scanner=row_module_pb.Scanner_Request(scanner_id=scanner_id, request=scanner_pb.Request(**kwargs))
            )
        )

    @staticmethod
    def _is_scanner_reply(reply: Reply) -> bool:
        return bool(reply.WhichOneof("reply") == "row_module" and reply.row_module.WhichOneof("reply") == "scanner")

    """ Row Module API """

    async def hard_reset(self) -> None:
        request = self._nano_connector.create_request(
            row_module=row_module_pb.Request(reset=row_module_pb.Reset_Request())
        )
        # This will break the connection
        await self._nano_connector.send_request(request)

    async def clear_config(self) -> None:
        request = self._nano_connector.create_request(
            row_module=row_module_pb.Request(clear=row_module_pb.Clear_Config_Request())
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "row_module" or reply.row_module.WhichOneof("reply") != "ack":
            raise RowModuleBoardException("Failed to Clear Configuration")

    async def scanner_get_gimbal_SDO(self, scanner_id: int, index: int, subindex: int) -> Tuple[int, int]:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(
                    epos=epos_pb.Request(
                        can=can_pb.Request(sdo_upload=can_pb.SDO_Upload_Request(index=index, subindex=subindex))
                    )
                ),
                tilt=servo_pb.Request(
                    epos=epos_pb.Request(
                        can=can_pb.Request(sdo_upload=can_pb.SDO_Upload_Request(index=index, subindex=subindex))
                    )
                ),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "epos"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "epos"
            or reply.row_module.scanner.gimbal.pan.epos.WhichOneof("reply") != "can"
            or reply.row_module.scanner.gimbal.tilt.epos.WhichOneof("reply") != "can"
            or reply.row_module.scanner.gimbal.pan.epos.can.WhichOneof("reply") != "msg"
            or reply.row_module.scanner.gimbal.tilt.epos.can.WhichOneof("reply") != "msg"
        ):
            raise RowModuleBoardException(
                f"Failed to get scanner gimbal SDO {scanner_id}: Index: {index} Subindex: {subindex} reply: {reply}"
            )
        return (
            int.from_bytes(reply.row_module.scanner.gimbal.pan.epos.can.msg.sdo.data, byteorder="little"),
            int.from_bytes(reply.row_module.scanner.gimbal.tilt.epos.can.msg.sdo.data, byteorder="little"),
        )

    async def scanner_set_gimbal_SDO(self, scanner_id: int, index: int, subindex: int, value: int) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(
                    epos=epos_pb.Request(
                        can=can_pb.Request(
                            sdo_download=can_pb.SDO_Download_Request(index=index, subindex=subindex, value=value)
                        )
                    )
                ),
                tilt=servo_pb.Request(
                    epos=epos_pb.Request(
                        can=can_pb.Request(
                            sdo_download=can_pb.SDO_Download_Request(index=index, subindex=subindex, value=value)
                        )
                    )
                ),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "epos"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "epos"
            or reply.row_module.scanner.gimbal.pan.epos.WhichOneof("reply") != "can"
            or reply.row_module.scanner.gimbal.tilt.epos.WhichOneof("reply") != "can"
            or reply.row_module.scanner.gimbal.pan.epos.can.WhichOneof("reply") != "ack"
            or reply.row_module.scanner.gimbal.tilt.epos.can.WhichOneof("reply") != "ack"
        ):
            raise RowModuleBoardException(
                f"Failed to set scanner gimbal SDO {scanner_id}: Index: {index} Subindex: {subindex} reply: {reply}"
            )

    async def scanner_gimbal_enable(self, scanner_id: int) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(epos=epos_pb.Request(enable=epos_pb.Enable_Request())),
                tilt=servo_pb.Request(epos=epos_pb.Request(enable=epos_pb.Enable_Request())),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "epos"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "epos"
            or reply.row_module.scanner.gimbal.pan.epos.WhichOneof("reply") != "ack"
            or reply.row_module.scanner.gimbal.tilt.epos.WhichOneof("reply") != "ack"
        ):
            raise RowModuleBoardException(f"Failed to scanner gimbal enable {scanner_id} reply: {reply}")

    async def scanner_get_gimbal_error_codes(self, scanner_id: int) -> Tuple[int, int]:
        return await self.scanner_get_gimbal_SDO(scanner_id, 0x603F, 0x00)

    async def scanner_get_gimbal_control_words(self, scanner_id: int) -> Tuple[int, int]:
        return await self.scanner_get_gimbal_SDO(scanner_id, 0x6040, 0x00)

    async def scanner_set_gimbal_control_words(self, scanner_id: int, value: int) -> None:
        await self.scanner_set_gimbal_SDO(scanner_id, 0x6040, 0x00, value)

    async def scanner_clear_gimbal_fault(self, scanner_id: int) -> None:
        await self.scanner_set_gimbal_control_words(scanner_id, 0b10101111)
        await self.scanner_gimbal_enable(scanner_id)

    async def scanner_get_gimbal_status_words(self, scanner_id: int) -> Tuple[int, int]:
        return await self.scanner_get_gimbal_SDO(scanner_id, 0x6041, 0x00)

    async def scanner_boot(
        self, scanner_id: int, profile_velocity: int, min_position: int, max_position: int, step_size: int, offset: int
    ) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            boot=scanner_pb.Boot_Request(
                pan_params=epos_pb.Home_Params(
                    profile_velocity=profile_velocity,
                    max_position=max_position,
                    min_position=min_position,
                    hard_stop=epos_pb.Hard_Home_Params(step_size=step_size, offset=offset),
                ),
                tilt_params=epos_pb.Home_Params(
                    profile_velocity=profile_velocity,
                    max_position=max_position,
                    min_position=min_position,
                    hard_stop=epos_pb.Hard_Home_Params(step_size=step_size, offset=offset),
                ),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=10000)
        if not self._is_scanner_reply(reply) or reply.row_module.scanner.WhichOneof("reply") != "ack":
            raise RowModuleBoardException(f"Failed to boot scanner: {scanner_id}")

    async def scanner_boot_switch(
        self,
        scanner_id: int,
        profile_velocity: int,
        min_position: int,
        max_position: int,
        step_size: int,
        threshold_step: int,
    ) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            boot=scanner_pb.Boot_Request(
                pan_params=epos_pb.Home_Params(
                    profile_velocity=profile_velocity,
                    max_position=max_position,
                    min_position=min_position,
                    limit_switch=epos_pb.Switch_Home_Params(step_size=step_size, threshold_step=threshold_step),
                ),
                tilt_params=epos_pb.Home_Params(
                    profile_velocity=profile_velocity,
                    max_position=max_position,
                    min_position=min_position,
                    limit_switch=epos_pb.Switch_Home_Params(step_size=step_size, threshold_step=threshold_step),
                ),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=10000)
        if not self._is_scanner_reply(reply) or reply.row_module.scanner.WhichOneof("reply") != "ack":
            raise RowModuleBoardException(f"Failed to boot scanner: {scanner_id}")

    async def scanner_stop(self, scanner_id: int) -> None:
        request = await self._create_scanner_request(scanner_id=scanner_id, stop=scanner_pb.Stop_Request())
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if not self._is_scanner_reply(reply) or reply.row_module.scanner.WhichOneof("reply") != "ack":
            raise RowModuleBoardException(f"Failed to stop scanner: {scanner_id}")

    async def scanner_laser(self, scanner_id: int, on: bool) -> None:
        request = await self._create_scanner_request(scanner_id=scanner_id, laser=scanner_pb.Laser_Request(on=on))
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if not self._is_scanner_reply(reply) or reply.row_module.scanner.WhichOneof("reply") != "ack":
            raise RowModuleBoardException(f"Failed to turn laser on/off scanner: {scanner_id}")

    async def scanner_intensity(self, scanner_id: int, intensity: int) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id, intensity=scanner_pb.Intensity_Request(intensity=intensity)
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if not self._is_scanner_reply(reply) or reply.row_module.scanner.WhichOneof("reply") != "ack":
            raise RowModuleBoardException(f"Failed to change intensity of scanner: {scanner_id}")

    async def scanner_config(
        self,
        scanner_id: int,
        pan_node_id: int,
        tilt_node_id: int,
        max_profile_velocity_mrpm: int,
        settle_timeout: int,
        settle_window: int,
    ) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(
                    config=servo_pb.Config_Request(
                        node_id=pan_node_id,
                        config=servo_pb.Config(
                            max_profile_velocity=max_profile_velocity_mrpm,
                            settle_window=settle_window,
                            settle_timeout=settle_timeout,
                        ),
                    )
                ),
                tilt=servo_pb.Request(
                    config=servo_pb.Config_Request(
                        node_id=tilt_node_id,
                        config=servo_pb.Config(
                            max_profile_velocity=max_profile_velocity_mrpm,
                            settle_window=settle_window,
                            settle_timeout=settle_timeout,
                        ),
                    )
                ),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "ack"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "ack"
        ):
            raise RowModuleBoardException(f"Failed to configure scanner: {scanner_id}")

    async def scanner_go_to(
        self, scanner_id: int, position: Tuple[int, int], velocity: Tuple[int, int], await_settle: bool
    ) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(
                    go_to=servo_pb.Go_To_Request(position=position[0], velocity=velocity[0], await_settle=await_settle)
                ),
                tilt=servo_pb.Request(
                    go_to=servo_pb.Go_To_Request(position=position[1], velocity=velocity[1], await_settle=await_settle)
                ),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "ack"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "ack"
        ):
            pan_code, tilt_code = await self.scanner_get_gimbal_error_codes(scanner_id)
            raise RowModuleBoardException(
                f"Failed to go to scanner {scanner_id} position: {position} with velocity: {velocity} reply: {reply}, Error Codes: Pan: {pan_code}, Tilt: {tilt_code}"
            )

    async def scanner_go_to_delta(
        self, scanner_id: int, delta_position: Tuple[int, int], velocity: Tuple[int, int], mode: GoToModeType
    ) -> Tuple[int, int]:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(
                    delta=servo_pb.Go_To_Delta_Request(
                        delta_position=delta_position[0], velocity=velocity[0], mode=mode
                    )
                ),
                tilt=servo_pb.Request(
                    delta=servo_pb.Go_To_Delta_Request(
                        delta_position=delta_position[1], velocity=velocity[1], mode=mode
                    )
                ),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "pos"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "pos"
        ):
            pan_code, tilt_code = await self.scanner_get_gimbal_error_codes(scanner_id)
            raise RowModuleBoardException(
                f"Failed to go to delta scanner {scanner_id} position: {delta_position} with velocity: {velocity} reply: {reply}, Error Codes: Pan: {pan_code}, Tilt: {tilt_code}"
            )
        return reply.row_module.scanner.gimbal.pan.pos.position, reply.row_module.scanner.gimbal.tilt.pos.position

    async def scanner_go_to_delta_follow(
        self,
        scanner_id: int,
        delta_position: Tuple[int, int],
        velocity: Tuple[int, int],
        follow_velocity_vector: Tuple[int, int],
        follow_velocity_mrpm: Tuple[int, int],
        interval_sleep_time_ms: int,
        mode: GoToModeType,
    ) -> Tuple[int, int]:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(
                    follow=servo_pb.Go_To_Delta_Follow_Request(
                        delta=servo_pb.Go_To_Delta_Request(
                            delta_position=delta_position[0], velocity=velocity[0], mode=mode
                        ),
                        follow_velocity_vector=follow_velocity_vector[0],
                        follow_velocity_mrpm=follow_velocity_mrpm[0],
                        interval_sleep_time_ms=interval_sleep_time_ms,
                    )
                ),
                tilt=servo_pb.Request(
                    follow=servo_pb.Go_To_Delta_Follow_Request(
                        delta=servo_pb.Go_To_Delta_Request(
                            delta_position=delta_position[1], velocity=velocity[1], mode=mode
                        ),
                        follow_velocity_vector=follow_velocity_vector[1],
                        follow_velocity_mrpm=follow_velocity_mrpm[1],
                        interval_sleep_time_ms=interval_sleep_time_ms,
                    )
                ),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "pos"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "pos"
        ):
            raise RowModuleBoardException(
                f"Failed to go to delta follow scanner {scanner_id} position: {delta_position} with velocity: {velocity} reply: {reply}"
            )
        return reply.row_module.scanner.gimbal.pan.pos.position, reply.row_module.scanner.gimbal.tilt.pos.position

    async def scanner_get_pos_vel(self, scanner_id: int) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(epos=epos_pb.Request(pos_vel=epos_pb.Get_Pos_Vel_Request())),
                tilt=servo_pb.Request(epos=epos_pb.Request(pos_vel=epos_pb.Get_Pos_Vel_Request())),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "epos"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "epos"
            or reply.row_module.scanner.gimbal.pan.epos.WhichOneof("reply") != "pos_vel"
            or reply.row_module.scanner.gimbal.pan.epos.WhichOneof("reply") != "pos_vel"
        ):
            raise RowModuleBoardException(f"Failed to get scanner {scanner_id} position and velocity")
        return (
            (
                reply.row_module.scanner.gimbal.pan.epos.pos_vel.position,
                reply.row_module.scanner.gimbal.tilt.epos.pos_vel.position,
            ),
            (
                reply.row_module.scanner.gimbal.pan.epos.pos_vel.velocity,
                reply.row_module.scanner.gimbal.tilt.epos.pos_vel.velocity,
            ),
        )

    async def scanner_go_to_pan(self, scanner_id: int, position: int, velocity: int, await_settle: bool) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(
                    go_to=servo_pb.Go_To_Request(position=position, velocity=velocity, await_settle=await_settle)
                ),
                tilt=servo_pb.Request(
                    limit=servo_pb.Get_Limits_Request()
                ),  # Limits is used as a placeholder for do nothing
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "ack"
        ):
            raise RowModuleBoardException(
                f"Failed to go to scanner {scanner_id} Pan position: {position} with velocity: {velocity}"
            )

    async def scanner_go_to_tilt(self, scanner_id: int, position: int, velocity: int, await_settle: bool) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                tilt=servo_pb.Request(
                    go_to=servo_pb.Go_To_Request(position=position, velocity=velocity, await_settle=await_settle)
                ),
                pan=servo_pb.Request(
                    limit=servo_pb.Get_Limits_Request()
                ),  # Limits is used as a placeholder for do nothing
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "ack"
        ):
            raise RowModuleBoardException(
                f"Failed to go to scanner {scanner_id} Tilt position: {position} with velocity: {velocity}"
            )

    async def scanner_get_limits(self, scanner_id: int) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(limit=servo_pb.Get_Limits_Request()),
                tilt=servo_pb.Request(limit=servo_pb.Get_Limits_Request()),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "limit"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "limit"
        ):
            raise RowModuleBoardException(f"Failed to get limits from scanner {scanner_id}")
        return (
            (reply.row_module.scanner.gimbal.pan.limit.min, reply.row_module.scanner.gimbal.pan.limit.max),
            (reply.row_module.scanner.gimbal.tilt.limit.min, reply.row_module.scanner.gimbal.tilt.limit.max),
        )

    async def dawg_pet(self) -> None:
        request = self._nano_connector.create_request(
            row_module=row_module_pb.Request(dawg=dawg_pb.Request(pet=dawg_pb.Pet_Request()))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "row_module"
            or reply.row_module.WhichOneof("reply") != "dawg"
            or reply.row_module.dawg.WhichOneof("reply") != "ack"
        ):
            raise RowModuleBoardException("Failed to pet Dawg")

    async def dawg_arm(self, armed: bool) -> None:
        request = self._nano_connector.create_request(
            row_module=row_module_pb.Request(dawg=dawg_pb.Request(arm=dawg_pb.Arm_Request(armed=armed)))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "row_module"
            or reply.row_module.WhichOneof("reply") != "dawg"
            or reply.row_module.dawg.WhichOneof("reply") != "ack"
        ):
            raise RowModuleBoardException("Failed to arm Dawg")

    async def dawg_config(self, timeout_ms: int) -> None:
        request = self._nano_connector.create_request(
            row_module=row_module_pb.Request(dawg=dawg_pb.Request(config=dawg_pb.Config_Request(timeout_ms=timeout_ms)))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "row_module"
            or reply.row_module.WhichOneof("reply") != "dawg"
            or reply.row_module.dawg.WhichOneof("reply") != "ack"
        ):
            raise RowModuleBoardException("Failed to Config Dawg")

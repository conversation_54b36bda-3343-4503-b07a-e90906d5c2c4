import asyncio
import time
from argparse import ArgumentParser

import numpy as np

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.row_module_board.row_module_board_connector import RowModuleBoardConnector
from lib.drivers.psoc_usb.psoc_m_usb import PsocMUSBConnector

LOG = lib.common.logging.get_logger(__name__)


async def _ping_test(board: RowModuleBoardConnector) -> None:
    times = np.array([await board.ping() for x in range(1000)])
    LOG.info(
        f"Nano Ping: mean: {times.mean()}, 95th %tile: {np.percentile(times, 95)}, 99th %tile: {np.percentile(times, 99)}"
    )


async def _scanner_test(board: RowModuleBoardConnector, scanner_id: int, pan_id: int, tilt_id: int) -> None:
    await board.clear_config()
    await board.scanner_config(scanner_id, pan_id, tilt_id, 590000, 1000, 50)
    await board.dawg_config(1000)
    await board.scanner_boot(scanner_id, 590000, 0, 30000, 1000, 1000)
    await board.scanner_laser(scanner_id, True)

    await board.dawg_pet()
    await board.dawg_arm(True)
    await board.scanner_intensity(1, 0)

    await asyncio.sleep(2)

    await board.dawg_pet()
    await board.scanner_intensity(1, 30000)

    await asyncio.sleep(0.5)

    print(await board.scanner_get_gimbal_control_words(scanner_id))
    print(await board.scanner_get_gimbal_status_words(scanner_id))
    print("Clearing Faults")
    await board.scanner_clear_gimbal_fault(scanner_id)
    print(await board.scanner_get_gimbal_control_words(scanner_id))
    print(await board.scanner_get_gimbal_status_words(scanner_id))

    limit_pan, limit_tilt = await board.scanner_get_limits(scanner_id)
    print(f"Limits: {(limit_pan, limit_tilt)}")

    await board.scanner_go_to(scanner_id, (limit_pan[0], limit_tilt[0]), (590000, 590000), True)
    await asyncio.sleep(1)
    await board.scanner_go_to(scanner_id, (limit_pan[1], limit_tilt[1]), (590000, 590000), True)

    print("Successfully went to limits")

    await board.dawg_arm(False)

    for i in range(10000000):
        start_time = time.time()
        if i % 1000 == 0:
            print(f"Steps: {i}")
        val = (i * 100) % 9000
        await board.scanner_go_to(scanner_id, (val, val), (590000, 590000), False)
        to_wait = 0.005 - (time.time() - start_time)
        if to_wait > 0:
            await asyncio.sleep(to_wait)
    await board.scanner_laser(scanner_id, False)

    await asyncio.sleep(1)

    print(await board.scanner_get_gimbal_error_codes(scanner_id))

    await board.scanner_stop(scanner_id)


async def _test(serial_port: str, pan_id: int, tilt_id: int) -> None:
    connector = PsocMUSBConnector(serial_port, asyncio.get_event_loop())
    await connector.open()
    board = RowModuleBoardConnector(connector)
    await _ping_test(board)
    await _scanner_test(board, 1, pan_id, tilt_id)


def main() -> None:
    parser = ArgumentParser("Row Module Board Tester")
    parser.add_argument("serial_port", type=str)
    parser.add_argument("--pan", type=int, default=1)
    parser.add_argument("--tilt", type=int, default=2)
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _test(args.serial_port, args.pan, args.tilt)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

import time

import lib.common.logging
from firmware.release.firmware_release_manager import FirmwareVersion
from generated.lib.drivers.nanopb.proto.strobe_control_board_pb2 import Reply, Request
from generated.lib.drivers.nanopb.proto.version_pb2 import Reset_Request, Version_Request
from lib.common.error import MakaException
from lib.common.generation import is_reaper, is_slayer
from lib.drivers.errors.error import RetryableMakaDeviceException
from lib.drivers.nanopb.bootloadable_connector import BootloadableConnector
from lib.drivers.nanopb.nano_connector import NanoPbConnector
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

LOG = lib.common.logging.get_logger(__name__)


def get_strobe_control_ip() -> str:
    if is_slayer():
        return "*********"
    elif is_reaper():
        return "*********"
    else:
        return "*********"


def get_strobe_control_bootloader_ip() -> str:
    if is_slayer() or is_reaper():
        return "nonexistent"
    else:
        return "**********"


STROBE_CONTROL_PORT = 4243
STROBE_CONTROL_IP = get_strobe_control_ip()
STROBE_CONTROL_BOOTLOADER_IP = get_strobe_control_bootloader_ip()


class StrobeControlBoardException(MakaException):
    pass


class StrobeControlBoardConnector(BootloadableConnector):
    def __init__(self, protocol_connector: MakaProtocolConnector):
        self._nano_connector: NanoPbConnector[Request, Reply] = NanoPbConnector(protocol_connector, Request, Reply)

    async def stop(self) -> None:
        await self._nano_connector.stop()

    async def ping(self) -> float:
        payload = 42
        request = self._nano_connector.create_request()
        request.ping.x = 42
        start_time = time.time()
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        duration = time.time() - start_time
        assert reply.WhichOneof("reply") == "pong"
        assert reply.pong.x == payload
        return duration

    async def hard_reset(self) -> None:
        request = self._nano_connector.create_request(reset=Reset_Request())
        # This will break the connection
        await self._nano_connector.send_request(request)

    async def get_version(self) -> FirmwareVersion:
        request = self._nano_connector.create_request(version=Version_Request())
        try:
            reply: Reply = await self._nano_connector.send_request_await_reply(request)
        except RetryableMakaDeviceException:
            raise StrobeControlBoardException("Board Failed to Reply to Version Request")
        if reply.WhichOneof("reply") != "version":
            raise StrobeControlBoardException("Failed to Get Version")
        return FirmwareVersion(reply.version.major, reply.version.minor, 0)

    async def strobe_control(self, exposure_us: int, period_us: int, targets_per_predict_ratio: int) -> None:
        request = self._nano_connector.create_request()
        request.strobe_control.exposure_us = exposure_us
        request.strobe_control.period_us = period_us
        request.strobe_control.targets_per_predict_ratio = targets_per_predict_ratio
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert reply.WhichOneof("reply") == "strobe_control"
        if not reply.strobe_control.ok:
            raise StrobeControlBoardException("Board rejected strobe control request (ok=false)")

    async def camera_power_control(self, camera_id: int, power_on: bool) -> None:
        request = self._nano_connector.create_request()
        request.camera_power_control.camera_id = camera_id
        request.camera_power_control.power_on = power_on
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert reply.WhichOneof("reply") == "camera_power_control"
        if not reply.camera_power_control.ok:
            raise StrobeControlBoardException("Board rejected camera power control request (ok=false)")

import asyncio
from argparse import ArgumentParser

import numpy as np

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.strobe_control_board.strobe_control_board_connector import (
    StrobeControlBoardConnector,
    StrobeControlBoardException,
)
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)


async def _ping_test(board: StrobeControlBoardConnector) -> None:
    times = np.array([await board.ping() for x in range(1000)])
    LOG.info(
        f"Nano Ping: mean: {times.mean()}, 95th %tile: {np.percentile(times, 95)}, 99th %tile: {np.percentile(times, 99)}"
    )


async def _strobe_control_test(board: StrobeControlBoardConnector) -> None:
    await board.strobe_control(1000, 10000, 5)

    try:
        await board.strobe_control(1, 10000, 5)
        assert False, "Expected error due to exposure_us"
    except StrobeControlBoardException:
        pass

    try:
        await board.strobe_control(1000, 100, 5)
        assert False, "Expected error due to period_us"
    except StrobeControlBoardException:
        pass

    try:
        await board.strobe_control(1000, 10000, 100)
        assert False, "Expected error due to targets_per_predict_ratio"
    except StrobeControlBoardException:
        pass


async def _camera_power_control_test(board: StrobeControlBoardConnector) -> None:
    await board.camera_power_control(0, False)
    await board.camera_power_control(1, True)
    await board.camera_power_control(2, False)
    await board.camera_power_control(3, True)

    try:
        await board.camera_power_control(4, True)
        assert False, "Expected error due to camera_id"
    except StrobeControlBoardException:
        pass


async def _test(host: str, port: int, power_control_test: bool) -> None:
    connector = PsocMEthernetConnector(host, port, asyncio.get_event_loop())
    await connector.open()
    board = StrobeControlBoardConnector(connector)
    await _ping_test(board)
    await _strobe_control_test(board)
    if power_control_test:
        await _camera_power_control_test(board)


def main() -> None:
    parser = ArgumentParser("Strobe Control Board Tester")
    parser.add_argument("--host", type=str, default="*********")
    parser.add_argument("--port", type=int, default=4243)
    parser.add_argument("--power-control-test", action="store_true")
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _test(args.host, args.port, args.power_control_test)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

import ctypes
import time
from dataclasses import dataclass, field
from enum import IntEnum, auto
from typing import List, Optional, Protocol, Tuple

from typing_extensions import Self

import generated.lib.drivers.nanopb.proto.gps_pb2 as gps_pb2
import lib.common.logging
from firmware.release.firmware_release_manager import FirmwareVersion
from generated.lib.drivers.nanopb.proto.benjamin_gps_board_pb2 import Reply, Request
from generated.lib.drivers.nanopb.proto.hwinfo_pb2 import BoardVersionRequest
from generated.lib.drivers.nanopb.proto.hwinfo_pb2 import Request as HwInfoRequest
from lib.common.error import MakaException
from lib.common.generation import is_reaper, is_rtc, is_slayer
from lib.common.gps.spartn_messages import SPARTN
from lib.common.units.angle import Angle
from lib.drivers.nanopb.bootloadable_connector import BootloadableConnector
from lib.drivers.nanopb.nano_connector import NanoPbConnector
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

LOG = lib.common.logging.get_logger(__name__)
SPARTN_MAX_SIZE = 64
RTCM_MAX_SIZE = 64


def get_gps_ip() -> str:
    if is_slayer() or is_reaper() or is_rtc():
        return "*********"
    else:
        return "***********"


BENJAMIN_GPS_PORT = 4243
BENJAMIN_GPS_IP = get_gps_ip()
BENJAMIN_GPS_BOOTLOADER_IP = "nonexistent"


class BenjaminGPSBoardException(MakaException):
    pass


class PtpMode(IntEnum):
    # GPS board operates as PTP grandmaster, providing source for network time
    MASTER = auto()
    # GPS board synchronizes to an external PTP master for time
    SLAVE = auto()


class FixType(IntEnum):
    NO_FIX = 0
    DEAD_RECKONING_ONLY = 1
    FIX_2D = 2
    FIX_3D = 3
    GNSS_DR = 4  # GNSS + dead reckoning combined
    TIME_ONLY = 5


class CarrierSolution(IntEnum):
    NO_SOLUTION = 0
    FLOATING_SOLUTION = 1
    FIXED_SOLUTION = 2


@dataclass
class FixFlags:
    gnss_fix_ok: bool = False
    diff_soln: bool = False
    carr_soln: CarrierSolution = CarrierSolution.NO_SOLUTION

    @staticmethod
    def from_int(flags: int) -> "FixFlags":
        return FixFlags(
            gnss_fix_ok=bool(flags & 0b1),  # bit 0
            diff_soln=bool(flags & 0b10),  # bit 1
            carr_soln=CarrierSolution((flags >> 6) & 0b11),  # bits 6-7
        )


class ValueWithAccuracyWire(Protocol):
    @property
    def value(self) -> float:
        ...

    @property
    def accuracy(self) -> float:
        ...


@dataclass
class ValueWithAccuracy:
    value: float
    accuracy: float

    @classmethod
    def FromMessage(cls, packet: Optional[ValueWithAccuracyWire]) -> Optional[Self]:
        if packet is None:
            return None
        return cls(value=packet.value, accuracy=packet.accuracy)

    def __repr__(self) -> str:
        return f"{self.value}±{self.accuracy}"


class DualGpsDataWire(Protocol):
    @property
    def gnss_valid(self) -> bool:
        ...

    @property
    def diff_corrections(self) -> bool:
        ...

    @property
    def is_moving_base(self) -> bool:
        ...

    @property
    def carrier_phase(self) -> int:
        ...

    @property
    def north(self) -> Optional[ValueWithAccuracyWire]:
        ...

    @property
    def east(self) -> Optional[ValueWithAccuracyWire]:
        ...

    @property
    def down(self) -> Optional[ValueWithAccuracyWire]:
        ...

    @property
    def length(self) -> Optional[ValueWithAccuracyWire]:
        ...

    @property
    def heading(self) -> Optional[ValueWithAccuracyWire]:
        ...


@dataclass
class DualGpsData:
    gnss_valid: bool
    diff_corrections: bool
    is_moving_base: bool
    carrier_solution: CarrierSolution

    north: Optional[ValueWithAccuracy]
    east: Optional[ValueWithAccuracy]
    down: Optional[ValueWithAccuracy]
    length: Optional[ValueWithAccuracy]
    heading: Optional[ValueWithAccuracy]

    def __repr__(self) -> str:
        return f"DualGpsData(gnss_valid={self.gnss_valid}, diff_corrections={self.diff_corrections}, is_moving_base={self.is_moving_base}, carr_soln={self.carrier_solution}, north={self.north} mm, east={self.east} mm, down={self.down} mm, length={self.length} mm, self.heading={self.heading} °)"

    @classmethod
    def FromMessage(cls, packet: DualGpsDataWire) -> Self:
        return cls(
            gnss_valid=packet.gnss_valid,
            diff_corrections=packet.diff_corrections,
            is_moving_base=packet.is_moving_base,
            carrier_solution=CarrierSolution(packet.carrier_phase),
            north=ValueWithAccuracy.FromMessage(packet.north),
            east=ValueWithAccuracy.FromMessage(packet.east),
            down=ValueWithAccuracy.FromMessage(packet.down),
            length=ValueWithAccuracy.FromMessage(packet.length),
            heading=ValueWithAccuracy.FromMessage(packet.heading),
        )


@dataclass
class Position:
    have_fix: bool
    have_approx_fix: bool  # gnssFixOK
    latitude: float
    longitude: float
    height_mm: int
    num_sats: int
    hdop: float
    timestamp_ms: int
    fix_type: FixType = FixType.NO_FIX
    fix_flags: FixFlags = field(default_factory=FixFlags)
    dual_gps_data: Optional[DualGpsData] = None


class Heading:
    def __init__(
        self, *, have_fix: bool, have_approx_fix: bool, heading_deg: float, accuracy_deg: float, timestamp_ms: int
    ):
        self._have_fix = have_fix
        self._have_approx_fix = have_approx_fix
        self._heading_deg = heading_deg
        self._accuracy_deg = accuracy_deg
        self._timestamp_ms = timestamp_ms

    @property
    def have_fix(self) -> bool:
        return self._have_fix

    @property
    def have_approx_fix(self) -> bool:
        return self._have_approx_fix

    @property
    def heading_deg(self) -> float:
        return self._heading_deg

    @property
    def accuracy_deg(self) -> float:
        return self._accuracy_deg

    @property
    def timestamp_ms(self) -> int:
        return self._timestamp_ms


class BenjaminGPSBoardConnector(BootloadableConnector):
    def __init__(self, protocol_connector: MakaProtocolConnector):
        self._nano_connector: NanoPbConnector[Request, Reply] = NanoPbConnector(protocol_connector, Request, Reply)

    def _assert_no_error(self, message: Reply) -> None:
        """
        Raise an exception if a given reply indicates an error processing the request
        """
        if message.WhichOneof("reply") == "error":
            signed_err = ctypes.c_int(message.error.code).value
            raise BenjaminGPSBoardException(f"GPS board returned request failure: {signed_err}")

    async def stop(self) -> None:
        await self._nano_connector.stop()

    async def ping(self) -> float:
        payload = 42
        request = self._nano_connector.create_request()
        request.ping.x = 42
        start_time = time.time()
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        duration = time.time() - start_time
        assert reply.WhichOneof("reply") == "pong"
        assert reply.pong.x == payload
        return duration

    async def position(self) -> Position:
        request = self._nano_connector.create_request()
        request.gps.position.SetInParent()
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert reply.WhichOneof("reply") == "gps" and reply.gps.WhichOneof("reply") == "position"

        dual: Optional[DualGpsData] = None

        if reply.gps.position.dual:
            dual = DualGpsData.FromMessage(reply.gps.position.dual)

        return Position(
            have_fix=reply.gps.position.have_fix,
            have_approx_fix=reply.gps.position.have_approx_fix,
            latitude=reply.gps.position.latitude,
            longitude=reply.gps.position.longitude,
            height_mm=reply.gps.position.height_mm,
            num_sats=reply.gps.position.num_sats,
            hdop=reply.gps.position.hdop,
            timestamp_ms=reply.gps.position.timestamp_ms,
            fix_type=FixType(reply.gps.position.fix_type),
            fix_flags=FixFlags.from_int(reply.gps.position.fix_flags),
            dual_gps_data=dual,
        )

    async def heading(self) -> Heading:
        request = self._nano_connector.create_request()
        request.heading.SetInParent()
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert reply.WhichOneof("reply") == "heading"
        return Heading(
            have_fix=reply.heading.have_fix,
            have_approx_fix=reply.heading.have_approx_fix,
            heading_deg=reply.heading.heading_deg,
            accuracy_deg=reply.heading.accuracy_deg,
            timestamp_ms=reply.heading.timestamp_ms,
        )

    async def spartn(self, msg: SPARTN) -> None:
        requests: List[Request] = []
        for ind in range(0, len(msg.raw), SPARTN_MAX_SIZE):
            requests.append(
                self._nano_connector.create_request(
                    gps=gps_pb2.Request(
                        spartn=gps_pb2.Spartn_Request(
                            data=msg.raw[ind : min(ind + SPARTN_MAX_SIZE, len(msg.raw))], end=False
                        )
                    )
                )
            )
        if requests:
            requests[-1].gps.spartn.end = True
        for req in requests:
            reply: Reply = await self._nano_connector.send_request_await_reply(req)
            assert reply.WhichOneof("reply") == "gps" and reply.gps.WhichOneof("reply") == "spartn"

    async def rtcm(self, msg: bytes) -> None:
        requests: List[Request] = []
        for ind in range(0, len(msg), RTCM_MAX_SIZE):
            requests.append(
                self._nano_connector.create_request(
                    gps=gps_pb2.Request(
                        rtcm=gps_pb2.Rtcm_Request(data=msg[ind : min(ind + RTCM_MAX_SIZE, len(msg))], end=False)
                    )
                )
            )
        if requests:
            requests[-1].gps.rtcm.end = True
        for req in requests:
            reply: Reply = await self._nano_connector.send_request_await_reply(req)
            assert reply.WhichOneof("reply") == "gps" and reply.gps.WhichOneof("reply") == "rtcm"

    async def get_latest_gga(self) -> bytes:
        request = self._nano_connector.create_request(gps=gps_pb2.Request(gga=gps_pb2.GetLastGga_Request()))
        reply = await self._nano_connector.send_request_await_reply(request)
        assert reply.WhichOneof("reply") == "gps" and reply.gps.WhichOneof("reply") == "gga"
        sentence = reply.gps.gga.raw_sentence
        sentence = sentence.rstrip("\r\n")
        sentence += "\r\n"
        return sentence.encode()

    async def hard_reset(self) -> None:
        pass

    async def get_version(self) -> FirmwareVersion:
        return FirmwareVersion(0, 0, 0)

    async def get_board_version(self) -> Tuple[str, Optional[int]]:
        """
        Query the board for the hardware personality (board model + PCB rev)
        """
        request = self._nano_connector.create_request(hwinfo=HwInfoRequest(version=BoardVersionRequest()))
        reply = await self._nano_connector.send_request_await_reply(request)

        assert reply.WhichOneof("reply") == "hwinfo"
        assert reply.hwinfo.WhichOneof("reply") == "version"

        hwrev: Optional[int] = None
        if reply.hwinfo.version.rev != 0xFFFFFFFF:
            hwrev = reply.hwinfo.version.rev

        return (reply.hwinfo.version.model, hwrev)

    async def set_heading_correction(self, offset: Angle) -> None:
        request = self._nano_connector.create_request()
        request.gps.heading_correction.SetInParent()
        request.gps.heading_correction.heading_offset = offset.degrees
        reply = await self._nano_connector.send_request_await_reply(request)
        assert reply.WhichOneof("reply") == "gps" and reply.gps.WhichOneof("reply") == "heading_correction"

    async def set_ptp_mode(self, mode: PtpMode) -> None:
        """
        Configure whether the GPS baord serves as the source of truth for network time (PTP master)
        or whether it synchronizes to an external master.

        NOTE: This config is stored in nonvolatile memory on the GPS board. Changes take effect
              only after a reset of the board.
        """
        request = self._nano_connector.create_request()
        request.ptp_config.SetInParent()
        request.ptp_config.is_master = mode is PtpMode.MASTER

        reply = await self._nano_connector.send_request_await_reply(request)

        self._assert_no_error(reply)
        assert reply.WhichOneof("reply") == "ack", f"Unexpected reply {reply}"

import asyncio
import time
from argparse import ArgumentParser

import numpy as np

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.benjamin_gps_board.benjamin_gps_board_connector import BenjaminGPSBoardConnector
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)


async def _ping_test(board: BenjaminGPSBoardConnector) -> None:
    times = np.array([await board.ping() for x in range(1000)])
    LOG.info(
        f"Nano Ping: mean: {times.mean()}, 95th %tile: {np.percentile(times, 95)}, 99th %tile: {np.percentile(times, 99)}"
    )


async def _position_test(board: BenjaminGPSBoardConnector) -> None:
    position = await board.position()
    print(
        f"Have fix? {position.have_fix}, Latitude: {position.latitude}, Longitude: {position.longitude}, Height: {position.height_mm}, # Sats: {position.num_sats}, HDOP: {position.hdop}, Time: {position.timestamp_ms}"
    )
    assert position.have_fix, position.have_fix
    assert abs(position.latitude - 47.536) < 0.001, position.latitude
    assert abs(position.longitude - (-122.159)) < 0.001, position.longitude
    assert position.num_sats > 3, position.num_sats
    assert position.hdop < 2, position.hdop
    assert abs(position.timestamp_ms - time.time() * 1000) < 1000


async def _heading_test(board: BenjaminGPSBoardConnector) -> None:
    heading = await board.heading()
    print(
        f"Have fix? {heading.have_fix}, Heading deg: {heading.heading_deg}, Accuracy deg: {heading.accuracy_deg}, Time: {heading.timestamp_ms}"
    )
    assert heading.have_fix, heading.have_fix
    assert heading.accuracy_deg < 3, heading.accuracy_deg
    assert abs(heading.timestamp_ms - time.time() * 1000) < 1000


async def _test(host: str, port: int) -> None:
    connector = PsocMEthernetConnector(host, port, asyncio.get_event_loop())
    await connector.open()
    board = BenjaminGPSBoardConnector(connector)
    await _ping_test(board)
    await _position_test(board)
    await _heading_test(board)


def main() -> None:
    parser = ArgumentParser("Benjamin GPS Board Tester")
    parser.add_argument("--host", type=str, default="**********")
    parser.add_argument("--port", type=int, default=4244)
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _test(args.host, args.port)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

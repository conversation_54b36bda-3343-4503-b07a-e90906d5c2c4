syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/diagnostic.proto";
import "generated/lib/drivers/nanopb/proto/request.proto";
import "generated/lib/drivers/nanopb/proto/version.proto";
import "generated/lib/drivers/nanopb/proto/time.proto";
import "generated/lib/drivers/nanopb/proto/carbon_tractor.proto";
import "generated/lib/drivers/nanopb/proto/ots_tractor.proto";

package hasselhoff_board;
option go_package="nanopb/hasselhoff_board";

message Reply {
    request.RequestHeader header = 1;
    oneof reply {
        diagnostic.Pong pong = 2;
        time.Reply time = 3;
        version.Version_Reply version = 4;
        ots_tractor.Reply ots_tractor = 5;
        carbon_tractor.Reply carbon_tractor = 6;
    }
}

message Request {
    request.RequestHeader header = 1;
    oneof request {
        diagnostic.Ping ping = 2;
        time.Request time = 3;
        version.Version_Request version = 4;
        version.Reset_Request reset = 5;
        ots_tractor.Request ots_tractor = 6;
        carbon_tractor.Request carbon_tractor = 7;
    }
}
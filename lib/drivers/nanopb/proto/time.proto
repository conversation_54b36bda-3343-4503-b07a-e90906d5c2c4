syntax = "proto3";

package time;
option go_package="nanopb/time";

import "generated/lib/drivers/nanopb/proto/ack.proto";
import "generated/lib/drivers/nanopb/proto/error.proto";

message Timestamp {
  uint32 seconds = 1;
  uint32 micros = 2;
}

message Set_Epoch_Time_Request {
  Timestamp timestamp = 1;
}

message Get_Timestamp_Request {

}

message Get_Debug_Timestamp_Request {

}

message Get_Debug_Timestamp_Reply {
  Timestamp timestamp = 1;
  uint32 pps_timer_val = 2;
  uint32 pps_ticks = 3;
  double freq_mul = 4;
  int32 error_ticks = 5;
  int32 error_ticks2 = 6;
}

message Request {
  oneof request {
    Set_Epoch_Time_Request set = 1;
    Get_Timestamp_Request get = 2;
    Get_Debug_Timestamp_Request debug = 3;
  }
}

message Reply {
  oneof reply {
    error.Error error = 1;
    ack.Ack ack = 2;
    Timestamp timestamp = 3;
    Get_Debug_Timestamp_Reply debug = 4;
  }
}

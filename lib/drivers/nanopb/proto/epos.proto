syntax = "proto3";

package epos;
option go_package = "nanopb/epos";

import "generated/lib/drivers/nanopb/proto/can_open.proto";
import "generated/lib/drivers/nanopb/proto/error.proto";
import "generated/lib/drivers/nanopb/proto/ack.proto";

// Requests

message Setup_PDOs_Request {

}

message Enable_Request {

}

message Disable_Request {

}

message Hard_Home_Params {
  int32 step_size = 1;
  uint32 offset = 2;
}

message Switch_Home_Params {
  int32 step_size = 1;
  uint32 threshold_step = 2;
}

message Actual_Position_Home_Params {
  uint32 range = 1;
}

message Home_Params {
  int32 min_position = 1;
  int32 max_position = 2;
  uint32 profile_velocity = 3;
  oneof params {
    Hard_Home_Params hard_stop = 4;
    Switch_Home_Params limit_switch = 5;
    Actual_Position_Home_Params actual_position = 6;
  }
  bool invert = 7;
}

message Home_Request {
  Home_Params params = 1;
}

message Await_Settling_Request {
  int32 target_position = 1;
  uint32 window = 2;
  uint32 timeout_ms = 3;
}

message Get_Pos_Vel_Request {

}

message Go_To_Request {
  int32 position = 1;
  uint32 velocity = 2;
  uint32 window = 3;
  uint32 timeout_ms = 4;
}

message Await_Status_Request {
  uint32 timeout_ms = 1;
  uint32 expected = 2;
  uint32 expected_neg = 3;
}

message Set_Positional_PID_Request {
  uint32 gain_p = 1;
  uint32 gain_i = 2;
  uint32 gain_d = 3;
  uint32 gain_ffv = 4;
  uint32 gain_ffa = 5;
}
message Set_PID_Request {
  uint32 current_p = 1;
  uint32 current_i = 2;
  Set_Positional_PID_Request positional_pid = 3;
}

message EPOS_PID {
  uint32 gain_p = 1;
  uint32 gain_i = 2;
  uint32 gain_d = 3;
  uint32 gain_ffv = 4;
  uint32 gain_ffa = 5;
  uint32 current_p = 6;
  uint32 current_i = 7;
}

enum PID_Request_Type {
  PID_REQUEST_FALLBACK = 0;
  PID_REQUEST_TEST = 1;
  PID_REQUEST_SAVE = 2;
}
message Set_PID_V2_Request {
  PID_Request_Type type = 1;
  oneof pid {
    EPOS_PID epos = 2;
  }
}

message Get_PID_Request {

}


// Replies

message Homing_Limit_Reply {
  int32 limit = 1;
}

message Settling_Time_Reply {
  uint32 duration = 1;
}

message Pos_Vel_Reply {
  int32 position = 1;
  int32 velocity = 2;
}
message Get_PID_Reply {
  bool valid = 1;
  oneof pid {
    EPOS_PID epos = 2;
  }
}

// Mains

message Request {
  oneof request {
    can_open.Request can = 1;
    Setup_PDOs_Request setup_pdos = 2;
    Enable_Request enable = 3;
    Disable_Request disable = 4;
    Home_Request home = 5;
    Await_Settling_Request settle = 6;
    Get_Pos_Vel_Request pos_vel = 7;
    Go_To_Request go_to = 8;
    Await_Status_Request status = 9;
    Set_Positional_PID_Request positional_pid = 10;
    Set_PID_Request pid = 11;
    Set_PID_V2_Request pid_v2 = 12;
    Get_PID_Request get_pid = 13;
  }
}

message Reply {
  oneof reply {
    ack.Ack ack = 1;
    can_open.Reply can = 2;
    Homing_Limit_Reply limit = 3;
    Settling_Time_Reply settle = 4;
    Pos_Vel_Reply pos_vel = 5;
    error.Error error = 6;
    Get_PID_Reply pid = 7;
  }
}
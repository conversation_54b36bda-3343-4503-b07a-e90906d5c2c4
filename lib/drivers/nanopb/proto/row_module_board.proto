syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/diagnostic.proto";
import "generated/lib/drivers/nanopb/proto/request.proto";
import "generated/lib/drivers/nanopb/proto/row_module.proto";

package row_module_board;
option go_package = "nanopb/row_module_board";

message Reply {
  request.RequestHeader header = 1;
  oneof reply {
    diagnostic.Pong pong = 2;
    row_module.Reply row_module = 3;
  }
}

message Request {
  request.RequestHeader header = 1;
  oneof request {
    diagnostic.Ping ping = 2;
    row_module.Request row_module = 3;
  }
}
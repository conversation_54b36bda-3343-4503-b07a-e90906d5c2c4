syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/error.proto";
import "generated/lib/drivers/nanopb/proto/ack.proto";

package dawg;
option go_package = "nanopb/dawg";

// Requests

message Config_Request {
  uint32 timeout_ms = 1;
}

message Arm_Request {
  bool armed = 1;
}

message Pet_Request {
  bool firing = 1;
}

message Get_State_Request {

}

// Replies

message State_Reply {
  bool armed = 1;
  bool petted = 2;
}

// Mains

message Request {
  oneof request {
    Config_Request config = 1;
    Arm_Request arm = 2;
    Pet_Request pet = 3;
    Get_State_Request state = 4;
  }
}

message Reply {
  oneof reply {
    error.Error error = 1;
    ack.Ack ack = 2;
    State_Reply state = 3;
  }
}
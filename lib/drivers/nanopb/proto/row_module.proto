syntax = "proto3";

package row_module;
option go_package="nanopb/row_module";

import "generated/lib/drivers/nanopb/proto/scanner.proto";
import "generated/lib/drivers/nanopb/proto/dawg.proto";

// Requests

message Reset_Request {

}

message Clear_Config_Request {

}

message Scanner_Request {
  uint32 scanner_id = 1;
  scanner.Request request = 2;
}

// Replies

message ACK_Reply {

}

// Mains

message Request {
  oneof request {
    Reset_Request reset = 1 ;
    Clear_Config_Request clear = 2;
    Scanner_Request scanner = 3;
    dawg.Request dawg = 4;
  }
}

message Reply {
  oneof reply {
    ACK_Reply ack = 1;
    scanner.Reply scanner = 2;
    dawg.Reply dawg = 3;
  }
}
syntax = "proto3";

package reaper_module_controller;
option go_package = "nanopb/reaper_module_controller";

import "generated/lib/drivers/nanopb/proto/ack.proto";
import "generated/lib/drivers/nanopb/proto/diagnostic.proto";
import "generated/lib/drivers/nanopb/proto/error.proto";
import "generated/lib/drivers/nanopb/proto/hwinfo.proto";
import "generated/lib/drivers/nanopb/proto/request.proto";
import "generated/lib/drivers/nanopb/proto/strobe_control.proto";
import "generated/lib/drivers/nanopb/proto/time.proto";
import "generated/lib/drivers/nanopb/proto/version.proto";

/*
 * Network status and configuration (UDP + OOB)
 */
message NetworkConfigRequest {
    // Retrieve current IP addresses on the main interface
};

message NetworkResetRequest {
    // Take the MCB interface down and reset the network stack
};

message NetworkPowerCycleRequest {
    // Power cycle the Ethernet switch to force link re-negotiation
};

message NetworkRequest {
    oneof request {
        NetworkConfigRequest config = 1;
        NetworkResetRequest reset = 2;
        NetworkPowerCycleRequest powerCycle = 3;
    };
};

enum NetworkAddressSource {
    // Assigned manually (via UDP/serial)
    MANUAL      = 0;
    // Statically assigned based on module ID
    STATIC      = 1;
    // Acquirued via DHCP during boot
    DHCP        = 2;
};

// A single interface IP address; IPs are sent in network byte order
message NetworkAddress {
    // How was this address assigned?
    NetworkAddressSource source = 1;

    // Primary device address
    bytes unicast = 2;
    // Subnet mask for primary interface
    bytes subnet = 3;
    // Upstream gateway/router address, if any
    optional bytes gateway = 4;
};

// Current config/state of the MCB network interface
message NetworkConfigReply {
    // Link state
    bool linkUp = 1;
    // MAC address
    bytes mac = 2;

    // Assigned IPv4 addresses
    repeated NetworkAddress addresses = 3;
};

message NetworkReply {
    oneof reply {
        NetworkConfigReply config = 1;
    };
};

/*
 * Scanner status and control
 */
message ScannerStatus {
    // is power switched on to the scanner?
    bool powerEnabled = 1;

    // is polyfuse for scanner blown?
    bool fuseBlown = 2;
    uint64 fuseTimestamp = 3;

    // current consumed by the scanner
    float current = 4;
    uint64 currentTimestamp = 5;
};

message ScannerGetStatusRequest {
    // retrieve the status of connected scanners
};

message ScannerSetPowerRequest {
    optional bool scannerA = 1;
    optional bool scannerB = 2;
};

// Reset the latching overcurrent protect (fuse blown) flag
message ScannerResetOcpRequest {
    optional bool scannerA = 1;
    optional bool scannerB = 2;
};

message ScannerRequest {
    oneof request {
        ScannerGetStatusRequest status = 1;
        ScannerSetPowerRequest power = 2;
        ScannerResetOcpRequest ocp = 3;
    };
};

message ScannerStatusReply {
    repeated ScannerStatus data = 1;
};

message ScannerReply {
    oneof reply {
        ScannerStatusReply status = 1;
    };
};

/*
 * Switchable off-board DC power
 */
message PowerRequest {
    optional bool relayBoard = 1;
    optional bool strobeBoard = 2;
    optional bool ethSwitch = 3;
    optional bool predictCam = 4;
};

message PowerReply {
    bool relayBoard = 1;
    bool strobeBoard = 2;
    bool ethSwitch = 3;
    bool predictCam = 4;
};

/*
 * Strobe/BTL configuration
 */
message StrobeStatusRequest {
    // get the current strobe board status
};

message SetStrobeStateRequest {
    // Enable strobing and camera triggering
    bool enabled = 1;
};

message TimedStrobeDisableRequest {
    // How long the strobes are disabled for before restoring previous state
    uint32 durationMsec = 1;
};

message StrobeStatusReply {
    float voltage = 1;
    uint64 voltageTimestamp = 2;
    float current = 3;
    uint64 currentTimestamp = 4;

    // from thermistor on strobe board (°C)
    float temperature = 5;
    uint64 temperatureTimestamp = 6;

    // whether strobe board is charged/ready to fire
    bool ready = 7;
    // software request to enable strobing (change with SetState request)
    bool enabled = 8;
    // are the strobes actually firing? (e.g. safeties satisfied & enabled & ready)
    bool firing = 9;

    // currently configured output waveform
    optional uint32 exposureUs = 10;
    optional uint32 periodUs = 11;
    optional uint32 targetsPerPredict = 12;
};

message StrobeRequest {
    oneof request {
        strobe_control.Request setWaveform = 1;
        StrobeStatusRequest getStatus = 2;
        SetStrobeStateRequest setState = 3;
        TimedStrobeDisableRequest timedDisable = 4;
    };
};

message StrobeReply {
    oneof reply {
        StrobeStatusReply status = 1;
    };
};

/*
 * Messages to set configuration data on the board
 *
 * These are stored in nonvolatile memory (flash) on the MCB.
 */
// Module identity configuration: e.g. module number
message ModuleIdentity {
    // Module number within the row
    uint32 number = 2;
};

message GetModuleIdentityRequest {
    // request to get module location/identification
};

// Lock or unlock the OTP for writing
message SetOtpLockRequest {
    // whether to lock or unlock OTP: key is required only to unlock
    bool lock = 1;
    // must always be 'YEET'
    fixed32 key = 2;
};

// Program the board identity for hwinfo endpoint (requires OTP unlock within 1 sec before)
message SetBoardIdRequest {
    optional string cbsn = 1;
    optional string assySn = 2;
};

message ConfigRequest {
    oneof request {
        GetModuleIdentityRequest getIdentity = 1;
        ModuleIdentity setIdentity = 2;

        SetOtpLockRequest otpLock = 3;
        SetBoardIdRequest setBoardIdentity = 4;
    };
};

message ConfigReply {
    oneof reply {
        ModuleIdentity identity = 1;
    };
}

/*
 * Messages to control the circulating fans (manual + automatic control)
 */
enum ThermostatSource {
    DEFAULT         = 0;
    ENVIRO_INTERNAL = 1;
    ENVIRO_EXTERNAL = 2;
};

message ThermostatConfig {
    // Temperature above which fans are turned on (°C)
    float setpoint = 1;
    // Delta temp from setpoint before fans are enabled/disabled
    float hysteresis = 2;
    // Which temperature sensor to use for thermostatic control
    ThermostatSource source = 3;
};

message FanSetRequest {
    optional bool fan1 = 1;
    optional bool fan2 = 2;
};

message FanThermostatConfigRequest {
    // Whether the thermostat is enabled: this will lock out manual control
    optional bool enabled = 1;
    // New configuration to apply to the thermostat
    optional ThermostatConfig config = 2;
};

message FanRequest {
    oneof request {
        FanSetRequest set = 1;
        FanThermostatConfigRequest thermoConfig = 2;
    };
};

message FanReply {
    bool fan1 = 1;
    bool fan2 = 2;

    // Whether thermostat is enabled in user config
    bool thermostatEnabled = 3;
    // Current thermostat configuration, if it's enabled
    optional ThermostatConfig thermostatConfig = 4;
    // Most recently read temperature value, if thermostat is enabled
    optional float thermostatActual = 5;
};

/*
 * Requests to control AC power switched via relay board
 */
message RelayRequest {
    optional bool pc = 1;
    optional bool btl = 2;
    optional bool laser = 3;
};

message RelayReply {
    bool pc = 1;
    bool btl = 2;
    bool laser = 3;
};

/*
 * Messages for interacting with the sensor module
 *
 * - Timestamps are in milliseconds since the epoch
 */
message SensorRequest {
};

message SensorReply {
    // reading data from a single environmental sensor
    message envdata {
        // Timestamp at which this sensor's data was captured
        uint64 timestamp = 1;

        // temperature (in °C)
        float temp = 2;
        // relative humidity, [0, 1]
        float humidity = 3;
        // air pressure (Pa)
        float pressure = 4;
    };
    // IMU reading data
    message imudata {
        // Timestamp at which the IMU readings were captured
        uint64 timestamp = 1;
        // Accelerometer readings, in g
        repeated float accel = 2;
        // Gyroscope readings, in deg/sec
        repeated float gyro = 3;
    };
    // External thermistor data (single reading)
    message thermdata {
        // Timestamp at which this thermistor was sampled
        uint64 timestamp = 1;
        // Temperature, in °C
        float temp = 2;
    };
    // Leak sensor (boolean toggle with timestamp)
    message leakdata {
        // Timestamp when this leak sensor changed state
        uint64 timestamp = 1;
        // Current leak sensor state
        bool active = 2;
    };
    // Pressure transducer data
    message pressdata {
        // Timestamp at which this pressure transducer was sampled
        uint64 timestamp = 1;
        // Pressure value
        float pressure = 2;
        // Associated temperature value (°C)
        float temperature = 3;
    };

    // Environmental sensors; first is on-board, second is external
    repeated envdata env = 1;
    // raw reading from IMU
    imudata imu = 2;
    // external thermistors
    repeated thermdata therm = 3;
    // leak sensors
    repeated leakdata leak = 4;
    // external pressure sensors
    repeated pressdata press = 5;

    // flag indicating whether the external environmental sensor was detected at boot
    bool hasExternalEnv = 6;
};

// Hardware status endpoint, polled by hardware_manager on command
message StatusRequest {

};

message StatusReply {
    SensorReply sensors = 1;

    RelayReply relays = 2;
    PowerReply power = 3;

    StrobeStatusReply strobe = 4;

    repeated ScannerStatus scanners = 5;
};

/*
 * Coredump output messages
 *
 * These are emitted WITHOUT any request from the client, via the serial (OOB) interface.
 */
message CoreDumpStart {

};

message CoreDumpEnd {

};

// Carries actual binary data for a segment of the coredump
message CoreDumpData {
  /// This is the last segment of data in this frame
  bool is_last = 1;
  /// Byte offset of this data in the overall coredump
  uint32 offset = 2;
  /// Actual data to write out
  bytes data = 3;
};

message CoreDumpReply {
  oneof payload {
    CoreDumpStart start = 1;
    CoreDumpEnd end = 2;
    CoreDumpData data = 3;
  };
};

/*
 * The message frames below are used exclusively over the UDP interface.
 */
message UdpRequest {
    request.RequestHeader header = 1;

    oneof request {
        diagnostic.Ping ping = 2;
        version.Version_Request version = 3;
        version.Reset_Request reset = 4;
        time.Request time = 5;

        SensorRequest sensor = 6;
        RelayRequest relay = 7;
        FanRequest fan = 8;
        ConfigRequest config = 9;
        StrobeRequest strobe = 10;
        PowerRequest power = 11;
        ScannerRequest scanner = 12;
        NetworkRequest network = 13;
        hwinfo.Request hwinfo = 14;
        StatusRequest status = 15;
    };
};

message UdpReply {
    request.RequestHeader header = 1;

    oneof reply {
        error.Error error = 2;
        ack.Ack ack = 3;

        diagnostic.Pong pong = 4;
        version.Version_Reply version = 5;
        time.Reply time = 6;

        SensorReply sensor = 7;
        RelayReply relay = 8;
        FanReply fan = 9;
        ConfigReply config = 10;
        StrobeReply strobe = 11;
        PowerReply power = 12;
        ScannerReply scanner = 13;
        NetworkReply network = 14;
        hwinfo.Reply hwinfo = 15;
        StatusReply status = 16;
    };
};

/*
 * The message formats below are used exclusively over the RS232 interface to the host PC. A
 * subset of what's available via UDP can be called over RS232.
 */
message OobRequest {
    request.RequestHeader header = 1;

    oneof request {
        diagnostic.Ping ping = 2;
        version.Version_Request version = 3;
        version.Reset_Request reset = 4;

        ConfigRequest config = 5;
        NetworkRequest network = 6;
        hwinfo.Request hwinfo = 7;

        StrobeRequest strobe = 8;
        PowerRequest power = 9;
        RelayRequest relay = 10;
    };
};

message OobReply {
    request.RequestHeader header = 1;

    oneof reply {
        error.Error error = 2;
        ack.Ack ack = 3;

        diagnostic.Pong pong = 4;
        version.Version_Reply version = 5;

        ConfigReply config = 6;
        NetworkReply network = 7;
        hwinfo.Reply hwinfo = 8;

        StrobeReply strobe = 9;
        PowerReply power = 10;
        RelayReply relay = 11;

        CoreDumpReply coredump = 12;
    };
};

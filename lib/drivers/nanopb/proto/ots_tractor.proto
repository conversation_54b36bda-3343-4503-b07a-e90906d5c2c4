syntax = "proto3";

package ots_tractor;
option go_package = "nanopb/ots_tractor";

enum TractorVariantType {
    TV_UNKNOWN = 0;
    TV_JD_6LH = 1;
    TV_JD_6LHM = 2;
    TV_JD_6PRO = 3;
    TV_JD_7LH = 4;
    TV_JD_7PRO = 5;
    TV_JD_8RH = 6;
}

enum Gear {
    GEAR_PARK = 0;
    GEAR_REVERSE = 1;
    GEAR_NEUTRAL = 2;
    GEAR_FORWARD = 3;
    GEAR_POWERZERO = 4;
}

enum Lights {
    LIGHTS_OFF = 0;
    LIGHTS_LOW = 1;
    LIGHTS_HIGH = 2;
}

enum HitchCmd {
    LIFT = 0;
    LOWER = 1;
    PRECISE = 2;
}

message GearState {
    Gear gear = 1;
}

message LightsState {
    Lights lights = 1;
}

message SpeedControlState {
    float speed = 1;
}

message EngineRpmState {
    int32 rpms = 1;
}

message PtoState {
    bool enabled = 1;
}

message HitchV2Request {
    HitchCmd hitch = 1;
    float precise_hitch_percent = 2;
}

message HitchRequest {
    int32 hitch_lift_force = 1;
}
message HitchReply {
}

message ScvRequest {
    int32 scv_id = 1;
    int32 force = 2; // [-100, 100]
    int32 cmd_time_ms = 3; // How long to command this force for
}
message ScvReply {
}

message TractorVariantState {
    TractorVariantType variant = 1;
}

message WheelAngleCalState {
    float sensor_deg_per_bit = 1;
    uint32 sensor_center_val = 2;
    float center_trim_deg = 3;
    float right_lock_deg = 4;
    uint32 sensor_full_left_val = 5;
    uint32 sensor_full_right_val = 6;
}

message FuelLevel {
    float fuel_level = 1;
}

message EngineTemp {
    float engine_temp = 1;
}

message Empty {
}
message SetRequest {
    oneof set {
        GearState gear = 1;
        LightsState lights = 2;
        SpeedControlState speed_control = 3;
        HitchRequest hitch = 4;
        ScvRequest scv = 5;
        EngineRpmState rpms = 6;
        PtoState front_pto = 7;
        PtoState rear_pto = 8;
        TractorVariantState variant = 9;
        WheelAngleCalState wheel_cal = 10;
        Empty ignition_off = 11;
        HitchV2Request hitch_V2 = 12;
    }
}
message SetReply {
    oneof set {
        GearState gear = 1;
        LightsState lights = 2;
        SpeedControlState speed_control = 3;
        HitchReply hitch = 4;
        ScvReply scv = 5;
        EngineRpmState rpms = 6;
        PtoState front_pto = 7;
        PtoState rear_pto = 8;
        TractorVariantState variant = 9;
        WheelAngleCalState wheel_cal = 10;
        Empty ignition_off = 11;
        HitchReply hitch_V2 = 12;
    }
}
message GetRequest {
    oneof get {
        Empty gear = 1;
        Empty lights = 2;
        Empty speed_control = 3;
        Empty rpms = 4;
        Empty front_pto = 5;
        Empty rear_pto = 6;
        Empty variant = 7;
        Empty wheel_cal = 8;
        Empty fuel_level = 9;
        Empty engine_temp = 10;
    }
}
message GetReply {
    oneof get {
        GearState gear = 1;
        LightsState lights = 2;
        SpeedControlState speed_control = 3;
        EngineRpmState rpms = 4;
        PtoState front_pto = 5;
        PtoState rear_pto = 6;
        TractorVariantState variant = 7;
        WheelAngleCalState wheel_cal = 8;
        FuelLevel fuel_level = 9;
        EngineTemp engine_temp = 10;
    }
}
   
message Request {
    oneof request {
        SetRequest set = 1;
        GetRequest get = 2;
    }
}

message Reply {
    oneof reply {
        SetReply set = 1;
        GetReply get = 2;
    }
}
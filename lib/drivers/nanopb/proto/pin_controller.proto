syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/diagnostic.proto";
import "generated/lib/drivers/nanopb/proto/request.proto";
import "generated/lib/drivers/nanopb/proto/version.proto";
import "generated/lib/drivers/nanopb/proto/ack.proto";

package pin_controller;
option go_package = "nanopb/pin_controller";

enum DigitalPinValue {
  LOW = 0;
  HIGH = 1;
}

message AnalogReadValue {
  uint32 pin_number = 1;
  uint32 value = 2;
}

message DigitalReadValue {
  uint32 pin_number = 1;
  DigitalPinValue value = 2;
}

message PinReply {
  oneof reply {
    ack.Ack ack = 1;
    DigitalReadValue read = 2;
    AnalogReadValue analog = 3;
  }
}

message AnalogRead {
  uint32 pin_number = 1;
}

message DigitalRead {
  uint32 pin_number = 1;
}

message DigitalWrite {
  uint32 pin_number = 1;
  DigitalPinValue value = 2;
}

enum PinMode {
  INPUT = 0;
  OUTPUT = 1;
  INPUT_PULL_UP = 2;
}

message PinConfiguration {
  uint32 pin_number = 1;
  PinMode pin_mode = 2;
}

message PWMConfiguration {
  uint32 pin_number = 1;
  uint32 value_8bit = 2;
}

message PinRequest {
  oneof request {
    PinConfiguration pin = 1;
    PWMConfiguration pwm = 2;
    DigitalWrite write = 3;
    DigitalRead read = 4;
    AnalogRead analog = 5;
  }
}

message Reply {
  request.RequestHeader header = 1;
  oneof reply {
    diagnostic.Pong pong = 2;
    ack.Ack ack = 3;
    version.Version_Reply version = 4;
    PinReply pin = 5;
  }
}

message Request {
  request.RequestHeader header = 1;
  oneof request {
    diagnostic.Ping ping = 2;
    version.Version_Request version = 3;
    PinRequest pin = 4;
  }
}
syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/ack.proto";
import "generated/lib/drivers/nanopb/proto/diagnostic.proto";
import "generated/lib/drivers/nanopb/proto/error.proto";
import "generated/lib/drivers/nanopb/proto/gps.proto";
import "generated/lib/drivers/nanopb/proto/heading.proto";
import "generated/lib/drivers/nanopb/proto/hwinfo.proto";
import "generated/lib/drivers/nanopb/proto/request.proto";

package benjamin_gps_board;
option go_package = "nanopb/benjamin_gps_board";

message PtpConfig_Request {
    // Set to operate as PTP grandmaster, otherwise PTP slave
    bool is_master = 1;
};

message Reply {
  request.RequestHeader header = 1;
  oneof reply {
    diagnostic.Pong pong = 2;
    gps.Reply gps = 3;
    heading.Reply heading = 4;
    hwinfo.Reply hwinfo = 5;
    error.Error error = 6;
    ack.Ack ack = 7;
  }
}

message Request {
  request.RequestHeader header = 1;
  oneof request {
    diagnostic.Ping ping = 2;
    gps.Request gps = 3;
    heading.Request heading = 4;
    hwinfo.Request hwinfo = 5;
    PtpConfig_Request ptp_config = 6;
  }
}

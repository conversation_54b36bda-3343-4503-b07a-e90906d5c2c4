syntax = "proto3";

package hwinfo;
option go_package = "nanopb/hwinfo";

message BoardVersionRequest {
    // get PCB revision
};

message BoardIdentityRequest {
    // get serial numbers
};

message Request {
    oneof request {
        BoardVersionRequest version = 1;
        BoardIdentityRequest identity = 2;
    };
};

message BoardVersionReply {
    // PCB form factor/release (from DTS `compatible`)
    string model = 1;
    // Hardware revision
    uint32 rev = 2;
};

message BoardIdentityReply {
    // CBSN serial number of the board
    optional string cbsn = 1;
    // Assembly serial number
    optional string assySn = 2;
};

message Reply {
    oneof reply {
        BoardVersionReply version = 1;
        BoardIdentityReply identity = 2;
    };
};

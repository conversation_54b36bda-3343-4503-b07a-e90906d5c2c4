syntax = "proto3";

package pulczar;
option go_package = "nanopb/pulczar";

import "generated/lib/drivers/nanopb/proto/gimbal.proto";
import "generated/lib/drivers/nanopb/proto/laser.proto";
import "generated/lib/drivers/nanopb/proto/lens.proto";
import "generated/lib/drivers/nanopb/proto/dawg.proto";
import "generated/lib/drivers/nanopb/proto/ack.proto";
import "generated/lib/drivers/nanopb/proto/scanner_config.proto";
import "generated/lib/drivers/nanopb/proto/arc_detector.proto";

// Ambient temperature reporting for FHs
message AmbientTempConfig {
  bool enabled = 1;
  bool use_thermistor = 2;
  optional float temp = 3;
};

message AmbientTempGetStateRequest {

};

message AmbientTempRequest {
  oneof request {
    AmbientTempConfig set_config = 1;
    AmbientTempGetStateRequest get_config = 2;
  };
};

message AmbientTempReply {
  // temperature used for ambient reporting (if have sensors)
  float sense_temp = 1;

  // the actual configuration currently applied
  AmbientTempConfig config = 2;
};

// Messages for hardware status
message HwStatus_Request {
  // get the current hardware status dat
}

// TODO: what subset of this is available for EPOS controllers?
message HwStatus_Servo {
  bool connected = 1;
  uint32 controller_sn = 2;

  // timestamp for all of these readings
  uint64 sensor_time_ms = 3;
  float output_stage_temp_c = 4;
  float motor_supply_v = 5;
  float motor_current_a = 6;

  // timestamp for encoder
  uint64 encoder_time_ms = 7;
  int64 encoder_ticks = 8;
}

message HwStatus_Slayer_Reply {
  // Thermistor readings in beam combiner path
  float lpm_thermistor_beam_raw_mv = 1;
  float lpm_thermistor_beam_temp_c = 2;
  // Adjacent thermistor, reading the ambient temperature
  float lpm_thermistor_ambient_raw_mv = 3;
  float lpm_thermistor_ambient_temp_c = 4;

  // LPSU status and current feedback
  bool lpsu_status = 5;
  float lpsu_current_ma = 6;

  // status for pan and tilt servos
  optional HwStatus_Servo servo_pan = 7;
  optional HwStatus_Servo servo_tilt = 8;

  // switched target cam power state
  bool target_cam_power_on = 9;
}

message HwStatus_Reaper_Reply {
  // Diode laser status
  bool laser_connected = 1;
  laser.Laser_Inventory_Reply laser_inventory = 2;
  laser.Diode_Status_Reply laser_status = 3;

  // Laser thermistors and power meter
  repeated float laser_therm_temp_c = 4;
  float laser_power_w = 5;
  float laser_power_raw_mv = 6;

  // status for pan and tilt servos
  optional HwStatus_Servo servo_pan = 7;
  optional HwStatus_Servo servo_tilt = 8;

  // switched target cam power state
  bool target_cam_power_on = 9;
}

message HwStatus_Reply {
  oneof reply {
    HwStatus_Slayer_Reply slayer = 1;
    HwStatus_Reaper_Reply reaper = 2;
  }
}

// Requests

message Reset_Request {

}

message Clear_Config_Request {

}

message Status_Request {

}

message Override_Request {
  uint32 override = 1;
}

message Power_Request {
    optional bool targetCam = 1;
    optional bool firingBoard = 2;
};

// Replies

message Status_Reply {
  uint32 status = 1;
  laser.Laser_Status_Reply laser_status = 2;
}

message Power_Reply {
    bool targetCam = 1;
    bool firingBoard = 2;
};

// Mains

message Request {
  oneof request {
    Reset_Request reset = 1;
    Clear_Config_Request clear = 2;
    gimbal.Request gimbal = 3;
    dawg.Request dawg = 4;
    laser.Request laser = 5;
    Status_Request status = 6;
    lens.Request lens = 7;
    Override_Request override = 8;
    scanner_config.Request conf = 9;
    arc_detector.Request arc = 10;
    Power_Request power = 11;
    HwStatus_Request hw_status = 12;
    AmbientTempRequest ambient_temp = 13;
  }
}

message Reply {
  oneof reply {
    ack.Ack ack = 1;
    gimbal.Reply gimbal = 2;
    dawg.Reply dawg = 3;
    laser.Reply laser = 4;
    Status_Reply status = 5;
    lens.Reply lens = 6;
    scanner_config.Reply conf = 7;
    arc_detector.Reply arc = 8;
    Power_Reply power = 9;
    HwStatus_Reply hw_status = 10;
    AmbientTempReply ambient_temp = 11;
  }
}

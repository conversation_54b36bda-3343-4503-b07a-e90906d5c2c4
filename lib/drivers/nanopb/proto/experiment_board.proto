syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/diagnostic.proto";
import "generated/lib/drivers/nanopb/proto/request.proto";
import "generated/lib/drivers/nanopb/proto/row_module.proto";

package experiment_board;
option go_package = "nanopb/experiment_board";

message ExperimentItem {
  uint32 x = 1;
}

message ExperimentRepeated {
  repeated ExperimentItem items = 1;
}

message ExperimentResult {
    uint32 sum = 1;
}

message ExperimentReply {
  request.RequestHeader header = 1;
  oneof reply {
    diagnostic.Pong pong = 2;
    ExperimentResult result = 3;
    row_module.Reply row_module = 4;
  }
}

message ExperimentRequest {
  request.RequestHeader header = 1;
  oneof request {
    diagnostic.Ping ping = 2;
    ExperimentRepeated items = 3;
    row_module.Request row_module = 4;
  }
}
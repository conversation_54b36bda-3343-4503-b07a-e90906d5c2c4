syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/diagnostic.proto";
import "generated/lib/drivers/nanopb/proto/park_brake.proto";
import "generated/lib/drivers/nanopb/proto/request.proto";
import "generated/lib/drivers/nanopb/proto/rotary_encoder.proto";
import "generated/lib/drivers/nanopb/proto/version.proto";
import "generated/lib/drivers/nanopb/proto/sensors.proto";
import "generated/lib/drivers/nanopb/proto/drive_solenoids.proto";
import "generated/lib/drivers/nanopb/proto/time.proto";

package nofx_board;
option go_package="nanopb/nofx_board";

message Reply {
  request.RequestHeader header = 1;
  oneof reply {
    diagnostic.Pong pong = 2;
    rotary_encoder.Reply rotary_encoder = 3;
    park_brake.Reply park_brake = 4;
    park_brake.Query_Reply park_brake_query = 5;
    version.Version_Reply version = 6;
    sensors.Reply sensors = 7;
    time.Reply time = 8;
    drive_solenoids.Reply drive_solenoids = 9;
  }
}

message Request {
  request.RequestHeader header = 1;
  oneof request {
    diagnostic.Ping ping = 2;
    rotary_encoder.Request rotary_encoder = 3;
    park_brake.Request park_brake = 4;
    park_brake.Query_Request park_brake_query = 5;
    version.Version_Request version = 6;
    version.Reset_Request reset = 7;
    sensors.Request sensors = 8;
    time.Request time = 9;
    drive_solenoids.Request drive_solenoids = 10;
  }
}

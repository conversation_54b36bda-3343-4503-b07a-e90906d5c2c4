syntax = "proto3";

package drive_solenoids;
option go_package="nanopb/drive_solenoids";

message Drive_Request {
    enum Direction {
      forward = 0;
      backward = 1;
      stop = 3;
    }
    Direction dir = 1;
    float duty_cycle = 2;
}

message Drive_Reply {
}

message Turn_Request {
    enum Direction {
      left = 0;
      right = 1;
      straight = 2;
    }
    Direction dir = 1;
    float duty_cycle = 2;
}

message Turn_Reply {
}

message Request {
    oneof request {
        Drive_Request drive = 1;
        Turn_Request turn   = 2;
    }
}

message Reply {
    oneof reply {
        Drive_Reply drive = 1;
        Turn_Reply turn   = 2;
    }
}

syntax = "proto3";

package scanner;
option go_package = "nanopb/scanner";

import "generated/lib/drivers/nanopb/proto/servo.proto";
import "generated/lib/drivers/nanopb/proto/epos.proto";

// Requests

message Laser_Request {
  bool on = 1;
}

message Get_Laser_Request {

}

message Intensity_Request {
  int32 intensity = 1; // uint16_t
}

message Boot_Request {
  epos.Home_Params pan_params = 1;
  epos.Home_Params tilt_params = 2;
}

message Stop_Request {

}

message Gimbal_Request {
  servo.Request pan = 1;
  servo.Request tilt = 2;
}

// Replies

message Error_Reply {

}

message ACK_Reply {

}

message Laser_State_Reply {
  bool on = 1;
}

message Gimbal_Reply {
  servo.Reply pan = 1;
  servo.Reply tilt = 2;
}

// Mains

message Request {
  oneof request {
    Laser_Request laser = 1;
    Get_Laser_Request get_laser = 2;
    Boot_Request boot = 3;
    Stop_Request stop = 4;
    Gimbal_Request gimbal = 5;
    Intensity_Request intensity = 6;
  }
}

message Reply {
  oneof reply {
    Error_Reply error = 1;
    ACK_Reply ack = 2;
    Laser_State_Reply laser = 3;
    Gimbal_Reply gimbal = 4;
  }
}
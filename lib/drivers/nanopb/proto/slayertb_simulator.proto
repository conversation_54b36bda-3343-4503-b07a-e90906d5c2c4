syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/diagnostic.proto";
import "generated/lib/drivers/nanopb/proto/request.proto";

package slayertb_simulator;
option go_package="nanopb/slayertb_simulator";

enum Sensor {
    LIFTED = 0;
    LEFT_ESTOP = 1;
    RIGHT_ESTOP = 2;
    CAB_ESTOP = 3;
    LASER_KEY = 4;
    LASER_INTERLOCK = 5;
    WATER_PROTECT = 6;
    TRACTOR_POWER = 7;
    TEMP = 8;
    HUMIDITY = 9;
    BATTERY_VOLTAGE = 10;
    CHILLER_RUN_SIGNAL = 11;
    CAB_ESTOP_SIGNAL = 12;
    BEACON_LEFT_SIGNAL = 13;
    BEACON_RIGHT_SIGNAL = 14;
}

message GetValueRequest {
    Sensor sensor = 1;
}

message GetValueReply {
    oneof value {
        bool boolean_value = 1;
        int32 int_value = 2;
    }
}

message SetValueRequest {
    Sensor sensor = 1;
    oneof value {
        bool boolean_value = 2;
        int32 int_value = 3;
    }
}

message SetValueReply {

}

message UseTempSensorRequest {
    bool on = 1;
}

message UseTempSensorReply {

}

message Reply {
    request.RequestHeader header = 1;
    oneof reply {
      diagnostic.Pong pong = 2;
      GetValueReply get_value = 3;
      SetValueReply set_value = 4;
      UseTempSensorReply use_temp_sensor = 5;
    }
}
  
message Request {
    request.RequestHeader header = 1;
    oneof request {
        diagnostic.Ping ping = 2;
        GetValueRequest get_value = 3;
        SetValueRequest set_value = 4;
        UseTempSensorRequest use_temp_sensor = 5;
    }
}

syntax = "proto3";

package lens;
option go_package="nanopb/lens";

import "generated/lib/drivers/nanopb/proto/error.proto";
import "generated/lib/drivers/nanopb/proto/ack.proto";


// Requests

message Set_Request {
  uint32 value = 1; // uint8_t
}

message Get_Request {

}

// Replies

message Get_Reply {
  uint32 value = 1;
}

// Mains

message Request {
  oneof request {
    Set_Request set = 1;
    Get_Request get = 2;
  }
}

message Reply {
  oneof reply {
    error.Error error = 1;
    ack.Ack ack = 2;
    Get_Reply get = 3;
  }
}
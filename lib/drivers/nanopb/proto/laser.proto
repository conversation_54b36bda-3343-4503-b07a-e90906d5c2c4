syntax = "proto3";

package laser;
option go_package = "nanopb/laser";

import "generated/lib/drivers/nanopb/proto/error.proto";
import "generated/lib/drivers/nanopb/proto/ack.proto";

message Raw_Data_Request {

};

message Raw_Data_Reply {
    // power meter thermistor readings
    optional int32 therm1_raw = 1;
    optional int32 therm2_raw = 2;

    // photodiode input analog voltage
    optional int32 photodiode_raw = 3;
};

// Common laser
enum LaserType {
    LASERTYPE_UNKNOWN = 0;
    LASERTYPE_CO2 = 1;
    LASERTYPE_DIODE_BWT = 2;
    LASERTYPE_DIODE_JLIGHT = 3;
};

message Laser_Inventory_Request {
    // retrieve information about the installed laser
};

message Laser_Inventory_Reply {
    // laser controller model number
    string model = 1;
    // laser serial number
    string serial = 2;
    // rated laser power (W)
    uint32 power = 3;
    // Laser type/manufacturer
    LaserType type = 4;
};

// write the type of laser to EEPROM
message Laser_Set_Type_Request {
  LaserType type = 1;
};

// reset the laser subsystem
message Laser_Reset_Request {
    // reset transport layer
    bool transport = 1;
    // clear any alarms/faults latched in the laser
    bool faults = 2;
};

// Jilight laser specific
enum Jlight_Fault {
  JLIGHT_FAULT_UNKNOWN = 0;
  JLIGHT_FAULT_INTERLOCK = 1;
  JLIGHT_FAULT_SYSTEM = 2;
  JLIGHT_FAULT_CH1_OVERCURRENT = 3;
  JLIGHT_FAULT_CH2_OVERCURRENT = 4;
  JLIGHT_FAULT_CH1_OVERVOLTAGE = 5;
  JLIGHT_FAULT_CH2_OVERVOLTAGE = 6;
  JLIGHT_FAULT_CH1_OVERTEMP = 7;
  JLIGHT_FAULT_CH2_OVERTEMP = 8;
  JLIGHT_FAULT_DRIVER_OVERTEMP = 9;
}

message Jlight_Status {
    // Power spply temp
    float psuTemp = 1;
    // Power supply input
    float psuInputVolts = 2;
    // Output supply voltages
    repeated float psuOutputVolts = 3;
    // Fault conditions reported by laser
    repeated Jlight_Fault faults = 4;
};

// BWT laser specific
message Bwt_Status {
    // Laser specific fault codes, if any
    repeated int32 faults = 1;
};

message Bwt_Passthrough_Request {
    // Send this command to BWT laser, execute and return response
    string command = 1;
};
message Bwt_Passthrough_Reply {
    // Response to a previous BWT command
    string response = 1;
};

message Bwt_Transport_Get_Config_Request {

};

message Bwt_Transport_Config {
    // Print all BWT RX/TX messages to syslog
    bool log_messages = 1;
    // Force additional wait/delays between commands
    bool intercommand_delay = 2;
};

// Generic diode laser status
message Diode_Status_Request {
    // returns -ENXIO if not connected
};

message Diode_Status_Reply {
    // timestamp this data was acquired (msec)
    uint64 timestamp = 1;

    // laser system temperature and humidity readings
    repeated float temp = 2;
    repeated float humidity = 3;

    // Laser diode current
    repeated float current = 4;

    // error state flag: type specific may have more info
    bool faulted = 5;

    // converted thermistor temperatures (0 = collimator, 1 = fiber)
    repeated float thermistors = 6;

    // type specific bonus data
    oneof extra {
        Bwt_Status extra_bwt = 7;
        Jlight_Status extra_jlight = 8;
    };
};

// Set diode output current
message Diode_Set_Current_Request {
    repeated uint32 current = 1;

    // when set, the currents are committed to nonvolatile memory
    bool commit = 2;
};

// Requests

message Laser_Request {
  bool on = 1;
}

message Get_Laser_Request {

}

message Intensity_Request {
  int32 intensity = 1; // uint16_t
}

// Replies
message Laser_Reply {
  int32 raw_therm1_reading_mv = 1;
  int32 raw_therm2_reading_mv = 2;
  bool on = 3;
  bool lpsu_state = 4;
  bool fireable = 5;
}

message Laser_State_Reply {
  bool on = 1;
}

message Laser_Status_Reply {
  bool lpsu_state = 1;
  float lpsu_current = 2;
  float power = 3;
  bool arc_detected = 4;
}

// Mains

message Request {
  // was bwt_reset; replaced by laser_reset
  reserved 8;

  oneof request {
    Laser_Request laser = 1;
    Get_Laser_Request get_laser = 2;
    Intensity_Request intensity = 3;
    Raw_Data_Request raw_data = 4;

    Diode_Status_Request diode_status = 5;
    Laser_Inventory_Request laser_inventory = 6;

    Bwt_Passthrough_Request bwt_passthrough = 7;
    Bwt_Transport_Config bwt_set_config = 9;
    Bwt_Transport_Get_Config_Request bwt_get_config = 10;

    Laser_Reset_Request laser_reset = 11;
    Diode_Set_Current_Request diode_set_current = 12;
    Laser_Set_Type_Request set_type = 13;
  }
}

message Reply {
  oneof reply {
    error.Error error = 1;
    ack.Ack ack = 2;
    Laser_State_Reply laser = 3;
    Laser_Reply laser_reply = 4;
    Raw_Data_Reply raw_data = 5;

    Diode_Status_Reply diode_status = 6;
    Laser_Inventory_Reply laser_inventory = 7;

    Bwt_Passthrough_Reply bwt_passthrough = 8;
    Bwt_Transport_Config bwt_config = 9;
  }
}

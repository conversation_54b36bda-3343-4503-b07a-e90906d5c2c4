syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/diagnostic.proto";
import "generated/lib/drivers/nanopb/proto/hwinfo.proto";
import "generated/lib/drivers/nanopb/proto/request.proto";
import "generated/lib/drivers/nanopb/proto/version.proto";
import "generated/lib/drivers/nanopb/proto/time.proto";
import "generated/lib/drivers/nanopb/proto/pulczar.proto";

package pulczar_board;
option go_package = "nanopb/pulczar_board";

message Reply {
  request.RequestHeader header = 1;
  oneof reply {
    diagnostic.Pong pong = 2;
    pulczar.Reply pulczar = 3;
    version.Version_Reply version = 4;
    time.Reply time = 5;
    hwinfo.Reply hwinfo = 6;
  }
}

message Request {
  request.RequestHeader header = 1;
  oneof request {
    diagnostic.Ping ping = 2;
    pulczar.Request pulczar = 3;
    version.Version_Request version = 4;
    time.Request time = 5;
    hwinfo.Request hwinfo = 6;
  }
}

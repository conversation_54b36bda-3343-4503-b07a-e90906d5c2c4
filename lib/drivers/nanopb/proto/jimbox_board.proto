syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/diagnostic.proto";
import "generated/lib/drivers/nanopb/proto/request.proto";
import "generated/lib/drivers/nanopb/proto/cruise.proto";
import "generated/lib/drivers/nanopb/proto/version.proto";
import "generated/lib/drivers/nanopb/proto/time.proto";

package jimbox_board;
option go_package="nanopb/jimbox_board";

message Reply {
  request.RequestHeader header = 1;
  oneof reply {
    diagnostic.Pong pong = 2;
    cruise.Reply cruise = 3;
    version.Version_Reply version = 4;
    time.Reply time = 5;
  }
}

message Request {
  request.RequestHeader header = 1;
  oneof request {
    diagnostic.Ping ping = 2;
    cruise.Request cruise = 3;
    version.Version_Request version = 4;
    version.Reset_Request reset = 5;
    time.Request time = 6;
  }
}

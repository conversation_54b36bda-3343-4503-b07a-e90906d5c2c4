syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/ots_tractor.proto";

package carbon_tractor;
option go_package = "nanopb/carbon_tractor";

enum HHStateStatus {
    HH_UNKNOWN = 0;
    HH_DISABLED = 1;
    HH_OPERATIONAL = 2;
    HH_STOPPED = 3;
    HH_SAFE = 4;
    HH_ESTOP = 5;
}


message  Empty{
}

message  HHState{
    HHStateStatus state = 1;
}

message BrakeState {
    int32 force_left = 1; // 0-100 force
    int32 force_right = 2; // 0-100 force
}

message SafetySensorsState {
    bool triggered_sensor_1 = 1;
    bool triggered_sensor_2 = 2;
    bool triggered_sensor_3 = 3;
    bool triggered_sensor_4 = 4;
}

message SafetySensorBypassState {
    bool bypass = 1;
}

message TractorStatus {
    HHState state = 1;
    int32 error_flag = 2; // bit field for errors
    float ground_speed = 3;
    float wheel_angle = 4;
    float hitch_lift_percentage = 5;
    ots_tractor.GearState gear = 6; // Cannot direct import enum in nanopb
    bool safety_triggered = 7;
    bool safety_bypass = 8;
    bool remote_lockout = 9;
    int32 rpms = 10;
}

message PetRequest {
    // Dont use regular empty message incase we need to add here
}

message SteeringState {
    float angle = 1;
}

message SteeringCfgState {
    float kp = 1;
    float ki = 2;
    float kd = 3;
    float integral_limit = 4;
    int32 update_rate_hz = 5;
    uint32 min_steering_valve_current = 6;
    uint32 max_steering_valve_current = 7;
}

message PetLossState {
    bool use_stop = 1;
}

message SpeedLimitState {
    float speed_limit_mph = 1; // <0 is unbound otherwise max speed set here (note  0 is a valid max speed and you won't be able to move)
}

message SetRequest {
    oneof set {
        HHState hh_state = 1;
        BrakeState brakes = 2;
        SafetySensorBypassState safety_bypass = 3;
        SteeringState steering = 4;
        SteeringCfgState steering_cfg = 5;
        PetLossState pet_loss = 6;
        SpeedLimitState speed_limit = 7;
    }
}
message SetReply {
    oneof set {
        HHState hh_state = 1;
        BrakeState brakes = 2;
        SafetySensorBypassState safety_bypass = 3;
        SteeringState steering = 4;
        SteeringCfgState steering_cfg = 5;
        PetLossState pet_loss = 6;
        SpeedLimitState speed_limit = 7;
    }
}
message GetRequest {
    oneof get {
        Empty status = 1;
        Empty hh_state = 2;
        Empty brakes = 3;
        Empty safety = 4;
        Empty safety_bypass = 5;
        Empty steering = 6;
        Empty steering_cfg = 7;
        Empty pet_loss = 8;
        Empty speed_limit = 9;
    }
}
message GetReply {
    oneof get {
        TractorStatus status = 1;
        HHState hh_state = 2;
        BrakeState brakes = 3;
        SafetySensorsState safety = 4;
        SafetySensorBypassState safety_bypass = 5;
        SteeringState steering = 6;
        SteeringCfgState steering_cfg = 7;
        PetLossState pet_loss = 8;
        SpeedLimitState speed_limit = 9;
    }
}
message DebugRequest {
    /*
    oneof debug {
    }
    */
}
message DebugReply {
    /*
    oneof debug {
    }
    */
}
message Request {
    oneof request {
        SetRequest set = 1;
        GetRequest get = 2;
        PetRequest pet = 3;
        DebugRequest debug = 4;
    }
}

message Reply {
    oneof reply {
        SetReply set = 1;
        GetReply get = 2;
        TractorStatus pet = 3;
        DebugReply debug = 4;
    }
}
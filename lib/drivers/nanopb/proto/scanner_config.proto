syntax = "proto3";

package scanner_config;
option go_package="nanopb/scanner_config";

import "generated/lib/drivers/nanopb/proto/error.proto";
import "generated/lib/drivers/nanopb/proto/ack.proto";

message Delta_Target_Config {
  float pan_skew = 1;
  float tilt_skew = 2;
}

message  Color_Config {
  float red = 1;
  float green = 2;
  float blue = 3;
}

message Camera_Serial_Number_Config {
  bytes serial_number = 1;
}
message Scanner_Barcode_Str_Config {
  bytes barcode = 1;
}

// Requests 

message Set_Delta_Target_Config_Request {
  Delta_Target_Config config = 1;
}

message Get_Delta_Target_Config_Request {

}

message Set_Color_Config_Request {
  Color_Config config = 1;
}

message Get_Color_Config_Request {

}

message Set_Camera_Serial_Number_Config_Request {
  Camera_Serial_Number_Config config = 1;
}

message Get_Camera_Serial_Number_Config_Request {

}

message Set_HW_Revision_Request {
  uint32 revision = 1;
}
message Get_HW_Revision_Request {
}


message Set_Scanner_Barcode_Str_Request {
  Scanner_Barcode_Str_Config config = 1;
}
message Get_Scanner_Barcode_Str_Request {
}

// Replies 

message Get_Delta_Target_Config_Reply {
  Delta_Target_Config config = 1;
}

message Get_Color_Config_Reply {
  Color_Config config = 1;
}

message Get_Camera_Serial_Number_Config_Reply {
  Camera_Serial_Number_Config config = 1;
}
message Get_HW_Revision_Reply {
  uint32 revision = 1;
}

message Get_Scanner_Barcode_Str_Reply {
  Scanner_Barcode_Str_Config config = 1;
}


// Mains

message Request {
  oneof request {
    Set_Delta_Target_Config_Request set_dt = 1;
    Get_Delta_Target_Config_Request get_dt = 2;
    Set_Color_Config_Request set_color = 3;
    Get_Color_Config_Request get_color = 4;
    Set_Camera_Serial_Number_Config_Request set_sn = 5;
    Get_Camera_Serial_Number_Config_Request get_sn = 6;
    Set_HW_Revision_Request set_hw = 7;
    Get_HW_Revision_Request get_hw = 8;
    Set_Scanner_Barcode_Str_Request set_bc = 9;
    Get_Scanner_Barcode_Str_Request get_bc = 10;
  }
}

message Reply {
  oneof reply {
    error.Error error = 1;
    ack.Ack ack = 2;
    Get_Delta_Target_Config_Reply dt = 3;
    Get_Color_Config_Reply color = 4;
    Get_Camera_Serial_Number_Config_Reply sn = 5;
    Get_HW_Revision_Reply hw = 6;
    Get_Scanner_Barcode_Str_Reply bc = 7;
  }
}
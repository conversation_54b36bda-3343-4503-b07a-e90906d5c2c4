syntax = "proto3";

package arc_detector;
option go_package = "nanopb/arc_detector";

import "generated/lib/drivers/nanopb/proto/error.proto";
import "generated/lib/drivers/nanopb/proto/ack.proto";

// Common message types
message Config {
    // Whether arc detector is enabled and can cause alarms
    bool enabled = 1;

    // Upper and lower acceptable current limit (A)
    float upperLimit = 2;
    float lowerLimit = 3;

    // Time period in over which the arc detector integrates events
    uint32 alarmPeriod = 4;
    // Number of arc events to accumulate over period before alarm is raised
    uint32 alarmThreshold = 5;

    // Time delay between firing start and first LPSU current sample for arc detector (ms)
    uint32 initialDelay = 6; // uint16
    // Sample period for laser current after initial sample (ms)
    uint32 sampleInterval = 7; // uint16
};

// Reset arc detector alarm
message Reset_Alarm_Request {

};

// Get status
message Status_Request {

};

message Status_Reply {
    // Whether the arc detector has been properly configured and enabled
    bool enabled = 1;
    // Alarm threshold was reached and firing is inhibited
    bool alarm = 2;

    // Lowest and highest LPSU current readings during firing
    float minCurrent = 3;
    float maxCurrent = 4;
};

// Get/set arc detector config
message Set_Config_Request {
    Config newConfig = 1;
};

message Get_Config_Request {

};

message Config_Reply {
    Config conf = 1;
};

// Container messages
message Request {
    oneof request {
        Set_Config_Request setConf = 1;
        Get_Config_Request getConf = 2;
        Status_Request status = 3;
        Reset_Alarm_Request reset = 4;
    };
};

message Reply {
    oneof reply {
        error.Error error = 1;
        ack.Ack ack = 2;
        Status_Reply status = 3;
        Config_Reply conf = 4;
    };
};

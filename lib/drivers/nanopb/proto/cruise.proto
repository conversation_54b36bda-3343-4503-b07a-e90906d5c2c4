syntax = "proto3";


import "generated/lib/drivers/nanopb/proto/diagnostic.proto";

package cruise;
option go_package = "nanopb/cruise";

enum TractorVariantType {
    TV_UNKNOWN = 0;
    TV_JD_6LH = 1;
    TV_JD_6LHM = 2;
    TV_JD_6PRO = 3;
    TV_JD_7RH = 4;
    TV_JD_8PRO = 5;
}

message Throttle_Request {
    int32 change = 1;
}

message Throttle_Reply {
    bool success = 1;
}

message Status_Request {

}

message Status_Reply {
    bool enabled = 1;
    bool unavailable = 2;
    int32 speed_ticks = 3;
    int32 speed_lever_position = 4;
    bool speed_lever_s1 = 5;
    bool speed_lever_s2 = 6;
}

message Enable_Request {
    bool enabled = 1;
}

message Enable_Reply {
    bool enabled = 1;
}

message Gear_Request {
    int32 gear = 1;
}

message Gear_Reply {
    bool success = 1;
}

message Variant_Request {
    TractorVariantType variant = 1;
}

message Variant_Reply {
    TractorVariantType variant = 1;
}

message Request {
    oneof request {
        diagnostic.Ping ping = 2;
        Throttle_Request throttle = 3;
        Status_Request status = 4;
        Enable_Request enable = 5;
        Gear_Request gear = 6;
        Variant_Request variant = 7;
    }
}

message Reply {
    oneof reply {
        diagnostic.Pong pong = 2;
        Throttle_Reply throttle = 3;
        Status_Reply status = 4;
        Enable_Reply enable = 5;
        Gear_Reply gear = 6;
        Variant_Reply variant = 7;
    }
}
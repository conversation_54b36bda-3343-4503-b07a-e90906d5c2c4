syntax = "proto3";

package servo;
option go_package = "nanopb/servo";

import "generated/lib/drivers/nanopb/proto/epos.proto";
import "generated/lib/drivers/nanopb/proto/error.proto";
import "generated/lib/drivers/nanopb/proto/ack.proto";
import "generated/lib/drivers/nanopb/proto/time.proto";

// Requests

message Config {
  uint32 max_profile_velocity = 1;
  uint32 settle_window = 2;
  uint32 settle_timeout = 3;
  uint32 max_diff_millis = 4;
}

message Config_Request {
  uint32 node_id = 1;
  Config config = 2;
}

message Boot_Request {
  epos.Home_Params params = 1;
}

message Stop_Request {

}

message Go_To_Request {
  int32 position = 1;
  uint32 velocity = 2;
  bool await_settle = 3;
}

message Get_Limits_Request {

}

enum GoToMode {
    IMMEDIATE = 0;
    REACHED = 1;
    SETTLED = 2;
    TRAJECTORY = 3;
  }

message Go_To_Delta_Request {
  int32 delta_position = 1;
  uint32 velocity = 2;
  GoToMode mode = 3;
}

message Go_To_Delta_Follow_Request {
  Go_To_Delta_Request delta = 1;
  int32 follow_velocity_vector = 2;
  uint32 follow_velocity_mrpm = 3;
  uint32 interval_sleep_time_ms = 4;
  bool fast_return = 5;
}

message Go_To_Calibrate_Request {
  int32 position = 1;
  uint32 velocity = 2;
  uint32 window = 3;
  uint32 time_window_ms = 4;
  uint32 timeout_ms = 5;
  uint32 period_ms = 6;
}

message Go_To_Timestamp_Request {
  time.Timestamp timestamp = 1;
  GoToMode mode = 2;
  int32 position = 3;
  uint32 velocity_mrpm = 4;
  int32 follow_velocity = 5;
  int32 follow_accel = 6;
  uint32 interval_sleep_time_ms = 7;
}
message Go_To_Follow_Request {
  time.Timestamp timestamp = 1;
  int32 position = 2;
  uint32 velocity_mrpm = 3;
  int32 follow_velocity = 4;
  int32 follow_accel = 5;
  uint32 interval_sleep_time_ms = 6;
}

message Follow_Timestamp_Request {
  time.Timestamp timestamp = 1;
  int32 follow_velocity = 2;
  int32 follow_accel = 3;
}

// Replies

message Limits_Reply {
  int32 min = 1;
  int32 max = 2;
}

message Position_Reply {
  int32 position = 1;
}

message Settle_Time_Reply {
  uint32 settle_time = 1;
}

message Go_To_Timestamp_Reply {
  int32 pre_position = 1;
  int32 post_position = 2;
  time.Timestamp pre_timestamp = 3;
  time.Timestamp post_timestamp = 4;
}

message Follow_Timestamp_Reply {
  int32 pre_position = 1;
  time.Timestamp pre_timestamp = 2;
}
message Go_To_Follow_Reply {
  int32 pre_position = 1;
  time.Timestamp pre_timestamp = 2;
}

// Mains

message Request {
  oneof request {
    Config_Request config = 1;
    Boot_Request boot = 2;
    Stop_Request stop = 3;
    Go_To_Request go_to = 4;
    Get_Limits_Request limit = 5;
    epos.Request epos = 6;
    Go_To_Delta_Request delta = 7;
    Go_To_Delta_Follow_Request follow = 8;
    Go_To_Calibrate_Request calibrate = 9;
    Go_To_Timestamp_Request go_to_timestamp = 10;
    Follow_Timestamp_Request follow_timestamp = 11;
    Go_To_Follow_Request go_to_follow = 12;
  }
}

message Reply {
  oneof reply {
    error.Error error = 1;
    ack.Ack ack = 2;
    Limits_Reply limit = 3;
    epos.Reply epos = 4;
    Position_Reply pos = 5;
    Settle_Time_Reply settle = 6;
    Go_To_Timestamp_Reply go_to_timestamp = 7;
    Follow_Timestamp_Reply follow_timestamp = 8;
    Go_To_Follow_Reply go_to_follow = 9;
  }
}


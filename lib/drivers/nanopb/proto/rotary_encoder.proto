syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/time.proto";

package rotary_encoder;
option go_package="nanopb/rotary_encoder";

/* Configuration */
message RotaryEncodersConfig_Request {
    enum Type {
        TICK = 0;
        QUAD = 1;
        NONE = 2;
    }
    Type FL_type = 1;
    Type FR_type = 2;
    Type BL_type = 3;
    Type BR_type = 4;
}

message RotaryEncodersConfig_Reply {
}

/* Rotary Request */
message RotaryEncoder_Request {
}

message RotaryEncoder_Reply {
  int64 front_left_ticks = 1;
  int64 front_right_ticks = 2;
  int64 back_left_ticks = 3;
  int64 back_right_ticks = 4;
  time.Timestamp timestamp = 5;
}

/* Snapshot Request */
message RotaryEncoderSnapshot_Request {
  time.Timestamp first = 1;
  time.Timestamp last = 2;
}

message RotaryEncoderSnapshot_Reply {
  RotaryEncoder_Reply first_before = 1;
  RotaryEncoder_Reply first_after = 2;
  RotaryEncoder_Reply last_before = 3;
  RotaryEncoder_Reply last_after = 4;
  RotaryEncoderSnapshot_Request request = 5;
}

message RotaryEncoderHistoryVerify_Request {
}

message RotaryEncoderHistoryVerify_Reply {
  uint32 num_time_warps = 1;
  uint32 num_ooo_elements = 2;
  uint32 max_usec_distance = 3;
  uint32 num_epoch_resets = 4;
  uint32 num_plus1_usec = 5;
  uint32 num_missed_signal = 6;
  uint32 num_reject_signal = 7;
  uint32 num_short_time = 8;
  uint32 num_long_time = 9;
}

message Request {
  oneof request {
    RotaryEncoder_Request rotary = 1;
    RotaryEncodersConfig_Request config = 2;
    RotaryEncoderSnapshot_Request rotary_snapshot = 3;
    RotaryEncoderHistoryVerify_Request history_verify = 4;
  }
}

message Reply {
  oneof reply {
    RotaryEncoder_Reply rotary = 1;
    RotaryEncodersConfig_Reply config = 2;
    RotaryEncoderSnapshot_Reply rotary_snapshot = 3;
    RotaryEncoderHistoryVerify_Reply history_verify = 4;
  }
}

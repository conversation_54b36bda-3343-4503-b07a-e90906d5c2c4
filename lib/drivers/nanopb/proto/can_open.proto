syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/error.proto";
import "generated/lib/drivers/nanopb/proto/ack.proto";

package can_open;
option go_package = "nanopb/can_open";

// Requests

message SDO_Request {
    uint32 index = 1;
    uint32 subindex = 2;
    uint32 value = 3;
    uint32 cs = 4;
    uint32 expedited = 5;
}

message PDO_Request {
    uint32 func = 1;
    uint32 size = 2;
    bytes data = 3;
}

message RTR_PDO_Request {
    uint32 func = 1;
}

message NMT_Request {
    uint32 state = 1;
}

message Await_Request {
    uint32 timeout_ms = 1;
    uint32 func = 2;
}

message SDO_Download_Request {
    uint32 index = 1;
    uint32 subindex = 2;
    uint32 value = 3;
}

message SDO_Upload_Request {
    uint32 index = 1;
    uint32 subindex = 2;
}

message NMT_Reset_Request {

}

message NMT_Start_Request {

}

message NMT_Stop_Request {

}

// Replies

message ACK_Reply {

}

message SDO_Packet {
    uint32 spec = 1;
    uint32 index = 2;
    uint32 subindex = 3;
    bytes data = 4;
}

message PDO_Packet {
    bytes data = 1;
}

message NMT_Packet {
    uint32 state = 1;
    uint32 node_id = 2;
}

message Message_Reply {
    uint32 func = 1;
    uint32 node_id = 2;
    oneof pkt {
        SDO_Packet sdo = 3;
        PDO_Packet pdo = 4;
        NMT_Packet nmt = 5;
    }
}

// Mains

message Request {
    oneof request {
        SDO_Request sdo = 1;
        PDO_Request pdo = 2;
        RTR_PDO_Request rtr = 3;
        NMT_Request nmt = 4;
        Await_Request await = 5;
        SDO_Download_Request sdo_download = 6;
        SDO_Upload_Request sdo_upload = 7;
        NMT_Reset_Request reset = 8;
        NMT_Start_Request start = 9;
        NMT_Stop_Request stop = 10;
    }
}

message Reply {
    oneof reply {
        ack.Ack ack = 1;
        Message_Reply msg = 2;
        error.Error error = 3;
    }
}
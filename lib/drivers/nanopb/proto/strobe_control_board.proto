syntax = "proto3";

import "generated/lib/drivers/nanopb/proto/diagnostic.proto";
import "generated/lib/drivers/nanopb/proto/request.proto";
import "generated/lib/drivers/nanopb/proto/strobe_control.proto";
import "generated/lib/drivers/nanopb/proto/camera_power_control.proto";
import "generated/lib/drivers/nanopb/proto/version.proto";
import "generated/lib/drivers/nanopb/proto/time.proto";

package strobe_control_board;
option go_package="nanopb/strobe_control_board";

message Reply {
  request.RequestHeader header = 1;
  oneof reply {
    diagnostic.Pong pong = 2;
    strobe_control.Reply strobe_control = 3;
    camera_power_control.Reply camera_power_control = 4;
    version.Version_Reply version = 5;
    time.Reply time = 6;
  }
}

message Request {
  request.RequestHeader header = 1;
  oneof request {
    diagnostic.Ping ping = 2;
    strobe_control.Request strobe_control = 3;
    camera_power_control.Request camera_power_control = 4;
    version.Version_Request version = 5;
    version.Reset_Request reset = 6;
    time.Request time = 7;
  }
}

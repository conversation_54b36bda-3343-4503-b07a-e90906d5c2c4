syntax = "proto3";

package gps;
option go_package = "nanopb/gps";

message Position_Request {
}

message Spartn_Request {
  bytes data = 1;
  bool end = 2;
}
message Spartn_Reply {
}

message Rtcm_Request {
  bytes data = 1;
  bool end = 2;
}
message Rtcm_Reply {
}

message ValueWithAccuracy {
    double value = 1;
    double accuracy = 2;
};

enum CarrierPhaseSoln {
    NONE = 0;
    FLOATING = 1;
    FIXED = 2;
};

// Relative position information from secondary gps on dual gps board
message DualGpsData {
    bool gnss_valid = 1;
    bool diff_corrections = 2;
    bool is_moving_base = 3;
    CarrierPhaseSoln carrier_phase = 4;

    // Timestamp of this fix (if valid)
    uint64 timestamp_ms = 5;

    // North component of relative position vector (mm)
    optional ValueWithAccuracy north = 6;
    // East component of relative position vector (mm)
    optional ValueWithAccuracy east = 7;
    // Down component of relative position vector (mm)
    optional ValueWithAccuracy down = 8;
    // Relative position vector, the distance between antennas (mm)
    optional ValueWithAccuracy length = 9;
    // Heading of travel (°)
    optional ValueWithAccuracy heading = 10;
};

message Position_Reply {
  bool have_fix = 1;
  double latitude = 2;
  double longitude = 3;
  int32 num_sats = 4; 
  float hdop = 5;
  // Only valid if fix is present
  uint64 timestamp_ms = 6;
  int32 height_mm = 7;
  bool have_approx_fix = 8;
  int32 fix_type = 9;
  int32 fix_flags = 10;

  optional DualGpsData dual = 11;
}

message HeadingCorrection_Request {
  float heading_offset = 1;
}
message HeadingCorrection_Reply {
}

message GetLastGga_Request {

};
message GetLastGga_Reply {
    string raw_sentence = 1;
};

message Request {
  oneof request {
    Position_Request position = 1;
    Spartn_Request spartn = 2;
    HeadingCorrection_Request heading_correction = 3;
    Rtcm_Request rtcm = 4;
    GetLastGga_Request gga = 5;
    // TODO: satellite information
  }
}

message Reply {
  oneof reply {
    Position_Reply position = 1;
    Spartn_Reply spartn = 2;
    HeadingCorrection_Reply heading_correction = 3;
    Rtcm_Reply rtcm = 4;
    GetLastGga_Reply gga = 5;
  }
}

ROOT_DIR      = ../../../..
REL_DIR       = lib/drivers/nanopb/proto
GENERATED_DIR = $(ROOT_DIR)/generated/$(REL_DIR)
GOLANG_GENERATED_DIR = $(ROOT_DIR)/golang/generated/proto/nanopb

define NEWLINE

endef

# This makefile will generate the python and nanopb protobuf files for all .proto protobuf files in this directory

.PHONY: all proto clean

all: proto

LOCAL_PROTOS:= $(wildcard *.proto)
LOCAL_OPTIONS:= $(wildcard *.options)
PROTOS:= $(LOCAL_PROTOS:%.proto=$(GENERATED_DIR)/%.proto)
OPTIONS:= $(LOCAL_OPTIONS:%.options=$(GENERATED_DIR)/%.options)
GO_PROTOS := $(LOCAL_PROTOS:%.proto=$(GOLANG_GENERATED_DIR)/%.proto)
PBGENS:= $(PROTOS:.proto=_pb2.py) $(PROTOS:.proto=_pb2.pyi) $(PROTOS:.proto=.pb.h) $(PROTOS:.proto=.pb.c) $(GO_PROTOS:.proto=.pb.go)
proto: $(PBGENS)

clean:
	bash -c 'rm -rf $(GENERATED_DIR)/*'
	bash -c 'rm -rf $(GOLANG_GENERATED_DIR)/*'
	bash -c '[ -e "$(GENERATED_DIR)/.gitignore" ] && rm -f $(GENERATED_DIR)/.gitignore || true'

.PRECIOUS: $(PBGENS) $(PROTOS) $(OPTIONS)

$(GENERATED_DIR)/.gitignore:
	bash -c 'mkdir -p $(GENERATED_DIR)'
	bash -c 'echo "*.proto" > $(GENERATED_DIR)/.gitignore'
	bash -c 'echo "*.options" >> $(GENERATED_DIR)/.gitignore'

$(GENERATED_DIR)/%.options: %.options | $(GENERATED_DIR)/.gitignore
	bash -c 'cp $< $@'

$(GENERATED_DIR)/%.proto: %.proto | $(GENERATED_DIR)/.gitignore
	bash -c 'cp $< $@'

$(GENERATED_DIR)/%_pb2.py: $(PROTOS)
	python -m grpc_tools.protoc $(subst _pb2.py,.proto, $@) --proto_path=. --experimental_allow_proto3_optional=True --proto_path=$(ROOT_DIR) --python_out=$(ROOT_DIR) --mypy_out=${ROOT_DIR}

# Requires Python package "grpcio-tools"
$(GENERATED_DIR)/%_pb2.pyi: $(PROTOS)
	python -m grpc_tools.protoc $(subst _pb2.pyi,.proto, $@) --proto_path=$(ROOT_DIR) --experimental_allow_proto3_optional=True --mypy_out=$(ROOT_DIR)

# There is a bug in the latest release of nanopb, partially fixed in "0.4.3.dev1172" with the size of fields
# The Nanopb change to stuct size was mad to accomodate C++, but unfortunately it destabilizes the c version.
# The sed command makes the unions a typedef so that the compiler detects them appropriately.
$(GENERATED_DIR)/%.pb.c $(GENERATED_DIR)/%.pb.h: $(PROTOS) $(OPTIONS)
	nanopb_generator --strip-path $(subst .pb.h,.proto, $@) -I $(ROOT_DIR) -D $(ROOT_DIR)
	cp $@ tmp_nanopb_sed_file_union
	bash -c 'sed "s/^union \([A-Za-z0-9_]*\) \(.*\);/typedef union \1 \2 \1;/g" tmp_nanopb_sed_file_union > $@'
	rm tmp_nanopb_sed_file_union

$(GOLANG_GENERATED_DIR)/%.pb.go: $(PROTOS) $(OPTIONS)
	protoc $(subst .pb.go,.proto, $(notdir $@)) -I . -I $(ROOT_DIR) --go_out=$(ROOT_DIR)/golang/generated/proto/
	$(eval TARGET_FILE := $(ROOT_DIR)/golang/generated/proto/nanopb/$(subst .pb.go,,$(notdir $@))/$(notdir $@))
	for pat in gps cruise diagnostic heading request ack error1 drive_solenoids park_brake rotary_encoder sensors time version dawg gimbal laser lens scanner_config epos servo can_open pulczar row_module scanner camera_power_control strobe_control jimbox_board hasselhoff_board arc_detector ots_tractor carbon_tractor reaper_module_controller hwinfo; do \
	  cp $(TARGET_FILE) tmp_nanopb_sed_file ; \
	  eval "sed -s -e 's|$${pat} \"nanopb|$${pat} \"github.com/carbonrobotics/robot/golang/generated/proto/nanopb|' tmp_nanopb_sed_file > $(TARGET_FILE)" ; \
	  rm tmp_nanopb_sed_file ; \
	done

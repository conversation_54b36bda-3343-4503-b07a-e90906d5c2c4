syntax = "proto3";

package gimbal;
option go_package = "nanopb/gimbal";

import "generated/lib/drivers/nanopb/proto/servo.proto";
import "generated/lib/drivers/nanopb/proto/error.proto";
import "generated/lib/drivers/nanopb/proto/ack.proto";
import "generated/lib/drivers/nanopb/proto/epos.proto";

// Requests

message Boot_Request {
  epos.Home_Params pan_params = 1;
  epos.Home_Params tilt_params = 2;
}

message Stop_Request {

}

message Servos_Request {
  servo.Request pan = 1;
  servo.Request tilt = 2;
}

message GetPositionAtRequest {
  uint64 timestamp_us = 1;
}

// Replies

message Servos_Reply {
  servo.Reply pan = 1;
  servo.Reply tilt = 2;
}
message PositionAt {
  int32 pan = 1;
  int32 tilt = 2;
  uint64 timestamp_us = 3;
  bool valid = 4;
}
message GetPositionAtReply {
  PositionAt requested = 1;
  PositionAt current = 2;
}

// Mains

message Request {
  oneof request {
    Boot_Request boot = 1;
    Stop_Request stop = 2;
    Servos_Request servos = 3;
    GetPositionAtRequest position = 4;
  }
}

message Reply {
  oneof reply {
    error.Error error = 1;
    ack.Ack ack = 2;
    Servos_Reply servos = 3;
    GetPositionAtReply position = 4;
  }
}
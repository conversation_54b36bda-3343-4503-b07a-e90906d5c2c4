import asyncio
import copy
import time
from argparse import Argument<PERSON><PERSON><PERSON>
from typing import Awaitable, Callable, List, Tuple

import numpy as np

import lib.common.logging
from firmware.release.firmware_release_manager import FirmwareVersion, get_latest_firmware_version
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import PulczarBoardConnector, PulczarServoPIDConfig
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector_validation import _make_pulczar_bootloader_ip
from lib.drivers.psoc_ethernet.psoc_ethernet_bootloader import program
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)


PORT = 4243
VELOCITY_MRPM = 590000
VELOCITY_SMALL_MRPM = 5000

# Disable Bare Except Restriction
# flake8: noqa: E722


def _make_pulczar_board_ip(scanner_id: int) -> str:
    return f"10.11.4.{scanner_id}"


async def _do_a_go_to_servo(func: Callable[[int, int, bool], Awaitable[None]], ticks: int) -> float:
    await func(0, VELOCITY_MRPM, True)
    start_time = time.time()
    await func(ticks, VELOCITY_MRPM, True)
    await func(0, VELOCITY_MRPM, True)
    final_time = time.time() - start_time
    return final_time / 2


async def get_best_velocity_for_move(delta_ticks: int) -> int:
    val = 7500 * (abs(delta_ticks) / 200)
    return int(min(val, VELOCITY_MRPM))


async def _calibrate(
    board: PulczarBoardConnector, position: int, window: int, time_window_ms: int, timeout_ms: int, period_ms: int
) -> Tuple[float, float]:
    window = min(window, position - 1)
    velocity = await get_best_velocity_for_move(position)
    try:
        await board.gimbal_go_to_calibrate(0, velocity, window, time_window_ms, timeout_ms, period_ms)
        pan1, tilt1 = await board.gimbal_go_to_calibrate(
            position, velocity, window, time_window_ms, timeout_ms, period_ms
        )
        pan2, tilt2 = await board.gimbal_go_to_calibrate(0, velocity, window, time_window_ms, timeout_ms, period_ms)
        return (pan1 + pan2) / 2, (tilt1 + tilt2) / 2
    except Exception as e:
        print(e)
        return (1000, 1000)


async def _do_a_go_to_pan(board: PulczarBoardConnector, ticks: int) -> float:
    return await _do_a_go_to_servo(board.gimbal_go_to_pan, ticks)


async def _do_a_go_to_tilt(board: PulczarBoardConnector, ticks: int) -> float:
    return await _do_a_go_to_servo(board.gimbal_go_to_tilt, ticks)


async def _evaluate_config(
    boards: List[PulczarBoardConnector],
    pan_config: PulczarServoPIDConfig,
    tilt_config: PulczarServoPIDConfig,
    window: int,
    time_window_ms: int,
    timeout_ms: int,
    period_ms: int,
    tick_list: List[int],
    n_samples: int = 100,
) -> Tuple[List[float], List[float]]:
    await asyncio.gather(*[board.gimbal_configure_pids(pan_config, tilt_config) for board in boards])
    pan_mean_results: List[float] = []
    tilt_mean_results: List[float] = []
    for ticks in tick_list:
        pan_results: List[float] = []
        tilt_results: List[float] = []
        for _ in range(n_samples):
            times = await asyncio.gather(
                *[_calibrate(board, ticks, window, time_window_ms, timeout_ms, period_ms) for board in boards]
            )
            times = list(filter(lambda tm: not (tm[0] == 0 and tm[1] == 0), times))
            pan_results.extend([tm[0] for tm in times])
            tilt_results.extend([tm[1] for tm in times])
        if len(pan_results) <= 0.90 * (n_samples * len(boards)):
            pan_mean_results.append(1000)  # 1 seconds is quite large so that test is probably disqualified
        if len(tilt_results) <= 0.90 * (n_samples * len(boards)):
            tilt_mean_results.append(1000)  # 1 seconds is quite large so that test is probably disqualified
        else:
            pan_mean_results.append(np.array(pan_results).mean())
            tilt_mean_results.append(np.array(tilt_results).mean())
    return pan_mean_results, tilt_mean_results


def _gen_search_param(center: float, fraction_margin: float, granularity: int) -> List[float]:
    params: List[float] = [center]
    if center == 0:
        return [0, 1, 10, 100]
    for i in range(1, granularity + 1):
        delta: float = (fraction_margin / granularity) * i
        high = center + (delta * center)
        if high >= 0:
            params.append(high)
        low = center - (delta * center)
        if low >= 0:
            params.append(low)
    return params


def _gen_pp_param(config: PulczarServoPIDConfig, fraction_margin: float, granularity: int) -> List[float]:
    return _gen_search_param(config.position_p, fraction_margin, granularity)


def _gen_pi_param(config: PulczarServoPIDConfig, fraction_margin: float, granularity: int) -> List[float]:
    return _gen_search_param(config.position_i, fraction_margin, granularity)


def _gen_pd_param(config: PulczarServoPIDConfig, fraction_margin: float, granularity: int) -> List[float]:
    return _gen_search_param(config.position_d, fraction_margin, granularity)


def _gen_pffv_param(config: PulczarServoPIDConfig, fraction_margin: float, granularity: int) -> List[float]:
    return _gen_search_param(config.position_ffv, fraction_margin, granularity)


def _gen_pffa_param(config: PulczarServoPIDConfig, fraction_margin: float, granularity: int) -> List[float]:
    return _gen_search_param(config.position_ffa, fraction_margin, granularity)


def _pp_config_updater(config: PulczarServoPIDConfig, value: float) -> None:
    config.position_p = int(value)


def _pi_config_updater(config: PulczarServoPIDConfig, value: float) -> None:
    config.position_i = int(value)


def _pd_config_updater(config: PulczarServoPIDConfig, value: float) -> None:
    config.position_d = int(value)


def _pffv_config_updater(config: PulczarServoPIDConfig, value: float) -> None:
    config.position_ffv = int(value)


def _pffa_config_updater(config: PulczarServoPIDConfig, value: float) -> None:
    config.position_ffa = int(value)


async def _search_in_param(
    boards: List[PulczarBoardConnector],
    tick_list: List[int],
    pan_config: PulczarServoPIDConfig,
    tilt_config: PulczarServoPIDConfig,
    window: int,
    time_window_ms: int,
    timeout_ms: int,
    period_ms: int,
    fraction_margin: float,
    granularity: int,
    param_gen: Callable[[PulczarServoPIDConfig, float, int], List[float]],
    updater: Callable[[PulczarServoPIDConfig, float], None],
    n_samples: int,
) -> Tuple[Tuple[float, PulczarServoPIDConfig], Tuple[float, PulczarServoPIDConfig]]:
    pan_params = param_gen(pan_config, fraction_margin, granularity)
    tilt_params = param_gen(tilt_config, fraction_margin, granularity)
    pan_best_score = 1000
    tilt_best_score = 1000
    pan_best_config = copy.deepcopy(pan_config)
    tilt_best_config = copy.deepcopy(tilt_config)
    for pan_param, tilt_param in zip(pan_params, tilt_params):
        updater(pan_config, pan_param)
        updater(tilt_config, tilt_param)
        print(f"Evaluating Pan Config: {pan_config}, Tilt Config: {tilt_config}")
        pan_settle_times, tilt_settle_times = await _evaluate_config(
            boards,
            pan_config=pan_config,
            tilt_config=tilt_config,
            window=window,
            time_window_ms=time_window_ms,
            timeout_ms=timeout_ms,
            period_ms=period_ms,
            tick_list=tick_list,
            n_samples=n_samples,
        )
        print(f"Pan Results: {pan_settle_times}, Tilt Results: {tilt_settle_times}")
        pan_score = np.array(pan_settle_times).mean()
        tilt_score = np.array(tilt_settle_times).mean()
        print(f"Pan Score: {pan_score}, Tilt Score: {tilt_score}")
        if pan_score < pan_best_score:
            pan_best_config = copy.deepcopy(pan_config)
            pan_best_score = pan_score
        if tilt_score < tilt_best_score:
            tilt_best_config = copy.deepcopy(tilt_config)
            tilt_best_score = tilt_score
    return (pan_best_score, pan_best_config), (tilt_best_score, tilt_best_config)


BEST_2MM_PAN_PID_CONFIG = PulczarServoPIDConfig(  # 29.5 ms
    current_p=1540716,
    current_i=5019039,
    position_p=4213599,
    position_i=113617088,
    position_d=33640,
    position_ffv=7,
    position_ffa=126,
)

BEST_2MM_TILT_PID_CONFIG = PulczarServoPIDConfig(  # 26.5 ms
    current_p=1540716,
    current_i=5019039,
    position_p=7372288,
    position_i=51367680,
    position_d=50199,
    position_ffv=120,
    position_ffa=121,
)

BEST_1MM_PAN_PID_CONFIG = PulczarServoPIDConfig(  # 32.6 ms
    current_p=1540716,
    current_i=5019039,
    position_p=3916205,
    position_i=131933979,
    position_d=33640,
    position_ffv=4,
    position_ffa=126,
)

BEST_1MM_TILT_PID_CONFIG = PulczarServoPIDConfig(  # 33.1 ms
    current_p=1540716,
    current_i=5019039,
    position_p=7542734,
    position_i=87772980,
    position_d=44375,
    position_ffv=109,
    position_ffa=128,
)

BEST_9T_PAN_PID_CONFIG = PulczarServoPIDConfig(  # 66.8 ms
    current_p=1540716,
    current_i=5019039,
    position_p=4215402,
    position_i=383974053,
    position_d=19763,
    position_ffv=2,
    position_ffa=135,
)

BEST_9T_TILT_PID_CONFIG = PulczarServoPIDConfig(  # 80.6 ms
    current_p=1540716,
    current_i=5019039,
    position_p=9926237,
    position_i=126949219,
    position_d=58575,
    position_ffv=109,
    position_ffa=135,
)

BEST_9T_LARGE_SMALL_MOVE_PAN_PID_CONFIG = PulczarServoPIDConfig(  # 49.7 ms
    current_p=1540716,
    current_i=5019039,
    position_p=3409167,
    position_i=786065900,
    position_d=42538,
    position_ffv=0,
    position_ffa=139,
)
BEST_9T_LARGE_SMALL_MOVE_TILT_PID_CONFIG = PulczarServoPIDConfig(  # 61.7 ms
    current_p=1540716,
    current_i=5019039,
    position_p=4058447,
    position_i=388017561,
    position_d=26069,
    position_ffv=119,
    position_ffa=57,
)

SLAYER_EPOS_PAN_PID_CONFIG = PulczarServoPIDConfig(
    current_p=1474745,
    current_i=4633577,
    position_p=3999999,
    position_i=117833155,
    position_d=41305,
    position_ffv=3956,
    position_ffa=171,
)

SLAYER_EPOS_TILT_PID_CONFIG = PulczarServoPIDConfig(
    current_p=1450318,
    current_i=4945111,
    position_p=4196389,
    position_i=143726128,
    position_d=38007,
    position_ffv=2834,
    position_ffa=132,
)

SLAYER_9T_PAN_PID_CONFIG = PulczarServoPIDConfig(  # 66.8 ms
    current_p=1474745,
    current_i=4633577,
    position_p=4215402,
    position_i=383974053,
    position_d=19763,
    position_ffv=2,
    position_ffa=135,
)

SLAYER_9T_TILT_PID_CONFIG = PulczarServoPIDConfig(  # 80.6 ms
    current_p=1450318,
    current_i=4945111,
    position_p=9926237,
    position_i=126949219,
    position_d=58575,
    position_ffv=109,
    position_ffa=135,
)


async def _tune_servos(
    boards: List[PulczarBoardConnector],
    scanner_id: str,
    window: int,
    time_window_ms: int,
    timeout_ms: int,
    period_ms: int,
    n_samples: int,
    n_loops: int,
) -> bool:
    # Since the below algorithm is greedy, we can start from the latest result if it exists
    best_pan = copy.deepcopy(SLAYER_9T_PAN_PID_CONFIG)
    try:
        best_pan = PulczarServoPIDConfig.read_from_file(f"best_pan_s{scanner_id}")
        LOG.info(f"Read Pan Config From File: {best_pan}")
    except:
        pass

    best_tilt = copy.deepcopy(SLAYER_9T_TILT_PID_CONFIG)
    try:
        best_tilt = PulczarServoPIDConfig.read_from_file(f"best_tilt_s{scanner_id}")
        LOG.info(f"Read Tilt Config From File: {best_tilt}")
    except:
        pass
    original_configs = {
        "pan": best_pan,
        "tilt": best_tilt,
    }

    for board in boards:
        print("Setting Up Defaults Pids...")
        await board.clear_config()
        await board.gimbal_config(1, 2, 590000, 1000, 25, 300)
        await board.gimbal_configure_pids(best_pan, best_tilt)
        await board.gimbal_boot(590000, 0, 30000, 1000, (1000, 1000))
        print("Gimbal Booted and Ready")

        limit_pan, limit_tilt = await board.gimbal_get_limits()
        print(f"Limits: {(limit_pan, limit_tilt)}")

        assert 14000 > limit_pan[1] > 10000 and 14000 > limit_tilt[1] > 10000

    tick_list = [
        5000,
        1000,
        500,
        100,
    ]  # Same number more than once to add more weight to small moves

    param_search_funcs = [
        (_gen_pp_param, _pp_config_updater),
        (_gen_pi_param, _pi_config_updater),
        (_gen_pd_param, _pd_config_updater),
        (_gen_pffv_param, _pffv_config_updater),
        (_gen_pffa_param, _pffa_config_updater),
    ]

    search_factors = [(0.4, 5), (0.3, 5), (0.2, 5), (0.1, 5), (0.05, 5)]

    start_time = time.time()

    pan_best_score: float = 1000
    tilt_best_score: float = 1000
    pan_best_config = copy.deepcopy(original_configs["pan"])
    tilt_best_config = copy.deepcopy(original_configs["tilt"])
    time_str = time.strftime("%Y_%m_%d_%H_%M_%S")
    with open(f"servo_tuning_s{scanner_id}_{time_str}", "w") as f:
        f.write(
            f"Calibrate Parameters: Window: {window}, Time Window Ms: {time_window_ms}, Timeout: {timeout_ms}, Period Ms: {period_ms}\n"
        )
        f.write(f"Sampling Parameters: N Samples: {n_samples}, N Loops: {n_loops}\n")
        f.write(f"Tick List: {tick_list}\n")
        f.write(f"Search Factors: {search_factors}\n")
        for i in range(n_loops):
            for _fraction_margin, _granularity in search_factors:
                for _param_gen, _updater in param_search_funcs:
                    (pan_new_score, pan_new_config), (tilt_new_score, tilt_new_config) = await _search_in_param(
                        boards,
                        tick_list=tick_list,
                        pan_config=copy.deepcopy(pan_best_config),
                        tilt_config=copy.deepcopy(tilt_best_config),
                        window=window,
                        time_window_ms=time_window_ms,
                        timeout_ms=timeout_ms,
                        period_ms=period_ms,
                        fraction_margin=_fraction_margin,
                        granularity=_granularity,
                        param_gen=_param_gen,
                        updater=_updater,
                        n_samples=n_samples,
                    )
                    if pan_new_score < pan_best_score:
                        pan_best_config = copy.deepcopy(pan_new_config)
                        pan_best_score = pan_new_score
                    if tilt_new_score < tilt_best_score:
                        tilt_best_config = copy.deepcopy(tilt_new_config)
                        tilt_best_score = tilt_new_score
                print(
                    f"Loop: {i}, Margin: {_fraction_margin}, Granularity: {_granularity}, Pan Best Config: {pan_best_config}, Pan Score: {pan_best_score} Tilt Best Config: {tilt_best_config}, Tilt Score: {tilt_best_score}"
                )
                f.write(
                    f"Loop: {i}, Margin: {_fraction_margin}, Granularity: {_granularity}, Pan Best Config: {pan_best_config}, Pan Score: {pan_best_score} Tilt Best Config: {tilt_best_config}, Tilt Score: {tilt_best_score}\n"
                )
                f.flush()

            print(
                f"Pan Best Config: {pan_best_config}, Pan Score: {pan_best_score}, Tilt Best Config: {tilt_best_config}, Tilt Score: {tilt_best_score}"
            )
            f.write(
                f"Pan Best Config: {pan_best_config}, Pan Score: {pan_best_score}, Tilt Best Config: {tilt_best_config}, Tilt Score: {tilt_best_score}\n"
            )
            pan_best_config.write_to_file(f"best_pan_s{scanner_id}")
            tilt_best_config.write_to_file(f"best_tilt_s{scanner_id}")

    duration = time.time() - start_time
    print(f"Total Tuning Duration: {duration} seconds")

    return True


async def _reset_scanner(scanner_id: int) -> None:
    ip = _make_pulczar_board_ip(scanner_id)
    connector = PsocMEthernetConnector(ip, PORT, asyncio.get_event_loop())
    await connector.open()
    board = PulczarBoardConnector(connector)
    await board.hard_reset()


async def _flash_latest_firmware(scanner_id: int) -> FirmwareVersion:
    ip = _make_pulczar_bootloader_ip(scanner_id)
    firmware = await asyncio.get_event_loop().run_in_executor(None, lambda: get_latest_firmware_version("Pulczar"))
    assert firmware is not None
    print(f"Flashing Firmware: {firmware.path}")
    await _reset_scanner(scanner_id)
    await asyncio.sleep(2)
    print(f"Reaching Bootloader at IP: {ip}")
    await program(ip, PORT, firmware.path)
    print(f"Successfully Programmed Firmware Version: {firmware.version}")
    print("Rebooting Board... (May take several seconds)")
    await asyncio.sleep(7)
    return firmware.version


async def _get_boards(scanner_id: str, no_update: bool) -> PulczarBoardConnector:
    id = int(scanner_id)
    if not no_update:
        latest_version = await _flash_latest_firmware(id)
    ip = _make_pulczar_board_ip(id)
    connector = PsocMEthernetConnector(ip, PORT, asyncio.get_event_loop())
    await connector.open()
    print(f"Ethernet Connector Successfully Opened")
    board = PulczarBoardConnector(connector)
    if not no_update:
        version = await board.get_version()
        assert version.is_equal(latest_version)
    return board


async def _tune(
    scanner_ids: str,
    window: int,
    time_window_ms: int,
    timeout_ms: int,
    period_ms: int,
    n_samples: int,
    n_loops: int,
    no_update: bool,
) -> None:
    ids = scanner_ids.split(",")

    boards = await asyncio.gather(*[_get_boards(id, no_update) for id in ids])

    await _tune_servos(
        boards,
        scanner_ids.replace(",", "_"),
        window=window,
        time_window_ms=time_window_ms,
        timeout_ms=timeout_ms,
        period_ms=period_ms,
        n_samples=n_samples,
        n_loops=n_loops,
    )


def main() -> None:
    parser = ArgumentParser("Pulczar Board Servo Tuner")
    parser.add_argument(
        "-s",
        "--scanners",
        type=str,
        help="comma separated list of scanners to run on (Default:1,2,3,4,5,6,7,8)",
        default="1,2,3,4,5,6,7,8",
    )
    parser.add_argument("-w", "--window", type=int, help="Tick Settle Window (Default: 25)", default=25)
    parser.add_argument("-t", "--time-window", type=int, help="Time ms Window to maintain (Default: 100)", default=100)
    parser.add_argument("--timeout", type=int, help="Timeout in ms (Default: 1000)", default=1000)
    parser.add_argument("--period", type=int, help="Polling Period in ms (Default: 3)", default=3)
    parser.add_argument("--samples", type=int, help="Number of Samples per scanner per move (Default: 2)", default=2)
    parser.add_argument("--loops", type=int, help="Number of search loops (Default: 5)", default=5)
    parser.add_argument(
        "--no-update", help="Do not check try to update firmware on board", default=False, action="store_true",
    )
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _tune(
        scanner_ids=args.scanners,
        window=args.window,
        time_window_ms=args.time_window,
        timeout_ms=args.timeout,
        period_ms=args.period,
        n_samples=args.samples,
        n_loops=args.loops,
        no_update=args.no_update,
    )
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

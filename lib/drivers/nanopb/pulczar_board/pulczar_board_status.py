import asyncio
from argparse import ArgumentParser

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import PulczarBoardConnector
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)


PORT = 4243
SERVO_HIGH = 10000
REBOOT_TIME_S = 7


def _make_pulczar_board_ip(scanner_id: int) -> str:
    return f"10.11.4.{scanner_id}"


async def _print_status(scanner_id: int) -> None:
    ip = _make_pulczar_board_ip(scanner_id)
    connector = PsocMEthernetConnector(_make_pulczar_board_ip(scanner_id), PORT, asyncio.get_event_loop())
    print(f"Opening Ethernet Connector at IP: {ip}, All ADDR Pin should be connected")
    await connector.open()
    print("Ethernet Connector Successfully Opened")
    board = PulczarBoardConnector(connector)
    status = await board.get_status()
    print(status)


def main() -> None:
    parser = ArgumentParser("Pulczar Board Status")
    parser.add_argument("-s", "--scanner", default=1, type=int)
    args = parser.parse_args()
    lib.common.logging.init_log(level="DEBUG")
    future = _print_status(args.scanner)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

import asyncio
import time
from argparse import ArgumentParser

import lib.common.logging
from firmware.release.firmware_release_manager import Firm<PERSON>V<PERSON><PERSON>, get_latest_firmware_version
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.errors.error import RetryableMakaDeviceException
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import PulczarBoardConnector, PulczarServoPIDConfig
from lib.drivers.psoc_ethernet.psoc_ethernet_bootloader import program
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)


PORT = 4243
SERVO_HIGH = 10000
REBOOT_TIME_S = 7

# Disable Bare Except Restriction and Function Complexity
# flake8: noqa: E722, C901


def _make_pulczar_board_ip(scanner_id: int) -> str:
    return f"10.11.4.{scanner_id}"


def _make_pulczar_bootloader_ip(scanner_id: int) -> str:
    return f"10.11.4.{scanner_id + 10}"


async def _ping_test(board: PulczarBoardConnector) -> None:
    print("Starting Connectivity Test...")
    count = 0
    while True:
        count += 1
        try:
            await board.ping()
            break
        except RetryableMakaDeviceException:
            if count > 10:
                raise
    print("Ping Connectivity Successful")


async def _continue_or_skip() -> bool:
    cmd = await asyncio.get_event_loop().run_in_executor(None, input)
    if cmd == "skip":
        print("Skipping Test")
        return True
    return False


async def _status_test(board: PulczarBoardConnector) -> bool:
    print("Door Test...")
    print("Door has been cut from the design, so Door pin should be jumped")
    while True:
        status = await board.get_status()
        if not status.door:
            print(f"Failure, DOOR is False: \n{status}\n Please ensure door pin is jumped and hit enter or type 'skip'")
            if await _continue_or_skip():
                break
        else:
            print("Door Test Successful")
            break

    print("All Low Status Test...")
    await board.set_override(water=False, door=False, flow=False, stf_alt=False)
    while True:
        status = await board.get_status()
        if status.arm or status.flow or status.water or status.fire or status.dog or status.power:
            print(
                f"Failure, not all statuses False: \n{status}\n Please disable all status signal except DOOR and hit enter or type 'skip'"
            )
            if await _continue_or_skip():
                break
        else:
            print("Low Status Test Successful")
            break

    print("Testing Overrides...")
    await board.set_override(water=False, door=False, flow=False, stf_alt=False)
    await board.set_override(water=True, door=False, flow=False, stf_alt=False)
    status = await board.get_status()
    if not status.water:
        print("Water Override Failed")

    await board.set_override(water=False, door=False, flow=False, stf_alt=False)
    print("Overrides Test Successful")

    print("All High Status Test...")
    while True:
        status = await board.get_status()
        if not status.flow or not status.water or not status.door or not status.power:
            print(
                f"Failure, not all of [FLOW, WATER, DOOR, POWER] are True: \n{status}\n Please enable all status signal so they show up as True and hit enter or type 'skip'"
            )
            if await _continue_or_skip():
                break
        else:
            print("High Status Test Successful")
            break

    print("Safe To Fire Test...")
    print("Please ensure that STF LED is currently OFF, and hit enter.")
    if await _continue_or_skip():
        return False

    running: bool = True
    firing = False

    async def _watch_dog() -> None:
        await board.dawg_config(1000)
        value: int = 0
        while running:
            await board.laser_intensity(value % (2 ** 16))
            value += 5000
            await board.dawg_pet(firing)
            await asyncio.sleep(0.300)

    task = asyncio.get_event_loop().create_task(_watch_dog())
    try:
        await board.dawg_arm(True)
        firing = True
        await board.laser_set(firing)
        print("Please ensure that STF, ARM, L_FIRE and DOG LEDs are on, and hit enter.")
        if await _continue_or_skip():
            return False

        print(
            "Please ensure that FIRE, STF and INTENSITY (ramping) are ON on the Laser Power Supply Board, and hit enter."
        )
        if await _continue_or_skip():
            return False
    finally:
        running = False
        await board.dawg_arm(False)
        firing = False
        await board.laser_set(firing)
        await task

    return True


async def _scanner_test(board: PulczarBoardConnector) -> bool:
    print("Gimbal/Servos Test...")
    await board.clear_config()
    await board.gimbal_config(1, 2, 590000, 1000, 50, 300)
    print("Gimbal Configured")
    await board.gimbal_configure_pids(*PulczarServoPIDConfig.default_pids())
    print("Configured Gimbal PID Values")
    start_time = time.time()
    await board.gimbal_boot(590000, 0, 30000, 1000, (1000, 1000))
    print(f"Gimbal Booted in {time.time() - start_time} seconds")
    await board.clear_gimbal_fault()

    limit_pan, limit_tilt = await board.gimbal_get_limits()
    print(f"Limits: {(limit_pan, limit_tilt)}")

    if limit_pan[1] < SERVO_HIGH or limit_tilt[1] < SERVO_HIGH:
        print(f"Servos Limits Should be above {SERVO_HIGH}, If limits are too low, please mark as failure")
        await _continue_or_skip()
        return False

    await board.gimbal_go_to((limit_pan[0], limit_tilt[0]), (590000, 590000), True)
    await asyncio.sleep(0.2)
    await board.gimbal_go_to((limit_pan[1], limit_tilt[1]), (590000, 590000), True)
    await asyncio.sleep(0.2)
    print("Successfully went to limits")

    await board.gimbal_go_to((int(limit_pan[1] / 2), int(limit_tilt[1] / 2)), (590000, 590000), True)
    await asyncio.sleep(0.2)
    print("Successfully Centered Servos")

    await board.gimbal_stop()
    print("Gimbal Test Successful")

    return True


async def _lens_test(board: PulczarBoardConnector) -> bool:
    print("Lens and Camera Test...")

    print("Ensure that the Front Panel Fan is pushing air inward and the camera fan is turned on.")

    if await _continue_or_skip():
        return False

    print("Ensure both lock screws in the front panel have no defect.")

    if await _continue_or_skip():
        return False

    print("Open Pylon Viewer (>= 6.1.1 with GigE support), Please install from Basler website if you do not have it.")
    print("Scan, Detect and Connect to Camera, Start Continuous Shot. Hit Enter when you can see image stream")

    if await _continue_or_skip():
        return False

    print("Setup Line 2 for Exposure Active as Line Source in Digital I/O. Hit Enter")

    if await _continue_or_skip():
        return False

    print("Watch Oscilloscope and ensure that exposure time square waves are displayed right. Hit Enter")

    if await _continue_or_skip():
        return False

    running: bool = True

    async def _lens_flip_flop() -> None:
        target = 0
        step = 1
        while running:
            await board.lens_set(target)
            if await board.lens_get() != target:
                raise Exception("Failed to Set Lens Value")
            target = (target + step) % 256
            await asyncio.sleep(0.05)

    task = asyncio.get_event_loop().create_task(_lens_flip_flop())
    try:
        print(
            'Look at Image Stream and Ensure it focuses at both the far (42") and near target (27") as it rolls through the different focus values (You will need the light turned on) Then Hit Enter'
        )
        if await _continue_or_skip():
            return False

        print(
            "Look at Image Stream and Ensure that there is no ghosting. (You will need the light turned on) Then Hit Enter"
        )
        if await _continue_or_skip():
            return False
    finally:
        running = False
        await task

    print("Activate Trigger From Signal Generator at 10 Hz")

    if await _continue_or_skip():
        return False

    print(
        "In Acquisition Controls, set Trigger Selector: Frame Start, Source: Line1, Activation: Rising Edge then set Trigger Mode to On. Hit Enter"
    )

    if await _continue_or_skip():
        return False

    print("Confirm that the FPS is currently at 10 (or whatever the signal generator is set to), then Hit Enter")

    if await _continue_or_skip():
        return False

    print("Lens and Camera Test Succeeded")

    return True


async def _scanner_id_test(board: PulczarBoardConnector, scanner_id: int) -> PulczarBoardConnector:
    print(f"Testing Scanner ID {scanner_id}")
    await board.hard_reset()
    await asyncio.sleep(REBOOT_TIME_S)
    ip = _make_pulczar_board_ip(scanner_id)
    connector = PsocMEthernetConnector(ip, PORT, asyncio.get_event_loop())
    await connector.open()
    board = PulczarBoardConnector(connector)
    await _ping_test(board)
    print(f"Scanner ID {scanner_id} Test Successful")
    return board


async def _scanner_addr_test(board: PulczarBoardConnector, scanner_id: int, msg: str) -> PulczarBoardConnector:
    while True:
        print(msg)

        if await _continue_or_skip():
            break

        await _reset_all()

        try:
            board = await _scanner_id_test(board, scanner_id)
            break
        except:
            print("Failed to Test Address, try again")

    return board


async def _addr_test(board: PulczarBoardConnector) -> bool:
    board = await _scanner_addr_test(board, 2, "Connect ADDR1 and ADDR2, disconnect ADDR0, then Hit Enter")
    board = await _scanner_addr_test(board, 3, "Connect ADDR0 and ADDR2, disconnect ADDR1, then Hit Enter")
    board = await _scanner_addr_test(board, 5, "Connect ADDR0 and ADDR1, disconnect ADDR2, then Hit Enter")
    board = await _scanner_addr_test(board, 1, "Connect ADDR1, ADDR2 and ADDR0, then Hit Enter")

    print("ADDR Test Successful")
    return True


async def _reset_all() -> None:
    for i in range(8):
        scanner_id = i + 1
        ip = _make_pulczar_board_ip(scanner_id)
        connector = PsocMEthernetConnector(ip, PORT, asyncio.get_event_loop())
        await connector.open()
        board = PulczarBoardConnector(connector)
        await board.hard_reset()


async def _test_bad_firmware(scanner_id: int) -> None:
    print("Testing Bad Firmware...")
    ip = _make_pulczar_bootloader_ip(scanner_id)
    firmware = await asyncio.get_event_loop().run_in_executor(
        None, lambda: get_latest_firmware_version("BadPulczarTest")
    )
    assert firmware is not None
    print(f"Flashing Bad Firmware: {firmware.path}")
    await _reset_all()
    await asyncio.sleep(2)
    print(f"Reaching Bootloader at IP: {ip}")
    await program(ip, PORT, firmware.path)
    print("Successfully Programmed Bad Firmware...")
    print("Rebooting Board... (May take several seconds)")
    await asyncio.sleep(REBOOT_TIME_S)
    ip = _make_pulczar_board_ip(scanner_id)
    connector = PsocMEthernetConnector(_make_pulczar_board_ip(scanner_id), PORT, asyncio.get_event_loop())
    await connector.open()
    board = PulczarBoardConnector(connector)
    try:
        await board.ping()
    except AssertionError:
        pass
    print("Bad Firmware Test Successful")


async def _flash_latest_firmware(scanner_id: int) -> FirmwareVersion:
    ip = _make_pulczar_bootloader_ip(scanner_id)
    firmware = await asyncio.get_event_loop().run_in_executor(None, lambda: get_latest_firmware_version("Pulczar"))
    assert firmware is not None
    print(f"Flashing Firmware: {firmware.path}")
    await _reset_all()
    await asyncio.sleep(2)
    print(f"Reaching Bootloader at IP: {ip}")
    await program(ip, PORT, firmware.path)
    print(f"Successfully Programmed Firmware Version: {firmware.version}")
    print("Rebooting Board... (May take several seconds)")
    await asyncio.sleep(REBOOT_TIME_S)
    return firmware.version


async def _validate(scanner_id: int) -> None:
    await _test_bad_firmware(scanner_id)
    latest_version = await _flash_latest_firmware(scanner_id)
    ip = _make_pulczar_board_ip(scanner_id)
    connector = PsocMEthernetConnector(_make_pulczar_board_ip(scanner_id), PORT, asyncio.get_event_loop())
    print(f"Opening Ethernet Connector at IP: {ip}, All ADDR Pin should be connected")
    await connector.open()
    print(f"Ethernet Connector Successfully Opened")
    board = PulczarBoardConnector(connector)
    result = True
    await _ping_test(board)
    version = await board.get_version()
    assert version.is_equal(latest_version)
    await board.clear_config()
    result = result and await _scanner_test(board)
    result = result and await _status_test(board)
    result = result and await _lens_test(board)
    result = result and await _addr_test(board)

    if result:
        print("PULCZAR SCANNER VALIDATION SUCCESSFUL")
    else:
        print("FAIL FAIL FAIL FAIL")


def main() -> None:
    parser = ArgumentParser("Pulczar Board Validator")
    parser.add_argument("-s", "--scanner", default=1, type=int)
    args = parser.parse_args()
    lib.common.logging.init_log(level="DEBUG")
    future = _validate(args.scanner)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

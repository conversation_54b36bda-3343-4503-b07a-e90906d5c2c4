import asyncio
import copy
import json
import math
import time
from dataclasses import dataclass
from enum import Enum, IntEnum, auto
from typing import TYPE_CHECKING, Any, Dict, List, NamedTuple, Optional, Tuple, cast

from typing_extensions import Self

import generated.lib.drivers.nanopb.proto.arc_detector_pb2 as arc_detector_pb
import generated.lib.drivers.nanopb.proto.can_open_pb2 as can_pb
import generated.lib.drivers.nanopb.proto.dawg_pb2 as dawg_pb
import generated.lib.drivers.nanopb.proto.epos_pb2 as epos_pb
import generated.lib.drivers.nanopb.proto.gimbal_pb2 as gimbal_pb
import generated.lib.drivers.nanopb.proto.laser_pb2 as laser_pb
import generated.lib.drivers.nanopb.proto.lens_pb2 as lens_pb
import generated.lib.drivers.nanopb.proto.pulczar_pb2 as pulczar_pb
import generated.lib.drivers.nanopb.proto.scanner_config_pb2 as scanner_config_pb
import generated.lib.drivers.nanopb.proto.servo_pb2 as servo_pb
import generated.lib.drivers.nanopb.proto.time_pb2 as time_pb
import generated.lib.drivers.nanopb.proto.version_pb2 as version_pb
import lib.common.logging
from firmware.release.firmware_release_manager import FirmwareVersion
from generated.lib.drivers.nanopb.proto.hwinfo_pb2 import BoardVersionRequest
from generated.lib.drivers.nanopb.proto.hwinfo_pb2 import Request as HwInfoRequest
from generated.lib.drivers.nanopb.proto.pulczar_board_pb2 import Reply, Request
from lib.common.error import MakaException
from lib.common.generation import GENERATION, generation, is_bud, is_reaper, is_slayer
from lib.common.time import maka_control_timestamp_ms
from lib.common.units.current import Current
from lib.common.units.temperature import Temperature
from lib.common.units.voltage import Voltage
from lib.drivers.errors.error import RetryableMakaDeviceException
from lib.drivers.nanopb.bootloadable_connector import BootloadableConnector
from lib.drivers.nanopb.nano_connector import NanoPbConnector
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

LOG = lib.common.logging.get_logger(__name__)

PULCZAR_PORT = 4243

if TYPE_CHECKING:
    GoToModeType = servo_pb.GoToModeValue
else:
    GoToModeType = servo_pb.GoToMode


def make_pulczar_board_ip(scanner_id: int) -> str:
    if is_reaper():
        return "*********" if scanner_id == 1 else "*********"
    return f"10.11.4.{scanner_id}"


def make_pulczar_bootloader_ip(scanner_id: int) -> str:
    return f"10.11.4.{scanner_id + 10}"


class HwRevEnum(Enum):
    # EEPROM value indicating motor type. Also hardcoded in firmware.
    MAXON = 0
    FAULHABER = 100


class PulczarBoardException(MakaException):
    def __init__(self, *args: object, reply: Optional[Reply] = None):
        self.reply = reply
        super().__init__(*args)


class PulczarGimbalException(PulczarBoardException):
    pass


class PulczarOutOfBoundsException(PulczarGimbalException):
    def __init__(self, *args: object, error_in_pan: bool, error_in_tilt: bool):
        super().__init__(self, *args)
        self.error_in_pan = error_in_pan
        self.error_in_tilt = error_in_tilt


class TimeDebug:
    def __init__(
        self,
        timestamp_us: int,
        pps_timer_val: int,
        pps_ticks: int,
        freq_mul: float,
        error_ticks: int,
        error_ticks2: int,
    ) -> None:
        self.timestamp_us = timestamp_us
        self.pps_timer_val = pps_timer_val
        self.pps_ticks = pps_ticks
        self.freq_mul = freq_mul
        self.error_ticks = error_ticks
        self.error_ticks2 = error_ticks2


class FaulhaberServoControlConfig:
    class Parameter:
        def __init__(
            self,
            name: str,
            index: int,
            subindex: int,
            value: int,
            data_min: int,
            data_max: int,
            iterative_selector: List[str] = [],
            is_fh_control: bool = True,
        ) -> None:
            self.name = name
            self.index = index
            self.subindex = subindex
            self.min = data_min
            self.max = data_max
            self.value = value
            self.iterative_selector = iterative_selector
            self.is_fh_control = is_fh_control

            if len(self.iterative_selector) > 0:
                assert len(self.iterative_selector) == self.max - self.min + 1

        def generate_granular_value_list(self, fraction_margin: float, granularity: int) -> List[int]:
            # Iterative Selector Don't have granularity, they are like an enum, so we always try all options
            if len(self.iterative_selector) > 0:
                return [i for i in range(self.min, self.max + 1)]

            step = round((self.value * fraction_margin) / granularity)
            if step <= 0:
                step = 1

            retval = set()
            for i in range(granularity + 1):
                high = max(min(self.value + (i * step), self.max), self.min)
                low = max(min(self.value - (i * step), self.max), self.min)
                retval.add(high)
                retval.add(low)

            return list(sorted(retval))

    # Defaults are the Reaper Pan Defaults
    # In practice values should always be loaded
    def __init__(self) -> None:
        # Position
        self.gain_kv = FaulhaberServoControlConfig.Parameter("gain_kv", 0x2348, 0x01, 255, 0, 2 ** 8 - 1)
        self.velocity_feedforward_factor = FaulhaberServoControlConfig.Parameter(
            "velocity_feedforward_factor", 0x234A, 0x01, 126, 0, 2 ** 8 - 1
        )
        self.velocity_feedforward_delay = FaulhaberServoControlConfig.Parameter(
            "velocity_feedforward_delay", 0x234A, 0x02, 5, 0, 2 ** 16 - 1
        )
        self.gain_factor_kpn = FaulhaberServoControlConfig.Parameter(
            "gain_factor_kpn", 0x2347, 0x01, 128, 0, 2 ** 8 - 1
        )
        self.gain_factor_kv = FaulhaberServoControlConfig.Parameter("gain_factor_kv", 0x2347, 0x02, 128, 0, 2 ** 8 - 1)

        # Velocity
        self.gain_kp = FaulhaberServoControlConfig.Parameter("gain_kp", 0x2344, 0x01, 18000, 0, 2 ** 32 - 1)
        self.integral_time_tn = FaulhaberServoControlConfig.Parameter("integral_time_tn", 0x2344, 0x02, 800, 20, 1000)
        # Technically the above is dependent on not being in the 3rd state of the below parameter, but in practice it's not really viable to be in that state so we don't link the dependency
        self.integral_part_option = FaulhaberServoControlConfig.Parameter(
            "integral_part_option",
            0x2344,
            0x06,
            0,
            0,
            2,
            ["Active Integral Part", "Stopped Integral Part when in position corridor", "Deactivated Integral Part"],
        )
        self.current_feedforward_factor = FaulhaberServoControlConfig.Parameter(
            "current_feedforward_factor", 0x2349, 0x01, 40, 0, 128
        )
        self.current_feedforward_delay = FaulhaberServoControlConfig.Parameter(
            "current_feedforward_delay", 0x2349, 0x02, 0, 0, 2 ** 16 - 1
        )
        self.actual_velocity_filter_tf = FaulhaberServoControlConfig.Parameter(
            "actual_velocity_filter_tf", 0x2345, 0x01, 5, 0, 2 ** 16 - 1
        )
        self.display_velocity_filter = FaulhaberServoControlConfig.Parameter(
            "display_velocity_filter", 0x2345, 0x02, 5, 0, 2 ** 16 - 1
        )
        self.setpoint_velocity_filter_tf = FaulhaberServoControlConfig.Parameter(
            "setpoint_velocity_filter_tf", 0x2346, 0x01, 4, 0, 2 ** 16 - 1
        )
        self.setpoint_filter_enable = FaulhaberServoControlConfig.Parameter(
            "setpoint_filter_enable", 0x2346, 0x02, 0, 0, 1, ["Disabled", "Enabled"]
        )

        # Current
        self.torque_gain_kpi = FaulhaberServoControlConfig.Parameter(
            "torque_gain_kpi", 0x2342, 0x01, 2500, 0, 2 ** 32 - 1
        )
        self.torque_integral_time_tni = FaulhaberServoControlConfig.Parameter(
            "torque_integral_time_tni", 0x2342, 0x02, 152, 150, 2600
        )
        self.flux_gain_kpi = FaulhaberServoControlConfig.Parameter("flux_gain_kpi", 0x2343, 0x01, 2500, 0, 2 ** 32 - 1)
        self.flux_integral_time_tni = FaulhaberServoControlConfig.Parameter(
            "flux_integral_time_tni", 0x2343, 0x02, 152, 150, 2600
        )

        # Profile Parameters
        self.motion_profile_type = FaulhaberServoControlConfig.Parameter(
            "motion_profile_type", 0x6086, 0x00, 0, 0, 1, ["Linear Ramp", "Sin2 Ramp"]
        )
        self.max_motor_speed = FaulhaberServoControlConfig.Parameter(
            "max_motor_speed", 0x6080, 0x00, 200000, 1, 2 ** 32 - 1
        )
        self.profile_velocity = FaulhaberServoControlConfig.Parameter(
            "profile_velocity", 0x6081, 0x00, 200000, 1, 2 ** 32 - 1
        )
        self.profile_acceleration = FaulhaberServoControlConfig.Parameter(
            "profile_acceleration", 0x6083, 0x00, 800, 1, 2 ** 15 - 1
        )
        self.profile_deceleration = FaulhaberServoControlConfig.Parameter(
            "profile_deceleration", 0x6084, 0x00, 800, 1, 2 ** 15 - 1
        )
        self.quick_stop_deceleration = FaulhaberServoControlConfig.Parameter(
            "quick_stop_deceleration", 0x6085, 0x00, 800, 1, 2 ** 15 - 1
        )

        # Non-FH Tunables
        self.move_velocity_factor = FaulhaberServoControlConfig.Parameter(
            "move_velocity_factor", 0x0000, 0x00, 7500, 0, 2 ** 32 - 1, is_fh_control=False
        )

    def get_param(self, param_name: str) -> Parameter:
        if param_name not in self.__dict__:
            raise Exception(f"{param_name} Not Found in config")
        return cast(FaulhaberServoControlConfig.Parameter, self.__dict__[param_name])

    def get_fh_param_list(self) -> List[str]:
        return list(sorted([k for k, v in self.__dict__.items() if v.is_fh_control]))

    def toJSON(self) -> Dict[str, int]:
        retval: Dict[str, int] = {}
        for _, item in self.__dict__.items():
            if isinstance(item, FaulhaberServoControlConfig.Parameter):
                retval[item.name] = item.value
        return retval

    def fromJSON(self, data: Dict[str, int]) -> None:
        verify_set = set()
        for name, value in data.items():
            if name in self.__dict__:
                self.__dict__[name].value = value
                if name in verify_set:
                    LOG.error(f"Duplicate parameter issue {name}")
                verify_set.add(name)
            else:
                LOG.error(f"Likely parameter loading error for {name}")

    def write_to_file(self, name: str) -> None:
        with open(name, "w") as f:
            json.dump(self.toJSON(), f, indent=4)

    def read_from_file(self, name: str) -> None:
        with open(name, "r") as f:
            data = json.load(f)
            return self.fromJSON(data)

    def set_param(self, param_name: str, value: int) -> None:
        if param_name not in self.__dict__:
            raise Exception(f"{param_name} Not Found in config")
        self.__dict__[param_name].value = value

    def is_equal(self, other: "FaulhaberServoControlConfig") -> bool:
        if len(self.__dict__) != len(other.__dict__):
            return False

        for k, v in self.__dict__.items():
            if k not in other.__dict__:
                return False
            if v.value != other.__dict__[k].value:
                return False

        return True

    def generate_granular_configs(
        self, param_name: str, fraction_margin: float, granularity: int
    ) -> List["FaulhaberServoControlConfig"]:
        if param_name not in self.__dict__:
            raise Exception(f"{param_name} Not Found in config")

        granules = self.__dict__[param_name].generate_granular_value_list(fraction_margin, granularity)

        retval = []
        for item in granules:
            cfg = copy.deepcopy(self)
            cfg.set_param(param_name, item)
            retval.append(cfg)

        return retval

    def __repr__(self) -> str:
        return json.dumps(self.toJSON())

    def formatted_json(self) -> str:
        output = "{"
        first: bool = True
        for k, v in self.__dict__.items():
            if not first:
                output += ", "
            output += f'"{k}": {v.value: 10.10g}'
            first = False
        output += "}"
        return output


class PulczarServoPositionalPIDConfig:
    def __init__(
        self, position_p: int, position_i: int, position_d: int, position_ffv: int, position_ffa: int,
    ):
        self.position_p = position_p
        self.position_i = position_i
        self.position_d = position_d
        self.position_ffv = position_ffv
        self.position_ffa = position_ffa

    def write_to_file(self, name: str) -> None:
        with open(name, "w") as f:
            json.dump(self.__dict__, f)

    @staticmethod
    def read_from_file(name: str) -> "PulczarServoPositionalPIDConfig":
        with open(name, "r") as f:
            data = json.load(f)
            return PulczarServoPositionalPIDConfig(**data)

    def __repr__(self) -> str:
        return f"PP: {self.position_p}, PI: {self.position_i}, PD: {self.position_d}, FFV: {self.position_ffv}, FFA: {self.position_ffa}"


class PulczarServoPIDConfig(PulczarServoPositionalPIDConfig):
    def __init__(
        self,
        current_p: int,
        current_i: int,
        position_p: int,
        position_i: int,
        position_d: int,
        position_ffv: int,
        position_ffa: int,
    ):
        super().__init__(position_p, position_i, position_d, position_ffv, position_ffa)
        self.current_p = current_p
        self.current_i = current_i

    def write_to_file(self, name: str) -> None:
        with open(name, "w") as f:
            json.dump(self.__dict__, f)

    @staticmethod
    def read_from_file(name: str) -> "PulczarServoPIDConfig":
        with open(name, "r") as f:
            data = json.load(f)
            return PulczarServoPIDConfig(**data)

    @staticmethod
    def default_pids(
        generation_override: Optional[GENERATION] = None,
    ) -> Tuple["PulczarServoPIDConfig", "PulczarServoPIDConfig"]:
        gen = generation()
        if generation_override is not None:
            gen = generation_override
        if gen == GENERATION.SLAYER:
            return (
                PulczarServoPIDConfig(
                    current_p=1474745,
                    current_i=4633577,
                    position_p=3574120,
                    position_i=618346132,
                    position_d=37313,
                    position_ffv=85,
                    position_ffa=159,
                ),
                PulczarServoPIDConfig(
                    current_p=1450318,
                    current_i=4945111,
                    position_p=6155423,
                    position_i=810035043,
                    position_d=40986,
                    position_ffv=86,
                    position_ffa=96,
                ),
            )
        else:
            # default to bud
            return (
                PulczarServoPIDConfig(  # 66.8 ms
                    current_p=1540716,
                    current_i=5019039,
                    position_p=4215402,
                    position_i=383974053,
                    position_d=19763,
                    position_ffv=2,
                    position_ffa=135,
                ),
                PulczarServoPIDConfig(  # 80.6 ms
                    current_p=1540716,
                    current_i=5019039,
                    position_p=9926237,
                    position_i=126949219,
                    position_d=58575,
                    position_ffv=109,
                    position_ffa=135,
                ),
            )

    def __repr__(self) -> str:
        return f"CP: {self.current_p}, CI: {self.current_i}, {super().__repr__()}"


class PID_Config_Request_Type(Enum):
    # Needs to match epos.proto
    FALLBACK = epos_pb.PID_REQUEST_FALLBACK
    TEST = epos_pb.PID_REQUEST_TEST
    SAVE = epos_pb.PID_REQUEST_SAVE


class PulczarStatus:
    def __init__(self, status: int, laser_status: laser_pb.Laser_Status_Reply):
        self._status = status
        self._lpsu_state: bool = laser_status.lpsu_state
        if self._lpsu_state:
            self._status = self._status | 1 << 6
        # LPSU current measured with 99.5 ohm shunt and 1/2 divider to MCU
        self._lpsu_current: float = laser_status.lpsu_current * 2 * 0.00995  # in amps
        self._power_value: float = laser_status.power
        self._arc_detected: bool = laser_status.arc_detected

    @property
    def status(self) -> int:
        return self._status

    def is_safe_to_fire(self) -> bool:
        # TODO Consider whether arm should be in here
        return self.door and self.flow and self.water and self.dog and self.power

    def _prop(self, index: int) -> bool:
        return (self._status & (1 << index)) != 0

    @property
    def arm(self) -> bool:
        return self._prop(0)

    @property
    def water(self) -> bool:
        return self._prop(1)

    @property
    def flow(self) -> bool:
        return self._prop(2)

    @property
    def door(self) -> bool:
        return self._prop(3)

    @property
    def fire(self) -> bool:
        return self._prop(4)

    @property
    def dog(self) -> bool:
        return self._prop(5)

    @property
    def power(self) -> bool:
        return self._prop(6)

    @property
    def stf_alt(self) -> bool:
        return self._prop(7)

    @property
    def lpsu_state(self) -> bool:
        return self._lpsu_state

    @property
    def lpsu_current(self) -> float:
        return self._lpsu_current

    @property
    def power_value(self) -> float:
        return self._power_value

    @property
    def arc_detected(self) -> bool:
        """
        Alarm flag for scanner arc detector
        """
        return self._arc_detected

    def is_shootable(self) -> bool:
        if is_slayer():
            return self._lpsu_state
        return (self.stf_alt or (self.water and self.door and self.flow)) and (self.arm and self.power and self.dog)

    def to_json(self) -> Dict[str, Any]:
        return {
            "arm": self.arm,
            "water": self.water,
            "flow": self.flow,
            "door": self.door,
            "fire": self.fire,
            "dog": self.dog,
            "power": self.power,
            "stf_alt": self.stf_alt,
        }

    def __repr__(self) -> str:
        return "\n".join(
            [
                f"ARM: {self.arm}",
                f"WATER: {self.water}",
                f"FLOW: {self.flow}",
                f"DOOR: {self.door}",
                f"FIRE: {self.fire}",
                f"DOG: {self.dog}",
                f"POWER: {self.power}",
                f"STF_ALT: {self.stf_alt}",
                f"LPSU_STATE: {self.lpsu_state}",
                f"LPSU_CURRENT: {self.lpsu_current}",
                f"POWER_VALUE: {self.power_value}",
            ]
        )


class PulczarBoardFireStatus:
    def __init__(
        self, lpsu_power: bool, fire_pin: bool, fireable: bool, therm_power_mv: int, therm_baseline_mv: int
    ) -> None:
        self._lpsu_power = lpsu_power
        self._fire_pin = fire_pin
        self._fireable = fireable
        self._therm_power_mv = therm_power_mv
        self._therm_baseline_mv = therm_baseline_mv

    @staticmethod
    def temp(mv_curr: float) -> float:
        # https://www.amphenol-sensors.com/hubfs/Documents/AAS-913-318C-Temperature-resistance-curves-071816-web.pdf
        # Note the tables in pdf use resistance ratio we are using voltage ratio. They work the same as there is a
        # strict linear relationship, but 1 less step to convert as we read voltage from ADC
        a = 3.3540154e-3
        b = 2.5627725e-4
        c = 2.082921e-6
        d = 7.3003206e-8
        mv25 = 2500  # voltage reading at 25 deg C
        return (
            1
            / (
                a
                + b * math.log(mv_curr / mv25)
                + c * (math.log(mv_curr / mv25) ** 2)
                + d * (math.log(mv_curr / mv25) ** 3)
            )
            - 273.15
        )

    @property
    def lpsu_power(self) -> bool:
        return self._lpsu_power

    @property
    def fire_pin(self) -> bool:
        return self._fire_pin

    @property
    def fireable(self) -> bool:
        return self._fireable

    @property
    def firing(self) -> bool:
        return self.lpsu_power and self.fire_pin and self.fireable

    @property
    def power_temp(self) -> float:
        return PulczarBoardFireStatus.temp(self._therm_power_mv)

    @property
    def baseline_temp(self) -> float:
        return PulczarBoardFireStatus.temp(self._therm_baseline_mv)

    @property
    def delta_temp(self) -> float:
        return self.power_temp - self.baseline_temp


class ArcDetectorStatus:
    def __init__(self, reply: arc_detector_pb.Status_Reply):
        self._enabled = reply.enabled
        self._alarm = reply.alarm
        self._minCurrent = reply.minCurrent
        self._maxCurrent = reply.maxCurrent

    @property
    def enabled(self) -> bool:
        return self._enabled

    @property
    def alarm(self) -> bool:
        return self._alarm

    @property
    def minCurrent(self) -> float:
        return self._minCurrent

    @property
    def maxCurrent(self) -> float:
        return self._maxCurrent

    def to_json(self) -> Dict[str, Any]:
        return {
            "enabled": self.enabled,
            "alarm": self.alarm,
            "minCurrent": self.minCurrent,
            "maxCurrent": self.maxCurrent,
        }

    def __repr__(self) -> str:
        return "\n".join(
            [
                f"ENABLED: {self.enabled}",
                f"ALARM: {self.alarm}",
                f"MIN_CURRENT: {self.minCurrent} A",
                f"MAX_CURRENT: {self.maxCurrent} A",
            ]
        )


class ArcDetectorConfig:
    def __init__(self, message: Optional[arc_detector_pb.Config] = None):
        if message:
            self.enabled = message.enabled
            self.upperLimit = message.upperLimit
            self.lowerLimit = message.lowerLimit
            self.alarmPeriod = message.alarmPeriod
            self.alarmThreshold = message.alarmThreshold
            self.initialDelay = message.initialDelay
            self.sampleInterval = message.sampleInterval
        else:
            self.enabled = False
            self.upperLimit = 0
            self.lowerLimit = 0
            self.alarmPeriod = 0
            self.alarmThreshold = 0
            self.initialDelay = 0
            self.sampleInterval = 0

    def toConfig(self) -> arc_detector_pb.Config:
        conf = arc_detector_pb.Config()

        conf.enabled = self.enabled
        conf.upperLimit = self.upperLimit
        conf.lowerLimit = self.lowerLimit
        conf.alarmPeriod = self.alarmPeriod
        conf.alarmThreshold = self.alarmThreshold
        conf.initialDelay = self.initialDelay
        conf.sampleInterval = self.sampleInterval

        return conf

    def to_json(self) -> Dict[str, Any]:
        return {
            "enabled": self.enabled,
            "upperLimit": self.upperLimit,
            "lowerLimit": self.lowerLimit,
            "alarmPeriod": self.alarmPeriod,
            "alarmThreshold": self.alarmThreshold,
            "initialDelay": self.initialDelay,
            "sampleInterval": self.sampleInterval,
        }

    def __repr__(self) -> str:
        return "\n".join(
            [
                f"ENABLED: {self.enabled}",
                f"UPPER_LIMIT: {self.upperLimit} A",
                f"LOWER_LIMIT: {self.lowerLimit} A",
                f"ALARM_PERIOD: {self.alarmPeriod} seconds",
                f"ALARM_THRESHOLD: {self.alarmThreshold}",
                f"SAMPLE_DELAY: {self.initialDelay} msec",
                f"SAMPLE_INTERVAL: {self.sampleInterval} msec",
            ]
        )


class PositionAt:
    def __init__(self, pan: int, tilt: int, timestamp_us: int) -> None:
        self._pan = pan
        self._tilt = tilt
        self._timestamp_us = timestamp_us

    def __str__(self) -> str:
        return f"[{self.timestamp_ms}: ({self._pan}, {self._tilt})"

    @property
    def pan(self) -> int:
        return self._pan

    @property
    def tilt(self) -> int:
        return self._tilt

    @property
    def pos(self) -> Tuple[int, int]:
        return (self.pan, self.tilt)

    @property
    def timestamp_us(self) -> int:
        return self._timestamp_us

    @property
    def timestamp_ms(self) -> int:
        return round(self._timestamp_us / 1000)


class LaserType(IntEnum):
    SLAYER_CO2 = auto()
    REAPER_BWT = auto()
    REAPER_JLIGHT = auto()

    @staticmethod
    def FromProto(proto_type: laser_pb.LaserType) -> Optional["LaserType"]:  # type: ignore[valid-type]
        if proto_type == laser_pb.LaserType.LASERTYPE_CO2:
            return LaserType.SLAYER_CO2
        elif proto_type == laser_pb.LaserType.LASERTYPE_DIODE_BWT:
            return LaserType.REAPER_BWT
        elif proto_type == laser_pb.LaserType.LASERTYPE_DIODE_JLIGHT:
            return LaserType.REAPER_JLIGHT
        else:
            return None

    @staticmethod
    def ToProto(value: "LaserType") -> laser_pb.LaserType:  # type: ignore[valid-type]
        if value == LaserType.SLAYER_CO2:
            return laser_pb.LaserType.LASERTYPE_CO2
        elif value == LaserType.REAPER_BWT:
            return laser_pb.LaserType.LASERTYPE_DIODE_BWT
        elif value == LaserType.REAPER_JLIGHT:
            return laser_pb.LaserType.LASERTYPE_DIODE_JLIGHT

    @property
    def proto_value(self) -> laser_pb.LaserType:  # type: ignore[valid-type]
        return LaserType.ToProto(self)


@dataclass
class BwtStatus:
    connected: bool

    temperature: List[float]
    humidity: List[float]
    current: List[float]

    faulted: bool
    faults: Optional[List[int]]

    thermistors: List[float]

    @classmethod
    # def FromMessage(cls, packet: laser_pb.Bwt_Status_Reply) -> Self:
    def FromMessage(cls, packet: laser_pb.Diode_Status_Reply) -> Self:
        faults_list: Optional[List[int]] = None

        # BWT specific fault codes
        if packet.HasField("extra_bwt"):
            faults_list = list(packet.extra_bwt.faults)
        # TODO: faults for Jlight

        return cls(
            connected=True,
            temperature=list(packet.temp),
            humidity=list(packet.humidity),
            current=list(packet.current),
            faulted=packet.faulted,
            faults=faults_list,
            thermistors=list(packet.thermistors),
        )


@dataclass
class BwtInventory:
    """
    Information about the laser connected to a scanner
    """

    model: str
    serial: str
    ratedPower: int
    laser_type: Optional[LaserType]

    @classmethod
    def FromMessage(cls, packet: laser_pb.Laser_Inventory_Reply) -> Self:
        return cls(
            model=packet.model,
            serial=packet.serial,
            ratedPower=packet.power,
            laser_type=LaserType.FromProto(packet.type),
        )


@dataclass
class BwtTransportConfig:
    # Print raw UART rx/tx messages to syslog
    log_messages: bool
    # add additional delays between commands
    intercommand_delay: bool

    @classmethod
    def FromMessage(cls, packet: laser_pb.Bwt_Transport_Config) -> Self:
        return cls(log_messages=packet.log_messages, intercommand_delay=packet.intercommand_delay)


@dataclass
class ServoStatus:
    """
    Hardware status information for a single motor controller
    """

    connected: bool
    controller_sn: int

    sensor_timestamp: int
    output_stage_temp: Temperature
    motor_supply: Voltage
    motor_current: Current

    encoder_timestamp: int
    encoder_ticks: int

    @classmethod
    def FromMessage(cls, packet: pulczar_pb.HwStatus_Servo) -> Self:
        return cls(
            connected=packet.connected,
            controller_sn=packet.controller_sn,
            sensor_timestamp=packet.sensor_time_ms,
            output_stage_temp=Temperature.from_c(packet.output_stage_temp_c),
            motor_supply=Voltage.from_volts(packet.motor_supply_v),
            motor_current=Current.from_amps(packet.motor_current_a),
            encoder_timestamp=packet.encoder_time_ms,
            encoder_ticks=packet.encoder_ticks,
        )


@dataclass
class ReaperScannerHwStatus:
    """
    Hardware status information for a Reaper scanner

    This includes the BWT status, thermistors, as well as the general servo/motor controller status
    information.
    """

    bwt_connected: bool
    bwt_inventory: Optional[BwtInventory]
    bwt_status: Optional[BwtStatus]

    laser_temp_collimator: Temperature
    laser_temp_fiber: Temperature
    laser_power_w: float
    laser_power_raw: Voltage

    servo_pan: Optional[ServoStatus]
    servo_tilt: Optional[ServoStatus]

    target_cam_power: bool

    @classmethod
    def FromMessage(cls, packet: pulczar_pb.HwStatus_Reaper_Reply) -> Self:
        return cls(
            bwt_connected=packet.laser_connected,
            bwt_inventory=BwtInventory.FromMessage(packet.laser_inventory) if packet.laser_connected else None,
            bwt_status=BwtStatus.FromMessage(packet.laser_status) if packet.laser_connected else None,
            laser_temp_collimator=Temperature.from_c(packet.laser_therm_temp_c[0]),
            laser_temp_fiber=Temperature.from_c(packet.laser_therm_temp_c[1]),
            laser_power_w=packet.laser_power_w,
            laser_power_raw=Voltage.from_millivolts(packet.laser_power_raw_mv),
            servo_pan=ServoStatus.FromMessage(packet.servo_pan) if packet.servo_pan else None,
            servo_tilt=ServoStatus.FromMessage(packet.servo_tilt) if packet.servo_tilt else None,
            target_cam_power=packet.target_cam_power_on,
        )


@dataclass
class SlayerScannerHwStatus:
    """
    Hardware status information for a Slayer scanner

    Contains general servo/motor controller status information, as well as LPSU and laser power
    meter (thermistor) status.
    """

    lpm_thermistor_beam_raw: Voltage
    lpm_thermistor_beam: Temperature

    lpm_thermistor_ambient_raw: Voltage
    lpm_thermistor_ambient: Temperature

    lpsu_status: bool
    lpsu_current: Current

    servo_pan: Optional[ServoStatus]
    servo_tilt: Optional[ServoStatus]

    target_cam_power: bool

    @classmethod
    def FromMessage(cls, packet: pulczar_pb.HwStatus_Slayer_Reply) -> Self:
        return cls(
            lpm_thermistor_beam_raw=Voltage.from_millivolts(packet.lpm_thermistor_beam_raw_mv),
            lpm_thermistor_beam=Temperature.from_c(packet.lpm_thermistor_beam_temp_c),
            lpm_thermistor_ambient_raw=Voltage.from_millivolts(packet.lpm_thermistor_ambient_raw_mv),
            lpm_thermistor_ambient=Temperature.from_c(packet.lpm_thermistor_ambient_temp_c),
            lpsu_status=packet.lpsu_status,
            lpsu_current=Current.from_milliamps(packet.lpsu_current_ma),
            servo_pan=ServoStatus.FromMessage(packet.servo_pan) if packet.servo_pan else None,
            servo_tilt=ServoStatus.FromMessage(packet.servo_tilt) if packet.servo_tilt else None,
            target_cam_power=packet.target_cam_power_on,
        )


class PulczarBoardConnector(BootloadableConnector):
    def __init__(self, protocol_connector: MakaProtocolConnector):
        self._nano_connector: NanoPbConnector[Request, Reply] = NanoPbConnector(protocol_connector, Request, Reply)
        self._identifier = protocol_connector.get_identifier()
        self._bootloader_type = protocol_connector.get_bootloader_type()

    @property
    def identifier(self) -> str:
        return self._identifier

    async def stop(self) -> None:
        await self._nano_connector.stop()

    async def ping(self) -> float:
        payload = 42
        request = self._nano_connector.create_request()
        request.ping.x = 42
        start_time = time.time()
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        duration = time.time() - start_time
        assert reply.WhichOneof("reply") == "pong"
        assert reply.pong.x == payload
        return duration

    async def get_version(self) -> FirmwareVersion:
        request = self._nano_connector.create_request(version=version_pb.Version_Request())
        try:
            reply: Reply = await self._nano_connector.send_request_await_reply(request)
        except RetryableMakaDeviceException:
            raise PulczarBoardException(f"{self._identifier} Board Failed to Reply to Version Request")
        if reply.WhichOneof("reply") != "version":
            raise PulczarBoardException(f"{self._identifier} Failed to Get Version", reply=reply)
        return FirmwareVersion(reply.version.major, reply.version.minor, 0)

    # ### Pulczar ### #

    async def hard_reset(self) -> None:
        request = self._nano_connector.create_request(pulczar=pulczar_pb.Request(reset=pulczar_pb.Reset_Request()))
        # This will break the connection
        await self._nano_connector.send_request(request)

    async def clear_config(self) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(clear=pulczar_pb.Clear_Config_Request())
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "pulczar" or reply.pulczar.WhichOneof("reply") != "ack":
            raise PulczarBoardException(f"{self._identifier} Failed to Clear Configuration", reply=reply)

    async def get_status(self) -> PulczarStatus:
        request = self._nano_connector.create_request(pulczar=pulczar_pb.Request(status=pulczar_pb.Status_Request()))
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "pulczar" or reply.pulczar.WhichOneof("reply") != "status":
            raise PulczarBoardException(f"{self._identifier} Failed to Get Status", reply=reply)
        return PulczarStatus(reply.pulczar.status.status, reply.pulczar.status.laser_status)

    async def set_override(self, door: bool, flow: bool, water: bool, stf_alt: bool) -> None:
        override = (int(door) << 0) + (int(flow) << 1) + (int(water) << 2) + (int(stf_alt) << 3)
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(override=pulczar_pb.Override_Request(override=override))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "pulczar" or reply.pulczar.WhichOneof("reply") != "ack":
            raise PulczarBoardException(f"{self._identifier} Failed to Set Override", reply=reply)

    # ### Arc Detection ### #
    async def _create_arc_detector_request(self, **kwargs: Any) -> Request:
        return self._nano_connector.create_request(pulczar=pulczar_pb.Request(arc=arc_detector_pb.Request(**kwargs)))

    async def _wait_arc_detector_reply(self, request: Request, **kwargs: Any) -> Tuple[arc_detector_pb.Reply, Reply]:
        reply: Reply = await self._nano_connector.send_request_await_reply(request, **kwargs)

        if reply.WhichOneof("reply") != "pulczar" or reply.pulczar.WhichOneof("reply") != "arc":
            raise PulczarBoardException(f"{self._identifier} Invalid arc detector reply", reply=reply)

        return reply.pulczar.arc, reply

    async def get_arc_detector_config(self) -> ArcDetectorConfig:
        """
        Query the current configuration of the arc detector
        """
        request = await self._create_arc_detector_request(getConf=arc_detector_pb.Get_Config_Request())
        reply, outerReply = await self._wait_arc_detector_reply(request, timeout_ms=5000)

        if reply.WhichOneof("reply") != "conf":
            raise PulczarBoardException(f"{self._identifier} Failed to get arc detector config", reply=outerReply)

        return ArcDetectorConfig(reply.conf.conf)

    async def set_arc_detector_config(self, newConfig: ArcDetectorConfig) -> ArcDetectorConfig:
        """
        Update the configuration (e.g. thresholds and periods) for the arc detector
        """
        request = await self._create_arc_detector_request(
            setConf=arc_detector_pb.Set_Config_Request(newConfig=newConfig.toConfig())
        )
        reply, outerReply = await self._wait_arc_detector_reply(request, timeout_ms=5000)

        if reply.WhichOneof("reply") != "conf":
            raise PulczarBoardException(f"{self._identifier} Failed to set arc detector config", reply=outerReply)

        return ArcDetectorConfig(reply.conf.conf)

    async def get_arc_detector_status(self) -> ArcDetectorStatus:
        """
        Query current state (min/max current, alarm) of laser arc detector
        """
        request = await self._create_arc_detector_request(status=arc_detector_pb.Status_Request())
        reply, outerReply = await self._wait_arc_detector_reply(request, timeout_ms=5000)

        if reply.WhichOneof("reply") != "status":
            raise PulczarBoardException(f"{self._identifier} Failed to get arc detector status", reply=outerReply)

        return ArcDetectorStatus(reply.status)

    async def reset_arc_detector_alarm(self) -> None:
        """
        Reset any pending arc detector alarms to re-enable laser firing
        """
        request = await self._create_arc_detector_request(reset=arc_detector_pb.Reset_Alarm_Request())
        reply, outerReply = await self._wait_arc_detector_reply(request, timeout_ms=5000)

        if reply.WhichOneof("reply") != "ack":
            raise PulczarBoardException(f"{self._identifier} Failed to reset arc detector alarm", reply=outerReply)

    # ### Gimbal ### #

    def _create_servos_request(self, **kwargs: Any) -> Request:
        return self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(gimbal=gimbal_pb.Request(servos=gimbal_pb.Servos_Request(**kwargs)))
        )

    async def send_nmt_reset(self) -> None:
        request = self._create_servos_request(
            pan=servo_pb.Request(epos=epos_pb.Request(can=can_pb.Request(reset=can_pb.NMT_Reset_Request()))),
            tilt=servo_pb.Request(epos=epos_pb.Request(can=can_pb.Request(reset=can_pb.NMT_Reset_Request()))),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=5000)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.pan.epos.WhichOneof("reply") != "can"
            or reply.pulczar.gimbal.servos.tilt.epos.WhichOneof("reply") != "can"
            or reply.pulczar.gimbal.servos.pan.epos.can.WhichOneof("reply") != "ack"
            or reply.pulczar.gimbal.servos.tilt.epos.can.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to NMT Reset", reply=reply,
            )

    async def get_gimbal_SDO(self, index: int, subindex: int) -> Tuple[int, int]:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                epos=epos_pb.Request(
                    can=can_pb.Request(sdo_upload=can_pb.SDO_Upload_Request(index=index, subindex=subindex))
                )
            ),
            tilt=servo_pb.Request(
                epos=epos_pb.Request(
                    can=can_pb.Request(sdo_upload=can_pb.SDO_Upload_Request(index=index, subindex=subindex))
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.pan.epos.WhichOneof("reply") != "can"
            or reply.pulczar.gimbal.servos.tilt.epos.WhichOneof("reply") != "can"
            or reply.pulczar.gimbal.servos.pan.epos.can.WhichOneof("reply") != "msg"
            or reply.pulczar.gimbal.servos.tilt.epos.can.WhichOneof("reply") != "msg"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to get gimbal SDO Index: {index} Subindex: {subindex} reply: {reply}",
                reply=reply,
            )
        return (
            int.from_bytes(reply.pulczar.gimbal.servos.pan.epos.can.msg.sdo.data, byteorder="little"),
            int.from_bytes(reply.pulczar.gimbal.servos.tilt.epos.can.msg.sdo.data, byteorder="little"),
        )

    async def set_gimbal_SDO(self, index: int, subindex: int, pan_value: int, tilt_value: int) -> None:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                epos=epos_pb.Request(
                    can=can_pb.Request(
                        sdo_download=can_pb.SDO_Download_Request(index=index, subindex=subindex, value=pan_value)
                    )
                )
            ),
            tilt=servo_pb.Request(
                epos=epos_pb.Request(
                    can=can_pb.Request(
                        sdo_download=can_pb.SDO_Download_Request(index=index, subindex=subindex, value=tilt_value)
                    )
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.pan.epos.WhichOneof("reply") != "can"
            or reply.pulczar.gimbal.servos.tilt.epos.WhichOneof("reply") != "can"
            or reply.pulczar.gimbal.servos.pan.epos.can.WhichOneof("reply") != "ack"
            or reply.pulczar.gimbal.servos.tilt.epos.can.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to set gimbal SDO Index: {index} Subindex: {subindex} reply: {reply}",
                reply=reply,
            )

    async def gimbal_configure_faulhaber_tuning_config(
        self, pan_cfg: FaulhaberServoControlConfig, tilt_cfg: FaulhaberServoControlConfig, param_name: str = ""
    ) -> None:
        # TODO Set the move velocity factor in here somewhere ???
        pan_param_list = pan_cfg.get_fh_param_list()
        tilt_param_list = tilt_cfg.get_fh_param_list()
        assert pan_param_list == tilt_param_list

        if param_name != "":
            pan_param_list = [param_name]
            tilt_param_list = [param_name]

        for param in pan_param_list:
            pan_param = pan_cfg.get_param(param)
            tilt_param = tilt_cfg.get_param(param)
            assert tilt_param.index == pan_param.index
            assert tilt_param.subindex == pan_param.subindex
            await self.set_gimbal_SDO(pan_param.index, pan_param.subindex, pan_param.value, tilt_param.value)

    async def gimbal_read_faulhaber_tuning_config(
        self, pan_cfg: FaulhaberServoControlConfig, tilt_cfg: FaulhaberServoControlConfig
    ) -> None:
        pan_param_list = pan_cfg.get_fh_param_list()
        tilt_param_list = tilt_cfg.get_fh_param_list()
        assert pan_param_list == tilt_param_list

        for param in pan_param_list:
            pan_param = pan_cfg.get_param(param)
            tilt_param = tilt_cfg.get_param(param)
            assert tilt_param.index == pan_param.index
            assert tilt_param.subindex == pan_param.subindex
            pan, tilt = await self.get_gimbal_SDO(pan_param.index, pan_param.subindex)
            pan_param.value = pan
            tilt_param.value = tilt

    async def gimbal_configure_pids_v2(
        self,
        pan_pid: PulczarServoPIDConfig,
        tilt_pid: PulczarServoPIDConfig,
        type: PID_Config_Request_Type = PID_Config_Request_Type.FALLBACK,
    ) -> None:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                epos=epos_pb.Request(
                    pid_v2=epos_pb.Set_PID_V2_Request(
                        type=type.value,
                        epos=epos_pb.EPOS_PID(
                            gain_p=pan_pid.position_p,
                            gain_i=pan_pid.position_i,
                            gain_d=pan_pid.position_d,
                            gain_ffa=pan_pid.position_ffa,
                            gain_ffv=pan_pid.position_ffv,
                            current_p=pan_pid.current_p,
                            current_i=pan_pid.current_i,
                        ),
                    )
                )
            ),
            tilt=servo_pb.Request(
                epos=epos_pb.Request(
                    pid_v2=epos_pb.Set_PID_V2_Request(
                        type=type.value,
                        epos=epos_pb.EPOS_PID(
                            gain_p=tilt_pid.position_p,
                            gain_i=tilt_pid.position_i,
                            gain_d=tilt_pid.position_d,
                            gain_ffa=tilt_pid.position_ffa,
                            gain_ffv=tilt_pid.position_ffv,
                            current_p=tilt_pid.current_p,
                            current_i=tilt_pid.current_i,
                        ),
                    )
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.pan.epos.WhichOneof("reply") != "ack"
            or reply.pulczar.gimbal.servos.tilt.epos.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to set gimbal pid: reply: {reply}", reply=reply)

    async def gimbal_configure_pids(
        self,
        pan_pid: PulczarServoPIDConfig,
        tilt_pid: PulczarServoPIDConfig,
        type: PID_Config_Request_Type = PID_Config_Request_Type.FALLBACK,
    ) -> None:
        if not is_bud():
            return await self.gimbal_configure_pids_v2(pan_pid, tilt_pid, type)
        request = self._create_servos_request(
            pan=servo_pb.Request(
                epos=epos_pb.Request(
                    pid=epos_pb.Set_PID_Request(
                        current_p=pan_pid.current_p,
                        current_i=pan_pid.current_i,
                        positional_pid=epos_pb.Set_Positional_PID_Request(
                            gain_p=pan_pid.position_p,
                            gain_i=pan_pid.position_i,
                            gain_d=pan_pid.position_d,
                            gain_ffv=pan_pid.position_ffv,
                            gain_ffa=pan_pid.position_ffa,
                        ),
                    )
                )
            ),
            tilt=servo_pb.Request(
                epos=epos_pb.Request(
                    pid=epos_pb.Set_PID_Request(
                        current_p=tilt_pid.current_p,
                        current_i=tilt_pid.current_i,
                        positional_pid=epos_pb.Set_Positional_PID_Request(
                            gain_p=tilt_pid.position_p,
                            gain_i=tilt_pid.position_i,
                            gain_d=tilt_pid.position_d,
                            gain_ffv=tilt_pid.position_ffv,
                            gain_ffa=tilt_pid.position_ffa,
                        ),
                    )
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.pan.epos.WhichOneof("reply") != "ack"
            or reply.pulczar.gimbal.servos.tilt.epos.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to set gimbal pid: reply: {reply}", reply=reply)

    async def gimbal_read_pids(self) -> Tuple[Optional[PulczarServoPIDConfig], Optional[PulczarServoPIDConfig]]:
        request = self._create_servos_request(
            pan=servo_pb.Request(epos=epos_pb.Request(get_pid=epos_pb.Get_PID_Request())),
            tilt=servo_pb.Request(epos=epos_pb.Request(get_pid=epos_pb.Get_PID_Request())),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.pan.epos.WhichOneof("reply") != "pid"
            or reply.pulczar.gimbal.servos.tilt.epos.WhichOneof("reply") != "pid"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to set gimbal pid: reply: {reply}", reply=reply)
        pan_pid: Optional[PulczarServoPIDConfig] = None
        tilt_pid: Optional[PulczarServoPIDConfig] = None
        if reply.pulczar.gimbal.servos.pan.epos.pid.valid:
            assert reply.pulczar.gimbal.servos.pan.epos.pid.WhichOneof("pid") == "epos"
            pan_pid = PulczarServoPIDConfig(
                current_p=reply.pulczar.gimbal.servos.pan.epos.pid.epos.current_p,
                current_i=reply.pulczar.gimbal.servos.pan.epos.pid.epos.current_i,
                position_p=reply.pulczar.gimbal.servos.pan.epos.pid.epos.gain_p,
                position_i=reply.pulczar.gimbal.servos.pan.epos.pid.epos.gain_i,
                position_d=reply.pulczar.gimbal.servos.pan.epos.pid.epos.gain_d,
                position_ffa=reply.pulczar.gimbal.servos.pan.epos.pid.epos.gain_ffa,
                position_ffv=reply.pulczar.gimbal.servos.pan.epos.pid.epos.gain_ffv,
            )
        if reply.pulczar.gimbal.servos.tilt.epos.pid.valid:
            assert reply.pulczar.gimbal.servos.tilt.epos.pid.WhichOneof("pid") == "epos"
            tilt_pid = PulczarServoPIDConfig(
                current_p=reply.pulczar.gimbal.servos.tilt.epos.pid.epos.current_p,
                current_i=reply.pulczar.gimbal.servos.tilt.epos.pid.epos.current_i,
                position_p=reply.pulczar.gimbal.servos.tilt.epos.pid.epos.gain_p,
                position_i=reply.pulczar.gimbal.servos.tilt.epos.pid.epos.gain_i,
                position_d=reply.pulczar.gimbal.servos.tilt.epos.pid.epos.gain_d,
                position_ffa=reply.pulczar.gimbal.servos.tilt.epos.pid.epos.gain_ffa,
                position_ffv=reply.pulczar.gimbal.servos.tilt.epos.pid.epos.gain_ffv,
            )
        return (pan_pid, tilt_pid)

    # not supported in servo firmware
    async def gimbal_enable(self) -> None:
        request = self._create_servos_request(
            pan=servo_pb.Request(epos=epos_pb.Request(enable=epos_pb.Enable_Request())),
            tilt=servo_pb.Request(epos=epos_pb.Request(enable=epos_pb.Enable_Request())),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.pan.epos.WhichOneof("reply") != "ack"
            or reply.pulczar.gimbal.servos.tilt.epos.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to gimbal enable reply: {reply}", reply=reply)

    async def controller_is_faulhaber(self) -> bool:
        FAULHABER_VENDOR_ID = 327
        (vendor_id_pan, vendor_id_tilt) = await self.get_gimbal_SDO(0x1018, 0x1)
        if vendor_id_pan == FAULHABER_VENDOR_ID:
            return True
        else:
            return False

    async def get_gimbal_error_codes(self) -> Tuple[int, int]:
        MAXON_ERROR_REGISTER = 0x603F
        FAULHABER_ERROR_REGISTER = 0x2320
        if await self.controller_is_faulhaber():
            return await self.get_gimbal_SDO(FAULHABER_ERROR_REGISTER, 0x00)
        else:
            return await self.get_gimbal_SDO(MAXON_ERROR_REGISTER, 0x00)

    async def get_gimbal_control_words(self) -> Tuple[int, int]:
        return await self.get_gimbal_SDO(0x6040, 0x00)

    async def set_gimbal_control_words(self, value: int) -> None:
        await self.set_gimbal_SDO(0x6040, 0x00, value, value)

    async def clear_gimbal_fault(self) -> None:
        await self.set_gimbal_control_words(0b10101111)
        await self.gimbal_enable()

    async def get_gimbal_status_words(self) -> Tuple[int, int]:
        return await self.get_gimbal_SDO(0x6041, 0x00)

    async def gimbal_boot(
        self,
        profile_velocity: int = 25000,
        min_position: int = 0,
        max_position: int = 30000,
        step_size: int = 1000,
        offset: Tuple[int, int] = (1000, 1000),
        invert: Tuple[bool, bool] = (False, False),
    ) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                gimbal=gimbal_pb.Request(
                    boot=gimbal_pb.Boot_Request(
                        pan_params=epos_pb.Home_Params(
                            profile_velocity=abs(profile_velocity),
                            max_position=max_position,
                            min_position=min_position,
                            hard_stop=epos_pb.Hard_Home_Params(step_size=step_size, offset=offset[0]),
                            invert=invert[0],
                        ),
                        tilt_params=epos_pb.Home_Params(
                            profile_velocity=abs(profile_velocity),
                            max_position=max_position,
                            min_position=min_position,
                            hard_stop=epos_pb.Hard_Home_Params(step_size=step_size, offset=offset[1]),
                            invert=invert[1],
                        ),
                    ),
                ),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=20000)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "ack"
        ):
            raise PulczarGimbalException(f"{self._identifier} Failed to boot gimbal: {reply}", reply=reply)

    # Note this method should only be used for tuning outside of a scanner assembly
    async def gimbal_boot_in_place(
        self,
        profile_velocity: int = 25000,
        min_position: int = 0,  # unused in firmware
        max_position: int = 30000,  # unused in firmware
        range: Tuple[int, int] = (14000, 14000),
        invert: Tuple[bool, bool] = (False, False),
    ) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                gimbal=gimbal_pb.Request(
                    boot=gimbal_pb.Boot_Request(
                        pan_params=epos_pb.Home_Params(
                            profile_velocity=abs(profile_velocity),
                            max_position=max_position,
                            min_position=min_position,
                            actual_position=epos_pb.Actual_Position_Home_Params(range=range[0]),
                            invert=invert[0],
                        ),
                        tilt_params=epos_pb.Home_Params(
                            profile_velocity=abs(profile_velocity),
                            max_position=max_position,
                            min_position=min_position,
                            actual_position=epos_pb.Actual_Position_Home_Params(range=range[1]),
                            invert=invert[1],
                        ),
                    ),
                ),
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=20000)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "ack"
        ):
            raise PulczarGimbalException(f"{self._identifier} Failed to boot gimbal: {reply}", reply=reply)

    async def gimbal_stop(self) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(gimbal=gimbal_pb.Request(stop=gimbal_pb.Stop_Request()))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to stop gimbal: {reply}", reply=reply)

    async def gimbal_config(
        self,
        pan_node_id: int,
        tilt_node_id: int,
        max_profile_velocity_mrpm: int,
        settle_timeout: int,
        settle_window: int,
        max_diff_millis: int,
    ) -> None:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                config=servo_pb.Config_Request(
                    node_id=pan_node_id,
                    config=servo_pb.Config(
                        max_profile_velocity=abs(max_profile_velocity_mrpm),
                        settle_window=settle_window,
                        settle_timeout=settle_timeout,
                        max_diff_millis=max_diff_millis,
                    ),
                )
            ),
            tilt=servo_pb.Request(
                config=servo_pb.Config_Request(
                    node_id=tilt_node_id,
                    config=servo_pb.Config(
                        max_profile_velocity=abs(max_profile_velocity_mrpm),
                        settle_window=settle_window,
                        settle_timeout=settle_timeout,
                        max_diff_millis=max_diff_millis,
                    ),
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "ack"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to configure gimbal: {reply}", reply=reply)

    async def gimbal_go_to(self, position: Tuple[int, int], velocity: Tuple[int, int], await_settle: bool) -> None:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                go_to=servo_pb.Go_To_Request(position=position[0], velocity=abs(velocity[0]), await_settle=await_settle)
            ),
            tilt=servo_pb.Request(
                go_to=servo_pb.Go_To_Request(position=position[1], velocity=abs(velocity[1]), await_settle=await_settle)
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "ack"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "ack"
        ):
            pan_code, tilt_code = await self.get_gimbal_error_codes()
            raise PulczarBoardException(
                f"{self._identifier} Failed to go to gimbal position: {position} with velocity: {velocity} reply: {reply}, Error Codes: Pan: {pan_code}, Tilt: {tilt_code}",
                reply=reply,
            )

    async def gimbal_go_to_delta(
        self, delta_position: Tuple[int, int], velocity: Tuple[int, int], mode: GoToModeType
    ) -> Tuple[int, int]:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                delta=servo_pb.Go_To_Delta_Request(
                    delta_position=delta_position[0], velocity=abs(velocity[0]), mode=mode
                )
            ),
            tilt=servo_pb.Request(
                delta=servo_pb.Go_To_Delta_Request(
                    delta_position=delta_position[1], velocity=abs(velocity[1]), mode=mode
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "pos"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "pos"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to go to delta position: {delta_position} with velocity: {velocity} reply: {reply}",
                reply=reply,
            )
        return reply.pulczar.gimbal.servos.pan.pos.position, reply.pulczar.gimbal.servos.tilt.pos.position

    async def gimbal_go_to_delta_follow(
        self,
        delta_position: Tuple[int, int],
        velocity: Tuple[int, int],
        follow_velocity_vector: Tuple[int, int],
        follow_velocity_mrpm: Tuple[int, int],
        interval_sleep_time_ms: int,
        mode: GoToModeType,
        fast_return: bool,
        timeout_ms: int = 1000,
    ) -> Tuple[int, int]:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                follow=servo_pb.Go_To_Delta_Follow_Request(
                    delta=servo_pb.Go_To_Delta_Request(
                        delta_position=delta_position[0], velocity=abs(velocity[0]), mode=mode
                    ),
                    follow_velocity_vector=follow_velocity_vector[0],
                    follow_velocity_mrpm=abs(follow_velocity_mrpm[0]),
                    interval_sleep_time_ms=interval_sleep_time_ms,
                    fast_return=fast_return,
                )
            ),
            tilt=servo_pb.Request(
                follow=servo_pb.Go_To_Delta_Follow_Request(
                    delta=servo_pb.Go_To_Delta_Request(
                        delta_position=delta_position[1], velocity=abs(velocity[1]), mode=mode
                    ),
                    follow_velocity_vector=follow_velocity_vector[1],
                    follow_velocity_mrpm=abs(follow_velocity_mrpm[1]),
                    interval_sleep_time_ms=interval_sleep_time_ms,
                    fast_return=fast_return,
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=timeout_ms)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "pos"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "pos"
        ):
            pan_error = (
                reply.pulczar.gimbal.servos.pan.WhichOneof("reply") == "error"
                and reply.pulczar.gimbal.servos.pan.error.code == 806
            )
            tilt_error = (
                reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") == "error"
                and reply.pulczar.gimbal.servos.tilt.error.code == 806
            )
            if pan_error or tilt_error:
                raise PulczarOutOfBoundsException(
                    f"Pulczar {self._identifier} Delta Servos Move {delta_position} is Out of Bounds",
                    error_in_pan=pan_error,
                    error_in_tilt=tilt_error,
                )
            raise PulczarBoardException(
                f"{self._identifier} Failed to go to delta follow position: {delta_position} with velocity: {velocity} reply: {reply}",
                reply=reply,
            )
        return reply.pulczar.gimbal.servos.pan.pos.position, reply.pulczar.gimbal.servos.tilt.pos.position

    def _timestamp_to_ms(self, timestamp: time_pb.Timestamp) -> int:
        return int((timestamp.seconds * 1000) + int(timestamp.micros / 1000))

    async def gimbal_go_to_timestamp(
        self,
        timestamp_ms: int,
        mode: GoToModeType,
        position: Tuple[int, int],
        velocity_mrpm: Tuple[int, int],
        follow_velocity: Tuple[int, int],
        follow_accel: Tuple[int, int],
        interval_sleep_time_ms: int,
        timeout_ms: int = 10000,
    ) -> Tuple[Tuple[Tuple[int, int], Tuple[int, int]], Tuple[Tuple[int, int], Tuple[int, int]]]:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                go_to_timestamp=servo_pb.Go_To_Timestamp_Request(
                    timestamp=time_pb.Timestamp(seconds=int(timestamp_ms / 1000), micros=(timestamp_ms % 1000) * 1000),
                    mode=mode,
                    position=position[0],
                    velocity_mrpm=abs(velocity_mrpm[0]),
                    follow_velocity=follow_velocity[0],
                    follow_accel=follow_accel[0],
                    interval_sleep_time_ms=interval_sleep_time_ms,
                )
            ),
            tilt=servo_pb.Request(
                go_to_timestamp=servo_pb.Go_To_Timestamp_Request(
                    timestamp=time_pb.Timestamp(seconds=int(timestamp_ms / 1000), micros=(timestamp_ms % 1000) * 1000),
                    mode=mode,
                    position=position[1],
                    velocity_mrpm=abs(velocity_mrpm[1]),
                    follow_velocity=follow_velocity[1],
                    follow_accel=follow_accel[1],
                    interval_sleep_time_ms=interval_sleep_time_ms,
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=timeout_ms)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "go_to_timestamp"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "go_to_timestamp"
        ):
            pan_error = (
                reply.pulczar.gimbal.servos.pan.WhichOneof("reply") == "error"
                and reply.pulczar.gimbal.servos.pan.error.code == 806
            )
            tilt_error = (
                reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") == "error"
                and reply.pulczar.gimbal.servos.tilt.error.code == 806
            )
            if pan_error or tilt_error:
                raise PulczarOutOfBoundsException(
                    "Pulczar Servos Move is Out of Bounds", error_in_pan=pan_error, error_in_tilt=tilt_error
                )
            raise PulczarBoardException(
                f"{self._identifier} Failed to go to timestamp: {timestamp_ms}, time_diff: {maka_control_timestamp_ms() - timestamp_ms}, position: {position} with velocity: {follow_velocity} reply: {reply}",
                reply=reply,
            )

        timestamped_pre_position = (
            (
                self._timestamp_to_ms(reply.pulczar.gimbal.servos.pan.go_to_timestamp.pre_timestamp),
                self._timestamp_to_ms(reply.pulczar.gimbal.servos.tilt.go_to_timestamp.pre_timestamp),
            ),
            (
                reply.pulczar.gimbal.servos.pan.go_to_timestamp.pre_position,
                reply.pulczar.gimbal.servos.tilt.go_to_timestamp.pre_position,
            ),
        )

        timestamped_post_position = (
            (
                self._timestamp_to_ms(reply.pulczar.gimbal.servos.pan.go_to_timestamp.post_timestamp),
                self._timestamp_to_ms(reply.pulczar.gimbal.servos.tilt.go_to_timestamp.post_timestamp),
            ),
            (
                reply.pulczar.gimbal.servos.pan.go_to_timestamp.post_position,
                reply.pulczar.gimbal.servos.tilt.go_to_timestamp.post_position,
            ),
        )

        return timestamped_pre_position, timestamped_post_position

    async def gimbal_go_to_follow(
        self,
        timestamp_ms: int,
        position: Tuple[int, int],
        velocity_mrpm: Tuple[int, int],
        follow_velocity: Tuple[int, int],
        follow_accel: Tuple[int, int],
        interval_sleep_time_ms: int,
    ) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                go_to_follow=servo_pb.Go_To_Follow_Request(
                    timestamp=time_pb.Timestamp(seconds=int(timestamp_ms / 1000), micros=(timestamp_ms % 1000) * 1000),
                    position=position[0],
                    velocity_mrpm=abs(velocity_mrpm[0]),
                    follow_velocity=follow_velocity[0],
                    follow_accel=follow_accel[0],
                    interval_sleep_time_ms=interval_sleep_time_ms,
                )
            ),
            tilt=servo_pb.Request(
                go_to_follow=servo_pb.Go_To_Follow_Request(
                    timestamp=time_pb.Timestamp(seconds=int(timestamp_ms / 1000), micros=(timestamp_ms % 1000) * 1000),
                    position=position[1],
                    velocity_mrpm=abs(velocity_mrpm[1]),
                    follow_velocity=follow_velocity[1],
                    follow_accel=follow_accel[1],
                    interval_sleep_time_ms=interval_sleep_time_ms,
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=10000)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "go_to_follow"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "go_to_follow"
        ):
            pan_error = (
                reply.pulczar.gimbal.servos.pan.WhichOneof("reply") == "error"
                and reply.pulczar.gimbal.servos.pan.error.code == 806
            )
            tilt_error = (
                reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") == "error"
                and reply.pulczar.gimbal.servos.tilt.error.code == 806
            )
            if pan_error or tilt_error:
                raise PulczarOutOfBoundsException(
                    "Pulczar Servos Move is Out of Bounds", error_in_pan=pan_error, error_in_tilt=tilt_error
                )
            LOG.info(f"is of type: '{reply.pulczar.gimbal.WhichOneof('reply')}'")
            raise PulczarBoardException(
                f"{self._identifier} Failed to go to timestamp: {timestamp_ms}, time_diff: {maka_control_timestamp_ms() - timestamp_ms}, position: {position} with velocity: {follow_velocity} reply: {reply}",
                reply=reply,
            )

        timestamped_pre_position = (
            (
                self._timestamp_to_ms(reply.pulczar.gimbal.servos.pan.go_to_follow.pre_timestamp),
                self._timestamp_to_ms(reply.pulczar.gimbal.servos.tilt.go_to_follow.pre_timestamp),
            ),
            (
                reply.pulczar.gimbal.servos.pan.go_to_follow.pre_position,
                reply.pulczar.gimbal.servos.tilt.go_to_follow.pre_position,
            ),
        )

        return timestamped_pre_position

    async def gimbal_follow_timestamp(
        self, timestamp_ms: int, follow_velocity: Tuple[int, int], follow_accel: Tuple[int, int],
    ) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                follow_timestamp=servo_pb.Follow_Timestamp_Request(
                    timestamp=time_pb.Timestamp(seconds=int(timestamp_ms / 1000), micros=(timestamp_ms % 1000) * 1000),
                    follow_velocity=follow_velocity[0],
                    follow_accel=follow_accel[0],
                )
            ),
            tilt=servo_pb.Request(
                follow_timestamp=servo_pb.Follow_Timestamp_Request(
                    timestamp=time_pb.Timestamp(seconds=int(timestamp_ms / 1000), micros=(timestamp_ms % 1000) * 1000),
                    follow_velocity=follow_velocity[1],
                    follow_accel=follow_accel[1],
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=10000)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "follow_timestamp"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "follow_timestamp"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to follow timestamp with velocity: {follow_velocity} reply: {reply}",
                reply=reply,
            )

        timestamped_pre_position = (
            (
                self._timestamp_to_ms(reply.pulczar.gimbal.servos.pan.follow_timestamp.pre_timestamp),
                self._timestamp_to_ms(reply.pulczar.gimbal.servos.tilt.follow_timestamp.pre_timestamp),
            ),
            (
                reply.pulczar.gimbal.servos.pan.follow_timestamp.pre_position,
                reply.pulczar.gimbal.servos.tilt.follow_timestamp.pre_position,
            ),
        )

        return timestamped_pre_position

    async def gimbal_get_pos_vel(self) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        request = self._create_servos_request(
            pan=servo_pb.Request(epos=epos_pb.Request(pos_vel=epos_pb.Get_Pos_Vel_Request())),
            tilt=servo_pb.Request(epos=epos_pb.Request(pos_vel=epos_pb.Get_Pos_Vel_Request())),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=100)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "epos"
            or reply.pulczar.gimbal.servos.pan.epos.WhichOneof("reply") != "pos_vel"
            or reply.pulczar.gimbal.servos.tilt.epos.WhichOneof("reply") != "pos_vel"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to get gimbal position and velocity: {reply}", reply=reply
            )
        return (
            (
                reply.pulczar.gimbal.servos.pan.epos.pos_vel.position,
                reply.pulczar.gimbal.servos.tilt.epos.pos_vel.position,
            ),
            (
                reply.pulczar.gimbal.servos.pan.epos.pos_vel.velocity,
                reply.pulczar.gimbal.servos.tilt.epos.pos_vel.velocity,
            ),
        )

    async def gimbal_go_to_calibrate(
        self, position: int, velocity: int, window: int, time_window_ms: int, timeout_ms: int, period_ms: int
    ) -> Tuple[int, int]:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                calibrate=servo_pb.Go_To_Calibrate_Request(
                    position=position,
                    velocity=abs(velocity),
                    window=window,
                    time_window_ms=time_window_ms,
                    timeout_ms=timeout_ms,
                    period_ms=period_ms,
                )
            ),
            tilt=servo_pb.Request(
                calibrate=servo_pb.Go_To_Calibrate_Request(
                    position=position,
                    velocity=abs(velocity),
                    window=window,
                    time_window_ms=time_window_ms,
                    timeout_ms=timeout_ms,
                    period_ms=period_ms,
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=10000)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to go to gimbal Pan position: {position} with velocity: {velocity}, : {reply}",
                reply=reply,
            )
        pan_time_ms = timeout_ms
        if reply.pulczar.gimbal.servos.pan.WhichOneof("reply") == "settle":
            pan_time_ms = reply.pulczar.gimbal.servos.pan.settle.settle_time
        elif reply.pulczar.gimbal.servos.pan.WhichOneof("reply") == "error":
            LOG.error(
                f"Error For Pan Servo {self._identifier} Calibration: {reply.pulczar.gimbal.servos.pan.error.code}"
            )
        tilt_time_ms = timeout_ms
        if reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") == "settle":
            tilt_time_ms = reply.pulczar.gimbal.servos.tilt.settle.settle_time
        elif reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") == "error":
            LOG.error(
                f"Error For Tilt Servo {self._identifier} Calibration: {reply.pulczar.gimbal.servos.tilt.error.code}"
            )

        return pan_time_ms, tilt_time_ms

    async def gimbal_go_to_calibrate_profile_vel(
        self,
        position: int,
        velocity: Tuple[int, int],
        window: int,
        time_window_ms: int,
        timeout_ms: int,
        period_ms: int,
    ) -> Tuple[int, int]:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                calibrate=servo_pb.Go_To_Calibrate_Request(
                    position=position,
                    velocity=abs(velocity[0]),
                    window=window,
                    time_window_ms=time_window_ms,
                    timeout_ms=timeout_ms,
                    period_ms=period_ms,
                )
            ),
            tilt=servo_pb.Request(
                calibrate=servo_pb.Go_To_Calibrate_Request(
                    position=position,
                    velocity=abs(velocity[1]),
                    window=window,
                    time_window_ms=time_window_ms,
                    timeout_ms=timeout_ms,
                    period_ms=period_ms,
                )
            ),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=10000)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to go to gimbal Pan position: {position} with velocity: {velocity}, : {reply}",
                reply=reply,
            )
        pan_time_ms = 1000
        if reply.pulczar.gimbal.servos.pan.WhichOneof("reply") == "settle":
            pan_time_ms = reply.pulczar.gimbal.servos.pan.settle.settle_time
        elif reply.pulczar.gimbal.servos.pan.WhichOneof("reply") == "error":
            LOG.error(
                f"Error For Pan Servo {self._identifier} Calibration: {reply.pulczar.gimbal.servos.pan.error.code}"
            )
        tilt_time_ms = 1000
        if reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") == "settle":
            tilt_time_ms = reply.pulczar.gimbal.servos.tilt.settle.settle_time
        elif reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") == "error":
            LOG.error(
                f"Error For Tilt Servo {self._identifier} Calibration: {reply.pulczar.gimbal.servos.tilt.error.code}"
            )

        return pan_time_ms, tilt_time_ms

    async def gimbal_go_to_pan(self, position: int, velocity: int, await_settle: bool) -> None:
        request = self._create_servos_request(
            pan=servo_pb.Request(
                go_to=servo_pb.Go_To_Request(position=position, velocity=abs(velocity), await_settle=await_settle)
            ),
            tilt=servo_pb.Request(
                limit=servo_pb.Get_Limits_Request()
            ),  # Limits is used as a placeholder for do nothing
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to go to gimbal Pan position: {position} with velocity: {velocity}, : {reply}",
                reply=reply,
            )

    async def gimbal_go_to_tilt(self, position: int, velocity: int, await_settle: bool) -> None:
        request = self._create_servos_request(
            tilt=servo_pb.Request(
                go_to=servo_pb.Go_To_Request(position=position, velocity=abs(velocity), await_settle=await_settle)
            ),
            pan=servo_pb.Request(limit=servo_pb.Get_Limits_Request()),  # Limits is used as a placeholder for do nothing
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to go to gimbal Tilt position: {position} with velocity: {velocity}, : {reply}",
                reply=reply,
            )

    async def gimbal_get_limits(self) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        request = self._create_servos_request(
            pan=servo_pb.Request(limit=servo_pb.Get_Limits_Request()),
            tilt=servo_pb.Request(limit=servo_pb.Get_Limits_Request()),
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "servos"
            or reply.pulczar.gimbal.servos.pan.WhichOneof("reply") != "limit"
            or reply.pulczar.gimbal.servos.tilt.WhichOneof("reply") != "limit"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to get limits: {reply}", reply=reply)
        return (
            (reply.pulczar.gimbal.servos.pan.limit.min, reply.pulczar.gimbal.servos.pan.limit.max),
            (reply.pulczar.gimbal.servos.tilt.limit.min, reply.pulczar.gimbal.servos.tilt.limit.max),
        )

    async def gimbal_get_pos_at_time(self, timestamp_us: int) -> Tuple[PositionAt, PositionAt]:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                gimbal=gimbal_pb.Request(position=gimbal_pb.GetPositionAtRequest(timestamp_us=timestamp_us))
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "gimbal"
            or reply.pulczar.gimbal.WhichOneof("reply") != "position"
            or not reply.pulczar.gimbal.position.current.valid
            or not reply.pulczar.gimbal.position.requested.valid
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to get position: {reply}", reply=reply)
        resp = reply.pulczar.gimbal.position
        return (
            PositionAt(resp.requested.pan, resp.requested.tilt, resp.requested.timestamp_us),
            PositionAt(resp.current.pan, resp.current.tilt, resp.current.timestamp_us),
        )

    # ### Laser ### #

    async def laser_set(self, on: bool) -> PulczarBoardFireStatus:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(laser=laser_pb.Request(laser=laser_pb.Laser_Request(on=on)))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=100)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "laser"
            or reply.pulczar.laser.WhichOneof("reply") != "laser_reply"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to turn laser on/off: {reply}", reply=reply)
        lrp = reply.pulczar.laser.laser_reply
        return PulczarBoardFireStatus(
            lrp.lpsu_state, lrp.on, lrp.fireable, lrp.raw_therm1_reading_mv, lrp.raw_therm2_reading_mv
        )

    async def laser_intensity(self, intensity: int) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                laser=laser_pb.Request(intensity=laser_pb.Intensity_Request(intensity=intensity))
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "laser"
            or reply.pulczar.laser.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to set laser intensity: {reply}", reply=reply)

    # ### Dawg ## #

    async def dawg_pet(self, firing: bool) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(dawg=dawg_pb.Request(pet=dawg_pb.Pet_Request(firing=firing)))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "dawg"
            or reply.pulczar.dawg.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to pet Dawg: {reply}", reply=reply)

    async def dawg_arm(self, armed: bool) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(dawg=dawg_pb.Request(arm=dawg_pb.Arm_Request(armed=armed)))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "dawg"
            or reply.pulczar.dawg.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to arm Dawg: {reply}", reply=reply)

    async def dawg_config(self, timeout_ms: int) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(dawg=dawg_pb.Request(config=dawg_pb.Config_Request(timeout_ms=timeout_ms)))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "dawg"
            or reply.pulczar.dawg.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to Config Dawg: {reply}", reply=reply)

    # ### Lens ### #

    async def lens_set(self, value: int) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(lens=lens_pb.Request(set=lens_pb.Set_Request(value=value)))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "lens"
            or reply.pulczar.lens.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to set lens: {reply}", reply=reply)

    async def lens_get(self) -> int:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(lens=lens_pb.Request(get=lens_pb.Get_Request()))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "lens"
            or reply.pulczar.lens.WhichOneof("reply") != "get"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to get lens: {reply}", reply=reply)
        return reply.pulczar.lens.get.value

    # ### PPS ### #

    async def set_epoch_time(self) -> None:
        # Get a timestamp not near a second boundary
        timestamp_ms = maka_control_timestamp_ms()
        while timestamp_ms % 1000 > 800 or timestamp_ms % 1000 < 200:
            await asyncio.sleep(0.2)
            timestamp_ms = maka_control_timestamp_ms()
        request = self._nano_connector.create_request(
            time=time_pb.Request(
                set=time_pb.Set_Epoch_Time_Request(
                    timestamp=time_pb.Timestamp(
                        seconds=int(timestamp_ms / 1000), micros=((timestamp_ms % 1000) * 1000)
                    ),
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "time" or reply.time.WhichOneof("reply") != "ack":
            raise PulczarBoardException(f"{self._identifier} Failed to set epoch: {reply}", reply=reply)

    async def get_timestamp(self) -> int:
        request = self._nano_connector.create_request(time=time_pb.Request(get=time_pb.Get_Timestamp_Request()))
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "time" or reply.time.WhichOneof("reply") != "timestamp":
            raise PulczarBoardException(f"{self._identifier} Failed to get timestamp: {reply}", reply=reply)
        return int((reply.time.timestamp.seconds * 1000) + round(reply.time.timestamp.micros / 1000))

    async def get_time_debug(self) -> TimeDebug:
        request = self._nano_connector.create_request(time=time_pb.Request(debug=time_pb.Get_Debug_Timestamp_Request()))
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "time" or reply.time.WhichOneof("reply") != "debug":
            raise PulczarBoardException(f"{self._identifier} Failed to get time debug: {reply}", reply=reply)
        return TimeDebug(
            timestamp_us=int(reply.time.debug.timestamp.seconds * 1000000 + reply.time.debug.timestamp.micros),
            pps_timer_val=reply.time.debug.pps_timer_val,
            pps_ticks=reply.time.debug.pps_ticks,
            freq_mul=reply.time.debug.freq_mul,
            error_ticks=reply.time.debug.error_ticks,
            error_ticks2=reply.time.debug.error_ticks2,
        )

    async def set_delta_target_config(self, pan_skew: float, tilt_skew: float) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                conf=scanner_config_pb.Request(
                    set_dt=scanner_config_pb.Set_Delta_Target_Config_Request(
                        config=scanner_config_pb.Delta_Target_Config(pan_skew=pan_skew, tilt_skew=tilt_skew)
                    )
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "conf"
            or reply.pulczar.conf.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to set Delta Target Config: {reply}", reply=reply)

    async def get_delta_target_config(self) -> Tuple[float, float]:
        if self._bootloader_type == "psoc":
            return float(0), float(0)
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                conf=scanner_config_pb.Request(get_dt=scanner_config_pb.Get_Delta_Target_Config_Request())
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "conf"
            or reply.pulczar.conf.WhichOneof("reply") != "dt"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to Get Delta Target Config: {reply}", reply=reply)
        return float(reply.pulczar.conf.dt.config.pan_skew), float(reply.pulczar.conf.dt.config.tilt_skew)

    async def set_color_config(self, red: float, green: float, blue: float) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                conf=scanner_config_pb.Request(
                    set_color=scanner_config_pb.Set_Color_Config_Request(
                        config=scanner_config_pb.Color_Config(red=red, green=green, blue=blue)
                    )
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "conf"
            or reply.pulczar.conf.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to set Color Config: {reply}", reply=reply)

    async def get_color_config(self) -> Tuple[float, float, float]:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                conf=scanner_config_pb.Request(get_color=scanner_config_pb.Get_Color_Config_Request())
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "conf"
            or reply.pulczar.conf.WhichOneof("reply") != "color"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to Get Delta Target Config: {reply}", reply=reply)
        return (
            float(reply.pulczar.conf.color.config.red),
            float(reply.pulczar.conf.color.config.green),
            float(reply.pulczar.conf.color.config.blue),
        )

    async def set_serial_number_config(self, serial_number: str) -> None:
        data = serial_number.encode("utf-8").ljust(32, b"\0")
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                conf=scanner_config_pb.Request(
                    set_sn=scanner_config_pb.Set_Camera_Serial_Number_Config_Request(
                        config=scanner_config_pb.Camera_Serial_Number_Config(serial_number=data)
                    )
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "conf"
            or reply.pulczar.conf.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to set Camera Serial Number Config: {reply}", reply=reply
            )

    async def get_serial_number_config(self) -> str:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                conf=scanner_config_pb.Request(get_sn=scanner_config_pb.Get_Camera_Serial_Number_Config_Request())
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "conf"
            or reply.pulczar.conf.WhichOneof("reply") != "sn"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to Get Camera Serial Number Config: {reply}", reply=reply
            )
        data = reply.pulczar.conf.sn.config.serial_number
        return str(data.decode("utf-8").rstrip("\x00"))

    async def set_hw_rev_config(self, rev: HwRevEnum) -> None:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                conf=scanner_config_pb.Request(set_hw=scanner_config_pb.Set_HW_Revision_Request(revision=rev.value))
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "conf"
            or reply.pulczar.conf.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to set hardware revision: {reply}", reply=reply)

    async def get_hw_rev_config(self) -> HwRevEnum:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                conf=scanner_config_pb.Request(get_hw=scanner_config_pb.Get_HW_Revision_Request())
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "conf"
            or reply.pulczar.conf.WhichOneof("reply") != "hw"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to get hardware revision: {reply}", reply=reply)

        try:
            return HwRevEnum(reply.pulczar.conf.hw.revision)
        except Exception:
            return HwRevEnum.MAXON  # default to maxon if not valid

    async def set_scanner_assembly_sn(self, barcode_str: str) -> None:
        data = barcode_str.encode("utf-8").ljust(16, b"\0")
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                conf=scanner_config_pb.Request(
                    set_bc=scanner_config_pb.Set_Scanner_Barcode_Str_Request(
                        config=scanner_config_pb.Scanner_Barcode_Str_Config(barcode=data)
                    )
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "conf"
            or reply.pulczar.conf.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to set scanner assembly sn: {reply}", reply=reply)

    async def get_scanner_assembly_sn(self) -> Optional[str]:
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                conf=scanner_config_pb.Request(get_bc=scanner_config_pb.Get_Scanner_Barcode_Str_Request())
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "conf"
            or reply.pulczar.conf.WhichOneof("reply") != "bc"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to get scanner assembly sn: {reply}", reply=reply)
        if not reply.pulczar.conf.bc.HasField("config"):
            return None
        data = reply.pulczar.conf.bc.config.barcode
        return str(data.decode("utf-8").rstrip("\x00"))

    class LaserDebugData(NamedTuple):
        # voltage on thermistor 1 input
        therm1: Optional[float] = None
        # voltage on thermistor 2 input
        therm2: Optional[float] = None

        # voltage on photodiode
        photodiode: Optional[float] = None

        @classmethod
        def FromMessage(cls, packet: laser_pb.Raw_Data_Reply) -> Self:
            return cls(therm1=packet.therm1_raw, therm2=packet.therm2_raw, photodiode=packet.photodiode_raw)

    async def get_laser_debug_data(self) -> LaserDebugData:
        """
        Get the raw voltages/ADC values for various inputs to the laser subsystem

        Primarily intended for debugging and testing use
        """
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(laser=laser_pb.Request(raw_data=laser_pb.Raw_Data_Request()))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)

        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "laser"
            or reply.pulczar.laser.WhichOneof("reply") != "raw_data"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to get laser debug data", reply=reply)

        return self.LaserDebugData(
            therm1=reply.pulczar.laser.raw_data.therm1_raw,
            therm2=reply.pulczar.laser.raw_data.therm2_raw,
            photodiode=reply.pulczar.laser.raw_data.photodiode_raw,
        )

    class PowerState(NamedTuple):
        targetCam: Optional[bool] = None
        firingBoard: Optional[bool] = None

        @classmethod
        def FromMessage(cls, packet: pulczar_pb.Power_Reply) -> Self:
            return cls(targetCam=packet.targetCam, firingBoard=packet.firingBoard)

        def toMessage(self) -> pulczar_pb.Power_Request:
            req = pulczar_pb.Power_Request()

            if self.targetCam is not None:
                req.targetCam = self.targetCam
            if self.firingBoard is not None:
                req.firingBoard = self.firingBoard

            return req

    async def get_power(self) -> PowerState:
        """
        Query the current state of switchable +24V power rails
        """
        return await self._power_query(None)

    async def set_power(self, newState: PowerState) -> PowerState:
        """
        Update the state of switchable +24V power rails
        """
        # Slayer scanners obviously don't have firing boards
        if is_slayer():
            assert not newState.firingBoard

        return await self._power_query(newState)

    async def _power_query(self, newState: Optional[PowerState]) -> PowerState:
        """
        Send a request to the power control endpoint

        This can be used to query the current state (if argument is None) or update it as well; the
        actual state (after making any changes) is always reflected by the return value.
        """
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(power=(newState or self.PowerState()).toMessage())
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)

        if reply.WhichOneof("reply") != "pulczar" or reply.pulczar.WhichOneof("reply") != "power":
            raise PulczarBoardException(f"{self._identifier} Failed to decode power data", reply=reply)

        return self.PowerState.FromMessage(reply.pulczar.power)

    # general laser stuff
    async def get_laser_inventory(self) -> BwtInventory:
        """
        Retrieve information about the laser (serial number, model, etc.) read out during
        handshake.
        """
        if not is_reaper():
            raise PulczarBoardException("Only Reaper has smart laser")

        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(laser=laser_pb.Request(laser_inventory=laser_pb.Laser_Inventory_Request()))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)

        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "laser"
            or reply.pulczar.laser.WhichOneof("reply") != "laser_inventory"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to decode laser inventory", reply=reply)

        return BwtInventory.FromMessage(reply.pulczar.laser.laser_inventory)

    async def set_laser_currents(self, currents: List[Current], save: bool = False) -> None:
        """
        Update the current settings for the laser.
        """
        if not is_reaper():
            raise PulczarBoardException("Only Reaper has smart laser")

        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                laser=laser_pb.Request(
                    diode_set_current=laser_pb.Diode_Set_Current_Request(
                        current=[int(x.milliamps) for x in currents], commit=save
                    )
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=5000)

        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "laser"
            or reply.pulczar.laser.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to update laser currents", reply=reply)

    async def set_laser_type(self, new_type: Optional[LaserType]) -> None:
        """
        Configure the scanner for the given type of laser.
        """
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                laser=laser_pb.Request(
                    set_type=laser_pb.Laser_Set_Type_Request(
                        type=new_type.proto_value if new_type else laser_pb.LaserType.LASERTYPE_UNKNOWN
                    )
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)

        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "laser"
            or reply.pulczar.laser.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to set laser type", reply=reply)

    async def get_laser_type(self) -> LaserType:
        """
        Query the scanner for the connected type of laser
        """
        # TODO: actually query for Slayer when we have a better API
        if is_slayer():
            return LaserType.SLAYER_CO2

        inventory = await self.get_laser_inventory()
        assert inventory.laser_type, f"Invalid inventory: {inventory}"

        return inventory.laser_type

    # BWT laser status info
    # TODO: Refactor to use generic laser API instead of BWT; this is so existing code keeps working
    #       for testing
    async def get_bwt_status(self) -> BwtStatus:
        """
        Retrieve the current status of the BWT laser; this is asynchronously sampled in the
        background by a polling task so the provided current values may not correspond to when
        the laser is firing.
        """
        if not is_reaper():
            raise PulczarBoardException("Only Reaper has BWT laser")

        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(laser=laser_pb.Request(diode_status=laser_pb.Diode_Status_Request()))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)

        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "laser"
            or reply.pulczar.laser.WhichOneof("reply") != "diode_status"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to decode BWT laser status", reply=reply)

        return BwtStatus.FromMessage(reply.pulczar.laser.diode_status)

    async def get_bwt_inventory(self) -> BwtInventory:
        """
        Get BWT inventory data

        NOTE: should prefer to use get_laser_inventory instead
        """
        return await self.get_laser_inventory()

    async def run_bwt_command(self, command: str) -> str:
        """
        Send the given command to the BWT laser (via serial interface) and return the raw
        response.

        NOTE: Command does _not_ need newline/termination: added automatically
        """
        # TODO: ensure command doesn't have newline at end

        if not is_reaper():
            raise PulczarBoardException("Only Reaper has BWT laser")

        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                laser=laser_pb.Request(bwt_passthrough=laser_pb.Bwt_Passthrough_Request(command=command))
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=5000)

        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "laser"
            or reply.pulczar.laser.WhichOneof("reply") != "bwt_passthrough"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to decode BWT passthrough response", reply=reply)

        return reply.pulczar.laser.bwt_passthrough.response

    async def reset_bwt_transport(self) -> None:
        """
        Reset the BWT laser interface state machine and reinitialize the laser if it's still
        connected.
        """
        if not is_reaper():
            raise PulczarBoardException("Only Reaper has BWT laser")

        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(laser=laser_pb.Request(laser_reset=laser_pb.Laser_Reset_Request(transport=True)))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=5000)

        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "laser"
            or reply.pulczar.laser.WhichOneof("reply") != "ack"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to reset BWT transport", reply=reply)

    async def get_bwt_transport_config(self) -> BwtTransportConfig:
        """
        Get the current BWT laser transport configuration
        """
        if not is_reaper():
            raise PulczarBoardException("Only Reaper has BWT laser")

        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                laser=laser_pb.Request(bwt_get_config=laser_pb.Bwt_Transport_Get_Config_Request())
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)

        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "laser"
            or reply.pulczar.laser.WhichOneof("reply") != "bwt_config"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to query BWT transport state", reply=reply)

        return BwtTransportConfig.FromMessage(reply.pulczar.laser.bwt_config)

    async def set_bwt_transport_config(self, newSettings: BwtTransportConfig) -> BwtTransportConfig:
        """
        Update the BWT laser transport configuration
        """
        msg = laser_pb.Bwt_Transport_Config()

        msg.log_messages = newSettings.log_messages

        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(laser=laser_pb.Request(bwt_set_config=msg))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)

        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "laser"
            or reply.pulczar.laser.WhichOneof("reply") != "bwt_config"
        ):
            raise PulczarBoardException(f"{self._identifier} Failed to reset BWT transport", reply=reply)

        return BwtTransportConfig.FromMessage(reply.pulczar.laser.bwt_config)

    async def get_board_version(self) -> Tuple[str, Optional[int]]:
        """
        Query the board for the hardware personality (board model + PCB rev)
        """
        request = self._nano_connector.create_request(hwinfo=HwInfoRequest(version=BoardVersionRequest()))
        reply = await self._nano_connector.send_request_await_reply(request)

        assert reply.WhichOneof("reply") == "hwinfo"
        assert reply.hwinfo.WhichOneof("reply") == "version"

        hwrev: Optional[int] = None
        if reply.hwinfo.version.rev != 0xFFFFFFFF:
            hwrev = reply.hwinfo.version.rev

        return (reply.hwinfo.version.model, hwrev)

    # Hardware status
    async def get_hwstatus_slayer(self) -> SlayerScannerHwStatus:
        """
        Retrieve the latest Slayer scanner hardware status information. This is a composite of
        various pieces of information, including data polled from the motor controllers.
        """
        if not is_slayer():
            raise PulczarBoardException("Unsupported generation")

        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(hw_status=pulczar_pb.HwStatus_Request())
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "hw_status"
            or reply.pulczar.hw_status.WhichOneof("reply") != "slayer"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to decode Slayer hardware status reply", reply=reply
            )

        return SlayerScannerHwStatus.FromMessage(reply.pulczar.hw_status.slayer)

    async def get_hwstatus_reaper(self) -> ReaperScannerHwStatus:
        """
        Retrieve the latest Reaper scanner hardware status information. This is a composite of
        various pieces of information, including data polled from the motor controllers.
        """
        if not is_reaper():
            raise PulczarBoardException("Unsupported generation")

        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(hw_status=pulczar_pb.HwStatus_Request())
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "pulczar"
            or reply.pulczar.WhichOneof("reply") != "hw_status"
            or reply.pulczar.hw_status.WhichOneof("reply") != "reaper"
        ):
            raise PulczarBoardException(
                f"{self._identifier} Failed to decode Reaper hardware status: reply=", reply=reply
            )

        try:
            return ReaperScannerHwStatus.FromMessage(reply.pulczar.hw_status.reaper)
        except Exception:
            LOG.exception(f"Failed to decode HwStatus_Reaper_Reply: reply={reply}")
            raise PulczarBoardException()

    async def set_drive_ambient_temp_reporting(
        self, enabled: bool, override_temp: Optional[Temperature] = None
    ) -> None:
        """
        Reconfigure the ambient temperature reporting for drives

        This can take the temperature either from scanner thermistors or from the manually
        specified temperature provided by the user.
        """
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                ambient_temp=pulczar_pb.AmbientTempRequest(
                    set_config=pulczar_pb.AmbientTempConfig(
                        enabled=enabled,
                        use_thermistor=(override_temp is None),
                        temp=override_temp.deg_c if override_temp else None,
                    )
                )
            )
        )

        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "pulczar" or reply.pulczar.WhichOneof("reply") != "ack":
            raise PulczarBoardException(
                f"{self._identifier} Failed to decode set ambient temp reply: reply=", reply=reply
            )

    async def get_drive_ambient_temp_reporting(self) -> Tuple[bool, Temperature]:
        """
        Query the board for current ambient temp reporting state.

        Returns a 2-tuple of the enabled state and the last ambient temp sent to the drives.
        """
        request = self._nano_connector.create_request(
            pulczar=pulczar_pb.Request(
                ambient_temp=pulczar_pb.AmbientTempRequest(get_config=pulczar_pb.AmbientTempGetStateRequest())
            )
        )

        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "pulczar" or reply.pulczar.WhichOneof("reply") != "ambient_temp":
            raise PulczarBoardException(f"{self._identifier} Failed to decode ambient temp status", reply=reply)

        return (reply.pulczar.ambient_temp.config.enabled, Temperature.from_c(reply.pulczar.ambient_temp.sense_temp))

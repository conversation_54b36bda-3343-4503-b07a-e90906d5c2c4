import asyncio
import time
from argparse import ArgumentParser

import numpy as np

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import (
    PULCZAR_PORT,
    PulczarBoardConnector,
    make_pulczar_board_ip,
)
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)


async def _ping_test(board: PulczarBoardConnector) -> None:
    times = np.array([await board.ping() for x in range(1000)])
    LOG.info(
        f"Nano Ping: mean: {times.mean()}, 95th %tile: {np.percentile(times, 95)}, 99th %tile: {np.percentile(times, 99)}"
    )


async def _timed_get(board: PulczarBoardConnector) -> float:
    start_time = time.time()
    await board.gimbal_get_pos_vel()
    return time.time() - start_time


async def _servo_ping_test(board: PulczarBoardConnector) -> None:
    times = np.array([await _timed_get(board) for x in range(1000)])
    LOG.info(
        f"Get Pos Vel: mean: {times.mean()}, 95th %tile: {np.percentile(times, 95)}, 99th %tile: {np.percentile(times, 99)}"
    )


async def _get_barcode_test(board: PulczarBoardConnector) -> None:
    sn = await board.get_scanner_assembly_sn()
    LOG.info(f"Scanner assembly sn: {sn}")


async def _test(scanner_id: int) -> None:
    connector = PsocMEthernetConnector(make_pulczar_board_ip(scanner_id), PULCZAR_PORT, asyncio.get_event_loop())
    await connector.open()
    board = PulczarBoardConnector(connector)
    print(await board.get_gimbal_SDO(0x2325, 0x07))
    await _ping_test(board)
    await _servo_ping_test(board)
    await _get_barcode_test(board)


def main() -> None:
    parser = ArgumentParser("Pulczar Board Tester")
    parser.add_argument("-s", type=int, default=1)
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _test(args.s)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

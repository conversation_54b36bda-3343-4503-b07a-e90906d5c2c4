import asyncio
from argparse import ArgumentParser
from typing import Optional

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time import maka_control_timestamp_ms
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import (
    PULCZAR_PORT,
    PulczarBoardConnector,
    PulczarServoPIDConfig,
    make_pulczar_board_ip,
    make_pulczar_bootloader_ip,
    servo_pb,
)
from lib.drivers.psoc_ethernet.psoc_ethernet_bootloader import program
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)


async def _pps_test(board: PulczarBoardConnector, get_only: bool, kp: int, ki: int) -> None:
    current_time = await board.get_timestamp()
    if not get_only and (maka_control_timestamp_ms() - current_time > 1000000 or True):
        t = maka_control_timestamp_ms()
        wait = 1000 - (t % 1000)
        wait += 500
        await asyncio.sleep(wait / 1000)
        await board.set_epoch_time()

    for i in range(10000000):
        before = maka_control_timestamp_ms()
        result = await board.get_time_debug()

        after = maka_control_timestamp_ms()
        avg = int((after + before) / 2)
        print(
            f"PC: {avg:18}, Board: {int(result.timestamp_us / 1000):18}, diff: {avg - (int(result.timestamp_us / 1000)):10}, pps_timer_val: {result.pps_timer_val}, pps_ticks: {result.pps_ticks}, freq_mul: {result.freq_mul:10}, err: {result.error_ticks:10}, adj: {result.error_ticks2:10}"
        )
        await asyncio.sleep(0.1)


async def _servo_test(board: PulczarBoardConnector, get_only: bool, kp: int, ki: int) -> None:
    current_time = await board.get_timestamp()
    if not get_only and (maka_control_timestamp_ms() - current_time > 1000000 or True):
        t = maka_control_timestamp_ms()
        wait = 1000 - (t % 1000)
        wait += 500
        await asyncio.sleep(wait / 1000)
        await board.set_epoch_time()

    await board.clear_config()
    await board.gimbal_config(1, 2, 590000, 1000, 25, 300)
    await board.gimbal_configure_pids(*PulczarServoPIDConfig.default_pids())
    await board.gimbal_boot(590000, 0, 30000, 1000, (1000, 1000))
    while True:
        r1 = await board.gimbal_go_to_timestamp(
            maka_control_timestamp_ms() - 100,
            mode=servo_pb.IMMEDIATE,
            position=(0, 0),
            velocity_mrpm=(590000, 590000),
            follow_velocity=(0, 1000),
            follow_accel=(0, 10000),
            interval_sleep_time_ms=30,
        )
        print(f"Positive: {r1}")
        await asyncio.sleep(1)
        r2 = await board.gimbal_go_to_timestamp(
            maka_control_timestamp_ms(),
            mode=servo_pb.IMMEDIATE,
            position=(10000, 10000),
            velocity_mrpm=(590000, 590000),
            follow_velocity=(0, -1000),
            follow_accel=(0, 0),
            interval_sleep_time_ms=30,
        )
        print(f"Negative: {r2}")
        await asyncio.sleep(1)


async def _servo_test2(board: PulczarBoardConnector, get_only: bool, kp: int, ki: int) -> None:
    await board.clear_config()
    await board.gimbal_config(1, 2, 590000, 1000, 25, 300)
    await board.gimbal_configure_pids(*PulczarServoPIDConfig.default_pids())
    await board.gimbal_boot(590000, 0, 30000, 1000, (1000, 1000))
    while True:
        await board.gimbal_go_to(
            position=(0, 0), velocity=(590000, 590000), await_settle=True,
        )
        print("Positive:")
        await asyncio.sleep(0.2)
        await board.gimbal_go_to(
            position=(10000, 10000), velocity=(590000, 590000), await_settle=True,
        )
        print("Negative:")
        await asyncio.sleep(0.2)


async def _reset_scanner(scanner_id: int) -> None:
    ip = make_pulczar_board_ip(scanner_id)
    connector = PsocMEthernetConnector(ip, PULCZAR_PORT, asyncio.get_event_loop())
    await connector.open()
    board = PulczarBoardConnector(connector)
    await board.hard_reset()


async def _flash_firmware(scanner_id: int, firmware_path: str) -> None:
    ip = make_pulczar_bootloader_ip(scanner_id)
    print(f"Flashing Firmware: {firmware_path}")
    await _reset_scanner(scanner_id)
    await asyncio.sleep(2)
    print(f"Reaching Bootloader at IP: {ip}")
    await program(ip, PULCZAR_PORT, firmware_path)
    print(f"Successfully Programmed Firmware: {firmware_path}")
    print("Rebooting Board... (May take several seconds)")
    await asyncio.sleep(7)


async def _test(scanner_id: int, firmware_path: Optional[str], target: str, get_only: bool, kp: int, ki: int) -> None:
    if firmware_path is not None:
        await _flash_firmware(scanner_id, firmware_path)
    connector = PsocMEthernetConnector(make_pulczar_board_ip(scanner_id), PULCZAR_PORT, asyncio.get_event_loop())
    await connector.open()
    board = PulczarBoardConnector(connector)
    await board.ping()
    print("Ping Successful")
    if target == "pps":
        await _pps_test(board, get_only, kp, ki)
    elif target == "servo":
        await _servo_test(board, get_only, kp, ki)
    elif target == "servo2":
        await _servo_test2(board, get_only, kp, ki)


def main() -> None:
    parser = ArgumentParser("Pulczar Board PPS Tester")
    parser.add_argument("-s", type=int, default=1, help="Scanner ID")
    parser.add_argument("-f", type=str, default=None, help="Firmware File Path")
    parser.add_argument("-t", type=str, default="pps", help="[pps, servo, servo2]")
    parser.add_argument("-g", action="store_true", default=False, help="Get Only")
    parser.add_argument("-p", type=int, default=900, help="P term for the timing PID")
    parser.add_argument("-i", type=int, default=900, help="I term for the timing PID")
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _test(args.s, args.f, args.t, args.g, args.p, args.i)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

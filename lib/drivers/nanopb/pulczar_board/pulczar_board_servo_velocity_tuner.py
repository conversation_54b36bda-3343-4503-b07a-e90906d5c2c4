import asyncio
import sys
import time
from argparse import Argument<PERSON><PERSON><PERSON>
from logging import exception
from typing import <PERSON><PERSON>, <PERSON><PERSON>, cast

import numpy as np

import lib.common.logging
from firmware.release.firmware_release_manager import FirmwareVersion, get_latest_firmware_version
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import (
    PULCZAR_PORT,
    PulczarBoardConnector,
    PulczarServoPIDConfig,
    make_pulczar_board_ip,
)
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)


VELOCITY_MRPM = 590000
MIN_VELOCITY_MRPM = 1000

PAN_PID_CFG = PulczarServoPIDConfig(1540716, 5019039, 6949129, 722559299, 21026, 1, 113)
TILT_PID_CFG = PulczarServoPIDConfig(1540716, 5019039, 7171310, 600729444, 23294, 66, 27)


# Disable Bare Except Restriction
# flake8: noqa: E722


async def _calibrate(
    board: PulczarBoardConnector,
    position: int,
    velocity: int,
    window: int,
    time_window_ms: int,
    timeout_ms: int,
    period_ms: int,
) -> Tuple[float, float]:
    await board.gimbal_go_to_calibrate(0, VELOCITY_MRPM, window, time_window_ms, timeout_ms, period_ms)
    pan1, tilt1 = await board.gimbal_go_to_calibrate(position, velocity, window, time_window_ms, timeout_ms, period_ms)
    pan2, tilt2 = await board.gimbal_go_to_calibrate(0, velocity, window, time_window_ms, timeout_ms, period_ms)
    return (pan1 + pan2) / 2, (tilt1 + tilt2) / 2


def log(data: str, f: TextIO) -> None:
    print(data)
    f.write(f"{data}\n")


async def _tune_servos(
    board: PulczarBoardConnector,
    scanner_id: int,
    window: int,
    time_window_ms: int,
    timeout_ms: int,
    period_ms: int,
    n_samples: int,
    n_loops: int,
) -> bool:
    print("Setting Up Defaults Pids...")
    await board.clear_config()
    await board.gimbal_config(1, 2, 590000, 1000, 25, 300)
    await board.gimbal_configure_pids(PAN_PID_CFG, TILT_PID_CFG)
    await board.gimbal_boot(VELOCITY_MRPM, 0, 30000, 1000, (1000, 1000))
    print("Gimbal Booted and Ready")

    limit_pan, limit_tilt = await board.gimbal_get_limits()
    print(f"Limits: {(limit_pan, limit_tilt)}")

    assert 11000 > limit_pan[1] > 10000 and 11000 > limit_tilt[1] > 10000

    tick_list = [100, 50, 25, 10, 5]
    speed_samples = 10

    start_time = time.time()

    time_str = time.strftime("%Y_%m_%d_%H_%M_%S")
    with open(f"servo_velocity_tuning_s{scanner_id}_{time_str}", "w") as f:
        f.write(
            f"Calibrate Parameters: Window: {window}, Time Window Ms: {time_window_ms}, Timeout: {timeout_ms}, Period Ms: {period_ms}\n"
        )
        f.write(f"Tick List: {tick_list}\n")
        for ticks in tick_list:
            best_settling_times = [1000.0, 1000.0]
            best_velocities = [0, 0]
            local_window = min(window, ticks - 1)
            for axis in range(2):
                min_speed = MIN_VELOCITY_MRPM
                # max_speed = VELOCITY_MRPM
                max_speed = 100000
                for l in range(n_loops):
                    settle_times = []
                    # speeds = list(np.linspace(min_speed, max_speed, speed_samples).round().astype(int))
                    speeds = list(range(min_speed, max_speed, 1000))
                    for speed in speeds:
                        settle_time = []
                        print(f"collecting samples for velocity {speed}: ", end="")
                        for _ in range(n_samples):
                            try:
                                times = await _calibrate(
                                    board, ticks, speed, local_window, time_window_ms, timeout_ms, period_ms
                                )
                                settle_time.append(times[axis])
                            except:
                                pass
                            print(".", end="")
                            sys.stdout.flush()
                        print()
                        settle_times.append(cast(float, np.mean(settle_time)))
                        log(f"ticks: {ticks}, velocity: {speed}, time: {settle_times[-1]}", f)
                    print(f"Settling times = {settle_times}")
                    min_index = int(np.argmin(settle_times))
                    log(
                        f"loop {l + 1}/{n_loops}: tick size: {ticks}, axis: {axis}, optimal velocity: {speeds[min_index]}, best settling time: {settle_times[min_index]} velocity range: [{min_speed} - {max_speed}]",
                        f,
                    )
                    if min_index == 0:
                        min_speed = speeds[min_index]
                        max_speed = speeds[min_index + 1]
                    elif min_index == len(speeds) - 1:
                        min_speed = speeds[min_index - 1]
                        max_speed = speeds[min_index]
                    elif settle_times[min_index - 1] < settle_times[min_index + 1]:
                        min_speed = speeds[min_index - 1]
                        max_speed = speeds[min_index]
                    else:
                        min_speed = speeds[min_index]
                        max_speed = speeds[min_index + 1]
                    if settle_times[min_index] < best_settling_times[axis]:
                        best_settling_times[axis] = settle_times[min_index]
                        best_velocities[axis] = speeds[min_index]
            log(
                f"tick size: {ticks} pan optimal velocity: {best_velocities[0]}, tilt optimal velocity: {best_velocities[1]}, pan min settle time: {best_settling_times[0]}, tilt best settle time: {best_settling_times[1]}",
                f,
            )
    duration = time.time() - start_time
    print(f"Total Tuning Duration: {duration} seconds")

    return True


async def _tune(
    scanner_id: int, window: int, time_window_ms: int, timeout_ms: int, period_ms: int, n_samples: int, n_loops: int
) -> None:
    ip = make_pulczar_board_ip(scanner_id)
    connector = PsocMEthernetConnector(ip, PULCZAR_PORT, asyncio.get_event_loop())
    await connector.open()
    print(f"Ethernet Connector Successfully Opened")
    board = PulczarBoardConnector(connector)
    await _tune_servos(
        board,
        scanner_id,
        window=window,
        time_window_ms=time_window_ms,
        timeout_ms=timeout_ms,
        period_ms=period_ms,
        n_samples=n_samples,
        n_loops=n_loops,
    )


def main() -> None:
    parser = ArgumentParser("Pulczar Board Servo Tuner")
    parser.add_argument("-s", "--scanner", type=int, help="Scanner ID (Default: 1)", default=1)
    parser.add_argument("-w", "--window", type=int, help="Tick Settle Window (Default: 25)", default=25)
    parser.add_argument("-t", "--time-window", type=int, help="Time ms Window to maintain (Default: 100)", default=100)
    parser.add_argument("--timeout", type=int, help="Timeout in ms (Default: 1000)", default=1000)
    parser.add_argument("--period", type=int, help="Polling Period in ms (Default: 3)", default=3)
    parser.add_argument("--samples", type=int, help="Number of Samples per move (Default: 10)", default=10)
    parser.add_argument("--loops", type=int, help="Number of search loops (Default: 5)", default=5)
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _tune(
        args.scanner,
        window=args.window,
        time_window_ms=args.time_window,
        timeout_ms=args.timeout,
        period_ms=args.period,
        n_samples=args.samples,
        n_loops=args.loops,
    )
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

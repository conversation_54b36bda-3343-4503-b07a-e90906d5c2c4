import asyncio
import traceback
from argparse import ArgumentParser
from typing import Optional

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.pulczar_board.pulczar_board_connector import (
    PULCZAR_PORT,
    PulczarBoardConnector,
    PulczarServoPIDConfig,
    make_pulczar_board_ip,
    make_pulczar_bootloader_ip,
)
from lib.drivers.psoc_ethernet.psoc_ethernet_bootloader import program
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)


async def _servo_test(board: PulczarBoardConnector) -> None:
    for i in range(10000):
        try:
            print(f"Boot Loop: {i}")
            await board.clear_config()
            await board.gimbal_config(1, 2, 590000, 1000, 25, 300)
            await board.gimbal_configure_pids(*PulczarServoPIDConfig.default_pids())
            await board.gimbal_boot(590000, 0, 30000, 20000, (1000, 1000))
            limits = await board.gimbal_get_limits()
            if limits[0][1] > 11000:
                print(f"Slips Slips Slips Pan {limits[0][1]}")
            if limits[1][1] > 11000:
                print(f"Slips Slips Slips Tilt {limits[1][1]}")
        except SystemExit:
            raise
        except:  # noqa
            traceback.print_exc()


async def _reset_scanner(scanner_id: int) -> None:
    ip = make_pulczar_board_ip(scanner_id)
    connector = PsocMEthernetConnector(ip, PULCZAR_PORT, asyncio.get_event_loop())
    await connector.open()
    board = PulczarBoardConnector(connector)
    await board.hard_reset()


async def _flash_firmware(scanner_id: int, firmware_path: str) -> None:
    ip = make_pulczar_bootloader_ip(scanner_id)
    print(f"Flashing Firmware: {firmware_path}")
    await _reset_scanner(scanner_id)
    await asyncio.sleep(2)
    print(f"Reaching Bootloader at IP: {ip}")
    await program(ip, PULCZAR_PORT, firmware_path)
    print(f"Successfully Programmed Firmware: {firmware_path}")
    print("Rebooting Board... (May take several seconds)")
    await asyncio.sleep(7)


async def _test(scanner_id: int, firmware_path: Optional[str]) -> None:
    if firmware_path is not None:
        await _flash_firmware(scanner_id, firmware_path)
    connector = PsocMEthernetConnector(make_pulczar_board_ip(scanner_id), PULCZAR_PORT, asyncio.get_event_loop())
    await connector.open()
    board = PulczarBoardConnector(connector)
    await board.ping()
    print("Ping Successful")
    await _servo_test(board)


def main() -> None:
    parser = ArgumentParser("Pulczar Board Servo Destroyer")
    parser.add_argument("-s", type=int, default=1, help="Scanner ID")
    parser.add_argument("-f", type=str, default=None, help="Firmware File Path")
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _test(args.s, args.f)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

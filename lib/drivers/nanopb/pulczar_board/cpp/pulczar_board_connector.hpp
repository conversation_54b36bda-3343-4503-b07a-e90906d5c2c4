#pragma once
#include "generated/lib/drivers/nanopb/proto/pulczar_board.pb.h"
#include "lib/drivers/nanopb/cpp/connector.hpp"

#include "lib/common/cpp/exceptions.h"

namespace carbon {
namespace nanopb {
namespace pulczar_board {

class PulczarBoardError : public maka_error {
public:
  explicit PulczarBoardError(const std::string &what, size_t stack_skip = 1) : maka_error(what, stack_skip + 1) {}
};

class PulczarBoardConnector {
public:
  PulczarBoardConnector(const std::string &ip);
  float ping();

private:
  nanopb::Connector<pulczar_board_Request, pulczar_board_Reply> connector_;
};
} // namespace pulczar_board
} // namespace nanopb
} // namespace carbon

#include "lib/drivers/nanopb/pulczar_board/cpp/pulczar_board_connector.hpp"
#include <chrono>

#include <spdlog/spdlog.h>

#define PORT 4243
namespace carbon {
namespace nanopb {
namespace pulczar_board {
PulczarBoardConnector::PulczarBoardConnector(const std::string &ip)
    : connector_(ip, PORT, pulczar_board_Request_fields, pulczar_board_Reply_fields) {}
float PulczarBoardConnector::ping() {
  const static int32_t payload = 42;
  auto req = connector_.get();
  req.which_request = pulczar_board_Request_ping_tag;
  req.request.ping.x = payload;
  pulczar_board_Reply reply = {};
  auto now = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch())
                 .count();
  if (!connector_.send_request_await_reply(req, &reply)) {
    throw PulczarBoardError("Failed to get response.");
  }
  if (reply.which_reply != pulczar_board_Reply_pong_tag) {
    throw PulczarBoardError("Invalid response type.");
  }
  if (reply.reply.pong.x != payload) {
    throw PulczarBoardError("Invalid response payload.");
  }
  return (float)(std::chrono::duration_cast<std::chrono::milliseconds>(
                     std::chrono::system_clock::now().time_since_epoch())
                     .count() -
                 now) /
         1000;
}
} // namespace pulczar_board
} // namespace nanopb
} // namespace carbon
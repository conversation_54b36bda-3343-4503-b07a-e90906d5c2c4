import abc

from firmware.release.firmware_release_manager import FirmwareVersion

BOOTLOADER_PORT = 4243
RESET_BOOTLOADER_WAIT_TIME_MS = 2000
RESET_WAIT_TIME_MS = 7000
PROGRAM_WAIT_TIME_MS = 3000


class BootloadableConnector(abc.ABC):
    @abc.abstractmethod
    async def hard_reset(self) -> None:
        pass

    @abc.abstractmethod
    async def get_version(self) -> FirmwareVersion:
        pass

#pragma once
#include <stdint.h>
namespace carbon {
namespace nanopb {

struct TimeDebug {
  uint64_t timestamp_us;
  int pps_timer_val;
  int pps_ticks;
  float freq_mul;
  int error_ticks;
  int error_ticks2;
  TimeDebug(uint64_t _timestamp_us, int _pps_timer_val, int _pps_ticks, float _freq_mul, int _error_ticks,
            int _error_ticks2)
      : timestamp_us(_timestamp_us), pps_timer_val(_pps_timer_val), pps_ticks(_pps_ticks), freq_mul(_freq_mul),
        error_ticks(_error_ticks), error_ticks2(_error_ticks2) {}
};
} // namespace nanopb
} // namespace carbon
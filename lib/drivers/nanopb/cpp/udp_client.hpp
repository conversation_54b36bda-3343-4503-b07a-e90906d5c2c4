#pragma once

#include <netinet/in.h>
#include <string>

namespace carbon {
namespace nanopb {
class UDP_Client {
public:
  UDP_Client(const std::string &ip, uint16_t port, size_t size, uint32_t timeout_ms = 1000);
  ~UDP_Client();
  uint8_t *buffer() { return buffer_; }
  size_t buf_size() { return size_; }
  bool send_buff(size_t count);
  int recv_buff();

private:
  int sockfd_;
  size_t size_;
  uint8_t *buffer_;
  struct sockaddr_in servaddr_;
  bool connected_;
};
} // namespace nanopb
} // namespace carbon

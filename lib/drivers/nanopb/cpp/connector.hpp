#pragma once
#include "lib/drivers/nanopb/cpp/pb_decode.h"
#include "lib/drivers/nanopb/cpp/pb_encode.h"
#include "lib/drivers/nanopb/cpp/udp_client.hpp"
#include "spdlog/spdlog.h"
#include <atomic>
#include <string>
namespace carbon {
namespace nanopb {
template <typename Req, typename Resp>
class Connector {
public:
  Connector(const std::string &ip, uint16_t port, const pb_msgdesc_t *req_type, const pb_msgdesc_t *resp_type);
  void send_request(const Req &req);
  bool send_request_await_reply(const Req &req, Resp *resp, uint32_t timeout_ms = 1000);
  Req get();
  uint16_t get_id();
  std::string ip() { return ip_; }

private:
  void _send_request(const Req &req, UDP_Client &client);
  const std::string ip_;
  const uint16_t port_;
  const pb_msgdesc_t *req_type_;
  const pb_msgdesc_t *resp_type_;
  std::atomic<uint16_t> id_;
};
template <typename Req, typename Resp>
Connector<Req, Resp>::Connector(const std::string &ip, uint16_t port, const pb_msgdesc_t *req_type,
                                const pb_msgdesc_t *resp_type)
    : ip_(ip), port_(port), req_type_(req_type), resp_type_(resp_type), id_(0) {}

template <typename Req, typename Resp>
void Connector<Req, Resp>::_send_request(const Req &req, UDP_Client &client) {
  pb_ostream_t stream = pb_ostream_from_buffer(client.buffer(), client.buf_size());
  bool status = pb_encode(&stream, req_type_, &req);
  if (!status) {
    // TODO handle
    return;
  }
  client.send_buff(stream.bytes_written);
}

template <typename Req, typename Resp>
void Connector<Req, Resp>::send_request(const Req &req) {
  UDP_Client client(ip_, port_, (sizeof(Req) > sizeof(Resp) ? sizeof(Req) : sizeof(Resp)) * 2);
  return _send_request(req, client);
}

template <typename Req, typename Resp>
bool Connector<Req, Resp>::send_request_await_reply(const Req &req, Resp *resp, uint32_t timeout_ms) {
  UDP_Client client(ip_, port_, (sizeof(Req) > sizeof(Resp) ? sizeof(Req) : sizeof(Resp)) * 2, timeout_ms);
  _send_request(req, client);
  int received = client.recv_buff();
  if (received <= 0) {
    return false;
  }
  pb_istream_t stream = pb_istream_from_buffer(client.buffer(), received);
  bool status = pb_decode(&stream, resp_type_, resp);
  return status;
}
template <typename Req, typename Resp>
Req Connector<Req, Resp>::get() {
  Req r = {};
  r.has_header = true;
  r.header.requestId = ++id_;
  return r;
}
template <typename Req, typename Resp>
uint16_t Connector<Req, Resp>::get_id() {
  return ++id_;
}
} // namespace nanopb
} // namespace carbon
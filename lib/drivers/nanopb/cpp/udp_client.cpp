#include "lib/drivers/nanopb/cpp/udp_client.hpp"
#include "lib/drivers/nanopb/cpp/pb_decode.h"
#include "lib/drivers/nanopb/cpp/pb_encode.h"
#include <arpa/inet.h>
#include <cerrno>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>

#include <spdlog/spdlog.h>

namespace carbon {
namespace nanopb {
UDP_Client::UDP_Client(const std::string &ip, uint16_t port, size_t size, uint32_t timeout_ms) : size_(size) {
  buffer_ = new uint8_t[size];
  sockfd_ = socket(AF_INET, SOCK_DGRAM, 0);
  struct timeval tv;
  tv.tv_sec = (timeout_ms / 1000);
  tv.tv_usec = ((timeout_ms % 1000) * 1000);
  setsockopt(sockfd_, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(struct timeval));
  memset(&servaddr_, 0, sizeof(servaddr_));
  servaddr_.sin_family = AF_INET;
  servaddr_.sin_addr.s_addr = inet_addr(ip.c_str());
  servaddr_.sin_port = htons(port);
  connected_ = connect(sockfd_, (struct sockaddr *)&servaddr_, sizeof(servaddr_)) == 0;
}
UDP_Client::~UDP_Client() {
  delete[] buffer_;
  close(sockfd_);
}
bool UDP_Client::send_buff(size_t count) {
  auto resp = send(sockfd_, buffer_, count, 0);
  for (size_t i = 0; i < count; ++i) {
  }
  return (resp >= 0 && ((size_t)resp == count));
}
int UDP_Client::recv_buff() {
  int resp = (int)read(sockfd_, buffer_, size_);
  return resp;
}
} // namespace nanopb
} // namespace carbon
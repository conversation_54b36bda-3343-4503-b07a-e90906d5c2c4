from typing import Optional

from wheel_encoder.pybind.wheel_encoder_python import WheelEncoder

class FirmwareVersion:
    major: int
    minor: int
    rev: int

class TimeDebug:
    timestamp_us: int
    pps_timer_val: int
    pps_ticks: int
    freq_mul: float
    error_ticks: int
    error_ticks2: int

class PulczarBoardConnector:
    def __init__(self, ip: str) -> None: ...
    def ping(self) -> float: ...

class NoFXRotaryType:
    TICK: "NoFXRotaryType"
    QUAD: "NoFXRotaryType"
    NONE: "NoFXRotaryType"

class RotaryReply:
    usec: int
    front_left_ticks: int
    front_right_ticks: int
    back_left_ticks: int
    back_right_ticks: int

class RotarySnapshotReply:
    def first_before(self) -> RotaryReply: ...
    def first_after(self) -> RotaryReply: ...
    def last_before(self) -> RotaryReply: ...
    def last_after(self) -> RotaryReply: ...
    def first_us(self) -> int: ...
    def last_us(self) -> int: ...

class NoFXBoardConnector:
    def __init__(self, ip: str, use_broadcast: bool) -> None: ...
    def ip(self) -> str: ...
    def ping(self) -> float: ...
    def hard_reset(self) -> None: ...
    def get_version(self) -> FirmwareVersion: ...
    def rotary(self) -> RotaryReply: ...
    def rotary_snapshot(self, time_first: int, time_last: int) -> RotarySnapshotReply: ...
    def verify_history(self) -> None: ...
    def drive(self, forward: bool, backward: bool, stop: bool, duty_cycle: float) -> None: ...
    def turn(self, left: bool, right: bool, straight: bool, duty_cycle: float) -> None: ...
    def park_brake(self, onoff: bool) -> None: ...
    def park_brake_query(self) -> bool: ...
    def fuel_gauge(self) -> float: ...
    def config(self, fl: NoFXRotaryType, fr: NoFXRotaryType, bl: NoFXRotaryType, br: NoFXRotaryType) -> None: ...
    def set_epoch_time(self) -> None: ...
    def get_timestamp(self) -> int: ...
    def get_time_debug(self) -> TimeDebug: ...
    def await_next(self, timeout_ms: int = 1000) -> bool: ...
    def get_next(self, prev_time_us: int, timeout_ms: int = 1000) -> Optional[RotaryReply]: ...

class WheelEncoderNOFX(WheelEncoder):
    def __init__(
        self, connector: NoFXBoardConnector, reverse_polarity: bool, rotary_resolution: int, poll_interval_ms: int
    ) -> None: ...

class WheelEncoderNOFXSim(WheelEncoder):
    def __init__(self, reverse_polarity: bool, rotary_resolution: int, poll_interval_ms: int) -> None: ...

class NoFXBoardError(Exception): ...

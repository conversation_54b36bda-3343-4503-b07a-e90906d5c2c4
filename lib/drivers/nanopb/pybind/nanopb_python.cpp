#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "lib/drivers/nanopb/cpp/firmware_version.hpp"
#include "lib/drivers/nanopb/cpp/time_debug.hpp"
#include "lib/drivers/nanopb/nofx_board/cpp/nofx_board_connector.hpp"
#include "lib/drivers/nanopb/nofx_board/cpp/wheel_encoder.hpp"
#include "lib/drivers/nanopb/pulczar_board/cpp/pulczar_board_connector.hpp"

namespace py = pybind11;

namespace carbon {
namespace nanopb {

PYBIND11_MODULE(nanopb_python, m) {
  py::class_<FirmwareVersion, std::unique_ptr<FirmwareVersion>>(m, "FirmwareVersion")
      .def_readonly("major", &FirmwareVersion::major)
      .def_readonly("minor", &FirmwareVersion::minor)
      .def_readonly("rev", &FirmwareVersion::rev);

  py::class_<TimeDebug, std::unique_ptr<TimeDebug>>(m, "TimeDebug")
      .def_readonly("timestamp_us", &TimeDebug::timestamp_us)
      .def_readonly("pps_timer_val", &TimeDebug::pps_timer_val)
      .def_readonly("pps_ticks", &TimeDebug::pps_ticks)
      .def_readonly("freq_mul", &TimeDebug::freq_mul)
      .def_readonly("error_ticks", &TimeDebug::error_ticks)
      .def_readonly("error_ticks2", &TimeDebug::error_ticks2);

  // Pulczar

  py::class_<pulczar_board::PulczarBoardConnector, std::shared_ptr<pulczar_board::PulczarBoardConnector>>(
      m, "PulczarBoardConnector")
      .def(py::init<const std::string &>(), py::arg("ip"), py::call_guard<py::gil_scoped_release>())
      .def("ping", &pulczar_board::PulczarBoardConnector::ping, py::call_guard<py::gil_scoped_release>());

  py::register_exception<pulczar_board::PulczarBoardError>(m, "PulczarBoardError");

  // NoFX

  py::class_<nofx_board::NoFXBoardConnector, std::shared_ptr<nofx_board::NoFXBoardConnector>>(m, "NoFXBoardConnector")
      .def(py::init<const std::string &, bool>(), py::arg("ip"), py::arg("use_broadcast"),
           py::call_guard<py::gil_scoped_release>())
      .def("ip", &nofx_board::NoFXBoardConnector::ip, py::call_guard<py::gil_scoped_release>())
      .def("ping", &nofx_board::NoFXBoardConnector::ping, py::call_guard<py::gil_scoped_release>())
      .def("hard_reset", &nofx_board::NoFXBoardConnector::hard_reset, py::call_guard<py::gil_scoped_release>())
      .def("get_version", &nofx_board::NoFXBoardConnector::get_version, py::call_guard<py::gil_scoped_release>())
      .def("rotary", &nofx_board::NoFXBoardConnector::rotary, py::call_guard<py::gil_scoped_release>())
      .def("rotary_snapshot", &nofx_board::NoFXBoardConnector::rotary_snapshot, py::arg("time_first"),
           py::arg("time_last"), py::call_guard<py::gil_scoped_release>())
      .def("verify_history", &nofx_board::NoFXBoardConnector::verify_history, py::call_guard<py::gil_scoped_release>())
      .def("drive", &nofx_board::NoFXBoardConnector::drive, py::arg("forward"), py::arg("backward"), py::arg("stop"),
           py::arg("duty_cycle"), py::call_guard<py::gil_scoped_release>())
      .def("turn", &nofx_board::NoFXBoardConnector::turn, py::arg("left"), py::arg("right"), py::arg("straight"),
           py::arg("duty_cycle"), py::call_guard<py::gil_scoped_release>())
      .def("park_brake", &nofx_board::NoFXBoardConnector::park_brake, py::arg("onoff"),
           py::call_guard<py::gil_scoped_release>())
      .def("park_brake_query", &nofx_board::NoFXBoardConnector::park_brake_query,
           py::call_guard<py::gil_scoped_release>())
      .def("fuel_gauge", &nofx_board::NoFXBoardConnector::fuel_gauge, py::call_guard<py::gil_scoped_release>())
      .def("config", &nofx_board::NoFXBoardConnector::config, py::arg("fl"), py::arg("fr"), py::arg("bl"),
           py::arg("br"), py::call_guard<py::gil_scoped_release>())
      .def("set_epoch_time", &nofx_board::NoFXBoardConnector::set_epoch_time, py::call_guard<py::gil_scoped_release>())
      .def("get_timestamp", &nofx_board::NoFXBoardConnector::get_timestamp, py::call_guard<py::gil_scoped_release>())
      .def("get_time_debug", &nofx_board::NoFXBoardConnector::get_time_debug, py::call_guard<py::gil_scoped_release>())
      .def("await_next", &nofx_board::NoFXBoardConnector::await_next, py::arg("timeout_ms") = 1000,
           py::call_guard<py::gil_scoped_release>())
      .def("get_next", &nofx_board::NoFXBoardConnector::get_next, py::arg("prev_time_us"), py::arg("timeout_ms") = 1000,
           py::call_guard<py::gil_scoped_release>());

  py::register_exception<nofx_board::NoFXBoardError>(m, "NoFXBoardError");

  py::class_<fastbin_Rotary_Reply, std::unique_ptr<fastbin_Rotary_Reply>>(m, "RotaryReply")
      .def_readonly("usec", &fastbin_Rotary_Reply::usec)
      .def_readonly("front_left_ticks", &fastbin_Rotary_Reply::front_left_ticks)
      .def_readonly("front_right_ticks", &fastbin_Rotary_Reply::front_right_ticks)
      .def_readonly("back_left_ticks", &fastbin_Rotary_Reply::back_left_ticks)
      .def_readonly("back_right_ticks", &fastbin_Rotary_Reply::back_right_ticks);
  py::class_<fastbin_Rotary_Snapshot_Reply, std::unique_ptr<fastbin_Rotary_Snapshot_Reply>>(m, "RotarySnapshotReply")
      .def(
          "first_before",
          [](const fastbin_Rotary_Snapshot_Reply &rep) {
            return std::make_unique<fastbin_Rotary_Reply>(rep.first_before);
          },
          py::call_guard<py::gil_scoped_release>())
      .def(
          "first_after",
          [](const fastbin_Rotary_Snapshot_Reply &rep) {
            return std::make_unique<fastbin_Rotary_Reply>(rep.first_after);
          },
          py::call_guard<py::gil_scoped_release>())
      .def(
          "last_before",
          [](const fastbin_Rotary_Snapshot_Reply &rep) {
            return std::make_unique<fastbin_Rotary_Reply>(rep.last_before);
          },
          py::call_guard<py::gil_scoped_release>())
      .def("last_after",
           [](const fastbin_Rotary_Snapshot_Reply &rep) {
             return std::make_unique<fastbin_Rotary_Reply>(rep.last_after);
           })
      .def("first_us", [](const fastbin_Rotary_Snapshot_Reply &rep) { return rep.request.first_us; })
      .def(
          "last_us", [](const fastbin_Rotary_Snapshot_Reply &rep) { return rep.request.last_us; },
          py::call_guard<py::gil_scoped_release>());

  py::enum_<nofx_board::NoFXRotaryType>(m, "NoFXRotaryType")
      .value("TICK", nofx_board::NOFX_ROTARY_TYPE_TICK)
      .value("QUAD", nofx_board::NOFX_ROTARY_TYPE_QUAD)
      .value("NONE", nofx_board::NOFX_ROTARY_TYPE_NONE)
      .export_values();

  py::object wheel_encoder =
      (py::object)py::module_::import("wheel_encoder.pybind.wheel_encoder_python").attr("WheelEncoder");
  py::class_<nofx_board::WheelEncoderNOFX, std::shared_ptr<nofx_board::WheelEncoderNOFX>>(m, "WheelEncoderNOFX",
                                                                                          wheel_encoder)
      .def(py::init<std::shared_ptr<nofx_board::NoFXBoardConnector>, bool, uint32_t, uint32_t>(), py::arg("connector"),
           py::arg("reverse_polarity"), py::arg("rotary_resolution"), py::arg("poll_interval_ms"),
           py::call_guard<py::gil_scoped_release>());

  py::class_<nofx_board::WheelEncoderNOFXSim, std::shared_ptr<nofx_board::WheelEncoderNOFXSim>>(
      m, "WheelEncoderNOFXSim", wheel_encoder)
      .def(py::init<bool, uint32_t, uint32_t>(), py::arg("reverse_polarity"), py::arg("rotary_resolution"),
           py::arg("poll_interval_ms"), py::call_guard<py::gil_scoped_release>());
}

} // namespace nanopb
} // namespace carbon
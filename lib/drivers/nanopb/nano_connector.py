import asyncio
import logging
from typing import Any, Awaitable, Callable, Dict, Generic, Optional, Protocol, Type, TypeVar

from generated.lib.drivers.nanopb.proto.request_pb2 import RequestHeader
from lib.drivers.errors.error import RetryableMakaDeviceException
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

LOG = logging.getLogger(__name__)


class CarbonMsgProtocol(Protocol):
    def __init__(self, **kwargs: Any) -> None:
        ...

    @property
    def header(self) -> RequestHeader:
        ...


class RequestProtocol(CarbonMsgProtocol, Protocol):
    def SerializeToString(self) -> bytes:
        ...


class ReplyProtocol(CarbonMsgProtocol, Protocol):
    def ParseFromString(self, data: bytes) -> int:
        ...


Request = TypeVar("Request", bound=RequestProtocol)
Reply = TypeVar("Reply", bound=ReplyProtocol)

# smallest message ID to interpret as an unsolicited message
#
# note that this implementation only sends message IDs up to 0xffff
UNSOLICITED_REQUEST_ID_MIN = 0x800000


class NanoPbConnector(Generic[Request, Reply]):
    def __init__(
        self,
        protocol_connector: MakaProtocolConnector,
        req_cls: Type[Request],
        reply_cls: Type[Reply],
        default_timeout_ms: int = 1000,
    ):
        self._req_cls = req_cls
        self._reply_cls = reply_cls
        self._reply_events: Dict[int, asyncio.Event] = {}
        self._reply_data: Dict[int, Reply] = {}
        self._next_request_id = 0
        self._protocol_connector = protocol_connector
        self._read_task = asyncio.get_event_loop().create_task(self._read_loop())
        self._default_timeout = default_timeout_ms
        self._unsolicited_callback: Optional[Callable[[Reply], Awaitable[None]]] = None

    def __encoder(self, request: Request) -> bytes:
        return request.SerializeToString()

    def __decoder(self, data: bytes) -> Reply:
        reply: Reply = self._reply_cls()
        reply.ParseFromString(data)
        return reply

    def create_request(self, **kwargs: Any) -> Request:
        request: Request = self._req_cls(**kwargs)
        self.__generate_header(request.header)
        return request

    def set_unsolicited_callback(self, callback: Callable[[Reply], Awaitable[None]]) -> None:
        """
        Set an async function to be invoked when a message is received from the board which was not
        a response for a previous request
        """
        self._unsolicited_callback = callback

    async def stop(self) -> None:
        self._read_task.cancel()
        await self._read_task

    async def _read_loop(self) -> None:
        await self._protocol_connector.wait_for_online()

        while True:
            try:
                data = await self._protocol_connector.read()
                assert data
                nano_obj = self.__decoder(data)

                # unsolicited message may have no header, or header with request id above boundary
                if not nano_obj.header or nano_obj.header.requestId >= UNSOLICITED_REQUEST_ID_MIN:
                    if self._unsolicited_callback:
                        await self._unsolicited_callback(nano_obj)
                    else:
                        LOG.error(f"Got unsolicited message but no handler (msg={nano_obj})")
                # normal reply message, notify waiter
                else:
                    if nano_obj.header.requestId not in self._reply_events:
                        # It is very common to get here if we cancel while a request is in flight.
                        LOG.debug(
                            f"{self._protocol_connector.get_identifier()} NanoPbConnector received message with no caller listening for Request ID: {nano_obj.header.requestId}"
                        )
                        continue
                    self._reply_data[nano_obj.header.requestId] = nano_obj
                    self._reply_events[nano_obj.header.requestId].set()

            except asyncio.CancelledError:
                break
            except Exception:
                LOG.exception("Exception occured while reading NanoPb message")

    def __generate_header(self, header: RequestHeader) -> None:
        # Keep request ids to at most 2 bytes, should be sufficient for anticipated request load
        self._next_request_id = (self._next_request_id if self._next_request_id < 2 ** 16 - 1 else 0) + 1
        header.requestId = self._next_request_id

    def fill_header(self, request: Request) -> Request:
        self.__generate_header(request.header)
        return request

    async def _register_request(self, header: RequestHeader) -> None:
        self._reply_events[header.requestId] = asyncio.Event()

    async def _await_request(self, header: RequestHeader, timeout_ms: int = 1000) -> Reply:
        try:
            await asyncio.wait_for(self._reply_events[header.requestId].wait(), timeout=timeout_ms / 1000)
        except asyncio.TimeoutError:
            raise RetryableMakaDeviceException(
                f"{self._protocol_connector.get_identifier()} NanoPB Request Timed Out: await_request"
            ) from None
        else:
            reply = self._reply_data[header.requestId]
            del self._reply_data[header.requestId]
            return reply
        finally:
            del self._reply_events[header.requestId]

    async def send_request(self, request: Request, timeout_ms: Optional[int] = None) -> None:
        if timeout_ms is None:
            timeout_ms = self._default_timeout
        data = self.__encoder(request)
        try:
            await asyncio.wait_for(self._protocol_connector.write(data), timeout=timeout_ms / 1000)
        except asyncio.TimeoutError:
            raise RetryableMakaDeviceException(
                f"{self._protocol_connector.get_identifier()} NanoPB Request Timed Out: send_request"
            ) from None

    async def send_request_await_reply(self, request: Request, timeout_ms: Optional[int] = None) -> Reply:
        if timeout_ms is None:
            timeout_ms = self._default_timeout
        await self._register_request(request.header)
        await self.send_request(request)
        reply: Reply = await self._await_request(request.header, timeout_ms=timeout_ms)

        return reply

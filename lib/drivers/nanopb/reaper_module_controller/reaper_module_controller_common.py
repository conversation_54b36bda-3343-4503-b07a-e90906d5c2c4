import ctypes
import errno
import random
import time
from typing import Generic, Optional, Type, TypeVar

import generated.lib.drivers.nanopb.proto.diagnostic_pb2 as diagnostic_pb
import generated.lib.drivers.nanopb.proto.version_pb2 as version_pb
from firmware.release.firmware_release_manager import FirmwareVersion
from generated.lib.drivers.nanopb.proto.error_pb2 import Error
from generated.lib.drivers.nanopb.proto.hwinfo_pb2 import BoardIdentityRequest, BoardVersionRequest
from generated.lib.drivers.nanopb.proto.hwinfo_pb2 import Request as HwInfoRequest
from generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2 import (
    ConfigReply,
    ConfigRequest,
    GetModuleIdentityRequest,
    NetworkConfigRequest,
    NetworkPowerCycleRequest,
    NetworkRequest,
    OobReply,
    OobRequest,
    SetBoardIdRequest,
    SetOtpLockRequest,
    UdpReply,
    UdpRequest,
)
from lib.common.error import MakaException
from lib.drivers.nanopb.nano_connector import NanoPbConnector
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_types import (
    BoardIdentity,
    BoardVersion,
    ModuleIdentityConfig,
    NetworkState,
    PowerState,
    RelayState,
)
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

Request = TypeVar("Request", UdpRequest, OobRequest)
Reply = TypeVar("Reply", UdpReply, OobReply)


class ReaperModuleControllerError(MakaException):
    """
    Mapping of MCB error codes to human-readable errors
    """

    ERRNO_MAP = {
        # negative values correspond to errno values
        -1: "Permission denied",
        -5: "IO error",
        -12: "Out of memory",
        -16: "Device busy",
        -19: "No such device",
        -22: "Invalid argument",
        -34: "Argument out of range",
        -88: "Not implemented",
        -120: "Already in progress",
        -134: "Operation not supported",
    }

    def __init__(self, error: Error) -> None:
        self._error = error

        # the error code from MCB is a signed 32-bit integer so cast it as such
        signedErr = ctypes.c_int(error.code).value
        errDescription = self.ERRNO_MAP.get(signedErr, "Unknown")

        super().__init__(f"MCB returned error: {errDescription} ({signedErr})")

    pass


class ReaperModuleConnectorBase(Generic[Request, Reply]):
    """
    Common methods that work over both the UDP and RS232/serial interface
    """

    def __init__(self, protocol: MakaProtocolConnector, req_cls: Type[Request], reply_cls: Type[Reply]):
        self._nano_connector: NanoPbConnector[Request, Reply] = NanoPbConnector(protocol, req_cls, reply_cls)

    @property
    def connector(self) -> NanoPbConnector[Request, Reply]:
        return self._nano_connector

    @property
    def transport(self) -> MakaProtocolConnector:
        return self.connector._protocol_connector

    def _handle_error(self, message: Reply) -> None:
        """
        Raise an exception if a given reply indicates an error from the MCB
        """
        if message.WhichOneof("reply") == "error":
            raise ReaperModuleControllerError(message.error)

    async def ping(self) -> float:
        """
        Send a ping message to the device to determine if it's alive. Returns the time taken
        for a response (in seconds)
        """
        payload = random.randint(1, 255)
        request = self._nano_connector.create_request(ping=diagnostic_pb.Ping(x=payload))

        startTime = time.time()
        reply = await self._nano_connector.send_request_await_reply(request)
        duration = time.time() - startTime

        assert reply.WhichOneof("reply") == "pong"
        assert reply.pong.x == payload
        return duration

    async def get_firmware_version(self) -> Optional[FirmwareVersion]:
        """
        Query the currently running firmware version on the device
        """
        request = self._nano_connector.create_request(version=version_pb.Version_Request())
        reply = await self._nano_connector.send_request_await_reply(request)

        if reply.WhichOneof("reply") == "error":
            if ctypes.c_int(reply.error.code).value == -5:
                # ENXIO means that there's no header, e.g. running dev firmware
                return None
            else:
                raise ReaperModuleControllerError(reply.error)
        else:
            assert reply.WhichOneof("reply") == "version"
            return FirmwareVersion(reply.version.major, reply.version.minor, 0)

    async def reboot(self) -> None:
        """
        Request the device reboot (no response is generated; the connection will be broken)
        """
        request = self._nano_connector.create_request(reset=version_pb.Reset_Request())
        await self._nano_connector.send_request(request)

    def _handle_config_get(self, reply: Reply) -> Optional[ConfigReply]:
        """
        Process a config get reply; convert errors as appropriate and transform ENOENT into a
        None value instead of an exception
        """
        if reply.WhichOneof("reply") == "error":
            err = ctypes.c_int32(reply.error.code).value
            if err == -errno.ENOENT:
                return None
            else:
                raise ReaperModuleControllerError(reply.error)
        else:
            assert reply.WhichOneof("reply") == "config"

            return reply.config

    async def _unlock_config(self) -> None:
        """
        Send a request to unlock writing to config parameters
        """
        OTP_LOCK_KEY = 0x59454554
        request = self._nano_connector.create_request(
            config=ConfigRequest(otpLock=SetOtpLockRequest(lock=False, key=OTP_LOCK_KEY))
        )
        reply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "ack"

    async def get_module_identity(self) -> Optional[ModuleIdentityConfig]:
        """
        Retrieve the module identity stored in nonvolatile memory
        """
        request = self._nano_connector.create_request(config=ConfigRequest(getIdentity=GetModuleIdentityRequest()))
        reply = await self._nano_connector.send_request_await_reply(request)

        confReply = self._handle_config_get(reply)

        if confReply:
            assert confReply.WhichOneof("reply") == "identity"
            return ModuleIdentityConfig.FromMessage(reply.config.identity)
        else:
            return None

    async def set_module_identity(self, identity: ModuleIdentityConfig) -> None:
        """
        Update the physical location of the module; used by the computer to get its IP address
        information
        """
        request = self._nano_connector.create_request(config=ConfigRequest(setIdentity=identity.toMessage()))
        reply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "ack"

    async def clear_module_identity(self) -> None:
        """
        Clear the stored module ID
        """
        return await self.set_module_identity(ModuleIdentityConfig(module=0))

    async def get_network_interface_config(self) -> NetworkState:
        """
        Retrieve the current configuration (e.g. assigned addresses, link state) for the MCB uplink
        interface. This is similar to `ifconfig(8)`
        """
        request = self._nano_connector.create_request(network=NetworkRequest(config=NetworkConfigRequest()))
        reply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "network"
        assert reply.network.WhichOneof("reply") == "config"

        return NetworkState.FromMessage(reply.network.config)

    async def network_power_cycle(self) -> None:
        """
        Power cycle the Ethernet switch; this can be used to recover the network if it gets
        broken under some circumstances.

        NOTE: When called via UDP, there is a chance the reply packet does not reach the client; so
              timeout errors are not necessarily fatal. Always test if the board is reachable via
              nanopb ping to determine if it recovered.
        """
        request = self._nano_connector.create_request(network=NetworkRequest(powerCycle=NetworkPowerCycleRequest()))
        reply = await self._nano_connector.send_request_await_reply(request, timeout_ms=17500)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "ack"

    async def get_board_version(self) -> BoardVersion:
        """
        Query the board for the hardware personality (board model + PCB rev)
        """
        request = self._nano_connector.create_request(hwinfo=HwInfoRequest(version=BoardVersionRequest()))
        reply = await self._nano_connector.send_request_await_reply(request)

        if reply.WhichOneof("reply") == "error":
            raise ReaperModuleControllerError(reply.error)
        else:
            assert reply.WhichOneof("reply") == "hwinfo"
            assert reply.hwinfo.WhichOneof("reply") == "version"

            return BoardVersion.FromMessage(reply.hwinfo.version)

    async def get_board_identity(self) -> BoardIdentity:
        """
        Retrieve the board identity information (CBSN of the MCB itself, as well as the overall
        assembly serial number, if available)
        """
        request = self._nano_connector.create_request(hwinfo=HwInfoRequest(identity=BoardIdentityRequest()))
        reply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "hwinfo"
        assert reply.hwinfo.WhichOneof("reply") == "identity"

        return BoardIdentity.FromMessage(reply.hwinfo.identity)

    async def set_board_identity(self, cbsn: Optional[str] = None, assySn: Optional[str] = None) -> None:
        """
        Write board identity information to NVRAM
        """
        if cbsn is None and assySn is None:
            return

        # unloock OTP first
        await self._unlock_config()

        # then send the request to write to OTP
        msg = SetBoardIdRequest(cbsn=cbsn, assySn=assySn)

        request = self._nano_connector.create_request(config=ConfigRequest(setBoardIdentity=msg))
        reply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "ack"

    async def get_power(self) -> PowerState:
        """
        Query the current state of switchable +24V power
        """
        return await self._power_query(None)

    async def set_power(self, newState: PowerState) -> PowerState:
        """
        Update the state of externally switchable +24V power
        """
        return await self._power_query(newState)

    async def _power_query(self, newState: Optional[PowerState]) -> PowerState:
        """
        Send a request to the power control endpoint

        This can be used to query the current state (if argument is None) or update it as well; the
        actual state (after making any changes) is always reflected by the return value.
        """
        request = self._nano_connector.create_request(power=(newState or PowerState()).toMessage())
        reply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "power"
        powerReply = reply.power

        return PowerState.FromMessage(powerReply)

    async def get_relays(self) -> RelayState:
        """
        Query the current state of the power output relays
        """
        return await self._relay_query(None)

    async def set_relays(self, newState: RelayState) -> RelayState:
        """
        Update the state of the relay board
        """
        return await self._relay_query(newState)

    async def _relay_query(self, newState: Optional[RelayState]) -> RelayState:
        """
        Send a relay request

        This can be used to query the current state (pass None) or to update one or more
        relays.
        """
        request = self._nano_connector.create_request(relay=(newState or RelayState()).toMessage())
        reply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "relay"
        relayReply = reply.relay

        return RelayState.FromMessage(relayReply)

import asyncio
from argparse import ArgumentParser

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_oob_connector import get_board

LOG = lib.common.logging.get_logger(__name__)


# run tests
async def _test(port: str) -> None:
    board = await get_board(port)

    print(f"Ping latency (sec): {await board.ping()}")
    version = (await board.get_firmware_version()) or "(development firmware)"
    print(f"Firmware version: {version}")
    print(f"Module ID: {await board.get_module_identity()}")
    print(f"Network config: {await board.get_network_interface_config()}")
    print(f"PCB hardware version: {await board.get_board_version()}")
    print(f"PCB identity: {await board.get_board_identity()}")


# main stuff
if __name__ == "__main__":
    parser = ArgumentParser("Reaper Module OOB Interface")
    parser.add_argument("--port", type=str)
    args = parser.parse_args()
    lib.common.logging.init_log()

    # connect board
    future = _test(args.port)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()

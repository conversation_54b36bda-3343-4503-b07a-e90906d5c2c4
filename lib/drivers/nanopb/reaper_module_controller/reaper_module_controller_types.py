from dataclasses import dataclass
from enum import IntEnum
from ipaddress import IPv4Address
from typing import List, Optional

from typing_extensions import Self

from generated.lib.drivers.nanopb.proto import reaper_module_controller_pb2, strobe_control_pb2
from generated.lib.drivers.nanopb.proto.hwinfo_pb2 import BoardIdentityReply, BoardVersionReply
from generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2 import (
    FanReply,
    FanSetRequest,
    ModuleIdentity,
    NetworkConfigReply,
    PowerReply,
    PowerRequest,
    RelayReply,
    RelayRequest,
    ScannerStatus,
    SensorReply,
    StatusReply,
    StrobeStatusReply,
    ThermostatConfig,
)
from lib.common.units.current import Current
from lib.common.units.duration import Duration
from lib.common.units.pressure import Pressure
from lib.common.units.temperature import Temperature
from lib.common.units.voltage import Voltage


@dataclass
class Point3D:
    x: float
    y: float
    z: float


@dataclass
class SensorData:
    @dataclass
    class EnviroData:
        timestamp: float
        temp: Temperature
        humidity: float
        pressure: Pressure

        @classmethod
        def FromMessage(cls, packet: SensorReply.envdata) -> Self:
            return cls(
                timestamp=packet.timestamp,
                temp=Temperature.from_c(packet.temp),
                humidity=packet.humidity,
                pressure=Pressure.from_kpa(packet.pressure),
            )

        def __repr__(self) -> str:
            return f"(time = {self.timestamp}, temp = {self.temp}, humid = {self.humidity:.1f} %RH, pressure = {self.pressure})"

    @dataclass
    class ImuData:
        timestamp: float
        accel: Point3D
        gyro: Point3D

        @classmethod
        def FromMessage(cls, packet: SensorReply.imudata) -> Self:
            return cls(
                timestamp=packet.timestamp,
                accel=Point3D(packet.accel[0], packet.accel[1], packet.accel[2]),
                gyro=Point3D(packet.gyro[0], packet.gyro[1], packet.gyro[2]),
            )

        def __repr__(self) -> str:
            return f"(time = {self.timestamp}, accel = {self.accel}, gyro = {self.gyro})"

    @dataclass
    class ThermistorData:
        timestamp: float
        temp: Temperature

        def __repr__(self) -> str:
            return f"(time = {self.timestamp}, temp = {self.temp})"

        @classmethod
        def FromMessage(cls, packet: SensorReply.thermdata) -> Self:
            return cls(timestamp=packet.timestamp, temp=Temperature.from_c(packet.temp))

    @dataclass
    class LeakData:
        timestamp: float
        active: bool

        @classmethod
        def FromMessage(cls, packet: SensorReply.leakdata) -> Self:
            return cls(timestamp=packet.timestamp, active=packet.active)

        def __repr__(self) -> str:
            return f"(time = {self.timestamp}, leaking = {self.active})"

    @dataclass
    class PressureData:
        timestamp: float
        pressure: Pressure
        temperature: Temperature

        @classmethod
        def FromMessage(cls, packet: SensorReply.pressdata) -> Self:
            return cls(
                timestamp=packet.timestamp,
                pressure=Pressure.from_kpa(packet.pressure),
                temperature=Temperature.from_c(packet.temperature),
            )

    """
    Encapsulates all sensor data as read from the device
    """

    def __init__(self, response: Optional[SensorReply] = None):
        self._environmental: List[SensorData.EnviroData] = []
        self._imu: SensorData.ImuData
        self._thermistors: List[SensorData.ThermistorData] = []
        self._leak: List[SensorData.LeakData] = []
        self._pressure: List[SensorData.PressureData] = []

        if response:
            self._environmental = list(map(lambda env: SensorData.EnviroData.FromMessage(env), response.env))
            self._imu = SensorData.ImuData.FromMessage(response.imu)
            self._thermistors = list(map(lambda env: SensorData.ThermistorData.FromMessage(env), response.therm))
            self._leak = list(map(lambda env: SensorData.LeakData.FromMessage(env), response.leak))
            self._pressure = list(map(lambda env: SensorData.PressureData.FromMessage(env), response.press))

    @property
    def environmental(self) -> List[EnviroData]:
        return self._environmental

    @property
    def imu(self) -> ImuData:
        return self._imu

    @property
    def thermistors(self) -> List[ThermistorData]:
        return self._thermistors

    @property
    def leak(self) -> List[LeakData]:
        return self._leak

    @property
    def pressure(self) -> List[PressureData]:
        return self._pressure

    def __repr__(self) -> str:
        return "\n".join(
            [
                f"environmental: {self.environmental}",
                f"imu: {self.imu}",
                f"thermistors: {self.thermistors}",
                f"leak: {self.leak}",
                f"pressure: {self.pressure}",
            ]
        )


@dataclass
class RelayState:
    """
    Contains the state of each of the relays on the system. All fields are populated in queries
    for the current state; leave fields at `None` to not update that relay.
    """

    pc: Optional[bool] = None
    laser: Optional[bool] = None
    btl: Optional[bool] = None

    @classmethod
    def FromMessage(cls, packet: RelayReply) -> Self:
        return cls(pc=packet.pc, laser=packet.laser, btl=packet.btl)

    def toMessage(self) -> RelayRequest:
        setReq = RelayRequest()
        if self.pc is not None:
            setReq.pc = self.pc
        if self.laser is not None:
            setReq.laser = self.laser
        if self.btl is not None:
            setReq.btl = self.btl

        return setReq


class ThermostatSource(IntEnum):
    """
    Temperature sources for the automatic fan control
    """

    DEFAULT = 1
    ENVIRO_INTERNAL = 2
    ENVIRO_EXTERNAL = 3


@dataclass
class FanThermostatConfig:
    """
    Configuration for the fan control thermostat
    """

    setpoint: Temperature
    hysteresis: Temperature
    source: ThermostatSource = ThermostatSource.DEFAULT

    @classmethod
    def FromMessage(cls, packet: ThermostatConfig) -> Self:
        source: ThermostatSource

        if packet.source is reaper_module_controller_pb2.ThermostatSource.ENVIRO_INTERNAL:
            source = ThermostatSource.ENVIRO_INTERNAL
        elif packet.source is reaper_module_controller_pb2.ThermostatSource.ENVIRO_EXTERNAL:
            source = ThermostatSource.ENVIRO_EXTERNAL
        else:
            source = ThermostatSource.DEFAULT

        return cls(
            setpoint=Temperature.from_c(packet.setpoint),
            hysteresis=Temperature.from_c(packet.hysteresis),
            source=source,
        )

    def toMessage(self) -> ThermostatConfig:
        msg = ThermostatConfig(setpoint=self.setpoint.deg_c, hysteresis=self.hysteresis.deg_c)
        if self.source is ThermostatSource.ENVIRO_INTERNAL:
            msg.source = reaper_module_controller_pb2.ThermostatSource.ENVIRO_INTERNAL
        elif self.source is ThermostatSource.ENVIRO_EXTERNAL:
            msg.source = reaper_module_controller_pb2.ThermostatSource.ENVIRO_EXTERNAL
        else:
            msg.source = reaper_module_controller_pb2.ThermostatSource.DEFAULT
        return msg


@dataclass
class FanState:
    """
    Contains the state of the external cooling fans
    """

    fan1: Optional[bool] = None
    fan2: Optional[bool] = None

    @classmethod
    def FromMessage(cls, packet: FanReply) -> Self:
        return cls(fan1=packet.fan1, fan2=packet.fan2)

    def toMessage(self) -> FanSetRequest:
        setReq = FanSetRequest()
        if self.fan1 is not None:
            setReq.fan1 = self.fan1
        if self.fan2 is not None:
            setReq.fan2 = self.fan2

        return setReq


@dataclass
class FanThermostatState:
    """
    Status of the fan/thermostat control

    This includes the actual state of the fans as well as the current configuration of the
    thermostat.

    On the wire this is decoded from the same message as the fan status message.
    """

    fans: FanState

    enabled: bool
    currentTemp: Optional[Temperature] = None
    currentConfig: Optional[FanThermostatConfig] = None

    @classmethod
    def FromMessage(cls, packet: FanReply) -> Self:
        if packet.HasField("thermostatConfig"):
            return cls(
                fans=FanState.FromMessage(packet),
                enabled=packet.thermostatEnabled,
                currentTemp=Temperature.from_c(packet.thermostatActual)
                if packet.thermostatActual is not None
                else None,
                currentConfig=FanThermostatConfig.FromMessage(packet.thermostatConfig),
            )
        else:
            return cls(
                fans=FanState.FromMessage(packet),
                enabled=packet.thermostatEnabled,
                currentTemp=Temperature.from_c(packet.thermostatActual)
                if packet.thermostatActual is not None
                else None,
            )


@dataclass
class ModuleIdentityConfig:
    """
    Identifies the physical location of the weeding module
    """

    module: int

    @classmethod
    def FromMessage(cls, packet: ModuleIdentity) -> Self:
        return cls(module=packet.number)

    def toMessage(self) -> ModuleIdentity:
        return ModuleIdentity(number=self.module)


@dataclass
class PowerState:
    """
    State of all switchable +24V power sources.

    When sending a request to the device, leave variable unset to not change its current power
    status.
    """

    relayBoard: Optional[bool] = None
    strobeBoard: Optional[bool] = None
    ethSwitch: Optional[bool] = None
    predictCam: Optional[bool] = None

    @classmethod
    def FromMessage(cls, packet: PowerReply) -> Self:
        return cls(
            relayBoard=packet.relayBoard,
            strobeBoard=packet.strobeBoard,
            ethSwitch=packet.ethSwitch,
            predictCam=packet.predictCam,
        )

    def toMessage(self) -> PowerRequest:
        setReq = PowerRequest()

        if self.relayBoard is not None:
            setReq.relayBoard = self.relayBoard
        if self.strobeBoard is not None:
            setReq.strobeBoard = self.strobeBoard
        if self.ethSwitch is not None:
            setReq.ethSwitch = self.ethSwitch
        if self.predictCam is not None:
            setReq.predictCam = self.predictCam

        return setReq


@dataclass
class StrobeConfig:
    """
    Configuration for the strobe exposure duration, interval, and ratio of camera trigger pulses
    generated.
    """

    # overall period
    period: Duration
    # exposure duration
    exposureDuration: Duration
    # number of target trigger pulses per predict trigger
    targetPredictRatio: int

    def toMessage(self) -> strobe_control_pb2.Request:
        return strobe_control_pb2.Request(
            exposure_us=int(self.exposureDuration.microseconds),
            period_us=int(self.period.microseconds),
            targets_per_predict_ratio=self.targetPredictRatio,
        )


@dataclass
class StrobeStatus:
    """
    Feedback and operational information from the strobe board
    """

    # Voltage of strobe capacitors
    voltage: Voltage
    # Current through strobe capacitors
    current: Current
    # Temperature on strobe board (NTC)
    temperature: Temperature

    # strobe board ready to fire asserted
    ready: bool
    # strobe enable flag set
    enabled: bool
    # whether firing/strobe signals are being generated
    firing: bool

    # Current strobe configuration
    currentConfig: Optional[StrobeConfig]

    @classmethod
    def FromMessage(cls, packet: StrobeStatusReply) -> Self:
        config: Optional[StrobeConfig] = None

        if packet.HasField("exposureUs") and packet.HasField("periodUs") and packet.HasField("targetsPerPredict"):
            config = StrobeConfig(
                period=Duration.from_microseconds(packet.periodUs),
                exposureDuration=Duration.from_microseconds(packet.exposureUs),
                targetPredictRatio=packet.targetsPerPredict,
            )

        return cls(
            voltage=Voltage.from_volts(packet.voltage),
            current=Current.from_amps(packet.current),
            temperature=Temperature.from_c(packet.temperature),
            ready=packet.ready,
            enabled=packet.enabled,
            firing=packet.firing,
            currentConfig=config,
        )


@dataclass
class ScannerState:
    """
    Status information for one of the connected scanners
    """

    power: bool
    fuseBlown: bool
    current: Current

    @classmethod
    def FromMessage(cls, packet: ScannerStatus) -> Self:
        return cls(power=packet.powerEnabled, fuseBlown=packet.fuseBlown, current=Current.from_amps(packet.current))


class NetworkAddressSource(IntEnum):
    """
    Where did an IP address come from?
    """

    UNKNOWN = 0
    MANUAL = 1
    STATIC = 2
    DHCP = 3


@dataclass
class NetworkAddress:
    """
    Information about a single IP address
    """

    # Assigned IP address for unicast traffic
    unicast: IPv4Address
    # Subnet mask associated with this address
    subnet: IPv4Address
    # Upstream gateway/router
    gateway: Optional[IPv4Address]

    # How was this address assigned?
    provenance: NetworkAddressSource

    @classmethod
    def FromMessage(cls, packet: reaper_module_controller_pb2.NetworkAddress) -> Self:
        gateway: Optional[IPv4Address] = None
        if packet.gateway is not None:
            gateway = IPv4Address(packet.gateway)

        provenance: NetworkAddressSource = NetworkAddressSource.UNKNOWN
        if packet.source == reaper_module_controller_pb2.NetworkAddressSource.MANUAL:
            provenance = NetworkAddressSource.MANUAL
        if packet.source == reaper_module_controller_pb2.NetworkAddressSource.STATIC:
            provenance = NetworkAddressSource.STATIC
        if packet.source == reaper_module_controller_pb2.NetworkAddressSource.DHCP:
            provenance = NetworkAddressSource.DHCP

        return cls(
            unicast=IPv4Address(packet.unicast),
            subnet=IPv4Address(packet.subnet),
            gateway=gateway,
            provenance=provenance,
        )


@dataclass
class NetworkState:
    """
    Encapsulates the current status of the MCB's IP network interface.
    """

    # Is the Ethernet link up?
    linkUp: bool
    # L2 (MAC) address
    macAddress: bytes
    # All addresses assigned to the interface
    addresses: List[NetworkAddress]

    @classmethod
    def FromMessage(cls, packet: NetworkConfigReply) -> Self:
        return cls(
            linkUp=packet.linkUp,
            macAddress=packet.mac,
            addresses=list(map(lambda addr: NetworkAddress.FromMessage(addr), packet.addresses)),
        )

    def __repr__(self) -> str:
        return f"NetworkState(linkUp={self.linkUp}, mac={self.macAddress.hex(':')}, addresses={self.addresses})"


@dataclass
class BoardVersion:
    """
    Info about the board revision (model identifier and HW rev)
    """

    # Board model (devbice tree `compatible` property)
    model: str
    # Hardware revision
    rev: int

    @classmethod
    def FromMessage(cls, packet: BoardVersionReply) -> Self:
        return cls(model=packet.model, rev=packet.rev)

    def __repr__(self) -> str:
        return f"BoardVersion(model='{self.model}', rev={self.rev})"


@dataclass
class BoardIdentity:
    """
    Board identity (serial numbers) information
    """

    # Board's serial number (CBSN)
    cbsn: str
    # Assembly serial number (for the weeding module)
    assemblySn: Optional[str]

    @classmethod
    def FromMessage(cls, packet: BoardIdentityReply) -> Self:
        return cls(cbsn=packet.cbsn, assemblySn=packet.assySn)

    def __repr__(self) -> str:
        assySnStr = "(null)" if self.assemblySn is None else f"'{self.assemblySn}'"
        return f"BoardIdentity(cbsn='{self.cbsn}', assemblySn={assySnStr})"


@dataclass
class McbStatus:
    """
    Status information from all subsystems of the MCB. Used to quickly poll the status
    """

    sensors: SensorData

    relays: RelayState
    power: PowerState

    strobe: StrobeStatus

    scanners: List[ScannerState]

    @classmethod
    def FromMessage(cls, packet: StatusReply) -> Self:
        return cls(
            sensors=SensorData(packet.sensors),
            relays=RelayState.FromMessage(packet.relays),
            power=PowerState.FromMessage(packet.power),
            strobe=StrobeStatus.FromMessage(packet.strobe),
            scanners=[ScannerState.FromMessage(x) for x in packet.scanners],
        )

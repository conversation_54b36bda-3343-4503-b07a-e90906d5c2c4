import asyncio
from typing import Awaitable, Callable, Optional

import lib.common.logging
from generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2 import CoreDumpReply, OobReply, OobRequest
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_common import ReaperModuleConnectorBase
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector
from lib.drivers.serial.serial_connector import SerialConnector

LOG = lib.common.logging.get_logger(__name__)

"""
Baud rate used for the RS232 out-of-band connection to the MCB
"""
REAPER_MODULE_CONTROLLER_BAUD = 9600

"""
Baud rate used during core dumps
"""
REAPER_MODULE_CONTROLLER_COREDUMP_BAUD = 115200

COREDUMP_CALLBACK_TYPE = Callable[[CoreDumpReply], Awaitable[None]]


class ReaperModuleOobConnector(ReaperModuleConnectorBase[OobRequest, OobReply]):
    """
    Board connector for use with the out-of-band (serial) interface
    """

    def __init__(self, protoConnector: MakaProtocolConnector):
        super().__init__(protoConnector, OobRequest, OobReply)

        self._coredump_callback: Optional[COREDUMP_CALLBACK_TYPE] = None

        self._nano_connector.set_unsolicited_callback(self._unsolicited_callback)

    def set_coredump_callback(self, callback: COREDUMP_CALLBACK_TYPE) -> None:
        self._coredump_callback = callback

    async def _unsolicited_callback(self, message: OobReply) -> None:
        if message.WhichOneof("reply") == "coredump":
            if self._coredump_callback:
                await self._coredump_callback(message.coredump)
            else:
                LOG.warning(f"Got coredump message, but no callback installed: {message.coredump}")
        else:
            LOG.warning(f"Unknown unsolicited MCB message: {message}")


async def get_board(serialPort: str, baudrate: int = REAPER_MODULE_CONTROLLER_BAUD) -> ReaperModuleOobConnector:
    """
    Create a MCB connector that uses the serial port (out-of-band) interface to
    communicate. Only a subset of commands are available via this.
    """
    connector = SerialConnector(serialPort, asyncio.get_event_loop(), baudrate=baudrate)
    await connector.open()
    board = ReaperModuleOobConnector(connector)
    return board

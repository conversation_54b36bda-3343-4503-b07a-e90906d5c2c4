import asyncio
from ipaddress import IPv4Address
from typing import List, Optional, Union

import lib.common.logging
from firmware.release.firmware_release_manager import FirmwareVersion
from generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2 import (
    FanReply,
    FanRequest,
    FanThermostatConfigRequest,
    ScannerGetStatusRequest,
    ScannerRequest,
    ScannerResetOcpRequest,
    ScannerSetPowerRequest,
    SensorRequest,
    SetStrobeStateRequest,
    StatusRequest,
    StrobeRequest,
    StrobeStatusRequest,
    TimedStrobeDisableRequest,
    UdpReply,
    UdpRequest,
)
from lib.common.units.duration import Duration
from lib.drivers.nanopb.bootloadable_connector import BootloadableConnector
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_common import ReaperModuleConnectorBase
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_types import (
    FanState,
    FanThermostatConfig,
    <PERSON><PERSON>hermostatState,
    McbStatus,
    ScannerState,
    SensorData,
    StrobeConfig,
    StrobeStatus,
)
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

LOG = lib.common.logging.get_logger(__name__)

"""
UDP port on which the Reaper module controller board listens for NanoPB requests
"""
REAPER_MODULE_CONTROLLER_PORT = 4200


class ReaperModuleConnector(BootloadableConnector, ReaperModuleConnectorBase[UdpRequest, UdpReply]):
    """
    Reaper module controller connector for use with the nanopb (UDP) interface
    """

    def __init__(self, protoConnector: MakaProtocolConnector):
        super().__init__(protoConnector, UdpRequest, UdpReply)
        self._identifier = protoConnector.get_identifier()
        self._bootloader_type = protoConnector.get_bootloader_type()

    @property
    def identifier(self) -> str:
        return self._identifier

    async def hard_reset(self) -> None:
        """
        Send a request to reboot the board
        """
        await self.reboot()

    async def get_version(self) -> FirmwareVersion:
        """
        Retrieve the current firmware version installed on this board
        """
        return (await self.get_firmware_version()) or FirmwareVersion(major=0, minor=0, rev=0)

    async def get_sensors(self) -> SensorData:
        """
        Query the device for the most recently captured sensor data
        """
        request = self._nano_connector.create_request(sensor=SensorRequest())
        reply: UdpReply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "sensor"
        return SensorData(reply.sensor)

    async def get_fans(self) -> FanState:
        """
        Query the current state of the power output fans
        """
        return await self._fan_query()

    async def set_fans(self, newState: FanState) -> FanState:
        """
        Update the state of the module fans
        """
        return await self._fan_query(newState)

    async def get_fans_thermostat_state(self) -> FanThermostatState:
        """
        Retrieve the current state and configuration of the automated fan control thermostat
        """
        return await self._fan_thermo_query()

    async def set_fans_thermostat_enabled(self, isEnabled: bool) -> FanThermostatState:
        """
        Enable or disable the automatic thermostat control
        """
        return await self._fan_thermo_query(isEnabled=isEnabled)

    async def set_fans_thermostat_config(self, config: FanThermostatConfig) -> FanThermostatState:
        """
        Change the operating parameters of the fan control thermostat
        """
        return await self._fan_thermo_query(newConfig=config)

    async def _fan_query(self, newState: Optional[FanState] = None) -> FanState:
        """
        Send a fan request

        This can be used to query the current state (pass None) or to update one or more
        fans.
        """
        reply = await self._fan_query_shared(FanRequest(set=(newState or FanState()).toMessage()))
        return FanState.FromMessage(reply)

    async def _fan_thermo_query(
        self, isEnabled: Optional[bool] = None, newConfig: Optional[FanThermostatConfig] = None
    ) -> FanThermostatState:
        """
        Send thermostat request
        """
        msg: FanThermostatConfigRequest

        if newConfig is not None:
            msg = FanThermostatConfigRequest(config=newConfig.toMessage())
        else:
            msg = FanThermostatConfigRequest()

        if isEnabled is not None:
            msg.enabled = isEnabled

        reply = await self._fan_query_shared(FanRequest(thermoConfig=msg))
        return FanThermostatState.FromMessage(reply)

    async def _fan_query_shared(self, fanRequest: FanRequest) -> FanReply:
        """
        Send a fan request endpoint and wait for reply
        """
        request = self._nano_connector.create_request(fan=fanRequest)
        reply: UdpReply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "fan"
        fanReply = reply.fan

        return fanReply

    async def set_scanner_power(self, scannerA: Optional[bool] = None, scannerB: Optional[bool] = None) -> None:
        """
        Turn on or off the switchable +24V power for a scanner
        """
        request = self._nano_connector.create_request(
            scanner=ScannerRequest(power=ScannerSetPowerRequest(scannerA=scannerA, scannerB=scannerB))
        )
        reply: UdpReply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "ack"

    async def reset_scanner_ocp(self, scannerA: Optional[bool] = None, scannerB: Optional[bool] = None) -> None:
        """
        Clear the latching overcurrent detection for a scanner
        """
        request = self._nano_connector.create_request(
            scanner=ScannerRequest(ocp=ScannerResetOcpRequest(scannerA=scannerA, scannerB=scannerB))
        )
        reply: UdpReply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "ack"

    async def get_scanner_status(self) -> List[ScannerState]:
        """
        Retrieve the status (power/fuse state, and current draw) for all scanners
        """
        request = self._nano_connector.create_request(scanner=ScannerRequest(status=ScannerGetStatusRequest()))
        reply: UdpReply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "scanner"
        scannerReply = reply.scanner
        assert scannerReply.WhichOneof("reply") == "status"

        return list(map(lambda x: ScannerState.FromMessage(x), scannerReply.status.data))

    async def set_strobe_config(self, newConfig: StrobeConfig) -> None:
        """
        Update the strobe pulse configuration
        """
        request = self._nano_connector.create_request(strobe=StrobeRequest(setWaveform=newConfig.toMessage()))
        reply: UdpReply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "ack"

    async def set_strobe_enabled(self, enabled: bool) -> None:
        """
        Configure whether the BTLs are strobed and cameras are triggered
        """
        request = self._nano_connector.create_request(
            strobe=StrobeRequest(setState=SetStrobeStateRequest(enabled=enabled))
        )
        reply: UdpReply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "ack"

    async def strobe_disable_for(self, duration: Duration) -> None:
        """
        Inhibit strobing for the specified time period; at the conclusion of the time interval, the
        previous strobe state is restored.
        """
        request = self._nano_connector.create_request(
            strobe=StrobeRequest(timedDisable=TimedStrobeDisableRequest(durationMsec=int(duration.milliseconds)))
        )
        reply: UdpReply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "ack"

    async def get_strobe_status(self) -> StrobeStatus:
        """
        Get the current state of the strobe board
        """
        request = self._nano_connector.create_request(strobe=StrobeRequest(getStatus=StrobeStatusRequest()))
        reply: UdpReply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "strobe"
        assert reply.strobe.WhichOneof("reply") == "status"

        return StrobeStatus.FromMessage(reply.strobe.status)

    async def get_board_status(self) -> McbStatus:
        """
        Query the current status of all MCB subsystems.
        """
        request = self._nano_connector.create_request(status=StatusRequest())
        reply: UdpReply = await self._nano_connector.send_request_await_reply(request)

        self._handle_error(reply)
        assert reply.WhichOneof("reply") == "status"

        return McbStatus.FromMessage(reply.status)


async def get_board(address: Union[str, IPv4Address]) -> ReaperModuleConnector:
    """
    Make a connector using the UDP NanoPB interface to communicate
    """
    connector = PsocMEthernetConnector(str(address), REAPER_MODULE_CONTROLLER_PORT, asyncio.get_event_loop())
    await connector.open()

    board = ReaperModuleConnector(connector)
    return board

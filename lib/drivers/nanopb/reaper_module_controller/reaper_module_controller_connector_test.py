import asyncio
from argparse import ArgumentParser

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.units.duration import Duration
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_connector import get_board
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_types import RelayState, StrobeConfig

LOG = lib.common.logging.get_logger(__name__)


# run tests
async def _test(address: str) -> None:
    board = await get_board(address)

    print(await board.ping())
    print(await board.get_version())

    print(await board.get_sensors())
    print(await board.get_relays())
    print(await board.set_relays(RelayState(pc=True, btl=False, laser=False)))
    print(await board.get_fans())
    print(await board.get_fans_thermostat_state())

    print(await board.get_power())

    print(await board.get_network_interface_config())

    print(await board.get_module_identity())
    print(await board.get_board_version())
    print(await board.get_board_identity())

    await board.set_strobe_config(
        StrobeConfig(
            period=Duration.from_hz(40), exposureDuration=Duration.from_microseconds(500), targetPredictRatio=5
        )
    )
    await board.set_strobe_enabled(True)
    print(await board.get_strobe_status())


# main stuff
if __name__ == "__main__":
    parser = ArgumentParser("Reaper Module NanoPB (UDP) Interface Test")
    parser.add_argument("--address", type=str)
    args = parser.parse_args()
    lib.common.logging.init_log()

    # connect board
    future = _test(args.address)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()

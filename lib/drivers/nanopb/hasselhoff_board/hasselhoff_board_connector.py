import asyncio
import time
from dataclasses import dataclass
from enum import IntEnum
from typing import Optional

import generated.lib.drivers.nanopb.proto.carbon_tractor_pb2 as ct_pb
import generated.lib.drivers.nanopb.proto.ots_tractor_pb2 as ots_pb
from firmware.release.firmware_release_manager import FirmwareVersion
from generated.lib.drivers.nanopb.proto.hasselhoff_board_pb2 import Reply, Request
from lib.common.units.angle import Angle
from lib.common.units.speed import Speed
from lib.drivers.nanopb.bootloadable_connector import BootloadableConnector
from lib.drivers.nanopb.nano_connector import NanoPbConnector
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

HASSELHOFF_PORT = 4243
HASSELHOFF_IP = "**********"


class Gear(IntEnum):
    PARK = ots_pb.GEAR_PARK
    REVERSE = ots_pb.GEAR_REVERSE
    NEUTRAL = ots_pb.GEAR_NEUTRAL
    FORWARD = ots_pb.GEAR_FORWARD
    POWERZERO = ots_pb.GEAR_POWERZERO


class Lights(IntEnum):
    OFF = ots_pb.LIGHTS_OFF
    LOW = ots_pb.LIGHTS_LOW
    HIGH = ots_pb.LIGHTS_HIGH


class HHState(IntEnum):
    UNKNOWN = ct_pb.HH_UNKNOWN
    DISABLED = ct_pb.HH_DISABLED
    OPERATIONAL = ct_pb.HH_OPERATIONAL
    STOPPED = ct_pb.HH_STOPPED
    SAFE = ct_pb.HH_SAFE
    ESTOP = ct_pb.HH_ESTOP


class TractorVariantType(IntEnum):
    UNKNOWN = ots_pb.TV_UNKNOWN
    JD_6LH = ots_pb.TV_JD_6LH
    JD_6LHM = ots_pb.TV_JD_6LHM
    JD_6PRO = ots_pb.TV_JD_6PRO
    JD_7LH = ots_pb.TV_JD_7LH
    JD_7PRO = ots_pb.TV_JD_7PRO
    JD_8RH = ots_pb.TV_JD_8RH

    @staticmethod
    def from_str(variant: str) -> Optional["TractorVariantType"]:
        variant = variant.upper()
        if variant in TractorVariantType.__members__:
            return TractorVariantType.__members__[variant]
        return None


@dataclass
class WheelAngleCal:
    sensor_deg_per_bit: float
    sensor_center_val: int
    center_trim: Angle
    right_lock: Angle
    sensor_full_left_val: int
    sensor_full_right_val: int

    @classmethod
    def from_proto(cls, proto: ots_pb.WheelAngleCalState) -> "WheelAngleCal":
        return WheelAngleCal(
            sensor_deg_per_bit=proto.sensor_deg_per_bit,
            sensor_center_val=proto.sensor_center_val,
            center_trim=Angle.from_degrees(proto.center_trim_deg),
            right_lock=Angle.from_degrees(proto.right_lock_deg),
            sensor_full_left_val=proto.sensor_full_left_val,
            sensor_full_right_val=proto.sensor_full_right_val,
        )

    def to_proto(self) -> ots_pb.WheelAngleCalState:
        return ots_pb.WheelAngleCalState(
            sensor_deg_per_bit=self.sensor_deg_per_bit,
            sensor_center_val=self.sensor_center_val,
            center_trim_deg=self.center_trim.degrees,
            right_lock_deg=self.right_lock.degrees,
            sensor_full_left_val=self.sensor_full_left_val,
            sensor_full_right_val=self.sensor_full_right_val,
        )


@dataclass
class SteeringCfg:
    kp: float
    ki: float
    kd: float
    integral_limit: float
    update_rate_hz: int
    min_steering_valve_current: int
    max_steering_valve_current: int

    @classmethod
    def from_proto(cls, proto: ct_pb.SteeringCfgState) -> "SteeringCfg":
        return SteeringCfg(
            kp=proto.kp,
            ki=proto.ki,
            kd=proto.kd,
            integral_limit=proto.integral_limit,
            update_rate_hz=proto.update_rate_hz,
            min_steering_valve_current=proto.min_steering_valve_current,
            max_steering_valve_current=proto.max_steering_valve_current,
        )

    def to_proto(self) -> ct_pb.SteeringCfgState:
        return ct_pb.SteeringCfgState(
            kp=self.kp,
            ki=self.ki,
            kd=self.kd,
            integral_limit=self.integral_limit,
            update_rate_hz=self.update_rate_hz,
            min_steering_valve_current=self.min_steering_valve_current,
            max_steering_valve_current=self.max_steering_valve_current,
        )


class HasselhoffBoardConnector(BootloadableConnector):
    def __init__(self, protocol_connector: MakaProtocolConnector):
        self._nano_connector: NanoPbConnector[Request, Reply] = NanoPbConnector(protocol_connector, Request, Reply, 250)

    async def stop(self) -> None:
        await self._nano_connector.stop()

    async def ping(self) -> float:
        payload = 42
        request = self._nano_connector.create_request()
        request.ping.x = 42
        start_time = time.time()
        reply = await self._nano_connector.send_request_await_reply(request)
        duration = time.time() - start_time
        assert reply.WhichOneof("reply") == "pong"
        assert reply.pong.x == payload
        return duration

    async def hard_reset(self) -> None:
        pass

    def __is_carbon_tractor(self, reply: Reply) -> bool:
        return reply.WhichOneof("reply") == "carbon_tractor"

    def __is_ots_tractor(self, reply: Reply) -> bool:
        return reply.WhichOneof("reply") == "ots_tractor"

    def __is_carbon_set(self, reply: Reply) -> bool:
        return self.__is_carbon_tractor(reply) and reply.carbon_tractor.WhichOneof("reply") == "set"

    def __is_carbon_get(self, reply: Reply) -> bool:
        return self.__is_carbon_tractor(reply) and reply.carbon_tractor.WhichOneof("reply") == "get"

    def __is_ots_set(self, reply: Reply) -> bool:
        return self.__is_ots_tractor(reply) and reply.ots_tractor.WhichOneof("reply") == "set"

    def __is_ots_get(self, reply: Reply) -> bool:
        return self.__is_ots_tractor(reply) and reply.ots_tractor.WhichOneof("reply") == "get"

    async def set_speed(self, speed: Speed) -> None:
        request = self._nano_connector.fill_header(
            Request(
                ots_tractor=ots_pb.Request(
                    set=ots_pb.SetRequest(speed_control=ots_pb.SpeedControlState(speed=speed.mph))
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_set(reply) and reply.ots_tractor.set.WhichOneof("set") == "speed_control"

    async def raise_hitch(self) -> None:
        await self.send_hitch_value(0x0)
        await self.send_hitch_value(0x7D)

    async def lower_hitch(self) -> None:
        await self.send_hitch_value(0xFA)
        await self.send_hitch_value(0x7D)

    async def send_hitch_value(self, force: int) -> None:
        request = self._nano_connector.fill_header(
            Request(
                ots_tractor=ots_pb.Request(set=ots_pb.SetRequest(hitch=ots_pb.HitchRequest(hitch_lift_force=force)))
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_set(reply) and reply.ots_tractor.set.WhichOneof("set") == "hitch"

    async def set_scv(self, scv_id: int, force: int, cmd_time_ms: int) -> None:
        request = self._nano_connector.fill_header(
            Request(
                ots_tractor=ots_pb.Request(
                    set=ots_pb.SetRequest(scv=ots_pb.ScvRequest(scv_id=scv_id, force=force, cmd_time_ms=cmd_time_ms))
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_set(reply) and reply.ots_tractor.set.WhichOneof("set") == "scv"

    async def set_gear(self, gear: Gear) -> Gear:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(set=ots_pb.SetRequest(gear=ots_pb.GearState(gear=gear.value))))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_set(reply) and reply.ots_tractor.set.WhichOneof("set") == "gear"
        return Gear(reply.ots_tractor.set.gear.gear)

    async def set_lights(self, lights: Lights) -> Lights:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(set=ots_pb.SetRequest(lights=ots_pb.LightsState(lights=lights.value))))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_set(reply) and reply.ots_tractor.set.WhichOneof("set") == "lights"
        return Lights(reply.ots_tractor.set.lights.lights)

    async def set_hh_state(self, state: HHState) -> HHState:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(set=ct_pb.SetRequest(hh_state=ct_pb.HHState(state=state.value))))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_set(reply) and reply.carbon_tractor.set.WhichOneof("set") == "hh_state"
        return HHState(reply.carbon_tractor.set.hh_state.state)

    async def set_safety_bypass(self, bypass: bool) -> bool:
        request = self._nano_connector.fill_header(
            Request(
                carbon_tractor=ct_pb.Request(
                    set=ct_pb.SetRequest(safety_bypass=ct_pb.SafetySensorBypassState(bypass=bypass))
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_set(reply) and reply.carbon_tractor.set.WhichOneof("set") == "safety_bypass"
        return reply.carbon_tractor.set.safety_bypass.bypass

    async def set_brakes(self, left: int, right: int) -> None:
        left = min(max(left, 0), 100)
        right = min(max(right, 0), 100)
        request = self._nano_connector.fill_header(
            Request(
                carbon_tractor=ct_pb.Request(
                    set=ct_pb.SetRequest(brakes=ct_pb.BrakeState(force_left=left, force_right=right))
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_tractor(reply) and reply.carbon_tractor.set.WhichOneof("set") == "brakes"

    async def set_steering(self, angle: Angle) -> None:
        request = self._nano_connector.fill_header(
            Request(
                carbon_tractor=ct_pb.Request(set=ct_pb.SetRequest(steering=ct_pb.SteeringState(angle=angle.degrees)))
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_tractor(reply) and reply.carbon_tractor.set.WhichOneof("set") == "steering"

    async def set_engine_rpm_state(self, rpms: int) -> int:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(set=ots_pb.SetRequest(rpms=ots_pb.EngineRpmState(rpms=rpms))))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_set(reply) and reply.ots_tractor.set.WhichOneof("set") == "rpms"
        return reply.ots_tractor.set.rpms.rpms

    async def set_front_pto_state(self, enabled: bool) -> bool:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(set=ots_pb.SetRequest(front_pto=ots_pb.PtoState(enabled=enabled))))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_set(reply) and reply.ots_tractor.set.WhichOneof("set") == "front_pto"
        return reply.ots_tractor.set.front_pto.enabled

    async def set_rear_pto_state(self, enabled: bool) -> bool:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(set=ots_pb.SetRequest(rear_pto=ots_pb.PtoState(enabled=enabled))))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_set(reply) and reply.ots_tractor.set.WhichOneof("set") == "rear_pto"
        return reply.ots_tractor.set.rear_pto.enabled

    async def set_tractor_variant(self, variant: TractorVariantType) -> TractorVariantType:
        request = self._nano_connector.fill_header(
            Request(
                ots_tractor=ots_pb.Request(
                    set=ots_pb.SetRequest(variant=ots_pb.TractorVariantState(variant=variant.value))
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_set(reply) and reply.ots_tractor.set.WhichOneof("set") == "variant"
        return TractorVariantType(reply.ots_tractor.set.variant.variant)

    async def set_wheel_angle_cal(self, cal: WheelAngleCal) -> WheelAngleCal:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(set=ots_pb.SetRequest(wheel_cal=cal.to_proto())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_set(reply) and reply.ots_tractor.set.WhichOneof("set") == "wheel_cal"
        return WheelAngleCal.from_proto(reply.ots_tractor.set.wheel_cal)

    async def set_steering_cfg(self, cfg: SteeringCfg) -> SteeringCfg:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(set=ct_pb.SetRequest(steering_cfg=cfg.to_proto())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_set(reply) and reply.carbon_tractor.set.WhichOneof("set") == "steering_cfg"
        return SteeringCfg.from_proto(reply.carbon_tractor.set.steering_cfg)

    async def set_pet_lost_to_stop(self, use_stop: bool) -> bool:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(set=ct_pb.SetRequest(pet_loss=ct_pb.PetLossState(use_stop=use_stop))))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_set(reply) and reply.carbon_tractor.set.WhichOneof("set") == "pet_loss"
        return reply.carbon_tractor.set.pet_loss.use_stop

    async def set_speed_limit(self, speed_limit: Speed) -> Speed:
        request = self._nano_connector.fill_header(
            Request(
                carbon_tractor=ct_pb.Request(
                    set=ct_pb.SetRequest(speed_limit=ct_pb.SpeedLimitState(speed_limit_mph=speed_limit.mph))
                )
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_set(reply) and reply.carbon_tractor.set.WhichOneof("set") == "speed_limit"
        return Speed.from_mph(reply.carbon_tractor.set.speed_limit.speed_limit_mph)

    async def pet(self) -> ct_pb.TractorStatus:
        request = self._nano_connector.fill_header(Request(carbon_tractor=ct_pb.Request(pet=ct_pb.PetRequest())))
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_tractor(reply) and reply.carbon_tractor.WhichOneof("reply") == "pet"
        return reply.carbon_tractor.pet

    async def get_tractor_status(self) -> ct_pb.TractorStatus:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(get=ct_pb.GetRequest(status=ct_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_get(reply) and reply.carbon_tractor.get.WhichOneof("get") == "status"
        return reply.carbon_tractor.get.status

    async def get_hh_state(self) -> HHState:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(get=ct_pb.GetRequest(hh_state=ct_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_get(reply) and reply.carbon_tractor.get.WhichOneof("get") == "hh_state"
        return HHState(reply.carbon_tractor.get.hh_state.state)

    async def get_brake_state(self) -> ct_pb.BrakeState:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(get=ct_pb.GetRequest(brakes=ct_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_get(reply) and reply.carbon_tractor.get.WhichOneof("get") == "brakes"
        return reply.carbon_tractor.get.brakes

    async def get_safety_state(self) -> ct_pb.SafetySensorsState:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(get=ct_pb.GetRequest(safety=ct_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_get(reply) and reply.carbon_tractor.get.WhichOneof("get") == "safety"
        return reply.carbon_tractor.get.safety

    async def get_safety_bypass_state(self) -> bool:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(get=ct_pb.GetRequest(safety_bypass=ct_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_get(reply) and reply.carbon_tractor.get.WhichOneof("get") == "safety_bypass"
        return reply.carbon_tractor.get.safety_bypass.bypass

    async def get_steering_state(self) -> Angle:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(get=ct_pb.GetRequest(steering=ct_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_get(reply) and reply.carbon_tractor.get.WhichOneof("get") == "steering"
        return Angle.from_degrees(reply.carbon_tractor.get.steering.angle)

    async def get_gear_state(self) -> Gear:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(get=ots_pb.GetRequest(gear=ots_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_get(reply) and reply.ots_tractor.get.WhichOneof("get") == "gear"
        return Gear(reply.ots_tractor.get.gear.gear)

    async def get_lights_state(self) -> Lights:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(get=ots_pb.GetRequest(lights=ots_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_get(reply) and reply.ots_tractor.get.WhichOneof("get") == "lights"
        return Lights(reply.ots_tractor.get.lights.lights)

    async def get_speed_state(self) -> Speed:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(get=ots_pb.GetRequest(speed_control=ots_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_get(reply) and reply.ots_tractor.get.WhichOneof("get") == "speed_control"
        return Speed.from_mph(reply.ots_tractor.get.speed_control.speed)

    async def get_engine_rpm_state(self) -> int:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(get=ots_pb.GetRequest(rpms=ots_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_get(reply) and reply.ots_tractor.get.WhichOneof("get") == "rpms"
        return reply.ots_tractor.get.rpms.rpms

    async def get_front_pto_state(self) -> bool:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(get=ots_pb.GetRequest(front_pto=ots_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_get(reply) and reply.ots_tractor.get.WhichOneof("get") == "front_pto"
        return reply.ots_tractor.get.front_pto.enabled

    async def get_rear_pto_state(self) -> bool:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(get=ots_pb.GetRequest(rear_pto=ots_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_get(reply) and reply.ots_tractor.get.WhichOneof("get") == "rear_pto"
        return reply.ots_tractor.get.rear_pto.enabled

    async def get_tractor_variant(self) -> TractorVariantType:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(get=ots_pb.GetRequest(variant=ots_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_get(reply) and reply.ots_tractor.get.WhichOneof("get") == "variant"
        return TractorVariantType(reply.ots_tractor.get.variant.variant)

    async def get_wheel_angle_cal(self) -> WheelAngleCal:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(get=ots_pb.GetRequest(wheel_cal=ots_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_get(reply) and reply.ots_tractor.get.WhichOneof("get") == "wheel_cal"
        return WheelAngleCal.from_proto(reply.ots_tractor.get.wheel_cal)

    async def get_fuel_level(self) -> float:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(get=ots_pb.GetRequest(fuel_level=ots_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_ots_get(reply) and reply.ots_tractor.get.WhichOneof("get") == "fuel_level"
        return reply.ots_tractor.get.fuel_level.fuel_level

    async def get_steering_cfg(self) -> SteeringCfg:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(get=ct_pb.GetRequest(steering_cfg=ct_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_get(reply) and reply.carbon_tractor.get.WhichOneof("get") == "steering_cfg"
        return SteeringCfg.from_proto(reply.carbon_tractor.get.steering_cfg)

    async def get_pet_lost_to_stop(self) -> bool:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(get=ct_pb.GetRequest(pet_loss=ct_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_get(reply) and reply.carbon_tractor.get.WhichOneof("get") == "pet_loss"
        return reply.carbon_tractor.get.pet_loss.use_stop

    async def shutdown_tractor(self) -> None:
        request = self._nano_connector.fill_header(
            Request(ots_tractor=ots_pb.Request(set=ots_pb.SetRequest(ignition_off=ots_pb.Empty())))
        )
        await self._nano_connector.send_request(request)
        # No reply is expected as this shuts the entire system off including this client

    async def get_speed_limit(self) -> Speed:
        request = self._nano_connector.fill_header(
            Request(carbon_tractor=ct_pb.Request(get=ct_pb.GetRequest(speed_limit=ct_pb.Empty())))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        assert self.__is_carbon_get(reply) and reply.carbon_tractor.get.WhichOneof("get") == "speed_limit"
        return Speed.from_mph(reply.carbon_tractor.get.speed_limit.speed_limit_mph)

    async def get_version(self) -> FirmwareVersion:
        return FirmwareVersion(0, 0, 0)


async def __get_connector(ip: str = HASSELHOFF_IP, port: int = HASSELHOFF_PORT) -> HasselhoffBoardConnector:
    from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

    connector = PsocMEthernetConnector(ip, port, asyncio.get_event_loop())
    await connector.open()
    return HasselhoffBoardConnector(connector)


def interactive() -> None:
    from lib.common.asyncio.repl import start

    imports = {
        "get_connector": __get_connector,
    }
    start(imports)


def main() -> None:
    interactive()


if __name__ == "__main__":
    main()

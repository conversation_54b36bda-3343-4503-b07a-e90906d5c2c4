import time
from typing import Any, Tuple

import generated.lib.drivers.nanopb.proto.dawg_pb2 as dawg_pb
import generated.lib.drivers.nanopb.proto.epos_pb2 as epos_pb
import generated.lib.drivers.nanopb.proto.row_module_pb2 as row_module_pb
import generated.lib.drivers.nanopb.proto.scanner_pb2 as scanner_pb
import generated.lib.drivers.nanopb.proto.servo_pb2 as servo_pb
import lib.common.logging
from generated.lib.drivers.nanopb.proto.experiment_board_pb2 import ExperimentItem, ExperimentReply, ExperimentRequest
from lib.common.error import MakaException
from lib.drivers.nanopb.nano_connector import NanoPbConnector
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

LOG = lib.common.logging.get_logger(__name__)


class ExperimentBoardException(MakaException):
    pass


class ExperimentBoardConnector:
    def __init__(self, protocol_connector: MakaProtocolConnector):
        self._nano_connector: NanoPbConnector[ExperimentRequest, ExperimentReply] = NanoPbConnector(
            protocol_connector, ExperimentRequest, ExperimentReply
        )

    async def stop(self) -> None:
        await self._nano_connector.stop()

    async def items(self, n_items: int) -> float:
        request = self._nano_connector.create_request()
        for i in range(n_items):
            request.items.items.append(ExperimentItem(x=i))
        start_time = time.time()
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request)
        duration = time.time() - start_time
        assert reply.WhichOneof("reply") == "result"
        assert reply.result.sum == n_items * (n_items - 1) / 2
        return duration

    async def ping(self) -> float:
        payload = 42
        request = self._nano_connector.create_request()
        request.ping.x = 42
        start_time = time.time()
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request)
        duration = time.time() - start_time
        assert reply.WhichOneof("reply") == "pong"
        assert reply.pong.x == payload
        return duration

    async def _create_scanner_request(self, scanner_id: int, **kwargs: Any) -> ExperimentRequest:
        return self._nano_connector.create_request(
            row_module=row_module_pb.Request(
                scanner=row_module_pb.Scanner_Request(scanner_id=scanner_id, request=scanner_pb.Request(**kwargs))
            )
        )

    @staticmethod
    def _is_scanner_reply(reply: ExperimentReply) -> bool:
        return bool(reply.WhichOneof("reply") == "row_module" and reply.row_module.WhichOneof("reply") == "scanner")

    """ Row Module API """

    async def hard_reset(self) -> None:
        request = self._nano_connector.create_request(
            row_module=row_module_pb.Request(reset=row_module_pb.Reset_Request())
        )
        # This will break the connection
        await self._nano_connector.send_request(request)

    async def clear_config(self) -> None:
        request = self._nano_connector.create_request(
            row_module=row_module_pb.Request(clear=row_module_pb.Clear_Config_Request())
        )
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "row_module" or reply.row_module.WhichOneof("reply") != "ack":
            raise ExperimentBoardException("Failed to Clear Configuration")

    async def scanner_boot(self, scanner_id: int) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            boot=scanner_pb.Boot_Request(
                pan_params=epos_pb.Home_Params(
                    hard_stop=epos_pb.Hard_Home_Params(step_size=1000, offset=1000),
                    max_position=20000,
                    min_position=0,
                    profile_velocity=590000,
                ),
                tilt_params=epos_pb.Home_Params(
                    hard_stop=epos_pb.Hard_Home_Params(step_size=1000, offset=1000),
                    max_position=20000,
                    min_position=0,
                    profile_velocity=590000,
                ),
            ),
        )
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request, timeout_ms=10000)
        if not self._is_scanner_reply(reply) or reply.row_module.scanner.WhichOneof("reply") != "ack":
            raise ExperimentBoardException(f"Failed to boot scanner: {scanner_id}")

    async def scanner_stop(self, scanner_id: int) -> None:
        request = await self._create_scanner_request(scanner_id=scanner_id, stop=scanner_pb.Stop_Request())
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request)
        if not self._is_scanner_reply(reply) or reply.row_module.scanner.WhichOneof("reply") != "ack":
            raise ExperimentBoardException(f"Failed to stop scanner: {scanner_id}")

    async def scanner_laser(self, scanner_id: int, on: bool) -> None:
        request = await self._create_scanner_request(scanner_id=scanner_id, laser=scanner_pb.Laser_Request(on=on))
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request)
        if not self._is_scanner_reply(reply) or reply.row_module.scanner.WhichOneof("reply") != "ack":
            raise ExperimentBoardException(f"Failed to turn laser on/off scanner: {scanner_id}")

    async def scanner_intensity(self, scanner_id: int, intensity: int) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id, intensity=scanner_pb.Intensity_Request(intensity=intensity)
        )
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request)
        if not self._is_scanner_reply(reply) or reply.row_module.scanner.WhichOneof("reply") != "ack":
            raise ExperimentBoardException(f"Failed to change intensity of scanner: {scanner_id}")

    async def scanner_config(
        self,
        scanner_id: int,
        pan_node_id: int,
        tilt_node_id: int,
        max_profile_velocity: int,
        settle_timeout: int,
        settle_window: int,
    ) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(
                    config=servo_pb.Config_Request(
                        node_id=pan_node_id,
                        config=servo_pb.Config(
                            max_profile_velocity=max_profile_velocity,
                            settle_window=settle_window,
                            settle_timeout=settle_timeout,
                        ),
                    )
                ),
                tilt=servo_pb.Request(
                    config=servo_pb.Config_Request(
                        node_id=tilt_node_id,
                        config=servo_pb.Config(
                            max_profile_velocity=max_profile_velocity,
                            settle_window=settle_window,
                            settle_timeout=settle_timeout,
                        ),
                    )
                ),
            ),
        )
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "ack"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "ack"
        ):
            raise ExperimentBoardException(f"Failed to configure scanner: {scanner_id}")

    async def scanner_go_to(
        self, scanner_id: int, position: Tuple[int, int], velocity: Tuple[int, int], await_settle: bool
    ) -> None:
        request = await self._create_scanner_request(
            scanner_id=scanner_id,
            gimbal=scanner_pb.Gimbal_Request(
                pan=servo_pb.Request(
                    go_to=servo_pb.Go_To_Request(position=position[0], velocity=velocity[0], await_settle=await_settle)
                ),
                tilt=servo_pb.Request(
                    go_to=servo_pb.Go_To_Request(position=position[1], velocity=velocity[1], await_settle=await_settle)
                ),
            ),
        )
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request)
        if (
            not self._is_scanner_reply(reply)
            or reply.row_module.scanner.WhichOneof("reply") != "gimbal"
            or reply.row_module.scanner.gimbal.pan.WhichOneof("reply") != "ack"
            or reply.row_module.scanner.gimbal.tilt.WhichOneof("reply") != "ack"
        ):
            raise ExperimentBoardException(
                f"Failed to go to scanner {scanner_id} position: {position} with velocity: {velocity}"
            )

    async def dawg_pet(self) -> None:
        request = self._nano_connector.create_request(
            row_module=row_module_pb.Request(dawg=dawg_pb.Request(pet=dawg_pb.Pet_Request()))
        )
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "row_module"
            or reply.row_module.WhichOneof("reply") != "dawg"
            or reply.row_module.dawg.WhichOneof("reply") != "ack"
        ):
            raise ExperimentBoardException("Failed to pet Dawg")

    async def dawg_arm(self, armed: bool) -> None:
        request = self._nano_connector.create_request(
            row_module=row_module_pb.Request(dawg=dawg_pb.Request(arm=dawg_pb.Arm_Request(armed=armed)))
        )
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "row_module"
            or reply.row_module.WhichOneof("reply") != "dawg"
            or reply.row_module.dawg.WhichOneof("reply") != "ack"
        ):
            raise ExperimentBoardException("Failed to arm Dawg")

    async def dawg_config(self, timeout_ms: int) -> None:
        request = self._nano_connector.create_request(
            row_module=row_module_pb.Request(dawg=dawg_pb.Request(config=dawg_pb.Config_Request(timeout_ms=timeout_ms)))
        )
        reply: ExperimentReply = await self._nano_connector.send_request_await_reply(request)
        if (
            reply.WhichOneof("reply") != "row_module"
            or reply.row_module.WhichOneof("reply") != "dawg"
            or reply.row_module.dawg.WhichOneof("reply") != "ack"
        ):
            raise ExperimentBoardException("Failed to Config Dawg")

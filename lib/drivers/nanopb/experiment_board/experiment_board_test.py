import asyncio
from argparse import ArgumentParser

import numpy as np

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.experiment_board.experiment_board_connector import ExperimentBoardConnector
from lib.drivers.psoc_usb.psoc_m_usb import PsocMUSBConnector

LOG = lib.common.logging.get_logger(__name__)


async def _ping_test(board: ExperimentBoardConnector) -> None:
    times = np.array([await board.ping() for x in range(1000)])
    LOG.info(
        f"Nano Ping: mean: {times.mean()}, 95th %tile: {np.percentile(times, 95)}, 99th %tile: {np.percentile(times, 99)}"
    )


async def _items_test(board: ExperimentBoardConnector) -> None:
    size = 50
    times = np.array([await board.items(size) for x in range(1000)])
    LOG.info(
        f"Nano {size} Items Test: mean: {times.mean()}, 95th %tile: {np.percentile(times, 95)}, 99th %tile: {np.percentile(times, 99)}"
    )


async def _scanner_test(board: ExperimentBoardConnector) -> None:
    await board.clear_config()
    await board.scanner_config(1, 1, 2, 590000, 250, 50)
    await board.dawg_config(1000)
    await board.scanner_boot(1)
    await board.scanner_laser(1, True)

    await board.dawg_pet()
    await board.dawg_arm(True)
    await board.scanner_intensity(1, 0)

    await asyncio.sleep(2)

    await board.dawg_pet()
    await board.scanner_intensity(1, 30000)

    await asyncio.sleep(0.5)

    await board.dawg_arm(False)
    await board.scanner_go_to(1, (1000, 1000), (590000, 590000), True)
    await board.scanner_laser(1, False)

    await asyncio.sleep(1)

    await board.scanner_stop(1)


async def _test(serial_port: str) -> None:
    connector = PsocMUSBConnector(serial_port, asyncio.get_event_loop())
    await connector.open()
    board = ExperimentBoardConnector(connector)
    await _ping_test(board)
    await _scanner_test(board)
    await _items_test(board)


def main() -> None:
    parser = ArgumentParser("PsocMUSB Tester")
    parser.add_argument("serial_port", type=str)
    args = parser.parse_args()
    lib.common.logging.init_log()
    future = _test(args.serial_port)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

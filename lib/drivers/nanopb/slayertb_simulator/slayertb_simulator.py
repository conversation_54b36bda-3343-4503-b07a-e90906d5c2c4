import argparse
import asyncio

from generated.lib.drivers.nanopb.proto import slayertb_simulator_pb2 as pb
from lib.drivers.carbon_serial.carbon_serial import CarbonSerialConnector
from lib.drivers.nanopb.slayertb_simulator.slayertb_simulator_connector import SlayerTBSimulatorConnector


async def SetDefaults(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    await connector.set_boolean_value(sensor=pb.LIFTED, value=False)
    await connector.set_boolean_value(sensor=pb.LEFT_ESTOP, value=False)
    await connector.set_boolean_value(sensor=pb.RIGHT_ESTOP, value=False)
    await connector.set_boolean_value(sensor=pb.CAB_ESTOP, value=False)
    await connector.set_boolean_value(sensor=pb.LASER_KEY, value=True)
    await connector.set_boolean_value(sensor=pb.LASER_INTERLOCK, value=True)
    await connector.set_boolean_value(sensor=pb.WATER_PROTECT, value=True)
    await connector.set_boolean_value(sensor=pb.TRACTOR_POWER, value=True)
    await connector.set_int_value(sensor=pb.TEMP, value=2500)
    await connector.set_int_value(sensor=pb.HUMIDITY, value=5000)
    await connector.set_int_value(sensor=pb.BATTERY_VOLTAGE, value=1300)


async def GetLifted(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.LIFTED)
    print("Lifted = " + str(reply))


async def SetLifted(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    val = args.v.upper() == "TRUE"
    await connector.set_boolean_value(sensor=pb.LIFTED, value=val)
    print(f"Set lifted status to {val}")


async def GetLeftEstop(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.LEFT_ESTOP)
    print("LeftEstop pressed = " + str(reply))


async def SetLeftEstop(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    val = args.v.upper() == "TRUE"
    await connector.set_boolean_value(sensor=pb.LEFT_ESTOP, value=val)
    print(f"Set Left Estop pressed to {val}")


async def GetRightEstop(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.RIGHT_ESTOP)
    print("RightEstop pressed = " + str(reply))


async def SetRightEstop(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    val = args.v.upper() == "TRUE"
    await connector.set_boolean_value(sensor=pb.RIGHT_ESTOP, value=val)
    print(f"Set Right Estop pressed to {val}")


async def GetCabEstop(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.CAB_ESTOP)
    print("CabEstop pressed = " + str(reply))


async def SetCabEstop(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    val = args.v.upper() == "TRUE"
    await connector.set_boolean_value(sensor=pb.CAB_ESTOP, value=val)
    print(f"Set Cab Estop pressed to {val}")


async def GetLaserKey(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.LASER_KEY)
    print("LaserKey armed = " + str(reply))


async def SetLaserKey(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    val = args.v.upper() == "TRUE"
    await connector.set_boolean_value(sensor=pb.LASER_KEY, value=val)
    print(f"Set Laser Key Armed to {val}")


async def GetLaserInterlock(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.LASER_INTERLOCK)
    print("Laser Interlock engaged = " + str(reply))


async def SetLaserInterlock(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    val = args.v.upper() == "TRUE"
    await connector.set_boolean_value(sensor=pb.LASER_INTERLOCK, value=val)
    print(f"Set Laser Interlock engaged to {val}")


async def GetWaterProtect(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.WATER_PROTECT)
    print("Water Protect sensor = " + str(reply))


async def SetWaterProtect(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    val = args.v.upper() == "TRUE"
    await connector.set_boolean_value(sensor=pb.WATER_PROTECT, value=val)
    print(f"Set Water protect sensor to {val}")


async def GetTractorPower(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.TRACTOR_POWER)
    print("Tractor Power connected = " + str(reply))


async def GetChillerSignal(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.CHILLER_RUN_SIGNAL)
    print("Chiller run signal = " + str(reply))


async def GetCabEstopSignal(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.CAB_ESTOP_SIGNAL)
    print("Cab Estop signal = " + str(reply))


async def GetBeaconLeftSignal(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.BEACON_LEFT_SIGNAL)
    print("Beacon left signal = " + str(reply))


async def GetBeaconRightSignal(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_boolean_value(sensor=pb.BEACON_RIGHT_SIGNAL)
    print("Beacon right signal = " + str(reply))


async def SetTractorPower(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    val = args.v.upper() == "TRUE"
    await connector.set_boolean_value(sensor=pb.TRACTOR_POWER, value=val)
    print(f"Set Tractor Power connected to {val}")


async def GetTemp(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_int_value(sensor=pb.TEMP)
    print("Temperature = " + str(reply / 100))


async def SetTemp(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    await connector.set_int_value(sensor=pb.TEMP, value=int(args.v * 100))
    print(f"Set Temperature to {args.v}")


async def GetHumidity(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_int_value(sensor=pb.HUMIDITY)
    print("Humidity = " + str(reply / 100))


async def SetHumidity(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    await connector.set_int_value(sensor=pb.HUMIDITY, value=int(args.v * 100))
    print(f"Set Humidity to {args.v}")


async def GetBatteryVoltage(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    reply = await connector.get_int_value(sensor=pb.BATTERY_VOLTAGE)
    print("Battery voltage = " + str(reply / 100))


async def SetBatteryVoltage(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    await connector.set_int_value(sensor=pb.BATTERY_VOLTAGE, value=int(args.v * 100))
    print(f"Set Battery Voltage to {args.v}")


async def UseTempSensor(connector: SlayerTBSimulatorConnector, args: argparse.Namespace) -> None:
    val = args.v.upper() == "TRUE"
    await connector.use_temp_sensor(on=val)
    print(f"Sent temp sensor request, on={val}")


async def main() -> None:
    parser = argparse.ArgumentParser()
    subparsers = parser.add_subparsers(dest="sensor")
    subparsers.required = True

    defaults_parser = subparsers.add_parser("defaults", help="Lifted sensor")
    defaults_subparsers = defaults_parser.add_subparsers(dest="cmd", required=True)
    subparser = defaults_subparsers.add_parser("set")
    subparser.set_defaults(func=SetDefaults)

    lifted_parser = subparsers.add_parser("lifted", help="Lifted sensor")
    lifted_subparsers = lifted_parser.add_subparsers(dest="cmd", required=True)
    subparser = lifted_subparsers.add_parser("get")
    subparser.set_defaults(func=GetLifted)
    subparser = lifted_subparsers.add_parser("set")
    subparser.add_argument("v", help="True/False for lifted/lowered")
    subparser.set_defaults(func=SetLifted)

    leftestop_parser = subparsers.add_parser("left_estop", help="Left Estop")
    leftestop_subparsers = leftestop_parser.add_subparsers(dest="cmd", required=True)
    subparser = leftestop_subparsers.add_parser("get")
    subparser.set_defaults(func=GetLeftEstop)
    subparser = leftestop_subparsers.add_parser("set")
    subparser.add_argument("v", help="True/False for Released/pressed")
    subparser.set_defaults(func=SetLeftEstop)

    rightestop_parser = subparsers.add_parser("right_estop", help="Right Estop")
    rightestop_subparsers = rightestop_parser.add_subparsers(dest="cmd", required=True)
    subparser = rightestop_subparsers.add_parser("get")
    subparser.set_defaults(func=GetRightEstop)
    subparser = rightestop_subparsers.add_parser("set")
    subparser.add_argument("v", help="True/False for Released/pressed")
    subparser.set_defaults(func=SetRightEstop)

    cabestop_parser = subparsers.add_parser("cab_estop", help="Cab Estop")
    cabestop_subparsers = cabestop_parser.add_subparsers(dest="cmd", required=True)
    subparser = cabestop_subparsers.add_parser("get")
    subparser.set_defaults(func=GetCabEstop)
    subparser = cabestop_subparsers.add_parser("set")
    subparser.add_argument("v", help="True/False for Released/pressed")
    subparser.set_defaults(func=SetCabEstop)

    laser_key_parser = subparsers.add_parser("laser_key", help="Laser Key")
    laser_key_subparsers = laser_key_parser.add_subparsers(dest="cmd", required=True)
    subparser = laser_key_subparsers.add_parser("get")
    subparser.set_defaults(func=GetLaserKey)
    subparser = laser_key_subparsers.add_parser("set")
    subparser.add_argument("v", help="True/False for Armed/disarmed")
    subparser.set_defaults(func=SetLaserKey)

    laser_interlock_parser = subparsers.add_parser("laser_interlock", help="Laser Interlock")
    laser_interlock_subparsers = laser_interlock_parser.add_subparsers(dest="cmd", required=True)
    subparser = laser_interlock_subparsers.add_parser("get")
    subparser.set_defaults(func=GetLaserInterlock)
    subparser = laser_interlock_subparsers.add_parser("set")
    subparser.add_argument("v", help="True/False for Enagaged/disengaged")
    subparser.set_defaults(func=SetLaserInterlock)

    water_protect_parser = subparsers.add_parser("water_protect", help="Water protect")
    water_protect_subparsers = water_protect_parser.add_subparsers(dest="cmd", required=True)
    subparser = water_protect_subparsers.add_parser("get")
    subparser.set_defaults(func=GetWaterProtect)
    subparser = water_protect_subparsers.add_parser("set")
    subparser.add_argument("v", help="True/False")
    subparser.set_defaults(func=SetWaterProtect)

    tractor_power_parser = subparsers.add_parser("tractor_power", help="Tractor power")
    tractor_power_subparsers = tractor_power_parser.add_subparsers(dest="cmd", required=True)
    subparser = tractor_power_subparsers.add_parser("get")
    subparser.set_defaults(func=GetTractorPower)
    subparser = tractor_power_subparsers.add_parser("set")
    subparser.add_argument("v", help="True/False")
    subparser.set_defaults(func=SetTractorPower)

    chiller_run_parser = subparsers.add_parser("chiller_run_signal", help="Chiller run signal")
    chiller_run_subparsers = chiller_run_parser.add_subparsers(dest="cmd", required=True)
    subparser = chiller_run_subparsers.add_parser("get")
    subparser.set_defaults(func=GetChillerSignal)

    cab_estop_signal_parser = subparsers.add_parser("cab_estop_signal")
    cab_estop_signal_subparsers = cab_estop_signal_parser.add_subparsers(dest="cmd", required=True)
    subparser = cab_estop_signal_subparsers.add_parser("get")
    subparser.set_defaults(func=GetCabEstopSignal)

    beacon_left_parser = subparsers.add_parser("beacon_left_signal")
    beacon_left_subparsers = beacon_left_parser.add_subparsers(dest="cmd", required=True)
    subparser = beacon_left_subparsers.add_parser("get")
    subparser.set_defaults(func=GetBeaconLeftSignal)

    beacon_right_parser = subparsers.add_parser("beacon_right_signal")
    beacon_right_subparsers = beacon_right_parser.add_subparsers(dest="cmd", required=True)
    subparser = beacon_right_subparsers.add_parser("get")
    subparser.set_defaults(func=GetBeaconRightSignal)

    temperature_parser = subparsers.add_parser("temperature", help="temperature")
    temperature_subparsers = temperature_parser.add_subparsers(dest="cmd", required=True)
    subparser = temperature_subparsers.add_parser("get")
    subparser.set_defaults(func=GetTemp)
    subparser = temperature_subparsers.add_parser("set")
    subparser.add_argument("v", type=float)
    subparser.set_defaults(func=SetTemp)

    humidity_parser = subparsers.add_parser("humidity", help="humidity")
    humidity_subparsers = humidity_parser.add_subparsers(dest="cmd", required=True)
    subparser = humidity_subparsers.add_parser("get")
    subparser.set_defaults(func=GetHumidity)
    subparser = humidity_subparsers.add_parser("set")
    subparser.add_argument("v", type=float)
    subparser.set_defaults(func=SetHumidity)

    battery_voltage_parser = subparsers.add_parser("battery_voltage", help="battery_voltage")
    battery_voltage_subparsers = battery_voltage_parser.add_subparsers(dest="cmd", required=True)
    subparser = battery_voltage_subparsers.add_parser("get")
    subparser.set_defaults(func=GetBatteryVoltage)
    subparser = battery_voltage_subparsers.add_parser("set")
    subparser.add_argument("v", type=float)
    subparser.set_defaults(func=SetBatteryVoltage)

    use_temp_sensor_parser = subparsers.add_parser("use_temp_sensor")
    use_temp_sensor_subparsers = use_temp_sensor_parser.add_subparsers(dest="cmd", required=True)
    subparser = use_temp_sensor_subparsers.add_parser("set")
    subparser.add_argument("v", type=str)
    subparser.set_defaults(func=UseTempSensor)

    try:
        args = parser.parse_args()
    except SystemExit as e:
        print(f"Invalid use of the tool: {e}")
        return

    serial_path = "/dev/serial/by-id/usb-Arduino_LLC_Arduino_MKRZERO_3F6824975055344E312E3120FF0A1A1B-if00"
    serial = CarbonSerialConnector(serial_path, asyncio.get_event_loop(), delay_ms=1000,)
    connector = SlayerTBSimulatorConnector(serial)
    await serial.open()
    await connector.ping()

    await args.func(connector, args)


# bot run -p common python -m lib.drivers.nanopb.slayertb_simulator.slayertb_simulator
if __name__ == "__main__":
    asyncio.run(main())

import time
from typing import TYPE_CHECKING

from generated.lib.drivers.nanopb.proto import slayertb_simulator_pb2 as pb
from generated.lib.drivers.nanopb.proto.slayertb_simulator_pb2 import Reply, Request
from lib.drivers.nanopb.nano_connector import NanoPbConnector
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

if TYPE_CHECKING:
    SensorType = pb.SensorValue
else:
    SensorType = pb.Sensor


class SlayerTBSimulatorConnector:
    def __init__(self, protocol_connector: MakaProtocolConnector):
        self._nano_connector: NanoPbConnector[Request, Reply] = NanoPbConnector(protocol_connector, Request, Reply)

    async def ping(self) -> float:
        payload = 42
        request = self._nano_connector.create_request()
        request.ping.x = payload
        start_time = time.time()
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        duration = time.time() - start_time
        assert reply.WhichOneof("reply") == "pong"
        assert reply.pong.x == payload
        return duration

    async def get_boolean_value(self, sensor: SensorType) -> bool:
        request = self._nano_connector.create_request(get_value=pb.GetValueRequest(sensor=sensor))
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "get_value":
            raise Exception("Bad reply: " + str(reply))
        return reply.get_value.boolean_value

    async def set_boolean_value(self, sensor: SensorType, value: bool) -> None:
        request = self._nano_connector.create_request(set_value=pb.SetValueRequest(sensor=sensor, boolean_value=value))
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "set_value":
            raise Exception("Bad reply: " + str(reply))

    async def get_int_value(self, sensor: SensorType) -> int:
        request = self._nano_connector.create_request(get_value=pb.GetValueRequest(sensor=sensor))
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "get_value":
            raise Exception("Bad reply: " + str(reply))
        return reply.get_value.int_value

    async def set_int_value(self, sensor: SensorType, value: int) -> None:
        request = self._nano_connector.create_request(set_value=pb.SetValueRequest(sensor=sensor, int_value=value))
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "set_value":
            raise Exception("Bad reply: " + str(reply))

    async def use_temp_sensor(self, on: bool) -> None:
        request = self._nano_connector.create_request(use_temp_sensor=pb.UseTempSensorRequest(on=on))
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "use_temp_sensor":
            raise Exception("Bad reply: " + str(reply))

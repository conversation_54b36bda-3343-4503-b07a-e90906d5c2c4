import asyncio

import numpy as np

import lib.common.logging
from lib.drivers.nanopb.jimbox_board.jimbox_board_connector import JIMBOX_IP, JIMBOX_PORT, JimboxBoardConnector
from lib.drivers.psoc_ethernet.psoc_m_ethernet import PsocMEthernetConnector

# from argparse import ArgumentParser


LOG = lib.common.logging.get_logger(__name__)


async def _ping_test(board: JimboxBoardConnector) -> None:
    print("PING TEST")
    times = np.array([await board.ping() for x in range(1000)])
    LOG.info(
        f"Nano Ping: mean: {times.mean()}, 95th %tile: {np.percentile(times, 95)}, 99th %tile: {np.percentile(times, 99)}"
    )


async def _enable_test(board: JimboxBoardConnector) -> None:
    print("Enable test")
    await board.set_enabled(False)
    await board.set_enabled(True)
    enabled = await board.get_enabled()
    assert enabled is True


async def _tick_test(board: JimboxBoardConnector) -> None:
    print("Tick test")
    # transmission_ticks = 20
    # board.reconcile_speed(transmission_ticks)
    await board.set_enabled(True)
    setpoint_speed = 0.76  # should give 0.80
    await board.set_speed(setpoint_speed)
    # should here 5 ticks
    new_speed = board.get_speed()
    print(new_speed)
    assert new_speed == 0.80


async def _test() -> None:
    print("Starting test")
    connector = PsocMEthernetConnector(JIMBOX_IP, JIMBOX_PORT, asyncio.get_event_loop())
    await connector.open()
    board = JimboxBoardConnector(connector)
    await _ping_test(board)
    await _enable_test(board)
    await _tick_test(board)


def main() -> None:
    # parser = ArgumentParser("JIMBOX Tester")
    # args = parser.parse_args()
    lib.common.logging.init_log()
    future = _test()
    asyncio.get_event_loop().run_until_complete(future)


if __name__ == "__main__":
    main()

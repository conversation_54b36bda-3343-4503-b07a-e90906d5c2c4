import pandas as pd

from lib.common.db.carbon_db_lite import CarbonDBLite
from lib.common.db.records.latest_rotary_samples import LatestRotarySamples
from lib.common.time import maka_control_timestamp_ms


def main() -> None:
    db = CarbonDBLite(load_async=False, filepath="/data/carbon_robot_db_lite.db")
    df = pd.read_sql_query(
        f"""
            SELECT * FROM {LatestRotarySamples.__tablename__}
            """,
        con=db.engine,
    )
    df.to_csv(f"/data/rotary_encoder_dump_{maka_control_timestamp_ms()}.csv")


if __name__ == "__main__":
    main()

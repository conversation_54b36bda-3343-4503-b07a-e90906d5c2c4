add_compile_options(-fvisibility=default)

set(GENERATED_PROTO_PATH ../../../generated/lib/drivers/nanopb/proto)

execute_process(COMMAND make -C ${CMAKE_CURRENT_LIST_DIR}/proto all OUTPUT_QUIET)
file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)
file(GLOB SOURCES_PULCZAR CONFIGURE_DEPENDS pulczar_board/cpp/*.cpp pulczar_board/cpp/*.c pulczar_board/cpp/*.h pulczar_board/cpp/*.hpp)
file(GLOB SOURCES_NOFX CONFIGURE_DEPENDS nofx_board/cpp/*.cpp nofx_board/cpp/*.c nofx_board/cpp/*.h nofx_board/cpp/*.hpp)
file(GLOB PROTO_SOURCES CONFIGURE_DEPENDS ${GENERATED_PROTO_PATH}/*.c ${GENERATED_PROTO_PATH}/*.h)

set(ALL_SOURCES ${SOURCES} ${SOURCES_PULCZAR} ${PROTO_SOURCES} ${SOURCES_NOFX})

add_library(nanopb SHARED ${ALL_SOURCES})
add_dependencies(nanopb make_lib_drivers)
add_dependencies(generated nanopb)
target_compile_definitions(nanopb PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_include_directories(nanopb SYSTEM PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/cpp/) #needed for generated  code to find nanopb files
target_link_libraries(nanopb PUBLIC m stdc++fs pthread rt exceptions fmt config_tree_lib config_client_lib wheel_encoder bot_stop)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(nanopb_python SHARED ${SOURCES_PYBIND})
target_link_libraries(nanopb_python PUBLIC nanopb wheel_encoder_python)
target_compile_definitions(nanopb_python PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
target_include_directories(nanopb_python SYSTEM PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/cpp/) #needed for generated  code to find nanopb files
set_target_properties(nanopb_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)
import time
from typing import TYPE_CHECKING

from tenacity import retry, stop_after_attempt

import generated.lib.drivers.nanopb.proto.version_pb2 as version_pb
import lib.common.logging
from firmware.release.firmware_release_manager import FirmwareVersion
from generated.lib.drivers.nanopb.proto import pin_controller_pb2
from generated.lib.drivers.nanopb.proto.pin_controller_pb2 import Reply, Request
from lib.common.error import MakaException
from lib.drivers.errors.error import RetryableMakaDeviceException
from lib.drivers.nanopb.nano_connector import NanoPbConnector
from lib.drivers.protocol.maka_protocol import MakaProtocolConnector

LOG = lib.common.logging.get_logger(__name__)


class PinControllerException(MakaException):
    pass


if TYPE_CHECKING:
    DPVVType = pin_controller_pb2.DigitalPinValueValue
    PinModeType = pin_controller_pb2.PinModeValue
else:
    DPVVType = int
    PinModeType = int

PIN_MODE_INPUT = pin_controller_pb2.PinMode.INPUT
PIN_MODE_OUTPUT = pin_controller_pb2.PinMode.OUTPUT
PIN_MODE_INPUT_PULL_UP = pin_controller_pb2.PinMode.INPUT_PULL_UP

PIN_HIGH = pin_controller_pb2.DigitalPinValue.HIGH
PIN_LOW = pin_controller_pb2.DigitalPinValue.LOW


class PinControllerConnector:
    def __init__(self, protocol_connector: MakaProtocolConnector, analog_bit_resolution: int = 10):
        self._nano_connector: NanoPbConnector[Request, Reply] = NanoPbConnector(protocol_connector, Request, Reply)
        self._identifier = protocol_connector.get_identifier()
        self._analog_bit_resolution = analog_bit_resolution
        self._analog_denominator = (2 ** 10) - 1

    async def stop(self) -> None:
        await self._nano_connector.stop()

    async def ping(self) -> float:
        payload = 42
        request = self._nano_connector.create_request()
        request.ping.x = payload
        start_time = time.time()
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        duration = time.time() - start_time
        assert reply.WhichOneof("reply") == "pong"
        assert reply.pong.x == payload
        return duration

    async def get_version(self) -> FirmwareVersion:
        request = self._nano_connector.create_request(version=version_pb.Version_Request())
        try:
            reply: Reply = await self._nano_connector.send_request_await_reply(request)
        except RetryableMakaDeviceException:
            raise PinControllerException(f"{self._identifier} Board Failed to Reply to Version Request")
        if reply.WhichOneof("reply") != "version":
            raise PinControllerException(f"{self._identifier} Failed to Get Version: {reply}")
        return FirmwareVersion(reply.version.major, reply.version.minor, 0)

    # ### Pin Controller ### #

    @retry(stop=stop_after_attempt(3))
    async def configure_pin(self, pin_number: int, pin_mode: PinModeType) -> None:
        request = self._nano_connector.create_request(
            pin=pin_controller_pb2.PinRequest(
                pin=pin_controller_pb2.PinConfiguration(pin_number=pin_number, pin_mode=pin_mode)
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "pin" or reply.pin.WhichOneof("reply") != "ack":
            raise PinControllerException(f"{self._identifier} Failed to configure pin: {reply}")

    @retry(stop=stop_after_attempt(3))
    async def configure_pwm(self, pin_number: int, value_8bit: int) -> None:
        request = self._nano_connector.create_request(
            pin=pin_controller_pb2.PinRequest(
                pwm=pin_controller_pb2.PWMConfiguration(pin_number=pin_number, value_8bit=value_8bit)
            )
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "pin" or reply.pin.WhichOneof("reply") != "ack":
            raise PinControllerException(f"{self._identifier} Failed to configure pwm: {reply}")

    @retry(stop=stop_after_attempt(3))
    async def digital_write(self, pin_number: int, value: DPVVType) -> None:
        request = self._nano_connector.create_request(
            pin=pin_controller_pb2.PinRequest(write=pin_controller_pb2.DigitalWrite(pin_number=pin_number, value=value))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "pin" or reply.pin.WhichOneof("reply") != "ack":
            raise PinControllerException(f"{self._identifier} Failed to write pin: {reply}")

    @retry(stop=stop_after_attempt(3))
    async def digital_read(self, pin_number: int) -> int:
        request = self._nano_connector.create_request(
            pin=pin_controller_pb2.PinRequest(read=pin_controller_pb2.DigitalRead(pin_number=pin_number))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "pin" or reply.pin.WhichOneof("reply") != "read":
            raise PinControllerException(f"{self._identifier} Failed to read pin: {reply}")

        return int(reply.pin.read.value)

    @retry(stop=stop_after_attempt(3))
    async def analog_read(self, pin_number: int) -> float:
        request = self._nano_connector.create_request(
            pin=pin_controller_pb2.PinRequest(analog=pin_controller_pb2.AnalogRead(pin_number=pin_number))
        )
        reply: Reply = await self._nano_connector.send_request_await_reply(request)
        if reply.WhichOneof("reply") != "pin" or reply.pin.WhichOneof("reply") != "analog":
            raise PinControllerException(f"{self._identifier} Failed to analog read pin: {reply}")

        return float(int(reply.pin.analog.value) / self._analog_denominator)

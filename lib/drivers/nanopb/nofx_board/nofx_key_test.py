import asyncio

from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.nofx_board.nofx_board_connector import NoFXBoardConnector


async def run() -> None:
    board = NoFXBoardConnector()
    while True:
        try:
            key = input(
                "'f' for forward, 'b' for backward, 's' to stop\n'r' to turn right, 'l' to turn left, 'ps' to point straight\n"
            )
            if key == "f":
                await board.drive(forward=True)
                print("driving forward")
            elif key == "b":
                await board.drive(backward=True)
                print("driving backward")
            elif key == "s":
                await board.drive(stop=True)
                print("stopping drive")
            elif key == "r":
                await board.turn(right=True)
                print("turning right")
            elif key == "l":
                await board.turn(left=True)
                print("turning left")
            elif key == "ps":
                await board.turn(straight=True)
                print("going straight")
        except Exception as e:
            print(e)


def main() -> None:
    future = run()
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

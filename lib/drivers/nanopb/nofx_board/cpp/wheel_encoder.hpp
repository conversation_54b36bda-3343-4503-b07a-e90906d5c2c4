#pragma once
#include <wheel_encoder/cpp/wheel_encoder.hpp>

#include <memory>

namespace carbon {
namespace nanopb {
namespace nofx_board {
class NoFXBoardConnector;
class WheelEncoderNOFX : public wheel_encoder::WheelEncoder {
private:
  std::shared_ptr<NoFXBoardConnector> connector_;
  void broadcast_loop();

protected:
  virtual void _poll_loop() override;

public:
  WheelEncoderNOFX(bool reverse_polarity, uint32_t rotary_resolution, uint32_t poll_interval_ms);
  WheelEncoderNOFX(std::shared_ptr<NoFXBoardConnector> connector, bool reverse_polarity, uint32_t rotary_resolution,
                   uint32_t poll_interval_ms);
};

class WheelEncoderNOFXSim : public wheel_encoder::WheelEncoder {
private:
  void broadcast_loop();

protected:
  virtual void _poll_loop() override;

public:
  WheelEncoderNOFXSim(bool reverse_polarity, uint32_t rotary_resolution, uint32_t poll_interval_ms);
};

} // namespace nofx_board
} // namespace nanopb
} // namespace carbon

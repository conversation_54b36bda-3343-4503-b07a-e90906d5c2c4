#include "lib/drivers/nanopb/nofx_board/cpp/wheel_encoder.hpp"

#include <chrono>
#include <config/client/cpp/config_subscriber.hpp>
#include <exception>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/drivers/nanopb/nofx_board/cpp/nofx_board_connector.hpp>
#include <math.h>
#include <spdlog/spdlog.h>

static constexpr float IN_TO_MM(float inch) { return inch * 25.4f; }
static constexpr float TREADKILL_WHEEL_DIAM_IN = 4.096f;
static constexpr float TREADKILL_WHEEL_DIAM_MM = IN_TO_MM(TREADKILL_WHEEL_DIAM_IN);

static constexpr std::chrono::microseconds MAX_BOOT_TIME_DIFF =
    std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::hours(365 * 24));

float ticks_to_mm(float ticks, float diameter_mm, float resolution) {
  return (ticks / resolution) * (float)M_PI * diameter_mm;
}
namespace carbon {
namespace nanopb {
namespace nofx_board {
WheelEncoderNOFX::WheelEncoderNOFX(bool reverse_polarity, uint32_t rotary_resolution, uint32_t poll_interval_ms)
    : WheelEncoder(reverse_polarity, rotary_resolution, poll_interval_ms),
      connector_(std::make_shared<NoFXBoardConnector>("10.10.7.1", true)) {
  ready_ = true;
}

WheelEncoderNOFX::WheelEncoderNOFX(std::shared_ptr<NoFXBoardConnector> connector, bool reverse_polarity,
                                   uint32_t rotary_resolution, uint32_t poll_interval_ms)
    : WheelEncoder(reverse_polarity, rotary_resolution, poll_interval_ms), connector_(connector) {
  ready_ = true;
}

void WheelEncoderNOFX::_poll_loop() {
  static std::chrono::time_point<std::chrono::system_clock> boot_time = std::chrono::system_clock::now();
  if (connector_->use_broadcast()) {
    return broadcast_loop();
  }
  while (!stopped_) {
    reload();
    try {
      auto latest = connector_->rotary();
      if (!latest) {
        spdlog::info("Failed to get next rotary data");
      } else {
        // REAPER IMPLEMENTATION HACK: 3/11/2025
        // TODO: Remove this when we have a better solution
        // check for valid timestamp, valid: "within boot time by 1 year"
        auto time_diff_us =
            std::chrono::abs(std::chrono::microseconds(latest->usec) -
                             std::chrono::duration_cast<std::chrono::microseconds>(boot_time.time_since_epoch()));
        if (time_diff_us.count() > MAX_BOOT_TIME_DIFF.count()) {
          spdlog::info("Received rotary data with timestamp outside of boot time range");
          continue;
        }
        update(wheel_encoder::EncoderData(latest->front_left_ticks, latest->front_right_ticks, latest->back_left_ticks,
                                          latest->back_right_ticks, latest->usec));
      }
    } catch (const std::exception &ex) {
      spdlog::error("Exception in No FX poll loop: {}", ex.what());
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(poll_inverval_ms_));
  }
}
void WheelEncoderNOFX::broadcast_loop() {
  static std::chrono::time_point<std::chrono::system_clock> boot_time = std::chrono::system_clock::now();
  uint64_t usec = 0;
  while (!stopped_ && !lib::common::bot::BotStopHandler::get().is_stopped()) {
    reload();
    auto latest = connector_->get_next(usec);
    if (!latest) {
      spdlog::warn("Failed to get next wheel encoder broadcast data for timestamp {}", usec);
      continue;
    } else {
      // REAPER IMPLEMENTATION HACK: 3/11/2025
      // TODO: Remove this when we have a better solution
      // check for valid timestamp, valid: "within boot time by 1 year"
      auto time_diff_us =
          std::chrono::abs(std::chrono::microseconds(latest->usec) -
                           std::chrono::duration_cast<std::chrono::microseconds>(boot_time.time_since_epoch()));
      if (time_diff_us.count() > MAX_BOOT_TIME_DIFF.count()) {
        spdlog::info("Received rotary data with timestamp outside of boot time range");
        continue;
      }
      usec = latest->usec;
      update(wheel_encoder::EncoderData(latest->front_left_ticks, latest->front_right_ticks, latest->back_left_ticks,
                                        latest->back_right_ticks, latest->usec));
    }
  }
}

WheelEncoderNOFXSim::WheelEncoderNOFXSim(bool reverse_polarity, uint32_t rotary_resolution, uint32_t poll_interval_ms)
    : WheelEncoder(reverse_polarity, rotary_resolution, poll_interval_ms) {
  ready_ = true;
}

void WheelEncoderNOFXSim::_poll_loop() {}
} // namespace nofx_board
} // namespace nanopb
} // namespace carbon
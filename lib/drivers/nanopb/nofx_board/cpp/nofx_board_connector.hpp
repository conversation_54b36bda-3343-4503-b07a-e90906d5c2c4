#pragma once
#include "firmware/zephyr/wheel_encoder_board/src/fastbin.h"
#include "generated/lib/drivers/nanopb/proto/nofx_board.pb.h"
#include "lib/drivers/nanopb/cpp/connector.hpp"
#include "lib/drivers/nanopb/cpp/firmware_version.hpp"
#include "lib/drivers/nanopb/cpp/time_debug.hpp"

#include "lib/common/cpp/exceptions.h"

#include <condition_variable>
#include <memory>
#include <shared_mutex>
#include <thread>

#include <boost/circular_buffer.hpp>

namespace lib::common::bot {
class BotStopEvent;
}
namespace carbon {
namespace nanopb {
namespace nofx_board {

class NoFXBoardError : public maka_error {
public:
  explicit NoFXBoardError(const std::string &what, size_t stack_skip = 1) : maka_error(what, stack_skip + 1) {}
};

enum NoFXRotaryType {
  NOFX_ROTARY_TYPE_TICK = rotary_encoder_RotaryEncodersConfig_Request_Type_TICK,
  NOFX_ROTARY_TYPE_QUAD = rotary_encoder_RotaryEncodersConfig_Request_Type_QUAD,
  NOFX_ROTARY_TYPE_NONE = rotary_encoder_RotaryEncodersConfig_Request_Type_NONE,
};
class FastBinConnector {
private:
  void broadcast_loop();
  const std::string ip_;
  std::atomic<uint32_t> id_;
  bool use_broadcast_;
  boost::circular_buffer<fastbin_Rotary_Reply> broadcast_data_;
  std::shared_mutex mut_;
  std::condition_variable_any cv_;

  // leave last so we can use all data
  std::thread broadcast_thread_;

public:
  FastBinConnector(const std::string &ip, bool use_broadcast);
  ~FastBinConnector();
  std::unique_ptr<fastbin_Rotary_Reply> rotary();
  std::unique_ptr<fastbin_Rotary_Snapshot_Reply> rotary_snapshot(uint64_t time_first, uint64_t time_last);
  bool await_next(uint32_t timeout_ms = 1000);
  std::unique_ptr<fastbin_Rotary_Reply> get_next(uint64_t prev_time_us, uint32_t timeout_ms = 1000);
  inline bool use_broadcast() { return use_broadcast_; }
};

class NoFXBoardConnector {
public:
  NoFXBoardConnector(const std::string &ip, bool use_broadcast);
  inline std::string ip() { return connector_.ip(); }
  float ping();
  void hard_reset();
  std::unique_ptr<FirmwareVersion> get_version();

  std::unique_ptr<fastbin_Rotary_Reply> rotary();
  std::unique_ptr<fastbin_Rotary_Snapshot_Reply> rotary_snapshot(uint64_t time_first, uint64_t time_last);

  void verify_history(); // -> RotaryEncoderHistoryVerify_Reply:
  void drive(bool forward, bool backward, bool stop, float duty_cycle);
  void turn(bool left, bool right, bool straight, float duty_cycle);
  void park_brake(bool onoff);
  bool park_brake_query();
  float fuel_gauge();
  void config(NoFXRotaryType fl, NoFXRotaryType fr, NoFXRotaryType bl, NoFXRotaryType br);
  void set_epoch_time();
  uint64_t get_timestamp();
  std::unique_ptr<TimeDebug> get_time_debug();
  inline bool await_next(uint32_t timeout_ms = 1000) { return fb_connector_.await_next(timeout_ms); }
  inline std::unique_ptr<fastbin_Rotary_Reply> get_next(uint64_t prev_time_us, uint32_t timeout_ms = 1000) {
    return fb_connector_.get_next(prev_time_us, timeout_ms);
  }
  inline bool use_broadcast() { return fb_connector_.use_broadcast(); }

private:
  nanopb::Connector<nofx_board_Request, nofx_board_Reply> connector_;
  FastBinConnector fb_connector_;
};
} // namespace nofx_board
} // namespace nanopb
} // namespace carbon

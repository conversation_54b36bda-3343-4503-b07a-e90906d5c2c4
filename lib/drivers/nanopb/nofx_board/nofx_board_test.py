import asyncio
from argparse import ArgumentParser
from typing import List

import numpy as np

from lib.common.logging import get_logger, init_log
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time.sleep import async_sleep_ms
from lib.drivers.errors.error import RetryableMakaDeviceException
from lib.drivers.nanopb.nofx_board.nofx_board_connector import (
    NOFX_END_OF_TIME,
    NoFXBoardConnector,
    NoFXRotarySnapshot,
    NoFXRotaryTicks,
    NoFXRotaryType,
)

LOG = get_logger(__name__)


async def _ping_test(board: NoFXBoardConnector) -> None:
    times = np.array([await board.ping() for x in range(1000)])
    LOG.info(
        f"Nano Ping: mean: {times.mean()}, 95th %tile: {np.percentile(times, 95)}, 99th %tile: {np.percentile(times, 99)}"
    )


async def _rotary_request_test(board: NoFXBoardConnector, num: int = 60) -> None:
    replies: List[NoFXRotaryTicks] = []
    for _ in range(num):
        rotaries: NoFXRotaryTicks = await board.rotary()
        LOG.info(f"Got rotaries \n{rotaries}")
        replies.append(rotaries)
        await asyncio.sleep(1)


async def _rotary_snapshots_request_test(board: NoFXBoardConnector, num: int = 60) -> None:
    for i in range(num):
        snapshot: NoFXRotarySnapshot = await board.rotary_snapshot(0, NOFX_END_OF_TIME)
        LOG.info(f"Got snapshot \n{snapshot}")
        LOG.info("entire range micros: {}".format(snapshot.last_before.usec - snapshot.first_after.usec))

        await asyncio.sleep(3)


async def _park_brake_test(board: NoFXBoardConnector, num: int = 1) -> None:
    for _ in range(num):
        await board.park_brake(True)
        LOG.info("Park Brake Set, waiting 5 secs")
        status = await board.park_brake_query()
        assert status
        await asyncio.sleep(5)
        await board.park_brake(False)
        LOG.info("Park Brake off")
        status = await board.park_brake_query()
        assert not status
        await asyncio.sleep(5)


async def _fuel_gauge_test(board: NoFXBoardConnector) -> None:
    fuel_lvl = await board.fuel_gauge()
    LOG.info(f"Got fuel level {fuel_lvl}")


async def _config(board: NoFXBoardConnector) -> None:
    config = (NoFXRotaryType.QUAD, NoFXRotaryType.NONE, NoFXRotaryType.NONE, NoFXRotaryType.NONE)
    await board.config(*config)
    LOG.info(f"Set config: {config}")


async def _test(rotary_runs: int, version_request: bool) -> None:
    board = NoFXBoardConnector()

    if version_request:
        v = await board.get_version()
        print(v)
    else:
        while True:
            try:
                await _ping_test(board)
                await _config(board)
                await _fuel_gauge_test(board)
                await _rotary_snapshots_request_test(board, rotary_runs)
                await _rotary_request_test(board, rotary_runs)
                await _park_brake_test(board)
            except RetryableMakaDeviceException:
                LOG.exception("Got Exception")
                LOG.debug("Sleep 5 seconds")
                await async_sleep_ms(5000)
                continue


def main() -> None:
    parser = ArgumentParser("NoFX Board Tester")
    parser.add_argument("--host", type=str, default="*********")
    parser.add_argument("--port", type=int, default=4243)
    parser.add_argument("--rotary-runs", type=int, default=1)
    parser.add_argument("-v", "--version", default=False, action="store_true")
    args = parser.parse_args()
    init_log(level="DEBUG")
    future = _test(args.rotary_runs, args.version)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

from lib.drivers.nanopb.pybind.nanopb_python import RotaryReply, RotarySnapshotReply


class NoFXRotaryTicks:
    def __init__(
        self, usec: int, front_left_ticks: int, front_right_ticks: int, back_left_ticks: int, back_right_ticks: int
    ):
        self.usec = usec
        self.front_left_ticks = front_left_ticks
        self.front_right_ticks = front_right_ticks
        self.back_left_ticks = back_left_ticks
        self.back_right_ticks = back_right_ticks

    @staticmethod
    def from_cpp(reply: RotaryReply) -> "NoFXRotaryTicks":
        return NoFXRotaryTicks(
            usec=reply.usec,
            front_left_ticks=reply.front_left_ticks,
            front_right_ticks=reply.front_right_ticks,
            back_left_ticks=reply.back_left_ticks,
            back_right_ticks=reply.back_right_ticks,
        )

    def __str__(self) -> str:
        return (
            f"[@{self.usec} -> front_left_ticks: {self.front_left_ticks}, front_right_ticks: {self.front_right_ticks},"
            + f" back_left_ticks: {self.back_left_ticks}, back_right_ticks: {self.back_right_ticks}]"
        )


class NoFXRotarySnapshot:
    def __init__(
        self,
        first_before: NoFXRotaryTicks,
        first_after: NoFXRotaryTicks,
        last_before: NoFXRotaryTicks,
        last_after: NoFXRotaryTicks,
        request_first: int,
        request_last: int,
    ):
        self.first_before = first_before
        self.first_after = first_after

        self.last_before = last_before
        self.last_after = last_after

        self.request_first = request_first
        self.request_last = request_last

    @staticmethod
    def from_cpp(reply: RotarySnapshotReply) -> "NoFXRotarySnapshot":
        return NoFXRotarySnapshot(
            first_before=NoFXRotaryTicks.from_cpp(reply.first_before()),
            first_after=NoFXRotaryTicks.from_cpp(reply.first_after()),
            last_before=NoFXRotaryTicks.from_cpp(reply.last_before()),
            last_after=NoFXRotaryTicks.from_cpp(reply.last_after()),
            request_first=reply.first_us(),
            request_last=reply.last_us(),
        )

    def __str__(self) -> str:
        return "fb: [ticks: {}], fa: [ticks: {}], " "lb: [ticks: {}], la: [ticks: {}]".format(
            self.first_before, self.first_after, self.last_before, self.last_after,
        )

    @property
    def first_before_time(self) -> int:
        return self.first_before.usec

    @property
    def first_after_time(self) -> int:
        return self.first_after.usec

    @property
    def last_before_time(self) -> int:
        return self.last_before.usec

    @property
    def last_after_time(self) -> int:
        return self.last_after.usec

    @property
    def first_before_ticks(self) -> NoFXRotaryTicks:
        return self.first_before

    @property
    def first_after_ticks(self) -> NoFXRotaryTicks:
        return self.first_after

    @property
    def last_before_ticks(self) -> NoFXRotaryTicks:
        return self.last_before

    @property
    def last_after_ticks(self) -> NoFXRotaryTicks:
        return self.last_after

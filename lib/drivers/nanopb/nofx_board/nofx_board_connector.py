import asyncio
from typing import Optional

from firmware.release.firmware_release_manager import FirmwareVersion
from lib.common.error import Ma<PERSON>Exception
from lib.common.generation import is_bud
from lib.common.logging import get_logger
from lib.drivers.nanopb.bootloadable_connector import BootloadableConnector
from lib.drivers.nanopb.nofx_board.nofx_snapshot import NoFXRotarySnapshot, NoFXRotaryTicks
from lib.drivers.nanopb.pybind.nanopb_python import NoFXBoardConnector as NoFXBoardConnectorcpp
from lib.drivers.nanopb.pybind.nanopb_python import NoFXRotaryType, TimeDebug

LOG = get_logger(__name__)


def get_nofx_ip() -> str:
    if is_bud():
        return "*********"
    else:
        return "*********"


def get_nofx_bootloader_ip() -> str:
    if is_bud():
        return "**********"
    else:
        return "nonexistent"


NOFX_IP = get_nofx_ip()
NOFX_BOOTLOADER_IP = get_nofx_bootloader_ip()

NOFX_END_OF_TIME = 4000000000 * 1000 * 1000


def str_to_nofxrotarytype(v: str) -> NoFXRotaryType:
    if v == "none":
        return NoFXRotaryType.NONE
    if v == "tick":
        return NoFXRotaryType.TICK
    if v == "quad":
        return NoFXRotaryType.QUAD
    assert False, f"invalid rotary type {v}"


class NoFXBoardException(MakaException):
    pass


class NoFXBoardConnector(BootloadableConnector):
    def __init__(self) -> None:
        self._cpp_connector = NoFXBoardConnectorcpp(NOFX_IP, not is_bud())

    async def ping(self) -> float:
        return await asyncio.get_event_loop().run_in_executor(None, self._cpp_connector.ping)

    async def hard_reset(self) -> None:
        return await asyncio.get_event_loop().run_in_executor(None, self._cpp_connector.hard_reset)

    async def get_version(self) -> FirmwareVersion:
        version = await asyncio.get_event_loop().run_in_executor(None, self._cpp_connector.get_version)
        return FirmwareVersion(version.major, version.minor, version.rev)

    async def rotary(self) -> NoFXRotaryTicks:
        reply = await asyncio.get_event_loop().run_in_executor(None, self._cpp_connector.rotary)
        return NoFXRotaryTicks.from_cpp(reply)

    async def await_next(self, timeout_ms: int) -> bool:
        return await asyncio.get_event_loop().run_in_executor(None, lambda: self._cpp_connector.await_next(timeout_ms))

    async def get_next(self, prev_time_us: int, timeout_ms: int) -> Optional[NoFXRotaryTicks]:
        reply = await asyncio.get_event_loop().run_in_executor(
            None, lambda: self._cpp_connector.get_next(prev_time_us, timeout_ms)
        )
        if reply is None:
            return None
        return NoFXRotaryTicks.from_cpp(reply)

    async def rotary_snapshot(self, time_first: int, time_last: int) -> NoFXRotarySnapshot:
        reply = await asyncio.get_event_loop().run_in_executor(
            None, lambda: self._cpp_connector.rotary_snapshot(time_first, time_last)
        )
        return NoFXRotarySnapshot.from_cpp(reply)

    async def verify_history(self) -> None:
        # TODO implement this if needed
        return await asyncio.get_event_loop().run_in_executor(None, self._cpp_connector.verify_history)

    async def drive(
        self, forward: bool = False, backward: bool = False, stop: bool = False, duty_cycle: float = 1.0
    ) -> None:
        return await asyncio.get_event_loop().run_in_executor(
            None, lambda: self._cpp_connector.drive(forward, backward, stop, duty_cycle)
        )

    async def turn(
        self, left: bool = False, right: bool = False, straight: bool = False, duty_cycle: float = 1.0
    ) -> None:
        return await asyncio.get_event_loop().run_in_executor(
            None, lambda: self._cpp_connector.turn(left, right, straight, duty_cycle)
        )

    async def park_brake(self, onoff: bool) -> None:
        return await asyncio.get_event_loop().run_in_executor(None, lambda: self._cpp_connector.park_brake(onoff))

    async def park_brake_query(self) -> bool:
        return await asyncio.get_event_loop().run_in_executor(None, self._cpp_connector.park_brake_query)

    async def fuel_gauge(self) -> float:
        return await asyncio.get_event_loop().run_in_executor(None, self._cpp_connector.fuel_gauge)

    async def config(self, fl: NoFXRotaryType, fr: NoFXRotaryType, bl: NoFXRotaryType, br: NoFXRotaryType) -> None:
        return await asyncio.get_event_loop().run_in_executor(None, lambda: self._cpp_connector.config(fl, fr, bl, br))

    # ### PPS ### #
    async def set_epoch_time(self) -> None:
        return await asyncio.get_event_loop().run_in_executor(None, self._cpp_connector.set_epoch_time)

    async def get_timestamp(self) -> int:
        return await asyncio.get_event_loop().run_in_executor(None, self._cpp_connector.get_timestamp)

    async def get_time_debug(self) -> TimeDebug:
        return await asyncio.get_event_loop().run_in_executor(None, self._cpp_connector.get_time_debug)

    @property
    def connector(self) -> NoFXBoardConnectorcpp:
        return self._cpp_connector

    @property
    def ip(self) -> str:
        return self._cpp_connector.ip()

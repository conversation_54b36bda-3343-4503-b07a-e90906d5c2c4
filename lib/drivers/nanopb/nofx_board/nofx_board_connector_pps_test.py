import asyncio
import random
from argparse import ArgumentParser
from typing import Optional

import lib.common.logging
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time import maka_control_timestamp_ms
from lib.drivers.nanopb.nofx_board.nofx_board_connector import NoFXBoardConnector, NoFXRotaryType

# from lib.drivers.psoc_ethernet.psoc_ethernet_bootloader import program

LOG = lib.common.logging.get_logger(__name__)


async def _pps_test(board: NoFXBoardConnector, get_only: bool, kp: int, ki: int) -> None:
    current_time = await board.get_timestamp()
    if not get_only and (maka_control_timestamp_ms() - current_time > 1000000 or True):
        t = maka_control_timestamp_ms()
        wait = 1000 - (t % 1000)
        wait += 500
        await asyncio.sleep(wait / 1000)
        await board.set_epoch_time()

    for i in range(10000000):
        before = maka_control_timestamp_ms()
        result = await board.get_time_debug()

        after = maka_control_timestamp_ms()
        avg = int((after + before) / 2)
        print(
            f"PC: {avg:18}, Board: {int(result.timestamp_us / 1000):18}, diff: {avg - (int(result.timestamp_us / 1000)):10}, pps_timer_val: {result.pps_timer_val}, pps_ticks: {result.pps_ticks}, freq_mul: {result.freq_mul:10}, err: {result.error_ticks:10}, adj: {result.error_ticks2:10}"
        )
        await asyncio.sleep(0.1)


async def _rotary_test(board: NoFXBoardConnector, get_only: bool, kp: int, ki: int) -> None:
    result = await board.get_timestamp()
    if not get_only and (maka_control_timestamp_ms() - result > 1000000 or True):
        t = maka_control_timestamp_ms()
        wait = 1000 - (t % 1000)
        wait += 500
        await asyncio.sleep(wait / 1000)
        await board.set_epoch_time()

    for i in range(10000000):
        result_rotary = await board.rotary()
        print(f"Rotary: {result_rotary}")
        await asyncio.sleep(0.1 * random.random())


async def _snapshot_test(board: NoFXBoardConnector, get_only: bool, kp: int, ki: int) -> None:
    result = await board.get_timestamp()
    if not get_only and (maka_control_timestamp_ms() - result > 1000000 or True):
        t = maka_control_timestamp_ms()
        wait = 1000 - (t % 1000)
        wait += 500
        await asyncio.sleep(wait / 1000)
        await board.set_epoch_time()

    for i in range(10000000):
        snapshot = await board.rotary_snapshot(0, 999999999)
        print("--------------------")
        print(f"Snapshots: {snapshot}")
        print("--------------------")
        await asyncio.sleep(0.1 * random.random())


async def _reset_board() -> None:
    board = NoFXBoardConnector()
    await board.hard_reset()


async def _flash_firmware(firmware_path: str) -> None:
    # TODO make compatible with zephyr boards
    # ip = NOFX_BOOTLOADER_IP
    # print(f"Flashing Firmware: {firmware_path}")
    # await _reset_board()
    # await asyncio.sleep(2)
    # print(f"Reaching Bootloader at IP: {ip}")
    # await program(ip, NOFX_PORT, firmware_path)
    # print(f"Successfully Programmed Firmware: {firmware_path}")
    # print("Rebooting Board... (May take several seconds)")
    # await asyncio.sleep(7)
    pass


async def _test(firmware_path: Optional[str], get_only: bool, target: str, kp: int, ki: int) -> None:
    if firmware_path is not None:
        await _flash_firmware(firmware_path)
    board = NoFXBoardConnector()
    await board.ping()
    await board.config(NoFXRotaryType.QUAD, NoFXRotaryType.NONE, NoFXRotaryType.NONE, NoFXRotaryType.NONE)
    print("Ping Successful")
    if target == "pps":
        await _pps_test(board, get_only, kp, ki)
    elif target == "rotary":
        await _rotary_test(board, get_only, kp, ki)
    elif target == "snapshot":
        await _snapshot_test(board, get_only, kp, ki)


def main() -> None:
    parser = ArgumentParser("NoFX Board PPS Tester")
    parser.add_argument("-f", type=str, default=None, help="Firmware File Path")
    parser.add_argument("-g", action="store_true", default=False, help="Get Only")
    parser.add_argument("-t", type=str, default="pps", help="[pps, rotary, snapshot]")
    parser.add_argument("-p", type=int, default=900, help="P term for the timing PID")
    parser.add_argument("-i", type=int, default=900, help="I term for the timing PID")
    args = parser.parse_args()
    lib.common.logging.init_log(level="DEBUG")
    future = _test(args.f, args.g, args.t, args.p, args.i)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()


if __name__ == "__main__":
    main()

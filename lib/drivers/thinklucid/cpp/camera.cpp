#include "camera.h"

#include <fmt/format.h>
#include <fmt/ostream.h>
#include <spdlog/spdlog.h>

#include "camera_factory.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/cuda_util.h"
#include "lib/common/cpp/exceptions.h"
#include "lib/common/cpp/time.h"
#include "lib/common/cpp/utils.h"
#include "lib/drivers/thinklucid/cpp/firmware_updater.h"
#include <config/tree/cpp/config_tree.hpp>

namespace lib {
namespace drivers {
namespace thinklucid {

const int kGrabTimeoutMs = 1000;

class GrabImageGuard {
public:
  GrabImageGuard(Arena::IDevice *device) : device_(device) {
    grab_result_ = device->GetImage(kGrabTimeoutMs);
    if (grab_result_ == NULL) {
      throw grab_incomplete_error("Grab failed! Couldn't convert buffer to image");
    }
  }
  ~GrabImageGuard() {
    try {
      device_->RequeueBuffer(grab_result_);
    } catch (GenICam::GenericException &ex) {
      spdlog::error("GenICam error trying to requeue buffer in GrabImageGuard dtor: {}", ex.what());
    } catch (std::exception &ex) {
      spdlog::error("Standard lib error trying to requeue buffer in GrabImageGuard dtor: {}", ex.what());
    } catch (...) {
      // catch all for safety
      spdlog::error("Uknown error trying to requeue buffer in GrabImageGuard dtor");
    }
  }
  DELETE_COPY_AND_MOVE(GrabImageGuard)

  Arena::IImage *operator->() const noexcept { return grab_result_; }

private:
  Arena::IDevice *device_;
  Arena::IImage *grab_result_;
};

ThinkLucidCameraImpl::ThinkLucidCameraImpl(const CameraInfo &info, const CameraSettings &settings,
                                           std::shared_ptr<carbon::config::ConfigTree> camera_config)
    : CameraImpl(info, settings, camera_config), device_(nullptr), stopped_(false), pause_updating_(false),
      grabbing_(false) {
  if (info.vendor != CameraVendor::kThinkLucid) {
    throw camera_error(fmt::format("Received CameraInfo for vendor {}", info.vendor));
  }

  CameraInfo matched_info = info;
  auto matched_info_opt = ThinkLucidCameraFactory::get_instance()->get_device(info);
  if (!matched_info_opt) {
    throw camera_error(fmt::format("Failed to find camera {}", info));
  }
  matched_info = *matched_info_opt;

  try {
    Arena::DeviceInfo &device_info = *(static_cast<Arena::DeviceInfo *>(matched_info.handle.get()));
    auto *system = term_.get_system();
    try {
      device_ = system->CreateDevice(device_info);
    } catch (GenICam::GenericException &ex) {
      throw camera_error(fmt::format("Failed to create camera {}: {}", matched_info, ex.what()));
    }

    set_info(matched_info);

    apply_settings();
    autodetect_pixel_format();

    with_device device_guard(*get_settings().gpu_id);
    pinned_temp_image_ =
        torch::empty({1, height_, width_}, torch::TensorOptions().pinned_memory(true).dtype(torch::kUInt8));
  } catch (...) {
    if (device_ != nullptr) {
      try {
        spdlog::warn("Trying to destroy camera: {}", get_info());
        auto *system = term_.get_system();
        system->DestroyDevice(device_);
        spdlog::warn("Succesfully destroyed camera: {}", get_info());
      } catch (GenICam::GenericException &ex) {
        spdlog::warn("Failed to destroy camera {}: {}", get_info(), ex.what());
      }
    }
    throw;
  }
  settings_update_task_ = std::thread(&ThinkLucidCameraImpl::settings_update_loop, this);
}

void ThinkLucidCameraImpl::settings_update_loop() {
  auto bse = lib::common::bot::BotStopHandler::get().create_scoped_event(
      fmt::format("camera_settings_update_loop_{}", get_info().camera_id));

  while (!bse.is_stopped() && !stopped_) {
    try {
      update_settings_from_config();
    } catch (std::exception &ex) {
      spdlog::warn("Error updating settings {}: {}", get_info().camera_id, ex.what());
    }
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }
}

void ThinkLucidCameraImpl::apply_settings() {
  auto &settings = get_settings();
  try {
    auto *nodemap = device_->GetNodeMap();

    int64_t target_width =
        settings.roi_width.has_value() ? *settings.roi_width : GenApi::CIntegerPtr(nodemap->GetNode("Width"))->GetMax();
    Arena::SetNodeValue<int64_t>(nodemap, "Width", target_width);
    width_ = (int)Arena::GetNodeValue<int64_t>(nodemap, "Width");

    int64_t target_height = settings.roi_height.has_value() ? *settings.roi_height
                                                            : GenApi::CIntegerPtr(nodemap->GetNode("Height"))->GetMax();
    Arena::SetNodeValue<int64_t>(nodemap, "Height", target_height);
    height_ = (int)Arena::GetNodeValue<int64_t>(nodemap, "Height");

    apply_online_updateable_settings(settings);
    apply_offline_updateable_settings(settings);

    if (settings.light_source_preset.has_value()) {
      throw camera_error(fmt::format("Camera {} does not support light source presets.", get_info().model));
    }

    Arena::SetNodeValue<bool>(nodemap, "ChunkModeActive", true);
    Arena::SetNodeValue<GenICam::gcstring>(nodemap, "ChunkSelector", "ExposureTime");
    Arena::SetNodeValue<bool>(nodemap, "ChunkEnable", true);

    // Performance related settings.
    auto *tl_stream_nodemap = device_->GetTLStreamNodeMap();
    if (get_info().model == "ATL120S-C" || get_info().model == "ATP120S-C") {
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "ADCBitDepth", "Bits10");
    }
    Arena::SetNodeValue<GenICam::gcstring>(tl_stream_nodemap, "StreamBufferHandlingMode", "NewestOnly");
    Arena::SetNodeValue<bool>(tl_stream_nodemap, "StreamAutoNegotiatePacketSize", true);
    Arena::SetNodeValue<bool>(tl_stream_nodemap, "StreamPacketResendEnable", settings.stream_packet_resend);
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to apply settings for camera {}: {}", get_info(), ex.what()));
  }
}

int64_t ThinkLucidCameraImpl::update_settings(const CameraSettings &settings) {
  std::lock_guard<std::mutex> setting_lock(settings_mutex_);
  auto &previous_settings = get_settings();
  CameraSettings new_settings = previous_settings;

  if (settings.exposure_us.has_value()) {
    new_settings.exposure_us = settings.exposure_us;
  }

  if (settings.gain_db.has_value()) {
    new_settings.gain_db = settings.gain_db;
  }

  if (settings.auto_whitebalance.has_value()) {
    new_settings.auto_whitebalance = settings.auto_whitebalance;
    if (settings.auto_whitebalance.value()) {
      pause_updating_ = true;
    } else {
      pause_updating_ = false;
    }
  }

  {
    std::unique_lock<std::mutex> lock(grabbing_mutex_);
    apply_online_updateable_settings(new_settings);
  }

  set_settings(new_settings);

  return maka_control_timestamp_ms();
}

void ThinkLucidCameraImpl::update_settings_from_config() {
  if (pause_updating_) {
    // If updating is paused, don't update
    return;
  }
  std::lock_guard<std::mutex> setting_lock(settings_mutex_);
  if (device_ == NULL) {
    // Skip if device isn't yet set
    return;
  }
  auto &previous_settings = get_settings();
  CameraSettings new_settings = previous_settings;
  bool set_offline_settings = false;
  auto config_settings = cv::runtime::get_camera_settings(camera_config_);

  if (!camera_config_->get_node("auto_brightness_enabled")->get_value<bool>()) {
    if (previous_settings.exposure_us.has_value() && config_settings.exposure_us.has_value() &&
        config_settings.exposure_us.value() != previous_settings.exposure_us.value()) {
      new_settings.exposure_us = config_settings.exposure_us;
    }
  }
  if (previous_settings.gamma.has_value() && config_settings.gamma.has_value() &&
      config_settings.gamma.value() != previous_settings.gamma.value()) {
    new_settings.gamma = config_settings.gamma;
  }
  if (previous_settings.gain_db.has_value() && config_settings.gain_db.has_value() &&
      config_settings.gain_db.value() != previous_settings.gain_db.value()) {
    new_settings.gain_db = config_settings.gain_db;
  }
  if (previous_settings.light_source_preset.has_value() && config_settings.light_source_preset.has_value() &&
      config_settings.light_source_preset.value() != previous_settings.light_source_preset.value()) {
    new_settings.light_source_preset = config_settings.light_source_preset;
  }
  if (previous_settings.wb_ratio_red.has_value() && config_settings.wb_ratio_red.has_value() &&
      config_settings.wb_ratio_red.value() != previous_settings.wb_ratio_red.value()) {
    new_settings.wb_ratio_red = config_settings.wb_ratio_red;
  }
  if (previous_settings.wb_ratio_green.has_value() && config_settings.wb_ratio_green.has_value() &&
      config_settings.wb_ratio_green.value() != previous_settings.wb_ratio_green.value()) {
    new_settings.wb_ratio_green = config_settings.wb_ratio_green;
  }
  if (previous_settings.wb_ratio_blue.has_value() && config_settings.wb_ratio_blue.has_value() &&
      config_settings.wb_ratio_blue.value() != previous_settings.wb_ratio_blue.value()) {
    new_settings.wb_ratio_blue = config_settings.wb_ratio_blue;
  }

  if (previous_settings.roi_offset_x.has_value() && config_settings.roi_offset_x.has_value() &&
      config_settings.roi_offset_x.value() != previous_settings.roi_offset_x.value()) {
    set_offline_settings = true;
    new_settings.roi_offset_x = config_settings.roi_offset_x;
  }
  if (previous_settings.roi_offset_y.has_value() && config_settings.roi_offset_y.has_value() &&
      config_settings.roi_offset_y.value() != previous_settings.roi_offset_y.value()) {
    set_offline_settings = true;
    new_settings.roi_offset_y = config_settings.roi_offset_y;
  }
  if (previous_settings.mirror != config_settings.mirror) {
    set_offline_settings = true;
    new_settings.mirror = config_settings.mirror;
  }
  if (previous_settings.flip != config_settings.flip) {
    set_offline_settings = true;
    new_settings.flip = config_settings.flip;
  }
  if (previous_settings.strobing != config_settings.strobing) {
    set_offline_settings = true;
    new_settings.strobing = config_settings.strobing;
  }
  if (previous_settings.ptp != config_settings.ptp) {
    set_offline_settings = true;
    new_settings.ptp = config_settings.ptp;
  }
  if (previous_settings.line_filter_selector != config_settings.line_filter_selector) {
    set_offline_settings = true;
    new_settings.line_filter_selector = config_settings.line_filter_selector;
  }
  if (previous_settings.line_filter_width != config_settings.line_filter_width) {
    set_offline_settings = true;
    new_settings.line_filter_width = config_settings.line_filter_width;
  }

  {
    std::unique_lock<std::mutex> lock(grabbing_mutex_);
    apply_online_updateable_settings(new_settings);
  }

  if (set_offline_settings) {
    std::unique_lock<std::mutex> lock(grabbing_mutex_);
    try {
      stop_grabbing_();
      apply_offline_updateable_settings(new_settings);
      autodetect_pixel_format();
      start_grabbing_();
    } catch (camera_error &ex) {
      start_grabbing_();
      throw ex;
    }
  }

  set_settings(new_settings);
}

void ThinkLucidCameraImpl::apply_offline_updateable_settings(const CameraSettings &settings) {
  try {
    auto *nodemap = device_->GetNodeMap();

    Arena::SetNodeValue<int64_t>(
        nodemap, "OffsetX",
        settings.roi_offset_x.value_or(GenApi::CIntegerPtr(nodemap->GetNode("OffsetX"))->GetMax() / 2));
    Arena::SetNodeValue<int64_t>(
        nodemap, "OffsetY",
        settings.roi_offset_y.value_or(GenApi::CIntegerPtr(nodemap->GetNode("OffsetY"))->GetMax() / 2));

    Arena::SetNodeValue<bool>(nodemap, "ReverseX", settings.flip);
    Arena::SetNodeValue<bool>(nodemap, "ReverseY", settings.mirror);

    if (settings.strobing) {
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "LineSelector", "Line0");
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "LineMode", "Input");
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "TriggerSelector", "FrameStart");
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "TriggerSource", "Line0");
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "TriggerMode", "On");
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "TriggerActivation", "RisingEdge");
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "LineFilterSelector",
                                             settings.line_filter_selector.value_or("Deglitch").c_str());
      Arena::SetNodeValue<double>(nodemap, "LineFilterWidth", settings.line_filter_width.value_or(20));

      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "LineSelector", "Line1");
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "LineMode", "Output");
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "LineSource", "ExposureActive");
    } else {
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "TriggerMode", "Off");
    }

    if (settings.ptp) {
      Arena::SetNodeValue<bool>(nodemap, "PtpSlaveOnly", true);
      Arena::SetNodeValue<bool>(nodemap, "PtpEnable", true);
    } else {
      Arena::SetNodeValue<bool>(nodemap, "PtpEnable", false);
    }
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to apply offline settings for camera {}: {}", get_info(), ex.what()));
  }
}

void ThinkLucidCameraImpl::apply_online_updateable_settings(const CameraSettings &settings) {
  try {
    auto *nodemap = device_->GetNodeMap();
    Arena::SetNodeValue<GenICam::gcstring>(nodemap, "ExposureAuto", "Off");
    Arena::SetNodeValue<double>(nodemap, "ExposureTime", settings.exposure_us.value_or(1000.0));
    Arena::SetNodeValue<bool>(nodemap, "GammaEnable", settings.gamma.has_value());
    if (settings.gamma.has_value()) {
      Arena::SetNodeValue<double>(nodemap, "Gamma", *settings.gamma);
    }
    Arena::SetNodeValue<double>(nodemap, "Gain", settings.gain_db.value_or(0.0));

    Arena::SetNodeValue<bool>(nodemap, "BalanceWhiteEnable", true);
    if (settings.auto_whitebalance.value_or(false)) {
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "BalanceWhiteAuto", "Continuous");
    } else {
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "BalanceWhiteAuto", "Off");
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "BalanceRatioSelector", "Red");
      Arena::SetNodeValue<double>(nodemap, "BalanceRatio", settings.wb_ratio_red.value_or(1.0));
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "BalanceRatioSelector", "Green");
      Arena::SetNodeValue<double>(nodemap, "BalanceRatio", settings.wb_ratio_green.value_or(1.0));
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "BalanceRatioSelector", "Blue");
      Arena::SetNodeValue<double>(nodemap, "BalanceRatio", settings.wb_ratio_blue.value_or(1.0));
    }
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to apply online settings for camera {}: {}", get_info(), ex.what()));
  }
}

void ThinkLucidCameraImpl::autodetect_pixel_format() {
  try {
    auto *nodemap = device_->GetNodeMap();
    // Basler API has TrySetValue, but here we're stuck with fighting through exceptions.
    try {
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "PixelFormat", "BayerGR8");
      pixel_format_ = PixelFormat::kBayerGR8;
      return;
    } catch (GenICam::AccessException &ex) {
    }
    try {
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "PixelFormat", "BayerRG8");
      pixel_format_ = PixelFormat::kBayerRG8;
      return;
    } catch (GenICam::AccessException &ex) {
    }
    try {
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "PixelFormat", "BayerGB8");
      pixel_format_ = PixelFormat::kBayerGB8;
      return;
    } catch (GenICam::AccessException &ex) {
    }
    try {
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "PixelFormat", "BayerBG8");
      pixel_format_ = PixelFormat::kBayerBG8;
      return;
    } catch (GenICam::AccessException &ex) {
    }
    // Fallback for Mono cameras.
    try {
      Arena::SetNodeValue<GenICam::gcstring>(nodemap, "PixelFormat", "Mono8");
      pixel_format_ = PixelFormat::kMono8;
      return;
    } catch (GenICam::AccessException &ex) {
    }
    throw camera_error(fmt::format("Failed to find suitable PixelFormat for camera {}", get_info()));
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to autodetect PixelFormat for camera {}: {}", get_info(), ex.what()));
  }
}

void ThinkLucidCameraImpl::sync_settings() {
  auto *nodemap = device_->GetNodeMap();

  Arena::SetNodeValue<GenICam::gcstring>(nodemap, "BalanceRatioSelector", "Red");
  float balance_ratio_red = (float)Arena::GetNodeValue<double>(nodemap, "BalanceRatio");
  Arena::SetNodeValue<GenICam::gcstring>(nodemap, "BalanceRatioSelector", "Green");
  auto balance_ratio_green = (float)Arena::GetNodeValue<double>(nodemap, "BalanceRatio");
  Arena::SetNodeValue<GenICam::gcstring>(nodemap, "BalanceRatioSelector", "Blue");
  auto balance_ratio_blue = (float)Arena::GetNodeValue<double>(nodemap, "BalanceRatio");

  settings_.wb_ratio_red = balance_ratio_red;
  settings_.wb_ratio_blue = balance_ratio_blue;
  settings_.wb_ratio_green = balance_ratio_green;
}

void ThinkLucidCameraImpl::start_grabbing_() {
  try {
    device_->StartStream();
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to start grabbing for camera {}: {}", get_info(), ex.what()));
  }
}

void ThinkLucidCameraImpl::start_grabbing() {
  std::unique_lock<std::mutex> lock(grabbing_mutex_);
  if (!grabbing_) {
    start_grabbing_();
    grabbing_ = true;
  }
}

CameraImage ThinkLucidCameraImpl::grab() {
  try {
    std::unique_lock<std::mutex> lock(grabbing_mutex_);
    GrabImageGuard grab_result(device_);

    if (grab_result->IsIncomplete()) {

      throw grab_incomplete_error(fmt::format(
          "Incomplete grab for camera {}. Data size filled {}, total buffer size {}. Check network configuration.",
          get_info(), grab_result->GetSizeFilled(), grab_result->GetSizeOfBuffer()));
    }

    int64_t timestamp_ms;
    int64_t exposure_time_us =
        (int64_t)GenApi::CFloatPtr(grab_result->AsChunkData()->GetChunk("ChunkExposureTime"))->GetValue();
    if (get_settings().ptp) {
      timestamp_ms = ((int64_t)grab_result->GetTimestampNs() / 1000 + exposure_time_us / 2) / 1000;
    } else {
      // TODO: adjust for the expected delay.
      timestamp_ms = maka_control_timestamp_ms() - exposure_time_us / 2000;
    }
    CameraImage cam_image;
    cam_image.camera_id = get_info().camera_id;
    cam_image.timestamp_ms = timestamp_ms;
    cam_image.pixel_format = pixel_format_;
    cam_image.ppi = get_info().ppi;
    auto wrapped_image =
        torch::from_blob(const_cast<uint8_t *>(grab_result->GetData()), {height_, width_}, torch::kUInt8);
    if (get_settings().gpu_id.has_value()) {
      pinned_temp_image_.copy_(wrapped_image);
      cam_image.image = pinned_temp_image_.to({torch::kCUDA, *get_settings().gpu_id}, true);
    } else {
      cam_image.image = wrapped_image.clone();
    }

    return cam_image;

  } catch (GenICam::TimeoutException &ex) {
    throw grab_timeout_error(fmt::format("Timed out trying to grab an image for camera {}: {}", get_info(), ex.what()));
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to grab an image for camera {}: {}", get_info(), ex.what()));
  }
}

void ThinkLucidCameraImpl::stop_grabbing_() {
  try {
    device_->StopStream();
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to stop grabbing for camera {}: {}", get_info(), ex.what()));
  }
}

void ThinkLucidCameraImpl::stop_grabbing() {
  std::unique_lock<std::mutex> lock(grabbing_mutex_);
  if (grabbing_) {
    stop_grabbing_();
    grabbing_ = false;
  }
}

ThinkLucidCameraImpl::~ThinkLucidCameraImpl() {
  spdlog::info("Destroying {}", get_info().camera_id);
  stopped_ = true;
  settings_update_task_.join();
  if (device_ != nullptr) {
    try {
      auto *system = term_.get_system();
      system->DestroyDevice(device_);
    } catch (GenICam::GenericException &ex) {
      spdlog::warn("Failed to destroy camera {}: {}", get_info(), ex.what());
    }
  }
}

double ThinkLucidCameraImpl::get_temperature() {
  try {
    auto *nodemap = device_->GetNodeMap();
    return (double)Arena::GetNodeValue<double>(nodemap, "DeviceTemperature");
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to get temperature {}: {}", get_info(), ex.what()));
  }
}

int64_t ThinkLucidCameraImpl::get_link_speed() {
  try {
    auto *nodemap = device_->GetNodeMap();
    return (int64_t)Arena::GetNodeValue<int64_t>(nodemap, "DeviceLinkSpeed");
  } catch (GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to get device link speed {}: {}", get_info(), ex.what()));
  }
}
} // namespace thinklucid
} // namespace drivers
} // namespace lib

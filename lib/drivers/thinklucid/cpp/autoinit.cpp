#include <atomic>
#include <memory>
#include <mutex>

#include <ArenaApi.h>
#include <boost/fiber/all.hpp>

#include "autoinit.h"

namespace lib {
namespace drivers {
namespace thinklucid {

static int refcnt;
static std::mutex mutex;
static std::atomic<Arena::ISystem *> system;

Arena::ISystem *ArenaAutoInitTerm::get_system() const {
  assert(system != nullptr);
  return system;
}

void ArenaAutoInitTerm::init() {
  std::lock_guard<std::mutex> lock(mutex);
  if (refcnt == 0) {
    system = Arena::OpenSystem();
  }
  refcnt++;
}

void ArenaAutoInitTerm::fini() {
  std::lock_guard<std::mutex> lock(mutex);
  refcnt--;
  if (refcnt == 0) {
    Arena::CloseSystem(system);
  }
}

} // namespace thinklucid
} // namespace drivers
} // namespace lib

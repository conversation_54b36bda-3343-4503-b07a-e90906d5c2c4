#pragma once

#include <filesystem>
#include <optional>
#include <string>

namespace lib {
namespace drivers {
namespace thinklucid {

class FirmwareVersion {
private:
  int major_, minor_, revision_, build_;

public:
  FirmwareVersion(std::string version_string);
  FirmwareVersion(int major, int minor, int revision, int build);
  std::string to_string();

  friend bool operator==(const FirmwareVersion &c1, const FirmwareVersion &c2);
  friend bool operator!=(const FirmwareVersion &c1, const FirmwareVersion &c2);

  friend bool operator<(const FirmwareVersion &c1, const FirmwareVersion &c2);
  friend bool operator>(const FirmwareVersion &c1, const FirmwareVersion &c2);

  friend bool operator<=(const FirmwareVersion &c1, const FirmwareVersion &c2);
  friend bool operator>=(const FirmwareVersion &c1, const FirmwareVersion &c2);
};

std::optional<std::pair<FirmwareVersion, std::filesystem::path>> get_latest_firmware_version(std::string model);
bool update_firmware(const std::string &ip_address, std::filesystem::path firmware_path);
bool update_firmware_if_necessary(std::string model, std::string ip_address, FirmwareVersion current_version);

} // namespace thinklucid
} // namespace drivers
} // namespace lib

#include <ArenaApi.h>

#include <fmt/format.h>
#include <fmt/ostream.h>

#include "camera.h"
#include "camera_factory.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace drivers {
namespace thinklucid {

const int kDeviceEnumerationTimeoutMs = 100;
const std::string kLucidVisionVendorName = "Lucid Vision Labs";

ThinkLucidCameraFactory::ThinkLucidCameraFactory() { update_camera_info(); }

std::vector<CameraInfo> ThinkLucidCameraFactory::list_devices() {
  std::shared_lock<std::shared_mutex> lck(camera_info_mutex_);
  return camera_infos_;
}

std::optional<CameraInfo> ThinkLucidCameraFactory::get_device(const CameraInfo &target_info) {
  std::shared_lock<std::shared_mutex> lck(camera_info_mutex_);

  for (auto &info : camera_infos_) {
    if (info.matches(target_info)) {
      return info.combine(target_info);
    }
  }
  return {};
}

void ThinkLucidCameraFactory::update_camera_info() {
  std::unique_lock<std::shared_mutex> lck(camera_info_mutex_);

  std::vector<Arena::DeviceInfo> device_infos;
  try {
    auto *system = term_.get_system();
    system->UpdateDevices(kDeviceEnumerationTimeoutMs);
    device_infos = system->GetDevices();
  } catch (const GenICam::GenericException &ex) {
    throw camera_error(fmt::format("Failed to list ThinkLucid devices: {}", ex.what()));
  }
  camera_infos_.clear();
  for (auto &dev : device_infos) {
    if (kLucidVisionVendorName == dev.VendorName().c_str()) {
      CameraInfo info;
      info.vendor = CameraVendor::kThinkLucid;
      info.serial_number = dev.SerialNumber().c_str();
      info.ip_address = dev.IpAddressStr().c_str();
      info.model = dev.ModelName().c_str();
      info.handle = std::make_shared<Arena::DeviceInfo>(dev);
      info.firmware_version = std::string(dev.DeviceVersion().c_str());
      camera_infos_.emplace_back(info);
    }
  }
}

Camera ThinkLucidCameraFactory::create_device(const CameraInfo &info, const CameraSettings &settings,
                                              std::shared_ptr<carbon::config::ConfigTree> camera_config) {
  return Camera(
      [=](const CameraInfo &info_inner) {
        return std::make_unique<ThinkLucidCameraImpl>(info_inner, settings, camera_config);
      },
      info);
}

} // namespace thinklucid
} // namespace drivers
} // namespace lib

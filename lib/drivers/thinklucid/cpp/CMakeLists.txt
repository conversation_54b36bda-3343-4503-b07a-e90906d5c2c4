file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp)
list(FILTER SOURCES EXCLUDE REGEX ".*_python\\.cpp$")

set(ARENA_ROOT /opt/ArenaSDK_Linux_x64)
set(ARENA_ROOT_ARM /opt/ArenaSDK_Linux_ARM64)
add_compile_options(-fvisibility=default)

add_library(thinklucid_camera SHARED ${SOURCES})
target_compile_definitions(thinklucid_camera PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_compile_options(thinklucid_camera PUBLIC ${TORCH_CXX_FLAGS})
target_link_directories(thinklucid_camera PRIVATE ${ARENA_ROOT}/lib64
    ${ARENA_ROOT}/GenICam/library/lib/Linux64_x64)
target_link_directories(thinklucid_camera PRIVATE ${ARENA_ROOT_ARM}/lib
    ${ARENA_ROOT_ARM}/GenICam/library/lib/Linux64_ARM)
target_link_libraries(thinklucid_camera PRIVATE m yaml-cpp spdlog fmt arena gentl exceptions config_client_lib
    config_tree_lib GCBase_gcc54_v3_3_LUCID GenApi_gcc54_v3_3_LUCID ${TORCH_LIBRARIES} simulator_proto)
target_include_directories(thinklucid_camera PRIVATE ${ARENA_ROOT}/include/Arena
    ${ARENA_ROOT}/GenICam/library/CPP/include)
target_include_directories(thinklucid_camera PRIVATE ${ARENA_ROOT_ARM}/include/Arena
    ${ARENA_ROOT_ARM}/GenICam/library/CPP/include)

pybind11_add_module(thinklucid_camera_python SHARED camera_python.cpp)
target_link_libraries(thinklucid_camera_python PUBLIC thinklucid_camera)
target_compile_definitions(thinklucid_camera_python PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
set_target_properties(thinklucid_camera_python PROPERTIES OUTPUT_NAME camera_python.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

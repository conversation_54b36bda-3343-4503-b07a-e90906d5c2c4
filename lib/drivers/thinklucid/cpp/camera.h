#pragma once

#include <mutex>
#include <optional>

#include <ArenaApi.h>
#include <torch/torch.h>

#include "autoinit.h"
#include "config/tree/cpp/config_tree.hpp"
#include "cv/runtime/cpp/config/from_config_tree.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/cpp/utils.h"

namespace lib {
namespace drivers {
namespace thinklucid {

using namespace lib::common::camera;

class ThinkLucidCameraImpl : public CameraImpl {
public:
  ThinkLucidCameraImpl(const CameraInfo &info, const CameraSettings &settings,
                       std::shared_ptr<carbon::config::ConfigTree> camera_config);
  ~ThinkLucidCameraImpl();
  DELETE_COPY_AND_MOVE(ThinkLucidCameraImpl)

  virtual void sync_settings() override;
  virtual void start_grabbing() override;
  virtual CameraImage grab() override;
  virtual void stop_grabbing() override;
  virtual int64_t update_settings(const CameraSettings &settings) override;
  virtual double get_temperature() override;
  virtual int64_t get_link_speed() override;

private:
  void apply_settings();
  void autodetect_pixel_format();
  void apply_online_updateable_settings(const CameraSettings &settings);
  void apply_offline_updateable_settings(const CameraSettings &settings);
  void start_grabbing_();
  void stop_grabbing_();
  void settings_update_loop();
  void update_settings_from_config();

  // Used to keep LUCID runtime initialized during lifetime of the CameraFactory.
  ArenaAutoInitTerm term_;
  Arena::IDevice *device_;
  torch::Tensor pinned_temp_image_;
  std::mutex grabbing_mutex_;
  std::mutex settings_mutex_;
  std::thread settings_update_task_;
  std::atomic<bool> stopped_;
  std::atomic<bool> pause_updating_;
  bool grabbing_;
};

} // namespace thinklucid
} // namespace drivers
} // namespace lib

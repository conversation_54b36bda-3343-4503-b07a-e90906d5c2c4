#include "firmware_updater.h"

#include <fmt/format.h>
#include <map>
#include <spdlog/spdlog.h>
#include <string>

#include "lib/common/cpp/exceptions.h"

namespace lib {
namespace drivers {
namespace thinklucid {
bool operator==(const FirmwareVersion &v1, const FirmwareVersion &v2) {
  return v1.major_ == v2.major_ && v1.minor_ == v2.minor_ && v1.revision_ == v2.revision_ && v1.build_ == v2.build_;
}

bool operator!=(const FirmwareVersion &v1, const FirmwareVersion &v2) { return !operator==(v1, v2); }

bool operator>(const FirmwareVersion &v1, const FirmwareVersion &v2) {
  return (v1.major_ > v2.major_) || (v1.major_ == v2.major_ && v1.minor_ > v2.minor_) ||
         (v1.major_ == v2.major_ && v1.minor_ == v2.minor_ && v1.revision_ > v2.revision_) ||
         (v1.major_ == v2.major_ && v1.minor_ == v2.minor_ && v1.revision_ == v2.revision_ && v1.build_ > v2.build_);
}

bool operator<(const FirmwareVersion &v1, const FirmwareVersion &v2) {
  return (v1.major_ < v2.major_) || (v1.major_ == v2.major_ && v1.minor_ < v2.minor_) ||
         (v1.major_ == v2.major_ && v1.minor_ == v2.minor_ && v1.revision_ < v2.revision_) ||
         (v1.major_ == v2.major_ && v1.minor_ == v2.minor_ && v1.revision_ == v2.revision_ && v1.build_ < v2.build_);
}

bool operator<=(const FirmwareVersion &v1, const FirmwareVersion &v2) {
  return operator<(v1, v2) || operator==(v1, v2);
}

bool operator>=(const FirmwareVersion &v1, const FirmwareVersion &v2) {
  return operator>(v1, v2) || operator==(v1, v2);
}

FirmwareVersion::FirmwareVersion(std::string version_string) : major_(0), minor_(0), revision_(0), build_(0) {
  size_t last_pos = 0;
  int index = 0;
  while (index < 4) {
    size_t pos = version_string.find(".", last_pos);
    if (pos == std::string::npos) {
      pos = version_string.size();
    }
    auto value_string = version_string.substr(last_pos, pos - last_pos);
    auto value = std::stoi(value_string);
    if (index == 0) {
      major_ = value;
    } else if (index == 1) {
      minor_ = value;
    } else if (index == 2) {
      revision_ = value;
    } else if (index == 3) {
      build_ = value;
    }
    index++;
    last_pos = pos + 1;
  }
}

FirmwareVersion::FirmwareVersion(int major, int minor, int revision, int build)
    : major_(major), minor_(minor), revision_(revision), build_(build) {}

std::string FirmwareVersion::to_string() { return fmt::format("{}.{}.{}.{}", major_, minor_, revision_, build_); }

std::optional<std::pair<FirmwareVersion, std::filesystem::path>> get_latest_firmware_version(std::string model) {
  auto model_dash_pos = model.rfind("-");
  if (model_dash_pos != std::string::npos) {
    model = model.substr(0, model_dash_pos);
  }

  const std::string firmware_file_suffix = ".fwa";
  std::string robot_dir = "/robot";
  char *robot_dir_ptr = std::getenv("MAKA_ROBOT_DIR");
  if (robot_dir_ptr != NULL) {
    robot_dir = robot_dir_ptr;
  }

  std::map<FirmwareVersion, std::filesystem::path> firmware_versions;
  for (auto const &dir_entry :
       std::filesystem::directory_iterator{std::filesystem::path(robot_dir) / "lib/drivers/thinklucid/cpp/firmware/"}) {
    auto path = dir_entry.path();
    auto filename = path.filename();
    if (filename.string().rfind(firmware_file_suffix, filename.string().size() - firmware_file_suffix.size()) ==
        std::string::npos) {
      continue;
    }
    auto filename_no_ext = path.stem();

    auto underscore_position = filename_no_ext.string().find("_");
    if (underscore_position == std::string::npos) {
      continue;
    }
    auto firmware_model = filename_no_ext.string().substr(0, underscore_position);
    if (firmware_model != model) {
      continue;
    }
    auto firmware_version_string = filename_no_ext.string().substr(underscore_position + 1);
    auto firmware_version = FirmwareVersion(firmware_version_string);
    firmware_versions[firmware_version] = path;
  }
  if (firmware_versions.size() > 0) {
    auto latest_version = *(--firmware_versions.end());
    return {{latest_version.first, latest_version.second}};
  }
  return {};
}

bool update_firmware(const std::string &ip_address, std::filesystem::path firmware_path) {
  std::string robot_dir = "/robot";
  char *robot_dir_ptr = std::getenv("MAKA_ROBOT_DIR");
  if (robot_dir_ptr != NULL) {
    robot_dir = robot_dir_ptr;
  }
  int exit_code = std::system(
      fmt::format(
          "{} -i {} -f {}",
          (std::filesystem::path(robot_dir) / "lib/drivers/thinklucid/cpp/firmware_bin/LucidFirmwareUpdater").string(),
          ip_address, firmware_path.string())
          .c_str());
  return exit_code == 0;
}

bool update_firmware_if_necessary(std::string model, std::string ip_address, FirmwareVersion current_version) {
  auto latest_firmware_result = get_latest_firmware_version(model);
  if (!latest_firmware_result) {
    spdlog::info("No firmware versions available for {}. Not updating {}", model, ip_address);
    return true;
  }

  auto [latest_firmware_version, latest_firmware_path] = *latest_firmware_result;

  if (latest_firmware_version > current_version) {
    spdlog::info("Updating firmware for {}:{} from {} to {}", model, ip_address, current_version.to_string(),
                 latest_firmware_version.to_string());
    bool result = update_firmware(ip_address, latest_firmware_path);
    if (result) {
      spdlog::info("Successfully updated firmware for {}:{} from {} to {}", model, ip_address,
                   current_version.to_string(), latest_firmware_version.to_string());
    } else {
      spdlog::info("Failed to update firmware for {}:{} from {} to {}", model, ip_address, current_version.to_string(),
                   latest_firmware_version.to_string());
    }
    return result;
  }
  return true;
}

} // namespace thinklucid
} // namespace drivers
} // namespace lib

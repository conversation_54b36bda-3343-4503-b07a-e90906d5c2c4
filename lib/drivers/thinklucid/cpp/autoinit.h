#pragma once

namespace Arena { // NOLINT
class ISystem;
}

namespace lib {
namespace drivers {
namespace thinklucid {

class ArenaAutoInitTerm {
public:
  ArenaAutoInitTerm() { init(); }
  ~ArenaAutoInitTerm() { fini(); }
  ArenaAutoInitTerm(const ArenaAutoInitTerm &) { init(); }
  ArenaAutoInitTerm &operator=(const ArenaAutoInitTerm &) = default;
  ArenaAutoInitTerm(ArenaAutoInitTerm &&) { init(); }
  ArenaAutoInitTerm &operator=(ArenaAutoInitTerm &&) = default;
  Arena::ISystem *get_system() const;

private:
  void init();
  void fini();
};

} // namespace thinklucid
} // namespace drivers
} // namespace lib

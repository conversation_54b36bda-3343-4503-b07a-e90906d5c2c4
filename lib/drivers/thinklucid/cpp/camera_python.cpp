#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "firmware_updater.h"

namespace py = pybind11;

namespace lib::drivers::thinklucid {

PYBIND11_MODULE(camera_python, m) {
  py::class_<FirmwareVersion>(m, "FirmwareVersion")
      .def(py::init<std::string>(), py::call_guard<py::gil_scoped_release>());

  m.def("update_firmware_if_necessary", &update_firmware_if_necessary, py::call_guard<py::gil_scoped_release>(),
        py::arg("model"), py::arg("ip_address"), py::arg("current_version"));
}
} // namespace lib::drivers::thinklucid
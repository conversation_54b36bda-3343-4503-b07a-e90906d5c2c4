#pragma once

#include "autoinit.h"
#include "lib/common/camera/cpp/camera_factory.h"

namespace lib {
namespace drivers {
namespace thinklucid {

using namespace lib::common::camera;

class ThinkLucidCameraFactory : public CameraFactory {
public:
  ThinkLucidCameraFactory();
  std::vector<CameraInfo> list_devices() override;
  std::optional<CameraInfo> get_device(const CameraInfo &info);
  Camera create_device(const CameraInfo &info, const CameraSettings &settings,
                       std::shared_ptr<carbon::config::ConfigTree> camera_config) override;
  CameraVendor get_vendor() const override { return CameraVendor::kThinkLucid; }

  void update_camera_info();

  static std::shared_ptr<ThinkLucidCameraFactory> get_instance() {
    static std::shared_ptr<ThinkLucidCameraFactory> inst{new ThinkLucidCameraFactory()};
    return inst;
  }

private:
  // Used to keep LUCID runtime initialized during lifetime of the CameraFactory.
  ArenaAutoInitTerm term_;
  std::shared_mutex camera_info_mutex_;
  std::vector<CameraInfo> camera_infos_;
};

} // namespace thinklucid
} // namespace drivers
} // namespace lib

import os
import platform
import subprocess
from typing import Op<PERSON>, Tuple, cast

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)


class FirmwareVersion:
    def __init__(self, version: str) -> None:
        version_parts = version.split(".")
        assert len(version_parts) == 4

        self._major, self._minor, self._revision, self._build = [int(x) for x in version_parts]

    def __lt__(self, other: "FirmwareVersion") -> bool:
        return (
            (self._major < other._major)
            or (self._major == other._major and self._minor < other._minor)
            or (self._major == other._major and self._minor == other._minor and self._revision < other._revision)
            or (
                self._major == other._major
                and self._minor == other._minor
                and self._revision == other._revision
                and self._build < other._build
            )
        )

    def __eq__(self, other: object) -> bool:
        other = cast("FirmwareVersion", other)
        return (
            self._major == other._major
            and self._minor == other._minor
            and self._revision == other._revision
            and self._build == other._build
        )

    def __le__(self, other: "FirmwareVersion") -> bool:
        return self.__lt__(other) or self.__eq__(other)

    def __str__(self) -> str:
        return f"v{self._major}.{self._minor}.{self._revision}.{self._build}"

    def __repr__(self) -> str:
        return str(self)


def get_latest_firmware_version(model: str) -> Optional[Tuple[FirmwareVersion, str]]:
    model = model.rsplit("-", maxsplit=1)[0]

    firmware_file_suffix = ".fwa"
    robot_dir = os.environ.get("MAKA_ROBOT_DIR", "/robot")
    latest_firmware = None
    latest_firmware_path = None
    firmware_dir = os.path.join(robot_dir, "lib/drivers/thinklucid/cpp/firmware/")
    for file in os.listdir(firmware_dir):
        if not file.endswith(firmware_file_suffix):
            continue

        # Split model name from version in firmware file. Ex: TRI120S_1.103.0.0.fwa
        filename_no_ext = file.rsplit(".", maxsplit=1)[0]
        firmware_model, firmware_version_string = filename_no_ext.split("_", maxsplit=1)
        if firmware_model != model:
            continue

        firmware_version = FirmwareVersion(firmware_version_string)

        if latest_firmware is None or latest_firmware < firmware_version:
            latest_firmware = firmware_version
            latest_firmware_path = os.path.join(firmware_dir, file)

    if latest_firmware_path is not None and latest_firmware is not None:
        return latest_firmware, latest_firmware_path
    else:
        return None


def update_firmware(ip_address: str, firmware_path: str) -> bool:
    robot_dir = os.environ.get("MAKA_ROBOT_DIR", "/robot")

    if platform.machine() == "aarch64":
        exe_path = os.path.join(robot_dir, "lib/drivers/thinklucid/cpp/firmware_bin/LucidFirmwareUpdaterARM64")
    else:
        exe_path = os.path.join(robot_dir, "lib/drivers/thinklucid/cpp/firmware_bin/LucidFirmwareUpdater")
    exitcode = subprocess.call([exe_path, "-i", ip_address, "-f", firmware_path])
    return exitcode == 0


def update_firmware_if_necessary(name: str, model: str, ip_address: str, current_version: FirmwareVersion) -> bool:
    latest_firmware = get_latest_firmware_version(model)
    if latest_firmware is None:
        LOG.info(f"No firmware versions available for {model}. Not updating {ip_address}")
        return True

    latest_firmware_version, latest_firmware_path = latest_firmware

    if latest_firmware_version <= current_version:
        return True

    LOG.info(f"Updating firmware for {name} {model}:{ip_address} from {current_version} to {latest_firmware_version}")
    result = update_firmware(ip_address, latest_firmware_path)
    if not result:
        return False

    return True

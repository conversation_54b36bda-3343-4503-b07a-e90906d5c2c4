import os
import shutil
import time
from tarfile import TarFile
from typing import Optional

import ftputil
from ftputil import FTPHost

from firmware.release.firmware_release_manager import (
    Firmware,
    FirmwareVersion,
    get_all_firmware_versions,
    get_latest_firmware_version,
)
from lib.common.error import MakaException

CURRENT_VERSION_BACKUP = "/tmp/RUNLOAD.bak"
TARGET_VERSION_EXTRACTED = "/tmp/RUNLOAD"
KEYENCE_FIRMWARE_PATH = "1_CPUMEM/RUNLOAD"
PROJECT = "KeyenceKV8000"


class MakaUpdateException(MakaException):
    pass


def enter_mode(ftp: FTPHost, mode: str) -> None:
    files = ftp.listdir("./")
    if "Status_" + mode in files:
        return
    with ftp.open("To_" + mode) as fp:
        fp.read()

    files = ftp.listdir("./")
    retries = 15
    while "Status_" + mode not in files and retries > 0:
        time.sleep(1)
        files = ftp.listdir("./")
        retries -= 1

    if retries == 0:
        raise MakaUpdateException(f"Failed to enter mode {mode}")


def enter_run_mode(ftp: FTPHost) -> None:
    enter_mode(ftp, "RUN")


def enter_prg_mode(ftp: FTPHost) -> None:
    enter_mode(ftp, "PRG")


def get_remote_version(ftp: FTPHost) -> FirmwareVersion:
    try:
        with ftp.open(f"{KEYENCE_FIRMWARE_PATH}/VERSION") as fp:
            lines = fp.readlines()
        major, minor, rev = lines[0].split(".")
        return FirmwareVersion(int(major), int(minor), int(rev))
    except ftputil.error.FTPIOError:
        return FirmwareVersion(0, 0, 0)


def upload_folder(ftp: FTPHost, local_path: str, remote_path: str) -> None:
    original_dir = os.getcwd()
    os.chdir(local_path)
    for (dirpath, _, filenames) in os.walk("./"):
        ftp.makedirs(os.path.join(remote_path, dirpath), exist_ok=True)
        for f in filenames:
            remote_file_path = os.path.join(remote_path, dirpath, f)
            local_file_path = os.path.join(dirpath, f)
            if (
                os.path.isfile(local_file_path)
                and f != "VERSION"
                and not os.path.basename(local_file_path).startswith(".")
            ):
                ftp.upload(local_file_path, remote_file_path)
    os.chdir(original_dir)


def download_folder(ftp: FTPHost, local_path: str, remote_path: str) -> None:
    original_dir = ftp.getcwd()
    ftp.chdir(remote_path)
    for (dirpath, _, filenames) in ftp.walk("./"):
        os.makedirs(os.path.join(local_path, dirpath), exist_ok=True)
        for f in filenames:
            remote_file_path = os.path.join(dirpath, f)
            local_file_path = os.path.join(local_path, dirpath, f)
            if ftp.path.isfile(remote_file_path):
                ftp.download(remote_file_path, local_file_path)
    ftp.chdir(original_dir)


def get_local_version(path: str) -> FirmwareVersion:
    with open(os.path.join(path, "VERSION"), "r") as fp:
        version_str = fp.read().strip()
    major, minor, rev = version_str.split(".")
    return FirmwareVersion(int(major), int(minor), int(rev))


def update_firmware(ftp: FTPHost, path: str) -> None:
    enter_prg_mode(ftp)
    upload_folder(ftp, path, KEYENCE_FIRMWARE_PATH)
    enter_run_mode(ftp)

    # Upload version file after confirming all other files were copied and we successfully entered run mode
    version_file_local = os.path.join(path, "VERSION")
    version_file_remote = os.path.join(KEYENCE_FIRMWARE_PATH, "VERSION")
    ftp.upload(version_file_local, version_file_remote)


def initialize_firmware(
    version: Optional[str] = None,
    variant: Optional[str] = None,
    ip_address: str = "*********",
    user: str = "KV",
    password: str = "a",
) -> None:
    project = PROJECT
    if variant:
        project = project + "_" + variant
    target_firmware: Optional[Firmware] = None
    if not version:
        target_firmware = get_latest_firmware_version(project)
    else:
        firmwares = get_all_firmware_versions(project)
        major, minor, rev = [int(x) for x in version.strip("v").split(".")]
        searched_version = FirmwareVersion(major, minor, rev)
        for firmware in firmwares:
            if firmware.version.is_equal(searched_version):
                target_firmware = firmware
                break
        if target_firmware is None:
            target_firmware = get_latest_firmware_version(project)

    if target_firmware is None:
        raise MakaException(f"Firmware Version Not Found: {version}")

    ftp = FTPHost(ip_address, user, password)

    current_version = get_remote_version(ftp)
    target_version = target_firmware.version
    if target_version.is_equal(current_version):
        return

    shutil.rmtree(TARGET_VERSION_EXTRACTED, ignore_errors=True)
    with TarFile(target_firmware.path, "r") as firmware_tar_file:
        firmware_tar_file.extractall(TARGET_VERSION_EXTRACTED)

    ftp.makedirs(KEYENCE_FIRMWARE_PATH, exist_ok=True)

    shutil.rmtree(CURRENT_VERSION_BACKUP, ignore_errors=True)
    # Backup existing version to revert to in case new version fails to load
    download_folder(ftp, CURRENT_VERSION_BACKUP, KEYENCE_FIRMWARE_PATH)

    print(f"Updating Keyence KV8000A from {current_version} to {target_version}")

    try:
        # Clear existing firmware to assure all files are from new firmware
        ftp.rmtree(KEYENCE_FIRMWARE_PATH)
        update_firmware(ftp, TARGET_VERSION_EXTRACTED)
    except MakaUpdateException as e:
        print(f"Error: {e}. Failed to update to {target_version}. Reverting to {current_version}.")

        update_firmware(ftp, CURRENT_VERSION_BACKUP)
    shutil.rmtree(CURRENT_VERSION_BACKUP)

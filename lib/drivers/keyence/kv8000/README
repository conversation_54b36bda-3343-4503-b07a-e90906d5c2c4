To generate an update for the keyence supervisory PLC:
1. open the project in KV Studio
2. Navigate to File -> Memory card -> Write
3. Change Loading Method to "Load when mode changes from PROG to RUN"
4. Select a destination folder (Output folder will be named RUNLOAD)
5. Run `python -m tools.keyence.kv8000.package_firmware <destination folder>/RUNLOAD <MAJOR VERSION> <MINOR VERSION> <REV VERSION>`
6. Tar file will be output to current directory
7. Place tar file in firmware/release/boards/KeyenceKV8000
8. Run `python -m tools.keyence.kv8000.update_firmware` to update firmware

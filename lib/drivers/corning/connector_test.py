import random
from typing import Generator

import pytest
import serial

from lib.drivers.corning.connector import CorningConnector

# Is Hardware-In-the-Loop?
HIL = False


@pytest.fixture
def lens() -> Generator[CorningConnector, None, None]:
    try:
        yield CorningConnector(serial_device="/dev/tty.usbserial-0001")
    except serial.SerialException:
        if HIL:
            raise


def test_init(lens: CorningConnector) -> None:
    try:
        assert lens is not None
    except serial.SerialException:
        if HIL:
            raise


def test_get_focus(lens: CorningConnector) -> None:
    try:
        lens.get_focus()
    except serial.SerialException:
        # HIL required
        if HIL:
            raise


def test_set_focus(lens: CorningConnector) -> None:
    try:
        assert lens
        x = lens.get_focus()
        r = random.randint(0, 0xFFFF)
        lens.set_focus(r)
        assert lens.get_focus() == r
        lens.set_focus(0)
        assert lens.get_focus() == 0
        lens.set_focus(x)
        assert lens.get_focus() == x
        lens.set_focus(0)
        assert lens.get_focus() == 0
    except serial.SerialException:
        # HIL required
        if HIL:
            raise

import serial

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)

DEFAULT_SERIAL_DEVICE = "/dev/cu.SLAB_USB_toUART"
DEFAULT_SERIAL_BAUDRATE = 57600

# USB COMMANDS

STX = 0x02
ACK = 0x06
NACK = 0x15
WRITE = 0x37
READ = 0x38


class CorningConnector:
    def __init__(self, serial_device: str = DEFAULT_SERIAL_DEVICE, baudrate: int = DEFAULT_SERIAL_BAUDRATE):
        self._serial_device: str = serial_device
        self._baud_rate: int = baudrate
        self._port = serial.Serial(self._serial_device, self._baud_rate, timeout=1)

        LOG.info(f"Corning Connector: {self._serial_device}, {self._baud_rate}, {self._port}")

    def _asserted_read(self, expected: int) -> None:
        result = self._port.read()
        assert len(result) == 1 and expected == result[0], f"Expected {expected}, got {result}"

    def _read_req(self, addr: int) -> int:
        length = 1
        self._port.write([STX, READ, addr, length, (STX + READ + addr + length) & 0xFF])
        self._asserted_read(STX)
        self._asserted_read(READ)
        data = int(self._port.read()[0])
        self._asserted_read((STX + READ + data) & 0xFF)
        return data

    def _write_req(self, addr: int, data: int) -> None:
        length = 1
        self._port.write([STX, WRITE, addr, length, data, (STX + WRITE + addr + length + data) & 0xFF])
        self._asserted_read(STX)
        self._asserted_read(WRITE)
        self._asserted_read(ACK)
        self._asserted_read((STX + WRITE + ACK) & 0xFF)

    def get_focus(self) -> int:
        lsb = self._read_req(0x00)
        msb = self._read_req(0x01)
        focus = msb * 0xFF + lsb
        return focus

    def set_focus(self, new_focus: int) -> None:
        new_lsb = new_focus % 0xFF
        new_msb = int(new_focus / 0xFF)
        assert new_msb * 0xFF + new_lsb == new_focus
        self._write_req(0x00, new_lsb)
        self._write_req(0x01, new_msb)

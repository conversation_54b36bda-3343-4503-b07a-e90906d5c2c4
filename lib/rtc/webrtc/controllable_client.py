from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
from enum import Enum
from typing import AnyS<PERSON>, AsyncIterator, Optional

from lib.common.asyncio.wait_map import WaitMap
from lib.common.logging import get_logger
from lib.rtc.messaging.message import Message


class Channel(str, Enum):
    META = "meta"
    GPS = "gps"
    TRACTOR_CTL = "tractor_controls"
    DECK = "view_controls"


ChannelIds = {
    Channel.META: 0,
    Channel.GPS: 1,
    Channel.TRACTOR_CTL: 2,
    Channel.DECK: 3,
}

LOG = get_logger(__name__)


class MsgBus(ABC):
    @abstractmethod
    async def send(self, channel: Channel, in_msg: Message) -> None:
        pass

    @abstractmethod
    async def request_control(self, cid: Optional[str] = None) -> bool:
        pass

    @abstractmethod
    async def relinquish_control(self) -> bool:
        pass

    @property
    @abstractmethod
    def active_controller(self) -> Optional[str]:
        pass


class ControllableClient:
    def __init__(self, channel_label: Channel) -> None:
        self._msg_map: WaitMap[int, Message] = WaitMap()
        self._label = channel_label
        self._bus: Optional[MsgBus] = None

    @property
    def label(self) -> Channel:
        return self._label

    async def request_control(self, cid: Optional[str] = None) -> bool:
        assert self._bus is not None
        return await self._bus.request_control(cid)

    async def relinquish_control(self) -> bool:
        assert self._bus is not None
        return await self._bus.relinquish_control()

    @property
    def active_controller(self) -> Optional[str]:
        assert self._bus is not None
        return self._bus.active_controller

    def set_bus(self, bus: MsgBus) -> None:
        self._bus = bus

    async def handle_msg(self, message: AnyStr) -> None:
        m = Message.from_json(message)
        if m.response_to is not None:
            try:
                await self._msg_map.set_resp(m.response_to, m)
            except Exception:
                LOG.exception(f"Failed to add msg response to {m.response_to} to map")
        else:
            try:
                await self._server_initiated_msg(m)
            except Exception:
                LOG.exception(f"Failed to handle msg {m}")

    async def _server_initiated_msg(self, m: Message) -> None:
        pass

    async def send(self, msg: Message) -> None:
        assert self._bus is not None
        await self._bus.send(self._label, msg)

    async def sr_msg(self, in_msg: Message, timeout: float = 1.0) -> Message:
        assert self._bus is not None
        return await self._msg_map.await_resp(in_msg.id, self._bus.send(self._label, in_msg), timeout)

    @asynccontextmanager
    async def temp_control(self) -> AsyncIterator[None]:
        current_controller = self.active_controller
        await self.request_control()
        try:
            yield
        finally:
            if current_controller is not None:
                await self.request_control(current_controller)
            else:
                await self.relinquish_control()

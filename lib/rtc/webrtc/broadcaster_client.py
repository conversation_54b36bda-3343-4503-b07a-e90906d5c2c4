import asyncio
import json
import logging
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Type, TypeVar
from uuid import uuid4

from aiortc import RTCDataChannel, RTCPeerConnection, RTCSessionDescription
from dataclass_wizard import <PERSON>SON<PERSON><PERSON>rd

import lib.rtc.messaging.signal_server_msgs as ss_msg
from lib.common.logging import get_logger
from lib.rtc.broadcaster.local_signal_client import LocalSignalClient
from lib.rtc.messaging.message import Message
from lib.rtc.webrtc.controllable_client import Channel, ChannelIds, ControllableClient, MsgBus

LOG = get_logger(__name__)
get_logger("aioice.ice").setLevel(logging.WARNING)  # this unit is too verbose


def sdp_from_str(message_str: str) -> Optional[RTCSessionDescription]:
    message = json.loads(message_str)
    if message["type"] in ["answer", "offer"]:
        return RTCSessionDescription(**message)
    return None


@dataclass
class ControlStatus(JSONWizard):
    controller: Optional[str] = None


@dataclass
class ControlRequest(JSONWizard):
    inControl: bool


class MetaMsgType(str, Enum):
    CONTROL_REQUEST = "CONTROL_REQUEST"
    CONTROL_RESPONSE = "CONTROL_RESPONSE"
    CONTROL_STATUS = "CONTROL_STATUS"


class MetaController(ControllableClient):
    def __init__(self) -> None:
        super().__init__(Channel.META)
        self._active_controller: Optional[str] = None

    @property
    def active_controller(self) -> Optional[str]:
        return self._active_controller

    async def _server_initiated_msg(self, m: Message) -> None:
        if m.msg_type == MetaMsgType.CONTROL_STATUS:
            status = ControlStatus.from_dict(m.content)
            self._active_controller = status.controller
            LOG.info(f"Active controller is now {self._active_controller}")
        else:
            LOG.debug(f"Got meta msg {m}")

    async def request_control(self, cid: Optional[str] = None) -> bool:
        msg = Message.build(MetaMsgType.CONTROL_REQUEST, ControlRequest(inControl=True), client_id=cid)
        out = await self.sr_msg(msg)
        if out.msg_type != MetaMsgType.CONTROL_RESPONSE:
            LOG.error(f"Received unknown response type {out}")
            return False
        if out.error is not None:
            LOG.info(f"Failed to take control: {out.error}")
            return False
        return True

    async def relinquish_control(self) -> bool:
        out = await self.sr_msg(Message.build(MetaMsgType.CONTROL_REQUEST, ControlRequest(inControl=False)))
        if out.msg_type != MetaMsgType.CONTROL_RESPONSE:
            LOG.error(f"Received unknown response type {out}")
            return False
        if out.error is not None:
            LOG.info(f"Failed to take control: {out.error}")
            return False
        return True


T = TypeVar("T", bound="Client")


class Client(MsgBus):
    def __init__(self, id: str, sc: LocalSignalClient, controllables: List[ControllableClient]) -> None:
        self._id = id
        self._sc = sc
        self._pc: Optional[RTCPeerConnection] = None
        self._active_controller: Optional[str] = None

        # setup Signaling messages
        sc.register(ss_msg.MessageType.SDP, self._on_sdp)
        sc.register(ss_msg.MessageType.CANDIDATE, self._on_candidate)
        self._channels: Dict[Channel, RTCDataChannel] = {}
        self._meta_controller = MetaController()
        controllables.append(self._meta_controller)

        self._controllables: Dict[Channel, ControllableClient] = {}
        for client in controllables:
            client.set_bus(self)
            self._controllables[client.label] = client

    @property
    def id(self) -> str:
        return self._id

    @property
    def active_controller(self) -> Optional[str]:
        return self._meta_controller.active_controller

    @classmethod
    async def build(
        cls: Type[T],
        signal_client: LocalSignalClient,
        controllables: List[ControllableClient],
        id: Optional[str] = None,
        connect_if_trivial: bool = True,
    ) -> T:
        if id is None:
            id = str(uuid4())
        assert id is not None
        client = cls(id, signal_client, controllables)
        if connect_if_trivial:
            streams = await client.get_streams()
            if len(streams) == 1:
                streamId = list(streams.values())[0]
                LOG.info(f"Connecting to {streamId}")
                await client.connect(streamId)
        return client

    async def stop(self) -> None:
        await self._sc.stop_client()

    async def get_streams(self) -> Dict[str, str]:
        return await self._sc.broadcaster_grpc.get_streams()

    async def connect(self, streamId: str) -> None:
        await self.close()
        await self._sc.start_client()
        self._pc = RTCPeerConnection()
        assert self._pc is not None
        done = asyncio.Event()

        def inner() -> None:
            if self._pc is None:
                return
            if self._pc.connectionState in ["connected", "failed", "closed"]:
                done.set()

        self._pc.add_listener("connectionstatechange", inner)
        self._sc.send(
            Message.build(
                msg_type=ss_msg.MessageType.VIEW_REQUEST,
                content=ss_msg.ViewRequest(hostID=self._id, streamID=streamId, trustedForwarder=True),
            )
        )
        await done.wait()
        if self._pc.connectionState != "connected":
            raise Exception("Failed to connect")

        for chan, client in self._controllables.items():
            rtc_channel = self._pc.createDataChannel(label=chan, ordered=True, negotiated=True, id=ChannelIds[chan])
            rtc_channel.add_listener("message", client.handle_msg)
            self._channels[chan] = rtc_channel

    async def _on_sdp(self, msg: Message) -> None:
        assert msg.content is not None
        sdp_msg = ss_msg.Sdp.from_dict(msg.content)
        if self._pc is None:
            LOG.warning("got sdp but peer connection is missing")
            return
        await self._pc.setRemoteDescription(sdp_msg.sdp)
        await self._pc.setLocalDescription(await self._pc.createAnswer())
        self._sc.send(
            Message.build(
                msg_type=ss_msg.MessageType.SDP,
                content=ss_msg.Sdp(
                    targetHostID=sdp_msg.hostID,
                    hostID=self._id,
                    connectionID=sdp_msg.connectionID,
                    sdp=self._pc.localDescription,
                ),
            )
        )

    async def _on_candidate(self, msg: Message) -> None:
        if self._pc is None:
            return
        candidate_msg = ss_msg.Candidate.from_dict(msg.content)
        candidate = candidate_msg.candidate.toIceCandidate()
        if candidate is None:
            LOG.info(f"Unable to parse candidate msg {msg}")
            return
        await self._pc.addIceCandidate(candidate)

    async def send(self, channel: Channel, in_msg: Message) -> None:
        assert channel in self._channels
        self._channels[channel].send(in_msg.to_json())

    async def request_control(self, cid: Optional[str] = None) -> bool:
        return await self._meta_controller.request_control(cid)

    async def relinquish_control(self) -> bool:
        return await self._meta_controller.relinquish_control()

    async def close(self) -> None:
        self._channels = {}
        if self._pc is not None:
            await self._pc.close()
            self._pc = None


def interactive() -> None:
    from lib.common.asyncio.repl import start

    imports = {
        "Client": Client,
        "LocalSignalClient": LocalSignalClient,
    }
    start(imports)


def main() -> None:
    interactive()


if __name__ == "__main__":
    main()

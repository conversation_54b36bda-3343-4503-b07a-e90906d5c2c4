from dataclasses import dataclass

from dataclass_wizard import JSONWizard

from lib.drivers.nanopb.benjamin_gps_board.benjamin_gps_board_connector import CarrierSolution, FixType

GPS_UPDATE_TYPE = "GPS_UPDATE"


@dataclass
class GpsUpdate(JSONWizard):
    timestamp: int
    latitude: float
    longitude: float
    altitude: float
    heading: float
    gnssValid: bool
    diffSoln: bool
    fixType: FixType
    carrierPhaseSoln: CarrierSolution

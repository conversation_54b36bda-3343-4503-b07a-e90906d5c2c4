from dataclasses import dataclass
from enum import Enum, IntEnum
from typing import Optional

from aiortc import RTCIceCandidate, RTCSessionDescription
from dataclass_wizard import JSONWizard


class ConnectionType(IntEnum):
    UNKNOWN = 0
    MUXED = 1
    DIRECT = 2
    INDIRECT = 3


class MessageType(str, Enum):
    VIEW_REQUEST = "VIEW_REQUEST"
    SDP = "SDP"
    CANDIDATE = "CANDIDATE"


@dataclass
class ViewRequest(JSONWizard):
    hostID: str
    streamID: str
    connectionType: ConnectionType = ConnectionType.DIRECT
    trustedForwarder: bool = False


@dataclass
class Sdp(JSONWizard):
    targetHostID: str
    hostID: str
    connectionID: str
    sdp: RTCSessionDescription


@dataclass
class IceCandidateInit(JSONWizard):
    candidate: str
    sdpMid: Optional[str] = None
    sdpMLineIndex: Optional[int] = None
    usernameFragment: Optional[str] = None

    def toIceCandidate(self) -> Optional[RTCIceCandidate]:
        parts = self.candidate.replace("candidate:", "", 1).split(" ")
        if len(parts) < 8:
            return None
        if parts[6] != "typ":
            return None
        return RTCIceCandidate(
            component=int(parts[1]),
            foundation=parts[0],
            ip=parts[4],
            port=int(parts[5]),
            priority=int(parts[3]),
            protocol=parts[2],
            type=parts[7],
            relatedAddress=parts[9] if len(parts) >= 10 and parts[8] == "raddr" else None,
            relatedPort=int(parts[11]) if len(parts) >= 12 and parts[9] == "rport" else None,
            sdpMid=self.sdpMid,
            sdpMLineIndex=self.sdpMLineIndex,
        )


@dataclass
class Candidate(JSONWizard):
    targetHostID: str
    hostID: str
    connectionID: str
    candidate: IceCandidateInit

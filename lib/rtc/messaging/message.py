from dataclasses import dataclass
from typing import Optional

from dataclass_wizard import <PERSON>SO<PERSON><PERSON><PERSON>rd

from lib.common.messaging.message import ContentType, ErrorMsg, JSONObject
from lib.common.messaging.message import Message as BaseMessage
from lib.common.messaging.message import next_id

STATUS_MSG = "SERVICE_STATUS"


@dataclass
class Message(BaseMessage):
    class _(JSONWizard.Meta):
        # skip default values for dataclass fields when `to_dict` is called
        skip_defaults = True
        recursive = False

    clientId: Optional[str] = None

    @classmethod
    def build(
        cls,
        msg_type: str,
        content: ContentType,
        id: Optional[int] = None,
        response_to: Optional[int] = None,
        client_id: Optional[str] = None,
    ) -> "Message":
        if hasattr(content, "to_dict"):
            converted: JSONObject = content.to_dict()
        else:
            converted = content
        if id is None:
            id = next_id()
        return cls(msg_type=msg_type, content=converted, id=id, response_to=response_to, clientId=client_id)

    @classmethod
    def build_error(
        cls,
        msg_type: str,
        error: ErrorMsg,
        id: Optional[int] = None,
        response_to: Optional[int] = None,
        client_id: Optional[str] = None,
    ) -> "Message":
        if id is None:
            id = next_id()
        return cls(msg_type=msg_type, error=error, id=id, response_to=response_to, clientId=client_id)

    def set_response_to(self, req: "Message") -> None:
        super().set_response_to(req)
        self.clientId = req.clientId

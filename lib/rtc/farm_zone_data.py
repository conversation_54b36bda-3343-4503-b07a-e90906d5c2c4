from dataclasses import dataclass, field
from typing import Set


@dataclass
class TractorBoundaryState:
    intersects: Set[str] = field(default_factory=set)
    inside: Set[str] = field(default_factory=set)
    valid: bool = True

    def touches(self, zones: Set[str]) -> bool:
        combined = self.inside | self.intersects
        return not combined.isdisjoint(zones)


@dataclass
class FarmAreas:
    farm: Set[str] = field(default_factory=set)
    field_area: Set[str] = field(default_factory=set)
    headland: Set[str] = field(default_factory=set)
    private_road: Set[str] = field(default_factory=set)
    obstacle: Set[str] = field(default_factory=set)

    def clear(self) -> None:
        self.farm.clear()
        self.field_area.clear()
        self.headland.clear()
        self.private_road.clear()
        self.obstacle.clear()

import asyncio
from contextlib import AbstractAsyncContextManager
from types import TracebackType
from typing import TYPE_CHECKING, Awaitable, Callable, Dict, Optional, Type

import grpc

from generated.proto.rtc import broadcaster_pb2 as br_pb
from lib.common.asyncio.cancellable_await import cancellable_await
from lib.common.asyncio.task_master import TaskMaster
from lib.common.logging import get_logger
from lib.rtc.broadcaster.client import DEFAULT_BR_HOSTNAME, DEFAULT_BR_PORT, BroadcasterClient
from lib.rtc.messaging.message import Message

if TYPE_CHECKING:
    TASK_TYPE = asyncio.Task[None]
    MSG_QUEUE = asyncio.Queue[br_pb.SignalingMsg]
    ACM = AbstractAsyncContextManager["LocalSignalClient"]
else:
    TASK_TYPE = asyncio.Task
    MSG_QUEUE = asyncio.Queue
    ACM = AbstractAsyncContextManager

LOG = get_logger(__name__)

OnMsgCallback = Callable[[Message], Awaitable[None]]


class LocalSignalClient(TaskMaster):
    def __init__(self, hostname: str, port: int, queue: MSG_QUEUE, stop_event: asyncio.Event) -> None:
        super().__init__()
        self._bc = BroadcasterClient(hostname, port)
        self._respq = queue
        self._stop_event = stop_event
        self._handlers: Dict[str, OnMsgCallback] = {}
        self._started = False

    @property
    def broadcaster_grpc(self) -> BroadcasterClient:
        return self._bc

    @staticmethod
    async def build(hostname: str = DEFAULT_BR_HOSTNAME, port: int = DEFAULT_BR_PORT) -> "LocalSignalClient":
        return LocalSignalClient(hostname, port, asyncio.Queue(), asyncio.Event())

    def register(self, msg_type: str, handler: OnMsgCallback) -> None:
        assert not self._started
        self._handlers[msg_type] = handler

    def send(self, msg: Message) -> None:
        assert self._started
        self._respq.put_nowait(br_pb.SignalingMsg(msg=msg.to_json_bytes()))

    async def _read_from_stream(
        self, stream: grpc.aio.StreamStreamCall[br_pb.SignalingMsg, br_pb.SignalingMsg], stop_event: asyncio.Event
    ) -> None:
        try:
            while not stop_event.is_set():
                try:
                    req = await stream.read()
                    if req == grpc.aio.EOF:
                        return
                    assert isinstance(req, br_pb.SignalingMsg)
                    msg = Message.from_json(req.msg)
                    if msg.msg_type in self._handlers:
                        await self._handlers[msg.msg_type](msg)
                    else:
                        LOG.info(f"Received a msg of type {msg.msg_type}, but have no handler for said message")
                except grpc.aio.AioRpcError as ex:
                    if ex.code() == grpc.StatusCode.UNAVAILABLE:
                        LOG.info("Connection lost")
                        await asyncio.sleep(1)
                    else:
                        LOG.error(f"Unknown RPC error {ex}")
                    return
                except Exception:
                    LOG.exception("Unknown exception")
                    return
        finally:
            stop_event.set()

    async def _run(self) -> None:
        stop_event = asyncio.Event()
        read_task: Optional[TASK_TYPE] = None
        while not self._stop_event.is_set():
            try:
                async with self._bc.signaling_stream() as stream:
                    stop_event.clear()
                    read_task = asyncio.get_event_loop().create_task(self._read_from_stream(stream, stop_event))
                    while not self._stop_event.is_set():
                        opt_resp = await cancellable_await(self._respq.get(), stop_event)
                        if opt_resp is None:
                            break
                        await stream.write(opt_resp)
            except asyncio.InvalidStateError:
                LOG.info("Connection already closed")
            except Exception:
                LOG.exception("Unknown error")
            finally:
                stop_event.set()
                if read_task is not None:
                    await read_task

    async def start_client(self) -> None:
        if self._started:
            return
        self._stop_event.clear()
        await self.add_task(asyncio.get_event_loop().create_task(self._run()))
        self._started = True

    async def __aenter__(self) -> "LocalSignalClient":
        await self.start_client()
        return self

    async def stop_client(self) -> None:
        if not self._started:
            return
        self._stop_event.set()
        await self.stop()
        self._started = False

    async def __aexit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        await self.stop_client()

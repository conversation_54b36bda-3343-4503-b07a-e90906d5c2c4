from contextlib import asynccontextmanager
from typing import AsyncIterator, Dict

import grpc

from generated.proto.rtc import broadcaster_pb2 as br_pb
from generated.proto.rtc import broadcaster_pb2_grpc as br_grpc
from lib.common.logging import get_logger

LOG = get_logger(__name__)

DEFAULT_BR_HOSTNAME = "localhost"
DEFAULT_BR_PORT = 61019


class BroadcasterClient:
    def __init__(self, hostname: str = DEFAULT_BR_HOSTNAME, port: int = DEFAULT_BR_PORT):
        self._hostname = hostname
        self._port = port

    @asynccontextmanager
    async def _get_stub(self) -> AsyncIterator[br_grpc.BroadcasterStub]:
        async with grpc.aio.insecure_channel(f"{self._hostname}:{self._port}") as channel:
            yield br_grpc.BroadcasterStub(channel)

    @asynccontextmanager
    async def message_bus_stream(
        self, data_channel: str
    ) -> AsyncIterator[grpc.aio.StreamStreamCall[br_pb.RtcMessage, br_pb.RtcMessage]]:
        async with self._get_stub() as stub:
            stream: grpc.aio.StreamStreamCall[br_pb.RtcMessage, br_pb.RtcMessage] = stub.MessageBus(
                metadata=(("data_channel", data_channel),)
            )
            try:
                yield stream
            finally:
                try:
                    await stream.done_writing()
                except Exception:
                    LOG.exception("Error sending done to stream")

    @asynccontextmanager
    async def signaling_stream(
        self,
    ) -> AsyncIterator[grpc.aio.StreamStreamCall[br_pb.SignalingMsg, br_pb.SignalingMsg]]:
        async with self._get_stub() as stub:
            stream: grpc.aio.StreamStreamCall[br_pb.SignalingMsg, br_pb.SignalingMsg] = stub.LocalSignalServer(
                metadata=(("media_support", "false"),)
            )
            try:
                yield stream
            finally:
                try:
                    await stream.done_writing()
                except Exception:
                    LOG.exception("Error sending done to stream")

    async def get_streams(self) -> Dict[str, str]:
        async with self._get_stub() as stub:
            resp: br_pb.StreamListResponse = await stub.GetStreamList(br_pb.StreamListRequest())
            streams: Dict[str, str] = {}
            for name, sid in resp.streams.items():
                streams[name] = sid
            return streams

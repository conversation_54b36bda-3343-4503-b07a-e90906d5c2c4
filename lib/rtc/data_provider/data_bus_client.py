import asyncio
from dataclasses import dataclass
from typing import TYPE_CHECKING, Awaitable, Callable, Dict, Optional

import grpc

from generated.proto.rtc import broadcaster_pb2 as br_pb
from lib.common.asyncio.cancellable_await import cancellable_await
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.logging import get_logger
from lib.common.messaging.message import ErrorMsg, ErrorMsgException
from lib.rtc.broadcaster.client import DEFAULT_BR_HOSTNAME, DEFAULT_BR_PORT, BroadcasterClient
from lib.rtc.messaging.message import Message

if TYPE_CHECKING:
    TASK_TYPE = asyncio.Task[None]
    MSG_QUEUE = asyncio.Queue[br_pb.RtcMessage]
else:
    TASK_TYPE = asyncio.Task
    MSG_QUEUE = asyncio.Queue

FORBIDDEN = 403
NOT_FOUND = 404

LOG = get_logger(__name__)


AuthCmpFunc = Callable[[br_pb.AuthStatus], bool]
AUTH_READ_REQ: AuthCmpFunc = lambda cmp: cmp.read
AUTH_WRITE_REQ: AuthCmpFunc = lambda cmp: cmp.write


OnMsgCallback = Callable[[Message], Awaitable[Optional[Message]]]
ConnCallback = Callable[[], Awaitable[Optional[Message]]]


@dataclass
class DecodedRtcMsg:
    msg: Message
    auth: br_pb.AuthStatus
    id: int


class _Handler:
    def __init__(self, msg_type: str, msg_handler: OnMsgCallback, resp_type: str, auth: AuthCmpFunc) -> None:
        self.msg_type = msg_type
        self._msg_handler = msg_handler
        self._resp_type = resp_type
        self._queue: asyncio.Queue[DecodedRtcMsg] = asyncio.Queue()
        self._auth = auth

    def put(self, msg: DecodedRtcMsg) -> None:
        self._queue.put_nowait(msg)

    async def run(self, write_queue: MSG_QUEUE) -> None:
        read_queue = self._queue
        while not bot_stop_handler.stopped:
            try:
                msg = await read_queue.get()
                resp: Optional[Message] = None
                if not self._auth(msg.auth):
                    resp = Message.build_error(
                        self._resp_type,
                        ErrorMsg(msg=f"You do not have permissions to access resource {self.msg_type}", code=FORBIDDEN),
                    )
                else:
                    try:
                        resp = await self._msg_handler(msg.msg)
                    except ErrorMsgException as ex:
                        resp = Message.build_error(self._resp_type, ex.error_msg)
                    except Exception as ex:
                        resp = Message.build_error(self._resp_type, ErrorMsg(msg=str(ex)))
                if resp is None:
                    # Assume valid response is empty
                    resp = Message.build(self._resp_type, {})
                assert resp is not None
                resp.set_id_if_invalid()
                resp.set_response_to(msg.msg)
                write_queue.put_nowait(br_pb.RtcMessage(id=msg.id, msg=resp.to_json_bytes()))
            except Exception:
                LOG.exception(f"Unknown error processing msg for {self.msg_type}")


@dataclass
class _CBDetails:
    cb: OnMsgCallback
    auth: AuthCmpFunc


class DataBus:
    def __init__(self) -> None:
        self._req_resp: Dict[str, str] = {}
        self._dc = ""
        self._bc: Optional[BroadcasterClient] = None
        self._respq: Optional[MSG_QUEUE] = None
        self._on_msg_cbs: Dict[str, _CBDetails] = {}
        self._on_msg_handlers: Dict[str, _Handler] = {}
        self._on_new_cb: Optional[ConnCallback] = None
        self._started = False
        self._loop = asyncio.get_event_loop()

    def register(self, msg_type: str, callback: OnMsgCallback, auth: AuthCmpFunc) -> None:
        assert self._started is False
        self._on_msg_cbs[msg_type] = _CBDetails(callback, auth)

    def on_connect(self, on_connect_cb: ConnCallback) -> None:
        assert self._started is False
        self._on_new_cb = on_connect_cb

    async def _on_connect(self, msg: DecodedRtcMsg) -> None:
        if self._on_new_cb is None:
            return
        assert self._respq is not None
        resp = await self._on_new_cb()
        if resp is not None:
            resp.set_id_if_invalid()
            resp.clientId = msg.msg.clientId
            self._respq.put_nowait(br_pb.RtcMessage(id=msg.id, msg=resp.to_json_bytes()))

    async def start(
        self,
        data_channel: str,
        req_resp: Dict[str, str],
        hostname: str = DEFAULT_BR_HOSTNAME,
        port: int = DEFAULT_BR_PORT,
    ) -> None:
        self._dc = data_channel
        self._bc = BroadcasterClient(hostname, port)
        self._req_resp = req_resp
        self._respq = asyncio.Queue()

        assert self._respq is not None
        for msg_type, cbd in self._on_msg_cbs.items():
            assert msg_type in self._req_resp, f"{msg_type} is missing a response type mapping"
            self._on_msg_handlers[msg_type] = _Handler(msg_type, cbd.cb, self._req_resp[msg_type], cbd.auth)

        self._on_msg_cbs = {}
        self._tasks = [
            asyncio.get_event_loop().create_task(handler.run(self._respq)) for handler in self._on_msg_handlers.values()
        ]
        self._tasks.append(asyncio.get_event_loop().create_task(self._run()))
        self._started = True

    def broadcast_msg_sync(self, msg: Message) -> None:
        asyncio.run_coroutine_threadsafe(self.broadcast_msg(msg), self._loop)

    async def broadcast_msg(self, msg: Message) -> None:
        assert self._respq is not None
        msg.clientId = "BROADCAST"
        self._respq.put_nowait(br_pb.RtcMessage(id=0, msg=msg.to_json_bytes()))

    async def _read_from_stream(
        self, stream: grpc.aio.StreamStreamCall[br_pb.RtcMessage, br_pb.RtcMessage], stop_event: asyncio.Event
    ) -> None:
        LOG.info("read started")
        assert self._respq is not None
        try:
            while not stop_event.is_set():
                try:
                    req = await stream.read()
                    if req == grpc.aio.EOF:
                        LOG.info("Stream closed")
                        return
                    assert isinstance(req, br_pb.RtcMessage)
                    decoded = DecodedRtcMsg(msg=Message.from_json(req.msg), auth=req.auth, id=req.id)
                    if decoded.msg.msg_type == "PROXY_CLIENT_CONNECTED":
                        await self._on_connect(decoded)
                    elif decoded.msg.msg_type in self._on_msg_handlers:
                        self._on_msg_handlers[decoded.msg.msg_type].put(decoded)
                    else:
                        LOG.info(f"Received a msg of type {decoded.msg.msg_type}, but have no handler for said message")
                        if decoded.msg.msg_type in self._req_resp:
                            self._respq.put_nowait(
                                br_pb.RtcMessage(
                                    id=req.id,
                                    msg=Message.build_error(
                                        self._req_resp[decoded.msg.msg_type],
                                        ErrorMsg(
                                            msg=f"Unsupported message type {decoded.msg.msg_type}", code=NOT_FOUND
                                        ),
                                        client_id=decoded.msg.clientId,
                                        response_to=decoded.msg.id,
                                    ).to_json_bytes(),
                                )
                            )

                except grpc.aio.AioRpcError as ex:
                    if ex.code() == grpc.StatusCode.UNAVAILABLE:
                        LOG.info("Connection lost")
                        await asyncio.sleep(1)
                    else:
                        LOG.error(f"Unknown RPC error {ex}")
                    return
                except Exception:
                    LOG.exception("Unknown exception")
                    return
        finally:
            stop_event.set()

    async def _run(self) -> None:
        assert self._respq is not None
        assert self._bc is not None
        stop_event = asyncio.Event()
        read_task: Optional[TASK_TYPE] = None
        while not bot_stop_handler.stopped:
            LOG.info("Creating connection")
            try:
                async with self._bc.message_bus_stream(self._dc) as stream:
                    stop_event.clear()
                    read_task = asyncio.get_event_loop().create_task(self._read_from_stream(stream, stop_event))
                    while not bot_stop_handler.stopped:
                        opt_resp = await cancellable_await(self._respq.get(), stop_event)
                        if opt_resp is None:
                            break
                        await stream.write(opt_resp)
            except asyncio.InvalidStateError:
                LOG.info("Connection already closed")
            except Exception:
                LOG.exception("Unknown error")
            finally:
                stop_event.set()
                if read_task is not None:
                    await read_task


DATA_BUS = DataBus()

import asyncio
from copy import deepcopy
from dataclasses import dataclass, field
from typing import Optional, cast

from aioredis import Redis as RedisClient
from dataclass_wizard import JSONWizard

from hardware_manager.python.client import HardwareManagerClient
from lib.common.generation import get_command_ip
from lib.common.geo.geojson import Position
from lib.common.gps.robot_pose import Rel<PERSON><PERSON>, RobotPose, _RelPosPolar
from lib.common.logging import get_logger
from lib.common.redis_client import RedisClient as CRRedisClient
from lib.common.units.angle import Angle

LOG = get_logger(__name__)


# 0,0 point is center of front axle XY axis is a top down view of tractor
@dataclass
class TractorDef(JSONWizard):
    rear_axle_center_pt: RelPos = field(default_factory=RelPos)
    fixed_hitch_pt: RelPos = field(default_factory=RelPos)
    three_pt_hitch_pt: RelPos = field(default_factory=RelPos)
    nose_point: RelPos = field(default_factory=RelPos)
    width_mm: float = 0.0


# 0 point is main gps (moving base gps) antenna with a XY axis from a top down view of the tractor
@dataclass
class _TractorDef:
    rear_axle_center_pt: _RelPosPolar
    fixed_hitch_pt: _RelPosPolar
    three_pt_hitch_pt: _RelPosPolar
    nose_point: _RelPosPolar


class TractorPose(RobotPose):
    def __init__(self) -> None:
        super().__init__()
        self._def: Optional[TractorDef] = None
        self._internal_def: Optional[_TractorDef] = None

    # TractorDef does not include front_axle_center_pt as that is per install dependent, however
    # the TractorDef should be the same across all tractors of the same type
    # (caveat type does not necessarily mean model but model +build date etc)
    def set_tractor_def(self, tractor_def: TractorDef, front_axle_center_pt: RelPos) -> None:
        super().set_robot_pose(front_axle_center_pt)
        # adjust coordinate system of tractor def to be relative to antenna
        self._def = tractor_def
        tmp_def = deepcopy(tractor_def)
        tmp_def.rear_axle_center_pt += front_axle_center_pt
        tmp_def.fixed_hitch_pt += front_axle_center_pt
        tmp_def.three_pt_hitch_pt += front_axle_center_pt
        tmp_def.nose_point += front_axle_center_pt

        self._internal_def = _TractorDef(
            rear_axle_center_pt=self._rel_pos_to_rel_pos_polar(tmp_def.rear_axle_center_pt),
            fixed_hitch_pt=self._rel_pos_to_rel_pos_polar(tmp_def.fixed_hitch_pt),
            three_pt_hitch_pt=self._rel_pos_to_rel_pos_polar(tmp_def.three_pt_hitch_pt),
            nose_point=self._rel_pos_to_rel_pos_polar(tmp_def.nose_point),
        )

    def get_front_axle(self, pos_gps: Position, heading: Angle) -> Position:
        return self.get_fixed_point(pos_gps, heading)

    def get_rear_axle(self, pos_gps: Position, heading: Angle) -> Position:
        assert self._internal_def is not None
        return self._rel_pos_to_ll(pos_gps=pos_gps, heading=heading, rel_pos=self._internal_def.rear_axle_center_pt)

    def get_fixed_hitch(self, pos_gps: Position, heading: Angle) -> Position:
        assert self._internal_def is not None
        return self._rel_pos_to_ll(pos_gps=pos_gps, heading=heading, rel_pos=self._internal_def.fixed_hitch_pt)

    def get_three_pt_hitch(self, pos_gps: Position, heading: Angle) -> Position:
        assert self._internal_def is not None
        return self._rel_pos_to_ll(pos_gps=pos_gps, heading=heading, rel_pos=self._internal_def.three_pt_hitch_pt)

    def get_nose(self, pos_gps: Position, heading: Angle) -> Position:
        assert self._internal_def is not None
        return self._rel_pos_to_ll(pos_gps=pos_gps, heading=heading, rel_pos=self._internal_def.nose_point)

    @property
    def tractor_def(self) -> TractorDef:
        assert self._def is not None
        return self._def

    @property
    def front_axle_pos(self) -> RelPos:
        assert self._fixed_point is not None
        return self._fixed_point


TRACTOR_POSE = TractorPose()


async def fetch_tractor_def(client: Optional[RedisClient] = None) -> TractorDef:
    if client is None:
        client = await CRRedisClient.build()
    data = cast(Optional[str], await client.get("/tractor_def"))
    if data is not None:
        return cast(TractorDef, TractorDef.from_json(data))
    LOG.warning("No Tractor definition found, all points are now at front axle pos")
    return TractorDef()


async def init_pose() -> None:
    # Currently no infra to store the tractor def in cloud, but most likely thats where it will live.
    td = await fetch_tractor_def()
    hwc = HardwareManagerClient(get_command_ip())
    while True:
        try:
            axle_pos = await hwc.get_gps_fixed_pos()
            LOG.info(f"Got axle pos {axle_pos} from hw manager")
            break
        except Exception:
            LOG.warning("Failed to get axle pos from hw manager")
            await asyncio.sleep(1)
    TRACTOR_POSE.set_tractor_def(td, axle_pos)

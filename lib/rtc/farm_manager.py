import asyncio
from contextlib import AbstractAsyncContextManager
from types import TracebackType
from typing import TYPE_CHECKING, List, Optional, Protocol, Type

from aioredis import Redis as RedisClient
from google.protobuf import timestamp_pb2

from config.client.cpp.config_client_python import ConfigTree
from generated.portal.proto import farm_pb2 as fpb
from generated.portal.proto import farm_pb2_grpc as fgrpc
from generated.proto.geo import geo_pb2 as gpb
from lib.cloud.auth import TokenStore
from lib.cloud.grpc import GrpcConf, get_channel
from lib.cloud.services import PORTAL_GRPC_HOST, PORTAL_GRPC_PORT
from lib.common.logging import get_logger
from lib.common.redis_client import RedisClient as CRRedisClient

if TYPE_CHECKING:
    ACM = AbstractAsyncContextManager["FarmSyncer"]
    TASK_TYPE = asyncio.Task[None]
else:
    ACM = AbstractAsyncContextManager
    TASK_TYPE = asyncio.Task

LOG = get_logger(__name__)
ACTIVE_FARM_KEY = "/farm/active"  # if you change this, change it in game_server.go as well


async def save_farm(farm: fpb.Farm, client: Optional[RedisClient] = None) -> None:
    if client is None:
        client = await CRRedisClient.build(False)
    data = farm.SerializeToString()
    await client.set(ACTIVE_FARM_KEY, data)


async def get_farm(client: Optional[RedisClient] = None) -> Optional[fpb.Farm]:
    if client is None:
        client = await CRRedisClient.build(False)
    data = await client.get(ACTIVE_FARM_KEY)
    if data is None or len(data) == 0:
        return None
    farm = fpb.Farm()
    farm.ParseFromString(data)
    if farm.id == "":
        return None
    return farm


def is_newer(lhs: timestamp_pb2.Timestamp, rhs: timestamp_pb2.Timestamp) -> bool:
    if lhs.seconds > rhs.seconds:
        return True
    elif lhs.seconds < rhs.seconds:
        return False
    # seconds are equal look at nanos
    return lhs.nanos > rhs.nanos


def get_latest_update(farm: fpb.Farm) -> timestamp_pb2.Timestamp:
    newest = farm.version.update_time
    for pt in farm.point_defs:
        if is_newer(pt.version.update_time, newest):
            newest = pt.version.update_time
    for zone in farm.zones:
        if is_newer(zone.version.update_time, newest):
            newest = zone.version.update_time
    return newest


class FarmChangeNotifier(Protocol):
    async def farm_changed(self) -> None:
        ...


class FarmSyncer(ACM):
    def __init__(
        self,
        farm_id_tree: ConfigTree,
        notifier: FarmChangeNotifier,
        redis: RedisClient,
        ts: TokenStore,
        grpc_conf: Optional[GrpcConf] = None,
    ) -> None:
        self._id_tree = farm_id_tree
        self._notifier = notifier
        self._redis = redis
        self._farm_id = ""
        self._cfg_changed = asyncio.Event()
        self._stop_event = asyncio.Event()
        self._tasks: List[TASK_TYPE] = []
        self._id_tree.register_callback(self.__on_cfg_change)
        if grpc_conf is None:
            self._grpc_conf = GrpcConf(PORTAL_GRPC_HOST, PORTAL_GRPC_PORT)
        else:
            self._grpc_conf = grpc_conf
        self._ts = ts
        self.__on_cfg_change()

    @staticmethod
    async def build(
        farm_id_tree: ConfigTree, notifier: FarmChangeNotifier, ts: TokenStore, grpc_conf: Optional[GrpcConf] = None
    ) -> "FarmSyncer":
        redis = await CRRedisClient.build(False)
        return FarmSyncer(farm_id_tree, notifier, redis, ts, grpc_conf)

    def __on_cfg_change(self) -> None:
        self._farm_id = self._id_tree.get_string_value()
        self._cfg_changed.set()

    async def __aenter__(self) -> "FarmSyncer":
        self._tasks.append(asyncio.get_event_loop().create_task(self._syncer()))
        return self

    async def __aexit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        self._stop_event.set()
        self._cfg_changed.set()
        await asyncio.gather(*self._tasks)

    async def _syncer(self) -> None:
        last_ts: Optional[timestamp_pb2.Timestamp] = None
        token = await self._ts.fetch()
        async with get_channel(self._grpc_conf, token) as chan:
            stub = fgrpc.FarmsServiceStub(chan)
            while not self._stop_event.is_set():
                try:
                    try:
                        await asyncio.wait_for(self._cfg_changed.wait(), 30)
                    except (asyncio.TimeoutError, asyncio.CancelledError):
                        if self._stop_event.is_set():
                            break
                        if self._farm_id == "":
                            LOG.warning("No farm id set, not attempting to fetch")
                            continue
                    if self._cfg_changed.is_set():
                        self._cfg_changed.clear()
                        if self._farm_id == "":
                            LOG.warning("No farm id set, not attempting to fetch")
                            continue
                        farm = await get_farm(self._redis)
                        if farm is not None:
                            if farm.id.id != self._farm_id:
                                last_ts = None
                            else:
                                last_ts = get_latest_update(farm)
                        else:
                            last_ts = None
                    req = fpb.GetFarmRequest(id=gpb.Id(id=self._farm_id), if_modified_since=last_ts)
                    resp: fpb.GetFarmResponse = await stub.GetFarm(req)
                    if resp.HasField("farm"):
                        last_ts = get_latest_update(resp.farm)
                        await save_farm(resp.farm, self._redis)
                        await self._notifier.farm_changed()
                except Exception:
                    LOG.exception("Unknown exception")

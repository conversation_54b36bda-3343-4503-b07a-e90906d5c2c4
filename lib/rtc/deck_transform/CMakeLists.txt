add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)

add_library(deck_transform SHARED ${SOURCES})
target_include_directories(deck_transform SYSTEM PUBLIC ${OpenCV_INCLUDE_DIRS})

target_compile_definitions(deck_transform PRIVATE -DSPDLOG_FMT_EXTERNAL=1)


target_link_libraries(deck_transform PRIVATE m pthread spdlog fmt exceptions bot_stop deck_cam opencv_imgproc opencv_cudaimgproc opencv_core opencv_imgcodecs cudart opencv_cudawarping opencv_cudaarithm opencv_calib3d deck_cv_client torch)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(deck_transform_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(deck_transform_python  PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
target_link_libraries(deck_transform_python PUBLIC deck_transform deck_cam_python)
set_target_properties(deck_transform_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)
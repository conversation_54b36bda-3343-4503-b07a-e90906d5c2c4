#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <stdexcept>

#include "lib/rtc/deck_transform/cpp/composite_view.hpp"
#include "lib/rtc/deck_transform/cpp/crop_transform.hpp"
#include "lib/rtc/deck_transform/cpp/fisheye_transform.hpp"
#include "lib/rtc/deck_transform/cpp/furrow_render_transform.hpp"
#include "lib/rtc/deck_transform/cpp/mirror_flip_transform.hpp"
#include "lib/rtc/deck_transform/cpp/perspective_transform.hpp"
#include "lib/rtc/deck_transform/cpp/resize_transform.hpp"
#include "lib/rtc/deck_transform/cpp/rotate_transform.hpp"
#include "lib/rtc/deck_transform/cpp/transform.hpp"
#include "lib/rtc/deck_transform/cpp/transform_pipeline.hpp"
#include "lib/rtc/deck_transform/cpp/undistort.hpp"
#include "lib/rtc/deck_transform/cpp/wheel_pos_render_transform.hpp"

#include <cv/deck/client/cpp/grpc_client.h>
#include <lib/drivers/deck_cam/cpp/camera.hpp>
#include <opencv2/core/mat.hpp>

namespace py = pybind11;

#include <fmt/format.h>
#include <spdlog/spdlog.h>
namespace carbon::deck_transform {
cv::Mat buffer_to_mat(py::buffer arr) {
  /*
  Make sure the input is a known type use np.astype or can get bad data
  */
  auto info = arr.request();
  if (info.ndim != 2 && info.ndim != 3) {
    throw std::runtime_error("Cannot convert python object yet");
  }
  if (info.ndim == 3 && info.shape[2] != 3) {
    throw std::runtime_error("Cannot convert python object yet");
  }
  int cv_type = -123456;
  if (info.format == "f") {
    if (info.ndim == 2) {
      cv_type = CV_32FC1;
    } else {
      cv_type = CV_32FC3;
    }
  }
  if (info.format == "d") {
    if (info.ndim == 2) {
      cv_type = CV_64FC1;
    } else {
      cv_type = CV_64FC3;
    }
  }
  if (info.format == "b") {
    if (info.ndim == 2) {
      cv_type = CV_8SC1;
    } else {
      cv_type = CV_8SC3;
    }
  }
  if (info.format == "B") {
    if (info.ndim == 2) {
      cv_type = CV_8UC1;
    } else {
      cv_type = CV_8UC3;
    }
  }
  if (info.format == "i") {
    if (info.ndim == 2) {
      cv_type = CV_32SC1;
    } else {
      cv_type = CV_32SC3;
    }
  }
  if (cv_type == -123456) {
    throw std::runtime_error("Cannot convert python object yet");
  }
  return cv::Mat(static_cast<int>(info.shape[0]), static_cast<int>(info.shape[1]), cv_type, info.ptr);
}

PYBIND11_MODULE(deck_transform_python, m) {
  py::class_<Transform, std::shared_ptr<Transform>>(m, "Transform");

  py::class_<WheelPosRenderTransform, std::shared_ptr<WheelPosRenderTransform>, Transform>(m, "WheelPosRenderTransform")
      .def(py::init<const std::string &, uint8_t, uint8_t, uint8_t, int>(), py::call_guard<py::gil_scoped_release>())
      .def_static("set_pos_for_cam", &WheelPosRenderTransform::set_pos_for_cam, py::arg("camera_id"),
                  py::arg("top_left_x"), py::arg("top_left_y"), py::arg("bottom_right_x"), py::arg("bottom_right_y"),
                  py::call_guard<py::gil_scoped_release>());

  py::class_<FurrowRenderTransform, std::shared_ptr<FurrowRenderTransform>, Transform>(m, "FurrowRenderTransform")
      .def(py::init<const std::string &>(), py::call_guard<py::gil_scoped_release>());

  py::class_<CudaTransform, std::shared_ptr<CudaTransform>>(m, "CudaTransform");

  py::class_<CropTransform, std::shared_ptr<CropTransform>, CudaTransform>(m, "CropTransform")
      .def(py::init<uint32_t, uint32_t, uint32_t, uint32_t>(), py::call_guard<py::gil_scoped_release>());

  py::class_<FisheyeTransform, std::shared_ptr<FisheyeTransform>, CudaTransform>(m, "FisheyeTransform")
      .def(py::init([](uint32_t width, uint32_t height, py::buffer remap_x, py::buffer remap_y) {
             return FisheyeTransform(width, height, buffer_to_mat(remap_x), buffer_to_mat(remap_y));
           }),
           py::call_guard<py::gil_scoped_release>());

  py::class_<Undistort, std::shared_ptr<Undistort>, CudaTransform>(m, "Undistort")
      .def(py::init([](uint32_t width, uint32_t height, py::buffer cam_mat, py::buffer dist_coeff) {
             return Undistort(width, height, buffer_to_mat(cam_mat), buffer_to_mat(dist_coeff));
           }),
           py::call_guard<py::gil_scoped_release>());
  py::enum_<MirrorFlipDir>(m, "MirrorFlipDir")
      .value("FLIP_UP_DOWN", MirrorFlipDir::FLIP_UP_DOWN)
      .value("MIRROR_LEFT_RIGHT", MirrorFlipDir::MIRROR_LEFT_RIGHT)
      .export_values();

  py::class_<MirrorFlipTransform, std::shared_ptr<MirrorFlipTransform>, CudaTransform>(m, "MirrorFlipTransform")
      .def(py::init<MirrorFlipDir>(), py::call_guard<py::gil_scoped_release>());

  py::class_<ResizeTransform, std::shared_ptr<ResizeTransform>, CudaTransform>(m, "ResizeTransform")
      .def(py::init<uint32_t, uint32_t>(), py::call_guard<py::gil_scoped_release>());

  py::enum_<RotationDir>(m, "RotationDir")
      .value("ROTATE_90_CLOCKWISE", RotationDir::ROTATE_90_CLOCKWISE)
      .value("ROTATE_90_COUNTERCLOCKWISE", RotationDir::ROTATE_90_COUNTERCLOCKWISE)
      .export_values();

  py::class_<RotateTransform, std::shared_ptr<RotateTransform>, CudaTransform>(m, "RotateTransform")
      .def(py::init<RotationDir, uint32_t, uint32_t>(), py::call_guard<py::gil_scoped_release>());

  py::class_<PerspectiveTransform, std::shared_ptr<PerspectiveTransform>, CudaTransform>(m, "PerspectiveTransform")
      .def(py::init<PxPos, PxPos, PxPos, PxPos, PxPos>(), py::call_guard<py::gil_scoped_release>());

  py::class_<TransformPipeline, std::shared_ptr<TransformPipeline>>(m, "TransformPipeline")
      .def(py::init<std::shared_ptr<deck_cam::Camera>>(), py::call_guard<py::gil_scoped_release>())
      .def("add_pre_cuda_transform", &TransformPipeline::add_pre_cuda_transform, py::arg("t"),
           py::call_guard<py::gil_scoped_release>())
      .def("add_post_cuda_transform", &TransformPipeline::add_post_cuda_transform, py::arg("t"),
           py::call_guard<py::gil_scoped_release>())
      .def("add_cuda_transform", &TransformPipeline::add_cuda_transform, py::arg("t"),
           py::call_guard<py::gil_scoped_release>());

  py::class_<CompositeView, std::shared_ptr<CompositeView>>(m, "CompositeView")
      .def(py::init<int, uint32_t, uint32_t>(), py::call_guard<py::gil_scoped_release>())
      .def("add_pane", &CompositeView::add_pane, py::arg("id"), py::arg("pipeline"), py::arg("top_left_x"),
           py::arg("top_left_y"), py::arg("width"), py::arg("height"), py::arg("mask"),
           py::call_guard<py::gil_scoped_release>())
      .def("update_pane", &CompositeView::update_pane, py::arg("id"), py::arg("top_left_x"), py::arg("top_left_y"),
           py::arg("width"), py::arg("height"), py::call_guard<py::gil_scoped_release>())
      .def("update_order", &CompositeView::update_order, py::arg("ordered_ids"),
           py::call_guard<py::gil_scoped_release>())
      .def("start", &CompositeView::start, py::call_guard<py::gil_scoped_release>())
      .def("stop", &CompositeView::stop, py::call_guard<py::gil_scoped_release>())
      .def("render", &CompositeView::render, py::call_guard<py::gil_scoped_release>());
}

} // namespace carbon::deck_transform
from typing import List, <PERSON><PERSON>

import numpy as np
import numpy.typing as npt

from lib.drivers.deck_cam.pybind.deck_cam_python import Camera

class Transform: ...

class WheelPosRenderTransform(Transform):
    def __init__(self, camera_id: str, red: int, green: int, blue: int, thickness: int) -> None: ...
    @staticmethod
    def set_pos_for_cam(
        camera_id: str, top_left_x: int, top_left_y: int, bottom_right_x: int, bottom_right_y: int
    ) -> None: ...

class FurrowRenderTransform(Transform):
    def __init__(self, camera_id: str) -> None: ...

class CudaTransform: ...

class CropTransform(CudaTransform):
    def __init__(self, top_left_x: int, top_left_y: int, width: int, height: int) -> None: ...

class FisheyeTransform(CudaTransform):
    def __init__(
        self, width: int, height: int, remap_x: npt.NDArray[np.float32], remap_y: npt.NDArray[np.float32]
    ) -> None: ...

class Undistort(CudaTransform):
    def __init__(
        self, width: int, height: int, cam_mat: npt.NDArray[np.float32], dist_coeff: npt.NDArray[np.float32]
    ) -> None: ...

class MirrorFlipDir:
    FLIP_UP_DOWN: "MirrorFlipDir"
    MIRROR_LEFT_RIGHT: "MirrorFlipDir"

class MirrorFlipTransform(CudaTransform):
    def __init__(self, dir: MirrorFlipDir) -> None: ...

class ResizeTransform(CudaTransform):
    def __init__(self, width: int, height: int) -> None: ...

class RotationDir:
    ROTATE_90_CLOCKWISE: "RotationDir"
    ROTATE_90_COUNTERCLOCKWISE: "RotationDir"

PxPos = Tuple[int, int]

class PerspectiveTransform(CudaTransform):
    def __init__(
        self, top_left: PxPos, bottom_left: PxPos, top_right: PxPos, bottom_right: PxPos, out_shape: PxPos
    ) -> None: ...

class RotateTransform(CudaTransform):
    def __init__(self, dir: RotationDir, width: int, height: int) -> None: ...

class TransformPipeline:
    def __init__(self, cam: Camera) -> None: ...
    def add_pre_cuda_transform(self, t: Transform) -> None: ...
    def add_post_cuda_transform(self, t: Transform) -> None: ...
    def add_cuda_transform(self, t: CudaTransform) -> None: ...

class CompositeView:
    def __init__(self, fd: int, width: int, height: int) -> None: ...
    def add_pane(
        self,
        id: str,
        pipeline: TransformPipeline,
        top_left_x: int,
        top_left_y: int,
        width: int,
        height: int,
        mask: List[PxPos],
    ) -> None: ...
    def update_pane(self, id: str, top_left_x: int, top_left_y: int, width: int, height: int) -> None: ...
    def update_order(self, ordered_ids: List[str]) -> None: ...
    def start(self) -> None: ...
    def stop(self) -> None: ...
    def render(self) -> None: ...

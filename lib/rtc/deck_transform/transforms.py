import math
from typing import List, <PERSON>ple

import cv2
import numpy as np
import numpy.typing as npt

import lib.rtc.deck_transform.config as config
import lib.rtc.deck_transform.pybind.deck_transform_python as transforms
from config.client.cpp.config_client_python import ConfigTree
from lib.drivers.deck_cam.config import FisheyeConfig
from lib.drivers.deck_cam.pybind.deck_cam_python import Camera


def roll_mat(angle: float, degrees: bool = False) -> npt.NDArray[np.float64]:
    if degrees:
        angle = math.radians(angle)
    return np.array([[np.cos(angle), -np.sin(angle), 0], [np.sin(angle), np.cos(angle), 0], [0, 0, 1]])


def yaw_mat(angle: float, degrees: bool = False) -> npt.NDArray[np.float64]:
    if degrees:
        angle = math.radians(angle)
    return np.array([[np.cos(angle), 0, np.sin(angle)], [0, 1, 0], [-np.sin(angle), 0, np.cos(angle)]])


def pitch_mat(angle: float, degrees: bool = False) -> npt.NDArray[np.float64]:
    if degrees:
        angle = math.radians(angle)
    return np.array([[1, 0, 0], [0, np.cos(angle), -np.sin(angle)], [0, np.sin(angle), np.cos(angle)]])


def get_remap_from_fisheye_rect(
    fisheye_rect: config.FisheyeRectConfig, camera_config: ConfigTree
) -> Tuple[npt.NDArray[np.float32], npt.NDArray[np.float32]]:
    binning = camera_config.get_node("binning").get_int_value()
    if binning < 1:
        binning = 1
    fisheye_config = FisheyeConfig.FromConfigSvc(camera_config.get_node("fisheye"))
    assert fisheye_config is not None, "Camera does not have fisheye config, but referenced in fisheye viewport"
    K = np.array(fisheye_config.k, dtype=np.float32) / binning
    D = np.array(fisheye_config.d, dtype=np.float32)

    origin = np.array(fisheye_rect.origin, dtype=np.float32)
    x = np.array(fisheye_rect.x, dtype=np.float32)
    y = np.array(fisheye_rect.y, dtype=np.float32)

    out_width = int(round(fisheye_rect.width * fisheye_rect.window_width))
    out_height = int(
        round(fisheye_rect.width * np.linalg.norm(y - origin) / np.linalg.norm(x - origin) * fisheye_rect.window_width)
    )
    y_grid, x_grid = np.mgrid[0:out_height, 0:out_width].astype(np.float32)
    coords = (
        origin * np.ones([out_height, out_width, 1], dtype=np.float32)
        + (x - origin) * x_grid[..., np.newaxis] / out_width
        + (y - origin) * y_grid[..., np.newaxis] / out_height
    )

    squashed_maps, __ = cv2.fisheye.projectPoints(
        coords.reshape(1, out_height * out_width, 3), np.zeros((3,)), np.zeros((3,)), K, D
    )
    map_x = squashed_maps[:, :, 0].reshape(out_height, out_width)
    map_y = squashed_maps[:, :, 1].reshape(out_height, out_width)
    return (map_x.astype(np.float32), map_y.astype(np.float32))


def get_remap_from_euler(
    euler_cfg: config.EulerFisheyeConfig, camera_config: ConfigTree
) -> Tuple[npt.NDArray[np.float32], npt.NDArray[np.float32]]:
    # The rows of `rect` define the top-left corner ("origin"), top-right
    # corner ("x"), and bottom-left corner ("y") of the eventual viewport
    # rectangle.
    rect = np.array([[-0.5, -0.5, 1], [0.5, -0.5, 1], [-0.5, 0.5, 1]])

    (width, height) = euler_cfg.tangent_plane_size
    rect *= np.array([width, height, euler_cfg.zoom])

    if euler_cfg.mirror:
        rect *= np.array([-1, 1, 1])
    if euler_cfg.flip:
        rect *= np.array([1, -1, 1])

    # Translate in tangent plane space
    (dx, dy) = euler_cfg.tangent_plane_translation
    rect += np.array([dx, dy, 0])

    # Move in spherical coordinates
    r = roll_mat(euler_cfg.roll)
    p = pitch_mat(euler_cfg.pitch)
    y = yaw_mat(euler_cfg.yaw)
    rot = np.matmul(np.matmul(y, p), r)
    rect = np.dot(rect, rot.T)

    conf = config.FisheyeRectConfig(
        origin=tuple(rect[0]),
        x=tuple(rect[1]),
        y=tuple(rect[2]),
        width=width,
        window_width=euler_cfg.configured_view_width,
        window_height=0,
    )
    return get_remap_from_fisheye_rect(conf, camera_config)


def get_distortion_matrix(camera_config: ConfigTree) -> Tuple[npt.NDArray[np.float32], npt.NDArray[np.float32]]:
    binning = camera_config.get_node("binning").get_int_value()
    if binning < 1:
        binning = 1
    fisheye_config = FisheyeConfig.FromConfigSvc(camera_config.get_node("fisheye"), False)
    assert fisheye_config is not None
    K = np.array(fisheye_config.k, dtype=np.float32) / binning
    D = np.array(fisheye_config.d, dtype=np.float32)
    return (K, D)


# class RgbdToViewable(Transform):
#    def __init__(self, conf: config.RgbdToViewable, shape: Tuple[int, int]) -> None:
#        self._conf = conf
#        self._shape = shape
#
#    def __call__(self, img: npt.NDArray[np.uint8]) -> npt.NDArray[np.uint8]:
#        if self._conf.to_depth:
#            return cast(npt.NDArray[np.uint8], cv2.applyColorMap(img[:, :, 3], cv2.COLORMAP_JET))
#        else:
#            return img[:, :, 0:3]
#
#    @property
#    def shape(self) -> Tuple[int, int]:
#        return self._shape


def point_2_px(pt: config.Point2f, shape: Tuple[int, int]) -> Tuple[int, int]:
    return (int(round(pt.x * shape[0])), int(round(pt.y * shape[1])))


def get_pipeline(  # noqa: C901
    transform_cfgs: List[config.TransformType], camera: Camera
) -> Tuple[transforms.TransformPipeline, Tuple[int, int]]:
    p = transforms.TransformPipeline(camera)
    cv_node = camera.config.get_node("cv")
    if cv_node is not None and cv_node.get_node("furrows_enabled").get_bool_value():
        p.add_pre_cuda_transform(transforms.FurrowRenderTransform(camera.name))
    cuda_transform_count = 0
    last_shape = (camera.width, camera.height)
    for tf in transform_cfgs:
        cur_shape = tf.calc_shape(last_shape)
        if isinstance(tf, config.WheelPosViz):
            if cuda_transform_count > 0:
                raise Exception("Cannot add wheel pos viz post gpu upload")
            p.add_pre_cuda_transform(
                transforms.WheelPosRenderTransform(
                    camera.name, tf.rgb_color[0], tf.rgb_color[1], tf.rgb_color[2], tf.thickness
                )
            )
        if isinstance(tf, config.FisheyeRectConfig):
            remap_x, remap_y = get_remap_from_fisheye_rect(tf, camera.config)
            p.add_cuda_transform(transforms.FisheyeTransform(cur_shape[0], cur_shape[1], remap_x, remap_y))
            cuda_transform_count += 1
        elif isinstance(tf, config.EulerFisheyeConfig):
            remap_x, remap_y = get_remap_from_euler(tf, camera.config)
            p.add_cuda_transform(transforms.FisheyeTransform(cur_shape[0], cur_shape[1], remap_x, remap_y))
            cuda_transform_count += 1
        elif isinstance(tf, config.Mirror):
            p.add_cuda_transform(transforms.MirrorFlipTransform(transforms.MirrorFlipDir.MIRROR_LEFT_RIGHT))
            cuda_transform_count += 1
        elif isinstance(tf, config.Flip):
            p.add_cuda_transform(transforms.MirrorFlipTransform(transforms.MirrorFlipDir.FLIP_UP_DOWN))
            cuda_transform_count += 1
        elif isinstance(tf, config.Rotate):
            if tf.rotation == 90:
                rdir = transforms.RotationDir.ROTATE_90_CLOCKWISE
            else:
                rdir = transforms.RotationDir.ROTATE_90_COUNTERCLOCKWISE
            p.add_cuda_transform(transforms.RotateTransform(rdir, cur_shape[0], cur_shape[1]))
            cuda_transform_count += 1
        elif isinstance(tf, config.Resize):
            p.add_cuda_transform(transforms.ResizeTransform(cur_shape[0], cur_shape[1]))
            cuda_transform_count += 1
        elif isinstance(tf, config.Crop):
            p.add_cuda_transform(
                transforms.CropTransform(
                    round(tf.start_x * last_shape[0]), round(tf.start_y * last_shape[1]), cur_shape[0], cur_shape[1]
                )
            )
            cuda_transform_count += 1
        elif isinstance(tf, config.PerspectiveShift):
            p.add_cuda_transform(
                transforms.PerspectiveTransform(
                    point_2_px(tf.top_left, last_shape),
                    point_2_px(tf.bottom_left, last_shape),
                    point_2_px(tf.top_right, last_shape),
                    point_2_px(tf.bottom_right, last_shape),
                    cur_shape,
                )
            )
            cuda_transform_count += 1
        elif isinstance(tf, config.RgbdToViewable):
            if cuda_transform_count > 0:
                raise Exception("Conversion to viewable image must be first transform")
            # TODO add conversion to pre-cuda
            # transforms.append(RgbdToViewable(tf, last_shape))
            raise NotImplementedError("rgbd to viewable not yet implemented")
        elif isinstance(tf, config.Undistort):
            if cuda_transform_count > 0:
                raise Exception("Undistort must be first transform")
            cam_mat, dist_coeff = get_distortion_matrix(camera.config)
            p.add_cuda_transform(transforms.Undistort(cur_shape[0], cur_shape[1], cam_mat, dist_coeff))
            cuda_transform_count += 1
        last_shape = cur_shape
    return (p, last_shape)


def get_shape(transform_cfgs: List[config.TransformType], shape: Tuple[int, int]) -> Tuple[int, int]:
    last_shape = shape
    for tf in transform_cfgs:
        last_shape = tf.calc_shape(last_shape)
    return last_shape

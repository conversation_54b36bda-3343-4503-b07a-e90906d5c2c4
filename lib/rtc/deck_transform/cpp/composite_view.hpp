#pragma once
#include <atomic>
#include <chrono>
#include <condition_variable>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <tuple>
#include <vector>

#include <opencv2/core/mat.hpp>

#include <lib/rtc/deck_transform/cpp/transform_pipeline.hpp>

namespace carbon::deck_transform {
class Pane {
public:
  Pane(const std::string &id, std::shared_ptr<TransformPipeline> pipeline, cv::Rect global_pos, uint32_t view_width,
       uint32_t view_height, const std::vector<PxPos> &mask);
  void update_pos(cv::Rect global_pos);
  inline void start() { pipeline_->start(); }
  inline void stop() { pipeline_->stop(); }
  void write(cv::Mat output, std::chrono::milliseconds timeout);
  const std::string &id() const { return id_; }

private:
  const std::string id_;
  std::shared_ptr<TransformPipeline> pipeline_;
  cv::Rect global_pos_;
  bool oob_;
  const uint32_t view_width_;
  const uint32_t view_height_;
  std::unique_ptr<cv::Mat> prev_img_;
  bool prev_img_drawn_;
  std::optional<cv::Mat> mask_;
};
class CompositeView {
public:
  CompositeView(int fd, uint32_t width, uint32_t height);
  void add_pane(const std::string &id, std::shared_ptr<TransformPipeline> pipeline, uint32_t top_left_x,
                uint32_t top_left_y, uint32_t width, uint32_t height, const std::vector<PxPos> &mask);
  void update_pane(const std::string &id, uint32_t top_left_x, uint32_t top_left_y, uint32_t width, uint32_t height);
  void start();
  void stop();
  void render();
  void update_order(const std::vector<std::string> ordered_ids);

private:
  std::vector<std::shared_ptr<Pane>> panes_; // This must be ordered to respect render order
  int fd_;
  uint32_t width_;
  uint32_t height_;
  ssize_t img_byte_size_;
  std::mutex mut_;
};
} // namespace carbon::deck_transform
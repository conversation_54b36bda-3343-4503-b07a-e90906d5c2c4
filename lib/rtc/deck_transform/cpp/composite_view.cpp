#include "lib/rtc/deck_transform/cpp/composite_view.hpp"

#include <thread>
#include <unistd.h>
#include <unordered_map>

#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>

#include <fmt/format.h>
#include <spdlog/spdlog.h>
static cv::Rect reused_img_box(25, 25, 50, 50);
static cv::<PERSON><PERSON><PERSON> reused_img_color(128, 0, 128);

namespace carbon::deck_transform {
Pane::Pane(const std::string &id, std::shared_ptr<TransformPipeline> pipeline, cv::Rect global_pos, uint32_t view_width,
           uint32_t view_height, const std::vector<PxPos> &mask)
    : id_(id), pipeline_(pipeline), global_pos_(global_pos), oob_(false), view_width_(view_width),
      view_height_(view_height), prev_img_(nullptr), prev_img_drawn_(false) {
  if ((global_pos_.width + global_pos.x > static_cast<int>(view_width_)) ||
      (global_pos_.height + global_pos.y > static_cast<int>(view_height_))) {
    spdlog::info("Pane is out of bounds");
    oob_ = true;
  }
  if (mask.size() >= 3) {
    std::vector<cv::Point> polygon;
    for (const auto &pt : mask) {
      polygon.push_back(cv::Point(std::get<0>(pt), std::get<1>(pt)));
    }
    mask_ = cv::Mat(global_pos.height, global_pos.width, CV_8UC1, cv::Scalar(0));
    cv::fillPoly(*mask_, polygon, 255);
  }
}
void Pane::update_pos(cv::Rect global_pos) {
  global_pos_ = global_pos;
  if ((global_pos_.width + global_pos.x > static_cast<int>(view_width_)) ||
      (global_pos_.height + global_pos.y > static_cast<int>(view_height_))) {
    spdlog::info("Pane is out of bounds");
    oob_ = true;
  }
}
void Pane::write(cv::Mat output, std::chrono::milliseconds timeout) {
  if (oob_) {
    return;
  }
  auto img = pipeline_->get_next(timeout);
  cv::Mat *raw_img = img.get();
  auto roi = output(global_pos_);
  if (!img) {
    if (prev_img_) {
      if (!prev_img_drawn_) {
        cv::rectangle(*prev_img_, reused_img_box, reused_img_color, -1);
        prev_img_drawn_ = true;
      }
      raw_img = prev_img_.get();
    } else {
      return;
    }
  } else {
    prev_img_.swap(img);
    prev_img_drawn_ = false;
  }
  if (!mask_) {
    raw_img->copyTo(roi);
  } else {
    raw_img->copyTo(roi, *mask_);
  }
}

CompositeView::CompositeView(int fd, uint32_t width, uint32_t height)
    : fd_(fd), width_(width), height_(height), img_byte_size_(width * height * 3) {}
void CompositeView::add_pane(const std::string &id, std::shared_ptr<TransformPipeline> pipeline, uint32_t top_left_x,
                             uint32_t top_left_y, uint32_t width, uint32_t height, const std::vector<PxPos> &mask) {
  std::unique_lock<std::mutex> lk(mut_);
  panes_.emplace_back(
      std::make_shared<Pane>(id, pipeline, cv::Rect(top_left_x, top_left_y, width, height), width_, height_, mask));
}
void CompositeView::update_pane(const std::string &id, uint32_t top_left_x, uint32_t top_left_y, uint32_t width,
                                uint32_t height) {
  for (auto &pane : panes_) {
    if (pane->id() == id) {
      pane->update_pos(cv::Rect(top_left_x, top_left_y, width, height));
      return;
    }
  }
  spdlog::warn("No pane with id {} found in composite view", id);
}
void CompositeView::start() {
  for (auto &pane : panes_) {
    pane->start();
  }
}
void CompositeView::stop() {
  for (auto &pane : panes_) {
    pane->stop();
  }
}
void CompositeView::render() {
  cv::Mat m = cv::Mat::zeros(height_, width_, CV_8UC3);
  auto now = std::chrono::system_clock::now();
  std::chrono::milliseconds remainder(100); // Run at least at 10hz no matter what
  {
    std::unique_lock<std::mutex> lk(mut_);
    for (auto &pane : panes_) {
      if (!pane) {
        continue;
      }
      pane->write(m, remainder);
      auto tmp = now;
      now = std::chrono::system_clock::now();
      remainder -= std::chrono::duration_cast<std::chrono::milliseconds>(now - tmp);
      if (remainder <= std::chrono::milliseconds(0)) {
        remainder = std::chrono::milliseconds(1);
      }
    }
  }
  auto time_str =
      fmt::format("{}", std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count());

  const auto font = cv::FONT_HERSHEY_COMPLEX_SMALL;
  const double font_scale = 1;
  const int thickness = 1;
  const uint32_t padding = 5;

  int baseline = 0;
  auto text_size = cv::getTextSize(time_str, font, font_scale, thickness, &baseline);

  cv::putText(m, time_str, cv::Point(width_ - text_size.width - padding, text_size.height + padding), font, font_scale,
              CV_RGB(0, 0, 0), thickness + 2);
  cv::putText(m, time_str, cv::Point(width_ - text_size.width - padding, text_size.height + padding), font, font_scale,
              CV_RGB(255, 219, 0), thickness);

  auto written = write(fd_, m.data, img_byte_size_);
  if (written < img_byte_size_) {
    spdlog::warn("Only wrote {} of {} bytes to video device", written, img_byte_size_);
  }
}
void CompositeView::update_order(const std::vector<std::string> ordered_ids) {
  if (ordered_ids.size() != panes_.size()) {
    spdlog::error("Cannot reload order when sizes dont match");
    return;
  }
  bool match = true;
  for (size_t i = 0; i < ordered_ids.size(); ++i) {
    if (ordered_ids[i] != panes_[i]->id()) {
      match = false;
      break;
    }
  }
  if (match) {
    return; // order did not change
  }
  std::unique_lock<std::mutex> lk(mut_);
  std::unordered_map<std::string, std::shared_ptr<Pane>> tmp;
  for (auto p : panes_) {
    tmp.emplace(p->id(), p);
  }
  panes_.clear();
  for (const auto &id : ordered_ids) {
    panes_.emplace_back(tmp[id]);
  }
}
} // namespace carbon::deck_transform
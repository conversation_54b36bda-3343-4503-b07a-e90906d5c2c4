#include "lib/rtc/deck_transform/cpp/rotate_transform.hpp"

#include <opencv2/cudawarping.hpp>

namespace carbon::deck_transform {
cv::cuda::GpuMat RotateTransform::transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) {
  cv::cuda::GpuMat out;
  cv::cuda::rotate(img, out, cv::Size(width_, height_), angle_, shift_x_, shift_y_, cv::INTER_NEAREST, stream);
  return out;
}
} // namespace carbon::deck_transform
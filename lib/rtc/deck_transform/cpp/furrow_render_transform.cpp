#include "lib/rtc/deck_transform/cpp/furrow_render_transform.hpp"

#include <fmt/format.h>
#include <opencv2/imgproc.hpp>
#include <spdlog/spdlog.h>

#include <config/client/cpp/config_subscriber.hpp>
#include <cv/deck/client/cpp/grpc_client.h>

constexpr std::chrono::milliseconds furrow_timeout(0);
namespace carbon::deck_transform {
FurrowRenderTransform::FurrowRenderTransform(const std::string &camera_id)
    : client_(std::make_unique<cv::deck::DeckCvClient>()), camera_id_(camera_id), last_ts_(0), furrow_cat_("FURROW"),
      furrow_cat_cfg_(config::get_global_config_subscriber()->get_config_node("common", "furrow_model_category_id"),
                      false) {}

cv::Mat <PERSON>RenderTransform::transform(cv::Mat img) {
  auto resp = client_->get_next_furrow_detection(last_ts_, camera_id_, furrow_timeout);
  if (!resp) {
    return img;
  }
  if (furrow_cat_cfg_.reload_required()) {
    if (furrow_cat_cfg_.tree()) {
      furrow_cat_ = furrow_cat_cfg_->get_value<std::string>();
    }
  }
  for (const auto &furrow : resp->furrows()) {
    if (furrow.category() == furrow_cat_) {
      cv::Point start(static_cast<int>(furrow.start_x() * static_cast<float>(img.cols)),
                      static_cast<int>(furrow.start_y() * static_cast<float>(img.rows)));

      cv::Point end(static_cast<int>(furrow.end_x() * static_cast<float>(img.cols)),
                    static_cast<int>(furrow.end_y() * static_cast<float>(img.rows)));

      cv::line(img, start, end, cv::Scalar(255, 0, 0), 4);
    }
  }
  return img;
}
} // namespace carbon::deck_transform
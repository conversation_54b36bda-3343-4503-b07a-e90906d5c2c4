#include "lib/rtc/deck_transform/cpp/transform_pipeline.hpp"

#include <opencv2/core/cuda.hpp>

#include <lib/drivers/deck_cam/cpp/camera.hpp>

constexpr std::chrono::milliseconds cam_timeout(100);

namespace carbon::deck_transform {
std::unique_ptr<cv::Mat> TransformPipeline::transform(cv::Mat input) {
  auto img = input.clone();
  for (auto t : pre_) {
    img = t->transform(img);
  }
  if (!cuda_.empty()) {
    c10::cuda::CUDAStreamGuard stream_guard(*stream_);
    cv::cuda::Stream cv_stream = cv::cuda::StreamAccessor::wrapStream(stream_->stream());
    cv::cuda::GpuMat gimg;
    gimg.upload(img, cv_stream);
    for (auto t : cuda_) {
      gimg = t->transform(gimg, cv_stream);
    }
    gimg.download(img, cv_stream);
  }
  for (auto t : post_) {
    img = t->transform(img);
  }
  return std::make_unique<cv::Mat>(img);
}
std::unique_ptr<cv::Mat> TransformPipeline::get_next(std::chrono::milliseconds timeout) {
  std::unique_ptr<cv::Mat> tmp(nullptr);
  std::unique_lock<std::mutex> lk(this->mut_);
  if (next_ == nullptr) {
    this->cv_.wait_for(lk, timeout, [&] { return this->next_ != nullptr; });
  }
  next_.swap(tmp);
  return tmp;
}
void TransformPipeline::start() {
  running_.store(true);
  thread_ = std::thread(std::bind(&TransformPipeline::run, this));
}
void TransformPipeline::stop() {
  running_.store(false);
  thread_.join();
}
void TransformPipeline::run() {
  std::chrono::milliseconds timeout(100);
  auto last_ts = std::chrono::system_clock::now() - std::chrono::seconds(1); // Want an image can be old
  while (running_) {
    auto next = cam_->get_next(last_ts, timeout);
    if (!next) {
      continue;
    }
    last_ts = next->cap_time;

    auto transformed = transform(next->img);
    if (transformed) {
      std::lock_guard lg(mut_);
      next_.swap(transformed);
      cv_.notify_all();
    }
  }
}

} // namespace carbon::deck_transform
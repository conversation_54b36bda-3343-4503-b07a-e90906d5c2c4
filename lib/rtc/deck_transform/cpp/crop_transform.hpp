#pragma once
#include "lib/rtc/deck_transform/cpp/transform.hpp"

namespace carbon::deck_transform {
class CropTransform : public CudaTransform {
public:
  CropTransform(uint32_t top_left_x, uint32_t top_left_y, uint32_t width, uint32_t height)
      : roi_(top_left_x, top_left_y, width, height) {}
  virtual cv::cuda::GpuMat transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) override;

private:
  const cv::Rect roi_;
};
} // namespace carbon::deck_transform
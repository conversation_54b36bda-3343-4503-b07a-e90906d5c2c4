#include "lib/rtc/deck_transform/cpp/fisheye_transform.hpp"

#include <opencv2/core.hpp>
#include <opencv2/cudawarping.hpp>
#include <opencv2/imgproc.hpp>

#include <fmt/format.h>
#include <spdlog/spdlog.h>
namespace carbon::deck_transform {
FisheyeTransform::FisheyeTransform(uint32_t width, uint32_t height, cv::Mat remap_x, cv::Mat remap_y)
    : width_(width), height_(height) {
  remap_x_.upload(remap_x);
  remap_y_.upload(remap_y);
}
cv::cuda::GpuMat FisheyeTransform::transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) {
  cv::cuda::GpuMat out;
  cv::cuda::remap(img, out, remap_x_, remap_y_, cv::INTER_LINEAR, cv::BORDER_CONSTANT, cv::Scalar(), stream);
  return out;
}

} // namespace carbon::deck_transform
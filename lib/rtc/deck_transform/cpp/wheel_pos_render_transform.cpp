#include "lib/rtc/deck_transform/cpp/wheel_pos_render_transform.hpp"

#include <mutex>
#include <optional>
#include <unordered_map>

#include <fmt/format.h>
#include <opencv2/imgproc.hpp>
#include <spdlog/spdlog.h>

namespace carbon::deck_transform {
class WheelPosOwner {
public:
  static WheelPosOwner &get() {
    static WheelPosOwner inst;
    return inst;
  }
  void set_pos_for_cam(const std::string &camera_id, uint32_t top_left_x, uint32_t top_left_y, uint32_t bottom_right_x,
                       uint32_t bottom_right_y);
  std::optional<cv::Rect> get_pos_for_cam(const std::string &camera_id);

private:
  WheelPosOwner() {}
  WheelPosOwner(const WheelPosOwner &) = delete;
  WheelPosOwner &operator=(const WheelPosOwner &) = delete;
  std::mutex mut_;
  std::unordered_map<std::string, cv::Rect> positions_;
};
void WheelPosOwner::set_pos_for_cam(const std::string &camera_id, uint32_t top_left_x, uint32_t top_left_y,
                                    uint32_t bottom_right_x, uint32_t bottom_right_y) {
  std::unique_lock<std::mutex> lk(mut_);
  positions_[camera_id] = cv::Rect(cv::Point(static_cast<int>(top_left_x), static_cast<int>(top_left_y)),
                                   cv::Point(static_cast<int>(bottom_right_x), static_cast<int>(bottom_right_y)));
}
std::optional<cv::Rect> WheelPosOwner::get_pos_for_cam(const std::string &camera_id) {
  std::unique_lock<std::mutex> lk(mut_);
  auto it = positions_.find(camera_id);
  if (it == positions_.end()) {
    return std::nullopt;
  }
  return it->second;
}

WheelPosRenderTransform::WheelPosRenderTransform(const std::string &camera_id, uint8_t red, uint8_t green, uint8_t blue,
                                                 int thickness)
    : camera_id_(camera_id), color_(red, green, blue), thickness_(thickness) {}

cv::Mat WheelPosRenderTransform::transform(cv::Mat img) {
  auto opt_rect = WheelPosOwner::get().get_pos_for_cam(camera_id_);
  if (!opt_rect) {
    spdlog::warn("No wheel position set for {}, cannot render data", camera_id_);
  } else {
    cv::rectangle(img, *opt_rect, color_, thickness_);
  }
  return img;
}
void WheelPosRenderTransform::set_pos_for_cam(const std::string &camera_id, uint32_t top_left_x, uint32_t top_left_y,
                                              uint32_t bottom_right_x, uint32_t bottom_right_y) {
  WheelPosOwner::get().set_pos_for_cam(camera_id, top_left_x, top_left_y, bottom_right_x, bottom_right_y);
}
} // namespace carbon::deck_transform
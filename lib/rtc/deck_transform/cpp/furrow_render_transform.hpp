#pragma once
#include "lib/rtc/deck_transform/cpp/transform.hpp"

#include <memory>
#include <string>

#include <config/tree/cpp/config_scoped_callback.hpp>

namespace cv::deck {
class DeckCvClient;
}
namespace carbon::deck_transform {
class FurrowRenderTransform : public Transform {
public:
  FurrowRenderTransform(const std::string &camera_id);
  virtual cv::Mat transform(cv::Mat img) override;

private:
  std::unique_ptr<cv::deck::DeckCvClient> client_;
  const std::string camera_id_;
  int64_t last_ts_;
  std::string furrow_cat_;
  config::AtomicFlagConfigScopedCallback furrow_cat_cfg_;
};
} // namespace carbon::deck_transform
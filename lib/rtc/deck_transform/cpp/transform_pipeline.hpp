#pragma once
#include <atomic>
#include <chrono>
#include <condition_variable>
#include <memory>
#include <mutex>
#include <thread>
#include <vector>

#include <c10/cuda/CUDAGuard.h>
#include <opencv2/core/cuda_stream_accessor.hpp>
#include <opencv2/core/mat.hpp>

#include <lib/rtc/deck_transform/cpp/transform.hpp>

namespace carbon::deck_cam {
class Camera;
}
namespace carbon::deck_transform {
class TransformPipeline {
public:
  TransformPipeline(std::shared_ptr<deck_cam::Camera> cam)
      : cam_(cam), stream_(new c10::cuda::CUDAStream(c10::cuda::getStreamFromPool(true, 0))), next_(nullptr) {}
  void add_pre_cuda_transform(std::shared_ptr<Transform> t) { pre_.emplace_back(t); }
  void add_post_cuda_transform(std::shared_ptr<Transform> t) { post_.emplace_back(t); }
  void add_cuda_transform(std::shared_ptr<CudaTransform> t) { cuda_.emplace_back(t); }
  std::unique_ptr<cv::Mat> get_next(std::chrono::milliseconds timeout);
  void start();
  void stop();
  std::shared_ptr<deck_cam::Camera> cam() { return cam_; }

private:
  std::unique_ptr<cv::Mat> transform(cv::Mat img);
  void run();
  std::shared_ptr<deck_cam::Camera> cam_;
  std::unique_ptr<c10::cuda::CUDAStream> stream_;
  std::vector<std::shared_ptr<Transform>> pre_;
  std::vector<std::shared_ptr<Transform>> post_;
  std::vector<std::shared_ptr<CudaTransform>> cuda_;
  std::unique_ptr<cv::Mat> next_;
  std::mutex mut_;
  std::condition_variable cv_;
  std::atomic_bool running_;

  std::thread thread_;
};
} // namespace carbon::deck_transform
#pragma once
#include "lib/rtc/deck_transform/cpp/transform.hpp"

namespace carbon::deck_transform {
class FisheyeTransform : public CudaTransform {
public:
  FisheyeTransform(uint32_t width, uint32_t height, cv::Mat remap_x, cv::<PERSON> remap_y);
  virtual cv::cuda::GpuMat transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) override;

private:
  const uint32_t width_;
  const uint32_t height_;
  cv::cuda::GpuMat remap_x_;
  cv::cuda::GpuMat remap_y_;
};
} // namespace carbon::deck_transform
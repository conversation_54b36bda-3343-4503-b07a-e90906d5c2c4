#pragma once
#include <opencv2/core/cuda.hpp>
#include <opencv2/core/mat.hpp>

namespace carbon::deck_cam {
class Camera;
}
namespace carbon::deck_transform {
using PxPos = std::tuple<uint32_t, uint32_t>;
class Transform {
public:
  virtual cv::Mat transform(cv::Mat img) = 0;
};
class CudaTransform {
public:
  virtual cv::cuda::GpuMat transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) = 0;
};
} // namespace carbon::deck_transform
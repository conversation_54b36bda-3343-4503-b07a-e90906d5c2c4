#pragma once
#include "lib/rtc/deck_transform/cpp/transform.hpp"

namespace carbon::deck_transform {
class ResizeTransform : public CudaTransform {
public:
  ResizeTransform(uint32_t width, uint32_t height) : width_(width), height_(height) {}
  virtual cv::cuda::GpuMat transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) override;

private:
  const uint32_t width_;
  const uint32_t height_;
};
} // namespace carbon::deck_transform
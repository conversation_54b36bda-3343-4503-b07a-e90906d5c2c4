#pragma once
#include "lib/rtc/deck_transform/cpp/transform.hpp"

namespace carbon::deck_transform {
enum RotationDir { ROTATE_90_CLOCKWISE = 0, ROTATE_90_COUNTERCLOCKWISE = 1 };
class RotateTransform : public CudaTransform {
public:
  RotateTransform(RotationDir dir, uint32_t width, uint32_t height)
      : width_(width), height_(height), shift_x_(dir == ROTATE_90_COUNTERCLOCKWISE ? width : 0.0),
        shift_y_(dir == ROTATE_90_CLOCKWISE ? height : 0.0), angle_(dir == ROTATE_90_CLOCKWISE ? 90 : -90) {}
  virtual cv::cuda::GpuMat transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) override;

private:
  const uint32_t width_;
  const uint32_t height_;
  double shift_x_;
  double shift_y_;
  double angle_;
};
} // namespace carbon::deck_transform
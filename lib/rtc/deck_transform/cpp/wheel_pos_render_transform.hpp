#pragma once
#include "lib/rtc/deck_transform/cpp/transform.hpp"

#include <memory>
#include <opencv2/imgproc.hpp>

namespace carbon::deck_transform {
class WheelPosRenderTransform : public Transform {
public:
  WheelPosRenderTransform(const std::string &camera_id, uint8_t red, uint8_t green, uint8_t blue, int thickness);
  virtual cv::Mat transform(cv::Mat img) override;
  static void set_pos_for_cam(const std::string &camera_id, uint32_t top_left_x, uint32_t top_left_y,
                              uint32_t bottom_right_x, uint32_t bottom_right_y);

private:
  const std::string camera_id_;
  cv::Scalar color_;
  int thickness_;
};
} // namespace carbon::deck_transform
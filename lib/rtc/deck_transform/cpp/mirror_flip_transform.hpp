#pragma once
#include "lib/rtc/deck_transform/cpp/transform.hpp"

namespace carbon::deck_transform {
enum MirrorFlipDir {
  FLIP_UP_DOWN = 0,
  MIRROR_LEFT_RIGHT = 1,
};
class MirrorFlipTransform : public CudaTransform {
public:
  MirrorFlipTransform(MirrorFlipDir dir) : dir_(dir) {}
  virtual cv::cuda::GpuMat transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) override;

private:
  const MirrorFlipDir dir_;
};
} // namespace carbon::deck_transform
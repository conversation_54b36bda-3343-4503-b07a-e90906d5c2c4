from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Tuple, Union

import numpy as np


class TransformConfig(ABC):
    @abstractmethod
    def validate(self) -> bool:
        ...

    def calc_shape(self, shape: Tuple[int, int]) -> <PERSON><PERSON>[int, int]:
        return shape


@dataclass
class FisheyeRectConfig(TransformConfig):
    origin: Tuple[float, float, float]
    x: Tuple[float, float, float]
    y: Tuple[float, float, float]
    width: float
    window_width: int  # This is the size of the window used to set this
    window_height: int

    def validate(self) -> bool:
        if self.width <= 0.0 or self.window_height <= 0 or self.window_width <= 0:
            return False
        tmp_orig = np.array(self.origin)
        if np.linalg.norm(np.array(self.y) - tmp_orig) <= 0.0 or np.linalg.norm(np.array(self.x) - tmp_orig) <= 0.0:
            return False
        return True

    def calc_shape(self, shape: Tuple[int, int]) -> <PERSON><PERSON>[int, int]:
        origin = np.array(self.origin, dtype=np.float32)
        x = np.array(self.x, dtype=np.float32)
        y = np.array(self.y, dtype=np.float32)

        out_width = int(round(self.width * self.window_width))
        out_height = int(
            round(self.width * np.linalg.norm(y - origin) / np.linalg.norm(x - origin) * self.window_width)
        )
        return (out_width, out_height)


@dataclass
class EulerFisheyeConfig(TransformConfig):
    """
    Construction:

      - `pitch`, `yaw`, and `zoom` define a center point "C" in spherical
        coordinates;
      - `tangent_plane_translation` and `tangent_plane_size` define a rectangle
        in the plane tangent to the sphere with origin at "C", operating in a
        local East/North coordinate system;
      - `roll` rotates that rectangle about its normal;
      - if `mirror`, the rectangle is reflected horizontally; if `flip`, it is
        reflected vertically.

    That final rectangle is the rectangle that the user "looks through".
    """

    roll: float
    pitch: float
    yaw: float
    zoom: float
    configured_view_width: int  # set by backend used to maintain consistent size even if view changes shape
    mirror: bool
    flip: bool
    tangent_plane_translation: Tuple[float, float] = (0.0, 0.0)
    tangent_plane_size: Tuple[float, float] = (1.0, 1.0)

    def validate(self) -> bool:
        width, height = self.tangent_plane_size
        if width <= 0.0 or height <= 0.0 or self.configured_view_width <= 0 or self.zoom <= 0.0:
            return False
        return True

    def calc_shape(self, shape: Tuple[int, int]) -> Tuple[int, int]:
        width = self.tangent_plane_size[0] * self.configured_view_width
        height = self.tangent_plane_size[1] * self.configured_view_width
        return (int(round(width)), int(round(height)))


@dataclass
class Mirror(TransformConfig):
    pass

    def validate(self) -> bool:
        return True


@dataclass
class Flip(TransformConfig):
    pass

    def validate(self) -> bool:
        return True


@dataclass
class Rotate(TransformConfig):
    rotation: int

    def validate(self) -> bool:
        return abs(self.rotation) == 90

    def calc_shape(self, shape: Tuple[int, int]) -> Tuple[int, int]:
        return (shape[1], shape[0])


@dataclass
class Crop(TransformConfig):
    start_x: float
    start_y: float
    width: float
    height: float

    def validate(self) -> bool:
        return self.start_x >= 0.0 and self.start_y >= 0.0 and self.width > 0.0 and self.height > 0.0

    def calc_shape(self, shape: Tuple[int, int]) -> Tuple[int, int]:
        return (int(round(self.width * shape[0])), int(round(self.height * shape[1])))


@dataclass
class Resize(TransformConfig):
    width: float
    height: float

    def validate(self) -> bool:
        return self.width > 0.0 and self.height > 0.0

    def calc_shape(self, shape: Tuple[int, int]) -> Tuple[int, int]:
        return (int(round(self.width * shape[0])), int(round(self.height * shape[1])))


@dataclass
class RgbdToViewable(TransformConfig):
    to_depth: bool  # if true use depth channel else use color channels

    def validate(self) -> bool:
        return True


@dataclass
class WheelPosViz(TransformConfig):
    rgb_color: Tuple[int, int, int]
    thickness: int  # negative means filled else thickness of line, opencv standard

    def validate(self) -> bool:
        return True


@dataclass
class Undistort(TransformConfig):
    pass

    def validate(self) -> bool:
        return True


@dataclass
class Point2f:
    x: float
    y: float

    def validate(self) -> bool:
        return self.x >= 0 and self.y >= 0


@dataclass
class PerspectiveShift(TransformConfig):
    top_left: Point2f
    top_right: Point2f
    bottom_left: Point2f
    bottom_right: Point2f
    width_mult: float
    height_mult: float

    def validate(self) -> bool:
        return (
            self.top_left.validate()
            and self.top_right.validate()
            and self.bottom_left.validate()
            and self.bottom_right.validate()
            and self.width_mult > 0
            and self.height_mult > 0
        )

    def calc_shape(self, shape: Tuple[int, int]) -> Tuple[int, int]:
        return (int(round(self.width_mult * shape[0])), int(round(self.height_mult * shape[1])))


TransformType = Union[
    Mirror,
    Flip,
    Crop,
    Resize,
    Rotate,
    FisheyeRectConfig,
    EulerFisheyeConfig,
    RgbdToViewable,
    WheelPosViz,
    PerspectiveShift,
    Undistort,
]

RequiredFirstTransforms = (
    RgbdToViewable,
    Undistort,
)  # Transforms that must be absolutely first if used. only 1 can be used per window
MetadataTransforms = (WheelPosViz,)  # Adds metadata to image, so must be before all ModifyTransforms
ModifyTransforms = (
    Mirror,
    Flip,
    Crop,
    Resize,
    Rotate,
    FisheyeRectConfig,
    EulerFisheyeConfig,
    PerspectiveShift,
    Undistort,
)  # transforms that modify the base image

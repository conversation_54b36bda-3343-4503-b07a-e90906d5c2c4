#pragma once
#include <atomic>
#include <functional>
#include <optional>
#include <unordered_map>

#include <config/tree/cpp/config_tree.hpp>
#include <lib/common/cpp/utils/thread_safe_moving_average.hpp>
#include <lib/common/geometric/cpp/geometric_scanner.hpp>
#include <trajectory/cpp/trajectory.hpp>

/*
This is a shim class as scanner is currently a python class, and need to access it from CPP
eventually when we move the scanner to CPP we can use it directly. For now only implementing
needed functionality.
*/

namespace carbon {
namespace scanner {

using gimbal_limit_func = std::function<std::tuple<int, int>(void)>;
class ScannerWrapper {
public:
  ScannerWrapper(uint32_t id, std::shared_ptr<lib::common::geometric::GeometricScanner> geo_scanner,
                 gimbal_limit_func pan_lmts, gimbal_limit_func tilt_lmts,
                 std::shared_ptr<carbon::config::ConfigTree> conf_tree);
  ~ScannerWrapper();
  inline uint32_t id() const { return id_; }
  inline std::shared_ptr<lib::common::geometric::GeometricScanner> geo_scanner() { return geo_scanner_; }
  std::tuple<int, int> pan_limits();
  std::tuple<int, int> tilt_limits();
  int pan_middle();
  int tilt_middle();
  void await_weed_available();
  void await_target_change();
  void notify_target_change();
  void set_next(std::shared_ptr<trajectory::Trajectory> traj);
  void clear_next_if_equal(std::shared_ptr<trajectory::Trajectory> traj);
  void use_next(uint32_t time);
  inline std::shared_ptr<trajectory::Trajectory> get_target() const {
    const std::unique_lock<std::mutex> lk(mut_);
    return current_target_;
  }
  inline std::shared_ptr<trajectory::Trajectory> get_next_target() const {
    const std::unique_lock<std::mutex> lk(mut_);
    return next_target_;
  }
  inline uint32_t get_target_start_time() const { return target_time_; }
  bool enabled() const { return enabled_; }
  void enable(bool on);
  void limit_reset_callback();
  std::tuple<int, int> get_crosshair();
  void set_crosshair(std::tuple<int, int> crosshair);
  void add_tilt_start_point(int32_t start_point_ticks);
  float avg_tilt_start_point();
  bool traj_is_in_lane(const trajectory::TrackedItemCentroid &centroid);

  inline void set_tilt_offests(int32_t front, int32_t back) {
    tilt_offset_back_ = back;
    tilt_offset_front_ = front;
  }
  void stop();
  inline bool in_use() { return in_use_; }
  void cancel();
  float power() const;
  static std::string make_identifier(uint32_t id);
  inline const std::string &identifier() const { return identifier_; }
  inline int32_t tilt_offset_front() const { return tilt_offset_front_; }
  inline int32_t tilt_offset_back() const { return tilt_offset_back_; }
  inline float avg_height() const { return avg_height_.avg(); }

private:
  const uint32_t id_;
  const std::string identifier_;
  std::shared_ptr<lib::common::geometric::GeometricScanner> geo_scanner_;
  gimbal_limit_func pan_limits_func_;
  gimbal_limit_func tilt_limits_func_;
  std::optional<std::tuple<int, int>> pan_limits_;
  std::optional<std::tuple<int, int>> tilt_limits_;
  int pan_middle_;
  int tilt_middle_;
  std::mutex cv_await_mut_;
  std::condition_variable cv_await_;
  std::mutex cv_shooting_mut_;
  std::condition_variable cv_shooting_;
  std::shared_ptr<trajectory::Trajectory> current_target_;
  std::shared_ptr<trajectory::Trajectory> next_target_;
  uint32_t target_time_;
  std::shared_ptr<carbon::config::ConfigTree> conf_tree_;
  std::tuple<int, int> crosshair_;
  std::atomic<int32_t> tilt_offset_front_;
  std::atomic<int32_t> tilt_offset_back_;
  carbon::common::ThreadSafeMovingAverage<float> avg_;
  carbon::common::ThreadSafeMovingAverage<float> avg_height_;
  std::atomic<bool> in_use_;
  std::atomic<bool> target_changed_;
  std::atomic<bool> enabled_;
  std::atomic<float> power_;
  mutable std::mutex mut_;

  void callback();
  void start();
};
class ScannerWrapperOwner {
public:
  ScannerWrapperOwner(const ScannerWrapperOwner &we) = delete;
  ScannerWrapperOwner &operator=(const ScannerWrapperOwner &) = delete;
  static ScannerWrapperOwner &get();
  void set(const std::vector<std::shared_ptr<ScannerWrapper>> &scanners);
  const std::unordered_map<uint32_t, std::shared_ptr<ScannerWrapper>> &get_scanners() const;
  const std::shared_ptr<ScannerWrapper> get_scanner(uint32_t id) const;

private:
  mutable std::mutex mut_;
  bool set_;
  std::unordered_map<uint32_t, std::shared_ptr<ScannerWrapper>> scanners_;
  ScannerWrapperOwner();
  bool check_exists() const;
};
} // namespace scanner
} // namespace carbon
# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: calibration/proto/color_calibration.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='calibration/proto/color_calibration.proto',
  package='calibration.proto',
  syntax='proto3',
  serialized_options=b'Z\021proto/calibration',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n)calibration/proto/color_calibration.proto\x12\x11\x63\x61libration.proto\"\x07\n\x05\x45mpty\"+\n\x07Request\x12\x0e\n\x06row_id\x18\x01 \x01(\x03\x12\x10\n\x08row_type\x18\x02 \x01(\t\"\x1b\n\x08Response\x12\x0f\n\x07success\x18\x01 \x01(\x08\x32\xa3\x03\n\x17\x43olorCalibrationService\x12W\n\x1aInitializeColorCalibration\x12\x1a.calibration.proto.Request\x1a\x1b.calibration.proto.Response\"\x00\x12P\n\x13\x43\x65nterTargetCameras\x12\x1a.calibration.proto.Request\x1a\x1b.calibration.proto.Response\"\x00\x12P\n\x13SetAutoWhitebalance\x12\x1a.calibration.proto.Request\x1a\x1b.calibration.proto.Response\"\x00\x12I\n\x0cSaveToConfig\x12\x1a.calibration.proto.Request\x1a\x1b.calibration.proto.Response\"\x00\x12@\n\x05Reset\x12\x18.calibration.proto.Empty\x1a\x1b.calibration.proto.Response\"\x00\x42\x13Z\x11proto/calibrationb\x06proto3'
)




_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='calibration.proto.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=64,
  serialized_end=71,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='calibration.proto.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_id', full_name='calibration.proto.Request.row_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_type', full_name='calibration.proto.Request.row_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=73,
  serialized_end=116,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='calibration.proto.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='calibration.proto.Response.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=118,
  serialized_end=145,
)

DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'calibration.proto.color_calibration_pb2'
  # @@protoc_insertion_point(class_scope:calibration.proto.Empty)
  })
_sym_db.RegisterMessage(Empty)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'calibration.proto.color_calibration_pb2'
  # @@protoc_insertion_point(class_scope:calibration.proto.Request)
  })
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), {
  'DESCRIPTOR' : _RESPONSE,
  '__module__' : 'calibration.proto.color_calibration_pb2'
  # @@protoc_insertion_point(class_scope:calibration.proto.Response)
  })
_sym_db.RegisterMessage(Response)


DESCRIPTOR._options = None

_COLORCALIBRATIONSERVICE = _descriptor.ServiceDescriptor(
  name='ColorCalibrationService',
  full_name='calibration.proto.ColorCalibrationService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=148,
  serialized_end=567,
  methods=[
  _descriptor.MethodDescriptor(
    name='InitializeColorCalibration',
    full_name='calibration.proto.ColorCalibrationService.InitializeColorCalibration',
    index=0,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CenterTargetCameras',
    full_name='calibration.proto.ColorCalibrationService.CenterTargetCameras',
    index=1,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetAutoWhitebalance',
    full_name='calibration.proto.ColorCalibrationService.SetAutoWhitebalance',
    index=2,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SaveToConfig',
    full_name='calibration.proto.ColorCalibrationService.SaveToConfig',
    index=3,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='Reset',
    full_name='calibration.proto.ColorCalibrationService.Reset',
    index=4,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_COLORCALIBRATIONSERVICE)

DESCRIPTOR.services_by_name['ColorCalibrationService'] = _COLORCALIBRATIONSERVICE

# @@protoc_insertion_point(module_scope)

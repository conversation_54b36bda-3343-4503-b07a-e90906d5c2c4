// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: calibration/proto/color_calibration.proto

#include "calibration/proto/color_calibration.pb.h"
#include "calibration/proto/color_calibration.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace calibration {
namespace proto {

static const char* ColorCalibrationService_method_names[] = {
  "/calibration.proto.ColorCalibrationService/InitializeColorCalibration",
  "/calibration.proto.ColorCalibrationService/CenterTargetCameras",
  "/calibration.proto.ColorCalibrationService/SetAutoWhitebalance",
  "/calibration.proto.ColorCalibrationService/SaveToConfig",
  "/calibration.proto.ColorCalibrationService/Reset",
};

std::unique_ptr< ColorCalibrationService::Stub> ColorCalibrationService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ColorCalibrationService::Stub> stub(new ColorCalibrationService::Stub(channel, options));
  return stub;
}

ColorCalibrationService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_InitializeColorCalibration_(ColorCalibrationService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CenterTargetCameras_(ColorCalibrationService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetAutoWhitebalance_(ColorCalibrationService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SaveToConfig_(ColorCalibrationService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_Reset_(ColorCalibrationService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ColorCalibrationService::Stub::InitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) {
  return ::grpc::internal::BlockingUnaryCall< ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_InitializeColorCalibration_, context, request, response);
}

void ColorCalibrationService::Stub::async::InitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_InitializeColorCalibration_, context, request, response, std::move(f));
}

void ColorCalibrationService::Stub::async::InitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_InitializeColorCalibration_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* ColorCalibrationService::Stub::PrepareAsyncInitializeColorCalibrationRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::calibration::proto::Response, ::calibration::proto::Request, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_InitializeColorCalibration_, context, request);
}

::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* ColorCalibrationService::Stub::AsyncInitializeColorCalibrationRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncInitializeColorCalibrationRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ColorCalibrationService::Stub::CenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) {
  return ::grpc::internal::BlockingUnaryCall< ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CenterTargetCameras_, context, request, response);
}

void ColorCalibrationService::Stub::async::CenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CenterTargetCameras_, context, request, response, std::move(f));
}

void ColorCalibrationService::Stub::async::CenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CenterTargetCameras_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* ColorCalibrationService::Stub::PrepareAsyncCenterTargetCamerasRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::calibration::proto::Response, ::calibration::proto::Request, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CenterTargetCameras_, context, request);
}

::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* ColorCalibrationService::Stub::AsyncCenterTargetCamerasRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCenterTargetCamerasRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ColorCalibrationService::Stub::SetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) {
  return ::grpc::internal::BlockingUnaryCall< ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetAutoWhitebalance_, context, request, response);
}

void ColorCalibrationService::Stub::async::SetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetAutoWhitebalance_, context, request, response, std::move(f));
}

void ColorCalibrationService::Stub::async::SetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetAutoWhitebalance_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* ColorCalibrationService::Stub::PrepareAsyncSetAutoWhitebalanceRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::calibration::proto::Response, ::calibration::proto::Request, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetAutoWhitebalance_, context, request);
}

::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* ColorCalibrationService::Stub::AsyncSetAutoWhitebalanceRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetAutoWhitebalanceRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ColorCalibrationService::Stub::SaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) {
  return ::grpc::internal::BlockingUnaryCall< ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SaveToConfig_, context, request, response);
}

void ColorCalibrationService::Stub::async::SaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveToConfig_, context, request, response, std::move(f));
}

void ColorCalibrationService::Stub::async::SaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveToConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* ColorCalibrationService::Stub::PrepareAsyncSaveToConfigRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::calibration::proto::Response, ::calibration::proto::Request, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SaveToConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* ColorCalibrationService::Stub::AsyncSaveToConfigRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSaveToConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ColorCalibrationService::Stub::Reset(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::calibration::proto::Response* response) {
  return ::grpc::internal::BlockingUnaryCall< ::calibration::proto::Empty, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Reset_, context, request, response);
}

void ColorCalibrationService::Stub::async::Reset(::grpc::ClientContext* context, const ::calibration::proto::Empty* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::calibration::proto::Empty, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Reset_, context, request, response, std::move(f));
}

void ColorCalibrationService::Stub::async::Reset(::grpc::ClientContext* context, const ::calibration::proto::Empty* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Reset_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* ColorCalibrationService::Stub::PrepareAsyncResetRaw(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::calibration::proto::Response, ::calibration::proto::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Reset_, context, request);
}

::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* ColorCalibrationService::Stub::AsyncResetRaw(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncResetRaw(context, request, cq);
  result->StartCall();
  return result;
}

ColorCalibrationService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ColorCalibrationService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ColorCalibrationService::Service, ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ColorCalibrationService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::calibration::proto::Request* req,
             ::calibration::proto::Response* resp) {
               return service->InitializeColorCalibration(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ColorCalibrationService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ColorCalibrationService::Service, ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ColorCalibrationService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::calibration::proto::Request* req,
             ::calibration::proto::Response* resp) {
               return service->CenterTargetCameras(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ColorCalibrationService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ColorCalibrationService::Service, ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ColorCalibrationService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::calibration::proto::Request* req,
             ::calibration::proto::Response* resp) {
               return service->SetAutoWhitebalance(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ColorCalibrationService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ColorCalibrationService::Service, ::calibration::proto::Request, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ColorCalibrationService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::calibration::proto::Request* req,
             ::calibration::proto::Response* resp) {
               return service->SaveToConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ColorCalibrationService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ColorCalibrationService::Service, ::calibration::proto::Empty, ::calibration::proto::Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ColorCalibrationService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::calibration::proto::Empty* req,
             ::calibration::proto::Response* resp) {
               return service->Reset(ctx, req, resp);
             }, this)));
}

ColorCalibrationService::Service::~Service() {
}

::grpc::Status ColorCalibrationService::Service::InitializeColorCalibration(::grpc::ServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ColorCalibrationService::Service::CenterTargetCameras(::grpc::ServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ColorCalibrationService::Service::SetAutoWhitebalance(::grpc::ServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ColorCalibrationService::Service::SaveToConfig(::grpc::ServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ColorCalibrationService::Service::Reset(::grpc::ServerContext* context, const ::calibration::proto::Empty* request, ::calibration::proto::Response* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace calibration
}  // namespace proto


// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: calibration/proto/color_calibration.proto
#ifndef GRPC_calibration_2fproto_2fcolor_5fcalibration_2eproto__INCLUDED
#define GRPC_calibration_2fproto_2fcolor_5fcalibration_2eproto__INCLUDED

#include "calibration/proto/color_calibration.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace calibration {
namespace proto {

class ColorCalibrationService final {
 public:
  static constexpr char const* service_full_name() {
    return "calibration.proto.ColorCalibrationService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status InitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>> AsyncInitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>>(AsyncInitializeColorCalibrationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>> PrepareAsyncInitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>>(PrepareAsyncInitializeColorCalibrationRaw(context, request, cq));
    }
    virtual ::grpc::Status CenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>> AsyncCenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>>(AsyncCenterTargetCamerasRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>> PrepareAsyncCenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>>(PrepareAsyncCenterTargetCamerasRaw(context, request, cq));
    }
    virtual ::grpc::Status SetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>> AsyncSetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>>(AsyncSetAutoWhitebalanceRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>> PrepareAsyncSetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>>(PrepareAsyncSetAutoWhitebalanceRaw(context, request, cq));
    }
    virtual ::grpc::Status SaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>> AsyncSaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>>(AsyncSaveToConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>> PrepareAsyncSaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>>(PrepareAsyncSaveToConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status Reset(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::calibration::proto::Response* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>> AsyncReset(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>>(AsyncResetRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>> PrepareAsyncReset(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>>(PrepareAsyncResetRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void InitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)>) = 0;
      virtual void InitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void CenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void Reset(::grpc::ClientContext* context, const ::calibration::proto::Empty* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Reset(::grpc::ClientContext* context, const ::calibration::proto::Empty* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>* AsyncInitializeColorCalibrationRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>* PrepareAsyncInitializeColorCalibrationRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>* AsyncCenterTargetCamerasRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>* PrepareAsyncCenterTargetCamerasRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>* AsyncSetAutoWhitebalanceRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>* PrepareAsyncSetAutoWhitebalanceRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>* AsyncSaveToConfigRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>* PrepareAsyncSaveToConfigRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>* AsyncResetRaw(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::calibration::proto::Response>* PrepareAsyncResetRaw(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status InitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>> AsyncInitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>>(AsyncInitializeColorCalibrationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>> PrepareAsyncInitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>>(PrepareAsyncInitializeColorCalibrationRaw(context, request, cq));
    }
    ::grpc::Status CenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>> AsyncCenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>>(AsyncCenterTargetCamerasRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>> PrepareAsyncCenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>>(PrepareAsyncCenterTargetCamerasRaw(context, request, cq));
    }
    ::grpc::Status SetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>> AsyncSetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>>(AsyncSetAutoWhitebalanceRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>> PrepareAsyncSetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>>(PrepareAsyncSetAutoWhitebalanceRaw(context, request, cq));
    }
    ::grpc::Status SaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::calibration::proto::Response* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>> AsyncSaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>>(AsyncSaveToConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>> PrepareAsyncSaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>>(PrepareAsyncSaveToConfigRaw(context, request, cq));
    }
    ::grpc::Status Reset(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::calibration::proto::Response* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>> AsyncReset(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>>(AsyncResetRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>> PrepareAsyncReset(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>>(PrepareAsyncResetRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void InitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)>) override;
      void InitializeColorCalibration(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) override;
      void CenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)>) override;
      void CenterTargetCameras(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)>) override;
      void SetAutoWhitebalance(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)>) override;
      void SaveToConfig(::grpc::ClientContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) override;
      void Reset(::grpc::ClientContext* context, const ::calibration::proto::Empty* request, ::calibration::proto::Response* response, std::function<void(::grpc::Status)>) override;
      void Reset(::grpc::ClientContext* context, const ::calibration::proto::Empty* request, ::calibration::proto::Response* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* AsyncInitializeColorCalibrationRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* PrepareAsyncInitializeColorCalibrationRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* AsyncCenterTargetCamerasRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* PrepareAsyncCenterTargetCamerasRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* AsyncSetAutoWhitebalanceRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* PrepareAsyncSetAutoWhitebalanceRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* AsyncSaveToConfigRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* PrepareAsyncSaveToConfigRaw(::grpc::ClientContext* context, const ::calibration::proto::Request& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* AsyncResetRaw(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::calibration::proto::Response>* PrepareAsyncResetRaw(::grpc::ClientContext* context, const ::calibration::proto::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_InitializeColorCalibration_;
    const ::grpc::internal::RpcMethod rpcmethod_CenterTargetCameras_;
    const ::grpc::internal::RpcMethod rpcmethod_SetAutoWhitebalance_;
    const ::grpc::internal::RpcMethod rpcmethod_SaveToConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_Reset_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status InitializeColorCalibration(::grpc::ServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response);
    virtual ::grpc::Status CenterTargetCameras(::grpc::ServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response);
    virtual ::grpc::Status SetAutoWhitebalance(::grpc::ServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response);
    virtual ::grpc::Status SaveToConfig(::grpc::ServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response);
    virtual ::grpc::Status Reset(::grpc::ServerContext* context, const ::calibration::proto::Empty* request, ::calibration::proto::Response* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_InitializeColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_InitializeColorCalibration() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_InitializeColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status InitializeColorCalibration(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestInitializeColorCalibration(::grpc::ServerContext* context, ::calibration::proto::Request* request, ::grpc::ServerAsyncResponseWriter< ::calibration::proto::Response>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CenterTargetCameras : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CenterTargetCameras() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_CenterTargetCameras() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CenterTargetCameras(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCenterTargetCameras(::grpc::ServerContext* context, ::calibration::proto::Request* request, ::grpc::ServerAsyncResponseWriter< ::calibration::proto::Response>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetAutoWhitebalance : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetAutoWhitebalance() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_SetAutoWhitebalance() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetAutoWhitebalance(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetAutoWhitebalance(::grpc::ServerContext* context, ::calibration::proto::Request* request, ::grpc::ServerAsyncResponseWriter< ::calibration::proto::Response>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SaveToConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SaveToConfig() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_SaveToConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveToConfig(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveToConfig(::grpc::ServerContext* context, ::calibration::proto::Request* request, ::grpc::ServerAsyncResponseWriter< ::calibration::proto::Response>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_Reset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Reset() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_Reset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Reset(::grpc::ServerContext* /*context*/, const ::calibration::proto::Empty* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReset(::grpc::ServerContext* context, ::calibration::proto::Empty* request, ::grpc::ServerAsyncResponseWriter< ::calibration::proto::Response>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_InitializeColorCalibration<WithAsyncMethod_CenterTargetCameras<WithAsyncMethod_SetAutoWhitebalance<WithAsyncMethod_SaveToConfig<WithAsyncMethod_Reset<Service > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_InitializeColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_InitializeColorCalibration() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::calibration::proto::Request, ::calibration::proto::Response>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response) { return this->InitializeColorCalibration(context, request, response); }));}
    void SetMessageAllocatorFor_InitializeColorCalibration(
        ::grpc::MessageAllocator< ::calibration::proto::Request, ::calibration::proto::Response>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::calibration::proto::Request, ::calibration::proto::Response>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_InitializeColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status InitializeColorCalibration(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* InitializeColorCalibration(
      ::grpc::CallbackServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_CenterTargetCameras : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_CenterTargetCameras() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::calibration::proto::Request, ::calibration::proto::Response>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response) { return this->CenterTargetCameras(context, request, response); }));}
    void SetMessageAllocatorFor_CenterTargetCameras(
        ::grpc::MessageAllocator< ::calibration::proto::Request, ::calibration::proto::Response>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::calibration::proto::Request, ::calibration::proto::Response>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_CenterTargetCameras() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CenterTargetCameras(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CenterTargetCameras(
      ::grpc::CallbackServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetAutoWhitebalance : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetAutoWhitebalance() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::calibration::proto::Request, ::calibration::proto::Response>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response) { return this->SetAutoWhitebalance(context, request, response); }));}
    void SetMessageAllocatorFor_SetAutoWhitebalance(
        ::grpc::MessageAllocator< ::calibration::proto::Request, ::calibration::proto::Response>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::calibration::proto::Request, ::calibration::proto::Response>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetAutoWhitebalance() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetAutoWhitebalance(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetAutoWhitebalance(
      ::grpc::CallbackServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SaveToConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SaveToConfig() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::calibration::proto::Request, ::calibration::proto::Response>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::calibration::proto::Request* request, ::calibration::proto::Response* response) { return this->SaveToConfig(context, request, response); }));}
    void SetMessageAllocatorFor_SaveToConfig(
        ::grpc::MessageAllocator< ::calibration::proto::Request, ::calibration::proto::Response>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::calibration::proto::Request, ::calibration::proto::Response>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SaveToConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveToConfig(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveToConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_Reset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Reset() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::calibration::proto::Empty, ::calibration::proto::Response>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::calibration::proto::Empty* request, ::calibration::proto::Response* response) { return this->Reset(context, request, response); }));}
    void SetMessageAllocatorFor_Reset(
        ::grpc::MessageAllocator< ::calibration::proto::Empty, ::calibration::proto::Response>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::calibration::proto::Empty, ::calibration::proto::Response>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_Reset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Reset(::grpc::ServerContext* /*context*/, const ::calibration::proto::Empty* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Reset(
      ::grpc::CallbackServerContext* /*context*/, const ::calibration::proto::Empty* /*request*/, ::calibration::proto::Response* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_InitializeColorCalibration<WithCallbackMethod_CenterTargetCameras<WithCallbackMethod_SetAutoWhitebalance<WithCallbackMethod_SaveToConfig<WithCallbackMethod_Reset<Service > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_InitializeColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_InitializeColorCalibration() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_InitializeColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status InitializeColorCalibration(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CenterTargetCameras : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CenterTargetCameras() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_CenterTargetCameras() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CenterTargetCameras(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetAutoWhitebalance : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetAutoWhitebalance() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_SetAutoWhitebalance() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetAutoWhitebalance(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SaveToConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SaveToConfig() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_SaveToConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveToConfig(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_Reset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Reset() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_Reset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Reset(::grpc::ServerContext* /*context*/, const ::calibration::proto::Empty* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_InitializeColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_InitializeColorCalibration() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_InitializeColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status InitializeColorCalibration(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestInitializeColorCalibration(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CenterTargetCameras : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CenterTargetCameras() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_CenterTargetCameras() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CenterTargetCameras(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCenterTargetCameras(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetAutoWhitebalance : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetAutoWhitebalance() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_SetAutoWhitebalance() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetAutoWhitebalance(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetAutoWhitebalance(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SaveToConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SaveToConfig() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_SaveToConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveToConfig(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveToConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_Reset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Reset() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_Reset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Reset(::grpc::ServerContext* /*context*/, const ::calibration::proto::Empty* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReset(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_InitializeColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_InitializeColorCalibration() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->InitializeColorCalibration(context, request, response); }));
    }
    ~WithRawCallbackMethod_InitializeColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status InitializeColorCalibration(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* InitializeColorCalibration(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_CenterTargetCameras : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_CenterTargetCameras() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CenterTargetCameras(context, request, response); }));
    }
    ~WithRawCallbackMethod_CenterTargetCameras() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CenterTargetCameras(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CenterTargetCameras(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetAutoWhitebalance : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetAutoWhitebalance() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetAutoWhitebalance(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetAutoWhitebalance() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetAutoWhitebalance(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetAutoWhitebalance(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SaveToConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SaveToConfig() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SaveToConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_SaveToConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveToConfig(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveToConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Reset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Reset() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Reset(context, request, response); }));
    }
    ~WithRawCallbackMethod_Reset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Reset(::grpc::ServerContext* /*context*/, const ::calibration::proto::Empty* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Reset(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_InitializeColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_InitializeColorCalibration() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::calibration::proto::Request, ::calibration::proto::Response>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::calibration::proto::Request, ::calibration::proto::Response>* streamer) {
                       return this->StreamedInitializeColorCalibration(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_InitializeColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status InitializeColorCalibration(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedInitializeColorCalibration(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::calibration::proto::Request,::calibration::proto::Response>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CenterTargetCameras : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CenterTargetCameras() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::calibration::proto::Request, ::calibration::proto::Response>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::calibration::proto::Request, ::calibration::proto::Response>* streamer) {
                       return this->StreamedCenterTargetCameras(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_CenterTargetCameras() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CenterTargetCameras(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCenterTargetCameras(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::calibration::proto::Request,::calibration::proto::Response>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetAutoWhitebalance : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetAutoWhitebalance() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::calibration::proto::Request, ::calibration::proto::Response>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::calibration::proto::Request, ::calibration::proto::Response>* streamer) {
                       return this->StreamedSetAutoWhitebalance(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetAutoWhitebalance() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetAutoWhitebalance(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetAutoWhitebalance(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::calibration::proto::Request,::calibration::proto::Response>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SaveToConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SaveToConfig() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::calibration::proto::Request, ::calibration::proto::Response>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::calibration::proto::Request, ::calibration::proto::Response>* streamer) {
                       return this->StreamedSaveToConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SaveToConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SaveToConfig(::grpc::ServerContext* /*context*/, const ::calibration::proto::Request* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSaveToConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::calibration::proto::Request,::calibration::proto::Response>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Reset : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Reset() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::calibration::proto::Empty, ::calibration::proto::Response>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::calibration::proto::Empty, ::calibration::proto::Response>* streamer) {
                       return this->StreamedReset(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_Reset() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Reset(::grpc::ServerContext* /*context*/, const ::calibration::proto::Empty* /*request*/, ::calibration::proto::Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedReset(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::calibration::proto::Empty,::calibration::proto::Response>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_InitializeColorCalibration<WithStreamedUnaryMethod_CenterTargetCameras<WithStreamedUnaryMethod_SetAutoWhitebalance<WithStreamedUnaryMethod_SaveToConfig<WithStreamedUnaryMethod_Reset<Service > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_InitializeColorCalibration<WithStreamedUnaryMethod_CenterTargetCameras<WithStreamedUnaryMethod_SetAutoWhitebalance<WithStreamedUnaryMethod_SaveToConfig<WithStreamedUnaryMethod_Reset<Service > > > > > StreamedService;
};

}  // namespace proto
}  // namespace calibration


#endif  // GRPC_calibration_2fproto_2fcolor_5fcalibration_2eproto__INCLUDED

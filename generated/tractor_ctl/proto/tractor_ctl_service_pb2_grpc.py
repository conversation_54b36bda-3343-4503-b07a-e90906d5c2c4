# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from generated.tractor_ctl.proto import tractor_ctl_service_pb2 as tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2


class TractorCtlServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Ping = channel.unary_unary(
                '/tractor_ctl.TractorCtlService/Ping',
                request_serializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.PingMsg.SerializeToString,
                response_deserializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.PongMsg.FromString,
                )
        self.ReloadWheelPos = channel.unary_unary(
                '/tractor_ctl.TractorCtlService/ReloadWheelPos',
                request_serializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.ReloadWheelPosRequest.SerializeToString,
                response_deserializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.ReloadWheelPosResponse.FromString,
                )
        self.GetSpeed = channel.unary_unary(
                '/tractor_ctl.TractorCtlService/GetSpeed',
                request_serializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.GetSpeedRequest.SerializeToString,
                response_deserializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.GetSpeedResponse.FromString,
                )
        self.SubscribeTractorState = channel.unary_stream(
                '/tractor_ctl.TractorCtlService/SubscribeTractorState',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.TractorStateWrapper.FromString,
                )
        self.FarmChanged = channel.unary_unary(
                '/tractor_ctl.TractorCtlService/FarmChanged',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )


class TractorCtlServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReloadWheelPos(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSpeed(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubscribeTractorState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FarmChanged(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TractorCtlServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.PingMsg.FromString,
                    response_serializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.PongMsg.SerializeToString,
            ),
            'ReloadWheelPos': grpc.unary_unary_rpc_method_handler(
                    servicer.ReloadWheelPos,
                    request_deserializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.ReloadWheelPosRequest.FromString,
                    response_serializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.ReloadWheelPosResponse.SerializeToString,
            ),
            'GetSpeed': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSpeed,
                    request_deserializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.GetSpeedRequest.FromString,
                    response_serializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.GetSpeedResponse.SerializeToString,
            ),
            'SubscribeTractorState': grpc.unary_stream_rpc_method_handler(
                    servicer.SubscribeTractorState,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.TractorStateWrapper.SerializeToString,
            ),
            'FarmChanged': grpc.unary_unary_rpc_method_handler(
                    servicer.FarmChanged,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'tractor_ctl.TractorCtlService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class TractorCtlService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tractor_ctl.TractorCtlService/Ping',
            tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.PingMsg.SerializeToString,
            tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.PongMsg.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ReloadWheelPos(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tractor_ctl.TractorCtlService/ReloadWheelPos',
            tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.ReloadWheelPosRequest.SerializeToString,
            tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.ReloadWheelPosResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSpeed(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tractor_ctl.TractorCtlService/GetSpeed',
            tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.GetSpeedRequest.SerializeToString,
            tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.GetSpeedResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubscribeTractorState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/tractor_ctl.TractorCtlService/SubscribeTractorState',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            tractor__ctl_dot_proto_dot_tractor__ctl__service__pb2.TractorStateWrapper.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FarmChanged(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/tractor_ctl.TractorCtlService/FarmChanged',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tractor_ctl/proto/tractor_ctl_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/empty.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[7]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto;
namespace tractor_ctl {
class GetSpeedRequest;
struct GetSpeedRequestDefaultTypeInternal;
extern GetSpeedRequestDefaultTypeInternal _GetSpeedRequest_default_instance_;
class GetSpeedResponse;
struct GetSpeedResponseDefaultTypeInternal;
extern GetSpeedResponseDefaultTypeInternal _GetSpeedResponse_default_instance_;
class PingMsg;
struct PingMsgDefaultTypeInternal;
extern PingMsgDefaultTypeInternal _PingMsg_default_instance_;
class PongMsg;
struct PongMsgDefaultTypeInternal;
extern PongMsgDefaultTypeInternal _PongMsg_default_instance_;
class ReloadWheelPosRequest;
struct ReloadWheelPosRequestDefaultTypeInternal;
extern ReloadWheelPosRequestDefaultTypeInternal _ReloadWheelPosRequest_default_instance_;
class ReloadWheelPosResponse;
struct ReloadWheelPosResponseDefaultTypeInternal;
extern ReloadWheelPosResponseDefaultTypeInternal _ReloadWheelPosResponse_default_instance_;
class TractorStateWrapper;
struct TractorStateWrapperDefaultTypeInternal;
extern TractorStateWrapperDefaultTypeInternal _TractorStateWrapper_default_instance_;
}  // namespace tractor_ctl
PROTOBUF_NAMESPACE_OPEN
template<> ::tractor_ctl::GetSpeedRequest* Arena::CreateMaybeMessage<::tractor_ctl::GetSpeedRequest>(Arena*);
template<> ::tractor_ctl::GetSpeedResponse* Arena::CreateMaybeMessage<::tractor_ctl::GetSpeedResponse>(Arena*);
template<> ::tractor_ctl::PingMsg* Arena::CreateMaybeMessage<::tractor_ctl::PingMsg>(Arena*);
template<> ::tractor_ctl::PongMsg* Arena::CreateMaybeMessage<::tractor_ctl::PongMsg>(Arena*);
template<> ::tractor_ctl::ReloadWheelPosRequest* Arena::CreateMaybeMessage<::tractor_ctl::ReloadWheelPosRequest>(Arena*);
template<> ::tractor_ctl::ReloadWheelPosResponse* Arena::CreateMaybeMessage<::tractor_ctl::ReloadWheelPosResponse>(Arena*);
template<> ::tractor_ctl::TractorStateWrapper* Arena::CreateMaybeMessage<::tractor_ctl::TractorStateWrapper>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tractor_ctl {

// ===================================================================

class PingMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tractor_ctl.PingMsg) */ {
 public:
  inline PingMsg() : PingMsg(nullptr) {}
  ~PingMsg() override;
  explicit constexpr PingMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PingMsg(const PingMsg& from);
  PingMsg(PingMsg&& from) noexcept
    : PingMsg() {
    *this = ::std::move(from);
  }

  inline PingMsg& operator=(const PingMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline PingMsg& operator=(PingMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PingMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const PingMsg* internal_default_instance() {
    return reinterpret_cast<const PingMsg*>(
               &_PingMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(PingMsg& a, PingMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(PingMsg* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PingMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PingMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PingMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PingMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PingMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PingMsg* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tractor_ctl.PingMsg";
  }
  protected:
  explicit PingMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
  };
  // uint32 x = 1;
  void clear_x();
  uint32_t x() const;
  void set_x(uint32_t value);
  private:
  uint32_t _internal_x() const;
  void _internal_set_x(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tractor_ctl.PingMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t x_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto;
};
// -------------------------------------------------------------------

class PongMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tractor_ctl.PongMsg) */ {
 public:
  inline PongMsg() : PongMsg(nullptr) {}
  ~PongMsg() override;
  explicit constexpr PongMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PongMsg(const PongMsg& from);
  PongMsg(PongMsg&& from) noexcept
    : PongMsg() {
    *this = ::std::move(from);
  }

  inline PongMsg& operator=(const PongMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline PongMsg& operator=(PongMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PongMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const PongMsg* internal_default_instance() {
    return reinterpret_cast<const PongMsg*>(
               &_PongMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(PongMsg& a, PongMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(PongMsg* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PongMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PongMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PongMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PongMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PongMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PongMsg* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tractor_ctl.PongMsg";
  }
  protected:
  explicit PongMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
  };
  // uint32 x = 1;
  void clear_x();
  uint32_t x() const;
  void set_x(uint32_t value);
  private:
  uint32_t _internal_x() const;
  void _internal_set_x(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tractor_ctl.PongMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t x_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ReloadWheelPosRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tractor_ctl.ReloadWheelPosRequest) */ {
 public:
  inline ReloadWheelPosRequest() : ReloadWheelPosRequest(nullptr) {}
  explicit constexpr ReloadWheelPosRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReloadWheelPosRequest(const ReloadWheelPosRequest& from);
  ReloadWheelPosRequest(ReloadWheelPosRequest&& from) noexcept
    : ReloadWheelPosRequest() {
    *this = ::std::move(from);
  }

  inline ReloadWheelPosRequest& operator=(const ReloadWheelPosRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReloadWheelPosRequest& operator=(ReloadWheelPosRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReloadWheelPosRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReloadWheelPosRequest* internal_default_instance() {
    return reinterpret_cast<const ReloadWheelPosRequest*>(
               &_ReloadWheelPosRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ReloadWheelPosRequest& a, ReloadWheelPosRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ReloadWheelPosRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReloadWheelPosRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReloadWheelPosRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReloadWheelPosRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const ReloadWheelPosRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const ReloadWheelPosRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tractor_ctl.ReloadWheelPosRequest";
  }
  protected:
  explicit ReloadWheelPosRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tractor_ctl.ReloadWheelPosRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ReloadWheelPosResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tractor_ctl.ReloadWheelPosResponse) */ {
 public:
  inline ReloadWheelPosResponse() : ReloadWheelPosResponse(nullptr) {}
  explicit constexpr ReloadWheelPosResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReloadWheelPosResponse(const ReloadWheelPosResponse& from);
  ReloadWheelPosResponse(ReloadWheelPosResponse&& from) noexcept
    : ReloadWheelPosResponse() {
    *this = ::std::move(from);
  }

  inline ReloadWheelPosResponse& operator=(const ReloadWheelPosResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReloadWheelPosResponse& operator=(ReloadWheelPosResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReloadWheelPosResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReloadWheelPosResponse* internal_default_instance() {
    return reinterpret_cast<const ReloadWheelPosResponse*>(
               &_ReloadWheelPosResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ReloadWheelPosResponse& a, ReloadWheelPosResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ReloadWheelPosResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReloadWheelPosResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReloadWheelPosResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReloadWheelPosResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const ReloadWheelPosResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const ReloadWheelPosResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tractor_ctl.ReloadWheelPosResponse";
  }
  protected:
  explicit ReloadWheelPosResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tractor_ctl.ReloadWheelPosResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetSpeedRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tractor_ctl.GetSpeedRequest) */ {
 public:
  inline GetSpeedRequest() : GetSpeedRequest(nullptr) {}
  explicit constexpr GetSpeedRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetSpeedRequest(const GetSpeedRequest& from);
  GetSpeedRequest(GetSpeedRequest&& from) noexcept
    : GetSpeedRequest() {
    *this = ::std::move(from);
  }

  inline GetSpeedRequest& operator=(const GetSpeedRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetSpeedRequest& operator=(GetSpeedRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetSpeedRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetSpeedRequest* internal_default_instance() {
    return reinterpret_cast<const GetSpeedRequest*>(
               &_GetSpeedRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetSpeedRequest& a, GetSpeedRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetSpeedRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetSpeedRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetSpeedRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetSpeedRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const GetSpeedRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const GetSpeedRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tractor_ctl.GetSpeedRequest";
  }
  protected:
  explicit GetSpeedRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tractor_ctl.GetSpeedRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetSpeedResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tractor_ctl.GetSpeedResponse) */ {
 public:
  inline GetSpeedResponse() : GetSpeedResponse(nullptr) {}
  ~GetSpeedResponse() override;
  explicit constexpr GetSpeedResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetSpeedResponse(const GetSpeedResponse& from);
  GetSpeedResponse(GetSpeedResponse&& from) noexcept
    : GetSpeedResponse() {
    *this = ::std::move(from);
  }

  inline GetSpeedResponse& operator=(const GetSpeedResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetSpeedResponse& operator=(GetSpeedResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetSpeedResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetSpeedResponse* internal_default_instance() {
    return reinterpret_cast<const GetSpeedResponse*>(
               &_GetSpeedResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GetSpeedResponse& a, GetSpeedResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetSpeedResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetSpeedResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetSpeedResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetSpeedResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetSpeedResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetSpeedResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetSpeedResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tractor_ctl.GetSpeedResponse";
  }
  protected:
  explicit GetSpeedResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSpeedMphFieldNumber = 1,
  };
  // float speed_mph = 1;
  void clear_speed_mph();
  float speed_mph() const;
  void set_speed_mph(float value);
  private:
  float _internal_speed_mph() const;
  void _internal_set_speed_mph(float value);
  public:

  // @@protoc_insertion_point(class_scope:tractor_ctl.GetSpeedResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float speed_mph_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto;
};
// -------------------------------------------------------------------

class TractorStateWrapper final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tractor_ctl.TractorStateWrapper) */ {
 public:
  inline TractorStateWrapper() : TractorStateWrapper(nullptr) {}
  ~TractorStateWrapper() override;
  explicit constexpr TractorStateWrapper(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TractorStateWrapper(const TractorStateWrapper& from);
  TractorStateWrapper(TractorStateWrapper&& from) noexcept
    : TractorStateWrapper() {
    *this = ::std::move(from);
  }

  inline TractorStateWrapper& operator=(const TractorStateWrapper& from) {
    CopyFrom(from);
    return *this;
  }
  inline TractorStateWrapper& operator=(TractorStateWrapper&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TractorStateWrapper& default_instance() {
    return *internal_default_instance();
  }
  static inline const TractorStateWrapper* internal_default_instance() {
    return reinterpret_cast<const TractorStateWrapper*>(
               &_TractorStateWrapper_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(TractorStateWrapper& a, TractorStateWrapper& b) {
    a.Swap(&b);
  }
  inline void Swap(TractorStateWrapper* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TractorStateWrapper* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TractorStateWrapper* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TractorStateWrapper>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TractorStateWrapper& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TractorStateWrapper& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TractorStateWrapper* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tractor_ctl.TractorStateWrapper";
  }
  protected:
  explicit TractorStateWrapper(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMsgFieldNumber = 1,
  };
  // bytes msg = 1;
  void clear_msg();
  const std::string& msg() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_msg(ArgT0&& arg0, ArgT... args);
  std::string* mutable_msg();
  PROTOBUF_NODISCARD std::string* release_msg();
  void set_allocated_msg(std::string* msg);
  private:
  const std::string& _internal_msg() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_msg(const std::string& value);
  std::string* _internal_mutable_msg();
  public:

  // @@protoc_insertion_point(class_scope:tractor_ctl.TractorStateWrapper)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr msg_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PingMsg

// uint32 x = 1;
inline void PingMsg::clear_x() {
  x_ = 0u;
}
inline uint32_t PingMsg::_internal_x() const {
  return x_;
}
inline uint32_t PingMsg::x() const {
  // @@protoc_insertion_point(field_get:tractor_ctl.PingMsg.x)
  return _internal_x();
}
inline void PingMsg::_internal_set_x(uint32_t value) {
  
  x_ = value;
}
inline void PingMsg::set_x(uint32_t value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:tractor_ctl.PingMsg.x)
}

// -------------------------------------------------------------------

// PongMsg

// uint32 x = 1;
inline void PongMsg::clear_x() {
  x_ = 0u;
}
inline uint32_t PongMsg::_internal_x() const {
  return x_;
}
inline uint32_t PongMsg::x() const {
  // @@protoc_insertion_point(field_get:tractor_ctl.PongMsg.x)
  return _internal_x();
}
inline void PongMsg::_internal_set_x(uint32_t value) {
  
  x_ = value;
}
inline void PongMsg::set_x(uint32_t value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:tractor_ctl.PongMsg.x)
}

// -------------------------------------------------------------------

// ReloadWheelPosRequest

// -------------------------------------------------------------------

// ReloadWheelPosResponse

// -------------------------------------------------------------------

// GetSpeedRequest

// -------------------------------------------------------------------

// GetSpeedResponse

// float speed_mph = 1;
inline void GetSpeedResponse::clear_speed_mph() {
  speed_mph_ = 0;
}
inline float GetSpeedResponse::_internal_speed_mph() const {
  return speed_mph_;
}
inline float GetSpeedResponse::speed_mph() const {
  // @@protoc_insertion_point(field_get:tractor_ctl.GetSpeedResponse.speed_mph)
  return _internal_speed_mph();
}
inline void GetSpeedResponse::_internal_set_speed_mph(float value) {
  
  speed_mph_ = value;
}
inline void GetSpeedResponse::set_speed_mph(float value) {
  _internal_set_speed_mph(value);
  // @@protoc_insertion_point(field_set:tractor_ctl.GetSpeedResponse.speed_mph)
}

// -------------------------------------------------------------------

// TractorStateWrapper

// bytes msg = 1;
inline void TractorStateWrapper::clear_msg() {
  msg_.ClearToEmpty();
}
inline const std::string& TractorStateWrapper::msg() const {
  // @@protoc_insertion_point(field_get:tractor_ctl.TractorStateWrapper.msg)
  return _internal_msg();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TractorStateWrapper::set_msg(ArgT0&& arg0, ArgT... args) {
 
 msg_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tractor_ctl.TractorStateWrapper.msg)
}
inline std::string* TractorStateWrapper::mutable_msg() {
  std::string* _s = _internal_mutable_msg();
  // @@protoc_insertion_point(field_mutable:tractor_ctl.TractorStateWrapper.msg)
  return _s;
}
inline const std::string& TractorStateWrapper::_internal_msg() const {
  return msg_.Get();
}
inline void TractorStateWrapper::_internal_set_msg(const std::string& value) {
  
  msg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TractorStateWrapper::_internal_mutable_msg() {
  
  return msg_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TractorStateWrapper::release_msg() {
  // @@protoc_insertion_point(field_release:tractor_ctl.TractorStateWrapper.msg)
  return msg_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TractorStateWrapper::set_allocated_msg(std::string* msg) {
  if (msg != nullptr) {
    
  } else {
    
  }
  msg_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), msg,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (msg_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tractor_ctl.TractorStateWrapper.msg)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tractor_ctl

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto

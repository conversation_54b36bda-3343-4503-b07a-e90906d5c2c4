# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tractor_ctl/proto/tractor_ctl_service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tractor_ctl/proto/tractor_ctl_service.proto',
  package='tractor_ctl',
  syntax='proto3',
  serialized_options=b'Z\021proto/tractor_ctl',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n+tractor_ctl/proto/tractor_ctl_service.proto\x12\x0btractor_ctl\x1a\x1bgoogle/protobuf/empty.proto\"\x14\n\x07PingMsg\x12\t\n\x01x\x18\x01 \x01(\r\"\x14\n\x07PongMsg\x12\t\n\x01x\x18\x01 \x01(\r\"\x17\n\x15ReloadWheelPosRequest\"\x18\n\x16ReloadWheelPosResponse\"\x11\n\x0fGetSpeedRequest\"%\n\x10GetSpeedResponse\x12\x11\n\tspeed_mph\x18\x01 \x01(\x02\"\"\n\x13TractorStateWrapper\x12\x0b\n\x03msg\x18\x01 \x01(\x0c\x32\x89\x03\n\x11TractorCtlService\x12\x34\n\x04Ping\x12\x14.tractor_ctl.PingMsg\x1a\x14.tractor_ctl.PongMsg\"\x00\x12[\n\x0eReloadWheelPos\x12\".tractor_ctl.ReloadWheelPosRequest\x1a#.tractor_ctl.ReloadWheelPosResponse\"\x00\x12I\n\x08GetSpeed\x12\x1c.tractor_ctl.GetSpeedRequest\x1a\x1d.tractor_ctl.GetSpeedResponse\"\x00\x12U\n\x15SubscribeTractorState\x12\x16.google.protobuf.Empty\x1a .tractor_ctl.TractorStateWrapper\"\x00\x30\x01\x12?\n\x0b\x46\x61rmChanged\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\"\x00\x42\x13Z\x11proto/tractor_ctlb\x06proto3'
  ,
  dependencies=[google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,])




_PINGMSG = _descriptor.Descriptor(
  name='PingMsg',
  full_name='tractor_ctl.PingMsg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='tractor_ctl.PingMsg.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=89,
  serialized_end=109,
)


_PONGMSG = _descriptor.Descriptor(
  name='PongMsg',
  full_name='tractor_ctl.PongMsg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='tractor_ctl.PongMsg.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=111,
  serialized_end=131,
)


_RELOADWHEELPOSREQUEST = _descriptor.Descriptor(
  name='ReloadWheelPosRequest',
  full_name='tractor_ctl.ReloadWheelPosRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=133,
  serialized_end=156,
)


_RELOADWHEELPOSRESPONSE = _descriptor.Descriptor(
  name='ReloadWheelPosResponse',
  full_name='tractor_ctl.ReloadWheelPosResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=158,
  serialized_end=182,
)


_GETSPEEDREQUEST = _descriptor.Descriptor(
  name='GetSpeedRequest',
  full_name='tractor_ctl.GetSpeedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=184,
  serialized_end=201,
)


_GETSPEEDRESPONSE = _descriptor.Descriptor(
  name='GetSpeedResponse',
  full_name='tractor_ctl.GetSpeedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='speed_mph', full_name='tractor_ctl.GetSpeedResponse.speed_mph', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=203,
  serialized_end=240,
)


_TRACTORSTATEWRAPPER = _descriptor.Descriptor(
  name='TractorStateWrapper',
  full_name='tractor_ctl.TractorStateWrapper',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='msg', full_name='tractor_ctl.TractorStateWrapper.msg', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=242,
  serialized_end=276,
)

DESCRIPTOR.message_types_by_name['PingMsg'] = _PINGMSG
DESCRIPTOR.message_types_by_name['PongMsg'] = _PONGMSG
DESCRIPTOR.message_types_by_name['ReloadWheelPosRequest'] = _RELOADWHEELPOSREQUEST
DESCRIPTOR.message_types_by_name['ReloadWheelPosResponse'] = _RELOADWHEELPOSRESPONSE
DESCRIPTOR.message_types_by_name['GetSpeedRequest'] = _GETSPEEDREQUEST
DESCRIPTOR.message_types_by_name['GetSpeedResponse'] = _GETSPEEDRESPONSE
DESCRIPTOR.message_types_by_name['TractorStateWrapper'] = _TRACTORSTATEWRAPPER
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PingMsg = _reflection.GeneratedProtocolMessageType('PingMsg', (_message.Message,), {
  'DESCRIPTOR' : _PINGMSG,
  '__module__' : 'tractor_ctl.proto.tractor_ctl_service_pb2'
  # @@protoc_insertion_point(class_scope:tractor_ctl.PingMsg)
  })
_sym_db.RegisterMessage(PingMsg)

PongMsg = _reflection.GeneratedProtocolMessageType('PongMsg', (_message.Message,), {
  'DESCRIPTOR' : _PONGMSG,
  '__module__' : 'tractor_ctl.proto.tractor_ctl_service_pb2'
  # @@protoc_insertion_point(class_scope:tractor_ctl.PongMsg)
  })
_sym_db.RegisterMessage(PongMsg)

ReloadWheelPosRequest = _reflection.GeneratedProtocolMessageType('ReloadWheelPosRequest', (_message.Message,), {
  'DESCRIPTOR' : _RELOADWHEELPOSREQUEST,
  '__module__' : 'tractor_ctl.proto.tractor_ctl_service_pb2'
  # @@protoc_insertion_point(class_scope:tractor_ctl.ReloadWheelPosRequest)
  })
_sym_db.RegisterMessage(ReloadWheelPosRequest)

ReloadWheelPosResponse = _reflection.GeneratedProtocolMessageType('ReloadWheelPosResponse', (_message.Message,), {
  'DESCRIPTOR' : _RELOADWHEELPOSRESPONSE,
  '__module__' : 'tractor_ctl.proto.tractor_ctl_service_pb2'
  # @@protoc_insertion_point(class_scope:tractor_ctl.ReloadWheelPosResponse)
  })
_sym_db.RegisterMessage(ReloadWheelPosResponse)

GetSpeedRequest = _reflection.GeneratedProtocolMessageType('GetSpeedRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETSPEEDREQUEST,
  '__module__' : 'tractor_ctl.proto.tractor_ctl_service_pb2'
  # @@protoc_insertion_point(class_scope:tractor_ctl.GetSpeedRequest)
  })
_sym_db.RegisterMessage(GetSpeedRequest)

GetSpeedResponse = _reflection.GeneratedProtocolMessageType('GetSpeedResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETSPEEDRESPONSE,
  '__module__' : 'tractor_ctl.proto.tractor_ctl_service_pb2'
  # @@protoc_insertion_point(class_scope:tractor_ctl.GetSpeedResponse)
  })
_sym_db.RegisterMessage(GetSpeedResponse)

TractorStateWrapper = _reflection.GeneratedProtocolMessageType('TractorStateWrapper', (_message.Message,), {
  'DESCRIPTOR' : _TRACTORSTATEWRAPPER,
  '__module__' : 'tractor_ctl.proto.tractor_ctl_service_pb2'
  # @@protoc_insertion_point(class_scope:tractor_ctl.TractorStateWrapper)
  })
_sym_db.RegisterMessage(TractorStateWrapper)


DESCRIPTOR._options = None

_TRACTORCTLSERVICE = _descriptor.ServiceDescriptor(
  name='TractorCtlService',
  full_name='tractor_ctl.TractorCtlService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=279,
  serialized_end=672,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='tractor_ctl.TractorCtlService.Ping',
    index=0,
    containing_service=None,
    input_type=_PINGMSG,
    output_type=_PONGMSG,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ReloadWheelPos',
    full_name='tractor_ctl.TractorCtlService.ReloadWheelPos',
    index=1,
    containing_service=None,
    input_type=_RELOADWHEELPOSREQUEST,
    output_type=_RELOADWHEELPOSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetSpeed',
    full_name='tractor_ctl.TractorCtlService.GetSpeed',
    index=2,
    containing_service=None,
    input_type=_GETSPEEDREQUEST,
    output_type=_GETSPEEDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SubscribeTractorState',
    full_name='tractor_ctl.TractorCtlService.SubscribeTractorState',
    index=3,
    containing_service=None,
    input_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    output_type=_TRACTORSTATEWRAPPER,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='FarmChanged',
    full_name='tractor_ctl.TractorCtlService.FarmChanged',
    index=4,
    containing_service=None,
    input_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    output_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_TRACTORCTLSERVICE)

DESCRIPTOR.services_by_name['TractorCtlService'] = _TRACTORCTLSERVICE

# @@protoc_insertion_point(module_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tractor_ctl/proto/tractor_ctl_service.proto

#include "tractor_ctl/proto/tractor_ctl_service.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace tractor_ctl {
constexpr PingMsg::PingMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_(0u){}
struct PingMsgDefaultTypeInternal {
  constexpr PingMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PingMsgDefaultTypeInternal() {}
  union {
    PingMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PingMsgDefaultTypeInternal _PingMsg_default_instance_;
constexpr PongMsg::PongMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_(0u){}
struct PongMsgDefaultTypeInternal {
  constexpr PongMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PongMsgDefaultTypeInternal() {}
  union {
    PongMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PongMsgDefaultTypeInternal _PongMsg_default_instance_;
constexpr ReloadWheelPosRequest::ReloadWheelPosRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ReloadWheelPosRequestDefaultTypeInternal {
  constexpr ReloadWheelPosRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ReloadWheelPosRequestDefaultTypeInternal() {}
  union {
    ReloadWheelPosRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ReloadWheelPosRequestDefaultTypeInternal _ReloadWheelPosRequest_default_instance_;
constexpr ReloadWheelPosResponse::ReloadWheelPosResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ReloadWheelPosResponseDefaultTypeInternal {
  constexpr ReloadWheelPosResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ReloadWheelPosResponseDefaultTypeInternal() {}
  union {
    ReloadWheelPosResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ReloadWheelPosResponseDefaultTypeInternal _ReloadWheelPosResponse_default_instance_;
constexpr GetSpeedRequest::GetSpeedRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetSpeedRequestDefaultTypeInternal {
  constexpr GetSpeedRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetSpeedRequestDefaultTypeInternal() {}
  union {
    GetSpeedRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetSpeedRequestDefaultTypeInternal _GetSpeedRequest_default_instance_;
constexpr GetSpeedResponse::GetSpeedResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : speed_mph_(0){}
struct GetSpeedResponseDefaultTypeInternal {
  constexpr GetSpeedResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetSpeedResponseDefaultTypeInternal() {}
  union {
    GetSpeedResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetSpeedResponseDefaultTypeInternal _GetSpeedResponse_default_instance_;
constexpr TractorStateWrapper::TractorStateWrapper(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : msg_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct TractorStateWrapperDefaultTypeInternal {
  constexpr TractorStateWrapperDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TractorStateWrapperDefaultTypeInternal() {}
  union {
    TractorStateWrapper _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TractorStateWrapperDefaultTypeInternal _TractorStateWrapper_default_instance_;
}  // namespace tractor_ctl
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto[7];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto = nullptr;

const uint32_t TableStruct_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::tractor_ctl::PingMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::tractor_ctl::PingMsg, x_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::tractor_ctl::PongMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::tractor_ctl::PongMsg, x_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::tractor_ctl::ReloadWheelPosRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::tractor_ctl::ReloadWheelPosResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::tractor_ctl::GetSpeedRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::tractor_ctl::GetSpeedResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::tractor_ctl::GetSpeedResponse, speed_mph_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::tractor_ctl::TractorStateWrapper, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::tractor_ctl::TractorStateWrapper, msg_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::tractor_ctl::PingMsg)},
  { 7, -1, -1, sizeof(::tractor_ctl::PongMsg)},
  { 14, -1, -1, sizeof(::tractor_ctl::ReloadWheelPosRequest)},
  { 20, -1, -1, sizeof(::tractor_ctl::ReloadWheelPosResponse)},
  { 26, -1, -1, sizeof(::tractor_ctl::GetSpeedRequest)},
  { 32, -1, -1, sizeof(::tractor_ctl::GetSpeedResponse)},
  { 39, -1, -1, sizeof(::tractor_ctl::TractorStateWrapper)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::tractor_ctl::_PingMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::tractor_ctl::_PongMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::tractor_ctl::_ReloadWheelPosRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::tractor_ctl::_ReloadWheelPosResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::tractor_ctl::_GetSpeedRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::tractor_ctl::_GetSpeedResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::tractor_ctl::_TractorStateWrapper_default_instance_),
};

const char descriptor_table_protodef_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n+tractor_ctl/proto/tractor_ctl_service."
  "proto\022\013tractor_ctl\032\033google/protobuf/empt"
  "y.proto\"\024\n\007PingMsg\022\t\n\001x\030\001 \001(\r\"\024\n\007PongMsg"
  "\022\t\n\001x\030\001 \001(\r\"\027\n\025ReloadWheelPosRequest\"\030\n\026"
  "ReloadWheelPosResponse\"\021\n\017GetSpeedReques"
  "t\"%\n\020GetSpeedResponse\022\021\n\tspeed_mph\030\001 \001(\002"
  "\"\"\n\023TractorStateWrapper\022\013\n\003msg\030\001 \001(\0142\211\003\n"
  "\021TractorCtlService\0224\n\004Ping\022\024.tractor_ctl"
  ".PingMsg\032\024.tractor_ctl.PongMsg\"\000\022[\n\016Relo"
  "adWheelPos\022\".tractor_ctl.ReloadWheelPosR"
  "equest\032#.tractor_ctl.ReloadWheelPosRespo"
  "nse\"\000\022I\n\010GetSpeed\022\034.tractor_ctl.GetSpeed"
  "Request\032\035.tractor_ctl.GetSpeedResponse\"\000"
  "\022U\n\025SubscribeTractorState\022\026.google.proto"
  "buf.Empty\032 .tractor_ctl.TractorStateWrap"
  "per\"\0000\001\022\?\n\013FarmChanged\022\026.google.protobuf"
  ".Empty\032\026.google.protobuf.Empty\"\000B\023Z\021prot"
  "o/tractor_ctlb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_deps[1] = {
  &::descriptor_table_google_2fprotobuf_2fempty_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto = {
  false, false, 701, descriptor_table_protodef_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto, "tractor_ctl/proto/tractor_ctl_service.proto", 
  &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_once, descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_deps, 1, 7,
  schemas, file_default_instances, TableStruct_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto::offsets,
  file_level_metadata_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto, file_level_enum_descriptors_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto, file_level_service_descriptors_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_getter() {
  return &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto(&descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto);
namespace tractor_ctl {

// ===================================================================

class PingMsg::_Internal {
 public:
};

PingMsg::PingMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:tractor_ctl.PingMsg)
}
PingMsg::PingMsg(const PingMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  x_ = from.x_;
  // @@protoc_insertion_point(copy_constructor:tractor_ctl.PingMsg)
}

inline void PingMsg::SharedCtor() {
x_ = 0u;
}

PingMsg::~PingMsg() {
  // @@protoc_insertion_point(destructor:tractor_ctl.PingMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PingMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PingMsg::ArenaDtor(void* object) {
  PingMsg* _this = reinterpret_cast< PingMsg* >(object);
  (void)_this;
}
void PingMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PingMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PingMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:tractor_ctl.PingMsg)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  x_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PingMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PingMsg::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:tractor_ctl.PingMsg)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_x(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tractor_ctl.PingMsg)
  return target;
}

size_t PingMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tractor_ctl.PingMsg)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_x());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PingMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PingMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PingMsg::GetClassData() const { return &_class_data_; }

void PingMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PingMsg *>(to)->MergeFrom(
      static_cast<const PingMsg &>(from));
}


void PingMsg::MergeFrom(const PingMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tractor_ctl.PingMsg)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_x() != 0) {
    _internal_set_x(from._internal_x());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PingMsg::CopyFrom(const PingMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tractor_ctl.PingMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PingMsg::IsInitialized() const {
  return true;
}

void PingMsg::InternalSwap(PingMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(x_, other->x_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PingMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_getter, &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_once,
      file_level_metadata_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto[0]);
}

// ===================================================================

class PongMsg::_Internal {
 public:
};

PongMsg::PongMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:tractor_ctl.PongMsg)
}
PongMsg::PongMsg(const PongMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  x_ = from.x_;
  // @@protoc_insertion_point(copy_constructor:tractor_ctl.PongMsg)
}

inline void PongMsg::SharedCtor() {
x_ = 0u;
}

PongMsg::~PongMsg() {
  // @@protoc_insertion_point(destructor:tractor_ctl.PongMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PongMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PongMsg::ArenaDtor(void* object) {
  PongMsg* _this = reinterpret_cast< PongMsg* >(object);
  (void)_this;
}
void PongMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PongMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PongMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:tractor_ctl.PongMsg)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  x_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PongMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PongMsg::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:tractor_ctl.PongMsg)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_x(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tractor_ctl.PongMsg)
  return target;
}

size_t PongMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tractor_ctl.PongMsg)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_x());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PongMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PongMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PongMsg::GetClassData() const { return &_class_data_; }

void PongMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PongMsg *>(to)->MergeFrom(
      static_cast<const PongMsg &>(from));
}


void PongMsg::MergeFrom(const PongMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tractor_ctl.PongMsg)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_x() != 0) {
    _internal_set_x(from._internal_x());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PongMsg::CopyFrom(const PongMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tractor_ctl.PongMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PongMsg::IsInitialized() const {
  return true;
}

void PongMsg::InternalSwap(PongMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(x_, other->x_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PongMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_getter, &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_once,
      file_level_metadata_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto[1]);
}

// ===================================================================

class ReloadWheelPosRequest::_Internal {
 public:
};

ReloadWheelPosRequest::ReloadWheelPosRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:tractor_ctl.ReloadWheelPosRequest)
}
ReloadWheelPosRequest::ReloadWheelPosRequest(const ReloadWheelPosRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tractor_ctl.ReloadWheelPosRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ReloadWheelPosRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ReloadWheelPosRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata ReloadWheelPosRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_getter, &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_once,
      file_level_metadata_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto[2]);
}

// ===================================================================

class ReloadWheelPosResponse::_Internal {
 public:
};

ReloadWheelPosResponse::ReloadWheelPosResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:tractor_ctl.ReloadWheelPosResponse)
}
ReloadWheelPosResponse::ReloadWheelPosResponse(const ReloadWheelPosResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tractor_ctl.ReloadWheelPosResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ReloadWheelPosResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ReloadWheelPosResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata ReloadWheelPosResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_getter, &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_once,
      file_level_metadata_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto[3]);
}

// ===================================================================

class GetSpeedRequest::_Internal {
 public:
};

GetSpeedRequest::GetSpeedRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:tractor_ctl.GetSpeedRequest)
}
GetSpeedRequest::GetSpeedRequest(const GetSpeedRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:tractor_ctl.GetSpeedRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetSpeedRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetSpeedRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata GetSpeedRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_getter, &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_once,
      file_level_metadata_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto[4]);
}

// ===================================================================

class GetSpeedResponse::_Internal {
 public:
};

GetSpeedResponse::GetSpeedResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:tractor_ctl.GetSpeedResponse)
}
GetSpeedResponse::GetSpeedResponse(const GetSpeedResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  speed_mph_ = from.speed_mph_;
  // @@protoc_insertion_point(copy_constructor:tractor_ctl.GetSpeedResponse)
}

inline void GetSpeedResponse::SharedCtor() {
speed_mph_ = 0;
}

GetSpeedResponse::~GetSpeedResponse() {
  // @@protoc_insertion_point(destructor:tractor_ctl.GetSpeedResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetSpeedResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetSpeedResponse::ArenaDtor(void* object) {
  GetSpeedResponse* _this = reinterpret_cast< GetSpeedResponse* >(object);
  (void)_this;
}
void GetSpeedResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetSpeedResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetSpeedResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:tractor_ctl.GetSpeedResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  speed_mph_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetSpeedResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float speed_mph = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          speed_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetSpeedResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:tractor_ctl.GetSpeedResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float speed_mph = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed_mph = this->_internal_speed_mph();
  uint32_t raw_speed_mph;
  memcpy(&raw_speed_mph, &tmp_speed_mph, sizeof(tmp_speed_mph));
  if (raw_speed_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_speed_mph(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tractor_ctl.GetSpeedResponse)
  return target;
}

size_t GetSpeedResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tractor_ctl.GetSpeedResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float speed_mph = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed_mph = this->_internal_speed_mph();
  uint32_t raw_speed_mph;
  memcpy(&raw_speed_mph, &tmp_speed_mph, sizeof(tmp_speed_mph));
  if (raw_speed_mph != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetSpeedResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetSpeedResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetSpeedResponse::GetClassData() const { return &_class_data_; }

void GetSpeedResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetSpeedResponse *>(to)->MergeFrom(
      static_cast<const GetSpeedResponse &>(from));
}


void GetSpeedResponse::MergeFrom(const GetSpeedResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tractor_ctl.GetSpeedResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed_mph = from._internal_speed_mph();
  uint32_t raw_speed_mph;
  memcpy(&raw_speed_mph, &tmp_speed_mph, sizeof(tmp_speed_mph));
  if (raw_speed_mph != 0) {
    _internal_set_speed_mph(from._internal_speed_mph());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetSpeedResponse::CopyFrom(const GetSpeedResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tractor_ctl.GetSpeedResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetSpeedResponse::IsInitialized() const {
  return true;
}

void GetSpeedResponse::InternalSwap(GetSpeedResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(speed_mph_, other->speed_mph_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetSpeedResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_getter, &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_once,
      file_level_metadata_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto[5]);
}

// ===================================================================

class TractorStateWrapper::_Internal {
 public:
};

TractorStateWrapper::TractorStateWrapper(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:tractor_ctl.TractorStateWrapper)
}
TractorStateWrapper::TractorStateWrapper(const TractorStateWrapper& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_msg().empty()) {
    msg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_msg(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:tractor_ctl.TractorStateWrapper)
}

inline void TractorStateWrapper::SharedCtor() {
msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

TractorStateWrapper::~TractorStateWrapper() {
  // @@protoc_insertion_point(destructor:tractor_ctl.TractorStateWrapper)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TractorStateWrapper::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  msg_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TractorStateWrapper::ArenaDtor(void* object) {
  TractorStateWrapper* _this = reinterpret_cast< TractorStateWrapper* >(object);
  (void)_this;
}
void TractorStateWrapper::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TractorStateWrapper::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TractorStateWrapper::Clear() {
// @@protoc_insertion_point(message_clear_start:tractor_ctl.TractorStateWrapper)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  msg_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TractorStateWrapper::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes msg = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_msg();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TractorStateWrapper::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:tractor_ctl.TractorStateWrapper)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes msg = 1;
  if (!this->_internal_msg().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_msg(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:tractor_ctl.TractorStateWrapper)
  return target;
}

size_t TractorStateWrapper::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:tractor_ctl.TractorStateWrapper)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes msg = 1;
  if (!this->_internal_msg().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_msg());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TractorStateWrapper::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TractorStateWrapper::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TractorStateWrapper::GetClassData() const { return &_class_data_; }

void TractorStateWrapper::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TractorStateWrapper *>(to)->MergeFrom(
      static_cast<const TractorStateWrapper &>(from));
}


void TractorStateWrapper::MergeFrom(const TractorStateWrapper& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:tractor_ctl.TractorStateWrapper)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_msg().empty()) {
    _internal_set_msg(from._internal_msg());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TractorStateWrapper::CopyFrom(const TractorStateWrapper& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:tractor_ctl.TractorStateWrapper)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TractorStateWrapper::IsInitialized() const {
  return true;
}

void TractorStateWrapper::InternalSwap(TractorStateWrapper* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &msg_, lhs_arena,
      &other->msg_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata TractorStateWrapper::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_getter, &descriptor_table_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto_once,
      file_level_metadata_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto[6]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace tractor_ctl
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::tractor_ctl::PingMsg* Arena::CreateMaybeMessage< ::tractor_ctl::PingMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tractor_ctl::PingMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::tractor_ctl::PongMsg* Arena::CreateMaybeMessage< ::tractor_ctl::PongMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tractor_ctl::PongMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::tractor_ctl::ReloadWheelPosRequest* Arena::CreateMaybeMessage< ::tractor_ctl::ReloadWheelPosRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tractor_ctl::ReloadWheelPosRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::tractor_ctl::ReloadWheelPosResponse* Arena::CreateMaybeMessage< ::tractor_ctl::ReloadWheelPosResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tractor_ctl::ReloadWheelPosResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::tractor_ctl::GetSpeedRequest* Arena::CreateMaybeMessage< ::tractor_ctl::GetSpeedRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tractor_ctl::GetSpeedRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::tractor_ctl::GetSpeedResponse* Arena::CreateMaybeMessage< ::tractor_ctl::GetSpeedResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tractor_ctl::GetSpeedResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::tractor_ctl::TractorStateWrapper* Arena::CreateMaybeMessage< ::tractor_ctl::TractorStateWrapper >(Arena* arena) {
  return Arena::CreateMessageInternal< ::tractor_ctl::TractorStateWrapper >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: tractor_ctl/proto/tractor_ctl_service.proto

#include "tractor_ctl/proto/tractor_ctl_service.pb.h"
#include "tractor_ctl/proto/tractor_ctl_service.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace tractor_ctl {

static const char* TractorCtlService_method_names[] = {
  "/tractor_ctl.TractorCtlService/Ping",
  "/tractor_ctl.TractorCtlService/ReloadWheelPos",
  "/tractor_ctl.TractorCtlService/GetSpeed",
  "/tractor_ctl.TractorCtlService/SubscribeTractorState",
  "/tractor_ctl.TractorCtlService/FarmChanged",
};

std::unique_ptr< TractorCtlService::Stub> TractorCtlService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< TractorCtlService::Stub> stub(new TractorCtlService::Stub(channel, options));
  return stub;
}

TractorCtlService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Ping_(TractorCtlService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReloadWheelPos_(TractorCtlService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSpeed_(TractorCtlService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SubscribeTractorState_(TractorCtlService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  , rpcmethod_FarmChanged_(TractorCtlService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status TractorCtlService::Stub::Ping(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::tractor_ctl::PongMsg* response) {
  return ::grpc::internal::BlockingUnaryCall< ::tractor_ctl::PingMsg, ::tractor_ctl::PongMsg, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Ping_, context, request, response);
}

void TractorCtlService::Stub::async::Ping(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg* request, ::tractor_ctl::PongMsg* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::tractor_ctl::PingMsg, ::tractor_ctl::PongMsg, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, std::move(f));
}

void TractorCtlService::Stub::async::Ping(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg* request, ::tractor_ctl::PongMsg* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tractor_ctl::PongMsg>* TractorCtlService::Stub::PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::tractor_ctl::PongMsg, ::tractor_ctl::PingMsg, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Ping_, context, request);
}

::grpc::ClientAsyncResponseReader< ::tractor_ctl::PongMsg>* TractorCtlService::Stub::AsyncPingRaw(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status TractorCtlService::Stub::ReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::tractor_ctl::ReloadWheelPosResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::tractor_ctl::ReloadWheelPosRequest, ::tractor_ctl::ReloadWheelPosResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReloadWheelPos_, context, request, response);
}

void TractorCtlService::Stub::async::ReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest* request, ::tractor_ctl::ReloadWheelPosResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::tractor_ctl::ReloadWheelPosRequest, ::tractor_ctl::ReloadWheelPosResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadWheelPos_, context, request, response, std::move(f));
}

void TractorCtlService::Stub::async::ReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest* request, ::tractor_ctl::ReloadWheelPosResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadWheelPos_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tractor_ctl::ReloadWheelPosResponse>* TractorCtlService::Stub::PrepareAsyncReloadWheelPosRaw(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::tractor_ctl::ReloadWheelPosResponse, ::tractor_ctl::ReloadWheelPosRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReloadWheelPos_, context, request);
}

::grpc::ClientAsyncResponseReader< ::tractor_ctl::ReloadWheelPosResponse>* TractorCtlService::Stub::AsyncReloadWheelPosRaw(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReloadWheelPosRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status TractorCtlService::Stub::GetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::tractor_ctl::GetSpeedResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::tractor_ctl::GetSpeedRequest, ::tractor_ctl::GetSpeedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSpeed_, context, request, response);
}

void TractorCtlService::Stub::async::GetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest* request, ::tractor_ctl::GetSpeedResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::tractor_ctl::GetSpeedRequest, ::tractor_ctl::GetSpeedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSpeed_, context, request, response, std::move(f));
}

void TractorCtlService::Stub::async::GetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest* request, ::tractor_ctl::GetSpeedResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSpeed_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tractor_ctl::GetSpeedResponse>* TractorCtlService::Stub::PrepareAsyncGetSpeedRaw(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::tractor_ctl::GetSpeedResponse, ::tractor_ctl::GetSpeedRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSpeed_, context, request);
}

::grpc::ClientAsyncResponseReader< ::tractor_ctl::GetSpeedResponse>* TractorCtlService::Stub::AsyncGetSpeedRaw(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSpeedRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::ClientReader< ::tractor_ctl::TractorStateWrapper>* TractorCtlService::Stub::SubscribeTractorStateRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request) {
  return ::grpc::internal::ClientReaderFactory< ::tractor_ctl::TractorStateWrapper>::Create(channel_.get(), rpcmethod_SubscribeTractorState_, context, request);
}

void TractorCtlService::Stub::async::SubscribeTractorState(::grpc::ClientContext* context, const ::google::protobuf::Empty* request, ::grpc::ClientReadReactor< ::tractor_ctl::TractorStateWrapper>* reactor) {
  ::grpc::internal::ClientCallbackReaderFactory< ::tractor_ctl::TractorStateWrapper>::Create(stub_->channel_.get(), stub_->rpcmethod_SubscribeTractorState_, context, request, reactor);
}

::grpc::ClientAsyncReader< ::tractor_ctl::TractorStateWrapper>* TractorCtlService::Stub::AsyncSubscribeTractorStateRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::tractor_ctl::TractorStateWrapper>::Create(channel_.get(), cq, rpcmethod_SubscribeTractorState_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::tractor_ctl::TractorStateWrapper>* TractorCtlService::Stub::PrepareAsyncSubscribeTractorStateRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::tractor_ctl::TractorStateWrapper>::Create(channel_.get(), cq, rpcmethod_SubscribeTractorState_, context, request, false, nullptr);
}

::grpc::Status TractorCtlService::Stub::FarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::google::protobuf::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::google::protobuf::Empty, ::google::protobuf::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_FarmChanged_, context, request, response);
}

void TractorCtlService::Stub::async::FarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty* request, ::google::protobuf::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::google::protobuf::Empty, ::google::protobuf::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FarmChanged_, context, request, response, std::move(f));
}

void TractorCtlService::Stub::async::FarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty* request, ::google::protobuf::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FarmChanged_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* TractorCtlService::Stub::PrepareAsyncFarmChangedRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::google::protobuf::Empty, ::google::protobuf::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_FarmChanged_, context, request);
}

::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* TractorCtlService::Stub::AsyncFarmChangedRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncFarmChangedRaw(context, request, cq);
  result->StartCall();
  return result;
}

TractorCtlService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TractorCtlService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TractorCtlService::Service, ::tractor_ctl::PingMsg, ::tractor_ctl::PongMsg, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TractorCtlService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::tractor_ctl::PingMsg* req,
             ::tractor_ctl::PongMsg* resp) {
               return service->Ping(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TractorCtlService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TractorCtlService::Service, ::tractor_ctl::ReloadWheelPosRequest, ::tractor_ctl::ReloadWheelPosResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TractorCtlService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::tractor_ctl::ReloadWheelPosRequest* req,
             ::tractor_ctl::ReloadWheelPosResponse* resp) {
               return service->ReloadWheelPos(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TractorCtlService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TractorCtlService::Service, ::tractor_ctl::GetSpeedRequest, ::tractor_ctl::GetSpeedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TractorCtlService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::tractor_ctl::GetSpeedRequest* req,
             ::tractor_ctl::GetSpeedResponse* resp) {
               return service->GetSpeed(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TractorCtlService_method_names[3],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< TractorCtlService::Service, ::google::protobuf::Empty, ::tractor_ctl::TractorStateWrapper>(
          [](TractorCtlService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::google::protobuf::Empty* req,
             ::grpc::ServerWriter<::tractor_ctl::TractorStateWrapper>* writer) {
               return service->SubscribeTractorState(ctx, req, writer);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TractorCtlService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TractorCtlService::Service, ::google::protobuf::Empty, ::google::protobuf::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TractorCtlService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::google::protobuf::Empty* req,
             ::google::protobuf::Empty* resp) {
               return service->FarmChanged(ctx, req, resp);
             }, this)));
}

TractorCtlService::Service::~Service() {
}

::grpc::Status TractorCtlService::Service::Ping(::grpc::ServerContext* context, const ::tractor_ctl::PingMsg* request, ::tractor_ctl::PongMsg* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TractorCtlService::Service::ReloadWheelPos(::grpc::ServerContext* context, const ::tractor_ctl::ReloadWheelPosRequest* request, ::tractor_ctl::ReloadWheelPosResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TractorCtlService::Service::GetSpeed(::grpc::ServerContext* context, const ::tractor_ctl::GetSpeedRequest* request, ::tractor_ctl::GetSpeedResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TractorCtlService::Service::SubscribeTractorState(::grpc::ServerContext* context, const ::google::protobuf::Empty* request, ::grpc::ServerWriter< ::tractor_ctl::TractorStateWrapper>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TractorCtlService::Service::FarmChanged(::grpc::ServerContext* context, const ::google::protobuf::Empty* request, ::google::protobuf::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace tractor_ctl


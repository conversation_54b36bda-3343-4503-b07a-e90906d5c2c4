// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: tractor_ctl/proto/tractor_ctl_service.proto
#ifndef GRPC_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto__INCLUDED
#define GRPC_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto__INCLUDED

#include "tractor_ctl/proto/tractor_ctl_service.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace tractor_ctl {

class TractorCtlService final {
 public:
  static constexpr char const* service_full_name() {
    return "tractor_ctl.TractorCtlService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status Ping(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::tractor_ctl::PongMsg* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::PongMsg>> AsyncPing(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::PongMsg>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::PongMsg>> PrepareAsyncPing(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::PongMsg>>(PrepareAsyncPingRaw(context, request, cq));
    }
    virtual ::grpc::Status ReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::tractor_ctl::ReloadWheelPosResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::ReloadWheelPosResponse>> AsyncReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::ReloadWheelPosResponse>>(AsyncReloadWheelPosRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::ReloadWheelPosResponse>> PrepareAsyncReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::ReloadWheelPosResponse>>(PrepareAsyncReloadWheelPosRaw(context, request, cq));
    }
    virtual ::grpc::Status GetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::tractor_ctl::GetSpeedResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::GetSpeedResponse>> AsyncGetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::GetSpeedResponse>>(AsyncGetSpeedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::GetSpeedResponse>> PrepareAsyncGetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::GetSpeedResponse>>(PrepareAsyncGetSpeedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderInterface< ::tractor_ctl::TractorStateWrapper>> SubscribeTractorState(::grpc::ClientContext* context, const ::google::protobuf::Empty& request) {
      return std::unique_ptr< ::grpc::ClientReaderInterface< ::tractor_ctl::TractorStateWrapper>>(SubscribeTractorStateRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::tractor_ctl::TractorStateWrapper>> AsyncSubscribeTractorState(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::tractor_ctl::TractorStateWrapper>>(AsyncSubscribeTractorStateRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::tractor_ctl::TractorStateWrapper>> PrepareAsyncSubscribeTractorState(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::tractor_ctl::TractorStateWrapper>>(PrepareAsyncSubscribeTractorStateRaw(context, request, cq));
    }
    virtual ::grpc::Status FarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::google::protobuf::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>> AsyncFarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>>(AsyncFarmChangedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>> PrepareAsyncFarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>>(PrepareAsyncFarmChangedRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void Ping(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg* request, ::tractor_ctl::PongMsg* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Ping(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg* request, ::tractor_ctl::PongMsg* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest* request, ::tractor_ctl::ReloadWheelPosResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest* request, ::tractor_ctl::ReloadWheelPosResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest* request, ::tractor_ctl::GetSpeedResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest* request, ::tractor_ctl::GetSpeedResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SubscribeTractorState(::grpc::ClientContext* context, const ::google::protobuf::Empty* request, ::grpc::ClientReadReactor< ::tractor_ctl::TractorStateWrapper>* reactor) = 0;
      virtual void FarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty* request, ::google::protobuf::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void FarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty* request, ::google::protobuf::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::PongMsg>* AsyncPingRaw(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::PongMsg>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::ReloadWheelPosResponse>* AsyncReloadWheelPosRaw(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::ReloadWheelPosResponse>* PrepareAsyncReloadWheelPosRaw(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::GetSpeedResponse>* AsyncGetSpeedRaw(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tractor_ctl::GetSpeedResponse>* PrepareAsyncGetSpeedRaw(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderInterface< ::tractor_ctl::TractorStateWrapper>* SubscribeTractorStateRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::tractor_ctl::TractorStateWrapper>* AsyncSubscribeTractorStateRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::tractor_ctl::TractorStateWrapper>* PrepareAsyncSubscribeTractorStateRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>* AsyncFarmChangedRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>* PrepareAsyncFarmChangedRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status Ping(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::tractor_ctl::PongMsg* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::PongMsg>> AsyncPing(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::PongMsg>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::PongMsg>> PrepareAsyncPing(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::PongMsg>>(PrepareAsyncPingRaw(context, request, cq));
    }
    ::grpc::Status ReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::tractor_ctl::ReloadWheelPosResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::ReloadWheelPosResponse>> AsyncReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::ReloadWheelPosResponse>>(AsyncReloadWheelPosRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::ReloadWheelPosResponse>> PrepareAsyncReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::ReloadWheelPosResponse>>(PrepareAsyncReloadWheelPosRaw(context, request, cq));
    }
    ::grpc::Status GetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::tractor_ctl::GetSpeedResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::GetSpeedResponse>> AsyncGetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::GetSpeedResponse>>(AsyncGetSpeedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::GetSpeedResponse>> PrepareAsyncGetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tractor_ctl::GetSpeedResponse>>(PrepareAsyncGetSpeedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReader< ::tractor_ctl::TractorStateWrapper>> SubscribeTractorState(::grpc::ClientContext* context, const ::google::protobuf::Empty& request) {
      return std::unique_ptr< ::grpc::ClientReader< ::tractor_ctl::TractorStateWrapper>>(SubscribeTractorStateRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::tractor_ctl::TractorStateWrapper>> AsyncSubscribeTractorState(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::tractor_ctl::TractorStateWrapper>>(AsyncSubscribeTractorStateRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::tractor_ctl::TractorStateWrapper>> PrepareAsyncSubscribeTractorState(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::tractor_ctl::TractorStateWrapper>>(PrepareAsyncSubscribeTractorStateRaw(context, request, cq));
    }
    ::grpc::Status FarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::google::protobuf::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>> AsyncFarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>>(AsyncFarmChangedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>> PrepareAsyncFarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>>(PrepareAsyncFarmChangedRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void Ping(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg* request, ::tractor_ctl::PongMsg* response, std::function<void(::grpc::Status)>) override;
      void Ping(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg* request, ::tractor_ctl::PongMsg* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest* request, ::tractor_ctl::ReloadWheelPosResponse* response, std::function<void(::grpc::Status)>) override;
      void ReloadWheelPos(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest* request, ::tractor_ctl::ReloadWheelPosResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest* request, ::tractor_ctl::GetSpeedResponse* response, std::function<void(::grpc::Status)>) override;
      void GetSpeed(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest* request, ::tractor_ctl::GetSpeedResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SubscribeTractorState(::grpc::ClientContext* context, const ::google::protobuf::Empty* request, ::grpc::ClientReadReactor< ::tractor_ctl::TractorStateWrapper>* reactor) override;
      void FarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty* request, ::google::protobuf::Empty* response, std::function<void(::grpc::Status)>) override;
      void FarmChanged(::grpc::ClientContext* context, const ::google::protobuf::Empty* request, ::google::protobuf::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::tractor_ctl::PongMsg>* AsyncPingRaw(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tractor_ctl::PongMsg>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::tractor_ctl::PingMsg& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tractor_ctl::ReloadWheelPosResponse>* AsyncReloadWheelPosRaw(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tractor_ctl::ReloadWheelPosResponse>* PrepareAsyncReloadWheelPosRaw(::grpc::ClientContext* context, const ::tractor_ctl::ReloadWheelPosRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tractor_ctl::GetSpeedResponse>* AsyncGetSpeedRaw(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tractor_ctl::GetSpeedResponse>* PrepareAsyncGetSpeedRaw(::grpc::ClientContext* context, const ::tractor_ctl::GetSpeedRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReader< ::tractor_ctl::TractorStateWrapper>* SubscribeTractorStateRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request) override;
    ::grpc::ClientAsyncReader< ::tractor_ctl::TractorStateWrapper>* AsyncSubscribeTractorStateRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReader< ::tractor_ctl::TractorStateWrapper>* PrepareAsyncSubscribeTractorStateRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* AsyncFarmChangedRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* PrepareAsyncFarmChangedRaw(::grpc::ClientContext* context, const ::google::protobuf::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Ping_;
    const ::grpc::internal::RpcMethod rpcmethod_ReloadWheelPos_;
    const ::grpc::internal::RpcMethod rpcmethod_GetSpeed_;
    const ::grpc::internal::RpcMethod rpcmethod_SubscribeTractorState_;
    const ::grpc::internal::RpcMethod rpcmethod_FarmChanged_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Ping(::grpc::ServerContext* context, const ::tractor_ctl::PingMsg* request, ::tractor_ctl::PongMsg* response);
    virtual ::grpc::Status ReloadWheelPos(::grpc::ServerContext* context, const ::tractor_ctl::ReloadWheelPosRequest* request, ::tractor_ctl::ReloadWheelPosResponse* response);
    virtual ::grpc::Status GetSpeed(::grpc::ServerContext* context, const ::tractor_ctl::GetSpeedRequest* request, ::tractor_ctl::GetSpeedResponse* response);
    virtual ::grpc::Status SubscribeTractorState(::grpc::ServerContext* context, const ::google::protobuf::Empty* request, ::grpc::ServerWriter< ::tractor_ctl::TractorStateWrapper>* writer);
    virtual ::grpc::Status FarmChanged(::grpc::ServerContext* context, const ::google::protobuf::Empty* request, ::google::protobuf::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Ping() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::tractor_ctl::PingMsg* /*request*/, ::tractor_ctl::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::tractor_ctl::PingMsg* request, ::grpc::ServerAsyncResponseWriter< ::tractor_ctl::PongMsg>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ReloadWheelPos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ReloadWheelPos() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_ReloadWheelPos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReloadWheelPos(::grpc::ServerContext* /*context*/, const ::tractor_ctl::ReloadWheelPosRequest* /*request*/, ::tractor_ctl::ReloadWheelPosResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReloadWheelPos(::grpc::ServerContext* context, ::tractor_ctl::ReloadWheelPosRequest* request, ::grpc::ServerAsyncResponseWriter< ::tractor_ctl::ReloadWheelPosResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetSpeed() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSpeed(::grpc::ServerContext* /*context*/, const ::tractor_ctl::GetSpeedRequest* /*request*/, ::tractor_ctl::GetSpeedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSpeed(::grpc::ServerContext* context, ::tractor_ctl::GetSpeedRequest* request, ::grpc::ServerAsyncResponseWriter< ::tractor_ctl::GetSpeedResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SubscribeTractorState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SubscribeTractorState() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_SubscribeTractorState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SubscribeTractorState(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::grpc::ServerWriter< ::tractor_ctl::TractorStateWrapper>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSubscribeTractorState(::grpc::ServerContext* context, ::google::protobuf::Empty* request, ::grpc::ServerAsyncWriter< ::tractor_ctl::TractorStateWrapper>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(3, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_FarmChanged : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_FarmChanged() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_FarmChanged() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FarmChanged(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFarmChanged(::grpc::ServerContext* context, ::google::protobuf::Empty* request, ::grpc::ServerAsyncResponseWriter< ::google::protobuf::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Ping<WithAsyncMethod_ReloadWheelPos<WithAsyncMethod_GetSpeed<WithAsyncMethod_SubscribeTractorState<WithAsyncMethod_FarmChanged<Service > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::tractor_ctl::PingMsg, ::tractor_ctl::PongMsg>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::tractor_ctl::PingMsg* request, ::tractor_ctl::PongMsg* response) { return this->Ping(context, request, response); }));}
    void SetMessageAllocatorFor_Ping(
        ::grpc::MessageAllocator< ::tractor_ctl::PingMsg, ::tractor_ctl::PongMsg>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::tractor_ctl::PingMsg, ::tractor_ctl::PongMsg>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::tractor_ctl::PingMsg* /*request*/, ::tractor_ctl::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::tractor_ctl::PingMsg* /*request*/, ::tractor_ctl::PongMsg* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ReloadWheelPos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ReloadWheelPos() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::tractor_ctl::ReloadWheelPosRequest, ::tractor_ctl::ReloadWheelPosResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::tractor_ctl::ReloadWheelPosRequest* request, ::tractor_ctl::ReloadWheelPosResponse* response) { return this->ReloadWheelPos(context, request, response); }));}
    void SetMessageAllocatorFor_ReloadWheelPos(
        ::grpc::MessageAllocator< ::tractor_ctl::ReloadWheelPosRequest, ::tractor_ctl::ReloadWheelPosResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::tractor_ctl::ReloadWheelPosRequest, ::tractor_ctl::ReloadWheelPosResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ReloadWheelPos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReloadWheelPos(::grpc::ServerContext* /*context*/, const ::tractor_ctl::ReloadWheelPosRequest* /*request*/, ::tractor_ctl::ReloadWheelPosResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ReloadWheelPos(
      ::grpc::CallbackServerContext* /*context*/, const ::tractor_ctl::ReloadWheelPosRequest* /*request*/, ::tractor_ctl::ReloadWheelPosResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetSpeed() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::tractor_ctl::GetSpeedRequest, ::tractor_ctl::GetSpeedResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::tractor_ctl::GetSpeedRequest* request, ::tractor_ctl::GetSpeedResponse* response) { return this->GetSpeed(context, request, response); }));}
    void SetMessageAllocatorFor_GetSpeed(
        ::grpc::MessageAllocator< ::tractor_ctl::GetSpeedRequest, ::tractor_ctl::GetSpeedResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::tractor_ctl::GetSpeedRequest, ::tractor_ctl::GetSpeedResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSpeed(::grpc::ServerContext* /*context*/, const ::tractor_ctl::GetSpeedRequest* /*request*/, ::tractor_ctl::GetSpeedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSpeed(
      ::grpc::CallbackServerContext* /*context*/, const ::tractor_ctl::GetSpeedRequest* /*request*/, ::tractor_ctl::GetSpeedResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SubscribeTractorState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SubscribeTractorState() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackServerStreamingHandler< ::google::protobuf::Empty, ::tractor_ctl::TractorStateWrapper>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::google::protobuf::Empty* request) { return this->SubscribeTractorState(context, request); }));
    }
    ~WithCallbackMethod_SubscribeTractorState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SubscribeTractorState(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::grpc::ServerWriter< ::tractor_ctl::TractorStateWrapper>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::tractor_ctl::TractorStateWrapper>* SubscribeTractorState(
      ::grpc::CallbackServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_FarmChanged : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_FarmChanged() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::google::protobuf::Empty, ::google::protobuf::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::google::protobuf::Empty* request, ::google::protobuf::Empty* response) { return this->FarmChanged(context, request, response); }));}
    void SetMessageAllocatorFor_FarmChanged(
        ::grpc::MessageAllocator< ::google::protobuf::Empty, ::google::protobuf::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::google::protobuf::Empty, ::google::protobuf::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_FarmChanged() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FarmChanged(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* FarmChanged(
      ::grpc::CallbackServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::google::protobuf::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_Ping<WithCallbackMethod_ReloadWheelPos<WithCallbackMethod_GetSpeed<WithCallbackMethod_SubscribeTractorState<WithCallbackMethod_FarmChanged<Service > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Ping() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::tractor_ctl::PingMsg* /*request*/, ::tractor_ctl::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ReloadWheelPos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ReloadWheelPos() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_ReloadWheelPos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReloadWheelPos(::grpc::ServerContext* /*context*/, const ::tractor_ctl::ReloadWheelPosRequest* /*request*/, ::tractor_ctl::ReloadWheelPosResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetSpeed() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSpeed(::grpc::ServerContext* /*context*/, const ::tractor_ctl::GetSpeedRequest* /*request*/, ::tractor_ctl::GetSpeedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SubscribeTractorState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SubscribeTractorState() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_SubscribeTractorState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SubscribeTractorState(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::grpc::ServerWriter< ::tractor_ctl::TractorStateWrapper>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_FarmChanged : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_FarmChanged() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_FarmChanged() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FarmChanged(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Ping() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::tractor_ctl::PingMsg* /*request*/, ::tractor_ctl::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ReloadWheelPos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ReloadWheelPos() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_ReloadWheelPos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReloadWheelPos(::grpc::ServerContext* /*context*/, const ::tractor_ctl::ReloadWheelPosRequest* /*request*/, ::tractor_ctl::ReloadWheelPosResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReloadWheelPos(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetSpeed() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSpeed(::grpc::ServerContext* /*context*/, const ::tractor_ctl::GetSpeedRequest* /*request*/, ::tractor_ctl::GetSpeedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSpeed(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SubscribeTractorState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SubscribeTractorState() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_SubscribeTractorState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SubscribeTractorState(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::grpc::ServerWriter< ::tractor_ctl::TractorStateWrapper>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSubscribeTractorState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncWriter< ::grpc::ByteBuffer>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(3, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_FarmChanged : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_FarmChanged() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_FarmChanged() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FarmChanged(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFarmChanged(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Ping(context, request, response); }));
    }
    ~WithRawCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::tractor_ctl::PingMsg* /*request*/, ::tractor_ctl::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ReloadWheelPos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ReloadWheelPos() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ReloadWheelPos(context, request, response); }));
    }
    ~WithRawCallbackMethod_ReloadWheelPos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReloadWheelPos(::grpc::ServerContext* /*context*/, const ::tractor_ctl::ReloadWheelPosRequest* /*request*/, ::tractor_ctl::ReloadWheelPosResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ReloadWheelPos(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetSpeed() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetSpeed(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSpeed(::grpc::ServerContext* /*context*/, const ::tractor_ctl::GetSpeedRequest* /*request*/, ::tractor_ctl::GetSpeedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSpeed(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SubscribeTractorState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SubscribeTractorState() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackServerStreamingHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const::grpc::ByteBuffer* request) { return this->SubscribeTractorState(context, request); }));
    }
    ~WithRawCallbackMethod_SubscribeTractorState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SubscribeTractorState(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::grpc::ServerWriter< ::tractor_ctl::TractorStateWrapper>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::grpc::ByteBuffer>* SubscribeTractorState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_FarmChanged : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_FarmChanged() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->FarmChanged(context, request, response); }));
    }
    ~WithRawCallbackMethod_FarmChanged() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FarmChanged(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* FarmChanged(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Ping() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::tractor_ctl::PingMsg, ::tractor_ctl::PongMsg>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::tractor_ctl::PingMsg, ::tractor_ctl::PongMsg>* streamer) {
                       return this->StreamedPing(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::tractor_ctl::PingMsg* /*request*/, ::tractor_ctl::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedPing(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tractor_ctl::PingMsg,::tractor_ctl::PongMsg>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ReloadWheelPos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ReloadWheelPos() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::tractor_ctl::ReloadWheelPosRequest, ::tractor_ctl::ReloadWheelPosResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::tractor_ctl::ReloadWheelPosRequest, ::tractor_ctl::ReloadWheelPosResponse>* streamer) {
                       return this->StreamedReloadWheelPos(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ReloadWheelPos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ReloadWheelPos(::grpc::ServerContext* /*context*/, const ::tractor_ctl::ReloadWheelPosRequest* /*request*/, ::tractor_ctl::ReloadWheelPosResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedReloadWheelPos(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tractor_ctl::ReloadWheelPosRequest,::tractor_ctl::ReloadWheelPosResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetSpeed() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::tractor_ctl::GetSpeedRequest, ::tractor_ctl::GetSpeedResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::tractor_ctl::GetSpeedRequest, ::tractor_ctl::GetSpeedResponse>* streamer) {
                       return this->StreamedGetSpeed(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetSpeed(::grpc::ServerContext* /*context*/, const ::tractor_ctl::GetSpeedRequest* /*request*/, ::tractor_ctl::GetSpeedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetSpeed(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tractor_ctl::GetSpeedRequest,::tractor_ctl::GetSpeedResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_FarmChanged : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_FarmChanged() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::google::protobuf::Empty, ::google::protobuf::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::google::protobuf::Empty, ::google::protobuf::Empty>* streamer) {
                       return this->StreamedFarmChanged(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_FarmChanged() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status FarmChanged(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedFarmChanged(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::google::protobuf::Empty,::google::protobuf::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_ReloadWheelPos<WithStreamedUnaryMethod_GetSpeed<WithStreamedUnaryMethod_FarmChanged<Service > > > > StreamedUnaryService;
  template <class BaseClass>
  class WithSplitStreamingMethod_SubscribeTractorState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithSplitStreamingMethod_SubscribeTractorState() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::SplitServerStreamingHandler<
          ::google::protobuf::Empty, ::tractor_ctl::TractorStateWrapper>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerSplitStreamer<
                     ::google::protobuf::Empty, ::tractor_ctl::TractorStateWrapper>* streamer) {
                       return this->StreamedSubscribeTractorState(context,
                         streamer);
                  }));
    }
    ~WithSplitStreamingMethod_SubscribeTractorState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SubscribeTractorState(::grpc::ServerContext* /*context*/, const ::google::protobuf::Empty* /*request*/, ::grpc::ServerWriter< ::tractor_ctl::TractorStateWrapper>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with split streamed
    virtual ::grpc::Status StreamedSubscribeTractorState(::grpc::ServerContext* context, ::grpc::ServerSplitStreamer< ::google::protobuf::Empty,::tractor_ctl::TractorStateWrapper>* server_split_streamer) = 0;
  };
  typedef WithSplitStreamingMethod_SubscribeTractorState<Service > SplitStreamedService;
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_ReloadWheelPos<WithStreamedUnaryMethod_GetSpeed<WithSplitStreamingMethod_SubscribeTractorState<WithStreamedUnaryMethod_FarmChanged<Service > > > > > StreamedService;
};

}  // namespace tractor_ctl


#endif  // GRPC_tractor_5fctl_2fproto_2ftractor_5fctl_5fservice_2eproto__INCLUDED

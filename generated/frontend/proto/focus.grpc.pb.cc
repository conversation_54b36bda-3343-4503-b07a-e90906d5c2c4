// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/focus.proto

#include "frontend/proto/focus.pb.h"
#include "frontend/proto/focus.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace focus {

static const char* FocusService_method_names[] = {
  "/carbon.frontend.focus.FocusService/TogglePredictGridView",
  "/carbon.frontend.focus.FocusService/GetNextFocusState",
  "/carbon.frontend.focus.FocusService/StartAutoFocusSpecific",
  "/carbon.frontend.focus.FocusService/StartAutoFocusAll",
  "/carbon.frontend.focus.FocusService/StopAutoFocus",
  "/carbon.frontend.focus.FocusService/SetLensValue",
};

std::unique_ptr< FocusService::Stub> FocusService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< FocusService::Stub> stub(new FocusService::Stub(channel, options));
  return stub;
}

FocusService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_TogglePredictGridView_(FocusService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextFocusState_(FocusService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartAutoFocusSpecific_(FocusService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartAutoFocusAll_(FocusService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StopAutoFocus_(FocusService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetLensValue_(FocusService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status FocusService::Stub::TogglePredictGridView(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_TogglePredictGridView_, context, request, response);
}

void FocusService::Stub::async::TogglePredictGridView(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TogglePredictGridView_, context, request, response, std::move(f));
}

void FocusService::Stub::async::TogglePredictGridView(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TogglePredictGridView_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* FocusService::Stub::PrepareAsyncTogglePredictGridViewRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_TogglePredictGridView_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* FocusService::Stub::AsyncTogglePredictGridViewRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncTogglePredictGridViewRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status FocusService::Stub::GetNextFocusState(::grpc::ClientContext* context, const ::carbon::frontend::focus::FocusStateRequest& request, ::carbon::frontend::focus::FocusState* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::focus::FocusStateRequest, ::carbon::frontend::focus::FocusState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextFocusState_, context, request, response);
}

void FocusService::Stub::async::GetNextFocusState(::grpc::ClientContext* context, const ::carbon::frontend::focus::FocusStateRequest* request, ::carbon::frontend::focus::FocusState* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::focus::FocusStateRequest, ::carbon::frontend::focus::FocusState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextFocusState_, context, request, response, std::move(f));
}

void FocusService::Stub::async::GetNextFocusState(::grpc::ClientContext* context, const ::carbon::frontend::focus::FocusStateRequest* request, ::carbon::frontend::focus::FocusState* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextFocusState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::focus::FocusState>* FocusService::Stub::PrepareAsyncGetNextFocusStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::focus::FocusStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::focus::FocusState, ::carbon::frontend::focus::FocusStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextFocusState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::focus::FocusState>* FocusService::Stub::AsyncGetNextFocusStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::focus::FocusStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextFocusStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status FocusService::Stub::StartAutoFocusSpecific(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartAutoFocusSpecific_, context, request, response);
}

void FocusService::Stub::async::StartAutoFocusSpecific(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoFocusSpecific_, context, request, response, std::move(f));
}

void FocusService::Stub::async::StartAutoFocusSpecific(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoFocusSpecific_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* FocusService::Stub::PrepareAsyncStartAutoFocusSpecificRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::camera::CameraRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartAutoFocusSpecific_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* FocusService::Stub::AsyncStartAutoFocusSpecificRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartAutoFocusSpecificRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status FocusService::Stub::StartAutoFocusAll(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartAutoFocusAll_, context, request, response);
}

void FocusService::Stub::async::StartAutoFocusAll(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoFocusAll_, context, request, response, std::move(f));
}

void FocusService::Stub::async::StartAutoFocusAll(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoFocusAll_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* FocusService::Stub::PrepareAsyncStartAutoFocusAllRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartAutoFocusAll_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* FocusService::Stub::AsyncStartAutoFocusAllRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartAutoFocusAllRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status FocusService::Stub::StopAutoFocus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StopAutoFocus_, context, request, response);
}

void FocusService::Stub::async::StopAutoFocus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopAutoFocus_, context, request, response, std::move(f));
}

void FocusService::Stub::async::StopAutoFocus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopAutoFocus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* FocusService::Stub::PrepareAsyncStopAutoFocusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StopAutoFocus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* FocusService::Stub::AsyncStopAutoFocusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStopAutoFocusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status FocusService::Stub::SetLensValue(::grpc::ClientContext* context, const ::carbon::frontend::focus::LensSetRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::focus::LensSetRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetLensValue_, context, request, response);
}

void FocusService::Stub::async::SetLensValue(::grpc::ClientContext* context, const ::carbon::frontend::focus::LensSetRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::focus::LensSetRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLensValue_, context, request, response, std::move(f));
}

void FocusService::Stub::async::SetLensValue(::grpc::ClientContext* context, const ::carbon::frontend::focus::LensSetRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLensValue_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* FocusService::Stub::PrepareAsyncSetLensValueRaw(::grpc::ClientContext* context, const ::carbon::frontend::focus::LensSetRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::focus::LensSetRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetLensValue_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* FocusService::Stub::AsyncSetLensValueRaw(::grpc::ClientContext* context, const ::carbon::frontend::focus::LensSetRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetLensValueRaw(context, request, cq);
  result->StartCall();
  return result;
}

FocusService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      FocusService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< FocusService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](FocusService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->TogglePredictGridView(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      FocusService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< FocusService::Service, ::carbon::frontend::focus::FocusStateRequest, ::carbon::frontend::focus::FocusState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](FocusService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::focus::FocusStateRequest* req,
             ::carbon::frontend::focus::FocusState* resp) {
               return service->GetNextFocusState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      FocusService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< FocusService::Service, ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](FocusService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::camera::CameraRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartAutoFocusSpecific(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      FocusService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< FocusService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](FocusService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartAutoFocusAll(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      FocusService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< FocusService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](FocusService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StopAutoFocus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      FocusService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< FocusService::Service, ::carbon::frontend::focus::LensSetRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](FocusService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::focus::LensSetRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetLensValue(ctx, req, resp);
             }, this)));
}

FocusService::Service::~Service() {
}

::grpc::Status FocusService::Service::TogglePredictGridView(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status FocusService::Service::GetNextFocusState(::grpc::ServerContext* context, const ::carbon::frontend::focus::FocusStateRequest* request, ::carbon::frontend::focus::FocusState* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status FocusService::Service::StartAutoFocusSpecific(::grpc::ServerContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status FocusService::Service::StartAutoFocusAll(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status FocusService::Service::StopAutoFocus(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status FocusService::Service::SetLensValue(::grpc::ServerContext* context, const ::carbon::frontend::focus::LensSetRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace focus


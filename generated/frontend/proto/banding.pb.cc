// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/banding.proto

#include "frontend/proto/banding.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace banding {
constexpr BandingRow::BandingRow(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bands_()
  , row_id_(0){}
struct BandingRowDefaultTypeInternal {
  constexpr BandingRowDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BandingRowDefaultTypeInternal() {}
  union {
    BandingRow _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BandingRowDefaultTypeInternal _BandingRow_default_instance_;
constexpr BandingDef::BandingDef(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : rows_()
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct BandingDefDefaultTypeInternal {
  constexpr BandingDefDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BandingDefDefaultTypeInternal() {}
  union {
    BandingDef _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BandingDefDefaultTypeInternal _BandingDef_default_instance_;
constexpr SaveBandingDefRequest::SaveBandingDefRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bandingdef_(nullptr)
  , setactive_(false){}
struct SaveBandingDefRequestDefaultTypeInternal {
  constexpr SaveBandingDefRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SaveBandingDefRequestDefaultTypeInternal() {}
  union {
    SaveBandingDefRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SaveBandingDefRequestDefaultTypeInternal _SaveBandingDefRequest_default_instance_;
constexpr LoadBandingDefsResponse::LoadBandingDefsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bandingdefs_()
  , activedef_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , activedefuuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct LoadBandingDefsResponseDefaultTypeInternal {
  constexpr LoadBandingDefsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LoadBandingDefsResponseDefaultTypeInternal() {}
  union {
    LoadBandingDefsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LoadBandingDefsResponseDefaultTypeInternal _LoadBandingDefsResponse_default_instance_;
constexpr SetActiveBandingDefRequest::SetActiveBandingDefRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SetActiveBandingDefRequestDefaultTypeInternal {
  constexpr SetActiveBandingDefRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetActiveBandingDefRequestDefaultTypeInternal() {}
  union {
    SetActiveBandingDefRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetActiveBandingDefRequestDefaultTypeInternal _SetActiveBandingDefRequest_default_instance_;
constexpr GetActiveBandingDefResponse::GetActiveBandingDefResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetActiveBandingDefResponseDefaultTypeInternal {
  constexpr GetActiveBandingDefResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetActiveBandingDefResponseDefaultTypeInternal() {}
  union {
    GetActiveBandingDefResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetActiveBandingDefResponseDefaultTypeInternal _GetActiveBandingDefResponse_default_instance_;
constexpr DeleteBandingDefRequest::DeleteBandingDefRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DeleteBandingDefRequestDefaultTypeInternal {
  constexpr DeleteBandingDefRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeleteBandingDefRequestDefaultTypeInternal() {}
  union {
    DeleteBandingDefRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeleteBandingDefRequestDefaultTypeInternal _DeleteBandingDefRequest_default_instance_;
constexpr VisualizationData::VisualizationData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_mm_(0)
  , y_mm_(0)
  , z_mm_(0)
  , is_weed_(false){}
struct VisualizationDataDefaultTypeInternal {
  constexpr VisualizationDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VisualizationDataDefaultTypeInternal() {}
  union {
    VisualizationData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VisualizationDataDefaultTypeInternal _VisualizationData_default_instance_;
constexpr GetNextVisualizationDataRequest::GetNextVisualizationDataRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : types_to_include_()
  , _types_to_include_cached_byte_size_(0)
  , ts_(nullptr)
  , threshold_filters_(nullptr)
  , row_id_(0){}
struct GetNextVisualizationDataRequestDefaultTypeInternal {
  constexpr GetNextVisualizationDataRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextVisualizationDataRequestDefaultTypeInternal() {}
  union {
    GetNextVisualizationDataRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextVisualizationDataRequestDefaultTypeInternal _GetNextVisualizationDataRequest_default_instance_;
constexpr GetNextVisualizationDataResponse::GetNextVisualizationDataResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : data_()
  , bands_()
  , ts_(nullptr){}
struct GetNextVisualizationDataResponseDefaultTypeInternal {
  constexpr GetNextVisualizationDataResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextVisualizationDataResponseDefaultTypeInternal() {}
  union {
    GetNextVisualizationDataResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextVisualizationDataResponseDefaultTypeInternal _GetNextVisualizationDataResponse_default_instance_;
constexpr GetNextVisualizationData2Response::GetNextVisualizationData2Response(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : data_(nullptr)
  , ts_(nullptr){}
struct GetNextVisualizationData2ResponseDefaultTypeInternal {
  constexpr GetNextVisualizationData2ResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextVisualizationData2ResponseDefaultTypeInternal() {}
  union {
    GetNextVisualizationData2Response _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextVisualizationData2ResponseDefaultTypeInternal _GetNextVisualizationData2Response_default_instance_;
constexpr GetDimensionsRequest::GetDimensionsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : row_id_(0){}
struct GetDimensionsRequestDefaultTypeInternal {
  constexpr GetDimensionsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetDimensionsRequestDefaultTypeInternal() {}
  union {
    GetDimensionsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetDimensionsRequestDefaultTypeInternal _GetDimensionsRequest_default_instance_;
constexpr SetBandingEnabledRequest::SetBandingEnabledRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enabled_(false){}
struct SetBandingEnabledRequestDefaultTypeInternal {
  constexpr SetBandingEnabledRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetBandingEnabledRequestDefaultTypeInternal() {}
  union {
    SetBandingEnabledRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetBandingEnabledRequestDefaultTypeInternal _SetBandingEnabledRequest_default_instance_;
constexpr SetBandingEnabledResponse::SetBandingEnabledResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetBandingEnabledResponseDefaultTypeInternal {
  constexpr SetBandingEnabledResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetBandingEnabledResponseDefaultTypeInternal() {}
  union {
    SetBandingEnabledResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetBandingEnabledResponseDefaultTypeInternal _SetBandingEnabledResponse_default_instance_;
constexpr IsBandingEnabledResponse::IsBandingEnabledResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enabled_(false){}
struct IsBandingEnabledResponseDefaultTypeInternal {
  constexpr IsBandingEnabledResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~IsBandingEnabledResponseDefaultTypeInternal() {}
  union {
    IsBandingEnabledResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT IsBandingEnabledResponseDefaultTypeInternal _IsBandingEnabledResponse_default_instance_;
constexpr GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUseDefaultTypeInternal {
  constexpr GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUseDefaultTypeInternal _GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse_default_instance_;
constexpr GetVisualizationMetadataResponse::GetVisualizationMetadataResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_safety_radius_mm_per_row_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct GetVisualizationMetadataResponseDefaultTypeInternal {
  constexpr GetVisualizationMetadataResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetVisualizationMetadataResponseDefaultTypeInternal() {}
  union {
    GetVisualizationMetadataResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetVisualizationMetadataResponseDefaultTypeInternal _GetVisualizationMetadataResponse_default_instance_;
constexpr ThresholdFilter::ThresholdFilter(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : weeding_(0)

  , thinning_(0)

  , banding_(0)
{}
struct ThresholdFilterDefaultTypeInternal {
  constexpr ThresholdFilterDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ThresholdFilterDefaultTypeInternal() {}
  union {
    ThresholdFilter _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ThresholdFilterDefaultTypeInternal _ThresholdFilter_default_instance_;
constexpr ThresholdFilters::ThresholdFilters(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_(nullptr)
  , weed_(nullptr){}
struct ThresholdFiltersDefaultTypeInternal {
  constexpr ThresholdFiltersDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ThresholdFiltersDefaultTypeInternal() {}
  union {
    ThresholdFilters _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ThresholdFiltersDefaultTypeInternal _ThresholdFilters_default_instance_;
constexpr GetNextVisualizationDataForAllRowsRequest::GetNextVisualizationDataForAllRowsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : types_to_include_()
  , _types_to_include_cached_byte_size_(0)
  , ts_(nullptr)
  , threshold_filters_(nullptr){}
struct GetNextVisualizationDataForAllRowsRequestDefaultTypeInternal {
  constexpr GetNextVisualizationDataForAllRowsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextVisualizationDataForAllRowsRequestDefaultTypeInternal() {}
  union {
    GetNextVisualizationDataForAllRowsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextVisualizationDataForAllRowsRequestDefaultTypeInternal _GetNextVisualizationDataForAllRowsRequest_default_instance_;
constexpr GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUseDefaultTypeInternal {
  constexpr GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUseDefaultTypeInternal _GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse_default_instance_;
constexpr GetNextVisualizationDataForAllRowsResponse::GetNextVisualizationDataForAllRowsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : data_per_row_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , types_to_include_()
  , _types_to_include_cached_byte_size_(0)
  , ts_(nullptr){}
struct GetNextVisualizationDataForAllRowsResponseDefaultTypeInternal {
  constexpr GetNextVisualizationDataForAllRowsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextVisualizationDataForAllRowsResponseDefaultTypeInternal() {}
  union {
    GetNextVisualizationDataForAllRowsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextVisualizationDataForAllRowsResponseDefaultTypeInternal _GetNextVisualizationDataForAllRowsResponse_default_instance_;
constexpr GetNextBandingStateResponse::GetNextBandingStateResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bandingdefs_()
  , activedefuuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr)
  , is_banding_enabled_(false)
  , is_dynamic_banding_enabled_(false){}
struct GetNextBandingStateResponseDefaultTypeInternal {
  constexpr GetNextBandingStateResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextBandingStateResponseDefaultTypeInternal() {}
  union {
    GetNextBandingStateResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextBandingStateResponseDefaultTypeInternal _GetNextBandingStateResponse_default_instance_;
}  // namespace banding
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fbanding_2eproto[23];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_frontend_2fproto_2fbanding_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fbanding_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fbanding_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::BandingRow, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::BandingRow, row_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::BandingRow, bands_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::BandingDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::BandingDef, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::BandingDef, rows_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::BandingDef, uuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::SaveBandingDefRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::SaveBandingDefRequest, bandingdef_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::SaveBandingDefRequest, setactive_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::LoadBandingDefsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::LoadBandingDefsResponse, bandingdefs_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::LoadBandingDefsResponse, activedef_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::LoadBandingDefsResponse, activedefuuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::SetActiveBandingDefRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::SetActiveBandingDefRequest, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::SetActiveBandingDefRequest, uuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetActiveBandingDefResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetActiveBandingDefResponse, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetActiveBandingDefResponse, uuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::DeleteBandingDefRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::DeleteBandingDefRequest, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::DeleteBandingDefRequest, uuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::VisualizationData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::VisualizationData, x_mm_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::VisualizationData, y_mm_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::VisualizationData, z_mm_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::VisualizationData, is_weed_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataRequest, row_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataRequest, types_to_include_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataRequest, threshold_filters_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataResponse, data_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataResponse, bands_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationData2Response, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationData2Response, data_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationData2Response, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetDimensionsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetDimensionsRequest, row_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::SetBandingEnabledRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::SetBandingEnabledRequest, enabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::SetBandingEnabledResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::IsBandingEnabledResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::IsBandingEnabledResponse, enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetVisualizationMetadataResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetVisualizationMetadataResponse, crop_safety_radius_mm_per_row_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::ThresholdFilter, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::ThresholdFilter, weeding_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::ThresholdFilter, thinning_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::ThresholdFilter, banding_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::ThresholdFilters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::ThresholdFilters, crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::ThresholdFilters, weed_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, types_to_include_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, threshold_filters_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse, data_per_row_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse, types_to_include_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextBandingStateResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextBandingStateResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextBandingStateResponse, bandingdefs_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextBandingStateResponse, activedefuuid_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextBandingStateResponse, is_banding_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::banding::GetNextBandingStateResponse, is_dynamic_banding_enabled_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::banding::BandingRow)},
  { 8, -1, -1, sizeof(::carbon::frontend::banding::BandingDef)},
  { 17, -1, -1, sizeof(::carbon::frontend::banding::SaveBandingDefRequest)},
  { 25, -1, -1, sizeof(::carbon::frontend::banding::LoadBandingDefsResponse)},
  { 34, -1, -1, sizeof(::carbon::frontend::banding::SetActiveBandingDefRequest)},
  { 42, -1, -1, sizeof(::carbon::frontend::banding::GetActiveBandingDefResponse)},
  { 50, -1, -1, sizeof(::carbon::frontend::banding::DeleteBandingDefRequest)},
  { 58, -1, -1, sizeof(::carbon::frontend::banding::VisualizationData)},
  { 68, -1, -1, sizeof(::carbon::frontend::banding::GetNextVisualizationDataRequest)},
  { 78, -1, -1, sizeof(::carbon::frontend::banding::GetNextVisualizationDataResponse)},
  { 87, -1, -1, sizeof(::carbon::frontend::banding::GetNextVisualizationData2Response)},
  { 95, -1, -1, sizeof(::carbon::frontend::banding::GetDimensionsRequest)},
  { 102, -1, -1, sizeof(::carbon::frontend::banding::SetBandingEnabledRequest)},
  { 109, -1, -1, sizeof(::carbon::frontend::banding::SetBandingEnabledResponse)},
  { 115, -1, -1, sizeof(::carbon::frontend::banding::IsBandingEnabledResponse)},
  { 122, 130, -1, sizeof(::carbon::frontend::banding::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse)},
  { 132, -1, -1, sizeof(::carbon::frontend::banding::GetVisualizationMetadataResponse)},
  { 139, -1, -1, sizeof(::carbon::frontend::banding::ThresholdFilter)},
  { 148, -1, -1, sizeof(::carbon::frontend::banding::ThresholdFilters)},
  { 156, -1, -1, sizeof(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest)},
  { 165, 173, -1, sizeof(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse)},
  { 175, -1, -1, sizeof(::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse)},
  { 184, -1, -1, sizeof(::carbon::frontend::banding::GetNextBandingStateResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_BandingRow_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_BandingDef_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_SaveBandingDefRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_LoadBandingDefsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_SetActiveBandingDefRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_GetActiveBandingDefResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_DeleteBandingDefRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_VisualizationData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_GetNextVisualizationDataRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_GetNextVisualizationDataResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_GetNextVisualizationData2Response_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_GetDimensionsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_SetBandingEnabledRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_SetBandingEnabledResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_IsBandingEnabledResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_GetVisualizationMetadataResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_ThresholdFilter_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_ThresholdFilters_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_GetNextVisualizationDataForAllRowsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_GetNextVisualizationDataForAllRowsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::banding::_GetNextBandingStateResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fbanding_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\034frontend/proto/banding.proto\022\027carbon.f"
  "rontend.banding\032\031frontend/proto/util.pro"
  "to\032Hcore/controls/exterminator/controlle"
  "rs/aimbot/process/proto/aimbot.proto\032\'we"
  "ed_tracking/proto/weed_tracking.proto\"J\n"
  "\nBandingRow\022\016\n\006row_id\030\001 \001(\005\022,\n\005bands\030\002 \003"
  "(\0132\035.weed_tracking.BandDefinition\"[\n\nBan"
  "dingDef\022\014\n\004name\030\001 \001(\t\0221\n\004rows\030\002 \003(\0132#.ca"
  "rbon.frontend.banding.BandingRow\022\014\n\004uuid"
  "\030\003 \001(\t\"c\n\025SaveBandingDefRequest\0227\n\nbandi"
  "ngDef\030\001 \001(\0132#.carbon.frontend.banding.Ba"
  "ndingDef\022\021\n\tsetActive\030\002 \001(\010\"\201\001\n\027LoadBand"
  "ingDefsResponse\0228\n\013bandingDefs\030\001 \003(\0132#.c"
  "arbon.frontend.banding.BandingDef\022\025\n\tact"
  "iveDef\030\002 \001(\tB\002\030\001\022\025\n\ractiveDefUUID\030\003 \001(\t\""
  "<\n\032SetActiveBandingDefRequest\022\020\n\004name\030\001 "
  "\001(\tB\002\030\001\022\014\n\004uuid\030\002 \001(\t\"=\n\033GetActiveBandin"
  "gDefResponse\022\020\n\004name\030\001 \001(\tB\002\030\001\022\014\n\004uuid\030\002"
  " \001(\t\"9\n\027DeleteBandingDefRequest\022\020\n\004name\030"
  "\001 \001(\tB\002\030\001\022\014\n\004uuid\030\002 \001(\t\"N\n\021Visualization"
  "Data\022\014\n\004x_mm\030\001 \001(\005\022\014\n\004y_mm\030\002 \001(\005\022\014\n\004z_mm"
  "\030\003 \001(\005\022\017\n\007is_weed\030\004 \001(\010\"\363\001\n\037GetNextVisua"
  "lizationDataRequest\022+\n\002ts\030\001 \001(\0132\037.carbon"
  ".frontend.util.Timestamp\022\016\n\006row_id\030\002 \001(\005"
  "\022M\n\020types_to_include\030\003 \003(\01623.carbon.fron"
  "tend.banding.VisualizationTypeToInclude\022"
  "D\n\021threshold_filters\030\004 \001(\0132).carbon.fron"
  "tend.banding.ThresholdFilters\"\267\001\n GetNex"
  "tVisualizationDataResponse\022+\n\002ts\030\001 \001(\0132\037"
  ".carbon.frontend.util.Timestamp\0228\n\004data\030"
  "\002 \003(\0132*.carbon.frontend.banding.Visualiz"
  "ationData\022,\n\005bands\030\003 \003(\0132\035.weed_tracking"
  ".BandDefinition\"\202\001\n!GetNextVisualization"
  "Data2Response\0220\n\004data\030\001 \001(\0132\".weed_track"
  "ing.DiagnosticsSnapshot\022+\n\002ts\030\002 \001(\0132\037.ca"
  "rbon.frontend.util.Timestamp\"&\n\024GetDimen"
  "sionsRequest\022\016\n\006row_id\030\001 \001(\005\"+\n\030SetBandi"
  "ngEnabledRequest\022\017\n\007enabled\030\001 \001(\010\"\033\n\031Set"
  "BandingEnabledResponse\"+\n\030IsBandingEnabl"
  "edResponse\022\017\n\007enabled\030\001 \001(\010\"\343\001\n GetVisua"
  "lizationMetadataResponse\022~\n\035crop_safety_"
  "radius_mm_per_row\030\001 \003(\0132W.carbon.fronten"
  "d.banding.GetVisualizationMetadataRespon"
  "se.CropSafetyRadiusMmPerRowEntry\032\?\n\035Crop"
  "SafetyRadiusMmPerRowEntry\022\013\n\003key\030\001 \001(\005\022\r"
  "\n\005value\030\002 \001(\002:\0028\001\"\300\001\n\017ThresholdFilter\0228\n"
  "\007weeding\030\001 \001(\0162\'.carbon.frontend.banding"
  ".ThresholdState\0229\n\010thinning\030\002 \001(\0162\'.carb"
  "on.frontend.banding.ThresholdState\0228\n\007ba"
  "nding\030\003 \001(\0162\'.carbon.frontend.banding.Th"
  "resholdState\"\202\001\n\020ThresholdFilters\0226\n\004cro"
  "p\030\001 \001(\0132(.carbon.frontend.banding.Thresh"
  "oldFilter\0226\n\004weed\030\002 \001(\0132(.carbon.fronten"
  "d.banding.ThresholdFilter\"\355\001\n)GetNextVis"
  "ualizationDataForAllRowsRequest\022+\n\002ts\030\001 "
  "\001(\0132\037.carbon.frontend.util.Timestamp\022M\n\020"
  "types_to_include\030\002 \003(\01623.carbon.frontend"
  ".banding.VisualizationTypeToInclude\022D\n\021t"
  "hreshold_filters\030\003 \001(\0132).carbon.frontend"
  ".banding.ThresholdFilters\"\352\002\n*GetNextVis"
  "ualizationDataForAllRowsResponse\022i\n\014data"
  "_per_row\030\001 \003(\0132S.carbon.frontend.banding"
  ".GetNextVisualizationDataForAllRowsRespo"
  "nse.DataPerRowEntry\022+\n\002ts\030\002 \001(\0132\037.carbon"
  ".frontend.util.Timestamp\022M\n\020types_to_inc"
  "lude\030\003 \003(\01623.carbon.frontend.banding.Vis"
  "ualizationTypeToInclude\032U\n\017DataPerRowEnt"
  "ry\022\013\n\003key\030\001 \001(\005\0221\n\005value\030\002 \001(\0132\".weed_tr"
  "acking.DiagnosticsSnapshot:\0028\001\"\333\001\n\033GetNe"
  "xtBandingStateResponse\022+\n\002ts\030\001 \001(\0132\037.car"
  "bon.frontend.util.Timestamp\0228\n\013bandingDe"
  "fs\030\002 \003(\0132#.carbon.frontend.banding.Bandi"
  "ngDef\022\025\n\ractiveDefUUID\030\003 \001(\t\022\032\n\022is_bandi"
  "ng_enabled\030\004 \001(\010\022\"\n\032is_dynamic_banding_e"
  "nabled\030\005 \001(\010*\224\002\n\032VisualizationTypeToIncl"
  "ude\022\022\n\016DUPLICATE_WEED\020\000\022\022\n\016DUPLICATE_CRO"
  "P\020\001\022\017\n\013KILLED_WEED\020\002\022\017\n\013KILLED_CROP\020\003\022\020\n"
  "\014KILLING_WEED\020\004\022\020\n\014IGNORED_WEED\020\005\022\020\n\014KIL"
  "LING_CROP\020\006\022\016\n\nERROR_WEED\020\007\022\016\n\nERROR_CRO"
  "P\020\010\022\020\n\014IGNORED_CROP\020\t\022\010\n\004WEED\020\n\022\010\n\004CROP\020"
  "\013\022\017\n\013CROP_RADIUS\020\014\022\r\n\tCROP_KEPT\020\r\022\020\n\014THI"
  "NNING_BOX\020\016*-\n\016ThresholdState\022\007\n\003ANY\020\000\022\010"
  "\n\004PASS\020\001\022\010\n\004FAIL\020\0022\355\r\n\016BandingService\022`\n"
  "\017LoadBandingDefs\022\033.carbon.frontend.util."
  "Empty\0320.carbon.frontend.banding.LoadBand"
  "ingDefsResponse\022]\n\016SaveBandingDef\022..carb"
  "on.frontend.banding.SaveBandingDefReques"
  "t\032\033.carbon.frontend.util.Empty\022a\n\020Delete"
  "BandingDef\0220.carbon.frontend.banding.Del"
  "eteBandingDefRequest\032\033.carbon.frontend.u"
  "til.Empty\022g\n\023SetActiveBandingDef\0223.carbo"
  "n.frontend.banding.SetActiveBandingDefRe"
  "quest\032\033.carbon.frontend.util.Empty\022h\n\023Ge"
  "tActiveBandingDef\022\033.carbon.frontend.util"
  ".Empty\0324.carbon.frontend.banding.GetActi"
  "veBandingDefResponse\022\217\001\n\030GetNextVisualiz"
  "ationData\0228.carbon.frontend.banding.GetN"
  "extVisualizationDataRequest\0329.carbon.fro"
  "ntend.banding.GetNextVisualizationDataRe"
  "sponse\022\221\001\n\031GetNextVisualizationData2\0228.c"
  "arbon.frontend.banding.GetNextVisualizat"
  "ionDataRequest\032:.carbon.frontend.banding"
  ".GetNextVisualizationData2Response\022\255\001\n\"G"
  "etNextVisualizationDataForAllRows\022B.carb"
  "on.frontend.banding.GetNextVisualization"
  "DataForAllRowsRequest\032C.carbon.frontend."
  "banding.GetNextVisualizationDataForAllRo"
  "wsResponse\022]\n\rGetDimensions\022-.carbon.fro"
  "ntend.banding.GetDimensionsRequest\032\035.aim"
  "bot.GetDimensionsResponse\022z\n\021SetBandingE"
  "nabled\0221.carbon.frontend.banding.SetBand"
  "ingEnabledRequest\0322.carbon.frontend.band"
  "ing.SetBandingEnabledResponse\022b\n\020IsBandi"
  "ngEnabled\022\033.carbon.frontend.util.Empty\0321"
  ".carbon.frontend.banding.IsBandingEnable"
  "dResponse\022\201\001\n\030SetDynamicBandingEnabled\0221"
  ".carbon.frontend.banding.SetBandingEnabl"
  "edRequest\0322.carbon.frontend.banding.SetB"
  "andingEnabledResponse\022i\n\027IsDynamicBandin"
  "gEnabled\022\033.carbon.frontend.util.Empty\0321."
  "carbon.frontend.banding.IsBandingEnabled"
  "Response\022r\n\030GetVisualizationMetadata\022\033.c"
  "arbon.frontend.util.Empty\0329.carbon.front"
  "end.banding.GetVisualizationMetadataResp"
  "onse\022l\n\023GetNextBandingState\022\037.carbon.fro"
  "ntend.util.Timestamp\0324.carbon.frontend.b"
  "anding.GetNextBandingStateResponseB\020Z\016pr"
  "oto/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fbanding_2eproto_deps[3] = {
  &::descriptor_table_core_2fcontrols_2fexterminator_2fcontrollers_2faimbot_2fprocess_2fproto_2faimbot_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
  &::descriptor_table_weed_5ftracking_2fproto_2fweed_5ftracking_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fbanding_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fbanding_2eproto = {
  false, false, 5100, descriptor_table_protodef_frontend_2fproto_2fbanding_2eproto, "frontend/proto/banding.proto", 
  &descriptor_table_frontend_2fproto_2fbanding_2eproto_once, descriptor_table_frontend_2fproto_2fbanding_2eproto_deps, 3, 23,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fbanding_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fbanding_2eproto, file_level_enum_descriptors_frontend_2fproto_2fbanding_2eproto, file_level_service_descriptors_frontend_2fproto_2fbanding_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fbanding_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fbanding_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fbanding_2eproto(&descriptor_table_frontend_2fproto_2fbanding_2eproto);
namespace carbon {
namespace frontend {
namespace banding {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* VisualizationTypeToInclude_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fbanding_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fbanding_2eproto[0];
}
bool VisualizationTypeToInclude_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ThresholdState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fbanding_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fbanding_2eproto[1];
}
bool ThresholdState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class BandingRow::_Internal {
 public:
};

void BandingRow::clear_bands() {
  bands_.Clear();
}
BandingRow::BandingRow(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  bands_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.BandingRow)
}
BandingRow::BandingRow(const BandingRow& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      bands_(from.bands_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  row_id_ = from.row_id_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.BandingRow)
}

inline void BandingRow::SharedCtor() {
row_id_ = 0;
}

BandingRow::~BandingRow() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.BandingRow)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BandingRow::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BandingRow::ArenaDtor(void* object) {
  BandingRow* _this = reinterpret_cast< BandingRow* >(object);
  (void)_this;
}
void BandingRow::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BandingRow::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BandingRow::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.BandingRow)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bands_.Clear();
  row_id_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BandingRow::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 row_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          row_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .weed_tracking.BandDefinition bands = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_bands(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* BandingRow::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.BandingRow)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 row_id = 1;
  if (this->_internal_row_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_row_id(), target);
  }

  // repeated .weed_tracking.BandDefinition bands = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_bands_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_bands(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.BandingRow)
  return target;
}

size_t BandingRow::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.BandingRow)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .weed_tracking.BandDefinition bands = 2;
  total_size += 1UL * this->_internal_bands_size();
  for (const auto& msg : this->bands_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 row_id = 1;
  if (this->_internal_row_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_row_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BandingRow::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BandingRow::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BandingRow::GetClassData() const { return &_class_data_; }

void BandingRow::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<BandingRow *>(to)->MergeFrom(
      static_cast<const BandingRow &>(from));
}


void BandingRow::MergeFrom(const BandingRow& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.BandingRow)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  bands_.MergeFrom(from.bands_);
  if (from._internal_row_id() != 0) {
    _internal_set_row_id(from._internal_row_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BandingRow::CopyFrom(const BandingRow& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.BandingRow)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BandingRow::IsInitialized() const {
  return true;
}

void BandingRow::InternalSwap(BandingRow* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  bands_.InternalSwap(&other->bands_);
  swap(row_id_, other->row_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata BandingRow::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[0]);
}

// ===================================================================

class BandingDef::_Internal {
 public:
};

BandingDef::BandingDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  rows_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.BandingDef)
}
BandingDef::BandingDef(const BandingDef& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      rows_(from.rows_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.BandingDef)
}

inline void BandingDef::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

BandingDef::~BandingDef() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.BandingDef)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BandingDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void BandingDef::ArenaDtor(void* object) {
  BandingDef* _this = reinterpret_cast< BandingDef* >(object);
  (void)_this;
}
void BandingDef::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BandingDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BandingDef::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.BandingDef)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  rows_.Clear();
  name_.ClearToEmpty();
  uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BandingDef::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.banding.BandingDef.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.banding.BandingRow rows = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_rows(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string uuid = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.banding.BandingDef.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* BandingDef::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.BandingDef)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.banding.BandingDef.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // repeated .carbon.frontend.banding.BandingRow rows = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_rows_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_rows(i), target, stream);
  }

  // string uuid = 3;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.banding.BandingDef.uuid");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.BandingDef)
  return target;
}

size_t BandingDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.BandingDef)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.banding.BandingRow rows = 2;
  total_size += 1UL * this->_internal_rows_size();
  for (const auto& msg : this->rows_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string uuid = 3;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BandingDef::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BandingDef::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BandingDef::GetClassData() const { return &_class_data_; }

void BandingDef::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<BandingDef *>(to)->MergeFrom(
      static_cast<const BandingDef &>(from));
}


void BandingDef::MergeFrom(const BandingDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.BandingDef)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  rows_.MergeFrom(from.rows_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BandingDef::CopyFrom(const BandingDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.BandingDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BandingDef::IsInitialized() const {
  return true;
}

void BandingDef::InternalSwap(BandingDef* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  rows_.InternalSwap(&other->rows_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata BandingDef::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[1]);
}

// ===================================================================

class SaveBandingDefRequest::_Internal {
 public:
  static const ::carbon::frontend::banding::BandingDef& bandingdef(const SaveBandingDefRequest* msg);
};

const ::carbon::frontend::banding::BandingDef&
SaveBandingDefRequest::_Internal::bandingdef(const SaveBandingDefRequest* msg) {
  return *msg->bandingdef_;
}
SaveBandingDefRequest::SaveBandingDefRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.SaveBandingDefRequest)
}
SaveBandingDefRequest::SaveBandingDefRequest(const SaveBandingDefRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_bandingdef()) {
    bandingdef_ = new ::carbon::frontend::banding::BandingDef(*from.bandingdef_);
  } else {
    bandingdef_ = nullptr;
  }
  setactive_ = from.setactive_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.SaveBandingDefRequest)
}

inline void SaveBandingDefRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bandingdef_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&setactive_) -
    reinterpret_cast<char*>(&bandingdef_)) + sizeof(setactive_));
}

SaveBandingDefRequest::~SaveBandingDefRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.SaveBandingDefRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SaveBandingDefRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete bandingdef_;
}

void SaveBandingDefRequest::ArenaDtor(void* object) {
  SaveBandingDefRequest* _this = reinterpret_cast< SaveBandingDefRequest* >(object);
  (void)_this;
}
void SaveBandingDefRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SaveBandingDefRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SaveBandingDefRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.SaveBandingDefRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && bandingdef_ != nullptr) {
    delete bandingdef_;
  }
  bandingdef_ = nullptr;
  setactive_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SaveBandingDefRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.banding.BandingDef bandingDef = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_bandingdef(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool setActive = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          setactive_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SaveBandingDefRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.SaveBandingDefRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.banding.BandingDef bandingDef = 1;
  if (this->_internal_has_bandingdef()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::bandingdef(this), target, stream);
  }

  // bool setActive = 2;
  if (this->_internal_setactive() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_setactive(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.SaveBandingDefRequest)
  return target;
}

size_t SaveBandingDefRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.SaveBandingDefRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.banding.BandingDef bandingDef = 1;
  if (this->_internal_has_bandingdef()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bandingdef_);
  }

  // bool setActive = 2;
  if (this->_internal_setactive() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SaveBandingDefRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SaveBandingDefRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SaveBandingDefRequest::GetClassData() const { return &_class_data_; }

void SaveBandingDefRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SaveBandingDefRequest *>(to)->MergeFrom(
      static_cast<const SaveBandingDefRequest &>(from));
}


void SaveBandingDefRequest::MergeFrom(const SaveBandingDefRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.SaveBandingDefRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_bandingdef()) {
    _internal_mutable_bandingdef()->::carbon::frontend::banding::BandingDef::MergeFrom(from._internal_bandingdef());
  }
  if (from._internal_setactive() != 0) {
    _internal_set_setactive(from._internal_setactive());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SaveBandingDefRequest::CopyFrom(const SaveBandingDefRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.SaveBandingDefRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SaveBandingDefRequest::IsInitialized() const {
  return true;
}

void SaveBandingDefRequest::InternalSwap(SaveBandingDefRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SaveBandingDefRequest, setactive_)
      + sizeof(SaveBandingDefRequest::setactive_)
      - PROTOBUF_FIELD_OFFSET(SaveBandingDefRequest, bandingdef_)>(
          reinterpret_cast<char*>(&bandingdef_),
          reinterpret_cast<char*>(&other->bandingdef_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SaveBandingDefRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[2]);
}

// ===================================================================

class LoadBandingDefsResponse::_Internal {
 public:
};

LoadBandingDefsResponse::LoadBandingDefsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  bandingdefs_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.LoadBandingDefsResponse)
}
LoadBandingDefsResponse::LoadBandingDefsResponse(const LoadBandingDefsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      bandingdefs_(from.bandingdefs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  activedef_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    activedef_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_activedef().empty()) {
    activedef_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_activedef(), 
      GetArenaForAllocation());
  }
  activedefuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    activedefuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_activedefuuid().empty()) {
    activedefuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_activedefuuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.LoadBandingDefsResponse)
}

inline void LoadBandingDefsResponse::SharedCtor() {
activedef_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  activedef_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
activedefuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  activedefuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

LoadBandingDefsResponse::~LoadBandingDefsResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.LoadBandingDefsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LoadBandingDefsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  activedef_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  activedefuuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void LoadBandingDefsResponse::ArenaDtor(void* object) {
  LoadBandingDefsResponse* _this = reinterpret_cast< LoadBandingDefsResponse* >(object);
  (void)_this;
}
void LoadBandingDefsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LoadBandingDefsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LoadBandingDefsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.LoadBandingDefsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bandingdefs_.Clear();
  activedef_.ClearToEmpty();
  activedefuuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LoadBandingDefsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.banding.BandingDef bandingDefs = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_bandingdefs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string activeDef = 2 [deprecated = true];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_activedef();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.banding.LoadBandingDefsResponse.activeDef"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string activeDefUUID = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_activedefuuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.banding.LoadBandingDefsResponse.activeDefUUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LoadBandingDefsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.LoadBandingDefsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.banding.BandingDef bandingDefs = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_bandingdefs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_bandingdefs(i), target, stream);
  }

  // string activeDef = 2 [deprecated = true];
  if (!this->_internal_activedef().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_activedef().data(), static_cast<int>(this->_internal_activedef().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.banding.LoadBandingDefsResponse.activeDef");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_activedef(), target);
  }

  // string activeDefUUID = 3;
  if (!this->_internal_activedefuuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_activedefuuid().data(), static_cast<int>(this->_internal_activedefuuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.banding.LoadBandingDefsResponse.activeDefUUID");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_activedefuuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.LoadBandingDefsResponse)
  return target;
}

size_t LoadBandingDefsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.LoadBandingDefsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.banding.BandingDef bandingDefs = 1;
  total_size += 1UL * this->_internal_bandingdefs_size();
  for (const auto& msg : this->bandingdefs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string activeDef = 2 [deprecated = true];
  if (!this->_internal_activedef().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_activedef());
  }

  // string activeDefUUID = 3;
  if (!this->_internal_activedefuuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_activedefuuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LoadBandingDefsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LoadBandingDefsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LoadBandingDefsResponse::GetClassData() const { return &_class_data_; }

void LoadBandingDefsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LoadBandingDefsResponse *>(to)->MergeFrom(
      static_cast<const LoadBandingDefsResponse &>(from));
}


void LoadBandingDefsResponse::MergeFrom(const LoadBandingDefsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.LoadBandingDefsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  bandingdefs_.MergeFrom(from.bandingdefs_);
  if (!from._internal_activedef().empty()) {
    _internal_set_activedef(from._internal_activedef());
  }
  if (!from._internal_activedefuuid().empty()) {
    _internal_set_activedefuuid(from._internal_activedefuuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LoadBandingDefsResponse::CopyFrom(const LoadBandingDefsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.LoadBandingDefsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadBandingDefsResponse::IsInitialized() const {
  return true;
}

void LoadBandingDefsResponse::InternalSwap(LoadBandingDefsResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  bandingdefs_.InternalSwap(&other->bandingdefs_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &activedef_, lhs_arena,
      &other->activedef_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &activedefuuid_, lhs_arena,
      &other->activedefuuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata LoadBandingDefsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[3]);
}

// ===================================================================

class SetActiveBandingDefRequest::_Internal {
 public:
};

SetActiveBandingDefRequest::SetActiveBandingDefRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.SetActiveBandingDefRequest)
}
SetActiveBandingDefRequest::SetActiveBandingDefRequest(const SetActiveBandingDefRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.SetActiveBandingDefRequest)
}

inline void SetActiveBandingDefRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SetActiveBandingDefRequest::~SetActiveBandingDefRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.SetActiveBandingDefRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetActiveBandingDefRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetActiveBandingDefRequest::ArenaDtor(void* object) {
  SetActiveBandingDefRequest* _this = reinterpret_cast< SetActiveBandingDefRequest* >(object);
  (void)_this;
}
void SetActiveBandingDefRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetActiveBandingDefRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetActiveBandingDefRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.SetActiveBandingDefRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetActiveBandingDefRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1 [deprecated = true];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.banding.SetActiveBandingDefRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string uuid = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.banding.SetActiveBandingDefRequest.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetActiveBandingDefRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.SetActiveBandingDefRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1 [deprecated = true];
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.banding.SetActiveBandingDefRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // string uuid = 2;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.banding.SetActiveBandingDefRequest.uuid");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.SetActiveBandingDefRequest)
  return target;
}

size_t SetActiveBandingDefRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.SetActiveBandingDefRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1 [deprecated = true];
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string uuid = 2;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetActiveBandingDefRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetActiveBandingDefRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetActiveBandingDefRequest::GetClassData() const { return &_class_data_; }

void SetActiveBandingDefRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetActiveBandingDefRequest *>(to)->MergeFrom(
      static_cast<const SetActiveBandingDefRequest &>(from));
}


void SetActiveBandingDefRequest::MergeFrom(const SetActiveBandingDefRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.SetActiveBandingDefRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetActiveBandingDefRequest::CopyFrom(const SetActiveBandingDefRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.SetActiveBandingDefRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetActiveBandingDefRequest::IsInitialized() const {
  return true;
}

void SetActiveBandingDefRequest::InternalSwap(SetActiveBandingDefRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SetActiveBandingDefRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[4]);
}

// ===================================================================

class GetActiveBandingDefResponse::_Internal {
 public:
};

GetActiveBandingDefResponse::GetActiveBandingDefResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.GetActiveBandingDefResponse)
}
GetActiveBandingDefResponse::GetActiveBandingDefResponse(const GetActiveBandingDefResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.GetActiveBandingDefResponse)
}

inline void GetActiveBandingDefResponse::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetActiveBandingDefResponse::~GetActiveBandingDefResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.GetActiveBandingDefResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetActiveBandingDefResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetActiveBandingDefResponse::ArenaDtor(void* object) {
  GetActiveBandingDefResponse* _this = reinterpret_cast< GetActiveBandingDefResponse* >(object);
  (void)_this;
}
void GetActiveBandingDefResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetActiveBandingDefResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetActiveBandingDefResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.GetActiveBandingDefResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetActiveBandingDefResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1 [deprecated = true];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.banding.GetActiveBandingDefResponse.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string uuid = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.banding.GetActiveBandingDefResponse.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetActiveBandingDefResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.GetActiveBandingDefResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1 [deprecated = true];
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.banding.GetActiveBandingDefResponse.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // string uuid = 2;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.banding.GetActiveBandingDefResponse.uuid");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.GetActiveBandingDefResponse)
  return target;
}

size_t GetActiveBandingDefResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.GetActiveBandingDefResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1 [deprecated = true];
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string uuid = 2;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetActiveBandingDefResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetActiveBandingDefResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetActiveBandingDefResponse::GetClassData() const { return &_class_data_; }

void GetActiveBandingDefResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetActiveBandingDefResponse *>(to)->MergeFrom(
      static_cast<const GetActiveBandingDefResponse &>(from));
}


void GetActiveBandingDefResponse::MergeFrom(const GetActiveBandingDefResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.GetActiveBandingDefResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetActiveBandingDefResponse::CopyFrom(const GetActiveBandingDefResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.GetActiveBandingDefResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetActiveBandingDefResponse::IsInitialized() const {
  return true;
}

void GetActiveBandingDefResponse::InternalSwap(GetActiveBandingDefResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetActiveBandingDefResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[5]);
}

// ===================================================================

class DeleteBandingDefRequest::_Internal {
 public:
};

DeleteBandingDefRequest::DeleteBandingDefRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.DeleteBandingDefRequest)
}
DeleteBandingDefRequest::DeleteBandingDefRequest(const DeleteBandingDefRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.DeleteBandingDefRequest)
}

inline void DeleteBandingDefRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DeleteBandingDefRequest::~DeleteBandingDefRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.DeleteBandingDefRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeleteBandingDefRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DeleteBandingDefRequest::ArenaDtor(void* object) {
  DeleteBandingDefRequest* _this = reinterpret_cast< DeleteBandingDefRequest* >(object);
  (void)_this;
}
void DeleteBandingDefRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeleteBandingDefRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeleteBandingDefRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.DeleteBandingDefRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeleteBandingDefRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1 [deprecated = true];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.banding.DeleteBandingDefRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string uuid = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.banding.DeleteBandingDefRequest.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeleteBandingDefRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.DeleteBandingDefRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1 [deprecated = true];
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.banding.DeleteBandingDefRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // string uuid = 2;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.banding.DeleteBandingDefRequest.uuid");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.DeleteBandingDefRequest)
  return target;
}

size_t DeleteBandingDefRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.DeleteBandingDefRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1 [deprecated = true];
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string uuid = 2;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeleteBandingDefRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeleteBandingDefRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeleteBandingDefRequest::GetClassData() const { return &_class_data_; }

void DeleteBandingDefRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeleteBandingDefRequest *>(to)->MergeFrom(
      static_cast<const DeleteBandingDefRequest &>(from));
}


void DeleteBandingDefRequest::MergeFrom(const DeleteBandingDefRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.DeleteBandingDefRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeleteBandingDefRequest::CopyFrom(const DeleteBandingDefRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.DeleteBandingDefRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeleteBandingDefRequest::IsInitialized() const {
  return true;
}

void DeleteBandingDefRequest::InternalSwap(DeleteBandingDefRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DeleteBandingDefRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[6]);
}

// ===================================================================

class VisualizationData::_Internal {
 public:
};

VisualizationData::VisualizationData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.VisualizationData)
}
VisualizationData::VisualizationData(const VisualizationData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_mm_, &from.x_mm_,
    static_cast<size_t>(reinterpret_cast<char*>(&is_weed_) -
    reinterpret_cast<char*>(&x_mm_)) + sizeof(is_weed_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.VisualizationData)
}

inline void VisualizationData::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&x_mm_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&is_weed_) -
    reinterpret_cast<char*>(&x_mm_)) + sizeof(is_weed_));
}

VisualizationData::~VisualizationData() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.VisualizationData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VisualizationData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void VisualizationData::ArenaDtor(void* object) {
  VisualizationData* _this = reinterpret_cast< VisualizationData* >(object);
  (void)_this;
}
void VisualizationData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VisualizationData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VisualizationData::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.VisualizationData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_mm_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&is_weed_) -
      reinterpret_cast<char*>(&x_mm_)) + sizeof(is_weed_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VisualizationData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 x_mm = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          x_mm_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 y_mm = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          y_mm_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 z_mm = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          z_mm_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_weed = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          is_weed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* VisualizationData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.VisualizationData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 x_mm = 1;
  if (this->_internal_x_mm() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_x_mm(), target);
  }

  // int32 y_mm = 2;
  if (this->_internal_y_mm() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_y_mm(), target);
  }

  // int32 z_mm = 3;
  if (this->_internal_z_mm() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_z_mm(), target);
  }

  // bool is_weed = 4;
  if (this->_internal_is_weed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_is_weed(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.VisualizationData)
  return target;
}

size_t VisualizationData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.VisualizationData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 x_mm = 1;
  if (this->_internal_x_mm() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_x_mm());
  }

  // int32 y_mm = 2;
  if (this->_internal_y_mm() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_y_mm());
  }

  // int32 z_mm = 3;
  if (this->_internal_z_mm() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_z_mm());
  }

  // bool is_weed = 4;
  if (this->_internal_is_weed() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VisualizationData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VisualizationData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VisualizationData::GetClassData() const { return &_class_data_; }

void VisualizationData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<VisualizationData *>(to)->MergeFrom(
      static_cast<const VisualizationData &>(from));
}


void VisualizationData::MergeFrom(const VisualizationData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.VisualizationData)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_x_mm() != 0) {
    _internal_set_x_mm(from._internal_x_mm());
  }
  if (from._internal_y_mm() != 0) {
    _internal_set_y_mm(from._internal_y_mm());
  }
  if (from._internal_z_mm() != 0) {
    _internal_set_z_mm(from._internal_z_mm());
  }
  if (from._internal_is_weed() != 0) {
    _internal_set_is_weed(from._internal_is_weed());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VisualizationData::CopyFrom(const VisualizationData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.VisualizationData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VisualizationData::IsInitialized() const {
  return true;
}

void VisualizationData::InternalSwap(VisualizationData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VisualizationData, is_weed_)
      + sizeof(VisualizationData::is_weed_)
      - PROTOBUF_FIELD_OFFSET(VisualizationData, x_mm_)>(
          reinterpret_cast<char*>(&x_mm_),
          reinterpret_cast<char*>(&other->x_mm_));
}

::PROTOBUF_NAMESPACE_ID::Metadata VisualizationData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[7]);
}

// ===================================================================

class GetNextVisualizationDataRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextVisualizationDataRequest* msg);
  static const ::carbon::frontend::banding::ThresholdFilters& threshold_filters(const GetNextVisualizationDataRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextVisualizationDataRequest::_Internal::ts(const GetNextVisualizationDataRequest* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::banding::ThresholdFilters&
GetNextVisualizationDataRequest::_Internal::threshold_filters(const GetNextVisualizationDataRequest* msg) {
  return *msg->threshold_filters_;
}
void GetNextVisualizationDataRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextVisualizationDataRequest::GetNextVisualizationDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  types_to_include_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.GetNextVisualizationDataRequest)
}
GetNextVisualizationDataRequest::GetNextVisualizationDataRequest(const GetNextVisualizationDataRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      types_to_include_(from.types_to_include_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_threshold_filters()) {
    threshold_filters_ = new ::carbon::frontend::banding::ThresholdFilters(*from.threshold_filters_);
  } else {
    threshold_filters_ = nullptr;
  }
  row_id_ = from.row_id_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.GetNextVisualizationDataRequest)
}

inline void GetNextVisualizationDataRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&row_id_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(row_id_));
}

GetNextVisualizationDataRequest::~GetNextVisualizationDataRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.GetNextVisualizationDataRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextVisualizationDataRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete threshold_filters_;
}

void GetNextVisualizationDataRequest::ArenaDtor(void* object) {
  GetNextVisualizationDataRequest* _this = reinterpret_cast< GetNextVisualizationDataRequest* >(object);
  (void)_this;
}
void GetNextVisualizationDataRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextVisualizationDataRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextVisualizationDataRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.GetNextVisualizationDataRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  types_to_include_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && threshold_filters_ != nullptr) {
    delete threshold_filters_;
  }
  threshold_filters_ = nullptr;
  row_id_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextVisualizationDataRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 row_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          row_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedEnumParser(_internal_mutable_types_to_include(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 24) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_add_types_to_include(static_cast<::carbon::frontend::banding::VisualizationTypeToInclude>(val));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.banding.ThresholdFilters threshold_filters = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_threshold_filters(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextVisualizationDataRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.GetNextVisualizationDataRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // int32 row_id = 2;
  if (this->_internal_row_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_row_id(), target);
  }

  // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 3;
  {
    int byte_size = _types_to_include_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteEnumPacked(
          3, types_to_include_, byte_size, target);
    }
  }

  // .carbon.frontend.banding.ThresholdFilters threshold_filters = 4;
  if (this->_internal_has_threshold_filters()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::threshold_filters(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.GetNextVisualizationDataRequest)
  return target;
}

size_t GetNextVisualizationDataRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.GetNextVisualizationDataRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 3;
  {
    size_t data_size = 0;
    unsigned int count = static_cast<unsigned int>(this->_internal_types_to_include_size());for (unsigned int i = 0; i < count; i++) {
      data_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(
        this->_internal_types_to_include(static_cast<int>(i)));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _types_to_include_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.banding.ThresholdFilters threshold_filters = 4;
  if (this->_internal_has_threshold_filters()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *threshold_filters_);
  }

  // int32 row_id = 2;
  if (this->_internal_row_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_row_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextVisualizationDataRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextVisualizationDataRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextVisualizationDataRequest::GetClassData() const { return &_class_data_; }

void GetNextVisualizationDataRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextVisualizationDataRequest *>(to)->MergeFrom(
      static_cast<const GetNextVisualizationDataRequest &>(from));
}


void GetNextVisualizationDataRequest::MergeFrom(const GetNextVisualizationDataRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.GetNextVisualizationDataRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  types_to_include_.MergeFrom(from.types_to_include_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_threshold_filters()) {
    _internal_mutable_threshold_filters()->::carbon::frontend::banding::ThresholdFilters::MergeFrom(from._internal_threshold_filters());
  }
  if (from._internal_row_id() != 0) {
    _internal_set_row_id(from._internal_row_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextVisualizationDataRequest::CopyFrom(const GetNextVisualizationDataRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.GetNextVisualizationDataRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextVisualizationDataRequest::IsInitialized() const {
  return true;
}

void GetNextVisualizationDataRequest::InternalSwap(GetNextVisualizationDataRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  types_to_include_.InternalSwap(&other->types_to_include_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextVisualizationDataRequest, row_id_)
      + sizeof(GetNextVisualizationDataRequest::row_id_)
      - PROTOBUF_FIELD_OFFSET(GetNextVisualizationDataRequest, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextVisualizationDataRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[8]);
}

// ===================================================================

class GetNextVisualizationDataResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextVisualizationDataResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextVisualizationDataResponse::_Internal::ts(const GetNextVisualizationDataResponse* msg) {
  return *msg->ts_;
}
void GetNextVisualizationDataResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
void GetNextVisualizationDataResponse::clear_bands() {
  bands_.Clear();
}
GetNextVisualizationDataResponse::GetNextVisualizationDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  data_(arena),
  bands_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.GetNextVisualizationDataResponse)
}
GetNextVisualizationDataResponse::GetNextVisualizationDataResponse(const GetNextVisualizationDataResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      data_(from.data_),
      bands_(from.bands_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.GetNextVisualizationDataResponse)
}

inline void GetNextVisualizationDataResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextVisualizationDataResponse::~GetNextVisualizationDataResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.GetNextVisualizationDataResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextVisualizationDataResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextVisualizationDataResponse::ArenaDtor(void* object) {
  GetNextVisualizationDataResponse* _this = reinterpret_cast< GetNextVisualizationDataResponse* >(object);
  (void)_this;
}
void GetNextVisualizationDataResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextVisualizationDataResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextVisualizationDataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.GetNextVisualizationDataResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  data_.Clear();
  bands_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextVisualizationDataResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.banding.VisualizationData data = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_data(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .weed_tracking.BandDefinition bands = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_bands(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextVisualizationDataResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.GetNextVisualizationDataResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.frontend.banding.VisualizationData data = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_data_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_data(i), target, stream);
  }

  // repeated .weed_tracking.BandDefinition bands = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_bands_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_bands(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.GetNextVisualizationDataResponse)
  return target;
}

size_t GetNextVisualizationDataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.GetNextVisualizationDataResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.banding.VisualizationData data = 2;
  total_size += 1UL * this->_internal_data_size();
  for (const auto& msg : this->data_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .weed_tracking.BandDefinition bands = 3;
  total_size += 1UL * this->_internal_bands_size();
  for (const auto& msg : this->bands_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextVisualizationDataResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextVisualizationDataResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextVisualizationDataResponse::GetClassData() const { return &_class_data_; }

void GetNextVisualizationDataResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextVisualizationDataResponse *>(to)->MergeFrom(
      static_cast<const GetNextVisualizationDataResponse &>(from));
}


void GetNextVisualizationDataResponse::MergeFrom(const GetNextVisualizationDataResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.GetNextVisualizationDataResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  data_.MergeFrom(from.data_);
  bands_.MergeFrom(from.bands_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextVisualizationDataResponse::CopyFrom(const GetNextVisualizationDataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.GetNextVisualizationDataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextVisualizationDataResponse::IsInitialized() const {
  return true;
}

void GetNextVisualizationDataResponse::InternalSwap(GetNextVisualizationDataResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  data_.InternalSwap(&other->data_);
  bands_.InternalSwap(&other->bands_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextVisualizationDataResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[9]);
}

// ===================================================================

class GetNextVisualizationData2Response::_Internal {
 public:
  static const ::weed_tracking::DiagnosticsSnapshot& data(const GetNextVisualizationData2Response* msg);
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextVisualizationData2Response* msg);
};

const ::weed_tracking::DiagnosticsSnapshot&
GetNextVisualizationData2Response::_Internal::data(const GetNextVisualizationData2Response* msg) {
  return *msg->data_;
}
const ::carbon::frontend::util::Timestamp&
GetNextVisualizationData2Response::_Internal::ts(const GetNextVisualizationData2Response* msg) {
  return *msg->ts_;
}
void GetNextVisualizationData2Response::clear_data() {
  if (GetArenaForAllocation() == nullptr && data_ != nullptr) {
    delete data_;
  }
  data_ = nullptr;
}
void GetNextVisualizationData2Response::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextVisualizationData2Response::GetNextVisualizationData2Response(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.GetNextVisualizationData2Response)
}
GetNextVisualizationData2Response::GetNextVisualizationData2Response(const GetNextVisualizationData2Response& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_data()) {
    data_ = new ::weed_tracking::DiagnosticsSnapshot(*from.data_);
  } else {
    data_ = nullptr;
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.GetNextVisualizationData2Response)
}

inline void GetNextVisualizationData2Response::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&data_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&ts_) -
    reinterpret_cast<char*>(&data_)) + sizeof(ts_));
}

GetNextVisualizationData2Response::~GetNextVisualizationData2Response() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.GetNextVisualizationData2Response)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextVisualizationData2Response::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete data_;
  if (this != internal_default_instance()) delete ts_;
}

void GetNextVisualizationData2Response::ArenaDtor(void* object) {
  GetNextVisualizationData2Response* _this = reinterpret_cast< GetNextVisualizationData2Response* >(object);
  (void)_this;
}
void GetNextVisualizationData2Response::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextVisualizationData2Response::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextVisualizationData2Response::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.GetNextVisualizationData2Response)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && data_ != nullptr) {
    delete data_;
  }
  data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextVisualizationData2Response::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .weed_tracking.DiagnosticsSnapshot data = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextVisualizationData2Response::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.GetNextVisualizationData2Response)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .weed_tracking.DiagnosticsSnapshot data = 1;
  if (this->_internal_has_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::data(this), target, stream);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.GetNextVisualizationData2Response)
  return target;
}

size_t GetNextVisualizationData2Response::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.GetNextVisualizationData2Response)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .weed_tracking.DiagnosticsSnapshot data = 1;
  if (this->_internal_has_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *data_);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextVisualizationData2Response::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextVisualizationData2Response::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextVisualizationData2Response::GetClassData() const { return &_class_data_; }

void GetNextVisualizationData2Response::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextVisualizationData2Response *>(to)->MergeFrom(
      static_cast<const GetNextVisualizationData2Response &>(from));
}


void GetNextVisualizationData2Response::MergeFrom(const GetNextVisualizationData2Response& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.GetNextVisualizationData2Response)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_data()) {
    _internal_mutable_data()->::weed_tracking::DiagnosticsSnapshot::MergeFrom(from._internal_data());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextVisualizationData2Response::CopyFrom(const GetNextVisualizationData2Response& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.GetNextVisualizationData2Response)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextVisualizationData2Response::IsInitialized() const {
  return true;
}

void GetNextVisualizationData2Response::InternalSwap(GetNextVisualizationData2Response* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextVisualizationData2Response, ts_)
      + sizeof(GetNextVisualizationData2Response::ts_)
      - PROTOBUF_FIELD_OFFSET(GetNextVisualizationData2Response, data_)>(
          reinterpret_cast<char*>(&data_),
          reinterpret_cast<char*>(&other->data_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextVisualizationData2Response::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[10]);
}

// ===================================================================

class GetDimensionsRequest::_Internal {
 public:
};

GetDimensionsRequest::GetDimensionsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.GetDimensionsRequest)
}
GetDimensionsRequest::GetDimensionsRequest(const GetDimensionsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  row_id_ = from.row_id_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.GetDimensionsRequest)
}

inline void GetDimensionsRequest::SharedCtor() {
row_id_ = 0;
}

GetDimensionsRequest::~GetDimensionsRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.GetDimensionsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetDimensionsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetDimensionsRequest::ArenaDtor(void* object) {
  GetDimensionsRequest* _this = reinterpret_cast< GetDimensionsRequest* >(object);
  (void)_this;
}
void GetDimensionsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetDimensionsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetDimensionsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.GetDimensionsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  row_id_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetDimensionsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 row_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          row_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetDimensionsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.GetDimensionsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 row_id = 1;
  if (this->_internal_row_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_row_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.GetDimensionsRequest)
  return target;
}

size_t GetDimensionsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.GetDimensionsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 row_id = 1;
  if (this->_internal_row_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_row_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetDimensionsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetDimensionsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetDimensionsRequest::GetClassData() const { return &_class_data_; }

void GetDimensionsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetDimensionsRequest *>(to)->MergeFrom(
      static_cast<const GetDimensionsRequest &>(from));
}


void GetDimensionsRequest::MergeFrom(const GetDimensionsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.GetDimensionsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_row_id() != 0) {
    _internal_set_row_id(from._internal_row_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetDimensionsRequest::CopyFrom(const GetDimensionsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.GetDimensionsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetDimensionsRequest::IsInitialized() const {
  return true;
}

void GetDimensionsRequest::InternalSwap(GetDimensionsRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(row_id_, other->row_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetDimensionsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[11]);
}

// ===================================================================

class SetBandingEnabledRequest::_Internal {
 public:
};

SetBandingEnabledRequest::SetBandingEnabledRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.SetBandingEnabledRequest)
}
SetBandingEnabledRequest::SetBandingEnabledRequest(const SetBandingEnabledRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  enabled_ = from.enabled_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.SetBandingEnabledRequest)
}

inline void SetBandingEnabledRequest::SharedCtor() {
enabled_ = false;
}

SetBandingEnabledRequest::~SetBandingEnabledRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.SetBandingEnabledRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetBandingEnabledRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetBandingEnabledRequest::ArenaDtor(void* object) {
  SetBandingEnabledRequest* _this = reinterpret_cast< SetBandingEnabledRequest* >(object);
  (void)_this;
}
void SetBandingEnabledRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetBandingEnabledRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetBandingEnabledRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.SetBandingEnabledRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enabled_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetBandingEnabledRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool enabled = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetBandingEnabledRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.SetBandingEnabledRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.SetBandingEnabledRequest)
  return target;
}

size_t SetBandingEnabledRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.SetBandingEnabledRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetBandingEnabledRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetBandingEnabledRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetBandingEnabledRequest::GetClassData() const { return &_class_data_; }

void SetBandingEnabledRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetBandingEnabledRequest *>(to)->MergeFrom(
      static_cast<const SetBandingEnabledRequest &>(from));
}


void SetBandingEnabledRequest::MergeFrom(const SetBandingEnabledRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.SetBandingEnabledRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_enabled() != 0) {
    _internal_set_enabled(from._internal_enabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetBandingEnabledRequest::CopyFrom(const SetBandingEnabledRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.SetBandingEnabledRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetBandingEnabledRequest::IsInitialized() const {
  return true;
}

void SetBandingEnabledRequest::InternalSwap(SetBandingEnabledRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(enabled_, other->enabled_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetBandingEnabledRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[12]);
}

// ===================================================================

class SetBandingEnabledResponse::_Internal {
 public:
};

SetBandingEnabledResponse::SetBandingEnabledResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.SetBandingEnabledResponse)
}
SetBandingEnabledResponse::SetBandingEnabledResponse(const SetBandingEnabledResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.SetBandingEnabledResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetBandingEnabledResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetBandingEnabledResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SetBandingEnabledResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[13]);
}

// ===================================================================

class IsBandingEnabledResponse::_Internal {
 public:
};

IsBandingEnabledResponse::IsBandingEnabledResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.IsBandingEnabledResponse)
}
IsBandingEnabledResponse::IsBandingEnabledResponse(const IsBandingEnabledResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  enabled_ = from.enabled_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.IsBandingEnabledResponse)
}

inline void IsBandingEnabledResponse::SharedCtor() {
enabled_ = false;
}

IsBandingEnabledResponse::~IsBandingEnabledResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.IsBandingEnabledResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void IsBandingEnabledResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void IsBandingEnabledResponse::ArenaDtor(void* object) {
  IsBandingEnabledResponse* _this = reinterpret_cast< IsBandingEnabledResponse* >(object);
  (void)_this;
}
void IsBandingEnabledResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void IsBandingEnabledResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void IsBandingEnabledResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.IsBandingEnabledResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enabled_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* IsBandingEnabledResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool enabled = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* IsBandingEnabledResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.IsBandingEnabledResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.IsBandingEnabledResponse)
  return target;
}

size_t IsBandingEnabledResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.IsBandingEnabledResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData IsBandingEnabledResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    IsBandingEnabledResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*IsBandingEnabledResponse::GetClassData() const { return &_class_data_; }

void IsBandingEnabledResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<IsBandingEnabledResponse *>(to)->MergeFrom(
      static_cast<const IsBandingEnabledResponse &>(from));
}


void IsBandingEnabledResponse::MergeFrom(const IsBandingEnabledResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.IsBandingEnabledResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_enabled() != 0) {
    _internal_set_enabled(from._internal_enabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void IsBandingEnabledResponse::CopyFrom(const IsBandingEnabledResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.IsBandingEnabledResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IsBandingEnabledResponse::IsInitialized() const {
  return true;
}

void IsBandingEnabledResponse::InternalSwap(IsBandingEnabledResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(enabled_, other->enabled_);
}

::PROTOBUF_NAMESPACE_ID::Metadata IsBandingEnabledResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[14]);
}

// ===================================================================

GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse() {}
GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse::MergeFrom(const GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[15]);
}

// ===================================================================

class GetVisualizationMetadataResponse::_Internal {
 public:
};

GetVisualizationMetadataResponse::GetVisualizationMetadataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  crop_safety_radius_mm_per_row_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.GetVisualizationMetadataResponse)
}
GetVisualizationMetadataResponse::GetVisualizationMetadataResponse(const GetVisualizationMetadataResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  crop_safety_radius_mm_per_row_.MergeFrom(from.crop_safety_radius_mm_per_row_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.GetVisualizationMetadataResponse)
}

inline void GetVisualizationMetadataResponse::SharedCtor() {
}

GetVisualizationMetadataResponse::~GetVisualizationMetadataResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.GetVisualizationMetadataResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetVisualizationMetadataResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetVisualizationMetadataResponse::ArenaDtor(void* object) {
  GetVisualizationMetadataResponse* _this = reinterpret_cast< GetVisualizationMetadataResponse* >(object);
  (void)_this;
  _this->crop_safety_radius_mm_per_row_. ~MapField();
}
inline void GetVisualizationMetadataResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &GetVisualizationMetadataResponse::ArenaDtor);
  }
}
void GetVisualizationMetadataResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetVisualizationMetadataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.GetVisualizationMetadataResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  crop_safety_radius_mm_per_row_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetVisualizationMetadataResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<int32, float> crop_safety_radius_mm_per_row = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&crop_safety_radius_mm_per_row_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetVisualizationMetadataResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.GetVisualizationMetadataResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<int32, float> crop_safety_radius_mm_per_row = 1;
  if (!this->_internal_crop_safety_radius_mm_per_row().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_crop_safety_radius_mm_per_row().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_crop_safety_radius_mm_per_row().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >::const_iterator
          it = this->_internal_crop_safety_radius_mm_per_row().begin();
          it != this->_internal_crop_safety_radius_mm_per_row().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >::const_iterator
          it = this->_internal_crop_safety_radius_mm_per_row().begin();
          it != this->_internal_crop_safety_radius_mm_per_row().end(); ++it) {
        target = GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.GetVisualizationMetadataResponse)
  return target;
}

size_t GetVisualizationMetadataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.GetVisualizationMetadataResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<int32, float> crop_safety_radius_mm_per_row = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_crop_safety_radius_mm_per_row_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >::const_iterator
      it = this->_internal_crop_safety_radius_mm_per_row().begin();
      it != this->_internal_crop_safety_radius_mm_per_row().end(); ++it) {
    total_size += GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetVisualizationMetadataResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetVisualizationMetadataResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetVisualizationMetadataResponse::GetClassData() const { return &_class_data_; }

void GetVisualizationMetadataResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetVisualizationMetadataResponse *>(to)->MergeFrom(
      static_cast<const GetVisualizationMetadataResponse &>(from));
}


void GetVisualizationMetadataResponse::MergeFrom(const GetVisualizationMetadataResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.GetVisualizationMetadataResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  crop_safety_radius_mm_per_row_.MergeFrom(from.crop_safety_radius_mm_per_row_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetVisualizationMetadataResponse::CopyFrom(const GetVisualizationMetadataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.GetVisualizationMetadataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetVisualizationMetadataResponse::IsInitialized() const {
  return true;
}

void GetVisualizationMetadataResponse::InternalSwap(GetVisualizationMetadataResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  crop_safety_radius_mm_per_row_.InternalSwap(&other->crop_safety_radius_mm_per_row_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetVisualizationMetadataResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[16]);
}

// ===================================================================

class ThresholdFilter::_Internal {
 public:
};

ThresholdFilter::ThresholdFilter(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.ThresholdFilter)
}
ThresholdFilter::ThresholdFilter(const ThresholdFilter& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&weeding_, &from.weeding_,
    static_cast<size_t>(reinterpret_cast<char*>(&banding_) -
    reinterpret_cast<char*>(&weeding_)) + sizeof(banding_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.ThresholdFilter)
}

inline void ThresholdFilter::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&weeding_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&banding_) -
    reinterpret_cast<char*>(&weeding_)) + sizeof(banding_));
}

ThresholdFilter::~ThresholdFilter() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.ThresholdFilter)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ThresholdFilter::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ThresholdFilter::ArenaDtor(void* object) {
  ThresholdFilter* _this = reinterpret_cast< ThresholdFilter* >(object);
  (void)_this;
}
void ThresholdFilter::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ThresholdFilter::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ThresholdFilter::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.ThresholdFilter)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&weeding_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&banding_) -
      reinterpret_cast<char*>(&weeding_)) + sizeof(banding_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ThresholdFilter::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.banding.ThresholdState weeding = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_weeding(static_cast<::carbon::frontend::banding::ThresholdState>(val));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.banding.ThresholdState thinning = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_thinning(static_cast<::carbon::frontend::banding::ThresholdState>(val));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.banding.ThresholdState banding = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_banding(static_cast<::carbon::frontend::banding::ThresholdState>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ThresholdFilter::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.ThresholdFilter)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.banding.ThresholdState weeding = 1;
  if (this->_internal_weeding() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_weeding(), target);
  }

  // .carbon.frontend.banding.ThresholdState thinning = 2;
  if (this->_internal_thinning() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_thinning(), target);
  }

  // .carbon.frontend.banding.ThresholdState banding = 3;
  if (this->_internal_banding() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_banding(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.ThresholdFilter)
  return target;
}

size_t ThresholdFilter::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.ThresholdFilter)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.banding.ThresholdState weeding = 1;
  if (this->_internal_weeding() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_weeding());
  }

  // .carbon.frontend.banding.ThresholdState thinning = 2;
  if (this->_internal_thinning() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_thinning());
  }

  // .carbon.frontend.banding.ThresholdState banding = 3;
  if (this->_internal_banding() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_banding());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ThresholdFilter::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ThresholdFilter::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ThresholdFilter::GetClassData() const { return &_class_data_; }

void ThresholdFilter::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ThresholdFilter *>(to)->MergeFrom(
      static_cast<const ThresholdFilter &>(from));
}


void ThresholdFilter::MergeFrom(const ThresholdFilter& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.ThresholdFilter)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_weeding() != 0) {
    _internal_set_weeding(from._internal_weeding());
  }
  if (from._internal_thinning() != 0) {
    _internal_set_thinning(from._internal_thinning());
  }
  if (from._internal_banding() != 0) {
    _internal_set_banding(from._internal_banding());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ThresholdFilter::CopyFrom(const ThresholdFilter& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.ThresholdFilter)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ThresholdFilter::IsInitialized() const {
  return true;
}

void ThresholdFilter::InternalSwap(ThresholdFilter* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ThresholdFilter, banding_)
      + sizeof(ThresholdFilter::banding_)
      - PROTOBUF_FIELD_OFFSET(ThresholdFilter, weeding_)>(
          reinterpret_cast<char*>(&weeding_),
          reinterpret_cast<char*>(&other->weeding_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ThresholdFilter::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[17]);
}

// ===================================================================

class ThresholdFilters::_Internal {
 public:
  static const ::carbon::frontend::banding::ThresholdFilter& crop(const ThresholdFilters* msg);
  static const ::carbon::frontend::banding::ThresholdFilter& weed(const ThresholdFilters* msg);
};

const ::carbon::frontend::banding::ThresholdFilter&
ThresholdFilters::_Internal::crop(const ThresholdFilters* msg) {
  return *msg->crop_;
}
const ::carbon::frontend::banding::ThresholdFilter&
ThresholdFilters::_Internal::weed(const ThresholdFilters* msg) {
  return *msg->weed_;
}
ThresholdFilters::ThresholdFilters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.ThresholdFilters)
}
ThresholdFilters::ThresholdFilters(const ThresholdFilters& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_crop()) {
    crop_ = new ::carbon::frontend::banding::ThresholdFilter(*from.crop_);
  } else {
    crop_ = nullptr;
  }
  if (from._internal_has_weed()) {
    weed_ = new ::carbon::frontend::banding::ThresholdFilter(*from.weed_);
  } else {
    weed_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.ThresholdFilters)
}

inline void ThresholdFilters::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&crop_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&weed_) -
    reinterpret_cast<char*>(&crop_)) + sizeof(weed_));
}

ThresholdFilters::~ThresholdFilters() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.ThresholdFilters)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ThresholdFilters::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete crop_;
  if (this != internal_default_instance()) delete weed_;
}

void ThresholdFilters::ArenaDtor(void* object) {
  ThresholdFilters* _this = reinterpret_cast< ThresholdFilters* >(object);
  (void)_this;
}
void ThresholdFilters::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ThresholdFilters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ThresholdFilters::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.ThresholdFilters)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && crop_ != nullptr) {
    delete crop_;
  }
  crop_ = nullptr;
  if (GetArenaForAllocation() == nullptr && weed_ != nullptr) {
    delete weed_;
  }
  weed_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ThresholdFilters::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.banding.ThresholdFilter crop = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_crop(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.banding.ThresholdFilter weed = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_weed(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ThresholdFilters::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.ThresholdFilters)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.banding.ThresholdFilter crop = 1;
  if (this->_internal_has_crop()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::crop(this), target, stream);
  }

  // .carbon.frontend.banding.ThresholdFilter weed = 2;
  if (this->_internal_has_weed()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::weed(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.ThresholdFilters)
  return target;
}

size_t ThresholdFilters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.ThresholdFilters)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.banding.ThresholdFilter crop = 1;
  if (this->_internal_has_crop()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *crop_);
  }

  // .carbon.frontend.banding.ThresholdFilter weed = 2;
  if (this->_internal_has_weed()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *weed_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ThresholdFilters::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ThresholdFilters::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ThresholdFilters::GetClassData() const { return &_class_data_; }

void ThresholdFilters::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ThresholdFilters *>(to)->MergeFrom(
      static_cast<const ThresholdFilters &>(from));
}


void ThresholdFilters::MergeFrom(const ThresholdFilters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.ThresholdFilters)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_crop()) {
    _internal_mutable_crop()->::carbon::frontend::banding::ThresholdFilter::MergeFrom(from._internal_crop());
  }
  if (from._internal_has_weed()) {
    _internal_mutable_weed()->::carbon::frontend::banding::ThresholdFilter::MergeFrom(from._internal_weed());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ThresholdFilters::CopyFrom(const ThresholdFilters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.ThresholdFilters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ThresholdFilters::IsInitialized() const {
  return true;
}

void ThresholdFilters::InternalSwap(ThresholdFilters* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ThresholdFilters, weed_)
      + sizeof(ThresholdFilters::weed_)
      - PROTOBUF_FIELD_OFFSET(ThresholdFilters, crop_)>(
          reinterpret_cast<char*>(&crop_),
          reinterpret_cast<char*>(&other->crop_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ThresholdFilters::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[18]);
}

// ===================================================================

class GetNextVisualizationDataForAllRowsRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextVisualizationDataForAllRowsRequest* msg);
  static const ::carbon::frontend::banding::ThresholdFilters& threshold_filters(const GetNextVisualizationDataForAllRowsRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextVisualizationDataForAllRowsRequest::_Internal::ts(const GetNextVisualizationDataForAllRowsRequest* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::banding::ThresholdFilters&
GetNextVisualizationDataForAllRowsRequest::_Internal::threshold_filters(const GetNextVisualizationDataForAllRowsRequest* msg) {
  return *msg->threshold_filters_;
}
void GetNextVisualizationDataForAllRowsRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextVisualizationDataForAllRowsRequest::GetNextVisualizationDataForAllRowsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  types_to_include_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest)
}
GetNextVisualizationDataForAllRowsRequest::GetNextVisualizationDataForAllRowsRequest(const GetNextVisualizationDataForAllRowsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      types_to_include_(from.types_to_include_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_threshold_filters()) {
    threshold_filters_ = new ::carbon::frontend::banding::ThresholdFilters(*from.threshold_filters_);
  } else {
    threshold_filters_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest)
}

inline void GetNextVisualizationDataForAllRowsRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&threshold_filters_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(threshold_filters_));
}

GetNextVisualizationDataForAllRowsRequest::~GetNextVisualizationDataForAllRowsRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextVisualizationDataForAllRowsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete threshold_filters_;
}

void GetNextVisualizationDataForAllRowsRequest::ArenaDtor(void* object) {
  GetNextVisualizationDataForAllRowsRequest* _this = reinterpret_cast< GetNextVisualizationDataForAllRowsRequest* >(object);
  (void)_this;
}
void GetNextVisualizationDataForAllRowsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextVisualizationDataForAllRowsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextVisualizationDataForAllRowsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  types_to_include_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && threshold_filters_ != nullptr) {
    delete threshold_filters_;
  }
  threshold_filters_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextVisualizationDataForAllRowsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedEnumParser(_internal_mutable_types_to_include(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 16) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_add_types_to_include(static_cast<::carbon::frontend::banding::VisualizationTypeToInclude>(val));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.banding.ThresholdFilters threshold_filters = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_threshold_filters(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextVisualizationDataForAllRowsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 2;
  {
    int byte_size = _types_to_include_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteEnumPacked(
          2, types_to_include_, byte_size, target);
    }
  }

  // .carbon.frontend.banding.ThresholdFilters threshold_filters = 3;
  if (this->_internal_has_threshold_filters()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::threshold_filters(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest)
  return target;
}

size_t GetNextVisualizationDataForAllRowsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 2;
  {
    size_t data_size = 0;
    unsigned int count = static_cast<unsigned int>(this->_internal_types_to_include_size());for (unsigned int i = 0; i < count; i++) {
      data_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(
        this->_internal_types_to_include(static_cast<int>(i)));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _types_to_include_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.banding.ThresholdFilters threshold_filters = 3;
  if (this->_internal_has_threshold_filters()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *threshold_filters_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextVisualizationDataForAllRowsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextVisualizationDataForAllRowsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextVisualizationDataForAllRowsRequest::GetClassData() const { return &_class_data_; }

void GetNextVisualizationDataForAllRowsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextVisualizationDataForAllRowsRequest *>(to)->MergeFrom(
      static_cast<const GetNextVisualizationDataForAllRowsRequest &>(from));
}


void GetNextVisualizationDataForAllRowsRequest::MergeFrom(const GetNextVisualizationDataForAllRowsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  types_to_include_.MergeFrom(from.types_to_include_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_threshold_filters()) {
    _internal_mutable_threshold_filters()->::carbon::frontend::banding::ThresholdFilters::MergeFrom(from._internal_threshold_filters());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextVisualizationDataForAllRowsRequest::CopyFrom(const GetNextVisualizationDataForAllRowsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextVisualizationDataForAllRowsRequest::IsInitialized() const {
  return true;
}

void GetNextVisualizationDataForAllRowsRequest::InternalSwap(GetNextVisualizationDataForAllRowsRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  types_to_include_.InternalSwap(&other->types_to_include_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextVisualizationDataForAllRowsRequest, threshold_filters_)
      + sizeof(GetNextVisualizationDataForAllRowsRequest::threshold_filters_)
      - PROTOBUF_FIELD_OFFSET(GetNextVisualizationDataForAllRowsRequest, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextVisualizationDataForAllRowsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[19]);
}

// ===================================================================

GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse() {}
GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse::MergeFrom(const GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[20]);
}

// ===================================================================

class GetNextVisualizationDataForAllRowsResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextVisualizationDataForAllRowsResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextVisualizationDataForAllRowsResponse::_Internal::ts(const GetNextVisualizationDataForAllRowsResponse* msg) {
  return *msg->ts_;
}
void GetNextVisualizationDataForAllRowsResponse::clear_data_per_row() {
  data_per_row_.Clear();
}
void GetNextVisualizationDataForAllRowsResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextVisualizationDataForAllRowsResponse::GetNextVisualizationDataForAllRowsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  data_per_row_(arena),
  types_to_include_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse)
}
GetNextVisualizationDataForAllRowsResponse::GetNextVisualizationDataForAllRowsResponse(const GetNextVisualizationDataForAllRowsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      types_to_include_(from.types_to_include_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  data_per_row_.MergeFrom(from.data_per_row_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse)
}

inline void GetNextVisualizationDataForAllRowsResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextVisualizationDataForAllRowsResponse::~GetNextVisualizationDataForAllRowsResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextVisualizationDataForAllRowsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextVisualizationDataForAllRowsResponse::ArenaDtor(void* object) {
  GetNextVisualizationDataForAllRowsResponse* _this = reinterpret_cast< GetNextVisualizationDataForAllRowsResponse* >(object);
  (void)_this;
  _this->data_per_row_. ~MapField();
}
inline void GetNextVisualizationDataForAllRowsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &GetNextVisualizationDataForAllRowsResponse::ArenaDtor);
  }
}
void GetNextVisualizationDataForAllRowsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextVisualizationDataForAllRowsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  data_per_row_.Clear();
  types_to_include_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextVisualizationDataForAllRowsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<int32, .weed_tracking.DiagnosticsSnapshot> data_per_row = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&data_per_row_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedEnumParser(_internal_mutable_types_to_include(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 24) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_add_types_to_include(static_cast<::carbon::frontend::banding::VisualizationTypeToInclude>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextVisualizationDataForAllRowsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<int32, .weed_tracking.DiagnosticsSnapshot> data_per_row = 1;
  if (!this->_internal_data_per_row().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_data_per_row().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_data_per_row().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >::const_iterator
          it = this->_internal_data_per_row().begin();
          it != this->_internal_data_per_row().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >::const_iterator
          it = this->_internal_data_per_row().begin();
          it != this->_internal_data_per_row().end(); ++it) {
        target = GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
      }
    }
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 3;
  {
    int byte_size = _types_to_include_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteEnumPacked(
          3, types_to_include_, byte_size, target);
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse)
  return target;
}

size_t GetNextVisualizationDataForAllRowsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<int32, .weed_tracking.DiagnosticsSnapshot> data_per_row = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_data_per_row_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >::const_iterator
      it = this->_internal_data_per_row().begin();
      it != this->_internal_data_per_row().end(); ++it) {
    total_size += GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 3;
  {
    size_t data_size = 0;
    unsigned int count = static_cast<unsigned int>(this->_internal_types_to_include_size());for (unsigned int i = 0; i < count; i++) {
      data_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(
        this->_internal_types_to_include(static_cast<int>(i)));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _types_to_include_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextVisualizationDataForAllRowsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextVisualizationDataForAllRowsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextVisualizationDataForAllRowsResponse::GetClassData() const { return &_class_data_; }

void GetNextVisualizationDataForAllRowsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextVisualizationDataForAllRowsResponse *>(to)->MergeFrom(
      static_cast<const GetNextVisualizationDataForAllRowsResponse &>(from));
}


void GetNextVisualizationDataForAllRowsResponse::MergeFrom(const GetNextVisualizationDataForAllRowsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  data_per_row_.MergeFrom(from.data_per_row_);
  types_to_include_.MergeFrom(from.types_to_include_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextVisualizationDataForAllRowsResponse::CopyFrom(const GetNextVisualizationDataForAllRowsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextVisualizationDataForAllRowsResponse::IsInitialized() const {
  return true;
}

void GetNextVisualizationDataForAllRowsResponse::InternalSwap(GetNextVisualizationDataForAllRowsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  data_per_row_.InternalSwap(&other->data_per_row_);
  types_to_include_.InternalSwap(&other->types_to_include_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextVisualizationDataForAllRowsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[21]);
}

// ===================================================================

class GetNextBandingStateResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextBandingStateResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextBandingStateResponse::_Internal::ts(const GetNextBandingStateResponse* msg) {
  return *msg->ts_;
}
void GetNextBandingStateResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextBandingStateResponse::GetNextBandingStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  bandingdefs_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.banding.GetNextBandingStateResponse)
}
GetNextBandingStateResponse::GetNextBandingStateResponse(const GetNextBandingStateResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      bandingdefs_(from.bandingdefs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  activedefuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    activedefuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_activedefuuid().empty()) {
    activedefuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_activedefuuid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&is_banding_enabled_, &from.is_banding_enabled_,
    static_cast<size_t>(reinterpret_cast<char*>(&is_dynamic_banding_enabled_) -
    reinterpret_cast<char*>(&is_banding_enabled_)) + sizeof(is_dynamic_banding_enabled_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.banding.GetNextBandingStateResponse)
}

inline void GetNextBandingStateResponse::SharedCtor() {
activedefuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  activedefuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&is_dynamic_banding_enabled_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(is_dynamic_banding_enabled_));
}

GetNextBandingStateResponse::~GetNextBandingStateResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.banding.GetNextBandingStateResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextBandingStateResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  activedefuuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextBandingStateResponse::ArenaDtor(void* object) {
  GetNextBandingStateResponse* _this = reinterpret_cast< GetNextBandingStateResponse* >(object);
  (void)_this;
}
void GetNextBandingStateResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextBandingStateResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextBandingStateResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.banding.GetNextBandingStateResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bandingdefs_.Clear();
  activedefuuid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&is_banding_enabled_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&is_dynamic_banding_enabled_) -
      reinterpret_cast<char*>(&is_banding_enabled_)) + sizeof(is_dynamic_banding_enabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextBandingStateResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.banding.BandingDef bandingDefs = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_bandingdefs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string activeDefUUID = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_activedefuuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.banding.GetNextBandingStateResponse.activeDefUUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_banding_enabled = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          is_banding_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_dynamic_banding_enabled = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          is_dynamic_banding_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextBandingStateResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.banding.GetNextBandingStateResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.frontend.banding.BandingDef bandingDefs = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_bandingdefs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_bandingdefs(i), target, stream);
  }

  // string activeDefUUID = 3;
  if (!this->_internal_activedefuuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_activedefuuid().data(), static_cast<int>(this->_internal_activedefuuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.banding.GetNextBandingStateResponse.activeDefUUID");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_activedefuuid(), target);
  }

  // bool is_banding_enabled = 4;
  if (this->_internal_is_banding_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_is_banding_enabled(), target);
  }

  // bool is_dynamic_banding_enabled = 5;
  if (this->_internal_is_dynamic_banding_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_is_dynamic_banding_enabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.banding.GetNextBandingStateResponse)
  return target;
}

size_t GetNextBandingStateResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.banding.GetNextBandingStateResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.banding.BandingDef bandingDefs = 2;
  total_size += 1UL * this->_internal_bandingdefs_size();
  for (const auto& msg : this->bandingdefs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string activeDefUUID = 3;
  if (!this->_internal_activedefuuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_activedefuuid());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // bool is_banding_enabled = 4;
  if (this->_internal_is_banding_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool is_dynamic_banding_enabled = 5;
  if (this->_internal_is_dynamic_banding_enabled() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextBandingStateResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextBandingStateResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextBandingStateResponse::GetClassData() const { return &_class_data_; }

void GetNextBandingStateResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextBandingStateResponse *>(to)->MergeFrom(
      static_cast<const GetNextBandingStateResponse &>(from));
}


void GetNextBandingStateResponse::MergeFrom(const GetNextBandingStateResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.banding.GetNextBandingStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  bandingdefs_.MergeFrom(from.bandingdefs_);
  if (!from._internal_activedefuuid().empty()) {
    _internal_set_activedefuuid(from._internal_activedefuuid());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_is_banding_enabled() != 0) {
    _internal_set_is_banding_enabled(from._internal_is_banding_enabled());
  }
  if (from._internal_is_dynamic_banding_enabled() != 0) {
    _internal_set_is_dynamic_banding_enabled(from._internal_is_dynamic_banding_enabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextBandingStateResponse::CopyFrom(const GetNextBandingStateResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.banding.GetNextBandingStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextBandingStateResponse::IsInitialized() const {
  return true;
}

void GetNextBandingStateResponse::InternalSwap(GetNextBandingStateResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  bandingdefs_.InternalSwap(&other->bandingdefs_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &activedefuuid_, lhs_arena,
      &other->activedefuuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextBandingStateResponse, is_dynamic_banding_enabled_)
      + sizeof(GetNextBandingStateResponse::is_dynamic_banding_enabled_)
      - PROTOBUF_FIELD_OFFSET(GetNextBandingStateResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextBandingStateResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fbanding_2eproto_getter, &descriptor_table_frontend_2fproto_2fbanding_2eproto_once,
      file_level_metadata_frontend_2fproto_2fbanding_2eproto[22]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace banding
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::BandingRow* Arena::CreateMaybeMessage< ::carbon::frontend::banding::BandingRow >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::BandingRow >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::BandingDef* Arena::CreateMaybeMessage< ::carbon::frontend::banding::BandingDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::BandingDef >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::SaveBandingDefRequest* Arena::CreateMaybeMessage< ::carbon::frontend::banding::SaveBandingDefRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::SaveBandingDefRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::LoadBandingDefsResponse* Arena::CreateMaybeMessage< ::carbon::frontend::banding::LoadBandingDefsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::LoadBandingDefsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::SetActiveBandingDefRequest* Arena::CreateMaybeMessage< ::carbon::frontend::banding::SetActiveBandingDefRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::SetActiveBandingDefRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::GetActiveBandingDefResponse* Arena::CreateMaybeMessage< ::carbon::frontend::banding::GetActiveBandingDefResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::GetActiveBandingDefResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::DeleteBandingDefRequest* Arena::CreateMaybeMessage< ::carbon::frontend::banding::DeleteBandingDefRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::DeleteBandingDefRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::VisualizationData* Arena::CreateMaybeMessage< ::carbon::frontend::banding::VisualizationData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::VisualizationData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::GetNextVisualizationDataRequest* Arena::CreateMaybeMessage< ::carbon::frontend::banding::GetNextVisualizationDataRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::GetNextVisualizationDataRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::GetNextVisualizationDataResponse* Arena::CreateMaybeMessage< ::carbon::frontend::banding::GetNextVisualizationDataResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::GetNextVisualizationDataResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::GetNextVisualizationData2Response* Arena::CreateMaybeMessage< ::carbon::frontend::banding::GetNextVisualizationData2Response >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::GetNextVisualizationData2Response >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::GetDimensionsRequest* Arena::CreateMaybeMessage< ::carbon::frontend::banding::GetDimensionsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::GetDimensionsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::SetBandingEnabledRequest* Arena::CreateMaybeMessage< ::carbon::frontend::banding::SetBandingEnabledRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::SetBandingEnabledRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::SetBandingEnabledResponse* Arena::CreateMaybeMessage< ::carbon::frontend::banding::SetBandingEnabledResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::SetBandingEnabledResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::IsBandingEnabledResponse* Arena::CreateMaybeMessage< ::carbon::frontend::banding::IsBandingEnabledResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::IsBandingEnabledResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::banding::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::GetVisualizationMetadataResponse* Arena::CreateMaybeMessage< ::carbon::frontend::banding::GetVisualizationMetadataResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::GetVisualizationMetadataResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::ThresholdFilter* Arena::CreateMaybeMessage< ::carbon::frontend::banding::ThresholdFilter >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::ThresholdFilter >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::ThresholdFilters* Arena::CreateMaybeMessage< ::carbon::frontend::banding::ThresholdFilters >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::ThresholdFilters >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* Arena::CreateMaybeMessage< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* Arena::CreateMaybeMessage< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::banding::GetNextBandingStateResponse* Arena::CreateMaybeMessage< ::carbon::frontend::banding::GetNextBandingStateResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::banding::GetNextBandingStateResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

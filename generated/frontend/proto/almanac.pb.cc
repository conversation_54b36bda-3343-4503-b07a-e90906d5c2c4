// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/almanac.proto

#include "frontend/proto/almanac.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace almanac {
constexpr GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal {
  constexpr GetConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal _GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse_default_instance_;
constexpr GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal {
  constexpr GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal _GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse_default_instance_;
constexpr GetConfigDataResponse::GetConfigDataResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_category_names_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , weed_category_names_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , num_size_categories_(0u){}
struct GetConfigDataResponseDefaultTypeInternal {
  constexpr GetConfigDataResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetConfigDataResponseDefaultTypeInternal() {}
  union {
    GetConfigDataResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetConfigDataResponseDefaultTypeInternal _GetConfigDataResponse_default_instance_;
constexpr LoadAlmanacConfigRequest::LoadAlmanacConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct LoadAlmanacConfigRequestDefaultTypeInternal {
  constexpr LoadAlmanacConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LoadAlmanacConfigRequestDefaultTypeInternal() {}
  union {
    LoadAlmanacConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LoadAlmanacConfigRequestDefaultTypeInternal _LoadAlmanacConfigRequest_default_instance_;
constexpr LoadAlmanacConfigResponse::LoadAlmanacConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : config_(nullptr){}
struct LoadAlmanacConfigResponseDefaultTypeInternal {
  constexpr LoadAlmanacConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LoadAlmanacConfigResponseDefaultTypeInternal() {}
  union {
    LoadAlmanacConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LoadAlmanacConfigResponseDefaultTypeInternal _LoadAlmanacConfigResponse_default_instance_;
constexpr SaveAlmanacConfigRequest::SaveAlmanacConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : config_(nullptr)
  , set_active_(false){}
struct SaveAlmanacConfigRequestDefaultTypeInternal {
  constexpr SaveAlmanacConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SaveAlmanacConfigRequestDefaultTypeInternal() {}
  union {
    SaveAlmanacConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SaveAlmanacConfigRequestDefaultTypeInternal _SaveAlmanacConfigRequest_default_instance_;
constexpr SaveAlmanacConfigResponse::SaveAlmanacConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SaveAlmanacConfigResponseDefaultTypeInternal {
  constexpr SaveAlmanacConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SaveAlmanacConfigResponseDefaultTypeInternal() {}
  union {
    SaveAlmanacConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SaveAlmanacConfigResponseDefaultTypeInternal _SaveAlmanacConfigResponse_default_instance_;
constexpr SetActiveAlmanacConfigRequest::SetActiveAlmanacConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SetActiveAlmanacConfigRequestDefaultTypeInternal {
  constexpr SetActiveAlmanacConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetActiveAlmanacConfigRequestDefaultTypeInternal() {}
  union {
    SetActiveAlmanacConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetActiveAlmanacConfigRequestDefaultTypeInternal _SetActiveAlmanacConfigRequest_default_instance_;
constexpr DeleteAlmanacConfigRequest::DeleteAlmanacConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , new_active_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DeleteAlmanacConfigRequestDefaultTypeInternal {
  constexpr DeleteAlmanacConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeleteAlmanacConfigRequestDefaultTypeInternal() {}
  union {
    DeleteAlmanacConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeleteAlmanacConfigRequestDefaultTypeInternal _DeleteAlmanacConfigRequest_default_instance_;
constexpr GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetNextAlmanacConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal {
  constexpr GetNextAlmanacConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextAlmanacConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextAlmanacConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal _GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse_default_instance_;
constexpr GetNextAlmanacConfigResponse::GetNextAlmanacConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : available_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , active_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextAlmanacConfigResponseDefaultTypeInternal {
  constexpr GetNextAlmanacConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextAlmanacConfigResponseDefaultTypeInternal() {}
  union {
    GetNextAlmanacConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextAlmanacConfigResponseDefaultTypeInternal _GetNextAlmanacConfigResponse_default_instance_;
constexpr LoadDiscriminatorConfigRequest::LoadDiscriminatorConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct LoadDiscriminatorConfigRequestDefaultTypeInternal {
  constexpr LoadDiscriminatorConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LoadDiscriminatorConfigRequestDefaultTypeInternal() {}
  union {
    LoadDiscriminatorConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LoadDiscriminatorConfigRequestDefaultTypeInternal _LoadDiscriminatorConfigRequest_default_instance_;
constexpr LoadDiscriminatorConfigResponse::LoadDiscriminatorConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : config_(nullptr){}
struct LoadDiscriminatorConfigResponseDefaultTypeInternal {
  constexpr LoadDiscriminatorConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LoadDiscriminatorConfigResponseDefaultTypeInternal() {}
  union {
    LoadDiscriminatorConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LoadDiscriminatorConfigResponseDefaultTypeInternal _LoadDiscriminatorConfigResponse_default_instance_;
constexpr SaveDiscriminatorConfigRequest::SaveDiscriminatorConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : config_(nullptr)
  , associate_with_active_crop_(false){}
struct SaveDiscriminatorConfigRequestDefaultTypeInternal {
  constexpr SaveDiscriminatorConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SaveDiscriminatorConfigRequestDefaultTypeInternal() {}
  union {
    SaveDiscriminatorConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SaveDiscriminatorConfigRequestDefaultTypeInternal _SaveDiscriminatorConfigRequest_default_instance_;
constexpr SaveDiscriminatorConfigResponse::SaveDiscriminatorConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SaveDiscriminatorConfigResponseDefaultTypeInternal {
  constexpr SaveDiscriminatorConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SaveDiscriminatorConfigResponseDefaultTypeInternal() {}
  union {
    SaveDiscriminatorConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SaveDiscriminatorConfigResponseDefaultTypeInternal _SaveDiscriminatorConfigResponse_default_instance_;
constexpr SetActiveDiscriminatorConfigRequest::SetActiveDiscriminatorConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SetActiveDiscriminatorConfigRequestDefaultTypeInternal {
  constexpr SetActiveDiscriminatorConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetActiveDiscriminatorConfigRequestDefaultTypeInternal() {}
  union {
    SetActiveDiscriminatorConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetActiveDiscriminatorConfigRequestDefaultTypeInternal _SetActiveDiscriminatorConfigRequest_default_instance_;
constexpr DeleteDiscriminatorConfigRequest::DeleteDiscriminatorConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DeleteDiscriminatorConfigRequestDefaultTypeInternal {
  constexpr DeleteDiscriminatorConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeleteDiscriminatorConfigRequestDefaultTypeInternal() {}
  union {
    DeleteDiscriminatorConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeleteDiscriminatorConfigRequestDefaultTypeInternal _DeleteDiscriminatorConfigRequest_default_instance_;
constexpr GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal {
  constexpr GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal _GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse_default_instance_;
constexpr GetNextDiscriminatorConfigResponse::GetNextDiscriminatorConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : available_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , active_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextDiscriminatorConfigResponseDefaultTypeInternal {
  constexpr GetNextDiscriminatorConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextDiscriminatorConfigResponseDefaultTypeInternal() {}
  union {
    GetNextDiscriminatorConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextDiscriminatorConfigResponseDefaultTypeInternal _GetNextDiscriminatorConfigResponse_default_instance_;
constexpr GetNextModelinatorConfigResponse::GetNextModelinatorConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , config_(nullptr){}
struct GetNextModelinatorConfigResponseDefaultTypeInternal {
  constexpr GetNextModelinatorConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextModelinatorConfigResponseDefaultTypeInternal() {}
  union {
    GetNextModelinatorConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextModelinatorConfigResponseDefaultTypeInternal _GetNextModelinatorConfigResponse_default_instance_;
constexpr SaveModelinatorConfigRequest::SaveModelinatorConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : config_(nullptr){}
struct SaveModelinatorConfigRequestDefaultTypeInternal {
  constexpr SaveModelinatorConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SaveModelinatorConfigRequestDefaultTypeInternal() {}
  union {
    SaveModelinatorConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SaveModelinatorConfigRequestDefaultTypeInternal _SaveModelinatorConfigRequest_default_instance_;
constexpr FetchModelinatorConfigRequest::FetchModelinatorConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct FetchModelinatorConfigRequestDefaultTypeInternal {
  constexpr FetchModelinatorConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FetchModelinatorConfigRequestDefaultTypeInternal() {}
  union {
    FetchModelinatorConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FetchModelinatorConfigRequestDefaultTypeInternal _FetchModelinatorConfigRequest_default_instance_;
constexpr FetchModelinatorConfigResponse::FetchModelinatorConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : config_(nullptr){}
struct FetchModelinatorConfigResponseDefaultTypeInternal {
  constexpr FetchModelinatorConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FetchModelinatorConfigResponseDefaultTypeInternal() {}
  union {
    FetchModelinatorConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FetchModelinatorConfigResponseDefaultTypeInternal _FetchModelinatorConfigResponse_default_instance_;
constexpr ResetModelinatorConfigRequest::ResetModelinatorConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ResetModelinatorConfigRequestDefaultTypeInternal {
  constexpr ResetModelinatorConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ResetModelinatorConfigRequestDefaultTypeInternal() {}
  union {
    ResetModelinatorConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ResetModelinatorConfigRequestDefaultTypeInternal _ResetModelinatorConfigRequest_default_instance_;
constexpr GetNextConfigDataRequest::GetNextConfigDataRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lang_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextConfigDataRequestDefaultTypeInternal {
  constexpr GetNextConfigDataRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextConfigDataRequestDefaultTypeInternal() {}
  union {
    GetNextConfigDataRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextConfigDataRequestDefaultTypeInternal _GetNextConfigDataRequest_default_instance_;
constexpr GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal {
  constexpr GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal _GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse_default_instance_;
constexpr GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal {
  constexpr GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal _GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse_default_instance_;
constexpr GetNextConfigDataResponse::GetNextConfigDataResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_category_names_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , weed_category_names_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , ts_(nullptr)
  , num_size_categories_(0u){}
struct GetNextConfigDataResponseDefaultTypeInternal {
  constexpr GetNextConfigDataResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextConfigDataResponseDefaultTypeInternal() {}
  union {
    GetNextConfigDataResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextConfigDataResponseDefaultTypeInternal _GetNextConfigDataResponse_default_instance_;
}  // namespace almanac
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2falmanac_2eproto[28];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2falmanac_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2falmanac_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2falmanac_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse, num_size_categories_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse, crop_category_names_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetConfigDataResponse, weed_category_names_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::LoadAlmanacConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::LoadAlmanacConfigRequest, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::LoadAlmanacConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::LoadAlmanacConfigResponse, config_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveAlmanacConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveAlmanacConfigRequest, config_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveAlmanacConfigRequest, set_active_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveAlmanacConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveAlmanacConfigResponse, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SetActiveAlmanacConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SetActiveAlmanacConfigRequest, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::DeleteAlmanacConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::DeleteAlmanacConfigRequest, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::DeleteAlmanacConfigRequest, new_active_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextAlmanacConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextAlmanacConfigResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextAlmanacConfigResponse, active_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextAlmanacConfigResponse, available_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::LoadDiscriminatorConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::LoadDiscriminatorConfigRequest, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::LoadDiscriminatorConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::LoadDiscriminatorConfigResponse, config_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, config_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, associate_with_active_crop_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveDiscriminatorConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveDiscriminatorConfigResponse, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, crop_id_),
  ~0u,
  0,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse, active_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse, available_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextModelinatorConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextModelinatorConfigResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextModelinatorConfigResponse, config_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveModelinatorConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::SaveModelinatorConfigRequest, config_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::FetchModelinatorConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::FetchModelinatorConfigRequest, model_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::FetchModelinatorConfigRequest, crop_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::FetchModelinatorConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::FetchModelinatorConfigResponse, config_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::ResetModelinatorConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataRequest, lang_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse, num_size_categories_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse, crop_category_names_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::almanac::GetNextConfigDataResponse, weed_category_names_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 8, -1, sizeof(::carbon::frontend::almanac::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse)},
  { 10, 18, -1, sizeof(::carbon::frontend::almanac::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse)},
  { 20, -1, -1, sizeof(::carbon::frontend::almanac::GetConfigDataResponse)},
  { 29, -1, -1, sizeof(::carbon::frontend::almanac::LoadAlmanacConfigRequest)},
  { 36, -1, -1, sizeof(::carbon::frontend::almanac::LoadAlmanacConfigResponse)},
  { 43, -1, -1, sizeof(::carbon::frontend::almanac::SaveAlmanacConfigRequest)},
  { 51, -1, -1, sizeof(::carbon::frontend::almanac::SaveAlmanacConfigResponse)},
  { 58, -1, -1, sizeof(::carbon::frontend::almanac::SetActiveAlmanacConfigRequest)},
  { 65, -1, -1, sizeof(::carbon::frontend::almanac::DeleteAlmanacConfigRequest)},
  { 73, 81, -1, sizeof(::carbon::frontend::almanac::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse)},
  { 83, -1, -1, sizeof(::carbon::frontend::almanac::GetNextAlmanacConfigResponse)},
  { 92, -1, -1, sizeof(::carbon::frontend::almanac::LoadDiscriminatorConfigRequest)},
  { 99, -1, -1, sizeof(::carbon::frontend::almanac::LoadDiscriminatorConfigResponse)},
  { 106, -1, -1, sizeof(::carbon::frontend::almanac::SaveDiscriminatorConfigRequest)},
  { 114, -1, -1, sizeof(::carbon::frontend::almanac::SaveDiscriminatorConfigResponse)},
  { 121, 129, -1, sizeof(::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest)},
  { 131, -1, -1, sizeof(::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest)},
  { 138, 146, -1, sizeof(::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse)},
  { 148, -1, -1, sizeof(::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse)},
  { 157, -1, -1, sizeof(::carbon::frontend::almanac::GetNextModelinatorConfigResponse)},
  { 165, -1, -1, sizeof(::carbon::frontend::almanac::SaveModelinatorConfigRequest)},
  { 172, -1, -1, sizeof(::carbon::frontend::almanac::FetchModelinatorConfigRequest)},
  { 180, -1, -1, sizeof(::carbon::frontend::almanac::FetchModelinatorConfigResponse)},
  { 187, -1, -1, sizeof(::carbon::frontend::almanac::ResetModelinatorConfigRequest)},
  { 193, -1, -1, sizeof(::carbon::frontend::almanac::GetNextConfigDataRequest)},
  { 201, 209, -1, sizeof(::carbon::frontend::almanac::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse)},
  { 211, 219, -1, sizeof(::carbon::frontend::almanac::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse)},
  { 221, -1, -1, sizeof(::carbon::frontend::almanac::GetNextConfigDataResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetConfigDataResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_LoadAlmanacConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_LoadAlmanacConfigResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_SaveAlmanacConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_SaveAlmanacConfigResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_SetActiveAlmanacConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_DeleteAlmanacConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetNextAlmanacConfigResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_LoadDiscriminatorConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_LoadDiscriminatorConfigResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_SaveDiscriminatorConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_SaveDiscriminatorConfigResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_SetActiveDiscriminatorConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_DeleteDiscriminatorConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetNextDiscriminatorConfigResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetNextModelinatorConfigResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_SaveModelinatorConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_FetchModelinatorConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_FetchModelinatorConfigResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_ResetModelinatorConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetNextConfigDataRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::almanac::_GetNextConfigDataResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2falmanac_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\034frontend/proto/almanac.proto\022\027carbon.f"
  "rontend.almanac\032\031frontend/proto/util.pro"
  "to\032\033proto/almanac/almanac.proto\"\364\002\n\025GetC"
  "onfigDataResponse\022\033\n\023num_size_categories"
  "\030\001 \001(\r\022b\n\023crop_category_names\030\002 \003(\0132E.ca"
  "rbon.frontend.almanac.GetConfigDataRespo"
  "nse.CropCategoryNamesEntry\022b\n\023weed_categ"
  "ory_names\030\003 \003(\0132E.carbon.frontend.almana"
  "c.GetConfigDataResponse.WeedCategoryName"
  "sEntry\0328\n\026CropCategoryNamesEntry\022\013\n\003key\030"
  "\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\0328\n\026WeedCategory"
  "NamesEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\002"
  "8\001:\002\030\001\"&\n\030LoadAlmanacConfigRequest\022\n\n\002id"
  "\030\001 \001(\t\"Q\n\031LoadAlmanacConfigResponse\0224\n\006c"
  "onfig\030\001 \001(\0132$.carbon.aimbot.almanac.Alma"
  "nacConfig\"d\n\030SaveAlmanacConfigRequest\0224\n"
  "\006config\030\001 \001(\0132$.carbon.aimbot.almanac.Al"
  "manacConfig\022\022\n\nset_active\030\002 \001(\010\"\'\n\031SaveA"
  "lmanacConfigResponse\022\n\n\002id\030\001 \001(\t\"+\n\035SetA"
  "ctiveAlmanacConfigRequest\022\n\n\002id\030\001 \001(\t\"\?\n"
  "\032DeleteAlmanacConfigRequest\022\n\n\002id\030\001 \001(\t\022"
  "\025\n\rnew_active_id\030\002 \001(\t\"\346\001\n\034GetNextAlmana"
  "cConfigResponse\022+\n\002ts\030\001 \001(\0132\037.carbon.fro"
  "ntend.util.Timestamp\022\016\n\006active\030\002 \001(\t\022W\n\t"
  "available\030\003 \003(\0132D.carbon.frontend.almana"
  "c.GetNextAlmanacConfigResponse.Available"
  "Entry\0320\n\016AvailableEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005"
  "value\030\002 \001(\t:\0028\001\",\n\036LoadDiscriminatorConf"
  "igRequest\022\n\n\002id\030\001 \001(\t\"]\n\037LoadDiscriminat"
  "orConfigResponse\022:\n\006config\030\001 \001(\0132*.carbo"
  "n.aimbot.almanac.DiscriminatorConfig\"\200\001\n"
  "\036SaveDiscriminatorConfigRequest\022:\n\006confi"
  "g\030\001 \001(\0132*.carbon.aimbot.almanac.Discrimi"
  "natorConfig\022\"\n\032associate_with_active_cro"
  "p\030\002 \001(\010\"-\n\037SaveDiscriminatorConfigRespon"
  "se\022\n\n\002id\030\001 \001(\t\"S\n#SetActiveDiscriminator"
  "ConfigRequest\022\n\n\002id\030\001 \001(\t\022\024\n\007crop_id\030\002 \001"
  "(\tH\000\210\001\001B\n\n\010_crop_id\".\n DeleteDiscriminat"
  "orConfigRequest\022\n\n\002id\030\001 \001(\t\"\362\001\n\"GetNextD"
  "iscriminatorConfigResponse\022+\n\002ts\030\001 \001(\0132\037"
  ".carbon.frontend.util.Timestamp\022\016\n\006activ"
  "e\030\002 \001(\t\022]\n\tavailable\030\003 \003(\0132J.carbon.fron"
  "tend.almanac.GetNextDiscriminatorConfigR"
  "esponse.AvailableEntry\0320\n\016AvailableEntry"
  "\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\211\001\n Get"
  "NextModelinatorConfigResponse\022+\n\002ts\030\001 \001("
  "\0132\037.carbon.frontend.util.Timestamp\0228\n\006co"
  "nfig\030\002 \001(\0132(.carbon.aimbot.almanac.Model"
  "inatorConfig\"X\n\034SaveModelinatorConfigReq"
  "uest\0228\n\006config\030\001 \001(\0132(.carbon.aimbot.alm"
  "anac.ModelinatorConfig\"B\n\035FetchModelinat"
  "orConfigRequest\022\020\n\010model_id\030\001 \001(\t\022\017\n\007cro"
  "p_id\030\002 \001(\t\"Z\n\036FetchModelinatorConfigResp"
  "onse\0228\n\006config\030\001 \001(\0132(.carbon.aimbot.alm"
  "anac.ModelinatorConfig\"\037\n\035ResetModelinat"
  "orConfigRequest\"U\n\030GetNextConfigDataRequ"
  "est\022+\n\002ts\030\001 \001(\0132\037.carbon.frontend.util.T"
  "imestamp\022\014\n\004lang\030\002 \001(\t\"\251\003\n\031GetNextConfig"
  "DataResponse\022+\n\002ts\030\001 \001(\0132\037.carbon.fronte"
  "nd.util.Timestamp\022\033\n\023num_size_categories"
  "\030\002 \001(\r\022f\n\023crop_category_names\030\003 \003(\0132I.ca"
  "rbon.frontend.almanac.GetNextConfigDataR"
  "esponse.CropCategoryNamesEntry\022f\n\023weed_c"
  "ategory_names\030\004 \003(\0132I.carbon.frontend.al"
  "manac.GetNextConfigDataResponse.WeedCate"
  "goryNamesEntry\0328\n\026CropCategoryNamesEntry"
  "\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\0328\n\026Weed"
  "CategoryNamesEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value"
  "\030\002 \001(\t:\0028\0012\237\017\n\024AlmanacConfigService\022a\n\rG"
  "etConfigData\022\033.carbon.frontend.util.Empt"
  "y\032..carbon.frontend.almanac.GetConfigDat"
  "aResponse\"\003\210\002\001\022z\n\021GetNextConfigData\0221.ca"
  "rbon.frontend.almanac.GetNextConfigDataR"
  "equest\0322.carbon.frontend.almanac.GetNext"
  "ConfigDataResponse\022z\n\021LoadAlmanacConfig\022"
  "1.carbon.frontend.almanac.LoadAlmanacCon"
  "figRequest\0322.carbon.frontend.almanac.Loa"
  "dAlmanacConfigResponse\022z\n\021SaveAlmanacCon"
  "fig\0221.carbon.frontend.almanac.SaveAlmana"
  "cConfigRequest\0322.carbon.frontend.almanac"
  ".SaveAlmanacConfigResponse\022m\n\026SetActiveA"
  "lmanacConfig\0226.carbon.frontend.almanac.S"
  "etActiveAlmanacConfigRequest\032\033.carbon.fr"
  "ontend.util.Empty\022g\n\023DeleteAlmanacConfig"
  "\0223.carbon.frontend.almanac.DeleteAlmanac"
  "ConfigRequest\032\033.carbon.frontend.util.Emp"
  "ty\022n\n\024GetNextAlmanacConfig\022\037.carbon.fron"
  "tend.util.Timestamp\0325.carbon.frontend.al"
  "manac.GetNextAlmanacConfigResponse\022\214\001\n\027L"
  "oadDiscriminatorConfig\0227.carbon.frontend"
  ".almanac.LoadDiscriminatorConfigRequest\032"
  "8.carbon.frontend.almanac.LoadDiscrimina"
  "torConfigResponse\022\214\001\n\027SaveDiscriminatorC"
  "onfig\0227.carbon.frontend.almanac.SaveDisc"
  "riminatorConfigRequest\0328.carbon.frontend"
  ".almanac.SaveDiscriminatorConfigResponse"
  "\022y\n\034SetActiveDiscriminatorConfig\022<.carbo"
  "n.frontend.almanac.SetActiveDiscriminato"
  "rConfigRequest\032\033.carbon.frontend.util.Em"
  "pty\022s\n\031DeleteDiscriminatorConfig\0229.carbo"
  "n.frontend.almanac.DeleteDiscriminatorCo"
  "nfigRequest\032\033.carbon.frontend.util.Empty"
  "\022z\n\032GetNextDiscriminatorConfig\022\037.carbon."
  "frontend.util.Timestamp\032;.carbon.fronten"
  "d.almanac.GetNextDiscriminatorConfigResp"
  "onse\022v\n\030GetNextModelinatorConfig\022\037.carbo"
  "n.frontend.util.Timestamp\0329.carbon.front"
  "end.almanac.GetNextModelinatorConfigResp"
  "onse\022k\n\025SaveModelinatorConfig\0225.carbon.f"
  "rontend.almanac.SaveModelinatorConfigReq"
  "uest\032\033.carbon.frontend.util.Empty\022\211\001\n\026Fe"
  "tchModelinatorConfig\0226.carbon.frontend.a"
  "lmanac.FetchModelinatorConfigRequest\0327.c"
  "arbon.frontend.almanac.FetchModelinatorC"
  "onfigResponse\022m\n\026ResetModelinatorConfig\022"
  "6.carbon.frontend.almanac.ResetModelinat"
  "orConfigRequest\032\033.carbon.frontend.util.E"
  "mptyB\020Z\016proto/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2falmanac_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
  &::descriptor_table_proto_2falmanac_2falmanac_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2falmanac_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2falmanac_2eproto = {
  false, false, 4710, descriptor_table_protodef_frontend_2fproto_2falmanac_2eproto, "frontend/proto/almanac.proto", 
  &descriptor_table_frontend_2fproto_2falmanac_2eproto_once, descriptor_table_frontend_2fproto_2falmanac_2eproto_deps, 2, 28,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2falmanac_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2falmanac_2eproto, file_level_enum_descriptors_frontend_2fproto_2falmanac_2eproto, file_level_service_descriptors_frontend_2fproto_2falmanac_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2falmanac_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2falmanac_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2falmanac_2eproto(&descriptor_table_frontend_2fproto_2falmanac_2eproto);
namespace carbon {
namespace frontend {
namespace almanac {

// ===================================================================

GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse() {}
GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse::MergeFrom(const GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[0]);
}

// ===================================================================

GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse() {}
GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::MergeFrom(const GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[1]);
}

// ===================================================================

class GetConfigDataResponse::_Internal {
 public:
};

GetConfigDataResponse::GetConfigDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  crop_category_names_(arena),
  weed_category_names_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.GetConfigDataResponse)
}
GetConfigDataResponse::GetConfigDataResponse(const GetConfigDataResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  crop_category_names_.MergeFrom(from.crop_category_names_);
  weed_category_names_.MergeFrom(from.weed_category_names_);
  num_size_categories_ = from.num_size_categories_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.GetConfigDataResponse)
}

inline void GetConfigDataResponse::SharedCtor() {
num_size_categories_ = 0u;
}

GetConfigDataResponse::~GetConfigDataResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.GetConfigDataResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetConfigDataResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetConfigDataResponse::ArenaDtor(void* object) {
  GetConfigDataResponse* _this = reinterpret_cast< GetConfigDataResponse* >(object);
  (void)_this;
  _this->crop_category_names_. ~MapField();
  _this->weed_category_names_. ~MapField();
}
inline void GetConfigDataResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &GetConfigDataResponse::ArenaDtor);
  }
}
void GetConfigDataResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetConfigDataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.GetConfigDataResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  crop_category_names_.Clear();
  weed_category_names_.Clear();
  num_size_categories_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetConfigDataResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 num_size_categories = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          num_size_categories_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, string> crop_category_names = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&crop_category_names_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, string> weed_category_names = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&weed_category_names_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetConfigDataResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.GetConfigDataResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 num_size_categories = 1;
  if (this->_internal_num_size_categories() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_num_size_categories(), target);
  }

  // map<string, string> crop_category_names = 2;
  if (!this->_internal_crop_category_names().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_crop_category_names().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_crop_category_names().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_crop_category_names().begin();
          it != this->_internal_crop_category_names().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse::Funcs::InternalSerialize(2, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_crop_category_names().begin();
          it != this->_internal_crop_category_names().end(); ++it) {
        target = GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse::Funcs::InternalSerialize(2, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, string> weed_category_names = 3;
  if (!this->_internal_weed_category_names().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_weed_category_names().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_weed_category_names().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_weed_category_names().begin();
          it != this->_internal_weed_category_names().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::Funcs::InternalSerialize(3, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_weed_category_names().begin();
          it != this->_internal_weed_category_names().end(); ++it) {
        target = GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::Funcs::InternalSerialize(3, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.GetConfigDataResponse)
  return target;
}

size_t GetConfigDataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.GetConfigDataResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> crop_category_names = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_crop_category_names_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_crop_category_names().begin();
      it != this->_internal_crop_category_names().end(); ++it) {
    total_size += GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, string> weed_category_names = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_weed_category_names_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_weed_category_names().begin();
      it != this->_internal_weed_category_names().end(); ++it) {
    total_size += GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // uint32 num_size_categories = 1;
  if (this->_internal_num_size_categories() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_num_size_categories());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetConfigDataResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetConfigDataResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetConfigDataResponse::GetClassData() const { return &_class_data_; }

void GetConfigDataResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetConfigDataResponse *>(to)->MergeFrom(
      static_cast<const GetConfigDataResponse &>(from));
}


void GetConfigDataResponse::MergeFrom(const GetConfigDataResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.GetConfigDataResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  crop_category_names_.MergeFrom(from.crop_category_names_);
  weed_category_names_.MergeFrom(from.weed_category_names_);
  if (from._internal_num_size_categories() != 0) {
    _internal_set_num_size_categories(from._internal_num_size_categories());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetConfigDataResponse::CopyFrom(const GetConfigDataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.GetConfigDataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetConfigDataResponse::IsInitialized() const {
  return true;
}

void GetConfigDataResponse::InternalSwap(GetConfigDataResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  crop_category_names_.InternalSwap(&other->crop_category_names_);
  weed_category_names_.InternalSwap(&other->weed_category_names_);
  swap(num_size_categories_, other->num_size_categories_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetConfigDataResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[2]);
}

// ===================================================================

class LoadAlmanacConfigRequest::_Internal {
 public:
};

LoadAlmanacConfigRequest::LoadAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.LoadAlmanacConfigRequest)
}
LoadAlmanacConfigRequest::LoadAlmanacConfigRequest(const LoadAlmanacConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.LoadAlmanacConfigRequest)
}

inline void LoadAlmanacConfigRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

LoadAlmanacConfigRequest::~LoadAlmanacConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.LoadAlmanacConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LoadAlmanacConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void LoadAlmanacConfigRequest::ArenaDtor(void* object) {
  LoadAlmanacConfigRequest* _this = reinterpret_cast< LoadAlmanacConfigRequest* >(object);
  (void)_this;
}
void LoadAlmanacConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LoadAlmanacConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LoadAlmanacConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.LoadAlmanacConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LoadAlmanacConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.LoadAlmanacConfigRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LoadAlmanacConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.LoadAlmanacConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.LoadAlmanacConfigRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.LoadAlmanacConfigRequest)
  return target;
}

size_t LoadAlmanacConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.LoadAlmanacConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LoadAlmanacConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LoadAlmanacConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LoadAlmanacConfigRequest::GetClassData() const { return &_class_data_; }

void LoadAlmanacConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LoadAlmanacConfigRequest *>(to)->MergeFrom(
      static_cast<const LoadAlmanacConfigRequest &>(from));
}


void LoadAlmanacConfigRequest::MergeFrom(const LoadAlmanacConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.LoadAlmanacConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LoadAlmanacConfigRequest::CopyFrom(const LoadAlmanacConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.LoadAlmanacConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadAlmanacConfigRequest::IsInitialized() const {
  return true;
}

void LoadAlmanacConfigRequest::InternalSwap(LoadAlmanacConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata LoadAlmanacConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[3]);
}

// ===================================================================

class LoadAlmanacConfigResponse::_Internal {
 public:
  static const ::carbon::aimbot::almanac::AlmanacConfig& config(const LoadAlmanacConfigResponse* msg);
};

const ::carbon::aimbot::almanac::AlmanacConfig&
LoadAlmanacConfigResponse::_Internal::config(const LoadAlmanacConfigResponse* msg) {
  return *msg->config_;
}
void LoadAlmanacConfigResponse::clear_config() {
  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
}
LoadAlmanacConfigResponse::LoadAlmanacConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.LoadAlmanacConfigResponse)
}
LoadAlmanacConfigResponse::LoadAlmanacConfigResponse(const LoadAlmanacConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_config()) {
    config_ = new ::carbon::aimbot::almanac::AlmanacConfig(*from.config_);
  } else {
    config_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.LoadAlmanacConfigResponse)
}

inline void LoadAlmanacConfigResponse::SharedCtor() {
config_ = nullptr;
}

LoadAlmanacConfigResponse::~LoadAlmanacConfigResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.LoadAlmanacConfigResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LoadAlmanacConfigResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete config_;
}

void LoadAlmanacConfigResponse::ArenaDtor(void* object) {
  LoadAlmanacConfigResponse* _this = reinterpret_cast< LoadAlmanacConfigResponse* >(object);
  (void)_this;
}
void LoadAlmanacConfigResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LoadAlmanacConfigResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LoadAlmanacConfigResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.LoadAlmanacConfigResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LoadAlmanacConfigResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.AlmanacConfig config = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LoadAlmanacConfigResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.LoadAlmanacConfigResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.AlmanacConfig config = 1;
  if (this->_internal_has_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::config(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.LoadAlmanacConfigResponse)
  return target;
}

size_t LoadAlmanacConfigResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.LoadAlmanacConfigResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.AlmanacConfig config = 1;
  if (this->_internal_has_config()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *config_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LoadAlmanacConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LoadAlmanacConfigResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LoadAlmanacConfigResponse::GetClassData() const { return &_class_data_; }

void LoadAlmanacConfigResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LoadAlmanacConfigResponse *>(to)->MergeFrom(
      static_cast<const LoadAlmanacConfigResponse &>(from));
}


void LoadAlmanacConfigResponse::MergeFrom(const LoadAlmanacConfigResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.LoadAlmanacConfigResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_config()) {
    _internal_mutable_config()->::carbon::aimbot::almanac::AlmanacConfig::MergeFrom(from._internal_config());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LoadAlmanacConfigResponse::CopyFrom(const LoadAlmanacConfigResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.LoadAlmanacConfigResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadAlmanacConfigResponse::IsInitialized() const {
  return true;
}

void LoadAlmanacConfigResponse::InternalSwap(LoadAlmanacConfigResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(config_, other->config_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LoadAlmanacConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[4]);
}

// ===================================================================

class SaveAlmanacConfigRequest::_Internal {
 public:
  static const ::carbon::aimbot::almanac::AlmanacConfig& config(const SaveAlmanacConfigRequest* msg);
};

const ::carbon::aimbot::almanac::AlmanacConfig&
SaveAlmanacConfigRequest::_Internal::config(const SaveAlmanacConfigRequest* msg) {
  return *msg->config_;
}
void SaveAlmanacConfigRequest::clear_config() {
  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
}
SaveAlmanacConfigRequest::SaveAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.SaveAlmanacConfigRequest)
}
SaveAlmanacConfigRequest::SaveAlmanacConfigRequest(const SaveAlmanacConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_config()) {
    config_ = new ::carbon::aimbot::almanac::AlmanacConfig(*from.config_);
  } else {
    config_ = nullptr;
  }
  set_active_ = from.set_active_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.SaveAlmanacConfigRequest)
}

inline void SaveAlmanacConfigRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&config_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&set_active_) -
    reinterpret_cast<char*>(&config_)) + sizeof(set_active_));
}

SaveAlmanacConfigRequest::~SaveAlmanacConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.SaveAlmanacConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SaveAlmanacConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete config_;
}

void SaveAlmanacConfigRequest::ArenaDtor(void* object) {
  SaveAlmanacConfigRequest* _this = reinterpret_cast< SaveAlmanacConfigRequest* >(object);
  (void)_this;
}
void SaveAlmanacConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SaveAlmanacConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SaveAlmanacConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.SaveAlmanacConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
  set_active_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SaveAlmanacConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.AlmanacConfig config = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool set_active = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          set_active_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SaveAlmanacConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.SaveAlmanacConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.AlmanacConfig config = 1;
  if (this->_internal_has_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::config(this), target, stream);
  }

  // bool set_active = 2;
  if (this->_internal_set_active() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_set_active(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.SaveAlmanacConfigRequest)
  return target;
}

size_t SaveAlmanacConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.SaveAlmanacConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.AlmanacConfig config = 1;
  if (this->_internal_has_config()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *config_);
  }

  // bool set_active = 2;
  if (this->_internal_set_active() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SaveAlmanacConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SaveAlmanacConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SaveAlmanacConfigRequest::GetClassData() const { return &_class_data_; }

void SaveAlmanacConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SaveAlmanacConfigRequest *>(to)->MergeFrom(
      static_cast<const SaveAlmanacConfigRequest &>(from));
}


void SaveAlmanacConfigRequest::MergeFrom(const SaveAlmanacConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.SaveAlmanacConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_config()) {
    _internal_mutable_config()->::carbon::aimbot::almanac::AlmanacConfig::MergeFrom(from._internal_config());
  }
  if (from._internal_set_active() != 0) {
    _internal_set_set_active(from._internal_set_active());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SaveAlmanacConfigRequest::CopyFrom(const SaveAlmanacConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.SaveAlmanacConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SaveAlmanacConfigRequest::IsInitialized() const {
  return true;
}

void SaveAlmanacConfigRequest::InternalSwap(SaveAlmanacConfigRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SaveAlmanacConfigRequest, set_active_)
      + sizeof(SaveAlmanacConfigRequest::set_active_)
      - PROTOBUF_FIELD_OFFSET(SaveAlmanacConfigRequest, config_)>(
          reinterpret_cast<char*>(&config_),
          reinterpret_cast<char*>(&other->config_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SaveAlmanacConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[5]);
}

// ===================================================================

class SaveAlmanacConfigResponse::_Internal {
 public:
};

SaveAlmanacConfigResponse::SaveAlmanacConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.SaveAlmanacConfigResponse)
}
SaveAlmanacConfigResponse::SaveAlmanacConfigResponse(const SaveAlmanacConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.SaveAlmanacConfigResponse)
}

inline void SaveAlmanacConfigResponse::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SaveAlmanacConfigResponse::~SaveAlmanacConfigResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.SaveAlmanacConfigResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SaveAlmanacConfigResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SaveAlmanacConfigResponse::ArenaDtor(void* object) {
  SaveAlmanacConfigResponse* _this = reinterpret_cast< SaveAlmanacConfigResponse* >(object);
  (void)_this;
}
void SaveAlmanacConfigResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SaveAlmanacConfigResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SaveAlmanacConfigResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.SaveAlmanacConfigResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SaveAlmanacConfigResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.SaveAlmanacConfigResponse.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SaveAlmanacConfigResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.SaveAlmanacConfigResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.SaveAlmanacConfigResponse.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.SaveAlmanacConfigResponse)
  return target;
}

size_t SaveAlmanacConfigResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.SaveAlmanacConfigResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SaveAlmanacConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SaveAlmanacConfigResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SaveAlmanacConfigResponse::GetClassData() const { return &_class_data_; }

void SaveAlmanacConfigResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SaveAlmanacConfigResponse *>(to)->MergeFrom(
      static_cast<const SaveAlmanacConfigResponse &>(from));
}


void SaveAlmanacConfigResponse::MergeFrom(const SaveAlmanacConfigResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.SaveAlmanacConfigResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SaveAlmanacConfigResponse::CopyFrom(const SaveAlmanacConfigResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.SaveAlmanacConfigResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SaveAlmanacConfigResponse::IsInitialized() const {
  return true;
}

void SaveAlmanacConfigResponse::InternalSwap(SaveAlmanacConfigResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SaveAlmanacConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[6]);
}

// ===================================================================

class SetActiveAlmanacConfigRequest::_Internal {
 public:
};

SetActiveAlmanacConfigRequest::SetActiveAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.SetActiveAlmanacConfigRequest)
}
SetActiveAlmanacConfigRequest::SetActiveAlmanacConfigRequest(const SetActiveAlmanacConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.SetActiveAlmanacConfigRequest)
}

inline void SetActiveAlmanacConfigRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SetActiveAlmanacConfigRequest::~SetActiveAlmanacConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.SetActiveAlmanacConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetActiveAlmanacConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetActiveAlmanacConfigRequest::ArenaDtor(void* object) {
  SetActiveAlmanacConfigRequest* _this = reinterpret_cast< SetActiveAlmanacConfigRequest* >(object);
  (void)_this;
}
void SetActiveAlmanacConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetActiveAlmanacConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetActiveAlmanacConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.SetActiveAlmanacConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetActiveAlmanacConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.SetActiveAlmanacConfigRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetActiveAlmanacConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.SetActiveAlmanacConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.SetActiveAlmanacConfigRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.SetActiveAlmanacConfigRequest)
  return target;
}

size_t SetActiveAlmanacConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.SetActiveAlmanacConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetActiveAlmanacConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetActiveAlmanacConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetActiveAlmanacConfigRequest::GetClassData() const { return &_class_data_; }

void SetActiveAlmanacConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetActiveAlmanacConfigRequest *>(to)->MergeFrom(
      static_cast<const SetActiveAlmanacConfigRequest &>(from));
}


void SetActiveAlmanacConfigRequest::MergeFrom(const SetActiveAlmanacConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.SetActiveAlmanacConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetActiveAlmanacConfigRequest::CopyFrom(const SetActiveAlmanacConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.SetActiveAlmanacConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetActiveAlmanacConfigRequest::IsInitialized() const {
  return true;
}

void SetActiveAlmanacConfigRequest::InternalSwap(SetActiveAlmanacConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SetActiveAlmanacConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[7]);
}

// ===================================================================

class DeleteAlmanacConfigRequest::_Internal {
 public:
};

DeleteAlmanacConfigRequest::DeleteAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.DeleteAlmanacConfigRequest)
}
DeleteAlmanacConfigRequest::DeleteAlmanacConfigRequest(const DeleteAlmanacConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  new_active_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    new_active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_new_active_id().empty()) {
    new_active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_new_active_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.DeleteAlmanacConfigRequest)
}

inline void DeleteAlmanacConfigRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
new_active_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  new_active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DeleteAlmanacConfigRequest::~DeleteAlmanacConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.DeleteAlmanacConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeleteAlmanacConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  new_active_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DeleteAlmanacConfigRequest::ArenaDtor(void* object) {
  DeleteAlmanacConfigRequest* _this = reinterpret_cast< DeleteAlmanacConfigRequest* >(object);
  (void)_this;
}
void DeleteAlmanacConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeleteAlmanacConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeleteAlmanacConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.DeleteAlmanacConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  new_active_id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeleteAlmanacConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.DeleteAlmanacConfigRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string new_active_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_new_active_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.DeleteAlmanacConfigRequest.new_active_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeleteAlmanacConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.DeleteAlmanacConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.DeleteAlmanacConfigRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string new_active_id = 2;
  if (!this->_internal_new_active_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_new_active_id().data(), static_cast<int>(this->_internal_new_active_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.DeleteAlmanacConfigRequest.new_active_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_new_active_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.DeleteAlmanacConfigRequest)
  return target;
}

size_t DeleteAlmanacConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.DeleteAlmanacConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string new_active_id = 2;
  if (!this->_internal_new_active_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_new_active_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeleteAlmanacConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeleteAlmanacConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeleteAlmanacConfigRequest::GetClassData() const { return &_class_data_; }

void DeleteAlmanacConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeleteAlmanacConfigRequest *>(to)->MergeFrom(
      static_cast<const DeleteAlmanacConfigRequest &>(from));
}


void DeleteAlmanacConfigRequest::MergeFrom(const DeleteAlmanacConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.DeleteAlmanacConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_new_active_id().empty()) {
    _internal_set_new_active_id(from._internal_new_active_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeleteAlmanacConfigRequest::CopyFrom(const DeleteAlmanacConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.DeleteAlmanacConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeleteAlmanacConfigRequest::IsInitialized() const {
  return true;
}

void DeleteAlmanacConfigRequest::InternalSwap(DeleteAlmanacConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &new_active_id_, lhs_arena,
      &other->new_active_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DeleteAlmanacConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[8]);
}

// ===================================================================

GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse() {}
GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse::MergeFrom(const GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[9]);
}

// ===================================================================

class GetNextAlmanacConfigResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextAlmanacConfigResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextAlmanacConfigResponse::_Internal::ts(const GetNextAlmanacConfigResponse* msg) {
  return *msg->ts_;
}
void GetNextAlmanacConfigResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextAlmanacConfigResponse::GetNextAlmanacConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  available_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.GetNextAlmanacConfigResponse)
}
GetNextAlmanacConfigResponse::GetNextAlmanacConfigResponse(const GetNextAlmanacConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  available_.MergeFrom(from.available_);
  active_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active().empty()) {
    active_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.GetNextAlmanacConfigResponse)
}

inline void GetNextAlmanacConfigResponse::SharedCtor() {
active_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextAlmanacConfigResponse::~GetNextAlmanacConfigResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.GetNextAlmanacConfigResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextAlmanacConfigResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  active_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextAlmanacConfigResponse::ArenaDtor(void* object) {
  GetNextAlmanacConfigResponse* _this = reinterpret_cast< GetNextAlmanacConfigResponse* >(object);
  (void)_this;
  _this->available_. ~MapField();
}
inline void GetNextAlmanacConfigResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &GetNextAlmanacConfigResponse::ArenaDtor);
  }
}
void GetNextAlmanacConfigResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextAlmanacConfigResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.GetNextAlmanacConfigResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  available_.Clear();
  active_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextAlmanacConfigResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_active();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.GetNextAlmanacConfigResponse.active"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, string> available = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&available_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextAlmanacConfigResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.GetNextAlmanacConfigResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // string active = 2;
  if (!this->_internal_active().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active().data(), static_cast<int>(this->_internal_active().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.GetNextAlmanacConfigResponse.active");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_active(), target);
  }

  // map<string, string> available = 3;
  if (!this->_internal_available().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_available().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_available().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_available().begin();
          it != this->_internal_available().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse::Funcs::InternalSerialize(3, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_available().begin();
          it != this->_internal_available().end(); ++it) {
        target = GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse::Funcs::InternalSerialize(3, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.GetNextAlmanacConfigResponse)
  return target;
}

size_t GetNextAlmanacConfigResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.GetNextAlmanacConfigResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> available = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_available_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_available().begin();
      it != this->_internal_available().end(); ++it) {
    total_size += GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // string active = 2;
  if (!this->_internal_active().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextAlmanacConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextAlmanacConfigResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextAlmanacConfigResponse::GetClassData() const { return &_class_data_; }

void GetNextAlmanacConfigResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextAlmanacConfigResponse *>(to)->MergeFrom(
      static_cast<const GetNextAlmanacConfigResponse &>(from));
}


void GetNextAlmanacConfigResponse::MergeFrom(const GetNextAlmanacConfigResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.GetNextAlmanacConfigResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  available_.MergeFrom(from.available_);
  if (!from._internal_active().empty()) {
    _internal_set_active(from._internal_active());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextAlmanacConfigResponse::CopyFrom(const GetNextAlmanacConfigResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.GetNextAlmanacConfigResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextAlmanacConfigResponse::IsInitialized() const {
  return true;
}

void GetNextAlmanacConfigResponse::InternalSwap(GetNextAlmanacConfigResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  available_.InternalSwap(&other->available_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_, lhs_arena,
      &other->active_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextAlmanacConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[10]);
}

// ===================================================================

class LoadDiscriminatorConfigRequest::_Internal {
 public:
};

LoadDiscriminatorConfigRequest::LoadDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.LoadDiscriminatorConfigRequest)
}
LoadDiscriminatorConfigRequest::LoadDiscriminatorConfigRequest(const LoadDiscriminatorConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.LoadDiscriminatorConfigRequest)
}

inline void LoadDiscriminatorConfigRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

LoadDiscriminatorConfigRequest::~LoadDiscriminatorConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.LoadDiscriminatorConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LoadDiscriminatorConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void LoadDiscriminatorConfigRequest::ArenaDtor(void* object) {
  LoadDiscriminatorConfigRequest* _this = reinterpret_cast< LoadDiscriminatorConfigRequest* >(object);
  (void)_this;
}
void LoadDiscriminatorConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LoadDiscriminatorConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LoadDiscriminatorConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.LoadDiscriminatorConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LoadDiscriminatorConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.LoadDiscriminatorConfigRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LoadDiscriminatorConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.LoadDiscriminatorConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.LoadDiscriminatorConfigRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.LoadDiscriminatorConfigRequest)
  return target;
}

size_t LoadDiscriminatorConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.LoadDiscriminatorConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LoadDiscriminatorConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LoadDiscriminatorConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LoadDiscriminatorConfigRequest::GetClassData() const { return &_class_data_; }

void LoadDiscriminatorConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LoadDiscriminatorConfigRequest *>(to)->MergeFrom(
      static_cast<const LoadDiscriminatorConfigRequest &>(from));
}


void LoadDiscriminatorConfigRequest::MergeFrom(const LoadDiscriminatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.LoadDiscriminatorConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LoadDiscriminatorConfigRequest::CopyFrom(const LoadDiscriminatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.LoadDiscriminatorConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadDiscriminatorConfigRequest::IsInitialized() const {
  return true;
}

void LoadDiscriminatorConfigRequest::InternalSwap(LoadDiscriminatorConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata LoadDiscriminatorConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[11]);
}

// ===================================================================

class LoadDiscriminatorConfigResponse::_Internal {
 public:
  static const ::carbon::aimbot::almanac::DiscriminatorConfig& config(const LoadDiscriminatorConfigResponse* msg);
};

const ::carbon::aimbot::almanac::DiscriminatorConfig&
LoadDiscriminatorConfigResponse::_Internal::config(const LoadDiscriminatorConfigResponse* msg) {
  return *msg->config_;
}
void LoadDiscriminatorConfigResponse::clear_config() {
  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
}
LoadDiscriminatorConfigResponse::LoadDiscriminatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.LoadDiscriminatorConfigResponse)
}
LoadDiscriminatorConfigResponse::LoadDiscriminatorConfigResponse(const LoadDiscriminatorConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_config()) {
    config_ = new ::carbon::aimbot::almanac::DiscriminatorConfig(*from.config_);
  } else {
    config_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.LoadDiscriminatorConfigResponse)
}

inline void LoadDiscriminatorConfigResponse::SharedCtor() {
config_ = nullptr;
}

LoadDiscriminatorConfigResponse::~LoadDiscriminatorConfigResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.LoadDiscriminatorConfigResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LoadDiscriminatorConfigResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete config_;
}

void LoadDiscriminatorConfigResponse::ArenaDtor(void* object) {
  LoadDiscriminatorConfigResponse* _this = reinterpret_cast< LoadDiscriminatorConfigResponse* >(object);
  (void)_this;
}
void LoadDiscriminatorConfigResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LoadDiscriminatorConfigResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LoadDiscriminatorConfigResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.LoadDiscriminatorConfigResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LoadDiscriminatorConfigResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.DiscriminatorConfig config = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LoadDiscriminatorConfigResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.LoadDiscriminatorConfigResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.DiscriminatorConfig config = 1;
  if (this->_internal_has_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::config(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.LoadDiscriminatorConfigResponse)
  return target;
}

size_t LoadDiscriminatorConfigResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.LoadDiscriminatorConfigResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.DiscriminatorConfig config = 1;
  if (this->_internal_has_config()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *config_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LoadDiscriminatorConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LoadDiscriminatorConfigResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LoadDiscriminatorConfigResponse::GetClassData() const { return &_class_data_; }

void LoadDiscriminatorConfigResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LoadDiscriminatorConfigResponse *>(to)->MergeFrom(
      static_cast<const LoadDiscriminatorConfigResponse &>(from));
}


void LoadDiscriminatorConfigResponse::MergeFrom(const LoadDiscriminatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.LoadDiscriminatorConfigResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_config()) {
    _internal_mutable_config()->::carbon::aimbot::almanac::DiscriminatorConfig::MergeFrom(from._internal_config());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LoadDiscriminatorConfigResponse::CopyFrom(const LoadDiscriminatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.LoadDiscriminatorConfigResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadDiscriminatorConfigResponse::IsInitialized() const {
  return true;
}

void LoadDiscriminatorConfigResponse::InternalSwap(LoadDiscriminatorConfigResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(config_, other->config_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LoadDiscriminatorConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[12]);
}

// ===================================================================

class SaveDiscriminatorConfigRequest::_Internal {
 public:
  static const ::carbon::aimbot::almanac::DiscriminatorConfig& config(const SaveDiscriminatorConfigRequest* msg);
};

const ::carbon::aimbot::almanac::DiscriminatorConfig&
SaveDiscriminatorConfigRequest::_Internal::config(const SaveDiscriminatorConfigRequest* msg) {
  return *msg->config_;
}
void SaveDiscriminatorConfigRequest::clear_config() {
  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
}
SaveDiscriminatorConfigRequest::SaveDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.SaveDiscriminatorConfigRequest)
}
SaveDiscriminatorConfigRequest::SaveDiscriminatorConfigRequest(const SaveDiscriminatorConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_config()) {
    config_ = new ::carbon::aimbot::almanac::DiscriminatorConfig(*from.config_);
  } else {
    config_ = nullptr;
  }
  associate_with_active_crop_ = from.associate_with_active_crop_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.SaveDiscriminatorConfigRequest)
}

inline void SaveDiscriminatorConfigRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&config_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&associate_with_active_crop_) -
    reinterpret_cast<char*>(&config_)) + sizeof(associate_with_active_crop_));
}

SaveDiscriminatorConfigRequest::~SaveDiscriminatorConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.SaveDiscriminatorConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SaveDiscriminatorConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete config_;
}

void SaveDiscriminatorConfigRequest::ArenaDtor(void* object) {
  SaveDiscriminatorConfigRequest* _this = reinterpret_cast< SaveDiscriminatorConfigRequest* >(object);
  (void)_this;
}
void SaveDiscriminatorConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SaveDiscriminatorConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SaveDiscriminatorConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.SaveDiscriminatorConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
  associate_with_active_crop_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SaveDiscriminatorConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.DiscriminatorConfig config = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool associate_with_active_crop = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          associate_with_active_crop_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SaveDiscriminatorConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.SaveDiscriminatorConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.DiscriminatorConfig config = 1;
  if (this->_internal_has_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::config(this), target, stream);
  }

  // bool associate_with_active_crop = 2;
  if (this->_internal_associate_with_active_crop() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_associate_with_active_crop(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.SaveDiscriminatorConfigRequest)
  return target;
}

size_t SaveDiscriminatorConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.SaveDiscriminatorConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.DiscriminatorConfig config = 1;
  if (this->_internal_has_config()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *config_);
  }

  // bool associate_with_active_crop = 2;
  if (this->_internal_associate_with_active_crop() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SaveDiscriminatorConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SaveDiscriminatorConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SaveDiscriminatorConfigRequest::GetClassData() const { return &_class_data_; }

void SaveDiscriminatorConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SaveDiscriminatorConfigRequest *>(to)->MergeFrom(
      static_cast<const SaveDiscriminatorConfigRequest &>(from));
}


void SaveDiscriminatorConfigRequest::MergeFrom(const SaveDiscriminatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.SaveDiscriminatorConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_config()) {
    _internal_mutable_config()->::carbon::aimbot::almanac::DiscriminatorConfig::MergeFrom(from._internal_config());
  }
  if (from._internal_associate_with_active_crop() != 0) {
    _internal_set_associate_with_active_crop(from._internal_associate_with_active_crop());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SaveDiscriminatorConfigRequest::CopyFrom(const SaveDiscriminatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.SaveDiscriminatorConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SaveDiscriminatorConfigRequest::IsInitialized() const {
  return true;
}

void SaveDiscriminatorConfigRequest::InternalSwap(SaveDiscriminatorConfigRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SaveDiscriminatorConfigRequest, associate_with_active_crop_)
      + sizeof(SaveDiscriminatorConfigRequest::associate_with_active_crop_)
      - PROTOBUF_FIELD_OFFSET(SaveDiscriminatorConfigRequest, config_)>(
          reinterpret_cast<char*>(&config_),
          reinterpret_cast<char*>(&other->config_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SaveDiscriminatorConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[13]);
}

// ===================================================================

class SaveDiscriminatorConfigResponse::_Internal {
 public:
};

SaveDiscriminatorConfigResponse::SaveDiscriminatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.SaveDiscriminatorConfigResponse)
}
SaveDiscriminatorConfigResponse::SaveDiscriminatorConfigResponse(const SaveDiscriminatorConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.SaveDiscriminatorConfigResponse)
}

inline void SaveDiscriminatorConfigResponse::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SaveDiscriminatorConfigResponse::~SaveDiscriminatorConfigResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.SaveDiscriminatorConfigResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SaveDiscriminatorConfigResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SaveDiscriminatorConfigResponse::ArenaDtor(void* object) {
  SaveDiscriminatorConfigResponse* _this = reinterpret_cast< SaveDiscriminatorConfigResponse* >(object);
  (void)_this;
}
void SaveDiscriminatorConfigResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SaveDiscriminatorConfigResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SaveDiscriminatorConfigResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.SaveDiscriminatorConfigResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SaveDiscriminatorConfigResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.SaveDiscriminatorConfigResponse.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SaveDiscriminatorConfigResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.SaveDiscriminatorConfigResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.SaveDiscriminatorConfigResponse.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.SaveDiscriminatorConfigResponse)
  return target;
}

size_t SaveDiscriminatorConfigResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.SaveDiscriminatorConfigResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SaveDiscriminatorConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SaveDiscriminatorConfigResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SaveDiscriminatorConfigResponse::GetClassData() const { return &_class_data_; }

void SaveDiscriminatorConfigResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SaveDiscriminatorConfigResponse *>(to)->MergeFrom(
      static_cast<const SaveDiscriminatorConfigResponse &>(from));
}


void SaveDiscriminatorConfigResponse::MergeFrom(const SaveDiscriminatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.SaveDiscriminatorConfigResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SaveDiscriminatorConfigResponse::CopyFrom(const SaveDiscriminatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.SaveDiscriminatorConfigResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SaveDiscriminatorConfigResponse::IsInitialized() const {
  return true;
}

void SaveDiscriminatorConfigResponse::InternalSwap(SaveDiscriminatorConfigResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SaveDiscriminatorConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[14]);
}

// ===================================================================

class SetActiveDiscriminatorConfigRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<SetActiveDiscriminatorConfigRequest>()._has_bits_);
  static void set_has_crop_id(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

SetActiveDiscriminatorConfigRequest::SetActiveDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest)
}
SetActiveDiscriminatorConfigRequest::SetActiveDiscriminatorConfigRequest(const SetActiveDiscriminatorConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_crop_id()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest)
}

inline void SetActiveDiscriminatorConfigRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SetActiveDiscriminatorConfigRequest::~SetActiveDiscriminatorConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetActiveDiscriminatorConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetActiveDiscriminatorConfigRequest::ArenaDtor(void* object) {
  SetActiveDiscriminatorConfigRequest* _this = reinterpret_cast< SetActiveDiscriminatorConfigRequest* >(object);
  (void)_this;
}
void SetActiveDiscriminatorConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetActiveDiscriminatorConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetActiveDiscriminatorConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    crop_id_.ClearNonDefaultToEmpty();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetActiveDiscriminatorConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional string crop_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetActiveDiscriminatorConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // optional string crop_id = 2;
  if (_internal_has_crop_id()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.crop_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_crop_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest)
  return target;
}

size_t SetActiveDiscriminatorConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // optional string crop_id = 2;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetActiveDiscriminatorConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetActiveDiscriminatorConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetActiveDiscriminatorConfigRequest::GetClassData() const { return &_class_data_; }

void SetActiveDiscriminatorConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetActiveDiscriminatorConfigRequest *>(to)->MergeFrom(
      static_cast<const SetActiveDiscriminatorConfigRequest &>(from));
}


void SetActiveDiscriminatorConfigRequest::MergeFrom(const SetActiveDiscriminatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_has_crop_id()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetActiveDiscriminatorConfigRequest::CopyFrom(const SetActiveDiscriminatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetActiveDiscriminatorConfigRequest::IsInitialized() const {
  return true;
}

void SetActiveDiscriminatorConfigRequest::InternalSwap(SetActiveDiscriminatorConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SetActiveDiscriminatorConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[15]);
}

// ===================================================================

class DeleteDiscriminatorConfigRequest::_Internal {
 public:
};

DeleteDiscriminatorConfigRequest::DeleteDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest)
}
DeleteDiscriminatorConfigRequest::DeleteDiscriminatorConfigRequest(const DeleteDiscriminatorConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest)
}

inline void DeleteDiscriminatorConfigRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DeleteDiscriminatorConfigRequest::~DeleteDiscriminatorConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeleteDiscriminatorConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DeleteDiscriminatorConfigRequest::ArenaDtor(void* object) {
  DeleteDiscriminatorConfigRequest* _this = reinterpret_cast< DeleteDiscriminatorConfigRequest* >(object);
  (void)_this;
}
void DeleteDiscriminatorConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeleteDiscriminatorConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeleteDiscriminatorConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeleteDiscriminatorConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.DeleteDiscriminatorConfigRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeleteDiscriminatorConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.DeleteDiscriminatorConfigRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest)
  return target;
}

size_t DeleteDiscriminatorConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeleteDiscriminatorConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeleteDiscriminatorConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeleteDiscriminatorConfigRequest::GetClassData() const { return &_class_data_; }

void DeleteDiscriminatorConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeleteDiscriminatorConfigRequest *>(to)->MergeFrom(
      static_cast<const DeleteDiscriminatorConfigRequest &>(from));
}


void DeleteDiscriminatorConfigRequest::MergeFrom(const DeleteDiscriminatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeleteDiscriminatorConfigRequest::CopyFrom(const DeleteDiscriminatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeleteDiscriminatorConfigRequest::IsInitialized() const {
  return true;
}

void DeleteDiscriminatorConfigRequest::InternalSwap(DeleteDiscriminatorConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DeleteDiscriminatorConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[16]);
}

// ===================================================================

GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse() {}
GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse::MergeFrom(const GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[17]);
}

// ===================================================================

class GetNextDiscriminatorConfigResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextDiscriminatorConfigResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextDiscriminatorConfigResponse::_Internal::ts(const GetNextDiscriminatorConfigResponse* msg) {
  return *msg->ts_;
}
void GetNextDiscriminatorConfigResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextDiscriminatorConfigResponse::GetNextDiscriminatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  available_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse)
}
GetNextDiscriminatorConfigResponse::GetNextDiscriminatorConfigResponse(const GetNextDiscriminatorConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  available_.MergeFrom(from.available_);
  active_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active().empty()) {
    active_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse)
}

inline void GetNextDiscriminatorConfigResponse::SharedCtor() {
active_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextDiscriminatorConfigResponse::~GetNextDiscriminatorConfigResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextDiscriminatorConfigResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  active_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextDiscriminatorConfigResponse::ArenaDtor(void* object) {
  GetNextDiscriminatorConfigResponse* _this = reinterpret_cast< GetNextDiscriminatorConfigResponse* >(object);
  (void)_this;
  _this->available_. ~MapField();
}
inline void GetNextDiscriminatorConfigResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &GetNextDiscriminatorConfigResponse::ArenaDtor);
  }
}
void GetNextDiscriminatorConfigResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextDiscriminatorConfigResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  available_.Clear();
  active_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextDiscriminatorConfigResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_active();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.active"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, string> available = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&available_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextDiscriminatorConfigResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // string active = 2;
  if (!this->_internal_active().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active().data(), static_cast<int>(this->_internal_active().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.active");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_active(), target);
  }

  // map<string, string> available = 3;
  if (!this->_internal_available().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_available().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_available().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_available().begin();
          it != this->_internal_available().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse::Funcs::InternalSerialize(3, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_available().begin();
          it != this->_internal_available().end(); ++it) {
        target = GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse::Funcs::InternalSerialize(3, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse)
  return target;
}

size_t GetNextDiscriminatorConfigResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> available = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_available_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_available().begin();
      it != this->_internal_available().end(); ++it) {
    total_size += GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // string active = 2;
  if (!this->_internal_active().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextDiscriminatorConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextDiscriminatorConfigResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextDiscriminatorConfigResponse::GetClassData() const { return &_class_data_; }

void GetNextDiscriminatorConfigResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextDiscriminatorConfigResponse *>(to)->MergeFrom(
      static_cast<const GetNextDiscriminatorConfigResponse &>(from));
}


void GetNextDiscriminatorConfigResponse::MergeFrom(const GetNextDiscriminatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  available_.MergeFrom(from.available_);
  if (!from._internal_active().empty()) {
    _internal_set_active(from._internal_active());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextDiscriminatorConfigResponse::CopyFrom(const GetNextDiscriminatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextDiscriminatorConfigResponse::IsInitialized() const {
  return true;
}

void GetNextDiscriminatorConfigResponse::InternalSwap(GetNextDiscriminatorConfigResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  available_.InternalSwap(&other->available_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_, lhs_arena,
      &other->active_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextDiscriminatorConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[18]);
}

// ===================================================================

class GetNextModelinatorConfigResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextModelinatorConfigResponse* msg);
  static const ::carbon::aimbot::almanac::ModelinatorConfig& config(const GetNextModelinatorConfigResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextModelinatorConfigResponse::_Internal::ts(const GetNextModelinatorConfigResponse* msg) {
  return *msg->ts_;
}
const ::carbon::aimbot::almanac::ModelinatorConfig&
GetNextModelinatorConfigResponse::_Internal::config(const GetNextModelinatorConfigResponse* msg) {
  return *msg->config_;
}
void GetNextModelinatorConfigResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
void GetNextModelinatorConfigResponse::clear_config() {
  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
}
GetNextModelinatorConfigResponse::GetNextModelinatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.GetNextModelinatorConfigResponse)
}
GetNextModelinatorConfigResponse::GetNextModelinatorConfigResponse(const GetNextModelinatorConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_config()) {
    config_ = new ::carbon::aimbot::almanac::ModelinatorConfig(*from.config_);
  } else {
    config_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.GetNextModelinatorConfigResponse)
}

inline void GetNextModelinatorConfigResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&config_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(config_));
}

GetNextModelinatorConfigResponse::~GetNextModelinatorConfigResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.GetNextModelinatorConfigResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextModelinatorConfigResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete config_;
}

void GetNextModelinatorConfigResponse::ArenaDtor(void* object) {
  GetNextModelinatorConfigResponse* _this = reinterpret_cast< GetNextModelinatorConfigResponse* >(object);
  (void)_this;
}
void GetNextModelinatorConfigResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextModelinatorConfigResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextModelinatorConfigResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.GetNextModelinatorConfigResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextModelinatorConfigResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.ModelinatorConfig config = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextModelinatorConfigResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.GetNextModelinatorConfigResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // .carbon.aimbot.almanac.ModelinatorConfig config = 2;
  if (this->_internal_has_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::config(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.GetNextModelinatorConfigResponse)
  return target;
}

size_t GetNextModelinatorConfigResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.GetNextModelinatorConfigResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.aimbot.almanac.ModelinatorConfig config = 2;
  if (this->_internal_has_config()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *config_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextModelinatorConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextModelinatorConfigResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextModelinatorConfigResponse::GetClassData() const { return &_class_data_; }

void GetNextModelinatorConfigResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextModelinatorConfigResponse *>(to)->MergeFrom(
      static_cast<const GetNextModelinatorConfigResponse &>(from));
}


void GetNextModelinatorConfigResponse::MergeFrom(const GetNextModelinatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.GetNextModelinatorConfigResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_config()) {
    _internal_mutable_config()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_config());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextModelinatorConfigResponse::CopyFrom(const GetNextModelinatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.GetNextModelinatorConfigResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextModelinatorConfigResponse::IsInitialized() const {
  return true;
}

void GetNextModelinatorConfigResponse::InternalSwap(GetNextModelinatorConfigResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextModelinatorConfigResponse, config_)
      + sizeof(GetNextModelinatorConfigResponse::config_)
      - PROTOBUF_FIELD_OFFSET(GetNextModelinatorConfigResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextModelinatorConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[19]);
}

// ===================================================================

class SaveModelinatorConfigRequest::_Internal {
 public:
  static const ::carbon::aimbot::almanac::ModelinatorConfig& config(const SaveModelinatorConfigRequest* msg);
};

const ::carbon::aimbot::almanac::ModelinatorConfig&
SaveModelinatorConfigRequest::_Internal::config(const SaveModelinatorConfigRequest* msg) {
  return *msg->config_;
}
void SaveModelinatorConfigRequest::clear_config() {
  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
}
SaveModelinatorConfigRequest::SaveModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.SaveModelinatorConfigRequest)
}
SaveModelinatorConfigRequest::SaveModelinatorConfigRequest(const SaveModelinatorConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_config()) {
    config_ = new ::carbon::aimbot::almanac::ModelinatorConfig(*from.config_);
  } else {
    config_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.SaveModelinatorConfigRequest)
}

inline void SaveModelinatorConfigRequest::SharedCtor() {
config_ = nullptr;
}

SaveModelinatorConfigRequest::~SaveModelinatorConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.SaveModelinatorConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SaveModelinatorConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete config_;
}

void SaveModelinatorConfigRequest::ArenaDtor(void* object) {
  SaveModelinatorConfigRequest* _this = reinterpret_cast< SaveModelinatorConfigRequest* >(object);
  (void)_this;
}
void SaveModelinatorConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SaveModelinatorConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SaveModelinatorConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.SaveModelinatorConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SaveModelinatorConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.ModelinatorConfig config = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SaveModelinatorConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.SaveModelinatorConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.ModelinatorConfig config = 1;
  if (this->_internal_has_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::config(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.SaveModelinatorConfigRequest)
  return target;
}

size_t SaveModelinatorConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.SaveModelinatorConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.ModelinatorConfig config = 1;
  if (this->_internal_has_config()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *config_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SaveModelinatorConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SaveModelinatorConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SaveModelinatorConfigRequest::GetClassData() const { return &_class_data_; }

void SaveModelinatorConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SaveModelinatorConfigRequest *>(to)->MergeFrom(
      static_cast<const SaveModelinatorConfigRequest &>(from));
}


void SaveModelinatorConfigRequest::MergeFrom(const SaveModelinatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.SaveModelinatorConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_config()) {
    _internal_mutable_config()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_config());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SaveModelinatorConfigRequest::CopyFrom(const SaveModelinatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.SaveModelinatorConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SaveModelinatorConfigRequest::IsInitialized() const {
  return true;
}

void SaveModelinatorConfigRequest::InternalSwap(SaveModelinatorConfigRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(config_, other->config_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SaveModelinatorConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[20]);
}

// ===================================================================

class FetchModelinatorConfigRequest::_Internal {
 public:
};

FetchModelinatorConfigRequest::FetchModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.FetchModelinatorConfigRequest)
}
FetchModelinatorConfigRequest::FetchModelinatorConfigRequest(const FetchModelinatorConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_id().empty()) {
    model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_id(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.FetchModelinatorConfigRequest)
}

inline void FetchModelinatorConfigRequest::SharedCtor() {
model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

FetchModelinatorConfigRequest::~FetchModelinatorConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.FetchModelinatorConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FetchModelinatorConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void FetchModelinatorConfigRequest::ArenaDtor(void* object) {
  FetchModelinatorConfigRequest* _this = reinterpret_cast< FetchModelinatorConfigRequest* >(object);
  (void)_this;
}
void FetchModelinatorConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FetchModelinatorConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FetchModelinatorConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.FetchModelinatorConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  model_id_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FetchModelinatorConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string model_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.FetchModelinatorConfigRequest.model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.FetchModelinatorConfigRequest.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FetchModelinatorConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.FetchModelinatorConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_id().data(), static_cast<int>(this->_internal_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.FetchModelinatorConfigRequest.model_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_model_id(), target);
  }

  // string crop_id = 2;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.FetchModelinatorConfigRequest.crop_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_crop_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.FetchModelinatorConfigRequest)
  return target;
}

size_t FetchModelinatorConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.FetchModelinatorConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_id());
  }

  // string crop_id = 2;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FetchModelinatorConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FetchModelinatorConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FetchModelinatorConfigRequest::GetClassData() const { return &_class_data_; }

void FetchModelinatorConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FetchModelinatorConfigRequest *>(to)->MergeFrom(
      static_cast<const FetchModelinatorConfigRequest &>(from));
}


void FetchModelinatorConfigRequest::MergeFrom(const FetchModelinatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.FetchModelinatorConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_model_id().empty()) {
    _internal_set_model_id(from._internal_model_id());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FetchModelinatorConfigRequest::CopyFrom(const FetchModelinatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.FetchModelinatorConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FetchModelinatorConfigRequest::IsInitialized() const {
  return true;
}

void FetchModelinatorConfigRequest::InternalSwap(FetchModelinatorConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_id_, lhs_arena,
      &other->model_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata FetchModelinatorConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[21]);
}

// ===================================================================

class FetchModelinatorConfigResponse::_Internal {
 public:
  static const ::carbon::aimbot::almanac::ModelinatorConfig& config(const FetchModelinatorConfigResponse* msg);
};

const ::carbon::aimbot::almanac::ModelinatorConfig&
FetchModelinatorConfigResponse::_Internal::config(const FetchModelinatorConfigResponse* msg) {
  return *msg->config_;
}
void FetchModelinatorConfigResponse::clear_config() {
  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
}
FetchModelinatorConfigResponse::FetchModelinatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.FetchModelinatorConfigResponse)
}
FetchModelinatorConfigResponse::FetchModelinatorConfigResponse(const FetchModelinatorConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_config()) {
    config_ = new ::carbon::aimbot::almanac::ModelinatorConfig(*from.config_);
  } else {
    config_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.FetchModelinatorConfigResponse)
}

inline void FetchModelinatorConfigResponse::SharedCtor() {
config_ = nullptr;
}

FetchModelinatorConfigResponse::~FetchModelinatorConfigResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.FetchModelinatorConfigResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FetchModelinatorConfigResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete config_;
}

void FetchModelinatorConfigResponse::ArenaDtor(void* object) {
  FetchModelinatorConfigResponse* _this = reinterpret_cast< FetchModelinatorConfigResponse* >(object);
  (void)_this;
}
void FetchModelinatorConfigResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FetchModelinatorConfigResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FetchModelinatorConfigResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.FetchModelinatorConfigResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FetchModelinatorConfigResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.ModelinatorConfig config = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FetchModelinatorConfigResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.FetchModelinatorConfigResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.ModelinatorConfig config = 1;
  if (this->_internal_has_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::config(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.FetchModelinatorConfigResponse)
  return target;
}

size_t FetchModelinatorConfigResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.FetchModelinatorConfigResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.ModelinatorConfig config = 1;
  if (this->_internal_has_config()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *config_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FetchModelinatorConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FetchModelinatorConfigResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FetchModelinatorConfigResponse::GetClassData() const { return &_class_data_; }

void FetchModelinatorConfigResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FetchModelinatorConfigResponse *>(to)->MergeFrom(
      static_cast<const FetchModelinatorConfigResponse &>(from));
}


void FetchModelinatorConfigResponse::MergeFrom(const FetchModelinatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.FetchModelinatorConfigResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_config()) {
    _internal_mutable_config()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_config());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FetchModelinatorConfigResponse::CopyFrom(const FetchModelinatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.FetchModelinatorConfigResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FetchModelinatorConfigResponse::IsInitialized() const {
  return true;
}

void FetchModelinatorConfigResponse::InternalSwap(FetchModelinatorConfigResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(config_, other->config_);
}

::PROTOBUF_NAMESPACE_ID::Metadata FetchModelinatorConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[22]);
}

// ===================================================================

class ResetModelinatorConfigRequest::_Internal {
 public:
};

ResetModelinatorConfigRequest::ResetModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.ResetModelinatorConfigRequest)
}
ResetModelinatorConfigRequest::ResetModelinatorConfigRequest(const ResetModelinatorConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.ResetModelinatorConfigRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ResetModelinatorConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ResetModelinatorConfigRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata ResetModelinatorConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[23]);
}

// ===================================================================

class GetNextConfigDataRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextConfigDataRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextConfigDataRequest::_Internal::ts(const GetNextConfigDataRequest* msg) {
  return *msg->ts_;
}
void GetNextConfigDataRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextConfigDataRequest::GetNextConfigDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.GetNextConfigDataRequest)
}
GetNextConfigDataRequest::GetNextConfigDataRequest(const GetNextConfigDataRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  lang_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_lang().empty()) {
    lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_lang(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.GetNextConfigDataRequest)
}

inline void GetNextConfigDataRequest::SharedCtor() {
lang_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextConfigDataRequest::~GetNextConfigDataRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.GetNextConfigDataRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextConfigDataRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  lang_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextConfigDataRequest::ArenaDtor(void* object) {
  GetNextConfigDataRequest* _this = reinterpret_cast< GetNextConfigDataRequest* >(object);
  (void)_this;
}
void GetNextConfigDataRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextConfigDataRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextConfigDataRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.GetNextConfigDataRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  lang_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextConfigDataRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string lang = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_lang();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.almanac.GetNextConfigDataRequest.lang"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextConfigDataRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.GetNextConfigDataRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // string lang = 2;
  if (!this->_internal_lang().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_lang().data(), static_cast<int>(this->_internal_lang().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.almanac.GetNextConfigDataRequest.lang");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_lang(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.GetNextConfigDataRequest)
  return target;
}

size_t GetNextConfigDataRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.GetNextConfigDataRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string lang = 2;
  if (!this->_internal_lang().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_lang());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextConfigDataRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextConfigDataRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextConfigDataRequest::GetClassData() const { return &_class_data_; }

void GetNextConfigDataRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextConfigDataRequest *>(to)->MergeFrom(
      static_cast<const GetNextConfigDataRequest &>(from));
}


void GetNextConfigDataRequest::MergeFrom(const GetNextConfigDataRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.GetNextConfigDataRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_lang().empty()) {
    _internal_set_lang(from._internal_lang());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextConfigDataRequest::CopyFrom(const GetNextConfigDataRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.GetNextConfigDataRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextConfigDataRequest::IsInitialized() const {
  return true;
}

void GetNextConfigDataRequest::InternalSwap(GetNextConfigDataRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &lang_, lhs_arena,
      &other->lang_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextConfigDataRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[24]);
}

// ===================================================================

GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse() {}
GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse::MergeFrom(const GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[25]);
}

// ===================================================================

GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse() {}
GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::MergeFrom(const GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[26]);
}

// ===================================================================

class GetNextConfigDataResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextConfigDataResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextConfigDataResponse::_Internal::ts(const GetNextConfigDataResponse* msg) {
  return *msg->ts_;
}
void GetNextConfigDataResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextConfigDataResponse::GetNextConfigDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  crop_category_names_(arena),
  weed_category_names_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.almanac.GetNextConfigDataResponse)
}
GetNextConfigDataResponse::GetNextConfigDataResponse(const GetNextConfigDataResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  crop_category_names_.MergeFrom(from.crop_category_names_);
  weed_category_names_.MergeFrom(from.weed_category_names_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  num_size_categories_ = from.num_size_categories_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.almanac.GetNextConfigDataResponse)
}

inline void GetNextConfigDataResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&num_size_categories_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(num_size_categories_));
}

GetNextConfigDataResponse::~GetNextConfigDataResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.almanac.GetNextConfigDataResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextConfigDataResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextConfigDataResponse::ArenaDtor(void* object) {
  GetNextConfigDataResponse* _this = reinterpret_cast< GetNextConfigDataResponse* >(object);
  (void)_this;
  _this->crop_category_names_. ~MapField();
  _this->weed_category_names_. ~MapField();
}
inline void GetNextConfigDataResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &GetNextConfigDataResponse::ArenaDtor);
  }
}
void GetNextConfigDataResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextConfigDataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.almanac.GetNextConfigDataResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  crop_category_names_.Clear();
  weed_category_names_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  num_size_categories_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextConfigDataResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 num_size_categories = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          num_size_categories_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, string> crop_category_names = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&crop_category_names_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, string> weed_category_names = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&weed_category_names_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextConfigDataResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.almanac.GetNextConfigDataResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // uint32 num_size_categories = 2;
  if (this->_internal_num_size_categories() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_num_size_categories(), target);
  }

  // map<string, string> crop_category_names = 3;
  if (!this->_internal_crop_category_names().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_crop_category_names().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_crop_category_names().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_crop_category_names().begin();
          it != this->_internal_crop_category_names().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse::Funcs::InternalSerialize(3, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_crop_category_names().begin();
          it != this->_internal_crop_category_names().end(); ++it) {
        target = GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse::Funcs::InternalSerialize(3, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, string> weed_category_names = 4;
  if (!this->_internal_weed_category_names().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_weed_category_names().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_weed_category_names().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_weed_category_names().begin();
          it != this->_internal_weed_category_names().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::Funcs::InternalSerialize(4, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_weed_category_names().begin();
          it != this->_internal_weed_category_names().end(); ++it) {
        target = GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::Funcs::InternalSerialize(4, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.almanac.GetNextConfigDataResponse)
  return target;
}

size_t GetNextConfigDataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.almanac.GetNextConfigDataResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> crop_category_names = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_crop_category_names_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_crop_category_names().begin();
      it != this->_internal_crop_category_names().end(); ++it) {
    total_size += GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, string> weed_category_names = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_weed_category_names_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_weed_category_names().begin();
      it != this->_internal_weed_category_names().end(); ++it) {
    total_size += GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // uint32 num_size_categories = 2;
  if (this->_internal_num_size_categories() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_num_size_categories());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextConfigDataResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextConfigDataResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextConfigDataResponse::GetClassData() const { return &_class_data_; }

void GetNextConfigDataResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextConfigDataResponse *>(to)->MergeFrom(
      static_cast<const GetNextConfigDataResponse &>(from));
}


void GetNextConfigDataResponse::MergeFrom(const GetNextConfigDataResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.almanac.GetNextConfigDataResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  crop_category_names_.MergeFrom(from.crop_category_names_);
  weed_category_names_.MergeFrom(from.weed_category_names_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_num_size_categories() != 0) {
    _internal_set_num_size_categories(from._internal_num_size_categories());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextConfigDataResponse::CopyFrom(const GetNextConfigDataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.almanac.GetNextConfigDataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextConfigDataResponse::IsInitialized() const {
  return true;
}

void GetNextConfigDataResponse::InternalSwap(GetNextConfigDataResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  crop_category_names_.InternalSwap(&other->crop_category_names_);
  weed_category_names_.InternalSwap(&other->weed_category_names_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextConfigDataResponse, num_size_categories_)
      + sizeof(GetNextConfigDataResponse::num_size_categories_)
      - PROTOBUF_FIELD_OFFSET(GetNextConfigDataResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextConfigDataResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falmanac_2eproto_getter, &descriptor_table_frontend_2fproto_2falmanac_2eproto_once,
      file_level_metadata_frontend_2fproto_2falmanac_2eproto[27]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace almanac
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetConfigDataResponse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetConfigDataResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetConfigDataResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::LoadAlmanacConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::LoadAlmanacConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::LoadAlmanacConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::LoadAlmanacConfigResponse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::LoadAlmanacConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::LoadAlmanacConfigResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::SaveAlmanacConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::SaveAlmanacConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::SaveAlmanacConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::SaveAlmanacConfigResponse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::SaveAlmanacConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::SaveAlmanacConfigResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::DeleteAlmanacConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::DeleteAlmanacConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::SaveModelinatorConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::SaveModelinatorConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::SaveModelinatorConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::FetchModelinatorConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::FetchModelinatorConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::FetchModelinatorConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::FetchModelinatorConfigResponse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::FetchModelinatorConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::FetchModelinatorConfigResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::ResetModelinatorConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::ResetModelinatorConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::ResetModelinatorConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetNextConfigDataRequest* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetNextConfigDataRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetNextConfigDataRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::almanac::GetNextConfigDataResponse* Arena::CreateMaybeMessage< ::carbon::frontend::almanac::GetNextConfigDataResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::almanac::GetNextConfigDataResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.translation_pb2 import (
    AreaValue as frontend___proto___translation_pb2___AreaValue,
    DurationValue as frontend___proto___translation_pb2___DurationValue,
    IntegerValue as frontend___proto___translation_pb2___IntegerValue,
    PercentValue as frontend___proto___translation_pb2___PercentValue,
)

from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
    RepeatedComposite<PERSON>ieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

SafetyOverrideStateValue = typing___NewType('SafetyOverrideStateValue', builtin___int)
type___SafetyOverrideStateValue = SafetyOverrideStateValue
SafetyOverrideState: _SafetyOverrideState
class _SafetyOverrideState(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[SafetyOverrideStateValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    SafetyOverrideNone = typing___cast(SafetyOverrideStateValue, 0)
    SafetyOverrideVelocityStop = typing___cast(SafetyOverrideStateValue, 1)
SafetyOverrideNone = typing___cast(SafetyOverrideStateValue, 0)
SafetyOverrideVelocityStop = typing___cast(SafetyOverrideStateValue, 1)

ImplementStateValue = typing___NewType('ImplementStateValue', builtin___int)
type___ImplementStateValue = ImplementStateValue
ImplementState: _ImplementState
class _ImplementState(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ImplementStateValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    RAISED = typing___cast(ImplementStateValue, 0)
    LOWERED = typing___cast(ImplementStateValue, 1)
RAISED = typing___cast(ImplementStateValue, 0)
LOWERED = typing___cast(ImplementStateValue, 1)

class ExtraStatus(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    title: typing___Text = ...
    icon_name: typing___Text = ...
    icon_color: typing___Text = ...
    status_text: typing___Text = ...
    status_color: typing___Text = ...
    group_id: typing___Text = ...
    section_id: typing___Text = ...
    progress: builtin___float = ...
    width: builtin___int = ...
    bottom_text: typing___Text = ...

    def __init__(self,
        *,
        title : typing___Optional[typing___Text] = None,
        icon_name : typing___Optional[typing___Text] = None,
        icon_color : typing___Optional[typing___Text] = None,
        status_text : typing___Optional[typing___Text] = None,
        status_color : typing___Optional[typing___Text] = None,
        group_id : typing___Optional[typing___Text] = None,
        section_id : typing___Optional[typing___Text] = None,
        progress : typing___Optional[builtin___float] = None,
        width : typing___Optional[builtin___int] = None,
        bottom_text : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bottom_text",b"bottom_text",u"group_id",b"group_id",u"icon_color",b"icon_color",u"icon_name",b"icon_name",u"progress",b"progress",u"section_id",b"section_id",u"status_color",b"status_color",u"status_text",b"status_text",u"title",b"title",u"width",b"width"]) -> None: ...
type___ExtraStatus = ExtraStatus

class WeedTargeting(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___WeedTargeting = WeedTargeting

class ThinningTargeting(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...
    algorithm: builtin___int = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        algorithm : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"algorithm",b"algorithm",u"enabled",b"enabled"]) -> None: ...
type___ThinningTargeting = ThinningTargeting

class TargetingState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class EnabledRowsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...
        value: builtin___bool = ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[builtin___bool] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___EnabledRowsEntry = EnabledRowsEntry

    enabled: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...

    @property
    def weed_state(self) -> type___WeedTargeting: ...

    @property
    def thinning_state(self) -> type___ThinningTargeting: ...

    @property
    def enabled_rows(self) -> google___protobuf___internal___containers___ScalarMap[builtin___int, builtin___bool]: ...

    def __init__(self,
        *,
        weed_state : typing___Optional[type___WeedTargeting] = None,
        thinning_state : typing___Optional[type___ThinningTargeting] = None,
        enabled : typing___Optional[typing___Iterable[builtin___bool]] = None,
        enabled_rows : typing___Optional[typing___Mapping[builtin___int, builtin___bool]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"thinning_state",b"thinning_state",u"weed_state",b"weed_state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"enabled_rows",b"enabled_rows",u"thinning_state",b"thinning_state",u"weed_state",b"weed_state"]) -> None: ...
type___TargetingState = TargetingState

class ExtraConclusion(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    title: typing___Text = ...
    flip_thresholds: builtin___bool = ...
    good_threshold_percent: builtin___int = ...
    medium_threshold_percent: builtin___int = ...

    @property
    def percent(self) -> frontend___proto___translation_pb2___PercentValue: ...

    def __init__(self,
        *,
        title : typing___Optional[typing___Text] = None,
        flip_thresholds : typing___Optional[builtin___bool] = None,
        good_threshold_percent : typing___Optional[builtin___int] = None,
        medium_threshold_percent : typing___Optional[builtin___int] = None,
        percent : typing___Optional[frontend___proto___translation_pb2___PercentValue] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"percent",b"percent"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"flip_thresholds",b"flip_thresholds",u"good_threshold_percent",b"good_threshold_percent",u"medium_threshold_percent",b"medium_threshold_percent",u"percent",b"percent",u"title",b"title"]) -> None: ...
type___ExtraConclusion = ExtraConclusion

class RowStateMessage(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...
    target_state_mismatch: builtin___bool = ...
    ready: builtin___bool = ...
    safety_override_state: type___SafetyOverrideStateValue = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        target_state_mismatch : typing___Optional[builtin___bool] = None,
        ready : typing___Optional[builtin___bool] = None,
        safety_override_state : typing___Optional[type___SafetyOverrideStateValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"ready",b"ready",u"safety_override_state",b"safety_override_state",u"target_state_mismatch",b"target_state_mismatch"]) -> None: ...
type___RowStateMessage = RowStateMessage

class DashboardStateMessage(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class RowStatesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> type___RowStateMessage: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[type___RowStateMessage] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___RowStatesEntry = RowStatesEntry

    lasers_enabled: builtin___bool = ...
    row_enabled: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    target_state_mismatch: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    row_ready: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    row_exists: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    row_width_in: builtin___float = ...
    safety_override_state: google___protobuf___internal___containers___RepeatedScalarFieldContainer[type___SafetyOverrideStateValue] = ...
    implement_state: type___ImplementStateValue = ...
    efficiency_enabled: builtin___bool = ...
    error_rate_enabled: builtin___bool = ...
    weeding_enabled: builtin___bool = ...
    debug_mode: builtin___bool = ...
    cruise_enabled: builtin___bool = ...
    cruise_allow_enable: builtin___bool = ...
    cruise_disallowed_reason: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def extras(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ExtraStatus]: ...

    @property
    def selected_model(self) -> type___CropModel: ...

    @property
    def targeting_state(self) -> type___TargetingState: ...

    @property
    def efficiency_percent(self) -> frontend___proto___translation_pb2___PercentValue: ...

    @property
    def error_rate(self) -> frontend___proto___translation_pb2___PercentValue: ...

    @property
    def extra_conclusions(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ExtraConclusion]: ...

    @property
    def area_weeded_today(self) -> frontend___proto___translation_pb2___AreaValue: ...

    @property
    def area_weeded_total(self) -> frontend___proto___translation_pb2___AreaValue: ...

    @property
    def weeds_killed_today(self) -> frontend___proto___translation_pb2___IntegerValue: ...

    @property
    def weeds_killed_total(self) -> frontend___proto___translation_pb2___IntegerValue: ...

    @property
    def time_weeded_today(self) -> frontend___proto___translation_pb2___DurationValue: ...

    @property
    def time_weeded_total(self) -> frontend___proto___translation_pb2___DurationValue: ...

    @property
    def crops_killed_today(self) -> frontend___proto___translation_pb2___IntegerValue: ...

    @property
    def crops_killed_total(self) -> frontend___proto___translation_pb2___IntegerValue: ...

    @property
    def row_states(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, type___RowStateMessage]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        lasers_enabled : typing___Optional[builtin___bool] = None,
        row_enabled : typing___Optional[typing___Iterable[builtin___bool]] = None,
        extras : typing___Optional[typing___Iterable[type___ExtraStatus]] = None,
        selected_model : typing___Optional[type___CropModel] = None,
        targeting_state : typing___Optional[type___TargetingState] = None,
        target_state_mismatch : typing___Optional[typing___Iterable[builtin___bool]] = None,
        row_ready : typing___Optional[typing___Iterable[builtin___bool]] = None,
        row_exists : typing___Optional[typing___Iterable[builtin___bool]] = None,
        row_width_in : typing___Optional[builtin___float] = None,
        safety_override_state : typing___Optional[typing___Iterable[type___SafetyOverrideStateValue]] = None,
        implement_state : typing___Optional[type___ImplementStateValue] = None,
        efficiency_enabled : typing___Optional[builtin___bool] = None,
        efficiency_percent : typing___Optional[frontend___proto___translation_pb2___PercentValue] = None,
        error_rate_enabled : typing___Optional[builtin___bool] = None,
        error_rate : typing___Optional[frontend___proto___translation_pb2___PercentValue] = None,
        extra_conclusions : typing___Optional[typing___Iterable[type___ExtraConclusion]] = None,
        area_weeded_today : typing___Optional[frontend___proto___translation_pb2___AreaValue] = None,
        area_weeded_total : typing___Optional[frontend___proto___translation_pb2___AreaValue] = None,
        weeds_killed_today : typing___Optional[frontend___proto___translation_pb2___IntegerValue] = None,
        weeds_killed_total : typing___Optional[frontend___proto___translation_pb2___IntegerValue] = None,
        time_weeded_today : typing___Optional[frontend___proto___translation_pb2___DurationValue] = None,
        time_weeded_total : typing___Optional[frontend___proto___translation_pb2___DurationValue] = None,
        weeding_enabled : typing___Optional[builtin___bool] = None,
        debug_mode : typing___Optional[builtin___bool] = None,
        crops_killed_today : typing___Optional[frontend___proto___translation_pb2___IntegerValue] = None,
        crops_killed_total : typing___Optional[frontend___proto___translation_pb2___IntegerValue] = None,
        cruise_enabled : typing___Optional[builtin___bool] = None,
        row_states : typing___Optional[typing___Mapping[builtin___int, type___RowStateMessage]] = None,
        cruise_allow_enable : typing___Optional[builtin___bool] = None,
        cruise_disallowed_reason : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"area_weeded_today",b"area_weeded_today",u"area_weeded_total",b"area_weeded_total",u"crops_killed_today",b"crops_killed_today",u"crops_killed_total",b"crops_killed_total",u"efficiency_percent",b"efficiency_percent",u"error_rate",b"error_rate",u"selected_model",b"selected_model",u"targeting_state",b"targeting_state",u"time_weeded_today",b"time_weeded_today",u"time_weeded_total",b"time_weeded_total",u"ts",b"ts",u"weeds_killed_today",b"weeds_killed_today",u"weeds_killed_total",b"weeds_killed_total"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"area_weeded_today",b"area_weeded_today",u"area_weeded_total",b"area_weeded_total",u"crops_killed_today",b"crops_killed_today",u"crops_killed_total",b"crops_killed_total",u"cruise_allow_enable",b"cruise_allow_enable",u"cruise_disallowed_reason",b"cruise_disallowed_reason",u"cruise_enabled",b"cruise_enabled",u"debug_mode",b"debug_mode",u"efficiency_enabled",b"efficiency_enabled",u"efficiency_percent",b"efficiency_percent",u"error_rate",b"error_rate",u"error_rate_enabled",b"error_rate_enabled",u"extra_conclusions",b"extra_conclusions",u"extras",b"extras",u"implement_state",b"implement_state",u"lasers_enabled",b"lasers_enabled",u"row_enabled",b"row_enabled",u"row_exists",b"row_exists",u"row_ready",b"row_ready",u"row_states",b"row_states",u"row_width_in",b"row_width_in",u"safety_override_state",b"safety_override_state",u"selected_model",b"selected_model",u"target_state_mismatch",b"target_state_mismatch",u"targeting_state",b"targeting_state",u"time_weeded_today",b"time_weeded_today",u"time_weeded_total",b"time_weeded_total",u"ts",b"ts",u"weeding_enabled",b"weeding_enabled",u"weeds_killed_today",b"weeds_killed_today",u"weeds_killed_total",b"weeds_killed_total"]) -> None: ...
type___DashboardStateMessage = DashboardStateMessage

class CropModel(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    crop: typing___Text = ...
    has_model: builtin___bool = ...
    preferred: builtin___bool = ...
    crop_id: typing___Text = ...

    def __init__(self,
        *,
        crop : typing___Optional[typing___Text] = None,
        has_model : typing___Optional[builtin___bool] = None,
        preferred : typing___Optional[builtin___bool] = None,
        crop_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop",b"crop",u"crop_id",b"crop_id",u"has_model",b"has_model",u"preferred",b"preferred"]) -> None: ...
type___CropModel = CropModel

class CropModelOptions(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def models(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CropModel]: ...

    def __init__(self,
        *,
        models : typing___Optional[typing___Iterable[type___CropModel]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"models",b"models"]) -> None: ...
type___CropModelOptions = CropModelOptions

class RowId(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_number: builtin___int = ...

    def __init__(self,
        *,
        row_number : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"row_number",b"row_number"]) -> None: ...
type___RowId = RowId

class WeedingVelocity(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    current_velocity_mph: builtin___float = ...
    target_velocity_mph: builtin___float = ...
    tolerance_mph: builtin___float = ...
    primary_target_velocity_top_mph: builtin___float = ...
    primary_target_velocity_bottom_mph: builtin___float = ...
    secondary_target_velocity_top_mph: builtin___float = ...
    secondary_target_velocity_bottom_mph: builtin___float = ...
    cruise_control_velocity_mph: builtin___float = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        current_velocity_mph : typing___Optional[builtin___float] = None,
        target_velocity_mph : typing___Optional[builtin___float] = None,
        tolerance_mph : typing___Optional[builtin___float] = None,
        primary_target_velocity_top_mph : typing___Optional[builtin___float] = None,
        primary_target_velocity_bottom_mph : typing___Optional[builtin___float] = None,
        secondary_target_velocity_top_mph : typing___Optional[builtin___float] = None,
        secondary_target_velocity_bottom_mph : typing___Optional[builtin___float] = None,
        cruise_control_velocity_mph : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cruise_control_velocity_mph",b"cruise_control_velocity_mph",u"current_velocity_mph",b"current_velocity_mph",u"primary_target_velocity_bottom_mph",b"primary_target_velocity_bottom_mph",u"primary_target_velocity_top_mph",b"primary_target_velocity_top_mph",u"secondary_target_velocity_bottom_mph",b"secondary_target_velocity_bottom_mph",u"secondary_target_velocity_top_mph",b"secondary_target_velocity_top_mph",u"target_velocity_mph",b"target_velocity_mph",u"tolerance_mph",b"tolerance_mph",u"ts",b"ts"]) -> None: ...
type___WeedingVelocity = WeedingVelocity

class RowSpacing(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    width: builtin___float = ...

    def __init__(self,
        *,
        width : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"width",b"width"]) -> None: ...
type___RowSpacing = RowSpacing

class CruiseEnable(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___CruiseEnable = CruiseEnable

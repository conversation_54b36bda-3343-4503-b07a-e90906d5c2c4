// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/translation.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ftranslation_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ftranslation_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2ftranslation_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2ftranslation_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[12]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2ftranslation_2eproto;
namespace carbon {
namespace frontend {
namespace translation {
class AreaValue;
struct AreaValueDefaultTypeInternal;
extern AreaValueDefaultTypeInternal _AreaValue_default_instance_;
class DistanceValue;
struct DistanceValueDefaultTypeInternal;
extern DistanceValueDefaultTypeInternal _DistanceValue_default_instance_;
class DoubleValue;
struct DoubleValueDefaultTypeInternal;
extern DoubleValueDefaultTypeInternal _DoubleValue_default_instance_;
class DurationValue;
struct DurationValueDefaultTypeInternal;
extern DurationValueDefaultTypeInternal _DurationValue_default_instance_;
class FrequencyValue;
struct FrequencyValueDefaultTypeInternal;
extern FrequencyValueDefaultTypeInternal _FrequencyValue_default_instance_;
class IntegerValue;
struct IntegerValueDefaultTypeInternal;
extern IntegerValueDefaultTypeInternal _IntegerValue_default_instance_;
class PercentValue;
struct PercentValueDefaultTypeInternal;
extern PercentValueDefaultTypeInternal _PercentValue_default_instance_;
class SpeedValue;
struct SpeedValueDefaultTypeInternal;
extern SpeedValueDefaultTypeInternal _SpeedValue_default_instance_;
class StringValue;
struct StringValueDefaultTypeInternal;
extern StringValueDefaultTypeInternal _StringValue_default_instance_;
class TemperatureValue;
struct TemperatureValueDefaultTypeInternal;
extern TemperatureValueDefaultTypeInternal _TemperatureValue_default_instance_;
class TranslationParameter;
struct TranslationParameterDefaultTypeInternal;
extern TranslationParameterDefaultTypeInternal _TranslationParameter_default_instance_;
class VoltageValue;
struct VoltageValueDefaultTypeInternal;
extern VoltageValueDefaultTypeInternal _VoltageValue_default_instance_;
}  // namespace translation
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::translation::AreaValue* Arena::CreateMaybeMessage<::carbon::frontend::translation::AreaValue>(Arena*);
template<> ::carbon::frontend::translation::DistanceValue* Arena::CreateMaybeMessage<::carbon::frontend::translation::DistanceValue>(Arena*);
template<> ::carbon::frontend::translation::DoubleValue* Arena::CreateMaybeMessage<::carbon::frontend::translation::DoubleValue>(Arena*);
template<> ::carbon::frontend::translation::DurationValue* Arena::CreateMaybeMessage<::carbon::frontend::translation::DurationValue>(Arena*);
template<> ::carbon::frontend::translation::FrequencyValue* Arena::CreateMaybeMessage<::carbon::frontend::translation::FrequencyValue>(Arena*);
template<> ::carbon::frontend::translation::IntegerValue* Arena::CreateMaybeMessage<::carbon::frontend::translation::IntegerValue>(Arena*);
template<> ::carbon::frontend::translation::PercentValue* Arena::CreateMaybeMessage<::carbon::frontend::translation::PercentValue>(Arena*);
template<> ::carbon::frontend::translation::SpeedValue* Arena::CreateMaybeMessage<::carbon::frontend::translation::SpeedValue>(Arena*);
template<> ::carbon::frontend::translation::StringValue* Arena::CreateMaybeMessage<::carbon::frontend::translation::StringValue>(Arena*);
template<> ::carbon::frontend::translation::TemperatureValue* Arena::CreateMaybeMessage<::carbon::frontend::translation::TemperatureValue>(Arena*);
template<> ::carbon::frontend::translation::TranslationParameter* Arena::CreateMaybeMessage<::carbon::frontend::translation::TranslationParameter>(Arena*);
template<> ::carbon::frontend::translation::VoltageValue* Arena::CreateMaybeMessage<::carbon::frontend::translation::VoltageValue>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace translation {

// ===================================================================

class IntegerValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.IntegerValue) */ {
 public:
  inline IntegerValue() : IntegerValue(nullptr) {}
  ~IntegerValue() override;
  explicit constexpr IntegerValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IntegerValue(const IntegerValue& from);
  IntegerValue(IntegerValue&& from) noexcept
    : IntegerValue() {
    *this = ::std::move(from);
  }

  inline IntegerValue& operator=(const IntegerValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline IntegerValue& operator=(IntegerValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const IntegerValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const IntegerValue* internal_default_instance() {
    return reinterpret_cast<const IntegerValue*>(
               &_IntegerValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(IntegerValue& a, IntegerValue& b) {
    a.Swap(&b);
  }
  inline void Swap(IntegerValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IntegerValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  IntegerValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<IntegerValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const IntegerValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const IntegerValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(IntegerValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.IntegerValue";
  }
  protected:
  explicit IntegerValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // int64 value = 1;
  void clear_value();
  int64_t value() const;
  void set_value(int64_t value);
  private:
  int64_t _internal_value() const;
  void _internal_set_value(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.IntegerValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int64_t value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// -------------------------------------------------------------------

class DoubleValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.DoubleValue) */ {
 public:
  inline DoubleValue() : DoubleValue(nullptr) {}
  ~DoubleValue() override;
  explicit constexpr DoubleValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DoubleValue(const DoubleValue& from);
  DoubleValue(DoubleValue&& from) noexcept
    : DoubleValue() {
    *this = ::std::move(from);
  }

  inline DoubleValue& operator=(const DoubleValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline DoubleValue& operator=(DoubleValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DoubleValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const DoubleValue* internal_default_instance() {
    return reinterpret_cast<const DoubleValue*>(
               &_DoubleValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DoubleValue& a, DoubleValue& b) {
    a.Swap(&b);
  }
  inline void Swap(DoubleValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DoubleValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DoubleValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DoubleValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DoubleValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DoubleValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DoubleValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.DoubleValue";
  }
  protected:
  explicit DoubleValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // double value = 1;
  void clear_value();
  double value() const;
  void set_value(double value);
  private:
  double _internal_value() const;
  void _internal_set_value(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.DoubleValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// -------------------------------------------------------------------

class StringValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.StringValue) */ {
 public:
  inline StringValue() : StringValue(nullptr) {}
  ~StringValue() override;
  explicit constexpr StringValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StringValue(const StringValue& from);
  StringValue(StringValue&& from) noexcept
    : StringValue() {
    *this = ::std::move(from);
  }

  inline StringValue& operator=(const StringValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline StringValue& operator=(StringValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StringValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const StringValue* internal_default_instance() {
    return reinterpret_cast<const StringValue*>(
               &_StringValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(StringValue& a, StringValue& b) {
    a.Swap(&b);
  }
  inline void Swap(StringValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StringValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StringValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StringValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StringValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StringValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StringValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.StringValue";
  }
  protected:
  explicit StringValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // string value = 1;
  void clear_value();
  const std::string& value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_value();
  PROTOBUF_NODISCARD std::string* release_value();
  void set_allocated_value(std::string* value);
  private:
  const std::string& _internal_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_value(const std::string& value);
  std::string* _internal_mutable_value();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.StringValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// -------------------------------------------------------------------

class TemperatureValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.TemperatureValue) */ {
 public:
  inline TemperatureValue() : TemperatureValue(nullptr) {}
  ~TemperatureValue() override;
  explicit constexpr TemperatureValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TemperatureValue(const TemperatureValue& from);
  TemperatureValue(TemperatureValue&& from) noexcept
    : TemperatureValue() {
    *this = ::std::move(from);
  }

  inline TemperatureValue& operator=(const TemperatureValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline TemperatureValue& operator=(TemperatureValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TemperatureValue& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kCelcius = 1,
    kFahrenheit = 2,
    VALUE_NOT_SET = 0,
  };

  static inline const TemperatureValue* internal_default_instance() {
    return reinterpret_cast<const TemperatureValue*>(
               &_TemperatureValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TemperatureValue& a, TemperatureValue& b) {
    a.Swap(&b);
  }
  inline void Swap(TemperatureValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TemperatureValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TemperatureValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TemperatureValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TemperatureValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TemperatureValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TemperatureValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.TemperatureValue";
  }
  protected:
  explicit TemperatureValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCelciusFieldNumber = 1,
    kFahrenheitFieldNumber = 2,
  };
  // double celcius = 1;
  bool has_celcius() const;
  private:
  bool _internal_has_celcius() const;
  public:
  void clear_celcius();
  double celcius() const;
  void set_celcius(double value);
  private:
  double _internal_celcius() const;
  void _internal_set_celcius(double value);
  public:

  // double fahrenheit = 2;
  bool has_fahrenheit() const;
  private:
  bool _internal_has_fahrenheit() const;
  public:
  void clear_fahrenheit();
  double fahrenheit() const;
  void set_fahrenheit(double value);
  private:
  double _internal_fahrenheit() const;
  void _internal_set_fahrenheit(double value);
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.TemperatureValue)
 private:
  class _Internal;
  void set_has_celcius();
  void set_has_fahrenheit();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union ValueUnion {
    constexpr ValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    double celcius_;
    double fahrenheit_;
  } value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// -------------------------------------------------------------------

class PercentValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.PercentValue) */ {
 public:
  inline PercentValue() : PercentValue(nullptr) {}
  ~PercentValue() override;
  explicit constexpr PercentValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PercentValue(const PercentValue& from);
  PercentValue(PercentValue&& from) noexcept
    : PercentValue() {
    *this = ::std::move(from);
  }

  inline PercentValue& operator=(const PercentValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline PercentValue& operator=(PercentValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PercentValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const PercentValue* internal_default_instance() {
    return reinterpret_cast<const PercentValue*>(
               &_PercentValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(PercentValue& a, PercentValue& b) {
    a.Swap(&b);
  }
  inline void Swap(PercentValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PercentValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PercentValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PercentValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PercentValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PercentValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PercentValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.PercentValue";
  }
  protected:
  explicit PercentValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPercentFieldNumber = 1,
  };
  // uint32 percent = 1;
  void clear_percent();
  uint32_t percent() const;
  void set_percent(uint32_t value);
  private:
  uint32_t _internal_percent() const;
  void _internal_set_percent(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.PercentValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t percent_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// -------------------------------------------------------------------

class VoltageValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.VoltageValue) */ {
 public:
  inline VoltageValue() : VoltageValue(nullptr) {}
  ~VoltageValue() override;
  explicit constexpr VoltageValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VoltageValue(const VoltageValue& from);
  VoltageValue(VoltageValue&& from) noexcept
    : VoltageValue() {
    *this = ::std::move(from);
  }

  inline VoltageValue& operator=(const VoltageValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline VoltageValue& operator=(VoltageValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VoltageValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const VoltageValue* internal_default_instance() {
    return reinterpret_cast<const VoltageValue*>(
               &_VoltageValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(VoltageValue& a, VoltageValue& b) {
    a.Swap(&b);
  }
  inline void Swap(VoltageValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VoltageValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VoltageValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VoltageValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VoltageValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VoltageValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VoltageValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.VoltageValue";
  }
  protected:
  explicit VoltageValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVoltsFieldNumber = 1,
  };
  // double volts = 1;
  void clear_volts();
  double volts() const;
  void set_volts(double value);
  private:
  double _internal_volts() const;
  void _internal_set_volts(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.VoltageValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double volts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// -------------------------------------------------------------------

class FrequencyValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.FrequencyValue) */ {
 public:
  inline FrequencyValue() : FrequencyValue(nullptr) {}
  ~FrequencyValue() override;
  explicit constexpr FrequencyValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FrequencyValue(const FrequencyValue& from);
  FrequencyValue(FrequencyValue&& from) noexcept
    : FrequencyValue() {
    *this = ::std::move(from);
  }

  inline FrequencyValue& operator=(const FrequencyValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline FrequencyValue& operator=(FrequencyValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FrequencyValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const FrequencyValue* internal_default_instance() {
    return reinterpret_cast<const FrequencyValue*>(
               &_FrequencyValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(FrequencyValue& a, FrequencyValue& b) {
    a.Swap(&b);
  }
  inline void Swap(FrequencyValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FrequencyValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FrequencyValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FrequencyValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FrequencyValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FrequencyValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FrequencyValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.FrequencyValue";
  }
  protected:
  explicit FrequencyValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHertzFieldNumber = 1,
  };
  // double hertz = 1;
  void clear_hertz();
  double hertz() const;
  void set_hertz(double value);
  private:
  double _internal_hertz() const;
  void _internal_set_hertz(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.FrequencyValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double hertz_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// -------------------------------------------------------------------

class AreaValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.AreaValue) */ {
 public:
  inline AreaValue() : AreaValue(nullptr) {}
  ~AreaValue() override;
  explicit constexpr AreaValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AreaValue(const AreaValue& from);
  AreaValue(AreaValue&& from) noexcept
    : AreaValue() {
    *this = ::std::move(from);
  }

  inline AreaValue& operator=(const AreaValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline AreaValue& operator=(AreaValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AreaValue& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kAcres = 1,
    kHectares = 2,
    kSquareFeet = 3,
    kSquareMeters = 4,
    VALUE_NOT_SET = 0,
  };

  static inline const AreaValue* internal_default_instance() {
    return reinterpret_cast<const AreaValue*>(
               &_AreaValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(AreaValue& a, AreaValue& b) {
    a.Swap(&b);
  }
  inline void Swap(AreaValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AreaValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AreaValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AreaValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AreaValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AreaValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AreaValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.AreaValue";
  }
  protected:
  explicit AreaValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAcresFieldNumber = 1,
    kHectaresFieldNumber = 2,
    kSquareFeetFieldNumber = 3,
    kSquareMetersFieldNumber = 4,
  };
  // double acres = 1;
  bool has_acres() const;
  private:
  bool _internal_has_acres() const;
  public:
  void clear_acres();
  double acres() const;
  void set_acres(double value);
  private:
  double _internal_acres() const;
  void _internal_set_acres(double value);
  public:

  // double hectares = 2;
  bool has_hectares() const;
  private:
  bool _internal_has_hectares() const;
  public:
  void clear_hectares();
  double hectares() const;
  void set_hectares(double value);
  private:
  double _internal_hectares() const;
  void _internal_set_hectares(double value);
  public:

  // double square_feet = 3;
  bool has_square_feet() const;
  private:
  bool _internal_has_square_feet() const;
  public:
  void clear_square_feet();
  double square_feet() const;
  void set_square_feet(double value);
  private:
  double _internal_square_feet() const;
  void _internal_set_square_feet(double value);
  public:

  // double square_meters = 4;
  bool has_square_meters() const;
  private:
  bool _internal_has_square_meters() const;
  public:
  void clear_square_meters();
  double square_meters() const;
  void set_square_meters(double value);
  private:
  double _internal_square_meters() const;
  void _internal_set_square_meters(double value);
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.AreaValue)
 private:
  class _Internal;
  void set_has_acres();
  void set_has_hectares();
  void set_has_square_feet();
  void set_has_square_meters();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union ValueUnion {
    constexpr ValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    double acres_;
    double hectares_;
    double square_feet_;
    double square_meters_;
  } value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// -------------------------------------------------------------------

class DurationValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.DurationValue) */ {
 public:
  inline DurationValue() : DurationValue(nullptr) {}
  ~DurationValue() override;
  explicit constexpr DurationValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DurationValue(const DurationValue& from);
  DurationValue(DurationValue&& from) noexcept
    : DurationValue() {
    *this = ::std::move(from);
  }

  inline DurationValue& operator=(const DurationValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline DurationValue& operator=(DurationValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DurationValue& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kMilliseconds = 1,
    kSeconds = 2,
    kMinutes = 3,
    kHours = 4,
    VALUE_NOT_SET = 0,
  };

  static inline const DurationValue* internal_default_instance() {
    return reinterpret_cast<const DurationValue*>(
               &_DurationValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(DurationValue& a, DurationValue& b) {
    a.Swap(&b);
  }
  inline void Swap(DurationValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DurationValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DurationValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DurationValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DurationValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DurationValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DurationValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.DurationValue";
  }
  protected:
  explicit DurationValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMillisecondsFieldNumber = 1,
    kSecondsFieldNumber = 2,
    kMinutesFieldNumber = 3,
    kHoursFieldNumber = 4,
  };
  // uint64 milliseconds = 1;
  bool has_milliseconds() const;
  private:
  bool _internal_has_milliseconds() const;
  public:
  void clear_milliseconds();
  uint64_t milliseconds() const;
  void set_milliseconds(uint64_t value);
  private:
  uint64_t _internal_milliseconds() const;
  void _internal_set_milliseconds(uint64_t value);
  public:

  // uint64 seconds = 2;
  bool has_seconds() const;
  private:
  bool _internal_has_seconds() const;
  public:
  void clear_seconds();
  uint64_t seconds() const;
  void set_seconds(uint64_t value);
  private:
  uint64_t _internal_seconds() const;
  void _internal_set_seconds(uint64_t value);
  public:

  // uint64 minutes = 3;
  bool has_minutes() const;
  private:
  bool _internal_has_minutes() const;
  public:
  void clear_minutes();
  uint64_t minutes() const;
  void set_minutes(uint64_t value);
  private:
  uint64_t _internal_minutes() const;
  void _internal_set_minutes(uint64_t value);
  public:

  // uint64 hours = 4;
  bool has_hours() const;
  private:
  bool _internal_has_hours() const;
  public:
  void clear_hours();
  uint64_t hours() const;
  void set_hours(uint64_t value);
  private:
  uint64_t _internal_hours() const;
  void _internal_set_hours(uint64_t value);
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.DurationValue)
 private:
  class _Internal;
  void set_has_milliseconds();
  void set_has_seconds();
  void set_has_minutes();
  void set_has_hours();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union ValueUnion {
    constexpr ValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    uint64_t milliseconds_;
    uint64_t seconds_;
    uint64_t minutes_;
    uint64_t hours_;
  } value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// -------------------------------------------------------------------

class DistanceValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.DistanceValue) */ {
 public:
  inline DistanceValue() : DistanceValue(nullptr) {}
  ~DistanceValue() override;
  explicit constexpr DistanceValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DistanceValue(const DistanceValue& from);
  DistanceValue(DistanceValue&& from) noexcept
    : DistanceValue() {
    *this = ::std::move(from);
  }

  inline DistanceValue& operator=(const DistanceValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline DistanceValue& operator=(DistanceValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DistanceValue& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kMillimeters = 1,
    kMeters = 2,
    kKilometers = 3,
    kInches = 4,
    kFeet = 5,
    kMiles = 6,
    kCentimeters = 7,
    VALUE_NOT_SET = 0,
  };

  static inline const DistanceValue* internal_default_instance() {
    return reinterpret_cast<const DistanceValue*>(
               &_DistanceValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(DistanceValue& a, DistanceValue& b) {
    a.Swap(&b);
  }
  inline void Swap(DistanceValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DistanceValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DistanceValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DistanceValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DistanceValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DistanceValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DistanceValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.DistanceValue";
  }
  protected:
  explicit DistanceValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMillimetersFieldNumber = 1,
    kMetersFieldNumber = 2,
    kKilometersFieldNumber = 3,
    kInchesFieldNumber = 4,
    kFeetFieldNumber = 5,
    kMilesFieldNumber = 6,
    kCentimetersFieldNumber = 7,
  };
  // double millimeters = 1;
  bool has_millimeters() const;
  private:
  bool _internal_has_millimeters() const;
  public:
  void clear_millimeters();
  double millimeters() const;
  void set_millimeters(double value);
  private:
  double _internal_millimeters() const;
  void _internal_set_millimeters(double value);
  public:

  // double meters = 2;
  bool has_meters() const;
  private:
  bool _internal_has_meters() const;
  public:
  void clear_meters();
  double meters() const;
  void set_meters(double value);
  private:
  double _internal_meters() const;
  void _internal_set_meters(double value);
  public:

  // double kilometers = 3;
  bool has_kilometers() const;
  private:
  bool _internal_has_kilometers() const;
  public:
  void clear_kilometers();
  double kilometers() const;
  void set_kilometers(double value);
  private:
  double _internal_kilometers() const;
  void _internal_set_kilometers(double value);
  public:

  // double inches = 4;
  bool has_inches() const;
  private:
  bool _internal_has_inches() const;
  public:
  void clear_inches();
  double inches() const;
  void set_inches(double value);
  private:
  double _internal_inches() const;
  void _internal_set_inches(double value);
  public:

  // double feet = 5;
  bool has_feet() const;
  private:
  bool _internal_has_feet() const;
  public:
  void clear_feet();
  double feet() const;
  void set_feet(double value);
  private:
  double _internal_feet() const;
  void _internal_set_feet(double value);
  public:

  // double miles = 6;
  bool has_miles() const;
  private:
  bool _internal_has_miles() const;
  public:
  void clear_miles();
  double miles() const;
  void set_miles(double value);
  private:
  double _internal_miles() const;
  void _internal_set_miles(double value);
  public:

  // double centimeters = 7;
  bool has_centimeters() const;
  private:
  bool _internal_has_centimeters() const;
  public:
  void clear_centimeters();
  double centimeters() const;
  void set_centimeters(double value);
  private:
  double _internal_centimeters() const;
  void _internal_set_centimeters(double value);
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.DistanceValue)
 private:
  class _Internal;
  void set_has_millimeters();
  void set_has_meters();
  void set_has_kilometers();
  void set_has_inches();
  void set_has_feet();
  void set_has_miles();
  void set_has_centimeters();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union ValueUnion {
    constexpr ValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    double millimeters_;
    double meters_;
    double kilometers_;
    double inches_;
    double feet_;
    double miles_;
    double centimeters_;
  } value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// -------------------------------------------------------------------

class SpeedValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.SpeedValue) */ {
 public:
  inline SpeedValue() : SpeedValue(nullptr) {}
  ~SpeedValue() override;
  explicit constexpr SpeedValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SpeedValue(const SpeedValue& from);
  SpeedValue(SpeedValue&& from) noexcept
    : SpeedValue() {
    *this = ::std::move(from);
  }

  inline SpeedValue& operator=(const SpeedValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline SpeedValue& operator=(SpeedValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SpeedValue& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kKilometersPerHour = 1,
    kMilesPerHour = 2,
    VALUE_NOT_SET = 0,
  };

  static inline const SpeedValue* internal_default_instance() {
    return reinterpret_cast<const SpeedValue*>(
               &_SpeedValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(SpeedValue& a, SpeedValue& b) {
    a.Swap(&b);
  }
  inline void Swap(SpeedValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SpeedValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SpeedValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SpeedValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SpeedValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SpeedValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SpeedValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.SpeedValue";
  }
  protected:
  explicit SpeedValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKilometersPerHourFieldNumber = 1,
    kMilesPerHourFieldNumber = 2,
  };
  // double kilometers_per_hour = 1;
  bool has_kilometers_per_hour() const;
  private:
  bool _internal_has_kilometers_per_hour() const;
  public:
  void clear_kilometers_per_hour();
  double kilometers_per_hour() const;
  void set_kilometers_per_hour(double value);
  private:
  double _internal_kilometers_per_hour() const;
  void _internal_set_kilometers_per_hour(double value);
  public:

  // double miles_per_hour = 2;
  bool has_miles_per_hour() const;
  private:
  bool _internal_has_miles_per_hour() const;
  public:
  void clear_miles_per_hour();
  double miles_per_hour() const;
  void set_miles_per_hour(double value);
  private:
  double _internal_miles_per_hour() const;
  void _internal_set_miles_per_hour(double value);
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.SpeedValue)
 private:
  class _Internal;
  void set_has_kilometers_per_hour();
  void set_has_miles_per_hour();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union ValueUnion {
    constexpr ValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    double kilometers_per_hour_;
    double miles_per_hour_;
  } value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// -------------------------------------------------------------------

class TranslationParameter final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.translation.TranslationParameter) */ {
 public:
  inline TranslationParameter() : TranslationParameter(nullptr) {}
  ~TranslationParameter() override;
  explicit constexpr TranslationParameter(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TranslationParameter(const TranslationParameter& from);
  TranslationParameter(TranslationParameter&& from) noexcept
    : TranslationParameter() {
    *this = ::std::move(from);
  }

  inline TranslationParameter& operator=(const TranslationParameter& from) {
    CopyFrom(from);
    return *this;
  }
  inline TranslationParameter& operator=(TranslationParameter&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TranslationParameter& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kIntValue = 2,
    kDoubleValue = 3,
    kStringValue = 4,
    kTemperatureValue = 5,
    kPercentValue = 6,
    kVoltageValue = 7,
    kFrequencyValue = 8,
    kAreaValue = 9,
    kDurationValue = 10,
    kDistanceValue = 11,
    kSpeedValue = 12,
    VALUE_NOT_SET = 0,
  };

  static inline const TranslationParameter* internal_default_instance() {
    return reinterpret_cast<const TranslationParameter*>(
               &_TranslationParameter_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(TranslationParameter& a, TranslationParameter& b) {
    a.Swap(&b);
  }
  inline void Swap(TranslationParameter* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TranslationParameter* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TranslationParameter* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TranslationParameter>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TranslationParameter& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TranslationParameter& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TranslationParameter* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.translation.TranslationParameter";
  }
  protected:
  explicit TranslationParameter(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kIntValueFieldNumber = 2,
    kDoubleValueFieldNumber = 3,
    kStringValueFieldNumber = 4,
    kTemperatureValueFieldNumber = 5,
    kPercentValueFieldNumber = 6,
    kVoltageValueFieldNumber = 7,
    kFrequencyValueFieldNumber = 8,
    kAreaValueFieldNumber = 9,
    kDurationValueFieldNumber = 10,
    kDistanceValueFieldNumber = 11,
    kSpeedValueFieldNumber = 12,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .carbon.frontend.translation.IntegerValue int_value = 2;
  bool has_int_value() const;
  private:
  bool _internal_has_int_value() const;
  public:
  void clear_int_value();
  const ::carbon::frontend::translation::IntegerValue& int_value() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::IntegerValue* release_int_value();
  ::carbon::frontend::translation::IntegerValue* mutable_int_value();
  void set_allocated_int_value(::carbon::frontend::translation::IntegerValue* int_value);
  private:
  const ::carbon::frontend::translation::IntegerValue& _internal_int_value() const;
  ::carbon::frontend::translation::IntegerValue* _internal_mutable_int_value();
  public:
  void unsafe_arena_set_allocated_int_value(
      ::carbon::frontend::translation::IntegerValue* int_value);
  ::carbon::frontend::translation::IntegerValue* unsafe_arena_release_int_value();

  // .carbon.frontend.translation.DoubleValue double_value = 3;
  bool has_double_value() const;
  private:
  bool _internal_has_double_value() const;
  public:
  void clear_double_value();
  const ::carbon::frontend::translation::DoubleValue& double_value() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::DoubleValue* release_double_value();
  ::carbon::frontend::translation::DoubleValue* mutable_double_value();
  void set_allocated_double_value(::carbon::frontend::translation::DoubleValue* double_value);
  private:
  const ::carbon::frontend::translation::DoubleValue& _internal_double_value() const;
  ::carbon::frontend::translation::DoubleValue* _internal_mutable_double_value();
  public:
  void unsafe_arena_set_allocated_double_value(
      ::carbon::frontend::translation::DoubleValue* double_value);
  ::carbon::frontend::translation::DoubleValue* unsafe_arena_release_double_value();

  // .carbon.frontend.translation.StringValue string_value = 4;
  bool has_string_value() const;
  private:
  bool _internal_has_string_value() const;
  public:
  void clear_string_value();
  const ::carbon::frontend::translation::StringValue& string_value() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::StringValue* release_string_value();
  ::carbon::frontend::translation::StringValue* mutable_string_value();
  void set_allocated_string_value(::carbon::frontend::translation::StringValue* string_value);
  private:
  const ::carbon::frontend::translation::StringValue& _internal_string_value() const;
  ::carbon::frontend::translation::StringValue* _internal_mutable_string_value();
  public:
  void unsafe_arena_set_allocated_string_value(
      ::carbon::frontend::translation::StringValue* string_value);
  ::carbon::frontend::translation::StringValue* unsafe_arena_release_string_value();

  // .carbon.frontend.translation.TemperatureValue temperature_value = 5;
  bool has_temperature_value() const;
  private:
  bool _internal_has_temperature_value() const;
  public:
  void clear_temperature_value();
  const ::carbon::frontend::translation::TemperatureValue& temperature_value() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::TemperatureValue* release_temperature_value();
  ::carbon::frontend::translation::TemperatureValue* mutable_temperature_value();
  void set_allocated_temperature_value(::carbon::frontend::translation::TemperatureValue* temperature_value);
  private:
  const ::carbon::frontend::translation::TemperatureValue& _internal_temperature_value() const;
  ::carbon::frontend::translation::TemperatureValue* _internal_mutable_temperature_value();
  public:
  void unsafe_arena_set_allocated_temperature_value(
      ::carbon::frontend::translation::TemperatureValue* temperature_value);
  ::carbon::frontend::translation::TemperatureValue* unsafe_arena_release_temperature_value();

  // .carbon.frontend.translation.PercentValue percent_value = 6;
  bool has_percent_value() const;
  private:
  bool _internal_has_percent_value() const;
  public:
  void clear_percent_value();
  const ::carbon::frontend::translation::PercentValue& percent_value() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::PercentValue* release_percent_value();
  ::carbon::frontend::translation::PercentValue* mutable_percent_value();
  void set_allocated_percent_value(::carbon::frontend::translation::PercentValue* percent_value);
  private:
  const ::carbon::frontend::translation::PercentValue& _internal_percent_value() const;
  ::carbon::frontend::translation::PercentValue* _internal_mutable_percent_value();
  public:
  void unsafe_arena_set_allocated_percent_value(
      ::carbon::frontend::translation::PercentValue* percent_value);
  ::carbon::frontend::translation::PercentValue* unsafe_arena_release_percent_value();

  // .carbon.frontend.translation.VoltageValue voltage_value = 7;
  bool has_voltage_value() const;
  private:
  bool _internal_has_voltage_value() const;
  public:
  void clear_voltage_value();
  const ::carbon::frontend::translation::VoltageValue& voltage_value() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::VoltageValue* release_voltage_value();
  ::carbon::frontend::translation::VoltageValue* mutable_voltage_value();
  void set_allocated_voltage_value(::carbon::frontend::translation::VoltageValue* voltage_value);
  private:
  const ::carbon::frontend::translation::VoltageValue& _internal_voltage_value() const;
  ::carbon::frontend::translation::VoltageValue* _internal_mutable_voltage_value();
  public:
  void unsafe_arena_set_allocated_voltage_value(
      ::carbon::frontend::translation::VoltageValue* voltage_value);
  ::carbon::frontend::translation::VoltageValue* unsafe_arena_release_voltage_value();

  // .carbon.frontend.translation.FrequencyValue frequency_value = 8;
  bool has_frequency_value() const;
  private:
  bool _internal_has_frequency_value() const;
  public:
  void clear_frequency_value();
  const ::carbon::frontend::translation::FrequencyValue& frequency_value() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::FrequencyValue* release_frequency_value();
  ::carbon::frontend::translation::FrequencyValue* mutable_frequency_value();
  void set_allocated_frequency_value(::carbon::frontend::translation::FrequencyValue* frequency_value);
  private:
  const ::carbon::frontend::translation::FrequencyValue& _internal_frequency_value() const;
  ::carbon::frontend::translation::FrequencyValue* _internal_mutable_frequency_value();
  public:
  void unsafe_arena_set_allocated_frequency_value(
      ::carbon::frontend::translation::FrequencyValue* frequency_value);
  ::carbon::frontend::translation::FrequencyValue* unsafe_arena_release_frequency_value();

  // .carbon.frontend.translation.AreaValue area_value = 9;
  bool has_area_value() const;
  private:
  bool _internal_has_area_value() const;
  public:
  void clear_area_value();
  const ::carbon::frontend::translation::AreaValue& area_value() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::AreaValue* release_area_value();
  ::carbon::frontend::translation::AreaValue* mutable_area_value();
  void set_allocated_area_value(::carbon::frontend::translation::AreaValue* area_value);
  private:
  const ::carbon::frontend::translation::AreaValue& _internal_area_value() const;
  ::carbon::frontend::translation::AreaValue* _internal_mutable_area_value();
  public:
  void unsafe_arena_set_allocated_area_value(
      ::carbon::frontend::translation::AreaValue* area_value);
  ::carbon::frontend::translation::AreaValue* unsafe_arena_release_area_value();

  // .carbon.frontend.translation.DurationValue duration_value = 10;
  bool has_duration_value() const;
  private:
  bool _internal_has_duration_value() const;
  public:
  void clear_duration_value();
  const ::carbon::frontend::translation::DurationValue& duration_value() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::DurationValue* release_duration_value();
  ::carbon::frontend::translation::DurationValue* mutable_duration_value();
  void set_allocated_duration_value(::carbon::frontend::translation::DurationValue* duration_value);
  private:
  const ::carbon::frontend::translation::DurationValue& _internal_duration_value() const;
  ::carbon::frontend::translation::DurationValue* _internal_mutable_duration_value();
  public:
  void unsafe_arena_set_allocated_duration_value(
      ::carbon::frontend::translation::DurationValue* duration_value);
  ::carbon::frontend::translation::DurationValue* unsafe_arena_release_duration_value();

  // .carbon.frontend.translation.DistanceValue distance_value = 11;
  bool has_distance_value() const;
  private:
  bool _internal_has_distance_value() const;
  public:
  void clear_distance_value();
  const ::carbon::frontend::translation::DistanceValue& distance_value() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::DistanceValue* release_distance_value();
  ::carbon::frontend::translation::DistanceValue* mutable_distance_value();
  void set_allocated_distance_value(::carbon::frontend::translation::DistanceValue* distance_value);
  private:
  const ::carbon::frontend::translation::DistanceValue& _internal_distance_value() const;
  ::carbon::frontend::translation::DistanceValue* _internal_mutable_distance_value();
  public:
  void unsafe_arena_set_allocated_distance_value(
      ::carbon::frontend::translation::DistanceValue* distance_value);
  ::carbon::frontend::translation::DistanceValue* unsafe_arena_release_distance_value();

  // .carbon.frontend.translation.SpeedValue speed_value = 12;
  bool has_speed_value() const;
  private:
  bool _internal_has_speed_value() const;
  public:
  void clear_speed_value();
  const ::carbon::frontend::translation::SpeedValue& speed_value() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::SpeedValue* release_speed_value();
  ::carbon::frontend::translation::SpeedValue* mutable_speed_value();
  void set_allocated_speed_value(::carbon::frontend::translation::SpeedValue* speed_value);
  private:
  const ::carbon::frontend::translation::SpeedValue& _internal_speed_value() const;
  ::carbon::frontend::translation::SpeedValue* _internal_mutable_speed_value();
  public:
  void unsafe_arena_set_allocated_speed_value(
      ::carbon::frontend::translation::SpeedValue* speed_value);
  ::carbon::frontend::translation::SpeedValue* unsafe_arena_release_speed_value();

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:carbon.frontend.translation.TranslationParameter)
 private:
  class _Internal;
  void set_has_int_value();
  void set_has_double_value();
  void set_has_string_value();
  void set_has_temperature_value();
  void set_has_percent_value();
  void set_has_voltage_value();
  void set_has_frequency_value();
  void set_has_area_value();
  void set_has_duration_value();
  void set_has_distance_value();
  void set_has_speed_value();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  union ValueUnion {
    constexpr ValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::carbon::frontend::translation::IntegerValue* int_value_;
    ::carbon::frontend::translation::DoubleValue* double_value_;
    ::carbon::frontend::translation::StringValue* string_value_;
    ::carbon::frontend::translation::TemperatureValue* temperature_value_;
    ::carbon::frontend::translation::PercentValue* percent_value_;
    ::carbon::frontend::translation::VoltageValue* voltage_value_;
    ::carbon::frontend::translation::FrequencyValue* frequency_value_;
    ::carbon::frontend::translation::AreaValue* area_value_;
    ::carbon::frontend::translation::DurationValue* duration_value_;
    ::carbon::frontend::translation::DistanceValue* distance_value_;
    ::carbon::frontend::translation::SpeedValue* speed_value_;
  } value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_frontend_2fproto_2ftranslation_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// IntegerValue

// int64 value = 1;
inline void IntegerValue::clear_value() {
  value_ = int64_t{0};
}
inline int64_t IntegerValue::_internal_value() const {
  return value_;
}
inline int64_t IntegerValue::value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.IntegerValue.value)
  return _internal_value();
}
inline void IntegerValue::_internal_set_value(int64_t value) {
  
  value_ = value;
}
inline void IntegerValue::set_value(int64_t value) {
  _internal_set_value(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.IntegerValue.value)
}

// -------------------------------------------------------------------

// DoubleValue

// double value = 1;
inline void DoubleValue::clear_value() {
  value_ = 0;
}
inline double DoubleValue::_internal_value() const {
  return value_;
}
inline double DoubleValue::value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DoubleValue.value)
  return _internal_value();
}
inline void DoubleValue::_internal_set_value(double value) {
  
  value_ = value;
}
inline void DoubleValue::set_value(double value) {
  _internal_set_value(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DoubleValue.value)
}

// -------------------------------------------------------------------

// StringValue

// string value = 1;
inline void StringValue::clear_value() {
  value_.ClearToEmpty();
}
inline const std::string& StringValue::value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.StringValue.value)
  return _internal_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StringValue::set_value(ArgT0&& arg0, ArgT... args) {
 
 value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.StringValue.value)
}
inline std::string* StringValue::mutable_value() {
  std::string* _s = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.StringValue.value)
  return _s;
}
inline const std::string& StringValue::_internal_value() const {
  return value_.Get();
}
inline void StringValue::_internal_set_value(const std::string& value) {
  
  value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StringValue::_internal_mutable_value() {
  
  return value_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StringValue::release_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.StringValue.value)
  return value_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StringValue::set_allocated_value(std::string* value) {
  if (value != nullptr) {
    
  } else {
    
  }
  value_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (value_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.StringValue.value)
}

// -------------------------------------------------------------------

// TemperatureValue

// double celcius = 1;
inline bool TemperatureValue::_internal_has_celcius() const {
  return value_case() == kCelcius;
}
inline bool TemperatureValue::has_celcius() const {
  return _internal_has_celcius();
}
inline void TemperatureValue::set_has_celcius() {
  _oneof_case_[0] = kCelcius;
}
inline void TemperatureValue::clear_celcius() {
  if (_internal_has_celcius()) {
    value_.celcius_ = 0;
    clear_has_value();
  }
}
inline double TemperatureValue::_internal_celcius() const {
  if (_internal_has_celcius()) {
    return value_.celcius_;
  }
  return 0;
}
inline void TemperatureValue::_internal_set_celcius(double value) {
  if (!_internal_has_celcius()) {
    clear_value();
    set_has_celcius();
  }
  value_.celcius_ = value;
}
inline double TemperatureValue::celcius() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TemperatureValue.celcius)
  return _internal_celcius();
}
inline void TemperatureValue::set_celcius(double value) {
  _internal_set_celcius(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.TemperatureValue.celcius)
}

// double fahrenheit = 2;
inline bool TemperatureValue::_internal_has_fahrenheit() const {
  return value_case() == kFahrenheit;
}
inline bool TemperatureValue::has_fahrenheit() const {
  return _internal_has_fahrenheit();
}
inline void TemperatureValue::set_has_fahrenheit() {
  _oneof_case_[0] = kFahrenheit;
}
inline void TemperatureValue::clear_fahrenheit() {
  if (_internal_has_fahrenheit()) {
    value_.fahrenheit_ = 0;
    clear_has_value();
  }
}
inline double TemperatureValue::_internal_fahrenheit() const {
  if (_internal_has_fahrenheit()) {
    return value_.fahrenheit_;
  }
  return 0;
}
inline void TemperatureValue::_internal_set_fahrenheit(double value) {
  if (!_internal_has_fahrenheit()) {
    clear_value();
    set_has_fahrenheit();
  }
  value_.fahrenheit_ = value;
}
inline double TemperatureValue::fahrenheit() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TemperatureValue.fahrenheit)
  return _internal_fahrenheit();
}
inline void TemperatureValue::set_fahrenheit(double value) {
  _internal_set_fahrenheit(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.TemperatureValue.fahrenheit)
}

inline bool TemperatureValue::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void TemperatureValue::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline TemperatureValue::ValueCase TemperatureValue::value_case() const {
  return TemperatureValue::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// PercentValue

// uint32 percent = 1;
inline void PercentValue::clear_percent() {
  percent_ = 0u;
}
inline uint32_t PercentValue::_internal_percent() const {
  return percent_;
}
inline uint32_t PercentValue::percent() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.PercentValue.percent)
  return _internal_percent();
}
inline void PercentValue::_internal_set_percent(uint32_t value) {
  
  percent_ = value;
}
inline void PercentValue::set_percent(uint32_t value) {
  _internal_set_percent(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.PercentValue.percent)
}

// -------------------------------------------------------------------

// VoltageValue

// double volts = 1;
inline void VoltageValue::clear_volts() {
  volts_ = 0;
}
inline double VoltageValue::_internal_volts() const {
  return volts_;
}
inline double VoltageValue::volts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.VoltageValue.volts)
  return _internal_volts();
}
inline void VoltageValue::_internal_set_volts(double value) {
  
  volts_ = value;
}
inline void VoltageValue::set_volts(double value) {
  _internal_set_volts(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.VoltageValue.volts)
}

// -------------------------------------------------------------------

// FrequencyValue

// double hertz = 1;
inline void FrequencyValue::clear_hertz() {
  hertz_ = 0;
}
inline double FrequencyValue::_internal_hertz() const {
  return hertz_;
}
inline double FrequencyValue::hertz() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.FrequencyValue.hertz)
  return _internal_hertz();
}
inline void FrequencyValue::_internal_set_hertz(double value) {
  
  hertz_ = value;
}
inline void FrequencyValue::set_hertz(double value) {
  _internal_set_hertz(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.FrequencyValue.hertz)
}

// -------------------------------------------------------------------

// AreaValue

// double acres = 1;
inline bool AreaValue::_internal_has_acres() const {
  return value_case() == kAcres;
}
inline bool AreaValue::has_acres() const {
  return _internal_has_acres();
}
inline void AreaValue::set_has_acres() {
  _oneof_case_[0] = kAcres;
}
inline void AreaValue::clear_acres() {
  if (_internal_has_acres()) {
    value_.acres_ = 0;
    clear_has_value();
  }
}
inline double AreaValue::_internal_acres() const {
  if (_internal_has_acres()) {
    return value_.acres_;
  }
  return 0;
}
inline void AreaValue::_internal_set_acres(double value) {
  if (!_internal_has_acres()) {
    clear_value();
    set_has_acres();
  }
  value_.acres_ = value;
}
inline double AreaValue::acres() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.AreaValue.acres)
  return _internal_acres();
}
inline void AreaValue::set_acres(double value) {
  _internal_set_acres(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.AreaValue.acres)
}

// double hectares = 2;
inline bool AreaValue::_internal_has_hectares() const {
  return value_case() == kHectares;
}
inline bool AreaValue::has_hectares() const {
  return _internal_has_hectares();
}
inline void AreaValue::set_has_hectares() {
  _oneof_case_[0] = kHectares;
}
inline void AreaValue::clear_hectares() {
  if (_internal_has_hectares()) {
    value_.hectares_ = 0;
    clear_has_value();
  }
}
inline double AreaValue::_internal_hectares() const {
  if (_internal_has_hectares()) {
    return value_.hectares_;
  }
  return 0;
}
inline void AreaValue::_internal_set_hectares(double value) {
  if (!_internal_has_hectares()) {
    clear_value();
    set_has_hectares();
  }
  value_.hectares_ = value;
}
inline double AreaValue::hectares() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.AreaValue.hectares)
  return _internal_hectares();
}
inline void AreaValue::set_hectares(double value) {
  _internal_set_hectares(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.AreaValue.hectares)
}

// double square_feet = 3;
inline bool AreaValue::_internal_has_square_feet() const {
  return value_case() == kSquareFeet;
}
inline bool AreaValue::has_square_feet() const {
  return _internal_has_square_feet();
}
inline void AreaValue::set_has_square_feet() {
  _oneof_case_[0] = kSquareFeet;
}
inline void AreaValue::clear_square_feet() {
  if (_internal_has_square_feet()) {
    value_.square_feet_ = 0;
    clear_has_value();
  }
}
inline double AreaValue::_internal_square_feet() const {
  if (_internal_has_square_feet()) {
    return value_.square_feet_;
  }
  return 0;
}
inline void AreaValue::_internal_set_square_feet(double value) {
  if (!_internal_has_square_feet()) {
    clear_value();
    set_has_square_feet();
  }
  value_.square_feet_ = value;
}
inline double AreaValue::square_feet() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.AreaValue.square_feet)
  return _internal_square_feet();
}
inline void AreaValue::set_square_feet(double value) {
  _internal_set_square_feet(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.AreaValue.square_feet)
}

// double square_meters = 4;
inline bool AreaValue::_internal_has_square_meters() const {
  return value_case() == kSquareMeters;
}
inline bool AreaValue::has_square_meters() const {
  return _internal_has_square_meters();
}
inline void AreaValue::set_has_square_meters() {
  _oneof_case_[0] = kSquareMeters;
}
inline void AreaValue::clear_square_meters() {
  if (_internal_has_square_meters()) {
    value_.square_meters_ = 0;
    clear_has_value();
  }
}
inline double AreaValue::_internal_square_meters() const {
  if (_internal_has_square_meters()) {
    return value_.square_meters_;
  }
  return 0;
}
inline void AreaValue::_internal_set_square_meters(double value) {
  if (!_internal_has_square_meters()) {
    clear_value();
    set_has_square_meters();
  }
  value_.square_meters_ = value;
}
inline double AreaValue::square_meters() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.AreaValue.square_meters)
  return _internal_square_meters();
}
inline void AreaValue::set_square_meters(double value) {
  _internal_set_square_meters(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.AreaValue.square_meters)
}

inline bool AreaValue::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void AreaValue::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline AreaValue::ValueCase AreaValue::value_case() const {
  return AreaValue::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// DurationValue

// uint64 milliseconds = 1;
inline bool DurationValue::_internal_has_milliseconds() const {
  return value_case() == kMilliseconds;
}
inline bool DurationValue::has_milliseconds() const {
  return _internal_has_milliseconds();
}
inline void DurationValue::set_has_milliseconds() {
  _oneof_case_[0] = kMilliseconds;
}
inline void DurationValue::clear_milliseconds() {
  if (_internal_has_milliseconds()) {
    value_.milliseconds_ = uint64_t{0u};
    clear_has_value();
  }
}
inline uint64_t DurationValue::_internal_milliseconds() const {
  if (_internal_has_milliseconds()) {
    return value_.milliseconds_;
  }
  return uint64_t{0u};
}
inline void DurationValue::_internal_set_milliseconds(uint64_t value) {
  if (!_internal_has_milliseconds()) {
    clear_value();
    set_has_milliseconds();
  }
  value_.milliseconds_ = value;
}
inline uint64_t DurationValue::milliseconds() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DurationValue.milliseconds)
  return _internal_milliseconds();
}
inline void DurationValue::set_milliseconds(uint64_t value) {
  _internal_set_milliseconds(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DurationValue.milliseconds)
}

// uint64 seconds = 2;
inline bool DurationValue::_internal_has_seconds() const {
  return value_case() == kSeconds;
}
inline bool DurationValue::has_seconds() const {
  return _internal_has_seconds();
}
inline void DurationValue::set_has_seconds() {
  _oneof_case_[0] = kSeconds;
}
inline void DurationValue::clear_seconds() {
  if (_internal_has_seconds()) {
    value_.seconds_ = uint64_t{0u};
    clear_has_value();
  }
}
inline uint64_t DurationValue::_internal_seconds() const {
  if (_internal_has_seconds()) {
    return value_.seconds_;
  }
  return uint64_t{0u};
}
inline void DurationValue::_internal_set_seconds(uint64_t value) {
  if (!_internal_has_seconds()) {
    clear_value();
    set_has_seconds();
  }
  value_.seconds_ = value;
}
inline uint64_t DurationValue::seconds() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DurationValue.seconds)
  return _internal_seconds();
}
inline void DurationValue::set_seconds(uint64_t value) {
  _internal_set_seconds(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DurationValue.seconds)
}

// uint64 minutes = 3;
inline bool DurationValue::_internal_has_minutes() const {
  return value_case() == kMinutes;
}
inline bool DurationValue::has_minutes() const {
  return _internal_has_minutes();
}
inline void DurationValue::set_has_minutes() {
  _oneof_case_[0] = kMinutes;
}
inline void DurationValue::clear_minutes() {
  if (_internal_has_minutes()) {
    value_.minutes_ = uint64_t{0u};
    clear_has_value();
  }
}
inline uint64_t DurationValue::_internal_minutes() const {
  if (_internal_has_minutes()) {
    return value_.minutes_;
  }
  return uint64_t{0u};
}
inline void DurationValue::_internal_set_minutes(uint64_t value) {
  if (!_internal_has_minutes()) {
    clear_value();
    set_has_minutes();
  }
  value_.minutes_ = value;
}
inline uint64_t DurationValue::minutes() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DurationValue.minutes)
  return _internal_minutes();
}
inline void DurationValue::set_minutes(uint64_t value) {
  _internal_set_minutes(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DurationValue.minutes)
}

// uint64 hours = 4;
inline bool DurationValue::_internal_has_hours() const {
  return value_case() == kHours;
}
inline bool DurationValue::has_hours() const {
  return _internal_has_hours();
}
inline void DurationValue::set_has_hours() {
  _oneof_case_[0] = kHours;
}
inline void DurationValue::clear_hours() {
  if (_internal_has_hours()) {
    value_.hours_ = uint64_t{0u};
    clear_has_value();
  }
}
inline uint64_t DurationValue::_internal_hours() const {
  if (_internal_has_hours()) {
    return value_.hours_;
  }
  return uint64_t{0u};
}
inline void DurationValue::_internal_set_hours(uint64_t value) {
  if (!_internal_has_hours()) {
    clear_value();
    set_has_hours();
  }
  value_.hours_ = value;
}
inline uint64_t DurationValue::hours() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DurationValue.hours)
  return _internal_hours();
}
inline void DurationValue::set_hours(uint64_t value) {
  _internal_set_hours(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DurationValue.hours)
}

inline bool DurationValue::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void DurationValue::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline DurationValue::ValueCase DurationValue::value_case() const {
  return DurationValue::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// DistanceValue

// double millimeters = 1;
inline bool DistanceValue::_internal_has_millimeters() const {
  return value_case() == kMillimeters;
}
inline bool DistanceValue::has_millimeters() const {
  return _internal_has_millimeters();
}
inline void DistanceValue::set_has_millimeters() {
  _oneof_case_[0] = kMillimeters;
}
inline void DistanceValue::clear_millimeters() {
  if (_internal_has_millimeters()) {
    value_.millimeters_ = 0;
    clear_has_value();
  }
}
inline double DistanceValue::_internal_millimeters() const {
  if (_internal_has_millimeters()) {
    return value_.millimeters_;
  }
  return 0;
}
inline void DistanceValue::_internal_set_millimeters(double value) {
  if (!_internal_has_millimeters()) {
    clear_value();
    set_has_millimeters();
  }
  value_.millimeters_ = value;
}
inline double DistanceValue::millimeters() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DistanceValue.millimeters)
  return _internal_millimeters();
}
inline void DistanceValue::set_millimeters(double value) {
  _internal_set_millimeters(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DistanceValue.millimeters)
}

// double meters = 2;
inline bool DistanceValue::_internal_has_meters() const {
  return value_case() == kMeters;
}
inline bool DistanceValue::has_meters() const {
  return _internal_has_meters();
}
inline void DistanceValue::set_has_meters() {
  _oneof_case_[0] = kMeters;
}
inline void DistanceValue::clear_meters() {
  if (_internal_has_meters()) {
    value_.meters_ = 0;
    clear_has_value();
  }
}
inline double DistanceValue::_internal_meters() const {
  if (_internal_has_meters()) {
    return value_.meters_;
  }
  return 0;
}
inline void DistanceValue::_internal_set_meters(double value) {
  if (!_internal_has_meters()) {
    clear_value();
    set_has_meters();
  }
  value_.meters_ = value;
}
inline double DistanceValue::meters() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DistanceValue.meters)
  return _internal_meters();
}
inline void DistanceValue::set_meters(double value) {
  _internal_set_meters(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DistanceValue.meters)
}

// double kilometers = 3;
inline bool DistanceValue::_internal_has_kilometers() const {
  return value_case() == kKilometers;
}
inline bool DistanceValue::has_kilometers() const {
  return _internal_has_kilometers();
}
inline void DistanceValue::set_has_kilometers() {
  _oneof_case_[0] = kKilometers;
}
inline void DistanceValue::clear_kilometers() {
  if (_internal_has_kilometers()) {
    value_.kilometers_ = 0;
    clear_has_value();
  }
}
inline double DistanceValue::_internal_kilometers() const {
  if (_internal_has_kilometers()) {
    return value_.kilometers_;
  }
  return 0;
}
inline void DistanceValue::_internal_set_kilometers(double value) {
  if (!_internal_has_kilometers()) {
    clear_value();
    set_has_kilometers();
  }
  value_.kilometers_ = value;
}
inline double DistanceValue::kilometers() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DistanceValue.kilometers)
  return _internal_kilometers();
}
inline void DistanceValue::set_kilometers(double value) {
  _internal_set_kilometers(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DistanceValue.kilometers)
}

// double inches = 4;
inline bool DistanceValue::_internal_has_inches() const {
  return value_case() == kInches;
}
inline bool DistanceValue::has_inches() const {
  return _internal_has_inches();
}
inline void DistanceValue::set_has_inches() {
  _oneof_case_[0] = kInches;
}
inline void DistanceValue::clear_inches() {
  if (_internal_has_inches()) {
    value_.inches_ = 0;
    clear_has_value();
  }
}
inline double DistanceValue::_internal_inches() const {
  if (_internal_has_inches()) {
    return value_.inches_;
  }
  return 0;
}
inline void DistanceValue::_internal_set_inches(double value) {
  if (!_internal_has_inches()) {
    clear_value();
    set_has_inches();
  }
  value_.inches_ = value;
}
inline double DistanceValue::inches() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DistanceValue.inches)
  return _internal_inches();
}
inline void DistanceValue::set_inches(double value) {
  _internal_set_inches(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DistanceValue.inches)
}

// double feet = 5;
inline bool DistanceValue::_internal_has_feet() const {
  return value_case() == kFeet;
}
inline bool DistanceValue::has_feet() const {
  return _internal_has_feet();
}
inline void DistanceValue::set_has_feet() {
  _oneof_case_[0] = kFeet;
}
inline void DistanceValue::clear_feet() {
  if (_internal_has_feet()) {
    value_.feet_ = 0;
    clear_has_value();
  }
}
inline double DistanceValue::_internal_feet() const {
  if (_internal_has_feet()) {
    return value_.feet_;
  }
  return 0;
}
inline void DistanceValue::_internal_set_feet(double value) {
  if (!_internal_has_feet()) {
    clear_value();
    set_has_feet();
  }
  value_.feet_ = value;
}
inline double DistanceValue::feet() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DistanceValue.feet)
  return _internal_feet();
}
inline void DistanceValue::set_feet(double value) {
  _internal_set_feet(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DistanceValue.feet)
}

// double miles = 6;
inline bool DistanceValue::_internal_has_miles() const {
  return value_case() == kMiles;
}
inline bool DistanceValue::has_miles() const {
  return _internal_has_miles();
}
inline void DistanceValue::set_has_miles() {
  _oneof_case_[0] = kMiles;
}
inline void DistanceValue::clear_miles() {
  if (_internal_has_miles()) {
    value_.miles_ = 0;
    clear_has_value();
  }
}
inline double DistanceValue::_internal_miles() const {
  if (_internal_has_miles()) {
    return value_.miles_;
  }
  return 0;
}
inline void DistanceValue::_internal_set_miles(double value) {
  if (!_internal_has_miles()) {
    clear_value();
    set_has_miles();
  }
  value_.miles_ = value;
}
inline double DistanceValue::miles() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DistanceValue.miles)
  return _internal_miles();
}
inline void DistanceValue::set_miles(double value) {
  _internal_set_miles(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DistanceValue.miles)
}

// double centimeters = 7;
inline bool DistanceValue::_internal_has_centimeters() const {
  return value_case() == kCentimeters;
}
inline bool DistanceValue::has_centimeters() const {
  return _internal_has_centimeters();
}
inline void DistanceValue::set_has_centimeters() {
  _oneof_case_[0] = kCentimeters;
}
inline void DistanceValue::clear_centimeters() {
  if (_internal_has_centimeters()) {
    value_.centimeters_ = 0;
    clear_has_value();
  }
}
inline double DistanceValue::_internal_centimeters() const {
  if (_internal_has_centimeters()) {
    return value_.centimeters_;
  }
  return 0;
}
inline void DistanceValue::_internal_set_centimeters(double value) {
  if (!_internal_has_centimeters()) {
    clear_value();
    set_has_centimeters();
  }
  value_.centimeters_ = value;
}
inline double DistanceValue::centimeters() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.DistanceValue.centimeters)
  return _internal_centimeters();
}
inline void DistanceValue::set_centimeters(double value) {
  _internal_set_centimeters(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.DistanceValue.centimeters)
}

inline bool DistanceValue::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void DistanceValue::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline DistanceValue::ValueCase DistanceValue::value_case() const {
  return DistanceValue::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// SpeedValue

// double kilometers_per_hour = 1;
inline bool SpeedValue::_internal_has_kilometers_per_hour() const {
  return value_case() == kKilometersPerHour;
}
inline bool SpeedValue::has_kilometers_per_hour() const {
  return _internal_has_kilometers_per_hour();
}
inline void SpeedValue::set_has_kilometers_per_hour() {
  _oneof_case_[0] = kKilometersPerHour;
}
inline void SpeedValue::clear_kilometers_per_hour() {
  if (_internal_has_kilometers_per_hour()) {
    value_.kilometers_per_hour_ = 0;
    clear_has_value();
  }
}
inline double SpeedValue::_internal_kilometers_per_hour() const {
  if (_internal_has_kilometers_per_hour()) {
    return value_.kilometers_per_hour_;
  }
  return 0;
}
inline void SpeedValue::_internal_set_kilometers_per_hour(double value) {
  if (!_internal_has_kilometers_per_hour()) {
    clear_value();
    set_has_kilometers_per_hour();
  }
  value_.kilometers_per_hour_ = value;
}
inline double SpeedValue::kilometers_per_hour() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.SpeedValue.kilometers_per_hour)
  return _internal_kilometers_per_hour();
}
inline void SpeedValue::set_kilometers_per_hour(double value) {
  _internal_set_kilometers_per_hour(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.SpeedValue.kilometers_per_hour)
}

// double miles_per_hour = 2;
inline bool SpeedValue::_internal_has_miles_per_hour() const {
  return value_case() == kMilesPerHour;
}
inline bool SpeedValue::has_miles_per_hour() const {
  return _internal_has_miles_per_hour();
}
inline void SpeedValue::set_has_miles_per_hour() {
  _oneof_case_[0] = kMilesPerHour;
}
inline void SpeedValue::clear_miles_per_hour() {
  if (_internal_has_miles_per_hour()) {
    value_.miles_per_hour_ = 0;
    clear_has_value();
  }
}
inline double SpeedValue::_internal_miles_per_hour() const {
  if (_internal_has_miles_per_hour()) {
    return value_.miles_per_hour_;
  }
  return 0;
}
inline void SpeedValue::_internal_set_miles_per_hour(double value) {
  if (!_internal_has_miles_per_hour()) {
    clear_value();
    set_has_miles_per_hour();
  }
  value_.miles_per_hour_ = value;
}
inline double SpeedValue::miles_per_hour() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.SpeedValue.miles_per_hour)
  return _internal_miles_per_hour();
}
inline void SpeedValue::set_miles_per_hour(double value) {
  _internal_set_miles_per_hour(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.SpeedValue.miles_per_hour)
}

inline bool SpeedValue::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void SpeedValue::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline SpeedValue::ValueCase SpeedValue::value_case() const {
  return SpeedValue::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// TranslationParameter

// string name = 1;
inline void TranslationParameter::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& TranslationParameter::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TranslationParameter::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.translation.TranslationParameter.name)
}
inline std::string* TranslationParameter::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.name)
  return _s;
}
inline const std::string& TranslationParameter::_internal_name() const {
  return name_.Get();
}
inline void TranslationParameter::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TranslationParameter::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TranslationParameter::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TranslationParameter::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.name)
}

// .carbon.frontend.translation.IntegerValue int_value = 2;
inline bool TranslationParameter::_internal_has_int_value() const {
  return value_case() == kIntValue;
}
inline bool TranslationParameter::has_int_value() const {
  return _internal_has_int_value();
}
inline void TranslationParameter::set_has_int_value() {
  _oneof_case_[0] = kIntValue;
}
inline void TranslationParameter::clear_int_value() {
  if (_internal_has_int_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.int_value_;
    }
    clear_has_value();
  }
}
inline ::carbon::frontend::translation::IntegerValue* TranslationParameter::release_int_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.int_value)
  if (_internal_has_int_value()) {
    clear_has_value();
      ::carbon::frontend::translation::IntegerValue* temp = value_.int_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.int_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::IntegerValue& TranslationParameter::_internal_int_value() const {
  return _internal_has_int_value()
      ? *value_.int_value_
      : reinterpret_cast< ::carbon::frontend::translation::IntegerValue&>(::carbon::frontend::translation::_IntegerValue_default_instance_);
}
inline const ::carbon::frontend::translation::IntegerValue& TranslationParameter::int_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.int_value)
  return _internal_int_value();
}
inline ::carbon::frontend::translation::IntegerValue* TranslationParameter::unsafe_arena_release_int_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.translation.TranslationParameter.int_value)
  if (_internal_has_int_value()) {
    clear_has_value();
    ::carbon::frontend::translation::IntegerValue* temp = value_.int_value_;
    value_.int_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslationParameter::unsafe_arena_set_allocated_int_value(::carbon::frontend::translation::IntegerValue* int_value) {
  clear_value();
  if (int_value) {
    set_has_int_value();
    value_.int_value_ = int_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.translation.TranslationParameter.int_value)
}
inline ::carbon::frontend::translation::IntegerValue* TranslationParameter::_internal_mutable_int_value() {
  if (!_internal_has_int_value()) {
    clear_value();
    set_has_int_value();
    value_.int_value_ = CreateMaybeMessage< ::carbon::frontend::translation::IntegerValue >(GetArenaForAllocation());
  }
  return value_.int_value_;
}
inline ::carbon::frontend::translation::IntegerValue* TranslationParameter::mutable_int_value() {
  ::carbon::frontend::translation::IntegerValue* _msg = _internal_mutable_int_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.int_value)
  return _msg;
}

// .carbon.frontend.translation.DoubleValue double_value = 3;
inline bool TranslationParameter::_internal_has_double_value() const {
  return value_case() == kDoubleValue;
}
inline bool TranslationParameter::has_double_value() const {
  return _internal_has_double_value();
}
inline void TranslationParameter::set_has_double_value() {
  _oneof_case_[0] = kDoubleValue;
}
inline void TranslationParameter::clear_double_value() {
  if (_internal_has_double_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.double_value_;
    }
    clear_has_value();
  }
}
inline ::carbon::frontend::translation::DoubleValue* TranslationParameter::release_double_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.double_value)
  if (_internal_has_double_value()) {
    clear_has_value();
      ::carbon::frontend::translation::DoubleValue* temp = value_.double_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.double_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::DoubleValue& TranslationParameter::_internal_double_value() const {
  return _internal_has_double_value()
      ? *value_.double_value_
      : reinterpret_cast< ::carbon::frontend::translation::DoubleValue&>(::carbon::frontend::translation::_DoubleValue_default_instance_);
}
inline const ::carbon::frontend::translation::DoubleValue& TranslationParameter::double_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.double_value)
  return _internal_double_value();
}
inline ::carbon::frontend::translation::DoubleValue* TranslationParameter::unsafe_arena_release_double_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.translation.TranslationParameter.double_value)
  if (_internal_has_double_value()) {
    clear_has_value();
    ::carbon::frontend::translation::DoubleValue* temp = value_.double_value_;
    value_.double_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslationParameter::unsafe_arena_set_allocated_double_value(::carbon::frontend::translation::DoubleValue* double_value) {
  clear_value();
  if (double_value) {
    set_has_double_value();
    value_.double_value_ = double_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.translation.TranslationParameter.double_value)
}
inline ::carbon::frontend::translation::DoubleValue* TranslationParameter::_internal_mutable_double_value() {
  if (!_internal_has_double_value()) {
    clear_value();
    set_has_double_value();
    value_.double_value_ = CreateMaybeMessage< ::carbon::frontend::translation::DoubleValue >(GetArenaForAllocation());
  }
  return value_.double_value_;
}
inline ::carbon::frontend::translation::DoubleValue* TranslationParameter::mutable_double_value() {
  ::carbon::frontend::translation::DoubleValue* _msg = _internal_mutable_double_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.double_value)
  return _msg;
}

// .carbon.frontend.translation.StringValue string_value = 4;
inline bool TranslationParameter::_internal_has_string_value() const {
  return value_case() == kStringValue;
}
inline bool TranslationParameter::has_string_value() const {
  return _internal_has_string_value();
}
inline void TranslationParameter::set_has_string_value() {
  _oneof_case_[0] = kStringValue;
}
inline void TranslationParameter::clear_string_value() {
  if (_internal_has_string_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.string_value_;
    }
    clear_has_value();
  }
}
inline ::carbon::frontend::translation::StringValue* TranslationParameter::release_string_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.string_value)
  if (_internal_has_string_value()) {
    clear_has_value();
      ::carbon::frontend::translation::StringValue* temp = value_.string_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.string_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::StringValue& TranslationParameter::_internal_string_value() const {
  return _internal_has_string_value()
      ? *value_.string_value_
      : reinterpret_cast< ::carbon::frontend::translation::StringValue&>(::carbon::frontend::translation::_StringValue_default_instance_);
}
inline const ::carbon::frontend::translation::StringValue& TranslationParameter::string_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.string_value)
  return _internal_string_value();
}
inline ::carbon::frontend::translation::StringValue* TranslationParameter::unsafe_arena_release_string_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.translation.TranslationParameter.string_value)
  if (_internal_has_string_value()) {
    clear_has_value();
    ::carbon::frontend::translation::StringValue* temp = value_.string_value_;
    value_.string_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslationParameter::unsafe_arena_set_allocated_string_value(::carbon::frontend::translation::StringValue* string_value) {
  clear_value();
  if (string_value) {
    set_has_string_value();
    value_.string_value_ = string_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.translation.TranslationParameter.string_value)
}
inline ::carbon::frontend::translation::StringValue* TranslationParameter::_internal_mutable_string_value() {
  if (!_internal_has_string_value()) {
    clear_value();
    set_has_string_value();
    value_.string_value_ = CreateMaybeMessage< ::carbon::frontend::translation::StringValue >(GetArenaForAllocation());
  }
  return value_.string_value_;
}
inline ::carbon::frontend::translation::StringValue* TranslationParameter::mutable_string_value() {
  ::carbon::frontend::translation::StringValue* _msg = _internal_mutable_string_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.string_value)
  return _msg;
}

// .carbon.frontend.translation.TemperatureValue temperature_value = 5;
inline bool TranslationParameter::_internal_has_temperature_value() const {
  return value_case() == kTemperatureValue;
}
inline bool TranslationParameter::has_temperature_value() const {
  return _internal_has_temperature_value();
}
inline void TranslationParameter::set_has_temperature_value() {
  _oneof_case_[0] = kTemperatureValue;
}
inline void TranslationParameter::clear_temperature_value() {
  if (_internal_has_temperature_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.temperature_value_;
    }
    clear_has_value();
  }
}
inline ::carbon::frontend::translation::TemperatureValue* TranslationParameter::release_temperature_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.temperature_value)
  if (_internal_has_temperature_value()) {
    clear_has_value();
      ::carbon::frontend::translation::TemperatureValue* temp = value_.temperature_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.temperature_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::TemperatureValue& TranslationParameter::_internal_temperature_value() const {
  return _internal_has_temperature_value()
      ? *value_.temperature_value_
      : reinterpret_cast< ::carbon::frontend::translation::TemperatureValue&>(::carbon::frontend::translation::_TemperatureValue_default_instance_);
}
inline const ::carbon::frontend::translation::TemperatureValue& TranslationParameter::temperature_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.temperature_value)
  return _internal_temperature_value();
}
inline ::carbon::frontend::translation::TemperatureValue* TranslationParameter::unsafe_arena_release_temperature_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.translation.TranslationParameter.temperature_value)
  if (_internal_has_temperature_value()) {
    clear_has_value();
    ::carbon::frontend::translation::TemperatureValue* temp = value_.temperature_value_;
    value_.temperature_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslationParameter::unsafe_arena_set_allocated_temperature_value(::carbon::frontend::translation::TemperatureValue* temperature_value) {
  clear_value();
  if (temperature_value) {
    set_has_temperature_value();
    value_.temperature_value_ = temperature_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.translation.TranslationParameter.temperature_value)
}
inline ::carbon::frontend::translation::TemperatureValue* TranslationParameter::_internal_mutable_temperature_value() {
  if (!_internal_has_temperature_value()) {
    clear_value();
    set_has_temperature_value();
    value_.temperature_value_ = CreateMaybeMessage< ::carbon::frontend::translation::TemperatureValue >(GetArenaForAllocation());
  }
  return value_.temperature_value_;
}
inline ::carbon::frontend::translation::TemperatureValue* TranslationParameter::mutable_temperature_value() {
  ::carbon::frontend::translation::TemperatureValue* _msg = _internal_mutable_temperature_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.temperature_value)
  return _msg;
}

// .carbon.frontend.translation.PercentValue percent_value = 6;
inline bool TranslationParameter::_internal_has_percent_value() const {
  return value_case() == kPercentValue;
}
inline bool TranslationParameter::has_percent_value() const {
  return _internal_has_percent_value();
}
inline void TranslationParameter::set_has_percent_value() {
  _oneof_case_[0] = kPercentValue;
}
inline void TranslationParameter::clear_percent_value() {
  if (_internal_has_percent_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.percent_value_;
    }
    clear_has_value();
  }
}
inline ::carbon::frontend::translation::PercentValue* TranslationParameter::release_percent_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.percent_value)
  if (_internal_has_percent_value()) {
    clear_has_value();
      ::carbon::frontend::translation::PercentValue* temp = value_.percent_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.percent_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::PercentValue& TranslationParameter::_internal_percent_value() const {
  return _internal_has_percent_value()
      ? *value_.percent_value_
      : reinterpret_cast< ::carbon::frontend::translation::PercentValue&>(::carbon::frontend::translation::_PercentValue_default_instance_);
}
inline const ::carbon::frontend::translation::PercentValue& TranslationParameter::percent_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.percent_value)
  return _internal_percent_value();
}
inline ::carbon::frontend::translation::PercentValue* TranslationParameter::unsafe_arena_release_percent_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.translation.TranslationParameter.percent_value)
  if (_internal_has_percent_value()) {
    clear_has_value();
    ::carbon::frontend::translation::PercentValue* temp = value_.percent_value_;
    value_.percent_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslationParameter::unsafe_arena_set_allocated_percent_value(::carbon::frontend::translation::PercentValue* percent_value) {
  clear_value();
  if (percent_value) {
    set_has_percent_value();
    value_.percent_value_ = percent_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.translation.TranslationParameter.percent_value)
}
inline ::carbon::frontend::translation::PercentValue* TranslationParameter::_internal_mutable_percent_value() {
  if (!_internal_has_percent_value()) {
    clear_value();
    set_has_percent_value();
    value_.percent_value_ = CreateMaybeMessage< ::carbon::frontend::translation::PercentValue >(GetArenaForAllocation());
  }
  return value_.percent_value_;
}
inline ::carbon::frontend::translation::PercentValue* TranslationParameter::mutable_percent_value() {
  ::carbon::frontend::translation::PercentValue* _msg = _internal_mutable_percent_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.percent_value)
  return _msg;
}

// .carbon.frontend.translation.VoltageValue voltage_value = 7;
inline bool TranslationParameter::_internal_has_voltage_value() const {
  return value_case() == kVoltageValue;
}
inline bool TranslationParameter::has_voltage_value() const {
  return _internal_has_voltage_value();
}
inline void TranslationParameter::set_has_voltage_value() {
  _oneof_case_[0] = kVoltageValue;
}
inline void TranslationParameter::clear_voltage_value() {
  if (_internal_has_voltage_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.voltage_value_;
    }
    clear_has_value();
  }
}
inline ::carbon::frontend::translation::VoltageValue* TranslationParameter::release_voltage_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.voltage_value)
  if (_internal_has_voltage_value()) {
    clear_has_value();
      ::carbon::frontend::translation::VoltageValue* temp = value_.voltage_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.voltage_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::VoltageValue& TranslationParameter::_internal_voltage_value() const {
  return _internal_has_voltage_value()
      ? *value_.voltage_value_
      : reinterpret_cast< ::carbon::frontend::translation::VoltageValue&>(::carbon::frontend::translation::_VoltageValue_default_instance_);
}
inline const ::carbon::frontend::translation::VoltageValue& TranslationParameter::voltage_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.voltage_value)
  return _internal_voltage_value();
}
inline ::carbon::frontend::translation::VoltageValue* TranslationParameter::unsafe_arena_release_voltage_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.translation.TranslationParameter.voltage_value)
  if (_internal_has_voltage_value()) {
    clear_has_value();
    ::carbon::frontend::translation::VoltageValue* temp = value_.voltage_value_;
    value_.voltage_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslationParameter::unsafe_arena_set_allocated_voltage_value(::carbon::frontend::translation::VoltageValue* voltage_value) {
  clear_value();
  if (voltage_value) {
    set_has_voltage_value();
    value_.voltage_value_ = voltage_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.translation.TranslationParameter.voltage_value)
}
inline ::carbon::frontend::translation::VoltageValue* TranslationParameter::_internal_mutable_voltage_value() {
  if (!_internal_has_voltage_value()) {
    clear_value();
    set_has_voltage_value();
    value_.voltage_value_ = CreateMaybeMessage< ::carbon::frontend::translation::VoltageValue >(GetArenaForAllocation());
  }
  return value_.voltage_value_;
}
inline ::carbon::frontend::translation::VoltageValue* TranslationParameter::mutable_voltage_value() {
  ::carbon::frontend::translation::VoltageValue* _msg = _internal_mutable_voltage_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.voltage_value)
  return _msg;
}

// .carbon.frontend.translation.FrequencyValue frequency_value = 8;
inline bool TranslationParameter::_internal_has_frequency_value() const {
  return value_case() == kFrequencyValue;
}
inline bool TranslationParameter::has_frequency_value() const {
  return _internal_has_frequency_value();
}
inline void TranslationParameter::set_has_frequency_value() {
  _oneof_case_[0] = kFrequencyValue;
}
inline void TranslationParameter::clear_frequency_value() {
  if (_internal_has_frequency_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.frequency_value_;
    }
    clear_has_value();
  }
}
inline ::carbon::frontend::translation::FrequencyValue* TranslationParameter::release_frequency_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.frequency_value)
  if (_internal_has_frequency_value()) {
    clear_has_value();
      ::carbon::frontend::translation::FrequencyValue* temp = value_.frequency_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.frequency_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::FrequencyValue& TranslationParameter::_internal_frequency_value() const {
  return _internal_has_frequency_value()
      ? *value_.frequency_value_
      : reinterpret_cast< ::carbon::frontend::translation::FrequencyValue&>(::carbon::frontend::translation::_FrequencyValue_default_instance_);
}
inline const ::carbon::frontend::translation::FrequencyValue& TranslationParameter::frequency_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.frequency_value)
  return _internal_frequency_value();
}
inline ::carbon::frontend::translation::FrequencyValue* TranslationParameter::unsafe_arena_release_frequency_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.translation.TranslationParameter.frequency_value)
  if (_internal_has_frequency_value()) {
    clear_has_value();
    ::carbon::frontend::translation::FrequencyValue* temp = value_.frequency_value_;
    value_.frequency_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslationParameter::unsafe_arena_set_allocated_frequency_value(::carbon::frontend::translation::FrequencyValue* frequency_value) {
  clear_value();
  if (frequency_value) {
    set_has_frequency_value();
    value_.frequency_value_ = frequency_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.translation.TranslationParameter.frequency_value)
}
inline ::carbon::frontend::translation::FrequencyValue* TranslationParameter::_internal_mutable_frequency_value() {
  if (!_internal_has_frequency_value()) {
    clear_value();
    set_has_frequency_value();
    value_.frequency_value_ = CreateMaybeMessage< ::carbon::frontend::translation::FrequencyValue >(GetArenaForAllocation());
  }
  return value_.frequency_value_;
}
inline ::carbon::frontend::translation::FrequencyValue* TranslationParameter::mutable_frequency_value() {
  ::carbon::frontend::translation::FrequencyValue* _msg = _internal_mutable_frequency_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.frequency_value)
  return _msg;
}

// .carbon.frontend.translation.AreaValue area_value = 9;
inline bool TranslationParameter::_internal_has_area_value() const {
  return value_case() == kAreaValue;
}
inline bool TranslationParameter::has_area_value() const {
  return _internal_has_area_value();
}
inline void TranslationParameter::set_has_area_value() {
  _oneof_case_[0] = kAreaValue;
}
inline void TranslationParameter::clear_area_value() {
  if (_internal_has_area_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.area_value_;
    }
    clear_has_value();
  }
}
inline ::carbon::frontend::translation::AreaValue* TranslationParameter::release_area_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.area_value)
  if (_internal_has_area_value()) {
    clear_has_value();
      ::carbon::frontend::translation::AreaValue* temp = value_.area_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.area_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::AreaValue& TranslationParameter::_internal_area_value() const {
  return _internal_has_area_value()
      ? *value_.area_value_
      : reinterpret_cast< ::carbon::frontend::translation::AreaValue&>(::carbon::frontend::translation::_AreaValue_default_instance_);
}
inline const ::carbon::frontend::translation::AreaValue& TranslationParameter::area_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.area_value)
  return _internal_area_value();
}
inline ::carbon::frontend::translation::AreaValue* TranslationParameter::unsafe_arena_release_area_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.translation.TranslationParameter.area_value)
  if (_internal_has_area_value()) {
    clear_has_value();
    ::carbon::frontend::translation::AreaValue* temp = value_.area_value_;
    value_.area_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslationParameter::unsafe_arena_set_allocated_area_value(::carbon::frontend::translation::AreaValue* area_value) {
  clear_value();
  if (area_value) {
    set_has_area_value();
    value_.area_value_ = area_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.translation.TranslationParameter.area_value)
}
inline ::carbon::frontend::translation::AreaValue* TranslationParameter::_internal_mutable_area_value() {
  if (!_internal_has_area_value()) {
    clear_value();
    set_has_area_value();
    value_.area_value_ = CreateMaybeMessage< ::carbon::frontend::translation::AreaValue >(GetArenaForAllocation());
  }
  return value_.area_value_;
}
inline ::carbon::frontend::translation::AreaValue* TranslationParameter::mutable_area_value() {
  ::carbon::frontend::translation::AreaValue* _msg = _internal_mutable_area_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.area_value)
  return _msg;
}

// .carbon.frontend.translation.DurationValue duration_value = 10;
inline bool TranslationParameter::_internal_has_duration_value() const {
  return value_case() == kDurationValue;
}
inline bool TranslationParameter::has_duration_value() const {
  return _internal_has_duration_value();
}
inline void TranslationParameter::set_has_duration_value() {
  _oneof_case_[0] = kDurationValue;
}
inline void TranslationParameter::clear_duration_value() {
  if (_internal_has_duration_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.duration_value_;
    }
    clear_has_value();
  }
}
inline ::carbon::frontend::translation::DurationValue* TranslationParameter::release_duration_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.duration_value)
  if (_internal_has_duration_value()) {
    clear_has_value();
      ::carbon::frontend::translation::DurationValue* temp = value_.duration_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.duration_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::DurationValue& TranslationParameter::_internal_duration_value() const {
  return _internal_has_duration_value()
      ? *value_.duration_value_
      : reinterpret_cast< ::carbon::frontend::translation::DurationValue&>(::carbon::frontend::translation::_DurationValue_default_instance_);
}
inline const ::carbon::frontend::translation::DurationValue& TranslationParameter::duration_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.duration_value)
  return _internal_duration_value();
}
inline ::carbon::frontend::translation::DurationValue* TranslationParameter::unsafe_arena_release_duration_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.translation.TranslationParameter.duration_value)
  if (_internal_has_duration_value()) {
    clear_has_value();
    ::carbon::frontend::translation::DurationValue* temp = value_.duration_value_;
    value_.duration_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslationParameter::unsafe_arena_set_allocated_duration_value(::carbon::frontend::translation::DurationValue* duration_value) {
  clear_value();
  if (duration_value) {
    set_has_duration_value();
    value_.duration_value_ = duration_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.translation.TranslationParameter.duration_value)
}
inline ::carbon::frontend::translation::DurationValue* TranslationParameter::_internal_mutable_duration_value() {
  if (!_internal_has_duration_value()) {
    clear_value();
    set_has_duration_value();
    value_.duration_value_ = CreateMaybeMessage< ::carbon::frontend::translation::DurationValue >(GetArenaForAllocation());
  }
  return value_.duration_value_;
}
inline ::carbon::frontend::translation::DurationValue* TranslationParameter::mutable_duration_value() {
  ::carbon::frontend::translation::DurationValue* _msg = _internal_mutable_duration_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.duration_value)
  return _msg;
}

// .carbon.frontend.translation.DistanceValue distance_value = 11;
inline bool TranslationParameter::_internal_has_distance_value() const {
  return value_case() == kDistanceValue;
}
inline bool TranslationParameter::has_distance_value() const {
  return _internal_has_distance_value();
}
inline void TranslationParameter::set_has_distance_value() {
  _oneof_case_[0] = kDistanceValue;
}
inline void TranslationParameter::clear_distance_value() {
  if (_internal_has_distance_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.distance_value_;
    }
    clear_has_value();
  }
}
inline ::carbon::frontend::translation::DistanceValue* TranslationParameter::release_distance_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.distance_value)
  if (_internal_has_distance_value()) {
    clear_has_value();
      ::carbon::frontend::translation::DistanceValue* temp = value_.distance_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.distance_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::DistanceValue& TranslationParameter::_internal_distance_value() const {
  return _internal_has_distance_value()
      ? *value_.distance_value_
      : reinterpret_cast< ::carbon::frontend::translation::DistanceValue&>(::carbon::frontend::translation::_DistanceValue_default_instance_);
}
inline const ::carbon::frontend::translation::DistanceValue& TranslationParameter::distance_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.distance_value)
  return _internal_distance_value();
}
inline ::carbon::frontend::translation::DistanceValue* TranslationParameter::unsafe_arena_release_distance_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.translation.TranslationParameter.distance_value)
  if (_internal_has_distance_value()) {
    clear_has_value();
    ::carbon::frontend::translation::DistanceValue* temp = value_.distance_value_;
    value_.distance_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslationParameter::unsafe_arena_set_allocated_distance_value(::carbon::frontend::translation::DistanceValue* distance_value) {
  clear_value();
  if (distance_value) {
    set_has_distance_value();
    value_.distance_value_ = distance_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.translation.TranslationParameter.distance_value)
}
inline ::carbon::frontend::translation::DistanceValue* TranslationParameter::_internal_mutable_distance_value() {
  if (!_internal_has_distance_value()) {
    clear_value();
    set_has_distance_value();
    value_.distance_value_ = CreateMaybeMessage< ::carbon::frontend::translation::DistanceValue >(GetArenaForAllocation());
  }
  return value_.distance_value_;
}
inline ::carbon::frontend::translation::DistanceValue* TranslationParameter::mutable_distance_value() {
  ::carbon::frontend::translation::DistanceValue* _msg = _internal_mutable_distance_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.distance_value)
  return _msg;
}

// .carbon.frontend.translation.SpeedValue speed_value = 12;
inline bool TranslationParameter::_internal_has_speed_value() const {
  return value_case() == kSpeedValue;
}
inline bool TranslationParameter::has_speed_value() const {
  return _internal_has_speed_value();
}
inline void TranslationParameter::set_has_speed_value() {
  _oneof_case_[0] = kSpeedValue;
}
inline void TranslationParameter::clear_speed_value() {
  if (_internal_has_speed_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete value_.speed_value_;
    }
    clear_has_value();
  }
}
inline ::carbon::frontend::translation::SpeedValue* TranslationParameter::release_speed_value() {
  // @@protoc_insertion_point(field_release:carbon.frontend.translation.TranslationParameter.speed_value)
  if (_internal_has_speed_value()) {
    clear_has_value();
      ::carbon::frontend::translation::SpeedValue* temp = value_.speed_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    value_.speed_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::SpeedValue& TranslationParameter::_internal_speed_value() const {
  return _internal_has_speed_value()
      ? *value_.speed_value_
      : reinterpret_cast< ::carbon::frontend::translation::SpeedValue&>(::carbon::frontend::translation::_SpeedValue_default_instance_);
}
inline const ::carbon::frontend::translation::SpeedValue& TranslationParameter::speed_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.translation.TranslationParameter.speed_value)
  return _internal_speed_value();
}
inline ::carbon::frontend::translation::SpeedValue* TranslationParameter::unsafe_arena_release_speed_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.translation.TranslationParameter.speed_value)
  if (_internal_has_speed_value()) {
    clear_has_value();
    ::carbon::frontend::translation::SpeedValue* temp = value_.speed_value_;
    value_.speed_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslationParameter::unsafe_arena_set_allocated_speed_value(::carbon::frontend::translation::SpeedValue* speed_value) {
  clear_value();
  if (speed_value) {
    set_has_speed_value();
    value_.speed_value_ = speed_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.translation.TranslationParameter.speed_value)
}
inline ::carbon::frontend::translation::SpeedValue* TranslationParameter::_internal_mutable_speed_value() {
  if (!_internal_has_speed_value()) {
    clear_value();
    set_has_speed_value();
    value_.speed_value_ = CreateMaybeMessage< ::carbon::frontend::translation::SpeedValue >(GetArenaForAllocation());
  }
  return value_.speed_value_;
}
inline ::carbon::frontend::translation::SpeedValue* TranslationParameter::mutable_speed_value() {
  ::carbon::frontend::translation::SpeedValue* _msg = _internal_mutable_speed_value();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.translation.TranslationParameter.speed_value)
  return _msg;
}

inline bool TranslationParameter::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void TranslationParameter::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline TranslationParameter::ValueCase TranslationParameter::value_case() const {
  return TranslationParameter::ValueCase(_oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace translation
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ftranslation_2eproto

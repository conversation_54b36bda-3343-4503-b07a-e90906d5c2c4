# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/module.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/module.proto',
  package='carbon.frontend.module',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1b\x66rontend/proto/module.proto\x12\x16\x63\x61rbon.frontend.module\x1a\x19\x66rontend/proto/util.proto\",\n\x0eModuleIdentity\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0e\n\x06serial\x18\x02 \x01(\t\"H\n\x19GetNextModulesListRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"\x95\x02\n\x1aGetNextModulesListResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12@\n\x10\x61ssigned_modules\x18\x02 \x03(\x0b\x32&.carbon.frontend.module.ModuleIdentity\x12\x42\n\x12unassigned_modules\x18\x03 \x03(\x0b\x32&.carbon.frontend.module.ModuleIdentity\x12\x44\n\x14unset_serial_modules\x18\x04 \x03(\x0b\x32&.carbon.frontend.module.ModuleIdentity\"J\n\x1bGetNextActiveModulesRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"\x8b\x01\n\x1cGetNextActiveModulesResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12>\n\x0e\x61\x63tive_modules\x18\x02 \x03(\x0b\x32&.carbon.frontend.module.ModuleIdentity\"X\n\x15IdentifyModuleRequest\x12?\n\x0fmodule_identity\x18\x01 \x01(\x0b\x32&.carbon.frontend.module.ModuleIdentity\"V\n\x13\x41ssignModuleRequest\x12?\n\x0fmodule_identity\x18\x01 \x01(\x0b\x32&.carbon.frontend.module.ModuleIdentity\"_\n\x1c\x43learModuleAssignmentRequest\x12?\n\x0fmodule_identity\x18\x01 \x01(\x0b\x32&.carbon.frontend.module.ModuleIdentity\"m\n\x16SetModuleSerialRequest\x12?\n\x0fmodule_identity\x18\x01 \x01(\x0b\x32&.carbon.frontend.module.ModuleIdentity\x12\x12\n\nnew_serial\x18\x02 \x01(\t\"R\n\x10ModuleDefinition\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x19\n\x11module_spacing_mm\x18\x02 \x01(\x02\x12\x10\n\x08\x64isabled\x18\x03 \x01(\x08\"r\n\rRowDefinition\x12\x0e\n\x06row_id\x18\x01 \x01(\r\x12\x39\n\x07modules\x18\x02 \x03(\x0b\x32(.carbon.frontend.module.ModuleDefinition\x12\x16\n\x0erow_spacing_mm\x18\x03 \x01(\x02\"7\n\rBarDefinition\x12\x15\n\rbar_length_mm\x18\x01 \x01(\r\x12\x0f\n\x07\x66olding\x18\x02 \x01(\x08\"\x85\x01\n\x0fRobotDefinition\x12\x33\n\x04rows\x18\x01 \x03(\x0b\x32%.carbon.frontend.module.RowDefinition\x12=\n\x0e\x62\x61r_definition\x18\x03 \x01(\x0b\x32%.carbon.frontend.module.BarDefinition\"i\n\x06Preset\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12\x14\n\x0c\x64isplay_name\x18\x02 \x01(\t\x12;\n\ndefinition\x18\x03 \x01(\x0b\x32\'.carbon.frontend.module.RobotDefinition\")\n\x15GetPresetsListRequest\x12\x10\n\x08language\x18\x01 \x01(\t\"I\n\x16GetPresetsListResponse\x12/\n\x07presets\x18\x01 \x03(\x0b\x32\x1e.carbon.frontend.module.Preset\"\x84\x01\n!GetCurrentRobotDefinitionResponse\x12H\n\x12\x63urrent_definition\x18\x01 \x01(\x0b\x32\'.carbon.frontend.module.RobotDefinitionH\x00\x88\x01\x01\x42\x15\n\x13_current_definition\"g\n SetCurrentRobotDefinitionRequest\x12\x43\n\x12\x63urrent_definition\x18\x01 \x01(\x0b\x32\'.carbon.frontend.module.RobotDefinition2\xf8\x07\n\x17ModuleAssignmentService\x12{\n\x12GetNextModulesList\x12\x31.carbon.frontend.module.GetNextModulesListRequest\x1a\x32.carbon.frontend.module.GetNextModulesListResponse\x12\x81\x01\n\x14GetNextActiveModules\x12\x33.carbon.frontend.module.GetNextActiveModulesRequest\x1a\x34.carbon.frontend.module.GetNextActiveModulesResponse\x12\\\n\x0eIdentifyModule\x12-.carbon.frontend.module.IdentifyModuleRequest\x1a\x1b.carbon.frontend.util.Empty\x12X\n\x0c\x41ssignModule\x12+.carbon.frontend.module.AssignModuleRequest\x1a\x1b.carbon.frontend.util.Empty\x12j\n\x15\x43learModuleAssignment\x12\x34.carbon.frontend.module.ClearModuleAssignmentRequest\x1a\x1b.carbon.frontend.util.Empty\x12^\n\x0fSetModuleSerial\x12..carbon.frontend.module.SetModuleSerialRequest\x1a\x1b.carbon.frontend.util.Empty\x12o\n\x0eGetPresetsList\x12-.carbon.frontend.module.GetPresetsListRequest\x1a..carbon.frontend.module.GetPresetsListResponse\x12s\n\x19GetCurrentRobotDefinition\x12\x1b.carbon.frontend.util.Empty\x1a\x39.carbon.frontend.module.GetCurrentRobotDefinitionResponse\x12r\n\x19SetCurrentRobotDefinition\x12\x38.carbon.frontend.module.SetCurrentRobotDefinitionRequest\x1a\x1b.carbon.frontend.util.EmptyB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_MODULEIDENTITY = _descriptor.Descriptor(
  name='ModuleIdentity',
  full_name='carbon.frontend.module.ModuleIdentity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.module.ModuleIdentity.id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='serial', full_name='carbon.frontend.module.ModuleIdentity.serial', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=82,
  serialized_end=126,
)


_GETNEXTMODULESLISTREQUEST = _descriptor.Descriptor(
  name='GetNextModulesListRequest',
  full_name='carbon.frontend.module.GetNextModulesListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.module.GetNextModulesListRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=128,
  serialized_end=200,
)


_GETNEXTMODULESLISTRESPONSE = _descriptor.Descriptor(
  name='GetNextModulesListResponse',
  full_name='carbon.frontend.module.GetNextModulesListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.module.GetNextModulesListResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='assigned_modules', full_name='carbon.frontend.module.GetNextModulesListResponse.assigned_modules', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='unassigned_modules', full_name='carbon.frontend.module.GetNextModulesListResponse.unassigned_modules', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='unset_serial_modules', full_name='carbon.frontend.module.GetNextModulesListResponse.unset_serial_modules', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=203,
  serialized_end=480,
)


_GETNEXTACTIVEMODULESREQUEST = _descriptor.Descriptor(
  name='GetNextActiveModulesRequest',
  full_name='carbon.frontend.module.GetNextActiveModulesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.module.GetNextActiveModulesRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=482,
  serialized_end=556,
)


_GETNEXTACTIVEMODULESRESPONSE = _descriptor.Descriptor(
  name='GetNextActiveModulesResponse',
  full_name='carbon.frontend.module.GetNextActiveModulesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.module.GetNextActiveModulesResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_modules', full_name='carbon.frontend.module.GetNextActiveModulesResponse.active_modules', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=559,
  serialized_end=698,
)


_IDENTIFYMODULEREQUEST = _descriptor.Descriptor(
  name='IdentifyModuleRequest',
  full_name='carbon.frontend.module.IdentifyModuleRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_identity', full_name='carbon.frontend.module.IdentifyModuleRequest.module_identity', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=700,
  serialized_end=788,
)


_ASSIGNMODULEREQUEST = _descriptor.Descriptor(
  name='AssignModuleRequest',
  full_name='carbon.frontend.module.AssignModuleRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_identity', full_name='carbon.frontend.module.AssignModuleRequest.module_identity', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=790,
  serialized_end=876,
)


_CLEARMODULEASSIGNMENTREQUEST = _descriptor.Descriptor(
  name='ClearModuleAssignmentRequest',
  full_name='carbon.frontend.module.ClearModuleAssignmentRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_identity', full_name='carbon.frontend.module.ClearModuleAssignmentRequest.module_identity', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=878,
  serialized_end=973,
)


_SETMODULESERIALREQUEST = _descriptor.Descriptor(
  name='SetModuleSerialRequest',
  full_name='carbon.frontend.module.SetModuleSerialRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_identity', full_name='carbon.frontend.module.SetModuleSerialRequest.module_identity', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='new_serial', full_name='carbon.frontend.module.SetModuleSerialRequest.new_serial', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=975,
  serialized_end=1084,
)


_MODULEDEFINITION = _descriptor.Descriptor(
  name='ModuleDefinition',
  full_name='carbon.frontend.module.ModuleDefinition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='carbon.frontend.module.ModuleDefinition.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='module_spacing_mm', full_name='carbon.frontend.module.ModuleDefinition.module_spacing_mm', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='disabled', full_name='carbon.frontend.module.ModuleDefinition.disabled', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1086,
  serialized_end=1168,
)


_ROWDEFINITION = _descriptor.Descriptor(
  name='RowDefinition',
  full_name='carbon.frontend.module.RowDefinition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.module.RowDefinition.row_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='modules', full_name='carbon.frontend.module.RowDefinition.modules', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_spacing_mm', full_name='carbon.frontend.module.RowDefinition.row_spacing_mm', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1170,
  serialized_end=1284,
)


_BARDEFINITION = _descriptor.Descriptor(
  name='BarDefinition',
  full_name='carbon.frontend.module.BarDefinition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='bar_length_mm', full_name='carbon.frontend.module.BarDefinition.bar_length_mm', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='folding', full_name='carbon.frontend.module.BarDefinition.folding', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1286,
  serialized_end=1341,
)


_ROBOTDEFINITION = _descriptor.Descriptor(
  name='RobotDefinition',
  full_name='carbon.frontend.module.RobotDefinition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='carbon.frontend.module.RobotDefinition.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bar_definition', full_name='carbon.frontend.module.RobotDefinition.bar_definition', index=1,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1344,
  serialized_end=1477,
)


_PRESET = _descriptor.Descriptor(
  name='Preset',
  full_name='carbon.frontend.module.Preset',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.frontend.module.Preset.uuid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='display_name', full_name='carbon.frontend.module.Preset.display_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='definition', full_name='carbon.frontend.module.Preset.definition', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1479,
  serialized_end=1584,
)


_GETPRESETSLISTREQUEST = _descriptor.Descriptor(
  name='GetPresetsListRequest',
  full_name='carbon.frontend.module.GetPresetsListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='language', full_name='carbon.frontend.module.GetPresetsListRequest.language', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1586,
  serialized_end=1627,
)


_GETPRESETSLISTRESPONSE = _descriptor.Descriptor(
  name='GetPresetsListResponse',
  full_name='carbon.frontend.module.GetPresetsListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='presets', full_name='carbon.frontend.module.GetPresetsListResponse.presets', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1629,
  serialized_end=1702,
)


_GETCURRENTROBOTDEFINITIONRESPONSE = _descriptor.Descriptor(
  name='GetCurrentRobotDefinitionResponse',
  full_name='carbon.frontend.module.GetCurrentRobotDefinitionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current_definition', full_name='carbon.frontend.module.GetCurrentRobotDefinitionResponse.current_definition', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_current_definition', full_name='carbon.frontend.module.GetCurrentRobotDefinitionResponse._current_definition',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1705,
  serialized_end=1837,
)


_SETCURRENTROBOTDEFINITIONREQUEST = _descriptor.Descriptor(
  name='SetCurrentRobotDefinitionRequest',
  full_name='carbon.frontend.module.SetCurrentRobotDefinitionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current_definition', full_name='carbon.frontend.module.SetCurrentRobotDefinitionRequest.current_definition', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1839,
  serialized_end=1942,
)

_GETNEXTMODULESLISTREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTMODULESLISTRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTMODULESLISTRESPONSE.fields_by_name['assigned_modules'].message_type = _MODULEIDENTITY
_GETNEXTMODULESLISTRESPONSE.fields_by_name['unassigned_modules'].message_type = _MODULEIDENTITY
_GETNEXTMODULESLISTRESPONSE.fields_by_name['unset_serial_modules'].message_type = _MODULEIDENTITY
_GETNEXTACTIVEMODULESREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTACTIVEMODULESRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTACTIVEMODULESRESPONSE.fields_by_name['active_modules'].message_type = _MODULEIDENTITY
_IDENTIFYMODULEREQUEST.fields_by_name['module_identity'].message_type = _MODULEIDENTITY
_ASSIGNMODULEREQUEST.fields_by_name['module_identity'].message_type = _MODULEIDENTITY
_CLEARMODULEASSIGNMENTREQUEST.fields_by_name['module_identity'].message_type = _MODULEIDENTITY
_SETMODULESERIALREQUEST.fields_by_name['module_identity'].message_type = _MODULEIDENTITY
_ROWDEFINITION.fields_by_name['modules'].message_type = _MODULEDEFINITION
_ROBOTDEFINITION.fields_by_name['rows'].message_type = _ROWDEFINITION
_ROBOTDEFINITION.fields_by_name['bar_definition'].message_type = _BARDEFINITION
_PRESET.fields_by_name['definition'].message_type = _ROBOTDEFINITION
_GETPRESETSLISTRESPONSE.fields_by_name['presets'].message_type = _PRESET
_GETCURRENTROBOTDEFINITIONRESPONSE.fields_by_name['current_definition'].message_type = _ROBOTDEFINITION
_GETCURRENTROBOTDEFINITIONRESPONSE.oneofs_by_name['_current_definition'].fields.append(
  _GETCURRENTROBOTDEFINITIONRESPONSE.fields_by_name['current_definition'])
_GETCURRENTROBOTDEFINITIONRESPONSE.fields_by_name['current_definition'].containing_oneof = _GETCURRENTROBOTDEFINITIONRESPONSE.oneofs_by_name['_current_definition']
_SETCURRENTROBOTDEFINITIONREQUEST.fields_by_name['current_definition'].message_type = _ROBOTDEFINITION
DESCRIPTOR.message_types_by_name['ModuleIdentity'] = _MODULEIDENTITY
DESCRIPTOR.message_types_by_name['GetNextModulesListRequest'] = _GETNEXTMODULESLISTREQUEST
DESCRIPTOR.message_types_by_name['GetNextModulesListResponse'] = _GETNEXTMODULESLISTRESPONSE
DESCRIPTOR.message_types_by_name['GetNextActiveModulesRequest'] = _GETNEXTACTIVEMODULESREQUEST
DESCRIPTOR.message_types_by_name['GetNextActiveModulesResponse'] = _GETNEXTACTIVEMODULESRESPONSE
DESCRIPTOR.message_types_by_name['IdentifyModuleRequest'] = _IDENTIFYMODULEREQUEST
DESCRIPTOR.message_types_by_name['AssignModuleRequest'] = _ASSIGNMODULEREQUEST
DESCRIPTOR.message_types_by_name['ClearModuleAssignmentRequest'] = _CLEARMODULEASSIGNMENTREQUEST
DESCRIPTOR.message_types_by_name['SetModuleSerialRequest'] = _SETMODULESERIALREQUEST
DESCRIPTOR.message_types_by_name['ModuleDefinition'] = _MODULEDEFINITION
DESCRIPTOR.message_types_by_name['RowDefinition'] = _ROWDEFINITION
DESCRIPTOR.message_types_by_name['BarDefinition'] = _BARDEFINITION
DESCRIPTOR.message_types_by_name['RobotDefinition'] = _ROBOTDEFINITION
DESCRIPTOR.message_types_by_name['Preset'] = _PRESET
DESCRIPTOR.message_types_by_name['GetPresetsListRequest'] = _GETPRESETSLISTREQUEST
DESCRIPTOR.message_types_by_name['GetPresetsListResponse'] = _GETPRESETSLISTRESPONSE
DESCRIPTOR.message_types_by_name['GetCurrentRobotDefinitionResponse'] = _GETCURRENTROBOTDEFINITIONRESPONSE
DESCRIPTOR.message_types_by_name['SetCurrentRobotDefinitionRequest'] = _SETCURRENTROBOTDEFINITIONREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ModuleIdentity = _reflection.GeneratedProtocolMessageType('ModuleIdentity', (_message.Message,), {
  'DESCRIPTOR' : _MODULEIDENTITY,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.ModuleIdentity)
  })
_sym_db.RegisterMessage(ModuleIdentity)

GetNextModulesListRequest = _reflection.GeneratedProtocolMessageType('GetNextModulesListRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTMODULESLISTREQUEST,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.GetNextModulesListRequest)
  })
_sym_db.RegisterMessage(GetNextModulesListRequest)

GetNextModulesListResponse = _reflection.GeneratedProtocolMessageType('GetNextModulesListResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTMODULESLISTRESPONSE,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.GetNextModulesListResponse)
  })
_sym_db.RegisterMessage(GetNextModulesListResponse)

GetNextActiveModulesRequest = _reflection.GeneratedProtocolMessageType('GetNextActiveModulesRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTACTIVEMODULESREQUEST,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.GetNextActiveModulesRequest)
  })
_sym_db.RegisterMessage(GetNextActiveModulesRequest)

GetNextActiveModulesResponse = _reflection.GeneratedProtocolMessageType('GetNextActiveModulesResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTACTIVEMODULESRESPONSE,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.GetNextActiveModulesResponse)
  })
_sym_db.RegisterMessage(GetNextActiveModulesResponse)

IdentifyModuleRequest = _reflection.GeneratedProtocolMessageType('IdentifyModuleRequest', (_message.Message,), {
  'DESCRIPTOR' : _IDENTIFYMODULEREQUEST,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.IdentifyModuleRequest)
  })
_sym_db.RegisterMessage(IdentifyModuleRequest)

AssignModuleRequest = _reflection.GeneratedProtocolMessageType('AssignModuleRequest', (_message.Message,), {
  'DESCRIPTOR' : _ASSIGNMODULEREQUEST,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.AssignModuleRequest)
  })
_sym_db.RegisterMessage(AssignModuleRequest)

ClearModuleAssignmentRequest = _reflection.GeneratedProtocolMessageType('ClearModuleAssignmentRequest', (_message.Message,), {
  'DESCRIPTOR' : _CLEARMODULEASSIGNMENTREQUEST,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.ClearModuleAssignmentRequest)
  })
_sym_db.RegisterMessage(ClearModuleAssignmentRequest)

SetModuleSerialRequest = _reflection.GeneratedProtocolMessageType('SetModuleSerialRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETMODULESERIALREQUEST,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.SetModuleSerialRequest)
  })
_sym_db.RegisterMessage(SetModuleSerialRequest)

ModuleDefinition = _reflection.GeneratedProtocolMessageType('ModuleDefinition', (_message.Message,), {
  'DESCRIPTOR' : _MODULEDEFINITION,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.ModuleDefinition)
  })
_sym_db.RegisterMessage(ModuleDefinition)

RowDefinition = _reflection.GeneratedProtocolMessageType('RowDefinition', (_message.Message,), {
  'DESCRIPTOR' : _ROWDEFINITION,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.RowDefinition)
  })
_sym_db.RegisterMessage(RowDefinition)

BarDefinition = _reflection.GeneratedProtocolMessageType('BarDefinition', (_message.Message,), {
  'DESCRIPTOR' : _BARDEFINITION,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.BarDefinition)
  })
_sym_db.RegisterMessage(BarDefinition)

RobotDefinition = _reflection.GeneratedProtocolMessageType('RobotDefinition', (_message.Message,), {
  'DESCRIPTOR' : _ROBOTDEFINITION,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.RobotDefinition)
  })
_sym_db.RegisterMessage(RobotDefinition)

Preset = _reflection.GeneratedProtocolMessageType('Preset', (_message.Message,), {
  'DESCRIPTOR' : _PRESET,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.Preset)
  })
_sym_db.RegisterMessage(Preset)

GetPresetsListRequest = _reflection.GeneratedProtocolMessageType('GetPresetsListRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPRESETSLISTREQUEST,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.GetPresetsListRequest)
  })
_sym_db.RegisterMessage(GetPresetsListRequest)

GetPresetsListResponse = _reflection.GeneratedProtocolMessageType('GetPresetsListResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETPRESETSLISTRESPONSE,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.GetPresetsListResponse)
  })
_sym_db.RegisterMessage(GetPresetsListResponse)

GetCurrentRobotDefinitionResponse = _reflection.GeneratedProtocolMessageType('GetCurrentRobotDefinitionResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCURRENTROBOTDEFINITIONRESPONSE,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.GetCurrentRobotDefinitionResponse)
  })
_sym_db.RegisterMessage(GetCurrentRobotDefinitionResponse)

SetCurrentRobotDefinitionRequest = _reflection.GeneratedProtocolMessageType('SetCurrentRobotDefinitionRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETCURRENTROBOTDEFINITIONREQUEST,
  '__module__' : 'frontend.proto.module_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.module.SetCurrentRobotDefinitionRequest)
  })
_sym_db.RegisterMessage(SetCurrentRobotDefinitionRequest)


DESCRIPTOR._options = None

_MODULEASSIGNMENTSERVICE = _descriptor.ServiceDescriptor(
  name='ModuleAssignmentService',
  full_name='carbon.frontend.module.ModuleAssignmentService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1945,
  serialized_end=2961,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextModulesList',
    full_name='carbon.frontend.module.ModuleAssignmentService.GetNextModulesList',
    index=0,
    containing_service=None,
    input_type=_GETNEXTMODULESLISTREQUEST,
    output_type=_GETNEXTMODULESLISTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextActiveModules',
    full_name='carbon.frontend.module.ModuleAssignmentService.GetNextActiveModules',
    index=1,
    containing_service=None,
    input_type=_GETNEXTACTIVEMODULESREQUEST,
    output_type=_GETNEXTACTIVEMODULESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='IdentifyModule',
    full_name='carbon.frontend.module.ModuleAssignmentService.IdentifyModule',
    index=2,
    containing_service=None,
    input_type=_IDENTIFYMODULEREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='AssignModule',
    full_name='carbon.frontend.module.ModuleAssignmentService.AssignModule',
    index=3,
    containing_service=None,
    input_type=_ASSIGNMODULEREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ClearModuleAssignment',
    full_name='carbon.frontend.module.ModuleAssignmentService.ClearModuleAssignment',
    index=4,
    containing_service=None,
    input_type=_CLEARMODULEASSIGNMENTREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetModuleSerial',
    full_name='carbon.frontend.module.ModuleAssignmentService.SetModuleSerial',
    index=5,
    containing_service=None,
    input_type=_SETMODULESERIALREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetPresetsList',
    full_name='carbon.frontend.module.ModuleAssignmentService.GetPresetsList',
    index=6,
    containing_service=None,
    input_type=_GETPRESETSLISTREQUEST,
    output_type=_GETPRESETSLISTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCurrentRobotDefinition',
    full_name='carbon.frontend.module.ModuleAssignmentService.GetCurrentRobotDefinition',
    index=7,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_GETCURRENTROBOTDEFINITIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetCurrentRobotDefinition',
    full_name='carbon.frontend.module.ModuleAssignmentService.SetCurrentRobotDefinition',
    index=8,
    containing_service=None,
    input_type=_SETCURRENTROBOTDEFINITIONREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_MODULEASSIGNMENTSERVICE)

DESCRIPTOR.services_by_name['ModuleAssignmentService'] = _MODULEASSIGNMENTSERVICE

# @@protoc_insertion_point(module_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/model.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fmodel_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fmodel_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fmodel_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fmodel_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[26]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fmodel_2eproto;
namespace carbon {
namespace frontend {
namespace model {
class CropModelPair;
struct CropModelPairDefaultTypeInternal;
extern CropModelPairDefaultTypeInternal _CropModelPair_default_instance_;
class DownloadModelRequest;
struct DownloadModelRequestDefaultTypeInternal;
extern DownloadModelRequestDefaultTypeInternal _DownloadModelRequest_default_instance_;
class EnabledCrop;
struct EnabledCropDefaultTypeInternal;
extern EnabledCropDefaultTypeInternal _EnabledCrop_default_instance_;
class EnabledCropList;
struct EnabledCropListDefaultTypeInternal;
extern EnabledCropListDefaultTypeInternal _EnabledCropList_default_instance_;
class GetModelNicknamesRequest;
struct GetModelNicknamesRequestDefaultTypeInternal;
extern GetModelNicknamesRequestDefaultTypeInternal _GetModelNicknamesRequest_default_instance_;
class GetModelNicknamesResponse;
struct GetModelNicknamesResponseDefaultTypeInternal;
extern GetModelNicknamesResponseDefaultTypeInternal _GetModelNicknamesResponse_default_instance_;
class GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse;
struct GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUseDefaultTypeInternal;
extern GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUseDefaultTypeInternal _GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse_default_instance_;
class GetNextCaptureCropsRequest;
struct GetNextCaptureCropsRequestDefaultTypeInternal;
extern GetNextCaptureCropsRequestDefaultTypeInternal _GetNextCaptureCropsRequest_default_instance_;
class GetNextCaptureCropsResponse;
struct GetNextCaptureCropsResponseDefaultTypeInternal;
extern GetNextCaptureCropsResponseDefaultTypeInternal _GetNextCaptureCropsResponse_default_instance_;
class GetNextEnabledCropsRequest;
struct GetNextEnabledCropsRequestDefaultTypeInternal;
extern GetNextEnabledCropsRequestDefaultTypeInternal _GetNextEnabledCropsRequest_default_instance_;
class GetNextEnabledCropsResponse;
struct GetNextEnabledCropsResponseDefaultTypeInternal;
extern GetNextEnabledCropsResponseDefaultTypeInternal _GetNextEnabledCropsResponse_default_instance_;
class GetNextModelStateRequest;
struct GetNextModelStateRequestDefaultTypeInternal;
extern GetNextModelStateRequestDefaultTypeInternal _GetNextModelStateRequest_default_instance_;
class GetNextModelStateResponse;
struct GetNextModelStateResponseDefaultTypeInternal;
extern GetNextModelStateResponseDefaultTypeInternal _GetNextModelStateResponse_default_instance_;
class GetNextSelectedCropIDResponse;
struct GetNextSelectedCropIDResponseDefaultTypeInternal;
extern GetNextSelectedCropIDResponseDefaultTypeInternal _GetNextSelectedCropIDResponse_default_instance_;
class ListCropParameters;
struct ListCropParametersDefaultTypeInternal;
extern ListCropParametersDefaultTypeInternal _ListCropParameters_default_instance_;
class Model;
struct ModelDefaultTypeInternal;
extern ModelDefaultTypeInternal _Model_default_instance_;
class ModelEvent;
struct ModelEventDefaultTypeInternal;
extern ModelEventDefaultTypeInternal _ModelEvent_default_instance_;
class ModelEventTypeMatcher;
struct ModelEventTypeMatcherDefaultTypeInternal;
extern ModelEventTypeMatcherDefaultTypeInternal _ModelEventTypeMatcher_default_instance_;
class ModelHistoryRequest;
struct ModelHistoryRequestDefaultTypeInternal;
extern ModelHistoryRequestDefaultTypeInternal _ModelHistoryRequest_default_instance_;
class ModelHistoryResponse;
struct ModelHistoryResponseDefaultTypeInternal;
extern ModelHistoryResponseDefaultTypeInternal _ModelHistoryResponse_default_instance_;
class PinModelRequest;
struct PinModelRequestDefaultTypeInternal;
extern PinModelRequestDefaultTypeInternal _PinModelRequest_default_instance_;
class RefreshDefaultModelParametersRequest;
struct RefreshDefaultModelParametersRequestDefaultTypeInternal;
extern RefreshDefaultModelParametersRequestDefaultTypeInternal _RefreshDefaultModelParametersRequest_default_instance_;
class SelectCropRequest;
struct SelectCropRequestDefaultTypeInternal;
extern SelectCropRequestDefaultTypeInternal _SelectCropRequest_default_instance_;
class SetModelNicknameRequest;
struct SetModelNicknameRequestDefaultTypeInternal;
extern SetModelNicknameRequestDefaultTypeInternal _SetModelNicknameRequest_default_instance_;
class SyncCropIDsRequest;
struct SyncCropIDsRequestDefaultTypeInternal;
extern SyncCropIDsRequestDefaultTypeInternal _SyncCropIDsRequest_default_instance_;
class UnpinModelRequest;
struct UnpinModelRequestDefaultTypeInternal;
extern UnpinModelRequestDefaultTypeInternal _UnpinModelRequest_default_instance_;
}  // namespace model
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::model::CropModelPair* Arena::CreateMaybeMessage<::carbon::frontend::model::CropModelPair>(Arena*);
template<> ::carbon::frontend::model::DownloadModelRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::DownloadModelRequest>(Arena*);
template<> ::carbon::frontend::model::EnabledCrop* Arena::CreateMaybeMessage<::carbon::frontend::model::EnabledCrop>(Arena*);
template<> ::carbon::frontend::model::EnabledCropList* Arena::CreateMaybeMessage<::carbon::frontend::model::EnabledCropList>(Arena*);
template<> ::carbon::frontend::model::GetModelNicknamesRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::GetModelNicknamesRequest>(Arena*);
template<> ::carbon::frontend::model::GetModelNicknamesResponse* Arena::CreateMaybeMessage<::carbon::frontend::model::GetModelNicknamesResponse>(Arena*);
template<> ::carbon::frontend::model::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::model::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::model::GetNextCaptureCropsRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::GetNextCaptureCropsRequest>(Arena*);
template<> ::carbon::frontend::model::GetNextCaptureCropsResponse* Arena::CreateMaybeMessage<::carbon::frontend::model::GetNextCaptureCropsResponse>(Arena*);
template<> ::carbon::frontend::model::GetNextEnabledCropsRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::GetNextEnabledCropsRequest>(Arena*);
template<> ::carbon::frontend::model::GetNextEnabledCropsResponse* Arena::CreateMaybeMessage<::carbon::frontend::model::GetNextEnabledCropsResponse>(Arena*);
template<> ::carbon::frontend::model::GetNextModelStateRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::GetNextModelStateRequest>(Arena*);
template<> ::carbon::frontend::model::GetNextModelStateResponse* Arena::CreateMaybeMessage<::carbon::frontend::model::GetNextModelStateResponse>(Arena*);
template<> ::carbon::frontend::model::GetNextSelectedCropIDResponse* Arena::CreateMaybeMessage<::carbon::frontend::model::GetNextSelectedCropIDResponse>(Arena*);
template<> ::carbon::frontend::model::ListCropParameters* Arena::CreateMaybeMessage<::carbon::frontend::model::ListCropParameters>(Arena*);
template<> ::carbon::frontend::model::Model* Arena::CreateMaybeMessage<::carbon::frontend::model::Model>(Arena*);
template<> ::carbon::frontend::model::ModelEvent* Arena::CreateMaybeMessage<::carbon::frontend::model::ModelEvent>(Arena*);
template<> ::carbon::frontend::model::ModelEventTypeMatcher* Arena::CreateMaybeMessage<::carbon::frontend::model::ModelEventTypeMatcher>(Arena*);
template<> ::carbon::frontend::model::ModelHistoryRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::ModelHistoryRequest>(Arena*);
template<> ::carbon::frontend::model::ModelHistoryResponse* Arena::CreateMaybeMessage<::carbon::frontend::model::ModelHistoryResponse>(Arena*);
template<> ::carbon::frontend::model::PinModelRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::PinModelRequest>(Arena*);
template<> ::carbon::frontend::model::RefreshDefaultModelParametersRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::RefreshDefaultModelParametersRequest>(Arena*);
template<> ::carbon::frontend::model::SelectCropRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::SelectCropRequest>(Arena*);
template<> ::carbon::frontend::model::SetModelNicknameRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::SetModelNicknameRequest>(Arena*);
template<> ::carbon::frontend::model::SyncCropIDsRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::SyncCropIDsRequest>(Arena*);
template<> ::carbon::frontend::model::UnpinModelRequest* Arena::CreateMaybeMessage<::carbon::frontend::model::UnpinModelRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace model {

// ===================================================================

class Model final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.Model) */ {
 public:
  inline Model() : Model(nullptr) {}
  ~Model() override;
  explicit constexpr Model(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Model(const Model& from);
  Model(Model&& from) noexcept
    : Model() {
    *this = ::std::move(from);
  }

  inline Model& operator=(const Model& from) {
    CopyFrom(from);
    return *this;
  }
  inline Model& operator=(Model&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Model& default_instance() {
    return *internal_default_instance();
  }
  static inline const Model* internal_default_instance() {
    return reinterpret_cast<const Model*>(
               &_Model_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Model& a, Model& b) {
    a.Swap(&b);
  }
  inline void Swap(Model* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Model* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Model* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Model>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Model& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Model& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Model* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.Model";
  }
  protected:
  explicit Model(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSyncedToRowsFieldNumber = 8,
    kViableCropIdsFieldNumber = 16,
    kIdFieldNumber = 1,
    kCropFieldNumber = 2,
    kTypeFieldNumber = 10,
    kNicknameFieldNumber = 18,
    kTsFieldNumber = 3,
    kLastUsedTimestampFieldNumber = 11,
    kDownloadedTimestampFieldNumber = 14,
    kCustomFieldNumber = 4,
    kPinnedFieldNumber = 5,
    kActiveFieldNumber = 6,
    kSyncedFieldNumber = 7,
    kDownloadingProgressFieldNumber = 12,
    kEstimatedDownloadingRemainingTimeMsFieldNumber = 13,
    kDownloadingFieldNumber = 9,
    kRecommendedFieldNumber = 15,
    kMaintainedFieldNumber = 17,
  };
  // repeated bool synced_to_rows = 8;
  int synced_to_rows_size() const;
  private:
  int _internal_synced_to_rows_size() const;
  public:
  void clear_synced_to_rows();
  private:
  bool _internal_synced_to_rows(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_synced_to_rows() const;
  void _internal_add_synced_to_rows(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_synced_to_rows();
  public:
  bool synced_to_rows(int index) const;
  void set_synced_to_rows(int index, bool value);
  void add_synced_to_rows(bool value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      synced_to_rows() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_synced_to_rows();

  // repeated string viable_crop_ids = 16;
  int viable_crop_ids_size() const;
  private:
  int _internal_viable_crop_ids_size() const;
  public:
  void clear_viable_crop_ids();
  const std::string& viable_crop_ids(int index) const;
  std::string* mutable_viable_crop_ids(int index);
  void set_viable_crop_ids(int index, const std::string& value);
  void set_viable_crop_ids(int index, std::string&& value);
  void set_viable_crop_ids(int index, const char* value);
  void set_viable_crop_ids(int index, const char* value, size_t size);
  std::string* add_viable_crop_ids();
  void add_viable_crop_ids(const std::string& value);
  void add_viable_crop_ids(std::string&& value);
  void add_viable_crop_ids(const char* value);
  void add_viable_crop_ids(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& viable_crop_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_viable_crop_ids();
  private:
  const std::string& _internal_viable_crop_ids(int index) const;
  std::string* _internal_add_viable_crop_ids();
  public:

  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string crop = 2;
  void clear_crop();
  const std::string& crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop();
  PROTOBUF_NODISCARD std::string* release_crop();
  void set_allocated_crop(std::string* crop);
  private:
  const std::string& _internal_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop(const std::string& value);
  std::string* _internal_mutable_crop();
  public:

  // string type = 10;
  void clear_type();
  const std::string& type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type();
  PROTOBUF_NODISCARD std::string* release_type();
  void set_allocated_type(std::string* type);
  private:
  const std::string& _internal_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type(const std::string& value);
  std::string* _internal_mutable_type();
  public:

  // string nickname = 18;
  void clear_nickname();
  const std::string& nickname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_nickname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_nickname();
  PROTOBUF_NODISCARD std::string* release_nickname();
  void set_allocated_nickname(std::string* nickname);
  private:
  const std::string& _internal_nickname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_nickname(const std::string& value);
  std::string* _internal_mutable_nickname();
  public:

  // .carbon.frontend.util.Timestamp ts = 3;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.util.Timestamp last_used_timestamp = 11;
  bool has_last_used_timestamp() const;
  private:
  bool _internal_has_last_used_timestamp() const;
  public:
  void clear_last_used_timestamp();
  const ::carbon::frontend::util::Timestamp& last_used_timestamp() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_last_used_timestamp();
  ::carbon::frontend::util::Timestamp* mutable_last_used_timestamp();
  void set_allocated_last_used_timestamp(::carbon::frontend::util::Timestamp* last_used_timestamp);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_last_used_timestamp() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_last_used_timestamp();
  public:
  void unsafe_arena_set_allocated_last_used_timestamp(
      ::carbon::frontend::util::Timestamp* last_used_timestamp);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_last_used_timestamp();

  // .carbon.frontend.util.Timestamp downloaded_timestamp = 14;
  bool has_downloaded_timestamp() const;
  private:
  bool _internal_has_downloaded_timestamp() const;
  public:
  void clear_downloaded_timestamp();
  const ::carbon::frontend::util::Timestamp& downloaded_timestamp() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_downloaded_timestamp();
  ::carbon::frontend::util::Timestamp* mutable_downloaded_timestamp();
  void set_allocated_downloaded_timestamp(::carbon::frontend::util::Timestamp* downloaded_timestamp);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_downloaded_timestamp() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_downloaded_timestamp();
  public:
  void unsafe_arena_set_allocated_downloaded_timestamp(
      ::carbon::frontend::util::Timestamp* downloaded_timestamp);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_downloaded_timestamp();

  // bool custom = 4;
  void clear_custom();
  bool custom() const;
  void set_custom(bool value);
  private:
  bool _internal_custom() const;
  void _internal_set_custom(bool value);
  public:

  // bool pinned = 5;
  void clear_pinned();
  bool pinned() const;
  void set_pinned(bool value);
  private:
  bool _internal_pinned() const;
  void _internal_set_pinned(bool value);
  public:

  // bool active = 6;
  void clear_active();
  bool active() const;
  void set_active(bool value);
  private:
  bool _internal_active() const;
  void _internal_set_active(bool value);
  public:

  // bool synced = 7;
  void clear_synced();
  bool synced() const;
  void set_synced(bool value);
  private:
  bool _internal_synced() const;
  void _internal_set_synced(bool value);
  public:

  // float downloading_progress = 12;
  void clear_downloading_progress();
  float downloading_progress() const;
  void set_downloading_progress(float value);
  private:
  float _internal_downloading_progress() const;
  void _internal_set_downloading_progress(float value);
  public:

  // uint64 estimated_downloading_remaining_time_ms = 13;
  void clear_estimated_downloading_remaining_time_ms();
  uint64_t estimated_downloading_remaining_time_ms() const;
  void set_estimated_downloading_remaining_time_ms(uint64_t value);
  private:
  uint64_t _internal_estimated_downloading_remaining_time_ms() const;
  void _internal_set_estimated_downloading_remaining_time_ms(uint64_t value);
  public:

  // bool downloading = 9;
  void clear_downloading();
  bool downloading() const;
  void set_downloading(bool value);
  private:
  bool _internal_downloading() const;
  void _internal_set_downloading(bool value);
  public:

  // bool recommended = 15;
  void clear_recommended();
  bool recommended() const;
  void set_recommended(bool value);
  private:
  bool _internal_recommended() const;
  void _internal_set_recommended(bool value);
  public:

  // bool maintained = 17;
  void clear_maintained();
  bool maintained() const;
  void set_maintained(bool value);
  private:
  bool _internal_maintained() const;
  void _internal_set_maintained(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.Model)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > synced_to_rows_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> viable_crop_ids_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr nickname_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::util::Timestamp* last_used_timestamp_;
  ::carbon::frontend::util::Timestamp* downloaded_timestamp_;
  bool custom_;
  bool pinned_;
  bool active_;
  bool synced_;
  float downloading_progress_;
  uint64_t estimated_downloading_remaining_time_ms_;
  bool downloading_;
  bool recommended_;
  bool maintained_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class SelectCropRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.SelectCropRequest) */ {
 public:
  inline SelectCropRequest() : SelectCropRequest(nullptr) {}
  ~SelectCropRequest() override;
  explicit constexpr SelectCropRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SelectCropRequest(const SelectCropRequest& from);
  SelectCropRequest(SelectCropRequest&& from) noexcept
    : SelectCropRequest() {
    *this = ::std::move(from);
  }

  inline SelectCropRequest& operator=(const SelectCropRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SelectCropRequest& operator=(SelectCropRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SelectCropRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SelectCropRequest* internal_default_instance() {
    return reinterpret_cast<const SelectCropRequest*>(
               &_SelectCropRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SelectCropRequest& a, SelectCropRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SelectCropRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SelectCropRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SelectCropRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SelectCropRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SelectCropRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SelectCropRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SelectCropRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.SelectCropRequest";
  }
  protected:
  explicit SelectCropRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCropIdFieldNumber = 1,
  };
  // string crop_id = 1;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.SelectCropRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class ListCropParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.ListCropParameters) */ {
 public:
  inline ListCropParameters() : ListCropParameters(nullptr) {}
  ~ListCropParameters() override;
  explicit constexpr ListCropParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ListCropParameters(const ListCropParameters& from);
  ListCropParameters(ListCropParameters&& from) noexcept
    : ListCropParameters() {
    *this = ::std::move(from);
  }

  inline ListCropParameters& operator=(const ListCropParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListCropParameters& operator=(ListCropParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ListCropParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const ListCropParameters* internal_default_instance() {
    return reinterpret_cast<const ListCropParameters*>(
               &_ListCropParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ListCropParameters& a, ListCropParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(ListCropParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListCropParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ListCropParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ListCropParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ListCropParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ListCropParameters& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListCropParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.ListCropParameters";
  }
  protected:
  explicit ListCropParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLangFieldNumber = 1,
  };
  // string lang = 1;
  void clear_lang();
  const std::string& lang() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_lang(ArgT0&& arg0, ArgT... args);
  std::string* mutable_lang();
  PROTOBUF_NODISCARD std::string* release_lang();
  void set_allocated_lang(std::string* lang);
  private:
  const std::string& _internal_lang() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_lang(const std::string& value);
  std::string* _internal_mutable_lang();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.ListCropParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr lang_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class EnabledCrop final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.EnabledCrop) */ {
 public:
  inline EnabledCrop() : EnabledCrop(nullptr) {}
  ~EnabledCrop() override;
  explicit constexpr EnabledCrop(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EnabledCrop(const EnabledCrop& from);
  EnabledCrop(EnabledCrop&& from) noexcept
    : EnabledCrop() {
    *this = ::std::move(from);
  }

  inline EnabledCrop& operator=(const EnabledCrop& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnabledCrop& operator=(EnabledCrop&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EnabledCrop& default_instance() {
    return *internal_default_instance();
  }
  static inline const EnabledCrop* internal_default_instance() {
    return reinterpret_cast<const EnabledCrop*>(
               &_EnabledCrop_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(EnabledCrop& a, EnabledCrop& b) {
    a.Swap(&b);
  }
  inline void Swap(EnabledCrop* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EnabledCrop* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EnabledCrop* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EnabledCrop>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EnabledCrop& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EnabledCrop& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnabledCrop* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.EnabledCrop";
  }
  protected:
  explicit EnabledCrop(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kCarbonNameFieldNumber = 3,
    kCommonNameFieldNumber = 4,
    kDescriptionFieldNumber = 5,
    kNotesFieldNumber = 6,
    kPinnedModelIdFieldNumber = 7,
    kRecommendedModelFieldNumber = 8,
    kCreatedFieldNumber = 2,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string carbon_name = 3 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_carbon_name();
  PROTOBUF_DEPRECATED const std::string& carbon_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_carbon_name(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_carbon_name();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_carbon_name();
  PROTOBUF_DEPRECATED void set_allocated_carbon_name(std::string* carbon_name);
  private:
  const std::string& _internal_carbon_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_carbon_name(const std::string& value);
  std::string* _internal_mutable_carbon_name();
  public:

  // string common_name = 4;
  void clear_common_name();
  const std::string& common_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_common_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_common_name();
  PROTOBUF_NODISCARD std::string* release_common_name();
  void set_allocated_common_name(std::string* common_name);
  private:
  const std::string& _internal_common_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_common_name(const std::string& value);
  std::string* _internal_mutable_common_name();
  public:

  // string description = 5;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // string notes = 6;
  void clear_notes();
  const std::string& notes() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_notes(ArgT0&& arg0, ArgT... args);
  std::string* mutable_notes();
  PROTOBUF_NODISCARD std::string* release_notes();
  void set_allocated_notes(std::string* notes);
  private:
  const std::string& _internal_notes() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_notes(const std::string& value);
  std::string* _internal_mutable_notes();
  public:

  // string pinned_model_id = 7;
  void clear_pinned_model_id();
  const std::string& pinned_model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pinned_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pinned_model_id();
  PROTOBUF_NODISCARD std::string* release_pinned_model_id();
  void set_allocated_pinned_model_id(std::string* pinned_model_id);
  private:
  const std::string& _internal_pinned_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pinned_model_id(const std::string& value);
  std::string* _internal_mutable_pinned_model_id();
  public:

  // string recommended_model = 8;
  void clear_recommended_model();
  const std::string& recommended_model() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_recommended_model(ArgT0&& arg0, ArgT... args);
  std::string* mutable_recommended_model();
  PROTOBUF_NODISCARD std::string* release_recommended_model();
  void set_allocated_recommended_model(std::string* recommended_model);
  private:
  const std::string& _internal_recommended_model() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_recommended_model(const std::string& value);
  std::string* _internal_mutable_recommended_model();
  public:

  // int64 created = 2;
  void clear_created();
  int64_t created() const;
  void set_created(int64_t value);
  private:
  int64_t _internal_created() const;
  void _internal_set_created(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.EnabledCrop)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr carbon_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr common_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr notes_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pinned_model_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr recommended_model_;
  int64_t created_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class EnabledCropList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.EnabledCropList) */ {
 public:
  inline EnabledCropList() : EnabledCropList(nullptr) {}
  ~EnabledCropList() override;
  explicit constexpr EnabledCropList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EnabledCropList(const EnabledCropList& from);
  EnabledCropList(EnabledCropList&& from) noexcept
    : EnabledCropList() {
    *this = ::std::move(from);
  }

  inline EnabledCropList& operator=(const EnabledCropList& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnabledCropList& operator=(EnabledCropList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EnabledCropList& default_instance() {
    return *internal_default_instance();
  }
  static inline const EnabledCropList* internal_default_instance() {
    return reinterpret_cast<const EnabledCropList*>(
               &_EnabledCropList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(EnabledCropList& a, EnabledCropList& b) {
    a.Swap(&b);
  }
  inline void Swap(EnabledCropList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EnabledCropList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EnabledCropList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EnabledCropList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EnabledCropList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EnabledCropList& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnabledCropList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.EnabledCropList";
  }
  protected:
  explicit EnabledCropList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledCropsFieldNumber = 1,
  };
  // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
  int enabledcrops_size() const;
  private:
  int _internal_enabledcrops_size() const;
  public:
  void clear_enabledcrops();
  ::carbon::frontend::model::EnabledCrop* mutable_enabledcrops(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >*
      mutable_enabledcrops();
  private:
  const ::carbon::frontend::model::EnabledCrop& _internal_enabledcrops(int index) const;
  ::carbon::frontend::model::EnabledCrop* _internal_add_enabledcrops();
  public:
  const ::carbon::frontend::model::EnabledCrop& enabledcrops(int index) const;
  ::carbon::frontend::model::EnabledCrop* add_enabledcrops();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >&
      enabledcrops() const;

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.EnabledCropList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop > enabledcrops_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class GetNextSelectedCropIDResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.GetNextSelectedCropIDResponse) */ {
 public:
  inline GetNextSelectedCropIDResponse() : GetNextSelectedCropIDResponse(nullptr) {}
  ~GetNextSelectedCropIDResponse() override;
  explicit constexpr GetNextSelectedCropIDResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextSelectedCropIDResponse(const GetNextSelectedCropIDResponse& from);
  GetNextSelectedCropIDResponse(GetNextSelectedCropIDResponse&& from) noexcept
    : GetNextSelectedCropIDResponse() {
    *this = ::std::move(from);
  }

  inline GetNextSelectedCropIDResponse& operator=(const GetNextSelectedCropIDResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextSelectedCropIDResponse& operator=(GetNextSelectedCropIDResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextSelectedCropIDResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextSelectedCropIDResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextSelectedCropIDResponse*>(
               &_GetNextSelectedCropIDResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GetNextSelectedCropIDResponse& a, GetNextSelectedCropIDResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextSelectedCropIDResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextSelectedCropIDResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextSelectedCropIDResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextSelectedCropIDResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextSelectedCropIDResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextSelectedCropIDResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextSelectedCropIDResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.GetNextSelectedCropIDResponse";
  }
  protected:
  explicit GetNextSelectedCropIDResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCropIdFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // string crop_id = 2;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextSelectedCropIDResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class PinModelRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.PinModelRequest) */ {
 public:
  inline PinModelRequest() : PinModelRequest(nullptr) {}
  ~PinModelRequest() override;
  explicit constexpr PinModelRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PinModelRequest(const PinModelRequest& from);
  PinModelRequest(PinModelRequest&& from) noexcept
    : PinModelRequest() {
    *this = ::std::move(from);
  }

  inline PinModelRequest& operator=(const PinModelRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PinModelRequest& operator=(PinModelRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PinModelRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const PinModelRequest* internal_default_instance() {
    return reinterpret_cast<const PinModelRequest*>(
               &_PinModelRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(PinModelRequest& a, PinModelRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PinModelRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PinModelRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PinModelRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PinModelRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PinModelRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PinModelRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PinModelRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.PinModelRequest";
  }
  protected:
  explicit PinModelRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kCropFieldNumber = 2,
    kCropIdFieldNumber = 4,
    kAllowPinnedCropOverrideFieldNumber = 3,
    kP2PFieldNumber = 5,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string crop = 2 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_crop();
  PROTOBUF_DEPRECATED const std::string& crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_crop(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_crop();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_crop();
  PROTOBUF_DEPRECATED void set_allocated_crop(std::string* crop);
  private:
  const std::string& _internal_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop(const std::string& value);
  std::string* _internal_mutable_crop();
  public:

  // string crop_id = 4;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // bool allow_pinned_crop_override = 3;
  void clear_allow_pinned_crop_override();
  bool allow_pinned_crop_override() const;
  void set_allow_pinned_crop_override(bool value);
  private:
  bool _internal_allow_pinned_crop_override() const;
  void _internal_set_allow_pinned_crop_override(bool value);
  public:

  // bool p2p = 5;
  void clear_p2p();
  bool p2p() const;
  void set_p2p(bool value);
  private:
  bool _internal_p2p() const;
  void _internal_set_p2p(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.PinModelRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  bool allow_pinned_crop_override_;
  bool p2p_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class UnpinModelRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.UnpinModelRequest) */ {
 public:
  inline UnpinModelRequest() : UnpinModelRequest(nullptr) {}
  ~UnpinModelRequest() override;
  explicit constexpr UnpinModelRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UnpinModelRequest(const UnpinModelRequest& from);
  UnpinModelRequest(UnpinModelRequest&& from) noexcept
    : UnpinModelRequest() {
    *this = ::std::move(from);
  }

  inline UnpinModelRequest& operator=(const UnpinModelRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UnpinModelRequest& operator=(UnpinModelRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UnpinModelRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UnpinModelRequest* internal_default_instance() {
    return reinterpret_cast<const UnpinModelRequest*>(
               &_UnpinModelRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(UnpinModelRequest& a, UnpinModelRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UnpinModelRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UnpinModelRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UnpinModelRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UnpinModelRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UnpinModelRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UnpinModelRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UnpinModelRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.UnpinModelRequest";
  }
  protected:
  explicit UnpinModelRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCropFieldNumber = 1,
    kCropIdFieldNumber = 4,
    kP2PFieldNumber = 5,
  };
  // string crop = 1 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_crop();
  PROTOBUF_DEPRECATED const std::string& crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_crop(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_crop();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_crop();
  PROTOBUF_DEPRECATED void set_allocated_crop(std::string* crop);
  private:
  const std::string& _internal_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop(const std::string& value);
  std::string* _internal_mutable_crop();
  public:

  // string crop_id = 4;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // bool p2p = 5;
  void clear_p2p();
  bool p2p() const;
  void set_p2p(bool value);
  private:
  bool _internal_p2p() const;
  void _internal_set_p2p(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.UnpinModelRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  bool p2p_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class GetNextModelStateRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.GetNextModelStateRequest) */ {
 public:
  inline GetNextModelStateRequest() : GetNextModelStateRequest(nullptr) {}
  ~GetNextModelStateRequest() override;
  explicit constexpr GetNextModelStateRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextModelStateRequest(const GetNextModelStateRequest& from);
  GetNextModelStateRequest(GetNextModelStateRequest&& from) noexcept
    : GetNextModelStateRequest() {
    *this = ::std::move(from);
  }

  inline GetNextModelStateRequest& operator=(const GetNextModelStateRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextModelStateRequest& operator=(GetNextModelStateRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextModelStateRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextModelStateRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextModelStateRequest*>(
               &_GetNextModelStateRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(GetNextModelStateRequest& a, GetNextModelStateRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextModelStateRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextModelStateRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextModelStateRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextModelStateRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextModelStateRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextModelStateRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextModelStateRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.GetNextModelStateRequest";
  }
  protected:
  explicit GetNextModelStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCropFieldNumber = 1,
    kCropIdFieldNumber = 3,
    kTsFieldNumber = 2,
  };
  // string crop = 1 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_crop();
  PROTOBUF_DEPRECATED const std::string& crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_crop(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_crop();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_crop();
  PROTOBUF_DEPRECATED void set_allocated_crop(std::string* crop);
  private:
  const std::string& _internal_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop(const std::string& value);
  std::string* _internal_mutable_crop();
  public:

  // string crop_id = 3;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextModelStateRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class GetNextModelStateResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.GetNextModelStateResponse) */ {
 public:
  inline GetNextModelStateResponse() : GetNextModelStateResponse(nullptr) {}
  ~GetNextModelStateResponse() override;
  explicit constexpr GetNextModelStateResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextModelStateResponse(const GetNextModelStateResponse& from);
  GetNextModelStateResponse(GetNextModelStateResponse&& from) noexcept
    : GetNextModelStateResponse() {
    *this = ::std::move(from);
  }

  inline GetNextModelStateResponse& operator=(const GetNextModelStateResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextModelStateResponse& operator=(GetNextModelStateResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextModelStateResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextModelStateResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextModelStateResponse*>(
               &_GetNextModelStateResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GetNextModelStateResponse& a, GetNextModelStateResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextModelStateResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextModelStateResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextModelStateResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextModelStateResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextModelStateResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextModelStateResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextModelStateResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.GetNextModelStateResponse";
  }
  protected:
  explicit GetNextModelStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelsFieldNumber = 1,
    kCurrentP2PModelIdFieldNumber = 3,
    kCurrentDeepweedModelIdFieldNumber = 4,
    kTsFieldNumber = 2,
  };
  // repeated .carbon.frontend.model.Model models = 1;
  int models_size() const;
  private:
  int _internal_models_size() const;
  public:
  void clear_models();
  ::carbon::frontend::model::Model* mutable_models(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::Model >*
      mutable_models();
  private:
  const ::carbon::frontend::model::Model& _internal_models(int index) const;
  ::carbon::frontend::model::Model* _internal_add_models();
  public:
  const ::carbon::frontend::model::Model& models(int index) const;
  ::carbon::frontend::model::Model* add_models();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::Model >&
      models() const;

  // string current_p2p_model_id = 3;
  void clear_current_p2p_model_id();
  const std::string& current_p2p_model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_current_p2p_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_current_p2p_model_id();
  PROTOBUF_NODISCARD std::string* release_current_p2p_model_id();
  void set_allocated_current_p2p_model_id(std::string* current_p2p_model_id);
  private:
  const std::string& _internal_current_p2p_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_current_p2p_model_id(const std::string& value);
  std::string* _internal_mutable_current_p2p_model_id();
  public:

  // string current_deepweed_model_id = 4;
  void clear_current_deepweed_model_id();
  const std::string& current_deepweed_model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_current_deepweed_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_current_deepweed_model_id();
  PROTOBUF_NODISCARD std::string* release_current_deepweed_model_id();
  void set_allocated_current_deepweed_model_id(std::string* current_deepweed_model_id);
  private:
  const std::string& _internal_current_deepweed_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_current_deepweed_model_id(const std::string& value);
  std::string* _internal_mutable_current_deepweed_model_id();
  public:

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextModelStateResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::Model > models_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr current_p2p_model_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr current_deepweed_model_id_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class DownloadModelRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.DownloadModelRequest) */ {
 public:
  inline DownloadModelRequest() : DownloadModelRequest(nullptr) {}
  ~DownloadModelRequest() override;
  explicit constexpr DownloadModelRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DownloadModelRequest(const DownloadModelRequest& from);
  DownloadModelRequest(DownloadModelRequest&& from) noexcept
    : DownloadModelRequest() {
    *this = ::std::move(from);
  }

  inline DownloadModelRequest& operator=(const DownloadModelRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DownloadModelRequest& operator=(DownloadModelRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DownloadModelRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DownloadModelRequest* internal_default_instance() {
    return reinterpret_cast<const DownloadModelRequest*>(
               &_DownloadModelRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(DownloadModelRequest& a, DownloadModelRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DownloadModelRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DownloadModelRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DownloadModelRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DownloadModelRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DownloadModelRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DownloadModelRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DownloadModelRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.DownloadModelRequest";
  }
  protected:
  explicit DownloadModelRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelIdFieldNumber = 1,
  };
  // string model_id = 1;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_NODISCARD std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.DownloadModelRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class ModelHistoryRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.ModelHistoryRequest) */ {
 public:
  inline ModelHistoryRequest() : ModelHistoryRequest(nullptr) {}
  ~ModelHistoryRequest() override;
  explicit constexpr ModelHistoryRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelHistoryRequest(const ModelHistoryRequest& from);
  ModelHistoryRequest(ModelHistoryRequest&& from) noexcept
    : ModelHistoryRequest() {
    *this = ::std::move(from);
  }

  inline ModelHistoryRequest& operator=(const ModelHistoryRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelHistoryRequest& operator=(ModelHistoryRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelHistoryRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelHistoryRequest* internal_default_instance() {
    return reinterpret_cast<const ModelHistoryRequest*>(
               &_ModelHistoryRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(ModelHistoryRequest& a, ModelHistoryRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelHistoryRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelHistoryRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelHistoryRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelHistoryRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelHistoryRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModelHistoryRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelHistoryRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.ModelHistoryRequest";
  }
  protected:
  explicit ModelHistoryRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMatchFilterFieldNumber = 4,
    kTsFieldNumber = 5,
    kEventTypeMatcherFieldNumber = 6,
    kStartTimestampFieldNumber = 1,
    kCountFieldNumber = 2,
    kReverseFieldNumber = 3,
  };
  // .carbon.frontend.model.ModelEvent match_filter = 4;
  bool has_match_filter() const;
  private:
  bool _internal_has_match_filter() const;
  public:
  void clear_match_filter();
  const ::carbon::frontend::model::ModelEvent& match_filter() const;
  PROTOBUF_NODISCARD ::carbon::frontend::model::ModelEvent* release_match_filter();
  ::carbon::frontend::model::ModelEvent* mutable_match_filter();
  void set_allocated_match_filter(::carbon::frontend::model::ModelEvent* match_filter);
  private:
  const ::carbon::frontend::model::ModelEvent& _internal_match_filter() const;
  ::carbon::frontend::model::ModelEvent* _internal_mutable_match_filter();
  public:
  void unsafe_arena_set_allocated_match_filter(
      ::carbon::frontend::model::ModelEvent* match_filter);
  ::carbon::frontend::model::ModelEvent* unsafe_arena_release_match_filter();

  // .carbon.frontend.util.Timestamp ts = 5;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.model.ModelEventTypeMatcher event_type_matcher = 6;
  bool has_event_type_matcher() const;
  private:
  bool _internal_has_event_type_matcher() const;
  public:
  void clear_event_type_matcher();
  const ::carbon::frontend::model::ModelEventTypeMatcher& event_type_matcher() const;
  PROTOBUF_NODISCARD ::carbon::frontend::model::ModelEventTypeMatcher* release_event_type_matcher();
  ::carbon::frontend::model::ModelEventTypeMatcher* mutable_event_type_matcher();
  void set_allocated_event_type_matcher(::carbon::frontend::model::ModelEventTypeMatcher* event_type_matcher);
  private:
  const ::carbon::frontend::model::ModelEventTypeMatcher& _internal_event_type_matcher() const;
  ::carbon::frontend::model::ModelEventTypeMatcher* _internal_mutable_event_type_matcher();
  public:
  void unsafe_arena_set_allocated_event_type_matcher(
      ::carbon::frontend::model::ModelEventTypeMatcher* event_type_matcher);
  ::carbon::frontend::model::ModelEventTypeMatcher* unsafe_arena_release_event_type_matcher();

  // int64 start_timestamp = 1;
  void clear_start_timestamp();
  int64_t start_timestamp() const;
  void set_start_timestamp(int64_t value);
  private:
  int64_t _internal_start_timestamp() const;
  void _internal_set_start_timestamp(int64_t value);
  public:

  // int64 count = 2;
  void clear_count();
  int64_t count() const;
  void set_count(int64_t value);
  private:
  int64_t _internal_count() const;
  void _internal_set_count(int64_t value);
  public:

  // bool reverse = 3;
  void clear_reverse();
  bool reverse() const;
  void set_reverse(bool value);
  private:
  bool _internal_reverse() const;
  void _internal_set_reverse(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.ModelHistoryRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::model::ModelEvent* match_filter_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::model::ModelEventTypeMatcher* event_type_matcher_;
  int64_t start_timestamp_;
  int64_t count_;
  bool reverse_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class ModelEvent final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.ModelEvent) */ {
 public:
  inline ModelEvent() : ModelEvent(nullptr) {}
  ~ModelEvent() override;
  explicit constexpr ModelEvent(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelEvent(const ModelEvent& from);
  ModelEvent(ModelEvent&& from) noexcept
    : ModelEvent() {
    *this = ::std::move(from);
  }

  inline ModelEvent& operator=(const ModelEvent& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelEvent& operator=(ModelEvent&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelEvent& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelEvent* internal_default_instance() {
    return reinterpret_cast<const ModelEvent*>(
               &_ModelEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(ModelEvent& a, ModelEvent& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelEvent* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelEvent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelEvent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelEvent>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelEvent& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModelEvent& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelEvent* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.ModelEvent";
  }
  protected:
  explicit ModelEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypeFieldNumber = 1,
    kModelIdFieldNumber = 2,
    kModelTypeFieldNumber = 3,
    kCropIdFieldNumber = 4,
    kJobNameFieldNumber = 5,
    kModelNicknameFieldNumber = 7,
    kModelParametersFieldNumber = 8,
    kTimeFieldNumber = 6,
  };
  // string type = 1;
  void clear_type();
  const std::string& type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type();
  PROTOBUF_NODISCARD std::string* release_type();
  void set_allocated_type(std::string* type);
  private:
  const std::string& _internal_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type(const std::string& value);
  std::string* _internal_mutable_type();
  public:

  // string model_id = 2;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_NODISCARD std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // string model_type = 3;
  void clear_model_type();
  const std::string& model_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_type();
  PROTOBUF_NODISCARD std::string* release_model_type();
  void set_allocated_model_type(std::string* model_type);
  private:
  const std::string& _internal_model_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_type(const std::string& value);
  std::string* _internal_mutable_model_type();
  public:

  // string crop_id = 4;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // string job_name = 5;
  void clear_job_name();
  const std::string& job_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_job_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_job_name();
  PROTOBUF_NODISCARD std::string* release_job_name();
  void set_allocated_job_name(std::string* job_name);
  private:
  const std::string& _internal_job_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_job_name(const std::string& value);
  std::string* _internal_mutable_job_name();
  public:

  // string model_nickname = 7;
  void clear_model_nickname();
  const std::string& model_nickname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_nickname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_nickname();
  PROTOBUF_NODISCARD std::string* release_model_nickname();
  void set_allocated_model_nickname(std::string* model_nickname);
  private:
  const std::string& _internal_model_nickname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_nickname(const std::string& value);
  std::string* _internal_mutable_model_nickname();
  public:

  // string model_parameters = 8;
  void clear_model_parameters();
  const std::string& model_parameters() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_parameters(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_parameters();
  PROTOBUF_NODISCARD std::string* release_model_parameters();
  void set_allocated_model_parameters(std::string* model_parameters);
  private:
  const std::string& _internal_model_parameters() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_parameters(const std::string& value);
  std::string* _internal_mutable_model_parameters();
  public:

  // int64 time = 6;
  void clear_time();
  int64_t time() const;
  void set_time(int64_t value);
  private:
  int64_t _internal_time() const;
  void _internal_set_time(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.ModelEvent)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr job_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_nickname_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_parameters_;
  int64_t time_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class ModelEventTypeMatcher final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.ModelEventTypeMatcher) */ {
 public:
  inline ModelEventTypeMatcher() : ModelEventTypeMatcher(nullptr) {}
  ~ModelEventTypeMatcher() override;
  explicit constexpr ModelEventTypeMatcher(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelEventTypeMatcher(const ModelEventTypeMatcher& from);
  ModelEventTypeMatcher(ModelEventTypeMatcher&& from) noexcept
    : ModelEventTypeMatcher() {
    *this = ::std::move(from);
  }

  inline ModelEventTypeMatcher& operator=(const ModelEventTypeMatcher& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelEventTypeMatcher& operator=(ModelEventTypeMatcher&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelEventTypeMatcher& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelEventTypeMatcher* internal_default_instance() {
    return reinterpret_cast<const ModelEventTypeMatcher*>(
               &_ModelEventTypeMatcher_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(ModelEventTypeMatcher& a, ModelEventTypeMatcher& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelEventTypeMatcher* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelEventTypeMatcher* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelEventTypeMatcher* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelEventTypeMatcher>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelEventTypeMatcher& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModelEventTypeMatcher& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelEventTypeMatcher* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.ModelEventTypeMatcher";
  }
  protected:
  explicit ModelEventTypeMatcher(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotStartFieldNumber = 1,
    kPinnedFieldNumber = 2,
    kUnpinnedFieldNumber = 3,
    kRecommendedFieldNumber = 4,
    kActivatedFieldNumber = 5,
    kNicknameChangeFieldNumber = 6,
    kNicknameDeleteFieldNumber = 7,
    kDefaultParameterChangeFieldNumber = 8,
    kParameterChangeFieldNumber = 9,
  };
  // bool robot_start = 1;
  void clear_robot_start();
  bool robot_start() const;
  void set_robot_start(bool value);
  private:
  bool _internal_robot_start() const;
  void _internal_set_robot_start(bool value);
  public:

  // bool pinned = 2;
  void clear_pinned();
  bool pinned() const;
  void set_pinned(bool value);
  private:
  bool _internal_pinned() const;
  void _internal_set_pinned(bool value);
  public:

  // bool unpinned = 3;
  void clear_unpinned();
  bool unpinned() const;
  void set_unpinned(bool value);
  private:
  bool _internal_unpinned() const;
  void _internal_set_unpinned(bool value);
  public:

  // bool recommended = 4;
  void clear_recommended();
  bool recommended() const;
  void set_recommended(bool value);
  private:
  bool _internal_recommended() const;
  void _internal_set_recommended(bool value);
  public:

  // bool activated = 5;
  void clear_activated();
  bool activated() const;
  void set_activated(bool value);
  private:
  bool _internal_activated() const;
  void _internal_set_activated(bool value);
  public:

  // bool nickname_change = 6;
  void clear_nickname_change();
  bool nickname_change() const;
  void set_nickname_change(bool value);
  private:
  bool _internal_nickname_change() const;
  void _internal_set_nickname_change(bool value);
  public:

  // bool nickname_delete = 7;
  void clear_nickname_delete();
  bool nickname_delete() const;
  void set_nickname_delete(bool value);
  private:
  bool _internal_nickname_delete() const;
  void _internal_set_nickname_delete(bool value);
  public:

  // bool default_parameter_change = 8;
  void clear_default_parameter_change();
  bool default_parameter_change() const;
  void set_default_parameter_change(bool value);
  private:
  bool _internal_default_parameter_change() const;
  void _internal_set_default_parameter_change(bool value);
  public:

  // bool parameter_change = 9;
  void clear_parameter_change();
  bool parameter_change() const;
  void set_parameter_change(bool value);
  private:
  bool _internal_parameter_change() const;
  void _internal_set_parameter_change(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.ModelEventTypeMatcher)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool robot_start_;
  bool pinned_;
  bool unpinned_;
  bool recommended_;
  bool activated_;
  bool nickname_change_;
  bool nickname_delete_;
  bool default_parameter_change_;
  bool parameter_change_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class ModelHistoryResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.ModelHistoryResponse) */ {
 public:
  inline ModelHistoryResponse() : ModelHistoryResponse(nullptr) {}
  ~ModelHistoryResponse() override;
  explicit constexpr ModelHistoryResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelHistoryResponse(const ModelHistoryResponse& from);
  ModelHistoryResponse(ModelHistoryResponse&& from) noexcept
    : ModelHistoryResponse() {
    *this = ::std::move(from);
  }

  inline ModelHistoryResponse& operator=(const ModelHistoryResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelHistoryResponse& operator=(ModelHistoryResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelHistoryResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelHistoryResponse* internal_default_instance() {
    return reinterpret_cast<const ModelHistoryResponse*>(
               &_ModelHistoryResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(ModelHistoryResponse& a, ModelHistoryResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelHistoryResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelHistoryResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelHistoryResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelHistoryResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelHistoryResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModelHistoryResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelHistoryResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.ModelHistoryResponse";
  }
  protected:
  explicit ModelHistoryResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEventsFieldNumber = 1,
    kTsFieldNumber = 5,
  };
  // repeated .carbon.frontend.model.ModelEvent events = 1;
  int events_size() const;
  private:
  int _internal_events_size() const;
  public:
  void clear_events();
  ::carbon::frontend::model::ModelEvent* mutable_events(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::ModelEvent >*
      mutable_events();
  private:
  const ::carbon::frontend::model::ModelEvent& _internal_events(int index) const;
  ::carbon::frontend::model::ModelEvent* _internal_add_events();
  public:
  const ::carbon::frontend::model::ModelEvent& events(int index) const;
  ::carbon::frontend::model::ModelEvent* add_events();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::ModelEvent >&
      events() const;

  // .carbon.frontend.util.Timestamp ts = 5;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.ModelHistoryResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::ModelEvent > events_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class GetModelNicknamesRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.GetModelNicknamesRequest) */ {
 public:
  inline GetModelNicknamesRequest() : GetModelNicknamesRequest(nullptr) {}
  ~GetModelNicknamesRequest() override;
  explicit constexpr GetModelNicknamesRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetModelNicknamesRequest(const GetModelNicknamesRequest& from);
  GetModelNicknamesRequest(GetModelNicknamesRequest&& from) noexcept
    : GetModelNicknamesRequest() {
    *this = ::std::move(from);
  }

  inline GetModelNicknamesRequest& operator=(const GetModelNicknamesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetModelNicknamesRequest& operator=(GetModelNicknamesRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetModelNicknamesRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetModelNicknamesRequest* internal_default_instance() {
    return reinterpret_cast<const GetModelNicknamesRequest*>(
               &_GetModelNicknamesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(GetModelNicknamesRequest& a, GetModelNicknamesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetModelNicknamesRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetModelNicknamesRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetModelNicknamesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetModelNicknamesRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetModelNicknamesRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetModelNicknamesRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetModelNicknamesRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.GetModelNicknamesRequest";
  }
  protected:
  explicit GetModelNicknamesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelIdsFieldNumber = 1,
    kTsFieldNumber = 5,
  };
  // repeated string model_ids = 1;
  int model_ids_size() const;
  private:
  int _internal_model_ids_size() const;
  public:
  void clear_model_ids();
  const std::string& model_ids(int index) const;
  std::string* mutable_model_ids(int index);
  void set_model_ids(int index, const std::string& value);
  void set_model_ids(int index, std::string&& value);
  void set_model_ids(int index, const char* value);
  void set_model_ids(int index, const char* value, size_t size);
  std::string* add_model_ids();
  void add_model_ids(const std::string& value);
  void add_model_ids(std::string&& value);
  void add_model_ids(const char* value);
  void add_model_ids(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& model_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_model_ids();
  private:
  const std::string& _internal_model_ids(int index) const;
  std::string* _internal_add_model_ids();
  public:

  // .carbon.frontend.util.Timestamp ts = 5;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.GetModelNicknamesRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> model_ids_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse();
  explicit constexpr GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse& other);
  static const GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse*>(&_GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetModelNicknamesResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.GetModelNicknamesResponse) */ {
 public:
  inline GetModelNicknamesResponse() : GetModelNicknamesResponse(nullptr) {}
  ~GetModelNicknamesResponse() override;
  explicit constexpr GetModelNicknamesResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetModelNicknamesResponse(const GetModelNicknamesResponse& from);
  GetModelNicknamesResponse(GetModelNicknamesResponse&& from) noexcept
    : GetModelNicknamesResponse() {
    *this = ::std::move(from);
  }

  inline GetModelNicknamesResponse& operator=(const GetModelNicknamesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetModelNicknamesResponse& operator=(GetModelNicknamesResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetModelNicknamesResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetModelNicknamesResponse* internal_default_instance() {
    return reinterpret_cast<const GetModelNicknamesResponse*>(
               &_GetModelNicknamesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(GetModelNicknamesResponse& a, GetModelNicknamesResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetModelNicknamesResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetModelNicknamesResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetModelNicknamesResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetModelNicknamesResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetModelNicknamesResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetModelNicknamesResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetModelNicknamesResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.GetModelNicknamesResponse";
  }
  protected:
  explicit GetModelNicknamesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kModelNicknamesFieldNumber = 1,
    kTsFieldNumber = 5,
  };
  // map<string, string> model_nicknames = 1;
  int model_nicknames_size() const;
  private:
  int _internal_model_nicknames_size() const;
  public:
  void clear_model_nicknames();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_model_nicknames() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_model_nicknames();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      model_nicknames() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_model_nicknames();

  // .carbon.frontend.util.Timestamp ts = 5;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.GetModelNicknamesResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> model_nicknames_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class SetModelNicknameRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.SetModelNicknameRequest) */ {
 public:
  inline SetModelNicknameRequest() : SetModelNicknameRequest(nullptr) {}
  ~SetModelNicknameRequest() override;
  explicit constexpr SetModelNicknameRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetModelNicknameRequest(const SetModelNicknameRequest& from);
  SetModelNicknameRequest(SetModelNicknameRequest&& from) noexcept
    : SetModelNicknameRequest() {
    *this = ::std::move(from);
  }

  inline SetModelNicknameRequest& operator=(const SetModelNicknameRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetModelNicknameRequest& operator=(SetModelNicknameRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetModelNicknameRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetModelNicknameRequest* internal_default_instance() {
    return reinterpret_cast<const SetModelNicknameRequest*>(
               &_SetModelNicknameRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(SetModelNicknameRequest& a, SetModelNicknameRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetModelNicknameRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetModelNicknameRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetModelNicknameRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetModelNicknameRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetModelNicknameRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetModelNicknameRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetModelNicknameRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.SetModelNicknameRequest";
  }
  protected:
  explicit SetModelNicknameRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelIdFieldNumber = 1,
    kModelNicknameFieldNumber = 2,
  };
  // string model_id = 1;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_NODISCARD std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // string model_nickname = 2;
  void clear_model_nickname();
  const std::string& model_nickname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_nickname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_nickname();
  PROTOBUF_NODISCARD std::string* release_model_nickname();
  void set_allocated_model_nickname(std::string* model_nickname);
  private:
  const std::string& _internal_model_nickname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_nickname(const std::string& value);
  std::string* _internal_mutable_model_nickname();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.SetModelNicknameRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_nickname_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class CropModelPair final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.CropModelPair) */ {
 public:
  inline CropModelPair() : CropModelPair(nullptr) {}
  ~CropModelPair() override;
  explicit constexpr CropModelPair(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CropModelPair(const CropModelPair& from);
  CropModelPair(CropModelPair&& from) noexcept
    : CropModelPair() {
    *this = ::std::move(from);
  }

  inline CropModelPair& operator=(const CropModelPair& from) {
    CopyFrom(from);
    return *this;
  }
  inline CropModelPair& operator=(CropModelPair&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CropModelPair& default_instance() {
    return *internal_default_instance();
  }
  static inline const CropModelPair* internal_default_instance() {
    return reinterpret_cast<const CropModelPair*>(
               &_CropModelPair_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(CropModelPair& a, CropModelPair& b) {
    a.Swap(&b);
  }
  inline void Swap(CropModelPair* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CropModelPair* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CropModelPair* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CropModelPair>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CropModelPair& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CropModelPair& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CropModelPair* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.CropModelPair";
  }
  protected:
  explicit CropModelPair(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCropIdFieldNumber = 1,
    kModelIdFieldNumber = 2,
  };
  // string crop_id = 1;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // string model_id = 2;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_NODISCARD std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.CropModelPair)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class RefreshDefaultModelParametersRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.RefreshDefaultModelParametersRequest) */ {
 public:
  inline RefreshDefaultModelParametersRequest() : RefreshDefaultModelParametersRequest(nullptr) {}
  ~RefreshDefaultModelParametersRequest() override;
  explicit constexpr RefreshDefaultModelParametersRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RefreshDefaultModelParametersRequest(const RefreshDefaultModelParametersRequest& from);
  RefreshDefaultModelParametersRequest(RefreshDefaultModelParametersRequest&& from) noexcept
    : RefreshDefaultModelParametersRequest() {
    *this = ::std::move(from);
  }

  inline RefreshDefaultModelParametersRequest& operator=(const RefreshDefaultModelParametersRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RefreshDefaultModelParametersRequest& operator=(RefreshDefaultModelParametersRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RefreshDefaultModelParametersRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RefreshDefaultModelParametersRequest* internal_default_instance() {
    return reinterpret_cast<const RefreshDefaultModelParametersRequest*>(
               &_RefreshDefaultModelParametersRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(RefreshDefaultModelParametersRequest& a, RefreshDefaultModelParametersRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RefreshDefaultModelParametersRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RefreshDefaultModelParametersRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RefreshDefaultModelParametersRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RefreshDefaultModelParametersRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RefreshDefaultModelParametersRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RefreshDefaultModelParametersRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RefreshDefaultModelParametersRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.RefreshDefaultModelParametersRequest";
  }
  protected:
  explicit RefreshDefaultModelParametersRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCropModelPairsFieldNumber = 1,
  };
  // repeated .carbon.frontend.model.CropModelPair cropModelPairs = 1;
  int cropmodelpairs_size() const;
  private:
  int _internal_cropmodelpairs_size() const;
  public:
  void clear_cropmodelpairs();
  ::carbon::frontend::model::CropModelPair* mutable_cropmodelpairs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::CropModelPair >*
      mutable_cropmodelpairs();
  private:
  const ::carbon::frontend::model::CropModelPair& _internal_cropmodelpairs(int index) const;
  ::carbon::frontend::model::CropModelPair* _internal_add_cropmodelpairs();
  public:
  const ::carbon::frontend::model::CropModelPair& cropmodelpairs(int index) const;
  ::carbon::frontend::model::CropModelPair* add_cropmodelpairs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::CropModelPair >&
      cropmodelpairs() const;

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.RefreshDefaultModelParametersRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::CropModelPair > cropmodelpairs_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class SyncCropIDsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.SyncCropIDsRequest) */ {
 public:
  inline SyncCropIDsRequest() : SyncCropIDsRequest(nullptr) {}
  ~SyncCropIDsRequest() override;
  explicit constexpr SyncCropIDsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SyncCropIDsRequest(const SyncCropIDsRequest& from);
  SyncCropIDsRequest(SyncCropIDsRequest&& from) noexcept
    : SyncCropIDsRequest() {
    *this = ::std::move(from);
  }

  inline SyncCropIDsRequest& operator=(const SyncCropIDsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SyncCropIDsRequest& operator=(SyncCropIDsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SyncCropIDsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SyncCropIDsRequest* internal_default_instance() {
    return reinterpret_cast<const SyncCropIDsRequest*>(
               &_SyncCropIDsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(SyncCropIDsRequest& a, SyncCropIDsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SyncCropIDsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SyncCropIDsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SyncCropIDsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SyncCropIDsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SyncCropIDsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SyncCropIDsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SyncCropIDsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.SyncCropIDsRequest";
  }
  protected:
  explicit SyncCropIDsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kForceCacheRefreshFieldNumber = 1,
  };
  // bool force_cache_refresh = 1;
  void clear_force_cache_refresh();
  bool force_cache_refresh() const;
  void set_force_cache_refresh(bool value);
  private:
  bool _internal_force_cache_refresh() const;
  void _internal_set_force_cache_refresh(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.SyncCropIDsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool force_cache_refresh_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class GetNextEnabledCropsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.GetNextEnabledCropsRequest) */ {
 public:
  inline GetNextEnabledCropsRequest() : GetNextEnabledCropsRequest(nullptr) {}
  ~GetNextEnabledCropsRequest() override;
  explicit constexpr GetNextEnabledCropsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextEnabledCropsRequest(const GetNextEnabledCropsRequest& from);
  GetNextEnabledCropsRequest(GetNextEnabledCropsRequest&& from) noexcept
    : GetNextEnabledCropsRequest() {
    *this = ::std::move(from);
  }

  inline GetNextEnabledCropsRequest& operator=(const GetNextEnabledCropsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextEnabledCropsRequest& operator=(GetNextEnabledCropsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextEnabledCropsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextEnabledCropsRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextEnabledCropsRequest*>(
               &_GetNextEnabledCropsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(GetNextEnabledCropsRequest& a, GetNextEnabledCropsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextEnabledCropsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextEnabledCropsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextEnabledCropsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextEnabledCropsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextEnabledCropsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextEnabledCropsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextEnabledCropsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.GetNextEnabledCropsRequest";
  }
  protected:
  explicit GetNextEnabledCropsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLangFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // string lang = 2;
  void clear_lang();
  const std::string& lang() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_lang(ArgT0&& arg0, ArgT... args);
  std::string* mutable_lang();
  PROTOBUF_NODISCARD std::string* release_lang();
  void set_allocated_lang(std::string* lang);
  private:
  const std::string& _internal_lang() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_lang(const std::string& value);
  std::string* _internal_mutable_lang();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextEnabledCropsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr lang_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class GetNextEnabledCropsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.GetNextEnabledCropsResponse) */ {
 public:
  inline GetNextEnabledCropsResponse() : GetNextEnabledCropsResponse(nullptr) {}
  ~GetNextEnabledCropsResponse() override;
  explicit constexpr GetNextEnabledCropsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextEnabledCropsResponse(const GetNextEnabledCropsResponse& from);
  GetNextEnabledCropsResponse(GetNextEnabledCropsResponse&& from) noexcept
    : GetNextEnabledCropsResponse() {
    *this = ::std::move(from);
  }

  inline GetNextEnabledCropsResponse& operator=(const GetNextEnabledCropsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextEnabledCropsResponse& operator=(GetNextEnabledCropsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextEnabledCropsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextEnabledCropsResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextEnabledCropsResponse*>(
               &_GetNextEnabledCropsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(GetNextEnabledCropsResponse& a, GetNextEnabledCropsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextEnabledCropsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextEnabledCropsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextEnabledCropsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextEnabledCropsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextEnabledCropsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextEnabledCropsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextEnabledCropsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.GetNextEnabledCropsResponse";
  }
  protected:
  explicit GetNextEnabledCropsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledCropsFieldNumber = 1,
    kTsFieldNumber = 2,
  };
  // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
  int enabledcrops_size() const;
  private:
  int _internal_enabledcrops_size() const;
  public:
  void clear_enabledcrops();
  ::carbon::frontend::model::EnabledCrop* mutable_enabledcrops(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >*
      mutable_enabledcrops();
  private:
  const ::carbon::frontend::model::EnabledCrop& _internal_enabledcrops(int index) const;
  ::carbon::frontend::model::EnabledCrop* _internal_add_enabledcrops();
  public:
  const ::carbon::frontend::model::EnabledCrop& enabledcrops(int index) const;
  ::carbon::frontend::model::EnabledCrop* add_enabledcrops();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >&
      enabledcrops() const;

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextEnabledCropsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop > enabledcrops_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class GetNextCaptureCropsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.GetNextCaptureCropsRequest) */ {
 public:
  inline GetNextCaptureCropsRequest() : GetNextCaptureCropsRequest(nullptr) {}
  ~GetNextCaptureCropsRequest() override;
  explicit constexpr GetNextCaptureCropsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextCaptureCropsRequest(const GetNextCaptureCropsRequest& from);
  GetNextCaptureCropsRequest(GetNextCaptureCropsRequest&& from) noexcept
    : GetNextCaptureCropsRequest() {
    *this = ::std::move(from);
  }

  inline GetNextCaptureCropsRequest& operator=(const GetNextCaptureCropsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextCaptureCropsRequest& operator=(GetNextCaptureCropsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextCaptureCropsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextCaptureCropsRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextCaptureCropsRequest*>(
               &_GetNextCaptureCropsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(GetNextCaptureCropsRequest& a, GetNextCaptureCropsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextCaptureCropsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextCaptureCropsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextCaptureCropsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextCaptureCropsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextCaptureCropsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextCaptureCropsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextCaptureCropsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.GetNextCaptureCropsRequest";
  }
  protected:
  explicit GetNextCaptureCropsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLangFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // string lang = 2;
  void clear_lang();
  const std::string& lang() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_lang(ArgT0&& arg0, ArgT... args);
  std::string* mutable_lang();
  PROTOBUF_NODISCARD std::string* release_lang();
  void set_allocated_lang(std::string* lang);
  private:
  const std::string& _internal_lang() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_lang(const std::string& value);
  std::string* _internal_mutable_lang();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextCaptureCropsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr lang_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// -------------------------------------------------------------------

class GetNextCaptureCropsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.model.GetNextCaptureCropsResponse) */ {
 public:
  inline GetNextCaptureCropsResponse() : GetNextCaptureCropsResponse(nullptr) {}
  ~GetNextCaptureCropsResponse() override;
  explicit constexpr GetNextCaptureCropsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextCaptureCropsResponse(const GetNextCaptureCropsResponse& from);
  GetNextCaptureCropsResponse(GetNextCaptureCropsResponse&& from) noexcept
    : GetNextCaptureCropsResponse() {
    *this = ::std::move(from);
  }

  inline GetNextCaptureCropsResponse& operator=(const GetNextCaptureCropsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextCaptureCropsResponse& operator=(GetNextCaptureCropsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextCaptureCropsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextCaptureCropsResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextCaptureCropsResponse*>(
               &_GetNextCaptureCropsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(GetNextCaptureCropsResponse& a, GetNextCaptureCropsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextCaptureCropsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextCaptureCropsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextCaptureCropsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextCaptureCropsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextCaptureCropsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextCaptureCropsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextCaptureCropsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.model.GetNextCaptureCropsResponse";
  }
  protected:
  explicit GetNextCaptureCropsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledCropsFieldNumber = 1,
    kTsFieldNumber = 2,
  };
  // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
  int enabledcrops_size() const;
  private:
  int _internal_enabledcrops_size() const;
  public:
  void clear_enabledcrops();
  ::carbon::frontend::model::EnabledCrop* mutable_enabledcrops(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >*
      mutable_enabledcrops();
  private:
  const ::carbon::frontend::model::EnabledCrop& _internal_enabledcrops(int index) const;
  ::carbon::frontend::model::EnabledCrop* _internal_add_enabledcrops();
  public:
  const ::carbon::frontend::model::EnabledCrop& enabledcrops(int index) const;
  ::carbon::frontend::model::EnabledCrop* add_enabledcrops();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >&
      enabledcrops() const;

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextCaptureCropsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop > enabledcrops_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodel_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Model

// string id = 1;
inline void Model::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& Model::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Model::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.id)
}
inline std::string* Model::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.Model.id)
  return _s;
}
inline const std::string& Model::_internal_id() const {
  return id_.Get();
}
inline void Model::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Model::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Model::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.Model.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Model::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.Model.id)
}

// string crop = 2;
inline void Model::clear_crop() {
  crop_.ClearToEmpty();
}
inline const std::string& Model::crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.crop)
  return _internal_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Model::set_crop(ArgT0&& arg0, ArgT... args) {
 
 crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.crop)
}
inline std::string* Model::mutable_crop() {
  std::string* _s = _internal_mutable_crop();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.Model.crop)
  return _s;
}
inline const std::string& Model::_internal_crop() const {
  return crop_.Get();
}
inline void Model::_internal_set_crop(const std::string& value) {
  
  crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Model::_internal_mutable_crop() {
  
  return crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Model::release_crop() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.Model.crop)
  return crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Model::set_allocated_crop(std::string* crop) {
  if (crop != nullptr) {
    
  } else {
    
  }
  crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.Model.crop)
}

// .carbon.frontend.util.Timestamp ts = 3;
inline bool Model::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool Model::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& Model::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& Model::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.ts)
  return _internal_ts();
}
inline void Model::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.Model.ts)
}
inline ::carbon::frontend::util::Timestamp* Model::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Model::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.Model.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Model::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* Model::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.Model.ts)
  return _msg;
}
inline void Model::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.Model.ts)
}

// bool custom = 4;
inline void Model::clear_custom() {
  custom_ = false;
}
inline bool Model::_internal_custom() const {
  return custom_;
}
inline bool Model::custom() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.custom)
  return _internal_custom();
}
inline void Model::_internal_set_custom(bool value) {
  
  custom_ = value;
}
inline void Model::set_custom(bool value) {
  _internal_set_custom(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.custom)
}

// bool pinned = 5;
inline void Model::clear_pinned() {
  pinned_ = false;
}
inline bool Model::_internal_pinned() const {
  return pinned_;
}
inline bool Model::pinned() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.pinned)
  return _internal_pinned();
}
inline void Model::_internal_set_pinned(bool value) {
  
  pinned_ = value;
}
inline void Model::set_pinned(bool value) {
  _internal_set_pinned(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.pinned)
}

// bool active = 6;
inline void Model::clear_active() {
  active_ = false;
}
inline bool Model::_internal_active() const {
  return active_;
}
inline bool Model::active() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.active)
  return _internal_active();
}
inline void Model::_internal_set_active(bool value) {
  
  active_ = value;
}
inline void Model::set_active(bool value) {
  _internal_set_active(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.active)
}

// bool synced = 7;
inline void Model::clear_synced() {
  synced_ = false;
}
inline bool Model::_internal_synced() const {
  return synced_;
}
inline bool Model::synced() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.synced)
  return _internal_synced();
}
inline void Model::_internal_set_synced(bool value) {
  
  synced_ = value;
}
inline void Model::set_synced(bool value) {
  _internal_set_synced(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.synced)
}

// repeated bool synced_to_rows = 8;
inline int Model::_internal_synced_to_rows_size() const {
  return synced_to_rows_.size();
}
inline int Model::synced_to_rows_size() const {
  return _internal_synced_to_rows_size();
}
inline void Model::clear_synced_to_rows() {
  synced_to_rows_.Clear();
}
inline bool Model::_internal_synced_to_rows(int index) const {
  return synced_to_rows_.Get(index);
}
inline bool Model::synced_to_rows(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.synced_to_rows)
  return _internal_synced_to_rows(index);
}
inline void Model::set_synced_to_rows(int index, bool value) {
  synced_to_rows_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.synced_to_rows)
}
inline void Model::_internal_add_synced_to_rows(bool value) {
  synced_to_rows_.Add(value);
}
inline void Model::add_synced_to_rows(bool value) {
  _internal_add_synced_to_rows(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.model.Model.synced_to_rows)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
Model::_internal_synced_to_rows() const {
  return synced_to_rows_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
Model::synced_to_rows() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.model.Model.synced_to_rows)
  return _internal_synced_to_rows();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
Model::_internal_mutable_synced_to_rows() {
  return &synced_to_rows_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
Model::mutable_synced_to_rows() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.model.Model.synced_to_rows)
  return _internal_mutable_synced_to_rows();
}

// bool downloading = 9;
inline void Model::clear_downloading() {
  downloading_ = false;
}
inline bool Model::_internal_downloading() const {
  return downloading_;
}
inline bool Model::downloading() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.downloading)
  return _internal_downloading();
}
inline void Model::_internal_set_downloading(bool value) {
  
  downloading_ = value;
}
inline void Model::set_downloading(bool value) {
  _internal_set_downloading(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.downloading)
}

// string type = 10;
inline void Model::clear_type() {
  type_.ClearToEmpty();
}
inline const std::string& Model::type() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.type)
  return _internal_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Model::set_type(ArgT0&& arg0, ArgT... args) {
 
 type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.type)
}
inline std::string* Model::mutable_type() {
  std::string* _s = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.Model.type)
  return _s;
}
inline const std::string& Model::_internal_type() const {
  return type_.Get();
}
inline void Model::_internal_set_type(const std::string& value) {
  
  type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Model::_internal_mutable_type() {
  
  return type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Model::release_type() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.Model.type)
  return type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Model::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (type_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.Model.type)
}

// .carbon.frontend.util.Timestamp last_used_timestamp = 11;
inline bool Model::_internal_has_last_used_timestamp() const {
  return this != internal_default_instance() && last_used_timestamp_ != nullptr;
}
inline bool Model::has_last_used_timestamp() const {
  return _internal_has_last_used_timestamp();
}
inline const ::carbon::frontend::util::Timestamp& Model::_internal_last_used_timestamp() const {
  const ::carbon::frontend::util::Timestamp* p = last_used_timestamp_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& Model::last_used_timestamp() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.last_used_timestamp)
  return _internal_last_used_timestamp();
}
inline void Model::unsafe_arena_set_allocated_last_used_timestamp(
    ::carbon::frontend::util::Timestamp* last_used_timestamp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(last_used_timestamp_);
  }
  last_used_timestamp_ = last_used_timestamp;
  if (last_used_timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.Model.last_used_timestamp)
}
inline ::carbon::frontend::util::Timestamp* Model::release_last_used_timestamp() {
  
  ::carbon::frontend::util::Timestamp* temp = last_used_timestamp_;
  last_used_timestamp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Model::unsafe_arena_release_last_used_timestamp() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.Model.last_used_timestamp)
  
  ::carbon::frontend::util::Timestamp* temp = last_used_timestamp_;
  last_used_timestamp_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Model::_internal_mutable_last_used_timestamp() {
  
  if (last_used_timestamp_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    last_used_timestamp_ = p;
  }
  return last_used_timestamp_;
}
inline ::carbon::frontend::util::Timestamp* Model::mutable_last_used_timestamp() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_last_used_timestamp();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.Model.last_used_timestamp)
  return _msg;
}
inline void Model::set_allocated_last_used_timestamp(::carbon::frontend::util::Timestamp* last_used_timestamp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(last_used_timestamp_);
  }
  if (last_used_timestamp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(last_used_timestamp));
    if (message_arena != submessage_arena) {
      last_used_timestamp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, last_used_timestamp, submessage_arena);
    }
    
  } else {
    
  }
  last_used_timestamp_ = last_used_timestamp;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.Model.last_used_timestamp)
}

// float downloading_progress = 12;
inline void Model::clear_downloading_progress() {
  downloading_progress_ = 0;
}
inline float Model::_internal_downloading_progress() const {
  return downloading_progress_;
}
inline float Model::downloading_progress() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.downloading_progress)
  return _internal_downloading_progress();
}
inline void Model::_internal_set_downloading_progress(float value) {
  
  downloading_progress_ = value;
}
inline void Model::set_downloading_progress(float value) {
  _internal_set_downloading_progress(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.downloading_progress)
}

// uint64 estimated_downloading_remaining_time_ms = 13;
inline void Model::clear_estimated_downloading_remaining_time_ms() {
  estimated_downloading_remaining_time_ms_ = uint64_t{0u};
}
inline uint64_t Model::_internal_estimated_downloading_remaining_time_ms() const {
  return estimated_downloading_remaining_time_ms_;
}
inline uint64_t Model::estimated_downloading_remaining_time_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.estimated_downloading_remaining_time_ms)
  return _internal_estimated_downloading_remaining_time_ms();
}
inline void Model::_internal_set_estimated_downloading_remaining_time_ms(uint64_t value) {
  
  estimated_downloading_remaining_time_ms_ = value;
}
inline void Model::set_estimated_downloading_remaining_time_ms(uint64_t value) {
  _internal_set_estimated_downloading_remaining_time_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.estimated_downloading_remaining_time_ms)
}

// .carbon.frontend.util.Timestamp downloaded_timestamp = 14;
inline bool Model::_internal_has_downloaded_timestamp() const {
  return this != internal_default_instance() && downloaded_timestamp_ != nullptr;
}
inline bool Model::has_downloaded_timestamp() const {
  return _internal_has_downloaded_timestamp();
}
inline const ::carbon::frontend::util::Timestamp& Model::_internal_downloaded_timestamp() const {
  const ::carbon::frontend::util::Timestamp* p = downloaded_timestamp_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& Model::downloaded_timestamp() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.downloaded_timestamp)
  return _internal_downloaded_timestamp();
}
inline void Model::unsafe_arena_set_allocated_downloaded_timestamp(
    ::carbon::frontend::util::Timestamp* downloaded_timestamp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(downloaded_timestamp_);
  }
  downloaded_timestamp_ = downloaded_timestamp;
  if (downloaded_timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.Model.downloaded_timestamp)
}
inline ::carbon::frontend::util::Timestamp* Model::release_downloaded_timestamp() {
  
  ::carbon::frontend::util::Timestamp* temp = downloaded_timestamp_;
  downloaded_timestamp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Model::unsafe_arena_release_downloaded_timestamp() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.Model.downloaded_timestamp)
  
  ::carbon::frontend::util::Timestamp* temp = downloaded_timestamp_;
  downloaded_timestamp_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Model::_internal_mutable_downloaded_timestamp() {
  
  if (downloaded_timestamp_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    downloaded_timestamp_ = p;
  }
  return downloaded_timestamp_;
}
inline ::carbon::frontend::util::Timestamp* Model::mutable_downloaded_timestamp() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_downloaded_timestamp();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.Model.downloaded_timestamp)
  return _msg;
}
inline void Model::set_allocated_downloaded_timestamp(::carbon::frontend::util::Timestamp* downloaded_timestamp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(downloaded_timestamp_);
  }
  if (downloaded_timestamp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(downloaded_timestamp));
    if (message_arena != submessage_arena) {
      downloaded_timestamp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, downloaded_timestamp, submessage_arena);
    }
    
  } else {
    
  }
  downloaded_timestamp_ = downloaded_timestamp;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.Model.downloaded_timestamp)
}

// bool recommended = 15;
inline void Model::clear_recommended() {
  recommended_ = false;
}
inline bool Model::_internal_recommended() const {
  return recommended_;
}
inline bool Model::recommended() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.recommended)
  return _internal_recommended();
}
inline void Model::_internal_set_recommended(bool value) {
  
  recommended_ = value;
}
inline void Model::set_recommended(bool value) {
  _internal_set_recommended(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.recommended)
}

// repeated string viable_crop_ids = 16;
inline int Model::_internal_viable_crop_ids_size() const {
  return viable_crop_ids_.size();
}
inline int Model::viable_crop_ids_size() const {
  return _internal_viable_crop_ids_size();
}
inline void Model::clear_viable_crop_ids() {
  viable_crop_ids_.Clear();
}
inline std::string* Model::add_viable_crop_ids() {
  std::string* _s = _internal_add_viable_crop_ids();
  // @@protoc_insertion_point(field_add_mutable:carbon.frontend.model.Model.viable_crop_ids)
  return _s;
}
inline const std::string& Model::_internal_viable_crop_ids(int index) const {
  return viable_crop_ids_.Get(index);
}
inline const std::string& Model::viable_crop_ids(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.viable_crop_ids)
  return _internal_viable_crop_ids(index);
}
inline std::string* Model::mutable_viable_crop_ids(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.Model.viable_crop_ids)
  return viable_crop_ids_.Mutable(index);
}
inline void Model::set_viable_crop_ids(int index, const std::string& value) {
  viable_crop_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.viable_crop_ids)
}
inline void Model::set_viable_crop_ids(int index, std::string&& value) {
  viable_crop_ids_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.viable_crop_ids)
}
inline void Model::set_viable_crop_ids(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  viable_crop_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.frontend.model.Model.viable_crop_ids)
}
inline void Model::set_viable_crop_ids(int index, const char* value, size_t size) {
  viable_crop_ids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.frontend.model.Model.viable_crop_ids)
}
inline std::string* Model::_internal_add_viable_crop_ids() {
  return viable_crop_ids_.Add();
}
inline void Model::add_viable_crop_ids(const std::string& value) {
  viable_crop_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.model.Model.viable_crop_ids)
}
inline void Model::add_viable_crop_ids(std::string&& value) {
  viable_crop_ids_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.frontend.model.Model.viable_crop_ids)
}
inline void Model::add_viable_crop_ids(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  viable_crop_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.frontend.model.Model.viable_crop_ids)
}
inline void Model::add_viable_crop_ids(const char* value, size_t size) {
  viable_crop_ids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.frontend.model.Model.viable_crop_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
Model::viable_crop_ids() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.model.Model.viable_crop_ids)
  return viable_crop_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
Model::mutable_viable_crop_ids() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.model.Model.viable_crop_ids)
  return &viable_crop_ids_;
}

// bool maintained = 17;
inline void Model::clear_maintained() {
  maintained_ = false;
}
inline bool Model::_internal_maintained() const {
  return maintained_;
}
inline bool Model::maintained() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.maintained)
  return _internal_maintained();
}
inline void Model::_internal_set_maintained(bool value) {
  
  maintained_ = value;
}
inline void Model::set_maintained(bool value) {
  _internal_set_maintained(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.maintained)
}

// string nickname = 18;
inline void Model::clear_nickname() {
  nickname_.ClearToEmpty();
}
inline const std::string& Model::nickname() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.Model.nickname)
  return _internal_nickname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Model::set_nickname(ArgT0&& arg0, ArgT... args) {
 
 nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.Model.nickname)
}
inline std::string* Model::mutable_nickname() {
  std::string* _s = _internal_mutable_nickname();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.Model.nickname)
  return _s;
}
inline const std::string& Model::_internal_nickname() const {
  return nickname_.Get();
}
inline void Model::_internal_set_nickname(const std::string& value) {
  
  nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Model::_internal_mutable_nickname() {
  
  return nickname_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Model::release_nickname() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.Model.nickname)
  return nickname_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Model::set_allocated_nickname(std::string* nickname) {
  if (nickname != nullptr) {
    
  } else {
    
  }
  nickname_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), nickname,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (nickname_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.Model.nickname)
}

// -------------------------------------------------------------------

// SelectCropRequest

// string crop_id = 1;
inline void SelectCropRequest::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& SelectCropRequest::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.SelectCropRequest.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SelectCropRequest::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.SelectCropRequest.crop_id)
}
inline std::string* SelectCropRequest::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.SelectCropRequest.crop_id)
  return _s;
}
inline const std::string& SelectCropRequest::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void SelectCropRequest::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SelectCropRequest::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SelectCropRequest::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.SelectCropRequest.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SelectCropRequest::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.SelectCropRequest.crop_id)
}

// -------------------------------------------------------------------

// ListCropParameters

// string lang = 1;
inline void ListCropParameters::clear_lang() {
  lang_.ClearToEmpty();
}
inline const std::string& ListCropParameters::lang() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ListCropParameters.lang)
  return _internal_lang();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ListCropParameters::set_lang(ArgT0&& arg0, ArgT... args) {
 
 lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ListCropParameters.lang)
}
inline std::string* ListCropParameters::mutable_lang() {
  std::string* _s = _internal_mutable_lang();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ListCropParameters.lang)
  return _s;
}
inline const std::string& ListCropParameters::_internal_lang() const {
  return lang_.Get();
}
inline void ListCropParameters::_internal_set_lang(const std::string& value) {
  
  lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ListCropParameters::_internal_mutable_lang() {
  
  return lang_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ListCropParameters::release_lang() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ListCropParameters.lang)
  return lang_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ListCropParameters::set_allocated_lang(std::string* lang) {
  if (lang != nullptr) {
    
  } else {
    
  }
  lang_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), lang,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (lang_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ListCropParameters.lang)
}

// -------------------------------------------------------------------

// EnabledCrop

// string id = 1;
inline void EnabledCrop::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& EnabledCrop::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.EnabledCrop.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnabledCrop::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.EnabledCrop.id)
}
inline std::string* EnabledCrop::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.EnabledCrop.id)
  return _s;
}
inline const std::string& EnabledCrop::_internal_id() const {
  return id_.Get();
}
inline void EnabledCrop::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnabledCrop::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnabledCrop::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.EnabledCrop.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnabledCrop::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.EnabledCrop.id)
}

// int64 created = 2;
inline void EnabledCrop::clear_created() {
  created_ = int64_t{0};
}
inline int64_t EnabledCrop::_internal_created() const {
  return created_;
}
inline int64_t EnabledCrop::created() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.EnabledCrop.created)
  return _internal_created();
}
inline void EnabledCrop::_internal_set_created(int64_t value) {
  
  created_ = value;
}
inline void EnabledCrop::set_created(int64_t value) {
  _internal_set_created(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.EnabledCrop.created)
}

// string carbon_name = 3 [deprecated = true];
inline void EnabledCrop::clear_carbon_name() {
  carbon_name_.ClearToEmpty();
}
inline const std::string& EnabledCrop::carbon_name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.EnabledCrop.carbon_name)
  return _internal_carbon_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnabledCrop::set_carbon_name(ArgT0&& arg0, ArgT... args) {
 
 carbon_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.EnabledCrop.carbon_name)
}
inline std::string* EnabledCrop::mutable_carbon_name() {
  std::string* _s = _internal_mutable_carbon_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.EnabledCrop.carbon_name)
  return _s;
}
inline const std::string& EnabledCrop::_internal_carbon_name() const {
  return carbon_name_.Get();
}
inline void EnabledCrop::_internal_set_carbon_name(const std::string& value) {
  
  carbon_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnabledCrop::_internal_mutable_carbon_name() {
  
  return carbon_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnabledCrop::release_carbon_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.EnabledCrop.carbon_name)
  return carbon_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnabledCrop::set_allocated_carbon_name(std::string* carbon_name) {
  if (carbon_name != nullptr) {
    
  } else {
    
  }
  carbon_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), carbon_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (carbon_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    carbon_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.EnabledCrop.carbon_name)
}

// string common_name = 4;
inline void EnabledCrop::clear_common_name() {
  common_name_.ClearToEmpty();
}
inline const std::string& EnabledCrop::common_name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.EnabledCrop.common_name)
  return _internal_common_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnabledCrop::set_common_name(ArgT0&& arg0, ArgT... args) {
 
 common_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.EnabledCrop.common_name)
}
inline std::string* EnabledCrop::mutable_common_name() {
  std::string* _s = _internal_mutable_common_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.EnabledCrop.common_name)
  return _s;
}
inline const std::string& EnabledCrop::_internal_common_name() const {
  return common_name_.Get();
}
inline void EnabledCrop::_internal_set_common_name(const std::string& value) {
  
  common_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnabledCrop::_internal_mutable_common_name() {
  
  return common_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnabledCrop::release_common_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.EnabledCrop.common_name)
  return common_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnabledCrop::set_allocated_common_name(std::string* common_name) {
  if (common_name != nullptr) {
    
  } else {
    
  }
  common_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), common_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (common_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    common_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.EnabledCrop.common_name)
}

// string description = 5;
inline void EnabledCrop::clear_description() {
  description_.ClearToEmpty();
}
inline const std::string& EnabledCrop::description() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.EnabledCrop.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnabledCrop::set_description(ArgT0&& arg0, ArgT... args) {
 
 description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.EnabledCrop.description)
}
inline std::string* EnabledCrop::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.EnabledCrop.description)
  return _s;
}
inline const std::string& EnabledCrop::_internal_description() const {
  return description_.Get();
}
inline void EnabledCrop::_internal_set_description(const std::string& value) {
  
  description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnabledCrop::_internal_mutable_description() {
  
  return description_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnabledCrop::release_description() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.EnabledCrop.description)
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnabledCrop::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (description_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.EnabledCrop.description)
}

// string notes = 6;
inline void EnabledCrop::clear_notes() {
  notes_.ClearToEmpty();
}
inline const std::string& EnabledCrop::notes() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.EnabledCrop.notes)
  return _internal_notes();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnabledCrop::set_notes(ArgT0&& arg0, ArgT... args) {
 
 notes_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.EnabledCrop.notes)
}
inline std::string* EnabledCrop::mutable_notes() {
  std::string* _s = _internal_mutable_notes();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.EnabledCrop.notes)
  return _s;
}
inline const std::string& EnabledCrop::_internal_notes() const {
  return notes_.Get();
}
inline void EnabledCrop::_internal_set_notes(const std::string& value) {
  
  notes_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnabledCrop::_internal_mutable_notes() {
  
  return notes_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnabledCrop::release_notes() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.EnabledCrop.notes)
  return notes_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnabledCrop::set_allocated_notes(std::string* notes) {
  if (notes != nullptr) {
    
  } else {
    
  }
  notes_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), notes,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (notes_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    notes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.EnabledCrop.notes)
}

// string pinned_model_id = 7;
inline void EnabledCrop::clear_pinned_model_id() {
  pinned_model_id_.ClearToEmpty();
}
inline const std::string& EnabledCrop::pinned_model_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.EnabledCrop.pinned_model_id)
  return _internal_pinned_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnabledCrop::set_pinned_model_id(ArgT0&& arg0, ArgT... args) {
 
 pinned_model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.EnabledCrop.pinned_model_id)
}
inline std::string* EnabledCrop::mutable_pinned_model_id() {
  std::string* _s = _internal_mutable_pinned_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.EnabledCrop.pinned_model_id)
  return _s;
}
inline const std::string& EnabledCrop::_internal_pinned_model_id() const {
  return pinned_model_id_.Get();
}
inline void EnabledCrop::_internal_set_pinned_model_id(const std::string& value) {
  
  pinned_model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnabledCrop::_internal_mutable_pinned_model_id() {
  
  return pinned_model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnabledCrop::release_pinned_model_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.EnabledCrop.pinned_model_id)
  return pinned_model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnabledCrop::set_allocated_pinned_model_id(std::string* pinned_model_id) {
  if (pinned_model_id != nullptr) {
    
  } else {
    
  }
  pinned_model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), pinned_model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (pinned_model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    pinned_model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.EnabledCrop.pinned_model_id)
}

// string recommended_model = 8;
inline void EnabledCrop::clear_recommended_model() {
  recommended_model_.ClearToEmpty();
}
inline const std::string& EnabledCrop::recommended_model() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.EnabledCrop.recommended_model)
  return _internal_recommended_model();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnabledCrop::set_recommended_model(ArgT0&& arg0, ArgT... args) {
 
 recommended_model_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.EnabledCrop.recommended_model)
}
inline std::string* EnabledCrop::mutable_recommended_model() {
  std::string* _s = _internal_mutable_recommended_model();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.EnabledCrop.recommended_model)
  return _s;
}
inline const std::string& EnabledCrop::_internal_recommended_model() const {
  return recommended_model_.Get();
}
inline void EnabledCrop::_internal_set_recommended_model(const std::string& value) {
  
  recommended_model_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EnabledCrop::_internal_mutable_recommended_model() {
  
  return recommended_model_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EnabledCrop::release_recommended_model() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.EnabledCrop.recommended_model)
  return recommended_model_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EnabledCrop::set_allocated_recommended_model(std::string* recommended_model) {
  if (recommended_model != nullptr) {
    
  } else {
    
  }
  recommended_model_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), recommended_model,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (recommended_model_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    recommended_model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.EnabledCrop.recommended_model)
}

// -------------------------------------------------------------------

// EnabledCropList

// repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
inline int EnabledCropList::_internal_enabledcrops_size() const {
  return enabledcrops_.size();
}
inline int EnabledCropList::enabledcrops_size() const {
  return _internal_enabledcrops_size();
}
inline void EnabledCropList::clear_enabledcrops() {
  enabledcrops_.Clear();
}
inline ::carbon::frontend::model::EnabledCrop* EnabledCropList::mutable_enabledcrops(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.EnabledCropList.enabledCrops)
  return enabledcrops_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >*
EnabledCropList::mutable_enabledcrops() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.model.EnabledCropList.enabledCrops)
  return &enabledcrops_;
}
inline const ::carbon::frontend::model::EnabledCrop& EnabledCropList::_internal_enabledcrops(int index) const {
  return enabledcrops_.Get(index);
}
inline const ::carbon::frontend::model::EnabledCrop& EnabledCropList::enabledcrops(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.EnabledCropList.enabledCrops)
  return _internal_enabledcrops(index);
}
inline ::carbon::frontend::model::EnabledCrop* EnabledCropList::_internal_add_enabledcrops() {
  return enabledcrops_.Add();
}
inline ::carbon::frontend::model::EnabledCrop* EnabledCropList::add_enabledcrops() {
  ::carbon::frontend::model::EnabledCrop* _add = _internal_add_enabledcrops();
  // @@protoc_insertion_point(field_add:carbon.frontend.model.EnabledCropList.enabledCrops)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >&
EnabledCropList::enabledcrops() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.model.EnabledCropList.enabledCrops)
  return enabledcrops_;
}

// -------------------------------------------------------------------

// GetNextSelectedCropIDResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextSelectedCropIDResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextSelectedCropIDResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextSelectedCropIDResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextSelectedCropIDResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextSelectedCropIDResponse.ts)
  return _internal_ts();
}
inline void GetNextSelectedCropIDResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.GetNextSelectedCropIDResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextSelectedCropIDResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextSelectedCropIDResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextSelectedCropIDResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextSelectedCropIDResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextSelectedCropIDResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextSelectedCropIDResponse.ts)
  return _msg;
}
inline void GetNextSelectedCropIDResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextSelectedCropIDResponse.ts)
}

// string crop_id = 2;
inline void GetNextSelectedCropIDResponse::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& GetNextSelectedCropIDResponse::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextSelectedCropIDResponse.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextSelectedCropIDResponse::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.GetNextSelectedCropIDResponse.crop_id)
}
inline std::string* GetNextSelectedCropIDResponse::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextSelectedCropIDResponse.crop_id)
  return _s;
}
inline const std::string& GetNextSelectedCropIDResponse::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void GetNextSelectedCropIDResponse::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextSelectedCropIDResponse::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextSelectedCropIDResponse::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextSelectedCropIDResponse.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextSelectedCropIDResponse::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextSelectedCropIDResponse.crop_id)
}

// -------------------------------------------------------------------

// PinModelRequest

// string id = 1;
inline void PinModelRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& PinModelRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.PinModelRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PinModelRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.PinModelRequest.id)
}
inline std::string* PinModelRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.PinModelRequest.id)
  return _s;
}
inline const std::string& PinModelRequest::_internal_id() const {
  return id_.Get();
}
inline void PinModelRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PinModelRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PinModelRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.PinModelRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PinModelRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.PinModelRequest.id)
}

// string crop = 2 [deprecated = true];
inline void PinModelRequest::clear_crop() {
  crop_.ClearToEmpty();
}
inline const std::string& PinModelRequest::crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.PinModelRequest.crop)
  return _internal_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PinModelRequest::set_crop(ArgT0&& arg0, ArgT... args) {
 
 crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.PinModelRequest.crop)
}
inline std::string* PinModelRequest::mutable_crop() {
  std::string* _s = _internal_mutable_crop();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.PinModelRequest.crop)
  return _s;
}
inline const std::string& PinModelRequest::_internal_crop() const {
  return crop_.Get();
}
inline void PinModelRequest::_internal_set_crop(const std::string& value) {
  
  crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PinModelRequest::_internal_mutable_crop() {
  
  return crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PinModelRequest::release_crop() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.PinModelRequest.crop)
  return crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PinModelRequest::set_allocated_crop(std::string* crop) {
  if (crop != nullptr) {
    
  } else {
    
  }
  crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.PinModelRequest.crop)
}

// bool allow_pinned_crop_override = 3;
inline void PinModelRequest::clear_allow_pinned_crop_override() {
  allow_pinned_crop_override_ = false;
}
inline bool PinModelRequest::_internal_allow_pinned_crop_override() const {
  return allow_pinned_crop_override_;
}
inline bool PinModelRequest::allow_pinned_crop_override() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.PinModelRequest.allow_pinned_crop_override)
  return _internal_allow_pinned_crop_override();
}
inline void PinModelRequest::_internal_set_allow_pinned_crop_override(bool value) {
  
  allow_pinned_crop_override_ = value;
}
inline void PinModelRequest::set_allow_pinned_crop_override(bool value) {
  _internal_set_allow_pinned_crop_override(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.PinModelRequest.allow_pinned_crop_override)
}

// string crop_id = 4;
inline void PinModelRequest::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& PinModelRequest::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.PinModelRequest.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PinModelRequest::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.PinModelRequest.crop_id)
}
inline std::string* PinModelRequest::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.PinModelRequest.crop_id)
  return _s;
}
inline const std::string& PinModelRequest::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void PinModelRequest::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PinModelRequest::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PinModelRequest::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.PinModelRequest.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PinModelRequest::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.PinModelRequest.crop_id)
}

// bool p2p = 5;
inline void PinModelRequest::clear_p2p() {
  p2p_ = false;
}
inline bool PinModelRequest::_internal_p2p() const {
  return p2p_;
}
inline bool PinModelRequest::p2p() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.PinModelRequest.p2p)
  return _internal_p2p();
}
inline void PinModelRequest::_internal_set_p2p(bool value) {
  
  p2p_ = value;
}
inline void PinModelRequest::set_p2p(bool value) {
  _internal_set_p2p(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.PinModelRequest.p2p)
}

// -------------------------------------------------------------------

// UnpinModelRequest

// string crop = 1 [deprecated = true];
inline void UnpinModelRequest::clear_crop() {
  crop_.ClearToEmpty();
}
inline const std::string& UnpinModelRequest::crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.UnpinModelRequest.crop)
  return _internal_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UnpinModelRequest::set_crop(ArgT0&& arg0, ArgT... args) {
 
 crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.UnpinModelRequest.crop)
}
inline std::string* UnpinModelRequest::mutable_crop() {
  std::string* _s = _internal_mutable_crop();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.UnpinModelRequest.crop)
  return _s;
}
inline const std::string& UnpinModelRequest::_internal_crop() const {
  return crop_.Get();
}
inline void UnpinModelRequest::_internal_set_crop(const std::string& value) {
  
  crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UnpinModelRequest::_internal_mutable_crop() {
  
  return crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UnpinModelRequest::release_crop() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.UnpinModelRequest.crop)
  return crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UnpinModelRequest::set_allocated_crop(std::string* crop) {
  if (crop != nullptr) {
    
  } else {
    
  }
  crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.UnpinModelRequest.crop)
}

// string crop_id = 4;
inline void UnpinModelRequest::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& UnpinModelRequest::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.UnpinModelRequest.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UnpinModelRequest::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.UnpinModelRequest.crop_id)
}
inline std::string* UnpinModelRequest::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.UnpinModelRequest.crop_id)
  return _s;
}
inline const std::string& UnpinModelRequest::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void UnpinModelRequest::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UnpinModelRequest::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UnpinModelRequest::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.UnpinModelRequest.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UnpinModelRequest::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.UnpinModelRequest.crop_id)
}

// bool p2p = 5;
inline void UnpinModelRequest::clear_p2p() {
  p2p_ = false;
}
inline bool UnpinModelRequest::_internal_p2p() const {
  return p2p_;
}
inline bool UnpinModelRequest::p2p() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.UnpinModelRequest.p2p)
  return _internal_p2p();
}
inline void UnpinModelRequest::_internal_set_p2p(bool value) {
  
  p2p_ = value;
}
inline void UnpinModelRequest::set_p2p(bool value) {
  _internal_set_p2p(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.UnpinModelRequest.p2p)
}

// -------------------------------------------------------------------

// GetNextModelStateRequest

// string crop = 1 [deprecated = true];
inline void GetNextModelStateRequest::clear_crop() {
  crop_.ClearToEmpty();
}
inline const std::string& GetNextModelStateRequest::crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextModelStateRequest.crop)
  return _internal_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextModelStateRequest::set_crop(ArgT0&& arg0, ArgT... args) {
 
 crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.GetNextModelStateRequest.crop)
}
inline std::string* GetNextModelStateRequest::mutable_crop() {
  std::string* _s = _internal_mutable_crop();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextModelStateRequest.crop)
  return _s;
}
inline const std::string& GetNextModelStateRequest::_internal_crop() const {
  return crop_.Get();
}
inline void GetNextModelStateRequest::_internal_set_crop(const std::string& value) {
  
  crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextModelStateRequest::_internal_mutable_crop() {
  
  return crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextModelStateRequest::release_crop() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextModelStateRequest.crop)
  return crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextModelStateRequest::set_allocated_crop(std::string* crop) {
  if (crop != nullptr) {
    
  } else {
    
  }
  crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextModelStateRequest.crop)
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool GetNextModelStateRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextModelStateRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextModelStateRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextModelStateRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextModelStateRequest.ts)
  return _internal_ts();
}
inline void GetNextModelStateRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.GetNextModelStateRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextModelStateRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextModelStateRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextModelStateRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextModelStateRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextModelStateRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextModelStateRequest.ts)
  return _msg;
}
inline void GetNextModelStateRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextModelStateRequest.ts)
}

// string crop_id = 3;
inline void GetNextModelStateRequest::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& GetNextModelStateRequest::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextModelStateRequest.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextModelStateRequest::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.GetNextModelStateRequest.crop_id)
}
inline std::string* GetNextModelStateRequest::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextModelStateRequest.crop_id)
  return _s;
}
inline const std::string& GetNextModelStateRequest::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void GetNextModelStateRequest::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextModelStateRequest::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextModelStateRequest::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextModelStateRequest.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextModelStateRequest::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextModelStateRequest.crop_id)
}

// -------------------------------------------------------------------

// GetNextModelStateResponse

// repeated .carbon.frontend.model.Model models = 1;
inline int GetNextModelStateResponse::_internal_models_size() const {
  return models_.size();
}
inline int GetNextModelStateResponse::models_size() const {
  return _internal_models_size();
}
inline void GetNextModelStateResponse::clear_models() {
  models_.Clear();
}
inline ::carbon::frontend::model::Model* GetNextModelStateResponse::mutable_models(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextModelStateResponse.models)
  return models_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::Model >*
GetNextModelStateResponse::mutable_models() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.model.GetNextModelStateResponse.models)
  return &models_;
}
inline const ::carbon::frontend::model::Model& GetNextModelStateResponse::_internal_models(int index) const {
  return models_.Get(index);
}
inline const ::carbon::frontend::model::Model& GetNextModelStateResponse::models(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextModelStateResponse.models)
  return _internal_models(index);
}
inline ::carbon::frontend::model::Model* GetNextModelStateResponse::_internal_add_models() {
  return models_.Add();
}
inline ::carbon::frontend::model::Model* GetNextModelStateResponse::add_models() {
  ::carbon::frontend::model::Model* _add = _internal_add_models();
  // @@protoc_insertion_point(field_add:carbon.frontend.model.GetNextModelStateResponse.models)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::Model >&
GetNextModelStateResponse::models() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.model.GetNextModelStateResponse.models)
  return models_;
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool GetNextModelStateResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextModelStateResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextModelStateResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextModelStateResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextModelStateResponse.ts)
  return _internal_ts();
}
inline void GetNextModelStateResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.GetNextModelStateResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextModelStateResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextModelStateResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextModelStateResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextModelStateResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextModelStateResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextModelStateResponse.ts)
  return _msg;
}
inline void GetNextModelStateResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextModelStateResponse.ts)
}

// string current_p2p_model_id = 3;
inline void GetNextModelStateResponse::clear_current_p2p_model_id() {
  current_p2p_model_id_.ClearToEmpty();
}
inline const std::string& GetNextModelStateResponse::current_p2p_model_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextModelStateResponse.current_p2p_model_id)
  return _internal_current_p2p_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextModelStateResponse::set_current_p2p_model_id(ArgT0&& arg0, ArgT... args) {
 
 current_p2p_model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.GetNextModelStateResponse.current_p2p_model_id)
}
inline std::string* GetNextModelStateResponse::mutable_current_p2p_model_id() {
  std::string* _s = _internal_mutable_current_p2p_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextModelStateResponse.current_p2p_model_id)
  return _s;
}
inline const std::string& GetNextModelStateResponse::_internal_current_p2p_model_id() const {
  return current_p2p_model_id_.Get();
}
inline void GetNextModelStateResponse::_internal_set_current_p2p_model_id(const std::string& value) {
  
  current_p2p_model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextModelStateResponse::_internal_mutable_current_p2p_model_id() {
  
  return current_p2p_model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextModelStateResponse::release_current_p2p_model_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextModelStateResponse.current_p2p_model_id)
  return current_p2p_model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextModelStateResponse::set_allocated_current_p2p_model_id(std::string* current_p2p_model_id) {
  if (current_p2p_model_id != nullptr) {
    
  } else {
    
  }
  current_p2p_model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), current_p2p_model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (current_p2p_model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    current_p2p_model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextModelStateResponse.current_p2p_model_id)
}

// string current_deepweed_model_id = 4;
inline void GetNextModelStateResponse::clear_current_deepweed_model_id() {
  current_deepweed_model_id_.ClearToEmpty();
}
inline const std::string& GetNextModelStateResponse::current_deepweed_model_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextModelStateResponse.current_deepweed_model_id)
  return _internal_current_deepweed_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextModelStateResponse::set_current_deepweed_model_id(ArgT0&& arg0, ArgT... args) {
 
 current_deepweed_model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.GetNextModelStateResponse.current_deepweed_model_id)
}
inline std::string* GetNextModelStateResponse::mutable_current_deepweed_model_id() {
  std::string* _s = _internal_mutable_current_deepweed_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextModelStateResponse.current_deepweed_model_id)
  return _s;
}
inline const std::string& GetNextModelStateResponse::_internal_current_deepweed_model_id() const {
  return current_deepweed_model_id_.Get();
}
inline void GetNextModelStateResponse::_internal_set_current_deepweed_model_id(const std::string& value) {
  
  current_deepweed_model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextModelStateResponse::_internal_mutable_current_deepweed_model_id() {
  
  return current_deepweed_model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextModelStateResponse::release_current_deepweed_model_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextModelStateResponse.current_deepweed_model_id)
  return current_deepweed_model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextModelStateResponse::set_allocated_current_deepweed_model_id(std::string* current_deepweed_model_id) {
  if (current_deepweed_model_id != nullptr) {
    
  } else {
    
  }
  current_deepweed_model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), current_deepweed_model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (current_deepweed_model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    current_deepweed_model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextModelStateResponse.current_deepweed_model_id)
}

// -------------------------------------------------------------------

// DownloadModelRequest

// string model_id = 1;
inline void DownloadModelRequest::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& DownloadModelRequest::model_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.DownloadModelRequest.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DownloadModelRequest::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.DownloadModelRequest.model_id)
}
inline std::string* DownloadModelRequest::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.DownloadModelRequest.model_id)
  return _s;
}
inline const std::string& DownloadModelRequest::_internal_model_id() const {
  return model_id_.Get();
}
inline void DownloadModelRequest::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DownloadModelRequest::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DownloadModelRequest::release_model_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.DownloadModelRequest.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DownloadModelRequest::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.DownloadModelRequest.model_id)
}

// -------------------------------------------------------------------

// ModelHistoryRequest

// int64 start_timestamp = 1;
inline void ModelHistoryRequest::clear_start_timestamp() {
  start_timestamp_ = int64_t{0};
}
inline int64_t ModelHistoryRequest::_internal_start_timestamp() const {
  return start_timestamp_;
}
inline int64_t ModelHistoryRequest::start_timestamp() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelHistoryRequest.start_timestamp)
  return _internal_start_timestamp();
}
inline void ModelHistoryRequest::_internal_set_start_timestamp(int64_t value) {
  
  start_timestamp_ = value;
}
inline void ModelHistoryRequest::set_start_timestamp(int64_t value) {
  _internal_set_start_timestamp(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelHistoryRequest.start_timestamp)
}

// int64 count = 2;
inline void ModelHistoryRequest::clear_count() {
  count_ = int64_t{0};
}
inline int64_t ModelHistoryRequest::_internal_count() const {
  return count_;
}
inline int64_t ModelHistoryRequest::count() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelHistoryRequest.count)
  return _internal_count();
}
inline void ModelHistoryRequest::_internal_set_count(int64_t value) {
  
  count_ = value;
}
inline void ModelHistoryRequest::set_count(int64_t value) {
  _internal_set_count(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelHistoryRequest.count)
}

// bool reverse = 3;
inline void ModelHistoryRequest::clear_reverse() {
  reverse_ = false;
}
inline bool ModelHistoryRequest::_internal_reverse() const {
  return reverse_;
}
inline bool ModelHistoryRequest::reverse() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelHistoryRequest.reverse)
  return _internal_reverse();
}
inline void ModelHistoryRequest::_internal_set_reverse(bool value) {
  
  reverse_ = value;
}
inline void ModelHistoryRequest::set_reverse(bool value) {
  _internal_set_reverse(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelHistoryRequest.reverse)
}

// .carbon.frontend.model.ModelEvent match_filter = 4;
inline bool ModelHistoryRequest::_internal_has_match_filter() const {
  return this != internal_default_instance() && match_filter_ != nullptr;
}
inline bool ModelHistoryRequest::has_match_filter() const {
  return _internal_has_match_filter();
}
inline void ModelHistoryRequest::clear_match_filter() {
  if (GetArenaForAllocation() == nullptr && match_filter_ != nullptr) {
    delete match_filter_;
  }
  match_filter_ = nullptr;
}
inline const ::carbon::frontend::model::ModelEvent& ModelHistoryRequest::_internal_match_filter() const {
  const ::carbon::frontend::model::ModelEvent* p = match_filter_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::model::ModelEvent&>(
      ::carbon::frontend::model::_ModelEvent_default_instance_);
}
inline const ::carbon::frontend::model::ModelEvent& ModelHistoryRequest::match_filter() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelHistoryRequest.match_filter)
  return _internal_match_filter();
}
inline void ModelHistoryRequest::unsafe_arena_set_allocated_match_filter(
    ::carbon::frontend::model::ModelEvent* match_filter) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(match_filter_);
  }
  match_filter_ = match_filter;
  if (match_filter) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.ModelHistoryRequest.match_filter)
}
inline ::carbon::frontend::model::ModelEvent* ModelHistoryRequest::release_match_filter() {
  
  ::carbon::frontend::model::ModelEvent* temp = match_filter_;
  match_filter_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::model::ModelEvent* ModelHistoryRequest::unsafe_arena_release_match_filter() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ModelHistoryRequest.match_filter)
  
  ::carbon::frontend::model::ModelEvent* temp = match_filter_;
  match_filter_ = nullptr;
  return temp;
}
inline ::carbon::frontend::model::ModelEvent* ModelHistoryRequest::_internal_mutable_match_filter() {
  
  if (match_filter_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::model::ModelEvent>(GetArenaForAllocation());
    match_filter_ = p;
  }
  return match_filter_;
}
inline ::carbon::frontend::model::ModelEvent* ModelHistoryRequest::mutable_match_filter() {
  ::carbon::frontend::model::ModelEvent* _msg = _internal_mutable_match_filter();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelHistoryRequest.match_filter)
  return _msg;
}
inline void ModelHistoryRequest::set_allocated_match_filter(::carbon::frontend::model::ModelEvent* match_filter) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete match_filter_;
  }
  if (match_filter) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::model::ModelEvent>::GetOwningArena(match_filter);
    if (message_arena != submessage_arena) {
      match_filter = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, match_filter, submessage_arena);
    }
    
  } else {
    
  }
  match_filter_ = match_filter;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ModelHistoryRequest.match_filter)
}

// .carbon.frontend.util.Timestamp ts = 5;
inline bool ModelHistoryRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool ModelHistoryRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& ModelHistoryRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& ModelHistoryRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelHistoryRequest.ts)
  return _internal_ts();
}
inline void ModelHistoryRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.ModelHistoryRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* ModelHistoryRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* ModelHistoryRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ModelHistoryRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* ModelHistoryRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* ModelHistoryRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelHistoryRequest.ts)
  return _msg;
}
inline void ModelHistoryRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ModelHistoryRequest.ts)
}

// .carbon.frontend.model.ModelEventTypeMatcher event_type_matcher = 6;
inline bool ModelHistoryRequest::_internal_has_event_type_matcher() const {
  return this != internal_default_instance() && event_type_matcher_ != nullptr;
}
inline bool ModelHistoryRequest::has_event_type_matcher() const {
  return _internal_has_event_type_matcher();
}
inline void ModelHistoryRequest::clear_event_type_matcher() {
  if (GetArenaForAllocation() == nullptr && event_type_matcher_ != nullptr) {
    delete event_type_matcher_;
  }
  event_type_matcher_ = nullptr;
}
inline const ::carbon::frontend::model::ModelEventTypeMatcher& ModelHistoryRequest::_internal_event_type_matcher() const {
  const ::carbon::frontend::model::ModelEventTypeMatcher* p = event_type_matcher_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::model::ModelEventTypeMatcher&>(
      ::carbon::frontend::model::_ModelEventTypeMatcher_default_instance_);
}
inline const ::carbon::frontend::model::ModelEventTypeMatcher& ModelHistoryRequest::event_type_matcher() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelHistoryRequest.event_type_matcher)
  return _internal_event_type_matcher();
}
inline void ModelHistoryRequest::unsafe_arena_set_allocated_event_type_matcher(
    ::carbon::frontend::model::ModelEventTypeMatcher* event_type_matcher) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(event_type_matcher_);
  }
  event_type_matcher_ = event_type_matcher;
  if (event_type_matcher) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.ModelHistoryRequest.event_type_matcher)
}
inline ::carbon::frontend::model::ModelEventTypeMatcher* ModelHistoryRequest::release_event_type_matcher() {
  
  ::carbon::frontend::model::ModelEventTypeMatcher* temp = event_type_matcher_;
  event_type_matcher_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::model::ModelEventTypeMatcher* ModelHistoryRequest::unsafe_arena_release_event_type_matcher() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ModelHistoryRequest.event_type_matcher)
  
  ::carbon::frontend::model::ModelEventTypeMatcher* temp = event_type_matcher_;
  event_type_matcher_ = nullptr;
  return temp;
}
inline ::carbon::frontend::model::ModelEventTypeMatcher* ModelHistoryRequest::_internal_mutable_event_type_matcher() {
  
  if (event_type_matcher_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::model::ModelEventTypeMatcher>(GetArenaForAllocation());
    event_type_matcher_ = p;
  }
  return event_type_matcher_;
}
inline ::carbon::frontend::model::ModelEventTypeMatcher* ModelHistoryRequest::mutable_event_type_matcher() {
  ::carbon::frontend::model::ModelEventTypeMatcher* _msg = _internal_mutable_event_type_matcher();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelHistoryRequest.event_type_matcher)
  return _msg;
}
inline void ModelHistoryRequest::set_allocated_event_type_matcher(::carbon::frontend::model::ModelEventTypeMatcher* event_type_matcher) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete event_type_matcher_;
  }
  if (event_type_matcher) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::model::ModelEventTypeMatcher>::GetOwningArena(event_type_matcher);
    if (message_arena != submessage_arena) {
      event_type_matcher = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, event_type_matcher, submessage_arena);
    }
    
  } else {
    
  }
  event_type_matcher_ = event_type_matcher;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ModelHistoryRequest.event_type_matcher)
}

// -------------------------------------------------------------------

// ModelEvent

// string type = 1;
inline void ModelEvent::clear_type() {
  type_.ClearToEmpty();
}
inline const std::string& ModelEvent::type() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEvent.type)
  return _internal_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_type(ArgT0&& arg0, ArgT... args) {
 
 type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEvent.type)
}
inline std::string* ModelEvent::mutable_type() {
  std::string* _s = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelEvent.type)
  return _s;
}
inline const std::string& ModelEvent::_internal_type() const {
  return type_.Get();
}
inline void ModelEvent::_internal_set_type(const std::string& value) {
  
  type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_type() {
  
  return type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_type() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ModelEvent.type)
  return type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (type_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ModelEvent.type)
}

// string model_id = 2;
inline void ModelEvent::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& ModelEvent::model_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEvent.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEvent.model_id)
}
inline std::string* ModelEvent::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelEvent.model_id)
  return _s;
}
inline const std::string& ModelEvent::_internal_model_id() const {
  return model_id_.Get();
}
inline void ModelEvent::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_model_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ModelEvent.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ModelEvent.model_id)
}

// string model_type = 3;
inline void ModelEvent::clear_model_type() {
  model_type_.ClearToEmpty();
}
inline const std::string& ModelEvent::model_type() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEvent.model_type)
  return _internal_model_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_model_type(ArgT0&& arg0, ArgT... args) {
 
 model_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEvent.model_type)
}
inline std::string* ModelEvent::mutable_model_type() {
  std::string* _s = _internal_mutable_model_type();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelEvent.model_type)
  return _s;
}
inline const std::string& ModelEvent::_internal_model_type() const {
  return model_type_.Get();
}
inline void ModelEvent::_internal_set_model_type(const std::string& value) {
  
  model_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_model_type() {
  
  return model_type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_model_type() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ModelEvent.model_type)
  return model_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_model_type(std::string* model_type) {
  if (model_type != nullptr) {
    
  } else {
    
  }
  model_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_type,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_type_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ModelEvent.model_type)
}

// string crop_id = 4;
inline void ModelEvent::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& ModelEvent::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEvent.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEvent.crop_id)
}
inline std::string* ModelEvent::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelEvent.crop_id)
  return _s;
}
inline const std::string& ModelEvent::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void ModelEvent::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ModelEvent.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ModelEvent.crop_id)
}

// string job_name = 5;
inline void ModelEvent::clear_job_name() {
  job_name_.ClearToEmpty();
}
inline const std::string& ModelEvent::job_name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEvent.job_name)
  return _internal_job_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_job_name(ArgT0&& arg0, ArgT... args) {
 
 job_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEvent.job_name)
}
inline std::string* ModelEvent::mutable_job_name() {
  std::string* _s = _internal_mutable_job_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelEvent.job_name)
  return _s;
}
inline const std::string& ModelEvent::_internal_job_name() const {
  return job_name_.Get();
}
inline void ModelEvent::_internal_set_job_name(const std::string& value) {
  
  job_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_job_name() {
  
  return job_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_job_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ModelEvent.job_name)
  return job_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_job_name(std::string* job_name) {
  if (job_name != nullptr) {
    
  } else {
    
  }
  job_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), job_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (job_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    job_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ModelEvent.job_name)
}

// int64 time = 6;
inline void ModelEvent::clear_time() {
  time_ = int64_t{0};
}
inline int64_t ModelEvent::_internal_time() const {
  return time_;
}
inline int64_t ModelEvent::time() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEvent.time)
  return _internal_time();
}
inline void ModelEvent::_internal_set_time(int64_t value) {
  
  time_ = value;
}
inline void ModelEvent::set_time(int64_t value) {
  _internal_set_time(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEvent.time)
}

// string model_nickname = 7;
inline void ModelEvent::clear_model_nickname() {
  model_nickname_.ClearToEmpty();
}
inline const std::string& ModelEvent::model_nickname() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEvent.model_nickname)
  return _internal_model_nickname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_model_nickname(ArgT0&& arg0, ArgT... args) {
 
 model_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEvent.model_nickname)
}
inline std::string* ModelEvent::mutable_model_nickname() {
  std::string* _s = _internal_mutable_model_nickname();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelEvent.model_nickname)
  return _s;
}
inline const std::string& ModelEvent::_internal_model_nickname() const {
  return model_nickname_.Get();
}
inline void ModelEvent::_internal_set_model_nickname(const std::string& value) {
  
  model_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_model_nickname() {
  
  return model_nickname_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_model_nickname() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ModelEvent.model_nickname)
  return model_nickname_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_model_nickname(std::string* model_nickname) {
  if (model_nickname != nullptr) {
    
  } else {
    
  }
  model_nickname_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_nickname,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_nickname_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ModelEvent.model_nickname)
}

// string model_parameters = 8;
inline void ModelEvent::clear_model_parameters() {
  model_parameters_.ClearToEmpty();
}
inline const std::string& ModelEvent::model_parameters() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEvent.model_parameters)
  return _internal_model_parameters();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_model_parameters(ArgT0&& arg0, ArgT... args) {
 
 model_parameters_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEvent.model_parameters)
}
inline std::string* ModelEvent::mutable_model_parameters() {
  std::string* _s = _internal_mutable_model_parameters();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelEvent.model_parameters)
  return _s;
}
inline const std::string& ModelEvent::_internal_model_parameters() const {
  return model_parameters_.Get();
}
inline void ModelEvent::_internal_set_model_parameters(const std::string& value) {
  
  model_parameters_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_model_parameters() {
  
  return model_parameters_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_model_parameters() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ModelEvent.model_parameters)
  return model_parameters_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_model_parameters(std::string* model_parameters) {
  if (model_parameters != nullptr) {
    
  } else {
    
  }
  model_parameters_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_parameters,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_parameters_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_parameters_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ModelEvent.model_parameters)
}

// -------------------------------------------------------------------

// ModelEventTypeMatcher

// bool robot_start = 1;
inline void ModelEventTypeMatcher::clear_robot_start() {
  robot_start_ = false;
}
inline bool ModelEventTypeMatcher::_internal_robot_start() const {
  return robot_start_;
}
inline bool ModelEventTypeMatcher::robot_start() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEventTypeMatcher.robot_start)
  return _internal_robot_start();
}
inline void ModelEventTypeMatcher::_internal_set_robot_start(bool value) {
  
  robot_start_ = value;
}
inline void ModelEventTypeMatcher::set_robot_start(bool value) {
  _internal_set_robot_start(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEventTypeMatcher.robot_start)
}

// bool pinned = 2;
inline void ModelEventTypeMatcher::clear_pinned() {
  pinned_ = false;
}
inline bool ModelEventTypeMatcher::_internal_pinned() const {
  return pinned_;
}
inline bool ModelEventTypeMatcher::pinned() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEventTypeMatcher.pinned)
  return _internal_pinned();
}
inline void ModelEventTypeMatcher::_internal_set_pinned(bool value) {
  
  pinned_ = value;
}
inline void ModelEventTypeMatcher::set_pinned(bool value) {
  _internal_set_pinned(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEventTypeMatcher.pinned)
}

// bool unpinned = 3;
inline void ModelEventTypeMatcher::clear_unpinned() {
  unpinned_ = false;
}
inline bool ModelEventTypeMatcher::_internal_unpinned() const {
  return unpinned_;
}
inline bool ModelEventTypeMatcher::unpinned() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEventTypeMatcher.unpinned)
  return _internal_unpinned();
}
inline void ModelEventTypeMatcher::_internal_set_unpinned(bool value) {
  
  unpinned_ = value;
}
inline void ModelEventTypeMatcher::set_unpinned(bool value) {
  _internal_set_unpinned(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEventTypeMatcher.unpinned)
}

// bool recommended = 4;
inline void ModelEventTypeMatcher::clear_recommended() {
  recommended_ = false;
}
inline bool ModelEventTypeMatcher::_internal_recommended() const {
  return recommended_;
}
inline bool ModelEventTypeMatcher::recommended() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEventTypeMatcher.recommended)
  return _internal_recommended();
}
inline void ModelEventTypeMatcher::_internal_set_recommended(bool value) {
  
  recommended_ = value;
}
inline void ModelEventTypeMatcher::set_recommended(bool value) {
  _internal_set_recommended(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEventTypeMatcher.recommended)
}

// bool activated = 5;
inline void ModelEventTypeMatcher::clear_activated() {
  activated_ = false;
}
inline bool ModelEventTypeMatcher::_internal_activated() const {
  return activated_;
}
inline bool ModelEventTypeMatcher::activated() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEventTypeMatcher.activated)
  return _internal_activated();
}
inline void ModelEventTypeMatcher::_internal_set_activated(bool value) {
  
  activated_ = value;
}
inline void ModelEventTypeMatcher::set_activated(bool value) {
  _internal_set_activated(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEventTypeMatcher.activated)
}

// bool nickname_change = 6;
inline void ModelEventTypeMatcher::clear_nickname_change() {
  nickname_change_ = false;
}
inline bool ModelEventTypeMatcher::_internal_nickname_change() const {
  return nickname_change_;
}
inline bool ModelEventTypeMatcher::nickname_change() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEventTypeMatcher.nickname_change)
  return _internal_nickname_change();
}
inline void ModelEventTypeMatcher::_internal_set_nickname_change(bool value) {
  
  nickname_change_ = value;
}
inline void ModelEventTypeMatcher::set_nickname_change(bool value) {
  _internal_set_nickname_change(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEventTypeMatcher.nickname_change)
}

// bool nickname_delete = 7;
inline void ModelEventTypeMatcher::clear_nickname_delete() {
  nickname_delete_ = false;
}
inline bool ModelEventTypeMatcher::_internal_nickname_delete() const {
  return nickname_delete_;
}
inline bool ModelEventTypeMatcher::nickname_delete() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEventTypeMatcher.nickname_delete)
  return _internal_nickname_delete();
}
inline void ModelEventTypeMatcher::_internal_set_nickname_delete(bool value) {
  
  nickname_delete_ = value;
}
inline void ModelEventTypeMatcher::set_nickname_delete(bool value) {
  _internal_set_nickname_delete(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEventTypeMatcher.nickname_delete)
}

// bool default_parameter_change = 8;
inline void ModelEventTypeMatcher::clear_default_parameter_change() {
  default_parameter_change_ = false;
}
inline bool ModelEventTypeMatcher::_internal_default_parameter_change() const {
  return default_parameter_change_;
}
inline bool ModelEventTypeMatcher::default_parameter_change() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEventTypeMatcher.default_parameter_change)
  return _internal_default_parameter_change();
}
inline void ModelEventTypeMatcher::_internal_set_default_parameter_change(bool value) {
  
  default_parameter_change_ = value;
}
inline void ModelEventTypeMatcher::set_default_parameter_change(bool value) {
  _internal_set_default_parameter_change(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEventTypeMatcher.default_parameter_change)
}

// bool parameter_change = 9;
inline void ModelEventTypeMatcher::clear_parameter_change() {
  parameter_change_ = false;
}
inline bool ModelEventTypeMatcher::_internal_parameter_change() const {
  return parameter_change_;
}
inline bool ModelEventTypeMatcher::parameter_change() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelEventTypeMatcher.parameter_change)
  return _internal_parameter_change();
}
inline void ModelEventTypeMatcher::_internal_set_parameter_change(bool value) {
  
  parameter_change_ = value;
}
inline void ModelEventTypeMatcher::set_parameter_change(bool value) {
  _internal_set_parameter_change(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.ModelEventTypeMatcher.parameter_change)
}

// -------------------------------------------------------------------

// ModelHistoryResponse

// repeated .carbon.frontend.model.ModelEvent events = 1;
inline int ModelHistoryResponse::_internal_events_size() const {
  return events_.size();
}
inline int ModelHistoryResponse::events_size() const {
  return _internal_events_size();
}
inline void ModelHistoryResponse::clear_events() {
  events_.Clear();
}
inline ::carbon::frontend::model::ModelEvent* ModelHistoryResponse::mutable_events(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelHistoryResponse.events)
  return events_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::ModelEvent >*
ModelHistoryResponse::mutable_events() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.model.ModelHistoryResponse.events)
  return &events_;
}
inline const ::carbon::frontend::model::ModelEvent& ModelHistoryResponse::_internal_events(int index) const {
  return events_.Get(index);
}
inline const ::carbon::frontend::model::ModelEvent& ModelHistoryResponse::events(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelHistoryResponse.events)
  return _internal_events(index);
}
inline ::carbon::frontend::model::ModelEvent* ModelHistoryResponse::_internal_add_events() {
  return events_.Add();
}
inline ::carbon::frontend::model::ModelEvent* ModelHistoryResponse::add_events() {
  ::carbon::frontend::model::ModelEvent* _add = _internal_add_events();
  // @@protoc_insertion_point(field_add:carbon.frontend.model.ModelHistoryResponse.events)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::ModelEvent >&
ModelHistoryResponse::events() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.model.ModelHistoryResponse.events)
  return events_;
}

// .carbon.frontend.util.Timestamp ts = 5;
inline bool ModelHistoryResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool ModelHistoryResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& ModelHistoryResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& ModelHistoryResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.ModelHistoryResponse.ts)
  return _internal_ts();
}
inline void ModelHistoryResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.ModelHistoryResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* ModelHistoryResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* ModelHistoryResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.ModelHistoryResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* ModelHistoryResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* ModelHistoryResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.ModelHistoryResponse.ts)
  return _msg;
}
inline void ModelHistoryResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.ModelHistoryResponse.ts)
}

// -------------------------------------------------------------------

// GetModelNicknamesRequest

// repeated string model_ids = 1;
inline int GetModelNicknamesRequest::_internal_model_ids_size() const {
  return model_ids_.size();
}
inline int GetModelNicknamesRequest::model_ids_size() const {
  return _internal_model_ids_size();
}
inline void GetModelNicknamesRequest::clear_model_ids() {
  model_ids_.Clear();
}
inline std::string* GetModelNicknamesRequest::add_model_ids() {
  std::string* _s = _internal_add_model_ids();
  // @@protoc_insertion_point(field_add_mutable:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
  return _s;
}
inline const std::string& GetModelNicknamesRequest::_internal_model_ids(int index) const {
  return model_ids_.Get(index);
}
inline const std::string& GetModelNicknamesRequest::model_ids(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
  return _internal_model_ids(index);
}
inline std::string* GetModelNicknamesRequest::mutable_model_ids(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
  return model_ids_.Mutable(index);
}
inline void GetModelNicknamesRequest::set_model_ids(int index, const std::string& value) {
  model_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
}
inline void GetModelNicknamesRequest::set_model_ids(int index, std::string&& value) {
  model_ids_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
}
inline void GetModelNicknamesRequest::set_model_ids(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  model_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
}
inline void GetModelNicknamesRequest::set_model_ids(int index, const char* value, size_t size) {
  model_ids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
}
inline std::string* GetModelNicknamesRequest::_internal_add_model_ids() {
  return model_ids_.Add();
}
inline void GetModelNicknamesRequest::add_model_ids(const std::string& value) {
  model_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
}
inline void GetModelNicknamesRequest::add_model_ids(std::string&& value) {
  model_ids_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
}
inline void GetModelNicknamesRequest::add_model_ids(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  model_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
}
inline void GetModelNicknamesRequest::add_model_ids(const char* value, size_t size) {
  model_ids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
GetModelNicknamesRequest::model_ids() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
  return model_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
GetModelNicknamesRequest::mutable_model_ids() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.model.GetModelNicknamesRequest.model_ids)
  return &model_ids_;
}

// .carbon.frontend.util.Timestamp ts = 5;
inline bool GetModelNicknamesRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetModelNicknamesRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetModelNicknamesRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetModelNicknamesRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetModelNicknamesRequest.ts)
  return _internal_ts();
}
inline void GetModelNicknamesRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.GetModelNicknamesRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetModelNicknamesRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetModelNicknamesRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetModelNicknamesRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetModelNicknamesRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetModelNicknamesRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetModelNicknamesRequest.ts)
  return _msg;
}
inline void GetModelNicknamesRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetModelNicknamesRequest.ts)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GetModelNicknamesResponse

// map<string, string> model_nicknames = 1;
inline int GetModelNicknamesResponse::_internal_model_nicknames_size() const {
  return model_nicknames_.size();
}
inline int GetModelNicknamesResponse::model_nicknames_size() const {
  return _internal_model_nicknames_size();
}
inline void GetModelNicknamesResponse::clear_model_nicknames() {
  model_nicknames_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetModelNicknamesResponse::_internal_model_nicknames() const {
  return model_nicknames_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetModelNicknamesResponse::model_nicknames() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.model.GetModelNicknamesResponse.model_nicknames)
  return _internal_model_nicknames();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetModelNicknamesResponse::_internal_mutable_model_nicknames() {
  return model_nicknames_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetModelNicknamesResponse::mutable_model_nicknames() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.model.GetModelNicknamesResponse.model_nicknames)
  return _internal_mutable_model_nicknames();
}

// .carbon.frontend.util.Timestamp ts = 5;
inline bool GetModelNicknamesResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetModelNicknamesResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetModelNicknamesResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetModelNicknamesResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetModelNicknamesResponse.ts)
  return _internal_ts();
}
inline void GetModelNicknamesResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.GetModelNicknamesResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetModelNicknamesResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetModelNicknamesResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetModelNicknamesResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetModelNicknamesResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetModelNicknamesResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetModelNicknamesResponse.ts)
  return _msg;
}
inline void GetModelNicknamesResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetModelNicknamesResponse.ts)
}

// -------------------------------------------------------------------

// SetModelNicknameRequest

// string model_id = 1;
inline void SetModelNicknameRequest::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& SetModelNicknameRequest::model_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.SetModelNicknameRequest.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetModelNicknameRequest::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.SetModelNicknameRequest.model_id)
}
inline std::string* SetModelNicknameRequest::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.SetModelNicknameRequest.model_id)
  return _s;
}
inline const std::string& SetModelNicknameRequest::_internal_model_id() const {
  return model_id_.Get();
}
inline void SetModelNicknameRequest::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetModelNicknameRequest::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetModelNicknameRequest::release_model_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.SetModelNicknameRequest.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetModelNicknameRequest::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.SetModelNicknameRequest.model_id)
}

// string model_nickname = 2;
inline void SetModelNicknameRequest::clear_model_nickname() {
  model_nickname_.ClearToEmpty();
}
inline const std::string& SetModelNicknameRequest::model_nickname() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.SetModelNicknameRequest.model_nickname)
  return _internal_model_nickname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetModelNicknameRequest::set_model_nickname(ArgT0&& arg0, ArgT... args) {
 
 model_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.SetModelNicknameRequest.model_nickname)
}
inline std::string* SetModelNicknameRequest::mutable_model_nickname() {
  std::string* _s = _internal_mutable_model_nickname();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.SetModelNicknameRequest.model_nickname)
  return _s;
}
inline const std::string& SetModelNicknameRequest::_internal_model_nickname() const {
  return model_nickname_.Get();
}
inline void SetModelNicknameRequest::_internal_set_model_nickname(const std::string& value) {
  
  model_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetModelNicknameRequest::_internal_mutable_model_nickname() {
  
  return model_nickname_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetModelNicknameRequest::release_model_nickname() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.SetModelNicknameRequest.model_nickname)
  return model_nickname_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetModelNicknameRequest::set_allocated_model_nickname(std::string* model_nickname) {
  if (model_nickname != nullptr) {
    
  } else {
    
  }
  model_nickname_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_nickname,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_nickname_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.SetModelNicknameRequest.model_nickname)
}

// -------------------------------------------------------------------

// CropModelPair

// string crop_id = 1;
inline void CropModelPair::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& CropModelPair::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.CropModelPair.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CropModelPair::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.CropModelPair.crop_id)
}
inline std::string* CropModelPair::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.CropModelPair.crop_id)
  return _s;
}
inline const std::string& CropModelPair::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void CropModelPair::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CropModelPair::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CropModelPair::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.CropModelPair.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CropModelPair::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.CropModelPair.crop_id)
}

// string model_id = 2;
inline void CropModelPair::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& CropModelPair::model_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.CropModelPair.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CropModelPair::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.CropModelPair.model_id)
}
inline std::string* CropModelPair::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.CropModelPair.model_id)
  return _s;
}
inline const std::string& CropModelPair::_internal_model_id() const {
  return model_id_.Get();
}
inline void CropModelPair::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CropModelPair::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CropModelPair::release_model_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.CropModelPair.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CropModelPair::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.CropModelPair.model_id)
}

// -------------------------------------------------------------------

// RefreshDefaultModelParametersRequest

// repeated .carbon.frontend.model.CropModelPair cropModelPairs = 1;
inline int RefreshDefaultModelParametersRequest::_internal_cropmodelpairs_size() const {
  return cropmodelpairs_.size();
}
inline int RefreshDefaultModelParametersRequest::cropmodelpairs_size() const {
  return _internal_cropmodelpairs_size();
}
inline void RefreshDefaultModelParametersRequest::clear_cropmodelpairs() {
  cropmodelpairs_.Clear();
}
inline ::carbon::frontend::model::CropModelPair* RefreshDefaultModelParametersRequest::mutable_cropmodelpairs(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.RefreshDefaultModelParametersRequest.cropModelPairs)
  return cropmodelpairs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::CropModelPair >*
RefreshDefaultModelParametersRequest::mutable_cropmodelpairs() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.model.RefreshDefaultModelParametersRequest.cropModelPairs)
  return &cropmodelpairs_;
}
inline const ::carbon::frontend::model::CropModelPair& RefreshDefaultModelParametersRequest::_internal_cropmodelpairs(int index) const {
  return cropmodelpairs_.Get(index);
}
inline const ::carbon::frontend::model::CropModelPair& RefreshDefaultModelParametersRequest::cropmodelpairs(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.RefreshDefaultModelParametersRequest.cropModelPairs)
  return _internal_cropmodelpairs(index);
}
inline ::carbon::frontend::model::CropModelPair* RefreshDefaultModelParametersRequest::_internal_add_cropmodelpairs() {
  return cropmodelpairs_.Add();
}
inline ::carbon::frontend::model::CropModelPair* RefreshDefaultModelParametersRequest::add_cropmodelpairs() {
  ::carbon::frontend::model::CropModelPair* _add = _internal_add_cropmodelpairs();
  // @@protoc_insertion_point(field_add:carbon.frontend.model.RefreshDefaultModelParametersRequest.cropModelPairs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::CropModelPair >&
RefreshDefaultModelParametersRequest::cropmodelpairs() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.model.RefreshDefaultModelParametersRequest.cropModelPairs)
  return cropmodelpairs_;
}

// -------------------------------------------------------------------

// SyncCropIDsRequest

// bool force_cache_refresh = 1;
inline void SyncCropIDsRequest::clear_force_cache_refresh() {
  force_cache_refresh_ = false;
}
inline bool SyncCropIDsRequest::_internal_force_cache_refresh() const {
  return force_cache_refresh_;
}
inline bool SyncCropIDsRequest::force_cache_refresh() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.SyncCropIDsRequest.force_cache_refresh)
  return _internal_force_cache_refresh();
}
inline void SyncCropIDsRequest::_internal_set_force_cache_refresh(bool value) {
  
  force_cache_refresh_ = value;
}
inline void SyncCropIDsRequest::set_force_cache_refresh(bool value) {
  _internal_set_force_cache_refresh(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.model.SyncCropIDsRequest.force_cache_refresh)
}

// -------------------------------------------------------------------

// GetNextEnabledCropsRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextEnabledCropsRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextEnabledCropsRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextEnabledCropsRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextEnabledCropsRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextEnabledCropsRequest.ts)
  return _internal_ts();
}
inline void GetNextEnabledCropsRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.GetNextEnabledCropsRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextEnabledCropsRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextEnabledCropsRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextEnabledCropsRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextEnabledCropsRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextEnabledCropsRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextEnabledCropsRequest.ts)
  return _msg;
}
inline void GetNextEnabledCropsRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextEnabledCropsRequest.ts)
}

// string lang = 2;
inline void GetNextEnabledCropsRequest::clear_lang() {
  lang_.ClearToEmpty();
}
inline const std::string& GetNextEnabledCropsRequest::lang() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextEnabledCropsRequest.lang)
  return _internal_lang();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextEnabledCropsRequest::set_lang(ArgT0&& arg0, ArgT... args) {
 
 lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.GetNextEnabledCropsRequest.lang)
}
inline std::string* GetNextEnabledCropsRequest::mutable_lang() {
  std::string* _s = _internal_mutable_lang();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextEnabledCropsRequest.lang)
  return _s;
}
inline const std::string& GetNextEnabledCropsRequest::_internal_lang() const {
  return lang_.Get();
}
inline void GetNextEnabledCropsRequest::_internal_set_lang(const std::string& value) {
  
  lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextEnabledCropsRequest::_internal_mutable_lang() {
  
  return lang_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextEnabledCropsRequest::release_lang() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextEnabledCropsRequest.lang)
  return lang_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextEnabledCropsRequest::set_allocated_lang(std::string* lang) {
  if (lang != nullptr) {
    
  } else {
    
  }
  lang_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), lang,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (lang_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextEnabledCropsRequest.lang)
}

// -------------------------------------------------------------------

// GetNextEnabledCropsResponse

// repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
inline int GetNextEnabledCropsResponse::_internal_enabledcrops_size() const {
  return enabledcrops_.size();
}
inline int GetNextEnabledCropsResponse::enabledcrops_size() const {
  return _internal_enabledcrops_size();
}
inline void GetNextEnabledCropsResponse::clear_enabledcrops() {
  enabledcrops_.Clear();
}
inline ::carbon::frontend::model::EnabledCrop* GetNextEnabledCropsResponse::mutable_enabledcrops(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextEnabledCropsResponse.enabledCrops)
  return enabledcrops_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >*
GetNextEnabledCropsResponse::mutable_enabledcrops() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.model.GetNextEnabledCropsResponse.enabledCrops)
  return &enabledcrops_;
}
inline const ::carbon::frontend::model::EnabledCrop& GetNextEnabledCropsResponse::_internal_enabledcrops(int index) const {
  return enabledcrops_.Get(index);
}
inline const ::carbon::frontend::model::EnabledCrop& GetNextEnabledCropsResponse::enabledcrops(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextEnabledCropsResponse.enabledCrops)
  return _internal_enabledcrops(index);
}
inline ::carbon::frontend::model::EnabledCrop* GetNextEnabledCropsResponse::_internal_add_enabledcrops() {
  return enabledcrops_.Add();
}
inline ::carbon::frontend::model::EnabledCrop* GetNextEnabledCropsResponse::add_enabledcrops() {
  ::carbon::frontend::model::EnabledCrop* _add = _internal_add_enabledcrops();
  // @@protoc_insertion_point(field_add:carbon.frontend.model.GetNextEnabledCropsResponse.enabledCrops)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >&
GetNextEnabledCropsResponse::enabledcrops() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.model.GetNextEnabledCropsResponse.enabledCrops)
  return enabledcrops_;
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool GetNextEnabledCropsResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextEnabledCropsResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextEnabledCropsResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextEnabledCropsResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextEnabledCropsResponse.ts)
  return _internal_ts();
}
inline void GetNextEnabledCropsResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.GetNextEnabledCropsResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextEnabledCropsResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextEnabledCropsResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextEnabledCropsResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextEnabledCropsResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextEnabledCropsResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextEnabledCropsResponse.ts)
  return _msg;
}
inline void GetNextEnabledCropsResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextEnabledCropsResponse.ts)
}

// -------------------------------------------------------------------

// GetNextCaptureCropsRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextCaptureCropsRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextCaptureCropsRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextCaptureCropsRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextCaptureCropsRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextCaptureCropsRequest.ts)
  return _internal_ts();
}
inline void GetNextCaptureCropsRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.GetNextCaptureCropsRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextCaptureCropsRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextCaptureCropsRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextCaptureCropsRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextCaptureCropsRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextCaptureCropsRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextCaptureCropsRequest.ts)
  return _msg;
}
inline void GetNextCaptureCropsRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextCaptureCropsRequest.ts)
}

// string lang = 2;
inline void GetNextCaptureCropsRequest::clear_lang() {
  lang_.ClearToEmpty();
}
inline const std::string& GetNextCaptureCropsRequest::lang() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextCaptureCropsRequest.lang)
  return _internal_lang();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextCaptureCropsRequest::set_lang(ArgT0&& arg0, ArgT... args) {
 
 lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.model.GetNextCaptureCropsRequest.lang)
}
inline std::string* GetNextCaptureCropsRequest::mutable_lang() {
  std::string* _s = _internal_mutable_lang();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextCaptureCropsRequest.lang)
  return _s;
}
inline const std::string& GetNextCaptureCropsRequest::_internal_lang() const {
  return lang_.Get();
}
inline void GetNextCaptureCropsRequest::_internal_set_lang(const std::string& value) {
  
  lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextCaptureCropsRequest::_internal_mutable_lang() {
  
  return lang_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextCaptureCropsRequest::release_lang() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextCaptureCropsRequest.lang)
  return lang_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextCaptureCropsRequest::set_allocated_lang(std::string* lang) {
  if (lang != nullptr) {
    
  } else {
    
  }
  lang_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), lang,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (lang_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextCaptureCropsRequest.lang)
}

// -------------------------------------------------------------------

// GetNextCaptureCropsResponse

// repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
inline int GetNextCaptureCropsResponse::_internal_enabledcrops_size() const {
  return enabledcrops_.size();
}
inline int GetNextCaptureCropsResponse::enabledcrops_size() const {
  return _internal_enabledcrops_size();
}
inline void GetNextCaptureCropsResponse::clear_enabledcrops() {
  enabledcrops_.Clear();
}
inline ::carbon::frontend::model::EnabledCrop* GetNextCaptureCropsResponse::mutable_enabledcrops(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextCaptureCropsResponse.enabledCrops)
  return enabledcrops_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >*
GetNextCaptureCropsResponse::mutable_enabledcrops() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.model.GetNextCaptureCropsResponse.enabledCrops)
  return &enabledcrops_;
}
inline const ::carbon::frontend::model::EnabledCrop& GetNextCaptureCropsResponse::_internal_enabledcrops(int index) const {
  return enabledcrops_.Get(index);
}
inline const ::carbon::frontend::model::EnabledCrop& GetNextCaptureCropsResponse::enabledcrops(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextCaptureCropsResponse.enabledCrops)
  return _internal_enabledcrops(index);
}
inline ::carbon::frontend::model::EnabledCrop* GetNextCaptureCropsResponse::_internal_add_enabledcrops() {
  return enabledcrops_.Add();
}
inline ::carbon::frontend::model::EnabledCrop* GetNextCaptureCropsResponse::add_enabledcrops() {
  ::carbon::frontend::model::EnabledCrop* _add = _internal_add_enabledcrops();
  // @@protoc_insertion_point(field_add:carbon.frontend.model.GetNextCaptureCropsResponse.enabledCrops)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::model::EnabledCrop >&
GetNextCaptureCropsResponse::enabledcrops() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.model.GetNextCaptureCropsResponse.enabledCrops)
  return enabledcrops_;
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool GetNextCaptureCropsResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextCaptureCropsResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextCaptureCropsResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextCaptureCropsResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.model.GetNextCaptureCropsResponse.ts)
  return _internal_ts();
}
inline void GetNextCaptureCropsResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.model.GetNextCaptureCropsResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextCaptureCropsResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextCaptureCropsResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.model.GetNextCaptureCropsResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextCaptureCropsResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextCaptureCropsResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.model.GetNextCaptureCropsResponse.ts)
  return _msg;
}
inline void GetNextCaptureCropsResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.model.GetNextCaptureCropsResponse.ts)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fmodel_2eproto

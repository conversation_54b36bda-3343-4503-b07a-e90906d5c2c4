// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/plant_captcha.proto

#include "frontend/proto/plant_captcha.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace plant_captcha {
constexpr PlantCaptcha::PlantCaptcha(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : rows_used_()
  , _rows_used_cached_byte_size_(0)
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , start_time_ms_(int64_t{0}){}
struct PlantCaptchaDefaultTypeInternal {
  constexpr PlantCaptchaDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PlantCaptchaDefaultTypeInternal() {}
  union {
    PlantCaptcha _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PlantCaptchaDefaultTypeInternal _PlantCaptcha_default_instance_;
constexpr StartPlantCaptchaRequest::StartPlantCaptchaRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : plant_captcha_(nullptr){}
struct StartPlantCaptchaRequestDefaultTypeInternal {
  constexpr StartPlantCaptchaRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StartPlantCaptchaRequestDefaultTypeInternal() {}
  union {
    StartPlantCaptchaRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StartPlantCaptchaRequestDefaultTypeInternal _StartPlantCaptchaRequest_default_instance_;
constexpr StartPlantCaptchaResponse::StartPlantCaptchaResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct StartPlantCaptchaResponseDefaultTypeInternal {
  constexpr StartPlantCaptchaResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StartPlantCaptchaResponseDefaultTypeInternal() {}
  union {
    StartPlantCaptchaResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StartPlantCaptchaResponseDefaultTypeInternal _StartPlantCaptchaResponse_default_instance_;
constexpr GetNextPlantCaptchaStatusRequest::GetNextPlantCaptchaStatusRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr){}
struct GetNextPlantCaptchaStatusRequestDefaultTypeInternal {
  constexpr GetNextPlantCaptchaStatusRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextPlantCaptchaStatusRequestDefaultTypeInternal() {}
  union {
    GetNextPlantCaptchaStatusRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextPlantCaptchaStatusRequestDefaultTypeInternal _GetNextPlantCaptchaStatusRequest_default_instance_;
constexpr GetNextPlantCaptchaStatusResponse::GetNextPlantCaptchaStatusResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , status_(0)

  , total_images_(0)
  , images_taken_(0)
  , metadata_taken_(0){}
struct GetNextPlantCaptchaStatusResponseDefaultTypeInternal {
  constexpr GetNextPlantCaptchaStatusResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextPlantCaptchaStatusResponseDefaultTypeInternal() {}
  union {
    GetNextPlantCaptchaStatusResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextPlantCaptchaStatusResponseDefaultTypeInternal _GetNextPlantCaptchaStatusResponse_default_instance_;
constexpr GetNextPlantCaptchasListRequest::GetNextPlantCaptchasListRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr){}
struct GetNextPlantCaptchasListRequestDefaultTypeInternal {
  constexpr GetNextPlantCaptchasListRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextPlantCaptchasListRequestDefaultTypeInternal() {}
  union {
    GetNextPlantCaptchasListRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextPlantCaptchasListRequestDefaultTypeInternal _GetNextPlantCaptchasListRequest_default_instance_;
constexpr PlantCaptchaListItem::PlantCaptchaListItem(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : plant_captcha_(nullptr)
  , images_taken_(0)
  , images_processed_(0){}
struct PlantCaptchaListItemDefaultTypeInternal {
  constexpr PlantCaptchaListItemDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PlantCaptchaListItemDefaultTypeInternal() {}
  union {
    PlantCaptchaListItem _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PlantCaptchaListItemDefaultTypeInternal _PlantCaptchaListItem_default_instance_;
constexpr GetNextPlantCaptchasListResponse::GetNextPlantCaptchasListResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : plant_captchas_()
  , ts_(nullptr){}
struct GetNextPlantCaptchasListResponseDefaultTypeInternal {
  constexpr GetNextPlantCaptchasListResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextPlantCaptchasListResponseDefaultTypeInternal() {}
  union {
    GetNextPlantCaptchasListResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextPlantCaptchasListResponseDefaultTypeInternal _GetNextPlantCaptchasListResponse_default_instance_;
constexpr DeletePlantCaptchaRequest::DeletePlantCaptchaRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DeletePlantCaptchaRequestDefaultTypeInternal {
  constexpr DeletePlantCaptchaRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeletePlantCaptchaRequestDefaultTypeInternal() {}
  union {
    DeletePlantCaptchaRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeletePlantCaptchaRequestDefaultTypeInternal _DeletePlantCaptchaRequest_default_instance_;
constexpr GetPlantCaptchaRequest::GetPlantCaptchaRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetPlantCaptchaRequestDefaultTypeInternal {
  constexpr GetPlantCaptchaRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPlantCaptchaRequestDefaultTypeInternal() {}
  union {
    GetPlantCaptchaRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPlantCaptchaRequestDefaultTypeInternal _GetPlantCaptchaRequest_default_instance_;
constexpr PlantCaptchaItem::PlantCaptchaItem(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : additional_image_urls_()
  , additional_metadatas_()
  , image_url_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , metadata_(nullptr){}
struct PlantCaptchaItemDefaultTypeInternal {
  constexpr PlantCaptchaItemDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PlantCaptchaItemDefaultTypeInternal() {}
  union {
    PlantCaptchaItem _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PlantCaptchaItemDefaultTypeInternal _PlantCaptchaItem_default_instance_;
constexpr GetPlantCaptchaResponse::GetPlantCaptchaResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : items_()
  , plant_captcha_(nullptr){}
struct GetPlantCaptchaResponseDefaultTypeInternal {
  constexpr GetPlantCaptchaResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPlantCaptchaResponseDefaultTypeInternal() {}
  union {
    GetPlantCaptchaResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPlantCaptchaResponseDefaultTypeInternal _GetPlantCaptchaResponse_default_instance_;
constexpr StartPlantCaptchaUploadRequest::StartPlantCaptchaUploadRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct StartPlantCaptchaUploadRequestDefaultTypeInternal {
  constexpr StartPlantCaptchaUploadRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StartPlantCaptchaUploadRequestDefaultTypeInternal() {}
  union {
    StartPlantCaptchaUploadRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StartPlantCaptchaUploadRequestDefaultTypeInternal _StartPlantCaptchaUploadRequest_default_instance_;
constexpr GetNextPlantCaptchaUploadStateRequest::GetNextPlantCaptchaUploadStateRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextPlantCaptchaUploadStateRequestDefaultTypeInternal {
  constexpr GetNextPlantCaptchaUploadStateRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextPlantCaptchaUploadStateRequestDefaultTypeInternal() {}
  union {
    GetNextPlantCaptchaUploadStateRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextPlantCaptchaUploadStateRequestDefaultTypeInternal _GetNextPlantCaptchaUploadStateRequest_default_instance_;
constexpr GetNextPlantCaptchaUploadStateResponse::GetNextPlantCaptchaUploadStateResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , upload_state_(0)

  , percent_(0){}
struct GetNextPlantCaptchaUploadStateResponseDefaultTypeInternal {
  constexpr GetNextPlantCaptchaUploadStateResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextPlantCaptchaUploadStateResponseDefaultTypeInternal() {}
  union {
    GetNextPlantCaptchaUploadStateResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextPlantCaptchaUploadStateResponseDefaultTypeInternal _GetNextPlantCaptchaUploadStateResponse_default_instance_;
constexpr PlantCaptchaItemResult::PlantCaptchaItemResult(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , user_prediction_(0)
{}
struct PlantCaptchaItemResultDefaultTypeInternal {
  constexpr PlantCaptchaItemResultDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PlantCaptchaItemResultDefaultTypeInternal() {}
  union {
    PlantCaptchaItemResult _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PlantCaptchaItemResultDefaultTypeInternal _PlantCaptchaItemResult_default_instance_;
constexpr SubmitPlantCaptchaResultsRequest::SubmitPlantCaptchaResultsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : results_()
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SubmitPlantCaptchaResultsRequestDefaultTypeInternal {
  constexpr SubmitPlantCaptchaResultsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SubmitPlantCaptchaResultsRequestDefaultTypeInternal() {}
  union {
    SubmitPlantCaptchaResultsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SubmitPlantCaptchaResultsRequestDefaultTypeInternal _SubmitPlantCaptchaResultsRequest_default_instance_;
constexpr GetPlantCaptchaItemResultsRequest::GetPlantCaptchaItemResultsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_()
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetPlantCaptchaItemResultsRequestDefaultTypeInternal {
  constexpr GetPlantCaptchaItemResultsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPlantCaptchaItemResultsRequestDefaultTypeInternal() {}
  union {
    GetPlantCaptchaItemResultsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPlantCaptchaItemResultsRequestDefaultTypeInternal _GetPlantCaptchaItemResultsRequest_default_instance_;
constexpr GetPlantCaptchaItemResultsResponse::GetPlantCaptchaItemResultsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : results_(){}
struct GetPlantCaptchaItemResultsResponseDefaultTypeInternal {
  constexpr GetPlantCaptchaItemResultsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPlantCaptchaItemResultsResponseDefaultTypeInternal() {}
  union {
    GetPlantCaptchaItemResultsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPlantCaptchaItemResultsResponseDefaultTypeInternal _GetPlantCaptchaItemResultsResponse_default_instance_;
constexpr CalculatePlantCaptchaRequest::CalculatePlantCaptchaRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct CalculatePlantCaptchaRequestDefaultTypeInternal {
  constexpr CalculatePlantCaptchaRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CalculatePlantCaptchaRequestDefaultTypeInternal() {}
  union {
    CalculatePlantCaptchaRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CalculatePlantCaptchaRequestDefaultTypeInternal _CalculatePlantCaptchaRequest_default_instance_;
constexpr CalculatePlantCaptchaResponse::CalculatePlantCaptchaResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : modelinator_config_(nullptr)
  , succeeded_(false)
  , failure_reason_(0)
{}
struct CalculatePlantCaptchaResponseDefaultTypeInternal {
  constexpr CalculatePlantCaptchaResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CalculatePlantCaptchaResponseDefaultTypeInternal() {}
  union {
    CalculatePlantCaptchaResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CalculatePlantCaptchaResponseDefaultTypeInternal _CalculatePlantCaptchaResponse_default_instance_;
constexpr PlantCaptchaResult::PlantCaptchaResult(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : metadata_(nullptr)
  , label_(0)
{}
struct PlantCaptchaResultDefaultTypeInternal {
  constexpr PlantCaptchaResultDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PlantCaptchaResultDefaultTypeInternal() {}
  union {
    PlantCaptchaResult _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PlantCaptchaResultDefaultTypeInternal _PlantCaptchaResult_default_instance_;
constexpr PlantCaptchaResults::PlantCaptchaResults(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : captcha_results_()
  , algorithm_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , tiebreaker_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , mindoo_tiebreaker_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , tiebreaker_strategy_threshold_weed_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , tiebreaker_strategy_threshold_crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , tiebreaker_strategy_mindoo_weed_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , tiebreaker_strategy_mindoo_crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , current_parameters_(nullptr)
  , almanac_(nullptr)
  , goal_crops_targeted_(0)
  , goal_weeds_targeted_(0)
  , goal_unknown_targeted_(0)
  , max_recommended_mindoo_(0)
  , min_items_for_recommendation_(0)
  , min_recommended_mindoo_(0)
  , min_recommended_weed_threshold_(0)
  , max_recommended_weed_threshold_(0)
  , min_recommended_crop_threshold_(0)
  , max_recommended_crop_threshold_(0)
  , min_doo_for_recommendation_(0)
  , use_weed_categories_for_weed_labels_(false)
  , use_other_as_tiebreaker_(false)
  , limit_by_crops_missed_(false)
  , pad_crop_configurations_(false)
  , number_of_crop_configurations_(0)
  , use_beneficials_as_crops_(false)
  , use_volunteers_as_weeds_(false){}
struct PlantCaptchaResultsDefaultTypeInternal {
  constexpr PlantCaptchaResultsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PlantCaptchaResultsDefaultTypeInternal() {}
  union {
    PlantCaptchaResults _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PlantCaptchaResultsDefaultTypeInternal _PlantCaptchaResults_default_instance_;
constexpr VeselkaPlantCaptchaResponse::VeselkaPlantCaptchaResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : new_model_parameters_(nullptr)
  , succeeded_(false){}
struct VeselkaPlantCaptchaResponseDefaultTypeInternal {
  constexpr VeselkaPlantCaptchaResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VeselkaPlantCaptchaResponseDefaultTypeInternal() {}
  union {
    VeselkaPlantCaptchaResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VeselkaPlantCaptchaResponseDefaultTypeInternal _VeselkaPlantCaptchaResponse_default_instance_;
constexpr GetOriginalModelinatorConfigRequest::GetOriginalModelinatorConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetOriginalModelinatorConfigRequestDefaultTypeInternal {
  constexpr GetOriginalModelinatorConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetOriginalModelinatorConfigRequestDefaultTypeInternal() {}
  union {
    GetOriginalModelinatorConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetOriginalModelinatorConfigRequestDefaultTypeInternal _GetOriginalModelinatorConfigRequest_default_instance_;
constexpr GetOriginalModelinatorConfigResponse::GetOriginalModelinatorConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : modelinator_config_(nullptr){}
struct GetOriginalModelinatorConfigResponseDefaultTypeInternal {
  constexpr GetOriginalModelinatorConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetOriginalModelinatorConfigResponseDefaultTypeInternal() {}
  union {
    GetOriginalModelinatorConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetOriginalModelinatorConfigResponseDefaultTypeInternal _GetOriginalModelinatorConfigResponse_default_instance_;
constexpr GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUseDefaultTypeInternal {
  constexpr GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUseDefaultTypeInternal _GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse_default_instance_;
constexpr GetCaptchaRowStatusResponse::GetCaptchaRowStatusResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : row_status_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct GetCaptchaRowStatusResponseDefaultTypeInternal {
  constexpr GetCaptchaRowStatusResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetCaptchaRowStatusResponseDefaultTypeInternal() {}
  union {
    GetCaptchaRowStatusResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetCaptchaRowStatusResponseDefaultTypeInternal _GetCaptchaRowStatusResponse_default_instance_;
constexpr CancelPlantCaptchaOnRowRequest::CancelPlantCaptchaOnRowRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : row_id_(0){}
struct CancelPlantCaptchaOnRowRequestDefaultTypeInternal {
  constexpr CancelPlantCaptchaOnRowRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CancelPlantCaptchaOnRowRequestDefaultTypeInternal() {}
  union {
    CancelPlantCaptchaOnRowRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CancelPlantCaptchaOnRowRequestDefaultTypeInternal _CancelPlantCaptchaOnRowRequest_default_instance_;
}  // namespace plant_captcha
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[29];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_frontend_2fproto_2fplant_5fcaptcha_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fplant_5fcaptcha_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptcha, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptcha, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptcha, model_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptcha, crop_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptcha, crop_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptcha, start_time_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptcha, rows_used_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::StartPlantCaptchaRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::StartPlantCaptchaRequest, plant_captcha_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::StartPlantCaptchaResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse, status_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse, total_images_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse, images_taken_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse, metadata_taken_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaListItem, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaListItem, plant_captcha_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaListItem, images_taken_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaListItem, images_processed_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse, plant_captchas_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetPlantCaptchaRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetPlantCaptchaRequest, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaItem, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaItem, image_url_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaItem, metadata_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaItem, additional_image_urls_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaItem, additional_metadatas_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetPlantCaptchaResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetPlantCaptchaResponse, plant_captcha_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetPlantCaptchaResponse, items_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse, upload_state_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse, percent_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaItemResult, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaItemResult, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaItemResult, user_prediction_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, results_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse, results_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse, modelinator_config_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse, succeeded_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse, failure_reason_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResult, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResult, label_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResult, metadata_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, current_parameters_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, captcha_results_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, algorithm_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, goal_crops_targeted_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, goal_weeds_targeted_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, goal_unknown_targeted_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, almanac_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, max_recommended_mindoo_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, min_items_for_recommendation_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, use_weed_categories_for_weed_labels_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, min_recommended_mindoo_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, min_recommended_weed_threshold_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, max_recommended_weed_threshold_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, min_recommended_crop_threshold_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, max_recommended_crop_threshold_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, min_doo_for_recommendation_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, use_other_as_tiebreaker_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, limit_by_crops_missed_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, number_of_crop_configurations_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, tiebreaker_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, pad_crop_configurations_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, mindoo_tiebreaker_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, use_beneficials_as_crops_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, use_volunteers_as_weeds_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, tiebreaker_strategy_threshold_weed_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, tiebreaker_strategy_threshold_crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, tiebreaker_strategy_mindoo_weed_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::PlantCaptchaResults, tiebreaker_strategy_mindoo_crop_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::VeselkaPlantCaptchaResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::VeselkaPlantCaptchaResponse, new_model_parameters_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::VeselkaPlantCaptchaResponse, succeeded_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse, modelinator_config_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse, row_status_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest, row_id_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::plant_captcha::PlantCaptcha)},
  { 12, -1, -1, sizeof(::carbon::frontend::plant_captcha::StartPlantCaptchaRequest)},
  { 19, -1, -1, sizeof(::carbon::frontend::plant_captcha::StartPlantCaptchaResponse)},
  { 25, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest)},
  { 32, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse)},
  { 43, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest)},
  { 50, -1, -1, sizeof(::carbon::frontend::plant_captcha::PlantCaptchaListItem)},
  { 59, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse)},
  { 67, -1, -1, sizeof(::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest)},
  { 74, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetPlantCaptchaRequest)},
  { 81, -1, -1, sizeof(::carbon::frontend::plant_captcha::PlantCaptchaItem)},
  { 91, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetPlantCaptchaResponse)},
  { 99, -1, -1, sizeof(::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest)},
  { 106, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest)},
  { 114, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse)},
  { 123, -1, -1, sizeof(::carbon::frontend::plant_captcha::PlantCaptchaItemResult)},
  { 131, -1, -1, sizeof(::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest)},
  { 139, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest)},
  { 147, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse)},
  { 154, -1, -1, sizeof(::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest)},
  { 161, -1, -1, sizeof(::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse)},
  { 170, -1, -1, sizeof(::carbon::frontend::plant_captcha::PlantCaptchaResult)},
  { 178, -1, -1, sizeof(::carbon::frontend::plant_captcha::PlantCaptchaResults)},
  { 212, -1, -1, sizeof(::carbon::frontend::plant_captcha::VeselkaPlantCaptchaResponse)},
  { 220, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest)},
  { 227, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse)},
  { 234, 242, -1, sizeof(::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse)},
  { 244, -1, -1, sizeof(::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse)},
  { 251, -1, -1, sizeof(::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_PlantCaptcha_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_StartPlantCaptchaRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_StartPlantCaptchaResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetNextPlantCaptchaStatusRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetNextPlantCaptchaStatusResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetNextPlantCaptchasListRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_PlantCaptchaListItem_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetNextPlantCaptchasListResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_DeletePlantCaptchaRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetPlantCaptchaRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_PlantCaptchaItem_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetPlantCaptchaResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_StartPlantCaptchaUploadRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetNextPlantCaptchaUploadStateRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetNextPlantCaptchaUploadStateResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_PlantCaptchaItemResult_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_SubmitPlantCaptchaResultsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetPlantCaptchaItemResultsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetPlantCaptchaItemResultsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_CalculatePlantCaptchaRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_CalculatePlantCaptchaResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_PlantCaptchaResult_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_PlantCaptchaResults_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_VeselkaPlantCaptchaResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetOriginalModelinatorConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetOriginalModelinatorConfigResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_GetCaptchaRowStatusResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::plant_captcha::_CancelPlantCaptchaOnRowRequest_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fplant_5fcaptcha_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\"frontend/proto/plant_captcha.proto\022\035ca"
  "rbon.frontend.plant_captcha\032\031frontend/pr"
  "oto/util.proto\032\'weed_tracking/proto/weed"
  "_tracking.proto\032\033proto/almanac/almanac.p"
  "roto\"|\n\014PlantCaptcha\022\014\n\004name\030\001 \001(\t\022\020\n\010mo"
  "del_id\030\002 \001(\t\022\017\n\007crop_id\030\003 \001(\t\022\021\n\tcrop_na"
  "me\030\004 \001(\t\022\025\n\rstart_time_ms\030\005 \001(\003\022\021\n\trows_"
  "used\030\006 \003(\005\"^\n\030StartPlantCaptchaRequest\022B"
  "\n\rplant_captcha\030\001 \001(\0132+.carbon.frontend."
  "plant_captcha.PlantCaptcha\"\033\n\031StartPlant"
  "CaptchaResponse\"O\n GetNextPlantCaptchaSt"
  "atusRequest\022+\n\002ts\030\001 \001(\0132\037.carbon.fronten"
  "d.util.Timestamp\"\307\001\n!GetNextPlantCaptcha"
  "StatusResponse\022+\n\002ts\030\001 \001(\0132\037.carbon.fron"
  "tend.util.Timestamp\0221\n\006status\030\002 \001(\0162!.we"
  "ed_tracking.PlantCaptchaStatus\022\024\n\014total_"
  "images\030\003 \001(\005\022\024\n\014images_taken\030\004 \001(\005\022\026\n\016me"
  "tadata_taken\030\005 \001(\005\"N\n\037GetNextPlantCaptch"
  "asListRequest\022+\n\002ts\030\001 \001(\0132\037.carbon.front"
  "end.util.Timestamp\"\212\001\n\024PlantCaptchaListI"
  "tem\022B\n\rplant_captcha\030\001 \001(\0132+.carbon.fron"
  "tend.plant_captcha.PlantCaptcha\022\024\n\014image"
  "s_taken\030\002 \001(\005\022\030\n\020images_processed\030\003 \001(\005\""
  "\234\001\n GetNextPlantCaptchasListResponse\022+\n\002"
  "ts\030\001 \001(\0132\037.carbon.frontend.util.Timestam"
  "p\022K\n\016plant_captchas\030\002 \003(\01323.carbon.front"
  "end.plant_captcha.PlantCaptchaListItem\")"
  "\n\031DeletePlantCaptchaRequest\022\014\n\004name\030\001 \001("
  "\t\"&\n\026GetPlantCaptchaRequest\022\014\n\004name\030\001 \001("
  "\t\"\306\001\n\020PlantCaptchaItem\022\021\n\timage_url\030\001 \001("
  "\t\0229\n\010metadata\030\002 \001(\0132\'.weed_tracking.Plan"
  "tCaptchaItemMetadata\022\035\n\025additional_image"
  "_urls\030\003 \003(\t\022E\n\024additional_metadatas\030\004 \003("
  "\0132\'.weed_tracking.PlantCaptchaItemMetada"
  "ta\"\235\001\n\027GetPlantCaptchaResponse\022B\n\rplant_"
  "captcha\030\001 \001(\0132+.carbon.frontend.plant_ca"
  "ptcha.PlantCaptcha\022>\n\005items\030\002 \003(\0132/.carb"
  "on.frontend.plant_captcha.PlantCaptchaIt"
  "em\".\n\036StartPlantCaptchaUploadRequest\022\014\n\004"
  "name\030\001 \001(\t\"b\n%GetNextPlantCaptchaUploadS"
  "tateRequest\022+\n\002ts\030\001 \001(\0132\037.carbon.fronten"
  "d.util.Timestamp\022\014\n\004name\030\002 \001(\t\"\264\001\n&GetNe"
  "xtPlantCaptchaUploadStateResponse\022+\n\002ts\030"
  "\001 \001(\0132\037.carbon.frontend.util.Timestamp\022L"
  "\n\014upload_state\030\002 \001(\01626.carbon.frontend.p"
  "lant_captcha.PlantCaptchaUploadState\022\017\n\007"
  "percent\030\003 \001(\005\"h\n\026PlantCaptchaItemResult\022"
  "\n\n\002id\030\001 \001(\t\022B\n\017user_prediction\030\002 \001(\0162).w"
  "eed_tracking.PlantCaptchaUserPrediction\""
  "x\n SubmitPlantCaptchaResultsRequest\022\014\n\004n"
  "ame\030\001 \001(\t\022F\n\007results\030\002 \003(\01325.carbon.fron"
  "tend.plant_captcha.PlantCaptchaItemResul"
  "t\"=\n!GetPlantCaptchaItemResultsRequest\022\014"
  "\n\004name\030\001 \001(\t\022\n\n\002id\030\002 \003(\t\"l\n\"GetPlantCapt"
  "chaItemResultsResponse\022F\n\007results\030\001 \003(\0132"
  "5.carbon.frontend.plant_captcha.PlantCap"
  "tchaItemResult\",\n\034CalculatePlantCaptchaR"
  "equest\022\014\n\004name\030\001 \001(\t\"\321\001\n\035CalculatePlantC"
  "aptchaResponse\022D\n\022modelinator_config\030\001 \001"
  "(\0132(.carbon.aimbot.almanac.ModelinatorCo"
  "nfig\022\021\n\tsucceeded\030\002 \001(\010\022W\n\016failure_reaso"
  "n\030\003 \001(\0162\?.carbon.frontend.plant_captcha."
  "PlantLabelAlgorithmFailureReason\"\211\001\n\022Pla"
  "ntCaptchaResult\0228\n\005label\030\001 \001(\0162).weed_tr"
  "acking.PlantCaptchaUserPrediction\0229\n\010met"
  "adata\030\002 \001(\0132\'.weed_tracking.PlantCaptcha"
  "ItemMetadata\"\305\010\n\023PlantCaptchaResults\022D\n\022"
  "current_parameters\030\001 \001(\0132(.carbon.aimbot"
  ".almanac.ModelinatorConfig\022J\n\017captcha_re"
  "sults\030\002 \003(\01321.carbon.frontend.plant_capt"
  "cha.PlantCaptchaResult\022\021\n\talgorithm\030\003 \001("
  "\t\022\033\n\023goal_crops_targeted\030\004 \001(\002\022\033\n\023goal_w"
  "eeds_targeted\030\005 \001(\002\022\035\n\025goal_unknown_targ"
  "eted\030\006 \001(\002\0225\n\007almanac\030\007 \001(\0132$.carbon.aim"
  "bot.almanac.AlmanacConfig\022\036\n\026max_recomme"
  "nded_mindoo\030\010 \001(\002\022$\n\034min_items_for_recom"
  "mendation\030\t \001(\005\022+\n#use_weed_categories_f"
  "or_weed_labels\030\n \001(\010\022\036\n\026min_recommended_"
  "mindoo\030\013 \001(\002\022&\n\036min_recommended_weed_thr"
  "eshold\030\014 \001(\002\022&\n\036max_recommended_weed_thr"
  "eshold\030\r \001(\002\022&\n\036min_recommended_crop_thr"
  "eshold\030\016 \001(\002\022&\n\036max_recommended_crop_thr"
  "eshold\030\017 \001(\002\022\"\n\032min_doo_for_recommendati"
  "on\030\020 \001(\002\022\037\n\027use_other_as_tiebreaker\030\021 \001("
  "\010\022\035\n\025limit_by_crops_missed\030\022 \001(\010\022%\n\035numb"
  "er_of_crop_configurations\030\023 \001(\005\022\022\n\ntiebr"
  "eaker\030\024 \001(\t\022\037\n\027pad_crop_configurations\030\025"
  " \001(\010\022\031\n\021mindoo_tiebreaker\030\026 \001(\t\022 \n\030use_b"
  "eneficials_as_crops\030\027 \001(\010\022\037\n\027use_volunte"
  "ers_as_weeds\030\030 \001(\010\022*\n\"tiebreaker_strateg"
  "y_threshold_weed\030\031 \001(\t\022*\n\"tiebreaker_str"
  "ategy_threshold_crop\030\032 \001(\t\022\'\n\037tiebreaker"
  "_strategy_mindoo_weed\030\033 \001(\t\022\'\n\037tiebreake"
  "r_strategy_mindoo_crop\030\034 \001(\t\"x\n\033VeselkaP"
  "lantCaptchaResponse\022F\n\024new_model_paramet"
  "ers\030\001 \001(\0132(.carbon.aimbot.almanac.Modeli"
  "natorConfig\022\021\n\tsucceeded\030\002 \001(\010\"3\n#GetOri"
  "ginalModelinatorConfigRequest\022\014\n\004name\030\001 "
  "\001(\t\"l\n$GetOriginalModelinatorConfigRespo"
  "nse\022D\n\022modelinator_config\030\001 \001(\0132(.carbon"
  ".aimbot.almanac.ModelinatorConfig\"\331\001\n\033Ge"
  "tCaptchaRowStatusResponse\022]\n\nrow_status\030"
  "\001 \003(\0132I.carbon.frontend.plant_captcha.Ge"
  "tCaptchaRowStatusResponse.RowStatusEntry"
  "\032[\n\016RowStatusEntry\022\013\n\003key\030\001 \001(\005\0228\n\005value"
  "\030\002 \001(\0132).weed_tracking.PlantCaptchaStatu"
  "sResponse:\0028\001\"0\n\036CancelPlantCaptchaOnRow"
  "Request\022\016\n\006row_id\030\001 \001(\005*>\n\027PlantCaptchaU"
  "ploadState\022\010\n\004NONE\020\000\022\017\n\013IN_PROGRESS\020\001\022\010\n"
  "\004DONE\020\002*]\n PlantLabelAlgorithmFailureRea"
  "son\022\016\n\nNO_FAILURE\020\000\022\023\n\017METRICS_NOT_MET\020\001"
  "\022\024\n\020NOT_ENOUGH_ITEMS\020\0022\211\017\n\023PlantCaptchaS"
  "ervice\022\206\001\n\021StartPlantCaptcha\0227.carbon.fr"
  "ontend.plant_captcha.StartPlantCaptchaRe"
  "quest\0328.carbon.frontend.plant_captcha.St"
  "artPlantCaptchaResponse\022\236\001\n\031GetNextPlant"
  "CaptchaStatus\022\?.carbon.frontend.plant_ca"
  "ptcha.GetNextPlantCaptchaStatusRequest\032@"
  ".carbon.frontend.plant_captcha.GetNextPl"
  "antCaptchaStatusResponse\022\233\001\n\030GetNextPlan"
  "tCaptchasList\022>.carbon.frontend.plant_ca"
  "ptcha.GetNextPlantCaptchasListRequest\032\?."
  "carbon.frontend.plant_captcha.GetNextPla"
  "ntCaptchasListResponse\022k\n\022DeletePlantCap"
  "tcha\0228.carbon.frontend.plant_captcha.Del"
  "etePlantCaptchaRequest\032\033.carbon.frontend"
  ".util.Empty\022\200\001\n\017GetPlantCaptcha\0225.carbon"
  ".frontend.plant_captcha.GetPlantCaptchaR"
  "equest\0326.carbon.frontend.plant_captcha.G"
  "etPlantCaptchaResponse\022N\n\022CancelPlantCap"
  "tcha\022\033.carbon.frontend.util.Empty\032\033.carb"
  "on.frontend.util.Empty\022u\n\027StartPlantCapt"
  "chaUpload\022=.carbon.frontend.plant_captch"
  "a.StartPlantCaptchaUploadRequest\032\033.carbo"
  "n.frontend.util.Empty\022\255\001\n\036GetNextPlantCa"
  "ptchaUploadState\022D.carbon.frontend.plant"
  "_captcha.GetNextPlantCaptchaUploadStateR"
  "equest\032E.carbon.frontend.plant_captcha.G"
  "etNextPlantCaptchaUploadStateResponse\022y\n"
  "\031SubmitPlantCaptchaResults\022\?.carbon.fron"
  "tend.plant_captcha.SubmitPlantCaptchaRes"
  "ultsRequest\032\033.carbon.frontend.util.Empty"
  "\022\241\001\n\032GetPlantCaptchaItemResults\022@.carbon"
  ".frontend.plant_captcha.GetPlantCaptchaI"
  "temResultsRequest\032A.carbon.frontend.plan"
  "t_captcha.GetPlantCaptchaItemResultsResp"
  "onse\022\222\001\n\025CalculatePlantCaptcha\022;.carbon."
  "frontend.plant_captcha.CalculatePlantCap"
  "tchaRequest\032<.carbon.frontend.plant_capt"
  "cha.CalculatePlantCaptchaResponse\022\247\001\n\034Ge"
  "tOriginalModelinatorConfig\022B.carbon.fron"
  "tend.plant_captcha.GetOriginalModelinato"
  "rConfigRequest\032C.carbon.frontend.plant_c"
  "aptcha.GetOriginalModelinatorConfigRespo"
  "nse\022n\n\023GetCaptchaRowStatus\022\033.carbon.fron"
  "tend.util.Empty\032:.carbon.frontend.plant_"
  "captcha.GetCaptchaRowStatusResponse\022u\n\027C"
  "ancelPlantCaptchaOnRow\022=.carbon.frontend"
  ".plant_captcha.CancelPlantCaptchaOnRowRe"
  "quest\032\033.carbon.frontend.util.EmptyB\020Z\016pr"
  "oto/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_deps[3] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
  &::descriptor_table_proto_2falmanac_2falmanac_2eproto,
  &::descriptor_table_weed_5ftracking_2fproto_2fweed_5ftracking_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto = {
  false, false, 6420, descriptor_table_protodef_frontend_2fproto_2fplant_5fcaptcha_2eproto, "frontend/proto/plant_captcha.proto", 
  &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once, descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_deps, 3, 29,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto, file_level_enum_descriptors_frontend_2fproto_2fplant_5fcaptcha_2eproto, file_level_service_descriptors_frontend_2fproto_2fplant_5fcaptcha_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fplant_5fcaptcha_2eproto(&descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto);
namespace carbon {
namespace frontend {
namespace plant_captcha {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PlantCaptchaUploadState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fplant_5fcaptcha_2eproto[0];
}
bool PlantCaptchaUploadState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PlantLabelAlgorithmFailureReason_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fplant_5fcaptcha_2eproto[1];
}
bool PlantLabelAlgorithmFailureReason_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class PlantCaptcha::_Internal {
 public:
};

PlantCaptcha::PlantCaptcha(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  rows_used_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.PlantCaptcha)
}
PlantCaptcha::PlantCaptcha(const PlantCaptcha& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      rows_used_(from.rows_used_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_id().empty()) {
    model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_id(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  crop_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_name().empty()) {
    crop_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_name(), 
      GetArenaForAllocation());
  }
  start_time_ms_ = from.start_time_ms_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.PlantCaptcha)
}

inline void PlantCaptcha::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
start_time_ms_ = int64_t{0};
}

PlantCaptcha::~PlantCaptcha() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.PlantCaptcha)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PlantCaptcha::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void PlantCaptcha::ArenaDtor(void* object) {
  PlantCaptcha* _this = reinterpret_cast< PlantCaptcha* >(object);
  (void)_this;
}
void PlantCaptcha::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PlantCaptcha::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PlantCaptcha::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.PlantCaptcha)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  rows_used_.Clear();
  name_.ClearToEmpty();
  model_id_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  crop_name_.ClearToEmpty();
  start_time_ms_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PlantCaptcha::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptcha.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptcha.model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptcha.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_name = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_crop_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptcha.crop_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 start_time_ms = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          start_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int32 rows_used = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_rows_used(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 48) {
          _internal_add_rows_used(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PlantCaptcha::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.PlantCaptcha)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptcha.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // string model_id = 2;
  if (!this->_internal_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_id().data(), static_cast<int>(this->_internal_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptcha.model_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_model_id(), target);
  }

  // string crop_id = 3;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptcha.crop_id");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_crop_id(), target);
  }

  // string crop_name = 4;
  if (!this->_internal_crop_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_name().data(), static_cast<int>(this->_internal_crop_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptcha.crop_name");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_crop_name(), target);
  }

  // int64 start_time_ms = 5;
  if (this->_internal_start_time_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(5, this->_internal_start_time_ms(), target);
  }

  // repeated int32 rows_used = 6;
  {
    int byte_size = _rows_used_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          6, _internal_rows_used(), byte_size, target);
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.PlantCaptcha)
  return target;
}

size_t PlantCaptcha::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.PlantCaptcha)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int32 rows_used = 6;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->rows_used_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _rows_used_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string model_id = 2;
  if (!this->_internal_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_id());
  }

  // string crop_id = 3;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // string crop_name = 4;
  if (!this->_internal_crop_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_name());
  }

  // int64 start_time_ms = 5;
  if (this->_internal_start_time_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_start_time_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PlantCaptcha::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PlantCaptcha::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PlantCaptcha::GetClassData() const { return &_class_data_; }

void PlantCaptcha::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PlantCaptcha *>(to)->MergeFrom(
      static_cast<const PlantCaptcha &>(from));
}


void PlantCaptcha::MergeFrom(const PlantCaptcha& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.PlantCaptcha)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  rows_used_.MergeFrom(from.rows_used_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_model_id().empty()) {
    _internal_set_model_id(from._internal_model_id());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (!from._internal_crop_name().empty()) {
    _internal_set_crop_name(from._internal_crop_name());
  }
  if (from._internal_start_time_ms() != 0) {
    _internal_set_start_time_ms(from._internal_start_time_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PlantCaptcha::CopyFrom(const PlantCaptcha& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.PlantCaptcha)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PlantCaptcha::IsInitialized() const {
  return true;
}

void PlantCaptcha::InternalSwap(PlantCaptcha* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  rows_used_.InternalSwap(&other->rows_used_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_id_, lhs_arena,
      &other->model_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_name_, lhs_arena,
      &other->crop_name_, rhs_arena
  );
  swap(start_time_ms_, other->start_time_ms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PlantCaptcha::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[0]);
}

// ===================================================================

class StartPlantCaptchaRequest::_Internal {
 public:
  static const ::carbon::frontend::plant_captcha::PlantCaptcha& plant_captcha(const StartPlantCaptchaRequest* msg);
};

const ::carbon::frontend::plant_captcha::PlantCaptcha&
StartPlantCaptchaRequest::_Internal::plant_captcha(const StartPlantCaptchaRequest* msg) {
  return *msg->plant_captcha_;
}
StartPlantCaptchaRequest::StartPlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.StartPlantCaptchaRequest)
}
StartPlantCaptchaRequest::StartPlantCaptchaRequest(const StartPlantCaptchaRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_plant_captcha()) {
    plant_captcha_ = new ::carbon::frontend::plant_captcha::PlantCaptcha(*from.plant_captcha_);
  } else {
    plant_captcha_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.StartPlantCaptchaRequest)
}

inline void StartPlantCaptchaRequest::SharedCtor() {
plant_captcha_ = nullptr;
}

StartPlantCaptchaRequest::~StartPlantCaptchaRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.StartPlantCaptchaRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StartPlantCaptchaRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete plant_captcha_;
}

void StartPlantCaptchaRequest::ArenaDtor(void* object) {
  StartPlantCaptchaRequest* _this = reinterpret_cast< StartPlantCaptchaRequest* >(object);
  (void)_this;
}
void StartPlantCaptchaRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StartPlantCaptchaRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StartPlantCaptchaRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.StartPlantCaptchaRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && plant_captcha_ != nullptr) {
    delete plant_captcha_;
  }
  plant_captcha_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StartPlantCaptchaRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_plant_captcha(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* StartPlantCaptchaRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.StartPlantCaptchaRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
  if (this->_internal_has_plant_captcha()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::plant_captcha(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.StartPlantCaptchaRequest)
  return target;
}

size_t StartPlantCaptchaRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.StartPlantCaptchaRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
  if (this->_internal_has_plant_captcha()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *plant_captcha_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StartPlantCaptchaRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StartPlantCaptchaRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StartPlantCaptchaRequest::GetClassData() const { return &_class_data_; }

void StartPlantCaptchaRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StartPlantCaptchaRequest *>(to)->MergeFrom(
      static_cast<const StartPlantCaptchaRequest &>(from));
}


void StartPlantCaptchaRequest::MergeFrom(const StartPlantCaptchaRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.StartPlantCaptchaRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_plant_captcha()) {
    _internal_mutable_plant_captcha()->::carbon::frontend::plant_captcha::PlantCaptcha::MergeFrom(from._internal_plant_captcha());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StartPlantCaptchaRequest::CopyFrom(const StartPlantCaptchaRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.StartPlantCaptchaRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StartPlantCaptchaRequest::IsInitialized() const {
  return true;
}

void StartPlantCaptchaRequest::InternalSwap(StartPlantCaptchaRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(plant_captcha_, other->plant_captcha_);
}

::PROTOBUF_NAMESPACE_ID::Metadata StartPlantCaptchaRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[1]);
}

// ===================================================================

class StartPlantCaptchaResponse::_Internal {
 public:
};

StartPlantCaptchaResponse::StartPlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.StartPlantCaptchaResponse)
}
StartPlantCaptchaResponse::StartPlantCaptchaResponse(const StartPlantCaptchaResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.StartPlantCaptchaResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StartPlantCaptchaResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StartPlantCaptchaResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata StartPlantCaptchaResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[2]);
}

// ===================================================================

class GetNextPlantCaptchaStatusRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextPlantCaptchaStatusRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextPlantCaptchaStatusRequest::_Internal::ts(const GetNextPlantCaptchaStatusRequest* msg) {
  return *msg->ts_;
}
void GetNextPlantCaptchaStatusRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextPlantCaptchaStatusRequest::GetNextPlantCaptchaStatusRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest)
}
GetNextPlantCaptchaStatusRequest::GetNextPlantCaptchaStatusRequest(const GetNextPlantCaptchaStatusRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest)
}

inline void GetNextPlantCaptchaStatusRequest::SharedCtor() {
ts_ = nullptr;
}

GetNextPlantCaptchaStatusRequest::~GetNextPlantCaptchaStatusRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextPlantCaptchaStatusRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextPlantCaptchaStatusRequest::ArenaDtor(void* object) {
  GetNextPlantCaptchaStatusRequest* _this = reinterpret_cast< GetNextPlantCaptchaStatusRequest* >(object);
  (void)_this;
}
void GetNextPlantCaptchaStatusRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextPlantCaptchaStatusRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextPlantCaptchaStatusRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextPlantCaptchaStatusRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextPlantCaptchaStatusRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest)
  return target;
}

size_t GetNextPlantCaptchaStatusRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextPlantCaptchaStatusRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextPlantCaptchaStatusRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextPlantCaptchaStatusRequest::GetClassData() const { return &_class_data_; }

void GetNextPlantCaptchaStatusRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextPlantCaptchaStatusRequest *>(to)->MergeFrom(
      static_cast<const GetNextPlantCaptchaStatusRequest &>(from));
}


void GetNextPlantCaptchaStatusRequest::MergeFrom(const GetNextPlantCaptchaStatusRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextPlantCaptchaStatusRequest::CopyFrom(const GetNextPlantCaptchaStatusRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextPlantCaptchaStatusRequest::IsInitialized() const {
  return true;
}

void GetNextPlantCaptchaStatusRequest::InternalSwap(GetNextPlantCaptchaStatusRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextPlantCaptchaStatusRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[3]);
}

// ===================================================================

class GetNextPlantCaptchaStatusResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextPlantCaptchaStatusResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextPlantCaptchaStatusResponse::_Internal::ts(const GetNextPlantCaptchaStatusResponse* msg) {
  return *msg->ts_;
}
void GetNextPlantCaptchaStatusResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextPlantCaptchaStatusResponse::GetNextPlantCaptchaStatusResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse)
}
GetNextPlantCaptchaStatusResponse::GetNextPlantCaptchaStatusResponse(const GetNextPlantCaptchaStatusResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&status_, &from.status_,
    static_cast<size_t>(reinterpret_cast<char*>(&metadata_taken_) -
    reinterpret_cast<char*>(&status_)) + sizeof(metadata_taken_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse)
}

inline void GetNextPlantCaptchaStatusResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&metadata_taken_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(metadata_taken_));
}

GetNextPlantCaptchaStatusResponse::~GetNextPlantCaptchaStatusResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextPlantCaptchaStatusResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextPlantCaptchaStatusResponse::ArenaDtor(void* object) {
  GetNextPlantCaptchaStatusResponse* _this = reinterpret_cast< GetNextPlantCaptchaStatusResponse* >(object);
  (void)_this;
}
void GetNextPlantCaptchaStatusResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextPlantCaptchaStatusResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextPlantCaptchaStatusResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&status_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&metadata_taken_) -
      reinterpret_cast<char*>(&status_)) + sizeof(metadata_taken_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextPlantCaptchaStatusResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .weed_tracking.PlantCaptchaStatus status = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_status(static_cast<::weed_tracking::PlantCaptchaStatus>(val));
        } else
          goto handle_unusual;
        continue;
      // int32 total_images = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          total_images_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 images_taken = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          images_taken_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 metadata_taken = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          metadata_taken_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextPlantCaptchaStatusResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // .weed_tracking.PlantCaptchaStatus status = 2;
  if (this->_internal_status() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_status(), target);
  }

  // int32 total_images = 3;
  if (this->_internal_total_images() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_total_images(), target);
  }

  // int32 images_taken = 4;
  if (this->_internal_images_taken() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_images_taken(), target);
  }

  // int32 metadata_taken = 5;
  if (this->_internal_metadata_taken() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_metadata_taken(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse)
  return target;
}

size_t GetNextPlantCaptchaStatusResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .weed_tracking.PlantCaptchaStatus status = 2;
  if (this->_internal_status() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_status());
  }

  // int32 total_images = 3;
  if (this->_internal_total_images() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_total_images());
  }

  // int32 images_taken = 4;
  if (this->_internal_images_taken() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_images_taken());
  }

  // int32 metadata_taken = 5;
  if (this->_internal_metadata_taken() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_metadata_taken());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextPlantCaptchaStatusResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextPlantCaptchaStatusResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextPlantCaptchaStatusResponse::GetClassData() const { return &_class_data_; }

void GetNextPlantCaptchaStatusResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextPlantCaptchaStatusResponse *>(to)->MergeFrom(
      static_cast<const GetNextPlantCaptchaStatusResponse &>(from));
}


void GetNextPlantCaptchaStatusResponse::MergeFrom(const GetNextPlantCaptchaStatusResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_status() != 0) {
    _internal_set_status(from._internal_status());
  }
  if (from._internal_total_images() != 0) {
    _internal_set_total_images(from._internal_total_images());
  }
  if (from._internal_images_taken() != 0) {
    _internal_set_images_taken(from._internal_images_taken());
  }
  if (from._internal_metadata_taken() != 0) {
    _internal_set_metadata_taken(from._internal_metadata_taken());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextPlantCaptchaStatusResponse::CopyFrom(const GetNextPlantCaptchaStatusResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextPlantCaptchaStatusResponse::IsInitialized() const {
  return true;
}

void GetNextPlantCaptchaStatusResponse::InternalSwap(GetNextPlantCaptchaStatusResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextPlantCaptchaStatusResponse, metadata_taken_)
      + sizeof(GetNextPlantCaptchaStatusResponse::metadata_taken_)
      - PROTOBUF_FIELD_OFFSET(GetNextPlantCaptchaStatusResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextPlantCaptchaStatusResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[4]);
}

// ===================================================================

class GetNextPlantCaptchasListRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextPlantCaptchasListRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextPlantCaptchasListRequest::_Internal::ts(const GetNextPlantCaptchasListRequest* msg) {
  return *msg->ts_;
}
void GetNextPlantCaptchasListRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextPlantCaptchasListRequest::GetNextPlantCaptchasListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest)
}
GetNextPlantCaptchasListRequest::GetNextPlantCaptchasListRequest(const GetNextPlantCaptchasListRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest)
}

inline void GetNextPlantCaptchasListRequest::SharedCtor() {
ts_ = nullptr;
}

GetNextPlantCaptchasListRequest::~GetNextPlantCaptchasListRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextPlantCaptchasListRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextPlantCaptchasListRequest::ArenaDtor(void* object) {
  GetNextPlantCaptchasListRequest* _this = reinterpret_cast< GetNextPlantCaptchasListRequest* >(object);
  (void)_this;
}
void GetNextPlantCaptchasListRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextPlantCaptchasListRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextPlantCaptchasListRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextPlantCaptchasListRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextPlantCaptchasListRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest)
  return target;
}

size_t GetNextPlantCaptchasListRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextPlantCaptchasListRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextPlantCaptchasListRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextPlantCaptchasListRequest::GetClassData() const { return &_class_data_; }

void GetNextPlantCaptchasListRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextPlantCaptchasListRequest *>(to)->MergeFrom(
      static_cast<const GetNextPlantCaptchasListRequest &>(from));
}


void GetNextPlantCaptchasListRequest::MergeFrom(const GetNextPlantCaptchasListRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextPlantCaptchasListRequest::CopyFrom(const GetNextPlantCaptchasListRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextPlantCaptchasListRequest::IsInitialized() const {
  return true;
}

void GetNextPlantCaptchasListRequest::InternalSwap(GetNextPlantCaptchasListRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextPlantCaptchasListRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[5]);
}

// ===================================================================

class PlantCaptchaListItem::_Internal {
 public:
  static const ::carbon::frontend::plant_captcha::PlantCaptcha& plant_captcha(const PlantCaptchaListItem* msg);
};

const ::carbon::frontend::plant_captcha::PlantCaptcha&
PlantCaptchaListItem::_Internal::plant_captcha(const PlantCaptchaListItem* msg) {
  return *msg->plant_captcha_;
}
PlantCaptchaListItem::PlantCaptchaListItem(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.PlantCaptchaListItem)
}
PlantCaptchaListItem::PlantCaptchaListItem(const PlantCaptchaListItem& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_plant_captcha()) {
    plant_captcha_ = new ::carbon::frontend::plant_captcha::PlantCaptcha(*from.plant_captcha_);
  } else {
    plant_captcha_ = nullptr;
  }
  ::memcpy(&images_taken_, &from.images_taken_,
    static_cast<size_t>(reinterpret_cast<char*>(&images_processed_) -
    reinterpret_cast<char*>(&images_taken_)) + sizeof(images_processed_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.PlantCaptchaListItem)
}

inline void PlantCaptchaListItem::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&plant_captcha_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&images_processed_) -
    reinterpret_cast<char*>(&plant_captcha_)) + sizeof(images_processed_));
}

PlantCaptchaListItem::~PlantCaptchaListItem() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.PlantCaptchaListItem)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PlantCaptchaListItem::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete plant_captcha_;
}

void PlantCaptchaListItem::ArenaDtor(void* object) {
  PlantCaptchaListItem* _this = reinterpret_cast< PlantCaptchaListItem* >(object);
  (void)_this;
}
void PlantCaptchaListItem::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PlantCaptchaListItem::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PlantCaptchaListItem::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.PlantCaptchaListItem)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && plant_captcha_ != nullptr) {
    delete plant_captcha_;
  }
  plant_captcha_ = nullptr;
  ::memset(&images_taken_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&images_processed_) -
      reinterpret_cast<char*>(&images_taken_)) + sizeof(images_processed_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PlantCaptchaListItem::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_plant_captcha(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 images_taken = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          images_taken_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 images_processed = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          images_processed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PlantCaptchaListItem::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.PlantCaptchaListItem)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
  if (this->_internal_has_plant_captcha()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::plant_captcha(this), target, stream);
  }

  // int32 images_taken = 2;
  if (this->_internal_images_taken() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_images_taken(), target);
  }

  // int32 images_processed = 3;
  if (this->_internal_images_processed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_images_processed(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.PlantCaptchaListItem)
  return target;
}

size_t PlantCaptchaListItem::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.PlantCaptchaListItem)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
  if (this->_internal_has_plant_captcha()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *plant_captcha_);
  }

  // int32 images_taken = 2;
  if (this->_internal_images_taken() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_images_taken());
  }

  // int32 images_processed = 3;
  if (this->_internal_images_processed() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_images_processed());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PlantCaptchaListItem::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PlantCaptchaListItem::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PlantCaptchaListItem::GetClassData() const { return &_class_data_; }

void PlantCaptchaListItem::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PlantCaptchaListItem *>(to)->MergeFrom(
      static_cast<const PlantCaptchaListItem &>(from));
}


void PlantCaptchaListItem::MergeFrom(const PlantCaptchaListItem& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.PlantCaptchaListItem)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_plant_captcha()) {
    _internal_mutable_plant_captcha()->::carbon::frontend::plant_captcha::PlantCaptcha::MergeFrom(from._internal_plant_captcha());
  }
  if (from._internal_images_taken() != 0) {
    _internal_set_images_taken(from._internal_images_taken());
  }
  if (from._internal_images_processed() != 0) {
    _internal_set_images_processed(from._internal_images_processed());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PlantCaptchaListItem::CopyFrom(const PlantCaptchaListItem& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.PlantCaptchaListItem)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PlantCaptchaListItem::IsInitialized() const {
  return true;
}

void PlantCaptchaListItem::InternalSwap(PlantCaptchaListItem* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PlantCaptchaListItem, images_processed_)
      + sizeof(PlantCaptchaListItem::images_processed_)
      - PROTOBUF_FIELD_OFFSET(PlantCaptchaListItem, plant_captcha_)>(
          reinterpret_cast<char*>(&plant_captcha_),
          reinterpret_cast<char*>(&other->plant_captcha_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PlantCaptchaListItem::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[6]);
}

// ===================================================================

class GetNextPlantCaptchasListResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextPlantCaptchasListResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextPlantCaptchasListResponse::_Internal::ts(const GetNextPlantCaptchasListResponse* msg) {
  return *msg->ts_;
}
void GetNextPlantCaptchasListResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextPlantCaptchasListResponse::GetNextPlantCaptchasListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  plant_captchas_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse)
}
GetNextPlantCaptchasListResponse::GetNextPlantCaptchasListResponse(const GetNextPlantCaptchasListResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      plant_captchas_(from.plant_captchas_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse)
}

inline void GetNextPlantCaptchasListResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextPlantCaptchasListResponse::~GetNextPlantCaptchasListResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextPlantCaptchasListResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextPlantCaptchasListResponse::ArenaDtor(void* object) {
  GetNextPlantCaptchasListResponse* _this = reinterpret_cast< GetNextPlantCaptchasListResponse* >(object);
  (void)_this;
}
void GetNextPlantCaptchasListResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextPlantCaptchasListResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextPlantCaptchasListResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  plant_captchas_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextPlantCaptchasListResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.plant_captcha.PlantCaptchaListItem plant_captchas = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_plant_captchas(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextPlantCaptchasListResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.frontend.plant_captcha.PlantCaptchaListItem plant_captchas = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_plant_captchas_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_plant_captchas(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse)
  return target;
}

size_t GetNextPlantCaptchasListResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.plant_captcha.PlantCaptchaListItem plant_captchas = 2;
  total_size += 1UL * this->_internal_plant_captchas_size();
  for (const auto& msg : this->plant_captchas_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextPlantCaptchasListResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextPlantCaptchasListResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextPlantCaptchasListResponse::GetClassData() const { return &_class_data_; }

void GetNextPlantCaptchasListResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextPlantCaptchasListResponse *>(to)->MergeFrom(
      static_cast<const GetNextPlantCaptchasListResponse &>(from));
}


void GetNextPlantCaptchasListResponse::MergeFrom(const GetNextPlantCaptchasListResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  plant_captchas_.MergeFrom(from.plant_captchas_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextPlantCaptchasListResponse::CopyFrom(const GetNextPlantCaptchasListResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextPlantCaptchasListResponse::IsInitialized() const {
  return true;
}

void GetNextPlantCaptchasListResponse::InternalSwap(GetNextPlantCaptchasListResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  plant_captchas_.InternalSwap(&other->plant_captchas_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextPlantCaptchasListResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[7]);
}

// ===================================================================

class DeletePlantCaptchaRequest::_Internal {
 public:
};

DeletePlantCaptchaRequest::DeletePlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest)
}
DeletePlantCaptchaRequest::DeletePlantCaptchaRequest(const DeletePlantCaptchaRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest)
}

inline void DeletePlantCaptchaRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DeletePlantCaptchaRequest::~DeletePlantCaptchaRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeletePlantCaptchaRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DeletePlantCaptchaRequest::ArenaDtor(void* object) {
  DeletePlantCaptchaRequest* _this = reinterpret_cast< DeletePlantCaptchaRequest* >(object);
  (void)_this;
}
void DeletePlantCaptchaRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeletePlantCaptchaRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeletePlantCaptchaRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeletePlantCaptchaRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.DeletePlantCaptchaRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeletePlantCaptchaRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.DeletePlantCaptchaRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest)
  return target;
}

size_t DeletePlantCaptchaRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeletePlantCaptchaRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeletePlantCaptchaRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeletePlantCaptchaRequest::GetClassData() const { return &_class_data_; }

void DeletePlantCaptchaRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeletePlantCaptchaRequest *>(to)->MergeFrom(
      static_cast<const DeletePlantCaptchaRequest &>(from));
}


void DeletePlantCaptchaRequest::MergeFrom(const DeletePlantCaptchaRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeletePlantCaptchaRequest::CopyFrom(const DeletePlantCaptchaRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeletePlantCaptchaRequest::IsInitialized() const {
  return true;
}

void DeletePlantCaptchaRequest::InternalSwap(DeletePlantCaptchaRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DeletePlantCaptchaRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[8]);
}

// ===================================================================

class GetPlantCaptchaRequest::_Internal {
 public:
};

GetPlantCaptchaRequest::GetPlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetPlantCaptchaRequest)
}
GetPlantCaptchaRequest::GetPlantCaptchaRequest(const GetPlantCaptchaRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetPlantCaptchaRequest)
}

inline void GetPlantCaptchaRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetPlantCaptchaRequest::~GetPlantCaptchaRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetPlantCaptchaRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetPlantCaptchaRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetPlantCaptchaRequest::ArenaDtor(void* object) {
  GetPlantCaptchaRequest* _this = reinterpret_cast< GetPlantCaptchaRequest* >(object);
  (void)_this;
}
void GetPlantCaptchaRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetPlantCaptchaRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetPlantCaptchaRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetPlantCaptchaRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetPlantCaptchaRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.GetPlantCaptchaRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetPlantCaptchaRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetPlantCaptchaRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.GetPlantCaptchaRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetPlantCaptchaRequest)
  return target;
}

size_t GetPlantCaptchaRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetPlantCaptchaRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPlantCaptchaRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetPlantCaptchaRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPlantCaptchaRequest::GetClassData() const { return &_class_data_; }

void GetPlantCaptchaRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetPlantCaptchaRequest *>(to)->MergeFrom(
      static_cast<const GetPlantCaptchaRequest &>(from));
}


void GetPlantCaptchaRequest::MergeFrom(const GetPlantCaptchaRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetPlantCaptchaRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetPlantCaptchaRequest::CopyFrom(const GetPlantCaptchaRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetPlantCaptchaRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetPlantCaptchaRequest::IsInitialized() const {
  return true;
}

void GetPlantCaptchaRequest::InternalSwap(GetPlantCaptchaRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetPlantCaptchaRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[9]);
}

// ===================================================================

class PlantCaptchaItem::_Internal {
 public:
  static const ::weed_tracking::PlantCaptchaItemMetadata& metadata(const PlantCaptchaItem* msg);
};

const ::weed_tracking::PlantCaptchaItemMetadata&
PlantCaptchaItem::_Internal::metadata(const PlantCaptchaItem* msg) {
  return *msg->metadata_;
}
void PlantCaptchaItem::clear_metadata() {
  if (GetArenaForAllocation() == nullptr && metadata_ != nullptr) {
    delete metadata_;
  }
  metadata_ = nullptr;
}
void PlantCaptchaItem::clear_additional_metadatas() {
  additional_metadatas_.Clear();
}
PlantCaptchaItem::PlantCaptchaItem(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  additional_image_urls_(arena),
  additional_metadatas_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.PlantCaptchaItem)
}
PlantCaptchaItem::PlantCaptchaItem(const PlantCaptchaItem& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      additional_image_urls_(from.additional_image_urls_),
      additional_metadatas_(from.additional_metadatas_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  image_url_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    image_url_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_image_url().empty()) {
    image_url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_image_url(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_metadata()) {
    metadata_ = new ::weed_tracking::PlantCaptchaItemMetadata(*from.metadata_);
  } else {
    metadata_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.PlantCaptchaItem)
}

inline void PlantCaptchaItem::SharedCtor() {
image_url_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  image_url_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
metadata_ = nullptr;
}

PlantCaptchaItem::~PlantCaptchaItem() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.PlantCaptchaItem)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PlantCaptchaItem::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  image_url_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete metadata_;
}

void PlantCaptchaItem::ArenaDtor(void* object) {
  PlantCaptchaItem* _this = reinterpret_cast< PlantCaptchaItem* >(object);
  (void)_this;
}
void PlantCaptchaItem::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PlantCaptchaItem::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PlantCaptchaItem::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.PlantCaptchaItem)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  additional_image_urls_.Clear();
  additional_metadatas_.Clear();
  image_url_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && metadata_ != nullptr) {
    delete metadata_;
  }
  metadata_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PlantCaptchaItem::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string image_url = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_image_url();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptchaItem.image_url"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .weed_tracking.PlantCaptchaItemMetadata metadata = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_metadata(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string additional_image_urls = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_additional_image_urls();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .weed_tracking.PlantCaptchaItemMetadata additional_metadatas = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_additional_metadatas(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PlantCaptchaItem::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.PlantCaptchaItem)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string image_url = 1;
  if (!this->_internal_image_url().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_image_url().data(), static_cast<int>(this->_internal_image_url().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptchaItem.image_url");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_image_url(), target);
  }

  // .weed_tracking.PlantCaptchaItemMetadata metadata = 2;
  if (this->_internal_has_metadata()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::metadata(this), target, stream);
  }

  // repeated string additional_image_urls = 3;
  for (int i = 0, n = this->_internal_additional_image_urls_size(); i < n; i++) {
    const auto& s = this->_internal_additional_image_urls(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls");
    target = stream->WriteString(3, s, target);
  }

  // repeated .weed_tracking.PlantCaptchaItemMetadata additional_metadatas = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_additional_metadatas_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_additional_metadatas(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.PlantCaptchaItem)
  return target;
}

size_t PlantCaptchaItem::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.PlantCaptchaItem)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string additional_image_urls = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(additional_image_urls_.size());
  for (int i = 0, n = additional_image_urls_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      additional_image_urls_.Get(i));
  }

  // repeated .weed_tracking.PlantCaptchaItemMetadata additional_metadatas = 4;
  total_size += 1UL * this->_internal_additional_metadatas_size();
  for (const auto& msg : this->additional_metadatas_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string image_url = 1;
  if (!this->_internal_image_url().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_image_url());
  }

  // .weed_tracking.PlantCaptchaItemMetadata metadata = 2;
  if (this->_internal_has_metadata()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *metadata_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PlantCaptchaItem::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PlantCaptchaItem::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PlantCaptchaItem::GetClassData() const { return &_class_data_; }

void PlantCaptchaItem::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PlantCaptchaItem *>(to)->MergeFrom(
      static_cast<const PlantCaptchaItem &>(from));
}


void PlantCaptchaItem::MergeFrom(const PlantCaptchaItem& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.PlantCaptchaItem)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  additional_image_urls_.MergeFrom(from.additional_image_urls_);
  additional_metadatas_.MergeFrom(from.additional_metadatas_);
  if (!from._internal_image_url().empty()) {
    _internal_set_image_url(from._internal_image_url());
  }
  if (from._internal_has_metadata()) {
    _internal_mutable_metadata()->::weed_tracking::PlantCaptchaItemMetadata::MergeFrom(from._internal_metadata());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PlantCaptchaItem::CopyFrom(const PlantCaptchaItem& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.PlantCaptchaItem)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PlantCaptchaItem::IsInitialized() const {
  return true;
}

void PlantCaptchaItem::InternalSwap(PlantCaptchaItem* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  additional_image_urls_.InternalSwap(&other->additional_image_urls_);
  additional_metadatas_.InternalSwap(&other->additional_metadatas_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &image_url_, lhs_arena,
      &other->image_url_, rhs_arena
  );
  swap(metadata_, other->metadata_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PlantCaptchaItem::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[10]);
}

// ===================================================================

class GetPlantCaptchaResponse::_Internal {
 public:
  static const ::carbon::frontend::plant_captcha::PlantCaptcha& plant_captcha(const GetPlantCaptchaResponse* msg);
};

const ::carbon::frontend::plant_captcha::PlantCaptcha&
GetPlantCaptchaResponse::_Internal::plant_captcha(const GetPlantCaptchaResponse* msg) {
  return *msg->plant_captcha_;
}
GetPlantCaptchaResponse::GetPlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  items_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetPlantCaptchaResponse)
}
GetPlantCaptchaResponse::GetPlantCaptchaResponse(const GetPlantCaptchaResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      items_(from.items_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_plant_captcha()) {
    plant_captcha_ = new ::carbon::frontend::plant_captcha::PlantCaptcha(*from.plant_captcha_);
  } else {
    plant_captcha_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetPlantCaptchaResponse)
}

inline void GetPlantCaptchaResponse::SharedCtor() {
plant_captcha_ = nullptr;
}

GetPlantCaptchaResponse::~GetPlantCaptchaResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetPlantCaptchaResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetPlantCaptchaResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete plant_captcha_;
}

void GetPlantCaptchaResponse::ArenaDtor(void* object) {
  GetPlantCaptchaResponse* _this = reinterpret_cast< GetPlantCaptchaResponse* >(object);
  (void)_this;
}
void GetPlantCaptchaResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetPlantCaptchaResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetPlantCaptchaResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetPlantCaptchaResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  items_.Clear();
  if (GetArenaForAllocation() == nullptr && plant_captcha_ != nullptr) {
    delete plant_captcha_;
  }
  plant_captcha_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetPlantCaptchaResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_plant_captcha(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.plant_captcha.PlantCaptchaItem items = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_items(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetPlantCaptchaResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetPlantCaptchaResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
  if (this->_internal_has_plant_captcha()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::plant_captcha(this), target, stream);
  }

  // repeated .carbon.frontend.plant_captcha.PlantCaptchaItem items = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_items_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_items(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetPlantCaptchaResponse)
  return target;
}

size_t GetPlantCaptchaResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetPlantCaptchaResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.plant_captcha.PlantCaptchaItem items = 2;
  total_size += 1UL * this->_internal_items_size();
  for (const auto& msg : this->items_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
  if (this->_internal_has_plant_captcha()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *plant_captcha_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPlantCaptchaResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetPlantCaptchaResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPlantCaptchaResponse::GetClassData() const { return &_class_data_; }

void GetPlantCaptchaResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetPlantCaptchaResponse *>(to)->MergeFrom(
      static_cast<const GetPlantCaptchaResponse &>(from));
}


void GetPlantCaptchaResponse::MergeFrom(const GetPlantCaptchaResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetPlantCaptchaResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  items_.MergeFrom(from.items_);
  if (from._internal_has_plant_captcha()) {
    _internal_mutable_plant_captcha()->::carbon::frontend::plant_captcha::PlantCaptcha::MergeFrom(from._internal_plant_captcha());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetPlantCaptchaResponse::CopyFrom(const GetPlantCaptchaResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetPlantCaptchaResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetPlantCaptchaResponse::IsInitialized() const {
  return true;
}

void GetPlantCaptchaResponse::InternalSwap(GetPlantCaptchaResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  items_.InternalSwap(&other->items_);
  swap(plant_captcha_, other->plant_captcha_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetPlantCaptchaResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[11]);
}

// ===================================================================

class StartPlantCaptchaUploadRequest::_Internal {
 public:
};

StartPlantCaptchaUploadRequest::StartPlantCaptchaUploadRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest)
}
StartPlantCaptchaUploadRequest::StartPlantCaptchaUploadRequest(const StartPlantCaptchaUploadRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest)
}

inline void StartPlantCaptchaUploadRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

StartPlantCaptchaUploadRequest::~StartPlantCaptchaUploadRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StartPlantCaptchaUploadRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void StartPlantCaptchaUploadRequest::ArenaDtor(void* object) {
  StartPlantCaptchaUploadRequest* _this = reinterpret_cast< StartPlantCaptchaUploadRequest* >(object);
  (void)_this;
}
void StartPlantCaptchaUploadRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StartPlantCaptchaUploadRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StartPlantCaptchaUploadRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StartPlantCaptchaUploadRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* StartPlantCaptchaUploadRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest)
  return target;
}

size_t StartPlantCaptchaUploadRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StartPlantCaptchaUploadRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StartPlantCaptchaUploadRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StartPlantCaptchaUploadRequest::GetClassData() const { return &_class_data_; }

void StartPlantCaptchaUploadRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StartPlantCaptchaUploadRequest *>(to)->MergeFrom(
      static_cast<const StartPlantCaptchaUploadRequest &>(from));
}


void StartPlantCaptchaUploadRequest::MergeFrom(const StartPlantCaptchaUploadRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StartPlantCaptchaUploadRequest::CopyFrom(const StartPlantCaptchaUploadRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StartPlantCaptchaUploadRequest::IsInitialized() const {
  return true;
}

void StartPlantCaptchaUploadRequest::InternalSwap(StartPlantCaptchaUploadRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata StartPlantCaptchaUploadRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[12]);
}

// ===================================================================

class GetNextPlantCaptchaUploadStateRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextPlantCaptchaUploadStateRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextPlantCaptchaUploadStateRequest::_Internal::ts(const GetNextPlantCaptchaUploadStateRequest* msg) {
  return *msg->ts_;
}
void GetNextPlantCaptchaUploadStateRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextPlantCaptchaUploadStateRequest::GetNextPlantCaptchaUploadStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest)
}
GetNextPlantCaptchaUploadStateRequest::GetNextPlantCaptchaUploadStateRequest(const GetNextPlantCaptchaUploadStateRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest)
}

inline void GetNextPlantCaptchaUploadStateRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextPlantCaptchaUploadStateRequest::~GetNextPlantCaptchaUploadStateRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextPlantCaptchaUploadStateRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextPlantCaptchaUploadStateRequest::ArenaDtor(void* object) {
  GetNextPlantCaptchaUploadStateRequest* _this = reinterpret_cast< GetNextPlantCaptchaUploadStateRequest* >(object);
  (void)_this;
}
void GetNextPlantCaptchaUploadStateRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextPlantCaptchaUploadStateRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextPlantCaptchaUploadStateRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextPlantCaptchaUploadStateRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextPlantCaptchaUploadStateRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest)
  return target;
}

size_t GetNextPlantCaptchaUploadStateRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextPlantCaptchaUploadStateRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextPlantCaptchaUploadStateRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextPlantCaptchaUploadStateRequest::GetClassData() const { return &_class_data_; }

void GetNextPlantCaptchaUploadStateRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextPlantCaptchaUploadStateRequest *>(to)->MergeFrom(
      static_cast<const GetNextPlantCaptchaUploadStateRequest &>(from));
}


void GetNextPlantCaptchaUploadStateRequest::MergeFrom(const GetNextPlantCaptchaUploadStateRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextPlantCaptchaUploadStateRequest::CopyFrom(const GetNextPlantCaptchaUploadStateRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextPlantCaptchaUploadStateRequest::IsInitialized() const {
  return true;
}

void GetNextPlantCaptchaUploadStateRequest::InternalSwap(GetNextPlantCaptchaUploadStateRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextPlantCaptchaUploadStateRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[13]);
}

// ===================================================================

class GetNextPlantCaptchaUploadStateResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextPlantCaptchaUploadStateResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextPlantCaptchaUploadStateResponse::_Internal::ts(const GetNextPlantCaptchaUploadStateResponse* msg) {
  return *msg->ts_;
}
void GetNextPlantCaptchaUploadStateResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextPlantCaptchaUploadStateResponse::GetNextPlantCaptchaUploadStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse)
}
GetNextPlantCaptchaUploadStateResponse::GetNextPlantCaptchaUploadStateResponse(const GetNextPlantCaptchaUploadStateResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&upload_state_, &from.upload_state_,
    static_cast<size_t>(reinterpret_cast<char*>(&percent_) -
    reinterpret_cast<char*>(&upload_state_)) + sizeof(percent_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse)
}

inline void GetNextPlantCaptchaUploadStateResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&percent_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(percent_));
}

GetNextPlantCaptchaUploadStateResponse::~GetNextPlantCaptchaUploadStateResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextPlantCaptchaUploadStateResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextPlantCaptchaUploadStateResponse::ArenaDtor(void* object) {
  GetNextPlantCaptchaUploadStateResponse* _this = reinterpret_cast< GetNextPlantCaptchaUploadStateResponse* >(object);
  (void)_this;
}
void GetNextPlantCaptchaUploadStateResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextPlantCaptchaUploadStateResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextPlantCaptchaUploadStateResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&upload_state_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&percent_) -
      reinterpret_cast<char*>(&upload_state_)) + sizeof(percent_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextPlantCaptchaUploadStateResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.plant_captcha.PlantCaptchaUploadState upload_state = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_upload_state(static_cast<::carbon::frontend::plant_captcha::PlantCaptchaUploadState>(val));
        } else
          goto handle_unusual;
        continue;
      // int32 percent = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          percent_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextPlantCaptchaUploadStateResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // .carbon.frontend.plant_captcha.PlantCaptchaUploadState upload_state = 2;
  if (this->_internal_upload_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_upload_state(), target);
  }

  // int32 percent = 3;
  if (this->_internal_percent() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_percent(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse)
  return target;
}

size_t GetNextPlantCaptchaUploadStateResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.plant_captcha.PlantCaptchaUploadState upload_state = 2;
  if (this->_internal_upload_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_upload_state());
  }

  // int32 percent = 3;
  if (this->_internal_percent() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_percent());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextPlantCaptchaUploadStateResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextPlantCaptchaUploadStateResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextPlantCaptchaUploadStateResponse::GetClassData() const { return &_class_data_; }

void GetNextPlantCaptchaUploadStateResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextPlantCaptchaUploadStateResponse *>(to)->MergeFrom(
      static_cast<const GetNextPlantCaptchaUploadStateResponse &>(from));
}


void GetNextPlantCaptchaUploadStateResponse::MergeFrom(const GetNextPlantCaptchaUploadStateResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_upload_state() != 0) {
    _internal_set_upload_state(from._internal_upload_state());
  }
  if (from._internal_percent() != 0) {
    _internal_set_percent(from._internal_percent());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextPlantCaptchaUploadStateResponse::CopyFrom(const GetNextPlantCaptchaUploadStateResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextPlantCaptchaUploadStateResponse::IsInitialized() const {
  return true;
}

void GetNextPlantCaptchaUploadStateResponse::InternalSwap(GetNextPlantCaptchaUploadStateResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextPlantCaptchaUploadStateResponse, percent_)
      + sizeof(GetNextPlantCaptchaUploadStateResponse::percent_)
      - PROTOBUF_FIELD_OFFSET(GetNextPlantCaptchaUploadStateResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextPlantCaptchaUploadStateResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[14]);
}

// ===================================================================

class PlantCaptchaItemResult::_Internal {
 public:
};

PlantCaptchaItemResult::PlantCaptchaItemResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.PlantCaptchaItemResult)
}
PlantCaptchaItemResult::PlantCaptchaItemResult(const PlantCaptchaItemResult& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  user_prediction_ = from.user_prediction_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.PlantCaptchaItemResult)
}

inline void PlantCaptchaItemResult::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
user_prediction_ = 0;
}

PlantCaptchaItemResult::~PlantCaptchaItemResult() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.PlantCaptchaItemResult)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PlantCaptchaItemResult::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void PlantCaptchaItemResult::ArenaDtor(void* object) {
  PlantCaptchaItemResult* _this = reinterpret_cast< PlantCaptchaItemResult* >(object);
  (void)_this;
}
void PlantCaptchaItemResult::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PlantCaptchaItemResult::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PlantCaptchaItemResult::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.PlantCaptchaItemResult)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  user_prediction_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PlantCaptchaItemResult::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptchaItemResult.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .weed_tracking.PlantCaptchaUserPrediction user_prediction = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_user_prediction(static_cast<::weed_tracking::PlantCaptchaUserPrediction>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PlantCaptchaItemResult::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.PlantCaptchaItemResult)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptchaItemResult.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // .weed_tracking.PlantCaptchaUserPrediction user_prediction = 2;
  if (this->_internal_user_prediction() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_user_prediction(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.PlantCaptchaItemResult)
  return target;
}

size_t PlantCaptchaItemResult::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.PlantCaptchaItemResult)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // .weed_tracking.PlantCaptchaUserPrediction user_prediction = 2;
  if (this->_internal_user_prediction() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_user_prediction());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PlantCaptchaItemResult::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PlantCaptchaItemResult::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PlantCaptchaItemResult::GetClassData() const { return &_class_data_; }

void PlantCaptchaItemResult::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PlantCaptchaItemResult *>(to)->MergeFrom(
      static_cast<const PlantCaptchaItemResult &>(from));
}


void PlantCaptchaItemResult::MergeFrom(const PlantCaptchaItemResult& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.PlantCaptchaItemResult)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_user_prediction() != 0) {
    _internal_set_user_prediction(from._internal_user_prediction());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PlantCaptchaItemResult::CopyFrom(const PlantCaptchaItemResult& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.PlantCaptchaItemResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PlantCaptchaItemResult::IsInitialized() const {
  return true;
}

void PlantCaptchaItemResult::InternalSwap(PlantCaptchaItemResult* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  swap(user_prediction_, other->user_prediction_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PlantCaptchaItemResult::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[15]);
}

// ===================================================================

class SubmitPlantCaptchaResultsRequest::_Internal {
 public:
};

SubmitPlantCaptchaResultsRequest::SubmitPlantCaptchaResultsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  results_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest)
}
SubmitPlantCaptchaResultsRequest::SubmitPlantCaptchaResultsRequest(const SubmitPlantCaptchaResultsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      results_(from.results_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest)
}

inline void SubmitPlantCaptchaResultsRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SubmitPlantCaptchaResultsRequest::~SubmitPlantCaptchaResultsRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SubmitPlantCaptchaResultsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SubmitPlantCaptchaResultsRequest::ArenaDtor(void* object) {
  SubmitPlantCaptchaResultsRequest* _this = reinterpret_cast< SubmitPlantCaptchaResultsRequest* >(object);
  (void)_this;
}
void SubmitPlantCaptchaResultsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SubmitPlantCaptchaResultsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SubmitPlantCaptchaResultsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  results_.Clear();
  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SubmitPlantCaptchaResultsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.plant_captcha.PlantCaptchaItemResult results = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_results(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SubmitPlantCaptchaResultsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // repeated .carbon.frontend.plant_captcha.PlantCaptchaItemResult results = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_results_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_results(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest)
  return target;
}

size_t SubmitPlantCaptchaResultsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.plant_captcha.PlantCaptchaItemResult results = 2;
  total_size += 1UL * this->_internal_results_size();
  for (const auto& msg : this->results_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SubmitPlantCaptchaResultsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SubmitPlantCaptchaResultsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SubmitPlantCaptchaResultsRequest::GetClassData() const { return &_class_data_; }

void SubmitPlantCaptchaResultsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SubmitPlantCaptchaResultsRequest *>(to)->MergeFrom(
      static_cast<const SubmitPlantCaptchaResultsRequest &>(from));
}


void SubmitPlantCaptchaResultsRequest::MergeFrom(const SubmitPlantCaptchaResultsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  results_.MergeFrom(from.results_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SubmitPlantCaptchaResultsRequest::CopyFrom(const SubmitPlantCaptchaResultsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SubmitPlantCaptchaResultsRequest::IsInitialized() const {
  return true;
}

void SubmitPlantCaptchaResultsRequest::InternalSwap(SubmitPlantCaptchaResultsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  results_.InternalSwap(&other->results_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SubmitPlantCaptchaResultsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[16]);
}

// ===================================================================

class GetPlantCaptchaItemResultsRequest::_Internal {
 public:
};

GetPlantCaptchaItemResultsRequest::GetPlantCaptchaItemResultsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  id_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest)
}
GetPlantCaptchaItemResultsRequest::GetPlantCaptchaItemResultsRequest(const GetPlantCaptchaItemResultsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      id_(from.id_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest)
}

inline void GetPlantCaptchaItemResultsRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetPlantCaptchaItemResultsRequest::~GetPlantCaptchaItemResultsRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetPlantCaptchaItemResultsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetPlantCaptchaItemResultsRequest::ArenaDtor(void* object) {
  GetPlantCaptchaItemResultsRequest* _this = reinterpret_cast< GetPlantCaptchaItemResultsRequest* >(object);
  (void)_this;
}
void GetPlantCaptchaItemResultsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetPlantCaptchaItemResultsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetPlantCaptchaItemResultsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.Clear();
  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetPlantCaptchaItemResultsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_id();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetPlantCaptchaItemResultsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // repeated string id = 2;
  for (int i = 0, n = this->_internal_id_size(); i < n; i++) {
    const auto& s = this->_internal_id(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id");
    target = stream->WriteString(2, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest)
  return target;
}

size_t GetPlantCaptchaItemResultsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string id = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(id_.size());
  for (int i = 0, n = id_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      id_.Get(i));
  }

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPlantCaptchaItemResultsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetPlantCaptchaItemResultsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPlantCaptchaItemResultsRequest::GetClassData() const { return &_class_data_; }

void GetPlantCaptchaItemResultsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetPlantCaptchaItemResultsRequest *>(to)->MergeFrom(
      static_cast<const GetPlantCaptchaItemResultsRequest &>(from));
}


void GetPlantCaptchaItemResultsRequest::MergeFrom(const GetPlantCaptchaItemResultsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  id_.MergeFrom(from.id_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetPlantCaptchaItemResultsRequest::CopyFrom(const GetPlantCaptchaItemResultsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetPlantCaptchaItemResultsRequest::IsInitialized() const {
  return true;
}

void GetPlantCaptchaItemResultsRequest::InternalSwap(GetPlantCaptchaItemResultsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  id_.InternalSwap(&other->id_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetPlantCaptchaItemResultsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[17]);
}

// ===================================================================

class GetPlantCaptchaItemResultsResponse::_Internal {
 public:
};

GetPlantCaptchaItemResultsResponse::GetPlantCaptchaItemResultsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  results_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse)
}
GetPlantCaptchaItemResultsResponse::GetPlantCaptchaItemResultsResponse(const GetPlantCaptchaItemResultsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      results_(from.results_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse)
}

inline void GetPlantCaptchaItemResultsResponse::SharedCtor() {
}

GetPlantCaptchaItemResultsResponse::~GetPlantCaptchaItemResultsResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetPlantCaptchaItemResultsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetPlantCaptchaItemResultsResponse::ArenaDtor(void* object) {
  GetPlantCaptchaItemResultsResponse* _this = reinterpret_cast< GetPlantCaptchaItemResultsResponse* >(object);
  (void)_this;
}
void GetPlantCaptchaItemResultsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetPlantCaptchaItemResultsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetPlantCaptchaItemResultsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  results_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetPlantCaptchaItemResultsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.plant_captcha.PlantCaptchaItemResult results = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_results(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetPlantCaptchaItemResultsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.plant_captcha.PlantCaptchaItemResult results = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_results_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_results(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse)
  return target;
}

size_t GetPlantCaptchaItemResultsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.plant_captcha.PlantCaptchaItemResult results = 1;
  total_size += 1UL * this->_internal_results_size();
  for (const auto& msg : this->results_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPlantCaptchaItemResultsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetPlantCaptchaItemResultsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPlantCaptchaItemResultsResponse::GetClassData() const { return &_class_data_; }

void GetPlantCaptchaItemResultsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetPlantCaptchaItemResultsResponse *>(to)->MergeFrom(
      static_cast<const GetPlantCaptchaItemResultsResponse &>(from));
}


void GetPlantCaptchaItemResultsResponse::MergeFrom(const GetPlantCaptchaItemResultsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  results_.MergeFrom(from.results_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetPlantCaptchaItemResultsResponse::CopyFrom(const GetPlantCaptchaItemResultsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetPlantCaptchaItemResultsResponse::IsInitialized() const {
  return true;
}

void GetPlantCaptchaItemResultsResponse::InternalSwap(GetPlantCaptchaItemResultsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  results_.InternalSwap(&other->results_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetPlantCaptchaItemResultsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[18]);
}

// ===================================================================

class CalculatePlantCaptchaRequest::_Internal {
 public:
};

CalculatePlantCaptchaRequest::CalculatePlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest)
}
CalculatePlantCaptchaRequest::CalculatePlantCaptchaRequest(const CalculatePlantCaptchaRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest)
}

inline void CalculatePlantCaptchaRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

CalculatePlantCaptchaRequest::~CalculatePlantCaptchaRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CalculatePlantCaptchaRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CalculatePlantCaptchaRequest::ArenaDtor(void* object) {
  CalculatePlantCaptchaRequest* _this = reinterpret_cast< CalculatePlantCaptchaRequest* >(object);
  (void)_this;
}
void CalculatePlantCaptchaRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CalculatePlantCaptchaRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CalculatePlantCaptchaRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CalculatePlantCaptchaRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CalculatePlantCaptchaRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest)
  return target;
}

size_t CalculatePlantCaptchaRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CalculatePlantCaptchaRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CalculatePlantCaptchaRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CalculatePlantCaptchaRequest::GetClassData() const { return &_class_data_; }

void CalculatePlantCaptchaRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CalculatePlantCaptchaRequest *>(to)->MergeFrom(
      static_cast<const CalculatePlantCaptchaRequest &>(from));
}


void CalculatePlantCaptchaRequest::MergeFrom(const CalculatePlantCaptchaRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CalculatePlantCaptchaRequest::CopyFrom(const CalculatePlantCaptchaRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CalculatePlantCaptchaRequest::IsInitialized() const {
  return true;
}

void CalculatePlantCaptchaRequest::InternalSwap(CalculatePlantCaptchaRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata CalculatePlantCaptchaRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[19]);
}

// ===================================================================

class CalculatePlantCaptchaResponse::_Internal {
 public:
  static const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator_config(const CalculatePlantCaptchaResponse* msg);
};

const ::carbon::aimbot::almanac::ModelinatorConfig&
CalculatePlantCaptchaResponse::_Internal::modelinator_config(const CalculatePlantCaptchaResponse* msg) {
  return *msg->modelinator_config_;
}
void CalculatePlantCaptchaResponse::clear_modelinator_config() {
  if (GetArenaForAllocation() == nullptr && modelinator_config_ != nullptr) {
    delete modelinator_config_;
  }
  modelinator_config_ = nullptr;
}
CalculatePlantCaptchaResponse::CalculatePlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse)
}
CalculatePlantCaptchaResponse::CalculatePlantCaptchaResponse(const CalculatePlantCaptchaResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_modelinator_config()) {
    modelinator_config_ = new ::carbon::aimbot::almanac::ModelinatorConfig(*from.modelinator_config_);
  } else {
    modelinator_config_ = nullptr;
  }
  ::memcpy(&succeeded_, &from.succeeded_,
    static_cast<size_t>(reinterpret_cast<char*>(&failure_reason_) -
    reinterpret_cast<char*>(&succeeded_)) + sizeof(failure_reason_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse)
}

inline void CalculatePlantCaptchaResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&modelinator_config_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&failure_reason_) -
    reinterpret_cast<char*>(&modelinator_config_)) + sizeof(failure_reason_));
}

CalculatePlantCaptchaResponse::~CalculatePlantCaptchaResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CalculatePlantCaptchaResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete modelinator_config_;
}

void CalculatePlantCaptchaResponse::ArenaDtor(void* object) {
  CalculatePlantCaptchaResponse* _this = reinterpret_cast< CalculatePlantCaptchaResponse* >(object);
  (void)_this;
}
void CalculatePlantCaptchaResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CalculatePlantCaptchaResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CalculatePlantCaptchaResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && modelinator_config_ != nullptr) {
    delete modelinator_config_;
  }
  modelinator_config_ = nullptr;
  ::memset(&succeeded_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&failure_reason_) -
      reinterpret_cast<char*>(&succeeded_)) + sizeof(failure_reason_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CalculatePlantCaptchaResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_modelinator_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool succeeded = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          succeeded_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.plant_captcha.PlantLabelAlgorithmFailureReason failure_reason = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_failure_reason(static_cast<::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CalculatePlantCaptchaResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
  if (this->_internal_has_modelinator_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::modelinator_config(this), target, stream);
  }

  // bool succeeded = 2;
  if (this->_internal_succeeded() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_succeeded(), target);
  }

  // .carbon.frontend.plant_captcha.PlantLabelAlgorithmFailureReason failure_reason = 3;
  if (this->_internal_failure_reason() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_failure_reason(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse)
  return target;
}

size_t CalculatePlantCaptchaResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
  if (this->_internal_has_modelinator_config()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *modelinator_config_);
  }

  // bool succeeded = 2;
  if (this->_internal_succeeded() != 0) {
    total_size += 1 + 1;
  }

  // .carbon.frontend.plant_captcha.PlantLabelAlgorithmFailureReason failure_reason = 3;
  if (this->_internal_failure_reason() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_failure_reason());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CalculatePlantCaptchaResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CalculatePlantCaptchaResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CalculatePlantCaptchaResponse::GetClassData() const { return &_class_data_; }

void CalculatePlantCaptchaResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CalculatePlantCaptchaResponse *>(to)->MergeFrom(
      static_cast<const CalculatePlantCaptchaResponse &>(from));
}


void CalculatePlantCaptchaResponse::MergeFrom(const CalculatePlantCaptchaResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_modelinator_config()) {
    _internal_mutable_modelinator_config()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_modelinator_config());
  }
  if (from._internal_succeeded() != 0) {
    _internal_set_succeeded(from._internal_succeeded());
  }
  if (from._internal_failure_reason() != 0) {
    _internal_set_failure_reason(from._internal_failure_reason());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CalculatePlantCaptchaResponse::CopyFrom(const CalculatePlantCaptchaResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CalculatePlantCaptchaResponse::IsInitialized() const {
  return true;
}

void CalculatePlantCaptchaResponse::InternalSwap(CalculatePlantCaptchaResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CalculatePlantCaptchaResponse, failure_reason_)
      + sizeof(CalculatePlantCaptchaResponse::failure_reason_)
      - PROTOBUF_FIELD_OFFSET(CalculatePlantCaptchaResponse, modelinator_config_)>(
          reinterpret_cast<char*>(&modelinator_config_),
          reinterpret_cast<char*>(&other->modelinator_config_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CalculatePlantCaptchaResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[20]);
}

// ===================================================================

class PlantCaptchaResult::_Internal {
 public:
  static const ::weed_tracking::PlantCaptchaItemMetadata& metadata(const PlantCaptchaResult* msg);
};

const ::weed_tracking::PlantCaptchaItemMetadata&
PlantCaptchaResult::_Internal::metadata(const PlantCaptchaResult* msg) {
  return *msg->metadata_;
}
void PlantCaptchaResult::clear_metadata() {
  if (GetArenaForAllocation() == nullptr && metadata_ != nullptr) {
    delete metadata_;
  }
  metadata_ = nullptr;
}
PlantCaptchaResult::PlantCaptchaResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.PlantCaptchaResult)
}
PlantCaptchaResult::PlantCaptchaResult(const PlantCaptchaResult& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_metadata()) {
    metadata_ = new ::weed_tracking::PlantCaptchaItemMetadata(*from.metadata_);
  } else {
    metadata_ = nullptr;
  }
  label_ = from.label_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.PlantCaptchaResult)
}

inline void PlantCaptchaResult::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&metadata_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&label_) -
    reinterpret_cast<char*>(&metadata_)) + sizeof(label_));
}

PlantCaptchaResult::~PlantCaptchaResult() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.PlantCaptchaResult)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PlantCaptchaResult::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete metadata_;
}

void PlantCaptchaResult::ArenaDtor(void* object) {
  PlantCaptchaResult* _this = reinterpret_cast< PlantCaptchaResult* >(object);
  (void)_this;
}
void PlantCaptchaResult::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PlantCaptchaResult::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PlantCaptchaResult::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.PlantCaptchaResult)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && metadata_ != nullptr) {
    delete metadata_;
  }
  metadata_ = nullptr;
  label_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PlantCaptchaResult::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .weed_tracking.PlantCaptchaUserPrediction label = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_label(static_cast<::weed_tracking::PlantCaptchaUserPrediction>(val));
        } else
          goto handle_unusual;
        continue;
      // .weed_tracking.PlantCaptchaItemMetadata metadata = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_metadata(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PlantCaptchaResult::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.PlantCaptchaResult)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .weed_tracking.PlantCaptchaUserPrediction label = 1;
  if (this->_internal_label() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_label(), target);
  }

  // .weed_tracking.PlantCaptchaItemMetadata metadata = 2;
  if (this->_internal_has_metadata()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::metadata(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.PlantCaptchaResult)
  return target;
}

size_t PlantCaptchaResult::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.PlantCaptchaResult)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .weed_tracking.PlantCaptchaItemMetadata metadata = 2;
  if (this->_internal_has_metadata()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *metadata_);
  }

  // .weed_tracking.PlantCaptchaUserPrediction label = 1;
  if (this->_internal_label() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_label());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PlantCaptchaResult::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PlantCaptchaResult::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PlantCaptchaResult::GetClassData() const { return &_class_data_; }

void PlantCaptchaResult::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PlantCaptchaResult *>(to)->MergeFrom(
      static_cast<const PlantCaptchaResult &>(from));
}


void PlantCaptchaResult::MergeFrom(const PlantCaptchaResult& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.PlantCaptchaResult)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_metadata()) {
    _internal_mutable_metadata()->::weed_tracking::PlantCaptchaItemMetadata::MergeFrom(from._internal_metadata());
  }
  if (from._internal_label() != 0) {
    _internal_set_label(from._internal_label());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PlantCaptchaResult::CopyFrom(const PlantCaptchaResult& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.PlantCaptchaResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PlantCaptchaResult::IsInitialized() const {
  return true;
}

void PlantCaptchaResult::InternalSwap(PlantCaptchaResult* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PlantCaptchaResult, label_)
      + sizeof(PlantCaptchaResult::label_)
      - PROTOBUF_FIELD_OFFSET(PlantCaptchaResult, metadata_)>(
          reinterpret_cast<char*>(&metadata_),
          reinterpret_cast<char*>(&other->metadata_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PlantCaptchaResult::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[21]);
}

// ===================================================================

class PlantCaptchaResults::_Internal {
 public:
  static const ::carbon::aimbot::almanac::ModelinatorConfig& current_parameters(const PlantCaptchaResults* msg);
  static const ::carbon::aimbot::almanac::AlmanacConfig& almanac(const PlantCaptchaResults* msg);
};

const ::carbon::aimbot::almanac::ModelinatorConfig&
PlantCaptchaResults::_Internal::current_parameters(const PlantCaptchaResults* msg) {
  return *msg->current_parameters_;
}
const ::carbon::aimbot::almanac::AlmanacConfig&
PlantCaptchaResults::_Internal::almanac(const PlantCaptchaResults* msg) {
  return *msg->almanac_;
}
void PlantCaptchaResults::clear_current_parameters() {
  if (GetArenaForAllocation() == nullptr && current_parameters_ != nullptr) {
    delete current_parameters_;
  }
  current_parameters_ = nullptr;
}
void PlantCaptchaResults::clear_almanac() {
  if (GetArenaForAllocation() == nullptr && almanac_ != nullptr) {
    delete almanac_;
  }
  almanac_ = nullptr;
}
PlantCaptchaResults::PlantCaptchaResults(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  captcha_results_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.PlantCaptchaResults)
}
PlantCaptchaResults::PlantCaptchaResults(const PlantCaptchaResults& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      captcha_results_(from.captcha_results_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  algorithm_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    algorithm_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_algorithm().empty()) {
    algorithm_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_algorithm(), 
      GetArenaForAllocation());
  }
  tiebreaker_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    tiebreaker_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_tiebreaker().empty()) {
    tiebreaker_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_tiebreaker(), 
      GetArenaForAllocation());
  }
  mindoo_tiebreaker_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    mindoo_tiebreaker_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_mindoo_tiebreaker().empty()) {
    mindoo_tiebreaker_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_mindoo_tiebreaker(), 
      GetArenaForAllocation());
  }
  tiebreaker_strategy_threshold_weed_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    tiebreaker_strategy_threshold_weed_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_tiebreaker_strategy_threshold_weed().empty()) {
    tiebreaker_strategy_threshold_weed_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_tiebreaker_strategy_threshold_weed(), 
      GetArenaForAllocation());
  }
  tiebreaker_strategy_threshold_crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    tiebreaker_strategy_threshold_crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_tiebreaker_strategy_threshold_crop().empty()) {
    tiebreaker_strategy_threshold_crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_tiebreaker_strategy_threshold_crop(), 
      GetArenaForAllocation());
  }
  tiebreaker_strategy_mindoo_weed_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    tiebreaker_strategy_mindoo_weed_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_tiebreaker_strategy_mindoo_weed().empty()) {
    tiebreaker_strategy_mindoo_weed_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_tiebreaker_strategy_mindoo_weed(), 
      GetArenaForAllocation());
  }
  tiebreaker_strategy_mindoo_crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    tiebreaker_strategy_mindoo_crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_tiebreaker_strategy_mindoo_crop().empty()) {
    tiebreaker_strategy_mindoo_crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_tiebreaker_strategy_mindoo_crop(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_current_parameters()) {
    current_parameters_ = new ::carbon::aimbot::almanac::ModelinatorConfig(*from.current_parameters_);
  } else {
    current_parameters_ = nullptr;
  }
  if (from._internal_has_almanac()) {
    almanac_ = new ::carbon::aimbot::almanac::AlmanacConfig(*from.almanac_);
  } else {
    almanac_ = nullptr;
  }
  ::memcpy(&goal_crops_targeted_, &from.goal_crops_targeted_,
    static_cast<size_t>(reinterpret_cast<char*>(&use_volunteers_as_weeds_) -
    reinterpret_cast<char*>(&goal_crops_targeted_)) + sizeof(use_volunteers_as_weeds_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.PlantCaptchaResults)
}

inline void PlantCaptchaResults::SharedCtor() {
algorithm_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  algorithm_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
tiebreaker_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  tiebreaker_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
mindoo_tiebreaker_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  mindoo_tiebreaker_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
tiebreaker_strategy_threshold_weed_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  tiebreaker_strategy_threshold_weed_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
tiebreaker_strategy_threshold_crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  tiebreaker_strategy_threshold_crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
tiebreaker_strategy_mindoo_weed_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  tiebreaker_strategy_mindoo_weed_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
tiebreaker_strategy_mindoo_crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  tiebreaker_strategy_mindoo_crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&current_parameters_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&use_volunteers_as_weeds_) -
    reinterpret_cast<char*>(&current_parameters_)) + sizeof(use_volunteers_as_weeds_));
}

PlantCaptchaResults::~PlantCaptchaResults() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.PlantCaptchaResults)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PlantCaptchaResults::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  algorithm_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  tiebreaker_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  mindoo_tiebreaker_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  tiebreaker_strategy_threshold_weed_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  tiebreaker_strategy_threshold_crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  tiebreaker_strategy_mindoo_weed_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  tiebreaker_strategy_mindoo_crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete current_parameters_;
  if (this != internal_default_instance()) delete almanac_;
}

void PlantCaptchaResults::ArenaDtor(void* object) {
  PlantCaptchaResults* _this = reinterpret_cast< PlantCaptchaResults* >(object);
  (void)_this;
}
void PlantCaptchaResults::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PlantCaptchaResults::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PlantCaptchaResults::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.PlantCaptchaResults)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  captcha_results_.Clear();
  algorithm_.ClearToEmpty();
  tiebreaker_.ClearToEmpty();
  mindoo_tiebreaker_.ClearToEmpty();
  tiebreaker_strategy_threshold_weed_.ClearToEmpty();
  tiebreaker_strategy_threshold_crop_.ClearToEmpty();
  tiebreaker_strategy_mindoo_weed_.ClearToEmpty();
  tiebreaker_strategy_mindoo_crop_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && current_parameters_ != nullptr) {
    delete current_parameters_;
  }
  current_parameters_ = nullptr;
  if (GetArenaForAllocation() == nullptr && almanac_ != nullptr) {
    delete almanac_;
  }
  almanac_ = nullptr;
  ::memset(&goal_crops_targeted_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&use_volunteers_as_weeds_) -
      reinterpret_cast<char*>(&goal_crops_targeted_)) + sizeof(use_volunteers_as_weeds_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PlantCaptchaResults::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.ModelinatorConfig current_parameters = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_current_parameters(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.plant_captcha.PlantCaptchaResult captcha_results = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_captcha_results(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string algorithm = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_algorithm();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptchaResults.algorithm"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float goal_crops_targeted = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          goal_crops_targeted_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float goal_weeds_targeted = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          goal_weeds_targeted_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float goal_unknown_targeted = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          goal_unknown_targeted_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.AlmanacConfig almanac = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_almanac(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float max_recommended_mindoo = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          max_recommended_mindoo_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // int32 min_items_for_recommendation = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          min_items_for_recommendation_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool use_weed_categories_for_weed_labels = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          use_weed_categories_for_weed_labels_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float min_recommended_mindoo = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 93)) {
          min_recommended_mindoo_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float min_recommended_weed_threshold = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 101)) {
          min_recommended_weed_threshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float max_recommended_weed_threshold = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 109)) {
          max_recommended_weed_threshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float min_recommended_crop_threshold = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 117)) {
          min_recommended_crop_threshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float max_recommended_crop_threshold = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 125)) {
          max_recommended_crop_threshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float min_doo_for_recommendation = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 133)) {
          min_doo_for_recommendation_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // bool use_other_as_tiebreaker = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 136)) {
          use_other_as_tiebreaker_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool limit_by_crops_missed = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 144)) {
          limit_by_crops_missed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 number_of_crop_configurations = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 152)) {
          number_of_crop_configurations_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string tiebreaker = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 162)) {
          auto str = _internal_mutable_tiebreaker();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool pad_crop_configurations = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 168)) {
          pad_crop_configurations_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string mindoo_tiebreaker = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 178)) {
          auto str = _internal_mutable_mindoo_tiebreaker();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptchaResults.mindoo_tiebreaker"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool use_beneficials_as_crops = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 184)) {
          use_beneficials_as_crops_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool use_volunteers_as_weeds = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 192)) {
          use_volunteers_as_weeds_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string tiebreaker_strategy_threshold_weed = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 202)) {
          auto str = _internal_mutable_tiebreaker_strategy_threshold_weed();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_weed"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string tiebreaker_strategy_threshold_crop = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 210)) {
          auto str = _internal_mutable_tiebreaker_strategy_threshold_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string tiebreaker_strategy_mindoo_weed = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 218)) {
          auto str = _internal_mutable_tiebreaker_strategy_mindoo_weed();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_weed"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string tiebreaker_strategy_mindoo_crop = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 226)) {
          auto str = _internal_mutable_tiebreaker_strategy_mindoo_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PlantCaptchaResults::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.PlantCaptchaResults)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.ModelinatorConfig current_parameters = 1;
  if (this->_internal_has_current_parameters()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::current_parameters(this), target, stream);
  }

  // repeated .carbon.frontend.plant_captcha.PlantCaptchaResult captcha_results = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_captcha_results_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_captcha_results(i), target, stream);
  }

  // string algorithm = 3;
  if (!this->_internal_algorithm().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_algorithm().data(), static_cast<int>(this->_internal_algorithm().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptchaResults.algorithm");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_algorithm(), target);
  }

  // float goal_crops_targeted = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_goal_crops_targeted = this->_internal_goal_crops_targeted();
  uint32_t raw_goal_crops_targeted;
  memcpy(&raw_goal_crops_targeted, &tmp_goal_crops_targeted, sizeof(tmp_goal_crops_targeted));
  if (raw_goal_crops_targeted != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_goal_crops_targeted(), target);
  }

  // float goal_weeds_targeted = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_goal_weeds_targeted = this->_internal_goal_weeds_targeted();
  uint32_t raw_goal_weeds_targeted;
  memcpy(&raw_goal_weeds_targeted, &tmp_goal_weeds_targeted, sizeof(tmp_goal_weeds_targeted));
  if (raw_goal_weeds_targeted != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_goal_weeds_targeted(), target);
  }

  // float goal_unknown_targeted = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_goal_unknown_targeted = this->_internal_goal_unknown_targeted();
  uint32_t raw_goal_unknown_targeted;
  memcpy(&raw_goal_unknown_targeted, &tmp_goal_unknown_targeted, sizeof(tmp_goal_unknown_targeted));
  if (raw_goal_unknown_targeted != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_goal_unknown_targeted(), target);
  }

  // .carbon.aimbot.almanac.AlmanacConfig almanac = 7;
  if (this->_internal_has_almanac()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::almanac(this), target, stream);
  }

  // float max_recommended_mindoo = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_recommended_mindoo = this->_internal_max_recommended_mindoo();
  uint32_t raw_max_recommended_mindoo;
  memcpy(&raw_max_recommended_mindoo, &tmp_max_recommended_mindoo, sizeof(tmp_max_recommended_mindoo));
  if (raw_max_recommended_mindoo != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_max_recommended_mindoo(), target);
  }

  // int32 min_items_for_recommendation = 9;
  if (this->_internal_min_items_for_recommendation() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(9, this->_internal_min_items_for_recommendation(), target);
  }

  // bool use_weed_categories_for_weed_labels = 10;
  if (this->_internal_use_weed_categories_for_weed_labels() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(10, this->_internal_use_weed_categories_for_weed_labels(), target);
  }

  // float min_recommended_mindoo = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_recommended_mindoo = this->_internal_min_recommended_mindoo();
  uint32_t raw_min_recommended_mindoo;
  memcpy(&raw_min_recommended_mindoo, &tmp_min_recommended_mindoo, sizeof(tmp_min_recommended_mindoo));
  if (raw_min_recommended_mindoo != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(11, this->_internal_min_recommended_mindoo(), target);
  }

  // float min_recommended_weed_threshold = 12;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_recommended_weed_threshold = this->_internal_min_recommended_weed_threshold();
  uint32_t raw_min_recommended_weed_threshold;
  memcpy(&raw_min_recommended_weed_threshold, &tmp_min_recommended_weed_threshold, sizeof(tmp_min_recommended_weed_threshold));
  if (raw_min_recommended_weed_threshold != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(12, this->_internal_min_recommended_weed_threshold(), target);
  }

  // float max_recommended_weed_threshold = 13;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_recommended_weed_threshold = this->_internal_max_recommended_weed_threshold();
  uint32_t raw_max_recommended_weed_threshold;
  memcpy(&raw_max_recommended_weed_threshold, &tmp_max_recommended_weed_threshold, sizeof(tmp_max_recommended_weed_threshold));
  if (raw_max_recommended_weed_threshold != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(13, this->_internal_max_recommended_weed_threshold(), target);
  }

  // float min_recommended_crop_threshold = 14;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_recommended_crop_threshold = this->_internal_min_recommended_crop_threshold();
  uint32_t raw_min_recommended_crop_threshold;
  memcpy(&raw_min_recommended_crop_threshold, &tmp_min_recommended_crop_threshold, sizeof(tmp_min_recommended_crop_threshold));
  if (raw_min_recommended_crop_threshold != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(14, this->_internal_min_recommended_crop_threshold(), target);
  }

  // float max_recommended_crop_threshold = 15;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_recommended_crop_threshold = this->_internal_max_recommended_crop_threshold();
  uint32_t raw_max_recommended_crop_threshold;
  memcpy(&raw_max_recommended_crop_threshold, &tmp_max_recommended_crop_threshold, sizeof(tmp_max_recommended_crop_threshold));
  if (raw_max_recommended_crop_threshold != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(15, this->_internal_max_recommended_crop_threshold(), target);
  }

  // float min_doo_for_recommendation = 16;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_doo_for_recommendation = this->_internal_min_doo_for_recommendation();
  uint32_t raw_min_doo_for_recommendation;
  memcpy(&raw_min_doo_for_recommendation, &tmp_min_doo_for_recommendation, sizeof(tmp_min_doo_for_recommendation));
  if (raw_min_doo_for_recommendation != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(16, this->_internal_min_doo_for_recommendation(), target);
  }

  // bool use_other_as_tiebreaker = 17;
  if (this->_internal_use_other_as_tiebreaker() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(17, this->_internal_use_other_as_tiebreaker(), target);
  }

  // bool limit_by_crops_missed = 18;
  if (this->_internal_limit_by_crops_missed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(18, this->_internal_limit_by_crops_missed(), target);
  }

  // int32 number_of_crop_configurations = 19;
  if (this->_internal_number_of_crop_configurations() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(19, this->_internal_number_of_crop_configurations(), target);
  }

  // string tiebreaker = 20;
  if (!this->_internal_tiebreaker().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_tiebreaker().data(), static_cast<int>(this->_internal_tiebreaker().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker");
    target = stream->WriteStringMaybeAliased(
        20, this->_internal_tiebreaker(), target);
  }

  // bool pad_crop_configurations = 21;
  if (this->_internal_pad_crop_configurations() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(21, this->_internal_pad_crop_configurations(), target);
  }

  // string mindoo_tiebreaker = 22;
  if (!this->_internal_mindoo_tiebreaker().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_mindoo_tiebreaker().data(), static_cast<int>(this->_internal_mindoo_tiebreaker().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptchaResults.mindoo_tiebreaker");
    target = stream->WriteStringMaybeAliased(
        22, this->_internal_mindoo_tiebreaker(), target);
  }

  // bool use_beneficials_as_crops = 23;
  if (this->_internal_use_beneficials_as_crops() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(23, this->_internal_use_beneficials_as_crops(), target);
  }

  // bool use_volunteers_as_weeds = 24;
  if (this->_internal_use_volunteers_as_weeds() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(24, this->_internal_use_volunteers_as_weeds(), target);
  }

  // string tiebreaker_strategy_threshold_weed = 25;
  if (!this->_internal_tiebreaker_strategy_threshold_weed().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_tiebreaker_strategy_threshold_weed().data(), static_cast<int>(this->_internal_tiebreaker_strategy_threshold_weed().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_weed");
    target = stream->WriteStringMaybeAliased(
        25, this->_internal_tiebreaker_strategy_threshold_weed(), target);
  }

  // string tiebreaker_strategy_threshold_crop = 26;
  if (!this->_internal_tiebreaker_strategy_threshold_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_tiebreaker_strategy_threshold_crop().data(), static_cast<int>(this->_internal_tiebreaker_strategy_threshold_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_crop");
    target = stream->WriteStringMaybeAliased(
        26, this->_internal_tiebreaker_strategy_threshold_crop(), target);
  }

  // string tiebreaker_strategy_mindoo_weed = 27;
  if (!this->_internal_tiebreaker_strategy_mindoo_weed().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_tiebreaker_strategy_mindoo_weed().data(), static_cast<int>(this->_internal_tiebreaker_strategy_mindoo_weed().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_weed");
    target = stream->WriteStringMaybeAliased(
        27, this->_internal_tiebreaker_strategy_mindoo_weed(), target);
  }

  // string tiebreaker_strategy_mindoo_crop = 28;
  if (!this->_internal_tiebreaker_strategy_mindoo_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_tiebreaker_strategy_mindoo_crop().data(), static_cast<int>(this->_internal_tiebreaker_strategy_mindoo_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_crop");
    target = stream->WriteStringMaybeAliased(
        28, this->_internal_tiebreaker_strategy_mindoo_crop(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.PlantCaptchaResults)
  return target;
}

size_t PlantCaptchaResults::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.PlantCaptchaResults)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.plant_captcha.PlantCaptchaResult captcha_results = 2;
  total_size += 1UL * this->_internal_captcha_results_size();
  for (const auto& msg : this->captcha_results_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string algorithm = 3;
  if (!this->_internal_algorithm().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_algorithm());
  }

  // string tiebreaker = 20;
  if (!this->_internal_tiebreaker().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_tiebreaker());
  }

  // string mindoo_tiebreaker = 22;
  if (!this->_internal_mindoo_tiebreaker().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_mindoo_tiebreaker());
  }

  // string tiebreaker_strategy_threshold_weed = 25;
  if (!this->_internal_tiebreaker_strategy_threshold_weed().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_tiebreaker_strategy_threshold_weed());
  }

  // string tiebreaker_strategy_threshold_crop = 26;
  if (!this->_internal_tiebreaker_strategy_threshold_crop().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_tiebreaker_strategy_threshold_crop());
  }

  // string tiebreaker_strategy_mindoo_weed = 27;
  if (!this->_internal_tiebreaker_strategy_mindoo_weed().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_tiebreaker_strategy_mindoo_weed());
  }

  // string tiebreaker_strategy_mindoo_crop = 28;
  if (!this->_internal_tiebreaker_strategy_mindoo_crop().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_tiebreaker_strategy_mindoo_crop());
  }

  // .carbon.aimbot.almanac.ModelinatorConfig current_parameters = 1;
  if (this->_internal_has_current_parameters()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *current_parameters_);
  }

  // .carbon.aimbot.almanac.AlmanacConfig almanac = 7;
  if (this->_internal_has_almanac()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *almanac_);
  }

  // float goal_crops_targeted = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_goal_crops_targeted = this->_internal_goal_crops_targeted();
  uint32_t raw_goal_crops_targeted;
  memcpy(&raw_goal_crops_targeted, &tmp_goal_crops_targeted, sizeof(tmp_goal_crops_targeted));
  if (raw_goal_crops_targeted != 0) {
    total_size += 1 + 4;
  }

  // float goal_weeds_targeted = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_goal_weeds_targeted = this->_internal_goal_weeds_targeted();
  uint32_t raw_goal_weeds_targeted;
  memcpy(&raw_goal_weeds_targeted, &tmp_goal_weeds_targeted, sizeof(tmp_goal_weeds_targeted));
  if (raw_goal_weeds_targeted != 0) {
    total_size += 1 + 4;
  }

  // float goal_unknown_targeted = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_goal_unknown_targeted = this->_internal_goal_unknown_targeted();
  uint32_t raw_goal_unknown_targeted;
  memcpy(&raw_goal_unknown_targeted, &tmp_goal_unknown_targeted, sizeof(tmp_goal_unknown_targeted));
  if (raw_goal_unknown_targeted != 0) {
    total_size += 1 + 4;
  }

  // float max_recommended_mindoo = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_recommended_mindoo = this->_internal_max_recommended_mindoo();
  uint32_t raw_max_recommended_mindoo;
  memcpy(&raw_max_recommended_mindoo, &tmp_max_recommended_mindoo, sizeof(tmp_max_recommended_mindoo));
  if (raw_max_recommended_mindoo != 0) {
    total_size += 1 + 4;
  }

  // int32 min_items_for_recommendation = 9;
  if (this->_internal_min_items_for_recommendation() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_min_items_for_recommendation());
  }

  // float min_recommended_mindoo = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_recommended_mindoo = this->_internal_min_recommended_mindoo();
  uint32_t raw_min_recommended_mindoo;
  memcpy(&raw_min_recommended_mindoo, &tmp_min_recommended_mindoo, sizeof(tmp_min_recommended_mindoo));
  if (raw_min_recommended_mindoo != 0) {
    total_size += 1 + 4;
  }

  // float min_recommended_weed_threshold = 12;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_recommended_weed_threshold = this->_internal_min_recommended_weed_threshold();
  uint32_t raw_min_recommended_weed_threshold;
  memcpy(&raw_min_recommended_weed_threshold, &tmp_min_recommended_weed_threshold, sizeof(tmp_min_recommended_weed_threshold));
  if (raw_min_recommended_weed_threshold != 0) {
    total_size += 1 + 4;
  }

  // float max_recommended_weed_threshold = 13;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_recommended_weed_threshold = this->_internal_max_recommended_weed_threshold();
  uint32_t raw_max_recommended_weed_threshold;
  memcpy(&raw_max_recommended_weed_threshold, &tmp_max_recommended_weed_threshold, sizeof(tmp_max_recommended_weed_threshold));
  if (raw_max_recommended_weed_threshold != 0) {
    total_size += 1 + 4;
  }

  // float min_recommended_crop_threshold = 14;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_recommended_crop_threshold = this->_internal_min_recommended_crop_threshold();
  uint32_t raw_min_recommended_crop_threshold;
  memcpy(&raw_min_recommended_crop_threshold, &tmp_min_recommended_crop_threshold, sizeof(tmp_min_recommended_crop_threshold));
  if (raw_min_recommended_crop_threshold != 0) {
    total_size += 1 + 4;
  }

  // float max_recommended_crop_threshold = 15;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_recommended_crop_threshold = this->_internal_max_recommended_crop_threshold();
  uint32_t raw_max_recommended_crop_threshold;
  memcpy(&raw_max_recommended_crop_threshold, &tmp_max_recommended_crop_threshold, sizeof(tmp_max_recommended_crop_threshold));
  if (raw_max_recommended_crop_threshold != 0) {
    total_size += 1 + 4;
  }

  // float min_doo_for_recommendation = 16;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_doo_for_recommendation = this->_internal_min_doo_for_recommendation();
  uint32_t raw_min_doo_for_recommendation;
  memcpy(&raw_min_doo_for_recommendation, &tmp_min_doo_for_recommendation, sizeof(tmp_min_doo_for_recommendation));
  if (raw_min_doo_for_recommendation != 0) {
    total_size += 2 + 4;
  }

  // bool use_weed_categories_for_weed_labels = 10;
  if (this->_internal_use_weed_categories_for_weed_labels() != 0) {
    total_size += 1 + 1;
  }

  // bool use_other_as_tiebreaker = 17;
  if (this->_internal_use_other_as_tiebreaker() != 0) {
    total_size += 2 + 1;
  }

  // bool limit_by_crops_missed = 18;
  if (this->_internal_limit_by_crops_missed() != 0) {
    total_size += 2 + 1;
  }

  // bool pad_crop_configurations = 21;
  if (this->_internal_pad_crop_configurations() != 0) {
    total_size += 2 + 1;
  }

  // int32 number_of_crop_configurations = 19;
  if (this->_internal_number_of_crop_configurations() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_number_of_crop_configurations());
  }

  // bool use_beneficials_as_crops = 23;
  if (this->_internal_use_beneficials_as_crops() != 0) {
    total_size += 2 + 1;
  }

  // bool use_volunteers_as_weeds = 24;
  if (this->_internal_use_volunteers_as_weeds() != 0) {
    total_size += 2 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PlantCaptchaResults::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PlantCaptchaResults::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PlantCaptchaResults::GetClassData() const { return &_class_data_; }

void PlantCaptchaResults::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PlantCaptchaResults *>(to)->MergeFrom(
      static_cast<const PlantCaptchaResults &>(from));
}


void PlantCaptchaResults::MergeFrom(const PlantCaptchaResults& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.PlantCaptchaResults)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  captcha_results_.MergeFrom(from.captcha_results_);
  if (!from._internal_algorithm().empty()) {
    _internal_set_algorithm(from._internal_algorithm());
  }
  if (!from._internal_tiebreaker().empty()) {
    _internal_set_tiebreaker(from._internal_tiebreaker());
  }
  if (!from._internal_mindoo_tiebreaker().empty()) {
    _internal_set_mindoo_tiebreaker(from._internal_mindoo_tiebreaker());
  }
  if (!from._internal_tiebreaker_strategy_threshold_weed().empty()) {
    _internal_set_tiebreaker_strategy_threshold_weed(from._internal_tiebreaker_strategy_threshold_weed());
  }
  if (!from._internal_tiebreaker_strategy_threshold_crop().empty()) {
    _internal_set_tiebreaker_strategy_threshold_crop(from._internal_tiebreaker_strategy_threshold_crop());
  }
  if (!from._internal_tiebreaker_strategy_mindoo_weed().empty()) {
    _internal_set_tiebreaker_strategy_mindoo_weed(from._internal_tiebreaker_strategy_mindoo_weed());
  }
  if (!from._internal_tiebreaker_strategy_mindoo_crop().empty()) {
    _internal_set_tiebreaker_strategy_mindoo_crop(from._internal_tiebreaker_strategy_mindoo_crop());
  }
  if (from._internal_has_current_parameters()) {
    _internal_mutable_current_parameters()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_current_parameters());
  }
  if (from._internal_has_almanac()) {
    _internal_mutable_almanac()->::carbon::aimbot::almanac::AlmanacConfig::MergeFrom(from._internal_almanac());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_goal_crops_targeted = from._internal_goal_crops_targeted();
  uint32_t raw_goal_crops_targeted;
  memcpy(&raw_goal_crops_targeted, &tmp_goal_crops_targeted, sizeof(tmp_goal_crops_targeted));
  if (raw_goal_crops_targeted != 0) {
    _internal_set_goal_crops_targeted(from._internal_goal_crops_targeted());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_goal_weeds_targeted = from._internal_goal_weeds_targeted();
  uint32_t raw_goal_weeds_targeted;
  memcpy(&raw_goal_weeds_targeted, &tmp_goal_weeds_targeted, sizeof(tmp_goal_weeds_targeted));
  if (raw_goal_weeds_targeted != 0) {
    _internal_set_goal_weeds_targeted(from._internal_goal_weeds_targeted());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_goal_unknown_targeted = from._internal_goal_unknown_targeted();
  uint32_t raw_goal_unknown_targeted;
  memcpy(&raw_goal_unknown_targeted, &tmp_goal_unknown_targeted, sizeof(tmp_goal_unknown_targeted));
  if (raw_goal_unknown_targeted != 0) {
    _internal_set_goal_unknown_targeted(from._internal_goal_unknown_targeted());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_recommended_mindoo = from._internal_max_recommended_mindoo();
  uint32_t raw_max_recommended_mindoo;
  memcpy(&raw_max_recommended_mindoo, &tmp_max_recommended_mindoo, sizeof(tmp_max_recommended_mindoo));
  if (raw_max_recommended_mindoo != 0) {
    _internal_set_max_recommended_mindoo(from._internal_max_recommended_mindoo());
  }
  if (from._internal_min_items_for_recommendation() != 0) {
    _internal_set_min_items_for_recommendation(from._internal_min_items_for_recommendation());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_recommended_mindoo = from._internal_min_recommended_mindoo();
  uint32_t raw_min_recommended_mindoo;
  memcpy(&raw_min_recommended_mindoo, &tmp_min_recommended_mindoo, sizeof(tmp_min_recommended_mindoo));
  if (raw_min_recommended_mindoo != 0) {
    _internal_set_min_recommended_mindoo(from._internal_min_recommended_mindoo());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_recommended_weed_threshold = from._internal_min_recommended_weed_threshold();
  uint32_t raw_min_recommended_weed_threshold;
  memcpy(&raw_min_recommended_weed_threshold, &tmp_min_recommended_weed_threshold, sizeof(tmp_min_recommended_weed_threshold));
  if (raw_min_recommended_weed_threshold != 0) {
    _internal_set_min_recommended_weed_threshold(from._internal_min_recommended_weed_threshold());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_recommended_weed_threshold = from._internal_max_recommended_weed_threshold();
  uint32_t raw_max_recommended_weed_threshold;
  memcpy(&raw_max_recommended_weed_threshold, &tmp_max_recommended_weed_threshold, sizeof(tmp_max_recommended_weed_threshold));
  if (raw_max_recommended_weed_threshold != 0) {
    _internal_set_max_recommended_weed_threshold(from._internal_max_recommended_weed_threshold());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_recommended_crop_threshold = from._internal_min_recommended_crop_threshold();
  uint32_t raw_min_recommended_crop_threshold;
  memcpy(&raw_min_recommended_crop_threshold, &tmp_min_recommended_crop_threshold, sizeof(tmp_min_recommended_crop_threshold));
  if (raw_min_recommended_crop_threshold != 0) {
    _internal_set_min_recommended_crop_threshold(from._internal_min_recommended_crop_threshold());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_recommended_crop_threshold = from._internal_max_recommended_crop_threshold();
  uint32_t raw_max_recommended_crop_threshold;
  memcpy(&raw_max_recommended_crop_threshold, &tmp_max_recommended_crop_threshold, sizeof(tmp_max_recommended_crop_threshold));
  if (raw_max_recommended_crop_threshold != 0) {
    _internal_set_max_recommended_crop_threshold(from._internal_max_recommended_crop_threshold());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_doo_for_recommendation = from._internal_min_doo_for_recommendation();
  uint32_t raw_min_doo_for_recommendation;
  memcpy(&raw_min_doo_for_recommendation, &tmp_min_doo_for_recommendation, sizeof(tmp_min_doo_for_recommendation));
  if (raw_min_doo_for_recommendation != 0) {
    _internal_set_min_doo_for_recommendation(from._internal_min_doo_for_recommendation());
  }
  if (from._internal_use_weed_categories_for_weed_labels() != 0) {
    _internal_set_use_weed_categories_for_weed_labels(from._internal_use_weed_categories_for_weed_labels());
  }
  if (from._internal_use_other_as_tiebreaker() != 0) {
    _internal_set_use_other_as_tiebreaker(from._internal_use_other_as_tiebreaker());
  }
  if (from._internal_limit_by_crops_missed() != 0) {
    _internal_set_limit_by_crops_missed(from._internal_limit_by_crops_missed());
  }
  if (from._internal_pad_crop_configurations() != 0) {
    _internal_set_pad_crop_configurations(from._internal_pad_crop_configurations());
  }
  if (from._internal_number_of_crop_configurations() != 0) {
    _internal_set_number_of_crop_configurations(from._internal_number_of_crop_configurations());
  }
  if (from._internal_use_beneficials_as_crops() != 0) {
    _internal_set_use_beneficials_as_crops(from._internal_use_beneficials_as_crops());
  }
  if (from._internal_use_volunteers_as_weeds() != 0) {
    _internal_set_use_volunteers_as_weeds(from._internal_use_volunteers_as_weeds());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PlantCaptchaResults::CopyFrom(const PlantCaptchaResults& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.PlantCaptchaResults)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PlantCaptchaResults::IsInitialized() const {
  return true;
}

void PlantCaptchaResults::InternalSwap(PlantCaptchaResults* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  captcha_results_.InternalSwap(&other->captcha_results_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &algorithm_, lhs_arena,
      &other->algorithm_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &tiebreaker_, lhs_arena,
      &other->tiebreaker_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &mindoo_tiebreaker_, lhs_arena,
      &other->mindoo_tiebreaker_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &tiebreaker_strategy_threshold_weed_, lhs_arena,
      &other->tiebreaker_strategy_threshold_weed_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &tiebreaker_strategy_threshold_crop_, lhs_arena,
      &other->tiebreaker_strategy_threshold_crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &tiebreaker_strategy_mindoo_weed_, lhs_arena,
      &other->tiebreaker_strategy_mindoo_weed_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &tiebreaker_strategy_mindoo_crop_, lhs_arena,
      &other->tiebreaker_strategy_mindoo_crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PlantCaptchaResults, use_volunteers_as_weeds_)
      + sizeof(PlantCaptchaResults::use_volunteers_as_weeds_)
      - PROTOBUF_FIELD_OFFSET(PlantCaptchaResults, current_parameters_)>(
          reinterpret_cast<char*>(&current_parameters_),
          reinterpret_cast<char*>(&other->current_parameters_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PlantCaptchaResults::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[22]);
}

// ===================================================================

class VeselkaPlantCaptchaResponse::_Internal {
 public:
  static const ::carbon::aimbot::almanac::ModelinatorConfig& new_model_parameters(const VeselkaPlantCaptchaResponse* msg);
};

const ::carbon::aimbot::almanac::ModelinatorConfig&
VeselkaPlantCaptchaResponse::_Internal::new_model_parameters(const VeselkaPlantCaptchaResponse* msg) {
  return *msg->new_model_parameters_;
}
void VeselkaPlantCaptchaResponse::clear_new_model_parameters() {
  if (GetArenaForAllocation() == nullptr && new_model_parameters_ != nullptr) {
    delete new_model_parameters_;
  }
  new_model_parameters_ = nullptr;
}
VeselkaPlantCaptchaResponse::VeselkaPlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse)
}
VeselkaPlantCaptchaResponse::VeselkaPlantCaptchaResponse(const VeselkaPlantCaptchaResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_new_model_parameters()) {
    new_model_parameters_ = new ::carbon::aimbot::almanac::ModelinatorConfig(*from.new_model_parameters_);
  } else {
    new_model_parameters_ = nullptr;
  }
  succeeded_ = from.succeeded_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse)
}

inline void VeselkaPlantCaptchaResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&new_model_parameters_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&succeeded_) -
    reinterpret_cast<char*>(&new_model_parameters_)) + sizeof(succeeded_));
}

VeselkaPlantCaptchaResponse::~VeselkaPlantCaptchaResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VeselkaPlantCaptchaResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete new_model_parameters_;
}

void VeselkaPlantCaptchaResponse::ArenaDtor(void* object) {
  VeselkaPlantCaptchaResponse* _this = reinterpret_cast< VeselkaPlantCaptchaResponse* >(object);
  (void)_this;
}
void VeselkaPlantCaptchaResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VeselkaPlantCaptchaResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VeselkaPlantCaptchaResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && new_model_parameters_ != nullptr) {
    delete new_model_parameters_;
  }
  new_model_parameters_ = nullptr;
  succeeded_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VeselkaPlantCaptchaResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.ModelinatorConfig new_model_parameters = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_new_model_parameters(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool succeeded = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          succeeded_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* VeselkaPlantCaptchaResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.ModelinatorConfig new_model_parameters = 1;
  if (this->_internal_has_new_model_parameters()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::new_model_parameters(this), target, stream);
  }

  // bool succeeded = 2;
  if (this->_internal_succeeded() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_succeeded(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse)
  return target;
}

size_t VeselkaPlantCaptchaResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.ModelinatorConfig new_model_parameters = 1;
  if (this->_internal_has_new_model_parameters()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *new_model_parameters_);
  }

  // bool succeeded = 2;
  if (this->_internal_succeeded() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VeselkaPlantCaptchaResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VeselkaPlantCaptchaResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VeselkaPlantCaptchaResponse::GetClassData() const { return &_class_data_; }

void VeselkaPlantCaptchaResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<VeselkaPlantCaptchaResponse *>(to)->MergeFrom(
      static_cast<const VeselkaPlantCaptchaResponse &>(from));
}


void VeselkaPlantCaptchaResponse::MergeFrom(const VeselkaPlantCaptchaResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_new_model_parameters()) {
    _internal_mutable_new_model_parameters()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_new_model_parameters());
  }
  if (from._internal_succeeded() != 0) {
    _internal_set_succeeded(from._internal_succeeded());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VeselkaPlantCaptchaResponse::CopyFrom(const VeselkaPlantCaptchaResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VeselkaPlantCaptchaResponse::IsInitialized() const {
  return true;
}

void VeselkaPlantCaptchaResponse::InternalSwap(VeselkaPlantCaptchaResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VeselkaPlantCaptchaResponse, succeeded_)
      + sizeof(VeselkaPlantCaptchaResponse::succeeded_)
      - PROTOBUF_FIELD_OFFSET(VeselkaPlantCaptchaResponse, new_model_parameters_)>(
          reinterpret_cast<char*>(&new_model_parameters_),
          reinterpret_cast<char*>(&other->new_model_parameters_));
}

::PROTOBUF_NAMESPACE_ID::Metadata VeselkaPlantCaptchaResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[23]);
}

// ===================================================================

class GetOriginalModelinatorConfigRequest::_Internal {
 public:
};

GetOriginalModelinatorConfigRequest::GetOriginalModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest)
}
GetOriginalModelinatorConfigRequest::GetOriginalModelinatorConfigRequest(const GetOriginalModelinatorConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest)
}

inline void GetOriginalModelinatorConfigRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetOriginalModelinatorConfigRequest::~GetOriginalModelinatorConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetOriginalModelinatorConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetOriginalModelinatorConfigRequest::ArenaDtor(void* object) {
  GetOriginalModelinatorConfigRequest* _this = reinterpret_cast< GetOriginalModelinatorConfigRequest* >(object);
  (void)_this;
}
void GetOriginalModelinatorConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetOriginalModelinatorConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetOriginalModelinatorConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetOriginalModelinatorConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetOriginalModelinatorConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest)
  return target;
}

size_t GetOriginalModelinatorConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetOriginalModelinatorConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetOriginalModelinatorConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetOriginalModelinatorConfigRequest::GetClassData() const { return &_class_data_; }

void GetOriginalModelinatorConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetOriginalModelinatorConfigRequest *>(to)->MergeFrom(
      static_cast<const GetOriginalModelinatorConfigRequest &>(from));
}


void GetOriginalModelinatorConfigRequest::MergeFrom(const GetOriginalModelinatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetOriginalModelinatorConfigRequest::CopyFrom(const GetOriginalModelinatorConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetOriginalModelinatorConfigRequest::IsInitialized() const {
  return true;
}

void GetOriginalModelinatorConfigRequest::InternalSwap(GetOriginalModelinatorConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetOriginalModelinatorConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[24]);
}

// ===================================================================

class GetOriginalModelinatorConfigResponse::_Internal {
 public:
  static const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator_config(const GetOriginalModelinatorConfigResponse* msg);
};

const ::carbon::aimbot::almanac::ModelinatorConfig&
GetOriginalModelinatorConfigResponse::_Internal::modelinator_config(const GetOriginalModelinatorConfigResponse* msg) {
  return *msg->modelinator_config_;
}
void GetOriginalModelinatorConfigResponse::clear_modelinator_config() {
  if (GetArenaForAllocation() == nullptr && modelinator_config_ != nullptr) {
    delete modelinator_config_;
  }
  modelinator_config_ = nullptr;
}
GetOriginalModelinatorConfigResponse::GetOriginalModelinatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse)
}
GetOriginalModelinatorConfigResponse::GetOriginalModelinatorConfigResponse(const GetOriginalModelinatorConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_modelinator_config()) {
    modelinator_config_ = new ::carbon::aimbot::almanac::ModelinatorConfig(*from.modelinator_config_);
  } else {
    modelinator_config_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse)
}

inline void GetOriginalModelinatorConfigResponse::SharedCtor() {
modelinator_config_ = nullptr;
}

GetOriginalModelinatorConfigResponse::~GetOriginalModelinatorConfigResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetOriginalModelinatorConfigResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete modelinator_config_;
}

void GetOriginalModelinatorConfigResponse::ArenaDtor(void* object) {
  GetOriginalModelinatorConfigResponse* _this = reinterpret_cast< GetOriginalModelinatorConfigResponse* >(object);
  (void)_this;
}
void GetOriginalModelinatorConfigResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetOriginalModelinatorConfigResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetOriginalModelinatorConfigResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && modelinator_config_ != nullptr) {
    delete modelinator_config_;
  }
  modelinator_config_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetOriginalModelinatorConfigResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_modelinator_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetOriginalModelinatorConfigResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
  if (this->_internal_has_modelinator_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::modelinator_config(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse)
  return target;
}

size_t GetOriginalModelinatorConfigResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
  if (this->_internal_has_modelinator_config()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *modelinator_config_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetOriginalModelinatorConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetOriginalModelinatorConfigResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetOriginalModelinatorConfigResponse::GetClassData() const { return &_class_data_; }

void GetOriginalModelinatorConfigResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetOriginalModelinatorConfigResponse *>(to)->MergeFrom(
      static_cast<const GetOriginalModelinatorConfigResponse &>(from));
}


void GetOriginalModelinatorConfigResponse::MergeFrom(const GetOriginalModelinatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_modelinator_config()) {
    _internal_mutable_modelinator_config()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_modelinator_config());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetOriginalModelinatorConfigResponse::CopyFrom(const GetOriginalModelinatorConfigResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetOriginalModelinatorConfigResponse::IsInitialized() const {
  return true;
}

void GetOriginalModelinatorConfigResponse::InternalSwap(GetOriginalModelinatorConfigResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(modelinator_config_, other->modelinator_config_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetOriginalModelinatorConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[25]);
}

// ===================================================================

GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse() {}
GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse::MergeFrom(const GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[26]);
}

// ===================================================================

class GetCaptchaRowStatusResponse::_Internal {
 public:
};

void GetCaptchaRowStatusResponse::clear_row_status() {
  row_status_.Clear();
}
GetCaptchaRowStatusResponse::GetCaptchaRowStatusResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  row_status_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse)
}
GetCaptchaRowStatusResponse::GetCaptchaRowStatusResponse(const GetCaptchaRowStatusResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  row_status_.MergeFrom(from.row_status_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse)
}

inline void GetCaptchaRowStatusResponse::SharedCtor() {
}

GetCaptchaRowStatusResponse::~GetCaptchaRowStatusResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetCaptchaRowStatusResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetCaptchaRowStatusResponse::ArenaDtor(void* object) {
  GetCaptchaRowStatusResponse* _this = reinterpret_cast< GetCaptchaRowStatusResponse* >(object);
  (void)_this;
  _this->row_status_. ~MapField();
}
inline void GetCaptchaRowStatusResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &GetCaptchaRowStatusResponse::ArenaDtor);
  }
}
void GetCaptchaRowStatusResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetCaptchaRowStatusResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  row_status_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetCaptchaRowStatusResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<int32, .weed_tracking.PlantCaptchaStatusResponse> row_status = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&row_status_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetCaptchaRowStatusResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<int32, .weed_tracking.PlantCaptchaStatusResponse> row_status = 1;
  if (!this->_internal_row_status().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_row_status().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_row_status().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >::const_iterator
          it = this->_internal_row_status().begin();
          it != this->_internal_row_status().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >::const_iterator
          it = this->_internal_row_status().begin();
          it != this->_internal_row_status().end(); ++it) {
        target = GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse)
  return target;
}

size_t GetCaptchaRowStatusResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<int32, .weed_tracking.PlantCaptchaStatusResponse> row_status = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_row_status_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >::const_iterator
      it = this->_internal_row_status().begin();
      it != this->_internal_row_status().end(); ++it) {
    total_size += GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetCaptchaRowStatusResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetCaptchaRowStatusResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetCaptchaRowStatusResponse::GetClassData() const { return &_class_data_; }

void GetCaptchaRowStatusResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetCaptchaRowStatusResponse *>(to)->MergeFrom(
      static_cast<const GetCaptchaRowStatusResponse &>(from));
}


void GetCaptchaRowStatusResponse::MergeFrom(const GetCaptchaRowStatusResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  row_status_.MergeFrom(from.row_status_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetCaptchaRowStatusResponse::CopyFrom(const GetCaptchaRowStatusResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetCaptchaRowStatusResponse::IsInitialized() const {
  return true;
}

void GetCaptchaRowStatusResponse::InternalSwap(GetCaptchaRowStatusResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  row_status_.InternalSwap(&other->row_status_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetCaptchaRowStatusResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[27]);
}

// ===================================================================

class CancelPlantCaptchaOnRowRequest::_Internal {
 public:
};

CancelPlantCaptchaOnRowRequest::CancelPlantCaptchaOnRowRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest)
}
CancelPlantCaptchaOnRowRequest::CancelPlantCaptchaOnRowRequest(const CancelPlantCaptchaOnRowRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  row_id_ = from.row_id_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest)
}

inline void CancelPlantCaptchaOnRowRequest::SharedCtor() {
row_id_ = 0;
}

CancelPlantCaptchaOnRowRequest::~CancelPlantCaptchaOnRowRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CancelPlantCaptchaOnRowRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CancelPlantCaptchaOnRowRequest::ArenaDtor(void* object) {
  CancelPlantCaptchaOnRowRequest* _this = reinterpret_cast< CancelPlantCaptchaOnRowRequest* >(object);
  (void)_this;
}
void CancelPlantCaptchaOnRowRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CancelPlantCaptchaOnRowRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CancelPlantCaptchaOnRowRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  row_id_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CancelPlantCaptchaOnRowRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 row_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          row_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CancelPlantCaptchaOnRowRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 row_id = 1;
  if (this->_internal_row_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_row_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest)
  return target;
}

size_t CancelPlantCaptchaOnRowRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 row_id = 1;
  if (this->_internal_row_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_row_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CancelPlantCaptchaOnRowRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CancelPlantCaptchaOnRowRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CancelPlantCaptchaOnRowRequest::GetClassData() const { return &_class_data_; }

void CancelPlantCaptchaOnRowRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CancelPlantCaptchaOnRowRequest *>(to)->MergeFrom(
      static_cast<const CancelPlantCaptchaOnRowRequest &>(from));
}


void CancelPlantCaptchaOnRowRequest::MergeFrom(const CancelPlantCaptchaOnRowRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_row_id() != 0) {
    _internal_set_row_id(from._internal_row_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CancelPlantCaptchaOnRowRequest::CopyFrom(const CancelPlantCaptchaOnRowRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CancelPlantCaptchaOnRowRequest::IsInitialized() const {
  return true;
}

void CancelPlantCaptchaOnRowRequest::InternalSwap(CancelPlantCaptchaOnRowRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(row_id_, other->row_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CancelPlantCaptchaOnRowRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_getter, &descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto_once,
      file_level_metadata_frontend_2fproto_2fplant_5fcaptcha_2eproto[28]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace plant_captcha
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::PlantCaptcha* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::PlantCaptcha >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::PlantCaptcha >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::PlantCaptchaListItem* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::PlantCaptchaListItem >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::PlantCaptchaListItem >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::PlantCaptchaItem* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::PlantCaptchaItem >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::PlantCaptchaItem >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::PlantCaptchaResult* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::PlantCaptchaResult >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::PlantCaptchaResult >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::PlantCaptchaResults* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::PlantCaptchaResults >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::PlantCaptchaResults >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::VeselkaPlantCaptchaResponse* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::VeselkaPlantCaptchaResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::VeselkaPlantCaptchaResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* Arena::CreateMaybeMessage< ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

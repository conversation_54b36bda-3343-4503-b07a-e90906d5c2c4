# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/features.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/features.proto',
  package='carbon.frontend.features',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1d\x66rontend/proto/features.proto\x12\x18\x63\x61rbon.frontend.features\x1a\x19\x66rontend/proto/util.proto\"=\n\x10RowConfiguration\x12\x14\n\x0cnum_predicts\x18\x01 \x01(\x05\x12\x13\n\x0bnum_targets\x18\x02 \x01(\x05\"\xa4\x02\n\x12RobotConfiguration\x12\x10\n\x08num_rows\x18\x01 \x01(\x05\x12]\n\x11row_configuration\x18\x02 \x03(\x0b\x32\x42.carbon.frontend.features.RobotConfiguration.RowConfigurationEntry\x12\x38\n\ngeneration\x18\x03 \x01(\x0e\x32$.carbon.frontend.features.Generation\x1a\x63\n\x15RowConfigurationEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x39\n\x05value\x18\x02 \x01(\x0b\x32*.carbon.frontend.features.RowConfiguration:\x02\x38\x01*3\n\nGeneration\x12\r\n\tUndefined\x10\x00\x12\n\n\x06Slayer\x10\x01\x12\n\n\x06Reaper\x10\x02\x32\xd0\x01\n\x0e\x46\x65\x61tureService\x12Z\n\x13GetNextFeatureFlags\x12\x1f.carbon.frontend.util.Timestamp\x1a\".carbon.frontend.util.FeatureFlags\x12\x62\n\x15GetRobotConfiguration\x12\x1b.carbon.frontend.util.Empty\x1a,.carbon.frontend.features.RobotConfigurationB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])

_GENERATION = _descriptor.EnumDescriptor(
  name='Generation',
  full_name='carbon.frontend.features.Generation',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='Undefined', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Slayer', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Reaper', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=444,
  serialized_end=495,
)
_sym_db.RegisterEnumDescriptor(_GENERATION)

Generation = enum_type_wrapper.EnumTypeWrapper(_GENERATION)
Undefined = 0
Slayer = 1
Reaper = 2



_ROWCONFIGURATION = _descriptor.Descriptor(
  name='RowConfiguration',
  full_name='carbon.frontend.features.RowConfiguration',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='num_predicts', full_name='carbon.frontend.features.RowConfiguration.num_predicts', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_targets', full_name='carbon.frontend.features.RowConfiguration.num_targets', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=86,
  serialized_end=147,
)


_ROBOTCONFIGURATION_ROWCONFIGURATIONENTRY = _descriptor.Descriptor(
  name='RowConfigurationEntry',
  full_name='carbon.frontend.features.RobotConfiguration.RowConfigurationEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.features.RobotConfiguration.RowConfigurationEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.features.RobotConfiguration.RowConfigurationEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=343,
  serialized_end=442,
)

_ROBOTCONFIGURATION = _descriptor.Descriptor(
  name='RobotConfiguration',
  full_name='carbon.frontend.features.RobotConfiguration',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='num_rows', full_name='carbon.frontend.features.RobotConfiguration.num_rows', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_configuration', full_name='carbon.frontend.features.RobotConfiguration.row_configuration', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='generation', full_name='carbon.frontend.features.RobotConfiguration.generation', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_ROBOTCONFIGURATION_ROWCONFIGURATIONENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=150,
  serialized_end=442,
)

_ROBOTCONFIGURATION_ROWCONFIGURATIONENTRY.fields_by_name['value'].message_type = _ROWCONFIGURATION
_ROBOTCONFIGURATION_ROWCONFIGURATIONENTRY.containing_type = _ROBOTCONFIGURATION
_ROBOTCONFIGURATION.fields_by_name['row_configuration'].message_type = _ROBOTCONFIGURATION_ROWCONFIGURATIONENTRY
_ROBOTCONFIGURATION.fields_by_name['generation'].enum_type = _GENERATION
DESCRIPTOR.message_types_by_name['RowConfiguration'] = _ROWCONFIGURATION
DESCRIPTOR.message_types_by_name['RobotConfiguration'] = _ROBOTCONFIGURATION
DESCRIPTOR.enum_types_by_name['Generation'] = _GENERATION
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RowConfiguration = _reflection.GeneratedProtocolMessageType('RowConfiguration', (_message.Message,), {
  'DESCRIPTOR' : _ROWCONFIGURATION,
  '__module__' : 'frontend.proto.features_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.features.RowConfiguration)
  })
_sym_db.RegisterMessage(RowConfiguration)

RobotConfiguration = _reflection.GeneratedProtocolMessageType('RobotConfiguration', (_message.Message,), {

  'RowConfigurationEntry' : _reflection.GeneratedProtocolMessageType('RowConfigurationEntry', (_message.Message,), {
    'DESCRIPTOR' : _ROBOTCONFIGURATION_ROWCONFIGURATIONENTRY,
    '__module__' : 'frontend.proto.features_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.features.RobotConfiguration.RowConfigurationEntry)
    })
  ,
  'DESCRIPTOR' : _ROBOTCONFIGURATION,
  '__module__' : 'frontend.proto.features_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.features.RobotConfiguration)
  })
_sym_db.RegisterMessage(RobotConfiguration)
_sym_db.RegisterMessage(RobotConfiguration.RowConfigurationEntry)


DESCRIPTOR._options = None
_ROBOTCONFIGURATION_ROWCONFIGURATIONENTRY._options = None

_FEATURESERVICE = _descriptor.ServiceDescriptor(
  name='FeatureService',
  full_name='carbon.frontend.features.FeatureService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=498,
  serialized_end=706,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextFeatureFlags',
    full_name='carbon.frontend.features.FeatureService.GetNextFeatureFlags',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=frontend_dot_proto_dot_util__pb2._FEATUREFLAGS,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetRobotConfiguration',
    full_name='carbon.frontend.features.FeatureService.GetRobotConfiguration',
    index=1,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_ROBOTCONFIGURATION,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_FEATURESERVICE)

DESCRIPTOR.services_by_name['FeatureService'] = _FEATURESERVICE

# @@protoc_insertion_point(module_scope)

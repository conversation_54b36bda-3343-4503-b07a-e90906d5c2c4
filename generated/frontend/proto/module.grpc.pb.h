// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/module.proto
#ifndef GRPC_frontend_2fproto_2fmodule_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fmodule_2eproto__INCLUDED

#include "frontend/proto/module.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace module {

class ModuleAssignmentService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.module.ModuleAssignmentService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Module Identity 
    virtual ::grpc::Status GetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::carbon::frontend::module::GetNextModulesListResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextModulesListResponse>> AsyncGetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextModulesListResponse>>(AsyncGetNextModulesListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextModulesListResponse>> PrepareAsyncGetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextModulesListResponse>>(PrepareAsyncGetNextModulesListRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::carbon::frontend::module::GetNextActiveModulesResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextActiveModulesResponse>> AsyncGetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextActiveModulesResponse>>(AsyncGetNextActiveModulesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextActiveModulesResponse>> PrepareAsyncGetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextActiveModulesResponse>>(PrepareAsyncGetNextActiveModulesRaw(context, request, cq));
    }
    virtual ::grpc::Status IdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncIdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncIdentifyModuleRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncIdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncIdentifyModuleRaw(context, request, cq));
    }
    virtual ::grpc::Status AssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncAssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncAssignModuleRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncAssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncAssignModuleRaw(context, request, cq));
    }
    virtual ::grpc::Status ClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncClearModuleAssignmentRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncClearModuleAssignmentRaw(context, request, cq));
    }
    virtual ::grpc::Status SetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetModuleSerialRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetModuleSerialRaw(context, request, cq));
    }
    // Robot Definition 
    virtual ::grpc::Status GetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::carbon::frontend::module::GetPresetsListResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetPresetsListResponse>> AsyncGetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetPresetsListResponse>>(AsyncGetPresetsListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetPresetsListResponse>> PrepareAsyncGetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetPresetsListResponse>>(PrepareAsyncGetPresetsListRaw(context, request, cq));
    }
    virtual ::grpc::Status GetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>> AsyncGetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>>(AsyncGetCurrentRobotDefinitionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>> PrepareAsyncGetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>>(PrepareAsyncGetCurrentRobotDefinitionRaw(context, request, cq));
    }
    virtual ::grpc::Status SetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetCurrentRobotDefinitionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetCurrentRobotDefinitionRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      // Module Identity 
      virtual void GetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest* request, ::carbon::frontend::module::GetNextModulesListResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest* request, ::carbon::frontend::module::GetNextModulesListResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest* request, ::carbon::frontend::module::GetNextActiveModulesResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest* request, ::carbon::frontend::module::GetNextActiveModulesResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void IdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void IdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void AssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void AssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      // Robot Definition 
      virtual void GetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest* request, ::carbon::frontend::module::GetPresetsListResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest* request, ::carbon::frontend::module::GetPresetsListResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextModulesListResponse>* AsyncGetNextModulesListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextModulesListResponse>* PrepareAsyncGetNextModulesListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextActiveModulesResponse>* AsyncGetNextActiveModulesRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetNextActiveModulesResponse>* PrepareAsyncGetNextActiveModulesRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncIdentifyModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncIdentifyModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncAssignModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncAssignModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncClearModuleAssignmentRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncClearModuleAssignmentRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetModuleSerialRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetModuleSerialRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetPresetsListResponse>* AsyncGetPresetsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetPresetsListResponse>* PrepareAsyncGetPresetsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>* AsyncGetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>* PrepareAsyncGetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::carbon::frontend::module::GetNextModulesListResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextModulesListResponse>> AsyncGetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextModulesListResponse>>(AsyncGetNextModulesListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextModulesListResponse>> PrepareAsyncGetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextModulesListResponse>>(PrepareAsyncGetNextModulesListRaw(context, request, cq));
    }
    ::grpc::Status GetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::carbon::frontend::module::GetNextActiveModulesResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextActiveModulesResponse>> AsyncGetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextActiveModulesResponse>>(AsyncGetNextActiveModulesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextActiveModulesResponse>> PrepareAsyncGetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextActiveModulesResponse>>(PrepareAsyncGetNextActiveModulesRaw(context, request, cq));
    }
    ::grpc::Status IdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncIdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncIdentifyModuleRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncIdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncIdentifyModuleRaw(context, request, cq));
    }
    ::grpc::Status AssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncAssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncAssignModuleRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncAssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncAssignModuleRaw(context, request, cq));
    }
    ::grpc::Status ClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncClearModuleAssignmentRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncClearModuleAssignmentRaw(context, request, cq));
    }
    ::grpc::Status SetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetModuleSerialRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetModuleSerialRaw(context, request, cq));
    }
    ::grpc::Status GetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::carbon::frontend::module::GetPresetsListResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetPresetsListResponse>> AsyncGetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetPresetsListResponse>>(AsyncGetPresetsListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetPresetsListResponse>> PrepareAsyncGetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetPresetsListResponse>>(PrepareAsyncGetPresetsListRaw(context, request, cq));
    }
    ::grpc::Status GetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>> AsyncGetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>>(AsyncGetCurrentRobotDefinitionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>> PrepareAsyncGetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>>(PrepareAsyncGetCurrentRobotDefinitionRaw(context, request, cq));
    }
    ::grpc::Status SetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetCurrentRobotDefinitionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetCurrentRobotDefinitionRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest* request, ::carbon::frontend::module::GetNextModulesListResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest* request, ::carbon::frontend::module::GetNextModulesListResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest* request, ::carbon::frontend::module::GetNextActiveModulesResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest* request, ::carbon::frontend::module::GetNextActiveModulesResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void IdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void IdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void AssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void AssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void ClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest* request, ::carbon::frontend::module::GetPresetsListResponse* response, std::function<void(::grpc::Status)>) override;
      void GetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest* request, ::carbon::frontend::module::GetPresetsListResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response, std::function<void(::grpc::Status)>) override;
      void GetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextModulesListResponse>* AsyncGetNextModulesListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextModulesListResponse>* PrepareAsyncGetNextModulesListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextActiveModulesResponse>* AsyncGetNextActiveModulesRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextActiveModulesResponse>* PrepareAsyncGetNextActiveModulesRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncIdentifyModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncIdentifyModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncAssignModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncAssignModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncClearModuleAssignmentRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncClearModuleAssignmentRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetModuleSerialRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetModuleSerialRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetPresetsListResponse>* AsyncGetPresetsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetPresetsListResponse>* PrepareAsyncGetPresetsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>* AsyncGetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>* PrepareAsyncGetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextModulesList_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextActiveModules_;
    const ::grpc::internal::RpcMethod rpcmethod_IdentifyModule_;
    const ::grpc::internal::RpcMethod rpcmethod_AssignModule_;
    const ::grpc::internal::RpcMethod rpcmethod_ClearModuleAssignment_;
    const ::grpc::internal::RpcMethod rpcmethod_SetModuleSerial_;
    const ::grpc::internal::RpcMethod rpcmethod_GetPresetsList_;
    const ::grpc::internal::RpcMethod rpcmethod_GetCurrentRobotDefinition_;
    const ::grpc::internal::RpcMethod rpcmethod_SetCurrentRobotDefinition_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Module Identity 
    virtual ::grpc::Status GetNextModulesList(::grpc::ServerContext* context, const ::carbon::frontend::module::GetNextModulesListRequest* request, ::carbon::frontend::module::GetNextModulesListResponse* response);
    virtual ::grpc::Status GetNextActiveModules(::grpc::ServerContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest* request, ::carbon::frontend::module::GetNextActiveModulesResponse* response);
    virtual ::grpc::Status IdentifyModule(::grpc::ServerContext* context, const ::carbon::frontend::module::IdentifyModuleRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status AssignModule(::grpc::ServerContext* context, const ::carbon::frontend::module::AssignModuleRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status ClearModuleAssignment(::grpc::ServerContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status SetModuleSerial(::grpc::ServerContext* context, const ::carbon::frontend::module::SetModuleSerialRequest* request, ::carbon::frontend::util::Empty* response);
    // Robot Definition 
    virtual ::grpc::Status GetPresetsList(::grpc::ServerContext* context, const ::carbon::frontend::module::GetPresetsListRequest* request, ::carbon::frontend::module::GetPresetsListResponse* response);
    virtual ::grpc::Status GetCurrentRobotDefinition(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response);
    virtual ::grpc::Status SetCurrentRobotDefinition(::grpc::ServerContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* request, ::carbon::frontend::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextModulesList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextModulesList() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextModulesList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModulesList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextModulesListRequest* /*request*/, ::carbon::frontend::module::GetNextModulesListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextModulesList(::grpc::ServerContext* context, ::carbon::frontend::module::GetNextModulesListRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::module::GetNextModulesListResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextActiveModules : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextActiveModules() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextActiveModules() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveModules(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextActiveModulesRequest* /*request*/, ::carbon::frontend::module::GetNextActiveModulesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveModules(::grpc::ServerContext* context, ::carbon::frontend::module::GetNextActiveModulesRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::module::GetNextActiveModulesResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_IdentifyModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_IdentifyModule() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_IdentifyModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IdentifyModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::IdentifyModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestIdentifyModule(::grpc::ServerContext* context, ::carbon::frontend::module::IdentifyModuleRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_AssignModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_AssignModule() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_AssignModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AssignModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::AssignModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAssignModule(::grpc::ServerContext* context, ::carbon::frontend::module::AssignModuleRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ClearModuleAssignment : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ClearModuleAssignment() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_ClearModuleAssignment() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ClearModuleAssignment(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::ClearModuleAssignmentRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestClearModuleAssignment(::grpc::ServerContext* context, ::carbon::frontend::module::ClearModuleAssignmentRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetModuleSerial : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetModuleSerial() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_SetModuleSerial() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleSerial(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetModuleSerialRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetModuleSerial(::grpc::ServerContext* context, ::carbon::frontend::module::SetModuleSerialRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetPresetsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetPresetsList() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_GetPresetsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPresetsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetPresetsListRequest* /*request*/, ::carbon::frontend::module::GetPresetsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPresetsList(::grpc::ServerContext* context, ::carbon::frontend::module::GetPresetsListRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::module::GetPresetsListResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_GetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCurrentRobotDefinition(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_SetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetCurrentRobotDefinition(::grpc::ServerContext* context, ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextModulesList<WithAsyncMethod_GetNextActiveModules<WithAsyncMethod_IdentifyModule<WithAsyncMethod_AssignModule<WithAsyncMethod_ClearModuleAssignment<WithAsyncMethod_SetModuleSerial<WithAsyncMethod_GetPresetsList<WithAsyncMethod_GetCurrentRobotDefinition<WithAsyncMethod_SetCurrentRobotDefinition<Service > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextModulesList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextModulesList() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::GetNextModulesListRequest, ::carbon::frontend::module::GetNextModulesListResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::module::GetNextModulesListRequest* request, ::carbon::frontend::module::GetNextModulesListResponse* response) { return this->GetNextModulesList(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextModulesList(
        ::grpc::MessageAllocator< ::carbon::frontend::module::GetNextModulesListRequest, ::carbon::frontend::module::GetNextModulesListResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::GetNextModulesListRequest, ::carbon::frontend::module::GetNextModulesListResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextModulesList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModulesList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextModulesListRequest* /*request*/, ::carbon::frontend::module::GetNextModulesListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextModulesList(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::module::GetNextModulesListRequest* /*request*/, ::carbon::frontend::module::GetNextModulesListResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextActiveModules : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextActiveModules() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::GetNextActiveModulesRequest, ::carbon::frontend::module::GetNextActiveModulesResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest* request, ::carbon::frontend::module::GetNextActiveModulesResponse* response) { return this->GetNextActiveModules(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextActiveModules(
        ::grpc::MessageAllocator< ::carbon::frontend::module::GetNextActiveModulesRequest, ::carbon::frontend::module::GetNextActiveModulesResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::GetNextActiveModulesRequest, ::carbon::frontend::module::GetNextActiveModulesResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextActiveModules() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveModules(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextActiveModulesRequest* /*request*/, ::carbon::frontend::module::GetNextActiveModulesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveModules(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::module::GetNextActiveModulesRequest* /*request*/, ::carbon::frontend::module::GetNextActiveModulesResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_IdentifyModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_IdentifyModule() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::IdentifyModuleRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::module::IdentifyModuleRequest* request, ::carbon::frontend::util::Empty* response) { return this->IdentifyModule(context, request, response); }));}
    void SetMessageAllocatorFor_IdentifyModule(
        ::grpc::MessageAllocator< ::carbon::frontend::module::IdentifyModuleRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::IdentifyModuleRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_IdentifyModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IdentifyModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::IdentifyModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* IdentifyModule(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::module::IdentifyModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_AssignModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_AssignModule() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::AssignModuleRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::module::AssignModuleRequest* request, ::carbon::frontend::util::Empty* response) { return this->AssignModule(context, request, response); }));}
    void SetMessageAllocatorFor_AssignModule(
        ::grpc::MessageAllocator< ::carbon::frontend::module::AssignModuleRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::AssignModuleRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_AssignModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AssignModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::AssignModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AssignModule(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::module::AssignModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ClearModuleAssignment : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ClearModuleAssignment() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::ClearModuleAssignmentRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest* request, ::carbon::frontend::util::Empty* response) { return this->ClearModuleAssignment(context, request, response); }));}
    void SetMessageAllocatorFor_ClearModuleAssignment(
        ::grpc::MessageAllocator< ::carbon::frontend::module::ClearModuleAssignmentRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::ClearModuleAssignmentRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ClearModuleAssignment() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ClearModuleAssignment(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::ClearModuleAssignmentRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ClearModuleAssignment(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::module::ClearModuleAssignmentRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetModuleSerial : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetModuleSerial() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::SetModuleSerialRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::module::SetModuleSerialRequest* request, ::carbon::frontend::util::Empty* response) { return this->SetModuleSerial(context, request, response); }));}
    void SetMessageAllocatorFor_SetModuleSerial(
        ::grpc::MessageAllocator< ::carbon::frontend::module::SetModuleSerialRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::SetModuleSerialRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetModuleSerial() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleSerial(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetModuleSerialRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetModuleSerial(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::module::SetModuleSerialRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetPresetsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetPresetsList() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::GetPresetsListRequest, ::carbon::frontend::module::GetPresetsListResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::module::GetPresetsListRequest* request, ::carbon::frontend::module::GetPresetsListResponse* response) { return this->GetPresetsList(context, request, response); }));}
    void SetMessageAllocatorFor_GetPresetsList(
        ::grpc::MessageAllocator< ::carbon::frontend::module::GetPresetsListRequest, ::carbon::frontend::module::GetPresetsListResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::GetPresetsListRequest, ::carbon::frontend::module::GetPresetsListResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetPresetsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPresetsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetPresetsListRequest* /*request*/, ::carbon::frontend::module::GetPresetsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPresetsList(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::module::GetPresetsListRequest* /*request*/, ::carbon::frontend::module::GetPresetsListResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response) { return this->GetCurrentRobotDefinition(context, request, response); }));}
    void SetMessageAllocatorFor_GetCurrentRobotDefinition(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCurrentRobotDefinition(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::SetCurrentRobotDefinitionRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* request, ::carbon::frontend::util::Empty* response) { return this->SetCurrentRobotDefinition(context, request, response); }));}
    void SetMessageAllocatorFor_SetCurrentRobotDefinition(
        ::grpc::MessageAllocator< ::carbon::frontend::module::SetCurrentRobotDefinitionRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::module::SetCurrentRobotDefinitionRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetCurrentRobotDefinition(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextModulesList<WithCallbackMethod_GetNextActiveModules<WithCallbackMethod_IdentifyModule<WithCallbackMethod_AssignModule<WithCallbackMethod_ClearModuleAssignment<WithCallbackMethod_SetModuleSerial<WithCallbackMethod_GetPresetsList<WithCallbackMethod_GetCurrentRobotDefinition<WithCallbackMethod_SetCurrentRobotDefinition<Service > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextModulesList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextModulesList() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextModulesList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModulesList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextModulesListRequest* /*request*/, ::carbon::frontend::module::GetNextModulesListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextActiveModules : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextActiveModules() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextActiveModules() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveModules(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextActiveModulesRequest* /*request*/, ::carbon::frontend::module::GetNextActiveModulesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_IdentifyModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_IdentifyModule() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_IdentifyModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IdentifyModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::IdentifyModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_AssignModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_AssignModule() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_AssignModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AssignModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::AssignModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ClearModuleAssignment : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ClearModuleAssignment() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_ClearModuleAssignment() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ClearModuleAssignment(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::ClearModuleAssignmentRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetModuleSerial : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetModuleSerial() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_SetModuleSerial() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleSerial(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetModuleSerialRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetPresetsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetPresetsList() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_GetPresetsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPresetsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetPresetsListRequest* /*request*/, ::carbon::frontend::module::GetPresetsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_GetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_SetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextModulesList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextModulesList() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextModulesList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModulesList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextModulesListRequest* /*request*/, ::carbon::frontend::module::GetNextModulesListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextModulesList(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextActiveModules : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextActiveModules() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextActiveModules() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveModules(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextActiveModulesRequest* /*request*/, ::carbon::frontend::module::GetNextActiveModulesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveModules(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_IdentifyModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_IdentifyModule() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_IdentifyModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IdentifyModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::IdentifyModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestIdentifyModule(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_AssignModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_AssignModule() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_AssignModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AssignModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::AssignModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAssignModule(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ClearModuleAssignment : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ClearModuleAssignment() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_ClearModuleAssignment() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ClearModuleAssignment(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::ClearModuleAssignmentRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestClearModuleAssignment(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetModuleSerial : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetModuleSerial() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_SetModuleSerial() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleSerial(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetModuleSerialRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetModuleSerial(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetPresetsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetPresetsList() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_GetPresetsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPresetsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetPresetsListRequest* /*request*/, ::carbon::frontend::module::GetPresetsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPresetsList(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_GetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCurrentRobotDefinition(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_SetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetCurrentRobotDefinition(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextModulesList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextModulesList() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextModulesList(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextModulesList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModulesList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextModulesListRequest* /*request*/, ::carbon::frontend::module::GetNextModulesListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextModulesList(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextActiveModules : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextActiveModules() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextActiveModules(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextActiveModules() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveModules(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextActiveModulesRequest* /*request*/, ::carbon::frontend::module::GetNextActiveModulesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveModules(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_IdentifyModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_IdentifyModule() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->IdentifyModule(context, request, response); }));
    }
    ~WithRawCallbackMethod_IdentifyModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IdentifyModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::IdentifyModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* IdentifyModule(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_AssignModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_AssignModule() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->AssignModule(context, request, response); }));
    }
    ~WithRawCallbackMethod_AssignModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AssignModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::AssignModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AssignModule(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ClearModuleAssignment : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ClearModuleAssignment() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ClearModuleAssignment(context, request, response); }));
    }
    ~WithRawCallbackMethod_ClearModuleAssignment() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ClearModuleAssignment(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::ClearModuleAssignmentRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ClearModuleAssignment(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetModuleSerial : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetModuleSerial() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetModuleSerial(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetModuleSerial() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModuleSerial(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetModuleSerialRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetModuleSerial(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetPresetsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetPresetsList() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetPresetsList(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetPresetsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPresetsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetPresetsListRequest* /*request*/, ::carbon::frontend::module::GetPresetsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPresetsList(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetCurrentRobotDefinition(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCurrentRobotDefinition(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetCurrentRobotDefinition(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetCurrentRobotDefinition(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextModulesList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextModulesList() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::module::GetNextModulesListRequest, ::carbon::frontend::module::GetNextModulesListResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::module::GetNextModulesListRequest, ::carbon::frontend::module::GetNextModulesListResponse>* streamer) {
                       return this->StreamedGetNextModulesList(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextModulesList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextModulesList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextModulesListRequest* /*request*/, ::carbon::frontend::module::GetNextModulesListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextModulesList(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::module::GetNextModulesListRequest,::carbon::frontend::module::GetNextModulesListResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextActiveModules : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextActiveModules() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::module::GetNextActiveModulesRequest, ::carbon::frontend::module::GetNextActiveModulesResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::module::GetNextActiveModulesRequest, ::carbon::frontend::module::GetNextActiveModulesResponse>* streamer) {
                       return this->StreamedGetNextActiveModules(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextActiveModules() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextActiveModules(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetNextActiveModulesRequest* /*request*/, ::carbon::frontend::module::GetNextActiveModulesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextActiveModules(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::module::GetNextActiveModulesRequest,::carbon::frontend::module::GetNextActiveModulesResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_IdentifyModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_IdentifyModule() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::module::IdentifyModuleRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::module::IdentifyModuleRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedIdentifyModule(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_IdentifyModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status IdentifyModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::IdentifyModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedIdentifyModule(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::module::IdentifyModuleRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_AssignModule : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_AssignModule() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::module::AssignModuleRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::module::AssignModuleRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedAssignModule(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_AssignModule() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status AssignModule(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::AssignModuleRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedAssignModule(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::module::AssignModuleRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ClearModuleAssignment : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ClearModuleAssignment() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::module::ClearModuleAssignmentRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::module::ClearModuleAssignmentRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedClearModuleAssignment(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ClearModuleAssignment() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ClearModuleAssignment(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::ClearModuleAssignmentRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedClearModuleAssignment(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::module::ClearModuleAssignmentRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetModuleSerial : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetModuleSerial() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::module::SetModuleSerialRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::module::SetModuleSerialRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetModuleSerial(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetModuleSerial() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetModuleSerial(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetModuleSerialRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetModuleSerial(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::module::SetModuleSerialRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetPresetsList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetPresetsList() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::module::GetPresetsListRequest, ::carbon::frontend::module::GetPresetsListResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::module::GetPresetsListRequest, ::carbon::frontend::module::GetPresetsListResponse>* streamer) {
                       return this->StreamedGetPresetsList(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetPresetsList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetPresetsList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::GetPresetsListRequest* /*request*/, ::carbon::frontend::module::GetPresetsListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetPresetsList(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::module::GetPresetsListRequest,::carbon::frontend::module::GetPresetsListResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>* streamer) {
                       return this->StreamedGetCurrentRobotDefinition(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetCurrentRobotDefinition(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::module::GetCurrentRobotDefinitionResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetCurrentRobotDefinition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetCurrentRobotDefinition() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::module::SetCurrentRobotDefinitionRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::module::SetCurrentRobotDefinitionRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetCurrentRobotDefinition(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetCurrentRobotDefinition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetCurrentRobotDefinition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetCurrentRobotDefinition(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::module::SetCurrentRobotDefinitionRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextModulesList<WithStreamedUnaryMethod_GetNextActiveModules<WithStreamedUnaryMethod_IdentifyModule<WithStreamedUnaryMethod_AssignModule<WithStreamedUnaryMethod_ClearModuleAssignment<WithStreamedUnaryMethod_SetModuleSerial<WithStreamedUnaryMethod_GetPresetsList<WithStreamedUnaryMethod_GetCurrentRobotDefinition<WithStreamedUnaryMethod_SetCurrentRobotDefinition<Service > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextModulesList<WithStreamedUnaryMethod_GetNextActiveModules<WithStreamedUnaryMethod_IdentifyModule<WithStreamedUnaryMethod_AssignModule<WithStreamedUnaryMethod_ClearModuleAssignment<WithStreamedUnaryMethod_SetModuleSerial<WithStreamedUnaryMethod_GetPresetsList<WithStreamedUnaryMethod_GetCurrentRobotDefinition<WithStreamedUnaryMethod_SetCurrentRobotDefinition<Service > > > > > > > > > StreamedService;
};

}  // namespace module
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fmodule_2eproto__INCLUDED

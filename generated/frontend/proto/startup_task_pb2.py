# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/startup_task.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.proto.startup_task import startup_task_pb2 as proto_dot_startup__task_dot_startup__task__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/startup_task.proto',
  package='carbon.frontend.startup_task',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n!frontend/proto/startup_task.proto\x12\x1c\x63\x61rbon.frontend.startup_task\x1a%proto/startup_task/startup_task.proto\x1a\x19\x66rontend/proto/util.proto\"m\n\x14GetNextTasksResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12(\n\x05tasks\x18\x02 \x03(\x0b\x32\x19.carbon.startup_task.Task\"*\n\x17MarkTaskCompleteRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\"\x1a\n\x18MarkTaskCompleteResponse2\xfd\x01\n\x12StartupTaskService\x12\x63\n\x0cGetNextTasks\x12\x1f.carbon.frontend.util.Timestamp\x1a\x32.carbon.frontend.startup_task.GetNextTasksResponse\x12\x81\x01\n\x10MarkTaskComplete\x12\x35.carbon.frontend.startup_task.MarkTaskCompleteRequest\x1a\x36.carbon.frontend.startup_task.MarkTaskCompleteResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[proto_dot_startup__task_dot_startup__task__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_GETNEXTTASKSRESPONSE = _descriptor.Descriptor(
  name='GetNextTasksResponse',
  full_name='carbon.frontend.startup_task.GetNextTasksResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.startup_task.GetNextTasksResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tasks', full_name='carbon.frontend.startup_task.GetNextTasksResponse.tasks', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=133,
  serialized_end=242,
)


_MARKTASKCOMPLETEREQUEST = _descriptor.Descriptor(
  name='MarkTaskCompleteRequest',
  full_name='carbon.frontend.startup_task.MarkTaskCompleteRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='carbon.frontend.startup_task.MarkTaskCompleteRequest.task_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=244,
  serialized_end=286,
)


_MARKTASKCOMPLETERESPONSE = _descriptor.Descriptor(
  name='MarkTaskCompleteResponse',
  full_name='carbon.frontend.startup_task.MarkTaskCompleteResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=288,
  serialized_end=314,
)

_GETNEXTTASKSRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTTASKSRESPONSE.fields_by_name['tasks'].message_type = proto_dot_startup__task_dot_startup__task__pb2._TASK
DESCRIPTOR.message_types_by_name['GetNextTasksResponse'] = _GETNEXTTASKSRESPONSE
DESCRIPTOR.message_types_by_name['MarkTaskCompleteRequest'] = _MARKTASKCOMPLETEREQUEST
DESCRIPTOR.message_types_by_name['MarkTaskCompleteResponse'] = _MARKTASKCOMPLETERESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetNextTasksResponse = _reflection.GeneratedProtocolMessageType('GetNextTasksResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTTASKSRESPONSE,
  '__module__' : 'frontend.proto.startup_task_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.startup_task.GetNextTasksResponse)
  })
_sym_db.RegisterMessage(GetNextTasksResponse)

MarkTaskCompleteRequest = _reflection.GeneratedProtocolMessageType('MarkTaskCompleteRequest', (_message.Message,), {
  'DESCRIPTOR' : _MARKTASKCOMPLETEREQUEST,
  '__module__' : 'frontend.proto.startup_task_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.startup_task.MarkTaskCompleteRequest)
  })
_sym_db.RegisterMessage(MarkTaskCompleteRequest)

MarkTaskCompleteResponse = _reflection.GeneratedProtocolMessageType('MarkTaskCompleteResponse', (_message.Message,), {
  'DESCRIPTOR' : _MARKTASKCOMPLETERESPONSE,
  '__module__' : 'frontend.proto.startup_task_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.startup_task.MarkTaskCompleteResponse)
  })
_sym_db.RegisterMessage(MarkTaskCompleteResponse)


DESCRIPTOR._options = None

_STARTUPTASKSERVICE = _descriptor.ServiceDescriptor(
  name='StartupTaskService',
  full_name='carbon.frontend.startup_task.StartupTaskService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=317,
  serialized_end=570,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextTasks',
    full_name='carbon.frontend.startup_task.StartupTaskService.GetNextTasks',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTTASKSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='MarkTaskComplete',
    full_name='carbon.frontend.startup_task.StartupTaskService.MarkTaskComplete',
    index=1,
    containing_service=None,
    input_type=_MARKTASKCOMPLETEREQUEST,
    output_type=_MARKTASKCOMPLETERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_STARTUPTASKSERVICE)

DESCRIPTOR.services_by_name['StartupTaskService'] = _STARTUPTASKSERVICE

# @@protoc_insertion_point(module_scope)

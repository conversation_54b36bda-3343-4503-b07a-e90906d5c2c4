// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/power.proto

#include "frontend/proto/power.pb.h"
#include "frontend/proto/power.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace power {

static const char* PowerService_method_names[] = {
  "/carbon.frontend.power.PowerService/GetNextPowerStatus",
  "/carbon.frontend.power.PowerService/TurnOffDevice",
  "/carbon.frontend.power.PowerService/TurnOnDevice",
  "/carbon.frontend.power.PowerService/GetNextReaperAllHardwareStatus",
  "/carbon.frontend.power.PowerService/GetNextReaperHardwareStatus",
  "/carbon.frontend.power.PowerService/SetReaperScannerPower",
  "/carbon.frontend.power.PowerService/SetReaperTargetPower",
  "/carbon.frontend.power.PowerService/SetReaperPredictCamPower",
  "/carbon.frontend.power.PowerService/SetReaperStrobeEnable",
  "/carbon.frontend.power.PowerService/SetReaperAllStrobesEnable",
  "/carbon.frontend.power.PowerService/SetReaperModulePcPower",
  "/carbon.frontend.power.PowerService/SetReaperModuleLaserPower",
};

std::unique_ptr< PowerService::Stub> PowerService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< PowerService::Stub> stub(new PowerService::Stub(channel, options));
  return stub;
}

PowerService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextPowerStatus_(PowerService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_TurnOffDevice_(PowerService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_TurnOnDevice_(PowerService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextReaperAllHardwareStatus_(PowerService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextReaperHardwareStatus_(PowerService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperScannerPower_(PowerService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperTargetPower_(PowerService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperPredictCamPower_(PowerService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperStrobeEnable_(PowerService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperAllStrobesEnable_(PowerService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperModulePcPower_(PowerService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperModuleLaserPower_(PowerService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status PowerService::Stub::GetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::carbon::frontend::power::PowerStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::PowerStatusRequest, ::carbon::frontend::power::PowerStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextPowerStatus_, context, request, response);
}

void PowerService::Stub::async::GetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest* request, ::carbon::frontend::power::PowerStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::PowerStatusRequest, ::carbon::frontend::power::PowerStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextPowerStatus_, context, request, response, std::move(f));
}

void PowerService::Stub::async::GetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest* request, ::carbon::frontend::power::PowerStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextPowerStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::PowerStatusResponse>* PowerService::Stub::PrepareAsyncGetNextPowerStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::PowerStatusResponse, ::carbon::frontend::power::PowerStatusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextPowerStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::PowerStatusResponse>* PowerService::Stub::AsyncGetNextPowerStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextPowerStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PowerService::Stub::TurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::carbon::frontend::power::RelayResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_TurnOffDevice_, context, request, response);
}

void PowerService::Stub::async::TurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TurnOffDevice_, context, request, response, std::move(f));
}

void PowerService::Stub::async::TurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TurnOffDevice_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>* PowerService::Stub::PrepareAsyncTurnOffDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::RelayResponse, ::carbon::frontend::power::RelayRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_TurnOffDevice_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>* PowerService::Stub::AsyncTurnOffDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncTurnOffDeviceRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PowerService::Stub::TurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::carbon::frontend::power::RelayResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_TurnOnDevice_, context, request, response);
}

void PowerService::Stub::async::TurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TurnOnDevice_, context, request, response, std::move(f));
}

void PowerService::Stub::async::TurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TurnOnDevice_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>* PowerService::Stub::PrepareAsyncTurnOnDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::RelayResponse, ::carbon::frontend::power::RelayRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_TurnOnDevice_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>* PowerService::Stub::AsyncTurnOnDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncTurnOnDeviceRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PowerService::Stub::GetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextReaperAllHardwareStatus_, context, request, response);
}

void PowerService::Stub::async::GetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextReaperAllHardwareStatus_, context, request, response, std::move(f));
}

void PowerService::Stub::async::GetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextReaperAllHardwareStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>* PowerService::Stub::PrepareAsyncGetNextReaperAllHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse, ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextReaperAllHardwareStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>* PowerService::Stub::AsyncGetNextReaperAllHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextReaperAllHardwareStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PowerService::Stub::GetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::GetNextReaperHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextReaperHardwareStatus_, context, request, response);
}

void PowerService::Stub::async::GetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::GetNextReaperHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextReaperHardwareStatus_, context, request, response, std::move(f));
}

void PowerService::Stub::async::GetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextReaperHardwareStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>* PowerService::Stub::PrepareAsyncGetNextReaperHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse, ::carbon::frontend::power::GetNextReaperHardwareStatusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextReaperHardwareStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>* PowerService::Stub::AsyncGetNextReaperHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextReaperHardwareStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PowerService::Stub::SetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::SetReaperScannerPowerRequest, ::carbon::frontend::power::SetReaperScannerPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperScannerPower_, context, request, response);
}

void PowerService::Stub::async::SetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest* request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::SetReaperScannerPowerRequest, ::carbon::frontend::power::SetReaperScannerPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperScannerPower_, context, request, response, std::move(f));
}

void PowerService::Stub::async::SetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest* request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperScannerPower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperScannerPowerResponse>* PowerService::Stub::PrepareAsyncSetReaperScannerPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::SetReaperScannerPowerResponse, ::carbon::frontend::power::SetReaperScannerPowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperScannerPower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperScannerPowerResponse>* PowerService::Stub::AsyncSetReaperScannerPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperScannerPowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PowerService::Stub::SetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::SetReaperTargetPowerRequest, ::carbon::frontend::power::SetReaperTargetPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperTargetPower_, context, request, response);
}

void PowerService::Stub::async::SetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest* request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::SetReaperTargetPowerRequest, ::carbon::frontend::power::SetReaperTargetPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperTargetPower_, context, request, response, std::move(f));
}

void PowerService::Stub::async::SetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest* request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperTargetPower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperTargetPowerResponse>* PowerService::Stub::PrepareAsyncSetReaperTargetPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::SetReaperTargetPowerResponse, ::carbon::frontend::power::SetReaperTargetPowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperTargetPower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperTargetPowerResponse>* PowerService::Stub::AsyncSetReaperTargetPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperTargetPowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PowerService::Stub::SetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::SetReaperPredictCamPowerRequest, ::carbon::frontend::power::SetReaperPredictCamPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperPredictCamPower_, context, request, response);
}

void PowerService::Stub::async::SetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::SetReaperPredictCamPowerRequest, ::carbon::frontend::power::SetReaperPredictCamPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperPredictCamPower_, context, request, response, std::move(f));
}

void PowerService::Stub::async::SetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperPredictCamPower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>* PowerService::Stub::PrepareAsyncSetReaperPredictCamPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::SetReaperPredictCamPowerResponse, ::carbon::frontend::power::SetReaperPredictCamPowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperPredictCamPower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>* PowerService::Stub::AsyncSetReaperPredictCamPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperPredictCamPowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PowerService::Stub::SetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::SetReaperStrobeEnableRequest, ::carbon::frontend::power::SetReaperStrobeEnableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperStrobeEnable_, context, request, response);
}

void PowerService::Stub::async::SetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::SetReaperStrobeEnableRequest, ::carbon::frontend::power::SetReaperStrobeEnableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperStrobeEnable_, context, request, response, std::move(f));
}

void PowerService::Stub::async::SetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperStrobeEnable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperStrobeEnableResponse>* PowerService::Stub::PrepareAsyncSetReaperStrobeEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::SetReaperStrobeEnableResponse, ::carbon::frontend::power::SetReaperStrobeEnableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperStrobeEnable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperStrobeEnableResponse>* PowerService::Stub::AsyncSetReaperStrobeEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperStrobeEnableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PowerService::Stub::SetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::SetReaperAllStrobesEnableRequest, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperAllStrobesEnable_, context, request, response);
}

void PowerService::Stub::async::SetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::SetReaperAllStrobesEnableRequest, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperAllStrobesEnable_, context, request, response, std::move(f));
}

void PowerService::Stub::async::SetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperAllStrobesEnable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>* PowerService::Stub::PrepareAsyncSetReaperAllStrobesEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse, ::carbon::frontend::power::SetReaperAllStrobesEnableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperAllStrobesEnable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>* PowerService::Stub::AsyncSetReaperAllStrobesEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperAllStrobesEnableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PowerService::Stub::SetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::SetReaperModulePcPowerRequest, ::carbon::frontend::power::SetReaperModulePcPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperModulePcPower_, context, request, response);
}

void PowerService::Stub::async::SetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::SetReaperModulePcPowerRequest, ::carbon::frontend::power::SetReaperModulePcPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperModulePcPower_, context, request, response, std::move(f));
}

void PowerService::Stub::async::SetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperModulePcPower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModulePcPowerResponse>* PowerService::Stub::PrepareAsyncSetReaperModulePcPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::SetReaperModulePcPowerResponse, ::carbon::frontend::power::SetReaperModulePcPowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperModulePcPower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModulePcPowerResponse>* PowerService::Stub::AsyncSetReaperModulePcPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperModulePcPowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PowerService::Stub::SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::power::SetReaperModuleLaserPowerRequest, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperModuleLaserPower_, context, request, response);
}

void PowerService::Stub::async::SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::power::SetReaperModuleLaserPowerRequest, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperModuleLaserPower_, context, request, response, std::move(f));
}

void PowerService::Stub::async::SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperModuleLaserPower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>* PowerService::Stub::PrepareAsyncSetReaperModuleLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse, ::carbon::frontend::power::SetReaperModuleLaserPowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperModuleLaserPower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>* PowerService::Stub::AsyncSetReaperModuleLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperModuleLaserPowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

PowerService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::PowerStatusRequest, ::carbon::frontend::power::PowerStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::PowerStatusRequest* req,
             ::carbon::frontend::power::PowerStatusResponse* resp) {
               return service->GetNextPowerStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::RelayRequest* req,
             ::carbon::frontend::power::RelayResponse* resp) {
               return service->TurnOffDevice(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::RelayRequest* req,
             ::carbon::frontend::power::RelayResponse* resp) {
               return service->TurnOnDevice(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* req,
             ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* resp) {
               return service->GetNextReaperAllHardwareStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::GetNextReaperHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* req,
             ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* resp) {
               return service->GetNextReaperHardwareStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::SetReaperScannerPowerRequest, ::carbon::frontend::power::SetReaperScannerPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::SetReaperScannerPowerRequest* req,
             ::carbon::frontend::power::SetReaperScannerPowerResponse* resp) {
               return service->SetReaperScannerPower(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::SetReaperTargetPowerRequest, ::carbon::frontend::power::SetReaperTargetPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::SetReaperTargetPowerRequest* req,
             ::carbon::frontend::power::SetReaperTargetPowerResponse* resp) {
               return service->SetReaperTargetPower(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::SetReaperPredictCamPowerRequest, ::carbon::frontend::power::SetReaperPredictCamPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* req,
             ::carbon::frontend::power::SetReaperPredictCamPowerResponse* resp) {
               return service->SetReaperPredictCamPower(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::SetReaperStrobeEnableRequest, ::carbon::frontend::power::SetReaperStrobeEnableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::SetReaperStrobeEnableRequest* req,
             ::carbon::frontend::power::SetReaperStrobeEnableResponse* resp) {
               return service->SetReaperStrobeEnable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::SetReaperAllStrobesEnableRequest, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* req,
             ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* resp) {
               return service->SetReaperAllStrobesEnable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::SetReaperModulePcPowerRequest, ::carbon::frontend::power::SetReaperModulePcPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::SetReaperModulePcPowerRequest* req,
             ::carbon::frontend::power::SetReaperModulePcPowerResponse* resp) {
               return service->SetReaperModulePcPower(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PowerService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PowerService::Service, ::carbon::frontend::power::SetReaperModuleLaserPowerRequest, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PowerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* req,
             ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* resp) {
               return service->SetReaperModuleLaserPower(ctx, req, resp);
             }, this)));
}

PowerService::Service::~Service() {
}

::grpc::Status PowerService::Service::GetNextPowerStatus(::grpc::ServerContext* context, const ::carbon::frontend::power::PowerStatusRequest* request, ::carbon::frontend::power::PowerStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PowerService::Service::TurnOffDevice(::grpc::ServerContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PowerService::Service::TurnOnDevice(::grpc::ServerContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PowerService::Service::GetNextReaperAllHardwareStatus(::grpc::ServerContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PowerService::Service::GetNextReaperHardwareStatus(::grpc::ServerContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PowerService::Service::SetReaperScannerPower(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest* request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PowerService::Service::SetReaperTargetPower(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest* request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PowerService::Service::SetReaperPredictCamPower(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PowerService::Service::SetReaperStrobeEnable(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PowerService::Service::SetReaperAllStrobesEnable(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PowerService::Service::SetReaperModulePcPower(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PowerService::Service::SetReaperModuleLaserPower(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace power


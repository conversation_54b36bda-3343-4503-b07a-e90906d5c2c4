"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class TargetFocusState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    liquid_lens_value: builtin___int = ...
    focus_progress_pct: builtin___float = ...
    max_lens_value: builtin___int = ...
    min_lens_value: builtin___int = ...
    focus_in_progress: builtin___bool = ...

    def __init__(self,
        *,
        liquid_lens_value : typing___Optional[builtin___int] = None,
        focus_progress_pct : typing___Optional[builtin___float] = None,
        max_lens_value : typing___Optional[builtin___int] = None,
        min_lens_value : typing___Optional[builtin___int] = None,
        focus_in_progress : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"focus_in_progress",b"focus_in_progress",u"focus_progress_pct",b"focus_progress_pct",u"liquid_lens_value",b"liquid_lens_value",u"max_lens_value",b"max_lens_value",u"min_lens_value",b"min_lens_value"]) -> None: ...
type___TargetFocusState = TargetFocusState

class PredictFocusState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___PredictFocusState = PredictFocusState

class FocusState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    global_focus_progress_pct: builtin___float = ...
    grid_view_enabled: builtin___bool = ...
    focus_in_progress: builtin___bool = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def target(self) -> type___TargetFocusState: ...

    @property
    def predict(self) -> type___PredictFocusState: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        target : typing___Optional[type___TargetFocusState] = None,
        predict : typing___Optional[type___PredictFocusState] = None,
        global_focus_progress_pct : typing___Optional[builtin___float] = None,
        grid_view_enabled : typing___Optional[builtin___bool] = None,
        focus_in_progress : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"predict",b"predict",u"target",b"target",u"ts",b"ts",u"type_state",b"type_state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"focus_in_progress",b"focus_in_progress",u"global_focus_progress_pct",b"global_focus_progress_pct",u"grid_view_enabled",b"grid_view_enabled",u"predict",b"predict",u"target",b"target",u"ts",b"ts",u"type_state",b"type_state"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"type_state",b"type_state"]) -> typing_extensions___Literal["target","predict"]: ...
type___FocusState = FocusState

class LensSetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    lens_value: builtin___int = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        lens_value : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"lens_value",b"lens_value"]) -> None: ...
type___LensSetRequest = LensSetRequest

class FocusStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"ts",b"ts"]) -> None: ...
type___FocusStateRequest = FocusStateRequest

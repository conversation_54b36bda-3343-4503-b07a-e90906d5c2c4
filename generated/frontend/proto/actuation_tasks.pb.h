// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/actuation_tasks.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2factuation_5ftasks_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2factuation_5ftasks_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "core/controls/exterminator/controllers/aimbot/process/proto/aimbot.pb.h"
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2factuation_5ftasks_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2factuation_5ftasks_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto;
namespace carbon {
namespace frontend {
namespace actuation_tasks {
class GlobalActuationTaskState;
struct GlobalActuationTaskStateDefaultTypeInternal;
extern GlobalActuationTaskStateDefaultTypeInternal _GlobalActuationTaskState_default_instance_;
class GlobalAimbotActuationTaskRequest;
struct GlobalAimbotActuationTaskRequestDefaultTypeInternal;
extern GlobalAimbotActuationTaskRequestDefaultTypeInternal _GlobalAimbotActuationTaskRequest_default_instance_;
}  // namespace actuation_tasks
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* Arena::CreateMaybeMessage<::carbon::frontend::actuation_tasks::GlobalActuationTaskState>(Arena*);
template<> ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* Arena::CreateMaybeMessage<::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace actuation_tasks {

// ===================================================================

class GlobalAimbotActuationTaskRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest) */ {
 public:
  inline GlobalAimbotActuationTaskRequest() : GlobalAimbotActuationTaskRequest(nullptr) {}
  ~GlobalAimbotActuationTaskRequest() override;
  explicit constexpr GlobalAimbotActuationTaskRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GlobalAimbotActuationTaskRequest(const GlobalAimbotActuationTaskRequest& from);
  GlobalAimbotActuationTaskRequest(GlobalAimbotActuationTaskRequest&& from) noexcept
    : GlobalAimbotActuationTaskRequest() {
    *this = ::std::move(from);
  }

  inline GlobalAimbotActuationTaskRequest& operator=(const GlobalAimbotActuationTaskRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GlobalAimbotActuationTaskRequest& operator=(GlobalAimbotActuationTaskRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GlobalAimbotActuationTaskRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GlobalAimbotActuationTaskRequest* internal_default_instance() {
    return reinterpret_cast<const GlobalAimbotActuationTaskRequest*>(
               &_GlobalAimbotActuationTaskRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GlobalAimbotActuationTaskRequest& a, GlobalAimbotActuationTaskRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GlobalAimbotActuationTaskRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GlobalAimbotActuationTaskRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GlobalAimbotActuationTaskRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GlobalAimbotActuationTaskRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GlobalAimbotActuationTaskRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GlobalAimbotActuationTaskRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GlobalAimbotActuationTaskRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest";
  }
  protected:
  explicit GlobalAimbotActuationTaskRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTaskFieldNumber = 2,
    kRowIdFieldNumber = 1,
  };
  // .aimbot.ActuationTaskRequest task = 2;
  bool has_task() const;
  private:
  bool _internal_has_task() const;
  public:
  void clear_task();
  const ::aimbot::ActuationTaskRequest& task() const;
  PROTOBUF_NODISCARD ::aimbot::ActuationTaskRequest* release_task();
  ::aimbot::ActuationTaskRequest* mutable_task();
  void set_allocated_task(::aimbot::ActuationTaskRequest* task);
  private:
  const ::aimbot::ActuationTaskRequest& _internal_task() const;
  ::aimbot::ActuationTaskRequest* _internal_mutable_task();
  public:
  void unsafe_arena_set_allocated_task(
      ::aimbot::ActuationTaskRequest* task);
  ::aimbot::ActuationTaskRequest* unsafe_arena_release_task();

  // uint32 row_id = 1;
  void clear_row_id();
  uint32_t row_id() const;
  void set_row_id(uint32_t value);
  private:
  uint32_t _internal_row_id() const;
  void _internal_set_row_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::aimbot::ActuationTaskRequest* task_;
  uint32_t row_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2factuation_5ftasks_2eproto;
};
// -------------------------------------------------------------------

class GlobalActuationTaskState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.actuation_tasks.GlobalActuationTaskState) */ {
 public:
  inline GlobalActuationTaskState() : GlobalActuationTaskState(nullptr) {}
  ~GlobalActuationTaskState() override;
  explicit constexpr GlobalActuationTaskState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GlobalActuationTaskState(const GlobalActuationTaskState& from);
  GlobalActuationTaskState(GlobalActuationTaskState&& from) noexcept
    : GlobalActuationTaskState() {
    *this = ::std::move(from);
  }

  inline GlobalActuationTaskState& operator=(const GlobalActuationTaskState& from) {
    CopyFrom(from);
    return *this;
  }
  inline GlobalActuationTaskState& operator=(GlobalActuationTaskState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GlobalActuationTaskState& default_instance() {
    return *internal_default_instance();
  }
  static inline const GlobalActuationTaskState* internal_default_instance() {
    return reinterpret_cast<const GlobalActuationTaskState*>(
               &_GlobalActuationTaskState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GlobalActuationTaskState& a, GlobalActuationTaskState& b) {
    a.Swap(&b);
  }
  inline void Swap(GlobalActuationTaskState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GlobalActuationTaskState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GlobalActuationTaskState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GlobalActuationTaskState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GlobalActuationTaskState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GlobalActuationTaskState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GlobalActuationTaskState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.actuation_tasks.GlobalActuationTaskState";
  }
  protected:
  explicit GlobalActuationTaskState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kRunningFieldNumber = 2,
    kElapsedTimeMsFieldNumber = 3,
    kExpectedTimeMsFieldNumber = 4,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // bool running = 2;
  void clear_running();
  bool running() const;
  void set_running(bool value);
  private:
  bool _internal_running() const;
  void _internal_set_running(bool value);
  public:

  // uint32 elapsed_time_ms = 3;
  void clear_elapsed_time_ms();
  uint32_t elapsed_time_ms() const;
  void set_elapsed_time_ms(uint32_t value);
  private:
  uint32_t _internal_elapsed_time_ms() const;
  void _internal_set_elapsed_time_ms(uint32_t value);
  public:

  // uint32 expected_time_ms = 4;
  void clear_expected_time_ms();
  uint32_t expected_time_ms() const;
  void set_expected_time_ms(uint32_t value);
  private:
  uint32_t _internal_expected_time_ms() const;
  void _internal_set_expected_time_ms(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.actuation_tasks.GlobalActuationTaskState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  bool running_;
  uint32_t elapsed_time_ms_;
  uint32_t expected_time_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2factuation_5ftasks_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GlobalAimbotActuationTaskRequest

// uint32 row_id = 1;
inline void GlobalAimbotActuationTaskRequest::clear_row_id() {
  row_id_ = 0u;
}
inline uint32_t GlobalAimbotActuationTaskRequest::_internal_row_id() const {
  return row_id_;
}
inline uint32_t GlobalAimbotActuationTaskRequest::row_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest.row_id)
  return _internal_row_id();
}
inline void GlobalAimbotActuationTaskRequest::_internal_set_row_id(uint32_t value) {
  
  row_id_ = value;
}
inline void GlobalAimbotActuationTaskRequest::set_row_id(uint32_t value) {
  _internal_set_row_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest.row_id)
}

// .aimbot.ActuationTaskRequest task = 2;
inline bool GlobalAimbotActuationTaskRequest::_internal_has_task() const {
  return this != internal_default_instance() && task_ != nullptr;
}
inline bool GlobalAimbotActuationTaskRequest::has_task() const {
  return _internal_has_task();
}
inline const ::aimbot::ActuationTaskRequest& GlobalAimbotActuationTaskRequest::_internal_task() const {
  const ::aimbot::ActuationTaskRequest* p = task_;
  return p != nullptr ? *p : reinterpret_cast<const ::aimbot::ActuationTaskRequest&>(
      ::aimbot::_ActuationTaskRequest_default_instance_);
}
inline const ::aimbot::ActuationTaskRequest& GlobalAimbotActuationTaskRequest::task() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest.task)
  return _internal_task();
}
inline void GlobalAimbotActuationTaskRequest::unsafe_arena_set_allocated_task(
    ::aimbot::ActuationTaskRequest* task) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(task_);
  }
  task_ = task;
  if (task) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest.task)
}
inline ::aimbot::ActuationTaskRequest* GlobalAimbotActuationTaskRequest::release_task() {
  
  ::aimbot::ActuationTaskRequest* temp = task_;
  task_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::aimbot::ActuationTaskRequest* GlobalAimbotActuationTaskRequest::unsafe_arena_release_task() {
  // @@protoc_insertion_point(field_release:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest.task)
  
  ::aimbot::ActuationTaskRequest* temp = task_;
  task_ = nullptr;
  return temp;
}
inline ::aimbot::ActuationTaskRequest* GlobalAimbotActuationTaskRequest::_internal_mutable_task() {
  
  if (task_ == nullptr) {
    auto* p = CreateMaybeMessage<::aimbot::ActuationTaskRequest>(GetArenaForAllocation());
    task_ = p;
  }
  return task_;
}
inline ::aimbot::ActuationTaskRequest* GlobalAimbotActuationTaskRequest::mutable_task() {
  ::aimbot::ActuationTaskRequest* _msg = _internal_mutable_task();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest.task)
  return _msg;
}
inline void GlobalAimbotActuationTaskRequest::set_allocated_task(::aimbot::ActuationTaskRequest* task) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(task_);
  }
  if (task) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(task));
    if (message_arena != submessage_arena) {
      task = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, task, submessage_arena);
    }
    
  } else {
    
  }
  task_ = task;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest.task)
}

// -------------------------------------------------------------------

// GlobalActuationTaskState

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GlobalActuationTaskState::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GlobalActuationTaskState::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GlobalActuationTaskState::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GlobalActuationTaskState::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.actuation_tasks.GlobalActuationTaskState.ts)
  return _internal_ts();
}
inline void GlobalActuationTaskState::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.actuation_tasks.GlobalActuationTaskState.ts)
}
inline ::carbon::frontend::util::Timestamp* GlobalActuationTaskState::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GlobalActuationTaskState::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.actuation_tasks.GlobalActuationTaskState.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GlobalActuationTaskState::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GlobalActuationTaskState::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.actuation_tasks.GlobalActuationTaskState.ts)
  return _msg;
}
inline void GlobalActuationTaskState::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.actuation_tasks.GlobalActuationTaskState.ts)
}

// bool running = 2;
inline void GlobalActuationTaskState::clear_running() {
  running_ = false;
}
inline bool GlobalActuationTaskState::_internal_running() const {
  return running_;
}
inline bool GlobalActuationTaskState::running() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.actuation_tasks.GlobalActuationTaskState.running)
  return _internal_running();
}
inline void GlobalActuationTaskState::_internal_set_running(bool value) {
  
  running_ = value;
}
inline void GlobalActuationTaskState::set_running(bool value) {
  _internal_set_running(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.actuation_tasks.GlobalActuationTaskState.running)
}

// uint32 elapsed_time_ms = 3;
inline void GlobalActuationTaskState::clear_elapsed_time_ms() {
  elapsed_time_ms_ = 0u;
}
inline uint32_t GlobalActuationTaskState::_internal_elapsed_time_ms() const {
  return elapsed_time_ms_;
}
inline uint32_t GlobalActuationTaskState::elapsed_time_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.actuation_tasks.GlobalActuationTaskState.elapsed_time_ms)
  return _internal_elapsed_time_ms();
}
inline void GlobalActuationTaskState::_internal_set_elapsed_time_ms(uint32_t value) {
  
  elapsed_time_ms_ = value;
}
inline void GlobalActuationTaskState::set_elapsed_time_ms(uint32_t value) {
  _internal_set_elapsed_time_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.actuation_tasks.GlobalActuationTaskState.elapsed_time_ms)
}

// uint32 expected_time_ms = 4;
inline void GlobalActuationTaskState::clear_expected_time_ms() {
  expected_time_ms_ = 0u;
}
inline uint32_t GlobalActuationTaskState::_internal_expected_time_ms() const {
  return expected_time_ms_;
}
inline uint32_t GlobalActuationTaskState::expected_time_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.actuation_tasks.GlobalActuationTaskState.expected_time_ms)
  return _internal_expected_time_ms();
}
inline void GlobalActuationTaskState::_internal_set_expected_time_ms(uint32_t value) {
  
  expected_time_ms_ = value;
}
inline void GlobalActuationTaskState::set_expected_time_ms(uint32_t value) {
  _internal_set_expected_time_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.actuation_tasks.GlobalActuationTaskState.expected_time_ms)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace actuation_tasks
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2factuation_5ftasks_2eproto

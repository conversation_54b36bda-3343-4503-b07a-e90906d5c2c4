// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/plant_captcha.proto
#ifndef GRPC_frontend_2fproto_2fplant_5fcaptcha_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fplant_5fcaptcha_2eproto__INCLUDED

#include "frontend/proto/plant_captcha.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace plant_captcha {

class PlantCaptchaService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.plant_captcha.PlantCaptchaService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status StartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>> AsyncStartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>>(AsyncStartPlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>> PrepareAsyncStartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>>(PrepareAsyncStartPlantCaptchaRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>> AsyncGetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>>(AsyncGetNextPlantCaptchaStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>> PrepareAsyncGetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>>(PrepareAsyncGetNextPlantCaptchaStatusRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>> AsyncGetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>>(AsyncGetNextPlantCaptchasListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>> PrepareAsyncGetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>>(PrepareAsyncGetNextPlantCaptchasListRaw(context, request, cq));
    }
    virtual ::grpc::Status DeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncDeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncDeletePlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncDeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncDeletePlantCaptchaRaw(context, request, cq));
    }
    virtual ::grpc::Status GetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>> AsyncGetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>>(AsyncGetPlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>> PrepareAsyncGetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>>(PrepareAsyncGetPlantCaptchaRaw(context, request, cq));
    }
    virtual ::grpc::Status CancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncCancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncCancelPlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncCancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncCancelPlantCaptchaRaw(context, request, cq));
    }
    virtual ::grpc::Status StartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncStartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncStartPlantCaptchaUploadRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncStartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncStartPlantCaptchaUploadRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>> AsyncGetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>>(AsyncGetNextPlantCaptchaUploadStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>> PrepareAsyncGetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>>(PrepareAsyncGetNextPlantCaptchaUploadStateRaw(context, request, cq));
    }
    virtual ::grpc::Status SubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSubmitPlantCaptchaResultsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSubmitPlantCaptchaResultsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>> AsyncGetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>>(AsyncGetPlantCaptchaItemResultsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>> PrepareAsyncGetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>>(PrepareAsyncGetPlantCaptchaItemResultsRaw(context, request, cq));
    }
    virtual ::grpc::Status CalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>> AsyncCalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>>(AsyncCalculatePlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>> PrepareAsyncCalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>>(PrepareAsyncCalculatePlantCaptchaRaw(context, request, cq));
    }
    virtual ::grpc::Status GetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>> AsyncGetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>>(AsyncGetOriginalModelinatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>> PrepareAsyncGetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>>(PrepareAsyncGetOriginalModelinatorConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status GetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>> AsyncGetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>>(AsyncGetCaptchaRowStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>> PrepareAsyncGetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>>(PrepareAsyncGetCaptchaRowStatusRaw(context, request, cq));
    }
    virtual ::grpc::Status CancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncCancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncCancelPlantCaptchaOnRowRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncCancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncCancelPlantCaptchaOnRowRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void StartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void CancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void CalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void CancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>* AsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>* PrepareAsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>* AsyncGetNextPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>* PrepareAsyncGetNextPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>* AsyncGetNextPlantCaptchasListRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>* PrepareAsyncGetNextPlantCaptchasListRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncDeletePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncDeletePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>* AsyncGetPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>* PrepareAsyncGetPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncStartPlantCaptchaUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncStartPlantCaptchaUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>* AsyncGetNextPlantCaptchaUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>* PrepareAsyncGetNextPlantCaptchaUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSubmitPlantCaptchaResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSubmitPlantCaptchaResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>* AsyncGetPlantCaptchaItemResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>* PrepareAsyncGetPlantCaptchaItemResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>* AsyncCalculatePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>* PrepareAsyncCalculatePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>* AsyncGetOriginalModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>* PrepareAsyncGetOriginalModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>* AsyncGetCaptchaRowStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>* PrepareAsyncGetCaptchaRowStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncCancelPlantCaptchaOnRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncCancelPlantCaptchaOnRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status StartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>> AsyncStartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>>(AsyncStartPlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>> PrepareAsyncStartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>>(PrepareAsyncStartPlantCaptchaRaw(context, request, cq));
    }
    ::grpc::Status GetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>> AsyncGetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>>(AsyncGetNextPlantCaptchaStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>> PrepareAsyncGetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>>(PrepareAsyncGetNextPlantCaptchaStatusRaw(context, request, cq));
    }
    ::grpc::Status GetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>> AsyncGetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>>(AsyncGetNextPlantCaptchasListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>> PrepareAsyncGetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>>(PrepareAsyncGetNextPlantCaptchasListRaw(context, request, cq));
    }
    ::grpc::Status DeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncDeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncDeletePlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncDeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncDeletePlantCaptchaRaw(context, request, cq));
    }
    ::grpc::Status GetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>> AsyncGetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>>(AsyncGetPlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>> PrepareAsyncGetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>>(PrepareAsyncGetPlantCaptchaRaw(context, request, cq));
    }
    ::grpc::Status CancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncCancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncCancelPlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncCancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncCancelPlantCaptchaRaw(context, request, cq));
    }
    ::grpc::Status StartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncStartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncStartPlantCaptchaUploadRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncStartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncStartPlantCaptchaUploadRaw(context, request, cq));
    }
    ::grpc::Status GetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>> AsyncGetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>>(AsyncGetNextPlantCaptchaUploadStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>> PrepareAsyncGetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>>(PrepareAsyncGetNextPlantCaptchaUploadStateRaw(context, request, cq));
    }
    ::grpc::Status SubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSubmitPlantCaptchaResultsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSubmitPlantCaptchaResultsRaw(context, request, cq));
    }
    ::grpc::Status GetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>> AsyncGetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>>(AsyncGetPlantCaptchaItemResultsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>> PrepareAsyncGetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>>(PrepareAsyncGetPlantCaptchaItemResultsRaw(context, request, cq));
    }
    ::grpc::Status CalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>> AsyncCalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>>(AsyncCalculatePlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>> PrepareAsyncCalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>>(PrepareAsyncCalculatePlantCaptchaRaw(context, request, cq));
    }
    ::grpc::Status GetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>> AsyncGetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>>(AsyncGetOriginalModelinatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>> PrepareAsyncGetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>>(PrepareAsyncGetOriginalModelinatorConfigRaw(context, request, cq));
    }
    ::grpc::Status GetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>> AsyncGetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>>(AsyncGetCaptchaRowStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>> PrepareAsyncGetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>>(PrepareAsyncGetCaptchaRowStatusRaw(context, request, cq));
    }
    ::grpc::Status CancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncCancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncCancelPlantCaptchaOnRowRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncCancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncCancelPlantCaptchaOnRowRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void StartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response, std::function<void(::grpc::Status)>) override;
      void StartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void DeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response, std::function<void(::grpc::Status)>) override;
      void GetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void CancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void CancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void CalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response, std::function<void(::grpc::Status)>) override;
      void CalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void GetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response, std::function<void(::grpc::Status)>) override;
      void GetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void CancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void CancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>* AsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>* PrepareAsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>* AsyncGetNextPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>* PrepareAsyncGetNextPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>* AsyncGetNextPlantCaptchasListRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>* PrepareAsyncGetNextPlantCaptchasListRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncDeletePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncDeletePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>* AsyncGetPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>* PrepareAsyncGetPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncStartPlantCaptchaUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncStartPlantCaptchaUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>* AsyncGetNextPlantCaptchaUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>* PrepareAsyncGetNextPlantCaptchaUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSubmitPlantCaptchaResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSubmitPlantCaptchaResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>* AsyncGetPlantCaptchaItemResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>* PrepareAsyncGetPlantCaptchaItemResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>* AsyncCalculatePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>* PrepareAsyncCalculatePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>* AsyncGetOriginalModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>* PrepareAsyncGetOriginalModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>* AsyncGetCaptchaRowStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>* PrepareAsyncGetCaptchaRowStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncCancelPlantCaptchaOnRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncCancelPlantCaptchaOnRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_StartPlantCaptcha_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextPlantCaptchaStatus_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextPlantCaptchasList_;
    const ::grpc::internal::RpcMethod rpcmethod_DeletePlantCaptcha_;
    const ::grpc::internal::RpcMethod rpcmethod_GetPlantCaptcha_;
    const ::grpc::internal::RpcMethod rpcmethod_CancelPlantCaptcha_;
    const ::grpc::internal::RpcMethod rpcmethod_StartPlantCaptchaUpload_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextPlantCaptchaUploadState_;
    const ::grpc::internal::RpcMethod rpcmethod_SubmitPlantCaptchaResults_;
    const ::grpc::internal::RpcMethod rpcmethod_GetPlantCaptchaItemResults_;
    const ::grpc::internal::RpcMethod rpcmethod_CalculatePlantCaptcha_;
    const ::grpc::internal::RpcMethod rpcmethod_GetOriginalModelinatorConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_GetCaptchaRowStatus_;
    const ::grpc::internal::RpcMethod rpcmethod_CancelPlantCaptchaOnRow_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response);
    virtual ::grpc::Status GetNextPlantCaptchaStatus(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response);
    virtual ::grpc::Status GetNextPlantCaptchasList(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response);
    virtual ::grpc::Status DeletePlantCaptcha(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetPlantCaptcha(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response);
    virtual ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status StartPlantCaptchaUpload(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextPlantCaptchaUploadState(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response);
    virtual ::grpc::Status SubmitPlantCaptchaResults(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetPlantCaptchaItemResults(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response);
    virtual ::grpc::Status CalculatePlantCaptcha(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response);
    virtual ::grpc::Status GetOriginalModelinatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response);
    virtual ::grpc::Status GetCaptchaRowStatus(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response);
    virtual ::grpc::Status CancelPlantCaptchaOnRow(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* request, ::carbon::frontend::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartPlantCaptcha(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextPlantCaptchaStatus(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextPlantCaptchasList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextPlantCaptchasList() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetNextPlantCaptchasList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchasList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextPlantCaptchasList(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DeletePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DeletePlantCaptcha() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_DeletePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeletePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeletePlantCaptcha(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetPlantCaptcha() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_GetPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPlantCaptcha(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCancelPlantCaptcha(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StartPlantCaptchaUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartPlantCaptchaUpload() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_StartPlantCaptchaUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptchaUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartPlantCaptchaUpload(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextPlantCaptchaUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextPlantCaptchaUploadState() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_GetNextPlantCaptchaUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchaUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextPlantCaptchaUploadState(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SubmitPlantCaptchaResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SubmitPlantCaptchaResults() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_SubmitPlantCaptchaResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SubmitPlantCaptchaResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSubmitPlantCaptchaResults(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetPlantCaptchaItemResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetPlantCaptchaItemResults() {
      ::grpc::Service::MarkMethodAsync(9);
    }
    ~WithAsyncMethod_GetPlantCaptchaItemResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptchaItemResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPlantCaptchaItemResults(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CalculatePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CalculatePlantCaptcha() {
      ::grpc::Service::MarkMethodAsync(10);
    }
    ~WithAsyncMethod_CalculatePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CalculatePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCalculatePlantCaptcha(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetOriginalModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetOriginalModelinatorConfig() {
      ::grpc::Service::MarkMethodAsync(11);
    }
    ~WithAsyncMethod_GetOriginalModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetOriginalModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* /*request*/, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetOriginalModelinatorConfig(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetCaptchaRowStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetCaptchaRowStatus() {
      ::grpc::Service::MarkMethodAsync(12);
    }
    ~WithAsyncMethod_GetCaptchaRowStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCaptchaRowStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCaptchaRowStatus(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CancelPlantCaptchaOnRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CancelPlantCaptchaOnRow() {
      ::grpc::Service::MarkMethodAsync(13);
    }
    ~WithAsyncMethod_CancelPlantCaptchaOnRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptchaOnRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCancelPlantCaptchaOnRow(::grpc::ServerContext* context, ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_StartPlantCaptcha<WithAsyncMethod_GetNextPlantCaptchaStatus<WithAsyncMethod_GetNextPlantCaptchasList<WithAsyncMethod_DeletePlantCaptcha<WithAsyncMethod_GetPlantCaptcha<WithAsyncMethod_CancelPlantCaptcha<WithAsyncMethod_StartPlantCaptchaUpload<WithAsyncMethod_GetNextPlantCaptchaUploadState<WithAsyncMethod_SubmitPlantCaptchaResults<WithAsyncMethod_GetPlantCaptchaItemResults<WithAsyncMethod_CalculatePlantCaptcha<WithAsyncMethod_GetOriginalModelinatorConfig<WithAsyncMethod_GetCaptchaRowStatus<WithAsyncMethod_CancelPlantCaptchaOnRow<Service > > > > > > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response) { return this->StartPlantCaptcha(context, request, response); }));}
    void SetMessageAllocatorFor_StartPlantCaptcha(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartPlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response) { return this->GetNextPlantCaptchaStatus(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextPlantCaptchaStatus(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextPlantCaptchaStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextPlantCaptchasList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextPlantCaptchasList() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response) { return this->GetNextPlantCaptchasList(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextPlantCaptchasList(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextPlantCaptchasList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchasList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextPlantCaptchasList(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DeletePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DeletePlantCaptcha() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* request, ::carbon::frontend::util::Empty* response) { return this->DeletePlantCaptcha(context, request, response); }));}
    void SetMessageAllocatorFor_DeletePlantCaptcha(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DeletePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeletePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeletePlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetPlantCaptcha() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response) { return this->GetPlantCaptcha(context, request, response); }));}
    void SetMessageAllocatorFor_GetPlantCaptcha(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->CancelPlantCaptcha(context, request, response); }));}
    void SetMessageAllocatorFor_CancelPlantCaptcha(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CancelPlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StartPlantCaptchaUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartPlantCaptchaUpload() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* request, ::carbon::frontend::util::Empty* response) { return this->StartPlantCaptchaUpload(context, request, response); }));}
    void SetMessageAllocatorFor_StartPlantCaptchaUpload(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartPlantCaptchaUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptchaUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartPlantCaptchaUpload(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextPlantCaptchaUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextPlantCaptchaUploadState() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response) { return this->GetNextPlantCaptchaUploadState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextPlantCaptchaUploadState(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextPlantCaptchaUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchaUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextPlantCaptchaUploadState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SubmitPlantCaptchaResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SubmitPlantCaptchaResults() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* request, ::carbon::frontend::util::Empty* response) { return this->SubmitPlantCaptchaResults(context, request, response); }));}
    void SetMessageAllocatorFor_SubmitPlantCaptchaResults(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SubmitPlantCaptchaResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SubmitPlantCaptchaResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SubmitPlantCaptchaResults(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetPlantCaptchaItemResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetPlantCaptchaItemResults() {
      ::grpc::Service::MarkMethodCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response) { return this->GetPlantCaptchaItemResults(context, request, response); }));}
    void SetMessageAllocatorFor_GetPlantCaptchaItemResults(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(9);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetPlantCaptchaItemResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptchaItemResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPlantCaptchaItemResults(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_CalculatePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_CalculatePlantCaptcha() {
      ::grpc::Service::MarkMethodCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response) { return this->CalculatePlantCaptcha(context, request, response); }));}
    void SetMessageAllocatorFor_CalculatePlantCaptcha(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(10);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_CalculatePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CalculatePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CalculatePlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetOriginalModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetOriginalModelinatorConfig() {
      ::grpc::Service::MarkMethodCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response) { return this->GetOriginalModelinatorConfig(context, request, response); }));}
    void SetMessageAllocatorFor_GetOriginalModelinatorConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(11);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetOriginalModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetOriginalModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* /*request*/, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetOriginalModelinatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* /*request*/, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetCaptchaRowStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetCaptchaRowStatus() {
      ::grpc::Service::MarkMethodCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response) { return this->GetCaptchaRowStatus(context, request, response); }));}
    void SetMessageAllocatorFor_GetCaptchaRowStatus(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(12);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetCaptchaRowStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCaptchaRowStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCaptchaRowStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_CancelPlantCaptchaOnRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_CancelPlantCaptchaOnRow() {
      ::grpc::Service::MarkMethodCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* request, ::carbon::frontend::util::Empty* response) { return this->CancelPlantCaptchaOnRow(context, request, response); }));}
    void SetMessageAllocatorFor_CancelPlantCaptchaOnRow(
        ::grpc::MessageAllocator< ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(13);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_CancelPlantCaptchaOnRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptchaOnRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CancelPlantCaptchaOnRow(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_StartPlantCaptcha<WithCallbackMethod_GetNextPlantCaptchaStatus<WithCallbackMethod_GetNextPlantCaptchasList<WithCallbackMethod_DeletePlantCaptcha<WithCallbackMethod_GetPlantCaptcha<WithCallbackMethod_CancelPlantCaptcha<WithCallbackMethod_StartPlantCaptchaUpload<WithCallbackMethod_GetNextPlantCaptchaUploadState<WithCallbackMethod_SubmitPlantCaptchaResults<WithCallbackMethod_GetPlantCaptchaItemResults<WithCallbackMethod_CalculatePlantCaptcha<WithCallbackMethod_GetOriginalModelinatorConfig<WithCallbackMethod_GetCaptchaRowStatus<WithCallbackMethod_CancelPlantCaptchaOnRow<Service > > > > > > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextPlantCaptchasList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextPlantCaptchasList() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetNextPlantCaptchasList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchasList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DeletePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DeletePlantCaptcha() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_DeletePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeletePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetPlantCaptcha() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_GetPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StartPlantCaptchaUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartPlantCaptchaUpload() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_StartPlantCaptchaUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptchaUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextPlantCaptchaUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextPlantCaptchaUploadState() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_GetNextPlantCaptchaUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchaUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SubmitPlantCaptchaResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SubmitPlantCaptchaResults() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_SubmitPlantCaptchaResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SubmitPlantCaptchaResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetPlantCaptchaItemResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetPlantCaptchaItemResults() {
      ::grpc::Service::MarkMethodGeneric(9);
    }
    ~WithGenericMethod_GetPlantCaptchaItemResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptchaItemResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CalculatePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CalculatePlantCaptcha() {
      ::grpc::Service::MarkMethodGeneric(10);
    }
    ~WithGenericMethod_CalculatePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CalculatePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetOriginalModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetOriginalModelinatorConfig() {
      ::grpc::Service::MarkMethodGeneric(11);
    }
    ~WithGenericMethod_GetOriginalModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetOriginalModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* /*request*/, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetCaptchaRowStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetCaptchaRowStatus() {
      ::grpc::Service::MarkMethodGeneric(12);
    }
    ~WithGenericMethod_GetCaptchaRowStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCaptchaRowStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CancelPlantCaptchaOnRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CancelPlantCaptchaOnRow() {
      ::grpc::Service::MarkMethodGeneric(13);
    }
    ~WithGenericMethod_CancelPlantCaptchaOnRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptchaOnRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartPlantCaptcha(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextPlantCaptchaStatus(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextPlantCaptchasList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextPlantCaptchasList() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetNextPlantCaptchasList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchasList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextPlantCaptchasList(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DeletePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DeletePlantCaptcha() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_DeletePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeletePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeletePlantCaptcha(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetPlantCaptcha() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_GetPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPlantCaptcha(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCancelPlantCaptcha(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartPlantCaptchaUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartPlantCaptchaUpload() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_StartPlantCaptchaUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptchaUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartPlantCaptchaUpload(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextPlantCaptchaUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextPlantCaptchaUploadState() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_GetNextPlantCaptchaUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchaUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextPlantCaptchaUploadState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SubmitPlantCaptchaResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SubmitPlantCaptchaResults() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_SubmitPlantCaptchaResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SubmitPlantCaptchaResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSubmitPlantCaptchaResults(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetPlantCaptchaItemResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetPlantCaptchaItemResults() {
      ::grpc::Service::MarkMethodRaw(9);
    }
    ~WithRawMethod_GetPlantCaptchaItemResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptchaItemResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPlantCaptchaItemResults(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CalculatePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CalculatePlantCaptcha() {
      ::grpc::Service::MarkMethodRaw(10);
    }
    ~WithRawMethod_CalculatePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CalculatePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCalculatePlantCaptcha(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetOriginalModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetOriginalModelinatorConfig() {
      ::grpc::Service::MarkMethodRaw(11);
    }
    ~WithRawMethod_GetOriginalModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetOriginalModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* /*request*/, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetOriginalModelinatorConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetCaptchaRowStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetCaptchaRowStatus() {
      ::grpc::Service::MarkMethodRaw(12);
    }
    ~WithRawMethod_GetCaptchaRowStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCaptchaRowStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCaptchaRowStatus(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CancelPlantCaptchaOnRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CancelPlantCaptchaOnRow() {
      ::grpc::Service::MarkMethodRaw(13);
    }
    ~WithRawMethod_CancelPlantCaptchaOnRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptchaOnRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCancelPlantCaptchaOnRow(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartPlantCaptcha(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartPlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextPlantCaptchaStatus(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextPlantCaptchaStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextPlantCaptchasList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextPlantCaptchasList() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextPlantCaptchasList(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextPlantCaptchasList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchasList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextPlantCaptchasList(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DeletePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DeletePlantCaptcha() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DeletePlantCaptcha(context, request, response); }));
    }
    ~WithRawCallbackMethod_DeletePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeletePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeletePlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetPlantCaptcha() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetPlantCaptcha(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CancelPlantCaptcha(context, request, response); }));
    }
    ~WithRawCallbackMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CancelPlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartPlantCaptchaUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartPlantCaptchaUpload() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartPlantCaptchaUpload(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartPlantCaptchaUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptchaUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartPlantCaptchaUpload(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextPlantCaptchaUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextPlantCaptchaUploadState() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextPlantCaptchaUploadState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextPlantCaptchaUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPlantCaptchaUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextPlantCaptchaUploadState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SubmitPlantCaptchaResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SubmitPlantCaptchaResults() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SubmitPlantCaptchaResults(context, request, response); }));
    }
    ~WithRawCallbackMethod_SubmitPlantCaptchaResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SubmitPlantCaptchaResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SubmitPlantCaptchaResults(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetPlantCaptchaItemResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetPlantCaptchaItemResults() {
      ::grpc::Service::MarkMethodRawCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetPlantCaptchaItemResults(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetPlantCaptchaItemResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptchaItemResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPlantCaptchaItemResults(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_CalculatePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_CalculatePlantCaptcha() {
      ::grpc::Service::MarkMethodRawCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CalculatePlantCaptcha(context, request, response); }));
    }
    ~WithRawCallbackMethod_CalculatePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CalculatePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CalculatePlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetOriginalModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetOriginalModelinatorConfig() {
      ::grpc::Service::MarkMethodRawCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetOriginalModelinatorConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetOriginalModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetOriginalModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* /*request*/, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetOriginalModelinatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetCaptchaRowStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetCaptchaRowStatus() {
      ::grpc::Service::MarkMethodRawCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetCaptchaRowStatus(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetCaptchaRowStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCaptchaRowStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCaptchaRowStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_CancelPlantCaptchaOnRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_CancelPlantCaptchaOnRow() {
      ::grpc::Service::MarkMethodRawCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CancelPlantCaptchaOnRow(context, request, response); }));
    }
    ~WithRawCallbackMethod_CancelPlantCaptchaOnRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptchaOnRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CancelPlantCaptchaOnRow(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>* streamer) {
                       return this->StreamedStartPlantCaptcha(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartPlantCaptcha(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest,::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>* streamer) {
                       return this->StreamedGetNextPlantCaptchaStatus(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextPlantCaptchaStatus(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest,::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextPlantCaptchasList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextPlantCaptchasList() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>* streamer) {
                       return this->StreamedGetNextPlantCaptchasList(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextPlantCaptchasList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextPlantCaptchasList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextPlantCaptchasList(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest,::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DeletePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DeletePlantCaptcha() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedDeletePlantCaptcha(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DeletePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DeletePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDeletePlantCaptcha(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetPlantCaptcha() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>* streamer) {
                       return this->StreamedGetPlantCaptcha(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetPlantCaptcha(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest,::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedCancelPlantCaptcha(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCancelPlantCaptcha(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartPlantCaptchaUpload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartPlantCaptchaUpload() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedStartPlantCaptchaUpload(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartPlantCaptchaUpload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartPlantCaptchaUpload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartPlantCaptchaUpload(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextPlantCaptchaUploadState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextPlantCaptchaUploadState() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>* streamer) {
                       return this->StreamedGetNextPlantCaptchaUploadState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextPlantCaptchaUploadState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextPlantCaptchaUploadState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* /*request*/, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextPlantCaptchaUploadState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest,::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SubmitPlantCaptchaResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SubmitPlantCaptchaResults() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSubmitPlantCaptchaResults(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SubmitPlantCaptchaResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SubmitPlantCaptchaResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSubmitPlantCaptchaResults(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetPlantCaptchaItemResults : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetPlantCaptchaItemResults() {
      ::grpc::Service::MarkMethodStreamed(9,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>* streamer) {
                       return this->StreamedGetPlantCaptchaItemResults(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetPlantCaptchaItemResults() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetPlantCaptchaItemResults(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* /*request*/, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetPlantCaptchaItemResults(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest,::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CalculatePlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CalculatePlantCaptcha() {
      ::grpc::Service::MarkMethodStreamed(10,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>* streamer) {
                       return this->StreamedCalculatePlantCaptcha(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_CalculatePlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CalculatePlantCaptcha(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* /*request*/, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCalculatePlantCaptcha(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest,::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetOriginalModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetOriginalModelinatorConfig() {
      ::grpc::Service::MarkMethodStreamed(11,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>* streamer) {
                       return this->StreamedGetOriginalModelinatorConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetOriginalModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetOriginalModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* /*request*/, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetOriginalModelinatorConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest,::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetCaptchaRowStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetCaptchaRowStatus() {
      ::grpc::Service::MarkMethodStreamed(12,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>* streamer) {
                       return this->StreamedGetCaptchaRowStatus(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetCaptchaRowStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetCaptchaRowStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetCaptchaRowStatus(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CancelPlantCaptchaOnRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CancelPlantCaptchaOnRow() {
      ::grpc::Service::MarkMethodStreamed(13,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedCancelPlantCaptchaOnRow(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_CancelPlantCaptchaOnRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CancelPlantCaptchaOnRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCancelPlantCaptchaOnRow(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_StartPlantCaptcha<WithStreamedUnaryMethod_GetNextPlantCaptchaStatus<WithStreamedUnaryMethod_GetNextPlantCaptchasList<WithStreamedUnaryMethod_DeletePlantCaptcha<WithStreamedUnaryMethod_GetPlantCaptcha<WithStreamedUnaryMethod_CancelPlantCaptcha<WithStreamedUnaryMethod_StartPlantCaptchaUpload<WithStreamedUnaryMethod_GetNextPlantCaptchaUploadState<WithStreamedUnaryMethod_SubmitPlantCaptchaResults<WithStreamedUnaryMethod_GetPlantCaptchaItemResults<WithStreamedUnaryMethod_CalculatePlantCaptcha<WithStreamedUnaryMethod_GetOriginalModelinatorConfig<WithStreamedUnaryMethod_GetCaptchaRowStatus<WithStreamedUnaryMethod_CancelPlantCaptchaOnRow<Service > > > > > > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_StartPlantCaptcha<WithStreamedUnaryMethod_GetNextPlantCaptchaStatus<WithStreamedUnaryMethod_GetNextPlantCaptchasList<WithStreamedUnaryMethod_DeletePlantCaptcha<WithStreamedUnaryMethod_GetPlantCaptcha<WithStreamedUnaryMethod_CancelPlantCaptcha<WithStreamedUnaryMethod_StartPlantCaptchaUpload<WithStreamedUnaryMethod_GetNextPlantCaptchaUploadState<WithStreamedUnaryMethod_SubmitPlantCaptchaResults<WithStreamedUnaryMethod_GetPlantCaptchaItemResults<WithStreamedUnaryMethod_CalculatePlantCaptcha<WithStreamedUnaryMethod_GetOriginalModelinatorConfig<WithStreamedUnaryMethod_GetCaptchaRowStatus<WithStreamedUnaryMethod_CancelPlantCaptchaOnRow<Service > > > > > > > > > > > > > > StreamedService;
};

}  // namespace plant_captcha
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fplant_5fcaptcha_2eproto__INCLUDED

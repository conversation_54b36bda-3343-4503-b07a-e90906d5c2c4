"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.proto.thinning.thinning_pb2 import (
    ConfigDefinition as proto___thinning___thinning_pb2___ConfigDefinition,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

ThinningConfVerValue = typing___NewType('ThinningConfVerValue', builtin___int)
type___ThinningConfVerValue = ThinningConfVerValue
ThinningConfVer: _ThinningConfVer
class _ThinningConfVer(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ThinningConfVerValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    THIN_CONF_V1 = typing___cast(ThinningConfVerValue, 0)
    THIN_CONF_V2 = typing___cast(ThinningConfVerValue, 1)
THIN_CONF_V1 = typing___cast(ThinningConfVerValue, 0)
THIN_CONF_V2 = typing___cast(ThinningConfVerValue, 1)

class GetNextConfigurationsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    active_id: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def definitions(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[proto___thinning___thinning_pb2___ConfigDefinition]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        definitions : typing___Optional[typing___Iterable[proto___thinning___thinning_pb2___ConfigDefinition]] = None,
        active_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active_id",b"active_id",u"definitions",b"definitions",u"ts",b"ts"]) -> None: ...
type___GetNextConfigurationsResponse = GetNextConfigurationsResponse

class GetNextActiveConfResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    id: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        name : typing___Optional[typing___Text] = None,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"name",b"name",u"ts",b"ts"]) -> None: ...
type___GetNextActiveConfResponse = GetNextActiveConfResponse

class DefineConfigurationRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    set_active: builtin___bool = ...
    ver: type___ThinningConfVerValue = ...

    @property
    def definition(self) -> proto___thinning___thinning_pb2___ConfigDefinition: ...

    def __init__(self,
        *,
        definition : typing___Optional[proto___thinning___thinning_pb2___ConfigDefinition] = None,
        set_active : typing___Optional[builtin___bool] = None,
        ver : typing___Optional[type___ThinningConfVerValue] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"definition",b"definition"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"definition",b"definition",u"set_active",b"set_active",u"ver",b"ver"]) -> None: ...
type___DefineConfigurationRequest = DefineConfigurationRequest

class DefineConfigurationResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___DefineConfigurationResponse = DefineConfigurationResponse

class SetActiveConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    id: typing___Text = ...
    ver: type___ThinningConfVerValue = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        id : typing___Optional[typing___Text] = None,
        ver : typing___Optional[type___ThinningConfVerValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"name",b"name",u"ver",b"ver"]) -> None: ...
type___SetActiveConfigRequest = SetActiveConfigRequest

class SetActiveConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetActiveConfigResponse = SetActiveConfigResponse

class DeleteConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    id: typing___Text = ...
    ver: type___ThinningConfVerValue = ...
    new_active_id: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        id : typing___Optional[typing___Text] = None,
        ver : typing___Optional[type___ThinningConfVerValue] = None,
        new_active_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"name",b"name",u"new_active_id",b"new_active_id",u"ver",b"ver"]) -> None: ...
type___DeleteConfigRequest = DeleteConfigRequest

class DeleteConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___DeleteConfigResponse = DeleteConfigResponse

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/target_velocity_estimator.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
#include "proto/target_velocity_estimator/target_velocity_estimator.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[10]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
namespace carbon {
namespace frontend {
namespace target_velocity_estimator {
class DeleteTVEProfileRequest;
struct DeleteTVEProfileRequestDefaultTypeInternal;
extern DeleteTVEProfileRequestDefaultTypeInternal _DeleteTVEProfileRequest_default_instance_;
class DeleteTVEProfileResponse;
struct DeleteTVEProfileResponseDefaultTypeInternal;
extern DeleteTVEProfileResponseDefaultTypeInternal _DeleteTVEProfileResponse_default_instance_;
class GetNextActiveTVEProfileResponse;
struct GetNextActiveTVEProfileResponseDefaultTypeInternal;
extern GetNextActiveTVEProfileResponseDefaultTypeInternal _GetNextActiveTVEProfileResponse_default_instance_;
class GetNextAvailableTVEProfilesResponse;
struct GetNextAvailableTVEProfilesResponseDefaultTypeInternal;
extern GetNextAvailableTVEProfilesResponseDefaultTypeInternal _GetNextAvailableTVEProfilesResponse_default_instance_;
class LoadTVEProfileRequest;
struct LoadTVEProfileRequestDefaultTypeInternal;
extern LoadTVEProfileRequestDefaultTypeInternal _LoadTVEProfileRequest_default_instance_;
class LoadTVEProfileResponse;
struct LoadTVEProfileResponseDefaultTypeInternal;
extern LoadTVEProfileResponseDefaultTypeInternal _LoadTVEProfileResponse_default_instance_;
class SaveTVEProfileRequest;
struct SaveTVEProfileRequestDefaultTypeInternal;
extern SaveTVEProfileRequestDefaultTypeInternal _SaveTVEProfileRequest_default_instance_;
class SaveTVEProfileResponse;
struct SaveTVEProfileResponseDefaultTypeInternal;
extern SaveTVEProfileResponseDefaultTypeInternal _SaveTVEProfileResponse_default_instance_;
class SetActiveTVEProfileRequest;
struct SetActiveTVEProfileRequestDefaultTypeInternal;
extern SetActiveTVEProfileRequestDefaultTypeInternal _SetActiveTVEProfileRequest_default_instance_;
class SetActiveTVEProfileResponse;
struct SetActiveTVEProfileResponseDefaultTypeInternal;
extern SetActiveTVEProfileResponseDefaultTypeInternal _SetActiveTVEProfileResponse_default_instance_;
}  // namespace target_velocity_estimator
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* Arena::CreateMaybeMessage<::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest>(Arena*);
template<> ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* Arena::CreateMaybeMessage<::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>(Arena*);
template<> ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* Arena::CreateMaybeMessage<::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>(Arena*);
template<> ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* Arena::CreateMaybeMessage<::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>(Arena*);
template<> ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* Arena::CreateMaybeMessage<::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest>(Arena*);
template<> ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* Arena::CreateMaybeMessage<::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>(Arena*);
template<> ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* Arena::CreateMaybeMessage<::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest>(Arena*);
template<> ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* Arena::CreateMaybeMessage<::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>(Arena*);
template<> ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* Arena::CreateMaybeMessage<::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest>(Arena*);
template<> ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* Arena::CreateMaybeMessage<::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace target_velocity_estimator {

// ===================================================================

class GetNextAvailableTVEProfilesResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse) */ {
 public:
  inline GetNextAvailableTVEProfilesResponse() : GetNextAvailableTVEProfilesResponse(nullptr) {}
  ~GetNextAvailableTVEProfilesResponse() override;
  explicit constexpr GetNextAvailableTVEProfilesResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextAvailableTVEProfilesResponse(const GetNextAvailableTVEProfilesResponse& from);
  GetNextAvailableTVEProfilesResponse(GetNextAvailableTVEProfilesResponse&& from) noexcept
    : GetNextAvailableTVEProfilesResponse() {
    *this = ::std::move(from);
  }

  inline GetNextAvailableTVEProfilesResponse& operator=(const GetNextAvailableTVEProfilesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextAvailableTVEProfilesResponse& operator=(GetNextAvailableTVEProfilesResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextAvailableTVEProfilesResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextAvailableTVEProfilesResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextAvailableTVEProfilesResponse*>(
               &_GetNextAvailableTVEProfilesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GetNextAvailableTVEProfilesResponse& a, GetNextAvailableTVEProfilesResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextAvailableTVEProfilesResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextAvailableTVEProfilesResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextAvailableTVEProfilesResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextAvailableTVEProfilesResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextAvailableTVEProfilesResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextAvailableTVEProfilesResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextAvailableTVEProfilesResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse";
  }
  protected:
  explicit GetNextAvailableTVEProfilesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProfilesFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.aimbot.target_velocity_estimator.ProfileDetails profiles = 2;
  int profiles_size() const;
  private:
  int _internal_profiles_size() const;
  public:
  void clear_profiles();
  ::carbon::aimbot::target_velocity_estimator::ProfileDetails* mutable_profiles(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::target_velocity_estimator::ProfileDetails >*
      mutable_profiles();
  private:
  const ::carbon::aimbot::target_velocity_estimator::ProfileDetails& _internal_profiles(int index) const;
  ::carbon::aimbot::target_velocity_estimator::ProfileDetails* _internal_add_profiles();
  public:
  const ::carbon::aimbot::target_velocity_estimator::ProfileDetails& profiles(int index) const;
  ::carbon::aimbot::target_velocity_estimator::ProfileDetails* add_profiles();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::target_velocity_estimator::ProfileDetails >&
      profiles() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::target_velocity_estimator::ProfileDetails > profiles_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
};
// -------------------------------------------------------------------

class GetNextActiveTVEProfileResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse) */ {
 public:
  inline GetNextActiveTVEProfileResponse() : GetNextActiveTVEProfileResponse(nullptr) {}
  ~GetNextActiveTVEProfileResponse() override;
  explicit constexpr GetNextActiveTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextActiveTVEProfileResponse(const GetNextActiveTVEProfileResponse& from);
  GetNextActiveTVEProfileResponse(GetNextActiveTVEProfileResponse&& from) noexcept
    : GetNextActiveTVEProfileResponse() {
    *this = ::std::move(from);
  }

  inline GetNextActiveTVEProfileResponse& operator=(const GetNextActiveTVEProfileResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextActiveTVEProfileResponse& operator=(GetNextActiveTVEProfileResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextActiveTVEProfileResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextActiveTVEProfileResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextActiveTVEProfileResponse*>(
               &_GetNextActiveTVEProfileResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GetNextActiveTVEProfileResponse& a, GetNextActiveTVEProfileResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextActiveTVEProfileResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextActiveTVEProfileResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextActiveTVEProfileResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextActiveTVEProfileResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextActiveTVEProfileResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextActiveTVEProfileResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextActiveTVEProfileResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse";
  }
  protected:
  explicit GetNextActiveTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kProfileFieldNumber = 2,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 2;
  bool has_profile() const;
  private:
  bool _internal_has_profile() const;
  public:
  void clear_profile();
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& profile() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::target_velocity_estimator::TVEProfile* release_profile();
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* mutable_profile();
  void set_allocated_profile(::carbon::aimbot::target_velocity_estimator::TVEProfile* profile);
  private:
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& _internal_profile() const;
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _internal_mutable_profile();
  public:
  void unsafe_arena_set_allocated_profile(
      ::carbon::aimbot::target_velocity_estimator::TVEProfile* profile);
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* unsafe_arena_release_profile();

  // @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* profile_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
};
// -------------------------------------------------------------------

class LoadTVEProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest) */ {
 public:
  inline LoadTVEProfileRequest() : LoadTVEProfileRequest(nullptr) {}
  ~LoadTVEProfileRequest() override;
  explicit constexpr LoadTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LoadTVEProfileRequest(const LoadTVEProfileRequest& from);
  LoadTVEProfileRequest(LoadTVEProfileRequest&& from) noexcept
    : LoadTVEProfileRequest() {
    *this = ::std::move(from);
  }

  inline LoadTVEProfileRequest& operator=(const LoadTVEProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoadTVEProfileRequest& operator=(LoadTVEProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LoadTVEProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const LoadTVEProfileRequest* internal_default_instance() {
    return reinterpret_cast<const LoadTVEProfileRequest*>(
               &_LoadTVEProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(LoadTVEProfileRequest& a, LoadTVEProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(LoadTVEProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoadTVEProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LoadTVEProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LoadTVEProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LoadTVEProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LoadTVEProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoadTVEProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest";
  }
  protected:
  explicit LoadTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
};
// -------------------------------------------------------------------

class LoadTVEProfileResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse) */ {
 public:
  inline LoadTVEProfileResponse() : LoadTVEProfileResponse(nullptr) {}
  ~LoadTVEProfileResponse() override;
  explicit constexpr LoadTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LoadTVEProfileResponse(const LoadTVEProfileResponse& from);
  LoadTVEProfileResponse(LoadTVEProfileResponse&& from) noexcept
    : LoadTVEProfileResponse() {
    *this = ::std::move(from);
  }

  inline LoadTVEProfileResponse& operator=(const LoadTVEProfileResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoadTVEProfileResponse& operator=(LoadTVEProfileResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LoadTVEProfileResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const LoadTVEProfileResponse* internal_default_instance() {
    return reinterpret_cast<const LoadTVEProfileResponse*>(
               &_LoadTVEProfileResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(LoadTVEProfileResponse& a, LoadTVEProfileResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(LoadTVEProfileResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoadTVEProfileResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LoadTVEProfileResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LoadTVEProfileResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LoadTVEProfileResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LoadTVEProfileResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoadTVEProfileResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse";
  }
  protected:
  explicit LoadTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProfileFieldNumber = 1,
  };
  // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
  bool has_profile() const;
  private:
  bool _internal_has_profile() const;
  public:
  void clear_profile();
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& profile() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::target_velocity_estimator::TVEProfile* release_profile();
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* mutable_profile();
  void set_allocated_profile(::carbon::aimbot::target_velocity_estimator::TVEProfile* profile);
  private:
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& _internal_profile() const;
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _internal_mutable_profile();
  public:
  void unsafe_arena_set_allocated_profile(
      ::carbon::aimbot::target_velocity_estimator::TVEProfile* profile);
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* unsafe_arena_release_profile();

  // @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* profile_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
};
// -------------------------------------------------------------------

class SaveTVEProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest) */ {
 public:
  inline SaveTVEProfileRequest() : SaveTVEProfileRequest(nullptr) {}
  ~SaveTVEProfileRequest() override;
  explicit constexpr SaveTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SaveTVEProfileRequest(const SaveTVEProfileRequest& from);
  SaveTVEProfileRequest(SaveTVEProfileRequest&& from) noexcept
    : SaveTVEProfileRequest() {
    *this = ::std::move(from);
  }

  inline SaveTVEProfileRequest& operator=(const SaveTVEProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveTVEProfileRequest& operator=(SaveTVEProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SaveTVEProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SaveTVEProfileRequest* internal_default_instance() {
    return reinterpret_cast<const SaveTVEProfileRequest*>(
               &_SaveTVEProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(SaveTVEProfileRequest& a, SaveTVEProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveTVEProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveTVEProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SaveTVEProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SaveTVEProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SaveTVEProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SaveTVEProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveTVEProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest";
  }
  protected:
  explicit SaveTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProfileFieldNumber = 1,
    kSetActiveFieldNumber = 2,
  };
  // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
  bool has_profile() const;
  private:
  bool _internal_has_profile() const;
  public:
  void clear_profile();
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& profile() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::target_velocity_estimator::TVEProfile* release_profile();
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* mutable_profile();
  void set_allocated_profile(::carbon::aimbot::target_velocity_estimator::TVEProfile* profile);
  private:
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& _internal_profile() const;
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _internal_mutable_profile();
  public:
  void unsafe_arena_set_allocated_profile(
      ::carbon::aimbot::target_velocity_estimator::TVEProfile* profile);
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* unsafe_arena_release_profile();

  // bool set_active = 2;
  void clear_set_active();
  bool set_active() const;
  void set_set_active(bool value);
  private:
  bool _internal_set_active() const;
  void _internal_set_set_active(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* profile_;
  bool set_active_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
};
// -------------------------------------------------------------------

class SaveTVEProfileResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse) */ {
 public:
  inline SaveTVEProfileResponse() : SaveTVEProfileResponse(nullptr) {}
  ~SaveTVEProfileResponse() override;
  explicit constexpr SaveTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SaveTVEProfileResponse(const SaveTVEProfileResponse& from);
  SaveTVEProfileResponse(SaveTVEProfileResponse&& from) noexcept
    : SaveTVEProfileResponse() {
    *this = ::std::move(from);
  }

  inline SaveTVEProfileResponse& operator=(const SaveTVEProfileResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveTVEProfileResponse& operator=(SaveTVEProfileResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SaveTVEProfileResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SaveTVEProfileResponse* internal_default_instance() {
    return reinterpret_cast<const SaveTVEProfileResponse*>(
               &_SaveTVEProfileResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(SaveTVEProfileResponse& a, SaveTVEProfileResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveTVEProfileResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveTVEProfileResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SaveTVEProfileResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SaveTVEProfileResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SaveTVEProfileResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SaveTVEProfileResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveTVEProfileResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse";
  }
  protected:
  explicit SaveTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
};
// -------------------------------------------------------------------

class SetActiveTVEProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest) */ {
 public:
  inline SetActiveTVEProfileRequest() : SetActiveTVEProfileRequest(nullptr) {}
  ~SetActiveTVEProfileRequest() override;
  explicit constexpr SetActiveTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetActiveTVEProfileRequest(const SetActiveTVEProfileRequest& from);
  SetActiveTVEProfileRequest(SetActiveTVEProfileRequest&& from) noexcept
    : SetActiveTVEProfileRequest() {
    *this = ::std::move(from);
  }

  inline SetActiveTVEProfileRequest& operator=(const SetActiveTVEProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetActiveTVEProfileRequest& operator=(SetActiveTVEProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetActiveTVEProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetActiveTVEProfileRequest* internal_default_instance() {
    return reinterpret_cast<const SetActiveTVEProfileRequest*>(
               &_SetActiveTVEProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(SetActiveTVEProfileRequest& a, SetActiveTVEProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetActiveTVEProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetActiveTVEProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetActiveTVEProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetActiveTVEProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetActiveTVEProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetActiveTVEProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetActiveTVEProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest";
  }
  protected:
  explicit SetActiveTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
};
// -------------------------------------------------------------------

class SetActiveTVEProfileResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileResponse) */ {
 public:
  inline SetActiveTVEProfileResponse() : SetActiveTVEProfileResponse(nullptr) {}
  explicit constexpr SetActiveTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetActiveTVEProfileResponse(const SetActiveTVEProfileResponse& from);
  SetActiveTVEProfileResponse(SetActiveTVEProfileResponse&& from) noexcept
    : SetActiveTVEProfileResponse() {
    *this = ::std::move(from);
  }

  inline SetActiveTVEProfileResponse& operator=(const SetActiveTVEProfileResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetActiveTVEProfileResponse& operator=(SetActiveTVEProfileResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetActiveTVEProfileResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetActiveTVEProfileResponse* internal_default_instance() {
    return reinterpret_cast<const SetActiveTVEProfileResponse*>(
               &_SetActiveTVEProfileResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(SetActiveTVEProfileResponse& a, SetActiveTVEProfileResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetActiveTVEProfileResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetActiveTVEProfileResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetActiveTVEProfileResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetActiveTVEProfileResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SetActiveTVEProfileResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SetActiveTVEProfileResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.target_velocity_estimator.SetActiveTVEProfileResponse";
  }
  protected:
  explicit SetActiveTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
};
// -------------------------------------------------------------------

class DeleteTVEProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest) */ {
 public:
  inline DeleteTVEProfileRequest() : DeleteTVEProfileRequest(nullptr) {}
  ~DeleteTVEProfileRequest() override;
  explicit constexpr DeleteTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteTVEProfileRequest(const DeleteTVEProfileRequest& from);
  DeleteTVEProfileRequest(DeleteTVEProfileRequest&& from) noexcept
    : DeleteTVEProfileRequest() {
    *this = ::std::move(from);
  }

  inline DeleteTVEProfileRequest& operator=(const DeleteTVEProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteTVEProfileRequest& operator=(DeleteTVEProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteTVEProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteTVEProfileRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteTVEProfileRequest*>(
               &_DeleteTVEProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(DeleteTVEProfileRequest& a, DeleteTVEProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteTVEProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteTVEProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteTVEProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteTVEProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeleteTVEProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeleteTVEProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteTVEProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest";
  }
  protected:
  explicit DeleteTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kNewActiveIdFieldNumber = 2,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string new_active_id = 2;
  void clear_new_active_id();
  const std::string& new_active_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_new_active_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_new_active_id();
  PROTOBUF_NODISCARD std::string* release_new_active_id();
  void set_allocated_new_active_id(std::string* new_active_id);
  private:
  const std::string& _internal_new_active_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_new_active_id(const std::string& value);
  std::string* _internal_mutable_new_active_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr new_active_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
};
// -------------------------------------------------------------------

class DeleteTVEProfileResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.frontend.target_velocity_estimator.DeleteTVEProfileResponse) */ {
 public:
  inline DeleteTVEProfileResponse() : DeleteTVEProfileResponse(nullptr) {}
  explicit constexpr DeleteTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteTVEProfileResponse(const DeleteTVEProfileResponse& from);
  DeleteTVEProfileResponse(DeleteTVEProfileResponse&& from) noexcept
    : DeleteTVEProfileResponse() {
    *this = ::std::move(from);
  }

  inline DeleteTVEProfileResponse& operator=(const DeleteTVEProfileResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteTVEProfileResponse& operator=(DeleteTVEProfileResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteTVEProfileResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteTVEProfileResponse* internal_default_instance() {
    return reinterpret_cast<const DeleteTVEProfileResponse*>(
               &_DeleteTVEProfileResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(DeleteTVEProfileResponse& a, DeleteTVEProfileResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteTVEProfileResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteTVEProfileResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteTVEProfileResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteTVEProfileResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const DeleteTVEProfileResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const DeleteTVEProfileResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.target_velocity_estimator.DeleteTVEProfileResponse";
  }
  protected:
  explicit DeleteTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.DeleteTVEProfileResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GetNextAvailableTVEProfilesResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextAvailableTVEProfilesResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextAvailableTVEProfilesResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextAvailableTVEProfilesResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextAvailableTVEProfilesResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.ts)
  return _internal_ts();
}
inline void GetNextAvailableTVEProfilesResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextAvailableTVEProfilesResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAvailableTVEProfilesResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAvailableTVEProfilesResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextAvailableTVEProfilesResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.ts)
  return _msg;
}
inline void GetNextAvailableTVEProfilesResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.ts)
}

// repeated .carbon.aimbot.target_velocity_estimator.ProfileDetails profiles = 2;
inline int GetNextAvailableTVEProfilesResponse::_internal_profiles_size() const {
  return profiles_.size();
}
inline int GetNextAvailableTVEProfilesResponse::profiles_size() const {
  return _internal_profiles_size();
}
inline ::carbon::aimbot::target_velocity_estimator::ProfileDetails* GetNextAvailableTVEProfilesResponse::mutable_profiles(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.profiles)
  return profiles_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::target_velocity_estimator::ProfileDetails >*
GetNextAvailableTVEProfilesResponse::mutable_profiles() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.profiles)
  return &profiles_;
}
inline const ::carbon::aimbot::target_velocity_estimator::ProfileDetails& GetNextAvailableTVEProfilesResponse::_internal_profiles(int index) const {
  return profiles_.Get(index);
}
inline const ::carbon::aimbot::target_velocity_estimator::ProfileDetails& GetNextAvailableTVEProfilesResponse::profiles(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.profiles)
  return _internal_profiles(index);
}
inline ::carbon::aimbot::target_velocity_estimator::ProfileDetails* GetNextAvailableTVEProfilesResponse::_internal_add_profiles() {
  return profiles_.Add();
}
inline ::carbon::aimbot::target_velocity_estimator::ProfileDetails* GetNextAvailableTVEProfilesResponse::add_profiles() {
  ::carbon::aimbot::target_velocity_estimator::ProfileDetails* _add = _internal_add_profiles();
  // @@protoc_insertion_point(field_add:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.profiles)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::aimbot::target_velocity_estimator::ProfileDetails >&
GetNextAvailableTVEProfilesResponse::profiles() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.profiles)
  return profiles_;
}

// -------------------------------------------------------------------

// GetNextActiveTVEProfileResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextActiveTVEProfileResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextActiveTVEProfileResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveTVEProfileResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveTVEProfileResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.ts)
  return _internal_ts();
}
inline void GetNextActiveTVEProfileResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveTVEProfileResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveTVEProfileResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveTVEProfileResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveTVEProfileResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.ts)
  return _msg;
}
inline void GetNextActiveTVEProfileResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.ts)
}

// .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 2;
inline bool GetNextActiveTVEProfileResponse::_internal_has_profile() const {
  return this != internal_default_instance() && profile_ != nullptr;
}
inline bool GetNextActiveTVEProfileResponse::has_profile() const {
  return _internal_has_profile();
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& GetNextActiveTVEProfileResponse::_internal_profile() const {
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile* p = profile_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::target_velocity_estimator::TVEProfile&>(
      ::carbon::aimbot::target_velocity_estimator::_TVEProfile_default_instance_);
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& GetNextActiveTVEProfileResponse::profile() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.profile)
  return _internal_profile();
}
inline void GetNextActiveTVEProfileResponse::unsafe_arena_set_allocated_profile(
    ::carbon::aimbot::target_velocity_estimator::TVEProfile* profile) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile_);
  }
  profile_ = profile;
  if (profile) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.profile)
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetNextActiveTVEProfileResponse::release_profile() {
  
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_;
  profile_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetNextActiveTVEProfileResponse::unsafe_arena_release_profile() {
  // @@protoc_insertion_point(field_release:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.profile)
  
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_;
  profile_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetNextActiveTVEProfileResponse::_internal_mutable_profile() {
  
  if (profile_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::target_velocity_estimator::TVEProfile>(GetArenaForAllocation());
    profile_ = p;
  }
  return profile_;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetNextActiveTVEProfileResponse::mutable_profile() {
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _msg = _internal_mutable_profile();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.profile)
  return _msg;
}
inline void GetNextActiveTVEProfileResponse::set_allocated_profile(::carbon::aimbot::target_velocity_estimator::TVEProfile* profile) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile_);
  }
  if (profile) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile));
    if (message_arena != submessage_arena) {
      profile = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, profile, submessage_arena);
    }
    
  } else {
    
  }
  profile_ = profile;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.profile)
}

// -------------------------------------------------------------------

// LoadTVEProfileRequest

// string id = 1;
inline void LoadTVEProfileRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& LoadTVEProfileRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LoadTVEProfileRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest.id)
}
inline std::string* LoadTVEProfileRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest.id)
  return _s;
}
inline const std::string& LoadTVEProfileRequest::_internal_id() const {
  return id_.Get();
}
inline void LoadTVEProfileRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* LoadTVEProfileRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* LoadTVEProfileRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void LoadTVEProfileRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest.id)
}

// -------------------------------------------------------------------

// LoadTVEProfileResponse

// .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
inline bool LoadTVEProfileResponse::_internal_has_profile() const {
  return this != internal_default_instance() && profile_ != nullptr;
}
inline bool LoadTVEProfileResponse::has_profile() const {
  return _internal_has_profile();
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& LoadTVEProfileResponse::_internal_profile() const {
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile* p = profile_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::target_velocity_estimator::TVEProfile&>(
      ::carbon::aimbot::target_velocity_estimator::_TVEProfile_default_instance_);
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& LoadTVEProfileResponse::profile() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse.profile)
  return _internal_profile();
}
inline void LoadTVEProfileResponse::unsafe_arena_set_allocated_profile(
    ::carbon::aimbot::target_velocity_estimator::TVEProfile* profile) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile_);
  }
  profile_ = profile;
  if (profile) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse.profile)
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* LoadTVEProfileResponse::release_profile() {
  
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_;
  profile_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* LoadTVEProfileResponse::unsafe_arena_release_profile() {
  // @@protoc_insertion_point(field_release:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse.profile)
  
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_;
  profile_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* LoadTVEProfileResponse::_internal_mutable_profile() {
  
  if (profile_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::target_velocity_estimator::TVEProfile>(GetArenaForAllocation());
    profile_ = p;
  }
  return profile_;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* LoadTVEProfileResponse::mutable_profile() {
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _msg = _internal_mutable_profile();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse.profile)
  return _msg;
}
inline void LoadTVEProfileResponse::set_allocated_profile(::carbon::aimbot::target_velocity_estimator::TVEProfile* profile) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile_);
  }
  if (profile) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile));
    if (message_arena != submessage_arena) {
      profile = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, profile, submessage_arena);
    }
    
  } else {
    
  }
  profile_ = profile;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse.profile)
}

// -------------------------------------------------------------------

// SaveTVEProfileRequest

// .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
inline bool SaveTVEProfileRequest::_internal_has_profile() const {
  return this != internal_default_instance() && profile_ != nullptr;
}
inline bool SaveTVEProfileRequest::has_profile() const {
  return _internal_has_profile();
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& SaveTVEProfileRequest::_internal_profile() const {
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile* p = profile_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::target_velocity_estimator::TVEProfile&>(
      ::carbon::aimbot::target_velocity_estimator::_TVEProfile_default_instance_);
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& SaveTVEProfileRequest::profile() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest.profile)
  return _internal_profile();
}
inline void SaveTVEProfileRequest::unsafe_arena_set_allocated_profile(
    ::carbon::aimbot::target_velocity_estimator::TVEProfile* profile) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile_);
  }
  profile_ = profile;
  if (profile) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest.profile)
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* SaveTVEProfileRequest::release_profile() {
  
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_;
  profile_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* SaveTVEProfileRequest::unsafe_arena_release_profile() {
  // @@protoc_insertion_point(field_release:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest.profile)
  
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_;
  profile_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* SaveTVEProfileRequest::_internal_mutable_profile() {
  
  if (profile_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::target_velocity_estimator::TVEProfile>(GetArenaForAllocation());
    profile_ = p;
  }
  return profile_;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* SaveTVEProfileRequest::mutable_profile() {
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _msg = _internal_mutable_profile();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest.profile)
  return _msg;
}
inline void SaveTVEProfileRequest::set_allocated_profile(::carbon::aimbot::target_velocity_estimator::TVEProfile* profile) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile_);
  }
  if (profile) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile));
    if (message_arena != submessage_arena) {
      profile = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, profile, submessage_arena);
    }
    
  } else {
    
  }
  profile_ = profile;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest.profile)
}

// bool set_active = 2;
inline void SaveTVEProfileRequest::clear_set_active() {
  set_active_ = false;
}
inline bool SaveTVEProfileRequest::_internal_set_active() const {
  return set_active_;
}
inline bool SaveTVEProfileRequest::set_active() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest.set_active)
  return _internal_set_active();
}
inline void SaveTVEProfileRequest::_internal_set_set_active(bool value) {
  
  set_active_ = value;
}
inline void SaveTVEProfileRequest::set_set_active(bool value) {
  _internal_set_set_active(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest.set_active)
}

// -------------------------------------------------------------------

// SaveTVEProfileResponse

// string id = 1;
inline void SaveTVEProfileResponse::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& SaveTVEProfileResponse::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SaveTVEProfileResponse::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse.id)
}
inline std::string* SaveTVEProfileResponse::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse.id)
  return _s;
}
inline const std::string& SaveTVEProfileResponse::_internal_id() const {
  return id_.Get();
}
inline void SaveTVEProfileResponse::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SaveTVEProfileResponse::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SaveTVEProfileResponse::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SaveTVEProfileResponse::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse.id)
}

// -------------------------------------------------------------------

// SetActiveTVEProfileRequest

// string id = 1;
inline void SetActiveTVEProfileRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& SetActiveTVEProfileRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetActiveTVEProfileRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest.id)
}
inline std::string* SetActiveTVEProfileRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest.id)
  return _s;
}
inline const std::string& SetActiveTVEProfileRequest::_internal_id() const {
  return id_.Get();
}
inline void SetActiveTVEProfileRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetActiveTVEProfileRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetActiveTVEProfileRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetActiveTVEProfileRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest.id)
}

// -------------------------------------------------------------------

// SetActiveTVEProfileResponse

// -------------------------------------------------------------------

// DeleteTVEProfileRequest

// string id = 1;
inline void DeleteTVEProfileRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& DeleteTVEProfileRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteTVEProfileRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.id)
}
inline std::string* DeleteTVEProfileRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.id)
  return _s;
}
inline const std::string& DeleteTVEProfileRequest::_internal_id() const {
  return id_.Get();
}
inline void DeleteTVEProfileRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteTVEProfileRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteTVEProfileRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteTVEProfileRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.id)
}

// string new_active_id = 2;
inline void DeleteTVEProfileRequest::clear_new_active_id() {
  new_active_id_.ClearToEmpty();
}
inline const std::string& DeleteTVEProfileRequest::new_active_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.new_active_id)
  return _internal_new_active_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteTVEProfileRequest::set_new_active_id(ArgT0&& arg0, ArgT... args) {
 
 new_active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.new_active_id)
}
inline std::string* DeleteTVEProfileRequest::mutable_new_active_id() {
  std::string* _s = _internal_mutable_new_active_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.new_active_id)
  return _s;
}
inline const std::string& DeleteTVEProfileRequest::_internal_new_active_id() const {
  return new_active_id_.Get();
}
inline void DeleteTVEProfileRequest::_internal_set_new_active_id(const std::string& value) {
  
  new_active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteTVEProfileRequest::_internal_mutable_new_active_id() {
  
  return new_active_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteTVEProfileRequest::release_new_active_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.new_active_id)
  return new_active_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteTVEProfileRequest::set_allocated_new_active_id(std::string* new_active_id) {
  if (new_active_id != nullptr) {
    
  } else {
    
  }
  new_active_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), new_active_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (new_active_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    new_active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.new_active_id)
}

// -------------------------------------------------------------------

// DeleteTVEProfileResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace target_velocity_estimator
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/calibration.proto
#ifndef GRPC_frontend_2fproto_2fcalibration_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fcalibration_2eproto__INCLUDED

#include "frontend/proto/calibration.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace color_calibration {

class CalibrationService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.color_calibration.CalibrationService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status StartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::color_calibration::ColorCalibrationValues>> AsyncStartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::color_calibration::ColorCalibrationValues>>(AsyncStartColorCalibrationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::color_calibration::ColorCalibrationValues>> PrepareAsyncStartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::color_calibration::ColorCalibrationValues>>(PrepareAsyncStartColorCalibrationRaw(context, request, cq));
    }
    virtual ::grpc::Status SaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSaveColorCalibrationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSaveColorCalibrationRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void StartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::color_calibration::ColorCalibrationValues>* AsyncStartColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::color_calibration::ColorCalibrationValues>* PrepareAsyncStartColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSaveColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSaveColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status StartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::color_calibration::ColorCalibrationValues>> AsyncStartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::color_calibration::ColorCalibrationValues>>(AsyncStartColorCalibrationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::color_calibration::ColorCalibrationValues>> PrepareAsyncStartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::color_calibration::ColorCalibrationValues>>(PrepareAsyncStartColorCalibrationRaw(context, request, cq));
    }
    ::grpc::Status SaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSaveColorCalibrationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSaveColorCalibrationRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void StartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response, std::function<void(::grpc::Status)>) override;
      void StartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::color_calibration::ColorCalibrationValues>* AsyncStartColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::color_calibration::ColorCalibrationValues>* PrepareAsyncStartColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSaveColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSaveColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_StartColorCalibration_;
    const ::grpc::internal::RpcMethod rpcmethod_SaveColorCalibration_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status StartColorCalibration(::grpc::ServerContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response);
    virtual ::grpc::Status SaveColorCalibration(::grpc::ServerContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues* request, ::carbon::frontend::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_StartColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartColorCalibration() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_StartColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::color_calibration::ColorCalibrationValues* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartColorCalibration(::grpc::ServerContext* context, ::carbon::frontend::camera::CameraRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::color_calibration::ColorCalibrationValues>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SaveColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SaveColorCalibration() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_SaveColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::color_calibration::ColorCalibrationValues* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveColorCalibration(::grpc::ServerContext* context, ::carbon::frontend::color_calibration::ColorCalibrationValues* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_StartColorCalibration<WithAsyncMethod_SaveColorCalibration<Service > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_StartColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartColorCalibration() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::color_calibration::ColorCalibrationValues>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response) { return this->StartColorCalibration(context, request, response); }));}
    void SetMessageAllocatorFor_StartColorCalibration(
        ::grpc::MessageAllocator< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::color_calibration::ColorCalibrationValues>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::color_calibration::ColorCalibrationValues>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::color_calibration::ColorCalibrationValues* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartColorCalibration(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::color_calibration::ColorCalibrationValues* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SaveColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SaveColorCalibration() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::color_calibration::ColorCalibrationValues, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues* request, ::carbon::frontend::util::Empty* response) { return this->SaveColorCalibration(context, request, response); }));}
    void SetMessageAllocatorFor_SaveColorCalibration(
        ::grpc::MessageAllocator< ::carbon::frontend::color_calibration::ColorCalibrationValues, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::color_calibration::ColorCalibrationValues, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SaveColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::color_calibration::ColorCalibrationValues* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveColorCalibration(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::color_calibration::ColorCalibrationValues* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_StartColorCalibration<WithCallbackMethod_SaveColorCalibration<Service > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_StartColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartColorCalibration() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_StartColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::color_calibration::ColorCalibrationValues* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SaveColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SaveColorCalibration() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_SaveColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::color_calibration::ColorCalibrationValues* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartColorCalibration() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_StartColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::color_calibration::ColorCalibrationValues* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartColorCalibration(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SaveColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SaveColorCalibration() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_SaveColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::color_calibration::ColorCalibrationValues* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveColorCalibration(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartColorCalibration() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartColorCalibration(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::color_calibration::ColorCalibrationValues* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartColorCalibration(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SaveColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SaveColorCalibration() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SaveColorCalibration(context, request, response); }));
    }
    ~WithRawCallbackMethod_SaveColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::color_calibration::ColorCalibrationValues* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveColorCalibration(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartColorCalibration() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::color_calibration::ColorCalibrationValues>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::color_calibration::ColorCalibrationValues>* streamer) {
                       return this->StreamedStartColorCalibration(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::color_calibration::ColorCalibrationValues* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartColorCalibration(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::camera::CameraRequest,::carbon::frontend::color_calibration::ColorCalibrationValues>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SaveColorCalibration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SaveColorCalibration() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::color_calibration::ColorCalibrationValues, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::color_calibration::ColorCalibrationValues, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSaveColorCalibration(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SaveColorCalibration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SaveColorCalibration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::color_calibration::ColorCalibrationValues* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSaveColorCalibration(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::color_calibration::ColorCalibrationValues,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_StartColorCalibration<WithStreamedUnaryMethod_SaveColorCalibration<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_StartColorCalibration<WithStreamedUnaryMethod_SaveColorCalibration<Service > > StreamedService;
};

}  // namespace color_calibration
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fcalibration_2eproto__INCLUDED

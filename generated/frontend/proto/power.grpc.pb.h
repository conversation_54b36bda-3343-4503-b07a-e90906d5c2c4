// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/power.proto
#ifndef GRPC_frontend_2fproto_2fpower_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fpower_2eproto__INCLUDED

#include "frontend/proto/power.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace power {

class PowerService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.power.PowerService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::carbon::frontend::power::PowerStatusResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::PowerStatusResponse>> AsyncGetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::PowerStatusResponse>>(AsyncGetNextPowerStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::PowerStatusResponse>> PrepareAsyncGetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::PowerStatusResponse>>(PrepareAsyncGetNextPowerStatusRaw(context, request, cq));
    }
    virtual ::grpc::Status TurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::carbon::frontend::power::RelayResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>> AsyncTurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>>(AsyncTurnOffDeviceRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>> PrepareAsyncTurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>>(PrepareAsyncTurnOffDeviceRaw(context, request, cq));
    }
    virtual ::grpc::Status TurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::carbon::frontend::power::RelayResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>> AsyncTurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>>(AsyncTurnOnDeviceRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>> PrepareAsyncTurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>>(PrepareAsyncTurnOnDeviceRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>> AsyncGetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>>(AsyncGetNextReaperAllHardwareStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>> PrepareAsyncGetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>>(PrepareAsyncGetNextReaperAllHardwareStatusRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>> AsyncGetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>>(AsyncGetNextReaperHardwareStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>> PrepareAsyncGetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>>(PrepareAsyncGetNextReaperHardwareStatusRaw(context, request, cq));
    }
    virtual ::grpc::Status SetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperScannerPowerResponse>> AsyncSetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperScannerPowerResponse>>(AsyncSetReaperScannerPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperScannerPowerResponse>> PrepareAsyncSetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperScannerPowerResponse>>(PrepareAsyncSetReaperScannerPowerRaw(context, request, cq));
    }
    virtual ::grpc::Status SetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperTargetPowerResponse>> AsyncSetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperTargetPowerResponse>>(AsyncSetReaperTargetPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperTargetPowerResponse>> PrepareAsyncSetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperTargetPowerResponse>>(PrepareAsyncSetReaperTargetPowerRaw(context, request, cq));
    }
    virtual ::grpc::Status SetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>> AsyncSetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>>(AsyncSetReaperPredictCamPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>> PrepareAsyncSetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>>(PrepareAsyncSetReaperPredictCamPowerRaw(context, request, cq));
    }
    virtual ::grpc::Status SetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperStrobeEnableResponse>> AsyncSetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperStrobeEnableResponse>>(AsyncSetReaperStrobeEnableRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperStrobeEnableResponse>> PrepareAsyncSetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperStrobeEnableResponse>>(PrepareAsyncSetReaperStrobeEnableRaw(context, request, cq));
    }
    virtual ::grpc::Status SetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>> AsyncSetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>>(AsyncSetReaperAllStrobesEnableRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>> PrepareAsyncSetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>>(PrepareAsyncSetReaperAllStrobesEnableRaw(context, request, cq));
    }
    virtual ::grpc::Status SetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModulePcPowerResponse>> AsyncSetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModulePcPowerResponse>>(AsyncSetReaperModulePcPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModulePcPowerResponse>> PrepareAsyncSetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModulePcPowerResponse>>(PrepareAsyncSetReaperModulePcPowerRaw(context, request, cq));
    }
    virtual ::grpc::Status SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>> AsyncSetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>>(AsyncSetReaperModuleLaserPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>> PrepareAsyncSetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>>(PrepareAsyncSetReaperModuleLaserPowerRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest* request, ::carbon::frontend::power::PowerStatusResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest* request, ::carbon::frontend::power::PowerStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void TurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void TurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void TurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void TurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest* request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest* request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest* request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest* request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::PowerStatusResponse>* AsyncGetNextPowerStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::PowerStatusResponse>* PrepareAsyncGetNextPowerStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>* AsyncTurnOffDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>* PrepareAsyncTurnOffDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>* AsyncTurnOnDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::RelayResponse>* PrepareAsyncTurnOnDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>* AsyncGetNextReaperAllHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>* PrepareAsyncGetNextReaperAllHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>* AsyncGetNextReaperHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>* PrepareAsyncGetNextReaperHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperScannerPowerResponse>* AsyncSetReaperScannerPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperScannerPowerResponse>* PrepareAsyncSetReaperScannerPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperTargetPowerResponse>* AsyncSetReaperTargetPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperTargetPowerResponse>* PrepareAsyncSetReaperTargetPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>* AsyncSetReaperPredictCamPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>* PrepareAsyncSetReaperPredictCamPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperStrobeEnableResponse>* AsyncSetReaperStrobeEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperStrobeEnableResponse>* PrepareAsyncSetReaperStrobeEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>* AsyncSetReaperAllStrobesEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>* PrepareAsyncSetReaperAllStrobesEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModulePcPowerResponse>* AsyncSetReaperModulePcPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModulePcPowerResponse>* PrepareAsyncSetReaperModulePcPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>* AsyncSetReaperModuleLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>* PrepareAsyncSetReaperModuleLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::carbon::frontend::power::PowerStatusResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::PowerStatusResponse>> AsyncGetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::PowerStatusResponse>>(AsyncGetNextPowerStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::PowerStatusResponse>> PrepareAsyncGetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::PowerStatusResponse>>(PrepareAsyncGetNextPowerStatusRaw(context, request, cq));
    }
    ::grpc::Status TurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::carbon::frontend::power::RelayResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>> AsyncTurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>>(AsyncTurnOffDeviceRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>> PrepareAsyncTurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>>(PrepareAsyncTurnOffDeviceRaw(context, request, cq));
    }
    ::grpc::Status TurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::carbon::frontend::power::RelayResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>> AsyncTurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>>(AsyncTurnOnDeviceRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>> PrepareAsyncTurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>>(PrepareAsyncTurnOnDeviceRaw(context, request, cq));
    }
    ::grpc::Status GetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>> AsyncGetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>>(AsyncGetNextReaperAllHardwareStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>> PrepareAsyncGetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>>(PrepareAsyncGetNextReaperAllHardwareStatusRaw(context, request, cq));
    }
    ::grpc::Status GetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>> AsyncGetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>>(AsyncGetNextReaperHardwareStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>> PrepareAsyncGetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>>(PrepareAsyncGetNextReaperHardwareStatusRaw(context, request, cq));
    }
    ::grpc::Status SetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperScannerPowerResponse>> AsyncSetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperScannerPowerResponse>>(AsyncSetReaperScannerPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperScannerPowerResponse>> PrepareAsyncSetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperScannerPowerResponse>>(PrepareAsyncSetReaperScannerPowerRaw(context, request, cq));
    }
    ::grpc::Status SetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperTargetPowerResponse>> AsyncSetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperTargetPowerResponse>>(AsyncSetReaperTargetPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperTargetPowerResponse>> PrepareAsyncSetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperTargetPowerResponse>>(PrepareAsyncSetReaperTargetPowerRaw(context, request, cq));
    }
    ::grpc::Status SetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>> AsyncSetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>>(AsyncSetReaperPredictCamPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>> PrepareAsyncSetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>>(PrepareAsyncSetReaperPredictCamPowerRaw(context, request, cq));
    }
    ::grpc::Status SetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperStrobeEnableResponse>> AsyncSetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperStrobeEnableResponse>>(AsyncSetReaperStrobeEnableRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperStrobeEnableResponse>> PrepareAsyncSetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperStrobeEnableResponse>>(PrepareAsyncSetReaperStrobeEnableRaw(context, request, cq));
    }
    ::grpc::Status SetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>> AsyncSetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>>(AsyncSetReaperAllStrobesEnableRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>> PrepareAsyncSetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>>(PrepareAsyncSetReaperAllStrobesEnableRaw(context, request, cq));
    }
    ::grpc::Status SetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModulePcPowerResponse>> AsyncSetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModulePcPowerResponse>>(AsyncSetReaperModulePcPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModulePcPowerResponse>> PrepareAsyncSetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModulePcPowerResponse>>(PrepareAsyncSetReaperModulePcPowerRaw(context, request, cq));
    }
    ::grpc::Status SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>> AsyncSetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>>(AsyncSetReaperModuleLaserPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>> PrepareAsyncSetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>>(PrepareAsyncSetReaperModuleLaserPowerRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest* request, ::carbon::frontend::power::PowerStatusResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextPowerStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest* request, ::carbon::frontend::power::PowerStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void TurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, std::function<void(::grpc::Status)>) override;
      void TurnOffDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void TurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, std::function<void(::grpc::Status)>) override;
      void TurnOnDevice(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextReaperAllHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextReaperHardwareStatus(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest* request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response, std::function<void(::grpc::Status)>) override;
      void SetReaperScannerPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest* request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest* request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response, std::function<void(::grpc::Status)>) override;
      void SetReaperTargetPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest* request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response, std::function<void(::grpc::Status)>) override;
      void SetReaperPredictCamPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response, std::function<void(::grpc::Status)>) override;
      void SetReaperStrobeEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response, std::function<void(::grpc::Status)>) override;
      void SetReaperAllStrobesEnable(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response, std::function<void(::grpc::Status)>) override;
      void SetReaperModulePcPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response, std::function<void(::grpc::Status)>) override;
      void SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::PowerStatusResponse>* AsyncGetNextPowerStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::PowerStatusResponse>* PrepareAsyncGetNextPowerStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::PowerStatusRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>* AsyncTurnOffDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>* PrepareAsyncTurnOffDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>* AsyncTurnOnDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::RelayResponse>* PrepareAsyncTurnOnDeviceRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::RelayRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>* AsyncGetNextReaperAllHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>* PrepareAsyncGetNextReaperAllHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>* AsyncGetNextReaperHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>* PrepareAsyncGetNextReaperHardwareStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperScannerPowerResponse>* AsyncSetReaperScannerPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperScannerPowerResponse>* PrepareAsyncSetReaperScannerPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperTargetPowerResponse>* AsyncSetReaperTargetPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperTargetPowerResponse>* PrepareAsyncSetReaperTargetPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>* AsyncSetReaperPredictCamPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>* PrepareAsyncSetReaperPredictCamPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperStrobeEnableResponse>* AsyncSetReaperStrobeEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperStrobeEnableResponse>* PrepareAsyncSetReaperStrobeEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>* AsyncSetReaperAllStrobesEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>* PrepareAsyncSetReaperAllStrobesEnableRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModulePcPowerResponse>* AsyncSetReaperModulePcPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModulePcPowerResponse>* PrepareAsyncSetReaperModulePcPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>* AsyncSetReaperModuleLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>* PrepareAsyncSetReaperModuleLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextPowerStatus_;
    const ::grpc::internal::RpcMethod rpcmethod_TurnOffDevice_;
    const ::grpc::internal::RpcMethod rpcmethod_TurnOnDevice_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextReaperAllHardwareStatus_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextReaperHardwareStatus_;
    const ::grpc::internal::RpcMethod rpcmethod_SetReaperScannerPower_;
    const ::grpc::internal::RpcMethod rpcmethod_SetReaperTargetPower_;
    const ::grpc::internal::RpcMethod rpcmethod_SetReaperPredictCamPower_;
    const ::grpc::internal::RpcMethod rpcmethod_SetReaperStrobeEnable_;
    const ::grpc::internal::RpcMethod rpcmethod_SetReaperAllStrobesEnable_;
    const ::grpc::internal::RpcMethod rpcmethod_SetReaperModulePcPower_;
    const ::grpc::internal::RpcMethod rpcmethod_SetReaperModuleLaserPower_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextPowerStatus(::grpc::ServerContext* context, const ::carbon::frontend::power::PowerStatusRequest* request, ::carbon::frontend::power::PowerStatusResponse* response);
    virtual ::grpc::Status TurnOffDevice(::grpc::ServerContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response);
    virtual ::grpc::Status TurnOnDevice(::grpc::ServerContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response);
    virtual ::grpc::Status GetNextReaperAllHardwareStatus(::grpc::ServerContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response);
    virtual ::grpc::Status GetNextReaperHardwareStatus(::grpc::ServerContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response);
    virtual ::grpc::Status SetReaperScannerPower(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest* request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response);
    virtual ::grpc::Status SetReaperTargetPower(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest* request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response);
    virtual ::grpc::Status SetReaperPredictCamPower(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response);
    virtual ::grpc::Status SetReaperStrobeEnable(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response);
    virtual ::grpc::Status SetReaperAllStrobesEnable(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response);
    virtual ::grpc::Status SetReaperModulePcPower(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response);
    virtual ::grpc::Status SetReaperModuleLaserPower(::grpc::ServerContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextPowerStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextPowerStatus() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextPowerStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPowerStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::PowerStatusRequest* /*request*/, ::carbon::frontend::power::PowerStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextPowerStatus(::grpc::ServerContext* context, ::carbon::frontend::power::PowerStatusRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::PowerStatusResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_TurnOffDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_TurnOffDevice() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_TurnOffDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TurnOffDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestTurnOffDevice(::grpc::ServerContext* context, ::carbon::frontend::power::RelayRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::RelayResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_TurnOnDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_TurnOnDevice() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_TurnOnDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TurnOnDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestTurnOnDevice(::grpc::ServerContext* context, ::carbon::frontend::power::RelayRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::RelayResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextReaperAllHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextReaperAllHardwareStatus() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_GetNextReaperAllHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextReaperAllHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextReaperAllHardwareStatus(::grpc::ServerContext* context, ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextReaperHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextReaperHardwareStatus() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_GetNextReaperHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextReaperHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextReaperHardwareStatus(::grpc::ServerContext* context, ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetReaperScannerPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetReaperScannerPower() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_SetReaperScannerPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperScannerPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperScannerPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperScannerPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperScannerPower(::grpc::ServerContext* context, ::carbon::frontend::power::SetReaperScannerPowerRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::SetReaperScannerPowerResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetReaperTargetPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetReaperTargetPower() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_SetReaperTargetPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperTargetPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperTargetPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperTargetPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperTargetPower(::grpc::ServerContext* context, ::carbon::frontend::power::SetReaperTargetPowerRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::SetReaperTargetPowerResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetReaperPredictCamPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetReaperPredictCamPower() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_SetReaperPredictCamPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperPredictCamPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperPredictCamPower(::grpc::ServerContext* context, ::carbon::frontend::power::SetReaperPredictCamPowerRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::SetReaperPredictCamPowerResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetReaperStrobeEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetReaperStrobeEnable() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_SetReaperStrobeEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperStrobeEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperStrobeEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperStrobeEnable(::grpc::ServerContext* context, ::carbon::frontend::power::SetReaperStrobeEnableRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::SetReaperStrobeEnableResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetReaperAllStrobesEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetReaperAllStrobesEnable() {
      ::grpc::Service::MarkMethodAsync(9);
    }
    ~WithAsyncMethod_SetReaperAllStrobesEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperAllStrobesEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperAllStrobesEnable(::grpc::ServerContext* context, ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetReaperModulePcPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetReaperModulePcPower() {
      ::grpc::Service::MarkMethodAsync(10);
    }
    ~WithAsyncMethod_SetReaperModulePcPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperModulePcPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModulePcPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperModulePcPower(::grpc::ServerContext* context, ::carbon::frontend::power::SetReaperModulePcPowerRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::SetReaperModulePcPowerResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetReaperModuleLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetReaperModuleLaserPower() {
      ::grpc::Service::MarkMethodAsync(11);
    }
    ~WithAsyncMethod_SetReaperModuleLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperModuleLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperModuleLaserPower(::grpc::ServerContext* context, ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextPowerStatus<WithAsyncMethod_TurnOffDevice<WithAsyncMethod_TurnOnDevice<WithAsyncMethod_GetNextReaperAllHardwareStatus<WithAsyncMethod_GetNextReaperHardwareStatus<WithAsyncMethod_SetReaperScannerPower<WithAsyncMethod_SetReaperTargetPower<WithAsyncMethod_SetReaperPredictCamPower<WithAsyncMethod_SetReaperStrobeEnable<WithAsyncMethod_SetReaperAllStrobesEnable<WithAsyncMethod_SetReaperModulePcPower<WithAsyncMethod_SetReaperModuleLaserPower<Service > > > > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextPowerStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextPowerStatus() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::PowerStatusRequest, ::carbon::frontend::power::PowerStatusResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::PowerStatusRequest* request, ::carbon::frontend::power::PowerStatusResponse* response) { return this->GetNextPowerStatus(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextPowerStatus(
        ::grpc::MessageAllocator< ::carbon::frontend::power::PowerStatusRequest, ::carbon::frontend::power::PowerStatusResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::PowerStatusRequest, ::carbon::frontend::power::PowerStatusResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextPowerStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPowerStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::PowerStatusRequest* /*request*/, ::carbon::frontend::power::PowerStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextPowerStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::PowerStatusRequest* /*request*/, ::carbon::frontend::power::PowerStatusResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_TurnOffDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_TurnOffDevice() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response) { return this->TurnOffDevice(context, request, response); }));}
    void SetMessageAllocatorFor_TurnOffDevice(
        ::grpc::MessageAllocator< ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_TurnOffDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TurnOffDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* TurnOffDevice(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_TurnOnDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_TurnOnDevice() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::RelayRequest* request, ::carbon::frontend::power::RelayResponse* response) { return this->TurnOnDevice(context, request, response); }));}
    void SetMessageAllocatorFor_TurnOnDevice(
        ::grpc::MessageAllocator< ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_TurnOnDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TurnOnDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* TurnOnDevice(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextReaperAllHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextReaperAllHardwareStatus() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* response) { return this->GetNextReaperAllHardwareStatus(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextReaperAllHardwareStatus(
        ::grpc::MessageAllocator< ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextReaperAllHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextReaperAllHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextReaperAllHardwareStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextReaperHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextReaperHardwareStatus() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::GetNextReaperHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* request, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* response) { return this->GetNextReaperHardwareStatus(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextReaperHardwareStatus(
        ::grpc::MessageAllocator< ::carbon::frontend::power::GetNextReaperHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::GetNextReaperHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextReaperHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextReaperHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextReaperHardwareStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetReaperScannerPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetReaperScannerPower() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperScannerPowerRequest, ::carbon::frontend::power::SetReaperScannerPowerResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::SetReaperScannerPowerRequest* request, ::carbon::frontend::power::SetReaperScannerPowerResponse* response) { return this->SetReaperScannerPower(context, request, response); }));}
    void SetMessageAllocatorFor_SetReaperScannerPower(
        ::grpc::MessageAllocator< ::carbon::frontend::power::SetReaperScannerPowerRequest, ::carbon::frontend::power::SetReaperScannerPowerResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperScannerPowerRequest, ::carbon::frontend::power::SetReaperScannerPowerResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetReaperScannerPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperScannerPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperScannerPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperScannerPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperScannerPower(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::SetReaperScannerPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperScannerPowerResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetReaperTargetPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetReaperTargetPower() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperTargetPowerRequest, ::carbon::frontend::power::SetReaperTargetPowerResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::SetReaperTargetPowerRequest* request, ::carbon::frontend::power::SetReaperTargetPowerResponse* response) { return this->SetReaperTargetPower(context, request, response); }));}
    void SetMessageAllocatorFor_SetReaperTargetPower(
        ::grpc::MessageAllocator< ::carbon::frontend::power::SetReaperTargetPowerRequest, ::carbon::frontend::power::SetReaperTargetPowerResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperTargetPowerRequest, ::carbon::frontend::power::SetReaperTargetPowerResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetReaperTargetPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperTargetPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperTargetPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperTargetPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperTargetPower(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::SetReaperTargetPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperTargetPowerResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetReaperPredictCamPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetReaperPredictCamPower() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperPredictCamPowerRequest, ::carbon::frontend::power::SetReaperPredictCamPowerResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* request, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* response) { return this->SetReaperPredictCamPower(context, request, response); }));}
    void SetMessageAllocatorFor_SetReaperPredictCamPower(
        ::grpc::MessageAllocator< ::carbon::frontend::power::SetReaperPredictCamPowerRequest, ::carbon::frontend::power::SetReaperPredictCamPowerResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperPredictCamPowerRequest, ::carbon::frontend::power::SetReaperPredictCamPowerResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetReaperPredictCamPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperPredictCamPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperPredictCamPower(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetReaperStrobeEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetReaperStrobeEnable() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperStrobeEnableRequest, ::carbon::frontend::power::SetReaperStrobeEnableResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* request, ::carbon::frontend::power::SetReaperStrobeEnableResponse* response) { return this->SetReaperStrobeEnable(context, request, response); }));}
    void SetMessageAllocatorFor_SetReaperStrobeEnable(
        ::grpc::MessageAllocator< ::carbon::frontend::power::SetReaperStrobeEnableRequest, ::carbon::frontend::power::SetReaperStrobeEnableResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperStrobeEnableRequest, ::carbon::frontend::power::SetReaperStrobeEnableResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetReaperStrobeEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperStrobeEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperStrobeEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperStrobeEnable(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperStrobeEnableResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetReaperAllStrobesEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetReaperAllStrobesEnable() {
      ::grpc::Service::MarkMethodCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperAllStrobesEnableRequest, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* request, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* response) { return this->SetReaperAllStrobesEnable(context, request, response); }));}
    void SetMessageAllocatorFor_SetReaperAllStrobesEnable(
        ::grpc::MessageAllocator< ::carbon::frontend::power::SetReaperAllStrobesEnableRequest, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(9);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperAllStrobesEnableRequest, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetReaperAllStrobesEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperAllStrobesEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperAllStrobesEnable(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetReaperModulePcPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetReaperModulePcPower() {
      ::grpc::Service::MarkMethodCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperModulePcPowerRequest, ::carbon::frontend::power::SetReaperModulePcPowerResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* request, ::carbon::frontend::power::SetReaperModulePcPowerResponse* response) { return this->SetReaperModulePcPower(context, request, response); }));}
    void SetMessageAllocatorFor_SetReaperModulePcPower(
        ::grpc::MessageAllocator< ::carbon::frontend::power::SetReaperModulePcPowerRequest, ::carbon::frontend::power::SetReaperModulePcPowerResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(10);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperModulePcPowerRequest, ::carbon::frontend::power::SetReaperModulePcPowerResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetReaperModulePcPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperModulePcPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModulePcPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperModulePcPower(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModulePcPowerResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetReaperModuleLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetReaperModuleLaserPower() {
      ::grpc::Service::MarkMethodCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperModuleLaserPowerRequest, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* request, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* response) { return this->SetReaperModuleLaserPower(context, request, response); }));}
    void SetMessageAllocatorFor_SetReaperModuleLaserPower(
        ::grpc::MessageAllocator< ::carbon::frontend::power::SetReaperModuleLaserPowerRequest, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(11);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::power::SetReaperModuleLaserPowerRequest, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetReaperModuleLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperModuleLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperModuleLaserPower(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextPowerStatus<WithCallbackMethod_TurnOffDevice<WithCallbackMethod_TurnOnDevice<WithCallbackMethod_GetNextReaperAllHardwareStatus<WithCallbackMethod_GetNextReaperHardwareStatus<WithCallbackMethod_SetReaperScannerPower<WithCallbackMethod_SetReaperTargetPower<WithCallbackMethod_SetReaperPredictCamPower<WithCallbackMethod_SetReaperStrobeEnable<WithCallbackMethod_SetReaperAllStrobesEnable<WithCallbackMethod_SetReaperModulePcPower<WithCallbackMethod_SetReaperModuleLaserPower<Service > > > > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextPowerStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextPowerStatus() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextPowerStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPowerStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::PowerStatusRequest* /*request*/, ::carbon::frontend::power::PowerStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_TurnOffDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_TurnOffDevice() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_TurnOffDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TurnOffDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_TurnOnDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_TurnOnDevice() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_TurnOnDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TurnOnDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextReaperAllHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextReaperAllHardwareStatus() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_GetNextReaperAllHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextReaperAllHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextReaperHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextReaperHardwareStatus() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_GetNextReaperHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextReaperHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetReaperScannerPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetReaperScannerPower() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_SetReaperScannerPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperScannerPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperScannerPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperScannerPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetReaperTargetPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetReaperTargetPower() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_SetReaperTargetPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperTargetPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperTargetPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperTargetPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetReaperPredictCamPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetReaperPredictCamPower() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_SetReaperPredictCamPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperPredictCamPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetReaperStrobeEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetReaperStrobeEnable() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_SetReaperStrobeEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperStrobeEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperStrobeEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetReaperAllStrobesEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetReaperAllStrobesEnable() {
      ::grpc::Service::MarkMethodGeneric(9);
    }
    ~WithGenericMethod_SetReaperAllStrobesEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperAllStrobesEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetReaperModulePcPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetReaperModulePcPower() {
      ::grpc::Service::MarkMethodGeneric(10);
    }
    ~WithGenericMethod_SetReaperModulePcPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperModulePcPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModulePcPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetReaperModuleLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetReaperModuleLaserPower() {
      ::grpc::Service::MarkMethodGeneric(11);
    }
    ~WithGenericMethod_SetReaperModuleLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperModuleLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextPowerStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextPowerStatus() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextPowerStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPowerStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::PowerStatusRequest* /*request*/, ::carbon::frontend::power::PowerStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextPowerStatus(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_TurnOffDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_TurnOffDevice() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_TurnOffDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TurnOffDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestTurnOffDevice(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_TurnOnDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_TurnOnDevice() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_TurnOnDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TurnOnDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestTurnOnDevice(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextReaperAllHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextReaperAllHardwareStatus() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_GetNextReaperAllHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextReaperAllHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextReaperAllHardwareStatus(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextReaperHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextReaperHardwareStatus() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_GetNextReaperHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextReaperHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextReaperHardwareStatus(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetReaperScannerPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetReaperScannerPower() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_SetReaperScannerPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperScannerPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperScannerPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperScannerPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperScannerPower(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetReaperTargetPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetReaperTargetPower() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_SetReaperTargetPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperTargetPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperTargetPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperTargetPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperTargetPower(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetReaperPredictCamPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetReaperPredictCamPower() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_SetReaperPredictCamPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperPredictCamPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperPredictCamPower(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetReaperStrobeEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetReaperStrobeEnable() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_SetReaperStrobeEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperStrobeEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperStrobeEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperStrobeEnable(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetReaperAllStrobesEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetReaperAllStrobesEnable() {
      ::grpc::Service::MarkMethodRaw(9);
    }
    ~WithRawMethod_SetReaperAllStrobesEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperAllStrobesEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperAllStrobesEnable(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetReaperModulePcPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetReaperModulePcPower() {
      ::grpc::Service::MarkMethodRaw(10);
    }
    ~WithRawMethod_SetReaperModulePcPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperModulePcPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModulePcPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperModulePcPower(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetReaperModuleLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetReaperModuleLaserPower() {
      ::grpc::Service::MarkMethodRaw(11);
    }
    ~WithRawMethod_SetReaperModuleLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperModuleLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetReaperModuleLaserPower(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextPowerStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextPowerStatus() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextPowerStatus(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextPowerStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPowerStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::PowerStatusRequest* /*request*/, ::carbon::frontend::power::PowerStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextPowerStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_TurnOffDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_TurnOffDevice() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->TurnOffDevice(context, request, response); }));
    }
    ~WithRawCallbackMethod_TurnOffDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TurnOffDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* TurnOffDevice(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_TurnOnDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_TurnOnDevice() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->TurnOnDevice(context, request, response); }));
    }
    ~WithRawCallbackMethod_TurnOnDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TurnOnDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* TurnOnDevice(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextReaperAllHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextReaperAllHardwareStatus() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextReaperAllHardwareStatus(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextReaperAllHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextReaperAllHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextReaperAllHardwareStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextReaperHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextReaperHardwareStatus() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextReaperHardwareStatus(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextReaperHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextReaperHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextReaperHardwareStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetReaperScannerPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetReaperScannerPower() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetReaperScannerPower(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetReaperScannerPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperScannerPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperScannerPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperScannerPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperScannerPower(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetReaperTargetPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetReaperTargetPower() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetReaperTargetPower(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetReaperTargetPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperTargetPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperTargetPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperTargetPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperTargetPower(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetReaperPredictCamPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetReaperPredictCamPower() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetReaperPredictCamPower(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetReaperPredictCamPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperPredictCamPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperPredictCamPower(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetReaperStrobeEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetReaperStrobeEnable() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetReaperStrobeEnable(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetReaperStrobeEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperStrobeEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperStrobeEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperStrobeEnable(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetReaperAllStrobesEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetReaperAllStrobesEnable() {
      ::grpc::Service::MarkMethodRawCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetReaperAllStrobesEnable(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetReaperAllStrobesEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperAllStrobesEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperAllStrobesEnable(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetReaperModulePcPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetReaperModulePcPower() {
      ::grpc::Service::MarkMethodRawCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetReaperModulePcPower(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetReaperModulePcPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperModulePcPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModulePcPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperModulePcPower(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetReaperModuleLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetReaperModuleLaserPower() {
      ::grpc::Service::MarkMethodRawCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetReaperModuleLaserPower(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetReaperModuleLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetReaperModuleLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetReaperModuleLaserPower(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextPowerStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextPowerStatus() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::PowerStatusRequest, ::carbon::frontend::power::PowerStatusResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::PowerStatusRequest, ::carbon::frontend::power::PowerStatusResponse>* streamer) {
                       return this->StreamedGetNextPowerStatus(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextPowerStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextPowerStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::PowerStatusRequest* /*request*/, ::carbon::frontend::power::PowerStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextPowerStatus(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::PowerStatusRequest,::carbon::frontend::power::PowerStatusResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_TurnOffDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_TurnOffDevice() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse>* streamer) {
                       return this->StreamedTurnOffDevice(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_TurnOffDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status TurnOffDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedTurnOffDevice(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::RelayRequest,::carbon::frontend::power::RelayResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_TurnOnDevice : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_TurnOnDevice() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::RelayRequest, ::carbon::frontend::power::RelayResponse>* streamer) {
                       return this->StreamedTurnOnDevice(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_TurnOnDevice() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status TurnOnDevice(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::RelayRequest* /*request*/, ::carbon::frontend::power::RelayResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedTurnOnDevice(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::RelayRequest,::carbon::frontend::power::RelayResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextReaperAllHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextReaperAllHardwareStatus() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>* streamer) {
                       return this->StreamedGetNextReaperAllHardwareStatus(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextReaperAllHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextReaperAllHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextReaperAllHardwareStatus(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::GetNextReaperAllHardwareStatusRequest,::carbon::frontend::power::GetNextReaperAllHardwareStatusResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextReaperHardwareStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextReaperHardwareStatus() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::GetNextReaperHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::GetNextReaperHardwareStatusRequest, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse>* streamer) {
                       return this->StreamedGetNextReaperHardwareStatus(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextReaperHardwareStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextReaperHardwareStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::GetNextReaperHardwareStatusRequest* /*request*/, ::carbon::frontend::power::GetNextReaperHardwareStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextReaperHardwareStatus(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::GetNextReaperHardwareStatusRequest,::carbon::frontend::power::GetNextReaperHardwareStatusResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetReaperScannerPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetReaperScannerPower() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::SetReaperScannerPowerRequest, ::carbon::frontend::power::SetReaperScannerPowerResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::SetReaperScannerPowerRequest, ::carbon::frontend::power::SetReaperScannerPowerResponse>* streamer) {
                       return this->StreamedSetReaperScannerPower(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetReaperScannerPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetReaperScannerPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperScannerPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperScannerPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetReaperScannerPower(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::SetReaperScannerPowerRequest,::carbon::frontend::power::SetReaperScannerPowerResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetReaperTargetPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetReaperTargetPower() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::SetReaperTargetPowerRequest, ::carbon::frontend::power::SetReaperTargetPowerResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::SetReaperTargetPowerRequest, ::carbon::frontend::power::SetReaperTargetPowerResponse>* streamer) {
                       return this->StreamedSetReaperTargetPower(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetReaperTargetPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetReaperTargetPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperTargetPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperTargetPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetReaperTargetPower(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::SetReaperTargetPowerRequest,::carbon::frontend::power::SetReaperTargetPowerResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetReaperPredictCamPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetReaperPredictCamPower() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::SetReaperPredictCamPowerRequest, ::carbon::frontend::power::SetReaperPredictCamPowerResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::SetReaperPredictCamPowerRequest, ::carbon::frontend::power::SetReaperPredictCamPowerResponse>* streamer) {
                       return this->StreamedSetReaperPredictCamPower(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetReaperPredictCamPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetReaperPredictCamPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperPredictCamPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperPredictCamPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetReaperPredictCamPower(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::SetReaperPredictCamPowerRequest,::carbon::frontend::power::SetReaperPredictCamPowerResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetReaperStrobeEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetReaperStrobeEnable() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::SetReaperStrobeEnableRequest, ::carbon::frontend::power::SetReaperStrobeEnableResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::SetReaperStrobeEnableRequest, ::carbon::frontend::power::SetReaperStrobeEnableResponse>* streamer) {
                       return this->StreamedSetReaperStrobeEnable(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetReaperStrobeEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetReaperStrobeEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperStrobeEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperStrobeEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetReaperStrobeEnable(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::SetReaperStrobeEnableRequest,::carbon::frontend::power::SetReaperStrobeEnableResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetReaperAllStrobesEnable : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetReaperAllStrobesEnable() {
      ::grpc::Service::MarkMethodStreamed(9,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::SetReaperAllStrobesEnableRequest, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::SetReaperAllStrobesEnableRequest, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse>* streamer) {
                       return this->StreamedSetReaperAllStrobesEnable(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetReaperAllStrobesEnable() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetReaperAllStrobesEnable(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperAllStrobesEnableRequest* /*request*/, ::carbon::frontend::power::SetReaperAllStrobesEnableResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetReaperAllStrobesEnable(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::SetReaperAllStrobesEnableRequest,::carbon::frontend::power::SetReaperAllStrobesEnableResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetReaperModulePcPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetReaperModulePcPower() {
      ::grpc::Service::MarkMethodStreamed(10,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::SetReaperModulePcPowerRequest, ::carbon::frontend::power::SetReaperModulePcPowerResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::SetReaperModulePcPowerRequest, ::carbon::frontend::power::SetReaperModulePcPowerResponse>* streamer) {
                       return this->StreamedSetReaperModulePcPower(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetReaperModulePcPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetReaperModulePcPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModulePcPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModulePcPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetReaperModulePcPower(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::SetReaperModulePcPowerRequest,::carbon::frontend::power::SetReaperModulePcPowerResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetReaperModuleLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetReaperModuleLaserPower() {
      ::grpc::Service::MarkMethodStreamed(11,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::power::SetReaperModuleLaserPowerRequest, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::power::SetReaperModuleLaserPowerRequest, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse>* streamer) {
                       return this->StreamedSetReaperModuleLaserPower(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetReaperModuleLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetReaperModuleLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::power::SetReaperModuleLaserPowerRequest* /*request*/, ::carbon::frontend::power::SetReaperModuleLaserPowerResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetReaperModuleLaserPower(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::power::SetReaperModuleLaserPowerRequest,::carbon::frontend::power::SetReaperModuleLaserPowerResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextPowerStatus<WithStreamedUnaryMethod_TurnOffDevice<WithStreamedUnaryMethod_TurnOnDevice<WithStreamedUnaryMethod_GetNextReaperAllHardwareStatus<WithStreamedUnaryMethod_GetNextReaperHardwareStatus<WithStreamedUnaryMethod_SetReaperScannerPower<WithStreamedUnaryMethod_SetReaperTargetPower<WithStreamedUnaryMethod_SetReaperPredictCamPower<WithStreamedUnaryMethod_SetReaperStrobeEnable<WithStreamedUnaryMethod_SetReaperAllStrobesEnable<WithStreamedUnaryMethod_SetReaperModulePcPower<WithStreamedUnaryMethod_SetReaperModuleLaserPower<Service > > > > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextPowerStatus<WithStreamedUnaryMethod_TurnOffDevice<WithStreamedUnaryMethod_TurnOnDevice<WithStreamedUnaryMethod_GetNextReaperAllHardwareStatus<WithStreamedUnaryMethod_GetNextReaperHardwareStatus<WithStreamedUnaryMethod_SetReaperScannerPower<WithStreamedUnaryMethod_SetReaperTargetPower<WithStreamedUnaryMethod_SetReaperPredictCamPower<WithStreamedUnaryMethod_SetReaperStrobeEnable<WithStreamedUnaryMethod_SetReaperAllStrobesEnable<WithStreamedUnaryMethod_SetReaperModulePcPower<WithStreamedUnaryMethod_SetReaperModuleLaserPower<Service > > > > > > > > > > > > StreamedService;
};

}  // namespace power
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fpower_2eproto__INCLUDED

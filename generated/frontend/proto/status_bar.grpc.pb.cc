// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/status_bar.proto

#include "frontend/proto/status_bar.pb.h"
#include "frontend/proto/status_bar.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace status_bar {

static const char* StatusBarService_method_names[] = {
  "/carbon.frontend.status_bar.StatusBarService/GetNextStatus",
  "/carbon.frontend.status_bar.StatusBarService/ReportIssue",
  "/carbon.frontend.status_bar.StatusBarService/GetSupportPhone",
};

std::unique_ptr< StatusBarService::Stub> StatusBarService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< StatusBarService::Stub> stub(new StatusBarService::Stub(channel, options));
  return stub;
}

StatusBarService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextStatus_(StatusBarService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReportIssue_(StatusBarService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSupportPhone_(StatusBarService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status StatusBarService::Stub::GetNextStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::status_bar::StatusBarMessage* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::status_bar::StatusBarMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextStatus_, context, request, response);
}

void StatusBarService::Stub::async::GetNextStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::status_bar::StatusBarMessage* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::status_bar::StatusBarMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextStatus_, context, request, response, std::move(f));
}

void StatusBarService::Stub::async::GetNextStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::status_bar::StatusBarMessage* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::status_bar::StatusBarMessage>* StatusBarService::Stub::PrepareAsyncGetNextStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::status_bar::StatusBarMessage, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::status_bar::StatusBarMessage>* StatusBarService::Stub::AsyncGetNextStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status StatusBarService::Stub::ReportIssue(::grpc::ClientContext* context, const ::carbon::frontend::status_bar::ReportIssueRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::status_bar::ReportIssueRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReportIssue_, context, request, response);
}

void StatusBarService::Stub::async::ReportIssue(::grpc::ClientContext* context, const ::carbon::frontend::status_bar::ReportIssueRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::status_bar::ReportIssueRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReportIssue_, context, request, response, std::move(f));
}

void StatusBarService::Stub::async::ReportIssue(::grpc::ClientContext* context, const ::carbon::frontend::status_bar::ReportIssueRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReportIssue_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* StatusBarService::Stub::PrepareAsyncReportIssueRaw(::grpc::ClientContext* context, const ::carbon::frontend::status_bar::ReportIssueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::status_bar::ReportIssueRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReportIssue_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* StatusBarService::Stub::AsyncReportIssueRaw(::grpc::ClientContext* context, const ::carbon::frontend::status_bar::ReportIssueRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReportIssueRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status StatusBarService::Stub::GetSupportPhone(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::status_bar::SupportPhoneMessage* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::status_bar::SupportPhoneMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSupportPhone_, context, request, response);
}

void StatusBarService::Stub::async::GetSupportPhone(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::status_bar::SupportPhoneMessage* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::status_bar::SupportPhoneMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSupportPhone_, context, request, response, std::move(f));
}

void StatusBarService::Stub::async::GetSupportPhone(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::status_bar::SupportPhoneMessage* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSupportPhone_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::status_bar::SupportPhoneMessage>* StatusBarService::Stub::PrepareAsyncGetSupportPhoneRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::status_bar::SupportPhoneMessage, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSupportPhone_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::status_bar::SupportPhoneMessage>* StatusBarService::Stub::AsyncGetSupportPhoneRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSupportPhoneRaw(context, request, cq);
  result->StartCall();
  return result;
}

StatusBarService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      StatusBarService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< StatusBarService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::status_bar::StatusBarMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](StatusBarService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::status_bar::StatusBarMessage* resp) {
               return service->GetNextStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      StatusBarService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< StatusBarService::Service, ::carbon::frontend::status_bar::ReportIssueRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](StatusBarService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::status_bar::ReportIssueRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ReportIssue(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      StatusBarService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< StatusBarService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::status_bar::SupportPhoneMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](StatusBarService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::status_bar::SupportPhoneMessage* resp) {
               return service->GetSupportPhone(ctx, req, resp);
             }, this)));
}

StatusBarService::Service::~Service() {
}

::grpc::Status StatusBarService::Service::GetNextStatus(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::status_bar::StatusBarMessage* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status StatusBarService::Service::ReportIssue(::grpc::ServerContext* context, const ::carbon::frontend::status_bar::ReportIssueRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status StatusBarService::Service::GetSupportPhone(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::status_bar::SupportPhoneMessage* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace status_bar


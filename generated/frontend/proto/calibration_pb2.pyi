"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class ColorCalibrationValues(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    red: builtin___float = ...
    green: builtin___float = ...
    blue: builtin___float = ...
    cam_id: typing___Text = ...

    def __init__(self,
        *,
        red : typing___Optional[builtin___float] = None,
        green : typing___Optional[builtin___float] = None,
        blue : typing___Optional[builtin___float] = None,
        cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"blue",b"blue",u"cam_id",b"cam_id",u"green",b"green",u"red",b"red"]) -> None: ...
type___ColorCalibrationValues = ColorCalibrationValues

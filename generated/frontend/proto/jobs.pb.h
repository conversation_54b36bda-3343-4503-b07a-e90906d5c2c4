// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/jobs.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fjobs_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fjobs_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
#include "frontend/proto/weeding_diagnostics.pb.h"
#include "metrics/proto/metrics_aggregator_service.pb.h"
#include "frontend/proto/profile_sync.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fjobs_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fjobs_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[23]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fjobs_2eproto;
namespace carbon {
namespace frontend {
namespace jobs {
class ActiveProfile;
struct ActiveProfileDefaultTypeInternal;
extern ActiveProfileDefaultTypeInternal _ActiveProfile_default_instance_;
class CreateJobRequest;
struct CreateJobRequestDefaultTypeInternal;
extern CreateJobRequestDefaultTypeInternal _CreateJobRequest_default_instance_;
class CreateJobResponse;
struct CreateJobResponseDefaultTypeInternal;
extern CreateJobResponseDefaultTypeInternal _CreateJobResponse_default_instance_;
class DeleteJobRequest;
struct DeleteJobRequestDefaultTypeInternal;
extern DeleteJobRequestDefaultTypeInternal _DeleteJobRequest_default_instance_;
class GetActiveJobMetricsResponse;
struct GetActiveJobMetricsResponseDefaultTypeInternal;
extern GetActiveJobMetricsResponseDefaultTypeInternal _GetActiveJobMetricsResponse_default_instance_;
class GetConfigDumpRequest;
struct GetConfigDumpRequestDefaultTypeInternal;
extern GetConfigDumpRequestDefaultTypeInternal _GetConfigDumpRequest_default_instance_;
class GetConfigDumpResponse;
struct GetConfigDumpResponseDefaultTypeInternal;
extern GetConfigDumpResponseDefaultTypeInternal _GetConfigDumpResponse_default_instance_;
class GetJobRequest;
struct GetJobRequestDefaultTypeInternal;
extern GetJobRequestDefaultTypeInternal _GetJobRequest_default_instance_;
class GetJobResponse;
struct GetJobResponseDefaultTypeInternal;
extern GetJobResponseDefaultTypeInternal _GetJobResponse_default_instance_;
class GetNextActiveJobIdRequest;
struct GetNextActiveJobIdRequestDefaultTypeInternal;
extern GetNextActiveJobIdRequestDefaultTypeInternal _GetNextActiveJobIdRequest_default_instance_;
class GetNextActiveJobIdResponse;
struct GetNextActiveJobIdResponseDefaultTypeInternal;
extern GetNextActiveJobIdResponseDefaultTypeInternal _GetNextActiveJobIdResponse_default_instance_;
class GetNextJobRequest;
struct GetNextJobRequestDefaultTypeInternal;
extern GetNextJobRequestDefaultTypeInternal _GetNextJobRequest_default_instance_;
class GetNextJobResponse;
struct GetNextJobResponseDefaultTypeInternal;
extern GetNextJobResponseDefaultTypeInternal _GetNextJobResponse_default_instance_;
class GetNextJobsRequest;
struct GetNextJobsRequestDefaultTypeInternal;
extern GetNextJobsRequestDefaultTypeInternal _GetNextJobsRequest_default_instance_;
class GetNextJobsResponse;
struct GetNextJobsResponseDefaultTypeInternal;
extern GetNextJobsResponseDefaultTypeInternal _GetNextJobsResponse_default_instance_;
class Job;
struct JobDefaultTypeInternal;
extern JobDefaultTypeInternal _Job_default_instance_;
class JobDescription;
struct JobDescriptionDefaultTypeInternal;
extern JobDescriptionDefaultTypeInternal _JobDescription_default_instance_;
class JobWithMetrics;
struct JobWithMetricsDefaultTypeInternal;
extern JobWithMetricsDefaultTypeInternal _JobWithMetrics_default_instance_;
class Job_ActiveProfilesEntry_DoNotUse;
struct Job_ActiveProfilesEntry_DoNotUseDefaultTypeInternal;
extern Job_ActiveProfilesEntry_DoNotUseDefaultTypeInternal _Job_ActiveProfilesEntry_DoNotUse_default_instance_;
class MarkJobCompletedRequest;
struct MarkJobCompletedRequestDefaultTypeInternal;
extern MarkJobCompletedRequestDefaultTypeInternal _MarkJobCompletedRequest_default_instance_;
class MarkJobIncompleteRequest;
struct MarkJobIncompleteRequestDefaultTypeInternal;
extern MarkJobIncompleteRequestDefaultTypeInternal _MarkJobIncompleteRequest_default_instance_;
class StartJobRequest;
struct StartJobRequestDefaultTypeInternal;
extern StartJobRequestDefaultTypeInternal _StartJobRequest_default_instance_;
class UpdateJobRequest;
struct UpdateJobRequestDefaultTypeInternal;
extern UpdateJobRequestDefaultTypeInternal _UpdateJobRequest_default_instance_;
}  // namespace jobs
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::jobs::ActiveProfile* Arena::CreateMaybeMessage<::carbon::frontend::jobs::ActiveProfile>(Arena*);
template<> ::carbon::frontend::jobs::CreateJobRequest* Arena::CreateMaybeMessage<::carbon::frontend::jobs::CreateJobRequest>(Arena*);
template<> ::carbon::frontend::jobs::CreateJobResponse* Arena::CreateMaybeMessage<::carbon::frontend::jobs::CreateJobResponse>(Arena*);
template<> ::carbon::frontend::jobs::DeleteJobRequest* Arena::CreateMaybeMessage<::carbon::frontend::jobs::DeleteJobRequest>(Arena*);
template<> ::carbon::frontend::jobs::GetActiveJobMetricsResponse* Arena::CreateMaybeMessage<::carbon::frontend::jobs::GetActiveJobMetricsResponse>(Arena*);
template<> ::carbon::frontend::jobs::GetConfigDumpRequest* Arena::CreateMaybeMessage<::carbon::frontend::jobs::GetConfigDumpRequest>(Arena*);
template<> ::carbon::frontend::jobs::GetConfigDumpResponse* Arena::CreateMaybeMessage<::carbon::frontend::jobs::GetConfigDumpResponse>(Arena*);
template<> ::carbon::frontend::jobs::GetJobRequest* Arena::CreateMaybeMessage<::carbon::frontend::jobs::GetJobRequest>(Arena*);
template<> ::carbon::frontend::jobs::GetJobResponse* Arena::CreateMaybeMessage<::carbon::frontend::jobs::GetJobResponse>(Arena*);
template<> ::carbon::frontend::jobs::GetNextActiveJobIdRequest* Arena::CreateMaybeMessage<::carbon::frontend::jobs::GetNextActiveJobIdRequest>(Arena*);
template<> ::carbon::frontend::jobs::GetNextActiveJobIdResponse* Arena::CreateMaybeMessage<::carbon::frontend::jobs::GetNextActiveJobIdResponse>(Arena*);
template<> ::carbon::frontend::jobs::GetNextJobRequest* Arena::CreateMaybeMessage<::carbon::frontend::jobs::GetNextJobRequest>(Arena*);
template<> ::carbon::frontend::jobs::GetNextJobResponse* Arena::CreateMaybeMessage<::carbon::frontend::jobs::GetNextJobResponse>(Arena*);
template<> ::carbon::frontend::jobs::GetNextJobsRequest* Arena::CreateMaybeMessage<::carbon::frontend::jobs::GetNextJobsRequest>(Arena*);
template<> ::carbon::frontend::jobs::GetNextJobsResponse* Arena::CreateMaybeMessage<::carbon::frontend::jobs::GetNextJobsResponse>(Arena*);
template<> ::carbon::frontend::jobs::Job* Arena::CreateMaybeMessage<::carbon::frontend::jobs::Job>(Arena*);
template<> ::carbon::frontend::jobs::JobDescription* Arena::CreateMaybeMessage<::carbon::frontend::jobs::JobDescription>(Arena*);
template<> ::carbon::frontend::jobs::JobWithMetrics* Arena::CreateMaybeMessage<::carbon::frontend::jobs::JobWithMetrics>(Arena*);
template<> ::carbon::frontend::jobs::Job_ActiveProfilesEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::jobs::Job_ActiveProfilesEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::jobs::MarkJobCompletedRequest* Arena::CreateMaybeMessage<::carbon::frontend::jobs::MarkJobCompletedRequest>(Arena*);
template<> ::carbon::frontend::jobs::MarkJobIncompleteRequest* Arena::CreateMaybeMessage<::carbon::frontend::jobs::MarkJobIncompleteRequest>(Arena*);
template<> ::carbon::frontend::jobs::StartJobRequest* Arena::CreateMaybeMessage<::carbon::frontend::jobs::StartJobRequest>(Arena*);
template<> ::carbon::frontend::jobs::UpdateJobRequest* Arena::CreateMaybeMessage<::carbon::frontend::jobs::UpdateJobRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace jobs {

// ===================================================================

class JobDescription final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.JobDescription) */ {
 public:
  inline JobDescription() : JobDescription(nullptr) {}
  ~JobDescription() override;
  explicit constexpr JobDescription(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  JobDescription(const JobDescription& from);
  JobDescription(JobDescription&& from) noexcept
    : JobDescription() {
    *this = ::std::move(from);
  }

  inline JobDescription& operator=(const JobDescription& from) {
    CopyFrom(from);
    return *this;
  }
  inline JobDescription& operator=(JobDescription&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const JobDescription& default_instance() {
    return *internal_default_instance();
  }
  static inline const JobDescription* internal_default_instance() {
    return reinterpret_cast<const JobDescription*>(
               &_JobDescription_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(JobDescription& a, JobDescription& b) {
    a.Swap(&b);
  }
  inline void Swap(JobDescription* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(JobDescription* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  JobDescription* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<JobDescription>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const JobDescription& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const JobDescription& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(JobDescription* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.JobDescription";
  }
  protected:
  explicit JobDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 1,
    kNameFieldNumber = 2,
    kTimestampMsFieldNumber = 3,
  };
  // string jobId = 1;
  void clear_jobid();
  const std::string& jobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_jobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_jobid();
  PROTOBUF_NODISCARD std::string* release_jobid();
  void set_allocated_jobid(std::string* jobid);
  private:
  const std::string& _internal_jobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_jobid(const std::string& value);
  std::string* _internal_mutable_jobid();
  public:

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int64 timestampMs = 3;
  void clear_timestampms();
  int64_t timestampms() const;
  void set_timestampms(int64_t value);
  private:
  int64_t _internal_timestampms() const;
  void _internal_set_timestampms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.JobDescription)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr jobid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  int64_t timestampms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class ActiveProfile final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.ActiveProfile) */ {
 public:
  inline ActiveProfile() : ActiveProfile(nullptr) {}
  ~ActiveProfile() override;
  explicit constexpr ActiveProfile(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ActiveProfile(const ActiveProfile& from);
  ActiveProfile(ActiveProfile&& from) noexcept
    : ActiveProfile() {
    *this = ::std::move(from);
  }

  inline ActiveProfile& operator=(const ActiveProfile& from) {
    CopyFrom(from);
    return *this;
  }
  inline ActiveProfile& operator=(ActiveProfile&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ActiveProfile& default_instance() {
    return *internal_default_instance();
  }
  static inline const ActiveProfile* internal_default_instance() {
    return reinterpret_cast<const ActiveProfile*>(
               &_ActiveProfile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ActiveProfile& a, ActiveProfile& b) {
    a.Swap(&b);
  }
  inline void Swap(ActiveProfile* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ActiveProfile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ActiveProfile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ActiveProfile>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ActiveProfile& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ActiveProfile& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ActiveProfile* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.ActiveProfile";
  }
  protected:
  explicit ActiveProfile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 2,
    kNameFieldNumber = 3,
    kProfileTypeFieldNumber = 1,
  };
  // string id = 2;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string name = 3;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
  void clear_profile_type();
  ::carbon::frontend::profile_sync::ProfileType profile_type() const;
  void set_profile_type(::carbon::frontend::profile_sync::ProfileType value);
  private:
  ::carbon::frontend::profile_sync::ProfileType _internal_profile_type() const;
  void _internal_set_profile_type(::carbon::frontend::profile_sync::ProfileType value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.ActiveProfile)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  int profile_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class Job_ActiveProfilesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Job_ActiveProfilesEntry_DoNotUse, 
    int32_t, ::carbon::frontend::jobs::ActiveProfile,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Job_ActiveProfilesEntry_DoNotUse, 
    int32_t, ::carbon::frontend::jobs::ActiveProfile,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  Job_ActiveProfilesEntry_DoNotUse();
  explicit constexpr Job_ActiveProfilesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit Job_ActiveProfilesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const Job_ActiveProfilesEntry_DoNotUse& other);
  static const Job_ActiveProfilesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Job_ActiveProfilesEntry_DoNotUse*>(&_Job_ActiveProfilesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class Job final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.Job) */ {
 public:
  inline Job() : Job(nullptr) {}
  ~Job() override;
  explicit constexpr Job(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Job(const Job& from);
  Job(Job&& from) noexcept
    : Job() {
    *this = ::std::move(from);
  }

  inline Job& operator=(const Job& from) {
    CopyFrom(from);
    return *this;
  }
  inline Job& operator=(Job&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Job& default_instance() {
    return *internal_default_instance();
  }
  static inline const Job* internal_default_instance() {
    return reinterpret_cast<const Job*>(
               &_Job_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Job& a, Job& b) {
    a.Swap(&b);
  }
  inline void Swap(Job* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Job* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Job* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Job>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Job& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Job& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Job* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.Job";
  }
  protected:
  explicit Job(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kActiveProfilesFieldNumber = 15,
    kBandingProfileFieldNumber = 2,
    kThinningProfileFieldNumber = 3,
    kAlmanacFieldNumber = 8,
    kDiscriminatorFieldNumber = 9,
    kCropIdFieldNumber = 10,
    kBandingProfileUUIDFieldNumber = 11,
    kThinningProfileUUIDFieldNumber = 12,
    kAlmanacProfileUUIDFieldNumber = 13,
    kDiscriminatorProfileUUIDFieldNumber = 14,
    kJobDescriptionFieldNumber = 1,
    kStopTimeMsFieldNumber = 4,
    kLastUpdateTimeMsFieldNumber = 5,
    kExpectedAcreageFieldNumber = 6,
    kCompletedFieldNumber = 7,
    kLastUsedTimeMsFieldNumber = 16,
  };
  // map<int32, .carbon.frontend.jobs.ActiveProfile> active_profiles = 15;
  int active_profiles_size() const;
  private:
  int _internal_active_profiles_size() const;
  public:
  void clear_active_profiles();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >&
      _internal_active_profiles() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >*
      _internal_mutable_active_profiles();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >&
      active_profiles() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >*
      mutable_active_profiles();

  // string bandingProfile = 2;
  void clear_bandingprofile();
  const std::string& bandingprofile() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bandingprofile(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bandingprofile();
  PROTOBUF_NODISCARD std::string* release_bandingprofile();
  void set_allocated_bandingprofile(std::string* bandingprofile);
  private:
  const std::string& _internal_bandingprofile() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bandingprofile(const std::string& value);
  std::string* _internal_mutable_bandingprofile();
  public:

  // string thinningProfile = 3;
  void clear_thinningprofile();
  const std::string& thinningprofile() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_thinningprofile(ArgT0&& arg0, ArgT... args);
  std::string* mutable_thinningprofile();
  PROTOBUF_NODISCARD std::string* release_thinningprofile();
  void set_allocated_thinningprofile(std::string* thinningprofile);
  private:
  const std::string& _internal_thinningprofile() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_thinningprofile(const std::string& value);
  std::string* _internal_mutable_thinningprofile();
  public:

  // string almanac = 8;
  void clear_almanac();
  const std::string& almanac() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_almanac(ArgT0&& arg0, ArgT... args);
  std::string* mutable_almanac();
  PROTOBUF_NODISCARD std::string* release_almanac();
  void set_allocated_almanac(std::string* almanac);
  private:
  const std::string& _internal_almanac() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_almanac(const std::string& value);
  std::string* _internal_mutable_almanac();
  public:

  // string discriminator = 9;
  void clear_discriminator();
  const std::string& discriminator() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_discriminator(ArgT0&& arg0, ArgT... args);
  std::string* mutable_discriminator();
  PROTOBUF_NODISCARD std::string* release_discriminator();
  void set_allocated_discriminator(std::string* discriminator);
  private:
  const std::string& _internal_discriminator() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_discriminator(const std::string& value);
  std::string* _internal_mutable_discriminator();
  public:

  // string crop_id = 10;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // string bandingProfileUUID = 11;
  void clear_bandingprofileuuid();
  const std::string& bandingprofileuuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bandingprofileuuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bandingprofileuuid();
  PROTOBUF_NODISCARD std::string* release_bandingprofileuuid();
  void set_allocated_bandingprofileuuid(std::string* bandingprofileuuid);
  private:
  const std::string& _internal_bandingprofileuuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bandingprofileuuid(const std::string& value);
  std::string* _internal_mutable_bandingprofileuuid();
  public:

  // string thinningProfileUUID = 12;
  void clear_thinningprofileuuid();
  const std::string& thinningprofileuuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_thinningprofileuuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_thinningprofileuuid();
  PROTOBUF_NODISCARD std::string* release_thinningprofileuuid();
  void set_allocated_thinningprofileuuid(std::string* thinningprofileuuid);
  private:
  const std::string& _internal_thinningprofileuuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_thinningprofileuuid(const std::string& value);
  std::string* _internal_mutable_thinningprofileuuid();
  public:

  // string almanacProfileUUID = 13;
  void clear_almanacprofileuuid();
  const std::string& almanacprofileuuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_almanacprofileuuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_almanacprofileuuid();
  PROTOBUF_NODISCARD std::string* release_almanacprofileuuid();
  void set_allocated_almanacprofileuuid(std::string* almanacprofileuuid);
  private:
  const std::string& _internal_almanacprofileuuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_almanacprofileuuid(const std::string& value);
  std::string* _internal_mutable_almanacprofileuuid();
  public:

  // string discriminatorProfileUUID = 14;
  void clear_discriminatorprofileuuid();
  const std::string& discriminatorprofileuuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_discriminatorprofileuuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_discriminatorprofileuuid();
  PROTOBUF_NODISCARD std::string* release_discriminatorprofileuuid();
  void set_allocated_discriminatorprofileuuid(std::string* discriminatorprofileuuid);
  private:
  const std::string& _internal_discriminatorprofileuuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_discriminatorprofileuuid(const std::string& value);
  std::string* _internal_mutable_discriminatorprofileuuid();
  public:

  // .carbon.frontend.jobs.JobDescription jobDescription = 1;
  bool has_jobdescription() const;
  private:
  bool _internal_has_jobdescription() const;
  public:
  void clear_jobdescription();
  const ::carbon::frontend::jobs::JobDescription& jobdescription() const;
  PROTOBUF_NODISCARD ::carbon::frontend::jobs::JobDescription* release_jobdescription();
  ::carbon::frontend::jobs::JobDescription* mutable_jobdescription();
  void set_allocated_jobdescription(::carbon::frontend::jobs::JobDescription* jobdescription);
  private:
  const ::carbon::frontend::jobs::JobDescription& _internal_jobdescription() const;
  ::carbon::frontend::jobs::JobDescription* _internal_mutable_jobdescription();
  public:
  void unsafe_arena_set_allocated_jobdescription(
      ::carbon::frontend::jobs::JobDescription* jobdescription);
  ::carbon::frontend::jobs::JobDescription* unsafe_arena_release_jobdescription();

  // int64 stopTimeMs = 4;
  void clear_stoptimems();
  int64_t stoptimems() const;
  void set_stoptimems(int64_t value);
  private:
  int64_t _internal_stoptimems() const;
  void _internal_set_stoptimems(int64_t value);
  public:

  // int64 lastUpdateTimeMs = 5;
  void clear_lastupdatetimems();
  int64_t lastupdatetimems() const;
  void set_lastupdatetimems(int64_t value);
  private:
  int64_t _internal_lastupdatetimems() const;
  void _internal_set_lastupdatetimems(int64_t value);
  public:

  // float expectedAcreage = 6;
  void clear_expectedacreage();
  float expectedacreage() const;
  void set_expectedacreage(float value);
  private:
  float _internal_expectedacreage() const;
  void _internal_set_expectedacreage(float value);
  public:

  // bool completed = 7;
  void clear_completed();
  bool completed() const;
  void set_completed(bool value);
  private:
  bool _internal_completed() const;
  void _internal_set_completed(bool value);
  public:

  // int64 lastUsedTimeMs = 16;
  void clear_lastusedtimems();
  int64_t lastusedtimems() const;
  void set_lastusedtimems(int64_t value);
  private:
  int64_t _internal_lastusedtimems() const;
  void _internal_set_lastusedtimems(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.Job)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      Job_ActiveProfilesEntry_DoNotUse,
      int32_t, ::carbon::frontend::jobs::ActiveProfile,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> active_profiles_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bandingprofile_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr thinningprofile_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr almanac_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr discriminator_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bandingprofileuuid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr thinningprofileuuid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr almanacprofileuuid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr discriminatorprofileuuid_;
  ::carbon::frontend::jobs::JobDescription* jobdescription_;
  int64_t stoptimems_;
  int64_t lastupdatetimems_;
  float expectedacreage_;
  bool completed_;
  int64_t lastusedtimems_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class CreateJobRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.CreateJobRequest) */ {
 public:
  inline CreateJobRequest() : CreateJobRequest(nullptr) {}
  ~CreateJobRequest() override;
  explicit constexpr CreateJobRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CreateJobRequest(const CreateJobRequest& from);
  CreateJobRequest(CreateJobRequest&& from) noexcept
    : CreateJobRequest() {
    *this = ::std::move(from);
  }

  inline CreateJobRequest& operator=(const CreateJobRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateJobRequest& operator=(CreateJobRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CreateJobRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CreateJobRequest* internal_default_instance() {
    return reinterpret_cast<const CreateJobRequest*>(
               &_CreateJobRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(CreateJobRequest& a, CreateJobRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateJobRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateJobRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CreateJobRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CreateJobRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CreateJobRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CreateJobRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateJobRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.CreateJobRequest";
  }
  protected:
  explicit CreateJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kActiveFieldNumber = 2,
    kExpectedAcreageFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // bool active = 2;
  void clear_active();
  bool active() const;
  void set_active(bool value);
  private:
  bool _internal_active() const;
  void _internal_set_active(bool value);
  public:

  // float expectedAcreage = 3;
  void clear_expectedacreage();
  float expectedacreage() const;
  void set_expectedacreage(float value);
  private:
  float _internal_expectedacreage() const;
  void _internal_set_expectedacreage(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.CreateJobRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  bool active_;
  float expectedacreage_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class CreateJobResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.CreateJobResponse) */ {
 public:
  inline CreateJobResponse() : CreateJobResponse(nullptr) {}
  ~CreateJobResponse() override;
  explicit constexpr CreateJobResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CreateJobResponse(const CreateJobResponse& from);
  CreateJobResponse(CreateJobResponse&& from) noexcept
    : CreateJobResponse() {
    *this = ::std::move(from);
  }

  inline CreateJobResponse& operator=(const CreateJobResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateJobResponse& operator=(CreateJobResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CreateJobResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CreateJobResponse* internal_default_instance() {
    return reinterpret_cast<const CreateJobResponse*>(
               &_CreateJobResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(CreateJobResponse& a, CreateJobResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateJobResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateJobResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CreateJobResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CreateJobResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CreateJobResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CreateJobResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateJobResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.CreateJobResponse";
  }
  protected:
  explicit CreateJobResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 1,
  };
  // string jobId = 1;
  void clear_jobid();
  const std::string& jobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_jobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_jobid();
  PROTOBUF_NODISCARD std::string* release_jobid();
  void set_allocated_jobid(std::string* jobid);
  private:
  const std::string& _internal_jobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_jobid(const std::string& value);
  std::string* _internal_mutable_jobid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.CreateJobResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr jobid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class UpdateJobRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.UpdateJobRequest) */ {
 public:
  inline UpdateJobRequest() : UpdateJobRequest(nullptr) {}
  ~UpdateJobRequest() override;
  explicit constexpr UpdateJobRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UpdateJobRequest(const UpdateJobRequest& from);
  UpdateJobRequest(UpdateJobRequest&& from) noexcept
    : UpdateJobRequest() {
    *this = ::std::move(from);
  }

  inline UpdateJobRequest& operator=(const UpdateJobRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UpdateJobRequest& operator=(UpdateJobRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UpdateJobRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UpdateJobRequest* internal_default_instance() {
    return reinterpret_cast<const UpdateJobRequest*>(
               &_UpdateJobRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(UpdateJobRequest& a, UpdateJobRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UpdateJobRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UpdateJobRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UpdateJobRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UpdateJobRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UpdateJobRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UpdateJobRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UpdateJobRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.UpdateJobRequest";
  }
  protected:
  explicit UpdateJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobDescriptionFieldNumber = 1,
    kExpectedAcreageFieldNumber = 2,
  };
  // .carbon.frontend.jobs.JobDescription jobDescription = 1;
  bool has_jobdescription() const;
  private:
  bool _internal_has_jobdescription() const;
  public:
  void clear_jobdescription();
  const ::carbon::frontend::jobs::JobDescription& jobdescription() const;
  PROTOBUF_NODISCARD ::carbon::frontend::jobs::JobDescription* release_jobdescription();
  ::carbon::frontend::jobs::JobDescription* mutable_jobdescription();
  void set_allocated_jobdescription(::carbon::frontend::jobs::JobDescription* jobdescription);
  private:
  const ::carbon::frontend::jobs::JobDescription& _internal_jobdescription() const;
  ::carbon::frontend::jobs::JobDescription* _internal_mutable_jobdescription();
  public:
  void unsafe_arena_set_allocated_jobdescription(
      ::carbon::frontend::jobs::JobDescription* jobdescription);
  ::carbon::frontend::jobs::JobDescription* unsafe_arena_release_jobdescription();

  // float expectedAcreage = 2;
  void clear_expectedacreage();
  float expectedacreage() const;
  void set_expectedacreage(float value);
  private:
  float _internal_expectedacreage() const;
  void _internal_set_expectedacreage(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.UpdateJobRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::jobs::JobDescription* jobdescription_;
  float expectedacreage_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class GetNextJobsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.GetNextJobsRequest) */ {
 public:
  inline GetNextJobsRequest() : GetNextJobsRequest(nullptr) {}
  ~GetNextJobsRequest() override;
  explicit constexpr GetNextJobsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextJobsRequest(const GetNextJobsRequest& from);
  GetNextJobsRequest(GetNextJobsRequest&& from) noexcept
    : GetNextJobsRequest() {
    *this = ::std::move(from);
  }

  inline GetNextJobsRequest& operator=(const GetNextJobsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextJobsRequest& operator=(GetNextJobsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextJobsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextJobsRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextJobsRequest*>(
               &_GetNextJobsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(GetNextJobsRequest& a, GetNextJobsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextJobsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextJobsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextJobsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextJobsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextJobsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextJobsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextJobsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.GetNextJobsRequest";
  }
  protected:
  explicit GetNextJobsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimestampFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp timestamp = 1;
  bool has_timestamp() const;
  private:
  bool _internal_has_timestamp() const;
  public:
  void clear_timestamp();
  const ::carbon::frontend::util::Timestamp& timestamp() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_timestamp();
  ::carbon::frontend::util::Timestamp* mutable_timestamp();
  void set_allocated_timestamp(::carbon::frontend::util::Timestamp* timestamp);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_timestamp() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_timestamp();
  public:
  void unsafe_arena_set_allocated_timestamp(
      ::carbon::frontend::util::Timestamp* timestamp);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_timestamp();

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextJobsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* timestamp_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class JobWithMetrics final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.JobWithMetrics) */ {
 public:
  inline JobWithMetrics() : JobWithMetrics(nullptr) {}
  ~JobWithMetrics() override;
  explicit constexpr JobWithMetrics(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  JobWithMetrics(const JobWithMetrics& from);
  JobWithMetrics(JobWithMetrics&& from) noexcept
    : JobWithMetrics() {
    *this = ::std::move(from);
  }

  inline JobWithMetrics& operator=(const JobWithMetrics& from) {
    CopyFrom(from);
    return *this;
  }
  inline JobWithMetrics& operator=(JobWithMetrics&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const JobWithMetrics& default_instance() {
    return *internal_default_instance();
  }
  static inline const JobWithMetrics* internal_default_instance() {
    return reinterpret_cast<const JobWithMetrics*>(
               &_JobWithMetrics_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(JobWithMetrics& a, JobWithMetrics& b) {
    a.Swap(&b);
  }
  inline void Swap(JobWithMetrics* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(JobWithMetrics* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  JobWithMetrics* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<JobWithMetrics>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const JobWithMetrics& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const JobWithMetrics& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(JobWithMetrics* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.JobWithMetrics";
  }
  protected:
  explicit JobWithMetrics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobFieldNumber = 1,
    kMetricsFieldNumber = 2,
  };
  // .carbon.frontend.jobs.Job job = 1;
  bool has_job() const;
  private:
  bool _internal_has_job() const;
  public:
  void clear_job();
  const ::carbon::frontend::jobs::Job& job() const;
  PROTOBUF_NODISCARD ::carbon::frontend::jobs::Job* release_job();
  ::carbon::frontend::jobs::Job* mutable_job();
  void set_allocated_job(::carbon::frontend::jobs::Job* job);
  private:
  const ::carbon::frontend::jobs::Job& _internal_job() const;
  ::carbon::frontend::jobs::Job* _internal_mutable_job();
  public:
  void unsafe_arena_set_allocated_job(
      ::carbon::frontend::jobs::Job* job);
  ::carbon::frontend::jobs::Job* unsafe_arena_release_job();

  // .metrics_aggregator.Metrics metrics = 2;
  bool has_metrics() const;
  private:
  bool _internal_has_metrics() const;
  public:
  void clear_metrics();
  const ::metrics_aggregator::Metrics& metrics() const;
  PROTOBUF_NODISCARD ::metrics_aggregator::Metrics* release_metrics();
  ::metrics_aggregator::Metrics* mutable_metrics();
  void set_allocated_metrics(::metrics_aggregator::Metrics* metrics);
  private:
  const ::metrics_aggregator::Metrics& _internal_metrics() const;
  ::metrics_aggregator::Metrics* _internal_mutable_metrics();
  public:
  void unsafe_arena_set_allocated_metrics(
      ::metrics_aggregator::Metrics* metrics);
  ::metrics_aggregator::Metrics* unsafe_arena_release_metrics();

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.JobWithMetrics)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::jobs::Job* job_;
  ::metrics_aggregator::Metrics* metrics_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class GetNextJobsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.GetNextJobsResponse) */ {
 public:
  inline GetNextJobsResponse() : GetNextJobsResponse(nullptr) {}
  ~GetNextJobsResponse() override;
  explicit constexpr GetNextJobsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextJobsResponse(const GetNextJobsResponse& from);
  GetNextJobsResponse(GetNextJobsResponse&& from) noexcept
    : GetNextJobsResponse() {
    *this = ::std::move(from);
  }

  inline GetNextJobsResponse& operator=(const GetNextJobsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextJobsResponse& operator=(GetNextJobsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextJobsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextJobsResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextJobsResponse*>(
               &_GetNextJobsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GetNextJobsResponse& a, GetNextJobsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextJobsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextJobsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextJobsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextJobsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextJobsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextJobsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextJobsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.GetNextJobsResponse";
  }
  protected:
  explicit GetNextJobsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobsFieldNumber = 1,
    kActiveJobIdFieldNumber = 2,
    kTimestampFieldNumber = 3,
  };
  // repeated .carbon.frontend.jobs.JobWithMetrics jobs = 1;
  int jobs_size() const;
  private:
  int _internal_jobs_size() const;
  public:
  void clear_jobs();
  ::carbon::frontend::jobs::JobWithMetrics* mutable_jobs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::jobs::JobWithMetrics >*
      mutable_jobs();
  private:
  const ::carbon::frontend::jobs::JobWithMetrics& _internal_jobs(int index) const;
  ::carbon::frontend::jobs::JobWithMetrics* _internal_add_jobs();
  public:
  const ::carbon::frontend::jobs::JobWithMetrics& jobs(int index) const;
  ::carbon::frontend::jobs::JobWithMetrics* add_jobs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::jobs::JobWithMetrics >&
      jobs() const;

  // string activeJobId = 2;
  void clear_activejobid();
  const std::string& activejobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_activejobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_activejobid();
  PROTOBUF_NODISCARD std::string* release_activejobid();
  void set_allocated_activejobid(std::string* activejobid);
  private:
  const std::string& _internal_activejobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_activejobid(const std::string& value);
  std::string* _internal_mutable_activejobid();
  public:

  // .carbon.frontend.util.Timestamp timestamp = 3;
  bool has_timestamp() const;
  private:
  bool _internal_has_timestamp() const;
  public:
  void clear_timestamp();
  const ::carbon::frontend::util::Timestamp& timestamp() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_timestamp();
  ::carbon::frontend::util::Timestamp* mutable_timestamp();
  void set_allocated_timestamp(::carbon::frontend::util::Timestamp* timestamp);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_timestamp() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_timestamp();
  public:
  void unsafe_arena_set_allocated_timestamp(
      ::carbon::frontend::util::Timestamp* timestamp);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_timestamp();

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextJobsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::jobs::JobWithMetrics > jobs_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr activejobid_;
  ::carbon::frontend::util::Timestamp* timestamp_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class GetNextActiveJobIdRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.GetNextActiveJobIdRequest) */ {
 public:
  inline GetNextActiveJobIdRequest() : GetNextActiveJobIdRequest(nullptr) {}
  ~GetNextActiveJobIdRequest() override;
  explicit constexpr GetNextActiveJobIdRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextActiveJobIdRequest(const GetNextActiveJobIdRequest& from);
  GetNextActiveJobIdRequest(GetNextActiveJobIdRequest&& from) noexcept
    : GetNextActiveJobIdRequest() {
    *this = ::std::move(from);
  }

  inline GetNextActiveJobIdRequest& operator=(const GetNextActiveJobIdRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextActiveJobIdRequest& operator=(GetNextActiveJobIdRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextActiveJobIdRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextActiveJobIdRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextActiveJobIdRequest*>(
               &_GetNextActiveJobIdRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GetNextActiveJobIdRequest& a, GetNextActiveJobIdRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextActiveJobIdRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextActiveJobIdRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextActiveJobIdRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextActiveJobIdRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextActiveJobIdRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextActiveJobIdRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextActiveJobIdRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.GetNextActiveJobIdRequest";
  }
  protected:
  explicit GetNextActiveJobIdRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimestampFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp timestamp = 1;
  bool has_timestamp() const;
  private:
  bool _internal_has_timestamp() const;
  public:
  void clear_timestamp();
  const ::carbon::frontend::util::Timestamp& timestamp() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_timestamp();
  ::carbon::frontend::util::Timestamp* mutable_timestamp();
  void set_allocated_timestamp(::carbon::frontend::util::Timestamp* timestamp);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_timestamp() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_timestamp();
  public:
  void unsafe_arena_set_allocated_timestamp(
      ::carbon::frontend::util::Timestamp* timestamp);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_timestamp();

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextActiveJobIdRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* timestamp_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class GetNextActiveJobIdResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.GetNextActiveJobIdResponse) */ {
 public:
  inline GetNextActiveJobIdResponse() : GetNextActiveJobIdResponse(nullptr) {}
  ~GetNextActiveJobIdResponse() override;
  explicit constexpr GetNextActiveJobIdResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextActiveJobIdResponse(const GetNextActiveJobIdResponse& from);
  GetNextActiveJobIdResponse(GetNextActiveJobIdResponse&& from) noexcept
    : GetNextActiveJobIdResponse() {
    *this = ::std::move(from);
  }

  inline GetNextActiveJobIdResponse& operator=(const GetNextActiveJobIdResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextActiveJobIdResponse& operator=(GetNextActiveJobIdResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextActiveJobIdResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextActiveJobIdResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextActiveJobIdResponse*>(
               &_GetNextActiveJobIdResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(GetNextActiveJobIdResponse& a, GetNextActiveJobIdResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextActiveJobIdResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextActiveJobIdResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextActiveJobIdResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextActiveJobIdResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextActiveJobIdResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextActiveJobIdResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextActiveJobIdResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.GetNextActiveJobIdResponse";
  }
  protected:
  explicit GetNextActiveJobIdResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kActiveJobIdFieldNumber = 1,
    kTimestampFieldNumber = 2,
  };
  // string activeJobId = 1;
  void clear_activejobid();
  const std::string& activejobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_activejobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_activejobid();
  PROTOBUF_NODISCARD std::string* release_activejobid();
  void set_allocated_activejobid(std::string* activejobid);
  private:
  const std::string& _internal_activejobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_activejobid(const std::string& value);
  std::string* _internal_mutable_activejobid();
  public:

  // .carbon.frontend.util.Timestamp timestamp = 2;
  bool has_timestamp() const;
  private:
  bool _internal_has_timestamp() const;
  public:
  void clear_timestamp();
  const ::carbon::frontend::util::Timestamp& timestamp() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_timestamp();
  ::carbon::frontend::util::Timestamp* mutable_timestamp();
  void set_allocated_timestamp(::carbon::frontend::util::Timestamp* timestamp);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_timestamp() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_timestamp();
  public:
  void unsafe_arena_set_allocated_timestamp(
      ::carbon::frontend::util::Timestamp* timestamp);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_timestamp();

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextActiveJobIdResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr activejobid_;
  ::carbon::frontend::util::Timestamp* timestamp_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class GetJobRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.GetJobRequest) */ {
 public:
  inline GetJobRequest() : GetJobRequest(nullptr) {}
  ~GetJobRequest() override;
  explicit constexpr GetJobRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetJobRequest(const GetJobRequest& from);
  GetJobRequest(GetJobRequest&& from) noexcept
    : GetJobRequest() {
    *this = ::std::move(from);
  }

  inline GetJobRequest& operator=(const GetJobRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetJobRequest& operator=(GetJobRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetJobRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetJobRequest* internal_default_instance() {
    return reinterpret_cast<const GetJobRequest*>(
               &_GetJobRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(GetJobRequest& a, GetJobRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetJobRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetJobRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetJobRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetJobRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetJobRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetJobRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetJobRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.GetJobRequest";
  }
  protected:
  explicit GetJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 1,
  };
  // string jobId = 1;
  void clear_jobid();
  const std::string& jobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_jobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_jobid();
  PROTOBUF_NODISCARD std::string* release_jobid();
  void set_allocated_jobid(std::string* jobid);
  private:
  const std::string& _internal_jobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_jobid(const std::string& value);
  std::string* _internal_mutable_jobid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetJobRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr jobid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class GetJobResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.GetJobResponse) */ {
 public:
  inline GetJobResponse() : GetJobResponse(nullptr) {}
  ~GetJobResponse() override;
  explicit constexpr GetJobResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetJobResponse(const GetJobResponse& from);
  GetJobResponse(GetJobResponse&& from) noexcept
    : GetJobResponse() {
    *this = ::std::move(from);
  }

  inline GetJobResponse& operator=(const GetJobResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetJobResponse& operator=(GetJobResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetJobResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetJobResponse* internal_default_instance() {
    return reinterpret_cast<const GetJobResponse*>(
               &_GetJobResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(GetJobResponse& a, GetJobResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetJobResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetJobResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetJobResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetJobResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetJobResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetJobResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetJobResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.GetJobResponse";
  }
  protected:
  explicit GetJobResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobFieldNumber = 1,
  };
  // .carbon.frontend.jobs.Job job = 1;
  bool has_job() const;
  private:
  bool _internal_has_job() const;
  public:
  void clear_job();
  const ::carbon::frontend::jobs::Job& job() const;
  PROTOBUF_NODISCARD ::carbon::frontend::jobs::Job* release_job();
  ::carbon::frontend::jobs::Job* mutable_job();
  void set_allocated_job(::carbon::frontend::jobs::Job* job);
  private:
  const ::carbon::frontend::jobs::Job& _internal_job() const;
  ::carbon::frontend::jobs::Job* _internal_mutable_job();
  public:
  void unsafe_arena_set_allocated_job(
      ::carbon::frontend::jobs::Job* job);
  ::carbon::frontend::jobs::Job* unsafe_arena_release_job();

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetJobResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::jobs::Job* job_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class StartJobRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.StartJobRequest) */ {
 public:
  inline StartJobRequest() : StartJobRequest(nullptr) {}
  ~StartJobRequest() override;
  explicit constexpr StartJobRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StartJobRequest(const StartJobRequest& from);
  StartJobRequest(StartJobRequest&& from) noexcept
    : StartJobRequest() {
    *this = ::std::move(from);
  }

  inline StartJobRequest& operator=(const StartJobRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline StartJobRequest& operator=(StartJobRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StartJobRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const StartJobRequest* internal_default_instance() {
    return reinterpret_cast<const StartJobRequest*>(
               &_StartJobRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(StartJobRequest& a, StartJobRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(StartJobRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StartJobRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StartJobRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StartJobRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StartJobRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StartJobRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StartJobRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.StartJobRequest";
  }
  protected:
  explicit StartJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 1,
  };
  // string jobId = 1;
  void clear_jobid();
  const std::string& jobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_jobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_jobid();
  PROTOBUF_NODISCARD std::string* release_jobid();
  void set_allocated_jobid(std::string* jobid);
  private:
  const std::string& _internal_jobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_jobid(const std::string& value);
  std::string* _internal_mutable_jobid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.StartJobRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr jobid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class GetConfigDumpRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.GetConfigDumpRequest) */ {
 public:
  inline GetConfigDumpRequest() : GetConfigDumpRequest(nullptr) {}
  ~GetConfigDumpRequest() override;
  explicit constexpr GetConfigDumpRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetConfigDumpRequest(const GetConfigDumpRequest& from);
  GetConfigDumpRequest(GetConfigDumpRequest&& from) noexcept
    : GetConfigDumpRequest() {
    *this = ::std::move(from);
  }

  inline GetConfigDumpRequest& operator=(const GetConfigDumpRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetConfigDumpRequest& operator=(GetConfigDumpRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetConfigDumpRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetConfigDumpRequest* internal_default_instance() {
    return reinterpret_cast<const GetConfigDumpRequest*>(
               &_GetConfigDumpRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(GetConfigDumpRequest& a, GetConfigDumpRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetConfigDumpRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetConfigDumpRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetConfigDumpRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetConfigDumpRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetConfigDumpRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetConfigDumpRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetConfigDumpRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.GetConfigDumpRequest";
  }
  protected:
  explicit GetConfigDumpRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 1,
  };
  // string jobId = 1;
  void clear_jobid();
  const std::string& jobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_jobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_jobid();
  PROTOBUF_NODISCARD std::string* release_jobid();
  void set_allocated_jobid(std::string* jobid);
  private:
  const std::string& _internal_jobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_jobid(const std::string& value);
  std::string* _internal_mutable_jobid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetConfigDumpRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr jobid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class GetConfigDumpResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.GetConfigDumpResponse) */ {
 public:
  inline GetConfigDumpResponse() : GetConfigDumpResponse(nullptr) {}
  ~GetConfigDumpResponse() override;
  explicit constexpr GetConfigDumpResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetConfigDumpResponse(const GetConfigDumpResponse& from);
  GetConfigDumpResponse(GetConfigDumpResponse&& from) noexcept
    : GetConfigDumpResponse() {
    *this = ::std::move(from);
  }

  inline GetConfigDumpResponse& operator=(const GetConfigDumpResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetConfigDumpResponse& operator=(GetConfigDumpResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetConfigDumpResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetConfigDumpResponse* internal_default_instance() {
    return reinterpret_cast<const GetConfigDumpResponse*>(
               &_GetConfigDumpResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(GetConfigDumpResponse& a, GetConfigDumpResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetConfigDumpResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetConfigDumpResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetConfigDumpResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetConfigDumpResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetConfigDumpResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetConfigDumpResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetConfigDumpResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.GetConfigDumpResponse";
  }
  protected:
  explicit GetConfigDumpResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRootConfigFieldNumber = 1,
  };
  // .carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 1;
  bool has_rootconfig() const;
  private:
  bool _internal_has_rootconfig() const;
  public:
  void clear_rootconfig();
  const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot& rootconfig() const;
  PROTOBUF_NODISCARD ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* release_rootconfig();
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* mutable_rootconfig();
  void set_allocated_rootconfig(::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* rootconfig);
  private:
  const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot& _internal_rootconfig() const;
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* _internal_mutable_rootconfig();
  public:
  void unsafe_arena_set_allocated_rootconfig(
      ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* rootconfig);
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* unsafe_arena_release_rootconfig();

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetConfigDumpResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* rootconfig_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class GetActiveJobMetricsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.GetActiveJobMetricsResponse) */ {
 public:
  inline GetActiveJobMetricsResponse() : GetActiveJobMetricsResponse(nullptr) {}
  ~GetActiveJobMetricsResponse() override;
  explicit constexpr GetActiveJobMetricsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetActiveJobMetricsResponse(const GetActiveJobMetricsResponse& from);
  GetActiveJobMetricsResponse(GetActiveJobMetricsResponse&& from) noexcept
    : GetActiveJobMetricsResponse() {
    *this = ::std::move(from);
  }

  inline GetActiveJobMetricsResponse& operator=(const GetActiveJobMetricsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetActiveJobMetricsResponse& operator=(GetActiveJobMetricsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetActiveJobMetricsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetActiveJobMetricsResponse* internal_default_instance() {
    return reinterpret_cast<const GetActiveJobMetricsResponse*>(
               &_GetActiveJobMetricsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(GetActiveJobMetricsResponse& a, GetActiveJobMetricsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetActiveJobMetricsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetActiveJobMetricsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetActiveJobMetricsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetActiveJobMetricsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetActiveJobMetricsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetActiveJobMetricsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetActiveJobMetricsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.GetActiveJobMetricsResponse";
  }
  protected:
  explicit GetActiveJobMetricsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobMetricsFieldNumber = 1,
  };
  // .metrics_aggregator.Metrics jobMetrics = 1;
  bool has_jobmetrics() const;
  private:
  bool _internal_has_jobmetrics() const;
  public:
  void clear_jobmetrics();
  const ::metrics_aggregator::Metrics& jobmetrics() const;
  PROTOBUF_NODISCARD ::metrics_aggregator::Metrics* release_jobmetrics();
  ::metrics_aggregator::Metrics* mutable_jobmetrics();
  void set_allocated_jobmetrics(::metrics_aggregator::Metrics* jobmetrics);
  private:
  const ::metrics_aggregator::Metrics& _internal_jobmetrics() const;
  ::metrics_aggregator::Metrics* _internal_mutable_jobmetrics();
  public:
  void unsafe_arena_set_allocated_jobmetrics(
      ::metrics_aggregator::Metrics* jobmetrics);
  ::metrics_aggregator::Metrics* unsafe_arena_release_jobmetrics();

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetActiveJobMetricsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::metrics_aggregator::Metrics* jobmetrics_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class DeleteJobRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.DeleteJobRequest) */ {
 public:
  inline DeleteJobRequest() : DeleteJobRequest(nullptr) {}
  ~DeleteJobRequest() override;
  explicit constexpr DeleteJobRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteJobRequest(const DeleteJobRequest& from);
  DeleteJobRequest(DeleteJobRequest&& from) noexcept
    : DeleteJobRequest() {
    *this = ::std::move(from);
  }

  inline DeleteJobRequest& operator=(const DeleteJobRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteJobRequest& operator=(DeleteJobRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteJobRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteJobRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteJobRequest*>(
               &_DeleteJobRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(DeleteJobRequest& a, DeleteJobRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteJobRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteJobRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteJobRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteJobRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeleteJobRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeleteJobRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteJobRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.DeleteJobRequest";
  }
  protected:
  explicit DeleteJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 1,
  };
  // string jobId = 1;
  void clear_jobid();
  const std::string& jobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_jobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_jobid();
  PROTOBUF_NODISCARD std::string* release_jobid();
  void set_allocated_jobid(std::string* jobid);
  private:
  const std::string& _internal_jobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_jobid(const std::string& value);
  std::string* _internal_mutable_jobid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.DeleteJobRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr jobid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class MarkJobCompletedRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.MarkJobCompletedRequest) */ {
 public:
  inline MarkJobCompletedRequest() : MarkJobCompletedRequest(nullptr) {}
  ~MarkJobCompletedRequest() override;
  explicit constexpr MarkJobCompletedRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MarkJobCompletedRequest(const MarkJobCompletedRequest& from);
  MarkJobCompletedRequest(MarkJobCompletedRequest&& from) noexcept
    : MarkJobCompletedRequest() {
    *this = ::std::move(from);
  }

  inline MarkJobCompletedRequest& operator=(const MarkJobCompletedRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MarkJobCompletedRequest& operator=(MarkJobCompletedRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MarkJobCompletedRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const MarkJobCompletedRequest* internal_default_instance() {
    return reinterpret_cast<const MarkJobCompletedRequest*>(
               &_MarkJobCompletedRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(MarkJobCompletedRequest& a, MarkJobCompletedRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MarkJobCompletedRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MarkJobCompletedRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MarkJobCompletedRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MarkJobCompletedRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MarkJobCompletedRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MarkJobCompletedRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MarkJobCompletedRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.MarkJobCompletedRequest";
  }
  protected:
  explicit MarkJobCompletedRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 1,
  };
  // string jobId = 1;
  void clear_jobid();
  const std::string& jobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_jobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_jobid();
  PROTOBUF_NODISCARD std::string* release_jobid();
  void set_allocated_jobid(std::string* jobid);
  private:
  const std::string& _internal_jobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_jobid(const std::string& value);
  std::string* _internal_mutable_jobid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.MarkJobCompletedRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr jobid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class MarkJobIncompleteRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.MarkJobIncompleteRequest) */ {
 public:
  inline MarkJobIncompleteRequest() : MarkJobIncompleteRequest(nullptr) {}
  ~MarkJobIncompleteRequest() override;
  explicit constexpr MarkJobIncompleteRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MarkJobIncompleteRequest(const MarkJobIncompleteRequest& from);
  MarkJobIncompleteRequest(MarkJobIncompleteRequest&& from) noexcept
    : MarkJobIncompleteRequest() {
    *this = ::std::move(from);
  }

  inline MarkJobIncompleteRequest& operator=(const MarkJobIncompleteRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MarkJobIncompleteRequest& operator=(MarkJobIncompleteRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MarkJobIncompleteRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const MarkJobIncompleteRequest* internal_default_instance() {
    return reinterpret_cast<const MarkJobIncompleteRequest*>(
               &_MarkJobIncompleteRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(MarkJobIncompleteRequest& a, MarkJobIncompleteRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MarkJobIncompleteRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MarkJobIncompleteRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MarkJobIncompleteRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MarkJobIncompleteRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MarkJobIncompleteRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MarkJobIncompleteRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MarkJobIncompleteRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.MarkJobIncompleteRequest";
  }
  protected:
  explicit MarkJobIncompleteRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 1,
  };
  // string jobId = 1;
  void clear_jobid();
  const std::string& jobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_jobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_jobid();
  PROTOBUF_NODISCARD std::string* release_jobid();
  void set_allocated_jobid(std::string* jobid);
  private:
  const std::string& _internal_jobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_jobid(const std::string& value);
  std::string* _internal_mutable_jobid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.MarkJobIncompleteRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr jobid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class GetNextJobRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.GetNextJobRequest) */ {
 public:
  inline GetNextJobRequest() : GetNextJobRequest(nullptr) {}
  ~GetNextJobRequest() override;
  explicit constexpr GetNextJobRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextJobRequest(const GetNextJobRequest& from);
  GetNextJobRequest(GetNextJobRequest&& from) noexcept
    : GetNextJobRequest() {
    *this = ::std::move(from);
  }

  inline GetNextJobRequest& operator=(const GetNextJobRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextJobRequest& operator=(GetNextJobRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextJobRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextJobRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextJobRequest*>(
               &_GetNextJobRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(GetNextJobRequest& a, GetNextJobRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextJobRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextJobRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextJobRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextJobRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextJobRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextJobRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextJobRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.GetNextJobRequest";
  }
  protected:
  explicit GetNextJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // string jobId = 2;
  void clear_jobid();
  const std::string& jobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_jobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_jobid();
  PROTOBUF_NODISCARD std::string* release_jobid();
  void set_allocated_jobid(std::string* jobid);
  private:
  const std::string& _internal_jobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_jobid(const std::string& value);
  std::string* _internal_mutable_jobid();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextJobRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr jobid_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// -------------------------------------------------------------------

class GetNextJobResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.jobs.GetNextJobResponse) */ {
 public:
  inline GetNextJobResponse() : GetNextJobResponse(nullptr) {}
  ~GetNextJobResponse() override;
  explicit constexpr GetNextJobResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextJobResponse(const GetNextJobResponse& from);
  GetNextJobResponse(GetNextJobResponse&& from) noexcept
    : GetNextJobResponse() {
    *this = ::std::move(from);
  }

  inline GetNextJobResponse& operator=(const GetNextJobResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextJobResponse& operator=(GetNextJobResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextJobResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextJobResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextJobResponse*>(
               &_GetNextJobResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(GetNextJobResponse& a, GetNextJobResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextJobResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextJobResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextJobResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextJobResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextJobResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextJobResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextJobResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.jobs.GetNextJobResponse";
  }
  protected:
  explicit GetNextJobResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kJobFieldNumber = 2,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.jobs.JobWithMetrics job = 2;
  bool has_job() const;
  private:
  bool _internal_has_job() const;
  public:
  void clear_job();
  const ::carbon::frontend::jobs::JobWithMetrics& job() const;
  PROTOBUF_NODISCARD ::carbon::frontend::jobs::JobWithMetrics* release_job();
  ::carbon::frontend::jobs::JobWithMetrics* mutable_job();
  void set_allocated_job(::carbon::frontend::jobs::JobWithMetrics* job);
  private:
  const ::carbon::frontend::jobs::JobWithMetrics& _internal_job() const;
  ::carbon::frontend::jobs::JobWithMetrics* _internal_mutable_job();
  public:
  void unsafe_arena_set_allocated_job(
      ::carbon::frontend::jobs::JobWithMetrics* job);
  ::carbon::frontend::jobs::JobWithMetrics* unsafe_arena_release_job();

  // @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextJobResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::jobs::JobWithMetrics* job_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fjobs_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// JobDescription

// string jobId = 1;
inline void JobDescription::clear_jobid() {
  jobid_.ClearToEmpty();
}
inline const std::string& JobDescription::jobid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.JobDescription.jobId)
  return _internal_jobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void JobDescription::set_jobid(ArgT0&& arg0, ArgT... args) {
 
 jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.JobDescription.jobId)
}
inline std::string* JobDescription::mutable_jobid() {
  std::string* _s = _internal_mutable_jobid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.JobDescription.jobId)
  return _s;
}
inline const std::string& JobDescription::_internal_jobid() const {
  return jobid_.Get();
}
inline void JobDescription::_internal_set_jobid(const std::string& value) {
  
  jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* JobDescription::_internal_mutable_jobid() {
  
  return jobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* JobDescription::release_jobid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.JobDescription.jobId)
  return jobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void JobDescription::set_allocated_jobid(std::string* jobid) {
  if (jobid != nullptr) {
    
  } else {
    
  }
  jobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), jobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (jobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.JobDescription.jobId)
}

// string name = 2;
inline void JobDescription::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& JobDescription::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.JobDescription.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void JobDescription::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.JobDescription.name)
}
inline std::string* JobDescription::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.JobDescription.name)
  return _s;
}
inline const std::string& JobDescription::_internal_name() const {
  return name_.Get();
}
inline void JobDescription::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* JobDescription::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* JobDescription::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.JobDescription.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void JobDescription::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.JobDescription.name)
}

// int64 timestampMs = 3;
inline void JobDescription::clear_timestampms() {
  timestampms_ = int64_t{0};
}
inline int64_t JobDescription::_internal_timestampms() const {
  return timestampms_;
}
inline int64_t JobDescription::timestampms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.JobDescription.timestampMs)
  return _internal_timestampms();
}
inline void JobDescription::_internal_set_timestampms(int64_t value) {
  
  timestampms_ = value;
}
inline void JobDescription::set_timestampms(int64_t value) {
  _internal_set_timestampms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.JobDescription.timestampMs)
}

// -------------------------------------------------------------------

// ActiveProfile

// .carbon.frontend.profile_sync.ProfileType profile_type = 1;
inline void ActiveProfile::clear_profile_type() {
  profile_type_ = 0;
}
inline ::carbon::frontend::profile_sync::ProfileType ActiveProfile::_internal_profile_type() const {
  return static_cast< ::carbon::frontend::profile_sync::ProfileType >(profile_type_);
}
inline ::carbon::frontend::profile_sync::ProfileType ActiveProfile::profile_type() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.ActiveProfile.profile_type)
  return _internal_profile_type();
}
inline void ActiveProfile::_internal_set_profile_type(::carbon::frontend::profile_sync::ProfileType value) {
  
  profile_type_ = value;
}
inline void ActiveProfile::set_profile_type(::carbon::frontend::profile_sync::ProfileType value) {
  _internal_set_profile_type(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.ActiveProfile.profile_type)
}

// string id = 2;
inline void ActiveProfile::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& ActiveProfile::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.ActiveProfile.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ActiveProfile::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.ActiveProfile.id)
}
inline std::string* ActiveProfile::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.ActiveProfile.id)
  return _s;
}
inline const std::string& ActiveProfile::_internal_id() const {
  return id_.Get();
}
inline void ActiveProfile::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ActiveProfile::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ActiveProfile::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.ActiveProfile.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ActiveProfile::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.ActiveProfile.id)
}

// string name = 3;
inline void ActiveProfile::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& ActiveProfile::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.ActiveProfile.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ActiveProfile::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.ActiveProfile.name)
}
inline std::string* ActiveProfile::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.ActiveProfile.name)
  return _s;
}
inline const std::string& ActiveProfile::_internal_name() const {
  return name_.Get();
}
inline void ActiveProfile::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ActiveProfile::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ActiveProfile::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.ActiveProfile.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ActiveProfile::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.ActiveProfile.name)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// Job

// .carbon.frontend.jobs.JobDescription jobDescription = 1;
inline bool Job::_internal_has_jobdescription() const {
  return this != internal_default_instance() && jobdescription_ != nullptr;
}
inline bool Job::has_jobdescription() const {
  return _internal_has_jobdescription();
}
inline void Job::clear_jobdescription() {
  if (GetArenaForAllocation() == nullptr && jobdescription_ != nullptr) {
    delete jobdescription_;
  }
  jobdescription_ = nullptr;
}
inline const ::carbon::frontend::jobs::JobDescription& Job::_internal_jobdescription() const {
  const ::carbon::frontend::jobs::JobDescription* p = jobdescription_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::jobs::JobDescription&>(
      ::carbon::frontend::jobs::_JobDescription_default_instance_);
}
inline const ::carbon::frontend::jobs::JobDescription& Job::jobdescription() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.jobDescription)
  return _internal_jobdescription();
}
inline void Job::unsafe_arena_set_allocated_jobdescription(
    ::carbon::frontend::jobs::JobDescription* jobdescription) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(jobdescription_);
  }
  jobdescription_ = jobdescription;
  if (jobdescription) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.Job.jobDescription)
}
inline ::carbon::frontend::jobs::JobDescription* Job::release_jobdescription() {
  
  ::carbon::frontend::jobs::JobDescription* temp = jobdescription_;
  jobdescription_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::jobs::JobDescription* Job::unsafe_arena_release_jobdescription() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.Job.jobDescription)
  
  ::carbon::frontend::jobs::JobDescription* temp = jobdescription_;
  jobdescription_ = nullptr;
  return temp;
}
inline ::carbon::frontend::jobs::JobDescription* Job::_internal_mutable_jobdescription() {
  
  if (jobdescription_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::jobs::JobDescription>(GetArenaForAllocation());
    jobdescription_ = p;
  }
  return jobdescription_;
}
inline ::carbon::frontend::jobs::JobDescription* Job::mutable_jobdescription() {
  ::carbon::frontend::jobs::JobDescription* _msg = _internal_mutable_jobdescription();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.Job.jobDescription)
  return _msg;
}
inline void Job::set_allocated_jobdescription(::carbon::frontend::jobs::JobDescription* jobdescription) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete jobdescription_;
  }
  if (jobdescription) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::jobs::JobDescription>::GetOwningArena(jobdescription);
    if (message_arena != submessage_arena) {
      jobdescription = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, jobdescription, submessage_arena);
    }
    
  } else {
    
  }
  jobdescription_ = jobdescription;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.Job.jobDescription)
}

// string bandingProfile = 2;
inline void Job::clear_bandingprofile() {
  bandingprofile_.ClearToEmpty();
}
inline const std::string& Job::bandingprofile() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.bandingProfile)
  return _internal_bandingprofile();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Job::set_bandingprofile(ArgT0&& arg0, ArgT... args) {
 
 bandingprofile_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.bandingProfile)
}
inline std::string* Job::mutable_bandingprofile() {
  std::string* _s = _internal_mutable_bandingprofile();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.Job.bandingProfile)
  return _s;
}
inline const std::string& Job::_internal_bandingprofile() const {
  return bandingprofile_.Get();
}
inline void Job::_internal_set_bandingprofile(const std::string& value) {
  
  bandingprofile_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Job::_internal_mutable_bandingprofile() {
  
  return bandingprofile_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Job::release_bandingprofile() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.Job.bandingProfile)
  return bandingprofile_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Job::set_allocated_bandingprofile(std::string* bandingprofile) {
  if (bandingprofile != nullptr) {
    
  } else {
    
  }
  bandingprofile_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bandingprofile,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (bandingprofile_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    bandingprofile_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.Job.bandingProfile)
}

// string thinningProfile = 3;
inline void Job::clear_thinningprofile() {
  thinningprofile_.ClearToEmpty();
}
inline const std::string& Job::thinningprofile() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.thinningProfile)
  return _internal_thinningprofile();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Job::set_thinningprofile(ArgT0&& arg0, ArgT... args) {
 
 thinningprofile_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.thinningProfile)
}
inline std::string* Job::mutable_thinningprofile() {
  std::string* _s = _internal_mutable_thinningprofile();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.Job.thinningProfile)
  return _s;
}
inline const std::string& Job::_internal_thinningprofile() const {
  return thinningprofile_.Get();
}
inline void Job::_internal_set_thinningprofile(const std::string& value) {
  
  thinningprofile_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Job::_internal_mutable_thinningprofile() {
  
  return thinningprofile_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Job::release_thinningprofile() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.Job.thinningProfile)
  return thinningprofile_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Job::set_allocated_thinningprofile(std::string* thinningprofile) {
  if (thinningprofile != nullptr) {
    
  } else {
    
  }
  thinningprofile_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), thinningprofile,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (thinningprofile_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    thinningprofile_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.Job.thinningProfile)
}

// int64 stopTimeMs = 4;
inline void Job::clear_stoptimems() {
  stoptimems_ = int64_t{0};
}
inline int64_t Job::_internal_stoptimems() const {
  return stoptimems_;
}
inline int64_t Job::stoptimems() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.stopTimeMs)
  return _internal_stoptimems();
}
inline void Job::_internal_set_stoptimems(int64_t value) {
  
  stoptimems_ = value;
}
inline void Job::set_stoptimems(int64_t value) {
  _internal_set_stoptimems(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.stopTimeMs)
}

// int64 lastUpdateTimeMs = 5;
inline void Job::clear_lastupdatetimems() {
  lastupdatetimems_ = int64_t{0};
}
inline int64_t Job::_internal_lastupdatetimems() const {
  return lastupdatetimems_;
}
inline int64_t Job::lastupdatetimems() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.lastUpdateTimeMs)
  return _internal_lastupdatetimems();
}
inline void Job::_internal_set_lastupdatetimems(int64_t value) {
  
  lastupdatetimems_ = value;
}
inline void Job::set_lastupdatetimems(int64_t value) {
  _internal_set_lastupdatetimems(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.lastUpdateTimeMs)
}

// float expectedAcreage = 6;
inline void Job::clear_expectedacreage() {
  expectedacreage_ = 0;
}
inline float Job::_internal_expectedacreage() const {
  return expectedacreage_;
}
inline float Job::expectedacreage() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.expectedAcreage)
  return _internal_expectedacreage();
}
inline void Job::_internal_set_expectedacreage(float value) {
  
  expectedacreage_ = value;
}
inline void Job::set_expectedacreage(float value) {
  _internal_set_expectedacreage(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.expectedAcreage)
}

// bool completed = 7;
inline void Job::clear_completed() {
  completed_ = false;
}
inline bool Job::_internal_completed() const {
  return completed_;
}
inline bool Job::completed() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.completed)
  return _internal_completed();
}
inline void Job::_internal_set_completed(bool value) {
  
  completed_ = value;
}
inline void Job::set_completed(bool value) {
  _internal_set_completed(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.completed)
}

// string almanac = 8;
inline void Job::clear_almanac() {
  almanac_.ClearToEmpty();
}
inline const std::string& Job::almanac() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.almanac)
  return _internal_almanac();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Job::set_almanac(ArgT0&& arg0, ArgT... args) {
 
 almanac_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.almanac)
}
inline std::string* Job::mutable_almanac() {
  std::string* _s = _internal_mutable_almanac();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.Job.almanac)
  return _s;
}
inline const std::string& Job::_internal_almanac() const {
  return almanac_.Get();
}
inline void Job::_internal_set_almanac(const std::string& value) {
  
  almanac_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Job::_internal_mutable_almanac() {
  
  return almanac_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Job::release_almanac() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.Job.almanac)
  return almanac_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Job::set_allocated_almanac(std::string* almanac) {
  if (almanac != nullptr) {
    
  } else {
    
  }
  almanac_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), almanac,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (almanac_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    almanac_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.Job.almanac)
}

// string discriminator = 9;
inline void Job::clear_discriminator() {
  discriminator_.ClearToEmpty();
}
inline const std::string& Job::discriminator() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.discriminator)
  return _internal_discriminator();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Job::set_discriminator(ArgT0&& arg0, ArgT... args) {
 
 discriminator_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.discriminator)
}
inline std::string* Job::mutable_discriminator() {
  std::string* _s = _internal_mutable_discriminator();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.Job.discriminator)
  return _s;
}
inline const std::string& Job::_internal_discriminator() const {
  return discriminator_.Get();
}
inline void Job::_internal_set_discriminator(const std::string& value) {
  
  discriminator_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Job::_internal_mutable_discriminator() {
  
  return discriminator_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Job::release_discriminator() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.Job.discriminator)
  return discriminator_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Job::set_allocated_discriminator(std::string* discriminator) {
  if (discriminator != nullptr) {
    
  } else {
    
  }
  discriminator_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), discriminator,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (discriminator_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    discriminator_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.Job.discriminator)
}

// string crop_id = 10;
inline void Job::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& Job::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Job::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.crop_id)
}
inline std::string* Job::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.Job.crop_id)
  return _s;
}
inline const std::string& Job::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void Job::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Job::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Job::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.Job.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Job::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.Job.crop_id)
}

// string bandingProfileUUID = 11;
inline void Job::clear_bandingprofileuuid() {
  bandingprofileuuid_.ClearToEmpty();
}
inline const std::string& Job::bandingprofileuuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.bandingProfileUUID)
  return _internal_bandingprofileuuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Job::set_bandingprofileuuid(ArgT0&& arg0, ArgT... args) {
 
 bandingprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.bandingProfileUUID)
}
inline std::string* Job::mutable_bandingprofileuuid() {
  std::string* _s = _internal_mutable_bandingprofileuuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.Job.bandingProfileUUID)
  return _s;
}
inline const std::string& Job::_internal_bandingprofileuuid() const {
  return bandingprofileuuid_.Get();
}
inline void Job::_internal_set_bandingprofileuuid(const std::string& value) {
  
  bandingprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Job::_internal_mutable_bandingprofileuuid() {
  
  return bandingprofileuuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Job::release_bandingprofileuuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.Job.bandingProfileUUID)
  return bandingprofileuuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Job::set_allocated_bandingprofileuuid(std::string* bandingprofileuuid) {
  if (bandingprofileuuid != nullptr) {
    
  } else {
    
  }
  bandingprofileuuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bandingprofileuuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (bandingprofileuuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    bandingprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.Job.bandingProfileUUID)
}

// string thinningProfileUUID = 12;
inline void Job::clear_thinningprofileuuid() {
  thinningprofileuuid_.ClearToEmpty();
}
inline const std::string& Job::thinningprofileuuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.thinningProfileUUID)
  return _internal_thinningprofileuuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Job::set_thinningprofileuuid(ArgT0&& arg0, ArgT... args) {
 
 thinningprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.thinningProfileUUID)
}
inline std::string* Job::mutable_thinningprofileuuid() {
  std::string* _s = _internal_mutable_thinningprofileuuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.Job.thinningProfileUUID)
  return _s;
}
inline const std::string& Job::_internal_thinningprofileuuid() const {
  return thinningprofileuuid_.Get();
}
inline void Job::_internal_set_thinningprofileuuid(const std::string& value) {
  
  thinningprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Job::_internal_mutable_thinningprofileuuid() {
  
  return thinningprofileuuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Job::release_thinningprofileuuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.Job.thinningProfileUUID)
  return thinningprofileuuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Job::set_allocated_thinningprofileuuid(std::string* thinningprofileuuid) {
  if (thinningprofileuuid != nullptr) {
    
  } else {
    
  }
  thinningprofileuuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), thinningprofileuuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (thinningprofileuuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    thinningprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.Job.thinningProfileUUID)
}

// string almanacProfileUUID = 13;
inline void Job::clear_almanacprofileuuid() {
  almanacprofileuuid_.ClearToEmpty();
}
inline const std::string& Job::almanacprofileuuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.almanacProfileUUID)
  return _internal_almanacprofileuuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Job::set_almanacprofileuuid(ArgT0&& arg0, ArgT... args) {
 
 almanacprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.almanacProfileUUID)
}
inline std::string* Job::mutable_almanacprofileuuid() {
  std::string* _s = _internal_mutable_almanacprofileuuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.Job.almanacProfileUUID)
  return _s;
}
inline const std::string& Job::_internal_almanacprofileuuid() const {
  return almanacprofileuuid_.Get();
}
inline void Job::_internal_set_almanacprofileuuid(const std::string& value) {
  
  almanacprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Job::_internal_mutable_almanacprofileuuid() {
  
  return almanacprofileuuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Job::release_almanacprofileuuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.Job.almanacProfileUUID)
  return almanacprofileuuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Job::set_allocated_almanacprofileuuid(std::string* almanacprofileuuid) {
  if (almanacprofileuuid != nullptr) {
    
  } else {
    
  }
  almanacprofileuuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), almanacprofileuuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (almanacprofileuuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    almanacprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.Job.almanacProfileUUID)
}

// string discriminatorProfileUUID = 14;
inline void Job::clear_discriminatorprofileuuid() {
  discriminatorprofileuuid_.ClearToEmpty();
}
inline const std::string& Job::discriminatorprofileuuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.discriminatorProfileUUID)
  return _internal_discriminatorprofileuuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Job::set_discriminatorprofileuuid(ArgT0&& arg0, ArgT... args) {
 
 discriminatorprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.discriminatorProfileUUID)
}
inline std::string* Job::mutable_discriminatorprofileuuid() {
  std::string* _s = _internal_mutable_discriminatorprofileuuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.Job.discriminatorProfileUUID)
  return _s;
}
inline const std::string& Job::_internal_discriminatorprofileuuid() const {
  return discriminatorprofileuuid_.Get();
}
inline void Job::_internal_set_discriminatorprofileuuid(const std::string& value) {
  
  discriminatorprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Job::_internal_mutable_discriminatorprofileuuid() {
  
  return discriminatorprofileuuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Job::release_discriminatorprofileuuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.Job.discriminatorProfileUUID)
  return discriminatorprofileuuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Job::set_allocated_discriminatorprofileuuid(std::string* discriminatorprofileuuid) {
  if (discriminatorprofileuuid != nullptr) {
    
  } else {
    
  }
  discriminatorprofileuuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), discriminatorprofileuuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (discriminatorprofileuuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    discriminatorprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.Job.discriminatorProfileUUID)
}

// map<int32, .carbon.frontend.jobs.ActiveProfile> active_profiles = 15;
inline int Job::_internal_active_profiles_size() const {
  return active_profiles_.size();
}
inline int Job::active_profiles_size() const {
  return _internal_active_profiles_size();
}
inline void Job::clear_active_profiles() {
  active_profiles_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >&
Job::_internal_active_profiles() const {
  return active_profiles_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >&
Job::active_profiles() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.jobs.Job.active_profiles)
  return _internal_active_profiles();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >*
Job::_internal_mutable_active_profiles() {
  return active_profiles_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >*
Job::mutable_active_profiles() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.jobs.Job.active_profiles)
  return _internal_mutable_active_profiles();
}

// int64 lastUsedTimeMs = 16;
inline void Job::clear_lastusedtimems() {
  lastusedtimems_ = int64_t{0};
}
inline int64_t Job::_internal_lastusedtimems() const {
  return lastusedtimems_;
}
inline int64_t Job::lastusedtimems() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.Job.lastUsedTimeMs)
  return _internal_lastusedtimems();
}
inline void Job::_internal_set_lastusedtimems(int64_t value) {
  
  lastusedtimems_ = value;
}
inline void Job::set_lastusedtimems(int64_t value) {
  _internal_set_lastusedtimems(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.Job.lastUsedTimeMs)
}

// -------------------------------------------------------------------

// CreateJobRequest

// string name = 1;
inline void CreateJobRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& CreateJobRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.CreateJobRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CreateJobRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.CreateJobRequest.name)
}
inline std::string* CreateJobRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.CreateJobRequest.name)
  return _s;
}
inline const std::string& CreateJobRequest::_internal_name() const {
  return name_.Get();
}
inline void CreateJobRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CreateJobRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CreateJobRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.CreateJobRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CreateJobRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.CreateJobRequest.name)
}

// bool active = 2;
inline void CreateJobRequest::clear_active() {
  active_ = false;
}
inline bool CreateJobRequest::_internal_active() const {
  return active_;
}
inline bool CreateJobRequest::active() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.CreateJobRequest.active)
  return _internal_active();
}
inline void CreateJobRequest::_internal_set_active(bool value) {
  
  active_ = value;
}
inline void CreateJobRequest::set_active(bool value) {
  _internal_set_active(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.CreateJobRequest.active)
}

// float expectedAcreage = 3;
inline void CreateJobRequest::clear_expectedacreage() {
  expectedacreage_ = 0;
}
inline float CreateJobRequest::_internal_expectedacreage() const {
  return expectedacreage_;
}
inline float CreateJobRequest::expectedacreage() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.CreateJobRequest.expectedAcreage)
  return _internal_expectedacreage();
}
inline void CreateJobRequest::_internal_set_expectedacreage(float value) {
  
  expectedacreage_ = value;
}
inline void CreateJobRequest::set_expectedacreage(float value) {
  _internal_set_expectedacreage(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.CreateJobRequest.expectedAcreage)
}

// -------------------------------------------------------------------

// CreateJobResponse

// string jobId = 1;
inline void CreateJobResponse::clear_jobid() {
  jobid_.ClearToEmpty();
}
inline const std::string& CreateJobResponse::jobid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.CreateJobResponse.jobId)
  return _internal_jobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CreateJobResponse::set_jobid(ArgT0&& arg0, ArgT... args) {
 
 jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.CreateJobResponse.jobId)
}
inline std::string* CreateJobResponse::mutable_jobid() {
  std::string* _s = _internal_mutable_jobid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.CreateJobResponse.jobId)
  return _s;
}
inline const std::string& CreateJobResponse::_internal_jobid() const {
  return jobid_.Get();
}
inline void CreateJobResponse::_internal_set_jobid(const std::string& value) {
  
  jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CreateJobResponse::_internal_mutable_jobid() {
  
  return jobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CreateJobResponse::release_jobid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.CreateJobResponse.jobId)
  return jobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CreateJobResponse::set_allocated_jobid(std::string* jobid) {
  if (jobid != nullptr) {
    
  } else {
    
  }
  jobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), jobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (jobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.CreateJobResponse.jobId)
}

// -------------------------------------------------------------------

// UpdateJobRequest

// .carbon.frontend.jobs.JobDescription jobDescription = 1;
inline bool UpdateJobRequest::_internal_has_jobdescription() const {
  return this != internal_default_instance() && jobdescription_ != nullptr;
}
inline bool UpdateJobRequest::has_jobdescription() const {
  return _internal_has_jobdescription();
}
inline void UpdateJobRequest::clear_jobdescription() {
  if (GetArenaForAllocation() == nullptr && jobdescription_ != nullptr) {
    delete jobdescription_;
  }
  jobdescription_ = nullptr;
}
inline const ::carbon::frontend::jobs::JobDescription& UpdateJobRequest::_internal_jobdescription() const {
  const ::carbon::frontend::jobs::JobDescription* p = jobdescription_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::jobs::JobDescription&>(
      ::carbon::frontend::jobs::_JobDescription_default_instance_);
}
inline const ::carbon::frontend::jobs::JobDescription& UpdateJobRequest::jobdescription() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.UpdateJobRequest.jobDescription)
  return _internal_jobdescription();
}
inline void UpdateJobRequest::unsafe_arena_set_allocated_jobdescription(
    ::carbon::frontend::jobs::JobDescription* jobdescription) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(jobdescription_);
  }
  jobdescription_ = jobdescription;
  if (jobdescription) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.UpdateJobRequest.jobDescription)
}
inline ::carbon::frontend::jobs::JobDescription* UpdateJobRequest::release_jobdescription() {
  
  ::carbon::frontend::jobs::JobDescription* temp = jobdescription_;
  jobdescription_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::jobs::JobDescription* UpdateJobRequest::unsafe_arena_release_jobdescription() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.UpdateJobRequest.jobDescription)
  
  ::carbon::frontend::jobs::JobDescription* temp = jobdescription_;
  jobdescription_ = nullptr;
  return temp;
}
inline ::carbon::frontend::jobs::JobDescription* UpdateJobRequest::_internal_mutable_jobdescription() {
  
  if (jobdescription_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::jobs::JobDescription>(GetArenaForAllocation());
    jobdescription_ = p;
  }
  return jobdescription_;
}
inline ::carbon::frontend::jobs::JobDescription* UpdateJobRequest::mutable_jobdescription() {
  ::carbon::frontend::jobs::JobDescription* _msg = _internal_mutable_jobdescription();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.UpdateJobRequest.jobDescription)
  return _msg;
}
inline void UpdateJobRequest::set_allocated_jobdescription(::carbon::frontend::jobs::JobDescription* jobdescription) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete jobdescription_;
  }
  if (jobdescription) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::jobs::JobDescription>::GetOwningArena(jobdescription);
    if (message_arena != submessage_arena) {
      jobdescription = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, jobdescription, submessage_arena);
    }
    
  } else {
    
  }
  jobdescription_ = jobdescription;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.UpdateJobRequest.jobDescription)
}

// float expectedAcreage = 2;
inline void UpdateJobRequest::clear_expectedacreage() {
  expectedacreage_ = 0;
}
inline float UpdateJobRequest::_internal_expectedacreage() const {
  return expectedacreage_;
}
inline float UpdateJobRequest::expectedacreage() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.UpdateJobRequest.expectedAcreage)
  return _internal_expectedacreage();
}
inline void UpdateJobRequest::_internal_set_expectedacreage(float value) {
  
  expectedacreage_ = value;
}
inline void UpdateJobRequest::set_expectedacreage(float value) {
  _internal_set_expectedacreage(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.UpdateJobRequest.expectedAcreage)
}

// -------------------------------------------------------------------

// GetNextJobsRequest

// .carbon.frontend.util.Timestamp timestamp = 1;
inline bool GetNextJobsRequest::_internal_has_timestamp() const {
  return this != internal_default_instance() && timestamp_ != nullptr;
}
inline bool GetNextJobsRequest::has_timestamp() const {
  return _internal_has_timestamp();
}
inline const ::carbon::frontend::util::Timestamp& GetNextJobsRequest::_internal_timestamp() const {
  const ::carbon::frontend::util::Timestamp* p = timestamp_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextJobsRequest::timestamp() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetNextJobsRequest.timestamp)
  return _internal_timestamp();
}
inline void GetNextJobsRequest::unsafe_arena_set_allocated_timestamp(
    ::carbon::frontend::util::Timestamp* timestamp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp_);
  }
  timestamp_ = timestamp;
  if (timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.GetNextJobsRequest.timestamp)
}
inline ::carbon::frontend::util::Timestamp* GetNextJobsRequest::release_timestamp() {
  
  ::carbon::frontend::util::Timestamp* temp = timestamp_;
  timestamp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobsRequest::unsafe_arena_release_timestamp() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetNextJobsRequest.timestamp)
  
  ::carbon::frontend::util::Timestamp* temp = timestamp_;
  timestamp_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobsRequest::_internal_mutable_timestamp() {
  
  if (timestamp_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    timestamp_ = p;
  }
  return timestamp_;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobsRequest::mutable_timestamp() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_timestamp();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetNextJobsRequest.timestamp)
  return _msg;
}
inline void GetNextJobsRequest::set_allocated_timestamp(::carbon::frontend::util::Timestamp* timestamp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp_);
  }
  if (timestamp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp));
    if (message_arena != submessage_arena) {
      timestamp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, timestamp, submessage_arena);
    }
    
  } else {
    
  }
  timestamp_ = timestamp;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetNextJobsRequest.timestamp)
}

// -------------------------------------------------------------------

// JobWithMetrics

// .carbon.frontend.jobs.Job job = 1;
inline bool JobWithMetrics::_internal_has_job() const {
  return this != internal_default_instance() && job_ != nullptr;
}
inline bool JobWithMetrics::has_job() const {
  return _internal_has_job();
}
inline void JobWithMetrics::clear_job() {
  if (GetArenaForAllocation() == nullptr && job_ != nullptr) {
    delete job_;
  }
  job_ = nullptr;
}
inline const ::carbon::frontend::jobs::Job& JobWithMetrics::_internal_job() const {
  const ::carbon::frontend::jobs::Job* p = job_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::jobs::Job&>(
      ::carbon::frontend::jobs::_Job_default_instance_);
}
inline const ::carbon::frontend::jobs::Job& JobWithMetrics::job() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.JobWithMetrics.job)
  return _internal_job();
}
inline void JobWithMetrics::unsafe_arena_set_allocated_job(
    ::carbon::frontend::jobs::Job* job) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(job_);
  }
  job_ = job;
  if (job) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.JobWithMetrics.job)
}
inline ::carbon::frontend::jobs::Job* JobWithMetrics::release_job() {
  
  ::carbon::frontend::jobs::Job* temp = job_;
  job_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::jobs::Job* JobWithMetrics::unsafe_arena_release_job() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.JobWithMetrics.job)
  
  ::carbon::frontend::jobs::Job* temp = job_;
  job_ = nullptr;
  return temp;
}
inline ::carbon::frontend::jobs::Job* JobWithMetrics::_internal_mutable_job() {
  
  if (job_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::jobs::Job>(GetArenaForAllocation());
    job_ = p;
  }
  return job_;
}
inline ::carbon::frontend::jobs::Job* JobWithMetrics::mutable_job() {
  ::carbon::frontend::jobs::Job* _msg = _internal_mutable_job();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.JobWithMetrics.job)
  return _msg;
}
inline void JobWithMetrics::set_allocated_job(::carbon::frontend::jobs::Job* job) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete job_;
  }
  if (job) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::jobs::Job>::GetOwningArena(job);
    if (message_arena != submessage_arena) {
      job = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, job, submessage_arena);
    }
    
  } else {
    
  }
  job_ = job;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.JobWithMetrics.job)
}

// .metrics_aggregator.Metrics metrics = 2;
inline bool JobWithMetrics::_internal_has_metrics() const {
  return this != internal_default_instance() && metrics_ != nullptr;
}
inline bool JobWithMetrics::has_metrics() const {
  return _internal_has_metrics();
}
inline const ::metrics_aggregator::Metrics& JobWithMetrics::_internal_metrics() const {
  const ::metrics_aggregator::Metrics* p = metrics_;
  return p != nullptr ? *p : reinterpret_cast<const ::metrics_aggregator::Metrics&>(
      ::metrics_aggregator::_Metrics_default_instance_);
}
inline const ::metrics_aggregator::Metrics& JobWithMetrics::metrics() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.JobWithMetrics.metrics)
  return _internal_metrics();
}
inline void JobWithMetrics::unsafe_arena_set_allocated_metrics(
    ::metrics_aggregator::Metrics* metrics) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metrics_);
  }
  metrics_ = metrics;
  if (metrics) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.JobWithMetrics.metrics)
}
inline ::metrics_aggregator::Metrics* JobWithMetrics::release_metrics() {
  
  ::metrics_aggregator::Metrics* temp = metrics_;
  metrics_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metrics_aggregator::Metrics* JobWithMetrics::unsafe_arena_release_metrics() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.JobWithMetrics.metrics)
  
  ::metrics_aggregator::Metrics* temp = metrics_;
  metrics_ = nullptr;
  return temp;
}
inline ::metrics_aggregator::Metrics* JobWithMetrics::_internal_mutable_metrics() {
  
  if (metrics_ == nullptr) {
    auto* p = CreateMaybeMessage<::metrics_aggregator::Metrics>(GetArenaForAllocation());
    metrics_ = p;
  }
  return metrics_;
}
inline ::metrics_aggregator::Metrics* JobWithMetrics::mutable_metrics() {
  ::metrics_aggregator::Metrics* _msg = _internal_mutable_metrics();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.JobWithMetrics.metrics)
  return _msg;
}
inline void JobWithMetrics::set_allocated_metrics(::metrics_aggregator::Metrics* metrics) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(metrics_);
  }
  if (metrics) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metrics));
    if (message_arena != submessage_arena) {
      metrics = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metrics, submessage_arena);
    }
    
  } else {
    
  }
  metrics_ = metrics;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.JobWithMetrics.metrics)
}

// -------------------------------------------------------------------

// GetNextJobsResponse

// repeated .carbon.frontend.jobs.JobWithMetrics jobs = 1;
inline int GetNextJobsResponse::_internal_jobs_size() const {
  return jobs_.size();
}
inline int GetNextJobsResponse::jobs_size() const {
  return _internal_jobs_size();
}
inline void GetNextJobsResponse::clear_jobs() {
  jobs_.Clear();
}
inline ::carbon::frontend::jobs::JobWithMetrics* GetNextJobsResponse::mutable_jobs(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetNextJobsResponse.jobs)
  return jobs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::jobs::JobWithMetrics >*
GetNextJobsResponse::mutable_jobs() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.jobs.GetNextJobsResponse.jobs)
  return &jobs_;
}
inline const ::carbon::frontend::jobs::JobWithMetrics& GetNextJobsResponse::_internal_jobs(int index) const {
  return jobs_.Get(index);
}
inline const ::carbon::frontend::jobs::JobWithMetrics& GetNextJobsResponse::jobs(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetNextJobsResponse.jobs)
  return _internal_jobs(index);
}
inline ::carbon::frontend::jobs::JobWithMetrics* GetNextJobsResponse::_internal_add_jobs() {
  return jobs_.Add();
}
inline ::carbon::frontend::jobs::JobWithMetrics* GetNextJobsResponse::add_jobs() {
  ::carbon::frontend::jobs::JobWithMetrics* _add = _internal_add_jobs();
  // @@protoc_insertion_point(field_add:carbon.frontend.jobs.GetNextJobsResponse.jobs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::jobs::JobWithMetrics >&
GetNextJobsResponse::jobs() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.jobs.GetNextJobsResponse.jobs)
  return jobs_;
}

// string activeJobId = 2;
inline void GetNextJobsResponse::clear_activejobid() {
  activejobid_.ClearToEmpty();
}
inline const std::string& GetNextJobsResponse::activejobid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetNextJobsResponse.activeJobId)
  return _internal_activejobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextJobsResponse::set_activejobid(ArgT0&& arg0, ArgT... args) {
 
 activejobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.GetNextJobsResponse.activeJobId)
}
inline std::string* GetNextJobsResponse::mutable_activejobid() {
  std::string* _s = _internal_mutable_activejobid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetNextJobsResponse.activeJobId)
  return _s;
}
inline const std::string& GetNextJobsResponse::_internal_activejobid() const {
  return activejobid_.Get();
}
inline void GetNextJobsResponse::_internal_set_activejobid(const std::string& value) {
  
  activejobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextJobsResponse::_internal_mutable_activejobid() {
  
  return activejobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextJobsResponse::release_activejobid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetNextJobsResponse.activeJobId)
  return activejobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextJobsResponse::set_allocated_activejobid(std::string* activejobid) {
  if (activejobid != nullptr) {
    
  } else {
    
  }
  activejobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), activejobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (activejobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    activejobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetNextJobsResponse.activeJobId)
}

// .carbon.frontend.util.Timestamp timestamp = 3;
inline bool GetNextJobsResponse::_internal_has_timestamp() const {
  return this != internal_default_instance() && timestamp_ != nullptr;
}
inline bool GetNextJobsResponse::has_timestamp() const {
  return _internal_has_timestamp();
}
inline const ::carbon::frontend::util::Timestamp& GetNextJobsResponse::_internal_timestamp() const {
  const ::carbon::frontend::util::Timestamp* p = timestamp_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextJobsResponse::timestamp() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetNextJobsResponse.timestamp)
  return _internal_timestamp();
}
inline void GetNextJobsResponse::unsafe_arena_set_allocated_timestamp(
    ::carbon::frontend::util::Timestamp* timestamp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp_);
  }
  timestamp_ = timestamp;
  if (timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.GetNextJobsResponse.timestamp)
}
inline ::carbon::frontend::util::Timestamp* GetNextJobsResponse::release_timestamp() {
  
  ::carbon::frontend::util::Timestamp* temp = timestamp_;
  timestamp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobsResponse::unsafe_arena_release_timestamp() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetNextJobsResponse.timestamp)
  
  ::carbon::frontend::util::Timestamp* temp = timestamp_;
  timestamp_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobsResponse::_internal_mutable_timestamp() {
  
  if (timestamp_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    timestamp_ = p;
  }
  return timestamp_;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobsResponse::mutable_timestamp() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_timestamp();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetNextJobsResponse.timestamp)
  return _msg;
}
inline void GetNextJobsResponse::set_allocated_timestamp(::carbon::frontend::util::Timestamp* timestamp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp_);
  }
  if (timestamp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp));
    if (message_arena != submessage_arena) {
      timestamp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, timestamp, submessage_arena);
    }
    
  } else {
    
  }
  timestamp_ = timestamp;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetNextJobsResponse.timestamp)
}

// -------------------------------------------------------------------

// GetNextActiveJobIdRequest

// .carbon.frontend.util.Timestamp timestamp = 1;
inline bool GetNextActiveJobIdRequest::_internal_has_timestamp() const {
  return this != internal_default_instance() && timestamp_ != nullptr;
}
inline bool GetNextActiveJobIdRequest::has_timestamp() const {
  return _internal_has_timestamp();
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveJobIdRequest::_internal_timestamp() const {
  const ::carbon::frontend::util::Timestamp* p = timestamp_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveJobIdRequest::timestamp() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetNextActiveJobIdRequest.timestamp)
  return _internal_timestamp();
}
inline void GetNextActiveJobIdRequest::unsafe_arena_set_allocated_timestamp(
    ::carbon::frontend::util::Timestamp* timestamp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp_);
  }
  timestamp_ = timestamp;
  if (timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.GetNextActiveJobIdRequest.timestamp)
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveJobIdRequest::release_timestamp() {
  
  ::carbon::frontend::util::Timestamp* temp = timestamp_;
  timestamp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveJobIdRequest::unsafe_arena_release_timestamp() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetNextActiveJobIdRequest.timestamp)
  
  ::carbon::frontend::util::Timestamp* temp = timestamp_;
  timestamp_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveJobIdRequest::_internal_mutable_timestamp() {
  
  if (timestamp_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    timestamp_ = p;
  }
  return timestamp_;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveJobIdRequest::mutable_timestamp() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_timestamp();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetNextActiveJobIdRequest.timestamp)
  return _msg;
}
inline void GetNextActiveJobIdRequest::set_allocated_timestamp(::carbon::frontend::util::Timestamp* timestamp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp_);
  }
  if (timestamp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp));
    if (message_arena != submessage_arena) {
      timestamp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, timestamp, submessage_arena);
    }
    
  } else {
    
  }
  timestamp_ = timestamp;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetNextActiveJobIdRequest.timestamp)
}

// -------------------------------------------------------------------

// GetNextActiveJobIdResponse

// string activeJobId = 1;
inline void GetNextActiveJobIdResponse::clear_activejobid() {
  activejobid_.ClearToEmpty();
}
inline const std::string& GetNextActiveJobIdResponse::activejobid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetNextActiveJobIdResponse.activeJobId)
  return _internal_activejobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextActiveJobIdResponse::set_activejobid(ArgT0&& arg0, ArgT... args) {
 
 activejobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.GetNextActiveJobIdResponse.activeJobId)
}
inline std::string* GetNextActiveJobIdResponse::mutable_activejobid() {
  std::string* _s = _internal_mutable_activejobid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetNextActiveJobIdResponse.activeJobId)
  return _s;
}
inline const std::string& GetNextActiveJobIdResponse::_internal_activejobid() const {
  return activejobid_.Get();
}
inline void GetNextActiveJobIdResponse::_internal_set_activejobid(const std::string& value) {
  
  activejobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextActiveJobIdResponse::_internal_mutable_activejobid() {
  
  return activejobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextActiveJobIdResponse::release_activejobid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetNextActiveJobIdResponse.activeJobId)
  return activejobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextActiveJobIdResponse::set_allocated_activejobid(std::string* activejobid) {
  if (activejobid != nullptr) {
    
  } else {
    
  }
  activejobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), activejobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (activejobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    activejobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetNextActiveJobIdResponse.activeJobId)
}

// .carbon.frontend.util.Timestamp timestamp = 2;
inline bool GetNextActiveJobIdResponse::_internal_has_timestamp() const {
  return this != internal_default_instance() && timestamp_ != nullptr;
}
inline bool GetNextActiveJobIdResponse::has_timestamp() const {
  return _internal_has_timestamp();
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveJobIdResponse::_internal_timestamp() const {
  const ::carbon::frontend::util::Timestamp* p = timestamp_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveJobIdResponse::timestamp() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetNextActiveJobIdResponse.timestamp)
  return _internal_timestamp();
}
inline void GetNextActiveJobIdResponse::unsafe_arena_set_allocated_timestamp(
    ::carbon::frontend::util::Timestamp* timestamp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp_);
  }
  timestamp_ = timestamp;
  if (timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.GetNextActiveJobIdResponse.timestamp)
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveJobIdResponse::release_timestamp() {
  
  ::carbon::frontend::util::Timestamp* temp = timestamp_;
  timestamp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveJobIdResponse::unsafe_arena_release_timestamp() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetNextActiveJobIdResponse.timestamp)
  
  ::carbon::frontend::util::Timestamp* temp = timestamp_;
  timestamp_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveJobIdResponse::_internal_mutable_timestamp() {
  
  if (timestamp_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    timestamp_ = p;
  }
  return timestamp_;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveJobIdResponse::mutable_timestamp() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_timestamp();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetNextActiveJobIdResponse.timestamp)
  return _msg;
}
inline void GetNextActiveJobIdResponse::set_allocated_timestamp(::carbon::frontend::util::Timestamp* timestamp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp_);
  }
  if (timestamp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp));
    if (message_arena != submessage_arena) {
      timestamp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, timestamp, submessage_arena);
    }
    
  } else {
    
  }
  timestamp_ = timestamp;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetNextActiveJobIdResponse.timestamp)
}

// -------------------------------------------------------------------

// GetJobRequest

// string jobId = 1;
inline void GetJobRequest::clear_jobid() {
  jobid_.ClearToEmpty();
}
inline const std::string& GetJobRequest::jobid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetJobRequest.jobId)
  return _internal_jobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetJobRequest::set_jobid(ArgT0&& arg0, ArgT... args) {
 
 jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.GetJobRequest.jobId)
}
inline std::string* GetJobRequest::mutable_jobid() {
  std::string* _s = _internal_mutable_jobid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetJobRequest.jobId)
  return _s;
}
inline const std::string& GetJobRequest::_internal_jobid() const {
  return jobid_.Get();
}
inline void GetJobRequest::_internal_set_jobid(const std::string& value) {
  
  jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetJobRequest::_internal_mutable_jobid() {
  
  return jobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetJobRequest::release_jobid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetJobRequest.jobId)
  return jobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetJobRequest::set_allocated_jobid(std::string* jobid) {
  if (jobid != nullptr) {
    
  } else {
    
  }
  jobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), jobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (jobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetJobRequest.jobId)
}

// -------------------------------------------------------------------

// GetJobResponse

// .carbon.frontend.jobs.Job job = 1;
inline bool GetJobResponse::_internal_has_job() const {
  return this != internal_default_instance() && job_ != nullptr;
}
inline bool GetJobResponse::has_job() const {
  return _internal_has_job();
}
inline void GetJobResponse::clear_job() {
  if (GetArenaForAllocation() == nullptr && job_ != nullptr) {
    delete job_;
  }
  job_ = nullptr;
}
inline const ::carbon::frontend::jobs::Job& GetJobResponse::_internal_job() const {
  const ::carbon::frontend::jobs::Job* p = job_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::jobs::Job&>(
      ::carbon::frontend::jobs::_Job_default_instance_);
}
inline const ::carbon::frontend::jobs::Job& GetJobResponse::job() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetJobResponse.job)
  return _internal_job();
}
inline void GetJobResponse::unsafe_arena_set_allocated_job(
    ::carbon::frontend::jobs::Job* job) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(job_);
  }
  job_ = job;
  if (job) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.GetJobResponse.job)
}
inline ::carbon::frontend::jobs::Job* GetJobResponse::release_job() {
  
  ::carbon::frontend::jobs::Job* temp = job_;
  job_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::jobs::Job* GetJobResponse::unsafe_arena_release_job() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetJobResponse.job)
  
  ::carbon::frontend::jobs::Job* temp = job_;
  job_ = nullptr;
  return temp;
}
inline ::carbon::frontend::jobs::Job* GetJobResponse::_internal_mutable_job() {
  
  if (job_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::jobs::Job>(GetArenaForAllocation());
    job_ = p;
  }
  return job_;
}
inline ::carbon::frontend::jobs::Job* GetJobResponse::mutable_job() {
  ::carbon::frontend::jobs::Job* _msg = _internal_mutable_job();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetJobResponse.job)
  return _msg;
}
inline void GetJobResponse::set_allocated_job(::carbon::frontend::jobs::Job* job) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete job_;
  }
  if (job) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::jobs::Job>::GetOwningArena(job);
    if (message_arena != submessage_arena) {
      job = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, job, submessage_arena);
    }
    
  } else {
    
  }
  job_ = job;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetJobResponse.job)
}

// -------------------------------------------------------------------

// StartJobRequest

// string jobId = 1;
inline void StartJobRequest::clear_jobid() {
  jobid_.ClearToEmpty();
}
inline const std::string& StartJobRequest::jobid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.StartJobRequest.jobId)
  return _internal_jobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StartJobRequest::set_jobid(ArgT0&& arg0, ArgT... args) {
 
 jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.StartJobRequest.jobId)
}
inline std::string* StartJobRequest::mutable_jobid() {
  std::string* _s = _internal_mutable_jobid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.StartJobRequest.jobId)
  return _s;
}
inline const std::string& StartJobRequest::_internal_jobid() const {
  return jobid_.Get();
}
inline void StartJobRequest::_internal_set_jobid(const std::string& value) {
  
  jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StartJobRequest::_internal_mutable_jobid() {
  
  return jobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StartJobRequest::release_jobid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.StartJobRequest.jobId)
  return jobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StartJobRequest::set_allocated_jobid(std::string* jobid) {
  if (jobid != nullptr) {
    
  } else {
    
  }
  jobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), jobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (jobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.StartJobRequest.jobId)
}

// -------------------------------------------------------------------

// GetConfigDumpRequest

// string jobId = 1;
inline void GetConfigDumpRequest::clear_jobid() {
  jobid_.ClearToEmpty();
}
inline const std::string& GetConfigDumpRequest::jobid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetConfigDumpRequest.jobId)
  return _internal_jobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetConfigDumpRequest::set_jobid(ArgT0&& arg0, ArgT... args) {
 
 jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.GetConfigDumpRequest.jobId)
}
inline std::string* GetConfigDumpRequest::mutable_jobid() {
  std::string* _s = _internal_mutable_jobid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetConfigDumpRequest.jobId)
  return _s;
}
inline const std::string& GetConfigDumpRequest::_internal_jobid() const {
  return jobid_.Get();
}
inline void GetConfigDumpRequest::_internal_set_jobid(const std::string& value) {
  
  jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetConfigDumpRequest::_internal_mutable_jobid() {
  
  return jobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetConfigDumpRequest::release_jobid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetConfigDumpRequest.jobId)
  return jobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetConfigDumpRequest::set_allocated_jobid(std::string* jobid) {
  if (jobid != nullptr) {
    
  } else {
    
  }
  jobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), jobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (jobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetConfigDumpRequest.jobId)
}

// -------------------------------------------------------------------

// GetConfigDumpResponse

// .carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 1;
inline bool GetConfigDumpResponse::_internal_has_rootconfig() const {
  return this != internal_default_instance() && rootconfig_ != nullptr;
}
inline bool GetConfigDumpResponse::has_rootconfig() const {
  return _internal_has_rootconfig();
}
inline const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot& GetConfigDumpResponse::_internal_rootconfig() const {
  const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* p = rootconfig_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot&>(
      ::carbon::frontend::weeding_diagnostics::_ConfigNodeSnapshot_default_instance_);
}
inline const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot& GetConfigDumpResponse::rootconfig() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetConfigDumpResponse.rootConfig)
  return _internal_rootconfig();
}
inline void GetConfigDumpResponse::unsafe_arena_set_allocated_rootconfig(
    ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* rootconfig) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rootconfig_);
  }
  rootconfig_ = rootconfig;
  if (rootconfig) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.GetConfigDumpResponse.rootConfig)
}
inline ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* GetConfigDumpResponse::release_rootconfig() {
  
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* temp = rootconfig_;
  rootconfig_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* GetConfigDumpResponse::unsafe_arena_release_rootconfig() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetConfigDumpResponse.rootConfig)
  
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* temp = rootconfig_;
  rootconfig_ = nullptr;
  return temp;
}
inline ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* GetConfigDumpResponse::_internal_mutable_rootconfig() {
  
  if (rootconfig_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot>(GetArenaForAllocation());
    rootconfig_ = p;
  }
  return rootconfig_;
}
inline ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* GetConfigDumpResponse::mutable_rootconfig() {
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* _msg = _internal_mutable_rootconfig();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetConfigDumpResponse.rootConfig)
  return _msg;
}
inline void GetConfigDumpResponse::set_allocated_rootconfig(::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* rootconfig) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(rootconfig_);
  }
  if (rootconfig) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rootconfig));
    if (message_arena != submessage_arena) {
      rootconfig = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rootconfig, submessage_arena);
    }
    
  } else {
    
  }
  rootconfig_ = rootconfig;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetConfigDumpResponse.rootConfig)
}

// -------------------------------------------------------------------

// GetActiveJobMetricsResponse

// .metrics_aggregator.Metrics jobMetrics = 1;
inline bool GetActiveJobMetricsResponse::_internal_has_jobmetrics() const {
  return this != internal_default_instance() && jobmetrics_ != nullptr;
}
inline bool GetActiveJobMetricsResponse::has_jobmetrics() const {
  return _internal_has_jobmetrics();
}
inline const ::metrics_aggregator::Metrics& GetActiveJobMetricsResponse::_internal_jobmetrics() const {
  const ::metrics_aggregator::Metrics* p = jobmetrics_;
  return p != nullptr ? *p : reinterpret_cast<const ::metrics_aggregator::Metrics&>(
      ::metrics_aggregator::_Metrics_default_instance_);
}
inline const ::metrics_aggregator::Metrics& GetActiveJobMetricsResponse::jobmetrics() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetActiveJobMetricsResponse.jobMetrics)
  return _internal_jobmetrics();
}
inline void GetActiveJobMetricsResponse::unsafe_arena_set_allocated_jobmetrics(
    ::metrics_aggregator::Metrics* jobmetrics) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(jobmetrics_);
  }
  jobmetrics_ = jobmetrics;
  if (jobmetrics) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.GetActiveJobMetricsResponse.jobMetrics)
}
inline ::metrics_aggregator::Metrics* GetActiveJobMetricsResponse::release_jobmetrics() {
  
  ::metrics_aggregator::Metrics* temp = jobmetrics_;
  jobmetrics_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metrics_aggregator::Metrics* GetActiveJobMetricsResponse::unsafe_arena_release_jobmetrics() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetActiveJobMetricsResponse.jobMetrics)
  
  ::metrics_aggregator::Metrics* temp = jobmetrics_;
  jobmetrics_ = nullptr;
  return temp;
}
inline ::metrics_aggregator::Metrics* GetActiveJobMetricsResponse::_internal_mutable_jobmetrics() {
  
  if (jobmetrics_ == nullptr) {
    auto* p = CreateMaybeMessage<::metrics_aggregator::Metrics>(GetArenaForAllocation());
    jobmetrics_ = p;
  }
  return jobmetrics_;
}
inline ::metrics_aggregator::Metrics* GetActiveJobMetricsResponse::mutable_jobmetrics() {
  ::metrics_aggregator::Metrics* _msg = _internal_mutable_jobmetrics();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetActiveJobMetricsResponse.jobMetrics)
  return _msg;
}
inline void GetActiveJobMetricsResponse::set_allocated_jobmetrics(::metrics_aggregator::Metrics* jobmetrics) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(jobmetrics_);
  }
  if (jobmetrics) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(jobmetrics));
    if (message_arena != submessage_arena) {
      jobmetrics = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, jobmetrics, submessage_arena);
    }
    
  } else {
    
  }
  jobmetrics_ = jobmetrics;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetActiveJobMetricsResponse.jobMetrics)
}

// -------------------------------------------------------------------

// DeleteJobRequest

// string jobId = 1;
inline void DeleteJobRequest::clear_jobid() {
  jobid_.ClearToEmpty();
}
inline const std::string& DeleteJobRequest::jobid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.DeleteJobRequest.jobId)
  return _internal_jobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteJobRequest::set_jobid(ArgT0&& arg0, ArgT... args) {
 
 jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.DeleteJobRequest.jobId)
}
inline std::string* DeleteJobRequest::mutable_jobid() {
  std::string* _s = _internal_mutable_jobid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.DeleteJobRequest.jobId)
  return _s;
}
inline const std::string& DeleteJobRequest::_internal_jobid() const {
  return jobid_.Get();
}
inline void DeleteJobRequest::_internal_set_jobid(const std::string& value) {
  
  jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteJobRequest::_internal_mutable_jobid() {
  
  return jobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteJobRequest::release_jobid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.DeleteJobRequest.jobId)
  return jobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteJobRequest::set_allocated_jobid(std::string* jobid) {
  if (jobid != nullptr) {
    
  } else {
    
  }
  jobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), jobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (jobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.DeleteJobRequest.jobId)
}

// -------------------------------------------------------------------

// MarkJobCompletedRequest

// string jobId = 1;
inline void MarkJobCompletedRequest::clear_jobid() {
  jobid_.ClearToEmpty();
}
inline const std::string& MarkJobCompletedRequest::jobid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.MarkJobCompletedRequest.jobId)
  return _internal_jobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MarkJobCompletedRequest::set_jobid(ArgT0&& arg0, ArgT... args) {
 
 jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.MarkJobCompletedRequest.jobId)
}
inline std::string* MarkJobCompletedRequest::mutable_jobid() {
  std::string* _s = _internal_mutable_jobid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.MarkJobCompletedRequest.jobId)
  return _s;
}
inline const std::string& MarkJobCompletedRequest::_internal_jobid() const {
  return jobid_.Get();
}
inline void MarkJobCompletedRequest::_internal_set_jobid(const std::string& value) {
  
  jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MarkJobCompletedRequest::_internal_mutable_jobid() {
  
  return jobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MarkJobCompletedRequest::release_jobid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.MarkJobCompletedRequest.jobId)
  return jobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MarkJobCompletedRequest::set_allocated_jobid(std::string* jobid) {
  if (jobid != nullptr) {
    
  } else {
    
  }
  jobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), jobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (jobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.MarkJobCompletedRequest.jobId)
}

// -------------------------------------------------------------------

// MarkJobIncompleteRequest

// string jobId = 1;
inline void MarkJobIncompleteRequest::clear_jobid() {
  jobid_.ClearToEmpty();
}
inline const std::string& MarkJobIncompleteRequest::jobid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.MarkJobIncompleteRequest.jobId)
  return _internal_jobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MarkJobIncompleteRequest::set_jobid(ArgT0&& arg0, ArgT... args) {
 
 jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.MarkJobIncompleteRequest.jobId)
}
inline std::string* MarkJobIncompleteRequest::mutable_jobid() {
  std::string* _s = _internal_mutable_jobid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.MarkJobIncompleteRequest.jobId)
  return _s;
}
inline const std::string& MarkJobIncompleteRequest::_internal_jobid() const {
  return jobid_.Get();
}
inline void MarkJobIncompleteRequest::_internal_set_jobid(const std::string& value) {
  
  jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MarkJobIncompleteRequest::_internal_mutable_jobid() {
  
  return jobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MarkJobIncompleteRequest::release_jobid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.MarkJobIncompleteRequest.jobId)
  return jobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MarkJobIncompleteRequest::set_allocated_jobid(std::string* jobid) {
  if (jobid != nullptr) {
    
  } else {
    
  }
  jobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), jobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (jobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.MarkJobIncompleteRequest.jobId)
}

// -------------------------------------------------------------------

// GetNextJobRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextJobRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextJobRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextJobRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextJobRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetNextJobRequest.ts)
  return _internal_ts();
}
inline void GetNextJobRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.GetNextJobRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextJobRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetNextJobRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetNextJobRequest.ts)
  return _msg;
}
inline void GetNextJobRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetNextJobRequest.ts)
}

// string jobId = 2;
inline void GetNextJobRequest::clear_jobid() {
  jobid_.ClearToEmpty();
}
inline const std::string& GetNextJobRequest::jobid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetNextJobRequest.jobId)
  return _internal_jobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextJobRequest::set_jobid(ArgT0&& arg0, ArgT... args) {
 
 jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.jobs.GetNextJobRequest.jobId)
}
inline std::string* GetNextJobRequest::mutable_jobid() {
  std::string* _s = _internal_mutable_jobid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetNextJobRequest.jobId)
  return _s;
}
inline const std::string& GetNextJobRequest::_internal_jobid() const {
  return jobid_.Get();
}
inline void GetNextJobRequest::_internal_set_jobid(const std::string& value) {
  
  jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextJobRequest::_internal_mutable_jobid() {
  
  return jobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextJobRequest::release_jobid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetNextJobRequest.jobId)
  return jobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextJobRequest::set_allocated_jobid(std::string* jobid) {
  if (jobid != nullptr) {
    
  } else {
    
  }
  jobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), jobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (jobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetNextJobRequest.jobId)
}

// -------------------------------------------------------------------

// GetNextJobResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextJobResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextJobResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextJobResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextJobResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetNextJobResponse.ts)
  return _internal_ts();
}
inline void GetNextJobResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.GetNextJobResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextJobResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetNextJobResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextJobResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetNextJobResponse.ts)
  return _msg;
}
inline void GetNextJobResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetNextJobResponse.ts)
}

// .carbon.frontend.jobs.JobWithMetrics job = 2;
inline bool GetNextJobResponse::_internal_has_job() const {
  return this != internal_default_instance() && job_ != nullptr;
}
inline bool GetNextJobResponse::has_job() const {
  return _internal_has_job();
}
inline void GetNextJobResponse::clear_job() {
  if (GetArenaForAllocation() == nullptr && job_ != nullptr) {
    delete job_;
  }
  job_ = nullptr;
}
inline const ::carbon::frontend::jobs::JobWithMetrics& GetNextJobResponse::_internal_job() const {
  const ::carbon::frontend::jobs::JobWithMetrics* p = job_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::jobs::JobWithMetrics&>(
      ::carbon::frontend::jobs::_JobWithMetrics_default_instance_);
}
inline const ::carbon::frontend::jobs::JobWithMetrics& GetNextJobResponse::job() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.jobs.GetNextJobResponse.job)
  return _internal_job();
}
inline void GetNextJobResponse::unsafe_arena_set_allocated_job(
    ::carbon::frontend::jobs::JobWithMetrics* job) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(job_);
  }
  job_ = job;
  if (job) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.jobs.GetNextJobResponse.job)
}
inline ::carbon::frontend::jobs::JobWithMetrics* GetNextJobResponse::release_job() {
  
  ::carbon::frontend::jobs::JobWithMetrics* temp = job_;
  job_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::jobs::JobWithMetrics* GetNextJobResponse::unsafe_arena_release_job() {
  // @@protoc_insertion_point(field_release:carbon.frontend.jobs.GetNextJobResponse.job)
  
  ::carbon::frontend::jobs::JobWithMetrics* temp = job_;
  job_ = nullptr;
  return temp;
}
inline ::carbon::frontend::jobs::JobWithMetrics* GetNextJobResponse::_internal_mutable_job() {
  
  if (job_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::jobs::JobWithMetrics>(GetArenaForAllocation());
    job_ = p;
  }
  return job_;
}
inline ::carbon::frontend::jobs::JobWithMetrics* GetNextJobResponse::mutable_job() {
  ::carbon::frontend::jobs::JobWithMetrics* _msg = _internal_mutable_job();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.jobs.GetNextJobResponse.job)
  return _msg;
}
inline void GetNextJobResponse::set_allocated_job(::carbon::frontend::jobs::JobWithMetrics* job) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete job_;
  }
  if (job) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::jobs::JobWithMetrics>::GetOwningArena(job);
    if (message_arena != submessage_arena) {
      job = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, job, submessage_arena);
    }
    
  } else {
    
  }
  job_ = job;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.jobs.GetNextJobResponse.job)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace jobs
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fjobs_2eproto

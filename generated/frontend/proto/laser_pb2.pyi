"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class LaserDescriptor(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_number: builtin___int = ...
    laser_id: builtin___int = ...
    camera_id: typing___Text = ...
    serial: typing___Text = ...

    def __init__(self,
        *,
        row_number : typing___Optional[builtin___int] = None,
        laser_id : typing___Optional[builtin___int] = None,
        camera_id : typing___Optional[typing___Text] = None,
        serial : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"camera_id",b"camera_id",u"laser_id",b"laser_id",u"row_number",b"row_number",u"serial",b"serial"]) -> None: ...
type___LaserDescriptor = LaserDescriptor

class LaserState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    firing: builtin___bool = ...
    enabled: builtin___bool = ...
    error: builtin___bool = ...
    total_fire_count: builtin___int = ...
    total_fire_time_ms: builtin___int = ...
    delta_temp: builtin___float = ...
    current: builtin___float = ...
    target_trajectory_id: builtin___int = ...
    lifetime_sec: builtin___int = ...
    power_level: builtin___float = ...
    installed_at: builtin___int = ...

    @property
    def laser_descriptor(self) -> type___LaserDescriptor: ...

    def __init__(self,
        *,
        laser_descriptor : typing___Optional[type___LaserDescriptor] = None,
        firing : typing___Optional[builtin___bool] = None,
        enabled : typing___Optional[builtin___bool] = None,
        error : typing___Optional[builtin___bool] = None,
        total_fire_count : typing___Optional[builtin___int] = None,
        total_fire_time_ms : typing___Optional[builtin___int] = None,
        delta_temp : typing___Optional[builtin___float] = None,
        current : typing___Optional[builtin___float] = None,
        target_trajectory_id : typing___Optional[builtin___int] = None,
        lifetime_sec : typing___Optional[builtin___int] = None,
        power_level : typing___Optional[builtin___float] = None,
        installed_at : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"laser_descriptor",b"laser_descriptor"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current",b"current",u"delta_temp",b"delta_temp",u"enabled",b"enabled",u"error",b"error",u"firing",b"firing",u"installed_at",b"installed_at",u"laser_descriptor",b"laser_descriptor",u"lifetime_sec",b"lifetime_sec",u"power_level",b"power_level",u"target_trajectory_id",b"target_trajectory_id",u"total_fire_count",b"total_fire_count",u"total_fire_time_ms",b"total_fire_time_ms"]) -> None: ...
type___LaserState = LaserState

class LaserStateList(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def lasers(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___LaserState]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        lasers : typing___Optional[typing___Iterable[type___LaserState]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lasers",b"lasers",u"ts",b"ts"]) -> None: ...
type___LaserStateList = LaserStateList

class RowRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_number: builtin___int = ...

    def __init__(self,
        *,
        row_number : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"row_number",b"row_number"]) -> None: ...
type___RowRequest = RowRequest

class SetLaserPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    power_level: builtin___float = ...

    @property
    def laser_descriptor(self) -> type___LaserDescriptor: ...

    def __init__(self,
        *,
        laser_descriptor : typing___Optional[type___LaserDescriptor] = None,
        power_level : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"laser_descriptor",b"laser_descriptor"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"laser_descriptor",b"laser_descriptor",u"power_level",b"power_level"]) -> None: ...
type___SetLaserPowerRequest = SetLaserPowerRequest

class FixLaserMetricsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    total_fire_count: builtin___int = ...
    total_fire_time_ms: builtin___int = ...
    lifetime_sec: builtin___int = ...

    @property
    def laser_descriptor(self) -> type___LaserDescriptor: ...

    def __init__(self,
        *,
        laser_descriptor : typing___Optional[type___LaserDescriptor] = None,
        total_fire_count : typing___Optional[builtin___int] = None,
        total_fire_time_ms : typing___Optional[builtin___int] = None,
        lifetime_sec : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"laser_descriptor",b"laser_descriptor"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"laser_descriptor",b"laser_descriptor",u"lifetime_sec",b"lifetime_sec",u"total_fire_count",b"total_fire_count",u"total_fire_time_ms",b"total_fire_time_ms"]) -> None: ...
type___FixLaserMetricsRequest = FixLaserMetricsRequest

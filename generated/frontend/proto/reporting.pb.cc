// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/reporting.proto

#include "frontend/proto/reporting.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace features {
constexpr Location::Location(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , latitude_(0)
  , longitude_(0)
  , altitude_(0)
  , is_weeding_(false){}
struct LocationDefaultTypeInternal {
  constexpr LocationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LocationDefaultTypeInternal() {}
  union {
    Location _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LocationDefaultTypeInternal _Location_default_instance_;
constexpr LocationHistory::LocationHistory(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : history_()
  , ts_(nullptr){}
struct LocationHistoryDefaultTypeInternal {
  constexpr LocationHistoryDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LocationHistoryDefaultTypeInternal() {}
  union {
    LocationHistory _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LocationHistoryDefaultTypeInternal _LocationHistory_default_instance_;
}  // namespace features
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2freporting_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2freporting_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2freporting_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2freporting_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::Location, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::Location, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::Location, latitude_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::Location, longitude_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::Location, altitude_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::Location, is_weeding_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::LocationHistory, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::LocationHistory, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::LocationHistory, history_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::features::Location)},
  { 11, -1, -1, sizeof(::carbon::frontend::features::LocationHistory)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::features::_Location_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::features::_LocationHistory_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2freporting_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\036frontend/proto/reporting.proto\022\030carbon"
  ".frontend.features\032\031frontend/proto/util."
  "proto\"\202\001\n\010Location\022+\n\002ts\030\001 \001(\0132\037.carbon."
  "frontend.util.Timestamp\022\020\n\010latitude\030\002 \001("
  "\002\022\021\n\tlongitude\030\003 \001(\002\022\020\n\010altitude\030\004 \001(\002\022\022"
  "\n\nis_weeding\030\005 \001(\010\"s\n\017LocationHistory\022+\n"
  "\002ts\030\001 \001(\0132\037.carbon.frontend.util.Timesta"
  "mp\0223\n\007history\030\002 \003(\0132\".carbon.frontend.fe"
  "atures.Location2x\n\020ReportingService\022d\n\026G"
  "etNextLocationHistory\022\037.carbon.frontend."
  "util.Timestamp\032).carbon.frontend.feature"
  "s.LocationHistoryB\020Z\016proto/frontendb\006pro"
  "to3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2freporting_2eproto_deps[1] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2freporting_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2freporting_2eproto = {
  false, false, 483, descriptor_table_protodef_frontend_2fproto_2freporting_2eproto, "frontend/proto/reporting.proto", 
  &descriptor_table_frontend_2fproto_2freporting_2eproto_once, descriptor_table_frontend_2fproto_2freporting_2eproto_deps, 1, 2,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2freporting_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2freporting_2eproto, file_level_enum_descriptors_frontend_2fproto_2freporting_2eproto, file_level_service_descriptors_frontend_2fproto_2freporting_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2freporting_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2freporting_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2freporting_2eproto(&descriptor_table_frontend_2fproto_2freporting_2eproto);
namespace carbon {
namespace frontend {
namespace features {

// ===================================================================

class Location::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const Location* msg);
};

const ::carbon::frontend::util::Timestamp&
Location::_Internal::ts(const Location* msg) {
  return *msg->ts_;
}
void Location::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
Location::Location(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.features.Location)
}
Location::Location(const Location& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&latitude_, &from.latitude_,
    static_cast<size_t>(reinterpret_cast<char*>(&is_weeding_) -
    reinterpret_cast<char*>(&latitude_)) + sizeof(is_weeding_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.features.Location)
}

inline void Location::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&is_weeding_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(is_weeding_));
}

Location::~Location() {
  // @@protoc_insertion_point(destructor:carbon.frontend.features.Location)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Location::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void Location::ArenaDtor(void* object) {
  Location* _this = reinterpret_cast< Location* >(object);
  (void)_this;
}
void Location::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Location::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Location::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.features.Location)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&latitude_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&is_weeding_) -
      reinterpret_cast<char*>(&latitude_)) + sizeof(is_weeding_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Location::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float latitude = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          latitude_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float longitude = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          longitude_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float altitude = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          altitude_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // bool is_weeding = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          is_weeding_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Location::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.features.Location)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // float latitude = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_latitude = this->_internal_latitude();
  uint32_t raw_latitude;
  memcpy(&raw_latitude, &tmp_latitude, sizeof(tmp_latitude));
  if (raw_latitude != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_latitude(), target);
  }

  // float longitude = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_longitude = this->_internal_longitude();
  uint32_t raw_longitude;
  memcpy(&raw_longitude, &tmp_longitude, sizeof(tmp_longitude));
  if (raw_longitude != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_longitude(), target);
  }

  // float altitude = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_altitude = this->_internal_altitude();
  uint32_t raw_altitude;
  memcpy(&raw_altitude, &tmp_altitude, sizeof(tmp_altitude));
  if (raw_altitude != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_altitude(), target);
  }

  // bool is_weeding = 5;
  if (this->_internal_is_weeding() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_is_weeding(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.features.Location)
  return target;
}

size_t Location::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.features.Location)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // float latitude = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_latitude = this->_internal_latitude();
  uint32_t raw_latitude;
  memcpy(&raw_latitude, &tmp_latitude, sizeof(tmp_latitude));
  if (raw_latitude != 0) {
    total_size += 1 + 4;
  }

  // float longitude = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_longitude = this->_internal_longitude();
  uint32_t raw_longitude;
  memcpy(&raw_longitude, &tmp_longitude, sizeof(tmp_longitude));
  if (raw_longitude != 0) {
    total_size += 1 + 4;
  }

  // float altitude = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_altitude = this->_internal_altitude();
  uint32_t raw_altitude;
  memcpy(&raw_altitude, &tmp_altitude, sizeof(tmp_altitude));
  if (raw_altitude != 0) {
    total_size += 1 + 4;
  }

  // bool is_weeding = 5;
  if (this->_internal_is_weeding() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Location::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Location::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Location::GetClassData() const { return &_class_data_; }

void Location::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Location *>(to)->MergeFrom(
      static_cast<const Location &>(from));
}


void Location::MergeFrom(const Location& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.features.Location)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_latitude = from._internal_latitude();
  uint32_t raw_latitude;
  memcpy(&raw_latitude, &tmp_latitude, sizeof(tmp_latitude));
  if (raw_latitude != 0) {
    _internal_set_latitude(from._internal_latitude());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_longitude = from._internal_longitude();
  uint32_t raw_longitude;
  memcpy(&raw_longitude, &tmp_longitude, sizeof(tmp_longitude));
  if (raw_longitude != 0) {
    _internal_set_longitude(from._internal_longitude());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_altitude = from._internal_altitude();
  uint32_t raw_altitude;
  memcpy(&raw_altitude, &tmp_altitude, sizeof(tmp_altitude));
  if (raw_altitude != 0) {
    _internal_set_altitude(from._internal_altitude());
  }
  if (from._internal_is_weeding() != 0) {
    _internal_set_is_weeding(from._internal_is_weeding());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Location::CopyFrom(const Location& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.features.Location)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Location::IsInitialized() const {
  return true;
}

void Location::InternalSwap(Location* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Location, is_weeding_)
      + sizeof(Location::is_weeding_)
      - PROTOBUF_FIELD_OFFSET(Location, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Location::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2freporting_2eproto_getter, &descriptor_table_frontend_2fproto_2freporting_2eproto_once,
      file_level_metadata_frontend_2fproto_2freporting_2eproto[0]);
}

// ===================================================================

class LocationHistory::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const LocationHistory* msg);
};

const ::carbon::frontend::util::Timestamp&
LocationHistory::_Internal::ts(const LocationHistory* msg) {
  return *msg->ts_;
}
void LocationHistory::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
LocationHistory::LocationHistory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  history_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.features.LocationHistory)
}
LocationHistory::LocationHistory(const LocationHistory& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      history_(from.history_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.features.LocationHistory)
}

inline void LocationHistory::SharedCtor() {
ts_ = nullptr;
}

LocationHistory::~LocationHistory() {
  // @@protoc_insertion_point(destructor:carbon.frontend.features.LocationHistory)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LocationHistory::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void LocationHistory::ArenaDtor(void* object) {
  LocationHistory* _this = reinterpret_cast< LocationHistory* >(object);
  (void)_this;
}
void LocationHistory::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LocationHistory::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LocationHistory::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.features.LocationHistory)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  history_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LocationHistory::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.features.Location history = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_history(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LocationHistory::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.features.LocationHistory)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.frontend.features.Location history = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_history_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_history(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.features.LocationHistory)
  return target;
}

size_t LocationHistory::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.features.LocationHistory)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.features.Location history = 2;
  total_size += 1UL * this->_internal_history_size();
  for (const auto& msg : this->history_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LocationHistory::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LocationHistory::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LocationHistory::GetClassData() const { return &_class_data_; }

void LocationHistory::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LocationHistory *>(to)->MergeFrom(
      static_cast<const LocationHistory &>(from));
}


void LocationHistory::MergeFrom(const LocationHistory& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.features.LocationHistory)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  history_.MergeFrom(from.history_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LocationHistory::CopyFrom(const LocationHistory& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.features.LocationHistory)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LocationHistory::IsInitialized() const {
  return true;
}

void LocationHistory::InternalSwap(LocationHistory* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  history_.InternalSwap(&other->history_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LocationHistory::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2freporting_2eproto_getter, &descriptor_table_frontend_2fproto_2freporting_2eproto_once,
      file_level_metadata_frontend_2fproto_2freporting_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace features
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::features::Location* Arena::CreateMaybeMessage< ::carbon::frontend::features::Location >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::features::Location >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::features::LocationHistory* Arena::CreateMaybeMessage< ::carbon::frontend::features::LocationHistory >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::features::LocationHistory >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/almanac.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2falmanac_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2falmanac_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
#include "proto/almanac/almanac.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2falmanac_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2falmanac_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[28]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2falmanac_2eproto;
namespace carbon {
namespace frontend {
namespace almanac {
class DeleteAlmanacConfigRequest;
struct DeleteAlmanacConfigRequestDefaultTypeInternal;
extern DeleteAlmanacConfigRequestDefaultTypeInternal _DeleteAlmanacConfigRequest_default_instance_;
class DeleteDiscriminatorConfigRequest;
struct DeleteDiscriminatorConfigRequestDefaultTypeInternal;
extern DeleteDiscriminatorConfigRequestDefaultTypeInternal _DeleteDiscriminatorConfigRequest_default_instance_;
class FetchModelinatorConfigRequest;
struct FetchModelinatorConfigRequestDefaultTypeInternal;
extern FetchModelinatorConfigRequestDefaultTypeInternal _FetchModelinatorConfigRequest_default_instance_;
class FetchModelinatorConfigResponse;
struct FetchModelinatorConfigResponseDefaultTypeInternal;
extern FetchModelinatorConfigResponseDefaultTypeInternal _FetchModelinatorConfigResponse_default_instance_;
class GetConfigDataResponse;
struct GetConfigDataResponseDefaultTypeInternal;
extern GetConfigDataResponseDefaultTypeInternal _GetConfigDataResponse_default_instance_;
class GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse;
struct GetConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal;
extern GetConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal _GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse_default_instance_;
class GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse;
struct GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal;
extern GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal _GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse_default_instance_;
class GetNextAlmanacConfigResponse;
struct GetNextAlmanacConfigResponseDefaultTypeInternal;
extern GetNextAlmanacConfigResponseDefaultTypeInternal _GetNextAlmanacConfigResponse_default_instance_;
class GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse;
struct GetNextAlmanacConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal;
extern GetNextAlmanacConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal _GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse_default_instance_;
class GetNextConfigDataRequest;
struct GetNextConfigDataRequestDefaultTypeInternal;
extern GetNextConfigDataRequestDefaultTypeInternal _GetNextConfigDataRequest_default_instance_;
class GetNextConfigDataResponse;
struct GetNextConfigDataResponseDefaultTypeInternal;
extern GetNextConfigDataResponseDefaultTypeInternal _GetNextConfigDataResponse_default_instance_;
class GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse;
struct GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal;
extern GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUseDefaultTypeInternal _GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse_default_instance_;
class GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse;
struct GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal;
extern GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUseDefaultTypeInternal _GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse_default_instance_;
class GetNextDiscriminatorConfigResponse;
struct GetNextDiscriminatorConfigResponseDefaultTypeInternal;
extern GetNextDiscriminatorConfigResponseDefaultTypeInternal _GetNextDiscriminatorConfigResponse_default_instance_;
class GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse;
struct GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal;
extern GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUseDefaultTypeInternal _GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse_default_instance_;
class GetNextModelinatorConfigResponse;
struct GetNextModelinatorConfigResponseDefaultTypeInternal;
extern GetNextModelinatorConfigResponseDefaultTypeInternal _GetNextModelinatorConfigResponse_default_instance_;
class LoadAlmanacConfigRequest;
struct LoadAlmanacConfigRequestDefaultTypeInternal;
extern LoadAlmanacConfigRequestDefaultTypeInternal _LoadAlmanacConfigRequest_default_instance_;
class LoadAlmanacConfigResponse;
struct LoadAlmanacConfigResponseDefaultTypeInternal;
extern LoadAlmanacConfigResponseDefaultTypeInternal _LoadAlmanacConfigResponse_default_instance_;
class LoadDiscriminatorConfigRequest;
struct LoadDiscriminatorConfigRequestDefaultTypeInternal;
extern LoadDiscriminatorConfigRequestDefaultTypeInternal _LoadDiscriminatorConfigRequest_default_instance_;
class LoadDiscriminatorConfigResponse;
struct LoadDiscriminatorConfigResponseDefaultTypeInternal;
extern LoadDiscriminatorConfigResponseDefaultTypeInternal _LoadDiscriminatorConfigResponse_default_instance_;
class ResetModelinatorConfigRequest;
struct ResetModelinatorConfigRequestDefaultTypeInternal;
extern ResetModelinatorConfigRequestDefaultTypeInternal _ResetModelinatorConfigRequest_default_instance_;
class SaveAlmanacConfigRequest;
struct SaveAlmanacConfigRequestDefaultTypeInternal;
extern SaveAlmanacConfigRequestDefaultTypeInternal _SaveAlmanacConfigRequest_default_instance_;
class SaveAlmanacConfigResponse;
struct SaveAlmanacConfigResponseDefaultTypeInternal;
extern SaveAlmanacConfigResponseDefaultTypeInternal _SaveAlmanacConfigResponse_default_instance_;
class SaveDiscriminatorConfigRequest;
struct SaveDiscriminatorConfigRequestDefaultTypeInternal;
extern SaveDiscriminatorConfigRequestDefaultTypeInternal _SaveDiscriminatorConfigRequest_default_instance_;
class SaveDiscriminatorConfigResponse;
struct SaveDiscriminatorConfigResponseDefaultTypeInternal;
extern SaveDiscriminatorConfigResponseDefaultTypeInternal _SaveDiscriminatorConfigResponse_default_instance_;
class SaveModelinatorConfigRequest;
struct SaveModelinatorConfigRequestDefaultTypeInternal;
extern SaveModelinatorConfigRequestDefaultTypeInternal _SaveModelinatorConfigRequest_default_instance_;
class SetActiveAlmanacConfigRequest;
struct SetActiveAlmanacConfigRequestDefaultTypeInternal;
extern SetActiveAlmanacConfigRequestDefaultTypeInternal _SetActiveAlmanacConfigRequest_default_instance_;
class SetActiveDiscriminatorConfigRequest;
struct SetActiveDiscriminatorConfigRequestDefaultTypeInternal;
extern SetActiveDiscriminatorConfigRequestDefaultTypeInternal _SetActiveDiscriminatorConfigRequest_default_instance_;
}  // namespace almanac
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::DeleteAlmanacConfigRequest>(Arena*);
template<> ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest>(Arena*);
template<> ::carbon::frontend::almanac::FetchModelinatorConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::FetchModelinatorConfigRequest>(Arena*);
template<> ::carbon::frontend::almanac::FetchModelinatorConfigResponse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::FetchModelinatorConfigResponse>(Arena*);
template<> ::carbon::frontend::almanac::GetConfigDataResponse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetConfigDataResponse>(Arena*);
template<> ::carbon::frontend::almanac::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::almanac::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetNextAlmanacConfigResponse>(Arena*);
template<> ::carbon::frontend::almanac::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::almanac::GetNextConfigDataRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetNextConfigDataRequest>(Arena*);
template<> ::carbon::frontend::almanac::GetNextConfigDataResponse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetNextConfigDataResponse>(Arena*);
template<> ::carbon::frontend::almanac::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::almanac::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>(Arena*);
template<> ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::GetNextModelinatorConfigResponse>(Arena*);
template<> ::carbon::frontend::almanac::LoadAlmanacConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::LoadAlmanacConfigRequest>(Arena*);
template<> ::carbon::frontend::almanac::LoadAlmanacConfigResponse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::LoadAlmanacConfigResponse>(Arena*);
template<> ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::LoadDiscriminatorConfigRequest>(Arena*);
template<> ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>(Arena*);
template<> ::carbon::frontend::almanac::ResetModelinatorConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::ResetModelinatorConfigRequest>(Arena*);
template<> ::carbon::frontend::almanac::SaveAlmanacConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::SaveAlmanacConfigRequest>(Arena*);
template<> ::carbon::frontend::almanac::SaveAlmanacConfigResponse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::SaveAlmanacConfigResponse>(Arena*);
template<> ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::SaveDiscriminatorConfigRequest>(Arena*);
template<> ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* Arena::CreateMaybeMessage<::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>(Arena*);
template<> ::carbon::frontend::almanac::SaveModelinatorConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::SaveModelinatorConfigRequest>(Arena*);
template<> ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::SetActiveAlmanacConfigRequest>(Arena*);
template<> ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace almanac {

// ===================================================================

class GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse();
  explicit constexpr GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse& other);
  static const GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse*>(&_GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse();
  explicit constexpr GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse& other);
  static const GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse*>(&_GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetConfigDataResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.GetConfigDataResponse) */ {
 public:
  inline GetConfigDataResponse() : GetConfigDataResponse(nullptr) {}
  ~GetConfigDataResponse() override;
  explicit constexpr GetConfigDataResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetConfigDataResponse(const GetConfigDataResponse& from);
  GetConfigDataResponse(GetConfigDataResponse&& from) noexcept
    : GetConfigDataResponse() {
    *this = ::std::move(from);
  }

  inline GetConfigDataResponse& operator=(const GetConfigDataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetConfigDataResponse& operator=(GetConfigDataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetConfigDataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetConfigDataResponse* internal_default_instance() {
    return reinterpret_cast<const GetConfigDataResponse*>(
               &_GetConfigDataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GetConfigDataResponse& a, GetConfigDataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetConfigDataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetConfigDataResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetConfigDataResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetConfigDataResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetConfigDataResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetConfigDataResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetConfigDataResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.GetConfigDataResponse";
  }
  protected:
  explicit GetConfigDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kCropCategoryNamesFieldNumber = 2,
    kWeedCategoryNamesFieldNumber = 3,
    kNumSizeCategoriesFieldNumber = 1,
  };
  // map<string, string> crop_category_names = 2;
  int crop_category_names_size() const;
  private:
  int _internal_crop_category_names_size() const;
  public:
  void clear_crop_category_names();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_crop_category_names() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_crop_category_names();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      crop_category_names() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_crop_category_names();

  // map<string, string> weed_category_names = 3;
  int weed_category_names_size() const;
  private:
  int _internal_weed_category_names_size() const;
  public:
  void clear_weed_category_names();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_weed_category_names() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_weed_category_names();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      weed_category_names() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_weed_category_names();

  // uint32 num_size_categories = 1;
  void clear_num_size_categories();
  uint32_t num_size_categories() const;
  void set_num_size_categories(uint32_t value);
  private:
  uint32_t _internal_num_size_categories() const;
  void _internal_set_num_size_categories(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetConfigDataResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetConfigDataResponse_CropCategoryNamesEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> crop_category_names_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetConfigDataResponse_WeedCategoryNamesEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> weed_category_names_;
  uint32_t num_size_categories_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class LoadAlmanacConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.LoadAlmanacConfigRequest) */ {
 public:
  inline LoadAlmanacConfigRequest() : LoadAlmanacConfigRequest(nullptr) {}
  ~LoadAlmanacConfigRequest() override;
  explicit constexpr LoadAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LoadAlmanacConfigRequest(const LoadAlmanacConfigRequest& from);
  LoadAlmanacConfigRequest(LoadAlmanacConfigRequest&& from) noexcept
    : LoadAlmanacConfigRequest() {
    *this = ::std::move(from);
  }

  inline LoadAlmanacConfigRequest& operator=(const LoadAlmanacConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoadAlmanacConfigRequest& operator=(LoadAlmanacConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LoadAlmanacConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const LoadAlmanacConfigRequest* internal_default_instance() {
    return reinterpret_cast<const LoadAlmanacConfigRequest*>(
               &_LoadAlmanacConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(LoadAlmanacConfigRequest& a, LoadAlmanacConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(LoadAlmanacConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoadAlmanacConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LoadAlmanacConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LoadAlmanacConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LoadAlmanacConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LoadAlmanacConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoadAlmanacConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.LoadAlmanacConfigRequest";
  }
  protected:
  explicit LoadAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.LoadAlmanacConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class LoadAlmanacConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.LoadAlmanacConfigResponse) */ {
 public:
  inline LoadAlmanacConfigResponse() : LoadAlmanacConfigResponse(nullptr) {}
  ~LoadAlmanacConfigResponse() override;
  explicit constexpr LoadAlmanacConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LoadAlmanacConfigResponse(const LoadAlmanacConfigResponse& from);
  LoadAlmanacConfigResponse(LoadAlmanacConfigResponse&& from) noexcept
    : LoadAlmanacConfigResponse() {
    *this = ::std::move(from);
  }

  inline LoadAlmanacConfigResponse& operator=(const LoadAlmanacConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoadAlmanacConfigResponse& operator=(LoadAlmanacConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LoadAlmanacConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const LoadAlmanacConfigResponse* internal_default_instance() {
    return reinterpret_cast<const LoadAlmanacConfigResponse*>(
               &_LoadAlmanacConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(LoadAlmanacConfigResponse& a, LoadAlmanacConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(LoadAlmanacConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoadAlmanacConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LoadAlmanacConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LoadAlmanacConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LoadAlmanacConfigResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LoadAlmanacConfigResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoadAlmanacConfigResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.LoadAlmanacConfigResponse";
  }
  protected:
  explicit LoadAlmanacConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConfigFieldNumber = 1,
  };
  // .carbon.aimbot.almanac.AlmanacConfig config = 1;
  bool has_config() const;
  private:
  bool _internal_has_config() const;
  public:
  void clear_config();
  const ::carbon::aimbot::almanac::AlmanacConfig& config() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::AlmanacConfig* release_config();
  ::carbon::aimbot::almanac::AlmanacConfig* mutable_config();
  void set_allocated_config(::carbon::aimbot::almanac::AlmanacConfig* config);
  private:
  const ::carbon::aimbot::almanac::AlmanacConfig& _internal_config() const;
  ::carbon::aimbot::almanac::AlmanacConfig* _internal_mutable_config();
  public:
  void unsafe_arena_set_allocated_config(
      ::carbon::aimbot::almanac::AlmanacConfig* config);
  ::carbon::aimbot::almanac::AlmanacConfig* unsafe_arena_release_config();

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.LoadAlmanacConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::aimbot::almanac::AlmanacConfig* config_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class SaveAlmanacConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.SaveAlmanacConfigRequest) */ {
 public:
  inline SaveAlmanacConfigRequest() : SaveAlmanacConfigRequest(nullptr) {}
  ~SaveAlmanacConfigRequest() override;
  explicit constexpr SaveAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SaveAlmanacConfigRequest(const SaveAlmanacConfigRequest& from);
  SaveAlmanacConfigRequest(SaveAlmanacConfigRequest&& from) noexcept
    : SaveAlmanacConfigRequest() {
    *this = ::std::move(from);
  }

  inline SaveAlmanacConfigRequest& operator=(const SaveAlmanacConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveAlmanacConfigRequest& operator=(SaveAlmanacConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SaveAlmanacConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SaveAlmanacConfigRequest* internal_default_instance() {
    return reinterpret_cast<const SaveAlmanacConfigRequest*>(
               &_SaveAlmanacConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(SaveAlmanacConfigRequest& a, SaveAlmanacConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveAlmanacConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveAlmanacConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SaveAlmanacConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SaveAlmanacConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SaveAlmanacConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SaveAlmanacConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveAlmanacConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.SaveAlmanacConfigRequest";
  }
  protected:
  explicit SaveAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConfigFieldNumber = 1,
    kSetActiveFieldNumber = 2,
  };
  // .carbon.aimbot.almanac.AlmanacConfig config = 1;
  bool has_config() const;
  private:
  bool _internal_has_config() const;
  public:
  void clear_config();
  const ::carbon::aimbot::almanac::AlmanacConfig& config() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::AlmanacConfig* release_config();
  ::carbon::aimbot::almanac::AlmanacConfig* mutable_config();
  void set_allocated_config(::carbon::aimbot::almanac::AlmanacConfig* config);
  private:
  const ::carbon::aimbot::almanac::AlmanacConfig& _internal_config() const;
  ::carbon::aimbot::almanac::AlmanacConfig* _internal_mutable_config();
  public:
  void unsafe_arena_set_allocated_config(
      ::carbon::aimbot::almanac::AlmanacConfig* config);
  ::carbon::aimbot::almanac::AlmanacConfig* unsafe_arena_release_config();

  // bool set_active = 2;
  void clear_set_active();
  bool set_active() const;
  void set_set_active(bool value);
  private:
  bool _internal_set_active() const;
  void _internal_set_set_active(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SaveAlmanacConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::aimbot::almanac::AlmanacConfig* config_;
  bool set_active_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class SaveAlmanacConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.SaveAlmanacConfigResponse) */ {
 public:
  inline SaveAlmanacConfigResponse() : SaveAlmanacConfigResponse(nullptr) {}
  ~SaveAlmanacConfigResponse() override;
  explicit constexpr SaveAlmanacConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SaveAlmanacConfigResponse(const SaveAlmanacConfigResponse& from);
  SaveAlmanacConfigResponse(SaveAlmanacConfigResponse&& from) noexcept
    : SaveAlmanacConfigResponse() {
    *this = ::std::move(from);
  }

  inline SaveAlmanacConfigResponse& operator=(const SaveAlmanacConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveAlmanacConfigResponse& operator=(SaveAlmanacConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SaveAlmanacConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SaveAlmanacConfigResponse* internal_default_instance() {
    return reinterpret_cast<const SaveAlmanacConfigResponse*>(
               &_SaveAlmanacConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(SaveAlmanacConfigResponse& a, SaveAlmanacConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveAlmanacConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveAlmanacConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SaveAlmanacConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SaveAlmanacConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SaveAlmanacConfigResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SaveAlmanacConfigResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveAlmanacConfigResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.SaveAlmanacConfigResponse";
  }
  protected:
  explicit SaveAlmanacConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SaveAlmanacConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class SetActiveAlmanacConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.SetActiveAlmanacConfigRequest) */ {
 public:
  inline SetActiveAlmanacConfigRequest() : SetActiveAlmanacConfigRequest(nullptr) {}
  ~SetActiveAlmanacConfigRequest() override;
  explicit constexpr SetActiveAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetActiveAlmanacConfigRequest(const SetActiveAlmanacConfigRequest& from);
  SetActiveAlmanacConfigRequest(SetActiveAlmanacConfigRequest&& from) noexcept
    : SetActiveAlmanacConfigRequest() {
    *this = ::std::move(from);
  }

  inline SetActiveAlmanacConfigRequest& operator=(const SetActiveAlmanacConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetActiveAlmanacConfigRequest& operator=(SetActiveAlmanacConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetActiveAlmanacConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetActiveAlmanacConfigRequest* internal_default_instance() {
    return reinterpret_cast<const SetActiveAlmanacConfigRequest*>(
               &_SetActiveAlmanacConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(SetActiveAlmanacConfigRequest& a, SetActiveAlmanacConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetActiveAlmanacConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetActiveAlmanacConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetActiveAlmanacConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetActiveAlmanacConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetActiveAlmanacConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetActiveAlmanacConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetActiveAlmanacConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.SetActiveAlmanacConfigRequest";
  }
  protected:
  explicit SetActiveAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SetActiveAlmanacConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class DeleteAlmanacConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.DeleteAlmanacConfigRequest) */ {
 public:
  inline DeleteAlmanacConfigRequest() : DeleteAlmanacConfigRequest(nullptr) {}
  ~DeleteAlmanacConfigRequest() override;
  explicit constexpr DeleteAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteAlmanacConfigRequest(const DeleteAlmanacConfigRequest& from);
  DeleteAlmanacConfigRequest(DeleteAlmanacConfigRequest&& from) noexcept
    : DeleteAlmanacConfigRequest() {
    *this = ::std::move(from);
  }

  inline DeleteAlmanacConfigRequest& operator=(const DeleteAlmanacConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteAlmanacConfigRequest& operator=(DeleteAlmanacConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteAlmanacConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteAlmanacConfigRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteAlmanacConfigRequest*>(
               &_DeleteAlmanacConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(DeleteAlmanacConfigRequest& a, DeleteAlmanacConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteAlmanacConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteAlmanacConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteAlmanacConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteAlmanacConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeleteAlmanacConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeleteAlmanacConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteAlmanacConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.DeleteAlmanacConfigRequest";
  }
  protected:
  explicit DeleteAlmanacConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kNewActiveIdFieldNumber = 2,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string new_active_id = 2;
  void clear_new_active_id();
  const std::string& new_active_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_new_active_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_new_active_id();
  PROTOBUF_NODISCARD std::string* release_new_active_id();
  void set_allocated_new_active_id(std::string* new_active_id);
  private:
  const std::string& _internal_new_active_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_new_active_id(const std::string& value);
  std::string* _internal_mutable_new_active_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.DeleteAlmanacConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr new_active_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse();
  explicit constexpr GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse& other);
  static const GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse*>(&_GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetNextAlmanacConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.GetNextAlmanacConfigResponse) */ {
 public:
  inline GetNextAlmanacConfigResponse() : GetNextAlmanacConfigResponse(nullptr) {}
  ~GetNextAlmanacConfigResponse() override;
  explicit constexpr GetNextAlmanacConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextAlmanacConfigResponse(const GetNextAlmanacConfigResponse& from);
  GetNextAlmanacConfigResponse(GetNextAlmanacConfigResponse&& from) noexcept
    : GetNextAlmanacConfigResponse() {
    *this = ::std::move(from);
  }

  inline GetNextAlmanacConfigResponse& operator=(const GetNextAlmanacConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextAlmanacConfigResponse& operator=(GetNextAlmanacConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextAlmanacConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextAlmanacConfigResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextAlmanacConfigResponse*>(
               &_GetNextAlmanacConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GetNextAlmanacConfigResponse& a, GetNextAlmanacConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextAlmanacConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextAlmanacConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextAlmanacConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextAlmanacConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextAlmanacConfigResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextAlmanacConfigResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextAlmanacConfigResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.GetNextAlmanacConfigResponse";
  }
  protected:
  explicit GetNextAlmanacConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kAvailableFieldNumber = 3,
    kActiveFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // map<string, string> available = 3;
  int available_size() const;
  private:
  int _internal_available_size() const;
  public:
  void clear_available();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_available() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_available();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      available() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_available();

  // string active = 2;
  void clear_active();
  const std::string& active() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active();
  PROTOBUF_NODISCARD std::string* release_active();
  void set_allocated_active(std::string* active);
  private:
  const std::string& _internal_active() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active(const std::string& value);
  std::string* _internal_mutable_active();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextAlmanacConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetNextAlmanacConfigResponse_AvailableEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> available_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class LoadDiscriminatorConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.LoadDiscriminatorConfigRequest) */ {
 public:
  inline LoadDiscriminatorConfigRequest() : LoadDiscriminatorConfigRequest(nullptr) {}
  ~LoadDiscriminatorConfigRequest() override;
  explicit constexpr LoadDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LoadDiscriminatorConfigRequest(const LoadDiscriminatorConfigRequest& from);
  LoadDiscriminatorConfigRequest(LoadDiscriminatorConfigRequest&& from) noexcept
    : LoadDiscriminatorConfigRequest() {
    *this = ::std::move(from);
  }

  inline LoadDiscriminatorConfigRequest& operator=(const LoadDiscriminatorConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoadDiscriminatorConfigRequest& operator=(LoadDiscriminatorConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LoadDiscriminatorConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const LoadDiscriminatorConfigRequest* internal_default_instance() {
    return reinterpret_cast<const LoadDiscriminatorConfigRequest*>(
               &_LoadDiscriminatorConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(LoadDiscriminatorConfigRequest& a, LoadDiscriminatorConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(LoadDiscriminatorConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoadDiscriminatorConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LoadDiscriminatorConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LoadDiscriminatorConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LoadDiscriminatorConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LoadDiscriminatorConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoadDiscriminatorConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.LoadDiscriminatorConfigRequest";
  }
  protected:
  explicit LoadDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.LoadDiscriminatorConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class LoadDiscriminatorConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.LoadDiscriminatorConfigResponse) */ {
 public:
  inline LoadDiscriminatorConfigResponse() : LoadDiscriminatorConfigResponse(nullptr) {}
  ~LoadDiscriminatorConfigResponse() override;
  explicit constexpr LoadDiscriminatorConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LoadDiscriminatorConfigResponse(const LoadDiscriminatorConfigResponse& from);
  LoadDiscriminatorConfigResponse(LoadDiscriminatorConfigResponse&& from) noexcept
    : LoadDiscriminatorConfigResponse() {
    *this = ::std::move(from);
  }

  inline LoadDiscriminatorConfigResponse& operator=(const LoadDiscriminatorConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoadDiscriminatorConfigResponse& operator=(LoadDiscriminatorConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LoadDiscriminatorConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const LoadDiscriminatorConfigResponse* internal_default_instance() {
    return reinterpret_cast<const LoadDiscriminatorConfigResponse*>(
               &_LoadDiscriminatorConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(LoadDiscriminatorConfigResponse& a, LoadDiscriminatorConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(LoadDiscriminatorConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoadDiscriminatorConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LoadDiscriminatorConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LoadDiscriminatorConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LoadDiscriminatorConfigResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LoadDiscriminatorConfigResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoadDiscriminatorConfigResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.LoadDiscriminatorConfigResponse";
  }
  protected:
  explicit LoadDiscriminatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConfigFieldNumber = 1,
  };
  // .carbon.aimbot.almanac.DiscriminatorConfig config = 1;
  bool has_config() const;
  private:
  bool _internal_has_config() const;
  public:
  void clear_config();
  const ::carbon::aimbot::almanac::DiscriminatorConfig& config() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::DiscriminatorConfig* release_config();
  ::carbon::aimbot::almanac::DiscriminatorConfig* mutable_config();
  void set_allocated_config(::carbon::aimbot::almanac::DiscriminatorConfig* config);
  private:
  const ::carbon::aimbot::almanac::DiscriminatorConfig& _internal_config() const;
  ::carbon::aimbot::almanac::DiscriminatorConfig* _internal_mutable_config();
  public:
  void unsafe_arena_set_allocated_config(
      ::carbon::aimbot::almanac::DiscriminatorConfig* config);
  ::carbon::aimbot::almanac::DiscriminatorConfig* unsafe_arena_release_config();

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.LoadDiscriminatorConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::aimbot::almanac::DiscriminatorConfig* config_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class SaveDiscriminatorConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.SaveDiscriminatorConfigRequest) */ {
 public:
  inline SaveDiscriminatorConfigRequest() : SaveDiscriminatorConfigRequest(nullptr) {}
  ~SaveDiscriminatorConfigRequest() override;
  explicit constexpr SaveDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SaveDiscriminatorConfigRequest(const SaveDiscriminatorConfigRequest& from);
  SaveDiscriminatorConfigRequest(SaveDiscriminatorConfigRequest&& from) noexcept
    : SaveDiscriminatorConfigRequest() {
    *this = ::std::move(from);
  }

  inline SaveDiscriminatorConfigRequest& operator=(const SaveDiscriminatorConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveDiscriminatorConfigRequest& operator=(SaveDiscriminatorConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SaveDiscriminatorConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SaveDiscriminatorConfigRequest* internal_default_instance() {
    return reinterpret_cast<const SaveDiscriminatorConfigRequest*>(
               &_SaveDiscriminatorConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(SaveDiscriminatorConfigRequest& a, SaveDiscriminatorConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveDiscriminatorConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveDiscriminatorConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SaveDiscriminatorConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SaveDiscriminatorConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SaveDiscriminatorConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SaveDiscriminatorConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveDiscriminatorConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.SaveDiscriminatorConfigRequest";
  }
  protected:
  explicit SaveDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConfigFieldNumber = 1,
    kAssociateWithActiveCropFieldNumber = 2,
  };
  // .carbon.aimbot.almanac.DiscriminatorConfig config = 1;
  bool has_config() const;
  private:
  bool _internal_has_config() const;
  public:
  void clear_config();
  const ::carbon::aimbot::almanac::DiscriminatorConfig& config() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::DiscriminatorConfig* release_config();
  ::carbon::aimbot::almanac::DiscriminatorConfig* mutable_config();
  void set_allocated_config(::carbon::aimbot::almanac::DiscriminatorConfig* config);
  private:
  const ::carbon::aimbot::almanac::DiscriminatorConfig& _internal_config() const;
  ::carbon::aimbot::almanac::DiscriminatorConfig* _internal_mutable_config();
  public:
  void unsafe_arena_set_allocated_config(
      ::carbon::aimbot::almanac::DiscriminatorConfig* config);
  ::carbon::aimbot::almanac::DiscriminatorConfig* unsafe_arena_release_config();

  // bool associate_with_active_crop = 2;
  void clear_associate_with_active_crop();
  bool associate_with_active_crop() const;
  void set_associate_with_active_crop(bool value);
  private:
  bool _internal_associate_with_active_crop() const;
  void _internal_set_associate_with_active_crop(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SaveDiscriminatorConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::aimbot::almanac::DiscriminatorConfig* config_;
  bool associate_with_active_crop_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class SaveDiscriminatorConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.SaveDiscriminatorConfigResponse) */ {
 public:
  inline SaveDiscriminatorConfigResponse() : SaveDiscriminatorConfigResponse(nullptr) {}
  ~SaveDiscriminatorConfigResponse() override;
  explicit constexpr SaveDiscriminatorConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SaveDiscriminatorConfigResponse(const SaveDiscriminatorConfigResponse& from);
  SaveDiscriminatorConfigResponse(SaveDiscriminatorConfigResponse&& from) noexcept
    : SaveDiscriminatorConfigResponse() {
    *this = ::std::move(from);
  }

  inline SaveDiscriminatorConfigResponse& operator=(const SaveDiscriminatorConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveDiscriminatorConfigResponse& operator=(SaveDiscriminatorConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SaveDiscriminatorConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SaveDiscriminatorConfigResponse* internal_default_instance() {
    return reinterpret_cast<const SaveDiscriminatorConfigResponse*>(
               &_SaveDiscriminatorConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(SaveDiscriminatorConfigResponse& a, SaveDiscriminatorConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveDiscriminatorConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveDiscriminatorConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SaveDiscriminatorConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SaveDiscriminatorConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SaveDiscriminatorConfigResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SaveDiscriminatorConfigResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveDiscriminatorConfigResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.SaveDiscriminatorConfigResponse";
  }
  protected:
  explicit SaveDiscriminatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SaveDiscriminatorConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class SetActiveDiscriminatorConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest) */ {
 public:
  inline SetActiveDiscriminatorConfigRequest() : SetActiveDiscriminatorConfigRequest(nullptr) {}
  ~SetActiveDiscriminatorConfigRequest() override;
  explicit constexpr SetActiveDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetActiveDiscriminatorConfigRequest(const SetActiveDiscriminatorConfigRequest& from);
  SetActiveDiscriminatorConfigRequest(SetActiveDiscriminatorConfigRequest&& from) noexcept
    : SetActiveDiscriminatorConfigRequest() {
    *this = ::std::move(from);
  }

  inline SetActiveDiscriminatorConfigRequest& operator=(const SetActiveDiscriminatorConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetActiveDiscriminatorConfigRequest& operator=(SetActiveDiscriminatorConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetActiveDiscriminatorConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetActiveDiscriminatorConfigRequest* internal_default_instance() {
    return reinterpret_cast<const SetActiveDiscriminatorConfigRequest*>(
               &_SetActiveDiscriminatorConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(SetActiveDiscriminatorConfigRequest& a, SetActiveDiscriminatorConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetActiveDiscriminatorConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetActiveDiscriminatorConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetActiveDiscriminatorConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetActiveDiscriminatorConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetActiveDiscriminatorConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetActiveDiscriminatorConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetActiveDiscriminatorConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest";
  }
  protected:
  explicit SetActiveDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kCropIdFieldNumber = 2,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // optional string crop_id = 2;
  bool has_crop_id() const;
  private:
  bool _internal_has_crop_id() const;
  public:
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class DeleteDiscriminatorConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest) */ {
 public:
  inline DeleteDiscriminatorConfigRequest() : DeleteDiscriminatorConfigRequest(nullptr) {}
  ~DeleteDiscriminatorConfigRequest() override;
  explicit constexpr DeleteDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteDiscriminatorConfigRequest(const DeleteDiscriminatorConfigRequest& from);
  DeleteDiscriminatorConfigRequest(DeleteDiscriminatorConfigRequest&& from) noexcept
    : DeleteDiscriminatorConfigRequest() {
    *this = ::std::move(from);
  }

  inline DeleteDiscriminatorConfigRequest& operator=(const DeleteDiscriminatorConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteDiscriminatorConfigRequest& operator=(DeleteDiscriminatorConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteDiscriminatorConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteDiscriminatorConfigRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteDiscriminatorConfigRequest*>(
               &_DeleteDiscriminatorConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(DeleteDiscriminatorConfigRequest& a, DeleteDiscriminatorConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteDiscriminatorConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteDiscriminatorConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteDiscriminatorConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteDiscriminatorConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeleteDiscriminatorConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeleteDiscriminatorConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteDiscriminatorConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.DeleteDiscriminatorConfigRequest";
  }
  protected:
  explicit DeleteDiscriminatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse();
  explicit constexpr GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse& other);
  static const GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse*>(&_GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetNextDiscriminatorConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse) */ {
 public:
  inline GetNextDiscriminatorConfigResponse() : GetNextDiscriminatorConfigResponse(nullptr) {}
  ~GetNextDiscriminatorConfigResponse() override;
  explicit constexpr GetNextDiscriminatorConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextDiscriminatorConfigResponse(const GetNextDiscriminatorConfigResponse& from);
  GetNextDiscriminatorConfigResponse(GetNextDiscriminatorConfigResponse&& from) noexcept
    : GetNextDiscriminatorConfigResponse() {
    *this = ::std::move(from);
  }

  inline GetNextDiscriminatorConfigResponse& operator=(const GetNextDiscriminatorConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextDiscriminatorConfigResponse& operator=(GetNextDiscriminatorConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextDiscriminatorConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextDiscriminatorConfigResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextDiscriminatorConfigResponse*>(
               &_GetNextDiscriminatorConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(GetNextDiscriminatorConfigResponse& a, GetNextDiscriminatorConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextDiscriminatorConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextDiscriminatorConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextDiscriminatorConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextDiscriminatorConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextDiscriminatorConfigResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextDiscriminatorConfigResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextDiscriminatorConfigResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.GetNextDiscriminatorConfigResponse";
  }
  protected:
  explicit GetNextDiscriminatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kAvailableFieldNumber = 3,
    kActiveFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // map<string, string> available = 3;
  int available_size() const;
  private:
  int _internal_available_size() const;
  public:
  void clear_available();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_available() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_available();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      available() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_available();

  // string active = 2;
  void clear_active();
  const std::string& active() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active();
  PROTOBUF_NODISCARD std::string* release_active();
  void set_allocated_active(std::string* active);
  private:
  const std::string& _internal_active() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active(const std::string& value);
  std::string* _internal_mutable_active();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetNextDiscriminatorConfigResponse_AvailableEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> available_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class GetNextModelinatorConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.GetNextModelinatorConfigResponse) */ {
 public:
  inline GetNextModelinatorConfigResponse() : GetNextModelinatorConfigResponse(nullptr) {}
  ~GetNextModelinatorConfigResponse() override;
  explicit constexpr GetNextModelinatorConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextModelinatorConfigResponse(const GetNextModelinatorConfigResponse& from);
  GetNextModelinatorConfigResponse(GetNextModelinatorConfigResponse&& from) noexcept
    : GetNextModelinatorConfigResponse() {
    *this = ::std::move(from);
  }

  inline GetNextModelinatorConfigResponse& operator=(const GetNextModelinatorConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextModelinatorConfigResponse& operator=(GetNextModelinatorConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextModelinatorConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextModelinatorConfigResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextModelinatorConfigResponse*>(
               &_GetNextModelinatorConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(GetNextModelinatorConfigResponse& a, GetNextModelinatorConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextModelinatorConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextModelinatorConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextModelinatorConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextModelinatorConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextModelinatorConfigResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextModelinatorConfigResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextModelinatorConfigResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.GetNextModelinatorConfigResponse";
  }
  protected:
  explicit GetNextModelinatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kConfigFieldNumber = 2,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.aimbot.almanac.ModelinatorConfig config = 2;
  bool has_config() const;
  private:
  bool _internal_has_config() const;
  public:
  void clear_config();
  const ::carbon::aimbot::almanac::ModelinatorConfig& config() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::ModelinatorConfig* release_config();
  ::carbon::aimbot::almanac::ModelinatorConfig* mutable_config();
  void set_allocated_config(::carbon::aimbot::almanac::ModelinatorConfig* config);
  private:
  const ::carbon::aimbot::almanac::ModelinatorConfig& _internal_config() const;
  ::carbon::aimbot::almanac::ModelinatorConfig* _internal_mutable_config();
  public:
  void unsafe_arena_set_allocated_config(
      ::carbon::aimbot::almanac::ModelinatorConfig* config);
  ::carbon::aimbot::almanac::ModelinatorConfig* unsafe_arena_release_config();

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextModelinatorConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::aimbot::almanac::ModelinatorConfig* config_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class SaveModelinatorConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.SaveModelinatorConfigRequest) */ {
 public:
  inline SaveModelinatorConfigRequest() : SaveModelinatorConfigRequest(nullptr) {}
  ~SaveModelinatorConfigRequest() override;
  explicit constexpr SaveModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SaveModelinatorConfigRequest(const SaveModelinatorConfigRequest& from);
  SaveModelinatorConfigRequest(SaveModelinatorConfigRequest&& from) noexcept
    : SaveModelinatorConfigRequest() {
    *this = ::std::move(from);
  }

  inline SaveModelinatorConfigRequest& operator=(const SaveModelinatorConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveModelinatorConfigRequest& operator=(SaveModelinatorConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SaveModelinatorConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SaveModelinatorConfigRequest* internal_default_instance() {
    return reinterpret_cast<const SaveModelinatorConfigRequest*>(
               &_SaveModelinatorConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(SaveModelinatorConfigRequest& a, SaveModelinatorConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveModelinatorConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveModelinatorConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SaveModelinatorConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SaveModelinatorConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SaveModelinatorConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SaveModelinatorConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveModelinatorConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.SaveModelinatorConfigRequest";
  }
  protected:
  explicit SaveModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConfigFieldNumber = 1,
  };
  // .carbon.aimbot.almanac.ModelinatorConfig config = 1;
  bool has_config() const;
  private:
  bool _internal_has_config() const;
  public:
  void clear_config();
  const ::carbon::aimbot::almanac::ModelinatorConfig& config() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::ModelinatorConfig* release_config();
  ::carbon::aimbot::almanac::ModelinatorConfig* mutable_config();
  void set_allocated_config(::carbon::aimbot::almanac::ModelinatorConfig* config);
  private:
  const ::carbon::aimbot::almanac::ModelinatorConfig& _internal_config() const;
  ::carbon::aimbot::almanac::ModelinatorConfig* _internal_mutable_config();
  public:
  void unsafe_arena_set_allocated_config(
      ::carbon::aimbot::almanac::ModelinatorConfig* config);
  ::carbon::aimbot::almanac::ModelinatorConfig* unsafe_arena_release_config();

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SaveModelinatorConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::aimbot::almanac::ModelinatorConfig* config_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class FetchModelinatorConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.FetchModelinatorConfigRequest) */ {
 public:
  inline FetchModelinatorConfigRequest() : FetchModelinatorConfigRequest(nullptr) {}
  ~FetchModelinatorConfigRequest() override;
  explicit constexpr FetchModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FetchModelinatorConfigRequest(const FetchModelinatorConfigRequest& from);
  FetchModelinatorConfigRequest(FetchModelinatorConfigRequest&& from) noexcept
    : FetchModelinatorConfigRequest() {
    *this = ::std::move(from);
  }

  inline FetchModelinatorConfigRequest& operator=(const FetchModelinatorConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline FetchModelinatorConfigRequest& operator=(FetchModelinatorConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FetchModelinatorConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const FetchModelinatorConfigRequest* internal_default_instance() {
    return reinterpret_cast<const FetchModelinatorConfigRequest*>(
               &_FetchModelinatorConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(FetchModelinatorConfigRequest& a, FetchModelinatorConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(FetchModelinatorConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FetchModelinatorConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FetchModelinatorConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FetchModelinatorConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FetchModelinatorConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FetchModelinatorConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FetchModelinatorConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.FetchModelinatorConfigRequest";
  }
  protected:
  explicit FetchModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelIdFieldNumber = 1,
    kCropIdFieldNumber = 2,
  };
  // string model_id = 1;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_NODISCARD std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // string crop_id = 2;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.FetchModelinatorConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class FetchModelinatorConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.FetchModelinatorConfigResponse) */ {
 public:
  inline FetchModelinatorConfigResponse() : FetchModelinatorConfigResponse(nullptr) {}
  ~FetchModelinatorConfigResponse() override;
  explicit constexpr FetchModelinatorConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FetchModelinatorConfigResponse(const FetchModelinatorConfigResponse& from);
  FetchModelinatorConfigResponse(FetchModelinatorConfigResponse&& from) noexcept
    : FetchModelinatorConfigResponse() {
    *this = ::std::move(from);
  }

  inline FetchModelinatorConfigResponse& operator=(const FetchModelinatorConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline FetchModelinatorConfigResponse& operator=(FetchModelinatorConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FetchModelinatorConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const FetchModelinatorConfigResponse* internal_default_instance() {
    return reinterpret_cast<const FetchModelinatorConfigResponse*>(
               &_FetchModelinatorConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(FetchModelinatorConfigResponse& a, FetchModelinatorConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(FetchModelinatorConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FetchModelinatorConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FetchModelinatorConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FetchModelinatorConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FetchModelinatorConfigResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FetchModelinatorConfigResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FetchModelinatorConfigResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.FetchModelinatorConfigResponse";
  }
  protected:
  explicit FetchModelinatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConfigFieldNumber = 1,
  };
  // .carbon.aimbot.almanac.ModelinatorConfig config = 1;
  bool has_config() const;
  private:
  bool _internal_has_config() const;
  public:
  void clear_config();
  const ::carbon::aimbot::almanac::ModelinatorConfig& config() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::ModelinatorConfig* release_config();
  ::carbon::aimbot::almanac::ModelinatorConfig* mutable_config();
  void set_allocated_config(::carbon::aimbot::almanac::ModelinatorConfig* config);
  private:
  const ::carbon::aimbot::almanac::ModelinatorConfig& _internal_config() const;
  ::carbon::aimbot::almanac::ModelinatorConfig* _internal_mutable_config();
  public:
  void unsafe_arena_set_allocated_config(
      ::carbon::aimbot::almanac::ModelinatorConfig* config);
  ::carbon::aimbot::almanac::ModelinatorConfig* unsafe_arena_release_config();

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.FetchModelinatorConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::aimbot::almanac::ModelinatorConfig* config_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class ResetModelinatorConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.ResetModelinatorConfigRequest) */ {
 public:
  inline ResetModelinatorConfigRequest() : ResetModelinatorConfigRequest(nullptr) {}
  explicit constexpr ResetModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ResetModelinatorConfigRequest(const ResetModelinatorConfigRequest& from);
  ResetModelinatorConfigRequest(ResetModelinatorConfigRequest&& from) noexcept
    : ResetModelinatorConfigRequest() {
    *this = ::std::move(from);
  }

  inline ResetModelinatorConfigRequest& operator=(const ResetModelinatorConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResetModelinatorConfigRequest& operator=(ResetModelinatorConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ResetModelinatorConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ResetModelinatorConfigRequest* internal_default_instance() {
    return reinterpret_cast<const ResetModelinatorConfigRequest*>(
               &_ResetModelinatorConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(ResetModelinatorConfigRequest& a, ResetModelinatorConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ResetModelinatorConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResetModelinatorConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ResetModelinatorConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ResetModelinatorConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const ResetModelinatorConfigRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const ResetModelinatorConfigRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.ResetModelinatorConfigRequest";
  }
  protected:
  explicit ResetModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.ResetModelinatorConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class GetNextConfigDataRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.GetNextConfigDataRequest) */ {
 public:
  inline GetNextConfigDataRequest() : GetNextConfigDataRequest(nullptr) {}
  ~GetNextConfigDataRequest() override;
  explicit constexpr GetNextConfigDataRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextConfigDataRequest(const GetNextConfigDataRequest& from);
  GetNextConfigDataRequest(GetNextConfigDataRequest&& from) noexcept
    : GetNextConfigDataRequest() {
    *this = ::std::move(from);
  }

  inline GetNextConfigDataRequest& operator=(const GetNextConfigDataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextConfigDataRequest& operator=(GetNextConfigDataRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextConfigDataRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextConfigDataRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextConfigDataRequest*>(
               &_GetNextConfigDataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(GetNextConfigDataRequest& a, GetNextConfigDataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextConfigDataRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextConfigDataRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextConfigDataRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextConfigDataRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextConfigDataRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextConfigDataRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextConfigDataRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.GetNextConfigDataRequest";
  }
  protected:
  explicit GetNextConfigDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLangFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // string lang = 2;
  void clear_lang();
  const std::string& lang() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_lang(ArgT0&& arg0, ArgT... args);
  std::string* mutable_lang();
  PROTOBUF_NODISCARD std::string* release_lang();
  void set_allocated_lang(std::string* lang);
  private:
  const std::string& _internal_lang() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_lang(const std::string& value);
  std::string* _internal_mutable_lang();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextConfigDataRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr lang_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// -------------------------------------------------------------------

class GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse();
  explicit constexpr GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse& other);
  static const GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse*>(&_GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse();
  explicit constexpr GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse& other);
  static const GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse*>(&_GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetNextConfigDataResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.almanac.GetNextConfigDataResponse) */ {
 public:
  inline GetNextConfigDataResponse() : GetNextConfigDataResponse(nullptr) {}
  ~GetNextConfigDataResponse() override;
  explicit constexpr GetNextConfigDataResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextConfigDataResponse(const GetNextConfigDataResponse& from);
  GetNextConfigDataResponse(GetNextConfigDataResponse&& from) noexcept
    : GetNextConfigDataResponse() {
    *this = ::std::move(from);
  }

  inline GetNextConfigDataResponse& operator=(const GetNextConfigDataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextConfigDataResponse& operator=(GetNextConfigDataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextConfigDataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextConfigDataResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextConfigDataResponse*>(
               &_GetNextConfigDataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  friend void swap(GetNextConfigDataResponse& a, GetNextConfigDataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextConfigDataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextConfigDataResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextConfigDataResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextConfigDataResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextConfigDataResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextConfigDataResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextConfigDataResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.almanac.GetNextConfigDataResponse";
  }
  protected:
  explicit GetNextConfigDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kCropCategoryNamesFieldNumber = 3,
    kWeedCategoryNamesFieldNumber = 4,
    kTsFieldNumber = 1,
    kNumSizeCategoriesFieldNumber = 2,
  };
  // map<string, string> crop_category_names = 3;
  int crop_category_names_size() const;
  private:
  int _internal_crop_category_names_size() const;
  public:
  void clear_crop_category_names();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_crop_category_names() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_crop_category_names();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      crop_category_names() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_crop_category_names();

  // map<string, string> weed_category_names = 4;
  int weed_category_names_size() const;
  private:
  int _internal_weed_category_names_size() const;
  public:
  void clear_weed_category_names();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_weed_category_names() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_weed_category_names();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      weed_category_names() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_weed_category_names();

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // uint32 num_size_categories = 2;
  void clear_num_size_categories();
  uint32_t num_size_categories() const;
  void set_num_size_categories(uint32_t value);
  private:
  uint32_t _internal_num_size_categories() const;
  void _internal_set_num_size_categories(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextConfigDataResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetNextConfigDataResponse_CropCategoryNamesEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> crop_category_names_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetNextConfigDataResponse_WeedCategoryNamesEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> weed_category_names_;
  ::carbon::frontend::util::Timestamp* ts_;
  uint32_t num_size_categories_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falmanac_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GetConfigDataResponse

// uint32 num_size_categories = 1;
inline void GetConfigDataResponse::clear_num_size_categories() {
  num_size_categories_ = 0u;
}
inline uint32_t GetConfigDataResponse::_internal_num_size_categories() const {
  return num_size_categories_;
}
inline uint32_t GetConfigDataResponse::num_size_categories() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.GetConfigDataResponse.num_size_categories)
  return _internal_num_size_categories();
}
inline void GetConfigDataResponse::_internal_set_num_size_categories(uint32_t value) {
  
  num_size_categories_ = value;
}
inline void GetConfigDataResponse::set_num_size_categories(uint32_t value) {
  _internal_set_num_size_categories(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.GetConfigDataResponse.num_size_categories)
}

// map<string, string> crop_category_names = 2;
inline int GetConfigDataResponse::_internal_crop_category_names_size() const {
  return crop_category_names_.size();
}
inline int GetConfigDataResponse::crop_category_names_size() const {
  return _internal_crop_category_names_size();
}
inline void GetConfigDataResponse::clear_crop_category_names() {
  crop_category_names_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetConfigDataResponse::_internal_crop_category_names() const {
  return crop_category_names_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetConfigDataResponse::crop_category_names() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.almanac.GetConfigDataResponse.crop_category_names)
  return _internal_crop_category_names();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetConfigDataResponse::_internal_mutable_crop_category_names() {
  return crop_category_names_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetConfigDataResponse::mutable_crop_category_names() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.almanac.GetConfigDataResponse.crop_category_names)
  return _internal_mutable_crop_category_names();
}

// map<string, string> weed_category_names = 3;
inline int GetConfigDataResponse::_internal_weed_category_names_size() const {
  return weed_category_names_.size();
}
inline int GetConfigDataResponse::weed_category_names_size() const {
  return _internal_weed_category_names_size();
}
inline void GetConfigDataResponse::clear_weed_category_names() {
  weed_category_names_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetConfigDataResponse::_internal_weed_category_names() const {
  return weed_category_names_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetConfigDataResponse::weed_category_names() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.almanac.GetConfigDataResponse.weed_category_names)
  return _internal_weed_category_names();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetConfigDataResponse::_internal_mutable_weed_category_names() {
  return weed_category_names_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetConfigDataResponse::mutable_weed_category_names() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.almanac.GetConfigDataResponse.weed_category_names)
  return _internal_mutable_weed_category_names();
}

// -------------------------------------------------------------------

// LoadAlmanacConfigRequest

// string id = 1;
inline void LoadAlmanacConfigRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& LoadAlmanacConfigRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.LoadAlmanacConfigRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LoadAlmanacConfigRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.LoadAlmanacConfigRequest.id)
}
inline std::string* LoadAlmanacConfigRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.LoadAlmanacConfigRequest.id)
  return _s;
}
inline const std::string& LoadAlmanacConfigRequest::_internal_id() const {
  return id_.Get();
}
inline void LoadAlmanacConfigRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* LoadAlmanacConfigRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* LoadAlmanacConfigRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.LoadAlmanacConfigRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void LoadAlmanacConfigRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.LoadAlmanacConfigRequest.id)
}

// -------------------------------------------------------------------

// LoadAlmanacConfigResponse

// .carbon.aimbot.almanac.AlmanacConfig config = 1;
inline bool LoadAlmanacConfigResponse::_internal_has_config() const {
  return this != internal_default_instance() && config_ != nullptr;
}
inline bool LoadAlmanacConfigResponse::has_config() const {
  return _internal_has_config();
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& LoadAlmanacConfigResponse::_internal_config() const {
  const ::carbon::aimbot::almanac::AlmanacConfig* p = config_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::AlmanacConfig&>(
      ::carbon::aimbot::almanac::_AlmanacConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& LoadAlmanacConfigResponse::config() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.LoadAlmanacConfigResponse.config)
  return _internal_config();
}
inline void LoadAlmanacConfigResponse::unsafe_arena_set_allocated_config(
    ::carbon::aimbot::almanac::AlmanacConfig* config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  config_ = config;
  if (config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.LoadAlmanacConfigResponse.config)
}
inline ::carbon::aimbot::almanac::AlmanacConfig* LoadAlmanacConfigResponse::release_config() {
  
  ::carbon::aimbot::almanac::AlmanacConfig* temp = config_;
  config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* LoadAlmanacConfigResponse::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.LoadAlmanacConfigResponse.config)
  
  ::carbon::aimbot::almanac::AlmanacConfig* temp = config_;
  config_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* LoadAlmanacConfigResponse::_internal_mutable_config() {
  
  if (config_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::AlmanacConfig>(GetArenaForAllocation());
    config_ = p;
  }
  return config_;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* LoadAlmanacConfigResponse::mutable_config() {
  ::carbon::aimbot::almanac::AlmanacConfig* _msg = _internal_mutable_config();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.LoadAlmanacConfigResponse.config)
  return _msg;
}
inline void LoadAlmanacConfigResponse::set_allocated_config(::carbon::aimbot::almanac::AlmanacConfig* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config));
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    
  } else {
    
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.LoadAlmanacConfigResponse.config)
}

// -------------------------------------------------------------------

// SaveAlmanacConfigRequest

// .carbon.aimbot.almanac.AlmanacConfig config = 1;
inline bool SaveAlmanacConfigRequest::_internal_has_config() const {
  return this != internal_default_instance() && config_ != nullptr;
}
inline bool SaveAlmanacConfigRequest::has_config() const {
  return _internal_has_config();
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& SaveAlmanacConfigRequest::_internal_config() const {
  const ::carbon::aimbot::almanac::AlmanacConfig* p = config_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::AlmanacConfig&>(
      ::carbon::aimbot::almanac::_AlmanacConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& SaveAlmanacConfigRequest::config() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.SaveAlmanacConfigRequest.config)
  return _internal_config();
}
inline void SaveAlmanacConfigRequest::unsafe_arena_set_allocated_config(
    ::carbon::aimbot::almanac::AlmanacConfig* config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  config_ = config;
  if (config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.SaveAlmanacConfigRequest.config)
}
inline ::carbon::aimbot::almanac::AlmanacConfig* SaveAlmanacConfigRequest::release_config() {
  
  ::carbon::aimbot::almanac::AlmanacConfig* temp = config_;
  config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* SaveAlmanacConfigRequest::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.SaveAlmanacConfigRequest.config)
  
  ::carbon::aimbot::almanac::AlmanacConfig* temp = config_;
  config_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* SaveAlmanacConfigRequest::_internal_mutable_config() {
  
  if (config_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::AlmanacConfig>(GetArenaForAllocation());
    config_ = p;
  }
  return config_;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* SaveAlmanacConfigRequest::mutable_config() {
  ::carbon::aimbot::almanac::AlmanacConfig* _msg = _internal_mutable_config();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.SaveAlmanacConfigRequest.config)
  return _msg;
}
inline void SaveAlmanacConfigRequest::set_allocated_config(::carbon::aimbot::almanac::AlmanacConfig* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config));
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    
  } else {
    
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.SaveAlmanacConfigRequest.config)
}

// bool set_active = 2;
inline void SaveAlmanacConfigRequest::clear_set_active() {
  set_active_ = false;
}
inline bool SaveAlmanacConfigRequest::_internal_set_active() const {
  return set_active_;
}
inline bool SaveAlmanacConfigRequest::set_active() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.SaveAlmanacConfigRequest.set_active)
  return _internal_set_active();
}
inline void SaveAlmanacConfigRequest::_internal_set_set_active(bool value) {
  
  set_active_ = value;
}
inline void SaveAlmanacConfigRequest::set_set_active(bool value) {
  _internal_set_set_active(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.SaveAlmanacConfigRequest.set_active)
}

// -------------------------------------------------------------------

// SaveAlmanacConfigResponse

// string id = 1;
inline void SaveAlmanacConfigResponse::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& SaveAlmanacConfigResponse::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.SaveAlmanacConfigResponse.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SaveAlmanacConfigResponse::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.SaveAlmanacConfigResponse.id)
}
inline std::string* SaveAlmanacConfigResponse::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.SaveAlmanacConfigResponse.id)
  return _s;
}
inline const std::string& SaveAlmanacConfigResponse::_internal_id() const {
  return id_.Get();
}
inline void SaveAlmanacConfigResponse::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SaveAlmanacConfigResponse::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SaveAlmanacConfigResponse::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.SaveAlmanacConfigResponse.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SaveAlmanacConfigResponse::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.SaveAlmanacConfigResponse.id)
}

// -------------------------------------------------------------------

// SetActiveAlmanacConfigRequest

// string id = 1;
inline void SetActiveAlmanacConfigRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& SetActiveAlmanacConfigRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.SetActiveAlmanacConfigRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetActiveAlmanacConfigRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.SetActiveAlmanacConfigRequest.id)
}
inline std::string* SetActiveAlmanacConfigRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.SetActiveAlmanacConfigRequest.id)
  return _s;
}
inline const std::string& SetActiveAlmanacConfigRequest::_internal_id() const {
  return id_.Get();
}
inline void SetActiveAlmanacConfigRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetActiveAlmanacConfigRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetActiveAlmanacConfigRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.SetActiveAlmanacConfigRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetActiveAlmanacConfigRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.SetActiveAlmanacConfigRequest.id)
}

// -------------------------------------------------------------------

// DeleteAlmanacConfigRequest

// string id = 1;
inline void DeleteAlmanacConfigRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& DeleteAlmanacConfigRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.DeleteAlmanacConfigRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteAlmanacConfigRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.DeleteAlmanacConfigRequest.id)
}
inline std::string* DeleteAlmanacConfigRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.DeleteAlmanacConfigRequest.id)
  return _s;
}
inline const std::string& DeleteAlmanacConfigRequest::_internal_id() const {
  return id_.Get();
}
inline void DeleteAlmanacConfigRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteAlmanacConfigRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteAlmanacConfigRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.DeleteAlmanacConfigRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteAlmanacConfigRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.DeleteAlmanacConfigRequest.id)
}

// string new_active_id = 2;
inline void DeleteAlmanacConfigRequest::clear_new_active_id() {
  new_active_id_.ClearToEmpty();
}
inline const std::string& DeleteAlmanacConfigRequest::new_active_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.DeleteAlmanacConfigRequest.new_active_id)
  return _internal_new_active_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteAlmanacConfigRequest::set_new_active_id(ArgT0&& arg0, ArgT... args) {
 
 new_active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.DeleteAlmanacConfigRequest.new_active_id)
}
inline std::string* DeleteAlmanacConfigRequest::mutable_new_active_id() {
  std::string* _s = _internal_mutable_new_active_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.DeleteAlmanacConfigRequest.new_active_id)
  return _s;
}
inline const std::string& DeleteAlmanacConfigRequest::_internal_new_active_id() const {
  return new_active_id_.Get();
}
inline void DeleteAlmanacConfigRequest::_internal_set_new_active_id(const std::string& value) {
  
  new_active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteAlmanacConfigRequest::_internal_mutable_new_active_id() {
  
  return new_active_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteAlmanacConfigRequest::release_new_active_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.DeleteAlmanacConfigRequest.new_active_id)
  return new_active_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteAlmanacConfigRequest::set_allocated_new_active_id(std::string* new_active_id) {
  if (new_active_id != nullptr) {
    
  } else {
    
  }
  new_active_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), new_active_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (new_active_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    new_active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.DeleteAlmanacConfigRequest.new_active_id)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GetNextAlmanacConfigResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextAlmanacConfigResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextAlmanacConfigResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextAlmanacConfigResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextAlmanacConfigResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.GetNextAlmanacConfigResponse.ts)
  return _internal_ts();
}
inline void GetNextAlmanacConfigResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.GetNextAlmanacConfigResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextAlmanacConfigResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlmanacConfigResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.GetNextAlmanacConfigResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlmanacConfigResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlmanacConfigResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.GetNextAlmanacConfigResponse.ts)
  return _msg;
}
inline void GetNextAlmanacConfigResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.GetNextAlmanacConfigResponse.ts)
}

// string active = 2;
inline void GetNextAlmanacConfigResponse::clear_active() {
  active_.ClearToEmpty();
}
inline const std::string& GetNextAlmanacConfigResponse::active() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.GetNextAlmanacConfigResponse.active)
  return _internal_active();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextAlmanacConfigResponse::set_active(ArgT0&& arg0, ArgT... args) {
 
 active_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.GetNextAlmanacConfigResponse.active)
}
inline std::string* GetNextAlmanacConfigResponse::mutable_active() {
  std::string* _s = _internal_mutable_active();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.GetNextAlmanacConfigResponse.active)
  return _s;
}
inline const std::string& GetNextAlmanacConfigResponse::_internal_active() const {
  return active_.Get();
}
inline void GetNextAlmanacConfigResponse::_internal_set_active(const std::string& value) {
  
  active_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextAlmanacConfigResponse::_internal_mutable_active() {
  
  return active_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextAlmanacConfigResponse::release_active() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.GetNextAlmanacConfigResponse.active)
  return active_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextAlmanacConfigResponse::set_allocated_active(std::string* active) {
  if (active != nullptr) {
    
  } else {
    
  }
  active_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.GetNextAlmanacConfigResponse.active)
}

// map<string, string> available = 3;
inline int GetNextAlmanacConfigResponse::_internal_available_size() const {
  return available_.size();
}
inline int GetNextAlmanacConfigResponse::available_size() const {
  return _internal_available_size();
}
inline void GetNextAlmanacConfigResponse::clear_available() {
  available_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetNextAlmanacConfigResponse::_internal_available() const {
  return available_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetNextAlmanacConfigResponse::available() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.almanac.GetNextAlmanacConfigResponse.available)
  return _internal_available();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetNextAlmanacConfigResponse::_internal_mutable_available() {
  return available_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetNextAlmanacConfigResponse::mutable_available() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.almanac.GetNextAlmanacConfigResponse.available)
  return _internal_mutable_available();
}

// -------------------------------------------------------------------

// LoadDiscriminatorConfigRequest

// string id = 1;
inline void LoadDiscriminatorConfigRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& LoadDiscriminatorConfigRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.LoadDiscriminatorConfigRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LoadDiscriminatorConfigRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.LoadDiscriminatorConfigRequest.id)
}
inline std::string* LoadDiscriminatorConfigRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.LoadDiscriminatorConfigRequest.id)
  return _s;
}
inline const std::string& LoadDiscriminatorConfigRequest::_internal_id() const {
  return id_.Get();
}
inline void LoadDiscriminatorConfigRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* LoadDiscriminatorConfigRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* LoadDiscriminatorConfigRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.LoadDiscriminatorConfigRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void LoadDiscriminatorConfigRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.LoadDiscriminatorConfigRequest.id)
}

// -------------------------------------------------------------------

// LoadDiscriminatorConfigResponse

// .carbon.aimbot.almanac.DiscriminatorConfig config = 1;
inline bool LoadDiscriminatorConfigResponse::_internal_has_config() const {
  return this != internal_default_instance() && config_ != nullptr;
}
inline bool LoadDiscriminatorConfigResponse::has_config() const {
  return _internal_has_config();
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& LoadDiscriminatorConfigResponse::_internal_config() const {
  const ::carbon::aimbot::almanac::DiscriminatorConfig* p = config_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::DiscriminatorConfig&>(
      ::carbon::aimbot::almanac::_DiscriminatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& LoadDiscriminatorConfigResponse::config() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.LoadDiscriminatorConfigResponse.config)
  return _internal_config();
}
inline void LoadDiscriminatorConfigResponse::unsafe_arena_set_allocated_config(
    ::carbon::aimbot::almanac::DiscriminatorConfig* config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  config_ = config;
  if (config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.LoadDiscriminatorConfigResponse.config)
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* LoadDiscriminatorConfigResponse::release_config() {
  
  ::carbon::aimbot::almanac::DiscriminatorConfig* temp = config_;
  config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* LoadDiscriminatorConfigResponse::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.LoadDiscriminatorConfigResponse.config)
  
  ::carbon::aimbot::almanac::DiscriminatorConfig* temp = config_;
  config_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* LoadDiscriminatorConfigResponse::_internal_mutable_config() {
  
  if (config_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::DiscriminatorConfig>(GetArenaForAllocation());
    config_ = p;
  }
  return config_;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* LoadDiscriminatorConfigResponse::mutable_config() {
  ::carbon::aimbot::almanac::DiscriminatorConfig* _msg = _internal_mutable_config();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.LoadDiscriminatorConfigResponse.config)
  return _msg;
}
inline void LoadDiscriminatorConfigResponse::set_allocated_config(::carbon::aimbot::almanac::DiscriminatorConfig* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config));
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    
  } else {
    
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.LoadDiscriminatorConfigResponse.config)
}

// -------------------------------------------------------------------

// SaveDiscriminatorConfigRequest

// .carbon.aimbot.almanac.DiscriminatorConfig config = 1;
inline bool SaveDiscriminatorConfigRequest::_internal_has_config() const {
  return this != internal_default_instance() && config_ != nullptr;
}
inline bool SaveDiscriminatorConfigRequest::has_config() const {
  return _internal_has_config();
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& SaveDiscriminatorConfigRequest::_internal_config() const {
  const ::carbon::aimbot::almanac::DiscriminatorConfig* p = config_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::DiscriminatorConfig&>(
      ::carbon::aimbot::almanac::_DiscriminatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& SaveDiscriminatorConfigRequest::config() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.SaveDiscriminatorConfigRequest.config)
  return _internal_config();
}
inline void SaveDiscriminatorConfigRequest::unsafe_arena_set_allocated_config(
    ::carbon::aimbot::almanac::DiscriminatorConfig* config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  config_ = config;
  if (config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.SaveDiscriminatorConfigRequest.config)
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* SaveDiscriminatorConfigRequest::release_config() {
  
  ::carbon::aimbot::almanac::DiscriminatorConfig* temp = config_;
  config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* SaveDiscriminatorConfigRequest::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.SaveDiscriminatorConfigRequest.config)
  
  ::carbon::aimbot::almanac::DiscriminatorConfig* temp = config_;
  config_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* SaveDiscriminatorConfigRequest::_internal_mutable_config() {
  
  if (config_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::DiscriminatorConfig>(GetArenaForAllocation());
    config_ = p;
  }
  return config_;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* SaveDiscriminatorConfigRequest::mutable_config() {
  ::carbon::aimbot::almanac::DiscriminatorConfig* _msg = _internal_mutable_config();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.SaveDiscriminatorConfigRequest.config)
  return _msg;
}
inline void SaveDiscriminatorConfigRequest::set_allocated_config(::carbon::aimbot::almanac::DiscriminatorConfig* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config));
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    
  } else {
    
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.SaveDiscriminatorConfigRequest.config)
}

// bool associate_with_active_crop = 2;
inline void SaveDiscriminatorConfigRequest::clear_associate_with_active_crop() {
  associate_with_active_crop_ = false;
}
inline bool SaveDiscriminatorConfigRequest::_internal_associate_with_active_crop() const {
  return associate_with_active_crop_;
}
inline bool SaveDiscriminatorConfigRequest::associate_with_active_crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.SaveDiscriminatorConfigRequest.associate_with_active_crop)
  return _internal_associate_with_active_crop();
}
inline void SaveDiscriminatorConfigRequest::_internal_set_associate_with_active_crop(bool value) {
  
  associate_with_active_crop_ = value;
}
inline void SaveDiscriminatorConfigRequest::set_associate_with_active_crop(bool value) {
  _internal_set_associate_with_active_crop(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.SaveDiscriminatorConfigRequest.associate_with_active_crop)
}

// -------------------------------------------------------------------

// SaveDiscriminatorConfigResponse

// string id = 1;
inline void SaveDiscriminatorConfigResponse::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& SaveDiscriminatorConfigResponse::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.SaveDiscriminatorConfigResponse.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SaveDiscriminatorConfigResponse::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.SaveDiscriminatorConfigResponse.id)
}
inline std::string* SaveDiscriminatorConfigResponse::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.SaveDiscriminatorConfigResponse.id)
  return _s;
}
inline const std::string& SaveDiscriminatorConfigResponse::_internal_id() const {
  return id_.Get();
}
inline void SaveDiscriminatorConfigResponse::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SaveDiscriminatorConfigResponse::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SaveDiscriminatorConfigResponse::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.SaveDiscriminatorConfigResponse.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SaveDiscriminatorConfigResponse::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.SaveDiscriminatorConfigResponse.id)
}

// -------------------------------------------------------------------

// SetActiveDiscriminatorConfigRequest

// string id = 1;
inline void SetActiveDiscriminatorConfigRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& SetActiveDiscriminatorConfigRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetActiveDiscriminatorConfigRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.id)
}
inline std::string* SetActiveDiscriminatorConfigRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.id)
  return _s;
}
inline const std::string& SetActiveDiscriminatorConfigRequest::_internal_id() const {
  return id_.Get();
}
inline void SetActiveDiscriminatorConfigRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetActiveDiscriminatorConfigRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetActiveDiscriminatorConfigRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetActiveDiscriminatorConfigRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.id)
}

// optional string crop_id = 2;
inline bool SetActiveDiscriminatorConfigRequest::_internal_has_crop_id() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool SetActiveDiscriminatorConfigRequest::has_crop_id() const {
  return _internal_has_crop_id();
}
inline void SetActiveDiscriminatorConfigRequest::clear_crop_id() {
  crop_id_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& SetActiveDiscriminatorConfigRequest::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetActiveDiscriminatorConfigRequest::set_crop_id(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.crop_id)
}
inline std::string* SetActiveDiscriminatorConfigRequest::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.crop_id)
  return _s;
}
inline const std::string& SetActiveDiscriminatorConfigRequest::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void SetActiveDiscriminatorConfigRequest::_internal_set_crop_id(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetActiveDiscriminatorConfigRequest::_internal_mutable_crop_id() {
  _has_bits_[0] |= 0x00000001u;
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetActiveDiscriminatorConfigRequest::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.crop_id)
  if (!_internal_has_crop_id()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = crop_id_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void SetActiveDiscriminatorConfigRequest::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.crop_id)
}

// -------------------------------------------------------------------

// DeleteDiscriminatorConfigRequest

// string id = 1;
inline void DeleteDiscriminatorConfigRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& DeleteDiscriminatorConfigRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteDiscriminatorConfigRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest.id)
}
inline std::string* DeleteDiscriminatorConfigRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest.id)
  return _s;
}
inline const std::string& DeleteDiscriminatorConfigRequest::_internal_id() const {
  return id_.Get();
}
inline void DeleteDiscriminatorConfigRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteDiscriminatorConfigRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteDiscriminatorConfigRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteDiscriminatorConfigRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest.id)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GetNextDiscriminatorConfigResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextDiscriminatorConfigResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextDiscriminatorConfigResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextDiscriminatorConfigResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextDiscriminatorConfigResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.ts)
  return _internal_ts();
}
inline void GetNextDiscriminatorConfigResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextDiscriminatorConfigResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextDiscriminatorConfigResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextDiscriminatorConfigResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextDiscriminatorConfigResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.ts)
  return _msg;
}
inline void GetNextDiscriminatorConfigResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.ts)
}

// string active = 2;
inline void GetNextDiscriminatorConfigResponse::clear_active() {
  active_.ClearToEmpty();
}
inline const std::string& GetNextDiscriminatorConfigResponse::active() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.active)
  return _internal_active();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextDiscriminatorConfigResponse::set_active(ArgT0&& arg0, ArgT... args) {
 
 active_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.active)
}
inline std::string* GetNextDiscriminatorConfigResponse::mutable_active() {
  std::string* _s = _internal_mutable_active();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.active)
  return _s;
}
inline const std::string& GetNextDiscriminatorConfigResponse::_internal_active() const {
  return active_.Get();
}
inline void GetNextDiscriminatorConfigResponse::_internal_set_active(const std::string& value) {
  
  active_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextDiscriminatorConfigResponse::_internal_mutable_active() {
  
  return active_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextDiscriminatorConfigResponse::release_active() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.active)
  return active_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextDiscriminatorConfigResponse::set_allocated_active(std::string* active) {
  if (active != nullptr) {
    
  } else {
    
  }
  active_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.active)
}

// map<string, string> available = 3;
inline int GetNextDiscriminatorConfigResponse::_internal_available_size() const {
  return available_.size();
}
inline int GetNextDiscriminatorConfigResponse::available_size() const {
  return _internal_available_size();
}
inline void GetNextDiscriminatorConfigResponse::clear_available() {
  available_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetNextDiscriminatorConfigResponse::_internal_available() const {
  return available_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetNextDiscriminatorConfigResponse::available() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.available)
  return _internal_available();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetNextDiscriminatorConfigResponse::_internal_mutable_available() {
  return available_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetNextDiscriminatorConfigResponse::mutable_available() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.available)
  return _internal_mutable_available();
}

// -------------------------------------------------------------------

// GetNextModelinatorConfigResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextModelinatorConfigResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextModelinatorConfigResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextModelinatorConfigResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextModelinatorConfigResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.GetNextModelinatorConfigResponse.ts)
  return _internal_ts();
}
inline void GetNextModelinatorConfigResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.GetNextModelinatorConfigResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextModelinatorConfigResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextModelinatorConfigResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.GetNextModelinatorConfigResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextModelinatorConfigResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextModelinatorConfigResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.GetNextModelinatorConfigResponse.ts)
  return _msg;
}
inline void GetNextModelinatorConfigResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.GetNextModelinatorConfigResponse.ts)
}

// .carbon.aimbot.almanac.ModelinatorConfig config = 2;
inline bool GetNextModelinatorConfigResponse::_internal_has_config() const {
  return this != internal_default_instance() && config_ != nullptr;
}
inline bool GetNextModelinatorConfigResponse::has_config() const {
  return _internal_has_config();
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& GetNextModelinatorConfigResponse::_internal_config() const {
  const ::carbon::aimbot::almanac::ModelinatorConfig* p = config_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::ModelinatorConfig&>(
      ::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& GetNextModelinatorConfigResponse::config() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.GetNextModelinatorConfigResponse.config)
  return _internal_config();
}
inline void GetNextModelinatorConfigResponse::unsafe_arena_set_allocated_config(
    ::carbon::aimbot::almanac::ModelinatorConfig* config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  config_ = config;
  if (config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.GetNextModelinatorConfigResponse.config)
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetNextModelinatorConfigResponse::release_config() {
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = config_;
  config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetNextModelinatorConfigResponse::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.GetNextModelinatorConfigResponse.config)
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = config_;
  config_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetNextModelinatorConfigResponse::_internal_mutable_config() {
  
  if (config_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::ModelinatorConfig>(GetArenaForAllocation());
    config_ = p;
  }
  return config_;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetNextModelinatorConfigResponse::mutable_config() {
  ::carbon::aimbot::almanac::ModelinatorConfig* _msg = _internal_mutable_config();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.GetNextModelinatorConfigResponse.config)
  return _msg;
}
inline void GetNextModelinatorConfigResponse::set_allocated_config(::carbon::aimbot::almanac::ModelinatorConfig* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config));
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    
  } else {
    
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.GetNextModelinatorConfigResponse.config)
}

// -------------------------------------------------------------------

// SaveModelinatorConfigRequest

// .carbon.aimbot.almanac.ModelinatorConfig config = 1;
inline bool SaveModelinatorConfigRequest::_internal_has_config() const {
  return this != internal_default_instance() && config_ != nullptr;
}
inline bool SaveModelinatorConfigRequest::has_config() const {
  return _internal_has_config();
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& SaveModelinatorConfigRequest::_internal_config() const {
  const ::carbon::aimbot::almanac::ModelinatorConfig* p = config_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::ModelinatorConfig&>(
      ::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& SaveModelinatorConfigRequest::config() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.SaveModelinatorConfigRequest.config)
  return _internal_config();
}
inline void SaveModelinatorConfigRequest::unsafe_arena_set_allocated_config(
    ::carbon::aimbot::almanac::ModelinatorConfig* config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  config_ = config;
  if (config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.SaveModelinatorConfigRequest.config)
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* SaveModelinatorConfigRequest::release_config() {
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = config_;
  config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* SaveModelinatorConfigRequest::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.SaveModelinatorConfigRequest.config)
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = config_;
  config_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* SaveModelinatorConfigRequest::_internal_mutable_config() {
  
  if (config_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::ModelinatorConfig>(GetArenaForAllocation());
    config_ = p;
  }
  return config_;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* SaveModelinatorConfigRequest::mutable_config() {
  ::carbon::aimbot::almanac::ModelinatorConfig* _msg = _internal_mutable_config();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.SaveModelinatorConfigRequest.config)
  return _msg;
}
inline void SaveModelinatorConfigRequest::set_allocated_config(::carbon::aimbot::almanac::ModelinatorConfig* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config));
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    
  } else {
    
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.SaveModelinatorConfigRequest.config)
}

// -------------------------------------------------------------------

// FetchModelinatorConfigRequest

// string model_id = 1;
inline void FetchModelinatorConfigRequest::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& FetchModelinatorConfigRequest::model_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.FetchModelinatorConfigRequest.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FetchModelinatorConfigRequest::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.FetchModelinatorConfigRequest.model_id)
}
inline std::string* FetchModelinatorConfigRequest::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.FetchModelinatorConfigRequest.model_id)
  return _s;
}
inline const std::string& FetchModelinatorConfigRequest::_internal_model_id() const {
  return model_id_.Get();
}
inline void FetchModelinatorConfigRequest::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FetchModelinatorConfigRequest::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FetchModelinatorConfigRequest::release_model_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.FetchModelinatorConfigRequest.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FetchModelinatorConfigRequest::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.FetchModelinatorConfigRequest.model_id)
}

// string crop_id = 2;
inline void FetchModelinatorConfigRequest::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& FetchModelinatorConfigRequest::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.FetchModelinatorConfigRequest.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FetchModelinatorConfigRequest::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.FetchModelinatorConfigRequest.crop_id)
}
inline std::string* FetchModelinatorConfigRequest::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.FetchModelinatorConfigRequest.crop_id)
  return _s;
}
inline const std::string& FetchModelinatorConfigRequest::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void FetchModelinatorConfigRequest::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FetchModelinatorConfigRequest::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FetchModelinatorConfigRequest::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.FetchModelinatorConfigRequest.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FetchModelinatorConfigRequest::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.FetchModelinatorConfigRequest.crop_id)
}

// -------------------------------------------------------------------

// FetchModelinatorConfigResponse

// .carbon.aimbot.almanac.ModelinatorConfig config = 1;
inline bool FetchModelinatorConfigResponse::_internal_has_config() const {
  return this != internal_default_instance() && config_ != nullptr;
}
inline bool FetchModelinatorConfigResponse::has_config() const {
  return _internal_has_config();
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& FetchModelinatorConfigResponse::_internal_config() const {
  const ::carbon::aimbot::almanac::ModelinatorConfig* p = config_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::ModelinatorConfig&>(
      ::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& FetchModelinatorConfigResponse::config() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.FetchModelinatorConfigResponse.config)
  return _internal_config();
}
inline void FetchModelinatorConfigResponse::unsafe_arena_set_allocated_config(
    ::carbon::aimbot::almanac::ModelinatorConfig* config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  config_ = config;
  if (config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.FetchModelinatorConfigResponse.config)
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* FetchModelinatorConfigResponse::release_config() {
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = config_;
  config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* FetchModelinatorConfigResponse::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.FetchModelinatorConfigResponse.config)
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = config_;
  config_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* FetchModelinatorConfigResponse::_internal_mutable_config() {
  
  if (config_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::ModelinatorConfig>(GetArenaForAllocation());
    config_ = p;
  }
  return config_;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* FetchModelinatorConfigResponse::mutable_config() {
  ::carbon::aimbot::almanac::ModelinatorConfig* _msg = _internal_mutable_config();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.FetchModelinatorConfigResponse.config)
  return _msg;
}
inline void FetchModelinatorConfigResponse::set_allocated_config(::carbon::aimbot::almanac::ModelinatorConfig* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config));
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    
  } else {
    
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.FetchModelinatorConfigResponse.config)
}

// -------------------------------------------------------------------

// ResetModelinatorConfigRequest

// -------------------------------------------------------------------

// GetNextConfigDataRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextConfigDataRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextConfigDataRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextConfigDataRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextConfigDataRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.GetNextConfigDataRequest.ts)
  return _internal_ts();
}
inline void GetNextConfigDataRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.GetNextConfigDataRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigDataRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigDataRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.GetNextConfigDataRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigDataRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigDataRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.GetNextConfigDataRequest.ts)
  return _msg;
}
inline void GetNextConfigDataRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.GetNextConfigDataRequest.ts)
}

// string lang = 2;
inline void GetNextConfigDataRequest::clear_lang() {
  lang_.ClearToEmpty();
}
inline const std::string& GetNextConfigDataRequest::lang() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.GetNextConfigDataRequest.lang)
  return _internal_lang();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextConfigDataRequest::set_lang(ArgT0&& arg0, ArgT... args) {
 
 lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.GetNextConfigDataRequest.lang)
}
inline std::string* GetNextConfigDataRequest::mutable_lang() {
  std::string* _s = _internal_mutable_lang();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.GetNextConfigDataRequest.lang)
  return _s;
}
inline const std::string& GetNextConfigDataRequest::_internal_lang() const {
  return lang_.Get();
}
inline void GetNextConfigDataRequest::_internal_set_lang(const std::string& value) {
  
  lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextConfigDataRequest::_internal_mutable_lang() {
  
  return lang_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextConfigDataRequest::release_lang() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.GetNextConfigDataRequest.lang)
  return lang_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextConfigDataRequest::set_allocated_lang(std::string* lang) {
  if (lang != nullptr) {
    
  } else {
    
  }
  lang_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), lang,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (lang_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.GetNextConfigDataRequest.lang)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GetNextConfigDataResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextConfigDataResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextConfigDataResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextConfigDataResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextConfigDataResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.GetNextConfigDataResponse.ts)
  return _internal_ts();
}
inline void GetNextConfigDataResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.almanac.GetNextConfigDataResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigDataResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigDataResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.almanac.GetNextConfigDataResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigDataResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigDataResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.almanac.GetNextConfigDataResponse.ts)
  return _msg;
}
inline void GetNextConfigDataResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.almanac.GetNextConfigDataResponse.ts)
}

// uint32 num_size_categories = 2;
inline void GetNextConfigDataResponse::clear_num_size_categories() {
  num_size_categories_ = 0u;
}
inline uint32_t GetNextConfigDataResponse::_internal_num_size_categories() const {
  return num_size_categories_;
}
inline uint32_t GetNextConfigDataResponse::num_size_categories() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.almanac.GetNextConfigDataResponse.num_size_categories)
  return _internal_num_size_categories();
}
inline void GetNextConfigDataResponse::_internal_set_num_size_categories(uint32_t value) {
  
  num_size_categories_ = value;
}
inline void GetNextConfigDataResponse::set_num_size_categories(uint32_t value) {
  _internal_set_num_size_categories(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.almanac.GetNextConfigDataResponse.num_size_categories)
}

// map<string, string> crop_category_names = 3;
inline int GetNextConfigDataResponse::_internal_crop_category_names_size() const {
  return crop_category_names_.size();
}
inline int GetNextConfigDataResponse::crop_category_names_size() const {
  return _internal_crop_category_names_size();
}
inline void GetNextConfigDataResponse::clear_crop_category_names() {
  crop_category_names_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetNextConfigDataResponse::_internal_crop_category_names() const {
  return crop_category_names_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetNextConfigDataResponse::crop_category_names() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.almanac.GetNextConfigDataResponse.crop_category_names)
  return _internal_crop_category_names();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetNextConfigDataResponse::_internal_mutable_crop_category_names() {
  return crop_category_names_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetNextConfigDataResponse::mutable_crop_category_names() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.almanac.GetNextConfigDataResponse.crop_category_names)
  return _internal_mutable_crop_category_names();
}

// map<string, string> weed_category_names = 4;
inline int GetNextConfigDataResponse::_internal_weed_category_names_size() const {
  return weed_category_names_.size();
}
inline int GetNextConfigDataResponse::weed_category_names_size() const {
  return _internal_weed_category_names_size();
}
inline void GetNextConfigDataResponse::clear_weed_category_names() {
  weed_category_names_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetNextConfigDataResponse::_internal_weed_category_names() const {
  return weed_category_names_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
GetNextConfigDataResponse::weed_category_names() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.almanac.GetNextConfigDataResponse.weed_category_names)
  return _internal_weed_category_names();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetNextConfigDataResponse::_internal_mutable_weed_category_names() {
  return weed_category_names_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
GetNextConfigDataResponse::mutable_weed_category_names() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.almanac.GetNextConfigDataResponse.weed_category_names)
  return _internal_mutable_weed_category_names();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace almanac
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2falmanac_2eproto

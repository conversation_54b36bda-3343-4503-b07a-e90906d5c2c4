"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)

from generated.weed_tracking.proto.weed_tracking_pb2 import (
    Bands as weed_tracking___proto___weed_tracking_pb2___Bands,
    Detections as weed_tracking___proto___weed_tracking_pb2___Detections,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Annotations(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    crosshair_x: builtin___int = ...
    crosshair_y: builtin___int = ...

    @property
    def detections(self) -> weed_tracking___proto___weed_tracking_pb2___Detections: ...

    @property
    def bands(self) -> weed_tracking___proto___weed_tracking_pb2___Bands: ...

    def __init__(self,
        *,
        detections : typing___Optional[weed_tracking___proto___weed_tracking_pb2___Detections] = None,
        bands : typing___Optional[weed_tracking___proto___weed_tracking_pb2___Bands] = None,
        crosshair_x : typing___Optional[builtin___int] = None,
        crosshair_y : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"bands",b"bands",u"detections",b"detections"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bands",b"bands",u"crosshair_x",b"crosshair_x",u"crosshair_y",b"crosshair_y",u"detections",b"detections"]) -> None: ...
type___Annotations = Annotations

class Image(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    width: builtin___int = ...
    height: builtin___int = ...
    focus: builtin___float = ...
    data: builtin___bytes = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def annotations(self) -> type___Annotations: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        width : typing___Optional[builtin___int] = None,
        height : typing___Optional[builtin___int] = None,
        focus : typing___Optional[builtin___float] = None,
        data : typing___Optional[builtin___bytes] = None,
        annotations : typing___Optional[type___Annotations] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"annotations",b"annotations",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"annotations",b"annotations",u"data",b"data",u"focus",b"focus",u"height",b"height",u"ts",b"ts",u"width",b"width"]) -> None: ...
type___Image = Image

class CameraImageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    annotated: builtin___bool = ...
    include_annotations_metadata: builtin___bool = ...
    dont_downsample: builtin___bool = ...
    encode_as_png: builtin___bool = ...
    encode_as_raw: builtin___bool = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        annotated : typing___Optional[builtin___bool] = None,
        include_annotations_metadata : typing___Optional[builtin___bool] = None,
        dont_downsample : typing___Optional[builtin___bool] = None,
        encode_as_png : typing___Optional[builtin___bool] = None,
        encode_as_raw : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"annotated",b"annotated",u"cam_id",b"cam_id",u"dont_downsample",b"dont_downsample",u"encode_as_png",b"encode_as_png",u"encode_as_raw",b"encode_as_raw",u"include_annotations_metadata",b"include_annotations_metadata",u"ts",b"ts"]) -> None: ...
type___CameraImageRequest = CameraImageRequest

class GetPredictImageByTimestampRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    crop_around_x: builtin___int = ...
    crop_around_y: builtin___int = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        crop_around_x : typing___Optional[builtin___int] = None,
        crop_around_y : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"crop_around_x",b"crop_around_x",u"crop_around_y",b"crop_around_y",u"ts",b"ts"]) -> None: ...
type___GetPredictImageByTimestampRequest = GetPredictImageByTimestampRequest

class GetPredictImageByTimestampResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    data: builtin___bytes = ...
    center_x: builtin___int = ...
    center_y: builtin___int = ...

    def __init__(self,
        *,
        data : typing___Optional[builtin___bytes] = None,
        center_x : typing___Optional[builtin___int] = None,
        center_y : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_x",b"center_x",u"center_y",b"center_y",u"data",b"data"]) -> None: ...
type___GetPredictImageByTimestampResponse = GetPredictImageByTimestampResponse

class PossiblePerspective(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    crop_around_x: builtin___int = ...
    crop_around_y: builtin___int = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        crop_around_x : typing___Optional[builtin___int] = None,
        crop_around_y : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_around_x",b"crop_around_x",u"crop_around_y",b"crop_around_y",u"ts",b"ts"]) -> None: ...
type___PossiblePerspective = PossiblePerspective

class GetMultiPredictPerspectivesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    requested_perspectives: builtin___int = ...

    @property
    def perspectives(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PossiblePerspective]: ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        perspectives : typing___Optional[typing___Iterable[type___PossiblePerspective]] = None,
        requested_perspectives : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"perspectives",b"perspectives",u"requested_perspectives",b"requested_perspectives"]) -> None: ...
type___GetMultiPredictPerspectivesRequest = GetMultiPredictPerspectivesRequest

class CentroidPerspective(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    center_x: builtin___int = ...
    center_y: builtin___int = ...
    data: builtin___bytes = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        center_x : typing___Optional[builtin___int] = None,
        center_y : typing___Optional[builtin___int] = None,
        data : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_x",b"center_x",u"center_y",b"center_y",u"data",b"data",u"ts",b"ts"]) -> None: ...
type___CentroidPerspective = CentroidPerspective

class GetMultiPredictPerspectivesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def perspectives(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CentroidPerspective]: ...

    def __init__(self,
        *,
        perspectives : typing___Optional[typing___Iterable[type___CentroidPerspective]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"perspectives",b"perspectives"]) -> None: ...
type___GetMultiPredictPerspectivesResponse = GetMultiPredictPerspectivesResponse

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/startup_task.proto

#include "frontend/proto/startup_task.pb.h"
#include "frontend/proto/startup_task.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace startup_task {

static const char* StartupTaskService_method_names[] = {
  "/carbon.frontend.startup_task.StartupTaskService/GetNextTasks",
  "/carbon.frontend.startup_task.StartupTaskService/MarkTaskComplete",
};

std::unique_ptr< StartupTaskService::Stub> StartupTaskService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< StartupTaskService::Stub> stub(new StartupTaskService::Stub(channel, options));
  return stub;
}

StartupTaskService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextTasks_(StartupTaskService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_MarkTaskComplete_(StartupTaskService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status StartupTaskService::Stub::GetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::startup_task::GetNextTasksResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::startup_task::GetNextTasksResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextTasks_, context, request, response);
}

void StartupTaskService::Stub::async::GetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::startup_task::GetNextTasksResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::startup_task::GetNextTasksResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextTasks_, context, request, response, std::move(f));
}

void StartupTaskService::Stub::async::GetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::startup_task::GetNextTasksResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextTasks_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::GetNextTasksResponse>* StartupTaskService::Stub::PrepareAsyncGetNextTasksRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::startup_task::GetNextTasksResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextTasks_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::GetNextTasksResponse>* StartupTaskService::Stub::AsyncGetNextTasksRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextTasksRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status StartupTaskService::Stub::MarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::startup_task::MarkTaskCompleteRequest, ::carbon::frontend::startup_task::MarkTaskCompleteResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_MarkTaskComplete_, context, request, response);
}

void StartupTaskService::Stub::async::MarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::startup_task::MarkTaskCompleteRequest, ::carbon::frontend::startup_task::MarkTaskCompleteResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_MarkTaskComplete_, context, request, response, std::move(f));
}

void StartupTaskService::Stub::async::MarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_MarkTaskComplete_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>* StartupTaskService::Stub::PrepareAsyncMarkTaskCompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::startup_task::MarkTaskCompleteResponse, ::carbon::frontend::startup_task::MarkTaskCompleteRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_MarkTaskComplete_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>* StartupTaskService::Stub::AsyncMarkTaskCompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncMarkTaskCompleteRaw(context, request, cq);
  result->StartCall();
  return result;
}

StartupTaskService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      StartupTaskService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< StartupTaskService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::startup_task::GetNextTasksResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](StartupTaskService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::startup_task::GetNextTasksResponse* resp) {
               return service->GetNextTasks(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      StartupTaskService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< StartupTaskService::Service, ::carbon::frontend::startup_task::MarkTaskCompleteRequest, ::carbon::frontend::startup_task::MarkTaskCompleteResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](StartupTaskService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* req,
             ::carbon::frontend::startup_task::MarkTaskCompleteResponse* resp) {
               return service->MarkTaskComplete(ctx, req, resp);
             }, this)));
}

StartupTaskService::Service::~Service() {
}

::grpc::Status StartupTaskService::Service::GetNextTasks(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::startup_task::GetNextTasksResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status StartupTaskService::Service::MarkTaskComplete(::grpc::ServerContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace startup_task


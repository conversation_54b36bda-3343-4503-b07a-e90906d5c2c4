# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/almanac.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2
from generated.proto.almanac import almanac_pb2 as proto_dot_almanac_dot_almanac__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/almanac.proto',
  package='carbon.frontend.almanac',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1c\x66rontend/proto/almanac.proto\x12\x17\x63\x61rbon.frontend.almanac\x1a\x19\x66rontend/proto/util.proto\x1a\x1bproto/almanac/almanac.proto\"\xf4\x02\n\x15GetConfigDataResponse\x12\x1b\n\x13num_size_categories\x18\x01 \x01(\r\x12\x62\n\x13\x63rop_category_names\x18\x02 \x03(\x0b\x32\x45.carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry\x12\x62\n\x13weed_category_names\x18\x03 \x03(\x0b\x32\x45.carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry\x1a\x38\n\x16\x43ropCategoryNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x38\n\x16WeedCategoryNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01:\x02\x18\x01\"&\n\x18LoadAlmanacConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\"Q\n\x19LoadAlmanacConfigResponse\x12\x34\n\x06\x63onfig\x18\x01 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfig\"d\n\x18SaveAlmanacConfigRequest\x12\x34\n\x06\x63onfig\x18\x01 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfig\x12\x12\n\nset_active\x18\x02 \x01(\x08\"\'\n\x19SaveAlmanacConfigResponse\x12\n\n\x02id\x18\x01 \x01(\t\"+\n\x1dSetActiveAlmanacConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\"?\n\x1a\x44\x65leteAlmanacConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x15\n\rnew_active_id\x18\x02 \x01(\t\"\xe6\x01\n\x1cGetNextAlmanacConfigResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0e\n\x06\x61\x63tive\x18\x02 \x01(\t\x12W\n\tavailable\x18\x03 \x03(\x0b\x32\x44.carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry\x1a\x30\n\x0e\x41vailableEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\",\n\x1eLoadDiscriminatorConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\"]\n\x1fLoadDiscriminatorConfigResponse\x12:\n\x06\x63onfig\x18\x01 \x01(\x0b\x32*.carbon.aimbot.almanac.DiscriminatorConfig\"\x80\x01\n\x1eSaveDiscriminatorConfigRequest\x12:\n\x06\x63onfig\x18\x01 \x01(\x0b\x32*.carbon.aimbot.almanac.DiscriminatorConfig\x12\"\n\x1a\x61ssociate_with_active_crop\x18\x02 \x01(\x08\"-\n\x1fSaveDiscriminatorConfigResponse\x12\n\n\x02id\x18\x01 \x01(\t\"S\n#SetActiveDiscriminatorConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x14\n\x07\x63rop_id\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\n\n\x08_crop_id\".\n DeleteDiscriminatorConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\xf2\x01\n\"GetNextDiscriminatorConfigResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0e\n\x06\x61\x63tive\x18\x02 \x01(\t\x12]\n\tavailable\x18\x03 \x03(\x0b\x32J.carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry\x1a\x30\n\x0e\x41vailableEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x89\x01\n GetNextModelinatorConfigResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x38\n\x06\x63onfig\x18\x02 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\"X\n\x1cSaveModelinatorConfigRequest\x12\x38\n\x06\x63onfig\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\"B\n\x1d\x46\x65tchModelinatorConfigRequest\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x02 \x01(\t\"Z\n\x1e\x46\x65tchModelinatorConfigResponse\x12\x38\n\x06\x63onfig\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\"\x1f\n\x1dResetModelinatorConfigRequest\"U\n\x18GetNextConfigDataRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0c\n\x04lang\x18\x02 \x01(\t\"\xa9\x03\n\x19GetNextConfigDataResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x1b\n\x13num_size_categories\x18\x02 \x01(\r\x12\x66\n\x13\x63rop_category_names\x18\x03 \x03(\x0b\x32I.carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry\x12\x66\n\x13weed_category_names\x18\x04 \x03(\x0b\x32I.carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry\x1a\x38\n\x16\x43ropCategoryNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x38\n\x16WeedCategoryNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x32\x9f\x0f\n\x14\x41lmanacConfigService\x12\x61\n\rGetConfigData\x12\x1b.carbon.frontend.util.Empty\x1a..carbon.frontend.almanac.GetConfigDataResponse\"\x03\x88\x02\x01\x12z\n\x11GetNextConfigData\x12\x31.carbon.frontend.almanac.GetNextConfigDataRequest\x1a\x32.carbon.frontend.almanac.GetNextConfigDataResponse\x12z\n\x11LoadAlmanacConfig\x12\x31.carbon.frontend.almanac.LoadAlmanacConfigRequest\x1a\x32.carbon.frontend.almanac.LoadAlmanacConfigResponse\x12z\n\x11SaveAlmanacConfig\x12\x31.carbon.frontend.almanac.SaveAlmanacConfigRequest\x1a\x32.carbon.frontend.almanac.SaveAlmanacConfigResponse\x12m\n\x16SetActiveAlmanacConfig\x12\x36.carbon.frontend.almanac.SetActiveAlmanacConfigRequest\x1a\x1b.carbon.frontend.util.Empty\x12g\n\x13\x44\x65leteAlmanacConfig\x12\x33.carbon.frontend.almanac.DeleteAlmanacConfigRequest\x1a\x1b.carbon.frontend.util.Empty\x12n\n\x14GetNextAlmanacConfig\x12\x1f.carbon.frontend.util.Timestamp\x1a\x35.carbon.frontend.almanac.GetNextAlmanacConfigResponse\x12\x8c\x01\n\x17LoadDiscriminatorConfig\x12\x37.carbon.frontend.almanac.LoadDiscriminatorConfigRequest\x1a\x38.carbon.frontend.almanac.LoadDiscriminatorConfigResponse\x12\x8c\x01\n\x17SaveDiscriminatorConfig\x12\x37.carbon.frontend.almanac.SaveDiscriminatorConfigRequest\x1a\x38.carbon.frontend.almanac.SaveDiscriminatorConfigResponse\x12y\n\x1cSetActiveDiscriminatorConfig\x12<.carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest\x1a\x1b.carbon.frontend.util.Empty\x12s\n\x19\x44\x65leteDiscriminatorConfig\x12\x39.carbon.frontend.almanac.DeleteDiscriminatorConfigRequest\x1a\x1b.carbon.frontend.util.Empty\x12z\n\x1aGetNextDiscriminatorConfig\x12\x1f.carbon.frontend.util.Timestamp\x1a;.carbon.frontend.almanac.GetNextDiscriminatorConfigResponse\x12v\n\x18GetNextModelinatorConfig\x12\x1f.carbon.frontend.util.Timestamp\x1a\x39.carbon.frontend.almanac.GetNextModelinatorConfigResponse\x12k\n\x15SaveModelinatorConfig\x12\x35.carbon.frontend.almanac.SaveModelinatorConfigRequest\x1a\x1b.carbon.frontend.util.Empty\x12\x89\x01\n\x16\x46\x65tchModelinatorConfig\x12\x36.carbon.frontend.almanac.FetchModelinatorConfigRequest\x1a\x37.carbon.frontend.almanac.FetchModelinatorConfigResponse\x12m\n\x16ResetModelinatorConfig\x12\x36.carbon.frontend.almanac.ResetModelinatorConfigRequest\x1a\x1b.carbon.frontend.util.EmptyB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,proto_dot_almanac_dot_almanac__pb2.DESCRIPTOR,])




_GETCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY = _descriptor.Descriptor(
  name='CropCategoryNamesEntry',
  full_name='carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=368,
  serialized_end=424,
)

_GETCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY = _descriptor.Descriptor(
  name='WeedCategoryNamesEntry',
  full_name='carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=426,
  serialized_end=482,
)

_GETCONFIGDATARESPONSE = _descriptor.Descriptor(
  name='GetConfigDataResponse',
  full_name='carbon.frontend.almanac.GetConfigDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='num_size_categories', full_name='carbon.frontend.almanac.GetConfigDataResponse.num_size_categories', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_category_names', full_name='carbon.frontend.almanac.GetConfigDataResponse.crop_category_names', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_category_names', full_name='carbon.frontend.almanac.GetConfigDataResponse.weed_category_names', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY, _GETCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY, ],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=114,
  serialized_end=486,
)


_LOADALMANACCONFIGREQUEST = _descriptor.Descriptor(
  name='LoadAlmanacConfigRequest',
  full_name='carbon.frontend.almanac.LoadAlmanacConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.almanac.LoadAlmanacConfigRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=488,
  serialized_end=526,
)


_LOADALMANACCONFIGRESPONSE = _descriptor.Descriptor(
  name='LoadAlmanacConfigResponse',
  full_name='carbon.frontend.almanac.LoadAlmanacConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='carbon.frontend.almanac.LoadAlmanacConfigResponse.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=528,
  serialized_end=609,
)


_SAVEALMANACCONFIGREQUEST = _descriptor.Descriptor(
  name='SaveAlmanacConfigRequest',
  full_name='carbon.frontend.almanac.SaveAlmanacConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='carbon.frontend.almanac.SaveAlmanacConfigRequest.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='set_active', full_name='carbon.frontend.almanac.SaveAlmanacConfigRequest.set_active', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=611,
  serialized_end=711,
)


_SAVEALMANACCONFIGRESPONSE = _descriptor.Descriptor(
  name='SaveAlmanacConfigResponse',
  full_name='carbon.frontend.almanac.SaveAlmanacConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.almanac.SaveAlmanacConfigResponse.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=713,
  serialized_end=752,
)


_SETACTIVEALMANACCONFIGREQUEST = _descriptor.Descriptor(
  name='SetActiveAlmanacConfigRequest',
  full_name='carbon.frontend.almanac.SetActiveAlmanacConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.almanac.SetActiveAlmanacConfigRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=754,
  serialized_end=797,
)


_DELETEALMANACCONFIGREQUEST = _descriptor.Descriptor(
  name='DeleteAlmanacConfigRequest',
  full_name='carbon.frontend.almanac.DeleteAlmanacConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.almanac.DeleteAlmanacConfigRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='new_active_id', full_name='carbon.frontend.almanac.DeleteAlmanacConfigRequest.new_active_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=799,
  serialized_end=862,
)


_GETNEXTALMANACCONFIGRESPONSE_AVAILABLEENTRY = _descriptor.Descriptor(
  name='AvailableEntry',
  full_name='carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1047,
  serialized_end=1095,
)

_GETNEXTALMANACCONFIGRESPONSE = _descriptor.Descriptor(
  name='GetNextAlmanacConfigResponse',
  full_name='carbon.frontend.almanac.GetNextAlmanacConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.almanac.GetNextAlmanacConfigResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active', full_name='carbon.frontend.almanac.GetNextAlmanacConfigResponse.active', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='available', full_name='carbon.frontend.almanac.GetNextAlmanacConfigResponse.available', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETNEXTALMANACCONFIGRESPONSE_AVAILABLEENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=865,
  serialized_end=1095,
)


_LOADDISCRIMINATORCONFIGREQUEST = _descriptor.Descriptor(
  name='LoadDiscriminatorConfigRequest',
  full_name='carbon.frontend.almanac.LoadDiscriminatorConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.almanac.LoadDiscriminatorConfigRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1097,
  serialized_end=1141,
)


_LOADDISCRIMINATORCONFIGRESPONSE = _descriptor.Descriptor(
  name='LoadDiscriminatorConfigResponse',
  full_name='carbon.frontend.almanac.LoadDiscriminatorConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='carbon.frontend.almanac.LoadDiscriminatorConfigResponse.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1143,
  serialized_end=1236,
)


_SAVEDISCRIMINATORCONFIGREQUEST = _descriptor.Descriptor(
  name='SaveDiscriminatorConfigRequest',
  full_name='carbon.frontend.almanac.SaveDiscriminatorConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='carbon.frontend.almanac.SaveDiscriminatorConfigRequest.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='associate_with_active_crop', full_name='carbon.frontend.almanac.SaveDiscriminatorConfigRequest.associate_with_active_crop', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1239,
  serialized_end=1367,
)


_SAVEDISCRIMINATORCONFIGRESPONSE = _descriptor.Descriptor(
  name='SaveDiscriminatorConfigResponse',
  full_name='carbon.frontend.almanac.SaveDiscriminatorConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.almanac.SaveDiscriminatorConfigResponse.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1369,
  serialized_end=1414,
)


_SETACTIVEDISCRIMINATORCONFIGREQUEST = _descriptor.Descriptor(
  name='SetActiveDiscriminatorConfigRequest',
  full_name='carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest.crop_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_crop_id', full_name='carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest._crop_id',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1416,
  serialized_end=1499,
)


_DELETEDISCRIMINATORCONFIGREQUEST = _descriptor.Descriptor(
  name='DeleteDiscriminatorConfigRequest',
  full_name='carbon.frontend.almanac.DeleteDiscriminatorConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.almanac.DeleteDiscriminatorConfigRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1501,
  serialized_end=1547,
)


_GETNEXTDISCRIMINATORCONFIGRESPONSE_AVAILABLEENTRY = _descriptor.Descriptor(
  name='AvailableEntry',
  full_name='carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1047,
  serialized_end=1095,
)

_GETNEXTDISCRIMINATORCONFIGRESPONSE = _descriptor.Descriptor(
  name='GetNextDiscriminatorConfigResponse',
  full_name='carbon.frontend.almanac.GetNextDiscriminatorConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active', full_name='carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.active', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='available', full_name='carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.available', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETNEXTDISCRIMINATORCONFIGRESPONSE_AVAILABLEENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1550,
  serialized_end=1792,
)


_GETNEXTMODELINATORCONFIGRESPONSE = _descriptor.Descriptor(
  name='GetNextModelinatorConfigResponse',
  full_name='carbon.frontend.almanac.GetNextModelinatorConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.almanac.GetNextModelinatorConfigResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='config', full_name='carbon.frontend.almanac.GetNextModelinatorConfigResponse.config', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1795,
  serialized_end=1932,
)


_SAVEMODELINATORCONFIGREQUEST = _descriptor.Descriptor(
  name='SaveModelinatorConfigRequest',
  full_name='carbon.frontend.almanac.SaveModelinatorConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='carbon.frontend.almanac.SaveModelinatorConfigRequest.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1934,
  serialized_end=2022,
)


_FETCHMODELINATORCONFIGREQUEST = _descriptor.Descriptor(
  name='FetchModelinatorConfigRequest',
  full_name='carbon.frontend.almanac.FetchModelinatorConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.frontend.almanac.FetchModelinatorConfigRequest.model_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.almanac.FetchModelinatorConfigRequest.crop_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2024,
  serialized_end=2090,
)


_FETCHMODELINATORCONFIGRESPONSE = _descriptor.Descriptor(
  name='FetchModelinatorConfigResponse',
  full_name='carbon.frontend.almanac.FetchModelinatorConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='carbon.frontend.almanac.FetchModelinatorConfigResponse.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2092,
  serialized_end=2182,
)


_RESETMODELINATORCONFIGREQUEST = _descriptor.Descriptor(
  name='ResetModelinatorConfigRequest',
  full_name='carbon.frontend.almanac.ResetModelinatorConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2184,
  serialized_end=2215,
)


_GETNEXTCONFIGDATAREQUEST = _descriptor.Descriptor(
  name='GetNextConfigDataRequest',
  full_name='carbon.frontend.almanac.GetNextConfigDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.almanac.GetNextConfigDataRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lang', full_name='carbon.frontend.almanac.GetNextConfigDataRequest.lang', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2217,
  serialized_end=2302,
)


_GETNEXTCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY = _descriptor.Descriptor(
  name='CropCategoryNamesEntry',
  full_name='carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=368,
  serialized_end=424,
)

_GETNEXTCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY = _descriptor.Descriptor(
  name='WeedCategoryNamesEntry',
  full_name='carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=426,
  serialized_end=482,
)

_GETNEXTCONFIGDATARESPONSE = _descriptor.Descriptor(
  name='GetNextConfigDataResponse',
  full_name='carbon.frontend.almanac.GetNextConfigDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.almanac.GetNextConfigDataResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_size_categories', full_name='carbon.frontend.almanac.GetNextConfigDataResponse.num_size_categories', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_category_names', full_name='carbon.frontend.almanac.GetNextConfigDataResponse.crop_category_names', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_category_names', full_name='carbon.frontend.almanac.GetNextConfigDataResponse.weed_category_names', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETNEXTCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY, _GETNEXTCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2305,
  serialized_end=2730,
)

_GETCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY.containing_type = _GETCONFIGDATARESPONSE
_GETCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY.containing_type = _GETCONFIGDATARESPONSE
_GETCONFIGDATARESPONSE.fields_by_name['crop_category_names'].message_type = _GETCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY
_GETCONFIGDATARESPONSE.fields_by_name['weed_category_names'].message_type = _GETCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY
_LOADALMANACCONFIGRESPONSE.fields_by_name['config'].message_type = proto_dot_almanac_dot_almanac__pb2._ALMANACCONFIG
_SAVEALMANACCONFIGREQUEST.fields_by_name['config'].message_type = proto_dot_almanac_dot_almanac__pb2._ALMANACCONFIG
_GETNEXTALMANACCONFIGRESPONSE_AVAILABLEENTRY.containing_type = _GETNEXTALMANACCONFIGRESPONSE
_GETNEXTALMANACCONFIGRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTALMANACCONFIGRESPONSE.fields_by_name['available'].message_type = _GETNEXTALMANACCONFIGRESPONSE_AVAILABLEENTRY
_LOADDISCRIMINATORCONFIGRESPONSE.fields_by_name['config'].message_type = proto_dot_almanac_dot_almanac__pb2._DISCRIMINATORCONFIG
_SAVEDISCRIMINATORCONFIGREQUEST.fields_by_name['config'].message_type = proto_dot_almanac_dot_almanac__pb2._DISCRIMINATORCONFIG
_SETACTIVEDISCRIMINATORCONFIGREQUEST.oneofs_by_name['_crop_id'].fields.append(
  _SETACTIVEDISCRIMINATORCONFIGREQUEST.fields_by_name['crop_id'])
_SETACTIVEDISCRIMINATORCONFIGREQUEST.fields_by_name['crop_id'].containing_oneof = _SETACTIVEDISCRIMINATORCONFIGREQUEST.oneofs_by_name['_crop_id']
_GETNEXTDISCRIMINATORCONFIGRESPONSE_AVAILABLEENTRY.containing_type = _GETNEXTDISCRIMINATORCONFIGRESPONSE
_GETNEXTDISCRIMINATORCONFIGRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTDISCRIMINATORCONFIGRESPONSE.fields_by_name['available'].message_type = _GETNEXTDISCRIMINATORCONFIGRESPONSE_AVAILABLEENTRY
_GETNEXTMODELINATORCONFIGRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTMODELINATORCONFIGRESPONSE.fields_by_name['config'].message_type = proto_dot_almanac_dot_almanac__pb2._MODELINATORCONFIG
_SAVEMODELINATORCONFIGREQUEST.fields_by_name['config'].message_type = proto_dot_almanac_dot_almanac__pb2._MODELINATORCONFIG
_FETCHMODELINATORCONFIGRESPONSE.fields_by_name['config'].message_type = proto_dot_almanac_dot_almanac__pb2._MODELINATORCONFIG
_GETNEXTCONFIGDATAREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY.containing_type = _GETNEXTCONFIGDATARESPONSE
_GETNEXTCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY.containing_type = _GETNEXTCONFIGDATARESPONSE
_GETNEXTCONFIGDATARESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTCONFIGDATARESPONSE.fields_by_name['crop_category_names'].message_type = _GETNEXTCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY
_GETNEXTCONFIGDATARESPONSE.fields_by_name['weed_category_names'].message_type = _GETNEXTCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY
DESCRIPTOR.message_types_by_name['GetConfigDataResponse'] = _GETCONFIGDATARESPONSE
DESCRIPTOR.message_types_by_name['LoadAlmanacConfigRequest'] = _LOADALMANACCONFIGREQUEST
DESCRIPTOR.message_types_by_name['LoadAlmanacConfigResponse'] = _LOADALMANACCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['SaveAlmanacConfigRequest'] = _SAVEALMANACCONFIGREQUEST
DESCRIPTOR.message_types_by_name['SaveAlmanacConfigResponse'] = _SAVEALMANACCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['SetActiveAlmanacConfigRequest'] = _SETACTIVEALMANACCONFIGREQUEST
DESCRIPTOR.message_types_by_name['DeleteAlmanacConfigRequest'] = _DELETEALMANACCONFIGREQUEST
DESCRIPTOR.message_types_by_name['GetNextAlmanacConfigResponse'] = _GETNEXTALMANACCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['LoadDiscriminatorConfigRequest'] = _LOADDISCRIMINATORCONFIGREQUEST
DESCRIPTOR.message_types_by_name['LoadDiscriminatorConfigResponse'] = _LOADDISCRIMINATORCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['SaveDiscriminatorConfigRequest'] = _SAVEDISCRIMINATORCONFIGREQUEST
DESCRIPTOR.message_types_by_name['SaveDiscriminatorConfigResponse'] = _SAVEDISCRIMINATORCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['SetActiveDiscriminatorConfigRequest'] = _SETACTIVEDISCRIMINATORCONFIGREQUEST
DESCRIPTOR.message_types_by_name['DeleteDiscriminatorConfigRequest'] = _DELETEDISCRIMINATORCONFIGREQUEST
DESCRIPTOR.message_types_by_name['GetNextDiscriminatorConfigResponse'] = _GETNEXTDISCRIMINATORCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['GetNextModelinatorConfigResponse'] = _GETNEXTMODELINATORCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['SaveModelinatorConfigRequest'] = _SAVEMODELINATORCONFIGREQUEST
DESCRIPTOR.message_types_by_name['FetchModelinatorConfigRequest'] = _FETCHMODELINATORCONFIGREQUEST
DESCRIPTOR.message_types_by_name['FetchModelinatorConfigResponse'] = _FETCHMODELINATORCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['ResetModelinatorConfigRequest'] = _RESETMODELINATORCONFIGREQUEST
DESCRIPTOR.message_types_by_name['GetNextConfigDataRequest'] = _GETNEXTCONFIGDATAREQUEST
DESCRIPTOR.message_types_by_name['GetNextConfigDataResponse'] = _GETNEXTCONFIGDATARESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetConfigDataResponse = _reflection.GeneratedProtocolMessageType('GetConfigDataResponse', (_message.Message,), {

  'CropCategoryNamesEntry' : _reflection.GeneratedProtocolMessageType('CropCategoryNamesEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY,
    '__module__' : 'frontend.proto.almanac_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry)
    })
  ,

  'WeedCategoryNamesEntry' : _reflection.GeneratedProtocolMessageType('WeedCategoryNamesEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY,
    '__module__' : 'frontend.proto.almanac_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry)
    })
  ,
  'DESCRIPTOR' : _GETCONFIGDATARESPONSE,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetConfigDataResponse)
  })
_sym_db.RegisterMessage(GetConfigDataResponse)
_sym_db.RegisterMessage(GetConfigDataResponse.CropCategoryNamesEntry)
_sym_db.RegisterMessage(GetConfigDataResponse.WeedCategoryNamesEntry)

LoadAlmanacConfigRequest = _reflection.GeneratedProtocolMessageType('LoadAlmanacConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _LOADALMANACCONFIGREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.LoadAlmanacConfigRequest)
  })
_sym_db.RegisterMessage(LoadAlmanacConfigRequest)

LoadAlmanacConfigResponse = _reflection.GeneratedProtocolMessageType('LoadAlmanacConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _LOADALMANACCONFIGRESPONSE,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.LoadAlmanacConfigResponse)
  })
_sym_db.RegisterMessage(LoadAlmanacConfigResponse)

SaveAlmanacConfigRequest = _reflection.GeneratedProtocolMessageType('SaveAlmanacConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _SAVEALMANACCONFIGREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SaveAlmanacConfigRequest)
  })
_sym_db.RegisterMessage(SaveAlmanacConfigRequest)

SaveAlmanacConfigResponse = _reflection.GeneratedProtocolMessageType('SaveAlmanacConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _SAVEALMANACCONFIGRESPONSE,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SaveAlmanacConfigResponse)
  })
_sym_db.RegisterMessage(SaveAlmanacConfigResponse)

SetActiveAlmanacConfigRequest = _reflection.GeneratedProtocolMessageType('SetActiveAlmanacConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETACTIVEALMANACCONFIGREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SetActiveAlmanacConfigRequest)
  })
_sym_db.RegisterMessage(SetActiveAlmanacConfigRequest)

DeleteAlmanacConfigRequest = _reflection.GeneratedProtocolMessageType('DeleteAlmanacConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETEALMANACCONFIGREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.DeleteAlmanacConfigRequest)
  })
_sym_db.RegisterMessage(DeleteAlmanacConfigRequest)

GetNextAlmanacConfigResponse = _reflection.GeneratedProtocolMessageType('GetNextAlmanacConfigResponse', (_message.Message,), {

  'AvailableEntry' : _reflection.GeneratedProtocolMessageType('AvailableEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETNEXTALMANACCONFIGRESPONSE_AVAILABLEENTRY,
    '__module__' : 'frontend.proto.almanac_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry)
    })
  ,
  'DESCRIPTOR' : _GETNEXTALMANACCONFIGRESPONSE,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextAlmanacConfigResponse)
  })
_sym_db.RegisterMessage(GetNextAlmanacConfigResponse)
_sym_db.RegisterMessage(GetNextAlmanacConfigResponse.AvailableEntry)

LoadDiscriminatorConfigRequest = _reflection.GeneratedProtocolMessageType('LoadDiscriminatorConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _LOADDISCRIMINATORCONFIGREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.LoadDiscriminatorConfigRequest)
  })
_sym_db.RegisterMessage(LoadDiscriminatorConfigRequest)

LoadDiscriminatorConfigResponse = _reflection.GeneratedProtocolMessageType('LoadDiscriminatorConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _LOADDISCRIMINATORCONFIGRESPONSE,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.LoadDiscriminatorConfigResponse)
  })
_sym_db.RegisterMessage(LoadDiscriminatorConfigResponse)

SaveDiscriminatorConfigRequest = _reflection.GeneratedProtocolMessageType('SaveDiscriminatorConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _SAVEDISCRIMINATORCONFIGREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SaveDiscriminatorConfigRequest)
  })
_sym_db.RegisterMessage(SaveDiscriminatorConfigRequest)

SaveDiscriminatorConfigResponse = _reflection.GeneratedProtocolMessageType('SaveDiscriminatorConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _SAVEDISCRIMINATORCONFIGRESPONSE,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SaveDiscriminatorConfigResponse)
  })
_sym_db.RegisterMessage(SaveDiscriminatorConfigResponse)

SetActiveDiscriminatorConfigRequest = _reflection.GeneratedProtocolMessageType('SetActiveDiscriminatorConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETACTIVEDISCRIMINATORCONFIGREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest)
  })
_sym_db.RegisterMessage(SetActiveDiscriminatorConfigRequest)

DeleteDiscriminatorConfigRequest = _reflection.GeneratedProtocolMessageType('DeleteDiscriminatorConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETEDISCRIMINATORCONFIGREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.DeleteDiscriminatorConfigRequest)
  })
_sym_db.RegisterMessage(DeleteDiscriminatorConfigRequest)

GetNextDiscriminatorConfigResponse = _reflection.GeneratedProtocolMessageType('GetNextDiscriminatorConfigResponse', (_message.Message,), {

  'AvailableEntry' : _reflection.GeneratedProtocolMessageType('AvailableEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETNEXTDISCRIMINATORCONFIGRESPONSE_AVAILABLEENTRY,
    '__module__' : 'frontend.proto.almanac_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry)
    })
  ,
  'DESCRIPTOR' : _GETNEXTDISCRIMINATORCONFIGRESPONSE,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextDiscriminatorConfigResponse)
  })
_sym_db.RegisterMessage(GetNextDiscriminatorConfigResponse)
_sym_db.RegisterMessage(GetNextDiscriminatorConfigResponse.AvailableEntry)

GetNextModelinatorConfigResponse = _reflection.GeneratedProtocolMessageType('GetNextModelinatorConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTMODELINATORCONFIGRESPONSE,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextModelinatorConfigResponse)
  })
_sym_db.RegisterMessage(GetNextModelinatorConfigResponse)

SaveModelinatorConfigRequest = _reflection.GeneratedProtocolMessageType('SaveModelinatorConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _SAVEMODELINATORCONFIGREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.SaveModelinatorConfigRequest)
  })
_sym_db.RegisterMessage(SaveModelinatorConfigRequest)

FetchModelinatorConfigRequest = _reflection.GeneratedProtocolMessageType('FetchModelinatorConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _FETCHMODELINATORCONFIGREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.FetchModelinatorConfigRequest)
  })
_sym_db.RegisterMessage(FetchModelinatorConfigRequest)

FetchModelinatorConfigResponse = _reflection.GeneratedProtocolMessageType('FetchModelinatorConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _FETCHMODELINATORCONFIGRESPONSE,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.FetchModelinatorConfigResponse)
  })
_sym_db.RegisterMessage(FetchModelinatorConfigResponse)

ResetModelinatorConfigRequest = _reflection.GeneratedProtocolMessageType('ResetModelinatorConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _RESETMODELINATORCONFIGREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.ResetModelinatorConfigRequest)
  })
_sym_db.RegisterMessage(ResetModelinatorConfigRequest)

GetNextConfigDataRequest = _reflection.GeneratedProtocolMessageType('GetNextConfigDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTCONFIGDATAREQUEST,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextConfigDataRequest)
  })
_sym_db.RegisterMessage(GetNextConfigDataRequest)

GetNextConfigDataResponse = _reflection.GeneratedProtocolMessageType('GetNextConfigDataResponse', (_message.Message,), {

  'CropCategoryNamesEntry' : _reflection.GeneratedProtocolMessageType('CropCategoryNamesEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETNEXTCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY,
    '__module__' : 'frontend.proto.almanac_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry)
    })
  ,

  'WeedCategoryNamesEntry' : _reflection.GeneratedProtocolMessageType('WeedCategoryNamesEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETNEXTCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY,
    '__module__' : 'frontend.proto.almanac_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry)
    })
  ,
  'DESCRIPTOR' : _GETNEXTCONFIGDATARESPONSE,
  '__module__' : 'frontend.proto.almanac_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.almanac.GetNextConfigDataResponse)
  })
_sym_db.RegisterMessage(GetNextConfigDataResponse)
_sym_db.RegisterMessage(GetNextConfigDataResponse.CropCategoryNamesEntry)
_sym_db.RegisterMessage(GetNextConfigDataResponse.WeedCategoryNamesEntry)


DESCRIPTOR._options = None
_GETCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY._options = None
_GETCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY._options = None
_GETCONFIGDATARESPONSE._options = None
_GETNEXTALMANACCONFIGRESPONSE_AVAILABLEENTRY._options = None
_GETNEXTDISCRIMINATORCONFIGRESPONSE_AVAILABLEENTRY._options = None
_GETNEXTCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY._options = None
_GETNEXTCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY._options = None

_ALMANACCONFIGSERVICE = _descriptor.ServiceDescriptor(
  name='AlmanacConfigService',
  full_name='carbon.frontend.almanac.AlmanacConfigService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=2733,
  serialized_end=4684,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetConfigData',
    full_name='carbon.frontend.almanac.AlmanacConfigService.GetConfigData',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_GETCONFIGDATARESPONSE,
    serialized_options=b'\210\002\001',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextConfigData',
    full_name='carbon.frontend.almanac.AlmanacConfigService.GetNextConfigData',
    index=1,
    containing_service=None,
    input_type=_GETNEXTCONFIGDATAREQUEST,
    output_type=_GETNEXTCONFIGDATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LoadAlmanacConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.LoadAlmanacConfig',
    index=2,
    containing_service=None,
    input_type=_LOADALMANACCONFIGREQUEST,
    output_type=_LOADALMANACCONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SaveAlmanacConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.SaveAlmanacConfig',
    index=3,
    containing_service=None,
    input_type=_SAVEALMANACCONFIGREQUEST,
    output_type=_SAVEALMANACCONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetActiveAlmanacConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.SetActiveAlmanacConfig',
    index=4,
    containing_service=None,
    input_type=_SETACTIVEALMANACCONFIGREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteAlmanacConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.DeleteAlmanacConfig',
    index=5,
    containing_service=None,
    input_type=_DELETEALMANACCONFIGREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextAlmanacConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.GetNextAlmanacConfig',
    index=6,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTALMANACCONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LoadDiscriminatorConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.LoadDiscriminatorConfig',
    index=7,
    containing_service=None,
    input_type=_LOADDISCRIMINATORCONFIGREQUEST,
    output_type=_LOADDISCRIMINATORCONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SaveDiscriminatorConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.SaveDiscriminatorConfig',
    index=8,
    containing_service=None,
    input_type=_SAVEDISCRIMINATORCONFIGREQUEST,
    output_type=_SAVEDISCRIMINATORCONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetActiveDiscriminatorConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.SetActiveDiscriminatorConfig',
    index=9,
    containing_service=None,
    input_type=_SETACTIVEDISCRIMINATORCONFIGREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteDiscriminatorConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.DeleteDiscriminatorConfig',
    index=10,
    containing_service=None,
    input_type=_DELETEDISCRIMINATORCONFIGREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextDiscriminatorConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.GetNextDiscriminatorConfig',
    index=11,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTDISCRIMINATORCONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextModelinatorConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.GetNextModelinatorConfig',
    index=12,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTMODELINATORCONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SaveModelinatorConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.SaveModelinatorConfig',
    index=13,
    containing_service=None,
    input_type=_SAVEMODELINATORCONFIGREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='FetchModelinatorConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.FetchModelinatorConfig',
    index=14,
    containing_service=None,
    input_type=_FETCHMODELINATORCONFIGREQUEST,
    output_type=_FETCHMODELINATORCONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ResetModelinatorConfig',
    full_name='carbon.frontend.almanac.AlmanacConfigService.ResetModelinatorConfig',
    index=15,
    containing_service=None,
    input_type=_RESETMODELINATORCONFIGREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_ALMANACCONFIGSERVICE)

DESCRIPTOR.services_by_name['AlmanacConfigService'] = _ALMANACCONFIGSERVICE

# @@protoc_insertion_point(module_scope)

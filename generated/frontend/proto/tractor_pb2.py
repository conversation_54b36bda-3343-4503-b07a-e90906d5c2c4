# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/tractor.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/tractor.proto',
  package='carbon.frontend.tractor',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1c\x66rontend/proto/tractor.proto\x12\x17\x63\x61rbon.frontend.tractor\x1a\x19\x66rontend/proto/util.proto\"5\n\x0eTractorIfState\x12\x10\n\x08\x65xpected\x18\x01 \x01(\x08\x12\x11\n\tconnected\x18\x02 \x01(\x08\"7\n\x12TractorSafetyState\x12\x0f\n\x07is_safe\x18\x01 \x01(\x08\x12\x10\n\x08\x65nforced\x18\x02 \x01(\x08\"\x84\x01\n\x1dGetNextTractorIfStateResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x36\n\x05state\x18\x02 \x01(\x0b\x32\'.carbon.frontend.tractor.TractorIfState\"\x8c\x01\n!GetNextTractorSafetyStateResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12:\n\x05state\x18\x02 \x01(\x0b\x32+.carbon.frontend.tractor.TractorSafetyState\"/\n\x1bSetEnforcementPolicyRequest\x12\x10\n\x08\x65nforced\x18\x01 \x01(\x08\"\x1e\n\x1cSetEnforcementPolicyResponse2\x82\x03\n\x0eTractorService\x12p\n\x15GetNextTractorIfState\x12\x1f.carbon.frontend.util.Timestamp\x1a\x36.carbon.frontend.tractor.GetNextTractorIfStateResponse\x12x\n\x19GetNextTractorSafetyState\x12\x1f.carbon.frontend.util.Timestamp\x1a:.carbon.frontend.tractor.GetNextTractorSafetyStateResponse\x12\x83\x01\n\x14SetEnforcementPolicy\x12\x34.carbon.frontend.tractor.SetEnforcementPolicyRequest\x1a\x35.carbon.frontend.tractor.SetEnforcementPolicyResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_TRACTORIFSTATE = _descriptor.Descriptor(
  name='TractorIfState',
  full_name='carbon.frontend.tractor.TractorIfState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='expected', full_name='carbon.frontend.tractor.TractorIfState.expected', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='connected', full_name='carbon.frontend.tractor.TractorIfState.connected', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=84,
  serialized_end=137,
)


_TRACTORSAFETYSTATE = _descriptor.Descriptor(
  name='TractorSafetyState',
  full_name='carbon.frontend.tractor.TractorSafetyState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='is_safe', full_name='carbon.frontend.tractor.TractorSafetyState.is_safe', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enforced', full_name='carbon.frontend.tractor.TractorSafetyState.enforced', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=139,
  serialized_end=194,
)


_GETNEXTTRACTORIFSTATERESPONSE = _descriptor.Descriptor(
  name='GetNextTractorIfStateResponse',
  full_name='carbon.frontend.tractor.GetNextTractorIfStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.tractor.GetNextTractorIfStateResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.frontend.tractor.GetNextTractorIfStateResponse.state', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=197,
  serialized_end=329,
)


_GETNEXTTRACTORSAFETYSTATERESPONSE = _descriptor.Descriptor(
  name='GetNextTractorSafetyStateResponse',
  full_name='carbon.frontend.tractor.GetNextTractorSafetyStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.tractor.GetNextTractorSafetyStateResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.frontend.tractor.GetNextTractorSafetyStateResponse.state', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=332,
  serialized_end=472,
)


_SETENFORCEMENTPOLICYREQUEST = _descriptor.Descriptor(
  name='SetEnforcementPolicyRequest',
  full_name='carbon.frontend.tractor.SetEnforcementPolicyRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enforced', full_name='carbon.frontend.tractor.SetEnforcementPolicyRequest.enforced', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=474,
  serialized_end=521,
)


_SETENFORCEMENTPOLICYRESPONSE = _descriptor.Descriptor(
  name='SetEnforcementPolicyResponse',
  full_name='carbon.frontend.tractor.SetEnforcementPolicyResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=523,
  serialized_end=553,
)

_GETNEXTTRACTORIFSTATERESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTTRACTORIFSTATERESPONSE.fields_by_name['state'].message_type = _TRACTORIFSTATE
_GETNEXTTRACTORSAFETYSTATERESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTTRACTORSAFETYSTATERESPONSE.fields_by_name['state'].message_type = _TRACTORSAFETYSTATE
DESCRIPTOR.message_types_by_name['TractorIfState'] = _TRACTORIFSTATE
DESCRIPTOR.message_types_by_name['TractorSafetyState'] = _TRACTORSAFETYSTATE
DESCRIPTOR.message_types_by_name['GetNextTractorIfStateResponse'] = _GETNEXTTRACTORIFSTATERESPONSE
DESCRIPTOR.message_types_by_name['GetNextTractorSafetyStateResponse'] = _GETNEXTTRACTORSAFETYSTATERESPONSE
DESCRIPTOR.message_types_by_name['SetEnforcementPolicyRequest'] = _SETENFORCEMENTPOLICYREQUEST
DESCRIPTOR.message_types_by_name['SetEnforcementPolicyResponse'] = _SETENFORCEMENTPOLICYRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TractorIfState = _reflection.GeneratedProtocolMessageType('TractorIfState', (_message.Message,), {
  'DESCRIPTOR' : _TRACTORIFSTATE,
  '__module__' : 'frontend.proto.tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.tractor.TractorIfState)
  })
_sym_db.RegisterMessage(TractorIfState)

TractorSafetyState = _reflection.GeneratedProtocolMessageType('TractorSafetyState', (_message.Message,), {
  'DESCRIPTOR' : _TRACTORSAFETYSTATE,
  '__module__' : 'frontend.proto.tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.tractor.TractorSafetyState)
  })
_sym_db.RegisterMessage(TractorSafetyState)

GetNextTractorIfStateResponse = _reflection.GeneratedProtocolMessageType('GetNextTractorIfStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTTRACTORIFSTATERESPONSE,
  '__module__' : 'frontend.proto.tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.tractor.GetNextTractorIfStateResponse)
  })
_sym_db.RegisterMessage(GetNextTractorIfStateResponse)

GetNextTractorSafetyStateResponse = _reflection.GeneratedProtocolMessageType('GetNextTractorSafetyStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTTRACTORSAFETYSTATERESPONSE,
  '__module__' : 'frontend.proto.tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.tractor.GetNextTractorSafetyStateResponse)
  })
_sym_db.RegisterMessage(GetNextTractorSafetyStateResponse)

SetEnforcementPolicyRequest = _reflection.GeneratedProtocolMessageType('SetEnforcementPolicyRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETENFORCEMENTPOLICYREQUEST,
  '__module__' : 'frontend.proto.tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.tractor.SetEnforcementPolicyRequest)
  })
_sym_db.RegisterMessage(SetEnforcementPolicyRequest)

SetEnforcementPolicyResponse = _reflection.GeneratedProtocolMessageType('SetEnforcementPolicyResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETENFORCEMENTPOLICYRESPONSE,
  '__module__' : 'frontend.proto.tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.tractor.SetEnforcementPolicyResponse)
  })
_sym_db.RegisterMessage(SetEnforcementPolicyResponse)


DESCRIPTOR._options = None

_TRACTORSERVICE = _descriptor.ServiceDescriptor(
  name='TractorService',
  full_name='carbon.frontend.tractor.TractorService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=556,
  serialized_end=942,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextTractorIfState',
    full_name='carbon.frontend.tractor.TractorService.GetNextTractorIfState',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTTRACTORIFSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextTractorSafetyState',
    full_name='carbon.frontend.tractor.TractorService.GetNextTractorSafetyState',
    index=1,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTTRACTORSAFETYSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetEnforcementPolicy',
    full_name='carbon.frontend.tractor.TractorService.SetEnforcementPolicy',
    index=2,
    containing_service=None,
    input_type=_SETENFORCEMENTPOLICYREQUEST,
    output_type=_SETENFORCEMENTPOLICYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_TRACTORSERVICE)

DESCRIPTOR.services_by_name['TractorService'] = _TRACTORSERVICE

# @@protoc_insertion_point(module_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/chip.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fchip_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fchip_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fchip_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fchip_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fchip_2eproto;
namespace carbon {
namespace frontend {
namespace chip {
class ChipData;
struct ChipDataDefaultTypeInternal;
extern ChipDataDefaultTypeInternal _ChipData_default_instance_;
class ChipIdsResponse;
struct ChipIdsResponseDefaultTypeInternal;
extern ChipIdsResponseDefaultTypeInternal _ChipIdsResponse_default_instance_;
class GetChipMetadataResponse;
struct GetChipMetadataResponseDefaultTypeInternal;
extern GetChipMetadataResponseDefaultTypeInternal _GetChipMetadataResponse_default_instance_;
}  // namespace chip
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::chip::ChipData* Arena::CreateMaybeMessage<::carbon::frontend::chip::ChipData>(Arena*);
template<> ::carbon::frontend::chip::ChipIdsResponse* Arena::CreateMaybeMessage<::carbon::frontend::chip::ChipIdsResponse>(Arena*);
template<> ::carbon::frontend::chip::GetChipMetadataResponse* Arena::CreateMaybeMessage<::carbon::frontend::chip::GetChipMetadataResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace chip {

// ===================================================================

class ChipData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.chip.ChipData) */ {
 public:
  inline ChipData() : ChipData(nullptr) {}
  ~ChipData() override;
  explicit constexpr ChipData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ChipData(const ChipData& from);
  ChipData(ChipData&& from) noexcept
    : ChipData() {
    *this = ::std::move(from);
  }

  inline ChipData& operator=(const ChipData& from) {
    CopyFrom(from);
    return *this;
  }
  inline ChipData& operator=(ChipData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ChipData& default_instance() {
    return *internal_default_instance();
  }
  static inline const ChipData* internal_default_instance() {
    return reinterpret_cast<const ChipData*>(
               &_ChipData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ChipData& a, ChipData& b) {
    a.Swap(&b);
  }
  inline void Swap(ChipData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ChipData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ChipData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ChipData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ChipData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ChipData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ChipData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.chip.ChipData";
  }
  protected:
  explicit ChipData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kUrlFieldNumber = 2,
    kGeohashFieldNumber = 3,
    kChecksumFieldNumber = 4,
    kDownloadedTsFieldNumber = 6,
    kLastUsedTsFieldNumber = 7,
    kContentLengthFieldNumber = 5,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string url = 2;
  void clear_url();
  const std::string& url() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_url(ArgT0&& arg0, ArgT... args);
  std::string* mutable_url();
  PROTOBUF_NODISCARD std::string* release_url();
  void set_allocated_url(std::string* url);
  private:
  const std::string& _internal_url() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_url(const std::string& value);
  std::string* _internal_mutable_url();
  public:

  // string geohash = 3;
  void clear_geohash();
  const std::string& geohash() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_geohash(ArgT0&& arg0, ArgT... args);
  std::string* mutable_geohash();
  PROTOBUF_NODISCARD std::string* release_geohash();
  void set_allocated_geohash(std::string* geohash);
  private:
  const std::string& _internal_geohash() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_geohash(const std::string& value);
  std::string* _internal_mutable_geohash();
  public:

  // string checksum = 4;
  void clear_checksum();
  const std::string& checksum() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_checksum(ArgT0&& arg0, ArgT... args);
  std::string* mutable_checksum();
  PROTOBUF_NODISCARD std::string* release_checksum();
  void set_allocated_checksum(std::string* checksum);
  private:
  const std::string& _internal_checksum() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_checksum(const std::string& value);
  std::string* _internal_mutable_checksum();
  public:

  // .carbon.frontend.util.Timestamp downloaded_ts = 6;
  bool has_downloaded_ts() const;
  private:
  bool _internal_has_downloaded_ts() const;
  public:
  void clear_downloaded_ts();
  const ::carbon::frontend::util::Timestamp& downloaded_ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_downloaded_ts();
  ::carbon::frontend::util::Timestamp* mutable_downloaded_ts();
  void set_allocated_downloaded_ts(::carbon::frontend::util::Timestamp* downloaded_ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_downloaded_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_downloaded_ts();
  public:
  void unsafe_arena_set_allocated_downloaded_ts(
      ::carbon::frontend::util::Timestamp* downloaded_ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_downloaded_ts();

  // .carbon.frontend.util.Timestamp last_used_ts = 7;
  bool has_last_used_ts() const;
  private:
  bool _internal_has_last_used_ts() const;
  public:
  void clear_last_used_ts();
  const ::carbon::frontend::util::Timestamp& last_used_ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_last_used_ts();
  ::carbon::frontend::util::Timestamp* mutable_last_used_ts();
  void set_allocated_last_used_ts(::carbon::frontend::util::Timestamp* last_used_ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_last_used_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_last_used_ts();
  public:
  void unsafe_arena_set_allocated_last_used_ts(
      ::carbon::frontend::util::Timestamp* last_used_ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_last_used_ts();

  // uint32 content_length = 5;
  void clear_content_length();
  uint32_t content_length() const;
  void set_content_length(uint32_t value);
  private:
  uint32_t _internal_content_length() const;
  void _internal_set_content_length(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.chip.ChipData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr url_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr geohash_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr checksum_;
  ::carbon::frontend::util::Timestamp* downloaded_ts_;
  ::carbon::frontend::util::Timestamp* last_used_ts_;
  uint32_t content_length_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fchip_2eproto;
};
// -------------------------------------------------------------------

class GetChipMetadataResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.chip.GetChipMetadataResponse) */ {
 public:
  inline GetChipMetadataResponse() : GetChipMetadataResponse(nullptr) {}
  ~GetChipMetadataResponse() override;
  explicit constexpr GetChipMetadataResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetChipMetadataResponse(const GetChipMetadataResponse& from);
  GetChipMetadataResponse(GetChipMetadataResponse&& from) noexcept
    : GetChipMetadataResponse() {
    *this = ::std::move(from);
  }

  inline GetChipMetadataResponse& operator=(const GetChipMetadataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetChipMetadataResponse& operator=(GetChipMetadataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetChipMetadataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetChipMetadataResponse* internal_default_instance() {
    return reinterpret_cast<const GetChipMetadataResponse*>(
               &_GetChipMetadataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GetChipMetadataResponse& a, GetChipMetadataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetChipMetadataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetChipMetadataResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetChipMetadataResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetChipMetadataResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetChipMetadataResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetChipMetadataResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetChipMetadataResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.chip.GetChipMetadataResponse";
  }
  protected:
  explicit GetChipMetadataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChipsFieldNumber = 1,
  };
  // repeated .carbon.frontend.chip.ChipData chips = 1;
  int chips_size() const;
  private:
  int _internal_chips_size() const;
  public:
  void clear_chips();
  ::carbon::frontend::chip::ChipData* mutable_chips(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::chip::ChipData >*
      mutable_chips();
  private:
  const ::carbon::frontend::chip::ChipData& _internal_chips(int index) const;
  ::carbon::frontend::chip::ChipData* _internal_add_chips();
  public:
  const ::carbon::frontend::chip::ChipData& chips(int index) const;
  ::carbon::frontend::chip::ChipData* add_chips();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::chip::ChipData >&
      chips() const;

  // @@protoc_insertion_point(class_scope:carbon.frontend.chip.GetChipMetadataResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::chip::ChipData > chips_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fchip_2eproto;
};
// -------------------------------------------------------------------

class ChipIdsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.chip.ChipIdsResponse) */ {
 public:
  inline ChipIdsResponse() : ChipIdsResponse(nullptr) {}
  ~ChipIdsResponse() override;
  explicit constexpr ChipIdsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ChipIdsResponse(const ChipIdsResponse& from);
  ChipIdsResponse(ChipIdsResponse&& from) noexcept
    : ChipIdsResponse() {
    *this = ::std::move(from);
  }

  inline ChipIdsResponse& operator=(const ChipIdsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ChipIdsResponse& operator=(ChipIdsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ChipIdsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ChipIdsResponse* internal_default_instance() {
    return reinterpret_cast<const ChipIdsResponse*>(
               &_ChipIdsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ChipIdsResponse& a, ChipIdsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ChipIdsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ChipIdsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ChipIdsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ChipIdsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ChipIdsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ChipIdsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ChipIdsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.chip.ChipIdsResponse";
  }
  protected:
  explicit ChipIdsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChipIdsFieldNumber = 1,
  };
  // repeated string chip_ids = 1;
  int chip_ids_size() const;
  private:
  int _internal_chip_ids_size() const;
  public:
  void clear_chip_ids();
  const std::string& chip_ids(int index) const;
  std::string* mutable_chip_ids(int index);
  void set_chip_ids(int index, const std::string& value);
  void set_chip_ids(int index, std::string&& value);
  void set_chip_ids(int index, const char* value);
  void set_chip_ids(int index, const char* value, size_t size);
  std::string* add_chip_ids();
  void add_chip_ids(const std::string& value);
  void add_chip_ids(std::string&& value);
  void add_chip_ids(const char* value);
  void add_chip_ids(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& chip_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_chip_ids();
  private:
  const std::string& _internal_chip_ids(int index) const;
  std::string* _internal_add_chip_ids();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.chip.ChipIdsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> chip_ids_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fchip_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ChipData

// string id = 1;
inline void ChipData::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& ChipData::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.chip.ChipData.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ChipData::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.chip.ChipData.id)
}
inline std::string* ChipData::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.chip.ChipData.id)
  return _s;
}
inline const std::string& ChipData::_internal_id() const {
  return id_.Get();
}
inline void ChipData::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ChipData::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ChipData::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.chip.ChipData.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ChipData::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.chip.ChipData.id)
}

// string url = 2;
inline void ChipData::clear_url() {
  url_.ClearToEmpty();
}
inline const std::string& ChipData::url() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.chip.ChipData.url)
  return _internal_url();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ChipData::set_url(ArgT0&& arg0, ArgT... args) {
 
 url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.chip.ChipData.url)
}
inline std::string* ChipData::mutable_url() {
  std::string* _s = _internal_mutable_url();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.chip.ChipData.url)
  return _s;
}
inline const std::string& ChipData::_internal_url() const {
  return url_.Get();
}
inline void ChipData::_internal_set_url(const std::string& value) {
  
  url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ChipData::_internal_mutable_url() {
  
  return url_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ChipData::release_url() {
  // @@protoc_insertion_point(field_release:carbon.frontend.chip.ChipData.url)
  return url_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ChipData::set_allocated_url(std::string* url) {
  if (url != nullptr) {
    
  } else {
    
  }
  url_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), url,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (url_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    url_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.chip.ChipData.url)
}

// string geohash = 3;
inline void ChipData::clear_geohash() {
  geohash_.ClearToEmpty();
}
inline const std::string& ChipData::geohash() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.chip.ChipData.geohash)
  return _internal_geohash();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ChipData::set_geohash(ArgT0&& arg0, ArgT... args) {
 
 geohash_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.chip.ChipData.geohash)
}
inline std::string* ChipData::mutable_geohash() {
  std::string* _s = _internal_mutable_geohash();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.chip.ChipData.geohash)
  return _s;
}
inline const std::string& ChipData::_internal_geohash() const {
  return geohash_.Get();
}
inline void ChipData::_internal_set_geohash(const std::string& value) {
  
  geohash_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ChipData::_internal_mutable_geohash() {
  
  return geohash_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ChipData::release_geohash() {
  // @@protoc_insertion_point(field_release:carbon.frontend.chip.ChipData.geohash)
  return geohash_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ChipData::set_allocated_geohash(std::string* geohash) {
  if (geohash != nullptr) {
    
  } else {
    
  }
  geohash_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), geohash,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (geohash_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    geohash_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.chip.ChipData.geohash)
}

// string checksum = 4;
inline void ChipData::clear_checksum() {
  checksum_.ClearToEmpty();
}
inline const std::string& ChipData::checksum() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.chip.ChipData.checksum)
  return _internal_checksum();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ChipData::set_checksum(ArgT0&& arg0, ArgT... args) {
 
 checksum_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.chip.ChipData.checksum)
}
inline std::string* ChipData::mutable_checksum() {
  std::string* _s = _internal_mutable_checksum();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.chip.ChipData.checksum)
  return _s;
}
inline const std::string& ChipData::_internal_checksum() const {
  return checksum_.Get();
}
inline void ChipData::_internal_set_checksum(const std::string& value) {
  
  checksum_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ChipData::_internal_mutable_checksum() {
  
  return checksum_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ChipData::release_checksum() {
  // @@protoc_insertion_point(field_release:carbon.frontend.chip.ChipData.checksum)
  return checksum_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ChipData::set_allocated_checksum(std::string* checksum) {
  if (checksum != nullptr) {
    
  } else {
    
  }
  checksum_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), checksum,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (checksum_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    checksum_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.chip.ChipData.checksum)
}

// uint32 content_length = 5;
inline void ChipData::clear_content_length() {
  content_length_ = 0u;
}
inline uint32_t ChipData::_internal_content_length() const {
  return content_length_;
}
inline uint32_t ChipData::content_length() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.chip.ChipData.content_length)
  return _internal_content_length();
}
inline void ChipData::_internal_set_content_length(uint32_t value) {
  
  content_length_ = value;
}
inline void ChipData::set_content_length(uint32_t value) {
  _internal_set_content_length(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.chip.ChipData.content_length)
}

// .carbon.frontend.util.Timestamp downloaded_ts = 6;
inline bool ChipData::_internal_has_downloaded_ts() const {
  return this != internal_default_instance() && downloaded_ts_ != nullptr;
}
inline bool ChipData::has_downloaded_ts() const {
  return _internal_has_downloaded_ts();
}
inline const ::carbon::frontend::util::Timestamp& ChipData::_internal_downloaded_ts() const {
  const ::carbon::frontend::util::Timestamp* p = downloaded_ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& ChipData::downloaded_ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.chip.ChipData.downloaded_ts)
  return _internal_downloaded_ts();
}
inline void ChipData::unsafe_arena_set_allocated_downloaded_ts(
    ::carbon::frontend::util::Timestamp* downloaded_ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(downloaded_ts_);
  }
  downloaded_ts_ = downloaded_ts;
  if (downloaded_ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.chip.ChipData.downloaded_ts)
}
inline ::carbon::frontend::util::Timestamp* ChipData::release_downloaded_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = downloaded_ts_;
  downloaded_ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* ChipData::unsafe_arena_release_downloaded_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.chip.ChipData.downloaded_ts)
  
  ::carbon::frontend::util::Timestamp* temp = downloaded_ts_;
  downloaded_ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* ChipData::_internal_mutable_downloaded_ts() {
  
  if (downloaded_ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    downloaded_ts_ = p;
  }
  return downloaded_ts_;
}
inline ::carbon::frontend::util::Timestamp* ChipData::mutable_downloaded_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_downloaded_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.chip.ChipData.downloaded_ts)
  return _msg;
}
inline void ChipData::set_allocated_downloaded_ts(::carbon::frontend::util::Timestamp* downloaded_ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(downloaded_ts_);
  }
  if (downloaded_ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(downloaded_ts));
    if (message_arena != submessage_arena) {
      downloaded_ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, downloaded_ts, submessage_arena);
    }
    
  } else {
    
  }
  downloaded_ts_ = downloaded_ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.chip.ChipData.downloaded_ts)
}

// .carbon.frontend.util.Timestamp last_used_ts = 7;
inline bool ChipData::_internal_has_last_used_ts() const {
  return this != internal_default_instance() && last_used_ts_ != nullptr;
}
inline bool ChipData::has_last_used_ts() const {
  return _internal_has_last_used_ts();
}
inline const ::carbon::frontend::util::Timestamp& ChipData::_internal_last_used_ts() const {
  const ::carbon::frontend::util::Timestamp* p = last_used_ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& ChipData::last_used_ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.chip.ChipData.last_used_ts)
  return _internal_last_used_ts();
}
inline void ChipData::unsafe_arena_set_allocated_last_used_ts(
    ::carbon::frontend::util::Timestamp* last_used_ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(last_used_ts_);
  }
  last_used_ts_ = last_used_ts;
  if (last_used_ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.chip.ChipData.last_used_ts)
}
inline ::carbon::frontend::util::Timestamp* ChipData::release_last_used_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = last_used_ts_;
  last_used_ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* ChipData::unsafe_arena_release_last_used_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.chip.ChipData.last_used_ts)
  
  ::carbon::frontend::util::Timestamp* temp = last_used_ts_;
  last_used_ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* ChipData::_internal_mutable_last_used_ts() {
  
  if (last_used_ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    last_used_ts_ = p;
  }
  return last_used_ts_;
}
inline ::carbon::frontend::util::Timestamp* ChipData::mutable_last_used_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_last_used_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.chip.ChipData.last_used_ts)
  return _msg;
}
inline void ChipData::set_allocated_last_used_ts(::carbon::frontend::util::Timestamp* last_used_ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(last_used_ts_);
  }
  if (last_used_ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(last_used_ts));
    if (message_arena != submessage_arena) {
      last_used_ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, last_used_ts, submessage_arena);
    }
    
  } else {
    
  }
  last_used_ts_ = last_used_ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.chip.ChipData.last_used_ts)
}

// -------------------------------------------------------------------

// GetChipMetadataResponse

// repeated .carbon.frontend.chip.ChipData chips = 1;
inline int GetChipMetadataResponse::_internal_chips_size() const {
  return chips_.size();
}
inline int GetChipMetadataResponse::chips_size() const {
  return _internal_chips_size();
}
inline void GetChipMetadataResponse::clear_chips() {
  chips_.Clear();
}
inline ::carbon::frontend::chip::ChipData* GetChipMetadataResponse::mutable_chips(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.chip.GetChipMetadataResponse.chips)
  return chips_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::chip::ChipData >*
GetChipMetadataResponse::mutable_chips() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.chip.GetChipMetadataResponse.chips)
  return &chips_;
}
inline const ::carbon::frontend::chip::ChipData& GetChipMetadataResponse::_internal_chips(int index) const {
  return chips_.Get(index);
}
inline const ::carbon::frontend::chip::ChipData& GetChipMetadataResponse::chips(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.chip.GetChipMetadataResponse.chips)
  return _internal_chips(index);
}
inline ::carbon::frontend::chip::ChipData* GetChipMetadataResponse::_internal_add_chips() {
  return chips_.Add();
}
inline ::carbon::frontend::chip::ChipData* GetChipMetadataResponse::add_chips() {
  ::carbon::frontend::chip::ChipData* _add = _internal_add_chips();
  // @@protoc_insertion_point(field_add:carbon.frontend.chip.GetChipMetadataResponse.chips)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::chip::ChipData >&
GetChipMetadataResponse::chips() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.chip.GetChipMetadataResponse.chips)
  return chips_;
}

// -------------------------------------------------------------------

// ChipIdsResponse

// repeated string chip_ids = 1;
inline int ChipIdsResponse::_internal_chip_ids_size() const {
  return chip_ids_.size();
}
inline int ChipIdsResponse::chip_ids_size() const {
  return _internal_chip_ids_size();
}
inline void ChipIdsResponse::clear_chip_ids() {
  chip_ids_.Clear();
}
inline std::string* ChipIdsResponse::add_chip_ids() {
  std::string* _s = _internal_add_chip_ids();
  // @@protoc_insertion_point(field_add_mutable:carbon.frontend.chip.ChipIdsResponse.chip_ids)
  return _s;
}
inline const std::string& ChipIdsResponse::_internal_chip_ids(int index) const {
  return chip_ids_.Get(index);
}
inline const std::string& ChipIdsResponse::chip_ids(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.chip.ChipIdsResponse.chip_ids)
  return _internal_chip_ids(index);
}
inline std::string* ChipIdsResponse::mutable_chip_ids(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.chip.ChipIdsResponse.chip_ids)
  return chip_ids_.Mutable(index);
}
inline void ChipIdsResponse::set_chip_ids(int index, const std::string& value) {
  chip_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.chip.ChipIdsResponse.chip_ids)
}
inline void ChipIdsResponse::set_chip_ids(int index, std::string&& value) {
  chip_ids_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.frontend.chip.ChipIdsResponse.chip_ids)
}
inline void ChipIdsResponse::set_chip_ids(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  chip_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.frontend.chip.ChipIdsResponse.chip_ids)
}
inline void ChipIdsResponse::set_chip_ids(int index, const char* value, size_t size) {
  chip_ids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.frontend.chip.ChipIdsResponse.chip_ids)
}
inline std::string* ChipIdsResponse::_internal_add_chip_ids() {
  return chip_ids_.Add();
}
inline void ChipIdsResponse::add_chip_ids(const std::string& value) {
  chip_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.chip.ChipIdsResponse.chip_ids)
}
inline void ChipIdsResponse::add_chip_ids(std::string&& value) {
  chip_ids_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.frontend.chip.ChipIdsResponse.chip_ids)
}
inline void ChipIdsResponse::add_chip_ids(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  chip_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.frontend.chip.ChipIdsResponse.chip_ids)
}
inline void ChipIdsResponse::add_chip_ids(const char* value, size_t size) {
  chip_ids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.frontend.chip.ChipIdsResponse.chip_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ChipIdsResponse::chip_ids() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.chip.ChipIdsResponse.chip_ids)
  return chip_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ChipIdsResponse::mutable_chip_ids() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.chip.ChipIdsResponse.chip_ids)
  return &chip_ids_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace chip
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fchip_2eproto

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/dashboard.proto

#include "frontend/proto/dashboard.pb.h"
#include "frontend/proto/dashboard.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace dashboard {

static const char* DashboardService_method_names[] = {
  "/carbon.frontend.dashboard.DashboardService/ToggleRow",
  "/carbon.frontend.dashboard.DashboardService/ToggleLasers",
  "/carbon.frontend.dashboard.DashboardService/GetNextDashboardState",
  "/carbon.frontend.dashboard.DashboardService/GetCropModelOptions",
  "/carbon.frontend.dashboard.DashboardService/SetCropModel",
  "/carbon.frontend.dashboard.DashboardService/GetNextWeedingVelocity",
  "/carbon.frontend.dashboard.DashboardService/SetTargetingState",
  "/carbon.frontend.dashboard.DashboardService/SetRowSpacing",
  "/carbon.frontend.dashboard.DashboardService/SetCruiseEnabled",
};

std::unique_ptr< DashboardService::Stub> DashboardService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< DashboardService::Stub> stub(new DashboardService::Stub(channel, options));
  return stub;
}

DashboardService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_ToggleRow_(DashboardService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ToggleLasers_(DashboardService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextDashboardState_(DashboardService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetCropModelOptions_(DashboardService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetCropModel_(DashboardService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextWeedingVelocity_(DashboardService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetTargetingState_(DashboardService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetRowSpacing_(DashboardService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetCruiseEnabled_(DashboardService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status DashboardService::Stub::ToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::dashboard::RowId, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ToggleRow_, context, request, response);
}

void DashboardService::Stub::async::ToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::dashboard::RowId, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ToggleRow_, context, request, response, std::move(f));
}

void DashboardService::Stub::async::ToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ToggleRow_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::PrepareAsyncToggleRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::RowId, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ToggleRow_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::AsyncToggleRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncToggleRowRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DashboardService::Stub::ToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ToggleLasers_, context, request, response);
}

void DashboardService::Stub::async::ToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ToggleLasers_, context, request, response, std::move(f));
}

void DashboardService::Stub::async::ToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ToggleLasers_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::PrepareAsyncToggleLasersRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ToggleLasers_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::AsyncToggleLasersRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncToggleLasersRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DashboardService::Stub::GetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::dashboard::DashboardStateMessage* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::DashboardStateMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextDashboardState_, context, request, response);
}

void DashboardService::Stub::async::GetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::DashboardStateMessage* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::DashboardStateMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextDashboardState_, context, request, response, std::move(f));
}

void DashboardService::Stub::async::GetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::DashboardStateMessage* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextDashboardState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::DashboardStateMessage>* DashboardService::Stub::PrepareAsyncGetNextDashboardStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::dashboard::DashboardStateMessage, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextDashboardState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::DashboardStateMessage>* DashboardService::Stub::AsyncGetNextDashboardStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextDashboardStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DashboardService::Stub::GetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::dashboard::CropModelOptions* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::CropModelOptions, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCropModelOptions_, context, request, response);
}

void DashboardService::Stub::async::GetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::dashboard::CropModelOptions* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::CropModelOptions, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCropModelOptions_, context, request, response, std::move(f));
}

void DashboardService::Stub::async::GetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::dashboard::CropModelOptions* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCropModelOptions_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::CropModelOptions>* DashboardService::Stub::PrepareAsyncGetCropModelOptionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::dashboard::CropModelOptions, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCropModelOptions_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::CropModelOptions>* DashboardService::Stub::AsyncGetCropModelOptionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCropModelOptionsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DashboardService::Stub::SetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::dashboard::CropModel, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetCropModel_, context, request, response);
}

void DashboardService::Stub::async::SetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::dashboard::CropModel, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCropModel_, context, request, response, std::move(f));
}

void DashboardService::Stub::async::SetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCropModel_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::PrepareAsyncSetCropModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::CropModel, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetCropModel_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::AsyncSetCropModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetCropModelRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DashboardService::Stub::GetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::dashboard::WeedingVelocity* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::WeedingVelocity, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextWeedingVelocity_, context, request, response);
}

void DashboardService::Stub::async::GetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::WeedingVelocity* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::WeedingVelocity, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextWeedingVelocity_, context, request, response, std::move(f));
}

void DashboardService::Stub::async::GetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::WeedingVelocity* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextWeedingVelocity_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::WeedingVelocity>* DashboardService::Stub::PrepareAsyncGetNextWeedingVelocityRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::dashboard::WeedingVelocity, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextWeedingVelocity_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::WeedingVelocity>* DashboardService::Stub::AsyncGetNextWeedingVelocityRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextWeedingVelocityRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DashboardService::Stub::SetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::dashboard::TargetingState, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetTargetingState_, context, request, response);
}

void DashboardService::Stub::async::SetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::dashboard::TargetingState, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetTargetingState_, context, request, response, std::move(f));
}

void DashboardService::Stub::async::SetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetTargetingState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::PrepareAsyncSetTargetingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::TargetingState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetTargetingState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::AsyncSetTargetingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetTargetingStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DashboardService::Stub::SetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::dashboard::RowSpacing, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetRowSpacing_, context, request, response);
}

void DashboardService::Stub::async::SetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::dashboard::RowSpacing, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetRowSpacing_, context, request, response, std::move(f));
}

void DashboardService::Stub::async::SetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetRowSpacing_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::PrepareAsyncSetRowSpacingRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::RowSpacing, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetRowSpacing_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::AsyncSetRowSpacingRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetRowSpacingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DashboardService::Stub::SetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::dashboard::CruiseEnable, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetCruiseEnabled_, context, request, response);
}

void DashboardService::Stub::async::SetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::dashboard::CruiseEnable, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCruiseEnabled_, context, request, response, std::move(f));
}

void DashboardService::Stub::async::SetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCruiseEnabled_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::PrepareAsyncSetCruiseEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::CruiseEnable, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetCruiseEnabled_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DashboardService::Stub::AsyncSetCruiseEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetCruiseEnabledRaw(context, request, cq);
  result->StartCall();
  return result;
}

DashboardService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DashboardService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DashboardService::Service, ::carbon::frontend::dashboard::RowId, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DashboardService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::dashboard::RowId* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ToggleRow(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DashboardService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DashboardService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DashboardService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ToggleLasers(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DashboardService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DashboardService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::DashboardStateMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DashboardService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::dashboard::DashboardStateMessage* resp) {
               return service->GetNextDashboardState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DashboardService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DashboardService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::CropModelOptions, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DashboardService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::dashboard::CropModelOptions* resp) {
               return service->GetCropModelOptions(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DashboardService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DashboardService::Service, ::carbon::frontend::dashboard::CropModel, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DashboardService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::dashboard::CropModel* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetCropModel(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DashboardService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DashboardService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::WeedingVelocity, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DashboardService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::dashboard::WeedingVelocity* resp) {
               return service->GetNextWeedingVelocity(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DashboardService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DashboardService::Service, ::carbon::frontend::dashboard::TargetingState, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DashboardService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::dashboard::TargetingState* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetTargetingState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DashboardService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DashboardService::Service, ::carbon::frontend::dashboard::RowSpacing, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DashboardService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::dashboard::RowSpacing* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetRowSpacing(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DashboardService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DashboardService::Service, ::carbon::frontend::dashboard::CruiseEnable, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DashboardService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::dashboard::CruiseEnable* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetCruiseEnabled(ctx, req, resp);
             }, this)));
}

DashboardService::Service::~Service() {
}

::grpc::Status DashboardService::Service::ToggleRow(::grpc::ServerContext* context, const ::carbon::frontend::dashboard::RowId* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DashboardService::Service::ToggleLasers(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DashboardService::Service::GetNextDashboardState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::DashboardStateMessage* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DashboardService::Service::GetCropModelOptions(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::dashboard::CropModelOptions* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DashboardService::Service::SetCropModel(::grpc::ServerContext* context, const ::carbon::frontend::dashboard::CropModel* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DashboardService::Service::GetNextWeedingVelocity(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::WeedingVelocity* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DashboardService::Service::SetTargetingState(::grpc::ServerContext* context, const ::carbon::frontend::dashboard::TargetingState* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DashboardService::Service::SetRowSpacing(::grpc::ServerContext* context, const ::carbon::frontend::dashboard::RowSpacing* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DashboardService::Service::SetCruiseEnabled(::grpc::ServerContext* context, const ::carbon::frontend::dashboard::CruiseEnable* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace dashboard


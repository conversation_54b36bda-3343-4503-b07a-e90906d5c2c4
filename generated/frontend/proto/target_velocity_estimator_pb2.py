# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/target_velocity_estimator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2
from generated.proto.target_velocity_estimator import target_velocity_estimator_pb2 as proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/target_velocity_estimator.proto',
  package='carbon.frontend.target_velocity_estimator',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n.frontend/proto/target_velocity_estimator.proto\x12)carbon.frontend.target_velocity_estimator\x1a\x19\x66rontend/proto/util.proto\x1a?proto/target_velocity_estimator/target_velocity_estimator.proto\"\x9d\x01\n#GetNextAvailableTVEProfilesResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12I\n\x08profiles\x18\x02 \x03(\x0b\x32\x37.carbon.aimbot.target_velocity_estimator.ProfileDetails\"\x94\x01\n\x1fGetNextActiveTVEProfileResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x44\n\x07profile\x18\x02 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfile\"#\n\x15LoadTVEProfileRequest\x12\n\n\x02id\x18\x01 \x01(\t\"^\n\x16LoadTVEProfileResponse\x12\x44\n\x07profile\x18\x01 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfile\"q\n\x15SaveTVEProfileRequest\x12\x44\n\x07profile\x18\x01 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfile\x12\x12\n\nset_active\x18\x02 \x01(\x08\"$\n\x16SaveTVEProfileResponse\x12\n\n\x02id\x18\x01 \x01(\t\"(\n\x1aSetActiveTVEProfileRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\x1d\n\x1bSetActiveTVEProfileResponse\"<\n\x17\x44\x65leteTVEProfileRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x15\n\rnew_active_id\x18\x02 \x01(\t\"\x1a\n\x18\x44\x65leteTVEProfileResponse2\x96\x07\n\x1eTargetVelocityEstimatorService\x12\x8b\x01\n\x18GetNextAvailableProfiles\x12\x1f.carbon.frontend.util.Timestamp\x1aN.carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse\x12\x83\x01\n\x14GetNextActiveProfile\x12\x1f.carbon.frontend.util.Timestamp\x1aJ.carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse\x12\x92\x01\n\x0bLoadProfile\<EMAIL>.target_velocity_estimator.LoadTVEProfileRequest\x1a\x41.carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse\x12\x92\x01\n\x0bSaveProfile\<EMAIL>.target_velocity_estimator.SaveTVEProfileRequest\x1a\x41.carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse\x12\x9a\x01\n\tSetActive\x12\x45.carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest\x1a\x46.carbon.frontend.target_velocity_estimator.SetActiveTVEProfileResponse\x12\x98\x01\n\rDeleteProfile\x12\x42.carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest\x1a\x43.carbon.frontend.target_velocity_estimator.DeleteTVEProfileResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2.DESCRIPTOR,])




_GETNEXTAVAILABLETVEPROFILESRESPONSE = _descriptor.Descriptor(
  name='GetNextAvailableTVEProfilesResponse',
  full_name='carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='profiles', full_name='carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse.profiles', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=186,
  serialized_end=343,
)


_GETNEXTACTIVETVEPROFILERESPONSE = _descriptor.Descriptor(
  name='GetNextActiveTVEProfileResponse',
  full_name='carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='profile', full_name='carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse.profile', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=346,
  serialized_end=494,
)


_LOADTVEPROFILEREQUEST = _descriptor.Descriptor(
  name='LoadTVEProfileRequest',
  full_name='carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=496,
  serialized_end=531,
)


_LOADTVEPROFILERESPONSE = _descriptor.Descriptor(
  name='LoadTVEProfileResponse',
  full_name='carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='profile', full_name='carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse.profile', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=533,
  serialized_end=627,
)


_SAVETVEPROFILEREQUEST = _descriptor.Descriptor(
  name='SaveTVEProfileRequest',
  full_name='carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='profile', full_name='carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest.profile', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='set_active', full_name='carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest.set_active', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=629,
  serialized_end=742,
)


_SAVETVEPROFILERESPONSE = _descriptor.Descriptor(
  name='SaveTVEProfileResponse',
  full_name='carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=744,
  serialized_end=780,
)


_SETACTIVETVEPROFILEREQUEST = _descriptor.Descriptor(
  name='SetActiveTVEProfileRequest',
  full_name='carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=782,
  serialized_end=822,
)


_SETACTIVETVEPROFILERESPONSE = _descriptor.Descriptor(
  name='SetActiveTVEProfileResponse',
  full_name='carbon.frontend.target_velocity_estimator.SetActiveTVEProfileResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=824,
  serialized_end=853,
)


_DELETETVEPROFILEREQUEST = _descriptor.Descriptor(
  name='DeleteTVEProfileRequest',
  full_name='carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='new_active_id', full_name='carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.new_active_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=855,
  serialized_end=915,
)


_DELETETVEPROFILERESPONSE = _descriptor.Descriptor(
  name='DeleteTVEProfileResponse',
  full_name='carbon.frontend.target_velocity_estimator.DeleteTVEProfileResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=917,
  serialized_end=943,
)

_GETNEXTAVAILABLETVEPROFILESRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTAVAILABLETVEPROFILESRESPONSE.fields_by_name['profiles'].message_type = proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2._PROFILEDETAILS
_GETNEXTACTIVETVEPROFILERESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTACTIVETVEPROFILERESPONSE.fields_by_name['profile'].message_type = proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2._TVEPROFILE
_LOADTVEPROFILERESPONSE.fields_by_name['profile'].message_type = proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2._TVEPROFILE
_SAVETVEPROFILEREQUEST.fields_by_name['profile'].message_type = proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2._TVEPROFILE
DESCRIPTOR.message_types_by_name['GetNextAvailableTVEProfilesResponse'] = _GETNEXTAVAILABLETVEPROFILESRESPONSE
DESCRIPTOR.message_types_by_name['GetNextActiveTVEProfileResponse'] = _GETNEXTACTIVETVEPROFILERESPONSE
DESCRIPTOR.message_types_by_name['LoadTVEProfileRequest'] = _LOADTVEPROFILEREQUEST
DESCRIPTOR.message_types_by_name['LoadTVEProfileResponse'] = _LOADTVEPROFILERESPONSE
DESCRIPTOR.message_types_by_name['SaveTVEProfileRequest'] = _SAVETVEPROFILEREQUEST
DESCRIPTOR.message_types_by_name['SaveTVEProfileResponse'] = _SAVETVEPROFILERESPONSE
DESCRIPTOR.message_types_by_name['SetActiveTVEProfileRequest'] = _SETACTIVETVEPROFILEREQUEST
DESCRIPTOR.message_types_by_name['SetActiveTVEProfileResponse'] = _SETACTIVETVEPROFILERESPONSE
DESCRIPTOR.message_types_by_name['DeleteTVEProfileRequest'] = _DELETETVEPROFILEREQUEST
DESCRIPTOR.message_types_by_name['DeleteTVEProfileResponse'] = _DELETETVEPROFILERESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetNextAvailableTVEProfilesResponse = _reflection.GeneratedProtocolMessageType('GetNextAvailableTVEProfilesResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTAVAILABLETVEPROFILESRESPONSE,
  '__module__' : 'frontend.proto.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse)
  })
_sym_db.RegisterMessage(GetNextAvailableTVEProfilesResponse)

GetNextActiveTVEProfileResponse = _reflection.GeneratedProtocolMessageType('GetNextActiveTVEProfileResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTACTIVETVEPROFILERESPONSE,
  '__module__' : 'frontend.proto.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse)
  })
_sym_db.RegisterMessage(GetNextActiveTVEProfileResponse)

LoadTVEProfileRequest = _reflection.GeneratedProtocolMessageType('LoadTVEProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _LOADTVEPROFILEREQUEST,
  '__module__' : 'frontend.proto.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest)
  })
_sym_db.RegisterMessage(LoadTVEProfileRequest)

LoadTVEProfileResponse = _reflection.GeneratedProtocolMessageType('LoadTVEProfileResponse', (_message.Message,), {
  'DESCRIPTOR' : _LOADTVEPROFILERESPONSE,
  '__module__' : 'frontend.proto.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse)
  })
_sym_db.RegisterMessage(LoadTVEProfileResponse)

SaveTVEProfileRequest = _reflection.GeneratedProtocolMessageType('SaveTVEProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _SAVETVEPROFILEREQUEST,
  '__module__' : 'frontend.proto.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest)
  })
_sym_db.RegisterMessage(SaveTVEProfileRequest)

SaveTVEProfileResponse = _reflection.GeneratedProtocolMessageType('SaveTVEProfileResponse', (_message.Message,), {
  'DESCRIPTOR' : _SAVETVEPROFILERESPONSE,
  '__module__' : 'frontend.proto.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse)
  })
_sym_db.RegisterMessage(SaveTVEProfileResponse)

SetActiveTVEProfileRequest = _reflection.GeneratedProtocolMessageType('SetActiveTVEProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETACTIVETVEPROFILEREQUEST,
  '__module__' : 'frontend.proto.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest)
  })
_sym_db.RegisterMessage(SetActiveTVEProfileRequest)

SetActiveTVEProfileResponse = _reflection.GeneratedProtocolMessageType('SetActiveTVEProfileResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETACTIVETVEPROFILERESPONSE,
  '__module__' : 'frontend.proto.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileResponse)
  })
_sym_db.RegisterMessage(SetActiveTVEProfileResponse)

DeleteTVEProfileRequest = _reflection.GeneratedProtocolMessageType('DeleteTVEProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETETVEPROFILEREQUEST,
  '__module__' : 'frontend.proto.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest)
  })
_sym_db.RegisterMessage(DeleteTVEProfileRequest)

DeleteTVEProfileResponse = _reflection.GeneratedProtocolMessageType('DeleteTVEProfileResponse', (_message.Message,), {
  'DESCRIPTOR' : _DELETETVEPROFILERESPONSE,
  '__module__' : 'frontend.proto.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.target_velocity_estimator.DeleteTVEProfileResponse)
  })
_sym_db.RegisterMessage(DeleteTVEProfileResponse)


DESCRIPTOR._options = None

_TARGETVELOCITYESTIMATORSERVICE = _descriptor.ServiceDescriptor(
  name='TargetVelocityEstimatorService',
  full_name='carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=946,
  serialized_end=1864,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextAvailableProfiles',
    full_name='carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.GetNextAvailableProfiles',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTAVAILABLETVEPROFILESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextActiveProfile',
    full_name='carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.GetNextActiveProfile',
    index=1,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTACTIVETVEPROFILERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LoadProfile',
    full_name='carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.LoadProfile',
    index=2,
    containing_service=None,
    input_type=_LOADTVEPROFILEREQUEST,
    output_type=_LOADTVEPROFILERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SaveProfile',
    full_name='carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.SaveProfile',
    index=3,
    containing_service=None,
    input_type=_SAVETVEPROFILEREQUEST,
    output_type=_SAVETVEPROFILERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetActive',
    full_name='carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.SetActive',
    index=4,
    containing_service=None,
    input_type=_SETACTIVETVEPROFILEREQUEST,
    output_type=_SETACTIVETVEPROFILERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteProfile',
    full_name='carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService.DeleteProfile',
    index=5,
    containing_service=None,
    input_type=_DELETETVEPROFILEREQUEST,
    output_type=_DELETETVEPROFILERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_TARGETVELOCITYESTIMATORSERVICE)

DESCRIPTOR.services_by_name['TargetVelocityEstimatorService'] = _TARGETVELOCITYESTIMATORSERVICE

# @@protoc_insertion_point(module_scope)

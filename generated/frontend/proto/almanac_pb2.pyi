"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.proto.almanac.almanac_pb2 import (
    AlmanacConfig as proto___almanac___almanac_pb2___AlmanacConfig,
    DiscriminatorConfig as proto___almanac___almanac_pb2___DiscriminatorConfig,
    ModelinatorConfig as proto___almanac___almanac_pb2___ModelinatorConfig,
)

from typing import (
    Mapping as typing___Mapping,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class GetConfigDataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class CropCategoryNamesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___CropCategoryNamesEntry = CropCategoryNamesEntry

    class WeedCategoryNamesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___WeedCategoryNamesEntry = WeedCategoryNamesEntry

    num_size_categories: builtin___int = ...

    @property
    def crop_category_names(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    @property
    def weed_category_names(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    def __init__(self,
        *,
        num_size_categories : typing___Optional[builtin___int] = None,
        crop_category_names : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        weed_category_names : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_category_names",b"crop_category_names",u"num_size_categories",b"num_size_categories",u"weed_category_names",b"weed_category_names"]) -> None: ...
type___GetConfigDataResponse = GetConfigDataResponse

class LoadAlmanacConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___LoadAlmanacConfigRequest = LoadAlmanacConfigRequest

class LoadAlmanacConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> proto___almanac___almanac_pb2___AlmanacConfig: ...

    def __init__(self,
        *,
        config : typing___Optional[proto___almanac___almanac_pb2___AlmanacConfig] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___LoadAlmanacConfigResponse = LoadAlmanacConfigResponse

class SaveAlmanacConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    set_active: builtin___bool = ...

    @property
    def config(self) -> proto___almanac___almanac_pb2___AlmanacConfig: ...

    def __init__(self,
        *,
        config : typing___Optional[proto___almanac___almanac_pb2___AlmanacConfig] = None,
        set_active : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config",u"set_active",b"set_active"]) -> None: ...
type___SaveAlmanacConfigRequest = SaveAlmanacConfigRequest

class SaveAlmanacConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___SaveAlmanacConfigResponse = SaveAlmanacConfigResponse

class SetActiveAlmanacConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___SetActiveAlmanacConfigRequest = SetActiveAlmanacConfigRequest

class DeleteAlmanacConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    new_active_id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        new_active_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"new_active_id",b"new_active_id"]) -> None: ...
type___DeleteAlmanacConfigRequest = DeleteAlmanacConfigRequest

class GetNextAlmanacConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class AvailableEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___AvailableEntry = AvailableEntry

    active: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def available(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        active : typing___Optional[typing___Text] = None,
        available : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active",b"active",u"available",b"available",u"ts",b"ts"]) -> None: ...
type___GetNextAlmanacConfigResponse = GetNextAlmanacConfigResponse

class LoadDiscriminatorConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___LoadDiscriminatorConfigRequest = LoadDiscriminatorConfigRequest

class LoadDiscriminatorConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> proto___almanac___almanac_pb2___DiscriminatorConfig: ...

    def __init__(self,
        *,
        config : typing___Optional[proto___almanac___almanac_pb2___DiscriminatorConfig] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___LoadDiscriminatorConfigResponse = LoadDiscriminatorConfigResponse

class SaveDiscriminatorConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    associate_with_active_crop: builtin___bool = ...

    @property
    def config(self) -> proto___almanac___almanac_pb2___DiscriminatorConfig: ...

    def __init__(self,
        *,
        config : typing___Optional[proto___almanac___almanac_pb2___DiscriminatorConfig] = None,
        associate_with_active_crop : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"associate_with_active_crop",b"associate_with_active_crop",u"config",b"config"]) -> None: ...
type___SaveDiscriminatorConfigRequest = SaveDiscriminatorConfigRequest

class SaveDiscriminatorConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___SaveDiscriminatorConfigResponse = SaveDiscriminatorConfigResponse

class SetActiveDiscriminatorConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    crop_id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_crop_id",b"_crop_id",u"crop_id",b"crop_id"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_crop_id",b"_crop_id",u"crop_id",b"crop_id",u"id",b"id"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_crop_id",b"_crop_id"]) -> typing_extensions___Literal["crop_id"]: ...
type___SetActiveDiscriminatorConfigRequest = SetActiveDiscriminatorConfigRequest

class DeleteDiscriminatorConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___DeleteDiscriminatorConfigRequest = DeleteDiscriminatorConfigRequest

class GetNextDiscriminatorConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class AvailableEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___AvailableEntry = AvailableEntry

    active: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def available(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        active : typing___Optional[typing___Text] = None,
        available : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active",b"active",u"available",b"available",u"ts",b"ts"]) -> None: ...
type___GetNextDiscriminatorConfigResponse = GetNextDiscriminatorConfigResponse

class GetNextModelinatorConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def config(self) -> proto___almanac___almanac_pb2___ModelinatorConfig: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        config : typing___Optional[proto___almanac___almanac_pb2___ModelinatorConfig] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config",u"ts",b"ts"]) -> None: ...
type___GetNextModelinatorConfigResponse = GetNextModelinatorConfigResponse

class SaveModelinatorConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> proto___almanac___almanac_pb2___ModelinatorConfig: ...

    def __init__(self,
        *,
        config : typing___Optional[proto___almanac___almanac_pb2___ModelinatorConfig] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___SaveModelinatorConfigRequest = SaveModelinatorConfigRequest

class FetchModelinatorConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model_id: typing___Text = ...
    crop_id: typing___Text = ...

    def __init__(self,
        *,
        model_id : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_id",b"crop_id",u"model_id",b"model_id"]) -> None: ...
type___FetchModelinatorConfigRequest = FetchModelinatorConfigRequest

class FetchModelinatorConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> proto___almanac___almanac_pb2___ModelinatorConfig: ...

    def __init__(self,
        *,
        config : typing___Optional[proto___almanac___almanac_pb2___ModelinatorConfig] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___FetchModelinatorConfigResponse = FetchModelinatorConfigResponse

class ResetModelinatorConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ResetModelinatorConfigRequest = ResetModelinatorConfigRequest

class GetNextConfigDataRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lang: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        lang : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lang",b"lang",u"ts",b"ts"]) -> None: ...
type___GetNextConfigDataRequest = GetNextConfigDataRequest

class GetNextConfigDataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class CropCategoryNamesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___CropCategoryNamesEntry = CropCategoryNamesEntry

    class WeedCategoryNamesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___WeedCategoryNamesEntry = WeedCategoryNamesEntry

    num_size_categories: builtin___int = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def crop_category_names(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    @property
    def weed_category_names(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        num_size_categories : typing___Optional[builtin___int] = None,
        crop_category_names : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        weed_category_names : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_category_names",b"crop_category_names",u"num_size_categories",b"num_size_categories",u"ts",b"ts",u"weed_category_names",b"weed_category_names"]) -> None: ...
type___GetNextConfigDataResponse = GetNextConfigDataResponse

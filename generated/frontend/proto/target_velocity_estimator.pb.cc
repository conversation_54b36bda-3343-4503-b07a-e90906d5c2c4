// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/target_velocity_estimator.proto

#include "frontend/proto/target_velocity_estimator.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace target_velocity_estimator {
constexpr GetNextAvailableTVEProfilesResponse::GetNextAvailableTVEProfilesResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : profiles_()
  , ts_(nullptr){}
struct GetNextAvailableTVEProfilesResponseDefaultTypeInternal {
  constexpr GetNextAvailableTVEProfilesResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextAvailableTVEProfilesResponseDefaultTypeInternal() {}
  union {
    GetNextAvailableTVEProfilesResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextAvailableTVEProfilesResponseDefaultTypeInternal _GetNextAvailableTVEProfilesResponse_default_instance_;
constexpr GetNextActiveTVEProfileResponse::GetNextActiveTVEProfileResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , profile_(nullptr){}
struct GetNextActiveTVEProfileResponseDefaultTypeInternal {
  constexpr GetNextActiveTVEProfileResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextActiveTVEProfileResponseDefaultTypeInternal() {}
  union {
    GetNextActiveTVEProfileResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextActiveTVEProfileResponseDefaultTypeInternal _GetNextActiveTVEProfileResponse_default_instance_;
constexpr LoadTVEProfileRequest::LoadTVEProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct LoadTVEProfileRequestDefaultTypeInternal {
  constexpr LoadTVEProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LoadTVEProfileRequestDefaultTypeInternal() {}
  union {
    LoadTVEProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LoadTVEProfileRequestDefaultTypeInternal _LoadTVEProfileRequest_default_instance_;
constexpr LoadTVEProfileResponse::LoadTVEProfileResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : profile_(nullptr){}
struct LoadTVEProfileResponseDefaultTypeInternal {
  constexpr LoadTVEProfileResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LoadTVEProfileResponseDefaultTypeInternal() {}
  union {
    LoadTVEProfileResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LoadTVEProfileResponseDefaultTypeInternal _LoadTVEProfileResponse_default_instance_;
constexpr SaveTVEProfileRequest::SaveTVEProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : profile_(nullptr)
  , set_active_(false){}
struct SaveTVEProfileRequestDefaultTypeInternal {
  constexpr SaveTVEProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SaveTVEProfileRequestDefaultTypeInternal() {}
  union {
    SaveTVEProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SaveTVEProfileRequestDefaultTypeInternal _SaveTVEProfileRequest_default_instance_;
constexpr SaveTVEProfileResponse::SaveTVEProfileResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SaveTVEProfileResponseDefaultTypeInternal {
  constexpr SaveTVEProfileResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SaveTVEProfileResponseDefaultTypeInternal() {}
  union {
    SaveTVEProfileResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SaveTVEProfileResponseDefaultTypeInternal _SaveTVEProfileResponse_default_instance_;
constexpr SetActiveTVEProfileRequest::SetActiveTVEProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SetActiveTVEProfileRequestDefaultTypeInternal {
  constexpr SetActiveTVEProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetActiveTVEProfileRequestDefaultTypeInternal() {}
  union {
    SetActiveTVEProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetActiveTVEProfileRequestDefaultTypeInternal _SetActiveTVEProfileRequest_default_instance_;
constexpr SetActiveTVEProfileResponse::SetActiveTVEProfileResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetActiveTVEProfileResponseDefaultTypeInternal {
  constexpr SetActiveTVEProfileResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetActiveTVEProfileResponseDefaultTypeInternal() {}
  union {
    SetActiveTVEProfileResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetActiveTVEProfileResponseDefaultTypeInternal _SetActiveTVEProfileResponse_default_instance_;
constexpr DeleteTVEProfileRequest::DeleteTVEProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , new_active_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DeleteTVEProfileRequestDefaultTypeInternal {
  constexpr DeleteTVEProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeleteTVEProfileRequestDefaultTypeInternal() {}
  union {
    DeleteTVEProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeleteTVEProfileRequestDefaultTypeInternal _DeleteTVEProfileRequest_default_instance_;
constexpr DeleteTVEProfileResponse::DeleteTVEProfileResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct DeleteTVEProfileResponseDefaultTypeInternal {
  constexpr DeleteTVEProfileResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeleteTVEProfileResponseDefaultTypeInternal() {}
  union {
    DeleteTVEProfileResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeleteTVEProfileResponseDefaultTypeInternal _DeleteTVEProfileResponse_default_instance_;
}  // namespace target_velocity_estimator
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[10];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse, profiles_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse, profile_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse, profile_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, profile_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, set_active_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, new_active_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse)},
  { 8, -1, -1, sizeof(::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse)},
  { 16, -1, -1, sizeof(::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest)},
  { 23, -1, -1, sizeof(::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse)},
  { 30, -1, -1, sizeof(::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest)},
  { 38, -1, -1, sizeof(::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse)},
  { 45, -1, -1, sizeof(::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest)},
  { 52, -1, -1, sizeof(::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse)},
  { 58, -1, -1, sizeof(::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest)},
  { 66, -1, -1, sizeof(::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::target_velocity_estimator::_GetNextAvailableTVEProfilesResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::target_velocity_estimator::_GetNextActiveTVEProfileResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::target_velocity_estimator::_LoadTVEProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::target_velocity_estimator::_LoadTVEProfileResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::target_velocity_estimator::_SaveTVEProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::target_velocity_estimator::_SaveTVEProfileResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::target_velocity_estimator::_SetActiveTVEProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::target_velocity_estimator::_SetActiveTVEProfileResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::target_velocity_estimator::_DeleteTVEProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::target_velocity_estimator::_DeleteTVEProfileResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n.frontend/proto/target_velocity_estimat"
  "or.proto\022)carbon.frontend.target_velocit"
  "y_estimator\032\031frontend/proto/util.proto\032\?"
  "proto/target_velocity_estimator/target_v"
  "elocity_estimator.proto\"\235\001\n#GetNextAvail"
  "ableTVEProfilesResponse\022+\n\002ts\030\001 \001(\0132\037.ca"
  "rbon.frontend.util.Timestamp\022I\n\010profiles"
  "\030\002 \003(\01327.carbon.aimbot.target_velocity_e"
  "stimator.ProfileDetails\"\224\001\n\037GetNextActiv"
  "eTVEProfileResponse\022+\n\002ts\030\001 \001(\0132\037.carbon"
  ".frontend.util.Timestamp\022D\n\007profile\030\002 \001("
  "\01323.carbon.aimbot.target_velocity_estima"
  "tor.TVEProfile\"#\n\025LoadTVEProfileRequest\022"
  "\n\n\002id\030\001 \001(\t\"^\n\026LoadTVEProfileResponse\022D\n"
  "\007profile\030\001 \001(\01323.carbon.aimbot.target_ve"
  "locity_estimator.TVEProfile\"q\n\025SaveTVEPr"
  "ofileRequest\022D\n\007profile\030\001 \001(\01323.carbon.a"
  "imbot.target_velocity_estimator.TVEProfi"
  "le\022\022\n\nset_active\030\002 \001(\010\"$\n\026SaveTVEProfile"
  "Response\022\n\n\002id\030\001 \001(\t\"(\n\032SetActiveTVEProf"
  "ileRequest\022\n\n\002id\030\001 \001(\t\"\035\n\033SetActiveTVEPr"
  "ofileResponse\"<\n\027DeleteTVEProfileRequest"
  "\022\n\n\002id\030\001 \001(\t\022\025\n\rnew_active_id\030\002 \001(\t\"\032\n\030D"
  "eleteTVEProfileResponse2\226\007\n\036TargetVeloci"
  "tyEstimatorService\022\213\001\n\030GetNextAvailableP"
  "rofiles\022\037.carbon.frontend.util.Timestamp"
  "\032N.carbon.frontend.target_velocity_estim"
  "ator.GetNextAvailableTVEProfilesResponse"
  "\022\203\001\n\024GetNextActiveProfile\022\037.carbon.front"
  "end.util.Timestamp\032J.carbon.frontend.tar"
  "get_velocity_estimator.GetNextActiveTVEP"
  "rofileResponse\022\222\001\n\013LoadProfile\022@.carbon."
  "frontend.target_velocity_estimator.LoadT"
  "VEProfileRequest\032A.carbon.frontend.targe"
  "t_velocity_estimator.LoadTVEProfileRespo"
  "nse\022\222\001\n\013SaveProfile\<EMAIL>"
  "rget_velocity_estimator.SaveTVEProfileRe"
  "quest\032A.carbon.frontend.target_velocity_"
  "estimator.SaveTVEProfileResponse\022\232\001\n\tSet"
  "Active\022E.carbon.frontend.target_velocity"
  "_estimator.SetActiveTVEProfileRequest\032F."
  "carbon.frontend.target_velocity_estimato"
  "r.SetActiveTVEProfileResponse\022\230\001\n\rDelete"
  "Profile\022B.carbon.frontend.target_velocit"
  "y_estimator.DeleteTVEProfileRequest\032C.ca"
  "rbon.frontend.target_velocity_estimator."
  "DeleteTVEProfileResponseB\020Z\016proto/fronte"
  "ndb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
  &::descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto = {
  false, false, 1890, descriptor_table_protodef_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto, "frontend/proto/target_velocity_estimator.proto", 
  &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once, descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_deps, 2, 10,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto, file_level_enum_descriptors_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto, file_level_service_descriptors_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto(&descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto);
namespace carbon {
namespace frontend {
namespace target_velocity_estimator {

// ===================================================================

class GetNextAvailableTVEProfilesResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextAvailableTVEProfilesResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextAvailableTVEProfilesResponse::_Internal::ts(const GetNextAvailableTVEProfilesResponse* msg) {
  return *msg->ts_;
}
void GetNextAvailableTVEProfilesResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
void GetNextAvailableTVEProfilesResponse::clear_profiles() {
  profiles_.Clear();
}
GetNextAvailableTVEProfilesResponse::GetNextAvailableTVEProfilesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  profiles_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse)
}
GetNextAvailableTVEProfilesResponse::GetNextAvailableTVEProfilesResponse(const GetNextAvailableTVEProfilesResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      profiles_(from.profiles_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse)
}

inline void GetNextAvailableTVEProfilesResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextAvailableTVEProfilesResponse::~GetNextAvailableTVEProfilesResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextAvailableTVEProfilesResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextAvailableTVEProfilesResponse::ArenaDtor(void* object) {
  GetNextAvailableTVEProfilesResponse* _this = reinterpret_cast< GetNextAvailableTVEProfilesResponse* >(object);
  (void)_this;
}
void GetNextAvailableTVEProfilesResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextAvailableTVEProfilesResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextAvailableTVEProfilesResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  profiles_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextAvailableTVEProfilesResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.aimbot.target_velocity_estimator.ProfileDetails profiles = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_profiles(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextAvailableTVEProfilesResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.aimbot.target_velocity_estimator.ProfileDetails profiles = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_profiles_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_profiles(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse)
  return target;
}

size_t GetNextAvailableTVEProfilesResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.aimbot.target_velocity_estimator.ProfileDetails profiles = 2;
  total_size += 1UL * this->_internal_profiles_size();
  for (const auto& msg : this->profiles_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextAvailableTVEProfilesResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextAvailableTVEProfilesResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextAvailableTVEProfilesResponse::GetClassData() const { return &_class_data_; }

void GetNextAvailableTVEProfilesResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextAvailableTVEProfilesResponse *>(to)->MergeFrom(
      static_cast<const GetNextAvailableTVEProfilesResponse &>(from));
}


void GetNextAvailableTVEProfilesResponse::MergeFrom(const GetNextAvailableTVEProfilesResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  profiles_.MergeFrom(from.profiles_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextAvailableTVEProfilesResponse::CopyFrom(const GetNextAvailableTVEProfilesResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.target_velocity_estimator.GetNextAvailableTVEProfilesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextAvailableTVEProfilesResponse::IsInitialized() const {
  return true;
}

void GetNextAvailableTVEProfilesResponse::InternalSwap(GetNextAvailableTVEProfilesResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  profiles_.InternalSwap(&other->profiles_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextAvailableTVEProfilesResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[0]);
}

// ===================================================================

class GetNextActiveTVEProfileResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextActiveTVEProfileResponse* msg);
  static const ::carbon::aimbot::target_velocity_estimator::TVEProfile& profile(const GetNextActiveTVEProfileResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextActiveTVEProfileResponse::_Internal::ts(const GetNextActiveTVEProfileResponse* msg) {
  return *msg->ts_;
}
const ::carbon::aimbot::target_velocity_estimator::TVEProfile&
GetNextActiveTVEProfileResponse::_Internal::profile(const GetNextActiveTVEProfileResponse* msg) {
  return *msg->profile_;
}
void GetNextActiveTVEProfileResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
void GetNextActiveTVEProfileResponse::clear_profile() {
  if (GetArenaForAllocation() == nullptr && profile_ != nullptr) {
    delete profile_;
  }
  profile_ = nullptr;
}
GetNextActiveTVEProfileResponse::GetNextActiveTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse)
}
GetNextActiveTVEProfileResponse::GetNextActiveTVEProfileResponse(const GetNextActiveTVEProfileResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_profile()) {
    profile_ = new ::carbon::aimbot::target_velocity_estimator::TVEProfile(*from.profile_);
  } else {
    profile_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse)
}

inline void GetNextActiveTVEProfileResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&profile_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(profile_));
}

GetNextActiveTVEProfileResponse::~GetNextActiveTVEProfileResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextActiveTVEProfileResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete profile_;
}

void GetNextActiveTVEProfileResponse::ArenaDtor(void* object) {
  GetNextActiveTVEProfileResponse* _this = reinterpret_cast< GetNextActiveTVEProfileResponse* >(object);
  (void)_this;
}
void GetNextActiveTVEProfileResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextActiveTVEProfileResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextActiveTVEProfileResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && profile_ != nullptr) {
    delete profile_;
  }
  profile_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextActiveTVEProfileResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_profile(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextActiveTVEProfileResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 2;
  if (this->_internal_has_profile()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::profile(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse)
  return target;
}

size_t GetNextActiveTVEProfileResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 2;
  if (this->_internal_has_profile()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *profile_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextActiveTVEProfileResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextActiveTVEProfileResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextActiveTVEProfileResponse::GetClassData() const { return &_class_data_; }

void GetNextActiveTVEProfileResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextActiveTVEProfileResponse *>(to)->MergeFrom(
      static_cast<const GetNextActiveTVEProfileResponse &>(from));
}


void GetNextActiveTVEProfileResponse::MergeFrom(const GetNextActiveTVEProfileResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_profile()) {
    _internal_mutable_profile()->::carbon::aimbot::target_velocity_estimator::TVEProfile::MergeFrom(from._internal_profile());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextActiveTVEProfileResponse::CopyFrom(const GetNextActiveTVEProfileResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.target_velocity_estimator.GetNextActiveTVEProfileResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextActiveTVEProfileResponse::IsInitialized() const {
  return true;
}

void GetNextActiveTVEProfileResponse::InternalSwap(GetNextActiveTVEProfileResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextActiveTVEProfileResponse, profile_)
      + sizeof(GetNextActiveTVEProfileResponse::profile_)
      - PROTOBUF_FIELD_OFFSET(GetNextActiveTVEProfileResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextActiveTVEProfileResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[1]);
}

// ===================================================================

class LoadTVEProfileRequest::_Internal {
 public:
};

LoadTVEProfileRequest::LoadTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest)
}
LoadTVEProfileRequest::LoadTVEProfileRequest(const LoadTVEProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest)
}

inline void LoadTVEProfileRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

LoadTVEProfileRequest::~LoadTVEProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LoadTVEProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void LoadTVEProfileRequest::ArenaDtor(void* object) {
  LoadTVEProfileRequest* _this = reinterpret_cast< LoadTVEProfileRequest* >(object);
  (void)_this;
}
void LoadTVEProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LoadTVEProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LoadTVEProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LoadTVEProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LoadTVEProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest)
  return target;
}

size_t LoadTVEProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LoadTVEProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LoadTVEProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LoadTVEProfileRequest::GetClassData() const { return &_class_data_; }

void LoadTVEProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LoadTVEProfileRequest *>(to)->MergeFrom(
      static_cast<const LoadTVEProfileRequest &>(from));
}


void LoadTVEProfileRequest::MergeFrom(const LoadTVEProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LoadTVEProfileRequest::CopyFrom(const LoadTVEProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.target_velocity_estimator.LoadTVEProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadTVEProfileRequest::IsInitialized() const {
  return true;
}

void LoadTVEProfileRequest::InternalSwap(LoadTVEProfileRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata LoadTVEProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[2]);
}

// ===================================================================

class LoadTVEProfileResponse::_Internal {
 public:
  static const ::carbon::aimbot::target_velocity_estimator::TVEProfile& profile(const LoadTVEProfileResponse* msg);
};

const ::carbon::aimbot::target_velocity_estimator::TVEProfile&
LoadTVEProfileResponse::_Internal::profile(const LoadTVEProfileResponse* msg) {
  return *msg->profile_;
}
void LoadTVEProfileResponse::clear_profile() {
  if (GetArenaForAllocation() == nullptr && profile_ != nullptr) {
    delete profile_;
  }
  profile_ = nullptr;
}
LoadTVEProfileResponse::LoadTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse)
}
LoadTVEProfileResponse::LoadTVEProfileResponse(const LoadTVEProfileResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_profile()) {
    profile_ = new ::carbon::aimbot::target_velocity_estimator::TVEProfile(*from.profile_);
  } else {
    profile_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse)
}

inline void LoadTVEProfileResponse::SharedCtor() {
profile_ = nullptr;
}

LoadTVEProfileResponse::~LoadTVEProfileResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LoadTVEProfileResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete profile_;
}

void LoadTVEProfileResponse::ArenaDtor(void* object) {
  LoadTVEProfileResponse* _this = reinterpret_cast< LoadTVEProfileResponse* >(object);
  (void)_this;
}
void LoadTVEProfileResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LoadTVEProfileResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LoadTVEProfileResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && profile_ != nullptr) {
    delete profile_;
  }
  profile_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LoadTVEProfileResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_profile(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LoadTVEProfileResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
  if (this->_internal_has_profile()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::profile(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse)
  return target;
}

size_t LoadTVEProfileResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
  if (this->_internal_has_profile()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *profile_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LoadTVEProfileResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LoadTVEProfileResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LoadTVEProfileResponse::GetClassData() const { return &_class_data_; }

void LoadTVEProfileResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LoadTVEProfileResponse *>(to)->MergeFrom(
      static_cast<const LoadTVEProfileResponse &>(from));
}


void LoadTVEProfileResponse::MergeFrom(const LoadTVEProfileResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_profile()) {
    _internal_mutable_profile()->::carbon::aimbot::target_velocity_estimator::TVEProfile::MergeFrom(from._internal_profile());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LoadTVEProfileResponse::CopyFrom(const LoadTVEProfileResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.target_velocity_estimator.LoadTVEProfileResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadTVEProfileResponse::IsInitialized() const {
  return true;
}

void LoadTVEProfileResponse::InternalSwap(LoadTVEProfileResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(profile_, other->profile_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LoadTVEProfileResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[3]);
}

// ===================================================================

class SaveTVEProfileRequest::_Internal {
 public:
  static const ::carbon::aimbot::target_velocity_estimator::TVEProfile& profile(const SaveTVEProfileRequest* msg);
};

const ::carbon::aimbot::target_velocity_estimator::TVEProfile&
SaveTVEProfileRequest::_Internal::profile(const SaveTVEProfileRequest* msg) {
  return *msg->profile_;
}
void SaveTVEProfileRequest::clear_profile() {
  if (GetArenaForAllocation() == nullptr && profile_ != nullptr) {
    delete profile_;
  }
  profile_ = nullptr;
}
SaveTVEProfileRequest::SaveTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest)
}
SaveTVEProfileRequest::SaveTVEProfileRequest(const SaveTVEProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_profile()) {
    profile_ = new ::carbon::aimbot::target_velocity_estimator::TVEProfile(*from.profile_);
  } else {
    profile_ = nullptr;
  }
  set_active_ = from.set_active_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest)
}

inline void SaveTVEProfileRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&profile_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&set_active_) -
    reinterpret_cast<char*>(&profile_)) + sizeof(set_active_));
}

SaveTVEProfileRequest::~SaveTVEProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SaveTVEProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete profile_;
}

void SaveTVEProfileRequest::ArenaDtor(void* object) {
  SaveTVEProfileRequest* _this = reinterpret_cast< SaveTVEProfileRequest* >(object);
  (void)_this;
}
void SaveTVEProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SaveTVEProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SaveTVEProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && profile_ != nullptr) {
    delete profile_;
  }
  profile_ = nullptr;
  set_active_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SaveTVEProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_profile(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool set_active = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          set_active_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SaveTVEProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
  if (this->_internal_has_profile()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::profile(this), target, stream);
  }

  // bool set_active = 2;
  if (this->_internal_set_active() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_set_active(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest)
  return target;
}

size_t SaveTVEProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.aimbot.target_velocity_estimator.TVEProfile profile = 1;
  if (this->_internal_has_profile()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *profile_);
  }

  // bool set_active = 2;
  if (this->_internal_set_active() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SaveTVEProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SaveTVEProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SaveTVEProfileRequest::GetClassData() const { return &_class_data_; }

void SaveTVEProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SaveTVEProfileRequest *>(to)->MergeFrom(
      static_cast<const SaveTVEProfileRequest &>(from));
}


void SaveTVEProfileRequest::MergeFrom(const SaveTVEProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_profile()) {
    _internal_mutable_profile()->::carbon::aimbot::target_velocity_estimator::TVEProfile::MergeFrom(from._internal_profile());
  }
  if (from._internal_set_active() != 0) {
    _internal_set_set_active(from._internal_set_active());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SaveTVEProfileRequest::CopyFrom(const SaveTVEProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.target_velocity_estimator.SaveTVEProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SaveTVEProfileRequest::IsInitialized() const {
  return true;
}

void SaveTVEProfileRequest::InternalSwap(SaveTVEProfileRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SaveTVEProfileRequest, set_active_)
      + sizeof(SaveTVEProfileRequest::set_active_)
      - PROTOBUF_FIELD_OFFSET(SaveTVEProfileRequest, profile_)>(
          reinterpret_cast<char*>(&profile_),
          reinterpret_cast<char*>(&other->profile_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SaveTVEProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[4]);
}

// ===================================================================

class SaveTVEProfileResponse::_Internal {
 public:
};

SaveTVEProfileResponse::SaveTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse)
}
SaveTVEProfileResponse::SaveTVEProfileResponse(const SaveTVEProfileResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse)
}

inline void SaveTVEProfileResponse::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SaveTVEProfileResponse::~SaveTVEProfileResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SaveTVEProfileResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SaveTVEProfileResponse::ArenaDtor(void* object) {
  SaveTVEProfileResponse* _this = reinterpret_cast< SaveTVEProfileResponse* >(object);
  (void)_this;
}
void SaveTVEProfileResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SaveTVEProfileResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SaveTVEProfileResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SaveTVEProfileResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SaveTVEProfileResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse)
  return target;
}

size_t SaveTVEProfileResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SaveTVEProfileResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SaveTVEProfileResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SaveTVEProfileResponse::GetClassData() const { return &_class_data_; }

void SaveTVEProfileResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SaveTVEProfileResponse *>(to)->MergeFrom(
      static_cast<const SaveTVEProfileResponse &>(from));
}


void SaveTVEProfileResponse::MergeFrom(const SaveTVEProfileResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SaveTVEProfileResponse::CopyFrom(const SaveTVEProfileResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.target_velocity_estimator.SaveTVEProfileResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SaveTVEProfileResponse::IsInitialized() const {
  return true;
}

void SaveTVEProfileResponse::InternalSwap(SaveTVEProfileResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SaveTVEProfileResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[5]);
}

// ===================================================================

class SetActiveTVEProfileRequest::_Internal {
 public:
};

SetActiveTVEProfileRequest::SetActiveTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest)
}
SetActiveTVEProfileRequest::SetActiveTVEProfileRequest(const SetActiveTVEProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest)
}

inline void SetActiveTVEProfileRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SetActiveTVEProfileRequest::~SetActiveTVEProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetActiveTVEProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetActiveTVEProfileRequest::ArenaDtor(void* object) {
  SetActiveTVEProfileRequest* _this = reinterpret_cast< SetActiveTVEProfileRequest* >(object);
  (void)_this;
}
void SetActiveTVEProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetActiveTVEProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetActiveTVEProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetActiveTVEProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetActiveTVEProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest)
  return target;
}

size_t SetActiveTVEProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetActiveTVEProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetActiveTVEProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetActiveTVEProfileRequest::GetClassData() const { return &_class_data_; }

void SetActiveTVEProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetActiveTVEProfileRequest *>(to)->MergeFrom(
      static_cast<const SetActiveTVEProfileRequest &>(from));
}


void SetActiveTVEProfileRequest::MergeFrom(const SetActiveTVEProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetActiveTVEProfileRequest::CopyFrom(const SetActiveTVEProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetActiveTVEProfileRequest::IsInitialized() const {
  return true;
}

void SetActiveTVEProfileRequest::InternalSwap(SetActiveTVEProfileRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SetActiveTVEProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[6]);
}

// ===================================================================

class SetActiveTVEProfileResponse::_Internal {
 public:
};

SetActiveTVEProfileResponse::SetActiveTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileResponse)
}
SetActiveTVEProfileResponse::SetActiveTVEProfileResponse(const SetActiveTVEProfileResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.target_velocity_estimator.SetActiveTVEProfileResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetActiveTVEProfileResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetActiveTVEProfileResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SetActiveTVEProfileResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[7]);
}

// ===================================================================

class DeleteTVEProfileRequest::_Internal {
 public:
};

DeleteTVEProfileRequest::DeleteTVEProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest)
}
DeleteTVEProfileRequest::DeleteTVEProfileRequest(const DeleteTVEProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  new_active_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    new_active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_new_active_id().empty()) {
    new_active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_new_active_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest)
}

inline void DeleteTVEProfileRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
new_active_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  new_active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DeleteTVEProfileRequest::~DeleteTVEProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeleteTVEProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  new_active_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DeleteTVEProfileRequest::ArenaDtor(void* object) {
  DeleteTVEProfileRequest* _this = reinterpret_cast< DeleteTVEProfileRequest* >(object);
  (void)_this;
}
void DeleteTVEProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeleteTVEProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeleteTVEProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  new_active_id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeleteTVEProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string new_active_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_new_active_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.new_active_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeleteTVEProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string new_active_id = 2;
  if (!this->_internal_new_active_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_new_active_id().data(), static_cast<int>(this->_internal_new_active_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest.new_active_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_new_active_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest)
  return target;
}

size_t DeleteTVEProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string new_active_id = 2;
  if (!this->_internal_new_active_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_new_active_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeleteTVEProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeleteTVEProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeleteTVEProfileRequest::GetClassData() const { return &_class_data_; }

void DeleteTVEProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeleteTVEProfileRequest *>(to)->MergeFrom(
      static_cast<const DeleteTVEProfileRequest &>(from));
}


void DeleteTVEProfileRequest::MergeFrom(const DeleteTVEProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_new_active_id().empty()) {
    _internal_set_new_active_id(from._internal_new_active_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeleteTVEProfileRequest::CopyFrom(const DeleteTVEProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.target_velocity_estimator.DeleteTVEProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeleteTVEProfileRequest::IsInitialized() const {
  return true;
}

void DeleteTVEProfileRequest::InternalSwap(DeleteTVEProfileRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &new_active_id_, lhs_arena,
      &other->new_active_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DeleteTVEProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[8]);
}

// ===================================================================

class DeleteTVEProfileResponse::_Internal {
 public:
};

DeleteTVEProfileResponse::DeleteTVEProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.target_velocity_estimator.DeleteTVEProfileResponse)
}
DeleteTVEProfileResponse::DeleteTVEProfileResponse(const DeleteTVEProfileResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.target_velocity_estimator.DeleteTVEProfileResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeleteTVEProfileResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeleteTVEProfileResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata DeleteTVEProfileResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto[9]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace target_velocity_estimator
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* Arena::CreateMaybeMessage< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* Arena::CreateMaybeMessage< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* Arena::CreateMaybeMessage< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* Arena::CreateMaybeMessage< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* Arena::CreateMaybeMessage< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* Arena::CreateMaybeMessage< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* Arena::CreateMaybeMessage< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* Arena::CreateMaybeMessage< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* Arena::CreateMaybeMessage< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* Arena::CreateMaybeMessage< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

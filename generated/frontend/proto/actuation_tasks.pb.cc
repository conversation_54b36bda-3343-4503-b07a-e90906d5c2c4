// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/actuation_tasks.proto

#include "frontend/proto/actuation_tasks.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace actuation_tasks {
constexpr GlobalAimbotActuationTaskRequest::GlobalAimbotActuationTaskRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : task_(nullptr)
  , row_id_(0u){}
struct GlobalAimbotActuationTaskRequestDefaultTypeInternal {
  constexpr GlobalAimbotActuationTaskRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GlobalAimbotActuationTaskRequestDefaultTypeInternal() {}
  union {
    GlobalAimbotActuationTaskRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GlobalAimbotActuationTaskRequestDefaultTypeInternal _GlobalAimbotActuationTaskRequest_default_instance_;
constexpr GlobalActuationTaskState::GlobalActuationTaskState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , running_(false)
  , elapsed_time_ms_(0u)
  , expected_time_ms_(0u){}
struct GlobalActuationTaskStateDefaultTypeInternal {
  constexpr GlobalActuationTaskStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GlobalActuationTaskStateDefaultTypeInternal() {}
  union {
    GlobalActuationTaskState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GlobalActuationTaskStateDefaultTypeInternal _GlobalActuationTaskState_default_instance_;
}  // namespace actuation_tasks
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2factuation_5ftasks_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2factuation_5ftasks_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2factuation_5ftasks_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2factuation_5ftasks_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, row_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, task_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::actuation_tasks::GlobalActuationTaskState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::actuation_tasks::GlobalActuationTaskState, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::actuation_tasks::GlobalActuationTaskState, running_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::actuation_tasks::GlobalActuationTaskState, elapsed_time_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::actuation_tasks::GlobalActuationTaskState, expected_time_ms_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest)},
  { 8, -1, -1, sizeof(::carbon::frontend::actuation_tasks::GlobalActuationTaskState)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::actuation_tasks::_GlobalAimbotActuationTaskRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::actuation_tasks::_GlobalActuationTaskState_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2factuation_5ftasks_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n$frontend/proto/actuation_tasks.proto\022\037"
  "carbon.frontend.actuation_tasks\032Hcore/co"
  "ntrols/exterminator/controllers/aimbot/p"
  "rocess/proto/aimbot.proto\032\031frontend/prot"
  "o/util.proto\"^\n GlobalAimbotActuationTas"
  "kRequest\022\016\n\006row_id\030\001 \001(\r\022*\n\004task\030\002 \001(\0132\034"
  ".aimbot.ActuationTaskRequest\"\213\001\n\030GlobalA"
  "ctuationTaskState\022+\n\002ts\030\001 \001(\0132\037.carbon.f"
  "rontend.util.Timestamp\022\017\n\007running\030\002 \001(\010\022"
  "\027\n\017elapsed_time_ms\030\003 \001(\r\022\030\n\020expected_tim"
  "e_ms\030\004 \001(\r2\366\002\n\025ActuationTasksService\022}\n\037"
  "GetNextGlobalActuationTaskState\022\037.carbon"
  ".frontend.util.Timestamp\0329.carbon.fronte"
  "nd.actuation_tasks.GlobalActuationTaskSt"
  "ate\022\200\001\n\036StartGlobalAimbotActuationTask\022A"
  ".carbon.frontend.actuation_tasks.GlobalA"
  "imbotActuationTaskRequest\032\033.carbon.front"
  "end.util.Empty\022[\n\037CancelGlobalAimbotActu"
  "ationTask\022\033.carbon.frontend.util.Empty\032\033"
  ".carbon.frontend.util.EmptyB\020Z\016proto/fro"
  "ntendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto_deps[2] = {
  &::descriptor_table_core_2fcontrols_2fexterminator_2fcontrollers_2faimbot_2fprocess_2fproto_2faimbot_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto = {
  false, false, 813, descriptor_table_protodef_frontend_2fproto_2factuation_5ftasks_2eproto, "frontend/proto/actuation_tasks.proto", 
  &descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto_once, descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto_deps, 2, 2,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2factuation_5ftasks_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2factuation_5ftasks_2eproto, file_level_enum_descriptors_frontend_2fproto_2factuation_5ftasks_2eproto, file_level_service_descriptors_frontend_2fproto_2factuation_5ftasks_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2factuation_5ftasks_2eproto(&descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto);
namespace carbon {
namespace frontend {
namespace actuation_tasks {

// ===================================================================

class GlobalAimbotActuationTaskRequest::_Internal {
 public:
  static const ::aimbot::ActuationTaskRequest& task(const GlobalAimbotActuationTaskRequest* msg);
};

const ::aimbot::ActuationTaskRequest&
GlobalAimbotActuationTaskRequest::_Internal::task(const GlobalAimbotActuationTaskRequest* msg) {
  return *msg->task_;
}
void GlobalAimbotActuationTaskRequest::clear_task() {
  if (GetArenaForAllocation() == nullptr && task_ != nullptr) {
    delete task_;
  }
  task_ = nullptr;
}
GlobalAimbotActuationTaskRequest::GlobalAimbotActuationTaskRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest)
}
GlobalAimbotActuationTaskRequest::GlobalAimbotActuationTaskRequest(const GlobalAimbotActuationTaskRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_task()) {
    task_ = new ::aimbot::ActuationTaskRequest(*from.task_);
  } else {
    task_ = nullptr;
  }
  row_id_ = from.row_id_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest)
}

inline void GlobalAimbotActuationTaskRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&task_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&row_id_) -
    reinterpret_cast<char*>(&task_)) + sizeof(row_id_));
}

GlobalAimbotActuationTaskRequest::~GlobalAimbotActuationTaskRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GlobalAimbotActuationTaskRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete task_;
}

void GlobalAimbotActuationTaskRequest::ArenaDtor(void* object) {
  GlobalAimbotActuationTaskRequest* _this = reinterpret_cast< GlobalAimbotActuationTaskRequest* >(object);
  (void)_this;
}
void GlobalAimbotActuationTaskRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GlobalAimbotActuationTaskRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GlobalAimbotActuationTaskRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && task_ != nullptr) {
    delete task_;
  }
  task_ = nullptr;
  row_id_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GlobalAimbotActuationTaskRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 row_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          row_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .aimbot.ActuationTaskRequest task = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_task(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GlobalAimbotActuationTaskRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 row_id = 1;
  if (this->_internal_row_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_row_id(), target);
  }

  // .aimbot.ActuationTaskRequest task = 2;
  if (this->_internal_has_task()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::task(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest)
  return target;
}

size_t GlobalAimbotActuationTaskRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .aimbot.ActuationTaskRequest task = 2;
  if (this->_internal_has_task()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *task_);
  }

  // uint32 row_id = 1;
  if (this->_internal_row_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_row_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GlobalAimbotActuationTaskRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GlobalAimbotActuationTaskRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GlobalAimbotActuationTaskRequest::GetClassData() const { return &_class_data_; }

void GlobalAimbotActuationTaskRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GlobalAimbotActuationTaskRequest *>(to)->MergeFrom(
      static_cast<const GlobalAimbotActuationTaskRequest &>(from));
}


void GlobalAimbotActuationTaskRequest::MergeFrom(const GlobalAimbotActuationTaskRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_task()) {
    _internal_mutable_task()->::aimbot::ActuationTaskRequest::MergeFrom(from._internal_task());
  }
  if (from._internal_row_id() != 0) {
    _internal_set_row_id(from._internal_row_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GlobalAimbotActuationTaskRequest::CopyFrom(const GlobalAimbotActuationTaskRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GlobalAimbotActuationTaskRequest::IsInitialized() const {
  return true;
}

void GlobalAimbotActuationTaskRequest::InternalSwap(GlobalAimbotActuationTaskRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GlobalAimbotActuationTaskRequest, row_id_)
      + sizeof(GlobalAimbotActuationTaskRequest::row_id_)
      - PROTOBUF_FIELD_OFFSET(GlobalAimbotActuationTaskRequest, task_)>(
          reinterpret_cast<char*>(&task_),
          reinterpret_cast<char*>(&other->task_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GlobalAimbotActuationTaskRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto_getter, &descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto_once,
      file_level_metadata_frontend_2fproto_2factuation_5ftasks_2eproto[0]);
}

// ===================================================================

class GlobalActuationTaskState::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GlobalActuationTaskState* msg);
};

const ::carbon::frontend::util::Timestamp&
GlobalActuationTaskState::_Internal::ts(const GlobalActuationTaskState* msg) {
  return *msg->ts_;
}
void GlobalActuationTaskState::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GlobalActuationTaskState::GlobalActuationTaskState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.actuation_tasks.GlobalActuationTaskState)
}
GlobalActuationTaskState::GlobalActuationTaskState(const GlobalActuationTaskState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&running_, &from.running_,
    static_cast<size_t>(reinterpret_cast<char*>(&expected_time_ms_) -
    reinterpret_cast<char*>(&running_)) + sizeof(expected_time_ms_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.actuation_tasks.GlobalActuationTaskState)
}

inline void GlobalActuationTaskState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&expected_time_ms_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(expected_time_ms_));
}

GlobalActuationTaskState::~GlobalActuationTaskState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.actuation_tasks.GlobalActuationTaskState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GlobalActuationTaskState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GlobalActuationTaskState::ArenaDtor(void* object) {
  GlobalActuationTaskState* _this = reinterpret_cast< GlobalActuationTaskState* >(object);
  (void)_this;
}
void GlobalActuationTaskState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GlobalActuationTaskState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GlobalActuationTaskState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.actuation_tasks.GlobalActuationTaskState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&running_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&expected_time_ms_) -
      reinterpret_cast<char*>(&running_)) + sizeof(expected_time_ms_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GlobalActuationTaskState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool running = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          running_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 elapsed_time_ms = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          elapsed_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 expected_time_ms = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          expected_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GlobalActuationTaskState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.actuation_tasks.GlobalActuationTaskState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // bool running = 2;
  if (this->_internal_running() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_running(), target);
  }

  // uint32 elapsed_time_ms = 3;
  if (this->_internal_elapsed_time_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_elapsed_time_ms(), target);
  }

  // uint32 expected_time_ms = 4;
  if (this->_internal_expected_time_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_expected_time_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.actuation_tasks.GlobalActuationTaskState)
  return target;
}

size_t GlobalActuationTaskState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.actuation_tasks.GlobalActuationTaskState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // bool running = 2;
  if (this->_internal_running() != 0) {
    total_size += 1 + 1;
  }

  // uint32 elapsed_time_ms = 3;
  if (this->_internal_elapsed_time_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_elapsed_time_ms());
  }

  // uint32 expected_time_ms = 4;
  if (this->_internal_expected_time_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_expected_time_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GlobalActuationTaskState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GlobalActuationTaskState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GlobalActuationTaskState::GetClassData() const { return &_class_data_; }

void GlobalActuationTaskState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GlobalActuationTaskState *>(to)->MergeFrom(
      static_cast<const GlobalActuationTaskState &>(from));
}


void GlobalActuationTaskState::MergeFrom(const GlobalActuationTaskState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.actuation_tasks.GlobalActuationTaskState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_running() != 0) {
    _internal_set_running(from._internal_running());
  }
  if (from._internal_elapsed_time_ms() != 0) {
    _internal_set_elapsed_time_ms(from._internal_elapsed_time_ms());
  }
  if (from._internal_expected_time_ms() != 0) {
    _internal_set_expected_time_ms(from._internal_expected_time_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GlobalActuationTaskState::CopyFrom(const GlobalActuationTaskState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.actuation_tasks.GlobalActuationTaskState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GlobalActuationTaskState::IsInitialized() const {
  return true;
}

void GlobalActuationTaskState::InternalSwap(GlobalActuationTaskState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GlobalActuationTaskState, expected_time_ms_)
      + sizeof(GlobalActuationTaskState::expected_time_ms_)
      - PROTOBUF_FIELD_OFFSET(GlobalActuationTaskState, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GlobalActuationTaskState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto_getter, &descriptor_table_frontend_2fproto_2factuation_5ftasks_2eproto_once,
      file_level_metadata_frontend_2fproto_2factuation_5ftasks_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace actuation_tasks
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* Arena::CreateMaybeMessage< ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* Arena::CreateMaybeMessage< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/reporting.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2freporting_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2freporting_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2freporting_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2freporting_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2freporting_2eproto;
namespace carbon {
namespace frontend {
namespace features {
class Location;
struct LocationDefaultTypeInternal;
extern LocationDefaultTypeInternal _Location_default_instance_;
class LocationHistory;
struct LocationHistoryDefaultTypeInternal;
extern LocationHistoryDefaultTypeInternal _LocationHistory_default_instance_;
}  // namespace features
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::features::Location* Arena::CreateMaybeMessage<::carbon::frontend::features::Location>(Arena*);
template<> ::carbon::frontend::features::LocationHistory* Arena::CreateMaybeMessage<::carbon::frontend::features::LocationHistory>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace features {

// ===================================================================

class Location final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.features.Location) */ {
 public:
  inline Location() : Location(nullptr) {}
  ~Location() override;
  explicit constexpr Location(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Location(const Location& from);
  Location(Location&& from) noexcept
    : Location() {
    *this = ::std::move(from);
  }

  inline Location& operator=(const Location& from) {
    CopyFrom(from);
    return *this;
  }
  inline Location& operator=(Location&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Location& default_instance() {
    return *internal_default_instance();
  }
  static inline const Location* internal_default_instance() {
    return reinterpret_cast<const Location*>(
               &_Location_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Location& a, Location& b) {
    a.Swap(&b);
  }
  inline void Swap(Location* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Location* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Location* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Location>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Location& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Location& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Location* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.features.Location";
  }
  protected:
  explicit Location(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kLatitudeFieldNumber = 2,
    kLongitudeFieldNumber = 3,
    kAltitudeFieldNumber = 4,
    kIsWeedingFieldNumber = 5,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // float latitude = 2;
  void clear_latitude();
  float latitude() const;
  void set_latitude(float value);
  private:
  float _internal_latitude() const;
  void _internal_set_latitude(float value);
  public:

  // float longitude = 3;
  void clear_longitude();
  float longitude() const;
  void set_longitude(float value);
  private:
  float _internal_longitude() const;
  void _internal_set_longitude(float value);
  public:

  // float altitude = 4;
  void clear_altitude();
  float altitude() const;
  void set_altitude(float value);
  private:
  float _internal_altitude() const;
  void _internal_set_altitude(float value);
  public:

  // bool is_weeding = 5;
  void clear_is_weeding();
  bool is_weeding() const;
  void set_is_weeding(bool value);
  private:
  bool _internal_is_weeding() const;
  void _internal_set_is_weeding(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.features.Location)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  float latitude_;
  float longitude_;
  float altitude_;
  bool is_weeding_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2freporting_2eproto;
};
// -------------------------------------------------------------------

class LocationHistory final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.features.LocationHistory) */ {
 public:
  inline LocationHistory() : LocationHistory(nullptr) {}
  ~LocationHistory() override;
  explicit constexpr LocationHistory(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LocationHistory(const LocationHistory& from);
  LocationHistory(LocationHistory&& from) noexcept
    : LocationHistory() {
    *this = ::std::move(from);
  }

  inline LocationHistory& operator=(const LocationHistory& from) {
    CopyFrom(from);
    return *this;
  }
  inline LocationHistory& operator=(LocationHistory&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LocationHistory& default_instance() {
    return *internal_default_instance();
  }
  static inline const LocationHistory* internal_default_instance() {
    return reinterpret_cast<const LocationHistory*>(
               &_LocationHistory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(LocationHistory& a, LocationHistory& b) {
    a.Swap(&b);
  }
  inline void Swap(LocationHistory* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LocationHistory* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LocationHistory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LocationHistory>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LocationHistory& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LocationHistory& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LocationHistory* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.features.LocationHistory";
  }
  protected:
  explicit LocationHistory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHistoryFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.frontend.features.Location history = 2;
  int history_size() const;
  private:
  int _internal_history_size() const;
  public:
  void clear_history();
  ::carbon::frontend::features::Location* mutable_history(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::features::Location >*
      mutable_history();
  private:
  const ::carbon::frontend::features::Location& _internal_history(int index) const;
  ::carbon::frontend::features::Location* _internal_add_history();
  public:
  const ::carbon::frontend::features::Location& history(int index) const;
  ::carbon::frontend::features::Location* add_history();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::features::Location >&
      history() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.features.LocationHistory)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::features::Location > history_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2freporting_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Location

// .carbon.frontend.util.Timestamp ts = 1;
inline bool Location::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool Location::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& Location::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& Location::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.features.Location.ts)
  return _internal_ts();
}
inline void Location::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.features.Location.ts)
}
inline ::carbon::frontend::util::Timestamp* Location::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Location::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.features.Location.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Location::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* Location::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.features.Location.ts)
  return _msg;
}
inline void Location::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.features.Location.ts)
}

// float latitude = 2;
inline void Location::clear_latitude() {
  latitude_ = 0;
}
inline float Location::_internal_latitude() const {
  return latitude_;
}
inline float Location::latitude() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.features.Location.latitude)
  return _internal_latitude();
}
inline void Location::_internal_set_latitude(float value) {
  
  latitude_ = value;
}
inline void Location::set_latitude(float value) {
  _internal_set_latitude(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.features.Location.latitude)
}

// float longitude = 3;
inline void Location::clear_longitude() {
  longitude_ = 0;
}
inline float Location::_internal_longitude() const {
  return longitude_;
}
inline float Location::longitude() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.features.Location.longitude)
  return _internal_longitude();
}
inline void Location::_internal_set_longitude(float value) {
  
  longitude_ = value;
}
inline void Location::set_longitude(float value) {
  _internal_set_longitude(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.features.Location.longitude)
}

// float altitude = 4;
inline void Location::clear_altitude() {
  altitude_ = 0;
}
inline float Location::_internal_altitude() const {
  return altitude_;
}
inline float Location::altitude() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.features.Location.altitude)
  return _internal_altitude();
}
inline void Location::_internal_set_altitude(float value) {
  
  altitude_ = value;
}
inline void Location::set_altitude(float value) {
  _internal_set_altitude(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.features.Location.altitude)
}

// bool is_weeding = 5;
inline void Location::clear_is_weeding() {
  is_weeding_ = false;
}
inline bool Location::_internal_is_weeding() const {
  return is_weeding_;
}
inline bool Location::is_weeding() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.features.Location.is_weeding)
  return _internal_is_weeding();
}
inline void Location::_internal_set_is_weeding(bool value) {
  
  is_weeding_ = value;
}
inline void Location::set_is_weeding(bool value) {
  _internal_set_is_weeding(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.features.Location.is_weeding)
}

// -------------------------------------------------------------------

// LocationHistory

// .carbon.frontend.util.Timestamp ts = 1;
inline bool LocationHistory::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool LocationHistory::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& LocationHistory::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& LocationHistory::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.features.LocationHistory.ts)
  return _internal_ts();
}
inline void LocationHistory::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.features.LocationHistory.ts)
}
inline ::carbon::frontend::util::Timestamp* LocationHistory::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* LocationHistory::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.features.LocationHistory.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* LocationHistory::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* LocationHistory::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.features.LocationHistory.ts)
  return _msg;
}
inline void LocationHistory::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.features.LocationHistory.ts)
}

// repeated .carbon.frontend.features.Location history = 2;
inline int LocationHistory::_internal_history_size() const {
  return history_.size();
}
inline int LocationHistory::history_size() const {
  return _internal_history_size();
}
inline void LocationHistory::clear_history() {
  history_.Clear();
}
inline ::carbon::frontend::features::Location* LocationHistory::mutable_history(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.features.LocationHistory.history)
  return history_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::features::Location >*
LocationHistory::mutable_history() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.features.LocationHistory.history)
  return &history_;
}
inline const ::carbon::frontend::features::Location& LocationHistory::_internal_history(int index) const {
  return history_.Get(index);
}
inline const ::carbon::frontend::features::Location& LocationHistory::history(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.features.LocationHistory.history)
  return _internal_history(index);
}
inline ::carbon::frontend::features::Location* LocationHistory::_internal_add_history() {
  return history_.Add();
}
inline ::carbon::frontend::features::Location* LocationHistory::add_history() {
  ::carbon::frontend::features::Location* _add = _internal_add_history();
  // @@protoc_insertion_point(field_add:carbon.frontend.features.LocationHistory.history)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::features::Location >&
LocationHistory::history() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.features.LocationHistory.history)
  return history_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace features
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2freporting_2eproto

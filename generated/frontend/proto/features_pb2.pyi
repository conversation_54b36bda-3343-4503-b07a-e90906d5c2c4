"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Mapping as typing___Mapping,
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

GenerationValue = typing___NewType('GenerationValue', builtin___int)
type___GenerationValue = GenerationValue
Generation: _Generation
class _Generation(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[GenerationValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    Undefined = typing___cast(GenerationValue, 0)
    Slayer = typing___cast(GenerationValue, 1)
    Reaper = typing___cast(GenerationValue, 2)
Undefined = typing___cast(GenerationValue, 0)
Slayer = typing___cast(GenerationValue, 1)
Reaper = typing___cast(GenerationValue, 2)

class RowConfiguration(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    num_predicts: builtin___int = ...
    num_targets: builtin___int = ...

    def __init__(self,
        *,
        num_predicts : typing___Optional[builtin___int] = None,
        num_targets : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"num_predicts",b"num_predicts",u"num_targets",b"num_targets"]) -> None: ...
type___RowConfiguration = RowConfiguration

class RobotConfiguration(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class RowConfigurationEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> type___RowConfiguration: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[type___RowConfiguration] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___RowConfigurationEntry = RowConfigurationEntry

    num_rows: builtin___int = ...
    generation: type___GenerationValue = ...

    @property
    def row_configuration(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, type___RowConfiguration]: ...

    def __init__(self,
        *,
        num_rows : typing___Optional[builtin___int] = None,
        row_configuration : typing___Optional[typing___Mapping[builtin___int, type___RowConfiguration]] = None,
        generation : typing___Optional[type___GenerationValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"generation",b"generation",u"num_rows",b"num_rows",u"row_configuration",b"row_configuration"]) -> None: ...
type___RobotConfiguration = RobotConfiguration

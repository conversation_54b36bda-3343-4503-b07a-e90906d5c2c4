# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import status_bar_pb2 as frontend_dot_proto_dot_status__bar__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class StatusBarServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextStatus = channel.unary_unary(
                '/carbon.frontend.status_bar.StatusBarService/GetNextStatus',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_status__bar__pb2.StatusBarMessage.FromString,
                )
        self.ReportIssue = channel.unary_unary(
                '/carbon.frontend.status_bar.StatusBarService/ReportIssue',
                request_serializer=frontend_dot_proto_dot_status__bar__pb2.ReportIssueRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetSupportPhone = channel.unary_unary(
                '/carbon.frontend.status_bar.StatusBarService/GetSupportPhone',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_status__bar__pb2.SupportPhoneMessage.FromString,
                )


class StatusBarServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReportIssue(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSupportPhone(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_StatusBarServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextStatus,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_status__bar__pb2.StatusBarMessage.SerializeToString,
            ),
            'ReportIssue': grpc.unary_unary_rpc_method_handler(
                    servicer.ReportIssue,
                    request_deserializer=frontend_dot_proto_dot_status__bar__pb2.ReportIssueRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetSupportPhone': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSupportPhone,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_status__bar__pb2.SupportPhoneMessage.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.status_bar.StatusBarService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class StatusBarService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.status_bar.StatusBarService/GetNextStatus',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_status__bar__pb2.StatusBarMessage.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ReportIssue(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.status_bar.StatusBarService/ReportIssue',
            frontend_dot_proto_dot_status__bar__pb2.ReportIssueRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSupportPhone(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.status_bar.StatusBarService/GetSupportPhone',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_status__bar__pb2.SupportPhoneMessage.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

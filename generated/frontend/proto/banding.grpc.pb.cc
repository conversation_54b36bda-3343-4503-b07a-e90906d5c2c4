// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/banding.proto

#include "frontend/proto/banding.pb.h"
#include "frontend/proto/banding.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace banding {

static const char* BandingService_method_names[] = {
  "/carbon.frontend.banding.BandingService/LoadBandingDefs",
  "/carbon.frontend.banding.BandingService/SaveBandingDef",
  "/carbon.frontend.banding.BandingService/DeleteBandingDef",
  "/carbon.frontend.banding.BandingService/SetActiveBandingDef",
  "/carbon.frontend.banding.BandingService/GetActiveBandingDef",
  "/carbon.frontend.banding.BandingService/GetNextVisualizationData",
  "/carbon.frontend.banding.BandingService/GetNextVisualizationData2",
  "/carbon.frontend.banding.BandingService/GetNextVisualizationDataForAllRows",
  "/carbon.frontend.banding.BandingService/GetDimensions",
  "/carbon.frontend.banding.BandingService/SetBandingEnabled",
  "/carbon.frontend.banding.BandingService/IsBandingEnabled",
  "/carbon.frontend.banding.BandingService/SetDynamicBandingEnabled",
  "/carbon.frontend.banding.BandingService/IsDynamicBandingEnabled",
  "/carbon.frontend.banding.BandingService/GetVisualizationMetadata",
  "/carbon.frontend.banding.BandingService/GetNextBandingState",
};

std::unique_ptr< BandingService::Stub> BandingService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< BandingService::Stub> stub(new BandingService::Stub(channel, options));
  return stub;
}

BandingService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_LoadBandingDefs_(BandingService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SaveBandingDef_(BandingService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeleteBandingDef_(BandingService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetActiveBandingDef_(BandingService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetActiveBandingDef_(BandingService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextVisualizationData_(BandingService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextVisualizationData2_(BandingService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextVisualizationDataForAllRows_(BandingService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDimensions_(BandingService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetBandingEnabled_(BandingService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_IsBandingEnabled_(BandingService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetDynamicBandingEnabled_(BandingService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_IsDynamicBandingEnabled_(BandingService_method_names[12], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetVisualizationMetadata_(BandingService_method_names[13], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextBandingState_(BandingService_method_names[14], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status BandingService::Stub::LoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::LoadBandingDefsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::LoadBandingDefsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LoadBandingDefs_, context, request, response);
}

void BandingService::Stub::async::LoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::LoadBandingDefsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::LoadBandingDefsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LoadBandingDefs_, context, request, response, std::move(f));
}

void BandingService::Stub::async::LoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::LoadBandingDefsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LoadBandingDefs_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::LoadBandingDefsResponse>* BandingService::Stub::PrepareAsyncLoadBandingDefsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::banding::LoadBandingDefsResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LoadBandingDefs_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::LoadBandingDefsResponse>* BandingService::Stub::AsyncLoadBandingDefsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLoadBandingDefsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::SaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::banding::SaveBandingDefRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SaveBandingDef_, context, request, response);
}

void BandingService::Stub::async::SaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::banding::SaveBandingDefRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveBandingDef_, context, request, response, std::move(f));
}

void BandingService::Stub::async::SaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveBandingDef_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* BandingService::Stub::PrepareAsyncSaveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::SaveBandingDefRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SaveBandingDef_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* BandingService::Stub::AsyncSaveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSaveBandingDefRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::DeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::banding::DeleteBandingDefRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DeleteBandingDef_, context, request, response);
}

void BandingService::Stub::async::DeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::banding::DeleteBandingDefRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteBandingDef_, context, request, response, std::move(f));
}

void BandingService::Stub::async::DeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteBandingDef_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* BandingService::Stub::PrepareAsyncDeleteBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::DeleteBandingDefRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DeleteBandingDef_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* BandingService::Stub::AsyncDeleteBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDeleteBandingDefRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::SetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::banding::SetActiveBandingDefRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetActiveBandingDef_, context, request, response);
}

void BandingService::Stub::async::SetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::banding::SetActiveBandingDefRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActiveBandingDef_, context, request, response, std::move(f));
}

void BandingService::Stub::async::SetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActiveBandingDef_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* BandingService::Stub::PrepareAsyncSetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::SetActiveBandingDefRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetActiveBandingDef_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* BandingService::Stub::AsyncSetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetActiveBandingDefRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::GetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetActiveBandingDefResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetActiveBandingDef_, context, request, response);
}

void BandingService::Stub::async::GetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetActiveBandingDefResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetActiveBandingDef_, context, request, response, std::move(f));
}

void BandingService::Stub::async::GetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetActiveBandingDef_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetActiveBandingDefResponse>* BandingService::Stub::PrepareAsyncGetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::banding::GetActiveBandingDefResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetActiveBandingDef_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetActiveBandingDefResponse>* BandingService::Stub::AsyncGetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetActiveBandingDefRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::GetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextVisualizationData_, context, request, response);
}

void BandingService::Stub::async::GetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextVisualizationData_, context, request, response, std::move(f));
}

void BandingService::Stub::async::GetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextVisualizationData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataResponse>* BandingService::Stub::PrepareAsyncGetNextVisualizationDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::banding::GetNextVisualizationDataResponse, ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextVisualizationData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataResponse>* BandingService::Stub::AsyncGetNextVisualizationDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextVisualizationDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::GetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationData2Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextVisualizationData2_, context, request, response);
}

void BandingService::Stub::async::GetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationData2Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextVisualizationData2_, context, request, response, std::move(f));
}

void BandingService::Stub::async::GetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextVisualizationData2_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationData2Response>* BandingService::Stub::PrepareAsyncGetNextVisualizationData2Raw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::banding::GetNextVisualizationData2Response, ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextVisualizationData2_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationData2Response>* BandingService::Stub::AsyncGetNextVisualizationData2Raw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextVisualizationData2Raw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::GetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextVisualizationDataForAllRows_, context, request, response);
}

void BandingService::Stub::async::GetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextVisualizationDataForAllRows_, context, request, response, std::move(f));
}

void BandingService::Stub::async::GetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextVisualizationDataForAllRows_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>* BandingService::Stub::PrepareAsyncGetNextVisualizationDataForAllRowsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextVisualizationDataForAllRows_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>* BandingService::Stub::AsyncGetNextVisualizationDataForAllRowsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextVisualizationDataForAllRowsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::GetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::aimbot::GetDimensionsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::banding::GetDimensionsRequest, ::aimbot::GetDimensionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDimensions_, context, request, response);
}

void BandingService::Stub::async::GetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest* request, ::aimbot::GetDimensionsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::banding::GetDimensionsRequest, ::aimbot::GetDimensionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDimensions_, context, request, response, std::move(f));
}

void BandingService::Stub::async::GetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest* request, ::aimbot::GetDimensionsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDimensions_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::GetDimensionsResponse>* BandingService::Stub::PrepareAsyncGetDimensionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::GetDimensionsResponse, ::carbon::frontend::banding::GetDimensionsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDimensions_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::GetDimensionsResponse>* BandingService::Stub::AsyncGetDimensionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDimensionsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::SetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::carbon::frontend::banding::SetBandingEnabledResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetBandingEnabled_, context, request, response);
}

void BandingService::Stub::async::SetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetBandingEnabled_, context, request, response, std::move(f));
}

void BandingService::Stub::async::SetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetBandingEnabled_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>* BandingService::Stub::PrepareAsyncSetBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::banding::SetBandingEnabledResponse, ::carbon::frontend::banding::SetBandingEnabledRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetBandingEnabled_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>* BandingService::Stub::AsyncSetBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetBandingEnabledRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::IsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::IsBandingEnabledResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_IsBandingEnabled_, context, request, response);
}

void BandingService::Stub::async::IsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_IsBandingEnabled_, context, request, response, std::move(f));
}

void BandingService::Stub::async::IsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_IsBandingEnabled_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>* BandingService::Stub::PrepareAsyncIsBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::banding::IsBandingEnabledResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_IsBandingEnabled_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>* BandingService::Stub::AsyncIsBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncIsBandingEnabledRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::SetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::carbon::frontend::banding::SetBandingEnabledResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetDynamicBandingEnabled_, context, request, response);
}

void BandingService::Stub::async::SetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetDynamicBandingEnabled_, context, request, response, std::move(f));
}

void BandingService::Stub::async::SetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetDynamicBandingEnabled_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>* BandingService::Stub::PrepareAsyncSetDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::banding::SetBandingEnabledResponse, ::carbon::frontend::banding::SetBandingEnabledRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetDynamicBandingEnabled_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>* BandingService::Stub::AsyncSetDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetDynamicBandingEnabledRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::IsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::IsBandingEnabledResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_IsDynamicBandingEnabled_, context, request, response);
}

void BandingService::Stub::async::IsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_IsDynamicBandingEnabled_, context, request, response, std::move(f));
}

void BandingService::Stub::async::IsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_IsDynamicBandingEnabled_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>* BandingService::Stub::PrepareAsyncIsDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::banding::IsBandingEnabledResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_IsDynamicBandingEnabled_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>* BandingService::Stub::AsyncIsDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncIsDynamicBandingEnabledRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::GetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetVisualizationMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetVisualizationMetadata_, context, request, response);
}

void BandingService::Stub::async::GetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetVisualizationMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetVisualizationMetadata_, context, request, response, std::move(f));
}

void BandingService::Stub::async::GetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetVisualizationMetadata_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetVisualizationMetadataResponse>* BandingService::Stub::PrepareAsyncGetVisualizationMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::banding::GetVisualizationMetadataResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetVisualizationMetadata_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetVisualizationMetadataResponse>* BandingService::Stub::AsyncGetVisualizationMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetVisualizationMetadataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status BandingService::Stub::GetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::banding::GetNextBandingStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::banding::GetNextBandingStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextBandingState_, context, request, response);
}

void BandingService::Stub::async::GetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::banding::GetNextBandingStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::banding::GetNextBandingStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextBandingState_, context, request, response, std::move(f));
}

void BandingService::Stub::async::GetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::banding::GetNextBandingStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextBandingState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextBandingStateResponse>* BandingService::Stub::PrepareAsyncGetNextBandingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::banding::GetNextBandingStateResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextBandingState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextBandingStateResponse>* BandingService::Stub::AsyncGetNextBandingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextBandingStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

BandingService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::banding::LoadBandingDefsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::banding::LoadBandingDefsResponse* resp) {
               return service->LoadBandingDefs(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::banding::SaveBandingDefRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::banding::SaveBandingDefRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SaveBandingDef(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::banding::DeleteBandingDefRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::banding::DeleteBandingDefRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->DeleteBandingDef(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::banding::SetActiveBandingDefRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::banding::SetActiveBandingDefRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetActiveBandingDef(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetActiveBandingDefResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::banding::GetActiveBandingDefResponse* resp) {
               return service->GetActiveBandingDef(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::banding::GetNextVisualizationDataRequest* req,
             ::carbon::frontend::banding::GetNextVisualizationDataResponse* resp) {
               return service->GetNextVisualizationData(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationData2Response, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::banding::GetNextVisualizationDataRequest* req,
             ::carbon::frontend::banding::GetNextVisualizationData2Response* resp) {
               return service->GetNextVisualizationData2(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* req,
             ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* resp) {
               return service->GetNextVisualizationDataForAllRows(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::banding::GetDimensionsRequest, ::aimbot::GetDimensionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::banding::GetDimensionsRequest* req,
             ::aimbot::GetDimensionsResponse* resp) {
               return service->GetDimensions(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::banding::SetBandingEnabledRequest* req,
             ::carbon::frontend::banding::SetBandingEnabledResponse* resp) {
               return service->SetBandingEnabled(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::banding::IsBandingEnabledResponse* resp) {
               return service->IsBandingEnabled(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::banding::SetBandingEnabledRequest* req,
             ::carbon::frontend::banding::SetBandingEnabledResponse* resp) {
               return service->SetDynamicBandingEnabled(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::banding::IsBandingEnabledResponse* resp) {
               return service->IsDynamicBandingEnabled(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[13],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetVisualizationMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::banding::GetVisualizationMetadataResponse* resp) {
               return service->GetVisualizationMetadata(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BandingService_method_names[14],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BandingService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::banding::GetNextBandingStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](BandingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::banding::GetNextBandingStateResponse* resp) {
               return service->GetNextBandingState(ctx, req, resp);
             }, this)));
}

BandingService::Service::~Service() {
}

::grpc::Status BandingService::Service::LoadBandingDefs(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::LoadBandingDefsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::SaveBandingDef(::grpc::ServerContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::DeleteBandingDef(::grpc::ServerContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::SetActiveBandingDef(::grpc::ServerContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::GetActiveBandingDef(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::GetNextVisualizationData(::grpc::ServerContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::GetNextVisualizationData2(::grpc::ServerContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::GetNextVisualizationDataForAllRows(::grpc::ServerContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::GetDimensions(::grpc::ServerContext* context, const ::carbon::frontend::banding::GetDimensionsRequest* request, ::aimbot::GetDimensionsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::SetBandingEnabled(::grpc::ServerContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::IsBandingEnabled(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::SetDynamicBandingEnabled(::grpc::ServerContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::IsDynamicBandingEnabled(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::GetVisualizationMetadata(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BandingService::Service::GetNextBandingState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::banding::GetNextBandingStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace banding


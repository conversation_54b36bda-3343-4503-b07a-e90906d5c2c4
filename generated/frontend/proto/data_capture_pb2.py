# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/data_capture.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/data_capture.proto',
  package='carbon.frontend.data_capture',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n!frontend/proto/data_capture.proto\x12\x1c\x63\x61rbon.frontend.data_capture\x1a\x19\x66rontend/proto/util.proto\"\x1f\n\x0f\x44\x61taCaptureRate\x12\x0c\n\x04rate\x18\x01 \x01(\x01\"\xb9\x04\n\x10\x44\x61taCaptureState\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x14\n\x0cimages_taken\x18\x02 \x01(\r\x12\x1b\n\x13target_images_taken\x18\x03 \x01(\r\x12+\n#estimated_capture_remaining_time_ms\x18\x04 \x01(\x04\x12\x17\n\x0fimages_uploaded\x18\x05 \x01(\r\x12\x1e\n\x16target_images_uploaded\x18\x06 \x01(\r\x12*\n\"estimated_upload_remaining_time_ms\x18\x07 \x01(\x04\x12;\n\x04rate\x18\x08 \x01(\x0b\x32-.carbon.frontend.data_capture.DataCaptureRate\x12!\n\x19wireless_upload_available\x18\t \x01(\x08\x12\x1d\n\x15usb_storage_connected\x18\n \x01(\x08\x12\x16\n\x0e\x63\x61pture_status\x18\x0b \x01(\t\x12\x15\n\rupload_status\x18\x0c \x01(\t\x12\x14\n\x0csession_name\x18\r \x01(\t\x12\x39\n\x04step\x18\x0e \x01(\x0e\x32+.carbon.frontend.data_capture.ProcedureStep\x12\x0c\n\x04\x63rop\x18\x0f \x01(\t\x12\x15\n\rerror_message\x18\x10 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x11 \x01(\t\"\"\n\x12\x44\x61taCaptureSession\x12\x0c\n\x04name\x18\x01 \x01(\t\"j\n\x17StartDataCaptureRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04rate\x18\x02 \x01(\x01\x12\x0c\n\x04\x63rop\x18\x03 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x04 \x01(\t\x12\x14\n\x0csnap_capture\x18\x05 \x01(\x08\"\x94\x01\n\x11SnapImagesRequest\x12\x0c\n\x04\x63rop\x18\x01 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x02 \x01(\t\x12\x13\n\x06\x63\x61m_id\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x19\n\x0ctimestamp_ms\x18\x04 \x01(\x03H\x01\x88\x01\x01\x12\x14\n\x0csession_name\x18\x05 \x01(\tB\t\n\x07_cam_idB\x0f\n\r_timestamp_ms\"t\n\x07Session\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x18\n\x10images_remaining\x18\x02 \x01(\r\x12\x14\n\x0cis_uploading\x18\x03 \x01(\x08\x12\x15\n\rhas_completed\x18\x04 \x01(\x08\x12\x14\n\x0cis_capturing\x18\x05 \x01(\x08\"S\n\x18\x41vailableSessionResponse\x12\x37\n\x08sessions\x18\x01 \x03(\x0b\x32%.carbon.frontend.data_capture.Session\"\x1b\n\x0bSessionName\x12\x0c\n\x04name\x18\x01 \x01(\t\"W\n\x14RegularCaptureStatus\x12\x10\n\x08uploaded\x18\x01 \x01(\r\x12\x0e\n\x06\x62udget\x18\x02 \x01(\r\x12\x1d\n\x15last_upload_timestamp\x18\x03 \x01(\x03*%\n\x0cUploadMethod\x12\x0c\n\x08WIRELESS\x10\x00\x12\x07\n\x03USB\x10\x01*\xcd\x01\n\rProcedureStep\x12\x07\n\x03NEW\x10\x00\x12\r\n\tCAPTURING\x10\x01\x12\x12\n\x0e\x43\x41PTURE_PAUSED\x10\x02\x12\x14\n\x10\x43\x41PTURE_COMPLETE\x10\x03\x12\x16\n\x12UPLOADING_WIRELESS\x10\x04\x12\x1d\n\x19UPLOADING_WIRELESS_PAUSED\x10\x05\x12\x11\n\rUPLOADING_USB\x10\x06\x12\x18\n\x14UPLOADING_USB_PAUSED\x10\x07\x12\x16\n\x12UPLOADING_COMPLETE\x10\x08\x32\xa5\x0e\n\x12\x44\x61taCaptureService\x12\x66\n\x10StartDataCapture\x12\x35.carbon.frontend.data_capture.StartDataCaptureRequest\x1a\x1b.carbon.frontend.util.Empty\x12L\n\x10PauseDataCapture\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12K\n\x0fStopDataCapture\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12M\n\x11ResumeDataCapture\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12O\n\x13\x43ompleteDataCapture\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12Z\n\x1eStartDataCaptureWirelessUpload\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12U\n\x19StartDataCaptureUSBUpload\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12Q\n\x15StopDataCaptureUpload\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12R\n\x16PauseDataCaptureUpload\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12S\n\x17ResumeDataCaptureUpload\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12r\n(StartBackgroundDataCaptureWirelessUpload\x12).carbon.frontend.data_capture.SessionName\x1a\x1b.carbon.frontend.util.Empty\x12m\n#StartBackgroundDataCaptureUSBUpload\x12).carbon.frontend.data_capture.SessionName\x1a\x1b.carbon.frontend.util.Empty\x12i\n\x1fStopBackgroundDataCaptureUpload\x12).carbon.frontend.data_capture.SessionName\x1a\x1b.carbon.frontend.util.Empty\x12j\n PauseBackgroundDataCaptureUpload\x12).carbon.frontend.data_capture.SessionName\x1a\x1b.carbon.frontend.util.Empty\x12k\n!ResumeBackgroundDataCaptureUpload\x12).carbon.frontend.data_capture.SessionName\x1a\x1b.carbon.frontend.util.Empty\x12j\n\x17GetNextDataCaptureState\x12\x1f.carbon.frontend.util.Timestamp\x1a..carbon.frontend.data_capture.DataCaptureState\x12Z\n\nSnapImages\x12/.carbon.frontend.data_capture.SnapImagesRequest\x1a\x1b.carbon.frontend.util.Empty\x12\x62\n\x0bGetSessions\x12\x1b.carbon.frontend.util.Empty\x1a\x36.carbon.frontend.data_capture.AvailableSessionResponse\x12j\n\x17GetRegularCaptureStatus\x12\x1b.carbon.frontend.util.Empty\x1a\x32.carbon.frontend.data_capture.RegularCaptureStatusB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])

_UPLOADMETHOD = _descriptor.EnumDescriptor(
  name='UploadMethod',
  full_name='carbon.frontend.data_capture.UploadMethod',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='WIRELESS', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='USB', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1315,
  serialized_end=1352,
)
_sym_db.RegisterEnumDescriptor(_UPLOADMETHOD)

UploadMethod = enum_type_wrapper.EnumTypeWrapper(_UPLOADMETHOD)
_PROCEDURESTEP = _descriptor.EnumDescriptor(
  name='ProcedureStep',
  full_name='carbon.frontend.data_capture.ProcedureStep',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NEW', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CAPTURING', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CAPTURE_PAUSED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CAPTURE_COMPLETE', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UPLOADING_WIRELESS', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UPLOADING_WIRELESS_PAUSED', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UPLOADING_USB', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UPLOADING_USB_PAUSED', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UPLOADING_COMPLETE', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1355,
  serialized_end=1560,
)
_sym_db.RegisterEnumDescriptor(_PROCEDURESTEP)

ProcedureStep = enum_type_wrapper.EnumTypeWrapper(_PROCEDURESTEP)
WIRELESS = 0
USB = 1
NEW = 0
CAPTURING = 1
CAPTURE_PAUSED = 2
CAPTURE_COMPLETE = 3
UPLOADING_WIRELESS = 4
UPLOADING_WIRELESS_PAUSED = 5
UPLOADING_USB = 6
UPLOADING_USB_PAUSED = 7
UPLOADING_COMPLETE = 8



_DATACAPTURERATE = _descriptor.Descriptor(
  name='DataCaptureRate',
  full_name='carbon.frontend.data_capture.DataCaptureRate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='rate', full_name='carbon.frontend.data_capture.DataCaptureRate.rate', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=94,
  serialized_end=125,
)


_DATACAPTURESTATE = _descriptor.Descriptor(
  name='DataCaptureState',
  full_name='carbon.frontend.data_capture.DataCaptureState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.data_capture.DataCaptureState.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='images_taken', full_name='carbon.frontend.data_capture.DataCaptureState.images_taken', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_images_taken', full_name='carbon.frontend.data_capture.DataCaptureState.target_images_taken', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='estimated_capture_remaining_time_ms', full_name='carbon.frontend.data_capture.DataCaptureState.estimated_capture_remaining_time_ms', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='images_uploaded', full_name='carbon.frontend.data_capture.DataCaptureState.images_uploaded', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_images_uploaded', full_name='carbon.frontend.data_capture.DataCaptureState.target_images_uploaded', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='estimated_upload_remaining_time_ms', full_name='carbon.frontend.data_capture.DataCaptureState.estimated_upload_remaining_time_ms', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rate', full_name='carbon.frontend.data_capture.DataCaptureState.rate', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wireless_upload_available', full_name='carbon.frontend.data_capture.DataCaptureState.wireless_upload_available', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='usb_storage_connected', full_name='carbon.frontend.data_capture.DataCaptureState.usb_storage_connected', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='capture_status', full_name='carbon.frontend.data_capture.DataCaptureState.capture_status', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='upload_status', full_name='carbon.frontend.data_capture.DataCaptureState.upload_status', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='session_name', full_name='carbon.frontend.data_capture.DataCaptureState.session_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='step', full_name='carbon.frontend.data_capture.DataCaptureState.step', index=13,
      number=14, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop', full_name='carbon.frontend.data_capture.DataCaptureState.crop', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_message', full_name='carbon.frontend.data_capture.DataCaptureState.error_message', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.data_capture.DataCaptureState.crop_id', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=128,
  serialized_end=697,
)


_DATACAPTURESESSION = _descriptor.Descriptor(
  name='DataCaptureSession',
  full_name='carbon.frontend.data_capture.DataCaptureSession',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.data_capture.DataCaptureSession.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=699,
  serialized_end=733,
)


_STARTDATACAPTUREREQUEST = _descriptor.Descriptor(
  name='StartDataCaptureRequest',
  full_name='carbon.frontend.data_capture.StartDataCaptureRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.data_capture.StartDataCaptureRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rate', full_name='carbon.frontend.data_capture.StartDataCaptureRequest.rate', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop', full_name='carbon.frontend.data_capture.StartDataCaptureRequest.crop', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.data_capture.StartDataCaptureRequest.crop_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='snap_capture', full_name='carbon.frontend.data_capture.StartDataCaptureRequest.snap_capture', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=735,
  serialized_end=841,
)


_SNAPIMAGESREQUEST = _descriptor.Descriptor(
  name='SnapImagesRequest',
  full_name='carbon.frontend.data_capture.SnapImagesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='crop', full_name='carbon.frontend.data_capture.SnapImagesRequest.crop', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.data_capture.SnapImagesRequest.crop_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.data_capture.SnapImagesRequest.cam_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.frontend.data_capture.SnapImagesRequest.timestamp_ms', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='session_name', full_name='carbon.frontend.data_capture.SnapImagesRequest.session_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_cam_id', full_name='carbon.frontend.data_capture.SnapImagesRequest._cam_id',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_timestamp_ms', full_name='carbon.frontend.data_capture.SnapImagesRequest._timestamp_ms',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=844,
  serialized_end=992,
)


_SESSION = _descriptor.Descriptor(
  name='Session',
  full_name='carbon.frontend.data_capture.Session',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.data_capture.Session.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='images_remaining', full_name='carbon.frontend.data_capture.Session.images_remaining', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_uploading', full_name='carbon.frontend.data_capture.Session.is_uploading', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='has_completed', full_name='carbon.frontend.data_capture.Session.has_completed', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_capturing', full_name='carbon.frontend.data_capture.Session.is_capturing', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=994,
  serialized_end=1110,
)


_AVAILABLESESSIONRESPONSE = _descriptor.Descriptor(
  name='AvailableSessionResponse',
  full_name='carbon.frontend.data_capture.AvailableSessionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sessions', full_name='carbon.frontend.data_capture.AvailableSessionResponse.sessions', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1112,
  serialized_end=1195,
)


_SESSIONNAME = _descriptor.Descriptor(
  name='SessionName',
  full_name='carbon.frontend.data_capture.SessionName',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.data_capture.SessionName.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1197,
  serialized_end=1224,
)


_REGULARCAPTURESTATUS = _descriptor.Descriptor(
  name='RegularCaptureStatus',
  full_name='carbon.frontend.data_capture.RegularCaptureStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='uploaded', full_name='carbon.frontend.data_capture.RegularCaptureStatus.uploaded', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='budget', full_name='carbon.frontend.data_capture.RegularCaptureStatus.budget', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_upload_timestamp', full_name='carbon.frontend.data_capture.RegularCaptureStatus.last_upload_timestamp', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1226,
  serialized_end=1313,
)

_DATACAPTURESTATE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_DATACAPTURESTATE.fields_by_name['rate'].message_type = _DATACAPTURERATE
_DATACAPTURESTATE.fields_by_name['step'].enum_type = _PROCEDURESTEP
_SNAPIMAGESREQUEST.oneofs_by_name['_cam_id'].fields.append(
  _SNAPIMAGESREQUEST.fields_by_name['cam_id'])
_SNAPIMAGESREQUEST.fields_by_name['cam_id'].containing_oneof = _SNAPIMAGESREQUEST.oneofs_by_name['_cam_id']
_SNAPIMAGESREQUEST.oneofs_by_name['_timestamp_ms'].fields.append(
  _SNAPIMAGESREQUEST.fields_by_name['timestamp_ms'])
_SNAPIMAGESREQUEST.fields_by_name['timestamp_ms'].containing_oneof = _SNAPIMAGESREQUEST.oneofs_by_name['_timestamp_ms']
_AVAILABLESESSIONRESPONSE.fields_by_name['sessions'].message_type = _SESSION
DESCRIPTOR.message_types_by_name['DataCaptureRate'] = _DATACAPTURERATE
DESCRIPTOR.message_types_by_name['DataCaptureState'] = _DATACAPTURESTATE
DESCRIPTOR.message_types_by_name['DataCaptureSession'] = _DATACAPTURESESSION
DESCRIPTOR.message_types_by_name['StartDataCaptureRequest'] = _STARTDATACAPTUREREQUEST
DESCRIPTOR.message_types_by_name['SnapImagesRequest'] = _SNAPIMAGESREQUEST
DESCRIPTOR.message_types_by_name['Session'] = _SESSION
DESCRIPTOR.message_types_by_name['AvailableSessionResponse'] = _AVAILABLESESSIONRESPONSE
DESCRIPTOR.message_types_by_name['SessionName'] = _SESSIONNAME
DESCRIPTOR.message_types_by_name['RegularCaptureStatus'] = _REGULARCAPTURESTATUS
DESCRIPTOR.enum_types_by_name['UploadMethod'] = _UPLOADMETHOD
DESCRIPTOR.enum_types_by_name['ProcedureStep'] = _PROCEDURESTEP
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DataCaptureRate = _reflection.GeneratedProtocolMessageType('DataCaptureRate', (_message.Message,), {
  'DESCRIPTOR' : _DATACAPTURERATE,
  '__module__' : 'frontend.proto.data_capture_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.DataCaptureRate)
  })
_sym_db.RegisterMessage(DataCaptureRate)

DataCaptureState = _reflection.GeneratedProtocolMessageType('DataCaptureState', (_message.Message,), {
  'DESCRIPTOR' : _DATACAPTURESTATE,
  '__module__' : 'frontend.proto.data_capture_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.DataCaptureState)
  })
_sym_db.RegisterMessage(DataCaptureState)

DataCaptureSession = _reflection.GeneratedProtocolMessageType('DataCaptureSession', (_message.Message,), {
  'DESCRIPTOR' : _DATACAPTURESESSION,
  '__module__' : 'frontend.proto.data_capture_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.DataCaptureSession)
  })
_sym_db.RegisterMessage(DataCaptureSession)

StartDataCaptureRequest = _reflection.GeneratedProtocolMessageType('StartDataCaptureRequest', (_message.Message,), {
  'DESCRIPTOR' : _STARTDATACAPTUREREQUEST,
  '__module__' : 'frontend.proto.data_capture_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.StartDataCaptureRequest)
  })
_sym_db.RegisterMessage(StartDataCaptureRequest)

SnapImagesRequest = _reflection.GeneratedProtocolMessageType('SnapImagesRequest', (_message.Message,), {
  'DESCRIPTOR' : _SNAPIMAGESREQUEST,
  '__module__' : 'frontend.proto.data_capture_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.SnapImagesRequest)
  })
_sym_db.RegisterMessage(SnapImagesRequest)

Session = _reflection.GeneratedProtocolMessageType('Session', (_message.Message,), {
  'DESCRIPTOR' : _SESSION,
  '__module__' : 'frontend.proto.data_capture_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.Session)
  })
_sym_db.RegisterMessage(Session)

AvailableSessionResponse = _reflection.GeneratedProtocolMessageType('AvailableSessionResponse', (_message.Message,), {
  'DESCRIPTOR' : _AVAILABLESESSIONRESPONSE,
  '__module__' : 'frontend.proto.data_capture_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.AvailableSessionResponse)
  })
_sym_db.RegisterMessage(AvailableSessionResponse)

SessionName = _reflection.GeneratedProtocolMessageType('SessionName', (_message.Message,), {
  'DESCRIPTOR' : _SESSIONNAME,
  '__module__' : 'frontend.proto.data_capture_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.SessionName)
  })
_sym_db.RegisterMessage(SessionName)

RegularCaptureStatus = _reflection.GeneratedProtocolMessageType('RegularCaptureStatus', (_message.Message,), {
  'DESCRIPTOR' : _REGULARCAPTURESTATUS,
  '__module__' : 'frontend.proto.data_capture_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.RegularCaptureStatus)
  })
_sym_db.RegisterMessage(RegularCaptureStatus)


DESCRIPTOR._options = None

_DATACAPTURESERVICE = _descriptor.ServiceDescriptor(
  name='DataCaptureService',
  full_name='carbon.frontend.data_capture.DataCaptureService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1563,
  serialized_end=3392,
  methods=[
  _descriptor.MethodDescriptor(
    name='StartDataCapture',
    full_name='carbon.frontend.data_capture.DataCaptureService.StartDataCapture',
    index=0,
    containing_service=None,
    input_type=_STARTDATACAPTUREREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='PauseDataCapture',
    full_name='carbon.frontend.data_capture.DataCaptureService.PauseDataCapture',
    index=1,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StopDataCapture',
    full_name='carbon.frontend.data_capture.DataCaptureService.StopDataCapture',
    index=2,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ResumeDataCapture',
    full_name='carbon.frontend.data_capture.DataCaptureService.ResumeDataCapture',
    index=3,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CompleteDataCapture',
    full_name='carbon.frontend.data_capture.DataCaptureService.CompleteDataCapture',
    index=4,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartDataCaptureWirelessUpload',
    full_name='carbon.frontend.data_capture.DataCaptureService.StartDataCaptureWirelessUpload',
    index=5,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartDataCaptureUSBUpload',
    full_name='carbon.frontend.data_capture.DataCaptureService.StartDataCaptureUSBUpload',
    index=6,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StopDataCaptureUpload',
    full_name='carbon.frontend.data_capture.DataCaptureService.StopDataCaptureUpload',
    index=7,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='PauseDataCaptureUpload',
    full_name='carbon.frontend.data_capture.DataCaptureService.PauseDataCaptureUpload',
    index=8,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ResumeDataCaptureUpload',
    full_name='carbon.frontend.data_capture.DataCaptureService.ResumeDataCaptureUpload',
    index=9,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartBackgroundDataCaptureWirelessUpload',
    full_name='carbon.frontend.data_capture.DataCaptureService.StartBackgroundDataCaptureWirelessUpload',
    index=10,
    containing_service=None,
    input_type=_SESSIONNAME,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartBackgroundDataCaptureUSBUpload',
    full_name='carbon.frontend.data_capture.DataCaptureService.StartBackgroundDataCaptureUSBUpload',
    index=11,
    containing_service=None,
    input_type=_SESSIONNAME,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StopBackgroundDataCaptureUpload',
    full_name='carbon.frontend.data_capture.DataCaptureService.StopBackgroundDataCaptureUpload',
    index=12,
    containing_service=None,
    input_type=_SESSIONNAME,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='PauseBackgroundDataCaptureUpload',
    full_name='carbon.frontend.data_capture.DataCaptureService.PauseBackgroundDataCaptureUpload',
    index=13,
    containing_service=None,
    input_type=_SESSIONNAME,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ResumeBackgroundDataCaptureUpload',
    full_name='carbon.frontend.data_capture.DataCaptureService.ResumeBackgroundDataCaptureUpload',
    index=14,
    containing_service=None,
    input_type=_SESSIONNAME,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextDataCaptureState',
    full_name='carbon.frontend.data_capture.DataCaptureService.GetNextDataCaptureState',
    index=15,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_DATACAPTURESTATE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SnapImages',
    full_name='carbon.frontend.data_capture.DataCaptureService.SnapImages',
    index=16,
    containing_service=None,
    input_type=_SNAPIMAGESREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetSessions',
    full_name='carbon.frontend.data_capture.DataCaptureService.GetSessions',
    index=17,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_AVAILABLESESSIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetRegularCaptureStatus',
    full_name='carbon.frontend.data_capture.DataCaptureService.GetRegularCaptureStatus',
    index=18,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_REGULARCAPTURESTATUS,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_DATACAPTURESERVICE)

DESCRIPTOR.services_by_name['DataCaptureService'] = _DATACAPTURESERVICE

# @@protoc_insertion_point(module_scope)

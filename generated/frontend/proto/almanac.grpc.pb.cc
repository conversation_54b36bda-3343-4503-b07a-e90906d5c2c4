// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/almanac.proto

#include "frontend/proto/almanac.pb.h"
#include "frontend/proto/almanac.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace almanac {

static const char* AlmanacConfigService_method_names[] = {
  "/carbon.frontend.almanac.AlmanacConfigService/GetConfigData",
  "/carbon.frontend.almanac.AlmanacConfigService/GetNextConfigData",
  "/carbon.frontend.almanac.AlmanacConfigService/LoadAlmanacConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/SaveAlmanacConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/SetActiveAlmanacConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/DeleteAlmanacConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/GetNextAlmanacConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/LoadDiscriminatorConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/SaveDiscriminatorConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/SetActiveDiscriminatorConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/DeleteDiscriminatorConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/GetNextDiscriminatorConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/GetNextModelinatorConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/SaveModelinatorConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/FetchModelinatorConfig",
  "/carbon.frontend.almanac.AlmanacConfigService/ResetModelinatorConfig",
};

std::unique_ptr< AlmanacConfigService::Stub> AlmanacConfigService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< AlmanacConfigService::Stub> stub(new AlmanacConfigService::Stub(channel, options));
  return stub;
}

AlmanacConfigService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetConfigData_(AlmanacConfigService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextConfigData_(AlmanacConfigService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LoadAlmanacConfig_(AlmanacConfigService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SaveAlmanacConfig_(AlmanacConfigService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetActiveAlmanacConfig_(AlmanacConfigService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeleteAlmanacConfig_(AlmanacConfigService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextAlmanacConfig_(AlmanacConfigService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LoadDiscriminatorConfig_(AlmanacConfigService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SaveDiscriminatorConfig_(AlmanacConfigService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetActiveDiscriminatorConfig_(AlmanacConfigService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeleteDiscriminatorConfig_(AlmanacConfigService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextDiscriminatorConfig_(AlmanacConfigService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextModelinatorConfig_(AlmanacConfigService_method_names[12], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SaveModelinatorConfig_(AlmanacConfigService_method_names[13], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_FetchModelinatorConfig_(AlmanacConfigService_method_names[14], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ResetModelinatorConfig_(AlmanacConfigService_method_names[15], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status AlmanacConfigService::Stub::GetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::almanac::GetConfigDataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::GetConfigDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetConfigData_, context, request, response);
}

void AlmanacConfigService::Stub::async::GetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::almanac::GetConfigDataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::GetConfigDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConfigData_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::GetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::almanac::GetConfigDataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConfigData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetConfigDataResponse>* AlmanacConfigService::Stub::PrepareAsyncGetConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::almanac::GetConfigDataResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetConfigData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetConfigDataResponse>* AlmanacConfigService::Stub::AsyncGetConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetConfigDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::GetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::GetNextConfigDataRequest, ::carbon::frontend::almanac::GetNextConfigDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextConfigData_, context, request, response);
}

void AlmanacConfigService::Stub::async::GetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest* request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::GetNextConfigDataRequest, ::carbon::frontend::almanac::GetNextConfigDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextConfigData_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::GetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest* request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextConfigData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextConfigDataResponse>* AlmanacConfigService::Stub::PrepareAsyncGetNextConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::almanac::GetNextConfigDataResponse, ::carbon::frontend::almanac::GetNextConfigDataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextConfigData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextConfigDataResponse>* AlmanacConfigService::Stub::AsyncGetNextConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextConfigDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::LoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::LoadAlmanacConfigRequest, ::carbon::frontend::almanac::LoadAlmanacConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LoadAlmanacConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::LoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::LoadAlmanacConfigRequest, ::carbon::frontend::almanac::LoadAlmanacConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LoadAlmanacConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::LoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LoadAlmanacConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>* AlmanacConfigService::Stub::PrepareAsyncLoadAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::almanac::LoadAlmanacConfigResponse, ::carbon::frontend::almanac::LoadAlmanacConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LoadAlmanacConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>* AlmanacConfigService::Stub::AsyncLoadAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLoadAlmanacConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::SaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::SaveAlmanacConfigRequest, ::carbon::frontend::almanac::SaveAlmanacConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SaveAlmanacConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::SaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::SaveAlmanacConfigRequest, ::carbon::frontend::almanac::SaveAlmanacConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveAlmanacConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::SaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveAlmanacConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>* AlmanacConfigService::Stub::PrepareAsyncSaveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::almanac::SaveAlmanacConfigResponse, ::carbon::frontend::almanac::SaveAlmanacConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SaveAlmanacConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>* AlmanacConfigService::Stub::AsyncSaveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSaveAlmanacConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::SetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetActiveAlmanacConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::SetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActiveAlmanacConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::SetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActiveAlmanacConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::PrepareAsyncSetActiveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetActiveAlmanacConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::AsyncSetActiveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetActiveAlmanacConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::DeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::DeleteAlmanacConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DeleteAlmanacConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::DeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::DeleteAlmanacConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteAlmanacConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::DeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteAlmanacConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::PrepareAsyncDeleteAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::DeleteAlmanacConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DeleteAlmanacConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::AsyncDeleteAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDeleteAlmanacConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::GetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextAlmanacConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::GetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAlmanacConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::GetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAlmanacConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>* AlmanacConfigService::Stub::PrepareAsyncGetNextAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextAlmanacConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>* AlmanacConfigService::Stub::AsyncGetNextAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextAlmanacConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::LoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LoadDiscriminatorConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::LoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LoadDiscriminatorConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::LoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LoadDiscriminatorConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>* AlmanacConfigService::Stub::PrepareAsyncLoadDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse, ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LoadDiscriminatorConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>* AlmanacConfigService::Stub::AsyncLoadDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLoadDiscriminatorConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::SaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SaveDiscriminatorConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::SaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveDiscriminatorConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::SaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveDiscriminatorConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>* AlmanacConfigService::Stub::PrepareAsyncSaveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse, ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SaveDiscriminatorConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>* AlmanacConfigService::Stub::AsyncSaveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSaveDiscriminatorConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::SetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetActiveDiscriminatorConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::SetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActiveDiscriminatorConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::SetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActiveDiscriminatorConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::PrepareAsyncSetActiveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetActiveDiscriminatorConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::AsyncSetActiveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetActiveDiscriminatorConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::DeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DeleteDiscriminatorConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::DeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteDiscriminatorConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::DeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteDiscriminatorConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::PrepareAsyncDeleteDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DeleteDiscriminatorConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::AsyncDeleteDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDeleteDiscriminatorConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::GetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextDiscriminatorConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::GetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextDiscriminatorConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::GetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextDiscriminatorConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>* AlmanacConfigService::Stub::PrepareAsyncGetNextDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextDiscriminatorConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>* AlmanacConfigService::Stub::AsyncGetNextDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextDiscriminatorConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::GetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextModelinatorConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::GetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextModelinatorConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::GetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextModelinatorConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>* AlmanacConfigService::Stub::PrepareAsyncGetNextModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextModelinatorConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>* AlmanacConfigService::Stub::AsyncGetNextModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextModelinatorConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::SaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::SaveModelinatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SaveModelinatorConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::SaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::SaveModelinatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveModelinatorConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::SaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveModelinatorConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::PrepareAsyncSaveModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::SaveModelinatorConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SaveModelinatorConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::AsyncSaveModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSaveModelinatorConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::FetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::FetchModelinatorConfigRequest, ::carbon::frontend::almanac::FetchModelinatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_FetchModelinatorConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::FetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::FetchModelinatorConfigRequest, ::carbon::frontend::almanac::FetchModelinatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FetchModelinatorConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::FetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FetchModelinatorConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>* AlmanacConfigService::Stub::PrepareAsyncFetchModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::almanac::FetchModelinatorConfigResponse, ::carbon::frontend::almanac::FetchModelinatorConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_FetchModelinatorConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>* AlmanacConfigService::Stub::AsyncFetchModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncFetchModelinatorConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlmanacConfigService::Stub::ResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::almanac::ResetModelinatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ResetModelinatorConfig_, context, request, response);
}

void AlmanacConfigService::Stub::async::ResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::almanac::ResetModelinatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetModelinatorConfig_, context, request, response, std::move(f));
}

void AlmanacConfigService::Stub::async::ResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetModelinatorConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::PrepareAsyncResetModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::ResetModelinatorConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ResetModelinatorConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlmanacConfigService::Stub::AsyncResetModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncResetModelinatorConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

AlmanacConfigService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::GetConfigDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::almanac::GetConfigDataResponse* resp) {
               return service->GetConfigData(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::GetNextConfigDataRequest, ::carbon::frontend::almanac::GetNextConfigDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::GetNextConfigDataRequest* req,
             ::carbon::frontend::almanac::GetNextConfigDataResponse* resp) {
               return service->GetNextConfigData(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::LoadAlmanacConfigRequest, ::carbon::frontend::almanac::LoadAlmanacConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* req,
             ::carbon::frontend::almanac::LoadAlmanacConfigResponse* resp) {
               return service->LoadAlmanacConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::SaveAlmanacConfigRequest, ::carbon::frontend::almanac::SaveAlmanacConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* req,
             ::carbon::frontend::almanac::SaveAlmanacConfigResponse* resp) {
               return service->SaveAlmanacConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetActiveAlmanacConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::DeleteAlmanacConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->DeleteAlmanacConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* resp) {
               return service->GetNextAlmanacConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* req,
             ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* resp) {
               return service->LoadDiscriminatorConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* req,
             ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* resp) {
               return service->SaveDiscriminatorConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetActiveDiscriminatorConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->DeleteDiscriminatorConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* resp) {
               return service->GetNextDiscriminatorConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* resp) {
               return service->GetNextModelinatorConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[13],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::SaveModelinatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SaveModelinatorConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[14],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::FetchModelinatorConfigRequest, ::carbon::frontend::almanac::FetchModelinatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* req,
             ::carbon::frontend::almanac::FetchModelinatorConfigResponse* resp) {
               return service->FetchModelinatorConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlmanacConfigService_method_names[15],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlmanacConfigService::Service, ::carbon::frontend::almanac::ResetModelinatorConfigRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlmanacConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ResetModelinatorConfig(ctx, req, resp);
             }, this)));
}

AlmanacConfigService::Service::~Service() {
}

::grpc::Status AlmanacConfigService::Service::GetConfigData(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::almanac::GetConfigDataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::GetNextConfigData(::grpc::ServerContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest* request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::LoadAlmanacConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::SaveAlmanacConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::SetActiveAlmanacConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::DeleteAlmanacConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::GetNextAlmanacConfig(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::LoadDiscriminatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::SaveDiscriminatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::SetActiveDiscriminatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::DeleteDiscriminatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::GetNextDiscriminatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::GetNextModelinatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::SaveModelinatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::FetchModelinatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlmanacConfigService::Service::ResetModelinatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace almanac


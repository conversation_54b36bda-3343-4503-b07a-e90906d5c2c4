# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.core.controls.exterminator.controllers.aimbot.process.proto import aimbot_pb2 as core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2
from generated.frontend.proto import banding_pb2 as frontend_dot_proto_dot_banding__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class BandingServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.LoadBandingDefs = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/LoadBandingDefs',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_banding__pb2.LoadBandingDefsResponse.FromString,
                )
        self.SaveBandingDef = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/SaveBandingDef',
                request_serializer=frontend_dot_proto_dot_banding__pb2.SaveBandingDefRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.DeleteBandingDef = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/DeleteBandingDef',
                request_serializer=frontend_dot_proto_dot_banding__pb2.DeleteBandingDefRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.SetActiveBandingDef = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/SetActiveBandingDef',
                request_serializer=frontend_dot_proto_dot_banding__pb2.SetActiveBandingDefRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetActiveBandingDef = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/GetActiveBandingDef',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_banding__pb2.GetActiveBandingDefResponse.FromString,
                )
        self.GetNextVisualizationData = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/GetNextVisualizationData',
                request_serializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataResponse.FromString,
                )
        self.GetNextVisualizationData2 = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/GetNextVisualizationData2',
                request_serializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationData2Response.FromString,
                )
        self.GetNextVisualizationDataForAllRows = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/GetNextVisualizationDataForAllRows',
                request_serializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataForAllRowsRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataForAllRowsResponse.FromString,
                )
        self.GetDimensions = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/GetDimensions',
                request_serializer=frontend_dot_proto_dot_banding__pb2.GetDimensionsRequest.SerializeToString,
                response_deserializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2.GetDimensionsResponse.FromString,
                )
        self.SetBandingEnabled = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/SetBandingEnabled',
                request_serializer=frontend_dot_proto_dot_banding__pb2.SetBandingEnabledRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_banding__pb2.SetBandingEnabledResponse.FromString,
                )
        self.IsBandingEnabled = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/IsBandingEnabled',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_banding__pb2.IsBandingEnabledResponse.FromString,
                )
        self.SetDynamicBandingEnabled = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/SetDynamicBandingEnabled',
                request_serializer=frontend_dot_proto_dot_banding__pb2.SetBandingEnabledRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_banding__pb2.SetBandingEnabledResponse.FromString,
                )
        self.IsDynamicBandingEnabled = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/IsDynamicBandingEnabled',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_banding__pb2.IsBandingEnabledResponse.FromString,
                )
        self.GetVisualizationMetadata = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/GetVisualizationMetadata',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_banding__pb2.GetVisualizationMetadataResponse.FromString,
                )
        self.GetNextBandingState = channel.unary_unary(
                '/carbon.frontend.banding.BandingService/GetNextBandingState',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_banding__pb2.GetNextBandingStateResponse.FromString,
                )


class BandingServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def LoadBandingDefs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveBandingDef(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteBandingDef(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetActiveBandingDef(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetActiveBandingDef(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextVisualizationData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextVisualizationData2(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextVisualizationDataForAllRows(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDimensions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetBandingEnabled(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IsBandingEnabled(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetDynamicBandingEnabled(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IsDynamicBandingEnabled(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetVisualizationMetadata(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextBandingState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_BandingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'LoadBandingDefs': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadBandingDefs,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_banding__pb2.LoadBandingDefsResponse.SerializeToString,
            ),
            'SaveBandingDef': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveBandingDef,
                    request_deserializer=frontend_dot_proto_dot_banding__pb2.SaveBandingDefRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'DeleteBandingDef': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteBandingDef,
                    request_deserializer=frontend_dot_proto_dot_banding__pb2.DeleteBandingDefRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'SetActiveBandingDef': grpc.unary_unary_rpc_method_handler(
                    servicer.SetActiveBandingDef,
                    request_deserializer=frontend_dot_proto_dot_banding__pb2.SetActiveBandingDefRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetActiveBandingDef': grpc.unary_unary_rpc_method_handler(
                    servicer.GetActiveBandingDef,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_banding__pb2.GetActiveBandingDefResponse.SerializeToString,
            ),
            'GetNextVisualizationData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextVisualizationData,
                    request_deserializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataResponse.SerializeToString,
            ),
            'GetNextVisualizationData2': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextVisualizationData2,
                    request_deserializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationData2Response.SerializeToString,
            ),
            'GetNextVisualizationDataForAllRows': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextVisualizationDataForAllRows,
                    request_deserializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataForAllRowsRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataForAllRowsResponse.SerializeToString,
            ),
            'GetDimensions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDimensions,
                    request_deserializer=frontend_dot_proto_dot_banding__pb2.GetDimensionsRequest.FromString,
                    response_serializer=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2.GetDimensionsResponse.SerializeToString,
            ),
            'SetBandingEnabled': grpc.unary_unary_rpc_method_handler(
                    servicer.SetBandingEnabled,
                    request_deserializer=frontend_dot_proto_dot_banding__pb2.SetBandingEnabledRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_banding__pb2.SetBandingEnabledResponse.SerializeToString,
            ),
            'IsBandingEnabled': grpc.unary_unary_rpc_method_handler(
                    servicer.IsBandingEnabled,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_banding__pb2.IsBandingEnabledResponse.SerializeToString,
            ),
            'SetDynamicBandingEnabled': grpc.unary_unary_rpc_method_handler(
                    servicer.SetDynamicBandingEnabled,
                    request_deserializer=frontend_dot_proto_dot_banding__pb2.SetBandingEnabledRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_banding__pb2.SetBandingEnabledResponse.SerializeToString,
            ),
            'IsDynamicBandingEnabled': grpc.unary_unary_rpc_method_handler(
                    servicer.IsDynamicBandingEnabled,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_banding__pb2.IsBandingEnabledResponse.SerializeToString,
            ),
            'GetVisualizationMetadata': grpc.unary_unary_rpc_method_handler(
                    servicer.GetVisualizationMetadata,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_banding__pb2.GetVisualizationMetadataResponse.SerializeToString,
            ),
            'GetNextBandingState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextBandingState,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_banding__pb2.GetNextBandingStateResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.banding.BandingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class BandingService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def LoadBandingDefs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/LoadBandingDefs',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_banding__pb2.LoadBandingDefsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SaveBandingDef(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/SaveBandingDef',
            frontend_dot_proto_dot_banding__pb2.SaveBandingDefRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteBandingDef(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/DeleteBandingDef',
            frontend_dot_proto_dot_banding__pb2.DeleteBandingDefRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetActiveBandingDef(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/SetActiveBandingDef',
            frontend_dot_proto_dot_banding__pb2.SetActiveBandingDefRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetActiveBandingDef(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/GetActiveBandingDef',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_banding__pb2.GetActiveBandingDefResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextVisualizationData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/GetNextVisualizationData',
            frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataRequest.SerializeToString,
            frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextVisualizationData2(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/GetNextVisualizationData2',
            frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataRequest.SerializeToString,
            frontend_dot_proto_dot_banding__pb2.GetNextVisualizationData2Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextVisualizationDataForAllRows(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/GetNextVisualizationDataForAllRows',
            frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataForAllRowsRequest.SerializeToString,
            frontend_dot_proto_dot_banding__pb2.GetNextVisualizationDataForAllRowsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDimensions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/GetDimensions',
            frontend_dot_proto_dot_banding__pb2.GetDimensionsRequest.SerializeToString,
            core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2.GetDimensionsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetBandingEnabled(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/SetBandingEnabled',
            frontend_dot_proto_dot_banding__pb2.SetBandingEnabledRequest.SerializeToString,
            frontend_dot_proto_dot_banding__pb2.SetBandingEnabledResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def IsBandingEnabled(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/IsBandingEnabled',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_banding__pb2.IsBandingEnabledResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetDynamicBandingEnabled(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/SetDynamicBandingEnabled',
            frontend_dot_proto_dot_banding__pb2.SetBandingEnabledRequest.SerializeToString,
            frontend_dot_proto_dot_banding__pb2.SetBandingEnabledResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def IsDynamicBandingEnabled(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/IsDynamicBandingEnabled',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_banding__pb2.IsBandingEnabledResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetVisualizationMetadata(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/GetVisualizationMetadata',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_banding__pb2.GetVisualizationMetadataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextBandingState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.banding.BandingService/GetNextBandingState',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_banding__pb2.GetNextBandingStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

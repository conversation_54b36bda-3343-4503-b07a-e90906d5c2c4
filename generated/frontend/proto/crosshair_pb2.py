# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/crosshair.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import camera_pb2 as frontend_dot_proto_dot_camera__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/crosshair.proto',
  package='carbon.frontend.crosshair',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1e\x66rontend/proto/crosshair.proto\x12\x19\x63\x61rbon.frontend.crosshair\x1a\x1b\x66rontend/proto/camera.proto\x1a\x19\x66rontend/proto/util.proto\")\n\x11\x43rosshairPosition\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\"\xb1\x01\n\x16\x43rosshairPositionState\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x39\n\x03pos\x18\x02 \x01(\x0b\x32,.carbon.frontend.crosshair.CrosshairPosition\x12\x13\n\x0b\x63\x61librating\x18\x03 \x01(\x08\x12\x1a\n\x12\x63\x61libration_failed\x18\x04 \x01(\x08\"W\n\x18\x43rosshairPositionRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"h\n\x1bSetCrosshairPositionRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x39\n\x03pos\x18\x02 \x01(\x0b\x32,.carbon.frontend.crosshair.CrosshairPosition\":\n\x12MoveScannerRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\t\n\x01x\x18\x02 \x01(\x02\x12\t\n\x01y\x18\x03 \x01(\x02\"K\n\x1c\x41utoCrossHairCalStateRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"\x83\x01\n\x1d\x41utoCrossHairCalStateResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x13\n\x0bin_progress\x18\x02 \x01(\x08\x12\x10\n\x08progress\x18\x03 \x01(\x02\x12\x0e\n\x06\x66\x61iled\x18\x04 \x03(\t2\xfe\x05\n\x10\x43rosshairService\x12\x61\n\x1bStartAutoCalibrateCrosshair\x12%.carbon.frontend.camera.CameraRequest\x1a\x1b.carbon.frontend.util.Empty\x12[\n\x1fStartAutoCalibrateAllCrosshairs\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12M\n\x11StopAutoCalibrate\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12\x7f\n\x15GetNextCrosshairState\x12\x33.carbon.frontend.crosshair.CrosshairPositionRequest\x1a\x31.carbon.frontend.crosshair.CrosshairPositionState\x12k\n\x14SetCrosshairPosition\x12\x36.carbon.frontend.crosshair.SetCrosshairPositionRequest\x1a\x1b.carbon.frontend.util.Empty\x12Y\n\x0bMoveScanner\x12-.carbon.frontend.crosshair.MoveScannerRequest\x1a\x1b.carbon.frontend.util.Empty\x12\x91\x01\n\x1cGetNextAutoCrossHairCalState\x12\x37.carbon.frontend.crosshair.AutoCrossHairCalStateRequest\x1a\x38.carbon.frontend.crosshair.AutoCrossHairCalStateResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_camera__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_CROSSHAIRPOSITION = _descriptor.Descriptor(
  name='CrosshairPosition',
  full_name='carbon.frontend.crosshair.CrosshairPosition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='carbon.frontend.crosshair.CrosshairPosition.x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='carbon.frontend.crosshair.CrosshairPosition.y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=117,
  serialized_end=158,
)


_CROSSHAIRPOSITIONSTATE = _descriptor.Descriptor(
  name='CrosshairPositionState',
  full_name='carbon.frontend.crosshair.CrosshairPositionState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.crosshair.CrosshairPositionState.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pos', full_name='carbon.frontend.crosshair.CrosshairPositionState.pos', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='calibrating', full_name='carbon.frontend.crosshair.CrosshairPositionState.calibrating', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='calibration_failed', full_name='carbon.frontend.crosshair.CrosshairPositionState.calibration_failed', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=161,
  serialized_end=338,
)


_CROSSHAIRPOSITIONREQUEST = _descriptor.Descriptor(
  name='CrosshairPositionRequest',
  full_name='carbon.frontend.crosshair.CrosshairPositionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.crosshair.CrosshairPositionRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.crosshair.CrosshairPositionRequest.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=340,
  serialized_end=427,
)


_SETCROSSHAIRPOSITIONREQUEST = _descriptor.Descriptor(
  name='SetCrosshairPositionRequest',
  full_name='carbon.frontend.crosshair.SetCrosshairPositionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.crosshair.SetCrosshairPositionRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pos', full_name='carbon.frontend.crosshair.SetCrosshairPositionRequest.pos', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=429,
  serialized_end=533,
)


_MOVESCANNERREQUEST = _descriptor.Descriptor(
  name='MoveScannerRequest',
  full_name='carbon.frontend.crosshair.MoveScannerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.crosshair.MoveScannerRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x', full_name='carbon.frontend.crosshair.MoveScannerRequest.x', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='carbon.frontend.crosshair.MoveScannerRequest.y', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=535,
  serialized_end=593,
)


_AUTOCROSSHAIRCALSTATEREQUEST = _descriptor.Descriptor(
  name='AutoCrossHairCalStateRequest',
  full_name='carbon.frontend.crosshair.AutoCrossHairCalStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.crosshair.AutoCrossHairCalStateRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=595,
  serialized_end=670,
)


_AUTOCROSSHAIRCALSTATERESPONSE = _descriptor.Descriptor(
  name='AutoCrossHairCalStateResponse',
  full_name='carbon.frontend.crosshair.AutoCrossHairCalStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.crosshair.AutoCrossHairCalStateResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='in_progress', full_name='carbon.frontend.crosshair.AutoCrossHairCalStateResponse.in_progress', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='progress', full_name='carbon.frontend.crosshair.AutoCrossHairCalStateResponse.progress', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='failed', full_name='carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=673,
  serialized_end=804,
)

_CROSSHAIRPOSITIONSTATE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_CROSSHAIRPOSITIONSTATE.fields_by_name['pos'].message_type = _CROSSHAIRPOSITION
_CROSSHAIRPOSITIONREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_SETCROSSHAIRPOSITIONREQUEST.fields_by_name['pos'].message_type = _CROSSHAIRPOSITION
_AUTOCROSSHAIRCALSTATEREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_AUTOCROSSHAIRCALSTATERESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['CrosshairPosition'] = _CROSSHAIRPOSITION
DESCRIPTOR.message_types_by_name['CrosshairPositionState'] = _CROSSHAIRPOSITIONSTATE
DESCRIPTOR.message_types_by_name['CrosshairPositionRequest'] = _CROSSHAIRPOSITIONREQUEST
DESCRIPTOR.message_types_by_name['SetCrosshairPositionRequest'] = _SETCROSSHAIRPOSITIONREQUEST
DESCRIPTOR.message_types_by_name['MoveScannerRequest'] = _MOVESCANNERREQUEST
DESCRIPTOR.message_types_by_name['AutoCrossHairCalStateRequest'] = _AUTOCROSSHAIRCALSTATEREQUEST
DESCRIPTOR.message_types_by_name['AutoCrossHairCalStateResponse'] = _AUTOCROSSHAIRCALSTATERESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CrosshairPosition = _reflection.GeneratedProtocolMessageType('CrosshairPosition', (_message.Message,), {
  'DESCRIPTOR' : _CROSSHAIRPOSITION,
  '__module__' : 'frontend.proto.crosshair_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.CrosshairPosition)
  })
_sym_db.RegisterMessage(CrosshairPosition)

CrosshairPositionState = _reflection.GeneratedProtocolMessageType('CrosshairPositionState', (_message.Message,), {
  'DESCRIPTOR' : _CROSSHAIRPOSITIONSTATE,
  '__module__' : 'frontend.proto.crosshair_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.CrosshairPositionState)
  })
_sym_db.RegisterMessage(CrosshairPositionState)

CrosshairPositionRequest = _reflection.GeneratedProtocolMessageType('CrosshairPositionRequest', (_message.Message,), {
  'DESCRIPTOR' : _CROSSHAIRPOSITIONREQUEST,
  '__module__' : 'frontend.proto.crosshair_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.CrosshairPositionRequest)
  })
_sym_db.RegisterMessage(CrosshairPositionRequest)

SetCrosshairPositionRequest = _reflection.GeneratedProtocolMessageType('SetCrosshairPositionRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETCROSSHAIRPOSITIONREQUEST,
  '__module__' : 'frontend.proto.crosshair_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.SetCrosshairPositionRequest)
  })
_sym_db.RegisterMessage(SetCrosshairPositionRequest)

MoveScannerRequest = _reflection.GeneratedProtocolMessageType('MoveScannerRequest', (_message.Message,), {
  'DESCRIPTOR' : _MOVESCANNERREQUEST,
  '__module__' : 'frontend.proto.crosshair_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.MoveScannerRequest)
  })
_sym_db.RegisterMessage(MoveScannerRequest)

AutoCrossHairCalStateRequest = _reflection.GeneratedProtocolMessageType('AutoCrossHairCalStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _AUTOCROSSHAIRCALSTATEREQUEST,
  '__module__' : 'frontend.proto.crosshair_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.AutoCrossHairCalStateRequest)
  })
_sym_db.RegisterMessage(AutoCrossHairCalStateRequest)

AutoCrossHairCalStateResponse = _reflection.GeneratedProtocolMessageType('AutoCrossHairCalStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _AUTOCROSSHAIRCALSTATERESPONSE,
  '__module__' : 'frontend.proto.crosshair_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.AutoCrossHairCalStateResponse)
  })
_sym_db.RegisterMessage(AutoCrossHairCalStateResponse)


DESCRIPTOR._options = None

_CROSSHAIRSERVICE = _descriptor.ServiceDescriptor(
  name='CrosshairService',
  full_name='carbon.frontend.crosshair.CrosshairService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=807,
  serialized_end=1573,
  methods=[
  _descriptor.MethodDescriptor(
    name='StartAutoCalibrateCrosshair',
    full_name='carbon.frontend.crosshair.CrosshairService.StartAutoCalibrateCrosshair',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_camera__pb2._CAMERAREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartAutoCalibrateAllCrosshairs',
    full_name='carbon.frontend.crosshair.CrosshairService.StartAutoCalibrateAllCrosshairs',
    index=1,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StopAutoCalibrate',
    full_name='carbon.frontend.crosshair.CrosshairService.StopAutoCalibrate',
    index=2,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextCrosshairState',
    full_name='carbon.frontend.crosshair.CrosshairService.GetNextCrosshairState',
    index=3,
    containing_service=None,
    input_type=_CROSSHAIRPOSITIONREQUEST,
    output_type=_CROSSHAIRPOSITIONSTATE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetCrosshairPosition',
    full_name='carbon.frontend.crosshair.CrosshairService.SetCrosshairPosition',
    index=4,
    containing_service=None,
    input_type=_SETCROSSHAIRPOSITIONREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='MoveScanner',
    full_name='carbon.frontend.crosshair.CrosshairService.MoveScanner',
    index=5,
    containing_service=None,
    input_type=_MOVESCANNERREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextAutoCrossHairCalState',
    full_name='carbon.frontend.crosshair.CrosshairService.GetNextAutoCrossHairCalState',
    index=6,
    containing_service=None,
    input_type=_AUTOCROSSHAIRCALSTATEREQUEST,
    output_type=_AUTOCROSSHAIRCALSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CROSSHAIRSERVICE)

DESCRIPTOR.services_by_name['CrosshairService'] = _CROSSHAIRSERVICE

# @@protoc_insertion_point(module_scope)

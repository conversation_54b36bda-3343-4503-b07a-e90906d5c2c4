# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import camera_pb2 as frontend_dot_proto_dot_camera__pb2
from generated.frontend.proto import laser_pb2 as frontend_dot_proto_dot_laser__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class LaserServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.FireLaser = channel.stream_unary(
                '/carbon.frontend.laser.LaserService/FireLaser',
                request_serializer=frontend_dot_proto_dot_camera__pb2.CameraRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetNextLaserState = channel.unary_unary(
                '/carbon.frontend.laser.LaserService/GetNextLaserState',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_laser__pb2.LaserStateList.FromString,
                )
        self.ToggleLaserEnabled = channel.unary_unary(
                '/carbon.frontend.laser.LaserService/ToggleLaserEnabled',
                request_serializer=frontend_dot_proto_dot_laser__pb2.LaserDescriptor.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.EnableRow = channel.unary_unary(
                '/carbon.frontend.laser.LaserService/EnableRow',
                request_serializer=frontend_dot_proto_dot_laser__pb2.RowRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.DisableRow = channel.unary_unary(
                '/carbon.frontend.laser.LaserService/DisableRow',
                request_serializer=frontend_dot_proto_dot_laser__pb2.RowRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.ResetLaserMetrics = channel.unary_unary(
                '/carbon.frontend.laser.LaserService/ResetLaserMetrics',
                request_serializer=frontend_dot_proto_dot_laser__pb2.LaserDescriptor.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.FixLaserMetrics = channel.unary_unary(
                '/carbon.frontend.laser.LaserService/FixLaserMetrics',
                request_serializer=frontend_dot_proto_dot_laser__pb2.FixLaserMetricsRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.SetLaserPower = channel.unary_unary(
                '/carbon.frontend.laser.LaserService/SetLaserPower',
                request_serializer=frontend_dot_proto_dot_laser__pb2.SetLaserPowerRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )


class LaserServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def FireLaser(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextLaserState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ToggleLaserEnabled(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EnableRow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DisableRow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResetLaserMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FixLaserMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetLaserPower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LaserServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'FireLaser': grpc.stream_unary_rpc_method_handler(
                    servicer.FireLaser,
                    request_deserializer=frontend_dot_proto_dot_camera__pb2.CameraRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextLaserState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextLaserState,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_laser__pb2.LaserStateList.SerializeToString,
            ),
            'ToggleLaserEnabled': grpc.unary_unary_rpc_method_handler(
                    servicer.ToggleLaserEnabled,
                    request_deserializer=frontend_dot_proto_dot_laser__pb2.LaserDescriptor.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'EnableRow': grpc.unary_unary_rpc_method_handler(
                    servicer.EnableRow,
                    request_deserializer=frontend_dot_proto_dot_laser__pb2.RowRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'DisableRow': grpc.unary_unary_rpc_method_handler(
                    servicer.DisableRow,
                    request_deserializer=frontend_dot_proto_dot_laser__pb2.RowRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'ResetLaserMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.ResetLaserMetrics,
                    request_deserializer=frontend_dot_proto_dot_laser__pb2.LaserDescriptor.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'FixLaserMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.FixLaserMetrics,
                    request_deserializer=frontend_dot_proto_dot_laser__pb2.FixLaserMetricsRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'SetLaserPower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetLaserPower,
                    request_deserializer=frontend_dot_proto_dot_laser__pb2.SetLaserPowerRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.laser.LaserService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class LaserService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def FireLaser(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_unary(request_iterator, target, '/carbon.frontend.laser.LaserService/FireLaser',
            frontend_dot_proto_dot_camera__pb2.CameraRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextLaserState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.laser.LaserService/GetNextLaserState',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_laser__pb2.LaserStateList.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ToggleLaserEnabled(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.laser.LaserService/ToggleLaserEnabled',
            frontend_dot_proto_dot_laser__pb2.LaserDescriptor.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EnableRow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.laser.LaserService/EnableRow',
            frontend_dot_proto_dot_laser__pb2.RowRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DisableRow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.laser.LaserService/DisableRow',
            frontend_dot_proto_dot_laser__pb2.RowRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ResetLaserMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.laser.LaserService/ResetLaserMetrics',
            frontend_dot_proto_dot_laser__pb2.LaserDescriptor.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FixLaserMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.laser.LaserService/FixLaserMetrics',
            frontend_dot_proto_dot_laser__pb2.FixLaserMetricsRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetLaserPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.laser.LaserService/SetLaserPower',
            frontend_dot_proto_dot_laser__pb2.SetLaserPowerRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/almanac.proto
#ifndef GRPC_frontend_2fproto_2falmanac_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2falmanac_2eproto__INCLUDED

#include "frontend/proto/almanac.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace almanac {

class AlmanacConfigService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.almanac.AlmanacConfigService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::almanac::GetConfigDataResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetConfigDataResponse>> AsyncGetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetConfigDataResponse>>(AsyncGetConfigDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetConfigDataResponse>> PrepareAsyncGetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetConfigDataResponse>>(PrepareAsyncGetConfigDataRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextConfigDataResponse>> AsyncGetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextConfigDataResponse>>(AsyncGetNextConfigDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextConfigDataResponse>> PrepareAsyncGetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextConfigDataResponse>>(PrepareAsyncGetNextConfigDataRaw(context, request, cq));
    }
    virtual ::grpc::Status LoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>> AsyncLoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>>(AsyncLoadAlmanacConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>> PrepareAsyncLoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>>(PrepareAsyncLoadAlmanacConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status SaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>> AsyncSaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>>(AsyncSaveAlmanacConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>> PrepareAsyncSaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>>(PrepareAsyncSaveAlmanacConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status SetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetActiveAlmanacConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetActiveAlmanacConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status DeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncDeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncDeleteAlmanacConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteAlmanacConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>> AsyncGetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>>(AsyncGetNextAlmanacConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>> PrepareAsyncGetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>>(PrepareAsyncGetNextAlmanacConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status LoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>> AsyncLoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>>(AsyncLoadDiscriminatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>> PrepareAsyncLoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>>(PrepareAsyncLoadDiscriminatorConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status SaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>> AsyncSaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>>(AsyncSaveDiscriminatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>> PrepareAsyncSaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>>(PrepareAsyncSaveDiscriminatorConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status SetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetActiveDiscriminatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetActiveDiscriminatorConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status DeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncDeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncDeleteDiscriminatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteDiscriminatorConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>> AsyncGetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>>(AsyncGetNextDiscriminatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>> PrepareAsyncGetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>>(PrepareAsyncGetNextDiscriminatorConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>> AsyncGetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>>(AsyncGetNextModelinatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>> PrepareAsyncGetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>>(PrepareAsyncGetNextModelinatorConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status SaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSaveModelinatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSaveModelinatorConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status FetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>> AsyncFetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>>(AsyncFetchModelinatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>> PrepareAsyncFetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>>(PrepareAsyncFetchModelinatorConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status ResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncResetModelinatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncResetModelinatorConfigRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::almanac::GetConfigDataResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::almanac::GetConfigDataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest* request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest* request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void LoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void LoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void LoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void LoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void FetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void FetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetConfigDataResponse>* AsyncGetConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetConfigDataResponse>* PrepareAsyncGetConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextConfigDataResponse>* AsyncGetNextConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextConfigDataResponse>* PrepareAsyncGetNextConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>* AsyncLoadAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>* PrepareAsyncLoadAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>* AsyncSaveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>* PrepareAsyncSaveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetActiveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetActiveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncDeleteAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>* AsyncGetNextAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>* PrepareAsyncGetNextAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>* AsyncLoadDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>* PrepareAsyncLoadDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>* AsyncSaveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>* PrepareAsyncSaveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetActiveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetActiveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncDeleteDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>* AsyncGetNextDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>* PrepareAsyncGetNextDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>* AsyncGetNextModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>* PrepareAsyncGetNextModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSaveModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSaveModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>* AsyncFetchModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>* PrepareAsyncFetchModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncResetModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncResetModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::almanac::GetConfigDataResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetConfigDataResponse>> AsyncGetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetConfigDataResponse>>(AsyncGetConfigDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetConfigDataResponse>> PrepareAsyncGetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetConfigDataResponse>>(PrepareAsyncGetConfigDataRaw(context, request, cq));
    }
    ::grpc::Status GetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextConfigDataResponse>> AsyncGetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextConfigDataResponse>>(AsyncGetNextConfigDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextConfigDataResponse>> PrepareAsyncGetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextConfigDataResponse>>(PrepareAsyncGetNextConfigDataRaw(context, request, cq));
    }
    ::grpc::Status LoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>> AsyncLoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>>(AsyncLoadAlmanacConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>> PrepareAsyncLoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>>(PrepareAsyncLoadAlmanacConfigRaw(context, request, cq));
    }
    ::grpc::Status SaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>> AsyncSaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>>(AsyncSaveAlmanacConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>> PrepareAsyncSaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>>(PrepareAsyncSaveAlmanacConfigRaw(context, request, cq));
    }
    ::grpc::Status SetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetActiveAlmanacConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetActiveAlmanacConfigRaw(context, request, cq));
    }
    ::grpc::Status DeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncDeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncDeleteAlmanacConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteAlmanacConfigRaw(context, request, cq));
    }
    ::grpc::Status GetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>> AsyncGetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>>(AsyncGetNextAlmanacConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>> PrepareAsyncGetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>>(PrepareAsyncGetNextAlmanacConfigRaw(context, request, cq));
    }
    ::grpc::Status LoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>> AsyncLoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>>(AsyncLoadDiscriminatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>> PrepareAsyncLoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>>(PrepareAsyncLoadDiscriminatorConfigRaw(context, request, cq));
    }
    ::grpc::Status SaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>> AsyncSaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>>(AsyncSaveDiscriminatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>> PrepareAsyncSaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>>(PrepareAsyncSaveDiscriminatorConfigRaw(context, request, cq));
    }
    ::grpc::Status SetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetActiveDiscriminatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetActiveDiscriminatorConfigRaw(context, request, cq));
    }
    ::grpc::Status DeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncDeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncDeleteDiscriminatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteDiscriminatorConfigRaw(context, request, cq));
    }
    ::grpc::Status GetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>> AsyncGetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>>(AsyncGetNextDiscriminatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>> PrepareAsyncGetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>>(PrepareAsyncGetNextDiscriminatorConfigRaw(context, request, cq));
    }
    ::grpc::Status GetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>> AsyncGetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>>(AsyncGetNextModelinatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>> PrepareAsyncGetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>>(PrepareAsyncGetNextModelinatorConfigRaw(context, request, cq));
    }
    ::grpc::Status SaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSaveModelinatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSaveModelinatorConfigRaw(context, request, cq));
    }
    ::grpc::Status FetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>> AsyncFetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>>(AsyncFetchModelinatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>> PrepareAsyncFetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>>(PrepareAsyncFetchModelinatorConfigRaw(context, request, cq));
    }
    ::grpc::Status ResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncResetModelinatorConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncResetModelinatorConfigRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::almanac::GetConfigDataResponse* response, std::function<void(::grpc::Status)>) override;
      void GetConfigData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::almanac::GetConfigDataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest* request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextConfigData(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest* request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void LoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void LoadAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void SaveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetActiveAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void DeleteAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextAlmanacConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void LoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void LoadDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void SaveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetActiveDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void DeleteDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextDiscriminatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SaveModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void FetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void FetchModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void ResetModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetConfigDataResponse>* AsyncGetConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetConfigDataResponse>* PrepareAsyncGetConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextConfigDataResponse>* AsyncGetNextConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextConfigDataResponse>* PrepareAsyncGetNextConfigDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>* AsyncLoadAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>* PrepareAsyncLoadAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>* AsyncSaveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>* PrepareAsyncSaveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetActiveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetActiveAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncDeleteAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>* AsyncGetNextAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>* PrepareAsyncGetNextAlmanacConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>* AsyncLoadDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>* PrepareAsyncLoadDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>* AsyncSaveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>* PrepareAsyncSaveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetActiveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetActiveDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncDeleteDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>* AsyncGetNextDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>* PrepareAsyncGetNextDiscriminatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>* AsyncGetNextModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>* PrepareAsyncGetNextModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSaveModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSaveModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>* AsyncFetchModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>* PrepareAsyncFetchModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncResetModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncResetModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetConfigData_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextConfigData_;
    const ::grpc::internal::RpcMethod rpcmethod_LoadAlmanacConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_SaveAlmanacConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_SetActiveAlmanacConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_DeleteAlmanacConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextAlmanacConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_LoadDiscriminatorConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_SaveDiscriminatorConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_SetActiveDiscriminatorConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_DeleteDiscriminatorConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextDiscriminatorConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextModelinatorConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_SaveModelinatorConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_FetchModelinatorConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_ResetModelinatorConfig_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetConfigData(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::almanac::GetConfigDataResponse* response);
    virtual ::grpc::Status GetNextConfigData(::grpc::ServerContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest* request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response);
    virtual ::grpc::Status LoadAlmanacConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response);
    virtual ::grpc::Status SaveAlmanacConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response);
    virtual ::grpc::Status SetActiveAlmanacConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status DeleteAlmanacConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextAlmanacConfig(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response);
    virtual ::grpc::Status LoadDiscriminatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response);
    virtual ::grpc::Status SaveDiscriminatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response);
    virtual ::grpc::Status SetActiveDiscriminatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status DeleteDiscriminatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextDiscriminatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response);
    virtual ::grpc::Status GetNextModelinatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response);
    virtual ::grpc::Status SaveModelinatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status FetchModelinatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response);
    virtual ::grpc::Status ResetModelinatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetConfigData() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::almanac::GetConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetConfigData(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::almanac::GetConfigDataResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextConfigData() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::GetNextConfigDataRequest* /*request*/, ::carbon::frontend::almanac::GetNextConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextConfigData(::grpc::ServerContext* context, ::carbon::frontend::almanac::GetNextConfigDataRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::almanac::GetNextConfigDataResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_LoadAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_LoadAlmanacConfig() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_LoadAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLoadAlmanacConfig(::grpc::ServerContext* context, ::carbon::frontend::almanac::LoadAlmanacConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::almanac::LoadAlmanacConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SaveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SaveAlmanacConfig() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_SaveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveAlmanacConfig(::grpc::ServerContext* context, ::carbon::frontend::almanac::SaveAlmanacConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::almanac::SaveAlmanacConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetActiveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetActiveAlmanacConfig() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_SetActiveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActiveAlmanacConfig(::grpc::ServerContext* context, ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DeleteAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DeleteAlmanacConfig() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_DeleteAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteAlmanacConfig(::grpc::ServerContext* context, ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextAlmanacConfig() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_GetNextAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAlmanacConfig(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_LoadDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_LoadDiscriminatorConfig() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_LoadDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLoadDiscriminatorConfig(::grpc::ServerContext* context, ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SaveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SaveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_SaveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveDiscriminatorConfig(::grpc::ServerContext* context, ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetActiveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetActiveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodAsync(9);
    }
    ~WithAsyncMethod_SetActiveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActiveDiscriminatorConfig(::grpc::ServerContext* context, ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DeleteDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DeleteDiscriminatorConfig() {
      ::grpc::Service::MarkMethodAsync(10);
    }
    ~WithAsyncMethod_DeleteDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteDiscriminatorConfig(::grpc::ServerContext* context, ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextDiscriminatorConfig() {
      ::grpc::Service::MarkMethodAsync(11);
    }
    ~WithAsyncMethod_GetNextDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextDiscriminatorConfig(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextModelinatorConfig() {
      ::grpc::Service::MarkMethodAsync(12);
    }
    ~WithAsyncMethod_GetNextModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextModelinatorConfig(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SaveModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SaveModelinatorConfig() {
      ::grpc::Service::MarkMethodAsync(13);
    }
    ~WithAsyncMethod_SaveModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveModelinatorConfig(::grpc::ServerContext* context, ::carbon::frontend::almanac::SaveModelinatorConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_FetchModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_FetchModelinatorConfig() {
      ::grpc::Service::MarkMethodAsync(14);
    }
    ~WithAsyncMethod_FetchModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FetchModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* /*request*/, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFetchModelinatorConfig(::grpc::ServerContext* context, ::carbon::frontend::almanac::FetchModelinatorConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::almanac::FetchModelinatorConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ResetModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ResetModelinatorConfig() {
      ::grpc::Service::MarkMethodAsync(15);
    }
    ~WithAsyncMethod_ResetModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestResetModelinatorConfig(::grpc::ServerContext* context, ::carbon::frontend::almanac::ResetModelinatorConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(15, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetConfigData<WithAsyncMethod_GetNextConfigData<WithAsyncMethod_LoadAlmanacConfig<WithAsyncMethod_SaveAlmanacConfig<WithAsyncMethod_SetActiveAlmanacConfig<WithAsyncMethod_DeleteAlmanacConfig<WithAsyncMethod_GetNextAlmanacConfig<WithAsyncMethod_LoadDiscriminatorConfig<WithAsyncMethod_SaveDiscriminatorConfig<WithAsyncMethod_SetActiveDiscriminatorConfig<WithAsyncMethod_DeleteDiscriminatorConfig<WithAsyncMethod_GetNextDiscriminatorConfig<WithAsyncMethod_GetNextModelinatorConfig<WithAsyncMethod_SaveModelinatorConfig<WithAsyncMethod_FetchModelinatorConfig<WithAsyncMethod_ResetModelinatorConfig<Service > > > > > > > > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetConfigData() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::GetConfigDataResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::almanac::GetConfigDataResponse* response) { return this->GetConfigData(context, request, response); }));}
    void SetMessageAllocatorFor_GetConfigData(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::GetConfigDataResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::GetConfigDataResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::almanac::GetConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetConfigData(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::almanac::GetConfigDataResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextConfigData() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::GetNextConfigDataRequest, ::carbon::frontend::almanac::GetNextConfigDataResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::GetNextConfigDataRequest* request, ::carbon::frontend::almanac::GetNextConfigDataResponse* response) { return this->GetNextConfigData(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextConfigData(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::GetNextConfigDataRequest, ::carbon::frontend::almanac::GetNextConfigDataResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::GetNextConfigDataRequest, ::carbon::frontend::almanac::GetNextConfigDataResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::GetNextConfigDataRequest* /*request*/, ::carbon::frontend::almanac::GetNextConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextConfigData(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::GetNextConfigDataRequest* /*request*/, ::carbon::frontend::almanac::GetNextConfigDataResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_LoadAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_LoadAlmanacConfig() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::LoadAlmanacConfigRequest, ::carbon::frontend::almanac::LoadAlmanacConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* request, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* response) { return this->LoadAlmanacConfig(context, request, response); }));}
    void SetMessageAllocatorFor_LoadAlmanacConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::LoadAlmanacConfigRequest, ::carbon::frontend::almanac::LoadAlmanacConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::LoadAlmanacConfigRequest, ::carbon::frontend::almanac::LoadAlmanacConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_LoadAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LoadAlmanacConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SaveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SaveAlmanacConfig() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::SaveAlmanacConfigRequest, ::carbon::frontend::almanac::SaveAlmanacConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* request, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* response) { return this->SaveAlmanacConfig(context, request, response); }));}
    void SetMessageAllocatorFor_SaveAlmanacConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::SaveAlmanacConfigRequest, ::carbon::frontend::almanac::SaveAlmanacConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::SaveAlmanacConfigRequest, ::carbon::frontend::almanac::SaveAlmanacConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SaveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveAlmanacConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetActiveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetActiveAlmanacConfig() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response) { return this->SetActiveAlmanacConfig(context, request, response); }));}
    void SetMessageAllocatorFor_SetActiveAlmanacConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetActiveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActiveAlmanacConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DeleteAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DeleteAlmanacConfig() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::DeleteAlmanacConfigRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* request, ::carbon::frontend::util::Empty* response) { return this->DeleteAlmanacConfig(context, request, response); }));}
    void SetMessageAllocatorFor_DeleteAlmanacConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::DeleteAlmanacConfigRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::DeleteAlmanacConfigRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DeleteAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteAlmanacConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextAlmanacConfig() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* response) { return this->GetNextAlmanacConfig(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextAlmanacConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAlmanacConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_LoadDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_LoadDiscriminatorConfig() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* response) { return this->LoadDiscriminatorConfig(context, request, response); }));}
    void SetMessageAllocatorFor_LoadDiscriminatorConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_LoadDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LoadDiscriminatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SaveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SaveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* request, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* response) { return this->SaveDiscriminatorConfig(context, request, response); }));}
    void SetMessageAllocatorFor_SaveDiscriminatorConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SaveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveDiscriminatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetActiveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetActiveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response) { return this->SetActiveDiscriminatorConfig(context, request, response); }));}
    void SetMessageAllocatorFor_SetActiveDiscriminatorConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(9);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetActiveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActiveDiscriminatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DeleteDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DeleteDiscriminatorConfig() {
      ::grpc::Service::MarkMethodCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* request, ::carbon::frontend::util::Empty* response) { return this->DeleteDiscriminatorConfig(context, request, response); }));}
    void SetMessageAllocatorFor_DeleteDiscriminatorConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(10);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DeleteDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteDiscriminatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextDiscriminatorConfig() {
      ::grpc::Service::MarkMethodCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* response) { return this->GetNextDiscriminatorConfig(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextDiscriminatorConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(11);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextDiscriminatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextModelinatorConfig() {
      ::grpc::Service::MarkMethodCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* response) { return this->GetNextModelinatorConfig(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextModelinatorConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(12);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextModelinatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SaveModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SaveModelinatorConfig() {
      ::grpc::Service::MarkMethodCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::SaveModelinatorConfigRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response) { return this->SaveModelinatorConfig(context, request, response); }));}
    void SetMessageAllocatorFor_SaveModelinatorConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::SaveModelinatorConfigRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(13);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::SaveModelinatorConfigRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SaveModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveModelinatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_FetchModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_FetchModelinatorConfig() {
      ::grpc::Service::MarkMethodCallback(14,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::FetchModelinatorConfigRequest, ::carbon::frontend::almanac::FetchModelinatorConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* request, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* response) { return this->FetchModelinatorConfig(context, request, response); }));}
    void SetMessageAllocatorFor_FetchModelinatorConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::FetchModelinatorConfigRequest, ::carbon::frontend::almanac::FetchModelinatorConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(14);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::FetchModelinatorConfigRequest, ::carbon::frontend::almanac::FetchModelinatorConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_FetchModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FetchModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* /*request*/, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* FetchModelinatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* /*request*/, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ResetModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ResetModelinatorConfig() {
      ::grpc::Service::MarkMethodCallback(15,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::ResetModelinatorConfigRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* request, ::carbon::frontend::util::Empty* response) { return this->ResetModelinatorConfig(context, request, response); }));}
    void SetMessageAllocatorFor_ResetModelinatorConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::almanac::ResetModelinatorConfigRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(15);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::almanac::ResetModelinatorConfigRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ResetModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ResetModelinatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetConfigData<WithCallbackMethod_GetNextConfigData<WithCallbackMethod_LoadAlmanacConfig<WithCallbackMethod_SaveAlmanacConfig<WithCallbackMethod_SetActiveAlmanacConfig<WithCallbackMethod_DeleteAlmanacConfig<WithCallbackMethod_GetNextAlmanacConfig<WithCallbackMethod_LoadDiscriminatorConfig<WithCallbackMethod_SaveDiscriminatorConfig<WithCallbackMethod_SetActiveDiscriminatorConfig<WithCallbackMethod_DeleteDiscriminatorConfig<WithCallbackMethod_GetNextDiscriminatorConfig<WithCallbackMethod_GetNextModelinatorConfig<WithCallbackMethod_SaveModelinatorConfig<WithCallbackMethod_FetchModelinatorConfig<WithCallbackMethod_ResetModelinatorConfig<Service > > > > > > > > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetConfigData() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::almanac::GetConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextConfigData() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::GetNextConfigDataRequest* /*request*/, ::carbon::frontend::almanac::GetNextConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_LoadAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_LoadAlmanacConfig() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_LoadAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SaveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SaveAlmanacConfig() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_SaveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetActiveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetActiveAlmanacConfig() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_SetActiveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DeleteAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DeleteAlmanacConfig() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_DeleteAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextAlmanacConfig() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_GetNextAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_LoadDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_LoadDiscriminatorConfig() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_LoadDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SaveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SaveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_SaveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetActiveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetActiveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodGeneric(9);
    }
    ~WithGenericMethod_SetActiveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DeleteDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DeleteDiscriminatorConfig() {
      ::grpc::Service::MarkMethodGeneric(10);
    }
    ~WithGenericMethod_DeleteDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextDiscriminatorConfig() {
      ::grpc::Service::MarkMethodGeneric(11);
    }
    ~WithGenericMethod_GetNextDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextModelinatorConfig() {
      ::grpc::Service::MarkMethodGeneric(12);
    }
    ~WithGenericMethod_GetNextModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SaveModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SaveModelinatorConfig() {
      ::grpc::Service::MarkMethodGeneric(13);
    }
    ~WithGenericMethod_SaveModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_FetchModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_FetchModelinatorConfig() {
      ::grpc::Service::MarkMethodGeneric(14);
    }
    ~WithGenericMethod_FetchModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FetchModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* /*request*/, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ResetModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ResetModelinatorConfig() {
      ::grpc::Service::MarkMethodGeneric(15);
    }
    ~WithGenericMethod_ResetModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetConfigData() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::almanac::GetConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetConfigData(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextConfigData() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::GetNextConfigDataRequest* /*request*/, ::carbon::frontend::almanac::GetNextConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextConfigData(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_LoadAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_LoadAlmanacConfig() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_LoadAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLoadAlmanacConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SaveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SaveAlmanacConfig() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_SaveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveAlmanacConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetActiveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetActiveAlmanacConfig() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_SetActiveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActiveAlmanacConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DeleteAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DeleteAlmanacConfig() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_DeleteAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteAlmanacConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextAlmanacConfig() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_GetNextAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAlmanacConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_LoadDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_LoadDiscriminatorConfig() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_LoadDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLoadDiscriminatorConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SaveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SaveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_SaveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveDiscriminatorConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetActiveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetActiveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodRaw(9);
    }
    ~WithRawMethod_SetActiveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActiveDiscriminatorConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DeleteDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DeleteDiscriminatorConfig() {
      ::grpc::Service::MarkMethodRaw(10);
    }
    ~WithRawMethod_DeleteDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteDiscriminatorConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextDiscriminatorConfig() {
      ::grpc::Service::MarkMethodRaw(11);
    }
    ~WithRawMethod_GetNextDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextDiscriminatorConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextModelinatorConfig() {
      ::grpc::Service::MarkMethodRaw(12);
    }
    ~WithRawMethod_GetNextModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextModelinatorConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SaveModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SaveModelinatorConfig() {
      ::grpc::Service::MarkMethodRaw(13);
    }
    ~WithRawMethod_SaveModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveModelinatorConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_FetchModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_FetchModelinatorConfig() {
      ::grpc::Service::MarkMethodRaw(14);
    }
    ~WithRawMethod_FetchModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FetchModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* /*request*/, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFetchModelinatorConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ResetModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ResetModelinatorConfig() {
      ::grpc::Service::MarkMethodRaw(15);
    }
    ~WithRawMethod_ResetModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestResetModelinatorConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(15, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetConfigData() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetConfigData(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::almanac::GetConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetConfigData(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextConfigData() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextConfigData(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::GetNextConfigDataRequest* /*request*/, ::carbon::frontend::almanac::GetNextConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextConfigData(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_LoadAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_LoadAlmanacConfig() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->LoadAlmanacConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_LoadAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LoadAlmanacConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SaveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SaveAlmanacConfig() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SaveAlmanacConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_SaveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveAlmanacConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetActiveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetActiveAlmanacConfig() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetActiveAlmanacConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetActiveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActiveAlmanacConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DeleteAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DeleteAlmanacConfig() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DeleteAlmanacConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_DeleteAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteAlmanacConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextAlmanacConfig() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextAlmanacConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAlmanacConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_LoadDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_LoadDiscriminatorConfig() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->LoadDiscriminatorConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_LoadDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LoadDiscriminatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SaveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SaveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SaveDiscriminatorConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_SaveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveDiscriminatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetActiveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetActiveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodRawCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetActiveDiscriminatorConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetActiveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActiveDiscriminatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DeleteDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DeleteDiscriminatorConfig() {
      ::grpc::Service::MarkMethodRawCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DeleteDiscriminatorConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_DeleteDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteDiscriminatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextDiscriminatorConfig() {
      ::grpc::Service::MarkMethodRawCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextDiscriminatorConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextDiscriminatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextModelinatorConfig() {
      ::grpc::Service::MarkMethodRawCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextModelinatorConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextModelinatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SaveModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SaveModelinatorConfig() {
      ::grpc::Service::MarkMethodRawCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SaveModelinatorConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_SaveModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveModelinatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_FetchModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_FetchModelinatorConfig() {
      ::grpc::Service::MarkMethodRawCallback(14,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->FetchModelinatorConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_FetchModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FetchModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* /*request*/, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* FetchModelinatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ResetModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ResetModelinatorConfig() {
      ::grpc::Service::MarkMethodRawCallback(15,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ResetModelinatorConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_ResetModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ResetModelinatorConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetConfigData() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::GetConfigDataResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::almanac::GetConfigDataResponse>* streamer) {
                       return this->StreamedGetConfigData(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::almanac::GetConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetConfigData(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::almanac::GetConfigDataResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextConfigData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextConfigData() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::GetNextConfigDataRequest, ::carbon::frontend::almanac::GetNextConfigDataResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::GetNextConfigDataRequest, ::carbon::frontend::almanac::GetNextConfigDataResponse>* streamer) {
                       return this->StreamedGetNextConfigData(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextConfigData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextConfigData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::GetNextConfigDataRequest* /*request*/, ::carbon::frontend::almanac::GetNextConfigDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextConfigData(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::GetNextConfigDataRequest,::carbon::frontend::almanac::GetNextConfigDataResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_LoadAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_LoadAlmanacConfig() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::LoadAlmanacConfigRequest, ::carbon::frontend::almanac::LoadAlmanacConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::LoadAlmanacConfigRequest, ::carbon::frontend::almanac::LoadAlmanacConfigResponse>* streamer) {
                       return this->StreamedLoadAlmanacConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_LoadAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status LoadAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedLoadAlmanacConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::LoadAlmanacConfigRequest,::carbon::frontend::almanac::LoadAlmanacConfigResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SaveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SaveAlmanacConfig() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::SaveAlmanacConfigRequest, ::carbon::frontend::almanac::SaveAlmanacConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::SaveAlmanacConfigRequest, ::carbon::frontend::almanac::SaveAlmanacConfigResponse>* streamer) {
                       return this->StreamedSaveAlmanacConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SaveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SaveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveAlmanacConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSaveAlmanacConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::SaveAlmanacConfigRequest,::carbon::frontend::almanac::SaveAlmanacConfigResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetActiveAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetActiveAlmanacConfig() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetActiveAlmanacConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetActiveAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetActiveAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetActiveAlmanacConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::SetActiveAlmanacConfigRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DeleteAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DeleteAlmanacConfig() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::DeleteAlmanacConfigRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::DeleteAlmanacConfigRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedDeleteAlmanacConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DeleteAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DeleteAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteAlmanacConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDeleteAlmanacConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::DeleteAlmanacConfigRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextAlmanacConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextAlmanacConfig() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse>* streamer) {
                       return this->StreamedGetNextAlmanacConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextAlmanacConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextAlmanacConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextAlmanacConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextAlmanacConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::almanac::GetNextAlmanacConfigResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_LoadDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_LoadDiscriminatorConfig() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>* streamer) {
                       return this->StreamedLoadDiscriminatorConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_LoadDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status LoadDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::LoadDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedLoadDiscriminatorConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::LoadDiscriminatorConfigRequest,::carbon::frontend::almanac::LoadDiscriminatorConfigResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SaveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SaveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>* streamer) {
                       return this->StreamedSaveDiscriminatorConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SaveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SaveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::almanac::SaveDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSaveDiscriminatorConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::SaveDiscriminatorConfigRequest,::carbon::frontend::almanac::SaveDiscriminatorConfigResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetActiveDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetActiveDiscriminatorConfig() {
      ::grpc::Service::MarkMethodStreamed(9,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetActiveDiscriminatorConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetActiveDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetActiveDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetActiveDiscriminatorConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::SetActiveDiscriminatorConfigRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DeleteDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DeleteDiscriminatorConfig() {
      ::grpc::Service::MarkMethodStreamed(10,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedDeleteDiscriminatorConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DeleteDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DeleteDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDeleteDiscriminatorConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::DeleteDiscriminatorConfigRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextDiscriminatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextDiscriminatorConfig() {
      ::grpc::Service::MarkMethodStreamed(11,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>* streamer) {
                       return this->StreamedGetNextDiscriminatorConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextDiscriminatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextDiscriminatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextDiscriminatorConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::almanac::GetNextDiscriminatorConfigResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextModelinatorConfig() {
      ::grpc::Service::MarkMethodStreamed(12,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse>* streamer) {
                       return this->StreamedGetNextModelinatorConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::almanac::GetNextModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextModelinatorConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::almanac::GetNextModelinatorConfigResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SaveModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SaveModelinatorConfig() {
      ::grpc::Service::MarkMethodStreamed(13,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::SaveModelinatorConfigRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::SaveModelinatorConfigRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSaveModelinatorConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SaveModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SaveModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::SaveModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSaveModelinatorConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::SaveModelinatorConfigRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_FetchModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_FetchModelinatorConfig() {
      ::grpc::Service::MarkMethodStreamed(14,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::FetchModelinatorConfigRequest, ::carbon::frontend::almanac::FetchModelinatorConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::FetchModelinatorConfigRequest, ::carbon::frontend::almanac::FetchModelinatorConfigResponse>* streamer) {
                       return this->StreamedFetchModelinatorConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_FetchModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status FetchModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::FetchModelinatorConfigRequest* /*request*/, ::carbon::frontend::almanac::FetchModelinatorConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedFetchModelinatorConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::FetchModelinatorConfigRequest,::carbon::frontend::almanac::FetchModelinatorConfigResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ResetModelinatorConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ResetModelinatorConfig() {
      ::grpc::Service::MarkMethodStreamed(15,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::almanac::ResetModelinatorConfigRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::almanac::ResetModelinatorConfigRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedResetModelinatorConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ResetModelinatorConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ResetModelinatorConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::almanac::ResetModelinatorConfigRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedResetModelinatorConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::almanac::ResetModelinatorConfigRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetConfigData<WithStreamedUnaryMethod_GetNextConfigData<WithStreamedUnaryMethod_LoadAlmanacConfig<WithStreamedUnaryMethod_SaveAlmanacConfig<WithStreamedUnaryMethod_SetActiveAlmanacConfig<WithStreamedUnaryMethod_DeleteAlmanacConfig<WithStreamedUnaryMethod_GetNextAlmanacConfig<WithStreamedUnaryMethod_LoadDiscriminatorConfig<WithStreamedUnaryMethod_SaveDiscriminatorConfig<WithStreamedUnaryMethod_SetActiveDiscriminatorConfig<WithStreamedUnaryMethod_DeleteDiscriminatorConfig<WithStreamedUnaryMethod_GetNextDiscriminatorConfig<WithStreamedUnaryMethod_GetNextModelinatorConfig<WithStreamedUnaryMethod_SaveModelinatorConfig<WithStreamedUnaryMethod_FetchModelinatorConfig<WithStreamedUnaryMethod_ResetModelinatorConfig<Service > > > > > > > > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetConfigData<WithStreamedUnaryMethod_GetNextConfigData<WithStreamedUnaryMethod_LoadAlmanacConfig<WithStreamedUnaryMethod_SaveAlmanacConfig<WithStreamedUnaryMethod_SetActiveAlmanacConfig<WithStreamedUnaryMethod_DeleteAlmanacConfig<WithStreamedUnaryMethod_GetNextAlmanacConfig<WithStreamedUnaryMethod_LoadDiscriminatorConfig<WithStreamedUnaryMethod_SaveDiscriminatorConfig<WithStreamedUnaryMethod_SetActiveDiscriminatorConfig<WithStreamedUnaryMethod_DeleteDiscriminatorConfig<WithStreamedUnaryMethod_GetNextDiscriminatorConfig<WithStreamedUnaryMethod_GetNextModelinatorConfig<WithStreamedUnaryMethod_SaveModelinatorConfig<WithStreamedUnaryMethod_FetchModelinatorConfig<WithStreamedUnaryMethod_ResetModelinatorConfig<Service > > > > > > > > > > > > > > > > StreamedService;
};

}  // namespace almanac
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2falmanac_2eproto__INCLUDED

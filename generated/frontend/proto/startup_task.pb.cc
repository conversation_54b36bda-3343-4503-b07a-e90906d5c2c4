// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/startup_task.proto

#include "frontend/proto/startup_task.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace startup_task {
constexpr GetNextTasksResponse::GetNextTasksResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : tasks_()
  , ts_(nullptr){}
struct GetNextTasksResponseDefaultTypeInternal {
  constexpr GetNextTasksResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextTasksResponseDefaultTypeInternal() {}
  union {
    GetNextTasksResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextTasksResponseDefaultTypeInternal _GetNextTasksResponse_default_instance_;
constexpr MarkTaskCompleteRequest::MarkTaskCompleteRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : task_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct MarkTaskCompleteRequestDefaultTypeInternal {
  constexpr MarkTaskCompleteRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MarkTaskCompleteRequestDefaultTypeInternal() {}
  union {
    MarkTaskCompleteRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MarkTaskCompleteRequestDefaultTypeInternal _MarkTaskCompleteRequest_default_instance_;
constexpr MarkTaskCompleteResponse::MarkTaskCompleteResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct MarkTaskCompleteResponseDefaultTypeInternal {
  constexpr MarkTaskCompleteResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MarkTaskCompleteResponseDefaultTypeInternal() {}
  union {
    MarkTaskCompleteResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MarkTaskCompleteResponseDefaultTypeInternal _MarkTaskCompleteResponse_default_instance_;
}  // namespace startup_task
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fstartup_5ftask_2eproto[3];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2fstartup_5ftask_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fstartup_5ftask_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fstartup_5ftask_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::startup_task::GetNextTasksResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::startup_task::GetNextTasksResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::startup_task::GetNextTasksResponse, tasks_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::startup_task::MarkTaskCompleteRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::startup_task::MarkTaskCompleteRequest, task_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::startup_task::MarkTaskCompleteResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::startup_task::GetNextTasksResponse)},
  { 8, -1, -1, sizeof(::carbon::frontend::startup_task::MarkTaskCompleteRequest)},
  { 15, -1, -1, sizeof(::carbon::frontend::startup_task::MarkTaskCompleteResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::startup_task::_GetNextTasksResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::startup_task::_MarkTaskCompleteRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::startup_task::_MarkTaskCompleteResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fstartup_5ftask_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n!frontend/proto/startup_task.proto\022\034car"
  "bon.frontend.startup_task\032%proto/startup"
  "_task/startup_task.proto\032\031frontend/proto"
  "/util.proto\"m\n\024GetNextTasksResponse\022+\n\002t"
  "s\030\001 \001(\0132\037.carbon.frontend.util.Timestamp"
  "\022(\n\005tasks\030\002 \003(\0132\031.carbon.startup_task.Ta"
  "sk\"*\n\027MarkTaskCompleteRequest\022\017\n\007task_id"
  "\030\001 \001(\t\"\032\n\030MarkTaskCompleteResponse2\375\001\n\022S"
  "tartupTaskService\022c\n\014GetNextTasks\022\037.carb"
  "on.frontend.util.Timestamp\0322.carbon.fron"
  "tend.startup_task.GetNextTasksResponse\022\201"
  "\001\n\020MarkTaskComplete\0225.carbon.frontend.st"
  "artup_task.MarkTaskCompleteRequest\0326.car"
  "bon.frontend.startup_task.MarkTaskComple"
  "teResponseB\020Z\016proto/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
  &::descriptor_table_proto_2fstartup_5ftask_2fstartup_5ftask_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto = {
  false, false, 596, descriptor_table_protodef_frontend_2fproto_2fstartup_5ftask_2eproto, "frontend/proto/startup_task.proto", 
  &descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto_once, descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto_deps, 2, 3,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fstartup_5ftask_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fstartup_5ftask_2eproto, file_level_enum_descriptors_frontend_2fproto_2fstartup_5ftask_2eproto, file_level_service_descriptors_frontend_2fproto_2fstartup_5ftask_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fstartup_5ftask_2eproto(&descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto);
namespace carbon {
namespace frontend {
namespace startup_task {

// ===================================================================

class GetNextTasksResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextTasksResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextTasksResponse::_Internal::ts(const GetNextTasksResponse* msg) {
  return *msg->ts_;
}
void GetNextTasksResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
void GetNextTasksResponse::clear_tasks() {
  tasks_.Clear();
}
GetNextTasksResponse::GetNextTasksResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  tasks_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.startup_task.GetNextTasksResponse)
}
GetNextTasksResponse::GetNextTasksResponse(const GetNextTasksResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      tasks_(from.tasks_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.startup_task.GetNextTasksResponse)
}

inline void GetNextTasksResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextTasksResponse::~GetNextTasksResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.startup_task.GetNextTasksResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextTasksResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextTasksResponse::ArenaDtor(void* object) {
  GetNextTasksResponse* _this = reinterpret_cast< GetNextTasksResponse* >(object);
  (void)_this;
}
void GetNextTasksResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextTasksResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextTasksResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.startup_task.GetNextTasksResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tasks_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextTasksResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.startup_task.Task tasks = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_tasks(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextTasksResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.startup_task.GetNextTasksResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.startup_task.Task tasks = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_tasks_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_tasks(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.startup_task.GetNextTasksResponse)
  return target;
}

size_t GetNextTasksResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.startup_task.GetNextTasksResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.startup_task.Task tasks = 2;
  total_size += 1UL * this->_internal_tasks_size();
  for (const auto& msg : this->tasks_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextTasksResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextTasksResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextTasksResponse::GetClassData() const { return &_class_data_; }

void GetNextTasksResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextTasksResponse *>(to)->MergeFrom(
      static_cast<const GetNextTasksResponse &>(from));
}


void GetNextTasksResponse::MergeFrom(const GetNextTasksResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.startup_task.GetNextTasksResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  tasks_.MergeFrom(from.tasks_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextTasksResponse::CopyFrom(const GetNextTasksResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.startup_task.GetNextTasksResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextTasksResponse::IsInitialized() const {
  return true;
}

void GetNextTasksResponse::InternalSwap(GetNextTasksResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  tasks_.InternalSwap(&other->tasks_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextTasksResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto_getter, &descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstartup_5ftask_2eproto[0]);
}

// ===================================================================

class MarkTaskCompleteRequest::_Internal {
 public:
};

MarkTaskCompleteRequest::MarkTaskCompleteRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.startup_task.MarkTaskCompleteRequest)
}
MarkTaskCompleteRequest::MarkTaskCompleteRequest(const MarkTaskCompleteRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  task_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    task_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_task_id().empty()) {
    task_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_task_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.startup_task.MarkTaskCompleteRequest)
}

inline void MarkTaskCompleteRequest::SharedCtor() {
task_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  task_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

MarkTaskCompleteRequest::~MarkTaskCompleteRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.startup_task.MarkTaskCompleteRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MarkTaskCompleteRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  task_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void MarkTaskCompleteRequest::ArenaDtor(void* object) {
  MarkTaskCompleteRequest* _this = reinterpret_cast< MarkTaskCompleteRequest* >(object);
  (void)_this;
}
void MarkTaskCompleteRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MarkTaskCompleteRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MarkTaskCompleteRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.startup_task.MarkTaskCompleteRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  task_id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MarkTaskCompleteRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string task_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_task_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.startup_task.MarkTaskCompleteRequest.task_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MarkTaskCompleteRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.startup_task.MarkTaskCompleteRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string task_id = 1;
  if (!this->_internal_task_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_task_id().data(), static_cast<int>(this->_internal_task_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.startup_task.MarkTaskCompleteRequest.task_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_task_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.startup_task.MarkTaskCompleteRequest)
  return target;
}

size_t MarkTaskCompleteRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.startup_task.MarkTaskCompleteRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string task_id = 1;
  if (!this->_internal_task_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_task_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MarkTaskCompleteRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MarkTaskCompleteRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MarkTaskCompleteRequest::GetClassData() const { return &_class_data_; }

void MarkTaskCompleteRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MarkTaskCompleteRequest *>(to)->MergeFrom(
      static_cast<const MarkTaskCompleteRequest &>(from));
}


void MarkTaskCompleteRequest::MergeFrom(const MarkTaskCompleteRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.startup_task.MarkTaskCompleteRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_task_id().empty()) {
    _internal_set_task_id(from._internal_task_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MarkTaskCompleteRequest::CopyFrom(const MarkTaskCompleteRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.startup_task.MarkTaskCompleteRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MarkTaskCompleteRequest::IsInitialized() const {
  return true;
}

void MarkTaskCompleteRequest::InternalSwap(MarkTaskCompleteRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &task_id_, lhs_arena,
      &other->task_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata MarkTaskCompleteRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto_getter, &descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstartup_5ftask_2eproto[1]);
}

// ===================================================================

class MarkTaskCompleteResponse::_Internal {
 public:
};

MarkTaskCompleteResponse::MarkTaskCompleteResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.startup_task.MarkTaskCompleteResponse)
}
MarkTaskCompleteResponse::MarkTaskCompleteResponse(const MarkTaskCompleteResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.startup_task.MarkTaskCompleteResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MarkTaskCompleteResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MarkTaskCompleteResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata MarkTaskCompleteResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto_getter, &descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstartup_5ftask_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace startup_task
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::startup_task::GetNextTasksResponse* Arena::CreateMaybeMessage< ::carbon::frontend::startup_task::GetNextTasksResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::startup_task::GetNextTasksResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::startup_task::MarkTaskCompleteRequest* Arena::CreateMaybeMessage< ::carbon::frontend::startup_task::MarkTaskCompleteRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::startup_task::MarkTaskCompleteRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::startup_task::MarkTaskCompleteResponse* Arena::CreateMaybeMessage< ::carbon::frontend::startup_task::MarkTaskCompleteResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::startup_task::MarkTaskCompleteResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

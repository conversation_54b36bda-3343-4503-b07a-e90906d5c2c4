// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/image_stream.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fimage_5fstream_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fimage_5fstream_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
#include "weed_tracking/proto/weed_tracking.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fimage_5fstream_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fimage_5fstream_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[9]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto;
namespace carbon {
namespace frontend {
namespace image_stream {
class Annotations;
struct AnnotationsDefaultTypeInternal;
extern AnnotationsDefaultTypeInternal _Annotations_default_instance_;
class CameraImageRequest;
struct CameraImageRequestDefaultTypeInternal;
extern CameraImageRequestDefaultTypeInternal _CameraImageRequest_default_instance_;
class CentroidPerspective;
struct CentroidPerspectiveDefaultTypeInternal;
extern CentroidPerspectiveDefaultTypeInternal _CentroidPerspective_default_instance_;
class GetMultiPredictPerspectivesRequest;
struct GetMultiPredictPerspectivesRequestDefaultTypeInternal;
extern GetMultiPredictPerspectivesRequestDefaultTypeInternal _GetMultiPredictPerspectivesRequest_default_instance_;
class GetMultiPredictPerspectivesResponse;
struct GetMultiPredictPerspectivesResponseDefaultTypeInternal;
extern GetMultiPredictPerspectivesResponseDefaultTypeInternal _GetMultiPredictPerspectivesResponse_default_instance_;
class GetPredictImageByTimestampRequest;
struct GetPredictImageByTimestampRequestDefaultTypeInternal;
extern GetPredictImageByTimestampRequestDefaultTypeInternal _GetPredictImageByTimestampRequest_default_instance_;
class GetPredictImageByTimestampResponse;
struct GetPredictImageByTimestampResponseDefaultTypeInternal;
extern GetPredictImageByTimestampResponseDefaultTypeInternal _GetPredictImageByTimestampResponse_default_instance_;
class Image;
struct ImageDefaultTypeInternal;
extern ImageDefaultTypeInternal _Image_default_instance_;
class PossiblePerspective;
struct PossiblePerspectiveDefaultTypeInternal;
extern PossiblePerspectiveDefaultTypeInternal _PossiblePerspective_default_instance_;
}  // namespace image_stream
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::image_stream::Annotations* Arena::CreateMaybeMessage<::carbon::frontend::image_stream::Annotations>(Arena*);
template<> ::carbon::frontend::image_stream::CameraImageRequest* Arena::CreateMaybeMessage<::carbon::frontend::image_stream::CameraImageRequest>(Arena*);
template<> ::carbon::frontend::image_stream::CentroidPerspective* Arena::CreateMaybeMessage<::carbon::frontend::image_stream::CentroidPerspective>(Arena*);
template<> ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* Arena::CreateMaybeMessage<::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest>(Arena*);
template<> ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* Arena::CreateMaybeMessage<::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>(Arena*);
template<> ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* Arena::CreateMaybeMessage<::carbon::frontend::image_stream::GetPredictImageByTimestampRequest>(Arena*);
template<> ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* Arena::CreateMaybeMessage<::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>(Arena*);
template<> ::carbon::frontend::image_stream::Image* Arena::CreateMaybeMessage<::carbon::frontend::image_stream::Image>(Arena*);
template<> ::carbon::frontend::image_stream::PossiblePerspective* Arena::CreateMaybeMessage<::carbon::frontend::image_stream::PossiblePerspective>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace image_stream {

// ===================================================================

class Annotations final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.image_stream.Annotations) */ {
 public:
  inline Annotations() : Annotations(nullptr) {}
  ~Annotations() override;
  explicit constexpr Annotations(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Annotations(const Annotations& from);
  Annotations(Annotations&& from) noexcept
    : Annotations() {
    *this = ::std::move(from);
  }

  inline Annotations& operator=(const Annotations& from) {
    CopyFrom(from);
    return *this;
  }
  inline Annotations& operator=(Annotations&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Annotations& default_instance() {
    return *internal_default_instance();
  }
  static inline const Annotations* internal_default_instance() {
    return reinterpret_cast<const Annotations*>(
               &_Annotations_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Annotations& a, Annotations& b) {
    a.Swap(&b);
  }
  inline void Swap(Annotations* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Annotations* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Annotations* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Annotations>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Annotations& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Annotations& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Annotations* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.image_stream.Annotations";
  }
  protected:
  explicit Annotations(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDetectionsFieldNumber = 1,
    kBandsFieldNumber = 2,
    kCrosshairXFieldNumber = 3,
    kCrosshairYFieldNumber = 4,
  };
  // .weed_tracking.Detections detections = 1;
  bool has_detections() const;
  private:
  bool _internal_has_detections() const;
  public:
  void clear_detections();
  const ::weed_tracking::Detections& detections() const;
  PROTOBUF_NODISCARD ::weed_tracking::Detections* release_detections();
  ::weed_tracking::Detections* mutable_detections();
  void set_allocated_detections(::weed_tracking::Detections* detections);
  private:
  const ::weed_tracking::Detections& _internal_detections() const;
  ::weed_tracking::Detections* _internal_mutable_detections();
  public:
  void unsafe_arena_set_allocated_detections(
      ::weed_tracking::Detections* detections);
  ::weed_tracking::Detections* unsafe_arena_release_detections();

  // .weed_tracking.Bands bands = 2;
  bool has_bands() const;
  private:
  bool _internal_has_bands() const;
  public:
  void clear_bands();
  const ::weed_tracking::Bands& bands() const;
  PROTOBUF_NODISCARD ::weed_tracking::Bands* release_bands();
  ::weed_tracking::Bands* mutable_bands();
  void set_allocated_bands(::weed_tracking::Bands* bands);
  private:
  const ::weed_tracking::Bands& _internal_bands() const;
  ::weed_tracking::Bands* _internal_mutable_bands();
  public:
  void unsafe_arena_set_allocated_bands(
      ::weed_tracking::Bands* bands);
  ::weed_tracking::Bands* unsafe_arena_release_bands();

  // int32 crosshair_x = 3;
  void clear_crosshair_x();
  int32_t crosshair_x() const;
  void set_crosshair_x(int32_t value);
  private:
  int32_t _internal_crosshair_x() const;
  void _internal_set_crosshair_x(int32_t value);
  public:

  // int32 crosshair_y = 4;
  void clear_crosshair_y();
  int32_t crosshair_y() const;
  void set_crosshair_y(int32_t value);
  private:
  int32_t _internal_crosshair_y() const;
  void _internal_set_crosshair_y(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.Annotations)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::weed_tracking::Detections* detections_;
  ::weed_tracking::Bands* bands_;
  int32_t crosshair_x_;
  int32_t crosshair_y_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fimage_5fstream_2eproto;
};
// -------------------------------------------------------------------

class Image final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.image_stream.Image) */ {
 public:
  inline Image() : Image(nullptr) {}
  ~Image() override;
  explicit constexpr Image(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Image(const Image& from);
  Image(Image&& from) noexcept
    : Image() {
    *this = ::std::move(from);
  }

  inline Image& operator=(const Image& from) {
    CopyFrom(from);
    return *this;
  }
  inline Image& operator=(Image&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Image& default_instance() {
    return *internal_default_instance();
  }
  static inline const Image* internal_default_instance() {
    return reinterpret_cast<const Image*>(
               &_Image_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Image& a, Image& b) {
    a.Swap(&b);
  }
  inline void Swap(Image* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Image* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Image* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Image>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Image& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Image& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Image* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.image_stream.Image";
  }
  protected:
  explicit Image(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 5,
    kTsFieldNumber = 1,
    kAnnotationsFieldNumber = 6,
    kWidthFieldNumber = 2,
    kHeightFieldNumber = 3,
    kFocusFieldNumber = 4,
  };
  // bytes data = 5;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.image_stream.Annotations annotations = 6;
  bool has_annotations() const;
  private:
  bool _internal_has_annotations() const;
  public:
  void clear_annotations();
  const ::carbon::frontend::image_stream::Annotations& annotations() const;
  PROTOBUF_NODISCARD ::carbon::frontend::image_stream::Annotations* release_annotations();
  ::carbon::frontend::image_stream::Annotations* mutable_annotations();
  void set_allocated_annotations(::carbon::frontend::image_stream::Annotations* annotations);
  private:
  const ::carbon::frontend::image_stream::Annotations& _internal_annotations() const;
  ::carbon::frontend::image_stream::Annotations* _internal_mutable_annotations();
  public:
  void unsafe_arena_set_allocated_annotations(
      ::carbon::frontend::image_stream::Annotations* annotations);
  ::carbon::frontend::image_stream::Annotations* unsafe_arena_release_annotations();

  // uint32 width = 2;
  void clear_width();
  uint32_t width() const;
  void set_width(uint32_t value);
  private:
  uint32_t _internal_width() const;
  void _internal_set_width(uint32_t value);
  public:

  // uint32 height = 3;
  void clear_height();
  uint32_t height() const;
  void set_height(uint32_t value);
  private:
  uint32_t _internal_height() const;
  void _internal_set_height(uint32_t value);
  public:

  // double focus = 4;
  void clear_focus();
  double focus() const;
  void set_focus(double value);
  private:
  double _internal_focus() const;
  void _internal_set_focus(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.Image)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::image_stream::Annotations* annotations_;
  uint32_t width_;
  uint32_t height_;
  double focus_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fimage_5fstream_2eproto;
};
// -------------------------------------------------------------------

class CameraImageRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.image_stream.CameraImageRequest) */ {
 public:
  inline CameraImageRequest() : CameraImageRequest(nullptr) {}
  ~CameraImageRequest() override;
  explicit constexpr CameraImageRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CameraImageRequest(const CameraImageRequest& from);
  CameraImageRequest(CameraImageRequest&& from) noexcept
    : CameraImageRequest() {
    *this = ::std::move(from);
  }

  inline CameraImageRequest& operator=(const CameraImageRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CameraImageRequest& operator=(CameraImageRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CameraImageRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CameraImageRequest* internal_default_instance() {
    return reinterpret_cast<const CameraImageRequest*>(
               &_CameraImageRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(CameraImageRequest& a, CameraImageRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CameraImageRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CameraImageRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CameraImageRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CameraImageRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CameraImageRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CameraImageRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CameraImageRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.image_stream.CameraImageRequest";
  }
  protected:
  explicit CameraImageRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCamIdFieldNumber = 1,
    kTsFieldNumber = 2,
    kAnnotatedFieldNumber = 3,
    kIncludeAnnotationsMetadataFieldNumber = 4,
    kDontDownsampleFieldNumber = 5,
    kEncodeAsPngFieldNumber = 6,
    kEncodeAsRawFieldNumber = 7,
  };
  // string cam_id = 1;
  void clear_cam_id();
  const std::string& cam_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cam_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cam_id();
  PROTOBUF_NODISCARD std::string* release_cam_id();
  void set_allocated_cam_id(std::string* cam_id);
  private:
  const std::string& _internal_cam_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cam_id(const std::string& value);
  std::string* _internal_mutable_cam_id();
  public:

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // bool annotated = 3;
  void clear_annotated();
  bool annotated() const;
  void set_annotated(bool value);
  private:
  bool _internal_annotated() const;
  void _internal_set_annotated(bool value);
  public:

  // bool include_annotations_metadata = 4;
  void clear_include_annotations_metadata();
  bool include_annotations_metadata() const;
  void set_include_annotations_metadata(bool value);
  private:
  bool _internal_include_annotations_metadata() const;
  void _internal_set_include_annotations_metadata(bool value);
  public:

  // bool dont_downsample = 5;
  void clear_dont_downsample();
  bool dont_downsample() const;
  void set_dont_downsample(bool value);
  private:
  bool _internal_dont_downsample() const;
  void _internal_set_dont_downsample(bool value);
  public:

  // bool encode_as_png = 6;
  void clear_encode_as_png();
  bool encode_as_png() const;
  void set_encode_as_png(bool value);
  private:
  bool _internal_encode_as_png() const;
  void _internal_set_encode_as_png(bool value);
  public:

  // bool encode_as_raw = 7;
  void clear_encode_as_raw();
  bool encode_as_raw() const;
  void set_encode_as_raw(bool value);
  private:
  bool _internal_encode_as_raw() const;
  void _internal_set_encode_as_raw(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.CameraImageRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cam_id_;
  ::carbon::frontend::util::Timestamp* ts_;
  bool annotated_;
  bool include_annotations_metadata_;
  bool dont_downsample_;
  bool encode_as_png_;
  bool encode_as_raw_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fimage_5fstream_2eproto;
};
// -------------------------------------------------------------------

class GetPredictImageByTimestampRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.image_stream.GetPredictImageByTimestampRequest) */ {
 public:
  inline GetPredictImageByTimestampRequest() : GetPredictImageByTimestampRequest(nullptr) {}
  ~GetPredictImageByTimestampRequest() override;
  explicit constexpr GetPredictImageByTimestampRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPredictImageByTimestampRequest(const GetPredictImageByTimestampRequest& from);
  GetPredictImageByTimestampRequest(GetPredictImageByTimestampRequest&& from) noexcept
    : GetPredictImageByTimestampRequest() {
    *this = ::std::move(from);
  }

  inline GetPredictImageByTimestampRequest& operator=(const GetPredictImageByTimestampRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPredictImageByTimestampRequest& operator=(GetPredictImageByTimestampRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPredictImageByTimestampRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPredictImageByTimestampRequest* internal_default_instance() {
    return reinterpret_cast<const GetPredictImageByTimestampRequest*>(
               &_GetPredictImageByTimestampRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GetPredictImageByTimestampRequest& a, GetPredictImageByTimestampRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPredictImageByTimestampRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPredictImageByTimestampRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPredictImageByTimestampRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPredictImageByTimestampRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetPredictImageByTimestampRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetPredictImageByTimestampRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetPredictImageByTimestampRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.image_stream.GetPredictImageByTimestampRequest";
  }
  protected:
  explicit GetPredictImageByTimestampRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCamIdFieldNumber = 1,
    kTsFieldNumber = 2,
    kCropAroundXFieldNumber = 3,
    kCropAroundYFieldNumber = 4,
  };
  // string cam_id = 1;
  void clear_cam_id();
  const std::string& cam_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cam_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cam_id();
  PROTOBUF_NODISCARD std::string* release_cam_id();
  void set_allocated_cam_id(std::string* cam_id);
  private:
  const std::string& _internal_cam_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cam_id(const std::string& value);
  std::string* _internal_mutable_cam_id();
  public:

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // int32 crop_around_x = 3;
  void clear_crop_around_x();
  int32_t crop_around_x() const;
  void set_crop_around_x(int32_t value);
  private:
  int32_t _internal_crop_around_x() const;
  void _internal_set_crop_around_x(int32_t value);
  public:

  // int32 crop_around_y = 4;
  void clear_crop_around_y();
  int32_t crop_around_y() const;
  void set_crop_around_y(int32_t value);
  private:
  int32_t _internal_crop_around_y() const;
  void _internal_set_crop_around_y(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.GetPredictImageByTimestampRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cam_id_;
  ::carbon::frontend::util::Timestamp* ts_;
  int32_t crop_around_x_;
  int32_t crop_around_y_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fimage_5fstream_2eproto;
};
// -------------------------------------------------------------------

class GetPredictImageByTimestampResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.image_stream.GetPredictImageByTimestampResponse) */ {
 public:
  inline GetPredictImageByTimestampResponse() : GetPredictImageByTimestampResponse(nullptr) {}
  ~GetPredictImageByTimestampResponse() override;
  explicit constexpr GetPredictImageByTimestampResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPredictImageByTimestampResponse(const GetPredictImageByTimestampResponse& from);
  GetPredictImageByTimestampResponse(GetPredictImageByTimestampResponse&& from) noexcept
    : GetPredictImageByTimestampResponse() {
    *this = ::std::move(from);
  }

  inline GetPredictImageByTimestampResponse& operator=(const GetPredictImageByTimestampResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPredictImageByTimestampResponse& operator=(GetPredictImageByTimestampResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPredictImageByTimestampResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPredictImageByTimestampResponse* internal_default_instance() {
    return reinterpret_cast<const GetPredictImageByTimestampResponse*>(
               &_GetPredictImageByTimestampResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetPredictImageByTimestampResponse& a, GetPredictImageByTimestampResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPredictImageByTimestampResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPredictImageByTimestampResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPredictImageByTimestampResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPredictImageByTimestampResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetPredictImageByTimestampResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetPredictImageByTimestampResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetPredictImageByTimestampResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.image_stream.GetPredictImageByTimestampResponse";
  }
  protected:
  explicit GetPredictImageByTimestampResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
    kCenterXFieldNumber = 2,
    kCenterYFieldNumber = 3,
  };
  // bytes data = 1;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // int32 center_x = 2;
  void clear_center_x();
  int32_t center_x() const;
  void set_center_x(int32_t value);
  private:
  int32_t _internal_center_x() const;
  void _internal_set_center_x(int32_t value);
  public:

  // int32 center_y = 3;
  void clear_center_y();
  int32_t center_y() const;
  void set_center_y(int32_t value);
  private:
  int32_t _internal_center_y() const;
  void _internal_set_center_y(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.GetPredictImageByTimestampResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  int32_t center_x_;
  int32_t center_y_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fimage_5fstream_2eproto;
};
// -------------------------------------------------------------------

class PossiblePerspective final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.image_stream.PossiblePerspective) */ {
 public:
  inline PossiblePerspective() : PossiblePerspective(nullptr) {}
  ~PossiblePerspective() override;
  explicit constexpr PossiblePerspective(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PossiblePerspective(const PossiblePerspective& from);
  PossiblePerspective(PossiblePerspective&& from) noexcept
    : PossiblePerspective() {
    *this = ::std::move(from);
  }

  inline PossiblePerspective& operator=(const PossiblePerspective& from) {
    CopyFrom(from);
    return *this;
  }
  inline PossiblePerspective& operator=(PossiblePerspective&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PossiblePerspective& default_instance() {
    return *internal_default_instance();
  }
  static inline const PossiblePerspective* internal_default_instance() {
    return reinterpret_cast<const PossiblePerspective*>(
               &_PossiblePerspective_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(PossiblePerspective& a, PossiblePerspective& b) {
    a.Swap(&b);
  }
  inline void Swap(PossiblePerspective* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PossiblePerspective* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PossiblePerspective* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PossiblePerspective>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PossiblePerspective& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PossiblePerspective& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PossiblePerspective* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.image_stream.PossiblePerspective";
  }
  protected:
  explicit PossiblePerspective(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kCropAroundXFieldNumber = 2,
    kCropAroundYFieldNumber = 3,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // int32 crop_around_x = 2;
  void clear_crop_around_x();
  int32_t crop_around_x() const;
  void set_crop_around_x(int32_t value);
  private:
  int32_t _internal_crop_around_x() const;
  void _internal_set_crop_around_x(int32_t value);
  public:

  // int32 crop_around_y = 3;
  void clear_crop_around_y();
  int32_t crop_around_y() const;
  void set_crop_around_y(int32_t value);
  private:
  int32_t _internal_crop_around_y() const;
  void _internal_set_crop_around_y(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.PossiblePerspective)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  int32_t crop_around_x_;
  int32_t crop_around_y_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fimage_5fstream_2eproto;
};
// -------------------------------------------------------------------

class GetMultiPredictPerspectivesRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest) */ {
 public:
  inline GetMultiPredictPerspectivesRequest() : GetMultiPredictPerspectivesRequest(nullptr) {}
  ~GetMultiPredictPerspectivesRequest() override;
  explicit constexpr GetMultiPredictPerspectivesRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetMultiPredictPerspectivesRequest(const GetMultiPredictPerspectivesRequest& from);
  GetMultiPredictPerspectivesRequest(GetMultiPredictPerspectivesRequest&& from) noexcept
    : GetMultiPredictPerspectivesRequest() {
    *this = ::std::move(from);
  }

  inline GetMultiPredictPerspectivesRequest& operator=(const GetMultiPredictPerspectivesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetMultiPredictPerspectivesRequest& operator=(GetMultiPredictPerspectivesRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetMultiPredictPerspectivesRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetMultiPredictPerspectivesRequest* internal_default_instance() {
    return reinterpret_cast<const GetMultiPredictPerspectivesRequest*>(
               &_GetMultiPredictPerspectivesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(GetMultiPredictPerspectivesRequest& a, GetMultiPredictPerspectivesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetMultiPredictPerspectivesRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetMultiPredictPerspectivesRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetMultiPredictPerspectivesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetMultiPredictPerspectivesRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetMultiPredictPerspectivesRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetMultiPredictPerspectivesRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetMultiPredictPerspectivesRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest";
  }
  protected:
  explicit GetMultiPredictPerspectivesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPerspectivesFieldNumber = 2,
    kCamIdFieldNumber = 1,
    kRequestedPerspectivesFieldNumber = 3,
  };
  // repeated .carbon.frontend.image_stream.PossiblePerspective perspectives = 2;
  int perspectives_size() const;
  private:
  int _internal_perspectives_size() const;
  public:
  void clear_perspectives();
  ::carbon::frontend::image_stream::PossiblePerspective* mutable_perspectives(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::image_stream::PossiblePerspective >*
      mutable_perspectives();
  private:
  const ::carbon::frontend::image_stream::PossiblePerspective& _internal_perspectives(int index) const;
  ::carbon::frontend::image_stream::PossiblePerspective* _internal_add_perspectives();
  public:
  const ::carbon::frontend::image_stream::PossiblePerspective& perspectives(int index) const;
  ::carbon::frontend::image_stream::PossiblePerspective* add_perspectives();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::image_stream::PossiblePerspective >&
      perspectives() const;

  // string cam_id = 1;
  void clear_cam_id();
  const std::string& cam_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cam_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cam_id();
  PROTOBUF_NODISCARD std::string* release_cam_id();
  void set_allocated_cam_id(std::string* cam_id);
  private:
  const std::string& _internal_cam_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cam_id(const std::string& value);
  std::string* _internal_mutable_cam_id();
  public:

  // int32 requested_perspectives = 3;
  void clear_requested_perspectives();
  int32_t requested_perspectives() const;
  void set_requested_perspectives(int32_t value);
  private:
  int32_t _internal_requested_perspectives() const;
  void _internal_set_requested_perspectives(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::image_stream::PossiblePerspective > perspectives_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cam_id_;
  int32_t requested_perspectives_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fimage_5fstream_2eproto;
};
// -------------------------------------------------------------------

class CentroidPerspective final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.image_stream.CentroidPerspective) */ {
 public:
  inline CentroidPerspective() : CentroidPerspective(nullptr) {}
  ~CentroidPerspective() override;
  explicit constexpr CentroidPerspective(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CentroidPerspective(const CentroidPerspective& from);
  CentroidPerspective(CentroidPerspective&& from) noexcept
    : CentroidPerspective() {
    *this = ::std::move(from);
  }

  inline CentroidPerspective& operator=(const CentroidPerspective& from) {
    CopyFrom(from);
    return *this;
  }
  inline CentroidPerspective& operator=(CentroidPerspective&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CentroidPerspective& default_instance() {
    return *internal_default_instance();
  }
  static inline const CentroidPerspective* internal_default_instance() {
    return reinterpret_cast<const CentroidPerspective*>(
               &_CentroidPerspective_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(CentroidPerspective& a, CentroidPerspective& b) {
    a.Swap(&b);
  }
  inline void Swap(CentroidPerspective* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CentroidPerspective* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CentroidPerspective* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CentroidPerspective>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CentroidPerspective& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CentroidPerspective& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CentroidPerspective* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.image_stream.CentroidPerspective";
  }
  protected:
  explicit CentroidPerspective(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 4,
    kTsFieldNumber = 1,
    kCenterXFieldNumber = 2,
    kCenterYFieldNumber = 3,
  };
  // bytes data = 4;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // int32 center_x = 2;
  void clear_center_x();
  int32_t center_x() const;
  void set_center_x(int32_t value);
  private:
  int32_t _internal_center_x() const;
  void _internal_set_center_x(int32_t value);
  public:

  // int32 center_y = 3;
  void clear_center_y();
  int32_t center_y() const;
  void set_center_y(int32_t value);
  private:
  int32_t _internal_center_y() const;
  void _internal_set_center_y(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.CentroidPerspective)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  ::carbon::frontend::util::Timestamp* ts_;
  int32_t center_x_;
  int32_t center_y_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fimage_5fstream_2eproto;
};
// -------------------------------------------------------------------

class GetMultiPredictPerspectivesResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse) */ {
 public:
  inline GetMultiPredictPerspectivesResponse() : GetMultiPredictPerspectivesResponse(nullptr) {}
  ~GetMultiPredictPerspectivesResponse() override;
  explicit constexpr GetMultiPredictPerspectivesResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetMultiPredictPerspectivesResponse(const GetMultiPredictPerspectivesResponse& from);
  GetMultiPredictPerspectivesResponse(GetMultiPredictPerspectivesResponse&& from) noexcept
    : GetMultiPredictPerspectivesResponse() {
    *this = ::std::move(from);
  }

  inline GetMultiPredictPerspectivesResponse& operator=(const GetMultiPredictPerspectivesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetMultiPredictPerspectivesResponse& operator=(GetMultiPredictPerspectivesResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetMultiPredictPerspectivesResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetMultiPredictPerspectivesResponse* internal_default_instance() {
    return reinterpret_cast<const GetMultiPredictPerspectivesResponse*>(
               &_GetMultiPredictPerspectivesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(GetMultiPredictPerspectivesResponse& a, GetMultiPredictPerspectivesResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetMultiPredictPerspectivesResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetMultiPredictPerspectivesResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetMultiPredictPerspectivesResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetMultiPredictPerspectivesResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetMultiPredictPerspectivesResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetMultiPredictPerspectivesResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetMultiPredictPerspectivesResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse";
  }
  protected:
  explicit GetMultiPredictPerspectivesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPerspectivesFieldNumber = 1,
  };
  // repeated .carbon.frontend.image_stream.CentroidPerspective perspectives = 1;
  int perspectives_size() const;
  private:
  int _internal_perspectives_size() const;
  public:
  void clear_perspectives();
  ::carbon::frontend::image_stream::CentroidPerspective* mutable_perspectives(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::image_stream::CentroidPerspective >*
      mutable_perspectives();
  private:
  const ::carbon::frontend::image_stream::CentroidPerspective& _internal_perspectives(int index) const;
  ::carbon::frontend::image_stream::CentroidPerspective* _internal_add_perspectives();
  public:
  const ::carbon::frontend::image_stream::CentroidPerspective& perspectives(int index) const;
  ::carbon::frontend::image_stream::CentroidPerspective* add_perspectives();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::image_stream::CentroidPerspective >&
      perspectives() const;

  // @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::image_stream::CentroidPerspective > perspectives_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fimage_5fstream_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Annotations

// .weed_tracking.Detections detections = 1;
inline bool Annotations::_internal_has_detections() const {
  return this != internal_default_instance() && detections_ != nullptr;
}
inline bool Annotations::has_detections() const {
  return _internal_has_detections();
}
inline const ::weed_tracking::Detections& Annotations::_internal_detections() const {
  const ::weed_tracking::Detections* p = detections_;
  return p != nullptr ? *p : reinterpret_cast<const ::weed_tracking::Detections&>(
      ::weed_tracking::_Detections_default_instance_);
}
inline const ::weed_tracking::Detections& Annotations::detections() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.Annotations.detections)
  return _internal_detections();
}
inline void Annotations::unsafe_arena_set_allocated_detections(
    ::weed_tracking::Detections* detections) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(detections_);
  }
  detections_ = detections;
  if (detections) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.image_stream.Annotations.detections)
}
inline ::weed_tracking::Detections* Annotations::release_detections() {
  
  ::weed_tracking::Detections* temp = detections_;
  detections_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::weed_tracking::Detections* Annotations::unsafe_arena_release_detections() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.Annotations.detections)
  
  ::weed_tracking::Detections* temp = detections_;
  detections_ = nullptr;
  return temp;
}
inline ::weed_tracking::Detections* Annotations::_internal_mutable_detections() {
  
  if (detections_ == nullptr) {
    auto* p = CreateMaybeMessage<::weed_tracking::Detections>(GetArenaForAllocation());
    detections_ = p;
  }
  return detections_;
}
inline ::weed_tracking::Detections* Annotations::mutable_detections() {
  ::weed_tracking::Detections* _msg = _internal_mutable_detections();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.Annotations.detections)
  return _msg;
}
inline void Annotations::set_allocated_detections(::weed_tracking::Detections* detections) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(detections_);
  }
  if (detections) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(detections));
    if (message_arena != submessage_arena) {
      detections = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, detections, submessage_arena);
    }
    
  } else {
    
  }
  detections_ = detections;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.Annotations.detections)
}

// .weed_tracking.Bands bands = 2;
inline bool Annotations::_internal_has_bands() const {
  return this != internal_default_instance() && bands_ != nullptr;
}
inline bool Annotations::has_bands() const {
  return _internal_has_bands();
}
inline const ::weed_tracking::Bands& Annotations::_internal_bands() const {
  const ::weed_tracking::Bands* p = bands_;
  return p != nullptr ? *p : reinterpret_cast<const ::weed_tracking::Bands&>(
      ::weed_tracking::_Bands_default_instance_);
}
inline const ::weed_tracking::Bands& Annotations::bands() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.Annotations.bands)
  return _internal_bands();
}
inline void Annotations::unsafe_arena_set_allocated_bands(
    ::weed_tracking::Bands* bands) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bands_);
  }
  bands_ = bands;
  if (bands) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.image_stream.Annotations.bands)
}
inline ::weed_tracking::Bands* Annotations::release_bands() {
  
  ::weed_tracking::Bands* temp = bands_;
  bands_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::weed_tracking::Bands* Annotations::unsafe_arena_release_bands() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.Annotations.bands)
  
  ::weed_tracking::Bands* temp = bands_;
  bands_ = nullptr;
  return temp;
}
inline ::weed_tracking::Bands* Annotations::_internal_mutable_bands() {
  
  if (bands_ == nullptr) {
    auto* p = CreateMaybeMessage<::weed_tracking::Bands>(GetArenaForAllocation());
    bands_ = p;
  }
  return bands_;
}
inline ::weed_tracking::Bands* Annotations::mutable_bands() {
  ::weed_tracking::Bands* _msg = _internal_mutable_bands();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.Annotations.bands)
  return _msg;
}
inline void Annotations::set_allocated_bands(::weed_tracking::Bands* bands) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(bands_);
  }
  if (bands) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bands));
    if (message_arena != submessage_arena) {
      bands = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bands, submessage_arena);
    }
    
  } else {
    
  }
  bands_ = bands;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.Annotations.bands)
}

// int32 crosshair_x = 3;
inline void Annotations::clear_crosshair_x() {
  crosshair_x_ = 0;
}
inline int32_t Annotations::_internal_crosshair_x() const {
  return crosshair_x_;
}
inline int32_t Annotations::crosshair_x() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.Annotations.crosshair_x)
  return _internal_crosshair_x();
}
inline void Annotations::_internal_set_crosshair_x(int32_t value) {
  
  crosshair_x_ = value;
}
inline void Annotations::set_crosshair_x(int32_t value) {
  _internal_set_crosshair_x(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.Annotations.crosshair_x)
}

// int32 crosshair_y = 4;
inline void Annotations::clear_crosshair_y() {
  crosshair_y_ = 0;
}
inline int32_t Annotations::_internal_crosshair_y() const {
  return crosshair_y_;
}
inline int32_t Annotations::crosshair_y() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.Annotations.crosshair_y)
  return _internal_crosshair_y();
}
inline void Annotations::_internal_set_crosshair_y(int32_t value) {
  
  crosshair_y_ = value;
}
inline void Annotations::set_crosshair_y(int32_t value) {
  _internal_set_crosshair_y(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.Annotations.crosshair_y)
}

// -------------------------------------------------------------------

// Image

// .carbon.frontend.util.Timestamp ts = 1;
inline bool Image::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool Image::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& Image::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& Image::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.Image.ts)
  return _internal_ts();
}
inline void Image::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.image_stream.Image.ts)
}
inline ::carbon::frontend::util::Timestamp* Image::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Image::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.Image.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Image::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* Image::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.Image.ts)
  return _msg;
}
inline void Image::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.Image.ts)
}

// uint32 width = 2;
inline void Image::clear_width() {
  width_ = 0u;
}
inline uint32_t Image::_internal_width() const {
  return width_;
}
inline uint32_t Image::width() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.Image.width)
  return _internal_width();
}
inline void Image::_internal_set_width(uint32_t value) {
  
  width_ = value;
}
inline void Image::set_width(uint32_t value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.Image.width)
}

// uint32 height = 3;
inline void Image::clear_height() {
  height_ = 0u;
}
inline uint32_t Image::_internal_height() const {
  return height_;
}
inline uint32_t Image::height() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.Image.height)
  return _internal_height();
}
inline void Image::_internal_set_height(uint32_t value) {
  
  height_ = value;
}
inline void Image::set_height(uint32_t value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.Image.height)
}

// double focus = 4;
inline void Image::clear_focus() {
  focus_ = 0;
}
inline double Image::_internal_focus() const {
  return focus_;
}
inline double Image::focus() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.Image.focus)
  return _internal_focus();
}
inline void Image::_internal_set_focus(double value) {
  
  focus_ = value;
}
inline void Image::set_focus(double value) {
  _internal_set_focus(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.Image.focus)
}

// bytes data = 5;
inline void Image::clear_data() {
  data_.ClearToEmpty();
}
inline const std::string& Image::data() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.Image.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Image::set_data(ArgT0&& arg0, ArgT... args) {
 
 data_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.Image.data)
}
inline std::string* Image::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.Image.data)
  return _s;
}
inline const std::string& Image::_internal_data() const {
  return data_.Get();
}
inline void Image::_internal_set_data(const std::string& value) {
  
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Image::_internal_mutable_data() {
  
  return data_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Image::release_data() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.Image.data)
  return data_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Image::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  data_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), data,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (data_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.Image.data)
}

// .carbon.frontend.image_stream.Annotations annotations = 6;
inline bool Image::_internal_has_annotations() const {
  return this != internal_default_instance() && annotations_ != nullptr;
}
inline bool Image::has_annotations() const {
  return _internal_has_annotations();
}
inline void Image::clear_annotations() {
  if (GetArenaForAllocation() == nullptr && annotations_ != nullptr) {
    delete annotations_;
  }
  annotations_ = nullptr;
}
inline const ::carbon::frontend::image_stream::Annotations& Image::_internal_annotations() const {
  const ::carbon::frontend::image_stream::Annotations* p = annotations_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::image_stream::Annotations&>(
      ::carbon::frontend::image_stream::_Annotations_default_instance_);
}
inline const ::carbon::frontend::image_stream::Annotations& Image::annotations() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.Image.annotations)
  return _internal_annotations();
}
inline void Image::unsafe_arena_set_allocated_annotations(
    ::carbon::frontend::image_stream::Annotations* annotations) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(annotations_);
  }
  annotations_ = annotations;
  if (annotations) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.image_stream.Image.annotations)
}
inline ::carbon::frontend::image_stream::Annotations* Image::release_annotations() {
  
  ::carbon::frontend::image_stream::Annotations* temp = annotations_;
  annotations_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::image_stream::Annotations* Image::unsafe_arena_release_annotations() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.Image.annotations)
  
  ::carbon::frontend::image_stream::Annotations* temp = annotations_;
  annotations_ = nullptr;
  return temp;
}
inline ::carbon::frontend::image_stream::Annotations* Image::_internal_mutable_annotations() {
  
  if (annotations_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::image_stream::Annotations>(GetArenaForAllocation());
    annotations_ = p;
  }
  return annotations_;
}
inline ::carbon::frontend::image_stream::Annotations* Image::mutable_annotations() {
  ::carbon::frontend::image_stream::Annotations* _msg = _internal_mutable_annotations();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.Image.annotations)
  return _msg;
}
inline void Image::set_allocated_annotations(::carbon::frontend::image_stream::Annotations* annotations) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete annotations_;
  }
  if (annotations) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::image_stream::Annotations>::GetOwningArena(annotations);
    if (message_arena != submessage_arena) {
      annotations = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, annotations, submessage_arena);
    }
    
  } else {
    
  }
  annotations_ = annotations;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.Image.annotations)
}

// -------------------------------------------------------------------

// CameraImageRequest

// string cam_id = 1;
inline void CameraImageRequest::clear_cam_id() {
  cam_id_.ClearToEmpty();
}
inline const std::string& CameraImageRequest::cam_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.CameraImageRequest.cam_id)
  return _internal_cam_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CameraImageRequest::set_cam_id(ArgT0&& arg0, ArgT... args) {
 
 cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.CameraImageRequest.cam_id)
}
inline std::string* CameraImageRequest::mutable_cam_id() {
  std::string* _s = _internal_mutable_cam_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.CameraImageRequest.cam_id)
  return _s;
}
inline const std::string& CameraImageRequest::_internal_cam_id() const {
  return cam_id_.Get();
}
inline void CameraImageRequest::_internal_set_cam_id(const std::string& value) {
  
  cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CameraImageRequest::_internal_mutable_cam_id() {
  
  return cam_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CameraImageRequest::release_cam_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.CameraImageRequest.cam_id)
  return cam_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CameraImageRequest::set_allocated_cam_id(std::string* cam_id) {
  if (cam_id != nullptr) {
    
  } else {
    
  }
  cam_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cam_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.CameraImageRequest.cam_id)
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool CameraImageRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool CameraImageRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& CameraImageRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& CameraImageRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.CameraImageRequest.ts)
  return _internal_ts();
}
inline void CameraImageRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.image_stream.CameraImageRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* CameraImageRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* CameraImageRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.CameraImageRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* CameraImageRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* CameraImageRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.CameraImageRequest.ts)
  return _msg;
}
inline void CameraImageRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.CameraImageRequest.ts)
}

// bool annotated = 3;
inline void CameraImageRequest::clear_annotated() {
  annotated_ = false;
}
inline bool CameraImageRequest::_internal_annotated() const {
  return annotated_;
}
inline bool CameraImageRequest::annotated() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.CameraImageRequest.annotated)
  return _internal_annotated();
}
inline void CameraImageRequest::_internal_set_annotated(bool value) {
  
  annotated_ = value;
}
inline void CameraImageRequest::set_annotated(bool value) {
  _internal_set_annotated(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.CameraImageRequest.annotated)
}

// bool include_annotations_metadata = 4;
inline void CameraImageRequest::clear_include_annotations_metadata() {
  include_annotations_metadata_ = false;
}
inline bool CameraImageRequest::_internal_include_annotations_metadata() const {
  return include_annotations_metadata_;
}
inline bool CameraImageRequest::include_annotations_metadata() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.CameraImageRequest.include_annotations_metadata)
  return _internal_include_annotations_metadata();
}
inline void CameraImageRequest::_internal_set_include_annotations_metadata(bool value) {
  
  include_annotations_metadata_ = value;
}
inline void CameraImageRequest::set_include_annotations_metadata(bool value) {
  _internal_set_include_annotations_metadata(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.CameraImageRequest.include_annotations_metadata)
}

// bool dont_downsample = 5;
inline void CameraImageRequest::clear_dont_downsample() {
  dont_downsample_ = false;
}
inline bool CameraImageRequest::_internal_dont_downsample() const {
  return dont_downsample_;
}
inline bool CameraImageRequest::dont_downsample() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.CameraImageRequest.dont_downsample)
  return _internal_dont_downsample();
}
inline void CameraImageRequest::_internal_set_dont_downsample(bool value) {
  
  dont_downsample_ = value;
}
inline void CameraImageRequest::set_dont_downsample(bool value) {
  _internal_set_dont_downsample(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.CameraImageRequest.dont_downsample)
}

// bool encode_as_png = 6;
inline void CameraImageRequest::clear_encode_as_png() {
  encode_as_png_ = false;
}
inline bool CameraImageRequest::_internal_encode_as_png() const {
  return encode_as_png_;
}
inline bool CameraImageRequest::encode_as_png() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.CameraImageRequest.encode_as_png)
  return _internal_encode_as_png();
}
inline void CameraImageRequest::_internal_set_encode_as_png(bool value) {
  
  encode_as_png_ = value;
}
inline void CameraImageRequest::set_encode_as_png(bool value) {
  _internal_set_encode_as_png(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.CameraImageRequest.encode_as_png)
}

// bool encode_as_raw = 7;
inline void CameraImageRequest::clear_encode_as_raw() {
  encode_as_raw_ = false;
}
inline bool CameraImageRequest::_internal_encode_as_raw() const {
  return encode_as_raw_;
}
inline bool CameraImageRequest::encode_as_raw() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.CameraImageRequest.encode_as_raw)
  return _internal_encode_as_raw();
}
inline void CameraImageRequest::_internal_set_encode_as_raw(bool value) {
  
  encode_as_raw_ = value;
}
inline void CameraImageRequest::set_encode_as_raw(bool value) {
  _internal_set_encode_as_raw(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.CameraImageRequest.encode_as_raw)
}

// -------------------------------------------------------------------

// GetPredictImageByTimestampRequest

// string cam_id = 1;
inline void GetPredictImageByTimestampRequest::clear_cam_id() {
  cam_id_.ClearToEmpty();
}
inline const std::string& GetPredictImageByTimestampRequest::cam_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.cam_id)
  return _internal_cam_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetPredictImageByTimestampRequest::set_cam_id(ArgT0&& arg0, ArgT... args) {
 
 cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.cam_id)
}
inline std::string* GetPredictImageByTimestampRequest::mutable_cam_id() {
  std::string* _s = _internal_mutable_cam_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.cam_id)
  return _s;
}
inline const std::string& GetPredictImageByTimestampRequest::_internal_cam_id() const {
  return cam_id_.Get();
}
inline void GetPredictImageByTimestampRequest::_internal_set_cam_id(const std::string& value) {
  
  cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetPredictImageByTimestampRequest::_internal_mutable_cam_id() {
  
  return cam_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetPredictImageByTimestampRequest::release_cam_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.cam_id)
  return cam_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetPredictImageByTimestampRequest::set_allocated_cam_id(std::string* cam_id) {
  if (cam_id != nullptr) {
    
  } else {
    
  }
  cam_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cam_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.cam_id)
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool GetPredictImageByTimestampRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetPredictImageByTimestampRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetPredictImageByTimestampRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetPredictImageByTimestampRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.ts)
  return _internal_ts();
}
inline void GetPredictImageByTimestampRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetPredictImageByTimestampRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetPredictImageByTimestampRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetPredictImageByTimestampRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetPredictImageByTimestampRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.ts)
  return _msg;
}
inline void GetPredictImageByTimestampRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.ts)
}

// int32 crop_around_x = 3;
inline void GetPredictImageByTimestampRequest::clear_crop_around_x() {
  crop_around_x_ = 0;
}
inline int32_t GetPredictImageByTimestampRequest::_internal_crop_around_x() const {
  return crop_around_x_;
}
inline int32_t GetPredictImageByTimestampRequest::crop_around_x() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.crop_around_x)
  return _internal_crop_around_x();
}
inline void GetPredictImageByTimestampRequest::_internal_set_crop_around_x(int32_t value) {
  
  crop_around_x_ = value;
}
inline void GetPredictImageByTimestampRequest::set_crop_around_x(int32_t value) {
  _internal_set_crop_around_x(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.crop_around_x)
}

// int32 crop_around_y = 4;
inline void GetPredictImageByTimestampRequest::clear_crop_around_y() {
  crop_around_y_ = 0;
}
inline int32_t GetPredictImageByTimestampRequest::_internal_crop_around_y() const {
  return crop_around_y_;
}
inline int32_t GetPredictImageByTimestampRequest::crop_around_y() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.crop_around_y)
  return _internal_crop_around_y();
}
inline void GetPredictImageByTimestampRequest::_internal_set_crop_around_y(int32_t value) {
  
  crop_around_y_ = value;
}
inline void GetPredictImageByTimestampRequest::set_crop_around_y(int32_t value) {
  _internal_set_crop_around_y(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.GetPredictImageByTimestampRequest.crop_around_y)
}

// -------------------------------------------------------------------

// GetPredictImageByTimestampResponse

// bytes data = 1;
inline void GetPredictImageByTimestampResponse::clear_data() {
  data_.ClearToEmpty();
}
inline const std::string& GetPredictImageByTimestampResponse::data() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.GetPredictImageByTimestampResponse.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetPredictImageByTimestampResponse::set_data(ArgT0&& arg0, ArgT... args) {
 
 data_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.GetPredictImageByTimestampResponse.data)
}
inline std::string* GetPredictImageByTimestampResponse::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.GetPredictImageByTimestampResponse.data)
  return _s;
}
inline const std::string& GetPredictImageByTimestampResponse::_internal_data() const {
  return data_.Get();
}
inline void GetPredictImageByTimestampResponse::_internal_set_data(const std::string& value) {
  
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetPredictImageByTimestampResponse::_internal_mutable_data() {
  
  return data_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetPredictImageByTimestampResponse::release_data() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.GetPredictImageByTimestampResponse.data)
  return data_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetPredictImageByTimestampResponse::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  data_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), data,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (data_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.GetPredictImageByTimestampResponse.data)
}

// int32 center_x = 2;
inline void GetPredictImageByTimestampResponse::clear_center_x() {
  center_x_ = 0;
}
inline int32_t GetPredictImageByTimestampResponse::_internal_center_x() const {
  return center_x_;
}
inline int32_t GetPredictImageByTimestampResponse::center_x() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.GetPredictImageByTimestampResponse.center_x)
  return _internal_center_x();
}
inline void GetPredictImageByTimestampResponse::_internal_set_center_x(int32_t value) {
  
  center_x_ = value;
}
inline void GetPredictImageByTimestampResponse::set_center_x(int32_t value) {
  _internal_set_center_x(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.GetPredictImageByTimestampResponse.center_x)
}

// int32 center_y = 3;
inline void GetPredictImageByTimestampResponse::clear_center_y() {
  center_y_ = 0;
}
inline int32_t GetPredictImageByTimestampResponse::_internal_center_y() const {
  return center_y_;
}
inline int32_t GetPredictImageByTimestampResponse::center_y() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.GetPredictImageByTimestampResponse.center_y)
  return _internal_center_y();
}
inline void GetPredictImageByTimestampResponse::_internal_set_center_y(int32_t value) {
  
  center_y_ = value;
}
inline void GetPredictImageByTimestampResponse::set_center_y(int32_t value) {
  _internal_set_center_y(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.GetPredictImageByTimestampResponse.center_y)
}

// -------------------------------------------------------------------

// PossiblePerspective

// .carbon.frontend.util.Timestamp ts = 1;
inline bool PossiblePerspective::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool PossiblePerspective::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& PossiblePerspective::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& PossiblePerspective::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.PossiblePerspective.ts)
  return _internal_ts();
}
inline void PossiblePerspective::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.image_stream.PossiblePerspective.ts)
}
inline ::carbon::frontend::util::Timestamp* PossiblePerspective::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* PossiblePerspective::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.PossiblePerspective.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* PossiblePerspective::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* PossiblePerspective::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.PossiblePerspective.ts)
  return _msg;
}
inline void PossiblePerspective::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.PossiblePerspective.ts)
}

// int32 crop_around_x = 2;
inline void PossiblePerspective::clear_crop_around_x() {
  crop_around_x_ = 0;
}
inline int32_t PossiblePerspective::_internal_crop_around_x() const {
  return crop_around_x_;
}
inline int32_t PossiblePerspective::crop_around_x() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.PossiblePerspective.crop_around_x)
  return _internal_crop_around_x();
}
inline void PossiblePerspective::_internal_set_crop_around_x(int32_t value) {
  
  crop_around_x_ = value;
}
inline void PossiblePerspective::set_crop_around_x(int32_t value) {
  _internal_set_crop_around_x(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.PossiblePerspective.crop_around_x)
}

// int32 crop_around_y = 3;
inline void PossiblePerspective::clear_crop_around_y() {
  crop_around_y_ = 0;
}
inline int32_t PossiblePerspective::_internal_crop_around_y() const {
  return crop_around_y_;
}
inline int32_t PossiblePerspective::crop_around_y() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.PossiblePerspective.crop_around_y)
  return _internal_crop_around_y();
}
inline void PossiblePerspective::_internal_set_crop_around_y(int32_t value) {
  
  crop_around_y_ = value;
}
inline void PossiblePerspective::set_crop_around_y(int32_t value) {
  _internal_set_crop_around_y(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.PossiblePerspective.crop_around_y)
}

// -------------------------------------------------------------------

// GetMultiPredictPerspectivesRequest

// string cam_id = 1;
inline void GetMultiPredictPerspectivesRequest::clear_cam_id() {
  cam_id_.ClearToEmpty();
}
inline const std::string& GetMultiPredictPerspectivesRequest::cam_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.cam_id)
  return _internal_cam_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetMultiPredictPerspectivesRequest::set_cam_id(ArgT0&& arg0, ArgT... args) {
 
 cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.cam_id)
}
inline std::string* GetMultiPredictPerspectivesRequest::mutable_cam_id() {
  std::string* _s = _internal_mutable_cam_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.cam_id)
  return _s;
}
inline const std::string& GetMultiPredictPerspectivesRequest::_internal_cam_id() const {
  return cam_id_.Get();
}
inline void GetMultiPredictPerspectivesRequest::_internal_set_cam_id(const std::string& value) {
  
  cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetMultiPredictPerspectivesRequest::_internal_mutable_cam_id() {
  
  return cam_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetMultiPredictPerspectivesRequest::release_cam_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.cam_id)
  return cam_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetMultiPredictPerspectivesRequest::set_allocated_cam_id(std::string* cam_id) {
  if (cam_id != nullptr) {
    
  } else {
    
  }
  cam_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cam_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.cam_id)
}

// repeated .carbon.frontend.image_stream.PossiblePerspective perspectives = 2;
inline int GetMultiPredictPerspectivesRequest::_internal_perspectives_size() const {
  return perspectives_.size();
}
inline int GetMultiPredictPerspectivesRequest::perspectives_size() const {
  return _internal_perspectives_size();
}
inline void GetMultiPredictPerspectivesRequest::clear_perspectives() {
  perspectives_.Clear();
}
inline ::carbon::frontend::image_stream::PossiblePerspective* GetMultiPredictPerspectivesRequest::mutable_perspectives(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.perspectives)
  return perspectives_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::image_stream::PossiblePerspective >*
GetMultiPredictPerspectivesRequest::mutable_perspectives() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.perspectives)
  return &perspectives_;
}
inline const ::carbon::frontend::image_stream::PossiblePerspective& GetMultiPredictPerspectivesRequest::_internal_perspectives(int index) const {
  return perspectives_.Get(index);
}
inline const ::carbon::frontend::image_stream::PossiblePerspective& GetMultiPredictPerspectivesRequest::perspectives(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.perspectives)
  return _internal_perspectives(index);
}
inline ::carbon::frontend::image_stream::PossiblePerspective* GetMultiPredictPerspectivesRequest::_internal_add_perspectives() {
  return perspectives_.Add();
}
inline ::carbon::frontend::image_stream::PossiblePerspective* GetMultiPredictPerspectivesRequest::add_perspectives() {
  ::carbon::frontend::image_stream::PossiblePerspective* _add = _internal_add_perspectives();
  // @@protoc_insertion_point(field_add:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.perspectives)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::image_stream::PossiblePerspective >&
GetMultiPredictPerspectivesRequest::perspectives() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.perspectives)
  return perspectives_;
}

// int32 requested_perspectives = 3;
inline void GetMultiPredictPerspectivesRequest::clear_requested_perspectives() {
  requested_perspectives_ = 0;
}
inline int32_t GetMultiPredictPerspectivesRequest::_internal_requested_perspectives() const {
  return requested_perspectives_;
}
inline int32_t GetMultiPredictPerspectivesRequest::requested_perspectives() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.requested_perspectives)
  return _internal_requested_perspectives();
}
inline void GetMultiPredictPerspectivesRequest::_internal_set_requested_perspectives(int32_t value) {
  
  requested_perspectives_ = value;
}
inline void GetMultiPredictPerspectivesRequest::set_requested_perspectives(int32_t value) {
  _internal_set_requested_perspectives(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.requested_perspectives)
}

// -------------------------------------------------------------------

// CentroidPerspective

// .carbon.frontend.util.Timestamp ts = 1;
inline bool CentroidPerspective::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool CentroidPerspective::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& CentroidPerspective::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& CentroidPerspective::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.CentroidPerspective.ts)
  return _internal_ts();
}
inline void CentroidPerspective::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.image_stream.CentroidPerspective.ts)
}
inline ::carbon::frontend::util::Timestamp* CentroidPerspective::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* CentroidPerspective::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.CentroidPerspective.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* CentroidPerspective::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* CentroidPerspective::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.CentroidPerspective.ts)
  return _msg;
}
inline void CentroidPerspective::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.CentroidPerspective.ts)
}

// int32 center_x = 2;
inline void CentroidPerspective::clear_center_x() {
  center_x_ = 0;
}
inline int32_t CentroidPerspective::_internal_center_x() const {
  return center_x_;
}
inline int32_t CentroidPerspective::center_x() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.CentroidPerspective.center_x)
  return _internal_center_x();
}
inline void CentroidPerspective::_internal_set_center_x(int32_t value) {
  
  center_x_ = value;
}
inline void CentroidPerspective::set_center_x(int32_t value) {
  _internal_set_center_x(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.CentroidPerspective.center_x)
}

// int32 center_y = 3;
inline void CentroidPerspective::clear_center_y() {
  center_y_ = 0;
}
inline int32_t CentroidPerspective::_internal_center_y() const {
  return center_y_;
}
inline int32_t CentroidPerspective::center_y() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.CentroidPerspective.center_y)
  return _internal_center_y();
}
inline void CentroidPerspective::_internal_set_center_y(int32_t value) {
  
  center_y_ = value;
}
inline void CentroidPerspective::set_center_y(int32_t value) {
  _internal_set_center_y(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.CentroidPerspective.center_y)
}

// bytes data = 4;
inline void CentroidPerspective::clear_data() {
  data_.ClearToEmpty();
}
inline const std::string& CentroidPerspective::data() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.CentroidPerspective.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CentroidPerspective::set_data(ArgT0&& arg0, ArgT... args) {
 
 data_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.image_stream.CentroidPerspective.data)
}
inline std::string* CentroidPerspective::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.CentroidPerspective.data)
  return _s;
}
inline const std::string& CentroidPerspective::_internal_data() const {
  return data_.Get();
}
inline void CentroidPerspective::_internal_set_data(const std::string& value) {
  
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CentroidPerspective::_internal_mutable_data() {
  
  return data_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CentroidPerspective::release_data() {
  // @@protoc_insertion_point(field_release:carbon.frontend.image_stream.CentroidPerspective.data)
  return data_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CentroidPerspective::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  data_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), data,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (data_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.image_stream.CentroidPerspective.data)
}

// -------------------------------------------------------------------

// GetMultiPredictPerspectivesResponse

// repeated .carbon.frontend.image_stream.CentroidPerspective perspectives = 1;
inline int GetMultiPredictPerspectivesResponse::_internal_perspectives_size() const {
  return perspectives_.size();
}
inline int GetMultiPredictPerspectivesResponse::perspectives_size() const {
  return _internal_perspectives_size();
}
inline void GetMultiPredictPerspectivesResponse::clear_perspectives() {
  perspectives_.Clear();
}
inline ::carbon::frontend::image_stream::CentroidPerspective* GetMultiPredictPerspectivesResponse::mutable_perspectives(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse.perspectives)
  return perspectives_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::image_stream::CentroidPerspective >*
GetMultiPredictPerspectivesResponse::mutable_perspectives() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse.perspectives)
  return &perspectives_;
}
inline const ::carbon::frontend::image_stream::CentroidPerspective& GetMultiPredictPerspectivesResponse::_internal_perspectives(int index) const {
  return perspectives_.Get(index);
}
inline const ::carbon::frontend::image_stream::CentroidPerspective& GetMultiPredictPerspectivesResponse::perspectives(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse.perspectives)
  return _internal_perspectives(index);
}
inline ::carbon::frontend::image_stream::CentroidPerspective* GetMultiPredictPerspectivesResponse::_internal_add_perspectives() {
  return perspectives_.Add();
}
inline ::carbon::frontend::image_stream::CentroidPerspective* GetMultiPredictPerspectivesResponse::add_perspectives() {
  ::carbon::frontend::image_stream::CentroidPerspective* _add = _internal_add_perspectives();
  // @@protoc_insertion_point(field_add:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse.perspectives)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::image_stream::CentroidPerspective >&
GetMultiPredictPerspectivesResponse::perspectives() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse.perspectives)
  return perspectives_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace image_stream
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fimage_5fstream_2eproto

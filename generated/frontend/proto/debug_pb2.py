# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/debug.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import alarm_pb2 as frontend_dot_proto_dot_alarm__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2
from generated.proto.logging import logging_pb2 as proto_dot_logging_dot_logging__pb2
from generated.weed_tracking.proto import weed_tracking_pb2 as weed__tracking_dot_proto_dot_weed__tracking__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/debug.proto',
  package='carbon.frontend.debug',
  syntax='proto3',
  serialized_options=b'H\003Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1a\x66rontend/proto/debug.proto\x12\x15\x63\x61rbon.frontend.debug\x1a\x1a\x66rontend/proto/alarm.proto\x1a\x19\x66rontend/proto/util.proto\x1a\x1bproto/logging/logging.proto\x1a\'weed_tracking/proto/weed_tracking.proto\"O\n\x0cRobotMessage\x12\x0e\n\x06serial\x18\x01 \x01(\t\x12/\n\x06\x61larms\x18\x02 \x03(\x0b\x32\x1f.carbon.frontend.alarm.AlarmRow\"a\n\x12SetLogLevelRequest\x12\'\n\x05level\x18\x01 \x01(\x0e\x32\x18.carbon.logging.LogLevel\x12\x11\n\tcomponent\x18\x02 \x01(\t\x12\x0f\n\x07row_num\x18\x03 \x01(\x05\x32\xb1\x04\n\x0c\x44\x65\x62ugService\x12L\n\x08GetRobot\x12\x1b.carbon.frontend.util.Empty\x1a#.carbon.frontend.debug.RobotMessage\x12U\n\x0bSetLogLevel\x12).carbon.frontend.debug.SetLogLevelRequest\x1a\x1b.carbon.frontend.util.Empty\x12t\n\"StartSavingCropLineDetectionReplay\x12\x38.weed_tracking.StartSavingCropLineDetectionReplayRequest\x1a\x14.weed_tracking.Empty\x12[\n\x1aStartRecordingAimbotInputs\x12\'.weed_tracking.RecordAimbotInputRequest\x1a\x14.weed_tracking.Empty\x12V\n\x1a\x41\x64\x64MockSpatialMetricsBlock\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12Q\n\x15\x44\x65leteProfileSyncData\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.EmptyB\x12H\x03Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_alarm__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,proto_dot_logging_dot_logging__pb2.DESCRIPTOR,weed__tracking_dot_proto_dot_weed__tracking__pb2.DESCRIPTOR,])




_ROBOTMESSAGE = _descriptor.Descriptor(
  name='RobotMessage',
  full_name='carbon.frontend.debug.RobotMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='serial', full_name='carbon.frontend.debug.RobotMessage.serial', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alarms', full_name='carbon.frontend.debug.RobotMessage.alarms', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=178,
  serialized_end=257,
)


_SETLOGLEVELREQUEST = _descriptor.Descriptor(
  name='SetLogLevelRequest',
  full_name='carbon.frontend.debug.SetLogLevelRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='level', full_name='carbon.frontend.debug.SetLogLevelRequest.level', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='component', full_name='carbon.frontend.debug.SetLogLevelRequest.component', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_num', full_name='carbon.frontend.debug.SetLogLevelRequest.row_num', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=259,
  serialized_end=356,
)

_ROBOTMESSAGE.fields_by_name['alarms'].message_type = frontend_dot_proto_dot_alarm__pb2._ALARMROW
_SETLOGLEVELREQUEST.fields_by_name['level'].enum_type = proto_dot_logging_dot_logging__pb2._LOGLEVEL
DESCRIPTOR.message_types_by_name['RobotMessage'] = _ROBOTMESSAGE
DESCRIPTOR.message_types_by_name['SetLogLevelRequest'] = _SETLOGLEVELREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RobotMessage = _reflection.GeneratedProtocolMessageType('RobotMessage', (_message.Message,), {
  'DESCRIPTOR' : _ROBOTMESSAGE,
  '__module__' : 'frontend.proto.debug_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.debug.RobotMessage)
  })
_sym_db.RegisterMessage(RobotMessage)

SetLogLevelRequest = _reflection.GeneratedProtocolMessageType('SetLogLevelRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETLOGLEVELREQUEST,
  '__module__' : 'frontend.proto.debug_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.debug.SetLogLevelRequest)
  })
_sym_db.RegisterMessage(SetLogLevelRequest)


DESCRIPTOR._options = None

_DEBUGSERVICE = _descriptor.ServiceDescriptor(
  name='DebugService',
  full_name='carbon.frontend.debug.DebugService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=359,
  serialized_end=920,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetRobot',
    full_name='carbon.frontend.debug.DebugService.GetRobot',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_ROBOTMESSAGE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetLogLevel',
    full_name='carbon.frontend.debug.DebugService.SetLogLevel',
    index=1,
    containing_service=None,
    input_type=_SETLOGLEVELREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartSavingCropLineDetectionReplay',
    full_name='carbon.frontend.debug.DebugService.StartSavingCropLineDetectionReplay',
    index=2,
    containing_service=None,
    input_type=weed__tracking_dot_proto_dot_weed__tracking__pb2._STARTSAVINGCROPLINEDETECTIONREPLAYREQUEST,
    output_type=weed__tracking_dot_proto_dot_weed__tracking__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartRecordingAimbotInputs',
    full_name='carbon.frontend.debug.DebugService.StartRecordingAimbotInputs',
    index=3,
    containing_service=None,
    input_type=weed__tracking_dot_proto_dot_weed__tracking__pb2._RECORDAIMBOTINPUTREQUEST,
    output_type=weed__tracking_dot_proto_dot_weed__tracking__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='AddMockSpatialMetricsBlock',
    full_name='carbon.frontend.debug.DebugService.AddMockSpatialMetricsBlock',
    index=4,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteProfileSyncData',
    full_name='carbon.frontend.debug.DebugService.DeleteProfileSyncData',
    index=5,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_DEBUGSERVICE)

DESCRIPTOR.services_by_name['DebugService'] = _DEBUGSERVICE

# @@protoc_insertion_point(module_scope)

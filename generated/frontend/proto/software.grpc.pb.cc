// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/software.proto

#include "frontend/proto/software.pb.h"
#include "frontend/proto/software.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace software {

static const char* SoftwareService_method_names[] = {
  "/carbon.frontend.software.SoftwareService/GetNextSoftwareVersionState",
  "/carbon.frontend.software.SoftwareService/UpdateHost",
  "/carbon.frontend.software.SoftwareService/Update",
  "/carbon.frontend.software.SoftwareService/Revert",
  "/carbon.frontend.software.SoftwareService/FixVersionMismatch",
};

std::unique_ptr< SoftwareService::Stub> SoftwareService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< SoftwareService::Stub> stub(new SoftwareService::Stub(channel, options));
  return stub;
}

SoftwareService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextSoftwareVersionState_(SoftwareService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UpdateHost_(SoftwareService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_Update_(SoftwareService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_Revert_(SoftwareService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_FixVersionMismatch_(SoftwareService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status SoftwareService::Stub::GetNextSoftwareVersionState(::grpc::ClientContext* context, const ::carbon::frontend::software::SoftwareVersionStateRequest& request, ::carbon::frontend::software::SoftwareVersionState* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::software::SoftwareVersionStateRequest, ::carbon::frontend::software::SoftwareVersionState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextSoftwareVersionState_, context, request, response);
}

void SoftwareService::Stub::async::GetNextSoftwareVersionState(::grpc::ClientContext* context, const ::carbon::frontend::software::SoftwareVersionStateRequest* request, ::carbon::frontend::software::SoftwareVersionState* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::software::SoftwareVersionStateRequest, ::carbon::frontend::software::SoftwareVersionState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextSoftwareVersionState_, context, request, response, std::move(f));
}

void SoftwareService::Stub::async::GetNextSoftwareVersionState(::grpc::ClientContext* context, const ::carbon::frontend::software::SoftwareVersionStateRequest* request, ::carbon::frontend::software::SoftwareVersionState* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextSoftwareVersionState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::software::SoftwareVersionState>* SoftwareService::Stub::PrepareAsyncGetNextSoftwareVersionStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::software::SoftwareVersionStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::software::SoftwareVersionState, ::carbon::frontend::software::SoftwareVersionStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextSoftwareVersionState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::software::SoftwareVersionState>* SoftwareService::Stub::AsyncGetNextSoftwareVersionStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::software::SoftwareVersionStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextSoftwareVersionStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status SoftwareService::Stub::UpdateHost(::grpc::ClientContext* context, const ::carbon::frontend::software::UpdateHostRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::software::UpdateHostRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UpdateHost_, context, request, response);
}

void SoftwareService::Stub::async::UpdateHost(::grpc::ClientContext* context, const ::carbon::frontend::software::UpdateHostRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::software::UpdateHostRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateHost_, context, request, response, std::move(f));
}

void SoftwareService::Stub::async::UpdateHost(::grpc::ClientContext* context, const ::carbon::frontend::software::UpdateHostRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateHost_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* SoftwareService::Stub::PrepareAsyncUpdateHostRaw(::grpc::ClientContext* context, const ::carbon::frontend::software::UpdateHostRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::software::UpdateHostRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UpdateHost_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* SoftwareService::Stub::AsyncUpdateHostRaw(::grpc::ClientContext* context, const ::carbon::frontend::software::UpdateHostRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUpdateHostRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status SoftwareService::Stub::Update(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Update_, context, request, response);
}

void SoftwareService::Stub::async::Update(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Update_, context, request, response, std::move(f));
}

void SoftwareService::Stub::async::Update(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Update_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* SoftwareService::Stub::PrepareAsyncUpdateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Update_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* SoftwareService::Stub::AsyncUpdateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUpdateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status SoftwareService::Stub::Revert(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Revert_, context, request, response);
}

void SoftwareService::Stub::async::Revert(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Revert_, context, request, response, std::move(f));
}

void SoftwareService::Stub::async::Revert(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Revert_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* SoftwareService::Stub::PrepareAsyncRevertRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Revert_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* SoftwareService::Stub::AsyncRevertRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncRevertRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status SoftwareService::Stub::FixVersionMismatch(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_FixVersionMismatch_, context, request, response);
}

void SoftwareService::Stub::async::FixVersionMismatch(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FixVersionMismatch_, context, request, response, std::move(f));
}

void SoftwareService::Stub::async::FixVersionMismatch(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FixVersionMismatch_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* SoftwareService::Stub::PrepareAsyncFixVersionMismatchRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_FixVersionMismatch_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* SoftwareService::Stub::AsyncFixVersionMismatchRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncFixVersionMismatchRaw(context, request, cq);
  result->StartCall();
  return result;
}

SoftwareService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SoftwareService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SoftwareService::Service, ::carbon::frontend::software::SoftwareVersionStateRequest, ::carbon::frontend::software::SoftwareVersionState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SoftwareService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::software::SoftwareVersionStateRequest* req,
             ::carbon::frontend::software::SoftwareVersionState* resp) {
               return service->GetNextSoftwareVersionState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SoftwareService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SoftwareService::Service, ::carbon::frontend::software::UpdateHostRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SoftwareService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::software::UpdateHostRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->UpdateHost(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SoftwareService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SoftwareService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SoftwareService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->Update(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SoftwareService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SoftwareService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SoftwareService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->Revert(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SoftwareService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SoftwareService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SoftwareService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->FixVersionMismatch(ctx, req, resp);
             }, this)));
}

SoftwareService::Service::~Service() {
}

::grpc::Status SoftwareService::Service::GetNextSoftwareVersionState(::grpc::ServerContext* context, const ::carbon::frontend::software::SoftwareVersionStateRequest* request, ::carbon::frontend::software::SoftwareVersionState* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status SoftwareService::Service::UpdateHost(::grpc::ServerContext* context, const ::carbon::frontend::software::UpdateHostRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status SoftwareService::Service::Update(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status SoftwareService::Service::Revert(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status SoftwareService::Service::FixVersionMismatch(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace software


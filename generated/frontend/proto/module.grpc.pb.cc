// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/module.proto

#include "frontend/proto/module.pb.h"
#include "frontend/proto/module.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace module {

static const char* ModuleAssignmentService_method_names[] = {
  "/carbon.frontend.module.ModuleAssignmentService/GetNextModulesList",
  "/carbon.frontend.module.ModuleAssignmentService/GetNextActiveModules",
  "/carbon.frontend.module.ModuleAssignmentService/IdentifyModule",
  "/carbon.frontend.module.ModuleAssignmentService/AssignModule",
  "/carbon.frontend.module.ModuleAssignmentService/ClearModuleAssignment",
  "/carbon.frontend.module.ModuleAssignmentService/SetModuleSerial",
  "/carbon.frontend.module.ModuleAssignmentService/GetPresetsList",
  "/carbon.frontend.module.ModuleAssignmentService/GetCurrentRobotDefinition",
  "/carbon.frontend.module.ModuleAssignmentService/SetCurrentRobotDefinition",
};

std::unique_ptr< ModuleAssignmentService::Stub> ModuleAssignmentService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ModuleAssignmentService::Stub> stub(new ModuleAssignmentService::Stub(channel, options));
  return stub;
}

ModuleAssignmentService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextModulesList_(ModuleAssignmentService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextActiveModules_(ModuleAssignmentService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_IdentifyModule_(ModuleAssignmentService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_AssignModule_(ModuleAssignmentService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ClearModuleAssignment_(ModuleAssignmentService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetModuleSerial_(ModuleAssignmentService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetPresetsList_(ModuleAssignmentService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetCurrentRobotDefinition_(ModuleAssignmentService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetCurrentRobotDefinition_(ModuleAssignmentService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ModuleAssignmentService::Stub::GetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::carbon::frontend::module::GetNextModulesListResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::module::GetNextModulesListRequest, ::carbon::frontend::module::GetNextModulesListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextModulesList_, context, request, response);
}

void ModuleAssignmentService::Stub::async::GetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest* request, ::carbon::frontend::module::GetNextModulesListResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::module::GetNextModulesListRequest, ::carbon::frontend::module::GetNextModulesListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextModulesList_, context, request, response, std::move(f));
}

void ModuleAssignmentService::Stub::async::GetNextModulesList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest* request, ::carbon::frontend::module::GetNextModulesListResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextModulesList_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextModulesListResponse>* ModuleAssignmentService::Stub::PrepareAsyncGetNextModulesListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::module::GetNextModulesListResponse, ::carbon::frontend::module::GetNextModulesListRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextModulesList_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextModulesListResponse>* ModuleAssignmentService::Stub::AsyncGetNextModulesListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextModulesListRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextModulesListRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModuleAssignmentService::Stub::GetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::carbon::frontend::module::GetNextActiveModulesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::module::GetNextActiveModulesRequest, ::carbon::frontend::module::GetNextActiveModulesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextActiveModules_, context, request, response);
}

void ModuleAssignmentService::Stub::async::GetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest* request, ::carbon::frontend::module::GetNextActiveModulesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::module::GetNextActiveModulesRequest, ::carbon::frontend::module::GetNextActiveModulesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveModules_, context, request, response, std::move(f));
}

void ModuleAssignmentService::Stub::async::GetNextActiveModules(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest* request, ::carbon::frontend::module::GetNextActiveModulesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveModules_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextActiveModulesResponse>* ModuleAssignmentService::Stub::PrepareAsyncGetNextActiveModulesRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::module::GetNextActiveModulesResponse, ::carbon::frontend::module::GetNextActiveModulesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextActiveModules_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetNextActiveModulesResponse>* ModuleAssignmentService::Stub::AsyncGetNextActiveModulesRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextActiveModulesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModuleAssignmentService::Stub::IdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::module::IdentifyModuleRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_IdentifyModule_, context, request, response);
}

void ModuleAssignmentService::Stub::async::IdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::module::IdentifyModuleRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_IdentifyModule_, context, request, response, std::move(f));
}

void ModuleAssignmentService::Stub::async::IdentifyModule(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_IdentifyModule_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModuleAssignmentService::Stub::PrepareAsyncIdentifyModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::module::IdentifyModuleRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_IdentifyModule_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModuleAssignmentService::Stub::AsyncIdentifyModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncIdentifyModuleRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModuleAssignmentService::Stub::AssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::module::AssignModuleRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_AssignModule_, context, request, response);
}

void ModuleAssignmentService::Stub::async::AssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::module::AssignModuleRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AssignModule_, context, request, response, std::move(f));
}

void ModuleAssignmentService::Stub::async::AssignModule(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AssignModule_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModuleAssignmentService::Stub::PrepareAsyncAssignModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::module::AssignModuleRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_AssignModule_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModuleAssignmentService::Stub::AsyncAssignModuleRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::AssignModuleRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncAssignModuleRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModuleAssignmentService::Stub::ClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::module::ClearModuleAssignmentRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ClearModuleAssignment_, context, request, response);
}

void ModuleAssignmentService::Stub::async::ClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::module::ClearModuleAssignmentRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ClearModuleAssignment_, context, request, response, std::move(f));
}

void ModuleAssignmentService::Stub::async::ClearModuleAssignment(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ClearModuleAssignment_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModuleAssignmentService::Stub::PrepareAsyncClearModuleAssignmentRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::module::ClearModuleAssignmentRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ClearModuleAssignment_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModuleAssignmentService::Stub::AsyncClearModuleAssignmentRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncClearModuleAssignmentRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModuleAssignmentService::Stub::SetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::module::SetModuleSerialRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetModuleSerial_, context, request, response);
}

void ModuleAssignmentService::Stub::async::SetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::module::SetModuleSerialRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetModuleSerial_, context, request, response, std::move(f));
}

void ModuleAssignmentService::Stub::async::SetModuleSerial(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetModuleSerial_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModuleAssignmentService::Stub::PrepareAsyncSetModuleSerialRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::module::SetModuleSerialRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetModuleSerial_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModuleAssignmentService::Stub::AsyncSetModuleSerialRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetModuleSerialRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetModuleSerialRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModuleAssignmentService::Stub::GetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::carbon::frontend::module::GetPresetsListResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::module::GetPresetsListRequest, ::carbon::frontend::module::GetPresetsListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetPresetsList_, context, request, response);
}

void ModuleAssignmentService::Stub::async::GetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest* request, ::carbon::frontend::module::GetPresetsListResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::module::GetPresetsListRequest, ::carbon::frontend::module::GetPresetsListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPresetsList_, context, request, response, std::move(f));
}

void ModuleAssignmentService::Stub::async::GetPresetsList(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest* request, ::carbon::frontend::module::GetPresetsListResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPresetsList_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetPresetsListResponse>* ModuleAssignmentService::Stub::PrepareAsyncGetPresetsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::module::GetPresetsListResponse, ::carbon::frontend::module::GetPresetsListRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetPresetsList_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetPresetsListResponse>* ModuleAssignmentService::Stub::AsyncGetPresetsListRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::GetPresetsListRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetPresetsListRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModuleAssignmentService::Stub::GetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCurrentRobotDefinition_, context, request, response);
}

void ModuleAssignmentService::Stub::async::GetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCurrentRobotDefinition_, context, request, response, std::move(f));
}

void ModuleAssignmentService::Stub::async::GetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCurrentRobotDefinition_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>* ModuleAssignmentService::Stub::PrepareAsyncGetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCurrentRobotDefinition_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse>* ModuleAssignmentService::Stub::AsyncGetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCurrentRobotDefinitionRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModuleAssignmentService::Stub::SetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::module::SetCurrentRobotDefinitionRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetCurrentRobotDefinition_, context, request, response);
}

void ModuleAssignmentService::Stub::async::SetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::module::SetCurrentRobotDefinitionRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCurrentRobotDefinition_, context, request, response, std::move(f));
}

void ModuleAssignmentService::Stub::async::SetCurrentRobotDefinition(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCurrentRobotDefinition_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModuleAssignmentService::Stub::PrepareAsyncSetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::module::SetCurrentRobotDefinitionRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetCurrentRobotDefinition_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModuleAssignmentService::Stub::AsyncSetCurrentRobotDefinitionRaw(::grpc::ClientContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetCurrentRobotDefinitionRaw(context, request, cq);
  result->StartCall();
  return result;
}

ModuleAssignmentService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleAssignmentService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleAssignmentService::Service, ::carbon::frontend::module::GetNextModulesListRequest, ::carbon::frontend::module::GetNextModulesListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleAssignmentService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::module::GetNextModulesListRequest* req,
             ::carbon::frontend::module::GetNextModulesListResponse* resp) {
               return service->GetNextModulesList(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleAssignmentService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleAssignmentService::Service, ::carbon::frontend::module::GetNextActiveModulesRequest, ::carbon::frontend::module::GetNextActiveModulesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleAssignmentService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::module::GetNextActiveModulesRequest* req,
             ::carbon::frontend::module::GetNextActiveModulesResponse* resp) {
               return service->GetNextActiveModules(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleAssignmentService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleAssignmentService::Service, ::carbon::frontend::module::IdentifyModuleRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleAssignmentService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::module::IdentifyModuleRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->IdentifyModule(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleAssignmentService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleAssignmentService::Service, ::carbon::frontend::module::AssignModuleRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleAssignmentService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::module::AssignModuleRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->AssignModule(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleAssignmentService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleAssignmentService::Service, ::carbon::frontend::module::ClearModuleAssignmentRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleAssignmentService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::module::ClearModuleAssignmentRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ClearModuleAssignment(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleAssignmentService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleAssignmentService::Service, ::carbon::frontend::module::SetModuleSerialRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleAssignmentService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::module::SetModuleSerialRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetModuleSerial(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleAssignmentService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleAssignmentService::Service, ::carbon::frontend::module::GetPresetsListRequest, ::carbon::frontend::module::GetPresetsListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleAssignmentService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::module::GetPresetsListRequest* req,
             ::carbon::frontend::module::GetPresetsListResponse* resp) {
               return service->GetPresetsList(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleAssignmentService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleAssignmentService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleAssignmentService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* resp) {
               return service->GetCurrentRobotDefinition(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModuleAssignmentService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModuleAssignmentService::Service, ::carbon::frontend::module::SetCurrentRobotDefinitionRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModuleAssignmentService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetCurrentRobotDefinition(ctx, req, resp);
             }, this)));
}

ModuleAssignmentService::Service::~Service() {
}

::grpc::Status ModuleAssignmentService::Service::GetNextModulesList(::grpc::ServerContext* context, const ::carbon::frontend::module::GetNextModulesListRequest* request, ::carbon::frontend::module::GetNextModulesListResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModuleAssignmentService::Service::GetNextActiveModules(::grpc::ServerContext* context, const ::carbon::frontend::module::GetNextActiveModulesRequest* request, ::carbon::frontend::module::GetNextActiveModulesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModuleAssignmentService::Service::IdentifyModule(::grpc::ServerContext* context, const ::carbon::frontend::module::IdentifyModuleRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModuleAssignmentService::Service::AssignModule(::grpc::ServerContext* context, const ::carbon::frontend::module::AssignModuleRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModuleAssignmentService::Service::ClearModuleAssignment(::grpc::ServerContext* context, const ::carbon::frontend::module::ClearModuleAssignmentRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModuleAssignmentService::Service::SetModuleSerial(::grpc::ServerContext* context, const ::carbon::frontend::module::SetModuleSerialRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModuleAssignmentService::Service::GetPresetsList(::grpc::ServerContext* context, const ::carbon::frontend::module::GetPresetsListRequest* request, ::carbon::frontend::module::GetPresetsListResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModuleAssignmentService::Service::GetCurrentRobotDefinition(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModuleAssignmentService::Service::SetCurrentRobotDefinition(::grpc::ServerContext* context, const ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace module


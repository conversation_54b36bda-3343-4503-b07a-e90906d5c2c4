// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/tractor.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ftractor_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ftractor_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2ftractor_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2ftractor_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2ftractor_2eproto;
namespace carbon {
namespace frontend {
namespace tractor {
class GetNextTractorIfStateResponse;
struct GetNextTractorIfStateResponseDefaultTypeInternal;
extern GetNextTractorIfStateResponseDefaultTypeInternal _GetNextTractorIfStateResponse_default_instance_;
class GetNextTractorSafetyStateResponse;
struct GetNextTractorSafetyStateResponseDefaultTypeInternal;
extern GetNextTractorSafetyStateResponseDefaultTypeInternal _GetNextTractorSafetyStateResponse_default_instance_;
class SetEnforcementPolicyRequest;
struct SetEnforcementPolicyRequestDefaultTypeInternal;
extern SetEnforcementPolicyRequestDefaultTypeInternal _SetEnforcementPolicyRequest_default_instance_;
class SetEnforcementPolicyResponse;
struct SetEnforcementPolicyResponseDefaultTypeInternal;
extern SetEnforcementPolicyResponseDefaultTypeInternal _SetEnforcementPolicyResponse_default_instance_;
class TractorIfState;
struct TractorIfStateDefaultTypeInternal;
extern TractorIfStateDefaultTypeInternal _TractorIfState_default_instance_;
class TractorSafetyState;
struct TractorSafetyStateDefaultTypeInternal;
extern TractorSafetyStateDefaultTypeInternal _TractorSafetyState_default_instance_;
}  // namespace tractor
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::tractor::GetNextTractorIfStateResponse* Arena::CreateMaybeMessage<::carbon::frontend::tractor::GetNextTractorIfStateResponse>(Arena*);
template<> ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* Arena::CreateMaybeMessage<::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>(Arena*);
template<> ::carbon::frontend::tractor::SetEnforcementPolicyRequest* Arena::CreateMaybeMessage<::carbon::frontend::tractor::SetEnforcementPolicyRequest>(Arena*);
template<> ::carbon::frontend::tractor::SetEnforcementPolicyResponse* Arena::CreateMaybeMessage<::carbon::frontend::tractor::SetEnforcementPolicyResponse>(Arena*);
template<> ::carbon::frontend::tractor::TractorIfState* Arena::CreateMaybeMessage<::carbon::frontend::tractor::TractorIfState>(Arena*);
template<> ::carbon::frontend::tractor::TractorSafetyState* Arena::CreateMaybeMessage<::carbon::frontend::tractor::TractorSafetyState>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace tractor {

// ===================================================================

class TractorIfState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.tractor.TractorIfState) */ {
 public:
  inline TractorIfState() : TractorIfState(nullptr) {}
  ~TractorIfState() override;
  explicit constexpr TractorIfState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TractorIfState(const TractorIfState& from);
  TractorIfState(TractorIfState&& from) noexcept
    : TractorIfState() {
    *this = ::std::move(from);
  }

  inline TractorIfState& operator=(const TractorIfState& from) {
    CopyFrom(from);
    return *this;
  }
  inline TractorIfState& operator=(TractorIfState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TractorIfState& default_instance() {
    return *internal_default_instance();
  }
  static inline const TractorIfState* internal_default_instance() {
    return reinterpret_cast<const TractorIfState*>(
               &_TractorIfState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TractorIfState& a, TractorIfState& b) {
    a.Swap(&b);
  }
  inline void Swap(TractorIfState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TractorIfState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TractorIfState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TractorIfState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TractorIfState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TractorIfState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TractorIfState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.tractor.TractorIfState";
  }
  protected:
  explicit TractorIfState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExpectedFieldNumber = 1,
    kConnectedFieldNumber = 2,
  };
  // bool expected = 1;
  void clear_expected();
  bool expected() const;
  void set_expected(bool value);
  private:
  bool _internal_expected() const;
  void _internal_set_expected(bool value);
  public:

  // bool connected = 2;
  void clear_connected();
  bool connected() const;
  void set_connected(bool value);
  private:
  bool _internal_connected() const;
  void _internal_set_connected(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.tractor.TractorIfState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool expected_;
  bool connected_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftractor_2eproto;
};
// -------------------------------------------------------------------

class TractorSafetyState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.tractor.TractorSafetyState) */ {
 public:
  inline TractorSafetyState() : TractorSafetyState(nullptr) {}
  ~TractorSafetyState() override;
  explicit constexpr TractorSafetyState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TractorSafetyState(const TractorSafetyState& from);
  TractorSafetyState(TractorSafetyState&& from) noexcept
    : TractorSafetyState() {
    *this = ::std::move(from);
  }

  inline TractorSafetyState& operator=(const TractorSafetyState& from) {
    CopyFrom(from);
    return *this;
  }
  inline TractorSafetyState& operator=(TractorSafetyState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TractorSafetyState& default_instance() {
    return *internal_default_instance();
  }
  static inline const TractorSafetyState* internal_default_instance() {
    return reinterpret_cast<const TractorSafetyState*>(
               &_TractorSafetyState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(TractorSafetyState& a, TractorSafetyState& b) {
    a.Swap(&b);
  }
  inline void Swap(TractorSafetyState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TractorSafetyState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TractorSafetyState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TractorSafetyState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TractorSafetyState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TractorSafetyState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TractorSafetyState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.tractor.TractorSafetyState";
  }
  protected:
  explicit TractorSafetyState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIsSafeFieldNumber = 1,
    kEnforcedFieldNumber = 2,
  };
  // bool is_safe = 1;
  void clear_is_safe();
  bool is_safe() const;
  void set_is_safe(bool value);
  private:
  bool _internal_is_safe() const;
  void _internal_set_is_safe(bool value);
  public:

  // bool enforced = 2;
  void clear_enforced();
  bool enforced() const;
  void set_enforced(bool value);
  private:
  bool _internal_enforced() const;
  void _internal_set_enforced(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.tractor.TractorSafetyState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool is_safe_;
  bool enforced_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftractor_2eproto;
};
// -------------------------------------------------------------------

class GetNextTractorIfStateResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.tractor.GetNextTractorIfStateResponse) */ {
 public:
  inline GetNextTractorIfStateResponse() : GetNextTractorIfStateResponse(nullptr) {}
  ~GetNextTractorIfStateResponse() override;
  explicit constexpr GetNextTractorIfStateResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextTractorIfStateResponse(const GetNextTractorIfStateResponse& from);
  GetNextTractorIfStateResponse(GetNextTractorIfStateResponse&& from) noexcept
    : GetNextTractorIfStateResponse() {
    *this = ::std::move(from);
  }

  inline GetNextTractorIfStateResponse& operator=(const GetNextTractorIfStateResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextTractorIfStateResponse& operator=(GetNextTractorIfStateResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextTractorIfStateResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextTractorIfStateResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextTractorIfStateResponse*>(
               &_GetNextTractorIfStateResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GetNextTractorIfStateResponse& a, GetNextTractorIfStateResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextTractorIfStateResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextTractorIfStateResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextTractorIfStateResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextTractorIfStateResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextTractorIfStateResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextTractorIfStateResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextTractorIfStateResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.tractor.GetNextTractorIfStateResponse";
  }
  protected:
  explicit GetNextTractorIfStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kStateFieldNumber = 2,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.tractor.TractorIfState state = 2;
  bool has_state() const;
  private:
  bool _internal_has_state() const;
  public:
  void clear_state();
  const ::carbon::frontend::tractor::TractorIfState& state() const;
  PROTOBUF_NODISCARD ::carbon::frontend::tractor::TractorIfState* release_state();
  ::carbon::frontend::tractor::TractorIfState* mutable_state();
  void set_allocated_state(::carbon::frontend::tractor::TractorIfState* state);
  private:
  const ::carbon::frontend::tractor::TractorIfState& _internal_state() const;
  ::carbon::frontend::tractor::TractorIfState* _internal_mutable_state();
  public:
  void unsafe_arena_set_allocated_state(
      ::carbon::frontend::tractor::TractorIfState* state);
  ::carbon::frontend::tractor::TractorIfState* unsafe_arena_release_state();

  // @@protoc_insertion_point(class_scope:carbon.frontend.tractor.GetNextTractorIfStateResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::tractor::TractorIfState* state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftractor_2eproto;
};
// -------------------------------------------------------------------

class GetNextTractorSafetyStateResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.tractor.GetNextTractorSafetyStateResponse) */ {
 public:
  inline GetNextTractorSafetyStateResponse() : GetNextTractorSafetyStateResponse(nullptr) {}
  ~GetNextTractorSafetyStateResponse() override;
  explicit constexpr GetNextTractorSafetyStateResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextTractorSafetyStateResponse(const GetNextTractorSafetyStateResponse& from);
  GetNextTractorSafetyStateResponse(GetNextTractorSafetyStateResponse&& from) noexcept
    : GetNextTractorSafetyStateResponse() {
    *this = ::std::move(from);
  }

  inline GetNextTractorSafetyStateResponse& operator=(const GetNextTractorSafetyStateResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextTractorSafetyStateResponse& operator=(GetNextTractorSafetyStateResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextTractorSafetyStateResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextTractorSafetyStateResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextTractorSafetyStateResponse*>(
               &_GetNextTractorSafetyStateResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GetNextTractorSafetyStateResponse& a, GetNextTractorSafetyStateResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextTractorSafetyStateResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextTractorSafetyStateResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextTractorSafetyStateResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextTractorSafetyStateResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextTractorSafetyStateResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextTractorSafetyStateResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextTractorSafetyStateResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.tractor.GetNextTractorSafetyStateResponse";
  }
  protected:
  explicit GetNextTractorSafetyStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kStateFieldNumber = 2,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.tractor.TractorSafetyState state = 2;
  bool has_state() const;
  private:
  bool _internal_has_state() const;
  public:
  void clear_state();
  const ::carbon::frontend::tractor::TractorSafetyState& state() const;
  PROTOBUF_NODISCARD ::carbon::frontend::tractor::TractorSafetyState* release_state();
  ::carbon::frontend::tractor::TractorSafetyState* mutable_state();
  void set_allocated_state(::carbon::frontend::tractor::TractorSafetyState* state);
  private:
  const ::carbon::frontend::tractor::TractorSafetyState& _internal_state() const;
  ::carbon::frontend::tractor::TractorSafetyState* _internal_mutable_state();
  public:
  void unsafe_arena_set_allocated_state(
      ::carbon::frontend::tractor::TractorSafetyState* state);
  ::carbon::frontend::tractor::TractorSafetyState* unsafe_arena_release_state();

  // @@protoc_insertion_point(class_scope:carbon.frontend.tractor.GetNextTractorSafetyStateResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::tractor::TractorSafetyState* state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftractor_2eproto;
};
// -------------------------------------------------------------------

class SetEnforcementPolicyRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.tractor.SetEnforcementPolicyRequest) */ {
 public:
  inline SetEnforcementPolicyRequest() : SetEnforcementPolicyRequest(nullptr) {}
  ~SetEnforcementPolicyRequest() override;
  explicit constexpr SetEnforcementPolicyRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetEnforcementPolicyRequest(const SetEnforcementPolicyRequest& from);
  SetEnforcementPolicyRequest(SetEnforcementPolicyRequest&& from) noexcept
    : SetEnforcementPolicyRequest() {
    *this = ::std::move(from);
  }

  inline SetEnforcementPolicyRequest& operator=(const SetEnforcementPolicyRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetEnforcementPolicyRequest& operator=(SetEnforcementPolicyRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetEnforcementPolicyRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetEnforcementPolicyRequest* internal_default_instance() {
    return reinterpret_cast<const SetEnforcementPolicyRequest*>(
               &_SetEnforcementPolicyRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(SetEnforcementPolicyRequest& a, SetEnforcementPolicyRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetEnforcementPolicyRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetEnforcementPolicyRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetEnforcementPolicyRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetEnforcementPolicyRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetEnforcementPolicyRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetEnforcementPolicyRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetEnforcementPolicyRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.tractor.SetEnforcementPolicyRequest";
  }
  protected:
  explicit SetEnforcementPolicyRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnforcedFieldNumber = 1,
  };
  // bool enforced = 1;
  void clear_enforced();
  bool enforced() const;
  void set_enforced(bool value);
  private:
  bool _internal_enforced() const;
  void _internal_set_enforced(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.tractor.SetEnforcementPolicyRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool enforced_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftractor_2eproto;
};
// -------------------------------------------------------------------

class SetEnforcementPolicyResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.frontend.tractor.SetEnforcementPolicyResponse) */ {
 public:
  inline SetEnforcementPolicyResponse() : SetEnforcementPolicyResponse(nullptr) {}
  explicit constexpr SetEnforcementPolicyResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetEnforcementPolicyResponse(const SetEnforcementPolicyResponse& from);
  SetEnforcementPolicyResponse(SetEnforcementPolicyResponse&& from) noexcept
    : SetEnforcementPolicyResponse() {
    *this = ::std::move(from);
  }

  inline SetEnforcementPolicyResponse& operator=(const SetEnforcementPolicyResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetEnforcementPolicyResponse& operator=(SetEnforcementPolicyResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetEnforcementPolicyResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetEnforcementPolicyResponse* internal_default_instance() {
    return reinterpret_cast<const SetEnforcementPolicyResponse*>(
               &_SetEnforcementPolicyResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(SetEnforcementPolicyResponse& a, SetEnforcementPolicyResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetEnforcementPolicyResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetEnforcementPolicyResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetEnforcementPolicyResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetEnforcementPolicyResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SetEnforcementPolicyResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SetEnforcementPolicyResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.tractor.SetEnforcementPolicyResponse";
  }
  protected:
  explicit SetEnforcementPolicyResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.frontend.tractor.SetEnforcementPolicyResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ftractor_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TractorIfState

// bool expected = 1;
inline void TractorIfState::clear_expected() {
  expected_ = false;
}
inline bool TractorIfState::_internal_expected() const {
  return expected_;
}
inline bool TractorIfState::expected() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.tractor.TractorIfState.expected)
  return _internal_expected();
}
inline void TractorIfState::_internal_set_expected(bool value) {
  
  expected_ = value;
}
inline void TractorIfState::set_expected(bool value) {
  _internal_set_expected(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.tractor.TractorIfState.expected)
}

// bool connected = 2;
inline void TractorIfState::clear_connected() {
  connected_ = false;
}
inline bool TractorIfState::_internal_connected() const {
  return connected_;
}
inline bool TractorIfState::connected() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.tractor.TractorIfState.connected)
  return _internal_connected();
}
inline void TractorIfState::_internal_set_connected(bool value) {
  
  connected_ = value;
}
inline void TractorIfState::set_connected(bool value) {
  _internal_set_connected(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.tractor.TractorIfState.connected)
}

// -------------------------------------------------------------------

// TractorSafetyState

// bool is_safe = 1;
inline void TractorSafetyState::clear_is_safe() {
  is_safe_ = false;
}
inline bool TractorSafetyState::_internal_is_safe() const {
  return is_safe_;
}
inline bool TractorSafetyState::is_safe() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.tractor.TractorSafetyState.is_safe)
  return _internal_is_safe();
}
inline void TractorSafetyState::_internal_set_is_safe(bool value) {
  
  is_safe_ = value;
}
inline void TractorSafetyState::set_is_safe(bool value) {
  _internal_set_is_safe(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.tractor.TractorSafetyState.is_safe)
}

// bool enforced = 2;
inline void TractorSafetyState::clear_enforced() {
  enforced_ = false;
}
inline bool TractorSafetyState::_internal_enforced() const {
  return enforced_;
}
inline bool TractorSafetyState::enforced() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.tractor.TractorSafetyState.enforced)
  return _internal_enforced();
}
inline void TractorSafetyState::_internal_set_enforced(bool value) {
  
  enforced_ = value;
}
inline void TractorSafetyState::set_enforced(bool value) {
  _internal_set_enforced(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.tractor.TractorSafetyState.enforced)
}

// -------------------------------------------------------------------

// GetNextTractorIfStateResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextTractorIfStateResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextTractorIfStateResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextTractorIfStateResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextTractorIfStateResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.tractor.GetNextTractorIfStateResponse.ts)
  return _internal_ts();
}
inline void GetNextTractorIfStateResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.tractor.GetNextTractorIfStateResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextTractorIfStateResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextTractorIfStateResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.tractor.GetNextTractorIfStateResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextTractorIfStateResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextTractorIfStateResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.tractor.GetNextTractorIfStateResponse.ts)
  return _msg;
}
inline void GetNextTractorIfStateResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.tractor.GetNextTractorIfStateResponse.ts)
}

// .carbon.frontend.tractor.TractorIfState state = 2;
inline bool GetNextTractorIfStateResponse::_internal_has_state() const {
  return this != internal_default_instance() && state_ != nullptr;
}
inline bool GetNextTractorIfStateResponse::has_state() const {
  return _internal_has_state();
}
inline void GetNextTractorIfStateResponse::clear_state() {
  if (GetArenaForAllocation() == nullptr && state_ != nullptr) {
    delete state_;
  }
  state_ = nullptr;
}
inline const ::carbon::frontend::tractor::TractorIfState& GetNextTractorIfStateResponse::_internal_state() const {
  const ::carbon::frontend::tractor::TractorIfState* p = state_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::tractor::TractorIfState&>(
      ::carbon::frontend::tractor::_TractorIfState_default_instance_);
}
inline const ::carbon::frontend::tractor::TractorIfState& GetNextTractorIfStateResponse::state() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.tractor.GetNextTractorIfStateResponse.state)
  return _internal_state();
}
inline void GetNextTractorIfStateResponse::unsafe_arena_set_allocated_state(
    ::carbon::frontend::tractor::TractorIfState* state) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(state_);
  }
  state_ = state;
  if (state) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.tractor.GetNextTractorIfStateResponse.state)
}
inline ::carbon::frontend::tractor::TractorIfState* GetNextTractorIfStateResponse::release_state() {
  
  ::carbon::frontend::tractor::TractorIfState* temp = state_;
  state_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::tractor::TractorIfState* GetNextTractorIfStateResponse::unsafe_arena_release_state() {
  // @@protoc_insertion_point(field_release:carbon.frontend.tractor.GetNextTractorIfStateResponse.state)
  
  ::carbon::frontend::tractor::TractorIfState* temp = state_;
  state_ = nullptr;
  return temp;
}
inline ::carbon::frontend::tractor::TractorIfState* GetNextTractorIfStateResponse::_internal_mutable_state() {
  
  if (state_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::tractor::TractorIfState>(GetArenaForAllocation());
    state_ = p;
  }
  return state_;
}
inline ::carbon::frontend::tractor::TractorIfState* GetNextTractorIfStateResponse::mutable_state() {
  ::carbon::frontend::tractor::TractorIfState* _msg = _internal_mutable_state();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.tractor.GetNextTractorIfStateResponse.state)
  return _msg;
}
inline void GetNextTractorIfStateResponse::set_allocated_state(::carbon::frontend::tractor::TractorIfState* state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete state_;
  }
  if (state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::tractor::TractorIfState>::GetOwningArena(state);
    if (message_arena != submessage_arena) {
      state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, state, submessage_arena);
    }
    
  } else {
    
  }
  state_ = state;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.tractor.GetNextTractorIfStateResponse.state)
}

// -------------------------------------------------------------------

// GetNextTractorSafetyStateResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextTractorSafetyStateResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextTractorSafetyStateResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextTractorSafetyStateResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextTractorSafetyStateResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.tractor.GetNextTractorSafetyStateResponse.ts)
  return _internal_ts();
}
inline void GetNextTractorSafetyStateResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.tractor.GetNextTractorSafetyStateResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextTractorSafetyStateResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextTractorSafetyStateResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.tractor.GetNextTractorSafetyStateResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextTractorSafetyStateResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextTractorSafetyStateResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.tractor.GetNextTractorSafetyStateResponse.ts)
  return _msg;
}
inline void GetNextTractorSafetyStateResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.tractor.GetNextTractorSafetyStateResponse.ts)
}

// .carbon.frontend.tractor.TractorSafetyState state = 2;
inline bool GetNextTractorSafetyStateResponse::_internal_has_state() const {
  return this != internal_default_instance() && state_ != nullptr;
}
inline bool GetNextTractorSafetyStateResponse::has_state() const {
  return _internal_has_state();
}
inline void GetNextTractorSafetyStateResponse::clear_state() {
  if (GetArenaForAllocation() == nullptr && state_ != nullptr) {
    delete state_;
  }
  state_ = nullptr;
}
inline const ::carbon::frontend::tractor::TractorSafetyState& GetNextTractorSafetyStateResponse::_internal_state() const {
  const ::carbon::frontend::tractor::TractorSafetyState* p = state_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::tractor::TractorSafetyState&>(
      ::carbon::frontend::tractor::_TractorSafetyState_default_instance_);
}
inline const ::carbon::frontend::tractor::TractorSafetyState& GetNextTractorSafetyStateResponse::state() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.tractor.GetNextTractorSafetyStateResponse.state)
  return _internal_state();
}
inline void GetNextTractorSafetyStateResponse::unsafe_arena_set_allocated_state(
    ::carbon::frontend::tractor::TractorSafetyState* state) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(state_);
  }
  state_ = state;
  if (state) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.tractor.GetNextTractorSafetyStateResponse.state)
}
inline ::carbon::frontend::tractor::TractorSafetyState* GetNextTractorSafetyStateResponse::release_state() {
  
  ::carbon::frontend::tractor::TractorSafetyState* temp = state_;
  state_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::tractor::TractorSafetyState* GetNextTractorSafetyStateResponse::unsafe_arena_release_state() {
  // @@protoc_insertion_point(field_release:carbon.frontend.tractor.GetNextTractorSafetyStateResponse.state)
  
  ::carbon::frontend::tractor::TractorSafetyState* temp = state_;
  state_ = nullptr;
  return temp;
}
inline ::carbon::frontend::tractor::TractorSafetyState* GetNextTractorSafetyStateResponse::_internal_mutable_state() {
  
  if (state_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::tractor::TractorSafetyState>(GetArenaForAllocation());
    state_ = p;
  }
  return state_;
}
inline ::carbon::frontend::tractor::TractorSafetyState* GetNextTractorSafetyStateResponse::mutable_state() {
  ::carbon::frontend::tractor::TractorSafetyState* _msg = _internal_mutable_state();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.tractor.GetNextTractorSafetyStateResponse.state)
  return _msg;
}
inline void GetNextTractorSafetyStateResponse::set_allocated_state(::carbon::frontend::tractor::TractorSafetyState* state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete state_;
  }
  if (state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::tractor::TractorSafetyState>::GetOwningArena(state);
    if (message_arena != submessage_arena) {
      state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, state, submessage_arena);
    }
    
  } else {
    
  }
  state_ = state;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.tractor.GetNextTractorSafetyStateResponse.state)
}

// -------------------------------------------------------------------

// SetEnforcementPolicyRequest

// bool enforced = 1;
inline void SetEnforcementPolicyRequest::clear_enforced() {
  enforced_ = false;
}
inline bool SetEnforcementPolicyRequest::_internal_enforced() const {
  return enforced_;
}
inline bool SetEnforcementPolicyRequest::enforced() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.tractor.SetEnforcementPolicyRequest.enforced)
  return _internal_enforced();
}
inline void SetEnforcementPolicyRequest::_internal_set_enforced(bool value) {
  
  enforced_ = value;
}
inline void SetEnforcementPolicyRequest::set_enforced(bool value) {
  _internal_set_enforced(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.tractor.SetEnforcementPolicyRequest.enforced)
}

// -------------------------------------------------------------------

// SetEnforcementPolicyResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tractor
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ftractor_2eproto
